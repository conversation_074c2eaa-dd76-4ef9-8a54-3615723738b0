
setup: pre-commit-setup

check-format: pre-commit

pre-commit:
	pre-commit run --all-files --hook-stage commit

pre-commit-setup:
	# 如果遇到 Failed to connect to raw.githubusercontent.com port 443: Connection refused
	# 为DNS污染问题，可在https://www.ipaddress.com/查询域名，然后写入hosts文件中
	# 见：https://github.com/hawtim/blog/issues/10
	wget https://gitlab.bees360.com/engineers/pre-commit-hooks/-/raw/master/scripts/pre-commit-setup.sh -O - | bash

migrate-web:
	cd db/web/ && liquibase update

rollback-web:
	cd db/web/ && liquibase rollbackCount 1

migrate-test-web:
	cd db/web/ && liquibase rollbackCount 25

migrate-test-ai:
	cd db/ai/ && liquibase updateTestingRollback
