#!/bin/bash
set -e

TOKEN="PRIVATE-TOKEN: $1"
UPDATE="$2"
TODAY=$(date -I)
DATE=$(date -I --date="1 week ago")

BASEURL="https://gitlab.bees360.com/api/v4"
PROJECT="10"
GROUP="9"
ISSUES_URL="${BASEURL}/projects/${PROJECT}/issues?scope=all&per_page=100"
MEMBERS_URL="${BASEURL}/groups/${GROUP}/members"

MEMBERS="$(curl -s -H "$TOKEN" "$MEMBERS_URL" | jq -r '.[]|select(.access_level >= 30)|.username')"

SPT=".time_stats.human_total_time_spent"
SPT="\\(if $SPT then \"__\\($SPT)__\" else \"\`\\($SPT)\`\" end)"
EST=".time_stats.human_time_estimate"
EST="\\(if $EST then \"__\\($EST)__\" else \"\`\\($EST)\`\" end)"
DUE=".due_date"
DUE="\\(if $DUE and $DUE <= $TODAY then \"__\\($DUE)__\" else \"\`\\($DUE)\`\" end)"
CLOSED_FMT="| #\\(.iid) \\(.title) | $EST | $SPT | \\(if .due_date then .due_date else \"\" end) |"
CLOSED_FMT="[.[]|\"$CLOSED_FMT\"]|join(\"\n\")"
OPENED_FMT="| #\\(.iid) \\(.title) | $EST | $SPT | $DUE |"
OPENED_FMT="[.[]|select(.labels|contains([\"On Hold\"])|not)|\"$OPENED_FMT\"]|join(\"\n\")"

! read -r -d '' DESC <<- EOM
# 说明

这个页面统计所以Bees360人员每周issue的完成情况。
每个人每周完成的issue和未完成的issue都会列在这个页面中(标为On Hold的issue除外)。
每个人的issue都应该有对应的预计完成所需时间、已经花费时间、以及到期日。
如果这几项没有的话，在表格中会以红色字体显示。

这个wiki页面会在每次代码提交到master之后自动更新。
更新脚本在[upstream/devops/update_weekly_work_summary](https://gitlab.bees360.com/root/upstream/blob/master/devops/update_weekly_work_summary)。
EOM

for MB in $MEMBERS
do
    NAME="## @${MB}"
    HEAD="| Issue | Estimate Time | Spent Time | Due Date |
| ----- | ------------- | ---------- | -------- |"
    CLOSED="$(curl -s -H "$TOKEN" \
    "$ISSUES_URL&state=closed&assignee_username=$MB&updated_after=$DATE" \
    | jq -r "$CLOSED_FMT")"
    OPENED="$(curl -s -H "$TOKEN" \
    "$ISSUES_URL&state=opened&assignee_username=$MB" | jq -r "$OPENED_FMT")"
    BODY="$CLOSED"
    if [ ! -z "$OPENED" ]; then
        if [ ! -z "$CLOSED" ]; then
            BODY="$BODY\n$OPENED"
        else
            BODY="$OPENED"
        fi
    fi
    printf -v DESC "%s\n%s\n%s\n%s\n" "$DESC" "$NAME" "$HEAD" "$BODY"
done

printf -v CONTENT "# Work Summary %s to %s\n%s" "$DATE" "$TODAY" "$DESC"
echo -e "$CONTENT"

exec curl -X PUT -H "$TOKEN" --data-urlencode "content=$CONTENT" \
    "$BASEURL/projects/$PROJECT/wikis/Weekly-Work-Summary"
