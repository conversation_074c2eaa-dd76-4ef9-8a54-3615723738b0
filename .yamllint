---

extends: default

rules:
  braces:
    level: error
    max-spaces-inside: 1
  brackets:
    level: error
    max-spaces-inside: 1
  colons:
    level: error
    max-spaces-before: 0
    max-spaces-after: 1
  commas:
    level: error
    max-spaces-before: 0
    min-spaces-after: 1
    max-spaces-after: 1
  comments:
    # whether require a space character right after the '#'
    require-starting-space: false
    # ignore #!(shebang) in the start of the file
    ignore-shebangs: true
    # min space comment from content
    min-spaces-from-content: 1
  # force comments to be indented like content.
  comments-indentation: {}
  # Use this rule to require or forbid the use of document start marker (---).
  document-start: disable
  empty-lines:
    level: error
  hyphens:
    level: error
    # max spaces after hyphens(-)
    max-spaces-after: 1
  indentation:
    level: error
    spaces: 2
    # defines whether block sequences should be indented or not
    indent-sequences: true
  line-length:
    max: 120
    level: error
    allow-non-breakable-words: true
    allow-non-breakable-inline-mappings: true
  # whether forbid non-explictly typed truthy values other than allowed ones
  # by default: true and false
  truthy: disable
  new-line-at-end-of-file: enable
  new-lines:
    # use UNIX-typed new line characters (\n)
    type: unix
