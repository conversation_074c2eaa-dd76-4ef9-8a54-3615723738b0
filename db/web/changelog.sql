--liquibase formatted sql

--changeset shoushan:add_company_sagesure
INSERT IGNORE INTO `Company` (`company_id`, `company_name`, `company_type`, `website`, `logo`, `auto_pay`, `contact_name`, `phone`, `email`, `is_deleted`, `created_time`, `updated_time`) VALUES (2756, 'SageSure Insurance', 0, 'www.sagesure.com/', 'company/logo/16282631131856656.png', 0, '', '', '', 0, 1628263086884, 1628263113914);
-- rollback


--changeset guanrong:add_table_file_export_record_batch_and_file_export_record
CREATE TABLE IF NOT EXISTS `file_export_record_batch` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `destination` varchar(512) NOT NULL,
  `destination_id` varchar(512) NOT NULL,
  `status` varchar(256) NOT NULL,
  `error_log` varchar(5000) NOT NULL DEFAULT '',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10000 DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `file_export_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_id` bigint(20) NOT NULL,
  `resource_id` varchar(512) NOT NULL,
  `executor_id` varchar(512) NOT NULL DEFAULT '',
  `resource_type` varchar(512) NOT NULL,
  `resource_src` varchar(512) NOT NULL,
  `original_name` varchar(512) NOT NULL,
  `file_name` varchar(512) NOT NULL,
  `description` varchar(512) NOT NULL,
  `location` varchar(512) NOT NULL,
  `size` int(11) NOT NULL,
  `sort` int(11) NOT NULL,
  `status` varchar(256) NOT NULL,
  `error_log` varchar(5000) NOT NULL DEFAULT '',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_resource_id` (`resource_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10000 DEFAULT CHARSET=utf8mb4;
-- rollback DROP TABLE IF EXISTS file_export_record;
-- rollback DROP TABLE IF EXISTS file_export_record_batch;

--changeset guanrong:add_new_company_2762_germania
INSERT IGNORE INTO `Company` (`company_id`, `company_name`, `company_type`, `website`, `logo`, `auto_pay`, `contact_name`, `phone`, `email`, `is_deleted`, `created_time`, `updated_time`) VALUES (2762, 'Germania Insurance', 0, 'www.germaniainsurance.com', 'company/logo/16359154591435597.png', 0, '', '', '', 0, 1639974835212, 1639974835212);
-- rollback

--changeset dunhong:add_WAITING_FOR_AIRSPACE_AUTHORIZATION_and_INSURED_OUT_OF_TOWN
INSERT INTO ProjectLabel(id, `label_name`, `label_desc`) value (14, 'Restricted airspace', 'RESTRICTED_AIRSPACE');
INSERT INTO ProjectLabel(id, `label_name`, `label_desc`) value (15, 'Insured not at home', 'INSURED_NOT_AT_HOME');
INSERT INTO ProjectLabel(id, `label_name`, `label_desc`) value (16, 'Inclement weather', 'INCLEMENT_WEATHER');
INSERT INTO ProjectLabel(id, `label_name`, `label_desc`) value (17, 'Personal accident', 'PERSONAL_ACCIDENT');
INSERT INTO ProjectLabel(id, `label_name`, `label_desc`) value (18, 'Personal accident', 'TECH_PROBLEMS');
INSERT INTO ProjectLabel(id, `label_name`, `label_desc`) value (19, 'Insured COVID quarantine', 'INSURED_COVID_QUARANTINE');
INSERT INTO ProjectLabel(id, `label_name`, `label_desc`) value (20, 'Unable to reach insured after multiple attempts', 'UNABLE_TO_REACH_INSURED_AFTER_MULTIPLE_ATTEMPTS');
-- rollback delete from ProjectLabel where id in (14,15,16,17,18,19,20);

--changeset mingcong:add_address_id_to_project
ALTER TABLE Project ADD COLUMN `address_id` varchar(64);
-- rollback ALTER TABLE Project DROP COLUMN `address_id`;

--changeset shoushan:add_homeowners_company
INSERT IGNORE INTO Company (`company_id`, `company_name`, `company_type`, `website`, `logo`, `auto_pay`, `contact_name`, `phone`, `email`, `is_deleted`, `created_time`, `updated_time`) VALUES (2760, 'Homeowners of America', 0, 'hoaic.com/', 'company/logo/1634272332758704.png', 0, '', '', '', 0, 1634272277930, 1634272333466);
-- rollback

--changeset shoushan:add_project_created_time_index
ALTER TABLE Project ADD INDEX `idx_created_time` (`created_time`);
-- rollback ALTER TABLE Project DROP INDEX `idx_created_time`;

--changeset yangguanrong:add_company_rhp_general_agency
INSERT IGNORE INTO Company (`company_id`, `company_name`, `company_type`, `website`, `logo`, `auto_pay`, `contact_name`, `phone`, `email`, `is_deleted`, `created_time`, `updated_time`) VALUES (2766, 'RHP General Agency', 0, 'rhpga.com/', 'company/logo/16443972401649632.jpg', 0, '', '************', '<EMAIL>', 0, 1643730295928, 1643730308194);
-- rollback

--changeset dunhong:add_operations_tag_Tarp_in_Place
INSERT INTO ProjectLabel(id, `label_name`, `label_desc`) values (21, 'Tarp in Place','TRAP_IN_PLACE');
-- rollback delete from ProjectLabel where id = 21;

-- changeset mingcong:add_user_web_robot
INSERT INTO `User`(`user_id`, `first_name`, `last_name`, `email`, `qr_code`, `phone`, `address`, `city`, `state`, `country`, `zip_code`, `avatar`, `company_id`, `employee_id`, `password`, `last_login_time`, `registration_time`, `active_status`, `gps_location`, `most_recent_gps_location`, `roles`, `role_application_status`, `certificate_list`, `discount_percent`, `free_on_trail_project_num`, `new_customer_discount_percent`, `new_customer_discount_project_num`, `wallet_balance`, `commission_balance`, `currency`, `inspection_service`, `highfly_service`, `order_service_time`, `travel_radius`, `source`) VALUES (20000, 'BeesWeb', '', '<EMAIL>', NULL, '', NULL, NULL, NULL, NULL, NULL, 'https://s3.amazonaws.com/bees360/user/10005/avatar/1517273056251.png', 1062, NULL, '62C8AD0A15D9D1CA38D5DEE762A16E01', 1523372660137, 1513956734712, 1, ST_GeomFromText('POINT(0 0)'), ST_GeomFromText('POINT(0 0)'), 4, 0, NULL, 0.000, 0, 0.000, 0, 0.000, NULL, NULL, NULL, NULL, 0, 0, 0);
-- rollback delete from User where user_id = 20000;


--changeset yangguanrong:add_company_rhp_twfg_insurance
INSERT IGNORE INTO Company (`company_id`, `company_name`, `company_type`, `website`, `logo`, `auto_pay`, `contact_name`, `phone`, `email`, `is_deleted`, `created_time`, `updated_time`) VALUES (2768, 'TWFG Insurance', 0, 'twfg.com/', 'company/logo/16448034402913632.png', 0, '', '', '', 0, 1644779074454, 1644779172043);
-- rollback

--changeset yangguanrong:add_company_berkley_one_insurance
INSERT IGNORE INTO Company (`company_id`, `company_name`, `company_type`, `website`, `logo`, `auto_pay`, `contact_name`, `phone`, `email`, `is_deleted`, `created_time`, `updated_time`)
    VALUES (2757, 'Berkley One Insurance', 0, 'www.berkleyone.com/', 'company/logo/16464606518189259.png', 0, '', '', '', 0, 1628520440611, 1628733171473);
-- rollback

--changeset mingcong:add_missing_interior_images
INSERT INTO ProjectLabel(id, `label_name`, `label_desc`) value (22, 'Missing interior images', 'MISSING_INTERIOR_IMAGES');
-- rollback delete from ProjectLabel where id in (22);

--changeset shoushan:add_company_key
ALTER TABLE Company ADD COLUMN company_key varchar(200) NOT NULL COMMENT 'Company的key,默认与Company name相同' AFTER `company_name`;
INSERT IGNORE INTO Company (`company_id`, `company_name`, `company_key`, `company_type`, `website`, `logo`, `auto_pay`, `contact_name`, `phone`, `email`, `is_deleted`, `created_time`, `updated_time`) VALUES (2755, 'Twico', 'TWFG', 0, 'www.twico.com/', 'company/logo/16278316560261860.png', 0, '', '', '', 0, 1627831607321, 1627831656540);
UPDATE Company SET company_key = company_name;
UPDATE Company SET company_key = 'TWFG' WHERE company_id = 2755;
-- rollback ALTER TABLE Company DROP COLUMN `company_key`;

--changeset guanrong:add_table_project_creation
CREATE TABLE IF NOT EXISTS `project_creation` (
    `project_id` bigint NOT NULL,
    `creation_channel` varchar(62) NOT NULL,
    `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- rollback DROP TABLE project_creation;

--changeset guanrong:init_table_project_creation
INSERT IGNORE INTO project_creation(project_id, creation_channel)
    SELECT P.project_id, 'OPENAPI' AS creation_channel FROM Project P LEFT JOIN User U ON P.created_by = U.user_id
    WHERE U.email = '<EMAIL>';
INSERT IGNORE INTO project_creation(project_id, creation_channel)
    SELECT P.project_id, 'OPENAPI' AS creation_channel FROM Project P LEFT JOIN User U ON P.created_by = U.user_id
    WHERE U.email = '<EMAIL>';
INSERT IGNORE INTO project_creation(project_id, creation_channel)
    SELECT P.project_id, 'XACTANALYSIS' AS creation_channel FROM Project P LEFT JOIN User U ON P.created_by = U.user_id
    WHERE U.email = '<EMAIL>';
INSERT IGNORE INTO project_creation(project_id, creation_channel)
    SELECT P.project_id, 'SYMBILITY' AS creation_channel FROM Project P LEFT JOIN User U ON P.created_by = U.user_id
    WHERE U.email = '<EMAIL>';
--- rollback DELETE FROM project_creation where 1 = 1;

--changeset yongjian:modify_project_claim_note_encode
ALTER TABLE `Bees360`.`Project` MODIFY COLUMN claim_note text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- rollback ALTER TABLE `Bees360`.`Project` MODIFY COLUMN claim_note text CHARACTER SET utf8 COLLATE utf8_general_ci;

--changeset guanrong:add_table_to_record_xa_assignment_status
CREATE TABLE IF NOT EXISTS `xa_assignment_status` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `transaction_id` VARCHAR(512) NOT NULL,
  `orig_transaction_id` VARCHAR(512) NOT NULL,
  `status` VARCHAR(512) NOT NULL,
  `status_stamp` DATETIME,
  `xactdoc_data` TEXT NOT NULL,
  `create_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_orig_transaction_id` (`orig_transaction_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10000 DEFAULT CHARSET=utf8mb4;
-- rollback DROP TABLE IF EXISTS xa_assignment_status;

--changeset guanrong:set_company_deleted
UPDATE Company SET is_deleted = 1 WHERE company_name NOT IN (
  "Bees360",
  "Associated Services Inspections - Commercial",
  "State Farm",
  "Berkshire Hathaway",
  "Liberty Mutual",
  "Allstate",
  "Progressive",
  "Travelers",
  "Chubb",
  "USAA",
  "Farmers",
  "Nationwide",
  "AIG",
  "Zurich",
  "Hartford",
  "AmTrust Group",
  "American Family",
  "Auto Owners Group",
  "Erie Insurance",
  "Tokio Marine",
  "American Financial",
  "W.R. Berkley",
  "Assurant",
  "Fairfax Financial Holdings",
  "Cincinnati Financial",
  "Insurance Risk Services, Inc.",
  "Swyfft Homeowners Insurance",
  "UPC Insurance",
  "Swyfft Underwriting",
  "Allied Trust Insurance",
  "Velocity",
  "Florida Family",
  "Centauri Insurance",
  "Venture Underwriting",
  "Bees360 Test Carrier",
  "American Modern Insurance",
  "Plymouth Rock Home Assurance",
  "DCI Insurance",
  "New York Central Mutual",
  "Utica First Insurance Company",
  "GeoVera Holdings - Claims",
  "GeoVera Holdings - Underwriting",
  "Security First Florida",
  "Mercury Insurance",
  "Gulfstream Property & Casualty Insurance Company",
  "TWICO/TWFG",
  "SageSure Insurance",
  "Berkley One Insurance",
  "Church Mutual Insurance",
  "Central Insurance",
  "Homeowners of America",
  "Olympus Insurance",
  "Germania Insurance",
  "Citizens Florida",
  "American Integrity",
  "Berkshire Hathaway GUARD",
  "RHP General Agency",
  "Pure Insurance",
  "TWFG Insurance",
  "Universal Property & Casualty Insurance Company",
  "9realms"
);
-- rollback

-- changeset mingcong:add_bees360_test_company
INSERT IGNORE INTO Bees360.Company (company_id, company_name, company_key, company_type, website, logo, auto_pay, contact_name, phone, email, is_deleted, created_time, updated_time) VALUES (2744, 'Bees360 Test Carrier', 'Bees360 Test Carrier', 0, '', 'company/logo/16123289796691055.png', 0, '', '', '', 0, 1612328987615, 1612328987615);
-- rollback DELETE FROM Bees360.Company where company_id = 2744;

--changeset xiaojun:add_table_project_operating_company
CREATE TABLE IF NOT EXISTS `project_operating_company` (
	`id` BIGINT(20) NOT NULL AUTO_INCREMENT,
	`operating_company` VARCHAR(512) NOT NULL,
	`project_id` BIGINT(20) NOT NULL,
	`created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	primary key(`id`),
	key `idx_project_id` (`project_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10000 DEFAULT CHARSET=utf8mb4;
-- rollback DROP TABLE IF EXISTS project_operating_company;

--changeset xiaojun:add_denied_on_location_operation_tag
INSERT INTO ProjectLabel(id, `label_name`, `label_desc`) value (23, 'Denied on location', 'DENIED_ON_LOCATION');
-- rollback delete from ProjectLabel where id in (23);

-- changeset mingcong:add_Magicplan_Missing
INSERT INTO ProjectLabel(id, `label_name`, `label_desc`) value (24, 'Magicplan Missing', 'MAGICPLAN_MISSING');
-- rollback delete from ProjectLabel where id in (24);

--changeset guanrong:update_company_key_for_customer_company_of_irs
update Company set company_key = 'RHPGA' where company_name = 'RHP General Agency';
update Company set company_key = 'FFI' where company_name = 'Florida Family';
update Company set company_key = 'VC' where company_name = 'Velocity';
update Company set company_key = 'AT' where company_name = 'Allied Trust Insurance';
update Company set company_key = 'TWFG' where company_name = 'TWICO/TWFG';
-- rollback update Company set company_key = company_name where company_name in ('RHP General Agency', 'Florida Family', 'Velocity', 'Allied Trust Insurance', 'TWICO/TWFG');

-- changeset jianjing:add_No_Insured_Phone_project_label
INSERT INTO ProjectLabel(id, label_name, label_desc) value (25, 'No Insured Phone', 'NO_INSURED_PHONE');
-- rollback delete from ProjectLabel where id = 25;

-- changeset jianjing:modify_User_first_name_last_name
ALTER TABLE User MODIFY COLUMN first_name VARCHAR(128) NOT NULL, MODIFY COLUMN last_name VARCHAR(128) NOT NULL;
-- rollback ALTER TABLE User MODIFY COLUMN first_name VARCHAR(50) NOT NULL, MODIFY COLUMN last_name VARCHAR(50) NOT NULL;

-- changeset jianjing:abort_add_No_Insured_Phone_project_label
DELETE FROM ProjectLabel WHERE id = 25;
-- rollback INSERT INTO ProjectLabel(id, label_name, label_desc) value (25, 'No Insured Phone', 'NO_INSURED_PHONE');

-- changeset shoushan:add_cancellation_confirmed_project_label
INSERT INTO ProjectLabel(id, label_name, label_desc) value (26, 'Cancellation Confirmed', 'CANCELLATION_CONFIRMED');
-- rollback delete from ProjectLabel where id = 26;

-- changeset dunhong:create_project_timeline
CREATE TABLE IF NOT EXISTS ProjectTimeline (
    id BIGINT(20) primary key AUTO_INCREMENT,
    project_id BIGINT(20) NOT NULL,
    initial_customer_contact_time TIMESTAMP,
    customer_contacted_time TIMESTAMP,
    UNIQUE KEY (project_id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- rollback drop table ProjectTimeline;

-- changeset zhoubiao:add_attention_needed_for-loss_type_project_label
INSERT INTO ProjectLabel(id, label_name, label_desc) value (27, 'Attention Needed for Loss Type', 'ATTENTION_NEEDED_FOR_LOSS_TYPE');
-- rollback delete from ProjectLabel where id = 27;

-- changeset xiaojun:add_project_image_tag_table
CREATE TABLE IF NOT EXISTS `ProjectImageTag` (
    id BIGINT(20) primary key AUTO_INCREMENT,
    image_id VARCHAR(256) NOT NULL,
    image_category VARCHAR(256) NOT NULL,
    UNIQUE KEY (image_id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- rollback drop table ProjectImageTag

--changeset yangguanrong:change_uidx_xactware_id_to_idx
ALTER TABLE xa_xactnet_address DROP INDEX `idx_xactnet_address_xactware_id`;
ALTER TABLE xa_xactnet_address ADD INDEX `idx_xactnet_address_xactware_id` (`xactware_id`);
-- rollback ALTER TABLE xa_xactnet_address DROP INDEX `idx_xactnet_address_xactware_id`;
-- rollback ALTER TABLE xa_xactnet_address ADD UNIQUE INDEX `idx_xactnet_address_xactware_id` (`xactware_id`, `deleted`);

-- changeset xiaojun:add_unique_idx_on_project_operating_company
ALTER TABLE project_operating_company DROP INDEX `idx_project_id`;
ALTER TABLE project_operating_company ADD UNIQUE INDEX `unique_idx_project_id` (`project_id`);
-- rollback ALTER TABLE project_operating_company DROP INDEX `unique_idx_project_id`;
-- rollback ALTER TABLE project_operating_company ADD INDEX `idx_project_id` (`project_id`);

--changeset yangguanrong:update_company_for_security_first_florida
UPDATE Company SET company_key = 'Security First Insurance' WHERE company_name = 'Security First Florida';
-- rollback UPDATE Company SET company_key = 'Security First Florida' WHERE company_name = 'Security First Florida';

--changeset weizongfang:update_company_key_deleted
ALTER TABLE Company MODIFY COLUMN `is_deleted` bigint(20) NOT NULL DEFAULT 0;
UPDATE Company SET `is_deleted` = `company_id` WHERE `is_deleted` <> 0;
ALTER TABLE Company ADD UNIQUE INDEX company_key_deleted(`company_key`, `is_deleted`);
-- rollback DROP INDEX company_key_deleted on Company;
-- rollback UPDATE Company SET `is_deleted` = 1 WHERE `is_deleted` <> 0;
-- rollback ALTER TABLE Company MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0;

--changeset xinlinwu:update_file_export_record
ALTER TABLE file_export_record ADD INDEX `idx_executor_id` (`executor_id`);
ALTER TABLE file_export_record_batch ADD INDEX `idx_destination_id` (`destination_id`);
-- rollback ALTER TABLE file_export_record DROP INDEX `idx_executor_id`;
-- rollback ALTER TABLE file_export_record_batch DROP INDEX `idx_destination_id`;

--changeset xinlinwu:add_address_id_index
ALTER TABLE Project ADD INDEX `idx_address_id` (`address_id`);
--rollback ALTER TABLE Project DROP INDEX `idx_address_id`;

--changeset xinlinwu:add_external_user_id
ALTER TABLE `User` ADD COLUMN `external_user_id` varchar(32) NULL DEFAULT NULL;
--rollback ALTER TABLE `User` DROP COLUMN `external_user_id`

--changeset yangguanrong:create_table_project_inspection_schedule
CREATE TABLE IF NOT EXISTS `project_inspection_schedule` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `project_id` BIGINT NOT NULL,
  `scheduled_time` BIGINT NULL DEFAULT NULL,
  `due_date` BIGINT NULL DEFAULT NULL,
  `create_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_project_id` (`project_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10000 DEFAULT CHARSET=utf8mb4;
--rollback DROP TABLE IF EXISTS project_inspection_schedule;

--changeset yangguanrong:p82i107_create_table_project_inspection_schedule
/*
  set @claim_type_underwriting = 5;
  set @service_type_premium_four_point_underwriting = 7;
  - underwriting exclude Premium 4-point: project_inspection_schedule.due_date = project.inspection_time
  - underwriting of Premium 4-point:      project_inspection_schedule.scheduled_time = project.inspection_time
  - claim:                                project_inspection_schedule.scheduled_time = project.inspection_time
*/
INSERT INTO project_inspection_schedule(project_id, due_date) SELECT project_id, inspection_time FROM Project P
  WHERE P.claim_type = 5 and P.service_type != 7
  ON DUPLICATE KEY UPDATE due_date = P.inspection_time;
INSERT INTO project_inspection_schedule(project_id, scheduled_time) SELECT project_id, inspection_time FROM Project P
  WHERE P.claim_type = 5 and P.service_type = 7
  ON DUPLICATE KEY UPDATE scheduled_time = P.inspection_time;

INSERT INTO project_inspection_schedule(project_id, scheduled_time) SELECT project_id, inspection_time FROM Project P
  WHERE P.claim_type != 5
  ON DUPLICATE KEY UPDATE scheduled_time = P.inspection_time;
--rollback DELETE FROM project_inspection_schedule WHERE 1 = 1;

--changeset weizong:bb_change_batch_note_to_text
ALTER TABLE `BeesPilotBatch` MODIFY COLUMN `note` TEXT DEFAULT NULL;
-- rollback ALTER TABLE `BeesPilotBatch` MODIFY COLUMN `note` VARCHAR(500) DEFAULT NULL;

--changeset dunhong:create_table_project_state
CREATE TABLE IF NOT EXISTS project_state (
    id BIGINT(20) primary key AUTO_INCREMENT,
    project_id              BIGINT(20) NOT NULL UNIQUE,
    project_state           TEXT NOT NULL,
    project_state_reason TEXT NOT NULL
);
--rollback DROP TABLE IF EXISTS project_state;

--changeset dunhong:add_updated_at_to_project_state
ALTER TABLE `project_state` ADD COLUMN updated_at BIGINT;
--rollback ALTER TABLE `project_state` DROP COLUMN updated_at;

--changeset dunhong:add_inspector_needed_operation_tag
INSERT INTO ProjectLabel(id, `label_name`, `label_desc`) values (28, 'INSPECTOR_NEEDED', 'Inspector Needed');
-- rollback delete from ProjectLabel where id = 28;

--changeset liushuo:change_Member_to_project_member
ALTER TABLE Bees360.Member RENAME to Bees360.project_member;
-- rollback ALTER TABLE Bees360.project_member RENAME to Bees360.Member;

--changeset liushuo:delete_abandoned_table_web
DROP TABLE IF EXISTS Bees360.schema_migrations;
DROP TABLE IF EXISTS Bees360.TempCompanyLogoUrlToKey;
DROP TABLE IF EXISTS Bees360.TempUrlToKey;
DROP TABLE IF EXISTS Bees360.test;
DROP TABLE IF EXISTS Bees360.user;
DROP TABLE IF EXISTS Bees360.user_profile;
DROP TABLE IF EXISTS Bees360.user_finance;
DROP TABLE IF EXISTS Bees360.user_role;
DROP TABLE IF EXISTS Bees360.v_user;
DROP TABLE IF EXISTS Bees360Report.test;
--rollback select 'N/A'

--changeset linrui:create_table_address_airspace
CREATE TABLE IF NOT EXISTS address_airspace (
    id              BIGINT PRIMARY KEY AUTO_INCREMENT,
    address_id      VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    status          VARCHAR(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
    height_ceiling  INTEGER DEFAULT NULL,
    created_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uidx_address_id` (`address_id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
--rollback DROP TABLE IF EXISTS address_airspace;

--changeset linrui:create_table_project_airspace
CREATE TABLE IF NOT EXISTS project_airspace (
    id              BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_id      BIGINT NOT NULL,
    status          VARCHAR(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
    height_ceiling  INTEGER DEFAULT NULL,
    created_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uidx_project_id` (`project_id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
--rollback DROP TABLE IF EXISTS project_airspace;

--changeset dunhong:extend_user_external_user_id_length
ALTER TABLE `User` MODIFY `external_user_id` varchar(40) NULL DEFAULT NULL;
--rollback ALTER TABLE `User` MODIFY `external_user_id` varchar(32) NULL DEFAULT NULL;
