--liquibase formatted sql

--changeset chenyu:1_test_case
create table test  (
    id int primary key not null,
    name varchar(50)
);
-- rollback drop table test;

--changeset yangguanrong:2_add_table_xactnet_address
CREATE TABLE IF NOT EXISTS `xa_xactnet_address` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '逻辑主键',
  `xactware_id` varchar(512) NOT NULL COMMENT 'XA上用户的Xactware ID',
  `xactnet_address` varchar(512) NOT NULL COMMENT 'XA上用户的XactNet Address',
  `first_name` varchar(512) NOT NULL COMMENT 'XA上用户的first name',
  `last_name` varchar(512) NOT NULL COMMENT 'XA上用户的last name',
  `email` varchar(512) NOT NULL COMMENT 'XA上用户的email',
  `phone` varchar(512) NOT NULL COMMENT 'XA上用户的phone',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '该记录最后一次更新的时间',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '该记录创建的时间',
  `deleted` bigint(20) NOT NULL DEFAULT '0' COMMENT '软删除标志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_xactnet_address_email` (`email`,`deleted`),
  UNIQUE KEY `idx_xactnet_address_xactware_id` (`xactware_id`,`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8;
-- rollback drop table xa_xactnet_address;

--changeset yangguanrong:3_add_table_xa_assignment
CREATE TABLE IF NOT EXISTS `xa_assignment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '逻辑主键',
  `transaction_id` varchar(512) NOT NULL,
  `claim_number` varchar(512) NOT NULL,
  `profile_code` varchar(512) NOT NULL COMMENT 'The profile code is related with data set.',
  `carrier_id` varchar(512) NOT NULL COMMENT 'The carrier id is related with data set.',
  `assignment_data` text NOT NULL COMMENT 'The assignment xml data.',
  `assignee` varchar(512) NOT NULL COMMENT 'The adjuster address of the assignee.',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_transaction_id` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
-- rollback  drop table xa_assignment;

--changeset jianjing:drop_member_foreign_key
ALTER TABLE Member DROP FOREIGN KEY `Member_ibfk_2`;
-- rollback alter table Member add CONSTRAINT `Member_ibfk_2` FOREIGN KEY (user_id) REFERENCES User (user_id);

-- changeset jianjing:drop_foreign_key_reference_User
alter table Notification drop foreign key Notification_ibfk_2;
alter table Member drop foreign key Member_ibfk_3;
alter table ProjectStatus drop foreign key ProjectStatus_ibfk_2;
alter table EventHistory drop foreign key EventHistory_ibfk_2;
alter table EventHistory drop foreign key EventHistory_ibfk_3;
-- rollback alter table Notification add CONSTRAINT `Notification_ibfk_2` FOREIGN KEY (`recipient`) REFERENCES `User` (`user_id`);
-- rollback alter table Member add CONSTRAINT `Member_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `User` (`user_id`);
-- rollback alter table ProjectStatus add CONSTRAINT `ProjectStatus_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `User` (`user_id`);
-- rollback alter table EventHistory add CONSTRAINT `EventHistory_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `User` (`user_id`);
-- rollback alter table EventHistory add CONSTRAINT `EventHistory_ibfk_3` FOREIGN KEY (`modified_by`) REFERENCES `User` (`user_id`);

-- changeset jianjing:drop_foreign_key_ProjectImage
alter table ProjectImage drop foreign key ProjectImage_ibfk_2;
-- rollback alter table ProjectImage add CONSTRAINT ProjectImage_ibfk_2 FOREIGN KEY (`user_id`) REFERENCES `User` (`user_id`);

-- changeset mingcong:update_project_image_add_floor_number
ALTER TABLE Bees360.ProjectImage ADD floor_level varchar(100) NULL COMMENT '楼层信息';
ALTER TABLE Bees360.ProjectImage ADD `number` varchar(50) NULL COMMENT '序号';
-- rollback ALTER TABLE Bees360.ProjectImage DROP COLUMN floor_level;
-- rollback ALTER TABLE Bees360.ProjectImage DROP COLUMN `number`;

-- changeset ygr:insert_xactnet_address
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'Alan', 'Johnson', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'Ashlea', 'Hawkins', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'Brent', 'Baker', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'David', 'Lam', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'John', 'Bloomfield', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'Mark', 'Cooke', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'Mike', 'Galusha', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'Alicia', 'Lu', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'Paul', 'Matteson', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'Raquelle', 'Cady', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'Sunnie', 'Wu', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'Tammy', 'Martin', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'Tara', 'Markuson', '<EMAIL>', '');
insert ignore into xa_xactnet_address(xactware_id, xactnet_address, first_name, last_name, email, phone)
    values ('<EMAIL>', '<EMAIL>', 'Tommy', 'Guilbeaux', '<EMAIL>', '');
-- rollback delete from xa_xactnet_address where xactware_id in ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>');

-- changeset ygr:add_table_integration_connect
CREATE TABLE IF NOT EXISTS `integration_connect` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '逻辑主键',
  `integration_type` varchar(512) NOT NULL COMMENT '集成的平台类型',
  `customer_id` varchar(512) NOT NULL COMMENT '客户在集成平台的id',
  `customer_name` varchar(512) NOT NULL COMMENT '客户在集成平台的名称',
  `customer_code` varchar(512) NOT NULL COMMENT '客户在集成平台的独有code',
  `inner_user_id` varchar(512) NOT NULL COMMENT '在系统负责该客户的用户',
  `inner_customer_id` varchar(512) NOT NULL COMMENT '该客户在系统内部的id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_integration_connect_t_c` (`integration_type`,`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8;
-- rollback drop table integration_connect

-- changeset ygr:init_table_integration_connect
insert into integration_connect(integration_type, customer_id, customer_name, customer_code, inner_user_id, inner_customer_id)
  select 'xactanalysis.com' as integration_type, '7700013' as customer_id, 'GeoVera - Bees360' as customer_name, '5L' as customer_code, U.user_id as inner_user_id, C.company_id as inner_customer_id
    from Company C, User U where C.company_name = 'GeoVera Holdings - Claims' and C.is_deleted = 0
      and U.email = '<EMAIL>' and U.active_status = 1;

insert into integration_connect(integration_type, customer_id, customer_name, customer_code, inner_user_id, inner_customer_id)
  select 'xactanalysis.com' as integration_type, '2540113' as customer_id, 'Plymouth Rock - Bees360' as customer_name, '75' as customer_code, U.user_id as inner_user_id, C.company_id as inner_customer_id
    from Company C, User U where C.company_name = 'Plymouth Rock Home Assurance' and C.is_deleted = 0
      and U.email = '<EMAIL>' and U.active_status = 1;

insert into integration_connect(integration_type, customer_id, customer_name, customer_code, inner_user_id, inner_customer_id)
  select 'xactanalysis.com' as integration_type, '3398914' as customer_id, 'UPC - Bees360' as customer_name, 'FJ' as customer_code, U.user_id as inner_user_id, C.company_id as inner_customer_id
    from Company C, User U where C.company_name = 'UPC Insurance' and C.is_deleted = 0
      and U.email = '<EMAIL>' and U.active_status = 1;

insert into integration_connect(integration_type, customer_id, customer_name, customer_code, inner_user_id, inner_customer_id)
  select 'xactanalysis.com' as integration_type, '1613076' as customer_id, 'E1P - Bees360' as customer_name, '72' as customer_code, U.user_id as inner_user_id, C.company_id as inner_customer_id
    from Company C, User U where C.company_name = 'American Family' and C.is_deleted = 0
      and U.email = '<EMAIL>' and U.active_status = 1;

insert into integration_connect(integration_type, customer_id, customer_name, customer_code, inner_user_id, inner_customer_id)
  select 'xactanalysis.com' as integration_type, '6000044' as customer_id, 'TECH – Bees360' as customer_name, 'CG' as customer_code, U.user_id as inner_user_id, C.company_id as inner_customer_id
    from Company C, User U where C.company_name = 'Olympus Insurance' and C.is_deleted = 0
      and U.email = '<EMAIL>' and U.active_status = 1;

-- rollback delete from integration_connect where integration_type = 'xactanalysis.com' and customer_id = '7700013';
-- rollback delete from integration_connect where integration_type = 'xactanalysis.com' and customer_id = '2540113';
-- rollback delete from integration_connect where integration_type = 'xactanalysis.com' and customer_id = '3398914';
-- rollback delete from integration_connect where integration_type = 'xactanalysis.com' and customer_id = '1613076';
-- rollback delete from integration_connect where integration_type = 'xactanalysis.com' and customer_id = '6000044';

--changeset dunhong:create_iBees_user
INSERT INTO User(user_id, email,first_name, last_name, avatar, password, registration_time,
active_status, gps_location, most_recent_gps_location)
SELECT 9998, '<EMAIL>', 'iBees',  '', 'users/9998/default/avatar.jpg', '14A8064FF0611EBEC1147BE1D3F313F1',
1637033296008, 1 , POINT(7.0, 81.0), POINT(7.0, 81.0) FROM DUAL
WHERE NOT EXISTS (SELECT user_id FROM User WHERE email = '<EMAIL>' and active_status != -1);
-- rollback delete from User where user_id = '9998';
