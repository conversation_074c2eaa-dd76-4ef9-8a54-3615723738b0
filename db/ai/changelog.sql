--liquibase formatted sql

-- changeset mingcong:add_tapb_1001487
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1507, '7', 0, 19, 7);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1508, '8', 0, 19, 8);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1509, '9', 0, 19, 9);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1510, '10', 0, 19, 10);
UPDATE Bees360Report.ImageTagDict set sort = sort + 1 where category = 0 and `type` = 19 and sort > 1;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1511, '1', 0, 19, 2);
UPDATE Bees360Report.ImageTagDict set sort = sort + 2 where category = 0 and `type` = 4 and sort > 110;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1512, 'Utility Room', 0, 4, 111);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1513, 'Media Room', 0, 4, 112);
UPDATE Bees360Report.ImageTagDict set sort = sort + 4 where category = 6 and `type` = 5 and sort > 54;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1514, 'Stove', 6, 5, 55);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1515, 'Fridge', 6, 5, 56);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1516, 'Hood', 6, 5, 57);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1517, 'Shower Tub', 6, 5, 58);
-- rollback DELETE FROM Bees360Report.ImageTagDict where code in (1507, 1508, 1509, 1510, 1511, 1512, 1513, 1514, 1515, 1516, 1517);
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 1 where category = 0 and `type` = 19 and sort > 1;
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 2 where category = 0 and `type` = 4 and sort > 110;
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 4 where category = 6 and `type` = 5 and sort > 54;

-- changeset mingcong:add_tag_breakfast
UPDATE Bees360Report.ImageTagDict set sort = sort + 1 where category = 0 and `type` = 4 and sort > 85;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1518, 'Breakfast Room', 0, 4, 86);
-- rollback DELETE FROM Bees360Report.ImageTagDict where code in (1518);
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 1 where category = 0 and `type` = 4 and sort > 85;

-- changeset shoushan:add_image_compass
ALTER TABLE Bees360Report.ProjectImage ADD COLUMN compass DOUBLE COMMENT 'The picture was taken with the compass facing.';
-- rollback ALTER TABLE Bees360Report.ProjectImage DROP COLUMN compass;

-- changeset mingcong:update_PRR_to_PCA
UPDATE Bees360Report.ImageTagDict set name = 'PCA' where name ='PRR';
-- rollback UPDATE Bees360Report.ImageTagDict set name = 'PRR' where name ='PCA';

-- changeset mingchung:add_tag_tapb_10028882
UPDATE Bees360Report.ImageTagDict set sort = sort + 5 where category = 6 and `type` = 5 and sort > 54;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1519, 'Vent', 6, 5, 55);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1520, 'Pipe', 6, 5, 56);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1521, 'Awning', 6, 5, 57);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1522, 'Porch Ceiling', 6, 5, 58);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1523, 'Porch Pillar', 6, 5, 59);

UPDATE Bees360Report.ImageTagDict set sort = sort + 2 where category = 6 and `type` = 5 and sort > 17;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1524, 'Generator', 6, 5, 18);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1525, 'Satellite Dish', 6, 5, 19);

UPDATE Bees360Report.ImageTagDict set sort = sort + 1 where category = 6 and `type` = 5 and sort > 10;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1526, 'Fire Pit', 6, 5, 11);

UPDATE Bees360Report.ImageTagDict set name = 'Garden House' where name = 'Garden';
UPDATE Bees360Report.ImageTagDict set sort = sort + 1 where category = 3 and `type` = 1 and sort > 50;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1527, 'Garden Bed', 3, 1, 51);

UPDATE Bees360Report.ImageTagDict set sort = sort + 2 where category = 3 and `type` = 1 and sort > 60;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1528, 'Pond', 3, 1, 61);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1529, 'Patio', 3, 1, 62);

UPDATE Bees360Report.ImageTagDict set sort = sort + 1 where category = 3 and `type` = 1 and sort > 35;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1530, 'Storage Bin', 3, 1, 36);

UPDATE Bees360Report.ImageTagDict set sort = sort + 1 where category = 6 and `type` = 5 and sort > 25;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1531, 'Pool Cage', 6, 5, 26);
-- rollback delete from ImageTagDict where code in (1519, 1520, 1521, 1522, 1523, 1524, 1525, 1526, 1527, 1528, 1529, 1530, 1531);

-- changeset mingcong:add_tag_tapb_1002916_1
-- Swimming Pool
UPDATE Bees360Report.ImageTagDict set sort = 61 where code = 1109;
-- pool heater
UPDATE Bees360Report.ImageTagDict set sort = 25 where code = 523;
-- rollback UPDATE Bees360Report.ImageTagDict set sort = 64 where code = 1109;
-- rollback UPDATE Bees360Report.ImageTagDict set sort = 29 where code = 523;

-- changeset mingcong:add_tag_tapb_1002916_2
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1532, 'Barn', 3, 1, 53);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1533, 'Stable', 3, 1, 54);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1534, 'Tree', 3, 1, 91);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1535, 'Vehicle', 3, 1, 92);
-- rollback delete from ImageTagDict where code in (1532, 1533, 1534, 1535);

-- changeset mingcong:add_tag_tapb_1002916_3
UPDATE Bees360Report.ImageTagDict set sort = sort + 1 where category = 6 and `type` = 5 and sort > 63;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1536, 'Outdoor Light', 6, 5, 64);
-- rollback DELETE FROM Bees360Report.ImageTagDict where code in (1536);
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 1 where category = 6 and `type` = 5 and sort > 63;

-- changeset mingcong:add_tag_tapb_1002916_4
UPDATE Bees360Report.ImageTagDict set sort = sort + 1 where category = 6 and `type` = 5 and sort > 51;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1537, 'Screen', 6, 5, 52);
-- rollback DELETE FROM Bees360Report.ImageTagDict where code in (1537);
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 1 where category = 6 and `type` = 5 and sort > 51;

-- changeset mingcong:add_tag_tapb_1002916_5
UPDATE Bees360Report.ImageTagDict set sort = sort + 2 where category = 6 and `type` = 5 and sort > 34;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1538, 'Water Pump', 6, 5, 35);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1539, 'Water Filter/Softener', 6, 5, 36);
-- rollback DELETE FROM Bees360Report.ImageTagDict where code in (1538, 1539);
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 2 where category = 6 and `type` = 5 and sort > 34;

-- changeset mingcong:add_tag_BA_issue_10_1
UPDATE Bees360Report.ImageTagDict set sort = sort + 2 where code = 140;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1540, 'Dog Kennel', 3, 1, 55);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1541, 'Chicken Coop', 3, 1, 56);
-- rollback delete from ImageTagDict where code in (1540, 1541);
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 2 where code = 140;

-- changeset mingcong:add_tag_BA_issue_10_2
UPDATE Bees360Report.ImageTagDict set sort = sort + 1 where category = 6 and `type` = 5 and sort > 66;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1542, 'Porch Railing', 6, 5, 67);
-- rollback delete from ImageTagDict where code in (1542);
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 1 where category = 6 and `type` = 5 and sort > 66;

-- changeset mingcong:add_tag_BA_issue_10_3
UPDATE Bees360Report.ImageTagDict set sort = sort + 1 where category = 6 and `type` = 5 and sort > 61;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1543, 'Stair', 6, 5, 62);
-- rollback delete from ImageTagDict where code in (1543);
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 1 where category = 6 and `type` = 5 and sort > 61;

-- changeset mingcong:add_tag_BA_issue_10_4
UPDATE Bees360Report.ImageTagDict set sort = sort + 2 where category = 6 and `type` = 5 and sort > 36;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1544, 'Propane Tank', 6, 5, 37);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1545, 'Hot Tub', 6, 5, 38);
-- rollback delete from ImageTagDict where code in (1544, 1545);
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 2 where category = 6 and `type` = 5 and sort > 36;

-- changeset shoushan:add_image_tag_update_tag_dic_8_avengers
INSERT INTO ImageTagDict(id, dict_type, code, name) VALUES (15, 1, 10, 'Category');
UPDATE ImageTagDict SET type=10 WHERE id IN (106, 124, 129);
CREATE TABLE `ImageTag` (
  `image_id` varchar(32) NOT NULL,
  `tag_code` int(4) NOT NULL,
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'data create time',
  PRIMARY KEY (`image_id`,`tag_code`) USING BTREE,
  KEY `idx_ImageTagDict_code` (`tag_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='imageTag dict';
-- rollback DROP TABLE IF EXISTS `ImageTag`;
-- rollback UPDATE ImageTagDict SET type=1 WHERE id IN (106, 124, 129);
-- rollback DELETE FROM ImageTagDict WHERE id=15;

-- changeset shoushan:update_image_tag_id_encoding
ALTER TABLE ImageTag CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- rollback ALTER TABLE ImageTag CONVERT TO CHARACTER SET utf8 COLLATE utf8_general_ci;

-- changeset shoushan:add_image_direction_tag
UPDATE ImageTagDict SET sort=23 WHERE code=301;
UPDATE ImageTagDict SET sort=25 WHERE code=302;
UPDATE ImageTagDict SET sort=27 WHERE code=303;

INSERT INTO `Bees360Report`.`ImageTagDict` (`dict_type`, `code`, `name`, `category`, `type`, `sort`) VALUES (3, 305, 'FR', 0, 3, 24);
INSERT INTO `Bees360Report`.`ImageTagDict` (`dict_type`, `code`, `name`, `category`, `type`, `sort`) VALUES (3, 306, 'RR', 0, 3, 26);
INSERT INTO `Bees360Report`.`ImageTagDict` (`dict_type`, `code`, `name`, `category`, `type`, `sort`) VALUES (3, 307, 'RL', 0, 3, 28);
INSERT INTO `Bees360Report`.`ImageTagDict` (`dict_type`, `code`, `name`, `category`, `type`, `sort`) VALUES (3, 308, 'FL', 0, 3, 30);
-- rollback UPDATE ImageTagDict SET sort=26 WHERE code=301;
-- rollback UPDATE ImageTagDict SET sort=27 WHERE code=302;
-- rollback UPDATE ImageTagDict SET sort=28 WHERE code=303;
-- rollback DELETE FROM ImageTagDict WHERE `code` IN (305, 306, 307, 308);

-- changeset jianjing:add_5_object_tag
insert into ImageTagDict (dict_type, code, name, category, type, sort) values
(3, 1548, 'Jacuzzi', 3, 1, 62),
(3, 1549, 'Spa', 3, 1, 63),
(3, 1550, 'Boat', 3, 1, 93),
(3, 1551, 'Yacht', 3, 1, 94),
(3, 1552, 'Floor Plan', 5, 1, 101);
update ImageTagDict set sort = 64 where code = 1528 and name = 'Pond' and sort = 62;
update ImageTagDict set sort = 65 where code = 1529 and name = 'Patio' and sort = 63;
-- rollback delete from ImageTagDict where code in (1548, 1549, 1550, 1551, 1552);

-- changeset shoushan:add_image_cracked_tag
INSERT INTO `Bees360Report`.`ImageTagDict` (`dict_type`, `code`, `name`, `category`, `type`, `sort`) VALUES (3, 620, 'Cracked/Damaged Tile', 7, 5, 37);
-- rollback DELETE FROM ImageTagDict WHERE `code` = 620;

--changeset liushuo:delete_abandoned_table_ai
DROP TABLE IF EXISTS Bees360Report.Bidding;
DROP TABLE IF EXISTS Bees360Report.CustomizedReportElement;
DROP TABLE IF EXISTS Bees360Report.CustomizedReportItem;
DROP TABLE IF EXISTS Bees360Report.CustomizedSIBCategory;
DROP TABLE IF EXISTS Bees360Report.House;
DROP TABLE IF EXISTS Bees360Report.HouseCategory;
DROP TABLE IF EXISTS Bees360Report.HouseCategoryVersion;
DROP TABLE IF EXISTS Bees360Report.HouseImageSegmentType;
DROP TABLE IF EXISTS Bees360Report.HouseImageUnderwriting;
DROP TABLE IF EXISTS Bees360Report.HouseSegmentValue;
DROP TABLE IF EXISTS Bees360Report.ImageTag;
DROP TABLE IF EXISTS Bees360Report.ImageTagDict;
DROP TABLE IF EXISTS Bees360Report.ProjectCustomizedInfo;
DROP TABLE IF EXISTS Bees360Report.QRTZ_PAUSED_TRIGGER_GRPS;
DROP TABLE IF EXISTS Bees360Report.QRTZ_SCHEDULER_STATE;
DROP TABLE IF EXISTS Bees360Report.QRTZ_SIMPLE_TRIGGERS;
DROP TABLE IF EXISTS Bees360Report.QRTZ_SIMPROP_TRIGGERS;
DROP TABLE IF EXISTS Bees360Report.QRTZ_BLOB_TRIGGERS;
DROP TABLE IF EXISTS Bees360Report.QRTZ_CALENDARS;
DROP TABLE IF EXISTS Bees360Report.QRTZ_CRON_TRIGGERS;
DROP TABLE IF EXISTS Bees360Report.QRTZ_LOCKS;
DROP TABLE IF EXISTS Bees360Report.QRTZ_FIRED_TRIGGERS;
DROP TABLE IF EXISTS Bees360Report.QRTZ_TRIGGERS;
DROP TABLE IF EXISTS Bees360Report.QRTZ_JOB_DETAILS;
DROP TABLE IF EXISTS Bees360Report.ReportImageElement;
DROP TABLE IF EXISTS Bees360Report.ReportTemplate;
DROP TABLE IF EXISTS Bees360Report.schema_migrations;
DROP TABLE IF EXISTS Bees360Report.ReportMaterial;
DROP TABLE IF EXISTS Bees360Report.Template;
DROP TABLE IF EXISTS Bees360Report.TemplateConfig;
DROP TABLE IF EXISTS Bees360Report.TemplatePrefab;
DROP TABLE IF EXISTS Bees360Report.TemplateScreenshot;
DROP TABLE IF EXISTS Bees360Report.test;
DROP TABLE IF EXISTS Bees360Report.ImageAnnotation2D;
--rollback select 'N/A'
