--liquibase formatted sql

--changeset chenyu:1_test_case
create table test  (
    id int primary key not null,
    name varchar(50)
);
-- rollback drop table test;

--changeset mingcong:update_ai_projectReport_add_index
CREATE INDEX ProjectReportFile_report_type_IDX USING BTREE ON Bees360Report.ProjectReportFile (report_type,generation_status);
-- rollback ALTER TABLE ImageAnnotation DROP INDEX `ProjectReportFile_report_type_IDX`;

-- changeset mingcong:update_project_image_add_floor_number
ALTER TABLE Bees360Report.ProjectImage ADD floor_level_tag int(11) NULL COMMENT '楼层标签';
ALTER TABLE Bees360Report.ProjectImage ADD number_tag int(11) NULL COMMENT '序号标签';
-- rollback ALTER TABLE Bees360.ProjectImage DROP COLUMN floor_level_tag;
-- rollback ALTER TABLE Bees360.ProjectImage DROP COLUMN number_tag;

-- changeset mingcong:insert_image_tag_type_floor
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(1, 18, 'Floor Level', NULL, NULL, NULL);
UPDATE Bees360Report.ImageTagDict SET `type`=18 WHERE code=401;
UPDATE Bees360Report.ImageTagDict SET `type`=18 WHERE code=402;
UPDATE Bees360Report.ImageTagDict SET `type`=18 WHERE code=403;
UPDATE Bees360Report.ImageTagDict SET `type`=18 WHERE code=404;
UPDATE Bees360Report.ImageTagDict SET `type`=18 WHERE code=405;
UPDATE Bees360Report.ImageTagDict SET `type`=18 WHERE code=407;
UPDATE Bees360Report.ImageTagDict SET name='3rd Floor' WHERE code=403;
-- rollback DELETE FROM Bees360Report.ImageTagDict where name='Floor Level';
-- rollback UPDATE Bees360Report.ImageTagDict SET `type`=4 WHERE code=401;
-- rollback UPDATE Bees360Report.ImageTagDict SET `type`=4 WHERE code=402;
-- rollback UPDATE Bees360Report.ImageTagDict SET `type`=4 WHERE code=403;
-- rollback UPDATE Bees360Report.ImageTagDict SET `type`=4 WHERE code=404;
-- rollback UPDATE Bees360Report.ImageTagDict SET `type`=4 WHERE code=405;
-- rollback UPDATE Bees360Report.ImageTagDict SET `type`=4 WHERE code=407;
-- rollback UPDATE Bees360Report.ImageTagDict SET name='3nd Floor' WHERE code=403;

-- changeset mingcong:insert_image_tag_type_number
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(1, 19, 'Number', NULL, NULL, NULL);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1501, 'Master', 0, 19, 1);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1502, '2', 0, 19, 2);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1503, '3', 0, 19, 3);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1504, '4', 0, 19, 4);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1505, '5', 0, 19, 5);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1506, '6', 0, 19, 6);
-- rollback DELETE FROM Bees360Report.ImageTagDict where name='Number';
-- rollback DELETE FROM Bees360Report.ImageTagDict where code in (1507, 1508, 1509, 1510, 1511, 1512);


-- changeset mingcong:update_split_unit
UPDATE Bees360Report.ImageTagDict SET name='Split Unit' WHERE code=546;
-- rollback UPDATE Bees360Report.ImageTagDict SET name='Spilt Unit' WHERE code=546;

-- changeset mingcong:add_tapb_1001487
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1507, '7', 0, 19, 7);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1508, '8', 0, 19, 8);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1509, '9', 0, 19, 9);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1510, '10', 0, 19, 10);
UPDATE Bees360Report.ImageTagDict set sort = sort + 1 where category = 0 and `type` = 19 and sort > 1;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1511, '1', 0, 19, 2);
UPDATE Bees360Report.ImageTagDict set sort = sort + 2 where category = 0 and `type` = 4 and sort > 110;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1512, 'Utility Room', 0, 4, 111);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1513, 'Media Room', 0, 4, 112);
UPDATE Bees360Report.ImageTagDict set sort = sort + 4 where category = 6 and `type` = 5 and sort > 54;
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1514, 'Stove', 6, 5, 55);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1515, 'Fridge', 6, 5, 56);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1516, 'Hood', 6, 5, 57);
INSERT INTO Bees360Report.ImageTagDict (dict_type, code, name, category, `type`, sort) VALUES(3, 1517, 'Shower Tub', 6, 5, 58);
-- rollback DELETE FROM Bees360Report.ImageTagDict where code in (1507, 1508, 1509, 1510, 1511, 1512, 1513, 1514, 1515, 1516, 1517);
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 1 where category = 0 and `type` = 19 and sort > 1;
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 2 where category = 0 and `type` = 4 and sort > 110;
-- rollback UPDATE Bees360Report.ImageTagDict set sort = sort - 4 where category = 6 and `type` = 5 and sort > 54;
