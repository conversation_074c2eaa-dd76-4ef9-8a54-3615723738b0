syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.common";
package api.common;

service FileStreamingService {
    rpc pull (stream FileChunk) returns (stream TransferResponse);
    rpc push (stream FileChunk) returns (stream TransferResponse);
}

message FileChunk {
    int64 projectId = 1;
    int32 chunkIndex = 2;
    Chunk chunk = 3;
}

message Chunk {
    bytes content = 1;
}

message TransferResponse {
    int64 projectId = 1;
    int32 chunkIndex = 2;
    TransferStatusCode code = 3;
    string message = 4;
}

enum TransferStatusCode {
    UNKNOWN = 0;
    OK = 1;
    FAILED = 2;
}
