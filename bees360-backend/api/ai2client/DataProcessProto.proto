syntax = "proto3";

option java_package = "com.bees360.internal.ai.exchange.ai2client";

package api.ai2client;

message DataProcessStage {
    int64 project_id = 1;
    DataProcessStageItem modeling = 2;
    DataProcessStageItem ranging = 3;
    DataProcessStageItem scoping = 4;
    DataProcessStageItem plane = 5;
    DataProcessStageItem boundary = 6;
    DataProcessStageItem confirm = 7;
    string latest_ai_stage_status = 8;
}

message DataProcessStageItem {
    // 通过该请求，可以获取该阶段的数据
    string stage_name = 1;
    // 该阶段初始化点云文件key或者save时的点云文件key(因为plane和boundary阶段save时的数据结构和confirm时的数据结构不一样,所以save时保留到这个字段)
    string data_path = 2;
    repeated int32 selected_indexes = 3;
    int64 created_at = 4;
    int64 updated_at = 5;
    string updated_by = 6;
    string status = 7;
    string msg = 8;
    bool is_next = 9;
    //点云数据操作类型 1:save;2:confirm
    int32 operation_type = 10;
    // confirm时临时保存的点云文件key(plane和boundary阶段save时的数据结构和confirm是的数据结构不一样)
    string result_data_path = 11;
}

message SelectedIndexes {
    repeated int32 indexes = 1;
}

message ModelingParams {
    int32 image_size = 1;
    int32 pmvs_level = 2;
    int32 pmvsw_size = 3;
    double pmvs_threshold = 4;
    int32 pmvs_max_angle = 5;
    double pmvs_quad = 6;
    string feature_level = 7;
}

message DataProcessStageConfirm {
    bool is_success = 1;
    // 如果不是成功的，则需要提供理由
    string reason = 2;
}
