syntax = "proto3";

option java_package = "com.bees360.internal.ai.exchange.ai2client";
package api.ai2client;

import public "google/protobuf/timestamp.proto";


message ProjectDiscussParam {
    string note = 1;
    repeated DiscussionParticipantParam participants = 2;
}

message DiscussionParticipantParam {
    string refer_user_id = 1;
    string refer_group = 2;
}

message DiscussMarkReadParam {
    string note_id = 1;
}

message ProjectDiscussMessage {
    int64 project_id = 1;
    repeated DiscussEntryMessage entry = 2;
    int64 un_read_num = 3;
}

message DiscussEntryMessage {
    string note_id = 1;
    string note = 2;
    string created_by = 3;
    string created_name = 4;
    int64 created_at = 5;
    repeated DiscussionParticipantMessage participants = 6;
    string avatar_url = 7;
    repeated string creator_roles = 8;
}

message DiscussionParticipantMessage {
    string refer_user_id = 1;
    string refer_group = 2;
    string refer_name = 3;
    bool is_read = 4;
    repeated string is_read_by_user = 5;
}
