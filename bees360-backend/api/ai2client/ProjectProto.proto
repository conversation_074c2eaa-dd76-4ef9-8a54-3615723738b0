syntax = "proto3";

option java_package = "com.bees360.internal.ai.exchange.ai2client";
package api.ai2client;

import "ai2client/CommonProto.proto";

message ProjectPageResult {
    repeated ProjectListItem items = 1;
    Pagination page = 2;
}

message ProjectListItem {
    int64 project_id = 1;
    string full_address = 2;
    int64 created_time = 3;
}

message ProjectProperty {
    int64 project_id = 1;
    // 房屋的类型
    int32 project_type = 2;
    string address = 3;
    string city = 4;
    string state= 5;
    string country = 6;
    string zip_code = 7;
    // 经度
    double lng = 8;
    // 纬度
    double lat = 9;
    string description = 10;
}

message ProjectSearch {
    // 项目是否已经结束
    // bool is_finished = 1;
    // 创建时间结束
    int64 start_time = 2;
    // 创建时间开始
    int64 end_time = 3;
    // 页码数，从1开始
    int32 page_index = 4;
    int32 page_size = 5;

    string sort_key = 6;
    // desc/asc
    string sort_order = 7;
}

message ProjectMapPointResult {
    repeated ProjectMapPoint items = 1;
}
message ProjectMapPoint {
    int64 project_id = 1;
	int64 inspection_time = 2;
    int32 inspection_purpose_type_code = 3;
	string inspection_purpose_type_name = 4;
    double gps_location_latitude = 5;
    double gps_location_longitude = 6;
    string address = 7;
    string city = 8;
    string state = 9;
	string zip_code = 10;
    string country = 11;
}
message ProjectMapPointSearchOption {
    // project's created timestamp start point
    int64 start_time = 1;
    // project's created timestamp end point
    int64 end_time = 2;
    // project's latest status
    int32 latest_status = 3;
    // format is x,y (longitude, latitude)
    string center = 4;
    // as a query for projectId
    int64 search_project_id = 5;
    // as a query for address
    string search_address = 6;
    // project status array
    string project_statuses = 7;
    string inspection_purpose_types = 8;
    // start point of inspectionTime timestamp
    int64 inspection_start_time = 9;
    // end point of inspectionTime timestamp
    int64 inspection_end_time = 10;
}

message ProjectImageUpload {
    int32 inspectionTypes = 1;
    int32 fileSourceType = 2;
}

message ProjectCanStart3D {
    bool is3dable = 1;
}

message ProjectBatchUpdateStatus {
    repeated int64 project_ids = 1;
    int32 status = 2;
    bool changeForce = 3;
}

message ImageScoreMessage {
    float image_score = 1;
    string pilot_id = 2;
}

message ProjectRange {
    int64 min_project_id = 1;
    int64 max_project_id = 2;
}
