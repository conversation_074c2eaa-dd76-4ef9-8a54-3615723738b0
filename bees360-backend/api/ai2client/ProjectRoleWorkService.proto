syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.web2ai";
package api.web2ai;

import "common/StatusCodeResponse.proto";

service ProjectRoleWorkService {
    rpc calAiRoleWorkResult(ProjectRoleWorkResult) returns (common.StatusCodeResponse);
}

message ProjectRoleWorkResult {
    string start_date = 1;
    string end_date = 2;
    repeated ProjectWorkItem claim = 3;
    repeated ProjectWorkItem underwriting = 4;
}

message ProjectWorkItem {
    string role_name = 1;
    int32 count = 2;
}
