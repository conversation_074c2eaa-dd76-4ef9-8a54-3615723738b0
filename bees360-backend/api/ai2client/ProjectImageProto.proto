syntax = "proto3";

option java_package = "com.bees360.internal.ai.exchange.ai2client";

package api.ai2client;

import "report2web/ImageProto.proto";
import "google/protobuf/wrappers.proto";

// 图片列表
message ProjectImageList {
    repeated ProjectImage images = 1;
}

// 图片信息
message ProjectImage {
    string image_id = 1;
    // 图片所属项目
    int64 project_id = 2;
    // 上传该图片的用户
    int64 user_id = 3;
    // 原图路径
    string file_name = 4;
    // 中图路径
    string file_name_middle_resolution = 5;
    // 小图路径
    string file_name_lower_resolution = 6;
    // 标注图片
    string annotation_image = 7;
    // 原图名称
    string original_file_name = 8;
    // 原图高度
    int32 image_height = 9;
    // 原图宽度
    int32 image_width = 10;
    // 原图的大小
    int64 file_size = 11;
    // 图片的拍摄来源类型
    int32 file_source_type = 12;
    // 图片的拍摄内容类型
    int32 image_type = 13;
    // 东南西北朝向
    int32 direction = 14;
    // 是否有手动标注
    bool manually_annotated = 15;
    // 前后左右朝向
    int32 orientation = 16;

    // 上传时间
    int64 upload_time = 18;
    // 删除标志
    bool deleted = 19;

    // =========================================
    // Metadata

    // Metadata: 纬度
    double gps_location_latitude = 20;
    // Metadata: 经度
    double gps_location_longitude = 21;
    // Metadata: 图片信息中的 TIFF/Orientation，默认值为1(Normal)
    int32 tiff_orientation = 22;
    // Metadata: 相对起飞点高度
    double relative_altitude = 23;

    // =========================================
    // 其他辅助信息

    string image_category = 24;
    int64 shooting_time = 25;

    int32 partial_type = 26;

    // 是否参与3d建模 1:参与建模;-1:非参与建模; 0:默认值(兼容之前版本的图片属性处理)
    int32 in3DModel = 27;

    int64 image_sort = 28;

    int32 object_tag = 29;

    int32 location_tag = 30;

    int32 scope_tag = 31;

    int32 direction_tag = 32;

    repeated report2web.ImageAnnotation annotations = 33;

    report2web.ImageCreatedVo image_created_vo = 34;

    string room_name = 35;

    int32 report_tag = 36;

    //朝向，东南西北
    int32 orientation_tag = 37;

    // 楼层
    int32 floor_level_tag = 38;

    // 序号
    int32 number_tag = 39;

    google.protobuf.DoubleValue compass = 40;

    int32 category_tag = 41;

    string origin_id = 42;

    repeated string note = 43;

    ImageTag category_image_tag = 44;
    ImageTag object_image_tag = 45;
    ImageTag location_image_tag = 46;
    ImageTag scope_image_tag = 47;
    ImageTag direction_image_tag = 48;
    ImageTag orientation_image_tag = 49;
    ImageTag report_image_tag = 50;
    ImageTag floor_level_image_tag = 51;
    ImageTag number_image_tag = 52;
}

// image tag 基本信息列表
message ImageTag {
    string id = 1;
    int32 tag_id = 2;
    report2web.AttributeMessage attribute = 3;
}

// 图片基本信息列表
message TinyProjectImageList {
    // 图片列表
    repeated TinyProjectImage images = 1;
}

// 图片最基本信息
// 一张图片基本的信息，尽量携带多个不同的size
message TinyProjectImage {
    string image_id = 1;
    // 原图路径
    string file_name = 2;
    // 中图路径
    string file_name_middle_resolution = 3;
    // 小图路径
    string file_name_lower_resolution = 4;
    // 图片原始名称
    string original_file_name = 5;
    // 原图高度
    int32 image_height = 6;
    // 原图宽度
    int32 image_width = 7;
}

// 一个对图片的简单过滤
message ImageFilter {
    repeated string imageIds = 1;
}

// 图片检索
message ImageQuery {
    // 图片的拍摄来源类型
    int32 file_source_type = 1;
    // 是否被删除
    bool deleted = 2;
}

// 图片 orientation
message ImageOrientation {
    // 图片的拍摄来源类型
    int32 orientation = 1;
}

message ImageDistanceRequest {
    repeated Line line = 1;

    message Line {
        Point start = 1;
        Point end = 2;
    }

    message Point {
        double x = 1;
        double y = 2;
    }
}

message ImageDistanceResult {
    repeated double distance = 1;
}

message ImageOrientationItems {
    repeated ImageOrientationItem orientations = 1;
}

message ImageOrientationItem {
    int32 orientation = 1;
    repeated string imageIds = 2;
}

message ImageIn3DModelItems {
    repeated ImageIn3DModelItem in3DModelItem = 1;
}

message ImageIn3DModelItem {
    int32 in3DModel = 1;
    repeated string imageIds = 2;
}

message ImageSort {
    repeated IdValue sorts = 1;
}

message IdValue {
    string id = 1;
    int64 value = 2;
}

message ImageTypeTreeItems {
    repeated ImageTypeTree items = 1;
}

message ImageTypeTree {
    int64 id = 1;
    string value = 2;
    repeated ImageTypeTree children = 3;
}

message ImageDirectionItems {
    repeated ImageDirection items = 1;
}

message ImageDirection {
    string image_id = 1;
    // East and so on, it must be assigned a value through the Enum DirectionEnum.
    int32 direction = 2;
    string file_name_lower_resolution = 3;
}

message ImageOrientationUpdateList {
    repeated ImageOrientationUpdate orientations = 1;
}

message ImageOrientationUpdate {
    int32 orientation = 1;
	repeated string image_ids = 2;
}

message ImageRecoverDelete {
    repeated string image_ids = 1;
    // true for delete, false for recovery from delete
    bool deleted = 2;
}

message ImageTypeUpdateList {
    repeated ImageTypeUpdate types = 1;
}

message ImageTypeUpdate{
    int32 image_type = 1;
    repeated string image_ids = 2;
}

message ImagePropertyUpdate{
    int32 image_type = 1;
    int32 partial_type = 2;
    int32 orientation = 3;
    repeated string image_ids = 4;
}

// this is designed for common user, don't change/add field
message ImageIdList {
    repeated string image_ids = 1;
}

message ImageCompassList {
    repeated ImageIdCompass imageIdCompass = 1;
}

message ImageIdCompass {
    string image_id = 1;
    google.protobuf.DoubleValue compass = 2;
}

message ImageCopyResult {
    repeated ProjectImage success_images = 1;
    repeated ProjectImage fail_images = 2;
}

message ImageUploadResult {
    repeated ProjectImage success_images = 1;
    repeated ProjectImage fail_images = 2;
}

message ImageUpload {
    int32 file_source_type = 1;
    repeated ProjectImage images = 2;
}

message ImageFileSourceTypeUpdate{
    repeated string image_ids = 1;
    int32 file_source_type = 2;
}

message ImageTagDirectoryMessage {
    repeated ImageTagMessage object = 1;
    repeated ImageTagMessage location = 2;
    repeated ImageTagMessage scope = 3;
    repeated ImageTagMessage direction = 4;
    repeated ImageTagMessage annotation = 5;
    repeated ImageTagMessage category = 6;
}

message ImageTagMessage {
    int32 id = 1;
    string name = 2;
    CodeNameMessage type = 3;
    CodeNameMessage category = 4;
}

message CodeNameMessage {
    int32 code = 1;
    string name = 2;
}

message ImageTagListRequest {
    repeated ImageTagRequest image_tag = 1;
    int64 project_id = 2;
}

message ImageTagChangeRequest {
    repeated string image_ids = 1;
    int32 tag_id = 2;
    bool is_deleted = 3;
    int64 project_id = 4;
}

message ImageTagRequest {
    int32 type_id = 1;
    int32 tag_id = 2;
}


message ImageAnnotationModel {
    int64 annotationId = 1;

    string imageId = 2;

    int32 facetId = 3;

    int64 projectId = 4;

    string annotationPolygon = 5;

    double centerPointX = 6;

    double centerPointY = 7;

    int32 annotationType = 8;

    int32 usageType = 9;

    int64 generatedBy = 10;

    int32 sourceType = 11;

    int64 createdTime = 12;

    string categoryTagName = 13;

    string tagName = 14;

    string remark = 15;
}

message ImageTagResortRequest {
    string prevImageId = 1;
    string nextImageId = 2;
    ImageTagRequest tag = 3;
}

message ADParameter {
    bool forcedStart = 1;
    repeated int32 fileSourceTypes = 2;
    int32 count = 3;
    float level = 4;
    int32 damageShown = 5;
    repeated string imageIds = 6;
}

message ADDeleteParameter {
    repeated int32 damageTypes = 1;
    float level = 2;
}

message DerivativeImage {
    int64 size = 1;
    string originId = 2;
    string resourceKey = 3;
    int32 width = 4;
    int32 height = 5;
}

message DerivativeImageRequest {
    repeated DerivativeImage derivativeImages = 1;
}
