syntax = "proto3";

option java_package = "com.bees360.internal.ai.exchange.ai2client";
package api.ai2client;

import "ai2client/CommonProto.proto";

message Company {
    int64 company_id = 1;
    string company_name = 2;
    // 公司的业务类型
    int32 company_type = 3;
    string phone = 4;
    string email = 5;
    string contact_name = 6;
    string website = 7;
    string logo = 8;
    bool is_deleted = 9;
    int64 created_time = 10;
    int64 updated_time = 11;
}
