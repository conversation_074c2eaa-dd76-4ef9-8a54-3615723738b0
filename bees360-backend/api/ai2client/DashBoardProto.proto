syntax = "proto3";

option java_package = "com.bees360.internal.ai.exchange.ai2client";
package api.ai2client;

import "ai2client/CommonProto.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/type/money.proto";

message DashBoardDict {
    string dictType = 1;
    repeated CodeNameDto codeNames = 2;
}

message ProjectStatusVo {
    CodeNameDto status = 1;
    int64 createdTime = 2;
    string userName = 3;
}

message CodeNameDto {
    int32 code = 1;
    string name = 2;
}

service ProjectCandidatesSearch {
    rpc getProjectCandidatesSearch(ProjectMembersQuery) returns (MemberCandidates);
}

message ProjectMembersQuery {
    int64 user_id = 1;
    string name = 2;
    string memberType = 3;
}

message ProjectMembersParam {
    MemberInfo members = 1;
}

message ProjectMembersResult {
    int64 project_id = 1;
    repeated MemberInfo members = 2;
}

message ProjectMemberBatch {
    repeated int64 project_ids = 1;
    MemberInfo member = 2;
}

message MemberCandidates {
    repeated MemberInfo processors = 1;
    repeated MemberInfo adjusters = 2;
    repeated MemberInfo reviewers = 3;
}

message MemberCandidatesParam {
    string name = 1;
}

message MemberInfo {
    string id = 1;
    string name = 2;
    string auth = 3;
    string phone = 4;
    string email = 5;
    string avatarUrl = 6;
    string photo = 7;
}


message IdNameDto {
    string id = 1;
    string name = 2;
}

message ProjectEsQueryParam{
    message PipelineTaskQuery {
        // pipeline task definition key should not be empty
        string key = 1;
        repeated int32 status = 2;
        // default present query all task, '*' present query all assigned owner, '-' present to query unassigned owner task
        google.protobuf.StringValue owner_id = 3;
        google.protobuf.Timestamp start_at = 4;
        google.protobuf.Timestamp end_at = 5;
    }

    int64 projectId = 1;

    string policyNumber = 2;

    string address = 3;

    string city = 4;

    string state = 5;

    int32 projectStatus = 6;

    repeated string companyName = 7;

    repeated string pilot = 8;

    int64 createStartTime = 9;
    int64 createEndTime = 10;

    int64 inspectionStartTime = 11;
    int64 inspectionEndTime = 12;

    string roleTag = 13;
    string sortTag = 14;

    repeated int32 serviceTypes = 15;

    bool searchDaysOld = 16;

    int64 daysOldStart = 17;

    int64 daysOldEnd = 18;

    string orderField = 20;

    string orderDesc = 21;

    string inspectionNumber = 22;

    string statusTag = 23;

    string creatorName = 24;

    int32 pageIndex = 25;

    int32 pageSize = 26;

    string memberRole = 27;
    string memberName = 28;

    bool isClaimSearch = 29;

    bool isExport = 30;

    repeated int64 projectTags = 31;

    string assetOwnerName = 32;
    string assetOwnerPhone = 33;

    string policyEffectiveDateStart = 34;
    string policyEffectiveDateEnd = 35;

    string darHandler = 36;
    string estimateHandler = 37;

    string zipCode = 39;

    int64 projectStatusStartTime = 40;
    int64 projectStatusEndTime = 41;

    repeated string projectTagList = 42;

    // 多个查询结果交集
    repeated PipelineTaskQuery pipeline_task_query = 43;

    string catSerialNumber = 44;

    repeated int32 claimTypes = 45;

    repeated int32 projectTypes = 46;

    repeated string insuranceCompanyName = 47;

    repeated string processCompanyName = 48;

    // multi status search
    repeated int32 projectStatusList = 49;

    google.protobuf.Int32Value droneImageCountMax = 50;
    google.protobuf.Int32Value droneImageCountMin = 51;
    google.protobuf.Int32Value mobileImageCountMax = 52;
    google.protobuf.Int32Value mobileImageCountMin = 53;

    repeated int32 darStatusList = 54;

    repeated int32 pirStatusList = 55;

    repeated string operatingCompanyList = 56;

    repeated string policyTypeList = 57;

    repeated int64 projectIds = 58;

    // multi project state search
    repeated string projectState = 59;

    string projectStateChangeReason = 60;

    repeated string states = 61;

    repeated string changeReasonGroups = 62;

    repeated string policyNumbers = 63;

    repeated string inspectionNumbers = 64;

    google.protobuf.BoolValue isParentProject = 65;

    google.protobuf.BoolValue isChildProject = 66;
}

message ProjectStatusTagNum {
    int64 todo_num = 1;
    int64 active_num = 2;
    int64 closed_num = 3;
}

message ProjectStatusTagNumMessage {
    ProjectNumMessage project_num = 1;
    ProjectNumMessage discuss_num = 2;
}

message ProjectNumMessage {
    int64 todo_num = 1;
    int64 active_num = 2;
    int64 closed_num = 3;
    int64 urgent_num = 4;
    int64 dar_num = 5;
    int64 estimate_num = 6;
    int64 review_num = 7;
    int64 archived_num = 8;
    int64 rework_num = 9;
}

message StatusRequest {
    int32 status = 1;
}

message ProjectTagsRequest{
    repeated int64 project_tag = 1;
    bool isDelete = 2;
}

message ProjectCalResult{
    repeated ProjectCalQueryVo items = 1;
}

message ProjectCalQueryVo{
    int64 project_id = 1;
    // 飞手名字
    string pilot_name = 2;
    // processor 名字
    string process_name = 3;
    // 触发 site inspected 事件时间
    int64 site_inspected = 4;
    // 触发 image uploaded 事件时间
    int64 image_uploaded = 5;
    // 服务器的编号
    string server_no = 6;
    // 3d 开始时间
    int64 third_started = 7;
    // 3d 完成时间
    int64 third_finished = 8;
    // plane 完成时间
    int64 plane_finished = 9;
    // post-boundary 完成时间
    int64 third_processed = 10;
    // pir 报告生成时间
    int64 pir_generated = 11;
    // dar 报告生成时间
    int64 dar_generated = 12;
    // pir 报告 submitted 时间
    int64 pir_submitted = 13;
    // dar 报告 submitted 时间
    int64 dar_submitted = 14;
    // dar 报告最后一次 submitted 时间
    int64 dar_last_submitted = 71;
    // dar 报告 submitted 次数
    int32 dar_submitted_times = 72;
    // fsr 报告 submitted 时间
    int64 fsr_submitted = 15;
    // ror 报告 submitted 时间
    int64 ror_submitted = 16;
    // adjuster 名字
    string adjuster_name = 17;
    // reviewer 名字
    string reviewer_name = 18;
    // pir 报告 approved 时间
    int64 pir_approved = 19;
    // dar 报告 approved 时间
    int64 dar_approved = 20;
    // dar 报告最后一次 approved 时间
    int64 dar_last_approved = 73;
    // dar 报告 approved 次数
    int32 dar_approved_times = 74;
    // fsr 报告  approved 时间
    int64 fsr_approved = 21;
    // ror 报告 approved 时间
    int64 ror_approved = 22;
    string policy_number = 23;
    string claim_number = 24;
    string insured_name = 25;
    string insured_phone = 26;
    string insured_email = 27;
    string street_address = 28;
    string city = 29;
    string state = 30;
    string zip_code = 31;
    string cat_number = 32;
    int64 create_time = 33;
    int64 completion_time = 34;
    string batch_number = 35;
    double total_pay = 36;
    int32 service_type = 37;
    double latitude = 38;
    double longitude = 39;
    int32 fly_zone_type = 40;
    int64 days_old = 41;
    int64 customer_contacted = 42;
    int64 assigned_to_pilot = 43;
    int64 inspection_dead_line = 44;
    string external_adjuster_name = 45;
    string creator = 46;
    bool subscribePlnar = 47;
    bool paid = 48;
    bool canceled = 49;
    int64 client_received = 50;
    int64 payment_date = 51;
    string policy_effective_date = 52;
    repeated int64 project_tags = 53;
    repeated string pilot_feedback = 54;
    string dar_approved_name = 55;
    int64 estimate_complete_time = 56;
    string estimate_complete_name = 57;
    string client_received_name = 58;
    string insured_by = 59;
    float hive_distance = 60;
    google.type.Money estimate_total_pay = 61;
    string producer_name = 62;
    string pir_submitted_by = 63;
    string operating_company = 64;
    string policy_type = 65;
    int32 hover_status = 66;
    int32 cat_level = 67;
    string project_state = 68;
    string project_state_change_reason = 69;
    AirspaceMessage airspace = 70;
    repeated ProjectStatusVo timeLines = 75;
    int32 drone_image_count = 76;
    int32 mobile_image_count = 77;
    string change_reason_group = 78;
    int32 project_type = 79;
}

message AirspaceMessage {
    // allow to be empty string
    string status = 1;
    google.protobuf.Int32Value height_ceiling = 2;
}

message ProjectFolderRequest {
    repeated int64 project_ids = 1;
    string type = 2;
}
