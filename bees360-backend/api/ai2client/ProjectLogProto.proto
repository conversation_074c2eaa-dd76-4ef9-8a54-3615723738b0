syntax = "proto3";

option java_package = "com.bees360.internal.ai.exchange.ai2client";
package api.ai2client;

import public "google/protobuf/timestamp.proto";
import "ai2client/DashBoardProto.proto";


message ProjectLogMessage {

    int64 project_id = 1;
    repeated LogEntryMessage log_entry = 2;

}

message LogEntryMessage {
    string user_id = 1;

    string username = 2;

    string type = 3;

    google.protobuf.Timestamp created_time = 4;

    LogDetailMessage log_detail = 5;

}

message LogDetailMessage {
    int32 detailCode = 1;
    int32 actionCode = 2;
}

message LogEntryTypeDictMessage {
    string type = 1;
    repeated CodeNameDto detail = 2;
    repeated CodeNameDto action = 3;
}

message DictMessage {
    repeated LogEntryTypeDictMessage dict = 1;
}
