syntax = "proto3";

option java_package = "com.bees360.internal.ai.exchange.ai2client";
package api.ai2client;

message Pagination {
    int32 page_index = 1;
	int32 page_size = 2;
    int64 total_page = 3;
	int64 sum = 4;
}

message OperationResult {
    bool isSuccess = 1;
}

message BatchUpdateResult {
    repeated int64 succeed = 1;
    repeated UpdateFailed failed = 2;
}

message UpdateFailed {
    int64 id = 1;
    string message = 2;
    OperationFeedback operation_feedback = 3;

    enum OperationFeedback {

        UNDEFINED = 0;

        ALLOWED = 1;

        NOT_FORCED = 2;

        NOT_ALLOWED = 3;
    }
}
