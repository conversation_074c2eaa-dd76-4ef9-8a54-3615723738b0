syntax = "proto3";

option java_package = "com.bees360.internal.ai.exchange.ai2client";
package api.ai2client;

// 报告列表
message ReportList {
    repeated Report reports = 1;
}

// 报告
message Report {
    string report_id = 1;
    int64 project_id = 2;
    // 报告的类型
    int32 report_type = 3;
    string report_name = 4;
    // 报告的状态
    int32 report_status = 5;
    // 报告文件的大小
    string size = 6;
    // 报告文件的格式
    string mime_type = 7;
    // 生成这份报告的人
    int64 created_by = 8;
    int64 created_time = 9;
}
