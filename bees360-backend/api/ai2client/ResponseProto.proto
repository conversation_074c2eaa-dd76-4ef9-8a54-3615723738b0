syntax="proto3";

option java_package = "com.bees360.internal.ai.exchange.ai2client";

package api.ai2client;

import "common/AnyDataProto.proto";

message ResponseBody {
    string code = 1;
    string message = 2;
    common.AnyData data = 3;
}

message ResponseErrorBody {
    string code = 1;
    string message = 2;
    ValidErrors data = 3;
}

message ValidErrors {
    repeated ValidError errors = 1;
}

message ValidError {
    ValidErrorType type = 1;
    string field = 2;
    string message = 3;

    enum ValidErrorType {
        // 无法识别类型
        UNRECOGNIZED_TYPE = 0;
        // 缺少必要字段
        MISSING_FIELD = 1;
        // 校验失败，详细见文档
        INVALID = 2;
        // 类型不匹配
        TYPE_MISMATCH = 3;
    }
}
