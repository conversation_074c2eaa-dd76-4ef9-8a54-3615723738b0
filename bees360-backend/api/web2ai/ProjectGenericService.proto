syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.web2ai";
package api.web2ai;

import "common/StatusCodeResponse.proto";

service ProjectGenericService {
    rpc updateProjectStatusOnWebStatusChange(ProjectStatusChangeRequest) returns (common.StatusCodeResponse);
    rpc addProjectMessageOnWebChange(ProjectMessage) returns (common.StatusCodeResponse);
    rpc deleteAiImagesOnWebDeleted(ImagesDeletedMessage) returns (common.StatusCodeResponse);
    rpc updateReportStatusAfterEntryRecordCheck(ReportStatusRecordChange) returns (common.StatusCodeResponse);

    rpc updateProjectTagsOnWebChanged(ProjectTagChangedRequest) returns (common.StatusCodeResponse);
    rpc updateProjectInspectionTimeOnWebChanged(ProjectInspectionTime) returns (common.StatusCodeResponse);
}

message ProjectStatusChangeRequest {
    int64 projectId = 1;
    int32 statusCode = 2;
    string type = 3;
    string createdBy = 4;
    int64 createdAt = 5;
}

message ProjectMessage {
    int64 id = 1;
    int64 project_id = 2;
    string title = 3;
    string content = 4;
    int32 type = 5;
    int64 create_time = 6;
    string extra = 7;
}

message ImagesDeletedMessage {
    int64 project_id = 1;
    repeated string image_ids = 2;
    int32 delete_status = 3;
}

message ReportStatusRecordChange {
    int64 project_id = 1;
    string type = 2;
    int32 status = 3;
}

message ProjectTagChangedRequest{
    repeated ProjectTag projectTags = 1;
}

message ProjectTag{
    int64 projectId = 1;
    repeated int64 labelId = 2;
}

message ProjectInspectionTime{
    int64 project_id = 1;
    int64 inspection_time = 2;
}
