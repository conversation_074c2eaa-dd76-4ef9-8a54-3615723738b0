syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.web2ai";
package api.web2ai;

import "common/StatusCodeResponse.proto";

message ProjectClaimTagRequest {
    repeated int64 projectId = 1;
}

service ProjectClaimTagService {
    rpc getTags(ProjectClaimTagRequest) returns (ProjectClaimTagResponse);
}

message ProjectClaimTagResponse {
    common.StatusCodeResponse statusCodeResponse = 1;
    repeated ProjectClaimTag tag = 2 ;
}

message ProjectClaimTag {
    int64 projectId = 1;
    repeated string tags = 2;
}
