syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.web2ai";

package api.web2ai;

import "common/StatusCodeResponse.proto";
import "web2ai/ProjectImage.proto";
import "web2ai/ProjectId.proto";
import "web2ai/Project.proto";

service ReportGenerateService {
    // send request when user's images upload finished
    rpc generateReport(ReportGenerateServiceRequest) returns (common.StatusCodeResponse);

    // delete all project datas in redis after project is return to client
    rpc changeStatusToReturnToClient(ProjectId) returns (common.StatusCodeResponse);

    // receive questionnaire report data
    rpc receiveQuestionnaireData(QuestionnaireDataRequest) returns (common.StatusCodeResponse);
}

message ReportGenerateServiceRequest {
    Project project = 1;
    ProjectEsModel esProject = 2;
    bool imageSyncFlag = 3;
    string syncPoint = 4;
}

message User {
    int64 userId = 1;
    string name = 2;
    string firstName = 3;
    string lastName = 4;
    string email = 5;
    string phone = 6;
    int64 roles = 7;
    int32 activeStatus = 8;
    Company company = 9;
}

message Project {
    int64 projectId = 1;
    string policyNumber = 2;
    string claimNumber = 3;
    double claimRcv = 4;
    double claimAcv = 5;
    int32 claimType = 6;

    int64 createdTime = 7;
    int64 damageEventTime = 8;
    User createdBy = 9;

    int32 projectType = 10;
    string address = 11;
    string city = 12;
    string state = 13;
    string country = 14;
    string zipCode = 15;

    double gpsLocationLongitude = 16;
    double gpsLocationLatitude = 17;

    string assetOwnerName = 18;
    string assetOwnerPhone = 19;
    string assetOwnerEmail = 20;

    Company insuranceCompany = 21;
    Company repairCompany = 22;
    Company materialProviderCompany = 23;
    Company companyId = 24;
    string description = 25;
    string claimNote = 26;
    string north = 27;
    int32 latestStatus = 28;

    int64 inspectionTypes = 29;
    int64 inspectionTime = 30;
    int32 damageSeverity = 31;

    bool isBooking = 32;
    string contacterName = 33;
    string contacterEmail = 34;
    string contacterPhone = 35;
    int32 roofEstimatedAreaItem = 36;
    int32 reportServiceOption = 37;
    repeated int32 reportTypes = 38;
    repeated ReportTemplate reportTemplates = 39;
    bool needPilot = 40;
    int32 chimney = 41;

    repeated ProjectImage images = 42;
    repeated Member members = 43;
    //repeated EventHistory eventHistories = 44;

    int32 imagesArchiveFileChunkSize = 45;

    int32 serviceType = 46;

    string inspectionNumber = 47;

    // 增加字段 adjustedBy
    string adjustedBy = 48;

    //NewProjectStatusEnum#getCode() 90:"returned_to_client"
    int32 projectStatus = 49;
    int64 siteInspectedTime = 50;

    int64 customerContactedTime = 51;

    string inspectedBy = 52;

    string catNumber = 53;
}

message ProjectReportFile {
    string reportId = 1;
    int64 projectId = 2;
    int32 reportType = 3;
    string reportWordFileName = 4;
    string reportPdfFileName = 5;
    int32 size = 6;
    int64 createdBy = 7;
    int64 createdTime = 8;
    bool isRead = 9;
    bool isDeleted = 10;
    int32 generationStatus = 11;
}

message Company {
    int64 companyId = 1;
    string companyName = 2;
    int32 companyType = 3;
    string phone = 4;
    string email = 5;
    string contactName = 6;
    string website = 7;
    string logo = 8;
    bool isDeleted = 9;
    int64 createdTime = 10;
    int64 updatedTime = 11;
}

message Member {
    int64 projectId = 1;
    int64 userId = 2;
    int32 role = 3;
    int64 createdTime = 4;
    int64 createdBy = 5;
    string description = 6;
    bool deleted = 7;
    //default value is -1
    int32 creater = 8;
}

message ReportTemplate {
    int64 id = 1;
    int64 companyId = 2;
    int64 materialId = 3;
    string reportTypes = 4;
    int32 pageNumber = 5;
    int32 pageType = 6;
    int32 sort = 7;
    int32 materialType = 8;
    double pointX = 9;
    double pointY = 10;
    string dataSource = 11;
    string checkIsImplement = 12;
    bool isShowDamageType = 13;
    int32 size = 14;
    bool isBold = 15;
    string color = 16;
    int32 align = 17;
    string materialUrl = 18;
    int32 width = 19;
    int32 height = 20;
    int32 materialNum = 21;
}

message QuizAnswer {
    int64 quizId = 1;
    string answer = 2;
}

message Signature {
    string url = 1;
    int32 type = 2;
    int64 uploadTime = 3;
}

message QuestionnaireDataRequest {
    int64 projectId = 1;
    repeated QuizAnswer quizAnswers = 2;
    repeated Signature signatures = 3;
    string unsignedReason = 4;
}
