syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.web2ai";
package api.web2ai;

service FileStreamingService {
    rpc pull (stream CompressedFileChunk) returns (stream UploadResponse);

    rpc transfer (FileStream) returns (FileStreamResponse);
}

message CompressedFileChunk {
    int64 projectId = 1;
    int32 chunkIndex = 2;
    Chunk chunk = 3;
}

message Chunk {
    bytes content = 1;
}

message FileStream {
    string key = 1;
    int32 chunkOffset = 2;
    int32 chunkLength = 3;
    int32 chunkNumber = 4;
    bytes content = 5;
}

message FileStreamResponse {
    string key = 1;
    int32 chunkOffset = 3;
    int32 chunkNumber = 2;
    UploadStatusCode code = 4;
    string message = 5;
}

message UploadResponse {
    int64 projectId = 1;
    int32 chunkIndex = 2;
    UploadStatusCode code = 3;
    string message = 4;
}

enum UploadStatusCode {
    UNKNOWN = 0;
    OK = 1;
    FAILED = 2;
}
