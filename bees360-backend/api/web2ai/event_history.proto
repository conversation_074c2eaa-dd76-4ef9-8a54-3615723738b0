syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.web2ai";
option java_outer_classname = "EventHistoryProto";

package api.web2ai;

message EventHistoryList {
    repeated EventHistory eventHistories = 1;
}

message EventHistory {
    int64 eventId = 1;
    int64 projectId = 2;
    int64 userId = 3;
    int32 status = 4;

    int64 statusTime = 5;
    string description = 6;
    int64 createdTime = 7;
    int64 modifiedBy = 8;

    string userName = 9;
    string statusName = 10;
}
