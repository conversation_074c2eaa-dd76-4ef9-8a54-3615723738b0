syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.web2ai";
package api.web2ai;

import "common/StatusCodeResponse.proto";
import "web2ai/ProjectImage.proto";
import "web2ai/ProjectId.proto";

service ProjectImageService {
    // add image to bees360-ai'cache (including copy) one by one
    rpc saveProjectImages(ProjectImagesRequest) returns (common.StatusCodeResponse);

    // delete images from bees360-ai'cache
    rpc saveProjectImageStatuses(ProjectImageStatusesRequest) returns (common.StatusCodeResponse);

    // update image's imageType to closeup or other
    rpc saveProjectImageTypes(ProjectImageTypesRequest) returns (common.StatusCodeResponse);

    // update images orientation
    rpc saveProjectImagesOrientation(ProjectImageOrientationRequest) returns (common.StatusCodeResponse);

    // get ProjectImages by projectId
    rpc getProjectImages(ProjectId) returns (ProjectImagesResponse);
}

message ProjectImageTypesRequest {
    int64 projectId = 1;
    int32 fileSourceType = 2;
    repeated string imageId = 3;
    int32 imageType = 4;
}

message ProjectImagesRequest {
    int64 projectId = 1;
    repeated ProjectImage projectImages = 3;
}

message ProjectImageStatusesRequest {
    int64 projectId = 1;
    map<string, int32> imageStatuses = 2;
}

message ProjectImageIdsRequest {
    int64 projectId = 1;
    repeated string imageId = 2;
}

message ImageKeyRequest {
    string imageKey = 1;
}

message ProjectImageContentResponse {
    common.StatusCodeResponse statusCodeResponse = 1;
    ProjectImageContent projectImageContent = 2;
}

message ImageIdRequest {
    string imageId = 1;
}

message ProjectImageResponse {
    common.StatusCodeResponse statusCodeResponse = 1;
    ProjectImage projectImage = 2 ;
}

message ProjectImagesResponse {
    common.StatusCodeResponse statusCodeResponse = 1;
    repeated ProjectImage projectImages = 2 ;
}

message ProjectImageContent {
    string imageId = 1;
    bytes content = 2;
}

message ProjectImageOrientationRequest {
    int64 projectId = 1;
    map<string, int32> imageOrientation = 2;
}
