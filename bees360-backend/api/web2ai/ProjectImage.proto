syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.web2ai";

package api.web2ai;

message ProjectImage {
    string imageId = 1;
    int64 projectId = 2;
    string fileName = 3;
    string fileNameMiddleResolution = 4;
    string fileNameLowerResolution = 5;
    int64 fileSize = 6;
    int64 userId = 7;
    int64 uploadTime = 8;
    string originalFileName = 9;
    int32 fileSourceType = 10;
    double gpsLocationLongitude = 11;
    double gpsLocationLatitude = 12;
    double relativeAltitude = 13;

    int32 imageHeight = 14;
    int32 imageWidth = 15;
    int32 direction = 16;
    int32 orientation = 17;
    int32 imageType = 18;
    string imageCategory = 19;
    string camPropertyMatrix = 20;
    bool deleted = 21;
    bool manuallyAnnotated = 22;
    string parentId = 23;
    int32 partialType = 24;
    int64 shootingTime = 25;
    int64 imageSort = 26;
    int32 tiffOrientation = 27;
    int64 updateTime = 28;
    string roomName = 29;
    string floorLevel = 30;
    string number = 31;

//    bytes content = 25;
//    bytes middleResolutionContent = 26;
//    bytes lowerResolutionContent = 27;
}
