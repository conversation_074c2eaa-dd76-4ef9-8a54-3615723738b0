syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.web2ai";

package api.web2ai;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

import "ai2client/DashBoardProto.proto";
import "ai2client/CommonProto.proto";

message ProjectDashBoardVo {
    string roleTag = 1;
    repeated ProjectListResult result = 2;
}

message ProjectListResult {
    string statusTag = 1;
    repeated ProjectEsModel projectList = 2;
    int32 projectCount = 3;
}

message ProjectEsModel {
    int64 projectId = 1;

    string policyNumber = 2;

    string claimNumber = 3;

    int32 claimType = 4;

    int64 createdTime = 5;

    int64 createdBy = 6;

    int32 projectType = 7;

    string address = 8;

    string city = 9;

    string state = 10;

    string country = 11;

    string zipCode = 12;

    string assetOwnerName = 13;

    string assetOwnerPhone = 14;

    string assetOwnerEmail = 15;

    string inspectionNumber = 16;

    int64 dueDate = 17;

    string inspectionType = 18;

    string description = 19;

    string claimNote = 20;
    string north = 21;
    /**
     * related to com.bees360.entity.enums.ProcessStatus
     */
    int32 latestStatus = 22;

    int32 projectStatus = 23;

    int64 statusUpdateTime = 24;

    int32 imageUploadStatus = 25;

    int64 inspectionTypes = 26;

    int64 inspectionTime = 27;

    int32 serviceType = 28;

    string creatorName = 29;

    string statusName = 30;
    string agent = 31;
    string agentContactName = 32;
    string agentPhone = 33;
    string agentEmail = 34;

    int32 flyZoneType = 35;

    string insuranceCompanyName = 36;
    string repairCompanyName = 37;

    string customer = 38;
    string guideline = 39;
    string insuredHomePhone = 40;
    string insuredWorkPhone = 41;
    double gpsLocationLongitude = 42;
    double gpsLocationLatitude = 43;
    bool isBooking = 44;
    bool needPilot = 45;
    int32 chimney = 46;
    int32 roofEstimatedAreaItem = 47;
    int32 reportServiceOption = 48;
    int64 damageEventTime = 49;

    string specialInstructions = 50;
    string specialInstructionComments = 51;
    string policyEffectiveDate = 52;
    string yearBuilt = 53;

    int32 payStatus = 54;

    string companyName = 55;

    string companyLogo = 56;
    string insuranceCompanyNameLogo = 57;
    string repairCompanyNameLogo = 58;

    repeated int32 reportTypes = 59;

    repeated api.ai2client.MemberInfo members = 60;

    repeated ProjectStatusVo timeLines = 61;

    repeated ProjectQuizMessage projectQuiz = 62;

    int64 daysOld = 63;

    int64 siteInspectedTime = 64;

    int32 droneImageCount = 65;

    int32 mobileImageCount = 66;

    int64 discussCount = 67;

    double rotationDegree = 68;

    int64 customerContactedTime = 69;
    string inspectedBy = 70;
    int64 insuranceCompany = 71;
    int64 repairCompany = 72;

    string adjuster = 73;
    string adjusterPhone = 74;

    repeated string tags = 75;

    int32 pirReportStatus = 76;

    int32 darReportStatus = 77;

    int32 hoverMarkStatus = 79;
    int32 plnarMarkStatus = 80;

    string pilotName = 81;
    string adjusterEmail = 82;

    repeated int64 projectTags = 83;

    string catNumber = 84;

    repeated ExternalMember externalMember = 85;

    string batchNo = 86;

    string folderType = 87;

    BeesPilotBatch batchInfo = 88;
    repeated string pilotFeedbacks = 89;

    float imageScore = 90;

    repeated ProjectTagMessage project_tag_list = 91;

    string address_id = 92;

    string operating_company = 93;

    string project_state = 94;

    string project_state_change_reason = 95;

    string time_zone = 96;

    AirspaceMessage airspace = 97;

    google.protobuf.BoolValue is_parent_project = 98;

    google.protobuf.BoolValue is_child_project = 99;
}

message AirspaceMessage {
    // allow to be empty string
    string status = 1;
    google.protobuf.Int32Value height_ceiling = 2;
}

message ProjectTagMessage {

    string id = 1;

    string title = 2;

    string description = 3;

    string color = 4;

    string icon = 5;

    string company_id = 6;

    ProjectTagType type = 7;

    enum ProjectTagType {

        UNKNOWN = 0;

        CLAIM = 1;

        UNDERWRITING = 2;

    }

}



message ProjectStatusVo {
    CodeNameDto status = 1;
    int64 createdTime = 2;
    string userName = 3;
}

message CodeNameDto {
    int32 code = 1;
    string name = 2;
}

message HistoryLogVo {
    int64 projectId = 1;
    int64 userId = 2;
    int32 status = 3;
	string modifiedBy = 4;
    string user = 5;
	int64 statusTime = 6;
    int64 createdTime = 7;
    string statusName = 8;
}


message ProjectQuizMessage {
    int64 quizId = 1;

    int64 projectId = 2;

    string subject = 3;

    int32 claimType = 4;

    int32 type = 5;

    repeated string choices = 6;

    int32 sequence = 7;

    repeated string answers = 8;

    string answer = 9;
}

message DashBoardProjectPage {
    api.ai2client.Pagination page = 1;
    repeated ProjectEsModel items = 2;
}


message ExternalMember {
    string name = 1;
    string auth = 2;
    string company = 3;
    string email = 4;
    string phone = 5;
}

message BeesPilotBatch {
    string batchNo = 1;
    double basePay = 2;
    double extraPay = 3;
    int64 paymentDate = 4;
}
