API
===

###### `guanrong.yang` `2019/07/22`

存放系统之间交互的`protobuf`文件。

基本原则:

1. 如果一个服务是简单的且自封闭的（如3D建模服务），请定义服务端接口和客户端回调接口
    * 服务端接口定义：客户端向该服务发送请求的接口；
    * 客户端回调接口：服务器端异步处理完成之后，会执行的请求接口。此时客户端需要实现回调接口的服务器端；
1. 一个服务仅仅需要定义自己对外的接口
    * 服务器端应该尽量少感知客户端的类型，尽量关注自身完成的服务
1. 一个服务不应该去使用其他服务定义模型或者接口来定义自身接口，从而减少耦合

## 通用接口

通过接口是提供给所有服务器使用的通用定义，一旦定义，就不应做破坏性的修改。

- common: 通用定义

## ad对外接口

- ai2ad

## 3d端对外

- aito3d

## ai端对外

对外服务：

- ai2client: ai 对客户端提供的restful接口定义

内部服务：

- web2ai: ai对web服务器开放的忌口

## web端对外

- report2web
