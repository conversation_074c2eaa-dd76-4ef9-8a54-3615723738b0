syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc";
package api.ai;

import "google/type/money.proto";

import "web2ai/Project.proto";
import "web2ai/ProjectId.proto";

message UpdateProjectBatch {
    repeated UpdateProjectBatchItem item = 1;
}

message UpdateProjectBatchItem {
    string claim_number = 1;
    string external_adjuster_name = 2;
    string external_adjuster_email = 3;
    string external_adjuster_phone = 4;
    google.type.Money estimate_total_pay = 12;
}

service ProjectService {
    rpc findById(web2ai.ProjectId) returns (web2ai.ProjectEsModel);
}
