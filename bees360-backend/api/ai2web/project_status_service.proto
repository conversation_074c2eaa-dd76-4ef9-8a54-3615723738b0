syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.ai2web";
option java_outer_classname = "ProjectsStatusProto";

import "common/StatusCodeResponse.proto";

package api.ai2web;

service ProjectsStatusService {

    rpc batchUpdateProjectStatus(ProjectsStatusChangedRequest) returns (common.StatusCodeResponse);

    rpc reworkProject(ProjectsReworkRequest) returns (common.StatusCodeResponse);

}

message ProjectsStatusChangedRequest {
    repeated int64 projectId = 1;
    int32 status = 2;
    string system_type = 3;
}

message ProjectsReworkRequest {
    int64 projectId = 1;
    string title = 2;
    string content = 3;
}
