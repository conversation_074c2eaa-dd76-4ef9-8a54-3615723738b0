syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.ai2web";
option java_outer_classname = "ProjectTagProto";

import "common/StatusCodeResponse.proto";

package api.ai2web;

service ProjectTagService {

    rpc updateProjectTags(ProjectTagUpdateRequest) returns (common.StatusCodeResponse);

}

message ProjectTagUpdateRequest {
    int64 projectId = 1;
    repeated int64 tagId = 2;
    string userId = 3;
}
