syntax = "proto3";

option java_package = "com.bees360.internal.ai.grpc.api.ai2web";
option java_outer_classname = "ImageDeleteProto";

import "common/StatusCodeResponse.proto";

package api.ai2web;

service ImageDeleteService {

    rpc deleteImages(ImageDeleted) returns (common.StatusCodeResponse);

}

message ImageDeleted {
    int64 projectId = 1;
    repeated string image_id = 2;
    int32 delete_status = 3;
}
