syntax = "proto3";

package api.report2web;

option java_multiple_files = true;
option java_outer_classname = "ImageProto";
option java_package = "com.bees360.report.grpc.api.report2web";

import "report2web/ImageAnnotationProto.proto";
import "google/protobuf/wrappers.proto";


message AnnotatedImageRequest {
    int32 tag_code = 1;
    int32 facet_id = 2;
    PointMessage lt_point = 3;
    PointMessage rb_point = 4;
    string remark = 5;
    double confidenceLevel = 6;
    string attribute = 7;
    google.protobuf.Int32Value sourceType = 8;
}

message AnnotatedImageBatchCreateRequest {
    repeated AnnotatedImageRequest request = 1;
}

message ImageAnnotationRemark {
    string remark = 1;
}

message AnnotatedImageVo {
    string image_id = 1;
    string annotated_image_id = 2;
    string annotated_image_key = 3;
    repeated ImageAnnotation image_annotations = 4;
}

message ImageCreatedVo {
    string image_id = 1;
    AnnotatedImageVo annotated = 2;
    AnnotatedImageVo test_square = 3;
}

message TestSquareParam {
    PointMessage points = 1;
    bool is_async_refer = 2;
}
message ImageEffectedVo {
    ImageCreatedVo origin = 1;
    repeated ImageCreatedVo effected = 2;
}

message ImagesEffectedVo {
    repeated ImageCreatedVo origin = 1;
    repeated ImageCreatedVo effected = 2;
}

message AnnotatedImageBatchCreateResponse {
    repeated ImageEffectedVo imageEffectedVos = 1;
    repeated AnnotatedImageRequest failedRequest = 2;
    string imageId = 3;
}

message TestSquareCreatedVo {
    TestRectangle test_rectangle = 1;
    ImageEffectedVo effected = 2;
}
message TestRectangle {
    string annotation_id = 1;
    int32 facet_id = 2;
    PointVo points = 3;
    int32 orientation_tag = 4;
    repeated DamageCount damage_count = 5;
    string remark = 6;
}

message PointVo {
    PointMessage p1 = 1;
    PointMessage p2 = 2;
    PointMessage p3 = 3;
    PointMessage p4 = 4;
}

message DamageCount {
    int32 tag = 1;
    int32 count = 2;
}

message ImageAnnotation {
    string annotationId = 1;

    string imageId = 2;

    int32 facetId = 3;

    int64 projectId = 4;

    string annotationPolygon = 5;

    double centerPointX = 6;

    double centerPointY = 7;

    int32 annotationType = 8;

    int32 usageType = 9;

    int64 generatedBy = 10;

    int32 sourceType = 11;

    int64 createdTime = 12;

    string categoryTagName = 13;

    string tagName = 14;

    string remark = 15;

    string annotationKey = 16;

    int32 report_sort = 17;

    string origin_annotation_id = 18;

    AttributeMessage attribute = 19;
}

message AttributeMessage {
    google.protobuf.Int32Value index = 1;
    string source = 2;
    google.protobuf.StringValue roof_mapping_info = 3;
}

message AnnotationReportSortParam {
    repeated AnnotationReportSort sortParam = 1;
}

message AnnotationReportSort {
    string annotation_id = 1;
    int32 sort = 2;
}

message MappingCreateParam {
    repeated string image_ids = 1;
    repeated MappingImage mapping_images = 2;
}

message MappingImage {
    string image_id = 1;
    string mapping_image_id = 2;
}

message MappingDetail {
   int32 facetId = 1;
   double center = 2;
   repeated PointMessage path = 3;
}

message MappingInfo {
    repeated MappingDetail mapping = 1;
}

message MappingCreateVo {
    MappingInfo mapping_info = 1;
    AnnotatedImageVo created = 2;
    MappingImage mapping_image = 3;
}

message MappingBatchCreateVo {
    repeated MappingCreateVo batchCreatedVo = 1;
}
