syntax = "proto3";

option java_package = "com.bees360.report.grpc.api.report2web";

package api.report2web;

message ReportImageMessage {
    string imageId = 1;
    int64 projectId = 2;
    string annotationImage = 3;
    int32 direction = 4;
    int32 orientation = 5;
    int32 partialType = 6;
    int32 imageType = 7;
    string camPropertyMatrix = 8;
    int64 imageSort = 9;

    string fileName = 10;
    string fileNameMiddleResolution = 11;
    string fileNameLowerResolution = 12;
    int64 uploadTime = 13;
    int64 fileSize = 14;
    string originalFileName = 15;
    int32 fileSourceType = 16;
    double gpsLocationLongitude = 17;
    double gpsLocationLatitude = 18;
    double relativeAltitude = 19;
    int32 imageHeight = 20;
    int32 imageWidth = 21;

    bool manuallyAnnotated = 22;
    string parentId = 23;
    int64 shootingTime = 24;
    int32 tiffOrientation = 26;

    //图片是否参与3d建模: default value 0: 该图片不需要参与3d建模; 1:参与3d建模; -1: 没有参与建模(被建模算法剔除)
    int32 in3DModel = 27;
    int64 updateTime = 28;

    string tagCategory = 29;
    string tagDescription = 30;

}
