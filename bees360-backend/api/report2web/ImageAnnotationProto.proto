syntax = "proto3";

package api.report2web;

option java_multiple_files = true;
option java_outer_classname = "ImageAnnotationProto";
option java_package = "com.bees360.report.grpc.api.report2web";


message ImageAnnotationTagRequest {
    int64 annotationId = 1;
    int32 type = 2;
    int32 usage_type = 3;
    int32 facet_id = 4;
    PointMessage lt_point = 5;
    PointMessage rb_point = 6;
    string remark = 7;
    bool force_update = 8;
    string attribute = 9;
}

message ImageAnnotationTagCreateRequest {
    repeated string image_ids = 1;
    ImageAnnotationTagRequest image_tag = 2;
    int64 project_id = 3;
}

message AnnotationIds {
    repeated string annotation_ids = 1;
    bool only_delete_self = 2;
    string image_id = 3;
}

message ImageAnnotationBatchDeleteRequest {
    repeated string annotation_ids = 1;
    bool only_delete_self = 2;
}

message ImageAnnotationBatchChangeRequest {
    string imageId = 1;
    repeated ImageAnnotationTagRequest image_tag = 2;
    int64 project_id = 3;
}

message PointMessage {
    double x = 1;
    double y = 2;
}

message ImageAnnotationList {
    repeated ImageAnnotationObj annotations = 1;
}

message ImageAnnotationObj {
    string annotationId = 1;

    string imageId = 2;

    int32 facetId = 3;

    int64 projectId = 4;

    string annotationPolygon = 5;

    double centerPointX = 6;

    double centerPointY = 7;

    int32 annotationType = 8;

    int32 usageType = 9;

    int64 generatedBy = 10;

    int32 sourceType = 11;

    int64 createdTime = 12;

    string categoryTagName = 13;

    string tagName = 14;

    string remark = 15;

    string attribute = 16;
}

message ImageAnnotationAggregateResult {
    repeated ImageAnnotationAggregateVo annotation_data = 1;
}

message ImageAnnotationAggregateVo {
    int32 tag = 1;
    string tag_name = 2;
    int32 tag_count = 3;
    repeated ImageAnnotationObj annotations = 4;
}

message ImageTagIdsRequest {
    repeated string image_ids = 1;
}

message ImageAnnotationVo {
    string image_id = 1;
    repeated ImageAnnotationObj annotations = 2;
}

message ImageAnnotationVoRes {
    repeated ImageAnnotationVo annotation_data = 1;

}
