syntax = "proto3";

option java_package = "com.bees360.report.grpc.api.report2web";

package api.report2web;

import "common/StatusCodeResponse.proto";
import "report2web/ProjectReportFile.proto";
import "report2web/ReportProjectMessage.proto";
import "report2web/Annotation2D.proto";
import "report2web/Annotation3D.proto";
import "report2web/Facet.proto";
import "report2web/ImageFacet.proto";
import "report2web/ReportImageMessage.proto";
import "report2web/ReportElement.proto";
import "report2web/CustomizedReportElement.proto";
import "report2web/CustomizedReportItem.proto";
import "report2web/project_entity.proto";
import "report2web/report_entity.proto";
import "report2web/ReportAnnotationImage.proto";
import "report2web/ImageDeleted.proto";

service UpdateReportService {
    rpc updateReport(UpdateReportRequest) returns (common.StatusCodeResponse);
}

message UpdateReportRequest {
    // 2D and 3D
    ReportProjectMessage reportProjectMessage = 1;
    repeated ProjectReportFile projectReportFiles = 2;
    repeated ReportElement reportElements = 3;
    repeated ReportImageMessage reportImageMessages = 4;
    repeated Annotation2D annotation2Ds = 5;
    repeated Annotation3D annotation3Ds = 6;

    // 3D
    repeated Facet facets = 7;
    repeated ImageFacet imageFacets = 8;

    // 2D
    repeated CustomizedReportElement customizedReportElements = 9;
    repeated CustomizedReportItem customizedReportItems = 10;

    ExportData exportData = 11;

    repeated ReportAnnotationImage reportAnnotationImages = 12;

    repeated ReportSummaryProto reportSummary = 13;

    repeated ImageDeleted imageDeleted = 14;
}
