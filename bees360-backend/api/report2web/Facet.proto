syntax = "proto3";

option java_package = "com.bees360.report.grpc.api.report2web";

package api.report2web;

message Facet {
    int32 facetId = 1;
    int64 projectId = 2;
    int32 componentId = 3;
    string name = 4;
    int64 createdBy = 5;
    int64 createdTime = 6;
    int32 orientation = 7;
    int32 damagePercent = 8;
    double area = 9;
    string areaUnit = 10;
    double pitch = 11;
    string thdPath = 12;
    string pathUnit = 13;
    string pathType = 14;
    string sharedFacet = 15;
    string planeProp = 16;
    string planeCoef = 17;
    bool isHighRoof = 18;
}
