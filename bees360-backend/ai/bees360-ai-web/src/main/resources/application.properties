spring.profiles.active=${ENV:local}
# server
server.port=8080
server.servlet.context-path=/api

########## mysql #########
spring.datasource.driver-class-name=org.mariadb.jdbc.Driver
spring.datasource.password=mysql_password
spring.datasource.url=************************************************************************************************************************************************************************
spring.datasource.username=mysql_username

spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=30
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=XxxHikariCP
spring.datasource.hikari.max-lifetime=2000000
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.connection-test-query=SELECT 'x' FROM DUAL
spring.datasource.hikari.register-mbeans=true

# éç¾¤ç¨,åå²
bees360.ai.elasticsearch.ipAddr=elasticsearch:9200

########## redis #########
spring.cache.type=redis
spring.cache.redis.cache-null-values=false
spring.cache.redis.time-to-live=600000
spring.cache.redis.use-key-prefix=true

spring.redis.host=redis
spring.redis.port=6379
spring.redis.database=1
spring.redis.timeout=300000
spring.redis.lettuce.pool.max-active=100
spring.redis.lettuce.pool.max-idle=100
spring.redis.lettuce.pool.min-idle=10
spring.redis.lettuce.pool.max-wait=-1ms
spring.redis.lettuce.shutdown-timeout=200ms

# jasypt for spring config file encryption
jasypt.encryptor.password=${BEES360_SECRET_KEY}

# åç 404 éè¯¯æ¶, ç´æ¥æåºå¼å¸¸
spring.mvc.throw-exception-if-no-handler-found=true
# æ¯å¦å¼å¯é»è®¤çèµæºå¤çï¼é»è®¤ä¸ºtrue
spring.web.resources.add-mappings=false

spring.servlet.multipart.max-file-size=30MB
spring.servlet.multipart.max-request-size=100MB

########## mail #########
mail.send.enabled=false
mail.senders.statistics-mail-sender=<EMAIL>

# projectæªå®æé®ä»¶æ¥è¯¢å¤©æ°
scheduled.projectMissionWarn.durationDays=7


# mybatis
mybatis.config-location=classpath:config/mybatis-config.xml
mybatis.mapper-locations=classpath*:mappings/*.xml,classpath*:mappings/**/*.xml

# è·¨å
# origin çåè¡¨
cors.access.control.allowed.originWhites=
# origin å¹éçæ­£åè¡¨è¾¾å¼åè¡¨ï¼ä»¥åè§éå·åå²ãå¦éä¿®æ¹åå²ç¬¦å·ï¼è¯·ä¿®æ¹ com.bees360.internal.ai.config.filter.CustomCorsFilter
cors.access.control.allowed.originRegexes=
cors.access.control.allowed.methods=GET,POST,HEAD,OPTIONS,PUT,DELETE,PATCH
cors.access.control.allowed.headers=Authorization,Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers,Content-MD5
cors.access.control.allowed.credentials=true
cors.access.control.exposed.headers=Authorization,Access-Control-Allow-Origin,Access-Control-Allow-Credentials,X-Protobuf-Message
cors.access.control.maxAge=86400

#aiç³»ç»æ¯å¦å°å¾çä¿æå°redis
ai.bees360.io.system.constant.saveImagesToRedis=true

spring.task.execution.pool.core-size=11

bees360.web.http.request.socketTimeout=90000
bees360.web.http.request.connectTimeout=6000
bees360.web.http.request.connectionRequestTimeout=3000
bees360.web.http.request.retryTimes=20
bees360.web.http.request.retryInterval=60000

bees360.datahub.module.name=ai

#log config
logging.config=classpath:logger/log4j2.yml

bees360.user.client.accesstokenexpiry=600000

# 3dæå¡å¨æ¯å¦ç©ºé²æ£æ¥æ¬¡æ°åé´é,æé¿ç­å¾æ¶é´è®¾ç½®ä¸º2å°æ¶(120/5*æå¡å¨æ°é(5)=120æ¬¡)
bees360.ai.server.3d.status.check.interval=300000
bees360.ai.server.3d.status.check.retryCount=120

# http start
http.client.connect-timeout=60000
http.client.socket-timeout=300000
http.client.connection-request-timeout=300000
http.client.max-total=200
http.client.max-per-route=20
# èµæºæå¡å¨http-clientéç½®
http.client.maxConnPerRoute=16
http.client.maxConnTotal=64
http.client.evictIdleConnectionsAfter=PT10M
http.client.connectionTimeToLive=PT10M
http.client.evictExpiredConnections=true
http.client.redirectStrategy=always
http.client.request.connectTimeout=PT15S
http.client.request.connectionRequestTimeout=PT30S
http.client.request.socketTimeout=PT15S
http.client.request.redirectEnabled=true
http.client.request.expectContinueEnabled=true
# http end

# the expire(seconds) of image file in redis
bees360.ai.cache.expiration.project-data-expire=1296000

image.resource.wait.time=20
image.resource.executor.threadNum=16
image.tag.exclude-number-path=wall

spring.thymeleaf.prefix=classpath:message/thymeleaf/
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8

## ai-backend grpc server port
com.bees360.grpc.server.ai.port=9898
com.bees360.grpc.server.ai.handshakeTimeout=60
com.bees360.grpc.server.threed.endpoints=bees360threed-grpc:9898
com.bees360.grpc.server.threed.maxRetryAttempts=3
com.bees360.grpc.server.threed.keepAliveTime=480
com.bees360.grpc.server.threed.keepAliveTimeout=60
com.bees360.grpc.server.threed.initialBackoff=0.5
com.bees360.grpc.server.threed.maxBackoff=1
com.bees360.grpc.server.threed.backoffMultiplier=2

com.bees360.grpc.server.web.endpoints=bees360web-grpc:9898
com.bees360.grpc.server.web.maxRetryAttempts=3
com.bees360.grpc.server.web.keepAliveTime=480
com.bees360.grpc.server.web.keepAliveTimeout=60
com.bees360.grpc.server.web.initialBackoff=0.5
com.bees360.grpc.server.web.maxBackoff=1
com.bees360.grpc.server.web.backoffMultiplier=2

grpc.server.port=9899

# èµæºæå¡å¨ GrpcResourceClientéç½®
grpc.client.resourcePool.address=static://bees360-resource-app-grpc:9898
grpc.client.resourcePool.enableKeepAlive=true
grpc.client.resourcePool.keepAliveWithoutCalls=true
grpc.client.resourcePool.negotiationType=plaintext

# Activity Grpc Clientéç½®
grpc.client.activityManager.address=static://bees360-activity-app-grpc:9898
grpc.client.activityManager.enableKeepAlive=true
grpc.client.activityManager.keepAliveWithoutCalls=true
grpc.client.activityManager.negotiationType=plaintext
grpc.client.commentManager.address=static://bees360-activity-app-grpc:9898
grpc.client.commentManager.enableKeepAlive=true
grpc.client.commentManager.keepAliveWithoutCalls=true
grpc.client.commentManager.negotiationType=plaintext

grpc.client.userProvider.address=static://bees360web-grpc:9899
grpc.client.userProvider.enableKeepAlive=true
grpc.client.userProvider.keepAliveWithoutCalls=true
grpc.client.userProvider.negotiationType=plaintext

# project tag
grpc.client.projectTagManager.address=static://bees360-project-app-grpc:9898
grpc.client.projectTagManager.enableKeepAlive=true
grpc.client.projectTagManager.keepAliveWithoutCalls=true
grpc.client.projectTagManager.negotiationType=plaintext

# project pipeline
grpc.client.pipelineService.address=static://bees360-project-app-grpc:9898
grpc.client.pipelineService.enableKeepAlive=true
grpc.client.pipelineService.keepAliveWithoutCalls=true
grpc.client.pipelineService.negotiationType=plaintext

# project airspace manager
grpc.client.projectAirspaceManager.address=static://bees360-project-app-grpc:9898
grpc.client.projectAirspaceManager.enableKeepAlive=true
grpc.client.projectAirspaceManager.keepAliveWithoutCalls=true
grpc.client.projectAirspaceManager.negotiationType=plaintext

# hive location
grpc.client.hiveLocationManager.address=static://bees360-address-app-grpc:9898
grpc.client.hiveLocationManager.enableKeepAlive=true
grpc.client.hiveLocationManager.keepAliveWithoutCalls=true
grpc.client.hiveLocationManager.negotiationType=plaintext

# address
grpc.client.addressManager.address=static://bees360-address-app-grpc:9898
grpc.client.addressManager.enableKeepAlive=true
grpc.client.addressManager.keepAliveWithoutCalls=true
grpc.client.addressManager.negotiationType=plaintext

# group manager
grpc.client.groupManager.address=static://bees360-user-app-grpc:9898
grpc.client.groupManager.enableKeepAlive=true
grpc.client.groupManager.keepAliveWithoutCalls=true
grpc.client.groupManager.negotiationType=plaintext

# image tag manager
grpc.client.imageTagManager.address=static://bees360-image-app-grpc:9898
grpc.client.imageTagManager.enableKeepAlive=true
grpc.client.imageTagManager.keepAliveWithoutCalls=true
grpc.client.imageTagManager.negotiationType=plaintext

# projectCatastrophe manager
grpc.client.projectCatastropheManager.address=static://bees360-project-app-grpc:9898
grpc.client.projectCatastropheManager.enableKeepAlive=true
grpc.client.projectCatastropheManager.keepAliveWithoutCalls=true
grpc.client.projectCatastropheManager.negotiationType=plaintext

# projectIIManager
grpc.client.projectIIManager.address=static://bees360-project-app-grpc:9898
grpc.client.projectIIManager.enableKeepAlive=true
grpc.client.projectIIManager.keepAliveWithoutCalls=true
grpc.client.projectIIManager.negotiationType=plaintext

# image manager
grpc.client.imageManager.address=static://bees360-image-app-grpc:9898
grpc.client.imageManager.enableKeepAlive=true
grpc.client.imageManager.keepAliveWithoutCalls=true
grpc.client.imageManager.negotiationType=plaintext

# report manager
grpc.client.reportManager.address=static://bees360-report-app-grpc:9898
grpc.client.reportManager.enableKeepAlive=true
grpc.client.reportManager.keepAliveWithoutCalls=true
grpc.client.reportManager.negotiationType=plaintext

# ProjectInvoiceGenerator
grpc.client.projectInvoiceGenerator.address=static://bees360-project-app-grpc:9898
grpc.client.projectInvoiceGenerator.enableKeepAlive=true
grpc.client.projectInvoiceGenerator.keepAliveWithoutCalls=true
grpc.client.projectInvoiceGenerator.negotiationType=plaintext

# UserKeyProvider
grpc.client.userKeyProvider.address=static://bees360-user-app-grpc:9898
grpc.client.userKeyProvider.enableKeepAlive=true
grpc.client.userKeyProvider.keepAliveWithoutCalls=true
grpc.client.userKeyProvider.negotiationType=plaintext


spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
report.scheduler.report-data-to-web.name=report-data-to-web
report.scheduler.report-data-to-web.thread-count=5
report.resource.prefix=report/

thread-pool-executor.corePoolSize=4
thread-pool-executor.maxPoolSize=4
thread-pool-executor.keepAliveTimeMillis=60000

# adobe ä¸ä½¿ç¨äºï¼æä»¥æ ééç½®
adobe.credential.clientId=notset
adobe.credential.organizationId=notset
adobe.credential.accountId=notset
adobe.credential.clientSecret=notset
adobe.credential.privateKey=notset

# ReportTypeEnum enum.
report.log-entry.special-report-type=DRONE_PHOTO_SHEET,MOBILE_PHOTO_SHEET

report.infera.job-name-pipeline-task-key.annotate_vent=sent_image_to_beesense_vent
report.infera.job-name-pipeline-task-key.annotate_wind_damage=sent_image_to_beesense_wind

# Add image annotation need delete value source
image.annotation.need-delete-factor-sources=GOOGLE_MAP,BRIGHT_DATA,VEXCEL,PERPLEXITY,CLAUDE,ROOF_OVERVIEW_DIRECTION
