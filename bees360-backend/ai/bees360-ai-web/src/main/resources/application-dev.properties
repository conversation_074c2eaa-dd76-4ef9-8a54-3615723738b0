server.port=9090

# log
debug=true

########## mysql #########
spring.datasource.password=ENC(tXIpBqYQAjZpA9CPOoR8Qw==)
spring.datasource.username=dev

########## redis #########
spring.data.redis.password=ENC(tXIpBqYQAjZpA9CPOoR8Qw==)

########## spring java mail #########
spring.mail.password=ENC(YfRTeYEW5RQyXXK1N2mFSrVOPiiclXdz)
bees360.mail.sending.enabled=false

# email
spring.mail.sender=Bees360-Dev<<EMAIL>>

email.recipients.enabled=false
email.recipients.claimIncompleteStatistics=<EMAIL>,<EMAIL>
email.recipients.claimCompanyCal=<EMAIL>
email.recipients.adjusterWorkloadStatistics=<EMAIL>,<EMAIL>
email.recipients.processorWorkloadStatistics=<EMAIL>
email.recipients.processor.workload.statistics.special.chinese.staffs=<EMAIL>
email.recipients.reviewerWorkloadStatistics=<EMAIL>


## CORS allowed origins
#bees360.ai.cors.allowedOrigins=http://127.0.0.1:3000,http://localhost:3000,http://**************:3000,\
#  http://*************:3000,http://*************:4000,http://*************:4001

# system constant
ai.bees360.io.system.constant.host=http://127.0.0.1:9090
# add 127.0.0.1 ai.dev.bees360.com to the end of your /etc/hosts file
ai.bees360.io.system.constant.aiWebDomains=http://us.ai.dev.bees360.com:3000,http://ai.dev.bees360.com:3000
ai.bees360.io.system.constant.autoStart3d=true

# è·¨åç½ååéç½®
cors.access.control.allowed.originWhites=http://localhost:4001,\
  http://dev-ai.bees360.com:3000,\
  https://dev-ai.bees360.com,\
  http://dev.bees360.com:4000,\
  http://127.0.0.1:3000,\
  http://127.0.0.1:4001,\
  http://127.0.0.1:4000,\
  http://localhost:4000,\
  http://us.ai.dev.bees360.com:3000,\
  http://sz.bees360.com:3000,\
  http://dev.bees360.com,\
  http://localhost:3000,\
  http://localhost:5050,\
  http://tolocalhost.com:3000
cors.access.control.allowed.originRegexes=https?://192.168.10.*,https?://.*bees360[.]com

# èµæºæå¡å¨ URI
resource.endpoint.uri=https://dev-resource.bees360.com/resource/
# èµæºæå¡å¨ cacheResourcePool
resource.cached.sourcePool=https://dev-resource.bees360.com/resource/
resource.cached.cacheRepository=file:///var/bees360/www/cache/
resource.cached.capacity=2000
# 12 hours
resource.cached.expiry=43200000

grpc.client.resourcePool.context=https://dev-resource.bees360.com/resource
grpc.client.szResourcePool.context=https://dev-resource.bees360.com/resource

company.id.swyfftUnderwriting=2131
company.id.swyfftHomeownerInsurance=2294
company.id.upcInsurance=2332
company.id.geoVeraHoldingsClaims=2334
company.id.plymouthRockHomeAssurance=2335
company.id.americanModernInsurance=2336
company.id.floridaFamily=2304
company.id.allstate=2295
company.id.alliedTrust=1733
company.id.velocity=2303
company.id.asiCommerical=2321
company.id.geoVeraInsurance=2334

company-id-map.Insurance_Risk_Services_Inc_ID=2096
company-id-map.Associated_Services_Inspections_Commercial_ID=1313
company-id-map.Allstate_ID=1733
company-id-map.Swift_ID=2300
company-id-map.Allied_Trust_ID=2299
company-id-map.Velocity_ID=2297
company-id-map.Swyfft_Underwriting=2131
company-id-map.Swyfft_Homeowner_Insurance=2332
company-id-map.UPC_Insurance=2332
company-id-map.Centauri_Insurance=2331
company-id-map.Security_First_Florida=2339
company-id-map.GeoVera_Holdings_Underwriting=2338
company-id-map.Olympus=2761
company-id-map.SageSure=2756
company-id-map.Mdow=2784
company-id-map.Canopius=2776

ai.bees360.sz.refer.prefix=localhost:3000

spring.datasource.report.driver-class-name=org.mariadb.jdbc.Driver
spring.datasource.report.password=ENC(tXIpBqYQAjZpA9CPOoR8Qw==)
spring.datasource.report.jdbc-url=************************************************************************************************************************************************************************
spring.datasource.report.username=dev

# swyfft company: 2131,2294
report.summary.forCompanies=2131,2294
