ENV=local
BEES360_SECRET_KEY=notset

server.port=9090

# log
debug=true

########## mysql #########
spring.datasource.password=123456
spring.datasource.username=root

########## spring java mail #########
spring.mail.password=H%7V%yAB
bees360.mail.sending.enabled=false

# email
spring.cc.sender=Bees360-Local<<EMAIL>>

email.recipients.enabled=false
email.recipients.claimIncompleteStatistics=<EMAIL>
email.recipients.claimCompanyCal=<EMAIL>
email.recipients.adjusterWorkloadStatistics=<EMAIL>,<EMAIL>
email.recipients.processorWorkloadStatistics=<EMAIL>
email.recipients.processor.workload.statistics.special.chinese.staffs=<EMAIL>
email.recipients.reviewerWorkloadStatistics=<EMAIL>

## CORS allowed origins
#bees360.ai.cors.allowedOrigins=http://127.0.0.1:3000,http://localhost:3000,http://**************:3000,\
#  http://*************:3000,http://*************:4000,http://*************:4001

# system constant
ai.bees360.io.system.constant.host=http://127.0.0.1:9090
# add 127.0.0.1 ai.dev.bees360.com to the end of your /etc/hosts file
ai.bees360.io.system.constant.aiWebDomains=notset
ai.bees360.io.system.constant.autoStart3d=true

# è·¨åç½ååéç½®
cors.access.control.allowed.originRegexes=https?://192.168.10.*,https?://.*bees360[.]com

# èµæºæå¡å¨ URI
resource.endpoint.uri=notset
# èµæºæå¡å¨ cacheResourcePool
resource.cached.sourcePool=notset
resource.cached.cacheRepository=file:///var/bees360/www/cache/
resource.cached.capacity=2000
# 12 hours
resource.cached.expiry=43200000

company.id.swyfftUnderwriting=2357
company.id.swyfftHomeownerInsurance=2294
company.id.upcInsurance=2315
company.id.geoVeraHoldingsClaims=2750
company.id.plymouthRockHomeAssurance=2746
company.id.americanModernInsurance=2745
company.id.floridaFamily=2511
company.id.allstate=1736
company.id.alliedTrust=2382
company.id.velocity=2501
company.id.asiCommerical=1313
company.id.geoVeraInsurance=2334

company-id-map.Insurance_Risk_Services_Inc_ID=2096
company-id-map.Associated_Services_Inspections_Commercial_ID=1313
company-id-map.Allstate_ID=1736
company-id-map.Swift_ID=2294
company-id-map.Allied_Trust_ID=2382
company-id-map.Velocity_ID=2510
company-id-map.Swyfft_Underwriting=2357
company-id-map.Swyfft_Homeowner_Insurance=2294
company-id-map.UPC_Insurance=2315
company-id-map.Centauri_Insurance=2742
company-id-map.Security_First_Florida=2752
company-id-map.GeoVera_Holdings_Underwriting=2751
company-id-map.Olympus=2761
company-id-map.SageSure=2756
company-id-map.Mdow=2784
company-id-map.Canopius=2776

spring.datasource.report.driver-class-name=org.mariadb.jdbc.Driver
spring.datasource.report.password=123456
spring.datasource.report.jdbc-url=************************************************************************************************************************************************************************
spring.datasource.report.username=root

# swyfft company: 2131,2294
report.summary.forCompanies=2131,2294

osm.nominatim.server.url=https://nominatim.openstreetmap.org/
osm.nominatim.header.email=<EMAIL>
