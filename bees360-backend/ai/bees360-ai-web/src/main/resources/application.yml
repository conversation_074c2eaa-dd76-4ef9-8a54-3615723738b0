rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    password: ${BEES360_SECRET_KEY}

spring:
  main:
    allow-circular-references: true
  profiles:
    include: actuator
  web:
    locale-resolver: fixed
    locale: en

springdoc:
  default-produces-media-type: application/json
  api-docs:
    enabled: true # Whether to enable the springdoc openapi endpoint. Default is true
    path: /openapi/v3/api-docs #springdoc openapi endpoint path default /v3/api-docs
  swagger-ui:
    enabled: true # Whether to enable swagger ui. Default is true
    path: /openapi/swagger-ui/index.html # Customize swagger access path. Default is swagger-ui.html

price:
  strategy:
    area-range:
      - name: area-range
        project_types:
          - 5
        area_range:
          - min_area: 0
            max_area: 4050
            price: 600
          - min_area: 4050
            max_area: 8050
            price: 1000
          - min_area: 8050
            max_area: 12050
            price: 1380
          - min_area: 12050
            max_area: 0
            price: 2000

company-config:
  1748:
    price:
      strategy:
        - area-range
grpc:
  client:
    projectIIManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectContactManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    customerManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectParticipantManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectStateChangeReasonManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    stateChangeReasonGroupManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectStatusManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectGroupManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectProcessStatusManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectMemberManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

three-d:
  local:
    company-key:
      - Nationwide

http:
  request:
    matchers:
      - method: "*"
        path: "/es/dashboard/{projectId:\\d+}/**"
        accessRule: "authenticated and @PAS.isProjectAccessible(#projectId)"
      - method: "*"
        path: "/projects/{projectId:\\d+}/**"
        accessRule: "authenticated and @PAS.isProjectAccessible(#projectId)"
      - method: "*"
        path: "/new-image/{projectId:\\d+}/**"
        accessRule: "authenticated and @PAS.isProjectAccessible(#projectId)"
      - method: "PUT"
        path: "/es/dashboard/projects"
        accessRule: "hasRole('ADMIN')"
    access-security:
      companyStrategy:
        realms9: 2770
        forbiddenCompanyList: 1742

pipeline:
  task-member-role:
    - task: submit_pir
      role: PRODUCER
    - task: submit_fur
      role: PROCESSOR
    - task: submit_crr
      role: PROCESSOR
    - task: submit_dar
      role: PROCESSOR
    - task: estimate_complete
      role: ADJUSTER
    - task: approve_dar
      role: REVIEWER
    - task: submit_ror
      role: PROCESSOR
    - task: approve_fur
      role: REVIEWER
    - task: approve_crr
      role: REVIEWER
    - task: approve_ror
      role: REVIEWER

project:
  state:
    close-condition:
      customer-report-condition:
        # 针对如下公司针对如下公司, 若 required-report-type 配置的报告已经 approve 但 optional-report-type 配置的却没有时，需要强制 close
        "[Amwins Insurance]":
          EXTERIOR_UNDERWRITING:
            # 18: FUR (Full-scope Underwriting Report)
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            # 42: FSR_RCE (Full-scope Underwriting Report with Recovery Cost Estimate)
            optional-report-type: [FSR_RCE]
          FOUR_POINT_UNDERWRITING:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
          FOUR_POINT_SELF_UNDERWRITING:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
          PREMIUM_FOUR_POINT_UNDERWRITING:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
          WHITE_GLOVE:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
        "[Tower Hill Insurance Group]":
          EXTERIOR_UNDERWRITING:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
          FOUR_POINT_UNDERWRITING:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
          FOUR_POINT_SELF_UNDERWRITING:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
          PREMIUM_FOUR_POINT_UNDERWRITING:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
          WHITE_GLOVE:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
        "[Olympus Insurance]":
          EXTERIOR_UNDERWRITING:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
          FOUR_POINT_UNDERWRITING:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
          FOUR_POINT_SELF_UNDERWRITING:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
          PREMIUM_FOUR_POINT_UNDERWRITING:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]
          WHITE_GLOVE:
            required-report-type: [FULL_SCOPE_UNDERWRITING_REPORT]
            optional-report-type: [FSR_RCE]

# graceful shutdown
server.shutdown: graceful
spring.lifecycle.timeout-per-shutdown-phase: 30s

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: local

auth:
  jwt:
    verifierKey: "-----BEGIN CERTIFICATE-----\nMIIC/DCCAeSgAwIBAgIIOcybo4ESpmMwDQYJKoZIhvcNAQEFBQAwIDEeMBwGA1UE\nAxMVMTAzNzQxMjAwMzE3OTIwODg2ODQ1MCAXDTIxMDcyNzAzNDgxM1oYDzk5OTkx\nMjMxMjM1OTU5WjAgMR4wHAYDVQQDExUxMDM3NDEyMDAzMTc5MjA4ODY4NDUwggEi\nMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDnpfrDqFClLoVqAg9GcM0sEmIR\ni7BP+gC8KautIhPyrScuujFHZf8MHP1AezjVd2gcWCi1pGTTNUbeZH2xet5qqP/X\nGc2OsLFh9vAGOXQz1WtJWENv4ncazZu8uBeurVyJrNIaY0G3v3JYAUHKi/Wu0GCy\nt0bSwVYVlzH8IOwlZezuUmsoWMX56eI0CFrfA2WuGwvMJQzB7daN6XB68hGt+WFE\n18xXeJCKmyjhaFoY/KpWO1Z0g8/gLYLkPrC/oa6tNigENaK7aQmaPCJ3phwiDz2a\nWPNJ8evXMGVFIE5qUHG3aNEpA2Rru4pFA9+g3ZfjIu8VnQh1kLmL5dtw0x4pAgMB\nAAGjODA2MAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgeAMBYGA1UdJQEB/wQM\nMAoGCCsGAQUFBwMCMA0GCSqGSIb3DQEBBQUAA4IBAQCTlQLQOUEr5PHvG1Y9z7Hc\n+6MXFb7WxiRM7IcSEPA0BwkZM94BF5fojlHZ2ox335669wrGqGpkNLBqIMLuxw1b\n4N5wxeeS2M8fZeUjM7kaHT8ZE+27FzOY1NNbFn+dyUqonGM/1GlGL3hgOV4hkyzk\n3vderqQakqR7bYyUmjCgBcAvQahOpkJxX4/XgqzaDQBDGmhG1VOvyTJEyZtBX1GN\nnmsz9CzqClBgydf6bnjgm4Gh4CHI4YKL/7yGXAkk50JqYbhkAmlNcIbILryilTRu\nMZGX7oskns0vBqMtLmIPG5eH/KL3KqUS1KTxW33Ucca+WnmKQjNys2QKBVLR1Y+F\n-----END CERTIFICATE-----\n"

redis:
  client:
    host: redis
    port: 6379
    database: 0

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev

auth:
  jwt:
    verifierKey: "-----BEGIN CERTIFICATE-----\nMIIC/DCCAeSgAwIBAgIIOcybo4ESpmMwDQYJKoZIhvcNAQEFBQAwIDEeMBwGA1UE\nAxMVMTAzNzQxMjAwMzE3OTIwODg2ODQ1MCAXDTIxMDcyNzAzNDgxM1oYDzk5OTkx\nMjMxMjM1OTU5WjAgMR4wHAYDVQQDExUxMDM3NDEyMDAzMTc5MjA4ODY4NDUwggEi\nMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDnpfrDqFClLoVqAg9GcM0sEmIR\ni7BP+gC8KautIhPyrScuujFHZf8MHP1AezjVd2gcWCi1pGTTNUbeZH2xet5qqP/X\nGc2OsLFh9vAGOXQz1WtJWENv4ncazZu8uBeurVyJrNIaY0G3v3JYAUHKi/Wu0GCy\nt0bSwVYVlzH8IOwlZezuUmsoWMX56eI0CFrfA2WuGwvMJQzB7daN6XB68hGt+WFE\n18xXeJCKmyjhaFoY/KpWO1Z0g8/gLYLkPrC/oa6tNigENaK7aQmaPCJ3phwiDz2a\nWPNJ8evXMGVFIE5qUHG3aNEpA2Rru4pFA9+g3ZfjIu8VnQh1kLmL5dtw0x4pAgMB\nAAGjODA2MAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgeAMBYGA1UdJQEB/wQM\nMAoGCCsGAQUFBwMCMA0GCSqGSIb3DQEBBQUAA4IBAQCTlQLQOUEr5PHvG1Y9z7Hc\n+6MXFb7WxiRM7IcSEPA0BwkZM94BF5fojlHZ2ox335669wrGqGpkNLBqIMLuxw1b\n4N5wxeeS2M8fZeUjM7kaHT8ZE+27FzOY1NNbFn+dyUqonGM/1GlGL3hgOV4hkyzk\n3vderqQakqR7bYyUmjCgBcAvQahOpkJxX4/XgqzaDQBDGmhG1VOvyTJEyZtBX1GN\nnmsz9CzqClBgydf6bnjgm4Gh4CHI4YKL/7yGXAkk50JqYbhkAmlNcIbILryilTRu\nMZGX7oskns0vBqMtLmIPG5eH/KL3KqUS1KTxW33Ucca+WnmKQjNys2QKBVLR1Y+F\n-----END CERTIFICATE-----\n"

redis:
  client:
    host: redis
    password: ENC(tXIpBqYQAjZpA9CPOoR8Qw==)
    port: 6379
    database: 0

rabbit:
  client:
    username: combee
    password: ENC(QfLd7QndPsbwwsfsuqnUnA==)
---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: stag-sz,stag-us

spring.thymeleaf:
  prefix: classpath:message/thymeleaf/
  mode: HTML
  encoding: UTF-8

auth:
  jwt:
    verifierKey: "-----BEGIN CERTIFICATE-----\nMIIC/DCCAeSgAwIBAgIIARrZbDDH8PwwDQYJKoZIhvcNAQEFBQAwIDEeMBwGA1UE\nAxMVMTE2MjAzMDQxMTEwNzQ5Njg2NjU5MCAXDTIxMDcyNzA5NDQ0OVoYDzk5OTkx\nMjMxMjM1OTU5WjAgMR4wHAYDVQQDExUxMTYyMDMwNDExMTA3NDk2ODY2NTkwggEi\nMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCPFEDfqMcKbx3dEuno7uyA3lCV\nEcBvDVOpk/Px7S8y/ofsV0aZ1CUM1vRhCXaSvUvf5tQfTiHLTXkx0tnlWwjo55hi\nscjzvPJNvFms0YN7yqqx9hRNZPJ2CXC6ZvPoE+B5ludYMIirYm6RgTiuYFu400KK\nxeyW19ZTkhRCXS+YGETx/o39J2Kc8jo6zqrWm3ol2N+BOUWjLyZw2u1mB3L2ACRC\n9VrRmZ43TjRbx14eCjEQkSAt2pFhL0nXpgp5DUnsSnRnXo9bpZCytj+0YUYlJzMm\nqqUdMrHpzo0lY98Hvc2Y9o5W9PMoe6nEPoWZv/YA4IJOAp4Xb1xM1sKOW9NVAgMB\nAAGjODA2MAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgeAMBYGA1UdJQEB/wQM\nMAoGCCsGAQUFBwMCMA0GCSqGSIb3DQEBBQUAA4IBAQA9CqqgZTKZlp8tlA1DlwsG\n63ZQMPIxiQoVmGtNSNRumKCvzk1g0fBvCTyT+b8UEOK8ft1fitIxF66F6kK/V9C6\nkEhDibynnU0wNTy5kxDzP65rye0bntcY9jhpmv3NZ2PA2xyzzaLcgoKuDYAwZU5D\nPksP7z9PNVNl2oK3TucVEof0S1rNkWDOgpguOGAw8Hnk6StX3OITqsR4MPm0YPFG\nYraqdQQ3fcHsTq1LJCMbqqrYX1vAIr9z7jvUk1W94NSJVgCnOaM2ulgskDM57fq0\ndlQHj9NmzdZTzpxhClFrR0jZQo35QZkNIcqWe/L06EhGw8Oxss8BspUuYz8upPpy\n-----END CERTIFICATE-----\n"

redis:
  client:
    host: redis.test.bees360.ai
    port: 6379
    database: 0

rabbit:
  client:
    host: test.mq.bees360.io
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: combee
    password: ENC(nwwtbk66iG6H+OGzyCTOoUDBhz1RndSY)

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod-sz,prod-us

auth:
  jwt:
    verifierKey: "-----BEGIN CERTIFICATE-----\nMIIC/DCCAeSgAwIBAgIIDYfA3+3qTAEwDQYJKoZIhvcNAQEFBQAwIDEeMBwGA1UE\nAxMVMTE1OTQ0NTQ0NDYwNTEyODc5MDAwMCAXDTIxMDgyMzA5MjkwNVoYDzk5OTkx\nMjMxMjM1OTU5WjAgMR4wHAYDVQQDExUxMTU5NDQ1NDQ0NjA1MTI4NzkwMDAwggEi\nMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCRAINKr3BHtADUtmhFEGHex3oe\nV8vt9xvaZ++q88Ek4KudTr6rjCbPK+m/PoDfAk/wgSLs7txDHqpvZGBQ/5XV+IYW\nfr1gHqvUZo/y6n3uTtr67Dx6NDfHSwrZc+Y8C7oZKJixRpVGIyNynFx200pBRkJh\n+r9BhGbJn/ztJ5gLqb7XA8hxsw0x4Pbu+n1tTSCS9u/cBtHdPJ2F2VrdiNThCT87\nww5ZBWBUPeVmjiIIaUkMqRK3k+Wtc7mG7Z6i+7cJIOHd4VNFxZXr25XI4dxCJBF8\nWTr5K0p66lBjJuGQQet7nbJx6/Sbpe/tlcvxZat3L6Q0NZZkATtPgRpjIhLJAgMB\nAAGjODA2MAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgeAMBYGA1UdJQEB/wQM\nMAoGCCsGAQUFBwMCMA0GCSqGSIb3DQEBBQUAA4IBAQBw5e2k1Tiu1poNKhT+KwT3\nnEampY80CM8Ors1wMP7YFfhjcdoFOBySvqmeTPmRRlm8BiW7j6Cx7UVG4Y4bB/ca\nDF94w4tIGhNpH1JcEtz9o+dpi29WIoOiPzHF3JKu6EoF4csPw1CY4xHOdJJUvagH\nIrXiDl0UagNLwGZHYLGr+57WbKO3XzENvuXIhlrOI0bZ9UKKS5FhQoowEWXsx5kX\nrNaWryA0v/QBTyw/yB+mD/uoSg/LxKV/3ma86veak2qtoglXMsqXD9QYpoxhTC5R\nLBq9d4fSiAZRtlqyEXERiX/mOXHJsyk37f8idy+sAk1c+4Tqm7DlAbzK2+3O5lj+\n-----END CERTIFICATE-----\n"

redis:
  client:
    host: redis.bees360.ai
    port: 6379
    database: 0

rabbit:
  client:
    host: mq.bees360.io
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: combee
    password: ENC(scajLNFejGRBRzYBtHJgGskB4AQK4GeS)

report:
  infera:
    job-name-pipeline-task-key:
      annotate_vent: "sent_image_to_beesense_vent"
      annotate_wind_damage: "sent_image_to_beesense_wind"
