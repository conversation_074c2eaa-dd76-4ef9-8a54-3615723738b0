package com.bees360.internal.ai.controller;

import com.bees360.internal.ai.scheduled.AdjusterWorkloadStatisticsScheduled;
import com.bees360.internal.ai.scheduled.ProcessorWorkloadStatisticsScheduledBeijing;
import com.bees360.internal.ai.scheduled.ProcessorWorkloadStatisticsScheduledCst;
import com.bees360.internal.ai.scheduled.ReviewerWorkloadStatisticsScheduled;
import com.bees360.internal.ai.scheduled.SearchedNameCacheRefreshScheduled;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

@PreAuthorize("hasRole('ROLE_ADMIN')")
@RestController
@RequestMapping("/scheduled")
public class ScheduledTriggerController {

    @Autowired
    AdjusterWorkloadStatisticsScheduled adjusterWorkloadStatisticsScheduled;

    @Autowired
    ProcessorWorkloadStatisticsScheduledBeijing processorWorkloadStatisticsScheduledBeijing;

    @Autowired
    ProcessorWorkloadStatisticsScheduledCst processorWorkloadStatisticsScheduledCst;

    @Autowired
    SearchedNameCacheRefreshScheduled nameCacheRefreshScheduled;

    @Autowired
    ReviewerWorkloadStatisticsScheduled reviewerWorkloadStatisticsScheduled;

    private static final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    @GetMapping("/adjusterWeeklyWorkStats")
    void sendAdjusterWeeklyWorkloadStatics() {
        adjusterWorkloadStatisticsScheduled.sendAdjusterWeeklyWorkloadStatics();
    }

    @GetMapping("/adjusterDailyWorkStats")
    void sendAdjusterDailyWorkloadStatics() {
        adjusterWorkloadStatisticsScheduled.sendAdjusterDailyWorkloadStatics();
    }

    @GetMapping("/processorDailyWorkBeijing")
    void sendProcessorDailyWorkStatisticsBeijing() {
        processorWorkloadStatisticsScheduledBeijing.sendProcessorDailyWorkloadStatics();
    }

    @GetMapping("/processorWeeklyWorkBeijing")
    void sendProcessorWeeklyWorkStatisticsBeijing() {
        processorWorkloadStatisticsScheduledBeijing.sendProcessorWeeklyWorkloadStatics();
    }

    @GetMapping("/processorDailyWorkCst")
    void sendProcessorDailyWorkStatisticsCst() {
        processorWorkloadStatisticsScheduledCst.sendProcessorDailyWorkloadStatics();
    }

    @GetMapping("/processorWeeklyWorkCst")
    void sendProcessorWeeklyWorkStatisticsCst() {
        processorWorkloadStatisticsScheduledCst.sendProcessorWeeklyWorkloadStatics();
    }

    @GetMapping("/searchNameRefresh")
    void searchNameRefresh() {
        nameCacheRefreshScheduled.searchNameRefresh();
    }

    @GetMapping("/reviewerStatsWeekly")
    void sendReviewerStatsWeekly(String startDate, String endDate) {
        final Instant start = parseDate(startDate);
        final Instant end = parseDate(endDate);
        reviewerWorkloadStatisticsScheduled.sendWorkloadStatics(start, end);
    }

    private static Instant parseDate(String date) {
        final LocalDate parsed = LocalDate.parse(date, dtf);
        final LocalDateTime localDateTime = parsed.atTime(LocalTime.MIN);
        return localDateTime.toInstant(ZoneOffset.UTC);
    }
}
