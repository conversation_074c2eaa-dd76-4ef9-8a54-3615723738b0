package com.bees360.internal.ai;

import com.bees360.auth.CustomTokenReader;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.config.ActuatorSecurityConfig;
import com.bees360.event.CronTriggerConfig;
import com.bees360.internal.ai.config.ProjectReportAutoApprovePipelineTaskConfig;
import com.bees360.project.config.GrpcProjectProcessStatusManagerConfig;
import com.bees360.project.config.GrpcProjectStatusManagerConfig;
import com.bees360.project.config.ProjectHttpSecurityConfig;
import com.bees360.user.config.GrpcGroupManagerConfig;
import lombok.extern.slf4j.Slf4j;
import com.bees360.commons.firebasesupport.config.annotation.EnableFirebaseHelperConfig;
import java.util.Optional;

import com.bees360.http.LogAndSuppressRequestRejectedExceptionFilter;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.pipeline.config.GrpcPipelineClientConfig;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mail.MailSenderAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.repository.configuration.EnableRedisRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.bees360.auth.config.JwtResourceServerConfig;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.user.config.GrpcUserProviderConfig;
import com.bees360.util.EnvUtils;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

@SpringBootApplication(
        exclude = {
            UserDetailsServiceAutoConfiguration.class,
            RestClientAutoConfiguration.class,
            MailSenderAutoConfiguration.class
        })
@Configuration
@EnableRedisRepositories(basePackages = "com.bees360.internal.ai.cache")
@EnableEncryptableProperties()
@EnableScheduling
@EnableAsync
@EnableFirebaseHelperConfig
@Import({
    // @formatter:off
    ApiExceptionHandler.class,
    JwtResourceServerConfig.class,
    CustomTokenReader.class,
    GrpcUserProviderConfig.class,
    ProtoHttpMessageConverterConfig.class,
    LogAndSuppressRequestRejectedExceptionFilter.class,
    GrpcPipelineClientConfig.class,
    GrpcGroupManagerConfig.class,
    ProjectHttpSecurityConfig.class,
    CronTriggerConfig.class,
    ActuatorSecurityConfig.class,
    GrpcProjectStatusManagerConfig.class,
    GrpcProjectProcessStatusManagerConfig.class,
    // @formatter:on
    ProjectReportAutoApprovePipelineTaskConfig.class,
    ActuatorSecurityConfig.class,
})
@Slf4j
public class Bees360AiApplication {

    public static void main(String[] args) throws Exception {
        EnvUtils.initLog4jSystemProperties(
                Optional.of("bees360-ai"), Optional.empty(), Optional.empty(), Optional.empty());
        ExitableSpringApplication.run(Bees360AiApplication.class, args);
    }
}
