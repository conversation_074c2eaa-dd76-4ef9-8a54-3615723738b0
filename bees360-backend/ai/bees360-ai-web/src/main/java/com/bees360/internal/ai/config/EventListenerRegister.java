package com.bees360.internal.ai.config;

import com.bees360.event.EventDispatcher;
import com.bees360.event.EventListener;
import java.util.List;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * 事件监听器 统一注册
 */
@Slf4j
@Configuration
public class EventListenerRegister {

    @Autowired
    private List<EventListener> eventListeners;

    @Autowired
    private EventDispatcher rabbitEventDispatcher;

    /**
     * enlist event executor
     */
    @PostConstruct
    void enlistEventExecutors() {
        eventListeners.forEach(e -> rabbitEventDispatcher.enlist(e));
    }

}
