package com.bees360.internal.ai.scheduled;

import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.internal.ai.entity.dto.EmailRecipients;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.ReviewerWorkloadStatisticsJob;
import com.bees360.util.Calendars;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;

import static com.bees360.commons.lang.time.DateTimeUtil.getStartOfDay;

@Component
@Log4j2
@RequiredArgsConstructor
public class ReviewerWorkloadStatisticsScheduled {
    private static final ZoneId US_CENTER_ZONE =
            TimeZone.getTimeZone(AmericaTimeZone.US_CENTRAL).toZoneId();

    private final JobScheduler jobScheduler;
    private final EmailRecipients emailRecipients;

    /** 每周一的凌晨两点统计上周的所有Reviewer的工作量 (完成Return to client和Return to client报告的数量) */
    public void sendReviewerWeeklyWorkloadStatics() {
        log.info("sendReviewerWeeklyWorkloadStatics cronTriggerEveryMondayAt2AmCst");
        // 得到美国时间七天前凌晨0点0分0秒的时间戳
        Instant startTime = getStartOfDay(-7, US_CENTER_ZONE);
        // 得到美国时间当天凌晨0点0分0秒的时间戳
        Instant endTime = getStartOfDay(0, US_CENTER_ZONE).minusSeconds(1);
        var recipients =
                Optional.ofNullable(emailRecipients.getWeeklyReviewerWorkloadStatistics())
                        .orElse(emailRecipients.getReviewerWorkloadStatistics());
        sendWorkloadStatics(startTime, endTime, recipients);
    }

    /** 每天一的凌晨两点统计上周的所有Reviewer的工作量 (完成Return to client和Return to client报告的数量) */
    public void sendReviewerDailyWorkloadStatics() {
        log.info("sendReviewerDailyWorkloadStatics cronTriggerDailyAt2AmCst");
        // 得到美国时间前一天凌晨0点0分0秒的时间戳
        Instant startTime = getStartOfDay(-1, US_CENTER_ZONE);
        // 得到美国时间前一天凌晨23点59分59秒的时间戳
        Instant endTime = getStartOfDay(0, US_CENTER_ZONE).minusSeconds(1);
        var recipients = emailRecipients.getReviewerWorkloadStatistics();
        sendWorkloadStatics(startTime, endTime, recipients);
    }

    /** 美国时间每月一号,统计前一个月所有的Reviewer工作量 */
    public void sendReviewerMonthlyWorkloadStatics() {
        log.info("sendReviewerMonthlyWorkloadStatics cronTriggerMonthly1stAt2AmCst");
        Instant startTime = Calendars.getStartOfMonth(-1, US_CENTER_ZONE);
        Instant endTime = Calendars.getEndOfMonth(-1, US_CENTER_ZONE).minusSeconds(1);
        var recipients =
                Optional.ofNullable(emailRecipients.getMonthlyReviewerWorkloadStatistics())
                        .orElse(emailRecipients.getReviewerWorkloadStatistics());
        sendWorkloadStatics(startTime, endTime, recipients);
    }

    /** 每周一的凌晨两点统计本月所有Reviewer的工作量 (完成Return to client和Return to client报告的数量) */
    public void sendReviewerMonthlyAccumulatedWorkloadStatics() {
        log.info("sendReviewerMonthlyAccumulatedWorkloadStatics cronTriggerMonthly1stAt2AmCst");
        Instant startTime = Calendars.getStartOfMonth(0, US_CENTER_ZONE);
        Instant endTime = Calendars.getEndOfMonth(0, US_CENTER_ZONE).minusSeconds(1);
        var recipients =
            Optional.ofNullable(emailRecipients.getMonthlyReviewerWorkloadStatistics())
                .orElse(emailRecipients.getReviewerWorkloadStatistics());
        sendWorkloadStatics(startTime, endTime, recipients);
    }

    public void sendWorkloadStatics(Instant startTime, Instant endTime) {
        sendWorkloadStatics(startTime, endTime, emailRecipients.getReviewerWorkloadStatistics());
    }

    public void sendWorkloadStatics(Instant startTime, Instant endTime, List<String> recipients) {
        var job = new ReviewerWorkloadStatisticsJob(startTime, endTime, recipients);
        jobScheduler.schedule(
                RetryableJob.of(JobPayloads.encode(job), 8, Duration.ofMinutes(2), null));
        log.info(
                "Schedule reviewer workload statistics from {} to {} email sending job successfully.",
                startTime,
                endTime);
    }
}
