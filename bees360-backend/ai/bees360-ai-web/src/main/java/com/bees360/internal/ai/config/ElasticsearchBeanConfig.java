package com.bees360.internal.ai.config;

import com.bees360.commons.elasticsearchsupport.ElasticSearchHelper;
import com.bees360.commons.elasticsearchsupport.EsModelConverter;
import com.bees360.commons.elasticsearchsupport.EsModelGsonConverter;
import org.opensearch.client.RestHighLevelClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> Yang
 */
@Configuration
public class ElasticsearchBeanConfig {

    private final RestHighLevelClient restHighLevelClient;

    public ElasticsearchBeanConfig(RestHighLevelClient restHighLevelClient) {
        this.restHighLevelClient = restHighLevelClient;
    }

    @Bean
    public ElasticSearchHelper elasticSearchHelper() {
        ElasticSearchHelper helper = new ElasticSearchHelper(restHighLevelClient);
        helper.setAllowUpsert(true);
        return helper;
    }

    @Bean
    public EsModelConverter esModelConverter() {
        return new EsModelGsonConverter();
    }
}
