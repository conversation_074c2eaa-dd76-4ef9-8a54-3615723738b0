package com.bees360.event;

import com.bees360.event.registry.CronTriggerMonthly1stAt2AmBeijing;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.ProcessorWorkloadStatisticsScheduledBeijing;
import java.io.IOException;

/**
 * 每月1号凌晨2点（北京时间）触发处理器工作负载统计任务并发送统计结果
 */
public class ProcessorMonthlyWorkloadStaticsScheduledBeijingCronTrigger extends AbstractNamedEventListener<CronTriggerMonthly1stAt2AmBeijing> {

    private final ProcessorWorkloadStatisticsScheduledBeijing processorWorkloadStatisticsScheduledBeijing;

    public ProcessorMonthlyWorkloadStaticsScheduledBeijingCronTrigger(ProcessorWorkloadStatisticsScheduledBeijing processorWorkloadStatisticsScheduledBeijing) {
        this.processorWorkloadStatisticsScheduledBeijing = processorWorkloadStatisticsScheduledBeijing;
    }

    @Override
    public void handle(CronTriggerMonthly1stAt2AmBeijing cronTriggerMonthly1stAt2AmBeijing) throws IOException {
        processorWorkloadStatisticsScheduledBeijing.sendProcessorMonthlyWorkloadStatics();
    }
}
