package com.bees360.internal.ai.scheduled;

import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.internal.ai.entity.dto.ProcessorClaimStatistics;
import com.bees360.internal.ai.entity.dto.ProcessorUWStatistics;
import com.bees360.internal.ai.service.AiNotificationService;
import com.bees360.internal.ai.service.ProjectWorkloadStatisticService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.job.registry.ProcessorWorkloadStatisticsJob;
import com.bees360.job.util.AbstractJobExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Log4j2
@Component
@RequiredArgsConstructor
public class ProcessorWorkloadStatisticsJobExecutor extends AbstractJobExecutor<ProcessorWorkloadStatisticsJob>{

    private final ProjectWorkloadStatisticService projectWorkloadStatisticService;

    private final AiNotificationService aiNotificationService;

    @Autowired final Bees360FeatureSwitch bees360FeatureSwitch;

    @Override
    protected void handle(ProcessorWorkloadStatisticsJob job) throws IOException {
        log.info(
                "Start to send processor workload statistics email to '{}' between '{}' and '{}'.",
                job.getRecipients(),
                job.getStartTime(),
                job.getEndTime());
        Instant startTime = job.getStartTime();
        Instant endTime = job.getEndTime();
        final ZoneId zoneId = TimeZone.getTimeZone(job.getTimeZone()).toZoneId();
        Collection<ProcessorUWStatistics> uwStatistics;
        Collection<ProcessorClaimStatistics> claimStatistics;
        uwStatistics = projectWorkloadStatisticService.getProcessorUWWorkloadStatistic(startTime, endTime);
        log.info("Successfully get underwriting statistics '{}'", uwStatistics);
        if (bees360FeatureSwitch.isEnableCloseClaimEmail()) {
            claimStatistics = CollectionUtils.emptyCollection();
        } else {
            claimStatistics = projectWorkloadStatisticService.getProcessorClaimWorkloadStatistic(startTime, endTime);
        }
        log.info("Successfully get claim statistics '{}'", claimStatistics);
        var umEmailMap =
                uwStatistics.stream()
                        .filter(uw -> Objects.nonNull(uw.getStaffEmail()))
                        .collect(Collectors.groupingBy(ProcessorUWStatistics::getStaffEmail));
        var claimEmailMap =
                claimStatistics.stream()
                        .filter(claim -> Objects.nonNull(claim.getStaffEmail()))
                        .collect(Collectors.groupingBy(ProcessorClaimStatistics::getStaffEmail));
        final List<ProcessorUWStatistics> defaultUwStatistics = Collections.emptyList();
        final List<ProcessorClaimStatistics> defaultClaimStatistics = Collections.emptyList();
        List<ProcessorUWStatistics> managerUWStatistics = new ArrayList<>();
        List<ProcessorClaimStatistics> managerClaimStatistics = new ArrayList<>();
        var emails =
                Stream.of(umEmailMap.keySet(), claimEmailMap.keySet())
                        .flatMap(Set::stream)
                        .collect(Collectors.toSet());
        boolean isChineseEmail = job.getTimeZone().equals("Asia/Shanghai");
        emails.forEach(
                email -> {
                    if (job.getTimeZone().equals("Asia/Shanghai")
                            && !isChineseEmail(email, job.getSpecialChineseStaffs())) {
                        return;
                    }
                    if (job.getTimeZone().equals(AmericaTimeZone.US_CENTRAL)
                            && !isAmericanEmail(email, job.getSpecialChineseStaffs())) {
                        return;
                    }
                    var uwStatistic = umEmailMap.getOrDefault(email, defaultUwStatistics);
                    var claimStatistic = claimEmailMap.getOrDefault(email, defaultClaimStatistics);
                    managerUWStatistics.addAll(uwStatistic);
                    managerClaimStatistics.addAll(claimStatistic);
                    if (!isChineseEmail) {
                        var uws =
                            uwStatistic.stream()
                                .map(ProcessorUWStatistics::getCompanyName)
                                .collect(Collectors.toList());
                        var claims =
                            claimStatistic.stream()
                                .map(ProcessorClaimStatistics::getCompanyName)
                                .collect(Collectors.toList());
                        aiNotificationService.sendProcessStatistics(
                            uwStatistic,
                            claimStatistic,
                            LocalDate.ofInstant(startTime, zoneId),
                            LocalDate.ofInstant(endTime, zoneId),
                            List.of(email),
                            job.getTimeZone());
                        log.info("send email to :{} with uw and claim :{}, {}", email, uws, claims);
                    }
                });
        aiNotificationService.sendProcessStatistics(
            managerUWStatistics,
            managerClaimStatistics,
            LocalDate.ofInstant(startTime, zoneId),
            LocalDate.ofInstant(endTime, zoneId),
            job.getRecipients(),
            job.getTimeZone());
        log.info("Success send processor workload statistics email from {} to {}.", startTime, endTime);
    }

    public boolean isChineseEmail(String email, List<String> specialChineseStaffs) {
        log.info("Determine if the email is a Chinese email: {}", email);
        // The email suffix must be "@9realms.co", or in specialChineseStaffs.
        String emailSuffix = email.substring(email.length() - "@9realms.co".length());
        return emailSuffix.equals("@9realms.co") || specialChineseStaffs.contains(email);
    }

    public boolean isAmericanEmail(String email, List<String> specialChineseStaffs) {
        log.info("Determine if the email is a America email: {}", email);
        // The email suffix must be "@bees360.com" and not in specialChineseStaffs.
        String emailSuffix = email.substring(email.length() - "@bees360.com".length());
        return emailSuffix.equals("@bees360.com") && !specialChineseStaffs.contains(email);
    }
}
