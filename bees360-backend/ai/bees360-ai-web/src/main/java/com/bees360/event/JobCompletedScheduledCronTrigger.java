package com.bees360.event;

import com.bees360.event.registry.CronTriggerHalfHourCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.service.scheduled.JobCompletedScheduled;
import java.io.IOException;

/**
 * 该类是一个定时任务触发器，每半小时调用JobCompletedScheduled的sendWorkloadStatics方法发送工作负载统计信息。
 */
public class JobCompletedScheduledCronTrigger extends AbstractNamedEventListener<CronTriggerHalfHourCst> {

    private final JobCompletedScheduled jobCompletedScheduled;

    public JobCompletedScheduledCronTrigger(JobCompletedScheduled jobCompletedScheduled) {
        this.jobCompletedScheduled = jobCompletedScheduled;
    }

    @Override
    public void handle(CronTriggerHalfHourCst cronTriggerHalfHourCst) throws IOException {
        jobCompletedScheduled.sendWorkloadStatics();
    }
}
