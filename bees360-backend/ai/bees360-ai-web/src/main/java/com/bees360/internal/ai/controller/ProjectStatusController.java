package com.bees360.internal.ai.controller;

import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.common.msgcode.MsgCodeManager;
import com.bees360.internal.ai.entity.ProjectReworkRequest;
import com.bees360.internal.ai.entity.StatusRequest;
import com.bees360.internal.ai.entity.enums.UserAuthEnum;
import com.bees360.internal.ai.service.ProjectStatusService;
import com.bees360.user.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/05/09 17:23
 */
@RestController
@RequestMapping("/projects/{projectId:\\d+}/statuses")
public class ProjectStatusController {

    @Autowired
    private ProjectStatusService projectStatusService;


    @PutMapping(path = "/update")
    public void updateStatusByAiUser(@AuthenticationPrincipal User user, @PathVariable long projectId,
                                     @RequestBody StatusRequest statusRequest) {
        if (!UserAuthEnum.hasRole(user.getAllAuthority(), UserAuthEnum.ROLE_ADMIN)) {
            throw new ServiceException(MsgCodeManager.ROLE.ROLE_AUTH_ERROR);
        }
        projectStatusService.updateStatusByAiUser(projectId, statusRequest.getCode(), user);
    }

    @PostMapping(path = "/rework")
    public void reworkProject(@AuthenticationPrincipal User user, @PathVariable long projectId,
        @RequestBody ProjectReworkRequest request) {
        projectStatusService.reworkProject(projectId,user.getId(), request.getTitle(),request.getContent());
    }

}
