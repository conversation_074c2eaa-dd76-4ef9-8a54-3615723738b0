package com.bees360.internal.ai.controller;

import com.bees360.common.collections.ListUtil;
import com.bees360.image.ImageGroupManager;
import com.bees360.internal.ai.controller.voassemble.VoAssembleException;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.config.webannotation.ProtoGetMapping;
import com.bees360.internal.ai.config.webannotation.ProtoPatchMapping;
import com.bees360.internal.ai.config.webannotation.ProtoPostMapping;
import com.bees360.internal.ai.controller.voassemble.ProjectImageVoAssemble;
import com.bees360.internal.ai.entity.dto.IdValueDto;
import com.bees360.internal.ai.entity.dto.ImageIdAndCompassDto;
import com.bees360.internal.ai.entity.dto.ImageIn3DModelModifierListDto;
import com.bees360.internal.ai.entity.dto.ImageQueryDto;
import com.bees360.internal.ai.entity.dto.ProjectImageFilter;
import com.bees360.internal.ai.entity.enums.LogProjectDataActionEnum;
import com.bees360.internal.ai.event.ProjectImageChangeEvent;
import com.bees360.internal.ai.exchange.ai2client.ProjectImageProto;
import com.bees360.internal.ai.service.ProjectImageService;
import com.bees360.user.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> Guanrong
 * @date 2019/08/09 18:52
 */
@RestController
@RequestMapping("/projects/{projectId:\\d+}/images")
public class ProjectImageController {

    @Autowired
    private ProjectImageService projectImageService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private ImageGroupManager imageGroupManager;

    @ProtoGetMapping("")
    public ProjectImageProto.ProjectImageList listImages(@PathVariable long projectId,
                                                         ProjectImageProto.ImageQuery.Builder imageQuery) throws Exception {
        ImageQueryDto imageQueryDto = ProjectImageVoAssemble.toImageQueryDto(imageQuery.build());
        List<ProjectImage> images = projectImageService.listImages(projectId, imageQueryDto);
        return ProjectImageVoAssemble.toProtoImageList(images);
    }

    @ProtoPostMapping("/boundary")
    public ProjectImageProto.ProjectImageList listBoundaryImages(@PathVariable long projectId,
                                                                 @RequestBody ProjectImageProto.ImageFilter imageFilter) throws Exception {
        ProjectImageFilter projectImageFilter = new ProjectImageFilter();
        projectImageFilter.setImageIds(imageFilter.getImageIdsList());
        List<ProjectImage> images = projectImageService.listBoundaryImages(projectId, projectImageFilter);
        return ProjectImageVoAssemble.toProtoImageList(images);
    }

    @ProtoPatchMapping("/sort")
    public void updateImageSort(@PathVariable long projectId, @RequestBody ProjectImageProto.ImageSort imageSort, @AuthenticationPrincipal User user) {
        List<IdValueDto<String, Long>> sorts = ListUtil
            .toList(sortItem -> new IdValueDto<>(sortItem.getId(), sortItem.getValue()), imageSort.getSortsList());
        projectImageService.updateImageSort(projectId, sorts, user.getId());
    }

    @ProtoPatchMapping("/in-3d-model")
    public void updateImageIn3DModel(@PathVariable long projectId,
        @RequestBody ProjectImageProto.ImageIn3DModelItems imageIn3DModelItems) {
        ImageIn3DModelModifierListDto imageIn3DModels = ProjectImageVoAssemble
            .toProtoImageIn3DModelItems(imageIn3DModelItems);
        projectImageService.updateImageIn3DModel(projectId, imageIn3DModels);
    }

    @DeleteMapping("/delete-completely")
    public void deleteCompletely(
            @AuthenticationPrincipal User user,
            @PathVariable long projectId,
            @RequestBody ProjectImageProto.ImageIdList imageIds) {
        List<String> imageIdList = imageIds.getImageIdsList();
        projectImageService.deleteCompletely(projectId, user, imageIdList);
    }

    /**
     * delete or recover images
     */
    @ProtoPatchMapping("/existence")
    public void recoverDeletedImages(@AuthenticationPrincipal User user, @PathVariable long projectId,
                                     @RequestBody ProjectImageProto.ImageRecoverDelete imageBatchDelete) {
        List<String> imageIds = imageBatchDelete.getImageIdsList();
        boolean deleted = imageBatchDelete.getDeleted();
        // delete or recover
        if (deleted) {
            projectImageService.deleteImages(projectId, imageIds);
            imageGroupManager.moveToTrash(String.valueOf(projectId), "GROUP_PROJECT", imageIds, user.getId());
            publisher.publishEvent(new ProjectImageChangeEvent(this, projectId, LogProjectDataActionEnum.DELETED,
                user));
        } else {
            projectImageService.recoverImages(projectId, imageIds);
            imageGroupManager.recoverFromTrash(String.valueOf(projectId), "GROUP_PROJECT", imageIds, user.getId());
        }
    }

    @PutMapping("/compass")
    public void updateImagesCompass(
            @AuthenticationPrincipal User user,
            @PathVariable long projectId,
            @RequestBody ProjectImageProto.ImageCompassList imageCompassList) {
        List<ImageIdAndCompassDto> imageIdAndCompassDtos =
                ListUtil.toList(
                        imageIdCompass ->
                                ImageIdAndCompassDto.builder()
                                        .imageId(imageIdCompass.getImageId())
                                        .compass(
                                                imageIdCompass.hasCompass()
                                                        ? imageIdCompass.getCompass().getValue()
                                                        : null)
                                        .build(),
                        imageCompassList.getImageIdCompassList());
        projectImageService.updateImagesCompass(projectId, user, imageIdAndCompassDtos);
    }

    @PostMapping("/derive")
    public ProjectImageProto.ProjectImageList deriveImages(
        @AuthenticationPrincipal User user, @PathVariable long projectId,
        @RequestBody ProjectImageProto.DerivativeImageRequest derivativeImages) throws VoAssembleException {
        var images = projectImageService.deriveImages(projectId, user, derivativeImages);
        return ProjectImageVoAssemble.toProtoImageList(images);
    }
}
