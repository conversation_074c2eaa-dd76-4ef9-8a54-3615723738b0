package com.bees360.internal.ai.config;

import com.bees360.mail.MailSender;
import com.bees360.mail.MailSenderProvider;
import com.bees360.mail.config.JobMailSenderConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    JobMailSenderConfig.class,
})
@Configuration
public class MailSenderConfig {

    @Bean
    MailSender mailSender(MailSenderProvider mailSenderProvider) {
        return mailSenderProvider.get("statistics-mail-sender");
    }
}
