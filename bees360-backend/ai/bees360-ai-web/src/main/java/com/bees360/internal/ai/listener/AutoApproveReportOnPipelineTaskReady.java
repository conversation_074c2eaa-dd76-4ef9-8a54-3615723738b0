package com.bees360.internal.ai.listener;

import static com.bees360.report.Message.ReportMessage.Status.APPROVED;
import static com.bees360.report.Message.ReportMessage.Status.SUBMITTED;

import com.bees360.entity.User;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.ReportManager;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.service.ProjectReportFileService;
import lombok.extern.log4j.Log4j2;
import com.google.common.collect.Iterables;
import com.bees360.pipeline.Message;
import com.bees360.event.util.PipelineTaskChangedListeners;

import java.util.function.Predicate;
import java.io.IOException;
import java.util.function.Supplier;

/**
 * 监听流水线任务状态变化并自动审批符合条件的报告
 */
@Log4j2
public class AutoApproveReportOnPipelineTaskReady
        extends AbstractNamedEventListener<PipelineTaskChanged> {

    public static final String EVENT_LISTENER_QUEUE_PREFIX = "report_auto_approve_on/";

    private final String pipelineTaskKey;
    private final String reportType;
    private final Predicate<String> projectAutoApprovePredicate;
    private final ProjectReportProvider projectReportProvider;
    private final ReportManager reportManager;
    private final ProjectReportFileService projectReportFileService;

    private final Supplier<String> systemUserSupplier;

    public AutoApproveReportOnPipelineTaskReady(
        String pipelineTaskKey,
        String reportType,
        Predicate<String> projectAutoApprovePredicate,
        ProjectReportProvider projectReportProvider,
        ReportManager reportManager,
        ProjectReportFileService projectReportFileService,
        Supplier<String> systemUserSupplier) {
        this.pipelineTaskKey = pipelineTaskKey;
        this.reportType = reportType;
        this.projectAutoApprovePredicate = projectAutoApprovePredicate;
        this.projectReportProvider = projectReportProvider;
        this.reportManager = reportManager;
        this.projectReportFileService = projectReportFileService;
        this.systemUserSupplier = systemUserSupplier;
        log.info(
                "Created {}(pipelineTaskKey={}, reportType={}).",
                this,
                this.pipelineTaskKey,
                this.reportType);
    }

    @Override
    public void handle(PipelineTaskChanged event) throws IOException {

        var projectId = event.getPipelineId();
        var reports = projectReportProvider.find(projectId, reportType, null);
        var latestReport = Iterables.getFirst(reports, null);

        if (latestReport == null) {
            return;
        } else if (APPROVED.equals(latestReport.getStatus())
                || !projectAutoApprovePredicate.test(projectId)) {
            log.info(
                    "The {} {} report for project {} will not be approved.",
                    latestReport.getStatus(),
                    reportType,
                    projectId);
            return;
        }

        var reportId = latestReport.getId();
        // To approve the report, first update its status to 'SUBMITTED'.
        reportManager.updateStatus(reportId, SUBMITTED, systemUserSupplier.get());
        try {
            projectReportFileService.approveReport(
                    Long.parseLong(projectId), User.AI_ID, reportId, String.valueOf(User.AI_ID));
        } catch (ServiceException e) {
            log.error("Fail to approve {} report for project {}.", reportType, projectId);
            return;
        }

        log.info("Successfully approve {} report for project {}. ", reportType, projectId);
    }

    @Override
    public String getName() {
        return EVENT_LISTENER_QUEUE_PREFIX + getRoutingKey();
    }

    @Override
    public String getRoutingKey() {
        return PipelineTaskChangedListeners.getEventName(
                pipelineTaskKey, Message.PipelineStatus.READY);
    }
}
