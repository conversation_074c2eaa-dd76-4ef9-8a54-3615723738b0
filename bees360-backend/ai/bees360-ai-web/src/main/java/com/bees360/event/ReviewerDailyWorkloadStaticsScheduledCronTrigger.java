package com.bees360.event;

import com.bees360.event.registry.CronTriggerDailyAt2AmCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.ReviewerWorkloadStatisticsScheduled;
import java.io.IOException;

/**
 * 定时触发评审员每日工作量统计任务，每天凌晨2点执行统计并发送结果
 */
public class ReviewerDailyWorkloadStaticsScheduledCronTrigger extends AbstractNamedEventListener<CronTriggerDailyAt2AmCst> {

    private final ReviewerWorkloadStatisticsScheduled reviewerWorkloadStatisticsScheduled;

    public ReviewerDailyWorkloadStaticsScheduledCronTrigger(ReviewerWorkloadStatisticsScheduled reviewerWorkloadStatisticsScheduled) {
        this.reviewerWorkloadStatisticsScheduled = reviewerWorkloadStatisticsScheduled;
    }

    @Override
    public void handle(CronTriggerDailyAt2AmCst cronTriggerDailyAt2AmCst) throws IOException {
        reviewerWorkloadStatisticsScheduled.sendReviewerDailyWorkloadStatics();
    }
}
