package com.bees360.internal.ai.controller.voassemble;

import com.bees360.common.collections.ListUtil;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.entity.dto.ImageIn3DModelModifierItemDto;
import com.bees360.internal.ai.entity.dto.ImageIn3DModelModifierListDto;
import com.bees360.internal.ai.entity.dto.ImageQueryDto;
import com.bees360.internal.ai.exchange.ai2client.ProjectImageProto;
import com.bees360.report.entity.ProjectImageTag;
import com.bees360.report.service.util.image.AttributeMessageUtil;
import com.google.protobuf.DoubleValue;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.bees360.util.Functions.acceptIfNotNull;

/**
 * <AUTHOR>
 * @date 2019/08/24 11:38
 */
public class ProjectImageVoAssemble {

    public static ProjectImageProto.ProjectImageList toProtoImageList(List<ProjectImage> images)
        throws VoAssembleException {
        try {
            ProjectImageProto.ProjectImageList.Builder builder = ProjectImageProto.ProjectImageList.newBuilder();
            for (ProjectImage image : images) {
                builder.addImages(toProtoImageItem(image));
            }
            return builder.build();
        } catch (Exception e) {
            throw new VoAssembleException(e.getMessage(), e);
        }
    }

    public static ProjectImageProto.ProjectImage toProtoImageItem(ProjectImage image) {

        // @formatter:off
        ProjectImageProto.ProjectImage.Builder builder = ProjectImageProto.ProjectImage.newBuilder()
            .setImageId(image.getImageId())
            .setProjectId(image.getProjectId())
            .setUserId(image.getUserId())
            .setFileName(StringUtils.trimToEmpty(image.getFileName()))
            .setFileNameMiddleResolution(StringUtils.trimToEmpty(image.getFileNameMiddleResolution()))
            .setFileNameLowerResolution(StringUtils.trimToEmpty(image.getFileNameLowerResolution()))
            .setAnnotationImage(StringUtils.trimToEmpty(image.getAnnotationImage()))
            .setOriginalFileName(StringUtils.trimToEmpty(image.getOriginalFileName()))
            .setImageHeight(image.getImageHeight())
            .setImageWidth(image.getImageWidth())
            .setFileSize(image.getFileSize())
            .setFileSourceType(image.getFileSourceType())
            .setImageType(image.getImageType())
            .setDirection(image.getDirection())
            .setManuallyAnnotated(image.isManuallyAnnotated())
            .setOrientation(Optional.ofNullable(image.getOrientation()).orElse(0))
            .setUploadTime(image.getUploadTime())
            .setDeleted(image.isDeleted())
            .setGpsLocationLatitude(image.getGpsLocationLatitude())
            .setGpsLocationLongitude(image.getGpsLocationLongitude())
            .setPartialType(image.getPartialType())
            .setTiffOrientation(image.getTiffOrientation())
            .setRelativeAltitude(image.getRelativeAltitude())
            .setImageCategory(StringUtils.trimToEmpty(image.getImageCategory()))
            .setIn3DModel(image.getIn3DModel())
            .setShootingTime(image.getShootingTime())
            .setImageSort(Objects.isNull(image.getImageSort()) ? 0 : image.getImageSort())
            .setCategoryTag(Objects.isNull(image.getCategoryTag()) ? 0 : image.getCategoryTag())
            .setObjectTag(Objects.isNull(image.getObjectTag()) ? 0 : image.getObjectTag())
            .setScopeTag(Objects.isNull(image.getScopeTag()) ? 0 : image.getScopeTag())
            .setDirectionTag(Objects.isNull(image.getDirectionTag()) ? 0 : image.getDirectionTag())
            .setLocationTag(Objects.isNull(image.getLocationTag()) ? 0 : image.getLocationTag())
            .setReportTag(Objects.isNull(image.getReportTag()) ? 0 : image.getReportTag())
            .setOrientationTag(Objects.isNull(image.getOrientationTag()) ? 0 : image.getOrientationTag())
            .setCategoryImageTag(toProtoImageTag(image.getCategoryImageTag()))
            .setObjectImageTag(toProtoImageTag(image.getObjectImageTag()))
            .setLocationImageTag(toProtoImageTag(image.getLocationImageTag()))
            .setScopeImageTag(toProtoImageTag(image.getScopeImageTag()))
            .setDirectionImageTag(toProtoImageTag(image.getDirectionImageTag()))
            .setOrientationImageTag(toProtoImageTag(image.getOrientationImageTag()))
            .setReportImageTag(toProtoImageTag(image.getReportImageTag()))
            .setFloorLevelImageTag(toProtoImageTag(image.getFloorLevelImageTag()))
            .setNumberImageTag(toProtoImageTag(image.getNumberImageTag()))
            // 前端没有用到过roomName
            .setRoomName("");
        if (Objects.nonNull(image.getCompass())) {
            builder.setCompass(
                    DoubleValue.newBuilder(
                                    DoubleValue.newBuilder().setValue(image.getCompass()).build())
                            .build());
        }
        // @formatter:on

        return builder.build();
    }

    private static ProjectImageProto.ImageTag toProtoImageTag(ProjectImageTag imageTag) {
        if (Objects.isNull(imageTag)) {
            return ProjectImageProto.ImageTag.getDefaultInstance();
        }
        var builder = ProjectImageProto.ImageTag.newBuilder();
        acceptIfNotNull(builder::setId, imageTag.getId());
        acceptIfNotNull(builder::setTagId, imageTag.getTagId());
        acceptIfNotNull(builder::setId, imageTag.getId());
        acceptIfNotNull(builder::setAttribute, imageTag.getAttribute(), AttributeMessageUtil::jsonToAttribute);
        return builder.build();
    }

    public static ImageQueryDto toImageQueryDto(ProjectImageProto.ImageQuery imageQuery) {
        return ImageQueryDto.builder()
            .fileSourceType(imageQuery.getFileSourceType())
            .deleted(imageQuery.getDeleted())
            .build();
    }

    public static ImageIn3DModelModifierListDto toProtoImageIn3DModelItems(
        ProjectImageProto.ImageIn3DModelItems imageIn3DModels) {
        List<ImageIn3DModelModifierItemDto> imageIn3DModelModifierItem = ListUtil
            .toList(item -> ImageIn3DModelModifierItemDto.builder()
                .imageIds(item.getImageIdsList())
                .in3DModel(item.getIn3DModel())
                .build(), imageIn3DModels.getIn3DModelItemList());
        return ImageIn3DModelModifierListDto.builder()
            .in3DModels(imageIn3DModelModifierItem)
            .build();
    }
}
