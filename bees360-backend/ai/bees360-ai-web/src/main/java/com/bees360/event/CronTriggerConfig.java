package com.bees360.event;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
public class CronTriggerConfig {
    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"searched-name-cache-refresh"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({SearchedNameCacheRefreshScheduledCronTrigger.class})
    static class SearchedNameCacheRefreshScheduledCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"adjuster-daily-workload-statics"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({AdjusterDailyWorkloadStaticsScheduledCronTrigger.class})
    static class AdjusterDailyWorkloadStaticsScheduledCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"adjuster-monthly-workload-statics"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({AdjusterMonthlyWorkloadStaticsScheduledCronTrigger.class})
    static class AdjusterMonthlyWorkloadStaticsScheduledCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"adjuster-weekly-workload-statics"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({AdjusterWeeklyWorkloadStaticsScheduledCronTrigger.class})
    static class AdjusterWeeklyWorkloadStaticsScheduledCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"processor-daily-workload-statics-beijing"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ProcessorDailyWorkloadStaticsScheduledBeijingCronTrigger.class})
    static class ProcessorDailyWorkloadStaticsScheduledBeijingCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"processor-monthly-workload-statics-beijing"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ProcessorMonthlyWorkloadStaticsScheduledBeijingCronTrigger.class})
    static class ProcessorMonthlyWorkloadStaticsScheduledBeijingCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"processor-monthly-accumulated-workload-statics-beijing"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ProcessorMonthlyAccumulatedWorkloadStaticsScheduledBeijingCronTrigger.class})
    static class ProcessorMonthlyAccumulatedWorkloadStaticsScheduledBeijingCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"processor-weekly-workload-statics-beijing"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ProcessorWeeklyWorkloadStaticsScheduledBeijingCronTrigger.class})
    static class ProcessorWeeklyWorkloadStaticsScheduledBeijingCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"processor-daily-workload-statics-cst"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ProcessorDailyWorkloadStaticsScheduledCstCronTrigger.class})
    static class ProcessorDailyWorkloadStaticsScheduledCstCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"processor-monthly-workload-statics-cst"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ProcessorMonthlyWorkloadStaticsScheduledCstCronTrigger.class})
    static class ProcessorMonthlyWorkloadStaticsScheduledCstCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"processor-monthly-accumulated-workload-statics-cst"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ProcessorMonthlyAccumulatedWorkloadStaticsScheduledCstCronTrigger.class})
    static class ProcessorMonthlyAccumulatedWorkloadStaticsScheduledCstCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"processor-weekly-workload-statics-cst"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ProcessorWeeklyWorkloadStaticsScheduledCstCronTrigger.class})
    static class ProcessorWeeklyWorkloadStaticsScheduledCstCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"reviewer-daily-workload-statics"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ReviewerDailyWorkloadStaticsScheduledCronTrigger.class})
    static class ReviewerDailyWorkloadStaticsScheduledCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"reviewer-weekly-workload-statics"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ReviewerWeeklyWorkloadStaticsScheduledCronTrigger.class})
    static class ReviewerWeeklyWorkloadStaticsScheduledCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"reviewer-monthly-workload-statics"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ReviewerMonthlyWorkloadStaticsScheduledCronTrigger.class})
    static class ReviewerMonthlyWorkloadStaticsScheduledCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"reviewer-monthly-accumulated-workload-statics"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ReviewerMonthlyAccumulatedWorkloadStaticsScheduledCronTrigger.class})
    static class ReviewerMonthlyAccumulatedWorkloadStaticsScheduledCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"job-completed-scheduled"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({JobCompletedScheduledCronTrigger.class})
    static class JobCompletedScheduledCronTriggerConfig {}

    @Configuration
    @ConditionalOnProperty(
        prefix = "bees360.feature",
        name = {"project-es-deleted-scheduled"},
        havingValue = "true",
        matchIfMissing = true)
    @Import({ProjectEsDeletedScheduledCronTrigger.class})
    static class ProjectEsDeletedScheduledCronTriggerConfig {}

}
