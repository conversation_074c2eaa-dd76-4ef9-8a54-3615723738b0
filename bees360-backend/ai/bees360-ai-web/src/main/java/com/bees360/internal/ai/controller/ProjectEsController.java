package com.bees360.internal.ai.controller;

import com.bees360.auth.CustomTokenReader;
import com.bees360.base.ResponseJson;
import com.bees360.common.protobuf.ProtoBeanUtils;
import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.entity.enums.ProjectReportRecordEnum;
import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.common.exceptions.ServiceMessageException;
import com.bees360.internal.ai.common.msgcode.MsgCodeManager;
import com.bees360.internal.ai.config.webannotation.ProtoDeleteMapping;
import com.bees360.internal.ai.config.webannotation.ProtoGetMapping;
import com.bees360.internal.ai.config.webannotation.ProtoPutMapping;
import com.bees360.internal.ai.entity.ImageTagDirectoryVo;
import com.bees360.internal.ai.entity.MemberInfo;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectStatusTagNum;
import com.bees360.internal.ai.entity.dto.PageResult;
import com.bees360.internal.ai.entity.enums.DashBoardRoleTagToDoEnum;
import com.bees360.internal.ai.entity.enums.UserAuthEnum;
import com.bees360.internal.ai.entity.vo.ProjectCalQueryVo;
import com.bees360.internal.ai.entity.vo.ProjectEsOptionDict;
import com.bees360.internal.ai.entity.vo.RoleQueryResult;
import com.bees360.internal.ai.exchange.ai2client.DashBoardProto;
import com.bees360.internal.ai.grpc.ProjectServiceOuterClass.UpdateProjectBatch;
import com.bees360.internal.ai.grpc.api.web2ai.Project;
import com.bees360.internal.ai.scheduled.ProjectRoleWorkCountScheduled;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectOptionDictService;
import com.bees360.internal.ai.service.ProjectService;
import com.bees360.internal.ai.service.ProjectStatusService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.internal.ai.service.converter.ProjectEsConverter;
import com.bees360.internal.ai.service.entity.query.ProjectEsQueryParam;
import com.bees360.internal.ai.service.scheduled.ProjectEsDeletedScheduled;
import com.bees360.internal.ai.util.date.DateUtil;
import com.bees360.pipeline.PipelineHelper;
import com.bees360.project.claim.ProjectCatastropheManager;
import com.bees360.project.config.ProjectHttpSecurityConfig;
import com.bees360.report.service.ImageTagDictService;
import com.bees360.report.service.ProjectReportFileService;
import com.bees360.user.User;
import com.bees360.util.Iterables;
import com.google.gson.Gson;
import com.google.protobuf.Message;
import com.google.type.Money;
import jakarta.annotation.Nullable;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/05/06 12:30
 */
@Slf4j
@RestController
@RequestMapping("/es/dashboard")
@Log4j2
public class ProjectEsController {

    @Autowired private ProjectService projectService;

    @Autowired private ProjectEsService projectEsService;
    @Autowired private ProjectReportFileService projectReportFileService;
    @Autowired private ImageTagDictService imageTagDictService;

    @Autowired private ProjectEsDeletedScheduled projectEsDeletedScheduled;

    @Autowired private ProjectRoleWorkCountScheduled projectRoleWorkCountScheduled;

    @Autowired private ProjectOptionDictService projectOptionDictService;

    @Autowired private ProjectStatusService projectStatusService;

    @Autowired private PipelineHelper pipelineHelper;

    @Autowired private ProjectCatastropheManager projectCatastropheManager;

    @Autowired private CustomTokenReader customTokenReader;

    @Autowired private ProjectHttpSecurityConfig.HttpSecurityProperties httpSecurityProperties;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @PostMapping("/list")
    public Project.DashBoardProjectPage getDashBoardProjectList(
            @AuthenticationPrincipal User user,
            @RequestBody DashBoardProto.ProjectEsQueryParam param)
            throws Exception {
        log.info("getDashBoard the project export data. The parameters are {}", param);
        // 存在 long类型默认0被转换为null的情况
        ProjectEsQueryParam paramPojo = ProtoBeanUtils.toPojoBean(ProjectEsQueryParam.class, param);
        handleQueryParam(param, paramPojo);
        paramPojo.setIsClaimSearch(param.getIsClaimSearch());
        // 左侧tag = roleTag
        if (StringUtils.isBlank(paramPojo.getRoleTag())
                || StringUtils.isBlank(paramPojo.getStatusTag())) {
            throw new ServiceException(
                    MsgCodeManager.REQUEST.PARAMETER_INVALID.CODE, "tag is null");
        }
        var projectIds = getProjectIdList(param);
        Optional.ofNullable(projectIds).ifPresent(paramPojo::setProjectIds);

        setAccessStrategyByUserCompany(paramPojo);
        PageResult<ProjectEsModel> pageResult =
                projectEsService.getProjectPageResult(paramPojo, user);
        Project.DashBoardProjectPage.Builder builder = Project.DashBoardProjectPage.newBuilder();
        try {
            ProtoBeanUtils.toProtoBean(builder, pageResult);
        } catch (IOException e) {
            log.error("getDashBoardProjectInfo IOException error:{}", e.getMessage());
        }
        return builder.build();
    }

    @PutMapping("/{projectId:\\d+}/status")
    public void clientReceived(
            @AuthenticationPrincipal User user,
            @PathVariable long projectId,
            @RequestBody DashBoardProto.StatusRequest param) {
        projectStatusService.updateStatus(
                projectId, user, param.getStatus(), System.currentTimeMillis());
    }

    @PostMapping("/count")
    public Message getDashBoardStatusTagNum(
            @AuthenticationPrincipal User user,
            @RequestBody DashBoardProto.ProjectEsQueryParam param)
            throws Exception {
        log.info("getDashBoardStatusTagNum the project export data. The parameters are {}", param);
        ProjectEsQueryParam paramPojo = ProtoBeanUtils.toPojoBean(ProjectEsQueryParam.class, param);
        handleQueryParam(param, paramPojo);
        paramPojo.setIsClaimSearch(param.getIsClaimSearch());
        // 左侧tag = roleTag
        if (!StringUtils.isNotBlank(param.getRoleTag())) {
            throw new ServiceException(
                    MsgCodeManager.REQUEST.PARAMETER_INVALID.CODE, "roleTag is null");
        }
        var projectIds = getProjectIdList(param);
        Optional.ofNullable(projectIds).ifPresent(paramPojo::setProjectIds);
        log.info("getDashBoardStatusTagNum the project export data. The projectIds are {}", projectIds);

        setAccessStrategyByUserCompany(paramPojo);
        ProjectStatusTagNum statusTagNum = projectEsService.getProjectStatusTagNum(paramPojo, user);
        log.debug("getDashBoardStatusTagNum:{}", new Gson().toJson(statusTagNum));
        log.info("getDashBoardStatusTagNum the project export data. The statusTagNum are {}", statusTagNum);
        return ProtoBeanUtils.toProtoMessage(
                DashBoardProto.ProjectStatusTagNumMessage.newBuilder(), statusTagNum);
    }

    @GetMapping("/{projectId:\\d+}/info")
    public Project.ProjectEsModel getDashBoardProjectInfo(
            @AuthenticationPrincipal User user, @PathVariable long projectId) throws Exception {
        ProjectEsModel esModel = projectEsService.getProjectInfo(projectId, user);
        Project.ProjectEsModel.Builder builder = Project.ProjectEsModel.newBuilder();
        try {
            ProtoBeanUtils.toProtoBean(builder, esModel);
        } catch (IOException e) {
            log.error("getDashBoardProjectInfo IOException error:{}", e.getMessage());
        }
        return builder.build();
    }

    @GetMapping("/{projectId:\\d+}/GPI")
    public ResponseJson getPostDashBoardGpi(
            @AuthenticationPrincipal User user, @PathVariable long projectId) throws Exception {
        ResponseJson json = new ResponseJson();
        ProjectEsModel esModel = projectEsService.findProjectByProjectId(projectId);
        json.setData(ProjectEsConverter.toProjectEsGpiVo(esModel));
        return json;
    }

    @GetMapping("/{projectId:\\d+}/insured-info")
    public ResponseJson getInsuredInfo(@PathVariable long projectId) throws Exception {
        ProjectEsModel esModel = projectEsService.findProjectByProjectId(projectId);
        return new ResponseJson(ProjectEsConverter.toProjectInsuredInfoVo(esModel));
    }

    @GetMapping("/{projectId:\\d+}/inspection-info")
    public ResponseJson getInspectionInfo(@PathVariable long projectId) throws Exception {
        ProjectEsModel esModel = projectEsService.findProjectByProjectId(projectId);
        return new ResponseJson(ProjectEsConverter.toProjectInspectionInfoVo(esModel));
    }

    @ProtoGetMapping("/{projectId:\\d+}/member")
    public DashBoardProto.ProjectMembersResult getDashBoardProjectMembers(
            @AuthenticationPrincipal User user, @PathVariable long projectId) throws Exception {
        if (!UserAuthEnum.hasRole(user.getAllAuthority(), UserAuthEnum.ROLE_ADMIN)) {
            throw new ServiceException(MsgCodeManager.ROLE.ROLE_AUTH_ERROR);
        }
        List<MemberInfo> members = projectEsService.getDashBoardProjectMembers(projectId);
        if (CollectionUtils.isEmpty(members)) {
            return null;
        }
        log.info("members:{}", new Gson().toJson(members));
        List<DashBoardProto.MemberInfo> grpcResult =
                members.stream()
                        .map(
                                res -> {
                                    DashBoardProto.MemberInfo.Builder resultBuilder =
                                            DashBoardProto.MemberInfo.newBuilder();
                                    try {
                                        ProtoBeanUtils.toProtoBean(resultBuilder, res);
                                    } catch (IOException e) {
                                        log.error(
                                                "dashBoardList IOException error:{}",
                                                e.getMessage());
                                    }
                                    return resultBuilder.build();
                                })
                        .collect(Collectors.toList());
        return DashBoardProto.ProjectMembersResult.newBuilder()
                .setProjectId(projectId)
                .addAllMembers(grpcResult)
                .build();
    }

    @PutMapping("/{projectId}/member")
    public void assignDashboardProjectMembers(
            @AuthenticationPrincipal User user,
            @PathVariable long projectId,
            @RequestBody DashBoardProto.MemberInfo param)
            throws Exception {
        MemberInfo paramPojo = ProtoBeanUtils.toPojoBean(MemberInfo.class, param);

        boolean result =
                projectEsService.changeDashBoardProjectMembers(user, projectId, paramPojo, false);
        if (!result) {
            throw new ServiceException(MsgCodeManager.PROJECT.PROJECT_MEMBER_CAN_NOT_UPDATED);
        }
    }

    @ProtoPutMapping("/member/batch")
    public void batchAssignDashboardProjectMembers(
            @AuthenticationPrincipal User user,
            @RequestBody DashBoardProto.ProjectMemberBatch param)
            throws Exception {
        for (long projectId : param.getProjectIdsList()) {
            assignDashboardProjectMembers(user, projectId, param.getMember());
        }
    }

    @ProtoDeleteMapping("/{projectId:\\d+}/member")
    public void removeDashboardMembers(
            @AuthenticationPrincipal User user,
            @PathVariable long projectId,
            @RequestBody DashBoardProto.MemberInfo param)
            throws Exception {
        MemberInfo paramPojo = ProtoBeanUtils.toPojoBean(MemberInfo.class, param);
        boolean result =
                projectEsService.changeDashBoardProjectMembers(user, projectId, paramPojo, true);
        if (!result) {
            throw new ServiceException(MsgCodeManager.PROJECT.PROJECT_MEMBER_CAN_NOT_UPDATED);
        }
    }

    @ProtoPutMapping("/{projectId}/hover")
    public void updateProjectHoverStatus(
            @AuthenticationPrincipal User user,
            @PathVariable long projectId,
            @RequestBody DashBoardProto.StatusRequest param)
            throws Exception {
        if (bees360FeatureSwitch.isEnableSyncFirebaseHoverStatus() && !projectEsService.isSubscribeToHover(projectId)) {
            throw new ServiceException(MsgCodeManager.PROJECT.PROJECT_IS_NOT_ASSOCIATED_WITH_HOVER);
        }
        projectEsService.updateReportStatus(
                user, projectId, param.getStatus(), ProjectReportRecordEnum.HOVER.getType());
    }

    @ProtoPutMapping("/{projectId}/plnar")
    public void updateProjectPlnarStatus(
            @AuthenticationPrincipal User user,
            @PathVariable long projectId,
            @RequestBody DashBoardProto.StatusRequest param)
            throws Exception {
        projectEsService.updateReportStatus(
                user, projectId, param.getStatus(), ProjectReportRecordEnum.PLNAR.getType());
    }

    @ProtoPutMapping("/{projectId}/project-tags")
    public void updateProjectTags(
            @AuthenticationPrincipal User user,
            @PathVariable long projectId,
            @RequestBody DashBoardProto.ProjectTagsRequest param) {
        boolean isDelete = param.getIsDelete();
        if (isDelete) {
            projectEsService.updateProjectTags(user, projectId, new ArrayList<>());
        } else {
            projectEsService.updateProjectTags(user, projectId, param.getProjectTagList());
        }
    }

    @GetMapping("/options")
    public ResponseJson getOptionDict() {
        var begin = System.currentTimeMillis();
        ProjectEsOptionDict dict = projectOptionDictService.getOptionDict();
        var end = System.currentTimeMillis();
        log.debug("getOptionDict Time consumes {} millis", end - begin);

        // imageTag字典存在report模块数据库里
        var begin2 = System.currentTimeMillis();
        ImageTagDirectoryVo imageTag = imageTagDictService.getImageTagDictVo();
        var end2 = System.currentTimeMillis();
        log.debug("getImageTagDictVo Time consumes {} millis", end2 - begin2);
        dict.setCategory(imageTag.getCategory());
        dict.setObject(imageTag.getObject());
        dict.setLocation(imageTag.getLocation());
        dict.setScope(imageTag.getScope());
        dict.setDirection(imageTag.getDirection());
        dict.setAnnotation(imageTag.getAnnotation());
        dict.setReportTags(imageTag.getReport());
        dict.setOrientationTags(imageTag.getOrientation());
        dict.setNumberTags(imageTag.getNumber());
        dict.setFloorLevelTags(imageTag.getFloorLevel());

        return new ResponseJson(dict);
    }

    @GetMapping("/{projectId:\\d+}/quiz")
    public ResponseJson listProjectQuiz(@PathVariable long projectId) throws Exception {
        ProjectEsModel esModel = projectEsService.findProjectByProjectId(projectId);
        if (Objects.isNull(esModel)) {
            return new ResponseJson();
        }
        return new ResponseJson(esModel.getProjectQuiz());
    }

    @GetMapping("/close-project")
    public void cleanClosedProject() throws Exception {
        projectEsDeletedScheduled.softDeleteProjectEsData();
    }

    @PostMapping("/query")
    public DashBoardProto.ProjectCalResult countAdjusterQuery(
            @AuthenticationPrincipal User user,
            @RequestBody DashBoardProto.ProjectEsQueryParam param)
            throws Exception {
        log.info("Query the project export data. The parameters are {}", param);
        // 存在 long类型默认0被转换为null的情况
        ProjectEsQueryParam paramPojo = ProtoBeanUtils.toPojoBean(ProjectEsQueryParam.class, param);
        if (paramPojo.isSearchDaysOld()) {
            // 在无法区分时间跟daysOld的0值转换查询的情况，先以这种形式实现，后面有更好的转换方式，可以修改这里;
            paramPojo.setDaysOldStart(param.getDaysOldStart());
            paramPojo.setDaysOldEnd(param.getDaysOldEnd());
        }
        // 左侧tag = roleTag
        if (StringUtils.isAnyBlank(paramPojo.getRoleTag(), paramPojo.getStatusTag())) {
            throw new ServiceException(
                    MsgCodeManager.REQUEST.PARAMETER_INVALID.CODE, "tag is null");
        }
        DashBoardRoleTagToDoEnum toDoEnum =
                DashBoardRoleTagToDoEnum.getByRoleTag(Optional.ofNullable(param.getRoleTag()));
        if (!UserAuthEnum.hasRole(user.getAllAuthority(), UserAuthEnum.ROLE_ADMIN)
                || !StringUtils.equals(
                        toDoEnum.getRoleTag(), DashBoardRoleTagToDoEnum.ADMIN_TODO.getRoleTag())) {
            throw new ServiceException(
                    MsgCodeManager.REQUEST.PARAMETER_INVALID.CODE, "user is not admin");
        }
        paramPojo.setPageSize(20000);
        var projectIds = getProjectIdList(param);
        log.info(
                "Query the project export data. The project quantity is {}",
                projectIds == null ? 0 : projectIds.size());
        Optional.ofNullable(projectIds).ifPresent(paramPojo::setProjectIds);

        setAccessStrategyByUserCompany(paramPojo);
        List<ProjectCalQueryVo> queryVo = projectEsService.queryCalExportResult(paramPojo);
        DashBoardProto.ProjectCalResult.Builder builder =
                DashBoardProto.ProjectCalResult.newBuilder();
        for (var item: queryVo) {
            var itemBuilder = DashBoardProto.ProjectCalQueryVo.newBuilder();
            ProtoBeanUtils.toProtoBean(itemBuilder, item);
            setEstimateTotalPay(item, itemBuilder);
            builder.addItems(itemBuilder);
        }
        return builder.build();
    }

    private void setEstimateTotalPay(ProjectCalQueryVo item, DashBoardProto.ProjectCalQueryVo.Builder itemBuilder) {
        var estimateTotal = Optional.ofNullable(item).map(ProjectCalQueryVo::getEstimateTotalMoney).orElse(null);
        if (estimateTotal == null) {
            return;
        }
        int units = estimateTotal.intValue();
        var nanos = estimateTotal.remainder(BigDecimal.ONE).movePointRight(estimateTotal.scale());
        var money = Money.newBuilder()
            .setUnits(units)
            .setNanos(nanos.intValue())
            .build();
        itemBuilder.setEstimateTotalPay(money);
    }

    @GetMapping("/role-query")
    public ResponseJson countAdjusterQuery(
            String startDate, String endDate, String role, boolean isClaim) throws Exception {
        DateTimeFormatter dateTimeFormatter =
                DateTimeFormatter.ofPattern(DateUtil.DATE_TIME_FORMAT);

        Long statusStartTime =
                ZonedDateTime.of(
                                LocalDateTime.parse(startDate, dateTimeFormatter),
                                ZoneId.of(AmericaTimeZone.US_CENTRAL))
                        .toInstant()
                        .toEpochMilli();
        Long statusEndTime =
                ZonedDateTime.of(
                                LocalDateTime.parse(endDate, dateTimeFormatter),
                                ZoneId.of(AmericaTimeZone.US_CENTRAL))
                        .toInstant()
                        .toEpochMilli();

        List<RoleQueryResult> results =
                projectEsService.queryRoleCountProject(
                        statusStartTime, statusEndTime, role, isClaim);

        return new ResponseJson(results);
    }

    @GetMapping("/role-schedule")
    public ResponseJson tempStartRoleQuery() throws Exception {
        projectRoleWorkCountScheduled.calProjectWorkCount();
        return new ResponseJson();
    }

    /**
     * 根据token的用户公司id配置对应的case访问权限控制
     */
    private void setAccessStrategyByUserCompany(ProjectEsQueryParam param) {
        var companyId = customTokenReader.getByKey("company_id");
        var companyStrategy = httpSecurityProperties.getCompanyStrategy();
        if (StringUtils.isNoneEmpty(companyId)
                && Long.parseLong(companyId) == companyStrategy.getRealms9()) {
            param.setExcludeCompanyIds(companyStrategy.getForbiddenCompanyList());
        }
    }

    @Nullable
    private List<Long> getProjectIdList(DashBoardProto.ProjectEsQueryParam param) {
        List<Long> projectIdList = null;
        if (StringUtils.isNotBlank(param.getCatSerialNumber())) {
            projectIdList =  Iterables.toList(projectCatastropheManager.findProjectIdBySerialNum(param.getCatSerialNumber()))
                                    .stream().map(Long::parseLong).collect(Collectors.toList());
        }
        if (param.getPipelineTaskQueryCount() > 0) {
            var projects = Iterables.toList(pipelineHelper.getProjectId(param.getPipelineTaskQueryList()));
            projectIdList = Optional.ofNullable(projectIdList).map(list ->list.stream()
                .filter(projects::contains)
                .collect(Collectors.toList()))
                .orElse(projects);
        }
        if(CollectionUtils.isNotEmpty(param.getProjectIdsList())) {
            if (projectIdList != null) {
                projectIdList = new ArrayList<>(CollectionUtils.intersection(projectIdList, param.getProjectIdsList()));
            } else {
                projectIdList = new ArrayList<>(param.getProjectIdsList());
            }
        }
        return projectIdList;
    }

    @PutMapping("projects")
    public void updateExternalAdjusterInfo(@AuthenticationPrincipal User user,
            @RequestBody UpdateProjectBatch updateProjectBatch) {
        for (var item: updateProjectBatch.getItemList()) {
            if (StringUtils.isEmpty(item.getClaimNumber())) {
                throw new ServiceMessageException(
                    MsgCodeManager.REQUEST.PARAMETER_INVALID.CODE, "claim number must not be empty.");
            }
        }
        for (var item: updateProjectBatch.getItemList()) {
            projectEsService.updatePartial(item, user.getId());
        }
    }

    /**
     * 处理请求体中，0和null的的区别
     */
    private void handleQueryParam(
        DashBoardProto.ProjectEsQueryParam param, ProjectEsQueryParam paramPojo) {
        if (paramPojo.isSearchDaysOld()) {
            // 在无法区分时间跟daysOld的0值转换查询的情况，先以这种形式实现，后面有更好的转换方式，可以修改这里;
            paramPojo.setDaysOldStart(param.getDaysOldStart());
            paramPojo.setDaysOldEnd(param.getDaysOldEnd());
        }
        if (param.hasDroneImageCountMax()) {
            paramPojo.setDroneImageCountMax(param.getDroneImageCountMax().getValue());
        }
        if (param.hasDroneImageCountMin()) {
            paramPojo.setDroneImageCountMin(param.getDroneImageCountMin().getValue());
        }
        if (param.hasMobileImageCountMax()) {
            paramPojo.setMobileImageCountMax(param.getMobileImageCountMax().getValue());
        }
        if (param.hasMobileImageCountMin()) {
            paramPojo.setMobileImageCountMin(param.getMobileImageCountMin().getValue());
        }
    }
}
