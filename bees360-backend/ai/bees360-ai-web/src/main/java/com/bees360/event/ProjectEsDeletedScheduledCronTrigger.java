package com.bees360.event;

import com.bees360.event.registry.CronTriggerEverySundayAt0AmCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.service.scheduled.ProjectEsDeletedScheduled;
import java.io.IOException;

/**
 * 该类是一个定时任务监听器，用于每周日0点触发软删除项目ES数据的操作。
 */
public class ProjectEsDeletedScheduledCronTrigger extends AbstractNamedEventListener<CronTriggerEverySundayAt0AmCst> {

    private final ProjectEsDeletedScheduled projectEsDeletedScheduled;

    public ProjectEsDeletedScheduledCronTrigger(ProjectEsDeletedScheduled projectEsDeletedScheduled) {
        this.projectEsDeletedScheduled = projectEsDeletedScheduled;
    }

    @Override
    public void handle(CronTriggerEverySundayAt0AmCst cronTriggerEverySundayAt0AmCst) throws IOException {
        projectEsDeletedScheduled.softDeleteProjectEsData();
    }
}
