package com.bees360.internal.ai.config;

import com.bees360.pipeline.PipelineHelper;
import com.bees360.pipeline.PipelineService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PipelineConfig {
    @Bean
    public PipelineHelper pipelineHelper(PipelineService pipelineService) {
        return new PipelineHelper(pipelineService);
    }
}
