package com.bees360.internal.ai.config.converter;

import org.springframework.http.converter.protobuf.ProtobufHttpMessageConverter;

/**
 * <AUTHOR>
 * @date 2019/11/01 19:01
 */
public class CustomProtobufHttpMessageConverter extends ProtobufHttpMessageConverter {

    @Override
    protected boolean supports(Class<?> clazz) {
        return super.supports(clazz) || isVoid(clazz);
    }

    private boolean isVoid(Class<?> clazz) {
        return void.class.isAssignableFrom(clazz) || Void.class.isAssignableFrom(clazz);
    }
}
