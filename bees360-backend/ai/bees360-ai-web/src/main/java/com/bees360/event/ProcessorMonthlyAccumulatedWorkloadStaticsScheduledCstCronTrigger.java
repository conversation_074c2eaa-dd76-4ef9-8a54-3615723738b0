package com.bees360.event;

import com.bees360.event.registry.CronTriggerEveryMondayAt2AmCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.ProcessorWorkloadStatisticsScheduledCst;

import java.io.IOException;

/**
 * 该类是一个定时任务处理器，每周一凌晨2点触发执行处理器月度累计工作负载统计任务。
 */
public class ProcessorMonthlyAccumulatedWorkloadStaticsScheduledCstCronTrigger
        extends AbstractNamedEventListener<CronTriggerEveryMondayAt2AmCst> {

    private final ProcessorWorkloadStatisticsScheduledCst processorWorkloadStatisticsScheduledCst;

    public ProcessorMonthlyAccumulatedWorkloadStaticsScheduledCstCronTrigger(
            ProcessorWorkloadStatisticsScheduledCst processorWorkloadStatisticsScheduledCst) {
        this.processorWorkloadStatisticsScheduledCst = processorWorkloadStatisticsScheduledCst;
    }

    @Override
    public void handle(CronTriggerEveryMondayAt2AmCst cronTriggerEveryMondayAt2AmBeijing)
            throws IOException {
        processorWorkloadStatisticsScheduledCst.sendProcessorMonthlyAccumulatedWorkloadStatics();
    }
}
