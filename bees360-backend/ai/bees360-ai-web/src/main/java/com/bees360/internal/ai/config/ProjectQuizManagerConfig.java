package com.bees360.internal.ai.config;

import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.impl.EsProjectQuizManager;
import com.bees360.project.GrpcProjectQuizService;
import com.bees360.project.quiz.ProjectQuizManager;
import net.devh.boot.grpc.server.service.GrpcService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ProjectQuizManagerConfig {

	@Bean
	public ProjectQuizManager projectQuizManager(ProjectEsService projectEsService) {
		return new EsProjectQuizManager(projectEsService);
	}

	@GrpcService
	public GrpcProjectQuizService grpcProjectQuizService(ProjectQuizManager projectQuizManager) {
		return new GrpcProjectQuizService(projectQuizManager);
	}
}
