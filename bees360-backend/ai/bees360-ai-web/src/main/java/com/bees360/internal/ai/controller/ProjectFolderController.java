package com.bees360.internal.ai.controller;

import com.bees360.internal.ai.config.webannotation.ProtoDeleteMapping;
import com.bees360.internal.ai.exchange.ai2client.DashBoardProto;
import com.bees360.internal.ai.service.ProjectFolderService;
import com.bees360.report.config.ProtoPostMapping;
import com.bees360.user.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/04/21 18:27
 */
@Slf4j
@RestController
@RequestMapping("/projects/folder")
public class ProjectFolderController {

    @Autowired
    private ProjectFolderService projectFolderService;

    @ProtoPostMapping()
    public void addProjectFolder(@AuthenticationPrincipal User user,
        @RequestBody DashBoardProto.ProjectFolderRequest request) {
        if (CollectionUtils.isEmpty(request.getProjectIdsList()) || StringUtils.isBlank(request.getType())) {
            throw new IllegalArgumentException("projectIds or type is empty.");
        }
        request.getProjectIdsList().forEach(o -> {
            projectFolderService.addProjectFolder(user, o, request.getType());
        });
    }

    @ProtoDeleteMapping()
    public void delete(@AuthenticationPrincipal User user,
        @RequestBody DashBoardProto.ProjectFolderRequest request) {
        if (CollectionUtils.isEmpty(request.getProjectIdsList()) || StringUtils.isBlank(request.getType())) {
            throw new IllegalArgumentException("projectIds or type is empty.");
        }
        request.getProjectIdsList().forEach(o -> {
            projectFolderService.deleteById(user, o, request.getType());
        });
    }


}
