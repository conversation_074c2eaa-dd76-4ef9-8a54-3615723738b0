package com.bees360.internal.ai.controller;

import com.bees360.internal.ai.exchange.ai2client.ProjectTagProto;
import com.bees360.internal.ai.service.ProjectTagService;
import com.bees360.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/02/24 10:58
 */
@Slf4j
@RestController
@RequestMapping("/projects/tag")
public class ProjectTagController {

    @Autowired
    private ProjectTagService projectTagService;

    @PostMapping("/{projectId:\\d+}")
    public void addProjectTag(@AuthenticationPrincipal User user, @PathVariable long projectId,
                              @RequestBody ProjectTagProto.ProjectTag tag) {
        if (tag.getIsDeleted()) {
            projectTagService.deleteProjectTag(user, projectId);
        } else {
            projectTagService.insertOrUpdateProjectTag(user, projectId, tag.getTagsList());
        }
    }


}
