package com.bees360.internal.ai.config;

import com.bees360.customer.CustomerProvider;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;

import static com.bees360.internal.ai.entity.enums.JobTypeEnum.LOCAL_JOB_NAME_PREFIX;

/**
 * <AUTHOR>
 * @since 2022/11/28
 */
@Configuration
public class ThreeDJobNamePrefixConfig {

    @ConfigurationProperties(prefix = "three-d.local.company-key")
    @Bean
    Set<String> localThreeDCompanyKeys() {
        return new HashSet<>();
    }

    @Bean
    public Function<String, String> threeDJobNamePrefixProvider(
            Set<String> localThreeDCompanyKeys,
            ProjectIIManager projectIIManager,
            CustomerProvider customerProvider) {
        return projectId -> {
            var contract =
                    Optional.ofNullable(projectIIManager.findById(projectId))
                            .map(ProjectII::getContract)
                            .orElse(null);
            if (contract != null && contract.getInsuredBy() != null) {
                var customer = customerProvider.findById(contract.getInsuredBy().getId());
                if (localThreeDCompanyKeys.contains(customer.getCompanyKey())) {
                    return LOCAL_JOB_NAME_PREFIX;
                }
            }
            return StringUtils.EMPTY;
        };
    }
}
