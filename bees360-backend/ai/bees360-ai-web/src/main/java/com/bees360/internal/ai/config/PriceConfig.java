package com.bees360.internal.ai.config;

import com.bees360.internal.ai.service.PriceStrategy;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/06/01
 */
@Configuration
public class PriceConfig {

    @Bean
    @ConfigurationProperties(prefix = "price.strategy.area-range")
    public List<InvoiceAreaRangePriceConfig> invoiceRoofAreaRangeConfig() {
        return new ArrayList<>();
    }

    @Bean
    public List<PriceStrategy> areaRangePriceStrategy(
            List<InvoiceAreaRangePriceConfig> invoiceRoofAreaRangeConfig) {
        return invoiceRoofAreaRangeConfig.stream()
                .map(
                        roofAreaRange ->
                                new AreaRangePriceStrategy(roofAreaRange))
                .collect(Collectors.toList());
    }

    @Bean
    public Function<String, PriceStrategy> priceStrategyProvider(
            List<List<PriceStrategy>> priceStrategies) {
        var priceStrategyMap =
                priceStrategies.stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toMap(PriceStrategy::getName, Function.identity()));
        return priceStrategyMap::get;
    }

    @Bean
    public Function<Long, PriceStrategy> companyInvoicePriceStrategyProvider(
            Map<Long, BaseConfig.CompanyConfig> companyConfig,
            Function<String, PriceStrategy> priceStrategyProvider) {
        return companyId -> {
            var companyInvoicePriceStrategy = new ArrayList<PriceStrategy>();
            if (companyId == null
                    || !companyConfig.containsKey(companyId)
                    || companyConfig.get(companyId).getPrice() == null) {
                return new CompositePriceStrategy(companyInvoicePriceStrategy);
            }
            companyConfig
                    .get(companyId)
                    .getPrice()
                    .getStrategy()
                    .forEach(
                            strategyName -> {
                                PriceStrategy priceStrategy =
                                        priceStrategyProvider.apply(strategyName);
                                if (priceStrategy != null) {
                                    companyInvoicePriceStrategy.add(priceStrategy);
                                }
                            });
            return new CompositePriceStrategy(companyInvoicePriceStrategy);
        };
    }

    @Data
    static class InvoiceAreaRangePriceConfig {

        private String name;

        private Set<Integer> projectTypes;

        private List<InvoiceAreaRangePriceItem> areaRange;
    }

    @Setter
    @Getter
    static class InvoiceAreaRangePriceItem {

        private double minArea;

        private double maxArea;

        private BigDecimal price;

        public double getMaxArea() {
            return maxArea == 0 ? Double.MAX_VALUE : maxArea;
        }
    }
}

class CompositePriceStrategy implements PriceStrategy {

    private final List<PriceStrategy> priceStrategies;

    CompositePriceStrategy(List<PriceStrategy> priceStrategies) {
        this.priceStrategies = priceStrategies;
    }

    @Override
    public String getName() {
        return "composite-"
                + priceStrategies.stream()
                        .map(PriceStrategy::getName)
                        .collect(Collectors.joining(","));
    }

    @Override
    public BigDecimal calculatePrice(long projectId) {
        if (CollectionUtils.isEmpty(priceStrategies)) {
            return null;
        }
        BigDecimal price = null;
        for (PriceStrategy priceStrategy : priceStrategies) {
            price = priceStrategy.calculatePrice(projectId);
            if (price != null) {
                break;
            }
        }
        return price;
    }
}

class AreaRangePriceStrategy implements PriceStrategy {

    private final PriceConfig.InvoiceAreaRangePriceConfig invoiceRoofAreaRangeConfig;

    AreaRangePriceStrategy(
            PriceConfig.InvoiceAreaRangePriceConfig invoiceRoofAreaRangeConfig) {
        this.invoiceRoofAreaRangeConfig = invoiceRoofAreaRangeConfig;
    }

    @Override
    public String getName() {
        return invoiceRoofAreaRangeConfig.getName();
    }

    @Override
    public BigDecimal calculatePrice(long projectId) {
        // 无3D信息无法计算area，直接返回null
        return null;
    }
}
