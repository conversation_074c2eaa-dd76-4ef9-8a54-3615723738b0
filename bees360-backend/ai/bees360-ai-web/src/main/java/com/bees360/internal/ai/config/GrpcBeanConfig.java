package com.bees360.internal.ai.config;

import com.bees360.common.grpc.GrpcConfig;
import com.bees360.internal.ai.common.grpc.AiGrpcClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2019/07/13
 */
@Configuration
public class GrpcBeanConfig {
    @Bean
    @ConfigurationProperties(prefix = "com.bees360.grpc.server.web")
    public GrpcConfig webGrpcConfig() {
        return new GrpcConfig();
    }

    @Bean
    public AiGrpcClient webGrpcClient() {
        return new AiGrpcClient(webGrpcConfig());
    }

}
