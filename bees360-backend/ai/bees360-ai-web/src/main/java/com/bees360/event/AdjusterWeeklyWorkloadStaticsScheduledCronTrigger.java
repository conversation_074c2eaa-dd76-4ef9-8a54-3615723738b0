package com.bees360.event;

import com.bees360.event.registry.CronTriggerEveryMondayAt2AmCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.AdjusterWorkloadStatisticsScheduled;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 每周一凌晨2点触发调整员工作量统计任务的定时调度类
 */
@Log4j2
public class AdjusterWeeklyWorkloadStaticsScheduledCronTrigger extends AbstractNamedEventListener<CronTriggerEveryMondayAt2AmCst> {

    private final AdjusterWorkloadStatisticsScheduled adjusterWorkloadStatisticsScheduled;

    public AdjusterWeeklyWorkloadStaticsScheduledCronTrigger(AdjusterWorkloadStatisticsScheduled adjusterWorkloadStatisticsScheduled) {
        this.adjusterWorkloadStatisticsScheduled = adjusterWorkloadStatisticsScheduled;
        log.info(
                "Created {}(adjusterWorkloadStatisticsScheduled={})",
                this,
                adjusterWorkloadStatisticsScheduled);
    }

    @Override
    public void handle(CronTriggerEveryMondayAt2AmCst cronTriggerEveryMondayAt2AmCst) throws IOException {
        adjusterWorkloadStatisticsScheduled.sendAdjusterWeeklyWorkloadStatics();
    }
}
