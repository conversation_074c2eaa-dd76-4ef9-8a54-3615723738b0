package com.bees360.event;

import com.bees360.event.registry.CronTriggerEveryMondayAt2AmCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.ReviewerWorkloadStatisticsScheduled;
import java.io.IOException;

/**
 * 该类是一个定时任务触发器，每周一凌晨2点触发执行评审员工作负载统计数据的发送任务。
 */
public class ReviewerWeeklyWorkloadStaticsScheduledCronTrigger extends AbstractNamedEventListener<CronTriggerEveryMondayAt2AmCst> {

    private final ReviewerWorkloadStatisticsScheduled reviewerWorkloadStatisticsScheduled;

    public ReviewerWeeklyWorkloadStaticsScheduledCronTrigger(ReviewerWorkloadStatisticsScheduled reviewerWorkloadStatisticsScheduled) {
        this.reviewerWorkloadStatisticsScheduled = reviewerWorkloadStatisticsScheduled;
    }

    @Override
    public void handle(CronTriggerEveryMondayAt2AmCst cronTriggerEveryMondayAt2AmCst) throws IOException {
        reviewerWorkloadStatisticsScheduled.sendReviewerWeeklyWorkloadStatics();
    }
}
