package com.bees360.internal.ai.config;

import com.bees360.image.ImageGroupProvider;
import com.bees360.image.config.GrpcImageAnnotationClientConfig;
import com.bees360.image.config.GrpcImageApiClientConfig;
import com.bees360.image.config.GrpcImageClientConfig;
import com.bees360.image.config.GrpcImageGroupClientConfig;
import com.bees360.image.config.GrpcImageNoteClientConfig;
import com.bees360.image.config.GrpcImageTagDictClientConfig;
import com.bees360.image.config.GrpcImageTagClientConfig;
import com.bees360.project.image.DefaultProjectImageProvider;
import com.bees360.project.image.ProjectImageProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({
    GrpcImageTagDictClientConfig.class,
    GrpcImageTagClientConfig.class,
    GrpcImageClientConfig.class,
    GrpcImageGroupClientConfig.class,
    GrpcImageAnnotationClientConfig.class,
    GrpcImageNoteClientConfig.class,
    GrpcImageApiClientConfig.class,
})
public class ImageManagerConfig {

    @Bean
    public ProjectImageProvider projectImageProvider(ImageGroupProvider imageGroupProvider) {
        return new DefaultProjectImageProvider(imageGroupProvider);
    }
}
