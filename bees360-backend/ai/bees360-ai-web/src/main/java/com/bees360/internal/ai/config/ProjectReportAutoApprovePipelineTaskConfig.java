package com.bees360.internal.ai.config;

import com.bees360.event.autoconfig.EnableEventAutoRegister;
import com.bees360.internal.ai.listener.AutoApproveReportOnPipelineTaskReady;
import com.bees360.internal.ai.service.config.SystemUserConfig;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.ReportManager;

import com.bees360.report.service.ProjectReportFileService;
import lombok.Getter;
import lombok.Setter;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Configuration
@EnableEventAutoRegister
@EnableConfigurationProperties
@ConditionalOnProperty(
        prefix = "pipeline",
        name = "enable-report-auto-approve",
        havingValue = "true")
@Import({SystemUserConfig.class})
public class ProjectReportAutoApprovePipelineTaskConfig {

    private static final SpelExpressionParser SPEL_EXPRESSION_PARSER = new SpelExpressionParser();

    @Getter
    @Setter
    @Configuration
    @ConfigurationProperties(prefix = "pipeline.report-auto-approve")
    static class PipelineTaskAutoApproveProperties {
        private List<TaskApproveConditionProperties> taskApproveConditions = new ArrayList<>();

        @Getter
        @Setter
        static class TaskApproveConditionProperties {
            /** pipeline task key */
            private String taskKey;

            /** report type key {@link com.bees360.report.ReportTypeEnum} */
            private String reportType;

            /**
             * Expression that determines whether a report should be automatically approved. </br>
             * When set to "true", the report is always approved automatically.
             */
            private String condition =
                    "(#context.insuredBy.isEmpty() or"
                            + " #context.insuredBy.contains(#project.contract.insuredBy.companyKey))"
                            + " and(#context.processedBy.isEmpty() or"
                            + " #context.processedBy.contains(#project.contract.processedBy.companyKey))"
                            + " and(#context.serviceType.isEmpty() or"
                            + " #context.serviceType.contains(#project.serviceType.name()))"
                            + " and(#context.isRenewal == null or #context.isRenewal =="
                            + " #project.policy.isRenewal())";

            private ConditionContext context;

            @Getter
            @Setter
            static class ConditionContext {
                private List<String> insuredBy = new ArrayList<>();

                private List<String> processedBy = new ArrayList<>();

                private List<String> serviceType = new ArrayList<>();

                private Boolean isRenewal = null;
            }
        }
    }

    @Bean
    List<AutoApproveReportOnPipelineTaskReady> autoApproveReportOnPipelineTaskReady(
            PipelineTaskAutoApproveProperties autoApproveProperties,
            ProjectIIRepository projectIIRepository,
            ProjectReportProvider projectReportProvider,
            ReportManager reportManager,
            ProjectReportFileService projectReportFileService,
            @Qualifier("systemUserSupplier") Supplier<String> systemUserSupplier) {
        var properties = autoApproveProperties.getTaskApproveConditions();
        return properties.stream()
                .map(
                        prop -> {
                            Predicate<String> projectReportApprovePredicate =
                                    projectId -> {
                                        var project = projectIIRepository.findById(projectId);
                                        Map<String, Object> variables =
                                                Map.of(
                                                        "project",
                                                        project,
                                                        "context",
                                                        prop.getContext());

                                        return evaluateCondition(prop.getCondition(), variables);
                                    };
                            return new AutoApproveReportOnPipelineTaskReady(
                                    prop.getTaskKey(),
                                    prop.getReportType(),
                                    projectReportApprovePredicate,
                                    projectReportProvider,
                                    reportManager,
                                    projectReportFileService,
                                    systemUserSupplier);
                        })
                .collect(Collectors.toList());
    }

    private boolean evaluateCondition(String condition, Map<String, Object> variables) {
        var context = new StandardEvaluationContext();
        context.setVariables(variables);
        var expression = SPEL_EXPRESSION_PARSER.parseExpression(condition);
        return Boolean.TRUE.equals(expression.getValue(context, Boolean.class));
    }
}
