package com.bees360.event;

import com.bees360.event.registry.CronTriggerMonthly1stAt2AmCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.AdjusterWorkloadStatisticsScheduled;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 该类是一个定时任务触发器，每月1日凌晨2点触发并执行调整员月度工作量统计的发送操作。
 */
@Log4j2
public class AdjusterMonthlyWorkloadStaticsScheduledCronTrigger extends AbstractNamedEventListener<CronTriggerMonthly1stAt2AmCst> {

    private final AdjusterWorkloadStatisticsScheduled adjusterWorkloadStatisticsScheduled;

    public AdjusterMonthlyWorkloadStaticsScheduledCronTrigger(AdjusterWorkloadStatisticsScheduled adjusterWorkloadStatisticsScheduled) {
        this.adjusterWorkloadStatisticsScheduled = adjusterWorkloadStatisticsScheduled;
        log.info(
                "Created {}(adjusterWorkloadStatisticsScheduled={})",
                this,
                adjusterWorkloadStatisticsScheduled);
    }

    @Override
    public void handle(CronTriggerMonthly1stAt2AmCst cronTriggerMonthly1stAt2AmCst) throws IOException {
        adjusterWorkloadStatisticsScheduled.sendAdjusterMonthlyWorkloadStatics();
    }
}
