package com.bees360.event;

import com.bees360.event.registry.CronTriggerMonthly1stAt2AmCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.ProcessorWorkloadStatisticsScheduledCst;

import java.io.IOException;

/**
 * 每月1日凌晨2点CST时区触发处理器工作负载统计任务
 */
public class ProcessorMonthlyWorkloadStaticsScheduledCstCronTrigger extends AbstractNamedEventListener<CronTriggerMonthly1stAt2AmCst> {

    private final ProcessorWorkloadStatisticsScheduledCst processorWorkloadStatisticsScheduledCst;

    public ProcessorMonthlyWorkloadStaticsScheduledCstCronTrigger(ProcessorWorkloadStatisticsScheduledCst processorWorkloadStatisticsScheduledCst) {
        this.processorWorkloadStatisticsScheduledCst = processorWorkloadStatisticsScheduledCst;
    }

    @Override
    public void handle(CronTriggerMonthly1stAt2AmCst cronTriggerMonthly1stAt2AmBeijing) throws IOException {
        processorWorkloadStatisticsScheduledCst.sendProcessorMonthlyWorkloadStatics();
    }
}
