package com.bees360.internal.ai.scheduled;

import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.ProjectOptionDictTypeEnum;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.entity.query.ProjectEsQueryParam;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import com.google.common.collect.Maps;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.bees360.internal.ai.service.impl.CacheMemberService.REMOVED;
import static com.bees360.internal.ai.service.impl.CacheMemberService.STAFF_NAME_SET_PREFIX;

@Log4j2
@Component
public class SearchedNameCacheRefreshScheduled {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ProjectEsService projectEsService;

    @Autowired
    private UserProvider userProvider;

    public void searchNameRefresh() {
        log.info("searchNameRefresh cronTriggerEverySundayAt0AmBeijing");
        doCleanUserNameWithAllProjectArchived(ProjectOptionDictTypeEnum.ADJUSTER);
        doCleanUserNameWithAllProjectArchived(ProjectOptionDictTypeEnum.PILOT);
        doCleanUserNameWithAllProjectArchived(ProjectOptionDictTypeEnum.REVIEWER);
        doCleanUserNameWithAllProjectArchived(ProjectOptionDictTypeEnum.PROCESSOR);
        doCleanUserNameWithAllProjectArchived(ProjectOptionDictTypeEnum.CREATOR);
    }

    private void doCleanUserNameWithAllProjectArchived(ProjectOptionDictTypeEnum roleEnum) {
        String roleName = roleEnum.getType();
        log.info("start to refresh search name group:{}", roleName);
        RMap<String, Byte> map = redissonClient.getMapCache(STAFF_NAME_SET_PREFIX + roleName);
        if (!map.isExists() || map.isEmpty()) {
            log.info("No mapping of group of role:{} is found", roleEnum.name());
            map = redissonClient.getMapCache(STAFF_NAME_SET_PREFIX + roleName);
        }
        Set<String> keySet = map.readAllKeySet();
        Map<String, Byte> removeNameMap = Maps.newHashMap();
        for(String name: keySet) {
            List<? extends User> users = Iterables.toList(userProvider.findUserByName(name));
            // Staff exists, should not be checked
            if (!users.isEmpty()) {
                continue;
            }
            ProjectEsQueryParam param = new ProjectEsQueryParam();
            param.setMemberName(name);
            List<ProjectEsModel> projects = projectEsService.findProjectListByQueryBuild(param);
            if (!projects.isEmpty()) {
                boolean hasProjectNotArchived = projects.stream().anyMatch(project -> project.getProjectStatus() !=
                    AiProjectStatusEnum.PROJECT_ARCHIVED.getCode());
                if (hasProjectNotArchived) {
                    continue;
                }
            }
            removeNameMap.put(name, REMOVED);
            log.info("remove searched name:{} from cache, group:{}", name, roleName);
        }
        map.putAll(removeNameMap);
        log.info("finish refreshing search name group:{}", roleName);
    }
}
