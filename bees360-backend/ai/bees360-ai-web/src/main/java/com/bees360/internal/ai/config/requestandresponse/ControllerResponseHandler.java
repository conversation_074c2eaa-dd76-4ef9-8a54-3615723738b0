package com.bees360.internal.ai.config.requestandresponse;

import com.bees360.internal.ai.Bees360AiServletInitializer;
import com.bees360.internal.ai.exchange.ai2client.ResponseProto;
import com.bees360.internal.ai.util.protobuf.AnyDataPacker;
import com.google.protobuf.GeneratedMessageV3;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@ControllerAdvice(basePackageClasses = Bees360AiServletInitializer.class)
@Slf4j
public class ControllerResponseHandler implements ResponseBodyAdvice<Object> {

	private static final String DEFAULT_SUCCESS_CODE = "0";

	/**
	 * It will supports all return types.
	 */
	@Override
	public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
	    log.debug("converterType: " + converterType.getName());
		return true;
	}

	@Override
	public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
			Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request,
			ServerHttpResponse response) {
        if (isJsonMediaType(selectedContentType)) {
            return body;
	    }
		if(body instanceof ResponseProto.ResponseBody) {
            // 目标protobuf bean格式
			return body;
        } else if(body instanceof ResponseProto.ResponseErrorBody errorBody) {
            if(isJsonMediaType(selectedContentType)) {
                return body;
            }
            return errorBodyToBody(errorBody);
        } else if (body instanceof GeneratedMessageV3 || body == null) {
			// protobuf bean 转为标准 ResponseProto.ResponseBody
			// 这里为成功处理的结果
			HttpStatus httpStatus = HttpStatus.OK;

			ResponseProto.ResponseBody.Builder responseBodyBuilder = ResponseProto.ResponseBody.newBuilder()
				.setCode(DEFAULT_SUCCESS_CODE)
				.setMessage(httpStatus.getReasonPhrase());
			if(body == null) {
				return responseBodyBuilder.build();
			}
			GeneratedMessageV3 result = (GeneratedMessageV3)body;
			return responseBodyBuilder.setData(AnyDataPacker.pack(result)).build();
		}
		// 不是 protobuf bean
		return body;
	}

    private ResponseProto.ResponseBody errorBodyToBody(ResponseProto.ResponseErrorBody errorBody) {
        ResponseProto.ResponseBody body = ResponseProto.ResponseBody.newBuilder()
            .setCode(errorBody.getCode())
            .setMessage(errorBody.getMessage())
            .setData(AnyDataPacker.pack(errorBody.getData()))
            .build();
        return body;
    }

    private boolean isJsonMediaType(MediaType selectedContentType) {
        return MediaType.APPLICATION_JSON.equals(selectedContentType)
            || MediaType.APPLICATION_JSON.equals(selectedContentType);
    }
}
