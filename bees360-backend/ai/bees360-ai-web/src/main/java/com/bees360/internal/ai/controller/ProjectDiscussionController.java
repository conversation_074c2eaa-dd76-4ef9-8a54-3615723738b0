package com.bees360.internal.ai.controller;

import com.bees360.common.protobuf.ProtoBeanUtils;
import com.bees360.internal.ai.common.exceptions.ServiceMessageException;
import com.bees360.internal.ai.common.msgcode.MsgCodeManager;
import com.bees360.internal.ai.config.webannotation.ProtoGetMapping;
import com.bees360.internal.ai.config.webannotation.ProtoPostMapping;
import com.bees360.internal.ai.entity.DiscussionParticipant;
import com.bees360.internal.ai.entity.ProjectDiscuss;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.enums.UserAuthEnum;
import com.bees360.internal.ai.exchange.ai2client.ProjectDiscussionProto;
import com.bees360.internal.ai.service.ProjectDiscussService;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.user.User;
import com.bees360.activity.Message.CommentMessage;
import com.google.protobuf.Message;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/05/06 12:30
 */
@Slf4j
@RestController
@RequestMapping("/projects/discuss")
public class ProjectDiscussionController {

    @Autowired
    private ProjectDiscussService projectDiscussService;

    @Autowired
    private ProjectEsService projectEsService;

    @PostMapping("/{projectId:\\d+}")
    public CommentMessage addProjectDiscussion(@AuthenticationPrincipal User user, @PathVariable long projectId,
                                  @RequestBody ProjectDiscussionProto.ProjectDiscussParam discussMessage) throws IOException {

        List<DiscussionParticipant> discussMember = discussMessage.getParticipantsList()
            .stream()
            .map(o -> new DiscussionParticipant(o.getReferUserId(), UserAuthEnum.roleToAuth(o.getReferGroup())))
            .collect(Collectors.toList());
        if (StringUtils.isBlank(discussMessage.getNote())) {
            throw new ServiceMessageException(MsgCodeManager.REQUEST.PARAMETER_INVALID.CODE, "note is null");
        }
        String commentId = projectDiscussService.addProjectDiscussion(user, projectId, user.getId(), discussMessage.getNote(), discussMember);
        return CommentMessage.newBuilder().setId(commentId).build();
    }

    @ProtoGetMapping("/{projectId:\\d+}")
    public Message getProjectDiscuss(@AuthenticationPrincipal User user, @PathVariable long projectId) throws IOException {
        ProjectEsModel projectEsModel = projectEsService.findProjectByProjectIdCheckExisted(projectId);
        ProjectDiscuss discuss = projectDiscussService.getProjectDiscuss(user, projectId, projectEsModel);
        return ProtoBeanUtils.toProtoMessage(ProjectDiscussionProto.ProjectDiscussMessage.newBuilder(), discuss);

    }



    @ProtoPostMapping("/{projectId:\\d+}/read")
    public void markAsRead(@AuthenticationPrincipal User user, @PathVariable long projectId,
                           @RequestBody ProjectDiscussionProto.DiscussMarkReadParam markReadParam) throws IOException {
        projectDiscussService.markAsRead(projectId, user, markReadParam);
    }



}
