package com.bees360.internal.ai.listener;

import com.bees360.api.InvalidArgumentException;
import com.bees360.image.ImageApiProvider;
import com.bees360.image.ImageApiQuery;
import com.bees360.image.ImageTagCategoryEnum;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.ImageTagManager;
import com.bees360.image.ImageTagRequest;
import com.bees360.image.tag.ImageTagGroupType;
import com.bees360.image.tag.ImageTagDictProvider;
import com.bees360.internal.ai.config.HostaReportConfig;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.entity.ImageTagDict;
import com.bees360.internal.ai.entity.consts.AiBotUser;
import com.bees360.internal.ai.entity.dto.ImageRoomDto;
import com.bees360.internal.ai.entity.enums.ImagePartialTypeEnum;
import com.bees360.internal.ai.entity.enums.ProjectSyncPointEnum;
import com.bees360.internal.ai.entity.enums.images.FirebaseImageCategoryTagEnum;
import com.bees360.internal.ai.entity.enums.images.FloorLevelTagEnum;
import com.bees360.internal.ai.entity.enums.images.ImageTagTypeEnum;
import com.bees360.internal.ai.entity.enums.images.RoomNameTagEnum;
import com.bees360.internal.ai.entity.enums.images.NumberTagEnum;
import com.bees360.internal.ai.event.AiImageDownloadEvent;
import com.bees360.internal.ai.service.ProjectImageManager;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.report.entity.enums.AnnotationTagFaceIdEnum;
import com.bees360.report.grpc.api.report2web.ImageAnnotationTagRequest;
import com.bees360.report.grpc.api.report2web.PointMessage;
import com.bees360.report.service.ImageAnnotationTagService;
import com.bees360.report.service.ImageTagDictService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

import com.bees360.util.Iterables;
import com.google.gson.Gson;
import com.google.protobuf.Int32Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Import;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import com.bees360.common.Message.QuerySymbol;
import com.bees360.common.Message.LogicalOperator;
import com.bees360.image.Message.AttributeMessage;


import static com.bees360.entity.enums.PipelineTaskEnum.IMAGE_UPLOADED;
import static com.bees360.entity.enums.PipelineTaskEnum.TAG_ELEVATION_OVERVIEWS;
import static com.bees360.entity.enums.PipelineTaskEnum.UPLOAD_EXTERIOR_IMAGES;
import static com.bees360.image.util.AttributeMessageAdapter.attributeToJson;

/**
 * <AUTHOR>
 * @date 2020/11/09 21:13
 */
@Import({
    HostaReportConfig.class,
})
@Slf4j
@Component
public class ImageDownloadListener {

    private static final Set<Message.PipelineStatus> TAG_ELEVATION_OVERVIEWS_NOT_COMPLETED =
            Set.of(Message.PipelineStatus.READY, Message.PipelineStatus.ERROR);

    private static final Set<Integer> EXCLUDE_NUMBER_TAGS =
            Set.of(
                    ImageTagEnum.ADDRESS_VERIFICATION.getCode(),
                    ImageTagEnum.CONTENT.getCode(),
                    ImageTagEnum.DEBRIS.getCode(),
                    ImageTagEnum.ELEVATED_DECK.getCode(),
                    ImageTagEnum.DOCK.getCode(),
                    ImageTagEnum.PLAY_OR_SWING_SET.getCode(),
                    ImageTagEnum.TRAMPOLINE.getCode(),
                    ImageTagEnum.BASKETBALL_HOOP.getCode(),
                    ImageTagEnum.OUTDOOR_FURNITURE.getCode(),
                    ImageTagEnum.BBQ_GRILL.getCode(),
                    ImageTagEnum.ROOF_LAYER.getCode());

    private static final Set<Integer> SPECIAL_HANDLING_LOCATION_TAGS =
            Set.of(
                    ImageTagEnum.BEDROOM.getCode(),
                    ImageTagEnum.BATHROOM.getCode(),
                    ImageTagEnum.CLOSET.getCode());

    private static final Set<Integer> INCLUDE_NUMBER_TAG_CATEGORIES =
            Set.of(ImageTagCategoryEnum.OBJECT.getCode(), ImageTagCategoryEnum.LOCATION.getCode());

    private static final AttributeMessage DEFAULT_TAG_ATTRIBUTE = AttributeMessage.getDefaultInstance();

    public static final String DEFAULT_TAG_ATTRIBUTE_SOURCE_APP = "APP";

    @Value("${image.tag.exclude-number-path}")
    private String[] excludePath;

    @Autowired
    private ImageTagDictService imageTagDictService;

    @Autowired private ImageTagDictProvider imageTagDictProvider;

    @Autowired private ImageTagManager imageTagManager;

    @Autowired private ImageApiProvider imageApiProvider;

    @Autowired private ProjectImageManager projectImageManager;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Autowired
    private ImageAnnotationTagService imageAnnotationTagService;

    @Autowired private PipelineService pipelineService;

    @Autowired
    @Qualifier("hostaTaskCalculator")
    private ObjectProvider<Consumer<Long>> hostaTaskCalculator;

    @Async
    @EventListener
    public void addImageTagOnAiImageDownloadEvent(AiImageDownloadEvent event) throws ServiceException {
        var projectId = event.getProjectId();
        log.info("addImageTagOnAiImageDownloadEvent projectId:{} syncPoint:{} imageIds:{}", projectId, event.getSyncPoint(), event.getImages().stream().map(ProjectImage::getImageId)
            .collect(Collectors.toList()));
        List<ProjectImage> images = event.getImages();
        if (!CollectionUtils.isEmpty(images)) {

            var imageRooms = event.getImageRooms();
            var tagDict = imageTagDictService.getImageTagDict();
            var imageTags = new HashMap<String, Map<String, AttributeMessage>>();
            var imageTagIds = new HashMap<String, Iterable<String>>();
            images.forEach(image->{
                try {
                    Map<String, AttributeMessage> needAddTags = getNeedAddTagsByImage(image, imageRooms, tagDict);
                    if (!MapUtils.isEmpty(needAddTags)) {
                        imageTags.put(image.getImageId(), needAddTags);
                        imageTagIds.put(image.getImageId(), needAddTags.keySet());
                    }
                } catch (Exception e) {
                    log.error("add Image tag on ai image download event error projectId {}, imageId {}",
                            image.getProjectId(),
                            image.getImageId(), e);
                }
            });
            log.info("addImageTagOnAiImageDownloadEvent imageTags:{}", imageTags);
            if (!imageTags.isEmpty()) {
                if (bees360FeatureSwitch.isEnableAutoTagWithNewLogic()) {
                    var imageTagRequests =
                            imageTags.entrySet().stream()
                                    .map(
                                            entry -> {
                                                var builder = com.bees360.image.Message.ImageTagRequest.newBuilder();
                                                builder.setImageId(entry.getKey());
                                                var tags = entry.getValue().entrySet().stream()
                                                        .map(t -> com.bees360.image.Message.ImageTagRequest.AddTag.newBuilder()
                                                                .setTagId(t.getKey())
                                                                .setAttribute(t.getValue())
                                                                .build())
                                                        .collect(Collectors.toList());
                                                builder.addAllTag(tags);
                                                return ImageTagRequest.from(builder.build());
                                            })
                                    .collect(Collectors.toList());
                    imageTagManager.addAllImageTag(imageTagRequests, AiBotUser.AI_NEW_USER_ID);
                } else {
                    log.info("AutoTag with oldLogic. imageTagIds:{}", imageTagIds);
                    imageTagManager.addAllImageTag(imageTagIds, AiBotUser.AI_NEW_USER_ID);
                }
            }

            projectImageManager.resetRoofLayerDar(event.getImages().get(0).getProjectId());

            if (!bees360FeatureSwitch.isNeedCheckUploadExteriorImagesTaskStatus()) {
                setElevationOverviewTagPipelineStatus(images.get(0).getProjectId());
            }

            hostaTaskCalculator.ifAvailable(calculator -> calculator.accept(images.get(0).getProjectId()));
        }

        if (bees360FeatureSwitch.isNeedCheckUploadExteriorImagesTaskStatus()
                && checkUploadExteriorImagesTaskStatusDone(projectId)
                && checkContainElevationAndOverviewTag(projectId)) {
            setElevationOverviewTagPipelineStatus(projectId);
        }

        if (Objects.equals(event.getSyncPoint(), ProjectSyncPointEnum.IMAGE_UPLOADED)) {
            setPipelineTaskStatus(projectId, IMAGE_UPLOADED.getKey(), Message.PipelineStatus.DONE);
        }
    }

    private boolean checkContainElevationAndOverviewTag(Long projectId) {
        log.info("check contain Elevation and Overview tag. {}", projectId);
        var includeTags = List.of(ImageTagEnum.ELEVATION, ImageTagEnum.OVERVIEW);
        String query = getImageApiQueryString(String.valueOf(projectId), includeTags);
        var imageByQuery = imageApiProvider.findImageByQuery(ImageApiQuery.of(query, 1, 0));
        return imageByQuery.getResultCount() > 0;
    }

    private String getImageApiQueryString(String projectId, List<ImageTagEnum> includeTags) {
        var projectIdQuery = Map.of("projectId", Map.of(QuerySymbol.EQ, projectId));
        var includeTagQuery = Map.of(
                "tag",
                Map.of(
                        QuerySymbol.INC,
                        Iterables.toList(
                                Iterables.transform(includeTags, ImageTagEnum::getDisplay))));
        var map = Map.of(LogicalOperator.AND, List.of(projectIdQuery, includeTagQuery));
        return new Gson().toJson(map);
    }

    private boolean checkUploadExteriorImagesTaskStatusDone(Long projectId) {
        log.info("check UploadExteriorImagesPipelineStatus {}", projectId);
        var pipeline = pipelineService.findById(String.valueOf(projectId));
        if (pipeline == null) {
            return false;
        }
        var taskKey = UPLOAD_EXTERIOR_IMAGES.getKey();
        var task =
                Iterables.toStream(pipeline.getTask())
                        .filter(
                                t ->
                                        StringUtils.equals(t.getKey(), taskKey)
                                                && t.getStatus() == Message.PipelineStatus.DONE)
                        .findFirst()
                        .orElse(null);
        return task != null;
    }

    private void setElevationOverviewTagPipelineStatus(long projectId) {
        log.info("set ElevationOverviewTagPipelineStatus {}", projectId);
        var pipeline = pipelineService.findById(String.valueOf(projectId));
        if (pipeline == null) {
            return;
        }
        var taskKey = TAG_ELEVATION_OVERVIEWS.getKey();
        var task =
                Iterables.toStream(pipeline.getTask())
                        .filter(
                                t ->
                                        StringUtils.equals(t.getKey(), taskKey)
                                                && TAG_ELEVATION_OVERVIEWS_NOT_COMPLETED.contains(
                                                        t.getStatus()))
                        .findFirst()
                        .orElse(null);
        if (task == null) {
            return;
        }

        var status = Message.PipelineStatus.DONE;
        setPipelineTaskStatus(projectId, taskKey, status);
    }

    private void setPipelineTaskStatus(long projectId, String key, Message.PipelineStatus status) {
        try {
            pipelineService.setTaskStatus(String.valueOf(projectId), key, status);
            log.info("Successfully set pipeline '{}' key '{}' to '{}'", projectId, key, status);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Failed set pipeline '{}' key '{}' to '{}'", projectId, key, status, e);
        }
    }

    private Map<String, AttributeMessage> getNeedAddTagsByImage(ProjectImage image, Map<String, ImageRoomDto> getImageRooms, List<ImageTagDict> tagDictList) {
        String imageId = image.getImageId();
        log.info("addImageTagOnAiImageDownloadEvent imageId:{}, imageCategory:{}, fileSourceType:{}, partialType:{}, type:{}, orientation:{}",
            imageId, image.getImageCategory(), image.getFileSourceType(), image.getPartialType(), image.getImageType(), image.getOrientation());
        log.info("GetNeedAddTagsByImage getImageRooms: {}", getImageRooms);

        // 先用新的解析逻辑解析出tag，若不存在则用老逻辑
        Map<Integer, AttributeMessage> tagCodesWithAttribute = getTagFromImageCategory(image, tagDictList);
        List<Integer> tagCodes = new ArrayList<>(tagCodesWithAttribute.keySet());
        if (tagCodes.isEmpty()) {
            ImagePartialTypeEnum partialTypeEnum = ImagePartialTypeEnum.getEnum(image.getPartialType());
            if (Objects.isNull(partialTypeEnum)) {
                return new HashMap<>();
            }
            FirebaseImageCategoryTagEnum tagEnum = FirebaseImageCategoryTagEnum
                .getByImagePartialTypeEnum(partialTypeEnum, image.getImageType(), image.getOrientation());
            if (Objects.isNull(tagEnum)) {
                return new HashMap<>();
            }

            // 设置object，方向类标签
            tagCodes = FirebaseImageCategoryTagEnum.getImageDictCodesByTagOrientation(tagEnum, image.getOrientation());

            // 设置房屋类标签
            if (getImageRooms != null && getImageRooms.containsKey(imageId)) {
                setRoomTag(tagCodes, getImageRooms.get(imageId));
            }
        } else if (getImageRooms != null
            && getImageRooms.containsKey(imageId)
            && getImageRooms.get(imageId) != null) {
            var imageRoom = getImageRooms.get(imageId);
            // 楼层
            var floorLevel = FloorLevelTagEnum.valueOfDisplayIgnoreCase(imageRoom.getFloorLevel());
            if (floorLevel != null) {
                tagCodes.add(floorLevel.getCode());
            }
        }

        var tagCodeSet = new HashSet<>(tagCodes);

        List<ImageTagDict> tagDict = tagDictList.stream()
            .filter(tag -> tagCodeSet.contains(tag.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tagDict)) {
            return new HashMap<>();
        }

        // 根据ImageTagType进行去重
        List<ImageTagDict> finalTagDict = tagDict.stream()
            .collect(
                Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ImageTagDict::getType))), ArrayList::new));

        // 添加整张图的annotation
        var annotationTagDict = tagDict.stream().filter(tag -> Objects.equals(tag.getType(), ImageTagTypeEnum.ANNOTATION.getCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(annotationTagDict)) {
            annotationTagDict.forEach(tag -> {
                var tagId = tag.getCode();
                var attribute = tagCodesWithAttribute.getOrDefault(tagId, DEFAULT_TAG_ATTRIBUTE);
                var builder = ImageAnnotationTagRequest.newBuilder()
                    .setFacetId(AnnotationTagFaceIdEnum.FULL.getCode())
                    .setLtPoint(PointMessage.newBuilder().setX(0.0).setY(0.0).build())
                    .setRbPoint(PointMessage.newBuilder().setX(0.0).setY(0.0).build())
                    .setType(tagId);
                Optional.ofNullable(attributeToJson(attribute)).ifPresent(builder::setAttribute);
                ImageAnnotationTagRequest tagRequest = builder.build();
                try {
                    imageAnnotationTagService.createImageAnnotationTag(AiBotUser.AI_NEW_USER_ID, imageId, image.getProjectId(), tagRequest);
                } catch (Exception e) {
                    log.error("addImageTagOnAiImageDownloadEvent createImageAnnotationTag, imageId:{}, msg:{}", imageId,
                        e.getMessage(), e);
                }
            });
        }

        return finalTagDict.stream()
            .filter(tag -> !Objects.equals(tag.getType(), ImageTagTypeEnum.ANNOTATION.getCode()))
            .collect(Collectors.toMap(
                    tag -> String.valueOf(tag.getCode()),
                    tag -> tagCodesWithAttribute.getOrDefault(tag.getCode(), DEFAULT_TAG_ATTRIBUTE)
            ));
    }

    private void setRoomTag(List<Integer> tags, ImageRoomDto imageRoom) {
        if (imageRoom == null) {
            return;
        }
        // 房间名称
        var roomLocation = RoomNameTagEnum.findByRoomName(imageRoom.getRoomName());
        Optional.ofNullable(roomLocation).ifPresent(e -> tags.add(e.getCode()));

        // 序号
        if (roomLocation != null) {
            var numberTag = NumberTagEnum.findByNumber(imageRoom.getNumber());
            Optional.ofNullable(numberTag).ifPresent(e -> tags.add(e.getCode()));
        }

        // 楼层
        var floorLevel = FloorLevelTagEnum.valueOfDisplayIgnoreCase(imageRoom.getFloorLevel());
        Optional.ofNullable(floorLevel).ifPresent(e -> tags.add(e.getCode()));
    }

    /**
     * 从imageCategory字段解析出拍摄时生成的路径标签
     */
    private Map<Integer, AttributeMessage> getTagFromImageCategory(ProjectImage image, List<ImageTagDict> imageTags) {
        log.info("Parse the path label generated when shooting from the imageCategory '%s'.".formatted(image.getImageCategory()));
        if (StringUtils.isEmpty(image.getImageCategory())) {
            return new HashMap<>();
        }
        Map<String, Integer> imageTagDict = imageTags
                                            .stream()
                                            .distinct()
                                            .collect(Collectors.toMap(ImageTagDict::getName, ImageTagDict::getCode));
        // 截掉文件名
        String imageCategory = image.getImageCategory().replace(image.getOriginalFileName(), "");
        // 根据分隔符 '/' 拆分字符串
        String[] tagGroups = StringUtils.split(imageCategory, "/");
        if (tagGroups == null || tagGroups.length == 0) {
            return new HashMap<>();
        }

        Map<Integer, AttributeMessage> tagCodesWithAttribute = new LinkedHashMap<>();
        Arrays.stream(tagGroups).forEach(tagGroup -> {
            // 根据分隔符 '%' 拆分字符串
            var tagGroupsWithNumber = tagGroup.split("%");

            /*
            2024-10-25 New logic:
            1. Default index cannot overwrite the specified number.
            2. If multiple numbers are specified for the same tag, the first one is used.
             */
            AttributeMessage attribute;
            if (tagGroupsWithNumber.length == 2 && StringUtils.isNumeric(tagGroupsWithNumber[1])) {
                var index = Integer.parseInt(tagGroupsWithNumber[1]) - 1;
                attribute = createAttributeMessage(index);
            } else {
                attribute = null;
            }
            var group = tagGroupsWithNumber[0];
            Iterables.toList(imageTagDictProvider.findByGroup(group, ImageTagGroupType.BEES_PILOT.getValue()))
                    .forEach(
                            imageTag -> {
                                if (imageTagDict.containsKey(imageTag.getTitle())) {
                                    var tagCode = imageTagDict.get(imageTag.getTitle());
                                    if (tagCodesWithAttribute.get(tagCode) == null) {
                                        tagCodesWithAttribute.put(tagCode, attribute);
                                    }
                                }
                            });
        });
        // reset tagCodesWithAttribute attribute null -> default attribute
        var defaultAttribute = createAttributeMessage(0);
        for (Map.Entry<Integer, AttributeMessage> entry : tagCodesWithAttribute.entrySet()) {
            var tagCode = entry.getKey();
            var attribute = entry.getValue();
            if (attribute == null) {
                tagCodesWithAttribute.put(tagCode, defaultAttribute);
            }
        }

        tagGroups =
                Arrays.stream(StringUtils.split(imageCategory, "/"))
                        .filter(path -> !StringUtils.containsAnyIgnoreCase(path, excludePath))
                        .flatMap(path -> Arrays.stream(StringUtils.split(path, "%")))
                        .toArray(String[]::new);

        if (bees360FeatureSwitch.isEnableAddNumberTagFromImageCategory()) {
            var includeNumberTags =
                    imageTags.stream()
                            .filter(tagDict -> tagDict.getCategory() != null)
                            .filter(
                                    tagDict ->
                                            INCLUDE_NUMBER_TAG_CATEGORIES.contains(
                                                    tagDict.getCategory()))
                            .map(ImageTagDict::getCode)
                            .collect(Collectors.toSet());
            var tagCodes = tagCodesWithAttribute.keySet();
            if (!CollectionUtils.containsAny(tagCodes, EXCLUDE_NUMBER_TAGS)
                    && CollectionUtils.containsAny(tagCodes, includeNumberTags)) {
                if (bees360FeatureSwitch.isEnableAddNumberTagWithNewLogic()) {
                    var numberTag = getNumberTagWithNewLogic(imageTags, tagCodesWithAttribute, imageTagDict);
                    log.info("Get number tag code {} with tagCodesWithAttribute {}.", numberTag, tagCodesWithAttribute);
                    if (numberTag != null) {
                        tagCodesWithAttribute.put(numberTag, DEFAULT_TAG_ATTRIBUTE);
                    }
                } else {
                    for (var tagGroup : tagGroups) {
                        if (StringUtils.isNumeric(tagGroup)) {
                            Optional.ofNullable(imageTagDict.get(tagGroup)).ifPresent(t -> tagCodesWithAttribute.put(t, DEFAULT_TAG_ATTRIBUTE));
                            break;
                        }
                    }
                }
            }
        }

        log.info("Complete the acquisition of tagMap based on imageCategory. imageCategory='%s', tags='%s'".formatted(imageCategory, tagCodesWithAttribute));
        return tagCodesWithAttribute;
    }

    private AttributeMessage createAttributeMessage(int index) {
        return AttributeMessage.newBuilder()
                .setIndex(Int32Value.of(index))
                .setSource(DEFAULT_TAG_ATTRIBUTE_SOURCE_APP)
                .build();
    }

    /**
     * numberTag 的新逻辑详见文档: https://confluence.9realms.co/pages/viewpage.action?pageId=34635826
     * 优先取 objectTag 的 number 作为 numberTag、其次 取 Location tag 的number作为 numberTag，
     * 其中需要特殊处理 Location 分类下的  Bedroom、Bathroom、Closet 三个 tag 为 Master number。
     * PS: numberTag = attribute index + 1
     * @param imageTags imageTags
     * @param tagCodesWithAttribute tagCodesWithAttribute
     * @param imageTagDict imageTagDict
     * @return numberTag code
     */
    private Integer getNumberTagWithNewLogic(
            List<ImageTagDict> imageTags,
            Map<Integer, AttributeMessage> tagCodesWithAttribute,
            Map<String, Integer> imageTagDict) {
        // 优先取 objectTag 的 number 作为 numberTag
        var objectTagEntry = getTagEntryByCategory(imageTags, ImageTagCategoryEnum.OBJECT, tagCodesWithAttribute);
        if (objectTagEntry != null) {
            // numberTag = attribute index + 1
            var objectNumberTag = objectTagEntry.getValue().getIndex().getValue() + 1;
            return imageTagDict.get(String.valueOf(objectNumberTag));
        }

        // 其次 取 Location tag 的number作为 numberTag
        var locationTagEntry = getTagEntryByCategory(imageTags, ImageTagCategoryEnum.LOCATION, tagCodesWithAttribute);
        if (locationTagEntry != null) {
            var locationTagId = locationTagEntry.getKey();
            // numberTag = attribute index + 1
            var locationNumberTag = locationTagEntry.getValue().getIndex().getValue() + 1;
            if (SPECIAL_HANDLING_LOCATION_TAGS.contains(locationTagId) && locationNumberTag == 1) {
                // 特殊处理 Location 分类下的  Bedroom、Bathroom、Closet 三个 tag 为 Master number
                return ImageTagEnum.MASTER.getCode();
            }
            return imageTagDict.get(String.valueOf(locationNumberTag));
        }
        return null;
    }

    private Map.Entry<Integer, AttributeMessage> getTagEntryByCategory(List<ImageTagDict> imageTags, ImageTagCategoryEnum categoryEnum, Map<Integer, AttributeMessage> tagCodesWithAttribute) {
        var imageTagsByCategory = getImageTagsByCategory(imageTags, categoryEnum);
        return tagCodesWithAttribute.entrySet().stream()
                .filter(tag -> imageTagsByCategory.contains(tag.getKey()))
                .findFirst()
                .orElse(null);
    }

    private Set<Integer> getImageTagsByCategory(List<ImageTagDict> imageTags, ImageTagCategoryEnum categoryEnum) {
        return imageTags.stream()
                .filter(tagDict -> tagDict.getCategory() != null)
                .filter(tagDict -> categoryEnum.getCode() == tagDict.getCategory())
                .map(ImageTagDict::getCode)
                .collect(Collectors.toSet());
    }
}
