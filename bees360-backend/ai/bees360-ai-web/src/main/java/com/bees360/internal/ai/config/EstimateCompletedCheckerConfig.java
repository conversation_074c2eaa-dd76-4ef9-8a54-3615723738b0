package com.bees360.internal.ai.config;

import com.bees360.internal.ai.entity.dto.UpdateFailedDto;
import com.bees360.internal.ai.exchange.ai2client.CommonProto;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.report.Message;
import com.bees360.report.ReportTypeEnum;
import com.bees360.util.Iterables;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.function.Function;

@Configuration
public class EstimateCompletedCheckerConfig {

    @Bean("estimateCompletedChecker")
    public Function<Long, UpdateFailedDto> estimateCompletedChecker(
        Bees360FeatureSwitch bees360FeatureSwitch,
        ProjectReportManager projectReportManager) {
        return (projectId) -> {
            if (!bees360FeatureSwitch.isEnableInvoiceStatus()) {
                return null;
            }

            var invoiceReports = projectReportManager.find(
                String.valueOf(projectId), ReportTypeEnum.INV.getKey(), null);

            // check if latest invoice report approved
            if (Iterables.isNotEmpty(invoiceReports)
                    && Message.ReportMessage.Status.APPROVED == invoiceReports.iterator().next().getStatus()) {
                return null;
            }

            return UpdateFailedDto.builder()
                .id(projectId)
                .message("The invoice has not been approved. Please approve invoice first and then complete estimate.")
                .operationFeedback(CommonProto.UpdateFailed.OperationFeedback.NOT_ALLOWED)
                .build();
        };
    }
}
