package com.bees360.internal.ai.scheduled;

import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.internal.ai.entity.dto.ClaimWorkloadStatistics;
import com.bees360.internal.ai.entity.dto.UWWorkloadStatistics;
import com.bees360.internal.ai.service.AiNotificationService;
import com.bees360.internal.ai.service.ProjectWorkloadStatisticService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.job.registry.ReviewerWorkloadStatisticsJob;
import com.bees360.job.util.AbstractJobExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collection;
import java.util.TimeZone;

@Component
@Log4j2
@RequiredArgsConstructor
public class ReviewerWorkloadStatisticsJobExecutor
        extends AbstractJobExecutor<ReviewerWorkloadStatisticsJob> {

    private static final ZoneId US_CENTER_ZONE =
            TimeZone.getTimeZone(AmericaTimeZone.US_CENTRAL).toZoneId();

    private final AiNotificationService aiNotificationService;

    private final ProjectWorkloadStatisticService projectWorkloadStatisticService;

    private final Bees360FeatureSwitch bees360FeatureSwitch;

    @Override
    protected void handle(ReviewerWorkloadStatisticsJob job) throws IOException {
        Instant startTime = job.getStartTime();
        Instant endTime = job.getEndTime();

        Collection<UWWorkloadStatistics> uwStatistics;
        Collection<ClaimWorkloadStatistics> claimStatistics;
        try {
            uwStatistics =
                    projectWorkloadStatisticService.getReviewerUWWorkloadStatistic(
                            startTime, endTime);
            if (bees360FeatureSwitch.isEnableCloseClaimEmail()) {
                claimStatistics = CollectionUtils.emptyCollection();
            } else {
                claimStatistics =
                        projectWorkloadStatisticService.getReviewerClaimWorkloadStatistic(
                                startTime, endTime);
            }
        } catch (RuntimeException e) {
            throw new IllegalStateException("Failed to collect reviewer workload statistics.", e);
        }

        // send to admin
        aiNotificationService.sendReviewerStatistics(
                uwStatistics,
                claimStatistics,
                LocalDate.ofInstant(startTime, US_CENTER_ZONE),
                LocalDate.ofInstant(endTime, US_CENTER_ZONE),
                job.getRecipients());
    }
}
