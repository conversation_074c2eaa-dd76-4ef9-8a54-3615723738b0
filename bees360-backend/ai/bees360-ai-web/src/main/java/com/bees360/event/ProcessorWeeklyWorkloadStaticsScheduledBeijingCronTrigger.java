package com.bees360.event;

import com.bees360.event.registry.CronTriggerEveryMondayAt2AmBeijing;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.ProcessorWorkloadStatisticsScheduledBeijing;
import java.io.IOException;

/**
 * 每周一北京时间凌晨2点触发处理器工作负载统计任务
 */
public class ProcessorWeeklyWorkloadStaticsScheduledBeijingCronTrigger extends AbstractNamedEventListener<CronTriggerEveryMondayAt2AmBeijing> {

    private final ProcessorWorkloadStatisticsScheduledBeijing processorWorkloadStatisticsScheduledBeijing;

    public ProcessorWeeklyWorkloadStaticsScheduledBeijingCronTrigger(ProcessorWorkloadStatisticsScheduledBeijing processorWorkloadStatisticsScheduledBeijing) {
        this.processorWorkloadStatisticsScheduledBeijing = processorWorkloadStatisticsScheduledBeijing;
    }

    @Override
    public void handle(CronTriggerEveryMondayAt2AmBeijing cronTriggerEveryMondayAt2AmBeijing) throws IOException {
        processorWorkloadStatisticsScheduledBeijing.sendProcessorWeeklyWorkloadStatics();
    }
}
