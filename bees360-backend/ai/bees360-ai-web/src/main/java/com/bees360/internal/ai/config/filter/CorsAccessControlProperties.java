package com.bees360.internal.ai.config.filter;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2019/10/22 18:26
 */
@Getter
@Setter
@ToString
public class CorsAccessControlProperties {

    @Getter
    @Setter
    @ToString
    public static class CorsAccessControlAllow {
        private String originWhites;
        private String originRegexes;
        private String methods;
        private String headers;
        private boolean credentials;
    }

    @Getter
    @Setter
    @ToString
    public static class CorsAccessControlExpose {
        private String headers;
    }

    private CorsAccessControlAllow allowed;
    private CorsAccessControlExpose exposed;
    // cors.preflight.maxage
    private long maxAge;
}
