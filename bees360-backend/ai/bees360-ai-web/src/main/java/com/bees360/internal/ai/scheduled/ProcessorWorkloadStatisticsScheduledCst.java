package com.bees360.internal.ai.scheduled;

import static com.bees360.commons.lang.time.DateTimeUtil.getStartOfDay;
import static com.bees360.commons.lang.time.DateTimeUtil.getStartOfMonth;

import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.internal.ai.entity.dto.EmailRecipients;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.ProcessorWorkloadStatisticsJob;
import com.bees360.util.schedule.annotation.DistributedScheduled;

import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;

@Component
@Log4j2
@AllArgsConstructor
public class ProcessorWorkloadStatisticsScheduledCst {

    private static final ZoneId US_CENTER_ZONE =
        TimeZone.getTimeZone(AmericaTimeZone.US_CENTRAL).toZoneId();

    private final JobScheduler jobScheduler;

    private final EmailRecipients emailRecipients;

    /** 每天的凌晨两点统计前一天的所有Process的工作量 (Generator报告和Submit报告) */
    public void sendProcessorDailyWorkloadStatics() {
        log.info("sendProcessorDailyWorkloadStaticsCst cronTriggerDailyAt2AmCst");
        // 得到北京时间前一天凌晨0点0分0秒的时间戳
        Instant startTime = getStartOfDay(-1, US_CENTER_ZONE);
        // 得到北京时间前一天当23点59分59秒的时间戳
        Instant endTime = getStartOfDay(0, US_CENTER_ZONE).minusSeconds(1);
        var emailRecipient  =emailRecipients.getProcessorWorkloadStatistics();
        var specialChineseStaffs = emailRecipients.getProcessorWorkloadStatisticsSpecialChineseStaffs();
        sendProcessorWorkloadStatics(startTime, endTime, emailRecipient, specialChineseStaffs);
    }

    /** 每周的凌晨两点统计前一周的所有Process的工作量 (Generator报告和Submit报告) */
    public void sendProcessorWeeklyWorkloadStatics() {
        log.info("sendProcessorWeeklyWorkloadStaticsCst cronTriggerEveryMondayAt2AmCst");
        // 得到北京时间七天前凌晨0点0分0秒的时间戳
        Instant startTime = getStartOfDay(-7, US_CENTER_ZONE);
        // 得到北京时间前一天当23点59分59秒的时间戳
        Instant endTime = getStartOfDay(0, US_CENTER_ZONE).minusSeconds(1);
        var emailRecipient =
            Optional.ofNullable(emailRecipients.getWeeklyProcessorWorkloadStatistics())
                .orElse(emailRecipients.getProcessorWorkloadStatistics());
        var specialChineseStaffs = emailRecipients.getProcessorWorkloadStatisticsSpecialChineseStaffs();
        sendProcessorWorkloadStatics(startTime, endTime, emailRecipient, specialChineseStaffs);
    }

    /** 每月一号的凌晨两点统计前一月的所有Process的工作量 (Generator报告和Submit报告) */
    public void sendProcessorMonthlyWorkloadStatics() {
        log.info("sendProcessorMonthlyWorkloadStaticsCst cronTriggerMonthly1stAt2AmCst");
        Instant startTime = getStartOfMonth(-1, US_CENTER_ZONE);
        Instant endTime = getStartOfMonth(0, US_CENTER_ZONE).minusSeconds(1);
        var emailRecipient =
                Optional.ofNullable(emailRecipients.getMonthlyProcessorWorkloadStatistics())
                        .orElse(emailRecipients.getProcessorWorkloadStatistics());
        var specialChineseStaffs = emailRecipients.getProcessorWorkloadStatisticsSpecialChineseStaffs();
        sendProcessorWorkloadStatics(startTime, endTime, emailRecipient, specialChineseStaffs);
    }

    /** 每周的凌晨两点统计当月的所有Process的累计工作量 (Generator报告和Submit报告) */
    public void sendProcessorMonthlyAccumulatedWorkloadStatics() {
        log.info("sendProcessorMonthlyAccumulatedWorkloadStatics cronTriggerEveryMondayAt2AmCst");
        Instant startTime = getStartOfMonth(0, US_CENTER_ZONE);
        Instant endTime = getStartOfMonth(1, US_CENTER_ZONE).minusSeconds(1);
        var emailRecipient =
            Optional.ofNullable(emailRecipients.getWeeklyProcessorWorkloadStatistics())
                .orElse(emailRecipients.getProcessorWorkloadStatistics());
        var specialChineseStaffs = emailRecipients.getProcessorWorkloadStatisticsSpecialChineseStaffs();
        sendProcessorWorkloadStatics(startTime, endTime, emailRecipient, specialChineseStaffs);
    }

    public void sendProcessorWorkloadStatics(Instant startTime, Instant endTime, List<String> recipients, List<String> specialChineseStaffs) {
        var job = new ProcessorWorkloadStatisticsJob(startTime, endTime, recipients, specialChineseStaffs, AmericaTimeZone.US_CENTRAL);
        jobScheduler.schedule(RetryableJob.of(JobPayloads.encode(job), 8, Duration.ofMinutes(2), null));
        log.info("Schedule processor workload statistics Cst from {} to {} email sending job successfully.", startTime, endTime);
    }
}
