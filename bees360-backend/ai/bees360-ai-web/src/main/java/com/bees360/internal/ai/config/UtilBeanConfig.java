package com.bees360.internal.ai.config;

import com.bees360.common.grpc.GrpcHeaderInterceptor;
import com.bees360.common.httpclient.HttpClientConfig;
import com.bees360.internal.ai.config.converter.CustomProtobufHttpMessageConverter;
import com.bees360.internal.ai.entity.dto.EmailRecipients;
import io.grpc.ClientInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 用于将工具类加入到Spring的管理中
 *
 * <AUTHOR>
 * @date 2019/08/06
 */
@Configuration
@Slf4j
public class UtilBeanConfig {

    @Bean
    @ConfigurationProperties(prefix = "http.client")
    public HttpClientConfig httpClientConfig() {
        return new HttpClientConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "email.recipients")
    public EmailRecipients emailRecipients() {
        return new EmailRecipients();
    }

    @Bean
    public ClientInterceptor grpcHeaderInterceptor() {
        return new GrpcHeaderInterceptor();
    }

    @Bean
    public CustomProtobufHttpMessageConverter customProtobufHttpMessageConverter() {
        return new CustomProtobufHttpMessageConverter();
    }
}
