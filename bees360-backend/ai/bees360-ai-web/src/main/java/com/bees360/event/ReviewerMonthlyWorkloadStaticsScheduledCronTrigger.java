package com.bees360.event;

import com.bees360.event.registry.CronTriggerMonthly1stAt2AmCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.ReviewerWorkloadStatisticsScheduled;
import java.io.IOException;

/**
 * 每月1日凌晨2点触发审阅员工作量统计任务的定时触发器类
 */
public class ReviewerMonthlyWorkloadStaticsScheduledCronTrigger extends AbstractNamedEventListener<CronTriggerMonthly1stAt2AmCst> {

    private final ReviewerWorkloadStatisticsScheduled reviewerWorkloadStatisticsScheduled;

    public ReviewerMonthlyWorkloadStaticsScheduledCronTrigger(ReviewerWorkloadStatisticsScheduled reviewerWorkloadStatisticsScheduled) {
        this.reviewerWorkloadStatisticsScheduled = reviewerWorkloadStatisticsScheduled;
    }

    @Override
    public void handle(CronTriggerMonthly1stAt2AmCst cronTriggerMonthly1stAt2AmCst) throws IOException {
        reviewerWorkloadStatisticsScheduled.sendReviewerMonthlyWorkloadStatics();
    }
}
