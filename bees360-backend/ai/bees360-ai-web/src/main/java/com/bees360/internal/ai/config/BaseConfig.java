package com.bees360.internal.ai.config;

import com.bees360.entity.CompanyIDMap;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/07/19
 */
@Configuration
//@PropertySource("classpath:secrets-${ENV}.properties")
//secrets-${ENV}.properties is not defined in this project
public class BaseConfig {

    @ConfigurationProperties(prefix = "company-id-map")
    @Bean
    CompanyIDMap companyIDMap() {
        return new CompanyIDMap();
    }

    @Bean
    @ConfigurationProperties(prefix = "company-config")
    public Map<Long, CompanyConfig> companyConfig() {
        return new HashMap<>();
    }

    @Data
    public static class CompanyConfig {

        private CompanyPriceStrategy price;
    }

    @Data
    public static class CompanyPriceStrategy {

        private List<String> strategy;
    }
}
