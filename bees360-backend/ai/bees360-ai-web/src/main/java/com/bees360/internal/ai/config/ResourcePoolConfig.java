package com.bees360.internal.ai.config;

import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.resource.GrpcResourceClient;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.ResourceUrlProvider;
import com.bees360.resource.config.GrpcResourceClientConfig;
import com.google.api.client.util.Preconditions;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 *
 **/
@Configuration
@Log4j2
@Import({ApacheHttpClientConfig.class, GrpcResourceClientConfig.class})
public class ResourcePoolConfig {

    @Bean({"resourcePool"})
    public ResourcePool resourcePool(GrpcResourceClient grpcResourceClient) {
        // 用GrpcResourceClient当作默认的ResourcePool
        return grpcResourceClient;
    }

    @Bean
    public ResourceUrlProvider resourceUrlProvider(ResourcePool resourcePool) {
        Preconditions.checkArgument(
            resourcePool.isResourceUrlProvider(), "Resource url provider should be provide");
        return resourcePool.asResourceUrlProvider();
    }
}
