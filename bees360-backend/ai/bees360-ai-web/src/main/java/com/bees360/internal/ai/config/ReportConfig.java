package com.bees360.internal.ai.config;

import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.report.ReportGroupManager;
import com.bees360.report.config.GrpcReportGroupManagerConfig;
import com.bees360.report.config.GrpcReportManagerClientConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import(
        value = {
            GrpcReportManagerClientConfig.class,
            GrpcReportGroupManagerConfig.class,
        })
@Configuration
public class ReportConfig {

    @Bean
    public ProjectReportManager projectReportManager(ReportGroupManager reportGroupManager) {
        return new DefaultProjectReportManager(reportGroupManager);
    }
}
