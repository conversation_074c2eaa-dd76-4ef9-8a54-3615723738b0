package com.bees360.internal.ai.config;

import com.bees360.internal.ai.config.filter.CorsAccessControlProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/06/16 09:55
 */
@Configuration
public class CorsConfig {


    @Bean
    @ConfigurationProperties(prefix="cors.access.control")
    public CorsAccessControlProperties corsAccessControlProperties() {
        return new CorsAccessControlProperties();
    }
}
