package com.bees360.internal.ai.config;

import com.bees360.customer.config.GrpcCustomerClientConfig;
import com.bees360.project.config.GrpcProjectAirspaceManagerConfig;
import com.bees360.project.config.GrpcProjectCatastropheClientConfig;
import com.bees360.project.config.GrpcProjectContactClientConfig;
import com.bees360.project.config.GrpcProjectIIMangerConfig;
import com.bees360.project.config.GrpcProjectParticipantClientConfig;
import com.bees360.project.config.GrpcProjectStateChangeReasonManagerConfig;
import com.bees360.project.config.GrpcProjectTagManagerConfig;
import com.bees360.project.config.GrpcStateChangeReasonGroupManagerConfig;
import com.bees360.project.state.ChangeReasonFromCategoryProvider;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import(value = {
    GrpcProjectIIMangerConfig.class,
    GrpcProjectTagManagerConfig.class,
    GrpcProjectCatastropheClientConfig.class,
    GrpcProjectContactClientConfig.class,
    GrpcCustomerClientConfig.class,
    GrpcProjectParticipantClientConfig.class,
    GrpcProjectAirspaceManagerConfig.class,
    GrpcProjectStateChangeReasonManagerConfig.class,
    GrpcStateChangeReasonGroupManagerConfig.class,
    ChangeReasonFromCategoryProvider.class,
})
@Configuration
public class ProjectConfig {}
