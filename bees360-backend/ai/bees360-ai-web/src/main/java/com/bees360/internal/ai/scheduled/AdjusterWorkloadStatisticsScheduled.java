package com.bees360.internal.ai.scheduled;

import com.bees360.internal.ai.entity.dto.EmailRecipients;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.AdjusterWorkloadStatisticsJob;
import com.bees360.job.registry.JobPayloads;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;

import static com.bees360.commons.lang.time.AmericaTimeZone.US_CENTRAL;
import static com.bees360.commons.lang.time.DateTimeUtil.getStartOfDay;
import static com.bees360.commons.lang.time.DateTimeUtil.getStartOfMonth;

@Component
@Log4j2
public class AdjusterWorkloadStatisticsScheduled {

    private static final ZoneId US_CENTER_ZONE = TimeZone.getTimeZone(US_CENTRAL).toZoneId();

    @Autowired private JobScheduler rabbitJobScheduler;

    @Autowired private EmailRecipients emailRecipients;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    /**
     * 每周一的凌晨两点统计上周的所有Adjuster的工作量 (完成Estimate和Dar报告的数量)
     */
    public void sendAdjusterWeeklyWorkloadStatics() {
        log.info("sendAdjusterWeeklyWorkloadStatics CronTriggerEveryMondayAt2AmCst");
        // 得到美国时间七天前凌晨0点0分0秒的时间戳
        Instant startTime = getStartOfDay(-7, US_CENTER_ZONE);
        // 得到美国时间前一天凌晨23点59分59秒的时间戳
        Instant endTime = getStartOfDay(0, US_CENTER_ZONE).minusSeconds(1);
        var recipients =
                Optional.ofNullable(emailRecipients.getWeeklyAdjusterWorkloadStatistics())
                        .orElse(emailRecipients.getAdjusterWorkloadStatistics());
        deSendWorkloadStatics(startTime, endTime, recipients);
    }

    /**
     * 每天的凌晨两点统计前一天的所有Adjuster的工作量 (完成Estimate和Dar报告的数量)
     */
    public void sendAdjusterDailyWorkloadStatics() {
        log.info("sendAdjusterDailyWorkloadStatics CronTriggerDailyAt2AmCst");
        // 得到美国时间前一天凌晨0点0分0秒的时间戳
        Instant startTime = getStartOfDay(-1, US_CENTER_ZONE);
        // 得到美国时间前一天凌晨23点59分59秒的时间戳
        Instant endTime = getStartOfDay(0, US_CENTER_ZONE).minusSeconds(1);
        var recipients = emailRecipients.getAdjusterWorkloadStatistics();
        deSendWorkloadStatics(startTime, endTime, recipients);
    }

    /**
     * 每月一号的凌晨两点统计前一天的所有Adjuster的工作量 (完成Estimate和Dar报告的数量)
     */
    public void sendAdjusterMonthlyWorkloadStatics() {
        log.info("sendAdjusterMonthlyWorkloadStatics CronTriggerMonthly1stAt2AmCst");
        Instant startTime = getStartOfMonth(-1, US_CENTER_ZONE);
        Instant endTime = getStartOfMonth(0, US_CENTER_ZONE).minusSeconds(1);
        var recipients =
                Optional.ofNullable(emailRecipients.getMonthlyAdjusterWorkloadStatistics())
                        .orElse(emailRecipients.getAdjusterWorkloadStatistics());
        deSendWorkloadStatics(startTime, endTime, recipients);
    }

    private void deSendWorkloadStatics(
            Instant startTime, Instant endTime, List<String> recipients) {
        if (bees360FeatureSwitch.isEnableCloseClaimEmail()) {
            log.info("Skip adjuster workload statistics from {} to {} email sending job successfully.",
                startTime, endTime);
            return;
        }
        deSendWorkloadStatics(startTime, endTime, recipients, true);
    }

    public void deSendWorkloadStatics(Instant startTime, Instant endTime, List<String> recipients, boolean sendToStaff) {
        var job = new AdjusterWorkloadStatisticsJob(startTime, endTime, recipients, sendToStaff);
        rabbitJobScheduler.schedule(RetryableJob.of(JobPayloads.encode(job), 8, Duration.ofMinutes(2), null));
        log.info("Schedule adjuster workload statistics from {} to {} email sending job successfully.", startTime, endTime);
    }
}
