package com.bees360.internal.ai.controller;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivityQuery;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.entity.ProjectStatusVo;
import com.bees360.internal.ai.scheduled.AdjusterWorkloadStatisticsScheduled;
import com.bees360.internal.ai.scheduled.ProcessorWorkloadStatisticsScheduledBeijing;
import com.bees360.internal.ai.scheduled.ProcessorWorkloadStatisticsScheduledCst;
import com.bees360.internal.ai.scheduled.ReviewerWorkloadStatisticsScheduled;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectImageService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Nullable;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bees360.activity.Message.ActivityMessage.EntityType.PROJECT;
import static com.bees360.activity.Message.ActivityMessage.FieldName.STATUS;
import static com.bees360.entity.enums.NewProjectStatusEnum.ASSIGNED_TO_PILOT;
import static com.bees360.entity.enums.NewProjectStatusEnum.CLIENT_RECEIVED;
import static com.bees360.entity.enums.NewProjectStatusEnum.IMAGE_UPLOADED;
import static com.bees360.entity.enums.NewProjectStatusEnum.RETURNED_TO_CLIENT;
import static com.bees360.entity.enums.NewProjectStatusEnum.SITE_INSPECTED;

/**
 * 用于提供统计邮件的接口
 *
 * <AUTHOR> Guanrong
 * @date 2019/08/24 12:21
 */
@RestController
@RequestMapping("/temp")
@Log4j2
public class TempController {
    @Autowired private AdjusterWorkloadStatisticsScheduled adjusterWorkloadStatisticsScheduled;
    @Autowired private ProcessorWorkloadStatisticsScheduledBeijing processorWorkloadStatisticsScheduledBeijing;
    @Autowired private ProcessorWorkloadStatisticsScheduledCst processorWorkloadStatisticsScheduledCst;
    @Autowired private ReviewerWorkloadStatisticsScheduled reviewerWorkloadStatisticsScheduled;
    @Autowired private ProjectEsService projectEsService;
    @Autowired private ActivityManager activityManager;
    @Autowired private ProjectImageService projectImageService;
    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @GetMapping("sendAdjusterWorkloadStatics")
    public void sendAdjusterWorkloadStatics(
            String startEcho, String endEcho, String recipients, boolean isSendToStaff) {
        adjusterWorkloadStatisticsScheduled.deSendWorkloadStatics(
                ZonedDateTime.parse(startEcho).toInstant(),
                ZonedDateTime.parse(endEcho).toInstant(),
                Arrays.asList(recipients.split(",")),
                isSendToStaff);
    }

    @GetMapping("sendProcessorWorkloadStaticsBeijing")
    public void sendProcessorWorkloadStaticsBeijing(
            String startEcho, String endEcho, String recipients, String specialChineseStaffs) {
        processorWorkloadStatisticsScheduledBeijing.sendProcessorWorkloadStatics(
                ZonedDateTime.parse(startEcho).toInstant(),
                ZonedDateTime.parse(endEcho).toInstant(),
                Arrays.asList(recipients.split(",")),
                Arrays.asList(specialChineseStaffs.split(",")));
    }

    @GetMapping("sendProcessorWorkloadStaticsCst")
    public void sendProcessorWorkloadStaticsCst(
            String startEcho, String endEcho, String recipients, String specialChineseStaffs) {
        processorWorkloadStatisticsScheduledCst.sendProcessorWorkloadStatics(
                ZonedDateTime.parse(startEcho).toInstant(),
                ZonedDateTime.parse(endEcho).toInstant(),
                Arrays.asList(recipients.split(",")),
                Arrays.asList(specialChineseStaffs.split(",")));
    }

    @GetMapping("sendReviewerWorkloadStatics")
    public void sendReviewerWorkloadStatics(String startEcho, String endEcho, String recipients) {
        reviewerWorkloadStatisticsScheduled.sendWorkloadStatics(
                ZonedDateTime.parse(startEcho).toInstant(),
                ZonedDateTime.parse(endEcho).toInstant(),
                Arrays.asList(recipients.split(",")));
    }

    @Deprecated
    @PostMapping("recoverProjectTimeline")
    private void recoverProjectTimeline(Long projectIdStart, Long projectIdEnd) {
        recoverTimeline(projectIdStart, projectIdEnd);
    }

    public void recoverTimeline(Long projectIdStart, Long projectIdEnd) {
        for (long i = projectIdStart; i <= projectIdEnd; i++) {
            var project = projectEsService.findProjectByProjectId(i);
            if (project == null) {
                continue;
            }

            try {
                log.info("Start recover project '{}' timeline.", i);
                doRecoverTimeline(project);
                log.info("Successfully recover project '{}' timeline.", i);
            } catch (RuntimeException e) {
                log.warn("Failed to recover project '{}' timeline.", i, e);
            }
        }
    }

    private void doRecoverTimeline(ProjectEsModel esModel) {
        var projectId = esModel.getProjectId();
        var timelines = esModel.getTimeLines();
        if (CollectionUtils.isEmpty(timelines)) {
            return;
        }
        var timelinesOrderAsc =
                timelines.stream()
                        .sorted(Comparator.comparing(ProjectStatusVo::getCreatedTime))
                        .collect(Collectors.toList());
        var activities = getActivity(projectId);
        var siteInspected = getStatusActivity(activities.stream(), SITE_INSPECTED.getDisplay());
        Optional.ofNullable(siteInspected).ifPresent(esModel::setSiteInspectedTime);
        setStatusTime(projectId, activities, timelinesOrderAsc, ASSIGNED_TO_PILOT);
        setStatusTime(projectId, activities, timelinesOrderAsc, IMAGE_UPLOADED);
        setStatusTime(projectId, activities, timelinesOrderAsc, RETURNED_TO_CLIENT);
        setStatusTime(projectId, activities, timelinesOrderAsc, CLIENT_RECEIVED);
        if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
            var esModelUpdater = ProjectEsModelUpdater.toBuilder().setProjectId(projectId);
            Optional.ofNullable(siteInspected).ifPresent(esModelUpdater::setSiteInspectedTime);
            projectEsService.updatePartial(esModelUpdater.build());
        } else {
            projectEsService.syncToEsFromProjectEsModel(esModel, false);
        }
    }

    private void setStatusTime(
            long projectId,
            List<? extends Activity> activities,
            List<ProjectStatusVo> timelinesOrderAsc,
            NewProjectStatusEnum statusEnum) {
        var statusTime = getStatusActivity(activities.stream(), statusEnum.getDisplay());
        if (statusTime != null) {
            timelinesOrderAsc.stream()
                    .filter(timeline -> timeline.getStatus().getCode() == statusEnum.getCode())
                    .findFirst()
                    .ifPresent(status -> status.setCreatedTime(statusTime));
        }
    }

    private List<? extends Activity> getActivity(long projectId) {
        return activityManager.getActivities(
                ActivityQuery.builder()
                        .projectId(projectId)
                        .entityType(PROJECT.name())
                        .fieldName(STATUS.name())
                        .sortBy(Collections.singletonList("createdAt"))
                        .sortDirection("asc")
                        .build());
    }

    @Nullable
    private Long getStatusActivity(Stream<? extends Activity> source, String status) {
        return source.filter(activity -> status.equals(activity.getValue()))
                .findFirst()
                .map(Activity::getCreatedAt)
                .map(Instant::toEpochMilli)
                .orElse(null);
    }

    @PutMapping("/count/{projectId:\\d+}")
    public void updateNumberOfOutbuildingsAndInteriorRooms(@PathVariable long projectId) {
        projectImageService.updateNumberOfOutbuildingsAndInteriorRooms(projectId);
    }
}
