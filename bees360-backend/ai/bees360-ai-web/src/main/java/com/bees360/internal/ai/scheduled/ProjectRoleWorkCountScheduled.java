package com.bees360.internal.ai.scheduled;

import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.internal.ai.entity.enums.UserAuthEnum;
import com.bees360.internal.ai.entity.vo.ProjectRoleWorkVo;
import com.bees360.internal.ai.entity.vo.RoleQueryResult;
import com.bees360.internal.ai.grpc.service.GrpcProjectRoleWorkComponent;
import com.bees360.internal.ai.service.AiNotificationService;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.util.date.DateUtil;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2020/07/15 18:11
 */
@Slf4j
@Component
public class ProjectRoleWorkCountScheduled {

    private final ProjectEsService projectEsService;

    private final GrpcProjectRoleWorkComponent grpcProjectRoleWorkComponent;

    @Autowired
    private AiNotificationService aiNotificationService;

    public ProjectRoleWorkCountScheduled(ProjectEsService projectEsService,
        GrpcProjectRoleWorkComponent grpcProjectRoleWorkComponent) {
        this.projectEsService = projectEsService;
        this.grpcProjectRoleWorkComponent = grpcProjectRoleWorkComponent;
    }

    // TODO 去掉这里有统计邮件，由 {@link ReviewerWorkloadStatisticsScheduled}取代
    public void calProjectWorkCount() {
        LocalDateTime startDate = LocalDateTime.now().minusHours(1).minusDays(7);
        LocalDateTime endDate = LocalDateTime.now().minusHours(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATE_FORMAT);

        Long statusStartTime = ZonedDateTime.of(startDate,
            ZoneId.of(AmericaTimeZone.US_CENTRAL)).toInstant().toEpochMilli();
        Long statusEndTime = ZonedDateTime.of(endDate,
            ZoneId.of(AmericaTimeZone.US_CENTRAL)).toInstant().toEpochMilli();

        List<RoleQueryResult> claimResults = projectEsService.queryRoleCountProject(statusStartTime,
            statusEndTime, UserAuthEnum.ROLE_ADJUSTER.getAuth(), true);

        List<RoleQueryResult> underwritingResults = projectEsService.queryRoleCountProject(statusStartTime,
            statusEndTime, UserAuthEnum.ROLE_REVIEWER.getAuth(), false);

        ProjectRoleWorkVo roleWorkVo = new ProjectRoleWorkVo();
        roleWorkVo.setStartDate(startDate.format(formatter));
        roleWorkVo.setEndData(endDate.format(formatter));
        roleWorkVo.setClaimRes(claimResults);
        roleWorkVo.setUnderRes(underwritingResults);
        grpcProjectRoleWorkComponent.syncProjectRoleWorkData(roleWorkVo);
    }
}
