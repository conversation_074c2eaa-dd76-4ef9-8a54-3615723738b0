package com.bees360.event;

import com.bees360.event.registry.CronTriggerEveryMondayAt2AmBeijing;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.ProcessorWorkloadStatisticsScheduledBeijing;

import java.io.IOException;

/**
 * 该类是一个定时任务监听器，每周一凌晨2点触发执行处理器月度累计工作负载统计任务。
 */
public class ProcessorMonthlyAccumulatedWorkloadStaticsScheduledBeijingCronTrigger
        extends AbstractNamedEventListener<CronTriggerEveryMondayAt2AmBeijing> {

    private final ProcessorWorkloadStatisticsScheduledBeijing
            processorWorkloadStatisticsScheduledBeijing;

    public ProcessorMonthlyAccumulatedWorkloadStaticsScheduledBeijingCronTrigger(
            ProcessorWorkloadStatisticsScheduledBeijing
                    processorWorkloadStatisticsScheduledBeijing) {
        this.processorWorkloadStatisticsScheduledBeijing =
                processorWorkloadStatisticsScheduledBeijing;
    }

    @Override
    public void handle(CronTriggerEveryMondayAt2AmBeijing cronTriggerEveryMondayAt2AmBeijing)
            throws IOException {
        processorWorkloadStatisticsScheduledBeijing.sendProcessorMonthlyAccumulatedWorkloadStatics();
    }
}
