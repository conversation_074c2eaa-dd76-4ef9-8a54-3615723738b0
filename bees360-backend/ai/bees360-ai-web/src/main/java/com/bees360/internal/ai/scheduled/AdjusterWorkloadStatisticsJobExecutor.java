package com.bees360.internal.ai.scheduled;

import com.bees360.internal.ai.entity.dto.ClaimWorkloadStatistics;
import com.bees360.internal.ai.service.AiNotificationService;
import com.bees360.internal.ai.service.ProjectWorkloadStatisticService;
import com.bees360.job.registry.AdjusterWorkloadStatisticsJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.user.UserProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bees360.commons.lang.time.AmericaTimeZone.US_CENTRAL;

@Log4j2
@Component
@RequiredArgsConstructor
public class AdjusterWorkloadStatisticsJobExecutor extends AbstractJobExecutor<AdjusterWorkloadStatisticsJob> {

    private final UserProvider userProvider;

    private final AiNotificationService aiNotificationService;

    private final ProjectWorkloadStatisticService projectWorkloadStatisticService;

    private static final ZoneId US_CENTER_ZONE = TimeZone.getTimeZone(US_CENTRAL).toZoneId();

    @Override
    protected void handle(AdjusterWorkloadStatisticsJob job) throws IOException {
        Instant startTime = job.getStartTime();
        Instant endTime = job.getEndTime();
        // 得到所有项目的统计
        Collection<ClaimWorkloadStatistics> statistics = projectWorkloadStatisticService.getAdjusterWorkloadStatistic(startTime, endTime);
        // 将所有项目按照用户分组
        Map<String, Collection<ClaimWorkloadStatistics>> detailStatisticsGroupByUser =
            statistics.stream()
                .collect(
                    Collectors.toMap(
                        ClaimWorkloadStatistics::getStaffId,
                        Collections::singleton,
                        (a1, a2) ->
                            Stream.of(a1, a2)
                                .flatMap(Collection::stream)
                                .collect(Collectors.toSet())));
        // 将所有项目按照用户分组并聚合统计
        Map<String, ClaimWorkloadStatistics> summaryStatistic =
            statistics.stream()
                .collect(
                    Collectors.toMap(
                        ClaimWorkloadStatistics::getStaffId,
                        a -> a,
                        (a1, a2) ->
                            new ClaimWorkloadStatistics(
                                a1.getStaffId(),
                                a1.getStaffName(),
                                a1.getDarCount()
                                    + a2.getDarCount(),
                                a1.getEstimateCount()
                                    + a2.getEstimateCount(),
                                a1.getThreeDCount()
                                    + a2.getThreeDCount(),
                                null)));

        // send to staff
        if (job.isSendToStaff()) {
            log.info("Start to send adjuster workload statistics email to staff '{}'",
                Arrays.toString(detailStatisticsGroupByUser.keySet().toArray()));
            sendEmailToStaff(detailStatisticsGroupByUser, summaryStatistic, startTime, endTime);
        }

        // send to admin
        aiNotificationService.sendWorkloadStatisticsToAdmin(
            LocalDate.ofInstant(startTime, US_CENTER_ZONE),
            LocalDate.ofInstant(endTime, US_CENTER_ZONE),
            summaryStatistic.values().stream()
                .sorted(Comparator.comparing(ClaimWorkloadStatistics::getStaffName))
                .collect(Collectors.toList()),
            statistics.stream()
                .sorted(Comparator.comparing(ClaimWorkloadStatistics::getStaffName))
                .collect(Collectors.toList()),
            job.getRecipients());
        log.info("Success send adjuster workload statistics email from {} to {}.", startTime, endTime);
    }

    private void sendEmailToStaff(
        Map<String, Collection<ClaimWorkloadStatistics>> detailStatisticsGroupByUser,
        Map<String, ClaimWorkloadStatistics> summaryStatistic,
        Instant startTime,
        Instant endTime) {

        detailStatisticsGroupByUser.forEach(
            (key, value) -> {
                String email = getEmail(key);
                Optional.ofNullable(email)
                    .ifPresent((e) -> doSend(key, e, value, summaryStatistic, startTime, endTime));
            });
    }

    private String getEmail(String userId) {
        try {
            return userProvider.getUser(userId).getEmail();
        } catch (NoSuchElementException e) {
            log.warn("Failed to get user '{}' when try to send staff workload email.", userId);
            return null;
        }
    }

    private void doSend(
        String key,
        String email,
        Collection<ClaimWorkloadStatistics> value,
        Map<String, ClaimWorkloadStatistics> summaryStatistic,
        Instant startTime,
        Instant endTime) {
        aiNotificationService.sendWorkloadStatisticsToStaff(
            LocalDate.ofInstant(startTime, US_CENTER_ZONE),
            LocalDate.ofInstant(endTime, US_CENTER_ZONE),
            Collections.singleton(summaryStatistic.get(key)),
            value.stream()
                .sorted(Comparator.comparing(ClaimWorkloadStatistics::getStaffName))
                .collect(Collectors.toList()),
            Collections.singletonList(email));
    }
}
