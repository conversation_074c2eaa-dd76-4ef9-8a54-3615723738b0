package com.bees360.event;

import com.bees360.event.registry.CronTriggerEveryMondayAt2AmCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.ReviewerWorkloadStatisticsScheduled;

import java.io.IOException;

/**
 * 该类是一个定时任务触发器，每周一凌晨2点触发执行评审员月度累计工作量统计的发送任务。
 */
public class ReviewerMonthlyAccumulatedWorkloadStaticsScheduledCronTrigger
        extends AbstractNamedEventListener<CronTriggerEveryMondayAt2AmCst> {

    private final ReviewerWorkloadStatisticsScheduled reviewerWorkloadStatisticsScheduled;

    public ReviewerMonthlyAccumulatedWorkloadStaticsScheduledCronTrigger(
            ReviewerWorkloadStatisticsScheduled reviewerWorkloadStatisticsScheduled) {
        this.reviewerWorkloadStatisticsScheduled = reviewerWorkloadStatisticsScheduled;
    }

    @Override
    public void handle(CronTriggerEveryMondayAt2AmCst cronTriggerEveryMondayAt2AmCst)
            throws IOException {
        reviewerWorkloadStatisticsScheduled.sendReviewerMonthlyAccumulatedWorkloadStatics();
    }
}
