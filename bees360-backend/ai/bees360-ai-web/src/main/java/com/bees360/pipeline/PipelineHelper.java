package com.bees360.pipeline;

import com.bees360.internal.ai.exchange.ai2client.DashBoardProto;
import com.bees360.util.Iterables;
import com.google.protobuf.StringValue;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Objects;
import java.util.stream.Collectors;

@Log4j2
public class PipelineHelper {
    public PipelineHelper(PipelineService pipelineService) {
        this.pipelineService = pipelineService;
        log.info("Created '{}(pipelineService={}'", this, this.pipelineService);
    }

    private final PipelineService pipelineService;

    public Iterable<Long> getProjectId(
            Iterable<DashBoardProto.ProjectEsQueryParam.PipelineTaskQuery> queries) {
        var set = Iterables.toSet(queries);
        if (set.isEmpty()) {
            return Collections.emptyList();
        }
        var taskQueries =
                Iterables.toStream(queries)
                        .map(
                                query -> {
                                    var builder = Message.PipelineTaskQueryRequest.newBuilder();
                                    builder.setKey(query.getKey());
                                    var ownerId = query.getOwnerId();
                                    if (ownerId != StringValue.getDefaultInstance()) {
                                        builder.setOwnerId(query.getOwnerId());
                                    }
                                    builder.setStartAt(query.getStartAt());
                                    builder.setEndAt(query.getEndAt());
                                    if (query.getStatusCount() > 0) {
                                        builder.addAllStatus(
                                                query.getStatusList().stream()
                                                        .map(Message.PipelineStatus::forNumber)
                                                        .collect(Collectors.toSet()));
                                    }
                                    return PipelineTaskQuery.from(builder.build());
                                })
                        .collect(Collectors.toSet());
        return Iterables.toStream(pipelineService.findPipelineIdByTask(taskQueries))
                .filter(StringUtils::isNumeric)
                .map(Long::parseLong)
                .collect(Collectors.toSet());
    }
}
