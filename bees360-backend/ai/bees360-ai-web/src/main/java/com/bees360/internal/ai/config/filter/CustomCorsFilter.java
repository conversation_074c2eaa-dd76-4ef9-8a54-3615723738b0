package com.bees360.internal.ai.config.filter;

import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> Yang
 * @date 2019/10/22 17:57
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
@Configuration
public class CustomCorsFilter implements Filter {

    private CorsAccessControlProperties corsAccessControlProperties;

    public CustomCorsFilter(CorsAccessControlProperties corsAccessControlProperties) {
        this.corsAccessControlProperties = corsAccessControlProperties;
    }


    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        System.out.println(filterConfig.getInitParameterNames());
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
        throws IOException, ServletException {
        HttpServletRequest servletRequest = (HttpServletRequest)request;
        HttpServletResponse servletResponse = (HttpServletResponse)response;

        String origin = servletRequest.getHeader("Origin");

        if (isOriginAllowed(origin)) {
            servletResponse.setHeader("Access-Control-Allow-Origin", origin);
            servletResponse.setHeader("Access-Control-Allow-Methods",
                corsAccessControlProperties.getAllowed().getMethods());
            servletResponse.setHeader("Access-Control-Allow-Headers",
                corsAccessControlProperties.getAllowed().getHeaders());
            servletResponse.addHeader("Access-Control-Expose-Headers",
                corsAccessControlProperties.getExposed().getHeaders());
            servletResponse.addHeader("Access-Control-Allow-Credentials",
                String.valueOf(corsAccessControlProperties.getAllowed().isCredentials()));
            servletResponse.setHeader("Access-Control-Max-Age",
                String.valueOf(corsAccessControlProperties.getMaxAge()));
        } else {
            servletResponse.setHeader("Access-Control-Allow-Origin", null);
        }

        if ("OPTIONS".equals(servletRequest.getMethod())) {
            servletResponse.setStatus(HttpServletResponse.SC_OK);
        } else {
            chain.doFilter(servletRequest, servletResponse);
        }
    }

    private boolean isOriginAllowed(String origin) {
        // 请不要用 StringUtils 替换该判断，减少对包的依赖
        if (origin == null || origin.isEmpty()) {
            return false;
        }
        String originWhitesStr = corsAccessControlProperties.getAllowed().getOriginWhites();
        if (originWhitesStr != null && !originWhitesStr.isEmpty()) {
            List<String> originWhites = Arrays.asList(originWhitesStr.split(","));
            for (String originWhite : originWhites) {
                if (origin.equals(originWhite)) {
                    return true;
                }
            }
        }
        String originRegexesStr = corsAccessControlProperties.getAllowed().getOriginRegexes();
        if (originRegexesStr != null && !originRegexesStr.isEmpty()) {
            List<String> originRegexes = Arrays.asList(originRegexesStr.split(","));
            for (String originRegex : originRegexes) {
                if (isOriginMatch(origin, originRegex)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean isOriginMatch(String origin, String originRegex) {
        Pattern p = Pattern.compile(originRegex);
        Matcher matcher = p.matcher(origin);
        return matcher.matches();
    }

    @Override
    public void destroy() {

    }


}
