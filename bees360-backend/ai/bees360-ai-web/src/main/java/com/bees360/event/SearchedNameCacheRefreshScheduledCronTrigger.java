package com.bees360.event;

import com.bees360.event.registry.CronTriggerEverySundayAt0AmBeijing;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.SearchedNameCacheRefreshScheduled;
import java.io.IOException;

/**
 * 该类是一个定时任务监听器，每周日北京时间0点触发刷新搜索名称缓存的操作。
 */
public class SearchedNameCacheRefreshScheduledCronTrigger extends AbstractNamedEventListener<CronTriggerEverySundayAt0AmBeijing> {

    private final SearchedNameCacheRefreshScheduled searchedNameCacheRefreshScheduled;

    public SearchedNameCacheRefreshScheduledCronTrigger(SearchedNameCacheRefreshScheduled searchedNameCacheRefreshScheduled) {
        this.searchedNameCacheRefreshScheduled = searchedNameCacheRefreshScheduled;
    }

    @Override
    public void handle(CronTriggerEverySundayAt0AmBeijing cronTriggerEverySundayAt0AmBeijing) throws IOException {
        searchedNameCacheRefreshScheduled.searchNameRefresh();
    }
}
