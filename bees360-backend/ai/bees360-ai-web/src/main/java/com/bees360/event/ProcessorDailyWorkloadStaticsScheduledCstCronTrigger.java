package com.bees360.event;

import com.bees360.event.registry.CronTriggerDailyAt2AmCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.ProcessorWorkloadStatisticsScheduledCst;

import java.io.IOException;

/**
 * 定时在每天凌晨2点CST时间触发处理器工作负载统计任务
 */
public class ProcessorDailyWorkloadStaticsScheduledCstCronTrigger extends AbstractNamedEventListener<CronTriggerDailyAt2AmCst> {

    private final ProcessorWorkloadStatisticsScheduledCst processorWorkloadStatisticsScheduledCst;

    public ProcessorDailyWorkloadStaticsScheduledCstCronTrigger(ProcessorWorkloadStatisticsScheduledCst processorWorkloadStatisticsScheduledCst) {
        this.processorWorkloadStatisticsScheduledCst = processorWorkloadStatisticsScheduledCst;
    }

    @Override
    public void handle(CronTriggerDailyAt2AmCst cronTriggerDailyAt2AmBeijing) throws IOException {
        processorWorkloadStatisticsScheduledCst.sendProcessorDailyWorkloadStatics();
    }
}
