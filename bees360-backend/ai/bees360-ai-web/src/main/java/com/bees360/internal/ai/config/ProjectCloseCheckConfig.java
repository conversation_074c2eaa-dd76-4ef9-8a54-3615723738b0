package com.bees360.internal.ai.config;

import com.bees360.customer.Customer;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.internal.ai.entity.dto.ProjectsStatusModifierDto;
import com.bees360.internal.ai.entity.dto.UpdateFailedDto;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.exchange.ai2client.CommonProto;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.Message;
import com.bees360.report.service.impl.ProjectReportFileServiceImpl;
import com.bees360.util.Iterables;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum;

@Configuration
@EnableConfigurationProperties
@Log4j2
public class ProjectCloseCheckConfig {
    private static final String PROJECT_CLOSE_CHECK_COMPANY_REPORT_UNSATISFIED_REASON = "Missing an approved %s";

    @Bean
    @ConfigurationProperties(prefix = "project.state.close-condition.customer-report-condition")
    Map<String, Map<ProjectServiceTypeEnum, CompanyReportCheckForceProperty>> companyReportCheckForceCondition() {
        return new HashMap<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "project.status.update-condition.state")
    Map<ProjectStateEnum, Set<AiProjectStatusEnum>> notAllowedUpdatedStatus() {
        return new HashMap<>();
    }

    @Data
    private static class CompanyReportCheckForceProperty {
        private List<ReportTypeEnum> requiredReportType;
        private List<ReportTypeEnum> optionalReportType;
    }

    @Bean
    public BiFunction<Long, ProjectsStatusModifierDto, UpdateFailedDto> closeProjectChecker(
        ProjectReportProvider projectReportProvider,
        ProjectIIManager projectIIManager,
        Map<String, Map<ProjectServiceTypeEnum, CompanyReportCheckForceProperty>> companyReportCheckForceCondition) {
        return (projectId, projectsStatusModifierDto) -> {
            UpdateFailedDto.UpdateFailedDtoBuilder builder =
                UpdateFailedDto.builder()
                    .id(projectId)
                    .operationFeedback(CommonProto.UpdateFailed.OperationFeedback.ALLOWED);
            var project = projectIIManager.get(String.valueOf(projectId));
            if (projectsStatusModifierDto.getStatus()
                == AiProjectStatusEnum.CLIENT_RECEIVED.getCode()) {
                // project which is not yet completed not allow closing
                if (!checkProjectCompleted(project, projectReportProvider)) {
                    builder.message("The project is not yet completed.")
                        .operationFeedback(CommonProto.UpdateFailed.OperationFeedback.NOT_ALLOWED);
                } else if (!projectsStatusModifierDto.isChangeForce()) {
                    checkCompanyReport(
                        project, projectReportProvider, companyReportCheckForceCondition, builder);
                }
            }

            var updateFailedDto = builder.build();
            if (CommonProto.UpdateFailed.OperationFeedback.ALLOWED.equals(updateFailedDto.getOperationFeedback())) {
                return null;
            }

            return updateFailedDto;
        };
    }

    @Bean
    public BiFunction<Long, ProjectsStatusModifierDto, UpdateFailedDto> aiProjectStatusChecker(
            Bees360FeatureSwitch bees360FeatureSwitch,
            ProjectIIRepository projectIIRepository,
            Map<ProjectStateEnum, Set<AiProjectStatusEnum>> notAllowedUpdatedStatus) {
        log.info(
                "Create aiProjectStatusChecker with notAllowedUpdatedStatus : {}",
                notAllowedUpdatedStatus);
        return (projectId, projectsStatusModifierDto) -> {
            if (!bees360FeatureSwitch.isEnableCheckCloseProjectTargetStatus()) {
                return null;
            }
            var targetProjectStatus =
                    AiProjectStatusEnum.getEnum(projectsStatusModifierDto.getStatus());
            if (targetProjectStatus == null) {
                return null;
            }
            var allNotAllowedStatus =
                    notAllowedUpdatedStatus.values().stream()
                            .flatMap(Set::stream)
                            .collect(Collectors.toSet());
            if (!allNotAllowedStatus.contains(targetProjectStatus)) {
                return null;
            }
            var project = projectIIRepository.get(String.valueOf(projectId));
            if (notAllowedUpdatedStatus
                    .getOrDefault(project.getCurrentState().getState(), Set.of())
                    .contains(targetProjectStatus)) {
                return UpdateFailedDto.builder()
                        .id(projectId)
                        .operationFeedback(CommonProto.UpdateFailed.OperationFeedback.NOT_ALLOWED)
                        .message(
                            "The project is not allowed to update status to \"%s\" while not opening".formatted(
                                AiProjectStatusEnum.getEnum(
                                    projectsStatusModifierDto.getStatus())))
                        .build();
            }
            return null;
        };
    }

    private boolean checkProjectCompleted(
        ProjectII project,
        ProjectReportProvider projectReportProvider) {
        var approvedReports =
            projectReportProvider.find(project.getId(), null, Message.ReportMessage.Status.APPROVED);
        if (com.google.common.collect.Iterables.isEmpty(approvedReports)) {
            return false;
        }
        var reportTypes =
            Iterables.toStream(approvedReports)
                .map(ProjectReportFileServiceImpl::getReportType)
                .collect(Collectors.toList());
        if (project.getProjectType() == ProjectTypeEnum.UNDERWRITING
            && reportTypes.contains(ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT.getCode())) {
            return true;
        }
        var serviceType = ProjectServiceTypeEnum.getEnum(project.getServiceType().getCode());
        var serviceReportTypes =
            serviceType.getReportTypes().stream()
                .map(ReportTypeEnum::getCode)
                .collect(Collectors.toList());
        return CollectionUtils.containsAll(reportTypes, serviceReportTypes);
    }

    private void checkCompanyReport(
        ProjectII project,
        ProjectReportProvider projectReportProvider,
        Map<String, Map<ProjectServiceTypeEnum, CompanyReportCheckForceProperty>> companyReportCheckForceCondition,
        UpdateFailedDto.UpdateFailedDtoBuilder builder) {
        // On closing project, check company report condition
        var companyName = Optional.ofNullable(project.getContract().getInsuredBy()).map(Customer::getName).orElse(null);
        var serviceType = ProjectServiceTypeEnum.getEnum(project.getServiceType().getCode());
        var reportCheck = Optional.ofNullable(companyReportCheckForceCondition.get(companyName)).orElse(Collections.emptyMap()).get(serviceType);
        if (reportCheck == null) {
            return;
        }
        var approvedReportTypes =
            Iterables.toStream(
                    projectReportProvider.find(
                        String.valueOf(project.getId()),
                        null,
                        com.bees360.report.Message.ReportMessage.Status
                            .APPROVED))
                .map(ProjectReportFileServiceImpl::getReportType)
                .collect(Collectors.toList());
        var missingReports = checkMissingReports(approvedReportTypes, reportCheck);
        if (CollectionUtils.isNotEmpty(missingReports)) {
            builder.message(
                    PROJECT_CLOSE_CHECK_COMPANY_REPORT_UNSATISFIED_REASON.formatted(
                        String.join(" & ", missingReports)))
                .operationFeedback(CommonProto.UpdateFailed.OperationFeedback.NOT_FORCED);
        }
    }

    /**
     * 在 neededReportType 指定的报告存在，如果在 optionalReportType 指定的报告不存在，那么该 Project 需要强制才能删除
     *
     * @param approvedReportTypes approvedReportTypes
     * @param reportCheck         reportCheck
     * @return missingReports
     */
    private List<String> checkMissingReports(
        List<Integer> approvedReportTypes, CompanyReportCheckForceProperty reportCheck) {
        // check pre had reposts
        var containNeeded =
            CollectionUtils.containsAll(
                approvedReportTypes,
                Optional.of(
                        reportCheck.getRequiredReportType().stream()
                            .map(ReportTypeEnum::getCode)
                            .collect(Collectors.toList()))
                    .orElse(Collections.emptyList()));
        if (containNeeded) {
            var checkReportTypes =
                Optional.of(
                        reportCheck.getOptionalReportType().stream()
                            .map(ReportTypeEnum::getCode)
                            .collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
            var subtract = CollectionUtils.subtract(checkReportTypes, approvedReportTypes);
            if (CollectionUtils.isNotEmpty(subtract)) {
                return subtract.stream()
                    .map(ReportTypeEnum::getEnum)
                    .map(ReportTypeEnum::getDisplay)
                    .collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }
}
