package com.bees360.internal.ai.controller;

import com.bees360.internal.ai.base.ResponseJson;
import com.bees360.internal.ai.entity.ProjectExportData;
import com.bees360.internal.ai.service.ProjectExportDataManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/export-data")
public class BsExportDataController {

    @Autowired
    private ProjectExportDataManager compositedProjectExportDataManager;

    @GetMapping()
    public ResponseJson getByRelatedIdAndType(String relatedId, String relatedType) {
        return new ResponseJson(compositedProjectExportDataManager.getByRelatedIdAndType(relatedId, relatedType));
    }

    @PutMapping()
    public ResponseJson insertOrUpdateData(@RequestBody ProjectExportData param) {
        return new ResponseJson(compositedProjectExportDataManager.insertOrUpdateData(param));
    }

    @DeleteMapping()
    public void deleteExportData(String relatedId, String relatedType) {
        compositedProjectExportDataManager.deleteExportData(relatedId, relatedType);
    }

}
