package com.bees360.internal.ai.config;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.opensearch.client.RestClient;
import org.opensearch.client.RestClientBuilder;
import org.opensearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/04/28 17:35
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(ElasticsearchRestClientConfig.ESProperties.class)
public class ElasticsearchRestClientConfig {

    private static final int ADDRESS_LENGTH = 2;

    @ConfigurationProperties(prefix = "bees360.ai.elasticsearch")
    @Setter
    static class ESProperties {
        String username;
        String password;
        String ipAddr;
        String scheme = "http";
        boolean trustAllCerts;
    }

    @Bean
    public RestClientBuilder restClientBuilder(ESProperties properties) {
        HttpHost[] hosts =
                Arrays.stream(properties.ipAddr.split(","))
                        .map(e -> this.makeHttpHost(e, properties.scheme))
                        .filter(Objects::nonNull)
                        .toArray(HttpHost[]::new);
        log.info("hosts:{}", Arrays.toString(hosts));
        var builder = RestClient.builder(hosts).setHttpClientConfigCallback(
            httpAsyncClientBuilder ->
                configHttpClientConfigCallback(properties, httpAsyncClientBuilder));
        return builder;
    }

    private HttpAsyncClientBuilder configHttpClientConfigCallback(
        ESProperties properties, HttpAsyncClientBuilder httpAsyncClientBuilder) {
        if (properties.username != null && properties.password != null) {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(
                AuthScope.ANY,
                new UsernamePasswordCredentials(properties.username, properties.password));
            httpAsyncClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
        }
        if (properties.trustAllCerts) {
            log.info("Trust all certs when connect to {}.", properties.ipAddr);
            trustAllCerts(httpAsyncClientBuilder);
        }
        return httpAsyncClientBuilder;
    }

    @SuppressWarnings({"java:S4423", "java:S4830", "java:S5527"})
    private HttpAsyncClientBuilder trustAllCerts(HttpAsyncClientBuilder builder) {
        builder.setSSLHostnameVerifier((s, sslSession) -> true);
        try {
            var sc = SSLContext.getInstance("SSL");
            var trustAllCerts =
                new TrustManager[] {
                    new X509TrustManager() {
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return null;
                        }

                        public void checkClientTrusted(
                            X509Certificate[] certs, String authType) {}

                        public void checkServerTrusted(
                            X509Certificate[] certs, String authType) {}
                    }
                };
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            return builder.setSSLContext(sc);
        } catch (Exception ex) {
            throw new IllegalStateException(ex);
        }
    }

    @Bean(name = "highLevelClient", destroyMethod = "close")
    public RestHighLevelClient highLevelClient(@Autowired RestClientBuilder restClientBuilder) {
        // restClientBuilder.setMaxRetryTimeoutMillis(60000);
        return new RestHighLevelClient(restClientBuilder);
    }


    /**
     * 127.0.0.1:9200 拆分构建httpHost
     * @param s
     * @return
     */
    private HttpHost makeHttpHost(String s, String scheme) {
        assert StringUtils.isNotEmpty(s);
        String[] address = s.split(":");
        if (address.length == ADDRESS_LENGTH) {
            String ip = address[0];
            int port = Integer.parseInt(address[1]);
            return new HttpHost(ip, port, scheme);
        } else {
            return null;
        }
    }
}
