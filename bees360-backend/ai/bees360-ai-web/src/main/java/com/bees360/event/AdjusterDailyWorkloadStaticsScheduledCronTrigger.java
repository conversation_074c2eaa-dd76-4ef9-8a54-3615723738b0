package com.bees360.event;

import com.bees360.event.registry.CronTriggerDailyAt2AmCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.AdjusterWorkloadStatisticsScheduled;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 定时任务类，每天凌晨2点触发并发送调解员每日工作量统计信息。
 */
@Log4j2
public class AdjusterDailyWorkloadStaticsScheduledCronTrigger extends AbstractNamedEventListener<CronTriggerDailyAt2AmCst> {

    private final AdjusterWorkloadStatisticsScheduled adjusterWorkloadStatisticsScheduled;

    public AdjusterDailyWorkloadStaticsScheduledCronTrigger(AdjusterWorkloadStatisticsScheduled adjusterWorkloadStatisticsScheduled) {
        this.adjusterWorkloadStatisticsScheduled = adjusterWorkloadStatisticsScheduled;
        log.info(
                "Created {}(adjusterWorkloadStatisticsScheduled={})",
                this,
                adjusterWorkloadStatisticsScheduled);
    }

    @Override
    public void handle(CronTriggerDailyAt2AmCst cronTriggerDailyAt2AmCst) throws IOException {
        adjusterWorkloadStatisticsScheduled.sendAdjusterDailyWorkloadStatics();
    }
}
