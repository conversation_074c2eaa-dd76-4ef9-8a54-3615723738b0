package com.bees360.internal.ai.controller;

import com.bees360.base.ResponseJson;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.common.protobuf.ProtoBeanUtils;
import com.bees360.internal.ai.common.exceptions.IllegalParameterException;

import com.bees360.internal.ai.common.exceptions.ServiceMessageException;
import com.bees360.internal.ai.common.msgcode.MsgCodeManager;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.dto.BatchUpdateResultDto;
import com.bees360.internal.ai.entity.dto.ProjectsStatusModifierDto;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.exchange.ai2client.CommonProto.BatchUpdateResult;
import com.bees360.internal.ai.exchange.ai2client.CommonProto.UpdateFailed;
import com.bees360.internal.ai.exchange.ai2client.ProjectProto;
import com.bees360.internal.ai.grpc.api.web2ai.Project;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectService;
import com.bees360.internal.ai.service.ProjectStatusService;
import com.bees360.user.User;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

import com.google.protobuf.DoubleValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Guanrong
 * @date 2019/08/12 17:56
 */
@Slf4j
@RestController
@RequestMapping("/projects")
public class ProjectController {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectEsService projectEsService;

    @Autowired
    private ProjectStatusService projectStatusService;

    @GetMapping("/{projectId:\\d+}")
    public Project.ProjectEsModel getProjectProperty(@PathVariable long projectId) {
        ProjectEsModel esModel = projectEsService.findProjectByProjectId(projectId);
        com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel.Builder builder = Project.ProjectEsModel
            .newBuilder();
        try {
            ProtoBeanUtils.toProtoBean(builder, esModel);
        } catch (IOException e) {
            log.error("getProjectProperty IOException error:{}", e.getMessage());
        }
        return builder.build();
    }

    @DeleteMapping("/{projectId:\\d+}")
    public ResponseJson deleteProject(@PathVariable long projectId) {
        projectService.deleteProject(projectId);
        return new ResponseJson();
    }

    @PutMapping("/status")
    public BatchUpdateResult batchUpdateProjectStatus(@AuthenticationPrincipal User user,
        @RequestBody ProjectProto.ProjectBatchUpdateStatus projectsStatus) {
        if (!AiProjectStatusEnum
            .getStatusCodes(AiProjectStatusEnum.PROJECT_CANCELED, AiProjectStatusEnum.ESTIMATE_COMPLETED,
                AiProjectStatusEnum.REPORT_ASSEMBLED, AiProjectStatusEnum.RETURNED_TO_CLIENT,
                AiProjectStatusEnum.CLIENT_RECEIVED).contains(projectsStatus.getStatus())) {
            throw new IllegalParameterException("Unsupported status.");
        }
        if (CollectionAssistant.isEmpty(projectsStatus.getProjectIdsList())) {
            throw new IllegalParameterException("Project id cannot be empty.");
        }
        log.info("batchUpdateProjectStatus projectIds:{}, status:{}, username:{}", projectsStatus.getProjectIdsList(),
            projectsStatus.getStatus(), user.getName());

        // client receive 权限要求是admin和reviewer
        Consumer<User> authorize =
                (e) -> {
                    if (!Objects.equals(
                            AiProjectStatusEnum.CLIENT_RECEIVED.getCode(),
                            projectsStatus.getStatus())) {
                        return;
                    }
                    if (e.getAllAuthority().contains("ROLE_ADMIN")
                            || e.getAllAuthority().contains("ROLE_REVIEWER")) {
                        return;
                    }
                    throw new ServiceMessageException(
                            MsgCodeManager.AI.AI_OPERATION_FORBIDDEN.CODE, "Access denied");
                };
        authorize.accept(user);

        BatchUpdateResultDto result =
                projectStatusService.batchUpdateStatus(
                        ProjectsStatusModifierDto.builder()
                                .status(projectsStatus.getStatus())
                                .projectIds(projectsStatus.getProjectIdsList())
                                .changeForce(projectsStatus.getChangeForce())
                                .build(),
                        user);
        List<UpdateFailed> failedList = ListUtil.toList(failed -> UpdateFailed.newBuilder()
            .setId(failed.getId()).setOperationFeedback(failed.getOperationFeedback())
            .setMessage(failed.getMessage()).build(), result.getFailed());
        return BatchUpdateResult.newBuilder().addAllSucceed(result.getSucceed()).addAllFailed(failedList).build();
    }

    @GetMapping("/{projectId:\\d+}/invoice/price")
    public DoubleValue getProjectInvoicePrice(@PathVariable long projectId) {
        BigDecimal fee = projectService.getProjectInvoicePrice(projectId);
        return Optional.ofNullable(fee)
                .map(f -> DoubleValue.newBuilder().setValue(f.doubleValue()).build())
                .orElse(DoubleValue.getDefaultInstance());
    }

    @PutMapping("/{projectId:\\d+}/image-score")
    public void updateImageScore(
            @AuthenticationPrincipal User user,
            @PathVariable long projectId,
            @RequestBody ProjectProto.ImageScoreMessage imageScore) {
        projectEsService.updateImageScore(
                projectId, user, imageScore.getPilotId(), imageScore.getImageScore());
    }
}
