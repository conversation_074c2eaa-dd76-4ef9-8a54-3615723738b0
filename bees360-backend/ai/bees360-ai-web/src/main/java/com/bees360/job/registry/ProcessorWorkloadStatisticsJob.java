package com.bees360.job.registry;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.Instant;
import java.util.List;

@JobPayload
@Getter
@AllArgsConstructor
public class ProcessorWorkloadStatisticsJob {
    private final Instant startTime;
    private final Instant endTime;
    private final List<String> recipients;
    private final List<String> specialChineseStaffs;
    private final String timeZone;
}
