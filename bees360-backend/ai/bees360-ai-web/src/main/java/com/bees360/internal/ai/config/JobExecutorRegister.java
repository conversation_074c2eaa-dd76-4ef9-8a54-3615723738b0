package com.bees360.internal.ai.config;

import com.bees360.job.JobDispatcher;
import com.bees360.job.JobExecutor;
import java.util.List;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * 切分创建JobDispatcher和注册JobExecutor，防止出现循环依赖。
 *
 * <AUTHOR>
 */
@Configuration
public class JobExecutorRegister {

    @Autowired
    private JobDispatcher rabbitJobDispatcher;

    @Autowired
    private List<JobExecutor> jobExecutors;

    @PostConstruct
    public void enlistJobExecutors() {
        jobExecutors.forEach(e -> rabbitJobDispatcher.enlist(e));
    }
}
