package com.bees360.internal.ai.config;

import com.bees360.api.InvalidArgumentException;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.util.Iterables;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.function.Consumer;

@Log4j2
@Configuration
public class HostaReportConfig {

    @Bean
    public Consumer<Long> hostaTaskCalculator(
        PipelineService pipelineService,
        HostaReportProperties properties) {
        var taskKey = properties.getTaskKey();
        var taskNotCompletedStatuses = properties.getTaskNotCompletedStatuses();
        if (StringUtils.isBlank(taskKey) || CollectionUtils.isEmpty(taskNotCompletedStatuses)) {
            log.warn(
                "hosta task key or not completed statuses is missing, calculator can not"
                    + " activate");
            return (projectId) -> {};
        }
        log.info("created {} (properties='{}')", this, properties);
        return (projectId) -> {
            log.info("set {} PipelineStatus '{}'", projectId, taskKey);
            var pipeline = pipelineService.findById(String.valueOf(projectId));
            if (pipeline == null) {
                log.info("Project {} hosta Pipeline not found.", projectId);
                return;
            }
            var task =
                Iterables.toStream(pipeline.getTask())
                    .filter(
                        t ->
                            StringUtils.equals(t.getKey(), taskKey)
                                && taskNotCompletedStatuses.contains(
                                t.getStatus()))
                    .findFirst()
                    .orElse(null);
            if (task == null) {
                log.info("Project {} hosta Pipeline task not found.", projectId);
                return;
            }
            setPipelineTaskStatus(
                pipelineService, projectId, taskKey, Message.PipelineStatus.DONE, null);
        };
    }

    private void setPipelineTaskStatus(
        PipelineService pipelineService,
        long projectId,
        String key,
        Message.PipelineStatus status,
        String comment) {
        try {
            pipelineService.setTaskStatus(String.valueOf(projectId), key, status, comment);
            log.info("Successfully set pipeline '{}' key '{}' to '{}'", projectId, key, status);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            // 若设置失败说明是solid中tag校验未通过,将该task设置为EERROR状态
            pipelineService.setTaskStatus(
                String.valueOf(projectId),
                key,
                Message.PipelineStatus.ERROR,
                e.getMessage());
        }
    }

    @Data
    @Component
    @ConfigurationProperties(prefix = "hosta.report")
    static class HostaReportProperties {
        /** hosta对应的tag任务 task key */
        private String taskKey;
        /** hosta对应的tag任务未完成状态集合 */
        private Set<Message.PipelineStatus> taskNotCompletedStatuses;
    }
}
