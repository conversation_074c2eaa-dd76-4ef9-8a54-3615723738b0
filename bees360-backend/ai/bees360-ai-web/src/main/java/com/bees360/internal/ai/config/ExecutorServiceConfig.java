package com.bees360.internal.ai.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ExecutorServiceConfig {

    @Value("${image.resource.executor.threadNum:8}")
    private int newImageThreadNum;

    @Bean
    public ExecutorService annotationImageExecutor() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("bees360-ai-image-annotation-%d").build();

        return TtlExecutors.getTtlExecutorService(Executors.newFixedThreadPool(newImageThreadNum, threadFactory));
    }
}
