package com.bees360.internal.ai.config.requestandresponse;

import com.bees360.internal.ai.common.exceptions.HttpRequestForBiddenException;
import com.bees360.internal.ai.common.exceptions.HttpRequestUnauthorizedException;
import com.bees360.internal.ai.common.exceptions.IllegalParameterException;
import com.bees360.internal.ai.common.exceptions.ResourceNotFoundException;
import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.common.exceptions.ServiceMessageException;
import com.bees360.internal.ai.common.msgcode.MCodeFactory;
import com.bees360.internal.ai.controller.ProjectController;
import com.bees360.internal.ai.exchange.ai2client.ResponseProto;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.exc.MismatchedInputException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Path;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.ConversionNotSupportedException;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * <AUTHOR> Guanrong
 */
@ControllerAdvice(basePackageClasses = ProjectController.class)
@ResponseBody
public class ControllerExceptionHandler {

	private static final String logExceptionFormat = "[EXIGENCE] Some thing wrong with the system: %s";
	private Logger logger = LoggerFactory.getLogger(ControllerExceptionHandler.class);

	private static final String DEFAULT_ERROR_CODE = "-1";

    /**
     * 业务异常，提示信息有异常中的<code>message</code>指定
     */
	@ExceptionHandler({ ServiceMessageException.class })
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public ResponseProto.ResponseErrorBody serviceMessageException(HttpServletRequest request, ServiceMessageException e) {
		return generateMessageCodeResponse(e.getMsgCode(), e.getMessage());
	}

    /**
     * 业务异常，提示信息有异常中的<code>code</code>按照配置指定
     */
	@ExceptionHandler({ ServiceException.class })
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public ResponseProto.ResponseErrorBody serviceException(HttpServletRequest request, ServiceException e) {
		String message = codeToMessage(e.getMsgCode());
		return generateMessageCodeResponse(e.getMsgCode(), message);
	}

    /**
     * {@link ResourceNotFoundException} 是自定义的一个异常，表示资源不存在，返回404错误
     */
	@ExceptionHandler(ResourceNotFoundException.class)
	@ResponseStatus(HttpStatus.NOT_FOUND)
	public ResponseProto.ResponseErrorBody resourceNotFoundException(HttpServletRequest request,
		ResourceNotFoundException e) {
		String message = "No resource found for " + request.getMethod() + " " + request.getServletPath();
		return generateErrorResponse(request, message, e);
	}

    /**
     * 参数错误
     */
	@ExceptionHandler(IllegalParameterException.class)
	@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public ResponseProto.ResponseErrorBody illegalParameterException(HttpServletRequest request,
		IllegalParameterException e) {
		logger.debug(e.getMessage(), e);
		List<ResponseProto.ValidError> errors = new ArrayList<>();
		return generateValidatorErrorResponse(errors, e);
	}

    /**
     * 参数错误
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public ResponseProto.ResponseErrorBody illegalArgumentException(HttpServletRequest request,
        IllegalParameterException e) {
        logger.info(e.getMessage(), e);
        List<ResponseProto.ValidError> errors = new ArrayList<>();
        return generateValidatorErrorResponse(errors, e);
    }

    /**
     * 认证不通过
     */
	@ExceptionHandler(HttpRequestUnauthorizedException.class)
	@ResponseStatus(HttpStatus.UNAUTHORIZED)
	public ResponseProto.ResponseErrorBody httpRequestUnAuthorizedException(HttpServletRequest request,
			HttpRequestUnauthorizedException e) {
		return generateErrorResponse(request, HttpStatus.UNAUTHORIZED.getReasonPhrase(), e);
	}

    /**
     * 授权不通过
     */
	@ExceptionHandler(HttpRequestForBiddenException.class)
	@ResponseStatus(HttpStatus.FORBIDDEN)
	public ResponseProto.ResponseErrorBody httpRequestForBiddenException(HttpServletRequest request,
			HttpRequestForBiddenException e) {
		return generateErrorResponse(request, HttpStatus.FORBIDDEN.getReasonPhrase(), e);
	}

	/**
	 * used for handling the exception occurs while validating the object type
	 * method argument. It happen in post, put, patch type request.
	 */
	@ExceptionHandler(MethodArgumentNotValidException.class)
	@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
	public ResponseProto.ResponseErrorBody handleMethodArgumentNotValid(MethodArgumentNotValidException ex) {
		return generateValidatorErrorResponse(ex.getBindingResult(), ex);
	}

	@ExceptionHandler(MismatchedInputException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public ResponseProto.ResponseErrorBody handleMismatchedInputException(HttpServletRequest request,
        MismatchedInputException ex) {

        ResponseProto.ValidError.ValidErrorType validErrorType =
            ResponseProto.ValidError.ValidErrorType.TYPE_MISMATCH;
        List<JsonMappingException.Reference> pathRefs = ex.getPath();
        JsonMappingException.Reference lastRef = pathRefs.get(pathRefs.size() - 1);

        String fieldName = lastRef.getFieldName();
        String message = "The type of parameter '" + fieldName + "' mismatch.";
        ResponseProto.ValidError validError = ResponseProto.ValidError.newBuilder()
            .setType(validErrorType)
            .setField(fieldName)
            .setMessage(message)
            .build();

	    return generateValidatorErrorResponse(Arrays.asList(validError), ex);
    }

	/**
	 * This exception reports the result of constraint violations. It occurs
	 * when the validator is used in controller method while is used for
	 * handling the get type request.
	 */
	@ExceptionHandler(ConstraintViolationException.class)
	@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
	public ResponseProto.ResponseErrorBody handleConstraintViolationException(HttpServletRequest request,
			ConstraintViolationException ex) {
		List<ResponseProto.ValidError> errors = new ArrayList<>();

		for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
			errors.add(toFieldNotValidError(violation));
		}

		return generateValidatorErrorResponse(errors, ex);
	}

	private ResponseProto.ValidError toFieldNotValidError(ConstraintViolation<?> violation) {
		Path.Node lastNode = null;
		for (Path.Node node : violation.getPropertyPath()) {
			lastNode = node;
		}
		String fieldName = lastNode.getName();

		return ResponseProto.ValidError.newBuilder()
		    .setType(ResponseProto.ValidError.ValidErrorType.INVALID)
		    .setField(fieldName)
		    .setMessage(violation.getMessage())
		    .build();
	}

    private ResponseProto.ValidError toFieldNotValidError(BindingResult result, FieldError error) {

        String message = error.getDefaultMessage();
        ResponseProto.ValidError.ValidErrorType validErrorType =
            ResponseProto.ValidError.ValidErrorType.INVALID;

        if ("typeMismatch".equals(error.getCode())) {
            message = "The parameter '" + error.getField() + "' should of type '"
                + result.getFieldType(error.getField()).getSimpleName().toLowerCase() + "'";
            validErrorType = ResponseProto.ValidError.ValidErrorType.TYPE_MISMATCH;
        }

		return ResponseProto.ValidError.newBuilder()
            .setType(validErrorType)
            .setField(error.getField())
            .setMessage(message)
            .build();
	}

	@ExceptionHandler(BindException.class)
	@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
	public ResponseProto.ResponseErrorBody handleBindException(HttpServletRequest request, BindException ex) {
		return generateValidatorErrorResponse(ex.getBindingResult(), ex);
	}

	@ExceptionHandler(ConversionNotSupportedException.class)
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseProto.ResponseErrorBody conversionNotSupportedException(HttpServletRequest request,
        ConversionNotSupportedException ex) {
		return generateErrorResponse(request, HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase(), ex);
	}

	@ExceptionHandler(HttpMediaTypeNotAcceptableException.class)
	@ResponseStatus(HttpStatus.NOT_ACCEPTABLE)
    public ResponseProto.ResponseErrorBody httpMediaTypeNotAcceptableException(HttpServletRequest request,
        HttpMediaTypeNotAcceptableException ex) {
        StringBuilder messageBuilder =
            new StringBuilder().append("The media type is not acceptable.").append(" Acceptable media types are ");
        ex.getSupportedMediaTypes().forEach(t -> messageBuilder.append(t + ", "));
        String message = messageBuilder.substring(0, messageBuilder.length() - 2);

        return generateErrorResponse(request, message, ex);
	}

	@ExceptionHandler(HttpMediaTypeNotSupportedException.class)
	@ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public ResponseProto.ResponseErrorBody httpMediaTypeNotSupportedException(HttpServletRequest request,
        HttpMediaTypeNotSupportedException ex) {
        StringBuilder messageBuilder = new StringBuilder().append(ex.getContentType())
            .append(" media type is not supported.").append(" Supported media types are ");
        ex.getSupportedMediaTypes().forEach(t -> messageBuilder.append(t + ", "));
        String message = messageBuilder.substring(0, messageBuilder.length() - 2);

        return generateErrorResponse(request, message, ex);
	}

	@ExceptionHandler(HttpMessageNotReadableException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseProto.ResponseErrorBody httpMessageNotReadableException(HttpServletRequest request,
        HttpMessageNotReadableException ex) {
	    if(ex.getCause() != null && ex.getCause() instanceof MismatchedInputException) {
            return handleMismatchedInputException(request, (MismatchedInputException) ex.getCause());
        }
        String message = "Problems parsing JSON";
        return generateErrorResponse(request, message, ex);
	}

	@ExceptionHandler(HttpRequestMethodNotSupportedException.class)
	@ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public ResponseProto.ResponseErrorBody httpRequestMethodNotSupportedException(HttpServletRequest request,
        HttpRequestMethodNotSupportedException ex) {
        StringBuilder messageBuilder = new StringBuilder().append(ex.getMethod())
            .append(" method is not supported for this request.").append(" Supported methods are ");

        ex.getSupportedHttpMethods().forEach(m -> messageBuilder.append(m + ","));
        String message = messageBuilder.substring(0, messageBuilder.length() - 2);

        return generateErrorResponse(request, message, ex);
	}

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
	@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public ResponseProto.ResponseErrorBody methodArgumentTypeMismatchExceptionHandler(HttpServletRequest request,
        MethodArgumentTypeMismatchException ex) {

        String message = "The parameter '" + ex.getName() + "' should of type '"
            + ex.getRequiredType().getSimpleName().toLowerCase() + "'";

        ResponseProto.ValidError validError = ResponseProto.ValidError
            .newBuilder().setType(ResponseProto.ValidError.ValidErrorType.TYPE_MISMATCH).setField(ex.getName())
            .setMessage(message).build();

        List<ResponseProto.ValidError> errors = Arrays.asList(validError);

        return generateValidatorErrorResponse(errors, ex);
    }

	@ExceptionHandler(MissingServletRequestParameterException.class)
	@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public ResponseProto.ResponseErrorBody missingServletRequestParameterException(HttpServletRequest request,
        MissingServletRequestParameterException ex) {

        String message = "Required parameter '" + ex.getParameterName() + "' is not present";

        ResponseProto.ValidError validError = ResponseProto.ValidError
            .newBuilder().setType(ResponseProto.ValidError.ValidErrorType.MISSING_FIELD)
            .setField(ex.getParameterName()).setMessage(message).build();

		List<ResponseProto.ValidError> errors = Arrays.asList(validError);

		return generateValidatorErrorResponse(errors, ex);
	}

	@ExceptionHandler(MissingServletRequestPartException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseProto.ResponseErrorBody missingServletRequestPartException(HttpServletRequest request,
        MissingServletRequestPartException ex) {
		return generateErrorResponse(request, ex.getMessage(), ex);
	}

	@ExceptionHandler(NoHandlerFoundException.class)
	@ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseProto.ResponseErrorBody noHandlerFoundException(HttpServletRequest request,
        NoHandlerFoundException ex) {

        String message = "No resource found for " + ex.getHttpMethod() + " " + ex.getRequestURL();
        return generateErrorResponse(request, message, ex);
	}

	@ExceptionHandler(TypeMismatchException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseProto.ResponseErrorBody typeMismatchException(HttpServletRequest request,
        TypeMismatchException ex) {
		return generateErrorResponse(request, HttpStatus.BAD_REQUEST.getReasonPhrase(), ex);
	}

	@ExceptionHandler(MissingPathVariableException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseProto.ResponseErrorBody missingPathVariableException(HttpServletRequest request,
        MissingPathVariableException ex) {
		return generateErrorResponse(request, HttpStatus.BAD_REQUEST.getReasonPhrase(), ex);
	}

	@ExceptionHandler()
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	public ResponseProto.ResponseErrorBody allException(HttpServletRequest request, Exception e) {
		return internalServerError(request, e);
	}

	private ResponseProto.ResponseErrorBody generateMessageCodeResponse(int code, String msg) {
		ResponseProto.ResponseErrorBody responseBody = ResponseProto.ResponseErrorBody.newBuilder()
			.setCode(code + "")
			.setMessage(msg)
			.build();
		return responseBody;
	}

	private String codeToMessage(int code) {
		return MCodeFactory.getMsg(code);
	}

    private ResponseProto.ResponseErrorBody generateErrorResponse(HttpServletRequest request, String message, Exception e) {
		if(e != null) {
            message = "%s %s %s".formatted(message, request.getMethod(), request.getServletPath());
			logger.warn(message, e);
		}
        return ResponseProto.ResponseErrorBody.newBuilder()
            .setCode(DEFAULT_ERROR_CODE)
            .setMessage(message)
            .build();
	}

    private ResponseProto.ResponseErrorBody generateValidatorErrorResponse(BindingResult result, Exception ex) {
        List<ResponseProto.ValidError> errors = new ArrayList<>();
        for (FieldError error : result.getFieldErrors()) {
            errors.add(toFieldNotValidError(result, error));
        }
        return generateValidatorErrorResponse(errors, ex);
    }

    private ResponseProto.ResponseErrorBody generateValidatorErrorResponse(List<ResponseProto.ValidError> errors, Exception ex) {
		if(ex != null) {
			logger.debug(ex.getMessage(), ex);
		}
        errors = (errors == null ? new ArrayList<>() : errors);

		ResponseProto.ValidErrors validErrors = ResponseProto.ValidErrors.newBuilder()
			.addAllErrors(errors)
			.build();

		ResponseProto.ResponseErrorBody responseBody = ResponseProto.ResponseErrorBody.newBuilder()
            .setMessage("Validation Failed")
            .setData(validErrors)
            .build();
		return responseBody;
    }

    public ResponseProto.ResponseErrorBody internalServerError(HttpServletRequest request, Exception e) {
        reportException(request, e);
		ResponseProto.ResponseErrorBody responseBody = ResponseProto.ResponseErrorBody.newBuilder()
			.setCode(DEFAULT_ERROR_CODE)
			.setMessage(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase())
			.build();
        return responseBody;
    }

	private <T extends Throwable> void reportException(HttpServletRequest request, T e) {
        String message = "%s when make a %s request to %s";
        message = message.formatted(e.getMessage(), request.getMethod(), request.getServletPath());
        logger.error(logExceptionFormat.formatted(message), e);
	}

}
