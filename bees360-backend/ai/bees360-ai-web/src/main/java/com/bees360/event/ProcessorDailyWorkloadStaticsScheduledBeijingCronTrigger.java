package com.bees360.event;

import com.bees360.event.registry.CronTriggerDailyAt2AmBeijing;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.scheduled.ProcessorWorkloadStatisticsScheduledBeijing;
import java.io.IOException;

/**
 * 定时在北京时间每天凌晨2点执行处理器工作负载统计任务
 */
public class ProcessorDailyWorkloadStaticsScheduledBeijingCronTrigger extends AbstractNamedEventListener<CronTriggerDailyAt2AmBeijing> {

    private final ProcessorWorkloadStatisticsScheduledBeijing processorWorkloadStatisticsScheduledBeijing;

    public ProcessorDailyWorkloadStaticsScheduledBeijingCronTrigger(ProcessorWorkloadStatisticsScheduledBeijing processorWorkloadStatisticsScheduledBeijing) {
        this.processorWorkloadStatisticsScheduledBeijing = processorWorkloadStatisticsScheduledBeijing;
    }

    @Override
    public void handle(CronTriggerDailyAt2AmBeijing cronTriggerDailyAt2AmBeijing) throws IOException {
        processorWorkloadStatisticsScheduledBeijing.sendProcessorDailyWorkloadStatics();
    }
}
