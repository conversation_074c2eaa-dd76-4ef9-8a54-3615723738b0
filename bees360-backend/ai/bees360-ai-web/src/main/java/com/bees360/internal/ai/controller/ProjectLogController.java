package com.bees360.internal.ai.controller;

import com.bees360.common.protobuf.ProtoBeanUtils;
import com.bees360.internal.ai.config.webannotation.ProtoGetMapping;
import com.bees360.internal.ai.entity.LogEntry;
import com.bees360.internal.ai.entity.ProjectLog;
import com.bees360.internal.ai.exchange.ai2client.ProjectLogProto;
import com.bees360.internal.ai.service.ProjectLogService;
import com.google.protobuf.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Comparator;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/05/06 12:30
 */
@Slf4j
@RestController
@RequestMapping("/projects/log")
public class ProjectLogController {

    @Autowired
    private ProjectLogService projectLogService;


    @ProtoGetMapping("/{projectId:\\d+}")
    public Message getProjectLogEntry(@PathVariable long projectId) throws IOException {
        ProjectLog projectLog = Optional.ofNullable(projectLogService.getById(projectId)).orElse(new ProjectLog(projectId));
        projectLog.setLogEntry(projectLog.getLogEntry()
            .stream()
            .sorted(Comparator.comparing(LogEntry::getCreatedTime)
                .reversed())
            .collect(Collectors.toList()));
        return ProtoBeanUtils.toProtoMessage(ProjectLogProto.ProjectLogMessage.newBuilder(), projectLog);
    }


}
