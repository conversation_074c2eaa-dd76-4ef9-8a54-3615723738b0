package com.bees360.report.entity.vo;

import com.bees360.report.entity.vo.summary.SummaryBldg;
import com.bees360.report.entity.vo.summary.SummaryExterior;
import com.bees360.report.entity.vo.summary.SummaryImage;
import com.bees360.report.entity.vo.summary.SummaryInterior;
import com.bees360.report.entity.vo.summary.SummaryInteriorElectric;
import com.bees360.report.entity.vo.summary.SummaryInteriorPlumbing;
import com.bees360.report.entity.vo.summary.SummaryRecommendation;
import com.bees360.report.entity.vo.summary.SummaryRisk;
import com.bees360.report.entity.vo.summary.SummaryRoof;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ReportSummaryVoTest {

    public static void main(String[] args) {
        ReportSummaryVo summaryVo = new ReportSummaryVo();
        summaryVo.setYearBuilt(1940);
        summaryVo.setLivingArea(10.0);
        summaryVo.setLotSize(245.0);
        summaryVo.setRisk(summaryRisk());
        summaryVo.setBldg(summaryBldg());
        summaryVo.setRoof(summaryRoof());
        summaryVo.setExterior(summaryExterior());
        summaryVo.setInterior(summaryInterior());
        summaryVo.setAddlStructures(Arrays.asList("hahah"));
        summaryVo.setHazards(Arrays.asList("dfheih"));
        summaryVo.setRecommendations(recommendationList());
        Gson gson = new GsonBuilder().serializeNulls().setPrettyPrinting().create();
        String json = gson.toJson(summaryVo);
        System.out.println(json);
    }

    private static List<SummaryRecommendation> recommendationList() {
        SummaryRecommendation re = new SummaryRecommendation();
        re.setText("kktygj");
        SummaryImage img = new SummaryImage();
        img.setId("242515");
        re.setImage(Arrays.asList(img));

        return Arrays.asList(re);
    }

    private static SummaryInterior summaryInterior() {
        SummaryInterior in = new SummaryInterior();
        in.setOverallCondition("Good");
        in.setHasVisibleLeaks(true);
        in.setHasExistingDamage(true);
        in.setPlumbing(plumbing());
        in.setElectric(electric());

        return in;
    }

    private static SummaryInteriorElectric electric() {
        SummaryInteriorElectric ele = new SummaryInteriorElectric();
        ele.setHasIneligiblePanel(true);
        return ele;
    }

    private static SummaryInteriorPlumbing plumbing() {
        SummaryInteriorPlumbing p = new SummaryInteriorPlumbing();
        p.setNoShutoffValve(true);
        p.setHasOldWaterHeater(true);
        p.setHasPoorWaterHeaterCondition(true);
        return p;
    }

    private static SummaryExterior summaryExterior() {
        SummaryExterior ext = new SummaryExterior();
        ext.setOverallCondition("441");
        ext.setSiding(map());
        ext.setHasShutters(true);
        ext.setHasPorch(true);
        ext.setHasStairsWithoutHandRails(true);
        ext.setHasYardDebris(true);
        ext.setHasDiscardedVehicles(true);
        ext.setHasTreeLimbs(true);
        ext.setHasPoolWithoutFence(true);
        ext.setHasDogPresent(true);
        ext.setNumDogPresent(10);
        ext.setComments(Arrays.asList("commm"));
        ext.setHasFirePit(true);

        return ext;
    }

    private static Map<String, Integer> map() {
        Map<String, Integer> map = new HashMap<String, Integer>();
        map.put("hahha", 100);
        return map;
    }

    private static SummaryRoof summaryRoof() {
        SummaryRoof roof = new SummaryRoof();
        roof.setOverallCondition("44");
        roof.setEstAge("dfdf");
        roof.setEstLife("424");
        roof.setGeometry(map());
        roof.setCoveringMaterial(Arrays.asList("tjykl"));
        roof.setHasSolarPanel(true);
        roof.setHasCurlingShingles(true);
        roof.setHasGranularLoss(true);
        roof.setHasPatchedAreas(true);
        roof.setHasTarp(true);
        roof.setMaterial(map());
        roof.setComments(Arrays.asList("45545"));
        roof.setHasAlgaeOrMoss(true);
        roof.setHasOtherRoofAnomalies(true);

        return roof;
    }

    private static SummaryBldg summaryBldg() {
        SummaryBldg bldg = new SummaryBldg();
        bldg.setOverallCondition("uj");
        bldg.setDwellingType("jk");
        bldg.setConstruction("ty");
        bldg.setConstructionOverWater(true);
        bldg.setGarage("tyui");
        bldg.setHvac("hvac");
        bldg.setNumStories(12);
        bldg.setHurricaneStraps(true);
        bldg.setDesignatedHistoricHome(true);
        bldg.setFoundation("ff");
        bldg.setManufacturedOrMobileHome("manu");
        bldg.setExteriorDamage(true);
        bldg.setOtherSidingAnomalies(true);
        return bldg;
    }

    private static SummaryRisk summaryRisk() {
        SummaryRisk risk = new SummaryRisk();
        risk.setOverallCondition("over");
        risk.setAreaEconomy("area");
        risk.setNeighborhood("nei");
        risk.setGatedCommunity(true);
        risk.setIsolatedDwelling(true);
        risk.setSeasonalDwelling(true);
        risk.setBusinessOperation("buss");
        risk.setRental(true);

        return risk;
    }
}
