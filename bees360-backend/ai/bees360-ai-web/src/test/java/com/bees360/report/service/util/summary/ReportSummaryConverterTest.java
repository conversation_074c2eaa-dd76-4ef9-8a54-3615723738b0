package com.bees360.report.service.util.summary;

import com.bees360.report.entity.vo.ReportSummaryVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.fail;

/**
 * <AUTHOR>
 */
public class ReportSummaryConverterTest {

    private ReportSummaryConverter reportSummaryConverter;

    @BeforeEach
    public void setUp() {
        reportSummaryConverter = new ReportSummaryConverter(new ObjectMapper());
    }

    @Test
    public void toSummaryVo() {
        ReportSummaryVo summaryVo = reportSummaryConverter.toSummaryVo("", ReportSummaryVo.class);
        assertNull(summaryVo, "summaryVo should be null when json is empty.");

        summaryVo = reportSummaryConverter.toSummaryVo("{}", ReportSummaryVo.class);
        assertNotNull(summaryVo, "summaryVo shouldn't be null when json is {}.");
    }

    @Test
    public void toSummaryJson() {
        String json = reportSummaryConverter.toSummaryJson(null);
        assertEquals("", json, "json should be empty when SummaryVo is null.");
    }
}
