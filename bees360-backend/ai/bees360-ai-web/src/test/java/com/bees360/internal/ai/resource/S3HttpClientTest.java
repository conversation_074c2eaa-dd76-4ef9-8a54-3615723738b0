package com.bees360.internal.ai.resource;

import com.bees360.resource.HttpResourceClient;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourceMetadata;
import com.bees360.util.ByteStrings;
import com.google.common.hash.Hashing;
import com.google.protobuf.ByteString;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 * @since 2020/9/21 12:51 下午
 **/
//@RunWith(SpringJUnit4ClassRunner.class)
public class S3HttpClientTest {
    //    @Test
    public void testUploadToRedirectUrl() throws Exception {
        final String url = "http://192.168.20.42:9090/resource/project/1003957/report/1600661697223PropertyImageReport";
        final Resource resource = Resource.of(ByteString.copyFrom(RandomUtils.nextBytes(10 * 1024 *1024)));
        //TODO
        //final HttpResourceClient client = HttpResourceClient.getDefaultInstance();
        final HttpResourceClient client = null;
        client.put(url, resource);
        Resource expected = client.get(url);
        assert expected != null;
        ResourceMetadata resourceMetadata = expected.getMetadata();
        expected = expected.buffered();
        assertEquals(expected.getMetadata().getContentMD5(), resource.getMetadata().getContentMD5());
    }
}
