package com.bees360.internal.ai.controller;

import com.bees360.internal.ai.exchange.ai2client.DataProcessProto;
import com.bees360.internal.ai.exchange.ai2client.ProjectProto;
import com.bees360.internal.ai.exchange.ai2client.ResponseProto;
import com.google.common.collect.Maps;
import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Guanrong
 * @date 2019/08/20 16:59
 */
public class ProtobufHttpClient {

    private static Class<ResponseProto.ResponseBody> errorResponseClass = ResponseProto.ResponseBody.class;

    public static Message get(String url, Map<String, String> headers, Map<String, String> params,
        Message.Builder builder) throws Exception {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        URIBuilder uriBuilder = new URIBuilder(url);
        setParameters(uriBuilder, params);

        HttpGet httpGet = new HttpGet(uriBuilder.build());
        setHeaders(httpGet, headers);

        return execute(httpclient, httpGet, builder);
    }

    private static void setHeaders(HttpRequestBase request, Map<String, String> headers) {
        request.setHeader("User-Agent",
            "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36");
        request.setHeader("Content-Type", "application/x-protobuf");
        if (headers == null) {
            return;
        }
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            request.setHeader(entry.getKey(), entry.getValue());
        }
    }

    private static void setParameters(URIBuilder uriBuilder, Map<String, String> parameters) {
        if (parameters == null) {
            return;
        }
        for (Map.Entry<String, String> entry : parameters.entrySet()) {
            uriBuilder.setParameter(entry.getKey(), entry.getValue());
        }
    }

    public static Message post(Message message, String url, Map<String, String> headers, Message.Builder builder)
        throws IOException {
        // 创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建http POST请求
        HttpPost httpPost = new HttpPost(url);
        HttpEntity httpEntity = new ByteArrayEntity(message.toByteArray());
        httpPost.setEntity(httpEntity);
        setHeaders(httpPost, headers);

        return execute(httpclient, httpPost, builder);
    }

    public static Message put(Message message, String url, Map<String, String> headers, Message.Builder builder)
        throws IOException {
        // 创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建http POST请求
        HttpPut httpPut = new HttpPut(url);
        setHeaders(httpPut, headers);

        return execute(httpclient, httpPut, builder);
    }

    public static Message patch(Message message, String url, Map<String, String> header, Message.Builder builder)
    throws IOException{
        // 创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建http Patch请求
        HttpPatch httpPatch = new HttpPatch(url);
        HttpEntity httpEntity = new ByteArrayEntity(message.toByteArray());
        httpPatch.setEntity(httpEntity);
        setHeaders(httpPatch, header);
        return execute(httpclient, httpPatch, builder);
    }

    public static Message delete(String url, Map<String, String> header, Message.Builder builder, Map<String, String> params) throws Exception{
        URIBuilder uriBuilder = new URIBuilder(url);
        setParameters(uriBuilder, params);

        // 创建http Patch请求
        HttpDelete httpDelete = new HttpDelete(uriBuilder.build());
        setHeaders(httpDelete, header);

        // 创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        return execute(httpclient, httpDelete, builder);
    }

    private static Message execute(CloseableHttpClient httpclient, HttpUriRequest request, Message.Builder builder)
        throws IOException {

        CloseableHttpResponse response = null;
        try {
            // 执行请求
            response = httpclient.execute(request);
            InputStream content = response.getEntity().getContent();
            switch (response.getStatusLine().getStatusCode()) {
                case 200: {
                    return execute200(content, builder);
                }
                case 400: {
                    return execute400(content);
                }
                case 401: {
                    return execute401(content);
                }
                case 403: {
                    return execute403(content);
                }
                case 404: {
                    return execute404(content);
                }
                case 415: {
                    return executeError(content);
                }
                case 419: {
                    return executeError(content);
                }
                case 422: {
                    return executeError(content);
                }
                case 500: {
                    return execute500(content);
                }
                default: {
                    System.err.println(content);
                    return null;
                }
            }
        } finally {
            if (response != null) {
                response.close();
            }
            httpclient.close();
        }
    }

    private static Message execute200(InputStream protobufStream, Message.Builder builder) throws IOException {
        return builder.mergeFrom(protobufStream).build();
    }

    private static Message execute400(InputStream protobufStream) throws IOException {
        return executeError(protobufStream);
    }

    private static Message execute401(InputStream protobufStream) throws IOException {
        return executeError(protobufStream);
    }

    private static Message execute403(InputStream protobufStream) throws IOException {
        return executeError(protobufStream);
    }

    private static Message execute404(InputStream protobufStream) throws IOException {
        return executeError(protobufStream);
    }

    private static Message execute500(InputStream protobufStream) throws IOException {
        return executeError(protobufStream);
    }

    private static Message executeError(InputStream protobufStream) throws IOException {
        Message message = com.google.protobuf.Internal.getDefaultInstance(errorResponseClass);
        return message.getParserForType().parseFrom(protobufStream);
    }

    /* ======== */

    private static String HOST = "http://127.0.0.1:9090";
    private static void testGetStage() throws Exception {
        String uri = HOST + "/projects/1003291/process-stage";
        DataProcessProto.DataProcessStage.Builder builder = DataProcessProto.DataProcessStage.newBuilder();
        Message message = ProtobufHttpClient.get(uri, getAuthHeaders(), null, builder);
        System.out.println(JsonFormat.printer().print(message));

    }

    private static void testPostModeling() throws IOException {
        String uri = HOST + "/projects/1003527/process-stage/modeling";
        DataProcessProto.DataProcessStage.Builder builder = DataProcessProto.DataProcessStage.newBuilder();
        DataProcessProto.ModelingParams modelingParams = DataProcessProto.ModelingParams.newBuilder()
            .setPmvsLevel(1)
            .setPmvsMaxAngle(20)
            .setPmvsThreshold(1.2)
            .setImageSize(1500)
            .build();

        Message message = ProtobufHttpClient.post(modelingParams, uri, getAuthHeaders(), builder);
        System.out.println(JsonFormat.printer().print(message));
    }

    private static void testGetPipelines() throws Exception {
        String uri = HOST + "/projects/1003291/pipelines/333";
        DataProcessProto.DataProcessStage.Builder builder = DataProcessProto.DataProcessStage.newBuilder();
        Message message = ProtobufHttpClient.get(uri, getAuthHeaders(), null, builder);
        System.out.println(JsonFormat.printer().print(message));

    }
    private static void testGetProjects() throws Exception {
        String uri = HOST + "/projects";
        Message.Builder builder = ProjectProto.ProjectPageResult.newBuilder();
        Map<String, String> params = new HashMap<String, String>();
        params.put("sort", "fullAddress_asc");
        params.put("createdTimeStart", "1542598506315");
        params.put("createdTimeEnd", "1542598506317");
        Message message = ProtobufHttpClient.get(uri, getAuthHeaders(), params, builder);
        System.out.println(message == null? null: JsonFormat.printer().print(message));
    }

    private static Map<String, String> getAuthHeaders() {
        Map<String, String > headers = Maps.newHashMap();
        headers.put("content-type", "application/x-protobuf");
        headers.put("Authorization", "Bearer your jwt");
        return headers;
    }

    private static void testGetLogs() throws Exception {
        String uri = HOST + "/projects/1003527/logs";
        ResponseProto.ResponseBody.Builder builder = ResponseProto.ResponseBody.newBuilder();
        Message message = ProtobufHttpClient.get(uri, getAuthHeaders(), null, builder);
       System.out.println(message == null? null: JsonFormat.printer().print(message));
    }

    public static void main(String[] args) throws Exception {
//        testGetLogs();
    }
}
