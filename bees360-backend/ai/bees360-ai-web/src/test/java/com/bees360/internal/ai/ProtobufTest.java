package com.bees360.internal.ai;

import com.bees360.api.Message;
import com.google.protobuf.util.JsonFormat;

import lombok.SneakyThrows;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class ProtobufTest {
    @Test
    @SneakyThrows
    void testProtobuf() {
        Assertions.assertDoesNotThrow(Message.ApiMessage::getDescriptor);
        var json = JsonFormat.printer().print(Message.ApiMessage.getDefaultInstance());
        var builder = Message.ApiMessage.newBuilder();
        JsonFormat.parser().merge(json, builder);
        Assertions.assertEquals(Message.ApiMessage.getDefaultInstance(), builder.build());
    }
}
