package com.bees360.internal.ai.config;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import com.bees360.codec.GsonCodec;
import com.bees360.contract.Contract;
import com.bees360.customer.Customer;
import com.bees360.entity.User;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.event.util.PipelineTaskChangedListeners;
import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.policy.Policy;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.report.ReportManager;
import com.bees360.report.ReportTypeEnum;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.service.ProjectReportFileService;
import com.google.common.util.concurrent.MoreExecutors;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;
import java.util.concurrent.Executor;

@SpringBootTest(
        classes = ProjectReportAutoApprovePipelineTaskConfigTest.Config.class,
        properties = "spring.config.location = classpath:application-test.yml")
class ProjectReportAutoApprovePipelineTaskConfigTest {

    @Import({
        InMemoryEventPublisher.class,
        ProjectReportAutoApprovePipelineTaskConfig.class,
    })
    @Configuration
    static class Config {
        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @MockBean ProjectIIRepository projectIIRepository;

    @MockBean ProjectReportProvider projectReportProvider;

    @MockBean ReportManager reportManager;

    @MockBean ProjectReportFileService projectReportFileService;

    @Autowired EventPublisher eventPublisher;

    GsonCodec gson = GsonCodec.INSTANCE;

    @Test
    void testAutoApproveFUR() throws ServiceException {
        String taskKey = "approve_fur";
        var project =
                randomUwProject(ServiceTypeEnum.FOUR_POINT, "approveFUR", "processedBy", true);
        var report = randomReport(Message.ReportMessage.Status.GENERATED);
        var projectId = project.getId();
        doReturn(project).when(projectIIRepository).findById(projectId);
        doReturn(List.of(report))
                .when(projectReportProvider)
                .find(eq(projectId), eq(ReportTypeEnum.FUR.getKey()), any());

        var event = buildTaskChangedEvent(projectId, taskKey, PipelineStatus.READY);
        eventPublisher.publish(
                PipelineTaskChangedListeners.getEventName(
                        event.getTaskDefKey(), event.getState().getStatus()),
                gson.encode(event));
        verify(reportManager).updateStatus(report.getId(), Message.ReportMessage.Status.SUBMITTED,
            String.valueOf(User.AI_ID));
        verify(projectReportFileService)
                .approveReport(
                        Long.parseLong(projectId),
                        User.AI_ID,
                        report.getId(),
                        String.valueOf(User.AI_ID));
    }

    @Test
    void testAutoApprovePIR() throws ServiceException {
        String taskKey = "approve_pir";
        var project =
                randomUwProject(ServiceTypeEnum.FOUR_POINT, "approvePIR", "processedBy", true);
        var report = randomReport(Message.ReportMessage.Status.GENERATED);
        var projectId = project.getId();
        doReturn(project).when(projectIIRepository).findById(projectId);
        doReturn(List.of(report))
                .when(projectReportProvider)
                .find(eq(projectId), eq(ReportTypeEnum.PIR.getKey()), any());

        var event = buildTaskChangedEvent(projectId, taskKey, PipelineStatus.READY);
        eventPublisher.publish(
                PipelineTaskChangedListeners.getEventName(
                        event.getTaskDefKey(), event.getState().getStatus()),
                gson.encode(event));
        verify(reportManager).updateStatus(report.getId(), Message.ReportMessage.Status.SUBMITTED,
            String.valueOf(User.AI_ID));
        verify(projectReportFileService)
                .approveReport(
                        Long.parseLong(projectId),
                        User.AI_ID,
                        report.getId(),
                        String.valueOf(User.AI_ID));
    }

    @Test
    void testAutoApproveINVWhenNoConfiguration() throws ServiceException {
        String taskKey = "approve_inv";
        var project =
                randomUwProject(ServiceTypeEnum.FOUR_POINT, "approvePIR", "processedBy", true);
        var report = randomReport(Message.ReportMessage.Status.GENERATED);
        var projectId = project.getId();
        doReturn(project).when(projectIIRepository).findById(projectId);
        doReturn(List.of(report))
                .when(projectReportProvider)
                .find(eq(projectId), eq(ReportTypeEnum.INV.getKey()), any());

        var event = buildTaskChangedEvent(projectId, taskKey, PipelineStatus.READY);
        eventPublisher.publish(
                PipelineTaskChangedListeners.getEventName(
                        event.getTaskDefKey(), event.getState().getStatus()),
                gson.encode(event));
        verify(reportManager, never()).updateStatus(anyString(), any(), anyString());
        verify(projectReportFileService, never())
                .approveReport(anyLong(), anyLong(), anyString(), anyString());
    }

    @Test
    void testAutoApproveFURWhenApprovedReport() throws ServiceException {
        String taskKey = "approve_fur";
        var project =
                randomUwProject(ServiceTypeEnum.FOUR_POINT, "approvePIR", "processedBy", true);
        var report = randomReport(Message.ReportMessage.Status.APPROVED);
        var projectId = project.getId();
        doReturn(project).when(projectIIRepository).findById(projectId);
        doReturn(List.of(report))
                .when(projectReportProvider)
                .find(eq(projectId), eq(ReportTypeEnum.FUR.getKey()), any());

        var event = buildTaskChangedEvent(projectId, taskKey, PipelineStatus.READY);
        eventPublisher.publish(
                PipelineTaskChangedListeners.getEventName(
                        event.getTaskDefKey(), event.getState().getStatus()),
                gson.encode(event));
        verify(reportManager, never()).updateStatus(anyString(), any(), anyString());
        verify(projectReportFileService, never())
                .approveReport(anyLong(), anyLong(), anyString(), anyString());
    }

    @Test
    void testAutoApproveFURWhenNoReportFound() throws ServiceException {
        String taskKey = "approve_fur";
        var project =
                randomUwProject(ServiceTypeEnum.FOUR_POINT, "approvePIR", "processedBy", true);
        var projectId = project.getId();
        doReturn(project).when(projectIIRepository).findById(projectId);
        doReturn(List.of())
                .when(projectReportProvider)
                .find(eq(projectId), eq(ReportTypeEnum.FUR.getKey()), any());

        var event = buildTaskChangedEvent(projectId, taskKey, PipelineStatus.READY);
        eventPublisher.publish(
                PipelineTaskChangedListeners.getEventName(
                        event.getTaskDefKey(), event.getState().getStatus()),
                gson.encode(event));
        verify(reportManager, never()).updateStatus(anyString(), any(), anyString());
        verify(projectReportFileService, never())
                .approveReport(anyLong(), anyLong(), anyString(), anyString());
    }

    public static PipelineTaskChanged buildTaskChangedEvent(
            String projectId, String taskKey, PipelineStatus status) {
        var event = new PipelineTaskChanged();
        event.setTaskDefKey(taskKey);
        event.setPipelineId(projectId);

        var state = new PipelineTaskChanged.State();
        state.setStatus(status);
        event.setState(state);
        return event;
    }

    private Report randomReport(Message.ReportMessage.Status status) {
        var report = mock(Report.class);
        var reportId = RandomStringUtils.randomNumeric(12);
        doReturn(reportId).when(report).getId();
        doReturn(status).when(report).getStatus();
        return report;
    }

    private ProjectII randomUwProject(
            ServiceTypeEnum serviceType, String insuredBy, String processedBy, boolean isRenewal) {
        var project = mock(ProjectII.class);
        var policy = mock(Policy.class);
        var contract = mock(Contract.class);
        var insuredByCustomer = mock(Customer.class);
        var processedByCustomer = mock(Customer.class);
        var projectId = "1" + RandomStringUtils.randomNumeric(4);
        doReturn(projectId).when(project).getId();
        doReturn(serviceType).when(project).getServiceType();

        doReturn(policy).when(project).getPolicy();
        doReturn(isRenewal).when(policy).isRenewal();

        doReturn(contract).when(project).getContract();
        doReturn(insuredByCustomer).when(contract).getInsuredBy();
        doReturn(processedByCustomer).when(contract).getProcessedBy();
        doReturn(insuredBy).when(insuredByCustomer).getCompanyKey();
        doReturn(processedBy).when(processedByCustomer).getCompanyKey();

        return project;
    }
}
