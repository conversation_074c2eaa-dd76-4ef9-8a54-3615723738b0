<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.bees360.ai</groupId>
		<artifactId>bees360-ai</artifactId>
		<version>${revision}${changelist}</version>
	</parent>
	<artifactId>bees360-ai-web</artifactId>
	<packaging>jar</packaging>
    <properties>
        <mainClass>com.bees360.internal.ai.Bees360AiApplication</mainClass>
    </properties>
	<dependencies>
		<dependency>
			<groupId>com.bees360.ai</groupId>
			<artifactId>bees360-ai-grpc</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
		</dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-http-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-image-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-image-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-http-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-http-auth</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-activity-api</artifactId>
        </dependency>
        <dependency>
          <groupId>com.bees360</groupId>
          <artifactId>bees360-activity-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-resource-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-pipeline-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-report-grpc-client</artifactId>
        </dependency>
        <!-- External project module -->
		<dependency>
			<groupId>com.bees360</groupId>
			<artifactId>bees360-report-spring-boot-start</artifactId>
			<version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.bees360</groupId>
                    <artifactId>bees360-web-grpc-proto</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>bcprov-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jetty</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-resource-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-address-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-grpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>co.realms9</groupId>
            <artifactId>realms9-secretsmanger-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-monitor-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-mail-job</artifactId>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <configuration>
                    <container>
                        <mainClass>${mainClass}</mainClass>
                    </container>
                    <extraDirectories>
                        <permissions>
                            <permission>
                                <file>/var/bees360/www/logs</file>
                                <mode>755</mode>
                            </permission>
                            <permission>
                                <file>/var/bees360/bees360ai</file>
                                <mode>755</mode>
                            </permission>
                        </permissions>
                    </extraDirectories>
                    <from>
                        <image>harbor.9realms.co/private/upstream-base:latest</image>
                    </from>
                </configuration>
                <executions>
                    <execution>
                        <id>upstream-base</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                        <configuration>
                            <from>
                                <image>harbor.9realms.co/private/upstream-base:latest</image>
                            </from>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>${mainClass}</mainClass>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
