<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.bees360.ai</groupId>
		<artifactId>bees360-ai</artifactId>
		<version>${revision}${changelist}</version>
	</parent>
	<artifactId>bees360-ai-grpc-proto</artifactId>
	<name>bees360-ai-grpc-proto</name>

	<properties>
		<protos.basedir>${project.basedir}/../../api</protos.basedir>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
		</dependency>

		<!-- [start] gRPC -->
		<dependency>
			<groupId>io.grpc</groupId>
			<artifactId>grpc-netty-shaded</artifactId>
		</dependency>
		<dependency>
			<groupId>io.grpc</groupId>
			<artifactId>grpc-protobuf</artifactId>
		</dependency>
		<dependency>
			<groupId>io.grpc</groupId>
			<artifactId>grpc-stub</artifactId>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- [end] gRPC -->
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.xolstice.maven.plugins</groupId>
				<artifactId>protobuf-maven-plugin</artifactId>
				<configuration>
					<!-- 参考: https://www.xolstice.org/protobuf-maven-plugin/usage.html -->
					<protocArtifact>com.google.protobuf:protoc:${protobuf-java.version}:exe:${os.detected.classifier}</protocArtifact>
					<!-- 默认为: src/main/proto/xxx.proto -->
					<protoSourceRoot>${protos.basedir}</protoSourceRoot>
					<!-- 默认为: ${project.build.directory}/generated-sources/protobuf/java，会自动添加为source，因为使用默认即可 -->
					<!-- <outputDirectory>${project.basedir}/src/main/java</outputDirectory> -->
					<!-- 当 clearOutputDirectory = true 时，每次执行都会清楚 outputDirectory 指定的目录下的所有文件 -->
					<!-- <clearOutputDirectory>false</clearOutputDirectory> -->
					<!-- If set to true, the compiler will generate a binary descriptor
						set file for the specified .proto files. Default value is false -->
					<!-- <writeDescriptorSet>true</writeDescriptorSet> -->
					<!-- If true and writeDescriptorSet has been set, the compiler will
						include all dependencies in the descriptor set making it "self-contained". -->
					<!-- <includeDependenciesInDescriptorSet>true</includeDependenciesInDescriptorSet> -->
                    <pluginParameter>
                        @generated=omit
                    </pluginParameter>
					<includes>
						<include>common/**/*.proto</include>
						<!-- ai对外开放的接口 -->
                        <include>ai/**/*.proto</include>
						<include>web2ai/**/*.proto</include>
						<include>ai2client/**/*.proto</include>
						<!-- ai调用的接口 -->
                        <include>ai2web/**/*.proto</include>
						<include>report2web/**/*.proto</include>
					</includes>
					<pluginId>grpc-java</pluginId>
					<pluginArtifact>io.grpc:protoc-gen-grpc-java:1.68.2:exe:${os.detected.classifier}</pluginArtifact>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>compile</goal>
							<goal>compile-custom</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
