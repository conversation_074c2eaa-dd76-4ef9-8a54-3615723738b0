<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.bees360.ai</groupId>
		<artifactId>bees360-ai</artifactId>
		<version>${revision}${changelist}</version>
	</parent>
	<artifactId>bees360-ai-entity</artifactId>

	<dependencies>
		<!-- 内部模块 -->
		<dependency>
			<groupId>com.bees360.ai</groupId>
			<artifactId>bees360-ai-grpc-proto</artifactId>
		</dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-api</artifactId>
        </dependency>
		<dependency>
			<groupId>com.bees360.commons</groupId>
			<artifactId>elasticsearch-support</artifactId>
		</dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-event-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-api</artifactId>
        </dependency>
		<dependency>
			<groupId>com.bees360</groupId>
			<artifactId>bees360-report-entity</artifactId>
			<version>${project.version}</version>
		</dependency>
	</dependencies>
</project>
