package com.bees360.internal.ai.entity.enums;

/**
 * <AUTHOR>
 * @date 2019/08/08 18:33
 */
public enum AiProcessStageStatusE {
    WAITING(1, "Waiting", "The stage is waiting to be processed."),
    PREPARING(2, "Preparing", "The stage is preparing to be processed."),
    READY(3, "Ready", "The stage is ready to be processed"),
    IN_PROCESS(4, "in Process", "The stage is in process."),
    FAILED(5, "Failed", "The stage is fail to be processed."),
    FINISHED(6, "Finished", "The stage is finished."),
    STOPPED(7, "Stopped", "The stage is stopped.")
    ;

    private final int code;
    private final String value;
    private final String message;

    AiProcessStageStatusE(int code, String value, String message) {
        if(value == null || value.isEmpty()) {
            throw new AssertionError("The value shouldn't be null or empty.");
        }
        if(message == null) {
            throw new AssertionError("The message shouldn't be null.");
        }
        this.code = code;
        this.value = value;
        this.message = message;
    }

    public String getValue() {
        return value;
    }
    public static AiProcessStageStatusE getEnum(String value) {
        for(AiProcessStageStatusE statusE: values()) {
            if(statusE.getValue().equals(value)) {
                return statusE;
            }
        }
        return null;
    }

    public String getMessage() {
        return message;
    }

    public int getCode() {
        return code;
    }
}
