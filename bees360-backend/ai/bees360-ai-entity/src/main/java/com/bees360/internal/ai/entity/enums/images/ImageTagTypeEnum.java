package com.bees360.internal.ai.entity.enums.images;

/**
 * <AUTHOR>
 * @date 2020/10/30 16:55
 */
public enum ImageTagTypeEnum {

    CATEGORY(10, "Category"),
    OBJECT(1, "Object"),
    SCOPE(2, "Scope"),
    DIRECTION(3, "Direction"),
    LOCATION(4, "Location"),
    ANNOTATION(5, "Annotation"),
    REPORT(16, "Report"),
    ORIENTATION(17, "Orientation"),
    FLOOR_LEVEL(18, "Floor Level"),
    NUMBER(19, "Number"),

    ;

    private final int code;
    private final String display;

    ImageTagTypeEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    public int getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }

}
