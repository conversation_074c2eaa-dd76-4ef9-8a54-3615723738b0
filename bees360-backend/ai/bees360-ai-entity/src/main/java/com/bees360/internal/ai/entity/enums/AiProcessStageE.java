package com.bees360.internal.ai.entity.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2019/08/13 10:10
 */
public enum AiProcessStageE {
    // @formatter:off
    NONE("None", 0, null),
    MODELING("Modeling", 3, AiStageEvent.MODELING_FINISHED),
    RANGING("Ranging", 7, AiStageEvent.RANGING_FINISHED),
    SCOPING("Scoping", 10, AiStageEvent.SCOPING_FINISHED),
    PLANE("Plane", 13, AiStageEvent.PLANE_FINISHED),
    BOUNDARY("Boundary", 16, AiStageEvent.BOUNDARY_FINISHED),
    CONFIRM("Confirm", 19, AiStageEvent.AI_PROCESS_CONFIRMED)
    ;
    // @formatter:on

    private final String value;
    private final int order;
    private AiProcessStageE prior;
    private AiProcessStageE next;

    private final AiStageEvent finished;

    static {
        AiProcessStageE[] stages = values();
        Arrays.sort(stages, (o1, o2) -> {
            if(o1 == o2) {
                return 0;
            } else if(o1 == null) {
                return 1;
            } else if(o2 == null) {
                return -1;
            }
            return Integer.compare(o1.getOrder(), o2.getOrder());
        });

        for(int i = 0; i < stages.length; i ++) {
            stages[i].prior = (i - 1 < 0? null: stages[i - 1]);
            stages[i].next = (i + 1 == stages.length? null: stages[i + 1]);
        }
    }

    AiProcessStageE(String value, int order, AiStageEvent finished) {
        if(value == null || value.isEmpty()) {
            throw new AssertionError("The value shouldn't be null or empty.");
        }
        this.value = value;
        this.order = order;
        this.finished = finished;
    }

    public static AiProcessStageE getEnum(String value) {
        for(AiProcessStageE stepE: values()) {
            if(stepE.getValue().equals(value)) {
                return stepE;
            }
        }
        return NONE;
    }

    public String getValue() {
        return value;
    }
    public int getOrder() {
        return order;
    }
    public AiProcessStageE getPrior() {
        return prior;
    }
    public AiProcessStageE getNext() {
        return next;
    }
}
