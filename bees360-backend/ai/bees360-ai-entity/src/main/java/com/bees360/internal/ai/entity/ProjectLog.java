package com.bees360.internal.ai.entity;

import com.bees360.commons.elasticsearchsupport.definition.DataModel;
import com.bees360.commons.elasticsearchsupport.definition.EsDocument;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/07/25 14:46
 */
@Data
@EsDocument(indexName = "project_log_entry", idPrefix = "project_id")
@NoArgsConstructor
public class ProjectLog implements DataModel<Long> {

    private long projectId;

    private List<LogEntry> logEntry;

    @Override
    public Long id() {
        return projectId;
    }

    public ProjectLog(long projectId) {
        this.projectId = projectId;
        this.logEntry = Collections.emptyList();
    }
}
