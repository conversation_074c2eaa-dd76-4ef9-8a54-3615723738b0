package com.bees360.internal.ai.entity.enums;

/**
 * <AUTHOR>
 * @date 2019/08/13 20:50
 */
public enum AiStageEvent {
    // @formatter:off
    MODELING_STARTED,
    MODELING_SUCCEEDED,
    MOD<PERSON>ING_FAILED,
    <PERSON><PERSON><PERSON><PERSON>_FAILED_START,

    M<PERSON><PERSON><PERSON>_STOPPED,
    M<PERSON><PERSON>ING_FINISHED,

    PRE_RANGING_STARTED,
    PRE_RANGING_SUCCEEDED,
    PRE_RANGING_FAILED,
    PRE_RANGING_FAILED_START,
    PRE_RANGING_STOPPED,

    RANGING_FINISHED,

    PRE_SCOPING_STARTED,
    PRE_SCOPING_SUCCEEDED,
    PRE_SCOPING_FAILED,
    PRE_SCOPING_FAILED_START,
    PRE_SCOPING_STOPPED,

    SCOPING_FINISHED,

    PRE_PLANE_STARTED,
    PRE_PLANE_SUCCEEDED,
    PRE_PLANE_FAILED,
    PRE_PLANE_FAILED_START,
    PRE_PLANE_STOPPED,

    PLANE_FINISHED,

    PRE_BOUNDARY_STARTED,
    PRE_BOUNDARY_SUCCEEDED,
    PRE_BOUNDARY_FAILED,
    PRE_BOUNDARY_FAILED_START,
    PRE_BOUNDARY_STOPPED,

    BOUNDARY_FINISHED,

    POST_BOUNDARY_STARTED,
    POST_BOUNDARY_SUCCEEDED,
    POST_BOUNDARY_FAILED,
    POST_BOUNDARY_FAILED_START,
    POST_BOUNDARY_STOPPED,

    AI_PROCESS_ABANDONED,
    AI_PROCESS_CONFIRMED
    ;
    // @formatter:on

    public boolean isStoppedEvent() {
        return this == MODELING_STOPPED || this == PRE_RANGING_STOPPED || this == PRE_SCOPING_STOPPED
               || this == PRE_PLANE_STOPPED || this == PRE_BOUNDARY_STOPPED || this == POST_BOUNDARY_STOPPED;
    }

    public boolean isSucceededEvent() {
        return this == MODELING_SUCCEEDED || this == PRE_RANGING_SUCCEEDED || this == PRE_SCOPING_SUCCEEDED
            || this == PRE_PLANE_SUCCEEDED || this == PRE_BOUNDARY_SUCCEEDED || this == POST_BOUNDARY_SUCCEEDED;
    }
}
