package com.bees360.internal.ai.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/02/28 14:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectInsuredInfoVo {

    private String address;
    private String city;
    private String state;
    private String country;
    private String zipCode;
    private Double lat;
    private Double lng;
    /**
     * Policy Holder
     */
    private String assetOwnerName;
    /**
     * Policy Holder Phone
     */
    private String assetOwnerPhone;
    /**
     * Policy Holder Email
     */
    private String assetOwnerEmail;
    /**
     * Type Of Property
     */
    private Integer projectType;

    private Long dueDate;
    private String claimNote;

    private String insuredHomePhone;

    private String insuredWorkPhone;
}
