package com.bees360.internal.ai.entity;

import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.DashBoardRoleTagToDoEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/07/27 20:53
 */
public class ProjectStatusTag {

    public static List<Integer> getTodoStatusCodes(DashBoardRoleTagToDoEnum roleTab) {
        return roleTab.getTodoStatusCodes();
    }

    public static List<Integer> getClosedStatusShowCodes() {
        List<Integer> statusCodes = AiProjectStatusEnum
            .getStatusCodeList(AiProjectStatusEnum.CLIENT_RECEIVED, AiProjectStatusEnum.PROJECT_CANCELED);
        return statusCodes;
    }

    public static List<Integer> getActiveTagStatus() {
        return Arrays.stream(AiProjectStatusEnum.values())
            .filter(o -> !getClosedStatusShowCodes().contains(o.getCode()))
            .map(AiProjectStatusEnum::getCode)
            .collect(Collectors.toList());
    }
}
