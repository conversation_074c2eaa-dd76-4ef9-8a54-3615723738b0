package com.bees360.internal.ai.entity.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public enum JobTypeEnum implements BaseCodeEnum {
    THREE_D(1, "3d", "3d_ad"),
    ADD(2, "ad", "3d_ad"),
    PRE_RANGING(3, "pr", "point_cloud"),
    PRE_SCOPING(4, "ps", "point_cloud"),
    PRE_PLANE(5, "pp", "point_cloud"),
    PRE_BOUNDARY(6, "pd", "point_cloud"),
    POST_BOUNDARY(7, "pb", "point_cloud"),
    HTML_PDF(8,"pdf","html_pdf"),
    STATIC_HTML_TO_PDF_JOB(9,"pdf","static_html_pdf");

    private final int code;
    private final String display;
    private final String name;

    public static final String LOCAL_JOB_NAME_PREFIX = "local_";

    JobTypeEnum(int code, String display, String name) {
        this.code = code;
        this.display = display;
        this.name = name;
    }

    public static JobTypeEnum getEnum(int code) {
        for (JobTypeEnum direction : JobTypeEnum.values()) {
            if (Objects.equals(direction.getCode(), code)) {
                return direction;
            }
        }
        return null;
    }

    public static boolean isPdfJob(String name) {
        return StringUtils.equalsAny(name, HTML_PDF.getName(), STATIC_HTML_TO_PDF_JOB.getName());
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    public String getName() {
        return name;
    }
}
