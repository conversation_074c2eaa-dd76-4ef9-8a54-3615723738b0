package com.bees360.internal.ai.entity;

import com.bees360.internal.ai.entity.enums.LogActionType;
import com.bees360.internal.ai.entity.enums.LogDetailType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/07/27 18:28
 */
@Data
public class LogEntryDetail {

    private Integer detailCode;

    private Integer actionCode;

    public LogEntryDetail(Integer detailCode, Integer actionCode) {
        this.detailCode = detailCode;
        this.actionCode = actionCode;
    }

    public LogEntryDetail(LogDetailType detailType, LogActionType actionType) {
        this.detailCode = detailType == null ? null : detailType.getCode();
        this.actionCode = actionType == null ? null : actionType.getCode();
    }
}
