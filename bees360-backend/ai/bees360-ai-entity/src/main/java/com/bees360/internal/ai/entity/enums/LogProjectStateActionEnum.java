package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;
import com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/08/16 14:10
 */
public enum LogProjectStateActionEnum implements LogActionType {

    PROJECT_OPEN(10, "Opened", ProjectStateEnum.PROJECT_OPEN),
    PROJECT_PAUSE(20, "Paused", ProjectStateEnum.PROJECT_PAUSE),
    PROJECT_CLOSE(30, "Closed", ProjectStateEnum.PROJECT_CLOSE);

    private final int code;
    private final String value;
    private final ProjectStateEnum state;

    LogProjectStateActionEnum(int code, String value, ProjectStateEnum state){
        this.code = code;
        this.value = value;
        this.state = state;
    }

    public static LogProjectStateActionEnum getEnumByValue(String value) {
        return Stream.of(LogProjectStateActionEnum.values())
            .filter(o -> Objects.equals(value, o.getValue()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogProjectStateActionEnum is null, value:" + value));

    }

    public static LogProjectStateActionEnum getEnumByState(ProjectStateEnum state) {
        return Stream.of(LogProjectStateActionEnum.values())
            .filter(o -> Objects.equals(state, o.getState()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogProjectStateActionEnum is null, state:" + state));

    }

    public static List<CodeNameDto> getEnumDict() {
      return Arrays.stream(LogProjectStateActionEnum.values())
          .map(o -> new CodeNameDto(o.getCode(), o.getValue()))
          .collect(Collectors.toList());
    }
    @Override
    public int getCode(){
        return code;
    }

    public String getValue() {
        return value;
    }

    public ProjectStateEnum getState() {
        return state;
    }
}
