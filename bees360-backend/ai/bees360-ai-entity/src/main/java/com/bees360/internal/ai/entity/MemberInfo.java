package com.bees360.internal.ai.entity;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class MemberInfo {

    private String id;

    private String name;

    private String auth;

    private String phone;

    private String email;

    private String avatarUrl;

    private String photo;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        MemberInfo info = (MemberInfo) o;
        return Objects.equals(getId(), info.getId()) && Objects.equals(getAuth(), info.getAuth());
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.getId(), this.getAuth());
    }
}
