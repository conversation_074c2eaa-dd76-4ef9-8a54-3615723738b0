package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;
import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/07/30 09:27
 */
public enum LogADDetailEnum implements LogDetailType {

    AD_GENERATION(1, "AD Generation");

    private final int code;
    private final String value;

    LogADDetailEnum(int code, String value){
        this.code = code;
        this.value = value;
    }

    public static LogADDetailEnum getEnumByValue(String value) {
        return Stream.of(LogADDetailEnum.values())
            .filter(o -> Objects.equals(value, o.getValue()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogADDetailEnum is null, value:" + value));
    }

    public static List<CodeNameDto> getEnumDict() {
        return Arrays.stream(LogADDetailEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getValue()))
            .collect(Collectors.toList());
    }

    @Override
    public int getCode(){
        return code;
    }

    public String getValue() {
        return value;
    }
}
