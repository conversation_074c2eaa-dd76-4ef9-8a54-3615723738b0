package com.bees360.internal.ai.entity;

import com.bees360.internal.ai.entity.dto.CodeNameDto;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.LogADActionEnum;
import com.bees360.internal.ai.entity.enums.LogADDetailEnum;
import com.bees360.internal.ai.entity.enums.LogAiFlowActionEnum;
import com.bees360.internal.ai.entity.enums.LogAiFlowDetailEnum;
import com.bees360.internal.ai.entity.enums.LogDarPirStatusActionEnum;
import com.bees360.internal.ai.entity.enums.LogDarPirStatusDetailEnum;
import com.bees360.internal.ai.entity.enums.LogEntryTypeEnum;
import com.bees360.internal.ai.entity.enums.LogHoverPlnarStatusActionEnum;
import com.bees360.internal.ai.entity.enums.LogHoverPlnarStatusDetailEnum;
import com.bees360.internal.ai.entity.enums.LogProjectDataActionEnum;
import com.bees360.internal.ai.entity.enums.LogProjectDataDetailEnum;
import com.bees360.internal.ai.entity.enums.LogReportActionEnum;
import com.bees360.internal.ai.entity.enums.LogReportDetailEnum;
import com.bees360.internal.ai.entity.enums.LogProjectStateDetailEnum;
import com.bees360.internal.ai.entity.enums.LogProjectStateActionEnum;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/07/31 10:42
 */
@Data
public class LogEntryTypeDict {

    private String type;

    private List<CodeNameDto> detail;

    private List<CodeNameDto> action;

    public LogEntryTypeDict(String type, List<CodeNameDto> detail, List<CodeNameDto> action) {
        this.type = type;
        this.detail = detail;
        this.action = action;
    }

    public static List<LogEntryTypeDict> loadLogEntryTypeDict() {
        return Arrays.asList(
            new LogEntryTypeDict(LogEntryTypeEnum.PROJECT_DATA.getType(), LogProjectDataDetailEnum.getEnumDict(), LogProjectDataActionEnum.getEnumDict()),
            new LogEntryTypeDict(LogEntryTypeEnum.PROJECT_STATUS_CHANGE.getType(), AiProjectStatusEnum.getEnumDict(), null),
            new LogEntryTypeDict(LogEntryTypeEnum.PROJECT_AI_FLOW.getType(), LogAiFlowDetailEnum.getEnumDict(), LogAiFlowActionEnum.getEnumDict()),
            new LogEntryTypeDict(LogEntryTypeEnum.PROJECT_AD_FLOW.getType(), LogADDetailEnum.getEnumDict(), LogADActionEnum.getEnumDict()),
            new LogEntryTypeDict(LogEntryTypeEnum.REPORT.getType(), LogReportDetailEnum.getEnumDict(), LogReportActionEnum.getEnumDict()),
            new LogEntryTypeDict(LogEntryTypeEnum.HOVER_PLNAR_STATUS.getType(), LogHoverPlnarStatusDetailEnum.getEnumDict(),
                LogHoverPlnarStatusActionEnum.getEnumDict()),
            new LogEntryTypeDict(LogEntryTypeEnum.DAR_PIR_STATUS.getType(), LogDarPirStatusDetailEnum.getEnumDict(),
                LogDarPirStatusActionEnum.getEnumDict()),
            new LogEntryTypeDict(LogEntryTypeEnum.PROJECT_STATE_CHANGE.getType(), LogProjectStateDetailEnum.getEnumDict(),
                LogProjectStateActionEnum.getEnumDict())
        );
    }
}
