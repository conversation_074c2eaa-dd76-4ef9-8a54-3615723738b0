package com.bees360.internal.ai.entity.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @deprecated Please use {@code com.bees360.entity.enums.ReportTypeEnum} instead.
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Deprecated
public enum ReportTypeEnum {

	PREMIUM_DAMAGE_ASSESSMENT_REPORT(1, "Premium Damage Assessment Report", "application/pdf",
			true, false, true, true, true,
			ProjectStatusEnum.DAMAGE_REPORT_SUBMITTED,
			ProjectStatusEnum.DAMAGE_REPORT_GENERATED,
			ProjectStatusEnum.DAMAGE_REPORT_APPROVED,
			ProjectStatusEnum.DAMAGE_REPORT_DISAPPROVED),

	PREMIUM_ROOF_MEASUREMENT_REPORT(2, "Premium Measurement Report", "application/pdf",
			true, false, true, true, false,
			ProjectStatusEnum.MEASUREMENT_REPORT_SUBMITTED,
			ProjectStatusEnum.MEASUREMENT_REPORT_GENERATED,
			ProjectStatusEnum.MEASUREMENT_REPORT_APPROVED,
			ProjectStatusEnum.MEASUREMENT_REPORT_DISAPPROVED),

	MACRO_XML_REPORT(4, "Macro XML Report", "application/zip",
			false, true, false, false, false,
			null,
			ProjectStatusEnum.MACRO_XML_GENERATED,
			null,
			null),

	QUICK_DAMAGE_ASSESSMENT_REPORT(5, "Preliminary Damage Assessment Report", "application/pdf",
			true, false, true, true, false,
			ProjectStatusEnum.ONSITE_DAMAGE_REPORT_SUBMITTED,
			ProjectStatusEnum.ONSITE_DAMAGE_REPORT_GENERATED,
			ProjectStatusEnum.ONSITE_DAMAGE_REPORT_APPROVED,
			ProjectStatusEnum.ONSITE_DAMAGE_REPORT_DISAPPROVED),

	QUICK_ROOF_EVALUATION_REPORT(7, "Highfly Evaluation Report", "application/pdf",
			true, false, true, true, false,
			ProjectStatusEnum.DAMAGE_ASSESSMENT_REPORT_SUBMITTED,
			ProjectStatusEnum.DAMAGE_ASSESSMENT_REPORT_GENERATED,
			ProjectStatusEnum.DAMAGE_ASSESSMENT_REPORT_APPROVED,
			ProjectStatusEnum.DAMAGE_ASSESSMENT_REPORT_DISAPPROVED),

	REALTIME_ROOF_INSPECTION_REPORT(8, "Real-time Damage Assessment Report", "application/pdf",
			true, false, true, true, false,
			ProjectStatusEnum.REALTIME_APP_DAMAGE_REPORT_SUBMITTED,
			ProjectStatusEnum.REALTIME_APP_DAMAGE_REPORT_GENERATED,
			ProjectStatusEnum.REALTIME_APP_DAMAGE_REPORT_APPROVED,
			ProjectStatusEnum.REALTIME_APP_DAMAGE_REPORT_DISAPPROVED),

	SYMBILITY_SCAN_REPORT(9, "Symbility XML Report", "application/xml",
			false, true, false, false, false,
			null,
			ProjectStatusEnum.SYMBILITY_XML_REPORT_GENERATED,
			null,
			null),

	WEATHER_REPORT(10, "Weather Report", "application/pdf",
        false, false, false, true, false,
			ProjectStatusEnum.WEATHER_REPORT_SUBMITTED,
			ProjectStatusEnum.WEATHER_REPORT_GENERATED,
			ProjectStatusEnum.WEATHER_REPORT_APPROVED,
			ProjectStatusEnum.WEATHER_REPORT_DISAPPROVED),

    ESTIMATE_REPORT(11, "Estimate Report", "application/pdf",
        true, false, false, true, false,
			ProjectStatusEnum.ESTIMATE_REPORT_SUBMITTED,
			ProjectStatusEnum.ESTIMATE_REPORT_GENERATED,
			ProjectStatusEnum.ESTIMATE_REPORT_APPROVED,
			ProjectStatusEnum.ESTIMATE_REPORT_DISAPPROVED),

	DXF_REPORT(12, "Measurement DXF Report", "application/dxf",
			true, false, false, false, false,
			ProjectStatusEnum.DXF_REPORT_SUBMITTED,
			ProjectStatusEnum.DXF_REPORT_GENERATED,
			ProjectStatusEnum.DXF_REPORT_APPROVED,
			ProjectStatusEnum.DXF_REPORT_DISAPPROVED),

	BID_REPORT(13, "On-Site Bidding Report", "application/pdf",
			false, false, true, true, false,
			ProjectStatusEnum.BIDDING_SUBMITTED,
			ProjectStatusEnum.BIDDING_GENERATED,
			ProjectStatusEnum.BIDDING_APPROVED,
			ProjectStatusEnum.BIDDING_DISAPPROVED),

	QUICK_SQUARE_REPORT(14, "Real-time Quick Square Report", "application/pdf",
			false, true, true, true, false,
			ProjectStatusEnum.QUICK_SQUARE_SUBMITTED,
			ProjectStatusEnum.QUICK_SQUARE_GENERATED,
			ProjectStatusEnum.QUICK_SQUARE_APPROVED,
			ProjectStatusEnum.QUICK_SQUARE_DISAPPROVED),

	PROPERTY_IMAGE_REPORT(15, "Property Image Report", "application/pdf",
			true, false, true, true, true,
			ProjectStatusEnum.APP_DAMAGE_REPORT_SUBMITTED,
			ProjectStatusEnum.APP_DAMAGE_REPORT_GENERATED,
			ProjectStatusEnum.APP_DAMAGE_REPORT_APPROVED,
			ProjectStatusEnum.APP_DAMAGE_REPORT_DISAPPROVED),

	INFRARED_DAMAGE_REPORT(16, "Infrared Damage Assessment Report", "application/pdf",
			true, false, true, true, false,
			ProjectStatusEnum.INFRARED_DAMAGE_REPORT_SUBMITTED,
			ProjectStatusEnum.INFRARED_DAMAGE_REPORT_GENERATED,
			ProjectStatusEnum.INFRARED_DAMAGE_REPORT_APPROVED,
			ProjectStatusEnum.INFRARED_DAMAGE_REPORT_DISAPPROVED),

    ROOF_ONLY_UNDERWRITING_REPORT(17, "Roof-only Underwriting Report", "application/pdf",
        true, false, true, true, true,
        ProjectStatusEnum.ROOF_ONLY_UNDERWRITING_REPORT_SUBMITTED,
        ProjectStatusEnum.ROOF_ONLY_UNDERWRITING_REPORT_GENERATED,
        ProjectStatusEnum.ROOF_ONLY_UNDERWRITING_REPORT_APPROVED,
        ProjectStatusEnum.ROOF_ONLY_UNDERWRITING_REPORT_DISAPPROVED),

    FULL_SCOPE_UNDERWRITING_REPORT(18, "Full-scope Underwriting Report", "application/pdf",
        true, false, true, true, true,
        ProjectStatusEnum.FULL_SCOPE_UNDERWRITING_REPORT_SUBMITTED,
        ProjectStatusEnum.FULL_SCOPE_UNDERWRITING_REPORT_GENERATED,
        ProjectStatusEnum.FULL_SCOPE_UNDERWRITING_REPORT_APPROVED,
        ProjectStatusEnum.FULL_SCOPE_UNDERWRITING_REPORT_DISAPPROVED),

    LETTER_OF_CANCELLATION_REPORT(19, "Inspection Closeout Report", "application/pdf",
        false, true, false, true, true,
        ProjectStatusEnum.LETTER_OF_CANCELLATION_REPORT_SUBMITTED,
        ProjectStatusEnum.LETTER_OF_CANCELLATION_REPORT_GENERATED,
        ProjectStatusEnum.LETTER_OF_CANCELLATION_REPORT_APPROVED,
        ProjectStatusEnum.LETTER_OF_CANCELLATION_REPORT_DISAPPROVED),

    CLAIM_DAMAGE_FORM(20, "Claim Damage Form", "application/pdf",
        false, false, false, true, false,
        ProjectStatusEnum.CLAIM_DAMAGE_FORM_SUBMITTED,
        ProjectStatusEnum.CLAIM_DAMAGE_FORM_GENERATED,
        ProjectStatusEnum.CLAIM_DAMAGE_FORM_APPROVED,
        ProjectStatusEnum.CLAIM_DAMAGE_FORM_DISAPPROVED),
    /**
     * 重新检测报告，用于完成理赔之后检测屋顶修缮效果，从而辅助判断是否存在理赔欺诈行为（资金是否都用于修缮屋顶）。
     */
    POST_CONSTRUCTION_AUDIT_REPORT(21, "Post-Construction Audit Report", "application/pdf",
        true, false, true, true, true,
        ProjectStatusEnum.POST_CONSTRUCTION_AUDIT_REPORT_SUBMITTED,
        ProjectStatusEnum.POST_CONSTRUCTION_AUDIT_REPORT_GENERATED,
        ProjectStatusEnum.POST_CONSTRUCTION_AUDIT_REPORT_APPROVED,
        ProjectStatusEnum.POST_CONSTRUCTION_AUDIT_REPORT_DISAPPROVED),

	HOVER_PRO_REPORT(22, "HOVER Pro Report", "application/pdf",
		false, true, false, true, false,
		null,null, null, null),

	HOVER_ESX(23, "HOVER", "application/octet-stream",
		false, true, false, true, false,
		null, null, null, null),

	XACT_ESX(24, "XACT", "application/octet-stream",
		false, true, false, true, false,
		null, null, null, null),

    INVOICE(25, "Invoice", "application/pdf",
        true, true, false, true, false,
        null, null, null, null),

    MAGIC_PLAN(26, "MagicPlan report", "application/pdf", false,
        true, false, true, false,
        null, null, null, null),

    HOMEOWNER_SURVEY(27, "Homeowner Inspection Survey Results", "application/pdf",
        false, true, false, true, true,
        ProjectStatusEnum.HOMEOWNER_SURVEY_REPORT_SUBMITTED,
        ProjectStatusEnum.HOMEOWNER_SURVEY_REPORT_GENERATED,
        ProjectStatusEnum.HOMEOWNER_SURVEY_REPORT_APPROVED,
        ProjectStatusEnum.HOMEOWNER_SURVEY_REPORT_DISAPPROVED),

    CHECKLIST(32, "Checklist", "application/pdf",
        false, true, false, true, false,
        null, null, null, null),

    CLAIM(33, "Claim Report", "application/pdf",
        false, true, false, true, false,
        null, null, null, null),


    GENERAL_LOSS_REPORT(34, "General Loss Report", "application/pdf",
        false, true, false, true, false,
        null, null, null, null),


    DRONE_PHOTO_SHEET(37, "Drone Photo Sheet", "application/pdf",
        false, true, false, true, false,
        null, null, null, null),

    MOBILE_PHOTO_SHEET(38, "Mobile Photo Sheet", "application/pdf",
        false, true, false, true, false,
        null, null, null, null),

    PLNAR(39, "Plnar", "application/pdf",
        false, true, false, true, false,
        null, null, null, null),

    CUBICASA(40, "Cubicasa", "application/pdf",
        false, true, false, true, false,
        null, null, null, null),

    HOVER_TLA(41, "Hover TLA", "application/pdf",
        false, true, false, true, false,
        null, null, null, null),

    FSR_RCE(42, "Full-scope Underwriting Report with Recovery Cost Estimate", "application/pdf",
        false, true, false, true, false,
        null, null, null, null),

    OCC(43, "OneClick Code Report", "application/pdf",
        false, true, false, true, false,
        null, null, null, null),

    EVRP(44, "EagleView Report", "application/pdf",
        false, true, false, true, false,
        null, null, null, null),

    EVOB(45, "EV .OBJ", "model/obj",
        false, true, false, true, false,
        null, null, null, null),

    EUR(46, "Express Underwriting Report", "application/pdf",
        true, false, true, true, true,
        ProjectStatusEnum.EUR_REPORT_SUBMITTED,
        ProjectStatusEnum.EUR_REPORT_GENERATED,
        ProjectStatusEnum.EUR_REPORT_APPROVED,
        ProjectStatusEnum.EUR_REPORT_DISAPPROVED),
    CUBICASA_VIDEO_ZIP(47, "Cubicasa Video Zip", "application/zip",  false, true, false, true, false, null, null, null, null),
    CUBICASA_VIDEO(48, "Cubicasa Video", "video/x-m4v",  false, true, false, true, false, null, null, null, null),
    HOSTA_SUMMARY_ZIP(49, "Hosta Summary Zip", "application/zip",  false, true, false, true, false, null, null, null, null),
    HOSTA_MODEL_ZIP(50, "Hosta Model Zip", "application/zip",  false, true, false, true, false, null, null, null, null),
    CUSTOM_HOME_REPORT(51, "Policyholder Property Inspection Report", "application/pdf",
        false, true, false, true, false,
        ProjectStatusEnum.CHR_REPORT_SUBMITTED,
        ProjectStatusEnum.CHR_REPORT_GENERATED,
        ProjectStatusEnum.CHR_REPORT_APPROVED,
        ProjectStatusEnum.CHR_REPORT_DISAPPROVED),
    SCHEDULING_ONLY_SUMMARY(52, "Scheduling Only Summary", "application/pdf",
        true, true, true, true, true,
        null, null, null, null),

	BEESSKETCH(53, "BeesSketch", "application/pdf",
			true, true, false, true, false,
			ProjectStatusEnum.BEESSKETCH_REPORT_SUBMITTED,
			ProjectStatusEnum.BEESSKETCH_REPORT_GENERATED,
			ProjectStatusEnum.BEESSKETCH_REPORT_APPROVED,
			ProjectStatusEnum.BEESSKETCH_REPORT_DISAPPROVED),

	SNAP(54, "Snap360 Report", "application/pdf",
			true, false, true, true, true,
			ProjectStatusEnum.SNAP_REPORT_SUBMITTED,
			ProjectStatusEnum.SNAP_REPORT_GENERATED,
			ProjectStatusEnum.SNAP_REPORT_APPROVED,
			ProjectStatusEnum.SNAP_REPORT_DISAPPROVED),
    ;

	private final int code;
	private final String display;
	private final String contentType;

	private final boolean needApproved;
	private final boolean isFree;
	private final boolean isOrderable;
	private final boolean isReadable;
    private final boolean isSyncToWeb;

	private ProjectStatusEnum currentStatus;

	public final ProjectStatusEnum SUBMITTED;
	public final ProjectStatusEnum GENERATED;
	public final ProjectStatusEnum APPROVED;
	public final ProjectStatusEnum REJECTED;

	private static Map<ProjectStatusEnum, ReportTypeEnum> STATUS_MAP_TYPE;

	public static List<ReportTypeEnum> REPORTS_NEED_3D;

	static {
		Map<ProjectStatusEnum, ReportTypeEnum> statusMapTypes
			= new HashMap<ProjectStatusEnum, ReportTypeEnum>();
		ReportTypeEnum[] types = ReportTypeEnum.values();
		for(ReportTypeEnum type: types) {
			if(type.SUBMITTED != null) {
				statusMapTypes.put(type.SUBMITTED, type);
			}
			if(type.GENERATED != null) {
				statusMapTypes.put(type.GENERATED, type);
			}
			if(type.APPROVED != null) {
				statusMapTypes.put(type.APPROVED, type);
			}
			if(type.REJECTED != null) {
				statusMapTypes.put(type.REJECTED, type);
			}
		}
		STATUS_MAP_TYPE = Collections.unmodifiableMap(statusMapTypes);

		REPORTS_NEED_3D = Collections.unmodifiableList(Arrays.asList(ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT,
				ReportTypeEnum.PREMIUM_ROOF_MEASUREMENT_REPORT));
	}

	ReportTypeEnum(int code, String display, String contentType,
                   boolean needApproved, boolean isFree, boolean isOrderable, boolean isReadable, boolean isSyncToWeb,
                   ProjectStatusEnum submitted, ProjectStatusEnum generated,
                   ProjectStatusEnum approved, ProjectStatusEnum rejected){
		this.code = code;
		this.display = display;
		this.contentType = contentType;
		this.needApproved = needApproved;
		this.isFree = isFree;
		this.isOrderable = isOrderable;
		this.isReadable = isReadable;
        this.isSyncToWeb = isSyncToWeb;

		this.SUBMITTED = submitted;
		this.GENERATED = generated;
		this.APPROVED = approved;
		this.REJECTED = rejected;

		this.currentStatus = null;
	}

	public static ReportTypeEnum getType(ProjectStatusEnum status) {
		ReportTypeEnum type = STATUS_MAP_TYPE.get(status);
		if(type != null) {
			type.currentStatus = status;
		}
		return type;
	}

	public static boolean exist(int type) {
		return getEnum(type) != null;
	}

	public static ReportTypeEnum getEnum(int code) {
		for(ReportTypeEnum type: ReportTypeEnum.values()) {
			if(type.getCode() == code) {
				return type;
			}
		}
		return null;
	}

	public int getCode() {
		return code;
	}
	public String getDisplay() {
		return display;
	}
	public String getContentType() {
		return contentType;
	}
	public boolean needApproved() {
		return needApproved;
	}
    public boolean isSyncToWeb() {
        return isSyncToWeb;
    }
    public boolean isFree() {
		return isFree;
	}
	public boolean isOrderable() {
		return isOrderable;
	}
	public boolean isReadable() {
		return isReadable;
	}
	public ProjectStatusEnum getCurStatus() {
		return currentStatus;
	}

    public static boolean needSyncData(ReportTypeEnum type) {
        return Objects.equals(type, HOMEOWNER_SURVEY);
    }
}
