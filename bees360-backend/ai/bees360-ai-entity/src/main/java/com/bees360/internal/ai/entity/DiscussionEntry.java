package com.bees360.internal.ai.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * 留言板
 * <AUTHOR>
 * @date 2020/09/08 14:34
 */
@Data
@Builder
@AllArgsConstructor
public class DiscussionEntry {

    private String noteId;

    private String note;

    private String createdBy;

    private String createdName;

    private long createdAt;

    /**
     * 留言板note提醒的组或者用户
     */
    private List<DiscussionParticipant> participants;

    private String avatarUrl;

    private List<String> creatorRoles;

    public DiscussionEntry(String note, String createdBy, String creatorName, long createdAt, List<DiscussionParticipant> participants) {
        this.noteId = createdBy + "_" + createdAt;
        this.note = note;
        this.createdBy = createdBy;
        this.createdName = creatorName;
        this.createdAt = createdAt;
        this.participants = participants;
    }
}
