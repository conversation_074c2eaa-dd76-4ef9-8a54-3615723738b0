package com.bees360.internal.ai.entity.vo;

import com.bees360.internal.ai.entity.Airspace;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/03/30 10:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectCalQueryVo {

    private long projectId;
    /**
     * 飞手名字
     */
    private String pilotName;
    /**
     * processor 名字
     */
    private String processName;
    /**
     * 触发 site inspected 事件时间
     */
    private Long siteInspected;
    /**
     * 触发 image uploaded 事件时间
     */
    private Long imageUploaded;
    /**
     * 服务器的编号，目前未定义
     */
    private String serverNo;
    /**
     * 3d 开始时间
     */
    private Long thirdStarted;
    /**
     * 3d 完成时间
     */
    private Long thirdFinished;
    /**
     * plane 完成时间
     */
    private Long planeFinished;
    /**
     * post-boundary 完成时间
     */
    private Long thirdProcessed;
    /**
     * pir 报告生成时间
     */
    private Long pirGenerated;
    /**
     * dar 报告生成时间
     */
    private Long darGenerated;
    /**
     * pir 报告 submitted 时间
     */
    private Long pirSubmitted;
    /**
     * dar 报告 submitted 时间
     */
    private Long darSubmitted;
    /**
     * dar 报告最后一次 submitted 时间
     */
    private Long darLastSubmitted;
    /**
     * dar 报告 submitted 次数
     */
    private Integer darSubmittedTimes;
    /**
     * fsr 报告 submitted 时间
     */
    private Long fsrSubmitted;
    /**
     * ror 报告 submitted 时间
     */
    private Long rorSubmitted;
    /**
     * adjuster 名字
     */
    private String adjusterName;
    /**
     * reviewer 名字
     */
    private String reviewerName;
    /**
     * pir 报告 approved 时间
     */
    private Long pirApproved;
    /**
     * dar 报告 approved 时间
     */
    private Long darApproved;
    /**
     * dar 报告最后一次 approved 时间
     */
    private Long darLastApproved;
    /**
     * dar 报告 approved 次数
     */
    private Integer darApprovedTimes;
    /**
     * fsr 报告  approved 时间
     */
    private Long fsrApproved;
    /**
     * ror 报告 approved 时间
     */
    private Long rorApproved;

    private String policyNumber;

    private Integer projectType;

    private String claimNumber;

    private String insuredPhone;

    private String insuredName;

    private String insuredEmail;

    private String streetAddress;

    private String city;

    private String  state;

    private String zipCode;

    private String catNumber;

    private Integer catLevel;

    // todo
    private Double score;

    private Long createTime;

    private Long completionTime;

    private String batchNumber;

    private Double totalPay;

    private Integer serviceType;

    private Double latitude;

    private Double longitude;

    /**
     * 空域信息
     */
    private int flyZoneType;

    private Long daysOld;

    private Long customerContacted;

    private Long assignedToPilot;

    private Long inspectionDeadLine;

    private String externalAdjusterName;

    private String creator;

    private boolean subscribePlnar;

    private Integer RoomNumberProcessed;

    private boolean paid;

    private boolean canceled;

    private Long clientReceived;

    private Long paymentDate;

    private String policyEffectiveDate;

    private List<Long> projectTags;

    private List<String> pilotFeedback;

    private String darApprovedName;

    private Long estimateCompleteTime;

    private String estimateCompleteName;

    private String clientReceivedName;

    private String insuredBy;

    // miles
    private Float hiveDistance;

    private BigDecimal estimateTotalMoney;

    private String producerName;

    private String pirSubmittedBy;

    private String operatingCompany;

    private String policyType;

    private Integer hoverStatus;

    private String projectState;

    private String projectStateChangeReason;

    private String changeReasonGroup;

    private List<com.bees360.internal.ai.entity.ProjectStatusVo> timeLines;

    private Airspace airspace;

    private int droneImageCount;

    private int mobileImageCount;
}
