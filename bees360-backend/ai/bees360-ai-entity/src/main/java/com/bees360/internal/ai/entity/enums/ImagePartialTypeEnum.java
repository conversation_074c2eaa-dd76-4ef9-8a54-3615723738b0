package com.bees360.internal.ai.entity.enums;

public enum ImagePartialTypeEnum implements BaseCodeEnum {
    ROOF(1, "Roof"),
    ELEVATION(2, "Elevation"),
    INTERIOR(3, "Interior"),
    GARAGE(4, "Garage"),
    APS(5, "Other Structures"),
    OTHERS(6, "Contents"),
    ADDRESS(7, "Address verification"),
    ELECTRICAL_PANEL(8, "Electrical Panel"),
    PLUMBING_SYSTEM(9, "Plumbing System"),
    WATER_HEATER(10, "Water Heater"),
    HAZARDS(11, "Hazards"),
    ROOM_OVERVIEW(12, "Room Overview"),
    FLOOR_OVERVIEW(13, "Floor Overview"),
    CEILING_OVERVIEW(14, "Ceiling Overview"),
    DAMAGE_AREA(15, "Damage Area"),
    OTHER_ITEMS(16, "Other Items"),
    SELFIE(17, "<PERSON>ie"),

    PROPERTY_OVERVIEW(28, "Property Overview"),

    ALARM(29, "Alarm"),
    GENERATOR(30, "Generator"),
    WATER_LEAK_DETECTION(31, "Water Leak Detection"),
    DRY_HYDRANTS(32, "Dry Hydrants"),
    HOME_SPRINKLERS(33, "Home Sprinklers"),
    FIRE_EXTINGUISHERS(34, "Fire Extinguishers"),
    FIRE_PROOF_CABINETS(35, "Fire Proof Cabinets"),
    FLAMMABLE_RAG(36, "Flammable Rag"),
    NO_SMOKING_SIGNS(37, "No Smoking Signs"),
    HAZARDOUS_ADJACENT_PROPERTY(39, "Hazardous Adjacent Property"),
    FIRE_ALARM(40, "Fire Alarm"),
    BURGLAR_ALARM(41, "Burglar Alarm"),
    FIRE_SPRINKLERS(42, "Fire Sprinklers"),
    LOW_TEMP_SYSTEM(43, "Low-Temp System"),
    GAS_LEAK_DETECTION(44, "Gas Leak Detection"),
    LEED_CERTIFICATE(45, "Leed Certificate"),
    CARETAKER(48, "Caretaker"),
    DOORMAN_24_HOUR(51, "Doorman 24 Hour"),
    ELEVATOR(52, "Elevator"),
    IMPACT_RATED_GLASS_MARK(53, "Impact Rated Glass Mark"),
    WIND_LOAD_STICKER(54, "Wind Load Sticker"),
    WINDOW_PROTECTION(55, "Window Protection"),

    WASHING_MACHINE_OVERVIEW(9004, "Washing Machine Overview"),
    WASHING_MACHINE_SUPPLY_LINES(9005, "Washing Machine Supply Lines"),
    WASHING_MACHINE_SERIAL_NUMBER(9006, "Washing Machine Serial Number"),
    DRYER_SERIAL_NUMBER(9007, "Dryer Serial Number"),
    DISHWASHER_OVERVIEW(9030, "Dishwasher Overview"),
    DISHWASHER_DOOR_OPEN(9031, "Dishwasher Door Open"),
    DISHWASHER_SERIAL_NUMBER(9032, "Dishwasher Serial Number"),
    REFRIGERATOR_OVERVIEW(9040, "Refrigerator Overview"),
    REFRIGERATOR_SERIAL_NUMBER(9041, "Refrigerator Serial Number"),
    RANGE_OVERVIEW(9050, "Range Overview"),
    RANGE_SERIAL_NUMBER(9051, "Range Serial Number"),
    REVERSE_OSMOSIS_SYSTEM_OVERVIEW(9060, "Reverse Osmosis System Overview"),
    REVERSE_OSMOSIS_SYSTEM_SERIAL_NUMBER(9061, "Reverse Osmosis System Serial Number"),
    SOLAR_PANEL_BATTERY_BACK_UP(9070, "Solar Panel Battery Back Up"),
    WATER_SHUT_OFF_PANEL(9080, "Water Shut Off Panel"),

    WATER_LEAK_DETECTION_CERTIFICATE(31002, "Water Leak Detection Certificate"),
    FIRE_ALARM_CONTROL_PANEL(40001, "Fire Alarm Control Panel"),
    FIRE_ALARM_CERTIFICATE(40002, "Fire Alarm Certificate"),
    BURGLAR_ALARM_CONTROL_PANEL(41001, "Burglar Alarm Control Panel"),
    BURGLAR_ALARM_CERTIFICATE(41002, "Burglar Alarm Certificate"),
    FIRE_SPRINKLERS_FLOW_SWITCH(42001, "Fire Sprinklers Flow Switch"),
    FIRE_SPRINKLERS_CERTIFICATE(42002, "Fire Sprinklers Certificate"),
    LOW_TEMP_SYSTEM_CERTIFICATE(43001, "Low-Temp System Certificate"),
    GAS_LEAK_DETECTION_CERTIFICATE(44001, "Gas Leak Detection Certificate"),
    ELEVATOR_CONTROL_PANEL(52001, "Elevator Control Panel"),
    ELEVATOR_LOBBY_CAMERAS(52002, "Elevator Lobby Cameras"),
    ELEVATOR_CERTIFICATE(52003, "Elevator Certificate"),
    ;

    private final int code;
    private final String display;

    ImagePartialTypeEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    public static ImagePartialTypeEnum getEnum(int code) {
        ImagePartialTypeEnum[] enums = ImagePartialTypeEnum.values();
        for (ImagePartialTypeEnum type : enums) {
            if (type.code == code) {
                return type;
            }
        }
        return OTHERS;
    }
}
