package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/07/30 09:27
 */
public enum LogReportActionEnum implements LogActionType {

    GENERATED(1, "Generated"),
    SUBMITTED(2, "Submitted"),
    APPROVED(3, "Approved"),
    DISAPPROVED(4, "Disapproved"),
    UPLOADED(5, "Uploaded"),
    FAILED(6, "Failed"),
    STARTED(7, "Started"),
    DELETED(8, "Deleted");

    private final int code;
    private final String value;

    LogReportActionEnum(int code, String value){
        this.code = code;
        this.value = value;
    }

    public static LogReportActionEnum getEnumByValue(String value) {
        return Stream.of(LogReportActionEnum.values())
            .filter(o -> Objects.equals(value, o.getValue()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogReportActionEnum is null, value:" + value));
    }

    public static List<CodeNameDto> getEnumDict() {
        return Arrays.stream(LogReportActionEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getValue()))
            .collect(Collectors.toList());
    }

    public static LogReportActionEnum getEnum(int code) {
        return Stream.of(LogReportActionEnum.values()).filter(o -> Objects.equals(code, o.getCode())).findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogReportActionEnum is null, code:" + code));

    }

    @Override
    public int getCode(){
        return code;
    }

    public String getValue() {
        return value;
    }
}
