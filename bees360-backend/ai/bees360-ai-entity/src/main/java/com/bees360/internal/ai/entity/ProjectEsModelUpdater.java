package com.bees360.internal.ai.entity;

import com.bees360.commons.elasticsearchsupport.definition.DataModel;
import com.bees360.commons.elasticsearchsupport.definition.EsDocument;
import com.bees360.user.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.annotation.Nullable;
import java.net.URL;
import java.util.List;
import java.util.Optional;

/**
 * 没有基本类型的ProjectEsModel, 主要用于更新数据
 */
@Data
@EsDocument(
        indexName = "dashboard_project",
        alias = "dashboard_project_alias",
        idPrefix = "project_id")
@Builder(builderMethodName = "toBuilder", setterPrefix = "set")
@NoArgsConstructor
@AllArgsConstructor
public class ProjectEsModelUpdater implements DataModel<Long> {
    public final static float DEFAULT_IMAGE_SCORE = -1;

    private Long projectId;

    private String policyNumber;

    private String claimNumber;

    private Integer claimType;

    private Long createdTime;

    private Long createdBy;

    private Integer projectType;

    private String address;

    private String city;

    private String state;

    private String country;

    private String zipCode;

    private String addressId;

    private String assetOwnerName;

    private String assetOwnerPhone;

    private String assetOwnerEmail;

    private String inspectionNumber;

    private Long dueDate;

    private String inspectionType;

    private Long insuranceCompany;
    /** 表示为processed_by */
    private Long repairCompany;

    private Long companyId;

    private String description;

    private String claimNote;
    private String north;
    /** related to com.bees360.entity.enums.ProcessStatus */
    private Integer latestStatus;

    private Integer projectStatus;

    /** project status 最近修改时间 */
    private Long statusUpdateTime;

    private Integer imageUploadStatus;

    private Long inspectionTypes;

    private Long inspectionTime;

    private String statusName;

    /** project service type 订阅服务类型 */
    private Integer serviceType;

    private String creatorName;

    private String agent;
    /** 代理人名称 */
    private String agentContactName;

    private String agentPhone;
    private String agentEmail;

    private Integer flyZoneType;

    private String insuranceCompanyName;
    private String repairCompanyName;

    private String customer;
    private String guideline;
    private String insuredHomePhone;
    private String insuredWorkPhone;
    private Double gpsLocationLongitude;
    private Double gpsLocationLatitude;
    private Boolean isBooking;
    private Boolean needPilot;
    private Integer chimney;
    private Integer roofEstimatedAreaItem;
    private Integer reportServiceOption;

    private Long damageEventTime;

    private String specialInstructions;
    private String specialInstructionComments;
    private String policyEffectiveDate;
    private String yearBuilt;

    private Integer payStatus;

    private String companyName;

    private String companyLogo;
    private String insuranceCompanyNameLogo;
    private String repairCompanyNameLogo;

    private Long daysOld;

    private Long syncDataTime;

    /** 创建项目时指定订购的报告 */
    private List<Integer> reportTypes;

    private List<MemberInfo> members;

    private List<ProjectStatusVo> timeLines;

    private List<HistoryLogVo> historyLogs;

    private List<ProjectQuizDto> projectQuiz;

    private Long siteInspectedTime;

    private Integer droneImageCount;

    private Integer mobileImageCount;

    private Long discussCount;

    private Double rotationDegree;

    private Integer damageSeverity;

    private Long customerContactedTime;

    private String inspectedBy;

    private List<String> tags;

    private List<Long> projectTags;

    private Integer pirReportStatus;

    private Integer darReportStatus;

    private Integer hoverMarkStatus;
    private Integer plnarMarkStatus;
    private Integer darUploadStatus;
    private Integer pirUploadStatus;

    private String pilotName;

    private String catNumber;

    // 这个字段整合了过多的member和contact，无法直接去除，详细见：com.bees360.service.grpc.impl.BuildProjectInfoConvertersService
    // 已经转移External Adjuster到ContactManager
    private List<ExternalMember> externalMember;

    private String batchNo;

    private String folderType;

    private PilotBatchInfo batchInfo;

    private List<String> pilotFeedbacks;

    private String hoverJobId;

    /** 统一处理的tag */
    private List<ProjectTagEs> projectTagList;

    private Float imageScore = DEFAULT_IMAGE_SCORE;

    private String operatingCompany;

    /** 对于当前保单，保险公司应该赔付保单持有人的金额 */
    private Double estimateTotalPay;

    private String projectState;

    private String projectStateChangeReason;

    private String projectStateChangeReasonId;

    private Boolean isParentProject;

    private Boolean isChildProject;

    @Override
    public Long id() {
        return projectId;
    }

    public Double getTotalPay() {
        if (batchInfo == null) {
            return 0D;
        }
        return batchInfo.getTotalPay();
    }

    public Long getPaymentDate() {
        if (batchInfo != null) {
            return batchInfo.getPaymentDate();
        }
        return null;
    }

    public String getClaimNumber() {
        if (claimNumber == null || claimNumber.trim().length() == 0) {
            return inspectionNumber;
        }
        return claimNumber;
    }

    public String getFullAddress() {
        return address + ", " + city + ", " + state + " " + zipCode;
    }

    @Data
    static public class Participant {

        private String id;

        private String name;

        private String phone;

        private String email;

        @Nullable
        private String photo;

        public Participant() {}

        public Participant(User user) {
            this.id = user.getId();
            this.name = user.getName();
            this.email = user.getEmail();
            this.phone = user.getPhone();
            this.photo = Optional.ofNullable(user.getPhoto()).map(URL::toString).orElse(null);
        }

        public static Participant from(User user) {
            return new Participant(user);
        }
    }

    private List<Participant> participants;
}
