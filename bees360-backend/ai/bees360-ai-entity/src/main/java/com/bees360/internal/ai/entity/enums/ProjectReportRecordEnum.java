package com.bees360.internal.ai.entity.enums;

/**
 * <AUTHOR>
 * @date 2020/10/26 19:07
 */
public enum ProjectReportRecordEnum {

    HOVER(1, "HOVER" ),
    PLNAR(2, "PLNAR"),
    PROPERTY_IMAGE_REPORT(3, "PIR"),
    PREMIUM_DAMAGE_ASSESSMENT_REPORT(4, "DAR")
    ;

    private final int code;
    private final String type;

    ProjectReportRecordEnum(int code, String type){
        this.code = code;
        this.type = type;
    }
    public int getCode() {
        return code;
    }

    public String getType() {
        return type;
    }
}
