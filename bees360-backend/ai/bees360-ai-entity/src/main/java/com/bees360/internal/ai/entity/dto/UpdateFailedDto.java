package com.bees360.internal.ai.entity.dto;

import com.bees360.internal.ai.exchange.ai2client.CommonProto;
import lombok.Builder;
import lombok.Data;

/**
 * 操作失败信息entity
 *
 * <AUTHOR>
 * @since 2021/4/26
 */
@Data
@Builder
public class UpdateFailedDto {

    /**
     * id
     */
    private long id;

    /**
     * 失败原因（用于前端显示用）
     */
    private String message;

    private CommonProto.UpdateFailed.OperationFeedback operationFeedback;
}
