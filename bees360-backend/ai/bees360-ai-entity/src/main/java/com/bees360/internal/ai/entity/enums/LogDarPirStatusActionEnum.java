package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/07/30 09:27
 */
public enum LogDarPirStatusActionEnum implements LogActionType {

    NOTCENERATED(0, "changed to Not Generated"),
    GENERATED(1, "changed to Generated"),
    SUBMITTED(2, "changed to Under Review"),
    APPROVED(3, "changed to Approved"),
    DISAPPROVED(4, "changed to Disapproved"),
    UPLOADED(5, "changed to Uploaded")
    ;

    private final int code;
    private final String value;

    LogDarPirStatusActionEnum(int code, String value){
        this.code = code;
        this.value = value;
    }

    public static LogDarPirStatusActionEnum getEnumByValue(String value) {
        return Stream.of(LogDarPirStatusActionEnum.values())
            .filter(o -> Objects.equals(value, o.getValue()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogDarPirStatusActionEnum" +
                " is null, value:" + value));

    }

    public static List<CodeNameDto> getEnumDict() {
        return Arrays.stream(LogDarPirStatusActionEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getValue()))
            .collect(Collectors.toList());
    }

    @Override
    public int getCode(){
        return code;
    }

    public String getValue() {
        return value;
    }
}
