package com.bees360.internal.ai.entity;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/09/24 15:02
 */
@Data
public class ImageTagDirectoryVo {

    private List<ImageTag> category;
    private List<ImageTag> object;
    private List<ImageTag> location;
    private List<ImageTag> scope;
    private List<ImageTag> direction;
    private List<ImageTag> annotation;
    private List<ImageTag> report;
    private List<ImageTag> orientation;
    private List<ImageTag> number;
    private List<ImageTag> floorLevel;

    public ImageTagDirectoryVo() {
        this.category = Collections.emptyList();
        this.object = Collections.emptyList();
        this.location = Collections.emptyList();
        this.scope = Collections.emptyList();
        this.direction = Collections.emptyList();
        this.annotation = Collections.emptyList();
        this.report = Collections.emptyList();
        this.orientation = Collections.emptyList();
        this.number = Collections.emptyList();
        this.floorLevel = Collections.emptyList();
    }
}
