package com.bees360.internal.ai.entity.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
public class ClaimWorkloadStatistics {
    private String staffId;
    private final String staffName;
    private long darCount;
    private long count;
    private long estimateCount;
    @Setter
    private long threeDCount;
    private final Long projectId;
    public ClaimWorkloadStatistics(String staffId, String staffName, long darCount, long estimateCount, long threeDCount, Long projectId) {
        this.staffId = staffId;
        this.staffName = staffName;
        this.darCount = darCount;
        this.estimateCount = estimateCount;
        this.projectId = projectId;
        this.threeDCount = threeDCount;
    }

    public ClaimWorkloadStatistics(String staffId, String staffName, Long projectId) {
        this.staffId = staffId;
        this.staffName = staffName;
        this.count = 1;
        this.projectId = projectId;
    }
    public ClaimWorkloadStatistics(String staffName, long count) {
        this.staffName = staffName;
        this.count = count;
        this.projectId = null;
    }
}
