package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/07/30 09:27
 */
public enum LogDarPirStatusDetailEnum implements LogDetailType {

    DAR_STATUS(1, "DAR Status", ProjectReportRecordEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT.getType()),
    PIR_STATUS(2, "PIR Status", ProjectReportRecordEnum.PROPERTY_IMAGE_REPORT.getType());

    private final int code;
    private final String value;
    private final String type;

    LogDarPirStatusDetailEnum(int code, String value, String type) {
        this.code = code;
        this.value = value;
        this.type = type;
    }

    public static LogDarPirStatusDetailEnum getEnumByValue(String type) {
        return Stream.of(LogDarPirStatusDetailEnum.values())
            .filter(o -> Objects.equals(type, o.getValue()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogHoverPlnarStatusDetailEnum" +
                " is null, type:" + type));
    }

    public static List<CodeNameDto> getEnumDict() {
        return Arrays.stream(LogDarPirStatusDetailEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getValue()))
            .collect(Collectors.toList());
    }

    @Override
    public int getCode(){
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
