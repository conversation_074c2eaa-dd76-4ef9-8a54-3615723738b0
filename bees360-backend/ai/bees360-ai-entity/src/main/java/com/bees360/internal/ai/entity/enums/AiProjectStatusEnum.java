package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;
import com.bees360.internal.ai.entity.dto.ProcessStatusEnum;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 项目的状态
 *
 * <AUTHOR>
 * @date 2019/12/26 10:46
 */
public enum AiProjectStatusEnum implements LogDetailType {

    PROJECT_RECOVERED(Integer.MIN_VALUE, "project_recovered", "Project Recovered", null, false),

    PROJECT_ARCHIVED(-99, "project_archived", "Project Archived", ProcessStatusEnum.DELETED, true),

    PROJECT_DELETED(-2, "project_deleted", "Project Deleted", ProcessStatusEnum.DELETED, true),

    PROJECT_CANCELED(-1, "project_canceled", "Project Canceled", ProcessStatusEnum.DELETED, true),
    /**
     * 项目创建成功
     */
    PROJECT_CREATED(10, "project_created", "Project Created", ProcessStatusEnum.NEW, true),
    /**
     * 联系客户
     */
    CUSTOMER_CONTACTED(30, "customer_contacted", "Customer Contacted", ProcessStatusEnum.NEW, true),

    /**
     * 项目预分配给飞手，等待飞手接收
     */
    PENDING_ACCEPTANCE(40, "Pending Acceptance", "Pending Acceptance", ProcessStatusEnum.NEW, true),
    /**
     * 项目已经分配给飞手
     */
    ASSIGNED_TO_PILOT(50, "assigned_to_pilot", "Assigned to Pilot", ProcessStatusEnum.NEW, true),
    /**
     * project image需要重新拍摄
     */
    PROJECT_REWORK(60, "Project Rework", "Project Rework", ProcessStatusEnum.NEW, true),

    /**
     * 现场检测完成，也就是采集照片完成
     */
    SITE_INSPECTED(70, "site_inspected", "Site Inspected", ProcessStatusEnum.IMAGE_UPLOADED, true),

    /**
     * 分配人员完成
     * @deprecated 该状态已废弃
     */
    @Deprecated
    MEMBER_ASSIGNED(72, "member_assigned", "Member Assigned", ProcessStatusEnum.IN_PROCESS, true),

    /**
     * 图片处理完成，也就是refine finished
     */
    THREE_D_REFINED_FINISHED_PROCESSED(73, "3d_processed", "3D Processed", ProcessStatusEnum.IN_PROCESS, true),
    /**
     * 报告创建，当Processor在Editor界面点击Submit按钮后触发
     */
    REPORT_ASSEMBLED(75, "report_assembled", "Report Assembled", ProcessStatusEnum.UNDER_REVIEW, true),

    /**
     * 房屋损坏评估完成，当Adjuster在Editor界面点击Submit按钮后触发
     */
    DAMAGES_ASSESSED(78, "damages_assessed", "Damages Assessed", ProcessStatusEnum.UNDER_REVIEW, true),

    /**
     * ibees上传图片全部上传到了我们平台上
     */
    IBEES_UPLOADED(79, "IBees Uploaded", "IBees Uploaded", ProcessStatusEnum.IMAGE_UPLOADED, true),

    /**
     * 采集照片完成并全部上传到了我们平台上
     */
    IMAGE_UPLOADED(80, "Image Uploaded", "Image Uploaded", ProcessStatusEnum.IMAGE_UPLOADED, true),


    REVIEWER_APPROVED(85, "reviewer_approved", "Reviewer Approved", ProcessStatusEnum.REPORT_APPROVED, true),
    /**
     * 报告完成并且提交已提交给客户，此时所有的报告的状态均为APPROVED状态
     */
    RETURNED_TO_CLIENT(90, "returned_to_client", "Returned to Client", ProcessStatusEnum.AI_FINISHED_RETURN_TO_CLIENT,
        true),

    ESTIMATE_COMPLETED(95, "estimate_completed", "Estimate Completed", ProcessStatusEnum.AI_FINISHED_RETURN_TO_CLIENT,
        true),

    CLIENT_RECEIVED(100, "Client Received", "Client Received", ProcessStatusEnum.CLIENT_RECEIVED, true),

    RECEIVE_ERROR(101, "Receive Error", "Receive Error", ProcessStatusEnum.CLIENT_RECEIVED, true),
    ;

    private static final Set<AiProjectStatusEnum> COMPLETED_STATUS_SET =
            Set.of(RETURNED_TO_CLIENT, CLIENT_RECEIVED, PROJECT_ARCHIVED);

    /**
     * 不可变不可重复状态值
     */
    private final int code;
    /**
     * 不可变不可重复字符串状态值
     */
    private final String value;
    /**
     * 可变状态值名称
     */
    private final String display;
    private final ProcessStatusEnum processStatus;

    private final boolean visible;

    AiProjectStatusEnum(int code, String value, String display, ProcessStatusEnum processStatus, boolean visible) {
        this.code = code;
        this.value = value;
        this.display = display;
        this.processStatus = processStatus;
        this.visible = visible;
    }

    @Override
    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    public boolean isVisible() {
        return visible;
    }

    public ProcessStatusEnum getProcessStatus() {
        return processStatus;
    }

    public static boolean containsCode(Integer code) {
        return Objects.nonNull(getEnum(code));
    }

    public static AiProjectStatusEnum getEnum(Integer code) {
        if (code == null) {
            return null;
        }
        for (AiProjectStatusEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    public static String getStatusDesc(Integer code) {
        if (code == null) {
            return null;
        }
        for (AiProjectStatusEnum status : values()) {
            if (status.getCode() == code) {
                return status.getDisplay();
            }
        }
        return null;
    }

    public static AiProjectStatusEnum getEnumByValue(String value) {
        for (AiProjectStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    public static List<CodeNameDto> getEnumDict() {
        return Arrays.stream(AiProjectStatusEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getDisplay()))
            .collect(Collectors.toList());
    }

    public static Set<Integer> getStatusCodes(AiProjectStatusEnum... enums) {
        if (enums == null) {
            return Collections.emptySet();
        }
        return Arrays.stream(enums).map(AiProjectStatusEnum::getCode).collect(Collectors.toSet());
    }

    public static List<Integer> getStatusCodeList(AiProjectStatusEnum... enums) {
        if (enums == null) {
            return Collections.emptyList();
        }
        return Arrays.stream(enums).map(AiProjectStatusEnum::getCode).collect(Collectors.toList());
    }

    public static List<CodeNameDto> getVisibleDict() {
        return Arrays.stream(AiProjectStatusEnum.values())
            .filter(AiProjectStatusEnum::isVisible).map(o -> new CodeNameDto(o.getCode(), o.getDisplay()))
            .collect(Collectors.toList());
    }

    /**
     * 状态是否可以强制更新，默认code码大小控制状态变更流程 除了删除状态和图片上传code
     *
     * @param oldStatus
     * @param newStatus
     * @return
     */
    public static boolean forceUpdateStatusCheck(int oldStatus, int newStatus) {
        return getDeletedStatusCode().contains(newStatus) || Arrays
            .asList(IMAGE_UPLOADED.getCode(), SITE_INSPECTED.getCode()).contains(oldStatus);
    }

    public static List<Integer> getDeletedStatusCode() {
        return Arrays.asList(PROJECT_ARCHIVED.getCode(), PROJECT_DELETED.getCode(), PROJECT_CANCELED.getCode());
    }

    public static boolean canRecover(int latestStatus, int newStatus) {
        return PROJECT_CANCELED == getEnum(latestStatus) && PROJECT_RECOVERED == getEnum(newStatus);
    }

    public static boolean isCanceledStatus(int newStatus) {
        return Objects.equals(PROJECT_CANCELED.getCode(), newStatus);
    }

    public static boolean isComplete(AiProjectStatusEnum status) {
        return COMPLETED_STATUS_SET.contains(status);
    }
}
