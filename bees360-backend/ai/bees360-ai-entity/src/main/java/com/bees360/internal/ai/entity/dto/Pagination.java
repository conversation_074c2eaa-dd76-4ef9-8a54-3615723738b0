package com.bees360.internal.ai.entity.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2019/09/02 15:43
 */
@Getter
@Setter
@ToString
@Builder
public class Pagination {
    private int pageIndex;
    private int pageSize;
    private int totalPage;
    private int sum;

    public static int DEFAULT_PAGE_START = 1;
    public static int DEFAULT_PAGE_SIZE = 10000;


    public static Pagination empty(int pageIndex, int pageSize) {
        // @formatter:off
        return Pagination.builder()
            .pageIndex(pageIndex)
            .pageSize(pageSize)
            .totalPage(0)
            .sum(0)
            .build();
        // @formatter:on
    }

    public static int countTotalPage(int sum, int pageSize) {
        return (sum + pageSize - 1) / pageSize;
    }

    public static int countIndexFrom(int pageIndex, int pageSize) {
        return (pageIndex - 1) * pageSize;
    }

    public static int countIndexTo(int pageIndex, int pageSize, int sum) {
        return Math.min(countIndexFrom(pageIndex, pageSize) + pageSize, sum);
    }

    public int countIndexFrom() {
        return countIndexFrom(pageIndex, pageSize);
    }

    public int countIndexTo() {
        return countIndexTo(pageIndex, pageSize, sum);
    }
}
