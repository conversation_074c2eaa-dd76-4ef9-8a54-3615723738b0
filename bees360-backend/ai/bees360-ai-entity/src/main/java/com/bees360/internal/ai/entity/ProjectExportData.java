package com.bees360.internal.ai.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class ProjectExportData {

    /**
     * unique id
     */
    private long id;

    /**
     * type 对应存储的id值， eg: 存projectId
     */
    private String relatedId;

    /**
     *  数据所属类型
     */
    private String relatedType;

    /**
     *  json数据
     */
    private String dataLog;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Timestamp updatedTime;
}
