package com.bees360.internal.ai.entity.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Builder(builderClassName = "Builder", builderMethodName = "newBuilder", setterPrefix = "set")
public class ProcessorUWStatistics {
    private final String staffName;
    private final String companyName;
    private final long generated;
    private final String generatedProjects;
    private final long submitted;
    private final String submittedProjects;
    private final long approved;
    private final String approvedProjects;

    // data for Premium 4-point UW.
    private final long generatedPremium;
    private final long submittedPremium;
    private final long approvedPremium;
    private final String staffEmail;

    @Setter
    private String generatedPremiumProjects;
    @Setter
    private String submittedPremiumProjects;
    @Setter
    private String approvedPremiumProjects;
}
