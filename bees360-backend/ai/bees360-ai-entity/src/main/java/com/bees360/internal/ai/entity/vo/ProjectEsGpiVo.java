package com.bees360.internal.ai.entity.vo;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/06/12 17:46
 */
@Data
@Builder
public class ProjectEsGpiVo {

	private  String assetOwnerName;
	private  String assetOwnerPhone;
	private  String assetOwnerEmail;

	private  Integer projectType;
	private  String address;
	private  String city;
	private  String state;
	private  String country;
	private  String zipCode;
	private  Integer flyZoneType;

	private  String inspectionNumber;
	private  Long dueDate;
	private  String customer;
	private  String inspectionType;
	private  String agent;
	private  String agentEmail;
	private  String agentContactName;
	private  String agentPhone;
	private  String guideline;
	private  String insuredHomePhone;
	private  String insuredWorkPhone;

	private  String insuranceCompanyName;
	private  String repairCompanyName;
    private  String insuranceCompanyLogo;
    private  String repairCompanyLogo;

	private  String description;
	private  double lng;
	private  double lat;

	private  boolean isBooking;
	private  boolean needPilot;
	private  Integer roofEstimatedAreaItem;
	private  Integer reportServiceOption;
	private  int chimney;

	private Integer serviceType;

	private String serviceName;
}
