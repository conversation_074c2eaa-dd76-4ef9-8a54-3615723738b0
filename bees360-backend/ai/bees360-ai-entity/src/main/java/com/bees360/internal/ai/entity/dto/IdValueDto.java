package com.bees360.internal.ai.entity.dto;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class IdValueDto<T, V> implements Serializable {

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;

    private T id;
    private V value;

    public IdValueDto() {
    }

    public IdValueDto(T id, V value) {
        this.id = id;
        this.value = value;
    }

    //	Getter and Setter
    public T getId() {
        return id;
    }

    public void setId(T id) {
        this.id = id;
    }

    public V getValue() {
        return value;
    }

    public void setValue(V value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "IdNameDto [id=" + id + ", name=" + value + "]";
    }
}
