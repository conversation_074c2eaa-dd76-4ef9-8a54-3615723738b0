package com.bees360.internal.ai.entity.enums.images;

import java.util.Arrays;

/** 楼层信息tag */
public enum FloorLevelTagEnum {
    ENTRY_LEVEl(401, "Entry Level"),
    SECOND_FLOOR(402, "2nd Floor"),
    THIRD_FLOOR(403, "3rd Floor"),
    FOURTH_FLOOR_OR_HIGHER(404, "4th Floor or higher"),
    GARDEN_LEVEL(407, "Garden Level"),
    BASEMENT(405, "Basement"),
    ;

    private final int code;
    private final String display;

    FloorLevelTagEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    public static FloorLevelTagEnum valueOfDisplayIgnoreCase(String display) {
        if (null == display) {
            return null;
        }
        return Arrays.stream(values())
                .filter(o -> o.getDisplay().equalsIgnoreCase(display))
                .findFirst()
                .orElse(null);
    }

    public int getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }
}
