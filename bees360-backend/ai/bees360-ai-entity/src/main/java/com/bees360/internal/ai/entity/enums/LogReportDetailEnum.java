package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 记录report的logDetail
 * 便于区分跟后续移除掉projectStatusEnum
 * <AUTHOR>
 * @date 2020/07/30 09:27
 */
public enum LogReportDetailEnum implements LogDetailType {


    PREMIUM_DAMAGE_ASSESSMENT_REPORT(1, "Premium Damage Assessment Report", ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT.getCode()),

    PREMIUM_ROOF_MEASUREMENT_REPORT(2, "Premium Measurement Report", ReportTypeEnum.PREMIUM_ROOF_MEASUREMENT_REPORT.getCode()),

    MACRO_XML_REPORT(3, "Macro XML Report", ReportTypeEnum.MACRO_XML_REPORT.getCode()),

    QUICK_DAMAGE_ASSESSMENT_REPORT(4, "Preliminary Damage Assessment Report", ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT.getCode()),

    QUICK_ROOF_EVALUATION_REPORT(5, "Highfly Evaluation Report", ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT.getCode()),

    REALTIME_ROOF_INSPECTION_REPORT(6, "Real-time Damage Assessment Report", ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT.getCode()),

    WEATHER_REPORT(8, "Weather Report", ReportTypeEnum.WEATHER_REPORT.getCode()),

    ESTIMATE_REPORT(9, "Estimate Report", ReportTypeEnum.ESTIMATE_REPORT.getCode()),

    DXF_REPORT(10, "Measurement DXF Report", ReportTypeEnum.DXF_REPORT.getCode()),

    BID_REPORT(11, "On-Site Bidding Report", ReportTypeEnum.BID_REPORT.getCode()),

    QUICK_SQUARE_REPORT(12, "Real-time Quick Square Report", ReportTypeEnum.QUICK_SQUARE_REPORT.getCode()),

    PROPERTY_IMAGE_REPORT(13, "Property Image Report", ReportTypeEnum.PROPERTY_IMAGE_REPORT.getCode()),

    INFRARED_DAMAGE_REPORT(14, "Infrared Damage Assessment Report", ReportTypeEnum.INFRARED_DAMAGE_REPORT.getCode()),

    ROOF_ONLY_UNDERWRITING_REPORT(15, "Roof-only Underwriting Report", ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT.getCode()),

    FULL_SCOPE_UNDERWRITING_REPORT(16, "Full-scope Underwriting Report", ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT.getCode()),

    INSPECTION_CLOSEOUT_REPORT(17, "Inspection Closeout Report", ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT.getCode()),

    INVOICE_REPORT(18, "Invoice Report", ReportTypeEnum.INVOICE.getCode()),

    CUSTOM_HOME_REPORT(19, "Custom Home Report", ReportTypeEnum.CUSTOM_HOME_REPORT.getCode()),

    DRONE_PHOTO_SHEET(20, "Drone Photo Sheet", ReportTypeEnum.DRONE_PHOTO_SHEET.getCode()),

    MOBILE_PHOTO_SHEET(21, "Mobile Photo Sheet", ReportTypeEnum.MOBILE_PHOTO_SHEET.getCode()),

    CLAIM_DAMAGE_FORM(22, "Claim Damage Form", ReportTypeEnum.CLAIM_DAMAGE_FORM.getCode()),

    POST_CONSTRUCTION_AUDIT_REPORT(23, "Post-Construction Audit Report", ReportTypeEnum.POST_CONSTRUCTION_AUDIT_REPORT.getCode()),

    HOVER_PRO_REPORT(24, "HOVER Pro Report", ReportTypeEnum.HOVER_PRO_REPORT.getCode()),

    HOVER_ESX(25, "HOVER", ReportTypeEnum.HOVER_ESX.getCode()),

    XACT_ESX(26, "XACT", ReportTypeEnum.XACT_ESX.getCode()),

    MAGIC_PLAN(27, "MagicPlan report", ReportTypeEnum.MAGIC_PLAN.getCode()),

    HOMEOWNER_SURVEY(28, "Homeowner Inspection Survey Results", ReportTypeEnum.HOMEOWNER_SURVEY.getCode()),

    CHECKLIST(32, "Checklist", ReportTypeEnum.CHECKLIST.getCode()),

    CLAIM(33, "Claim Report", ReportTypeEnum.CLAIM.getCode()),

    GENERAL_LOSS_REPORT(34, "General Loss Report", ReportTypeEnum.GENERAL_LOSS_REPORT.getCode()),

    PLNAR(39, "Plnar", ReportTypeEnum.PLNAR.getCode()),

    CUBICASA(40, "Cubicasa", ReportTypeEnum.CUBICASA.getCode()),

    HOVER_TLA(41, "Hover TLA", ReportTypeEnum.HOVER_TLA.getCode()),

    FSR_RCE(42, "Full-scope Underwriting Report with Recovery Cost Estimate", ReportTypeEnum.FSR_RCE.getCode()),

    OCC(43, "OneClick Code Report", ReportTypeEnum.OCC.getCode()),

    EVRP(44, "EagleView Report", ReportTypeEnum.EVRP.getCode()),

    EVOB(45, "EV .OBJ", ReportTypeEnum.EVOB.getCode()),

    EUR(46, "Express Underwriting Report", ReportTypeEnum.EUR.getCode()),

    CUBICASA_VIDEO_ZIP(47, "Cubicasa Video Zip", ReportTypeEnum.CUBICASA_VIDEO_ZIP.getCode()),

    CUBICASA_VIDEO(48, "Cubicasa Video", ReportTypeEnum.CUBICASA_VIDEO.getCode()),

    HOSTA_SUMMARY_ZIP(49, "Hosta Summary Zip", ReportTypeEnum.HOSTA_SUMMARY_ZIP.getCode()),

    HOSTA_MODEL_ZIP(50, "Hosta Model Zip", ReportTypeEnum.HOSTA_MODEL_ZIP.getCode()),

    BEESSKETCH(51, "BeesSketch", ReportTypeEnum.BEESSKETCH.getCode()),

    SNAP(52, "Snap360 Report", ReportTypeEnum.SNAP.getCode()),
    ;

    private final int code;
    private final String value;
    private final int reportType;

    LogReportDetailEnum(int code, String value, int reportType){
        this.code = code;
        this.value = value;
        this.reportType = reportType;
    }

    public static LogReportDetailEnum getEnumByValue(String value) {
        return Stream.of(LogReportDetailEnum.values())
            .filter(o -> Objects.equals(value, o.getValue()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogReportDetailEnum is null, value:" + value));
    }

    public static List<CodeNameDto> getEnumDict() {
        return Arrays.stream(LogReportDetailEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getValue()))
            .collect(Collectors.toList());
    }

    public static LogReportDetailEnum getEnumByReportType(int reportType) {
        return Stream.of(LogReportDetailEnum.values()).filter(o -> Objects.equals(reportType, o.getReportType())).findFirst().orElse(null);

    }

    @Override
    public int getCode(){
        return code;
    }

    public String getValue() {
        return value;
    }

    public int getReportType() {
        return reportType;
    }
}
