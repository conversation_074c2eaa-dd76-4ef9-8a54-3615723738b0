package com.bees360.internal.ai.entity.enums.images;

import com.bees360.image.ImageTagEnum;
import com.bees360.internal.ai.entity.consts.ImageTagCodeDict;
import com.bees360.internal.ai.entity.enums.ImagePartialTypeEnum;
import com.bees360.internal.ai.entity.enums.ImageTypeEnum;
import com.bees360.internal.ai.entity.enums.OrientationEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 图片category tag,自动映射
 * tagCode详情见imageTagDict table,没有额外加一个enum
 *
 * <AUTHOR>
 * @date 2020/11/09 14:46
 */
public enum FirebaseImageCategoryTagEnum {

    //    Address Verification -> Address Verification
    ADDRESS_VERIFICATION(1, "Address Verification", Arrays.asList(ImageTagCodeDict.ADDRESS_VERIFICATION), ImagePartialTypeEnum.ADDRESS),

    // Front Elevation -> Front, Elevation
    FRONT_ELEVATION(2, "Front Elevation", Arrays.asList(ImageTagCodeDict.FRONT, ImageTagCodeDict.ELEVATION), ImagePartialTypeEnum.ELEVATION),

    //Back Elevation -> Back, Elevation
    BACK_ELEVATION(3, "Back Elevation", Arrays.asList(ImageTagCodeDict.REAR, ImageTagCodeDict.ELEVATION), ImagePartialTypeEnum.ELEVATION),

    //Left Elevation -> Left, Elevation
    LEFT_ELEVATION(4, "Left Elevation", Arrays.asList(ImageTagCodeDict.LEFT, ImageTagCodeDict.ELEVATION), ImagePartialTypeEnum.ELEVATION),

    //Right Elevation -> Right, Elevation
    RIGHT_ELEVATION(5, "Right Elevation", Arrays.asList(ImageTagCodeDict.RIGHT, ImageTagCodeDict.ELEVATION), ImagePartialTypeEnum.ELEVATION),

    //Interior -> Interior
    INTERIOR(6, "Interior", Arrays.asList(ImageTagCodeDict.INTERIOR), ImagePartialTypeEnum.INTERIOR),

    //Garage -> Detached Garage
    GARAGE(7, "Garage", Arrays.asList(ImageTagCodeDict.DETACHED_GARAGE), ImagePartialTypeEnum.GARAGE),

    //Other Structures -> Other Structure
    APS(8, "Other Structures", Arrays.asList(ImageTagCodeDict.OTHER_STRUCTURE), ImagePartialTypeEnum.APS),

    //Contents-> Content
    CONTENTS(9, "Contents", Arrays.asList(ImageTagCodeDict.CONTENT), ImagePartialTypeEnum.OTHERS),

    //Roof Overhead -> Roof, Overview
    OVERHEAD(10, "Roof Overhead", Arrays.asList(ImageTagCodeDict.ROOF, ImageTagCodeDict.OVERVIEW), ImagePartialTypeEnum.ROOF),

    //Birdview -> Roof
    BIRDVIEW(11, "Birdview", Arrays.asList(ImageTagCodeDict.ROOF), ImagePartialTypeEnum.ROOF),

    //Closeup -> Roof, Closeup
    CLOSEUP(12, "Closeup", Arrays.asList(ImageTagCodeDict.ROOF, ImageTagCodeDict.CLOSEUP), ImagePartialTypeEnum.ROOF),

    //Others -> Roof
    OTHERS(13, "Others", Arrays.asList(ImageTagCodeDict.ROOF), ImagePartialTypeEnum.ROOF),

    //Electrical Panel ->Electrical Panel， Interior
    ELECTRICAL_PANEL(14, "Electrical Panel", Arrays.asList(ImageTagCodeDict.ELECTRICAL_PANEL, ImageTagCodeDict.INTERIOR), ImagePartialTypeEnum.ELECTRICAL_PANEL),

    //Plumbing System -> Supply Lines， Interior
    PLUMBING_SYSTEM(15, "Plumbing System", Arrays.asList(ImageTagCodeDict.SUPPLY_LINES, ImageTagCodeDict.INTERIOR), ImagePartialTypeEnum.PLUMBING_SYSTEM),

    //Water Heater -> Water Heater， Interior
    WATER_HEATER(16, "Water Heater", Arrays.asList(ImageTagCodeDict.WATER_HEATER, ImageTagCodeDict.INTERIOR), ImagePartialTypeEnum.WATER_HEATER),

    //Hazards -> Hazards
    HAZARDS(17, "Hazards", Arrays.asList(ImageTagCodeDict.HAZARDS), ImagePartialTypeEnum.HAZARDS),

    //Zigzag -> Zigzag,roof
    ZIGZAG(18, "Zigzag", List.of(ImageTagCodeDict.ZIGZAG, ImageTagCodeDict.ROOF), ImagePartialTypeEnum.ROOF),

    //Roof Layer -> Roof Layer,roof
    ROOF_LAYER(19, "Roof Layer", List.of(ImageTagCodeDict.ROOF_LAYER, ImageTagCodeDict.ROOF), ImagePartialTypeEnum.ROOF),

    //Room Overview -> interior, Overview
    ROOM_OVERVIEW(20, "Room Overview", List.of(ImageTagCodeDict.INTERIOR, ImageTagCodeDict.OVERVIEW),
        ImagePartialTypeEnum.ROOM_OVERVIEW),

    //Floor Overview -> interior, Floor, overview
    FLOOR_OVERVIEW(21, "Floor Overview",
        List.of(ImageTagCodeDict.INTERIOR, ImageTagCodeDict.FLOOR, ImageTagCodeDict.OVERVIEW),
        ImagePartialTypeEnum.FLOOR_OVERVIEW),

    //Ceiling Overview -> interior, ceiling, overview
    CEILING_OVERVIEW(22, "Ceiling Overview",
        List.of(ImageTagCodeDict.INTERIOR, ImageTagCodeDict.CEILING, ImageTagCodeDict.OVERVIEW),
        ImagePartialTypeEnum.CEILING_OVERVIEW),

    //Damage Area -> interior，Damage
    DAMAGE_AREA(23, "Damage Area", List.of(ImageTagCodeDict.DAMAGE, ImageTagCodeDict.INTERIOR),
        ImagePartialTypeEnum.DAMAGE_AREA),

    //Other Items -> interior，Other Items
    OTHER_ITEMS(24, "Other Items", List.of(ImageTagCodeDict.OTHER_ITEMS, ImageTagCodeDict.INTERIOR),
        ImagePartialTypeEnum.OTHER_ITEMS),

    PROPERTY_OVERVIEW(28, "Alarm", List.of(ImageTagEnum.ROOF.getCode(), ImageTagEnum.OVERVIEW.getCode(), ImageTagEnum.FENCE_LINE.getCode()),
        ImagePartialTypeEnum.PROPERTY_OVERVIEW),

    ALARM(29, "Alarm", List.of(ImageTagEnum.ALARM.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.ALARM),
    GENERATOR(30, "Generator", List.of(ImageTagEnum.GENERATOR.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.GENERATOR),
    WATER_LEAK_DETECTION(31, "Water Leak Detection", List.of(ImageTagEnum.WATER_LEAK_DETECTION.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.WATER_LEAK_DETECTION),
    DRY_HYDRANTS(32, "Dry Hydrants", List.of(ImageTagEnum.DRY_HYDRANTS.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.DRY_HYDRANTS),
    HOME_SPRINKLERS(33, "Home Sprinklers", List.of(ImageTagEnum.HOME_SPRINKLERS.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.HOME_SPRINKLERS),
    FIRE_EXTINGUISHERS(34, "Fire Extinguishers", List.of(ImageTagEnum.FIRE_EXTINGUISHERS.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.FIRE_EXTINGUISHERS),
    FIRE_PROOF_CABINETS(35, "Fire Proof Cabinets", List.of(ImageTagEnum.FIRE_PROOF_CABINETS.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.FIRE_PROOF_CABINETS),
    FLAMMABLE_RAG(36, "Flammable Rag", List.of(ImageTagEnum.FLAMMABLE_RAG.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.FLAMMABLE_RAG),
    NO_SMOKING_SIGNS(37, "No Smoking Signs", List.of(ImageTagEnum.NO_SMOKING_SIGNS.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.NO_SMOKING_SIGNS),
    HAZARDOUS_ADJACENT_PROPERTY(39, "Hazardous Adjacent Property",
        List.of(ImageTagEnum.HAZARDOUS_ADJACENT_PROPERTY.getCode()),
        ImagePartialTypeEnum.HAZARDOUS_ADJACENT_PROPERTY),
    FIRE_ALARM(40, "Fire Alarm",
        List.of(ImageTagEnum.ALARM.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.FIRE_ALARM),
    BURGLAR_ALARM(41, "Burglar Alarm",
        List.of(ImageTagEnum.BURGLAR_ALARM.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.BURGLAR_ALARM),
    FIRE_SPRINKLERS(42, "Fire Sprinklers",
        List.of(ImageTagEnum.HOME_SPRINKLERS.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.FIRE_SPRINKLERS),
    LOW_TEMP_SYSTEM(43, "Low-Temp System",
        List.of(ImageTagEnum.LOW_TEMP_SYSTEM.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.CONTROL_PANEL.getCode()),
        ImagePartialTypeEnum.LOW_TEMP_SYSTEM),
    GAS_LEAK_DETECTION(44, "Gas Leak Detection",
        List.of(ImageTagEnum.GAS_LEAK_DETECTION.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.CONTROL_PANEL.getCode()),
        ImagePartialTypeEnum.GAS_LEAK_DETECTION),
    LEED_CERTIFICATE(45, "Leed Certificate",
        List.of(ImageTagEnum.LEED.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.LEED_CERTIFICATE),
    CARETAKER(48, "Caretaker",
        List.of(ImageTagEnum.CARETAKER.getCode()),
        ImagePartialTypeEnum.CARETAKER),
    DOORMAN_24_HOUR(51, "Doorman 24 Hour",
        List.of(ImageTagEnum.DOORMAN.getCode()),
        ImagePartialTypeEnum.DOORMAN_24_HOUR),
    ELEVATOR(52, "Elevator",
        List.of(ImageTagEnum.ELEVATOR_ROOM.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.ELEVATOR),
    IMPACT_RATED_GLASS_MARK(53, "Impact Rated Glass Mark",
        List.of(ImageTagEnum.IMPACT_RATED_GLASS.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.IMPACT_RATED_GLASS_MARK),
    WIND_LOAD_STICKER(54, "Wind Load Sticker",
        List.of(ImageTagEnum.WIND_LOAD_STICKER.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.WIND_LOAD_STICKER),
    WINDOW_PROTECTION(55, "Window Protection",
        List.of(ImageTagEnum.WINDOW_PROTECTION.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.WINDOW_PROTECTION),

    WASHING_MACHINE_OVERVIEW(9004, "Washing Machine Overview",
        List.of(ImageTagEnum.WASHER.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.WASHING_MACHINE_OVERVIEW),
    WASHING_MACHINE_SUPPLY_LINES(9005, "Washing Machine Supply Lines",
        List.of(ImageTagEnum.WASHER.getCode(), ImageTagEnum.SUPPLY_LINES.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.WASHING_MACHINE_SUPPLY_LINES),
    WASHING_MACHINE_SERIAL_NUMBER(9006, "Washing Machine Serial Number",
        List.of(ImageTagEnum.WASHER.getCode(), ImageTagEnum.SERIAL_NUMBER.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.WASHING_MACHINE_SERIAL_NUMBER),
    DRYER_SERIAL_NUMBER(9007, "Washing Machine Dryer Serial Number",
        List.of(ImageTagEnum.DRYER.getCode(), ImageTagEnum.SERIAL_NUMBER.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.DRYER_SERIAL_NUMBER),
    DISHWASHER_OVERVIEW(9030, "Dishwasher Overview",
        List.of(ImageTagEnum.DISHWASHER.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.DISHWASHER_OVERVIEW),
    DISHWASHER_DOOR_OPEN(9031, "Dishwasher Door Open",
        List.of(ImageTagEnum.DISHWASHER.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.DISHWASHER_DOOR_OPEN),
    DISHWASHER_SERIAL_NUMBER(9032, "Dishwasher Serial Number",
        List.of(ImageTagEnum.DISHWASHER.getCode(), ImageTagEnum.SERIAL_NUMBER.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.DISHWASHER_SERIAL_NUMBER),
    REFRIGERATOR_OVERVIEW(9040, "Refrigerator Overview",
        List.of(ImageTagEnum.REFRIGERATOR.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.REFRIGERATOR_OVERVIEW),
    REFRIGERATOR_SERIAL_NUMBER(9041, "Refrigerator Serial Number",
        List.of(ImageTagEnum.REFRIGERATOR.getCode(), ImageTagEnum.SERIAL_NUMBER.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.REFRIGERATOR_SERIAL_NUMBER),
    RANGE_OVERVIEW(9050, "Range Overview",
        List.of(ImageTagEnum.RANGE.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.RANGE_OVERVIEW),
    RANGE_SERIAL_NUMBER(9051, "Range Serial Number",
        List.of(ImageTagEnum.RANGE.getCode(), ImageTagEnum.SERIAL_NUMBER.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.RANGE_SERIAL_NUMBER),
    REVERSE_OSMOSIS_SYSTEM_OVERVIEW(9060, "Reverse Osmosis System Overview",
        List.of(ImageTagEnum.REVERSE_OSMOSIS_SYSTEM.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.REVERSE_OSMOSIS_SYSTEM_OVERVIEW),
    REVERSE_OSMOSIS_SYSTEM_SERIAL_NUMBER(9061, "Reverse Osmosis System Serial Number",
        List.of(ImageTagEnum.REVERSE_OSMOSIS_SYSTEM.getCode(), ImageTagEnum.SERIAL_NUMBER.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.REVERSE_OSMOSIS_SYSTEM_SERIAL_NUMBER),
    SOLAR_PANEL_BATTERY_BACK_UP(9070, "Solar Panel Battery Back Up",
        List.of(ImageTagEnum.BACK_UP_BATTERY.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.SOLAR_PANEL_BATTERY_BACK_UP),
    WATER_SHUT_OFF_PANEL(9080, "Water Shut Off Panel",
        List.of(ImageTagEnum.WATER_SHUT_OFF_PANEL.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.WATER_SHUT_OFF_PANEL),

    WATER_LEAK_DETECTION_CERTIFICATE(31002, "Water Leak Detection Certificate",
        List.of(ImageTagEnum.WATER_LEAK_DETECTION.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.CERTIFICATE.getCode()),
        ImagePartialTypeEnum.WATER_LEAK_DETECTION_CERTIFICATE),
    FIRE_ALARM_CONTROL_PANEL(40001, "Fire Alarm Control Panel",
        List.of(ImageTagEnum.ALARM.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.CONTROL_PANEL.getCode()),
        ImagePartialTypeEnum.FIRE_ALARM_CONTROL_PANEL),
    FIRE_ALARM_CERTIFICATE(40002, "Fire Alarm Certificate",
        List.of(ImageTagEnum.ALARM.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.CERTIFICATE.getCode()),
        ImagePartialTypeEnum.FIRE_ALARM_CERTIFICATE),
    BURGLAR_ALARM_CONTROL_PANEL(41001, "Burglar Alarm Control Panel",
        List.of(ImageTagEnum.BURGLAR_ALARM.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.CONTROL_PANEL.getCode()),
        ImagePartialTypeEnum.BURGLAR_ALARM_CONTROL_PANEL),
    BURGLAR_ALARM_CERTIFICATE(41002, "Burglar Alarm Certificate",
        List.of(ImageTagEnum.BURGLAR_ALARM.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.CERTIFICATE.getCode()),
        ImagePartialTypeEnum.BURGLAR_ALARM_CERTIFICATE),
    FIRE_SPRINKLERS_FLOW_SWITCH(42001, "Fire Sprinklers Flow Switch",
        List.of(ImageTagEnum.FIRE_SPRINKLERS_FLOW_SWITCH.getCode(), ImageTagEnum.INTERIOR.getCode()),
        ImagePartialTypeEnum.FIRE_SPRINKLERS_FLOW_SWITCH),
    FIRE_SPRINKLERS_CERTIFICATE(42002, "Fire Sprinklers Certificate",
        List.of(ImageTagEnum.HOME_SPRINKLERS.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.CERTIFICATE.getCode()),
        ImagePartialTypeEnum.FIRE_SPRINKLERS_CERTIFICATE),
    LOW_TEMP_SYSTEM_CERTIFICATE(43001, "Low-Temp System Certificate",
        List.of(ImageTagEnum.LOW_TEMP_SYSTEM.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.CERTIFICATE.getCode()),
        ImagePartialTypeEnum.LOW_TEMP_SYSTEM_CERTIFICATE),
    GAS_LEAK_DETECTION_CERTIFICATE(44001, "Gas Leak Detection Certificate",
        List.of(ImageTagEnum.GAS_LEAK_DETECTION.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.CERTIFICATE.getCode()),
        ImagePartialTypeEnum.GAS_LEAK_DETECTION_CERTIFICATE),
    ELEVATOR_CONTROL_PANEL(52001, "Elevator Control Panel",
        List.of(ImageTagEnum.ELEVATOR_ROOM.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.CONTROL_PANEL.getCode()),
        ImagePartialTypeEnum.ELEVATOR_CONTROL_PANEL),
    ELEVATOR_LOBBY_CAMERAS(52002, "Elevator Lobby Cameras",
        List.of(ImageTagEnum.ELEVATOR_ROOM.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.LOBBY_CAMERAS.getCode()),
        ImagePartialTypeEnum.ELEVATOR_LOBBY_CAMERAS),
    ELEVATOR_CERTIFICATE(52003, "Elevator Certificate",
        List.of(ImageTagEnum.ELEVATOR_ROOM.getCode(), ImageTagEnum.INTERIOR.getCode(), ImageTagEnum.CERTIFICATE.getCode()),
        ImagePartialTypeEnum.ELEVATOR_CERTIFICATE),
    ;

    private final int categoryId;
    private final String display;
    private final List<Integer> tagCodes;
    private final ImagePartialTypeEnum partialTypeEnum;

    FirebaseImageCategoryTagEnum(int categoryId, String display, List<Integer> tagCodes, ImagePartialTypeEnum partialTypeEnum) {
        this.categoryId = categoryId;
        this.display = display;
        this.tagCodes = tagCodes;
        this.partialTypeEnum = partialTypeEnum;
    }

    public static FirebaseImageCategoryTagEnum getByImagePartialTypeEnum(ImagePartialTypeEnum typeEnum, int imageType, Integer orientation) {
        FirebaseImageCategoryTagEnum tagEnum = null;
        if (Objects.equals(typeEnum, ImagePartialTypeEnum.ELEVATION)) {
            tagEnum = getImageCategoryTagEnumByOrientation(orientation);
        }
        if (Objects.equals(typeEnum, ImagePartialTypeEnum.ROOF)) {
            if (imageType != 0) {
                if (ImageTypeEnum.OVERVIEW.getCode() == imageType) {
                    tagEnum = FirebaseImageCategoryTagEnum.OVERHEAD;
                }
                if (ImageTypeEnum.BIRDVIEW.getCode() == imageType) {
                    tagEnum = FirebaseImageCategoryTagEnum.BIRDVIEW;
                }
                if (ImageTypeEnum.CLOSEUP.getCode() == imageType) {
                    tagEnum = FirebaseImageCategoryTagEnum.CLOSEUP;
                }
                if (ImageTypeEnum.ROOF_LAYER.getCode() == imageType) {
                    tagEnum = FirebaseImageCategoryTagEnum.ROOF_LAYER;
                }
            } else {
                tagEnum = FirebaseImageCategoryTagEnum.OTHERS;
            }
        }
        if (Objects.equals(typeEnum, ImagePartialTypeEnum.OTHERS)) {
            if (ImageTypeEnum.ZIGZAG.getCode() == imageType) {
                tagEnum = FirebaseImageCategoryTagEnum.ZIGZAG;
            }
        }
        if (tagEnum != null) {
            return tagEnum;
        } else {
            return Arrays.stream(FirebaseImageCategoryTagEnum.values())
                .filter(o -> o.getPartialTypeEnum() == typeEnum)
                .findFirst()
                .orElse(null);
        }
    }

    private static FirebaseImageCategoryTagEnum getImageCategoryTagEnumByOrientation(Integer orientation) {
        if (orientation != null) {
            OrientationEnum orientationEnum = OrientationEnum.getEnum(orientation);

            if (Objects.equals(orientationEnum, OrientationEnum.FRONT)) {
                return FirebaseImageCategoryTagEnum.FRONT_ELEVATION;
            }
            if (Objects.equals(orientationEnum, OrientationEnum.BACK)) {
                return FirebaseImageCategoryTagEnum.BACK_ELEVATION;
            }
            if (Objects.equals(orientationEnum, OrientationEnum.RIGHT)) {
                return FirebaseImageCategoryTagEnum.RIGHT_ELEVATION;
            }
            if (Objects.equals(orientationEnum, OrientationEnum.LEFT)) {
                return FirebaseImageCategoryTagEnum.LEFT_ELEVATION;
            }
        }
        return null;
    }

    public static List<Integer> getImageDictCodesByTagOrientation(FirebaseImageCategoryTagEnum categoryTagEnum, Integer orientation) {
        if (Objects.isNull(categoryTagEnum)) {
            return Collections.emptyList();
        }

        List<Integer> tagCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(categoryTagEnum.getTagCodes())) {
            tagCodes.addAll(categoryTagEnum.getTagCodes());
        }
        if (Objects.nonNull(orientation)) {
            OrientationEnum orientationEnum = OrientationEnum.getEnum(orientation);
            if (Objects.equals(orientationEnum, OrientationEnum.FRONT)) {
                tagCodes.add(ImageTagCodeDict.FRONT);
            }
            if (Objects.equals(orientationEnum, OrientationEnum.BACK)) {
                tagCodes.add(ImageTagCodeDict.REAR);
            }
            if (Objects.equals(orientationEnum, OrientationEnum.RIGHT)) {
                tagCodes.add(ImageTagCodeDict.RIGHT);
            }
            if (Objects.equals(orientationEnum, OrientationEnum.LEFT)) {
                tagCodes.add(ImageTagCodeDict.LEFT);
            }
            if (FirebaseImageCategoryTagEnum.BIRDVIEW == categoryTagEnum ) {
                tagCodes.add(ImageTagCodeDict.OVERVIEW);
            }
        }
        return tagCodes;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public String getDisplay() {
        return display;
    }

    public List<Integer> getTagCodes() {
        return tagCodes;
    }

    public ImagePartialTypeEnum getPartialTypeEnum() {
        return partialTypeEnum;
    }
}
