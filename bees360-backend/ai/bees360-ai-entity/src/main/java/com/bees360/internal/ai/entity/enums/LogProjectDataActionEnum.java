package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/07/30 09:27
 */
public enum LogProjectDataActionEnum implements LogActionType {

    TRANSFERRED(1, "Transferred"),
    DOWNLOADED(2, "Downloaded"),
    DELETED(3, "Deleted"),
    CHANGED(4, "Changed"),
    ;

    private final int code;
    private final String value;

    LogProjectDataActionEnum(int code, String value){
        this.code = code;
        this.value = value;
    }

    public static LogProjectDataActionEnum getEnumByValue(String value) {
        return Stream.of(LogProjectDataActionEnum.values())
            .filter(o -> Objects.equals(value, o.getValue()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogProjectDataActionEnum is null, value:" + value));
    }

    public static List<CodeNameDto> getEnumDict() {
        return Arrays.stream(LogProjectDataActionEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getValue()))
            .collect(Collectors.toList());
    }

    @Override
    public int getCode(){
        return code;
    }

    public String getValue() {
        return value;
    }
}
