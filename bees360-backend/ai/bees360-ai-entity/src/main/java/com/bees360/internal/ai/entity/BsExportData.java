package com.bees360.internal.ai.entity;

import com.bees360.commons.elasticsearchsupport.definition.DataModel;
import com.bees360.commons.elasticsearchsupport.definition.EsDocument;
import lombok.Data;

/**
 * 导出数据存储，根据类型和Id查找对应的data
 * <AUTHOR>
 * @date 2020/05/19 15:08
 */
@Data
@EsDocument(indexName = "project_export_data", idPrefix = "related_id_type")
public class BsExportData implements DataModel<String> {

    /**
     * type 对应存储的id值， eg: 存projectId
     */
    private String relatedId;

    /**
     *  数据所属类型
     */
    private String relatedType;

    /**
     *  json数据
     */
    private String dataLog;

    public BsExportData() {}

    public BsExportData(String relatedId, String relatedType) {
        this.relatedId = relatedId;
        this.relatedType = relatedType;
    }

    @Override
    public String id() {
        return relatedId + "_" + relatedType;
    }
}
