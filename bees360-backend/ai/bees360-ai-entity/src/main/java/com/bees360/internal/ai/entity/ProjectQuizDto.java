package com.bees360.internal.ai.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020/4/10 12:11 PM
 **/
@Data
public class ProjectQuizDto {
    /** used when Quiz type is multiple-choice or date-fill-in-the-blanks **/
    @JsonIgnore
    public static final String ANSWER_SPLITTER = "::";

    @JsonIgnore
    private static final String ANSWER_SPLITTER_DISPLAY = ", ";
    private long quizId;

    private long projectId;

    /** 题干 **/
    private String subject;

    private int claimType;

    private int type;

    /**
     * 如果是题目类型`type`为选择题，此处为备选项
     * 如果题目类型为`type`为日期类型，此处为日期格式
     */
    private String[] choices;

    /** 题目排序号 **/
    private int sequence;

    /** 被问卷者最近一次回答的答案 **/
    private String[] answers;

    /** 被问卷者最近一次回答的答案 **/
    private String answer;

}
