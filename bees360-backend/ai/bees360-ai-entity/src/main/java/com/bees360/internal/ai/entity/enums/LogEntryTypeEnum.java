package com.bees360.internal.ai.entity.enums;

import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/07/25 16:47
 */
public enum LogEntryTypeEnum {

    PROJECT_STATUS_CHANGE("project_status"),
    PROJECT_AI_FLOW("ai_flow"),
    // 记录auto damage detection流程日志
    PROJECT_AD_FLOW("ad_flow"),
    REPORT("report"),
    PROJECT_DATA("project_data"),
    HOVER_PLNAR_STATUS("hover_plnar_status"),
    DAR_PIR_STATUS("dar_pir_status"),
    PROJECT_STATE_CHANGE("project_state")
    ;

    private final String type;

    LogEntryTypeEnum(String type){
        this.type = type;
    }

    public static LogEntryTypeEnum getEnum(String type){
        return Stream.of(LogEntryTypeEnum.values())
            .filter(o -> Objects.equals(type, o.getType()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogEntryTypeEnum is null, value:" + type));
    }

    public String getType() {
        return type;
    }

}
