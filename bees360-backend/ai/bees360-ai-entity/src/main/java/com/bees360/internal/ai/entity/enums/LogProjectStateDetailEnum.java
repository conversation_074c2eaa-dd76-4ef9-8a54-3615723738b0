package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/08/16 14:10
 */
public enum LogProjectStateDetailEnum implements LogDetailType {

    PROJECT_STATE(1, "Project");

    private final int code;
    private final String value;

    LogProjectStateDetailEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static LogProjectStateDetailEnum getEnumByValue(String type) {
        return Stream.of(LogProjectStateDetailEnum.values())
            .filter(o -> Objects.equals(type, o.getValue()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogProjectStateDetailEnum" +
                " is null, type:" + type));
    }

    public static List<CodeNameDto> getEnumDict() {
        return Arrays.stream(LogProjectStateDetailEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getValue()))
            .collect(Collectors.toList());
    }

    @Override
    public int getCode(){
        return code;
    }

    public String getValue() {
        return value;
    }
}
