package com.bees360.internal.ai.entity;

import com.bees360.internal.ai.entity.consts.ImageTagCodeDict;
import com.bees360.internal.ai.entity.enums.DirectionEnum;
import com.bees360.internal.ai.entity.enums.FileSourceTypeEnum;
import com.bees360.internal.ai.entity.enums.ImageTypeEnum;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.bees360.report.entity.ProjectImageTag;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.util.CollectionUtils;

@Slf4j
@ToString
public class ProjectImage implements Cloneable{

    public static final int COMPLETELY_DELETED = -1;

    public static final long DEFAULT_SORT = 0;

	// Identifier of an image.
	private String imageId;
	// The file name in the system for this image. This name is probably used by Amazon cloud sevices.
	private String fileName;
	// The file name for the same image with the middle resolution.
	private String fileNameMiddleResolution;
	// The file name for the same image with the smaller resolution.
	private String fileNameLowerResolution;
	// The url of  lower resolution image with annotations on it.
	private String annotationImage;
	private long fileSize;
	// Who uploaded this image.
	private long userId;
	private long uploadTime;
	// The local filename when this file is uploaded from some local device.
	private String originalFileName;
	// The sources of this file: 0: drone image 1: cell-phone image …
	// it must be assigned a value through the Enum FileSourceTypeEnum.
	private int fileSourceType;
	// The GPS position of this image when it is generated.
	private double gpsLocationLongitude;
	private double gpsLocationLatitude;

	private double relativeAltitude;

	private int imageHeight;
	private int imageWidth;
	// The project that is linked win this project image.
	private long projectId;

    // The direction of this image in a house,
	// it must be assigned a value through the Enum DirectionEnum.
	private int direction;
	// The orientation of this image in a house.
	// it must be assigned a value through the Enum OrientationEnum(LEFT, RIGHT, BACK, FRONT).
	private Integer orientation;
	// it must be assigned a value through the Enum ImageTypeEnum.
	private int imageType;
	private String imageCategory;

	// It is a 3*4 matrix used for 3D construction, i.e.,
	// specifically the coordinate space transformation.
	private String camPropertyMatrix;
	// Whether this image is softly deleted.  -1:Completely deleted
	private boolean deleted;

	private boolean manuallyAnnotated;

	private String parentId;

	private double weight;

	private long shootingTime;

    private Long imageSort;

    private String imageSortV2;

	// 图片信息中的 TIFF/Orientation，默认值为1(Normal)
	private int tiffOrientation = 1;

    // it must be assigned a value through the Enum ImagePartialTypeEnum.
    private int partialType ;

    //图片是否参与3d建模: default value 0: 该图片不需要参与3d建模; 1:参与3d建模; -1: 没有参与建模(被建模算法剔除)
    private int in3DModel;

    // 是否是AI端copy的image，如果是AI端copy的，则此image仅参与3D建模，不参与其它业务处理，也不会被同步到web端。
    // 此字段web端没有
    private boolean isCopy;

    private Set<Integer> tags;

    private Integer objectTag;

    private Integer locationTag;

    private Integer scopeTag;

    /** 前后左右 */
    private Integer directionTag;

    /** 东南西北 */
    private Integer orientationTag;

    private Integer reportTag;

	private Integer floorLevelTag;

	private Integer numberTag;

    private long updateTime;

    /** 标记图片内房屋的指南针朝向 */
    private Double compass;

	private ProjectImageTag categoryImageTag;

	private ProjectImageTag objectImageTag;

	private ProjectImageTag locationImageTag;

	private ProjectImageTag scopeImageTag;

	/** 前后左右 */
	private ProjectImageTag directionImageTag;

	/** 东南西北 */
	private ProjectImageTag orientationImageTag;

	private ProjectImageTag reportImageTag;

	private ProjectImageTag floorLevelImageTag;

	private ProjectImageTag numberImageTag;

    public static int convertBoolToIntForIn3DModel(boolean in3DModel) {
        return in3DModel ? 1 : -1;
    }

    public boolean getBoolForIn3DModel() {
        return this.in3DModel == 1;
    }

    public String getS3Key() {
        return fileName;
    }

    public String getS3MiddleResolutionKey() {
        return fileNameMiddleResolution;
    }
    public String getS3LowerResolutionKey() {
        return fileNameLowerResolution;
    }


	public ProjectImage() {
		super();
	}


	public ProjectImage(String imageId) {
		super();
		this.imageId = imageId;
	}

	public static ProjectImage generateDefaultImage() {
		ProjectImage image = new ProjectImage();
		image.setFileName("");
		image.setFileNameMiddleResolution(null);
		image.setFileNameLowerResolution(null);
		image.setDirection(DirectionEnum.DEFAULT);
		image.setFileSize(0);
		image.setFileSourceType(FileSourceTypeEnum.DEFAULT);
		image.setGpsLocationLatitude(0.0);
		image.setGpsLocationLongitude(0.0);
		image.setImageHeight(0);
		image.setImageWidth(0);
		image.setImageType(ImageTypeEnum.DEFAULT);
		image.setFileSourceType(FileSourceTypeEnum.DEFAULT);
		image.setDeleted(false);
		image.setWeight(0);
		return image;
	}

	@Override
	public int hashCode(){
		return imageId.hashCode();
	}

	@Override
	public boolean equals(Object obj){
		if(this == obj){
			return true;
		}
		if(!(obj instanceof ProjectImage)){
			return false;
		}
		ProjectImage image = (ProjectImage)obj;
		return Objects.equals(imageId, image.getImageId());
	}

	@Override
	public ProjectImage clone(){
		ProjectImage image = null;
		try {
			image = (ProjectImage)super.clone();
		} catch (CloneNotSupportedException e) {
			log.error("Fail to clone ProjectImage.", e);
		}

		return image;
	}

	// getter and setter

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFileNameLowerResolution() {
		return fileNameLowerResolution;
	}

	public void setFileNameLowerResolution(String fileNameLowerResolution) {
		this.fileNameLowerResolution = fileNameLowerResolution;
	}

	public String getFileNameMiddleResolution() {
		return fileNameMiddleResolution;
	}

	public void setFileNameMiddleResolution(String fileNameMiddleResolution) {
		this.fileNameMiddleResolution = fileNameMiddleResolution;
	}

	public String getAnnotationImage() {
		return annotationImage;
	}

	public void setAnnotationImage(String annotationImage) {
		this.annotationImage = annotationImage;
	}

	public long getFileSize() {
		return fileSize;
	}

	public void setFileSize(long fileSize) {
		this.fileSize = fileSize;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public long getUploadTime() {
		return uploadTime;
	}

	public void setUploadTime(long uploadTime) {
		this.uploadTime = uploadTime;
	}

	public String getOriginalFileName() {
		return originalFileName;
	}

	public void setOriginalFileName(String originalFileName) {
		this.originalFileName = originalFileName;
	}

	public int getFileSourceType() {
        // ai前端已去掉有对FileSourceType的维护功能，打了roof tag的都是drone image
        if (isRoof() && FileSourceTypeEnum.PROJECT_TYPES.contains(fileSourceType)) {
            return FileSourceTypeEnum.DRONE_IMAGE.getCode();
        }
		return fileSourceType;
	}

	public void setFileSourceType(int fileSourceType) {
		this.fileSourceType = fileSourceType;
	}

	public double getGpsLocationLongitude() {
		return gpsLocationLongitude;
	}

	public void setGpsLocationLongitude(double gpsLocationLongitude) {
		this.gpsLocationLongitude = gpsLocationLongitude;
	}

	public double getGpsLocationLatitude() {
		return gpsLocationLatitude;
	}

	public void setGpsLocationLatitude(double gpsLocationLatitude) {
		this.gpsLocationLatitude = gpsLocationLatitude;
	}

	public double getRelativeAltitude() {
		return relativeAltitude;
	}

	public void setRelativeAltitude(double relativeAltitude) {
		this.relativeAltitude = relativeAltitude;
	}

	public int getImageHeight() {
		return imageHeight;
	}

	public void setImageHeight(int imageHeight) {
		this.imageHeight = imageHeight;
	}

	public int getImageWidth() {
		return imageWidth;
	}

	public void setImageWidth(int imageWidth) {
		this.imageWidth = imageWidth;
	}

	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public int getDirection() {
		return direction;
	}

	public void setDirection(int direction) {
		this.direction = direction;
	}

	public Integer getOrientation() {
		return orientation;
	}

	public void setOrientation(Integer orientation) {
		this.orientation = orientation;
	}

	public int getImageType() {
		return imageType;
	}

	public void setImageType(int imageType) {
		this.imageType = imageType;
	}

	public String getImageCategory() {
		return imageCategory;
	}

	public void setImageCategory(String imageCategory) {
		this.imageCategory = imageCategory;
	}

	@JsonIgnore
	public String getCamPropertyMatrix() {
		return camPropertyMatrix;
	}

	public void setCamPropertyMatrix(String camPropertyMatrix) {
		this.camPropertyMatrix = camPropertyMatrix;
	}

	public boolean getDeleted() {
		return deleted;
	}

	public void setDeleted(boolean deleted) {
		this.deleted = deleted;
	}

	public boolean isManuallyAnnotated() {
		return manuallyAnnotated;
	}

	public void setManuallyAnnotated(boolean manuallyAnnotated) {
		this.manuallyAnnotated = manuallyAnnotated;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public double getWeight() {
		return weight;
	}

	public void setWeight(double weight) {
		this.weight = weight;
	}

	public int getTiffOrientation() {
		return tiffOrientation;
	}

	public void setTiffOrientation(int tiffOrientation) {
		this.tiffOrientation = tiffOrientation;
	}

    public int getPartialType() {
        return partialType;
    }

    public void setPartialType(int partialType) {
        this.partialType = partialType;
    }

    public long getShootingTime() {
        return shootingTime;
    }

    public void setShootingTime(long shootingTime) {
        this.shootingTime = shootingTime;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public Long getImageSort() {
        return imageSort;
    }

    public void setImageSort(Long imageSort) {
        this.imageSort = imageSort;
    }

    public String getImageSortV2() {
        return imageSortV2;
    }

    public void setImageSortV2(String imageSortV2) {
        this.imageSortV2 = imageSortV2;
    }

    public int getIn3DModel() {
        return in3DModel;
    }

    public void setIn3DModel(int in3DModel) {
        this.in3DModel = in3DModel;
    }

    public boolean isCopy() {
        return isCopy;
    }

    public void setCopy(boolean copy) {
        isCopy = copy;
    }

    public Set<Integer> getTags() {
        return tags;
    }

    public void setTags(Set<Integer> tags) {
        this.tags = tags;
    }

    public void setCategoryTag(Integer categoryTag) {
        if (tags == null) {
            tags = ConcurrentHashMap.newKeySet();
        }
        Optional.ofNullable(categoryTag).ifPresentOrElse(tags::add, tags::clear);
    }

    public Integer getCategoryTag() {
        return CollectionUtils.isEmpty(tags) ? null : tags.stream().findFirst().get();
    }

    public Integer getObjectTag() {
        return objectTag;
    }

    @JsonIgnore
    public Integer getOriginObjectTag() {
        return objectTag;
    }

    public void setObjectTag(Integer objectTag) {
        this.objectTag = objectTag;
    }

    public Integer getLocationTag() {
        return locationTag;
    }

    public void setLocationTag(Integer locationTag) {
        this.locationTag = locationTag;
    }

    public Integer getScopeTag() {
        return scopeTag;
    }

    public void setScopeTag(Integer scopeTag) {
        this.scopeTag = scopeTag;
    }

    public Integer getDirectionTag() {
        return directionTag;
    }

    public void setDirectionTag(Integer directionTag) {
        this.directionTag = directionTag;
    }

    public Integer getOrientationTag() {
        return orientationTag;
    }

    public void setOrientationTag(Integer orientationTag) {
        this.orientationTag = orientationTag;
    }

    public Integer getReportTag() {
        return reportTag;
    }

    public void setReportTag(Integer reportTag) {
        this.reportTag = reportTag;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

	public Integer getFloorLevelTag() {
		return floorLevelTag;
	}

	public void setFloorLevelTag(Integer floorLevelTag) {
		this.floorLevelTag = floorLevelTag;
	}

	public Integer getNumberTag() {
		return numberTag;
	}

	public void setNumberTag(Integer numberTag) {
		this.numberTag = numberTag;
	}

    public Double getCompass() {
        return compass;
    }

    public void setCompass(Double compass) {
        this.compass = compass;
    }

    public boolean isRoof() {
        return Objects.equals(getCategoryTag(), ImageTagCodeDict.ROOF);
    }

	public ProjectImageTag getCategoryImageTag() {
		return categoryImageTag;
	}

	public void setCategoryImageTag(ProjectImageTag categoryImageTag) {
		this.categoryImageTag = categoryImageTag;
	}

	public ProjectImageTag getObjectImageTag() {
		return objectImageTag;
	}

	public void setObjectImageTag(ProjectImageTag objectImageTag) {
		this.objectImageTag = objectImageTag;
	}

	public ProjectImageTag getLocationImageTag() {
		return locationImageTag;
	}

	public void setLocationImageTag(ProjectImageTag locationImageTag) {
		this.locationImageTag = locationImageTag;
	}

	public ProjectImageTag getScopeImageTag() {
		return scopeImageTag;
	}

	public void setScopeImageTag(ProjectImageTag scopeImageTag) {
		this.scopeImageTag = scopeImageTag;
	}

	public ProjectImageTag getDirectionImageTag() {
		return directionImageTag;
	}

	public void setDirectionImageTag(ProjectImageTag directionImageTag) {
		this.directionImageTag = directionImageTag;
	}

	public ProjectImageTag getOrientationImageTag() {
		return orientationImageTag;
	}

	public void setOrientationImageTag(ProjectImageTag orientationImageTag) {
		this.orientationImageTag = orientationImageTag;
	}

	public ProjectImageTag getReportImageTag() {
		return reportImageTag;
	}

	public void setReportImageTag(ProjectImageTag reportImageTag) {
		this.reportImageTag = reportImageTag;
	}

	public ProjectImageTag getFloorLevelImageTag() {
		return floorLevelImageTag;
	}

	public void setFloorLevelImageTag(ProjectImageTag floorLevelImageTag) {
		this.floorLevelImageTag = floorLevelImageTag;
	}

	public ProjectImageTag getNumberImageTag() {
		return numberImageTag;
	}

	public void setNumberImageTag(ProjectImageTag numberImageTag) {
		this.numberImageTag = numberImageTag;
	}
}
