package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/07/30 09:27
 */
public enum LogAiFlowDetailEnum implements LogDetailType {

    THREE_D_GENERATION(1, "3D Generation", AiProcessStageE.MODELING),
    PRE_RANGING(2, "Pre-Ranging", AiProcessStageE.RANGING),
    RANGING(3, "Ranging", null),
    PRE_SCOPING(4, "Pre-Scoping", AiProcessStageE.SCOPING),
    SCOPING(5, "Scoping", null),
    PRE_PLANE(6, "Pre-Plane", AiProcessStageE.PLANE),
    PLANE(7, "Plane", null),
    PRE_BOUNDARY(8, "Pre-Boundary", AiProcessStageE.BOUNDARY),
    BOUNDARY(9, "Boundary", null),

    POST_BOUNDARY(10, "Post-Boundary", AiProcessStageE.CONFIRM);

    private final int code;
    private final String value;
    private final AiProcessStageE aiStage;

    LogAiFlowDetailEnum(int code, String value, AiProcessStageE aiStage){
        this.code = code;
        this.value = value;
        this.aiStage = aiStage;
    }

    public static LogAiFlowDetailEnum getEnumByValue(String value) {
        return Stream.of(LogAiFlowDetailEnum.values())
            .filter(o -> Objects.equals(value, o.getValue()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogAiFlowDetailEnum is null, value:" + value));
    }

    public static List<CodeNameDto> getEnumDict() {
        return Arrays.stream(LogAiFlowDetailEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getValue()))
            .collect(Collectors.toList());
    }

    @Override
    public int getCode(){
        return code;
    }

    public String getValue() {
        return value;
    }

    public AiProcessStageE getAiStage() {
        return aiStage;
    }
}
