package com.bees360.internal.ai.entity.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/5/27
 */
@Data
public class EmailRecipients implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private boolean enabled;

    private List<String> claimIncompleteStatistics;

    private List<String> claimCompanyCal;

    /** adjuster工作量统计收件人 */
    private List<String> adjusterWorkloadStatistics;

    /** adjuster周工作量统计收件人, 可能为空 */
    private List<String> weeklyAdjusterWorkloadStatistics;

    /** adjuster月工作量统计收件人, 可能为空 */
    private List<String> monthlyAdjusterWorkloadStatistics;

    /** 默认的processor工作量统计收件人,可能会被其它配置给覆盖 */
    private List<String> processorWorkloadStatistics;

    /** 默认的processor工作量统计特殊中国员工收件人,可能会被其它配置给覆盖 */
    private List<String> processorWorkloadStatisticsSpecialChineseStaffs;

    /** processor周工作量统计收件人, 可能为空 */
    private List<String> weeklyProcessorWorkloadStatistics;

    /** processor月工作量统计收件人, 可能为空 */
    private List<String> monthlyProcessorWorkloadStatistics;

    /** 默认的reviewer工作量统计收件人,可能会被其它配置给覆盖 */
    private List<String> reviewerWorkloadStatistics;

    /** reviewer周工作量统计收件人, 可能为空 */
    private List<String> weeklyReviewerWorkloadStatistics;

    /** reviewer月工作量统计收件人, 可能为空 */
    private List<String> monthlyReviewerWorkloadStatistics;

    /** 不需要统计的Adjuster */
    private List<String> adjusterExclude = new ArrayList<>();
}
