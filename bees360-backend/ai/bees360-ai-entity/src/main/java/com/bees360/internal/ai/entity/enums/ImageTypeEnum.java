package com.bees360.internal.ai.entity.enums;

import java.util.HashMap;
import java.util.Map;

public enum ImageTypeEnum implements BaseCodeEnum{
	NOT_EDITABLE(-1, "Uneditable", ViewType.OTHER),
	<PERSON><PERSON><PERSON>(0, "Other", ViewType.OTHER),
	NEIGHBOR(1, "Neighbor", ViewType.DRONE_VIEW),
	OVERVIEW(2, "Overview", ViewType.DRONE_VIEW),
	BIRDVIEW(3, "Birdview", ViewType.DRONE_VIEW),
	CLOSEUP(4, "Closeup", ViewType.DRONE_VIEW),
	ADDRESS(5, "Address", ViewType.ADDRESS),
	<PERSON><PERSON>VA<PERSON><PERSON>(6, "Elevation", ViewType.ELEVATION),
	CELLPHONE_ROOF(7, "Cellphone Roof", ViewType.ELEVATION),

	ZIG<PERSON>AG(10, "Zigzag", ViewType.DRONE_VIEW),
	ROOF_LAYER(11, "Roof Layer", ViewType.DRONE_VIEW),


	BEES_GO_FRONT_ELEVATION(10, "Front Elevation", ViewType.ELEVATION),
	BEES_GO_RIGHT_ELEVATION(30, "Right Elevation", ViewType.ELEVATION),
	BEES_GO_BACK_ELEVATION(50, "Back Elevation", ViewType.ELEVATION),
	BEES_GO_LEFT_ELEVATION(70, "Left Elevation", ViewType.ELEVATION),
	BEES_GO_INTERIOR(95, "Interior", ViewType.ELEVATION),
	BEES_GO_GARAGE(185, "Garage", ViewType.ELEVATION),
	BEES_GO_APS(205, "APS", ViewType.ELEVATION),
	BEES_GO_OTHERS(220, "Others", ViewType.ELEVATION)
	;

	public static enum ViewType{
		DRONE_VIEW,
		ADDRESS,
		ELEVATION,
		OTHER
		;
	}

	public static final int DEFAULT = -2;

	private final int code;
	private final String display;
	private final ViewType viewType;

	private final static Map<Integer, ImageTypeEnum> typeMap = new HashMap<>();

	static {
		for(ImageTypeEnum type: ImageTypeEnum.values()) {
			typeMap.put(type.getCode(), type);
		}
	}

	ImageTypeEnum(int code, String display, ViewType viewType){
		this.code = code;
		this.display = display;
		this.viewType = viewType;
	}
	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public ViewType getViewType() {
		return viewType;
	}

	public static ImageTypeEnum getEnum(int claimTypeCode){
		return typeMap.get(claimTypeCode);
	}
}
