package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/3/22 8:05 PM
 **/
public enum ClaimTypeEnum implements BaseCodeEnum{
    HAIL(0, "Hail"),
    WIND(1, "Wind"),
    HAIL_WIND(2, "Hail & Wind"),
    FIRE(3, "Fire"),
    FLOOD(4, "Flood"),
    HURRICANE(9, "Hurricane"),
    UNDERWRITING_FIRST_INSPECTION(5, "Underwriting/First Inspection"),
    OTHERS(6, "Others")
    ;

    private final int code;
    private final String display;

    ClaimTypeEnum(int code, String display){
        this.code = code;
        this.display = display;
    }

    public static boolean isClaim(int claimType) {
        return UNDERWRITING_FIRST_INSPECTION.getCode() != claimType;
    }


    public static List<Integer> getTypeCode(Boolean isClaim) {
        if (Objects.isNull(isClaim)) {
            isClaim = false;
        }
        if (isClaim) {
            return getClaimCodes();
        }
        return Arrays.asList(UNDERWRITING_FIRST_INSPECTION.getCode());
    }

    private static List<Integer> getClaimCodes(){
        return Arrays.stream(ClaimTypeEnum.values()).filter(o -> o.getCode() != UNDERWRITING_FIRST_INSPECTION.getCode())
            .map(ClaimTypeEnum::getCode).collect(Collectors.toList());
    }

    public static List<CodeNameDto> getDict() {
        return Arrays.stream(ClaimTypeEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getDisplay())).collect(Collectors.toList());
    }

    @Override
    public String getDisplay(){
        return display;
    }

    @Override
    public int getCode() {
        return code;
    }

    public static ClaimTypeEnum getEnum(Integer claimTypeCode){
        return claimTypeCode == null? null: getEnum(claimTypeCode.intValue());
    }

    public static ClaimTypeEnum getEnum(int claimTypeCode){
        ClaimTypeEnum[] types = ClaimTypeEnum.values();
        for(ClaimTypeEnum type: types){
            if(type.getCode() == claimTypeCode){
                return type;
            }
        }
        return null;
    }

    public static boolean exist(Integer code) {
        return code != null && getEnum(code) != null;
    }
}
