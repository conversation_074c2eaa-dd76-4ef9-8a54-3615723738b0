package com.bees360.internal.ai.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/04/23 11:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectListResult {

    private String statusTag;

    private List<ProjectEsModel> projectList;

    private int projectCount;

    public static class StatusTag {
        public static final String TODO = "TODO";
        public static final String ACTIVE = "ACTIVE";
        public static final String CLOSED = "CLOSED";
        public static final String URGENT = "URGENT";

        /**
         * {@link com.bees360.internal.ai.entity.enums.ReportGenerationStatusEnum.SUBMITTED}
         *
         * <p>1、dar处于SUBMITTED状态
         *
         * <p>2、dar是APPROVED、DISAPPROVED、UPLOADED,PIR 不是APPROVED和UPLOADED状态</p>
         */
        public static final String DAR = "DAR";

        public static final String REVIEW = "REVIEW";

        public static final String ARCHIVED = "ARCHIVED";

        public static final String REWORK = "REWORK";
    }

    public ProjectListResult(String statusTag) {
        this(statusTag, new ArrayList<>());
    }

    public ProjectListResult(String statusTag, List<ProjectEsModel> projectList) {
        this.statusTag = statusTag;
        this.projectList = projectList;
        this.projectCount = projectList.size();
    }

    public static ProjectListResult empty(String statusTag) {
        return new ProjectListResult(statusTag);
    }
}
