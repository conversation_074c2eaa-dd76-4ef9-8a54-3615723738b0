package com.bees360.internal.ai.entity.enums;

import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2020/07/25 16:47
 */
public enum ProjectUserFolderTypeEnum {

    URGENT("URGENT")
    ;

    private final String type;

    ProjectUserFolderTypeEnum(String type){
        this.type = type;
    }

    public static ProjectUserFolderTypeEnum getEnum(String type){
        return Stream.of(ProjectUserFolderTypeEnum.values())
            .filter(o -> StringUtils.equals(type, o.getType()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getProjectUserFolderTypeEnum is null, value:" + type));
    }

    public String getType() {
        return type;
    }
}
