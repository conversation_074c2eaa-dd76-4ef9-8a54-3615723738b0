package com.bees360.internal.ai.entity;

import com.bees360.commons.elasticsearchsupport.definition.DataModel;
import com.bees360.commons.elasticsearchsupport.definition.EsDocument;
import com.bees360.internal.ai.entity.enums.ProjectUserFolderTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/07/25 14:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EsDocument(indexName = "project_folder", idPrefix = "project_id")
public class ProjectFolder implements DataModel<String> {

    private Long projectId;

    /**
     * @see ProjectUserFolderTypeEnum
     */
    private String type;

    private String userId;

    private long createdTime;

    @Override
    public String id() {
        return getUniqueId(projectId, type);
    }

    public ProjectFolder(Long projectId, String type, String userId) {
        this.projectId = projectId;
        this.type = type;
        this.userId = userId;
        this.createdTime = System.currentTimeMillis();
    }

    public static String getUniqueId(long projectId, String type) {
        return type + "_" + projectId;
    }

}
