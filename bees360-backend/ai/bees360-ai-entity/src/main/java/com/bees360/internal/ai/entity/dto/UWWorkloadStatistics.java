package com.bees360.internal.ai.entity.dto;

import lombok.Data;

import java.util.concurrent.atomic.AtomicInteger;

@Data
public class UWWorkloadStatistics {
    private String staffId;
    private String staffName;
    private Long insuredCompanyId;
    private String insuredCompanyName;
    private AtomicInteger roofOnly = new AtomicInteger(0);
    private AtomicInteger exterior = new AtomicInteger(0);
    private AtomicInteger fourPoint = new AtomicInteger(0);
    private AtomicInteger premiumFourPoint = new AtomicInteger(0);
    private AtomicInteger total = new AtomicInteger(0);

}
