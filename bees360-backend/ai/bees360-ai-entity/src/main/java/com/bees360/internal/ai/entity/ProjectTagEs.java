package com.bees360.internal.ai.entity;

import com.bees360.event.registry.ProjectTagChangedObject;
import com.bees360.project.tag.Message;
import com.bees360.project.tag.ProjectTag;
import lombok.Builder;
import lombok.Data;

import jakarta.annotation.Nullable;
import java.time.Instant;

@Data
@Builder
public class ProjectTagEs implements ProjectTag {

    private String id;
    private String title;
    private String description;
    private String color;
    private String icon;
    private String companyId;
    private Message.ProjectTagType type;

    public static ProjectTagEs eventToES(ProjectTagChangedObject e) {
        return ProjectTagEs.builder()
                .id(e.getTagId())
                .title(e.getTitle())
                .color(e.getColor())
                .companyId(e.getCompanyId())
                .description(e.getDescription())
                .icon(e.getIcon())
                .type(com.bees360.project.tag.Message.ProjectTagType.forNumber(e.getTagType()))
                .build();
    }

    @Override
    public String getTitle() {
        return title;
    }

    @Nullable
    @Override
    public String getDescription() {
        return description;
    }

    @Nullable
    @Override
    public String getColor() {
        return color;
    }

    @Nullable
    @Override
    public String getIcon() {
        return icon;
    }

    @Nullable
    @Override
    public String getCompanyId() {
        return companyId;
    }

    @Nullable
    @Override
    public Message.ProjectTagType getType() {
        return type;
    }

    @Nullable
    @Override
    public Instant getAddedAt() {
        return null;
    }

    @Nullable
    @Override
    public String getAddedBy() {
        return null;
    }

    @Nullable
    @Override
    public String getId() {
        return id;
    }
}
