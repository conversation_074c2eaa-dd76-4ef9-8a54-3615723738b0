package com.bees360.internal.ai.entity.enums;

import java.util.Objects;

public enum OrientationEnum {
	FRONT(1, "Front", "F"),
	BACK(2, "Rear" , "B"),
	LEFT(3, "Left", "L"),
	RIGHT(4, "Right", "R");

	private final int code;
	private final String display;
	private final String shortCut;

    OrientationEnum(int code, String display, String shortCut) {
        this.code = code;
        this.display = display;
        this.shortCut = shortCut;
    }

    public int getCode() {
		return code;
	}

	public String getDisplay() {
		return display;
	}

    public String getShortCut() {
        return shortCut;
    }

    public static boolean contains(Integer code){
        if (Objects.isNull(code)) {
            return false;
        }
        return Objects.nonNull(getEnum(code));
    }

    public static OrientationEnum getEnum(int code){
		OrientationEnum[] orientations = OrientationEnum.values();
		for(OrientationEnum orientation: orientations){
			if(orientation.code == code){
				return orientation;
			}
		}
		return null;
	}
}
