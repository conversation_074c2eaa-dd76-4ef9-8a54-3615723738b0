package com.bees360.internal.ai.entity.enums;

import com.bees360.user.Message;
import com.bees360.user.User;
import com.bees360.user.util.AbstractUser;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Nullable;
import java.net.URL;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/08/05 16:19
 */
public enum AiBotUserEnum {

    AI_NEW_USER_ID("10000", "BeesAI"),
    WEB_NEW_USER_ID("20000", "BeesWeb"),
    UN_KNOWN_USER("30000", "Unknown User")
    ;

    private final String code;
    private final String display;


    AiBotUserEnum(String code, String display){
        this.code = code;
        this.display = display;
    }

    public String getCode() {
        return code;
    }

    public String getDisplay(){
        return display;
    }

    public static AiBotUserEnum getEnum(String code) {
        return Stream.of(AiBotUserEnum.values())
            .filter(o -> Objects.equals(code, o.getCode()))
            .findFirst()
            .orElse(null);

    }

    public static final String AI_BOT_GROUP = "Bot";

    public static String getName(String code) {
        return Optional.ofNullable(getEnum(code)).map(AiBotUserEnum::getDisplay).orElse(null);
    }

    public static boolean isBotUser(String id) {
        return Stream.of(AiBotUserEnum.values())
            .anyMatch(o -> StringUtils.equals(id, o.getCode()));
    }

    public static User reloadUserByBot(AiBotUserEnum botUserEnum) {
        if (Objects.isNull(botUserEnum)) {
            return null;
        }
        return new AbstractUser() {
            @Override
            public String getId() {
                return botUserEnum.getCode();
            }

            @Nullable
            @Override
            public String getEmail() {
                return null;
            }

            @Nullable
            @Override
            public String getPhone() {
                return null;
            }

            @Nullable
            @Override
            public URL getPhoto() {
                return null;
            }

            @Override
            public Set<String> getAllAuthority() {
                return Sets.newHashSet();
            }

            @Nullable
            @Override
            public String getName() {
                return botUserEnum.getDisplay();
            }

            @Override
            public Message.UserMessage toMessage() {
                return null;
            }
        };
    }
}
