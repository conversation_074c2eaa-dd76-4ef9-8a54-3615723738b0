package com.bees360.internal.ai.entity.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Set;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum FileSourceTypeEnum implements BaseCodeEnum{

	DRONE_IMAGE(0, "Full Damage Image"),
	CELL_PHONE_IMAGE(1, "Cell Phone Image"),
	SCREENSHOT(2, "Screenshot"),
	PLACEHOLDER(3, "Placeholder"),
	DRONE_PREVISIT_IMAGE(4, "Pre-visit Image"),
	DRONE_REALTIME_IMAGE(5, "Realtime Image"),
    /**
     * 报告中生成/使用到的图片
     */
    REPORT_IMAGE(6, "Annotation Image")
	;

    public static final int DEFAULT = -1;

    // 将来去掉file source type的时候，drone image和mobile image会移到project模块的project image中
    public static final Set<Integer> PROJECT_TYPES =
            Set.of(DRONE_IMAGE.getCode(), CELL_PHONE_IMAGE.getCode());

	FileSourceTypeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	private final int code;
	private final String display;

	@Override
	@JsonValue
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public static FileSourceTypeEnum getEnum(int type) {
		return valueOf(type);
	}

	public static FileSourceTypeEnum valueOf(int type){
		FileSourceTypeEnum[] types = FileSourceTypeEnum.values();
		for(FileSourceTypeEnum typeEnum: types){
			if(typeEnum.getCode() == type){
				return typeEnum;
			}
		}
		return null;
	}
}
