package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.ProcessStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.ArrayList;
import java.util.List;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ProjectStatusEnum implements BaseCodeEnum{

	DELETE(0, ProcessStatusEnum.DELETED, "Deleted"),

	// the project was created
	NEW_PROJECT(1, ProcessStatusEnum.NEW, "New Project"),

	JOB_RECEIVED(2, null, "Job Received"),

	PILOT_SCHEDULED(20, null, "Pilot Scheduled"),
	PILOT_CANCELLED(21, null, "Pilot Cancelled"),
	// The first time a policy holder is contacted
	POLICY_HOLDER_CONTACTED(22, null, "Policy Holder Contacted"),
	PILOT_CHECKED_IN(23, null, "Pilot Checked In"),
	PILOT_CHECKED_OUT(24, null, "Pilot Checked Out"),
	// The real time that the pilot do the inspection
	PILOT_INSPECTED(25, null, "Pilot Inspected"),
	// pilot uploaded all images, and pilot finished his job.
	IMAGE_UPLOADED(26, ProcessStatusEnum.IMAGE_UPLOADED, "Image Uploaded"),

	ADJUSTER_CLAIM_CHECKED_IN(27, null, "Adjuster Claim Checked In"),
	ADJUSTER_CLAIM_CHECKED_OUT(28, null, "Adjuster Claim Checked Out"),

	THREE_D_GENERATION_START(29, ProcessStatusEnum.IN_PROCESS, "3D Generation started"),
	THREE_D_GENERATION_FINISHED(30, ProcessStatusEnum.IN_PROCESS, "3D Generation Finished"),
	PRE_RANGING_FINISHED(31, ProcessStatusEnum.IN_PROCESS, "Pre-Ranging Finished"),
	RANGING_FINISHED(32, ProcessStatusEnum.IN_PROCESS, "Ranging Finished"),
	PRE_SCOPING_FINISHED(33, ProcessStatusEnum.IN_PROCESS, "Pre-Scoping Finished"),
	SCOPING_FINISHED(34, ProcessStatusEnum.IN_PROCESS, "Scoping Finished"),
	PRE_PLANE_FINISHED(35, ProcessStatusEnum.IN_PROCESS, "Pre-Plane Finished"),
	PLANE_FINISHED(36, ProcessStatusEnum.IN_PROCESS, "Plane Finished"),
	PRE_BOUNDARY_FINISHED(37, ProcessStatusEnum.IN_PROCESS, "Pre-Boundary Finished"),
	BOUNDARY_FINISHED(38, ProcessStatusEnum.IN_PROCESS, "Boundary Finished"),
	POST_BOUNDARY_FINISHED(39, ProcessStatusEnum.IN_PROCESS, "Post-Boundary Finished"),

	DAMAGE_ANNOTATED(40, null, "Damage Annotated"),

    ADJUSTER_UNDERWRITING_CHECKED_IN(41, null, "Underwriting Inspector Checked In"),
    ADJUSTER_UNDERWRITING_CHECKED_OUT(42, null, "Underwriting Inspector Checked Out"),

    ADJUSTER_SCHEDULED(50, null, "Adjuster Scheduled"),
	ADJUSTER_CANCELLED(51, null, "Adjuster Cancelled"),
	ADJUSTER_FINISHED(52, null, "Adjuster Finished"),

	AD_STARTED(54, ProcessStatusEnum.IN_PROCESS, "Auto Detection Started"),
	AD_GENERATED(55, ProcessStatusEnum.IN_PROCESS, "Auto Detection Generated"),
	REALTIME_AD_STARTED(56, ProcessStatusEnum.IN_PROCESS, "Auto Detection Started"),
	REALTIME_AD_GENERATED(57, ProcessStatusEnum.IN_PROCESS, "Auto Detection Generated"),

	// Damage Report
	DAMAGE_REPORT_SUBMITTED(110, ProcessStatusEnum.UNDER_REVIEW, "Premium Damage Assessment Report Submitted"),
	DAMAGE_REPORT_GENERATED(62, ProcessStatusEnum.IN_PROCESS, "Premium Damage Assessment Report Generated"),
	DAMAGE_REPORT_APPROVED(111, ProcessStatusEnum.REPORT_APPROVED, "Premium Damage Assessment Report Approved"),
	DAMAGE_REPORT_DISAPPROVED(112, ProcessStatusEnum.IN_PROCESS, "Premium Damage Assessment Report Disapproved"),

	// Measurement Report
	MEASUREMENT_REPORT_SUBMITTED(121, ProcessStatusEnum.UNDER_REVIEW, "Premium Measurement Report Submitted"),
	MEASUREMENT_REPORT_GENERATED(60, ProcessStatusEnum.IN_PROCESS, "Premium Measurement Report Generated"),
	MEASUREMENT_REPORT_APPROVED(122, ProcessStatusEnum.REPORT_APPROVED, "Premium Measurement Report Approved"),
	MEASUREMENT_REPORT_DISAPPROVED(123, ProcessStatusEnum.IN_PROCESS, "Premium Measurement Report Disapproved"),

	// Image Report
	IMAGE_REPORT_SUBMITTED(124, ProcessStatusEnum.UNDER_REVIEW, "Premium Image Assessment Report Submitted"),
	IMAGE_REPORT_GENERATED(61, ProcessStatusEnum.IN_PROCESS, "Premium Image Assessment Report Generated"),
	IMAGE_REPORT_APPROVED(125, ProcessStatusEnum.REPORT_APPROVED, "Premium Image Assessment Report Approved"),
	IMAGE_REPORT_DISAPPROVED(126, ProcessStatusEnum.IN_PROCESS, "Premium Image Assessment Report Disapproved"),

	// Onsite Damage Report
	ONSITE_DAMAGE_REPORT_SUBMITTED(127, ProcessStatusEnum.UNDER_REVIEW, "Preliminary Damage Assessment Report Submitted"),
	ONSITE_DAMAGE_REPORT_GENERATED(67, ProcessStatusEnum.IN_PROCESS, "Preliminary Damage Assessment Report Generated"),
	ONSITE_DAMAGE_REPORT_APPROVED(128, ProcessStatusEnum.REPORT_APPROVED, "Preliminary Damage Assessment Report Approved"),
	ONSITE_DAMAGE_REPORT_DISAPPROVED(129, ProcessStatusEnum.IN_PROCESS, "Preliminary Damage Assessment Report Disapproved"),

	// Onsite Image Report
	ONSITE_IMAGE_REPORT_SUBMITTED(130, ProcessStatusEnum.UNDER_REVIEW, "Preliminary Image Assessment Report Report Submitted"),
	ONSITE_IMAGE_REPORT_GENERATED(68, ProcessStatusEnum.IN_PROCESS, "Preliminary Image Assessment Report Generated"),
	ONSITE_IMAGE_REPORT_APPROVED(131, ProcessStatusEnum.REPORT_APPROVED, "Preliminary Image Assessment Report Approved"),
	ONSITE_IMAGE_REPORT_DISAPPROVED(132, ProcessStatusEnum.IN_PROCESS, "Preliminary Image Assessment Report Disapproved"),

	// Damage Assessment Report
	DAMAGE_ASSESSMENT_REPORT_SUBMITTED(133, ProcessStatusEnum.UNDER_REVIEW, "Highfly Evaluation Report Submitted"),
	DAMAGE_ASSESSMENT_REPORT_GENERATED(100, ProcessStatusEnum.IN_PROCESS, "Highfly Evaluation Report Generated"),
	DAMAGE_ASSESSMENT_REPORT_APPROVED(134, ProcessStatusEnum.REPORT_APPROVED, "Highfly Evaluation Report Approved"),
	DAMAGE_ASSESSMENT_REPORT_DISAPPROVED(135, ProcessStatusEnum.IN_PROCESS, "Highfly Evaluation Report Disapproved"),

	// App Damage Report
	APP_DAMAGE_REPORT_SUBMITTED(150, ProcessStatusEnum.UNDER_REVIEW, "Property Image Report Submitted"),
	APP_DAMAGE_REPORT_GENERATED(151, ProcessStatusEnum.IN_PROCESS, "Property Image Report Generated"),
	APP_DAMAGE_REPORT_APPROVED(152, ProcessStatusEnum.REPORT_APPROVED, "Property Image Report Approved"),
	APP_DAMAGE_REPORT_DISAPPROVED(153, ProcessStatusEnum.IN_PROCESS, "Property Image Report Disapproved"),

	// Symbility XML
	SYMBILITY_XML_REPORT_GENERATED(155, null, "Symbility XML Report Generated"),

	WEATHER_REPORT_SUBMITTED(136, ProcessStatusEnum.UNDER_REVIEW, "Weather Report Submitted"),
	WEATHER_REPORT_GENERATED(63, ProcessStatusEnum.IN_PROCESS, "Weather Report Generated"),
	WEATHER_REPORT_APPROVED(137, ProcessStatusEnum.REPORT_APPROVED, "Weather Report Approved"),
	WEATHER_REPORT_DISAPPROVED(138, ProcessStatusEnum.IN_PROCESS, "Weather Report Disapproved"),

	ESTIMATE_REPORT_SUBMITTED(139, ProcessStatusEnum.UNDER_REVIEW, "Estimate Report Submitted"),
    ESTIMATE_REPORT_GENERATED(64, ProcessStatusEnum.IN_PROCESS, "Estimate Report Generated"),
    ESTIMATE_REPORT_APPROVED(140, ProcessStatusEnum.REPORT_APPROVED, "Estimate Report Approved"),
    ESTIMATE_REPORT_DISAPPROVED(141, ProcessStatusEnum.IN_PROCESS, "Estimate Report Disapproved"),

	DXF_REPORT_SUBMITTED(142, ProcessStatusEnum.UNDER_REVIEW, "Measurement DXF Report Submitted"),
	DXF_REPORT_GENERATED(143, ProcessStatusEnum.IN_PROCESS, "Measurement DXF Report Generated"),
	DXF_REPORT_APPROVED(144, ProcessStatusEnum.REPORT_APPROVED, "Measurement DXF Report Approved"),
	DXF_REPORT_DISAPPROVED(145, ProcessStatusEnum.IN_PROCESS, "Measurement DXF Report Disapproved"),

	BIDDING_SUBMITTED(147, ProcessStatusEnum.UNDER_REVIEW, "On-Site Bidding Report Submitted"),
	BIDDING_GENERATED(148, ProcessStatusEnum.IN_PROCESS, "On-Site Bidding Report Generated"),
	BIDDING_APPROVED(149, ProcessStatusEnum.REPORT_APPROVED, "On-Site Bidding Report Approved"),
	BIDDING_DISAPPROVED(167, ProcessStatusEnum.IN_PROCESS, "On-Site Bidding Report Disapproved"),

	QUICK_SQUARE_SUBMITTED(159, ProcessStatusEnum.UNDER_REVIEW, "Real-time Quick Square Report Submitted"),
	QUICK_SQUARE_GENERATED(160, ProcessStatusEnum.IN_PROCESS, "Real-time Quick Square Report Generated"),
	QUICK_SQUARE_APPROVED(165, ProcessStatusEnum.REPORT_APPROVED, "Real-time Quick Square Report Approved"),
	QUICK_SQUARE_DISAPPROVED(166, ProcessStatusEnum.IN_PROCESS, "Real-time Quick Square Report Disapproved"),

	// Realtime Damage Report
	REALTIME_APP_DAMAGE_REPORT_SUBMITTED(161, ProcessStatusEnum.UNDER_REVIEW, "Real-time Damage Assessment Report Submitted"),
	REALTIME_APP_DAMAGE_REPORT_GENERATED(162, ProcessStatusEnum.IN_PROCESS, "Real-time Damage Assessment Report Generated"),
	REALTIME_APP_DAMAGE_REPORT_APPROVED(163, ProcessStatusEnum.REPORT_APPROVED, "Real-time Damage Assessment Report Approved"),
	REALTIME_APP_DAMAGE_REPORT_DISAPPROVED(164, ProcessStatusEnum.IN_PROCESS, "Real-time Damage Assessment Report Disapproved"),

	INFRARED_DAMAGE_REPORT_SUBMITTED(168, ProcessStatusEnum.UNDER_REVIEW, "Infrared Damage Assessment Report Submitted"),
	INFRARED_DAMAGE_REPORT_GENERATED(169, ProcessStatusEnum.IN_PROCESS, "Infrared Damage Assessment Report Generated"),
	INFRARED_DAMAGE_REPORT_APPROVED(170, ProcessStatusEnum.REPORT_APPROVED, "Infrared Damage Assessment Report Approved"),
	INFRARED_DAMAGE_REPORT_DISAPPROVED(171, ProcessStatusEnum.IN_PROCESS, "Infrared Damage Assessment Report Disapproved"),

	ROOF_ONLY_UNDERWRITING_REPORT_SUBMITTED(172, ProcessStatusEnum.UNDER_REVIEW, "Roof-only Underwriting Report Submitted"),
	ROOF_ONLY_UNDERWRITING_REPORT_GENERATED(173, ProcessStatusEnum.IN_PROCESS, "Roof-only Underwriting Report Generated"),
	ROOF_ONLY_UNDERWRITING_REPORT_APPROVED(174, ProcessStatusEnum.REPORT_APPROVED, "Roof-only Underwriting Report Approved"),
	ROOF_ONLY_UNDERWRITING_REPORT_DISAPPROVED(175, ProcessStatusEnum.IN_PROCESS, "Roof-only Underwriting Report Disapproved"),

	FULL_SCOPE_UNDERWRITING_REPORT_SUBMITTED(176, ProcessStatusEnum.UNDER_REVIEW, "Full-scope Underwriting Report Submitted"),
	FULL_SCOPE_UNDERWRITING_REPORT_GENERATED(177, ProcessStatusEnum.IN_PROCESS, "Full-scope Underwriting Report Generated"),
	FULL_SCOPE_UNDERWRITING_REPORT_APPROVED(178, ProcessStatusEnum.REPORT_APPROVED, "Full-scope Underwriting Report Approved"),
	FULL_SCOPE_UNDERWRITING_REPORT_DISAPPROVED(179, ProcessStatusEnum.IN_PROCESS, "Full-scope Underwriting Report Disapproved"),

    LETTER_OF_CANCELLATION_REPORT_SUBMITTED(180, ProcessStatusEnum.UNDER_REVIEW, "Letter Of Cancellation Report Submitted"),
    LETTER_OF_CANCELLATION_REPORT_GENERATED(181, ProcessStatusEnum.IN_PROCESS, "Letter Of Cancellation Report Generated"),
    LETTER_OF_CANCELLATION_REPORT_APPROVED(182, ProcessStatusEnum.REPORT_APPROVED, "Letter Of Cancellation Report Approved"),
    LETTER_OF_CANCELLATION_REPORT_DISAPPROVED(183, ProcessStatusEnum.IN_PROCESS, "Letter Of Cancellation Report Disapproved"),

    CLAIM_DAMAGE_FORM_SUBMITTED(184, ProcessStatusEnum.UNDER_REVIEW, "Claim Damage Form Submitted"),
    CLAIM_DAMAGE_FORM_GENERATED(185, ProcessStatusEnum.IN_PROCESS, "Claim Damage FormGenerated"),
    CLAIM_DAMAGE_FORM_APPROVED(186, ProcessStatusEnum.REPORT_APPROVED, "Claim Damage Form Approved"),
    CLAIM_DAMAGE_FORM_DISAPPROVED(187, ProcessStatusEnum.IN_PROCESS, "Claim Damage Form Disapproved"),

    POST_CONSTRUCTION_AUDIT_REPORT_SUBMITTED(191, ProcessStatusEnum.UNDER_REVIEW, "Post-Construction Audit Report Submitted"),
    POST_CONSTRUCTION_AUDIT_REPORT_GENERATED(192, ProcessStatusEnum.IN_PROCESS, "Post-Construction Audit Report Generated"),
    POST_CONSTRUCTION_AUDIT_REPORT_APPROVED(193, ProcessStatusEnum.REPORT_APPROVED, "Post-Construction Audit Report Approved"),
    POST_CONSTRUCTION_AUDIT_REPORT_DISAPPROVED(194, ProcessStatusEnum.IN_PROCESS, "Post-Construction Audit Report Disapproved"),

    HOMEOWNER_SURVEY_REPORT_SUBMITTED(210, ProcessStatusEnum.UNDER_REVIEW, "Homeowner Inspection Survey Results Report Submitted"),
    HOMEOWNER_SURVEY_REPORT_GENERATED(211, ProcessStatusEnum.IN_PROCESS, "Homeowner Inspection Survey Results Report Generated"),
    HOMEOWNER_SURVEY_REPORT_APPROVED(212, ProcessStatusEnum.REPORT_APPROVED, "Homeowner Inspection Survey Results Report Approved"),
    HOMEOWNER_SURVEY_REPORT_DISAPPROVED(213, ProcessStatusEnum.IN_PROCESS, "Homeowner Inspection Survey Results Report Disapproved"),

    EUR_REPORT_SUBMITTED(214, ProcessStatusEnum.UNDER_REVIEW, "Express Underwriting Report Submitted"),
    EUR_REPORT_GENERATED(215, ProcessStatusEnum.IN_PROCESS, "Express Underwriting Report Generated"),
    EUR_REPORT_APPROVED(216, ProcessStatusEnum.REPORT_APPROVED, "Express Underwriting Report Approved"),
    EUR_REPORT_DISAPPROVED(217, ProcessStatusEnum.IN_PROCESS, "Express Underwriting Report Disapproved"),

    CHR_REPORT_SUBMITTED(218, ProcessStatusEnum.UNDER_REVIEW, "Custom Home Report Submitted"),
    CHR_REPORT_GENERATED(219, ProcessStatusEnum.IN_PROCESS, "Custom Home Report Generated"),
    CHR_REPORT_APPROVED(220, ProcessStatusEnum.REPORT_APPROVED, "Custom Home Report Approved"),
    CHR_REPORT_DISAPPROVED(221, ProcessStatusEnum.IN_PROCESS, "Custom Home Report Disapproved"),

	BEESSKETCH_REPORT_SUBMITTED(222, ProcessStatusEnum.UNDER_REVIEW, "BeesSketch Report Submitted"),
	BEESSKETCH_REPORT_GENERATED(223, ProcessStatusEnum.IN_PROCESS, "BeesSketch Report Generated"),
	BEESSKETCH_REPORT_APPROVED(224, ProcessStatusEnum.REPORT_APPROVED, "BeesSketch Report Approved"),
	BEESSKETCH_REPORT_DISAPPROVED(225, ProcessStatusEnum.IN_PROCESS, "BeesSketch Report Disapproved"),

	SNAP_REPORT_SUBMITTED(226, ProcessStatusEnum.UNDER_REVIEW, "Snap360 Report Submitted"),
	SNAP_REPORT_GENERATED(227, ProcessStatusEnum.IN_PROCESS, "Snap360 Report Generated"),
	SNAP_REPORT_APPROVED(228, ProcessStatusEnum.REPORT_APPROVED, "Snap360 Report Approved"),
	SNAP_REPORT_DISAPPROVED(229, ProcessStatusEnum.IN_PROCESS, "Snap360 Report Disapproved"),

	MACRO_XML_GENERATED(146, null, "Macro XML Report Generated"),

	CLAIM_REVIEWED(70, null, "Claim Reviewed"),

	// Claim report was submitted to insurance company
	CLAIM_SUBMITTED(71, null, "Claim Submitted"),

	CONSTRUCTION_SCHEDULED(80, null, "Construction Scheduled"),
	CONSTRUCTION_CANCELLED(81, null, "Construction Cancelled"),
	CONSTRUCTION_FINISHED(82, null, "Construction Finished"),

	// Claim Estimate was approved by insurance company
	COMPLETED(90, null, "Completed"),

    // ai-failure status
    THREE_D_GENERATION_FAILED(1001, ProcessStatusEnum.IN_PROCESS, "3D Generation Failed"),
    PRE_RANGING_FAILED(1002, ProcessStatusEnum.IN_PROCESS, "Pre-Ranging Failed"),
    RANGING_FAILED(1003, ProcessStatusEnum.IN_PROCESS, "Ranging Failed"),
    PRE_SCOPING_FAILED(1004, ProcessStatusEnum.IN_PROCESS, "Pre-Scoping Failed"),
    SCOPING_FAILED(1005, ProcessStatusEnum.IN_PROCESS, "Scoping Failed"),
    PRE_PLANE_FAILED(1006, ProcessStatusEnum.IN_PROCESS, "Pre-Plane Failed"),
    PLANE_FAILED(1007, ProcessStatusEnum.IN_PROCESS, "Plane Failed"),
    PRE_BOUNDARY_FAILED(1008, ProcessStatusEnum.IN_PROCESS, "Pre-Boundary Failed"),
    BOUNDARY_FAILED(1009, ProcessStatusEnum.IN_PROCESS, "Boundary Failed"),
    POST_BOUNDARY_FAILED(1010, ProcessStatusEnum.IN_PROCESS, "Post-Boundary Failed"),
    PRE_RANGING_STARTED(1011, ProcessStatusEnum.IN_PROCESS, "Pre-Ranging Started"),
    DATA_TRANSFERRED(1012, ProcessStatusEnum.IN_PROCESS, "Project Data Transferred"),

    AI_FINISHED_RETURN_TO_CLIENT(10000, ProcessStatusEnum.AI_FINISHED_RETURN_TO_CLIENT, "Project Data Transferred"),
    ;

	private final int code;
	private final String display;
	private final ProcessStatusEnum category;
	private final static List<ProjectStatusEnum> aiFlow;
	private final static List<ProjectStatusEnum> aiFlowFromUser;

	static{
		// [!important] Don't change the order
		aiFlow = new ArrayList<>();
		aiFlowFromUser = new ArrayList<>();

		aiFlow.add(ProjectStatusEnum.THREE_D_GENERATION_START);
		aiFlowFromUser.add(ProjectStatusEnum.THREE_D_GENERATION_START);

		aiFlow.add(ProjectStatusEnum.THREE_D_GENERATION_FINISHED);
		aiFlow.add(ProjectStatusEnum.PRE_RANGING_FINISHED);

		aiFlow.add(ProjectStatusEnum.RANGING_FINISHED);
		aiFlowFromUser.add(ProjectStatusEnum.RANGING_FINISHED);

		aiFlow.add(ProjectStatusEnum.PRE_SCOPING_FINISHED);

		aiFlow.add(ProjectStatusEnum.SCOPING_FINISHED);
		aiFlowFromUser.add(ProjectStatusEnum.SCOPING_FINISHED);

		aiFlow.add(ProjectStatusEnum.PRE_PLANE_FINISHED);

		aiFlow.add(ProjectStatusEnum.PLANE_FINISHED);
		aiFlowFromUser.add(ProjectStatusEnum.PLANE_FINISHED);

		aiFlow.add(ProjectStatusEnum.PRE_BOUNDARY_FINISHED);

		aiFlow.add(ProjectStatusEnum.BOUNDARY_FINISHED);
		aiFlowFromUser.add(ProjectStatusEnum.BOUNDARY_FINISHED);

		aiFlow.add(ProjectStatusEnum.POST_BOUNDARY_FINISHED);
	}

	ProjectStatusEnum(int code, ProcessStatusEnum category, String display){
		this.code = code;
		this.category = category;
		this.display = display;
	}

	public static ProjectStatusEnum getEnum(int code){
		ProjectStatusEnum[] statuses = ProjectStatusEnum.values();
		for(ProjectStatusEnum status: statuses){
			if(status.getCode() == code){
				return status;
			}
		}
		return null;
	}

	@Override
	public int getCode(){
		return code;
	}
	public String getCategory(){
		return category == null? "": category.getDisplay();
	}
	public ProcessStatusEnum getProcessStatus() {
		return category;
	}
	@Override
	public String getDisplay(){
		return display;
	}

	public boolean isInAiFlow(){
		return aiFlow.contains(this);
	}

	public boolean isAiRequestFromUser() {
		return aiFlowFromUser.contains(this);
	}
}
