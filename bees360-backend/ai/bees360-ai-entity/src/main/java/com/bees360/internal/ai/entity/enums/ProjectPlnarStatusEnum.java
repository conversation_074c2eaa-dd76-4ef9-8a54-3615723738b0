package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;
import com.bees360.internal.ai.entity.dto.IdValueDto;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/07/25 16:47
 */
public enum ProjectPlnarStatusEnum {

    NONE(0, "None"),
    LINK_RECEIVED(1, "Link Received"),
    PROCESSING(2, "Processing"),
    UPLOADED(3, "Uploaded To Symbility")
    ;

    private final Integer code;
    private final String display;

    ProjectPlnarStatusEnum(Integer code, String display) {
        this.code = code;
        this.display = display;
    }

    public static ProjectPlnarStatusEnum getEnum(Integer code){
        return Stream.of(ProjectPlnarStatusEnum.values())
            .filter(o -> Objects.equals(code, o.getCode()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("ProjectPlnarStatusEnum is null, value:" + code));
    }

    public static List<CodeNameDto> getPlnarStatusDict() {
        return Arrays.stream(ProjectPlnarStatusEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getDisplay()))
            .collect(Collectors.toList());
    }

    public Integer getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }

    public static boolean orderedPlnar(Integer status) {
        return getOrderedStatus().stream().anyMatch(s -> Objects.equals(s.getCode(), status));
    }

    private static List<ProjectPlnarStatusEnum> getOrderedStatus() {
        return Arrays.asList(ProjectPlnarStatusEnum.LINK_RECEIVED, ProjectPlnarStatusEnum.PROCESSING,
            ProjectPlnarStatusEnum.UPLOADED);
    }
}
