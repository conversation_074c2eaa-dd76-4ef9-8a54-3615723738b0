package com.bees360.internal.ai.entity.dto;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2019/11/02 14:37
 */
@Getter
@Setter
@ToString
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
public class ImageQueryDto {
    /** 图片的拍摄来源类型 **/
    private int fileSourceType;
    /** 是否被删除 **/
    private boolean deleted;

    /**
     * 当 fileSourceType 为<code>IGNORE_FILE_SOURCE_TYPE</code>时
     * 不对 fileSourceType 进行过滤
     */
    private static final int IGNORE_FILE_SOURCE_TYPE = -1;

    /**
     * 查询时，是否忽略fileSourceType这个字段
     * @return 是否忽略fileSourceType这个字段
     */
    public boolean ignoreFileSourceType() {
        return IGNORE_FILE_SOURCE_TYPE == fileSourceType;
    }
}
