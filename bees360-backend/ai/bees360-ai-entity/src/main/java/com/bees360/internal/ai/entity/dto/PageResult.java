package com.bees360.internal.ai.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/09/02 15:45
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PageResult<T> {
    private List<T> items;
    private Pagination page;

    public static <T> PageResult<T> empty(int pageIndex, int pageSize) {
        PageResult<T> pageResult = new PageResult<>();
        pageResult.setItems(new ArrayList<>());
        pageResult.setPage(Pagination.empty(pageIndex, pageSize));
        return pageResult;
    }

    public static <T> PageResult<T> empty(Pagination pagination) {
        PageResult<T> pageResult = new PageResult<>();
        pageResult.setItems(new ArrayList<>());
        pageResult.setPage(pagination);
        return pageResult;
    }

    public static <T> PageResult<T> fetchPage(PageResult<T> pageResult, List<T> dataList,
                                              int pageIndex, int pageSize, int sum) {
        if (CollectionUtils.isEmpty(dataList)) {
            return PageResult.empty(pageIndex, pageSize);
        }
        Pagination pagination = Pagination.builder()
            .pageIndex(pageIndex)
            .pageSize(pageSize)
            .sum(sum)
            .totalPage(Pagination.countTotalPage(sum, pageSize))
            .build();

        if (checkErrorPage(pageIndex, pagination)) {
            return PageResult.empty(pagination);
        }
        pageResult.setItems(dataList);
        pageResult.setPage(pagination);
        return pageResult;
    }

    private static boolean checkErrorPage(int pageIndex, Pagination pagination) {
        // 页码超出范围，页码应该从1开始，且少于或者等于总页数
        return pageIndex < 1 || pagination.getPageIndex() > pagination.getTotalPage();
    }
}
