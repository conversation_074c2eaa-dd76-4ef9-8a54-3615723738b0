package com.bees360.internal.ai.entity.enums;

import java.util.Objects;
import java.util.stream.Stream;

public enum PayStatusEnum implements BaseCodeEnum{
    UNPAID(-1, "N"),
    PAID(1, "Y"),
    ;

    private final int code;
    private final String display;

    PayStatusEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }


    @Override
    public String getDisplay() {
        return display;
    }

    @Override
    public int getCode() {
        return code;
    }

    public static PayStatusEnum getEnum(int code) {
        return Stream.of(PayStatusEnum.values()).filter(o -> Objects.equals(code, o.getCode())).findFirst().orElse(null);
    }

    public static boolean isPaid(Integer code) {
        return Objects.equals(PAID.code, code);
    }
}
