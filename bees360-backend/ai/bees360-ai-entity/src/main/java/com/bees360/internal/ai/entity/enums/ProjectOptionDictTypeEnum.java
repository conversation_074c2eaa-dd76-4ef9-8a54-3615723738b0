package com.bees360.internal.ai.entity.enums;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/05/12 11:42
 */
public enum ProjectOptionDictTypeEnum {

    CITY("CITY"),
    STAT<PERSON>("STATE"),
    COMPANY("COMPANY"),
    CREATOR("CREATOR"),
    AD<PERSON>USTER("ADJUSTER"),
    PROCESSOR("PROCESSOR"),
    REVIEWER("REVIEWER"),
    PILOT("PILOT"),
    MISSION_WARN_EMAIL("MISSION_WARN_EMAIL"),
    UNDERWRITING_MISSION_WARN_EMAIL_COMPANY("UNDERWRITING_MISSION_WARN_EMAIL_COMPANY"),
    CLAIM_MISSION_WARN_EMAIL_COMPANY("CLAIM_MISSION_WARN_EMAIL_COMPANY"),
    PRODUCER("PRODUCER")
    ;

    private final String type;

    public String getType() {
        return type;
    }

    ProjectOptionDictTypeEnum(String type) {
        this.type = type;
    }

    public static List<String> getOptionTypes() {
        return Stream.of(ProjectOptionDictTypeEnum.values())
            .map(ProjectOptionDictTypeEnum::getType)
            .collect(Collectors.toList());
    }
}
