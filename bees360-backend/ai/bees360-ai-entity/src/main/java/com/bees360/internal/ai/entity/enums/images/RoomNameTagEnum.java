package com.bees360.internal.ai.entity.enums.images;

import org.apache.commons.collections4.map.CaseInsensitiveMap;

import java.util.Arrays;
import java.util.Map;
import java.util.StringTokenizer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @see com.bees360.entity.enums.RoomNameEnum
 * 房间名称tag
 */
public enum RoomNameTagEnum {
    BEDROOM(505, "Bedroom", "bed"),
    BATHROOM(506, "Bathroom", "bath"),
    DINING_ROOM(504, "DiningRoom", "dining"),
    LIVING_ROOM(503, "LivingRoom", "living"),
    FAMILY_ROOM(502, "FamilyRoom", "family"),
    OFFICE_ROOM(541, "OfficeRoom", "office"),
    STUDY_ROOM(541, "StudyRoom", "study"),
    KITCHEN(507, "Kitchen", "kitchen"),
    CLOSET(508, "Closet", "closet"),
    LAUNDRY_ROOM(525, "LaundryRoom", "laundry"),
    STAIRCASE(510, "Staircase", "staircase"),
    CORRIDOR(509, "Corridor", "corridor"),
    GARAGE(411, "Garage", "garage"),
    UTILITY_ROOM(1512, "UtilityRoom", "utility"),
    MEDIA_ROOM(1513, "MediaRoom", "media"),
    BREAKFAST_ROOM(1518, "BreakfastRoom", "breakfast"),
    OTHER_ROOM(410, null, null),
    ATTIC(408, "Attic", "attic"),
    ;

    private final int code;
    private final String display;
    private final String key;

    RoomNameTagEnum(int code, String display, String key) {
        this.code = code;
        this.display = display;
        this.key = key;
    }

    /**
     * 分词查找RoomName，只要找到关键字匹配，就可以找到相应的Enum
     *
     * @param roomName
     * @return RoomNameTagEnum
     */
    public static RoomNameTagEnum findByRoomName(String roomName) {
        if (null == roomName) {
            return null;
        }
        Map<String, RoomNameTagEnum> roomNameTagEnumMap =
                Arrays.stream(values())
                        .collect(
                                Collectors.toMap(
                                        RoomNameTagEnum::getKey, Function.identity(), (a, b) -> b));
        CaseInsensitiveMap<String, RoomNameTagEnum> map =
                new CaseInsensitiveMap<>(roomNameTagEnumMap);

        String room = roomName.replaceAll("(?i)(room)", " ");
        StringTokenizer st = new StringTokenizer(room, " \t\n\r\f/|,");
        while (st.hasMoreElements()) {
            RoomNameTagEnum roomNameTagEnum = map.get((String) st.nextElement());
            if (null != roomNameTagEnum) {
                return roomNameTagEnum;
            }
        }
        return OTHER_ROOM;
    }

    public int getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }

    public String getKey() {
        return key;
    }
}
