package com.bees360.internal.ai.entity.enums.images;

import java.util.Arrays;

/** 序号tag */
public enum NumberTagEnum {
    MASTER(1501, "Master"),
    FIRST(1511, "1"),
    SECOND(1502, "2"),
    THIRD(1503, "3"),
    FOURTH(1504, "4"),
    <PERSON>IFTH(1505, "5"),
    <PERSON>IXTH(1506, "6"),
    SEVE<PERSON><PERSON>(1507, "7"),
    EIGHTH(1508, "8"),
    NINTH(1509, "9"),
    TENTH(1510, "10"),
    ;

    private final int code;
    private final String display;

    NumberTagEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    public static NumberTagEnum valueOfDisplayIgnoreCase(String display) {
        if (null == display) {
            return null;
        }
        return Arrays.stream(values())
                .filter(o -> o.getDisplay().equalsIgnoreCase(display))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据序号查找numberTag
     *
     * @param display 数字
     * @return NumberTagEnum
     */
    public static NumberTagEnum findByNumber(String display) {
        return valueOfDisplayIgnoreCase(display);
    }

    public int getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }
}
