package com.bees360.internal.ai.entity;

import lombok.Data;

@Data
public class PilotBatchInfo {

    private String batchNo;
    private Double basePay;
    private Double extraPay;
    private Long paymentDate;

    public Double getTotalPay() {
        Double total = 0D;
        if (basePay != null) {
            total += basePay;
        }
        if (extraPay != null) {
            total += extraPay;
        }
        return total;
    }
}
