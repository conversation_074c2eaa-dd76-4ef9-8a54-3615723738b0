package com.bees360.internal.ai.entity.dto;

import java.util.HashMap;
import java.util.Map;

public enum ProcessStatusEnum {
	DELETED(0, "Deleted", false),
	NEW(1, "New Project", true),
	IMAGE_UPLOADED(2, "Image Uploaded", true),
	IN_PROCESS(3, "In Process", true),
	UNDER_REVIEW(4, "Under Review", true),
	REPORT_APPROVED(5, "Report Approved", true),
    AI_FINISHED_RETURN_TO_CLIENT(6, "Return To Client", true),
    CLIENT_RECEIVED(7, "Client Received", true)
	;

	private final int code;
	private final String display;
	private final boolean visible;
	ProcessStatusEnum(int code, String display, boolean visible) {
		this.code = code;
		this.display = display;
		this.visible = visible;
	}

	private static final Map<Integer, ProcessStatusEnum> statusMap = new HashMap<>();

	static {
		for(ProcessStatusEnum status: ProcessStatusEnum.values()) {
			statusMap.put(status.getCode(), status);
		}
	}

	public String getDisplay() {
		return display;
	}
	public int getCode() {
		return code;
	}
	public boolean isVisible() {
		return visible;
	}

	public static ProcessStatusEnum getEnum(int code) {
		return statusMap.get(code);
	}
}
