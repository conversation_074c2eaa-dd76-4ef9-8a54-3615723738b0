package com.bees360.internal.ai.entity;

import com.bees360.commons.elasticsearchsupport.definition.DataModel;
import com.bees360.commons.elasticsearchsupport.definition.EsDocument;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/07/25 14:46
 */
@Data
@EsDocument(indexName = "project_option_dict", idPrefix = "dict_type")
@NoArgsConstructor
public class ProjectOptionDict implements DataModel<String> {

    private String type;

    private Set<String> dict;

    @Override
    public String id() {
        return type;
    }
}
