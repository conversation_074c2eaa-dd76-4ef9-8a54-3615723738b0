package com.bees360.internal.ai.entity.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/10/26 19:07
 */
public enum ProjectSyncPointEnum {

    MANUAL_SYNC(1, "<PERSON><PERSON><PERSON>_SYNC", null),
    IMAGE_UPLOADED(2, "IMAGE_UPLOADED", AiProjectStatusEnum.IMAGE_UPLOADED),
    CUSTOM_CONTACTED(3, "CUSTOM_CONTACTED", AiProjectStatusEnum.CUSTOMER_CONTACTED),
    SITE_INSPECTED(4, "SITE_INSPECTED", AiProjectStatusEnum.SITE_INSPECTED),
    ASSIGNED_TO_PILOT(5, "ASSIGNED_TO_PILOT", AiProjectStatusEnum.ASSIGNED_TO_PILOT),
    IBEES_UPLOADED(6, "IBEES_UPLOADED", AiProjectStatusEnum.IBEES_UPLOADED),
    PROJECT_CREATED(7, "PROJECT_CREATED", AiProjectStatusEnum.PROJECT_CREATED)
    ;

    private final int code;
    private final String type;
    private final AiProjectStatusEnum statusEnum;

    ProjectSyncPointEnum(int code, String type, AiProjectStatusEnum statusEnum){
        this.code = code;
        this.type = type;
        this.statusEnum = statusEnum;
    }
    public int getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    public AiProjectStatusEnum getStatusEnum() {
        return statusEnum;
    }

    public static ProjectSyncPointEnum getEnumByType(String syncPoint) {
        return Stream.of(ProjectSyncPointEnum.values()).filter(o -> StringUtils.equals(syncPoint, o.getType())).findFirst().orElse(null);
    }
}
