package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum CompanyTypeEnum implements BaseCodeEnum{

	INSURANCE_COMPANY(0, "Insurance Company"),
	REPAIR_COMPANY(1, "Repair Company"),
	MATERIAL_PROVIDER_COMPANY(2, "Material Provider Company"),
	OTHER(3, "Others"),
	CLAIM_COMPANY(4, "Claim Company"),
	UNDERWRITING_COMPANY(5, "Underwriting Company"),
	ROOFING_COMPANY(6, "Roofing Company")
	;

	private final int code;
	private final String display;

	CompanyTypeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public String getDisplay(){
		return display;
	}

	public static CompanyTypeEnum getEnum(int code) {
		for(CompanyTypeEnum type: CompanyTypeEnum.values()) {
			if(type.getCode() == code) {
				return type;
			}
		}
		return null;
	}

    public static List<CodeNameDto> getDict() {
        return Arrays.stream(CompanyTypeEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getDisplay())).collect(Collectors.toList());
    }

	@Override
	@JsonValue
	public int getCode() {
		return code;
	}

	@Override
	public String toString(){
		return display;
	}
}
