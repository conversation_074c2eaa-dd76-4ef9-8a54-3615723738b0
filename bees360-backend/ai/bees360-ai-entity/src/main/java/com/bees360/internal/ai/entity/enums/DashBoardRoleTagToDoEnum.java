package com.bees360.internal.ai.entity.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * dashboard role todoStatus 对应列表
 * <AUTHOR>
 * @date 2020/04/23 11:01
 */
public enum DashBoardRoleTagToDoEnum {

    // roleTag = dashboard左侧标签,避免跟userAuth混淆
    ADMIN_TODO(1, "ADMIN", Arrays.asList(AiProjectStatusEnum.CUSTOMER_CONTACTED, AiProjectStatusEnum.ASSIGNED_TO_PILOT,
        AiProjectStatusEnum.SITE_INSPECTED, AiProjectStatusEnum.PROJECT_REWORK, AiProjectStatusEnum.IMAGE_UPLOADED, AiProjectStatusEnum.IBEES_UPLOADED)),
    PROCESSOR_TODO(2, "PROCESSOR", Arrays.asList(
        AiProjectStatusEnum.IMAGE_UPLOADED, AiProjectStatusEnum.IBEES_UPLOADED)),

    ADJUSTER_TODO(3, "ADJUSTER", Arrays.asList(AiProjectStatusEnum.CUSTOMER_CONTACTED,
        AiProjectStatusEnum.ASSIGNED_TO_PILOT,
        AiProjectStatusEnum.SITE_INSPECTED, AiProjectStatusEnum.MEMBER_ASSIGNED,
        AiProjectStatusEnum.PROJECT_REWORK, AiProjectStatusEnum.IMAGE_UPLOADED, AiProjectStatusEnum.IBEES_UPLOADED,
        AiProjectStatusEnum.REPORT_ASSEMBLED)),
    REVIEWER_TODO(4, "REVIEWER", Arrays.asList(AiProjectStatusEnum.REPORT_ASSEMBLED, AiProjectStatusEnum.MEMBER_ASSIGNED)),
    PRODUCER_TODO(5, "PRODUCER", Arrays.asList(
        AiProjectStatusEnum.CUSTOMER_CONTACTED,
        AiProjectStatusEnum.ASSIGNED_TO_PILOT,
        AiProjectStatusEnum.SITE_INSPECTED, AiProjectStatusEnum.MEMBER_ASSIGNED,
        AiProjectStatusEnum.PROJECT_REWORK, AiProjectStatusEnum.IMAGE_UPLOADED, AiProjectStatusEnum.IBEES_UPLOADED)),
    ;

    private final int code;
    private final String roleTag;
    private final List<Integer> todoStatusCodes;


    DashBoardRoleTagToDoEnum(int code, String roleTag, List<AiProjectStatusEnum> todoStatusCodes){
        this.code = code;
        this.roleTag = roleTag;
        this.todoStatusCodes = Collections.unmodifiableList(
            todoStatusCodes.stream().map(AiProjectStatusEnum::getCode).collect(Collectors.toList()));
    }

    public static DashBoardRoleTagToDoEnum getByRoleTag(Optional<String> optional) {
        // 默认admin
        String roleTag = optional.orElse(DashBoardRoleTagToDoEnum.ADMIN_TODO.getRoleTag());
        return getEnum(roleTag);
    }

    public static DashBoardRoleTagToDoEnum getEnum(String value) {
        if (value == null) {
            return null;
        }
        return Arrays.stream(DashBoardRoleTagToDoEnum.values())
            .filter(e -> Objects.equals(e.getRoleTag(), value))
            .findFirst()
            .orElse(null);
    }

    public int getCode() {
        return code;
    }

    public String getRoleTag() {
        return roleTag;
    }

    public List<Integer> getTodoStatusCodes() {
        return todoStatusCodes;
    }
}
