package com.bees360.internal.ai.entity.dto;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder(builderClassName = "Builder", builderMethodName = "newBuilder", setterPrefix = "set")
public class ProcessorClaimStatistics {
    private String staffName;
    private String companyName;
    private long darSubmitted;
    private String darSubmittedProjects;
    private long pirSubmitted;
    private String pirSubmittedProjects;
    private long threeDSubmitted;
    private String threeDSubmittedProjects;
    private long darApproved;
    private String darApprovedProjects;
    private long pirApproved;
    private String pirApprovedProjects;
    private long hailReportUploaded;
    private String hailReportUploadedProjects;
    private long projectionFixed;
    private String projectionFixedProjects;
    private String staffEmail;
    private long annotationCompleted;
    private String annotationCompletedProjects;
}
