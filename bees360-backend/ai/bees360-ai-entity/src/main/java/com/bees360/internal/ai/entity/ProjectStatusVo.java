package com.bees360.internal.ai.entity;

import com.bees360.internal.ai.entity.dto.CodeNameDto;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/12/26 11:45
 */
@Data
@NoArgsConstructor
public class ProjectStatusVo {
    private CodeNameDto status;
    private Long createdTime;
    private String userName;

    public ProjectStatusVo(AiProjectStatusEnum status) {
        this.status = new CodeNameDto(status.getCode(), status.getDisplay());
    }

    public boolean available() {
        return createdTime != null;
    }
}
