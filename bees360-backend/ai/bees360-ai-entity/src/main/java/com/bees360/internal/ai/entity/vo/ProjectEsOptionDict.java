package com.bees360.internal.ai.entity.vo;

import com.bees360.internal.ai.entity.ImageTag;
import com.bees360.internal.ai.entity.LogEntryTypeDict;
import com.bees360.internal.ai.entity.dto.CodeNameDto;
import lombok.Data;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/06/19 15:00
 */
@Data
public class ProjectEsOptionDict {

    private List<CodeNameDto> serviceType;

    private List<CodeNameDto> newProjectStatus;

    private List<CodeNameDto> companyType;

    private List<CodeNameDto> projectType;

    private List<CodeNameDto> claimType;

    private List<CodeNameDto> processStatus;

    private List<ImageTag> category;

    private List<ImageTag> object;

    private List<ImageTag> location;

    private List<ImageTag> scope;

    private List<ImageTag> direction;

    private List<ImageTag> annotation;

    private List<ImageTag> reportTags;

    private List<ImageTag> orientationTags;

    private List<ImageTag> numberTags;

    private List<ImageTag> floorLevelTags;

    private List<CodeNameDto> hoverStatus;

    private List<CodeNameDto> plnarStatus;

    private List<CodeNameDto> reportStatus;

    private List<LogEntryTypeDict> logDict;

    /**
     * 从已有project过滤出来的搜索项，默认空
     */
    private Collection<String> city = Collections.emptyList();

    private Collection<String> state = Collections.emptyList();

    private Collection<String> company = Collections.emptyList();

    private Collection<String> creator = Collections.emptyList();


    private Collection<String> memberRole = Collections.emptyList();

    private Collection<String> adjuster = Collections.emptyList();
    private Collection<String> processor = Collections.emptyList();
    private Collection<String> reviewer = Collections.emptyList();

    private Collection<Long> projectTags = Collections.emptyList();

    private Collection<String> pilots = Collections.emptyList();

    private Collection<String> producers = Collections.emptyList();
}
