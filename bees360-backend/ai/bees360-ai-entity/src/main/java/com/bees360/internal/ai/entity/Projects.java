package com.bees360.internal.ai.entity;

import com.bees360.project.Message;
import com.bees360.customer.Message.CustomerMessage;
import com.bees360.contract.Message.ContractMessage;
import com.bees360.project.Project;
import com.google.protobuf.util.Timestamps;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Objects;
import java.util.Optional;

import static com.bees360.util.Functions.acceptIfNotNull;

public class Projects {
    private Projects(){}

    public static Project from(ProjectEsModel esModel) {
        Message.ProjectMessage.Builder builder = Message.ProjectMessage.newBuilder();
        if (esModel != null) {
            builder.setId(String.valueOf(esModel.getProjectId()));
            acceptIfNotNull(
                    builder::setServiceType,
                    Message.ServiceType.forNumber(esModel.getServiceType()));
            acceptIfNotNull(builder::setPolicyNumber, esModel.getPolicyNumber());
            acceptIfNotNull(builder::setInspectionNumber, esModel.getInspectionNumber());
            acceptIfNotNull(builder::setPolicyEffectiveDate, esModel.getPolicyEffectiveDate());
            acceptIfNotNull(
                    builder::setClaimType,
                    Optional.of(esModel.getClaimType())
                            .map(Message.ClaimType::forNumber)
                            .orElse(null));
            acceptIfNotNull(
                    builder::setServiceType,
                    Optional.of(esModel.getServiceType())
                            .map(Message.ServiceType::forNumber)
                            .orElse(null));
            acceptIfNotNull(builder::setDueDate, esModel.getDueDate(), Timestamps::fromMillis);
            acceptIfNotNull(
                    builder::setLatestStatus, Message.ProjectStatus.forNumber(esModel.getProjectStatus()));

            acceptIfNotNull(builder::setId, esModel.getProjectId(), String::valueOf);
            acceptIfNotNull(
                    builder::setCreateBy,
                    esModel.getCreatedBy(),
                    userId ->
                            com.bees360.user.Message.UserMessage.newBuilder()
                                    .setId(String.valueOf(userId))
                                    .build());
            acceptIfNotNull(builder::setCreateAt, esModel.getCreatedTime(), Timestamps::fromMillis);

            acceptIfNotNull(builder::setGps, getGpsLocationMessage(esModel));
            acceptIfNotNull(builder::setBuilding, getBuildingMessage(esModel));
            acceptIfNotNull(builder::setAddress, getAddressMessage(esModel));

            acceptIfNotNull(
                    builder::setInspectionAppointmentTime,
                    esModel.getInspectionTime(),
                    Timestamps::fromMillis);

            var contract = ContractMessage.newBuilder();
            Optional.ofNullable(esModel.getInsuranceCompany()).ifPresent(
                id -> contract.setInsuredBy(CustomerMessage.newBuilder()
                    .setId(String.valueOf(id))
                    .build())
            );
            Optional.ofNullable(esModel.getRepairCompany()).ifPresent(
                id -> contract.setProcessedBy(CustomerMessage.newBuilder()
                    .setId(String.valueOf(id))
                    .build())
            );
            builder.setContract(contract);
        }
        return Project.from(builder.build());
    }

    public static Message.GpsLocationMessage getGpsLocationMessage(ProjectEsModel esModel) {
        double lat = esModel.getGpsLocationLatitude();
        double lng = esModel.getGpsLocationLongitude();
        if (!ObjectUtils.allNotNull(lat, lng)) {
            return null;
        }
        return Message.GpsLocationMessage.newBuilder().setLat(lat).setLng(lng).build();
    }

    private static com.bees360.building.Message.BuildingMessage getBuildingMessage(
            ProjectEsModel esModel) {
        if (Objects.isNull(esModel)) {
            return null;
        }
        com.bees360.building.Message.BuildingMessage.Builder builder =
                com.bees360.building.Message.BuildingMessage.newBuilder();

        String yearBuild = org.apache.commons.lang3.StringUtils.trimToNull(esModel.getYearBuilt());
        acceptIfNotNull(builder::setYearBuilt, yearBuild, Integer::parseInt);
        acceptIfNotNull(
                builder::setType,
                esModel.getProjectType(),
                com.bees360.building.Message.BuildingType::forNumber);

        return builder.build();
    }

    private static com.bees360.project.base.Message.AddressMessage getAddressMessage(
            ProjectEsModel esModel) {
        if (Objects.isNull(esModel)) {
            return null;
        }
        com.bees360.project.base.Message.AddressMessage.Builder builder =
                com.bees360.project.base.Message.AddressMessage.newBuilder();
        acceptIfNotNull(builder::setCountry, esModel.getCountry());
        acceptIfNotNull(builder::setState, esModel.getState());
        acceptIfNotNull(builder::setCity, esModel.getCity());
        acceptIfNotNull(builder::setZipCode, esModel.getZipCode());
        acceptIfNotNull(builder::setAddressLine1, esModel.getAddress());
        acceptIfNotNull(builder::setAddressLine2, "");
        return builder.build();
    }
}
