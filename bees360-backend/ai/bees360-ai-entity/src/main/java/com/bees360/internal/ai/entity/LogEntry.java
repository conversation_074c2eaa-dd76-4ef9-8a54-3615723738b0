package com.bees360.internal.ai.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/07/24 14:58
 */
@Data
@NoArgsConstructor
public class LogEntry {

    private String userId;

    private String username;

    private String type;

    private String createdTime;

    private LogEntryDetail logDetail;

    public LogEntry(String userId, String username, String type, String createdTime, LogEntryDetail logDetail) {
        this.userId = userId;
        this.username = username;
        this.type = type;
        this.createdTime = createdTime;
        this.logDetail = logDetail;
    }
}
