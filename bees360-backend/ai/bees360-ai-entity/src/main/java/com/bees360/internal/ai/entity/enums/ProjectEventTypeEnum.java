package com.bees360.internal.ai.entity.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Stream;

public enum ProjectEventTypeEnum {
	DELETE(-1, "delete", true),
	CANCEL(-2, "cancel", true),
    CREATED(1, "created", true),
    RECOVERED(Integer.MIN_VALUE, "recovered", true),
	REWORK(70, "rework", true),
    IMAGE_UPLOADED(80, "imageUploaded", true),
    RETURN_TO_CLIENT(90, "returnToClient", true),
    ESTIMATE_COMPLETE(95, "estimateComplete", true),
    CLIENT_RECEIVED(100, "clientReceived", true)
    ;

    private final int code;
	private final String type;
	private final boolean isForce;
	ProjectEventTypeEnum(int code, String type, boolean isForce){
		this.code = code;
		this.type = type;
		this.isForce = isForce;
	}

    public static ProjectEventTypeEnum getEnumByType(String type) {
        return Stream.of(ProjectEventTypeEnum.values())
            .filter(o -> StringUtils.equals(type, o.getType()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getProjectEventTypeEnum is null, type:" + type));
    }

	public int getCode() {
		return code;
	}

	public String getType() {
		return type;
	}

    public boolean isForce() {
        return isForce;
    }
}
