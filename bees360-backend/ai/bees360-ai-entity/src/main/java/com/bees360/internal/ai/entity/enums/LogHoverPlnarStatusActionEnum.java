package com.bees360.internal.ai.entity.enums;

import com.bees360.internal.ai.entity.dto.CodeNameDto;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/07/30 09:27
 */
public enum LogHoverPlnarStatusActionEnum implements LogActionType {

    NONE(0, "changed to None"),
    ORDERED(1, "changed to Ordered"),
    PROCESSING(2, "changed to Processing"),
    NOT_FOUND(3, "changed to Not Found"),
    FAILED(4, "changed to Failed"),
    ;


    private final int code;
    private final String value;

    LogHoverPlnarStatusActionEnum(int code, String value){
        this.code = code;
        this.value = value;
    }

    public static LogHoverPlnarStatusActionEnum getEnumByValue(String value) {
        return Stream.of(LogHoverPlnarStatusActionEnum.values())
            .filter(o -> Objects.equals(value, o.getValue()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("getLogHoverPlnarStatusActionEnum" +
                " is null, value:" + value));

    }

    public static List<CodeNameDto> getEnumDict() {
        return Arrays.stream(LogHoverPlnarStatusActionEnum.values())
            .map(o -> new CodeNameDto(o.getCode(), o.getValue()))
            .collect(Collectors.toList());
    }

    @Override
    public int getCode(){
        return code;
    }

    public String getValue() {
        return value;
    }
}
