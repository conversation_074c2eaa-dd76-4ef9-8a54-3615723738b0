package com.bees360.internal.ai.entity;

import java.util.Arrays;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

@Data
@Builder
@ToString
public class JobData {

    private long id;

    private long projectId;

    private long pipelineId;

    private String userId;

    private String jobId;

    private String jobName;

    private String jobParameter;

    private String resultData;

    private int jobType;

    private int status;

    private int retryCount;

    private long startTime;

    private Long completedTime;

    public List<String> getResultDataKeys() {
        return Arrays.asList(resultData.split(","));
    }
}
