package com.bees360.internal.ai.entity.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/02/28 14:49
 */
@Data
@NoArgsConstructor
public class ProjectInspectionInfoVo {
    private String policyNumber;
    private String inspectionNumber;
    /**
     * 对应页面的: Type of Loss
     */
    private Integer claimType;
    /**
     * 对应页面的: Date of Loss
     */
    private Long damageEventTime;
    /**
     * 代理信息
     */
    private String agent;
    /**
     * 代理人名称
     */
    private String agentContactName;
    private String agentPhone;
    private String agentEmail;

    private  String insuranceCompanyName;
    private  String repairCompanyName;

    private  String insuranceCompanyLogo;
    private  String repairCompanyLogo;
    /**
     * 检测时间
     */
    private Long inspectionTime;

    private String policyEffectiveDate;

    private String yearBuilt;

    private int payStatus;

    private String payStatusName;

    private long siteInspectedTime;

    private Long createdTime;

    private Long customerContactedTime;

    private String operatingCompanyName;
}
