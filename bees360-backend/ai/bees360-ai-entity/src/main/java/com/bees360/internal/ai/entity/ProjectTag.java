package com.bees360.internal.ai.entity;

import com.bees360.commons.elasticsearchsupport.definition.DataModel;
import com.bees360.commons.elasticsearchsupport.definition.EsDocument;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/09/08 11:32
 */
@Data
@EsDocument(indexName = "project_tag", idPrefix = "project_id")
@NoArgsConstructor
public class ProjectTag implements DataModel<Long> {

    private long projectId;

    private List<String> tags;

    @Override
    public Long id() {
        return this.projectId;
    }
}
