package com.bees360.internal.ai.entity.enums;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/07/02 20:03
 */
public enum UserAuthEnum {

    ROLE_ADMIN(1, "ROLE_ADMIN", "ADMIN", false),
    ROLE_ADJUSTER(2, "ROLE_ADJUSTER", "ADJUSTER", true),
    ROLE_REVIEWER(3, "ROLE_REVIEWER", "REVIEWER", true),
    ROLE_PROCESSOR(4, "ROLE_PROCESSOR", "PROCESSOR", true),
    EXTERNAL_ADJUSTER(5, "EXTERNAL_ADJUSTER", "EXTERNAL_ADJUSTER", false),
    ROLE_DEVELOPER(6, "ROLE_DEVELOPER", "DEVELOPER", false),
    ROLE_PRODUCER(7, "ROLE_PRODUCER", "PRODUCER", true),
    OPERATIONS_MANAGER(8, "OPERATIONS_MANAGER", "OPERATIONS_MANAGER", false);

    private final int code;
    private final String role;
    private final String auth;
    // 是否为可分配角色并且在search框可见
    private final boolean isMemberRole;

    private static final String ROLE_PREFIX = "ROLE_";

    UserAuthEnum(int code, String role, String auth, boolean isMemberRole) {
        this.code = code;
        this.role = role;
        this.auth = auth;
        this.isMemberRole = isMemberRole;
    }

    /**
     * 判断用户是否为某个角色
     * @param grantedAuthorities
     * @param authEnum
     * @return
     */
    public static boolean hasRole(Collection<String> grantedAuthorities, UserAuthEnum authEnum) {
        if (grantedAuthorities == null || authEnum == null) {
            return false;
        }
        return grantedAuthorities.contains(authEnum.getRole());
    }

    public static List<String> getMemberRoleDict() {
        return Arrays.stream(UserAuthEnum.values())
            .filter(UserAuthEnum::isMemberRole)
            .map(UserAuthEnum::getAuth)
            .collect(Collectors.toList());
    }

    public static String roleToAuth(String role) {
        return role.replace(ROLE_PREFIX , "");
    }

    public int getCode() {
        return code;
    }

    public String getRole() {
        return role;
    }

    public String getAuth() {
        return auth;
    }

    public boolean isMemberRole() {
        return isMemberRole;
    }
}
