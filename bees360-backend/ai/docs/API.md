Controller 层说明
===

### `guanrong.yang` `2019/09/06`

## API 说明

该项目提供`REST` + `Protobuf`的HTTP接口。

### API 响应体结构

```
HTTP/1.1 XXX
{
    code: int,
    message: string,
    data: {
        type: string,
        bytes: value
    }
}
```

字段名 | 描述
--- | ---
HTTP STATUS | Http响应码，划分响应结果大类
code | 响应详细 code
message | 响应提示信息
data | 响应结果数据，仅 `HTTP STATUS`值为200或者422时才有数据。

当`Http Status`为200时候，`code`值为0，表示成功。当`Http Status`为400时，表示请求失败，`code` 大于 0时，表示业务处理失败；-1时，表示非业务处理失败的客户端错误，设计不赋予该类错误特殊的code值，因而均默认为-1。

### HEADERS

HEADER | 值 | 描述
--- | --- | ---
Content-Type | application/x-protobuf | protobuf交互的接口使用该content-type

### HTTP 状态码
如下为系统中将会使用的状态码

状态码 | 含义
--- | ---
200 OK | 对一次成功的GET, PUT, PATCH 或 DELETE的响应
400 Bad Request | 业务处理错误，或者客户端请求错误
401 Unauthorized | 当没有提供或提供了无效认证细节时
403 Forbidden | 当认证成功但是认证用户无权访问该资源时，或者由于其他安全原因而被禁止时
404 Not Found | 当一个不存在的资源被请求时
415 Unsupported Media Type | 如果请求中包含了不正确的内容类型
422 Unprocessable Entity | 数据校验不通过
429 Too Many Requests | 请求过于频繁，需要稍后再尝试
500 Internal Server Error | 服务内部错误

部分接口我们会使用 `404 Not Found` 来代替`403 Forbiden` 来表示用户访问了无权访问的接口的错误，以保护用户私有数数据安全。

> 400 Bad Request

情况 | message | 描述
--- | --- | ---
业务处理不通过 | 见相应错误 | 业务逻辑处理失败，比如【提交】一个已经【审核通过】的报告
请求格式错误 | Problems parsing JSON | content-type为`application/json`时存在，传入的数据不是json, 客户端应该尽量避免这样的异常

- [ ] 如果发送的内容不是protobuf，需要怎么处理

> 422 Unprocessable Entity

数据校验错误。当数据需要校验，且校验不通过时，返回该类别错误。

```
{
    type: string,
    field: string,
    message: string
}
```

错误类型：

类型 | 描述
--- | ---
unrecognized_type | 无法识别类型
missing_field | 缺少必要字段
invalid | 校验失败，详细见文档
type_mismatch | 类型不匹配
