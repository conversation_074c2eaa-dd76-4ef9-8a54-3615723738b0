Bees360Ai Backend
====

###### `2019-07-13` `guanrong.yang`  |

Quick Start
---

创建目录
```bash
sudo mkdir -p /var/bees360/
sudo chmod -R 777 /var/bees360/
```

添加必要的环境变量
```bash
# 请确保环境变量添加后立即生效
# 可以将下列环境变量配置到IDE中或者 $HOME/.bashrc中
export ENV="dev" # 开发环境
export BEES360_SECRET_KEY="${SECRET_KEY}" # 请向管理员询问${SECRET_KEY}值
export BEES360_AI_REDIS_HOST="test.bees360.com"
export BEES360_AI_HOST="http://127.0.0.1:9090"
```

启动
```shell script
# 开发环境启动
cd upstream/bees360-backend/
make
# 然后在IDE中启动Bees360AiApplication的main方法
```
## 项目代码结构及依赖关系

### 项目代码结构

### 依赖关系

每个项目模块的描述：
- **bees360-ai-common:** 通用的，不依赖与系统和系统中的实体类的公共工具类。
- **bees360-ai-grpc-proto:** 存放proto文件生成的类和gPRC服务接口了。作为 `bees360-ai-entity` 的上一层是为了方便entity结合proto实体类进行开发。
- **bees360-ai-entity:** 系统实体类，包含数据库映射实体类，前后端交互的视图实体类以及数据传输实体类。
- **bees360-ai-mapper:** 数据库访问层
- **bees360-ai-service:** 业务功能实现层
- **bees360-ai-utils:** `bees360-ai-service` 的工具类集合，仅仅服务于`bees360-ai-service`
- **bees360-ai-web:** REST API接口提供层
- **bees360-ai-grpc:** GRPC 接口提供层
