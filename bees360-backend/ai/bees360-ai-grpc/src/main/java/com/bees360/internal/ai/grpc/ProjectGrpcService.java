package com.bees360.internal.ai.grpc;

import com.bees360.common.protobuf.ProtoBeanUtils;
import com.bees360.internal.ai.grpc.api.web2ai.Project;
import com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectIdOuterClass.ProjectId;
import com.bees360.internal.ai.service.ProjectEsService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class ProjectGrpcService extends ProjectServiceGrpc.ProjectServiceImplBase {

    private final ProjectEsService projectEsService;

    @Override
    public void findById(ProjectId request, StreamObserver<ProjectEsModel> responseObserver) {
        long projectId = request.getProjectId();
        com.bees360.internal.ai.entity.ProjectEsModel esModel = projectEsService.findProjectByProjectId(projectId);
        ProjectEsModel.Builder builder = ProjectEsModel.newBuilder();
        try {
            ProtoBeanUtils.toProtoBean(builder, esModel);
        } catch (IOException e) {
            throw new IllegalStateException("Fail to convert ProjectEsModel to proto object", e);
        }
        ProjectEsModel project = builder.build();
        responseObserver.onNext(project);
        responseObserver.onCompleted();
    }
}
