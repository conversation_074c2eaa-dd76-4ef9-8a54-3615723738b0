package com.bees360.internal.ai.grpc.listener;

import com.bees360.internal.ai.common.grpc.AiGrpcClient;
import com.bees360.internal.ai.event.ProjectReworkEvent;
import com.bees360.internal.ai.grpc.api.ai2web.ProjectsStatusProto.ProjectsReworkRequest;
import com.bees360.internal.ai.grpc.api.ai2web.ProjectsStatusServiceGrpc;
import com.bees360.internal.ai.grpc.api.ai2web.ProjectsStatusServiceGrpc.ProjectsStatusServiceFutureStub;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass.StatusCodeResponse;
import java.util.Objects;
import jakarta.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 将批量更新的project status同步到web端的listener
 *
 * <AUTHOR>
 * @since 2021/4/26
 */
@Slf4j
@Component
public class ProjectsStatusChangedListener {

    @Autowired
    private AiGrpcClient webGrpcClient;

    private ProjectsStatusServiceFutureStub stub;

    @PostConstruct
    public void init() {
        this.stub = ProjectsStatusServiceGrpc.newFutureStub(webGrpcClient.getChannel());
    }

    /**
     * rework项目 同步web
     * @param event rework理由
     */
    @Async
    @EventListener
    public void syncToWebOnProjectReworkEvent(ProjectReworkEvent event) {
        log.info("project rework sync to web projectId {}, event {}", event.getProjectId(), event);
        ProjectsReworkRequest request = ProjectsReworkRequest.newBuilder()
            .setProjectId(event.getProjectId())
            .setTitle(event.getTitle())
            .setContent(event.getContent())
            .build();

        try {
            StatusCodeResponse response = stub.reworkProject(request).get();
            if (response == null || !Objects.equals(response.getStatus(), 0)) {
                log.warn("project rework sync to web failed projectId {}", event.getProjectId());
            }
        } catch (Exception e) {
            log.error("project rework sync to web error projectId {}", event.getProjectId(), e);
        }
    }

}
