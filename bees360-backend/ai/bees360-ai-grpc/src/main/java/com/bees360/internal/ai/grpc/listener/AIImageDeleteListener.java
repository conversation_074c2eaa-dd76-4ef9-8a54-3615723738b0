package com.bees360.internal.ai.grpc.listener;

import com.bees360.event.registry.AIImageDeleteEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.common.grpc.AiGrpcClient;
import com.bees360.internal.ai.grpc.api.ai2web.ImageDeleteProto.ImageDeleted;
import com.bees360.internal.ai.grpc.api.ai2web.ImageDeleteServiceGrpc;
import com.bees360.internal.ai.grpc.api.ai2web.ImageDeleteServiceGrpc.ImageDeleteServiceFutureStub;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass.StatusCodeResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Objects;

/**
 * 监听并处理图片删除事件，通过gRPC服务同步删除图片状态
 */
@Slf4j
@Component
public class AIImageDeleteListener extends AbstractNamedEventListener<AIImageDeleteEvent> {

    @Nullable
    @Override
    public String getName() {
        return "ai_image_delete_listener";
    }

    public AIImageDeleteListener(AiGrpcClient webGrpcClient) {
        this.stub = ImageDeleteServiceGrpc.newFutureStub(webGrpcClient.getChannel());
    }

    private final ImageDeleteServiceFutureStub stub;

    @SneakyThrows
    @Override
    public void handle(AIImageDeleteEvent event) {
        long projectId = event.getProjectId();
        List<String> imageIds = event.getImageIds();
        int deleteStatus = event.getDeleteStatus();
        log.info("syncDeleteImages to web handle projectId {}", event.getProjectId());
        ImageDeleted request =
                ImageDeleted.newBuilder()
                        .setProjectId(projectId)
                        .addAllImageId(imageIds)
                        .setDeleteStatus(deleteStatus)
                        .build();
        StatusCodeResponse response;
        try {
            response = stub.deleteImages(request).get();
        } catch (Exception e) {
            log.error(
                    "syncDeleteImages to web throws exception. projectId:{}, status:{}",
                    projectId,
                    deleteStatus,
                    e);
            throw new IllegalStateException("Call to grpc service failed for " + event.getClass(), e);
        }
        if (Objects.isNull(response) || !Objects.equals(response.getStatus(), 0)) {
            log.error(
                    "syncDeleteImages to web error. projectId:{}, status:{}",
                    projectId,
                    deleteStatus);
        }
        log.info("syncDeleteImages response projectId: {}, status: {}", projectId, deleteStatus);
    }
}
