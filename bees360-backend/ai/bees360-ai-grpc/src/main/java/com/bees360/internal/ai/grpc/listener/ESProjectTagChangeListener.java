package com.bees360.internal.ai.grpc.listener;

import com.bees360.internal.ai.common.grpc.AiGrpcClient;
import com.bees360.internal.ai.event.ProjectTagChangedEvent;
import com.bees360.internal.ai.grpc.api.ai2web.ProjectTagProto.ProjectTagUpdateRequest;
import com.bees360.internal.ai.grpc.api.ai2web.ProjectTagProto.ProjectTagUpdateRequest.Builder;
import com.bees360.internal.ai.grpc.api.ai2web.ProjectTagServiceGrpc;
import com.bees360.internal.ai.grpc.api.ai2web.ProjectTagServiceGrpc.ProjectTagServiceFutureStub;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass.StatusCodeResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
public class ESProjectTagChangeListener {

    @Autowired
    private AiGrpcClient webGrpcClient;

    private ProjectTagServiceFutureStub stub;

    @PostConstruct
    public void init() {
        this.stub = ProjectTagServiceGrpc.newFutureStub(webGrpcClient.getChannel());
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void syncOnTagChanged(ProjectTagChangedEvent event) {

        Long projectId = event.getProjectId();
        List<Long> projectTags = event.getProjectTags();
        log.info("syncTag request start projectId: {}, tags: {}", projectId, projectTags);
        StatusCodeResponse response;
        Builder builder =
                ProjectTagUpdateRequest.newBuilder()
                        .setProjectId(projectId)
                        .setUserId(event.getUserId());
        if (Objects.isNull(projectTags)){
            projectTags = new ArrayList<>();
        }
        builder.addAllTagId(projectTags);
        try {
            response = stub.updateProjectTags(builder.build()).get();
        } catch (Exception e) {
            log.error("syncProjectTagsOnTagChanged throws exception. projectId:" + projectId, e);
            return;
        }
        assert response != null;
        log.info("syncProjectTagsOnTagChanged response for projectId {} response: {} ", projectId, response.toString());
    }

}
