package com.bees360.internal.ai.grpc.service;

import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectClaimTagServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectTag;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass;
import com.bees360.internal.ai.service.ProjectTagService;
import io.grpc.stub.StreamObserver;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Import({ExceptionTranslateInterceptor.class})
@Log4j2
public class ProjectClaimTagService
        extends ProjectClaimTagServiceGrpc.ProjectClaimTagServiceImplBase {

    private final ProjectTagService projectTagService;

    public ProjectClaimTagService(ProjectTagService projectTagService) {
        this.projectTagService = projectTagService;

        log.info("Created '{}(projectTagService={})'", this, this.projectTagService);
    }

    @Override
    public void getTags(
            ProjectTag.ProjectClaimTagRequest request,
            StreamObserver<ProjectTag.ProjectClaimTagResponse> responseObserver) {

        ProjectTag.ProjectClaimTagResponse.Builder builder =
                ProjectTag.ProjectClaimTagResponse.newBuilder();
        List<com.bees360.internal.ai.entity.ProjectTag> tagList =
                projectTagService.getByProjectIds(request.getProjectIdList());
        tagList.forEach(
                tag ->
                        builder.addTag(
                                ProjectTag.ProjectClaimTag.newBuilder()
                                        .setProjectId(tag.getProjectId())
                                        .addAllTags(tag.getTags())
                                        .build()));
        builder.setStatusCodeResponse(
                StatusCodeResponseOuterClass.StatusCodeResponse.newBuilder()
                        .setStatus(0)
                        .setCode("OK"));

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
