package com.bees360.internal.ai.grpc.service;

import com.bees360.internal.ai.common.grpc.AiGrpcClient;
import com.bees360.internal.ai.entity.vo.ProjectRoleWorkVo;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectRoleWorkServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectRoleWorkServiceOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 服务端
 *
 * <AUTHOR>
 * @date 2020-2-18 12:32:50
 */
@Component
@Slf4j
public class GrpcProjectRoleWorkComponent {


    @Autowired
    private AiGrpcClient webGrpcClient;

    private ProjectRoleWorkServiceGrpc.ProjectRoleWorkServiceFutureStub stub;

    @PostConstruct
    public void initStub() {
        this.stub = ProjectRoleWorkServiceGrpc.newFutureStub(webGrpcClient.getChannel());
    }


    public void syncProjectRoleWorkData(ProjectRoleWorkVo roleWork) {
        log.info("syncProjectRoleWorkData start sync, startDate " + roleWork.getStartDate());
        List<ProjectRoleWorkServiceOuterClass.ProjectWorkItem> claimItems = roleWork.getClaimRes()
            .stream().map(o -> {
                return ProjectRoleWorkServiceOuterClass.ProjectWorkItem.newBuilder()
                    .setRoleName(o.getName())
                    .setCount(o.getCount())
                    .build();
            }).collect(Collectors.toList());
        List<ProjectRoleWorkServiceOuterClass.ProjectWorkItem> underItems = roleWork.getUnderRes()
            .stream().map(o -> {
                return ProjectRoleWorkServiceOuterClass.ProjectWorkItem.newBuilder()
                    .setRoleName(o.getName())
                    .setCount(o.getCount())
                    .build();
            }).collect(Collectors.toList());
        ProjectRoleWorkServiceOuterClass.ProjectRoleWorkResult request = ProjectRoleWorkServiceOuterClass.ProjectRoleWorkResult
            .newBuilder().setStartDate(roleWork.getStartDate()).setEndDate(roleWork.getEndData())
            .addAllClaim(claimItems).addAllUnderwriting(underItems)
            .build();
        try {
            StatusCodeResponseOuterClass.StatusCodeResponse statusCodeResponse =
                stub.calAiRoleWorkResult(request).get();
            log.info("make a grpc syncProjectRoleWorkData for startDate " + roleWork.getStartDate());
        } catch (Exception e) {
            log.error(
                "Failed to make a grpc on addAiProjectMessageOnWebChange for startDate " + roleWork.getStartDate(), e);
        }
    }
}
