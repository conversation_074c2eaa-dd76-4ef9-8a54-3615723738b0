package com.bees360.internal.ai.grpc;

import java.util.List;

import com.bees360.common.grpc.GrpcServerInitializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import io.grpc.BindableService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2019/07/13
 */
@Component
@Slf4j
public class AiGrpcServerInitializer extends GrpcServerInitializer implements ApplicationRunner {

    public AiGrpcServerInitializer(List<BindableService> services,
        @Value("${com.bees360.grpc.server.ai.port}") int port,
        @Value("${com.bees360.grpc.server.ai.handshakeTimeout}") int handshakeTimeout) {
        super(services, port);
        super.setHandshakeTimeout(handshakeTimeout);
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        super.run();
    }


    @Override
    protected void showInfo() {
        StringBuilder gprcServerInfo = new StringBuilder();
        gprcServerInfo.append("ai-backend Grpc Server started on port: " + getPort() + "\n");
        gprcServerInfo.append("The following ai-backend Grpc Services is available: \n");
        for (BindableService service : getServices()) {
            gprcServerInfo.append("\t" + service.getClass().getSimpleName() + "\n");
        }
        log.info("ai-backend Gprc Server Info:\n" + gprcServerInfo);
    }
}
