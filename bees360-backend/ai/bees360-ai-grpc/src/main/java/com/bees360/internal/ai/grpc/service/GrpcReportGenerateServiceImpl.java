package com.bees360.internal.ai.grpc.service;

import com.bees360.internal.ai.event.ProjectStatusChangedToReturnToClientEvent;
import com.bees360.internal.ai.event.ProjectReportDataInitializedEvent;
import com.bees360.internal.ai.grpc.api.web2ai.*;
import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceOuterClass.Project;
import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceOuterClass.ReportGenerateServiceRequest;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/07/25
 */

@Slf4j
@Service
public class GrpcReportGenerateServiceImpl extends ReportGenerateServiceGrpc.ReportGenerateServiceImplBase implements ApplicationEventPublisherAware {

    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    /**
     * generate report when bees360's client upload images finished
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void generateReport(ReportGenerateServiceRequest request,
                               StreamObserver<StatusCodeResponseOuterClass.StatusCodeResponse> responseObserver) {
        assert request != null && request.getProject() != null;
        Project project = request.getProject();
        long projectId = project.getProjectId();
        com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel esProject = request.getEsProject();
        log.info("bees360-ai have received generateReport request from bees360-web with projectId {} and status {}, syncPoint:{}",
            project.getProjectId(), project.getProjectStatus(), request.getSyncPoint());
        try {
            applicationEventPublisher.publishEvent(new ProjectReportDataInitializedEvent(this, projectId, project, esProject,
                request.getImageSyncFlag(), request.getSyncPoint()));
            StatusCodeResponseOuterClass.StatusCodeResponse response = StatusCodeResponseOuterClass.StatusCodeResponse
                .newBuilder().setStatus(0).setCode("OK").setMessage("success").build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
            log.info("bees360-ai have finished processing ReportGenerateEvent with projectId {}", projectId);
        } catch (Exception e) {
            responseObserver.onError(e);
            log.error("Failed to generateReport on bees360-ai with projectId " + projectId, e);
            return;
        }
    }

    @Override
    public void changeStatusToReturnToClient(ProjectIdOuterClass.ProjectId request,
                                      StreamObserver<StatusCodeResponseOuterClass.StatusCodeResponse> responseObserver) {
        assert request != null && request.getProjectId() != 0;
        long projectId = request.getProjectId();
        log.info("bees360-ai have received changeStatusToReturnToClient request from bees360-web with projectId {}", projectId);
        try {
            applicationEventPublisher.publishEvent(new ProjectStatusChangedToReturnToClientEvent(this, projectId));
            StatusCodeResponseOuterClass.StatusCodeResponse response = StatusCodeResponseOuterClass.StatusCodeResponse
                .newBuilder().setStatus(0).setCode("OK").setMessage("success").build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
            log.info("bees360-ai have finished processing ProjectStatusChangedToReturnToClientEvent with projectId {}", projectId);
        } catch (Exception e) {
            log.error("Failed to generateReport on bees360-ai with projectId " + request.getProjectId(), e);
            responseObserver.onError(e);
            return;
        }
    }
}
