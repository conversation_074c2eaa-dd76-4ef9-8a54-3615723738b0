package com.bees360.internal.ai.grpc.service;

import static com.bees360.internal.ai.entity.enums.AiBotUserEnum.WEB_NEW_USER_ID;
import static com.bees360.internal.ai.entity.enums.AiBotUserEnum.reloadUserByBot;
import static com.bees360.internal.ai.entity.enums.ClaimTypeEnum.UNDERWRITING_FIRST_INSPECTION;

import com.bees360.entity.enums.ProjectReportRecordEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.entity.enums.AiBotUserEnum;
import com.bees360.internal.ai.entity.enums.ProjectEventTypeEnum;
import com.bees360.internal.ai.entity.enums.ProjectHoverStatusEnum;
import com.bees360.internal.ai.entity.enums.ProjectPlnarStatusEnum;
import com.bees360.internal.ai.entity.enums.ProjectUserFolderTypeEnum;
import com.bees360.internal.ai.event.ProjectHoverStatusChangedEvent;
import com.bees360.internal.ai.event.ProjectImageChangeEvent;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectGenericServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectGenericServiceOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass;
import com.bees360.internal.ai.service.AiUserService;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectFolderService;
import com.bees360.internal.ai.service.ProjectImageService;
import com.bees360.internal.ai.service.ProjectStatusService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.user.User;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class GrpcProjectGenericServiceImpl extends ProjectGenericServiceGrpc.ProjectGenericServiceImplBase {

    @Autowired
    private ProjectStatusService projectStatusService;

    @Autowired
    private ProjectEsService projectEsService;

    @Autowired
    private ProjectImageService projectImageService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private ProjectFolderService projectFolderService;

    @Autowired
    private AiUserService aiUserService;

    @Autowired
    private Bees360FeatureSwitch bees360FeatureSwitch;

    @Override
    public void updateProjectStatusOnWebStatusChange(ProjectGenericServiceOuterClass.ProjectStatusChangeRequest request,
        StreamObserver<StatusCodeResponseOuterClass.StatusCodeResponse> responseObserver) {
        assert request != null && request.getProjectId() != 0;
        long projectId = request.getProjectId();
        int statusCode = request.getStatusCode();
        log.info("updateProjectStatusOnWebStatusChange start, projectId:{}， statusCode:{},"
                + "createdAt:{}, createdBy:{}", projectId, statusCode,
            request.getCreatedAt(), request.getCreatedBy());

        String type = request.getType();
        try {
            ProjectEventTypeEnum eventTypeEnum = ProjectEventTypeEnum.getEnumByType(type);
            if (Objects.equals(eventTypeEnum, ProjectEventTypeEnum.RECOVERED)) {
                projectStatusService.reCoverProjectStatus(reloadUserByBot(WEB_NEW_USER_ID),
                    projectId, statusCode);
            } else if (Objects.equals(eventTypeEnum, ProjectEventTypeEnum.CREATED)) {
                // web创建完project之后先构建project索引，方便兼顾project和image的数据同步以及日志记录
                ProjectEsModel esModel = new ProjectEsModel();
                esModel.setProjectId(projectId);
                esModel.setTimeLines(new ArrayList<>());
                projectEsService.syncToEsFromProjectEsModel(esModel, true);
            } else if (Arrays.asList(ProjectEventTypeEnum.ESTIMATE_COMPLETE.getType(),
                ProjectEventTypeEnum.CLIENT_RECEIVED.getType()).contains(type)
                && StringUtils.isNotBlank(request.getCreatedBy())) {
                String createdBy = StringUtils.join(StringUtils.split(request.getCreatedBy(), " "), "*");
                User user = aiUserService.getUserByUsername(createdBy);
                if (Objects.isNull(user) || AiBotUserEnum.isBotUser(user.getId())) {
                    log.warn("getUserByUsername is null, projectId:{}, username:{}", projectId, createdBy);
                }
                if (user == null) {
                    log.info("Because user from grpc request is null, the {} will be used.", WEB_NEW_USER_ID);
                    user = reloadUserByBot(WEB_NEW_USER_ID);
                }
                projectStatusService.updateStatus(projectId, user, statusCode, request.getCreatedAt());
            } else {
                projectStatusService.updateStatus(projectId, reloadUserByBot(WEB_NEW_USER_ID), statusCode,
                    Objects.equals(request.getCreatedAt(), 0L)? System.currentTimeMillis() : request.getCreatedAt());
            }

            StatusCodeResponseOuterClass.StatusCodeResponse response = StatusCodeResponseOuterClass.StatusCodeResponse
                .newBuilder().setStatus(0).setCode("OK").setMessage("success").build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
            log.info("updateProjectStatusOnWebStatusChange end, projectId:{}， statusCode:{}", projectId, statusCode);
        } catch (ServiceException e) {
            log.warn("updateProjectStatusOnWebStatusChange system record, projectId:{}, status:{}, msg:{}",
                projectId, statusCode, e.getMessage(), e);
            StatusCodeResponseOuterClass.StatusCodeResponse response = StatusCodeResponseOuterClass.StatusCodeResponse
                .newBuilder().setStatus(0).setCode("OK").setMessage("success").build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error(
                "Failed to updateProjectStatusOnWebStatusChange on bees360-ai with projectId " + request.getProjectId(),
                e);
            responseObserver.onError(e);
            return;
        }
    }


    @Override
    public void addProjectMessageOnWebChange(ProjectGenericServiceOuterClass.ProjectMessage request,
        StreamObserver<StatusCodeResponseOuterClass.StatusCodeResponse> responseObserver) {
        try {
            if (request.getContent().contains("hover") && request.getContent().contains("success")) {
                ProjectEsModel model = projectEsService.findProjectByProjectId(request.getProjectId());
                if (Objects.nonNull(model) && Objects
                    .equals(model.getHoverMarkStatus(),
                        com.bees360.internal.ai.entity.enums.ProjectHoverStatusEnum.NONE.getCode())) {
                    model.setHoverMarkStatus(
                            com.bees360.internal.ai.entity.enums.ProjectHoverStatusEnum.ORDERED
                                    .getCode());
                    var esModelUpdater =
                            ProjectEsModelUpdater.toBuilder().setProjectId(request.getProjectId());
                    esModelUpdater.setHoverMarkStatus(
                            com.bees360.internal.ai.entity.enums.ProjectHoverStatusEnum.ORDERED
                                    .getCode());
                    if (StringUtils.isNotBlank(request.getExtra())) {
                        model.setHoverJobId(request.getExtra());
                        esModelUpdater.setHoverJobId(request.getExtra());
                    }
                    if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
                        projectEsService.updatePartial(esModelUpdater.build());
                    } else {
                        projectEsService.syncToEsFromProjectEsModel(model, true);
                    }
                    publisher.publishEvent(
                        new ProjectHoverStatusChangedEvent(this, request.getProjectId(),
                            ProjectHoverStatusEnum.NONE.getCode(),
                            ProjectHoverStatusEnum.ORDERED.getCode()));
                }
            }
            StatusCodeResponseOuterClass.StatusCodeResponse response = StatusCodeResponseOuterClass.StatusCodeResponse
                .newBuilder().setStatus(0).setCode("OK").setMessage("success").build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error(
                "Failed to updateProjectStatusOnWebStatusChange on bees360-ai with projectId " + request.getProjectId(),
                e);
            responseObserver.onError(e);
            return;
        }
    }

    @Override
    public void deleteAiImagesOnWebDeleted(ProjectGenericServiceOuterClass.ImagesDeletedMessage request,
        StreamObserver<StatusCodeResponseOuterClass.StatusCodeResponse> responseObserver) {
        try {
            if (Objects.equals(request.getDeleteStatus(), ProjectImage.COMPLETELY_DELETED)) {
                projectImageService.deleteCompletely(
                        request.getProjectId(), reloadUserByBot(WEB_NEW_USER_ID), request.getImageIdsList());
            } else {
                projectImageService.deleteImages(request.getProjectId(), request.getImageIdsList());
            }
            publisher.publishEvent(
                new ProjectImageChangeEvent(this, request.getProjectId(),
                    com.bees360.internal.ai.entity.enums.LogProjectDataActionEnum.DELETED,
                    reloadUserByBot(WEB_NEW_USER_ID)));

            StatusCodeResponseOuterClass.StatusCodeResponse response = StatusCodeResponseOuterClass.StatusCodeResponse
                .newBuilder().setStatus(0).setCode("OK").setMessage("success").build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error(
                "Failed to updateProjectStatusOnWebStatusChange on bees360-ai with projectId " + request.getProjectId(),
                e);
            responseObserver.onError(e);
            return;
        }
    }

    @Override
    public void updateReportStatusAfterEntryRecordCheck(
        ProjectGenericServiceOuterClass.ReportStatusRecordChange request,
        StreamObserver<StatusCodeResponseOuterClass.StatusCodeResponse>
            responseObserver) {
        assert request != null && request.getProjectId() != 0;
        log.info("updateReportStatusAfterEntryRecordCheck start, projectId:{}, type:{}, status:{}",
            request.getProjectId(), request.getType(), request.getStatus());
        try {
            updateReportStatus(request);
            StatusCodeResponseOuterClass.StatusCodeResponse response = StatusCodeResponseOuterClass.StatusCodeResponse
                .newBuilder().setStatus(0).setCode("OK").setMessage("success").build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error(
                "Failed to updateProjectStatusOnWebStatusChange on bees360-ai with projectId " + request.getProjectId(),
                e);
            responseObserver.onError(e);
            return;
        }
    }

    /**
     * update project report status
     * @param request grpc request
     */
    private void updateReportStatus(ProjectGenericServiceOuterClass.ReportStatusRecordChange request) {
        long projectId = request.getProjectId();
        ProjectEsModel esModel = projectEsService.findProjectByProjectId(projectId);
        if (Objects.isNull(esModel)) {
            log.info("report status change, project not found with projectId " + request.getProjectId());
            return;
        }

        ProjectReportRecordEnum type = ProjectReportRecordEnum.getEnumByType(request.getType());
        switch (type) {
            case HOVER:
                ProjectHoverStatusEnum hoverStatus = ProjectHoverStatusEnum.getEnum(request.getStatus());
                if (Objects.equals(esModel.getHoverMarkStatus(), hoverStatus.getCode())) {
                    log.info("report status change, status not change with projectId " + request.getProjectId());
                    return;
                }
                break;
            case PLNAR:
                ProjectPlnarStatusEnum plnarStatus = ProjectPlnarStatusEnum.getEnum(request.getStatus());
                if (Objects.equals(esModel.getPlnarMarkStatus(), plnarStatus.getCode())) {
                    log.info("report status change, status not change with projectId " + request.getProjectId());
                    return;
                }
                break;
            case PREMIUM_DAMAGE_ASSESSMENT_REPORT:
            case PROPERTY_IMAGE_REPORT:
                ReportGenerationStatusEnum statusEnum = ReportGenerationStatusEnum.getEnum(request.getStatus());
                if (Objects.isNull(statusEnum)) {
                    log.info("report status change, status not found with projectId " + request.getProjectId());
                    return;
                }
                break;
            default:
                log.warn("report status change, type not found with projectId " + request.getProjectId());
                return;
        }

        projectEsService.updateReportStatus(reloadUserByBot(AiBotUserEnum.AI_NEW_USER_ID), projectId,
            request.getStatus(), request.getType());
        log.info("report status change, update successful with projectId " + request.getProjectId());
    }

    @Override
    public void updateProjectTagsOnWebChanged(ProjectGenericServiceOuterClass.ProjectTagChangedRequest request,
        StreamObserver<StatusCodeResponseOuterClass.StatusCodeResponse> responseObserver) {
        List<ProjectGenericServiceOuterClass.ProjectTag> projectTagsList = request.getProjectTagsList();
        for (ProjectGenericServiceOuterClass.ProjectTag projectTag : projectTagsList) {
            var projectId = projectTag.getProjectId();
            var labelList = projectTag.getLabelIdList();

            log.info("update project {} after project label changed to {}", projectId, labelList);
            try {
                ProjectEsModel esModel = projectEsService.findProjectByProjectId(projectId);
                if (Objects.isNull(esModel)) {
                    continue;
                }

                if (!CollectionUtils.isEmpty(labelList) && bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
                    var esModelUpdater =
                        ProjectEsModelUpdater.toBuilder()
                                .setProjectId(projectId)
                                .setProjectTags(labelList)
                                .build();
                    projectEsService.updatePartial(esModelUpdater);
                } else {
                    esModel.setProjectTags(CollectionUtils.isEmpty(labelList) ? null : labelList);
                    projectEsService.syncToEsFromProjectEsModel(esModel, true);
                }

                if (UNDERWRITING_FIRST_INSPECTION.getCode() != esModel.getClaimType()) {
                    //if web has set the tags then transfer case to URGENT folder else remove from it;
                    User user = reloadUserByBot(WEB_NEW_USER_ID);
                    if (!CollectionUtils.isEmpty(labelList)) {
                        projectFolderService.addProjectFolder(user, projectId, ProjectUserFolderTypeEnum.URGENT.getType());
                    } else {
                        projectFolderService.deleteById(user, projectId, ProjectUserFolderTypeEnum.URGENT.getType());
                    }
                }
            } catch (Exception e) {
                log.error("Failed to updateProjectTagsOnWebChanged on bees360-ai with projectId {}",
                    projectId, e);
            }
        }
        responseObserver.onNext(StatusCodeResponseOuterClass.StatusCodeResponse.newBuilder().setStatus(0).setCode("Ok")
            .setMessage("success").build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateProjectInspectionTimeOnWebChanged(ProjectGenericServiceOuterClass.ProjectInspectionTime request,
        StreamObserver<StatusCodeResponseOuterClass.StatusCodeResponse> responseObserver) {
        try {
            projectEsService.updateInspectionTime(request.getProjectId(), request.getInspectionTime());

            StatusCodeResponseOuterClass.StatusCodeResponse response = StatusCodeResponseOuterClass.StatusCodeResponse
                .newBuilder().setStatus(0).setCode("OK").setMessage("success").build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (ServiceException e) {
            log.warn("updateProjectInspectionTimeOnWebChanged system record, projectId:{}, status:{}, msg:{}",
                request.getProjectId(), request.getInspectionTime(), e.getMessage(), e);
            StatusCodeResponseOuterClass.StatusCodeResponse response = StatusCodeResponseOuterClass.StatusCodeResponse
                .newBuilder().setStatus(0).setCode("OK").setMessage("success").build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error(
                "Failed to updateProjectInspectionTimeOnWebChanged on bees360-ai with projectId " + request
                    .getProjectId(),
                e);
            responseObserver.onError(e);
            return;
        }
    }
}
