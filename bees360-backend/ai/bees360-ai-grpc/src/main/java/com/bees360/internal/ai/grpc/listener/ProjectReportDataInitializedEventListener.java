package com.bees360.internal.ai.grpc.listener;

import com.bees360.atomic.RedisLockProvider;
import com.bees360.common.grpc.MessageBeanUtil;
import com.bees360.common.protobuf.ProtoBeanUtils;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.common.msgcode.MsgCodeManager;
import com.bees360.internal.ai.entity.LogEntryDetail;
import com.bees360.internal.ai.entity.MemberInfo;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectStatusVo;
import com.bees360.internal.ai.entity.consts.RedisLockKey;
import com.bees360.internal.ai.entity.dto.ImageRoomDto;
import com.bees360.internal.ai.entity.enums.AiBotUserEnum;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.LogEntryTypeEnum;
import com.bees360.internal.ai.entity.enums.LogProjectDataActionEnum;
import com.bees360.internal.ai.entity.enums.LogProjectDataDetailEnum;
import com.bees360.internal.ai.entity.enums.ProjectSyncPointEnum;
import com.bees360.internal.ai.entity.enums.UserAuthEnum;
import com.bees360.internal.ai.event.ProjectReportDataInitializedEvent;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectImageOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceOuterClass;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectLogService;
import com.bees360.internal.ai.service.ProjectOptionDictService;
import com.bees360.internal.ai.service.entity.ImageSort;
import com.bees360.internal.ai.service.job.IncrementalAdditionImageJob;
import com.bees360.job.JobScheduler;
import com.bees360.policy.Policy;
import com.bees360.project.ProjectIIManager;
import com.google.gson.Gson;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.stream.Stream;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/** the process of */
@Slf4j
@Component
public class ProjectReportDataInitializedEventListener {

    @Autowired private ProjectEsService projectEsService;

    @Autowired private ProjectLogService projectLogService;

    @Autowired private ProjectOptionDictService projectOptionDictService;

    @Autowired private JobScheduler jobScheduler;

    @Autowired private RedisLockProvider projectLockProvider;

    @Autowired private ProjectIIManager projectIIManager;

    @EventListener
    public void transferData(ProjectReportDataInitializedEvent reportGenerateEvent) {
        long projectId = reportGenerateEvent.getProjectId();
        ReportGenerateServiceOuterClass.Project project = reportGenerateEvent.getProject();

        transferDataToEs(reportGenerateEvent.getEsProject(), reportGenerateEvent.getSyncPoint());

        transferData(
                projectId,
                project,
                ProjectSyncPointEnum.getEnumByType(reportGenerateEvent.getSyncPoint()));
        // 添加 同步数据的projectLog
        if (!StringUtils.equals(
                reportGenerateEvent.getSyncPoint(),
                ProjectSyncPointEnum.SITE_INSPECTED.getType())) {
            projectLogService.addLogEntryByLogEntryType(
                    projectId,
                    AiBotUserEnum.WEB_NEW_USER_ID.getCode(),
                    LogEntryTypeEnum.PROJECT_DATA,
                    new LogEntryDetail(
                            LogProjectDataDetailEnum.PROJECT_DATA_SYNC,
                            LogProjectDataActionEnum.TRANSFERRED));
        }
    }

    private void transferDataToEs(
            com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel request,
            String syncPoint) {
        log.info(
                "syncProjectInfoToEs projectId:{}, status:{}, syncPoint:{}",
                request.getProjectId(),
                request.getProjectStatus(),
                syncPoint);
        var lock = projectLockProvider.lock(RedisLockKey.PROJECT_LOCK_KEY + request.getProjectId());
        try {
            if (Objects.equals(request.getProjectId(), 0L)) {
                return;
            }
            ProjectEsModel esModel = ProtoBeanUtils.toPojoBean(ProjectEsModel.class, request);
            Assert.notNull(esModel, "project info not found");

            ProjectEsModel existedModel =
                    projectEsService.findProjectByProjectId(esModel.getProjectId());

            esModel.setServiceType(request.getServiceType());
            esModel.setClaimType(request.getClaimType());
            long curTime = System.currentTimeMillis();
            esModel.setStatusUpdateTime(curTime);

            if (StringUtils.isNotBlank(request.getAdjuster())) {
                MemberInfo memberInfo = new MemberInfo();
                memberInfo.setAuth(UserAuthEnum.EXTERNAL_ADJUSTER.getAuth());
                memberInfo.setId(
                        StringUtils.upperCase(StringUtils.replace(request.getAdjuster(), " ", "")));
                memberInfo.setName(request.getAdjuster());
                memberInfo.setPhone(request.getAdjusterPhone());
                memberInfo.setEmail(request.getAdjusterEmail());
                esModel.setMembers(new ArrayList<>(List.of(memberInfo)));
            }

            if (CollectionUtils.isNotEmpty(request.getExternalMemberList())) {
                request.getExternalMemberList().stream()
                        .filter(
                                member ->
                                        StringUtils.equals(
                                                member.getAuth(),
                                                UserAuthEnum.OPERATIONS_MANAGER.getAuth()))
                        .findFirst()
                        .ifPresent(
                                member -> {
                                    MemberInfo memberInfo = new MemberInfo();
                                    memberInfo.setAuth(UserAuthEnum.OPERATIONS_MANAGER.getAuth());
                                    memberInfo.setName(member.getName());
                                    memberInfo.setPhone(member.getPhone());
                                    memberInfo.setEmail(member.getEmail());
                                    if (CollectionUtils.isEmpty(esModel.getMembers())) {
                                        esModel.setMembers(new ArrayList<>());
                                    }
                                    esModel.getMembers().add(memberInfo);
                                });
            }

            if (Objects.nonNull(existedModel)) {

                if (CollectionUtils.isNotEmpty(existedModel.getTimeLines())) {
                    // ai端的状态与web端保持一致，除非 request.getProjectStatus() > CUSTOMER_CONTACTED 且
                    // existedModel.getProjectStatus() not existed in webStatus
                    // existedModel.getProjectStatus() 不处于封存中
                    NewProjectStatusEnum webStatus =
                            NewProjectStatusEnum.getEnum(existedModel.getProjectStatus());
                    if (request.getProjectStatus()
                                    > AiProjectStatusEnum.CUSTOMER_CONTACTED.getCode()
                            && webStatus == null
                            && !Objects.equals(
                                    AiProjectStatusEnum.PROJECT_ARCHIVED.getCode(),
                                    existedModel.getProjectStatus())) {
                        esModel.setProjectStatus(existedModel.getProjectStatus());
                    } else {
                        // due to the reason, WEB status is 110 but AI status is -1
                        if(Objects.equals(
                            NewProjectStatusEnum.PROJECT_CANCELED.getCode(),
                            request.getProjectStatus())){
                            esModel.setProjectStatus(
                                    NewProjectStatusEnum.PROJECT_CANCELED.getAiCode());
                        }else {
                            esModel.setProjectStatus(request.getProjectStatus());
                        }
                    }
                }

                esModel.setStatusUpdateTime(existedModel.getStatusUpdateTime());
                resetProjectUpdateStatusTime(esModel, existedModel);

                if (CollectionUtils.isNotEmpty(existedModel.getMembers())) {
                    List<MemberInfo> memberInfoList = existedModel.getMembers();
                    if (CollectionUtils.isNotEmpty(esModel.getMembers())) {
                        // 如果有存在eaAdjuster或者om，就删除，保存最新的，防止web端更新
                        memberInfoList.removeIf(
                                o ->
                                        StringUtils.equals(
                                                        o.getAuth(),
                                                        UserAuthEnum.EXTERNAL_ADJUSTER.getAuth())
                                                || StringUtils.equals(
                                                        o.getAuth(),
                                                        UserAuthEnum.OPERATIONS_MANAGER.getAuth()));
                        memberInfoList.addAll(esModel.getMembers());
                    }
                    esModel.setMembers(memberInfoList);
                }
                esModel.setTags(existedModel.getTags());
                if (existedModel.getPlnarMarkStatus() != 0) {
                    esModel.setPlnarMarkStatus(existedModel.getPlnarMarkStatus());
                }
                if (existedModel.getHoverMarkStatus() != 0) {
                    esModel.setHoverMarkStatus(existedModel.getHoverMarkStatus());
                }
                esModel.setDarReportStatus(existedModel.getDarReportStatus());
                esModel.setPirReportStatus(existedModel.getPirReportStatus());
                esModel.setProjectTagList(existedModel.getProjectTagList());
            }
            esModel.setSyncDataTime(curTime);
            if (existedModel != null && !CollectionUtils.isEmpty(existedModel.getTimeLines())) {
                setTimelineEsModel(esModel, existedModel.getTimeLines());
            }

            setPolicyType(esModel);

            projectEsService.syncToEsFromProjectEsModel(esModel, false);
            projectOptionDictService.addByNewProject(esModel);
            log.info("syncProjectInfoToEs esModel:{}, sync end", new Gson().toJson(esModel));
        } catch (Exception e) {
            String message = "Failed to syncProjectInfoToEs with projectId %s";
            message = message.formatted(request.getProjectId());
            throw new ServiceException(MsgCodeManager.SYSTEM.PPC_EXCEPTION.CODE, message, e);
        } finally {
            lock.unlock();
        }
    }

    private void resetProjectUpdateStatusTime(ProjectEsModel esModel, ProjectEsModel existedModel) {
        if (timestampNotEmptyPredicate(esModel.getStatusUpdateTime())) {
            return;
        }
        Long statusTime = null;
        var timeLine = existedModel.getTimeLines();
        if (CollectionUtils.isNotEmpty(timeLine)) {
            statusTime =
                    timeLine.stream()
                            .filter(s -> timestampNotEmptyPredicate(s.getCreatedTime()))
                            .max(Comparator.comparing(ProjectStatusVo::getCreatedTime))
                            .map(ProjectStatusVo::getCreatedTime)
                            .orElse(null);
        }

        statusTime =
                timestampNotEmptyPredicate(statusTime) ? statusTime : existedModel.getCreatedTime();
        esModel.setStatusUpdateTime(statusTime);
    }

    private boolean timestampNotEmptyPredicate(Long timestamp) {
        return timestamp != null && !timestamp.equals(0L);
    }

    private void setPolicyType(ProjectEsModel esModel) {
        var projectII = projectIIManager.findById(String.valueOf(esModel.getProjectId()));
        Optional.ofNullable(projectII.getPolicy()).map(Policy::getType).ifPresent(esModel::setPolicyType);
    }

    private void setTimelineEsModel(
            ProjectEsModel esModel, List<ProjectStatusVo> existedTimelines) {
        var timelinesFromRequest = CollectionUtils.emptyIfNull(esModel.getTimeLines());
        // due to the reason, WEB status is 110 but AI status is -1
        timelinesFromRequest.forEach(
                e -> {
                    var status = e.getStatus();
                    if (status != null
                            && Objects.equals(
                                    NewProjectStatusEnum.PROJECT_CANCELED.getCode(),
                                    status.getCode())) {
                        status.setCode(NewProjectStatusEnum.PROJECT_CANCELED.getAiCode());
                    }
                });
        Function<ProjectStatusVo, String> keyMapper =
                timeline ->
                        Strings.concat(
                                String.valueOf(timeline.getStatus().getCode()),
                                String.valueOf(timeline.getCreatedTime()));
        var timelines =
                Stream.concat(existedTimelines.stream(), timelinesFromRequest.stream())
                        .collect(Collectors.toMap(keyMapper, Function.identity(), (k1, k2) -> k1))
                        .values()
                        .stream()
                        .sorted(Comparator.comparing(ProjectStatusVo::getCreatedTime))
                        .collect(Collectors.toList());
        setLatestStatusToTimelineLastIndex(esModel.getProjectStatus(), timelines);
        esModel.setTimeLines(timelines);
    }

    private void setLatestStatusToTimelineLastIndex(
            int latestStatus, List<ProjectStatusVo> timelines) {
        // set latestStatus to last
        for (int i = timelines.size() - 1; i >= 0; i--) {
            if (timelines.get(i).getStatus().getCode() == latestStatus) {
                var latest = timelines.get(i);
                var oldLatest = timelines.get(timelines.size() - 1);
                timelines.set(i, oldLatest);
                timelines.set(timelines.size() - 1, latest);
                break;
            }
        }
    }

    /**
     * 将数据进行处理并保存到redis
     *
     * @param projectId
     * @param project
     */
    private void transferData(
            long projectId,
            ReportGenerateServiceOuterClass.Project project,
            ProjectSyncPointEnum syncPointEnum) {
        assert projectId != 0 && project != null;
        log.info("Start to transfer project' {}' related data to ai", projectId);

        List<ProjectImage> images = buildProjectImageCaches(projectId, project.getImagesList());
        if (!CollectionUtils.isEmpty(images)) {
            if (syncPointEnum == ProjectSyncPointEnum.IMAGE_UPLOADED) {
                initImageSortV2(images);
            }
            var imageRooms = buildProjectImageRooms(project.getImagesList());
            // save images
            jobScheduler.schedule(
                    new IncrementalAdditionImageJob(
                            projectId,
                            syncPointEnum,
                            project.getServiceType(),
                            images,
                            imageRooms));
        }
        log.info("Success transferred project' {}' related data to ai", projectId);
    }

    private Map<String, ImageRoomDto> buildProjectImageRooms(
            List<ProjectImageOuterClass.ProjectImage> imagesList) {
        return imagesList.stream()
                .filter(
                        image ->
                                !StringUtils.isAllBlank(
                                        image.getRoomName(),
                                        image.getFloorLevel(),
                                        image.getNumber()))
                .collect(
                        Collectors.toMap(
                                ProjectImageOuterClass.ProjectImage::getImageId,
                                image ->
                                        ImageRoomDto.builder()
                                                .roomName(image.getRoomName())
                                                .floorLevel(image.getFloorLevel())
                                                .number(image.getNumber())
                                                .build(),
                                (k1, k2) -> k1));
    }

    private List<ProjectImage> buildProjectImageCaches(
            long projectId, List<ProjectImageOuterClass.ProjectImage> projectImages)
            throws ServiceException {
        if (projectImages == null || projectImages.size() == 0) {
            return null;
        }
        List<ProjectImage> modelProjectImages = new ArrayList<>();
        for (ProjectImageOuterClass.ProjectImage projectImage : projectImages) {
            modelProjectImages.add(buildProjectImageCache(projectId, projectImage));
        }
        return modelProjectImages;
    }

    private ProjectImage buildProjectImageCache(
            long projectId, ProjectImageOuterClass.ProjectImage projectImage)
            throws ServiceException {
        ProjectImage modelProjectImage = new ProjectImage();
        try {
            MessageBeanUtil.copyMessagePropertiesToBean(
                    projectImage.toBuilder(), modelProjectImage);
        } catch (InvocationTargetException | IllegalAccessException | NoSuchMethodException e) {
            String message = "Failed to buildProjectImageCache on projectId " + projectId;
            throw new ServiceException(MsgCodeManager.SYSTEM.SYSTEM_BUSY.CODE, message, e);
        }
        return modelProjectImage;
    }

    private void initImageSortV2(List<ProjectImage> projectImages) {

        projectImages.sort(Comparator.comparingLong(ProjectImage::getShootingTime));
        int imageSize = projectImages.size();

        for (int i = 0; i < imageSize; i++) {
            ProjectImage projectImage = projectImages.get(i);
            int shootingOrder = ImageSort.getMaxShootingOrder() / imageSize * (i + 1);
            ImageSort imageSort = new ImageSort(shootingOrder);
            projectImage.setImageSortV2(imageSort.getImageSort());
        }
    }
}
