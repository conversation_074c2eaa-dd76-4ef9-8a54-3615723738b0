<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.bees360.ai</groupId>
		<artifactId>bees360-ai</artifactId>
		<version>${revision}${changelist}</version>
	</parent>
	<artifactId>bees360-ai-grpc</artifactId>
	<name>bees360-ai-grpc</name>

	<dependencies>
		<dependency>
			<groupId>com.bees360.ai</groupId>
			<artifactId>bees360-ai-service</artifactId>
		</dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-event</artifactId>
            <version>${revision}${changelist}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-grpc-core</artifactId>
        </dependency>
    </dependencies>
</project>
