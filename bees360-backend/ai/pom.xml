<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.bees360</groupId>
		<artifactId>bees360-parent</artifactId>
		<version>${revision}${changelist}</version>
		<relativePath>../parent/pom.xml</relativePath>
	</parent>
	<groupId>com.bees360.ai</groupId>
	<artifactId>bees360-ai</artifactId>
	<name>bees360-ai</name>
	<packaging>pom</packaging>

	<modules>
		<module>bees360-ai-grpc</module>
		<module>bees360-ai-grpc-proto</module>
		<module>bees360-ai-common</module>
		<module>bees360-ai-entity</module>
		<module>bees360-ai-mapper</module>
		<module>bees360-ai-service</module>
		<module>bees360-ai-utils</module>
		<module>bees360-ai-web</module>
        <module>bees360-ai-event</module>
    </modules>

	<properties>
		<bees360-ai.version>${project.version}</bees360-ai.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- [start] modules -->
			<dependency>
				<groupId>com.bees360.ai</groupId>
				<artifactId>bees360-ai-common</artifactId>
				<version>${bees360-ai.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bees360.ai</groupId>
				<artifactId>bees360-ai-entity</artifactId>
				<version>${bees360-ai.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bees360.ai</groupId>
				<artifactId>bees360-ai-mapper</artifactId>
				<version>${bees360-ai.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bees360.ai</groupId>
				<artifactId>bees360-ai-service</artifactId>
				<version>${bees360-ai.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bees360.ai</groupId>
				<artifactId>bees360-ai-utils</artifactId>
				<version>${bees360-ai.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bees360.ai</groupId>
				<artifactId>bees360-ai-web</artifactId>
				<version>${bees360-ai.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bees360.ai</groupId>
				<artifactId>bees360-ai-grpc</artifactId>
				<version>${bees360-ai.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bees360.ai</groupId>
				<artifactId>bees360-ai-grpc-proto</artifactId>
				<version>${bees360-ai.version}</version>
			</dependency>
			<!-- [end] modules -->
            <dependency>
                <groupId>com.bees360.ai</groupId>
                <artifactId>bees360-ai-event</artifactId>
                <version>${bees360-ai.version}</version>
                <scope>compile</scope>
            </dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>com.bees360.common</groupId>
			<artifactId>bees360-common</artifactId>
		</dependency>
        <dependency>
            <groupId>com.bees360.commons</groupId>
            <artifactId>commons-lang</artifactId>
        </dependency>

		<!-- springboot starter -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jetty</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-log4j2</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
		</dependency>
		<!-- springboot -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<!-- Request interface data verification -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<!-- spring boot config file encryption and decryption -->
		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>

		<dependency>
			<groupId>org.mariadb.jdbc</groupId>
			<artifactId>mariadb-java-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-yaml</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>

		<!-- [start] common utils -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
            </exclusions>
		</dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
		<!-- [end] common utils -->

        <!-- powermock for static method -->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
        </dependency>
	</dependencies>
</project>
