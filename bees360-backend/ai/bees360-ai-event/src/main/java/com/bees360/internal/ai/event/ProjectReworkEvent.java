package com.bees360.internal.ai.event;

import java.io.Serial;

import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * 项目rework
 */
@ToString
public class ProjectReworkEvent extends ApplicationEvent {

    @Serial
    private static final long serialVersionUID = -2952371774058767277L;
    private long projectId;

    private String title;

    private String content;

    public ProjectReworkEvent(Object source, long projectId, String title, String content) {
        super(source);
        this.projectId = projectId;
        this.title = title;
        this.content = content;
    }

    public long getProjectId() {
        return projectId;
    }

    public String getTitle() {
        return title;
    }

    public String getContent() {
        return content;
    }

    public ProjectReworkEvent(Object source) {
        super(source);
    }
}
