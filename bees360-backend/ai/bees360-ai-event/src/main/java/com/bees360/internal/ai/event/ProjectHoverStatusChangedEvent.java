package com.bees360.internal.ai.event;

import com.bees360.internal.ai.entity.enums.ProjectHoverStatusEnum;
import org.springframework.context.ApplicationEvent;

/**
 * hover 状态修改事件
 */
public class ProjectHoverStatusChangedEvent extends ApplicationEvent {

    private final long projectId;

    /**
     * @see ProjectHoverStatusEnum
     */
    private final int oldStatus;

    /**
     * @see ProjectHoverStatusEnum
     */
    private final int newStatus;

    public ProjectHoverStatusChangedEvent(Object source, long projectId, int oldStatus, int newStatus) {
        super(source);
        this.projectId = projectId;
        this.oldStatus = oldStatus;
        this.newStatus = newStatus;
    }

    public long getProjectId() {
        return projectId;
    }

    public int getOldStatus() {
        return oldStatus;
    }

    public int getNewStatus() {
        return newStatus;
    }
}
