package com.bees360.internal.ai.event;

import com.bees360.internal.ai.entity.enums.LogProjectDataActionEnum;
import com.bees360.user.User;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2020/11/09 21:11
 */
public class ProjectImageChangeEvent extends ApplicationEvent {


    @Getter
    private Long projectId;

    @Getter
    private LogProjectDataActionEnum actionEnum;

    @Getter
    private User user;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public ProjectImageChangeEvent(Object source) {
        super(source);
    }

    public ProjectImageChangeEvent(Object source, Long projectId, LogProjectDataActionEnum actionEnum, User user) {
        super(source);
        this.projectId = projectId;
        this.actionEnum = actionEnum;
        this.user = user;
    }
}
