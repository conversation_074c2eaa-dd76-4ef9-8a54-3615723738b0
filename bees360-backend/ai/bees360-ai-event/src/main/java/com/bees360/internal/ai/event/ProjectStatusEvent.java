package com.bees360.internal.ai.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;
import org.springframework.lang.NonNull;

public abstract class ProjectStatusEvent extends ApplicationEvent {

    @Getter
    protected long projectId;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public ProjectStatusEvent(@NonNull Object source, @NonNull long projectId) {
        super(source);
        this.projectId = projectId;
    }
}
