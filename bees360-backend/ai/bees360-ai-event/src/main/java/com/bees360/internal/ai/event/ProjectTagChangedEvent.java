package com.bees360.internal.ai.event;

import java.util.List;
import org.springframework.context.ApplicationEvent;

public class ProjectTagChangedEvent extends ApplicationEvent {

    private Long projectId;
    private List<Long> projectTags;

    private String userId;

    public ProjectTagChangedEvent(
            Object source, Long projectId, List<Long> projectTags, String userId) {
        super(source);
        this.projectId = projectId;
        this.projectTags = projectTags;
        this.userId = userId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public List<Long> getProjectTags() {
        return projectTags;
    }

    public String getUserId() {
        return userId;
    }
}
