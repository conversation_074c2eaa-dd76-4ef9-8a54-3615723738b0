package com.bees360.internal.ai.event;

/**
 * define event when project's status is changed to return to client
 */
public class ProjectStatusChangedToReturnToClientEvent extends ProjectStatusEvent {

    /**
     * 接收Listener中需要的处理的参数
     *
     * @param source
     * @param projectId
     */
    public ProjectStatusChangedToReturnToClientEvent(Object source, long projectId) {
        super(source, projectId);
    }
}
