package com.bees360.internal.ai.event;

import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.entity.dto.ImageRoomDto;
import com.bees360.internal.ai.entity.enums.ProjectSyncPointEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/09 21:11
 */
public class AiImageDownloadEvent extends ApplicationEvent {

    private List<ProjectImage> images;

    private Map<String, ImageRoomDto> imageRooms;
    private Long projectId;
    private ProjectSyncPointEnum syncPoint;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public AiImageDownloadEvent(Object source) {
        super(source);
    }

    public AiImageDownloadEvent(Object source, List<ProjectImage> images) {
        super(source);
        this.images = images;
    }

    public AiImageDownloadEvent(Object source, List<ProjectImage> images, Map<String, ImageRoomDto> imageRooms, long projectId, ProjectSyncPointEnum syncPoint) {
        this(source, images);
        this.imageRooms = imageRooms;
        this.projectId = projectId;
        this.syncPoint = syncPoint;
    }

    public List<ProjectImage> getImages() {
        return images;
    }

    public Map<String, ImageRoomDto> getImageRooms() {
        return imageRooms;
    }

    public Long getProjectId() {
        return projectId;
    }

    public ProjectSyncPointEnum getSyncPoint() {
        return syncPoint;
    }
}
