package com.bees360.internal.ai.event;

import com.bees360.internal.ai.grpc.api.web2ai.Project;
import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceOuterClass;
import lombok.Getter;

/**
 * the event that web transfer data to ai
 */
public class ProjectReportDataInitializedEvent extends ProjectStatusEvent {

    @Getter
    private ReportGenerateServiceOuterClass.Project project;

    @Getter
    private com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel esProject;

    @Getter
    private boolean imageSyncFlag;

    @Getter
    private String syncPoint;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source               the object on which the event initially occurred (never {@code null})
     * @param projectId
     */
    public ProjectReportDataInitializedEvent(Object source, long projectId , ReportGenerateServiceOuterClass.Project project,
                                             com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel esProject,
                                             boolean imageSyncFlag) {
        super(source, projectId);
        this.project = project;
        this.esProject = esProject;
        this.imageSyncFlag = imageSyncFlag;
    }

    public ProjectReportDataInitializedEvent(Object source, long projectId, ReportGenerateServiceOuterClass.Project project, Project.ProjectEsModel esProject,
                                             boolean imageSyncFlag, String syncPoint) {
        super(source, projectId);
        this.project = project;
        this.esProject = esProject;
        this.imageSyncFlag = imageSyncFlag;
        this.syncPoint = syncPoint;
    }
}
