package com.bees360.internal.ai.event;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/07/25 15:23
 */
public class ProjectAiStatusChangeEvent extends ProjectStatusEvent {


    @Getter
    private String userId;
    @Getter
    private String username;
    @Getter
    private long createTime;
    @Getter
    private int preStatus;
    @Getter
    private int newStatus;
    /**
     * Create a new ApplicationEvent.
     *
     * @param source    the object on which the event initially occurred (never {@code null})
     * @param projectId
     */
    public ProjectAiStatusChangeEvent(Object source, long projectId, String userId, String username, long createTime,
                                      int preStatus, int newStatus) {
        super(source, projectId);
        this.userId = userId;
        this.username = username;
        this.createTime = createTime;
        this.preStatus = preStatus;
        this.newStatus = newStatus;
    }
}
