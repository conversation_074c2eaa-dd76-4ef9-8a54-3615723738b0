package com.bees360.ai.mapper;

import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.entity.dto.IdValueDto;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.bees360.internal.ai.entity.dto.ImageDeletedDto;
import com.bees360.internal.ai.entity.dto.ImageIdAndCompassDto;
import org.apache.ibatis.annotations.Param;

public interface ProjectImageMapper {

    void saveAll(@Param("projectImages") List<ProjectImage> projectImages);

    /**
     * 对应到ProjectImageMapper的findById的操作
     */
    ProjectImage findById(@Param("imageId") String imageId);

    Set<String> listProjectImageIdsIncludeDeleted(@Param("projectId") long projectId);

    List<ProjectImage> listByIds(@Param("imageIds") List<String> imageIds);

    List<ProjectImage> listImages(@Param("projectId") long projectId, @Param("fileSourceType") Integer fileSourceType,
        @Param("imageIds") List<String> imageIds, @Param("isDeleted") Boolean isDeleted);

    List<ImageDeletedDto> listDeletedImages(
            @Param("projectId") long projectId,
            @Param("fileSourceTypes") List<Integer> fileSourceTypes);

    void updateImageDeletedStatus(@Param("projectId") long projectId, @Param("imageIds") List<String> imageIds,
        @Param("isDeleted") boolean isDeleted);

    void deleteCompletely(@Param("projectId") long projectId, @Param("imageIds") List<String> imageIds);

    void updateImageIn3DModel(@Param("projectId") long projectId, @Param("in3DModel") Integer in3DModel,
        @Param("imageIds") List<String> imageIds);

    void updateImageSort(@Param("projectId") long projectId, @Param("sorts") List<IdValueDto<String, Long>> sorts);

    void updateImagesCompass(
            @Param("projectId") long projectId,
            @Param("imageIdAndCompass") List<ImageIdAndCompassDto> imageIdAndCompassDtos);

    void copyProjectImage(
            @Param("projectId") long projectId, @Param("imageMap") Map<String, String> imageMap);
}
