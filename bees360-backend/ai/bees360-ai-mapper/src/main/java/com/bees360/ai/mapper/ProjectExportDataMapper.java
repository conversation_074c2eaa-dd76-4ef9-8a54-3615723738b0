package com.bees360.ai.mapper;

import com.bees360.internal.ai.entity.ProjectExportData;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectExportDataMapper {

    int insertExportData(@Param("param")ProjectExportData param);

    List<ProjectExportData> getExportData(@Param("relatedId") String relatedId,
        @Param("relatedType") String relatedType);

    /**
     * 不返回text类型的大字段数据
     * @param relatedId
     * @param relatedType
     * @return
     */
    List<ProjectExportData> getSimpleExportData(@Param("relatedId") String relatedId,
        @Param("relatedType") String relatedType);

    ProjectExportData getSimpleExportDataById(@Param("id") long id);

    ProjectExportData getById(@Param("id") long id);

    void updateExportData(@Param("param") ProjectExportData param);

    void deleteExportData(long id);
}
