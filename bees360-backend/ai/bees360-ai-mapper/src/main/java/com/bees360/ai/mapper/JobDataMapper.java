package com.bees360.ai.mapper;

import com.bees360.internal.ai.entity.JobData;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JobDataMapper {

    /** 保存job data数据 */
    void save(@Param("jobData") JobData jobData);

    int saveJobDataIfNotExists(@Param("jobData") JobData jobData);

    /** 根据jobId和jobName查询job data数据 */
    List<JobData> listByJobIdAndName(
            @Param("jobId") String jobId, @Param("jobName") String jobName);

    List<JobData> listUnfinishedJob(
        @Param("maxStartTime") long startTime, @Param("jobTypes") List<Integer> jobTypes);

    List<JobData> listProjectUnfinishedJob(
            @Param("projectId") long projectId,
            @Param("minStartTime") long minStartTime,
            @Param("jobTypes") List<Integer> jobTypes);

    List<JobData> listByProjectIdAndName(
            @Param("projectId") long projectId,
            @Param("jobName") String jobName);

    /** 完成一个job，记录相应数据 */
    void completedJob(
            @Param("jobId") String jobId,
            @Param("jobName") String jobName,
            @Param("status") int status);
}
