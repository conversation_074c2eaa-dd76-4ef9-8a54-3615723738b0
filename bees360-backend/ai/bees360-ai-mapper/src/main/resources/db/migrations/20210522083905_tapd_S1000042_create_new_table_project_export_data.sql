-- migrate:up
create table project_export_data
(
    id           bigint(20) not null auto_increment,
    related_id   varchar(50)     default null comment 'related data id',
    related_type varchar(50)     default null comment 'related data type. eg:[project]',
    data_log     text            default null comment 'the json of data',
    create_time  timestamp  null default current_timestamp comment 'the created time of data',
    update_time  timestamp  null default current_timestamp on update current_timestamp comment 'the updated time of data',
    primary key (id) using btree,
    unique index project_export_data_related_id_type (related_id, related_type) using btree
) ENGINE = InnoDB
  auto_increment = 1
  default charset = utf8
  row_format = dynamic comment = 'exported data of project';

-- migrate:down
drop table project_export_data;
