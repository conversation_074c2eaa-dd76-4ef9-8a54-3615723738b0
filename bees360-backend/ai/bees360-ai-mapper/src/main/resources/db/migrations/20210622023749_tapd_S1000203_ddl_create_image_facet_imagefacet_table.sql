-- migrate:up
CREATE TABLE ProjectImage
(
    image_id                    varchar(64)  NOT NULL,
    file_name                   varchar(256) NOT NULL,
    file_name_lower_resolution  varchar(256) NULL DEFAULT NULL,
    file_name_middle_resolution varchar(256) NULL DEFAULT NULL,
    file_size                   bigint(20) NOT NULL,
    user_id                     bigint(20) NOT NULL,
    upload_time                 bigint(20) NOT NULL,
    original_file_name          varchar(100) NOT NULL,
    file_source_type            int(11) NOT NULL,
    gps_location                point        NOT NULL,
    relative_altitude           double NULL DEFAULT NULL,
    image_height                int(11) NOT NULL,
    image_width                 int(11) NOT NULL,
    project_id                  bigint(20) NOT NULL,
    direction                   int(11) NULL DEFAULT NULL,
    orientation                 tinyint(4) NULL DEFAULT NULL,
    image_type                  int(11) NULL DEFAULT NULL,
    image_category              varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    cam_property_matrix         varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    is_deleted                  tinyint(1) NOT NULL DEFAULT 0,
    manually_annotated          tinyint(1) NOT NULL DEFAULT 0,
    parent_id                   varchar(256) NULL DEFAULT NULL,
    annotation_image            varchar(256) NULL DEFAULT NULL,
    weight                      double       NOT NULL DEFAULT 0 COMMENT 'The weight of the picture.',
    tiff_orientation            int(11) NOT NULL DEFAULT 1,
    shooting_time               bigint(20) NULL DEFAULT 0 COMMENT 'image shooting time',
    partial_type                int(11) NULL DEFAULT 1 COMMENT 'Image Partial View Type, 1:Roof, 2:Elevation, 3:Interior,4:Garage,5:APS,6:Others',
    image_sort                  bigint(20) NULL DEFAULT NULL COMMENT 'Image Sort',
    in_3d_model                 tinyint(1) NULL DEFAULT 0,
    update_time                 bigint(20) NOT NULL DEFAULT 0,
    is_copy                     tinyint(1) NOT NULL DEFAULT 0,
    image_sort_v2               varchar(64) NULL DEFAULT NULL COMMENT 'Image Sort',
    object_tag                  int(11) NULL DEFAULT NULL,
    location_tag                int(11) NULL DEFAULT NULL,
    scope_tag                   int(11) NULL DEFAULT NULL,
    direction_tag               int(11) NULL DEFAULT NULL,
    PRIMARY KEY (image_id) USING BTREE,
    INDEX                       idx_project_id(project_id) USING BTREE
) ENGINE = InnoDB
  default charset = utf8mb4 COLLATE = utf8mb4_unicode_ci
  row_format = dynamic comment = 'image table';

create table ImageFacet
(
    facet_id              int(11) NOT NULL,
    image_id              varchar(64) NOT NULL,
    project_id            bigint(20) NOT NULL,
    path_2d_full          geometry    NOT NULL,
    path_2d_seen          geometry    NOT NULL,
    path_2d_crsp_overview geometry NULL,
    projected_angel       float DEFAULT NULL,
    abs_area_per_pixel    float DEFAULT NULL,
    center_point          point       NOT NULL,
    relevance_score       float       NOT NULL,
    PRIMARY KEY (project_id, image_id, facet_id) USING BTREE,
    SPATIAL               INDEX sidx_path_2d_full(path_2d_full),
    SPATIAL               INDEX sidx_path_2d_seen(path_2d_seen)
) ENGINE = InnoDB
  default charset = utf8mb4 COLLATE = utf8mb4_unicode_ci
  row_format = dynamic comment = 'the relations between facet and image';

create table ProjectFacet
(
    facet_id       int(11) NOT NULL,
    project_id     bigint(20) NOT NULL,
    component_id   int(11) NOT NULL,
    name           varchar(10) NULL DEFAULT NULL,
    created_time   bigint(20) NOT NULL,
    area           float NULL DEFAULT NULL,
    area_unit      varchar(20) NULL DEFAULT NULL,
    pitch          float NULL DEFAULT NULL,
    3d_path        text NULL,
    path_unit      varchar(20) NULL DEFAULT NULL,
    path_type      text NULL,
    shared_facet   text NULL,
    plane_prop     text NULL,
    plane_coef     text NULL,
    is_high_roof   tinyint(1) NULL DEFAULT 0,
    orientation    tinyint(4) NULL DEFAULT NULL,
    damage_percent tinyint(7) NULL DEFAULT NULL,
    PRIMARY KEY (project_id, facet_id) USING BTREE
) ENGINE = InnoDB
  default charset = utf8mb4 COLLATE = utf8mb4_unicode_ci
  row_format = dynamic comment = 'the relations between project and facet';

-- migrate:down
drop table ProjectImage;
drop table ImageFacet;
drop table ProjectFacet;
