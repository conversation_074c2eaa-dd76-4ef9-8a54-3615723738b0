<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.ai.mapper.ProjectImageMapper">

    <resultMap id="baseProjectImageMap" type="com.bees360.internal.ai.entity.ProjectImage">
        <id column="image_id" jdbcType="VARCHAR" property="imageId"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_name_middle_resolution" jdbcType="VARCHAR" property="fileNameMiddleResolution"/>
        <result column="file_name_lower_resolution" jdbcType="VARCHAR" property="fileNameLowerResolution"/>
        <result column="file_size" jdbcType="BIGINT" property="fileSize"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="upload_time" jdbcType="BIGINT" property="uploadTime"/>
        <result column="original_file_name" jdbcType="VARCHAR" property="originalFileName"/>
        <result column="file_source_type" jdbcType="INTEGER" property="fileSourceType"/>
        <result column="gps_location_longitude" jdbcType="DOUBLE" property="gpsLocationLongitude"/>
        <result column="gps_location_latitude" jdbcType="DOUBLE" property="gpsLocationLatitude"/>
        <result column="relative_altitude" jdbcType="DOUBLE" property="relativeAltitude"/>
        <result column="image_height" jdbcType="INTEGER" property="imageHeight"/>
        <result column="image_width" jdbcType="INTEGER" property="imageWidth"/>
        <result column="project_id" jdbcType="INTEGER" property="projectId"/>
        <result column="direction" jdbcType="INTEGER" property="direction"/>
        <result column="orientation" jdbcType="INTEGER" property="orientation"/>
        <result column="image_type" jdbcType="INTEGER" property="imageType"/>
        <result column="image_category" jdbcType="INTEGER" property="imageCategory"/>
        <result column="cam_property_matrix" jdbcType="VARCHAR" property="camPropertyMatrix"/>
        <result column="is_deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="manually_annotated" jdbcType="INTEGER" property="manuallyAnnotated"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="annotation_image" jdbcType="VARCHAR" property="annotationImage"/>
        <result column="weight" jdbcType="DOUBLE" property="weight"/>
        <result column="tiff_orientation" jdbcType="INTEGER" property="tiffOrientation"/>
        <result column="shooting_time" jdbcType="BIGINT" property="shootingTime"/>
        <result column="partial_type" jdbcType="INTEGER" property="partialType"/>
        <result column="image_sort" jdbcType="BIGINT" property="imageSort"/>
        <result column="in_3d_model" jdbcType="INTEGER" property="in3DModel"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="is_copy" jdbcType="INTEGER" property="isCopy"/>
        <result column="image_sort_v2" jdbcType="VARCHAR" property="imageSortV2"/>
        <result column="compass" jdbcType="DOUBLE" property="compass"/>
    </resultMap>

    <resultMap id="deletedImageMap" type="com.bees360.internal.ai.entity.dto.ImageDeletedDto">
        <id column="image_id" jdbcType="VARCHAR" property="imageId"/>
        <result column="is_deleted" jdbcType="INTEGER" property="deleteStatus"/>
    </resultMap>

    <sql id="timeStamp">
        REPLACE(unix_timestamp(current_timestamp(3)),'.','')
    </sql>

    <!-- with cam_property_matrix -->
    <sql id="projectImageSelectColumns">
        pi.image_id, pi.file_name, pi.file_name_lower_resolution, pi.file_name_middle_resolution, pi.file_size, pi.user_id, pi.upload_time,
        pi.original_file_name, pi.file_source_type, ST_X(pi.gps_location) as gps_location_longitude,
        ST_Y(pi.gps_location) as gps_location_latitude, pi.relative_altitude, pi.image_height, pi.image_width, pi.project_id,
        pi.direction, pi.orientation, pi.image_type, pi.image_category, pi.cam_property_matrix, pi.is_deleted, pi.manually_annotated,
        pi.parent_id, pi.annotation_image, pi.weight, pi.tiff_orientation, pi.shooting_time, pi.partial_type, pi.image_sort, pi.in_3d_model,
        pi.update_time, pi.is_copy, pi.image_sort_v2, pi.compass
    </sql>

    <sql id="andInImageIds">
        <if test="imageIds != null and imageIds.size() > 0">
            and image_id in
            <foreach collection="imageIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="andInImageIdsAlias">
        <if test="imageIds != null and imageIds.size() > 0">
            and pi.image_id in
            <foreach collection="imageIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="whereInImageIdsAlias">
        where
        <if test="imageIds != null and imageIds.size() > 0">
            pi.image_id in
        <foreach collection="imageIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        </if>
        <if test="imageIds == null or imageIds.size() == 0">
            1=0
        </if>
    </sql>

    <!-- Completely delete image -->
    <sql id="completelyDelete">-1</sql>
    <sql id="notCompletelyDelete"> and is_deleted <![CDATA[<>]]> <include refid="completelyDelete" /></sql>
    <sql id="notCompletelyDeleteAlias"> and pi.is_deleted <![CDATA[<>]]> <include refid="completelyDelete" /></sql>

    <insert id="saveAll" parameterType="java.util.List">
        insert into ProjectImage (image_id, file_name, file_name_middle_resolution, file_name_lower_resolution,
        file_size, user_id, upload_time, original_file_name, file_source_type,
        gps_location, relative_altitude,
        image_height, image_width, project_id, direction, orientation,
        image_type, cam_property_matrix, is_deleted,
        manually_annotated, parent_id, annotation_image, weight,
        tiff_orientation, shooting_time, partial_type, image_sort, in_3d_model,
        update_time, is_copy, image_sort_v2
        )
        values
        <foreach collection="projectImages" item="item" separator=",">
            (#{item.imageId}, #{item.fileName}, #{item.fileNameMiddleResolution}, #{item.fileNameLowerResolution},
            #{item.fileSize}, #{item.userId}, #{item.uploadTime}, #{item.originalFileName}, #{item.fileSourceType},
            Point(#{item.gpsLocationLongitude}, #{item.gpsLocationLatitude}), #{item.relativeAltitude},
            #{item.imageHeight}, #{item.imageWidth}, #{item.projectId}, #{item.direction}, #{item.orientation},
            #{item.imageType}, #{item.camPropertyMatrix}, #{item.deleted},
            #{item.manuallyAnnotated}, #{item.parentId}, #{item.annotationImage}, #{item.weight},
            #{item.tiffOrientation}, #{item.shootingTime}, #{item.partialType}, #{item.imageSort}, #{item.in3DModel},
            <include refid="timeStamp"/>, #{item.isCopy}, #{item.imageSortV2})
        </foreach>
    </insert>

    <insert id="copyProjectImage">
        INSERT INTO ProjectImage (
        image_id, file_name, file_name_middle_resolution, file_name_lower_resolution,
        file_size, user_id, upload_time, original_file_name, file_source_type,
        gps_location, relative_altitude, image_height, image_width, project_id,
        direction, orientation, image_type, cam_property_matrix, is_deleted,
        manually_annotated, parent_id, annotation_image, weight, tiff_orientation,
        shooting_time, partial_type, image_sort, in_3d_model, update_time,
        is_copy, image_sort_v2
        )
        <foreach collection="imageMap.entrySet()" index="key" item="value" separator=" UNION ALL ">
            SELECT
            #{value} as image_id,
            file_name,
            file_name_middle_resolution,
            file_name_lower_resolution,
            file_size,
            user_id,
            upload_time,
            original_file_name,
            file_source_type,
            gps_location,
            relative_altitude,
            image_height,
            image_width,
            #{projectId} as project_id,
            direction,
            orientation,
            image_type,
            cam_property_matrix,
            is_deleted,
            manually_annotated,
            parent_id,
            annotation_image,
            weight,
            tiff_orientation,
            shooting_time,
            partial_type,
            image_sort,
            in_3d_model,
            update_time,
            is_copy,
            image_sort_v2
            FROM ProjectImage
            WHERE image_id = #{key}
        </foreach>
    </insert>

    <select id="listImages" resultMap="baseProjectImageMap">
        select <include refid="projectImageSelectColumns" />
        from ProjectImage pi
        where pi.project_id = #{projectId}
        <include refid="notCompletelyDeleteAlias" />
        <include refid="andInImageIdsAlias" />
        <if test="fileSourceType != null">
            and pi.file_source_type = #{fileSourceType}
        </if>
        <if test="isDeleted != null">
            and pi.is_deleted = #{isDeleted}
        </if>
    </select>

    <select id="listByIds" resultMap="baseProjectImageMap">
        select <include refid="projectImageSelectColumns" />
        from ProjectImage pi
        <include refid="whereInImageIdsAlias" />
        <include refid="notCompletelyDeleteAlias" />
    </select>

    <select id="listProjectImageIdsIncludeDeleted" resultType="String">
        select image_id from ProjectImage where project_id = #{projectId};
    </select>

    <select id="findById" resultMap="baseProjectImageMap">
        select <include refid="projectImageSelectColumns" />
        from ProjectImage pi
        where pi.image_id = #{imageId} <include refid="notCompletelyDeleteAlias" />
    </select>

    <select id="listDeletedImages" resultMap="deletedImageMap">
        select image_id, is_deleted
        from ProjectImage
        where project_id = #{projectId}
            <if test="fileSourceTypes != null and fileSourceTypes.size() > 0">
                and file_source_type in
                <foreach collection="fileSourceTypes" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            and is_deleted = 1
    </select>

    <update id="updateImageDeletedStatus" parameterType="java.util.List">
        update ProjectImage set is_deleted = #{isDeleted}, update_time = <include refid="timeStamp" />
        where project_id = #{projectId}
        <include refid="andInImageIds" />
    </update>

    <update id="deleteCompletely" parameterType="java.util.List">
        update ProjectImage set is_deleted = <include refid="completelyDelete" />,
        update_time = <include refid="timeStamp" />
        where project_id = #{projectId}
        <include refid="andInImageIds" />
    </update>

    <update id="updateImageIn3DModel" parameterType="java.util.List">
        update ProjectImage
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="in_3d_model=case" suffix="end,">
                <foreach collection="imageIds" item="item" index="index">
                    when image_id=#{item} then #{in3DModel}
                </foreach>
            </trim>
            update_time = <include refid="timeStamp" />
        </trim>
        where image_id in
        <foreach collection="imageIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateImageSort" parameterType="java.util.List">
        update ProjectImage
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="image_sort=case" suffix="end,">
                <foreach collection="sorts" item="item" index="index">
                    <if test="item.value!=null">
                        when image_id=#{item.id} then #{item.value}
                    </if>
                </foreach>
            </trim>
            update_time = <include refid="timeStamp" />
        </trim>
        where project_id = #{projectId} and image_id in
        <foreach collection="sorts" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateImagesCompass" parameterType="java.util.List">
        update ProjectImage
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="compass=case" suffix="end,">
                <foreach collection="imageIdAndCompass" item="item" index="index">
                    when image_id=#{item.imageId} then #{item.compass}
                </foreach>
            </trim>
            update_time = <include refid="timeStamp" />
        </trim>
        where project_id = #{projectId} and image_id in
        <foreach collection="imageIdAndCompass" index="index" item="item" separator="," open="(" close=")">
            #{item.imageId}
        </foreach>
    </update>

</mapper>
