<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.ai.mapper.ProjectExportDataMapper">

	<resultMap id="projectExportDataResultMap"
		type="com.bees360.internal.ai.entity.ProjectExportData">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="related_id" jdbcType="VARCHAR" property="relatedId" />
		<result column="related_type" jdbcType="VARCHAR" property="relatedType" />
		<result column="data_log" jdbcType="VARCHAR" property="dataLog" />
	</resultMap>

    <sql id="simpleProjectExportDataColumns">
        id, related_id, related_type
    </sql>

	<sql id="projectExportDataColumns">
	   id, related_id, related_type, data_log
	</sql>

    <select id="getById" resultMap="projectExportDataResultMap">
    	select * from ProjectExportData where id = #{id};
	</select>

    <select id="getSimpleExportDataById" resultMap="projectExportDataResultMap">
        select <include refid="simpleProjectExportDataColumns"/> from ProjectExportData where id = #{id};
    </select>

    <select id="getExportData" resultMap="projectExportDataResultMap">
        select
        <include refid="projectExportDataColumns"/>
        from ProjectExportData
        <where>
            <if test="relatedType != null and relatedType.length() > 0">
                related_type  = #{relatedType}
            </if>
            <if test="relatedId != null and relatedId.length() > 0">
                and related_id  = #{relatedId}
            </if>
        </where>
    </select>

    <select id="getSimpleExportData" resultMap="projectExportDataResultMap">
        select
        <include refid="simpleProjectExportDataColumns"/>
        from ProjectExportData
        <where>
            <if test="relatedType != null and relatedType.length() > 0">
                related_type  = #{relatedType}
            </if>
            <if test="relatedId != null and relatedId.length() > 0">
                and related_id  = #{relatedId}
            </if>
        </where>
    </select>

    <insert id="insertExportData" useGeneratedKeys="true" keyProperty="id">
        insert into ProjectExportData(related_id, related_type, data_log
        <if test="param.updatedTime != null">
            , update_time
        </if>
        )
        values (#{param.relatedId}, #{param.relatedType}, #{param.dataLog}
        <if test="param.updatedTime != null">
            , #{param.updatedTime}
        </if>
        )
    </insert>

    <update id="updateExportData">
        update ProjectExportData
        set data_log = #{param.dataLog},
        related_id = #{param.relatedId},
        related_type = #{param.relatedType}
        <if test="param.updatedTime != null">
            , update_time = #{param.updatedTime}
        </if>
        where id = #{param.id}
        <if test="param.updatedTime != null">
            and update_time &lt; #{param.updatedTime}
        </if>
    </update>

    <delete id="deleteExportData">
      delete from ProjectExportData
      where id = #{id}
    </delete>
</mapper>
