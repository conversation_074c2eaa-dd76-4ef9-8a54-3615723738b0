<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.ai.mapper.JobDataMapper">

    <resultMap id="baseJobDataMap" type="com.bees360.internal.ai.entity.JobData">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="pipeline_id" jdbcType="BIGINT" property="pipelineId"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="job_id" jdbcType="VARCHAR" property="jobId"/>
        <result column="job_name" jdbcType="VARCHAR" property="jobName"/>
        <result column="job_parameter" jdbcType="LONGVARCHAR" property="jobParameter"/>
        <result column="result_data" jdbcType="VARCHAR" property="resultData"/>
        <result column="job_type" jdbcType="INTEGER" property="jobType"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="retry_count" jdbcType="INTEGER" property="retryCount"/>
        <result column="start_time" jdbcType="BIGINT" property="startTime"/>
        <result column="completed_time" jdbcType="BIGINT" property="completedTime"/>
    </resultMap>

    <sql id="jobDataBaseColumns">
        project_id
        , pipeline_id, user_id, job_id, job_name, job_parameter, result_data,
        job_type, status, retry_count, start_time, completed_time
    </sql>

    <sql id="timeStamp">
        REPLACE
        (unix_timestamp(current_timestamp(3)),'.','')
    </sql>

    <sql id="selectBase">
        select id,
        <include refid="jobDataBaseColumns"/>
        from JobData
    </sql>

    <sql id="jobInProcess">4</sql>

    <insert id="save" parameterType="com.bees360.internal.ai.entity.JobData">
        insert into JobData (<include refid="jobDataBaseColumns"/>)
        values (#{jobData.projectId}, #{jobData.pipelineId}, #{jobData.userId}, #{jobData.jobId},
        #{jobData.jobName}, #{jobData.jobParameter}, #{jobData.resultData}, #{jobData.jobType},
        #{jobData.status}, #{jobData.retryCount}, #{jobData.startTime}, #{jobData.completedTime})
    </insert>

    <insert id="saveJobDataIfNotExists" parameterType="com.bees360.internal.ai.entity.JobData">
        insert into JobData (<include refid="jobDataBaseColumns"/>)
        select #{jobData.projectId}, #{jobData.pipelineId}, #{jobData.userId}, #{jobData.jobId},
        #{jobData.jobName}, #{jobData.jobParameter}, #{jobData.resultData}, #{jobData.jobType},
        #{jobData.status}, #{jobData.retryCount}, #{jobData.startTime}, #{jobData.completedTime}
        where NOT EXISTS (
            SELECT 1 FROM JobData WHERE project_id = #{jobData.projectId}
                                        AND job_name = #{jobData.jobName}
                                        AND job_type = #{jobData.jobType}
        );
    </insert>

    <select id="listByJobIdAndName" resultMap="baseJobDataMap">
        <include refid="selectBase"/>
        where job_id = #{jobId} and job_name = #{jobName} order by start_time desc;
    </select>

    <select id="listUnfinishedJob" resultMap="baseJobDataMap">
        <include refid="selectBase"/>
        where status = <include refid="jobInProcess"/>
        <if test="jobTypes != null and jobTypes.size() > 0">
            and job_type in
            <foreach collection="jobTypes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and start_time <![CDATA[<]]> #{maxStartTime};
    </select>

    <select id="listProjectUnfinishedJob" resultMap="baseJobDataMap">
        <include refid="selectBase"/>
        where status = <include refid="jobInProcess"/>
        and project_id = #{projectId}
        <if test="jobTypes != null and jobTypes.size() > 0">
            and job_type in
            <foreach collection="jobTypes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and start_time <![CDATA[>=]]> #{minStartTime};
    </select>

    <update id="completedJob">
        update JobData set status = #{status},
        completed_time =
        <include refid="timeStamp"/>
        where job_id = #{jobId} and job_name = #{jobName};
    </update>

    <select id="listByProjectIdAndName" resultMap="baseJobDataMap">
        <include refid="selectBase"/>
        where project_id = #{projectId} and job_name = #{jobName}
        order by start_time desc;
    </select>
</mapper>
