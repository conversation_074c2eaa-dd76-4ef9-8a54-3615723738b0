<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bees360.ai</groupId>
        <artifactId>bees360-ai</artifactId>
        <version>${revision}${changelist}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bees360-ai-service</artifactId>

    <dependencies>
        <!-- First Party Library -->
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-mapper</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-event</artifactId>
        </dependency>

        <!-- Second Party Library -->
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-activity-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-report-mapper</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-job-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-event-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-atomic-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-atomic-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-pipeline-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-pipeline-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-report-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-imaging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-image-job</artifactId>
            <version>${bees360-solid.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-job-config</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-mail-api</artifactId>
        </dependency>
    </dependencies>
</project>
