package com.bees360.internal.ai.service.listener;

import com.bees360.ai.mapper.ProjectImageMapper;
import com.bees360.event.registry.ImageAdded;
import com.bees360.image.Message;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.entity.consts.AiBotUser;
import com.bees360.internal.ai.entity.enums.FileSourceTypeEnum;
import com.bees360.internal.ai.entity.enums.ImageTypeEnum;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.util.Resources;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.time.Instant;
import java.util.List;

@ExtendWith(MockitoExtension.class)
public class SaveThirdPartyImageListenerTest {
    @Mock private ProjectImageMapper projectImageMapper;

    private SaveThirdPartyImageListener listener;

    @Mock private ResourcePool resourcePool;

    @BeforeEach
    public void init() {
        this.listener =
                new SaveThirdPartyImageListener(
                        "third_party/(?<projectId>.*)/hosta_floorplan/(?<resourceName>.*)",
                        projectImageMapper, resourcePool);
    }

    @Test
    public void saveThirdPartyImageTest() throws IOException {
        Mockito.when(resourcePool.get(Mockito.anyString())).thenReturn(Resources.fromFile(ResourceUtils.getFile("classpath:1234_Mobile_Image_Elevation_front_001.png")));

        var event = new ImageAdded();
        var projectId = randomId();
        var resourceName = randomResourceName();
        event.setId(randomId());
        event.setCategory(getCategory(projectId, resourceName));
        event.setUpdatedBy(RandomStringUtils.randomAlphabetic(8));
        event.setGetUrl(RandomStringUtils.randomAlphanumeric(8));
        event.setCreatedBy(RandomStringUtils.randomAlphabetic(5));
        listener.handle(event);

        Mockito.verify(projectImageMapper, Mockito.times(1))
               .saveAll(List.of(generateImage(event.getId(), projectId, resourceName)));
    }

    @Test
    public void saveNotThirdPartyImageTest() throws IOException {
        var event = new ImageAdded();
        event.setId(randomId());
        event.setCategory(RandomStringUtils.randomAlphanumeric(15));
        event.setUpdatedBy(RandomStringUtils.randomAlphabetic(8));
        event.setGetUrl(RandomStringUtils.randomAlphanumeric(8));
        event.setCreatedBy(RandomStringUtils.randomAlphabetic(5));
        listener.handle(event);

        Mockito.verify(projectImageMapper, Mockito.times(0)).saveAll(Mockito.any());
    }

    public static String randomId() {
        return RandomStringUtils.randomNumeric(3);
    }

    private String randomResourceName() {
        return RandomStringUtils.randomAlphanumeric(8) + ".jpg";
    }

    private String getCategory(String projectId, String resourceName) {
        return "third_party/" + projectId + "/hosta_floorplan/" + resourceName;
    }

    private String getImageResourceKey(
            String imageId, Message.ImageMessage.Resource.Type imageType) {
        return "image/" + imageId + "/" + imageType.getNumber();
    }

    private ProjectImage generateImage(String imageId, String projectId, String resourceName) {
        ProjectImage image = ProjectImage.generateDefaultImage();
        image.setImageId(imageId);
        image.setUserId(AiBotUser.AI_ID);
        image.setFileName(getImageResourceKey(imageId, Message.ImageMessage.Resource.Type.LARGE));
        image.setOriginalFileName(
                getImageResourceKey(imageId, Message.ImageMessage.Resource.Type.ORIGIN));
        image.setFileNameMiddleResolution(
                getImageResourceKey(imageId, Message.ImageMessage.Resource.Type.MIDDLE));
        image.setFileNameLowerResolution(
                getImageResourceKey(imageId, Message.ImageMessage.Resource.Type.SMALL));
        image.setOriginalFileName(resourceName);
        image.setProjectId(Long.parseLong(projectId));
        image.setImageType(ImageTypeEnum.OVERVIEW.getCode());
        image.setFileSourceType(FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode());
        image.setShootingTime(Instant.now().toEpochMilli());
        return image;
    }
}
