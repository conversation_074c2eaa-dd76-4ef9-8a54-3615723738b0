package com.bees360.internal.ai.service.impl;

import com.bees360.atomic.Lock;
import com.bees360.atomic.LockExpiredException;
import com.bees360.atomic.RedisLockProvider;
import com.bees360.common.protobuf.ProtoBeanUtils;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.internal.ai.entity.MemberInfo;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.consts.RedisLockKey;
import com.bees360.internal.ai.entity.enums.UserAuthEnum;
import com.bees360.internal.ai.exchange.ai2client.DashBoardProto;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.internal.ai.service.listener.PipelineTaskOwnerChangedListener;
import com.bees360.user.Message;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.google.protobuf.ByteString;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TestTaskOwnerChangedTest {
    @Mock private ProjectEsService projectEsService;
    @Mock private UserProvider userProvider;
    private PipelineTaskOwnerChangedListener projectTaskOwnerChangedListener;
    @Mock private RedisLockProvider projectLockProvider;

    private final Map<String, String> taskAuthMap =
            Map.of(
                    "producer",
                    UserAuthEnum.ROLE_PRODUCER.getAuth(),
                    "processor",
                    UserAuthEnum.ROLE_PROCESSOR.getAuth());

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        projectTaskOwnerChangedListener =
                new PipelineTaskOwnerChangedListener(
                        projectEsService,
                        userProvider,
                        taskAuthMap,
                        projectLockProvider,
                        () -> "10000",
                        new Bees360FeatureSwitch());
    }

    @Test
    public void testChangeProcessorMember() throws Exception {
        var pipelineId = RandomUtils.nextLong();
        var ownerId = RandomStringUtils.randomAlphabetic(6);
        var userName = RandomStringUtils.randomAlphabetic(6);
        var taskList = List.of("processor", "producer");
        var taskDefKey = taskList.get(RandomUtils.nextInt(0, 1));
        var auth = taskAuthMap.get(taskDefKey);
        var event = createEvent(ownerId, pipelineId, taskDefKey);
        User user =
                User.from(
                        Message.UserMessage.newBuilder().setId(ownerId).setName(userName).build());

        MemberInfo paramPojo = createMemberInfo(ownerId, userName, auth);
        ProjectEsModel esModel = createProjectEsModel(ownerId, auth, false);
        Mockito.when(
                        projectEsService.changeDashBoardProjectMembers(
                                user, pipelineId, paramPojo, false))
                .thenReturn(true);
        Mockito.when(userProvider.getUser(ownerId))
                .thenReturn(
                        User.from(
                                Message.UserMessage.newBuilder()
                                        .setId(ownerId)
                                        .setName(userName)
                                        .build()));
        Mockito.when(projectEsService.updatePartialReturningIfChanged(Mockito.any())).thenReturn(true);
        Mockito.when(projectEsService.findProjectByProjectId(Mockito.anyLong()))
                .thenReturn(esModel);
        Mockito.when(projectLockProvider.lock(RedisLockKey.PROJECT_LOCK_KEY + pipelineId))
                .thenReturn(new TestLock(String.valueOf(pipelineId)));
        // Assert listener can take the event
        projectTaskOwnerChangedListener.execute(event);
        Mockito.verify(projectEsService, Mockito.times(1))
                .changeDashBoardProjectMembers(
                        Mockito.any(), Mockito.anyLong(), Mockito.any(), Mockito.anyBoolean());
    }

    @Test
    public void testChangeProcessorMemberWhenMemberExisted() throws Exception {
        var pipelineId = RandomUtils.nextLong();
        var ownerId = RandomStringUtils.randomAlphabetic(6);
        var userName = RandomStringUtils.randomAlphabetic(6);
        var taskList = List.of("processor", "producer");
        var taskDefKey = taskList.get(RandomUtils.nextInt(0, 1));
        var auth = taskAuthMap.get(taskDefKey);
        PipelineTaskChanged event = createEvent(ownerId, pipelineId, taskDefKey);

        User user =
                User.from(
                        Message.UserMessage.newBuilder().setId(ownerId).setName(userName).build());

        MemberInfo paramPojo = createMemberInfo(ownerId, userName, auth);
        ProjectEsModel esModel = createProjectEsModel(ownerId, auth, true);
        Mockito.when(
                        projectEsService.changeDashBoardProjectMembers(
                                user, pipelineId, paramPojo, false))
                .thenReturn(true);
        Mockito.when(userProvider.getUser(ownerId))
                .thenReturn(
                        User.from(
                                Message.UserMessage.newBuilder()
                                        .setId(ownerId)
                                        .setName(userName)
                                        .build()));
        Mockito.when(projectEsService.findProjectByProjectId(Mockito.anyLong()))
                .thenReturn(esModel);
        Mockito.when(projectLockProvider.lock(RedisLockKey.PROJECT_LOCK_KEY + pipelineId))
                .thenReturn(new TestLock(String.valueOf(pipelineId)));

        // Assert listener can take the event
        projectTaskOwnerChangedListener.execute(event);
        Mockito.verify(projectEsService, Mockito.times(0))
                .changeDashBoardProjectMembers(
                        Mockito.any(), Mockito.anyLong(), Mockito.any(), Mockito.anyBoolean());
    }

    private MemberInfo createMemberInfo(String userId, String userName, String auth)
            throws IOException {
        var param =
                DashBoardProto.MemberInfo.newBuilder()
                        .setId(userId)
                        .setName(userName)
                        .setAuth(auth)
                        .build();
        return ProtoBeanUtils.toPojoBean(MemberInfo.class, param);
    }

    private ProjectEsModel createProjectEsModel(String userId, String auth, Boolean existed) {
        ProjectEsModel esModel = new ProjectEsModel();
        ArrayList<MemberInfo> memberInfoList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        if (existed) {
            memberInfo.setId(userId);
            memberInfo.setAuth(auth);
            memberInfoList.add(memberInfo);
            esModel.setMembers(memberInfoList);
        }
        return esModel;
    }

    private PipelineTaskChanged createEvent(String userId, long pipelineId, String taskDefKey) {
        PipelineTaskChanged event = new PipelineTaskChanged();
        var state = new PipelineTaskChanged.State();
        state.setOwnerId(userId);
        event.setPipelineId(String.valueOf(pipelineId));
        event.setState(state);
        event.setTaskDefKey(taskDefKey);
        event.setUpdatedBy("10000");
        return event;
    }

    static class TestLock implements Lock {
        private final String id;

        TestLock(String id) {
            this.id = id;
        }

        @Override
        public String getNamespace() {
            return null;
        }

        @Override
        public String getId() {
            return id;
        }

        @Override
        public void unlock() throws LockExpiredException {}

        @Override
        public ByteString toByteString() {
            return null;
        }
    }
}
