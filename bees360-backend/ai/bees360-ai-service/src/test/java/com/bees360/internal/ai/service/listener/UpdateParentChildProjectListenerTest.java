package com.bees360.internal.ai.service.listener;

import com.bees360.event.registry.ProjectParentChildGroupUpdatedEvent;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.service.ProjectEsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.time.Instant;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class UpdateParentChildProjectListenerTest {

    @Mock
    private ProjectEsService projectEsService;

    private UpdateParentChildProjectListener listener;

    @BeforeEach
    public void setUp() {
        listener = new UpdateParentChildProjectListener(projectEsService);
    }

    @Test
    public void testHandleParentProjectCreated() throws IOException {
        // Arrange
        String projectId = "12345";
        String groupKey = "12345"; // Same as projectId for parent project
        boolean isDeleted = false;

        ProjectParentChildGroupUpdatedEvent event = new ProjectParentChildGroupUpdatedEvent(
            projectId,
            groupKey,
            "GROUP_PARENT_CHILD",
            "10000",
            Instant.now().toString(),
            isDeleted
        );

        ArgumentCaptor<ProjectEsModelUpdater> updaterCaptor = ArgumentCaptor.forClass(ProjectEsModelUpdater.class);

        // Act
        listener.handle(event);

        // Assert
        verify(projectEsService).updatePartial(updaterCaptor.capture());
        ProjectEsModelUpdater updater = updaterCaptor.getValue();

        assertEquals(Long.parseLong(projectId), updater.getProjectId());
        assertTrue(updater.getIsParentProject());
    }

    @Test
    public void testHandleParentProjectDeleted() throws IOException {
        // Arrange
        String projectId = "12345";
        String groupKey = "12345"; // Same as projectId for parent project
        boolean isDeleted = true;

        ProjectParentChildGroupUpdatedEvent event = new ProjectParentChildGroupUpdatedEvent(
            projectId,
            groupKey,
            "GROUP_PARENT_CHILD",
            "10000",
            Instant.now().toString(),
            isDeleted
        );

        ArgumentCaptor<ProjectEsModelUpdater> updaterCaptor = ArgumentCaptor.forClass(ProjectEsModelUpdater.class);

        // Act
        listener.handle(event);

        // Assert
        verify(projectEsService).updatePartial(updaterCaptor.capture());
        ProjectEsModelUpdater updater = updaterCaptor.getValue();

        assertEquals(Long.parseLong(projectId), updater.getProjectId());
        assertFalse(updater.getIsParentProject());
    }

    @Test
    public void testHandleChildProjectCreated() throws IOException {
        // Arrange
        String projectId = "12345";
        String groupKey = "67890"; // Different from projectId for child project
        boolean isDeleted = false;

        ProjectParentChildGroupUpdatedEvent event = new ProjectParentChildGroupUpdatedEvent(
            projectId,
            groupKey,
            "GROUP_PARENT_CHILD",
            "10000",
            Instant.now().toString(),
            isDeleted
        );

        ArgumentCaptor<ProjectEsModelUpdater> updaterCaptor = ArgumentCaptor.forClass(ProjectEsModelUpdater.class);

        // Act
        listener.handle(event);

        // Assert
        verify(projectEsService).updatePartial(updaterCaptor.capture());
        ProjectEsModelUpdater updater = updaterCaptor.getValue();

        assertEquals(Long.parseLong(projectId), updater.getProjectId());
        assertTrue(updater.getIsChildProject());
    }

    @Test
    public void testHandleChildProjectDeleted() throws IOException {
        // Arrange
        String projectId = "12345";
        String groupKey = "67890"; // Different from projectId for child project
        boolean isDeleted = true;

        ProjectParentChildGroupUpdatedEvent event = new ProjectParentChildGroupUpdatedEvent(
            projectId,
            groupKey,
            "GROUP_PARENT_CHILD",
            "10000",
            Instant.now().toString(),
            isDeleted
        );

        ArgumentCaptor<ProjectEsModelUpdater> updaterCaptor = ArgumentCaptor.forClass(ProjectEsModelUpdater.class);

        // Act
        listener.handle(event);

        // Assert
        verify(projectEsService).updatePartial(updaterCaptor.capture());
        ProjectEsModelUpdater updater = updaterCaptor.getValue();

        assertEquals(Long.parseLong(projectId), updater.getProjectId());
        assertFalse(updater.getIsChildProject());
    }
}
