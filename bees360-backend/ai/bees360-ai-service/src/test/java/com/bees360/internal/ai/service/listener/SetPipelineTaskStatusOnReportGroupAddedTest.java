package com.bees360.internal.ai.service.listener;

import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.report.Report;
import com.bees360.report.ReportProvider;
import com.bees360.report.ReportResource;
import com.bees360.report.Message.ReportMessage.Resource.Type;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SetPipelineTaskStatusOnReportGroupAddedTest {

    @Mock private PipelineService pipelineService;

    @Mock private ReportProvider reportProvider;

    @InjectMocks private SetPipelineTaskStatusOnReportGroupAdded listener;

    private ReportGroupAdded validEvent;

    private final String reportId = "report-123";

    private final String projectId = "456";

    @BeforeEach
    void setUp() {
        validEvent = new ReportGroupAdded();
        validEvent.setId("testReportGroupId");
        validEvent.setReportId(reportId);
        validEvent.setGroupType(DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE);
        validEvent.setGroupKey(projectId);
        validEvent.setCreatedBy("123");
    }

    @Test
    void handleWhenInvalidGroupTypeShouldSkip() throws IOException {
        var invalidEvent = new ReportGroupAdded();
        invalidEvent.setId("testReportGroupId");
        invalidEvent.setReportId(reportId);
        invalidEvent.setGroupType("INVALID_TYPE");
        invalidEvent.setGroupKey(projectId);
        invalidEvent.setCreatedBy("123");

        listener.handle(invalidEvent);

        verifyNoInteractions(pipelineService, reportProvider);
    }

    @Test
    void handleWhenValidEventShouldSetTaskToDone() throws IOException {
        Report mockReport = mock(Report.class);
        when(reportProvider.get(reportId)).thenReturn(mockReport);
        when(mockReport.getType())
                .thenReturn(ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT.getShortCut());
        ReportResource mockReportResource = mock(ReportResource.class);
        when(mockReport.getResources()).thenAnswer(e -> List.of(mockReportResource));
        when(mockReportResource.getType()).thenReturn(Type.ORIGIN);

        listener.handle(validEvent);

        verify(pipelineService)
                .setTaskStatus(projectId, "generate_fur", Message.PipelineStatus.DONE);
    }
}
