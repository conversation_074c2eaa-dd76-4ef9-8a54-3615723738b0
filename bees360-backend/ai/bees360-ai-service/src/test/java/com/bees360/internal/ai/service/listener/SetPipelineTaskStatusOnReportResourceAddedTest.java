package com.bees360.internal.ai.service.listener;

import static com.bees360.report.Message.ReportMessage.Resource.Type.ORIGIN_VALUE;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.registry.ReportResourceAdded;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.report.Report;
import com.bees360.report.ReportProvider;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;

@ExtendWith(MockitoExtension.class)
public class SetPipelineTaskStatusOnReportResourceAddedTest {

    @Mock private ProjectReportManager projectReportManager;

    @Mock private PipelineService pipelineService;

    @Mock private ReportProvider reportProvider;

    @InjectMocks private SetPipelineTaskStatusOnReportResourceAdded listener;

    private final String reportId = "123";
    private final String pipelineId1 = "456";
    private final String pipelineId2 = "789";
    private ReportResourceAdded validEvent;
    private Report mockReport;

    @BeforeEach
    void setUp() {
        validEvent = new ReportResourceAdded();
        validEvent.setId(reportId);
        validEvent.setReportType(ReportTypeEnum.INVOICE.getShortCut());
        validEvent.setResourceType(ORIGIN_VALUE);
        mockReport = mock(Report.class);
    }

    @Test
    void handleWhenUnsupportedReportTypeShouldSkip() throws IOException {
        ReportResourceAdded invalidEvent = new ReportResourceAdded();
        invalidEvent.setId(reportId);
        invalidEvent.setReportType(ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT.getShortCut());
        invalidEvent.setResourceType(ORIGIN_VALUE);

        listener.handle(invalidEvent);

        verifyNoInteractions(projectReportManager, pipelineService, reportProvider);
    }

    @Test
    void handleWhenUnsupportedResourceTypeShouldSkip() throws IOException {
        ReportResourceAdded invalidEvent = new ReportResourceAdded();
        invalidEvent.setId(reportId);
        invalidEvent.setReportType(ReportTypeEnum.INVOICE.getShortCut());
        invalidEvent.setResourceType(2);

        listener.handle(invalidEvent);

        verifyNoInteractions(projectReportManager, pipelineService, reportProvider);
    }

    @Test
    void handleWhenNonOriginResourceTypeShouldSkip() throws IOException {
        ReportResourceAdded invalidEvent = new ReportResourceAdded();
        invalidEvent.setId(reportId);
        invalidEvent.setReportType(ReportTypeEnum.INVOICE.getShortCut());
        invalidEvent.setResourceType(2);

        listener.handle(invalidEvent);

        verifyNoInteractions(projectReportManager, pipelineService, reportProvider);
    }

    @Test
    void handleWhenConditionsMetShouldSetTaskStatus() throws IOException {
        when(reportProvider.get(reportId)).thenReturn(mockReport);
        when(mockReport.getType()).thenReturn("INV");
        when(projectReportManager.findProjectId(reportId))
                .thenReturn(Arrays.asList(pipelineId1, pipelineId2));

        listener.handle(validEvent);

        verify(pipelineService)
                .setTaskStatus(pipelineId1, "generate_inv", Message.PipelineStatus.DONE);
        verify(pipelineService)
                .setTaskStatus(pipelineId2, "generate_inv", Message.PipelineStatus.DONE);
    }

    @Test
    void handleWhenNoProjectsFoundShouldNotSetTaskStatus() throws IOException {
        when(reportProvider.get(reportId)).thenReturn(mockReport);
        when(mockReport.getType()).thenReturn("INV");
        when(projectReportManager.findProjectId(reportId)).thenReturn(Collections.emptyList());

        listener.handle(validEvent);

        verifyNoInteractions(pipelineService);
    }
}
