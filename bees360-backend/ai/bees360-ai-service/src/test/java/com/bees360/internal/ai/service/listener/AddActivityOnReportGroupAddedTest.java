package com.bees360.internal.ai.service.listener;

import com.bees360.activity.ActivityManager;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.report.Report;
import com.bees360.report.ReportProvider;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AddActivityOnReportGroupAddedTest {

    @Mock private ActivityManager activityManager;

    @Mock private ReportProvider reportProvider;

    @InjectMocks private AddActivityOnReportGroupAdded listener;

    private final String reportId = "123";

    private final String projectId = "456";

    private ReportGroupAdded validEvent;

    @BeforeEach
    void setUp() {
        validEvent = new ReportGroupAdded();
        validEvent.setId("testReportGroupId");
        validEvent.setReportId(reportId);
        validEvent.setGroupType(DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE);
        validEvent.setGroupKey(projectId);
        validEvent.setCreatedBy("789");
    }

    @Test
    void handleWhenInvalidGroupTypeShouldSkip() throws IOException {
        var invalidEvent = new ReportGroupAdded();
        invalidEvent.setId("testReportGroupId");
        invalidEvent.setReportId(reportId);
        invalidEvent.setGroupType("INVALID_TYPE");
        invalidEvent.setGroupKey(projectId);
        invalidEvent.setCreatedBy("123");

        listener.handle(invalidEvent);

        verifyNoInteractions(activityManager, reportProvider);
    }

    @Test
    void handleWhenValidEventShouldSubmitActivity() throws IOException {
        Report mockReport = mock(Report.class);
        when(reportProvider.get(reportId)).thenReturn(mockReport);
        when(mockReport.getType())
                .thenReturn(ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT.getShortCut());

        listener.handle(validEvent);

        Mockito.verify(activityManager, Mockito.times(1)).submitActivity(any());
    }
}
