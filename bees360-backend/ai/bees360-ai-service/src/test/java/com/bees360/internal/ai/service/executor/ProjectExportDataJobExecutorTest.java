package com.bees360.internal.ai.service.executor;

import com.bees360.internal.ai.service.ProjectExportDataManager;
import com.bees360.job.registry.ProjectExportDataJob;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.util.Resources;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import org.springframework.util.ResourceUtils;

import java.io.IOException;

import static com.bees360.internal.ai.service.listener.SaveThirdPartyImageListenerTest.randomId;
import static org.mockito.ArgumentMatchers.any;


@ExtendWith(MockitoExtension.class)
public class ProjectExportDataJobExecutorTest {

	@Mock
	private ResourcePool resourcePool;

	@Mock
	private ProjectExportDataManager compositedProjectExportDataManager;

	private ProjectExportDataJobExecutor executor;

	@BeforeEach
	public void init() {
		this.executor =
				new ProjectExportDataJobExecutor(resourcePool, compositedProjectExportDataManager);
	}

	@Test
	public void testProjectExportDataJobExecutor() throws IOException {
		var projectId = randomId();
		var resourceKey = "test_export_data_resource_key";

		Mockito.when(resourcePool.get(resourceKey)).thenReturn(Resources.fromFile(ResourceUtils.getFile("classpath:test_export_data.json")));

		var job = new ProjectExportDataJob(projectId, resourceKey);
		executor.handle(job);

		Mockito.verify(compositedProjectExportDataManager, Mockito.times(1))
				.insertOrUpdateData(any());
	}

}
