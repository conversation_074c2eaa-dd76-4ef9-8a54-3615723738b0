package com.bees360.job.registry;

import com.bees360.internal.ai.service.ProjectImageService;
import com.bees360.internal.ai.service.job.SyncImageToMysqlAfterImageImported;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.job.util.InMemoryJobScheduler;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Map;
@SpringBootTest
@Disabled
public class SyncImageToMysqlJobExecutorTest {
    @Configuration
    @Import({
        InMemoryJobScheduler.class,
        AutoRegisterJobExecutorConfig.class,
    })
    static class Config {
        @MockBean ProjectImageService projectImageService;

        @Bean
        SyncImageToMysqlAfterImageImported syncImageToMysqlAfterImageImported(
                ProjectImageService projectImageService) {
            return new SyncImageToMysqlAfterImageImported(projectImageService);
        }
    }

    @Autowired ProjectImageService projectImageService;
    @Autowired JobScheduler jobScheduler;

    @Test
    void testSyncToMysqlAfterProjectImageCopied() {
        var projectId = "123000";
        var imageMap = Map.of("originalImageId", "newImageId");
        var job = new ProjectImageCopiedJob(projectId, imageMap);
        jobScheduler.schedule(Job.ofPayload(job));

        Mockito.verify(projectImageService, Mockito.times(1)).copyProjectImage(123000L, imageMap);
    }
}
