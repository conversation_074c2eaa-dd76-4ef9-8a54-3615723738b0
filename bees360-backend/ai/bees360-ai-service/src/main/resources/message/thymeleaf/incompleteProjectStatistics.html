<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>infoAdminRoleWorkWeeklyCount</title>
</head>

<body style="margin: 0; padding: 0;">
<table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
        <td>
            <span>Dear Admin</span>,<br/><br/>
        </td>
    </tr>
    <table align="left" border="0" cellpadding="0" cellspacing="0" width="1100" style="border-collapse: collapse;">

        <tr>
            <td>
                <table style="border:1px solid #000; border-collapse:collapse;" width="1100">
                    <tr style="border:1px solid #000;">
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;">Pilot</th>
                        <th style="border:1px solid #000;">Assignment Type</th>
                        <th style="border:1px solid #000;">Drone</th>
                        <th style="border:1px solid #000;">Mobile</th>
                        <th style="border:1px solid #000;">Hover</th>
                        <th style="border:1px solid #000;">Company</th>
                        <th style="border:1px solid #000;">Address</th>
                    </tr>
                    <tr style="border:1px solid #000;text-align:center;" th:each="claim: ${projectList}">
                        <td style="border:1px solid #000;" th:utext="${claim.projectId}"></td>
                        <td style="border:1px solid #000;" th:utext="${claim.pilot}"></td>
                        <td style="border:1px solid #000;" th:utext="${claim.assignmentType}"></td>
                        <td style="border:1px solid #000;" th:utext="${claim.drone}"></td>
                        <td style="border:1px solid #000;" th:utext="${claim.mobile}"></td>
                        <td style="border:1px solid #000;" th:utext="${claim.hover}"></td>
                        <td style="border:1px solid #000;" th:utext="${claim.company}"></td>
                        <td style="border:1px solid #000;" th:utext="${claim.address}"></td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr>
            <td><br/></td>
        </tr>
        <tr>
            <td>
                <span th:include="_footer.html :: footer">FOOTER</span>
            </td>
        </tr>
    </table>
    </td>
    </tr>
</table>
</body>
</html>
