<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Reviewer Team Workload Statistics</title>
</head>

<body style="margin: 0; padding: 0;">
<table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
        <td>
    <tr>
        <td>
            Dear Admin</span>,<br/><br/>
        </td>
    </tr>
    <tr>
        <td>
            This is the statistics table for reviewer of underwriting type：
            <br/>
        </td>
    </tr>
    <br/>
    <table align="left" border="0" cellpadding="0" cellspacing="0" width="600" style="border-collapse: collapse;">

        <tr>
            <td>
                <table style="border:1px solid #000; border-collapse:collapse;" width="600">
                    <tr style="border:1px solid #000;">
                        <th style="border:1px solid #000;">Name</th>
                        <th style="border:1px solid #000; white-space: nowrap;">&nbsp;Insured by&nbsp;</th>
                        <th style="border:1px solid #000; white-space: nowrap;">&nbsp;# of Roof-only&nbsp;</th>
                        <th style="border:1px solid #000; white-space: nowrap;">&nbsp;# of Exterior&nbsp;</th>
                        <th style="border:1px solid #000; white-space: nowrap;">&nbsp;# of 4-Point&nbsp;</th>
                        <th style="border:1px solid #000; white-space: nowrap;">&nbsp;# of Premium 4-Point&nbsp;</th>
                        <th style="border:1px solid #000; white-space: nowrap;">&nbsp;# of Total&nbsp;</th>
                    </tr>
                    <tr style="border:1px solid #000;text-align:center;" th:each="stat: ${uwStatistics}">
                        <td style="border:1px solid #000;" th:utext="${stat.staffName}">name</td>
                        <td style="border:1px solid #000;" th:utext="${stat.insuredCompanyName}">0</td>
                        <td style="border:1px solid #000;" th:utext="${stat.roofOnly}">0</td>
                        <td style="border:1px solid #000;" th:utext="${stat.exterior}">0</td>
                        <td style="border:1px solid #000;" th:utext="${stat.fourPoint}">0</td>
                        <td style="border:1px solid #000;" th:utext="${stat.premiumFourPoint}">0</td>
                        <td style="border:1px solid #000;" th:utext="${stat.total}">0</td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td><br/></td>
        </tr>

    </table>
    <br />
    </td>
    </tr>
</table>
</body>
</html>
