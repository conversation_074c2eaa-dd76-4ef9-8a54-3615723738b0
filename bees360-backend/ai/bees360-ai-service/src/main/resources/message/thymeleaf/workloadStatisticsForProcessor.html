<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
</head>

<body style="margin: 0; padding: 0;">
<table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
        <td><br/></td>
    </tr>
    <table align="left" border="0" cellpadding="0" cellspacing="0" width="1100" style="border-collapse: collapse;">
        <tr>
            <td>Underwriting Project Workload Statistics</td>
        </tr>
        <tr>
            <td>
                <table style="border:1px solid #000; border-collapse:collapse;" width="1100">
                    <tr style="border:1px solid #000;">
                        <th style="border:1px solid #000;">Name</th>
                        <th style="border:1px solid #000;">Insured By</th>
                        <th style="border:1px solid #000;"># Of Reports Generated</th>
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;"># Of Premium-4-Point Reports Generated</th>
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;"># Of Reports Submitted</th>
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;"># Of Premium-4-Point Reports Submitted</th>
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;"># Of Reports Approved</th>
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;"># Of Premium-4-Point Reports Approved</th>
                        <th style="border:1px solid #000;">Project ID</th>
                    </tr>
                    <tr style="border:1px solid #000;text-align:center;" th:each="stat: ${uwStatistics}">
                        <td style="border:1px solid #000;" th:utext="${stat.staffName}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.companyName}"></td>
                        <td style="border:1px solid #000;"><span th:utext="${stat.generated}"></span></td>
                        <td style="border:1px solid #000;"><span th:utext="${stat.generatedProjects}"></span></td>
                        <td style="border:1px solid #000;"><span th:utext="${stat.generatedPremium}"></span></td>
                        <td style="border:1px solid #000;"><span th:utext="${stat.generatedPremiumProjects}"></span></td>
                        <td style="border:1px solid #000;"><span th:utext="${stat.submitted}"></span></td>
                        <td style="border:1px solid #000;"><span th:utext="${stat.submittedProjects}"></span></td>
                        <td style="border:1px solid #000;"><span th:utext="${stat.submittedPremium}"></span></td>
                        <td style="border:1px solid #000;"><span th:utext="${stat.submittedPremiumProjects}"></span></td>
                        <td style="border:1px solid #000;"><span th:utext="${stat.approved}"></span></td>
                        <td style="border:1px solid #000;"><span th:utext="${stat.approvedProjects}"></span></td>
                        <td style="border:1px solid #000;"><span th:utext="${stat.approvedPremium}"></span></td>
                        <td style="border:1px solid #000;"><span th:utext="${stat.approvedPremiumProjects}"></span></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td><br/></td>
        </tr>
        <tr>
            <td>Claim Project Workload Statistics</td>
        </tr>
        <tr>
            <td>
                <table style="border:1px solid #000; border-collapse:collapse;" width="1100">
                    <tr style="border:1px solid #000;">
                        <th style="border:1px solid #000;">Name</th>
                        <th style="border:1px solid #000;">Insured By</th>
                        <th style="border:1px solid #000;"># Of DAR Submitted</th>
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;"># Of PIR Submitted</th>
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;"># Of 3D Submitted</th>
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;"># Of DAR Approved</th>
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;"># Of PIR Approved</th>
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;"># Of Hail Report Uploaded</th>
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;"># Of Projection Fixed</th>
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;"># Of PIR Annotated</th>
                        <th style="border:1px solid #000;">Project ID</th>
                    </tr>
                    <tr style="border:1px solid #000;text-align:center;" th:each="stat: ${claimStatistics}">
                        <td style="border:1px solid #000;" th:utext="${stat.staffName}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.companyName}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.darSubmitted}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.darSubmittedProjects}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.pirSubmitted}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.pirSubmittedProjects}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.threeDSubmitted}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.threeDSubmittedProjects}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.darApproved}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.darApprovedProjects}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.pirApproved}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.pirApprovedProjects}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.hailReportUploaded}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.hailReportUploadedProjects}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.projectionFixed}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.projectionFixedProjects}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.annotationCompleted}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.annotationCompletedProjects}"></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td><br/></td>
        </tr>
        <tr>
            <td>
                <span th:include="_project_team_footer.html :: footer">FOOTER</span>
            </td>
        </tr>
    </table>
    </td>
    </tr>
</table>
</body>
</html>
