<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
</head>

<body style="margin: 0; padding: 0;">
<table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
        <td>
            <span>Dear <span th:text="${statistics[0].staffName}"></span></span>,<br/><br/>
        </td>
    </tr>
    <tr>
        <td>
            <b>This is a summary of your work this week</b>
        </td>
    </tr>
    <tr>
        <td><br/></td>
    </tr>
    <table align="left" border="0" cellpadding="0" cellspacing="0" width="1100" style="border-collapse: collapse;">

        <tr>
            <td>
                <table style="border:1px solid #000; border-collapse:collapse;" width="1100">
                    <tr style="border:1px solid #000;">
                        <th style="border:1px solid #000;">DAR Completed</th>
                        <th style="border:1px solid #000;">Estimate Completed</th>
                    </tr>
                    <tr style="border:1px solid #000;text-align:center;" th:each="stat: ${statistics}">
                        <td style="border:1px solid #000;" th:utext="${stat.darCount}"></td>
                        <td style="border:1px solid #000;" th:utext="${stat.estimateCount}"></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td><br/></td>
        </tr>
        <tr>
            <td><br/></td>
        </tr>
        <tr>
            <td>
                <table style="border:1px solid #000; border-collapse:collapse;" width="1100">
                    <tr style="border:1px solid #000;">
                        <th style="border:1px solid #000;">Project ID</th>
                        <th style="border:1px solid #000;">DAR</th>
                        <th style="border:1px solid #000;">Estimate</th>
                    </tr>
                    <tr style="border:1px solid #000;text-align:center;" th:each="stat: ${detailStatistics}">
                        <td style="border:1px solid #000;" th:utext="${stat.projectId}"></td>
                        <td style="border:1px solid #000;">
                            <span th:if="${stat.darCount > 0}">✓</span>
                        </td>
                        <td style="border:1px solid #000;">
                            <span th:if="${stat.estimateCount > 0}">✓</span>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td><br/></td>
        </tr>
        <tr>
            <td><b>Thank you for your hard work!</b></td>
        </tr>
        <tr>
            <td><br/></td>
        </tr>
        <tr>
            <td>
                <span th:include="_claim_team_footer.html :: footer">FOOTER</span>
            </td>
        </tr>
    </table>
    </td>
    </tr>
</table>
</body>
</html>
