package com.bees360.internal.ai.service.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.function.Supplier;

@Configuration
public class SystemUserConfig {

    @Bean("systemUserSupplier")
    public Supplier<String> systemUserSupplier(
            @Value("${bees360.system-user:10000}") String systemUser) {
        return () -> systemUser;
    }
}
