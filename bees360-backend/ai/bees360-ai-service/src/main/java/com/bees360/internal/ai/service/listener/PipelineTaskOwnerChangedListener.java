package com.bees360.internal.ai.service.listener;

import com.bees360.atomic.Lock;
import com.bees360.atomic.RedisLockProvider;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.internal.ai.entity.MemberInfo;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.consts.RedisLockKey;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.listener.Listener;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CancellationException;
import java.util.function.Supplier;

import static com.bees360.entity.User.AI_ID;

@Log4j2
public class PipelineTaskOwnerChangedListener implements Listener<PipelineTaskChanged> {

    private final ProjectEsService projectEsService;
    private final UserProvider userProvider;
    private final Map<String, String> taskOwner2MemberRoleMap;
    private final RedisLockProvider projectLockProvider;
    private final Supplier<String> systemUserSupplier;
    private final Bees360FeatureSwitch bees360FeatureSwitch;

    public PipelineTaskOwnerChangedListener(
        @NonNull ProjectEsService projectEsService,
        @NonNull UserProvider userProvider,
        @NonNull Map<String, String> taskOwner2MemberRoleMap,
        @NonNull RedisLockProvider projectLockProvider,
        @NonNull Supplier<String> systemUserSupplier,
        @NonNull Bees360FeatureSwitch bees360FeatureSwitch) {
        this.projectEsService = projectEsService;
        this.userProvider = userProvider;
        this.taskOwner2MemberRoleMap = taskOwner2MemberRoleMap;
        this.projectLockProvider = projectLockProvider;
        this.systemUserSupplier = systemUserSupplier;
        this.bees360FeatureSwitch = bees360FeatureSwitch;
        log.info("Created {}(projectEsService={}, userProvider={}, taskOwner2MemberRoleMap={}, " +
                "projectLockProvider={}, systemUserSupplier={}, bees360FeatureSwitch={}).",
                this,
                this.projectEsService,
                this.userProvider,
                this.taskOwner2MemberRoleMap,
                this.projectLockProvider,
                this.systemUserSupplier,
                this.bees360FeatureSwitch);
    }

    @Override
    public void execute(PipelineTaskChanged event) throws IOException {
        var systemId = systemUserSupplier.get();
        log.info("Set Member for pipeline task owner changed event:{}, systemId:{}", event, systemId);
        if (bees360FeatureSwitch.isEnableSkipSetMemberOnSystemSetTaskOwner()
                && !StringUtils.equals(event.getUpdatedBy(), systemId)) {
            return;
        }
        var projectId = Long.parseLong(event.getPipelineId());
        var userId = event.getState().getOwnerId();
        var taskDefKey = event.getTaskDefKey();
        final String EMPTY_AUTH = "EMPTY";
        var memberAuth = taskOwner2MemberRoleMap.getOrDefault(taskDefKey, EMPTY_AUTH);
        log.info(
                "Received pipeline task changed event in project :{} and task :{}, start to update member :{}",
                projectId,
                taskDefKey,
                userId);
        if (userId == null) {
            // 更新后的user为空，不处理这种情况
            log.info("The status user is null, not sync to es in project :{}", projectId);
            return;
        }
        var user = userProvider.getUser(userId);
        var defaultUser = userProvider.getUser(String.valueOf(AI_ID));
        var oldOwnerId =
                Optional.ofNullable(event.getOldState())
                        .map(PipelineTaskChanged.State::getOwnerId)
                        .orElse(null);
        var memberInfo = setMemberInfo(user, memberAuth);
        // 获取redis锁，防止因为没有获取锁导致es无法更新
        Lock lock = null;
        try {
            lock =
                    projectLockProvider.lock(
                            RedisLockKey.PROJECT_LOCK_KEY + projectId, Duration.ofMinutes(2));
            ProjectEsModel projectEsModel = projectEsService.findProjectByProjectId(projectId);
            if (projectEsModel == null) {
                // 当io端创建项目时会自动设置部分task owner为开发组人员并触发pipeline_changed.task_owner_changed
                // 但此时项目尚未同步至es库，这种情况不对es进行更新操作
                // TODO 这个地方后续考虑更改为通过定时任务批量设置这些默认的Owner。解决在项目刚刚创建时Owner无法同步至es的问题。
                log.info("Project :{} has not synced to es ", projectId);
                return;
            }
            // 开始设置project member，若auth不存在则直接跳过
            if (StringUtils.equals(memberAuth, EMPTY_AUTH)) {
                return;
            }
            // 已存在的projectMembers
            Set<MemberInfo> memberInfoSet =
                    new HashSet<>(
                            Optional.ofNullable(projectEsModel.getMembers())
                                    .orElse(new ArrayList<>()));
            var memberExisted =
                    memberInfoSet.stream()
                            .anyMatch(
                                    o ->
                                            StringUtils.equals(o.getId(), userId)
                                                    && StringUtils.equals(o.getAuth(), memberAuth));
            // 若该角色用户或更新前任务的用户与该用户相同则不执行此次分配
            if (memberExisted || (oldOwnerId != null && oldOwnerId.equals(userId))) {
                return;
            }
            boolean result =
                    projectEsService.changeDashBoardProjectMembers(
                            defaultUser, projectId, memberInfo, false);
            if (!result) {
                log.info(
                        "Unable to updated project member in project :{} in task :{} and member :{}.",
                        projectId,
                        taskDefKey,
                        userId);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new CancellationException(
                    "Fail to update es project when pipeline task owner changed in project :"
                            + projectId
                            + " with exception :"
                            + e.getMessage());
        } finally {
            Optional.ofNullable(lock).ifPresent(Lock::unlock);
        }
    }

    private MemberInfo setMemberInfo(User user, String memberAuth) {
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setAuth(memberAuth);
        memberInfo.setName(user.getName());
        memberInfo.setId(user.getId());
        memberInfo.setEmail(user.getEmail());
        memberInfo.setPhone(user.getPhone());
        var photoUrl = user.getPhoto();
        if (photoUrl != null) {
            var photo = photoUrl.toString();
            memberInfo.setAvatarUrl(photo);
            memberInfo.setPhoto(photo);
        }
        return memberInfo;
    }
}
