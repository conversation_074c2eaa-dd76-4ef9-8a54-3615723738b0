package com.bees360.event;

import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.image.ImageTagEnum;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.service.ProjectImageService;
import com.bees360.job.CommandJob;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.util.EventTriggeredJob;
import com.bees360.pipeline.Message;
import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.enums.AnnotationUsageTypeEnum;
import com.bees360.report.mapper.ImageAnnotationMapper;
import lombok.NonNull;

import java.time.Duration;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 当pipeline中任务ai_tag_images为READY状态时，发起job为图片打Tag
 */
public class AutoTagImagesOnPipelineTaskReady extends EventTriggeredJob<PipelineTaskChanged> {

    private final ProjectImageService projectImageService;
    private final ImageAnnotationMapper imageAnnotationMapper;

    private static final String PIPELINE_TASK_KEY = "ai_tag_images";
    private static final String TAG_IMAGE_COMMAND = "python3 /usr/automl/model_predict.py @model ./ @imgIds %s @imgPaths %s > output.json";
    private static final List<Integer> OBJECT_TAG_CODE = List.of(ImageTagEnum.BASKETBALL_HOOP.getCode(), ImageTagEnum.TRAMPOLINE.getCode(),
        ImageTagEnum.PLAY_OR_SWING_SET.getCode(), ImageTagEnum.DOCK.getCode(), ImageTagEnum.ELEVATED_DECK.getCode());
    private static final List<Integer> ANNOTATION_TAG_CODE = List.of(ImageTagEnum.BBQ_GRILL.getCode(), ImageTagEnum.OUTDOOR_FURNITURE.getCode());


    public AutoTagImagesOnPipelineTaskReady(JobScheduler jobScheduler, ProjectImageService projectImageService, ImageAnnotationMapper imageAnnotationMapper) {
        super(jobScheduler);
        this.projectImageService = projectImageService;
        this.imageAnnotationMapper = imageAnnotationMapper;
    }

    @Override
    protected Job convert(PipelineTaskChanged event) {
        var imageList = getImagesNeededTag(Long.parseLong(event.getPipelineId()));

        var command = CommandJob.newBuilder();
        command.setName("M-automl-scope-tag-v1").addInputArgument("M-automl-scope-tag-v1", "saved_model.pb");
        command.setId(event.getPipelineId());

        StringBuilder imgIds = new StringBuilder();
        StringBuilder imgPaths = new StringBuilder();
        int index = 1;
        for (ProjectImage image : imageList) {
            command.addInputArgument(image.getFileName());
            imgIds.append(" ").append(image.getImageId());
            imgPaths.append(" ./{").append(index++).append("}");
        }

        String bashCommand = TAG_IMAGE_COMMAND.formatted(imgIds, imgPaths);

        CommandJob job = command.setBashCommand(bashCommand).build();
        return RetryableJob.of(job, 4, Duration.ofSeconds(5), 2F);
    }

    // get all image that need to be tagged by Ai. Including annotation tag and object tag.
    private List<ProjectImage> getImagesNeededTag(long projectId) {
        var imageAnnotations = imageAnnotationMapper.listByProjectIdAndUsageType(projectId, AnnotationUsageTypeEnum.COMPONENT.getCode());
        var componentImageIds =
            imageAnnotations.stream()
                .filter(e -> ANNOTATION_TAG_CODE.contains(e.getAnnotationType()))
                .map(ImageAnnotation::getImageId)
                .distinct()
                .collect(Collectors.toList());

        var imageListWithoutDeleted = projectImageService.listImagesWithoutDeleted(projectId);
        var objectImageIds = imageListWithoutDeleted.stream()
            .filter(e -> e.getObjectTag() != null)
            .filter(e -> OBJECT_TAG_CODE.contains(e.getObjectTag()))
            .map(ProjectImage::getImageId)
            .collect(Collectors.toList());

        var idSet = new HashSet<String>();
        idSet.addAll(componentImageIds);
        idSet.addAll(objectImageIds);

        return imageListWithoutDeleted.stream().filter(e -> idSet.contains(e.getImageId())).collect(Collectors.toList());
    }

    protected boolean filter(@NonNull PipelineTaskChanged event) {
        return PIPELINE_TASK_KEY.equals(event.getTaskDefKey())
            && Objects.equals(event.getState().getStatus(), Message.PipelineStatus.READY);
    }

}
