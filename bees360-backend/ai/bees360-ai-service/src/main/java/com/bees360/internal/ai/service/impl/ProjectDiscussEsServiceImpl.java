package com.bees360.internal.ai.service.impl;

import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.activity.Message;
import com.bees360.commons.elasticsearchsupport.ElasticSearchHelper;
import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.common.msgcode.MsgCodeManager;
import com.bees360.internal.ai.entity.DiscussionEntry;
import com.bees360.internal.ai.entity.DiscussionParticipant;
import com.bees360.internal.ai.entity.MemberInfo;
import com.bees360.internal.ai.entity.ProjectDiscuss;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.enums.AiBotUserEnum;
import com.bees360.internal.ai.entity.enums.DashBoardRoleTagToDoEnum;
import com.bees360.internal.ai.entity.enums.UserAuthEnum;
import com.bees360.internal.ai.exchange.ai2client.ProjectDiscussionProto;
import com.bees360.internal.ai.service.AiUserService;
import com.bees360.internal.ai.service.ProjectDiscussService;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.user.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/07/24 17:40
 */
@Slf4j
@Service
public class ProjectDiscussEsServiceImpl implements ProjectDiscussService {
    public static final String ACTIVITY_SOURCE_AI = "AI";
    @Autowired
    private ElasticSearchHelper elasticSearchHelper;

    @Autowired
    private ProjectEsService projectEsService;

    @Autowired
    private AiUserService aiUserService;
    @Autowired
    private CommentManager commentManager;
    @Override
    public String addProjectDiscussion(User user, long projectId, String creatorId, String note, List<DiscussionParticipant> discussMembers) {
        return addDiscuss(user, projectId, creatorId, aiUserService.getUsernameById(creatorId), note, discussMembers);
    }

    private String addDiscuss(User user, long projectId, String creatorId, String creatorName, String note, List<DiscussionParticipant> discussMembers) {
        ProjectDiscuss discuss = getById(projectId);
        DiscussionEntry discussNote = new DiscussionEntry(note, creatorId, creatorName, System.currentTimeMillis(), discussMembers);
        if (Objects.isNull(discuss)) {
            discuss = new ProjectDiscuss();
            discuss.setProjectId(projectId);
            discuss.setEntry(Collections.singletonList(discussNote));
        } else {
            discuss.getEntry().add(discussNote);
        }
        // discuss相关移到activity 20210609
//        elasticSearchHelper.update(discuss);
        return commentManager.addComment(createCommentWithUser(user, Comment.from(projectId, creatorId, note)));
    }

    private Comment createCommentWithUser(User user, Comment comment) {
        Message.CommentMessage.Builder builder = comment.toMessage().toBuilder();
        builder.setCreatedBy(user.toMessage());
        builder.setSource(ACTIVITY_SOURCE_AI);
        return Comment.from(builder.build());
    }

    @Override
    public ProjectDiscuss getById(long projectId) {
        return elasticSearchHelper.getById(projectId + "", ProjectDiscuss.class).orElse(null);
    }

    @Override
    public void markAsRead(long projectId, User user, ProjectDiscussionProto.DiscussMarkReadParam markReadParam) {
        ProjectEsModel projectEsModel = projectEsService.findProjectByProjectIdCheckExisted(projectId);
        List<MemberInfo> members = null;
        if (CollectionUtils.isNotEmpty(projectEsModel.getMembers())) {
            members = projectEsModel.getMembers().stream()
                .filter(Objects::nonNull)
                .filter(o -> StringUtils.equals(o.getId(), user.getId()))
                .collect(Collectors.toList());
        }


        ProjectDiscuss discuss = Optional.ofNullable(getById(projectId))
            .orElseThrow(() -> new ServiceException(MsgCodeManager.PROJECT.PROJECT_DISCUSS_IS_NULL));

        List<MemberInfo> finalMembers = members;
        discuss.getEntry().forEach(d -> {
            if (StringUtils.equals(d.getNoteId(), markReadParam.getNoteId())) {
                d.getParticipants().forEach(part -> {
                    if (!UserAuthEnum.hasRole(user.getAllAuthority(), UserAuthEnum.ROLE_ADMIN)
                        && CollectionUtils.isEmpty(finalMembers)
                        && !StringUtils.equals(part.getReferUserId(), user.getId())) {
                        throw new ServiceException(MsgCodeManager.ROLE.ROLE_AUTH_ERROR);
                    }

                    boolean groupFlag = CollectionUtils.isNotEmpty(finalMembers)
                        && finalMembers.stream().anyMatch(m -> StringUtils.equals(m.getAuth().trim(), part.getReferGroup().trim()));
                    if (StringUtils.equals(part.getReferUserId(), user.getId())
                        || groupFlag) {
                        part.setRead(true);
                        Set<String> isReadByUser = Optional.ofNullable(part.getIsReadByUser()).orElse(new HashSet<>());
                        isReadByUser.add(user.getId());
                        part.setIsReadByUser(isReadByUser);
                    }
                });
            }
        });
        elasticSearchHelper.update(discuss);
    }

    @Override
    public ProjectDiscuss getProjectDiscuss(User user, long projectId, ProjectEsModel projectEsModel) {
        ProjectDiscuss discuss = getById(projectId);
        if (Objects.nonNull(discuss)) {
            Set<String> userIdSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(discuss.getEntry())) {
                Set<String> creatorSet = discuss.getEntry().stream().filter(o -> Objects.isNull(o.getCreatedName())).map(DiscussionEntry::getCreatedBy).collect(Collectors.toSet());
                userIdSet.addAll(creatorSet);
                Set<String> participants = discuss.getEntry().stream().map(DiscussionEntry::getParticipants)
                    .filter(Objects::nonNull)
                    .flatMap(part -> part.stream().map(DiscussionParticipant::getReferUserId))
                    .filter(Objects::nonNull).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(participants)) {
                    userIdSet.addAll(participants);
                }
            }
            Map<String, User> userMap = userIdSet.stream()
                .filter(StringUtils::isNotBlank)
                .map(o -> aiUserService.getUserById(o))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(User::getId, k -> k, (v1, v2) -> v1));

            discuss.getEntry().forEach(o -> {
                User creator = Optional.ofNullable(userMap.get(o.getCreatedBy())).orElse(null);
                if (Objects.nonNull(creator)) {
                    if (StringUtils.isBlank(o.getCreatedName())) {
                        o.setCreatedName(creator.getName());
                    }
                    o.setAvatarUrl(Optional.ofNullable(creator.getPhoto()).map(Object::toString).orElse(""));
                    List<String> creatorRoles = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(projectEsModel.getMembers())) {
                        projectEsModel.getMembers().forEach(m -> {
                            if (StringUtils.equals(m.getId(), o.getCreatedBy())) {
                                creatorRoles.add(m.getAuth());
                            }
                        });
                    }
                    if (UserAuthEnum.hasRole(creator.getAllAuthority(), UserAuthEnum.ROLE_ADMIN)) {
                        creatorRoles.add(DashBoardRoleTagToDoEnum.ADMIN_TODO.getRoleTag());
                    }
                    o.setCreatorRoles(creatorRoles);

                }
                if (AiBotUserEnum.isBotUser(o.getCreatedBy())) {
                    o.setCreatorRoles(Collections.singletonList(AiBotUserEnum.AI_BOT_GROUP));
                }
                if (CollectionUtils.isEmpty(o.getParticipants())) {
                    return;
                }
                o.getParticipants().forEach(part -> {

                    if (StringUtils.isNotBlank(part.getReferUserId())) {
                        part.setReferName(Optional.ofNullable(userMap.get(part.getReferUserId())).map(User::getName).orElse(""));
                    }
                    Boolean unReadFlag = getDiscussIsReadFlag(part, projectEsModel.getMembers());
                    part.setRead(unReadFlag);

                });

            });
        }
        return discuss;
    }


    /**
     * 判断单条留言，当前用户是否已读
     * true 已读
     * false 未读
     *
     * @param participant
     * @param members
     * @return
     */
    @Override
    public Boolean getDiscussIsReadFlag(DiscussionParticipant participant, List<MemberInfo> members) {
        String referUserId;
        if (Objects.isNull(participant)) {
            return true;
        }
        if (StringUtils.isNotBlank(participant.getReferUserId())) {
            referUserId = participant.getReferUserId();
        } else {
            if (CollectionUtils.isEmpty(members)) {
                return false;
            } else {
                referUserId = members.stream()
                    .filter(o -> StringUtils.equals(o.getAuth().trim(), participant.getReferGroup().trim()))
                    .map(MemberInfo::getId)
                    .findFirst()
                    .orElse(null);
            }
        }
        if (StringUtils.isBlank(referUserId)) {
            return false;
        }
        return !CollectionUtils.isEmpty(participant.getIsReadByUser()) && participant.getIsReadByUser().contains(referUserId);
    }

}
