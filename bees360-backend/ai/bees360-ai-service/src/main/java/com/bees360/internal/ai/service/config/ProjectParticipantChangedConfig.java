package com.bees360.internal.ai.service.config;

import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.listener.ProjectParticipantChangedListener;
import com.bees360.project.participant.ProjectParticipantProvider;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Log4j2
@Configuration
public class ProjectParticipantChangedConfig {

    @Bean
    ProjectParticipantChangedListener projectParticipantChangedListener(
            @Autowired ProjectEsService projectEsService,
            @Autowired ProjectParticipantProvider projectParticipantProvider,
            @Autowired Bees360FeatureSwitch bees360FeatureSwitch) {

        return new ProjectParticipantChangedListener(
                projectEsService, projectParticipantProvider, bees360FeatureSwitch);
    }
}
