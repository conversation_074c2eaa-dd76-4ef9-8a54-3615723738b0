package com.bees360.internal.ai.service;

import com.bees360.internal.ai.entity.LogEntry;
import com.bees360.internal.ai.entity.LogEntryDetail;
import com.bees360.internal.ai.entity.ProjectLog;
import com.bees360.internal.ai.entity.enums.LogEntryTypeEnum;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/07/24 17:33
 */
public interface ProjectLogService {

    void insertOrUpdateByLogEntry(long projectId,  LogEntry logEntry);

    /**
     * 插入currentTime的一条logEntry
     * @param projectId
     * @param userId
     * @param type
     * @param logDetail
     */
    void addLogEntry(long projectId,  String userId, String type,  LogEntryDetail logDetail);

    void addLogEntryByLogEntryType(long projectId, String userId, LogEntryTypeEnum entryTypeEnum, LogEntryDetail logDetail);

    /**
     * 根据具体字段值传入，插入一条log
     * 注意这里时间传的是long类型, 会转为String UTC时间, format(yyyy-MM-dd'T'HH:mm:ss.SSS'Z)
     * @param projectId
     * @param userId
     * @param username
     * @param type
     * @param createTime
     * @param logDetail
     */
    void addLogEntry(long projectId, String userId, String username, String type, long createTime, LogEntryDetail logDetail);

    ProjectLog getById(long projectId);

    List<ProjectLog> getLogList(Collection<Long> projectIds);
}
