package com.bees360.internal.ai.service.listener;

import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.entity.LogEntryDetail;
import com.bees360.internal.ai.entity.enums.AiBotUserEnum;
import com.bees360.internal.ai.entity.enums.LogEntryTypeEnum;
import com.bees360.internal.ai.entity.enums.LogReportActionEnum;
import com.bees360.internal.ai.entity.enums.LogReportDetailEnum;
import com.bees360.internal.ai.service.ProjectLogService;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.report.ReportProvider;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 监听报告组添加事件并记录相关日志信息
 */
@Log4j2
public class SubmitLogWhenReportGroupAddedEventListener extends AbstractNamedEventListener<ReportGroupAdded> {

    private ProjectLogService projectLogService;

    private ReportProvider reportProvider;

    private Set<ReportTypeEnum> logEntrySpecialReportTypes;

    public SubmitLogWhenReportGroupAddedEventListener(
            ProjectLogService projectLogService,
            ReportProvider reportProvider,
            Set<ReportTypeEnum> logEntrySpecialReportTypes) {
        this.projectLogService = projectLogService;
        this.reportProvider = reportProvider;
        this.logEntrySpecialReportTypes = logEntrySpecialReportTypes;
        log.info(
                "Created {}(projectLogService={}, reportProvider={}, logEntrySpecialReportTypes={}).",
                this,
                projectLogService,
                reportProvider,
                logEntrySpecialReportTypes);
    }

    @Override
    public void handle(ReportGroupAdded event) throws IOException {
        log.info("handleEventOnReportGroupAddedEvent event:{}, specialReportTypes:{}", event.toString(),
            logEntrySpecialReportTypes.stream().map(Enum::name).collect(Collectors.joining(",")));
        String groupType = event.getGroupType();
        if (!DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE.equalsIgnoreCase(groupType)) {
            return;
        }

        var projectId = Long.parseLong(event.getGroupKey());
        var reportId = event.getReportId();
        var report = reportProvider.get(reportId);
        var reportType = ReportTypeEnum.getReportType(report);
        String userId = Optional.ofNullable(event.getCreatedBy()).orElse(AiBotUserEnum.AI_NEW_USER_ID.getCode());
        LogReportDetailEnum detailEnum = LogReportDetailEnum.getEnumByReportType(reportType.getCode());
        projectLogService.addLogEntryByLogEntryType(
            projectId,
            userId,
            LogEntryTypeEnum.REPORT,
            new LogEntryDetail(detailEnum, LogReportActionEnum.GENERATED)
        );
    }
}
