package com.bees360.internal.ai.service.listener;

import com.bees360.event.registry.JobCompleted;
import com.bees360.event.util.AbstractNamedEventListener;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

/**
 * 监听job执行完成事件，目前通过jobName和jobId分别调用不同的方法
 *
 * <AUTHOR>
 */
@Log4j2
@Component
public class JobCompletedEvent extends AbstractNamedEventListener<JobCompleted> {

    @Override
    public void handle(JobCompleted jobCompleted) {
    }
}
