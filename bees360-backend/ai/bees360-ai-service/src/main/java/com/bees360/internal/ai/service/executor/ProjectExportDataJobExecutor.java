package com.bees360.internal.ai.service.executor;

import com.bees360.internal.ai.entity.ProjectExportData;
import com.bees360.internal.ai.service.ProjectExportDataManager;
import com.bees360.job.registry.ProjectExportDataJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.resource.ResourcePool;
import com.google.gson.Gson;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.Map;

@Log4j2
@Component
public class ProjectExportDataJobExecutor
		extends AbstractJobExecutor<ProjectExportDataJob> {

	private final ResourcePool resourcePool;

	private final ProjectExportDataManager compositedProjectExportDataManager;

	public ProjectExportDataJobExecutor(ResourcePool resourcePool, ProjectExportDataManager compositedProjectExportDataManager) {
		this.resourcePool = resourcePool;
		this.compositedProjectExportDataManager = compositedProjectExportDataManager;
		log.info(
				"Created {}(resourcePool='{}', compositedProjectExportDataManager='{}').",
				this,
				this.resourcePool,
				this.compositedProjectExportDataManager);
	}

	@Override
	protected void handle(ProjectExportDataJob job) throws IOException {
		log.info("Start saving project export data. job='{}'", job);
		var resourceKey = job.getDataResourceKey();
		var exportDataResource = resourcePool.get(resourceKey);
		var exportDataString =
				exportDataResource.apply(in -> IOUtils.toString(in, StandardCharsets.UTF_8));
		var exportDataMap = new Gson().fromJson(exportDataString, Map.class);

		var exportData = new ProjectExportData();
		exportData.setRelatedId((String) exportDataMap.get("relatedId"));
		exportData.setRelatedType((String) exportDataMap.get("relatedType"));
		exportData.setDataLog((String) exportDataMap.get("dataLog"));
		var updatedTime = (Number) exportDataMap.get("updatedTime");
		exportData.setUpdatedTime(new Timestamp(updatedTime.longValue()));

		compositedProjectExportDataManager.insertOrUpdateData(exportData);
		log.info("Project export data job executed successfully.");
	}
}
