package com.bees360.internal.ai.service.firebase;

import com.bees360.commons.firebasesupport.service.FirebaseHelper;
import com.bees360.job.registry.SerializableFirebaseHover;
import com.bees360.internal.ai.entity.enums.ProjectHoverStatusEnum;
import com.google.cloud.Timestamp;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FirebaseHoverJobService {

    public final static String collection = "hover_job";

    @Autowired
    private FirebaseHelper firebaseHelper;

    public SerializableFirebaseHover getByHoverJobId(String hoverJobId) {
        return firebaseHelper.getDataById(collection, hoverJobId, SerializableFirebaseHover.class);
    }

    /**
     * set Bees360HoverState
     *
     * @param hoverJobId
     * @param statusEnum ProjectHoverStatusEnum
     * @param hoverAccount hover平台账号
     */
    public void setBees360HoverState(
            String hoverJobId,
            long projectId,
            ProjectHoverStatusEnum statusEnum,
            String hoverAccount) {
        Map<String, Object> map = new HashMap();
        map.put("bees360HoverState", statusEnum.getCode());
        map.put("projectId", String.valueOf(projectId));
        map.put("updateTime", Timestamp.now());
        map.put("hoverAccount", hoverAccount);
        firebaseHelper.set(collection, String.valueOf(hoverJobId), map);
    }
}
