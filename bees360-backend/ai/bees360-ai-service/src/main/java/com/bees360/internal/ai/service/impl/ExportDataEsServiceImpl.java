package com.bees360.internal.ai.service.impl;

import com.bees360.commons.elasticsearchsupport.ElasticSearchHelper;
import com.bees360.internal.ai.common.exceptions.ServiceMessageException;
import com.bees360.internal.ai.entity.BsExportData;
import com.bees360.internal.ai.service.BsExportDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * 后续可切换redis实现数据存储
 */
@Slf4j
@Service
@Primary
public class ExportDataEsServiceImpl implements BsExportDataService {

    @Autowired
    private ElasticSearchHelper elasticSearchHelper;

    @Override
    public void deleteExportData(String relatedId, String type) throws ServiceMessageException {
        elasticSearchHelper.deleteById(new BsExportData(relatedId, type));
    }

    @Override
    public BsExportData getByRelatedIdAndType(String relatedId, String type) {
        BsExportData exportData = new BsExportData();
        exportData.setRelatedId(relatedId);
        exportData.setRelatedType(type);

        return elasticSearchHelper.getById(exportData.id(), BsExportData.class).orElse(null);
    }
}
