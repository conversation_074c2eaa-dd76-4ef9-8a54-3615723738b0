package com.bees360.internal.ai.service.entity;

import com.bees360.commons.elasticsearchsupport.EsDocumentUtil;
import com.bees360.entity.enums.AmericaStateEnums;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectListResult;
import com.bees360.internal.ai.entity.ProjectListResult.StatusTag;
import com.bees360.internal.ai.entity.ProjectStatusTag;
import com.bees360.internal.ai.entity.consts.ProjectTags;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.ClaimTypeEnum;
import com.bees360.internal.ai.entity.enums.DashBoardRoleTagToDoEnum;
import com.bees360.internal.ai.entity.enums.UserAuthEnum;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.internal.ai.service.entity.query.ProjectEsQueryParam;
import com.bees360.internal.ai.util.date.DateUtil;
import io.jsonwebtoken.lang.Collections;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.NestedQueryBuilder;
import org.opensearch.index.query.QueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.index.query.RangeQueryBuilder;
import org.opensearch.index.query.TermsQueryBuilder;
import org.opensearch.script.Script;
import org.opensearch.script.ScriptType;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE;
import static com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_OPEN;
import static com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_PAUSE;
import static java.util.Optional.ofNullable;

/**
 * <AUTHOR> Yang
 */
public class EsRequestConverter {

    public static QueryBuilder convert(ProjectEsQueryParam param, Bees360FeatureSwitch featureSwitch) {
        // @formatter:off
        BoolQueryBuilder projectQueryBuilder = QueryBuilders.boolQuery();
        ofNullable(param.getProjectId()).map(o -> projectQueryBuilder.must(QueryBuilders.termQuery("projectId", o)));
        // Use the getFilterScript method to filter based on the attribute value of policyNumber
        ofNullable(param.getPolicyNumber()).map(o -> projectQueryBuilder.filter(QueryBuilders.scriptQuery(getFilterScript("policyNumber", o))));
        // also Use the getFilterScript method to filter based on the attribute value of inspectionNumber
        ofNullable(param.getInspectionNumber()).map(o -> projectQueryBuilder.filter(QueryBuilders.scriptQuery(getFilterScript("inspectionNumber", o))));
        ofNullable(param.getAddress()).map(o -> projectQueryBuilder.must(QueryBuilders.matchPhraseQuery("address", o)));
        ofNullable(param.getCity()).map(o -> projectQueryBuilder.must(QueryBuilders.matchPhraseQuery("city", o)));
//        ofNullable(param.getState()).map(o -> projectQueryBuilder.must(QueryBuilders.matchPhraseQuery("state", o)));
        ofNullable(param.getCreateStartTime()).map(
            o -> projectQueryBuilder.must(QueryBuilders.rangeQuery("createdTime").gte(param.getCreateStartTime())));
        ofNullable(param.getCreateEndTime())
            .map(o -> projectQueryBuilder.must(QueryBuilders.rangeQuery("createdTime").lte(param.getCreateEndTime())));
        ofNullable(param.getInspectionStartTime()).map(o -> projectQueryBuilder
            .must(QueryBuilders.rangeQuery("inspectionTime").gte(param.getInspectionStartTime())));
        ofNullable(param.getInspectionEndTime()).map(o -> projectQueryBuilder
            .must(QueryBuilders.rangeQuery("inspectionTime").lte(param.getInspectionEndTime())));

        ofNullable(param.getSiteInspectionStartTime()).map(o -> projectQueryBuilder
            .must(QueryBuilders.rangeQuery("siteInspectedTime").gte(param.getSiteInspectionStartTime())));
        ofNullable(param.getSiteInspectionEndTime()).map(o -> projectQueryBuilder
            .must(QueryBuilders.rangeQuery("siteInspectedTime").lte(param.getSiteInspectionEndTime())));
        ofNullable(param.getServiceTypes())
            .map(o -> projectQueryBuilder.must(QueryBuilders.termsQuery("serviceType", o)));
        ofNullable(param.getCreatorName())
            .map(o -> projectQueryBuilder.must(QueryBuilders.matchPhraseQuery("creatorName", o)));
        ofNullable(param.getFilterStatuses())
            .map(o -> projectQueryBuilder.must(QueryBuilders.termsQuery("projectStatus", o)));
        ofNullable(param.getInsuranceCompanyIds())
            .map(o -> projectQueryBuilder.must(QueryBuilders.termsQuery("insuranceCompany", o)));
        ofNullable(param.getZipCode())
            .map(o -> projectQueryBuilder.must(QueryBuilders.termQuery("zipCode", o)));
        ofNullable(param.getOperatingCompanyList())
            .map(o -> projectQueryBuilder.must(QueryBuilders.termsQuery("operatingCompany.keyword", o)));
        ofNullable(param.getPolicyTypeList())
            .map(o -> projectQueryBuilder.must(QueryBuilders.termsQuery("policyType.keyword", o)));
        if(!param.isContainsDeleted()){
            projectQueryBuilder.mustNot(QueryBuilders.termsQuery("projectStatus",
                Arrays.asList(AiProjectStatusEnum.PROJECT_DELETED.getCode())));
        }

        setProjectStatusAndTime(param, projectQueryBuilder);
        setMultiProjectStatusAndTime(param, projectQueryBuilder);

        if (!param.isSearchAll() && !StatusTag.ARCHIVED.equals(param.getStatusTag())) {
            projectQueryBuilder.mustNot(QueryBuilders.termsQuery("projectStatus",
                Arrays.asList(AiProjectStatusEnum.PROJECT_ARCHIVED.getCode())));
        }
        setProjectIds(projectQueryBuilder, param);
        if (!param.isSearchAll() || (param.isSearchAll() && Objects.nonNull(param.getIsClaimSearch()))
            || param.isExport()) {
            // 如果不是搜索全部，则必须区分是claimDashBoard还是underwriting
            projectQueryBuilder
                .must(QueryBuilders.termsQuery("claimType", ClaimTypeEnum.getTypeCode(param.getIsClaimSearch())));
        }

        if (StringUtils.equals(param.getMemberName(), "Unassigned")){
            setUnassignedMemberRoleAndName(projectQueryBuilder, param.getMemberRole());
        } else {
            setMemberRoleAndName(projectQueryBuilder, param.getMemberName(), param.getMemberRole());
            setMemberName(projectQueryBuilder, param.getMemberName());
        }

        setCompanyName(projectQueryBuilder, param.getCompanyName());
        setEsParamStrList(
                projectQueryBuilder, "insuranceCompanyName", param.getInsuranceCompanyName());
        setEsParamStrList(projectQueryBuilder, "repairCompanyName", param.getProcessCompanyName());
        setExcludeCompanyIds(projectQueryBuilder, param.getExcludeCompanyIds());

        setQueryByRoleAndStatusTag(projectQueryBuilder, param.getRoleTag(), param.getStatusTag(), featureSwitch);

        setQueryByUserAuth(param, projectQueryBuilder);

        setDaysOldQueryBuild(projectQueryBuilder, param.isSearchDaysOld(), param.getDaysOldStart(), param.getDaysOldEnd());

        setProjectTagsQueryBuild(projectQueryBuilder, param.getProjectTags());

        ofNullable(param.getAssetOwnerName())
            .map(o -> projectQueryBuilder.must(QueryBuilders.matchPhraseQuery("assetOwnerName", o)));
        ofNullable(param.getAssetOwnerPhone())
            .map(o -> projectQueryBuilder.must(QueryBuilders.matchPhraseQuery("assetOwnerPhone", o)));

        setPolicyEffectiveDateQuery(projectQueryBuilder, param.getPolicyEffectiveDateStart(),
            param.getPolicyEffectiveDateEnd());
        setPilotQuery(projectQueryBuilder, param.getPilot());

        setMustCriteria(projectQueryBuilder, param);
        setMustNotCriteria(projectQueryBuilder, param);
        setState(projectQueryBuilder, param);
        setStates(projectQueryBuilder, param);
        setProjectTagList(projectQueryBuilder, param.getProjectTagList());
        ofNullable(param.getClaimTypes())
                .map(o -> projectQueryBuilder.must(QueryBuilders.termsQuery("claimType", o)));
        ofNullable(param.getProjectTypes())
                .map(o -> projectQueryBuilder.must(QueryBuilders.termsQuery("projectType", o)));
        setImageCount(projectQueryBuilder,param);
        setDarStatusList(projectQueryBuilder, param.getDarStatusList());
        setPirStatusList(projectQueryBuilder, param.getPirStatusList());
        // Project State: Open/Pause/Close & Change Reason
        setProjectStateAndChangeReason(projectQueryBuilder, param);
        setPolicyNumber(projectQueryBuilder, param);
        setInspectionNumber(projectQueryBuilder, param);
        ofNullable(param.getIsParentProject())
            .map(o -> projectQueryBuilder.must(QueryBuilders.termQuery("isParentProject", o)));
        ofNullable(param.getIsChildProject())
            .map(o -> projectQueryBuilder.must(QueryBuilders.termQuery("isChildProject", o)));
        // @formatter:on
        return projectQueryBuilder;
    }

    private static void setImageCount(BoolQueryBuilder projectQueryBuilder, ProjectEsQueryParam param) {
        if (param.getDroneImageCountMax() != null || param.getDroneImageCountMin() != null) {
            RangeQueryBuilder droneRangeQueryBuilder = QueryBuilders.rangeQuery("droneImageCount");
            ofNullable(param.getDroneImageCountMin())
                    .ifPresent(e -> droneRangeQueryBuilder.gte(param.getDroneImageCountMin()));
            ofNullable(param.getDroneImageCountMax())
                    .ifPresent(e -> droneRangeQueryBuilder.lte(param.getDroneImageCountMax()));
            projectQueryBuilder.must(droneRangeQueryBuilder);
        }
        if (param.getMobileImageCountMin() != null || param.getMobileImageCountMax() != null) {
            RangeQueryBuilder mobileRangeQueryBuilder =
                    QueryBuilders.rangeQuery("mobileImageCount");
            ofNullable(param.getMobileImageCountMin())
                    .ifPresent(e -> mobileRangeQueryBuilder.gte(param.getMobileImageCountMin()));
            ofNullable(param.getMobileImageCountMax())
                    .ifPresent(e -> mobileRangeQueryBuilder.lte(param.getMobileImageCountMax()));
            projectQueryBuilder.must(mobileRangeQueryBuilder);
        }
    }

    private static void setProjectStatusAndTime(ProjectEsQueryParam param, BoolQueryBuilder projectQueryBuilder) {
        if (param.getProjectStatus() == null) {
            return;
        }
        if (param.getProjectStatusStartTime() != null || param.getProjectStatusEndTime() != null) {
            setTimeLinesStatusQuery(
                    projectQueryBuilder,
                    List.of(param.getProjectStatus()),
                    param.getProjectStatusStartTime(),
                    param.getProjectStatusEndTime());
        } else {
            ofNullable(param.getProjectStatus())
                    .map(
                            o ->
                                    projectQueryBuilder.must(
                                            QueryBuilders.termQuery("projectStatus", o)));
        }
    }

    private static void setMultiProjectStatusAndTime(ProjectEsQueryParam param, BoolQueryBuilder projectQueryBuilder) {
        if(CollectionUtils.isEmpty(param.getProjectStatusList())){
            return ;
        }
        if (param.getProjectStatusStartTime() != null || param.getProjectStatusEndTime() != null) {
            setTimeLinesStatusQuery(
                projectQueryBuilder,
                param.getProjectStatusList(),
                param.getProjectStatusStartTime(),
                param.getProjectStatusEndTime());
        } else {
            projectQueryBuilder.must(
                    QueryBuilders.termsQuery("projectStatus", param.getProjectStatusList()));
        }
    }

    private static void setMustCriteria(BoolQueryBuilder queryBuilder, ProjectEsQueryParam param) {
        if(CollectionUtils.isEmpty(param.getMustCriteria())){
            return;
        }
        param.getMustCriteria()
                .forEach(
                        k -> {
                            if (k.getValue() instanceof String) {
                                queryBuilder.must(
                                        QueryBuilders.termQuery(k.getKey(), (String) k.getValue()));
                            } else if (k.getValue() instanceof List) {
                                queryBuilder.must(
                                        QueryBuilders.termsQuery(k.getKey(), (List) k.getValue()));
                            } else {
                                queryBuilder.must(
                                        QueryBuilders.termQuery(k.getKey(), k.getValue()));
                            }
                        });
    }

    private static void setMustNotCriteria(
            BoolQueryBuilder queryBuilder, ProjectEsQueryParam param) {
        if (CollectionUtils.isEmpty(param.getMustNotCriteria())) {
            return;
        }
        param.getMustNotCriteria()
                .forEach(
                        k -> {
                            if (k.getValue() instanceof String) {
                                queryBuilder.mustNot(
                                        QueryBuilders.termQuery(k.getKey(), (String) k.getValue()));
                            } else if (k.getValue() instanceof List) {
                                queryBuilder.mustNot(
                                        QueryBuilders.termsQuery(k.getKey(), (List) k.getValue()));
                            } else {
                                queryBuilder.mustNot(
                                        QueryBuilders.termQuery(k.getKey(), k.getValue()));
                            }
                        });
    }

    private static void setPolicyNumber(BoolQueryBuilder projectQueryBuilder, ProjectEsQueryParam param) {
        if (CollectionUtils.isNotEmpty(param.getPolicyNumbers())) {
            projectQueryBuilder.filter(QueryBuilders.scriptQuery(getFilterScript("policyNumber", param.getPolicyNumbers())));
        }
    }

    private static void setInspectionNumber(BoolQueryBuilder projectQueryBuilder, ProjectEsQueryParam param) {
        if (CollectionUtils.isNotEmpty(param.getInspectionNumbers())) {
            projectQueryBuilder.filter(QueryBuilders.scriptQuery(getFilterScript("inspectionNumber", param.getInspectionNumbers())));
        }
    }

    private static void setState(BoolQueryBuilder projectQueryBuilder, ProjectEsQueryParam param) {
        // 设置州的筛选条件
        if (StringUtils.isBlank(param.getState())) {
            return;
        }
        AmericaStateEnums stateValue = AmericaStateEnums.valueOfState(param.getState());
        if (Objects.isNull(stateValue)) {
            projectQueryBuilder.must(QueryBuilders.matchPhraseQuery("state", param.getState()));
            return;
        }
        BoolQueryBuilder projectStateQueryBuilder = QueryBuilders.boolQuery();
        projectStateQueryBuilder.should(
                QueryBuilders.matchPhraseQuery("state", stateValue.getAbbreviation()));
        projectStateQueryBuilder.should(
                QueryBuilders.matchPhraseQuery("state", stateValue.getFullName()));
        projectQueryBuilder.must(projectStateQueryBuilder);
    }

    private static void setStates(BoolQueryBuilder projectQueryBuilder, ProjectEsQueryParam param) {
        var states = Optional.ofNullable(param.getStates()).orElseGet(ArrayList::new);
        // 设置州的筛选条件
        if (CollectionUtils.isEmpty(states)) {
            return;
        }
        var multiStateBoolQuery = QueryBuilders.boolQuery();
        for (String state : states) {
            AmericaStateEnums stateValue = AmericaStateEnums.valueOfState(state);
            if (Objects.isNull(stateValue)) {
                multiStateBoolQuery.should(QueryBuilders.matchPhraseQuery("state", state));
                continue;
            }
            multiStateBoolQuery.should(
                QueryBuilders.matchPhraseQuery("state", stateValue.getAbbreviation()));
            multiStateBoolQuery.should(
                QueryBuilders.matchPhraseQuery("state", stateValue.getFullName()));
        }
        projectQueryBuilder.must(multiStateBoolQuery);
    }

    private static void setProjectStateAndChangeReason(
            BoolQueryBuilder projectQueryBuilder, ProjectEsQueryParam param) {
        if (CollectionUtils.isNotEmpty(param.getProjectState())) {
            projectQueryBuilder.must(
                    QueryBuilders.termsQuery("projectState", param.getProjectState()));
        }
        if (StringUtils.isNotBlank(param.getProjectStateChangeReason())) {
            projectQueryBuilder.must(
                    QueryBuilders.matchPhraseQuery(
                            "projectStateChangeReason",
                            param.getProjectStateChangeReason()));
        }
        // query by projectStateChangeReasonId
        if (CollectionUtils.isNotEmpty(param.getProjectStateChangeReasonIds())) {
            projectQueryBuilder.must(QueryBuilders.termsQuery("projectStateChangeReasonId", param.getProjectStateChangeReasonIds()));
        }
    }

    private static void setProjectTagList(BoolQueryBuilder boolQueryBuilder, List<String> projectTagList) {
        if (Collections.isEmpty(projectTagList)) {
            return;
        }

        var projectTagsQuery =
                QueryBuilders.boolQuery()
                        .must(QueryBuilders.termsQuery("projectTagList.id", projectTagList));
        boolQueryBuilder.must(projectTagsQuery);
    }

    private static void setDarStatusList(BoolQueryBuilder projectQueryBuilder, List<Integer> darStatusList) {
        if (Collections.isEmpty(darStatusList)) {
            return;
        }

        BoolQueryBuilder darStatusQuery = QueryBuilders.boolQuery().should(QueryBuilders.termsQuery("darReportStatus", darStatusList));
        if (darStatusList.contains(0)) {
            darStatusQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("darReportStatus")));
        }
        projectQueryBuilder.must(darStatusQuery);
    }

    private static void setPirStatusList(BoolQueryBuilder projectQueryBuilder, List<Integer> pirStatusList) {
        if (Collections.isEmpty(pirStatusList)) {
            return;
        }

        BoolQueryBuilder pirStatusQuery = QueryBuilders.boolQuery().should(QueryBuilders.termsQuery("pirReportStatus", pirStatusList));
        if (pirStatusList.contains(0)) {
            pirStatusQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("pirReportStatus")));
        }
        projectQueryBuilder.must(pirStatusQuery);
    }

    private static void setProjectIds(BoolQueryBuilder projectQueryBuilder, ProjectEsQueryParam param) {
        setProjectIds(projectQueryBuilder, param.getProjectIds());
        setProjectIds(projectQueryBuilder, param.getDarProjectIds());
        setProjectIds(projectQueryBuilder, param.getEstimateProjectIds());
    }

    private static void setProjectIds(BoolQueryBuilder projectQueryBuilder, List<Long> projectIds) {
        ofNullable(projectIds).ifPresent(o -> {
            List<String> esIds = o.stream().map(id -> EsDocumentUtil.getEsId(id + "", ProjectEsModel.class))
                .collect(Collectors.toList());
            projectQueryBuilder.must(QueryBuilders.idsQuery().addIds(esIds.toArray(new String[o.size()])));
        });

    }

    private static void setCompanyName(BoolQueryBuilder projectQueryBuilder, List<String> companyNames) {
        if (CollectionUtils.isEmpty(companyNames)) {
            return;
        }
        BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
        for (String companyName : companyNames) {
            shouldQueryBuilder.should(QueryBuilders.matchPhraseQuery("companyName", companyName))
                .should(QueryBuilders.matchPhraseQuery("insuranceCompanyName", companyName))
                .should(QueryBuilders.matchPhraseQuery("repairCompanyName", companyName));
        }
        projectQueryBuilder.must(shouldQueryBuilder);
    }

    private static void setEsParamStrList(
            BoolQueryBuilder projectQueryBuilder, String esParam, List<String> matchList) {
        if (CollectionUtils.isEmpty(matchList)) {
            return;
        }
        BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
        for (String matchStr : matchList) {
            shouldQueryBuilder.should(QueryBuilders.matchPhraseQuery(esParam, matchStr));
        }
        projectQueryBuilder.must(shouldQueryBuilder);
    }

    private static void setExcludeCompanyIds(BoolQueryBuilder projectQueryBuilder, List<Long> excludeCompanyIds) {
        if (CollectionUtils.isEmpty(excludeCompanyIds)) {
            return;
        }

        projectQueryBuilder.mustNot(QueryBuilders.termsQuery("insuranceCompany", excludeCompanyIds));
        projectQueryBuilder.mustNot(QueryBuilders.termsQuery("repairCompany", excludeCompanyIds));
    }

    private static void setMemberRoleAndName(BoolQueryBuilder projectQueryBuilder, String memberName,
        String memberRole) {
        if (StringUtils.isNotBlank(memberRole)) {
            BoolQueryBuilder nestedBoolQueryBuilder = QueryBuilders.boolQuery();
            nestedBoolQueryBuilder.must(QueryBuilders.matchPhraseQuery("members.auth", memberRole));
            if (StringUtils.isNotBlank(memberName)) {
                nestedBoolQueryBuilder.must(QueryBuilders.matchPhraseQuery("members.name", memberName));
            }
            NestedQueryBuilder nestedQueryBuilder = QueryBuilders
                .nestedQuery("members", nestedBoolQueryBuilder, ScoreMode.None);
            projectQueryBuilder.must(nestedQueryBuilder);
        }
    }

    private static void setMemberName(BoolQueryBuilder projectQueryBuilder, String memberName) {
        if (StringUtils.isNotBlank(memberName)) {
            BoolQueryBuilder nestedBoolQueryBuilder = QueryBuilders.boolQuery();
            nestedBoolQueryBuilder.must(QueryBuilders.matchPhraseQuery("members.name", memberName));
            NestedQueryBuilder nestedQueryBuilder = QueryBuilders
                .nestedQuery("members", nestedBoolQueryBuilder, ScoreMode.None);
            projectQueryBuilder.must(nestedQueryBuilder);
        }
    }

    private static void setUnassignedMemberRoleAndName(BoolQueryBuilder projectQueryBuilder, String memberRole) {
        BoolQueryBuilder nestedBoolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(memberRole)) {
            nestedBoolQueryBuilder.must(
                QueryBuilders.termQuery("members.auth", UserAuthEnum.ROLE_ADJUSTER.getAuth().toLowerCase()));
            nestedBoolQueryBuilder.must(
                QueryBuilders.termQuery("members.auth", UserAuthEnum.ROLE_PROCESSOR.getAuth().toLowerCase()));
            nestedBoolQueryBuilder.must(
                QueryBuilders.termQuery("members.auth", UserAuthEnum.ROLE_REVIEWER.getAuth().toLowerCase()));
            nestedBoolQueryBuilder.must(
                QueryBuilders.termQuery("members.auth", UserAuthEnum.ROLE_PRODUCER.getAuth().toLowerCase()));
        } else {
            nestedBoolQueryBuilder.must(QueryBuilders.termQuery("members.auth", memberRole.toLowerCase()));
        }

        NestedQueryBuilder nestedQueryBuilder = QueryBuilders
            .nestedQuery("members", nestedBoolQueryBuilder, ScoreMode.None);
        projectQueryBuilder.mustNot(nestedQueryBuilder);
    }

    private static void setQueryByUserAuth(
            ProjectEsQueryParam param, BoolQueryBuilder projectQueryBuilder) {
        var userKeys = param.getUserKeys();
        if ((CollectionUtils.isNotEmpty(userKeys) || StringUtils.isNotBlank(param.getUserId()))
                && StringUtils.isNotBlank(param.getRoleTag())) {
            BoolQueryBuilder membersBoolQueryBuilder = QueryBuilders.boolQuery();
            membersBoolQueryBuilder.must(
                    QueryBuilders.matchPhraseQuery("members.auth", param.getRoleTag()));
            BoolQueryBuilder memberShouldQueryBuilder = QueryBuilders.boolQuery();
            BoolQueryBuilder participantShouldQueryBuilder = QueryBuilders.boolQuery();
            BoolQueryBuilder participantsBoolQueryBuilder = QueryBuilders.boolQuery();

            if (CollectionUtils.isNotEmpty(userKeys)) {
                userKeys.forEach(
                        key -> {
                            memberShouldQueryBuilder.should(
                                    QueryBuilders.matchPhraseQuery("members.id", key));
                            participantShouldQueryBuilder.should(
                                    QueryBuilders.matchPhraseQuery("participants.id", key));
                        });
                participantsBoolQueryBuilder.must(participantShouldQueryBuilder);
                membersBoolQueryBuilder.must(memberShouldQueryBuilder);
            } else if (StringUtils.isNotBlank(param.getUserId())) {
                membersBoolQueryBuilder.must(
                        QueryBuilders.matchPhraseQuery("members.id", param.getUserId()));
                participantsBoolQueryBuilder.must(
                        QueryBuilders.matchPhraseQuery("participants.id", param.getUserId()));
            }

            NestedQueryBuilder membersQueryBuilder =
                    QueryBuilders.nestedQuery("members", membersBoolQueryBuilder, ScoreMode.None);
            NestedQueryBuilder participantsQueryBuilder =
                    QueryBuilders.nestedQuery(
                            "participants", participantsBoolQueryBuilder, ScoreMode.None);
            projectQueryBuilder.must(
                    QueryBuilders.boolQuery()
                            .should(membersQueryBuilder)
                            .should(participantsQueryBuilder));
        }
    }

    /**
     * status tag search
     * <p>dar and estimate  {@see ProjectEsServiceImpl#addQueryProjectIds(com.bees360.internal.ai.service.entity.query.ProjectEsQueryParam)}</p>
     */
    private static void setQueryByRoleAndStatusTag(BoolQueryBuilder boolQueryBuilder, String roleTag,
        String statusTag,Bees360FeatureSwitch bees360FeatureSwitch) {
        DashBoardRoleTagToDoEnum toDoEnum = DashBoardRoleTagToDoEnum.getByRoleTag(Optional.ofNullable(roleTag));
        if (StringUtils.equals(ProjectListResult.StatusTag.TODO, statusTag)) {
            boolQueryBuilder
                .must(QueryBuilders.termsQuery("projectStatus", ProjectStatusTag.getTodoStatusCodes(toDoEnum)));
            boolQueryBuilder.must(
                    QueryBuilders.termsQuery("projectState", PROJECT_OPEN.name().toLowerCase()));
            if (bees360FeatureSwitch.isEnableOpenCloseRead()) {
                setNotClosedQueryBuild(boolQueryBuilder);
            }
        }
        if (StringUtils.equals(ProjectListResult.StatusTag.ACTIVE, statusTag)) {
            setActiveQueryBuild(boolQueryBuilder);
            if (bees360FeatureSwitch.isEnableOpenCloseRead()) {
                setNotClosedQueryBuild(boolQueryBuilder);
            }
        }
        if (StringUtils.equals(ProjectListResult.StatusTag.CLOSED, statusTag)) {
            setClosedQueryBuild(boolQueryBuilder);
        }

        if (StringUtils.equals(StatusTag.REVIEW, statusTag)) {
            setReviewQueryBuilder(boolQueryBuilder);
            if (bees360FeatureSwitch.isEnableOpenCloseRead()) {
                setNotClosedQueryBuild(boolQueryBuilder);
            }
        }
        if (StringUtils.equals(StatusTag.ARCHIVED, statusTag)) {
            setArchivedQueryBuilder(boolQueryBuilder);
        }
    }

    private static void setActiveQueryBuild(BoolQueryBuilder boolQueryBuilder) {
        boolQueryBuilder.must(
                QueryBuilders.termsQuery("projectStatus", ProjectStatusTag.getActiveTagStatus()));
        boolQueryBuilder.must(
                QueryBuilders.termsQuery(
                        "projectState",
                        List.of(
                                PROJECT_OPEN.name().toLowerCase(),
                                PROJECT_PAUSE.name().toLowerCase())));
    }

    private static void setClosedQueryBuild(BoolQueryBuilder boolQueryBuilder) {
        BoolQueryBuilder statusQuery = QueryBuilders.boolQuery();
        statusQuery.should(QueryBuilders.termsQuery("projectStatus", ProjectStatusTag.getClosedStatusShowCodes()));

        BoolQueryBuilder stateQuery = QueryBuilders.boolQuery();
        stateQuery.must(QueryBuilders.termQuery("projectState", PROJECT_CLOSE.name().toLowerCase()));
        stateQuery.mustNot(QueryBuilders.termQuery("projectStatus", AiProjectStatusEnum.PROJECT_ARCHIVED.getCode()));

        BoolQueryBuilder finalQuery = QueryBuilders.boolQuery();
        finalQuery.should(statusQuery);
        finalQuery.should(stateQuery);

        boolQueryBuilder.must(finalQuery);
    }
    private static void setNotClosedQueryBuild(BoolQueryBuilder boolQueryBuilder) {
        var stateQuery =  QueryBuilders.termsQuery("projectState",PROJECT_CLOSE.name().toLowerCase());
        boolQueryBuilder.mustNot(stateQuery);
    }

    private static void setReviewQueryBuilder(BoolQueryBuilder boolQueryBuilder) {
        boolQueryBuilder
            .must(QueryBuilders.termQuery("projectStatus", AiProjectStatusEnum.ESTIMATE_COMPLETED.getCode()));
    }

    private static void setArchivedQueryBuilder(BoolQueryBuilder boolQueryBuilder) {
        boolQueryBuilder
            .must(QueryBuilders.termQuery("projectStatus", AiProjectStatusEnum.PROJECT_ARCHIVED.getCode()));
    }

    private static void setDaysOldQueryBuild(BoolQueryBuilder boolQueryBuilder, boolean isSearchDaysOld,
        Long daysOldStart, Long daysOldEnd) {

        if (!isSearchDaysOld
            || Objects.isNull(daysOldEnd) || daysOldEnd < 0L
            || Objects.isNull(daysOldStart) || daysOldStart < 0L) {
            return;
        }
        LocalDateTime centralNow = DateUtil.getUSCentralNow();
        long createTimeStart = centralNow
            .minusDays(daysOldEnd)
            .atZone(DateUtil.getUSCentralZoneId())
            .toInstant().toEpochMilli();

        long createdTimeEnd = centralNow
            .minusDays(daysOldStart)
            .atZone(DateUtil.getUSCentralZoneId())
            .toInstant().toEpochMilli();

        String policyDateStart = centralNow.minusDays(daysOldEnd).toString();

        String policyDateEnd = centralNow.minusDays(daysOldStart).toString();
        if (StringUtils.equals(policyDateEnd, centralNow.toString())) {
            // 用来处理policyEffectiveDate > 当前天也要查询出来的情况，默认搜索一年内的
            policyDateEnd = centralNow.plusYears(1).toString();
        }

        boolQueryBuilder
            .must(QueryBuilders.boolQuery()
                .should(QueryBuilders.rangeQuery("createdTime").gte(createTimeStart).lte(createdTimeEnd))
                .should(
                    QueryBuilders.boolQuery()
                        .must(QueryBuilders.existsQuery("policyEffectiveDate"))
                        .must(QueryBuilders.rangeQuery("policyEffectiveDate").gte(policyDateStart).lte(policyDateEnd))
                )
            );
    }

    private static void setProjectTagsQueryBuild(BoolQueryBuilder boolQueryBuilder, List<Long> projectTagIds) {
        if (Collections.isEmpty(projectTagIds)) {
            return;
        }
        BoolQueryBuilder projectTagsQuery = QueryBuilders.boolQuery()
            .should(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("projectTags", projectTagIds)));
        if (projectTagIds.contains(ProjectTags.NA)) {
            projectTagsQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("projectTags")));
        }

        boolQueryBuilder.must(projectTagsQuery);
    }

    private static void setPolicyEffectiveDateQuery(BoolQueryBuilder boolQueryBuilder, String policyEffectiveDateStart,
        String policyEffectiveDateEnd) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(policyEffectiveDateStart) || StringUtils.isNotBlank(policyEffectiveDateEnd)) {
            queryBuilder.must(QueryBuilders.existsQuery("policyEffectiveDate"));
        }
        if (StringUtils.isNotBlank(policyEffectiveDateStart)) {
            queryBuilder.must(QueryBuilders.rangeQuery("policyEffectiveDate")
                .gte(LocalDateTime.of(LocalDate.parse(policyEffectiveDateStart), LocalTime.MIN).toString()));
        }
        if (StringUtils.isNotBlank(policyEffectiveDateEnd)) {
            queryBuilder.must(QueryBuilders.rangeQuery("policyEffectiveDate")
                .lte(LocalDateTime.of(LocalDate.parse(policyEffectiveDateEnd), LocalTime.MAX).toString()));
        }
        boolQueryBuilder.must(queryBuilder);
    }

    private static void setPilotQuery(BoolQueryBuilder boolQueryBuilder, List<String> pilots) {
        if (CollectionUtils.isEmpty(pilots)) {
            return;
        }
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        for (String pilot : pilots) {
            queryBuilder.should(QueryBuilders.matchPhraseQuery("pilotName", pilot));
        }
        boolQueryBuilder.must(queryBuilder);
    }

    public static void setTimeLinesStatusQuery(
            BoolQueryBuilder boolQueryBuilder, List<Integer> status, Long startTime, Long endTime) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        ofNullable(status)
                .ifPresent(
                        e ->
                                queryBuilder.must(
                                        QueryBuilders.termsQuery("timeLines.status.code", status)));

        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("timeLines.createdTime");
        ofNullable(startTime).ifPresent(e -> rangeQueryBuilder.gte(startTime));
        ofNullable(endTime).ifPresent(e -> rangeQueryBuilder.lte(endTime));
        queryBuilder.must(rangeQueryBuilder);

        NestedQueryBuilder nestedQueryBuilder =
                QueryBuilders.nestedQuery("timeLines", queryBuilder, ScoreMode.Avg);

        boolQueryBuilder.must(nestedQueryBuilder);
    }

    public static TermsQueryBuilder getReworkExclusionStatus() {
        return QueryBuilders.termsQuery(
            "projectStatus",
            List.of(
                AiProjectStatusEnum.PROJECT_ARCHIVED.getCode(),
                AiProjectStatusEnum.PROJECT_DELETED.getCode(),
                AiProjectStatusEnum.PROJECT_CANCELED.getCode(),
                AiProjectStatusEnum.CLIENT_RECEIVED.getCode()));
    }

    public static NestedQueryBuilder getTimeLineStatusQuery(AiProjectStatusEnum status) {
        return QueryBuilders.nestedQuery(
            "timeLines",
            QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("timeLines.status.code", status.getCode())),
            ScoreMode.Avg);
    }

    private static Script getFilterScript(String fieldName, List<String> fieldValues) {
        Map<String, Object> params = Map.of(
            "fieldValues", fieldValues.stream().map(StringUtils::lowerCase).collect(Collectors.toList())
        );
        var filterScripted = "" +
            "if (doc['" + fieldName + ".keyword'].size() != 0) {" +
            "   for (fieldValue in params['fieldValues']) {" +
            "       if (doc['" + fieldName + ".keyword'].value.toLowerCase().startsWith(fieldValue)) {" +
            "           return true;" +
            "       }" +
            "   }" +
            "}" +
            "return false;";
        return new Script(ScriptType.INLINE, "painless", filterScripted, params);
    }

    /**
     * Filter by whether the attribute value fieldName starts with fieldValue
     * @param fieldName field name
     * @param fieldValue field value
     * @return
     */
    private static Script getFilterScript(String fieldName, String fieldValue){
        Map<String, Object> params = Map.of("value", StringUtils.lowerCase(fieldValue));
        String  filterScripted = "if (doc['" + fieldName + ".keyword'].size() != 0) { return doc['"+ fieldName +
            ".keyword'].value.toLowerCase().startsWith(params['value']); } return false;";
        Script script = new Script(ScriptType.INLINE, "painless", filterScripted, params);
        return script;
    }
}
