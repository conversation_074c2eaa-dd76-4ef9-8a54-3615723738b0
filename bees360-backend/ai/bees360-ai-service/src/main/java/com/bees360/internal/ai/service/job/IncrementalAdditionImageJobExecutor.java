package com.bees360.internal.ai.service.job;

import com.bees360.api.AbortedException;
import com.bees360.api.InvalidArgumentException;
import com.bees360.entity.enums.PipelineTaskEnum;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.entity.enums.ProjectSyncPointEnum;
import com.bees360.internal.ai.event.AiImageDownloadEvent;
import com.bees360.internal.ai.service.ProjectImageManager;
import com.bees360.job.Job;
import com.bees360.job.JobExecutor;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/** <AUTHOR> */
@Log4j2
@Component(IncrementalAdditionImageJobExecutor.EXECUTOR_NAME)
public class IncrementalAdditionImageJobExecutor implements JobExecutor {

    public static final String EXECUTOR_NAME = "incrementalAdditionImageJob";

    @Autowired private ProjectImageManager projectImageManager;

    @Autowired private ApplicationEventPublisher applicationEventPublisher;

    @Autowired private PipelineService pipelineService;

    @Override
    public String getName() {
        return EXECUTOR_NAME;
    }

    @Override
    public void execute(Job job) {
        IncrementalAdditionImageJob incrementalAdditionImageJob =
                IncrementalAdditionImageJob.decode(job.getPayload());
        long projectId = incrementalAdditionImageJob.getProjectId();
        ProjectSyncPointEnum syncPoint = incrementalAdditionImageJob.getSyncPoint();
        int serviceType = incrementalAdditionImageJob.getServiceType();
        List<ProjectImage> projectImages = incrementalAdditionImageJob.getImages();
        var imageRooms = incrementalAdditionImageJob.getImageRooms();
        try {
            projectImages = projectImageManager.updateImagesFromWeb(projectId, projectImages);
            List<ProjectImage> newImages =
                    projectImageManager.saveNewImages(projectId, projectImages);
            log.info(
                    "Success saved new images {} with project {} syncPoint {}",
                    Arrays.toString(
                            Optional.ofNullable(newImages)
                                    .map(
                                            images ->
                                                    images.stream()
                                                            .map(ProjectImage::getS3Key)
                                                            .toArray())
                                    .orElse(null)),
                    projectId,
                    syncPoint.getCode());
            setTransferExteriorImagesToAiStatus(projectId);

            applicationEventPublisher.publishEvent(new AiImageDownloadEvent(this, newImages, imageRooms, projectId, syncPoint));
        } catch (Exception e) {
            log.info("IncrementalAdditionImage error.projectId:{}", projectId, e);
            throw new AbortedException(e.getMessage(), e);
        }
    }

    private void setTransferExteriorImagesToAiStatus(long projectId) {
        var pipeline = pipelineService.findById(String.valueOf(projectId));
        if (pipeline == null) {
            return;
        }

        var key = PipelineTaskEnum.TRANSFER_EXTERIOR_IMAGES_TO_AI.getKey();
        var status = Message.PipelineStatus.DONE;
        try {
            pipelineService.setTaskStatus(String.valueOf(projectId), key, status);
            log.info("Successfully set pipeline '{}' key '{}' to '{}'", projectId, key, status);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Failed set pipeline '{}' key '{}' to '{}'", projectId, key, status, e);
        }
    }
}
