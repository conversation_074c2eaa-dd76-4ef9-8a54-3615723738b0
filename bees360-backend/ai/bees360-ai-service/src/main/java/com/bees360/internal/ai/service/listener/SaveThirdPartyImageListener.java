package com.bees360.internal.ai.service.listener;

import com.bees360.ai.mapper.ProjectImageMapper;
import com.bees360.event.registry.ImageAdded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.image.Message;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.entity.consts.AiBotUser;
import com.bees360.internal.ai.entity.enums.FileSourceTypeEnum;
import com.bees360.internal.ai.entity.enums.ImageTypeEnum;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.google.common.base.Preconditions;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.imaging.ImageReadException;
import org.apache.commons.imaging.Imaging;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 监听ImageAdded事件,将第三方Image同步至AI端
 */
@Log4j2
@Component
@ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        name = "enable-save-third-party-image",
        havingValue = "true")
public class SaveThirdPartyImageListener extends AbstractNamedEventListener<ImageAdded> {
    private final Pattern categoryPattern;
    private final ProjectImageMapper projectImageMapper;

    private final ResourcePool resourcePool;

    public SaveThirdPartyImageListener(
            @Value(
                    "${bees360.save-third-party-image.pattern:third_party/(?<projectId>.*)/(?<resourceType>.*)/(?<resourceName>.*)}")
            String pattern,
            ProjectImageMapper projectImageMapper,
            ResourcePool resourcePool) {
        this.projectImageMapper = projectImageMapper;
        this.categoryPattern = Pattern.compile(pattern);
        this.resourcePool = resourcePool;
        log.info(
                "Created {}(projectImageMapper='{}', pattern='{}').",
                this,
                this.projectImageMapper,
                this.categoryPattern);
    }

    private String getImageResourceKey(
            String imageId, Message.ImageMessage.Resource.Type imageType) {
        return "image/" + imageId + "/" + imageType.getNumber();
    }

    private ProjectImage generateImage(
            String imageId, String resourceUrl, String projectId, int width, int height, int fileSize) {
        ProjectImage image = ProjectImage.generateDefaultImage();
        image.setImageId(imageId);
        image.setUserId(AiBotUser.AI_ID);
        image.setFileName(resourceUrl);
        image.setOriginalFileName(resourceUrl);
        image.setFileNameMiddleResolution(
                getImageResourceKey(imageId, Message.ImageMessage.Resource.Type.MIDDLE));
        image.setFileNameLowerResolution(
                getImageResourceKey(imageId, Message.ImageMessage.Resource.Type.SMALL));
        image.setProjectId(Long.parseLong(projectId));
        image.setImageType(ImageTypeEnum.OVERVIEW.getCode());
        image.setFileSourceType(FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode());
        image.setShootingTime(Instant.now().toEpochMilli());
        image.setUploadTime(Instant.now().toEpochMilli());
        image.setImageWidth(width);
        image.setImageHeight(height);
        image.setFileSize(fileSize);
        image.setImageSort(ProjectImage.DEFAULT_SORT);
        return image;
    }

    @Override
    public void handle(ImageAdded event) throws IOException {
        Matcher matcher = categoryPattern.matcher(event.getCategory());
        if (!matcher.matches()) {
            return;
        }
        String projectId = matcher.group("projectId");
        String resourceName = matcher.group("resourceName");
        Preconditions.checkArgument(
                StringUtils.isNoneEmpty(projectId, resourceName),
                "save third party image resource must have project id and resource");
        log.info("save third party image resource '{}' to project {}.", resourceName, projectId);

        var imageId = event.getId();
        String resourceUrl = event.getGetUrl();
        Resource resource = resourcePool.get(resourceUrl);
        var imageSize = resource.apply(in -> {
            try {
                return Imaging.getImageSize(in, imageId);
            } catch (ImageReadException e) {
                throw new IllegalStateException(
                    "Cannot read file '%s', '%s'.".formatted(imageId, resourceUrl), e);
            }
        });
        int width = imageSize.width;
        int height = imageSize.height;

        log.info("get imageId '{}':  imageWidth: {}, imageHeight: {}", imageId, width, height);

        var contentLength = resource.getMetadata().getContentLength();
        Preconditions.checkState(
                Objects.nonNull(contentLength),
                "The image '{}' content length should not be null",
                imageId);
        projectImageMapper.saveAll(
                List.of(
                        generateImage(
                                imageId,
                                resourceUrl,
                                projectId,
                                width,
                                height,
                                Math.toIntExact(contentLength))));
    }
}
