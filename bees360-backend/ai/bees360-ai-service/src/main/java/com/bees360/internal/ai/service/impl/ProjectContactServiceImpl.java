package com.bees360.internal.ai.service.impl;

import com.bees360.commons.elasticsearchsupport.ElasticSearchHelper;
import com.bees360.entity.User;
import com.bees360.internal.ai.entity.ExternalMember;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.enums.UserAuthEnum;
import com.bees360.internal.ai.service.ProjectContactService;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.util.Functions;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;
import java.util.ArrayList;
import java.util.Optional;
import java.util.stream.Collectors;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class ProjectContactServiceImpl implements ProjectContactService {

    private static final String EXTERNAL_ADJUSTER_ROLE_IN_CONTACT = "External Adjuster";
    private static final String EXTERNAL_ADJUSTER_ROLE_IN_USER_AUTH = UserAuthEnum.EXTERNAL_ADJUSTER.getAuth();

    private final ContactManager contactManager;
    private final ElasticSearchHelper elasticSearchHelper;

    public ProjectContactServiceImpl(
            @NonNull ContactManager contactManager,
            @NonNull ElasticSearchHelper elasticSearchHelper) {
        this.contactManager = contactManager;
        this.elasticSearchHelper = elasticSearchHelper;
    }

    @Override
    public ExternalMember findExternalAdjuster(long projectId) {
        var externalAdjuster = findExternalAdjusterFromContact(projectId);
        if (externalAdjuster != null) {
            return externalAdjuster;
        }
        var externalAdjusterFromEs = findExternalAdjusterFromEs(projectId);
        if (externalAdjusterFromEs != null) {
            updateContact(projectId + "", externalAdjusterFromEs, EXTERNAL_ADJUSTER_ROLE_IN_CONTACT, User.AI_ID + "");
        }
        return externalAdjusterFromEs;
    }

    private ExternalMember findExternalAdjusterFromContact(long projectId) {
        var contacts = contactManager.findByProjectId(projectId + "");
        for (var contact: contacts) {
            if (StringUtils.equals(contact.getRole(), EXTERNAL_ADJUSTER_ROLE_IN_CONTACT)) {
                return convertToExternalMember(contact, EXTERNAL_ADJUSTER_ROLE_IN_USER_AUTH);
            }
        }
        return null;
    }

    private ExternalMember findExternalAdjusterFromEs(long projectId) {
        var project = findProjectByProjectId(projectId);
        if (project.getExternalMember() == null) {
            return null;
        }
        for (var member: project.getExternalMember()) {
            if (member != null && StringUtils.equals(member.getAuth(), EXTERNAL_ADJUSTER_ROLE_IN_USER_AUTH)) {
                return member;
            }
        }
        return null;
    }

    private ProjectEsModel findProjectByProjectId(long projectId) {
        if (!elasticSearchHelper.indexExist(ProjectEsModel.class)) {
            return null;
        }
        return elasticSearchHelper.getById(projectId + "", ProjectEsModel.class).orElse(null);
    }

    @Override
    public void updateExternalAdjuster(long projectId, ExternalMember externalAdjuster, String opUserId) {

        Preconditions.checkArgument(StringUtils.equals(externalAdjuster.getAuth(), EXTERNAL_ADJUSTER_ROLE_IN_USER_AUTH),
            "Require role %s for updating external adjuster.", EXTERNAL_ADJUSTER_ROLE_IN_USER_AUTH);
        var project = findProjectByProjectId(projectId);
        updateExternalMember(project, externalAdjuster);
        updateContact(projectId + "", externalAdjuster, EXTERNAL_ADJUSTER_ROLE_IN_CONTACT, opUserId);
    }

    private void updateContact(String projectId, ExternalMember externalMember, String contactRole, String opUserId) {
        var contacts = contactManager.findByProjectId(projectId);
        var contactsWithRole = Iterables.toStream(contacts)
            .filter(c -> StringUtils.equals(contactRole, c.getRole()))
            .collect(Collectors.toList());
        for (var contact: contactsWithRole) {
            contactManager.removeContact(contact.getId(), opUserId);
        }
        contactManager.addContact(projectId, convertToContact(null, externalMember, contactRole), opUserId);
    }

    private void updateExternalMember(ProjectEsModel project, ExternalMember externalMember) {
        var externalMembers = Optional.ofNullable(project.getExternalMember()).orElse(new ArrayList<>());
        externalMembers = externalMembers.stream()
            .filter(em -> !StringUtils.equals(em.getAuth(), externalMember.getAuth())).collect(Collectors.toList());
        externalMembers.add(externalMember);
        project.setExternalMember(externalMembers);
        elasticSearchHelper.update(project);
    }

    private ExternalMember convertToExternalMember(Contact contact, @Nullable String userAuth) {
        var member = new ExternalMember();
        userAuth = Optional.ofNullable(userAuth).orElse(contact.getRole());
        member.setAuth(userAuth);
        member.setName(contact.getFullName());
        member.setEmail(contact.getPrimaryEmail());
        member.setPhone(contact.getPrimaryPhone());
        return member;
    }

    private Contact convertToContact(@Nullable String contactId, ExternalMember externalMember, @Nullable String contactRole) {
        var builder = ProjectMessage.Contact.newBuilder();
        contactRole = Optional.ofNullable(contactRole).orElse(externalMember.getAuth());
        Functions.acceptIfNotNull(builder::setId, contactId);
        Functions.acceptIfNotNull(builder::setFullName, externalMember.getName());
        Functions.acceptIfNotNull(builder::setPrimaryEmail, externalMember.getEmail());
        Functions.acceptIfNotNull(builder::setPrimaryPhone, externalMember.getPhone());
        Functions.acceptIfNotNull(builder::setRole, contactRole);
        return Contact.of(builder.build());
    }
}
