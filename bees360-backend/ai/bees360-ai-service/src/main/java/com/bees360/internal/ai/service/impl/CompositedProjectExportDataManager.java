package com.bees360.internal.ai.service.impl;

import com.bees360.internal.ai.entity.BsExportData;
import com.bees360.internal.ai.entity.ProjectExportData;
import com.bees360.internal.ai.service.BsExportDataService;
import com.bees360.internal.ai.service.ProjectExportDataManager;
import com.google.common.base.Preconditions;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * composite mybatisProjectExportDataManager with BsExportDataService for data compatibility
 */
@Service
public class CompositedProjectExportDataManager implements ProjectExportDataManager {

    private ProjectExportDataManager mybatisProjectExportDataManager;
    private BsExportDataService bsExportDataService;

    public CompositedProjectExportDataManager(ProjectExportDataManager mybatisProjectExportDataManager,
        BsExportDataService bsExportDataService) {
        this.mybatisProjectExportDataManager = mybatisProjectExportDataManager;
        this.bsExportDataService = bsExportDataService;
    }

    @Override
    public ProjectExportData insertOrUpdateData(ProjectExportData projectExportData) {
        return mybatisProjectExportDataManager.insertOrUpdateData(projectExportData);
    }

    @Override
    public void deleteExportData(String relatedId, String relatedType) {
        Optional.ofNullable(mybatisProjectExportDataManager.getByRelatedIdAndType(relatedId, relatedType))
            .ifPresentOrElse(
                data -> mybatisProjectExportDataManager.deleteExportData(relatedId, relatedType),
                () -> bsExportDataService.deleteExportData(relatedId, relatedType)
            );
    }

    @Override
    public ProjectExportData getByRelatedIdAndType(String relatedId, String relatedType) {
        Preconditions.checkNotNull(relatedId, "relatedId should not be null.");
        Preconditions.checkNotNull(relatedType, "relatedType should not be null.");
        return Optional.ofNullable(mybatisProjectExportDataManager.getByRelatedIdAndType(relatedId, relatedType))
            .orElseGet(() -> {
                BsExportData bsExportData = bsExportDataService.getByRelatedIdAndType(relatedId, relatedType);
                return convert(bsExportData);
            });
    }

    private ProjectExportData convert(BsExportData bsExportData) {
        if(bsExportData == null) {
            return null;
        }
        ProjectExportData projectExportData = new ProjectExportData();
        projectExportData.setRelatedId(bsExportData.getRelatedId());
        projectExportData.setRelatedType(bsExportData.getRelatedType());
        projectExportData.setDataLog(bsExportData.getDataLog());
        return projectExportData;
    }
}
