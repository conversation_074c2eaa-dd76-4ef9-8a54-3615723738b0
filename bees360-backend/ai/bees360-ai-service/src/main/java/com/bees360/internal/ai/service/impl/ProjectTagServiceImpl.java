package com.bees360.internal.ai.service.impl;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.commons.elasticsearchsupport.ElasticSearchHelper;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.common.msgcode.MsgCodeManager;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectTag;
import com.bees360.internal.ai.entity.ProjectTagEs;
import com.bees360.internal.ai.service.ProjectTagService;
import com.bees360.user.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/02/24 10:48
 */
@Service
public class ProjectTagServiceImpl implements ProjectTagService {

    @Autowired private ElasticSearchHelper elasticSearchHelper;

    @Autowired private ActivityManager activityManager;
    final static String ACTIVITY_VALUE_DELIMITER = ";;";

    @Override
    public void insertOrUpdateProjectTag(User user, long projectId, List<String> tagItem) {
        if (tagItem.size() > 4) {
            throw new ServiceException(
                    MsgCodeManager.REQUEST.PARAMETER_INVALID.CODE, "tags only support 4 items");
        }
        ProjectTag projectTag = getById(projectId);
        if (Objects.isNull(projectTag)) {
            projectTag = new ProjectTag();
            projectTag.setProjectId(projectId);
        }
        projectTag.setTags(tagItem);
        elasticSearchHelper.update(projectTag);
        submitActivityOnProjectTagChanged(projectId, user, tagItem);
    }

    @Override
    public List<ProjectTag> getByProjectIds(List<Long> projectIds) {
        var projectEsModels = elasticSearchHelper.searchListResultById(
            projectIds.stream().map(String::valueOf).collect(Collectors.toList()),
            ProjectEsModel.class);
        return projectEsModels.stream()
                .filter(
                        e ->
                                org.apache.commons.collections4.CollectionUtils.isNotEmpty(
                                        e.getProjectTagList()))
                .map(
                        e -> {
                            var tagNameList =
                                    e.getProjectTagList().stream()
                                            .map(ProjectTagEs::getTitle)
                                            .collect(Collectors.toList());
                            ProjectTag tag = new ProjectTag();
                            tag.setProjectId(e.getProjectId());
                            tag.setTags(tagNameList);
                            return tag;
                        })
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<String>> getTagsByProjectIds(List<Long> projectIds) {
        List<ProjectTag> projectTags = getByProjectIds(projectIds);
        if (CollectionUtils.isEmpty(projectTags)) {
            return Collections.emptyMap();
        }
        return projectTags.stream()
                .collect(
                        Collectors.toMap(
                                ProjectTag::getProjectId, ProjectTag::getTags, (v1, v2) -> v1));
    }

    @Override
    public void deleteProjectTag(User user, long projectId) {
        elasticSearchHelper.deleteById(projectId + "", ProjectTag.class);
        submitActivityOnProjectTagChanged(projectId, user, Collections.emptyList());
    }

    private ProjectTag getById(long projectId) {
        return elasticSearchHelper.getById(projectId + "", ProjectTag.class).orElse(null);
    }

    private void submitActivityOnProjectTagChanged(
            long projectId, User user, List<String> tagItem) {
        String tags = String.join(ACTIVITY_VALUE_DELIMITER, tagItem);
        Message.ActivityMessage message =
                Message.ActivityMessage.newBuilder()
                        .setProjectId(projectId)
                        .setAction(Message.ActivityMessage.ActionType.CHANGE.name())
                        .setEntity(
                                Message.ActivityMessage.Entity.newBuilder()
                                        .setId(String.valueOf(projectId))
                                        .setType(Message.ActivityMessage.EntityType.PROJECT.name())
                                        .build())
                        .setField(
                                Message.ActivityMessage.Field.newBuilder()
                                        .setName(Message.ActivityMessage.FieldName.TAG.name())
                                        .setType(Message.ActivityMessage.FieldType.STRING.name())
                                        .setValue(tags)
                                        .build())
                        .setSource(ActivitySourceEnum.AI.getValue())
                        .setCreatedBy(
                                com.bees360.user.Message.UserMessage.newBuilder()
                                        .setId(Objects.requireNonNull(user.getId()))
                                        .build())
                        .build();
        activityManager.submitActivity(Activity.of(message));
    }
}
