package com.bees360.internal.ai.service.entity.query;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.util.LinkedList;
import java.util.Map.Entry;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * dashboard es 搜索项
 * <AUTHOR>
 * @date 2020/05/06 14:23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ProjectEsQueryParam  implements Cloneable {

    private Long projectId;

    private List<Long> projectIds;

    private String policyNumber;

    private String address;

    private String city;

    private String state;

    private List<String> states;

    private Integer projectStatus;

    private List<Integer> projectStatusList;

    /**
     * claim/inspection number
     */
    private String inspectionNumber;

    private List<String> companyName;

    private List<Long> insuranceCompanyIds;

    private List<Long> excludeCompanyIds;

    private String assetOwnerName;
    private String assetOwnerPhone;

    private String policyEffectiveDateStart;
    private String policyEffectiveDateEnd;

    private List<String> pilot;

    private Long createStartTime;
    private Long createEndTime;

    private Long inspectionStartTime;
    private Long inspectionEndTime;

    private String roleTag;

    private String userId;

    private String sortTag;

    private List<Integer> serviceTypes;

    private List<Long> projectTags;

    private boolean searchDaysOld;

    private Long daysOldStart;

    private Long daysOldEnd;

    private String orderField;

    private String orderDesc;

    private String statusTag;

    private int pageIndex = 1;

    private int pageSize = 200;

    private String creatorName;

    private String memberRole;

    private String memberName;

    private Boolean isClaimSearch;

    private boolean isSearchAll;

    private boolean isContainsDeleted;

    private boolean isExport;

    private List<Integer> filterStatuses;

    private String darHandler;
    private List<Long> darProjectIds;
    private String estimateHandler;
    private List<Long> estimateProjectIds;

    private Long siteInspectionStartTime;
    private Long siteInspectionEndTime;

    private String zipCode;

    private Long projectStatusStartTime;
    private Long projectStatusEndTime;

    private List<String> projectTagList;

    private List<Integer> darStatusList;

    private List<Integer> pirStatusList;

    /** 自定义条件,must */
    private List<Entry<String, Object>> mustCriteria = new LinkedList<>();
    private List<Entry<String, Object>> mustNotCriteria = new LinkedList<>();

    private String catSerialNumber;

    private List<Integer> claimTypes;

    private List<Integer> projectTypes;

    private List<String> insuranceCompanyName;

    private List<String> processCompanyName;

    private List<String> userKeys;

    private List<String> operatingCompanyList;

    private List<String> policyTypeList;

    private List<String> projectState;

    private String projectStateChangeReason;

    private List<String> projectStateChangeReasonIds;

    private List<String> changeReasonGroups;

    private Integer droneImageCountMax;
    private Integer droneImageCountMin;
    private Integer mobileImageCountMax;
    private Integer mobileImageCountMin;

    private List<String> policyNumbers;

    private List<String> inspectionNumbers;

    private Boolean isParentProject;

    private Boolean isChildProject;

    public void addMustCriteria(Entry<String, Object> entry) {
        if (mustCriteria == null) {
            mustCriteria = new LinkedList<>();
        }
        mustCriteria.add(entry);
    }

    public void addMustNotCriteria(Entry<String, Object> entry) {
        if (mustNotCriteria == null) {
            mustNotCriteria = new LinkedList<>();
        }
        mustNotCriteria.add(entry);
    }

    @Override
    public ProjectEsQueryParam clone() {
        Gson gson = new GsonBuilder().create();
        return gson.fromJson(gson.toJson(this), ProjectEsQueryParam.class);
    }


}
