package com.bees360.internal.ai.service.impl;

import static com.bees360.internal.ai.entity.enums.ClaimTypeEnum.UNDERWRITING_FIRST_INSPECTION;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivityQuery;
import com.bees360.activity.Message;
import com.bees360.activity.Message.ActivityMessage.EntityType;
import com.bees360.activity.Message.ActivityMessage.FieldName;
import com.bees360.address.AddressHiveLocationProvider;
import com.bees360.address.AddressProvider;
import com.bees360.catastrophe.ClaimCatastrophe;
import com.bees360.commons.elasticsearchsupport.ElasticSearchHelper;
import com.bees360.commons.elasticsearchsupport.entity.EsPage;
import com.bees360.commons.elasticsearchsupport.util.ElasticSearchUtil;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.ProjectReportRecordEnum;
import com.bees360.event.EventPublisher;
import com.bees360.event.registry.PilotRatedEvent;
import com.bees360.event.registry.ProjectMemberChangedEvent;
import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.common.msgcode.MsgCodeManager;
import com.bees360.internal.ai.entity.Airspace;
import com.bees360.internal.ai.entity.ExternalMember;
import com.bees360.internal.ai.entity.LogEntry;
import com.bees360.internal.ai.entity.LogEntryDetail;
import com.bees360.internal.ai.entity.MemberInfo;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.entity.ProjectListResult;
import com.bees360.internal.ai.entity.ProjectListResult.StatusTag;
import com.bees360.internal.ai.entity.ProjectLog;
import com.bees360.internal.ai.entity.ProjectNum;
import com.bees360.internal.ai.entity.ProjectStatusTagNum;
import com.bees360.internal.ai.entity.ProjectStatusVo;
import com.bees360.internal.ai.entity.consts.DashboardSortTag;
import com.bees360.internal.ai.entity.dto.PageResult;
import com.bees360.internal.ai.entity.dto.Pagination;
import com.bees360.internal.ai.entity.enums.AiBotUserEnum;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.ClaimTypeEnum;
import com.bees360.internal.ai.entity.enums.DashBoardRoleTagToDoEnum;
import com.bees360.internal.ai.entity.enums.FileSourceTypeEnum;
import com.bees360.internal.ai.entity.enums.LogAiFlowActionEnum;
import com.bees360.internal.ai.entity.enums.LogAiFlowDetailEnum;
import com.bees360.internal.ai.entity.enums.LogEntryTypeEnum;
import com.bees360.internal.ai.entity.enums.LogHoverPlnarStatusDetailEnum;
import com.bees360.internal.ai.entity.enums.LogProjectDataActionEnum;
import com.bees360.internal.ai.entity.enums.LogProjectDataDetailEnum;
import com.bees360.internal.ai.entity.enums.LogReportActionEnum;
import com.bees360.internal.ai.entity.enums.LogReportDetailEnum;
import com.bees360.internal.ai.entity.enums.PayStatusEnum;
import com.bees360.internal.ai.entity.enums.ProjectHoverStatusEnum;
import com.bees360.internal.ai.entity.enums.ProjectOptionDictTypeEnum;
import com.bees360.internal.ai.entity.enums.ProjectPlnarStatusEnum;
import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.internal.ai.entity.enums.ProjectUserFolderTypeEnum;
import com.bees360.internal.ai.entity.enums.ReportTypeEnum;
import com.bees360.internal.ai.entity.enums.UserAuthEnum;
import com.bees360.internal.ai.entity.vo.ItemResult;
import com.bees360.internal.ai.entity.vo.ProjectCalQueryVo;
import com.bees360.internal.ai.entity.vo.RoleQueryResult;
import com.bees360.internal.ai.event.ProjectHoverStatusChangedEvent;
import com.bees360.internal.ai.event.ProjectTagChangedEvent;
import com.bees360.internal.ai.grpc.ProjectServiceOuterClass.UpdateProjectBatchItem;
import com.bees360.internal.ai.service.AiUserService;
import com.bees360.internal.ai.service.ProjectContactService;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectFolderService;
import com.bees360.internal.ai.service.ProjectImageService;
import com.bees360.internal.ai.service.ProjectLogService;
import com.bees360.internal.ai.service.ProjectOptionDictService;
import com.bees360.internal.ai.service.ProjectStatusService;
import com.bees360.internal.ai.service.ProjectTagService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.internal.ai.service.entity.EsRequestConverter;
import com.bees360.internal.ai.service.entity.query.ProjectEsQueryParam;
import com.bees360.internal.ai.service.firebase.FirebaseProjectService;
import com.bees360.internal.ai.util.date.DateUtil;
import com.bees360.job.registry.SerializableFirebaseProject;
import com.bees360.project.airspace.ProjectAirspaceManager;
import com.bees360.project.claim.ClaimManager;
import com.bees360.project.claim.ProjectCatastropheManager;
import com.bees360.project.member.MemberManager;
import com.bees360.project.member.RoleEnum;
import com.bees360.project.state.ChangeReasonFromCategoryProvider;
import com.bees360.project.state.ProjectStateChangeReason;
import com.bees360.project.state.ProjectStateChangeReasonManager;
import com.bees360.user.User;
import com.bees360.user.UserKeyProvider;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import com.bees360.util.Null;
import com.google.type.Money;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.client.core.CountRequest;
import org.opensearch.index.query.QueryBuilder;
import org.opensearch.search.sort.ScriptSortBuilder.ScriptSortType;
import org.opensearch.search.sort.SortBuilder;
import org.opensearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/05/06 12:22
 */
@Slf4j
@Service
public class ProjectEsServiceImpl implements ProjectEsService {

    @Autowired
    private ProjectStatusService projectStatusService;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private ElasticSearchHelper elasticSearchHelper;

    @Autowired
    private ProjectLogService projectLogService;

    @Autowired
    private ProjectImageService projectImageService;

    @Autowired
    private ProjectTagService projectTagService;
    @Autowired
    private Bees360FeatureSwitch bees360FeatureSwitch;

    @Autowired
    private AiUserService aiUserService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private ProjectFolderService projectFolderService;

    @Autowired
    private ProjectOptionDictService projectOptionDictService;

    @Autowired
    private ActivityManager activityManager;

    @Autowired
    private FirebaseProjectService firebaseProjectService;

    @Autowired
    private EventPublisher eventPublisher;

    @Autowired private AddressHiveLocationProvider addressHiveLocationProvider;

    @Autowired
    private ProjectContactService projectContactService;

    @Autowired
    private UserKeyProvider userKeyProvider;

    @Autowired
    private AddressProvider addressProvider;

    @Autowired
    private ProjectCatastropheManager projectCatastropheManager;

    @Autowired
    private ProjectAirspaceManager projectAirspaceManager;

    @Autowired
    private ProjectStateChangeReasonManager projectStateChangeReasonManager;

    @Autowired
    private ChangeReasonFromCategoryProvider changeReasonFromCategoryProvider;

    @Autowired
    private ClaimManager claimManager;

    @Autowired
    private MemberManager memberManager;

    private final static List<RoleEnum> SYNC_AI_MEMBER_AUTHS = List.of(RoleEnum.PROCESSOR, RoleEnum.ADJUSTER, RoleEnum.REVIEWER, RoleEnum.PRODUCER);

    @Override
    public PageResult<ProjectEsModel> getProjectPageResult(ProjectEsQueryParam param, User user) {
        // todo标签角色 -> 状态划分
        PageResult<ProjectEsModel> pageResult = PageResult.empty(param.getPageIndex(), param.getPageSize());

        DashBoardRoleTagToDoEnum toDoEnum = DashBoardRoleTagToDoEnum.getByRoleTag(Optional.ofNullable(param.getRoleTag()));
        // 取出所有projects,过滤出符合param的projects
        if ((!UserAuthEnum.hasRole(user.getAllAuthority(), UserAuthEnum.ROLE_ADMIN)
            && !UserAuthEnum.hasRole(user.getAllAuthority(), UserAuthEnum.ROLE_DEVELOPER))
            || !StringUtils.equals(toDoEnum.getRoleTag(), DashBoardRoleTagToDoEnum.ADMIN_TODO.getRoleTag())) {

            // 如果不是管理员：只查找当前标签和用户下的project
            // 即使自身是admin角色，但是查看的是其他标签下的project列表
            if (bees360FeatureSwitch.isEnableUserKey()) {
                List<String> userKey =
                        Iterables.toStream(userKeyProvider.findUserKeysByKey(user.getId()))
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList());
                param.setUserKeys(userKey);
            } else {
                param.setUserId(user.getId());
            }
        }
        if (!elasticSearchHelper.indexExist(ProjectEsModel.class)) {
            return pageResult;
        }

        if (checkAndSetChangeReasonCondition(param)) {
            return pageResult;
        }

        EsPage<ProjectEsModel> projectPage = getProjectEsPageData(param);

        List<ProjectEsModel> projectList = projectPage.getItems();
        if (CollectionUtils.isEmpty(projectList)) {
            return pageResult;
        }
        if (bees360FeatureSwitch.isEnableFillTimeZoneWhenGetProject()) {
            setTimeZone(projectList);
        }
        setProjectStateChangeReason(projectList);
        setChangeReasonGroup(projectList);
        setNeedInfo(projectList, user);
        replaceExternalAdjuster(projectList);

        return PageResult.fetchPage(pageResult, projectList,
            param.getPageIndex(), param.getPageSize(), Integer.parseInt(projectPage.getTotal() + ""));
    }

    /**
     * 如果 change_reason 和 change_reason_group 筛选条件都不为空,
     * 那么 change_reason_group 所含有的全部 reason 与 change_reason 不存在交集时，应该返回空
     *
     * @param param param
     * @return true: 返回空; false: 查询 ES
     */
    private boolean checkAndSetChangeReasonCondition(ProjectEsQueryParam param) {
        var changeReason = param.getProjectStateChangeReason();
        var changeReasonIds = getChangeReasonIdByDisplayText(changeReason);
        if (StringUtils.isNotEmpty(changeReason) && CollectionUtils.isEmpty(changeReasonIds)) {
            // 搜索的 displayText 不存在: return empty project list
            return true;
        }
        param.setProjectStateChangeReason(null);

        var groupReasonIds = getChangeReasonIdByGroup(param.getChangeReasonGroups());

        if (CollectionUtils.isEmpty(changeReasonIds) && CollectionUtils.isEmpty(groupReasonIds)) {
            return false;
        }

        if (CollectionUtils.isNotEmpty(changeReasonIds) ^ CollectionUtils.isNotEmpty(groupReasonIds)) {
            changeReasonIds.addAll(groupReasonIds);
            param.setProjectStateChangeReasonIds(changeReasonIds);
            return false;
        }

        var intersection = CollectionUtils.intersection(changeReasonIds, groupReasonIds);
        if (CollectionUtils.isNotEmpty(intersection)) {
            param.setProjectStateChangeReasonIds(new ArrayList<>(intersection));
            return false;
        }
        // change_reason_group 所含有的全部 reason 与 change_reason 不存在交集
        return true;
    }

    private List<String> getChangeReasonIdByDisplayText(String changeReason) {
        var changeReasonIds = new ArrayList<String>();
        if (StringUtils.isNotEmpty(changeReason)) {
            changeReasonIds.addAll(Iterables.toStream(projectStateChangeReasonManager.findByQuery(List.of(), List.of(), List.of(changeReason)))
                .map(ProjectStateChangeReason::getId).collect(Collectors.toList()));
        }
        return changeReasonIds;
    }

    private List<String> getChangeReasonIdByGroup(List<String> changeReasonGroups) {
        var groupReasonIds = new ArrayList<String>();
        if (CollectionUtils.isNotEmpty(changeReasonGroups)) {
            for (String changeReasonGroup : changeReasonGroups) {
                var reasonIds = Iterables.toStream(changeReasonFromCategoryProvider.findChangeReasonByGroup(changeReasonGroup))
                    .map(ProjectStateChangeReason::getId)
                    .collect(Collectors.toList());
                groupReasonIds.addAll(reasonIds);
            }
        }
        return groupReasonIds;
    }

    /**
     * 设置 StateChangeReason, 防止 display text 一改名就需要全量刷新 AI ES 数据
     *
     * @param projectList project returned by es
     */
    private void setProjectStateChangeReason(List<ProjectEsModel> projectList) {
        var changeReasonIds = projectList.stream().map(ProjectEsModel::getProjectStateChangeReasonId)
            .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        var changeReasonIdToDisplayText = getChangeReasonIdToDisplayText(changeReasonIds);
        projectList.forEach(
            projectEsModel -> projectEsModel.setProjectStateChangeReason(
                changeReasonIdToDisplayText.getOrDefault(projectEsModel.getProjectStateChangeReasonId(), projectEsModel.getProjectStateChangeReason())
            ));
    }

    private Map<String, String> getChangeReasonIdToDisplayText(Iterable<String> changeReasonIds) {
        return Iterables.toStream(projectStateChangeReasonManager.findByQuery(changeReasonIds, List.of(), List.of()))
            .collect(Collectors.toMap(ProjectStateChangeReason::getId, ProjectStateChangeReason::getDisplayText));
    }

    private void setChangeReasonGroup(List<ProjectEsModel> projectList) {
        var reasonGroups = Iterables.toList(changeReasonFromCategoryProvider.listReasonGroup());
        var reasonIdToGroup = new HashMap<String, String>();
        for (String reasonGroup : reasonGroups) {
            Iterables.toStream(changeReasonFromCategoryProvider.findChangeReasonByGroup(reasonGroup)).forEach(e -> reasonIdToGroup.put(e.getId(), reasonGroup));
        }
        projectList.forEach(projectEsModel -> projectEsModel.setChangeReasonGroup(reasonIdToGroup.get(projectEsModel.getProjectStateChangeReasonId())));
    }

    private void setTimeZone(List<ProjectEsModel> projectList) {
        log.info("Set time zone using addressProvider");
        var addressIds =
                projectList.stream()
                        .map(ProjectEsModel::getAddressId)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
        if (addressIds.isEmpty()) {
            log.info("AddressIds is empty when setting time zone");
            return;
        }
        var address = addressProvider.findAllById(addressIds);
        var addressIdTimeZoneIdMap =
                Iterables.toStream(address)
                        .filter(a -> a.getTimeZone() != null)
                        .collect(
                                Collectors.toMap(
                                        com.bees360.address.Address::getId,
                                        a -> a.getTimeZone().getID(),
                                        (k1, k2) -> k1));
        for (var project : projectList) {
            var addressId = project.getAddressId();
            if (addressIdTimeZoneIdMap.containsKey(addressId)) {
                project.setTimeZone(addressIdTimeZoneIdMap.get(addressId));
            }
        }
    }

    public long getProjectCount(ProjectEsQueryParam param, User user) {
        DashBoardRoleTagToDoEnum toDoEnum = DashBoardRoleTagToDoEnum.getByRoleTag(Optional.ofNullable(param.getRoleTag()));
        if ((!UserAuthEnum.hasRole(user.getAllAuthority(), UserAuthEnum.ROLE_ADMIN)
            && !UserAuthEnum.hasRole(user.getAllAuthority(), UserAuthEnum.ROLE_DEVELOPER))
            || !StringUtils.equals(toDoEnum.getRoleTag(), DashBoardRoleTagToDoEnum.ADMIN_TODO.getRoleTag())) {
            param.setUserId(user.getId());
        }
        if (!elasticSearchHelper.indexExist(ProjectEsModel.class)) {
            return 0L;
        }
        return getProjectEsCount(param);
    }


    @Override
    public List<ProjectEsModel> findProjectListByQueryBuild(ProjectEsQueryParam param) {
        if (Objects.equals(param.getPageIndex(), 0)) {
            param.setPageIndex(1);
        }
        if (Objects.equals(param.getPageSize(), 0)) {
            param.setPageSize(10000);
        }
        if (!elasticSearchHelper.indexExist(ProjectEsModel.class)) {
            return Collections.emptyList();
        }
        SearchRequest searchRequest = buildSearchRequest(param);
        List<ProjectEsModel> projectEsModels =
            replaceExternalAdjuster(elasticSearchHelper.searchListResult(searchRequest, ProjectEsModel.class));
        setAirspaceIntoProjectEsModelList(projectEsModels);
        return projectEsModels;
    }

    @Override
    public ProjectStatusTagNum getProjectStatusTagNum(ProjectEsQueryParam queryParam, User user) {
        var startTime = Instant.now();
        DashBoardRoleTagToDoEnum toDoEnum = DashBoardRoleTagToDoEnum.getByRoleTag(Optional.ofNullable(queryParam.getRoleTag()));
        // 取出所有projects,过滤出符合param的projects
        if ((!UserAuthEnum.hasRole(user.getAllAuthority(), UserAuthEnum.ROLE_ADMIN)
            && !UserAuthEnum.hasRole(user.getAllAuthority(), UserAuthEnum.ROLE_DEVELOPER))
            || !StringUtils.equals(toDoEnum.getRoleTag(), DashBoardRoleTagToDoEnum.ADMIN_TODO.getRoleTag())) {

            // 如果不是管理员：只查找当前标签和用户下的project
            // 即使自身是admin角色，但是查看的是其他标签下的project列表
            queryParam.setUserId(user.getId());
        }
        if (!elasticSearchHelper.indexExist(ProjectEsModel.class)) {
            return new ProjectStatusTagNum();
        }
        ProjectNum projectNum = new ProjectNum();
        ProjectNum discussCount = new ProjectNum();

        if (checkAndSetChangeReasonCondition(queryParam)) {
            return new ProjectStatusTagNum(projectNum, discussCount);
        }

        // todo标签
        var enablePrintingAiEsCountTime = bees360FeatureSwitch.isEnablePrintingAiEsCountTime();
        var beginTime = Instant.now();
        queryParam.setStatusTag(ProjectListResult.StatusTag.TODO);
        long todoResult = getProjectCount(queryParam, user);
        projectNum.setTodoNum(todoResult);
        if (enablePrintingAiEsCountTime) {
            log.info("The todoNum data collection ends. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }

        // active
        beginTime = Instant.now();
        queryParam.setStatusTag(ProjectListResult.StatusTag.ACTIVE);
        long activeResult = getProjectCount(queryParam, user);
        projectNum.setActiveNum(activeResult);
        if (enablePrintingAiEsCountTime) {
            log.info("The activeNum data collection ends. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }

        // closed
        beginTime = Instant.now();
        queryParam.setStatusTag(ProjectListResult.StatusTag.CLOSED);
        long closedResult = getProjectCount(queryParam, user);
        projectNum.setClosedNum(closedResult);
        if (enablePrintingAiEsCountTime) {
            log.info("The closedNum data collection ends. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }

        // urgent
        beginTime = Instant.now();
        queryParam.setStatusTag(ProjectListResult.StatusTag.URGENT);
        long urgentResult = getProjectCount(queryParam, user);
        projectNum.setUrgentNum(urgentResult);
        if (enablePrintingAiEsCountTime) {
            log.info("The urgentNum data collection ends. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }

        // archived
        beginTime = Instant.now();
        queryParam.setStatusTag(StatusTag.ARCHIVED);
        long archivedNum = getProjectCount(queryParam, user);
        projectNum.setArchivedNum(archivedNum);
        if (enablePrintingAiEsCountTime) {
            log.info("The archivedNum data collection ends. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }

        // rework
        beginTime = Instant.now();
        queryParam.setStatusTag(StatusTag.REWORK);
        long reworkNum = getProjectCount(queryParam, user);
        projectNum.setReworkNum(reworkNum);
        if (enablePrintingAiEsCountTime) {
            log.info("The reworkNum data collection ends. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }

        log.info("getProjectStatusTagNum method execution ends. time consuming = {}",
                Instant.now().toEpochMilli() - startTime.toEpochMilli());
        return new ProjectStatusTagNum(projectNum, discussCount);

    }

    private SearchRequest buildSearchRequest(ProjectEsQueryParam queryParam) {
        ProjectEsQueryParam param = queryParam.clone();
        log.debug("AI project es queryParam {}", queryParam);
        setUrgentParamsToProjectIds(param);
        addQueryProjectIds(param);
        SortBuilder sortBuilder = buildSearchListOrderData(param.getOrderField(), param.getOrderDesc(),
                                                            param.getIsClaimSearch());
        QueryBuilder projectQueryBuilder = EsRequestConverter.convert(param, bees360FeatureSwitch);
        // 构建project 索引查询
        log.debug("AI project es query builder {}", projectQueryBuilder);
        SearchRequest projectRequest = ElasticSearchUtil.searchRequest(projectQueryBuilder, ProjectEsModel.class,
            Pagination.countIndexFrom(param.getPageIndex(), param.getPageSize()),
            param.getPageSize(), sortBuilder);
        return projectRequest;
    }

    private CountRequest buildCountRequest(ProjectEsQueryParam queryParam) {
        ProjectEsQueryParam param = queryParam.clone();
        var beginTime = Instant.now();
        setUrgentParamsToProjectIds(param);
        if (bees360FeatureSwitch.isEnablePrintingAiEsCountTime()) {
            log.info("The setUrgentParamsToProjectIds method ends. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }

        beginTime = Instant.now();
        addQueryProjectIds(param);
        if (bees360FeatureSwitch.isEnablePrintingAiEsCountTime()) {
            log.info("The addQueryProjectIds method ends. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }
        QueryBuilder projectQueryBuilder = EsRequestConverter.convert(param, bees360FeatureSwitch);
        // 构建project 索引查询
        return ElasticSearchUtil.countRequest(projectQueryBuilder, ProjectEsModel.class);
    }

    /**
     * 将 StageTag 为 URGENT 的查询转换为 projectIds 去查询
     * @param param ES 查询参数
     */
    private void setUrgentParamsToProjectIds(ProjectEsQueryParam param) {
        if (!StringUtils.equals(param.getStatusTag(), ProjectListResult.StatusTag.URGENT)) {
            return;
        }
        var projectIds = param.getProjectIds();
        var urgentProjectIds =
            projectFolderService.getByProjectIdsAndType(
                null, ProjectListResult.StatusTag.URGENT);
        // 如果 projectIds 参数不为空，则应该取 projectIds 和 urgentProjectIds 的交集
        if (projectIds == null) {
            projectIds = urgentProjectIds;
        } else {
            projectIds =
                    projectIds.stream()
                            .filter(urgentProjectIds::contains)
                            .collect(Collectors.toList());
        }
        param.setProjectIds(projectIds);
    }

    private void addQueryProjectIds(ProjectEsQueryParam param) {
        // dar
        var enablePrintingAiEsCountTime = bees360FeatureSwitch.isEnablePrintingAiEsCountTime();
        var beginTime = Instant.now();
        if (StringUtils.isNotBlank(param.getDarHandler())) {
            param.setDarProjectIds(getProjectIdsByActivity(param.getDarHandler(), creator ->
                ActivityQuery.builder().createdBy(creator).entityType(EntityType.REPORT
                    .name()).entityId(ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT.getCode() + "")
                    .value(LogReportActionEnum.APPROVED.getValue()).build()
            ));
        }
        if (enablePrintingAiEsCountTime) {
            log.info("Convert param's darHandler query to darProjectIds field query. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }
        // estimate
        beginTime = Instant.now();
        if (StringUtils.isNotBlank(param.getEstimateHandler())) {
            param.setEstimateProjectIds(getProjectIdsByActivity(param.getEstimateHandler(), creator ->
                ActivityQuery.builder().createdBy(creator).entityType(EntityType.PROJECT.name())
                    .fieldName(FieldName.STATUS
                        .name()).value(AiProjectStatusEnum.ESTIMATE_COMPLETED.getDisplay()).build()
            ));
        }
        if (enablePrintingAiEsCountTime) {
            log.info("Convert param's estimateHandler query to estimateProjectIds field query. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }

        // dar 文件夹
        beginTime = Instant.now();
        if (enablePrintingAiEsCountTime) {
            log.info("If statusTag is DAR, add parameters mustCriteria and mustNotCriteria to the query. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }

        // estimate 文件夹
        beginTime = Instant.now();
        if (enablePrintingAiEsCountTime) {
            log.info("If statusTag is ESTIMATE, add parameters mustCriteria and mustNotCriteria to the query. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }

        // rework folder
        beginTime = Instant.now();
        if (StringUtils.equals(param.getStatusTag(), ProjectListResult.StatusTag.REWORK)) {
            param.addMustCriteria(
                    new AbstractMap.SimpleEntry<>(
                            "projectId", projectFolderService.getReworkProjectIds()));
        }
        if (enablePrintingAiEsCountTime) {
            log.info("If statusTag is REWORK, add parameters mustCriteria to the query. time consuming = {}", Instant.now().toEpochMilli() - beginTime.toEpochMilli());
        }
    }

    private List<Long> getProjectIdsByActivity(String userName, Function<String, ActivityQuery> activityQuery) {
        if (StringUtils.isBlank(userName)) {
            return Collections.emptyList();
        }
        List<String> users = aiUserService.listUserIdByUsername(userName);
        if (users == null || users.isEmpty()) {
            return Collections.emptyList();
        }
        log.info("find user by {}, got ({})", userName, StringUtils.join(users, ","));
        List<Activity> activities = new ArrayList<>();
        users.forEach(user -> activities.addAll(activityManager.getActivities(activityQuery.apply(user))));

        if (CollectionUtils.isEmpty(activities)) {
            return Collections.emptyList();
        }
        List<Long> projectIds = activities.stream().map(Activity::getProjectId).distinct().collect(Collectors.toList());
        log.info("get projectIds {} from activity", projectIds);
        return projectIds;
    }


    private EsPage<ProjectEsModel> getProjectEsPageData(ProjectEsQueryParam param) {
        SearchRequest searchRequest = buildSearchRequest(param);
        return elasticSearchHelper.search(searchRequest, ProjectEsModel.class);
    }

    private long getProjectEsCount(ProjectEsQueryParam param) {
        CountRequest request = buildCountRequest(param);
        return elasticSearchHelper.count(request, ProjectEsModel.class);
    }

    /**
     * 构建排序。
     *
     * <ul>
     *     <li>needEsSort == false, 不需要排序，会直接返回false</li>
     *     <li>needEsSort == true, 且通过orderField和order可以构建排序条件，则按照该排序条件排序，否则返回默认排序条件。</li>
     * </ul>
     *
     * @param sortField  排序字段
     * @param sortOrder  顺序
     * @return
     */
    private SortBuilder buildSearchListOrderData(String sortField, String sortOrder, Boolean isClaimSearch) {

        Supplier<SortBuilder> defaultSortOrder = () ->
            ElasticSearchUtil.sortBuilder(DashboardSortTag.SYNC_DATA_TIME, Long.class, SortOrder.DESC.name(), () -> null);

        // days old排序需要使用script
        if(StringUtils.equals(sortField, DashboardSortTag.DAYS_OLD)){
            String scriptCode = getDaysOldSortScript();
            Map<String, Object> params = getDaysOldScriptParams(isClaimSearch != null && isClaimSearch);
            return ElasticSearchUtil.scriptSortBuilder(scriptCode, params, sortOrder, ScriptSortType.NUMBER);
        }

        // member role排序使用script
        if (!StringUtils.isEmpty(sortField) && DashboardSortTag.ROLE_TAGS.containsKey(sortField)) {
            String scriptCode = getMemberRoleSortScript();
            Map<String, Object> params = Map.of("auth", DashboardSortTag.ROLE_TAGS.get(sortField),
                                                "rule", sortOrder.toLowerCase());
            return ElasticSearchUtil.scriptSortBuilder(scriptCode, params, sortOrder, ScriptSortType.STRING);
        }

        // es text类型不支持直接用field排序，应该用field.keyword
        if (!StringUtils.isEmpty(sortField) && DashboardSortTag.CARRIER.containsKey(sortField)) {
            sortField = DashboardSortTag.CARRIER.get(sortField) + ".keyword";
        }

        return ElasticSearchUtil.sortBuilder(sortField, null, sortOrder, defaultSortOrder);
    }

    /** daysOld 排序script，排序规则见https://gitlab.bees360.com/engineers/solid/-/issues/543 */
    private static String getDaysOldSortScript() {
        return "long start = doc['createdTime'].value; long end = params['now'];"
            + "if(!params['claim'] && doc['policyEffectiveDate'].size() != 0) "
            + "{ start = (long) Math.max(doc['createdTime'].value, doc['policyEffectiveDate'].value.toInstant().toEpochMilli());}"
            + "if(doc['projectStatus'].size() != 0 && params['status'].contains( (int) doc['projectStatus'].value))"
            + "{ end = doc['statusUpdateTime'].value;}"
            + "return (end - start);";
    }

    private Map<String, Object> getDaysOldScriptParams(boolean isClaimSearch) {
        List<Integer> statusList = getProjectSpecificStatus();
        Map<String, Object> params = new HashMap<>();
        if (isClaimSearch) {
            params.put("claim", true);
        } else {
            params.put("claim", false);
        }
        Instant instant = Instant.now();
        long timeStamp = instant.toEpochMilli();
        params.put("status", statusList);
        params.put("now", timeStamp);
        return params;
    }

    private List<Integer> getProjectSpecificStatus() {
        return List.of(AiProjectStatusEnum.PROJECT_DELETED.getCode(),
            AiProjectStatusEnum.PROJECT_CANCELED.getCode(), AiProjectStatusEnum.CLIENT_RECEIVED.getCode());
    }

    /** 由于存在member name为空的情况，做了特殊处理*/
    private String getMemberRoleSortScript(){
        return "def x = 'zz';"
            + "if(params['rule'].equals('desc'))"
            + "{ x = 'aa';}"
            + "if(params._source.members == null)"
            + "{ return x; }"
            + "for(member in params._source.members)"
            + "{ if(member.auth != null && member.auth.equals(params['auth']))"
            + "{ return member.name == null ? x : member.name.toLowerCase(); } }"
            + "return x;";
    }

    /**
     * 设置字典展示跟前端必要展示值
     *
     * @param projectList
     */
    private void setNeedInfo(List<ProjectEsModel> projectList, User user) {
        List<Long> projectIds = projectList.stream().map(ProjectEsModel::getProjectId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(projectIds)) {
            return;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        Map<Long, List<String>> projectTagMap = projectTagService.getTagsByProjectIds(projectIds);

        List<Long> urgentList = projectFolderService.getByProjectIdsAndType(projectIds, ProjectUserFolderTypeEnum.URGENT.getType());
        projectList.forEach(pro -> {
            // daysOld
            long daysOld = getProjectDaysOld(df, pro);
            pro.setDaysOld(daysOld);

            // tagItem
            if (MapUtils.isNotEmpty(projectTagMap)) {
                pro.setTags(projectTagMap.get(pro.getProjectId()));
            }

            if (!CollectionUtils.isEmpty(urgentList) && urgentList.contains(pro.getProjectId())) {
                pro.setFolderType(ProjectUserFolderTypeEnum.URGENT.getType());
            }
        });
        setAirspaceIntoProjectEsModelList(projectList);
    }

    /**
     * 为List<ProjectEsModel>补全Airspace信息
     */
    private void setAirspaceIntoProjectEsModelList(List<ProjectEsModel> projectList) {
        var projectIds = Iterables.transform(projectList, p -> String.valueOf(p.getProjectId()));

        // 通过 GrpcProjectAirspaceManager 获取 Airspace
        var projectId2AirspaceMap = projectAirspaceManager.getLatestAirspace(projectIds);

        projectList.forEach(p -> {
            var projectAirspace = projectId2AirspaceMap.get(String.valueOf(p.getProjectId()));
            if (Objects.nonNull(projectAirspace)) {
                Airspace airspace = new Airspace();
                airspace.setStatus(projectAirspace.getStatus());
                airspace.setHeightCeiling(projectAirspace.getHeightCeiling());

                p.setAirspace(airspace);
            }
        });
    }

    private long getProjectDaysOld(DateTimeFormatter df, ProjectEsModel pro) {
        long daysOld = 0L;
        if (pro.getCreatedTime() != 0L) {
            ZoneId USCentral = ZoneId.of("US/Central");
            LocalDateTime centerCreatedTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(pro.getCreatedTime()),
                USCentral);
            // policyEffectiveDate string -> LocalDateTime
            LocalDateTime policyEffectiveDate = Optional.ofNullable(pro.getPolicyEffectiveDate())
                .map(o -> LocalDate.parse(o, df).atStartOfDay()).orElse(null);
            // 创建时间(美国中部) 和policyEffectiveDate 取较大值
            LocalDateTime maxTime = policyEffectiveDate != null && policyEffectiveDate.isAfter(centerCreatedTime) ?
                policyEffectiveDate : centerCreatedTime;

            LocalDateTime centerNow = ZonedDateTime.now(USCentral).toLocalDateTime();
            List<Integer> statusList = getProjectSpecificStatus();

            // 若project满足特殊的status，则daysOld的结束时间为该状态更新的时间
            if (statusList.contains(pro.getProjectStatus())) {
                centerNow = LocalDateTime.ofInstant(Instant.ofEpochMilli(pro.getStatusUpdateTime()),
                    USCentral);
            }

            daysOld = Math.max(Duration.between(maxTime, centerNow).toDays(), 0L);
        }
        return daysOld;
    }

    @Override
    public List<ProjectCalQueryVo> queryCalExportResult(ProjectEsQueryParam param) {
        log.info("Do query the project export data. The parameters are {}", param);
        if (checkAndSetChangeReasonCondition(param)) {
            return Collections.emptyList();
        }
        List<ProjectEsModel> projectList = findProjectListByQueryBuild(param);
        if (CollectionUtils.isEmpty(projectList)) {
            return Collections.emptyList();
        }
        List<ProjectLog> logList = projectLogService.getLogList(projectList.stream().map(ProjectEsModel::getProjectId).collect(Collectors.toList()));
        Map<Long, List<LogEntry>> logEntryMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(logList)) {
            logEntryMap = logList.stream().collect(Collectors.toMap(ProjectLog::getProjectId, ProjectLog::getLogEntry, (v1, v2) -> v1));
        }
        Map<String, ClaimCatastrophe> projectIdToCat = getProjectIdToCat(
            Iterables.transform(projectList, p -> Long.toString(p.getProjectId())));
        Map<Long, List<LogEntry>> finalLogEntryMap = logEntryMap;

        setProjectStateChangeReason(projectList);
        setChangeReasonGroup(projectList);

        List<ProjectCalQueryVo> calQueryVos = projectList.stream().map(p -> {
            Long imageUploadTime = null;
            if (!CollectionUtils.isEmpty(p.getTimeLines())) {
                // 统计 Image uploaded 的时间改为最后一次触发该状态的时间
                // @see https://gitlab.bees360.com/engineers/bumble-bee/issues/-/issues/182
                imageUploadTime = p.getTimeLines().stream()
                    .filter(o -> Objects.equals(o.getStatus().getCode(), AiProjectStatusEnum.IMAGE_UPLOADED.getCode()))
                    .min(Comparator.comparing(ProjectStatusVo::getCreatedTime)).map(ProjectStatusVo::getCreatedTime).orElse(null);
            }
            Float hiveDistance = null;
            if (StringUtils.isNotBlank(p.getAddressId())) {
               var distance = addressHiveLocationProvider.getHiveLocationDistance(p.getAddressId());
               hiveDistance = Null.getIfNPEThenDefault(() -> distance.getDistanceMiles(), null);
            }
            var estimateTotalPay = p.getEstimateTotalPay() == null? null: new BigDecimal(p.getEstimateTotalPay() + "");
            var siteInspected = Optional.ofNullable(p.getSiteInspectedTime()).orElse(imageUploadTime);
            imageUploadTime = Optional.ofNullable(imageUploadTime).orElse(siteInspected);
            var catLevel = Optional.ofNullable(projectIdToCat.get(Long.toString(p.getProjectId())))
                .map(cat -> cat.getLevel()).orElse(null);

            var projectCalQueryVoBuilder = ProjectCalQueryVo.builder();
            projectCalQueryVoBuilder.projectId(p.getProjectId())
                .projectState(p.getProjectState())
                .projectStateChangeReason(p.getProjectStateChangeReason())
                .changeReasonGroup(p.getChangeReasonGroup())
                .adjusterName(getMemberAuthNameByProject(p, UserAuthEnum.ROLE_ADJUSTER))
                .pilotName(p.getPilotName())
                .darApproved(getReportActionCreatedTime(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, LogReportActionEnum.APPROVED))
                .darGenerated(getReportActionCreatedTime(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, LogReportActionEnum.GENERATED))
                .darSubmitted(getReportActionCreatedTime(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, LogReportActionEnum.SUBMITTED))
                .fsrApproved(getReportActionCreatedTime(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.FULL_SCOPE_UNDERWRITING_REPORT, LogReportActionEnum.APPROVED))
                .fsrSubmitted(getReportActionCreatedTime(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.FULL_SCOPE_UNDERWRITING_REPORT, LogReportActionEnum.SUBMITTED))
                .rorApproved(getReportActionCreatedTime(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.ROOF_ONLY_UNDERWRITING_REPORT, LogReportActionEnum.APPROVED))
                .rorSubmitted(getReportActionCreatedTime(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.ROOF_ONLY_UNDERWRITING_REPORT, LogReportActionEnum.SUBMITTED))
                .pirApproved(getReportActionCreatedTime(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.PROPERTY_IMAGE_REPORT, LogReportActionEnum.APPROVED))
                .pirGenerated(getReportActionCreatedTime(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.PROPERTY_IMAGE_REPORT, LogReportActionEnum.GENERATED))
                .pirSubmitted(getReportActionCreatedTime(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.PROPERTY_IMAGE_REPORT, LogReportActionEnum.SUBMITTED))
                .imageUploaded(imageUploadTime)
                .siteInspected(siteInspected)
                .processName(getMemberAuthNameByProject(p, UserAuthEnum.ROLE_PROCESSOR))
                .producerName(getMemberAuthNameByProject(p, UserAuthEnum.ROLE_PRODUCER))
                .reviewerName(getMemberAuthNameByProject(p, UserAuthEnum.ROLE_REVIEWER))
                .thirdStarted(getAiFlowCreateTime(finalLogEntryMap, p.getProjectId(),
                    LogAiFlowDetailEnum.THREE_D_GENERATION, LogAiFlowActionEnum.STARTED))
                .thirdProcessed(getAiFlowCreateTime(finalLogEntryMap, p.getProjectId(),
                    LogAiFlowDetailEnum.POST_BOUNDARY, LogAiFlowActionEnum.FINISHED))
                .thirdFinished(getAiFlowCreateTime(finalLogEntryMap, p.getProjectId(),
                    LogAiFlowDetailEnum.THREE_D_GENERATION, LogAiFlowActionEnum.FINISHED))
                .planeFinished(getAiFlowCreateTime(finalLogEntryMap, p.getProjectId(),
                    LogAiFlowDetailEnum.PLANE, LogAiFlowActionEnum.FINISHED))
                .hoverStatus(p.getHoverMarkStatus())
                .policyNumber(p.getPolicyNumber())
                .projectType(p.getProjectType())
                .claimNumber(p.getClaimNumber())
                .insuredPhone(p.getAssetOwnerPhone())
                .insuredName(p.getAssetOwnerName())
                .insuredEmail(p.getAssetOwnerEmail())
                .streetAddress(p.getAddress())
                .city(p.getCity())
                .state(p.getState())
                .zipCode(p.getZipCode())
                .catNumber(p.getCatNumber())
                .catLevel(catLevel)
                .createTime(p.getCreatedTime())
                .completionTime(getProjectStatusCreatedTime(p, AiProjectStatusEnum.RETURNED_TO_CLIENT))
                .batchNumber(p.getBatchNo())
                .serviceType(p.getServiceType())
                .latitude(p.getGpsLocationLatitude())
                .longitude(p.getGpsLocationLongitude())
                .flyZoneType(p.getFlyZoneType())
                .daysOld(getProjectDaysOld(DateTimeFormatter.ofPattern("yyyy-MM-dd"), p))
                .customerContacted(p.getCustomerContactedTime())
                .assignedToPilot(getProjectStatusCreatedTime(p, AiProjectStatusEnum.ASSIGNED_TO_PILOT))
                .inspectionDeadLine(p.getInspectionTime())
                .externalAdjusterName(getExternalAdjuster(p.getProjectId()))
                .creator(p.getCreatorName())
                .subscribePlnar(ProjectPlnarStatusEnum.orderedPlnar(p.getPlnarMarkStatus()))
                .canceled(AiProjectStatusEnum.isCanceledStatus(p.getProjectStatus()))
                .paid(PayStatusEnum.isPaid(p.getPayStatus()))
                .paymentDate(p.getPaymentDate())
                .totalPay(p.getTotalPay())
                .clientReceived(getProjectStatusCreatedTime(p, AiProjectStatusEnum.CLIENT_RECEIVED))
                .policyEffectiveDate(p.getPolicyEffectiveDate())
                .projectTags(p.getProjectTags())
                .pilotFeedback(p.getPilotFeedbacks())
                .darApprovedName(Optional.ofNullable(getFirstLogEntryByType(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, LogReportActionEnum.APPROVED))
                    .map(LogEntry::getUsername).orElse(""))
                .pirSubmittedBy(Optional.ofNullable(getFirstLogEntryByType(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.PROPERTY_IMAGE_REPORT, LogReportActionEnum.SUBMITTED))
                    .map(LogEntry::getUsername).orElse(""))
                .clientReceivedName(Optional.ofNullable(getProjectStatus(p, AiProjectStatusEnum.CLIENT_RECEIVED))
                .map(ProjectStatusVo::getUserName).orElse(""))
                .estimateCompleteTime(getProjectStatusCreatedTime(p, AiProjectStatusEnum.ESTIMATE_COMPLETED))
                .estimateCompleteName(Optional.ofNullable(getProjectStatus(p, AiProjectStatusEnum.ESTIMATE_COMPLETED))
                    .map(ProjectStatusVo::getUserName).orElse(""))
                .insuredBy(p.getInsuranceCompanyName())
                .hiveDistance(hiveDistance)
                .estimateTotalMoney(estimateTotalPay)
                .operatingCompany(p.getOperatingCompany())
                .policyType(p.getPolicyType())
                .airspace(p.getAirspace())
                .droneImageCount(p.getDroneImageCount())
                .mobileImageCount(p.getMobileImageCount())
                .darLastApproved(getReportActionLastTime(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, LogReportActionEnum.APPROVED))
                .darApprovedTimes(getReportActionTimes(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, LogReportActionEnum.APPROVED))
                .darLastSubmitted(getReportActionLastTime(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, LogReportActionEnum.SUBMITTED))
                .darSubmittedTimes(getReportActionTimes(finalLogEntryMap, p.getProjectId(),
                    LogReportDetailEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, LogReportActionEnum.SUBMITTED))
                .timeLines(Optional.ofNullable(p.getTimeLines()).orElse(List.of()));
            return projectCalQueryVoBuilder.build();
        }).collect(Collectors.toList());
        return calQueryVos;
    }

    public Map<String, ClaimCatastrophe> getProjectIdToCat(Iterable<String> projectIds) {
        if (Objects.isNull(projectIds)) {
            return Collections.EMPTY_MAP;
        }
        return projectCatastropheManager.findByProjectIds(projectIds);
    }

    @Override
    public List<RoleQueryResult> queryRoleCountProject(Long statusStartTime, Long statusEndTime, String role, Boolean isClaim) {
        String format = "yyyy-MM-dd HH:mm:ss";

        ProjectEsQueryParam param = ProjectEsQueryParam.builder()
            .roleTag("ADMIN")
            .isSearchAll(true)
            .memberRole(role)
            .pageSize(30000)
            .isClaimSearch(isClaim)
            .build();
        List<ProjectEsModel> projectList = findProjectListByQueryBuild(param);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(projectList)) {
            projectList = projectList.stream()
                .filter(o -> AiProjectStatusEnum.getStatusCodeList(
                    AiProjectStatusEnum.RETURNED_TO_CLIENT, AiProjectStatusEnum.CLIENT_RECEIVED,
                    AiProjectStatusEnum.PROJECT_ARCHIVED).contains(o.getProjectStatus()))
                .filter(o -> o.getTimeLines().stream().anyMatch(time ->
                    time.getStatus().getCode() == AiProjectStatusEnum.RETURNED_TO_CLIENT.getCode() &&
                        time.getCreatedTime() >= statusStartTime &&
                        time.getCreatedTime() <= statusEndTime))
                .collect(Collectors.toList());
        }
        List<ItemResult> results = projectList.stream().map(o -> {
            ItemResult result = new ItemResult();
            o.getMembers().stream().filter(t -> t.getAuth().equals(role))
                .findFirst().ifPresent(member -> {
                result.setName(member.getName());
                result.setUserId(member.getId());
            });
            result.setCurrentStatus(Optional.ofNullable(AiProjectStatusEnum.getEnum(o.getProjectStatus()))
                .map(AiProjectStatusEnum::getDisplay).orElse(""));
            result.setProjectId(o.getProjectId());
            Long closedTime = o.getTimeLines().stream().filter(t -> t.getStatus().getCode() == AiProjectStatusEnum.RETURNED_TO_CLIENT.getCode())
                .findFirst().map(ProjectStatusVo::getCreatedTime).orElse(null);
            result.setStatusTime(Optional.ofNullable(closedTime).map(time -> DateUtil.convertDate(time, format, ZoneId.of("US/Central"))).orElse(""));
            result.setServiceType(Optional.ofNullable(o.getServiceType()).map(ProjectServiceTypeEnum::getEnum).map(ProjectServiceTypeEnum::getDisplay).orElse(null));
            if (result.getName() == null) {
                return null;
            }
            return result;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, List<ItemResult>> userMap = results.stream().collect(Collectors.groupingBy(ItemResult::getName));
        List<RoleQueryResult> queryResultList = new ArrayList<>();
        if (userMap != null) {
            userMap.forEach((key, value) -> {
                RoleQueryResult queryResult = new RoleQueryResult();
                queryResult.setName(key);
                queryResult.setCount(value.size());
                queryResult.setItemList(value);
                queryResultList.add(queryResult);
            });
        }
        return queryResultList;
    }

    @Override
    public void updateInspectionTime(long projectId, Long inspectionTime) {
        ProjectEsModel projectEsModel = findProjectByProjectIdCheckExisted(projectId);
        if (inspectionTime == null || inspectionTime == 0) {
            inspectionTime = null;
        }
        projectEsModel.setInspectionTime(inspectionTime);
        if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
            var projectEsModelUpdated =
                    ProjectEsModelUpdater.toBuilder()
                            .setProjectId(projectId)
                            .setInspectionTime(inspectionTime)
                            .build();
            updatePartial(projectEsModelUpdated);
        } else {
            syncToEsFromProjectEsModel(projectEsModel, true);
        }
    }

    private Long getProjectStatusCreatedTime(ProjectEsModel project, AiProjectStatusEnum projectStatusEnum) {
        Long createdTime = null;
        if (!CollectionUtils.isEmpty(project.getTimeLines())) {
            createdTime = project.getTimeLines().stream()
                .filter(o -> Objects.equals(o.getStatus().getCode(), projectStatusEnum.getCode()))
                .min(Comparator.comparing(ProjectStatusVo::getCreatedTime)).map(ProjectStatusVo::getCreatedTime)
                .orElse(null);
        }
        return createdTime;
    }

    private ProjectStatusVo getProjectStatus(ProjectEsModel project, AiProjectStatusEnum projectStatusEnum) {
        if (!CollectionUtils.isEmpty(project.getTimeLines())) {
            return project.getTimeLines().stream()
                .filter(o -> Objects.equals(o.getStatus().getCode(), projectStatusEnum.getCode()))
                .min(Comparator.comparing(ProjectStatusVo::getCreatedTime))
                .orElse(null);
        }
        return null;
    }

    private int getReportActionTimes(Map<Long, List<LogEntry>> logEntryMap, long projectId,
                                         LogReportDetailEnum reportDetailEnum, LogReportActionEnum actionEnum) {
        var reportLogs = getReportLogs(logEntryMap, projectId, reportDetailEnum, actionEnum);
        return reportLogs == null ? 0 : (int) reportLogs.count();
    }

    private Long getReportActionLastTime(Map<Long, List<LogEntry>> logEntryMap, long projectId,
                                            LogReportDetailEnum reportDetailEnum, LogReportActionEnum actionEnum) {
        String createdTime =
            Optional.ofNullable(
                    getLastLogEntryByType(
                        logEntryMap, projectId, reportDetailEnum, actionEnum))
                .map(LogEntry::getCreatedTime)
                .orElse("");
        return convertTime(createdTime);
    }

    private Long getReportActionCreatedTime(Map<Long, List<LogEntry>> logEntryMap, long projectId,
                                            LogReportDetailEnum reportDetailEnum, LogReportActionEnum actionEnum) {
        String createdTime =
                Optional.ofNullable(
                                getFirstLogEntryByType(
                                        logEntryMap, projectId, reportDetailEnum, actionEnum))
                        .map(LogEntry::getCreatedTime)
                        .orElse("");
        return convertTime(createdTime);
    }

    private Long convertTime(String createdTime) {
        if (StringUtils.isNotBlank(createdTime)) {
            try {
                return DateUtil.convertDate(createdTime, DateUtil.UTC_FORMAT, TimeZone.getTimeZone("UTC")).getTime();
            } catch (ParseException e) {
                return null;
            }
        }
        return null;
    }

    private LogEntry getFirstLogEntryByType(Map<Long, List<LogEntry>> logEntryMap, long projectId,
                                            LogReportDetailEnum reportDetailEnum, LogReportActionEnum actionEnum) {
        var reportLogs = getReportLogs(logEntryMap, projectId, reportDetailEnum, actionEnum);
        return reportLogs == null ? null : reportLogs.findFirst().orElse(null);
    }

    private LogEntry getLastLogEntryByType(Map<Long, List<LogEntry>> logEntryMap, long projectId,
                                            LogReportDetailEnum reportDetailEnum, LogReportActionEnum actionEnum) {
        var reportLogs = getReportLogs(logEntryMap, projectId, reportDetailEnum, actionEnum);
        return reportLogs == null ? null : reportLogs.reduce((first, second) -> second).orElse(null);
    }

    private Stream<LogEntry> getReportLogs(
            Map<Long, List<LogEntry>> logEntryMap,
            long projectId,
            LogReportDetailEnum reportDetailEnum,
            LogReportActionEnum actionEnum) {
        if (MapUtils.isEmpty(logEntryMap) || CollectionUtils.isEmpty(logEntryMap.get(projectId))) {
            return null;
        }
        return logEntryMap.get(projectId).stream().filter(o -> StringUtils.equals(o.getType(), LogEntryTypeEnum.REPORT.getType())
            && Objects.equals(o.getLogDetail().getDetailCode(), reportDetailEnum.getCode())
            && Objects.equals(o.getLogDetail().getActionCode(), actionEnum.getCode())
        );
    }

    private String getMemberAuthNameByProject(ProjectEsModel projectEsModel, UserAuthEnum userAuth) {
        if (CollectionUtils.isEmpty(projectEsModel.getMembers())) {
            return "";
        }
        return projectEsModel.getMembers().stream()
            .filter(m -> StringUtils.equals(m.getAuth().trim(), userAuth.getAuth()))
            .findFirst().map(MemberInfo::getName).orElse("");
    }

    private String getExternalAdjuster(long projectId) {
        return Optional.ofNullable(projectContactService.findExternalAdjuster(projectId))
            .map(ExternalMember::getName)
            .orElse("");
    }

    private Long getAiFlowCreateTime(Map<Long, List<LogEntry>> logEntryMap, long projectId,
                                     LogAiFlowDetailEnum aiFlowDetail, LogAiFlowActionEnum aiFlowAction) {
        if (MapUtils.isEmpty(logEntryMap) || CollectionUtils.isEmpty(logEntryMap.get(projectId))) {
            return null;
        }
        String createTime = logEntryMap.get(projectId).stream().filter(o -> StringUtils.equals(o.getType(), LogEntryTypeEnum.PROJECT_AI_FLOW.getType())
            && Objects.equals(o.getLogDetail().getDetailCode(), aiFlowDetail.getCode())
            && Objects.equals(o.getLogDetail().getActionCode(), aiFlowAction.getCode())
        ).findFirst().map(LogEntry::getCreatedTime).orElse("");
        if (StringUtils.isNotBlank(createTime)) {
            try {
                return DateUtil.convertDate(createTime, DateUtil.UTC_FORMAT, TimeZone.getTimeZone("UTC")).getTime();
            } catch (ParseException e) {
                log.error("date format incorrect", e);
                return null;
            }
        }
        return null;
    }

    /**
     * project是否订阅了hover
     * @param projectId
     * @return
     */
    @Override
    public boolean isSubscribeToHover(long projectId) {
        ProjectEsModel esModel = findProjectByProjectId(projectId);
        if (null != esModel.getHoverJobId()) {
            return true;
        }
        SerializableFirebaseProject project = firebaseProjectService.getByProjectId(projectId);
        return Objects.nonNull(project.getHover_job_id());
    }

    @Override
    public void updateReportStatus(User user, long projectId, Integer statusCode, String type) {
        ProjectEsModel esModel = findProjectByProjectId(projectId);
        var projectEsModelUpdater = ProjectEsModelUpdater.toBuilder().setProjectId(projectId);
        int tempHoverStatus = ProjectHoverStatusEnum.NONE.getCode();
        String logEntryType = null;
        Integer logDetailCode = null;
        if (Objects.nonNull(esModel)) {
            if (StringUtils.equals(type, ProjectReportRecordEnum.HOVER.getType())) {
                tempHoverStatus = esModel.getHoverMarkStatus();
                esModel.setHoverMarkStatus(statusCode);
                projectEsModelUpdater.setHoverMarkStatus(statusCode);
                logEntryType = LogEntryTypeEnum.HOVER_PLNAR_STATUS.getType();
                logDetailCode = LogHoverPlnarStatusDetailEnum.HOVER_STATUS.getCode();
            }
            if (StringUtils.equals(type, ProjectReportRecordEnum.PLNAR.getType())) {
                esModel.setPlnarMarkStatus(statusCode);
                projectEsModelUpdater.setPlnarMarkStatus(statusCode);
                logEntryType = LogEntryTypeEnum.HOVER_PLNAR_STATUS.getType();
                logDetailCode = LogHoverPlnarStatusDetailEnum.PLNAR_STATUS.getCode();

            }
        }
        if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
            updatePartial(projectEsModelUpdater.build());
        } else {
            syncToEsFromProjectEsModel(esModel, true);
        }
        if (StringUtils.isBlank(logEntryType)) {
            return;
        }
        LogEntryDetail logEntry = new LogEntryDetail(logDetailCode, statusCode);
        projectLogService.addLogEntry(projectId, user.getId(), logEntryType, logEntry);

        // hover状态修改发布事件处理
        if (StringUtils.equals(type, ProjectReportRecordEnum.HOVER.getType())) {
            applicationEventPublisher.publishEvent(
                new ProjectHoverStatusChangedEvent(this, projectId, tempHoverStatus, statusCode));
        }
    }

    @Override
    public void updateProjectTags(User user, long projectId, List<Long> tagIds) {
        ProjectEsModel esModel = findProjectByProjectId(projectId);
        if (esModel == null) {
            return;
        }
        List<Long> oldTags = esModel.getProjectTags();
        if (Objects.equals(oldTags, tagIds)) {
            return;
        }
        boolean shouldBeUrgent = true;
        if (CollectionUtils.isEmpty(tagIds)) {
            tagIds = null;
            shouldBeUrgent = false;
        }
        if (tagIds != null && bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
            var projectEsModelUpdater =
                ProjectEsModelUpdater.toBuilder()
                    .setProjectId(projectId)
                    .setProjectTags(tagIds)
                    .build();
            updatePartial(projectEsModelUpdater);
        } else {
            esModel.setProjectTags(tagIds);
            syncToEsFromProjectEsModel(esModel, true);
        }
        applicationEventPublisher.publishEvent(
                new ProjectTagChangedEvent(this, projectId, tagIds, user.getId()));
        //if the project is underwriting then skips
        if (UNDERWRITING_FIRST_INSPECTION.getCode() == esModel.getClaimType()){
            return;
        }
        //If a project is tagged, then it will be transferred to URGENT folder, and if the tag is cleared the project
        //will be removed from URGENT folder.
        if (shouldBeUrgent) {
            projectFolderService.addProjectFolder(user, projectId, ProjectUserFolderTypeEnum.URGENT.getType());
        } else {
            projectFolderService.deleteById(user, projectId, ProjectUserFolderTypeEnum.URGENT.getType());
        }
    }

    @Override
    public ProjectEsModel findProjectByProjectId(long projectId) {
        if (!elasticSearchHelper.indexExist(ProjectEsModel.class)) {
            return null;
        }
        return replaceExternalAdjuster(elasticSearchHelper.getById(projectId + "", ProjectEsModel.class).orElse(null));
    }

    @Override
    public ProjectEsModel getProjectInfo(long projectId, User user) {
        if (!elasticSearchHelper.indexExist(ProjectEsModel.class)) {
            return null;
        }
        ProjectEsModel esModel = elasticSearchHelper.getById(projectId + "", ProjectEsModel.class).orElse(null);
        if (Objects.nonNull(esModel)) {
            if (StringUtils.isNotBlank(esModel.getAddressId())
                    && StringUtils.isBlank(esModel.getTimeZone())
                    && bees360FeatureSwitch.isEnableFillTimeZoneWhenGetProject()) {
                var timeZone = addressProvider.findById(esModel.getAddressId()).getTimeZone();
                if (timeZone != null) {
                    esModel.setTimeZone(timeZone.getID());
                }
            }
            setNeedInfo(Collections.singletonList(esModel), user);
        }
        return replaceExternalAdjuster(esModel);
    }

    @Override
    public ProjectEsModel findProjectByProjectIdCheckExisted(long projectId) {
        if (!elasticSearchHelper.indexExist(ProjectEsModel.class)) {
            throw new ServiceException(MsgCodeManager.PROJECT.PROJECT_IS_NULL);
        }
        return replaceExternalAdjuster(elasticSearchHelper
            .getById(projectId + "", ProjectEsModel.class)
            .orElseThrow(
                () ->
                    new ServiceException(
                        MsgCodeManager.PROJECT.PROJECT_IS_NULL)));
    }

    private List<ProjectEsModel> replaceExternalAdjuster(List<ProjectEsModel> esModels) {
        if (esModels != null) {
            esModels.forEach(this::replaceExternalAdjuster);
        }
        return esModels;
    }

    private ProjectEsModel replaceExternalAdjuster(ProjectEsModel esModel) {
        if (esModel == null) {
            return null;
        }
        var externalAdjuster = projectContactService.findExternalAdjuster(esModel.getProjectId());
        var members = Optional.ofNullable(esModel.getExternalMember()).orElse(new ArrayList<>());
        members = members.stream()
            .filter(Objects::nonNull)
            .filter(member -> !StringUtils.equals(member.getAuth(), UserAuthEnum.EXTERNAL_ADJUSTER.getAuth()))
            .collect(Collectors.toList());
        if (externalAdjuster != null) {
            members.add(externalAdjuster);
        }
        esModel.setExternalMember(members);
        return esModel;
    }

    /**
     * 更新Project info/timeline 到es
     *
     * @param projectEsModel
     */
    @Override
    public synchronized Boolean syncToEsFromProjectEsModel(ProjectEsModel projectEsModel, boolean sync) {
        int effected = elasticSearchHelper.update(projectEsModel);
        log.info("syncToEsFromProjectEsModel sync success projectId:{}", projectEsModel.getProjectId());
        return effected > 0;
    }

    // 这个方法不会更新空值，如果局部更新的值为空不该使用这个方法
    @Override
    public void updatePartial(ProjectEsModelUpdater projectEsModel) {
        elasticSearchHelper.updatePartial(projectEsModel);
    }

    // 这个方法不会更新空值，如果局部更新的值为空不该使用这个方法
    @Override
    public Boolean updatePartialReturningIfChanged(ProjectEsModelUpdater projectEsModel) {
        int effected = elasticSearchHelper.updatePartial(projectEsModel);
        log.info("syncToEsFromProjectEsModelPartially sync success projectId:{}", projectEsModel.getProjectId());
        return effected > 0;
    }

    @Override
    public void softDeleteProjectDataByProjectId(long projectId) {
        projectStatusService.updateStatusByAiUser(projectId, AiProjectStatusEnum.PROJECT_ARCHIVED.getCode(),
            AiBotUserEnum.reloadUserByBot(AiBotUserEnum.AI_NEW_USER_ID));
    }

    /**
     * 更新member 信息
     *
     * @param user
     * @param projectId
     * @param memberInfo
     * @param isDeleted
     * @throws IOException
     */
    @Override
    public Boolean changeDashBoardProjectMembers(
            User user, long projectId, MemberInfo memberInfo, boolean isDeleted) {
        log.info(
                "changeDashBoardProjectMembers userId:{}, userName:{}, projectId:{}, assignedUser:{}, assignedAuth:{}",
                user.getId(),
                user.getName(),
                projectId,
                memberInfo.getName(),
                memberInfo.getAuth());
        var member = userProvider.getUser(memberInfo.getId());
        var memberAuthSet =
                member.getAllAuthority().stream()
                        .map(UserAuthEnum::roleToAuth)
                        .collect(Collectors.toSet());
        if (!memberAuthSet.contains(memberInfo.getAuth())) {
            log.warn(
                    "Cannot set user :{} to member :{} due to lack of authority.",
                    member.getId(),
                    memberInfo.getAuth());
            return false;
        }
        // 要添加的projectMembers
        ProjectEsModel projectEsModel = findProjectByProjectIdCheckExisted(projectId);
        // 已存在的projectMembers
        Set<MemberInfo> memberInfoSet =
                new HashSet<>(
                        Optional.ofNullable(projectEsModel.getMembers()).orElse(new ArrayList<>()));
        MemberInfo memberChangeFrom = null;
        MemberInfo memberChangeTo = null;

        Optional<MemberInfo> existedMemberRole =
            memberInfoSet.stream()
                .filter(o -> StringUtils.equals(o.getAuth(), memberInfo.getAuth()))
                .findAny();
        memberChangeFrom = existedMemberRole.orElse(null);
        if (isDeleted) {
            memberChangeTo = null;
            memberInfoSet.remove(memberChangeFrom);
        } else {
            memberChangeTo = memberInfo;
            // 已存在相同角色
            existedMemberRole.ifPresent(memberInfoSet::remove);
            memberInfoSet.add(memberInfo);
            submitActivityOnMemberAssigned(projectId, user, memberInfo.getAuth(), memberInfo.getId());
        }

        // 将AI特有的member(processor, adjuster, reviewer, producer)更新到PG
        var roleEnum = RoleEnum.valueOf(memberInfo.getAuth().toUpperCase());
        if (bees360FeatureSwitch.isEnableSyncAiEsDataToSolid() && SYNC_AI_MEMBER_AUTHS.contains(roleEnum)) {
            log.info("Synchronize AI-specific member changes to PG. projectId:{}, userId:{}, roleEnum:{}, opUserId:{}, isDeleted:{}",
                    projectId, memberInfo.getId(), roleEnum, user.getId(), isDeleted);
            if (isDeleted) {
                memberManager.removeMember(String.valueOf(projectId), roleEnum, user.getId());
            } else {
                memberManager.setMember(String.valueOf(projectId), memberInfo.getId(), roleEnum, user.getId());
            }
        }

        projectEsModel.setMembers(new ArrayList<>(memberInfoSet));
        Boolean result;
        if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
            var projectEsModelUpdater =
                    ProjectEsModelUpdater.toBuilder()
                            .setProjectId(projectId)
                            .setMembers(new ArrayList<>(memberInfoSet))
                            .build();
            result = updatePartialReturningIfChanged(projectEsModelUpdater);
        } else {
            result = syncToEsFromProjectEsModel(projectEsModel, true);
        }
        if (!isDeleted) {
            addProjectMemberDict(memberInfo);
        }
        publishMemberChange(projectId, user, memberInfo.getAuth(), memberChangeFrom, memberChangeTo);
        return result;
    }

    private void publishMemberChange(
            long projectId,
            User operator,
            String auth,
            MemberInfo memberChangeFrom,
            MemberInfo memberChangeTo) {
        User from =
                Optional.ofNullable(memberChangeFrom)
                        .map(m -> userProvider.findUserById(m.getId()))
                        .orElse(null);
        User to =
                Optional.ofNullable(memberChangeTo)
                        .map(m -> userProvider.findUserById(m.getId()))
                        .orElse(null);
        ProjectMemberChangedEvent event = new ProjectMemberChangedEvent();
        event.setProjectId(projectId + "");
        event.setOperator(operator.getId());
        event.setAuth(auth);
        event.setUserFrom(Optional.ofNullable(from).map(User::getId).orElse(null));
        event.setUserTo(Optional.ofNullable(to).map(User::getId).orElse(null));
        eventPublisher.publish(event);
    }

    private void submitActivityOnMemberAssigned(
            long projectId, User assigner, String role, String newUserId) {
        Message.ActivityMessage.Builder activityMessage = Message.ActivityMessage.newBuilder();
        activityMessage
                .setAction(Message.ActivityMessage.ActionType.CHANGE.name())
                .setCreatedBy(assigner.toMessage())
                .setProjectId(projectId)
                .setSource(ActivitySourceEnum.AI.getValue())
                .setEntity(
                        Message.ActivityMessage.Entity.newBuilder()
                                .setType(EntityType.MEMBER.name())
                                .setId(newUserId)
                                .build());
        Message.ActivityMessage.Field.Builder field =
                Message.ActivityMessage.Field.newBuilder()
                        .setType(Message.ActivityMessage.FieldType.STRING.name())
                        .setValue(role);
        activityMessage.setField(field);
        activityManager.submitActivity(Activity.of(activityMessage.build()));
    }

    private void addProjectMemberDict(MemberInfo memberInfo) {
        if (Objects.isNull(memberInfo)) {
            return;
        }
        String type = null;
        if (StringUtils.equalsIgnoreCase(memberInfo.getAuth(), UserAuthEnum.ROLE_ADJUSTER.getAuth())) {
            type = ProjectOptionDictTypeEnum.ADJUSTER.getType();
        }
        if (StringUtils.equalsIgnoreCase(memberInfo.getAuth(), UserAuthEnum.ROLE_PROCESSOR.getAuth())) {
            type = ProjectOptionDictTypeEnum.PROCESSOR.getType();
        }
        if (StringUtils.equalsIgnoreCase(memberInfo.getAuth(), UserAuthEnum.ROLE_REVIEWER.getAuth())) {
            type = ProjectOptionDictTypeEnum.REVIEWER.getType();
        }
        if (StringUtils.equalsIgnoreCase(memberInfo.getAuth(), UserAuthEnum.ROLE_PRODUCER.getAuth())) {
            type = ProjectOptionDictTypeEnum.PRODUCER.getType();
        }
        if (StringUtils.isNotBlank(type)) {
            projectOptionDictService.addProjectDict(type, Collections.singleton(memberInfo.getName()));
        }
    }

    @Override
    public List<MemberInfo> getDashBoardProjectMembers(long projectId) throws IOException {
        ProjectEsModel esModel = findProjectByProjectId(projectId);
        if (Objects.isNull(esModel)) {
            // 还未分配成员
            return Collections.emptyList();
        }
        return esModel.getMembers() != null ? esModel.getMembers() : new ArrayList<>();
    }

    @Override
    public void updateProjectStatusOnReportChange(long projectId,
                                                  String userId,
                                                  int newStatusCode,
                                                  int reportTypeCode,
                                                  Long updatedAt) {
        log.info(
                "Update project status. projectId:{}, userId:{}, newStatusCode:{}, reportTypeCode:{}, updatedAt:{}",
                projectId,
                userId,
                newStatusCode,
                reportTypeCode,
                updatedAt);
        User user = aiUserService.getUserById(userId);

        // reviewer approved, 如果是claim的case, 只有DAR报告approve时才将项目状态变成Reviewer Approved
        if (Objects.equals(newStatusCode, AiProjectStatusEnum.REVIEWER_APPROVED.getCode())) {
            projectStatusService.updateStatus(
                projectId, user, AiProjectStatusEnum.REVIEWER_APPROVED.getCode(), updatedAt);
            return;
        }

        ProjectEsModel projectEsModel = findProjectByProjectIdCheckExisted(projectId);
        // report assembled
        ProjectServiceTypeEnum serviceTypeEnum = ProjectServiceTypeEnum.getEnum(projectEsModel.getServiceType());
        if (serviceTypeEnum.containsReport(reportTypeCode)) {
            projectStatusService.updateStatusByAiUser(projectId, newStatusCode, userId);
        }
    }

    @Override
    public void updateProjectCountOnImageChange(long projectId, LogProjectDataActionEnum actionEnum, User user) {
        ProjectEsModel projectEsModel = findProjectByProjectId(projectId);
        List<ProjectImage> projectImages = projectImageService.listImagesWithoutDeleted(projectId);
        if (Objects.isNull(projectEsModel) || CollectionUtils.isEmpty(projectImages)) {
            return;
        }
        Map<Integer, Integer> typeCount = projectImages
            .stream()
            .collect(Collectors.groupingBy(ProjectImage::getFileSourceType))
            .entrySet()
            .stream()
            .collect(Collectors.toMap(Map.Entry::getKey, d1 -> d1.getValue().size()));
        int droneImageNum = MapUtils.getIntValue(typeCount, com.bees360.internal.ai.entity.enums.FileSourceTypeEnum.DRONE_IMAGE.getCode());
        int mobileImageNum = MapUtils.getIntValue(typeCount, FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode());
        projectEsModel.setDroneImageCount(droneImageNum);
        projectEsModel.setMobileImageCount(mobileImageNum);
        if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
            var projectEsModelUpdater =
                    ProjectEsModelUpdater.toBuilder()
                            .setProjectId(projectId)
                            .setDroneImageCount(droneImageNum)
                            .setMobileImageCount(mobileImageNum)
                            .build();
            updatePartial(projectEsModelUpdater);
        } else {
            syncToEsFromProjectEsModel(projectEsModel, true);
        }
        projectLogService.addLogEntryByLogEntryType(projectId, user.getId(), LogEntryTypeEnum.PROJECT_DATA,
            new LogEntryDetail(LogProjectDataDetailEnum.PROJECT_IMAGE, actionEnum));
        log.info("updateProjectCountOnImageDownload add projectLog success, projectId:{}, droneCount:{}, mobileCount:{}",
            projectId, droneImageNum, mobileImageNum);
    }

    @Override
    public void updateImageScore(long projectId, User user, String pilotId, float score) {
        ProjectEsModel projectEsModel = findProjectByProjectIdCheckExisted(projectId);
        projectEsModel.setImageScore(score);
        if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
            var projectEsModelUpdater =
                    ProjectEsModelUpdater.toBuilder()
                            .setProjectId(projectId)
                            .setImageScore(score)
                            .build();
            updatePartial(projectEsModelUpdater);
        } else {
            syncToEsFromProjectEsModel(projectEsModel, true);
        }

        submitRatePilotScoreEvent(projectId, user.getId(), pilotId, score);
    }

    @Override
    public synchronized Boolean syncPartiallyToEsFromProjectEsModel(
            Map<String, Object> updateMap, String projectId, boolean sync) {
        boolean effected =
                elasticSearchHelper.partiallyUpdate(updateMap, projectId, ProjectEsModel.class);
        log.info("syncPartiallyToEsFromProjectEsModel sync success projectId:{}", projectId);
        return effected;
    }

    private void submitRatePilotScoreEvent(
            long projectId, String ratedBy, String pilotId, float score) {
        var event = new PilotRatedEvent();
        event.setScore(score);
        event.setRatedBy(ratedBy);
        event.setPilotId(pilotId);
        event.setProjectId(String.valueOf(projectId));
        eventPublisher.publish(event);
    }

    @Override
    public void updatePartial(UpdateProjectBatchItem item, String opUserId) {
        var param = ProjectEsQueryParam.builder().isSearchAll(true).inspectionNumber(item.getClaimNumber());
        var projects = findProjectListByQueryBuild(param.build());
        for (var project: projects) {
            updateExternalAdjuster(item, project, opUserId);
            if (item.hasEstimateTotalPay()) {
                updateEstimateTotalPay(project.getProjectId(), item.getEstimateTotalPay());
            }
        }
    }

    private void updateExternalAdjuster(UpdateProjectBatchItem item, ProjectEsModel project, String opUserId) {
        var member = new ExternalMember();
        member.setAuth(UserAuthEnum.EXTERNAL_ADJUSTER.getAuth());
        member.setName(item.getExternalAdjusterName());
        member.setEmail(item.getExternalAdjusterEmail());
        member.setPhone(item.getExternalAdjusterPhone());

        projectContactService.updateExternalAdjuster(project.getProjectId(), member, opUserId);
    }

    private void updateEstimateTotalPay(long projectId, Money estimateTotalPay) {
        var project = findProjectByProjectId(projectId);
        var bigDecimal = new BigDecimal(estimateTotalPay.getUnits() + "." + estimateTotalPay.getNanos());
        var totalPay = bigDecimal.doubleValue();
        if (bees360FeatureSwitch.isEnableSyncAiEsDataToSolid()) {
            claimManager.updateEstimateTotalPay(String.valueOf(projectId), totalPay);
        }
        project.setEstimateTotalPay(totalPay);
        elasticSearchHelper.update(project);
    }
}
