package com.bees360.internal.ai.service.impl;

import com.bees360.ai.mapper.ProjectImageMapper;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.geom.Point;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.image.Image;
import com.bees360.image.ImageGroupProvider;
import com.bees360.image.ImageGroupQuery;
import com.bees360.image.ImageTagCategoryEnum;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.ImageTagManager;
import com.bees360.image.Message;
import com.bees360.image.tag.ImageTag;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.consts.AiBotUser;
import com.bees360.internal.ai.entity.dto.IdValueDto;
import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectImageManager;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.project.image.ProjectImageProvider;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.bees360.report.entity.ProjectImageTag;
import com.bees360.util.Iterables;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class ProjectImageManagerImpl implements ProjectImageManager {

    @Autowired private ProjectImageMapper projectImageMapper;

    @Autowired private ProjectImageProvider projectImageProvider;

    @Autowired private ProjectEsService projectEsService;

    @Autowired private ImageTagManager imageTagManager;

    @Autowired private ImageGroupProvider imageGroupProvider;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    /**
     * 判断是否是来自app上传
     */
    private static final Predicate<String> isAppImage =
        Pattern.compile(
            "^project/\\d+/images/origin/.*(beespilot_app|_iOS|_Android).*").asPredicate();

    private static final List<String> NEW_OVERVIEW_INCLUDE_TAG =
            List.of(
                    String.valueOf(ImageTagEnum.ROOF.getCode()),
                    String.valueOf(ImageTagEnum.OVERVIEW.getCode()));

    private static final List<String> NEW_OVERVIEW_EXCLUDE_TAG =
            List.of(
                    String.valueOf(ImageTagEnum.PCA.getCode()),
                    String.valueOf(ImageTagEnum.REW.getCode()));

    private static final Set<String> OBJECT_AND_DIRECTION_CATEGORY =
            Set.of(
                    ImageTagCategoryEnum.OBJECT.getDisplay(),
                    ImageTagCategoryEnum.DIRECTION.getDisplay());

    private static final List<ImageTagEnum> OVERVIEW_INCLUDE_TAGS =
            List.of(ImageTagEnum.ROOF, ImageTagEnum.OVERVIEW);
    private static final List<ImageTagEnum> OVERVIEW_EXCLUDE_TAGS =
            new ArrayList<>() {
                {
                    add(ImageTagEnum.PCA);
                    add(ImageTagEnum.REW);
                    addAll(
                            Arrays.stream(ImageTagEnum.values())
                                    .filter(
                                            tagEnum ->
                                                    Set.of(
                                                                    ImageTagCategoryEnum.OBJECT,
                                                                    ImageTagCategoryEnum.DIRECTION)
                                                            .contains(tagEnum.getCategory()))
                                    .collect(Collectors.toList()));
                }
            };

    @Override
    public List<ProjectImage> saveNewImages(long projectId, List<ProjectImage> projectImages) {
        if (CollectionAssistant.isEmpty(projectImages)) {
            return null;
        }

        Set<String> existsImageIds = projectImageMapper.listProjectImageIdsIncludeDeleted(projectId);
        projectImages.removeIf(image -> existsImageIds.contains(image.getImageId()));

        if (CollectionAssistant.isEmpty(projectImages)) {
            return projectImages;
        }

        setDefaultSort(projectImages);
        projectImageMapper.saveAll(projectImages);
        return projectImages;
    }

    public List<ProjectImage> updateImagesFromWeb(long projectId, List<ProjectImage> projectImages){
        ProjectEsModel project = projectEsService.findProjectByProjectId(projectId);
        ProjectServiceTypeEnum serviceType =
            ProjectServiceTypeEnum.getEnum(project.getServiceType());

        if (serviceType.equals(ProjectServiceTypeEnum.POST_CONSTRUCTION_AUDIT)) {
            projectImages.forEach(
                    image -> {
                        if (isAppImage.test(image.getFileName())) {
                            imageTagManager.addImageTag(image.getImageId(), List.of(String.valueOf(ImageTagEnum.PCA.getCode())), AiBotUser.AI_NEW_USER_ID);
                        }
                    });
        }
        return projectImages;
    }

    private void setDefaultSort(List<ProjectImage> images) {
        images.forEach(
                image -> {
                    if (image.getImageSort() == null) {
                        image.setImageSort(ProjectImage.DEFAULT_SORT);
                    }
                });
    }

    @Override
    public List<ProjectImage> findProjectImages(long projectId, Integer fileSourceType, List<String> imageIds, Boolean isDeleted){
        List<ProjectImage> projectImages = projectImageMapper.listImages(projectId, fileSourceType, imageIds, isDeleted);
        updateTagToAssembleProjectImages(projectImages);
        return projectImages;
    }

    private String getParentIdOrOriginId(ProjectImage image) {
        if (image.getFileSourceType() != FileSourceTypeEnum.REPORT_IMAGE.getCode()) {
            return image.getImageId();
        }
        return StringUtils.isNotBlank(image.getParentId())
            ? image.getParentId()
            : image.getImageId();
    }

    private void updateTagToAssembleProjectImages(List<ProjectImage> projectImages) {
        var imageIds = projectImages.stream().map(this::getParentIdOrOriginId).collect(Collectors.toList());
        var imageTags = imageTagManager.findByImageIds(imageIds);

        for (var image : projectImages) {
            var imageId = getParentIdOrOriginId(image);
            updateTagToAssembleProjectImage(image, imageTags.get(imageId));
        }
    }

    private void updateTagToAssembleProjectImage(ProjectImage projectImage) {
        if (Objects.isNull(projectImage)) {
            return;
        }
        var imageId = getParentIdOrOriginId(projectImage);
        Iterable<? extends ImageTag> imageTags = imageTagManager.findByImageId(imageId);
        updateTagToAssembleProjectImage(projectImage, imageTags);
    }

    private void updateTagToAssembleProjectImage(ProjectImage projectImage, Iterable<? extends ImageTag> imageTags) {
        if (imageTags == null) {
            return;
        }
        for (ImageTag imageTag : imageTags) {
            int tagId = Integer.parseInt(imageTag.getId());
            var imageTagId = imageTag.getImageTagId();
            String attribute = imageTag.getAttribute();
            var tag = new ProjectImageTag(imageTagId, tagId, attribute);
            var tagCategory = getTagCategoryByDisplay(imageTag.getCategory());
            if (tagCategory == null) {
                continue;
            }
            switch (tagCategory) {
                case CATEGORY:
                    projectImage.setCategoryTag(tagId);
                    projectImage.setCategoryImageTag(tag);
                    break;
                case ORIENTATION:
                    projectImage.setOrientationTag(tagId);
                    projectImage.setOrientationImageTag(tag);
                    break;
                case OBJECT:
                    projectImage.setObjectTag(tagId);
                    projectImage.setObjectImageTag(tag);
                    break;
                case SCOPE:
                    projectImage.setScopeTag(tagId);
                    projectImage.setScopeImageTag(tag);
                    break;
                case DIRECTION:
                    projectImage.setDirectionTag(tagId);
                    projectImage.setDirectionImageTag(tag);
                    break;
                case LOCATION:
                    projectImage.setLocationTag(tagId);
                    projectImage.setLocationImageTag(tag);
                    break;
                case FLOOR_LEVEL:
                    projectImage.setFloorLevelTag(tagId);
                    projectImage.setFloorLevelImageTag(tag);
                    break;
                case NUMBER:
                    projectImage.setNumberTag(tagId);
                    projectImage.setNumberImageTag(tag);
                    break;
                case REPORT:
                    projectImage.setReportTag(tagId);
                    projectImage.setReportImageTag(tag);
                    break;
            }
        }
    }

    private ImageTagCategoryEnum getTagCategoryByDisplay(String display) {
        return Arrays.stream(ImageTagCategoryEnum.values())
                .filter(c -> c.getDisplay().equals(display))
                .findAny()
                .orElse(null);
    }

    @Override
    public Iterable<ProjectImage> findReallyAllByProjectId(long projectId) {
        return findProjectImages(projectId, null, null, null);
    }

    @Override
    public Iterable<ProjectImage> findAllByProjectId(long projectId) {
        return findProjectImages(projectId, null, null, false);
    }

    @Override
    public ProjectImage findById(String imageId) {
        ProjectImage oldProjectImage = projectImageMapper.findById(imageId);
        updateTagToAssembleProjectImage(oldProjectImage);
        return oldProjectImage;
    }

    @Override
    public ProjectImage findLatestUploadedImage(
            long projectId, Integer fileSourceType, Boolean isDeleted) {
        List<ProjectImage> imageList = findProjectImages(projectId, fileSourceType, null, isDeleted);
        if (CollectionAssistant.isEmpty(imageList)) {
            return null;
        }
        return imageList.stream()
                .max((o1, o2) -> (int) (o1.getUploadTime() - o2.getUploadTime()))
                .orElse(null);
    }

    @Override
    public void deleteProjectImagesLogically(long projectId, List<String> imageIds) {
        if (CollectionAssistant.isEmpty(imageIds)) {
            return;
        }
        updateImagesDeleteStatus(projectId, imageIds, true);
    }

    @Override
    public void deleteCompletely(long projectId, List<String> imageIds) {
        projectImageMapper.deleteCompletely(projectId, imageIds);
    }

    @Override
    public void recoverProjectImage(long projectId, List<String> imageIds) {
        if (CollectionAssistant.isEmpty(imageIds)) {
            return;
        }
        updateImagesDeleteStatus(projectId, imageIds, false);
    }

    /**
     * TODO shoushan 目前这个定义有点问题
     * 重构的过程后端应该逐渐淡化直到取消 overview，或提供指定唯一overview image的功能
     * 目前overview的定义是：打了 overview和roof tag，并且没有打pca,rew,direction和object tag,如果又多个，取最先拍摄的
     * projectImageProvider的参数时ImageTagEnum,但是这个时可随时新增的，Enum中不全，所以后面又增加了一层校验，参数应该改成code。
     */
    @Override
    public ProjectImage getNewImageOverviewTag(long projectId) {
        if (bees360FeatureSwitch.isEnableNewGetOverviewLogic()) {
            var queryMessage =
                Message.FindImagesByTagsAndGroupRequest.newBuilder()
                    .setGroupId(String.valueOf(projectId))
                    .setGroupType("GROUP_PROJECT")
                    .addAllIncludeTagId(NEW_OVERVIEW_INCLUDE_TAG)
                    .addAllExcludeTagId(NEW_OVERVIEW_EXCLUDE_TAG)
                    .addAllExcludeCategory(OBJECT_AND_DIRECTION_CATEGORY)
                    .build();
            var overviews = imageGroupProvider.findByQuery(ImageGroupQuery.from(queryMessage));
            if (com.google.common.collect.Iterables.isEmpty(overviews)) {
                return null;
            }
            var overview =
                Iterables.toStream(overviews)
                    .filter(i -> i.getShootingTime() != null)
                    .min(Comparator.comparing(i -> i.getShootingTime().toEpochMilli()))
                    .orElse(null);
            if (overview == null) {
                return null;
            }
            return projectImageMapper.findById(overview.getId());
        }
        var overviewImage =
                Iterables.toList(
                        projectImageProvider.findByTag(
                                String.valueOf(projectId),
                                OVERVIEW_INCLUDE_TAGS,
                                OVERVIEW_EXCLUDE_TAGS));
        if (com.google.common.collect.Iterables.isEmpty(overviewImage)) {
            return null;
        }
        var imageIds =
                Iterables.toStream(overviewImage).map(Image::getId).collect(Collectors.toList());
        var imageTags = imageTagManager.findByImageIds(imageIds);

        var overviewImageIds = new ArrayList<String>();
        for (Map.Entry<String, Iterable<? extends ImageTag>> entry : imageTags.entrySet()) {
            var imageTagCategories =
                    Iterables.toStream(entry.getValue())
                            .map(ImageTag::getCategory)
                            .collect(Collectors.toSet());
            imageTagCategories.retainAll(OBJECT_AND_DIRECTION_CATEGORY);
            if (imageTagCategories.size() <= 0) {
                overviewImageIds.add(entry.getKey());
            }
        }
        if (CollectionUtils.isEmpty(overviewImageIds)) {
            return null;
        }
        var projectImages = projectImageMapper.listImages(projectId, null, overviewImageIds, false);
        if (CollectionUtils.isEmpty(projectImages)) {
            return null;
        }

        return projectImages.stream()
                .min(Comparator.comparingLong(ProjectImage::getShootingTime))
                .get();
    }

    @Override
    public void deleteAll(long projectId) {
        updateImagesDeleteStatus(projectId, null, true);
    }

    private void updateImagesDeleteStatus(
            long projectId, List<String> imageIds, boolean isDeleted) {
        projectImageMapper.updateImageDeletedStatus(projectId, imageIds, isDeleted);
    }

    @Override
    public void updateImageSort(long projectId, List<IdValueDto<String, Long>> sorts) {
        if (CollectionAssistant.isEmpty(sorts)) {
            return;
        }
        projectImageMapper.updateImageSort(projectId, sorts);
    }

    @Override
    public void updateImageIn3DModel(long projectId, Integer in3DModel, List<String> imageIds) {
        projectImageMapper.updateImageIn3DModel(projectId, in3DModel, imageIds);
    }

    @Override
    public List<ProjectImage> findAllByIds(List<String> imageIds) {
        List<ProjectImage> projectImages = projectImageMapper.listByIds(imageIds);
        updateTagToAssembleProjectImages(projectImages);
        return projectImages;
    }

    public void resetRoofLayerDar(long projectId) {
        var roofLayerImages =
                Iterables.toList(
                        projectImageProvider.findByTag(
                                String.valueOf(projectId), List.of(ImageTagEnum.ROOF_LAYER)));
        if (CollectionUtils.isEmpty(roofLayerImages)) {
            return;
        }

        var darImages =
                Iterables.toList(
                        projectImageProvider.findByTag(
                                String.valueOf(projectId),
                                List.of(ImageTagEnum.ROOF_LAYER, ImageTagEnum.DAR)));
        if (CollectionUtils.isNotEmpty(darImages)) {
            return;
        }

        var overview = getNewImageOverviewTag(projectId);
        var roofLayerImageList =
                findAllByIds(
                        Iterables.toStream(roofLayerImages)
                                .map(Image::getId)
                                .collect(Collectors.toList()));
        var roofLayerImageId = getRoofLayerIdByOverview(roofLayerImageList, overview);

        var darTag = List.of(String.valueOf(ImageTagEnum.DAR.getCode()));

        imageTagManager.addImageTag(roofLayerImageId, darTag, AiBotUser.AI_NEW_USER_ID);
    }

    private String getRoofLayerIdByOverview(
            List<ProjectImage> roofLayerImages, ProjectImage overview) {
        var roofLayerImage = roofLayerImages.get(0);
        if (overview == null) {
            return roofLayerImage.getImageId();
        }
        var overviewGps =
                new Point(overview.getGpsLocationLongitude(), overview.getGpsLocationLatitude());

        var distance = Double.MAX_VALUE;
        for (var image : roofLayerImages) {
            var imageGps =
                    new Point(image.getGpsLocationLongitude(), image.getGpsLocationLatitude());
            var distanceToOverview = getTwoPointDistance(imageGps, overviewGps);
            if (distance > distanceToOverview) {
                distance = distanceToOverview;
                roofLayerImage = image;
            }
        }
        return roofLayerImage.getImageId();
    }

    private static double getTwoPointDistance(Point pointOne, Point pointTwo) {
        var xx = Math.abs(pointTwo.getX() - pointOne.getX());
        var yy = Math.abs(pointTwo.getY() - pointOne.getY());
        return Math.sqrt(xx * xx + yy * yy);
    }
}
