package com.bees360.internal.ai.service;

import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.entity.dto.IdValueDto;
import java.util.List;

/**
 * 将ProjectImageCacheWrapper的接口移这里,提供不同的实现逻辑
 */
public interface ProjectImageManager {

    List<ProjectImage> saveNewImages(long projectId, List<ProjectImage> projectImages);

    List<ProjectImage> updateImagesFromWeb(long projectId, List<ProjectImage> projectImages);

    Iterable<ProjectImage> findReallyAllByProjectId(long projectId);

    Iterable<ProjectImage> findAllByProjectId(long projectId);

    ProjectImage findById(String imageId);

    /**
     * IDE中显示ProjectImageCacheWrapper.findLatestUploadedImage没有被调用，标记为deprecated
     * @deprecated
     * @return
     */
    ProjectImage findLatestUploadedImage(long projectId, Integer fileSourceType, Boolean isDeleted);

    void deleteProjectImagesLogically(long project, List<String> imageIds);

    void deleteCompletely(long projectId, List<String> imageIds);

    void recoverProjectImage(long project, List<String> imageIds);

    void deleteAll(long projectId);

    void updateImageSort(long projectId, List<IdValueDto<String, Long>> sorts);

    void updateImageIn3DModel(long projectId, Integer in3DModel, List<String> imageIds);

    List<ProjectImage> findAllByIds(List<String> imageIds);

    com.bees360.internal.ai.entity.ProjectImage getNewImageOverviewTag(long projectId);

    Iterable<ProjectImage> findProjectImages(long projectId, Integer fileSourceType, List<String> imageIds, Boolean isDeleted);

    /** 将一张离overview最近的RoofLayer image设置Dar tag， */
    void resetRoofLayerDar(long projectId);
}
