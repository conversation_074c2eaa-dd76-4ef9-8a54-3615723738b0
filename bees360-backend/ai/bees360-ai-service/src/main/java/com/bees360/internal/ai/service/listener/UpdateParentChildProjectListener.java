package com.bees360.internal.ai.service.listener;

import com.bees360.event.registry.ProjectParentChildGroupUpdatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import jodd.util.StringUtil;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听项目父子关系更新事件，更新Elasticsearch中项目的父子关系状态。
 */
@Log4j2
public class UpdateParentChildProjectListener extends AbstractNamedEventListener<ProjectParentChildGroupUpdatedEvent> {

    private final ProjectEsService projectEsService;

    public UpdateParentChildProjectListener(ProjectEsService projectEsService) {
        this.projectEsService = projectEsService;
    }

    @Override
    public void handle(ProjectParentChildGroupUpdatedEvent event) throws IOException {
        log.info("Received event: {}.", event);
        var projectId = event.getProjectId();
        var groupKey = event.getGroupKey();
        boolean isDeleted = event.isDeleted();

        if (StringUtil.equals(projectId, groupKey)) {
            // when projectId = groupKey and isDeleted = false，update is_parent_project = true
            // when projectId = groupKey and isDeleted = true，update is_parent_project = false
            var updater = ProjectEsModelUpdater.toBuilder()
                    .setProjectId(Long.parseLong(projectId))
                    .setIsParentProject(!isDeleted)
                    .build();

            projectEsService.updatePartial(updater);
            log.info("Updated project {} as parent project: {}", projectId, !isDeleted);
        } else {
            // when projectId != groupKey and isDeleted = false，set is_child_project = true
            // when projectId != groupKey and isDeleted = true，set is_child_project = false
            var updater = ProjectEsModelUpdater.toBuilder()
                    .setProjectId(Long.parseLong(projectId))
                    .setIsChildProject(!isDeleted)
                    .build();

            projectEsService.updatePartial(updater);
            log.info("Updated project {} as child project: {}", projectId, !isDeleted);
        }
    }
}
