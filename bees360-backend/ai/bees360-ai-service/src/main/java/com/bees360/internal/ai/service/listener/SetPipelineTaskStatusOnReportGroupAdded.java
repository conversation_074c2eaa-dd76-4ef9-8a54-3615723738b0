package com.bees360.internal.ai.service.listener;

import com.bees360.api.InvalidArgumentException;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.report.Message.ReportMessage.Resource.Type;
import com.bees360.report.ReportProvider;
import com.bees360.report.ReportResource;
import com.bees360.util.Iterables;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.stream.Collectors;

/**
 * 该类是一个事件监听器，主要功能是处理报告组添加事件，并根据报告资源类型更新管道任务状态。
 */
@Log4j2
public class SetPipelineTaskStatusOnReportGroupAdded
        extends AbstractNamedEventListener<ReportGroupAdded> {

    private final PipelineService pipelineService;

    private final ReportProvider reportProvider;

    public SetPipelineTaskStatusOnReportGroupAdded(
            PipelineService pipelineService,
            ReportProvider reportProvider) {
        this.pipelineService = pipelineService;
        this.reportProvider = reportProvider;
        log.info(
                "Created {}(pipelineService={}, reportProvider={}).",
                this,
                pipelineService,
                reportProvider);
    }

    @Override
    public void handle(ReportGroupAdded event) throws IOException {
        log.info("Set pipeline task status on ReportGroupAdded event:{}", event.toString());
        if (!DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE.equalsIgnoreCase(
                event.getGroupType())) {
            return;
        }
        var report = reportProvider.get(event.getReportId());
        if (Iterables.isEmpty(report.getResources())) {
            return;
        }
        var resourceTypeSet =
                Iterables.toStream(report.getResources())
                        .map(ReportResource::getType)
                        .collect(Collectors.toSet());
        if (!resourceTypeSet.contains(Type.ORIGIN)) {
            return;
        }
        var reportType = ReportTypeEnum.getReportType(report);

        var taskDefKey = String.join("_", "generate", reportType.getShortCut().toLowerCase());

        String pipelineId = event.getGroupKey();
        Message.PipelineStatus status = Message.PipelineStatus.DONE;
        try {
            pipelineService.setTaskStatus(pipelineId, taskDefKey, status);
            log.info(
                    "Set pipeline {} task {} into status {} by report action event {}.",
                    pipelineId,
                    taskDefKey,
                    status,
                    event);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn(
                    "Failed to set pipeline {} task {} to '{}'.",
                    pipelineId,
                    taskDefKey,
                    status,
                    e);
        } catch (RuntimeException e) {
            log.error(
                    "Failed to set pipeline '{}' task '{}' to '{}'.",
                    pipelineId,
                    taskDefKey,
                    status,
                    e);
        }
    }
}
