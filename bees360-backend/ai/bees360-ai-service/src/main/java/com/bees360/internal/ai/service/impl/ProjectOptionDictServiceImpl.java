package com.bees360.internal.ai.service.impl;

import com.bees360.commons.elasticsearchsupport.ElasticSearchHelper;
import com.bees360.commons.elasticsearchsupport.EsAccessException;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.internal.ai.entity.LogEntryTypeDict;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectOptionDict;
import com.bees360.internal.ai.entity.dto.CodeNameDto;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.ClaimTypeEnum;
import com.bees360.internal.ai.entity.enums.CompanyTypeEnum;
import com.bees360.internal.ai.entity.enums.ProjectHoverStatusEnum;
import com.bees360.internal.ai.entity.enums.ProjectOptionDictTypeEnum;
import com.bees360.internal.ai.entity.enums.ProjectPlnarStatusEnum;
import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.internal.ai.entity.enums.ProjectTypeEnum;
import com.bees360.internal.ai.entity.enums.UserAuthEnum;
import com.bees360.internal.ai.entity.vo.ProjectEsOptionDict;
import com.bees360.internal.ai.service.MemberService;
import com.bees360.internal.ai.service.ProjectOptionDictService;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/05/12 10:50
 */
@Slf4j
@Service
public class ProjectOptionDictServiceImpl implements ProjectOptionDictService {

    @Autowired
    private ElasticSearchHelper elasticSearchHelper;

    @Autowired
    private MemberService cacheMemberService;

    @Override
    public void addByNewProject(ProjectEsModel esModel) {
        if (Objects.isNull(esModel)) {
            return;
        }
        if (StringUtils.isNotBlank(esModel.getPilotName())) {
            addProjectDict(ProjectOptionDictTypeEnum.PILOT.getType(), Collections.singleton(esModel.getPilotName()));
        }
        if (StringUtils.isNotBlank(esModel.getCity())) {
            addProjectDict(ProjectOptionDictTypeEnum.CITY.getType(), Collections.singleton(esModel.getCity()));
        }
        if (StringUtils.isNotBlank(esModel.getState())) {
            addProjectDict(ProjectOptionDictTypeEnum.STATE.getType(), Collections.singleton(esModel.getState()));
        }
        if (StringUtils.isNotBlank(esModel.getRepairCompanyName()) || StringUtils.isNotBlank(esModel.getInsuranceCompanyName())
            || StringUtils.isNotBlank(esModel.getCompanyName())) {
            Set<String> company = new HashSet<>();
            Optional.ofNullable(esModel.getRepairCompanyName()).ifPresent(company::add);
            Optional.ofNullable(esModel.getInsuranceCompanyName()).ifPresent(company::add);
            Optional.ofNullable(esModel.getCompanyName()).ifPresent(company::add);
            addProjectDict(ProjectOptionDictTypeEnum.COMPANY.getType(), company);
        }
        if (StringUtils.isNotBlank(esModel.getCreatorName())) {
            addProjectDict(ProjectOptionDictTypeEnum.CREATOR.getType(), Collections.singleton(esModel.getCreatorName()));
        }
    }

    @Override
    public ProjectEsOptionDict getOptionDict() {
        // 获取常量对应字典
        ProjectEsOptionDict dict = new ProjectEsOptionDict();
        dict.setServiceType(ProjectServiceTypeEnum.getDict());

        dict.setNewProjectStatus(AiProjectStatusEnum.getVisibleDict());

        dict.setCompanyType(CompanyTypeEnum.getDict());

        dict.setProjectType(ProjectTypeEnum.getDict());

        dict.setClaimType(ClaimTypeEnum.getDict());

        dict.setLogDict(LogEntryTypeDict.loadLogEntryTypeDict());

        dict.setHoverStatus(ProjectHoverStatusEnum.getHoverStatusDict());
        dict.setPlnarStatus(ProjectPlnarStatusEnum.getPlnarStatusDict());

        dict.setReportStatus(ReportGenerationStatusEnum.getVisibleDict()
            .stream().map(o -> new CodeNameDto(o.getCode(), o.getName()))
            .collect(Collectors.toList()));

        dict.setMemberRole(UserAuthEnum.getMemberRoleDict());

        // 以下关于project搜索项的字典都从project_option_dict索引中获取
        var begin = System.currentTimeMillis();
        List<ProjectOptionDict> optionDicts = getByIds(ProjectOptionDictTypeEnum.getOptionTypes());
        var end = System.currentTimeMillis();
        log.debug("getEsOptionDictsByIds Time consumes {} millis", end - begin);
        if (CollectionUtils.isNotEmpty(optionDicts)) {
            optionDicts.forEach(o -> {

                if (StringUtils.equals(o.getType(), ProjectOptionDictTypeEnum.COMPANY.getType())) {
                    dict.setCompany(o.getDict());
                }
                if (StringUtils.equals(o.getType(), ProjectOptionDictTypeEnum.STATE.getType())) {
                    dict.setState(o.getDict());
                }
                if (StringUtils.equals(o.getType(), ProjectOptionDictTypeEnum.CITY.getType())) {
                    dict.setCity(o.getDict());
                }
            });
        }

        var begin2 = System.currentTimeMillis();
        setStaffSearchOptions(dict);
        var end2 = System.currentTimeMillis();
        log.debug("setStaffSearchOptions Time consumes {} millis", end2 - begin2);
        return dict;
    }

    @Override
    public void addProjectDict(String type, Set<String> dict) {
        ProjectOptionDict optionDict = getByType(type);
        if (Objects.isNull(optionDict)) {
            optionDict = new ProjectOptionDict();
            optionDict.setType(type);
            optionDict.setDict(dict);
        } else {
            optionDict.getDict().addAll(dict);
        }
        upsertProjectOptionDict(optionDict);
    }

    private void upsertProjectOptionDict(ProjectOptionDict optionDict) {
        try {
            elasticSearchHelper.update(optionDict);
        } catch (EsAccessException e) {
            log.warn("upsertProjectOptionDict error, log:{} msg:{}", new Gson().toJson(optionDict),
                e.getMessage(), e);
        }
    }

    private void setStaffSearchOptions(ProjectEsOptionDict dict) {
        dict.setAdjuster(cacheMemberService.getMemberNameByRole(ProjectOptionDictTypeEnum.ADJUSTER));
        dict.setReviewer(cacheMemberService.getMemberNameByRole(ProjectOptionDictTypeEnum.REVIEWER));
        dict.setProcessor(cacheMemberService.getMemberNameByRole(ProjectOptionDictTypeEnum.PROCESSOR));
        dict.setCreator(cacheMemberService.getMemberNameByRole(ProjectOptionDictTypeEnum.CREATOR));
        dict.setPilots(cacheMemberService.getMemberNameByRole(ProjectOptionDictTypeEnum.PILOT));
        dict.setProducers(cacheMemberService.getMemberNameByRole(ProjectOptionDictTypeEnum.PRODUCER));
    }

    @Override
    public List<ProjectOptionDict> getByIds(List<String> types) {
        return elasticSearchHelper.searchListResultById(types, ProjectOptionDict.class);
    }

    @Override
    public ProjectOptionDict getByType(String type) {
        return elasticSearchHelper.getById(type, ProjectOptionDict.class).orElse(null);
    }
}
