package com.bees360.internal.ai.service.listener;

import com.bees360.api.InvalidArgumentException;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.registry.ReportResourceAdded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.report.ReportProvider;
import com.bees360.util.Iterables;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Set;

/**
 * 监听报告资源添加事件并更新相关管道任务状态
 */
@Log4j2
public class SetPipelineTaskStatusOnReportResourceAdded
        extends AbstractNamedEventListener<ReportResourceAdded> {

    private static final Set<String> SUPPORTED_REPORT_TYPES =
            Set.of(ReportTypeEnum.INVOICE.getShortCut());

    private static final Set<Integer> SUPPORTED_REPORT_RESOURCE_TYPES =
            Set.of(com.bees360.report.Message.ReportMessage.Resource.Type.ORIGIN_VALUE);

    private final ProjectReportManager projectReportManager;

    private final PipelineService pipelineService;

    private final ReportProvider reportProvider;

    public SetPipelineTaskStatusOnReportResourceAdded(
            ProjectReportManager projectReportManager,
            PipelineService pipelineService,
            ReportProvider reportProvider) {
        this.projectReportManager = projectReportManager;
        this.pipelineService = pipelineService;
        this.reportProvider = reportProvider;
        log.info(
                "Created {}(projectReportManager={}, pipelineService={}, reportProvider={}).",
                this,
                projectReportManager,
                pipelineService,
                reportProvider);
    }

    @Override
    public void handle(ReportResourceAdded event) throws IOException {
        log.info("Set pipeline task status on ReportResourceAdded event:{}", event.toString());
        if (!SUPPORTED_REPORT_TYPES.contains(event.getReportType())
                || !SUPPORTED_REPORT_RESOURCE_TYPES.contains(event.getResourceType())) {
            return;
        }
        var reportId = event.getId();
        var report = reportProvider.get(reportId);
        var projectIds = Iterables.toList(projectReportManager.findProjectId(reportId));

        var reportType = ReportTypeEnum.getReportType(report);

        var taskDefKey = String.join("_", "generate", reportType.getShortCut().toLowerCase());

        for (var projectId : projectIds) {
            setPipelineTask(event, projectId, taskDefKey);
        }
    }

    private void setPipelineTask(ReportResourceAdded event, String pipelineId, String taskDefKey) {
        Message.PipelineStatus status = Message.PipelineStatus.DONE;
        try {
            pipelineService.setTaskStatus(pipelineId, taskDefKey, status);
            log.info(
                    "Set pipeline {} task {} into status {} by report action event {}.",
                    pipelineId,
                    taskDefKey,
                    status,
                    event);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn(
                    "Failed to set pipeline {} task {} to '{}'.",
                    pipelineId,
                    taskDefKey,
                    status,
                    e);
        } catch (RuntimeException e) {
            log.error(
                    "Failed to set pipeline '{}' task '{}' to '{}'.",
                    pipelineId,
                    taskDefKey,
                    status,
                    e);
        }
    }
}
