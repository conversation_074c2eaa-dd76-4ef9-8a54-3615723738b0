package com.bees360.internal.ai.service.firebase;

import com.bees360.commons.firebasesupport.service.FirebaseHelper;
import com.bees360.job.registry.SerializableFirebaseProject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FirebaseProjectService {

    public final static String collection = "project";

    @Autowired
    private FirebaseHelper firebaseHelper;

    public SerializableFirebaseProject getByProjectId(long projectId) {
        return firebaseHelper.getDataById(collection, String.valueOf(projectId), SerializableFirebaseProject.class);
    }

}
