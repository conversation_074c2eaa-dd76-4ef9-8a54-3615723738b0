package com.bees360.internal.ai.service.scheduled;

import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectListResult;
import com.bees360.internal.ai.entity.ProjectStatusVo;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.DashBoardRoleTagToDoEnum;
import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.entity.query.ProjectEsQueryParam;
import com.bees360.internal.ai.util.date.DateUtil;
import io.jsonwebtoken.lang.Collections;

import java.util.Comparator;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * archived
 * <AUTHOR>
 * @date 2020/07/15 18:11
 */
@Slf4j
@Component
public class ProjectEsDeletedScheduled {

    private final ProjectEsService projectEsService;

    public ProjectEsDeletedScheduled(ProjectEsService projectEsService) {
        this.projectEsService = projectEsService;
        log.info("Created '{}(projectEsService={})'", this, this.projectEsService);
    }

    public void softDeleteProjectEsData() {
        log.info("ProjectEsDeletedScheduled CronTriggerEverySundayAt0AmCst");
        ProjectEsQueryParam queryParam = ProjectEsQueryParam.builder()
            .roleTag(DashBoardRoleTagToDoEnum.ADMIN_TODO.getRoleTag())
            .isSearchAll(true)
            .statusTag(ProjectListResult.StatusTag.CLOSED).build();
        List<ProjectEsModel> projectEsModelList = projectEsService.findProjectListByQueryBuild(queryParam);
        if (Collections.isEmpty(projectEsModelList)) {
            return;
        }
        projectEsModelList.stream()
            .filter(this::isReadyArchive)
            .forEach(
                e -> {
                    try {
                        projectEsService.softDeleteProjectDataByProjectId(e.getProjectId());
                    } catch (Exception exception) {
                        log.warn(
                            "Project archived error projectId {}",
                            e.getProjectId(),
                            exception);
                    }
                    log.info("Project archived complete projectId {}", e.getProjectId());
                });
    }

    private boolean isReadyArchive(ProjectEsModel e) {
        LocalDateTime dateTime = DateUtil.getUSCentralNow().minusDays(15);
        if (Objects.equals(AiProjectStatusEnum.CLIENT_RECEIVED.getCode(), e.getProjectStatus())) {
            Optional<Long> laterThanCurrentTime15Day = e.getTimeLines().stream()
                .filter(o -> Objects.equals(o.getStatus().getCode(), AiProjectStatusEnum.CLIENT_RECEIVED.getCode()))
                .map(ProjectStatusVo::getCreatedTime)
                .filter(Objects::nonNull)
                .max(Comparator.naturalOrder())
                .filter(time -> LocalDateTime.ofInstant(Instant.ofEpochMilli(time), DateUtil.getUSCentralZoneId())
                    .isBefore(dateTime));
            return laterThanCurrentTime15Day.isPresent();
        }
        // Underwriting Return to client 状态不能Archive
        if (Objects.equals(AiProjectStatusEnum.RETURNED_TO_CLIENT.getCode(), e.getProjectStatus()) &&
            ProjectServiceTypeEnum.isUnderWriting(e.getServiceType())) {
            return false;
        }
        return true;
    }
}
