package com.bees360.internal.ai.service.impl;

import com.bees360.atomic.RedisLockProvider;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.common.msgcode.MsgCodeManager;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.entity.ProjectStatusVo;
import com.bees360.internal.ai.entity.consts.RedisLockKey;
import com.bees360.internal.ai.entity.dto.BatchUpdateResultDto;
import com.bees360.internal.ai.entity.dto.CodeNameDto;
import com.bees360.internal.ai.entity.dto.ProjectsStatusModifierDto;
import com.bees360.internal.ai.entity.dto.UpdateFailedDto;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.event.ProjectAiStatusChangeEvent;
import com.bees360.internal.ai.event.ProjectReworkEvent;
import com.bees360.internal.ai.service.AiUserService;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectStatusService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.project.Message;
import com.bees360.project.status.ProjectProcessStatusManager;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.user.User;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bees360.internal.ai.entity.enums.AiBotUserEnum.AI_NEW_USER_ID;
import static com.bees360.internal.ai.entity.enums.AiBotUserEnum.WEB_NEW_USER_ID;

/**
 * <AUTHOR>
 * @date 2020/05/09 17:29
 */
@Slf4j
@Service
public class ProjectStatusServiceImpl implements ProjectStatusService {

    @Autowired
    private ProjectEsService projectEsService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private AiUserService aiUserService;

    @Autowired
    private RedisLockProvider projectLockProvider;

    @Autowired private RabbitEventPublisher eventPublisher;

    @Autowired
    @Qualifier("closeProjectChecker")
    public BiFunction<Long, ProjectsStatusModifierDto, UpdateFailedDto> closeProjectChecker;

    @Autowired
    @Qualifier("estimateCompletedChecker")
    private Function<Long, UpdateFailedDto> estimateCompletedChecker;

    @Autowired
    @Qualifier("aiProjectStatusChecker")
    private BiFunction<Long, ProjectsStatusModifierDto, UpdateFailedDto> aiProjectStatusChecker;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Autowired private ProjectStatusManager projectStatusManager;


    @Autowired private ProjectProcessStatusManager projectProcessStatusManager;

    private static final String AI_HOST = "AI";

    @Override
    public void reCoverProjectStatus(User user, long projectId, int code) {
        // recover逻辑抽离，取出timeLine最后一个状态
        ProjectEsModel project = projectEsService.findProjectByProjectId(projectId);
        if (Objects.isNull(project)) {
            return;
        }
        boolean canRecover = canRecover(project, code);
        // 判断是否可recover
        if (!canRecover) {
            throw new ServiceException(MsgCodeManager.PROJECT.PROJECT_STATUS_CAN_NOT_UPDATED);
        }
        List<ProjectStatusVo> timeLines = project.getTimeLines();
        if (CollectionUtils.isEmpty(timeLines)) {
            throw new ServiceException(MsgCodeManager.PROJECT.PROJECT_STATUS_CAN_NOT_UPDATED);
        }
        timeLines = timeLines.stream()
            .filter(o -> !AiProjectStatusEnum.getDeletedStatusCode().contains(o.getStatus().getCode()))
            .collect(Collectors.toList());
        Integer newStatus = timeLines
            .stream()
            .max(Comparator.comparing(ProjectStatusVo::getCreatedTime))
            .map(ProjectStatusVo::getStatus)
            .map(CodeNameDto::getCode)
            .orElseThrow(() -> new ServiceException(MsgCodeManager.PROJECT.PROJECT_STATUS_CAN_NOT_UPDATED));
        updateStatusByAiUser(projectId, newStatus, user, timeLines);

    }

    @Override
    public void updateStatusByAiUser(long projectId, int code, User user, List<ProjectStatusVo> timeLines) {
        log.info("Update status by aiUser. projectId:{}, code:{}, userId:'{}', timeLines='{}'", projectId, code, user.getId(), timeLines);
        ProjectEsModel project = projectEsService.findProjectByProjectId(projectId);
        if (Objects.isNull(project)) {
            return;
        }
        int preStatusCode = project.getProjectStatus();
        long createdTime = System.currentTimeMillis();

        // 将AI特有的status变更写入PG
        syncAiProjectStatusToSolid(projectId, code, preStatusCode, createdTime, user.getId());

        project.setStatusUpdateTime(createdTime);
        project.setProjectStatus(code);
        project.setTimeLines(buildTimeLines(timeLines));
        Boolean result;
        if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
            var projectEsModelUpdater =
                ProjectEsModelUpdater.toBuilder()
                    .setProjectId(projectId)
                    .setStatusUpdateTime(project.getStatusUpdateTime())
                    .setProjectStatus(project.getProjectStatus())
                    .setTimeLines(project.getTimeLines())
                    .build();
            result =  projectEsService.updatePartialReturningIfChanged(projectEsModelUpdater);
        } else {
            result = projectEsService.syncToEsFromProjectEsModel(project, false);
        }
        if (!result) {
            log.error("updateProjectStatus error after call es, projectId:{}, code:{}", projectId, code);
            throw new ServiceException(MsgCodeManager.PROJECT.PROJECT_STATUS_UPDATE_FAILED);
        }
        publisher.publishEvent(new ProjectAiStatusChangeEvent(this, projectId, user.getId(),
            user.getName(),
            createdTime, preStatusCode, code));
    }

    @Override
    public void updateStatusByAiUser(long projectId, int code, User user) {
        updateStatus(projectId, user, code, System.currentTimeMillis());

    }


    @Override
    public void updateStatusByAiUser(long projectId, int code, String userId) {
        User user = aiUserService.getUserById(userId);
        updateStatus(projectId, user, code, System.currentTimeMillis());
    }

    @Override
    public void updateStatus(long projectId, User user, int newStatusCode, Long statusCreateTime) {
        log.info("Update status. projectId:{}, newStatusCode:{}, userId:'{}', statusCreateTime='{}'", projectId, newStatusCode, user.getId(), statusCreateTime);
        var lock = projectLockProvider.lock(RedisLockKey.PROJECT_LOCK_KEY + projectId);
        ProjectEsModel project;
        int preStatusCode;
        try {
            project = projectEsService.findProjectByProjectId(projectId);
            if (!AiProjectStatusEnum.containsCode(newStatusCode)) {
                throw new ServiceException(MsgCodeManager.PROJECT.PROJECT_STATUS_IS_WRONG);
            }
            if (Objects.isNull(project)) {
                // 存在只在web端操作，没有同步到ai端的project，无需修改状态
                throw new ServiceException(MsgCodeManager.PROJECT.PROJECT_IS_NULL);
            }
            if (Objects.isNull(user) || StringUtils.isBlank(user.getName())) {
                // 不存在操作用户
                throw new ServiceException(MsgCodeManager.USER.USER_IS_NULL);
            }

            preStatusCode = project.getProjectStatus();
            // 该方法里面更新了project，会被syncToEsFromProjectEsModel方法更新至es，通过其他方法更新时也应该注意更新这部分信息
            setProjectUpdateInfo(newStatusCode, project, user.getName(), statusCreateTime);

            // 将AI特有的status变更写入PG
            syncAiProjectStatusToSolid(projectId, newStatusCode, preStatusCode, statusCreateTime, user.getId());

            Boolean result;
            if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
                var projectEsModelUpdater =
                    ProjectEsModelUpdater.toBuilder()
                        .setProjectId(projectId)
                        .setStatusUpdateTime(project.getStatusUpdateTime())
                        .setProjectStatus(project.getProjectStatus())
                        .setTimeLines(project.getTimeLines())
                        .build();
                result = projectEsService.updatePartialReturningIfChanged(projectEsModelUpdater);
            } else {
                result = projectEsService.syncToEsFromProjectEsModel(project, false);
            }
            if (!result) {
                log.error("updateProjectStatus error after call es, projectId:{}, code:{}", project.getProjectId(), newStatusCode);
                throw new ServiceException(MsgCodeManager.PROJECT.PROJECT_STATUS_UPDATE_FAILED);
            }
        } finally {
            lock.unlock();
        }

        // 将AI触发的且不是AI特有的status变更写入PG
        // TODO rework操作有单独的流程同步回web
        var status = NewProjectStatusEnum.getEnumByAiCode(newStatusCode);
        if (!StringUtils.equals(user.getId(), WEB_NEW_USER_ID.getCode())
            && status != null
            && status != NewProjectStatusEnum.PROJECT_REWORK) {
            projectStatusManager.updateStatus(
                String.valueOf(projectId),
                Message.ProjectStatus.forNumber(status.getCode()),
                AI_NEW_USER_ID.getCode(),
                Instant.ofEpochMilli(statusCreateTime));
        }

        publisher.publishEvent(new ProjectAiStatusChangeEvent(this, projectId, user.getId(), user.getName(), statusCreateTime,
            preStatusCode, newStatusCode));
    }

    /**
     * 将AI特有的status变更写入PG
     *
     * @param projectId     project id
     * @param newStatusCode new project status code
     * @param preStatusCode pre project status code
     * @param createdTime   project status created time
     * @param userId        project status user id
     */
    private void syncAiProjectStatusToSolid(long projectId, int newStatusCode, int preStatusCode, long createdTime, String userId) {
        var status = NewProjectStatusEnum.getEnumByAiCode(newStatusCode);
        if (bees360FeatureSwitch.isEnableSyncAiEsDataToSolid() && status == null) {
            log.info("Synchronize AI-specific status changes to PG. projectId:{}, newStatusCode:{}, preStatusCode:{}, createdTime:{}, userId:{}",
                    projectId, newStatusCode, preStatusCode, createdTime, userId);
            var projectStatus = Message.ProjectStatus.forNumber(newStatusCode);
            projectProcessStatusManager.updateProcessStatus(String.valueOf(projectId),
                    projectStatus,
                    userId);
        }
    }

    private void setProjectUpdateInfo(int code, ProjectEsModel project, String username, Long createTime) {
        ProjectStatusVo projectStatusVo = buildProjectStatusVo(createTime, username, AiProjectStatusEnum.getEnum(code));
        if (Objects.isNull(project.getTimeLines())) {
            log.info("ProjectEsModel timelines is null. projectId=" + project.getProjectId());
            project.setTimeLines(new ArrayList<>());
        }
        project.getTimeLines().add(projectStatusVo);
        project.setProjectStatus(code);
        project.setStatusUpdateTime(createTime);
        project.setTimeLines(buildTimeLines(project.getTimeLines()));
    }


    private boolean canRecover(ProjectEsModel project, int code) {
        return project.getTimeLines().stream()
            .map(ProjectStatusVo::getStatus)
            .map(CodeNameDto::getCode)
            .anyMatch(latest -> AiProjectStatusEnum.canRecover(latest, code));
    }

    @Override
    public ProjectStatusVo buildProjectStatusVo(Long currentTime, String username, AiProjectStatusEnum projectStatusEnum) {
        ProjectStatusVo projectStatusVo = new ProjectStatusVo();
        projectStatusVo.setUserName(username);
        projectStatusVo.setCreatedTime(currentTime);
        projectStatusVo.setStatus(new CodeNameDto(projectStatusEnum.getCode(), projectStatusEnum.getDisplay()));
        return projectStatusVo;
    }

    @Override
    public List<ProjectStatusVo> buildTimeLines(List<ProjectStatusVo> timeLines) {
        if (CollectionUtils.isEmpty(timeLines)) {
            return Collections.emptyList();
        }
        Function<ProjectStatusVo, String> keyMapper =
                timeline ->
                        Strings.concat(
                                String.valueOf(timeline.getStatus().getCode()),
                                String.valueOf(timeline.getCreatedTime()));
        return timeLines.stream()
                .collect(Collectors.toMap(keyMapper, Function.identity(), (k1, k2) -> k1))
                .values()
                .stream()
                .sorted(Comparator.comparing(ProjectStatusVo::getCreatedTime))
                .collect(Collectors.toList());
    }

    @Override
    public BatchUpdateResultDto batchUpdateStatus(ProjectsStatusModifierDto projectsStatusModifierDto, User user) {
        List<Long> projectIds = projectsStatusModifierDto.getProjectIds();
        int status = projectsStatusModifierDto.getStatus();
        List<Long> succeed = new ArrayList<>();
        List<UpdateFailedDto> failed = new ArrayList<>();
        for (long projectId : projectIds) {
            var updateFailDto = checkIfUpdateStatusAllowed(projectId, projectsStatusModifierDto);
            if (updateFailDto != null) {
                failed.add(updateFailDto);
                continue;
            }

            try {
                updateStatusByAiUser(projectId, status, user);
                succeed.add(projectId);
            } catch (ServiceException e) {
                failed.add(UpdateFailedDto.builder().id(projectId).message(e.getMessage()).build());
                log.warn("update project status failed. projectId:{}, status:{}", projectId, status, e);
            }
        }

        return BatchUpdateResultDto.builder().succeed(succeed).failed(failed).build();
    }

    private UpdateFailedDto checkIfUpdateStatusAllowed(Long projectId, ProjectsStatusModifierDto statusDto) {
        var status = statusDto.getStatus();
        UpdateFailedDto updateFailedDto = null;
        if (status == AiProjectStatusEnum.CLIENT_RECEIVED.getCode()) {
            updateFailedDto = closeProjectChecker.apply(projectId, statusDto);
        } else if (status == AiProjectStatusEnum.ESTIMATE_COMPLETED.getCode()) {
            updateFailedDto = estimateCompletedChecker.apply(projectId);
        }
        if (updateFailedDto == null) {
            updateFailedDto = aiProjectStatusChecker.apply(projectId, statusDto);
        }
        // Return null if checking is passed.
        return updateFailedDto;
    }

    @Override
    public void reworkProject(long projectId, String userId, String title, String content) {
        log.info("rework project projectId {}, userId {}, title {}, content {}", projectId, userId, title, content);
        ProjectEsModel project = projectEsService.findProjectByProjectId(projectId);
        if (null == project) {
            return;
        }

        updateStatusByAiUser(projectId, AiProjectStatusEnum.PROJECT_REWORK.getCode(), userId);
        publisher.publishEvent(new ProjectReworkEvent(this, projectId, title, content));

        var projectType =
                com.bees360.project.base.Message.ProjectType.forNumber(
                        ProjectServiceTypeEnum.isClaim(project.getServiceType()) ? 1 : 2);
        eventPublisher.publish(
                new com.bees360.event.registry.ProjectReworkEvent(
                        String.valueOf(projectId), projectType, userId, AI_HOST));
    }

}
