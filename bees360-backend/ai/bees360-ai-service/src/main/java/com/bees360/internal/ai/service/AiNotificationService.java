package com.bees360.internal.ai.service;

import com.bees360.internal.ai.entity.dto.ProcessorClaimStatistics;
import com.bees360.internal.ai.entity.dto.ProcessorUWStatistics;
import com.bees360.internal.ai.entity.dto.ClaimWorkloadStatistics;
import com.bees360.internal.ai.entity.dto.UWWorkloadStatistics;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

public interface AiNotificationService {

    /**
     * 发送员工的工作量统计给员工本人
     * @param startDate 统计开始日期
     * @param endDate
     * @param summaryStatistics 员工工作量汇总
     * @param detailStatistics 员工工作量详情
     * @param recipients 员工的收件地址
     */
    void sendWorkloadStatisticsToStaff(
            LocalDate startDate,
            LocalDate endDate,
            Collection<ClaimWorkloadStatistics> summaryStatistics,
            Collection<ClaimWorkloadStatistics> detailStatistics,
            List<String> recipients);

    /**
     * 发送员工的工作量统计给管理员
     * @param startDate 统计开始日期
     * @param endDate
     * @param summaryStatistics 员工工作量汇总
     * @param detailStatistics 员工工作量详情
     * @param recipients 管理员的收件地址
     */
    void sendWorkloadStatisticsToAdmin(
            LocalDate startDate,
            LocalDate endDate,
            Collection<ClaimWorkloadStatistics> summaryStatistics,
            Collection<ClaimWorkloadStatistics> detailStatistics,
            List<String> recipients);

    /**
     * 发送Processor的工作量统计给指定的人
     * @param startDate 统计开始时间
     * @param endDate
     * @param recipients 发收人
     * @param timeZone 纳入统计员工的所属时区
     */
    void sendProcessStatistics(
        Collection<ProcessorUWStatistics> uwStatistics,
        Collection<ProcessorClaimStatistics> claimStatistics,
                               LocalDate startDate,
                               LocalDate endDate,
                               List<String> recipients,
                               String timeZone);
    /**
     * 发送Processor的工作量统计给指定的人
     * @param uwStatistics underwriting的统计数据
     * @param claimStatistics claim的统计数据
     * @param startDate 统计开始时间
     * @param endDate 统计开始时间
     * @param recipients 发件人
     */
    void sendReviewerStatistics(
        Collection<UWWorkloadStatistics> uwStatistics,
        Collection<ClaimWorkloadStatistics> claimStatistics,
        LocalDate startDate,
        LocalDate endDate,
        List<String> recipients);
}
