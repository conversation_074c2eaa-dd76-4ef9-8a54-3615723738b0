package com.bees360.internal.ai.service.impl;

import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.commons.lang.time.TimeUtil;
import com.bees360.internal.ai.entity.dto.ProcessorClaimStatistics;
import com.bees360.internal.ai.entity.dto.ProcessorUWStatistics;
import com.bees360.internal.ai.entity.dto.ClaimWorkloadStatistics;
import com.bees360.internal.ai.entity.dto.UWWorkloadStatistics;
import com.bees360.internal.ai.service.AiNotificationService;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.mail.MailMessage;
import com.bees360.mail.MailSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import static java.time.temporal.TemporalAdjusters.firstDayOfMonth;
import static java.time.temporal.TemporalAdjusters.lastDayOfMonth;

@Service
@Slf4j
public class AiNotificationServiceImpl implements AiNotificationService {

    private static final String ADJUSTER_DAILY_WORKLOAD_STATISTICS_EMAIL_TITLE =
            "Claims Department Daily Workload Statistics: %s to %s";
    private static final String ADJUSTER_WEEKLY_WORKLOAD_STATISTICS_EMAIL_TITLE =
            "Claims Department Weekly Workload Statistics: %s to %s";
    private static final String ADJUSTER_WORKLOAD_STATISTICS_EMAIL_TITLE =
            "Claims Department Workload Statistics: %s to %s";
    private static final String CHINESE_PROCESSOR_WORKLOAD_STATISTICS_SUBJECT =
            "Chinese Project Department Workload statistics: %s to %s";
    private static final String AMERICAN_PROCESSOR_WORKLOAD_STATISTICS_SUBJECT =
            "American Project Department Workload statistics: %s to %s";
    private static final String REVIEWER_WEEKLY_WORKLOAD_STATISTICS_SUBJECT =
            "Reviewer Team Weekly Workload Statistics: %s to %s";
    private static final String REVIEWER_DAILY_WORKLOAD_STATISTICS_SUBJECT =
            "Reviewer Team Daily Workload Statistics: %s to %s";
    private static final String REVIEWER_MONTHLY_WORKLOAD_STATISTICS_SUBJECT =
            "Reviewer Team Monthly Workload Statistics: %s to %s";
    private static final String REVIEWER_WORKLOAD_STATISTICS_SUBJECT =
            "Reviewer Team Workload Statistics: %s to %s";
    private static final String PROCESSOR_WORKLOAD_STATISTICS_TEMPLATE =
            "workloadStatisticsForProcessor";
    private static final String PROCESSOR_UW_WORKLOAD_STATISTICS_TEMPLATE =
        "workloadStatisticsForProcessorUW";
    private static final String REVIEWER_WORKLOAD_STATISTICS_TEMPLATE =
            "workloadWeeklyReviewerStatistics";
    private static final String REVIEWER_UW_WORKLOAD_STATISTICS_TEMPLATE =
            "workloadWeeklyReviewerUWStatistics";
    private static final String WORKLOAD_DAILY_STATISTICS_FOR_STAFF_EMAIL_TEMPLATE =
            "workloadDailyStatisticsForStaff";
    private static final String WORKLOAD_DAILY_STATISTICS_FOR_ADMIN_EMAIL_TEMPLATE =
            "workloadDailyStatisticsForAdmin";

    @Value("${mail.send.enabled:true}")
    private boolean sendingEnabled;

    @Autowired private MailSender mailSender;

    @Autowired private TemplateEngine templateEngine;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    private void doSendEmail(
            Collection<String> recipients, String subject, String content, boolean isHtml) {
        try {
            if (sendingEnabled) {
                mailSender.send(MailMessage.of(recipients, subject, content, null));
            }
        } catch (RuntimeException e) {
            throw new IllegalStateException("Fail to send email: " + e.getMessage(), e);
        }
    }

    @Override
    public void sendWorkloadStatisticsToAdmin(
            LocalDate startDate,
            LocalDate endDate,
            Collection<ClaimWorkloadStatistics> summaryStatistics,
            Collection<ClaimWorkloadStatistics> detailStatistics,
            List<String> recipients) {
        doSendWorkloadStatistics(
                getAdjusterTitle(startDate, endDate),
                summaryStatistics,
                detailStatistics,
                recipients,
                WORKLOAD_DAILY_STATISTICS_FOR_ADMIN_EMAIL_TEMPLATE);
    }

    @Override
    public void sendWorkloadStatisticsToStaff(
            LocalDate startDate,
            LocalDate endDate,
            Collection<ClaimWorkloadStatistics> summaryStatistics,
            Collection<ClaimWorkloadStatistics> detailStatistics,
            List<String> recipients) {
        doSendWorkloadStatistics(
                getAdjusterTitle(startDate, endDate),
                summaryStatistics,
                detailStatistics,
                recipients,
                WORKLOAD_DAILY_STATISTICS_FOR_STAFF_EMAIL_TEMPLATE);
    }

    @Override
    public void sendProcessStatistics(
            Collection<ProcessorUWStatistics> uwStatistics,
            Collection<ProcessorClaimStatistics> claimStatistics,
            LocalDate startDate,
            LocalDate endDate,
            List<String> recipients,
            String timeZone) {
        String title = CHINESE_PROCESSOR_WORKLOAD_STATISTICS_SUBJECT;
        if (timeZone.equals(AmericaTimeZone.US_CENTRAL)) {
            title = AMERICAN_PROCESSOR_WORKLOAD_STATISTICS_SUBJECT;
        }

        var subject = title.formatted(
            startDate.format(DateTimeFormatter.ofPattern(TimeUtil.DATE_DEFAULT_FORMAT)),
            endDate.format(DateTimeFormatter.ofPattern(TimeUtil.DATE_DEFAULT_FORMAT)));

        Map<String, Object> model = new HashMap<>();
        model.put("uwStatistics", uwStatistics);
        String template;
        if (bees360FeatureSwitch.isEnableCloseClaimEmail()) {
            template = PROCESSOR_UW_WORKLOAD_STATISTICS_TEMPLATE;
        } else {
            template = PROCESSOR_WORKLOAD_STATISTICS_TEMPLATE;
            model.put("claimStatistics", claimStatistics);
        }
        doSendEmail(recipients, subject, createContent(template, model), true);
    }

    @Override
    public void sendReviewerStatistics(
            Collection<UWWorkloadStatistics> uwStatistics,
            Collection<ClaimWorkloadStatistics> claimStatistics,
            LocalDate startDate,
            LocalDate endDate,
            List<String> recipients) {
        var subject = getReviewerTitle(startDate, endDate);
        Map<String, Object> model = new HashMap<>();
        model.put("uwStatistics", uwStatistics);
        String template;
        if (bees360FeatureSwitch.isEnableCloseClaimEmail()) {
            template = REVIEWER_UW_WORKLOAD_STATISTICS_TEMPLATE;
        } else {
            template = REVIEWER_WORKLOAD_STATISTICS_TEMPLATE;
            model.put("claimStatistics", claimStatistics);
        }
        doSendEmail(recipients, subject, createContent(template, model), true);
    }

    private String getReviewerTitle(LocalDate startDate, LocalDate endDate) {
        String template = REVIEWER_WORKLOAD_STATISTICS_SUBJECT;
        if (startDate.plusDays(1).isAfter(endDate)) {
            template = REVIEWER_DAILY_WORKLOAD_STATISTICS_SUBJECT;
        } else if (startDate.plusDays(7).isAfter(endDate)) {
            template = REVIEWER_WEEKLY_WORKLOAD_STATISTICS_SUBJECT;
        } else if (isMonthlyDateScope(startDate, endDate)) {
            template = REVIEWER_MONTHLY_WORKLOAD_STATISTICS_SUBJECT;
        }
        return template.formatted(
            startDate.format(DateTimeFormatter.ofPattern(TimeUtil.DATE_DEFAULT_FORMAT)),
            endDate.format(DateTimeFormatter.ofPattern(TimeUtil.DATE_DEFAULT_FORMAT)));
    }

    private static boolean isMonthlyDateScope(LocalDate start, LocalDate end) {
        var firstDate = start.with(firstDayOfMonth());
        var lastDate = end.with(lastDayOfMonth());
        var gap = end.toEpochDay() - start.toEpochDay();
        return start.isBefore(end) && firstDate.equals(start) && lastDate.equals(end) && gap <= 30;
    }

    private String getAdjusterTitle(LocalDate startDate, LocalDate endDate) {
        String template = ADJUSTER_WORKLOAD_STATISTICS_EMAIL_TITLE;
        if (startDate.plusDays(1).isAfter(endDate)) {
            template = ADJUSTER_DAILY_WORKLOAD_STATISTICS_EMAIL_TITLE;
        } else if (startDate.plusDays(7).isAfter(endDate)) {
            template = ADJUSTER_WEEKLY_WORKLOAD_STATISTICS_EMAIL_TITLE;
        }
        return template.formatted(
            startDate.format(DateTimeFormatter.ofPattern(TimeUtil.DATE_DEFAULT_FORMAT)),
            endDate.format(DateTimeFormatter.ofPattern(TimeUtil.DATE_DEFAULT_FORMAT)));
    }

    private void doSendWorkloadStatistics(
            String subject,
            Collection<ClaimWorkloadStatistics> summaryStatistics,
            Collection<ClaimWorkloadStatistics> detailStatistics,
            List<String> recipients,
            String emailTemplate) {
        Map<String, Object> model = new HashMap<>();
        model.put("statistics", summaryStatistics);
        model.put("detailStatistics", detailStatistics);
        doSendEmail(recipients, subject, createContent(emailTemplate, model), true);
    }

    private String createContent(String template, Map<String, Object> templateModel) {
        Context ctx = new Context(Locale.getDefault(), templateModel);
        return templateEngine.process(template + ".html", ctx);
    }
}
