package com.bees360.internal.ai.service.listener;

import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.service.ProjectEsService;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 监听项目状态变更事件并更新Elasticsearch中的项目状态信息
 */@Log4j2
@Component
@ConditionalOnProperty(prefix = "bees360.feature-switch", name = "enableEsOpenClose", havingValue = "true")
public class UpdateEsProjectStateOnProjectStateChanged extends AbstractNamedEventListener<ProjectStateChangedEvent> {
    private final ProjectEsService projectEsService;

    public UpdateEsProjectStateOnProjectStateChanged(@NonNull ProjectEsService projectEsService) {
        this.projectEsService = projectEsService;

        log.info("Created '{}(projectEsService={})",this, this.projectEsService);
    }

    @Override
    public void handle(ProjectStateChangedEvent event) throws IOException {
        var projectId = event.getProjectId();
        var state = event.getCurrentState();
        var stateName = state.getState().name().toLowerCase();
        var stateChangeReason = state.getStateChangeReason().getDisplayText();
        var stateChangeReasonId = state.getStateChangeReason().getId();
        var projectEsModel = ProjectEsModelUpdater.toBuilder()
                .setProjectId(projectId)
                .setProjectState(stateName)
                .setProjectStateChangeReason(stateChangeReason)
                .setProjectStateChangeReasonId(stateChangeReasonId)
                .build();

        projectEsService.updatePartial(projectEsModel);
    }
}
