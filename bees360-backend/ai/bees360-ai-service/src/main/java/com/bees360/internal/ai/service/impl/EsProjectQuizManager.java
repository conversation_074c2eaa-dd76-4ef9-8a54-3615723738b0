package com.bees360.internal.ai.service.impl;

import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.project.quiz.ProjectQuiz;
import com.bees360.project.quiz.ProjectQuizManager;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Log4j2
public class EsProjectQuizManager implements ProjectQuizManager {

	private final ProjectEsService projectEsService;

	public EsProjectQuizManager(ProjectEsService projectEsService) {
		this.projectEsService = projectEsService;
		log.info("Created {}.", this);
	}

	@Override
	public Iterable<? extends ProjectQuiz> findByProjectId(String projectId) {
		var esModel = projectEsService.findProjectByProjectId(Long.parseLong(projectId));
		if (Objects.isNull(esModel) || CollectionUtils.isEmpty(esModel.getProjectQuiz())) {
			return List.of();
		}
		return esModel.getProjectQuiz().stream()
				.map(quiz -> ProjectQuiz.of(
						String.valueOf(quiz.getQuizId()),
						String.valueOf(quiz.getProjectId()),
						quiz.getSubject(),
						quiz.getType(),
						Arrays.asList(quiz.getChoices()),
						quiz.getSequence(),
						Arrays.asList(quiz.getAnswers()), quiz.getAnswer()))
				.collect(Collectors.toList());
	}
}
