package com.bees360.internal.ai.service.job;

import com.bees360.codec.GsonCodec;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.entity.enums.ProjectSyncPointEnum;
import com.bees360.internal.ai.entity.dto.ImageRoomDto;
import com.bees360.job.util.AbstractRetryableJob;
import com.google.protobuf.ByteString;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.NonNull;

public class IncrementalAdditionImageJob extends AbstractRetryableJob {

    private static final GsonCodec codec = new GsonCodec();
    private final String id;
    @Getter private final long projectId;
    @Getter private final ProjectSyncPointEnum syncPoint;
    @Getter private final int serviceType;
    @Getter private final List<ProjectImage> images;
    @Getter private Map<String, ImageRoomDto> imageRooms;
    @Getter private final int retryCount;
    @Getter private final Duration retryDelay;

    public IncrementalAdditionImageJob(
            long projectId,
            ProjectSyncPointEnum syncPoint,
            int serviceType,
            List<ProjectImage> images,
            Map<String, ImageRoomDto> imageRooms) {
        this(projectId, syncPoint, serviceType, images, 5, Duration.ofSeconds(2L));
        this.imageRooms = imageRooms;
    }

    public IncrementalAdditionImageJob(
            long projectId,
            ProjectSyncPointEnum syncPoint,
            int serviceType,
            List<ProjectImage> images,
            int retryCount,
            Duration retryDelay) {
        this.id =
                IncrementalAdditionImageJobExecutor.EXECUTOR_NAME
                        + "-"
                        + projectId
                        + "-"
                        + syncPoint.getCode()
                        + "-"
                        + System.currentTimeMillis();
        this.projectId = projectId;
        this.syncPoint = syncPoint;
        this.serviceType = serviceType;
        this.images = images;
        this.retryCount = retryCount;
        this.retryDelay = retryDelay;
    }

    public static IncrementalAdditionImageJob decode(ByteString byteString) {
        return codec.decode(byteString, IncrementalAdditionImageJob.class);
    }

    public ByteString encode() {
        return codec.encode(this);
    }

    @Nonnull
    @Override
    public String getId() {
        return id;
    }

    @Nonnull
    @Override
    public String getName() {
        return IncrementalAdditionImageJobExecutor.EXECUTOR_NAME;
    }

    @Override
    public @NonNull ByteString getPayload() {
        return encode();
    }

    @Override
    public float getRetryDelayIncreaseFactor() {
        return 1;
    }
}
