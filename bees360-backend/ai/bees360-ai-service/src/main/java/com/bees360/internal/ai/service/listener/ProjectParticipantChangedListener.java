package com.bees360.internal.ai.service.listener;

import com.bees360.event.registry.ProjectParticipantChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.project.participant.ProjectParticipantProvider;
import com.bees360.util.Iterables;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 监听项目参与者变更事件并更新Elasticsearch中的项目参与者数据
 */
@Log4j2
public class ProjectParticipantChangedListener
        extends AbstractNamedEventListener<ProjectParticipantChanged> {

    private final ProjectEsService projectEsService;

    private final ProjectParticipantProvider projectParticipantProvider;

    private final Bees360FeatureSwitch bees360FeatureSwitch;

    private static final String ES_PARTICIPANTS = "participants";

    public ProjectParticipantChangedListener(
            ProjectEsService projectEsService,
            ProjectParticipantProvider projectParticipantProvider,
            Bees360FeatureSwitch bees360FeatureSwitch) {
        this.projectEsService = projectEsService;
        this.projectParticipantProvider = projectParticipantProvider;
        this.bees360FeatureSwitch = bees360FeatureSwitch;
    }

    @Override
    public void handle(ProjectParticipantChanged event) throws IOException {
        var projectId = event.getProjectId();
        log.info("Start to update participant in es :{}", event);

        ProjectEsModel projectEsModel =
                projectEsService.findProjectByProjectId(Long.parseLong(projectId));
        if (projectEsModel == null) {
            return;
        }
        // update all participants to avoid data race
        var participants =
                Iterables.toStream(projectParticipantProvider.getParticipant(projectId))
                        .map(ProjectEsModel.Participant::from)
                        .collect(Collectors.toList());
        Map<String, Object> updateMap = Map.of(ES_PARTICIPANTS, participants);
        log.info("Update es with :{} in project :{}", updateMap, projectId);
        if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
            var participantsUpdater =
                    Iterables.toStream(projectParticipantProvider.getParticipant(projectId))
                            .map(ProjectEsModelUpdater.Participant::from)
                            .collect(Collectors.toList());
            var projectEsModelUpdater =
                    ProjectEsModelUpdater.toBuilder()
                            .setProjectId(Long.parseLong(projectId))
                            .setParticipants(participantsUpdater)
                            .build();
            projectEsService.updatePartial(projectEsModelUpdater);
        } else {
            projectEsService.syncPartiallyToEsFromProjectEsModel(updateMap, projectId, true);
        }
    }
}
