package com.bees360.internal.ai.service.listener;

import com.bees360.atomic.RedisLockProvider;
import com.bees360.event.registry.ProjectTagAdded;
import com.bees360.event.registry.ProjectTagChangedObject;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.entity.ProjectTagEs;
import com.bees360.internal.ai.entity.consts.RedisLockKey;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 监听项目标签添加事件，并将新增标签同步到Elasticsearch中的项目文档。
 */
@Log4j2
@Component
public class ProjectTagAddedEventListener extends AbstractNamedEventListener<ProjectTagAdded> {

    private final ProjectEsService projectEsService;

    private final RedisLockProvider lockProvider;
    private final Bees360FeatureSwitch bees360FeatureSwitch;

    public ProjectTagAddedEventListener(
        ProjectEsService projectEsService, RedisLockProvider projectLockProvider, Bees360FeatureSwitch bees360FeatureSwitch) {
        this.projectEsService = projectEsService;
        this.lockProvider = projectLockProvider;
        this.bees360FeatureSwitch = bees360FeatureSwitch;
    }

    @Override
    public void handle(ProjectTagAdded event) throws IOException {
        var projectTags = event.getList();
        if (CollectionUtils.isEmpty(projectTags)) {
            return;
        }
        var ai =
                projectTags.stream()
                        .filter(e -> Objects.equals(e.getUpdatedVia(), "AI"))
                        .collect(Collectors.toList());
        syncToEs(ai);
    }

    @Nullable
    @Override
    public String getName() {
        return "project_tag_added";
    }

    public void syncToEs(List<ProjectTagChangedObject> projectTags) {
        log.info("Project tag added {}", projectTags);
        if (CollectionUtils.isEmpty(projectTags)) {
            return;
        }
        var projectTag = projectTags.get(0);
        var projectTagList =
                projectTags.stream().map(ProjectTagEs::eventToES).collect(Collectors.toList());

        var lock = lockProvider.lock(RedisLockKey.PROJECT_LOCK_KEY + projectTag.getProjectId());

        try {
            var projectEsModel =
                    projectEsService.findProjectByProjectId(
                            Long.parseLong(projectTag.getProjectId()));
            if (projectEsModel == null) {
                log.warn("Project tag added can not find project id {}", projectTag.getProjectId());
                // 不存在project时直接通过syncToEsFromProjectEsModel插入一条数据。
                projectEsModel = new ProjectEsModel();
                projectEsModel.setProjectId(Long.parseLong(projectTag.getProjectId()));
                projectEsModel.setProjectTagList(projectTagList);
                projectEsModel.setTimeLines(new ArrayList<>());
                projectEsService.syncToEsFromProjectEsModel(projectEsModel, true);
                return;
            }

            List<ProjectTagEs> exist =
                    projectEsModel.getProjectTagList() == null
                            ? new ArrayList<>()
                            : projectEsModel.getProjectTagList();
            CollectionUtils.addAll(projectTagList, exist);
            projectEsModel.setProjectTagList(projectTagList);
            if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
                var projectEsModelUpdater =
                        ProjectEsModelUpdater.toBuilder()
                                .setProjectId(Long.parseLong(projectTag.getProjectId()))
                                .setProjectTagList(projectTagList)
                                .build();
                projectEsService.updatePartial(projectEsModelUpdater);
            } else {
                projectEsService.syncToEsFromProjectEsModel(projectEsModel, true);
            }
        } finally {
            lock.unlock();
        }
    }
}
