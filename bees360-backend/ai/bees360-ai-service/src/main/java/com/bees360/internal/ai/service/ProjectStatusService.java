package com.bees360.internal.ai.service;

import com.bees360.internal.ai.entity.ProjectStatusVo;
import com.bees360.internal.ai.entity.dto.BatchUpdateResultDto;
import com.bees360.internal.ai.entity.dto.ProjectsStatusModifierDto;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.user.User;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/05/09 17:27
 */
public interface ProjectStatusService {

    /**
     *  更改project Status，发布状态变更事件
     */
    void updateStatusByAiUser(long projectId, int code, User user);

    void updateStatus(long projectId, User user, int newStatusCode, Long statusCreateTime);

    void updateStatusByAiUser(long projectId, int code, String userId);

    void updateStatusByAiUser(long projectId, int code, User user, List<ProjectStatusVo> timeLines);

    void reCoverProjectStatus(User user, long projectId, int code);

    ProjectStatusVo buildProjectStatusVo(Long currentTime, String userName, AiProjectStatusEnum projectStatusEnum);

    /**
     * 构建timeLine，每种状态只显示一次最新的时间
     */
    List<ProjectStatusVo> buildTimeLines(List<ProjectStatusVo> timeLines);

    BatchUpdateResultDto batchUpdateStatus(ProjectsStatusModifierDto projectsStatusModifier, User user);

    void reworkProject(long projectId, String userId, String title, String content);
}
