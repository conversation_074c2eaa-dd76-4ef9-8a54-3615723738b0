package com.bees360.internal.ai.service;

import com.bees360.internal.ai.entity.MemberInfo;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.entity.ProjectStatusTagNum;
import com.bees360.internal.ai.entity.dto.PageResult;
import com.bees360.internal.ai.entity.enums.LogProjectDataActionEnum;
import com.bees360.internal.ai.entity.vo.ProjectCalQueryVo;
import com.bees360.internal.ai.entity.vo.RoleQueryResult;
import com.bees360.internal.ai.grpc.ProjectServiceOuterClass.UpdateProjectBatchItem;
import com.bees360.internal.ai.service.entity.query.ProjectEsQueryParam;
import com.bees360.user.User;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/05/06 12:17
 */
public interface ProjectEsService {


    PageResult<ProjectEsModel> getProjectPageResult(ProjectEsQueryParam paramPojo, User user);
    /**
     *
     * @param projectEsModel
     * @param sync  true 实时，false 异步
     * @throws IOException
     */
    Boolean syncToEsFromProjectEsModel(ProjectEsModel projectEsModel, boolean sync);

    /**
     *  更新EsModel里的部分字段
     * @param projectEsModel EsModel for update
     */
    void updatePartial(ProjectEsModelUpdater projectEsModel);

    Boolean updatePartialReturningIfChanged(ProjectEsModelUpdater projectEsModel);

    List<ProjectEsModel> findProjectListByQueryBuild(ProjectEsQueryParam param);

    ProjectEsModel findProjectByProjectId(long projectId);

    ProjectEsModel findProjectByProjectIdCheckExisted(long projectId);

    ProjectEsModel getProjectInfo(long projectId, User user);

    void softDeleteProjectDataByProjectId(long projectId);

    Boolean changeDashBoardProjectMembers(User user, long projectId, MemberInfo param, boolean isDeleted) throws IOException;

    List<MemberInfo> getDashBoardProjectMembers(long projectId) throws IOException;

    void updateProjectStatusOnReportChange(long projectId, String userId, int newStatusCode, int reportTypeCode, Long updatedAt);

    ProjectStatusTagNum getProjectStatusTagNum(ProjectEsQueryParam queryParam, User user);

    void updateProjectCountOnImageChange(long projectId, LogProjectDataActionEnum actionEnum, User user);

    /**
     * project是否订阅了hover
     * @param projectId
     */
    boolean isSubscribeToHover(long projectId);

    void updateReportStatus(User user, long projectId, Integer statusCode, String type);

    void updateProjectTags(User user, long projectId, List<Long> tagIds);

    List<ProjectCalQueryVo> queryCalExportResult(ProjectEsQueryParam param);

    List<RoleQueryResult> queryRoleCountProject(Long startDate, Long endDate, String role, Boolean isClaim);

    void updateInspectionTime(long projectId, Long inspectionTime);

    void updateImageScore(long project, User user, String pilotId, float score);

    void updatePartial(UpdateProjectBatchItem updateProjectBatchItem, String opUserId);

    /**
     * 该方法仅对es中的数据进行更新操作，更新内容为updateMap，若map中有es中不存在的key，不会对更新有影响（不会新增，也不会影响其他key的更新）。
     *
     * @param updateMap 待更新内容，该方法将依据此参数中的内容进行部分更新。
     * @param sync true 实时，false 异步
     * @return 是否更新成功
     */
    @Deprecated
    Boolean syncPartiallyToEsFromProjectEsModel(
            Map<String, Object> updateMap, String projectId, boolean sync);
}
