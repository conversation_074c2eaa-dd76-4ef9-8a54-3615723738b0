package com.bees360.internal.ai.service.impl;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.ai.mapper.ProjectImageMapper;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.event.EventPublisher;
import com.bees360.event.registry.AIImageDeleteEvent;
import com.bees360.image.GroupImageSource;
import com.bees360.image.Image;
import com.bees360.image.ImageGroupManager;
import com.bees360.image.ImageSource;
import com.bees360.image.tag.ImageTag;
import com.bees360.image.tag.ImageTagDictProvider;
import com.bees360.image.tag.ImageTagGroupType;
import com.bees360.image.Message.ImageMessage;
import com.bees360.internal.ai.common.msgcode.MsgCodeManager;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.common.exceptions.ServiceMessageException;
import com.bees360.internal.ai.entity.dto.IdValueDto;
import com.bees360.internal.ai.entity.dto.ImageIdAndCompassDto;
import com.bees360.internal.ai.entity.dto.ImageIn3DModelModifierItemDto;
import com.bees360.internal.ai.entity.dto.ImageIn3DModelModifierListDto;
import com.bees360.internal.ai.entity.dto.ImageQueryDto;
import com.bees360.internal.ai.entity.dto.ProjectImageFilter;
import com.bees360.internal.ai.entity.enums.AiBotUserEnum;
import com.bees360.internal.ai.entity.enums.FileSourceTypeEnum;
import com.bees360.internal.ai.exchange.ai2client.ProjectImageProto;
import com.bees360.internal.ai.service.ProjectImageManager;
import com.bees360.internal.ai.service.ProjectImageService;
import com.bees360.project.ProjectIIManager;
import com.bees360.user.User;
import com.bees360.util.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/08/24 10:40
 */
@Service
@Slf4j
public class ProjectImageServiceImpl implements ProjectImageService {

    @Autowired
    private ProjectImageManager projectImageManager;

    @Autowired
    private ProjectImageMapper projectImageMapper;

    @Autowired private ActivityManager activityManager;

    @Autowired private EventPublisher eventPublisher;

    @Autowired
    private ProjectIIManager projectIIManager;

    @Autowired
    private ImageTagDictProvider imageTagDictProvider;

    @Autowired
    private ImageGroupManager imageGroupManager;

    private static final Integer INVALID_TAG_NUMBER = 0;

    private static final String OUTBUILDINGS_GROUP = "Outbuildings";

    private static final String INTERIOR_ROOMS = "Interior Rooms";

    private static final String EMPTY_TAG_CATEGORY = "default";

    private static final String GROUP_PROJECT = "GROUP_PROJECT";

    private static final String GROUP_IMAGE = "GROUP_IMAGE";


    @Override
    public List<ProjectImage> listImages(long projectId, ImageQueryDto imageQuery) throws ServiceException {
        Iterable<ProjectImage> imageIterable = projectImageManager.findReallyAllByProjectId(projectId);
        List<ProjectImage> images = new ArrayList<>();
        for (ProjectImage image : imageIterable) {
            images.add(image);
        }
        FileSourceTypeEnum fileSourceType = FileSourceTypeEnum.getEnum(imageQuery.getFileSourceType());
        if (fileSourceType == FileSourceTypeEnum.REPORT_IMAGE){
            images.sort(Comparator.comparingLong(ProjectImage::getUploadTime));
        }

        return filterImages(images, imageQuery);
    }

    @Override
    public List<ProjectImage> listImagesWithoutDeleted(long projectId) throws ServiceException {
        Iterable<ProjectImage> imageIterable = projectImageManager.findReallyAllByProjectId(projectId);
        List<ProjectImage> images = new ArrayList<>();
        for (ProjectImage image : imageIterable) {
            images.add(image);
        }
        return images.stream().filter(o -> !o.isDeleted() &&
            Arrays.asList(FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode(), FileSourceTypeEnum.DRONE_IMAGE.getCode())
                .contains(o.getFileSourceType())).collect(Collectors.toList());
    }

    private List<ProjectImage> filterImages(List<ProjectImage> images, ImageQueryDto imageQuery) {

        return images.stream()
                .filter(
                        img ->
                                imageQuery.ignoreFileSourceType()
                                        || Objects.equals(
                                                img.getFileSourceType(),
                                                imageQuery.getFileSourceType()))
                .filter(img -> Objects.equals(img.isDeleted(), imageQuery.isDeleted()))
                .collect(Collectors.toList());
    }

    @Override
    public void updateImageSort(long projectId, List<IdValueDto<String, Long>> sorts, String userId) {
        projectImageManager.updateImageSort(projectId, sorts);
        if (CollectionAssistant.isEmpty(sorts)) {
            return;
        }
        List<IdValueDto<String, Long>> sorted = sorts.stream().sorted(Comparator.comparing(IdValueDto::getValue)).collect(Collectors.toList());
        var maxImageSort = sorted.get(sorted.size() - 1).getValue();
        List<ProjectImage> allProjectImages = Iterables.toList(projectImageMapper.listImages(projectId, null, null, false));
        //getAllByProjectId取出来的是无序的，需要根据imagesort字段重新进行排序后再操作
        List<ProjectImage> allProjectImagesSorted = allProjectImages.stream()
            .filter(projectImage -> projectImage.getFileSourceType() != FileSourceTypeEnum.REPORT_IMAGE.getCode())
            .peek(projectImage -> {
                if (projectImage.getImageSort() == null) {
                    projectImage.setImageSort(ProjectImage.DEFAULT_SORT);
                }
            })
            .sorted(Comparator.comparing(ProjectImage::getImageSort)).collect(Collectors.toList());
        if (allProjectImagesSorted.isEmpty()) {
            return;
        }
        //算法描述：根据sorted中的第一张和最后一张图片，找到他们之间有序的imageId放入imagesNeedToSet，找到firstImageId的前置图片，调用setposition进行postgres数据库中image位置的设置
        List<String> imagesNeedToSet = new ArrayList<>();

        String preImageId = null;
        int startIndex = 0;
        for (; startIndex < allProjectImagesSorted.size(); startIndex++) {
            if (StringUtils.equals(allProjectImagesSorted.get(startIndex).getImageId(), sorted.get(0).getId())) {
                if (startIndex == 0) {
                    break;
                } else {
                    preImageId = allProjectImagesSorted.get(startIndex - 1).getImageId();
                    break;
                }
            }
        }

        for (; startIndex < allProjectImagesSorted.size(); startIndex++) {
            if (allProjectImagesSorted.get(startIndex).getImageSort() > maxImageSort) {
                break;
            } else {
                imagesNeedToSet.add(allProjectImagesSorted.get(startIndex).getImageId());
            }
        }

        imageGroupManager.setImagePosition(String.valueOf(projectId), "GROUP_PROJECT", imagesNeedToSet, preImageId, userId);
    }

    @Override
    public void updateImageIn3DModel(long projectId, ImageIn3DModelModifierListDto imageIn3DModels) {
        if (CollectionAssistant.isEmpty(imageIn3DModels.getIn3DModels())) {
            return;
        }
        for (ImageIn3DModelModifierItemDto imageIn3DModel : imageIn3DModels.getIn3DModels()) {
            if (!CollectionAssistant.isEmpty(imageIn3DModel.getImageIds())) {
                projectImageManager
                    .updateImageIn3DModel(projectId, imageIn3DModel.getIn3DModel(), imageIn3DModel.getImageIds());
            }
        }
    }

    private List<ProjectImage> filterImages(List<ProjectImage> images, ProjectImageFilter imageFilter) {
        Set<String> imageIdsFilter = new HashSet<>(imageFilter.getImageIds());
        return images.stream().filter(img -> imageIdsFilter.contains(img.getImageId())).collect(Collectors.toList());
    }

    @Override
    public List<ProjectImage> listBoundaryImages(long projectId, ProjectImageFilter imageFilter) {
        ImageQueryDto imageQuery = ImageQueryDto.builder()
            .fileSourceType(FileSourceTypeEnum.DRONE_IMAGE.getCode())
            .deleted(false)
            .build();
        List<ProjectImage> images = listImages(projectId, imageQuery);
        return filterImages(images, imageFilter);
    }

    @Override
    @Transactional(rollbackFor = ServiceMessageException.class)
    public void deleteImages(long projectId, List<String> imageIds) {
        projectImageManager.deleteProjectImagesLogically(projectId, imageIds);
    }

    @Override
    @Transactional(rollbackFor = ServiceMessageException.class)
    public void deleteCompletely(long projectId, User user, List<String> imageIds) {
        projectImageManager.deleteCompletely(projectId, imageIds);

        if (!StringUtils.equals(user.getId(), AiBotUserEnum.WEB_NEW_USER_ID.getCode())) {
            // 非WEB端删除
            submitActivityOnCompletelyDeleteImages(projectId, user, imageIds.size());

            AIImageDeleteEvent event = new AIImageDeleteEvent();
            event.setProjectId(projectId);
            event.setImageIds(imageIds);
            event.setDeleteStatus(com.bees360.internal.ai.entity.ProjectImage.COMPLETELY_DELETED);
            eventPublisher.publish(event);
        }
    }

    private void submitActivityOnCompletelyDeleteImages(long projectId, User assigner, int count) {
        Message.ActivityMessage.Builder activityMessage = Message.ActivityMessage.newBuilder();
        activityMessage
                .setAction("completely deleted")
                .setCreatedBy(assigner.toMessage())
                .setProjectId(projectId)
                .setSource(ActivitySourceEnum.AI.getValue())
                .setEntity(
                        Message.ActivityMessage.Entity.newBuilder()
                                .setType(Message.ActivityMessage.EntityType.IMAGE.name())
                                .setCount(count)
                                .build());
        activityManager.submitActivity(Activity.of(activityMessage.build()));
    }

    @Override
    public void recoverImages(long projectId, List<String> imageIds) {
        if (CollectionUtils.isEmpty(imageIds)) {
            throw new ServiceMessageException(MsgCodeManager.REQUEST.PARAMETER_INVALID.CODE,
                MsgCodeManager.REQUEST.PARAMETER_INVALID.MSG);
        }
        projectImageManager.recoverProjectImage(projectId, imageIds);
    }

    @Override
    public void updateImagesCompass(
            long projectId, User user, List<ImageIdAndCompassDto> imageIdAndCompassDtos) {
        if (CollectionUtils.isEmpty(imageIdAndCompassDtos)) {
            return;
        }
        log.info(
                "{} sets the compass information of project{}. data:{}",
                user.getId(),
                projectId,
                ListUtil.toString(imageIdAndCompassDtos));
        projectImageMapper.updateImagesCompass(projectId, imageIdAndCompassDtos);
    }

    @Override
    public void updateNumberOfOutbuildingsAndInteriorRooms(long projectId) {
        var images = listImagesWithoutDeleted(projectId);

        var outbuildings = countNumberOfOutbuildings(images);
        projectIIManager.updateNumberOfOutBuildings(String.valueOf(projectId), outbuildings);

        var interiorRooms = countNumberOfInteriorRooms(images);
        projectIIManager.updateNumberOfInteriorRooms(String.valueOf(projectId), interiorRooms);

        log.info("Update project {} number of outbuildings and interior rooms successfully.", projectId);
    }

    @Override
    public List<ProjectImage> deriveImages(
            long projectId, User user, ProjectImageProto.DerivativeImageRequest derivativeImages) {
        var imageIds = Lists.transform(derivativeImages.getDerivativeImagesList(),
            ProjectImageProto.DerivativeImage::getOriginId);

        var images = projectImageManager.findAllByIds(imageIds);
        var imageMap = Maps.uniqueIndex(images, ProjectImage::getImageId);

        var derivativeImageEntities = new ArrayList<ProjectImage>();
        var groupImageSources = new ArrayList<GroupImageSource>();
        for (var derivativeImage : derivativeImages.getDerivativeImagesList()) {
            if (!imageMap.containsKey(derivativeImage.getOriginId())) {
                throw new IllegalArgumentException("The origin image not be exists.");
            }
            var imageEntity = imageMap.get(derivativeImage.getOriginId());
            imageEntity.setProjectId(projectId);
            imageEntity.setParentId(derivativeImage.getOriginId());
            imageEntity.setFileName(derivativeImage.getResourceKey());
            imageEntity.setFileSize(derivativeImage.getSize());
            imageEntity.setImageWidth(derivativeImage.getWidth());
            imageEntity.setImageHeight(derivativeImage.getHeight());
            derivativeImageEntities.add(imageEntity);

            ImageSource imageSource;
            imageSource =
                    ImageSource.of(
                            derivativeImage.getResourceKey(),
                            StringUtils.EMPTY,
                            EMPTY_TAG_CATEGORY,
                            getShootingTimeInstant(imageEntity.getShootingTime()));
            var groupProject =
                    GroupImageSource.Group.builder()
                            .groupType(GROUP_PROJECT)
                            .groupKey(String.valueOf(projectId))
                            .build();
            var groupImage =
                    GroupImageSource.Group.builder()
                            .groupType(GROUP_IMAGE)
                            .groupKey(derivativeImage.getOriginId())
                            .build();
            groupImageSources.add(
                    GroupImageSource.of(imageSource, List.of(groupProject, groupImage)));
        }

        var imageCreated = imageGroupManager.createGroupImage(groupImageSources, user.getId());
        var imageIdList = Lists.transform(Iterables.toList(imageCreated), Image::getId);

        for (int i = 0; i < imageIdList.size(); i++) {
            var imageId = imageIdList.get(i);
            var image = derivativeImageEntities.get(i);
            image.setImageId(imageId);
            image.setFileNameMiddleResolution(
                    getImageResourceKey(imageId, ImageMessage.Resource.Type.MIDDLE));
            image.setFileNameLowerResolution(
                    getImageResourceKey(imageId, ImageMessage.Resource.Type.SMALL));
        }
        projectImageManager.saveNewImages(projectId, derivativeImageEntities);

        return derivativeImageEntities;
    }

    @Override
    public void copyProjectImage(long projectId, Map<String, String> copyImageMap) {
        projectImageMapper.copyProjectImage(projectId, copyImageMap);
    }

    private Instant getShootingTimeInstant(long shootingTime) {
        if (shootingTime > 0) {
            return Instant.ofEpochMilli(shootingTime);
        }
        return null;
    }

    public String getImageResourceKey(String imageId, ImageMessage.Resource.Type imageType) {
        return "image/" + imageId + "/" + imageType.getNumber();
    }

    private int countNumberOfOutbuildings(List<ProjectImage> images) {

        var buildingTags = Iterables
            .toStream(imageTagDictProvider.findByGroup(OUTBUILDINGS_GROUP, ImageTagGroupType.BEES_AI.getValue()))
            .map(ImageTag::getId)
            .collect(Collectors.toList());

        return (int) images.stream()
                            .filter(image -> image.getObjectTag() != null
                                                 && buildingTags.contains(String.valueOf(image.getObjectTag())))
                            .map(
                                image -> {
                                    var objectTag = image.getObjectTag();
                                    var numberTag = image.getNumberTag() == null ? INVALID_TAG_NUMBER : image.getNumberTag();
                                    return List.of(objectTag, numberTag);
                                }
                            )
                            .distinct()
                            .count();
    }

    private int countNumberOfInteriorRooms(List<ProjectImage> images) {
        var roomTags = Iterables
            .toStream(imageTagDictProvider.findByGroup(INTERIOR_ROOMS, ImageTagGroupType.BEES_AI.getValue()))
            .map(ImageTag::getId)
            .collect(Collectors.toList());

        return (int) images.stream()
                            .filter(image -> image.getLocationTag() != null
                                                 && roomTags.contains(String.valueOf(image.getLocationTag())))
                            .map(
                                image -> {
                                    var locationTag = image.getLocationTag();
                                    var numberTag = image.getNumberTag() == null ? INVALID_TAG_NUMBER : image.getNumberTag();
                                    return List.of(locationTag, numberTag);
                                }
                            )
                            .distinct()
                            .count();
    }

}
