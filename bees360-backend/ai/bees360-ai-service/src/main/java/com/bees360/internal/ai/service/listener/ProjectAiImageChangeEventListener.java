package com.bees360.internal.ai.service.listener;

import com.bees360.internal.ai.event.ProjectImageChangeEvent;
import com.bees360.internal.ai.service.ProjectEsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/07/24 17:15
 */
@Slf4j
@Component
public class ProjectAiImageChangeEventListener {


    @Autowired
    private ProjectEsService projectEsService;

    @EventListener
    public void updateProjectImageCountOnStatusChangeEvent(ProjectImageChangeEvent event) {
        log.debug("updateProjectImageCountOnStatusChangeEvent projectId:{}", event.getProjectId());
        projectEsService.updateProjectCountOnImageChange(event.getProjectId(), event.getActionEnum(), event.getUser());
    }


}
