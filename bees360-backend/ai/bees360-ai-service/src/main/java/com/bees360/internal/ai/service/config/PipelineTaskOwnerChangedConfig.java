package com.bees360.internal.ai.service.config;

import com.bees360.atomic.RedisLockProvider;
import com.bees360.event.EventListener;
import com.bees360.event.util.EventListeners;
import com.bees360.event.util.PipelineTaskChangedListeners;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.listener.PipelineTaskOwnerChangedListener;
import com.bees360.user.UserProvider;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Log4j2
@Configuration
public class PipelineTaskOwnerChangedConfig {

    @Data
    @ConfigurationProperties("pipeline")
    @EnableConfigurationProperties
    @Configuration
    public static class PipelineTaskUserAuthProperties {
        private final List<Property> taskMemberRole;

        @Data
        public static class Property {
            private String task;
            private String role;
        }
    }

    @Bean
    EventListener pipelineTaskOwnerChangedListener(
            @Autowired ProjectEsService projectEsService,
            @Autowired UserProvider userProvider,
            @Autowired PipelineTaskUserAuthProperties properties,
            @Autowired RedissonClient redissonClient,
            @Autowired Supplier<String> systemUserSupplier,
            @Autowired Bees360FeatureSwitch bees360FeatureSwitch) {

        Map<String, String> task2MemberRoleMap =
                properties.getTaskMemberRole().stream()
                        .collect(
                                Collectors.toMap(
                                        PipelineTaskUserAuthProperties.Property::getTask,
                                        PipelineTaskUserAuthProperties.Property::getRole));
        final String OWNER_CHANGED_LOCK = "owner_changed_lock";
        var projectLockProvider =
                new RedisLockProvider(redissonClient, OWNER_CHANGED_LOCK, Duration.ofMinutes(2));
        var listener =
                new PipelineTaskOwnerChangedListener(
                        projectEsService,
                        userProvider,
                        task2MemberRoleMap,
                        projectLockProvider,
                        systemUserSupplier,
                        bees360FeatureSwitch);
        var taskOwnerChangedListener =
                PipelineTaskChangedListeners.forwardToOwnerChangedListener(
                        listener, EventListeners.getListenerName(listener.getClass()), null);
        log.info(
                "Created pipeline task owner changed listener :{} with projectEsService :{}, userProvider :{} and properties :{}.",
                taskOwnerChangedListener,
                projectEsService,
                userProvider,
                properties);
        return taskOwnerChangedListener;
    }
}
