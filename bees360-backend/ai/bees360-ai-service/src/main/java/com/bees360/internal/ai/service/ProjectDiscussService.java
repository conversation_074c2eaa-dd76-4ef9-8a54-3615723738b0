package com.bees360.internal.ai.service;

import com.bees360.internal.ai.entity.DiscussionParticipant;
import com.bees360.internal.ai.entity.MemberInfo;
import com.bees360.internal.ai.entity.ProjectDiscuss;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.exchange.ai2client.ProjectDiscussionProto;
import com.bees360.user.User;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2020/07/24 17:33
 */
public interface ProjectDiscussService {

    /**
     * 添加一条project留言板
     */
    String addProjectDiscussion(User user, long projectId, String creatorId, String note, List<DiscussionParticipant> discussMembers);

    ProjectDiscuss getById(long projectId);

    ProjectDiscuss getProjectDiscuss(User user, long projectId, ProjectEsModel projectEsModel);

    void markAsRead(long projectId, User user, ProjectDiscussionProto.DiscussMarkReadParam markReadParam);

    Boolean getDiscussIsReadFlag(DiscussionParticipant participant, List<MemberInfo> members);
}
