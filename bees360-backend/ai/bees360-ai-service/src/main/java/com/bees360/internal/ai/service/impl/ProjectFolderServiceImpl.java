package com.bees360.internal.ai.service.impl;

import com.bees360.commons.elasticsearchsupport.ElasticSearchHelper;
import com.bees360.commons.elasticsearchsupport.EsAccessException;
import com.bees360.commons.elasticsearchsupport.EsDocumentUtil;
import com.bees360.commons.elasticsearchsupport.util.ElasticSearchUtil;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectFolder;
import com.bees360.internal.ai.entity.ProjectStatusVo;
import com.bees360.internal.ai.entity.dto.Pagination;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.service.ProjectFolderService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.internal.ai.service.entity.EsRequestConverter;
import com.bees360.project.group.GrpcProjectGroupManager;
import com.bees360.user.User;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.NestedQueryBuilder;
import org.opensearch.index.query.QueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.index.query.TermsQueryBuilder;
import org.opensearch.search.builder.SearchSourceBuilder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 * @date 2021/04/21 11:57
 */
@Slf4j
public class ProjectFolderServiceImpl implements ProjectFolderService {

    private final ElasticSearchHelper elasticSearchHelper;

    private final Bees360FeatureSwitch bees360FeatureSwitch;

    private final GrpcProjectGroupManager grpcProjectGroupManager;

    public static final String PROJECT_FOLDER_GROUP_TYPE = "PROJECT_FOLDER";
    public static final String PROJECT_URGENT_GROUP_KEY = "URGENT";


    public ProjectFolderServiceImpl(ElasticSearchHelper elasticSearchHelper, Bees360FeatureSwitch bees360FeatureSwitch, GrpcProjectGroupManager grpcProjectGroupManager) {
        this.elasticSearchHelper = elasticSearchHelper;
        this.bees360FeatureSwitch = bees360FeatureSwitch;
        this.grpcProjectGroupManager = grpcProjectGroupManager;
        log.info("Created {}(elasticSearchHelper={}, grpcProjectGroupManager={}).",
                this,
                this.elasticSearchHelper,
                this.grpcProjectGroupManager);
    }

    @Override
    public void addProjectFolder(User user, long projectId, String type) {
        addProjectFolder(user.getId(), projectId, type);
    }

    @Override
    public void addProjectFolder(String userId, long projectId, String type) {
        log.info("Add project[userId: {}, projectId: {}] to folder: {}", userId, projectId, type);
        if (bees360FeatureSwitch.isEnableSyncAiEsDataToSolid()) {
            grpcProjectGroupManager.addProjectToGroup(
                    PROJECT_URGENT_GROUP_KEY,
                    PROJECT_FOLDER_GROUP_TYPE,
                    List.of(String.valueOf(projectId)),
                    userId
            );
        }
        insertOrUpdateByProjectFolder(new ProjectFolder(
            projectId, type, userId));
    }

    private void insertOrUpdateByProjectFolder(ProjectFolder folder) {
        try {
            elasticSearchHelper.update(folder);
        } catch (EsAccessException e) {
            log.warn("insertOrUpdateByProjectFolder error, log:{} msg:{}", new Gson().toJson(folder),
                e.getMessage(), e);
        }
    }

    /**
     * 根据type获取得到的共有文件存储的projectId, 即需去重
     * future? 以后需求如果加了用户私有的存储，添加getByTypeAndUser方法即可
     *
     * @param type
     * @return
     */
    @Override
    public List<Long> getByProjectIdsAndType(List<Long> projectIds, String type) {
        QueryBuilder queryBuilder = convert(ProjectFolder.builder().type(type).build(), projectIds);
        SearchRequest searchRequest = ElasticSearchUtil.searchRequest(queryBuilder, ProjectFolder.class,
            Pagination.countIndexFrom(Pagination.DEFAULT_PAGE_START, Pagination.DEFAULT_PAGE_SIZE),
            Pagination.DEFAULT_PAGE_SIZE, null);
        List<ProjectFolder> folders = elasticSearchHelper.searchListResult(searchRequest, ProjectFolder.class);
        return Optional.ofNullable(folders).map(o -> o.stream().map(ProjectFolder::getProjectId).distinct().collect(Collectors.toList()))
            .orElse(Collections.emptyList());
    }

    /**
     * 1. 同时存在rework和image uploaded状态，并且rework触发时间比image uploaded更晚 <br/>
     * 2. 只存在rework状态，不存在image uploaded状态
     */
    public List<Long> getReworkProjectIds() {
        NestedQueryBuilder nestedImageQuery =
                EsRequestConverter.getTimeLineStatusQuery(AiProjectStatusEnum.IMAGE_UPLOADED);
        NestedQueryBuilder nestedReworkQuery =
                EsRequestConverter.getTimeLineStatusQuery(AiProjectStatusEnum.PROJECT_REWORK);
        TermsQueryBuilder exclusionStatus = EsRequestConverter.getReworkExclusionStatus();

        List<Long> projectPartOne =
                getProjectEsModels(
                                QueryBuilders.boolQuery()
                                        .must(nestedReworkQuery)
                                        .must(nestedImageQuery)
                                        .mustNot(exclusionStatus))
                        .stream()
                        .filter(this::filterReworkFolder)
                        .map(ProjectEsModel::getProjectId)
                        .collect(Collectors.toList());

        List<Long> projectPartTwo =
                getProjectEsModels(
                                QueryBuilders.boolQuery()
                                        .must(nestedReworkQuery)
                                        .mustNot(nestedImageQuery)
                                        .mustNot(exclusionStatus))
                        .stream()
                        .map(ProjectEsModel::getProjectId)
                        .collect(Collectors.toList());

        List<Long> projectIdList = new ArrayList<>(projectPartOne);
        projectIdList.addAll(projectPartTwo);
        return projectIdList;
    }

    private List<ProjectEsModel> getProjectEsModels(QueryBuilder queryBuilder) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(queryBuilder);
        sourceBuilder.size(Pagination.DEFAULT_PAGE_SIZE);
        SearchRequest request = new SearchRequest();
        request.indices(EsDocumentUtil.getIndexName(ProjectEsModel.class));
        request.source(sourceBuilder);
        return elasticSearchHelper.searchListResult(request, ProjectEsModel.class);
    }

    /**
     * 同时存在rework和image uploaded状态，并且rework触发时间比image uploaded更晚
     */
    private boolean filterReworkFolder(ProjectEsModel e) {
        var timeLines = e.getTimeLines();
        Long rework = null;
        Long imageUpload = null;
        for (ProjectStatusVo timeLine : timeLines) {
            if (timeLine.getStatus() == null) {
                continue;
            }
            if (Objects.equals(
                    timeLine.getStatus().getCode(), AiProjectStatusEnum.PROJECT_REWORK.getCode())) {
                rework = timeLine.getCreatedTime();
            }
            if (Objects.equals(
                    timeLine.getStatus().getCode(), AiProjectStatusEnum.IMAGE_UPLOADED.getCode())) {
                imageUpload = timeLine.getCreatedTime();
            }
        }
        if (rework == null || imageUpload == null) {
            return false;
        }
        return rework > imageUpload;
    }


    @Override
    public void deleteById(User user, long projectId, String type) {
        var userId = ofNullable(user).map(User::getId).orElse("");
        log.info("Delete project[userId: {}, projectId: {}] to folder: {}", userId, projectId, type);
        if (bees360FeatureSwitch.isEnableSyncAiEsDataToSolid()) {
            grpcProjectGroupManager.deleteProjectInGroup(
                    PROJECT_URGENT_GROUP_KEY,
                    PROJECT_FOLDER_GROUP_TYPE,
                    List.of(String.valueOf(projectId)),
                    userId
            );
        }
        elasticSearchHelper.deleteById(ProjectFolder.getUniqueId(projectId, type), ProjectFolder.class);
    }

    private static QueryBuilder convert(ProjectFolder folder, List<Long> projectIds) {
        BoolQueryBuilder projectQueryBuilder = QueryBuilders.boolQuery();
        ofNullable(folder.getProjectId()).map(o -> projectQueryBuilder.must(QueryBuilders.termQuery("projectId", o)));
        ofNullable(folder.getType()).map(o -> projectQueryBuilder.must(QueryBuilders.matchPhraseQuery("type", o)));
        ofNullable(folder.getUserId()).map(o -> projectQueryBuilder.must(QueryBuilders.termsQuery("userId", o)));
        ofNullable(projectIds).ifPresent(o -> {
            List<String> esIds = o.stream().map(id -> EsDocumentUtil.getEsId(ProjectFolder.getUniqueId(id, folder.getType())
                , ProjectFolder.class)).collect(Collectors.toList());
            projectQueryBuilder.must(QueryBuilders.idsQuery().addIds(esIds.toArray(new String[o.size()])));
        });
        return projectQueryBuilder;
    }
}
