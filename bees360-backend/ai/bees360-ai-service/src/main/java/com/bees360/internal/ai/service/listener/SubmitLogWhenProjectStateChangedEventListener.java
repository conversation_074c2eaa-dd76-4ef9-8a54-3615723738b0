package com.bees360.internal.ai.service.listener;

import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.entity.LogEntryDetail;
import com.bees360.internal.ai.entity.enums.AiBotUserEnum;
import com.bees360.internal.ai.entity.enums.LogEntryTypeEnum;
import com.bees360.internal.ai.entity.enums.LogProjectStateActionEnum;
import com.bees360.internal.ai.entity.enums.LogProjectStateDetailEnum;
import com.bees360.internal.ai.service.ProjectLogService;
import com.bees360.project.state.AbstractProjectState;
import com.google.gson.Gson;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;

/**
 * 监听项目状态变更事件并记录相关日志
 */
@Log4j2
@Component
public class SubmitLogWhenProjectStateChangedEventListener extends AbstractNamedEventListener<ProjectStateChangedEvent> {


    @Autowired
    private ProjectLogService projectLogService;

    @Override
    public void handle(ProjectStateChangedEvent event) throws IOException {
        log.info("handleEventOnProjectStateChangedEvent event:{}", new Gson().toJson(event));
        AbstractProjectState currentState = event.getCurrentState();
        String userId = Optional.ofNullable(currentState.getUpdatedBy()).orElse(AiBotUserEnum.AI_NEW_USER_ID.getCode());
        LogProjectStateActionEnum actionEnum = LogProjectStateActionEnum.getEnumByState(currentState.getState());
        if (Objects.nonNull(actionEnum)) {
            projectLogService.addLogEntryByLogEntryType(
                event.getProjectId(),
                userId,
                LogEntryTypeEnum.PROJECT_STATE_CHANGE,
                new LogEntryDetail(LogProjectStateDetailEnum.PROJECT_STATE, actionEnum));
        }
    }
}
