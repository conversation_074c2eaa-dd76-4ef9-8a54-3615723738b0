package com.bees360.internal.ai.service.listener;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message.ActivityMessage;
import com.bees360.activity.Message.ActivityMessage.ActionType;
import com.bees360.activity.Message.ActivityMessage.Entity;
import com.bees360.activity.Message.ActivityMessage.EntityType;
import com.bees360.activity.Message.ActivityMessage.Field;
import com.bees360.activity.Message.ActivityMessage.FieldName;
import com.bees360.api.InvalidArgumentException;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.PipelineTaskEnum;
import com.bees360.entity.enums.ProcessStatusEnum;
import com.bees360.event.EventPublisher;
import com.bees360.event.registry.ProjectClosedEvent;
import com.bees360.event.registry.ProjectReopenedEvent;
import com.bees360.internal.ai.entity.LogEntry;
import com.bees360.internal.ai.entity.Projects;
import com.bees360.internal.ai.entity.StatusChangeLogDetail;
import com.bees360.internal.ai.entity.enums.AiBotUserEnum;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.LogEntryTypeEnum;
import com.bees360.internal.ai.event.ProjectAiStatusChangeEvent;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectLogService;
import com.bees360.internal.ai.util.date.DateUtil;
import com.bees360.pipeline.PipelineService;
import com.bees360.user.Message.UserMessage;
import com.google.protobuf.util.Timestamps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.thymeleaf.util.StringUtils;

import java.time.ZoneId;
import java.util.Objects;

import static com.bees360.internal.ai.entity.dto.ProcessStatusEnum.DELETED;
import static com.bees360.pipeline.Message.PipelineStatus.DONE;

/**
 * <AUTHOR>
 * @since 2020/07/24 17:15
 */
@Slf4j
@Component
public class ProjectAiStatusChangeEventListener {

    @Autowired private ProjectLogService projectLogService;

    @Autowired private ActivityManager activityManager;
    @Autowired private EventPublisher eventPublisher;
    @Autowired private ProjectEsService projectService;
    @Autowired private PipelineService pipelineService;

    @EventListener
    public void addProjectLogOnStatusChangeEvent(ProjectAiStatusChangeEvent event) {
        log.debug(
                "addProjectLogOnStatusChangeEvent projectId:{}, newStatus:{}",
                event.getProjectId(),
                event.getNewStatus());
        projectLogService.insertOrUpdateByLogEntry(event.getProjectId(), toLogEntry(event));
    }

    private LogEntry toLogEntry(ProjectAiStatusChangeEvent event) {
        String createTime =
                DateUtil.convertDate(event.getCreateTime(), DateUtil.UTC_FORMAT, ZoneId.of("UTC"));
        StatusChangeLogDetail logDetail =
                new StatusChangeLogDetail(event.getNewStatus(), 0, event.getPreStatus());
        return new LogEntry(
                event.getUserId(),
                event.getUsername(),
                LogEntryTypeEnum.PROJECT_STATUS_CHANGE.getType(),
                createTime,
                logDetail);
    }

    @EventListener
    public void addActivityOnStatusChangeEvent(ProjectAiStatusChangeEvent event) {
        log.info(
                "addActivityOnStatusChangeEvent projectId:{}, oldStatus:{}, userId:{}, newStatus:{}",
                event.getProjectId(),
                event.getPreStatus(),
                event.getUserId(),
                event.getNewStatus());
        if (Objects.equals(event.getPreStatus(), event.getNewStatus())
                || StringUtils.equals(event.getUserId(), AiBotUserEnum.WEB_NEW_USER_ID.getCode())) {
            return;
        }
        UserMessage userMessage = UserMessage.newBuilder().setId(event.getUserId()).build();
        ActivityMessage message =
                ActivityMessage.newBuilder()
                        .setProjectId(event.getProjectId())
                        .setCreatedBy(userMessage)
                        .setCreatedAt(Timestamps.fromMillis(event.getCreateTime()))
                        .setSource(ActivitySourceEnum.AI.getValue())
                        .setEntity(
                                Entity.newBuilder()
                                        .setType(EntityType.PROJECT.name())
                                        .setCount(1)
                                        .build())
                        .setAction(ActionType.CHANGE.name())
                        .setField(
                                Field.newBuilder()
                                        .setName(FieldName.STATUS.name())
                                        .setOldValue(
                                                AiProjectStatusEnum.getStatusDesc(
                                                        event.getPreStatus()))
                                        .setValue(
                                                AiProjectStatusEnum.getStatusDesc(
                                                        event.getNewStatus()))
                                        .build())
                        .build();
        activityManager.submitActivity(Activity.of(message));
    }

    @Async
    @EventListener
    public void closeProjectOnProjectArchive(
            ProjectAiStatusChangeEvent projectAiStatusChangeEvent) {
        int preStatus = projectAiStatusChangeEvent.getPreStatus();
        int status = projectAiStatusChangeEvent.getNewStatus();
        if (!Objects.equals(AiProjectStatusEnum.PROJECT_ARCHIVED.getCode(), status)
                || status == preStatus) {
            return;
        }
        long projectId = projectAiStatusChangeEvent.getProjectId();
        var event = new ProjectClosedEvent();
        event.setProject(Projects.from(projectService.findProjectByProjectId(projectId)));
        eventPublisher.publish(event);
        log.info("Successfully publish ProjectClosedEvent on project '{}' archived.", projectId);
    }

    @Async
    @EventListener
    public void recoverProject(ProjectAiStatusChangeEvent projectAiStatusChangeEvent) {
        int preStatus = projectAiStatusChangeEvent.getPreStatus();
        int status = projectAiStatusChangeEvent.getNewStatus();
        var statusEnum = AiProjectStatusEnum.getEnum(status);
        if (statusEnum == null) {
            log.warn("Unknown project '{}' status {}.", projectAiStatusChangeEvent.getProjectId(), status);
            return;
        }
        var processEnum = statusEnum.getProcessStatus();
        if (!Objects.equals(AiProjectStatusEnum.PROJECT_ARCHIVED.getCode(), preStatus)
                || status == preStatus
                || Objects.equals(processEnum, DELETED)) {
            return;
        }
        long projectId = projectAiStatusChangeEvent.getProjectId();
        var event = new ProjectReopenedEvent();
        event.setProject(Projects.from(projectService.findProjectByProjectId(projectId)));
        eventPublisher.publish(event);
        log.info(
                "Successfully publish ProjectReopenedEvent on project '{}' status changed to '{}' from archived.",
                projectId,
                AiProjectStatusEnum.getEnum(status));
    }

    @EventListener
    public void setPipelineTaskStatusOnProjectStatusChanged(ProjectAiStatusChangeEvent event) {
        int status = event.getNewStatus();
        var taskKey = getTaskKey(status);
        if (taskKey == null) {
            return;
        }
        setPipelineTaskStatus(String.valueOf(event.getProjectId()), taskKey, DONE);
    }

    private String getTaskKey(int status) {
        var statusEnum = AiProjectStatusEnum.getEnum(status);
        if (statusEnum == null) {
            return null;
        }

        switch (statusEnum) {
            case CLIENT_RECEIVED:
                return PipelineTaskEnum.CLIENT_RECEIVED.getKey();

            case ESTIMATE_COMPLETED:
                return PipelineTaskEnum.ESTIMATE_COMPLETE.getKey();
        }
        return null;
    }

    private void setPipelineTaskStatus(
            String pipelineId, String key, com.bees360.pipeline.Message.PipelineStatus status) {
        try {
            pipelineService.setTaskStatus(pipelineId, key, status);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Failed to set pipeline '{}' task '{}' to '{}'.", pipelineId, key, status, e);
        } catch (RuntimeException e) {
            log.error("Failed to set pipeline '{}' task '{}' to '{}'.", pipelineId, key, status, e);
        }
    }
}
