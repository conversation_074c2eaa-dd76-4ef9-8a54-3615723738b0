package com.bees360.internal.ai.service.config;

import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.rabbit.config.RabbitApiConfig;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    RabbitApiConfig.class,
    RabbitJobDispatcher.class,
    RabbitJobScheduler.class,
    RabbitEventDispatcher.class,
    RabbitEventPublisher.class
})
@Configuration
public class JobConfig {}
