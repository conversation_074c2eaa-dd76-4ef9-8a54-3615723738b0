package com.bees360.internal.ai.service.listener;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.entity.enums.LogReportActionEnum;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.report.ReportProvider;

import com.bees360.user.Message;
import com.bees360.activity.Message.ActivityMessage;
import com.google.protobuf.util.Timestamps;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听报告组添加事件并创建相应的活动记录
 */
@Log4j2
public class AddActivityOnReportGroupAdded extends AbstractNamedEventListener<ReportGroupAdded> {

    private final ActivityManager activityManager;

    private final ReportProvider reportProvider;

    public AddActivityOnReportGroupAdded(
            ActivityManager activityManager,
            ReportProvider reportProvider) {
        this.activityManager = activityManager;
        this.reportProvider = reportProvider;
        log.info(
                "Created {}(activityManager={}, reportProvider={}).",
                this,
                activityManager,
                reportProvider);
    }

    @Override
    public void handle(ReportGroupAdded event) throws IOException {
        log.info("Add activity on ReportGroupAdded event:{}", event.toString());
        String groupType = event.getGroupType();
        if (!DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE.equalsIgnoreCase(groupType)) {
            return;
        }

        var projectId = Long.parseLong(event.getGroupKey());
        var report = reportProvider.get(event.getReportId());
        var reportType = ReportTypeEnum.getReportType(report);
        String userId = event.getCreatedBy();
        var logValue =
                LogReportActionEnum.getEnum(ReportGenerationStatusEnum.GENERATED.getCode())
                        .getValue();
        Message.UserMessage userMessage = Message.UserMessage.newBuilder().setId(userId).build();
        activityManager.submitActivity(
                Activity.of(
                        ActivityMessage.newBuilder()
                                .setAction(ActivityMessage.ActionType.CHANGE.name())
                                .setProjectId(projectId)
                                .setEntity(
                                        ActivityMessage.Entity.newBuilder()
                                                .setId(reportType.getCode() + "")
                                                .setCount(1)
                                                .setType(ActivityMessage.EntityType.REPORT.name())
                                                .build())
                                .setField(
                                        ActivityMessage.Field.newBuilder()
                                                .setType(ActivityMessage.FieldType.STRING.name())
                                                .setName(ActivityMessage.FieldName.STATUS.name())
                                                .setValue(logValue)
                                                .build())
                                .setCreatedBy(userMessage)
                                .setCreatedAt(Timestamps.fromMillis(System.currentTimeMillis()))
                                .setSource(ActivitySourceEnum.AI.getValue())
                                .build()));
    }
}
