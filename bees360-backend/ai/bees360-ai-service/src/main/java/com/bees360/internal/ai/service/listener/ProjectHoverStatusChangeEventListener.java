package com.bees360.internal.ai.service.listener;

import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.job.registry.SerializableFirebaseHover;
import com.bees360.job.registry.SerializableFirebaseProject;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.enums.ProjectHoverStatusEnum;
import com.bees360.internal.ai.event.ProjectHoverStatusChangedEvent;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.firebase.FirebaseHoverJobService;
import com.bees360.internal.ai.service.firebase.FirebaseProjectService;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * project hover状态更改处理事件
 */
@Slf4j
@Component
public class ProjectHoverStatusChangeEventListener {


    @Autowired
    private ProjectEsService projectEsService;

    @Autowired
    private FirebaseHoverJobService firebaseHoverJobService;

    @Autowired
    private FirebaseProjectService firebaseProjectService;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Async
    @EventListener
    public void projectHoverStatusChangeEvent(ProjectHoverStatusChangedEvent event) {
        log.info("project hover status change projectId {}, old status {}, new status {}", event.getProjectId(),
            event.getOldStatus(), event.getNewStatus());
        if (Objects.equals(event.getOldStatus(), event.getNewStatus())) {
            return;
        }
        ProjectEsModel project = projectEsService.findProjectByProjectId(event.getProjectId());
        if (null == project) {
            log.warn("project hover status change, projectId not found {}", event.getProjectId());
            return;
        }

        syncToFirebaseHoverJob(event, project.getHoverJobId());
    }

    /**
     * 状态同步到firebase HoverJob
     *
     * @param event
     */
    private void syncToFirebaseHoverJob(ProjectHoverStatusChangedEvent event, String hoverJobId) {
        SerializableFirebaseProject project = firebaseProjectService.getByProjectId(event.getProjectId());
        if (bees360FeatureSwitch.isEnableSyncFirebaseHoverStatus()) {
            log.info("SyncFirebaseHoverStatus enabled. sync hover projectId {}", event.getProjectId());
        } else {
            log.info("SyncFirebaseHoverStatus disabled. sync hover projectId {}", event.getProjectId());
            if (Objects.isNull(project) || Objects.isNull(project.getHover_job_id())) {
                log.info("Project {} does not have hover information in firebase, abandon modification.", event.getProjectId());
                return;
            }
        }
        if (Objects.isNull(hoverJobId)) {
            if (Objects.isNull(project.getHover_job_id())) {
                log.info("sync hover job, not yet subscribed to hover projectId {}", event.getProjectId());
                return;
            }
            hoverJobId = String.valueOf(project.getHover_job_id());
        }
        // 若状态无更改，无需同步
        SerializableFirebaseHover hoverJob = firebaseHoverJobService.getByHoverJobId(hoverJobId);
        if (Objects.nonNull(hoverJob) && Objects.equals(hoverJob.getBees360HoverState(), event.getNewStatus())) {
            log.info("sync hover job, hoverJob don't need to update projectId {}", event.getProjectId());
            return;
        }
        firebaseHoverJobService.setBees360HoverState(
                hoverJobId,
                event.getProjectId(),
                ProjectHoverStatusEnum.getEnum(event.getNewStatus()),
                project.getHover_account());
    }
}
