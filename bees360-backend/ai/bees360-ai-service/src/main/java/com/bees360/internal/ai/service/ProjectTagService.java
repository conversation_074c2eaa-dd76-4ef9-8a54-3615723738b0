package com.bees360.internal.ai.service;

import com.bees360.internal.ai.entity.ProjectTag;
import com.bees360.user.User;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/02/24 10:47
 */
@Deprecated
public interface ProjectTagService {

    @Deprecated
    void insertOrUpdateProjectTag(User user, long projectId, List<String> tagItem);

    @Deprecated
    List<ProjectTag> getByProjectIds(List<Long> projectIds);

    @Deprecated
    Map<Long, List<String>> getTagsByProjectIds(List<Long> projectIds);

    @Deprecated
    void deleteProjectTag(User user, long projectId);
}
