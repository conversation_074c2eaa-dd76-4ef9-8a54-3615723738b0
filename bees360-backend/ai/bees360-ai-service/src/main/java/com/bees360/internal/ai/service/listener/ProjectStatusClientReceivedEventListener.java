package com.bees360.internal.ai.service.listener;

import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.ProjectUserFolderTypeEnum;
import com.bees360.internal.ai.event.ProjectAiStatusChangeEvent;
import com.bees360.internal.ai.service.ProjectFolderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ProjectStatusClientReceivedEventListener {

    @Autowired private ProjectFolderService projectFolderService;

    @Async
    @EventListener
    public void removeProjectFromUrgentFolderOnClientReceived(ProjectAiStatusChangeEvent event) {
        long projectId = event.getProjectId();
        int status = event.getNewStatus();
        log.info(
                "Does the project {} status {} need to be removed from the urgent state? {}",
                projectId,
                status,
                AiProjectStatusEnum.CLIENT_RECEIVED.getCode() == status);
        if (AiProjectStatusEnum.CLIENT_RECEIVED.getCode() == status) {
            projectFolderService.deleteById(
                    null, projectId, ProjectUserFolderTypeEnum.URGENT.getType());
            log.info("Successfully remove project {} from urgent folder!", projectId);
        }
    }
}
