package com.bees360.internal.ai.service;

import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectOptionDict;
import com.bees360.internal.ai.entity.vo.ProjectEsOptionDict;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/07/24 17:33
 */
public interface ProjectOptionDictService {

    void addProjectDict(String type, Set<String> dict);

    ProjectOptionDict getByType(String type);

    List<ProjectOptionDict> getByIds(List<String> types);

    void addByNewProject(ProjectEsModel esModel);

    ProjectEsOptionDict getOptionDict();
}
