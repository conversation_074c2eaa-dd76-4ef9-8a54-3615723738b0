package com.bees360.internal.ai.service.impl;

import com.bees360.internal.ai.entity.enums.AiBotUserEnum;
import com.bees360.internal.ai.service.AiUserService;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class AiUserServiceImpl implements AiUserService {


    @Autowired
    private UserProvider userProvider;

    @Override
    public String getUsernameById(String id) {
        String aiBotName = AiBotUserEnum.getName(id);
        if (StringUtils.isBlank(aiBotName)) {
            try {
                User user = userProvider.findUserById(id);
                aiBotName = user.getName();
            } catch (Exception e) {
                aiBotName = AiBotUserEnum.UN_KNOWN_USER.getDisplay();
                log.warn("getUsernameById userId:{}, msg:{}, error:{}", id, e.getMessage(), e);
            }
        }
        return aiBotName;
    }

    @Override
    public User getUserById(String id) {
        User user = AiBotUserEnum.reloadUserByBot(AiBotUserEnum.getEnum(id));
        if (Objects.isNull(user)) {
            try {
                user = userProvider.findUserById(id);
            } catch (Exception e) {
                user = AiBotUserEnum.reloadUserByBot(AiBotUserEnum.UN_KNOWN_USER);
                log.warn("getUserById userId:{}, msg:{}, error:{}", id, e.getMessage(), e);
            }
        }
        return user;
    }

    @Override
    public User getUserByUsername(String username) {
        User user = null;
        try {
            Iterable<? extends User> userList = userProvider.findUserByName(username);
            if (userList != null) {
                user = Iterables.toStream(userList).findFirst().orElse(null);
            }
        } catch (Exception e) {
            log.warn("getUsernameById username:{}, msg:{}, error:{}", username, e.getMessage(), e);
        }
        if (Objects.isNull(user)) {
            user = AiBotUserEnum.reloadUserByBot(AiBotUserEnum.AI_NEW_USER_ID);
        }
        return user;
    }

    @Override
    public List<String> listUserIdByUsername(String username) {
        Iterable<? extends User> userList = userProvider.findUserByName(username);
        return Iterables.toList(Iterables.transform(userList, User::getId));
    }
}
