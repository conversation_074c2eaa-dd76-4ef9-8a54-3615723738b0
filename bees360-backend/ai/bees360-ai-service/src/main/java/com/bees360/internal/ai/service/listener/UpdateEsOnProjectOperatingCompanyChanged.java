package com.bees360.internal.ai.service.listener;

import com.bees360.event.registry.ProjectOperatingCompanyChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.service.ProjectEsService;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 监听项目运营公司变更事件并更新Elasticsearch中的项目数据
 */
@Log4j2
@Component
public class UpdateEsOnProjectOperatingCompanyChanged extends AbstractNamedEventListener<ProjectOperatingCompanyChanged> {
    private final ProjectEsService projectEsService;

    public UpdateEsOnProjectOperatingCompanyChanged(@NonNull ProjectEsService projectEsService) {
        this.projectEsService = projectEsService;
        log.info("Created '{}(projectEsService={})",this, this.projectEsService);
    }

    @Override
    public void handle(ProjectOperatingCompanyChanged event) throws IOException {
        var projectId = Long.parseLong(event.getProjectId());
        String operatingCompany = event.getOperatingCompany();
        var projectEsModel = ProjectEsModelUpdater.toBuilder()
            .setProjectId(projectId)
            .setOperatingCompany(operatingCompany)
            .build();

        projectEsService.updatePartial(projectEsModel);
    }
}
