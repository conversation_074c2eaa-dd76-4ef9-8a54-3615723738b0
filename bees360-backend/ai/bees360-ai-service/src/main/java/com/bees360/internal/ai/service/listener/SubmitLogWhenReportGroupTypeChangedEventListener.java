package com.bees360.internal.ai.service.listener;

import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.registry.ReportGroupTypeChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.entity.LogEntryDetail;
import com.bees360.internal.ai.entity.enums.LogEntryTypeEnum;
import com.bees360.internal.ai.entity.enums.LogReportActionEnum;
import com.bees360.internal.ai.entity.enums.LogReportDetailEnum;
import com.bees360.internal.ai.service.ProjectLogService;
import com.bees360.report.ReportProvider;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;

/**
 * 监听报告组类型变更事件，当组类型为永久删除时记录相关日志
 */
@Log4j2
public class SubmitLogWhenReportGroupTypeChangedEventListener
        extends AbstractNamedEventListener<ReportGroupTypeChanged> {

    private static final String PROJECT_PERMANENTLY_DELETE_GROUP_TYPE = "GROUP_PROJECT-D";

    private final ReportProvider reportProvider;

    private final ProjectLogService projectLogService;

    public SubmitLogWhenReportGroupTypeChangedEventListener(
            @NonNull ReportProvider reportProvider, @NonNull ProjectLogService projectLogService) {
        this.reportProvider = reportProvider;
        this.projectLogService = projectLogService;
        log.info(
                "Created {}(reportProvider={},projectLogService={}).",
                this,
                this.reportProvider,
                this.projectLogService);
    }

    @Override
    public void handle(ReportGroupTypeChanged event) throws IOException {
        log.info("Received ReportGroupTypeChanged event:{}", event);
        if (!StringUtils.equals(PROJECT_PERMANENTLY_DELETE_GROUP_TYPE, event.getGroupType())) {
            return;
        }
        var report = reportProvider.get(event.getReportId());
        var reportType =
                Optional.ofNullable(ReportTypeEnum.getReportType(report))
                        .orElseThrow(
                                () ->
                                        new NoSuchElementException(
                                                "The report type does not exist. value:"
                                                        + report.getType()));
        LogReportDetailEnum detailEnum = LogReportDetailEnum.getEnumByReportType(reportType.getCode());
        if (Objects.nonNull(detailEnum)) {
            projectLogService.addLogEntryByLogEntryType(
                    Long.parseLong(event.getGroupKey()),
                    event.getUpdatedBy(),
                    LogEntryTypeEnum.REPORT,
                    new LogEntryDetail(detailEnum, LogReportActionEnum.DELETED));
        }
    }
}
