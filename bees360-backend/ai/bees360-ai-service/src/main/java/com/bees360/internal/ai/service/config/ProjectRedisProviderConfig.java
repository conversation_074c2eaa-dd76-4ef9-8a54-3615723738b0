package com.bees360.internal.ai.service.config;

import com.bees360.atomic.RedisLockProvider;
import com.bees360.internal.ai.entity.consts.RedisLockKey;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
public class ProjectRedisProviderConfig {

    @Bean
    public RedisLockProvider projectLockProvider(RedissonClient redissonClient) {
        return new RedisLockProvider(
                redissonClient, RedisLockKey.PROJECT_NAMESPACE, Duration.ofMinutes(1));
    }

}
