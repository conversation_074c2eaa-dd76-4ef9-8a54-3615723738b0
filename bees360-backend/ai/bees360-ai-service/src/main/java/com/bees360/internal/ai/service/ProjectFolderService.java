package com.bees360.internal.ai.service;

import com.bees360.user.User;

import jakarta.annotation.Nullable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/07/24 17:33
 */
public interface ProjectFolderService {

    void addProjectFolder(User user, long projectId, String type);

    void addProjectFolder(String userId, long projectId, String type);

    void deleteById(@Nullable User user, long projectId, String type);

    List<Long> getByProjectIdsAndType(List<Long> projectIds, String type);

    /**
     * 获取rework folder对应的projectId
     *
     * @return projectId list
     */
    List<Long> getReworkProjectIds();
}
