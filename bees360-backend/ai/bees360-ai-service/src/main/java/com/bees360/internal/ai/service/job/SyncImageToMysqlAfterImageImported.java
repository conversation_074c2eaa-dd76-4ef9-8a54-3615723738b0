package com.bees360.internal.ai.service.job;

import com.bees360.internal.ai.service.ProjectImageService;
import com.bees360.job.registry.ProjectImageCopiedJob;
import com.bees360.job.util.AbstractJobExecutor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Log4j2
public class SyncImageToMysqlAfterImageImported extends AbstractJobExecutor<ProjectImageCopiedJob> {
    private final ProjectImageService projectImageService;

    public SyncImageToMysqlAfterImageImported(ProjectImageService projectImageService) {
        this.projectImageService = projectImageService;
        log.info("create '{} with projectImageService '{}'", this, projectImageService);
    }

    @Override
    protected void handle(ProjectImageCopiedJob job) throws IOException {
        log.info(
                "Start to copy image in ai with project '{}' and image map '{}'",
                job.getProjectId(),
                job.getImageId());
        projectImageService.copyProjectImage(Long.parseLong(job.getProjectId()), job.getImageId());
    }
}
