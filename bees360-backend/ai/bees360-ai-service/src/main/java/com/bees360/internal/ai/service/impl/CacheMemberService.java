package com.bees360.internal.ai.service.impl;

import com.bees360.commons.elasticsearchsupport.ElasticSearchHelper;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectOptionDict;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.ProjectOptionDictTypeEnum;
import com.bees360.internal.ai.service.MemberService;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.entity.query.ProjectEsQueryParam;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import java.util.concurrent.locks.Lock;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.regexp.RE;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RMap;
import org.redisson.api.RMapCache;
import org.redisson.api.RSet;
import org.redisson.api.RSetCache;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
@Service
public class CacheMemberService implements MemberService {

    private final RedissonClient redissonClient;

    private final ElasticSearchHelper elasticSearchHelper;

    public static final String STAFF_NAME_SET_PREFIX = "SEARCH_NAME_SET:";

    private static final String STAFF_NAME_EXPIRY_SET_PREFIX = "EXPIRED_FLAG_SET:";

    public static final Byte REMOVED = 1;

    private static final Byte NOT_REMOVED = 0;

    public CacheMemberService(RedissonClient redissonClient, ElasticSearchHelper elasticSearchHelper) {
        this.redissonClient = redissonClient;
        this.elasticSearchHelper = elasticSearchHelper;
    }

    @Override
    public List<String> getMemberNameByRole(ProjectOptionDictTypeEnum roleEnum) {
        RMapCache<String, Byte> mapCache = redissonClient.getMapCache(STAFF_NAME_SET_PREFIX + roleEnum.name());
        if (!mapCache.isExists() || checkCacheExpired(roleEnum.name())) {
            var start = System.currentTimeMillis();
            loadAllFromSource(roleEnum);
            log.info("Loading user from es to redis costs {}s.", System.currentTimeMillis() - start);
            mapCache = redissonClient.getMapCache(STAFF_NAME_SET_PREFIX + roleEnum.getType());
        }
        return mapCache.readAllEntrySet().stream()
            .filter(entry -> NOT_REMOVED.equals(entry.getValue()))
            .map(Map.Entry::getKey)
            .sorted(String::compareToIgnoreCase).collect(Collectors.toList());
    }


    private void loadAllFromSource(ProjectOptionDictTypeEnum roleEnum) {
        List<ProjectOptionDict> projectOptionDicts =
            elasticSearchHelper.searchListResultById(List.of(roleEnum.getType()), ProjectOptionDict.class);
        projectOptionDicts.forEach(dict -> {
            putNamesToRoleGroup(roleEnum, dict.getDict());
        });
    }

    private void putNamesToRoleGroup(ProjectOptionDictTypeEnum roleEnum, Collection<String> names) {
        RMapCache<String, Byte> map = redissonClient.getMapCache(STAFF_NAME_SET_PREFIX + roleEnum.getType());
        Map<String, Byte> nameMarkMap = names.stream()
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toMap(Function.identity(), n -> NOT_REMOVED, (n1, n2) -> n1));
        map.putAll(nameMarkMap);
    }

    /** 单独用RObject设置过期标志，避免由于Redisson机制问题导致过期标志未被清除*/
    private boolean checkCacheExpired(String role) {
        RSetCache<Byte> expirySet = redissonClient.getSetCache(STAFF_NAME_EXPIRY_SET_PREFIX + role);
        if(expirySet.isExists()) {
            return false;
        }

        refreshExpiredFlag(role);
        return true;
    }

    private void refreshExpiredFlag(String role) {
        RSetCache<Byte> expirySet = redissonClient.getSetCache(STAFF_NAME_EXPIRY_SET_PREFIX + role);
        expirySet.add(REMOVED, 1, TimeUnit.MINUTES);
        expirySet.expire(1, TimeUnit.MINUTES);
    }
}
