package com.bees360.internal.ai.service.impl;

import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.service.PriceStrategy;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectImageManager;
import com.bees360.internal.ai.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2019/09/02 14:41
 */
@Service
public class ProjectServiceImpl implements ProjectService {

    @Autowired
    private ProjectImageManager projectImageManager;

    @Autowired
    private ProjectEsService projectEsService;

    @Autowired private Function<Long, PriceStrategy> companyInvoicePriceStrategyProvider;

    /**
     * 删除redis中的key值
     * 1. project
     * 6. aiprocess
     * 8. pipeline
     *
     * @param projectId
     * @throws ServiceException
     */
    @Override
    @Transactional
    public void deleteProject(long projectId) throws ServiceException {

        projectImageManager.deleteAll(projectId);
    }

    @Override
    public BigDecimal getProjectInvoicePrice(long projectId) {
        var project = projectEsService.findProjectByProjectId(projectId);
        return companyInvoicePriceStrategyProvider
                .apply(project.getInsuranceCompany())
                .calculatePrice(projectId);
    }
}
