package com.bees360.internal.ai.service.impl;

import com.bees360.ai.mapper.ProjectExportDataMapper;
import com.bees360.internal.ai.entity.ProjectExportData;
import com.bees360.internal.ai.service.ProjectExportDataManager;
import com.google.common.base.Preconditions;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * mybatis
 */
@Service
public class MybatisProjectExportDataManager implements ProjectExportDataManager {

    private ProjectExportDataMapper projectExportDataMapper;

    public MybatisProjectExportDataManager(ProjectExportDataMapper projectExportDataMapper) {
        this.projectExportDataMapper = projectExportDataMapper;
    }

    @Override
    public ProjectExportData insertOrUpdateData(ProjectExportData projectExportData) {
        Preconditions.checkNotNull(projectExportData, "project export data should not be null.");
        Preconditions.checkNotNull(projectExportData.getRelatedId(), "relatedId should not be null.");
        Preconditions.checkNotNull(projectExportData.getRelatedType(), "relatedType should not be null.");

        if (projectExportData.getId() == 0) {
            ProjectExportData existedProjectExportData = getSimpleExportData(projectExportData.getRelatedId(),
                projectExportData.getRelatedType());
            if (existedProjectExportData != null) {
                projectExportData.setId(existedProjectExportData.getId());
                updateWithId(projectExportData);
            } else {
                projectExportDataMapper.insertExportData(projectExportData);
            }
        } else {
            updateWithId(projectExportData);
        }
        return projectExportData;
    }

    private void updateWithId(ProjectExportData projectExportData) {
        long id = projectExportData.getId();
        ProjectExportData mappedProjectExportData = getSimpleExportDataById(id);
        Optional.ofNullable(mappedProjectExportData).orElseThrow(() ->
            new IllegalStateException("Resource with id %d is not found.".formatted(id)));
        projectExportDataMapper.updateExportData(projectExportData);
    }

    @Override
    public void deleteExportData(String relatedId, String relatedType) {
        Preconditions.checkNotNull(relatedId, "relatedId should not be null.");
        Preconditions.checkNotNull(relatedType, "relatedType should not be null.");
        ProjectExportData mappedProjectExportData = getByRelatedIdAndType(relatedId, relatedType);
        Optional.ofNullable(mappedProjectExportData).orElseThrow(() -> new IllegalStateException(
            "Resource with id %s is not found.".formatted(getTypeId(relatedType, relatedId))));
        projectExportDataMapper.deleteExportData(mappedProjectExportData.getId());
    }

    @Override
    public ProjectExportData getByRelatedIdAndType(String relatedId, String relatedType) {
        Preconditions.checkNotNull(relatedId, "relatedId should not be null.");
        Preconditions.checkNotNull(relatedType, "relatedType should not be null.");
        return projectExportDataMapper.getExportData(relatedId, relatedType).stream().findFirst().orElse(null);
    }

    private ProjectExportData getSimpleExportData(String relatedId, String relatedType) {
        return projectExportDataMapper.getSimpleExportData(relatedId, relatedType)
            .stream().findFirst().orElse(null);
    }

    private ProjectExportData getSimpleExportDataById(long id) {
        return projectExportDataMapper.getSimpleExportDataById(id);
    }

    private String getTypeId(String relatedId, String relatedType) {
        return relatedType + "-" + relatedId;
    }
}
