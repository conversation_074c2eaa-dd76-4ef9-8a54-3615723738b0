package com.bees360.internal.ai.service.impl;

import com.bees360.commons.elasticsearchsupport.ElasticSearchHelper;
import com.bees360.commons.elasticsearchsupport.EsAccessException;
import com.bees360.internal.ai.entity.LogEntry;
import com.bees360.internal.ai.entity.LogEntryDetail;
import com.bees360.internal.ai.entity.ProjectLog;
import com.bees360.internal.ai.entity.enums.LogEntryTypeEnum;
import com.bees360.internal.ai.service.AiUserService;
import com.bees360.internal.ai.service.ProjectLogService;
import com.bees360.internal.ai.util.date.DateUtil;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/07/24 17:40
 */
@Slf4j
@Service
public class ProjectLogEsServiceImpl implements ProjectLogService {

    @Autowired
    private ElasticSearchHelper elasticSearchHelper;

    @Autowired
    private AiUserService aiUserService;

    private void upsertProjectLog(ProjectLog projectLog) {
        try {
            elasticSearchHelper.update(projectLog);
        } catch (EsAccessException e) {
            log.warn("upsertProjectLog error, log:{} msg:{}", new Gson().toJson(projectLog),
                e.getMessage(), e);
        }
    }

    @Override
    public void insertOrUpdateByLogEntry(long projectId, LogEntry logEntry) {
        ProjectLog projectLog = getById(projectId);
        if (Objects.isNull(projectLog)) {
            projectLog = new ProjectLog();
            projectLog.setProjectId(projectId);
            projectLog.setLogEntry(Collections.singletonList(logEntry));
        } else {
            projectLog.getLogEntry().add(logEntry);
        }
        upsertProjectLog(projectLog);
    }

    @Override
    public void addLogEntry(long projectId, String userId, String username, String type, long createdTime, LogEntryDetail logDetail) {
        String logTime = DateUtil.convertDate(createdTime, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", ZoneId.of("UTC"));
        LogEntry logEntry = new LogEntry(userId, username, type, logTime, logDetail);
        insertOrUpdateByLogEntry(projectId, logEntry);
    }

    @Override
    public void addLogEntry(long projectId, String userId, String type, LogEntryDetail logDetail) {
        long createTime = System.currentTimeMillis();
        String userName = aiUserService.getUsernameById(userId);
        addLogEntry(projectId, userId, userName, type, createTime, logDetail);
    }

    @Override
    public void addLogEntryByLogEntryType(long projectId, String userId, LogEntryTypeEnum entryTypeEnum, LogEntryDetail logDetail) {
        long createTime = System.currentTimeMillis();
        String userName = aiUserService.getUsernameById(userId);
        addLogEntry(projectId, userId, userName, entryTypeEnum.getType(), createTime, logDetail);
    }

    @Override
    public ProjectLog getById(long projectId) {
        return elasticSearchHelper.getById(projectId + "", ProjectLog.class).orElse(null);
    }

    @Override
    public List<ProjectLog> getLogList(Collection<Long> projectIds) {
        return elasticSearchHelper.searchListResultById(projectIds.stream().map(String::valueOf).collect(Collectors.toList()), ProjectLog.class);
    }

}
