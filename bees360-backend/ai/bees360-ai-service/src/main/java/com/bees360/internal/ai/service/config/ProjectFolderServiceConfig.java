package com.bees360.internal.ai.service.config;

import com.bees360.commons.elasticsearchsupport.ElasticSearchHelper;
import com.bees360.internal.ai.service.ProjectFolderService;
import com.bees360.internal.ai.service.impl.ProjectFolderServiceImpl;
import com.bees360.project.config.GrpcProjectGroupManagerConfig;
import com.bees360.project.group.GrpcProjectGroupManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({GrpcProjectGroupManagerConfig.class})
public class ProjectFolderServiceConfig {

    @Bean
    ProjectFolderService projectFolderService(
            @Autowired ElasticSearchHelper elasticSearchHelper,
            @Autowired Bees360FeatureSwitch bees360FeatureSwitch,
            @Autowired GrpcProjectGroupManager grpcProjectGroupManager) {
        return new ProjectFolderServiceImpl(elasticSearchHelper, bees360FeatureSwitch, grpcProjectGroupManager);
    }
}
