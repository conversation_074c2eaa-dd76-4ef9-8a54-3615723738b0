package com.bees360.internal.ai.service.scheduled;

import com.bees360.ai.mapper.JobDataMapper;
import com.bees360.api.ApiStatus;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.event.EventPublisher;
import com.bees360.event.registry.JobCompleted;
import com.bees360.internal.ai.entity.JobData;
import com.bees360.internal.ai.entity.enums.JobTypeEnum;
import com.bees360.internal.ai.util.date.DateUtil;
import com.bees360.resource.ResourcePool;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

/**
 * 监听job执行完成事件，目前通过jobName和jobId分别调用不同的方法
 *
 * <AUTHOR>
 */
@Log4j2
@Component
public class JobCompletedScheduled {

    private final JobDataMapper jobDataMapper;

    private final ResourcePool resourcePool;

    private final EventPublisher eventPublisher;

    public JobCompletedScheduled(JobDataMapper jobDataMapper, ResourcePool resourcePool, EventPublisher eventPublisher) {
        this.jobDataMapper = jobDataMapper;
        this.resourcePool = resourcePool;
        this.eventPublisher = eventPublisher;

        log.info("Created '{}(jobDataMapper={}, resourcePool={}, eventPublisher={})'", this, this.jobDataMapper, this.resourcePool, this.eventPublisher);
    }

    /**
     * 每隔30分钟执行一次，校验1.5小时之前启动的job是否执行完成 为了避免服务重启没有监听到JobCompleted事件做一个补偿机制
     */
    public void sendWorkloadStatics() {
        log.info("JobCompletedScheduled CronTriggerHalfHourCst");
        LocalDateTime centralNow = DateUtil.getUSCentralNow();
        long maxStartTime =
            centralNow
                .minusMinutes(90)
                .atZone(DateUtil.getUSCentralZoneId())
                .toInstant()
                .toEpochMilli();
        List<Integer> jobTypes =
            Arrays.stream(JobTypeEnum.values())
                .map(JobTypeEnum::getCode)
                .collect(Collectors.toList());
        List<JobData> jobDataList = jobDataMapper.listUnfinishedJob(maxStartTime, jobTypes);
        if (CollectionAssistant.isEmpty(jobDataList)) {
            log.info(
                "There are no outstanding jobs.maxStartTime:{}, jobTypes{}",
                maxStartTime,
                ListUtil.toString(jobTypes));
            return;
        }
        // TODO shoushan.zhao 此处暂不处理重启服务时没有监听到失败job的情况（暂时还没想到好的处理办法）
        // 原因1：这种情况出现概率极低， 原因2：判断启动时间是否超时并不合理，因为可能一直在排队，没有得到执行机会
        for (JobData jobData : jobDataList) {
            boolean complete = true;
            List<String> resultFileKeys = jobData.getResultDataKeys();
            for (String key : resultFileKeys) {
                if (Objects.isNull(resourcePool.head(key))) {
                    complete = false;
                    break;
                }
            }
            if (complete) {
                String jobName = jobData.getJobName();
                String pdfJob = JobTypeEnum.HTML_PDF.getName();
                JobCompleted jobCompleted =
                    new JobCompleted(jobData.getJobName(), jobData.getJobId(), ApiStatus.OK);

                log.info(
                    "Automatically complete the job. jobId:{}, jobName:{}",
                    jobData.getJobId(),
                    jobData.getJobName());

                if (pdfJob.equals(jobName)) {
                    eventPublisher.publish(jobCompleted);
                }
            }
        }
    }
}
