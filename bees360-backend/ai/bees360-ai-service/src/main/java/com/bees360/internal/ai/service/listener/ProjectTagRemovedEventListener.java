package com.bees360.internal.ai.service.listener;

import com.bees360.atomic.RedisLockProvider;
import com.bees360.event.registry.ProjectTagChangedObject;
import com.bees360.event.registry.ProjectTagRemoved;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.entity.ProjectTagEs;
import com.bees360.internal.ai.entity.consts.RedisLockKey;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 监听项目标签移除事件并同步更新Elasticsearch中的项目标签数据
 */
@Log4j2
@Component
public class ProjectTagRemovedEventListener extends AbstractNamedEventListener<ProjectTagRemoved> {

    private final ProjectEsService projectEsService;

    private final RedisLockProvider lockProvider;

    private final Bees360FeatureSwitch bees360FeatureSwitch;

    public ProjectTagRemovedEventListener(
            ProjectEsService projectEsService,
            RedisLockProvider projectLockProvider,
            Bees360FeatureSwitch bees360FeatureSwitch) {
        this.projectEsService = projectEsService;
        this.lockProvider = projectLockProvider;
        this.bees360FeatureSwitch = bees360FeatureSwitch;
    }

    @Override
    public void handle(ProjectTagRemoved event) throws IOException {
        log.info("Received ProjectTagRemoved :{}, start to handle.", event);
        var projectTags = event.getList();
        if (CollectionUtils.isEmpty(projectTags)) {
            return;
        }
        var ai =
                projectTags.stream()
                        .filter(
                                e ->
                                        Objects.equals(e.getUpdatedVia(), "AI")
                                                || Objects.equals(e.getUpdatedVia(), "IO"))
                        .collect(Collectors.toList());
        syncToEs(ai);
    }

    @Nullable
    @Override
    public String getName() {
        return "project_tag_removed";
    }

    public void syncToEs(List<ProjectTagChangedObject> projectTags) {
        log.info("Project tag removed {}", projectTags);
        if (CollectionUtils.isEmpty(projectTags)) {
            return;
        }
        var projectTag = projectTags.get(0);
        var projectTagList =
                projectTags.stream().map(ProjectTagEs::eventToES).collect(Collectors.toList());

        var lock = lockProvider.lock(RedisLockKey.PROJECT_LOCK_KEY + projectTag.getProjectId());

        try {
            var projectEsModel =
                    projectEsService.findProjectByProjectId(
                            Long.parseLong(projectTag.getProjectId()));
            if (projectEsModel == null) {
                log.warn(
                        "Project tag removed can not find project id {}",
                        projectTag.getProjectId());
                projectEsModel = new ProjectEsModel();
                projectEsModel.setProjectId(Long.parseLong(projectTag.getProjectId()));
                projectEsModel.setProjectTagList(List.of());
                projectEsModel.setTimeLines(new ArrayList<>());
                projectEsService.syncToEsFromProjectEsModel(projectEsModel, true);
            } else {
                List<ProjectTagEs> exist =
                        projectEsModel.getProjectTagList() == null
                                ? List.of()
                                : projectEsModel.getProjectTagList();
                var list =
                        exist.stream()
                                .filter(
                                        e ->
                                                projectTagList.stream()
                                                        .noneMatch(
                                                                o ->
                                                                        Objects.equals(
                                                                                e.getId(),
                                                                                o.getId())))
                                .collect(Collectors.toList());
                projectEsModel.setProjectTagList(list);
                if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
                    var esModelUpdater =
                            ProjectEsModelUpdater.toBuilder()
                                    .setProjectId(Long.parseLong(projectTag.getProjectId()))
                                    .setProjectTagList(list)
                                    .build();
                    projectEsService.updatePartial(esModelUpdater);
                } else {
                    projectEsService.syncToEsFromProjectEsModel(projectEsModel, true);
                }
            }
        } finally {
            lock.unlock();
        }
    }
}
