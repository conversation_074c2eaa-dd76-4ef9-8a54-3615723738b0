package com.bees360.internal.ai.service;

import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.entity.dto.IdValueDto;
import com.bees360.internal.ai.entity.dto.ImageIdAndCompassDto;
import com.bees360.internal.ai.entity.dto.ImageIn3DModelModifierListDto;
import com.bees360.internal.ai.entity.dto.ImageQueryDto;
import com.bees360.internal.ai.entity.dto.ProjectImageFilter;
import com.bees360.internal.ai.exchange.ai2client.ProjectImageProto;
import com.bees360.user.User;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/08/24
 */
public interface ProjectImageService {

    List<ProjectImage> listImages(long projectId, ImageQueryDto imageQuery);

    List<ProjectImage> listImagesWithoutDeleted(long projectId) throws ServiceException;

    void updateImageSort(long projectId, List<IdValueDto<String, Long>> sorts, String userId);

    void updateImageIn3DModel(long projectId, ImageIn3DModelModifierListDto imageIn3DModels);

    /**
     * 获取用于Boundary步骤的图片
     */
    List<ProjectImage> listBoundaryImages(long projectId, ProjectImageFilter imageFilter);

    /**
     * Delete specific images by imageId list, and related imageFacet(s) from redis,
     * But there is no check for authority of the user.
     * Notice: More image related clean steps need to be done in other service.
     *
     * @param projectId the project of the delete image
     * @param imageIds the imageId list of the delete images
     */
    void deleteImages(long projectId, List<String> imageIds);

    void deleteCompletely(long projectId, User user, List<String> imageIds);

    /**
     * recover image from logically delete
     *
     * @param projectId project primary key
     * @param imageIds imageId list of image that need to be recover
     */
    void recoverImages(long projectId, List<String> imageIds);

    void updateImagesCompass(
            long projectId, User user, List<ImageIdAndCompassDto> imageIdAndCompassDtos);

    void updateNumberOfOutbuildingsAndInteriorRooms(long projectId);

    List<ProjectImage> deriveImages(long projectId, User user, ProjectImageProto.DerivativeImageRequest derivativeImages);

    void copyProjectImage(long projectId, Map<String, String> copyImageMap);
}
