package com.bees360.internal.ai.service.entity;

import java.util.function.Function;

public class ImageSort {
    private static final int OBJECT_TAG_LEN = 4;
    private static final int SCOPE_TAG_LEN = 4;
    private static final int DIRECTION_TAG_LEN = 4;
    private static final int LOCATION_TAG_LEN = 4;
    private static final int SHOOTING_ORDER_LEN = 7;
    private static final int OBJECT_TAG_MAX = maxValue(OBJECT_TAG_LEN);
    private static final int SCOPE_TAG_MAX = maxValue(SCOPE_TAG_LEN);
    private static final int DIRECTION_TAG_MAX = maxValue(DIRECTION_TAG_LEN);
    private static final int LOCATION_TAG_MAX = maxValue(LOCATION_TAG_LEN);
    private static final int SHOOTING_ORDER_MAX = maxValue(SHOOTING_ORDER_LEN);

    private static final String DEFAULT_IMAGE_SORT_PREFIX = "%04d%04d%04d%04d"
        .formatted(OBJECT_TAG_MAX, SCOPE_TAG_MAX, DIRECTION_TAG_MAX, LOCATION_TAG_MAX);

    private static int maxValue(int len) {
        return (int) (Math.pow(10d, Integer.valueOf(len).doubleValue()) - 1);
    }

    private String imageSort;

    public ImageSort(Integer shootingOrder) {
        if (shootingOrder > SHOOTING_ORDER_MAX) {
            throw new IllegalArgumentException("shooting Order can not exceeds " + SHOOTING_ORDER_MAX);
        }
        this.imageSort = DEFAULT_IMAGE_SORT_PREFIX + generateImageSort(SHOOTING_ORDER_LEN, shootingOrder, sort -> sort);
    }

    public static Integer getMaxShootingOrder() {
        return SHOOTING_ORDER_MAX;
    }

    public String getImageSort() {
        return this.imageSort;
    }

    private String generateImageSort(int tagLen, Integer sortOrder, Function<String, String> sortBuilder) {
        return sortBuilder.apply(String.format("%0" + tagLen + "d", sortOrder));
    }
}
