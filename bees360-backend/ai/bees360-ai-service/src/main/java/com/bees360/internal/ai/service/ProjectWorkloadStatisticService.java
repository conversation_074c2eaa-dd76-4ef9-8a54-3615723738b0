package com.bees360.internal.ai.service;

import com.bees360.internal.ai.entity.dto.ProcessorClaimStatistics;
import com.bees360.internal.ai.entity.dto.ProcessorUWStatistics;
import com.bees360.internal.ai.entity.dto.ClaimWorkloadStatistics;
import com.bees360.internal.ai.entity.dto.UWWorkloadStatistics;

import java.time.Instant;
import java.util.Collection;

public interface ProjectWorkloadStatisticService {

    Collection<ProcessorUWStatistics> getProcessorUWWorkloadStatistic(Instant startTime, Instant endTime);

    Collection<ProcessorClaimStatistics> getProcessorClaimWorkloadStatistic(Instant startTime, Instant endTime);

    Collection<ClaimWorkloadStatistics> getAdjusterWorkloadStatistic(Instant startTime, Instant endTime);

    Collection<UWWorkloadStatistics> getReviewerUWWorkloadStatistic(Instant startTime, Instant endTime);

    Collection<ClaimWorkloadStatistics> getReviewerClaimWorkloadStatistic(Instant startTime, Instant endTime);
}
