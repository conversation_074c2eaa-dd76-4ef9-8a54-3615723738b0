package com.bees360.internal.ai.service.converter;

import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.enums.PayStatusEnum;
import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.internal.ai.entity.vo.ProjectEsGpiVo;
import com.bees360.internal.ai.entity.vo.ProjectInspectionInfoVo;
import com.bees360.internal.ai.entity.vo.ProjectInsuredInfoVo;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/06/12 17:45
 */
public class ProjectEsConverter {

    public static ProjectEsGpiVo toProjectEsGpiVo(ProjectEsModel esModel) {
        if (Objects.isNull(esModel)) {
            return null;
        }
        return ProjectEsGpiVo.builder()
            .address(esModel.getAddress())
            .agent(esModel.getAddress())
            .agentContactName(esModel.getAgentContactName())
            .agentEmail(esModel.getAgentEmail())
            .agentPhone(esModel.getAgentPhone())
            .assetOwnerEmail(esModel.getAssetOwnerEmail())
            .assetOwnerName(esModel.getAssetOwnerName())
            .assetOwnerPhone(esModel.getAssetOwnerPhone())
            .chimney(esModel.getChimney())
            .country(esModel.getCountry())
            .city(esModel.getCity())
            .customer(esModel.getCustomer())
            .description(esModel.getDescription())
            .dueDate(esModel.getDueDate())
            .flyZoneType(esModel.getFlyZoneType())
            .guideline(esModel.getGuideline())
            .inspectionNumber(esModel.getInspectionNumber())
            .inspectionType(esModel.getInspectionType())
            .insuranceCompanyName(esModel.getInsuranceCompanyName())
            .insuranceCompanyLogo(esModel.getInsuranceCompanyNameLogo())
            .insuredHomePhone(esModel.getInsuredHomePhone())
            .insuredWorkPhone(esModel.getInsuredWorkPhone())
            .isBooking(esModel.isBooking())
            .needPilot(esModel.isNeedPilot())
            .lat(esModel.getGpsLocationLatitude())
            .lng(esModel.getGpsLocationLongitude())
            .projectType(esModel.getProjectType())
            .repairCompanyName(esModel.getRepairCompanyName())
            .repairCompanyLogo(esModel.getRepairCompanyNameLogo())
            .reportServiceOption(esModel.getReportServiceOption())
            .roofEstimatedAreaItem(esModel.getRoofEstimatedAreaItem())
            .state(esModel.getState())
            .zipCode(esModel.getZipCode())
            .serviceType(esModel.getServiceType())
            .serviceName(Optional.ofNullable(ProjectServiceTypeEnum.getEnum(esModel.getServiceType())).map(ProjectServiceTypeEnum::getDisplay).orElse(null))
            .build();
    }

    public static ProjectInsuredInfoVo toProjectInsuredInfoVo(ProjectEsModel project) {
        if(project == null) {
            return null;
        }
        ProjectInsuredInfoVo insuredInfoVo = new ProjectInsuredInfoVo();
        insuredInfoVo.setAssetOwnerName(project.getAssetOwnerName());
        insuredInfoVo.setAssetOwnerEmail(project.getAssetOwnerEmail());
        insuredInfoVo.setAssetOwnerPhone(project.getAssetOwnerPhone());
        insuredInfoVo.setInsuredHomePhone(project.getInsuredHomePhone());
        insuredInfoVo.setInsuredWorkPhone(project.getInsuredWorkPhone());

        insuredInfoVo.setAddress(project.getAddress());
        insuredInfoVo.setCity(project.getCity());
        insuredInfoVo.setState(project.getState());
        insuredInfoVo.setZipCode(project.getZipCode());
        insuredInfoVo.setCountry(project.getCountry());
        insuredInfoVo.setLat(project.getGpsLocationLatitude());
        insuredInfoVo.setLng(project.getGpsLocationLongitude());

        insuredInfoVo.setProjectType(project.getProjectType());
        insuredInfoVo.setDueDate(project.getDueDate());
        String claimNote = "";
        if (Objects.nonNull(project.getServiceType()) &&
            (StringUtils.isBlank(project.getClaimNote()) || !project.getClaimNote().contains("serviceType"))) {
            claimNote = "serviceType: " + ProjectServiceTypeEnum.getEnum(project.getServiceType()).getDisplay() + "\r\n";
        }
        if (StringUtils.isNotBlank(project.getSpecialInstructions()) &&
            (StringUtils.isBlank(project.getClaimNote()) || !project.getClaimNote().contains("specialInstructions"))) {
            claimNote =  claimNote + "specialInstructions: " + project.getSpecialInstructions() + "\r\n";
        }
        if (StringUtils.isNotBlank(project.getSpecialInstructionComments()) &&
            (StringUtils.isBlank(project.getClaimNote()) || !project.getClaimNote().contains("specialInstructionComments"))) {
            claimNote =  claimNote + "specialInstructionComments: " + project.getSpecialInstructionComments() + "\r\n";
        }
        if (StringUtils.isNotBlank(project.getClaimNote())) {
            claimNote =  claimNote + project.getClaimNote();
        }

        insuredInfoVo.setClaimNote(claimNote);

        return insuredInfoVo;
    }

    public static ProjectInspectionInfoVo toProjectInspectionInfoVo(ProjectEsModel project) {
        if(project == null) {
            return null;
        }
        ProjectInspectionInfoVo inspectionInfoVo = new ProjectInspectionInfoVo();
        inspectionInfoVo.setPolicyNumber(project.getPolicyNumber());
        inspectionInfoVo.setInspectionNumber(project.getInspectionNumber());
        inspectionInfoVo.setClaimType(project.getClaimType());
        inspectionInfoVo.setDamageEventTime(project.getDamageEventTime());
        inspectionInfoVo.setAgent(project.getAgent());
        inspectionInfoVo.setAgentContactName(project.getAgentContactName());
        inspectionInfoVo.setAgentEmail(project.getAgentEmail());
        inspectionInfoVo.setAgentPhone(project.getAgentPhone());
        inspectionInfoVo.setInsuranceCompanyName(project.getInsuranceCompanyName());
        inspectionInfoVo.setInsuranceCompanyLogo(project.getInsuranceCompanyNameLogo());
        inspectionInfoVo.setRepairCompanyName(project.getRepairCompanyName());
        inspectionInfoVo.setRepairCompanyLogo(project.getRepairCompanyNameLogo());
        inspectionInfoVo.setInspectionTime(project.getInspectionTime());
        inspectionInfoVo.setPolicyEffectiveDate(Optional.ofNullable(project.getPolicyEffectiveDate())
            .map(o -> o.replaceAll("-", "")).orElse(""));
        inspectionInfoVo.setYearBuilt(project.getYearBuilt());
        inspectionInfoVo.setSiteInspectedTime(project.getSiteInspectedTime());
        inspectionInfoVo.setPayStatus(project.getPayStatus());
        if (Objects.nonNull(project.getPayStatus())) {
            inspectionInfoVo.setPayStatusName(PayStatusEnum.getEnum(project.getPayStatus()) == null ? "":
                PayStatusEnum.getEnum(project.getPayStatus()).getDisplay());
        }
        inspectionInfoVo.setCreatedTime(project.getCreatedTime());
        inspectionInfoVo.setCustomerContactedTime(project.getCustomerContactedTime());
        inspectionInfoVo.setOperatingCompanyName(project.getOperatingCompany());
        return inspectionInfoVo;
    }

}
