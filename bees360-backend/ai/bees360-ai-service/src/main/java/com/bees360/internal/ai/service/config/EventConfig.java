package com.bees360.internal.ai.service.config;

import com.bees360.activity.ActivityManager;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.*;

import com.bees360.internal.ai.service.listener.AddActivityOnReportGroupAdded;
import com.bees360.internal.ai.service.listener.SetPipelineTaskStatusOnReportGroupAdded;
import com.bees360.internal.ai.service.listener.SetPipelineTaskStatusOnReportResourceAdded;
import com.bees360.internal.ai.service.listener.UpdateParentChildProjectListener;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.report.ProjectReportManager;
import jakarta.annotation.PostConstruct;

import com.bees360.internal.ai.service.ProjectLogService;
import com.bees360.internal.ai.service.listener.SubmitLogWhenReportGroupAddedEventListener;
import com.bees360.internal.ai.service.listener.SubmitLogWhenReportGroupTypeChangedEventListener;
import com.bees360.report.ReportProvider;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Import({
    AutoTagImagesOnPipelineTaskReady.class,
    UpdateParentChildProjectListener.class,
})
@Configuration
public class EventConfig {

    @Autowired
    private ObjectProvider<EventListener> eventListeners;

    @Autowired
    private EventDispatcher eventDispatcher;

    @PostConstruct
    public void postConstruct() {
        eventListeners.stream().forEach(listener -> {
            eventDispatcher.enlist(listener);
        });
    }

    @Bean
    SubmitLogWhenReportGroupAddedEventListener submitLogWhenReportGroupAddedEventListener(
            @Autowired ProjectLogService projectLogService,
            @Autowired ReportProvider reportProvider,
            @Value("#{'${report.log-entry.special-report-type}'}")
                    Set<ReportTypeEnum> logEntrySpecialReportTypes) {
        return new SubmitLogWhenReportGroupAddedEventListener(
                projectLogService,
                reportProvider,
                logEntrySpecialReportTypes);
    }

    @Bean
    SubmitLogWhenReportGroupTypeChangedEventListener
    submitLogWhenReportGroupTypeChangedEventListener(
        @Autowired ProjectLogService projectLogService,
        @Autowired ReportProvider reportProvider) {
        return new SubmitLogWhenReportGroupTypeChangedEventListener(
            reportProvider, projectLogService);
    }

    @Bean
    SetPipelineTaskStatusOnReportGroupAdded setPipelineTaskStatusOnReportGroupAdded(
            @Autowired PipelineService pipelineService,
            @Autowired ReportProvider reportProvider) {
        return new SetPipelineTaskStatusOnReportGroupAdded(
                pipelineService, reportProvider);
    }

    @Bean
    SetPipelineTaskStatusOnReportResourceAdded setPipelineTaskStatusOnReportResourceAdded(
            @Autowired ProjectReportManager projectReportManager,
            @Autowired PipelineService pipelineService,
            @Autowired ReportProvider reportProvider) {
        return new SetPipelineTaskStatusOnReportResourceAdded(
                projectReportManager, pipelineService, reportProvider);
    }

    @Bean
    AddActivityOnReportGroupAdded addActivityOnReportGroupAdded(
            @Autowired ActivityManager activityManager,
            @Autowired ReportProvider reportProvider) {
        return new AddActivityOnReportGroupAdded(
                activityManager, reportProvider);
    }
}
