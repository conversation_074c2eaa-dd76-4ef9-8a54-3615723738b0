package com.bees360.internal.ai.common.msgcode;

public class ProjectMsgCode extends MCodeFactory{

    public MCode PROJECT_STATUS_IS_WRONG = code(1, "project status is wrong.");
    public MCode PROJECT_IS_NULL = code(2, "project not found.");
    public MCode PROJECT_STATUS_CAN_NOT_UPDATED = code(3, "project status can not be updated.");
    public MCode PROJECT_MEMBER_CAN_NOT_UPDATED = code(4, "fail to updated project member.");
    public MCode PROJECT_STATUS_UPDATE_FAILED = code(6, "fail to updated project status.");
    public MCode PROJECT_DISCUSS_IS_NULL = code(7, "project discuss note not found.");
    public MCode PROJECT_IS_NOT_ASSOCIATED_WITH_HOVER = code(8,
        "The project is not associated with <PERSON><PERSON>, please associate it in Beespilot.io before modifying the status.");

    public ProjectMsgCode(CodeLevel level, CodeModel model) {
        super(level.code(), model.code());
    }
}
