package com.bees360.internal.ai.common.msgcode;

public class MsgCodeManager {
	public static AIMsgCode AI = new AIMsgCode(CodeLevel.SERVICE, CodeModel.AI);
	public static SystemMsgCode SYSTEM = new SystemMsgCode();
	public static ProjectMsgCode PROJECT = new ProjectMsgCode(CodeLevel.SERVICE, CodeModel.PROJECT);
	public static ReportMsgCode REPORT = new ReportMsgCode(CodeLevel.SERVICE, CodeModel.REPORT);
	public static RequestMsgCode REQUEST = new RequestMsgCode(CodeLevel.SERVICE, CodeModel.REQUEST);
	public static RoleMsgCode ROLE = new RoleMsgCode(CodeLevel.SERVICE, CodeModel.ROLE);
    public static UserMsgCode USER = new UserMsgCode(CodeLevel.SERVICE, CodeModel.USER);
}
