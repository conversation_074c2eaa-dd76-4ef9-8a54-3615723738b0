package com.bees360.internal.ai.common.exceptions;

import org.apache.http.HttpStatus;

/**
 * <AUTHOR>
 * @date 2018/08/08
 */
public class HttpRequestUnauthorizedException extends HttpRequestException {

    private static final int HTTPS_STATUS = HttpStatus.SC_UNAUTHORIZED;
    private static final String MESSAGE = "Unauthorized";

    public HttpRequestUnauthorizedException() {
        super(HTTPS_STATUS, MESSAGE);
    }

    public HttpRequestUnauthorizedException(String msg) {
        super(HTTPS_STATUS, msg);
    }

    public HttpRequestUnauthorizedException(String msg, Throwable cause) {
        super(HTTPS_STATUS, msg, cause);
    }
}
