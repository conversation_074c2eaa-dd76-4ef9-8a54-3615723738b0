package com.bees360.internal.ai.common.msgcode;

public enum CodeLevel {
	SERVICE(1),
	REQUEST(2),
	SYSTEM(9)
	;

	private final int code;

	CodeLevel(int code) {
		scope(code);
		this.code = code;
	}

	public int code() {
		return code;
	}

	public static CodeLevel valueOf(int code) {
		for (CodeLevel level : CodeLevel.values()) {
			if (level.code() == code) {
				return level;
			}
		}
		return null;
	}

	private void scope(int code) {
		if (code < 1 || code > 9) {
			throw new AssertionError("The code of CodeLevel should great than 0 and lower than 10.");
		}
	}
}
