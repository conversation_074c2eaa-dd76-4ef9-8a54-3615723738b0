package com.bees360.internal.ai.common.exceptions;

import com.bees360.internal.ai.common.msgcode.MCodeFactory.MCode;
import java.io.Serial;

/**
 * 业务处理异常，发生这种异常的时候，会直接返回给客户端。
 * 因为可以直接继承RuntimeException，以减少没有必要的异常捕获。此外，RuntimeException 会触发Spring的事务回滚。
 *
 * <AUTHOR>
 * @date 2019/10/31
 */
public class ServiceException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = -824087270188153536L;

	protected int msgCode;

	public ServiceException(MCode code) {
		this(code.CODE, code.MSG);
	}

    public ServiceException(MCode code, Throwable cause) {
        this(code.CODE, code.MSG, cause);
    }

	public ServiceException(int msgCode) {
		super();
		this.msgCode = msgCode;
	}

	public ServiceException(int msgCode, String message) {
		super(message);
		this.msgCode = msgCode;
	}

	public ServiceException(int msgCode, Throwable cause) {
		super(cause);
		this.msgCode = msgCode;
	}

	public ServiceException(int msgCode, String message, Throwable cause) {
		super(message, cause);
		this.msgCode = msgCode;
	}

	public int getMsgCode() {
		return msgCode;
	}
}
