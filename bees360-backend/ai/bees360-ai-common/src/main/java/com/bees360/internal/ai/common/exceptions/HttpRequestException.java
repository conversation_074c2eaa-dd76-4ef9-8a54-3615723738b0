package com.bees360.internal.ai.common.exceptions;

/**
 * <AUTHOR>
 * @date 2018/08/08
 */
public abstract class HttpRequestException extends RuntimeException {

    private final int httpStatus;

    public HttpRequestException(int httpStatus, String msg) {
        super(msg);
        this.httpStatus = httpStatus;
    }

    public HttpRequestException(int httpStatus, String msg, Throwable cause) {
        super(msg, cause);
        this.httpStatus = httpStatus;
    }

    public int getHttpStatus() {
        return httpStatus;
    }
}
