package com.bees360.internal.ai.common.msgcode;

import java.util.HashMap;
import java.util.Map;

public class MCodeFactory {

	public static class MCode {
		public final int CODE;
		public final String MSG;

		MCode(int code, String msg) {
			this.CODE = code;
			this.MSG = msg;
		}

		@Override
		public String toString() {
			return "MCode [CODE=" + CODE + ", MSG=" + MSG + "]";
		}
	}

	private final CodeGenerater coder;
	private static final Map<Integer, MCode> mCodeStore = new HashMap<>();

	public MCodeFactory(int level, int model) {
		coder = new CodeGenerater(level, model);
	}

	protected MCode code(int detailCode, String msg) {
		MCode mCode = new MCode(coder.code(detailCode), msg);
		mCodeStore.put(mCode.CODE, mCode);
		return mCode;
	}

	protected static MCode putCode(int code, String msg) {
		MCode mCode = new MCode(code, msg);
		mCodeStore.put(mCode.CODE, mCode);
		return mCode;
	}

	public static MCode fromCode(int code) {
		return mCodeStore.get(code);
	}

	public static String getMsg(int code) {
		MCode mCode = fromCode(code);
		return mCode == null ? "" : mCode.MSG;
	}
}
