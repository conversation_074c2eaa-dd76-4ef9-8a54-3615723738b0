package com.bees360.internal.ai.common.grpc;

import com.bees360.common.grpc.GrpcClient;
import com.bees360.common.grpc.GrpcConfig;
import io.grpc.ManagedChannel;

/**
 * ai grpc client for [3d,pc,ad service]
 */
public class AiGrpcClient extends GrpcClient {

    private ManagedChannel channel;

    /**
     * 连接远程服务器
     */
    public AiGrpcClient(GrpcConfig grpcConfig) {
        super(grpcConfig);
        this.channel = buildChannel();
    }

    public ManagedChannel getChannel() {
        return this.channel;
    }

    @Override
    public String toString() {
        StringBuffer buf = new StringBuffer();
        buf.append("AiGrpcClient service endpoints=").append(getEndpoints());
        return buf.toString();
    }
}
