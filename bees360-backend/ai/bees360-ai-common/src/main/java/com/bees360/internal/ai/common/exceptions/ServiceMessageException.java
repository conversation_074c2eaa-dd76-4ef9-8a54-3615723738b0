package com.bees360.internal.ai.common.exceptions;

import java.io.Serial;

public class ServiceMessageException extends ServiceException {

    @Serial
    private static final long serialVersionUID = 4848022285285277567L;

	private String message;

	public ServiceMessageException(int code, String message) {
		super(code);
		this.message = message;
	}

	public ServiceMessageException(int code, String message, Throwable cause) {
		super(code, cause);
		this.message = message;
	}

	public String getMessage() {
		return message;
	}
}
