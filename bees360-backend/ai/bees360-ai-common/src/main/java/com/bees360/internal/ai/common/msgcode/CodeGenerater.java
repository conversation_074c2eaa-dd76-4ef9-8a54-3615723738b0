package com.bees360.internal.ai.common.msgcode;

import java.util.HashSet;
import java.util.Set;

//From bees360-shop migration
public class CodeGenerater {

	private static final int BITS_LEVEL = 1;
	private static final int BITS_MODEL = 2;
	private static final int BITS_DETAIL = 4;

	private static final int MAX_LEVEL = (int) Math.pow(10, BITS_LEVEL);
	private static final int MAX_MODEL = (int) Math.pow(10, BITS_MODEL);
	private static final int MAX_DETAIL = (int) Math.pow(10, BITS_DETAIL);

	private static final int MOVE_LEVEL = (int) Math.pow(10, BITS_MODEL + BITS_DETAIL);
	private static final int MOVE_MODEL = (int) Math.pow(10, BITS_DETAIL);
	private static final int MOVE_DETAIL = 1;

	private static final Set<Integer> LEVEL_MODEL_SET = new HashSet<>();

	private int level;
	private int model;

	public CodeGenerater(int level, int model) {
		check(level, model);

		this.level = level % MAX_LEVEL;
		this.model = model % MAX_MODEL;
	}

	private void check(int level, int model) {
		if (isIrregalValue(level, MAX_LEVEL) || isIrregalValue(model, MAX_MODEL)) {
			throw new AssertionError("The value of paramter in the specified range.");
		}
		int levelModel = level * MOVE_LEVEL + model * MOVE_MODEL;
		if (LEVEL_MODEL_SET.contains(levelModel)) {
			throw new AssertionError("The level-model pair has existed.");
		}
		LEVEL_MODEL_SET.add(levelModel);
	}

	private static boolean isIrregalValue(int value, int max) {
		return value < 0 || max <= value;
	}

	int code(int detail) {
		if (isIrregalValue(detail, MAX_DETAIL)) {
			throw new AssertionError("The value of paramter in the specified range.");
		}
		return generatedCode(this.level, this.model, detail);
	}

	private static int generatedCode(int level, int model, int detail) {
		return level * MOVE_LEVEL + model * MOVE_MODEL + detail * MOVE_DETAIL;
	}

	public static int code(int level, int model, int detail) {
		if (isIrregalValue(level, MAX_LEVEL) || isIrregalValue(model, MOVE_MODEL)
				|| isIrregalValue(detail, MAX_DETAIL)) {
			throw new AssertionError("The value of paramter in the specified range.");
		}
		return generatedCode(level, model, detail);
	}
}
