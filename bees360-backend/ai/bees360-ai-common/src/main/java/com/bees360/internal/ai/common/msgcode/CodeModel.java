package com.bees360.internal.ai.common.msgcode;

public enum CodeModel {
	AI(1),
	PROJECT(2),
	IMAGE(3),
	CLOUD(4),
	REPORT(5),
	COMPANY(6),
    R<PERSON><PERSON>(60),
    USER(70),
	REQUEST(98),
	SYSTEM(99)
	;

	private final int code;

	CodeModel(int code) {
		scope(code);
		this.code = code;
	}

	public int code() {
		return code;
	}

	public static CodeModel valueOf(int code) {
		for (CodeModel model : CodeModel.values()) {
			if (model.code() == code) {
				return model;
			}
		}
		return null;
	}

	private void scope(int code) {
		if (code < 0 || code > 99) {
			throw new AssertionError("The code of CodeModel should great than -1 and lower than 100.");
		}
	}
}
