package com.bees360.internal.ai.common.exceptions;

import org.apache.http.HttpStatus;

/**
 * <AUTHOR>
 * @date 2018/08/08
 */
public class HttpRequestForBiddenException extends HttpRequestException {

    private static final int HTTP_STATUS = HttpStatus.SC_FORBIDDEN;
    private static final String MESSAGE = "The server understood the request but refuses to authorize it.";

    public HttpRequestForBiddenException() {
        super(HTTP_STATUS, MESSAGE);
    }

    public HttpRequestForBiddenException(String msg) {
        super(HTTP_STATUS, msg);
    }

    public HttpRequestForBiddenException(String msg, Throwable cause) {
        super(HTTP_STATUS, msg, cause);
    }
}
