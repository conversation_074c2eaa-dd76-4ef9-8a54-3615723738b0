package com.bees360.internal.ai.base;

/**
 * define response json format of http
 *
 * <AUTHOR>
 *
 */
public class ResponseJson {

	// response code 0:success; >0:other code message
	private String code;
	private String msg;
	private Object data;

	public static final String SUCCESS_CODE = "0";
	public static final String SUCCESS_MSG = "success";

//	default as a Succese response
	public ResponseJson() {
		this.code = ResponseJson.SUCCESS_CODE;
		this.msg = ResponseJson.SUCCESS_MSG;
		this.data = null;
	}

	public ResponseJson(Object data) {
		this.code = ResponseJson.SUCCESS_CODE;
		this.msg = ResponseJson.SUCCESS_MSG;
		this.data = data;
	}

	public ResponseJson(String code, String msg, Object data) {
		this.code = code;
		this.msg = msg;
		this.data = data;
	}

	public ResponseJson(String code, String msg) {
		this.code = code;
		this.msg = msg;
		this.data = null;
	}

	public boolean success() {
		return SUCCESS_CODE.equals(code);
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return "ResponseJson [code=" + code + ", msg=" + msg + ", data=" + data + "]";
	}
}
