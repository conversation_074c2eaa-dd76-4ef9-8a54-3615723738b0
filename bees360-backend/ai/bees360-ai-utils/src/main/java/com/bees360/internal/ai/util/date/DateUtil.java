package com.bees360.internal.ai.util.date;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

public class DateUtil {
    public static String DATE_FORMAT = "yyyy-MM-dd";
    public static String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
	public static String UTC_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

	public static Date convertDate(String dateStr, String format,
			TimeZone timezone) throws ParseException {
		DateFormat dateFormat = new SimpleDateFormat(format);
		dateFormat.setTimeZone(timezone);
		return dateFormat.parse(dateStr);
	}

    public static ZoneId getUSCentralZoneId() {
        return ZoneId.of("US/Central");
    }

    public static LocalDateTime getUSCentralNow() {
        return LocalDateTime.now(getUSCentralZoneId());
    }

    public static String convertDate(long timeMills, String format, ZoneId zoneId) {
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timeMills), zoneId);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return zonedDateTime.format(formatter);
    }
}
