package com.bees360.internal.ai.util.protobuf;

import com.bees360.internal.ai.exchange.common.AnyDataProto;

/**
 * <AUTHOR>
 * @date 2019/09/09 11:34
 */
public class AnyDataPacker {
    public static <T extends com.google.protobuf.Message> AnyDataProto.AnyData pack(T message) {
        return AnyDataProto.AnyData.newBuilder()
            .setTypeUrl(message.getDescriptorForType().getFullName())
            .setValue(message.toByteString())
            .build();
    }
}
