package com.bees360.internal.ai.util.protobuf;

import com.bees360.internal.ai.exchange.ai2client.ResponseProto;
import com.bees360.internal.ai.exchange.common.AnyDataProto;
import org.junit.jupiter.api.Test;

public class AnyDataPackerTest {

    private ResponseProto.ResponseBody getData() {
        return ResponseProto.ResponseBody.newBuilder()
            .setCode("1")
            .setMessage("this is message")
            .build();
    }

    @Test
    public void pack() {
        ResponseProto.ResponseBody data = getData();
        AnyDataProto.AnyData anyData = AnyDataPacker.pack(data);
    }
}
