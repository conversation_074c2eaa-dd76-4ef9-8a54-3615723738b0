<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.bees360.ai</groupId>
		<artifactId>bees360-ai</artifactId>
		<version>${revision}${changelist}</version>
	</parent>
	<artifactId>bees360-ai-utils</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.bees360.ai</groupId>
			<artifactId>bees360-ai-entity</artifactId>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.bees360.ai</groupId>
			<artifactId>bees360-ai-common</artifactId>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>

		<!-- State Machine Framework -->
		<dependency>
			<groupId>org.squirrelframework</groupId>
			<artifactId>squirrel-foundation</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
		</dependency>

		<dependency>
			<groupId>org.im4java</groupId>
			<artifactId>im4java</artifactId>
			<version>1.4.0</version>
		</dependency>
	</dependencies>

</project>
