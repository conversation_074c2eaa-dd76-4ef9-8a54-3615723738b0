<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bees360-bom</artifactId>
        <groupId>com.bees360</groupId>
        <version>${revision}${changelist}</version>
        <relativePath>../bom/pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bees360-parent</artifactId>
    <packaging>pom</packaging>
    <!-- version = ${project.parent.version}-->

    <name>bees360-parent</name>
    <url>https://gitlab.bees360.com/root/upstream</url>
    <description>The backend of Bees360</description>
    <inceptionYear>2017</inceptionYear>
    <scm>
        <url>https://gitlab.bees360.com/root/upstream</url>
        <connection>scm:git:**********************:root/upstream.git</connection>
        <developerConnection>scm:git:**********************:root/upstream.git</developerConnection>
    </scm>
    <issueManagement>
        <system>GitLab Issue Management</system>
        <url>https://gitlab.bees360.com/root/upstream/issues</url>
    </issueManagement>
    <ciManagement>
        <system>Gitlab CI/CD</system>
        <url>https://gitlab.bees360.com/root/upstream/pipelines</url>
    </ciManagement>

    <!-- TODO 添加版本控制等信息 -->
    <properties>
        <!-- plugin version -->
        <jetty-maven-plugin.version>7.2.2.v20101205</jetty-maven-plugin.version>
        <protobuf-maven-plugin.version>0.6.1</protobuf-maven-plugin.version>
        <spring-boot-maven-plugin.version>${spring-boot.version}</spring-boot-maven-plugin.version>
        <os-maven-plugin.version>1.6.2</os-maven-plugin.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jul</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-log4j2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <!-- TODO plugin的管理 -->
    <build>
        <extensions>
            <extension>
                <!-- 引入系统变量 -->
                <!-- https://mvnrepository.com/artifact/kr.motd.maven/os-maven-plugin -->
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
            </extension>
        </extensions>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>kr.motd.maven</groupId>
                    <artifactId>os-maven-plugin</artifactId>
                    <version>${os-maven-plugin.version}</version>
                </plugin>
                <!-- 配置加入jetty服务器，开发时我们一般使用jetty服务器 -->
                <plugin>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>jetty-maven-plugin</artifactId>
                    <version>${jetty-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.xolstice.maven.plugins</groupId>
                    <artifactId>protobuf-maven-plugin</artifactId>
                    <version>${protobuf-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot-maven-plugin.version}</version>
                    <configuration>
                        <excludeDevtools>true</excludeDevtools>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
