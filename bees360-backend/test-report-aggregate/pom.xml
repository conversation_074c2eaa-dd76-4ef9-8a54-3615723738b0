<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bees360-build</artifactId>
        <groupId>com.bees360</groupId>
        <version>${revision}${changelist}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>test-report-aggregate</artifactId>
    <description>test statistic</description>
    <dependencies>
        <!-- Sort alphabetically-->
        <!-- ai -->
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-entity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-event</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-grpc</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-grpc-proto</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-mapper</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-utils</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.ai</groupId>
            <artifactId>bees360-ai-web</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- common -->
        <dependency>
            <groupId>com.bees360.common</groupId>
            <artifactId>bees360-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- commons -->
        <dependency>
            <groupId>com.bees360.commons</groupId>
            <artifactId>elasticsearch-support</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.commons</groupId>
            <artifactId>firebase-support</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.commons</groupId>
            <artifactId>hover-support</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.commons</groupId>
            <artifactId>invoice</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.commons</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.commons</groupId>
            <artifactId>spring-support</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- report -->
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-report-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-report-entity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-report-mapper</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-report-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-report-spring-boot-start</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-report-web</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- schedule -->
        <dependency>
            <groupId>com.bees360.schedule</groupId>
            <artifactId>bees360-schedule</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- web -->
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-web-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-entity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-event</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-manager-project</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-mapper</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-utils</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-web</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-web-grpc</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-web-grpc-proto</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-web-grpc-service</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>report-aggregate</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
