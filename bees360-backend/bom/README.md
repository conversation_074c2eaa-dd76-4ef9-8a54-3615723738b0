Bill of Materials
---

写在前面
---

以下为该构件的使用说明。其中部分内容仅仅是个人微薄的经验，如有不合理的地方请务必指正，我必将非常的感激。

-- @guanrong.yang

使用
---

该模块用于统一管理项目各个模块所使用到的maven构件的版本。所有的模块应该`import`该模块，并使用缺失版本版本号的依赖。

如果需要覆盖`bees360-pom`中jar包的版本，可以显示声明maven构件的版本。

```xml
<project>
    ...
    <dependencyManagement>
        <dependencies>
            <!-- import:bees360-dependencies -->
            <dependency>
                <groupId>com.bees360</groupId>
                <artifactId>bees360-pom</artifactId>
                <version>${project.version}</version>
                <!-- <relativePath /> --> <!-- lookup parent from repository -->
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    ...
    <dependencies>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <!-- 覆盖bees360-pom中声明的版本 -->
            <version>5.5.13.1</version>
        </dependency>
    </dependencies>
</project>
```

目前，所有的项目都会通过`bees360-parent`项目间接继承该项目，除非您想指定其他的`parent`，否则无需再通过以上方式import`bees360-pom`。

该项目的修改
---

该项目只有`pom.xml`，修改该项目即修改该`pom.xml`。由于该`pom.xml`管理了所有的项目的maven构件版本，因而任何的改动都影响到所有项目。请务必`谨慎操作`。可为IDEA安装`Maven Helper`插件辅助依赖分析。

对该项目的任何修改，请务必作为独立的**独立的commit**提交到Gitlab。

### 添加新的构件

只要添加到该项目的构件，以后的修改都会非常的麻烦。添加之前请务必认真思考。

1. **新构件和现有构件的关系。** 尽量选择和现有构件想匹配的构件。比如使用SpringBoot相关的构件时，尽量使用和现有的SpringBoot版本相同的构件。
1. **新构件所依赖的构件。** 查看新构件所引入的构件和现有构件的版本是否相同或者尽量相近。比如使用非官方SpringBoot Starter的时候，请务必确认该Starter所使用的SpringBoot版本是否与现有SpringBoot版本相同。
1. **构件版本是否太低。** 在满足上面的条件的前提下，使用尽量新版本的构件。过低构件功能不够丰富，且会约束新的构件的添加。
1. **构件版本是否太高。** 与之相关的构件的开发可能不会使用最新版本的构件，这会约束开发可选择的构件。
1. **新构件所依赖的构件是否会被独立使用。** 比如引入`mybatis-spring-boot-starter`时，可单独引入该构件所依赖的版本的`mybatis`和`mybatis-spring`。
1. **用\<properties\>管理版本号。** 推荐名称为 `<artifactId.version>`。

TIPS：
- 可以在 [https://mvnrepository.com/](https://mvnrepository.com/) 中查找构件，并查看不同的版本的依赖和依赖版本。

添加的构件请尽量注释添加原因。

### 修改构件版本

除非您有把握不对项目产生不良影响，否则不要随意修改该项目中设定的maven构件的版本。如项目中需要使用与该项目设定的不同版本的构件，可在您的项目中进行覆盖或者排除。

1. **已使用该构件的项目。** 可以直接在IDEA中做全局文本搜索`artifactId`定位。
1. **版本修改的跨度**。如果修改`主版本号.次版本号.修订号`中的`主版本号`或者`次版本号`，请务必认真考虑可能的影响，特别是`主版本号`。
1. **修改版本一定程度也是添加新的构件。** 请尽量按照添加新构件的约束进行修改。

覆盖的方法
```xml
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>itextpdf</artifactId>
    <!-- 指定版本号，覆盖bees360-pom中声明的版本 -->
    <version>5.5.13.1</version>
</dependency>
```

排除的方法
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter</artifactId>
    <exclusions>
        <!-- 排除 -->
        <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

#### 删除构件

只要所有项目都没有使用该构件，且以后也不会使用到，则可以考虑删除该构件。
