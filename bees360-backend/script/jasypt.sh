#!/bin/bash

# ---------------------
# Jasypt加密解密脚本
# chmod +x jasypt.sh

# ----- macbook -----
# vim ～/.bash_profile
# alias jasypt="<你的路径>/upstream/bees360-backend/docs/jasypt.sh"
#
# ---------------------

defaultVersion="1.9.2"
dafaultPassword=${BEES360_SECRET_KEY}
defaultAlgorithm="PBEWithMD5AndDES"
encrypt="e"
decrpyt="d"
defaultEncrypt=${encrypt}

echo "encrypt($encrypt) or decrpyt($decrpyt)(default $defaultEncrypt):"
read crypt

echo "jasypt version(default $defaultVersion):"
read version

echo "input:"
read input

echo "password(default $dafaultPassword):"
read password

echo "algorithm(default $defaultAlgorithm):"
read algorithm

if [ -z ${version} ]; then
    version=${defaultVersion}
fi
if [ -z ${password} ]; then
    password=${dafaultPassword}
fi
if [ -z ${algorithm} ]; then
    algorithm=${defaultAlgorithm}
fi
if [ -z ${crypt} ]; then
    crypt=${defaultEncrypt}
fi

if [ "$crypt" = "$encrypt" ]; then
    main="org.jasypt.intf.cli.JasyptPBEStringEncryptionCLI"
elif [ "$crypt" = "$decrpyt" ]; then
    main="org.jasypt.intf.cli.JasyptPBEStringDecryptionCLI"
fi
echo $crypt

jasypt="$HOME/.m2/repository/org/jasypt/jasypt/$version/jasypt-$version.jar"

echo $main
echo $jasypt

java -cp $jasypt $main input=$input password=$password algorithm=$algorithm
