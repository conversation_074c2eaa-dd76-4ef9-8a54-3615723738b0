Bees360 Backend
===


这里是所有的bees360的后端项目。系统设计相关的内容请看[系统设计文档](./docs)。

快速开始
---

项目列表
---

如下为已有的项目列表的快速预览。

### build
作为编译构建项目，负责快速编译构建所有的项目。

### parent

所有的项目都应该继承自该项目。

### bom

构件版本管理项目。

### web

### ai

### 3d

### ad

### pc

### common

### data

参与开发
---

### 添加新的项目

1. 继承`bees360-parent`项目。
1. goupId设置为`com.bees360.项目名`，artifactId为`bees360-项目名`。
1. 子模块groupId与该项目同，artifactId为`项目名-模块名`。

### 工具


#### 字符串加密解密

请使用 `script/jasypt.sh` 脚本进行生成

更多请参考：

- [ulisesbocchio/jasypt-spring-boot](https://github.com/ulisesbocchio/jasypt-spring-boot)
