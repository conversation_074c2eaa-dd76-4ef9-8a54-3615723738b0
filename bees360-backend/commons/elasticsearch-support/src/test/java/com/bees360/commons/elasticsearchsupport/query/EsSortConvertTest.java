package com.bees360.commons.elasticsearchsupport.query;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;

import org.apache.commons.lang3.StringUtils;
import org.opensearch.search.sort.FieldSortBuilder;
import org.opensearch.search.sort.SortBuilder;
import org.opensearch.search.sort.SortBuilders;
import org.opensearch.search.sort.SortOrder;

/**
 * <AUTHOR> Yang
 */
public class EsSortConvertTest {

    @Test
    public void parse() {
        List<SortBuilder> sortBuilderList = EsSortConverter.parse("-time,name");
        for (SortBuilder sort : sortBuilderList) {
            assertTrue(sort instanceof FieldSortBuilder);
            FieldSortBuilder fieldSort = (FieldSortBuilder)sort;
            if (StringUtils.equals(fieldSort.getFieldName(), "time")) {
                assertEquals(SortOrder.DESC, fieldSort.order());
            } else {
                assertEquals("name", fieldSort.getFieldName());
                assertEquals(SortOrder.ASC, fieldSort.order());
            }
        }
    }

    @Test
    public void convert() {
        String sortQueries = "-time,name";
        List<SortBuilder> sortBuilderList = Arrays.asList(SortBuilders.fieldSort("time").order(SortOrder.DESC),
            SortBuilders.fieldSort("name").order(SortOrder.ASC));
        String result = EsSortConverter.toSortQueries(sortBuilderList);
        assertEquals(sortQueries, result);
    }

    @Test
    public void validate() {
        String[] shouldPass = {"name,time", "-name,time1", "name2,-time", "-name3,-time", "name,+time", "+name,+time",
            ",,,,name,,,,,-time,,,,,"};

        String[] shouldFail = {"name-time", "- name,time", "name,-,time", "--name,time", "++name,--time"};

        for (String pass : shouldPass) {
            assertTrue(EsSortConverter.validate(pass));
        }
        for (String fail : shouldFail) {
            assertFalse(EsSortConverter.validate(fail));
        }
    }
}
