package com.bees360.commons.elasticsearchsupport;

import static org.junit.jupiter.api.Assertions.*;

import com.bees360.commons.elasticsearchsupport.definition.DataModel;

import com.bees360.commons.elasticsearchsupport.definition.EsDocument;
import org.junit.jupiter.api.Test;

import lombok.Data;

/**
 * <AUTHOR>
 */
public class EsDocumentUtilTest {

    private static final String ID_VALUE = "123";
    private static final String INDEX_NAME = "book";
    private static final String ID_PREFIX = "book_id";

    @Test
    public void getId() {
        IdModel idObj = new IdModel();
        idObj.setId(ID_VALUE);
        String idValue = EsDocumentUtil.getId(idObj);

        assertEquals(ID_VALUE, idValue);
    }

    @Test
    public void getIdNull() {
        assertThrows(NullPointerException.class, () -> {
            IdModel idObj = new IdModel();
            idObj.setId(null);

            EsDocumentUtil.getId(idObj);
        });
    }

    @Test
    public void getIdNoDateModel() {
        assertThrows(IllegalArgumentException.class, () -> {
            FakeId idObj = new FakeId();
            idObj.setId(ID_VALUE);

            EsDocumentUtil.getId(idObj);
        });
    }

    @Test
    public void getEsDocumentDef() {
        EsDocumentDef def = EsDocumentUtil.getEsDocumentDef(IdModel.class);
        assertEquals(new EsDocumentDef(INDEX_NAME, "", ID_PREFIX), def);

        EsDocumentDef def2 = EsDocumentUtil.getEsDocumentDef(IdModel.class);
        assertTrue(def == def2);
    }

    @Test
    public void getIndexName() {
        String indexName = EsDocumentUtil.getIndexName(IdModel.class);
        assertEquals(indexName, INDEX_NAME);
    }

    @Test
    public void getEsId() {
        IdModel idObj = new IdModel();
        idObj.setId(ID_VALUE);

        String esId = EsDocumentUtil.getEsId(idObj);
        String esIdExpected = ID_PREFIX + "_" + ID_VALUE;

        assertEquals(esIdExpected, esId);
    }

    @Test
    public void getEsId2() {
        String esId = EsDocumentUtil.getEsId(ID_VALUE, IdModel.class);
        String esIdExpected = ID_PREFIX + "_" + ID_VALUE;

        assertEquals(esIdExpected, esId);
    }

    @Data
    @EsDocument(indexName = INDEX_NAME, idPrefix = ID_PREFIX)
    public static class IdModel implements DataModel<String> {
        private String id;

        @Override
        public String id() {
            return id;
        }
    }

    @Data
    @EsDocument(indexName = "id")
    public static class FakeId {
        private String id;
    }
}
