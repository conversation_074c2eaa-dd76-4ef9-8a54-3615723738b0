package com.bees360.commons.elasticsearchsupport.util;

import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.client.core.CountRequest;
import org.opensearch.index.query.QueryBuilder;
import org.opensearch.script.Script;
import org.opensearch.script.ScriptType;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.opensearch.search.sort.FieldSortBuilder;
import org.opensearch.search.sort.ScriptSortBuilder.ScriptSortType;
import org.opensearch.search.sort.SortBuilder;
import org.opensearch.search.sort.SortBuilders;
import org.opensearch.search.sort.SortOrder;

import com.bees360.commons.elasticsearchsupport.EsDocumentUtil;

/**
 * <AUTHOR> Yang
 */
public class ElasticSearchUtil {

    public static SortBuilder sortBuilder(String field, Class<?> datatype, String order,
        Supplier<SortBuilder> defaultSort) {
        if (StringUtils.isBlank(field)) {
            return defaultSort.get();
        }
        SortOrder sortOrder = SortOrder.DESC.name().equalsIgnoreCase(order) ? SortOrder.DESC : SortOrder.ASC;
        FieldSortBuilder fsb = new FieldSortBuilder(field).order(sortOrder);
        if (datatype != null) {
            fsb.unmappedType(convertToEsDataType(datatype));
        }
        return fsb;
    }

    public static SortBuilder scriptSortBuilder(String scriptCode, Map<String, Object> params, String order, ScriptSortType dataType) {
        Script script = new Script(ScriptType.INLINE, "painless", scriptCode, params);
        SortBuilder sb = SortBuilders.scriptSort(script, dataType);
        sb.order(SortOrder.DESC.name().equalsIgnoreCase(order) ? SortOrder.DESC : SortOrder.ASC);
        return sb;
    }

    public static SortBuilder sortBuilder(String field, Class<?> datatype, String order, SortBuilder defaultSort) {
        return sortBuilder(field, datatype, order, () -> defaultSort);
    }

    public static SearchRequest searchRequest(QueryBuilder queryBuilder, Class<?> esModel, int from, int size,
        SortBuilder... sorts) {
        SearchRequest request = new SearchRequest();
        request.indices(EsDocumentUtil.getIndexName(esModel));

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.trackTotalHits(true).query(queryBuilder).from(from).size(size);
        if (sorts != null) {
            for (SortBuilder sort : sorts) {
                if (sort == null) {
                    continue;
                }
                sourceBuilder.sort(sort);
            }
        }
        request.source(sourceBuilder);
        return request;
    }

    public static CountRequest countRequest(QueryBuilder queryBuilder, Class<?> esModel) {
        CountRequest request = new CountRequest();
        request.indices(EsDocumentUtil.getIndexName(esModel));

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(queryBuilder);
        request.source(sourceBuilder);
        return request;
    }

    public static String convertToEsDataType(Class<?> datatype) {
        if (String.class.isAssignableFrom(datatype)) {
            return "string";
        } else if (Integer.class.isAssignableFrom(datatype)) {
            return "integer";
        } else if (Long.class.isAssignableFrom(datatype)) {
            return "long";
        } else if (Float.class.isAssignableFrom(datatype)) {
            return "float";
        } else if (Double.class.isAssignableFrom(datatype)) {
            return "double";
        } else if (Boolean.class.isAssignableFrom(datatype)) {
            return "boolean";
        } else if (Date.class.isAssignableFrom(datatype)) {
            return "date";
        } else if (Instant.class.isAssignableFrom(datatype)) {
            return "date";
        }
        return null;
    }

}
