package com.bees360.commons.elasticsearchsupport;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.gson.GsonBuilder;
import org.opensearch.search.SearchHit;

import com.google.gson.Gson;

/**
 * <AUTHOR>
 */
public class EsModelGsonConverter implements EsModelConverter {

    private Gson gson;

    public EsModelGsonConverter() {
        this(new GsonBuilder().serializeNulls().create());
    }

    public EsModelGsonConverter(Gson gson) {
        this.gson = gson;
    }

    @Override
    public <T> List<T> toEsModelForcibly(SearchHit[] hits, Class<T> clazz) {
        return toEsModelForcibly(Arrays.asList(hits), clazz);
    }

    @Override
    public String toJson(Object esModel) {
        return gson.toJson(esModel);
    }

    @Override
    public <T> T toEsModel(String json, Class<T> clazz) {
        return gson.fromJson(json, clazz);
    }

    @Override
    public <T> List<T> toEsModel(SearchHit[] hits, Class<T> clazz) {
        return toEsModel(Arrays.asList(hits), clazz);
    }

    @Override
    public <T> List<T> toEsModel(SearchHit[] hits, String index, Class<T> clazz) {
        return toEsModel(Arrays.asList(hits), index, clazz);
    }

    @Override
    public <T> List<T> toEsModelForcibly(List<SearchHit> hits, Class<T> clazz) {
        return hits.stream()
            .map(hit -> gson.fromJson(hit.getSourceAsString(), clazz))
            .collect(Collectors.toList());
    }

    @Override
    public <T> List<T> toEsModel(List<SearchHit> hits, Class<T> clazz) {
        EsDocumentDef docDef = EsDocumentUtil.getEsDocumentDef(clazz);
        return toEsModel(hits, docDef.getIndexName(), clazz);
    }

    @Override
    public <T> List<T> toEsModel(List<SearchHit> hits, String index, Class<T> clazz) {
        return hits.stream()
            .map(hit -> gson.fromJson(hit.getSourceAsString(), clazz))
            .collect(Collectors.toList());
    }
}
