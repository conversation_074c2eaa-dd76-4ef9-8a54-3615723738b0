package com.bees360.commons.elasticsearchsupport.query;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.opensearch.search.sort.FieldSortBuilder;
import org.opensearch.search.sort.SortBuilder;
import org.opensearch.search.sort.SortBuilders;
import org.opensearch.search.sort.SortOrder;

/**
 * 将字符串转化为sort.
 *
 * 目前仅仅支持FieldSortBuilder.
 *
 * <AUTHOR>
 */
public class EsSortConverter {

    /**
     * 匹配：time,name | -time,name | +time,-name | ,,,time,+name,,,, 等, 不匹配：--time,name | time,+,name | time-name
     */
    private static final String SORT_QUERIES_REGEX = ",*([\\-\\+]?[a-zA-Z0-9]+),+([\\-\\+]?[a-zA-Z0-9]+)+,*";

    private static final String ORDER_PREFIX_ASC = "+";
    private static final String ORDER_PREFIX_DESC = "-";
    private static final String SORT_SEPARATOR = ",";

    public static List<SortBuilder> parse(String sortQueries) {
        if (!validate(sortQueries)) {
            throw new IllegalArgumentException(
                "The format of sortQueries is incorrect, which should look like [-time,name], [+time,name]");
        }
        String[] sortWords = StringUtils.split(sortQueries, SORT_SEPARATOR);
        List<SortBuilder> sortBuilders = Arrays.stream(sortWords).filter(StringUtils::isNotBlank)
            .map(w -> wordToSortBuilder(w)).collect(Collectors.toList());
        return sortBuilders;
    }

    private static SortBuilder wordToSortBuilder(String sortWord) {
        SortOrder order = SortOrder.ASC;
        String fieldName = sortWord;
        if (sortWord.startsWith(ORDER_PREFIX_DESC)) {
            order = SortOrder.DESC;
            fieldName = sortWord.substring(1);
        } else if (sortWord.startsWith(ORDER_PREFIX_ASC)) {
            order = SortOrder.ASC;
            fieldName = sortWord.substring(1);
        }
        return SortBuilders.fieldSort(fieldName).order(order).missing("_last");
    }

    public static String toSortQueries(List<SortBuilder> sortBuilders) {
        if (sortBuilders == null || sortBuilders.isEmpty()) {
            return "";
        }
        return sortBuilders.stream().map(EsSortConverter::sortBuilderToWord).collect(Collectors.joining(SORT_SEPARATOR));
    }

    private static String sortBuilderToWord(SortBuilder sortBuilder) {
        if (sortBuilder == null || !(sortBuilder instanceof FieldSortBuilder)) {
            return "";
        }
        FieldSortBuilder fieldSort = (FieldSortBuilder)sortBuilder;
        String prefix = orderToPrefix(fieldSort.order());
        String fieldName = fieldSort.getFieldName();
        return prefix + fieldName;
    }

    private static String orderToPrefix(SortOrder order) {
        return order == SortOrder.DESC ? ORDER_PREFIX_DESC : "";
    }

    public static boolean validate(String sortQueries) {
        return StringUtils.isEmpty(sortQueries) || sortQueries.matches(SORT_QUERIES_REGEX);
    }
}
