package com.bees360.commons.elasticsearchsupport;

import java.util.List;

import org.opensearch.search.SearchHit;

/**
 * <AUTHOR> Yang
 */
public interface EsModelConverter {

    <T> List<T> toEsModelForcibly(SearchHit[] hits, Class<T> clazz);

    String toJson(Object esModel);

    <T> T toEsModel(String json, Class<T> clazz);

    <T> List<T> toEsModel(SearchHit[] hits, Class<T> clazz);

    <T> List<T> toEsModel(SearchHit[] hits, String index, Class<T> clazz);

    <T> List<T> toEsModelForcibly(List<SearchHit> hits, Class<T> clazz);

    <T> List<T> toEsModel(List<SearchHit> hits, Class<T> clazz);

    <T> List<T> toEsModel(List<SearchHit> hits, String index, Class<T> clazz);
}
