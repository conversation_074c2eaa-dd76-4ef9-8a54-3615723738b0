package com.bees360.commons.elasticsearchsupport;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;

import com.bees360.commons.elasticsearchsupport.definition.DataModel;
import org.apache.commons.lang3.StringUtils;

import com.bees360.commons.elasticsearchsupport.definition.EsDocument;

/**
 * <AUTHOR> Yang
 */
public class EsDocumentUtil {

    private static ConcurrentHashMap<Class<?>, EsDocumentDef> docDefMap = new ConcurrentHashMap<>();

    private static final String ID_PREFIX_SEPARATOR = "_";
    /**
     * 定义Id 注解
     *
     * 如需要通过注解定义id，可以给该字段赋值
      */
    private static final Class<? extends Annotation> ID_ANNOTATION = null;

    public static EsDocumentDef getEsDocumentDef(Class<?> clazz) {
        if (docDefMap.containsKey(clazz)) {
            return docDefMap.get(clazz);
        }
        EsDocumentDef docDef = getDocDef(clazz);
        if (StringUtils.isBlank(docDef.getIndexName())) {
            throw new IllegalArgumentException("Index name of class " + clazz.getName() + " is required.");
        }
        docDefMap.put(clazz, docDef);
        return docDef;
    }

    private static EsDocumentDef getDocDef(Class<?> clazz) {
        if (!clazz.isAnnotationPresent(EsDocument.class)) {
            throw new IllegalArgumentException("Annotation @EsDocument is required for Class " + clazz.getName());
        }
        EsDocument esDocument = clazz.getAnnotation(EsDocument.class);
        return new EsDocumentDef(esDocument.indexName(), esDocument.alias(), esDocument.idPrefix());
    }

    public static String getIndexName(Class<?> clazz) {
        EsDocumentDef docDef = getDocDef(clazz);
        if (StringUtils.isNotBlank(docDef.getAlias())) {
            return docDef.getAlias();
        }
        return docDef.getIndexName();
    }

    public static String getId(Object esModel) {
        String id = getIdFromInterface(esModel);
        if(id == null) {
            id = getIdFromIdAnnotation(esModel);
        }
        if(id == null) {
            throw new IllegalArgumentException("Id haven't been defined.");
        }
        return id;
    }

    private static String getIdFromIdAnnotation(Object esModel) {
        if(ID_ANNOTATION == null) {
            return null;
        }
        String id = getIdFromField(esModel, ID_ANNOTATION);
        if(id == null) {
            id = getIdFromMethod(esModel, ID_ANNOTATION);
        }
        return id;
    }

    private static String getIdFromField(Object esModel, Class<? extends Annotation> idAnnotationClass) {
        Field[] fields = esModel.getClass().getDeclaredFields();
        Field idField = null;
        for (Field field : fields) {
            if (field.isAnnotationPresent(idAnnotationClass)) {
                if (idField != null) {
                    throw new IllegalArgumentException(
                        "There are more than one id in class " + esModel.getClass().getName());
                }
                idField = field;
            }
        }
        if(idField == null) {
            return null;
        }
        try {
            idField.setAccessible(true);
            Object idValue = idField.get(esModel);
            if(idValue == null) {
                throw new NullPointerException("The value of id can't be null.");
            }
            return String.valueOf(idValue);
        } catch (IllegalAccessException e) {
            throw new IllegalArgumentException(
                "Unable to get value of " + esModel.getClass().getSimpleName() + "#" + idField.getName());
        }
    }

    private static String getIdFromMethod(Object esModel, Class<? extends Annotation> idAnnotationClass) {
        Method[] methods = esModel.getClass().getMethods();
        Method idMethod = null;
        for(Method method: methods) {
            if(method.isAnnotationPresent(idAnnotationClass)) {
                if(idMethod != null) {
                    throw new IllegalArgumentException(
                        "There are more than one id in class " + esModel.getClass().getName());
                }
                idMethod = method;
            }
        }
        if(idMethod == null) {
            return null;
        }
        try {
            Object idValue = idMethod.invoke(esModel);
            if(idValue == null) {
                throw new NullPointerException("The value of id can't be null.");
            }
            return String.valueOf(idValue);
        } catch (ReflectiveOperationException e) {
            throw new IllegalArgumentException(
                "Unable to get value of " + esModel.getClass().getSimpleName() + "#" + idMethod.getName());
        }
    }

    private static String getIdFromInterface(Object esModel) {
        if(esModel instanceof DataModel model) {
            DataModel dataModel = model;
            if(dataModel.id() == null) {
                throw new NullPointerException("The value of id can't be null.");
            }
            String id = String.valueOf(dataModel.id());
            return id;
        }
        return null;
    }

    public static String getEsId(Object esModel) {
        return getEsId(getId(esModel), esModel.getClass());
    }

    public static String getEsId(String id, Class<?> clazz) {
        EsDocumentDef docDef = EsDocumentUtil.getEsDocumentDef(clazz);
        if (StringUtils.isBlank(docDef.getIdPrefix())) {
            return id;
        }
        return docDef.getIdPrefix() + ID_PREFIX_SEPARATOR + id;
    }
}
