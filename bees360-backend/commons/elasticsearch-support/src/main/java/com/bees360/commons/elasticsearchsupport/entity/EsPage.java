package com.bees360.commons.elasticsearchsupport.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Yang
 */
public class EsPage<T> {
    private List<T> items;
    private long total;
    private int from;
    private int size;

    public static <T> EsPage<T> emptyPage(int from, int size) {
        EsPage<T> esPage = new EsPage<>();
        esPage.setItems(new ArrayList<>());
        esPage.setTotal(0);
        esPage.setFrom(from);
        esPage.setSize(size);
        return esPage;
    }

    public static <T> EsPage<T> newPageFrom(List<T> items, int from, int size, long total) {
        EsPage<T> esPage = new EsPage<>();
        esPage.setItems(items);
        esPage.setTotal(total);
        esPage.setFrom(from);
        esPage.setSize(size);
        return esPage;
    }

    public List<T> getItems() {
        return items;
    }

    public void setItems(List<T> items) {
        this.items = items;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public int getFrom() {
        return from;
    }

    public void setFrom(int from) {
        this.from = from;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getItemSize() {
        return items.size();
    }
}
