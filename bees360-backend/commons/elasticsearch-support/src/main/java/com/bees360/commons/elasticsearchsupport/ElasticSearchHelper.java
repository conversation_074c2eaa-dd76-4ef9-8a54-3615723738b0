package com.bees360.commons.elasticsearchsupport;

import com.bees360.commons.elasticsearchsupport.entity.EsPage;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.OpenSearchStatusException;
import org.opensearch.action.DocWriteResponse;
import org.opensearch.action.delete.DeleteRequest;
import org.opensearch.action.delete.DeleteResponse;
import org.opensearch.action.get.GetRequest;
import org.opensearch.action.get.GetResponse;
import org.opensearch.action.get.MultiGetItemResponse;
import org.opensearch.action.get.MultiGetRequest;
import org.opensearch.action.get.MultiGetResponse;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.action.support.IndicesOptions;
import org.opensearch.action.support.WriteRequest;
import org.opensearch.action.update.UpdateRequest;
import org.opensearch.action.update.UpdateResponse;
import org.opensearch.client.HttpAsyncResponseConsumerFactory;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.client.core.CountRequest;
import org.opensearch.client.core.CountResponse;
import org.opensearch.client.indices.GetIndexRequest;
import org.opensearch.common.xcontent.XContentType;
import org.opensearch.core.rest.RestStatus;
import org.opensearch.index.engine.DocumentMissingException;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yang
 */
@Slf4j
public class ElasticSearchHelper {

    private final RestHighLevelClient restClient;
    private final RequestOptions requestOptions;
    private EsModelConverter converter;

    private final EsModelConverter partialFieldConverter = new EsModelGsonConverter(new Gson());
    private boolean allowUpsert = false;

    private final static Integer RETRY_COUNT = 5;

    public ElasticSearchHelper(RestHighLevelClient restClient) {
        this(restClient, getRequestOptions());
    }

    public ElasticSearchHelper(RestHighLevelClient restClient, RequestOptions requestOptions) {
        this(restClient, requestOptions, new EsModelGsonConverter());
    }

    public ElasticSearchHelper(RestHighLevelClient restClient, RequestOptions requestOptions,
        EsModelConverter converter) {
        this.restClient = restClient;
        this.requestOptions = requestOptions;
        this.converter = converter;
    }

    public void setConverter(EsModelConverter converter) {
        this.converter = converter;
    }

    public void setAllowUpsert(boolean allowUpsert) {
        this.allowUpsert = allowUpsert;
    }

    public int update(Object esModel) {
        String indexName = EsDocumentUtil.getIndexName(esModel.getClass());
        UpdateRequest updateRequest = new UpdateRequest();
        String id = EsDocumentUtil.getEsId(esModel);
        String jsonDoc = converter.toJson(esModel);
        updateRequest.index(indexName);
        updateRequest.id(id);
        updateRequest.doc(jsonDoc, XContentType.JSON);
        updateRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        updateRequest.retryOnConflict(RETRY_COUNT);
        if(allowUpsert) {
            updateRequest.upsert(jsonDoc, XContentType.JSON);
        }
        UpdateResponse response = null;
        try {
            response = restClient.update(updateRequest, requestOptions);
            // 如果index不存在，会抛出IndexNotFoundException，这里不做处理
        } catch(DocumentMissingException ex) {
            // 文档不存在
            return 0;
        } catch (IOException e) {
            throw new EsAccessException(e);
        }
        if(DocWriteResponse.Result.NOOP.equals(response.getResult())) {
            // 数据没有发生变化，但操作是成功的
            return 1;
        }
        return 1;
    }

    public int updatePartial(Object esModel) {
        String indexName = EsDocumentUtil.getIndexName(esModel.getClass());
        UpdateRequest updateRequest = new UpdateRequest();
        String id = EsDocumentUtil.getEsId(esModel);
        String jsonDoc =partialFieldConverter.toJson(esModel);
        updateRequest.index(indexName);
        updateRequest.id(id);
        updateRequest.doc(jsonDoc, XContentType.JSON);
        updateRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        updateRequest.retryOnConflict(RETRY_COUNT);
        try {
            restClient.update(updateRequest, requestOptions);
        } catch (DocumentMissingException ex) {
            // 文档不存在
            log.warn("The document with docId {} does not exist in the ES database.", id);
            return 0;
        } catch (OpenSearchStatusException ex) {
            if (RestStatus.NOT_FOUND.equals(ex.status())) {
                // 文档找不到
                log.warn("The document with docId {} does not exist in the ES database. error message: {}", id, ex.getMessage());
                return 0;
            }
            throw new EsAccessException(ex);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
        return 1;
    }


    public int deleteById(Object esModel) {
        String id = EsDocumentUtil.getId(esModel);
        return deleteById(id, esModel.getClass());
    }

    public int deleteById(String id, Class<?> clazz) {
        String indexName = EsDocumentUtil.getIndexName(clazz);
        String esId = EsDocumentUtil.getEsId(id, clazz);
        DeleteRequest deleteRequest = new DeleteRequest(indexName, esId);
        deleteRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        DeleteResponse response = null;
        try {
            response = restClient.delete(deleteRequest, requestOptions);
        } catch (IOException e) {
            throw new EsAccessException(e);
        }
        if(DocWriteResponse.Result.NOT_FOUND.equals(response.getResult())) {
            return 0;
        }
        return 1;
    }

    public <T> Optional<T> getById(String id, Class<T> clazz) {
        String indexName = EsDocumentUtil.getIndexName(clazz);
        String esId = EsDocumentUtil.getEsId(id, clazz);
        if (!indexExist(indexName)) {
            return Optional.empty();
        }
        GetRequest request = new GetRequest(indexName, esId);
        GetResponse response = null;
        try {
            response = restClient.get(request, requestOptions);
        } catch (IOException e) {
            throw new EsAccessException(e);
        }
        if(!response.isExists()) {
            return Optional.empty();
        }
        return Optional.of(converter.toEsModel(response.getSourceAsString(), clazz));
    }

    public boolean indexExist(Class<?> esModel) {
        return indexExist(EsDocumentUtil.getIndexName(esModel));
    }

    public boolean indexExist(String indexName) {
        GetIndexRequest request = new GetIndexRequest(indexName);
        try {
            boolean existing = restClient.indices().exists(request, requestOptions);
            return existing;
        } catch (IOException e) {
            throw new EsAccessException(e);
        }
    }

    public <T> EsPage<T> search(SearchRequest searchRequest, Class<T> clazz) {
        log.debug("searchRequest:{}", new Gson().toJson(searchRequest));
        searchRequest.indicesOptions(IndicesOptions.LENIENT_EXPAND_OPEN);
        if(searchRequest.indices().length == 0) {
            searchRequest.indices(EsDocumentUtil.getIndexName(clazz));
        }
        SearchResponse response = null;
        try {
            response = restClient.search(searchRequest, requestOptions);
        } catch (IOException e) {
            throw new EsAccessException(e);
        }
        int from = searchRequest.source().from();
        int size = searchRequest.source().size();
        long totalHits = response.getHits().getTotalHits().value;

        List<T> items = converter.toEsModel(response.getHits().getHits(), clazz);
        return EsPage.newPageFrom(items, from, size, totalHits);
    }

    public <T> long count(CountRequest searchRequest, Class<T> clazz) {
        searchRequest.indicesOptions(IndicesOptions.LENIENT_EXPAND_OPEN);
        if(searchRequest.indices().length == 0) {
            searchRequest.indices(EsDocumentUtil.getIndexName(clazz));
        }
        CountResponse response = null;
        try {
            response = restClient.count(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new EsAccessException(e);
        }
        return response.getCount();
    }

    public <T> List<T> searchListResult(SearchRequest searchRequest, Class<T> clazz) {
        log.debug("searchListResult searchRequest:{}", new Gson().toJson(searchRequest));
        searchRequest.indicesOptions(IndicesOptions.LENIENT_EXPAND_OPEN);
        if(searchRequest.indices().length == 0) {
            searchRequest.indices(EsDocumentUtil.getIndexName(clazz));
        }
        SearchResponse response = null;
        try {
            response = restClient.search(searchRequest, requestOptions);
        } catch (IOException e) {
            throw new EsAccessException(e);
        }

        return converter.toEsModel(response.getHits().getHits(), clazz);
    }

    public <T> List<T> searchListResultById(List<String> ids, Class<T> clazz) {
        if (Objects.isNull(ids) || Objects.equals(ids.size(), 0)) {
            return Collections.emptyList();
        }
        String indexName = EsDocumentUtil.getIndexName(clazz);
        List<String> indexIds = ids.stream().map(o -> EsDocumentUtil.getEsId(o, clazz)).collect(Collectors.toList());
        MultiGetRequest request = new MultiGetRequest();
        indexIds.forEach(id -> request.add(indexName, id));
        MultiGetResponse response;

        try {
            response = restClient.mget(request, requestOptions);
        } catch (IOException e) {
            throw new EsAccessException(e);
        }
        List<T> result = new ArrayList<>();
        for (MultiGetItemResponse res : response.getResponses()) {
            if (!res.isFailed() && res.getResponse().isExists()) {
                T model = converter.toEsModel(res.getResponse().getSourceAsString(), clazz);
                result.add(model);
            }
        }
        return result;
    }

    public boolean partiallyUpdate(Map<String, Object> updateMap, String id, Class<?> clazz) {
        String indexName = EsDocumentUtil.getIndexName(clazz);
        String esId = EsDocumentUtil.getEsId(id, clazz);
        UpdateRequest updateRequest = new UpdateRequest();
        updateRequest.index(indexName);
        updateRequest.id(esId);
        var mapJson = partialFieldConverter.toJson(updateMap);
        updateRequest.doc(mapJson,XContentType.JSON);
        updateRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        updateRequest.retryOnConflict(RETRY_COUNT);
        UpdateResponse response = null;
        try {
            response = restClient.update(updateRequest, requestOptions);
            // 如果index不存在，会抛出IndexNotFoundException，这里不做处理
        } catch (DocumentMissingException ex) {
            // 文档不存在
            return false;
        } catch (IOException e) {
            throw new EsAccessException(e);
        }
        if (DocWriteResponse.Result.NOOP.equals(response.getResult())) {
            // 数据没有发生变化，但操作是成功的
            return true;
        }
        return true;
    }

    private static RequestOptions getRequestOptions() {
        RequestOptions.Builder builder = RequestOptions.DEFAULT.toBuilder();
        // 修改为500MB
        builder.setHttpAsyncResponseConsumerFactory(
            new HttpAsyncResponseConsumerFactory
                .HeapBufferedResponseConsumerFactory(500 * 1024 * 1024));
        return builder.build();
    }
}
