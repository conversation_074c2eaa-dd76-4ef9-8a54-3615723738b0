# ElasticSearch Support

## 定义模型

```java
import com.bees360.commons.elasticsearchsupport.definition.*;

@Data
@EsDocument(indexName = "book", idPrefix = "isbn")
public class Book implements DataModel<String> {

    private String isbn;
    private String name;

    @Override
    private String id() {
        return isbn;
    }
}
```

## 配置和使用

### 配置

```java
@Configuration
public class EsConfig {

    @Bean
    public elasticSearchHelper ElasticSearchHelper(RestHighLevelClient restClient) {
        // RestHighLevelClient 需要另外注入
        return new ElasticSearchHelper(restClient);
    }
}
```

### 使用

- `EsDocumentUtil`: 获取index和解析id
- `ElasticSearchHelper`: 和es进行交互，CURD 基本操作
- `ElasticSearchUtil`: 各种常用的方法
