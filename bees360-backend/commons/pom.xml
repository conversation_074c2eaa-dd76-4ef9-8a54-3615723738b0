<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bees360</groupId>
        <artifactId>bees360-parent</artifactId>
        <version>${revision}${changelist}</version>
        <relativePath>../parent/pom.xml</relativePath>
    </parent>
    <groupId>com.bees360.commons</groupId>
    <artifactId>bees360-commons</artifactId>
    <packaging>pom</packaging>

    <name>commons</name>

    <modules>
        <module>lang</module>
        <module>spring-support</module>
        <module>elasticsearch-support</module>
        <module>firebase-support</module>
        <module>hover-support</module>
        <module>invoice</module>
    </modules>
</project>
