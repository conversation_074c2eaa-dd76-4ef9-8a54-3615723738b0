package com.bees360.commons.invoice;

import java.math.BigDecimal;
import java.util.ArrayList;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 账单项
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class InvoiceItem {
    /**
     * 项目名称
     */
    private String name;
    /**
     * 项目描述
     */
    private String description;
    /**
     * 单价
     */
    private BigDecimal price;
    private Integer quantity;
    /**
     * 总价, 为 price * quantity
     */
    private BigDecimal amount;
    /**
     * 需要收的税
     */
    private Iterable<Tax> tax = new ArrayList<>();
}
