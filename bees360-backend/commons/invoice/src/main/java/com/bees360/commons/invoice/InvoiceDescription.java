package com.bees360.commons.invoice;

import java.math.BigDecimal;
import java.util.Map;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 账单内容描述
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class InvoiceDescription {
    /**
     * 账单标题
     */
    private String title;
    /**
     * 发出账单的客户
     */
    private Customer from;
    /**
     * 收到账单，并为账单支付的客户
     */
    private Customer to;
    /**
     * 账单项列表
     */
    private Iterable<InvoiceItem> items;
    /**
     * 各类税统计
     */
    private Iterable<TaxSubtotal> taxSubtotal;
    /**
     * 商品总额（不含税）
     */
    private BigDecimal subtotal;
    /**
     * 账单总额
     */
    private BigDecimal amount;
    /**
     * 自定义信息
     */
    private Map<String, String> addition;
    /**
     * 货币符号
     */
    private String currency;
}
