package com.bees360.commons.springsupport.property;

import java.io.IOException;
import java.util.Properties;

import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.support.DefaultPropertySourceFactory;
import org.springframework.core.io.support.EncodedResource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 同时支持.properties文件和.yml,.yaml文件的解析。默认的{@code PropertySource}不支持.yml和.yaml文件。
 * <pre>
 *     @PropertySource(value = "classpath:settings.yaml", factory = MixPropertySourceFactory.class)
 * </pre>
 * <AUTHOR> <PERSON>
 * @date 2019/12/06 00:18
 */
@Slf4j
public class MixPropertySourceFactory extends DefaultPropertySourceFactory {

    private static final String YML = ".yml";
    private static final String YAML = ".yaml";

    @Override
    public PropertySource<?> createPropertySource(String name, EncodedResource resource) throws IOException {
        String sourceName = name != null ? name : resource.getResource().getFilename();
        log.info("Create property source from [" + sourceName + "]");

        Properties properties = loadProperties(name, resource);
        return new PropertiesPropertySource(sourceName, properties);
    }

    protected Properties loadProperties(String name, EncodedResource resource) throws IOException {
        String sourceName = name != null ? name : resource.getResource().getFilename();

        if (!resource.getResource().exists()) {
            return new Properties();
        } else if (sourceName.endsWith(YML) || sourceName.endsWith(YAML)) {
            return loadYml(resource);
        } else {
           return PropertiesLoaderUtils.loadProperties(resource.getResource());
        }
    }

    private Properties loadYml(EncodedResource resource) throws IOException {
        YamlPropertiesFactoryBean factory = new YamlPropertiesFactoryBean();
        factory.setResources(resource.getResource());
        factory.afterPropertiesSet();
        return factory.getObject();
    }
}
