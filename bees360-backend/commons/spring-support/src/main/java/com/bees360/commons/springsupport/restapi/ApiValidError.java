package com.bees360.commons.springsupport.restapi;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ApiValidError {
	/**
	 * @see ApiValidErrorType
	 */
	private String type;
	/**
	 * 校验不通过的属性名
	 */
	private String field;
	/**
	 * 具体的错误提示
	 */
	private String message;

	public ApiValidError(String type, String field, String message) {
		this.type = type;
		this.field = field;
		this.message = message;
	}
}
