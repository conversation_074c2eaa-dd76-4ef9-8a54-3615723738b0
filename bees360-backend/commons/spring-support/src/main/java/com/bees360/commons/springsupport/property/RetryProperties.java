package com.bees360.commons.springsupport.property;

import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class RetryProperties {
    /** Max attempt times */
    private int maxAttempts;
    /** Time unit of initial interval is microsecond */
    private long initialInterval;
    /** Retry interval multiplier */
    private double multiplier;
    /** Time unit of max interval is microsecond */
    private long maxInterval;
    /** retry exception */
    @NonNull private List<Class<? extends Throwable>> include;
}
