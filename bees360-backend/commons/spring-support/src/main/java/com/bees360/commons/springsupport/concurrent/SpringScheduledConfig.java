package com.bees360.commons.springsupport.concurrent;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.Optional;

@Configuration
@EnableConfigurationProperties
@EnableScheduling
public class SpringScheduledConfig {

    @Bean
    @ConfigurationProperties(prefix = "spring-customized.scheduled.executor")
    ExecutorProperties schedulerExecutorProperties() {
        return new ExecutorProperties()
            .setCorePoolSize(20)
            .setThreadNamePrefixSet("Scheduled-")
            .setWaitForTasksToCompleteOnShutdown(true)
            .setAwaitTerminationSeconds(15);
    }

    @Bean
    TaskScheduler taskScheduler(ExecutorProperties schedulerExecutorProperties) {
        var scheduler = new ThreadPoolTaskScheduler();
        // 设置核心线程数
        Optional.ofNullable(schedulerExecutorProperties.getCorePoolSize()).ifPresent(scheduler::setPoolSize);
        // 设置默认线程名称
        if (StringUtils.isNotEmpty(schedulerExecutorProperties.getThreadNamePrefixSet())) {
            scheduler.setThreadNamePrefix(schedulerExecutorProperties.getThreadNamePrefixSet());
        }
        // 等待所有任务结束后再关闭线程池
        Optional.ofNullable(schedulerExecutorProperties.getWaitForTasksToCompleteOnShutdown())
            .ifPresent(scheduler::setWaitForTasksToCompleteOnShutdown);
        Optional.ofNullable(schedulerExecutorProperties.getAwaitTerminationSeconds())
            .ifPresent(scheduler::setAwaitTerminationSeconds);

        Optional.ofNullable(schedulerExecutorProperties.getRejectedExecutionHandler())
            .ifPresent(scheduler::setRejectedExecutionHandler);
        return scheduler;
    }
}
