package com.bees360.commons.springsupport.restapi.exception;

import com.bees360.commons.springsupport.restapi.entity.ResourceInfo;

/**
 * <AUTHOR>
 */
public class ResourceNotFoundApiException extends ApiException {

    private ResourceInfo resourceInfo;

    public ResourceNotFoundApiException() {
        super();
    }

    public ResourceNotFoundApiException(String message) {
        super(message);
    }

    public ResourceNotFoundApiException(String message, Throwable cause) {
        super(message, cause);
    }

    public ResourceNotFoundApiException(Throwable cause) {
        super(cause);
    }

    public ResourceNotFoundApiException(ResourceInfo resourceInfo) {
        super();
        this.resourceInfo = resourceInfo;
    }

    public ResourceNotFoundApiException(ResourceInfo resourceInfo, String message) {
        super(message);
        this.resourceInfo = resourceInfo;
    }

    public ResourceNotFoundApiException(ResourceInfo resourceInfo, String message, Throwable cause) {
        super(message, cause);
        this.resourceInfo = resourceInfo;
    }

    public ResourceNotFoundApiException(ResourceInfo resourceInfo, Throwable cause) {
        super(cause);
        this.resourceInfo = resourceInfo;
    }

    public ResourceInfo getResourceInfo() {
        return resourceInfo;
    }
}
