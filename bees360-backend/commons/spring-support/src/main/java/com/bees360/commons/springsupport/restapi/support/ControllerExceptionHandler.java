package com.bees360.commons.springsupport.restapi.support;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.ElementKind;
import jakarta.validation.Path;

import com.bees360.commons.springsupport.restapi.ApiError;
import com.bees360.commons.springsupport.restapi.ApiValidError;
import com.bees360.commons.springsupport.restapi.ApiValidErrorType;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.exc.MismatchedInputException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * <AUTHOR> Guanrong
 */
public class ControllerExceptionHandler {

    private static final String logExceptionFormat = "[EXIGENCE] Some thing wrong with the system: %s, %s";
    private Logger logger = LoggerFactory.getLogger(ControllerExceptionHandler.class);

    // =========================================
    //Data verification

    /**
     * MethodArgumentNotValidException: Entity class attribute validation failed
     *
     * e.g. listUsersValid(@RequestBody @Valid UserFilterOption option)
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiError handleMethodArgumentNotValid(HttpServletRequest request, MethodArgumentNotValidException ex) {
        logger.debug(ex.getMessage(), ex);
        List<ApiValidError> apiValidErrors = validatorErrors(ex.getBindingResult(), ex);
        return validationFailed(apiValidErrors);
    }

    @ExceptionHandler(MismatchedInputException.class)
    public ApiError handleMismatchedInputException(HttpServletRequest request, MismatchedInputException ex) {
        logger.debug(ex.getMessage(), ex);

        ApiValidErrorType apiValidErrorType = ApiValidErrorType.TYPE_MISMATCH;

        String fieldName = refPathToFieldName(ex.getPath());
        String message = "The type of parameter '" + fieldName + "' mismatch.";

        ApiValidError error = new ApiValidError(apiValidErrorType.getType(), fieldName, message);

        return ApiError.invalidArgument(new ArrayList<>(Arrays.asList(error)));
    }

    private String refPathToFieldName(List<JsonMappingException.Reference> fieldRefs) {
        StringBuilder fieldName = new StringBuilder();
        fieldRefs.stream().forEach(ref -> refPathToFieldName(ref, fieldName));
        return StringUtils.removeStart(fieldName.toString(), ".");
    }

    private void refPathToFieldName(JsonMappingException.Reference fieldRef, StringBuilder fieldName) {
        if (fieldRef.getIndex() > -1) {
            fieldName.append("[" + fieldRef.getIndex() + "]");
        } else {
            fieldName.append(".").append(fieldRef.getFieldName());
        }
    }

    private ApiError validationFailed(List<ApiValidError> apiValidErrors) {
        ApiError apiError = ApiError.invalidArgument(apiValidErrors);
        return apiError;
    }

    private List<ApiValidError> validatorErrors(BindingResult result, Exception ex) {
        List<ApiValidError> apiValidErrors = new ArrayList<>();
        for (FieldError error : result.getFieldErrors()) {
            apiValidErrors.add(toFieldNotValidError(result, error, ex));
        }
        // Here you can expand the data verification of the interface
        return apiValidErrors;
    }

    /**
     * ConstraintViolationException: Directly check the method parameters, and the check fails.
     *
     * e.g. pageUsers(@RequestParam @Min(1)int pageIndex, @RequestParam @Max(100)int pageSize)
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ApiError handleConstraintViolationException(HttpServletRequest request, ConstraintViolationException ex) {
        List<ApiValidError> apiValidErrors = validErrors(ex);
        // Here you can expand the data verification of the interface
        return validationFailed(apiValidErrors);
    }

    /**
     * BindException: Data binding exception
     *
     * The effect is similar to MethodArgumentNotValidException, and it is the parent class of MethodArgumentNotValidException
     */
    @ExceptionHandler(BindException.class)
    public ApiError handleBindException(HttpServletRequest request, BindException ex) {
        logger.debug(ex.getMessage(), ex);
        List<ApiValidError> apiValidErrors = validatorErrors(ex.getBindingResult(), ex);
        return validationFailed(apiValidErrors);
    }

    /**
     * Parameter type mismatch
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ApiError methodArgumentTypeMismatchExceptionHandler(HttpServletRequest request,
        MethodArgumentTypeMismatchException ex) {
        logger.debug(ex.getMessage(), ex);
        String message = "The parameter '" + ex.getName() + "' should of type '"
            + ex.getRequiredType().getSimpleName().toLowerCase() + "'";
        ApiValidError apiValidError = new ApiValidError(ApiValidErrorType.TYPE_MISMATCH.getType(), ex.getName(), message);
        // Here you can expand the data verification of the interface
        return validationFailed(Arrays.asList(apiValidError));
    }

    /**
     * Required field is missing
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ApiError exceptionHandle(HttpServletRequest request, MissingServletRequestParameterException ex) {
        logger.debug(ex.getMessage(), ex);
        String message = "Required parameter '" + ex.getParameterName() + "' is not present";
        ApiValidError
            apiValidError = new ApiValidError(ApiValidErrorType.MISSING_FIELD.getType(), ex.getParameterName(), message);
        // Here you can expand the data verification of the interface
        return validationFailed(Arrays.asList(apiValidError));
    }

    private ApiValidError toFieldNotValidError(ConstraintViolation<?> constraintViolation) {
        int paramIndex = 2;
        StringBuilder fieldNameBuilder = new StringBuilder();
        String paramName = "";
        for (Path.Node node : constraintViolation.getPropertyPath()) {
            if(paramIndex > 0) {
                paramIndex --;
                if(paramIndex == 0) {
                    paramName = node.getName();
                }
                continue;
            }
            if(node.isInIterable()) {
                if(node.getIndex() != null) {
                    fieldNameBuilder.append("[" + node.getIndex() + "]");
                } else {
                    String suffix = "";
                    if(node.getName().equals("<map key>")) {
                        suffix = "<K>";
                    }
                    fieldNameBuilder.append("[" + node.getKey() + "]" + suffix);
                }
            }
            if(ElementKind.CONTAINER_ELEMENT.equals(node.getKind())) {
                // Just ignore
                continue;
            }
            fieldNameBuilder.append("." + node.getName());
        }
        String fieldName = fieldNameBuilder.length() == 0? paramName: fieldNameBuilder.toString();

        String message = constraintViolation.getMessage();
        return new ApiValidError(ApiValidErrorType.INVALID.getType(), fieldName, message);
    }

    private ApiValidError toFieldNotValidError(BindingResult result, FieldError error, Exception ex) {

        ApiValidErrorType apiValidErrorType = ApiValidErrorType.INVALID;

        String message;
        if ("typeMismatch".equals(error.getCode())) {
            message = "The parameter '" + error.getField() + "' should of type '"
                + result.getFieldType(error.getField()).getSimpleName().toLowerCase() + "'";
            apiValidErrorType = ApiValidErrorType.TYPE_MISMATCH;
        } else {
            message = error.getDefaultMessage();
        }
        String fieldName = error.getField();
        if(fieldName.endsWith("[]")) {
            fieldName = fieldName.substring(0, fieldName.length() - 2);
            fieldName = fieldName + "[" + error.getRejectedValue() + "]";
        }
        // Iterator<Path.Node> nodeIterator = error.
        // String fieldName = createFieldName();
        return new ApiValidError(apiValidErrorType.getType(), fieldName, message);
    }

    private String createFieldName(Iterator<Path.Node> iterator) {
        StringBuilder fieldNameBuilder = new StringBuilder();
        while(iterator.hasNext()) {
            Path.Node node = iterator.next();
            if(node.isInIterable()) {
                if(node.getIndex() != null) {
                    fieldNameBuilder.append("[" + node.getIndex() + "]");
                } else {
                    String suffix = "";
                    if(node.getName().equals("<map key>")) {
                        suffix = "<K>";
                    }
                    fieldNameBuilder.append("[" + node.getKey() + "]" + suffix);
                }
            }
            if(ElementKind.CONTAINER_ELEMENT.equals(node.getKind())) {
                // Just ignore
                continue;
            }
            fieldNameBuilder.append("." + node.getName());
        }
        return fieldNameBuilder.toString();
    }

    private List<ApiValidError> validErrors(ConstraintViolationException ex) {
        List<ApiValidError> apiValidErrors = new ArrayList<>();
        for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
            ApiValidError apiValidError = toFieldNotValidError(violation);
            apiValidErrors.add(apiValidError);
        }
        return apiValidErrors;
    }

    // -------------------------------

    /**
     * Return value type conversion error
     */
    @ExceptionHandler(HttpMessageConversionException.class)
    public ApiError exceptionHandle(HttpServletRequest request, HttpMessageConversionException ex) {
        return internalServiceError(request, ex);
    }

    /**
     * The type that the client wants to accept in the accept header of the Http request is inconsistent with the type returned by the server. Although the interception is set here, it does not work. The cause needs to be further determined through the process of the http request.
     */
    @ExceptionHandler(HttpMediaTypeNotAcceptableException.class)
    public ApiError handleHttpMediaTypeNotAcceptableException(HttpServletRequest request,
        HttpMediaTypeNotAcceptableException ex) {
        logger.debug(ex.getMessage(), ex);
        StringBuilder messageBuilder =
            new StringBuilder().append("The media type is not acceptable.").append(" Acceptable media types are ");
        ex.getSupportedMediaTypes().forEach(t -> messageBuilder.append(t + ", "));
        String message = messageBuilder.substring(0, messageBuilder.length() - 2);

        return ApiError.notAcceptable(message);
    }

    /**
     * The data type sent by the client corresponding to the content-type of the request header is inconsistent with the data that the server wants to receive
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ApiError handleHttpMediaTypeNotSupportedException(HttpServletRequest request,
        HttpMediaTypeNotSupportedException ex) {
        logger.debug(ex.getMessage(), ex);
        return ApiError.unsupportedMediaType(ex.getContentType(), ex.getSupportedMediaTypes());
    }

    /**
     * The data sent by the front end cannot be processed normally. For example, the data you want to receive the day after tomorrow is a json data, but the data sent by the front end is xml format data or an incorrect json format data
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ApiError handlerHttpMessageNotReadableException(HttpServletRequest request,
        HttpMessageNotReadableException ex) {
        if(ex.getCause() != null && ex.getCause() instanceof MismatchedInputException) {
            return handleMismatchedInputException(request, (MismatchedInputException) ex.getCause());
        }
        logger.debug(ex.getMessage(), ex);
        String message = "Problems parsing JSON";
        return ApiError.badRequest(message);
    }

    /**
     * The problem caused when converting the returned result to the response data. When using json as the result format, the possible cause is a serialization error. Currently, it is known that if an object without attributes is returned as a result, this exception will occur.
     */
    @ExceptionHandler(HttpMessageNotWritableException.class)
    public ApiError handlerHttpMessageNotWritableException(HttpServletRequest request,
        HttpMessageNotWritableException ex) {
        return internalServiceError(request, ex);
    }

    /**
     * Request method not supported
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ApiError exceptionHandle(HttpServletRequest request, HttpRequestMethodNotSupportedException ex) {
        logger.debug(ex.getMessage(), ex);
        return ApiError.methodNotAllow(ex.getMethod(), ex.getSupportedHttpMethods());
    }

    /**
     * When uploading a file, the file field is missing
     */
    @ExceptionHandler(MissingServletRequestPartException.class)
    public ApiError exceptionHandle(HttpServletRequest request, MissingServletRequestPartException ex) {
        logger.debug(ex.getMessage(), ex);
        String message = "Required request part '" + ex.getRequestPartName() + "' is not present";
        ApiError apiError = ApiError.badRequest( message);
        return apiError;
    }

    /**
     * The requested path does not exist
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ApiError exceptionHandle(HttpServletRequest request, NoHandlerFoundException ex) {
        logger.debug(ex.getMessage(), ex);
        return ApiError.notFound(ex.getHttpMethod(), ex.getRequestURL());
    }

    /**
     * Missing path parameters The @PathVariable(required=true) parameter is defined in the Controller method, but it is not provided in the URL
     */
    @ExceptionHandler(MissingPathVariableException.class)
    public ApiError exceptionHandle(HttpServletRequest request, MissingPathVariableException ex) {
        String message = "Missing URI template variable '" + ex.getVariableName() + "'";
        return ApiError.badRequest(message);
    }

    /**
     * All other exceptions
     */
    @ExceptionHandler()
    public ApiError handleAll(HttpServletRequest request, Exception ex) {
        return internalServiceError(request, ex);
    }

    public ApiError internalServiceError(HttpServletRequest request, Exception e) {
        String errorMessage = logExceptionFormat.formatted(e.getClass().getName(), e.getMessage());
        logger.error(errorMessage, e);
        return ApiError.internalServerError();
    }

}
