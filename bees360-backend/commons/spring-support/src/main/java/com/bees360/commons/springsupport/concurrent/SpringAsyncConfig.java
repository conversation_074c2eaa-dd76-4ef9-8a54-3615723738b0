package com.bees360.commons.springsupport.concurrent;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Optional;

@Log4j2
@Configuration
@EnableConfigurationProperties
@EnableAsync
public class SpringAsyncConfig {

    @Bean
    @ConfigurationProperties(prefix = "spring-customized.async.executor")
    ExecutorProperties asyncExecutorProperties() {
        return new ExecutorProperties()
            .setCorePoolSize(20)
            .setThreadNamePrefixSet("Async-")
            .setWaitForTasksToCompleteOnShutdown(true)
            .setAwaitTerminationSeconds(15);
    }

    @Bean(name = AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public TaskExecutor taskExecutor(ExecutorProperties asyncExecutorProperties) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        Optional.ofNullable(asyncExecutorProperties.getCorePoolSize()).ifPresent(executor::setCorePoolSize);
        // 设置最大线程数
        Optional.ofNullable(asyncExecutorProperties.getMaxPoolSize()).ifPresent(executor::setMaxPoolSize);
        // 设置队列容量
        Optional.ofNullable(asyncExecutorProperties.getQueueCapacity()).ifPresent(executor::setQueueCapacity);
        // 设置线程活跃时间（秒）
        Optional.ofNullable(asyncExecutorProperties.getKeepAliveSeconds()).ifPresent(executor::setKeepAliveSeconds);
        // 设置默认线程名称
        if (StringUtils.isNotEmpty(asyncExecutorProperties.getThreadNamePrefixSet())) {
            executor.setThreadNamePrefix(asyncExecutorProperties.getThreadNamePrefixSet());
        }
        // 等待所有任务结束后再关闭线程池
        Optional.ofNullable(asyncExecutorProperties.getWaitForTasksToCompleteOnShutdown())
            .ifPresent(executor::setWaitForTasksToCompleteOnShutdown);
        Optional.ofNullable(asyncExecutorProperties.getAwaitTerminationSeconds())
                .ifPresent(executor::setAwaitTerminationSeconds);

        Optional.ofNullable(asyncExecutorProperties.getRejectedExecutionHandler())
                .ifPresent(executor::setRejectedExecutionHandler);
        return executor;
    }
}
