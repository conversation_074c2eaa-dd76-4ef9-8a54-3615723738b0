package com.bees360.commons.springsupport.concurrent;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

@Setter
@Getter
@Accessors(chain = true)
public class ExecutorProperties {

    private Integer corePoolSize;

    private Integer maxPoolSize;

    private Integer keepAliveSeconds;

    private Integer queueCapacity;

    private String threadNamePrefixSet;

    private Boolean waitForTasksToCompleteOnShutdown;

    private Integer awaitTerminationSeconds;

    private String rejectedExecutionHandler;

    public RejectedExecutionHandler getRejectedExecutionHandler() {
        if (rejectedExecutionHandler == null) {
            return null;
        }
        switch (rejectedExecutionHandler) {
            case "AbortPolicy":
                return new ThreadPoolExecutor.AbortPolicy();
            case "CallerRunsPolicy":
                return new ThreadPoolExecutor.CallerRunsPolicy();
            case "DiscardPolicy":
                return new ThreadPoolExecutor.DiscardPolicy();
            case "DiscardOldestPolicy":
                return new ThreadPoolExecutor.DiscardOldestPolicy();
            default:
                return null;
        }
    }
}
