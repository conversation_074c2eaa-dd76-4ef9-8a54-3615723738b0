package com.bees360.commons.springsupport.concurrent;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.concurrent.SettableListenableFuture;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(
    classes = {
        SpringAsyncConfig.class,
        SpringScheduledConfig.class,
        SpringAsyncExecutorTest.Config.class,
    }
)
class SpringAsyncExecutorTest {

    public static class AsyncThreadNameRecorder {

        private final SettableListenableFuture<String> futureTask;

        public AsyncThreadNameRecorder(SettableListenableFuture<String> futureTask) {
            this.futureTask = futureTask;
        }

        public AsyncThreadNameRecorder() {
            this.futureTask = new SettableListenableFuture<>();
        }

        @Async
        public void updateThreadNameRecord() {
            futureTask.set(Thread.currentThread().getName());
        }

        @SneakyThrows
        public String getThreadName(Duration wait) {
            return futureTask.get(wait.toMillis(), TimeUnit.MICROSECONDS);
        }
    }

    public static class ScheduledThreadNameRecorder {

        private final SettableListenableFuture<String> futureTask;

        public ScheduledThreadNameRecorder(SettableListenableFuture<String> futureTask) {
            this.futureTask = futureTask;
        }

        public ScheduledThreadNameRecorder() {
            this.futureTask = new SettableListenableFuture<>();
        }

        @Scheduled(fixedRate = 100)
        public void updateThreadNameRecord() {
            if (futureTask.isDone()) {
                return;
            }
            futureTask.set(Thread.currentThread().getName());
        }

        @SneakyThrows
        public String getThreadName(Duration wait) {
            return futureTask.get(wait.toMillis(), TimeUnit.MICROSECONDS);
        }
    }

    @Configuration
    static class Config {

        @Bean
        AsyncThreadNameRecorder threadNameRecorder() {
            return new AsyncThreadNameRecorder();
        }

        @Bean
        ScheduledThreadNameRecorder scheduledThreadNameRecorder() {
            return new ScheduledThreadNameRecorder();
        }
    }

    @Autowired
    private AsyncThreadNameRecorder asyncThreadNameRecorder;

    @Autowired
    private ScheduledThreadNameRecorder scheduledThreadNameRecorder;

    @Test
    void testAsyncConfig() {
        asyncThreadNameRecorder.updateThreadNameRecord();
        assertTrue(StringUtils.startsWith(asyncThreadNameRecorder.getThreadName(Duration.ofSeconds(1)), "Async-"));
    }

    @Test
    void testScheduledExecutorConfig() {
        assertTrue(StringUtils.startsWith(scheduledThreadNameRecorder.getThreadName(Duration.ofSeconds(1)), "Scheduled-"));
    }
}
