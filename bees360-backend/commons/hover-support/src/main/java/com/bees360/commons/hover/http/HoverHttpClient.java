package com.bees360.commons.hover.http;

import com.bees360.commons.hover.exception.HoverAuthenticationException;
import com.bees360.commons.hover.exception.HoverAuthorizationException;
import com.bees360.commons.hover.exception.HoverClientErrorException;
import com.bees360.commons.hover.exception.HoverException;
import com.bees360.commons.hover.exception.HoverServerErrorException;
import com.bees360.commons.hover.request.HoverRequest;
import com.bees360.http.ApacheHttpClient;
import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.http.util.TextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

@Slf4j
public class HoverHttpClient {

    private final ApacheHttpClient httpClient;

    public HoverHttpClient(ApacheHttpClient httpClient){
        this.httpClient = httpClient;
    }

    public <T> T doGet(HoverRequest request, Function<String, T> function) throws HoverException {
        return doGetWithRetry(request, function);
    }

    private <T> T doGetWithRetry(HoverRequest request, Function<String, T> function) throws HoverException {
        Retryer<T> retryer = newCommonRetryer();
        try {
            return retryer.call(() -> doGetOnce(request, function));
        } catch (ExecutionException | RetryException e) {
            Throwable cause = Optional.ofNullable(e.getCause()).orElse(new IllegalStateException(e));
            throw cause instanceof RuntimeException re? re: new IllegalStateException(e);
        }
    }

    private <T> T doGetOnce(HoverRequest request, Function<String, T> function) throws HoverException {

        String paramString = getParamQueryString(request.getParam());
        String uri = request.getURI();
        if (!ObjectUtils.isEmpty(paramString)) {
            uri += paramString;
        }
        HttpGet httpGet = httpGet(uri, request.getHeaders());
        return httpClient.execute(httpGet, response -> {
            HttpEntity entity = resolveResponse(response);
            try {
                return function.apply(EntityUtils.toString(entity));
            } catch (IOException e) {
                throw new UncheckedIOException(e);
            }
        });
    }

    public <T> T doPost(HoverRequest request, Function<String, T> function) throws HoverException {
        return doPostWithRetry(request, function);
    }

    private <T> T doPostWithRetry(HoverRequest request, Function<String, T> function) throws HoverException {
        Retryer<T> retryer = newCommonRetryer();
        try {
            return retryer.call(() -> doPostOnce(request, function));
        } catch (ExecutionException | RetryException e) {
            Throwable cause = Optional.ofNullable(e.getCause()).orElse(new IllegalStateException(e));
            throw cause instanceof RuntimeException re? re: new IllegalStateException(e);
        }
    }

    private <T> T doPostOnce(HoverRequest request, Function<String, T> function) throws HoverException {

        List<BasicNameValuePair> pairs = request.getParam().entrySet().stream()
            .map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue().toString())).collect(
                Collectors.toList());

        HttpPost httpPost = httpPost(request.getURI(), request.getHeaders());
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(pairs));
        } catch (IOException e) {
            throw new IllegalArgumentException(e);
        }
        return httpClient.execute(httpPost, response -> {
            HttpEntity entity = resolveResponse(response);
            try {
                return function.apply(EntityUtils.toString(entity));
            } catch (IOException e) {
                throw new UncheckedIOException(e);
            }
        });
    }

    public <T> T doFormPost(HoverRequest request, Function<String, T> function) throws HoverException {
        return doFormPostWithRetry(request, function);
    }

    private <T> T doFormPostWithRetry(HoverRequest request, Function<String, T> function) throws HoverException {
        Retryer<T> retryer = newCommonRetryer();
        try {
            return retryer.call(() -> doFormPostOnce(request, function));
        } catch (ExecutionException | RetryException e) {
            Throwable cause = Optional.ofNullable(e.getCause()).orElse(new IllegalStateException(e));
            throw cause instanceof RuntimeException re? re: new IllegalStateException(e);
        }
    }

    private <T> T doFormPostOnce(HoverRequest request, Function<String, T> function) throws HoverException {

        HttpPost httpPost = httpPost(request.getURI(), request.getHeaders());

        MultipartEntityBuilder entityBuilder = MultipartEntityBuilder.create().setCharset(Consts.UTF_8);
        Optional.of(request.getParam()).ifPresent(params -> {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                entityBuilder.addTextBody(entry.getKey(), String.valueOf(entry.getValue()),
                    ContentType.create("text/plain", Consts.UTF_8));
            }
        });
        httpPost.setEntity(entityBuilder.build());

        return httpClient.execute(httpPost, response -> {
            HttpEntity entity = resolveResponse(response);
            try {
                return function.apply(EntityUtils.toString(entity));
            } catch (IOException e) {
                throw new UncheckedIOException(e);
            }
        });
    }

    public <T> Retryer<T> newCommonRetryer() {
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
            .retryIfExceptionOfType(HoverServerErrorException.class)
            .withWaitStrategy(WaitStrategies.exponentialWait(1000, 9, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterAttempt(5))
            .build();
        return retryer;
    }

    private HttpEntity resolveResponse(HttpResponse response) throws HoverException {
        final Function<HttpResponse, String> formatErrorStatus = r -> String.format(
            "Received error HTTP Response '%s' '%s'", r.getStatusLine(), Arrays.toString(r.getAllHeaders()));
        int statusCode = response.getStatusLine().getStatusCode();
        if (statusCode >= 200 && statusCode < 300) {
            return response.getEntity();
        }
        if (statusCode == 401) {
            throw new HoverAuthenticationException(formatErrorStatus.apply(response));
        }
        if (statusCode == 403) {
            throw new HoverAuthorizationException(formatErrorStatus.apply(response));
        }
        if (statusCode >= 400 && statusCode < 500) {
            throw new HoverClientErrorException(formatErrorStatus.apply(response));
        }
        if (statusCode >= 500 && statusCode < 600) {
            throw new HoverServerErrorException(formatErrorStatus.apply(response));
        }
        throw new IllegalStateException(formatErrorStatus.apply(response));
    }

    private HttpGet httpGet(String uri, Map<String, String> headers) {
        HttpGet get = new HttpGet(uri);
        if (headers != null) {
            for (Entry<String, String> entry : headers.entrySet()) {
                get.setHeader(entry.getKey(), entry.getValue());
            }
        }
        return get;
    }

    private HttpPost httpPost(String url, Map<String, String> headers) {
        HttpPost post = new HttpPost(url);
        if (headers != null) {
            for (Entry<String, String> entry : headers.entrySet()) {
                post.setHeader(entry.getKey(), entry.getValue());
            }
        }
        return post;
    }

    private String getParamQueryString(Map<String, Object> paramMap) {
        if (paramMap == null) {
            return "";
        }
        StringBuilder buf = new StringBuilder();
        if (paramMap.size() == 0) {
            return buf.toString();
        }

        buf.append("?");
        String param = paramMap.entrySet().stream().map(entry -> entry.getKey() + "=" + entry.getValue())
            .collect(Collectors.joining("&"));
        buf.append(param);
        return buf.toString();
    }

}
