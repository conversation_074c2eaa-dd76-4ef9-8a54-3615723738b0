package com.bees360.commons.hover.request;

import java.util.Map;

public class HoverGetRequest implements HoverRequest {

    private String uri;
    private Map<String, String> headers;
    private Map<String, Object> params;

    public HoverGetRequest(String uri, Map<String, String> headers, Map<String, Object> params){
        this.uri = uri;
        this.headers = headers;
        this.params = params;
    }

    @Override
    public String getMethod() {
        return GET_METHOD;
    }

    @Override
    public String getURI() {
        return this.uri;
    }

    @Override
    public Map<String, String> getHeaders() {
        return this.headers;
    }

    @Override
    public Map<String, Object> getParam() {
        return this.params;
    }
}
