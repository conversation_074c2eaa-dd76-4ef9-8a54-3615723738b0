package com.bees360.commons.hover.service;

import com.bees360.commons.hover.entity.HoverAuthorization;
import com.bees360.commons.hover.entity.JacksonObjectMapper;
import com.bees360.util.RedisUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class HoverAuthorizationManager {

    @Autowired
    private RedisUtil redisUtil;

    public void put(String authKey, HoverAuthorization authorization) throws Exception {
        redisUtil.set(authKey, JacksonObjectMapper.serialize(authorization));
    }

    public HoverAuthorization get(String authKey) throws Exception {
        Object obj = redisUtil.get(authKey);
        if (obj == null){
            return null;
        }
        return JacksonObjectMapper.deserialize((String) obj, HoverAuthorization.class);
    }
}
