package com.bees360.commons.hover.entity.job;

import lombok.Data;

import java.util.List;

@Data
public class JobEntity {
    private Long id;
    private String name;
    private Long org_id;
    private String org_name;
    private String location_line_1;
    private String location_line_2;
    private String location_city;
    private String location_region;
    private String location_postal_code;
    private String location_country;
    private String location_lat;
    private String location_lon;
    private String created_at;
    private String updated_at;
    private String uploaded_at;
    private String reported_at;
    private String customer_first_name;
    private String customer_name;
    private String customer_phone;
    private String customer_email;
    private Boolean customer_contact_only_by_email;
    private Boolean customer_contact;
    private String archive_number;
    private String archived_at;
    private Boolean example;
    private Boolean shared;
    private List<String> machete_features;
    private Integer estimates_count;
    private Integer org_job_accesses_assigned_lead_count;
    private Integer deliverable_id;
    private Integer mobile_application_id;
    private Double estimated_hours_to_completion;
    private Boolean approved;
    private String completed_at;
    private String roof_estimate_access_level;
    private String first_completed_at;
    private Boolean archived;
    private Long user_id;
    private Long captured_user_id;
    private String state;
    private List<HoverId> imageIds;
}
