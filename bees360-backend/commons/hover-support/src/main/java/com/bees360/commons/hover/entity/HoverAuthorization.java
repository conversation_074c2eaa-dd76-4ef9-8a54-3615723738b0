package com.bees360.commons.hover.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.Data;

@Data
public class HoverAuthorization {

    @JsonProperty("access_token")
    private String accessToken;
    @JsonProperty("refresh_token")
    private String refreshToken;
    @JsonProperty("token_type")
    private String tokenType;
    @JsonProperty("expires_in")
    private int expiresIn;
    @JsonProperty("created_at")
    private long createAt;
    @JsonProperty("owner_id")
    private long ownerId;
    @JsonProperty("owner_type")
    private String ownerType;
    @JsonProperty("scope")
    private String scope;

    public boolean tokenExpire(){
        return createAt + expiresIn <= System.currentTimeMillis() / 1000;
    }
}
