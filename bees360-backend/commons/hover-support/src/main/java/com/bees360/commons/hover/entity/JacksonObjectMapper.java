package com.bees360.commons.hover.entity;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import java.io.IOException;
import java.util.Objects;

import org.springframework.util.ObjectUtils;

public class JacksonObjectMapper {

    private static ObjectMapper objectMapper(){
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        return objectMapper;
    }

    public static  <T> String serialize(T obj) throws JsonProcessingException {
        if (Objects.isNull(obj)){
            return "";
        }
        ObjectMapper objectMapper = objectMapper();
        if (obj instanceof String string){
            return string;
        }
        return objectMapper.writeValueAsString(obj);
    }

    public static <T> T deserialize(String json, Class<T> clazz) throws IOException {
        if (ObjectUtils.isEmpty(json)){
            return null;
        }
        return objectMapper().readValue(json, clazz);
    }

}
