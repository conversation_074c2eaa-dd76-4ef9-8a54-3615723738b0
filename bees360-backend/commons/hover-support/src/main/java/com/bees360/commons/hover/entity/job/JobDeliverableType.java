package com.bees360.commons.hover.entity.job;

/**
 * <AUTHOR>
 */
public enum JobDeliverableType {
    ROOF_ONLY(2),
    COMPLETE_3D(3),
    ROOF_ESTIMATE(4),
    TOTAL_LIVING_AREA_PLUS(5),
    TOTAL_LIVING_AREA(6)
    ;

    private final int deliverableId;

    JobDeliverableType(int deliverableId) {
        this.deliverableId = deliverableId;
    }

    public int getDeliverableId() {
        return deliverableId;
    }

    public static JobDeliverableType getType(Integer deliverableId) {
        if (deliverableId == null) {
            return null;
        }
        for (JobDeliverableType type: JobDeliverableType.values()) {
            if (type.getDeliverableId() == deliverableId) {
                return type;
            }
        }
        return null;
    }
}
