package com.bees360.commons.hover.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "hover.api")
@Data
public class HoverApiProperties {

    public static final String API_CREATE_JOB = "/api/v1/jobs";

    public static final String API_GET_JOB_DETAILS = "/api/v2/jobs/{jobId}";

    public static final String API_RETRIEVE_TOKEN = "/oauth/token";

    public static final String API_REFRESH_TOKEN = "/oauth/token";

    private String host;
}
