package com.bees360.commons.hover.request;

import java.util.Map;

public interface HoverRequest {

    String GET_METHOD = "GET";
    String POST_METHOD = "POST";
    String POST_FORM_METHOD = "POST_FORM";

    String getMethod();

    String getURI();

    Map<String, String> getHeaders();

    Map<String, Object> getParam();

    static HoverRequest get(String uri, Map<String, String> headers, Map<String, Object> params) {
        return request(GET_METHOD, uri, headers, params);
    }

    static HoverRequest post(String uri, Map<String, String> headers, Map<String, Object> params) {
        return request(POST_METHOD, uri, headers, params);
    }

    static HoverRequest postForm(String uri, Map<String, String> headers, Map<String, Object> params) {
        return request(POST_FORM_METHOD, uri, headers, params);
    }

    static HoverRequest request(String method, String uri, Map<String, String> headers, Map<String, Object> params) {

        return new HoverRequest() {

            @Override
            public String getMethod() {
                return method;
            }

            @Override
            public String getURI() {
                return uri;
            }

            @Override
            public Map<String, String> getHeaders() {
                return headers;
            }

            @Override
            public Map<String, Object> getParam() {
                return params;
            }
        };
    }

}
