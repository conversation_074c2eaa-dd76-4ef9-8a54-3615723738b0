package com.bees360.commons.hover.service;

import com.bees360.commons.hover.entity.CreateJobResponseEntity;
import com.bees360.commons.hover.entity.HoverAuthorization;
import com.bees360.commons.hover.entity.JacksonObjectMapper;
import com.bees360.commons.hover.exception.HoverException;
import com.bees360.commons.hover.http.HoverHttpClient;
import com.bees360.commons.hover.request.HoverRequest;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.function.Function;

@Log4j2
public abstract class AbstractHoverService {

    protected abstract HoverHttpClient getClient();

    protected final <T> T doRequest(HoverRequest request, Function<String, T> function)
        throws IOException, HoverException {

        String method = request.getMethod();
        if (HoverRequest.POST_METHOD.equalsIgnoreCase(method)){
            return getClient().doPost(request, function);
        }
        if (HoverRequest.POST_FORM_METHOD.equalsIgnoreCase(method)){
            return getClient().doFormPost(request, function);
        }
        if (HoverRequest.GET_METHOD.equalsIgnoreCase(method)){
            return getClient().doGet(request, function);
        }
        throw new IllegalArgumentException();
    }

    protected final <T> T doRequest(HoverRequest request, Class<T> responseType)
        throws IOException, HoverException {
        log.info("request {} {}", request.getMethod(), request.getURI());
        return doRequest(request, (resp) -> {
            try {
                return JacksonObjectMapper
                    .deserialize(resp, responseType);
            } catch (IOException e) {
                String message = "Fail to request %s %s with param %s";
                message = message.formatted(request.getMethod(), request.getURI(), request.getParam());
                throw new UncheckedIOException(message, e);
            }
        });
    }

    protected final String getApiUri(String host, String api, boolean https){
        StringBuilder sb;
        if (https){
            sb = new StringBuilder("https://");
        }else{
            sb = new StringBuilder("http://");
        }
        return sb.append(host).append(api).toString();
    }

    public String getAccessToken(HoverAuthorization authorization){
        if (authorization == null){
            return "";
        }
        return "Bearer " + authorization.getAccessToken();
    }
}
