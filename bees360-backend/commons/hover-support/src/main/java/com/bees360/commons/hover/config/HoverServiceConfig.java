package com.bees360.commons.hover.config;

import com.bees360.commons.hover.HoverClient;
import com.bees360.commons.hover.http.HoverHttpClient;
import com.bees360.commons.hover.service.AuthorizationService;
import com.bees360.commons.hover.service.HoverAuthorizationManager;
import com.bees360.commons.hover.service.JobService;
import com.bees360.http.ApacheHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties({HoverAuthorizationProperties.class, HoverApiProperties.class})
public class HoverServiceConfig {

    @Autowired
    private HoverAuthorizationProperties authorizationProperties;
    @Autowired
    private HoverApiProperties apiProperties;

    @Bean
    public HoverHttpClient hoverHttpClient(ApacheHttpClient httpClient){
        return new HoverHttpClient(httpClient);
    }

    @Bean
    public HoverClient hoverClient(){
        return new HoverClient();
    }

    @Bean
    public AuthorizationService authorizationService(HoverHttpClient httpClient){

        AuthorizationService authorizationService = new AuthorizationService(httpClient);
        authorizationService.setAuthorizationProperties(authorizationProperties);
        authorizationService.setApiProperties(apiProperties);
        return authorizationService;
    }

    @Bean
    public JobService jobService(HoverHttpClient httpClient, AuthorizationService authorizationService){
        JobService jobService = new JobService(httpClient, authorizationService, apiProperties);
        return jobService;
    }

    @Bean
    public HoverAuthorizationManager hoverAuthorizationManager(){
        return new HoverAuthorizationManager();
    }

}
