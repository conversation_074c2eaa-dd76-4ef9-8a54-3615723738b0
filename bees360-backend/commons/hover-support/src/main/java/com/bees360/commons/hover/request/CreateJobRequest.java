package com.bees360.commons.hover.request;

import com.bees360.commons.hover.entity.CreateJobRequestEntity;
import java.util.HashMap;
import java.util.Map;

public class CreateJobRequest implements HoverRequest {

    private final String uri;
    private Map<String, String> headers;
    private Map<String, Object> param;

    public CreateJobRequest(String uri) {
        this.uri = uri;
    }

    public static CreateJobRequestBuilder newBuilder(String uri) {
        return new CreateJobRequestBuilder(uri);
    }

    public static class CreateJobRequestBuilder {

        private final CreateJobRequest createJobRequest;

        public CreateJobRequestBuilder(String uri) {
            createJobRequest = new CreateJobRequest(uri);
            createJobRequest.headers = new HashMap<>();
            createJobRequest.param = new HashMap<>();
        }

        public CreateJobRequestBuilder headers(Map<String, String> headers) {
            createJobRequest.headers.putAll(headers);
            return this;
        }

        public CreateJobRequestBuilder header(String key, String value) {
            createJobRequest.headers.put(key, value);
            return this;
        }

        public CreateJobRequestBuilder params(Map<String, Object> params){
            createJobRequest.param.putAll(params);
            return this;
        }

        public CreateJobRequestBuilder param(String key, Object value){
            createJobRequest.param.put(key, value);
            return this;
        }

        public CreateJobRequestBuilder createJobEntity(CreateJobRequestEntity jobEntity) throws IllegalAccessException {
            createJobRequest.param.putAll(jobEntity.toFormattedMap(true));
            return this;
        }

        public CreateJobRequest build(){
            return createJobRequest;
        }
    }

    @Override
    public String getMethod() {
        return POST_FORM_METHOD;
    }

    @Override
    public String getURI() {
        return this.uri;
    }

    @Override
    public Map<String, String> getHeaders() {
        return this.headers;
    }

    @Override
    public Map<String, Object> getParam() {
        return this.param;
    }
}
