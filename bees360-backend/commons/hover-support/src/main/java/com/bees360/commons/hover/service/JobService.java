package com.bees360.commons.hover.service;

import com.bees360.commons.hover.config.HoverApiProperties;
import com.bees360.commons.hover.config.HoverAuthorizationProperties;
import com.bees360.commons.hover.entity.CreateJobRequestEntity;
import com.bees360.commons.hover.entity.CreateJobResponseEntity;
import com.bees360.commons.hover.entity.HoverAuthorization;
import com.bees360.commons.hover.entity.JacksonObjectMapper;
import com.bees360.commons.hover.entity.job.JobEntity;
import com.bees360.commons.hover.exception.HoverAuthenticationException;
import com.bees360.commons.hover.exception.HoverAuthorizationException;
import com.bees360.commons.hover.exception.HoverException;
import com.bees360.commons.hover.http.HoverHttpClient;
import com.bees360.commons.hover.request.CreateJobRequest;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import com.bees360.commons.hover.request.HoverRequest;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestTemplate;

@Slf4j
public class JobService extends AbstractHoverService {


    private final HoverHttpClient client;
    private final AuthorizationService authorizationService;
    private final HoverApiProperties apiProperties;

    public JobService(HoverHttpClient client, AuthorizationService authorizationService,
        HoverApiProperties apiProperties) {
        this.client = client;
        this.authorizationService = authorizationService;
        this.apiProperties = apiProperties;
    }

    public CreateJobResponseEntity createJob(CreateJobRequestEntity createJobRequestEntity)
        throws Exception {
        Retryer<CreateJobResponseEntity> retryer = newCommonRetryer();
        try {
            return retryer.call(() -> {
                // 每次都是一个完整的重试，确保token是最新的
                HoverAuthorization authorization = authorizationService.getAuthorization();
                CreateJobRequest request = CreateJobRequest
                    .newBuilder(getApiUri(apiProperties.getHost(), HoverApiProperties.API_CREATE_JOB, true))
                    .header("Authorization", getAccessToken(authorization))
                    .params(createJobRequestEntity.toFormattedMap(true))
                    .build();
                log.info("try to create hover job, params = {}", request.getParam());
                return doRequest(request, CreateJobResponseEntity.class);
            });
        } catch (ExecutionException | RetryException e) {
            Throwable cause = Optional.ofNullable(e.getCause()).orElse(new IllegalStateException(e));
            throw cause instanceof Exception e1? e1: (RuntimeException) cause;
        }
    }

    public JobEntity getJobDetails(Long hoverJobId) throws Exception {
        Retryer<JobEntity> retryer = newCommonRetryer();
        try {
            return retryer.call(() -> {
                // 每次都是一个完整的重试，确保token是最新的
                HoverAuthorization authorization = authorizationService.getAuthorization();
                Map<String, String> headers = Maps.newHashMap();
                headers.put("Authorization", getAccessToken(authorization));
                String url = getApiUri(HoverApiProperties.API_GET_JOB_DETAILS).replace("{jobId}", hoverJobId + "");
                HoverRequest request = HoverRequest.get(url, headers, null);
                return doRequest(request, JobEntity.class);
            });
        } catch (ExecutionException | RetryException e) {
            Throwable cause = Optional.ofNullable(e.getCause()).orElse(new IllegalStateException(e));
            throw cause instanceof Exception e1? e1: (RuntimeException) cause;
        }
    }

    public <T> Retryer<T> newCommonRetryer() {
        // 尽量重试，确保程序正确执行
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
            .retryIfException(cause -> {
                return cause instanceof IOException || cause instanceof UncheckedIOException
                    || cause instanceof HoverAuthenticationException || cause instanceof HoverAuthorizationException;
            })
            .withWaitStrategy(WaitStrategies.exponentialWait(1000, 7, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterAttempt(5))
            .build();
        return retryer;
    }

    private String getApiUri(String api) {
        return getApiUri(apiProperties.getHost(), api, true);
    }

    @Override
    protected HoverHttpClient getClient() {
        return client;
    }
}
