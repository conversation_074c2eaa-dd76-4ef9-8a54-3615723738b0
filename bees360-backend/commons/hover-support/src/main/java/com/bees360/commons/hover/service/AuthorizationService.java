package com.bees360.commons.hover.service;

import com.bees360.commons.hover.config.HoverApiProperties;
import com.bees360.commons.hover.config.HoverAuthorizationProperties;
import com.bees360.commons.hover.entity.HoverAuthorization;
import com.bees360.commons.hover.entity.JacksonObjectMapper;
import com.bees360.commons.hover.exception.HoverAuthorizationException;
import com.bees360.commons.hover.exception.HoverException;
import com.bees360.commons.hover.exception.HoverServerErrorException;
import com.bees360.commons.hover.http.HoverHttpClient;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import com.bees360.commons.hover.request.HoverRequest;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.net.ssl.SSLException;

@RequiredArgsConstructor
@Slf4j
public class AuthorizationService extends AbstractHoverService {

    // 新的token生成之后，旧的token将会失效，因此通过读写锁尽量防止在更新的时候读token
    private static ReentrantReadWriteLock readWriteLock = new ReentrantReadWriteLock();
    private static Lock readLock = readWriteLock.readLock();
    private static Lock writeLock = readWriteLock.writeLock();

    private final HoverHttpClient client;

    @Setter
    private HoverAuthorizationProperties authorizationProperties;
    @Setter
    private HoverApiProperties apiProperties;

    @Autowired
    private HoverAuthorizationManager authorizationManager;

    public HoverAuthorization retrieveToken(String code) throws HoverException {

        Map<String, Object> params = new HashMap<>();
        params.put("client_id", authorizationProperties.getClientId());
        params.put("code", code);
        params.put("client_secret", authorizationProperties.getClientSecret());
        params.put("redirect_uri", authorizationProperties.getRedirectUri());
        params.put("grant_type", "authorization_code");
        writeLock.lock();
        try {
            String requestUri = getApiUri(HoverApiProperties.API_RETRIEVE_TOKEN);
            // connection:close 设置为非持久连接，预防 Connection reset
            HoverRequest request = HoverRequest.post(requestUri, Map.of("Connection", "close"), params);
            HoverAuthorization hoverAuthorization = doRequest(request, HoverAuthorization.class);
            updateAuthorization(hoverAuthorization);
            return hoverAuthorization;
        } catch (Exception e) {
            throw new HoverException(e);
        } finally {
            writeLock.unlock();
        }
    }

    private String getApiUri(String api) {
        return getApiUri(apiProperties.getHost(), api, true);
    }

    public void refreshToken() throws HoverException {
        refreshTokenWithRetry();
    }

    private void refreshTokenWithRetry() throws HoverException {
        Retryer<Boolean> retryer = newCommonRetryer();
        try {
            retryer.call(() -> {
                refreshTokenOnce();
                return true;
            });
        } catch (ExecutionException | RetryException e) {
            throw new IllegalStateException(e);
        }
    }

    private void refreshTokenOnce() throws HoverException {
        writeLock.lock();
        try {
            String refreshToken = "";
            HoverAuthorization authorization = getAuthorization();
            if (authorization != null) {
                refreshToken = authorization.getRefreshToken();
            }
            Map<String, Object> params = new HashMap<>();
            params.put("grant_type", "refresh_token");
            params.put("refresh_token", refreshToken);
            params.put("client_id", authorizationProperties.getClientId());
            params.put("client_secret", authorizationProperties.getClientSecret());

            String requestUri = getApiUri(apiProperties.getHost(), HoverApiProperties.API_REFRESH_TOKEN, true);
            // connection:close 设置为非持久连接，预防 Connection reset
            HoverRequest request = HoverRequest.post(requestUri, Map.of("Connection", "close"), params);

            HoverAuthorization hoverAuthorization = doRequest(request, HoverAuthorization.class);
            updateAuthorization(hoverAuthorization);
        } catch (Exception e) {
            throw new HoverException(e);
        } finally {
            writeLock.unlock();
        }
    }

    public <T> Retryer<T> newCommonRetryer() {
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
            // 如果请求过程中如果发生异常，hover服务器接收到了请求并将原token失效，且该客户端没有接收到响应，则会影响到后续的所有请求。
            // 这里对所有的异常进行处理
            .retryIfExceptionOfType(Throwable.class)
            .withWaitStrategy(WaitStrategies.exponentialWait(1000, 9, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterAttempt(7))
            .build();
        return retryer;
    }

    private void updateAuthorization(HoverAuthorization hoverAuthorization) throws Exception {
        String authKey = authorizationProperties.getKey();
        authorizationManager.put(authKey, hoverAuthorization);
    }

    public HoverAuthorization getAuthorization() {
        readLock.lock();
        try {
            String authKey = authorizationProperties.getKey();
            return authorizationManager.get(authKey);
        } catch(Exception e) {
            throw new IllegalStateException(e);
        } finally {
            readLock.unlock();
        }
    }

    @Override
    protected HoverHttpClient getClient() {
        return client;
    }
}
