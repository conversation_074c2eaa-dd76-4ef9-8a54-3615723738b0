package com.bees360.commons.hover;

import com.bees360.commons.hover.service.AuthorizationService;
import com.bees360.commons.hover.service.JobService;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

public class HoverClient implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    public JobService forJob(){
        return applicationContext.getBean(JobService.class);
    }

    public AuthorizationService forAuthorization(){
        return applicationContext.getBean(AuthorizationService.class);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
