package com.bees360.commons.hover.entity;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class CreateJobRequestEntity implements HoverRequestEntity {

    private static final String FORMAT_PREFIX = "job";
    private static final String FORMAT_WRAPPER_LEFT = "[";
    private static final String FORMAT_WRAPPER_RIGHT = "]";

    public static final int ROOF_ONLY_DELIVERABLE_ID = 2;
    public static final int COMPLETE_DELIVERABLE_ID = 3;
    public static final int ROOF_ESTIMATE_DELIVERABLE_ID = 4;
    public static final int TOTAL_LIVING_AREA_PLUS_DELIVERABLE_ID = 5;
    public static final int TOTAL_LIVING_AREA_DELIVERABLE_ID = 6;

    // The name of the customer. Usually the homeowner.
    private String customer_name;
    // The email address of the customer. Usually the homeowner.
    private String customer_email;
    // The phone number of the customer. Usually the homeowner.
    private String customer_phone;
    // A name for this job
    private String name;
    // First line of this job's address
    private String location_line_1;
    // Second line of this job's address
    private String location_line_2;
    // The city of this job's address
    private String location_city;
    // The state of this job's address
    private String location_region;
    // The postal code of this job's address
    private String location_postal_code;
    // The postal code of this job's address
    private Double location_lat;
    // The postal code of this job's address
    private Double location_lon;
    // The postal code of this job's address
    private String location_country;
    // Supported deliverable_ids: 2(roof only), 3(complete), 4(roof estimate, HOVER NOW), 5(Total Living Area Plus), 6(Total Living Area)
    private Integer deliverable_id;
    private Boolean customer_contact_only_by_email;
    // This optional parameter causes a job to be a "test" job and to skip our processing pipeline.
    // Supported values are "complete" and "failed". "complete" auto completes your job with example results. "failed" automatically fails the job.
    // This parameter is optional and shouldn't be used on real jobs.
    private String test_state;
    // A unique identifier generated by the client. This can be anything you want and won't be changed.
    // It can be used to reference an ID in the client application's database.
    private String external_identifier;
    // If a value other than false is given for this attribute then all emails related to this job will be suppressed. That includes capture request emails.
    private Boolean suppress_email;
    // Optional. By passing this parameter you can specify the billing information for the job.
    private Integer wallet_id;

    // Required. The internal user that you want to assign the job to. Alternatively you can also use the current_user_id param.
    @WrapIgnore
    private String current_user_email;


    public Map<String, Object> toFormattedMap(boolean omitNullField) throws IllegalAccessException {

        Map<String, Object> formattedMap = new HashMap<>();
        Field[] fields = CreateJobRequestEntity.class.getDeclaredFields();
        for (Field field : fields){
            int modifiers = field.getModifiers();
            if (Modifier.isFinal(modifiers)){
                continue;
            }
            String formattedFieldName = field.getName();
            WrapIgnore wrapIgnore = field.getAnnotation(WrapIgnore.class);
            if (wrapIgnore == null){
                formattedFieldName = new StringBuilder(FORMAT_PREFIX)
                    .append(FORMAT_WRAPPER_LEFT)
                    .append(formattedFieldName)
                    .append(FORMAT_WRAPPER_RIGHT)
                    .toString();
            }

            field.setAccessible(true);
            Object o = field.get(this);
            if (omitNullField && Objects.isNull(o)){
                continue;
            }
            formattedMap.put(formattedFieldName, o);
        }

        return formattedMap;
    }
}
