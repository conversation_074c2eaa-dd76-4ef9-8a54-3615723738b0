package com.bees360.commons.hover.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "hover.auth")
@Data
public class HoverAuthorizationProperties {
    /**
     * 见 https://sandbox.hover.to/ui/#/hvr/settings/developer
     */
    private String clientId;
    /**
     * 见 https://sandbox.hover.to/ui/#/hvr/settings/developer
     */
    private String clientSecret;
    /**
     * authentication code认证回调接口
     */
    private String redirectUri;
    /**
     * 自动刷新token的时间
     */
    private long refreshTokenIntervalSecond;

    public String getKey(){
        return "hover_auth_" + clientId;
    }
}
