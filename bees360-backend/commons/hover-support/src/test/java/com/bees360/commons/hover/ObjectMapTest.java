package com.bees360.commons.hover;

import com.bees360.commons.hover.entity.CreateJobResponseEntity;
import com.bees360.commons.hover.entity.HoverAuthorization;
import com.bees360.commons.hover.entity.JacksonObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.io.IOException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class ObjectMapTest {

    @Test
    public void testSerializer() throws IOException {

        String json = """
            {
            	"job": {
            		"id": 3772701,
            		"org_id": 249268,
            		"name": "Test Hover Job2",
            		"org_name": "<PERSON><PERSON>",
            		"user_name": "<EMAIL>",
            		"user_id": 455989,
            		"location_line_1": "634 2nd St.",
            		"location_line_2": null,
            		"location_city": "Wilmington",
            		"location_region": "MA",
            		"location_postal_code": "94107",
            		"location_country": "US",
            		"location_lat": null,
            		"location_lon": null,
            		"location": {
            			"line_1": "634 2nd St.",
            			"line_2": null,
            			"city": "Wilmington",
            			"region": "MA",
            			"postal_code": "94107",
            			"country": "US"
            		},
            		"customer_first_name": null,
            		"customer_name": "Luke Groundrunner",
            		"customer_phone": null,
            		"customer_notes": null,
            		"customer_email": "<EMAIL>",
            		"customer_contact_only_by_email": false,
            		"customer_contact": null,
            		"created_at": "2021-02-02T02:25:34.716Z",
            		"updated_at": "2021-02-02T02:25:34.875Z",
            		"archived": false,
            		"state": "uploading",
            		"example": false,
            		"shared": false,
            		"machete_features": ["ShareFeature", "MeasurementsFeature", "DesignFeature"],
            		"contractor_estimate_sent_to": null,
            		"money_shot": null,
            		"via_job_share": null,
            		"machete_blob": {
            			"geometrySrc": "https://hover.to/api/v1/models/3770932/model_json_v3",
            			"metadataSrc": "https://hover.to/api/v1/models/3770932/machete",
            			"productsSrc": "https://products.hover.to/hover_models/products.json",
            			"useInfoFeature": true,
            			"InfoFeature": {
            				"address": "634 2nd St.",
            				"username": "HOVER"
            			},
            			"useOptInFeature": true,
            			"OptInFeature": {
            				"enabled": true,
            				"designProUrl": "https://hover.to/3d/3772701"
            			},
            			"useDesignPro": true,
            			"DesignPro": {
            				"usePBRMode": false,
            				"useDesignProScreenshotMode": true
            			},
            			"PlanetTerror": {
            				"productsSrc": "https://products.hover.to/hover_models/products.json",
            				"manufacturersSrc": "https://localhost/xxx/xxxx",
            				"manufacturerProductsSrc": "https://products.hover.to/hover_models/manufacturer_products.json"
            			},
            			"useMeasurementsFeature": true,
            			"MeasurementsFeature": {
            				"labelsSrc": "https://hover.to/hover_models/labels.json",
            				"measurementsSrc": "https://hover.to/api/v1/models/3770932/machete_model_measurements",
            				"includeProMeasurements": false,
            				"includePremiumMeasurements": true,
            				"includeRoof": true,
            				"includeWalls": false,
            				"includeOpenings": false,
            				"includeSurfaceAreas": true,
            				"useMetricSystem": false
            			},
            			"useShareFeature": true,
            			"ShareFeature": {},
            			"useLabsFeature": false,
            			"useDesignFeature": true,
            			"DesignFeature": {
            				"labelsSrc": "https://hover.to/hover_models/labels.json",
            				"manufacturersSrc": "https://localhost/xxx/xxxx",
            				"productsSrc": "https://products.hover.to/hover_models/products.json",
            				"manufacturerProductsSrc": "https://products.hover.to/hover_models/manufacturer_products.json",
            				"showWindowColors": false
            			}
            		},
            		"deliverable_id": 2,
            		"estimated_hours_to_completion": 0.0,
            		"approved": true,
            		"completed_at": null,
            		"roof_estimate_access_level": "none",
            		"roof_estimate_supported": true,
            		"roof_estimate_presold": false,
            		"roof_estimate_purchase_state": null,
            		"property_type": "single_family_residential",
            		"client_identifier": "111",
            		"suppress_email": false,
            		"external_identifier": null,
            		"complete_expedited": false,
            		"external_identifier_label": "Lead Number",
            		"wallet": {
            			"id": 249178,
            			"org": {
            				"customer_display_name": "Bees"
            			}
            		},
            		"models": [{
            			"id": 3770932,
            			"job_id": 3772701,
            			"midas_identifier": null,
            			"address": null,
            			"position_wgs": [],
            			"model_json_v3": null,
            			"atlas": null,
            			"created_at": "2021-02-02T02:25:34.769Z",
            			"updated_at": "2021-02-02T02:25:34.784Z",
            			"advanced": false,
            			"time_expectation": 45,
            			"showcase": false,
            			"test": false,
            			"archive_number": null,
            			"archived_at": null,
            			"state": "uploading",
            			"original_model_id": null,
            			"video": null,
            			"machete": null,
            			"siding_report": null,
            			"payment_method": "credit_card",
            			"created_with_job": true,
            			"image_archive": {
            				"upload_url": "https://localhost/xxx/xxxx",
            				"cloudfront_upload_url": "https://localhost/xxx/xxxx",
            				"high_res": {
            					"upload_url": "https://localhost/xxx/xxxx",
            					"cloudfront_upload_url": "https://localhost/xxx/xxxx"
            				}
            			},
            			"token": null,
            			"machete_model_measurements": {},
            			"pro_measurements": null,
            			"pro_premium_measurements": null,
            			"roof_measurements": null,
            			"complexity_attributes": null,
            			"failure_reason": null,
            			"manually_uploaded_by_id": null,
            			"manowar_identifier": null,
            			"message_to_production_team": null,
            			"tracking_log": {},
            			"export": null,
            			"reprocessed": false,
            			"important_changed_areas_to_notify_user_about": null,
            			"wireframe_front": {},
            			"wireframe_front_right": {},
            			"wireframe_right": {},
            			"wireframe_right_back": {},
            			"wireframe_back": {},
            			"wireframe_back_left": {},
            			"wireframe_left": {},
            			"wireframe_left_front": {},
            			"wireframe_footprint": {},
            			"wireframe_roof": {},
            			"total_living_area_measurements": null,
            			"total_living_area_plus_measurements": null,
            			"wireframe_total_living_area": {},
            			"cv_data": {},
            			"measurement_results_s3_key": null,
            			"modeling_status": null,
            			"dense_capture": null,
            			"json_footprint": null,
            			"footprint_to_a": [],
            			"archived": false,
            			"complexity_score": "large",
            			"is_3d_available": false,
            			"building_id": null,
            			"polygon": null,
            			"images": []
            		}],
            		"job_assignments": [{
            			"id": 5808964,
            			"user_id": 455989,
            			"job_id": 3772701,
            			"kind": "creator",
            			"archived": false,
            			"archived_at": null,
            			"created_at": "2021-02-02T02:25:34.743Z",
            			"updated_at": "2021-02-02T02:25:34.743Z",
            			"user": {
            				"id": 455989,
            				"first_name": "zhenbin.yang",
            				"last_name": "",
            				"email": "<EMAIL>",
            				"time_zone": "Pacific Time (US \\u0026 Canada)",
            				"aasm_state": "activated",
            				"sign_in_count": 0,
            				"preferred_identifier": null,
            				"require_job_approval": false
            			},
            			"assigned_by": null,
            			"org": {
            				"id": 249268,
            				"parent_id": 1092,
            				"kind": "manufacturer",
            				"sort_order": null,
            				"name": "Bees",
            				"ancestral_name": "GAF \\u003e Pro \\u003e Bees",
            				"model_price": 4000,
            				"updated_at": "2019-10-03T10:05:03.620Z",
            				"ordering_email": null
            			}
            		}],
            		"org_job_accesses": [{
            			"id": 4370530,
            			"lead_state": "assigned",
            			"ordering_state": "lead",
            			"kind": "creator",
            			"job_id": 3772701,
            			"archived": false,
            			"via_job_share": null,
            			"archived_at": null,
            			"created_at": "2021-02-02T02:25:34.724Z",
            			"org": {
            				"id": 249268,
            				"parent_id": 1092,
            				"kind": "manufacturer",
            				"sort_order": null,
            				"name": "Bees",
            				"ancestral_name": "GAF \\u003e Pro \\u003e Bees",
            				"model_price": 4000,
            				"updated_at": "2019-10-03T10:05:03.620Z",
            				"ordering_email": null
            			},
            			"owner": {
            				"id": 249268,
            				"parent_id": 1092,
            				"kind": "manufacturer",
            				"sort_order": null,
            				"name": "Bees",
            				"ancestral_name": "GAF \\u003e Pro \\u003e Bees",
            				"model_price": 4000,
            				"updated_at": "2019-10-03T10:05:03.620Z",
            				"ordering_email": null
            			}
            		}],
            		"via_org_job_accesses": [{
            			"id": 4370530,
            			"lead_state": "assigned",
            			"ordering_state": "lead",
            			"kind": "creator",
            			"job_id": 3772701,
            			"archived": false,
            			"via_job_share": null,
            			"archived_at": null,
            			"created_at": "2021-02-02T02:25:34.724Z",
            			"org": {
            				"id": 249268,
            				"parent_id": 1092,
            				"kind": "manufacturer",
            				"sort_order": null,
            				"name": "Bees",
            				"ancestral_name": "GAF \\u003e Pro \\u003e Bees",
            				"model_price": 4000,
            				"updated_at": "2019-10-03T10:05:03.620Z",
            				"ordering_email": null
            			},
            			"owner": {
            				"id": 249268,
            				"parent_id": 1092,
            				"kind": "manufacturer",
            				"sort_order": null,
            				"name": "Bees",
            				"ancestral_name": "GAF \\u003e Pro \\u003e Bees",
            				"model_price": 4000,
            				"updated_at": "2019-10-03T10:05:03.620Z",
            				"ordering_email": null
            			}
            		}],
            		"orgs": [{
            			"id": 249268,
            			"parent_id": 1092,
            			"kind": "manufacturer",
            			"sort_order": null,
            			"name": "Bees",
            			"ancestral_name": "GAF \\u003e Pro \\u003e Bees",
            			"model_price": 4000,
            			"updated_at": "2019-10-03T10:05:03.620Z",
            			"ordering_email": null
            		}],
            		"job_shares": [],
            		"through_org": {
            			"id": 249268,
            			"parent_id": 1092,
            			"kind": "manufacturer",
            			"sort_order": null,
            			"name": "Bees",
            			"ancestral_name": "GAF \\u003e Pro \\u003e Bees",
            			"label_ids": [408, 404, 409, 405, 7, 604, 603, 602, 601, 6, 5, 407, 406, 403, 402, 401, 400, 4, 310, 534, 527, 304, 504, 502, 301, 505, 503, 501, 513, 512, 511, 510, 509, 508, 506, 307, 309, 517, 306, 302, 300, 308, 305, 303, 533, 532, 531, 530, 529, 528, 526, 525, 524, 521, 520, 519, 518, 516, 515, 514, 3, 206, 222, 203, 202, 200, 221, 220, 210, 205, 209, 211, 2, 101, 100, 103, 104, 102, 1],
            			"cust_serv_contact": null,
            			"cust_serv_name": null,
            			"cust_serv_line_1": null,
            			"cust_serv_line_2": null,
            			"cust_serv_city": null,
            			"cust_serv_region": null,
            			"cust_serv_postal_code": null,
            			"cust_serv_website": null,
            			"cust_serv_phone": null,
            			"needs_3d": true,
            			"cust_serv_email": null,
            			"supports_openings": false,
            			"supports_paint": false,
            			"org_size": "5",
            			"sales_rep": null,
            			"updated_at": "2019-10-03T10:05:03.620Z",
            			"manufacturer_ids": [12, 389, 8, 6, 7, 4, 1, 9, 5, 15, 16, 58, 57],
            			"discounts": [],
            			"plan_billing_cycle": "monthly",
            			"deliverable_id": null,
            			"deliverable_ids": [2, 3, 4],
            			"leaf": true,
            			"empty": false,
            			"created_at": "2019-09-03T07:09:27.962Z",
            			"client_branding_logo_url": null,
            			"test_data": false,
            			"customer_display_name": "Bees",
            			"external_partner_identifier": null,
            			"brand": {
            				"id": 62,
            				"org_id": 371,
            				"primary_color": {
            					"b": 52,
            					"g": 4,
            					"r": 0
            				},
            				"secondary_color": {
            					"b": 74,
            					"g": 71,
            					"r": 70
            				},
            				"tertiary_color": {
            					"b": 42,
            					"g": 29,
            					"r": 242
            				},
            				"email_primary_color": {
            					"b": 52,
            					"g": 4,
            					"r": 0
            				},
            				"email_secondary_color": {
            					"b": 74,
            					"g": 71,
            					"r": 70
            				},
            				"email_tertiary_color": {
            					"b": 42,
            					"g": 29,
            					"r": 242
            				},
            				"logo": {
            					"url": "https://hover.to/api/v1/orgs/371/brand/logo.png",
            					"thumb": {
            						"url": "https://hover.to/api/v1/orgs/371/brand/logo.png?version=thumb"
            					},
            					"nonretina": {
            						"url": "https://hover.to/api/v1/orgs/371/brand/logo.png?version=nonretina"
            					},
            					"retina": {
            						"url": "https://hover.to/api/v1/orgs/371/brand/logo.png?version=retina"
            					}
            				},
            				"places_logo": {
            					"url": "https://hover.to/api/v1/orgs/371/brand/places_logo.png"
            				},
            				"email_logo": {
            					"url": "https://hover.to/api/v1/orgs/371/brand/email_logo.png"
            				},
            				"model_background_color": {
            					"r": 8,
            					"g": 148,
            					"b": 213
            				},
            				"model_terrain_color": {
            					"r": 102,
            					"g": 153,
            					"b": 102
            				}
            			},
            			"settings": {
            				"id": 249285,
            				"org_id": 249268,
            				"supports_openings_estimator": false,
            				"supports_paint_estimator": false,
            				"needs_3d": true,
            				"created_at": "2019-09-03T07:09:27.969Z",
            				"updated_at": "2019-09-03T07:09:28.152Z",
            				"auto_accept_users": true,
            				"app_id": null,
            				"machete_features": ["measurements", "share", "design"],
            				"supports_openings_instant_estimator": false,
            				"supports_paint_instant_estimator": false,
            				"supports_redirect_to_new_website": true,
            				"supports_full_job_info_display": true,
            				"supports_machete": true,
            				"model_price": 4000,
            				"payment_method": "credit_card",
            				"privacy_url": "/privacy",
            				"terms_url": "/terms_of_use",
            				"supports_co_branded_pro_measurements": true,
            				"config": {
            					"pro_org_init": {
            						"model_price": 4000
            					}
            				},
            				"priority": 12,
            				"auto_invoice_enabled": null,
            				"auto_invoice_day": null,
            				"customer_account": null,
            				"prepaid_quota": 0,
            				"needs_texture": true,
            				"supports_ordering": false,
            				"supports_leads": false,
            				"supports_wall_measurements": false,
            				"supports_roof_measurements": true,
            				"example_jobs_support": false,
            				"example_jobs_selection_method": 0,
            				"example_jobs_identifier": null,
            				"app_name": null,
            				"vip": false,
            				"allow_waste_factor_preference": null,
            				"invoiced": false,
            				"hide_price_in_email": null,
            				"trial_enabled": false,
            				"trial_start": "2019-09-03T07:09:28.102Z",
            				"trial_period": 30,
            				"trial_ends_at": null,
            				"trial_remaining_jobs_allowed": null,
            				"trial_expired": true,
            				"auto_approve_roof_estimate": false,
            				"capture_v2_enabled": true
            			},
            			"preferences": {
            				"id": 249267,
            				"org_id": 249268,
            				"estimator_custom_job_options": true,
            				"estimator_edit_global_options": true,
            				"estimator_detailed_pricing_pdf": true,
            				"estimator_default_pricing_pdf": 0,
            				"estimator_detailed_pricing_summary": true,
            				"estimator_default_pricing_summary": 0,
            				"estimator_line_item_pricing": true,
            				"estimator_default_line_item_pricing": true,
            				"estimator_include_salesrep_summary": false,
            				"estimator_default_view": 0,
            				"created_at": "2019-09-03T07:09:27.971Z",
            				"updated_at": "2019-09-03T07:09:28.155Z",
            				"estimator_detailed_pricing_breakdown": true,
            				"default_acl_template": "pro_plus",
            				"ordering_email": null,
            				"preferred_siding_waste_factor": null,
            				"preferred_roofing_waste_factor": null,
            				"external_identifier_label": "Lead Number",
            				"external_identifier_required": false,
            				"include_pro_user_name_in_invite_messages": true,
            				"deliverable_id": null
            			},
            			"plan": {
            				"id": 143,
            				"name": "Pay As You Go",
            				"partner_id": 2,
            				"machete": true,
            				"measurements": true,
            				"measurements_branding": false,
            				"ordering": false,
            				"basic_export": true,
            				"advanced_export": false,
            				"basic_design": true,
            				"advanced_design": true,
            				"basic_team_management": false,
            				"advanced_team_management": true,
            				"created_at": "2017-04-18T18:01:06.024Z",
            				"updated_at": "2021-01-05T12:37:03.280Z",
            				"default": false,
            				"visible": true,
            				"sort_index": 1,
            				"feature_icons": null,
            				"feature_bullets": null,
            				"email_receipt": true,
            				"auto_assign_leads": false,
            				"partner_analytics": false,
            				"org_analytics": false,
            				"allow_photo_only_capture": true,
            				"days_of_historical_job_access": 365,
            				"homeowner_lead_capture": false,
            				"plan_after_trial_id": null,
            				"allows_instant_estimate": false,
            				"allow_upgrade_to_pro": true,
            				"allow_multi_family_residential_captures": false,
            				"synced_monthly_with_stripe": true,
            				"synced_yearly_with_stripe": true,
            				"tier": "basic",
            				"deleted": false,
            				"deleted_at": null,
            				"partner": {
            					"id": 2,
            					"name": "GAF",
            					"deliverable_ids": [2, 3, 4],
            					"allow_deliverable_choice": true,
            					"client_branding_logo_url": null,
            					"allow_insurance_damage_capture": false,
            					"allow_insurance_underwriting_capture": false,
            					"identifier": "gaf"
            				}
            			},
            			"wallet": {
            				"id": 249178,
            				"org_id": 249268,
            				"balance": 0,
            				"stripe_token": null,
            				"created_at": "2019-09-03T07:09:28.090Z",
            				"updated_at": "2019-09-03T07:09:29.489Z",
            				"stripe_customer_identifier": null,
            				"billing_address_line_1": null,
            				"billing_address_line_2": null,
            				"billing_address_city": null,
            				"billing_address_region": null,
            				"billing_address_postal_code": "22162",
            				"billing_address_country": null,
            				"billing_email": null,
            				"card_brand": null,
            				"card_last4": null,
            				"cardholder_name": null,
            				"card_expires_on": null,
            				"last_error": null,
            				"deleted": false,
            				"deleted_at": null
            			},
            			"features": [{
            				"id": 12008,
            				"accessor_id": 143,
            				"accessor_type": "Plan",
            				"enabled": true,
            				"created_at": "2019-03-13T20:07:55.275Z",
            				"updated_at": "2019-03-13T20:07:55.275Z",
            				"identifier": "email_send_receipt",
            				"deleted": false,
            				"deleted_at": null
            			}, {
            				"id": 12010,
            				"accessor_id": 143,
            				"accessor_type": "Plan",
            				"enabled": true,
            				"created_at": "2019-03-13T20:07:55.302Z",
            				"updated_at": "2019-03-13T20:07:55.302Z",
            				"identifier": "prospect",
            				"deleted": false,
            				"deleted_at": null
            			}, {
            				"id": 12011,
            				"accessor_id": 143,
            				"accessor_type": "Plan",
            				"enabled": true,
            				"created_at": "2019-03-13T20:07:55.314Z",
            				"updated_at": "2019-03-13T20:07:55.314Z",
            				"identifier": "design_view_measurements_tool",
            				"deleted": false,
            				"deleted_at": null
            			}, {
            				"id": 12012,
            				"accessor_id": 143,
            				"accessor_type": "Plan",
            				"enabled": true,
            				"created_at": "2019-03-13T20:07:55.327Z",
            				"updated_at": "2019-03-13T20:07:55.327Z",
            				"identifier": "measurements_files_access",
            				"deleted": false,
            				"deleted_at": null
            			}, {
            				"id": 12640,
            				"accessor_id": 143,
            				"accessor_type": "Plan",
            				"enabled": true,
            				"created_at": "2019-03-13T21:55:11.230Z",
            				"updated_at": "2019-03-13T21:55:11.230Z",
            				"identifier": "upgrade_to_pro",
            				"deleted": false,
            				"deleted_at": null
            			}, {
            				"id": 13846,
            				"accessor_id": 143,
            				"accessor_type": "Plan",
            				"enabled": true,
            				"created_at": "2019-05-01T20:02:51.630Z",
            				"updated_at": "2019-05-01T20:02:51.630Z",
            				"identifier": "team_management_advanced",
            				"deleted": false,
            				"deleted_at": null
            			}]
            		},
            		"pending_capture_request": null,
            		"capture_request": null,
            		"roof_estimate": null
            	}
            }\
            """;

        CreateJobResponseEntity deserialize = JacksonObjectMapper.deserialize(json, CreateJobResponseEntity.class);
        Assertions.assertTrue(deserialize.getJob().getId() != null);
    }


    @Test
    public void testAuthDeserialize() throws IOException {

        String json = "{\"access_token\":\"hidden-token\",\"token_type\":\"Bearer\",\"expires_in\":7199,\"refresh_token\":\"hidden-token\",\"scope\":\"all\",\"created_at\":1612242161,\"owner_id\":249268,\"owner_type\":\"orgs\"}";

        HoverAuthorization deserialize = JacksonObjectMapper.deserialize(json, HoverAuthorization.class);

        Assertions.assertEquals(deserialize.getAccessToken(), "hidden-token");

    }


    @Test
    public void testSerialize() throws JsonProcessingException {

        HoverAuthorization authorization = new HoverAuthorization();
        authorization.setAccessToken("hidden-access-token");
        authorization.setTokenType("Bearer");
        authorization.setExpiresIn(7200);
        authorization.setRefreshToken("hidden-refresh-token");
        authorization.setScope("all");
        authorization.setCreateAt(1612242161);
        authorization.setOwnerId(249268);
        authorization.setOwnerType("orgs");

        Assertions.assertNotNull(JacksonObjectMapper.serialize(authorization));

    }

}
