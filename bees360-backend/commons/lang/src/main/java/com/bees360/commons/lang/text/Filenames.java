package com.bees360.commons.lang.text;

import org.apache.commons.lang3.RegExUtils;

public class Filenames {

    private static final String ILLEGAL_FILENAME_CHARS = "<>:\"/\\|?*";
    private static final String ILLEGAL_FILENAME_CHARS_REGEX = "[<>:\"/\\\\|\\?\\*]";

    public static String replaceIllegalCharTo(String filename, String replacement) {
        return RegExUtils.replaceAll(filename, ILLEGAL_FILENAME_CHARS_REGEX, replacement);
    }

    public static String replaceIllegalCharToUnderline(String filename) {
        return replaceIllegalCharTo(filename, "_");
    }
}
