package com.bees360.commons.lang.net;

import java.net.MalformedURLException;
import java.net.URL;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2021/6/23
 */
public class Urls {

    private static final String PROTOCOL_HTTP = "http://";
    private static final String PROTOCOL_HTTPS = "https://";

    public static URL newUrl(String url) throws MalformedURLException {
        return newUrl(url, PROTOCOL_HTTP);
    }

    public static URL newUrl(String url, String defaultProtocol) throws MalformedURLException {
        url = prependProtocol(url, defaultProtocol);
        return StringUtils.isEmpty(url)? null: new URL(url);
    }

    /**
     * 如果url缺少protocol，则使用https作为默认prefix
     * @param url
     * @return
     */
    public static String prependProtocol(String url) {
        return prependProtocol(url, PROTOCOL_HTTPS);
    }

    public static String prependProtocol(String url, String defaultProtocol) {
        url = StringUtils.trim(url);
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        if (StringUtils.startsWithIgnoreCase(url, PROTOCOL_HTTP) || StringUtils.startsWithIgnoreCase(url, PROTOCOL_HTTPS)) {
            return url;
        }
        return defaultProtocol + StringUtils.removeStart(url, "/");
    }
}
