package com.bees360.commons.lang.time;

import java.util.TimeZone;

/**
 * 美国的50个城市对应的时区
 *
 * <AUTHOR>
 * @since 2020/7/23 8:46 PM
 **/
public enum  AmericaStateTimeZoneEnum {
    /**
     * PACIFIC_Time * 4
     */
    WA("WA", "Washington", AmericaTimeZone.US_PACIFIC),
    OR("OR", "Oregon", AmericaTimeZone.US_PACIFIC),
    CA("CA", "California",AmericaTimeZone.US_PACIFIC),
    NV("NV", "Nevada", AmericaTimeZone.US_PACIFIC),

    /**
     * MOUNTAIN_TIME * 7
     */

    MT("MT", "Montana",AmericaTimeZone.US_MOUNTAIN),
    ID("ID", "Idaho",AmericaTimeZone.US_MOUNTAIN),
    WY("WY", "Wyoming",AmericaTimeZone.US_MOUNTAIN),
    UT("UT", "Utah",AmericaTimeZone.US_MOUNTAIN),
    CO("CO", "Colorado",AmericaTimeZone.US_MOUNTAIN),
    AZ("AZ", "Arizona",AmericaTimeZone.US_MOUNTAIN),
    NM("NM", "New Mexico",AmericaTimeZone.US_MOUNTAIN),

    /**
     * CENTRAL_TIME * 17
     */
    ND("ND", "North Dakota",AmericaTimeZone.US_CENTRAL),
    SD("SD", "South Dakota",AmericaTimeZone.US_CENTRAL),
    MN("MN", "Minnesota",AmericaTimeZone.US_CENTRAL),
    IA("IA", "Iowa",AmericaTimeZone.US_CENTRAL),
    WI("WI", "Wisconsin",AmericaTimeZone.US_CENTRAL),
    IL("IL", "Illinois",AmericaTimeZone.US_CENTRAL),
    NE("NE", "Nebraska",AmericaTimeZone.US_CENTRAL),
    KS("KS", "Kansas",AmericaTimeZone.US_CENTRAL),
    MO("MO", "Missouri",AmericaTimeZone.US_CENTRAL),
    OK("OK", "Oklahoma",AmericaTimeZone.US_CENTRAL),
    AR("AR", "Arkansas",AmericaTimeZone.US_CENTRAL),
    TX("TX", "Texas",AmericaTimeZone.US_CENTRAL),
    LA("LA", "Louisiana",AmericaTimeZone.US_CENTRAL),
    MS("MS", "Mississippi",AmericaTimeZone.US_CENTRAL),
    KY("KY", "Kentucky",AmericaTimeZone.US_CENTRAL),
    TN("TN", "Tennessee",AmericaTimeZone.US_CENTRAL),
    AL("AL", "Alabama",AmericaTimeZone.US_CENTRAL),

    /**
     * EASTERN_TIME * 20
     */
    MI("MI", "Michigan",AmericaTimeZone.US_EASTERN),
    IN("IN", "Indiana",AmericaTimeZone.US_EASTERN),
    OH("OH", "Ohio",AmericaTimeZone.US_EASTERN),
    PA("PA", "Pennsylvania",AmericaTimeZone.US_EASTERN),
    NY("NY", "New York",AmericaTimeZone.US_EASTERN),
    VT("VT", "Vermont",AmericaTimeZone.US_EASTERN),
    ME("ME", "Maine",AmericaTimeZone.US_EASTERN),
    NH("NH", "New Hampshire",AmericaTimeZone.US_EASTERN),
    MA("MA", "Massachusetts",AmericaTimeZone.US_EASTERN),
    CT("CT", "Connecticut",AmericaTimeZone.US_EASTERN),
    RI("RI", "Rhode Island",AmericaTimeZone.US_EASTERN),
    WV("WV", "West Virginia",AmericaTimeZone.US_EASTERN),
    VA("VA", "Virginia",AmericaTimeZone.US_EASTERN),
    MD("MD", "Maryland",AmericaTimeZone.US_EASTERN),
    NJ("NJ", "New Jersey",AmericaTimeZone.US_EASTERN),
    DE("DE", "Delaware",AmericaTimeZone.US_EASTERN),
    NC("NC", "North Carolina",AmericaTimeZone.US_EASTERN),
    GA("GA", "Georgia",AmericaTimeZone.US_EASTERN),
    SC("SC", "South Carolina",AmericaTimeZone.US_EASTERN),
    FL("FL", "Florida",AmericaTimeZone.US_EASTERN),

    /**
     * ALASKA_TIME * 1
     */
    AK("AK", "Alaska",AmericaTimeZone.US_Alaska),

    /**
     * HAWAII_TIME * 1
     */
    HI("HI", "Hawaii",AmericaTimeZone.US_HAWAII)
    ;

    AmericaStateTimeZoneEnum(String stateShortName, String stateName, String zoneId) {
        this.stateShortName = stateShortName;
        this.stateName = stateName;
        this.zoneId = zoneId;
    }
    private String stateName;
    private String stateShortName;
    private String zoneId;

    public String getStateName() {
        return stateName;
    }

    public String getStateShortName() {
        return stateShortName;
    }

    public String getZoneId() {
        return zoneId;
    }

    public static AmericaStateTimeZoneEnum getEnum(String state) {
        for (AmericaStateTimeZoneEnum americaStateTimeZoneEnum : values()) {
            if (americaStateTimeZoneEnum.stateShortName.equalsIgnoreCase(state) ||
                americaStateTimeZoneEnum.stateName.equalsIgnoreCase(state)) {
                return americaStateTimeZoneEnum;
            }
        }
        return null;
    }

    public static TimeZone getTimeZone(String state) {
        AmericaStateTimeZoneEnum timeZoneEnum = getEnum(state);
        if (timeZoneEnum == null) {
            return null;
        }
        return TimeZone.getTimeZone(timeZoneEnum.zoneId);
    }
}
