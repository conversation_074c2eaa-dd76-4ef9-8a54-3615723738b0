package com.bees360.commons.lang.time;

import java.sql.Time;
import java.time.ZoneId;
import java.util.TimeZone;

/**
 *
 /**
 * https://www.zeitverschiebung.net/cn/all-time-zones.html
 * @see ZoneId#getAvailableZoneIds()
 * @see TimeZone#getAvailableIDs()
 *
 * <AUTHOR>
 */
public class AmericaTimeZone {

    /**
     * 阿拉斯加时区(AKST)（西九区时间）：代表城市：费尔班克斯，冬令时与北京相差17小时，夏令时差16小时；
     */
    public static final String US_Alaska = "US/Alaska";
    public static final String US_ALEUTIAN = "US/Aleutian";
    public static final String US_ARIZONA = "US/Arizona";

    /**
     * 中部时区(CST)（西六区时间）：代表城市芝加哥，冬令时与北京相差14小时，夏令时差13小时。
     */
    public static final String US_CENTRAL = "US/Central";
    public static final String US_EAST_INDIANA = "US/East-Indiana";

    /**
     * 东部时区(EST)（西五区时间）：代表城市纽约、华盛顿，冬令时与北京相差13小时，夏令时差12小时。
     */
    public static final String US_EASTERN = "US/Eastern";

    /**
     * 夏威夷时区(HST)（西十区时间）：代表城市：火奴鲁鲁，与北京相差18小时（夏威夷没有夏时制）。
     */
    public static final String US_HAWAII = "US/Hawaii";
    public static final String US_INDIANA_STARKE = "US/Indiana-Starke";
    public static final String US_MICHIGAN = "US/Michigan";

    /**
     * 山区时区(MST)（西七区时间）：代表城市盐湖城，冬令时与北京相差15小时，夏令时差14小时。
     */
    public static final String US_MOUNTAIN = "US/Mountain";

    /**
     * 太平洋时区（西部时间）(PST)（西八区时间）：代表城市洛杉矶，冬令时与北京相差16小时，夏令时差15小时。
     */
    public static final String US_PACIFIC = "US/Pacific";
    public static final String US_PACIFIC_NEW = "US/Pacific-New";
    public static final String US_SAMOA = "US/Samoa";
}
