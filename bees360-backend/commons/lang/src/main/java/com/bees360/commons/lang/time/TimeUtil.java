package com.bees360.commons.lang.time;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @since 2020/7/23 7:58 PM
 **/
public class TimeUtil {

    public static final String DEFAULT_FORMAT = "MM/dd/yyyy hh:mm a z";
    public static final String DATE_DEFAULT_FORMAT = "MM/dd/yyyy";
    public static final String YEAR = "yyyy";
    public static final String NORMAL_FORMAT = "MMMM d, yyyy";

    public static String formatToUSTime(long milliSecondTime, String state) {
        return format(milliSecondTime, getTimeZone(state), DEFAULT_FORMAT);
    }

    public static String formatToUSTime(long milliSecondTime) {
        return format(milliSecondTime, getDefaultTimeZone(), DEFAULT_FORMAT);
    }

    public static String formatToUSDate(LocalDate localDate) {
        return format(localDate, getDefaultTimeZone(), DATE_DEFAULT_FORMAT);
    }

    public static String formatToUSDate(long milliSecondTime) {
        return formatToUSDate(Instant.ofEpochMilli(milliSecondTime));
    }

    public static String formatToUSDate(Instant instant) {
        return format(instant, getDefaultTimeZone(), DATE_DEFAULT_FORMAT);
    }

    private static TimeZone getDefaultTimeZone() {
        return TimeZone.getTimeZone(AmericaTimeZone.US_CENTRAL);
    }

    private static TimeZone getTimeZone(String state) {
        final TimeZone timeZone = AmericaStateTimeZoneEnum.getTimeZone(state);
        if (timeZone == null) {
            throw new IllegalArgumentException("The US state [" + state + "] is invalid");
        }
        return timeZone;
    }

    public static String format(long millisecondTime, String format) {
        return format(Instant.ofEpochMilli(millisecondTime), getDefaultTimeZone(), format);
    }

    public static String format(long millisecondTime, TimeZone timeZone, String format) {
        return format(Instant.ofEpochMilli(millisecondTime), timeZone, format);
    }

    public static String format(LocalDate localDate, TimeZone timeZone, String format) {
        return format(localDate.atStartOfDay(), timeZone, format);
    }

    public static String format(LocalDateTime localDateTime, TimeZone timeZone, String format) {
        return format(localDateTime.toInstant(ZoneOffset.UTC), timeZone, format);
    }

    public static String format(Instant instant, TimeZone timeZone, String format) {
        return ZonedDateTime.ofInstant(instant,
            timeZone.toZoneId()).format(DateTimeFormatter.ofPattern(format, Locale.ENGLISH));
    }
}
