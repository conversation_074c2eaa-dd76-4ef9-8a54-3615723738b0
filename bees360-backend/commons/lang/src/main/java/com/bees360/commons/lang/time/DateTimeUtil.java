package com.bees360.commons.lang.time;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;

public class DateTimeUtil {
    /**
     * 得到与当天相隔天数的开始时间
     *
     * @param dayFromToday 距离当天的天数
     * @return 指定的某天开始时间
     */
    public static Instant getStartOfDay(int dayFromToday, ZoneId zoneId) {
        return LocalDate.now(zoneId)
            .atStartOfDay()
            .plusDays(dayFromToday)
            .toInstant(zoneId.getRules().getOffset(Instant.now()));
    }

    public static Instant getStartOfMonth(int monthFromCurrentMonth, ZoneId zoneId) {
        return LocalDate.now(zoneId)
            .atStartOfDay()
            .plusMonths(monthFromCurrentMonth)
            .with(TemporalAdjusters.firstDayOfMonth())
            .toInstant(zoneId.getRules().getOffset(Instant.now()));
    }
}
