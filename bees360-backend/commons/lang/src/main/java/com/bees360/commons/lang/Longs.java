package com.bees360.commons.lang;

/**
 * <AUTHOR>
 */
public class Longs {

    public static Long valueOf(String value, Long defaultIfFail) {
        try {
            return Long.valueOf(value);
        } catch (Exception e) {
            return defaultIfFail;
        }
    }

    public static Long tryParse(String value, Long defaultIfFail) {
        try {
            return Long.parseLong(value);
        } catch (Exception e) {
            return defaultIfFail;
        }
    }
}
