package com.bees360.commons.lang.text;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Random;

import org.hamcrest.collection.IsIterableContainingInOrder;
import org.junit.jupiter.api.Test;

/**
 * ref：https://github.com/paour/natorder/blob/master/NaturalOrderComparator.java
 *
 * <AUTHOR>
 */
public class NaturalOrderComparatorTest {
    private String[] originalInOrder = new String[] {"1-2", "1-02", "1-20", "10-20", "fred", "jane", "pic01", "pic2",
        "pic02", "pic02a", "pic3", "pic4", "pic 4 else", "pic5", "pic 5", "pic05", "pic  5", "pic 5 something", "pic 6",
        "pic   7", "pic100", "pic100a", "pic120", "pic121", "pic02000", "tom", "x2-g8", "x2-y7", "x2-y08", "x8-y8"};

    @Test
    public void sort() {
        List<String> scrambled = newArray(originalInOrder);
        // 混乱
        Collections.shuffle(scrambled);

        Collections.sort(scrambled, new NaturalOrderComparator());

        assertThat(scrambled, IsIterableContainingInOrder.contains(originalInOrder));
    }

    @Test
    public void sort_shuffle3000() {

        List<String> scrambled = newArray(originalInOrder);
        Collections.shuffle(scrambled, new Random(3000));
        Collections.sort(scrambled, new NaturalOrderComparator());

        assertThat(scrambled, IsIterableContainingInOrder.contains(originalInOrder));
    }

    @Test
    public void compareIgnore0AndSpace() {

        NaturalOrderComparator comparator = new NaturalOrderComparator();

        int compare = comparator.compare("1-02", "1-3");
        assertTrue(compare < 0);
        compare = comparator.compare("1- 2", "1-3");
        assertTrue(compare < 0);

        compare = comparator.compare("1-02", "1-1");
        assertTrue(compare > 0);
        compare = comparator.compare("1- 2", "1-1");
        assertTrue(compare > 0);

        compare = comparator.compare("1-002", "1-02");
        assertTrue(compare > 0);

        compare = comparator.compare("1-  2", "1- 2");
        assertTrue(compare > 0);

        compare = comparator.compare("1-002", "1-12");
        assertTrue(compare < 0);
    }

    @Test
    public void compareSymmetric() {
        NaturalOrderComparator naturalOrderComparator = new NaturalOrderComparator();

        int compare1 = naturalOrderComparator.compare("1-2", "1-02");
        int compare2 = naturalOrderComparator.compare("1-02", "1-2");

        assertTrue(compare1 < 0);
        assertTrue(compare2 > 0);
        assertEquals(compare1 + compare2, 0);

        compare1 = naturalOrderComparator.compare("pic 5", "pic05");
        compare2 = naturalOrderComparator.compare("pic05", "pic 5");

        assertTrue(compare1 < 0);
        assertTrue(compare2 > 0);
        assertEquals(compare1 + compare2, 0);
    }

    @Test
    public void floatsWithCommas() {
        final String[] sorted = {"0.3", "0.6", "0.7", "0.8", "0.9", "1.0", "1.0b", "1.0c", "1.1", "1.2", "1.3"};
        List<String> unSorted = newArray(sorted);
        Collections.shuffle(unSorted);
        // System.out.println("Unsorted: " + unSorted);

        unSorted.sort(new NaturalOrderComparator());
        // System.out.println("Sorted: " + unSorted);

        assertThat(unSorted, IsIterableContainingInOrder.contains(sorted));
    }

    /**
     * NaturalOrderComparator 会忽略空格进行比较
     */
    @Test
    public void sortFileName() {
        // @formatter:off
        final String[] filesSorted = {
            "1-Front Slope-3-1-Close up.jpg",
            "1-Front Slope-5-7-Close up.jpg",
            "1-Front Slope-8-0-Hail damage.jpg",
            "1-Front Slope-  11-8-Close up.jpg",
            "1-Front Slope-13  -2-Close up.jpg",
            "1-Front Slope-13-  5-Close up.jpg",
            "1-Front Slope  -15-3-Close up.jpg",
            "1-Front Slope- 15-4-Close up.jpg",
            "1-Front Slope- 19-8-Close up.jpg",
            "1-Front    Slope-20-7-Close up.jpg",
            "3-Rear Slope-3-3-Close up.jpg",
            "3-  Rear Slope-5-5-Close up.jpg",
            "3-Rear Slope-10-1-Close up.jpg",
            "3  -Rear Slope-10-6-Close up.jpg",
            "4-Left Slope-3-6-  Close up.jpg",
            "4-Left Slope-7-0-Hail damage.jpg"
        };
        // @formatter:on
        List<String> unSorted = newArray(filesSorted);
        Collections.shuffle(unSorted);
        // System.out.println(unSorted);
        unSorted.sort(new NaturalOrderComparator());
        // System.out.println(unSorted);
        assertThat(unSorted, IsIterableContainingInOrder.contains(filesSorted));
    }

    private List<String> newArray(String[] array) {
        return new ArrayList<>(Arrays.asList(array));
    }
}
