/*
 * The Alphanum Algorithm is an improved sorting algorithm for strings containing numbers. Instead of sorting numbers in
 * ASCII order like a standard sort, this algorithm sorts numbers in numeric order.
 *
 * The Alphanum Algorithm is discussed at http://www.DaveKoelle.com
 *
 * Released under the MIT License - https://opensource.org/licenses/MIT
 *
 * Copyright 2007-2017 <PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the
 * Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
 * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
package com.bees360.commons.lang.text;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.hamcrest.collection.IsIterableContainingInOrder;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR> Yang
 */
public class AlphanumComparatorTest {
    private String[] originalInOrder = {"1-2", "1-02", "1-20", "10-20", "fred", "jane", "pic2", "pic3", "pic4", "pic5",
        "pic01", "pic02", "pic02a", "pic05", "pic100", "pic100a", "pic120", "pic121", "pic02000", "pic 4 else", "pic 5",
        "pic 5 something", "pic 6", "pic  5", "pic   7", "tom", "x2-g8", "x2-y7", "x2-y08", "x8-y8"};

    /**
     * Shows an example of how the comparator works. Feel free to delete this in your own code!
     */
    @Test
    public void sortMain() {
        String[] array = {"1", "2", "10", "EctoMorph6", "EctoMorph7", "EctoMorph62", "dazzle1", "dazzle2", "dazzle2.7",
            "dazzle2.10", "dazzle10"};
        List<String> values = newArray(array);
        Collections.shuffle(values);
        Collections.sort(values, new AlphanumComparator());

        assertThat(values, IsIterableContainingInOrder.contains(array));
    }

    @Test
    public void sort() {
        List<String> scrambled = newArray(originalInOrder);
        // 混乱
        Collections.shuffle(scrambled);

        Collections.sort(scrambled, new AlphanumComparator());

        assertThat(scrambled, IsIterableContainingInOrder.contains(originalInOrder));
    }

    @Test
    public void compareWith0AndSpace() {

        AlphanumComparator comparator = new AlphanumComparator();

        int compare = comparator.compare("1-02", "1-3");
        assertTrue(compare > 0);
        compare = comparator.compare("1- 2", "1-3");
        assertTrue(compare > 0);

        compare = comparator.compare("1-02", "1-1");
        assertTrue(compare > 0);
        compare = comparator.compare("1- 2", "1-1");
        assertTrue(compare > 0);

        compare = comparator.compare("1-002", "1-02");
        assertTrue(compare > 0);

        compare = comparator.compare("1-  2", "1- 2");
        assertTrue(compare > 0);

        // 002 比 12要长
        compare = comparator.compare("1-002", "1-12");
        assertTrue(compare > 0);
    }

    private List<String> newArray(String[] array) {
        return new ArrayList<>(Arrays.asList(array));
    }
}
