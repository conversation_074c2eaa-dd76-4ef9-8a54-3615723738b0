package com.bees360.commons.lang.text;


import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class FilenamesTest {

    @Test
    public void testReplaceIllegalCharToUnderline() {
        var illegalChars = "<>:\"/\\|?*";
        assertEquals("_________", Filenames.replaceIllegalCharToUnderline(illegalChars));
        for (char ch: illegalChars.toCharArray()) {
            assertEquals("a_a.pdf", Filenames.replaceIllegalCharToUnderline("a" + ch + "a.pdf"));
        }
    }
}
