package com.bees360.commons.lang.net;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.net.MalformedURLException;
import java.net.URL;
import org.junit.jupiter.api.Test;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class UrlsTest {

    @Test
    public void newUrl() throws MalformedURLException {
        String[] urls = {"www.bees360.com", "http://www.bees360.com", "  http://www.bees360.com  ",
            "https://www.bees360.com", "https://www.bees360.com/", "HTTP://www.bees360.com", "HTTPS://www.bees360.com"};
        for (String input : urls) {
            URL url = Urls.newUrl(input);
            assertEquals("www.bees360.com", url.getHost());
            assertTrue(StringUtils.equalsAny(url.getProtocol(), "http", "https"),
                "The protocol of " + input + " should be one of {http, https}, but url is " + url);
        }

        URL url = Urls.newUrl("");
        assertEquals(null, url);

        url = Urls.newUrl(null);
        assertEquals(null, url);
    }
}
