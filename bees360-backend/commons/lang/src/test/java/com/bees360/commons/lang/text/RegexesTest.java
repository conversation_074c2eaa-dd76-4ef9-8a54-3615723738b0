package com.bees360.commons.lang.text;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertEquals;
import java.util.List;
import org.junit.jupiter.api.Test;

public class RegexesTest {

    @Test
    public void testFindUrls() {
        final String text = "Visit http://www.somesite.com/page, where you will find more information." +
            "And https://somesite.com/page/dfed.xfdfe/se-dfd is a special url. " +
            "try this PLNAR Share Link: (https://snap.plnar.co/link/RwjV) and this PLNAR Share Link: https://snap.plnar.co/link/RwjV." +
            " How about this www.bees360.com, https://www.symbility.net/ux/site/#/claims?goodnews=dabdfds";
        final List<String> expected = Arrays.asList("http://www.somesite.com/page", "https://somesite.com/page/dfed.xfdfe/se-dfd",
            "https://snap.plnar.co/link/RwjV", "https://snap.plnar.co/link/RwjV", "www.bees360.com",
            "https://www.symbility.net/ux/site/#/claims?goodnews=dabdfds");
        List<String> result = Regexes.findUrls(text);
        assertEquals(expected, result);
    }

    @Test
    public void testFindPlnarLink() {
        final String text = "PLNAR Snap Project Link: https://snap.plnar.co/link/ESXt";
        final List<String> expected = Arrays.asList("https://snap.plnar.co/link/ESXt");
        List<String> result = Regexes.findUrls(text);
        assertEquals(expected, result);
    }
}
