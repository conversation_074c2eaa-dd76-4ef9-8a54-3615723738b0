package com.bees360.commons.lang.text;

import com.bees360.commons.lang.time.AmericaStateTimeZoneEnum;
import org.junit.jupiter.api.Test;

import static com.bees360.commons.lang.time.TimeUtil.formatToUSTime;

/**
 * <AUTHOR>
 * @since 2020/7/24 4:10 PM
 **/
public class TimeUtilTest {
    @Test
    public void testDefault() {
        long now = System.currentTimeMillis();
        // PT
        assert formatToUSTime(now, "WA").equals(formatToUSTime(now, "California"));
        System.out.println("PT: " + formatToUSTime(now, "WA"));
        System.out.println("PT: " + formatToUSTime(now, "WA"));

        //MT
        assert formatToUSTime(now, "MT").equals(formatToUSTime(now, "ID"));
        System.out.println("MT: " + formatToUSTime(now, "MT"));
        System.out.println("MT: " + formatToUSTime(now, "ID"));


        // CT
        assert formatToUSTime(now, "Oklahoma").equals(formatToUSTime(now, "Texas"));
        System.out.println("CT: " + formatToUSTime(now, "Texas"));
        System.out.println("CT: " + formatToUSTime(now, "Oklahoma"));


        // ET
        assert formatToUSTime(now, "GA").equals(formatToUSTime(now, "Georgia"));
        System.out.println("ET: " + formatToUSTime(now, "GA"));
        System.out.println("ET: " + formatToUSTime(now, "Georgia"));

        // ALASKA TIME
        System.out.println("ALASKA TIME: " + formatToUSTime(now, "ALASKA"));

        // HAWAII TIME
        System.out.println("HAWAII TIME: " + formatToUSTime(now, "Hawaii"));
    }

    @Test
    public void checkState() {
        String string = """
            Alabama - AL

            Alaska - AK

            Arizona - AZ

            Arkansas - AR

            California - CA

            Colorado - CO

            Connecticut - CT

            Delaware - DE

            Florida - FL

            Georgia - GA

            Hawaii - HI

            Idaho - ID

            Illinois - IL

            Indiana - IN

            Iowa - IA

            Kansas - KS

            Kentucky - KY

            Louisiana - LA

            Maine - ME

            Maryland - MD

            Massachusetts - MA

            Michigan - MI

            Minnesota - MN

            Mississippi - MS

            Missouri - MO

            Montana - MT

            Nebraska - NE

            Nevada - NV

            New Hampshire - NH

            New Jersey - NJ

            New Mexico - NM

            New York - NY

            North Carolina - NC

            North Dakota - ND

            Ohio - OH

            Oklahoma - OK

            Oregon - OR

            Pennsylvania - PA

            Rhode Island - RI

            South Carolina - SC

            South Dakota - SD

            Tennessee - TN

            Texas - TX

            Utah - UT

            Vermont - VT

            Virginia - VA

            Washington - WA

            West Virginia - WV

            Wisconsin - WI

            Wyoming - WY""";
        String[] states = string.split("\\n+");
        for (String state : states) {
            if (state.isEmpty()) {
                continue;
            }
            System.out.println(state);
            String[] names = state.split(" - ");
            String stateName = names[0];
            String shortName = names[1];
            AmericaStateTimeZoneEnum anEnum = AmericaStateTimeZoneEnum.getEnum(shortName);
            assert anEnum != null;
            assert anEnum.getStateName().equals(stateName);
        }
    }
}
