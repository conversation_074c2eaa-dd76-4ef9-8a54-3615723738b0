package com.bees360.commons.firebasesupport.service.impl;

import com.bees360.commons.firebasesupport.entity.BeespilotConstantRemoteConfig;
import com.bees360.commons.firebasesupport.entity.ContactQuestionRemoteConfig;
import com.bees360.commons.firebasesupport.entity.TaskIdMapRemoteConfig;
import com.bees360.commons.firebasesupport.entity.TaskRemoteConfig;
import com.bees360.commons.firebasesupport.enums.RemoteConfigParameter;
import com.bees360.commons.firebasesupport.service.RemoteConfigService;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.firebase.remoteconfig.Parameter;
import com.google.firebase.remoteconfig.ParameterValue;
import com.google.firebase.remoteconfig.Template;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.scheduling.annotation.Scheduled;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
public class RemoteConfigServiceImpl implements RemoteConfigService {

    private FirebaseRemoteConfig firebaseRemoteConfig;

    public RemoteConfigServiceImpl() {
    }

    public RemoteConfigServiceImpl(FirebaseRemoteConfig firebaseRemoteConfig) {
        this.firebaseRemoteConfig = firebaseRemoteConfig;
    }

    @PostConstruct
    public void initRemoteConfig() throws ExecutionException, InterruptedException {
        refreshRemoteConfig();
    }

    // 每天 UTC时间 零点零分执行更新 Remote Config
    @Scheduled(cron = "0 0 0 ? * *")
    public void refreshRemoteConfig() throws ExecutionException, InterruptedException {
        Template template = firebaseRemoteConfig.getTemplateAsync().get();
        for (Map.Entry<String, Parameter> en : template.getParameters().entrySet()) {
            Parameter parameter = en.getValue();
            ParameterValue value = parameter.getDefaultValue();
            if (value instanceof ParameterValue.Explicit explicit) {
                String valueStr = explicit.getValue();
                remoteConfigMap.put(en.getKey(), valueStr);
            }
        }
    }

    @Override
    public List<ContactQuestionRemoteConfig> getContactQuestion() {
        return remoteConfig(ContactQuestionRemoteConfig.class, RemoteConfigParameter.CONTACT_QUESTION.getName());
    }

    @Override
    public TaskIdMapRemoteConfig getTaskIdMapRemoteConfig() {
        var config =
                remoteConfigObject(
                        BeespilotConstantRemoteConfig.class,
                        RemoteConfigParameter.BEESPILOT_CONSTANT.getName());
        if (config == null) {
            return null;
        }
        return config.getTaskIdMap();
    }

    @Override
    public List<String> getDroneTaskId() {
        List<TaskRemoteConfig> task =
                getRemoteConfig(TaskRemoteConfig.class, RemoteConfigParameter.TASK.getName());
        String droneTask = "drone";
        return task.stream()
                .filter(e -> droneTask.equals(e.getSource()))
                .map(TaskRemoteConfig::getTaskId)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getMobileTaskId() {
        List<TaskRemoteConfig> task =
                getRemoteConfig(TaskRemoteConfig.class, RemoteConfigParameter.TASK.getName());
        String mobileTask = "mobile";
        return task.stream()
                .filter(e -> mobileTask.equals(e.getSource()))
                .map(TaskRemoteConfig::getTaskId)
                .collect(Collectors.toList());
    }

    @Override
    @NonNull
    public <T> List<T> getRemoteConfig(Class<T> tClass, String name) {
        return remoteConfig(tClass, name);
    }

    private <T> List<T> remoteConfig(Class<T> tClass, String name) {
        String jsonValue = remoteConfigMap.get(name);
        if (Strings.isBlank(jsonValue)) {
            return new ArrayList<>();
        }
        JsonArray jsonArray = new JsonParser().parse(jsonValue).getAsJsonArray();
        Gson gson = new Gson();
        List<T> list = new ArrayList<>();
        for (JsonElement jsonElement : jsonArray) {
            list.add(gson.fromJson(jsonElement, tClass));
        }
        return list;
    }

    public  <T> T remoteConfigObject(Class<T> tClass, String name) {
        String jsonValue = remoteConfigMap.get(name);
        if (Strings.isBlank(jsonValue)) {
            return null;
        }
        JsonElement json = new JsonParser().parse(jsonValue);
        Gson gson = new Gson();
        return gson.fromJson(json, tClass);
    }
}
