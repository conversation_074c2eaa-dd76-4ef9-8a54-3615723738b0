package com.bees360.commons.firebasesupport.service;

import com.bees360.commons.firebasesupport.entity.ContactQuestionRemoteConfig;
import com.bees360.commons.firebasesupport.entity.TaskIdMapRemoteConfig;
import lombok.NonNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

public interface RemoteConfigService {
    Map<String, String> remoteConfigMap = new HashMap<>();

    void refreshRemoteConfig() throws ExecutionException, InterruptedException;

    /**
     * 获取Remote Config中 Contact Question相关的配置
     *
     * @return RemoteConfig
     */
    List<ContactQuestionRemoteConfig> getContactQuestion();

    /**
     * 获取飞手任务配置列表
     * @return TaskIdMapRemoteConfig
     */
    TaskIdMapRemoteConfig getTaskIdMapRemoteConfig();

    /** 获取allDroneTasks */
    List<String> getDroneTaskId();

    /** 获取allMobileTasks */
    List<String> getMobileTaskId();

    /**
     * 获取remote config的值
     *
     * @param tClass
     * @param <T>
     * @return
     */
    @NonNull
    <T> List<T> getRemoteConfig(Class<T> tClass, String name);

    <T> T remoteConfigObject(Class<T> tClass, String name);
}
