package com.bees360.commons.firebasesupport.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContactQuestionRemoteConfig {
    /**
     * 该值为询问Claim类型的项目的是否有Damage的问卷题的Code
     */
    public static final int ClaimDamageQuizCode = 3;
    private String name;
    private int code;

    private List<String> choices;

    private int order;

    private int type;
}
