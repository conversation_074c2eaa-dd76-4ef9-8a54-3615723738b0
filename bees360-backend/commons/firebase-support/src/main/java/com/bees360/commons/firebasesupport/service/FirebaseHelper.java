package com.bees360.commons.firebasesupport.service;

import com.bees360.commons.firebasesupport.FirebaseException;
import com.bees360.commons.firebasesupport.entity.CollectionData;
import com.bees360.commons.firebasesupport.entity.DocumentData;
import com.bees360.commons.firebasesupport.entity.QueryModel;
import com.google.api.core.ApiFuture;
import com.google.cloud.firestore.CollectionReference;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.Query;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.cloud.firestore.SetOptions;
import com.google.cloud.firestore.WriteResult;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/08/24 15:34
 */
@Slf4j
public class FirebaseHelper {

    private Firestore firestore;

    public FirebaseHelper(Firestore firestore) {
        this.firestore = firestore;
    }

    /**
     * Add a document
     * Firestore auto-generate an ID
     * @param model
     * @param <T>
     */
    public <T> String add(CollectionData<T> model) {
        // 解析JSON
        CollectionReference collRef = firestore.collection(model.getCollection());

        ApiFuture<DocumentReference> result = collRef.add(model.getData());
        try {
            return result.get().getId();
        } catch (InterruptedException | ExecutionException e) {
            throw new FirebaseException(e);
        }

    }

    /**
     * 更新或插入document
     * @param collection
     * @param document
     * @param map
     */
    public void set(String collection, String document, Map<String, Object> map) {
        DocumentReference docRef = firestore.collection(collection).document(document);
        try {
            // asynchronously retrieve the document
            ApiFuture<WriteResult> result = docRef.set(map, SetOptions.merge());
            // future.get() blocks on response
            result.get();
        } catch (Exception e) {
            log.error("Fail to set firebase document ({}, {}).", collection, document, e);
        }
    }

    /**
     * 更新document
     * @param collection
     * @param document
     * @param map
     */
    public void update(String collection, String document, Map<String, Object> map) {
        DocumentReference docRef = firestore.collection(collection).document(document);
        try {
            // asynchronously retrieve the document
            ApiFuture<WriteResult> result = docRef.update(map);
            // future.get() blocks on response
            result.get();
        } catch (Exception e) {
            log.error("Fail to update firebase document ({}, {}).", collection, document, e);
        }
    }

    public <T> T getDataById(String collection, String document, Class<T> clazz) {
        // 解析JSON
        DocumentReference docRef = firestore.collection(collection).document(document);

        ApiFuture<DocumentSnapshot> apiFuture = docRef.get();
        try {
            DocumentSnapshot snapshot = apiFuture.get();
            return snapshot.toObject(clazz);
        } catch (InterruptedException | ExecutionException e) {
            throw new FirebaseException(e);
        }
    }

    public <T> List<DocumentData<T>> getDataBySingleEqualQuery(String collection, QueryModel queryModel, Class<T> clazz) {
        Query query = firestore.collection(collection).whereEqualTo(queryModel.getKey(), queryModel.getValue());
        ApiFuture<QuerySnapshot> querySnapshot = query.get();
        try {
            return querySnapshot.get()
                .getDocuments()
                .stream()
                .map(doc -> new DocumentData<>(collection, doc.getId(),
                    doc.toObject(clazz)))
                .collect(Collectors.toList());
        } catch (InterruptedException | ExecutionException e) {
            throw new FirebaseException(e);
        }
    }


    public DocumentReference getDocumentReference(String collection, String document) {
        return firestore.collection(collection).document(document);
    }

}
