package com.bees360.commons.firebasesupport.config.annotation;

import com.bees360.commons.firebasesupport.config.FirebaseConfig;
import org.springframework.context.annotation.Import;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(FirebaseConfig.class)
public @interface EnableFirebaseConfig {

}
