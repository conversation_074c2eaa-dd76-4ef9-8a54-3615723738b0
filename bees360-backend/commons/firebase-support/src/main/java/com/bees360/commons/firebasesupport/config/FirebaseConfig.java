package com.bees360.commons.firebasesupport.config;

import com.bees360.commons.firebasesupport.service.impl.RemoteConfigServiceImpl;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.firestore.Firestore;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.cloud.FirestoreClient;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigException;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2020/08/24 14:31
 */
@Configuration
public class FirebaseConfig {

    @Configuration
    @Data
    @ConfigurationProperties("firebase")
    @EnableConfigurationProperties
    static class Properties {
        private static final Gson gson = new GsonBuilder().disableHtmlEscaping().create();

        @Data
        static class Credential {
            private String type;
            private String project_id;
            private String private_key_id;
            private String private_key;
            private String client_email;
            private String client_id;
            private String auth_uri;
            private String token_uri;
            private String auth_provider_x509_cert_url;
            private String client_x509_cert_url;

            public GoogleCredentials getGoogleCredentials() throws IOException {
                String jsonString = gson.toJson(this);
                byte[] jsonKeyBytes = jsonString.getBytes(StandardCharsets.UTF_8);
                ByteArrayInputStream jsonKeyStream = new ByteArrayInputStream(jsonKeyBytes);
                return ServiceAccountCredentials.fromStream(jsonKeyStream);
            }
        }

        @Data
        static class Firestore {
            private String emulatorHost;
        }

        private Credential credential;
        private Firestore firestore;
    }

    @Bean
    public FirebaseApp firebaseApp(Properties properties) throws IOException {
        // Use a service account
        GoogleCredentials credentials = properties.getCredential().getGoogleCredentials();
        FirebaseOptions options = FirebaseOptions.builder()
            .setCredentials(credentials)
            .build();
        return FirebaseApp.initializeApp(options);
    }

    @Bean
    public Firestore firestore(FirebaseApp firebaseApp) {
        return FirestoreClient.getFirestore(firebaseApp);
    }

    @Bean
    public FirebaseAuth firebaseAuth(FirebaseApp firebaseApp) {
        return FirebaseAuth.getInstance(firebaseApp);
    }

    @Bean
    public FirebaseRemoteConfig firebaseRemoteConfig(FirebaseApp app) throws FirebaseRemoteConfigException {
        FirebaseRemoteConfig config = FirebaseRemoteConfig.getInstance(app);
        return config;
    }

    @Bean
    public RemoteConfigServiceImpl remoteConfigService(FirebaseApp app) {
        return new RemoteConfigServiceImpl(FirebaseRemoteConfig.getInstance(app));
    }
}
