package com.bees360.common.excel.easyexcel;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.DateUtil;

import com.alibaba.excel.converters.date.DateNumberConverter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * <AUTHOR>
 * @date 2019/11/14 18:57
 */
@Slf4j
public class DateTimeZoneNumberConverter extends DateNumberConverter {

    private final String globalTimeZoneId;

    public DateTimeZoneNumberConverter() {
        this(null);
    }

    public DateTimeZoneNumberConverter(String timeZoneId) {
        super();
        this.globalTimeZoneId = timeZoneId;
    }

    @Override
    public Date convertToJavaData(
            ReadCellData<?> cellData,
            ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration) {

        TimeZone timeZone = getTimeZone(contentProperty);
        boolean use1904windowing = getUse1904windowing(contentProperty, globalConfiguration);

        return DateUtil.getJavaDate(
                cellData.getNumberValue().doubleValue(), use1904windowing, timeZone);
    }

    @Override
    public WriteCellData<?> convertToExcelData(
            Date value,
            ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration) {

        TimeZone timeZone = getTimeZone(contentProperty);
        Calendar calendar = getCalendar(value, timeZone);

        boolean use1904windowing = getUse1904windowing(contentProperty, globalConfiguration);

        return new WriteCellData(
                BigDecimal.valueOf(DateUtil.getExcelDate(calendar, use1904windowing)));
    }

    private TimeZone getTimeZone(ExcelContentProperty contentProperty) {
        if(contentProperty == null) {
            return null;
        }
        String timeZoneId = DateTimeZoneUtil.getTimeZone(contentProperty.getField(), globalTimeZoneId);
        return TimeZone.getTimeZone(timeZoneId);
    }

    private Calendar getCalendar(Date date, TimeZone timeZone) {
        Calendar calStart = Calendar.getInstance();
        calStart.setTime(date);
        if(timeZone != null) {
            calStart.setTimeZone(timeZone);
        }

        return calStart;
    }

    private boolean getUse1904windowing(ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (contentProperty == null || contentProperty.getDateTimeFormatProperty() == null) {
            return contentProperty.getDateTimeFormatProperty().getUse1904windowing();
        } else {
            return globalConfiguration.getUse1904windowing();
        }
    }
}
