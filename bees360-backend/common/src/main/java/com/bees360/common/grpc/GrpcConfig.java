package com.bees360.common.grpc;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class GrpcConfig {

    //服务协议(自定义)
    private String schema;
    private int port;

    //负载均衡算法
    private String loadBalancingPolicy;

    //客户端要连接的服务配置: 多个ip以逗号分隔 ip1:port1,ip2:port2
    private String endpoints;

    private int maxRetryAttempts;

    private boolean permitKeepAliveWithoutCalls;
    // unit: minute
    private int keepAliveTime;
    private int keepAliveTimeout;
    private int handshakeTimeout;

    //unit: second
    private double initialBackoff;
    private double maxBackoff;
    private double backoffMultiplier;

    public int getServerCount() {
        if (endpoints == null || "".equals(endpoints)) {
            throw new IllegalStateException("The config item of endpoints for 3d must be not null or empty.");
        }
        return endpoints.split(",").length;
    }
}
