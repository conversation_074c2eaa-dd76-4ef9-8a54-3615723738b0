package com.bees360.common.file;

import org.apache.commons.codec.digest.DigestUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2019/08/07s
 */
public class FileDigestUtils {

    /**
     * Get the md5 value of the image <code>File</code>
     *
     * @param image
     * @return
     * @throws IOException
     */
    public static String getMD5(File image) throws IOException {
        try (FileInputStream input = new FileInputStream(image)) {
            return DigestUtils.md5Hex(input);
        }
    }

    public static String getMD5(byte[] bytes) throws IOException {
        if(bytes == null) {
            throw new IllegalArgumentException("bytes == null");
        }
        return DigestUtils.md5Hex(bytes);
    }

    /**
     * Get the sha1 value of the image <code>File</code>
     *
     * @param image
     * @return
     * @throws IOException
     */
    public static String getSha1(File image) throws IOException {
        try (FileInputStream input = new FileInputStream(image)) {
            return DigestUtils.shaHex(input);
        }
    }

    public static String getSha1(byte[] bytes) throws IOException {
        if(bytes == null) {
            throw new IllegalArgumentException("bytes == null");
        }
        return DigestUtils.shaHex(bytes);
    }

    /**
     * Get the sha256 value of the image <code>File</code>
     *
     * @param image
     * @return
     * @throws IOException
     */
    public static String getSha256(File image) throws IOException {
        try (FileInputStream input = new FileInputStream(image)) {
            return DigestUtils.sha256Hex(input);
        }
    }

    public static String getSha256(byte[] bytes) throws IOException {
        if(bytes == null) {
            throw new IllegalArgumentException("bytes == null");
        }
        return DigestUtils.sha256Hex(bytes);
    }

}
