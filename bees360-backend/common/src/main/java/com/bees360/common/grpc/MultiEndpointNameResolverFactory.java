package com.bees360.common.grpc;

import io.grpc.EquivalentAddressGroup;
import io.grpc.NameResolver;
import java.net.SocketAddress;
import java.net.URI;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定义服务发现方式
 */
public class MultiEndpointNameResolverFactory extends NameResolver.Factory {

    final List<EquivalentAddressGroup> endpoints;

    /**
     * 服务地址
     * @param addresses
     */
    public MultiEndpointNameResolverFactory(List<SocketAddress> addresses) {
        this.endpoints = addresses.stream()
            .map(EquivalentAddressGroup::new)
            .collect(Collectors.toList());
    }

    /**
     * 定义服务发现类
     * @param targetUri
     * @param args
     * @return
     */
    public NameResolver newNameResolver(URI targetUri, NameResolver.Args args) {
        return new MultiEndpointNameResolver(endpoints);
    }

    /**
     * 定义服务协议
     * @return
     */
    @Override
    public String getDefaultScheme() {
        return "endpoints";
    }
}
