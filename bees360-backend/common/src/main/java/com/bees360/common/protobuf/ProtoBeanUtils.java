package com.bees360.common.protobuf;

import java.io.IOException;

import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;

/**
 * 相互转化的两个对象的getter和setter字段要完全的匹配。
 * 此外，对于ProtoBean中的enum和bytes，与POJO转化时遵循如下的规则：
 * <ol>
 *     <li>enum -> String</li>
 *     <li>bytes -> String</li>
 * </ol>
 * <AUTHOR> <PERSON>
 * @date 2019/08/18 23:44
 */
@Slf4j
public class ProtoBeanUtils {

    /**
     * 将ProtoBean对象转化为POJO对象
     *
     * @param targetPojoClass 目标POJO对象的类类型
     * @param sourceMessage 含有数据的ProtoBean对象实例
     * @param <PojoType> 目标POJO对象的类类型范型
     * @return
     * @throws IOException
     */
    public static <PojoType> PojoType toPojoBean(Class<PojoType> targetPojoClass, Message sourceMessage)
            throws IOException {
        String json = JsonFormat.printer().print(sourceMessage);
        return new Gson().fromJson(json, targetPojoClass);
    }

    /**
     * 将POJO对象转化为ProtoBean对象
     *
     * @param targetBuilder 目标Message对象的Builder类
     * @param sourcePojoBean 含有数据的POJO对象
     * @return
     * @throws IOException
     */
    public static void toProtoBean(Message.Builder targetBuilder, Object sourcePojoBean) throws IOException {
        if (sourcePojoBean == null) {
            return;
        }
        String json = new Gson().toJson(sourcePojoBean);
        log.debug("ProotBeanUtils.toProtoBean#json: {}", json);
        JsonFormat.parser().ignoringUnknownFields().merge(json, targetBuilder);
    }

    public static Message toProtoMessage(Message.Builder targetBuilder, Object sourcePojoBean) throws IOException {
        if (sourcePojoBean == null) {
            return targetBuilder.build();
        }
        String json = new Gson().toJson(sourcePojoBean);
        JsonFormat.parser().ignoringUnknownFields().merge(json, targetBuilder);
        return targetBuilder.build();
    }

}
