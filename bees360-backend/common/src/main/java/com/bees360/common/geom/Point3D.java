package com.bees360.common.geom;

/**
 * <AUTHOR>
 * @date 2019/07/22
 */

public class Point3D extends Point {

    private double z;

    public Point3D() {
        this(0, 0, 0);
    }

    public Point3D(double x, double y) {
        this(x, y, 0);
    }

    public Point3D(double x, double y, double z) {
        super(x, y);
        this.z = z;
    }

    public double getZ() {
        return z;
    }

    public void setZ(double z) {
        this.z = z;
    }

    @Override
    public double[] values(){
        return new double[]{x, y, z};
    }

    @Override
    public double[] toArray() {
        return values();
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        long temp;
        temp = Double.doubleToLongBits(x);
        result = prime * result + (int)(temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(y);
        result = prime * result + (int)(temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(z);
        result = prime * result + (int)(temp ^ (temp >>> 32));
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        Point3D other = (Point3D)obj;
        if (Double.doubleToLongBits(x) != Double.doubleToLongBits(other.x))
            return false;
        if (Double.doubleToLongBits(y) != Double.doubleToLongBits(other.y))
            return false;
        if (Double.doubleToLongBits(z) != Double.doubleToLongBits(other.z))
            return false;
        return true;
    }

    @Override
    public String toString() {
        return "Point3D [z=" + z + ", x=" + x + ", y=" + y + "]";
    }

    @Override
    public Point3D clone() {
        return (Point3D) super.clone();
    }
}
