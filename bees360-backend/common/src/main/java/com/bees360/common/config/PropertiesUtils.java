package com.bees360.common.config;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

public class PropertiesUtils {

	/***
	 * relative path to classes
	 * @param filePath
	 * @return
	 */
	public static Properties getResourcesProperties(String filePath){
		Properties properties = new Properties();
		InputStream is = null;
		try {
			is = PropertiesUtils.class.getClassLoader().getResourceAsStream(filePath);
			properties.load(is);
		} catch (IOException e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}finally {
			try {
				is.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return properties;
	}

	public static Properties getProperties(File file) {
		Properties properties = new Properties();
		FileInputStream fis = null;
		try {
			fis = new FileInputStream(file);
			properties.load(fis);
		} catch (IOException e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}finally {
			try {
				fis.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			file = null;
		}
		return properties;
	}

	public static void main(String[] args) {
		File file = new File("/home/<USER>/project/bees360/bees360-web-backend/bees360-service/src/main/resources/message/emailSubject.properties");
		Properties ps = PropertiesUtils.getProperties(file);
		System.out.println(ps.getProperty("contact-us"));
	}
}
