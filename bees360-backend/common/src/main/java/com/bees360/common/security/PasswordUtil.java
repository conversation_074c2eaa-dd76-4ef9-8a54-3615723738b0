package com.bees360.common.security;

public class PasswordUtil {

    private static Encrypter encrypter = new Md5Tool();

    /**
     * use to encrypt password instead of Md5Tool or other tools.
     */
    public static String encrypt(String password) {
        return encrypter.encrypt(password);
    }

    /**
     * compare two password directly
     *
     * @param rawPasswd
     *            password before encrypted
     * @param encryptedPasswd
     *            password encrypted
     *
     * @return
     */
    public static boolean isValidate(String rawPasswd, String encryptedPasswd) {
        return encrypter.validate(rawPasswd, encryptedPasswd);
    }
}
