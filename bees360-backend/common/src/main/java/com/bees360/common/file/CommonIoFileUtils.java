package com.bees360.common.file;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import com.bees360.common.image.ImageResolutionTypeEnum;
import org.apache.commons.io.FileUtils;

public class CommonIoFileUtils {

    /**
     * 删除根目录下除excludedDirectoryNames之外的其它目录
     *
     * @param rootDirectoryPath
     * @param excludedDirectoryNames
     */
    public static void removeSubDirectories(String rootDirectoryPath, List<String> excludedDirectoryNames) {
        if (rootDirectoryPath == null || excludedDirectoryNames == null) {
            return;
        }
        File rootDirectory = new File(rootDirectoryPath);
        if (!rootDirectory.exists()) {
            return;
        }
        FileFilter excludeDirFilter = pathname -> {
            if (pathname.isDirectory() && !excludedDirectoryNames.contains(pathname.getName())) {
                return true;
            }
            return false;
        };
        File[] subDirectories = rootDirectory.listFiles(excludeDirFilter);
        if (subDirectories.length == 0) {
            return;
        }
        for (File subDirectory : subDirectories) {
            try {
                FileUtils.deleteDirectory(subDirectory);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void main(String[] args) {
        String projectRootPath = "/var/bees360/tmp/download/images/1003294";
        CommonIoFileUtils.removeSubDirectories(projectRootPath,
            Arrays.asList(ImageResolutionTypeEnum.ORIGIN.getDisplay()));
    }
}
