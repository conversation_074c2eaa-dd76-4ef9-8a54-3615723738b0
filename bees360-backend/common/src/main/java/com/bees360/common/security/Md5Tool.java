package com.bees360.common.security;

import com.google.common.hash.Hashing;
import com.google.common.io.Files;
import java.io.File;
import java.io.IOException;
import java.security.MessageDigest;

/**
 * if you want to encrypt password, please use PasswordUtil.
 */
public class Md5Tool implements Encrypter {

    private final String ALGORITHM = "MD5";
    private final String DEFAULT_CHARSET = "utf-8";

    @Override
    public String encrypt(String raw) {
        return getMd5(raw);
    }

    @Override
    public boolean validate(String raw, String hash) {
        if (raw == hash) {
            return true;
        }
        if (raw == null || hash == null) {
            return false;
        }
        try {
            byte[] rawEcryptedBytes = getMd5(raw).getBytes(DEFAULT_CHARSET);
            return MessageDigest.isEqual(rawEcryptedBytes, hash.getBytes(DEFAULT_CHARSET));
        } catch (Exception e) {
            // 这个异常是不会发生的
            throw new AssertionError(e);
        }
    }

    private byte[] getMd5Bytes(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            return md.digest(input.getBytes(DEFAULT_CHARSET));
        } catch (Exception e) {
            // 这个异常是不会发生的
            throw new AssertionError(e);
        }
    }

    /**
     * 将字符串转化为Md5字符串
     */
    private String getMd5(String input) {
        byte[] bytes = getMd5Bytes(input);
        return toHex(bytes);
    }

    /**
     * 将bytes转化为十六进制的字符串
     */
    private String toHex(byte[] bytes) {
        final char[] HEX_DIGITS = "0123456789ABCDEF".toCharArray();
        StringBuilder ret = new StringBuilder(bytes.length * 2);
        for (int i = 0; i < bytes.length; i++) {
            ret.append(HEX_DIGITS[(bytes[i] >> 4) & 0x0f]);
            ret.append(HEX_DIGITS[bytes[i] & 0x0f]);
        }
        return ret.toString();
    }

    /**
     * 获取文件的checksum
     * @param file
     * @return
     */
    public String getChecksum(File file) {
        if(file == null) {
            return "";
        }
        try {
            return Files.asByteSource(file).hash(Hashing.goodFastHash(64)).toString();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static void main(String[] args) {
        Encrypter encrypter = new Md5Tool();

        String password = "asdf12a$#!@)(*_#%34";
        String passwordEncrypted = encrypter.encrypt(password);

        System.out.println(passwordEncrypted);
        System.out.println(encrypter.validate(password, passwordEncrypted));

        // md5 file
        long start = System.currentTimeMillis();
        String zipFile = "/home/<USER>/DataSet/House/21697.zip";
        File file = new File(zipFile);
        System.out.println("length:" + file.length());
        System.out.println(new Md5Tool().getChecksum(file));
        long end = System.currentTimeMillis();
        System.out.println("time:" + (end - start));
    }
}
