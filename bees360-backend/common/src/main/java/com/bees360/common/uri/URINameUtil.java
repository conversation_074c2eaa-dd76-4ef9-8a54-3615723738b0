package com.bees360.common.uri;

import org.apache.commons.lang3.StringUtils;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;

/**
 * <AUTHOR>
 * @date 2019/09/27 18:52
 */
public class URINameUtil {
    public static String getUrlName(String url) {

        if(StringUtils.isEmpty(url)) {
            return "";
        }
        String path = "";
        try {
            URI uri = new URI(url);
            path = uri.getPath();
        } catch (URISyntaxException e) {
            return "";
        }
        int i = path.lastIndexOf("/");
        if(i < 0) {
            return "";
        }
        return path.substring(i + 1);
    }

    public static String getDomainWithSchemaAndPort(String url) {
        try {
            URI uri = new URI(url);
            String path = uri.getRawPath();
            int index = url.indexOf(path);
            if(index > 0) {
                return url.substring(0, index);
            } else {
                return "";
            }
        } catch (URISyntaxException e) {
            return "";
        }
    }
}
