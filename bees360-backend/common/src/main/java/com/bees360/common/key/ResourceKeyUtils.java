package com.bees360.common.key;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 * 对资源key进行处理
 */
public class ResourceKeyUtils {

    private static final String SLASH = "/";
    private static final String DOT = ".";

    /**
     * 从key分离出path和文件名
     * @param key project/44868/3DConstruction/workspace/pmvs/models/final.ply
     * @return [project/44868/3DConstruction/workspace/pmvs/models, final.ply]
     */
    public static String[] getPathAndNameFromKey(String key) {
        if(StringUtils.isEmpty(key)) {
            throw new IllegalArgumentException("The key must be not null.");
        }
        int index = key.lastIndexOf(SLASH);
        return new String[]{key.substring(0, index), key.substring(index + 1)};
    }

    /**
     * 在原来的资源名(文件名)的基础上加上一个时间戳,生成一个新的资源名(文件名)
     * @param resourceName
     * @return
     */
    public static String getNewNameWithTimestamp(String resourceName) {
        if(StringUtils.isEmpty(resourceName)) {
            throw new IllegalArgumentException("The resourceName must be not null.");
        }
        int index = resourceName.indexOf(DOT);
        String prefix = resourceName.substring(0, index);
        String postfix = resourceName.substring(index);
        return prefix + "-" + Instant.now().toEpochMilli() + postfix;
    }
}
