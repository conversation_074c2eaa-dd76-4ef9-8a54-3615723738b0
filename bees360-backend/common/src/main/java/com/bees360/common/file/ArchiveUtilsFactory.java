package com.bees360.common.file;

/**
 * 创建不同的Archive
 */
public class ArchiveUtilsFactory {

    public static final String ARCHIVE_TAR = "tar";
    public static final String ARCHIVE_ZIP = "zip";

    /**
     *
     * @param archiveName 压缩包名称
     * @return
     */
    public ArchiveUtils createArchiveUtils(String archiveName) {
        if(ARCHIVE_TAR.equals(archiveName)) {
            return new TarUtils();
        }
        else if(ARCHIVE_ZIP.equals(archiveName)) {
            return new ZipUtils();
        } else {
            //TODO
            throw new RuntimeException("archive " + archiveName + " is not supported currently");
        }
    }

}
