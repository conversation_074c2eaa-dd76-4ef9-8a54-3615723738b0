package com.bees360.common.string;

public class CharacterUtil {

    public static boolean isUpperCase(char c) {
        return c >=65 && c <= 90;
    }

    public static boolean isLowerCase(char c) {
        return c >=97 && c <= 122;
    }

    public static char toUpper(char c) {
        return c >=65 && c <= 90 ? c : (char)(c - 32);
    }

    public static char toLower(char c) {
        return c >=97 && c <= 122 ? c : (char)(c + 32);
    }

    public static void main(String[] args) {
        String method = "getName";
        method = method.replace("get", "");
        char firstLetter = method.charAt(0);
        if(CharacterUtil.isUpperCase(firstLetter)) {
            method = CharacterUtil.toLower(firstLetter) + method.substring(1);
        }
        System.out.println(method);

    }
}
