package com.bees360.common.thread;

import java.util.Set;
import java.util.concurrent.Future;

public interface ThreadManager {

    /**
     * 查看有总共有多少个线程
     * @return
     */
    public Iterable<Future> list();

    /**
     * 目前容器里线程key值
     * @return
     */
    public Set<String> keys();

    /**
     * 是否存在线程
     * @param id
     * @return
     */
    public boolean exists(String id);

    /**
     * 获取当前线程
     * @param id
     * @return
     */
    public Future get(String id);

    /**
     * 取消一个线程
     * @param future
     */
    public void add(String id, Future future);

    /**
     * 取消一个线程
     * @param id
     */
    public void stop(String id);

    /**
     * 移除一个线程
     * @param id
     */
    public void remove(String id);

    /**
     * 当前容器线程是否为空
     * @return
     */
    public boolean isEmpty();
}
