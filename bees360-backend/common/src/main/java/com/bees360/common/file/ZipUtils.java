package com.bees360.common.file;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.ArchiveException;
import org.apache.commons.compress.archivers.ArchiveInputStream;
import org.apache.commons.compress.archivers.ArchiveOutputStream;
import org.apache.commons.compress.archivers.ArchiveStreamFactory;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.compress.compressors.CompressorException;
import org.apache.commons.compress.compressors.CompressorInputStream;
import org.apache.commons.compress.compressors.CompressorOutputStream;
import org.apache.commons.compress.compressors.CompressorStreamFactory;
import org.apache.commons.compress.compressors.deflate.DeflateCompressorInputStream;
import org.apache.commons.compress.compressors.deflate.DeflateCompressorOutputStream;
import org.apache.commons.compress.compressors.deflate.DeflateParameters;
import org.apache.commons.compress.compressors.gzip.GzipCompressorOutputStream;
import org.apache.commons.io.IOUtils;

/**
 * add file to zip package and unpack zip to directory
 */
public class ZipUtils extends AbstractArchiveUtils {

    public static final String COMPRESSOR_DEFLATE = "DEFLATE";
    public static final String ARCHIVE_ZIP = "zip";
    public static final String SUFFIX_ZIP = ".zip";
    private static final String ENCODING_UTF8 = "UTF-8";

    private String compressor;

    private static ArchiveStreamFactory archiveStreamFactory;

    public ZipUtils() {
        this(COMPRESSOR_DEFLATE);
    }

    private static ArchiveStreamFactory getArchiveStreamFactory() {
        if (archiveStreamFactory == null) {
            synchronized (ZipUtils.class) {
                if (archiveStreamFactory == null) {
                    archiveStreamFactory = new ArchiveStreamFactory();
                }
            }
        }
        return archiveStreamFactory;
    }

    /**
     * this version just support DEFLATE compressor
     *
     * @param compressor
     */
    private ZipUtils(String compressor) {
        this.compressor = compressor;
    }

    @Override
    public void unpackArchive(String archivePath, String targetDir) throws IOException, ArchiveException {
        super.unpackArchive(archivePath, targetDir);
    }

    @Override
    protected String getCompressor() {
        return COMPRESSOR_DEFLATE;
    }

    @Override
    protected String getSuffix(String compressor) {
        return SUFFIX_ZIP;
    }

    @Override
    public ArchiveInputStream createArchiveInputStream(InputStream inputStream) throws ArchiveException {
        return getArchiveStreamFactory().createArchiveInputStream(ARCHIVE_ZIP, inputStream);
    }

    @Override
    public ArchiveOutputStream createArchiveOutputStream(OutputStream outputStream) throws ArchiveException {
        BufferedOutputStream bos = new BufferedOutputStream(outputStream);
        ArchiveOutputStream archiveOutputStream =
            new ArchiveStreamFactory().createArchiveOutputStream(ARCHIVE_ZIP, bos);
        ZipArchiveOutputStream zipArchiveOutputStream = (ZipArchiveOutputStream)archiveOutputStream;
        zipArchiveOutputStream.setMethod(ZipOutputStream.DEFLATED);
        zipArchiveOutputStream.setEncoding(ENCODING_UTF8);
        return zipArchiveOutputStream;
    }

    @Override
    protected ArchiveEntry createArchiveEntry(File file, String entryName) {
        ZipArchiveEntry archiveEntry = new ZipArchiveEntry(file, entryName);
        archiveEntry.setMethod(ZipEntry.DEFLATED);
        return archiveEntry;
    }

    @Override
    protected ArchiveEntry createArchiveEntry(String entryName) {
        ZipArchiveEntry archiveEntry = new ZipArchiveEntry(entryName);
        archiveEntry.setMethod(ZipEntry.DEFLATED);
        return archiveEntry;
    }

    @Override
    protected void setSize(ArchiveEntry archiveEntry, long size) {
        if (archiveEntry == null) {
            return;
        }
        ZipArchiveEntry zipArchiveEntry = (ZipArchiveEntry)archiveEntry;
        zipArchiveEntry.setSize(size);
    }

    public static void main(String[] args) {
        String archivePath = test_addFilesToArchive();
        test_unpackArchive(archivePath);
    }

    public static String test_addFilesToArchive() {
        String sourceDir = "/var/bees360/tmp/download/images/1003291";
        String archivePath = null;
        try {
            archivePath = new ZipUtils().addFilesToArchiveWithExcludedPaths(sourceDir,
                Arrays.asList(new String[] {"lower", "high/temp"}));
            File file = new File(archivePath);
            // 166384781
            System.out.println("archive length:" + file.length());
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ArchiveException e) {
            e.printStackTrace();
        }
        return archivePath;
    }

    private static void test_unpackArchive(String archivePath) {
        try {
            new ZipUtils().unpackArchive(archivePath, "/var/bees360/tmp/download");
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ArchiveException e) {
            e.printStackTrace();
        }
    }
}
