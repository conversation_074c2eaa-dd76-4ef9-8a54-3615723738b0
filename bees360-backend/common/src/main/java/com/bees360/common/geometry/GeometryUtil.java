package com.bees360.common.geometry;

import com.alibaba.fastjson.JSONArray;
import com.bees360.common.geom.Point;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

public class GeometryUtil {

    public static String toPoint(double x, double y) {
        return "POINT(" + x + " " + y + ")";
    }

    public static String toPoint(String pointString) {
        if (pointString.contains("POINT")) {
            return pointString;
        }
        StringBuilder pointBuilder = new StringBuilder("POINT(");
        JSONArray arrayPoint = JSONArray.parseArray(pointString);
        pointBuilder.append(arrayPoint.getString(0)).append(" ").append(arrayPoint.getString(1));
        pointBuilder.append(")");
        return pointBuilder.toString();
    }

    public static String toPolygon(String arrayString) {
        if (StringUtils.isEmpty(arrayString) || arrayString.contains("POLYGON")) {
            return arrayString;
        }
        StringBuilder polygonBuilder = new StringBuilder("POLYGON((");
        JSONArray arrayPolygon = JSONArray.parseArray(arrayString);
        for (int i = 0; i < arrayPolygon.size(); i++) {
            JSONArray arrayPoint = arrayPolygon.getJSONArray(i);
            polygonBuilder.append(arrayPoint.getString(0)).append(" ").append(arrayPoint.getString(1)).append(", ");
        }
        polygonBuilder.setLength(polygonBuilder.length() - 2);
        polygonBuilder.append("))");
        return polygonBuilder.toString();
    }

    /**
     * @param geomText a string format like "POINT(-1 1)", "LINESTRING(0 0,1 1,2 2)" or "POLYGON((1
     *     1,8 9,0 9,1 1))". Make sure that POLYGON must have only one ring.
     * @return points
     */
    public static List<Point> parseGeometryText(String geomText) {
        if (geomText == null) {
            return null;
        }
        List<Point> points = new ArrayList<>();
        // index of first digit
        int left = 0;
        while (left < geomText.length()
                && !Character.isDigit(geomText.charAt(left))
                && geomText.charAt(left) != '-') {
            left++;
        }
        // index of last digit
        int right = geomText.length() - 1;
        while (0 <= right && !Character.isDigit(geomText.charAt(right))) {
            right--;
        }
        String pointString = geomText.substring(left, right + 1);
        String[] numberPairs = pointString.split(",");
        for (String numPair : numberPairs) {
            Point point = new Point();
            String[] nums = numPair.trim().split("\\s+");
            point.setX(Double.parseDouble(nums[0]));
            point.setY(Double.parseDouble(nums[1]));
            points.add(point);
        }
        return points;
    }

    public static String polygonToText(List<Point> points) {
        if (points == null || points.size() == 0) {
            return null;
        }
        StringBuilder polygon = new StringBuilder("POLYGON((");
        for (int i = 0; i < points.size(); i++) {
            Point p = points.get(i);
            polygon.append(p.getX()).append(" ").append(p.getY());
            if (i < points.size() - 1) {
                polygon.append(",");
            }
        }
        Point firstPoint = points.get(0);
        Point lastPoint = points.get(points.size() - 1);
        if (firstPoint.getX() != lastPoint.getX() || firstPoint.getY() != lastPoint.getY()) {
            // The last point is not same as first one. Append the first point to the list.
            polygon.append(",").append(firstPoint.getX()).append(" ").append(firstPoint.getY());
        }
        polygon.append("))");
        return polygon.toString();
    }

    public static void main(String[] args) {
        String a = toPoint("[-1.8032525929073704,5.353071977058222]");
        System.out.println(a);
    }
}
