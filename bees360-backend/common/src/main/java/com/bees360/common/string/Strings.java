package com.bees360.common.string;

public class Strings {

    public static String CamelCaseToUnderScore(String src){

        StringBuilder newStr = new StringBuilder();
        if (src == null){
            return newStr.toString();
        }
        for (int i = 0; i < src.length(); i++){
            final char c = src.charAt(i);
            if (CharacterUtil.isUpperCase(c)){
                if (i == 0){
                    newStr.append(CharacterUtil.toLower(c));
                } else{
                    newStr.append("_").append(CharacterUtil.toLower(c));
                }
            }else{
                newStr.append(c);
            }
        }
        return newStr.toString();
    }


    public static void main(String[] args) {
        System.out.println(Strings.CamelCaseToUnderScore(null));
        System.out.println(Strings.CamelCaseToUnderScore("daysOld"));
    }
}
