package com.bees360.common.geom;

import java.util.ArrayList;
import java.util.List;

public class Transform3DUtil {

	public static Point twoDPointToAnother(Point p, double[][] camPropMatrixSrc, double[][] camPropMatrixDest, double[] planeCoefArr){
		double[] point2D = new double[]{p.getX(), p.getY()};
		double[] point3D = Transform3DUtil.twoDPointTo3D(point2D, camPropMatrixSrc, planeCoefArr);
		point2D = Transform3DUtil.threeDPointTo2D(point3D, camPropMatrixDest);

		return new Point(point2D[0], point2D[1]);
	}

	/**
	 *
	 * @param point2D
	 * @param camPropMatrix an 3*3 size double matrix
	 * @param planeCoefArr an size 4 double array
	 * @return
	 */
	public static Point3D twoDPointTo3D(Point point2D, double[][] camPropMatrix, double[] planeCoefArr) {
		double[] point3DArr = twoDPointTo3D(
				new double[]{point2D.getX(), point2D.getY()},
				camPropMatrix,
				planeCoefArr);
		return new Point3D(point3DArr[0],point3DArr[1],point3DArr[2]);
	}

	/**
	 *
	 * @param point2D　an size 2 double array
	 * @param camPropMatrix an 3*3 size double matrix
	 * @param planeCoefArr an size 4 double array
	 * @return
	 */
	public static double[] twoDPointTo3D(double[] point2D, double[][] camPropMatrix, double[] planeCoefArr) {
		double[][] tempMatrix = new double[3][3];
		for(int i = 0; i<3; i++){
			tempMatrix[0][i] = camPropMatrix[0][i] - camPropMatrix[2][i]*point2D[0];
	    }
		for(int i = 0; i<3; i++){
			tempMatrix[1][i] = camPropMatrix[1][i] - camPropMatrix[2][i]*point2D[1];
	    }
		for(int i = 0; i<3; i++){
			tempMatrix[2][i] = planeCoefArr[i];
	    }

		double[] tempArr = new double[3];
		tempArr[0] = camPropMatrix[2][3] * point2D[0] - camPropMatrix[0][3];
		tempArr[1] = camPropMatrix[2][3] * point2D[1] - camPropMatrix[1][3];
		tempArr[2] = -planeCoefArr[3];

		return multiplyMatrixes(inverseMatrix(tempMatrix), tempArr);
	}

	/**
	 *
	 * @param point3D
	 * @param camPropMatrix an 3*3 size double matrix
	 * @return
	 */
	public static Point threeDPointTo2D(Point3D point3D, double[][] camPropMatrix) {
		double[] point2DArr = threeDPointTo2D(
				new double[]{point3D.getX(), point3D.getY(), point3D.getZ()},
				camPropMatrix);

		return new Point(point2DArr[0], point2DArr[1]);
	}

	/**
	 *
	 * @param point3D an size 3 double array
	 * @param camPropMatrix an 3*3 size matrix
	 * @return  an size 2 double array
	 */
	public static double[] threeDPointTo2D(double[] point3D, double[][] camPropMatrix) {
		double[] projBoundary = new double[4];
        for(int i = 0; i < 3; i ++){
        	projBoundary[i] = point3D[i];
        }
        projBoundary[3] = 1;

        projBoundary = multiplyMatrixes(camPropMatrix, projBoundary);
        double x = projBoundary[0]/projBoundary[2];
        double y = projBoundary[1]/projBoundary[2];
        return new double[]{x, y};
	}

	/**
	 *
	 * @param matrix an 3*3 matrix
	 * @return an 3*3 matrix
	 */
	private static double[][] inverseMatrix(double[][] matrix) {
		double[][] result = new double[3][3];

		double a1 = matrix[0][0], a2 = matrix[1][0], a3 = matrix[2][0],
		b1 = matrix[0][1], b2 = matrix[1][1], b3 = matrix[2][1],
		c1 = matrix[0][2], c2 = matrix[1][2], c3 = matrix[2][2];
		double coefficient =  1.0 / ( a1*(b2*c3-c2*b3) - a2*(b1*c3-c1*b3) + a3*(b1*c2-c1*b2) );

		result[0][0] = (b2*c3 - c2*b3)*coefficient;
		result[0][1] = (c1*b3 - b1*c3)*coefficient;
		result[0][2] = (b1*c2 - c1*b2)*coefficient;

		result[1][0] = (c2*a3 - a2*c3)*coefficient;
		result[1][1] = (a1*c3 - c1*a3)*coefficient;
		result[1][2] = (c1*a2 - a1*c2)*coefficient;

		result[2][0] = (a2*b3 - b2*a3)*coefficient;
		result[2][1] = (b1*a3 - a1*b3)*coefficient;
		result[2][2] = (a1*b2 - b1*a2)*coefficient;

		return result;
	}

	private static double[] multiplyMatrixes(double[][] matrixInverse, double[] array) {
		double[] result = new double[array.length];
        for(int i = 0; i< matrixInverse.length; i++){
        	result[i] = 0;
          for(int j = 0; j< array.length; j++){
        	  result[i] += matrixInverse[i][j] * array[j];
          }
        }
        return result;
	}

	public static Point3D converseCoordinateSystem(Point3D originalPoint, double[][] planePropArr){
		double x = originalPoint.getX();
		double y = originalPoint.getY();
		double z = originalPoint.getZ();
		double xNew = x * planePropArr[0][0] + y * planePropArr[0][1] + z * planePropArr[0][2];
		double yNew = x * planePropArr[1][0] + y * planePropArr[1][1] + z * planePropArr[1][2];
		double zNew = x * planePropArr[2][0] + y * planePropArr[2][1] + z * planePropArr[2][2];
		Point3D resultPoint = new Point3D(xNew, yNew, zNew);
		return resultPoint;
	}
	public static void coordinateSystemUnitization(double[][] planePropArr){
		for(int i = 0; i < planePropArr.length; i++){
			double vectorModeLength = 0;
			for(int j = 0; j < planePropArr[i].length; j++){
				vectorModeLength += Math.pow(planePropArr[i][j], 2);
			}
			vectorModeLength = Math.sqrt(vectorModeLength);
			for(int j = 0; j < planePropArr[i].length; j++){
				planePropArr[i][j] = planePropArr[i][j] / vectorModeLength;
			}
		}
	}
	public static double[][] change3DCoordinateSystem(double[][] srcPlanePropArr, double[][] destPlanePropArr){
		double[][] resultPlanePropArr = new double[3][3];
		for(int i = 0; i < 3; i++){
			Point3D point3d = new Point3D(srcPlanePropArr[i][0], srcPlanePropArr[i][1], srcPlanePropArr[i][2]);
			point3d = converseCoordinateSystem(point3d, destPlanePropArr);
			resultPlanePropArr[i][0] = point3d.getX();
			resultPlanePropArr[i][1] = point3d.getY();
			resultPlanePropArr[i][2] = point3d.getZ();
		}
		return resultPlanePropArr;
	}

	public static List<Point3D> twoDPointsTo3D(List<Point> facetBoundary, double[][] camPropMatrix,
			double[] planeCoefArr) {
		List<Point3D> ps = new ArrayList<Point3D>();

		for(Point p: facetBoundary) {
			double[] xyz = twoDPointTo3D(p.values(), camPropMatrix, planeCoefArr);
			ps.add(new Point3D(xyz[0], xyz[1], xyz[2]));
		}
		return ps;
	}

	public static void main(String args[]){
		Point3D point = new Point3D(1,2,3);
		double [][]planePropArr = {{0.08382346761960975, 0.9963024717073422, -0.01884173946493871},{-0.9346336133946166,0.07204925103256729,-0.3482368649907249},{-0.34559170100383535,0.04680054613594089,0.9372172027223216}};
		double [][]planePropArr2 = {{1,0,0},{0,1,0},{0,0,1}};
		coordinateSystemUnitization(planePropArr);

		Point3D newPoint = converseCoordinateSystem(point, planePropArr);
		System.out.println(newPoint.getX() + " " + newPoint.getY() + " " + newPoint.toString());
		double[][] newPlanePropArr =change3DCoordinateSystem(planePropArr2, planePropArr);
		coordinateSystemUnitization(newPlanePropArr);
		newPoint =  converseCoordinateSystem(newPoint, newPlanePropArr);
		System.out.println(newPoint.getX() + " " + newPoint.getY() + " " + newPoint.toString());

		/*for(int i = 0; i < 3; i++){
			for(int j = 0; j < 3; j++){
				System.out.print(planePropArr[i][j] + " ");
			}
			System.out.println();
		}
		coordinateSystemUnitization(planePropArr);
		for(int i = 0; i < 3; i++){
			for(int j = 0; j < 3; j++){
				System.out.print(planePropArr[i][j] + " ");
			}
			System.out.println();
		}*/
	}
}
