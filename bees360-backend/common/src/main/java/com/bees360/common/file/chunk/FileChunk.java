package com.bees360.common.file.chunk;


public class FileChunk {
    private long projectId;
    private int index;
    private byte[] bytes;

    public FileChunk() {

    }

    public FileChunk(int index, byte[] bytes) {
        this.index = index;
        this.bytes = bytes;
    }

    public int getIndex() {
        return index;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public long getProjectId() {
        return projectId;
    }
}
