package com.bees360.common.grpc;

import io.grpc.CompressorRegistry;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.NameResolver;
import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * GRPC客户端
 */
public class GrpcClient {

    private static final String SCHEMA = "endpoints";
    private static final String LOAD_BALANCING_POLICY = "round_robin";

    //base property
    private String schema;
    private String loadBalancingPolicy;
    private String endpoints;

    // extended property
    private GrpcConfig grpcConfig;

    private Map<String, Object> serviceConfig = new HashMap<>();

    protected GrpcClient(String endpoints) {
        this.endpoints = endpoints;
        this.schema = SCHEMA;
        this.loadBalancingPolicy = LOAD_BALANCING_POLICY;
    }

    /**
     * grpc客户端构造器
     */
    protected GrpcClient(GrpcConfig grpcConfig) {
        this.grpcConfig = grpcConfig;
        this.endpoints = grpcConfig.getEndpoints();
        if (grpcConfig.getSchema() == null) {
            grpcConfig.setSchema(SCHEMA);
        }
        this.schema = grpcConfig.getSchema();
        if (grpcConfig.getLoadBalancingPolicy() == null) {
            grpcConfig.setLoadBalancingPolicy(LOAD_BALANCING_POLICY);
        }
        this.loadBalancingPolicy = grpcConfig.getLoadBalancingPolicy();
        init();
    }

    private void init() {
        Map<String, Object> retryPolicy = new HashMap<>();
        if (grpcConfig.getMaxRetryAttempts() > 0) {
            retryPolicy.put("maxAttempts", Double.valueOf(grpcConfig.getMaxRetryAttempts()));
        }
        if (grpcConfig.getInitialBackoff() > 0) {
            retryPolicy.put("initialBackoff", grpcConfig.getInitialBackoff() + "s");
        }
        if (grpcConfig.getMaxBackoff() > 0) {
            retryPolicy.put("maxBackoff", grpcConfig.getMaxBackoff() + "s");
        }
        if (grpcConfig.getBackoffMultiplier() > 0) {
            retryPolicy.put("backoffMultiplier", grpcConfig.getBackoffMultiplier());
        }
        retryPolicy.put("retryableStatusCodes", Arrays.asList("UNAVAILABLE"));
        Map<String, Object> methodConfig = new HashMap<>();

        Map<String, Object> name = new HashMap<>();
        name.put("service", "api.aito3d.ThreeDReconstructionService");
        methodConfig.put("name", Collections.<Object>singletonList(name));

        methodConfig.put("retryPolicy", retryPolicy);
        methodConfig.put("waitForReady", true);

        serviceConfig.put("methodConfig", Collections.singletonList(methodConfig));

        //setting sticky request header parameter
        serviceConfig.put("stickinessMetadataKey", GrpcKey.KEY_STICKY_JOB_ID);
    }

    /**
     * the best practise for ManagedChannel is reuse Channel, so just build channel once in most cases
     *
     * @return
     */
    protected ManagedChannel buildChannel() {
        ManagedChannelBuilder managedChannelBuilder = getChannelBuilder();
        if (grpcConfig == null) {
            return managedChannelBuilder.maxInboundMessageSize(GrpcConstants.MAX_INBOUND_MESSAGE_SIZE).usePlaintext().build();
        } else {
            if (serviceConfig != null) {
                managedChannelBuilder.enableRetry().defaultServiceConfig(serviceConfig);
            }
            if (grpcConfig.getMaxRetryAttempts() > 0) {
                managedChannelBuilder.maxRetryAttempts(grpcConfig.getMaxRetryAttempts());
            }
            if (grpcConfig.getKeepAliveTime() > 0) {
                managedChannelBuilder.keepAliveTime(grpcConfig.getKeepAliveTime(), TimeUnit.MINUTES);
            }
            if (grpcConfig.getKeepAliveTimeout() > 0) {
                managedChannelBuilder.keepAliveTimeout(grpcConfig.getKeepAliveTimeout(), TimeUnit.MINUTES);
            }
            // @formatter:off
            return managedChannelBuilder.maxInboundMessageSize(GrpcConstants.MAX_INBOUND_MESSAGE_SIZE)
                .usePlaintext()
                .compressorRegistry(CompressorRegistry.getDefaultInstance())
                .keepAliveWithoutCalls(true).build();
            // @formatter:on
        }
    }

    /**
     * 创建带有服务发现和负载均衡策略的ManagedChannelBuilder
     *
     * @return
     */
    private ManagedChannelBuilder getChannelBuilder() {
        return ManagedChannelBuilder.forTarget(getSchema())
            .nameResolverFactory(getNameResolverFactory(getEndpoints()))
            .defaultLoadBalancingPolicy(getLoadBalancingPolicy());
    }

    private NameResolver.Factory getNameResolverFactory(String multiEndpointsConfig) {
        return new MultiEndpointNameResolverFactory(getEndpoints(multiEndpointsConfig));
    }

    private List<SocketAddress> getEndpoints(String endpointConfig) {
        String[] splits = endpointConfig.split(",");
        return Arrays.stream(splits)
            .map(address -> getEndpoint(address))
            .collect(Collectors.toList());
    }

    /**
     * 将ip:port解析成SocketAddress
     *
     * @param endpoint
     * @return
     */
    private SocketAddress getEndpoint(String endpoint) {
        String[] temps = endpoint.split(":");
        return new InetSocketAddress(temps[0], Integer.parseInt(temps[1]));
    }

    public String getSchema() {
        return this.schema;
    }

    public String getLoadBalancingPolicy() {
        return this.loadBalancingPolicy;
    }

    public String getEndpoints() {
        return this.endpoints;
    }

    public GrpcConfig getGrpcConfig() {
        return this.grpcConfig;
    }

    @Override
    public String toString() {
        StringBuffer buf = new StringBuffer();
        buf.append("GrpcClient service endpoints=").append(grpcConfig.getEndpoints());
        return buf.toString();
    }
}
