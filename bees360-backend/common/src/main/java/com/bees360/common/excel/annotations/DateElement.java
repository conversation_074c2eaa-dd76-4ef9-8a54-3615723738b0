package com.bees360.common.excel.annotations;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Target(METHOD)
@Retention(RUNTIME)
@Documented
public @interface DateElement {
	public enum DateTypeAllowed {
		;
		private static Set<String> dataTypeSet = new HashSet<String>(Arrays.asList("long", "string", "date"));

		public static boolean allow(String dataType) {
			return dataType == null? false: dataTypeSet.contains(dataType.toLowerCase());
		}
	}
}
