package com.bees360.common.resource;

/**
 * TODO 移除这个类，这个工具类的方法并不能用，没有必要放到这个类中。
 * <AUTHOR>
 * @date 2019/08/05
 *
 */
public class ResourceKeyManager {
    private ResourceKeyManager() {
    }

    private static final String ORIGINAL_RESOLUTIOIN_IMAGE_NAME = "origin";
    private static final String MIDDLE_RESOLUTION_IMAGE_NAME = "biggerThumbnails";
    private static final String LOWER_RESOLUTION_IMAGE_NAME = "thumbnails";
    private static final String ANNOTATION_IMAGE_NAME = "annotations";

    public static final int ORIGINAL_RESOLUTIOIN_IMAGE = 0;
    public static final int MIDDLE_RESOLUTION_IMAGE = 1;
    public static final int LOWER_RESOLUTION_IMAGE = 2;
    public static final int ANNOTATION_IMAGE = 3;

    public static String createKey(String originFileName, int keyType){
        switch(keyType){
            case ORIGINAL_RESOLUTIOIN_IMAGE:{
                return createKey(originFileName, ORIGINAL_RESOLUTIOIN_IMAGE_NAME);
            }
            case MIDDLE_RESOLUTION_IMAGE:{
                return createKey(originFileName, MIDDLE_RESOLUTION_IMAGE_NAME);
            }
            case LOWER_RESOLUTION_IMAGE:{
                return createKey(originFileName, LOWER_RESOLUTION_IMAGE_NAME);
            }
            case ANNOTATION_IMAGE:{
                return createKey(originFileName, ANNOTATION_IMAGE_NAME);
            }
        }
        return null;
    }

    private static String createKey(String originFileName, String placeholder) {
        return originFileName.replaceFirst(ORIGINAL_RESOLUTIOIN_IMAGE_NAME, placeholder);
    }

    public static String parseFileName(String key) {
        if(key == null) {
            return "";
        }
        int index = key.lastIndexOf('/');
        if(index < 0) {
            return key;
        }
        return key.substring(index + 1);
    }

    public static String parseFileDir(String keyOrUrl) {
        if(keyOrUrl == null) {
            return "";
        }
        int index = keyOrUrl.lastIndexOf('/');
        if(index < 0) {
            return keyOrUrl;
        }
        return keyOrUrl.substring(0, index + 1);
    }

}
