package com.bees360.common.image;

import org.apache.commons.io.IOUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;

/**
 * <AUTHOR>
 * @date 2019/08/06
 */
public class ImageUtil {

    /**
     * Get the contents of an <code>File</code> as a <code>byte[]</code>.
     *
     * @param image
     * @return
     * @throws IOException
     */
    public static byte[] toByteArray(File image) throws IOException {
        try (FileInputStream input = new FileInputStream(image)) {
            return IOUtils.toByteArray(input);
        }
    }

    public static byte[] toByteArray(BufferedImage bufferedImage, String formatName) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        ImageIO.write(bufferedImage, formatName, outputStream);
        return outputStream.toByteArray();
    }

    public static BufferedImage toBufferedImage(File image) {
        if (image.exists() && !image.isDirectory() && image.canRead()) {
            try {
                return ImageIO.read(image);
            } catch (IOException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * @param bytes The contents of an image <code>File</code> as a <code>byte[]</code>.
     * @return
     */
    public static BufferedImage toBufferedImage(byte[] bytes) throws IOException {
        if (bytes == null || bytes.length == 0) {
            throw new IllegalArgumentException("bytes == null or bytes is empty.");
        }
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        return ImageIO.read(inputStream);
    }

    public static boolean toImageFile(BufferedImage bufferedImage, String formatName, File destImage)
            throws IOException {
        return ImageIO.write(bufferedImage, formatName, destImage);
    }

    public static boolean toImageFile(byte[] imageBytes, String formatName, File destImage)
            throws IOException {
        return toImageFile(toBufferedImage(imageBytes), formatName, destImage);
    }

    /**
     * zoom the image
     *
     * @param originalImage The source image
     * @param ratio         the zoom value
     * @return
     */
    public static BufferedImage zoomImage(BufferedImage originalImage, double ratio) {
        int width = (int) (originalImage.getWidth() * ratio);
        int height = (int) (originalImage.getHeight() * ratio);

        return resizeImage(originalImage, width, height);
    }

    public static BufferedImage resizeImage(BufferedImage originalImage, int width, int height) {

        BufferedImage newImage = new BufferedImage(width, height, originalImage.getType());
        Graphics g = newImage.getGraphics();
        g.drawImage(originalImage, 0, 0, width, height, null);
        return newImage;
    }

    /**
     * crop the image according to the rectangle.
     *
     * @param originalImage
     * @param screenshot
     * @return
     */
    public static BufferedImage crop(BufferedImage originalImage, Rectangle screenshot) {
        int width = (int) screenshot.getWidth();
        int height = (int) screenshot.getHeight();

        BufferedImage newImage = new BufferedImage(width, height, originalImage.getType());
        Graphics g = newImage.getGraphics();
        g.drawImage(originalImage, -(int) screenshot.getX(), -(int) screenshot.getY(),
                originalImage.getWidth(), originalImage.getHeight(), null);
        return newImage;
    }

    /**
     * zoom the image and out put to a new file
     *
     * @param srcImage  the path of the source image
     * @param destImage the path of the destination image
     * @param ratio     the zoom value
     * @return true if success, otherwise false
     */
    public static boolean zoomImage(File srcImage, File destImage, double ratio) throws IOException {
        BufferedImage bufferedImage = toBufferedImage(srcImage);
        if (bufferedImage == null) {
            return false;
        }
        bufferedImage = zoomImage(bufferedImage, ratio);
        return toImageFile(bufferedImage, getFileExtension(srcImage), destImage);
    }

    public static boolean zoomImageByWidth(File srcImage, File destImage, int width) throws IOException {
        BufferedImage bufferedImage = toBufferedImage(srcImage);
        if (bufferedImage == null) {
            return false;
        }
        bufferedImage = zoomImage(bufferedImage, (width * 1.0) / bufferedImage.getWidth());
        return toImageFile(bufferedImage, getFileExtension(srcImage), destImage);
    }

    public static boolean zoomImageByHeight(File srcImage, File destImage, int height) throws IOException {
        BufferedImage bufferedImage = toBufferedImage(srcImage);
        if (bufferedImage == null) {
            return false;
        }
        bufferedImage = zoomImage(bufferedImage, (height * 1.0) / bufferedImage.getHeight());
        return toImageFile(bufferedImage, getFileExtension(srcImage), destImage);
    }

    public static boolean resizeImage(File srcImage, File destImage, int width, int height) throws IOException {
        if (!srcImage.exists() || srcImage.isDirectory() || !srcImage.canRead()) {
            throw new IOException("The srcImage should be an existing and readable file.");
        }
        BufferedImage bufferedImage = ImageIO.read(srcImage);
        bufferedImage = resizeImage(bufferedImage, width, height);
        return ImageIO.write(bufferedImage, getFileExtension(srcImage), destImage);
    }

    /**
     * Get the bounds of the image
     *
     * @param image
     * @return
     * @throws IOException
     */
    public static Rectangle getImageBounds(File image) throws IOException {
        BufferedImage bufferedImage = ImageIO.read(image);
        Rectangle bounds = new Rectangle(0, 0, bufferedImage.getWidth(), bufferedImage.getHeight());
        return bounds;
    }

    /**
     * Get the extension of the <code>File</code>
     *
     * @param file
     * @return
     */
    public static String getFileExtension(File file) {
        if (file == null) {
            return "";
        }
        return getFileExtension(file.getName());
    }

    /**
     * Get the extension of the <code>File</code>
     *
     * @param fileName
     * @return
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null) {
            return "";
        }
        int lastPoint = fileName.lastIndexOf(".");
        return lastPoint < 0 ? "" : fileName.substring(lastPoint + 1);
    }

    public static void main(String[] args) throws IOException {
        File srcImage = new File("/Users/<USER>/Downloads/1556228841186.JPG");
        //File destImage = new File("/Users/<USER>/Downloads/1556228841186_100x200.JPG");
        //File destImage2 = new File("/Users/<USER>/Downloads/1556228841186_r0.5.JPG");
        //File destImage3 = new File("/Users/<USER>/Downloads/1556228841186_100.JPG");
        //File destImage4 = new File("/Users/<USER>/Downloads/1556228841186__200.JPG");
        //
        //ImageUtil.resizeImage(srcImage, destImage, 100, 200);
        //ImageUtil.zoomImage(srcImage, destImage2, 0.5);
        //ImageUtil.zoomImageByWidth(srcImage, destImage3, 100);
        //ImageUtil.zoomImageByHeight(srcImage, destImage4, 200);

        BufferedImage bfimage = ImageUtil.toBufferedImage(srcImage);
        System.out.println(bfimage.getWidth());
        byte[] bytes = ImageUtil.toByteArray(bfimage, "png");
        System.out.println("bytes: " + bytes.length);
        BufferedImage bufferedImageNew = ImageUtil.toBufferedImage(bytes);
        System.out.println("bufferedImageNew: " + bufferedImageNew.getHeight());

        ImageIO.write(bufferedImageNew, "jpg", new File("/Users/<USER>/Downloads/1556228841186_new2.png"));
    }
}
