package com.bees360.common.security;

import com.bees360.common.config.PropertiesUtils;
import com.bees360.common.system.SystemEnv;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class AESUtil {

	public static final String ALG_AES = "AES";
	public static final String AES_AAP = "AES/CBC/PKCS5Padding";
	public static final String SHA1 = "PBKDF2WithHmacSHA1";
	private static final String BEES360_SECRET_KEY = SystemEnv.ENV_BEES360_SECRET_KEY;

	public static String encrypt(String strToEncrypt) {
		if (strToEncrypt == null) {
			return null;
		}
	    return AESCipher.encrypt(strToEncrypt, BEES360_SECRET_KEY);
	}

	public static String descrypt(String strToDecrypt) {
		if (strToDecrypt == null) {
			return null;
		}
		return AESCipher.descrypt(strToDecrypt, BEES360_SECRET_KEY);
	}

	public static void main(String[] args) {
		String yourPathToProject = "";
		String file = yourPathToProject + "/upstream/bees360-backend/web/bees360-web/src/main/resources/secrets-sandbox.properties";
		String oldSecretKey = "";
		String newSecretKey = "";

		Properties properties = PropertiesUtils.getProperties(new File(file));
		Map<String, String> result = new HashMap<>();
		for(String name: properties.stringPropertyNames()) {
			String value = properties.getProperty(name);
			value = value.substring("AES(".length(), value.length() - 1);
			// System.out.println(value);
			value = AESCipher.descrypt(value, oldSecretKey);
			result.put(name, value);
		}

		for(Map.Entry<String, String> entry: result.entrySet()) {
			System.out.println(entry.getKey() + "=AES(" + AESCipher.encrypt(entry.getValue(), newSecretKey) + ")");
		}
	}
}
