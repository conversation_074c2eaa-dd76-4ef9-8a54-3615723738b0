package com.bees360.common.grpc;


public class GrpcConstants {

    //default maxInboundMessageSize is 4M, grpc is not for big message, so just set maxInboundMessageSize to 10-100M
    public static int MAX_INBOUND_MESSAGE_SIZE = 10 * 1024 * 1024;

    //default 15 minutes
    private int archiveCacheExpired;

    public int getArchiveCacheExpired() {
        return archiveCacheExpired;
    }

    public void setArchiveCacheExpired(int archiveCacheExpired) {
        this.archiveCacheExpired = archiveCacheExpired;
    }
}
