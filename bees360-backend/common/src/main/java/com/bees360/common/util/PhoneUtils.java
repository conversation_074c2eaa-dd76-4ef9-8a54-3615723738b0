package com.bees360.common.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 手机号码工具类
 */
public class PhoneUtils {


    /**
     * 格式化手机号码
     * 国内格式： 136-5139-9477
     * 美国格式：(847)977-1309
     *
     * @param phoneNumber 手机号码
     * @return format phone number
     */
    public static String format(String phoneNumber) {
        if (StringUtils.isEmpty(phoneNumber)) {
            return "";
        }
        String phoneFormatNumber = phoneNumber;
        phoneNumber = phoneNumber.replaceAll("-*\\(*\\)*", "")
            .replaceAll("^\\+.*\\D", "")
            .replaceAll("([^xX\\d])", "");
        if (phoneNumber.length() == 10) {
            phoneFormatNumber = "(" + phoneNumber.substring(0, 3) + ") "
                + phoneNumber.substring(3, 6)
                + "-" + phoneNumber.substring(6, 10);
        } else if (phoneNumber.length() == 11) {
            phoneFormatNumber = phoneNumber.substring(0, 3)
                + "-" + phoneNumber.substring(3, 7)
                + "-" + phoneNumber.substring(7, 11);
        }
        return phoneFormatNumber;
    }

    public static void main(String[] args) {
        String num1 = "******-977-1309";
        String num2 = "*************";
        String num3 = "+86 136-5139-9477";
        String format1 = format(num1);
        String format2 = format(num2);
        String format3 = format(num3);
        System.out.println(format1);
        System.out.println(format2);
        System.out.println(format3);
    }
}
