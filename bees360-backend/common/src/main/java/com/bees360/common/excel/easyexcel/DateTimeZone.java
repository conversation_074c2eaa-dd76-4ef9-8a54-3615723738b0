package com.bees360.common.excel.easyexcel;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2019/11/14 17:10
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface DateTimeZone {

    /**
     * Specific value reference {@link TimeZone#getAvailableIDs()}
     *
     * @return
     */
    String value() default "";
}
