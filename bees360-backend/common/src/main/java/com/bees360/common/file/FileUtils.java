package com.bees360.common.file;

import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import java.io.File;
import java.io.FileFilter;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class FileUtils {

	public static byte[] read(File file) throws IOException {
		assert file != null;
		return IOUtils.toByteArray(new FileInputStream(file));
	}

	public static void write(byte[] bytes, File file) throws IOException {
		assert bytes != null && file != null;
		IOUtils.write(bytes, new FileOutputStream(file));
	}

	public static void deleteFile(String filePath) {
		if(filePath == null || "".equals(filePath)) {
			return;
		}
		File file = new File(filePath);
		org.apache.commons.io.FileUtils.deleteQuietly(file);
	}

	public static void deleteDirectory(String directoryPath) throws IOException {
		if(directoryPath == null || "".equals(directoryPath)) {
			return;
		}
		org.apache.commons.io.FileUtils.deleteDirectory(new File(directoryPath));
	}

    /**
     * 删除指定目录下的文件
     * @param root
     * @param filter
     */
	public static void deletes (File root, FileFilter filter) throws IOException{
	    if(root == null) {
	        return;
        }
        if (!root.exists()) {
            return;
        }
        File[] files;
        if(filter != null) {
            files = root.listFiles(filter);
        } else {
            files = root.listFiles();
        }
        if(files == null || files.length == 0) {
            log.info("There is no file to be deleted.");
            return;
        }
        for(File file : files) {
            if(file == null) {
                continue;
            }
            if(file.isFile()) {
                FileUtils.deleteFile(file.getAbsolutePath());
            } else {
                log.info("Start to delete directory {}", file.getPath());
                FileUtils.deleteDirectory(file.getAbsolutePath());
                log.info("Successfully delete Directory {}", file.getPath());
            }
        }
    }

    public static boolean rename(File file, String destFileName) {
        if (Objects.isNull(file) || !file.exists() || StringUtils.isBlank(destFileName)) {
            return false;
        }
        // the destFileName equals old name then return true
        if (StringUtils.equals(destFileName, file.getName())) {
            return true;
        }
        File newFile = new File(file.getParent() + File.separator + destFileName);
        // the new name of file exists then return false
        return !newFile.exists() && file.renameTo(newFile);
    }
}
