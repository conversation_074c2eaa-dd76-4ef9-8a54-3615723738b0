package com.bees360.common.grpc;

import io.grpc.CallOptions;
import io.grpc.Metadata;

/**
 * 定义grpc中的key
 */
public class GrpcKey {

    //定义sticky key projectId
    public static final String KEY_PARAMETER_JOB_ID = "jobId";
    //定义方法参数名称 projectId
    public static final String KEY_STICKY_JOB_ID = "sticky-job-id";

    //定义grpc请求头中的Metadata key
    public static final Metadata.Key<String> KEY_METADATA_JOB_ID =
                                            Metadata.Key.of(KEY_STICKY_JOB_ID, Metadata.ASCII_STRING_MARSHALLER);

    //定义grpc方法Key
    public static final CallOptions.Key<String> KEY_CALL_OPTIONS_JOB_ID =
                                                                    CallOptions.Key.create(KEY_PARAMETER_JOB_ID);
}
