package com.bees360.common.thread;

import com.google.common.base.Preconditions;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

/**
 * 线程管理器
 */
public class SimpleThreadManager implements ThreadManager {

    private static final ConcurrentHashMap<String, Future> threadMap = new ConcurrentHashMap<>();

    @Override
    public Iterable<Future> list() {
        return threadMap.values();
    }

    @Override
    public Set<String> keys() {
        Set<String> set = new HashSet<>();
        Enumeration<String> keyEnum = threadMap.keys();
        while (keyEnum.hasMoreElements()) {
            set.add(keyEnum.nextElement());
        }
        return set;
    }

    @Override
    public boolean exists(String id) {
        return threadMap.containsKey(id);
    }

    @Override
    public Future get(String id) {
        Preconditions.checkNotNull(id);
        return threadMap.get(id);
    }

    @Override
    public void add(String id, Future future) {
        Preconditions.checkNotNull(id);
        threadMap.put(id, future);
    }

    @Override
    public void stop(String id) {
        Preconditions.checkNotNull(id);
        Future future = get(id);
        if (future == null) {
            throw new IllegalStateException("There is no running thread with id " + id);
        }
        future.cancel(true);
        remove(id);
    }

    @Override
    public void remove(String id) {
        if(!exists(id)) {
            return;
        }
        threadMap.remove(id);
    }

    @Override
    public boolean isEmpty() {
        return threadMap.size() == 0;
    }
}
