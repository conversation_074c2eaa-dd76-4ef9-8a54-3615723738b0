package com.bees360.common.file;

import org.apache.commons.compress.archivers.*;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.apache.commons.compress.compressors.CompressorException;
import org.apache.commons.compress.compressors.CompressorOutputStream;
import org.apache.commons.compress.compressors.CompressorStreamFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Arrays;

/**
 *
 * add files to tar.gz archive or unpack tar.gz to directory
 *
 */
public class TarUtils extends AbstractArchiveUtils {

    private static final Logger log = LoggerFactory.getLogger(TarUtils.class);

    public static final String COMPRESSOR_GZIP = "gz";
    public static final String ARCHIVE_TYPE = "tar";
    private static final String SUFFIX_TAR_GZ = ".tar.gz";

    private String compressor;

    public TarUtils() {
        this(COMPRESSOR_GZIP);
    }

    private TarUtils(String compressorName) {
        this.compressor = compressorName;
    }

    @Override
    public void unpackArchive(String archivePath, String targetDir) throws IOException, ArchiveException {
        super.unpackArchive(archivePath, targetDir);
    }

    @Override
    protected String getCompressor() {
        return COMPRESSOR_GZIP;
    }

    @Override
    protected String getSuffix(String compressor) {
        return SUFFIX_TAR_GZ;
    }

    @Override
    public ArchiveInputStream createArchiveInputStream(InputStream inputStream) throws ArchiveException {
        return new TarArchiveInputStream(inputStream);
    }

    @Override
    protected ArchiveOutputStream createArchiveOutputStream(OutputStream outputStream) throws ArchiveException {
        CompressorOutputStream cos = null;
        try {
            cos =
                new CompressorStreamFactory().createCompressorOutputStream(CompressorStreamFactory.GZIP, outputStream);
        } catch (CompressorException e) {
            log.error("createArchiveOutputStream CompressorException occur", e);
        }
        return new TarArchiveOutputStream(cos);
    }

    @Override
    protected ArchiveEntry createArchiveEntry(File file, String entryName) {
        return new TarArchiveEntry(file, entryName);
    }

    @Override
    protected ArchiveEntry createArchiveEntry(String entryName) {
        return new TarArchiveEntry(entryName);
    }

    @Override
    protected void setSize(ArchiveEntry archiveEntry, long size) {
        if (archiveEntry == null) {
            return;
        }
        TarArchiveEntry zipArchiveEntry = (TarArchiveEntry)archiveEntry;
        zipArchiveEntry.setSize(size);
    }

    public static void main(String[] args) {
        String archivePath = test_addFilesToArchive();
        test_unpackArchive(archivePath);
    }

    public static String test_addFilesToArchive() {
        String sourceDir = "/var/bees360/tmp/download/images/1003291";
        String archivePath = null;
        try {
            archivePath = new TarUtils().addFilesToArchiveWithExcludedPaths(sourceDir,
                Arrays.asList(new String[] {"lower", "high/temp", "high/1567559267374.JPG"}));
            File file = new File(archivePath);
            // 166384781
            System.out.println("archive length:" + file.length());
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ArchiveException e) {
            e.printStackTrace();
        }
        return archivePath;
    }

    private static void test_unpackArchive(String archivePath) {
        try {
            new TarUtils().unpackArchive(archivePath, "/var/bees360/tmp/download");
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ArchiveException e) {
            e.printStackTrace();
        }
    }
}
