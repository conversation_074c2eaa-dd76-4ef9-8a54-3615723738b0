package com.bees360.common.grpc;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import io.grpc.BindableService;
import io.grpc.DecompressorRegistry;
import io.grpc.Server;
import io.grpc.ServerBuilder;
import io.grpc.ServerInterceptor;
import io.grpc.util.TransmitStatusRuntimeExceptionInterceptor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2019/09/26
 */
@Slf4j
public abstract class GrpcServerInitializer {

    protected List<BindableService> services;

    private int port;
    private boolean permitKeepAliveWithoutCalls;
    private int keepAliveTime;
    private int keepAliveTimeout;

    private int handshakeTimeout;

    private ServerInterceptor serverInterceptor;

    public GrpcServerInitializer(List<BindableService> services, int port) {
        this(services, port, TransmitStatusRuntimeExceptionInterceptor.instance());
    }

    public GrpcServerInitializer(List<BindableService> services, int port, ServerInterceptor serverInterceptor) {
        this.port = port;
        this.services = services == null ? new ArrayList() : services;
        this.serverInterceptor = serverInterceptor;
    }

    public List<BindableService> getServices() {
        return services;
    }

    public int getPort() {
        return port;
    }

    public void run() throws Exception {
        Server server = buildServer();
        //Server server = serverBuilder.build();
        server.start();
        showInfo();

        startDaemonAwaitThread(server);
    }

    protected Server buildServer() {
        ServerBuilder serverBuilder = ServerBuilder.forPort(port);
        for (BindableService bindableService : services) {
            serverBuilder.addService(bindableService);
        }
        configServer(serverBuilder);
        if(handshakeTimeout > 0) {
            serverBuilder.handshakeTimeout(handshakeTimeout, TimeUnit.MINUTES);
        }
        return serverBuilder.build();
    }

    protected void configServer(ServerBuilder serverBuilder) {
        serverBuilder.maxInboundMessageSize(GrpcConstants.MAX_INBOUND_MESSAGE_SIZE);
        // set default decompression algorithm
        serverBuilder.decompressorRegistry(DecompressorRegistry.getDefaultInstance());
        serverBuilder.intercept(serverInterceptor);
    }

    abstract protected void showInfo();

    protected void startDaemonAwaitThread(Server server) {
        Thread awaitThread = new Thread(() -> {
            try {
                server.awaitTermination();
            } catch (InterruptedException ignore) {
                log.error("Fail to call awaitTermination() on Grpc Server {}", server.getPort());
            }
        });
        awaitThread.setDaemon(false);
        awaitThread.start();
    }

    public boolean isPermitKeepAliveWithoutCalls() {
        return permitKeepAliveWithoutCalls;
    }

    public int getKeepAliveTime() {
        return keepAliveTime;
    }

    public int getKeepAliveTimeout() {
        return keepAliveTimeout;
    }

    public int getHandshakeTimeout() {
        return handshakeTimeout;
    }

    public void setHandshakeTimeout(int handshakeTimeout) {
        this.handshakeTimeout = handshakeTimeout;
    }
}
