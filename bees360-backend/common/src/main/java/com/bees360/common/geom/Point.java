package com.bees360.common.geom;

/**
 * <AUTHOR>
 * @date 2019/07/22
 */
public class Point implements Cloneable {

    protected double x;
    protected double y;

    public Point() {
        this(0, 0);
    }

    public Point(double x, double y) {
        this.x = x;
        this.y = y;
    }

    public double getX() {
        return x;
    }

    public void setX(double x) {
        this.x = x;
    }

    public double getY() {
        return y;
    }

    public void setY(double y) {
        this.y = y;
    }

    public double[] values() {
        return new double[] {x, y};
    }

    public double[] toArray() {
        return values();
    }

    @Override
    public int hashCode() {
        int hash = 17;
        long f = Double.doubleToLongBits(x);
        hash = hash * 31 + (int)(f ^ (f >>> 32));
        f = Double.doubleToLongBits(y);
        hash = hash * 31 + (int)(f ^ (f >>> 32));
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof Point)) {
            return false;
        }
        Point p = (Point)obj;
        return p.getX() == x && p.getY() == y;
    }

    @Override
    public Point clone() {
        Point p = null;
        try {
            p = (Point)super.clone();
        } catch (CloneNotSupportedException e) {
            // do nothing
            e.printStackTrace();
        }
        return p;
    }

    @Override
    public String toString() {
        return "Point [x=" + x + ", y=" + y + "]";
    }

}
