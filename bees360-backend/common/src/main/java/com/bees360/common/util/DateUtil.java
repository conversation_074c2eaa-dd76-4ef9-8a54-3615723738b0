package com.bees360.common.util;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DateUtil {

    public static long getNow() {
        return System.currentTimeMillis();
    }

    public static long getOffset(ZoneId start, ZoneId end) {
        return Duration.between(ZonedDateTime.now(start).toLocalDateTime(), ZonedDateTime.now(end).toLocalDateTime())
            .toHours();
    }

    public static String formatLocalDate(LocalDate dateStr, String pattern) {
        return dateStr.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static Instant toInstant(LocalDateTime localDateTime, ZoneId zoneId) {
        return localDateTime.toInstant(zoneId.getRules().getOffset(Instant.now()));
    }
}
