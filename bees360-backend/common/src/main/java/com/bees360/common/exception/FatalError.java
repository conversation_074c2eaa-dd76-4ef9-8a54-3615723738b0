package com.bees360.common.exception;

public class FatalError extends Error{

    private String message;
    private Throwable cause;

    public FatalError(String message) {
        super(message);
        this.message = message;
    }

    public FatalError(String message, Throwable cause) {
        super(message, cause);
        this.message = message;
        this.cause = cause;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public Throwable getCause() {
        return cause;
    }
}
