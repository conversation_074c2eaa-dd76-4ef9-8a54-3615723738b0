package com.bees360.common.grpc;

import io.grpc.Attributes;
import io.grpc.EquivalentAddressGroup;
import io.grpc.NameResolver;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-26
 * 自定义服务发现
 */
public class MultiEndpointNameResolver extends NameResolver {

    private final List<EquivalentAddressGroup> addresses;

    /**
     *
     * @param addresses 服务地址
     */
    public MultiEndpointNameResolver(List<EquivalentAddressGroup> addresses) {
        this.addresses = addresses;
    }

    //服务授权(未处理)
    @Override
    public String getServiceAuthority() {
        return "fakeAuthority";
    }

    /**
     * 配置可用服务
     * @param listener
     */
    public void start(Listener2 listener) {
        listener.onResult(ResolutionResult.newBuilder().setAddresses(addresses).setAttributes(Attributes.EMPTY).build());
    }

    @Override
    public void shutdown() {

    }
}
