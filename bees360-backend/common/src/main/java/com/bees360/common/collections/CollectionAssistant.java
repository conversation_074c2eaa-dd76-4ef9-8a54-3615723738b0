package com.bees360.common.collections;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class CollectionAssistant {

    public static <K, E> Map<K, List<E>> nullToEmptyList(Map<K, List<E>> mapList) {
        return mapList == null ? new HashMap<K, List<E>>() : mapList;
    }

    public static <K, E> MapList<K, E> nullToEmpty(MapList<K, E> mapList) {
        return mapList == null ? new MapList<K, E>() : mapList;
    }

    public static <K, E> Map<K, E> nullToEmpty(Map<K, E> map) {
        return map == null ? new HashMap<K, E>() : map;
    }

    public static <E> List<E> nullToEmpty(List<E> list) {
        return list == null ? new ArrayList<E>() : list;
    }

    public static <E> Set<E> nullToEmpty(Set<E> set) {
        return set == null ? new HashSet<E>() : set;
    }

    public static <K, E> boolean isEmpty(Map<K, E> map) {
        return map == null || map.isEmpty();
    }

    public static <K, E> boolean isEmptyMapList(Map<K, List<E>> map) {
        if (map == null) {
            return true;
        }
        for (List<E> list : map.values()) {
            if (!isEmpty(list)) {
                return false;
            }
        }
        return true;
    }

    public static <K, E> boolean isEmpty(MapList<K, E> mapList) {
        return mapList == null || mapList.isEmpty();
    }

    public static <E> boolean isEmpty(Collection<E> collections) {
        return collections == null || collections.isEmpty();
    }

    public static <K, E> boolean isNotEmpty(Map<K, E> map) {
        return !isEmpty(map);
    }

    public static <K, E> boolean isNotEmpty(MapList<K, E> mapList) {
        return !isEmpty(mapList);
    }

    public static <E> boolean isNotEmpty(Collection<E> collections) {
        return !isEmpty(collections);
    }

}
