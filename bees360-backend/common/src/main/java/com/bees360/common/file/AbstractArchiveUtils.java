package com.bees360.common.file;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.ArchiveException;
import org.apache.commons.compress.archivers.ArchiveInputStream;
import org.apache.commons.compress.archivers.ArchiveOutputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.compressors.CompressorException;
import org.apache.commons.compress.compressors.CompressorInputStream;
import org.apache.commons.compress.compressors.CompressorOutputStream;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * common logic of compress and uncompress file
 */
public abstract class AbstractArchiveUtils implements ArchiveUtils {

    @Override
    public String addFilesToArchive(String sourceDir) throws IOException, ArchiveException {
        return addFilesToArchiveWithExcludedPaths(sourceDir, null);
    }

    @Override
    public String addFilesToArchiveWithAlias(String sourceDir, Map<String, String> entryAliasMap)
        throws IOException, ArchiveException {
        return addFilesToArchiveWithExcludedPaths(sourceDir, null, entryAliasMap, null);
    }

    @Override
    public String addFilesToArchiveWithAlias(String sourceDir, String archiveFileName, Map<String, String> entryAliasMap)
            throws IOException, ArchiveException {
        return addFilesToArchiveWithExcludedPaths(sourceDir, archiveFileName, entryAliasMap, null);
    }

    @Override
    public String addFilesToArchive(String sourceDir, String archiveFileName) throws IOException, ArchiveException {
        return addFilesToArchiveWithExcludedPaths(sourceDir, archiveFileName, null, null);
    }

    @Override
    public String addFilesToArchiveWithExcludedPaths(String sourceDir, List<String> relativeExcludedPaths)
        throws IOException, ArchiveException {
        return addFilesToArchiveWithExcludedPaths(sourceDir, null, null,null);
    }

    /**
     * 解压文件
     *
     * @param archivePath
     *            压缩包文件绝对路径
     * @param targetDir
     * @throws IOException
     * @throws CompressorException
     */
    @Override
    public void unpackArchive(String archivePath, String targetDir) throws IOException, ArchiveException {
        if (archivePath == null || "".equals(archivePath) || targetDir == null || "".equals(targetDir)) {
            return;
        }
        FileInputStream fis = null;
        BufferedInputStream bis = null;
        CompressorInputStream cis = null;
        ArchiveInputStream ais = null;
        ArchiveEntry archiveEntry = null;
        try {
            fis = new FileInputStream(new File(archivePath));
            // TODO maybe it can be replaced with CompressorInputStream
            bis = new BufferedInputStream(fis);
            ais = createArchiveInputStream(bis);
            while ((archiveEntry = ais.getNextEntry()) != null) {
                String absPath = targetDir + File.separator + archiveEntry.getName();
                File file = new File(absPath);
                if (archiveEntry.isDirectory()) {
                    if (!file.isDirectory() && !file.mkdirs()) {
                        throw new IOException("failed to create directory " + file);
                    }
                } else {
                    File parent = file.getParentFile();
                    if (!parent.isDirectory() && !parent.mkdirs()) {
                        throw new IOException("failed to create directory " + parent);
                    }
                    file.createNewFile();
                    try (OutputStream output = Files.newOutputStream(file.toPath())) {
                        IOUtils.copy(ais, output);
                    }
                }
            }
            archiveEntry = null;
        } finally {
            closeInputResources(ais, cis, bis, fis);
        }
    }

    @Override
    public Map<String, byte[]> readArchiveEntryBytes(ArchiveInputStream archiveInputStream) throws IOException {
        if (archiveInputStream == null) {
            return null;
        }
        ArchiveEntry archiveEntry = null;
        Map<String, byte[]> fileContentMap = new HashMap<>();
        try {
            while ((archiveEntry = archiveInputStream.getNextEntry()) != null) {
                String entryName = archiveEntry.getName();
                if (!archiveEntry.isDirectory()) {
                    fileContentMap.put(entryName, IOUtils.toByteArray(archiveInputStream));
                }
            }
        } finally {
            archiveEntry = null;
            closeInputResources(archiveInputStream, null, null, null);
        }
        return fileContentMap;
    }

    /**
     * 递归将文件加入到archive流中
     *
     * @param archiveOutputStream
     * @param path
     * @param base
     * @throws IOException
     */
    private void addFilesToArchive(ArchiveOutputStream archiveOutputStream, String path, String base, Map<String,String> entryAliasMap,
        List<String> relativeExcludedPaths) throws IOException {
        File file = new File(path);
        if (!file.exists()) {
            return;
        }

        String entryName = getEntryAliasName(base, file.getName(), entryAliasMap);
        ArchiveEntry entry = createArchiveEntry(file, entryName);
        if (file.isFile()) {
            setSize(entry, file.length());
            archiveOutputStream.putArchiveEntry(entry);
            archiveOutputStream.write(IOUtils.toByteArray(new FileInputStream(file)));
            archiveOutputStream.closeArchiveEntry();
        } else {
            File[] children = file.listFiles();
            String newBase = StringUtils.isEmpty(entryName)? "": entryName + "/";
            if (children != null && children.length > 0) {
                for (File child : children) {
                    // excludedDirPrefixes的文件不添加到压缩包中
                    if (!isFileExcluded(child.getPath(), relativeExcludedPaths)) {
                        addFilesToArchive(archiveOutputStream, child.getAbsolutePath(), newBase, entryAliasMap,
                            relativeExcludedPaths);
                    }
                }
            }
        }
    }

    /**
     * 将文件添加到zip archive中
     * @param sourceDir 源文件根目录
     * @param archiveFileName 自定义archive file name
     * @param entryAliasMap archive file entry的别名
     * @param relativeExcludedPaths 不包含的文件相对路径
     * @return
     * @throws IOException
     * @throws CompressorException
     */
    private String addFilesToArchiveWithExcludedPaths(String sourceDir, String archiveFileName,
        Map<String, String> entryAliasMap, List<String> relativeExcludedPaths) throws IOException, ArchiveException {
        if (sourceDir == null || "".equals(sourceDir)) {
            return null;
        }
        File archiveFile = null;
        FileOutputStream fos = null;
        BufferedOutputStream bos = null;

        CompressorOutputStream cos = null;
        ArchiveOutputStream aos = null;
        try {
            File sourceDirFile = new File(sourceDir);
            if (archiveFileName != null && !"".equals(archiveFileName)) {
                archiveFile = new File(sourceDirFile.getParent(), archiveFileName);
            } else {
                archiveFileName = sourceDirFile.getName() + getSuffix(this.getCompressor());
                archiveFile = new File(sourceDirFile.getParent(), archiveFileName);
            }

            fos = new FileOutputStream(archiveFile);
            bos = new BufferedOutputStream(fos);
            aos = createArchiveOutputStream(bos);

            addFilesToArchive(aos, sourceDir, "", entryAliasMap, getAbsoluteExcludedDirPrefixes(sourceDir, relativeExcludedPaths));
        } finally {
            closeOutputResources(aos, cos, bos, fos);
        }
        return archiveFile == null ? "" : archiveFile.getAbsolutePath();
    }

    public void addToArchive(ArchiveOutputStream archiveOutputStream, String entryName, InputStream entryContent)
        throws IOException {
        ArchiveEntry entry = createArchiveEntry(entryName);
        archiveOutputStream.putArchiveEntry(entry);
        int size = IOUtils.copy(entryContent, archiveOutputStream);
        setSize(entry, size);
        archiveOutputStream.closeArchiveEntry();
    }

    public void addToArchive(ArchiveOutputStream archiveOutputStream, String entryName, String entryContent)
        throws IOException {
        try (ByteArrayInputStream in = new ByteArrayInputStream(entryContent.getBytes())) {
            addToArchive(archiveOutputStream, entryName, in);
        }
    }

    /**
     * 获取entry的别名，默认为文件名
     * @param base
     * @param fileName
     * @param entryAliasMap
     * @return
     */
    private String getEntryAliasName(String base, String fileName, Map<String,String> entryAliasMap) {
        String entryName = null;
        if(entryAliasMap == null || entryAliasMap.size() == 0 || !entryAliasMap.containsKey(fileName)) {
            entryName = base + fileName;
        }else {
            entryName = base + entryAliasMap.get(fileName);
        }
        return entryName;
    }

    /**
     * close input streams
     *
     * @param ais
     * @param cis
     * @param bis
     * @param fis
     * @throws IOException
     */
    protected void closeInputResources(ArchiveInputStream ais, CompressorInputStream cis, BufferedInputStream bis,
        FileInputStream fis) throws IOException {
        if (ais != null) {
            ais.close();
        }
        if (cis != null) {
            cis.close();
        }
        if (bis != null) {
            bis.close();
        }
        if (fis != null) {
            fis.close();
        }
    }

    /**
     * close output streams
     *
     * @param aos
     * @param cos
     * @param bos
     * @param fos
     * @throws IOException
     */
    protected void closeOutputResources(ArchiveOutputStream aos, CompressorOutputStream cos, BufferedOutputStream bos,
        FileOutputStream fos) throws IOException {
        if (aos != null) {
            aos.close();
        }
        if (cos != null) {
            cos.close();
        }
        if (bos != null) {
            bos.close();
        }
        if (fos != null) {
            fos.close();
        }
    }

    /**
     * get compress algorithm (upper-case)
     *
     * @return
     */
    protected abstract String getCompressor();

    /**
     * 获取archive文件后缀名
     *
     * @param compressor
     * @return
     */
    protected abstract String getSuffix(String compressor);

    /**
     * create specific ArchiveInputStream for reading
     *
     * @param inputStream
     * @return
     * @throws ArchiveException
     */
    public abstract ArchiveInputStream createArchiveInputStream(InputStream inputStream) throws ArchiveException;

    /**
     * create specific ArchiveOutputStream for writing
     *
     * @param outputStream
     * @return
     * @throws IOException
     */
    protected abstract ArchiveOutputStream createArchiveOutputStream(OutputStream outputStream) throws ArchiveException;

    /**
     * create specific archiveEntry for different archive
     *
     * @return
     * @throws IOException
     */
    protected abstract ArchiveEntry createArchiveEntry(File file, String entryName);

    protected abstract ArchiveEntry createArchiveEntry(String entryName);

    /**
     * 设置ArchiveEntry的文件大小
     *
     * @param archiveEntry
     */
    protected abstract void setSize(ArchiveEntry archiveEntry, long size);

    /**
     * 获取觉得路径
     *
     * @param root
     * @param relativeExcludedPaths
     * @return
     */
    private List<String> getAbsoluteExcludedDirPrefixes(String root, List<String> relativeExcludedPaths) {
        if (relativeExcludedPaths == null || relativeExcludedPaths.size() == 0) {
            return null;
        }
        return relativeExcludedPaths.stream().map(item -> root + File.separator + item).collect(Collectors.toList());
    }

    /**
     * 过滤目录中一些不需要的目录
     *
     * @param filePath
     * @param relativeExcludedPaths
     * @return
     */
    private boolean isFileExcluded(String filePath, List<String> relativeExcludedPaths) {
        if (filePath == null) {
            return false;
        }
        if (relativeExcludedPaths == null || relativeExcludedPaths.size() == 0) {
            return false;
        }
        if (relativeExcludedPaths != null && relativeExcludedPaths.size() > 0) {
            for (String relativeExcludedPath : relativeExcludedPaths) {
                if (filePath.equals(relativeExcludedPath)) {
                    return true;
                }
            }
        }
        return false;
    }
}
