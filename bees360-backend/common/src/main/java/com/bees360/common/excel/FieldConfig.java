package com.bees360.common.excel;

import java.lang.reflect.Method;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;

class FieldConfig {
	private String fieldName;
	private int order;
	private boolean isDate;
	private DateCellConfig dateCellConfig;
	private BaseHandler handler;
	private Method method;
	private CellStyle cellStyle;


	public CellStyle getCellStyle() {
		return cellStyle;
	}
	public void setCellStyle(CellStyle cellStyle) {
		this.cellStyle = cellStyle;
	}
	public boolean isDate() {
		return isDate;
	}
	public void setDate(boolean isDate) {
		this.isDate = isDate;
	}
	public DateCellConfig getDateCellConfig() {
		return dateCellConfig;
	}
	public void setDateCellConfig(DateCellConfig dateCellConfig) {
		this.dateCellConfig = dateCellConfig;
	}
	public BaseHandler getHandler() {
		return handler;
	}
	public void setHandler(BaseHandler handler) {
		this.handler = handler;
	}
	public int getOrder() {
		return order;
	}
	public void setOrder(int order) {
		this.order = order;
	}
	public String getFieldName() {
		return fieldName;
	}
	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}
	public Method getMethod() {
		return method;
	}
	public void setMethod(Method method) {
		this.method = method;
	}
}
