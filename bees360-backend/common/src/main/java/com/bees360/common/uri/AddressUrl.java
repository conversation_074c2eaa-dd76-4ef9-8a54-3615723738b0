package com.bees360.common.uri;

public class AddressUrl {

    private static final String SLASH = "/";
    private static final String DASH = "-";

    /**
     * 如果将地址中包含/, lambda不支持将/作为文件名(主要是因为有些文件系统不支持).
     * 故这里对这些字符进行特殊处理,替换.只影响生成的文件名.
     *
     * @param address
     * @return
     */
    public static String replace(String address) {
        assert address != null;
        if (address.contains(SLASH)) {
            address = address.replace(SLASH, DASH);
        }
        return address;
    }

    public static void main(String[] args) {
        System.out.println(AddressUrl.replace("301 1/2 4th St, Jersey City, NJ 07302 US"));
    }
}
