package com.bees360.common.grpc;

import com.bees360.common.string.CharacterUtil;
import com.google.protobuf.Descriptors;
import com.google.protobuf.GeneratedMessageV3;
import org.apache.commons.beanutils.PropertyUtils;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * convert grpc message object to java bean
 * or conver java bean to grpc message object
 */
public class MessageBeanUtil {

    public static void copyMessagePropertiesToBean(GeneratedMessageV3.Builder message, Object destination) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        if (destination == null) {
            return;
        }
        List<String> getOrIsFields = getSimpleProperties(destination.getClass());
        if(getOrIsFields.size() == 0) {
            return;
        }
        Map<Descriptors.FieldDescriptor, Object> messageFields = message.getAllFields();
        for(Descriptors.FieldDescriptor fieldDescriptor : messageFields.keySet()) {
            String field = fieldDescriptor.getName();
            Object value = messageFields.get(fieldDescriptor);
            if (fieldDescriptor.getJavaType() != null) {
                if(fieldDescriptor.getJavaType() == Descriptors.FieldDescriptor.JavaType.MESSAGE) {
                    continue;
                }
                else if(fieldDescriptor.getJavaType() == Descriptors.FieldDescriptor.JavaType.BYTE_STRING) {
                    //TODO
                    value = null;
                }
            }
            if(getOrIsFields.contains(field)) {
                PropertyUtils.setProperty(destination, field, value);
            }
        }
    }

    public static void copyBeanPropertiesToMessage(Object source, GeneratedMessageV3.Builder message, Descriptors.Descriptor descriptor)
            throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
//        System.out.println("### source: " + source);
//        System.out.println("### descriptor: " + descriptor);

        if(source == null) {
            return;
        }

        List<String> simpleProps = getSimpleProperties(source.getClass());
        for (String propName : simpleProps) {
            Object value = getProperty(source, propName);
//            System.out.println("### " + source.getClass() + "$" + propName + " = " + value);
            if (value == null) continue;

            Descriptors.FieldDescriptor fd = descriptor.findFieldByName(propName);

            for(Descriptors.FieldDescriptor fieldDescriptor: descriptor.getFields()) {
//                System.out.println("### descriptor(" + descriptor.getFullName() + ")." + fieldDescriptor.getName() + " | " + fieldDescriptor.getJsonName());

                Descriptors.FieldDescriptor field = descriptor.findFieldByName(fieldDescriptor.getName());
//                System.out.println("### findFieldByName(" + fieldDescriptor.getName() + "): " + field);
                Descriptors.FieldDescriptor field2 = descriptor.findFieldByName(fieldDescriptor.getJsonName());
//                System.out.println("### findFieldByName(" + fieldDescriptor.getJsonName() + "): " + field2);
            }

//            System.out.println("### " + descriptor.getFullName() + "$" + propName + " = " + fd);
            if(fd == null) {
                continue;
            }
            if(fd.getJavaType() != null && fd.getJavaType() == Descriptors.FieldDescriptor.JavaType.MESSAGE) {
                continue;
            }
            message.setField(fd, value);
        }
    }

    public static List<String> getSimpleProperties(Class<?> cls) {
        List<String> simpleProps = new ArrayList<>();
        Field[] fieldList = cls.getDeclaredFields();
        List<String> getOrIsFields = getAllGetSetFields(cls);
        for (Field field : fieldList) {
            String fieldName = field.getName();
            if(getOrIsFields.contains(fieldName)) {
                simpleProps.add(fieldName);
            }
        }
        return simpleProps;
    }

    private static List<String> getAllGetSetFields(Class<?> cls) {
        Method[] methods = cls.getDeclaredMethods();
        List<String> fields = new ArrayList<>();
        String field = null;
        for (int i = 0; i < methods.length; i++) {
            String methodName = methods[i].getName();
            String fieldName = getFieldName(methodName);
            if(fieldName != null) {
                fields.add(fieldName);
            }
        }
        return fields;
    }

    private static String getFieldName(String methodName) {
        if(methodName == null) {
            return null;
        }
        if (methodName.startsWith("get")) {
            methodName = methodName.replace("get", "");
        }else if (methodName.startsWith("is")) {
            methodName = methodName.replace("is", "");
        } else {
            // TODO do nothing with other fields, it may be need to special process
            return null;
        }
        if("".equals(methodName)) {
            return null;
        }
        char firstLetter = methodName.charAt(0);
        String field = null;
        if(CharacterUtil.isUpperCase(firstLetter)) {
            field = CharacterUtil.toLower(firstLetter) + methodName.substring(1);
        }else {
            field = methodName;
        }
        return field;
    }

    public static void copy(Object source, Object destination) throws IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        PropertyUtils.copyProperties(destination, source);
    }

    public static Object getProperty(Object source, String propertyName) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        return PropertyUtils.getProperty(source, propertyName);
    }
}
