package com.bees360.common.excel.easyexcel;

import java.lang.reflect.Field;

import com.alibaba.excel.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2019/11/15 11:01
 */
public class DateTimeZoneUtil {

    public static String getTimeZone(Field field, String defaultTimeZoneId) {
        DateTimeZone dateTimeZone = field.getAnnotation(DateTimeZone.class);
        if (dateTimeZone == null) {
            // 如果Field的DateTimeZone，则使用全局的
            return defaultTimeZoneId;
        }
        String timeZoneId = dateTimeZone.value();
        if (StringUtils.isEmpty(timeZoneId)) {
            return defaultTimeZoneId;
        }
        return timeZoneId;
    }
}
