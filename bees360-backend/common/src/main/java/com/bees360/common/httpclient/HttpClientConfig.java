package com.bees360.common.httpclient;

import lombok.Data;

@Data
public class HttpClientConfig {

    // 建立连接的最大时间
    private int connectTimeout;

    // 服务器响应超时时间
    private int socketTimeout;

    // 指从连接池获取到链接的超时时间;
    private int connectionRequestTimeout;

    //max connection num:最大连接数量
    private int maxTotal;

    //max connection num for per route:每批次可执行的并行请求数量
    private int maxPerRoute;
}
