package com.bees360.common;

/**
 * <AUTHOR>
 * @date 2020/01/03 10:42
 */
public class NumberUtils {

    public static boolean equals(Short n1, Short n2) {
        return twoValueEquals(n1, n2);
    }

    public static boolean equals(Integer n1, Integer n2) {
        return twoValueEquals(n1, n2);
    }

    public static boolean equals(Long n1, Long n2) {
        return twoValueEquals(n1, n2);
    }

    public static boolean equals(Double n1, Double n2) {
        return twoValueEquals(n1, n2);
    }

    public static boolean equals(Float n1, Float n2) {
        return twoValueEquals(n1, n2);
    }

    private static boolean twoValueEquals(Object n1, Object n2) {
        if(n1 == n2) {
            return true;
        } else if (n1 ==  null || n2 == null) {
            return false;
        } else {
            return n1.equals(n2);
        }
    }
}
