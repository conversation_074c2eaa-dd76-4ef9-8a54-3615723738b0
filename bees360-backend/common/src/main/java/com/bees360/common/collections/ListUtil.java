package com.bees360.common.collections;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Wraps a List<K>, List<T> with various convenience methods for
 * accessing the data. Implements a List-like interface for easier transition.
 *
 * <AUTHOR>
 */
public class ListUtil {

	/**
	 * List to list through method.
	 *
	 * @param keyMapper The key that will be converted to set.
	 * @param list The value of a map is the object itself in the list.
	 * @return the list
	 */
    public static <K, T> List<K> toList(Function<? super T, ? extends K> keyMapper, final List<T> list) {
    	if (isEmpty(list)) {
    		return new ArrayList<>();
    	} else {
    		return list.stream().map(keyMapper).collect(Collectors.toList());
    	}
    }

	/**
	 * List to list through method.
	 *
	 * @param filter Filtration method.
	 * @param keyMapper The key that will be converted to set.
	 * @param list The value of a map is the object itself in the list.
	 * @return the list
	 */
	public static <K, T> List<K> toList(Predicate<? super T> filter, Function<? super T, ? extends K> keyMapper,
										final List<T> list) {
		if (isEmpty(list)) {
			return new ArrayList<>();
		} else {
			return list.stream().filter(filter).map(keyMapper).collect(Collectors.toList());
		}
	}

    /**
     * find first item.
     *
     * @param list The value of a map is the object itself in the list.
     * @return the list
     */
    public static <T> Optional<T> findFirst(Predicate<? super T> filter, List<T> list) {
        if (isEmpty(list)) {
            return Optional.empty();
        } else {
            return list.stream().filter(filter).findFirst();
        }
    }

    /**
     * List distinct.
     *
     * @param list The value of a map is the object itself in the list.
     * @return the list
     */
    public static <T> List<T> distinct(List<T> list) {
        if (isEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().distinct().collect(Collectors.toList());
        }
    }

    /**
     * List to list through method.
     *
     * @param keyMapper The key that will be converted to set.
     * @param list The value of a map is the object itself in the list.
     * @return the list
     */
    public static <K, T> List<K> toDistinctList(Function<? super T, ? extends K> keyMapper, final List<T> list) {
        if (isEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().map(keyMapper).distinct().collect(Collectors.toList());
        }
    }

	/**
	 * List to list through method and filter.
	 *
	 * @param filter Filtration method.
	 * @param list The value of a map is the object itself in the list.
	 * @return the list
	 */
    public static <T> List<T> filter(Predicate<? super T> filter, final List<T> list) {
    	if (isEmpty(list)) {
    		return new ArrayList<>();
    	} else {
    		return list.stream().filter(filter).collect(Collectors.toList());
    	}
    }

    /**
     * List to list through method and filter.
     *
     * @param filter Filtration method.
     * @param keyMapper The key that will be converted to set.
     * @param list The value of a map is the object itself in the list.
     * @return the list
     */
    public static <K, T> List<K> filter(
            Predicate<? super T> filter,
            Function<? super T, ? extends K> keyMapper,
            final List<T> list) {
        if (isEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list.stream().filter(filter).map(keyMapper).collect(Collectors.toList());
        }
    }

    /**
     * list to string
	 *
	 * @param list The value of a map is the object itself in the list.
     * @return the string
     */
    public static <T> String toString(List<T> list) {
    	if (isEmpty(list)) {
    		return "";
    	}
    	StringBuilder sb = new StringBuilder();
    	sb.append("[");
    	for (T t : list) {
    		sb.append("{").append(t.toString()).append("},");
    	}
    	sb.setLength(sb.length() - 1);
    	sb.append("]");
    	return sb.toString();
    }

	/**
	 * Determine whether the list is empty.
	 *
	 * @param list The value of a map is the object itself in the list.
	 * @return true: empty, false: not empty
	 */
	public static <T> boolean isEmpty(List<T> list) {
		return CollectionAssistant.isEmpty(list);
	}

	/**
	 * Determine whether the list is empty.
	 *
	 * @param list The value of a map is the object itself in the list.
	 * @return true: not empty, false: empty
	 */
	public static <T> boolean isNotEmpty(List<T> list) {
		return !isEmpty(list);
	}

	/**
	 * list to set
	 *
	 * @param list The value of a map is the object itself in the list.
	 * @return the set
	 */
	public static <T> Set<T> toSet(final List<T> list) {
		if (isEmpty(list)) {
			return new HashSet<>();
		} else {
			return new HashSet<>(list);
		}
	}

	/**
	 * list to set
	 *
	 * @param keyMapper The key that will be converted to set.
	 * @param list The value of a map is the object itself in the list.
	 * @return the set
	 */
	public static <K, T> Set<K> toSet(Function<? super T, ? extends K> keyMapper, final List<T> list) {
		if (isEmpty(list)) {
			return new HashSet<>();
		} else {
			return list.stream().map(keyMapper).collect(Collectors.toSet());
		}
	}

	/**
	 * List to set through method and filter.
	 *
     * @param filter Filtration method.
	 * @param keyMapper The key that will be converted to set.
	 * @param list The value of a map is the object itself in the list.
	 * @return the set
	 */
	public static <K, T> Set<K> toSet(Predicate<? super T> filter, Function<? super T, ? extends K> keyMapper,
									  final List<T> list) {
		if (isEmpty(list)) {
			return new HashSet<>();
		} else {
			return list.stream().filter(filter).map(keyMapper).collect(Collectors.toSet());
		}
	}

    /**
     * List to set through method and filter.
     *
     * @param filterMapper Filtration method.
     * @param keyMapper The key that will be converted to set.
     * @param list The value of a map is the object itself in the list.
     * @return the set
     */
    public static <K, T> Set<K> toFilterSet(Predicate<? super T> filterMapper,
                                            Function<? super T, ? extends K> keyMapper, final List<T> list) {
        if (isEmpty(list)) {
            return new HashSet<>();
        } else {
            return list.stream().filter(filterMapper).map(keyMapper).collect(Collectors.toSet());
        }
    }


    /**
	 * list to map
	 *
	 * @param keyMapper The key that will be converted to map.
	 * @param list The value of a map is the object itself in the list.
	 * @return the list
	 */
	public static <K, V> Map<K, V> toMap(Function<? super V, ? extends K> keyMapper, final List<V> list) {
		if (isEmpty(list)) {
			return new HashMap<>();
		} else {
			return list.stream().collect(Collectors.toMap(keyMapper, a -> a, (k1, k2) -> k1));
		}
	}

	/**
	 * list to map
	 *
	 * @param keyMapper The key that will be converted to map.
	 * @param valueMapper The value that will be converted to map.
	 * @param list the source of the list
	 * @return the list
	 */
	public static <K, V, T> Map<K, V> toMap(Function<? super T, ? extends K> keyMapper,
											Function<? super T, ? extends V> valueMapper, final List<T> list) {
		if (isEmpty(list)) {
			return new HashMap<>();
		} else {
			return list.stream().collect(Collectors.toMap(keyMapper, valueMapper, (k1, k2) -> k1));
		}
	}

    /**
     * list to map
     *
     * @param filter Filtration method.
     * @param keyMapper The key that will be converted to map.
     * @param valueMapper The value that will be converted to map.
     * @param list the source of the list
     * @return the list
     */
    public static <K, V, T> Map<K, V> toMap(Predicate<? super T> filter, Function<? super T, ? extends K> keyMapper,
                                            Function<? super T, ? extends V> valueMapper, final List<T> list) {
        if (isEmpty(list)) {
            return new HashMap<>();
        } else {
            return list.stream().filter(filter).collect(Collectors.toMap(keyMapper, valueMapper, (k1, k2) -> k1));
        }
    }

	/**
	 * list to group map
     * @param filterMapper Filtration method.
     * @param keyMapper The key that will be converted to map.
     * @param list The value of a map is the object itself in the list.
     * @return the list
     */
    public static <K, T> Map<K, T> toFilterMap(Predicate<? super T> filterMapper,
                                               Function<? super T, ? extends K> keyMapper, final List<T> list) {
        if (isEmpty(list)) {
            return new HashMap<>();
        } else {
            return list.stream().filter(filterMapper).collect(Collectors.toMap(keyMapper, a -> a, (k1, k2) -> k1));
        }
    }

    /**
     * list to map
     *
     * @param filterMapper Filtration method.
     * @param keyMapper The key that will be converted to map.
     * @param valueMapper The value that will be converted to map.
     * @param list The value of a map is the object itself in the list.
     * @return the list
     */
    public static <K, T, U> Map<K, U> toFilterMap(Predicate<? super T> filterMapper,
                                                  Function<? super T, ? extends K> keyMapper,
                                                  Function<? super T, ? extends U> valueMapper, final List<T> list) {
        if (isEmpty(list)) {
            return new HashMap<>();
        } else {
            return list.stream().filter(filterMapper).collect(Collectors.toMap(keyMapper, valueMapper, (k1, k2) -> k1));
        }
    }


    /**
	 * list to map
	 *
	 * @param keyMapper The key that will be converted to map.
	 * @param list the source of the list
	 * @return the list
	 */
	public static <K, T> Map<K, List<T>> toGroupMap(Function<? super T, ? extends K> keyMapper, final List<T> list) {
		if (isEmpty(list)) {
			return new HashMap<>();
		} else {
			return list.stream().collect(Collectors.groupingBy(keyMapper));
		}
	}

    /**
     * list to group map
     *
     * @param keyMapper The key that will be converted to map.
     * @param list the source of the list
     * @return the list
     */
    public static <K, U, T> Map<K, List<U>> toGroupMap(Function<? super T, ? extends K> keyMapper,
                                                       Function<? super T, ? extends U> valueMapper, final List<T> list) {
        if (isEmpty(list)) {
            return new HashMap<>();
        } else {
            Map<K, List<T>> groupMap = toGroupMap(keyMapper, list);
            Map<K, List<U>> resultMap = new HashMap<>();
            for (Map.Entry<K, List<T>> entry : groupMap.entrySet()) {
                resultMap.put(entry.getKey(), toList(valueMapper, entry.getValue()));
            }
            return resultMap;
        }
    }

    /**
     * list to group map
     *
     * @param keyMapper The key that will be converted to map.
     * @param list the source of the list
     * @return the list
     */
    public static <K, U, T> Map<K, Set<U>> toGroupSetMap(Function<? super T, ? extends K> keyMapper,
                                                         Function<? super T, ? extends U> valueMapper, final List<T> list) {
        if (isEmpty(list)) {
            return new HashMap<>();
        } else {
            Map<K, List<T>> groupMap = toGroupMap(keyMapper, list);
            Map<K, Set<U>> resultMap = new HashMap<>();
            for (Map.Entry<K, List<T>> entry : groupMap.entrySet()) {
                resultMap.put(entry.getKey(), toSet(valueMapper, entry.getValue()));
            }
            return resultMap;
        }
    }

    /**
     * sort desc
     *
     * @param number The number.
     * @param list the source of the list
     */
    public static <T> void sortDesc(Function<? super T, ? extends Long> number, List<T> list) {
        list.sort((o1, o2) -> number.apply(o2).compareTo(number.apply(o1)));
    }

    /**
     * deep copy list
     *
     * @param list target list
     * @return result list
     * @throws IOException io exception
     * @throws ClassNotFoundException class not found
     */
    public static <T> List<T> deepCopy(List<T> list) throws IOException, ClassNotFoundException {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        ObjectOutputStream out = new ObjectOutputStream(byteOut);
        out.writeObject(list);

        ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
        ObjectInputStream in = new ObjectInputStream(byteIn);
        @SuppressWarnings("unchecked")
        List<T> dest = (List<T>) in.readObject();
        return dest;
    }

    public static <T> boolean nonNullAndContains(List<T> list, T key) {
        return !CollectionAssistant.isEmpty(list) && list.contains(key);
    }

    public static <T> boolean nonNullAndNotContains(List<T> list, T key) {
        return !CollectionAssistant.isEmpty(list) && !list.contains(key);
    }

}
