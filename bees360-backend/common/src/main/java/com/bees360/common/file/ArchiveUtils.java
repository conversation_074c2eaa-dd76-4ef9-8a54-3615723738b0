package com.bees360.common.file;

import org.apache.commons.compress.archivers.ArchiveException;
import org.apache.commons.compress.archivers.ArchiveInputStream;
import org.apache.commons.compress.compressors.CompressorException;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 文件压缩和解压接口
 */
public interface ArchiveUtils {

    /**
     * 将文件添加到压缩包中
     * @param sourceDir 需要打包的图片的文件根目录
     * @return archivePath 生成的压缩包路径
     */
    public String addFilesToArchive(String sourceDir) throws IOException, ArchiveException;

    /**
     * 将文件添加到压缩包中
     * @param sourceDir 需要打包的图片的文件根目录
     * @param entryAliasMap 文件名别名
     * @return
     * @throws IOException
     * @throws ArchiveException
     */
    public String addFilesToArchiveWithAlias(String sourceDir, Map<String, String> entryAliasMap) throws IOException, ArchiveException;

    /**
     * 将文件添加到压缩包中
     * @param sourceDir 需要打包的图片的文件根目录
     * @param archiveFileName zip包名称
     * @param entryAliasMap 文件名别名
     * @return
     */
    public String addFilesToArchiveWithAlias(String sourceDir, String archiveFileName, Map<String, String> entryAliasMap) throws IOException, ArchiveException;

    /**
     * 将文件添加到压缩包中
     * @param sourceDir 需要打包的图片的文件根目录
     * @param archiveFileName 手动指定需要生成的文件名称
     * @return archivePath 生成的压缩包路径
     */
    public String addFilesToArchive(String sourceDir, String archiveFileName) throws IOException, ArchiveException;


    /**
     * 将文件添加到压缩包中
     * @param sourceDir 需要打包的图片的文件根目录
     * @param relativeExcludedPaths 不需要打进包中的子目录或者文件
     *                            如:subDir/child1
     *                              subDir/file1.JPG
     * @return
     * @throws IOException
     */
    public String addFilesToArchiveWithExcludedPaths(String sourceDir, List<String> relativeExcludedPaths) throws IOException, ArchiveException;


    /**
     * 解压压缩包到目录
     * @param archivePath 压缩包文件绝对路径
     * @param targetDir 解压到指定目录
     */
    public void unpackArchive(String archivePath, String targetDir) throws IOException, ArchiveException;


    /**
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    public ArchiveInputStream createArchiveInputStream(InputStream inputStream) throws ArchiveException;


    /**
     * 将ArchiveInputStream的文件信息读入到缓存
     * @param archiveInputStream
     * @return
     * @throws IOException
     */
    public Map<String, byte[]> readArchiveEntryBytes(ArchiveInputStream archiveInputStream) throws IOException;
}
