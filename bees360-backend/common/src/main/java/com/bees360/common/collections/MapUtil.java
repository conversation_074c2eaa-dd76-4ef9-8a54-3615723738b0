package com.bees360.common.collections;

import java.util.HashMap;
import java.util.Map;

/**
 * Wraps a List<K>, List<T> with various convenience methods for
 * accessing the data. Implements a List-like interface for easier transition.
 *
 * <AUTHOR>
 */
public class MapUtil {

	/**
	 * create result map, The parameters must be arranged by key-value pairs.
	 * @param objs the key value
	 * @return map
	 */
	@SuppressWarnings("unchecked")
	public static <T> Map<T, Object> getMap(Object... objs) {
		return new HashMap<T, Object>(){
			private static final long serialVersionUID = -1857966932251105053L;
			{
				for (int i = 0; i < objs.length; i+=2) {
					put((T) objs[i], objs[i + 1]);
				}
			}
		};
	}

}
