package com.bees360.common.key;

import com.bees360.common.collections.CollectionAssistant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date
 * 3d建模后生成的文件对应的资源key
 */
public class ThreeDModelKeyManager {

    //project/1012165/3DConstruction/workspace/inputpictures-1614407457779.manifest
    public static final String FILE_POSTFIX_MANIFEST = ".manifest";
    public static final String FILE_PREFIX_MANIFEST = "inputpictures";
    //project/1012165/3DConstruction/workspace/sfmlog-1614407457779.log
    public static final String FILE_POSTFIX_SFMLOG = ".log";
    public static final String FILE_PREFIX_SFMLOG = "sfmlog";
    //project/1012165/3DConstruction/workspace/maplist-1614407457779.txt
    public static final String FILE_POSTFIX_MAPLIST = ".txt";
    public static final String FILE_PREFIX_MAPLIST = "maplist";
    //project/1012165/3DConstruction/workspace/bundle-1614407457779.rd.out
    public static final String FILE_POSTFIX_BUNDLE = ".rd.out";
    public static final String FILE_PREFIX_BUNDLE = "bundle";
    //project/1012165/3DConstruction/workspace/final-1614407457779.ply
    public static final String FILE_POSTFIX_PLY = ".ply";
    public static final String FILE_PREFIX_PLY = "final";

    private final String KEY_PREFIX = "project";
    private final String SLASH = "/";

    // 3d模型文件
    private final String KEY_MANIFEST_DEFAULT = "3DConstruction/workspace/inputpictures.manifest";
    private final String KEY_MAP_LIST_DEFAULT = "3DConstruction/workspace/maplist.txt";
    private final String KEY_SFM_LOG_DEFAULT = "3DConstruction/workspace/sfmlog.log";
    private final String KEY_BUNDLE_RD_OUT_DEFAULT = "3DConstruction/workspace/bundle.rd.out";
    private final String KEY_FINAL_PLY_DEFAULT = "3DConstruction/workspace/final.ply";

    // 点云相关文件
    private final String KEY_RANGING = "Ranging" + SLASH + PC_FILE_RANGING;
    private final String KEY_SPLIT_SCOPING = "Scoping" + SLASH + PC_FILE_SCOPING;
    private final String KEY_SCOPING = "Scoping" + SLASH + PC_FILE_SCOPING;
    private final String KEY_PRE_PLANE = "PrePlane" + SLASH + PC_FILE_PRE_PLANE;
    private final String KEY_PLANE = "Plane" + SLASH + PC_FILE_PLANE;
    private final String KEY_PRE_BOUNDARY = "PreBoundary" + SLASH + PC_FILE_PRE_BOUNDARY;
    private final String KEY_BOUNDARY = "Boundary" + SLASH + PC_FILE_BOUNDARY;
    private final String KEY_POST_BOUNDARY = "PostBoundary" + SLASH + PC_FILE_POST_BOUNDARY;

    public static final String PC_FILE_RANGING = "ranging.mon";
    public static final String PC_FILE_SPLIT_SCOPING = "splitScoping.mon";
    public static final String PC_FILE_SCOPING = "scoping.mon";
    public static final String PC_FILE_PRE_PLANE = "prePlane.mon";
    public static final String PC_FILE_PLANE = "plane.mon";
    public static final String PC_FILE_PRE_BOUNDARY = "preBoundary.json";
    public static final String PC_FILE_BOUNDARY = "boundary.json";
    public static final String PC_FILE_POST_BOUNDARY = "postBoundary.json";

    public String getManifestResourceKey(long projectId) {
        return getNewResourceKey(KEY_MANIFEST_DEFAULT, projectId);
    }

    public String getMapListResourceKey(long projectId) {
        return getNewResourceKey(KEY_MAP_LIST_DEFAULT, projectId);
    }

    public String getSfmLogResourceKey(long projectId) {
        return getNewResourceKey(KEY_SFM_LOG_DEFAULT, projectId);
    }

    public String getBundleResourceKey(long projectId) {
        return getNewResourceKey(KEY_BUNDLE_RD_OUT_DEFAULT, projectId);
    }

    public String getPlyResourceKey(long projectId) {
        return getNewResourceKey(KEY_FINAL_PLY_DEFAULT, projectId);
    }

    public List<String> getRangingResourceKey(long projectId) {
        return Collections.singletonList(getNewResourceKey(KEY_RANGING, projectId));
    }

    public String getSplitScopingResourceKey(long projectId) {
        return getNewResourceKey(KEY_SPLIT_SCOPING, projectId);
    }

    public List<String> getScopingResourceKey(long projectId) {
        return Arrays.asList(getNewResourceKey(KEY_SCOPING, projectId));
    }

    public List<String> getPrePlaneResourceKey(long projectId) {
        return Arrays.asList(getNewResourceKey(KEY_PRE_PLANE, projectId));
    }

    public String getPrePlaneSerialKey(long projectId) {
        return getNewResourceKey(KEY_POST_BOUNDARY, projectId);
    }

    public String getPlaneResourceKey(long projectId) {
        return getNewResourceKey(KEY_PLANE, projectId);
    }

    public List<String> getPreBoundaryResourceKey(long projectId) {
        return Arrays.asList(getNewResourceKey(KEY_PRE_BOUNDARY, projectId));
    }

    public String getBoundaryResourceKey(long projectId) {
        return getNewResourceKey(KEY_BOUNDARY, projectId);
    }

    public String getBoundarySerialKey(long projectId) {
        return getNewResourceKey(KEY_POST_BOUNDARY, projectId);
    }

    public String getPostBoundaryResourceKey(long projectId) {
        return getNewResourceKey(KEY_POST_BOUNDARY, projectId);
    }

    public String getPostBoundarySerialKey(long projectId) {
        return getNewResourceKey(KEY_POST_BOUNDARY, projectId);
    }

    public List<String> getModelFileKeys(long projectId) {
        if(projectId == 0) {
            throw new IllegalArgumentException("The projectId must be not null.");
        }
        List<String> modelFileKeys = new ArrayList<>();
        modelFileKeys.add(getManifestResourceKey(projectId));
        modelFileKeys.add(getMapListResourceKey(projectId));
        modelFileKeys.add(getBundleResourceKey(projectId));
        modelFileKeys.add(getSfmLogResourceKey(projectId));
        modelFileKeys.add(getPlyResourceKey(projectId));
        return modelFileKeys;
    }

    public String getManifestFileKey(List<String> fileKeys) {
        return getFileKey(fileKeys, FILE_PREFIX_MANIFEST, FILE_POSTFIX_MANIFEST);
    }

    public String getSfmLogFileKey(List<String> fileKeys) {
        return getFileKey(fileKeys, FILE_PREFIX_SFMLOG, FILE_POSTFIX_SFMLOG);
    }

    public String getMapListFileKey(List<String> fileKeys) {
        return getFileKey(fileKeys, FILE_PREFIX_MAPLIST, FILE_POSTFIX_MAPLIST);
    }

    public String getBundleFileKey(List<String> fileKeys) {
        return getFileKey(fileKeys, FILE_PREFIX_BUNDLE, FILE_POSTFIX_BUNDLE);
    }

    public String getPlyFileKey(List<String> fileKeys) {
        return getFileKey(fileKeys, FILE_PREFIX_PLY, FILE_POSTFIX_PLY);
    }

    private String getFileKey(List<String> fileKeys, String fileNamePrefix, String fileNamePostfix) {
        if(CollectionAssistant.isEmpty(fileKeys)) {
            throw new IllegalArgumentException("fileKeys must be not null.");
        }
        for (String fileKey : fileKeys) {
            if (fileKey.endsWith(fileNamePostfix) && fileKey.indexOf(fileNamePrefix) >= 0) {
                return fileKey;
            }
        }
        return null;
    }

    /**
     *
     * @param oldKey
     * @return
     */
    private String getNewResourceKey(String oldKey, long projectId) {
        String[] pathAndFileName = ResourceKeyUtils.getPathAndNameFromKey(oldKey);
        return KEY_PREFIX + SLASH + projectId + SLASH + pathAndFileName[0] + SLASH + ResourceKeyUtils.getNewNameWithTimestamp(pathAndFileName[1]);
    }
}
