package com.bees360.common.file.chunk;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FileChunkUtils {

	private static final Logger log = LoggerFactory.getLogger(FileChunkUtils.class);
	private static final int FILE_CHUNK_SIZE = 1024 * 1024;

	/**
	 * 对文件流进行划分
	 *
	 * @param file
	 * @return
	 */
	public static Map<Integer, byte[]> splitFile(File file) {
		if (file == null || file.length() == 0) {
			return null;
		}
		Map<Integer, byte[]> bytesMap = new HashMap<>();
		int splitIndex = 0;
		byte[] bytes = null;
		// read tar.gz and each block is 1M.
		FileInputStream fis = null;
		try {
			//文件小于等于 FILE_CHUNK_SIZE
			fis = new FileInputStream(file);
			long length = file.length();
			if(file.length() <= FILE_CHUNK_SIZE) {
				bytes = new byte[(int)length];
				read(fis, bytes, (int)length);
				bytesMap.put(splitIndex, bytes);
				return bytesMap;
			}

			int loop = (int)length / FILE_CHUNK_SIZE;
			int remainder = (int)length % FILE_CHUNK_SIZE;
			if (loop > 0) {
				for (int i = 0; i < loop; i++) {
					bytes = new byte[FILE_CHUNK_SIZE];
					read(fis, bytes, FILE_CHUNK_SIZE);
					bytesMap.put(splitIndex++, bytes);
				}
			}
			if (remainder > 0) {
				bytes = new byte[remainder];
				read(fis, bytes, remainder);
				bytesMap.put(splitIndex, bytes);
			}
		} catch (IOException e) {
			log.error("archiveFile IOException occur", e);
		} finally {
			if (fis != null) {
				try {
					fis.close();
				} catch (IOException e) {
					log.error("FileInputStream close failed", e);
				}
			}
			fis = null;
		}
		return bytesMap;
	}

	public static List<byte[]> getFileChunks(String archivePath) {
		List<byte[]> bytesList = new ArrayList<>();
		File archiveFile = new File(archivePath);
		if(!archiveFile.exists()) {
			return bytesList;
		}
		Map<Integer,byte[]> fileChunkMap = splitFile(archiveFile);
		if(fileChunkMap == null || fileChunkMap.size() == 0) {
			return bytesList;
		}
		return fileChunkMap.values().stream().collect(Collectors.toList());
	}


	public static int read(InputStream inputStream, byte[] bytes, int numToRead) throws IOException {
		if (inputStream == null || bytes == null || bytes.length == 0) {
			return -1;
		}
		// totalRead is actually number of read bytes
		return inputStream.read(bytes, 0, numToRead);
	}
}
