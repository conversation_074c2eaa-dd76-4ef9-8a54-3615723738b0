package com.bees360.common.system;

public class SystemEnv {

	public static String ENV_PROFILE_ACTIVE;
	public static String ENV_BEES360_SECRET_KEY;

	static {
		// 如果发生 java.lang.NoClassDefFoundError: Could not initialize class xxx.xxx.xxx 类型的错误，可能是这里发生了异常
		try {
			ENV_PROFILE_ACTIVE = env("ENV");
			ENV_BEES360_SECRET_KEY = env("BEES360_SECRET_KEY");
		}catch(Exception e) {
			//如果运行环境参数校验不通过，则退出程序(虚拟机)
			System.exit(0);
		}
	}

	private static String env(String envKey) throws Exception{
		String envVal = getenv(envKey);
		if(envVal == null || "".equals(envVal)) {
			throw new Exception("Environment \"" + envKey + "\" must not be empty.");
		}
		return envVal;
	}

	public static String getenv(String envKey) {
		return System.getenv(envKey);
	}

	public static void main(String[] args) {
		String SPTRIP_PAYMENT_SECRET_KEY = SystemEnv.ENV_PROFILE_ACTIVE;
		System.out.println(SPTRIP_PAYMENT_SECRET_KEY);
	}
}
