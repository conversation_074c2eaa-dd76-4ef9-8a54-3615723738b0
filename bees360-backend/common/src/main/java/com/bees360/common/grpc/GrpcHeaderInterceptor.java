package com.bees360.common.grpc;

import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020/09/05 grpc客户端请求头处理拦截器
 */
@Slf4j
public class GrpcHeaderInterceptor implements ClientInterceptor {

    private static final String METHOD_NAME_RECONSTRUCT_3D = "reconstruct3d";
    private static final String METHOD_NAME_STOP_3D = "stop3d";
    private static final String METHOD_NAME_GET_STARTED_TASK_STATUS = "getStartedTaskStatus";

    @Override
    public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(MethodDescriptor<ReqT, RespT> method,
        CallOptions callOptions, Channel next) {
        String fullMethodName = method.getFullMethodName();
        String jobId = callOptions.getOption(GrpcKey.KEY_CALL_OPTIONS_JOB_ID);
        log.info("the parameter with name {} is {} in grpc service method {}", GrpcKey.KEY_PARAMETER_JOB_ID,
            jobId, fullMethodName);

        //添加请求头stickinessMetadataKey的键值对("sticky-job-id",value)
        return new ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT>(next.newCall(method, callOptions)) {
            @Override
            public void start(Listener<RespT> responseListener, Metadata headers) {
                // 如果是重构/停止3d方法,在请求头中添加jobId参数
                if (fullMethodName.endsWith(METHOD_NAME_RECONSTRUCT_3D) ||
                    fullMethodName.endsWith(METHOD_NAME_STOP_3D) ||
                    fullMethodName.endsWith(METHOD_NAME_GET_STARTED_TASK_STATUS)) {
                    headers.put(GrpcKey.KEY_METADATA_JOB_ID, jobId);
                }
                log.info("header after interceptor is {}", headers);
                super.start(responseListener, headers);
            }
        };
    }
}
