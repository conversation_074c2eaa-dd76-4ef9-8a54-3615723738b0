package com.bees360.common.excel.annotations;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

@Target(TYPE)
@Retention(RUNTIME)
@Documented
public @interface DateConfig {
	String format() default "yyyy-mm-dd HH:MM:SS";
	String timezone() default "UTC";
	String toTimezone() default "US/Central";
}
