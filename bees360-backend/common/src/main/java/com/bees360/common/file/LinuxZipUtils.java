package com.bees360.common.file;

import com.google.common.base.Preconditions;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UncheckedIOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Log4j2
public class LinuxZipUtils {

    private static final Duration DEFAULT_TIME_OUT = Duration.ofSeconds(60);

    public static void archiveFiles(String workingDir, String outputName, List<String> inputFileNames, Executor executor) throws TimeoutException {
        Preconditions.checkArgument(!CollectionUtils.isEmpty(inputFileNames),
            "Input files is empty, the archive process will be aborted");
        List<File> inputFiles = inputFileNames.stream().map(File::new).collect(Collectors.toList());
        archiveFiles(new File(workingDir), new File(outputName), inputFiles, executor);
    }

    public static void archiveFiles(File workingDir, File outputFile, List<File> inputFiles, Executor executor) throws TimeoutException {
        Preconditions.checkArgument(workingDir != null && workingDir.exists() && workingDir.isDirectory(),
            "Invalid working directory");
        Preconditions.checkArgument(!CollectionUtils.isEmpty(inputFiles),
            "Input files is empty, the archive process will be aborted");
        ArrayList<String> command = new ArrayList<>(4 + inputFiles.size());
        command.add("zip");
        command.add("-j");
        command.add("-o");
        command.add(outputFile.getAbsolutePath());
        command.addAll(inputFiles.stream().map(File::getName).collect(Collectors.toList()));
        Process process;
        try {
            process = new ProcessBuilder(command).directory(workingDir).redirectErrorStream(true).start();
            ConcurrentLinkedQueue<String> processLogs = new ConcurrentLinkedQueue<>();
            StreamGobbler streamGobbler = new StreamGobbler(process.getInputStream(),
                processLogs::add);
            executor.execute(streamGobbler);

            String inputLog = inputFiles.stream().map(File::getAbsolutePath).collect(Collectors.joining(","));
            String outputLog = outputFile.getAbsolutePath();
            boolean exited = process.waitFor(DEFAULT_TIME_OUT.getSeconds(), TimeUnit.SECONDS);
            if (!exited) {
                process.destroyForcibly();
                throw new TimeoutException(String.format("Archive process execution excesses timeout value, " +
                    "process was terminated. Input files: %s, output log:%s", inputLog, outputLog));
            }
            int exitCode = process.exitValue();

            String processLog = String.join(";", processLogs);
            if (exitCode != 0) {
                throw new IllegalStateException(String.format("Failed to archive file with command, exit code: %s, " +
                    "input keys: %s, output key: %s, process log: %s", exitCode, inputLog, outputLog, processLog
                ));
            }
            log.info("Finished archive file with command, input keys: {}, output key: {}, process log: {}",
                inputLog, outputLog, processLog);
        } catch (IOException e) {
            throw new UncheckedIOException("Failed to create zip archive process", e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    private static class StreamGobbler implements Runnable {
        private final InputStream inputStream;
        private final Consumer<String> consumer;

        public StreamGobbler(InputStream inputStream, Consumer<String> consumer) {
            this.inputStream = inputStream;
            this.consumer = consumer;
        }

        @Override
        public void run() {
            try {
                new BufferedReader(new InputStreamReader(inputStream)).lines()
                    .forEach(consumer);
            } catch (UncheckedIOException e) {
                if ( Objects.equals("Stream closed", e.getCause().getMessage())) {
                    log.warn("Process is terminated unexpectedly", e);
                    return;
                }
                throw e;
            }

        }
    }

}
