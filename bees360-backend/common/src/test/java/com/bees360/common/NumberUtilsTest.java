package com.bees360.common;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class NumberUtilsTest {

    @Test
    public void testEquals() {
        Short n1 = 1;
        Short n2 = 1;
        assertTrue(NumberUtils.equals(n1, n2));

        assertTrue(NumberUtils.equals((short)1, (short)1));
        assertTrue(NumberUtils.equals(Short.valueOf((short) 1), (short)1));
        assertFalse(NumberUtils.equals((short)1, (short)2));
        assertFalse(NumberUtils.equals(null, (short)2));
    }

    @Test
    public void testEquals1() {
        Integer n1 = 1;
        Integer n2 = 1;
        assertTrue(NumberUtils.equals(n1, n2));
        assertTrue(NumberUtils.equals(1, 1));
        assertTrue(NumberUtils.equals(Integer.valueOf(1), 1));
        assertFalse(NumberUtils.equals(1, 2));
        assertFalse(NumberUtils.equals(null, 2));
    }

    @Test
    public void testEquals2() {
        Long n1 = 1L;
        Long n2 = 1L;
        assertTrue(NumberUtils.equals(n1, n2));
        assertTrue(NumberUtils.equals(1L, 1L));
        assertTrue(NumberUtils.equals(Long.valueOf(1), 1L));
        assertFalse(NumberUtils.equals(1L, 2L));
        assertFalse(NumberUtils.equals(null, 2L));
    }

    @Test
    public void testEquals3() {
        Double n1 = 1.123;
        Double n2 = 1.123;
        assertTrue(NumberUtils.equals(n1, n2));
        assertTrue(NumberUtils.equals(1.123, 1.123));
        assertTrue(NumberUtils.equals(Double.valueOf(1.123), 1.123));
        assertFalse(NumberUtils.equals(1.123, 2.123));
        assertFalse(NumberUtils.equals(null, 1.123));
    }

    @Test
    public void testEquals4() {
        Float n1 = Float.valueOf("1.123");
        Float n2 = Float.valueOf("1.123");
        assertTrue(NumberUtils.equals(n1, n2));
        assertTrue(NumberUtils.equals(1.123f, 1.123f));
        assertFalse(NumberUtils.equals(1.123f, 2.123f));
        assertFalse(NumberUtils.equals(null, 2.123f));
    }
}
