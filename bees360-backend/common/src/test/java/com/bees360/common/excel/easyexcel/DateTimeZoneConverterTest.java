package com.bees360.common.excel.easyexcel;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import org.junit.jupiter.api.Test;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.io.TempDir;

/**
 * <AUTHOR> Yang
 * @date 2019/11/15 14:30
 */
public class DateTimeZoneConverterTest {

    @Getter
    @Setter
    @ToString
    @EqualsAndHashCode
    @NoArgsConstructor
    public static class TheDate {
        @DateTimeFormat("yyyy-MM-dd HH:mm:ss:SSS")
        @ExcelProperty(index = 0)
        private Date date;
    }

    @TempDir
    public File temporaryFolder;

    private static final String TIME_ZONE_ID_US_CENTRAL = "US/Central";
    private static final String TIME_ZONE_ID_ETC_UTC = "Etc/UTC";

    public File getTestDirectory() {
        return temporaryFolder;
    }

    @Test
    public void testDateTimeZoneStringConverter() {
        File file = new File(getTestDirectory(), "easyexcel-test-dateTimeZoneStringConverter.xlsx");

        if (file.exists()) {
            file.delete();
        }
        List<TheDate> listOriginal = data();

        // 用 US/Central 去写入Excel中的时间
        EasyExcel.write(file, TheDate.class)
                .registerConverter(new DateTimeZoneStringConverter(TIME_ZONE_ID_US_CENTRAL))
                .sheet("theDate")
                .doWrite(listOriginal);

        // 用 US/Central 去读取Excel中的时间
        List<TheDate> listUsCentral =
                EasyExcel.read(file)
                        .registerConverter(new DateTimeZoneStringConverter(TIME_ZONE_ID_US_CENTRAL))
                        .head(TheDate.class)
                        .sheet()
                        .doReadSync();

        assertList(listOriginal, listUsCentral);

        // 用 UTC 时区去读取Excel中的时间
        List<TheDate> listEtcUtc =
                EasyExcel.read(file)
                        .registerConverter(new DateTimeZoneStringConverter(TIME_ZONE_ID_ETC_UTC))
                        .head(TheDate.class)
                        .sheet()
                        .doReadSync();

        assertTimeSpan(listOriginal, listEtcUtc, TIME_ZONE_ID_US_CENTRAL, TIME_ZONE_ID_ETC_UTC);
    }

    @Test
    public void testDateTimeZoneNumberConverter() {
        File file = new File(getTestDirectory(), "easyexcel-test-dateTimeZoneNumberConverter.xlsx");

        if (file.exists()) {
            file.delete();
        }
        List<TheDate> listOriginal = data();

        // 用 US/Central 去写入Excel中的时间
        EasyExcel.write(file, TheDate.class)
                .registerConverter(new DateTimeZoneNumberConverter(TIME_ZONE_ID_US_CENTRAL))
                .sheet("theDate")
                .doWrite(listOriginal);

        // 用 US/Central 去读取Excel中的时间
        List<TheDate> listUsCentral =
                EasyExcel.read(file)
                        .registerConverter(new DateTimeZoneNumberConverter(TIME_ZONE_ID_US_CENTRAL))
                        .head(TheDate.class)
                        .sheet()
                        .doReadSync();

        assertList(listOriginal, listUsCentral);

        // 用 UTC 时区去读取Excel中的时间
        List<TheDate> listEtcUtc =
                EasyExcel.read(file)
                        .registerConverter(new DateTimeZoneNumberConverter(TIME_ZONE_ID_ETC_UTC))
                        .head(TheDate.class)
                        .sheet()
                        .doReadSync();

        assertTimeSpan(listOriginal, listEtcUtc, TIME_ZONE_ID_US_CENTRAL, TIME_ZONE_ID_ETC_UTC);
    }

    private List<TheDate> data() {
        Date now = getTime();

        List<TheDate> datas = new ArrayList<>();
        TheDate birthday = new TheDate();
        birthday.setDate(now);
        datas.add(birthday);
        return datas;
    }

    private Date getTime() {
        // 这里的时间保留保留位数应该和@DateTimeFormat一致，否则值比较时将会不相等
        return new Date();
    }

    private long getTimeSpan(Date from, Date to) {
        return from.getTime() - to.getTime();
    }

    private long getTimeZoneTimeSpan(String timeZoneIdfrom, String timeZoneIdTo) {
        long now = System.currentTimeMillis();

        return TimeZone.getTimeZone(timeZoneIdfrom).getOffset(now)
                - TimeZone.getTimeZone(timeZoneIdTo).getOffset(now);
    }

    private void assertList(List<TheDate> listOriginal, List<TheDate> listUsCentral) {
        assertEquals(listOriginal.size(), listUsCentral.size());
        for (int i = 0; i < listOriginal.size(); i++) {
            TheDate original = listOriginal.get(i);
            TheDate usCentral = listUsCentral.get(i);
            assertEquals(original, usCentral);
        }
    }

    private void assertTimeSpan(
            List<TheDate> datesFrom,
            List<TheDate> datesTo,
            String timeZoneIdFrom,
            String timeZoneIdTo) {

        long timeZoneSpanFromUsCentralToEtcUtc = getTimeZoneTimeSpan(timeZoneIdFrom, timeZoneIdTo);

        for (int i = 0; i < datesFrom.size(); i++) {
            TheDate dateFrom = datesFrom.get(i);
            TheDate dateTo = datesTo.get(i);
            // 对于同一个时间字符串，A时区 - B时区 = B时区解释 - A时区解释
            long span = getTimeSpan(dateTo.getDate(), dateFrom.getDate());
            assertEquals(timeZoneSpanFromUsCentralToEtcUtc, span);
        }
    }
}
