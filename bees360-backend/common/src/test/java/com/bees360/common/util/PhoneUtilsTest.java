package com.bees360.common.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 *  <AUTHOR>
 *  @date 2020/2/19 21:38
 */
public class PhoneUtilsTest {

    @Test
    public void testPhoneFormat() {
        String phone1 = PhoneUtils.format("13673241345");
        Assertions.assertEquals(phone1,"136-7324-1345");

        String phone2 = PhoneUtils.format("136-7324-1345");
        Assertions.assertEquals(phone2,"136-7324-1345");

        String phone3 = PhoneUtils.format("1673241345");
        Assertions.assertEquals(phone3,"(*************");

        String phone4 = PhoneUtils.format("************");
        Assertions.assertEquals(phone4,"(*************");

        String phone5 = PhoneUtils.format("(167)324-1345");
        Assertions.assertEquals(phone5,"(*************");

        String phone6 = PhoneUtils.format("1324-1345");
        Assertions.assertEquals(phone6,"1324-1345");
    }
}
