package com.bees360.common.excel.easyexcel;

import java.text.ParseException;
import java.util.Date;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class DateUtilsTest {

    @Test
    public void parseDate() throws ParseException {
        Date now = new Date();
        String format = "yyyy-MM-dd HH:mm:ss:SSS";
        String timeZone = "US/Central";

        String dateWrite = DateUtils.format(now, format, timeZone);

        Date dateRead = DateUtils.parseDate(dateWrite, format, timeZone);

        assertEquals(now.getTime(), dateRead.getTime());
        assertEquals(now, dateRead);
    }
}
