root = true

[*]
charset = utf-8
end_of_line = lf
indent_size = 4
indent_style = space
insert_final_newline = false
max_line_length = 120
ij_formatter_off_tag = @formatter:off
ij_formatter_on_tag = @formatter:on
ij_formatter_tags_enabled = true
ij_smart_tabs = false
ij_wrap_on_typing = false

[*.{java,groovy,xml,proto}]
tab_width = 4
indent_size = 4
indent_style = space
ij_continuation_indent_size = 4

[*.{yml,yaml,properties}]
tab_width = 2
ij_continuation_indent_size = 2
indent_style = space
indent_size = 2
