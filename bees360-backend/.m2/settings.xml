<settings xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.1.0 https://maven.apache.org/xsd/settings-1.1.0.xsd"
          xmlns="http://maven.apache.org/SETTINGS/1.1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <localRepository>${env.HOME}/.m2/repository</localRepository>
    <servers>
        <server>
            <id>bees360-maven-repo</id>
            <username>${env.MAVEN_REPO_USER}</username>
            <password>${env.MAVEN_REPO_PASS}</password>
        </server>
        <server>
            <id>gitlab.bees360.com:4567</id>
            <username>${env.CI_REGISTRY_USER}</username>
            <password>${env.CI_REGISTRY_PASSWORD}</password>
        </server>
    </servers>
    <mirrors>
        <mirror>
            <id>internal-nexus</id>
            <name>nexus</name>
            <url>http://nexus.9realms.co/repository/maven-public</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
        <mirror>
            <id>insecure-repo</id>
            <name>to overwrite the mirror maven-default-http-blocker in $MAVEN_HOME/config/settings.xml</name>
            <url>http://nexus.9realms.co/repository/maven-public</url>
            <mirrorOf>external:http:*</mirrorOf>
            <blocked>false</blocked>
        </mirror>
    </mirrors>
</settings>
