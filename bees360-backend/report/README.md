Bees360 Report
===

###### `shoushan.zhao` `2019/12/19`

Bees360 Report提供REVIEW界面所有需要的功能，包括报告生成，画annotation（后期应该需要抽出单独模块）。

### 目前项目模块
```
1、其它模块需要引入本模块，只需要在pom.xml中引入
		<dependency>
			<groupId>com.bees360</groupId>
			<artifactId>bees360-report-spring-boot-start</artifactId>
			<version>${project.version}</version>
		</dependency>
2、引用外部数据代码都在com.bees360.report.service.impl.ExternalInterfaceImpl中
```

### 需要优化的地方
```
1、annotation相关功能由于是REVIEW界面必须的，所以目前放在这个模块里。
2、调用其它模块的数据，目前写的不够灵活，由于其它部分还没有抽出单独模块，后续需要优化。
3、数据库还只有mysql。
4、生成报告后同步到web项目，submit report没有发通知。
5、现在只是能用，框架编码都不符合最终标准。
6、单元测试需要补。
```
