package com.bees360.report.spring.boot.autoconfigure;

import com.bees360.report.PackageMarker;
import com.bees360.report.grpc.client.job.SyncDataToWebJobExecutor;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @date 2019/11/26 09:08
 */
@AutoConfiguration
@ComponentScan(basePackageClasses = {PackageMarker.class}, basePackages = {"com.bees360.internal.ai"})
@EnableEncryptableProperties
@Import({
    SyncDataToWebJobExecutor.class,
})
public class ReportAutoConfiguration {
}
