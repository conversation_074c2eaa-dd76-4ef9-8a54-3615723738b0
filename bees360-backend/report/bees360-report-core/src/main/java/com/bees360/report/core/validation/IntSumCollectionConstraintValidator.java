package com.bees360.report.core.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Collection;

public class IntSumCollectionConstraintValidator implements ConstraintValidator<IntSum, Collection<Integer>> {

    private int sumExpected = 0;

    @Override
    public void initialize(IntSum sumValue) {
        sumExpected = sumValue.value();
    }

    @Override
    public boolean isValid(Collection<Integer> collection, ConstraintValidatorContext constraintValidatorContext) {
        if(collection == null || collection.isEmpty()) {
            return true;
        }
        int sumActual = 0;
        for (Integer value: collection) {
            if (value == null) {
                return false;
            }
            sumActual += value;
        }
        return sumActual == sumExpected;
    }
}
