package com.bees360.report.core.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.ElementType.TYPE_USE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 */
@Documented
@Constraint(validatedBy = {IntSumCollectionConstraintValidator.class, IntSumMapConstraintValidator.class})
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Repeatable(CollectionValid.List.class)
public @interface CollectionValid {

    String message() default "the collection is invalid.";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };

    String[] contains() default { };

    String[] notContains() default { };

    /**
     * 不含有null
     */
    boolean notContainsNull() default false;
    /**
     * 不含null和有空字符串
     */
    boolean notContainsEmpty() default false;
    /**
     * 不含有null和trim之后为空字符串的字符串
     */
    boolean notContainsBlank() default false;

    @Target({ METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE })
    @Retention(RUNTIME)
    @Documented
    public @interface List {
        CollectionValid[] value();
    }
}
