package com.bees360.report.core.exception;

import java.util.Objects;

/**
 * 手动校检请求参数，或者操作是否正常
 * <AUTHOR>
 * @since 2020/3/19 5:57 PM
 **/
public class AssertUtil {
    private AssertUtil(){}

    /**
     * Assert <code>object</code> is not null
     * @param object the object to be checked
     * @param message that message will be sent to client
     */
    public static void notNull(Object object, String message) throws ServiceMessageException {
        assertTrue(Objects.nonNull(object), MessageCode.PARAM_INVALID, message);;
    }

    /**
     * Assert <code>flag</code> is true, when flag is not true then do exceptin
     * @param flag the boolean to be tested
     * @param message that message will be sent to client
     * @param code exception code
     * @see MessageCode
     */
    public static void assertTrue(boolean flag, String code, String message) throws ServiceMessageException{
        if (!flag) {
            doException(code, message);
        }
    }

    private static void doException(String code, String message) throws ServiceMessageException{
        throw new ServiceMessageException(code, message);
    }
}
