package com.bees360.report.core.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Map;

public class IntSumMapConstraintValidator implements ConstraintValidator<IntSum, Map<String, Integer>> {

    private int sumExpected = 0;

    @Override
    public void initialize(IntSum sumValue) {
        sumExpected = sumValue.value();
    }

    @Override
    public boolean isValid(Map<String, Integer> map, ConstraintValidatorContext constraintValidatorContext) {
        if(map == null || map.isEmpty()) {
            return true;
        }
        int sumActual = 0;
        for(Map.Entry<String, Integer> entry: map.entrySet()) {
            if(entry.getKey() == null || entry.getValue() == null) {
                return false;
            }
            sumActual += entry.getValue();
        }
        return sumActual == sumExpected;
    }
}
