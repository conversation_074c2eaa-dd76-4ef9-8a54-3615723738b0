package com.bees360.report.core.exception;

import java.io.Serial;

/**
 * Difference from ServiceException, its message will be the message of the Response while ServiceException map its code
 * to a message. So its Constructor must at least have a code field and message field.
 *
 * <AUTHOR>
 */
public class ProtoServiceMessageException extends ServiceException {

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 6692022199545706408L;

    public ProtoServiceMessageException(String code, String message) {
        super(code, message);
    }

    public ProtoServiceMessageException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }
}
