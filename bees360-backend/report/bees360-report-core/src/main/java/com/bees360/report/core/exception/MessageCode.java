package com.bees360.report.core.exception;

import com.bees360.base.code.ApiErrorType;
import com.bees360.base.code.MessageCodeDocument;
import java.util.Locale;
import org.springframework.context.MessageSource;

public interface MessageCode {

	// common message code 001-1000
	// system exception
	@MessageCodeDocument(type = ApiErrorType.INTERNAL)
	String SYSTEM_EXCEPTION = "1";

	@MessageCodeDocument(type = ApiErrorType.INTERNAL)
	//some error occur in database operation
	String DATABASE_EXCEPTION = "2";

	//param is invalid
	@MessageCodeDocument(type = ApiErrorType.BAD_REQUEST)
	String PARAM_INVALID = "7";

	@MessageCodeDocument(type = ApiErrorType.BAD_REQUEST)
	String BAD_REQUEST = "400";

	@MessageCodeDocument(type = ApiErrorType.INVALID_ARGUMENT)
	String VALIDATION_FAILED = "701";

	@MessageCodeDocument(type = ApiErrorType.NOT_FOUND)
	String DATA_NON_EXIST = "703";

    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String NO_IMAGE_TO_START_AD = "3009";

    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String IMAGE_SOURCE_TYPE_NNOT = "3014";

    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String IMAGE_CANNOT_START_AD = "6103";

    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String DATA_FORMAT_ERROR = "5008";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_NOT_EXIST = "9004";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_BEEN_APPROVED = "9005";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_BEEN_DISAPPROVED = "9006";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_NOT_SUBMITTED = "9008";
    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String REPORT_SUMMARY_REQUIRED = "9010";
    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String REPORT_SUMMARY_INVALID = "9011";
    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String REPORT_APPROVED_WRONG_ORDER = "9013";
    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String REPORT_CANNOT_APPROVE = "9021";
    static String getMessage(MessageSource messageSource, String messageCode) {
		if(messageSource == null || messageCode == null) {
			return null;
		}
		return messageSource.getMessage(messageCode, null, Locale.ENGLISH);
	}
}
