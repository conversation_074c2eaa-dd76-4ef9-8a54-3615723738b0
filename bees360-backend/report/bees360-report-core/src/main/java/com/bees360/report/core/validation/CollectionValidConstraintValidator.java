package com.bees360.report.core.validation;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.*;

/**
 * <AUTHOR>
 */
public class CollectionValidConstraintValidator implements ConstraintValidator<CollectionValid, List<String>> {

    /**
     * 必须含有所有的元素
     */
    private Set<String> contains;
    /**
     * 不包含任意元素
     */
    private Set<String> notContains;

    private boolean notContainsBlank;

    @Override
    public void initialize(CollectionValid constraint) {
        contains = notOrSet(constraint.contains());
        notContains = notOrSet(constraint.notContains());
        if(constraint.notContainsNull()) {
            notContains.add(null);
        }
        if(constraint.notContainsEmpty()) {
            notContains.add("");
        }
        notContainsBlank = constraint.notContainsBlank();
    }

    private Set<String> notOrSet(String[] collection) {
        return collection == null? null: new HashSet<>(Arrays.asList(collection));
    }

    @Override
    public boolean isValid(List<String> list, ConstraintValidatorContext constraintValidatorContext) {
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        Set<String> sourceSet = new HashSet<>(list);
        return checkNotContainsBlank(sourceSet) && checkContains(sourceSet) && checkNotContains(sourceSet);
    }

    private boolean checkNotContainsBlank(Set<String> sourceSet) {
        if (!notContainsBlank) {
            return true;
        }
        return sourceSet.stream().allMatch(s -> !StringUtils.isBlank(s));
    }

    private boolean checkContains(Set<String> sourceSet) {
        if (CollectionUtils.isEmpty(contains)) {
            return true;
        }
        return contains.stream().allMatch(s -> sourceSet.contains(s));
    }

    private boolean checkNotContains(Set<String> sourceSet) {
        if (CollectionUtils.isEmpty(notContains)) {
            return true;
        }
        return notContains.stream().allMatch(s -> !sourceSet.contains(s));
    }
}
