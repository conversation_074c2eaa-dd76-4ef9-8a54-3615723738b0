package com.bees360.report.core.exception;

import java.io.Serial;

public class ServiceException extends Exception {

    @Serial
    private static final long serialVersionUID = 1L;

    // Exception code
    private String code;

    // Exception info
    private String message;

    /**
     * 请使用ServiceException(String msgCode, String message)，方便日志的输出和查看。
     */
    @Deprecated
    public ServiceException(String code) {
        super();
        this.code = code;
    }

    public ServiceException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public ServiceException(String code, Throwable cause) {
        super(cause);
        this.code = code;
    }

    public ServiceException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
