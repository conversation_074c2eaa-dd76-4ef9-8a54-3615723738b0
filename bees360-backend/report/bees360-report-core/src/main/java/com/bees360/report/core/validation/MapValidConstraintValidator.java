package com.bees360.report.core.validation;

import org.apache.commons.collections4.CollectionUtils;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.*;


/**
 * <AUTHOR> Yang
 */
public class MapValidConstraintValidator implements ConstraintValidator<MapValid, Map<String, ? extends Object>> {

    private Set<String> keyContains;

    private Set<String> keyNotContains;

    @Override
    public void initialize(MapValid constraintAnnotation) {
        String[] keyContainsArray = constraintAnnotation.keyContains();
        String[] keyNotContainsArray = constraintAnnotation.keyNotContains();
        keyContains = (keyContainsArray == null? null: new HashSet<>(Arrays.asList(keyContainsArray)));
        keyNotContains = (keyNotContainsArray == null? null: new HashSet<>(Arrays.asList(keyNotContainsArray)));
    }

    @Override
    public boolean isValid(Map<String, ? extends Object> map, ConstraintValidatorContext ctx) {
        if(map == null) {
            return true;
        }
        Set<String> keySet = map.keySet();
        return checkKeyContains(keySet) && checkKeyNotContains(keySet);
    }

    private boolean checkKeyContains(Set<String> sourceSet) {
        if(CollectionUtils.isEmpty(keyContains)) {
            return true;
        }
        return CollectionUtils.containsAll(sourceSet, keyContains);
    }

    private boolean checkKeyNotContains(Set<String> sourceSet) {
        if(CollectionUtils.isEmpty(keyNotContains)) {
            return true;
        }
        return sourceSet.stream().allMatch(k -> !keyNotContains.contains(k));
    }
}
