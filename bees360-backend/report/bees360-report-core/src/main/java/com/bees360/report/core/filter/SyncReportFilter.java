package com.bees360.report.core.filter;

import java.util.Set;

public class SyncReportFilter implements ReportFilter {

    private final Set<Integer> syncReportTypes;

    public SyncReportFilter(Set<Integer> syncReportTypes) {
        this.syncReportTypes = syncReportTypes;
    }

    @Override
    public boolean filter(Integer reportType) {
        return syncReportTypes.contains(reportType);
    }
}
