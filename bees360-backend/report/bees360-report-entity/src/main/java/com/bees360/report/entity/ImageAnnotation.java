package com.bees360.report.entity;

import com.bees360.entity.BaseImageAnnotation;
import com.bees360.entity.dto.Point;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/09/28 17:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageAnnotation {

    public static final int DEFAULT_REPORT_SORT = 0;

    private Long annotationId;

    private String imageId;

    private Integer facetId;

    private Long projectId;

    private String annotationPolygon;

    private double centerPointX;

    private double centerPointY;

    private Integer annotationType;

    private Integer usageType;

    private Long generatedBy;

    private Integer sourceType;

    private Long createdTime;

    private String categoryTagName;

    private String tagName;

    private String remark;

    private Integer reportSort;

    private String originAnnotationId;

    private String attribute;

    private double confidenceLevel = BaseImageAnnotation.DEFAULT_CONFIDENCE_LEVEL;

    public static Point center(List<Point> points) {
        double centerPointX = 0;
        double centerPointY = 0;
        for (Point point : points) {
            centerPointX += point.getX();
            centerPointY += point.getY();
        }
        centerPointX /= points.size();
        centerPointY /= points.size();
        return new Point(centerPointX < 0 ? 0 : centerPointX, centerPointY < 0 ? 0 : centerPointY);
    }

    public static ImageAnnotation copyImageAnnotation(ImageAnnotation i) {
        ImageAnnotation imageAnnotation = new ImageAnnotation();
        imageAnnotation.setAnnotationId(i.getAnnotationId());
        imageAnnotation.setImageId(i.getImageId());
        imageAnnotation.setFacetId(i.getFacetId());
        imageAnnotation.setProjectId(i.getProjectId());
        imageAnnotation.setAnnotationPolygon(i.getAnnotationPolygon());
        imageAnnotation.setCenterPointX(i.getCenterPointX());
        imageAnnotation.setCenterPointY(i.getCenterPointY());
        imageAnnotation.setAnnotationType(i.getAnnotationType());
        imageAnnotation.setUsageType(i.getUsageType());
        imageAnnotation.setGeneratedBy(i.getGeneratedBy());
        imageAnnotation.setSourceType(i.getSourceType());
        imageAnnotation.setCreatedTime(i.getCreatedTime());
        imageAnnotation.setCategoryTagName(i.getCategoryTagName());
        imageAnnotation.setTagName(i.getTagName());
        imageAnnotation.setRemark(i.getRemark());
        imageAnnotation.setReportSort(i.getReportSort());
        imageAnnotation.setOriginAnnotationId(i.getOriginAnnotationId());
        imageAnnotation.setConfidenceLevel(i.getConfidenceLevel());
        return imageAnnotation;
    }
}
