package com.bees360.report.entity.vo.summary;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.Map;

@Data
public class SummaryInteriorFloorplan {
    /**
     * Provides the count of bedrooms, bathrooms, closets, etc.
     */
    @Valid
    private Map<@NotEmpty String, Integer> room;
    /**
     * If floor damage discovered. It indicates if damage is present in each type of room.
     */
    @Valid
    private Map<@NotEmpty String, Boolean> hasDamage;
}
