package com.bees360.report.entity.vo.summary;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SummaryInterior {
    /**
     * Conditional of overall interior Excellent-5, Good-4, Average3, Fair-2, Poor-1
     * Required
     */
//    @NotBlank
    private String overallCondition;
    /**
     * If active or inactive leaks are visible
     * Optional
     */
    private Boolean hasVisibleLeaks;
    /**
     * If there is existing interior damage is present
     * Optional
     */
    private Boolean hasExistingDamage;

    private String waterLeakDetection;

    private List<String> coveragePotential;

    @Valid
    private SummaryInteriorPlumbing plumbing;
    @Valid
    private SummaryInteriorElectric electric;
    @Valid
    private SummaryInteriorFloorplan floorplan;
    @Valid
    private SummaryInteriorWaterHeater waterHeater;

    @Valid
    private SummaryInteriorHeatingCooling heatingCooling;

    @Valid
    private SummaryInteriorFireAlarm fireAlarm;

    @Valid
    private SummaryInteriorBurglarAlarm burglarAlarm;

    private List<String> otherSystems;

    private Boolean hasWaterHeater;

    private List<String> comments;

    private SummaryInteriorKitchen kitchen;
}
