package com.bees360.report.entity.enums;

import com.bees360.entity.enums.BaseCodeEnum;
import java.util.List;

public enum AnnotationUsageTypeEnum implements BaseCodeEnum{
	DAMAGE_ANNOTATION(1, "Damage Annotation"),
	SCREENSHOT_RACTANGLE(2, "Screenshot Ractangle"),
	FRAME_RACTANGLE(3, "FRAME Rectangle"),
	MAPPING_2D(4, "2D Mapping"),
    MAPPING_3D(5, "3D Mapping"),
    COMPONENT(6, "Component"),
    HAZARD(7, "Hazard"),
    TAG(8, "Tag"),
    DETAIL(9, "Detail"),
    FEEDBACK(10, "Feedback"),
    CREDIT(11, "Credit"),
	;
	private final int code;
	private final String display;

	AnnotationUsageTypeEnum(int code, String display) {
		this.code = code;
		this.display = display;
	}

    /** 获取Damage用途的Annotation */
    public static List<Integer> getDamageUsage() {
        return List.of(DAMAGE_ANNOTATION.code, COMPONENT.code, DETAIL.code, HAZARD.code, FEEDBACK.code, CREDIT.code);
    }

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}
}
