package com.bees360.report.entity.vo.summary;

import com.bees360.entity.dto.Point;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;

@Data
public class SummaryAnnotationImage {
    private String id;
    private String originalImageId;
    // According to current demand, this length is sufficient.
    @NotNull
    @Size(max=255)
    private String category;
    // According to current demand, this length is sufficient.
    @NotNull
    @Size(max=256)
    private String name;
    private String type;
    private List<SummaryImageAnnotation> annotations;
    private List<Point> screenShootCoordinate;
}
