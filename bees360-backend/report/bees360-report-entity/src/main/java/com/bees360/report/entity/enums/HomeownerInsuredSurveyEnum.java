package com.bees360.report.entity.enums;

import com.bees360.entity.enums.BaseCodeEnum;

public enum HomeownerInsuredSurveyEnum implements BaseCodeEnum {
    FULL(100, "Interior + Exterior Inspection Survey", "static/InteriorExteriorInspectionSurvey.html"),
    NORMAL(200, "Exterior Only Inspection Survey", "static/ExteriorOnlyInspectionSurvey.html");

    private final int code;
    private final String display;
    private final String htmlTemplateKey;

    HomeownerInsuredSurveyEnum(int code, String display, String htmlTemplateKey) {
        this.code = code;
        this.display = display;
        this.htmlTemplateKey = htmlTemplateKey;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    public String getHtmlTemplateKey() {
        return htmlTemplateKey;
    }

    public static HomeownerInsuredSurveyEnum get(int code){
        HomeownerInsuredSurveyEnum[] types = HomeownerInsuredSurveyEnum.values();
        for(HomeownerInsuredSurveyEnum type: types){
            if(type.code == code){
                return type;
            }
        }
        return null;
    }
}
