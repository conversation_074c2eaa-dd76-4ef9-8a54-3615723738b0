package com.bees360.report.entity.vo.summary;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SummaryInteriorElectric {
    /**
     * Ineligible panel, Split bus, fuse boxes, Stablok, Federal Pacific, GTE-Sylvania, Challenger or Zinsco
     * Optional
     */
    private Boolean hasIneligiblePanel;

    private String systemAge;

    private Integer yearUpdated;

    /**
     * Brand of the main electrical panel inspected. This indicates the manufacturer of the panel.
     * Common brands include Square D, General Electric, Siemens, etc.
     * example: Square D
     */
    private String panelBrand = "";

    private Boolean isUpdated;

    /** Indicates the age and update status of the heating and cooling system in the property. * */
    private String systemUpdateStatus;
}
