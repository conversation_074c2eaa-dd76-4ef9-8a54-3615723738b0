package com.bees360.report.entity.vo.summary;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.List;

@Data
public class SummaryHistory {
    @Pattern(regexp = "^[0-9]{4}-[0-9]{2}-[0-9]{2}$", message = "date format must be yyyy-MM-dd.")
    private String dateInspected;
    private String policyNumber;
    private List<@NotNull SummaryImage> image;
}
