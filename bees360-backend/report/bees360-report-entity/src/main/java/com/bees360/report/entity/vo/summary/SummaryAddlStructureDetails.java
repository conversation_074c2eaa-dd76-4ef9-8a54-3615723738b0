package com.bees360.report.entity.vo.summary;

import lombok.Data;

import java.util.Map;

@Data
public class SummaryAddlStructureDetails {

    private String type;

    private String description;

    private Integer squareFootage;

    private Integer value;

    private String condition;

    private String distanceToRisk;

    // Indicates if the additional structure has living space.
    private Boolean hasLivingSpace;

    private Roof roof;

    @Data
    static class Roof {
        // Indicates the roof geometry for the additional structure.
        private Map<String, Integer> geometry;
    }
}
