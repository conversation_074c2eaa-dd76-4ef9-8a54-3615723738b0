package com.bees360.report.entity.vo.summary;

import lombok.Data;

import java.util.List;

@Data
public class SummaryUnprotectedSupplement {

    private Road road;

    @Data
    static class Road {
        // Condition of road access.
        private String accessCondition;

        // Type of road access (e.g. "One Primary Road",
        // "Two or More Access Roads", "Dead End >200 ft", "Cul-de-Sac with 45 ft Radius").
        private String accessType;

        // Type of road (e.g. "Smooth (<PERSON>cre<PERSON>, <PERSON>phalt)", "Rough (Gravel, Chip Seal)", "Other (Dirt)").
        private String type;

        // Width of the access road.
        private String width;

        // Indicates if street is clearly marked.
        private Boolean isStreetClearlyMarked;

        // Indicates if address is easily visible.
        private Boolean isAddressNumberEasilyVisible;

        // Indicates if the driveway meets standards (15 ft of vertical clearance and is 12 ft wide).
        private Boolean isDrivewayMeetsSpec;

        // Description of any difficult access conditions.
        private List<String> difficultConditions;
    }
}
