package com.bees360.report.entity.vo.summary;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class SummaryRisk {
    /**
     * Overall risk condition, possible values include [ "Excellent", "Good", "Average", "Fair", "Poor" ].
     */
    private String overallCondition;
    /**
     * Status of area economy, possible values include [ "improving", "stable", "declining" ].
     */
    private String areaEconomy;
    /**
     * Type of neighborhood, possible values include [ "Urban", "Suburban", "Rural" ].
     */
    private String neighborhood;
    /**
     * Whether the property is located in a gated community.
     */
    private Boolean gatedCommunity;

    private Boolean locatedOnPavedRoad;
    /**
     * Whether the property is an isolated dwelling
     */
    private Boolean isolatedDwelling;
    /**
     * Whether the property is a seasonal dwelling.
     */
    private Boolean seasonalDwelling;
    /**
     * A string representing the type of business operation of the property, possible values include ["Farming",
     * "Gardening", "Professional Services"].
     */
    private String businessOperation;
    /**
     * Whether the property is vacant.
     */
    private Boolean vacant;
    /**
     * If the property is being rented (has tenants)
     */
    private Boolean rental;
    /**
     * If the property is currently for sale.
     */
    private Boolean forSale;
    /**
     * If the property is inaccessible for inspection.
     */
    private Boolean inaccessible;

    private String personInterviewed;

    private Integer yearPurchased;

    private Boolean hasSeismicShutoffValve;

    private Boolean hasCentralStationFireAlarm;

    private Boolean gatedResidenceDriveway;

    private Boolean hasEmployeesOnPremises;

    private Boolean hasKnoxBox;

    private String gateCommunityName;

    private List<String> comments;

    // Indicates occupancy or intended use of the property.
    private String occupancy;

    // Indicates if the property is used for short-term, full-time or seasonal rental.
    private String rentalType;

    // Indicates if someone checks on the home when unoccupied.
    private Boolean hasSomeoneCheckAtHome;

    private List<SummaryRiskDomesticEmployee> domesticEmployees;

    // Indicates if domestic employees have access to the home.
    private Boolean haveDomesticEmployeesHomeAccess;

    // County where the property is located.
    private String county;

    // Indicates if within 1 mile of the coastline.
    private Boolean isCoastlineLessThanOneMile;

    // Coastal parishes/counties for the property location.
    private String coastalParishes;

    // Indicates if the foundation has been retrofitted.
    private Boolean isFoundationRetrofitted;

    // Year the foundation was retrofitted.
    private Integer yearFoundationRetrofitted;

    // Indicates if water heater is double strapped.
    private Boolean isWaterHeaterDoubleStrapped;

    // Indicates if the property is properly elevated from flooding.
    private Boolean isElevatedForFlooding;

    // Indicates if breakaway walls are present.
    private Boolean hasBreakawayWalls;

    // List of risk reduction credits.
    private List<SummaryRiskCredit> credits;

    // Describing natural hazards.
    // Key for text. (e.g. hurricane,landslide,hail,wildfire,freezing,earthquake,flood,wind,tornado,lightning)
    private Map<String, SummaryRiskNaturalHazard> naturalHazards;

    // Indicates if property size is over 5 acres.
    private Boolean isOverFiveAcres;

    private Boolean hasLivestockOrFarming;

    private SummaryRiskWaterBody waterBody;

    private Double hazardScore;

    private Boolean hasCrops;

    private Boolean hasHorses;
}
