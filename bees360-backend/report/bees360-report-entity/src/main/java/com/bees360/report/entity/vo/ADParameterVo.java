package com.bees360.report.entity.vo;

import java.util.List;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2021/7/17
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ADParameterVo {

    private int count = 8;

    // overview每个方向上显示的最多的damage数量
    private int damageShown = 20;

    private float level = 0.95F;

    private boolean forcedStart = false;

    @NotEmpty private List<Integer> fileSourceTypes;

    private List<String> imageIds;
}
