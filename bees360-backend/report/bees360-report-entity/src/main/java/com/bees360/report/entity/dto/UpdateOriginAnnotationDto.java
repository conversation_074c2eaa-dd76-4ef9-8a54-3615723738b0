package com.bees360.report.entity.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

import jakarta.annotation.Nonnull;

@Data
@Builder(setterPrefix = "set", builderMethodName = "newBuilder")
public class UpdateOriginAnnotationDto {
    private long projectId;

    @Nonnull private final List<AnnotationInfo> annotationInfos;

    @Data
    @Builder(setterPrefix = "set", builderMethodName = "newBuilder")
    public static class AnnotationInfo {
        @Nonnull private String imageId;
        @Nonnull private String tagId;
        @Nonnull private Double confidenceLevel;
        @Nonnull private Double[] bbox;
    }
}
