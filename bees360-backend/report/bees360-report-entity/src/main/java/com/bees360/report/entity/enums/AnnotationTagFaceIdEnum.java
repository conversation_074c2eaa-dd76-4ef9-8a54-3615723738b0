package com.bees360.report.entity.enums;

import com.bees360.entity.enums.BaseCodeEnum;

public enum AnnotationTagFaceIdEnum implements BaseCodeEnum{
	FULL(-1, "Full Image tag"),
    NORMAL(0, "Normal Image Annotation Tag")
	;
	private final int code;
	private final String display;

	AnnotationTagFaceIdEnum(int code, String display) {
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}
}
