package com.bees360.report.entity.vo.summary;

import com.bees360.report.core.validation.IntSum;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class SummaryExterior {
    /**
     * Whether the overall condition of exterior is satisfactory.
     */
//    @NotBlank
    private String overallCondition;
    /**
     * Siding material and its corresponding percentage. The object's properties are the name of the siding material,
     * and the values represents its percentage. Possible values for siding materials include ["Asbestos", "Vingl",
     * "Metal", "Stone", "Brick Veneer", "Hardiplank", "Stucco", "Concrete Block", "Wood Shake", "Aluminum", "Log",
     * "Stone Veneer"].
     */
    // @NotNull
    @IntSum(value = 100, message = "the sum should be 100.")
    private Map<@NotNull String, @NotNull Integer> siding;
    /**
     * Whether any shutters discovered.
     */
    private Boolean hasShutters;
    /**
     * Whether property has porch.
     */
    private Boolean hasPorch;
    /**
     * If stairs are present without hand rails
     * Optional
     */
    private Boolean hasStairsWithoutHandRails;
    /**
     * If yard has excessive yard debris/trash
     * Optional
     */
    private Boolean hasYardDebris;
    /**
     * If yard has unregistered vehicles on premises
     * Optional
     */
    private Boolean hasDiscardedVehicles;
    /**
     * If there are trees or tree limbs touching or overhanging home
     * Optional
     */
    private Boolean hasTreeLimbs;
    /**
     * If pool present does it have a 4 ft fence with self locking gate?
     * Optional
     */
    private Boolean hasPoolWithoutFence;
    /**
     * # of dogs over 40lbs
     * Optional
     */
    private Integer numDogPresent;
    /**
     * If there is a dog or beware of dog sign
     * Optional
     */
    private Boolean hasDogPresent;
    /**
     * List of text comments to exterior.
     */
    // @NotNull
    private List<@NotNull String> comments = new ArrayList<>();

    private List<@NotNull SummaryComparison> comparison = new ArrayList<>();
    /**
     * If siding damage discovered
     */
    private Boolean hasSidingDamage;
    /**
     * If wall cracks discovered
     */
    private Boolean hasWallCracks;
    /**
     * If pealing paint on siding discovered
     */
    private Boolean hasPealingPaint;
    /**
     * If window screen damage discovered
     */
    private Boolean hasWindowDamage;
    /**
     * If water damage on exterior discovered
     */
    private Boolean hasWaterDamage;
    /**
     * If chimney damage discovered
     */
    private Boolean hasChimneyDamage;
    /**
     * If algae/moss discovered on roof
     */
    private Boolean hasMildewOrMoss;
    /**
     * If swimming pool fence/cage discovered
     */
    private Boolean hasPoolCage;
    /**
     * If swimming pool diving board discovered
     * If swimming pool slide discovered
     */
    private Boolean hasDivingBoardOrSlide;
    /**
     * Pet type with breed, such as Dog - Labrador Retriever, Cat, etc.
     */
    private List<String> petType;
    /**
     * Provides the count of pet dog, pet cat, etc.
     */
    @Valid
    private Map<@NotEmpty String, Integer> pet;
    /**
     * If dog sign presented on the property
     */
    private Boolean hasDogSign;
    /**
     * If pest activity discovered
     */
    private Boolean hasPestActivity;
    /**
     * If trampoline discovered
     */
    private Boolean hasTrampoline;
    /**
     * If watercraft discovered
     */
    private Boolean hasWatercraft;
    /**
     * If there is a play/swing set in the exterior area.
     */
    private Boolean hasPlaySwingSet;
    /**
     * If there is a tree house in the exterior area.
     */
    private Boolean hasTreeHouse;
    /**
     * If there is a basketball hoop in the exterior area.
     */
    private Boolean hasBasketballHoop;
    /**
     * If there is an ATV (all-terrain vehicle) in the exterior area.
     */
    private Boolean hasATV;
    /**
     * If there is a skateboard or bike ramp in the exterior area.
     */
    private Boolean hasSkateboardOrBikeRamp;
    /**
     * If there is a dirt bike in the exterior area.
     */
    private Boolean hasDirtBike;
    /**
     * If there is a propane or fuel tank in the exterior area.
     */
    private Boolean hasPropaneOrFuelTank;

    private Boolean hasElevator;

    private String elevatorOpenTo;
    // 仅在 valut中使用，protobuf没有提供。
    private String hasHallwayVideoSurveillance;
    /**
     * If there is a swimming pool in the exterior area.
     */
    private Boolean hasSwimmingPool;
    /**
     * If there is an awning in the exterior area.
     */
    private Boolean hasAwning;

    private Boolean hasLobbyCamera;

    // Indicates if the garage door is braced or reinforced.
    private Boolean hasBracedGarageDoor;

    // Indicates if the structure has a wind load rating sticker.
    private Boolean hasWindLoadSticker;

    // Indicates if exterior doors swing inward.
    private Boolean hasInswingDoors;

    // Indicates if skylights are present.
    private Boolean hasSkylights;

    // Indicates if a vicious/dangerous dog is present.
    private Boolean hasViciousDog;

    // Indicates if an outdoor fireplace is present.
    private Boolean hasOutdoorFireplace;

    private Boolean isEIFS;

    private Boolean hasPoolSelfLatchingGate;

    private String primarySidingMaterial;

    // Indicates if a fire pit is present.
    private Boolean hasFirePit;
}
