package com.bees360.report.entity.vo.summary;

import com.bees360.report.core.validation.MapValid;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.bees360.report.core.validation.IntSum;

/**
 * <AUTHOR>
 */
@Data
public class SummaryRoof {
    /**
     * Overall roof condition, possible values include [ "Excellent", "Good", "Average", "Fair", "Poor" ].
     */
//    @NotBlank
    private String overallCondition;
    /**
     * Estimated roof age, possible values include [less than 1 year, 1 ~ 5 years, 5 - 10 years].
     */
//    @NotBlank
    private String estAge;
    /**
     * Estimated roof remaining life, possible values include [less than 1 year, 1 ~ 5 years, 5 - 10 years].
     */
//    @NotBlank
    private String estLife;
    /**
     * An object presenting roof geometry and its corresponding percentage. Possible properties include [ "Flat",
     * "Gable", "Hip" ]. The value of each property presents its corresponding percentage.
     */
    // @NotNull
    @IntSum(value = 100, message = "the sum should be 100.")
    private Map<@NotNull String, @NotNull Integer> geometry;
    /**
     * List of dominant covering material, including [ "Asphalt", "Modified Bitumen", "Metal", "Wood", "Tile",
     * "Aluminum" ].
     */
    // @NotNull
    private List<@NotNull String> coveringMaterial = new ArrayList<>();
    /**
     * Whether any solar panels present on roof.
     */
    private Boolean hasSolarPanel;
    /**
     * If roof has curling shingles
     * Optional
     */
    private Boolean hasCurlingShingles;
    /**
     * If roof has granular loss
     * Optional
     */
    private Boolean hasGranularLoss;
    /**
     * If roof has missing or damaged shingles
     * Optional
     */
    private Boolean hasMissingDamagedShingles;
    /**
     * If roof has been repaired with patches
     * Optional
     */
    private Boolean hasPatchedAreas;
    /**
     * If roof has a tarp
     * Optional
     */
    private Boolean hasTarp;
    /**
     * A Object that records the roof meterial and its corresponding percentage. Possible roof meterial properties
     * include [ "CompositeShingles", "BuildupRoofNoGravel", "ClayConcreteTiles", "LightMetalPanels",
     * "SinglePlyMembrane", "SinglePlyMembraneBallasted", "Slate", "StandingSeamMetalRoof", "WoodenShingles" ].
     */
    // @NotNull
    @IntSum(value = 100, message = "the sum should be 100.")
    @MapValid(keyContains = {"CompositeShingles", "BuildupRoofNoGravel"},
        message = "should contain keys {CompositeShingles, BuildupRoofNoGravel}")
    private Map<@NotNull String, @NotNull Integer> material;
    /**
     * List of comments to the roof.
     */
    // @NotNull
    private List<@NotNull String> comments = new ArrayList<>();

    private List<@NotNull SummaryComparison> comparison = new ArrayList<>();
    /**
     * If debris discovered on roof
     */
    private Boolean hasRoofDebris;

    private String age;

    private Integer yearReplaced;

    // Indicates if shingles are missing from the roof.
    private Boolean hasMissingShingles;

    // Indicates if shingles on the roof are damaged.
    private Boolean hasDamagedShingles;

    // Indicates if shingles on the roof are loose.
    private Boolean hasLooseShingles;

    private String pitch;

    private String slopeOfSite;

    private Integer chimneyCount;

    private List<String> chimneyType;

    /**
     * algae/Moss on roof
     */
    private Boolean hasAlgaeOrMoss;

    /**
     * Other Roof Anomalies
     */
    private Boolean hasOtherRoofAnomalies;

    public void setHasMissingShingles(Boolean hasMissingShingles) {
        if (hasMissingDamagedShingles == null) {
            this.hasMissingDamagedShingles = hasMissingShingles;
        } else {
            this.hasMissingDamagedShingles = this.hasMissingDamagedShingles || hasMissingShingles;
        }

        this.hasMissingShingles = hasMissingShingles;

    }

    public void setHasDamagedShingles(Boolean hasDamagedShingles) {
        if (hasMissingDamagedShingles == null) {
            this.hasMissingDamagedShingles = hasDamagedShingles;
        } else {
            this.hasMissingDamagedShingles = this.hasMissingDamagedShingles || hasDamagedShingles;
        }

        this.hasDamagedShingles = hasDamagedShingles;
    }
}
