package com.bees360.report.entity.vo.summary;

import com.bees360.entity.openapi.reportsummary.SummaryImage;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.List;

@Data
public class SummaryComparison {
    /**
     * 匹配所有非中文
     */
    @Pattern(regexp = "[^\\u4e00-\\u9fa5]*", message = "text may not contain Chinese.")
    private String comment;
    /**
     * 匹配所有非中文
     */
    @Pattern(regexp = "[^\\u4e00-\\u9fa5]*", message = "text may not contain Chinese.")
    private String change;
    private String direction;
    private List<@Valid @NotNull SummaryImage> image;
}
