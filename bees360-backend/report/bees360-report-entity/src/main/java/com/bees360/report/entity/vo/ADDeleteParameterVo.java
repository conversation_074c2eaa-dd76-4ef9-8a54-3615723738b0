package com.bees360.report.entity.vo;

import com.bees360.image.ImageTagEnum;
import java.util.List;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 删除ADD annotation参数类
 * <AUTHOR>
 * @since 2021/7/17
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ADDeleteParameterVo {

    private double level = 0.95F;

    /**
     * @see ImageTagEnum
     */
    @NotEmpty private List<Integer> damageTypes;

    private List<String> originAnnotationIds;
}
