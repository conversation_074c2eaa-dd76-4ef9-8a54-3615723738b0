package com.bees360.report.entity.vo.summary;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SummaryBldg {
    /**
     * Building overall condition, possible values include [ "Excellent", "Good", "Average", "Fair", "Poor" ].
     */
    // @NotNull
    private String overallCondition;
    /**
     * Dwelling type, possible values include [ "Single-family", "Multi-family", "Condo" ].
     */
    // @NotNull
    private String dwellingType;
    /**
     * Construction material, possible values include [ "Concrete Block", "Frame", "Fire Resistive" ].
     */// @NotNull
    private String construction;
    /**
     * Building partially or completed constructed over water
     * Optional
     */
    private Boolean constructionOverWater;
    /**
     * The type of garage, such as ["Attached", "Detached", "Built-in", "Carport", "None"].
     */
    // @NotNull
    private String garage;
    /**
     * The type of HVAC, such as ["Central", "Window Unit", "Split Unit", "Baseboard heating", "Gas forced air
     * heating"].
     */
    // @NotNull
    private String hvac;
    /**
     * The number of stories of the main dwelling
     */
    private Integer numStories;
    /**
     * List of any wind protections discovered, such as ["Impact Resistant Door", "High Impact Glass"]. If none
     * observed, the field will be absent or has null value.
     */
    // @NotNull
    private List<@NotNull String> windProtections = new ArrayList<>();
    /**
     * Whether hurricane straps was discovered.
     */
    private Boolean hurricaneStraps;
    /**
     * If building is Designated Historic Home
     * Optional
     */
    private Boolean designatedHistoricHome;
    /**
     * The type of foundation, such as ["Slab on Grade", "Concrete Slab", "Basement"]
     */
    // @NotNull
    private String foundation;
    /**
     * Whether the building is manufactured or a mobile home.
     */
    private String manufacturedOrMobileHome;
    /**
     * If the property has noticeable exisiting exterior damage
     * Optional
     */
    private Boolean exteriorDamage;
    /**
     * If foundation damage discovered
     */
    private Boolean hasFoundationCracks;
    /**
     * If fence damage discovered
     */
    private Boolean hasFence;
    /**
     * If the property is under construction
     */
    private Boolean underConstruction;
    /**
     * If the property is under renovation
     */
    private Boolean underRenovation;

    private Boolean hasUndisclosedCocOrRenovations;

    private String windProtectionLevel;

    // Indicates if renovations are planned for the building.
    private Boolean plannedRenovation;

    // Indicates if renovations will occur within the next six months.
    private Boolean renovationWithinSixMonths;

    // Provides details about planned or ongoing renovations.
    private String renovationDetails;

    private Integer bldgFloor;

    private Integer residenceFloor;

    private Boolean recentRenovation;

    private Boolean isDeveloperSpeculation;

    private List<String> locale;

    private String architecturalStyle;

    private String constructionQuality;

    private String physicalShape;

    private Boolean otherSidingAnomalies;
}
