package com.bees360.report.entity.vo.summary;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.List;

@Data
public class SummaryFactor {

    @Pattern(regexp = "[^\\u4e00-\\u9fa5]*", message = "text may not contain chinese.")
    private String text;
    @NotBlank(message = "factor name can not be blank")
    private String name;
    private String direction;
    private List<@Valid @NotNull SummaryImage> image;
}
