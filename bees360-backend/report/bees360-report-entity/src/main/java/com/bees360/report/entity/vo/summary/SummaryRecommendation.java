package com.bees360.report.entity.vo.summary;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SummaryRecommendation {
    /**
     * 匹配所有非中文
     */
    @NotBlank
    @Pattern(regexp = "[^\\u4e00-\\u9fa5]*", message = "text may not contain chinese.")
    private String text;
    @NotNull
    private List<@Valid @NotNull SummaryImage> image;
}
