package com.bees360.report.entity.vo.summary;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SummaryInteriorPlumbing {
    /**
     * If there are any plumbing fixtures without shut off valves
     * Optional
     */
    private Boolean noShutoffValve;
    /**
     * If the water heater is older than 15 years
     * Optional
     */
    private Boolean hasOldWaterHeater;
    /**
     * If the water heater is rusting or in poor condition, has exposed wires or no TPR Valve
     * Optional
     */
    private Boolean hasPoorWaterHeaterCondition;

    private String systemAge;

    private Boolean isUpdated;

    private Integer yearUpdated;

    private List<String> laundryRoomLocations;

    private Boolean hasRubberHoses;

    private List<String> rubberHoseLocations;

    private Boolean hasPolybutylenePipes;

    private Boolean hasCastIronPipes;

    private List<String> concerns;

    private Boolean hasUninsulatedPipes;

    private List<String> uninsulatedPipesLocations;

    /**
     * Indicates if there is evidence that property has galvanized steel pipes based on visual inspection.
     * Galvanized steel pipes may be prone to flow restrictions due to internal corrosion build up over time.
     * example: true
     */
    private Boolean hasGalvanizedPipes;

    private String systemUpdateStatus;

    private Boolean hasIneligiblePlumbing;
}
