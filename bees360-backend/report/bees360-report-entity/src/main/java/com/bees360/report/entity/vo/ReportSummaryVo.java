package com.bees360.report.entity.vo;

import com.bees360.report.entity.vo.summary.SummaryAnnotationImage;
import com.bees360.report.entity.vo.summary.SummaryCommunity;
import com.bees360.report.entity.vo.summary.SummaryProject;
import com.bees360.report.entity.vo.summary.SummaryUnprotectedSupplement;
import com.bees360.report.entity.vo.summary.SummaryAddlStructureDetails;
import com.bees360.report.entity.vo.summary.SummaryBldg;
import com.bees360.report.entity.vo.summary.SummaryExterior;
import com.bees360.report.entity.vo.summary.SummaryFactor;
import com.bees360.report.entity.vo.summary.SummaryFireProtection;
import com.bees360.report.entity.vo.summary.SummaryHistory;
import com.bees360.report.entity.vo.summary.SummaryInterior;
import com.bees360.report.entity.vo.summary.SummaryRecommendation;
import com.bees360.report.entity.vo.summary.SummaryRisk;
import com.bees360.report.entity.vo.summary.SummaryRoof;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Yang
 */
@Data
public class ReportSummaryVo {
    /**
     * The built year as a four-digit integer.
     */
    private Integer yearBuilt;
    /**
     * The total living area in Sq.Ft..
     */
    private Double livingArea;
    /**
     * The lot size in acres.
     */
    private Double lotSize;
    @Valid
    private SummaryRisk risk;
    @Valid
    private SummaryBldg bldg;
    @Valid
    private SummaryRoof roof;
    @Valid
    private SummaryExterior exterior;
    @Valid
    private SummaryInterior interior;
    @Valid
    private SummaryFireProtection fireProtection;
    @Valid
    private SummaryCommunity community;
    /**
     * List of additional structures on the property, such as ["Storage Shed", "Horse Stable", "Garden", "Barn"]
     */
    // @NotNull
    private List<@NotNull String> addlStructures = new ArrayList<>();

    private List<SummaryAddlStructureDetails> addlStructureDetails = new ArrayList<>();

    /**
     * List of text comments of hazards discovered
     */
    // @NotNull
    private List<@NotNull String> hazards = new ArrayList<>();
    /**
     * List of recommendations. Each recommendation is an object with a text property, and an image property. text is a
     * String describing the recommendation, and image is an object that contains at least an id property that
     * represents the image's unique identifier.
     */
    // @NotNull
    private List<@Valid @NotNull SummaryRecommendation> recommendations = new ArrayList<>();

    private List<@Valid @NotNull SummaryFactor> factors = new ArrayList<>();

    private List<@Valid @NotNull SummaryHistory> history = new ArrayList<>();

    private SummaryUnprotectedSupplement unprotectedSupplement;

    private List<@Valid SummaryAnnotationImage> images;

    private SummaryProject project;
}
