<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <!-- <EMAIL> optimize the setting file -->
    <properties>
        <!--set mybatis info output -->
        <property name="dialect" value="mysql"/>
    </properties>

    <settings>
        <!-- set SIMPLE executor for a batch of insert which was expected to return self-growing id -->
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <!--         <setting name="defaultExecutorType" value="BATCH" /> -->
        <setting name="cacheEnabled" value="true"/>
        <setting name="lazyLoadingEnabled" value="true"/>
        <setting name="aggressiveLazyLoading" value="false"/>
        <setting name="multipleResultSetsEnabled" value="true"/>
        <setting name="useColumnLabel" value="true"/>
        <setting name="useGeneratedKeys" value="true"/>
        <setting name="autoMappingBehavior" value="FULL"/>
        <setting name="defaultStatementTimeout" value="25000"/>
        <setting name="logImpl" value="Log4j2"/>
    </settings>

</configuration>
