package com.bees360.report.config;

import com.bees360.internal.ai.entity.enums.ReportTypeEnum;
import com.bees360.report.core.filter.ReportFilter;
import com.bees360.report.core.filter.SyncReportFilter;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties
public class ReportBeanConfig {

    @Bean
    public ReportFilter syncReportFilter() {
        Set<Integer> syncReportTypes =
                Stream.of(ReportTypeEnum.values())
                        .filter(ReportTypeEnum::isSyncToWeb)
                        .map(ReportTypeEnum::getCode)
                        .collect(Collectors.toSet());
        return new SyncReportFilter(syncReportTypes);
    }
}
