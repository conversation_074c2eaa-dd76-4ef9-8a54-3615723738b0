package com.bees360.report.controller;

import com.bees360.base.ResponseJson;
import com.bees360.entity.dto.ReportPrefixDto;
import com.bees360.entity.dto.ServiceReportTypeDto;
import com.bees360.report.service.ProjectReportService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 *
 * @date 2021/1/05 15:36
 */
@RestController
@RequestMapping("/constants")
public class ConstantsController {

    @Autowired
    private ProjectReportService projectReportService;

    @GetMapping("/report-prefix")
    public ResponseJson getReportFilePrefix() {
        ResponseJson response = new ResponseJson();
        ReportPrefixDto prefix = projectReportService.getReportFilePrefix();
        response.setData(prefix);
        return response;
    }

    /**
     * get report types
     *
     * @return reportTypes
     */
    @GetMapping(value = "/report-types")
    public ResponseJson getReportTypes() {
        ResponseJson json = new ResponseJson();
        Map<String, List<ServiceReportTypeDto>> resultMap = new HashMap<>(1);
        List<ServiceReportTypeDto> reportTypes = projectReportService.getReportTypes();
        resultMap.put("reportTypes", reportTypes);
        json.setData(resultMap);
        return json;
    }
}
