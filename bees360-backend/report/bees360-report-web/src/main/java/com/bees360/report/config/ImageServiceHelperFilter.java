package com.bees360.report.config;

import com.bees360.report.service.config.ImageThreadHelpUtil;
import org.apache.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021/05/22 19:49
 */
@WebFilter(urlPatterns = "/*")
@Component
public class ImageServiceHelperFilter extends OncePerRequestFilter {


    @Override
    protected void doFilterInternal(HttpServletRequest request,
        HttpServletResponse httpServletResponse,
        Fi<PERSON><PERSON>hai<PERSON> filterChain) throws ServletException, IOException {
        String refer = getRefer(request);
        ImageThreadHelpUtil.clear();
        ImageThreadHelpUtil.setRefer(refer);
        filterChain.doFilter(request, httpServletResponse);
    }

    private String getRefer(HttpServletRequest request) {
        return request.getHeader(HttpHeaders.REFERER);
    }
}
