package com.bees360.report.controller;

import com.bees360.entity.BaseImageAnnotation;
import com.bees360.internal.ai.exchange.ai2client.ProjectImageProto;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.grpc.api.report2web.AnnotatedImageBatchCreateRequest;
import com.bees360.report.grpc.api.report2web.AnnotatedImageBatchCreateResponse;
import com.bees360.report.grpc.api.report2web.AnnotatedImageRequest;
import com.bees360.report.grpc.api.report2web.AnnotatedImageVo;
import com.bees360.report.grpc.api.report2web.AnnotationIds;
import com.bees360.report.grpc.api.report2web.AnnotationReportSortParam;
import com.bees360.report.grpc.api.report2web.ImageAnnotationBatchDeleteRequest;
import com.bees360.report.grpc.api.report2web.ImageCreatedVo;
import com.bees360.report.grpc.api.report2web.ImageEffectedVo;
import com.bees360.report.grpc.api.report2web.ImagesEffectedVo;
import com.bees360.report.grpc.api.report2web.MappingBatchCreateVo;
import com.bees360.report.grpc.api.report2web.MappingCreateParam;
import com.bees360.report.grpc.api.report2web.MappingCreateVo;
import com.bees360.report.grpc.api.report2web.MappingImage;
import com.bees360.report.grpc.api.report2web.MappingInfo;
import com.bees360.report.service.ImageAnnotationTagService;
import com.bees360.report.service.NewImageService;
import com.bees360.user.User;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/04/01 11:46
 */
@Slf4j
@RestController
@RequestMapping("/new-image")
public class ImageController {

    @Autowired private ImageAnnotationTagService imageAnnotationTagService;

    @Autowired private NewImageService imageService;

    @GetMapping("/{projectId:\\d+}/list")
    public ProjectImageProto.ProjectImageList listImagesWithoutDeleted(@PathVariable long projectId,
            boolean requireAnnotated) throws ServiceException {
        return imageService.listImages(projectId, requireAnnotated);
    }

    /**
     * 根据ImageId 获取图片标记的相关信息 每次获取都根据ImageAnnotation列表判断是否已经存在相关的annotatedInfo 没有则异步生成返回
     *
     * @return annotatedId, annotationId
     */
    @GetMapping("/{imageId}")
    public ImageCreatedVo getByImageId(@PathVariable String imageId) throws ServiceException {
        return imageService.createAnnotatedImageVo(imageId);
    }

    /**
     * 根据Image和Annotation 记录图片标记的相关信息
     *
     * @return annotatedId, annotationId
     */
    @PostMapping("/{imageId}/annotation")
    public ImageEffectedVo createAnnotatedImage(@AuthenticationPrincipal User user, @PathVariable String imageId,
            @RequestBody AnnotatedImageRequest annotatedRequest) throws ServiceException {
        log.info(
                "create annotated imageId: {}, user: {}, mapping: {}",
                imageId,
                user.getId(),
                annotatedRequest);

        return imageService.createAnnotatedImage(
                user,
                imageId,
                annotatedRequest.toBuilder()
                        .setConfidenceLevel(BaseImageAnnotation.DEFAULT_CONFIDENCE_LEVEL)
                        .build());
    }

    /**
     * 批量根据Image和Annotation 记录图片标记的相关信息，该接口会依次调用单个处理的方法
     *
     * @param user 操作用户
     * @param batchCreateRequest 记录图片标记的相关信息的请求内容
     * @return 成功的annotatedId, annotationId，失败的requestList
     */
    @PostMapping("/{imageId}/annotation/batch")
    public AnnotatedImageBatchCreateResponse batchCreateAnnotatedImage(
            @AuthenticationPrincipal User user,
            @PathVariable String imageId,
            @RequestBody AnnotatedImageBatchCreateRequest batchCreateRequest){
        log.info(
            "create annotated imageId: {}, user: {}, mapping: {}",
            imageId,
            user.getId(),
            batchCreateRequest);
        List<AnnotatedImageRequest> failedRequest = new ArrayList<>();
        var successResults =
                batchCreateRequest.getRequestList().stream()
                        .map(
                                request -> {
                                    var result =
                                            createSingleAnnotateCatchFailedReturningNull(
                                                    user, imageId, request);
                                    if (result == null) {
                                        failedRequest.add(request);
                                    }
                                    return result;
                                })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
        return AnnotatedImageBatchCreateResponse.newBuilder()
                .setImageId(imageId)
                .addAllImageEffectedVos(successResults)
                .addAllFailedRequest(failedRequest)
                .build();
    }

    /**
     * 根据Image和Annotation 记录图片标记的相关信息。若记录图片标记失败，则返回null
     *
     * @return annotatedId, annotationId
     */
    private ImageEffectedVo createSingleAnnotateCatchFailedReturningNull(
            User user,
            String imageId,
            AnnotatedImageRequest request) {
        try {
            return imageService.createAnnotatedImage(
                    user,
                    imageId,
                    request.toBuilder()
                            .setConfidenceLevel(BaseImageAnnotation.DEFAULT_CONFIDENCE_LEVEL)
                            .build());
        } catch (ServiceException e) {
            return null;
        }
    }

    /**
     * 根据ImageId 获取图片绿色阴影mapping相关信息
     */
    @PutMapping("/mapping/{imageId}")
    public MappingCreateVo createMappingImage(@PathVariable String imageId, @RequestBody MappingImage mappingImage)
        throws ServiceException {
        return imageService.createMappingImage(imageId, mappingImage.getMappingImageId());
    }

    /**
     * 获取图片绿色阴影mapping相关信息 没有则异步生成返回
     */
    @PutMapping("/mapping/batch")
    public MappingBatchCreateVo batchCreateMappingImage(@RequestBody MappingCreateParam createParam)
        throws ServiceException {
        return imageService.createMappingImages(createParam);
    }

    @PutMapping("/mapping/{imageId}/fill")
    public AnnotatedImageVo fillMappingToOverview(@AuthenticationPrincipal User user, @PathVariable String imageId,
        @RequestBody(required = false) MappingInfo mappingInfo) throws ServiceException {
        log.info("mapping fill imageId: {}, mapping: {}", imageId, mappingInfo);
        return imageService.fillMappingToOverview(imageId, mappingInfo, user);
    }

    @GetMapping("/{imageId}/effect")
    public ImageEffectedVo refreshGetEffectedImageVo(@PathVariable String imageId) throws ServiceException {
        return imageService.refreshGetEffectedImageVo(imageId);
    }

    /**
     * 删除单个annotation的记录
     *
     * @return image对应的最新annotatedImage相关信息
     */
    @DeleteMapping("/{imageId}/annotation/{annotationId}")
    public ImageCreatedVo delImageAnnotationTag(@AuthenticationPrincipal User user,
                                                @PathVariable String imageId,
                                                @PathVariable String annotationId,
                                                @RequestParam(defaultValue="false") Boolean onlyDeleteSelf) throws ServiceException {
        return imageService.delImageAnnotationTag(user, imageId, annotationId, onlyDeleteSelf);
    }

    /**
     * 批量删除annotation记录
     *
     * @return image对应的最新annotatedImage相关信息
     */
    @DeleteMapping("/{imageId}/annotation/batch")
    public ImageCreatedVo delImageAnnotationTags(@AuthenticationPrincipal User user,
            @PathVariable String imageId, @RequestBody AnnotationIds annotationIds) throws ServiceException {
        List<String> list = annotationIds.getAnnotationIdsList().stream().collect(Collectors.toUnmodifiableList());
        var onlyDeleteSelf = annotationIds.getOnlyDeleteSelf();
        return imageService.delImageAnnotationTags(user, imageId, list, onlyDeleteSelf);
    }

    /**
     * Batch delete annotation records
     *
     * @return All involved images and their corresponding latest annotated image related information.
     */
    @DeleteMapping("/annotation/batch")
    public ImagesEffectedVo batchDeleteImageAnnotation(@AuthenticationPrincipal User user,
                                                       @RequestBody ImageAnnotationBatchDeleteRequest request) throws ServiceException {
        return imageService.batchDeleteImageAnnotation(user, request);
    }

    /**
     * 更改ImageAnnotation的report_sort信息
     */
    @PutMapping("/{imageId}/report-sort")
    public void updateImageAnnotationReportSort(@AuthenticationPrincipal User user,
            @PathVariable String imageId, @RequestBody AnnotationReportSortParam sortParam) {
        imageAnnotationTagService.updateImageAnnotationReportSort(user, imageId, sortParam);
    }
}
