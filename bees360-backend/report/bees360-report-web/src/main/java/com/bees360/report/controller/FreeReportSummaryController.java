package com.bees360.report.controller;

import com.bees360.base.ResponseJson;
import com.bees360.entity.dto.ProjectReportFileDto;
import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.dto.ReportSummaryDto;
import com.bees360.report.entity.vo.EditorDataVo;
import com.bees360.report.entity.vo.EditorImageVo;
import com.bees360.report.entity.vo.ReportSummaryUnverifiedPercentageVo;
import com.bees360.report.entity.vo.ReportSummaryVo;
import com.bees360.report.service.ProjectReportService;
import com.bees360.report.service.util.summary.ReportSummaryConverter;
import com.bees360.report.service.util.summary.SummaryValidator;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Valid;
import jakarta.validation.Validator;
import java.util.List;
import java.util.function.Predicate;

/**
 * Request fields for summary are not only from {@code ReportSummaryDto}, the customized fields are accepted.
 */
@Log4j2
@RestController
@RequestMapping("/projects/{projectId:\\d+}/reports")
@ConditionalOnProperty(
    prefix = "report-web.controller.free-summary-controller",
    name = "enabled",
    havingValue = "true")
public class FreeReportSummaryController {

    @Data
    static class NewEditorDataVo {

        private ProjectReportFileDto report;

        private List<EditorImageVo> images;

        private JsonNode reportSummary;

        private String invoiceSummary;

        public String getReportSummaryJson() {
            if (reportSummary == null) {
                return null;
            }
            return reportSummary.toString();
        }
    }

    @Data
    static class FreeReportSummaryDto {

        private int reportType;

        private JsonNode reportSummary;

        public String getReportSummaryJson() {
            if (reportSummary == null) {
                return null;
            }
            return reportSummary.toString();
        }
    }

    private final SummaryValidator fullSummaryValidator;

    private final ProjectEsService projectEsService;

    private final ProjectReportService projectReportService;

    private final ObjectMapper objectMapper;
    private final ReportSummaryConverter reportSummaryConverter;
    private final Validator validator;

    private final Predicate<Integer> unverifiedPercentagePredicate;

    public FreeReportSummaryController(
        SummaryValidator fullSummaryValidator,
        ProjectEsService projectEsService,
        ProjectReportService projectReportService,
        ObjectMapper objectMapper,
        ReportSummaryConverter reportSummaryConverter,
        Validator validator,
        Predicate<Integer> unverifiedPercentagePredicate) {
        this.fullSummaryValidator = fullSummaryValidator;
        this.projectEsService = projectEsService;
        this.projectReportService = projectReportService;
        this.objectMapper = objectMapper;
        this.reportSummaryConverter = reportSummaryConverter;
        this.validator = validator;
        this.unverifiedPercentagePredicate = unverifiedPercentagePredicate;
        log.info(
            "Created {}(fullSummaryValidator={}, projectEsService={}, projectReportService={}, "
                + "objectMapper={}, reportSummaryConverter={}, validator={}, unverifiedPercentagePredicate={}).",
            this,
            this.fullSummaryValidator,
            this.projectEsService,
            this.projectReportService,
            this.objectMapper,
            this.reportSummaryConverter,
            this.validator,
            this.unverifiedPercentagePredicate);
    }

    @PostMapping("/summary")
    public ResponseJson upsertReportSummary(@AuthenticationPrincipal com.bees360.user.User user, @PathVariable long projectId,
                                            @RequestBody FreeReportSummaryDto request) throws ServiceException {
        var reportSummaryJson = request.getReportSummaryJson();
        var reportType = request.getReportType();
        var reportSummary = convertAndValidateSummary(reportSummaryJson, reportType);

        var dto = new ReportSummaryDto();
        dto.setReportType(reportType);
        dto.setReportSummary(reportSummary);

        projectReportService.upsertReportSummary(projectId, user.getId(), dto, reportSummaryJson);
        return new ResponseJson();
    }

    @PostMapping("/summary/validation")
    public ResponseJson validateReportSummary(@PathVariable long projectId,
                                              @RequestBody FreeReportSummaryDto request) throws ServiceException {
        var reportSummaryJson = request.getReportSummaryJson();
        var reportType = request.getReportType();
        var reportSummary = convertAndValidateSummary(reportSummaryJson, reportType);

        var dto = new ReportSummaryDto();
        dto.setReportType(reportType);
        dto.setReportSummary(reportSummary);

        var project = projectEsService.findProjectByProjectId(projectId);
        ProjectServiceTypeEnum serviceType = ProjectServiceTypeEnum.getEnum(project.getServiceType());
        fullSummaryValidator.validate(projectId, serviceType, reportType, reportSummary);
        return new ResponseJson();
    }

    @PostMapping("/editor")
    public ResponseJson saveOrUpdateReportEditorData(@AuthenticationPrincipal com.bees360.user.User user,
                                                     @PathVariable long projectId, @Valid @RequestBody NewEditorDataVo editorData) throws ServiceException {
        var reportSummaryJson = editorData.getReportSummaryJson();
        var reportType = editorData.getReport().getReportType();
        var reportSummary = convertAndValidateSummary(reportSummaryJson, reportType);
        var vo = new EditorDataVo();
        vo.setReport(editorData.getReport());
        vo.setImages(editorData.getImages());
        vo.setReportSummary(reportSummary);
        vo.setInvoiceSummary(editorData.getInvoiceSummary());
        projectReportService.saveOrUpdateReportEditorData(projectId, user.getId(), vo, reportSummaryJson);
        return new ResponseJson();
    }

    private ReportSummaryVo convertAndValidateSummary(String reportSummaryJson, int reportType) {
        Class<? extends ReportSummaryVo> clazz;
        if (unverifiedPercentagePredicate.test(reportType)) {
            clazz = ReportSummaryUnverifiedPercentageVo.class;
        } else {
            clazz = ReportSummaryVo.class;
        }
        return validateSummary(reportSummaryConverter.toSummaryVo(reportSummaryJson, clazz));
    }

    private ReportSummaryVo validateSummary(ReportSummaryVo summaryVo) {
        if (summaryVo != null) {
            var result = validator.validate(summaryVo);
            if (!result.isEmpty()) {
                throw new ConstraintViolationException(result);
            }
        }
        return summaryVo;
    }
}
