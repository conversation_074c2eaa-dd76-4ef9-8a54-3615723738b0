package com.bees360.report.advice;

import com.alibaba.fastjson.JSONObject;
import com.bees360.base.ResponseJson;
import com.bees360.internal.ai.exchange.ai2client.ResponseProto;
import com.bees360.report.controller.ImageAnnotationController;
import com.bees360.report.controller.ReportController;
import com.bees360.report.controller.ReportImageController;
import java.util.Objects;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
import jakarta.servlet.http.HttpServletRequest;

@ControllerAdvice(assignableTypes = {
    ReportController.class,
     ReportImageController.class,
    ImageAnnotationController.class})
public class ImageUrlDomainReplacerAdvice implements ResponseBodyAdvice {

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        if (Objects.isNull(body) || body instanceof ResponseProto.ResponseBody) {
            return body;
        }

        ServletServerHttpRequest servletServerHttpRequest = (ServletServerHttpRequest) request;
        HttpServletRequest httpServletRequest = servletServerHttpRequest.getServletRequest();
        String referer = httpServletRequest.getHeader("Referer");

        ResponseJson responseJson = (body instanceof ResponseJson rj ? rj : new ResponseJson(body));
        if(ObjectUtils.isEmpty(referer)) {
            return responseJson;
        }

        String jsonData = JSONObject.toJSONString(responseJson);

        return JSONObject.parseObject(jsonData, ResponseJson.class);
    }
}
