package com.bees360.report.config;

import com.bees360.report.grpc.client.GrpcReportClient;
import com.bees360.util.maps.googlemaps.GoogleMapUtilBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2018/08/08
 */
@Slf4j
@Configuration
public class ReportWebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

    }

    @Bean
    public GrpcReportClient grpcReportClient(@Value("${com.bees360.grpc.server.web.endpoints}") String endpoints) {
        log.info("The GrpcReportClient is created with {endpoints: " + endpoints + "}.");
        return new GrpcReportClient(endpoints);
    }
}
