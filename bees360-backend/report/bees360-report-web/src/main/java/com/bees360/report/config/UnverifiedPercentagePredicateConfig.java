package com.bees360.report.config;

import com.bees360.report.ReportTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashSet;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Configuration
@EnableConfigurationProperties
public class UnverifiedPercentagePredicateConfig {

    @Bean(name = {"unverifiedPercentagePredicate"})
    public Predicate<Integer> unverifiedPercentagePredicate(
            UnverifiedPercentageReportTypeProperties unverifiedPercentageReportTypeProperties) {
        var reportTypes =
                unverifiedPercentageReportTypeProperties.getReportType().stream()
                        .map(ReportTypeEnum::getCode)
                        .collect(Collectors.toList());
        return reportTypes::contains;
    }

    @Configuration
    @ConfigurationProperties(prefix = "report.summary-validation.unverified-percentage")
    @Getter
    @Setter
    static class UnverifiedPercentageReportTypeProperties {

        private Set<ReportTypeEnum> reportType = new HashSet<>();
    }
}
