package com.bees360.report.controller;

import com.bees360.common.protobuf.ProtoBeanUtils;
import com.bees360.report.config.ProtoDeleteMapping;
import com.bees360.report.config.ProtoPostMapping;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.vo.ImageAnnotationVo;
import com.bees360.report.entity.vo.ImageAnnotationVoRes;
import com.bees360.report.grpc.api.report2web.AnnotationIds;
import com.bees360.report.service.ImageAnnotationTagService;
import com.bees360.user.User;
import com.google.protobuf.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/image/tag")
public class ImageAnnotationTagController {

    @Autowired
    private ImageAnnotationTagService annotationTagService;

    /**
     * 多张图片创建单个annotation tag
     * @param user
     * @param imageTagReq
     * @throws ServiceException
     */
    @ProtoPostMapping("/annotation/batch")
    public Message batchChangeImageAnnotationTag(@AuthenticationPrincipal User user,
                                         @RequestBody com.bees360.report.grpc.api.report2web.ImageAnnotationTagCreateRequest
                                             imageTagReq) throws ServiceException, IOException {
        List<ImageAnnotationVo> annotations = annotationTagService.batchCreateImageAnnotationTag(user, imageTagReq);
        ImageAnnotationVoRes annotationVoRes = new ImageAnnotationVoRes(annotations);
        return ProtoBeanUtils.toProtoMessage(com.bees360.report.grpc.api.report2web.ImageAnnotationVoRes.newBuilder(), annotationVoRes);
    }

    @Deprecated
    @ProtoDeleteMapping("/annotation/batch")
    public void batchDeleteImageAnnotation(@AuthenticationPrincipal User user, @RequestBody @NotNull AnnotationIds annotationIds) throws ServiceException {
        var annotationIdList = annotationIds.getAnnotationIdsList().stream()
            .map(Long::parseLong).collect(Collectors.toSet());
        annotationTagService.deleteImageAnnotation(user, annotationIdList);
    }
}
