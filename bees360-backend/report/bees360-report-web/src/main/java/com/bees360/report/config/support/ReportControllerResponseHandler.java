package com.bees360.report.config.support;

import com.bees360.base.ResponseJson;
import com.bees360.commons.springsupport.restapi.ApiError;
import com.bees360.internal.ai.exchange.ai2client.ResponseProto;
import com.bees360.internal.ai.util.protobuf.AnyDataPacker;
import com.bees360.report.PackageMarker;
import com.bees360.report.core.exception.ValidErrors;
import com.bees360.util.CollectionAssitant;
import com.google.protobuf.GeneratedMessageV3;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * report模块统一返回值处理类
 *
 * <AUTHOR>
 * @date 2021/3/9
 */
@Slf4j
// TODO 此处配置成"com.bees360.report.controller"就不进此类，目前还不清楚为什么
@RestControllerAdvice(basePackageClasses = PackageMarker.class)
public class ReportControllerResponseHandler implements ResponseBodyAdvice<Object> {

    private static final String DEFAULT_SUCCESS_CODE = "0";

    /**
     * It will supports all return types.
     */
    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        log.debug("converterType: " + converterType);
        log.debug("returnType.executable: "
            + returnType.getExecutable().getClass().getName() + "#" + returnType.getExecutable().getName());
        log.debug("returnType: " + returnType.getClass());
        log.debug("MappingJackson2HttpMessageConverter is assignable from converterType: "
            + MappingJackson2HttpMessageConverter.class.isAssignableFrom(converterType));
        return true;
    }

    /**
     * package the return data into the data field of <code>ResponseJson</code>
     */
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
        Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request,
        ServerHttpResponse response) {
        log.debug("returnType: " + returnType);

        ResponseJson json;
        if (isJsonMediaType(selectedContentType)) {
            if (body instanceof ApiError) {
                ApiError apiError = (ApiError) body;
                if (Objects.nonNull(apiError.type())) {
                    response.setStatusCode(apiError.type().httpStatus());
                }
                return getApiErrorResponse(apiError);
            }
            return body;
        }

        if (body instanceof ResponseJson || body instanceof ResponseProto.ResponseBody) {
            // 目标protobuf bean格式
            return body;
        } else if (body instanceof ResponseProto.ResponseErrorBody errorBody) {
            if (isJsonMediaType(selectedContentType)) {
                return body;
            }
            return errorBodyToBody(errorBody);
        } else if (body instanceof ApiError apiError) {
            json = getApiErrorResponse(apiError);
            if (Objects.nonNull(apiError.type())) {
                response.setStatusCode(apiError.type().httpStatus());
            }
        } else if (Objects.isNull(body) || body instanceof GeneratedMessageV3) {
            // protobuf bean 转为标准 ResponseProto.ResponseBody
            // 这里为成功处理的结果
            HttpStatus httpStatus = HttpStatus.OK;

            ResponseProto.ResponseBody.Builder responseBodyBuilder = ResponseProto.ResponseBody.newBuilder()
                .setCode(DEFAULT_SUCCESS_CODE)
                .setMessage(httpStatus.getReasonPhrase());
            if (Objects.isNull(body)) {
                return responseBodyBuilder.build();
            }
            GeneratedMessageV3 result = (GeneratedMessageV3) body;
            return responseBodyBuilder.setData(AnyDataPacker.pack(result)).build();
        } else {
            json = new ResponseJson(body);
        }

        return json;
    }

    private ResponseJson getApiErrorResponse(ApiError body) {
        ResponseJson json;
        json = new ResponseJson();
        json.setCode(body.getCode());
        json.setData(getErrorMessage(body));
        if (!CollectionAssitant.isEmpty(body.getValidErrors())) {
            json.setMsg(body.getValidErrors().get(0).getMessage());
        } else {
            json.setMsg(body.getMessage());
        }
        if (StringUtils.isBlank(json.getCode())) {
            json.setCode(String.valueOf(body.type().httpStatus().value()));
        }
        return json;
    }

    private Object getErrorMessage(ApiError apiError) {
        if (CollectionAssitant.isEmpty(apiError.getValidErrors())) {
            return null;
        }
        return ValidErrors.builder().errors(apiError.getValidErrors()).build();
    }

    private ResponseProto.ResponseBody errorBodyToBody(ResponseProto.ResponseErrorBody errorBody) {
        return ResponseProto.ResponseBody.newBuilder()
            .setCode(errorBody.getCode())
            .setMessage(errorBody.getMessage())
            .setData(AnyDataPacker.pack(errorBody.getData()))
            .build();
    }

    private boolean isJsonMediaType(MediaType selectedContentType) {
        return MediaType.APPLICATION_JSON.equals(selectedContentType)
            || MediaType.APPLICATION_JSON.equals(selectedContentType);
    }

}
