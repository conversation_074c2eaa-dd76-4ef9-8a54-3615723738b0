package com.bees360.report.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;

/**
 * Spring application config配置
 *
 * <AUTHOR>
 * @date 2021/03/09
 */
@Slf4j
@Configuration
@EnableConfigurationProperties
public class SpringApplicationConfig {

    @Bean
    public ResourceBundleMessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasename("messages_en");
        return messageSource;
    }

}
