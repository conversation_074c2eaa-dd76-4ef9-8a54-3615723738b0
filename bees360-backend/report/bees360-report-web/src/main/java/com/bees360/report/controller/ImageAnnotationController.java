package com.bees360.report.controller;

import com.bees360.image.ImageTagEnum;
import com.bees360.internal.ai.exchange.ai2client.ProjectImageProto.ADDeleteParameter;
import com.bees360.internal.ai.exchange.ai2client.ProjectImageProto.ADParameter;
import com.bees360.report.config.ProtoDeleteMapping;
import com.bees360.report.config.ProtoPostMapping;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.vo.ADDeleteParameterVo;
import com.bees360.report.entity.vo.ADParameterVo;
import com.bees360.report.service.NewImageService;
import com.bees360.report.service.ProjectDamageDetectionService;
import com.bees360.user.User;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import jakarta.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/projects/{projectId:\\d+}/images")
public class ImageAnnotationController {

    @Autowired
    private ProjectDamageDetectionService projectDamageDetectionService;

    @Autowired private NewImageService imageService;

    /**
     * start AD Server
     *
     * @param projectId project id
     */
    @ProtoPostMapping("/ad")
    public void addADAnnotation(
            @AuthenticationPrincipal com.bees360.user.User user,
            @PathVariable long projectId,
            @Valid @RequestBody ADParameter aDParameter)
            throws ServiceException {
        ADParameterVo adParameterVo =
                new ADParameterVo(
                        aDParameter.getCount(),
                        aDParameter.getDamageShown(),
                        aDParameter.getLevel(),
                        true,
                        aDParameter.getFileSourceTypesList(),
                        aDParameter.getImageIdsList());
        projectDamageDetectionService.generateADAnnotations(projectId, user, adParameterVo, false);
    }

    /**
     * start AD Server
     *
     * @param projectId project id
     */
    @ProtoDeleteMapping("/ad")
    public void deleteADAnnotation(
            @AuthenticationPrincipal com.bees360.user.User user,
            @PathVariable long projectId,
            @Valid @RequestBody ADDeleteParameter aDDeleteParameter)
            throws ServiceException {
        ADDeleteParameterVo aDParameterVo =
            new ADDeleteParameterVo(
                aDDeleteParameter.getLevel(),
                List.of(ImageTagEnum.HAIL_DAMAGE.getCode()),
                null);
        projectDamageDetectionService.deleteADAnnotations(projectId, user.getId(), aDParameterVo);
    }

    /**
     * 批量删除annotation记录
     *
     * @return image对应的最新annotatedImage相关信息
     */
    @DeleteMapping("/infera/annotation/batch")
    public void delImageAnnotationByTagIds(@AuthenticationPrincipal User user,
                                                 @PathVariable long projectId,
                                                 @RequestParam(defaultValue = "") List<Integer> tagIds) {
        imageService.delImageAnnotationByTagIds(user.getId(), projectId, tagIds);
    }
}
