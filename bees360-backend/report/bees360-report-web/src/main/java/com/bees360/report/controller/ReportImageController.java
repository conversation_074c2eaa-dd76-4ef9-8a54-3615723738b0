package com.bees360.report.controller;

import com.bees360.base.ResponseJson;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.service.ReviewImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bees360.entity.enums.ReportTypeEnum;

@RestController
@RequestMapping("/projects/{projectId:\\d+}/images")
// TODO shoushan.zhao 因为review界面需要image信息但是现在还没有image模块，
//  为了保持系统完整性，加了这些代码，当有image模块的时候，这些需要迁到image模块内。
public class ReportImageController {

    @Autowired private ReviewImageService reviewImageService;

	/**
	 * get report images
	 * @param projectId
	 * @return
	 */
	@GetMapping("/report-images")
	public ResponseJson getReviewImages(@PathVariable long projectId, int reportType) throws ServiceException {
		ResponseJson result = new ResponseJson();
		result.setData(reviewImageService.getReviewImages(projectId, ReportTypeEnum.getEnum(reportType)));
		return result;
	}
}
