package com.bees360.report.controller;

import com.bees360.base.ResponseJson;
import com.bees360.entity.dto.ProjectReportFileDto;
import com.bees360.entity.dto.UrlDto;
import com.bees360.entity.vo.ReportTypeKeyVo;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.consts.AiBotUser;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.vo.ProjectReportFileTinyVo;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.report.core.exception.AssertUtil;
import com.bees360.report.core.exception.MessageCode;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.service.ProjectReportFileService;
import com.bees360.report.service.ProjectReportService;
import com.bees360.report.service.PdfReportGenerationService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

@Log4j2
@Validated
@RestController
@RequestMapping("/projects/{projectId:\\d+}/reports")
public class ReportController {

    @Autowired private ProjectReportService projectReportService;

    @Autowired private ProjectReportFileService projectReportFileService;

    @Autowired private PdfReportGenerationService pdfReportGeneratorService;

    @Autowired private ProjectEsService projectEsService;

    @Autowired private MessageSource messageSource;

    @PutMapping(value = "/{reportId}/status/submitted")
    public ResponseJson requestReview(@AuthenticationPrincipal com.bees360.user.User user,
                                      @PathVariable long projectId, @PathVariable String reportId) throws ServiceException {
        projectReportFileService.submitReport(projectId, AiBotUser.AI_ID, reportId, user.getId());
        return new ResponseJson();
    }

    @PutMapping("/{reportId}/status/approved")
    public ResponseJson approveReports(@AuthenticationPrincipal com.bees360.user.User user,
                                       @PathVariable long projectId, @PathVariable String reportId) throws ServiceException {
        ResponseJson json = new ResponseJson();
        ProjectEsModel project = projectEsService.findProjectByProjectId(projectId);

        int status = project.getProjectStatus();
        if (status == AiProjectStatusEnum.PROJECT_CANCELED.getCode() || status == AiProjectStatusEnum.PROJECT_DELETED.getCode()) {
            String msgCode = MessageCode.REPORT_CANNOT_APPROVE;
            json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
        } else {
            projectReportFileService.approveReport(projectId, AiBotUser.AI_ID, reportId, user.getId());
        }
        return json;
    }

    /**
     * 提供报告手动修改状态接口
     *
     * @param user
     * @param projectId
     * @param formMap
     * @return
     * @throws ServiceException
     */
    @PutMapping(value = "/status/updated")
    public ResponseJson updateReportStatus(@AuthenticationPrincipal com.bees360.user.User user,
                                           @PathVariable long projectId,
                                           @RequestBody Map<String, Integer> formMap) throws ServiceException {
        Integer status = formMap.get("status");
        Integer reportType = formMap.get("reportType");
        AssertUtil.notNull(status, "param status is null");
        projectReportFileService.updateReportStatus(projectId, user, reportType, status);
        return new ResponseJson();
    }

    /**
     * get report files
     *
     * @param projectId project id
     * @param type      report type
     * @return report message
     */
    @GetMapping(value = "")
    public ResponseJson getReportFiles(@PathVariable long projectId, Integer type) {
        ResponseJson json = new ResponseJson();
        List<ProjectReportFileTinyVo> reportFiles = projectReportFileService.getReportFiles(projectId, type);
        json.setData(reportFiles);
        return json;
    }

    /**
     * update report file url
     *
     * @param projectId project id
     * @param reportId  report id
     * @param urlDto    url parameter
     * @return is success
     */
    @PatchMapping(value = "/{reportId}/report")
    public ResponseJson updateReportUrl(@PathVariable long projectId,
                                        @PathVariable String reportId, @RequestBody UrlDto urlDto) {
        ResponseJson json = new ResponseJson();
        int update = projectReportService.updateReportUrl(projectId, reportId, urlDto.getUrl());
        json.setData(update);
        return json;
    }

    @DeleteMapping("/{reportId}")
    public ResponseJson deleteReport(@PathVariable long projectId, @PathVariable String reportId) {
        projectReportFileService.deleteReport(projectId, reportId);
        return new ResponseJson();
    }

    @PostMapping("")
    public ResponseJson createReportFile(@AuthenticationPrincipal com.bees360.user.User user, @PathVariable long projectId,
                                         @RequestBody @Valid ProjectReportFileDto projectReportFileDto) throws Exception {
        return new ResponseJson(projectReportFileService.createReportFile(AiBotUser.AI_ID, projectId, projectReportFileDto, user.getId()));
    }

    @PostMapping("/editor-report")
    public ResponseJson saveOrUpdateEditorReport(@AuthenticationPrincipal com.bees360.user.User user,
                @PathVariable long projectId, @Valid @RequestBody ReportTypeKeyVo reportTypeKey) throws ServiceException {
        ResponseJson json = new ResponseJson();
        Boolean replace = reportTypeKey.getReplace();
        if (replace == null || Boolean.FALSE.equals(replace)) {
            boolean isExecuted = pdfReportGeneratorService.generateEditorReport(projectId, user, reportTypeKey, false);
            if (!isExecuted) {
                json.setCode("429");
                json.setMsg("There's a report generation task being process.");
            }
            return json;
        } else {
            pdfReportGeneratorService.generateEditorReport(projectId, user, reportTypeKey, true);
            return json;
        }
    }

    /**
     * @param projectId
     * @throws ServiceException
     */
    @PutMapping("/report-data")
    public ResponseJson transferDataToWeb(@AuthenticationPrincipal com.bees360.user.User user,
                                          @PathVariable long projectId) throws ServiceException {
        log.info("{} ({}) request transfering data of project {} to web.", user.getName(), user.getId(), projectId);
        projectReportService.transferDataToWeb(projectId);
        return new ResponseJson();
    }
}
