package com.bees360.report.config.support;

import com.bees360.internal.ai.exchange.ai2client.ResponseProto;
import com.bees360.report.PackageMarker;
import com.bees360.report.core.exception.ProtoServiceMessageException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Proto请求统一异常处理类
 *
 * <AUTHOR>
 * @date 2021/3/9
 */
@Slf4j
@RestControllerAdvice(basePackageClasses = PackageMarker.class)
@ResponseBody
public class ReportProtoExceptionHandler {

    @ExceptionHandler(ProtoServiceMessageException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseProto.ResponseErrorBody serviceMessageException(ProtoServiceMessageException ex) {
        return ResponseProto.ResponseErrorBody.newBuilder()
            .setMessage(ex.getMessage())
            .build();
    }

}
