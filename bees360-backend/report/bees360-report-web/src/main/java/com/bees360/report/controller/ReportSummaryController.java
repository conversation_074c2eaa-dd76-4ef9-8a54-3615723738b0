package com.bees360.report.controller;

import com.bees360.base.ResponseJson;
import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.dto.ReportSummaryDto;
import com.bees360.report.entity.vo.EditorDataVo;
import com.bees360.report.service.ProjectReportService;
import com.bees360.report.service.util.summary.ReportSummaryConverter;
import com.bees360.report.service.util.summary.SummaryValidator;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * Request fields for summary are limited by {@link ReportSummaryDto}.
 */
@Log4j2
@RestController
@RequestMapping("/projects/{projectId:\\d+}/reports")
@ConditionalOnProperty(
    prefix = "report-web.controller.free-summary-controller",
    name = "enabled",
    havingValue = "false",
    matchIfMissing = true)
@Deprecated
public class ReportSummaryController {

    private final ProjectReportService projectReportService;
    private final ProjectEsService projectEsService;
    private final SummaryValidator fullSummaryValidator;
    private final ReportSummaryConverter reportSummaryConverter;

    public ReportSummaryController(
            ProjectReportService projectReportService,
            ProjectEsService projectEsService,
            SummaryValidator fullSummaryValidator,
            ReportSummaryConverter reportSummaryConverter) {
        this.projectReportService = projectReportService;
        this.projectEsService = projectEsService;
        this.fullSummaryValidator = fullSummaryValidator;
        this.reportSummaryConverter = reportSummaryConverter;
        log.info("Created {}.", this);
    }

    @PostMapping("/summary")
    public ResponseJson upsertReportSummary(@AuthenticationPrincipal com.bees360.user.User user, @PathVariable long projectId,
                                            @Valid @RequestBody ReportSummaryDto reportSummary) throws ServiceException {
        String summaryJson = reportSummaryConverter.toSummaryJson(reportSummary.getReportSummary());
        projectReportService.upsertReportSummary(projectId, user.getId(), reportSummary, summaryJson);
        return new ResponseJson();
    }

    @PostMapping("/summary/validation")
    public ResponseJson validateReportSummary(@PathVariable long projectId,
                                              @Valid @RequestBody ReportSummaryDto reportSummary) throws ServiceException {
        var project = projectEsService.findProjectByProjectId(projectId);
        ProjectServiceTypeEnum serviceType = ProjectServiceTypeEnum.getEnum(project.getServiceType());
        fullSummaryValidator.validate(projectId, serviceType, reportSummary.getReportType(), reportSummary.getReportSummary());
        return new ResponseJson();
    }

    @PostMapping("/editor")
    public ResponseJson saveOrUpdateReportEditorData(@AuthenticationPrincipal com.bees360.user.User user,
            @PathVariable long projectId, @Valid @RequestBody EditorDataVo editorData) throws ServiceException {
        var summaryJson = reportSummaryConverter.toSummaryJson(editorData.getReportSummary());
        projectReportService.saveOrUpdateReportEditorData(projectId, user.getId(), editorData, summaryJson);
        return new ResponseJson();
    }
}
