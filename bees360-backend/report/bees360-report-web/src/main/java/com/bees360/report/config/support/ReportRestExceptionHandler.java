package com.bees360.report.config.support;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.commons.springsupport.restapi.ApiError;
import com.bees360.commons.springsupport.restapi.support.ControllerExceptionHandler;
import com.bees360.report.PackageMarker;
import com.bees360.report.core.exception.MessageCode;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.core.exception.ServiceMessageException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Rest请求统一异常处理类
 *
 * <AUTHOR>
 * @date 2021/3/9
 */
@Slf4j
@RestControllerAdvice(basePackageClasses = PackageMarker.class)
@ResponseBody
public class ReportRestExceptionHandler extends ControllerExceptionHandler {

    @Autowired
    private MessageSource messageSource;

    private String getMessageFromCode(String code) {
        return MessageCode.getMessage(messageSource, code);
    }

    @ExceptionHandler({ServiceException.class})
    public ApiError reportServiceException(HttpServletRequest request, ServiceException e) {
        if (StringUtils
            .equalsAnyIgnoreCase(e.getCode(), MessageCode.SYSTEM_EXCEPTION, MessageCode.DATABASE_EXCEPTION)) {
            return internalServiceError(request, e);
        }
        log.info(e.getMessage(), e);
        String msg = getMessageFromCode(e.getCode());
        return ApiError.failedPrecondition(e.getCode(), msg);
    }

    @ExceptionHandler({ServiceMessageException.class})
    public ApiError serviceMessageException(ServiceMessageException e) {
        log.debug(e.getMessage(), e);
        return ApiError.failedPrecondition(e.getCode(), e.getMessage());
    }

    @ExceptionHandler({IllegalStateException.class})
    public ApiError serviceMessageException(IllegalStateException e) {
        log.debug(e.getMessage(), e);
        return ApiError.failedPrecondition(MessageCode.SYSTEM_EXCEPTION, e.getMessage());
    }

    @ExceptionHandler({IllegalArgumentException.class})
    public ApiError serviceMessageException(IllegalArgumentException e) {
        log.debug(e.getMessage(), e);
        return ApiError.failedPrecondition(MessageCode.VALIDATION_FAILED, e.getMessage());
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ApiError handleResourceNotFoundException(ResourceNotFoundException ex) {
        log.debug(ex.getMessage(), ex);
        return ApiError.notFound(ex.getMessage());
    }

}
