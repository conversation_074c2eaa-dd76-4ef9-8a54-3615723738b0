package com.bees360.report;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.autoconfig.EventAutoConfig;
import com.bees360.job.autoconfig.JobExecutorAutoConfig;
import com.bees360.report.controller.FreeReportSummaryController;
import com.bees360.report.controller.ReportSummaryController;
import com.bees360.report.core.exception.ServiceException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = {
        AbstractReportSummaryControllerTest.Config.class
    },
    properties = {
        "report-web.controller.free-summary-controller.enabled=true",
        "grpc.server.port=0"
    }
)
@ApplicationAutoConfig(
    exclude = {
        DataSourceAutoConfiguration.class,
        EventAutoConfig.class,
        JobExecutorAutoConfig.class,
    }
)
class FreeReportSummaryControllerTest extends AbstractReportSummaryControllerTest {

    @Autowired
    ApplicationContext applicationContext;

    @Test
    void testValidBean() {
        assertNotNull(applicationContext.getBean(FreeReportSummaryController.class));
        try {
            applicationContext.getBean(ReportSummaryController.class);
            fail();
        } catch (NoSuchBeanDefinitionException e) {}
    }

    @Test
    void testSaveSummary() throws ServiceException {
        testSaveSummary("{\"yearBuilt\":1962,\"a_field_name_never_defined_in_summary_pojo\":{\"fieldName\":123}}");
    }

    @Test
    void testSaveEditorSuccessfully() {
        testSaveEditorSuccessfully("{\"yearBuilt\":1962,\"a_field_name_never_defined_in_summary_pojo\":{\"fieldName\":123}}");
    }

    @Test
    void testSaveSummaryValidWithIncompletePercentageShouldSuccess() {
        super.testSaveSummaryValidWithIncompletePercentageShouldSuccess();
    }
}
