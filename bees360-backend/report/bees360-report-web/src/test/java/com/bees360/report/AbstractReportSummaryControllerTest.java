package com.bees360.report;

import com.bees360.base.ResponseJson;
import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.report.config.support.ReportControllerResponseHandler;
import com.bees360.report.config.support.ReportProtoExceptionHandler;
import com.bees360.report.config.support.ReportRestExceptionHandler;
import com.bees360.report.controller.FreeReportSummaryController;
import com.bees360.report.controller.ReportSummaryController;
import com.bees360.report.entity.dto.ReportSummaryDto;
import com.bees360.report.entity.vo.EditorDataVo;
import com.bees360.report.service.ProjectReportService;
import com.bees360.report.service.util.summary.ReportSummaryConverter;
import com.bees360.report.service.util.summary.SummaryValidator;
import com.bees360.user.Message;
import com.bees360.user.User;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;
import java.util.function.Predicate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = {
        AbstractReportSummaryControllerTest.Config.class
    }
)
@ApplicationAutoConfig(
    exclude = {
        DataSourceAutoConfiguration.class
    }
)
abstract class AbstractReportSummaryControllerTest {

    private static final ReportTypeEnum UNVERIFIED_PERCENTAGE_PREDICATE_REPORT_TYPE = ReportTypeEnum.SNAP;

    @Import({
        FreeReportSummaryController.class,
        ReportSummaryController.class,
        ReportRestExceptionHandler.class,
        ReportProtoExceptionHandler.class,
        ReportControllerResponseHandler.class,
        Config.WebMvcConfig.class,
    })
    static class Config {

        @Configuration
        public static class WebMvcConfig implements WebMvcConfigurer {

            @Override
            public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
                // Parse the parameters of @CurUserId annotation
                argumentResolvers.add(new HandlerMethodArgumentResolver() {

                    @Override
                    public boolean supportsParameter(MethodParameter parameter) {
                        return parameter.getParameterType().isAssignableFrom(User.class);
                    }

                    @Override
                    public Object resolveArgument(
                        MethodParameter p,
                        ModelAndViewContainer m,
                        NativeWebRequest r,
                        WebDataBinderFactory f) throws Exception {
                        return User.from(Message.UserMessage.newBuilder().setId("1").build());
                    }
                });
            }
        }

        @Bean
        ProjectEsService projectEsService() {
            var esService = Mockito.mock(ProjectEsService.class);
            var result = new ProjectEsModel();
            result.setServiceType(ProjectServiceTypeEnum.ROOF_ONLY_UNDERWRITING.getCode());
            when(esService.findProjectByProjectId(anyLong())).thenReturn(result);
            return esService;
        }

        @Bean
        public ReportSummaryConverter reportSummaryConverter(ObjectMapper objectMapper) {
            return new ReportSummaryConverter(objectMapper);
        }

        @Bean
        Bees360FeatureSwitch bees360FeatureSwitch() {
            return new Bees360FeatureSwitch();
        }

        @Bean
        Predicate<Integer> unverifiedPercentagePredicate() {
            return reportType -> reportType == UNVERIFIED_PERCENTAGE_PREDICATE_REPORT_TYPE.getCode();
        }
    }

    @MockitoBean
    ProjectReportService projectReportService;

    @MockitoBean
    SummaryValidator fullSummaryValidator;

    @Autowired
    private TestRestTemplate testRestTemplate;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    @SneakyThrows
    void testSaveSummary(String expectedSummaryJson) {
        // a_field_name_never_defined_in_summary_pojo is not any field name of ReportSummaryVo
        var summaryJson = "{\"reportType\":18,\"reportSummary\":{\"yearBuilt\":1962, \"a_field_name_never_defined_in_summary_pojo\": {\"fieldName\": 123}}}";
        var result = testRestTemplate.postForEntity("/projects/1/reports/summary", createJsonEntity(summaryJson), ResponseJson.class);
        assertEquals(HttpStatus.OK, result.getStatusCode());

        ArgumentCaptor<String> summaryJsonCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<ReportSummaryDto> summaryVoCaptor = ArgumentCaptor.forClass(ReportSummaryDto.class);
        verify(projectReportService).upsertReportSummary(anyLong(), nullable(String.class), summaryVoCaptor.capture(), summaryJsonCaptor.capture());
        var summary = summaryVoCaptor.getValue();
        var summaryJsonCapture = summaryJsonCaptor.getValue();
        assertEquals(1962, summary.getReportSummary().getYearBuilt());
        assertEquals(expectedSummaryJson, summaryJsonCapture);

        result = testRestTemplate.postForEntity("/projects/1/reports/summary/validation", createJsonEntity(summaryJson), ResponseJson.class);
        assertEquals(HttpStatus.OK, result.getStatusCode());
    }

    @Test
    void testInvalidJsonRequest() {
        var summaryJson = "{\"reportType\":18,\"reportSummary\":{\"yearBuilt\":1962";
        var result = testRestTemplate.postForEntity("/projects/1/reports/summary", createJsonEntity(summaryJson), ResponseJson.class);
        assertEquals(HttpStatus.BAD_REQUEST, result.getStatusCode());
    }

    @Test
    void testSaveSummaryValidWithJavaxValidation() {
        var summaryJson = "{\"reportType\":18,\"reportSummary\":{\"hazards\":[null, \"Damage\"]}}";
        var result = testRestTemplate.postForEntity("/projects/1/reports/summary", createJsonEntity(summaryJson), ResponseJson.class);
        assertEquals(HttpStatus.UNPROCESSABLE_ENTITY, result.getStatusCode());
        assertEquals("must not be null", result.getBody().getMsg());

        result = testRestTemplate.postForEntity("/projects/1/reports/summary/validation", createJsonEntity(summaryJson), ResponseJson.class);
        assertEquals(HttpStatus.UNPROCESSABLE_ENTITY, result.getStatusCode());
        assertEquals("must not be null", result.getBody().getMsg());
    }

    @Test
    void testSaveSummaryValidWithExteriorSidingCustomizedAnnotation() {
        var summaryJson = "{\"reportType\":18,\"reportSummary\":{\"exterior\":{\"siding\":{\"a\":20}}}}";
        var result = testRestTemplate.postForEntity("/projects/1/reports/summary", createJsonEntity(summaryJson), ResponseJson.class);
        assertEquals(HttpStatus.UNPROCESSABLE_ENTITY, result.getStatusCode());
        assertEquals("the sum should be 100.", result.getBody().getMsg());

        result = testRestTemplate.postForEntity("/projects/1/reports/summary/validation", createJsonEntity(summaryJson), ResponseJson.class);
        assertEquals(HttpStatus.UNPROCESSABLE_ENTITY, result.getStatusCode());
        assertEquals("the sum should be 100.", result.getBody().getMsg());
    }

    void testSaveSummaryValidWithIncompletePercentageShouldSuccess() {
        var summaryJson =
                "{\n"
                        + "  \"reportType\": " + UNVERIFIED_PERCENTAGE_PREDICATE_REPORT_TYPE.getCode() + ",\n"
                        + "  \"reportSummary\": {\n"
                        + "    \"yearBuilt\": 1962,\n"
                        + "    \"a_field_name_never_defined_in_summary_pojo\": {\n"
                        + "      \"fieldName\": 123\n"
                        + "    },\n"
                        + "    \"roof\": {\n"
                        + "      \"overallCondition\": \"Good\",\n"
                        + "      \"material\": {\n"
                        + "        \"CompositeShingles\": 10,\n"
                        + "        \"BuildupRoofNoGravel\": 0\n"
                        + "      },\n"
                        + "      \"hasCurlingShingles\": false,\n"
                        + "      \"coveringMaterial\": [\n"
                        + "        \"Asphalt\"\n"
                        + "      ],\n"
                        + "      \"hasGranularLoss\": false,\n"
                        + "      \"geometry\": {\n"
                        + "        \"HipValley\": 10,\n"
                        + "        \"Flat\": 0\n"
                        + "      },\n"
                        + "      \"hasMissingDamagedShingles\": false,\n"
                        + "      \"estAge\": \"10 ~ 15 years\",\n"
                        + "      \"hasPatchedAreas\": false,\n"
                        + "      \"hasTarp\": false,\n"
                        + "      \"hasSolarPanel\": false\n"
                        + "    }\n"
                        + "  }\n"
                        + "}";
        var result = testRestTemplate.postForEntity("/projects/1/reports/summary", createJsonEntity(summaryJson), ResponseJson.class);
        assertEquals(HttpStatus.OK, result.getStatusCode());

        result = testRestTemplate.postForEntity("/projects/1/reports/summary/validation", createJsonEntity(summaryJson), ResponseJson.class);
        assertEquals(HttpStatus.OK, result.getStatusCode());
    }

    @SneakyThrows
    void testSaveEditorSuccessfully(String expectedSummaryJson) {
        var summaryJson = "{\"report\":{\"reportType\":18},\"images\":[{\"componentId\":1}],\"reportSummary\":{\"yearBuilt\":1962,\"a_field_name_never_defined_in_summary_pojo\":{\"fieldName\":123}}}";

        var result = testRestTemplate.postForEntity("/projects/101/reports/editor", createJsonEntity(summaryJson), ResponseJson.class);
        System.out.println(result.getBody());
        assertEquals(HttpStatus.OK, result.getStatusCode());

        ArgumentCaptor<EditorDataVo> editorDataCaptor = ArgumentCaptor.forClass(EditorDataVo.class);
        ArgumentCaptor<String> summaryJsonCaptor = ArgumentCaptor.forClass(String.class);
        verify(projectReportService).saveOrUpdateReportEditorData(eq(101L), nullable(String.class), editorDataCaptor.capture(), summaryJsonCaptor.capture());
        var editorData = editorDataCaptor.getValue();
        assertEquals(18, editorData.getReport().getReportType());
        assertEquals(1, editorData.getImages().get(0).getComponentId());
        assertEquals(1962, editorData.getReportSummary().getYearBuilt());

        var summaryJsonCapture = summaryJsonCaptor.getValue();
        assertEquals(expectedSummaryJson, summaryJsonCapture);
    }

    @Test
    void testSaveEditorValidationFails() {
        var summaryJson = "{\"report\":{},\"reportSummary\":{\"exterior\":{\"siding\":{\"a\":20}}}}";
        var result = testRestTemplate.postForEntity("/projects/1/reports/editor", createJsonEntity(summaryJson), ResponseJson.class);
        assertEquals(HttpStatus.UNPROCESSABLE_ENTITY, result.getStatusCode());
        assertEquals("the sum should be 100.", result.getBody().getMsg());
    }

    private HttpEntity<String> createJsonEntity(String entity) {
        var headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return new HttpEntity<>(entity, headers);
    }
}
