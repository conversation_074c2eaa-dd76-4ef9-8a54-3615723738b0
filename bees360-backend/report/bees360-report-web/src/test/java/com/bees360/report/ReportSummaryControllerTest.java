package com.bees360.report;

import com.bees360.boot.ApplicationAutoConfig;
import com.bees360.event.autoconfig.EventAutoConfig;
import com.bees360.job.autoconfig.JobExecutorAutoConfig;
import com.bees360.report.controller.FreeReportSummaryController;
import com.bees360.report.controller.ReportSummaryController;
import com.bees360.report.core.exception.ServiceException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = {
        AbstractReportSummaryControllerTest.Config.class,
    },
    properties = "grpc.server.port=0"
)
@ApplicationAutoConfig(
    exclude = {
        DataSourceAutoConfiguration.class,
        EventAutoConfig.class,
        JobExecutorAutoConfig.class,
    }
)
class ReportSummaryControllerTest extends AbstractReportSummaryControllerTest {

    @Autowired
    ApplicationContext applicationContext;

    @Test
    void testValidBean() {
        assertNotNull(applicationContext.getBean(ReportSummaryController.class));
        try {
            applicationContext.getBean(FreeReportSummaryController.class);
            fail();
        } catch (NoSuchBeanDefinitionException e) {}
    }

    @Test
    void testSaveSummary() throws ServiceException {
        testSaveSummary("{\"yearBuilt\":1962,\"livingArea\":null,\"lotSize\":null,\"risk\":null,\"bldg\":null,\"roof\":null,\"exterior\":null,\"interior\":null,\"fireProtection\":null,\"community\":null,\"addlStructures\":[],\"addlStructureDetails\":[],\"hazards\":[],\"recommendations\":[],\"factors\":[],\"history\":[],\"unprotectedSupplement\":null,\"images\":null,\"project\":null}");
    }

    @Test
    void testSaveEditorSuccessfully() {
        testSaveEditorSuccessfully("{\"yearBuilt\":1962,\"livingArea\":null,\"lotSize\":null,\"risk\":null,\"bldg\":null,\"roof\":null,\"exterior\":null,\"interior\":null,\"fireProtection\":null,\"community\":null,\"addlStructures\":[],\"addlStructureDetails\":[],\"hazards\":[],\"recommendations\":[],\"factors\":[],\"history\":[],\"unprotectedSupplement\":null,\"images\":null,\"project\":null}");
    }
}
