package com.bees360.report.service.config;

import com.bees360.entity.User;
import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.autoconfig.AutoRegisterEventListenerConfig;
import com.bees360.event.registry.ReportStatusChanged;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.report.ReportManager;
import com.bees360.report.ReportTypeEnum;
import com.bees360.report.service.ProjectReportFileService;
import com.google.common.util.concurrent.MoreExecutors;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@SpringBootTest(
		classes = {
				ReportApprovedPostProcessingOnReportStatusChangedConfigTest.Config.class,
				AutoRegisterEventListenerConfig.class,
		},
		properties = "spring.config.location = classpath:application.yml")
class ReportApprovedPostProcessingOnReportStatusChangedConfigTest {

	@Import({
			ReportApprovedPostProcessingOnReportStatusChangedConfig.class,
	})
	@Configuration
	static class Config {

		@Bean
		InMemoryEventPublisher inMemoryEventPublisher() {
			return new InMemoryEventPublisher(MoreExecutors.directExecutor());
		}
	}

	@MockBean
	ReportManager reportManager;

	@MockBean
	ProjectReportManager projectReportManager;

	@MockBean
	ProjectReportFileService projectReportFileService;

	@Autowired
	EventPublisher eventPublisher;

	@BeforeEach
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		Mockito.reset(projectReportFileService);
	}


	@Test
	void testSnapAutoReportApprovedPostProcessing() throws Exception {
		var reportType = ReportTypeEnum.SNAP;
		var userId = String.valueOf(User.AI_ID);
		var report = randomReport(reportType, Message.ReportMessage.Status.APPROVED, userId);
		var reportId = report.getId();
		doReturn(report).when(reportManager).findById(reportId);

		var projectId = getRandomId();
		doReturn(List.of(projectId)).when(projectReportManager).findProjectId(reportId);

		var event = new ReportStatusChanged();
		event.setId(reportId);
		eventPublisher.publish(event);

		verify(projectReportFileService, times(1))
				.publishReportApprovedEvent(Long.parseLong(projectId), userId, com.bees360.entity.enums.ReportTypeEnum.SNAP);
	}


	@Test
	void testSNAPReportApprovedWithNotAiUser() throws Exception {
		var reportType = ReportTypeEnum.SNAP;
		var userId = getRandomId();
		var report = randomReport(reportType, Message.ReportMessage.Status.APPROVED, userId);
		var reportId = report.getId();
		doReturn(report).when(reportManager).findById(reportId);

		var projectId = getRandomId();
		doReturn(List.of(projectId)).when(projectReportManager).findProjectId(reportId);

		var event = new ReportStatusChanged();
		event.setId(reportId);
		eventPublisher.publish(event);

		verify(projectReportFileService, never())
				.publishReportApprovedEvent(anyLong(), anyString(), any());
	}


	@Test
	void testSNAPReportWithNotApprovedStatus() throws Exception {
		var reportType = ReportTypeEnum.SNAP;
		var userId = String.valueOf(User.AI_ID);
		var report = randomReport(reportType, Message.ReportMessage.Status.SUBMITTED, userId);
		var reportId = report.getId();
		doReturn(report).when(reportManager).findById(reportId);

		var projectId = getRandomId();
		doReturn(List.of(projectId)).when(projectReportManager).findProjectId(reportId);

		var event = new ReportStatusChanged();
		event.setId(reportId);
		eventPublisher.publish(event);

		verify(projectReportFileService, never())
				.publishReportApprovedEvent(anyLong(), anyString(), any());
	}


	@Test
	void testFURReportApproved() throws Exception {
		var reportType = ReportTypeEnum.FUR;
		var userId = String.valueOf(User.AI_ID);
		var report = randomReport(reportType, Message.ReportMessage.Status.APPROVED, userId);
		var reportId = report.getId();
		doReturn(report).when(reportManager).findById(reportId);

		var projectId = getRandomId();
		doReturn(List.of(projectId)).when(projectReportManager).findProjectId(reportId);

		var event = new ReportStatusChanged();
		event.setId(reportId);
		eventPublisher.publish(event);

		verify(projectReportFileService, never())
				.publishReportApprovedEvent(anyLong(), anyString(), any());
	}

	private Report randomReport(ReportTypeEnum reportType, Message.ReportMessage.Status status, String userId) {
		var report = mock(Report.class);
		var reportId = getRandomId();
		doReturn(reportId).when(report).getId();
		doReturn(status).when(report).getStatus();
		doReturn(reportType.getKey()).when(report).getType();
		doReturn(userId).when(report).getCreatedBy();
		return report;
	}

	public static String getRandomId() {
		return RandomStringUtils.randomNumeric(6);
	}

}
