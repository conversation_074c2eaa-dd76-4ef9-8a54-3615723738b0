package com.bees360.report.service.listener;

import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.report.Report;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.service.ProjectReportService;
import com.bees360.report.ReportProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SyncDataOnReportGroupAddedTest {

    @Mock private ProjectEsService projectEsService;

    @Mock private ReportProvider reportProvider;

    @Mock private ProjectReportService projectReportService;

    @InjectMocks private SyncDataOnReportGroupAdded listener;

    private final String reportId = "report-123";
    private final String projectId = "789";
    private ReportGroupAdded validEvent;
    private Report mockReport;

    @BeforeEach
    void setUp() {
        validEvent = new ReportGroupAdded();
        validEvent.setId("testReportGroupId");
        validEvent.setReportId(reportId);
        validEvent.setGroupType(DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE);
        validEvent.setGroupKey(projectId);
        validEvent.setCreatedBy("123");
        mockReport = mock(Report.class);
    }

    @Test
    void handleWhenInvalidGroupTypeShouldSkip() throws IOException {
        ReportGroupAdded invalidEvent = new ReportGroupAdded();
        invalidEvent.setId("testReportGroupId");
        invalidEvent.setReportId(reportId);
        invalidEvent.setGroupType("INVALID_TYPE");
        invalidEvent.setGroupKey(projectId);
        invalidEvent.setCreatedBy("123");

        listener.handle(invalidEvent);

        verifyNoInteractions(reportProvider, projectEsService, projectReportService);
    }

    @Test
    void handleWhenReportTypeNotNeedSyncShouldSkip() throws IOException {
        when(reportProvider.get(reportId)).thenReturn(mockReport);

        when(mockReport.getType()).thenReturn(com.bees360.report.ReportTypeEnum.FUR.getKey());

        listener.handle(validEvent);

        verify(reportProvider).get(reportId);
        verifyNoInteractions(projectEsService, projectReportService);
    }

    @Test
    void handleWhenProjectStatusNotCompleteShouldSkip() throws IOException {
        when(reportProvider.get(reportId)).thenReturn(mockReport);

        when(mockReport.getType()).thenReturn(com.bees360.report.ReportTypeEnum.HIS.getKey());

        ProjectEsModel esProject = new ProjectEsModel();
        esProject.setProjectStatus(AiProjectStatusEnum.SITE_INSPECTED.getCode());
        when(projectEsService.findProjectByProjectId(Long.parseLong(projectId)))
                .thenReturn(esProject);

        listener.handle(validEvent);

        verify(reportProvider).get(reportId);
        verify(projectEsService).findProjectByProjectId(Long.parseLong(projectId));
        verifyNoInteractions(projectReportService);
    }

    @Test
    void handleWhenConditionsMetShouldSyncData() throws IOException, ServiceException {
        when(reportProvider.get(reportId)).thenReturn(mockReport);

        when(mockReport.getType()).thenReturn(com.bees360.report.ReportTypeEnum.HIS.getKey());

        ProjectEsModel esProject = new ProjectEsModel();
        esProject.setProjectStatus(AiProjectStatusEnum.RETURNED_TO_CLIENT.getCode());
        when(projectEsService.findProjectByProjectId(Long.parseLong(projectId)))
                .thenReturn(esProject);

        listener.handle(validEvent);

        verify(projectReportService).transferDataToWeb(Long.parseLong(projectId));
    }

    @Test
    void handleWhenSyncThrowsServiceExceptionShouldNotPropagate()
            throws IOException, ServiceException {
        when(reportProvider.get(reportId)).thenReturn(mockReport);

        when(mockReport.getType()).thenReturn(com.bees360.report.ReportTypeEnum.HIS.getKey());

        ProjectEsModel esProject = new ProjectEsModel();
        esProject.setProjectStatus(AiProjectStatusEnum.RETURNED_TO_CLIENT.getCode());
        when(projectEsService.findProjectByProjectId(Long.parseLong(projectId)))
                .thenReturn(esProject);

        doThrow(new ServiceException("Sync error"))
                .when(projectReportService)
                .transferDataToWeb(Long.parseLong(projectId));

        listener.handle(validEvent);

        verify(projectReportService).transferDataToWeb(Long.parseLong(projectId));
    }
}
