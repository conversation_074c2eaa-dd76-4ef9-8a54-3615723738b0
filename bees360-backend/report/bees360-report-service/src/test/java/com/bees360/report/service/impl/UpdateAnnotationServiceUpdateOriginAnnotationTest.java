package com.bees360.report.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import com.bees360.image.ImageTagManager;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.dto.UpdateOriginAnnotationDto;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.ImageAnnotationTagService;
import com.bees360.report.service.NewImageService;
import com.bees360.report.service.UpdateAnnotationService;
import com.bees360.report.service.config.ServiceBeanConfig;
import com.bees360.report.service.util.image.AnnotationConvertUtil;
import com.bees360.util.SecureTokens;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import jakarta.annotation.Nonnull;

@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig
@DirtiesContext
@SpringBootTest(classes = UpdateAnnotationServiceUpdateOriginAnnotationTest.Config.class)
class UpdateAnnotationServiceUpdateOriginAnnotationTest {

    @Autowired private UpdateAnnotationService updateAnnotationService;

    @MockBean private ImageAnnotationTagService imageAnnotationTagService;
    @MockBean private ExternalInterface externalInterface;
    @MockBean private ImageTagManager imageTagManager;

    @MockBean NewImageService newImageService;
    @MockBean Bees360FeatureSwitch bees360FeatureSwitch;

    @Configuration
    @Import({
        ServiceBeanConfig.class,
    })
    static class Config {}

    /** save annotations twice */
    @Test
    void updateInferaCloseUpAnnotation() throws ServiceException {
        var projectId = new Random().nextLong();
        var imageId1 = SecureTokens.generateRandomBase64Token();
        var imageId2 = SecureTokens.generateRandomBase64Token();
        var tagId1 = "526";
        var tagId2 = "1514";
        var annotationInfo1 = mockAnnotationInfo(imageId1, tagId1);
        var annotationInfo2 = mockAnnotationInfo(imageId2, tagId2);
        // test save annotation
        Mockito.when(
                        imageAnnotationTagService.listByProjectIdAndAnnotationTypeAndSourceType(
                                Mockito.anyLong(), Mockito.anyList(), Mockito.anyList()))
                .thenReturn(List.of());
        updateAnnotationService.updateOriginAnnotation(
                UpdateOriginAnnotationDto.newBuilder()
                        .setProjectId(projectId)
                        .setAnnotationInfos(List.of(annotationInfo1, annotationInfo2))
                        .build());

        Mockito.verify(imageAnnotationTagService)
                .batchDelete(Mockito.argThat(Set::isEmpty), Mockito.anyString());

        Mockito.verify(imageAnnotationTagService)
                .batchAdd(
                        Mockito.argThat(
                                annotations -> {
                                    assertEquals(2, annotations.size());
                                    var a1 = annotations.get(0);
                                    var a2 = annotations.get(1);
                                    return equals(annotationInfo1, a1)
                                            && equals(annotationInfo2, a2);
                                }),
                        Mockito.anyString());
        Mockito.verify(imageTagManager)
                .addAllImageTag((Map<String, Iterable<String>>)Mockito.argThat(map -> ((Map<String, Iterable<String>>)map).size() == 2), Mockito.anyString());

        // test delete old annotation
        Mockito.when(
                        imageAnnotationTagService.listByProjectIdAndAnnotationTypeAndSourceType(
                                Mockito.anyLong(), Mockito.anyList(), Mockito.any()))
                .thenReturn(List.of(getRandomAnnotation(), getRandomAnnotation()));
        annotationInfo1.setBbox(new Double[] {0.1, 0.2, 0.3, 0.4});
        annotationInfo2.setBbox(new Double[] {0.4, 0.5, 0.2, 0.1});
        updateAnnotationService.updateOriginAnnotation(
                UpdateOriginAnnotationDto.newBuilder()
                        .setProjectId(projectId)
                        .setAnnotationInfos(List.of(annotationInfo1, annotationInfo2))
                        .build());

        Mockito.verify(imageAnnotationTagService)
                .batchDelete(
                        Mockito.argThat(annotationIds -> annotationIds.size() == 2),
                        Mockito.anyString());

        Mockito.verify(imageAnnotationTagService)
                .batchAdd(
                        Mockito.argThat(
                                annotations -> {
                                    assertEquals(2, annotations.size());
                                    var a1 = annotations.get(0);
                                    var a2 = annotations.get(1);
                                    return equals(annotationInfo1, a1)
                                            && equals(annotationInfo2, a2);
                                }),
                        Mockito.anyString());
        Mockito.verify(imageTagManager, Mockito.times(2))
                .addAllImageTag((Map<String, Iterable<String>>)Mockito.argThat(map -> ((Map<String, Iterable<String>>)map).size() == 2), Mockito.anyString());
    }

    UpdateOriginAnnotationDto.AnnotationInfo mockAnnotationInfo(String imageId, String tagId) {
        return UpdateOriginAnnotationDto.AnnotationInfo.newBuilder()
                .setImageId(imageId)
                .setBbox(new Double[] {1d, 1d, 2d, 3d})
                .setConfidenceLevel(new Random().nextDouble())
                .setTagId(tagId)
                .build();
    }

    boolean equals(
            @Nonnull UpdateOriginAnnotationDto.AnnotationInfo annotationInfo,
            @Nonnull ImageAnnotation imageAnnotation) {
        Double[] bbox = annotationInfo.getBbox();
        return annotationInfo.getImageId().equals(imageAnnotation.getImageId())
                && (bbox[0] + bbox[2]) / 2 == imageAnnotation.getCenterPointX()
                && (bbox[1] + bbox[3]) / 2 == imageAnnotation.getCenterPointY()
                && annotationInfo.getConfidenceLevel() == imageAnnotation.getConfidenceLevel()
                && annotationInfo
                        .getTagId()
                        .equals(
                                AnnotationConvertUtil.convertAnnotationTypeToSystemTagId(
                                        imageAnnotation.getAnnotationType()));
    }

    ImageAnnotation getRandomAnnotation() {
        var annotation = new ImageAnnotation();
        annotation.setAnnotationId(new Random().nextLong());
        return annotation;
    }
}
