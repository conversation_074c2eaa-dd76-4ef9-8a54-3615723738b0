package com.bees360.report.service.job;

import com.bees360.common.Message.PointMessage;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.Message;
import com.bees360.job.registry.BatchAddImageAnnotationJob;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.ProjectImage;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.ImageAnnotationTagService;
import com.bees360.report.service.NewImageService;
import com.bees360.report.service.config.BatchAddImageAnnotationJobExecutorConfig;
import com.bees360.user.User;
import com.google.protobuf.Int32Value;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;
import org.mockito.stubbing.Answer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static com.bees360.image.util.AttributeMessageAdapter.attributeToJson;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@SpringBootTest
@DirtiesContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
public class BatchAddImageAnnotationJobExecutorTest {

    @Configuration
    @Import({
			BatchAddImageAnnotationJobExecutorConfig.class,
    })
    static class Config {
        @MockBean
        NewImageService imageService;

        @MockBean
        ImageAnnotationTagService imageAnnotationTagService;

        @MockBean
        ExternalInterface externalInterface;
    }

    @Autowired
    private BatchAddImageAnnotationJobExecutor addImageAnnotationJobExecutor;

    @Autowired
    private NewImageService imageService;

    @Autowired
    private ImageAnnotationTagService imageAnnotationTagService;

    @Autowired
    private ExternalInterface externalInterface;

    @Test
    void testAddImageAnnotation() throws IOException, ServiceException {
        var projectId = 1232l;
        var imageId = randomId();
        var userId = randomId();
        var userMessage = com.bees360.user.Message.UserMessage.newBuilder().setId(userId).build();
        User user = User.from(userMessage);
        var tagId = String.valueOf(ImageTagEnum.OUTDOOR_KITCHEN.getCode());
        var attribute = attributeToJson(randomAttribute());
        var points = getPointMessages();
        var job = new BatchAddImageAnnotationJob(
                List.of(
                        new BatchAddImageAnnotationJob.AddImageAnnotation(imageId,
                                List.of(new BatchAddImageAnnotationJob.Annotation(tagId, attribute, points)))),
                userId);

        addImageAnnotationJobExecutor.handle(job);

        verify(imageService, times(1)).createAnnotatedImage(eq(user), eq(imageId), any());
    }

    @Test
    void testAddImageAnnotationWithFullImage() throws IOException, ServiceException {
        var imageId = randomId();
        var userId = randomId();
        var userMessage = com.bees360.user.Message.UserMessage.newBuilder().setId(userId).build();
        User user = User.from(userMessage);
        var tagId = String.valueOf(ImageTagEnum.OUTDOOR_KITCHEN.getCode());
        var attribute = attributeToJson(randomAttribute());
        var points = getFullImageAnnotationPointMessages();
        var job = new BatchAddImageAnnotationJob(
                List.of(
                        new BatchAddImageAnnotationJob.AddImageAnnotation(imageId,
                                List.of(new BatchAddImageAnnotationJob.Annotation(tagId, attribute, points)))),
                userId);

        addImageAnnotationJobExecutor.handle(job);

        verify(imageService, times(1)).createAnnotatedImage(eq(user), eq(imageId), any());
    }

    @Test
    void testAddImageAnnotationWithNullPolygon() throws IOException, ServiceException {
        var imageId = randomId();
        var userId = randomId();
        var userMessage = com.bees360.user.Message.UserMessage.newBuilder().setId(userId).build();
        User user = User.from(userMessage);
        var tagId = String.valueOf(ImageTagEnum.OUTDOOR_KITCHEN.getCode());
        var attribute = attributeToJson(randomAttribute());
        var job = new BatchAddImageAnnotationJob(
                List.of(
                        new BatchAddImageAnnotationJob.AddImageAnnotation(imageId,
                                List.of(new BatchAddImageAnnotationJob.Annotation(tagId, attribute, null)))),
                userId);

        addImageAnnotationJobExecutor.handle(job);

        verify(imageService, times(1)).createAnnotatedImage(eq(user), eq(imageId), any());
    }

    @Test
    void testAddImageAnnotationWithDeleteImageAnnotation() throws IOException, ServiceException {
        var projectId = randomLongId();
        var imageId = randomId();
        var userId = randomId();
        var userMessage = com.bees360.user.Message.UserMessage.newBuilder().setId(userId).build();
        User user = User.from(userMessage);
        var tagId = String.valueOf(ImageTagEnum.OUTDOOR_KITCHEN.getCode());
        var attribute = attributeToJson(randomAttribute());
        var points = getPointMessages();
        var job = new BatchAddImageAnnotationJob(
                List.of(
                        new BatchAddImageAnnotationJob.AddImageAnnotation(imageId,
                                List.of(new BatchAddImageAnnotationJob.Annotation(tagId, attribute, points)))),
                userId);

        Mockito.when(externalInterface.findImageByIds(Mockito.anyList()))
                .thenAnswer(
                        (Answer)
                                invocation -> {
                                    List<String> images = invocation.getArgument(0);

                                    return images.stream().map(x -> {
                                        var projectImage = new ProjectImage();
                                        projectImage.setProjectId(projectId);
                                        projectImage.setImageId(imageId);
                                        return projectImage;
                                    }).collect(Collectors.toList());
                                });
        Mockito.when(imageAnnotationTagService.listByProjectIdAndAnnotationTypeAndSourceType(any(), any(), any()))
                .thenAnswer((Answer) invocation -> {
                    var imageAnnotation = ImageAnnotation.builder()
                            .imageId(imageId)
                            .projectId(projectId)
                            .annotationId(randomLongId())
                            .attribute(attributeToJson(randomAttribute()))
                            .build();

                    return List.of(imageAnnotation);
                });

        addImageAnnotationJobExecutor.handle(job);

        verify(imageService, times(1)).createAnnotatedImage(eq(user), eq(imageId), any());
        verify(imageAnnotationTagService, times(1)).batchDelete(any(), eq(userId));
    }

    private String randomId() {
        return RandomStringUtils.randomAlphabetic(8);
    }

    private Long randomLongId() {
        return RandomUtils.nextLong(1, 100);
    }

    private Message.AttributeMessage randomAttribute() {
        return Message.AttributeMessage.newBuilder()
                .setIndex(Int32Value.of(RandomUtils.nextInt(1, 100)))
                .setSource("CLAUDE")
                .build();
    }

    private List<PointMessage> getFullImageAnnotationPointMessages() {
        return List.of(
                getPointMessage(0, 0),
                getPointMessage(0, 1),
                getPointMessage(1, 1),
                getPointMessage(1, 0));
    }

    private List<PointMessage> getPointMessages() {
        return getPointMessages(RandomUtils.nextLong(), RandomUtils.nextLong());
    }

    private List<PointMessage> getPointMessages(double centerX, double centerY) {
        return List.of(
                getPointMessage(centerX / 2, centerY / 2),
                getPointMessage(centerX / 2, centerY / 2 * 3),
                getPointMessage(centerX / 2 * 3, centerY / 2 * 3),
                getPointMessage(centerX / 2 * 3, centerY / 2));
    }

    private static PointMessage getPointMessage(double x, double y) {
        return PointMessage.newBuilder().setX(x).setY(y).build();
    }
}
