package com.bees360.report.service.job;

import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.service.ProjectImageManager;
import com.bees360.job.registry.SyncReportImageOnImageGenerated;
import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.report.ReportManager;
import com.bees360.report.ReportResource;
import com.bees360.report.ReportSummary;
import com.bees360.report.image.ReportImageManager;
import com.bees360.report.mapper.ReportAnnotationImageMapper;
import com.bees360.report.service.FileService;
import com.bees360.report.service.ProjectReportService;

import com.bees360.resource.Resource;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest
@DirtiesContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class GenerateAnnotationImageExecutorTest {

    @Configuration
    @Import({
        SyncReportImageOnImageGeneratedJobExecutor.class,
    })
    static class Config {}

    private static final String TEST_REPORT_ID = "123";

    @Autowired
    private SyncReportImageOnImageGeneratedJobExecutor syncReportImageOnImageGeneratedJobExecutor;

    @MockBean private ReportManager reportManager;
    @MockBean private ProjectImageManager projectImageManager;
    @MockBean private FileService fileService;
    @MockBean private ReportImageManager reportImageManager;
    @MockBean private ReportAnnotationImageMapper reportAnnotationImageMapper;
    @MockBean private ProjectReportService projectReportService;

    @Test
    void testSyncReportAnnotationImages() throws IOException {
        var job = new SyncReportImageOnImageGenerated();
        job.setReportId("123");
        var summary = loadResource("report_summary.json");
        var report = getTestReport(summary);
        when(reportManager.findById(TEST_REPORT_ID)).thenReturn(report);
        when(fileService.createKey(anyString(), anyInt()))
                .thenReturn("annotation/image/resource.jpg");
        when(projectImageManager.findAllByIds(anyList())).thenReturn(getOriginImages());
        when(reportImageManager.createReportImage(anyString(), anyList(), anyString()))
                .thenReturn(Collections.emptyList());
        doNothing().when(reportAnnotationImageMapper).insertBatch(anyList());
        doNothing().when(projectReportService).deleteOldAnnotationImagesAndImages(1234, 15);

        syncReportImageOnImageGeneratedJobExecutor.handle(job);

        verify(reportManager, times(2)).findById(TEST_REPORT_ID);
        verify(fileService, times(4)).createKey(anyString(), anyInt());
        verify(projectImageManager, times(1)).findAllByIds(anyList());
        verify(reportImageManager, times(1)).createReportImage(anyString(), anyList(), anyString());
        verify(reportAnnotationImageMapper, times(1)).insertBatch(anyList());
        verify(projectReportService, times(1)).deleteOldAnnotationImagesAndImages(1234, 15);
    }

    @Test
    void testThrowExceptionWhenOriginImageNotExists() {
        var job = new SyncReportImageOnImageGenerated();
        job.setReportId("123");
        var summary = loadResource("report_summary.json");
        var report = getTestReport(summary);
        when(reportManager.findById(TEST_REPORT_ID)).thenReturn(report);
        when(fileService.createKey(anyString(), anyInt()))
                .thenReturn("annotation/image/resource.jpg");
        when(projectImageManager.findAllByIds(anyList()))
                .thenReturn(List.of(getOriginImages().get(0)));
        when(reportImageManager.createReportImage(anyString(), anyList(), anyString()))
                .thenReturn(Collections.emptyList());
        doNothing().when(reportAnnotationImageMapper).insertBatch(anyList());
        doNothing().when(projectReportService).deleteOldAnnotationImagesAndImages(1234, 15);

        Assertions.assertThrows(
                IllegalStateException.class,
                () -> syncReportImageOnImageGeneratedJobExecutor.handle(job));
    }

    @Test
    void testAbandonSavingWhenAnnotationImageHasBeenSaved() throws IOException {
        var job = new SyncReportImageOnImageGenerated();
        job.setReportId("123");
        var summary = loadResource("report_summary.json");
        var report = getTestReport(summary);
        when(reportManager.findById(TEST_REPORT_ID)).thenReturn(report);
        when(fileService.createKey(anyString(), anyInt()))
                .thenReturn("annotation/image/resource.jpg");
        when(projectImageManager.findAllByIds(anyList()))
                .thenReturn(getOriginImagesIncludeNewImage());
        when(reportImageManager.createReportImage(anyString(), anyList(), anyString()))
                .thenReturn(Collections.emptyList());
        doNothing().when(reportAnnotationImageMapper).insertBatch(anyList());
        doNothing().when(projectReportService).deleteOldAnnotationImagesAndImages(1234, 15);

        syncReportImageOnImageGeneratedJobExecutor.handle(job);

        verify(reportManager, times(1)).findById(TEST_REPORT_ID);
        verify(fileService, times(0)).createKey(anyString(), anyInt());
        verify(projectImageManager, times(1)).findAllByIds(anyList());
        verify(reportImageManager, times(0)).createReportImage(anyString(), anyList(), anyString());
        verify(reportAnnotationImageMapper, times(0)).insertBatch(anyList());
        verify(projectReportService, times(0)).deleteOldAnnotationImagesAndImages(1234, 15);
    }

    private List<ProjectImage> getOriginImages() {
        var imageOne = new ProjectImage();
        imageOne.setImageId("010101010100001111");
        imageOne.setShootingTime(System.currentTimeMillis());
        imageOne.setFileName("project/88/images/origin/ai_1705573365324_0000.jpg");
        var imageTwo = new ProjectImage();
        imageTwo.setImageId("010101010100001112");
        imageTwo.setShootingTime(System.currentTimeMillis());
        imageTwo.setFileName("project/88/images/origin/ai_1674810321992_0012.jpeg");
        return List.of(imageOne, imageTwo);
    }

    private List<ProjectImage> getOriginImagesIncludeNewImage() {
        var imageOne = new ProjectImage();
        imageOne.setImageId("010101010100001111");
        imageOne.setShootingTime(System.currentTimeMillis());
        imageOne.setFileName("project/88/images/origin/ai_1705573365324_0000.jpg");
        var imageTwo = new ProjectImage();
        imageTwo.setImageId("010101010100001112");
        imageTwo.setShootingTime(System.currentTimeMillis());
        imageTwo.setFileName("project/88/images/origin/ai_1674810321992_0012.jpeg");
        var newImage = new ProjectImage();
        newImage.setImageId("2Fv-BBgRuK4M6_y12eAo-q2pFE7eBdUc");
        newImage.setShootingTime(System.currentTimeMillis());
        newImage.setFileName("project/88/images/origin/ai_1674810321992_0013.jpeg");
        return List.of(imageOne, imageTwo, newImage);
    }

    private List<ProjectImage> getNewImages() {
        return new ArrayList<>();
    }

    private Report getTestReport(String summary) {
        return new Report() {
            @Nonnull
            @Override
            public String getId() {
                return TEST_REPORT_ID;
            }

            @Override
            public String getType() {
                return "15";
            }

            @Nullable
            @Override
            public Message.ReportMessage.Status getStatus() {
                return null;
            }

            @Nullable
            @Override
            public Map<Message.ReportMessage.Resource.Type, String> getResourceUrl() {
                return null;
            }

            @Nullable
            @Override
            public Map<Message.ReportMessage.Resource.Type, String> getResourceKey() {
                return null;
            }

            @Nullable
            @Override
            public Map<Message.ReportMessage.Resource.Type, ? extends Resource> getResource() {
                return null;
            }

            @Nullable
            @Override
            public Iterable<? extends ReportResource> getResources() {
                return null;
            }

            @Override
            public ReportSummary getSummary() {
                return new ReportSummary() {
                    @Override
                    public String getVersion() {
                        return "1.0.0";
                    }

                    @Override
                    public String getSummary() {
                        return summary;
                    }
                };
            }

            @Override
            public String getCreatedBy() {
                return "10000";
            }

            @Nullable
            @Override
            public Instant getCreatedAt() {
                return null;
            }
        };
    }

    @SneakyThrows
    static String loadResource(String resourceFileName) {
        return IOUtils.resourceToString(
                resourceFileName,
                StandardCharsets.UTF_8,
                GenerateAnnotationImageExecutorTest.class.getClassLoader());
    }
}
