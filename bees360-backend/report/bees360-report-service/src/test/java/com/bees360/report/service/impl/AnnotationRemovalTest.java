package com.bees360.report.service.impl;

import com.bees360.image.ImageTagManager;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.ImageAnnotationTagService;
import com.bees360.report.service.NewImageService;
import com.bees360.report.service.config.ServiceBeanConfig;
import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.ProjectImage;
import com.bees360.report.core.exception.ServiceException;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@SpringBootTest
@DirtiesContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class AnnotationRemovalTest {

    @Autowired private UpdateAnnotationServiceImpl updateAnnotationService;

    @MockBean private ImageAnnotationTagService imageAnnotationTagService;

    @MockBean private ExternalInterface externalInterface;

    @MockBean private ImageTagManager imageTagManager;

    @MockBean NewImageService newImageService;

    @MockBean Bees360FeatureSwitch bees360FeatureSwitch;

    @Configuration
    @Import({
        ServiceBeanConfig.class,
    })
    static class Config {}

    @Test
    void testRemovesAnnotationCloseToEdge() throws ServiceException {
        var annotation1 = new ImageAnnotation();
        annotation1.setImageId("image1");
        annotation1.setAnnotationPolygon("POLYGON((0 0,0 10,10 10,10 0))");
        var annotation2 = new ImageAnnotation();
        annotation2.setImageId("image2");
        annotation2.setAnnotationPolygon("POLYGON((90 90,90 100,100 100,100 90))");
        List<ImageAnnotation> annotations = Arrays.asList(annotation1, annotation2);

        var image1 = new ProjectImage();
        image1.setImageId("image1");
        image1.setImageWidth(100);
        image1.setImageHeight(100);
        var image2 = new ProjectImage();
        image2.setImageId("image2");
        image2.setImageWidth(100);
        image2.setImageHeight(100);

        List<ProjectImage> images = Arrays.asList(image1, image2);

        when(externalInterface.findImageByIds(anyList())).thenReturn(images);

        annotations = updateAnnotationService.removeEdgeAnnotation(annotations);

        assertEquals(0, annotations.size());
    }

    @Test
    void testKeepAnnotationAwayFromEdge() throws ServiceException {
        var annotation1 = new ImageAnnotation();
        annotation1.setImageId("image1");
        annotation1.setAnnotationPolygon("POLYGON((20 20,20 30,30 30,30 20))");
        List<ImageAnnotation> annotations = List.of(annotation1);
        var image1 = new ProjectImage();
        image1.setImageId("image1");
        image1.setImageWidth(100);
        image1.setImageHeight(100);
        List<ProjectImage> images = List.of(image1);

        when(externalInterface.findImageByIds(anyList())).thenReturn(images);

        annotations = updateAnnotationService.removeEdgeAnnotation(annotations);

        assertEquals(1, annotations.size());
        assertEquals("image1", annotations.get(0).getImageId());
    }

    @Test
    void testRemovesAnnotationIfImageNotFound() throws ServiceException {
        var annotation1 = new ImageAnnotation();
        annotation1.setImageId("image1");
        annotation1.setAnnotationPolygon("POLYGON((20 20,20 30,30 30,30 20))");
        List<ImageAnnotation> annotations = List.of(annotation1);

        when(externalInterface.findImageByIds(anyList())).thenReturn(Collections.emptyList());

        annotations = updateAnnotationService.removeEdgeAnnotation(annotations);

        assertTrue(annotations.isEmpty());
    }

    @Test
    void testHandlesServiceException() throws ServiceException {
        var annotation1 = new ImageAnnotation();
        annotation1.setImageId("image1");
        annotation1.setAnnotationPolygon("POLYGON((20 20,20 30,30 30,30 20))");
        List<ImageAnnotation> annotations = Arrays.asList(annotation1);

        when(externalInterface.findImageByIds(anyList()))
                .thenThrow(new ServiceException("Service error"));

        assertThrows(
                IllegalStateException.class,
                () -> updateAnnotationService.removeEdgeAnnotation(annotations));
    }
}
