package com.bees360.report.service.job;

import static org.junit.jupiter.api.Assertions.*;

import com.bees360.event.InMemoryEventPublisher;
import com.bees360.job.Job;
import com.bees360.job.JobDispatcher;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.UpdateInferaAnnotationJob;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.pipeline.PipelineService;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.ProjectImage;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.UpdateAnnotationService;
import com.bees360.report.service.config.InferaJobConfig;
import com.bees360.util.ListenableFutures;
import com.bees360.util.SecureTokens;
import com.google.common.util.concurrent.MoreExecutors;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.concurrent.Executor;

@SpringBootTest
@DirtiesContext
@EnableConfigurationProperties
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
class UpdateInferaOriginAnnotationJobExecutorTest {

    private static final String INFERA_JOB_NAME = "infera-job-name";

    @Import(
            value = {
                InMemoryEventPublisher.class,
                InMemoryJobScheduler.class,
                InferaJobConfig.class,
            })
    @Configuration
    static class Config {
        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @Autowired private JobDispatcher jobDispatcher;
    @Autowired private JobScheduler jobScheduler;

    @MockBean UpdateAnnotationService updateAnnotationService;
    @MockBean PipelineService pipelineService;
    @MockBean ExternalInterface externalInterface;
    @Autowired InferaJobConfig inferaJobConfig;

    @Test
    void testUpdateInferaMappingAnnotationJob() throws ServiceException {
        jobDispatcher.enlist(
                new UpdateInferaOriginAnnotationJobExecutor(
                        updateAnnotationService,
                        externalInterface,
                        pipelineService,
                        inferaJobConfig.getJobNamePipelineTaskKey()));
        // test success

        var imageId1 = SecureTokens.generateRandomBase64Token();
        Mockito.when(externalInterface.findImageByIds(Mockito.anyList()))
                .thenReturn(List.of(getRandomProjectImage(4l, imageId1)));

        var job2 =
                new UpdateInferaAnnotationJob(
                        INFERA_JOB_NAME,
                        List.of(
                                UpdateInferaAnnotationJob.InferaImage.newBuilder()
                                        .setImageId(imageId1)
                                        .setAnnotations(List.of(getRandomInferaAnnotation("526")))
                                        .build()));

        var schedule2 = jobScheduler.schedule(RetryableJob.of(Job.ofPayload(job2)));
        assertDoesNotThrow(() -> ListenableFutures.getUnchecked(schedule2));
        Mockito.verify(updateAnnotationService).updateOriginAnnotation(Mockito.any());
        Mockito.verify(pipelineService)
                .setTaskStatus(Mockito.anyString(), Mockito.anyString(), Mockito.any());
    }

    @Test
    void testNoAnnotationCase() throws ServiceException {
        jobDispatcher.enlist(
                new UpdateInferaOriginAnnotationJobExecutor(
                        updateAnnotationService,
                        externalInterface,
                        pipelineService,
                        inferaJobConfig.getJobNamePipelineTaskKey()));

        // test set pipeline status if image without annotations
        var imageId1 = SecureTokens.generateRandomBase64Token();
        var imageId2 = SecureTokens.generateRandomBase64Token();

        Mockito.when(externalInterface.findImageByIds(Mockito.anyList()))
                .thenReturn(
                        List.of(
                                getRandomProjectImage(4l, imageId1),
                                getRandomProjectImage(4l, imageId2)));
        var job1 =
                new UpdateInferaAnnotationJob(
                        INFERA_JOB_NAME,
                        List.of(
                                UpdateInferaAnnotationJob.InferaImage.newBuilder()
                                        .setImageId(imageId1)
                                        .build(),
                                UpdateInferaAnnotationJob.InferaImage.newBuilder()
                                        .setImageId(imageId2)
                                        .build()));
        var schedule1 = jobScheduler.schedule(RetryableJob.of(Job.ofPayload(job1)));
        assertDoesNotThrow(() -> ListenableFutures.getUnchecked(schedule1));
        Mockito.verify(pipelineService)
                .setTaskStatus(Mockito.anyString(), Mockito.anyString(), Mockito.any());
        Mockito.verify(updateAnnotationService, Mockito.never())
                .updateOriginAnnotation(Mockito.any());
    }

    @Test
    void testFastReturnCase() throws ServiceException {
        jobDispatcher.enlist(
                new UpdateInferaOriginAnnotationJobExecutor(
                        updateAnnotationService,
                        externalInterface,
                        pipelineService,
                        inferaJobConfig.getJobNamePipelineTaskKey()));

        var job1 = new UpdateInferaAnnotationJob(List.of());

        // test fast return if no images
        Mockito.when(externalInterface.findImageByIds(Mockito.anyList())).thenReturn(List.of());
        var schedule1 = jobScheduler.schedule(RetryableJob.of(Job.ofPayload(job1)));
        assertDoesNotThrow(() -> ListenableFutures.getUnchecked(schedule1));
        Mockito.verify(updateAnnotationService, Mockito.never())
                .updateOriginAnnotation(Mockito.any());

        // test externalInterface.findImageByIds return no images
        Mockito.when(externalInterface.findImageByIds(Mockito.anyList())).thenReturn(List.of());
        var job3 =
                new UpdateInferaAnnotationJob(
                        INFERA_JOB_NAME,
                        List.of(
                                UpdateInferaAnnotationJob.InferaImage.newBuilder()
                                        .setImageId("abc")
                                        .setAnnotations(List.of())
                                        .build()));
        var schedule3 = jobScheduler.schedule(RetryableJob.of(Job.ofPayload(job3)));
        assertDoesNotThrow(() -> ListenableFutures.getUnchecked(schedule3));
        Mockito.verify(updateAnnotationService, Mockito.never())
                .updateOriginAnnotation(Mockito.any());

        // test externalInterface.findImageByIds error
        Mockito.when(externalInterface.findImageByIds(Mockito.anyList()))
                .thenThrow(new ServiceException("400", "error"));
        var job2 =
                new UpdateInferaAnnotationJob(
                        List.of(
                                UpdateInferaAnnotationJob.InferaImage.newBuilder()
                                        .setImageId("abc")
                                        .build()));
        var schedule2 = jobScheduler.schedule(RetryableJob.of(Job.ofPayload(job2)));
        assertThrows(IllegalStateException.class, () -> ListenableFutures.getUnchecked(schedule2));
    }

    @Test
    void testSomeProjectError() throws ServiceException {
        jobDispatcher.enlist(
                new UpdateInferaOriginAnnotationJobExecutor(
                        updateAnnotationService,
                        externalInterface,
                        pipelineService,
                        inferaJobConfig.getJobNamePipelineTaskKey()));

        var imageId1 = SecureTokens.generateRandomBase64Token();
        var imageId2 = SecureTokens.generateRandomBase64Token();
        Mockito.when(externalInterface.findImageByIds(Mockito.anyList()))
                .thenReturn(
                        List.of(
                                getRandomProjectImage(4l, imageId1),
                                getRandomProjectImage(5l, imageId2)));

        Mockito.doThrow(new RuntimeException("Mock error"))
                .when(updateAnnotationService)
                .updateOriginAnnotation(Mockito.argThat(dto -> dto.getProjectId() == 5L));

        List<UpdateInferaAnnotationJob.InferaAnnotation> annotations1 =
                List.of(getRandomInferaAnnotation("526"));
        List<UpdateInferaAnnotationJob.InferaAnnotation> annotations2 =
                List.of(getRandomInferaAnnotation("1514"));
        var job2 =
                new UpdateInferaAnnotationJob(
                        INFERA_JOB_NAME,
                        List.of(
                                UpdateInferaAnnotationJob.InferaImage.newBuilder()
                                        .setImageId(imageId1)
                                        .setAnnotations(annotations1)
                                        .build(),
                                UpdateInferaAnnotationJob.InferaImage.newBuilder()
                                        .setImageId(imageId2)
                                        .setAnnotations(annotations2)
                                        .build()));

        var schedule2 = jobScheduler.schedule(RetryableJob.of(Job.ofPayload(job2)));

        assertThrows(IllegalStateException.class, () -> ListenableFutures.getUnchecked(schedule2));
    }

    private static UpdateInferaAnnotationJob.InferaAnnotation getRandomInferaAnnotation(
            String tagId) {
        return UpdateInferaAnnotationJob.InferaAnnotation.newBuilder()
                .setTagId(tagId)
                .setBbox(new Double[] {1d, 2d, 3d, 4d})
                .setConfidenceLevel(0.92)
                .build();
    }

    protected ProjectImage getRandomProjectImage(long projectId, String imageId) {
        var image = new ProjectImage(imageId);
        image.setProjectId(projectId);
        image.setImageHeight(1000);
        image.setImageWidth(1000);
        return image;
    }
}
