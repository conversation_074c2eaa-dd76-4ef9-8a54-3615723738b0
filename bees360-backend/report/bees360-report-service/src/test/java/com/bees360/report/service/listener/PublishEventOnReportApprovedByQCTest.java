package com.bees360.report.service.listener;

import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.registry.ReportStatusChanged;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.report.ReportProvider;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.service.ProjectReportFileService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;
import java.util.function.Supplier;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PublishEventOnReportApprovedByQCTest {

    @Mock private ReportProvider reportProvider;

    @Mock private ProjectReportProvider projectReportProvider;

    @Mock private ProjectReportFileService projectReportFileService;

    @Mock private Supplier<String> qCUserSupplier;

    @InjectMocks private PublishEventOnReportApprovedByQC listener;

    private final String QC_USER = "qc_user";

    private final String REPORT_ID = "report-id";

    @Test
    void handleWhenUpdatedByNotQCShouldDoNothing() throws IOException {
        ReportStatusChanged event = new ReportStatusChanged();
        event.setId(REPORT_ID);
        event.setReportStatus(Message.ReportMessage.Status.APPROVED_VALUE);
        event.setUpdatedBy("other_user");

        when(qCUserSupplier.get()).thenReturn(QC_USER);
        listener.handle(event);

        verify(reportProvider, never()).get(anyString());
    }

    @Test
    void handleWhenStatusNotApproveShouldDoNothing() throws IOException {
        ReportStatusChanged event = new ReportStatusChanged();
        event.setId(REPORT_ID);
        event.setReportStatus(Message.ReportMessage.Status.SUBMITTED_VALUE);
        event.setUpdatedBy(QC_USER);

        listener.handle(event);

        verify(reportProvider, never()).get(anyString());
    }

    @Test
    void handleWhenReportTypeNotNeedApprovalShouldSkip() throws IOException {
        ReportStatusChanged event = new ReportStatusChanged();
        event.setId(REPORT_ID);
        event.setReportStatus(Message.ReportMessage.Status.APPROVED_VALUE);
        event.setUpdatedBy(QC_USER);

        Report mockReport = mock(Report.class);
        when(reportProvider.get(REPORT_ID)).thenReturn(mockReport);
        when(qCUserSupplier.get()).thenReturn(QC_USER);
        String NO_APPROVAL_TYPE = "CDF";
        when(mockReport.getType()).thenReturn(NO_APPROVAL_TYPE);

        listener.handle(event);

        verify(projectReportProvider, never()).findProjectId(anyString());
    }

    @Test
    void handleWhenValidConditionsShouldPublishEvents() throws IOException, ServiceException {
        ReportStatusChanged event = new ReportStatusChanged();
        event.setId(REPORT_ID);
        event.setReportStatus(Message.ReportMessage.Status.APPROVED_VALUE);
        event.setUpdatedBy(QC_USER);

        Report mockReport = mock(Report.class);
        when(reportProvider.get(REPORT_ID)).thenReturn(mockReport);
        String NEED_APPROVAL_TYPE = "ROR";
        when(mockReport.getType()).thenReturn(NEED_APPROVAL_TYPE);
        String PROJECT_ID = "456";
        when(projectReportProvider.findProjectId(REPORT_ID)).thenReturn(List.of(PROJECT_ID));
        when(qCUserSupplier.get()).thenReturn(QC_USER);

        listener.handle(event);

        verify(projectReportFileService)
                .publishReportApprovedEvent(
                        eq(Long.parseLong(PROJECT_ID)),
                        eq(QC_USER),
                        eq(ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT));
    }
}
