package com.bees360.report.service.event.project;

import com.bees360.report.service.event.Bees360Event;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

/**
 * 报告节点更改导致
 * <AUTHOR>
 */
public class ReportChangeStatusEvent extends Bees360Event {

    @Getter
    final long projectId;

    @Getter
    final int newStatusCode;

    @Getter
    final String userId;

    @Getter
    final int reportTypeCode;

    @Getter
    @Setter
    Instant updateAt;

    public ReportChangeStatusEvent(Object source, long projectId, int newStatusCode, String userId, int reportTypeCode) {
        super(source);
        this.projectId = projectId;
        this.newStatusCode = newStatusCode;
        this.userId = userId;
        this.reportTypeCode = reportTypeCode;
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }
}
