package com.bees360.report.service.util.summary;

import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.report.core.exception.MessageCode;
import com.bees360.report.core.exception.ServiceMessageException;
import com.bees360.report.entity.vo.ReportSummaryVo;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

@RequiredArgsConstructor
public class SummaryValidatorForFourPointSelfService implements SummaryValidatorProvider {

    @Override
    public boolean support(ProjectServiceTypeEnum serviceType, int curReportType) {
        return serviceType == ProjectServiceTypeEnum.FOUR_POINT_SELF_UNDERWRITING
            && serviceType.containsReport(curReportType);
    }

    @Override
    public void validate(long projectId, ProjectServiceTypeEnum serviceType, int reportType, ReportSummaryVo summary)
        throws ServiceMessageException {
        if (Objects.isNull(summary)) {
            return;
        }
        if(summary.getInterior() == null) {
            String message = "Interior is required for 4-Point Self inspection.";
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, message);
        }
    }
}
