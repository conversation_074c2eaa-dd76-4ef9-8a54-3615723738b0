package com.bees360.report.service.impl;

import com.bees360.entity.ReportSummary;
import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.project.ProjectIIManager;
import com.bees360.report.Report;
import com.bees360.report.ReportProvider;
import com.bees360.report.core.exception.MessageCode;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.vo.ReportSummaryUnverifiedPercentageVo;
import com.bees360.report.entity.vo.ReportSummaryVo;
import com.bees360.report.service.ReportCheckList;
import com.bees360.report.service.ReportSummaryService;
import com.bees360.report.service.config.ReportProperties;
import com.bees360.report.service.util.summary.ReportSummaryConverter;
import com.bees360.report.service.util.summary.SummaryValidator;
import io.jsonwebtoken.lang.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReportCheckListSummaryImpl implements ReportCheckList {

    private final ProjectIIManager projectIIManager;

    private final ReportProvider reportProvider;

    private final SummaryValidator summaryValidator;

    private final ReportSummaryConverter reportSummaryConverter;

    private final ReportProperties reportProperties;

    private final Validator validator;

    private final Predicate<Integer> unverifiedPercentagePredicate;

    @Override
    public void check(long projectId, String reportId) throws ServiceException {
        if (!isSupport(projectId, reportId)) {
            return;
        }
        var report = reportProvider.findById(reportId);
        if (Objects.isNull(report)) {
            throw new ServiceException(MessageCode.REPORT_NOT_EXIST, "report `" + reportId + "` not found.");
        }
        var reportType = Integer.parseInt(report.getType());
        ReportSummary reportSummary = convertSummaryToWEBSummary(projectId, reportType, report);
        if (Objects.isNull(reportSummary) || StringUtils.isEmpty(reportSummary.getSummary())) {
            throw new ServiceException(MessageCode.REPORT_SUMMARY_REQUIRED, "summary of report `" + reportId + "` is required.");
        }
        var project = projectIIManager.get(String.valueOf(projectId));
        log.info("check report summary projectId: {}, projectServiceType: {}",
            projectId, project.getServiceType().getCode());

        ProjectServiceTypeEnum projectServiceType = ProjectServiceTypeEnum.getEnum(project.getServiceType().getCode());

        Class<? extends ReportSummaryVo> clazz;
        if (unverifiedPercentagePredicate.test(reportType)) {
            clazz = ReportSummaryUnverifiedPercentageVo.class;
        } else {
            clazz = ReportSummaryVo.class;
        }
        ReportSummaryVo summaryVo = reportSummaryConverter.toSummaryVo(reportSummary.getSummary(), clazz);
        // ReportSummaryVo 的字段校验
        Set<ConstraintViolation<ReportSummaryVo>> constraintViolations = validator.validate(summaryVo);
        if (!Collections.isEmpty(constraintViolations)) {
            throw new ServiceException(MessageCode.REPORT_SUMMARY_INVALID, "summary of report`" + reportId + "` is invalid.");
        }
        summaryValidator.validate(projectId, projectServiceType, reportType, summaryVo);
    }

    private ReportSummary convertSummaryToWEBSummary(
        long projectId, int reportType, Report report) {
        if (StringUtils.equals(ReportSummaryService.NULL_SUMMARY, report.getSummary().getSummary())) {
            return null;
        }
        var summary = new ReportSummary();
        summary.setProjectId(projectId);
        summary.setReportType(reportType);
        summary.setDeleted(false);
        summary.setSummary(report.getSummary().getSummary());
        return summary;
    }

    private boolean isSupport(long projectId, String reportId) {
        var project = projectIIManager.get(String.valueOf(projectId));
        int serviceType = project.getServiceType().getCode();
        if (!ProjectServiceTypeEnum.isUnderWriting(serviceType)) {
            return false;
        }
        var companies = new HashSet<Long>();
        if (project.getContract().getInsuredBy() != null) {
            companies.add(Long.parseLong(project.getContract().getInsuredBy().getId()));
        }
        if (project.getContract().getProcessedBy() != null) {
            companies.add(Long.parseLong(project.getContract().getProcessedBy().getId()));
        }
        log.info("check is support company projectId: {}, companies: {}", projectId, companies);
        return companies.stream().anyMatch(id -> reportProperties.getSummary().getForCompanies().contains(id));
    }
}
