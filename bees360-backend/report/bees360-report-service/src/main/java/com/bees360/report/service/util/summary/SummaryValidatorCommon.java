package com.bees360.report.service.util.summary;

import com.bees360.report.entity.ProjectImage;
import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.report.core.exception.MessageCode;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.core.exception.ServiceMessageException;
import com.bees360.report.entity.vo.ReportSummaryVo;
import com.bees360.report.entity.vo.summary.SummaryImage;
import com.bees360.report.entity.vo.summary.SummaryRecommendation;
import com.bees360.report.service.ExternalInterface;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor
public class SummaryValidatorCommon implements SummaryValidatorProvider {

    private final ExternalInterface externalInterface;

    @Override
    public boolean support(ProjectServiceTypeEnum projectServiceType, int curReportType) {
        return true;
    }

    @Override
    public void validate(long projectId, ProjectServiceTypeEnum projectServiceType, int reportType,
                         ReportSummaryVo summary) throws ServiceException {
        checkSummaryRecommendationText(summary);
        checkSummaryRecommendationImages(projectId, summary);
        checkSummaryFactorImages(projectId, summary);
    }

    private void checkSummaryRecommendationText(ReportSummaryVo summary) throws ServiceException {
        if(summary == null || summary.getRecommendations() == null) {
            return;
        }
        List<SummaryRecommendation> recommendations = summary.getRecommendations();
        Set<String> textSet = recommendations.stream().map(rec -> rec.getText()).collect(Collectors.toSet());
        if (textSet.size() != recommendations.size()) {
            String message = "Duplicate texts in the recommendation list.";
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, message);
        }
    }

    private void checkSummaryRecommendationImages(long projectId, ReportSummaryVo summary) throws ServiceException {
        if(summary == null || summary.getRecommendations() == null) {
            return;
        }
        List<String> imageIds = summary.getRecommendations()
            .stream().filter(rec -> rec != null && rec.getImage() != null).flatMap(rec -> rec.getImage().stream())
            .filter(img -> img != null).map(img -> img.getId())
            .collect(Collectors.toList());

        checkImgIds(imageIds,projectId);
    }

    private void checkSummaryFactorImages(long projectId, ReportSummaryVo summary) throws ServiceException {
        if (summary == null || summary.getFactors() == null) {
            return;
        }
        List<String> imgIds = summary.getFactors().stream()
            .filter(fac -> fac != null && fac.getImage() != null)
            .flatMap(fac -> fac.getImage().stream())
            .filter(Objects::nonNull)
            .map(SummaryImage::getId)
            .collect(Collectors.toList());

        checkImgIds(imgIds,projectId);
    }

    private void checkImgIds(List<String> imgToCheck, long projectId) throws ServiceException {
        if (CollectionUtils.isEmpty(imgToCheck)) {
            return;
        }
        List<ProjectImage> images = externalInterface.listImages(projectId);
        Set<String> imagesInProject = images.stream().map(ProjectImage::getImageId).collect(Collectors.toSet());
        List<String> invalidImages = imgToCheck.stream().filter(id -> !imagesInProject.contains(id)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(invalidImages)) {
            String message = "Invalid image : " + invalidImages;
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, message);
        }
    }
}
