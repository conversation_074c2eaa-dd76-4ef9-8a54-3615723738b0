package com.bees360.report.service;

import com.bees360.entity.dto.DamageReportParamDto;
import com.bees360.entity.dto.ReportMessageDto;
import com.bees360.entity.dto.ReportPrefixDto;
import com.bees360.entity.dto.ServiceReportTypeDto;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.dto.ReportSummaryDto;
import com.bees360.report.entity.vo.EditorDataVo;
import java.util.List;

import com.bees360.entity.ProjectReportFile;

/**
 *
 * <AUTHOR>
 * @contributors
 * @date 2017/12/19 11:49:53
 */
public interface ProjectReportService {

	/**
	 * update report url
	 * @param projectId
	 * @param url
	 */
	int updateReportUrl(long projectId, String reportId, String url);

    void saveOrUpdateReportEditorData(long projectId, String userId, EditorDataVo editorData, String fullSummaryJson) throws ServiceException;

    void transferDataToWeb(long projectId) throws ServiceException;

    void upsertReportSummary(long projectId, String userId, ReportSummaryDto reportSummary, String fullSummaryJson) throws ServiceException;

    ReportPrefixDto getReportFilePrefix();

    ProjectReportFile updateReportMessage(DamageReportParamDto reportParam, ReportMessageDto reportMessage) throws Exception;

    List<ServiceReportTypeDto> getReportTypes();

    void saveProjectInvoice(String projectId, String reportKey, String htmlKey,
            String invoiceSummary, String userId) throws ServiceException;

    void deleteOldAnnotationImagesAndImages(long projectId, int reportType);
}
