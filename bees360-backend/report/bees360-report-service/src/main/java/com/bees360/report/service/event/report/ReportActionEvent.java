package com.bees360.report.service.event.report;

import com.bees360.report.service.event.Bees360Event;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/07/30 16:12
 */
public class ReportActionEvent extends Bees360Event {

    @Getter
    final long projectId;

    @Getter
    final int reportType;

    @Getter
    final int reportGenerationStatus;

    @Getter
    final String userId;

    public ReportActionEvent(Object source, long projectId, int reportType, int reportGenerationStatus, String userId) {
        super(source);
        this.projectId = projectId;
        this.reportType = reportType;
        this.reportGenerationStatus = reportGenerationStatus;
        this.userId = userId;
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }
}
