package com.bees360.report.service.impl;

import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.service.ReportCheckList;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.Iterator;

/**
 * <AUTHOR>
 */
@Primary
@Component
@RequiredArgsConstructor
public class ReportCheckListComposite implements ReportCheckList {

    private final ObjectProvider<ReportCheckList> checkLists;

    @Override
    public void check(long projectId, String reportId) throws ServiceException {
        Iterator<ReportCheckList> iter = checkLists.stream().iterator();

        while (iter.hasNext()) {
            ReportCheckList checkList = iter.next();
            checkList.check(projectId, reportId);
        }
    }
}
