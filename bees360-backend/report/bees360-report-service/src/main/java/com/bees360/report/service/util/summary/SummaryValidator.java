package com.bees360.report.service.util.summary;

import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.vo.ReportSummaryVo;

import java.util.ArrayList;
import java.util.List;

public class SummaryValidator {

    private List<SummaryValidatorProvider> providers = new ArrayList<>();

    public SummaryValidator() {}

    public SummaryValidator(List<SummaryValidatorProvider> providers) {
        this.providers = providers;
    }

    public void setValidatorProvider(List<SummaryValidatorProvider> providers) {
        this.providers = providers;
    }

    public void addValidatorProvider(SummaryValidatorProvider provider) {
        providers.add(provider);
    }

    public void validate(long projectId, ProjectServiceTypeEnum serviceType, int reportType, ReportSummaryVo summary)
        throws ServiceException {
        for (SummaryValidatorProvider provider: providers) {
            if(!provider.support(serviceType, reportType)) {
                continue;
            }
            provider.validate(projectId, serviceType, reportType, summary);
        }
    }
}
