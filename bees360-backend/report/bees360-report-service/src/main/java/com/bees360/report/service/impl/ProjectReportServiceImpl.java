package com.bees360.report.service.impl;

import com.bees360.api.AbortedException;
import com.bees360.api.AlreadyExistsException;
import com.bees360.api.InvalidArgumentException;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.entity.Project;
import com.bees360.entity.dto.ReportTypeDto;
import com.bees360.entity.dto.ServiceReportTypeDto;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.invoice.ProjectInvoiceFactory;
import com.bees360.project.invoice.ProjectInvoiceGenerator;
import com.bees360.report.core.exception.ServiceMessageException;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.ReportSummary;
import com.bees360.entity.User;
import com.bees360.entity.dto.DamageReportParamDto;
import com.bees360.entity.dto.ReportMessageDto;
import com.bees360.entity.dto.ReportPrefixDto;
import com.bees360.entity.enums.DamageSeverityEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.internal.ai.common.msgcode.MsgCodeManager;
import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.report.core.exception.MessageCode;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.dto.ReportSummaryDto;
import com.bees360.report.entity.vo.EditorDataVo;
import com.bees360.report.entity.vo.ReportSummaryVo;
import com.bees360.report.grpc.client.ReportGrpcClient;
import com.bees360.report.mapper.ReportAnnotationImageMapper;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.FileService;
import com.bees360.report.service.ProjectReportFileService;
import com.bees360.report.service.ProjectReportService;
import com.bees360.report.service.ReportSummaryService;
import com.bees360.report.service.event.report.ReportActionEvent;
import com.bees360.report.service.util.summary.SummaryValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import static com.bees360.pipeline.Message.PipelineStatus.ERROR;

@Slf4j
@Service("projectReportService")
public class ProjectReportServiceImpl implements ProjectReportService {

    private static final String INVOICE_PIPELINE_KEY = "generate_inv";
    @Inject
    private ExternalInterface externalInterface;

    @Inject private ProjectReportFileService projectReportFileService;

    @Inject
    private ReportAnnotationImageMapper reportAnnotationImageMapper;

    @Inject
    private FileService fileService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Inject ProjectIIManager projectIIManager;

    @Autowired
    private ReportGrpcClient reportGrpcClient;

    @Autowired
    private ReportSummaryService reportSummaryService;

    @Autowired
    private SummaryValidator summaryValidator;

    @Autowired
    private ProjectInvoiceGenerator projectInvoiceGenerator;

    @Autowired
    private PipelineService pipelineService;

    @Override
    public ProjectReportFile updateReportMessage(DamageReportParamDto reportParam, ReportMessageDto reportMessage)
        throws Exception {
        if (Objects.isNull(reportMessage)
            || StringUtils.isEmpty(reportMessage.getPdf())) {
            log.info("error to save report message. parameter is:" + reportParam.toString());
            throw new ServiceException(MessageCode.REPORT_NOT_EXIST);
        }
        long projectId = reportParam.getProject().getProjectId();

        // 存在生成时间更晚的report，直接返回存在的report
        var existedReport =
                projectReportFileService.getReportByType(
                        projectId, reportParam.getReportType().getCode());
        if (existedReport != null && existedReport.getCreatedTime() > reportParam.getTime()) {
            return existedReport;
        }

        String reportKey = reportMessage.getPdf();
        if (StringUtils.isNotEmpty(reportMessage.getFilePath())) {
            reportKey = fileService.uploadReport(reportMessage.getFilePath());
        }

        ReportTypeEnum reportType = reportParam.getReportType();
        long userId = reportParam.getUserId();

        ReportGenerationStatusEnum reportStatus = isAutoApproved(reportType) ?
            ReportGenerationStatusEnum.APPROVED : ReportGenerationStatusEnum.GENERATED;

        ProjectReportFile projectReportFile = new ProjectReportFile();
        projectReportFile.setProjectId(projectId);
        projectReportFile.setCreatedBy(userId);
        projectReportFile.setCreatedTime(reportParam.getTime());
        projectReportFile.setRead(false);
        projectReportFile.setDeleted(false);

        projectReportFile.setGenerationStatus(reportStatus);
        projectReportFile.setReportType(reportType.getCode());
        // TODO zhoushan.zhao 此字段应该改名字
        projectReportFile.setReportPdfFileName(reportKey);
        projectReportFile.setSize(reportMessage.getSize());
        projectReportFile.setAiUserId(reportParam.getAiUserId());
        updateReportFile(projectReportFile);

        // 发送report节点动作变更事件
        publishActionEvent(reportParam.getAiUserId(), projectId, reportType.getCode());
        if (Objects.nonNull(DamageSeverityEnum.getEnum(reportMessage.getDamageSevere()))) {
            externalInterface.updateDamageSeverity(projectId, reportMessage.getDamageSevere());
        }
        return projectReportFile;
    }

    private void publishActionEvent(String aiUserId, long projectId, int reportType) {
        if (isAutoApproved(ReportTypeEnum.getEnum(reportType))) {
            applicationEventPublisher.publishEvent(new ReportActionEvent(this, projectId,
                reportType, ReportGenerationStatusEnum.APPROVED.getCode(), aiUserId));
        }
    }

    private void updateReportFile(ProjectReportFile projectReportFile) {
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(projectReportFile.getReportType());
        assert reportType != null;
        projectReportFileService.insertReportFile(projectReportFile);
    }

    private boolean isAutoApproved(ReportTypeEnum reportType) {
        return !reportType.needApproved() && reportType.APPROVED != null;
    }

    @Override
    public int updateReportUrl(long projectId, String reportId, String url) {
        return projectReportFileService.updateReportUrl(projectId, reportId, url);
    }

    private ProjectReportFile insertReport(long projectId, long userId, ProjectReportFile reportFile, String aiUserId)
        throws ServiceException {
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportFile.getReportType());
        if (reportType == null) {
            throw new ServiceException(MessageCode.PARAM_INVALID);
        }
        DamageReportParamDto reportParam = new DamageReportParamDto();
        reportParam.setUserId(userId);
        reportParam.setProject(new Project(projectId));
        reportParam.setTime(System.currentTimeMillis());
        reportParam.setReportType(reportType);
        reportParam.setAiUserId(aiUserId);
        ReportMessageDto reportMessage = ReportMessageDto.builder()
            .pdf(reportFile.getReportPdfFileName())
            .size(reportFile.getSize())
            .build();
        try {
            return updateReportMessage(reportParam, reportMessage);
        } catch (Exception e) {
            log.error("insert report error. projectId:" + projectId, e);
            throw new ServiceException(MessageCode.BAD_REQUEST);
        }
    }
    @Transactional
    @Override
    public void saveOrUpdateReportEditorData(long projectId, String userId, EditorDataVo editorData, String fullReportSummaryJson)
        throws ServiceException {
        log.info("editor save or update report image annotation data start projectId:{}, imageList:{}",
            projectId, ListUtil.toString(editorData.getImages()));

        upsertReportSummary(projectId, editorData, fullReportSummaryJson);

        int reportType = editorData.getReport().getReportType();

        // insert report
        // 允许editor接口不传report
        var pdfKey = editorData.getReport().getReportUrl();
        if (StringUtils.isNotBlank(pdfKey)) {
            // 前置校验
            var invoiceSummary = editorData.getInvoiceSummary();
            if (reportType == ReportTypeEnum.INVOICE.getCode() && invoiceSummary != null) {
                // 防止前端传入空报告
                var summary = ProjectInvoiceFactory.fromSummary(invoiceSummary);
                if (summary.getInvoice() == null) {
                    log.info("save invoice report failed {}", invoiceSummary);
                    throw new ServiceMessageException(
                            "10004", "Failed to generate, summary is empty");
                }
                saveProjectInvoice(String.valueOf(projectId), pdfKey, null, invoiceSummary, userId);
            } else {
                saveReport(projectId, userId, editorData, reportType, pdfKey);
            }
        }

        log.info("editor save or update report image annotation data end projectId:{}", projectId);
    }

    private void saveReport(
            long projectId, String userId, EditorDataVo editorData, int reportType, String pdfKey)
            throws ServiceException {
        ProjectReportFile reportInsertFile = new ProjectReportFile();
        reportInsertFile.setReportPdfFileName(pdfKey);
        reportInsertFile.setSize(editorData.getReport().getSize());
        reportInsertFile.setReportType(reportType);
        insertReport(projectId, User.AI_ID, reportInsertFile, userId);
    }

    @Override
    public void transferDataToWeb(long projectId) throws ServiceException {
        try {
            reportGrpcClient.approveReport(projectId);
        } catch (Exception e) {
            log.error("failed to transfer data to web. projectId:{}", projectId, e);
            throw new ServiceException(MsgCodeManager.REPORT.REPORT_APPROVED_ERROR.MSG);
        }
    }

    @Override
    public void upsertReportSummary(long projectId, String userId, ReportSummaryDto reportSummary, String fullSummaryJson)
        throws ServiceException {
        upsertReportSummary(projectId, reportSummary.getReportType(), reportSummary.getReportSummary(), fullSummaryJson);
    }

    @Override
    public ReportPrefixDto getReportFilePrefix() {
        return ReportPrefixDto.builder().prefix(externalInterface.getReportFilePrefix()).build();
    }

    @Override
    public List<ServiceReportTypeDto> getReportTypes() {
        ProjectServiceTypeEnum[] projectServiceTypes = ProjectServiceTypeEnum.values();
        List<ServiceReportTypeDto> serviceReportTypes = new ArrayList<>();

        for (ProjectServiceTypeEnum projectServiceType : projectServiceTypes) {
            List<ReportTypeDto> reportTypes = new ArrayList<>();

            List<com.bees360.internal.ai.entity.enums.ReportTypeEnum> editorReports =
                    new ArrayList<>(projectServiceType.getReportTypes());
            editorReports.addAll(projectServiceType.getEditorExpandReports());
            for (com.bees360.internal.ai.entity.enums.ReportTypeEnum reportTypeEnum :
                    editorReports) {
                ReportTypeDto reportType =
                        new ReportTypeDto(reportTypeEnum.getCode(), reportTypeEnum.getDisplay());
                reportTypes.add(reportType);
            }

            serviceReportTypes.add(
                    ServiceReportTypeDto.builder()
                            .serviceType(projectServiceType.getCode())
                            .serviceTypeName(projectServiceType.getDisplay())
                            .reportTypes(reportTypes)
                            .build());
        }
        return serviceReportTypes;
    }

    private void upsertReportSummary(long projectId, EditorDataVo editorData, String reportSummaryJson) throws ServiceException {
        var reportType = editorData.getReport().getReportType();
        if (reportType == ReportTypeEnum.INVOICE.getCode()) {
            ReportSummary reportSummary = new ReportSummary();
            reportSummary.setProjectId(projectId);
            reportSummary.setReportType(reportType);
            reportSummary.setSummary(editorData.getInvoiceSummary());
            reportSummaryService.upsert(reportSummary);
            return;
        }
        upsertReportSummary(projectId, reportType, editorData.getReportSummary(), reportSummaryJson);
    }

    private void upsertReportSummary(long projectId, int reportType, ReportSummaryVo summary, String fullSummaryJson) throws ServiceException {
        var project = projectIIManager.get(String.valueOf(projectId));
        log.info("update report summary projectId: {}, projectServiceType: {}, report summary :{}",
            projectId, project.getServiceType().getCode(), summary);

        ProjectServiceTypeEnum serviceType = ProjectServiceTypeEnum.getEnum(project.getServiceType().getCode());

        summaryValidator.validate(projectId, serviceType, reportType, summary);

        ReportSummary reportSummary = new ReportSummary();
        reportSummary.setProjectId(projectId);
        reportSummary.setReportType(reportType);
        try {
            reportSummary.setSummary(fullSummaryJson);
            log.info("get summary json :{}", fullSummaryJson);
        } catch (Exception e) {
            throw new ServiceException(MessageCode.PARAM_INVALID, e);
        }
        reportSummaryService.upsert(reportSummary);
    }

    @Override
    public void deleteOldAnnotationImagesAndImages(long projectId, int reportType) {
        List<String> imageIdList = reportAnnotationImageMapper.listImageId(projectId, reportType);
        if (CollectionAssistant.isEmpty(imageIdList)) {
            return;
        }
        reportAnnotationImageMapper.deleteByProjectIdAndReportType(projectId, reportType);
        externalInterface.deleteAnnotationImageByIds(projectId, imageIdList);
    }

    private void setTaskError(String projectId, String key) {
        try {
            pipelineService.setTaskStatus(projectId, key, ERROR);
            log.info("Successfully set pipeline '{}' key '{}' to '{}'", projectId, key, ERROR);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Failed set pipeline '{}' key '{}' to '{}'", projectId, key, ERROR, e);
        }
    }

    @Override
    public void saveProjectInvoice(String projectId, String reportKey, String htmlKey, String invoiceSummary,
                                   String userId) throws ServiceMessageException {
        if (StringUtils.isBlank(invoiceSummary)) {
            var summary =
                    reportSummaryService.getOne(
                            Long.parseLong(projectId), ReportTypeEnum.INVOICE.getCode());
            if (summary == null) {
                throw new ServiceMessageException("10004", "The invoice summary not exists.");
            }
            invoiceSummary = summary.getSummary();
        }
        try {
            if (StringUtils.isNotBlank(reportKey)) {
                projectInvoiceGenerator.generateProjectInvoice(projectId, reportKey, invoiceSummary, userId);
            } else {
                projectInvoiceGenerator.generateProjectInvoiceByHtml(projectId, htmlKey, invoiceSummary, userId);
            }
        } catch (AlreadyExistsException e) {
            log.info("The invoice already exists projectId {}", projectId, e);
            throw new ServiceMessageException("10006", "The invoice already exists.");
        } catch (AbortedException e) {
            log.info("The invoice is paid projectId {}", projectId, e);
            throw new ServiceMessageException("10007", "The invoice is paid.");
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Generate invoice failed projectId {}", projectId, e);
            setTaskError(String.valueOf(projectId), INVOICE_PIPELINE_KEY);
            throw new ServiceMessageException("10005", "Generate invoice failed. ");
        }
    }
}
