package com.bees360.report.service.listener;

import com.bees360.event.registry.ReportStatusChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.report.Report;
import com.bees360.report.ReportManager;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.service.ProjectReportFileService;
import com.bees360.report.service.impl.ProjectReportFileServiceImpl;
import com.bees360.util.Iterables;
import lombok.extern.log4j.Log4j2;
import com.bees360.entity.enums.ReportTypeEnum;

import java.io.IOException;
import java.util.function.Predicate;

/**
 * 监听报告状态变更事件，在报告被批准后执行后续处理操作，包括发布报告批准事件。
 */
@Log4j2
public class ReportApprovedPostProcessingOnReportStatusChanged
		extends AbstractNamedEventListener<ReportStatusChanged> {

	private final ProjectReportManager projectReportManager;
	private final ReportManager reportManager;
	private final ProjectReportFileService projectReportFileService;

	private final Predicate<Report> reportApprovedPostProcessingPredicate;

	public ReportApprovedPostProcessingOnReportStatusChanged(
			ProjectReportManager projectReportManager,
			ReportManager reportManager,
			ProjectReportFileService projectReportFileService,
			Predicate<Report> reportApprovedPostProcessingPredicate) {
		this.projectReportManager = projectReportManager;
		this.reportManager = reportManager;
		this.projectReportFileService = projectReportFileService;
		this.reportApprovedPostProcessingPredicate = reportApprovedPostProcessingPredicate;
		log.info(
				"{} created: {projectReportManager={}, reportManager={}, projectReportFileService={}, reportApprovedPostProcessingPredicate={}}",
				this,
				this.projectReportManager,
				this.reportManager,
				this.projectReportFileService,
				this.reportApprovedPostProcessingPredicate);
	}


	@Override
	public void handle(ReportStatusChanged event) throws IOException {
        log.info("Received event: {}.", event);
		var reportId = event.getId();
		var report = reportManager.findById(reportId);
		if (report == null) {
			throw new IllegalArgumentException(String.format("The report does not exist. reportId='%s'", reportId));
		}

		var createdBy = report.getCreatedBy();
		var reportTypeCode = ProjectReportFileServiceImpl.getReportType(report);
		var reportType = ReportTypeEnum.getEnum(reportTypeCode);
		if (reportApprovedPostProcessingPredicate.test(report)) {
			var projectIds = Iterables.toList(projectReportManager.findProjectId(reportId));
            log.info("Found projectIds for processing: {}.", projectIds);
			projectIds.forEach(projectId -> {
				try {
					projectReportFileService.publishReportApprovedEvent(Long.parseLong(projectId), createdBy, reportType);
				} catch (ServiceException e) {
					throw new IllegalStateException(e);
				}
			});
            log.info("Successfully completed approved post-processing for report type: {} and projectIds: {}.", reportType, projectIds);
		}
	}
}
