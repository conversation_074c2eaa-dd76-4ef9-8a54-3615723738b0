package com.bees360.report.service;

import com.bees360.entity.vo.ReportTypeKeyVo;
import com.bees360.event.registry.JobCompleted;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.user.User;

public interface PdfReportGenerationService {

    boolean generateReportByHtml(
            long projectId,
            String userId,
            ReportTypeKeyVo reportTypeKey,
            String jsonData,
            boolean force) throws ServiceException;

    /**
     * generate editor report by request
     *
     * @param projectId project id
     * @param user user
     * @param reportTypeKey report key
     * @return task started or not
     */
    boolean generateEditorReport(Long projectId, User user, ReportTypeKeyVo reportTypeKey,
            boolean force) throws ServiceException;

    /**
     * publish report action event when report job is done.
     * @param jobCompleted report job completed event
     */
    void finishHtmlToPdfJob(JobCompleted jobCompleted);

}
