package com.bees360.report.service;

import com.bees360.internal.ai.entity.JobData;
import com.bees360.report.entity.ProjectImage;
import com.bees360.entity.vo.ProjectImageOnsiteVo;

import com.bees360.report.core.exception.ServiceException;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/18 12:22
 */
public interface ExternalInterface {

    /*-------------------- Project Start --------------------*/

    void updateDamageSeverity(long projectId, int damageServer);

    /*-------------------- Project End --------------------*/

    /*-------------------- Image Start --------------------*/

    ProjectImage getImageById(String imageId) throws ServiceException;

    List<ProjectImage> listImageByFileSourceType(long projectId, int fileSourceType) throws ServiceException;

    List<ProjectImageOnsiteVo> listOnsiteImages(long projectId, List<Integer> fileSourceTypeList);

    List<ProjectImage> findImageByIds(List<String> imageIds) throws ServiceException;

    List<ProjectImage> listNoDeleteImagesIn(long projectId, List<String> imageIdList) throws ServiceException;

    List<ProjectImage> listImages(long projectId) throws ServiceException;

    void deleteAnnotationImageByIds(long projectId, List<String> imageIdList);

    ProjectImage getNewImageOverview(long projectId) throws ServiceException;

    /*-------------------- Image End --------------------*/

    /*-------------------- History Start --------------------*/

    void addAdStartLog(long projectId, String userId);

    /*-------------------- History End --------------------*/

    /*-------------------- File Start --------------------*/

    String uploadReportFile(String filePath) throws IOException;

    String getReportFilePrefix();

    /*-------------------- File End --------------------*/

    /*-------------------- JobData Start --------------------*/

    void saveJobData(JobData jobData);

    int saveJobDataIfNotExists(JobData jobData);

    /*-------------------- JobData End --------------------*/
}
