package com.bees360.report.service.event.report;

import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.SyncDataToWebJob;
import com.bees360.report.service.ProjectReportFileService;
import com.bees360.report.service.event.Bees360EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Send report data to the web.
 */
@Slf4j
@Component
public class ReportApproveEventHandler implements Bees360EventHandler<ReportApproveEvent> {
    @Autowired
    private ProjectEsService projectEsService;

    @Autowired
    private ProjectReportFileService projectReportFileService;

    @Autowired private JobScheduler jobScheduler;

    @Override
    public void onApplicationEvent(ReportApproveEvent event) {
        String sourceName = event.getSource().getClass().getName();
        String eventName = event.getName();
        long projectId = event.projectId;

        // invoice approved should not trigger transfer
        if (!shouldTransferData(projectId) || ReportTypeEnum.INVOICE.getCode() == event.getReportType()){
            return;
        }

        log.info("Event(name:{}, source: {}, projectId: {}) : Send report information to web background.",
            eventName, sourceName, projectId);

        var job = new SyncDataToWebJob();
        job.setProjectId(projectId);
        jobScheduler.schedule(RetryableJob.of(Job.ofPayload(job), 25, Duration.ofMinutes(1), 1F));
    }

    private boolean shouldTransferData(long projectId){
        ProjectEsModel projectEsModel = projectEsService.findProjectByProjectId(projectId);
        ProjectServiceTypeEnum projectServiceType = ProjectServiceTypeEnum.getEnum(projectEsModel.getServiceType());
        if (projectServiceType == null) {
            return true;
        }
        List<ProjectReportFile> reports = projectReportFileService.findByProjectId(projectId);
        Set<Integer> completedReports = reports.stream().filter(r -> ReportGenerationStatusEnum.getEnum(r.getGenerationStatus()).isCompleted())
            .map(r -> r.getReportType()).collect(Collectors.toSet());

        return projectServiceType.getReportTypes().stream().allMatch(t -> completedReports.contains(t.getCode()));
    }

}
