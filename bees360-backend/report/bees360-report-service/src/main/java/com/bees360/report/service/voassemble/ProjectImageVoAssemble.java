package com.bees360.report.service.voassemble;

import com.bees360.image.ImageNote;
import com.bees360.internal.ai.exchange.ai2client.ProjectImageProto;
import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.ProjectImage;
import com.bees360.report.entity.ProjectImageTag;
import com.bees360.report.grpc.api.report2web.ImageCreatedVo;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.bees360.report.service.util.image.AttributeMessageUtil;
import com.bees360.util.Iterables;
import com.google.protobuf.DoubleValue;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import static com.bees360.report.service.util.image.AttributeMessageUtil.jsonToAttribute;
import static com.bees360.util.Functions.acceptIfNotNull;

/**
 * <AUTHOR> Guanrong
 * @date 2019/08/24 11:38
 */
public class ProjectImageVoAssemble {

    private static ProjectImageProto.ProjectImage toProtoImageItem(ProjectImage image)  {

        // @formatter:off
        ProjectImageProto.ProjectImage.Builder builder = ProjectImageProto.ProjectImage.newBuilder()
            .setImageId(image.getImageId())
            .setProjectId(image.getProjectId())
            .setUserId(image.getUserId())
            .setFileName(StringUtils.trimToEmpty(image.getFileName()))
            .setFileNameMiddleResolution(StringUtils.trimToEmpty(image.getFileNameMiddleResolution()))
            .setFileNameLowerResolution(StringUtils.trimToEmpty(image.getFileNameLowerResolution()))
            .setAnnotationImage(StringUtils.trimToEmpty(image.getAnnotationImage()))
            .setOriginalFileName(StringUtils.trimToEmpty(image.getOriginalFileName()))
            .setImageHeight(image.getImageHeight())
            .setImageWidth(image.getImageWidth())
            .setFileSize(image.getFileSize())
            .setFileSourceType(image.getFileSourceType())
            .setImageType(image.getImageType())
            .setDirection(image.getDirection())
            .setManuallyAnnotated(image.isManuallyAnnotated())
            .setOrientation(Optional.ofNullable(image.getOrientation()).orElse(0))
            .setUploadTime(image.getUploadTime())
            .setDeleted(image.isDeleted())
            .setGpsLocationLatitude(image.getGpsLocationLatitude())
            .setGpsLocationLongitude(image.getGpsLocationLongitude())
            .setPartialType(image.getPartialType())
            .setTiffOrientation(image.getTiffOrientation())
            .setRelativeAltitude(image.getRelativeAltitude())
            .setImageCategory(StringUtils.trimToEmpty(image.getImageCategory()))
            .setIn3DModel(image.getIn3DModel())
            .setShootingTime(image.getShootingTime())
            .setImageSort(Objects.isNull(image.getImageSort()) ? 0 : image.getImageSort())
            .setCategoryTag(Objects.isNull(image.getCategoryTag()) ? 0 : image.getCategoryTag())
            .setObjectTag(Objects.isNull(image.getObjectTag()) ? 0 : image.getObjectTag())
            .setScopeTag(Objects.isNull(image.getScopeTag()) ? 0 : image.getScopeTag())
            .setDirectionTag(Objects.isNull(image.getDirectionTag()) ? 0 : image.getDirectionTag())
            .setLocationTag(Objects.isNull(image.getLocationTag()) ? 0 : image.getLocationTag())
            .setReportTag(Objects.isNull(image.getReportTag()) ? 0 : image.getReportTag())
            .setOrientationTag(Objects.isNull(image.getOrientationTag()) ? 0 : image.getOrientationTag())
            .setFloorLevelTag(Objects.isNull(image.getFloorLevelTag()) ? 0 : image.getFloorLevelTag())
            .setNumberTag(Objects.isNull(image.getNumberTag()) ? 0 : image.getNumberTag())
            .setOriginId(StringUtils.trimToEmpty(image.getParentId()))
            .setCategoryImageTag(toProtoImageTag(image.getCategoryImageTag()))
            .setObjectImageTag(toProtoImageTag(image.getObjectImageTag()))
            .setLocationImageTag(toProtoImageTag(image.getLocationImageTag()))
            .setScopeImageTag(toProtoImageTag(image.getScopeImageTag()))
            .setDirectionImageTag(toProtoImageTag(image.getDirectionImageTag()))
            .setOrientationImageTag(toProtoImageTag(image.getOrientationImageTag()))
            .setReportImageTag(toProtoImageTag(image.getReportImageTag()))
            .setFloorLevelImageTag(toProtoImageTag(image.getFloorLevelImageTag()))
            .setNumberImageTag(toProtoImageTag(image.getNumberImageTag()));

        if (Objects.nonNull(image.getCompass())) {
            builder.setCompass(
                DoubleValue.newBuilder(
                        DoubleValue.newBuilder().setValue(image.getCompass()).build())
                    .build());
        }
        // @formatter:on

        return builder.build();
    }

    private static ProjectImageProto.ImageTag toProtoImageTag(ProjectImageTag imageTag) {
        if (Objects.isNull(imageTag)) {
            return ProjectImageProto.ImageTag.getDefaultInstance();
        }
        var builder = ProjectImageProto.ImageTag.newBuilder();
        acceptIfNotNull(builder::setId, imageTag.getId());
        acceptIfNotNull(builder::setTagId, imageTag.getTagId());
        acceptIfNotNull(builder::setId, imageTag.getId());
        acceptIfNotNull(builder::setAttribute, imageTag.getAttribute(), AttributeMessageUtil::jsonToAttribute);
        return builder.build();
    }

    public static ProjectImageProto.ProjectImageList toProtoImageListAddAnnotations(List<ProjectImage> images,
                                                                                    Map<String, List<ImageAnnotation>> annotations,
                                                                                    Map<String, ImageCreatedVo> imageCreatedMap,
                                                                                    Map<String, Iterable<? extends ImageNote>> imageNoteMap)
         {
            List<ProjectImageProto.ProjectImage> imageList = new ArrayList<>();
            for (ProjectImage image : images) {
                ProjectImageProto.ProjectImage projectImage = toProtoImageItem(image);
                if (MapUtils.isNotEmpty(annotations) && CollectionUtils.isNotEmpty(annotations.get(image.getImageId()))) {
                    projectImage = projectImage.toBuilder()
                        .addAllAnnotations(toImageAnnotationProto(annotations.get(image.getImageId()))).build();
                }
                if (MapUtils.isNotEmpty(imageCreatedMap) && Objects.nonNull(imageCreatedMap.get(image.getImageId()))) {
                    projectImage = projectImage.toBuilder()
                        .setImageCreatedVo(imageCreatedMap.get(image.getImageId()))
                        .build();
                }
                // 如果isEnableFillImageNote关闭， imageNoteMap会为空，不走这里的逻辑
                if (MapUtils.isNotEmpty(imageNoteMap) && imageNoteMap.containsKey(image.getImageId())) {
                    var notes =
                            Iterables.toStream(imageNoteMap.get(image.getImageId()))
                                    .map(ImageNote::getNote)
                                    .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(notes)) {
                        projectImage = projectImage.toBuilder().addAllNote(notes).build();
                    }
                }
                imageList.add(projectImage);
            }
            return ProjectImageProto.ProjectImageList.newBuilder().addAllImages(imageList).build();
    }

    private static List<com.bees360.report.grpc.api.report2web.ImageAnnotation> toImageAnnotationProto(List<ImageAnnotation> annotations) {
        if (CollectionUtils.isEmpty(annotations)) {
            return Collections.emptyList();
        }
        return annotations.stream().map(o -> com.bees360.report.grpc.api.report2web.ImageAnnotation.newBuilder()
            .setAnnotationId(o.getAnnotationId() + "")
            .setAnnotationPolygon(o.getAnnotationPolygon())
            .setAnnotationType(o.getAnnotationType())
            .setCenterPointX(o.getCenterPointX())
            .setCenterPointY(o.getCenterPointY())
            .setCreatedTime(o.getCreatedTime())
            .setFacetId(o.getFacetId())
            .setImageId(o.getImageId())
            .setProjectId(o.getProjectId())
            .setUsageType(o.getUsageType())
            .setSourceType(o.getSourceType())
            .setRemark(Optional.ofNullable(o.getRemark()).orElse(""))
            .setTagName(Optional.ofNullable(o.getTagName()).orElse(""))
            .setCategoryTagName(Optional.ofNullable(o.getCategoryTagName()).orElse(""))
            .setOriginAnnotationId(Optional.ofNullable(o.getOriginAnnotationId()).orElse(""))
            .setAttribute(jsonToAttribute(o.getAttribute()))
            .build()).collect(Collectors.toList());
    }
}
