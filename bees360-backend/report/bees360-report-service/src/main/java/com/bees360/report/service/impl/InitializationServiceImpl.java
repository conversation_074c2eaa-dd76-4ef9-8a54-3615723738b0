package com.bees360.report.service.impl;

import com.bees360.report.service.InitializationService;
import com.bees360.util.DeleteLowImageRunnable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service("initializationServiceImpl")
public class InitializationServiceImpl implements InitializationService {

    @Override
    @PostConstruct
    public void init() {
        startDeleteImagesSchedule();
    }

    private void startDeleteImagesSchedule() {
        ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
        // an hour
        long initDelay = 0;
        // an hour
        long period = 3600000;
        log.info("Start Schedule to delete thumbnail: start after " + initDelay + " ms, and run every hours.");
        Runnable runnable = new DeleteLowImageRunnable();
        executor.scheduleAtFixedRate(runnable, initDelay, period, TimeUnit.MILLISECONDS);
    }

}
