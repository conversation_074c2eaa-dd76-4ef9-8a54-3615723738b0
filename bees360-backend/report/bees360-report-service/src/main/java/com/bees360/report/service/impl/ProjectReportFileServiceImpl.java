package com.bees360.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.bees360.ai.mapper.JobDataMapper;
import com.bees360.base.exception.AccessDatabaseException;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.ReportSummary;
import com.bees360.entity.dto.ProjectReportFileDto;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.internal.ai.entity.JobData;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.entity.consts.AiBotUser;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.vo.ProjectReportFileTinyVo;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.internal.ai.util.date.DateUtil;
import com.bees360.project.Message;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.report.Message.ReportMessage.Resource.Type;
import com.bees360.report.Message.ReportMessage.Status;
import com.bees360.report.Report;
import com.bees360.report.ReportManager;
import com.bees360.report.core.exception.AssertUtil;
import com.bees360.report.core.exception.MessageCode;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.core.exception.ServiceMessageException;
import com.bees360.report.entity.dto.PdfParameterDto;
import com.bees360.report.service.ProjectReportFileService;
import com.bees360.report.service.ReportCheckList;
import com.bees360.report.service.ReportSummaryService;
import com.bees360.report.service.event.project.ReportChangeStatusEvent;
import com.bees360.report.service.event.report.ReportActionEvent;
import com.bees360.report.service.event.report.ReportApproveEvent;
import com.bees360.report.service.event.report.ReportApproveEventHandler;
import com.bees360.resource.ResourcePool;
import com.bees360.user.User;
import com.bees360.util.Iterables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.bees360.internal.ai.entity.enums.JobTypeEnum.HTML_PDF;
import static com.bees360.internal.ai.entity.enums.JobTypeEnum.STATIC_HTML_TO_PDF_JOB;

@Slf4j
@Service("projectReportFileService")
public class ProjectReportFileServiceImpl implements ProjectReportFileService {

    @Autowired private ReportApproveEventHandler reportApproveEventHandler;

    @Autowired private ApplicationEventPublisher applicationEventPublisher;

    @Autowired private ProjectEsService projectEsService;

    @Autowired private ReportCheckList reportCheckList;

    @Autowired private ReportManager reportManager;

    @Autowired private ProjectReportManager projectReportManager;

    @Autowired private ReportSummaryService reportSummaryService;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Autowired private ResourcePool resourcePool;

    @Autowired private ProjectIIManager projectIIManager;

    @Autowired private JobDataMapper jobDataMapper;

    @Override
    public List<ProjectReportFileTinyVo> getReportFiles(long projectId, Integer reportType) {
        List<ProjectReportFile> reportFiles;
        if (reportType == null) {
            reportFiles = findByProjectId(projectId);
        } else {
            reportFiles = List.of(getReportByType(projectId, reportType));
        }
        if (CollectionUtils.isEmpty(reportFiles)) {
            return new ArrayList<>();
        }
        return reportFiles.stream().map(r -> {
            var tinyReport = new ProjectReportFileTinyVo();
            tinyReport.setReportId(r.getReportId());
            tinyReport.setReportType(r.getReportType());
            tinyReport.setReportUrl(r.getReportPdfFileName());
            tinyReport.setRead(true);
            tinyReport.setReportPdfCompressed(r.getReportPdfCompressed());
            tinyReport.setCreatedTime(r.getCreatedTime());
            tinyReport.setSize(r.getSize());
            tinyReport.setSizeCompressed(r.getSizeCompressed());
            tinyReport.setGenerationStatus(r.getGenerationStatus());
            tinyReport.setPaid(false);
            tinyReport.setS3Key(r.getReportPdfFileName());
            tinyReport.setReportId(r.getReportId());
            tinyReport.setFileType(r.getFileType());
            tinyReport.setCreatedBy(r.getCreatedByString());
            return tinyReport;
        }).collect(Collectors.toList());
	}

    @Override
	public void submitReport(long projectId, long userId, String reportId, String aiUserId) throws ServiceException {
		ProjectReportFile report = getReportById(projectId, reportId);
		if(report == null) {
			throw new ServiceException(MessageCode.REPORT_NOT_EXIST);
		}
        var reportTypeCode = report.getReportType();
        // 如果存在正在生成的新报告，不允许 submit 该报告
        checkUnfinishedJobBeforeProcessingReport(projectId, reportTypeCode);

        ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportTypeCode);
		ReportGenerationStatusEnum generationStatus = ReportGenerationStatusEnum.getEnum(report.getGenerationStatus());
        var latestReport = getReportByType(projectId, reportType.getCode());
        if (!latestReport.getReportId().equals(reportId)) {
            throw new ServiceException(MessageCode.REPORT_NOT_EXIST);
        }

        assert generationStatus != null;
        switch(generationStatus) {
            case GENERATED:
            case DISAPPROVED: {
                // 判断报告是否满足提交的提交
                reportCheckList.check(projectId, reportId);
                try {
                    updateReportGenerationStatus(reportId, ReportGenerationStatusEnum.SUBMITTED.getCode(), aiUserId);
                    // 发送状态变更事件
                    applicationEventPublisher.publishEvent(new ReportChangeStatusEvent(this, projectId,
                        AiProjectStatusEnum.REPORT_ASSEMBLED.getCode(), aiUserId, reportType.getCode()));
                    // 发送report节点动作变更事件
                    applicationEventPublisher.publishEvent(new ReportActionEvent(this, projectId,
                        reportType.getCode(), ReportGenerationStatusEnum.SUBMITTED.getCode(), aiUserId));
                } catch (AccessDatabaseException e) {
                    log.error(e.getMessage(), e);
                    throw new ServiceException(MessageCode.DATABASE_EXCEPTION, e);
                }
                report.setGenerationStatus(ReportGenerationStatusEnum.SUBMITTED);
            }
            case SUBMITTED: {
                return;
            }
            case APPROVED: {
                throw new ServiceException(MessageCode.REPORT_BEEN_APPROVED);
            }
            default: {
            }
        }
    }

    @Override
    public void approveReport(long projectId, long userId, String reportId, String aiUserId) throws ServiceException {
        ProjectReportFile report = getReportById(projectId, reportId);
        if (report == null) {
            throw new ServiceException(MessageCode.REPORT_NOT_EXIST);
        }
        ReportGenerationStatusEnum reportStatus = ReportGenerationStatusEnum.getEnum(report.getGenerationStatus());
        var reportTypeCode = report.getReportType();
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportTypeCode);
        var latestReport = getReportByType(projectId, reportType.getCode());
        if (!latestReport.getReportId().equals(reportId)) {
            throw new ServiceException(MessageCode.REPORT_NOT_EXIST);
        }
        // 如果存在正在生成的新报告，不允许 approve 该报告
        checkUnfinishedJobBeforeProcessingReport(projectId, reportTypeCode);

        // 如果是 processor_training clone case，不允许approve report
        if (isProcessorTrainingCloneCase(projectId)) {
            throw new IllegalArgumentException(
                "Cannot approve report because this project is a processor training test case.");
        }

        assert reportStatus != null;
        switch(reportStatus) {
		case GENERATED: {
			throw new ServiceException(MessageCode.REPORT_NOT_SUBMITTED);
		}
		case SUBMITTED: {
            updateReportGenerationStatus(reportId, ReportGenerationStatusEnum.APPROVED.getCode(), aiUserId);
            // 发送report节点动作变更事件
            publishReportApprovedEvent(projectId, aiUserId, reportType);
            return;
		}
		case DISAPPROVED: {
			throw new ServiceException(MessageCode.REPORT_BEEN_DISAPPROVED);
		}
		case APPROVED: {
			return;
		}
		default: {
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
		}
	}

    private boolean isProcessorTrainingCloneCase(long projectId) {
        var projectII = projectIIManager.findById(String.valueOf(projectId));
        var metadata = projectII.getMetadata();
        return metadata.getCloneType() == Message.CloneType.PROCESSOR_TRAINING;
    }

    public void publishReportApprovedEvent(long projectId, String userId, ReportTypeEnum reportType) throws ServiceException {
        var updateTime = Instant.now();
        applicationEventPublisher.publishEvent(new ReportActionEvent(this, projectId,
            reportType.getCode(), ReportGenerationStatusEnum.APPROVED.getCode(), userId));
        reportApproveEventHandler.onApplicationEvent(new ReportApproveEvent(this,
                projectId, reportType.getCode()));
        if (checkProjectReportFileApproved(projectId, reportType.getCode())) {
            var reportChangeStatusEvent = new ReportChangeStatusEvent(this, projectId,
                AiProjectStatusEnum.REVIEWER_APPROVED.getCode(), userId, reportType.getCode());
            reportChangeStatusEvent.setUpdateAt(updateTime);
            applicationEventPublisher.publishEvent(reportChangeStatusEvent);
        }
    }

    private void checkUnfinishedJobBeforeProcessingReport(long projectId, int reportTypeCode) {
        if (bees360FeatureSwitch.isNeedCheckUnfinishedJobBeforeProcessingReport()) {
            // 如果存在（一天内）正在生成的新报告，不允许处理该报告
            long minStartTime =
                    DateUtil.getUSCentralNow()
                            .minusHours(2)
                            .atZone(DateUtil.getUSCentralZoneId())
                            .toInstant()
                            .toEpochMilli();
            var jobTypes = List.of(HTML_PDF.getCode(), STATIC_HTML_TO_PDF_JOB.getCode());
            var jobDataList = jobDataMapper.listProjectUnfinishedJob(projectId, minStartTime, jobTypes);
            JobData unfinishedJob = jobDataList.stream()
                    .filter(
                            jobData -> {
                                var pdfParameter = JSON.parseObject(jobData.getJobParameter(), PdfParameterDto.class);
                                return Objects.equals(pdfParameter.getReportKey(), reportTypeCode);
                            })
                    .findFirst()
                    .orElse(null);
            if (unfinishedJob != null) {
                throw new IllegalArgumentException(
                        "A new report is being generated, and operations on the report are not allowed.");
            }
        }

    }

    @Override
    public boolean checkProjectReportFileApproved(long projectId, Integer reportTypeCode) throws ServiceException {
        log.info("check project report file approved projectId: {} reportType: {}", projectId, reportTypeCode);
        if (reportTypeCode == null) {
            return false;
        }
        var project = projectIIManager.get(String.valueOf(projectId));
        if (Objects.isNull(project) || Objects.isNull(project.getServiceType())) {
            return false;
        }
        log.info("check project report file approved projectIdServiceType: {}", project.getServiceType().getCode());
        ProjectServiceTypeEnum serviceTypeEnum = ProjectServiceTypeEnum.getEnum(project.getServiceType().getCode());
        if (!serviceTypeEnum.containsReport(reportTypeCode)) {
            return false;
        }
        ProjectReportFile reportFile = getReportByType(projectId, reportTypeCode);

        return Objects.nonNull(reportFile) && reportFile.getGenerationStatus() == ReportGenerationStatusEnum.APPROVED.getCode();
    }

    @Override
    @Transactional(rollbackFor = ServiceException.class)
    public ProjectReportFileTinyVo createReportFile(long userId, long projectId,
                                                    ProjectReportFileDto projectReportFileDto, String aiUserId)
    throws ServiceException {
        int reportTypeCode = projectReportFileDto.getReportType();
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportTypeCode);
        AssertUtil.notNull(reportType, "Please select report service.");
        ProjectReportFile newProjectReportFile;
        try {
            newProjectReportFile = createAndInsertProjectReportFile(userId, projectId, projectReportFileDto, aiUserId);

        } catch (Exception e) {
            log.warn("Upload report failed.", e);
            throw new AccessDatabaseException("Upload report failed." + e.getMessage());
        }

        updateReportGenerationStatus(newProjectReportFile.getReportId(),
            ReportGenerationStatusEnum.GENERATED.getCode(), aiUserId);

        // 构建返回结果
        List<ProjectReportFileTinyVo> projectReportFileTinyVos = getReportFiles(projectId, reportTypeCode);

        return projectReportFileTinyVos.stream()
            .filter(r -> r.getReportType() == reportTypeCode)
            .findFirst()
            .orElse(null);
    }

    private ProjectReportFile createAndInsertProjectReportFile(long userId, long projectId, ProjectReportFileDto projectReportFileDto, String aiUserId) {

        ProjectReportFile projectReportFile = new ProjectReportFile();
        projectReportFile.setProjectId(projectId);
        projectReportFile.setCreatedBy(userId);
        projectReportFile.setCreatedTime(System.currentTimeMillis());
        projectReportFile.setDeleted(false);
        projectReportFile.setRead(false);

        projectReportFile.setGenerationStatus(ReportGenerationStatusEnum.GENERATED.getCode());
        projectReportFile.setReportType(projectReportFileDto.getReportType());
        projectReportFile.setReportWordFileName("");
        projectReportFile.setReportPdfFileName(projectReportFileDto.getReportUrl());
        projectReportFile.setSize(projectReportFileDto.getSize());
        projectReportFile.setAiUserId(aiUserId);
        insertReportFile(projectReportFile);

        return projectReportFile;
    }

    @Override
    public void updateReportStatus(long projectId, User user, Integer reportTypeCode, Integer status) throws ServiceException {
        if (Objects.equals(status, ReportGenerationStatusEnum.UPLOADED.getCode())) {
            ProjectEsModel esModel = projectEsService.findProjectByProjectId(projectId);
            var esModelUpdater = ProjectEsModelUpdater.toBuilder().setProjectId(projectId);
            if (Objects.nonNull(esModel)) {
                if (Objects.equals(
                        ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT.getCode(),
                        reportTypeCode)) {
                    esModel.setDarUploadStatus(status);
                    esModelUpdater.setDarUploadStatus(status);
                }
                if (Objects.equals(
                        ReportTypeEnum.PROPERTY_IMAGE_REPORT.getCode(), reportTypeCode)) {
                    esModel.setPirUploadStatus(status);
                    esModelUpdater.setPirUploadStatus(status);
                }
                if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
                    projectEsService.updatePartial(esModelUpdater.build());
                } else {
                    projectEsService.syncToEsFromProjectEsModel(esModel, true);
                }
            }
        } else {
            ReportGenerationStatusEnum generationStatus = ReportGenerationStatusEnum.getEnum(status);
            ProjectReportFile projectReportFile = getReportByType(projectId, reportTypeCode);
            if (Objects.isNull(projectReportFile)) {
                throw new ServiceMessageException(MessageCode.DATA_NON_EXIST, "Report not found");
            }
            if (generationStatus != null) {
                if (Objects.equals(status, ReportGenerationStatusEnum.GENERATED.getCode())) {
                    throw new ServiceMessageException(MessageCode.DATA_NON_EXIST, "Project list not support report generated");
                }
                if (Objects.equals(status, ReportGenerationStatusEnum.SUBMITTED.getCode())) {
                    submitReport(projectId, AiBotUser.AI_ID, projectReportFile.getReportId(), user.getId());
                }
                if (Objects.equals(status, ReportGenerationStatusEnum.APPROVED.getCode())) {
                    approveReport(projectId, AiBotUser.AI_ID, projectReportFile.getReportId(), user.getId());
                }
            }
        }

    }

    @Override
    public void insertReportFile(ProjectReportFile projectReportFile) {
        log.info("create report {} type {}", projectReportFile.getProjectId(), projectReportFile.getReportType());
        var projectId = projectReportFile.getProjectId();
        var reportType = projectReportFile.getReportType();
        var resourceUri = projectReportFile.getReportPdfFileName();
        // summary的获取不应该用这种方式，summary应该在report生成的时候同时生成，这个要在report job重构的时候修改
        var summary =
            Optional.ofNullable(reportSummaryService.getOne(projectId, reportType))
                .map(ReportSummary::getSummary)
                .orElse(ReportSummaryService.NULL_SUMMARY);
        summary = StringUtils.isBlank(summary) ? ReportSummaryService.NULL_SUMMARY : summary;
        var createdBy = Optional.ofNullable(projectReportFile.getAiUserId()).orElse(String.valueOf(projectReportFile.getCreatedBy()));
        var report = projectReportManager.createProjectReport(String.valueOf(projectId),
                String.valueOf(reportType), resourceUri, summary, StringUtils.EMPTY, createdBy);
        projectReportFile.setReportId(report.getId());
    }

    @Override
    public List<ProjectReportFile> findByProjectId(long projectId) {
        var reports = projectReportManager.findAll(String.valueOf(projectId));
        var projectReports = convertReportToAIReports(projectId, reports);
        return filterAiReportFile(projectReports).stream()
            .sorted(Comparator.comparingInt(ProjectReportFile::getReportType))
            .collect(Collectors.toList());
    }

    @Override
    public ProjectReportFile getReportById(long projectId, String reportId) {
        return Optional.ofNullable(reportManager.findById(reportId))
            .map(r -> convertReportToAIReport(projectId, r))
            .orElseThrow(() -> new NoSuchElementException("The report does not exist."));
    }

    @Override
    public ProjectReportFile getReportByType(long projectId, Integer reportType) {
        var reports =
                projectReportManager.find(
                        String.valueOf(projectId), String.valueOf(reportType), null);
        if (com.google.common.collect.Iterables.isEmpty(reports)) {
            return null;
        }
        var resultReports = convertReportToAIReports(projectId, reports);
        return filterAiReportFile(resultReports).get(0);
    }

    private List<ProjectReportFile> filterAiReportFile(List<ProjectReportFile> reportFiles) {
        var filterReports = new ArrayList<ProjectReportFile>();
        if (CollectionUtils.isEmpty(reportFiles)) {
            return filterReports;
        }
        var reportTypeSet = new HashSet<Integer>();
        reportFiles.sort((o1, o2) -> Long.compare(o2.getCreatedTime(), o1.getCreatedTime()));
        for (var report : reportFiles) {
            if (!reportTypeSet.contains(report.getReportType())) {
                filterReports.add(report);
            }
            reportTypeSet.add(report.getReportType());
        }
        return filterReports;
    }

    @Override
    public int updateReportUrl(long projectId, String reportId, String url) {
        log.info("update report {} url {}", reportId, url);
        // 目前这个功能就是重新生成report, 只有pdf
        var report = getReportById(projectId, reportId);
        var metadata = resourcePool.head(url);
        if (metadata == null) {
            throw new IllegalArgumentException("Resource not exists.");
        }
        report.setReportPdfFileName(url);
        report.setSize(Math.toIntExact(metadata.getContentLength()));
        insertReportFile(report);
        return 1;
    }

    private void updateReportGenerationStatus(String reportId, int status, String updatedBy) {
        log.info("update report {} status {}", reportId, status);

        Optional.ofNullable(Status.forNumber(status))
            .ifPresent(value -> reportManager.updateStatus(reportId, value, updatedBy));
    }

    @Override
    public void deleteReport(long projectId, String reportId) {
        log.info("delete report {}", reportId);
        projectReportManager.delete(
                String.valueOf(projectId),
                List.of(reportId),
                String.valueOf(com.bees360.entity.User.AI_ID));
    }

    @Override
    public void deleteOldReport(long projectId) {
        log.info("delete old report {}", projectId);
        var stringProjectId = String.valueOf(projectId);
        var allReports = projectReportManager.findAll(stringProjectId);
        if (com.google.common.collect.Iterables.isEmpty(allReports)) {
            return;
        }
        var reports = convertReportToAIReports(projectId, allReports);
        var reportIds =
                reports.stream().map(ProjectReportFile::getReportId).collect(Collectors.toSet());

        var newReportIds =
                filterAiReportFile(reports).stream()
                        .map(ProjectReportFile::getReportId)
                        .collect(Collectors.toSet());

        reportIds.removeAll(newReportIds);

        if (CollectionUtils.isNotEmpty(reportIds)) {
            var aiUserId = String.valueOf(com.bees360.entity.User.AI_ID);
            projectReportManager.delete(stringProjectId, reportIds, aiUserId);
        }
    }

    private List<ProjectReportFile> convertReportToAIReports(long projectId, Iterable<? extends Report> reports) {
        return Iterables.toStream(reports)
            .filter(report -> report.getResourceUrl().containsKey(Type.ORIGIN))
            .map(report -> convertReportToAIReport(projectId, report))
            .collect(Collectors.toList());
    }

    private ProjectReportFile convertReportToAIReport(long projectId, Report report) {
        if (!report.getResourceUrl().containsKey(Type.ORIGIN)) {
            return null;
        }
        var resourceMap = report.getResource();
        var resourceKeyMap = report.getResourceKey();
        var contentType =
            Optional.ofNullable(resourceMap.get(Type.ORIGIN))
                .map(r -> r.getMetadata().getContentType())
                .orElse("");
        var size =
                Optional.ofNullable(resourceMap.get(Type.ORIGIN))
                        .map(r -> r.getMetadata().getContentLength())
                        .orElse(0L);
        var sizeCompressed =
                Optional.ofNullable(resourceMap.get(Type.COMPRESSED))
                        .map(r -> r.getMetadata().getContentLength())
                        .orElse(0L);

        var reportFile = new ProjectReportFile();
        reportFile.setReportId(report.getId());
        reportFile.setReportType(getReportType(report));
        reportFile.setReportPdfFileName(resourceKeyMap.get(Type.ORIGIN));
        reportFile.setReportPdfCompressed(resourceKeyMap.get(Type.COMPRESSED));
        reportFile.setGenerationStatus(report.getStatus().getNumber());
        reportFile.setRead(true);
        reportFile.setDeleted(false);
        reportFile.setProjectId(projectId);
        reportFile.setCreatedTime(report.getCreatedAt().toEpochMilli());
        reportFile.setSize(Math.toIntExact(size));
        reportFile.setSizeCompressed(Math.toIntExact(sizeCompressed));
        reportFile.setCreatedBy(AiBotUser.AI_ID);
        reportFile.setFileType(contentType);
        reportFile.setCreatedByString(report.getCreatedBy());
        return reportFile;
    }

    public static Integer getReportType(Report report) {
        Integer mysqlType = null;
        var reportTypes = ReportTypeEnum.values();
        for (var type : reportTypes) {
            if (StringUtils.equalsAny(
                    report.getType(),
                    type.name(),
                    type.getShortCut(),
                    type.getDisplay(),
                    String.valueOf(type.getCode()))) {
                mysqlType = type.getCode();
            }
        }
        return mysqlType;
    }
}
