package com.bees360.report.service.config;

import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.pipeline.PipelineService;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.UpdateAnnotationService;
import com.bees360.report.service.job.UpdateInferaOriginAnnotationJobExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import(
    value = {
        AutoRegisterJobExecutorConfig.class,
        InferaJobConfig.class,
    })
public class UpdateInferaOriginAnnotationJobExecutorConfig {

    @Bean
    public UpdateInferaOriginAnnotationJobExecutor updateInferaOriginAnnotationJobExecutor(
            UpdateAnnotationService updateAnnotationService,
            ExternalInterface externalInterface,
            PipelineService pipelineService,
            InferaJobConfig inferaJobConfig) {
        return new UpdateInferaOriginAnnotationJobExecutor(
                updateAnnotationService,
                externalInterface,
                pipelineService,
                inferaJobConfig.getJobNamePipelineTaskKey()
        );
    }
}
