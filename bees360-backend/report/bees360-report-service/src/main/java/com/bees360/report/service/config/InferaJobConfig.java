package com.bees360.report.service.config;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
@Data
@ConfigurationProperties(prefix = "report.infera")
public class InferaJobConfig {
    private Map<String, String> jobNamePipelineTaskKey = new HashMap<>();
}
