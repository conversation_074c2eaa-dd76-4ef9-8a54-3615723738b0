package com.bees360.report.service.job;

import com.bees360.entity.BaseImageAnnotation;
import com.bees360.job.registry.BatchAddImageAnnotationJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.ProjectImage;
import com.bees360.report.entity.enums.AnnotationTagFaceIdEnum;
import com.bees360.report.grpc.api.report2web.AnnotatedImageRequest;
import com.bees360.report.grpc.api.report2web.PointMessage;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.ImageAnnotationTagService;
import com.bees360.report.service.NewImageService;
import com.bees360.user.Message;
import com.bees360.user.User;
import com.bees360.util.CollectionUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.bees360.image.util.AttributeMessageAdapter.jsonToAttribute;


@Slf4j
public class BatchAddImageAnnotationJobExecutor extends AbstractJobExecutor<BatchAddImageAnnotationJob> {

	private final static PointMessage FULL_ANNOTATION_LT_POINT = PointMessage.newBuilder().setX(0).setY(1).build();
	private final static PointMessage FULL_ANNOTATION_RB_POINT = PointMessage.newBuilder().setX(1).setY(0).build();
	private final static PointMessage MYSQL_FULL_ANNOTATION_POINT = PointMessage.newBuilder().setX(0).setY(0).build();

	private final NewImageService imageService;

	private final ImageAnnotationTagService imageAnnotationTagService;

	private final ExternalInterface externalInterface;

	private final List<String> needDeleteSources;

	public BatchAddImageAnnotationJobExecutor(
			NewImageService imageService,
			ImageAnnotationTagService imageAnnotationTagService,
			ExternalInterface externalInterface,
			List<String> needDeleteSources) {
		this.imageService = imageService;
		this.imageAnnotationTagService = imageAnnotationTagService;
		this.externalInterface = externalInterface;
		this.needDeleteSources = needDeleteSources;
		log.info("Created :{} with imageService :{}, imageAnnotationTagService :{}, externalInterface :{}, needDeleteSources :{}.",
				this, this.imageService, this.imageAnnotationTagService, this.externalInterface, this.needDeleteSources);
	}

	@SneakyThrows
	@Override
	protected void handle(BatchAddImageAnnotationJob job) throws IOException {
		log.info("Received AddImageAnnotationJob {}.", job);
		var imageAnnotations = job.getImageAnnotations();
		var userId = job.getUserId();
		if (CollectionUtils.isEmpty(imageAnnotations)) {
			return;
		}

		var userMessage = Message.UserMessage.newBuilder().setId(userId).build();
		User user = User.from(userMessage);

		// 1. delete target source annotation
		log.info("Starting to delete target source annotations for user: {}", userId);
		deleteRelationImageAnnotation(userId, imageAnnotations);

		// 2. add image annotation
		log.info("Starting to add image annotations for user: {}", userId);
		for (BatchAddImageAnnotationJob.AddImageAnnotation imageAnnotation : imageAnnotations) {
			var imageId = imageAnnotation.getImageId();
			var annotations = imageAnnotation.getAnnotations();
			for (BatchAddImageAnnotationJob.Annotation annotation : annotations) {
				var annotatedRequest = convertToAnnotatedImageRequest(annotation);
				imageService.createAnnotatedImage(user, imageId, annotatedRequest);
			}
		}
		log.info("Finished adding image annotations for user: {}", userId);
	}

	private void deleteRelationImageAnnotation(String userId, List<BatchAddImageAnnotationJob.AddImageAnnotation> imageAnnotations) throws ServiceException {
		if (CollectionUtils.isEmpty(imageAnnotations) || CollectionUtils.isEmpty(needDeleteSources)) {
			return;
		}

		var imageIds = imageAnnotations.stream().map(BatchAddImageAnnotationJob.AddImageAnnotation::getImageId).collect(Collectors.toList());
		var projectImages = externalInterface.findImageByIds(imageIds);
		var projectIdToImages = projectImages.stream().collect(Collectors.groupingBy(ProjectImage::getProjectId));
		var allNeedDeleteAnnotationIds = new HashSet<Long>();

		projectIdToImages.forEach((projectId, images) -> {
			var annotations = imageAnnotationTagService.listByProjectIdAndAnnotationTypeAndSourceType(projectId, null, null);
			if (CollectionUtils.isEmpty(annotations)) {
				return;
			}
			imageAnnotationTagService.reloadImageTagDict(annotations);
			var needDeleteAnnotationIds = annotations.stream()
					.filter(a -> StringUtils.isNotBlank(a.getAttribute()) && needDeleteSources.contains(jsonToAttribute(a.getAttribute()).getSource()))
					.map(ImageAnnotation::getAnnotationId)
					.collect(Collectors.toList());
			allNeedDeleteAnnotationIds.addAll(needDeleteAnnotationIds);
		});
		if (CollectionUtils.isNotEmpty(allNeedDeleteAnnotationIds)) {
			log.info("Deleting '{}' annotations for user: {}", allNeedDeleteAnnotationIds, userId);
			imageAnnotationTagService.batchDelete(allNeedDeleteAnnotationIds, userId);
		}
	}

	private AnnotatedImageRequest convertToAnnotatedImageRequest(BatchAddImageAnnotationJob.Annotation annotation) {
		var polygon = annotation.getPolygon();
		var ltPoint = convertToLtPointMessage(polygon);
		var rbPoint = convertToRbPointMessage(polygon);

		int facetId = AnnotationTagFaceIdEnum.NORMAL.getCode();
		if (equalsPointMessage(ltPoint, FULL_ANNOTATION_LT_POINT) && equalsPointMessage(rbPoint, FULL_ANNOTATION_RB_POINT)) {
			facetId = AnnotationTagFaceIdEnum.FULL.getCode();
			ltPoint = MYSQL_FULL_ANNOTATION_POINT;
			rbPoint = MYSQL_FULL_ANNOTATION_POINT;
		}
		return AnnotatedImageRequest.newBuilder()
				.setTagCode(Integer.parseInt(annotation.getTagId()))
				.setFacetId(facetId)
				.setLtPoint(ltPoint)
				.setRbPoint(rbPoint)
				.setConfidenceLevel(BaseImageAnnotation.DEFAULT_CONFIDENCE_LEVEL)
				.setAttribute(annotation.getAttribute())
				.build();
	}

	private boolean equalsPointMessage(PointMessage point1, PointMessage point2) {
		return Objects.equals(point1.getX(), point2.getX())
				&& Objects.equals(point1.getY(), point2.getY());
	}

	private PointMessage convertToLtPointMessage(List<com.bees360.common.Message.PointMessage> polygon) {
		if (CollectionUtils.isEmpty(polygon)) {
			return FULL_ANNOTATION_LT_POINT;
		}
		var pointList = polygon.stream().distinct().collect(Collectors.toList());
		double minX = pointList.stream().mapToDouble(x -> x.getX()).min().getAsDouble();
		double maxY = pointList.stream().mapToDouble(x -> x.getY()).max().getAsDouble();
		return PointMessage.newBuilder().setX(minX).setY(maxY).build();
	}

	private PointMessage convertToRbPointMessage(List<com.bees360.common.Message.PointMessage> polygon) {
		if (CollectionUtils.isEmpty(polygon)) {
			return FULL_ANNOTATION_RB_POINT;
		}
		var pointList = polygon.stream().distinct().collect(Collectors.toList());
		double minY = pointList.stream().mapToDouble(x -> x.getY()).min().getAsDouble();
		double maxX = pointList.stream().mapToDouble(x -> x.getX()).max().getAsDouble();
		return PointMessage.newBuilder().setX(maxX).setY(minY).build();
	}
}
