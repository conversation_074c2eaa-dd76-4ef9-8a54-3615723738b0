package com.bees360.report.service;

import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.vo.ADDeleteParameterVo;
import com.bees360.report.entity.vo.ADParameterVo;
import com.bees360.user.User;

public interface ProjectDamageDetectionService {

	void generateADAnnotations(long projectId, User user, ADParameterVo aDParameter, boolean autoStart)
        throws ServiceException;

    void deleteADAnnotations(long projectId, String userId, ADDeleteParameterVo aDParameterVo) throws ServiceException;
}
