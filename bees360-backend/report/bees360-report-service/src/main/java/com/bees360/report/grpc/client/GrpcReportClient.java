package com.bees360.report.grpc.client;

import com.bees360.common.grpc.GrpcClient;
import com.bees360.common.grpc.GrpcConfig;
import io.grpc.ManagedChannel;

/**
 * <AUTHOR>
 * @date 2019/11/28
 */

public class GrpcReportClient extends GrpcClient {

    private ManagedChannel channel;

    /**
     * 连接远程服务器
     * @param endpoints 服务地址
     *
     */
    public GrpcReportClient(String endpoints) {
        super(endpoints);
        this.channel = buildChannel();
    }

    public GrpcReportClient(GrpcConfig grpcConfig) {
        super(grpcConfig);
    }

    public ManagedChannel getChannel() {
        return channel;
    }

    @Override
    public String toString() {
        return "bees360-report-grpc-client service endpoints=" + getEndpoints();
    }
}
