package com.bees360.report.service.util.image;

import com.bees360.report.grpc.api.report2web.AttributeMessage;
import com.google.gson.Gson;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/** image tag/annotation attribute 字段适配器 */
@Log4j2
public class AttributeMessageUtil {

    public static final String DEFAULT_ATTRIBUTE_JSON = "{}";

    private static final JsonFormat.Parser jsonParser = JsonFormat.parser();
    private static final Gson gson = new Gson();

    private AttributeMessageUtil() {}

    /**
     * transform json string to attributeMessage
     *
     * @param jsonString json string
     * @return attributeMessage
     * @throws IllegalArgumentException jsonString无法被转化为 AttributeMessage
     */
    public static AttributeMessage jsonToAttribute(String jsonString) {
        if (StringUtils.isEmpty(jsonString) || Objects.equals(DEFAULT_ATTRIBUTE_JSON, jsonString)) {
            return AttributeMessage.getDefaultInstance();
        }

        var builder = AttributeMessage.newBuilder();
        try {
            // 使用 JsonFormat 将 JSON 字符串转换为 Protobuf 对象
            jsonParser.merge(jsonString, builder);
        } catch (Exception e) {
            throw new IllegalArgumentException(
                "AttributeMessageUtil Json %s cannot be convert to AttributeMessage.".formatted(gson.toJson(jsonString)), e);
        }
        return builder.build();
    }
}
