package com.bees360.report.service.impl;

import com.bees360.common.resource.ResourceKeyManager;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2019/12/04 14:30
 */
@Slf4j
@Service("fileService")
public class FileServiceImpl implements FileService {

    @Inject
    private ExternalInterface externalInterface;

    @Override
    public String uploadReport(String filePath) throws IOException {
        return externalInterface.uploadReportFile(filePath);
    }

    @Override
    public String createKey(String originFileName, int annotationImage) {
        return ResourceKeyManager.createKey(originFileName, annotationImage);
    }
}
