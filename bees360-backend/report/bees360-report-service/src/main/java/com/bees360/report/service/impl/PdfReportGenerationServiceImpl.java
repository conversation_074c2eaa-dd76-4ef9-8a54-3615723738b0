package com.bees360.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.bees360.ai.mapper.JobDataMapper;
import com.bees360.api.ApiStatus;
import com.bees360.status.Message;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.entity.Project;
import com.bees360.entity.dto.DamageReportParamDto;
import com.bees360.entity.dto.ReportMessageDto;
import com.bees360.common.util.UUIDUtil;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.vo.ReportTypeKeyVo;
import com.bees360.event.EventPublisher;
import com.bees360.event.registry.JobCancel;
import com.bees360.event.registry.JobCompleted;
import com.bees360.internal.ai.entity.JobData;
import com.bees360.internal.ai.entity.enums.AiProcessStageStatusE;
import com.bees360.internal.ai.entity.enums.JobTypeEnum;
import com.bees360.job.HtmlToPdfJob;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.project.invoice.ProjectInvoiceFactory;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.dto.PdfParameterDto;
import com.bees360.report.service.PdfReportGenerationService;
import com.bees360.report.service.ProjectReportService;
import com.bees360.report.service.event.report.ReportActionEvent;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.user.User;
import com.bees360.util.ByteStrings;
import com.google.protobuf.ByteString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.Instant;
import java.util.*;


@Service("PdfReportGenerationService")
@Slf4j
public class PdfReportGenerationServiceImpl implements PdfReportGenerationService {

    @Autowired
    private ProjectReportService projectReportService;

    @Autowired
    private JobScheduler rabbitJobScheduler;

    @Autowired
    private JobDataMapper jobDataMapper;

    @Autowired
    private ResourcePool resourcePool;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private EventPublisher eventPublisher;

    @Override
    public boolean generateReportByHtml(
            long projectId,
            String userId,
            ReportTypeKeyVo reportTypeKey,
            String jsonData,
            boolean force)
            throws ServiceException {
        if (!ReportTypeEnum.exist(reportTypeKey.getReportType())) {
            throw new IllegalArgumentException("Report type does not exist.");
        }

        String htmlKey = reportTypeKey.getKey();
        int reportType = reportTypeKey.getReportType();

        String base64Content = reportTypeKey.getBase64Content();
        if (base64Content != null) {
            uploadHtmlResource(base64Content, htmlKey);
        }

        if (resourcePool.head(htmlKey) == null) {
            log.error(
                    "Generate editor report error. projectId:{}, reportType:{}, htmlKey:{}",
                    projectId,
                    reportType,
                    htmlKey);
            applicationEventPublisher.publishEvent(
                    new ReportActionEvent(
                            this,
                            projectId,
                            reportType,
                            ReportGenerationStatusEnum.FAILED.getCode(),
                            userId));
            return false;
        }

        if (reportType == ReportTypeEnum.INVOICE.getCode()) {
            projectReportService.saveProjectInvoice(String.valueOf(projectId), null,
                htmlKey, reportTypeKey.getInvoiceSummary(), userId);

            applicationEventPublisher.publishEvent(
                new ReportActionEvent(
                    this,
                    projectId,
                    reportTypeKey.getReportType(),
                    ReportGenerationStatusEnum.STARTED.getCode(),
                    userId));
            return true;
        }

        String outputKey = UUIDUtil.getReportUUID() + ".pdf";
        PdfParameterDto pdfParameter =
                PdfParameterDto.builder()
                        .htmlKey(htmlKey)
                        .reportKey(reportType)
                        .outputKey(outputKey)
                        .invoiceSummary(reportTypeKey.getInvoiceSummary())
                        .autoStart(true)
                        .build();

        com.bees360.job.Job job = HtmlToPdfJob.getInstance(htmlKey, outputKey, jsonData);
        if (!replaceOldJob(job.getName(), projectId, reportType, force)) {
            log.info(
                    "Generate editor report task already exists. projectId:{}, reportType:{}, htmlKey:{}",
                    projectId,
                    reportType,
                    htmlKey);
            return false;
        }
        rabbitJobScheduler.schedule(RetryableJob.of(job, 3, Duration.ofMinutes(1), 1F));

        JobData jobData =
                JobData.builder()
                        .projectId(projectId)
                        .pipelineId(1)
                        .userId(userId)
                        .jobId(job.getId())
                        .jobName(job.getName())
                        .jobParameter(JSON.toJSONString(pdfParameter))
                        .resultData(outputKey)
                        .jobType(JobTypeEnum.HTML_PDF.getCode())
                        .status(AiProcessStageStatusE.IN_PROCESS.getCode())
                        .retryCount(0)
                        .startTime(System.currentTimeMillis())
                        .build();
        log.info("htmlToPdfJobData:{}",
            jobData);
        jobDataMapper.save(jobData);

        applicationEventPublisher.publishEvent(
                new ReportActionEvent(
                        this,
                        projectId,
                        reportTypeKey.getReportType(),
                        ReportGenerationStatusEnum.STARTED.getCode(),
                        userId));
        log.info(
                "Generate projectId {} editor report task starts with job {}.",
                projectId,
                job.getId());
        return true;
    }

    @Override
    public boolean generateEditorReport(
            Long projectId, User user, ReportTypeKeyVo reportTypeKey, boolean force) throws ServiceException {
        return generateReportByHtml(projectId, user.getId(), reportTypeKey, null, force);
    }

    /**
     * 前端传递base64Content以及htmlKey时，需要后端上传该html资源
     */
    private void uploadHtmlResource(String base64Content, String htmlKey){

        byte[] content = Base64.getDecoder().decode(base64Content);
        ByteString byteString = ByteString.copyFrom(content);
        var now = Instant.now();
        now = now.minus(Duration.ofNanos(now.getNano())); // remove nano part.

        final ResourceMetadata metadata =
            ResourceMetadata.newBuilder()
                .setContentType("text/html")
                .setContentLength((long) byteString.size())
                .setETag(ByteStrings.computeETag(byteString))
                .setLastModified(now)
                .build();

        resourcePool.put(htmlKey, Resource.of(byteString, metadata));
    }

    private boolean replaceOldJob(String jobName, long projectId, int reportType, boolean force) {
        List<JobData> data = jobDataMapper.listByProjectIdAndName(projectId, jobName);
        if (CollectionAssistant.isEmpty(data)) {
            return true;
        }

        // 存在projectId关联的html_pdf类型job且不需要覆盖 则返回false
        for (JobData jobData : data) {
            // 不同类型的报告job不需要覆盖
            PdfParameterDto pdfParameter = JSON.parseObject(jobData.getJobParameter(), PdfParameterDto.class);
            if (!Objects.equals(pdfParameter.getReportKey(), reportType)) {
                return true;
            }

            if (jobData.getStatus() == AiProcessStageStatusE.IN_PROCESS.getCode() && force) {
                eventPublisher.publish(new JobCancel(jobData.getJobName(), jobData.getJobId(), true));
                jobDataMapper.completedJob(
                    jobData.getJobId(),
                    jobData.getJobName(),
                    AiProcessStageStatusE.STOPPED.getCode());
                log.info("A stop html2pdf job event has been issued. projectId:{}", projectId);
                return true;
            } else if (jobData.getStatus() == AiProcessStageStatusE.IN_PROCESS.getCode() && !force) {
                return false;
            }
        }
        return true;
    }

    @Transactional
    @Override
    public void finishHtmlToPdfJob(JobCompleted jobCompleted){
        String jobId = jobCompleted.getId();
        String jobName = jobCompleted.getName();

        List<JobData> data = jobDataMapper.listByJobIdAndName(jobId, jobName);
        if (CollectionUtils.isEmpty(data)) {
            // 只处理report模块启动的 html to pdf job的Completed event
            log.info("Job {} is not recorded and will not be processed.", jobId);
            return;
        }
        ApiStatus jobStatus = jobCompleted.getStatus();
        JobData jobData = data.get(0);
        PdfParameterDto pdfParameter = JSON.parseObject(jobData.getJobParameter(), PdfParameterDto.class);
        int reportType = pdfParameter.getReportKey();

        log.info("Complete html to pdf job. projectId={}, jobStatus={}", jobData.getProjectId(), jobStatus);

        int statusInt = Objects.equals(jobStatus, ApiStatus.OK)
                ? AiProcessStageStatusE.FINISHED.getCode()
                : AiProcessStageStatusE.FAILED.getCode();
        jobDataMapper.completedJob(jobId, jobName, statusInt);

        if(!Objects.equals(jobStatus.getCode(), Message.StatusMessage.Code.OK)){
            applicationEventPublisher.publishEvent(new ReportActionEvent(this, jobData.getProjectId(),
                reportType, ReportGenerationStatusEnum.FAILED.getCode(), jobData.getUserId()));
            return;
        }

        try {
            DamageReportParamDto reportParam = new DamageReportParamDto();
            reportParam.setProject(new Project(jobData.getProjectId()));
            reportParam.setTime(jobData.getStartTime());
            reportParam.setReportType(ReportTypeEnum.getEnum(reportType));
            reportParam.setAiUserId(jobData.getUserId());
            reportParam.setUserId(com.bees360.entity.User.AI_ID);

            String pdfKey = pdfParameter.getOutputKey();
            ResourceMetadata pdfResource = resourcePool.head(pdfKey);
            if(pdfResource == null){
                applicationEventPublisher.publishEvent(new ReportActionEvent(this, jobData.getProjectId(),
                    reportType, ReportGenerationStatusEnum.FAILED.getCode(), jobData.getUserId()));
                return;
            }
            if (pdfParameter.getReportKey() == ReportTypeEnum.INVOICE.getCode()
                    && pdfParameter.getInvoiceSummary() != null) {
                // 防止前端传入空报告
                var summary = ProjectInvoiceFactory.fromSummary(pdfParameter.getInvoiceSummary());
                if (summary.getInvoice() == null) {
                    log.info("save invoice report failed {}", pdfParameter.getInvoiceSummary());
                    applicationEventPublisher.publishEvent(new ReportActionEvent(this, jobData.getProjectId(),
                        reportType, ReportGenerationStatusEnum.FAILED.getCode(), jobData.getUserId()));
                    return;
                }
            }

            int size = (int) pdfResource.getContentLength().longValue();

            ReportMessageDto reportMessageDto = ReportMessageDto.builder().pdf(pdfKey).size(size).build();
            var result = projectReportService.updateReportMessage(reportParam, reportMessageDto);

            // 这里判断下update后的结果是否为当前job生成的report，不是则代表更新失败直接结束
            if (result.getCreatedTime() != reportParam.getTime()) {
                return;
            }

            if (pdfParameter.getReportKey() == ReportTypeEnum.INVOICE.getCode() && pdfParameter.getInvoiceSummary() != null) {
                try{
                    projectReportService.saveProjectInvoice(String.valueOf(jobData.getProjectId()),
                        pdfKey, null, pdfParameter.getInvoiceSummary(),
                        String.valueOf(com.bees360.entity.User.AI_ID));
                } catch (ServiceException e) {
                    log.error("Failed to generate {} report for {}.",
                        ReportTypeEnum.INVOICE.getCode(), jobData.getProjectId(), e);
                }
            }
        } catch (Exception e) {
            log.error("Finish generating project={} report failed.", jobData.getProjectId(), e);
            applicationEventPublisher.publishEvent(new ReportActionEvent(this, jobData.getProjectId(),
                reportType, ReportGenerationStatusEnum.FAILED.getCode(), jobData.getUserId()));
        }
    }
}
