package com.bees360.report.service.impl;

import com.bees360.entity.ReportSummary;
import com.bees360.internal.ai.common.exceptions.ServiceException;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.report.mapper.ReportSummaryMapper;
import com.bees360.report.service.ReportSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Yang
 */
@Service
@Slf4j
public class ReportSummaryServiceImpl implements ReportSummaryService {

    private static final String REPORT_SUMMARY_HKEY = "bees360:reportSummary:";

    private static final Integer REPORT_SUMMARY_EXPIRE_HOUR = 12;

    @Autowired private RedisTemplate<String, Object> redisTemplate;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Autowired
    private ReportSummaryMapper reportSummaryMapper;

    @Override
    public ReportSummary getOne(long projectId, int reportType) throws ServiceException {
        if (bees360FeatureSwitch.isEnableStagingReportSummaryByRedis()) {
            var reportSummaryKey = getReportSummaryKey(projectId, reportType);
            var exists = redisTemplate.hasKey(reportSummaryKey);
            if (exists != null && exists) {
                var summaryObj = redisTemplate.opsForValue().get(reportSummaryKey);
                if (summaryObj != null) {
                    var summary = new ReportSummary();
                    summary.setProjectId(projectId);
                    summary.setReportType(reportType);
                    summary.setSummary(summaryObj.toString());
                    log.info("Find summary from redis. projectId: {}, reportType: {} ", projectId, reportType);
                    return summary;
                }
            }
        }
        log.info("Find summary from mysql. projectId: {}, reportType: {} ", projectId, reportType);
        return reportSummaryMapper.getOne(projectId, reportType);
    }

    @Override
    public void upsert(ReportSummary summary) throws ServiceException {
        var projectId = summary.getProjectId();
        var reportType = summary.getReportType();
        if (bees360FeatureSwitch.isEnableStagingReportSummaryByRedis()) {
            var reportSummaryKey = getReportSummaryKey(projectId, reportType);
            redisTemplate.opsForValue().set(reportSummaryKey, summary.getSummary());
            redisTemplate.expire(reportSummaryKey, REPORT_SUMMARY_EXPIRE_HOUR, TimeUnit.HOURS);
            log.info("Upsert summary to redis. projectId: {}, reportType: {} ", projectId, reportType);
        }
        ReportSummary oldSummary = reportSummaryMapper.getOne(projectId, reportType);
        try {
            if (oldSummary != null) {
                summary.setId(oldSummary.getId());
                reportSummaryMapper.update(summary);
                return;
            }
            reportSummaryMapper.insert(summary);
        } catch (DuplicateKeyException e) {
            log.warn("error occurred during concurrently modify report summary of project. projectId: {}, reportType: {} ", summary.getProjectId(), summary.getReportType(), e);
            ReportSummary one = reportSummaryMapper.getOne(projectId, reportType);
            summary.setId(one.getId());
            reportSummaryMapper.update(summary);
        } catch (DataIntegrityViolationException | BadSqlGrammarException e) {
            if (bees360FeatureSwitch.isEnableStagingReportSummaryByRedis()) {
                log.info("Report summary too long.: projectId: {}, reportType: {}", projectId, reportType);
            } else {
                throw e;
            }
        }
    }

    private String getReportSummaryKey(long projectId, int reportType) {
        return StringUtils.join(REPORT_SUMMARY_HKEY, projectId, "-", reportType);
    }
}
