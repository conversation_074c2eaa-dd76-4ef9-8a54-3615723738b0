package com.bees360.report.service.listener;

import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.enums.AiProjectStatusEnum;
import com.bees360.internal.ai.entity.enums.ReportTypeEnum;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.report.ReportProvider;

import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.service.ProjectReportService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 监听ReportGroupAdded事件并同步报告组数据到Web端
 */
@Log4j2
@Component
public class SyncDataOnReportGroupAdded extends AbstractNamedEventListener<ReportGroupAdded> {

    private final ProjectEsService projectEsService;

    private final ReportProvider reportProvider;

    private final ProjectReportService projectReportService;

    public SyncDataOnReportGroupAdded(
            ProjectEsService projectEsService,
            ReportProvider reportProvider,
            ProjectReportService projectReportService) {
        this.projectEsService = projectEsService;
        this.reportProvider = reportProvider;
        this.projectReportService = projectReportService;
        log.info(
                "Created {}(projectEsService={}, reportProvider={}, projectReportService={}).",
                this,
                projectEsService,
                reportProvider,
                projectReportService);
    }

    @Override
    public void handle(ReportGroupAdded event) throws IOException {
        log.info("SyncData on ReportGroupAdded event:{}", event.toString());
        if (!DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE.equalsIgnoreCase(
                event.getGroupType())) {
            return;
        }
        var report = reportProvider.get(event.getReportId());
        var reportType = com.bees360.entity.enums.ReportTypeEnum.getReportType(report);
        if (!ReportTypeEnum.needSyncData(ReportTypeEnum.getEnum(reportType.getCode()))) {
            return;
        }
        var projectId = Long.parseLong(event.getGroupKey());
        ProjectEsModel esProject = projectEsService.findProjectByProjectId(projectId);
        if (AiProjectStatusEnum.isComplete(
                AiProjectStatusEnum.getEnum(esProject.getProjectStatus()))) {
            try {
                projectReportService.transferDataToWeb(projectId);
            } catch (ServiceException e) {
                log.error("TransferDataToWeb failed: the ReportGroupAdded event is {}", event, e);
            }
        }
    }
}
