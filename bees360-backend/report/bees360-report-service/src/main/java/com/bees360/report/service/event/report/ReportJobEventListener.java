package com.bees360.report.service.event.report;

import com.bees360.event.registry.JobCompleted;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.internal.ai.entity.enums.JobTypeEnum;
import com.bees360.report.service.PdfReportGenerationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 监听任务完成事件，处理PDF报告生成的逻辑
 */
@Component
public class ReportJobEventListener extends AbstractNamedEventListener<JobCompleted> {

    @Autowired private PdfReportGenerationService pdfReportGenerationService;

    @Override
    public void handle(JobCompleted jobCompleted) {
        String jobName = jobCompleted.getName();
        if (JobTypeEnum.isPdfJob(jobName)) {
            pdfReportGenerationService.finishHtmlToPdfJob(jobCompleted);
        }
    }
}
