package com.bees360.report.service.util.summary;

import com.bees360.report.entity.vo.ReportSummaryVo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ReportSummaryConverter {

    private final ObjectMapper objectMapper;

    public ReportSummaryConverter(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    public ReportSummaryVo toSummaryVo(String summaryJson, Class<? extends ReportSummaryVo> clazz) {
        try {
            return StringUtils.isBlank(summaryJson)? null: objectMapper.readValue(summaryJson, clazz);
        } catch (IOException e) {
            throw new IllegalArgumentException("json can't convert to summary obj.", e);
        }
    }

    public String toSummaryJson(ReportSummaryVo summaryVo) {
        try {
            return Objects.isNull(summaryVo)? "": objectMapper.writeValueAsString(summaryVo);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("summary can't convert to json.", e);
        }
    }
}
