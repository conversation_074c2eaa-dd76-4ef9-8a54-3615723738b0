package com.bees360.report.service;

import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.internal.ai.entity.vo.ProjectReportFileTinyVo;
import com.bees360.report.core.exception.ServiceException;
import java.util.List;

import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.dto.ProjectReportFileDto;
import com.bees360.user.User;

/**
 * report file service
 * <AUTHOR>
 *
 */
public interface ProjectReportFileService {

	List<ProjectReportFileTinyVo> getReportFiles(long projectId, Integer reportType);

    List<ProjectReportFile> findByProjectId(long projectId);

    ProjectReportFile getReportByType(long projectId, Integer reportType);

    int updateReportUrl(long projectId, String reportId, String url);

	void submitReport(long projectId, long userId, String reportId, String aiUserId) throws ServiceException;

	void approveReport(long projectId, long userId, String reportId, String aiUserId) throws ServiceException;

	void publishReportApprovedEvent(long projectId, String userId, ReportTypeEnum reportType) throws ServiceException;

	void deleteReport(long projectId, String reportId);

    ProjectReportFileTinyVo createReportFile(long userId, long projectId, ProjectReportFileDto projectReportFileDto, String aiUserId)
        throws ServiceException;

    void insertReportFile(ProjectReportFile projectReportFile);

    boolean checkProjectReportFileApproved(long projectId, Integer reportTypeCode) throws ServiceException;

    ProjectReportFile getReportById(long projectId, String reportId);

    void updateReportStatus(long projectId, User user, Integer reportId, Integer status) throws ServiceException;

    void deleteOldReport(long projectId);
}
