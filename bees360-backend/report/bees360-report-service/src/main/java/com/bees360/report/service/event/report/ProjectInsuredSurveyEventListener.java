package com.bees360.report.service.event.report;

import com.alibaba.fastjson.JSON;
import com.bees360.commons.lang.time.TimeUtil;
import com.bees360.entity.vo.ReportTypeKeyVo;
import com.bees360.event.registry.FirebaseInsuredSurveyCompleted;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.firebase.domain.InsuredSurvey;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.enums.AiBotUserEnum;
import com.bees360.internal.ai.entity.enums.ReportTypeEnum;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.enums.HomeownerInsuredSurveyEnum;
import com.bees360.report.service.PdfReportGenerationService;
import com.bees360.resource.ResourceUrlProvider;
import com.google.cloud.firestore.DocumentChange;
import com.google.gson.Gson;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 监听FirebaseInsuredSurveyCompleted事件，处理投保调查完成后生成PDF报告的功能。
 */
@Log4j2
@Component
public class ProjectInsuredSurveyEventListener
        extends AbstractNamedEventListener<FirebaseInsuredSurveyCompleted> {

    private static final Gson gson = new Gson();

    @Autowired private PdfReportGenerationService pdfReportGeneratorService;

    @Autowired private ProjectEsService projectEsService;

    @Autowired private ResourceUrlProvider resourceUrlProvider;

    @Override
    public void handle(FirebaseInsuredSurveyCompleted event) {
        if (!DocumentChange.Type.ADDED.equals(event.getType())) {
            return;
        }
        String collectionName = event.getCollectionName();
        if (!StringUtils.equals(InsuredSurvey.COLLECTION_NAME, collectionName)) {
            log.info(
                    "Received document change other than insured survey, collection name: {}",
                    collectionName);
            return;
        }
        List<String> documentsChanged = event.getDocumentsChanged();
        log.info(
                "Received insured survey document change from event app, count: {}",
                documentsChanged.size());

        documentsChanged.stream()
                .map(json -> gson.fromJson(json, InsuredSurvey.class))
                .forEach(
                        insuredSurvey -> {
                            HomeownerInsuredSurveyEnum insuredSurveyType =
                                    HomeownerInsuredSurveyEnum.get(
                                            insuredSurvey.getSurvey().getSurveyId());
                            if (!Objects.equals(
                                            insuredSurvey.getStatus(),
                                            InsuredSurvey.FirebaseInsuredSurveyStatus.SUBMITTED.getCode())
                                    || Objects.isNull(insuredSurveyType)) {
                                return;
                            }

                            long projectId = insuredSurvey.getProjectId();

                            String jsonData =
                                    getInsuredSurveyReportParam(
                                            projectId, insuredSurvey.getSurvey().getAnswers());

                            ReportTypeKeyVo reportTypeKey = new ReportTypeKeyVo();
                            reportTypeKey.setReportType(ReportTypeEnum.HOMEOWNER_SURVEY.getCode());
                            reportTypeKey.setKey(insuredSurveyType.getHtmlTemplateKey());
                            try {
                                pdfReportGeneratorService.generateReportByHtml(
                                    insuredSurvey.getProjectId(),
                                    AiBotUserEnum.AI_NEW_USER_ID.getCode(),
                                    reportTypeKey,
                                    jsonData,
                                    true);
                            } catch (ServiceException e) {
                                log.error("Generate {} report failed.", reportTypeKey, e);
                            }
                        });
    }

    private String getInsuredSurveyReportParam(long projectId, Map<String, String> answers) {
        ProjectEsModel project = projectEsService.findProjectByProjectIdCheckExisted(projectId);
        Map<String, String> parameterMap = new HashMap<>();
        for (Map.Entry<String, String> entry : answers.entrySet()) {
            // 参数key避免纯数字，html转pdf中不支持
            String key =
                    StringUtils.isNumeric(entry.getKey())
                            ? "key-" + entry.getKey()
                            : entry.getKey();
            parameterMap.put(key, entry.getValue());
        }
        parameterMap.put("address", project.getAddress());
        parameterMap.put("city", project.getCity());
        parameterMap.put("state", project.getState());
        parameterMap.put("zipCode", project.getZipCode());
        parameterMap.put("policyNumber", project.getPolicyNumber());

        parameterMap.put(
                "companyLogo",
                Optional.ofNullable(project.getCompanyLogo())
                        .map(key -> resourceUrlProvider.getGetUrl(key).toString())
                        .orElse(StringUtils.EMPTY));
        parameterMap.put(
                "processCompanyLogo",
                Optional.ofNullable(project.getRepairCompanyNameLogo())
                        .map(key -> resourceUrlProvider.getGetUrl(key).toString())
                        .orElse(StringUtils.EMPTY));

        parameterMap.put("year", TimeUtil.format(System.currentTimeMillis(), TimeUtil.YEAR));
        parameterMap.put(
                "time", TimeUtil.format(System.currentTimeMillis(), TimeUtil.NORMAL_FORMAT));

        // 命令解析时{}会被认为是需要替换的参数，所以用'括起来
        return StringUtils.join("'", JSON.toJSONString(parameterMap), "'");
    }
}
