package com.bees360.report.service;

import com.bees360.internal.ai.entity.ImageTagDict;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.ProjectImage;
import com.bees360.report.entity.vo.ImageAnnotationVo;
import com.bees360.report.grpc.api.report2web.AnnotationReportSortParam;
import com.bees360.report.grpc.api.report2web.ImageAnnotationTagCreateRequest;
import com.bees360.report.grpc.api.report2web.ImageAnnotationTagRequest;
import com.bees360.user.User;

import jakarta.annotation.Nonnull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/09/28 16:30
 */
public interface ImageAnnotationTagService {

    void createImageAnnotationTag(String createdBy, String imageId, long projectId, ImageAnnotationTagRequest imageTag) throws ServiceException;

    void setTypeByTagDict(ImageAnnotation anno, ImageTagDict tagDict);

    void deleteImageAnnotation(User user, Set<Long> annotationIds) throws ServiceException;

    List<ImageAnnotation> getImageAnnotationTag(String imageId);

    List<ImageAnnotation> getImageAnnotationTagList(List<String> imageIds);

    ImageAnnotation getImageAnnotationById(String imageId, Long annotationId);

    void reloadImageTagDict(List<ImageAnnotation> annotations);

    List<ImageAnnotationVo> batchCreateImageAnnotationTag(User user, ImageAnnotationTagCreateRequest imageTagReq) throws ServiceException;

    ImageAnnotation addByAnnotationInfo(ImageAnnotation anno, User user, ProjectImage image);

    void batchAdd(List<ImageAnnotation> annotations, String createdBy);

    void batchDelete(Set<Long> annotationIds, String deletedBy);

    List<ImageAnnotation> getByUsageType(List<String> imageIds, Integer usageType);

    void deleteByUsageTypeAndSourceType(String imageId, Integer usageType, String deletedBy);

    void updateImageAnnotationReportSort(User user, String imageId, AnnotationReportSortParam sortParam);

    List<ImageAnnotation> getByOriginAnnotationId(long projectId, String originAnnotationId);

    List<ImageAnnotation> getByOriginAnnotationIds(long projectId, Set<String> originAnnotationIds);

    /**
     * 根据 projectId, tagIds 和 sourceType 获取对应的 annotation
     *
     * @param projectId       项目ID, 不能为空
     * @param annotationTypes ignore if null or empty
     * @param sourceTypes     ignore if null or empty
     * @return
     */
    List<ImageAnnotation> listByProjectIdAndAnnotationTypeAndSourceType(
        @Nonnull Long projectId, List<Integer> annotationTypes, List<Integer> sourceTypes);

    List<ImageAnnotation> getByAnnotationId(List<Long> annotationIds);
}
