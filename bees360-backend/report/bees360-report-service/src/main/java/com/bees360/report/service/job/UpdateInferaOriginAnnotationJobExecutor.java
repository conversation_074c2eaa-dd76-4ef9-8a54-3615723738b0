package com.bees360.report.service.job;

import com.bees360.api.InvalidArgumentException;
import com.bees360.job.registry.UpdateInferaAnnotationJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.ProjectImage;
import com.bees360.report.entity.dto.UpdateOriginAnnotationDto;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.UpdateAnnotationService;
import com.bees360.report.service.util.image.AnnotationConvertUtil;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Log4j2
public class UpdateInferaOriginAnnotationJobExecutor
        extends AbstractJobExecutor<UpdateInferaAnnotationJob> {

    private final UpdateAnnotationService updateAnnotationService;

    private final ExternalInterface externalInterface;

    private final PipelineService pipelineService;

    private final Map<String, String> jobNamePipelineTaskKey;


    public UpdateInferaOriginAnnotationJobExecutor(
            UpdateAnnotationService updateAnnotationService,
            ExternalInterface externalInterface,
            PipelineService pipelineService,
            Map<String, String> jobNamePipelineTaskKey) {
        this.updateAnnotationService = updateAnnotationService;
        this.externalInterface = externalInterface;
        this.pipelineService = pipelineService;
        this.jobNamePipelineTaskKey = jobNamePipelineTaskKey;
        log.info(
                "{} created: {updateAnnotationService={}, externalInterface={},"
                        + " pipelineService={}, jobNamePipelineTaskKey={}}",
                this,
                this.updateAnnotationService,
                this.externalInterface,
                this.pipelineService,
                this.jobNamePipelineTaskKey);
    }

    @Override
    protected void handle(UpdateInferaAnnotationJob refreshInferaAnnotationJob) throws IOException {
        log.info("Start to update infera origin annotation: {}", refreshInferaAnnotationJob);
        // 1. get inferaImages
        var inferaImages = refreshInferaAnnotationJob.getImages();
        if (CollectionUtils.isEmpty(inferaImages)) {
            return;
        }

        // 2. grouping by projectId, annotation -> image -> project
        // 2.1 annotation -> image
        var id2InferaImage =
                inferaImages.stream()
                        .filter(
                                inferaImage ->
                                        Objects.nonNull(inferaImage)
                                                && Objects.nonNull(inferaImage.getImageId()))
                        .collect(
                                Collectors.toMap(
                                        UpdateInferaAnnotationJob.InferaImage::getImageId,
                                        Function.identity(),
                                        (a, b) -> a));
        List<ProjectImage> projectImages;
        try {
            projectImages =
                    externalInterface.findImageByIds(new ArrayList<>(id2InferaImage.keySet()));
        } catch (ServiceException e) {
            log.warn("Update infera annotation error.", e);
            throw new IllegalStateException(
                "Cannot find images by ids: %s, message: %s".formatted(
                    id2InferaImage.keySet(), e.getMessage()));
        }
        if (CollectionUtils.isEmpty(projectImages)) {
            log.warn(
                    "Update infera origin annotation: No images found by ids: {}",
                    id2InferaImage.keySet());
            return;
        }
        // update bbox to absolute value
        updateBboxToAbsoluteValue(projectImages, id2InferaImage);
        // 2.2 image -> project
        // if some imageId in inferaImages but not in projectImages, they will be ignored after next
        // step
        var projectId2InferaImages =
                projectImages.stream()
                        .collect(
                                Collectors.groupingBy(
                                        ProjectImage::getProjectId,
                                        Collectors.mapping(
                                                image -> id2InferaImage.get(image.getImageId()),
                                                Collectors.toList())));
        // 3. process by projectId
        List<Long> errorProjectIds = new ArrayList<>();
        for (var entry : projectId2InferaImages.entrySet()) {
            var projectId = entry.getKey();
            var inferaImagesInProject = entry.getValue();
            log.info(
                    "Update infera origin annotation: start to update annotation. projectId: {}, annotationInfos: {}",
                    projectId,
                    inferaImagesInProject);
            try {
                var annotationInfos = convertToAnnotationInfo(inferaImagesInProject);
                if (CollectionUtils.isNotEmpty(annotationInfos)) {
                    updateAnnotationService.updateOriginAnnotation(
                            UpdateOriginAnnotationDto.newBuilder()
                                    .setProjectId(projectId)
                                    .setAnnotationInfos(annotationInfos)
                                    .build());
                }
                log.info(
                        "Update infera origin annotation: start to set pipeline task status. ProjectID: {}",
                        projectId);
                // update pipeline task status to success
                setPipelineTaskStatus(
                        String.valueOf(projectId),
                        refreshInferaAnnotationJob.getJobName(),
                        Message.PipelineStatus.DONE);
                log.info("Succeed to update origin mapping annotation: projectId {}", projectId);
            } catch (Exception e) {
                log.warn(
                        "update infera annotation error. projectId: {}, error message: {}",
                        projectId,
                        e.getMessage());
                setPipelineTaskStatus(
                        String.valueOf(projectId),
                        refreshInferaAnnotationJob.getJobName(),
                        Message.PipelineStatus.ERROR);
                errorProjectIds.add(projectId);
            }
        }
        if (!errorProjectIds.isEmpty()) {
            throw new IllegalStateException(
                "Update infera origin annotation failed. Error project ids: %s".formatted(
                    errorProjectIds));
        }
    }

    private void updateBboxToAbsoluteValue(
            List<ProjectImage> projectImages,
            Map<String, UpdateInferaAnnotationJob.InferaImage> id2InferaImage) {
        var id2ProjectImage =
                projectImages.stream()
                        .collect(
                                Collectors.toMap(
                                        ProjectImage::getImageId,
                                        Function.identity(),
                                        (a, b) -> a));
        id2InferaImage.forEach(
                (imageId, inferaImage) -> {
                    if (CollectionUtils.isEmpty(inferaImage.getAnnotations())) {
                        return;
                    }
                    var image = id2ProjectImage.get(imageId);
                    if (image == null) {
                        log.warn(
                                "Update infera origin annotations: Cannot find image by id: {}",
                                imageId);
                        return;
                    }
                    var imageWidth = image.getImageWidth();
                    var imageHeight = image.getImageHeight();
                    if (imageWidth == 0 || imageHeight == 0) {
                        log.warn("Update infera origin annotations: image width or height is 0. imageId:{}, width:{}, height:{}",
                                imageId, imageWidth, imageHeight);
                        // Avoid writing dirty data: clear annotations list.
                        inferaImage.getAnnotations().clear();
                        return;
                    }
                    inferaImage
                            .getAnnotations()
                            .forEach(
                                    annotationInfo -> {
                                        annotationInfo.setBbox(
                                                calculateActualBBox(
                                                        annotationInfo.getBbox(), image));
                                    });
                });
    }

    private Double[] calculateActualBBox(Double[] bbox, ProjectImage image) {
        return new Double[] {
            BigDecimal.valueOf(bbox[0])
                    .multiply(BigDecimal.valueOf(image.getImageWidth()))
                    .doubleValue(),
            BigDecimal.valueOf(bbox[1])
                    .multiply(BigDecimal.valueOf(image.getImageHeight()))
                    .doubleValue(),
            BigDecimal.valueOf(bbox[2])
                    .multiply(BigDecimal.valueOf(image.getImageWidth()))
                    .doubleValue(),
            BigDecimal.valueOf(bbox[3])
                    .multiply(BigDecimal.valueOf(image.getImageHeight()))
                    .doubleValue()
        };
    }

    private List<UpdateOriginAnnotationDto.AnnotationInfo> convertToAnnotationInfo(
            List<UpdateInferaAnnotationJob.InferaImage> inferaImages) {
        return inferaImages.stream()
                .flatMap(
                        image -> {
                            if (CollectionUtils.isEmpty(image.getAnnotations())) {
                                return Stream.of();
                            }
                            return image.getAnnotations().stream()
                                    .filter(this::validateInferaAnnotation)
                                    .map(a -> convertToAnnotationInfo(image.getImageId(), a));
                        })
                .collect(Collectors.toList());
    }

    private UpdateOriginAnnotationDto.AnnotationInfo convertToAnnotationInfo(
            String imageId, UpdateInferaAnnotationJob.InferaAnnotation annotation) {
        return UpdateOriginAnnotationDto.AnnotationInfo.newBuilder()
                .setImageId(imageId)
                .setTagId(annotation.getTagId())
                .setBbox(annotation.getBbox())
                .setConfidenceLevel(annotation.getConfidenceLevel())
                .build();
    }

    private boolean validateInferaAnnotation(
            UpdateInferaAnnotationJob.InferaAnnotation annotation) {
        if (Objects.isNull(annotation)
                || Objects.isNull(annotation.getBbox())
                || annotation.getBbox().length != 4) {
            return false;
        }
        for (Double value : annotation.getBbox()) {
            if (Objects.isNull(value)) {
                return false;
            }
        }
        return Objects.nonNull(annotation.getConfidenceLevel())
                && Objects.nonNull(
                        AnnotationConvertUtil.convertSystemTagIdToAnnotationType(
                                annotation.getTagId()));
    }

    private void setPipelineTaskStatus(
            String projectId, String inferaJobName, Message.PipelineStatus target) {
        if (StringUtils.isBlank(inferaJobName)) {
            throw new IllegalArgumentException("Infera job name is null.");
        }
        String pipelineTaskKey = jobNamePipelineTaskKey.get(inferaJobName);
        if (Objects.isNull(pipelineTaskKey)) {
            throw new IllegalStateException("Cannot find pipeline task key by inferaJobName: %s with config: %s".formatted(
                inferaJobName,
                jobNamePipelineTaskKey));
        }
        try {
            pipelineService.setTaskStatus(projectId, pipelineTaskKey, target);
        } catch (InvalidArgumentException | IllegalArgumentException e) {
            log.warn(
                "Project[%s] set task[%s] status[%s] failure: %s".formatted(
                    projectId, pipelineTaskKey, target, e));
        }
    }
}
