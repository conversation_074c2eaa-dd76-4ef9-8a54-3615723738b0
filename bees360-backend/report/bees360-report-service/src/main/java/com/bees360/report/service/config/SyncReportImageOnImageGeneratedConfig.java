package com.bees360.report.service.config;

import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.report.service.job.SyncReportImageOnImageGeneratedJobExecutor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import(
    value = {
        AutoRegisterJobExecutorConfig.class,
        SyncReportImageOnImageGeneratedJobExecutor.class,
    })
@ConditionalOnProperty(
    prefix = "bees360.feature-switch",
    value = "enable-async-generate-annotated-image",
    havingValue = "true")
public class SyncReportImageOnImageGeneratedConfig {}
