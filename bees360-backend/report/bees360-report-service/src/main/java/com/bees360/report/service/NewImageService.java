package com.bees360.report.service;

import com.bees360.internal.ai.exchange.ai2client.ProjectImageProto;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.grpc.api.report2web.AnnotatedImageRequest;
import com.bees360.report.grpc.api.report2web.AnnotatedImageVo;
import com.bees360.report.grpc.api.report2web.ImageAnnotationBatchDeleteRequest;
import com.bees360.report.grpc.api.report2web.ImageCreatedVo;
import com.bees360.report.grpc.api.report2web.ImageEffectedVo;
import com.bees360.report.grpc.api.report2web.ImagesEffectedVo;
import com.bees360.report.grpc.api.report2web.MappingBatchCreateVo;
import com.bees360.report.grpc.api.report2web.MappingCreateParam;
import com.bees360.report.grpc.api.report2web.MappingCreateVo;
import com.bees360.report.grpc.api.report2web.MappingInfo;
import com.bees360.user.User;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/04/02 09:50
 */
public interface NewImageService {

    ImageEffectedVo createAnnotatedImage(User user, String imageId, AnnotatedImageRequest annotatedRequest)
        throws ServiceException;

    ImageCreatedVo delImageAnnotationTag(User user, String imageId, String annotationId, boolean onlyDeleteSelf) throws ServiceException;

    ImageCreatedVo delImageAnnotationTags(User user, String imageId, List<String> annotationIds, boolean onlyDeleteSelf) throws ServiceException;

    ImagesEffectedVo batchDeleteImageAnnotation(User user, ImageAnnotationBatchDeleteRequest imageAnnotations) throws ServiceException;

    ImageCreatedVo createAnnotatedImageVo(String imageId) throws ServiceException;

    ImageEffectedVo refreshGetEffectedImageVo(String imageId) throws ServiceException;

    MappingCreateVo createMappingImage(String imageId, String mappingImageId) throws ServiceException;

    MappingBatchCreateVo createMappingImages(MappingCreateParam createParam) throws ServiceException;

    AnnotatedImageVo fillMappingToOverview(String imageId, MappingInfo mappingInfo, User user)
        throws ServiceException;

    ProjectImageProto.ProjectImageList listImages(long projectId, boolean requireAnnotated) throws ServiceException;

    void delImageAnnotationByTagIds(String id, long projectId, List<Integer> tagIds);
}
