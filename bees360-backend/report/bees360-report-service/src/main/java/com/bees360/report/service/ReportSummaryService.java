package com.bees360.report.service;

import com.bees360.entity.ReportSummary;
import com.bees360.internal.ai.common.exceptions.ServiceException;

/**
 * <AUTHOR>
 */
public interface ReportSummaryService {

    String NULL_SUMMARY = "{}";

    ReportSummary getOne(long projectId, int reportType) throws ServiceException;

    /**
     * 如果存在就更新，否则就插入
     */
    void upsert(ReportSummary summary) throws ServiceException;
}
