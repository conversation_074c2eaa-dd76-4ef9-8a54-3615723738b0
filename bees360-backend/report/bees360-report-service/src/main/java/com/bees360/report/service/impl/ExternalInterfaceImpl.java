package com.bees360.report.service.impl;

import com.bees360.ai.mapper.JobDataMapper;
import com.bees360.common.collections.ListUtil;
import com.bees360.common.grpc.MessageBeanUtil;
import com.bees360.entity.vo.ProjectImageOnsiteVo;
import com.bees360.internal.ai.entity.JobData;
import com.bees360.internal.ai.entity.LogEntryDetail;
import com.bees360.internal.ai.entity.ProjectEsModel;
import com.bees360.internal.ai.entity.ProjectEsModelUpdater;
import com.bees360.internal.ai.entity.enums.LogADActionEnum;
import com.bees360.internal.ai.entity.enums.LogADDetailEnum;
import com.bees360.internal.ai.entity.enums.LogEntryTypeEnum;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectImageManager;
import com.bees360.internal.ai.service.ProjectLogService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.report.core.exception.MessageCode;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.ProjectImage;
import com.bees360.report.service.ExternalInterface;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2019/11/18 12:23
 */
@Slf4j
@Service("externalInterface")
public class ExternalInterfaceImpl implements ExternalInterface {

    @Autowired private ProjectImageManager projectImageManager;

    /** 存取二进制流请用 resourcePool */
    @Resource private ResourcePool resourcePool;

    @Value("${report.resource.prefix}")
    private String reportFileKeyPrefix;

    @Autowired private ProjectEsService projectEsService;

    @Autowired private ProjectLogService projectLogService;

    @Autowired private JobDataMapper jobDataMapper;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Override
    public void updateDamageSeverity(long projectId, int damageServer) {
        ProjectEsModel esModel = projectEsService.findProjectByProjectIdCheckExisted(projectId);
        esModel.setDamageSeverity(damageServer);
        if (bees360FeatureSwitch.isEnablePartiallySyncToEs()) {
            var esModelUpdater =
                    ProjectEsModelUpdater.toBuilder()
                            .setProjectId(projectId)
                            .setDamageSeverity(damageServer)
                            .build();
            projectEsService.updatePartial(esModelUpdater);
        } else {
            projectEsService.syncToEsFromProjectEsModel(esModel, true);
        }
    }

    @Override
    public ProjectImage getImageById(String imageId) throws ServiceException {
        com.bees360.internal.ai.entity.ProjectImage image = projectImageManager.findById(imageId);
        if (Objects.isNull(image)) {
            throw new IllegalArgumentException("Failed to get project image. imageId = " + imageId);
        }
        ProjectImage projectImage = new ProjectImage();
        copyBean(image, projectImage);
        return projectImage;
    }

    @Override
    public List<ProjectImage> listImageByFileSourceType(long projectId, int fileSourceType) throws ServiceException {
        List<com.bees360.internal.ai.entity.ProjectImage> imageList = listAllImagesByProjectId(projectId);
        List<ProjectImage> projectImageList = new ArrayList<>();
        for (com.bees360.internal.ai.entity.ProjectImage image : imageList) {
            if (fileSourceType == image.getFileSourceType() || fileSourceType == -1) {
                ProjectImage projectImage = new ProjectImage();
                copyBean(image, projectImage);
                projectImageList.add(projectImage);
            }
        }
        return projectImageList;
    }

    @Override
    public List<ProjectImageOnsiteVo> listOnsiteImages(long projectId, List<Integer> fileSourceTypeList) {
        List<com.bees360.internal.ai.entity.ProjectImage> imageList = listAllImagesByProjectId(projectId);
        Set<Integer> fileSourceTypeSet = ListUtil.toSet(fileSourceTypeList);
        List<ProjectImageOnsiteVo> projectImageList = new ArrayList<>();
        for (com.bees360.internal.ai.entity.ProjectImage image : imageList) {
            if (fileSourceTypeSet.contains(image.getFileSourceType())) {
                projectImageList.add(modelImageToProjectImageOnsiteVo(image));
            }
        }
        return projectImageList;
    }

    private ProjectImageOnsiteVo modelImageToProjectImageOnsiteVo(com.bees360.internal.ai.entity.ProjectImage image) {
        ProjectImageOnsiteVo projectImage = new ProjectImageOnsiteVo();
        projectImage.setImageId(image.getImageId());
        projectImage.setFileName(image.getFileName());
        projectImage.setFileNameMiddleResolution(image.getFileNameMiddleResolution());
        projectImage.setFileNameLowerResolution(image.getFileNameLowerResolution());
        projectImage.setAnnotationImage(image.getAnnotationImage());
        projectImage.setFileSize(image.getFileSize());
        projectImage.setUserId(image.getUserId());
        projectImage.setUploadTime(image.getUploadTime());
        projectImage.setOriginalFileName(image.getOriginalFileName());
        projectImage.setFileSourceType(image.getFileSourceType());
        projectImage.setGpsLocationLatitude(image.getGpsLocationLatitude());
        projectImage.setGpsLocationLongitude(image.getGpsLocationLongitude());
        //relativeAltitude;
        projectImage.setImageHeight(image.getImageHeight());
        projectImage.setImageWidth(image.getImageWidth());
        projectImage.setProjectId(image.getProjectId());
        projectImage.setDirection(image.getDirection());
        projectImage.setOrientation(image.getOrientation() == null ? 0 : image.getOrientation());
        projectImage.setImageType(image.getImageType());
        projectImage.setImageCategory(image.getImageCategory());
        projectImage.setCamPropertyMatrix(image.getCamPropertyMatrix());
        //deleted
        //manuallyAnnotated
        projectImage.setParentId(image.getParentId());
        projectImage.setPartialType(image.getPartialType());
        projectImage.setShootingTime(image.getShootingTime());
        projectImage.setImageSort(image.getImageSort());
        projectImage.setTiffOrientation(image.getTiffOrientation());
        //2020-06-09 add in3DModel property
        projectImage.setIn3DModel(image.getIn3DModel());
        return projectImage;
    }

    @Override
    public List<ProjectImage> findImageByIds(List<String> imageIds) throws ServiceException {
        List<com.bees360.internal.ai.entity.ProjectImage> imageList =
            Lists.newArrayList(projectImageManager.findAllByIds(imageIds));
        imageList.removeIf(com.bees360.internal.ai.entity.ProjectImage::isCopy);
        Set<String> imageIdSet = ListUtil.toSet(imageIds);
        List<ProjectImage> projectImageList = new ArrayList<>();
        for (com.bees360.internal.ai.entity.ProjectImage image : imageList) {
            if (imageIdSet.contains(image.getImageId())) {
                ProjectImage projectImage = new ProjectImage();
                copyBean(image, projectImage);
                projectImageList.add(projectImage);
            }
        }
        projectImageList.sort(Comparator.comparing(ProjectImage::getOriginalFileName));
        return projectImageList;
    }

    @Override
    public List<ProjectImage> listNoDeleteImagesIn(long projectId, List<String> imageIdList) throws ServiceException {
        List<com.bees360.internal.ai.entity.ProjectImage> imageList = listAllImagesByProjectId(projectId);
        Set<String> imageIdSet = ListUtil.toSet(imageIdList);
        List<ProjectImage> projectImageList = new ArrayList<>();
        for (com.bees360.internal.ai.entity.ProjectImage image : imageList) {
            if (imageIdSet.contains(image.getImageId())) {
                ProjectImage projectImage = new ProjectImage();
                copyBean(image, projectImage);
                projectImageList.add(projectImage);
            }
        }
        projectImageList.sort(Comparator.comparing(ProjectImage::getOriginalFileName));
        return projectImageList;
    }

    @Override
    public List<ProjectImage> listImages(long projectId) throws ServiceException {
        List<com.bees360.internal.ai.entity.ProjectImage> imageList = listAllImagesByProjectId(projectId);
        List<ProjectImage> projectImageList = new ArrayList<>();
        for (com.bees360.internal.ai.entity.ProjectImage image : imageList) {
            ProjectImage projectImage = new ProjectImage();
            copyBean(image, projectImage);
            projectImageList.add(projectImage);
        }
        return projectImageList;
    }

    private List<com.bees360.internal.ai.entity.ProjectImage> listAllImagesByProjectId(long projectId) {
        List<com.bees360.internal.ai.entity.ProjectImage> imageList =
            Lists.newArrayList(projectImageManager.findAllByProjectId(projectId));
        imageList.removeIf(com.bees360.internal.ai.entity.ProjectImage::isCopy);
        return imageList;
    }

    @Override
    public void deleteAnnotationImageByIds(long projectId, List<String> imageIdList) {
        projectImageManager.deleteCompletely(projectId, imageIdList);
    }

    @Override
    public ProjectImage getNewImageOverview(long projectId) throws ServiceException {
        com.bees360.internal.ai.entity.ProjectImage image = projectImageManager.getNewImageOverviewTag(projectId);
        if (Objects.isNull(image)) {
            return null;
        }
        ProjectImage projectImage = new ProjectImage();
        copyBean(image, projectImage);
        return projectImage;
    }

    @Override
    public void addAdStartLog(long projectId, String userId) {
        addLog(projectId, userId, LogEntryTypeEnum.PROJECT_AD_FLOW, LogADDetailEnum.AD_GENERATION,
            LogADActionEnum.STARTED);
    }

    private void addLog(long projectId, String userId, LogEntryTypeEnum logEntryType, LogADDetailEnum logADDetail,
        LogADActionEnum logADAction) {
        projectLogService.addLogEntryByLogEntryType(
            projectId,
            userId,
            logEntryType,
            new LogEntryDetail(logADDetail, logADAction));
    }

    @Override
    public String uploadReportFile(String filePath) throws IOException {
        return doPut(filePath, reportFileKeyPrefix);
    }

    @Override
    public String getReportFilePrefix() {
        return reportFileKeyPrefix;
    }

    @Override
    public void saveJobData(JobData jobData) {
        jobDataMapper.save(jobData);
    }

    @Override
    public int saveJobDataIfNotExists(JobData jobData) {
        return jobDataMapper.saveJobDataIfNotExists(jobData);
    }

    private String doPut(String filePath, String prefix) throws IOException {
        try (InputStream input = new FileInputStream(filePath)) {
            byte[] bytes = com.bees360.common.image.ImageUtil.toByteArray(new File(filePath));
            ByteString byteString = ByteString.copyFrom(bytes);
            ResourceMetadata metadata = ResourceMetadata.extractFrom(input);
            metadata = metadata.toBuilder().setContentType(URLConnection.guessContentTypeFromName(filePath)).build();
            String key = ResourceMetadata.computeKeyFromContentMD5(metadata.getContentMD5());
            if (StringUtils.isNotEmpty(prefix)) {
                key = prefix + key;
            }
            resourcePool.put(key, com.bees360.resource.Resource.of(byteString, metadata));
            return key;
        }
    }

    private void copyBean(Object source, Object destination) throws ServiceException {
        try {
            MessageBeanUtil.copy(source, destination);
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(MessageCode.DATA_FORMAT_ERROR);
        }
    }

}
