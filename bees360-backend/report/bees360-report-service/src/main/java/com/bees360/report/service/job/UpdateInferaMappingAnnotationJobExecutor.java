package com.bees360.report.service.job;

import com.bees360.job.registry.MapInferaAnnotationJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.service.UpdateAnnotationService;

import lombok.extern.log4j.Log4j2;

import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
public class UpdateInferaMappingAnnotationJobExecutor
        extends AbstractJobExecutor<MapInferaAnnotationJob> {

    public UpdateInferaMappingAnnotationJobExecutor(
            UpdateAnnotationService updateAnnotationService) {
    }

    @Override
    protected void handle(MapInferaAnnotationJob mapInferaAnnotationJob) throws IOException {
        // TODO <PERSON>, 需要删除solid job
        log.info("Start to update infera mapping annotation: {}", mapInferaAnnotationJob);
    }
}
