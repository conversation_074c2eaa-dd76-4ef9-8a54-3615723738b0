package com.bees360.report.grpc.client.impl;

import com.bees360.ai.mapper.ProjectImageMapper;
import com.bees360.api.InvalidArgumentException;
import com.bees360.common.collections.ListUtil;
import com.bees360.common.grpc.MessageBeanUtil;
import com.bees360.common.protobuf.ProtoBeanUtils;
import com.bees360.entity.ReportSummary;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.PipelineTaskEnum;
import com.bees360.image.ImageProvider;
import com.bees360.image.tag.ImageTagDictProvider;
import com.bees360.internal.ai.entity.BsExportData;
import com.bees360.internal.ai.entity.ImageTagDict;
import com.bees360.internal.ai.entity.ProjectExportData;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.entity.consts.ExportDataRelatedType;
import com.bees360.internal.ai.entity.consts.ImageTagCodeDict;
import com.bees360.internal.ai.entity.dto.ImageDeletedDto;
import com.bees360.internal.ai.entity.enums.ReportTypeEnum;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass.StatusCodeResponse;
import com.bees360.internal.ai.service.ProjectEsService;
import com.bees360.internal.ai.service.ProjectExportDataManager;
import com.bees360.internal.ai.service.ProjectImageManager;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.report.ReportProvider;
import com.bees360.report.core.filter.ReportFilter;
import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.constant.TagCategory;
import com.bees360.report.entity.enums.AnnotationUsageTypeEnum;
import com.bees360.report.grpc.api.report2web.ExportData;
import com.bees360.report.grpc.api.report2web.ImageDeletedOuterClass.ImageDeleted;
import com.bees360.report.grpc.api.report2web.ProjectReportFileOuterClass.ProjectReportFile;
import com.bees360.report.grpc.api.report2web.ReportAnnotationImageOuterClass.ReportAnnotationImage;
import com.bees360.report.grpc.api.report2web.ReportImageMessageOuterClass.ReportImageMessage;
import com.bees360.report.grpc.api.report2web.ReportProjectMessageOuterClass.ReportProjectMessage;
import com.bees360.report.grpc.api.report2web.ReportSummaryProto;
import com.bees360.report.grpc.api.report2web.UpdateReportServiceGrpc;
import com.bees360.report.grpc.api.report2web.UpdateReportServiceGrpc.UpdateReportServiceFutureStub;
import com.bees360.report.grpc.api.report2web.UpdateReportServiceOuterClass.UpdateReportRequest;
import com.bees360.report.grpc.client.GrpcReportClient;
import com.bees360.report.grpc.client.ReportGrpcClient;
import com.bees360.report.mapper.ImageAnnotationMapper;
import com.bees360.report.mapper.ReportAnnotationImageMapper;
import com.bees360.report.mapper.ReportSummaryMapper;
import com.bees360.report.service.ProjectReportFileService;
import com.bees360.report.service.impl.ProjectReportFileServiceImpl;
import com.bees360.util.Iterables;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Timestamp;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;

import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.bees360.pipeline.Message.PipelineStatus.ONGOING;

/**
 * <AUTHOR>
 * @since 2019/07/25
 */
@Slf4j
@Service("reportGrpcClient")
public class ReportGrpcClientImpl implements ReportGrpcClient {

    @Inject private ProjectReportFileService projectReportFileService;

    @Autowired private ProjectImageMapper projectImageMapper;

    @Autowired private ImageTagDictProvider imageTagDictProvider;

    @Inject private ImageAnnotationMapper imageAnnotationMapper;

    @Inject private ReportAnnotationImageMapper reportAnnotationImageMapper;

    private UpdateReportServiceFutureStub stub;

    @Autowired private GrpcReportClient grpcReportClient;

    @Autowired private ProjectExportDataManager compositedProjectExportDataManager;

    @Autowired private ReportSummaryMapper reportSummaryMapper;

    @Autowired private ProjectEsService projectEsService;

    @Autowired private ReportFilter syncReportFilter;
    @Autowired private PipelineService pipelineService;

    @Autowired private ProjectImageManager projectImageManager;

    @Autowired private ReportProvider reportProvider;

    @Autowired private ImageProvider imageProvider;

    @PostConstruct
    public void initStub() {
        this.stub = UpdateReportServiceGrpc.newFutureStub(grpcReportClient.getChannel());
    }

    @Override
    public void approveReport(long projectId) {
        log.info("ReportApproveService request start projectId: {}", projectId);
        String pipelineId = String.valueOf(projectId);
        String key = PipelineTaskEnum.TRANSFERRED_DATA_TO_WEB.getKey();
        setPipelineTaskStatus(pipelineId, key, ONGOING);

        StatusCodeResponse response;
        deleteOldReport(projectId);
        UpdateReportRequest reportFileRequest = createReportFileRequest(projectId);
        checkAnnotatedImageGenerated(reportFileRequest);
        try {
            response = stub.updateReport(reportFileRequest).get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IllegalStateException(e);
        } catch (ExecutionException e) {
            var message = "Fail to update report for projectId:" + projectId;
            throw new IllegalStateException(message, e);
        }
        assert response != null;

        setPipelineTaskStatus(pipelineId, key, Message.PipelineStatus.DONE);
        log.info(
                "ReportApproveService response for projectId {} response: {} ",
                projectId,
                response);
    }

    private void checkAnnotatedImageGenerated(UpdateReportRequest reportFileRequest) {
        var reportFiles = reportFileRequest.getProjectReportFilesList();
        var annotatedImageReportTypes =
                reportFileRequest.getReportAnnotationImagesList().stream()
                        .map(ReportAnnotationImage::getReportType)
                        .collect(Collectors.toSet());
        for (var reportFile : reportFiles) {
            var report = reportProvider.get(reportFile.getReportId());
            var reportType = ProjectReportFileServiceImpl.getReportType(report);
            var summary = report.getSummary().getSummary();
            var images = getSummaryImages(summary);
            if (CollectionUtils.isEmpty(images)) {
                continue;
            }
            if (!annotatedImageReportTypes.contains(reportType)
                || null == imageProvider.findById(images.get(0).getId())) {
                throw new IllegalStateException(
                    "Project %s report %s data sync failed: The annotated image not generated.".formatted(
                        reportFile.getProjectId(), reportFile.getReportId()));
            }
        }
    }

    private List<com.bees360.report.summary.Message.ReportSummary.Image> getSummaryImages(
            String summary) {
        var imageNodeString = "images";
        var parserBuilder = com.bees360.report.summary.Message.ReportSummary.newBuilder();
        try {
            var summaryJson = JsonParser.parseString(summary).getAsJsonObject();
            if (summaryJson.has(imageNodeString)) {
                var imageJsonObject = new JsonObject();
                imageJsonObject.add(imageNodeString, summaryJson.get(imageNodeString));
                var summaryImageJson = imageJsonObject.toString();
                JsonFormat.parser().ignoringUnknownFields().merge(summaryImageJson, parserBuilder);
            }
        } catch (JsonSyntaxException | InvalidProtocolBufferException e) {
            throw new IllegalStateException(
                "Cannot parse summary in report %s into json: invalid json.".formatted(summary),
                    e);
        }
        return parserBuilder.getImagesList();
    }

    private void setPipelineTaskStatus(String pipelineId, String key, Message.PipelineStatus status){
        try {
            pipelineService.setTaskStatus(pipelineId, key, status);
        } catch (IllegalArgumentException | InvalidArgumentException e){
            log.warn("Failed to set pipeline '{}' task '{}' to '{}'.", pipelineId, key, status, e);
        } catch (RuntimeException e){
            log.error("Failed to set pipeline '{}' task '{}' to '{}'.", pipelineId, key, status, e);
        }
    }

    private void deleteOldReport(long projectId) {
        projectReportFileService.deleteOldReport(projectId);
    }

    private UpdateReportRequest createReportFileRequest(long projectId) {

        ReportProjectMessage.Builder projectMessage = getProjectMessage(projectId);
        List<ProjectReportFile> reportFiles = listReportFile(projectId);
        List<ReportImageMessage> imageMessages = listImageMessages(projectId);
        List<ImageDeleted> imageDeletedList = listDeletedImage(projectId);
        Set<String> imageIdSet = ListUtil.toSet(ReportImageMessage::getImageId, imageMessages);

        List<ReportAnnotationImage> reportAnnotationImages =
                listReportAnnotationImages(projectId, imageIdSet);

        UpdateReportRequest.Builder updateReportRequestBuilder = UpdateReportRequest.newBuilder();

        updateReportRequestBuilder
                .setReportProjectMessage(projectMessage)
                .addAllProjectReportFiles(reportFiles)
                .addAllReportImageMessages(imageMessages)
                .addAllReportAnnotationImages(reportAnnotationImages)
                .addAllImageDeleted(imageDeletedList);

        addExportData(updateReportRequestBuilder, projectId);

        addReportSummary(updateReportRequestBuilder, projectId);

        return updateReportRequestBuilder.build();
    }

    private void addExportData(
            UpdateReportRequest.Builder updateReportRequestBuilder, long projectId) {
        ProjectExportData bsExportData = null;
        for (String projectEditorType : ExportDataRelatedType.PROJECT_EDITOR_TYPES) {
            bsExportData =
                    compositedProjectExportDataManager.getByRelatedIdAndType(
                            projectId + "", projectEditorType);
            if (bsExportData != null) {
                break;
            }
        }
        if (bsExportData == null) {
            log.info("There aren't any export data for project {}.", projectId);
            return;
        }
        ExportData.Builder exportDataBuilder = ExportData.newBuilder();
        try {
            ProtoBeanUtils.toProtoBean(exportDataBuilder, bsExportData);
        } catch (Exception e) {
            log.error(
                    "Fail to convert `" + BsExportData.class + "` to `" + ExportData.class + "`.");
            return;
        }
        updateReportRequestBuilder.setExportData(exportDataBuilder);
    }

    private void addReportSummary(
            UpdateReportRequest.Builder updateReportRequestBuilder, long projectId) {
        List<ReportSummary> reportSummaries = reportSummaryMapper.listByProjectId(projectId);
        reportSummaries.forEach(
                rs -> {
                    ReportSummaryProto.Builder builder = ReportSummaryProto.newBuilder();
                    try {
                        ProtoBeanUtils.toProtoBean(builder, rs);
                    } catch (IOException e) {
                        log.error(
                                "Fail to convert `"
                                        + BsExportData.class
                                        + "` to `"
                                        + ExportData.class
                                        + "`.");
                        return;
                    }
                    updateReportRequestBuilder.addReportSummary(builder.build());
                });
    }

    private List<ReportAnnotationImage> listReportAnnotationImages(
            long projectId, Set<String> imageIdSet) {
        return ListUtil.toList(
                reportAnnotationImage -> imageIdSet.contains(reportAnnotationImage.getImageId()),
                reportAnnotationImage -> {
                    ReportAnnotationImage.Builder annotationImage =
                            convertGrpcEntity(
                                    ReportAnnotationImage::newBuilder, reportAnnotationImage);
                    annotationImage.setCreateTime(
                            convertGrpcEntity(
                                            Timestamp::newBuilder,
                                            reportAnnotationImage.getCreateTime())
                                    .build());
                    return annotationImage.build();
                },
                reportAnnotationImageMapper.listByProjectId(projectId));
    }

    private List<ReportImageMessage> listImageMessages(long projectId) {
        Iterable<ProjectImage> imageRecords = projectImageManager.findProjectImages(projectId, null, null, false);
        Map<String, Integer> reportTypeMap =
                reportAnnotationImageMapper.listByProjectId(projectId).stream()
                        .collect(
                                Collectors.toMap(
                                        com.bees360.entity.ReportAnnotationImage::getImageId,
                                        com.bees360.entity.ReportAnnotationImage::getReportType));
        Set<String> originalImagesSet =
                Iterables.toStream(imageRecords)
                        .map(ProjectImage::getParentId)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());

        var tagCodeMap =
                Iterables.toStream(imageTagDictProvider.getAll())
                        .map(
                                t -> {
                                    var tag = new ImageTagDict();
                                    tag.setId(Long.valueOf(t.getId()));
                                    tag.setCode(Integer.valueOf(t.getId()));
                                    tag.setName(t.getTitle());
                                    return tag;
                                })
                        .collect(Collectors.toMap(ImageTagDict::getCode, a -> a, (k1, k2) -> k1));

        List<ImageAnnotation> imageTagAnnotations =
                imageAnnotationMapper.listAnnotationTagByProjectIdAndSourceType(projectId, null);
        Map<String, List<Integer>> imageIdComponentTagCodeMap =
                ListUtil.toGroupMap(
                        ImageAnnotation::getImageId,
                        ImageAnnotation::getAnnotationType,
                        imageTagAnnotations.stream()
                                .filter(
                                        imageAnnotation ->
                                                AnnotationUsageTypeEnum.COMPONENT.getCode()
                                                        == imageAnnotation.getUsageType())
                                .collect(Collectors.toList()));

        return Iterables.toStream(imageRecords)
                .filter(image -> !image.isCopy())
                .map(
                        image -> {
                            String tagDescription = getTagDescription(image, tagCodeMap, imageIdComponentTagCodeMap);
                            String tagCategory;
                            if (originalImagesSet.contains(image.getImageId())) {
                                tagCategory = "";
                                tagDescription = StringUtils.EMPTY;
                            } else {
                                ReportTypeEnum reportTypeEnum = null;
                                if (reportTypeMap.containsKey(image.getImageId())) {
                                    int reportType = reportTypeMap.get(image.getImageId());
                                    reportTypeEnum = ReportTypeEnum.getEnum(reportType);
                                }
                                tagCategory = getTagCategory(image, reportTypeEnum, tagCodeMap);
                            }
                            return convertGrpcEntity(ReportImageMessage::newBuilder, image)
                                    .setTagCategory(tagCategory)
                                    .setTagDescription(tagDescription)
                                    .build();
                        })
                .collect(Collectors.toList());
    }

    private List<ImageDeleted> listDeletedImage(long projectId) {
        List<ImageDeletedDto> imageDeletedDtoList =
                projectImageMapper.listDeletedImages(
                        projectId,
                        Arrays.asList(
                                FileSourceTypeEnum.DRONE_IMAGE.getCode(),
                                FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode()));

        return ListUtil.toList(
                imageDeleted ->
                        ImageDeleted.newBuilder()
                                .setImageId(imageDeleted.getImageId())
                                .setDeleteStatus(imageDeleted.getDeleteStatus())
                                .build(),
                imageDeletedDtoList);
    }

    private String getTagDescription(
            ProjectImage image,
            Map<Integer, ImageTagDict> tagCodeMap,
            Map<String, List<Integer>> imageIdComponentTagCodeMap) {
        String object = getTagName(image.getObjectTag(), tagCodeMap);
        String direction = getTagName(image.getDirectionTag(), tagCodeMap);
        String category = getTagName(image.getCategoryTag(), tagCodeMap);
        String location = getTagName(image.getLocationTag(), tagCodeMap);
        String component =
                getComponentString(image.getImageId(), tagCodeMap, imageIdComponentTagCodeMap);
        String scope = getTagName(image.getScopeTag(), tagCodeMap);
        return object + direction + category + location + component + scope;
    }

    private String getComponentString(
            String imageId,
            Map<Integer, ImageTagDict> tagCodeMap,
            Map<String, List<Integer>> imageIdComponentTagCodeMap) {
        StringBuilder componentString = new StringBuilder();
        if (imageIdComponentTagCodeMap.containsKey(imageId)) {
            for (Integer tag : new HashSet<>(imageIdComponentTagCodeMap.get(imageId))) {
                componentString.append(getTagName(tag, tagCodeMap));
            }
        }
        return componentString.toString();
    }

    private String getTagName(Integer tagCode, Map<Integer, ImageTagDict> tagCodeMap) {
        return Optional.ofNullable(tagCodeMap.get(tagCode)).map(s -> s.getName() + ". ").orElse("");
    }

    private String getTagCategory(
            ProjectImage image, ReportTypeEnum reportType, Map<Integer, ImageTagDict> tagCodeMap) {
        if (image.getObjectTag() == null && image.getCategoryTag() == null) {
            return "";
        }
        if (!tagCodeMap.containsKey(image.getObjectTag())
                && !tagCodeMap.containsKey(image.getCategoryTag())) {
            return "";
        }
        if (ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT.equals(reportType)) {
            return TagCategory.ROOF;
        }
        if (ReportTypeEnum.POST_CONSTRUCTION_AUDIT_REPORT.equals(reportType) || hasPCATag(image, tagCodeMap)) {
            // Images with PRR and not used for report
            if (image.getFileSourceType() != FileSourceTypeEnum.REPORT_IMAGE.getCode()) {
                return TagCategory.REINSPECTION_OTHER_CLOSEUP;
            }

            // Images with PRR and used for report
            if (image.isRoof()) {
                return TagCategory.REINSPECTION_ROOF;
            } else if (hasInteriorTag(image)) {
                return TagCategory.REINSPECTION_INTERIORS;
            } else if (objectAndCategoryNotAllEmpty(image)) {
                return TagCategory.REINSPECTION_ELEVATIONS;
            }
        }
        if (image.isRoof() && hasCloseupTag(image, tagCodeMap)) {
            return TagCategory.ROOF_OTHER_CLOSEUP;
        }
        if (ReportTypeEnum.PROPERTY_IMAGE_REPORT.equals(reportType)) {
            if (hasElevationTag(image) || hasAddressTag(image, tagCodeMap)) {
                return TagCategory.ELEVATIONS;
            }
            if (hasInteriorTag(image)) {
                return TagCategory.INTERIORS;
            }
            if (objectAndCategoryNotAllEmpty(image)) {
                return TagCategory.OTHER_STRUCTURE;
            }
        }
        if (objectAndCategoryNotAllEmpty(image)) {
            return TagCategory.OTHER_IMAGES;
        }
        return "";
    }

    private boolean objectAndCategoryNotAllEmpty(ProjectImage image) {
        return image.getObjectTag() != null || image.getCategoryTag() != null;
    }

    private boolean hasCloseupTag(ProjectImage image, Map<Integer, ImageTagDict> tagCodeMap) {
        Integer scopeTag =
                Optional.ofNullable(tagCodeMap.get(image.getScopeTag()))
                        .map(ImageTagDict::getCode)
                        .orElse(null);
        return Objects.equals(scopeTag, ImageTagCodeDict.CLOSEUP);
    }

    private boolean hasElevationTag(ProjectImage image) {
        return Objects.equals(image.getCategoryTag(), ImageTagCodeDict.ELEVATION);
    }

    private boolean hasInteriorTag(ProjectImage image) {
        return Objects.equals(image.getCategoryTag(), ImageTagCodeDict.INTERIOR);
    }

    private boolean hasAddressTag(ProjectImage image, Map<Integer, ImageTagDict> tagCodeMap) {
        Integer objectTag =
                Optional.ofNullable(tagCodeMap.get(image.getObjectTag()))
                        .map(ImageTagDict::getCode)
                        .orElse(null);
        return Objects.equals(objectTag, ImageTagCodeDict.ADDRESS_VERIFICATION);
    }

    private boolean hasPCATag(ProjectImage image, Map<Integer, ImageTagDict> tagCodeMap) {
        Integer reportTag =
                Optional.ofNullable(tagCodeMap.get(image.getReportTag()))
                        .map(ImageTagDict::getCode)
                        .orElse(null);
        return Objects.equals(reportTag, ImageTagCodeDict.PCA);
    }

    private List<ProjectReportFile> listReportFile(long projectId) {
        List<ProjectReportFile> reportFiles =
                ListUtil.toList(
                        reportFile ->
                                convertGrpcEntity(ProjectReportFile::newBuilder, reportFile)
                                        .build(),
                    projectReportFileService.findByProjectId(projectId));
        return ListUtil.filter(
                report -> syncReportFilter.filter(report.getReportType()), reportFiles);
    }

    private ReportProjectMessage.Builder getProjectMessage(long projectId) {
        return convertGrpcEntity(
                ReportProjectMessage::newBuilder,
                projectEsService.findProjectByProjectIdCheckExisted(projectId));
    }

    private <T, G extends GeneratedMessageV3.Builder<G>> G convertGrpcEntity(
            Supplier<G> supplier, T t) {
        G g = supplier.get();
        try {
            MessageBeanUtil.copyBeanPropertiesToMessage(t, g, g.getDescriptorForType());
        } catch (InvocationTargetException | IllegalAccessException | NoSuchMethodException e) {
            log.error(
                    "bees360-ai ReportGrpcClientImpl.getConvertGrpcEntity convert data, data：" + g,
                    e);
        }
        return g;
    }
}
