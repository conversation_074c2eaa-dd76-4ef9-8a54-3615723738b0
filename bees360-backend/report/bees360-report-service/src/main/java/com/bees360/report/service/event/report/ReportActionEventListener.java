package com.bees360.report.service.event.report;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.api.InvalidArgumentException;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.internal.ai.entity.LogEntryDetail;
import com.bees360.internal.ai.entity.enums.LogEntryTypeEnum;
import com.bees360.internal.ai.entity.enums.LogReportActionEnum;
import com.bees360.internal.ai.entity.enums.LogReportDetailEnum;
import com.bees360.internal.ai.entity.enums.ReportTypeEnum;
import com.bees360.internal.ai.service.ProjectLogService;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.pipeline.PipelineService;
import com.bees360.user.Message.UserMessage;
import com.google.gson.Gson;
import com.google.protobuf.util.Timestamps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/04/30 10:22
 */
@Slf4j
@Component
public class ReportActionEventListener {

    @Autowired private ProjectLogService projectLogService;
    @Autowired private ActivityManager activityManager;

    @Autowired
    private PipelineService pipelineService;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Value("#{'${report.log-entry.special-report-type}'}")
    private Set<com.bees360.entity.enums.ReportTypeEnum> specialReportTypes;

    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void addLogOnReportActionEvent(ReportActionEvent event) {
        log.info("handleEventOnReportActionEvent event:{}", new Gson().toJson(event));
        int reportType = event.getReportType();
        LogReportDetailEnum detailEnum = LogReportDetailEnum.getEnumByReportType(reportType);
        if (Objects.nonNull(detailEnum) && !specialReportTypes.contains(com.bees360.entity.enums.ReportTypeEnum.getEnum(reportType))) {
            if (event.getReportGenerationStatus()
                == ReportGenerationStatusEnum.GENERATED.getCode()) {
                return;
            }
            projectLogService.addLogEntryByLogEntryType(
                    event.getProjectId(),
                    event.getUserId(),
                    LogEntryTypeEnum.REPORT,
                    new LogEntryDetail(
                            detailEnum,
                            getActionEnumByReportStatus(event.getReportGenerationStatus())));
        }
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void addActivityOnReportActionEvent(ReportActionEvent event) {
        if (event.getReportGenerationStatus() == ReportGenerationStatusEnum.GENERATED.getCode()) {
            return;
        }
        log.info("AddActivityOnReportActionEvent event:{}", new Gson().toJson(event));
        ReportTypeEnum reportTypeEnum = ReportTypeEnum.getEnum(event.getReportType());
        if (reportTypeEnum == null) {
            return;
        }
        UserMessage userMessage = UserMessage.newBuilder().setId(event.getUserId()).build();
        activityManager.submitActivity(
                Activity.of(
                        Message.ActivityMessage.newBuilder()
                                .setAction(Message.ActivityMessage.ActionType.CHANGE.name())
                                .setProjectId(event.getProjectId())
                                .setEntity(
                                        Message.ActivityMessage.Entity.newBuilder()
                                                .setId(reportTypeEnum.getCode() + "")
                                                .setCount(1)
                                                .setType(Message.ActivityMessage.EntityType.REPORT.name())
                                                .build())
                                .setField(
                                        Message.ActivityMessage.Field.newBuilder()
                                                .setType(Message.ActivityMessage.FieldType.STRING.name())
                                                .setName(Message.ActivityMessage.FieldName.STATUS.name())
                                                .setValue(
                                                        getActionEnumByReportStatus(
                                                                        event
                                                                                .getReportGenerationStatus())
                                                                .getValue())
                                                .build())
                                .setCreatedBy(userMessage)
                                .setCreatedAt(Timestamps.fromMillis(System.currentTimeMillis()))
                                .setSource(ActivitySourceEnum.AI.getValue())
                                .build()));
    }

    private LogReportActionEnum getActionEnumByReportStatus(int status) {
        return LogReportActionEnum.getEnum(status);
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void setPipelineTaskOnReportActionEvent(ReportActionEvent event) {
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(event.getReportType());
        ReportGenerationStatusEnum reportStatus =
            ReportGenerationStatusEnum.getEnum(event.getReportGenerationStatus());
        if (reportStatus == ReportGenerationStatusEnum.GENERATED) {
            return;
        }
        log.info("SetPipelineTaskOnReportActionEvent event:{}", new Gson().toJson(event));
        if (reportType == null || reportStatus == null) {
            return;
        }

        var taskDefKey = getTaskDefKey(reportType, reportStatus);
        if (StringUtils.isEmpty(taskDefKey)) {
            return;
        }

        String pipelineId = String.valueOf(event.getProjectId());
        PipelineStatus status = PipelineStatus.DONE;
        try {
            if (Objects.equals(reportStatus, ReportGenerationStatusEnum.FAILED)) {
                status = PipelineStatus.ERROR;
            } else if (Objects.equals(reportStatus, ReportGenerationStatusEnum.STARTED)){
                status = PipelineStatus.ONGOING;
            }

            pipelineService.setTaskStatus(pipelineId, taskDefKey, status);
            log.info("Set pipeline {} task {} into status {} by report action event {}.",
                pipelineId, taskDefKey, status, event);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Failed to set pipeline {} task {} to '{}'.", pipelineId, taskDefKey, status, e);
        } catch (RuntimeException e){
            log.error("Failed to set pipeline '{}' task '{}' to '{}'.", pipelineId, taskDefKey, status, e);
        }
    }

    private String getTaskDefKey(
            ReportTypeEnum reportType, ReportGenerationStatusEnum reportStatus) {
        com.bees360.entity.enums.ReportTypeEnum reportTypeEnum =
                com.bees360.entity.enums.ReportTypeEnum.getEnum(reportType.getCode());
        String type = null;
        switch (reportStatus) {
            case STARTED:
            case GENERATED:
            case FAILED:
                type = "generate";
                break;
            case SUBMITTED:
                type = "submit";
                break;
            case APPROVED:
                type = "approve";
                break;
            default:
                break;
        }
        String taskDefKey = null;
        if (type != null && reportTypeEnum != null && reportTypeEnum.getShortCut() != null) {
            String suffix = reportTypeEnum.getShortCut().toLowerCase();
            taskDefKey = String.join("_", type, suffix);
        }
        return taskDefKey;
    }
}
