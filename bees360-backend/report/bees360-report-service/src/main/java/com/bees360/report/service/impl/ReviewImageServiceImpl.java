package com.bees360.report.service.impl;

import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.entity.dto.ImageAnnotationsDto;
import com.bees360.entity.dto.Point;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.vo.ProjectImageOnsiteVo;
import com.bees360.image.ImageNote;
import com.bees360.image.ImageNoteProvider;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.ReviewImageService;
import com.bees360.util.Iterables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/11/22 10:07
 */
@Slf4j
@Service("reviewImageService")
public class ReviewImageServiceImpl implements ReviewImageService {

    @Inject
    private ExternalInterface externalInterface;

    @Autowired private ImageNoteProvider imageNoteProvider;

    @Override
    public Map<String, Object> getReviewImages(long projectId, ReportTypeEnum reportType) throws ServiceException {
        Map<String, Object> result = new HashMap<>();
        List<ProjectImageOnsiteVo> projectImageList = new ArrayList<>();
        result.put("images", projectImageList);
        if(reportType == null) {
            return result;
        }
        List<Integer> types = ListUtil.toList(FileSourceTypeEnum::getCode, reportType.listFileSourceTypesForReport());

        projectImageList = externalInterface.listOnsiteImages(projectId, types);
        if (CollectionAssistant.isEmpty(projectImageList)) {
            return result;
        }

        fillImageNote(projectImageList);

        for (ProjectImageOnsiteVo vo : projectImageList) {
            if (vo.getImageSort() == null || vo.getImageSort() == 0L) {
                vo.setImageSort(vo.getShootingTime());
            }
            vo.setMapping2d(new Point());
            vo.setFacetIds(new ArrayList<>());

            vo.setAnnotations(new ImageAnnotationsDto());
            vo.getAnnotations().setDamages(new ArrayList<>());

            vo.getAnnotations().setFrames(new ArrayList<>());
            vo.setAnnotationTypes(new HashSet<>());
            vo.setScreenshots(new ArrayList<>());

            List<Map<String, Object>> pointMapList = new ArrayList<>();
            vo.setMapping(pointMapList);
            vo.setArea("0");
        }
        // Assemble the data.    end
        result.put("images", projectImageList);
        return result;
    }

    private void fillImageNote(List<ProjectImageOnsiteVo> projectImageList) {
        var imageIds = projectImageList.stream().map(ProjectImageOnsiteVo::getImageId).collect(Collectors.toList());
        var imageNoteMap = imageNoteProvider.findByImageIds(imageIds);
        for (var image : projectImageList) {
            if (MapUtils.isNotEmpty(imageNoteMap) && imageNoteMap.containsKey(image.getImageId())) {
                var notes =
                    Iterables.toStream(imageNoteMap.get(image.getImageId()))
                        .map(ImageNote::getNote)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(notes)) {
                    image.setNote(notes);
                }
            }
        }
    }
}
