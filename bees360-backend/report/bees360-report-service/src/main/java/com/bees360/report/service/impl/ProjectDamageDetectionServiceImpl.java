package com.bees360.report.service.impl;

import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.OrientationEnum;
import com.bees360.image.ImageAnnotationManager;
import com.bees360.image.ImageTagEnum;
import com.bees360.internal.ai.entity.JobData;
import com.bees360.internal.ai.entity.consts.ImageTagCodeDict;
import com.bees360.internal.ai.entity.enums.AiProcessStageStatusE;
import com.bees360.internal.ai.entity.enums.JobTypeEnum;
import com.bees360.job.CommandJob;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.report.core.exception.MessageCode;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.ProjectImage;
import com.bees360.report.entity.vo.ADDeleteParameterVo;
import com.bees360.report.entity.vo.ADParameterVo;
import com.bees360.report.mapper.ImageAnnotationMapper;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.ProjectDamageDetectionService;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.inject.Inject;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service("projectDamageDetectionService")
public class ProjectDamageDetectionServiceImpl implements ProjectDamageDetectionService {

    private static final String ADD_RESULT_KEY =
        "projects/{projectId}/add/{jobId}/autoDamageResult.json";

    private static final String AD_JOB_ID_TEMPLATE = "{type}-{projectId}-{jobId}";

	@Inject
	private ExternalInterface externalInterface;

	@Inject
	private ImageAnnotationMapper imageAnnotationMapper;

    @Autowired private ImageAnnotationManager imageAnnotationManager;

    @Autowired private JobScheduler rabbitJobScheduler;

    @Autowired private Function<String, String> threeDJobNamePrefixProvider;

    private @Value("${ad.enabled:true}") boolean adEnabled;

    @Override
    public void generateADAnnotations(
            long projectId, com.bees360.user.User user, ADParameterVo aDParameter, boolean autoStart)
            throws ServiceException {
	    for (Integer fileSourceType : aDParameter.getFileSourceTypes()) {
	        if (Objects.isNull(FileSourceTypeEnum.getEnum(fileSourceType))) {
                throw new ServiceException(MessageCode.IMAGE_SOURCE_TYPE_NNOT, "fileSourceType not exist.");
            }
        }
        // 保证可以手动强制重启
        if (!adEnabled && !aDParameter.isForcedStart()) {
            log.info(
                    "Ad disabled and not forced to start. projectId:{}, userId:{}, param:{}",
                    projectId,
                    user.getId(),
                    aDParameter);
            return;
        }

        List<ProjectImage> images = listAdImages(projectId, aDParameter);

        externalInterface.addAdStartLog(projectId, user.getId());

        startADDJob(projectId, user, images, aDParameter, autoStart);
    }

    private void startADDJob(
            long projectId,
            com.bees360.user.User user,
            List<ProjectImage> images,
            ADParameterVo aDParameter,
            boolean autoStart) {
        long pipelineId = System.currentTimeMillis();
        JobTypeEnum jobType = JobTypeEnum.ADD;

        var jobNamePrefix = threeDJobNamePrefixProvider.apply(String.valueOf(projectId));
        var jobName = jobNamePrefix + jobType.getName();

        String autoDamageResultJsonKey =
                ADD_RESULT_KEY
                        .replace("{projectId}", String.valueOf(projectId))
                        .replace("{jobId}", String.valueOf(pipelineId));
        log.info("Start damage detection. result key is:" + autoDamageResultJsonKey);
        StringBuilder imagesKeyId = new StringBuilder();
        CommandJob.Builder adCommandJobBuilder =
                CommandJob.newBuilder()
                        .setId(
                                AD_JOB_ID_TEMPLATE
                                        .replace("{type}", jobType.getDisplay())
                                        .replace("{projectId}", String.valueOf(projectId))
                                        .replace("{jobId}", String.valueOf(pipelineId)))
                        .setName(jobName)
                        .setTimeout(Duration.ofHours(2))
                        .addOutputArgument(autoDamageResultJsonKey);
        AtomicInteger index = new AtomicInteger(1);
        images.forEach(
                image -> {
                    adCommandJobBuilder.addInputArgument(image.getFileName());
                    imagesKeyId.append(",,{").append(index).append("}").append(",")
                        .append(image.getImageId());
                    index.getAndIncrement();
                });
        String imagesKeyIdString = imagesKeyId.substring(2);
        String command = "python3 /var/bees360/add/app/AD.py  -f --images_path=$PWD --save_json={0}";
        command = command + " --images_key_id=" + imagesKeyIdString;
        command = command + " --count=" + aDParameter.getCount();
        command = command + " --level=" + aDParameter.getLevel();
        CommandJob adCommandJob =
                adCommandJobBuilder
                        .setBashCommand(command)
                        .build();

        RetryableJob adRetryableJob = RetryableJob.of(adCommandJob, 3, Duration.ofMinutes(1), 1F);

        aDParameter.setImageIds(ListUtil.toList(ProjectImage::getImageId, images));
        JobData jobData =
                JobData.builder()
                        .projectId(projectId)
                        .pipelineId(pipelineId)
                        .userId(user.getId())
                        .jobId(adRetryableJob.getId())
                        .jobName(adRetryableJob.getName())
                        .jobParameter(
                                com.alibaba.fastjson.JSONObject.toJSON(aDParameter).toString())
                        .resultData(autoDamageResultJsonKey)
                        .jobType(jobType.getCode())
                        .status(AiProcessStageStatusE.IN_PROCESS.getCode())
                        .retryCount(0)
                        .startTime(System.currentTimeMillis())
                        .build();
        // 如果是自动启动，只会启动一次
        if (autoStart) {
            var count = externalInterface.saveJobDataIfNotExists(jobData);
            if (count != 1) {
                log.info("Project {} has automatically started AD.", projectId);
                return;
            }
        } else {
            externalInterface.saveJobData(jobData);
        }
        rabbitJobScheduler.schedule(adRetryableJob);
    }

    private List<ProjectImage> listAdImages(long projectId, ADParameterVo aDParameter)
            throws ServiceException {
        if (CollectionAssistant.isNotEmpty(aDParameter.getImageIds())) {
            List<ProjectImage> images =
                    externalInterface.listNoDeleteImagesIn(projectId, aDParameter.getImageIds());
            if (CollectionAssistant.isEmpty(images)) {
                throw new ServiceException(
                        MessageCode.NO_IMAGE_TO_START_AD, "There is no image to run ad.");
            }
            return images;
        }
        List<ProjectImage> images = new ArrayList<>();
        for (int fileSourceType : aDParameter.getFileSourceTypes()) {
            if (!FileSourceTypeEnum.exist(fileSourceType)
                    || !FileSourceTypeEnum.getEnum(fileSourceType).canAD()) {
                throw new ServiceException(MessageCode.IMAGE_CANNOT_START_AD, "");
            }
            List<ProjectImage> imageList = externalInterface.listImageByFileSourceType(projectId, fileSourceType);
            if (Objects.equals(fileSourceType, FileSourceTypeEnum.DRONE_IMAGE.getCode())) {
                imageList =
                        ListUtil.filter(
                                image ->
                                        Objects.equals(
                                                image.getScopeTag(), ImageTagCodeDict.CLOSEUP),
                                imageList);
            }
            images.addAll(imageList);
        }
        if (CollectionAssistant.isEmpty(images)) {
            throw new ServiceException(MessageCode.NO_IMAGE_TO_START_AD, "There is no image to run ad.");
        }
        images =
                images.stream()
                        .filter(
                                image ->
                                        Objects.nonNull(
                                                OrientationEnum.getEnum(image.getOrientation())))
                        .collect(Collectors.toList());
        if (CollectionAssistant.isEmpty(images)) {
            throw new ServiceException(MessageCode.NO_IMAGE_TO_START_AD, "There is no image to run ad.");
        }
        return images;
    }

    @Override
    public void deleteADAnnotations(
            long projectId, String userId, ADDeleteParameterVo aDDeleteParameter)
            throws ServiceException {
        if (aDDeleteParameter.getLevel() <= 0 || aDDeleteParameter.getLevel() > 1) {
            throw new ServiceException(
                    MessageCode.PARAM_INVALID, "The confidence level must be between 0 and 1.");
        }
        if (CollectionAssistant.isNotEmpty(aDDeleteParameter.getDamageTypes())) {
            for (int damageType : aDDeleteParameter.getDamageTypes()) {
                if (Objects.isNull(ImageTagEnum.valueOf(damageType))) {
                    throw new ServiceException(
                            MessageCode.PARAM_INVALID, "The annotation type not exists.");
                }
            }
        }
        log.info(
                "{} delete AD annotations. projectId:{} param:{}",
                userId,
                projectId,
                aDDeleteParameter);
        deleteADAnnotations(projectId, aDDeleteParameter, userId);
    }

    private void deleteADAnnotations(
            long projectId, ADDeleteParameterVo aDDeleteParameter, String deletedBy) {
        var aDAnnotations =
                imageAnnotationMapper.findADAnnotations(projectId, aDDeleteParameter);
        var annotationIds =
                aDAnnotations.stream()
                        .map(a -> String.valueOf(a.getAnnotationId()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(annotationIds)) {
            imageAnnotationManager.deleteAll(annotationIds, deletedBy);
        }
        imageAnnotationMapper.deleteADAnnotations(projectId, aDDeleteParameter);
    }
}
