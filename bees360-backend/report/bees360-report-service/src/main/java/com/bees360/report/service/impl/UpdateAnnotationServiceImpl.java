package com.bees360.report.service.impl;

import com.bees360.common.collections.ListUtil;
import com.bees360.entity.enums.AnnotationSourceTypeEnum;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.ImageTagManager;
import com.bees360.internal.ai.entity.consts.AiBotUser;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.ProjectImage;
import com.bees360.report.entity.dto.UpdateOriginAnnotationDto;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.ImageAnnotationTagService;
import com.bees360.report.service.UpdateAnnotationService;
import com.bees360.report.service.config.ServiceBeanConfig;
import com.bees360.report.service.util.image.AnnotationConvertUtil;
import com.bees360.util.geom.GeometryUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.transaction.Transactional;

@Slf4j
public class UpdateAnnotationServiceImpl implements UpdateAnnotationService {

    private final ExternalInterface externalInterface;

    private final ImageAnnotationTagService imageAnnotationTagService;

    private final ImageTagManager imageTagManager;

    private final Bees360FeatureSwitch bees360FeatureSwitch;

    private final ServiceBeanConfig.DeduplicationProperties deduplicationProperties;

    public UpdateAnnotationServiceImpl(
        ExternalInterface externalInterface,
        ImageAnnotationTagService imageAnnotationTagService,
        ImageTagManager imageTagManager,
        Bees360FeatureSwitch bees360FeatureSwitch,
        ServiceBeanConfig.DeduplicationProperties deduplicationProperties) {
        this.externalInterface = externalInterface;
        this.imageAnnotationTagService = imageAnnotationTagService;
        this.imageTagManager = imageTagManager;
        this.bees360FeatureSwitch = bees360FeatureSwitch;
        this.deduplicationProperties = deduplicationProperties;
        log.info(
                "Created {}(externalInterface={}, imageAnnotationTagService={}, imageTagManager={}, "
                        + "bees360FeatureSwitch={}, deduplicationProperties={}).",
                this,
                externalInterface,
                imageAnnotationTagService,
                imageTagManager,
                bees360FeatureSwitch,
                deduplicationProperties);
    }

    /**
     * update closeup annotations
     *
     * <ol>
     *   <li>delete old infera, infera_mapping annotations
     *   <li>save {@code annotationInfos}
     * </ol>
     *
     * @param annotationDto new annotations
     */
    @Override
    @Transactional(rollbackOn = Exception.class)
    public void updateOriginAnnotation(UpdateOriginAnnotationDto annotationDto)
            throws ServiceException {
        log.info("Update origin annotation: start with data: {}", annotationDto);
        var annotationInfos = annotationDto.getAnnotationInfos();
        var projectId = annotationDto.getProjectId();
        // 1. Convert annotations
        var annotations = AnnotationConvertUtil.transferAnnotation(projectId, annotationInfos);

        // 2. Collect annotation IDs to delete
        var annotationIdsToDelete =
                collectAnnotationIdsToDeleteWhenUpdateCloseup(projectId, annotations);

        // 3. Delete old annotations
        imageAnnotationTagService.batchDelete(annotationIdsToDelete, AiBotUser.AI_NEW_USER_ID);

        // Delete Edge Annotations
        if (bees360FeatureSwitch.isEnableDeleteEdgeAnnotation()) {
            annotations = removeEdgeAnnotation(annotations);
        }
        if (CollectionUtils.isEmpty(annotations)) {
            return;
        }

        // 4. Add new annotations
        imageAnnotationTagService.batchAdd(annotations, AiBotUser.AI_NEW_USER_ID);

        log.info("Update origin annotation: start to add DAR tag. projectId: {}", projectId);

        // 5. Add DAR tag
        addDarTag(annotations);

        log.info(
                "Update origin annotation: start to generate annotationImage. projectId: {}",
                projectId);
    }

    /**
     * Delete the annotation for the edge.
     *
     * @param annotations annotation list
     * @return Annotations after removing edges.
     */
    public List<ImageAnnotation> removeEdgeAnnotation(List<ImageAnnotation> annotations) {
        var imageIds = annotations.stream().map(ImageAnnotation::getImageId).collect(Collectors.toList());
        List<ProjectImage> projectImages;
        try {
            projectImages = externalInterface.findImageByIds(imageIds);
        } catch (ServiceException e) {
            log.warn("Update infera annotation error.", e);
            throw new IllegalStateException(
                "Cannot find images by ids: %s, message: %s".formatted(
                    imageIds, e.getMessage()));
        }
        var imageIdMap = ListUtil.toMap(ProjectImage::getImageId, projectImages);
        return annotations.stream().filter(a -> {
            if (!imageIdMap.containsKey(a.getImageId())) {
                return false;
            }
            var image = imageIdMap.get(a.getImageId());
            return !checkEdgeAnnotation(a, image);
        }).collect(Collectors.toList());
    }

    private boolean checkEdgeAnnotation(ImageAnnotation annotation, ProjectImage image) {
        var imageWidth = image.getImageWidth();
        var imageHeight = image.getImageHeight();
        var polygon = GeometryUtil.parseGeometryText(annotation.getAnnotationPolygon());
        var minX = polygon.get(0).getX();
        var minY = polygon.get(0).getY();
        var maxX = polygon.get(0).getX();
        var maxY = polygon.get(0).getY();

        for (var point : polygon) {
            minX = Math.min(minX, point.getX());
            minY = Math.min(minY, point.getY());
            maxX = Math.max(maxX, point.getX());
            maxY = Math.max(maxY, point.getY());
        }
        var widthRange = imageWidth * deduplicationProperties.getEdgeAnnotationRatio();
        var heightRange = imageHeight * deduplicationProperties.getEdgeAnnotationRatio();
        return minX < widthRange
            || minY < heightRange
            || (imageWidth - maxX) < widthRange
            || (imageHeight - maxY) < heightRange;
    }

    /**
     * get all infera, infera_mapping annotations from the project
     *
     * @return annotations' id
     */
    private Set<Long> collectAnnotationIdsToDeleteWhenUpdateCloseup(
            Long projectId, List<ImageAnnotation> annotations) {
        var previousAnnotations =
                imageAnnotationTagService.listByProjectIdAndAnnotationTypeAndSourceType(
                        projectId,
                        annotations.stream()
                                .map(ImageAnnotation::getAnnotationType)
                                .distinct()
                                .collect(Collectors.toList()),
                        List.of(
                                AnnotationSourceTypeEnum.INFERA.getCode(),
                                AnnotationSourceTypeEnum.INFERA_MAPPING.getCode()));
        return previousAnnotations.stream()
                .map(ImageAnnotation::getAnnotationId)
                .collect(Collectors.toSet());
    }

    private void addDarTag(List<ImageAnnotation> annotations) {
        Map<String, Iterable<String>> imageId2Tags =
                annotations.stream()
                        .map(ImageAnnotation::getImageId)
                        .distinct()
                        .collect(
                                Collectors.toMap(
                                        String::valueOf,
                                        x -> List.of(String.valueOf(ImageTagEnum.DAR.getCode()))));
        imageTagManager.addAllImageTag(imageId2Tags, AiBotUser.AI_NEW_USER_ID);
    }
}
