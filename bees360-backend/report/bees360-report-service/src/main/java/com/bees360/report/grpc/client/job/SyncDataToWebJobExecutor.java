package com.bees360.report.grpc.client.job;

import com.bees360.job.registry.SyncDataToWebJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.report.grpc.client.ReportGrpcClient;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

@Log4j2
public class SyncDataToWebJobExecutor extends AbstractJobExecutor<SyncDataToWebJob> {

    private final ReportGrpcClient reportGrpcClient;

    public SyncDataToWebJobExecutor(@NonNull ReportGrpcClient reportGrpcClient) {
        this.reportGrpcClient = reportGrpcClient;
        log.info("Created {}(reportGrpcClient={})", this, reportGrpcClient);
    }

    @Override
    protected void handle(SyncDataToWebJob job) throws IOException {
        reportGrpcClient.approveReport(job.getProjectId());
    }
}
