package com.bees360.report.service.event.project;

import com.bees360.internal.ai.service.ProjectEsService;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/04/30 10:22
 */
@Slf4j
@Component
public class ReportChangeStatusListener {

    @Autowired
    private ProjectEsService projectEsService;

    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void handleEventOnProjectStatusEvent(ReportChangeStatusEvent event) {
        log.info("handleEventOnProjectStatusEvent updateProjectStatus event:{}", new Gson().toJson(event));

        long projectId = event.getProjectId();
        var updateTime = Optional.ofNullable(event.getUpdateAt())
                .map(Instant::toEpochMilli)
                .orElse(Instant.now().toEpochMilli());
        try {
            projectEsService.updateProjectStatusOnReportChange(
                    projectId, event.getUserId(),
                    event.getNewStatusCode(),
                    event.getReportTypeCode(),
                    updateTime);
        } catch (Exception e) {
            log.error("handleEventOnProjectStatusEvent updateProjectStatus failed. projectId:"
                + projectId , e);
        }

    }



}
