package com.bees360.report.service.config;

import com.bees360.image.ImageTagManager;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.ImageAnnotationTagService;
import com.bees360.report.service.UpdateAnnotationService;
import com.bees360.report.service.impl.UpdateAnnotationServiceImpl;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ServiceBeanConfig {
    @Data
    @Configuration
    @EnableConfigurationProperties
    @ConfigurationProperties(value = "image.annotation.deduplication")
    @ToString
    public static class DeduplicationProperties {
        private double intersectVariate = 0.15;
        private double confidenceDegradationParam = 0.001;
        private int dipAngle = 155;
        private String annotationProcessingUserId = "10000";
        private double precisionScale = 0.2;
        private double edgeAnnotationRatio = 0.05;
    }

    @Bean
    public UpdateAnnotationService updateAnnotationService(
            ExternalInterface externalInterface,
            ImageAnnotationTagService imageAnnotationTagService,
            ImageTagManager imageTagManager,
            Bees360FeatureSwitch bees360FeatureSwitch,
            DeduplicationProperties deduplicationProperties) {
        return new UpdateAnnotationServiceImpl(
                externalInterface,
                imageAnnotationTagService,
                imageTagManager,
                bees360FeatureSwitch,
                deduplicationProperties);
    }
}
