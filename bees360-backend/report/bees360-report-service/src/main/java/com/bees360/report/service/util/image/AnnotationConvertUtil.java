package com.bees360.report.service.util.image;

import com.bees360.entity.enums.AnnotationSourceTypeEnum;
import com.bees360.image.ImageTagEnum;
import com.bees360.internal.ai.entity.consts.AiBotUser;
import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.dto.UpdateOriginAnnotationDto;
import com.bees360.report.entity.enums.AnnotationUsageTypeEnum;
import com.bees360.util.idWorker.IdWorkerInstancer;
import com.google.common.base.Preconditions;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class AnnotationConvertUtil {

    private AnnotationConvertUtil() {}

    /**
     * Convert annotationType to tagId <br>
     * https://wiki.9realms.co/zh/Backend/Bees360Report%E6%95%B0%E6%8D%AE%E5%BA%93%E8%A1%A8%E8%AF%B4%E6%98%8E
     *
     * @param annotationType annotationType
     * @return null if not found
     */
    public static String convertAnnotationTypeToSystemTagId(Integer annotationType) {
        return Optional.ofNullable(ImageTagEnum.valueOf(annotationType))
                .map(t -> String.valueOf(t.getCode()))
                .orElse(null);
    }

    /**
     * Convert tagId to annotationType <br>
     * https://wiki.9realms.co/zh/Backend/Bees360Report%E6%95%B0%E6%8D%AE%E5%BA%93%E8%A1%A8%E8%AF%B4%E6%98%8E
     *
     * @param tagId tagId
     * @return null if not found
     */
    public static Integer convertSystemTagIdToAnnotationType(String tagId) {
        return Optional.ofNullable(ImageTagEnum.valueOf(Integer.parseInt(tagId)))
                .map(ImageTagEnum::getCode)
                .orElse(null);
    }

    /**
     * Convert tagId to annotationUsageType <br>
     *
     * @param tagId tagId
     * @return null if not found
     */
    public static Integer convertSystemTagIdToUsageType(String tagId) {
        var imageTagEnum = ImageTagEnum.valueOf(Integer.parseInt(tagId));
        if (imageTagEnum == null) {
            return null;
        }
        switch (imageTagEnum.getCategory()) {
            case COMPONENT:
                return AnnotationUsageTypeEnum.COMPONENT.getCode();
            case DAMAGE:
                return AnnotationUsageTypeEnum.DAMAGE_ANNOTATION.getCode();
            case FEEDBACK:
                return AnnotationUsageTypeEnum.FEEDBACK.getCode();
            case CREDIT:
                return AnnotationUsageTypeEnum.CREDIT.getCode();
            case HAZARD:
                return AnnotationUsageTypeEnum.HAZARD.getCode();
            default:
                return AnnotationUsageTypeEnum.TAG.getCode();
        }
    }


    public static List<ImageAnnotation> transferAnnotation(
            Long projectId, List<UpdateOriginAnnotationDto.AnnotationInfo> annotationInfos) {
        if (CollectionUtils.isEmpty(annotationInfos)) {
            return new ArrayList<>();
        }
        var idWorker = IdWorkerInstancer.getIdWorkerInstance();

        return annotationInfos.stream()
                .map(
                        annotationInfo -> {
                            var bbox = annotationInfo.getBbox();
                            Preconditions.checkArgument(
                                    Objects.nonNull(bbox) && bbox.length == 4,
                                "Cannot transfer bbox to polygon, bbox: %s".formatted(
                                    Optional.ofNullable(bbox)
                                        .map(Arrays::toString)
                                        .orElse(null)));
                            ImageAnnotation annotation = new ImageAnnotation();
                            var annotationId = idWorker.nextId();
                            annotation.setAnnotationId(annotationId);
                            annotation.setAnnotationType(
                                    convertSystemTagIdToAnnotationType(annotationInfo.getTagId()));
                            annotation.setAnnotationPolygon(transferBBoxToPolygonString(bbox));
                            var centerX =
                                    (annotationInfo.getBbox()[0] + annotationInfo.getBbox()[2]) / 2;
                            var centerY =
                                    (annotationInfo.getBbox()[1] + annotationInfo.getBbox()[3]) / 2;
                            annotation.setCenterPointX(centerX);
                            annotation.setCenterPointY(centerY);
                            annotation.setConfidenceLevel(annotationInfo.getConfidenceLevel());
                            annotation.setImageId(annotationInfo.getImageId());
                            annotation.setOriginAnnotationId(String.valueOf(annotationId));
                            annotation.setUsageType(convertSystemTagIdToUsageType(annotationInfo.getTagId()));
                            annotation.setSourceType(AnnotationSourceTypeEnum.INFERA.getCode());
                            annotation.setReportSort(Integer.MIN_VALUE);
                            annotation.setProjectId(projectId);
                            annotation.setCreatedTime(System.currentTimeMillis());
                            // default to 0
                            annotation.setFacetId(0);
                            annotation.setGeneratedBy(AiBotUser.AI_ID);
                            return annotation;
                        })
                .collect(Collectors.toList());
    }

    /**
     * convert bounding box to polygon with wkt format
     *
     * @param bbox bounding box, [ltX, ltY, rbX, rbY]
     * @return polygon with wkt format
     * @throws IllegalArgumentException if bbox is null or length is not 4
     */
    private static String transferBBoxToPolygonString(Double[] bbox) {
        Preconditions.checkArgument(
                Objects.nonNull(bbox) && bbox.length == 4,
            "Cannot transfer bbox to polygon, bbox: %s".formatted(
                Optional.ofNullable(bbox).map(Arrays::toString).orElse(null)));
        var ltX = bbox[0];
        var ltY = bbox[1];
        var rbX = bbox[2];
        var rbY = bbox[3];
        var points =
                List.of(
                        Pair.of(ltX, ltY),
                        Pair.of(ltX, rbY),
                        Pair.of(rbX, rbY),
                        Pair.of(rbX, ltY),
                        Pair.of(ltX, ltY));
        StringBuilder polygonBuilder = new StringBuilder("POLYGON((");

        for (int i = 0; i < points.size(); i++) {
            if (i > 0) {
                polygonBuilder.append(",");
            }
            var point = points.get(i);
            polygonBuilder.append(point.getLeft()).append(" ").append(point.getRight());
        }
        polygonBuilder.append("))");
        return polygonBuilder.toString();
    }
}
