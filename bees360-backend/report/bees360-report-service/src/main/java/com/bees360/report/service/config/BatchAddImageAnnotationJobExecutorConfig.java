package com.bees360.report.service.config;

import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.ImageAnnotationTagService;
import com.bees360.report.service.NewImageService;
import com.bees360.report.service.job.BatchAddImageAnnotationJobExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@ConditionalOnProperty(
		prefix = "bees360.feature-switch",
		value = "enable-batch-add-image-annotation",
		havingValue = "true",
		matchIfMissing = true)
public class BatchAddImageAnnotationJobExecutorConfig {

	@Bean
	public BatchAddImageAnnotationJobExecutor addImageAnnotationJobExecutor(
			NewImageService imageService,
			ImageAnnotationTagService imageAnnotationTagService,
			ExternalInterface externalInterface,
			@Value("${image.annotation.need-delete-factor-sources:}") List<String> needDeleteFactorSources) {
		return new BatchAddImageAnnotationJobExecutor(imageService, imageAnnotationTagService, externalInterface, needDeleteFactorSources);
	}
}
