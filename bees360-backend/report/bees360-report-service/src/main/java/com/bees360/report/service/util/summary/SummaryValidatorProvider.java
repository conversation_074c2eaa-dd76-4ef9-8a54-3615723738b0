package com.bees360.report.service.util.summary;

import com.bees360.internal.ai.entity.enums.ProjectServiceTypeEnum;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.vo.ReportSummaryVo;

public interface SummaryValidatorProvider {

    boolean support(ProjectServiceTypeEnum projectServiceType, int curReportType);

    void validate(long projectId, ProjectServiceTypeEnum projectServiceType, int reportType, ReportSummaryVo summary)
        throws ServiceException;
}
