package com.bees360.report.service.config;

import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.ReportProvider;
import com.bees360.report.service.ProjectReportFileService;
import com.bees360.report.service.listener.PublishEventOnReportApprovedByQC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Bean;

@Configuration
public class PublishEventOnReportApprovedByQCConfig {

    @Bean
    public PublishEventOnReportApprovedByQC publishEventOnReportApprovedByQC(
            ReportProvider reportProvider,
            ProjectReportProvider projectReportProvider,
            ProjectReportFileService projectReportFileService,
            @Value("${bees360.system-qc-user:0}") String systemQCUser) {
        return new PublishEventOnReportApprovedByQC(
                reportProvider,
                projectReportProvider,
                projectReportFileService,
                () -> systemQCUser);
    }
}
