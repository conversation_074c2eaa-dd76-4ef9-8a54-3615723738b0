package com.bees360.report.service.impl;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.ai.mapper.ProjectImageMapper;
import com.bees360.api.Entity;
import com.bees360.common.Message;
import com.bees360.common.collections.ListUtil;
import com.bees360.entity.dto.Point;
import com.bees360.entity.enums.AnnotationSourceTypeEnum;
import com.bees360.image.ImageAnnotationManager;
import com.bees360.image.ImageTagCategoryEnum;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.ImageTagManager;
import com.bees360.image.Message.ImageMessage.Tag;
import com.bees360.image.grpc.ProtobufImageAnnotation;
import com.bees360.image.util.AttributeMessageAdapter;
import com.bees360.internal.ai.entity.ImageTagDict;
import com.bees360.internal.ai.entity.consts.AiBotUser;
import com.bees360.internal.ai.entity.consts.ImageTagCodeDict;
import com.bees360.internal.ai.service.config.Bees360FeatureSwitch;
import com.bees360.report.core.exception.MessageCode;
import com.bees360.report.core.exception.ProtoServiceMessageException;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.entity.AnnotationTypeCode;
import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.ProjectImage;
import com.bees360.report.entity.enums.AnnotationTagFaceIdEnum;
import com.bees360.report.entity.enums.AnnotationUsageTypeEnum;
import com.bees360.report.entity.vo.ImageAnnotationVo;
import com.bees360.report.grpc.api.report2web.AnnotationReportSort;
import com.bees360.report.grpc.api.report2web.AnnotationReportSortParam;
import com.bees360.report.grpc.api.report2web.ImageAnnotationTagCreateRequest;
import com.bees360.report.grpc.api.report2web.ImageAnnotationTagRequest;
import com.bees360.report.grpc.api.report2web.PointMessage;
import com.bees360.report.mapper.ImageAnnotationMapper;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.ImageAnnotationTagService;
import com.bees360.report.service.ImageTagDictService;
import com.bees360.user.User;
import com.bees360.util.Iterables;
import com.bees360.util.geom.GeometryUtil;
import com.bees360.util.idWorker.IdWorker;
import com.bees360.util.idWorker.IdWorkerInstancer;
import com.google.common.base.Preconditions;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/09/28 16:31
 */
@Slf4j
@Service
public class ImageAnnotationTagServiceImpl implements ImageAnnotationTagService {

    @Autowired private ExternalInterface externalInterface;

    @Autowired
    private ImageAnnotationMapper imageAnnotationMapper;

    @Autowired
    private ImageTagDictService imageTagDictService;

    @Autowired private ImageTagManager imageTagManager;

    @Autowired private ImageAnnotationManager imageAnnotationManager;

    @Autowired private ProjectImageMapper projectImageMapper;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Override
    public void createImageAnnotationTag(String createdBy, String imageId, long projectId,
                                                          com.bees360.report.grpc.api.report2web.ImageAnnotationTagRequest imageTag) throws ServiceException {
        if (imageTag == null) {
            throw new ProtoServiceMessageException(MessageCode.PARAM_INVALID, "param imageTag is required.");
        }
        ProjectImage projectImage = externalInterface.getImageById(imageId);
        if (Objects.isNull(projectImage)) {
            throw new ProtoServiceMessageException(MessageCode.PARAM_INVALID, "Failed to get project image");
        }
        ImageAnnotation imageAnnotation = reloadImageAnnotationByTagRequest(imageId, projectId, imageTag,
            false);
        if (Objects.equals(imageTag.getFacetId(), AnnotationTagFaceIdEnum.FULL.getCode())) {
            List<ImageAnnotation> annotations = imageAnnotationMapper.listAnnotationsByImageIdAndSourceType(imageId, null);
            if (CollectionUtils.isNotEmpty(annotations)) {
                if (annotations.stream().anyMatch(o -> Objects.equals(o.getAnnotationType(), imageTag.getType())
                    && Objects.equals(o.getFacetId(), AnnotationTagFaceIdEnum.FULL.getCode()))) {
                    return;
                }
            }
        }
        batchAdd(List.of(imageAnnotation), createdBy);
    }

    private ImageAnnotation reloadImageAnnotationByTagRequest(String imageId, long projectId,
                                                              ImageAnnotationTagRequest imageTag, boolean forceUpdate) {
        List<Point> points = Stream.of(imageTag.getLtPoint(),
            PointMessage.newBuilder().setX(imageTag.getLtPoint().getX()).setY(imageTag.getRbPoint().getY()).build(),
            imageTag.getRbPoint(),
            PointMessage.newBuilder().setX(imageTag.getRbPoint().getX()).setY(imageTag.getLtPoint().getY()).build())
            .map(p -> new Point(p.getX(), p.getY()))
            .collect(Collectors.toList());
        ImageAnnotation anno = new ImageAnnotation();
        if (forceUpdate) {
            anno.setAnnotationId(imageTag.getAnnotationId());
        } else {
            IdWorker idWorker = IdWorkerInstancer.getIdWorkerInstance();
            anno.setAnnotationId(idWorker.nextId());
        }
        String polygon = GeometryUtil.polygonToText(points);
        anno.setAnnotationPolygon(polygon);
        anno.setAnnotationType(imageTag.getType());
        anno.setCenterPointX(ImageAnnotation.center(points).getX());
        anno.setCenterPointY(ImageAnnotation.center(points).getY());
        anno.setFacetId(imageTag.getFacetId());
        anno.setImageId(imageId);
        anno.setUsageType(AnnotationUsageTypeEnum.TAG.getCode());
        anno.setProjectId(projectId);
        anno.setGeneratedBy(AiBotUser.AI_ID);
        anno.setCreatedTime(System.currentTimeMillis());
        anno.setSourceType(AnnotationSourceTypeEnum.TAG.getCode());
        anno.setRemark(imageTag.getRemark());
        anno.setProjectId(projectId);
        anno.setAttribute(imageTag.getAttribute());
        ImageTagDict tagDict = imageTagDictService
            .getImageTagDictByTagIds(Collections.singletonList(imageTag.getType()))
            .stream().findFirst().orElse(null);
        setTypeByTagDict(anno, tagDict);
        return anno;
    }

    @Override
    public void setTypeByTagDict(ImageAnnotation anno, ImageTagDict tagDict) {
        if (Objects.isNull(tagDict)) {
            return;
        }
        if (Objects.equals(ImageTagCategoryEnum.COMPONENT.getCode(), tagDict.getCategory())) {
            anno.setUsageType(AnnotationUsageTypeEnum.COMPONENT.getCode());
        }
        if (Objects.equals(ImageTagCategoryEnum.DAMAGE.getCode(), tagDict.getCategory())) {
            if (Objects.equals(anno.getAnnotationType(), ImageTagCodeDict.DETAIL)) {
                anno.setUsageType(AnnotationUsageTypeEnum.DETAIL.getCode());
                return;
            }
            anno.setUsageType(AnnotationUsageTypeEnum.DAMAGE_ANNOTATION.getCode());
        }
        if (Objects.equals(ImageTagCategoryEnum.HAZARD.getCode(), tagDict.getCategory())) {
            anno.setUsageType(AnnotationUsageTypeEnum.HAZARD.getCode());
        }
        if (Objects.equals(ImageTagCategoryEnum.FEEDBACK.getCode(), tagDict.getCategory())) {
            anno.setUsageType(AnnotationUsageTypeEnum.FEEDBACK.getCode());
        }
        if (Objects.equals(ImageTagCategoryEnum.CREDIT.getCode(), tagDict.getCategory())) {
            anno.setUsageType(AnnotationUsageTypeEnum.CREDIT.getCode());
        }
    }

    @Override
    public void deleteImageAnnotation(User user, Set<Long> annotationIds) throws ServiceException {
        if (CollectionUtils.isEmpty(annotationIds)) {
            throw new ServiceException(MessageCode.PARAM_INVALID);
        }

        var annotations = imageAnnotationMapper.getByIds(annotationIds);
        if (CollectionUtils.isEmpty(annotations)) {
            return;
        }
        deleteImageAnnotation(annotations, user.getId());

        imageAnnotationMapper.batchDelete(annotationIds);
    }

    @Override
    public List<ImageAnnotation> getImageAnnotationTag(String imageId) {
        List<ImageAnnotation> annotations = imageAnnotationMapper.listAnnotationsByImageIdAndSourceType(imageId, null);
        if (CollectionUtils.isEmpty(annotations)) {
            return Collections.emptyList();
        }
        reloadImageTagDict(annotations);
        return annotations;
    }

    @Override
    public List<ImageAnnotation> getImageAnnotationTagList(List<String> imageIds) {
        List<ImageAnnotation> annotations = imageAnnotationMapper.listAnnotationsByImageIdListAndSourceType(imageIds, null);
        if (CollectionUtils.isEmpty(annotations)) {
            return Collections.emptyList();
        }
        reloadImageTagDict(annotations);
        return annotations;
    }

    @Override
    public ImageAnnotation getImageAnnotationById(String imageId, Long annotationId) {
        List<ImageAnnotation> annotations = getImageAnnotationTag(imageId);
        return CollectionUtils.isNotEmpty(annotations) ? annotations.stream().filter(o -> Objects.equals(o.getAnnotationId(),
            annotationId)).findFirst().orElse(null) : null;
    }

    @Override
    public void reloadImageTagDict(List<ImageAnnotation> annotations) {
        List<ImageTagDict> dicts = imageTagDictService.getImageTagDictByTagIds(
            annotations.stream().map(ImageAnnotation::getAnnotationType).collect(Collectors.toList()));
        Map<Integer, ImageTagDict> dictMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dicts)) {
            dictMap = dicts.stream().collect(Collectors.toMap(ImageTagDict::getCode, v -> v, (v1, v2) -> v1));
        }
        Map<Integer, ImageTagDict> finalDictMap = dictMap;
        annotations.forEach(annotation -> Optional.ofNullable(annotation.getAnnotationType()).ifPresent(o -> {
            ImageTagDict imageTagDict = finalDictMap.get(o);
            annotation.setTagName(Objects.isNull(imageTagDict) ? "" : imageTagDict.getName());
            annotation.setCategoryTagName(Objects.isNull(imageTagDict) ? "" : imageTagDict.getCategoryName());
        }));
        if (bees360FeatureSwitch.isEnableAutoTagWithNewLogic()) {
            reloadAnnotationAttribute(annotations);
        }
        log.debug(String.format("reloadAnnotationAttribute annotation = '%s'", annotations));
    }

    private List<ImageAnnotation> reloadAnnotationAttribute(List<ImageAnnotation> annotations) {
        if (Iterables.isEmpty(annotations)) {
            return annotations;
        }
        var ids = annotations.stream().map(a -> String.valueOf(a.getAnnotationId())).collect(Collectors.toList());
        var imageApiAnnotations = imageAnnotationManager.findByIds(ids);
        var idWithAttribute = new HashMap<String, String>();
        imageApiAnnotations.forEach(a -> {
            if (StringUtils.isNotEmpty(a.getAttribute())) {
                idWithAttribute.put(a.getId(), a.getAttribute());
            }
        });
        log.debug(String.format("reloadAnnotationAttribute idWithAttribute = '%s'", idWithAttribute));
        return annotations.stream().peek(a -> {
            var annotationId = String.valueOf(a.getAnnotationId());
            if (idWithAttribute.containsKey(annotationId)) {
                a.setAttribute(idWithAttribute.get(annotationId));
            }
        }).collect(Collectors.toList());
    }

    private List<ImageAnnotationVo> getAnnotationVos(List<String> imageIds) {
        List<ImageAnnotation> annotations = imageAnnotationMapper.listAnnotationsByImageIdListAndSourceType(imageIds, null);
        if (CollectionUtils.isEmpty(annotations)) {
            return Collections.emptyList();
        }
        reloadImageTagDict(annotations);
        return annotations.stream().collect(Collectors.groupingBy(ImageAnnotation::getImageId))
            .entrySet().stream().map(anno -> new ImageAnnotationVo(anno.getKey(), anno.getValue())).collect(Collectors.toList());
    }

    @Override
    public List<ImageAnnotationVo> batchCreateImageAnnotationTag(User user, ImageAnnotationTagCreateRequest imageTagReq)
        throws ServiceException {
        if (imageTagReq == null) {
            throw new ProtoServiceMessageException(MessageCode.PARAM_INVALID, "param imageTag is required.");
        }
        for (String imageId : imageTagReq.getImageIdsList()) {
            createImageAnnotationTag(user.getId(), imageId, imageTagReq.getProjectId(), imageTagReq.getImageTag());
        }
        return getAnnotationVos(imageTagReq.getImageIdsList());
    }

    @Override
    public ImageAnnotation addByAnnotationInfo(ImageAnnotation anno, User user, ProjectImage image) {
        batchAdd(List.of(anno), user.getId());
        return anno;
    }

    @Override
    public void batchAdd(List<ImageAnnotation> annotations, String createdBy) {
        if (CollectionUtils.isEmpty(annotations)) {
            return;
        }
        // 每次add前删除一次full tag
        deleteFullImageAnnotation(annotations, createdBy);
        saveImageAnnotation(annotations, createdBy);
        imageAnnotationMapper.insertBatchByTagVo(annotations);
    }

    @Override
    public void batchDelete(Set<Long> annotationIds, String deletedBy) {
        if (CollectionUtils.isEmpty(annotationIds)) {
            return;
        }
        var annotations = imageAnnotationMapper.getByIds(annotationIds);
        deleteImageAnnotation(annotations, deletedBy);

        imageAnnotationMapper.batchDelete(annotationIds);
        if (bees360FeatureSwitch.isEnableAutoDeleteDarTag()) {
            // 一键删除AI之后，如果这些图片不存在一个 annotation，删除该图片的 dar 标签(和手动删除保持一致)。
            var originImageIds = annotations.stream().map(ImageAnnotation::getImageId).collect(Collectors.toSet());
            List<ImageAnnotation> annotationList = imageAnnotationMapper.listAnnotationsByImageIdListAndSourceType(new ArrayList<>(originImageIds), null);
            var annotationImageIds = annotationList.stream().map(ImageAnnotation::getImageId).collect(Collectors.toSet());
            for (String imageId : originImageIds) {
                if (!annotationImageIds.contains(imageId)) {
                    // 如果图片不存在一个 annotation，尝试删除该图片的 dar 标签(和手动删除保持一致)。
                    log.info("The image {} does not have an annotation. Try to delete the DAR tag of the image.", imageId);
                    var darTag = List.of(String.valueOf(ImageTagEnum.DAR.getCode()));
                    imageTagManager.deleteImageTag(imageId, darTag, deletedBy);
                }
            }
        }
    }

    @Override
    public void updateImageAnnotationReportSort(User user, String imageId,
        AnnotationReportSortParam sortParam) {
        if (CollectionUtils.isEmpty(sortParam.getSortParamList())) {
            return;
        }
        var sortList =
                sortParam.getSortParamList().stream()
                        .map(
                                param -> {
                                    var annotation = new ImageAnnotation();
                                    annotation.setAnnotationId(
                                            Long.parseLong(param.getAnnotationId()));
                                    annotation.setReportSort(param.getSort());
                                    return annotation;
                                })
                        .collect(Collectors.toList());
        imageAnnotationMapper.updateImageAnnotationsSort(imageId, sortList);

        var annotationSortMap =
                sortParam.getSortParamList().stream()
                        .collect(
                                Collectors.toMap(
                                        AnnotationReportSort::getAnnotationId,
                                        AnnotationReportSort::getSort,
                                        (k1, k2) -> k1));
        imageAnnotationManager.updateAnnotationSort(annotationSortMap, user.getId());
    }

    @Override
    public List<ImageAnnotation> getByOriginAnnotationId(long projectId, String originAnnotationId) {
        return imageAnnotationMapper.getByOriginAnnotationId(projectId, originAnnotationId);
    }

    @Override
    public List<ImageAnnotation> getByOriginAnnotationIds(long projectId, Set<String> originAnnotationIds) {
        return imageAnnotationMapper.getByOriginAnnotationIds(projectId, originAnnotationIds);
    }

    @Override
    public List<ImageAnnotation> getByUsageType(List<String> imageIds, Integer usageType) {
        return imageAnnotationMapper.getAnnotationByUsageTypes(imageIds, usageType, AnnotationSourceTypeEnum.TAG.getCode());
    }

    @Override
    public void deleteByUsageTypeAndSourceType(String imageId, Integer usageType, String deletedBy) {
        var annotations =
            imageAnnotationMapper.getAnnotationByUsageTypes(
                List.of(imageId), usageType, AnnotationSourceTypeEnum.TAG.getCode());
        deleteImageAnnotation(annotations, deletedBy);

        imageAnnotationMapper.deleteByUsageTypeAndSourceType(imageId, usageType, AnnotationSourceTypeEnum.TAG.getCode());
    }

    private double divide(double a, double b) {
        return BigDecimal.valueOf(a)
                .divide(BigDecimal.valueOf(b), RoundingMode.HALF_UP)
                .doubleValue();
    }

    @Override
    public List<ImageAnnotation> getByAnnotationId(List<Long> annotationIds) {
        return imageAnnotationMapper.getByAnnotationId(annotationIds);
    }

    @Override
    public List<ImageAnnotation> listByProjectIdAndAnnotationTypeAndSourceType(
        Long projectId, List<Integer> annotationTypes, List<Integer> sourceTypes) {
        Preconditions.checkArgument(
            projectId != null, "projectId is required, but got " + projectId);
        return imageAnnotationMapper.listByProjectIdAndAnnotationTypeAndSourceType(
            projectId, annotationTypes, sourceTypes);
    }

    private void deleteFullImageAnnotation(List<ImageAnnotation> annotations, String createdBy) {
        var imageIds =
                annotations.stream().map(ImageAnnotation::getImageId).collect(Collectors.toList());
        var annotationList =
                imageAnnotationMapper.listAnnotationsByImageIdListAndSourceType(imageIds, null);
        var annotationTypes = annotations.stream().map(ImageAnnotation::getAnnotationType).collect(Collectors.toList());
        var fullTagAnnotationIds =
                annotationList.stream()
                        .filter(
                                a ->
                                        Objects.equals(
                                                a.getFacetId(),
                                                AnnotationTagFaceIdEnum.FULL.getCode())
                                                && annotationTypes.contains(a.getAnnotationType()))
                        .map(ImageAnnotation::getAnnotationId)
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fullTagAnnotationIds)) {
            imageAnnotationManager.deleteAll(Iterables.transform(fullTagAnnotationIds, String::valueOf), createdBy);
            if (bees360FeatureSwitch.isEnableDeleteMysqlFullAnnotation()) {
                log.info("delete mysql full graph annotation. annotationIds={}", fullTagAnnotationIds);
                imageAnnotationMapper.batchDelete(new HashSet<>(fullTagAnnotationIds));
            }
        }
    }

    private void saveImageAnnotation(List<ImageAnnotation> annotations, String addedBy) {
        var imageIds = annotations.stream().map(ImageAnnotation::getImageId).distinct().collect(Collectors.toList());
        var images = projectImageMapper.listByIds(imageIds);
        var imageMap = ListUtil.toMap(i -> i.getImageId(), images);
        saveImageAnnotation(annotations, imageMap, addedBy);
    }

    private void saveImageAnnotation(List<ImageAnnotation> annotations,
            Map<String, com.bees360.internal.ai.entity.ProjectImage> imageMap, String addedBy) {
        if (CollectionUtils.isEmpty(annotations)) {
            return;
        }
        for (var annotation : annotations) {
            if (!imageMap.containsKey(annotation.getImageId())) {
                throw new IllegalArgumentException(String.format("The image %s not found.", annotation.getImageId()));
            }
        }

        // 构建solid annotation list
        var annotationList =
                annotations.stream()
                        .filter(a -> !Objects.equals(a.getAnnotationType(), AnnotationTypeCode.MAPPING))
                        .map(
                                a -> {
                                    var image = imageMap.get(a.getImageId());
                                    var pointList = GeometryUtil.parseGeometryText(a.getAnnotationPolygon());
                                    var polygon =
                                            pointList.stream().map(
                                                            point ->
                                                                    Message.PointMessage
                                                                            .newBuilder()
                                                                            .setX(divide(point.getX(), image.getImageWidth()))
                                                                            .setY(divide(point.getY(), image.getImageHeight()))
                                                                            .build())
                                                    .collect(Collectors.toList());
                                    if (Objects.equals(a.getFacetId(), AnnotationTagFaceIdEnum.FULL.getCode())) {
                                        polygon = List.of(Message.PointMessage.newBuilder().setX(0).setY(0).build(),
                                            Message.PointMessage.newBuilder().setX(0).setY(1).build(),
                                            Message.PointMessage.newBuilder().setX(1).setY(1).build(),
                                            Message.PointMessage.newBuilder().setX(1).setY(0).build());
                                    }
                                    // mysql必须存合并的polygon（首尾时同一个点）pgsql收尾不必相同，目前annotation除了mapping以外都是四边形，五个点
                                    if (polygon.size() == 5) {
                                        polygon.remove(polygon.size() - 1);
                                    }
                                    var builder = Tag.Annotation.newBuilder();
                                    acceptIfNotNull(builder::addAllPolygon, polygon);
                                    acceptIfNotNull(builder::setDescription, a.getRemark());
                                    if (!StringUtils.equals(a.getOriginAnnotationId(), String.valueOf(a.getAnnotationId()))) {
                                        acceptIfNotNull(builder::setOriginAnnotationId, a.getOriginAnnotationId());
                                    }
                                    int sourceType;
                                    if (Objects.equals(a.getFacetId(), AnnotationTagFaceIdEnum.FULL.getCode())) {
                                        sourceType = AnnotationSourceTypeEnum.TAG.getCode();
                                    } else if (Objects.equals(
                                                    a.getSourceType(),
                                                    AnnotationSourceTypeEnum.INFERA.getCode())
                                            || Objects.equals(
                                                    a.getSourceType(),
                                                    AnnotationSourceTypeEnum.INFERA_MAPPING
                                                            .getCode())) {
                                        sourceType = a.getSourceType();
                                    } else if (StringUtils.equals(String.valueOf(a.getAnnotationId()), a.getOriginAnnotationId())) {
                                        sourceType = AnnotationSourceTypeEnum.ORIGINAL.getCode();
                                    } else {
                                        sourceType = AnnotationSourceTypeEnum.MAPPING.getCode();
                                    }
                                    acceptIfNotNull(builder::setSourceType, sourceType);
                                    var tagId = String.valueOf(a.getAnnotationType());
                                    if (Objects.equals(a.getAnnotationType(), AnnotationTypeCode.TEST_SQUARE)) {

                                        tagId = String.valueOf(ImageTagEnum.TEST_SQUARE.getCode());
                                    }
                                    acceptIfNotNull(builder::setAttribute, a.getAttribute(), AttributeMessageAdapter::jsonToAttribute);
                                    return new ProtobufImageAnnotation(builder.build(), a.getImageId(), tagId);
                                })
                        .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(annotationList)) {
            var tags = imageAnnotationManager.saveAll(annotationList, addedBy);
            // 重置annotationId，将annotationId设置成solid保存后的id
            for (var tag : tags) {
                if (tag.getAnnotation() == null) {
                    continue;
                }
                var annotationIds =
                    Iterables.toStream(tag.getAnnotation())
                        .map(com.bees360.image.ImageAnnotation::getId)
                        .collect(Collectors.toList());
                var tagAnnotations = annotations.stream()
                    .filter(a -> StringUtils.equals(a.getImageId(), tag.getImageId()))
                    .filter(a -> Objects.equals(a.getAnnotationType(), Integer.parseInt(tag.getId()))
                        || (Integer.parseInt(tag.getId()) == ImageTagEnum.TEST_SQUARE.getCode()
                            && Objects.equals(a.getAnnotationType(), AnnotationTypeCode.TEST_SQUARE)))
                    .collect(Collectors.toList());
                for (var i = 0; i < tagAnnotations.size(); i++) {
                    var annotation = tagAnnotations.get(i);
                    if (StringUtils.equals(String.valueOf(annotation.getAnnotationId()), annotation.getOriginAnnotationId())) {
                        annotation.setOriginAnnotationId(annotationIds.get(i));
                    }
                    tagAnnotations.get(i).setAnnotationId(Long.parseLong(annotationIds.get(i)));
                }
            }
        }
    }

    private void deleteImageAnnotation(List<ImageAnnotation> annotations, String deletedBy) {
        if (CollectionUtils.isEmpty(annotations)) {
            return;
        }

        var annotationIds =
            annotations.stream()
                .filter(a -> !Objects.equals(a.getAnnotationType(), AnnotationTypeCode.MAPPING))
                .map(a -> String.valueOf(a.getAnnotationId()))
                .collect(Collectors.toSet());

        // 删除annotation tag  start
        var imageIdList =
                annotations.stream().map(ImageAnnotation::getImageId).collect(Collectors.toList());
        var tags = imageTagManager.findByImageIds(imageIdList);

        var deleteTagIds = new HashMap<String, Iterable<String>>();
        // 直接删除annotation tag
        var imageTagMap =
            annotations.stream()
                .filter(a -> Objects.equals(a.getFacetId(), AnnotationTagFaceIdEnum.FULL.getCode()))
                .collect(Collectors.groupingBy(ImageAnnotation::getImageId));
        if (!imageTagMap.isEmpty()) {
            for (var entry : imageTagMap.entrySet()) {
                var tagIds =
                        entry.getValue().stream()
                                .map(
                                        a ->
                                                Objects.equals(a.getAnnotationType(), AnnotationTypeCode.TEST_SQUARE)
                                                        ? String.valueOf(ImageTagEnum.TEST_SQUARE.getCode())
                                                        : String.valueOf(a.getAnnotationType()))
                                .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tagIds)) {
                    deleteTagIds.put(entry.getKey(), tagIds);
                }
            }
        }
        // 直接删除annotation tag需要删除tag下的所有annotation
        if (!deleteTagIds.isEmpty()) {
            for (var entry : deleteTagIds.entrySet()) {
                var imageTags = tags.get(entry.getKey());
                var existsTagIds = Iterables.toSet(entry.getValue());
                for (var tag : imageTags) {
                    if (existsTagIds.contains(tag.getId()) && tag.getAnnotation() != null
                        && !com.google.common.collect.Iterables.isEmpty(tag.getAnnotation())) {
                        annotationIds.addAll(Iterables.toStream(tag.getAnnotation())
                            .map(com.bees360.image.ImageAnnotation::getId).collect(Collectors.toList()));
                    }
                }
            }
        }
        // 如果一个annotation Tag的所有annotation都被删除，这个annotation Tag也应该被删除
        // tag和annotation tag应该分开管理，但是还没有分开，分开后这里应该去掉
        for (var entry : tags.entrySet()) {
            var deletedTags =
                    Iterables.toStream(entry.getValue())
                            .filter(t -> Iterables.isNotEmpty(t.getAnnotation()))
                            .filter(
                                    t ->
                                            Iterables.toStream(t.getAnnotation())
                                                    .allMatch(
                                                            a -> annotationIds.contains(a.getId())))
                            .map(Entity::getId)
                            .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deletedTags)) {
                deleteTagIds.put(entry.getKey(), deletedTags);
            }
        }
        if (!deleteTagIds.isEmpty()) {
            imageTagManager.deleteAllImageTag(deleteTagIds, deletedBy);
        }
        // 删除annotation tag  end

        if (CollectionUtils.isNotEmpty(annotationIds)) {
            imageAnnotationManager.deleteAll(annotationIds, deletedBy);
        }
    }
}
