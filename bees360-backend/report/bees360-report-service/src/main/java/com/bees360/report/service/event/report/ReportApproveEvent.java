package com.bees360.report.service.event.report;

import com.bees360.report.service.event.Bees360Event;
import lombok.Getter;

public class ReportApproveEvent extends Bees360Event {

    @Getter
    final long projectId;

    @Getter
    final int reportType;


    public ReportApproveEvent(Object source, long projectId, int reportType) {
        super(source);
        this.projectId = projectId;
        this.reportType = reportType;
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }
}
