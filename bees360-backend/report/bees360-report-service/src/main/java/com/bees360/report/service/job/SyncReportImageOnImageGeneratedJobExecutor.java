package com.bees360.report.service.job;

import com.bees360.common.resource.ResourceKeyManager;
import com.bees360.entity.ReportAnnotationImage;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.grpc.Timestamp;
import com.bees360.image.ImageSource;
import com.bees360.internal.ai.entity.ProjectImage;
import com.bees360.internal.ai.service.ProjectImageManager;

import com.bees360.job.registry.SyncReportImageOnImageGenerated;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.report.Message;
import com.bees360.report.ReportManager;
import com.bees360.report.image.ReportImageManager;
import com.bees360.report.mapper.ReportAnnotationImageMapper;
import com.bees360.report.service.FileService;
import com.bees360.report.service.ProjectReportService;
import com.bees360.report.service.impl.ProjectReportFileServiceImpl;
import com.bees360.report.summary.Message.ReportSummary;
import com.google.common.base.Preconditions;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Log4j2
public class SyncReportImageOnImageGeneratedJobExecutor
        extends AbstractJobExecutor<SyncReportImageOnImageGenerated> {

    private final ReportManager reportManager;
    private final ProjectImageManager projectImageManager;
    private final FileService fileService;
    private final ReportImageManager reportImageManager;
    private final ReportAnnotationImageMapper reportAnnotationImageMapper;
    private final ProjectReportService projectReportService;

    public SyncReportImageOnImageGeneratedJobExecutor(
        ReportManager reportManager,
        ProjectImageManager projectImageManager,
        FileService fileService,
        ReportImageManager reportImageManager,
        ReportAnnotationImageMapper reportAnnotationImageMapper,
        ProjectReportService projectReportService) {
        this.reportManager = reportManager;
        this.projectImageManager = projectImageManager;
        this.fileService = fileService;
        this.reportImageManager = reportImageManager;
        this.reportAnnotationImageMapper = reportAnnotationImageMapper;
        this.projectReportService = projectReportService;
        log.info(
            "Created :{} with reportManager :{}, projectImageManager :{}, fileService :{}, "
            + "reportImageManager :{} and reportAnnotationImageMapper :{} and projectReportService :{}.",
            this,
            reportManager,
            projectImageManager,
            fileService,
            reportImageManager,
            reportAnnotationImageMapper,
            projectReportService);
    }

    @Override
    protected void handle(SyncReportImageOnImageGenerated job) throws IOException {
        var reportId = job.getReportId();
        log.info(
            "Detected event start saving the annotation image of report {} to the job in the database.",
            reportId);
        var report = reportManager.findById(reportId);
        Preconditions.checkArgument(
                report != null,
            "Cannot sync image in report %s: report does not exist.".formatted(report));
        var createdBy = report.getCreatedBy();
        var reportType = ProjectReportFileServiceImpl.getReportType(report);
        var summary = report.getSummary().getSummary();
        var images = getSummaryImages(summary);
        if (CollectionUtils.isEmpty(images)) {
            return;
        }
        var projectId = getProjectId(summary);
        if (StringUtils.isEmpty(projectId)) {
            return;
        }

        log.info("Start saving {} annotation image of report {} to the job in the database.imageCount:{}",
            projectId, reportId, images.size());
        var originImageIds =
                images.stream()
                        .map(ReportSummary.Image::getOriginalImageId)
                        .distinct()
                        .collect(Collectors.toList());

        var newImageIds =
                images.stream()
                        .map(ReportSummary.Image::getId)
                        .distinct()
                        .collect(Collectors.toList());

        List<ProjectImage> originImages = projectImageManager.findAllByIds(ListUtils.union(originImageIds, newImageIds));
        var existsOriginImages =
                originImages.stream()
                        .collect(
                                Collectors.toMap(ProjectImage::getImageId, a -> a, (k1, k2) -> k1));

        if (!existsOriginImages.keySet().containsAll(originImageIds)) {
            throw new IllegalStateException(
                "Image data is missing in report %s. : The image used does not exist.".formatted(
                    report.getId()));
        }
        if (CollectionUtils.containsAny(existsOriginImages.keySet(), newImageIds)) {
            log.warn("The ReportSummary for Report {} has been saved.", reportId);
            return;
        }

        var longProjectId = Long.parseLong(projectId);

        createSolidGroupReportImage(projectId, reportId, existsOriginImages, images, createdBy);

        projectReportService.deleteOldAnnotationImagesAndImages(longProjectId, reportType);
        insertImageReportImage(longProjectId, existsOriginImages, images);
        insertReportAnnotationImages(longProjectId, reportType, images);

        log.info(
            "End saving the annotation image of report {} to the job in the database.",
            reportId);
        var reportStatus = reportManager.findById(reportId).getStatus();
        if (Message.ReportMessage.Status.APPROVED.equals(reportStatus)) {
            // 作为预警，如果report已经approved，但是还没生成完annotation image，正常情况下应该是极少发生的
            log.warn(
                    "The report {} annotation for project {} has not been generated completely.",
                    reportId,
                    projectId);
        }
    }

    private String getProjectId(String summary) {
        var projectNodeString = "project";
        try {
            var summaryJson = JsonParser.parseString(summary).getAsJsonObject();
            if (summaryJson.has(projectNodeString)) {
                var projectJson = summaryJson.getAsJsonObject(projectNodeString);
                if (projectJson.has("id")) {
                    return projectJson.get("id").getAsString();
                }
            }
        } catch (JsonSyntaxException e) {
            throw new IllegalStateException(
                "Cannot parse summary in report %s into json: invalid json.".formatted(summary),
                e);
        }
        return null;
    }

    private List<com.bees360.report.summary.Message.ReportSummary.Image> getSummaryImages(String summary) {
        var imageNodeString = "images";
        var parserBuilder = com.bees360.report.summary.Message.ReportSummary.newBuilder();
        try {
            var summaryJson = JsonParser.parseString(summary).getAsJsonObject();
            if (summaryJson.has(imageNodeString)) {
                var imageJsonObject = new JsonObject();
                imageJsonObject.add(imageNodeString, summaryJson.get(imageNodeString));
                var summaryImageJson = imageJsonObject.toString();
                JsonFormat.parser().ignoringUnknownFields().merge(summaryImageJson, parserBuilder);
            }
        } catch (JsonSyntaxException | InvalidProtocolBufferException e) {
            throw new IllegalStateException(
                "Cannot parse summary in report %s into json: invalid json.".formatted(summary),
                e);
        }
        return parserBuilder.getImagesList();
    }

    private void insertImageReportImage(
            long projectId,
            Map<String, ProjectImage> existImageMap,
            List<ReportSummary.Image> reportImages) {
        List<ProjectImage> images = new ArrayList<>();
        int fileSourceType = FileSourceTypeEnum.REPORT_IMAGE.getCode();
        // uploadTime 升序排序
        long uploadTime = System.currentTimeMillis();
        for (var reportImage : reportImages) {
            uploadTime++;
            var imageId = reportImage.getId();
            String originalFileName = reportImage.getName();
            if (originalFileName.length()
                    > com.bees360.report.entity.ProjectImage.ORIGINAL_FILE_NAME_LENGTH_LIMIT) {
                // 前端下载elevation image名字，限制长度
                originalFileName =
                        originalFileName.substring(
                                        0,
                                        com.bees360.report.entity.ProjectImage
                                                .ORIGINAL_FILE_NAME_LENGTH_LIMIT)
                                + ".jpg";
            }
            var annotations = reportImage.getAnnotationsList();
            ProjectImage originalImage = existImageMap.get(reportImage.getOriginalImageId());
            var image = originalImage.clone();
            image.setImageId(imageId);
            image.setFileSourceType(fileSourceType);
            image.setUploadTime(uploadTime);
            image.setOriginalFileName(originalFileName);
            image.setParentId(originalImage.getImageId());
            if (reportImage.getType().equals(ReportSummary.Image.ImageType.ORIGINAL)) {
                images.add(image);
                continue;
            }
            if (reportImage.getType().equals(ReportSummary.Image.ImageType.ANNOTATED)
                    && CollectionUtils.isEmpty(annotations)) {
                images.add(image);
                continue;
            }

            var fileName = generateImageKey(String.valueOf(projectId), reportImage);
            String middleFileNameKey =
                    fileService.createKey(fileName, ResourceKeyManager.MIDDLE_RESOLUTION_IMAGE);
            String lowerFileNameKey =
                    fileService.createKey(fileName, ResourceKeyManager.LOWER_RESOLUTION_IMAGE);

            image.setFileName(fileName);
            image.setFileNameMiddleResolution(middleFileNameKey);
            image.setFileNameLowerResolution(lowerFileNameKey);
            image.setProjectId(projectId);
            image.setDeleted(false);

            images.add(image);
        }

        projectImageManager.saveNewImages(projectId, images);
    }

    private void insertReportAnnotationImages(
            long projectId, int reportType, List<ReportSummary.Image> reportImages) {
        List<ReportAnnotationImage> reportAnnotationImageList = new ArrayList<>();
        for (var image : reportImages) {
            Timestamp timestamp = new Timestamp();
            timestamp.setSeconds(System.currentTimeMillis());
            ReportAnnotationImage reportAnnotationImage =
                    ReportAnnotationImage.builder()
                            .projectId(projectId)
                            .reportType(reportType)
                            .imageId(image.getId())
                            .caption(image.getCategory())
                            .alias(image.getName())
                            .createTime(timestamp)
                            .build();
            reportAnnotationImageList.add(reportAnnotationImage);
        }
        reportAnnotationImageMapper.insertBatch(reportAnnotationImageList);
    }

    private void createSolidGroupReportImage(
            String projectId,
            String reportId,
            Map<String, ProjectImage> originImageMap,
            List<ReportSummary.Image> reportImages,
            String userId) {
        Iterable<Pair<? extends ImageSource, String>> imageSourcesWithParentId =
                reportImages.stream()
                        .map(
                                reportImage -> {
                                    var imageResourceKey = generateImageKey(projectId, reportImage);
                                    var originalImage =
                                            originImageMap.get(reportImage.getOriginalImageId());
                                    var reportImageType = reportImage.getType();
                                    if (reportImageType.equals(
                                            ReportSummary.Image.ImageType.ORIGINAL)) {
                                        imageResourceKey = originalImage.getFileName();
                                    }
                                    var annotations = reportImage.getAnnotationsList();
                                    if (reportImageType.equals(
                                                    ReportSummary.Image.ImageType.ANNOTATED)
                                            && CollectionUtils.isEmpty(annotations)) {
                                        imageResourceKey = originalImage.getFileName();
                                    }
                                    var shootingTime =
                                            getShootingTimeInstant(
                                                    originImageMap
                                                            .get(reportImage.getOriginalImageId())
                                                            .getShootingTime());
                                    return Pair.of(
                                            ImageSource.of(
                                                    imageResourceKey,
                                                    StringUtils.EMPTY,
                                                    "default",
                                                    shootingTime,
                                                    reportImage.getId()),
                                            reportImage.getOriginalImageId());
                                })
                        .collect(Collectors.toList());
        reportImageManager.createReportImage(reportId, imageSourcesWithParentId, userId);
    }

    private Instant getShootingTimeInstant(long shootingTime) {
        if (shootingTime > 0) {
            return Instant.ofEpochMilli(shootingTime);
        }
        return null;
    }

    private String generateImageKey(String projectId, ReportSummary.Image image) {
        var stringFormatter = "project/%s/images/origin/%s.jpeg";
        var imageKey = image.toBuilder().setId(StringUtils.EMPTY).build().toString().hashCode();
        return stringFormatter.formatted(projectId, "%018d".formatted(Math.abs(imageKey)));
    }
}
