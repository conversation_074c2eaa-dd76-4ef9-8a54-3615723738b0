package com.bees360.report.service.config;

import com.alibaba.ttl.TransmittableThreadLocal;

/**
 * <AUTHOR>
 * @date 2021/06/17 18:28
 */
public class ImageThreadHelpUtil {

    private  static TransmittableThreadLocal<String> referThread =  new  TransmittableThreadLocal<>();


    public static String getRefer() {
        return referThread.get();
    }

    public static void setRefer(String refer) {
        referThread.set(refer);
    }

    public static void clear() {
        referThread.remove();
    }

}
