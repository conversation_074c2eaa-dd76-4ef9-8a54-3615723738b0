package com.bees360.report.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "thread-pool-executor")
@Component
@Data
public class ThreadPoolExecutorProperties {

    public ThreadPoolExecutorProperties() {}

    private Integer corePoolSize = 4;

    private Integer maxPoolSize = 10;

    private Long keepAliveTimeMillis = 300000L;
}
