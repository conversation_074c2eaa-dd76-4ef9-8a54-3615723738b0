package com.bees360.report.service.config;

import com.bees360.entity.User;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.report.Message;
import com.bees360.report.Report;
import com.bees360.report.ReportManager;
import com.bees360.report.service.ProjectReportFileService;
import com.bees360.report.service.impl.ProjectReportFileServiceImpl;
import com.bees360.report.service.listener.ReportApprovedPostProcessingOnReportStatusChanged;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

@Configuration
@EnableConfigurationProperties
@ConditionalOnProperty(
		prefix = "report.approved-post-processing",
		name = "enabled",
		havingValue = "true")
@Log4j2
public class ReportApprovedPostProcessingOnReportStatusChangedConfig {

	@Getter
	@Setter
	@Configuration
	@ConfigurationProperties(prefix = "report.approved-post-processing")
	static class ReportApprovedPostProcessingProperties {
		private List<ReportTypeEnum> targetReportTypes = new ArrayList<>();
	}

	@Bean
	ReportApprovedPostProcessingOnReportStatusChanged reportApprovedPostProcessingOnReportStatusChanged(
			ProjectReportManager projectReportManager,
			ReportManager reportManager,
			ProjectReportFileService projectReportFileService,
			@Qualifier("reportApprovedPostProcessingPredicate") Predicate<Report> reportApprovedPostProcessingPredicate
	) {
		return new ReportApprovedPostProcessingOnReportStatusChanged(
				projectReportManager,
				reportManager,
				projectReportFileService,
				reportApprovedPostProcessingPredicate);
	}

	@Bean
	Predicate<Report> reportApprovedPostProcessingPredicate(ReportApprovedPostProcessingProperties properties) {
		var targetReportTypes = properties.getTargetReportTypes();
		log.info("Target report types: {}", targetReportTypes);
		return (report) -> {
			var reportStatus = report.getStatus();
			var createdBy = report.getCreatedBy();
			var reportTypeCode = ProjectReportFileServiceImpl.getReportType(report);
			var reportType = ReportTypeEnum.getEnum(reportTypeCode);
			return targetReportTypes.contains(reportType)
					&& reportStatus == Message.ReportMessage.Status.APPROVED
					&& Objects.equals(createdBy, String.valueOf(User.AI_ID));
		};
	}
}
