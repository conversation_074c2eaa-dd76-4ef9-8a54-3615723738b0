package com.bees360.report.service.listener;

import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.registry.ReportStatusChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.Message;
import com.bees360.report.ReportProvider;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.service.ProjectReportFileService;
import com.bees360.report.service.impl.ProjectReportFileServiceImpl;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.function.Supplier;

/**
 * 监听报告状态变更事件，当报告被QC批准时发布报告批准事件。
 */
@Log4j2
public class PublishEventOnReportApprovedByQC
        extends AbstractNamedEventListener<ReportStatusChanged> {

    private final ReportProvider reportProvider;

    private final ProjectReportProvider projectReportProvider;

    private final ProjectReportFileService projectReportFileService;

    private final Supplier<String> qCUserSupplier;

    public PublishEventOnReportApprovedByQC(
            ReportProvider reportProvider,
            ProjectReportProvider projectReportProvider,
            ProjectReportFileService projectReportFileService,
            Supplier<String> qCUserSupplier) {
        this.reportProvider = reportProvider;
        this.projectReportProvider = projectReportProvider;
        this.projectReportFileService = projectReportFileService;
        this.qCUserSupplier = qCUserSupplier;
        log.info(
                "Created {}(reportProvider={}, projectReportProvider={}, projectReportFileService={}, qCUserSupplier={}, qCUser={}).",
                this,
                this.reportProvider,
                this.projectReportProvider,
                this.projectReportFileService,
                this.qCUserSupplier,
                this.qCUserSupplier.get());
    }

    @Override
    public void handle(ReportStatusChanged event) throws IOException {
        log.info("Received event: {}.", event);
        if (event.getReportStatus() != Message.ReportMessage.Status.APPROVED_VALUE) {
            return;
        }
        if (!StringUtils.equals(event.getUpdatedBy(), qCUserSupplier.get())) {
            return;
        }
        var reportId = event.getId();
        var report = reportProvider.get(reportId);

        var reportTypeCode = ProjectReportFileServiceImpl.getReportType(report);
        var reportType = ReportTypeEnum.getEnum(reportTypeCode);

        if (!reportType.needApproved()) {
            return;
        }
        var projectIds = projectReportProvider.findProjectId(event.getId());
        for (var projectId : projectIds) {
            try {
                projectReportFileService.publishReportApprovedEvent(
                        Long.parseLong(projectId), qCUserSupplier.get(), reportType);
            } catch (ServiceException e) {
                log.error(
                        String.format(
                                "Report %s of Project %s has not been approved.",
                                reportType, projectId));
            }
        }
    }
}
