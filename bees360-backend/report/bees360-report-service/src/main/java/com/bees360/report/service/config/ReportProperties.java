package com.bees360.report.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Nonnull;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@Component
@Validated
@ConfigurationProperties(prefix = "report")
public class ReportProperties {

    private ReportSchedulerProperties scheduler;

    @Nonnull
    private ReportSummaryProperties summary = new ReportSummaryProperties();

    @Data
    public static class ReportSummaryProperties {

        @Nonnull
        private final Set<Long> forCompanies = new HashSet<>();
    }

}
