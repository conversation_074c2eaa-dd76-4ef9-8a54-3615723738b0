package com.bees360.report.service.impl;

import com.bees360.api.common.Circle;
import com.bees360.base.MessageCode;
import com.bees360.common.Message;
import com.bees360.common.collections.ListUtil;
import com.bees360.entity.dto.Point;
import com.bees360.entity.enums.AnnotationSourceTypeEnum;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.image.ImageAnnotationManager;
import com.bees360.image.ImageNote;
import com.bees360.image.ImageNoteProvider;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.ImageTagManager;
import com.bees360.image.Message.ImageMessage.Tag;
import com.bees360.image.grpc.ProtobufImageAnnotation;
import com.bees360.image.tag.ImageTag;
import com.bees360.internal.ai.entity.ImageTagDict;
import com.bees360.internal.ai.entity.consts.AiBotUser;
import com.bees360.internal.ai.entity.consts.ImageTagCodeDict;
import com.bees360.internal.ai.exchange.ai2client.ProjectImageProto;
import com.bees360.report.core.exception.ServiceException;
import com.bees360.report.core.exception.ServiceMessageException;
import com.bees360.report.entity.AnnotationTypeCode;
import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.ProjectImage;
import com.bees360.report.entity.enums.AnnotationTagFaceIdEnum;
import com.bees360.report.entity.enums.AnnotationUsageTypeEnum;
import com.bees360.report.grpc.api.report2web.AnnotatedImageRequest;
import com.bees360.report.grpc.api.report2web.AnnotatedImageVo;
import com.bees360.report.grpc.api.report2web.ImageAnnotationBatchDeleteRequest;
import com.bees360.report.grpc.api.report2web.ImageCreatedVo;
import com.bees360.report.grpc.api.report2web.ImageEffectedVo;
import com.bees360.report.grpc.api.report2web.ImagesEffectedVo;
import com.bees360.report.grpc.api.report2web.MappingBatchCreateVo;
import com.bees360.report.grpc.api.report2web.MappingCreateParam;
import com.bees360.report.grpc.api.report2web.MappingCreateVo;
import com.bees360.report.grpc.api.report2web.MappingDetail;
import com.bees360.report.grpc.api.report2web.MappingImage;
import com.bees360.report.grpc.api.report2web.MappingInfo;
import com.bees360.report.grpc.api.report2web.PointMessage;
import com.bees360.report.mapper.ImageAnnotationMapper;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.ImageAnnotationTagService;
import com.bees360.report.service.ImageTagDictService;
import com.bees360.report.service.NewImageService;
import com.bees360.report.service.voassemble.ProjectImageVoAssemble;
import com.bees360.user.User;
import com.bees360.util.DateUtil;
import com.bees360.util.Iterables;
import com.bees360.util.geom.GeometryUtil;
import com.bees360.util.idWorker.IdWorker;
import com.bees360.util.idWorker.IdWorkerInstancer;
import com.bees360.util.report.GraphicInnerPoint;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.bees360.image.util.AttributeMessageAdapter;

import static com.bees360.report.service.util.image.AttributeMessageUtil.jsonToAttribute;

/**
 * 目前所有annotatedImage和annotationImage表的数据都不做删除，为的是保留历史画图资源，不用重新生成 后续优化可考虑加一个最新版本字段以及如果考虑删除图片资源，可以清理不需要的历史数据
 *
 * <AUTHOR>
 * @date 2021/04/02 09:52
 */
@Slf4j
@Service
public class NewImageServiceImpl implements NewImageService {

    @Autowired
    private ImageAnnotationTagService imageAnnotationTagService;

    @Autowired
    private ExternalInterface externalInterface;

    @Autowired private ImageAnnotationManager imageAnnotationManager;

    @Autowired private ImageTagManager imageTagManager;

    @Autowired private ImageNoteProvider imageNoteProvider;

    @Autowired private ImageTagDictService imageTagDictService;

    @Autowired private ImageAnnotationMapper imageAnnotationMapper;

    private static final String TEST_SQUARE_CODE = String.valueOf(ImageTagEnum.TEST_SQUARE.getCode());

    public static final Set<String> RECONFIGURATION_TAG = Set.of(TEST_SQUARE_CODE);

    private static final String SPILT_SEPARATOR = "/";
    private static final String ORIGIN_STR = "origin";
    private static final String JPEG_STR = ".jpeg";


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ImageEffectedVo createAnnotatedImage(User user, String imageId, AnnotatedImageRequest annotatedRequest)
        throws ServiceException {
        log.info("Create annotated image. imageId='{}', userId='{}', annotatedRequest='{}'", imageId, user.getId(), annotatedRequest);

        ProjectImage image = externalInterface.getImageById(imageId);
        if (Objects.isNull(image)) {
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, "Image not found");
        }
        var projectId = image.getProjectId();
        int facetId = annotatedRequest.getFacetId();
        var polygon = getPolygon(annotatedRequest, image);
        int sourceType = annotatedRequest.hasSourceType()
                ? annotatedRequest.getSourceType().getValue()
                : AnnotationSourceTypeEnum.TAG.getCode();
        // 新增ImageAnnotation记录
        ImageAnnotation imageAnnotation =
                generateAnnotation(
                        projectId,
                        image.getImageId(),
                        facetId,
                        annotatedRequest.getTagCode(),
                        annotatedRequest.getRemark(),
                        annotatedRequest.getConfidenceLevel(),
                        polygon,
                        sourceType,
                        annotatedRequest.getAttribute());
        imageAnnotationTagService.addByAnnotationInfo(imageAnnotation, user, image);

        // 整张原图的标记不需要画图和映射
        if (Objects.equals(facetId, AnnotationTagFaceIdEnum.FULL.getCode())) {
            return ImageEffectedVo.getDefaultInstance();
        }

        ImageCreatedVo imageVo = createAnnotatedImageVo(image);
        ImageEffectedVo.Builder effectedVo = ImageEffectedVo.newBuilder().setOrigin(imageVo);

        return effectedVo.build();
    }

    private ImageAnnotation generateAnnotation(
        long projectId,
        String imageId,
        Integer facetId,
        Integer tagCode,
        String remark,
        double confidenceLevel,
        List<Point> polygon,
        int sourceType,
        String attribute) {
        var usageType =
                StringUtils.equals(String.valueOf(tagCode), TEST_SQUARE_CODE)
                        ? AnnotationUsageTypeEnum.FRAME_RACTANGLE.getCode()
                        : AnnotationUsageTypeEnum.TAG.getCode();
        ImageAnnotation anno = new ImageAnnotation();
        IdWorker idWorker = IdWorkerInstancer.getIdWorkerInstance();
        Long annotationId = idWorker.nextId();
        anno.setAnnotationId(annotationId);
        anno.setAnnotationPolygon(GeometryUtil.polygonToText(polygon));
        // 存的是tag code
        anno.setAnnotationType(tagCode);
        var center = ImageAnnotation.center(polygon);
        anno.setCenterPointX(center.getX());
        anno.setCenterPointY(center.getY());
        anno.setFacetId(facetId);
        anno.setImageId(imageId);
        anno.setUsageType(usageType);
        anno.setProjectId(projectId);
        anno.setGeneratedBy(AiBotUser.AI_ID);
        anno.setCreatedTime(System.currentTimeMillis());
        anno.setSourceType(sourceType);
        anno.setRemark(remark);
        anno.setOriginAnnotationId(annotationId + "");
        anno.setConfidenceLevel(confidenceLevel);
        anno.setAttribute(attribute);
        ImageTagDict tagDict =
            imageTagDictService
                .getImageTagDictByTagIds(Collections.singletonList(tagCode))
                .stream()
                .findFirst()
                .orElse(null);
        imageAnnotationTagService.setTypeByTagDict(anno, tagDict);

        return anno;
    }

    private List<Point> getPolygon(AnnotatedImageRequest annotatedRequest, ProjectImage image) {
        PointMessage ltPoint = annotatedRequest.getLtPoint();
        PointMessage rbPoint = annotatedRequest.getRbPoint();
        log.info("Received ltPoint: {}, rbPoint: {}", ltPoint, rbPoint);
        int facetId = annotatedRequest.getFacetId();
        if (!Objects.equals(facetId, AnnotationTagFaceIdEnum.FULL.getCode())
                && ltPoint.getX() <= 1 && ltPoint.getY() <= 1
                && rbPoint.getX() <= 1 && rbPoint.getY() <= 1
                && image.getImageWidth() > 0 && image.getImageHeight() > 0) {
            // 为了兼容 upstream annotation 表的 polygon 字段，如果是全图的 annotation，需要乘以宽高。
            var imageWidth = image.getImageWidth();
            var imageHeight = image.getImageHeight();
            ltPoint = PointMessage.newBuilder().setX(ltPoint.getX() * imageWidth).setY(ltPoint.getY() * imageHeight).build();
            rbPoint = PointMessage.newBuilder().setX(rbPoint.getX() * imageWidth).setY(rbPoint.getY() * imageHeight).build();
            log.info("Adjusted ltPoint: {}, rbPoint: {} after scaling", ltPoint, rbPoint);
        }
        return Stream.of(
                        ltPoint,
                        PointMessage.newBuilder().setX(ltPoint.getX()).setY(rbPoint.getY()).build(),
                        rbPoint,
                        PointMessage.newBuilder().setX(rbPoint.getX()).setY(ltPoint.getY()).build())
                .map(p -> new Point(p.getX(), p.getY()))
                .collect(Collectors.toList());
    }

    /**
     * 返回当前图片存储的信息和受影响的图片记录
     */
    @Override
    public ImageEffectedVo refreshGetEffectedImageVo(String imageId) throws ServiceException {
        ProjectImage image = externalInterface.getImageById(imageId);
        if (Objects.isNull(image)) {
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, "Image not found");
        }
        ImageCreatedVo createdVo = createAnnotatedImageVo(image);
        if (Objects.isNull(createdVo)) {
            return ImageEffectedVo.getDefaultInstance();
        }
        ImageEffectedVo.Builder builder = ImageEffectedVo.newBuilder().setOrigin(createdVo);
        var imageAnnotationIds = new HashSet<String>();
        if (CollectionUtils.isNotEmpty(createdVo.getAnnotated().getImageAnnotationsList())) {
            imageAnnotationIds.addAll(createdVo.getAnnotated().getImageAnnotationsList().stream().map(
                com.bees360.report.grpc.api.report2web.ImageAnnotation::getAnnotationId)
                .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(createdVo.getTestSquare().getImageAnnotationsList())) {
            imageAnnotationIds.addAll(createdVo.getTestSquare().getImageAnnotationsList().stream().map(
                com.bees360.report.grpc.api.report2web.ImageAnnotation::getAnnotationId)
                .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(imageAnnotationIds)) {
            var effectAnnotations = imageAnnotationMapper.getByOriginAnnotationIds(image.getProjectId(), imageAnnotationIds);
            if (CollectionUtils.isNotEmpty(effectAnnotations)) {
                Set<String> imageEffectIds = effectAnnotations.stream().map(ImageAnnotation::getImageId).collect(Collectors.toSet());
                var images = externalInterface.findImageByIds(new ArrayList<>(imageEffectIds));
                var effectVo = createAnnotatedImageVos(image.getProjectId(), images);
                builder.addAllEffected(effectVo);
            }
        }
        return builder.build();
    }

    private Map<String, ImageCreatedVo> buildImageCreatedVoByAnnotations(
        List<ProjectImage> images, List<ImageAnnotation> imageAnnotations) {
        if (CollectionUtils.isEmpty(images) || CollectionUtils.isEmpty(imageAnnotations)) {
            return Collections.emptyMap();
        }
        imageAnnotations = imageAnnotations.stream()
            .filter(o -> !Objects.equals(o.getFacetId(), AnnotationTagFaceIdEnum.FULL.getCode()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(imageAnnotations)) {
            return Collections.emptyMap();
        }
        Map<String, ProjectImage> imageMap = images.stream()
            .collect(Collectors.toMap(ProjectImage::getImageId, k -> k, (v1, v2) -> v1));
        Set<String> imageIds = imageAnnotations.stream()
            .map(ImageAnnotation::getImageId)
            .collect(Collectors.toSet());

        Map<String, List<ImageAnnotation>> damageImageAnnotationMap = imageAnnotations.stream()
            .filter(anno -> AnnotationUsageTypeEnum.getDamageUsage().contains(anno.getUsageType()))
            .collect(Collectors.groupingBy(ImageAnnotation::getImageId));
        Map<String, List<ImageAnnotation>> testSquareImageAnnotationMap = imageAnnotations.stream()
            .filter(anno -> Objects.equals(anno.getUsageType(), AnnotationUsageTypeEnum.FRAME_RACTANGLE.getCode()))
            .collect(Collectors.groupingBy(ImageAnnotation::getImageId));

        List<ImageCreatedVo> createdVoList = imageIds.stream()
            .map(imageId -> {
                List<ImageAnnotation> damageAnnotations=damageImageAnnotationMap.getOrDefault(imageId,List.of());
                List<ImageAnnotation> testSquareAnnotations =testSquareImageAnnotationMap.getOrDefault(imageId,List.of());
                ProjectImage projectImage = imageMap.get(imageId);
                return getImageCreatedVo(projectImage, damageAnnotations, testSquareAnnotations);
        }).collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(createdVoList) ? createdVoList.stream()
            .collect(Collectors.toMap(ImageCreatedVo::getImageId, k -> k, (v1, v2) -> v1)) : null;
    }

    private MappingInfo getMappingInfo(List<ImageAnnotation> imageAnnotation) {
        List<MappingDetail> mapping;
        if (CollectionUtils.isNotEmpty(imageAnnotation)) {
            //找自己画的
            AtomicInteger index = new AtomicInteger();
            mapping = imageAnnotation.stream().map(o -> MappingDetail.newBuilder().setFacetId(index.getAndIncrement())
                    .addAllPath(toPointMessageList(GeometryUtil.parseGeometryText(o.getAnnotationPolygon()))).build())
                .collect(Collectors.toList());
        } else {
            //找3d画的
            mapping = new ArrayList<>();
        }
        return MappingInfo.newBuilder().addAllMapping(mapping).build();
    }

    private MappingInfo getMappingInfo(ProjectImage image) {
        var imageId = image.getImageId();
        // ImageAnnotation表中获取图片的3d阴影
        var imageAnnotation =
                imageAnnotationTagService.getByUsageType(
                        List.of(imageId), AnnotationUsageTypeEnum.MAPPING_3D.getCode());
        return getMappingInfo(imageAnnotation);
    }

    @Override
    public MappingCreateVo createMappingImage(String imageId, String mappingImageId) throws ServiceException {
        log.info("create mapping image imageId {}, mappingImageId {}", imageId, mappingImageId);
        ProjectImage image = externalInterface.getImageById(imageId);
        if (Objects.isNull(image)) {
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, "Image not found");
        }
        var projectId = image.getProjectId();
        ProjectImage mappingImage;
        if (StringUtils.isNotBlank(mappingImageId)) {
            mappingImage = externalInterface.getImageById(mappingImageId);
        } else {
            mappingImage = externalInterface.getNewImageOverview(projectId);
        }
        MappingInfo mappingInfo;
        if (mappingImage == null) {
            mappingInfo = MappingInfo.getDefaultInstance();
        } else {
            mappingInfo = getMappingInfo(image);
        }
        return createMappingImage(image, mappingImage, mappingInfo);
    }

    private MappingCreateVo createMappingImage(ProjectImage image, ProjectImage mappingImage,
            MappingInfo mappingInfo) throws ServiceException {
        if (Objects.isNull(image)) {
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, "Image not found.");
        }
        var imageId = image.getImageId();
        var projectId = image.getProjectId();
        AnnotatedImageVo annotatedImageVo = buildAnnotatedImageVo(projectId, imageId, null);
        var mappingImageBuilder = MappingImage.newBuilder();
        mappingImageBuilder.setImageId(imageId);
        if (mappingImage != null) {
            mappingImageBuilder.setMappingImageId(mappingImage.getImageId());
        }
        return MappingCreateVo.newBuilder()
                .setMappingInfo(mappingInfo)
                .setCreated(annotatedImageVo)
                .setMappingImage(mappingImageBuilder.build())
                .build();
    }

    @Override
    public MappingBatchCreateVo createMappingImages(MappingCreateParam createParam) throws ServiceException {
        List<String> imageIds = createParam.getImageIdsList();
        var mappingByOverview = CollectionUtils.isNotEmpty(imageIds);
        if (!mappingByOverview) {
            imageIds =
                    createParam.getMappingImagesList().stream()
                            .map(MappingImage::getImageId)
                            .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(imageIds)) {
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, "Image cannot be empty.");
        }
        log.debug("create mapping image imageIds {}", imageIds);

        var images = externalInterface.findImageByIds(imageIds);
        if (CollectionUtils.isEmpty(images)) {
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, "Image not found.");
        }
        var projectIdSet = ListUtil.toSet(ProjectImage::getProjectId, images);
        if (projectIdSet.size() != 1) {
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, "Images are not in the same project.");
        }
        var projectId = images.get(0).getProjectId();
        var start = System.currentTimeMillis();
        Map<String, ProjectImage> imageIdMappingMap = new HashMap<>();
        if (mappingByOverview) {
            var overview = externalInterface.getNewImageOverview(projectId);
            if (overview != null) {
                imageIdMappingMap = imageIds.stream().collect(Collectors.toMap(i -> i, i -> overview, (k1, k2) -> k1));
            }
        } else {
            var mappingImageIds = createParam.getMappingImagesList().stream()
                .map(MappingImage::getMappingImageId)
                .distinct()
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mappingImageIds)) {
                var mappingImages = ListUtil.toMap(ProjectImage::getImageId,
                    externalInterface.findImageByIds(mappingImageIds));
                imageIdMappingMap = ListUtil.toMap(MappingImage::getImageId,
                    i -> mappingImages.get(i.getMappingImageId()), createParam.getMappingImagesList());
            }
        }

        for (var entry : imageIdMappingMap.entrySet()) {
            if (entry.getValue() != null && projectId != entry.getValue().getProjectId()) {
                throw new ServiceMessageException(MessageCode.PARAM_INVALID, "Images are not in the same project.");
            }
        }
        log.debug("Find images cost {}", System.currentTimeMillis() - start);
        var imageMap =
                images.stream()
                        .collect(
                                Collectors.toMap(ProjectImage::getImageId, a -> a, (k1, k2) -> k1));

        var threeDMapping = imageAnnotationTagService
            .getByUsageType(imageIds, AnnotationUsageTypeEnum.MAPPING_3D.getCode());
        var threeDMappingMaps = ListUtil.toGroupMap(ImageAnnotation::getImageId, threeDMapping);

        var batchCreated = new ArrayList<MappingCreateVo>();
        for (var imageId : imageIds) {
            if (!imageMap.containsKey(imageId)) {
                throw new ServiceMessageException(MessageCode.PARAM_INVALID, "Image not found.");
            }
            var mappingImage = imageIdMappingMap.get(imageId);
            var image = imageMap.get(imageId);
            MappingInfo mappingInfo = getMappingInfo(threeDMappingMaps.get(imageId));
            MappingCreateVo createVo = createMappingImage(image, mappingImage, mappingInfo);
            batchCreated.add(createVo);
        }
        long end = System.currentTimeMillis();
        log.debug("mapping batch cost {}", end - start);
        return MappingBatchCreateVo.newBuilder().addAllBatchCreatedVo(batchCreated).build();
    }

    @Override
    public AnnotatedImageVo fillMappingToOverview(String imageId, MappingInfo mappingInfo, User user)
        throws ServiceException {
        ProjectImage referImage = externalInterface.getImageById(imageId);
        if (Objects.isNull(referImage)) {
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, "Image not found");
        }
        long projectId = referImage.getProjectId();
        ProjectImage overview = externalInterface.getNewImageOverview(projectId);
        if (Objects.isNull(overview)) {
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, "Overview Image not found");
        }
        int usageType = AnnotationUsageTypeEnum.MAPPING_3D.getCode();
        imageAnnotationTagService.deleteByUsageTypeAndSourceType(imageId, AnnotationUsageTypeEnum.MAPPING_3D.getCode(), user.getId());
        if (Objects.nonNull(mappingInfo)) {
            List<ImageAnnotation> annotationList = new ArrayList<>(mappingInfo.getMappingList().size());
            for (MappingDetail detail : mappingInfo.getMappingList()) {
                if (CollectionUtils.isEmpty(detail.getPathList())) {
                    break;
                }
                List<Point> points = detail.getPathList().stream().map(o -> new Point(o.getX(), o.getY())).collect(
                    Collectors.toList());
                String polygon = GeometryUtil.polygonToText(points);
                Point centerPoint = GeometryUtil.calculateCentertPoint(points);
                ImageAnnotation annotation = ImageAnnotation.builder()
                    .annotationId(IdWorkerInstancer.getIdWorkerInstance().nextId())
                    .imageId(imageId).facetId(0).projectId(projectId).annotationPolygon(polygon)
                    .createdTime(DateUtil.getNow()).centerPointX(centerPoint.getX()).centerPointY(centerPoint.getY())
                    .annotationType(AnnotationTypeCode.MAPPING).usageType(usageType).generatedBy(AiBotUser.AI_ID)
                    .sourceType(AnnotationSourceTypeEnum.TAG.getCode())
                    .build();
                annotationList.add(annotation);
            }
            imageAnnotationTagService.batchAdd(annotationList, user.getId());
            return createMappingImageResource(referImage, overview);
        }
        return AnnotatedImageVo.getDefaultInstance();
    }

    /**
     * @param damageAnnotations 所有damage的annotation信息
     * @param testSquareAnnotations 所有testSquare的annotation信息
     */
    private ImageCreatedVo getImageCreatedVo(ProjectImage image, List<ImageAnnotation> damageAnnotations,
        List<ImageAnnotation> testSquareAnnotations) {
        if (Objects.isNull(image)) {
            return ImageCreatedVo.getDefaultInstance();
        }
        String imageId = image.getImageId();
        long projectId = image.getProjectId();
        ImageCreatedVo.Builder builder = ImageCreatedVo.newBuilder().setImageId(imageId);
        builder.setAnnotated(buildAnnotatedImageVo(projectId, imageId, damageAnnotations));

        if (CollectionUtils.isNotEmpty(testSquareAnnotations)) {
            builder.setTestSquare(buildAnnotatedImageVo(projectId, imageId, testSquareAnnotations));
        }
        return builder.build();
    }

    private AnnotatedImageVo createMappingImageResource(ProjectImage image, ProjectImage overviewImage) {
        if (Objects.isNull(overviewImage)) {
            return AnnotatedImageVo.getDefaultInstance();
        }
        MappingInfo mappingInfo = getMappingInfo(image);
        if (CollectionUtils.isEmpty(mappingInfo.getMappingList())) {
            return AnnotatedImageVo.getDefaultInstance();
        }

        return buildAnnotatedImageVo(image.getProjectId(), image.getImageId(), null);
    }

    public Set<String> delImageAnnotation(String imageId, String annotationId, String userId, boolean onlyDeleteSelf) {

        Long id = Long.valueOf(annotationId);
        // 删除imageAnnotation

        ImageAnnotation imageAnnotation = imageAnnotationTagService.getImageAnnotationById(imageId, id);
        if (Objects.isNull(imageAnnotation)) {
            log.warn(
                    "imageAnnotation not found imageId {}, annotationId {}", imageId, annotationId);
            return Set.of();
        }
        if (onlyDeleteSelf) {
            imageAnnotationTagService.batchDelete(Set.of(id), userId);
            return Set.of();
        }

        Set<Long> removeIds = new HashSet<>();
        Set<String> effectImageIds = new HashSet<>();
        List<ImageAnnotation> referAnnotations = imageAnnotationTagService
            .getByOriginAnnotationId(imageAnnotation.getProjectId(), annotationId);
        if (CollectionUtils.isNotEmpty(referAnnotations)) {
            // 如果是来源于其他图片映射
            removeIds.addAll(referAnnotations.stream()
                .map(ImageAnnotation::getAnnotationId)
                .collect(
                    Collectors.toSet()));
            effectImageIds.addAll(referAnnotations.stream().map(ImageAnnotation::getImageId).collect(Collectors.toSet()));
        }
        if (StringUtils.isNotBlank(imageAnnotation.getOriginAnnotationId())) {
            List<ImageAnnotation> imageAnnotations = imageAnnotationTagService
                .getByOriginAnnotationId(imageAnnotation.getProjectId(), imageAnnotation.getOriginAnnotationId());
            if (CollectionUtils.isNotEmpty(imageAnnotations)) {
                removeIds
                    .addAll(imageAnnotations.stream().map(ImageAnnotation::getAnnotationId)
                        .collect(Collectors.toSet()));
                effectImageIds.addAll(imageAnnotations.stream().map(ImageAnnotation::getImageId).collect(Collectors.toSet()));
            }
        } else {
            removeIds.add(imageAnnotation.getAnnotationId());
        }

        imageAnnotationTagService.batchDelete(removeIds, userId);

        return effectImageIds;
    }

    @Override
    public ImageCreatedVo delImageAnnotationTag(User user, String imageId, String annotationId, boolean onlyDeleteSelf)
            throws ServiceException {
        log.info("delete image annotation tag userId {}, imageId {}, annotationId {}, onlyDeleteSelf {}",
            user.getId(), imageId, annotationId, onlyDeleteSelf);
        delImageAnnotation(imageId, annotationId, user.getId(), onlyDeleteSelf);
        return createAnnotatedImageVo(imageId);
    }

    @Override
    public ImageCreatedVo delImageAnnotationTags(
        User user, String imageId, List<String> annotationIds, boolean onlyDeleteSelf) throws ServiceException {
        log.info("delete image annotation tags userId {}, imageId {}, annotationIds {}, onlyDeleteSelf {}",
            user.getId(), imageId, annotationIds, onlyDeleteSelf);
        for (String annotationId : annotationIds) {
            delImageAnnotation(imageId, annotationId, user.getId(), onlyDeleteSelf);
        }
        return createAnnotatedImageVo(imageId);
    }

    @Override
    public ImagesEffectedVo batchDeleteImageAnnotation(User user, ImageAnnotationBatchDeleteRequest request)
        throws ServiceException {
        var deletedBy = user.getId();
        log.info("batch delete image annotation tags userId {}, request {}", deletedBy, request);
        var annotationStringIds = request.getAnnotationIdsList();
        if (CollectionUtils.isEmpty(annotationStringIds)) {
            throw new IllegalArgumentException("Batch delete image annotations. The parameter list cannot be empty.");
        }

        var annotationIds = annotationStringIds.stream().map(Long::parseLong).collect(Collectors.toList());
        var annotations = imageAnnotationTagService.getByAnnotationId(annotationIds);
        var originImageIds = annotations.stream().map(ImageAnnotation::getImageId).collect(Collectors.toSet());
        if (!request.getOnlyDeleteSelf()) {
            var projectAnnotationMap =
                    ListUtil.toGroupMap(
                            ImageAnnotation::getProjectId,
                            annotations);
            for (var entry : projectAnnotationMap.entrySet()) {
                var originAnnotationIds =
                        entry.getValue().stream()
                                .flatMap(
                                        a ->
                                                Stream.of(
                                                        String.valueOf(a.getAnnotationId()),
                                                        a.getOriginAnnotationId()))
                                .collect(Collectors.toSet());
                var mappingAnnotations = imageAnnotationTagService.getByOriginAnnotationIds(
                        entry.getKey(), originAnnotationIds);
                annotations.addAll(mappingAnnotations);
            }
        }

        var removeIds = annotations.stream().map(ImageAnnotation::getAnnotationId).collect(Collectors.toSet());
        imageAnnotationTagService.batchDelete(removeIds, deletedBy);

        var allImageIds = annotations.stream().map(ImageAnnotation::getImageId)
            .distinct().collect(Collectors.toList());
        return createImagesEffectedVo(allImageIds, originImageIds);
    }

    private ImagesEffectedVo createImagesEffectedVo(List<String> allImageIds, Set<String> originImageIds)
        throws ServiceException {
        var images = externalInterface.findImageByIds(allImageIds);
        var originImages = images.stream().filter(i -> originImageIds.contains(i.getImageId()))
            .collect(Collectors.toList());
        var effectedImages = images.stream().filter(i -> !originImageIds.contains(i.getImageId()))
            .collect(Collectors.toList());

        var projectImageMap = images.stream().collect(Collectors.groupingBy(ProjectImage::getProjectId));

        var allImageAnnotations = new ArrayList<ImageAnnotation>();
        for (var entry : projectImageMap.entrySet()) {
            var projectAnnotations = getImageAnnotationWithoutFullFaceTags(entry.getKey(), entry.getValue());
            allImageAnnotations.addAll(projectAnnotations);
        }

        var imageAnnotationMap = allImageAnnotations.stream()
            .collect(Collectors.groupingBy(ImageAnnotation::getImageId));

        var builder = ImagesEffectedVo.newBuilder();
        for (var originImage : originImages) {
            builder.addOrigin(createAnnotatedImageVo(originImage,
                imageAnnotationMap.get(originImage.getImageId())));
        }
        for (var effectImage : effectedImages) {
            builder.addEffected(createAnnotatedImageVo(effectImage,
                imageAnnotationMap.get(effectImage.getImageId())));
        }
        return builder.build();
    }

    @Override
    public void delImageAnnotationByTagIds(String userId, long projectId, List<Integer> tagIds) {
        log.info("delete image annotation by tagIds. userId {}, projectId {}, tagIds {}", userId, projectId, tagIds);
        List<Integer> sourceTypes = List.of(AnnotationSourceTypeEnum.INFERA.getCode(), AnnotationSourceTypeEnum.INFERA_MAPPING.getCode());
        var imageAnnotations = imageAnnotationTagService.listByProjectIdAndAnnotationTypeAndSourceType(projectId, tagIds, sourceTypes);
        var annotationIds = imageAnnotations.stream().map(ImageAnnotation::getAnnotationId).collect(Collectors.toList());
        imageAnnotationTagService.batchDelete(new HashSet<>(annotationIds), userId);
    }

    @Override
    public ImageCreatedVo createAnnotatedImageVo(String imageId) throws ServiceException {
        ProjectImage image = externalInterface.getImageById(imageId);
        return createAnnotatedImageVo(image);
    }

    /**
     * 根据图片拿最新的annotatedImage和annotationImages
     */
    private ImageCreatedVo createAnnotatedImageVo(ProjectImage image) {
        String imageId = image.getImageId();
        List<ImageAnnotation> imageAnnotations = getImageAnnotationWithoutFullFaceTag(image.getProjectId(), imageId, image);
        return createAnnotatedImageVo(image, imageAnnotations);
    }

    private ImageCreatedVo createAnnotatedImageVo(ProjectImage image,
            List<ImageAnnotation> imageAnnotations) {
        if (CollectionUtils.isEmpty(imageAnnotations)) {
            String imageId = image.getImageId();
            return ImageCreatedVo.newBuilder().setImageId(imageId).build();
        }

        List<ImageAnnotation> damageImageAnnotations = imageAnnotations.stream()
            .filter(o -> AnnotationUsageTypeEnum.getDamageUsage()
                .contains(o.getUsageType())).collect(
                Collectors.toList());

        List<ImageAnnotation> testSquareImageAnnotation = imageAnnotations.stream()
            .filter(o -> Objects.equals(o.getUsageType(), AnnotationUsageTypeEnum.FRAME_RACTANGLE.getCode()))
            .collect(Collectors.toList());
        return getImageCreatedVo(image, damageImageAnnotations, testSquareImageAnnotation);
    }

    /**
     * 根据图片拿最新的annotatedImage和annotationImages
     */
    private List<ImageCreatedVo> createAnnotatedImageVos(long projectId, List<ProjectImage> images) {
        var imageAnnotationList = getImageAnnotationWithoutFullFaceTags(projectId, images);
        var imageAnnotationMap = ListUtil.toGroupMap(ImageAnnotation::getImageId, imageAnnotationList);
        var referAnnotatedVo = new ArrayList<ImageCreatedVo>();
        images.forEach(image -> {
            var imageId = image.getImageId();
            var imageAnnotations = imageAnnotationMap.get(imageId);
            if (CollectionUtils.isEmpty(imageAnnotations)) {
                referAnnotatedVo.add(ImageCreatedVo.newBuilder().setImageId(imageId).build());
                return;
            }

            List<ImageAnnotation> damageImageAnnotations = imageAnnotations.stream()
                .filter(o -> AnnotationUsageTypeEnum.getDamageUsage()
                    .contains(o.getUsageType())).collect(
                    Collectors.toList());

            List<ImageAnnotation> testSquareImageAnnotation = imageAnnotations.stream()
                .filter(o -> Objects.equals(o.getUsageType(), AnnotationUsageTypeEnum.FRAME_RACTANGLE.getCode()))
                .collect(Collectors.toList());
            var createdVo = getImageCreatedVo(image, damageImageAnnotations, testSquareImageAnnotation);
            if (Objects.nonNull(createdVo)) {
                referAnnotatedVo.add(createdVo);
            }
        });
        return referAnnotatedVo;
    }

    private List<ImageAnnotation> getImageAnnotationWithoutFullFaceTags(long projectId, List<ProjectImage> images) {
        if (CollectionUtils.isEmpty(images)) {
            return Collections.emptyList();
        }
        List<String> imageIds = images.stream().map(ProjectImage::getImageId).collect(Collectors.toList());
        var imageAnnotations = imageAnnotationTagService.getImageAnnotationTagList(imageIds);

        imageAnnotations = listSolidAnnotation(projectId, imageIds, imageAnnotations, images);

        if (CollectionUtils.isEmpty(imageAnnotations)) {
            return Collections.emptyList();
        }
        return imageAnnotations.stream()
            .filter(o -> !Objects.equals(o.getFacetId(), AnnotationTagFaceIdEnum.FULL.getCode()))
            .sorted(Comparator.comparing(ImageAnnotation::getReportSort))
            .collect(Collectors.toList());
    }

    @Override
    public ProjectImageProto.ProjectImageList listImages(long projectId, boolean requireAnnotated)
        throws ServiceException {
        List<ProjectImage> images = externalInterface.listImages(projectId);
        // Editor界面不显示drone image和mobile image以外的image
        images.removeIf(
                image ->
                        !Set.of(
                                        FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode(),
                                        FileSourceTypeEnum.DRONE_IMAGE.getCode())
                                .contains(image.getFileSourceType()));
        images.sort(Comparator.comparingLong(ProjectImage::getShootingTime));

        Map<String, List<ImageAnnotation>> annotations = new HashMap<>();
        Map<String, ImageCreatedVo> annotatedImageVo = new HashMap<>();
        Map<String, Iterable<? extends ImageNote>> imageNoteMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(images)) {
            List<String> imageIds = images.stream().map(ProjectImage::getImageId).collect(Collectors.toList());
            imageNoteMap = imageNoteProvider.findByImageIds(imageIds);
            List<ImageAnnotation> imageAnnotations = imageAnnotationTagService.getImageAnnotationTagList(imageIds);
            imageAnnotations = listSolidAnnotation(projectId, imageIds, imageAnnotations, images);
            if (CollectionUtils.isNotEmpty(imageAnnotations)) {
                // imageAnnotation只返回damage类型
                annotations = imageAnnotations.stream().filter(anno -> !Objects.equals(anno.getUsageType(),
                        AnnotationUsageTypeEnum.FRAME_RACTANGLE.getCode()))
                    .collect(Collectors.groupingBy(ImageAnnotation::getImageId));
                if (requireAnnotated) {
                    annotatedImageVo = buildImageCreatedVoByAnnotations(images, imageAnnotations);
                }
            }
        }
        return ProjectImageVoAssemble.toProtoImageListAddAnnotations(images, annotations, annotatedImageVo, imageNoteMap);
    }

    private PointMessage getPointMessage(double x, double y) {
        return PointMessage.newBuilder().setX(x).setY(y).build();
    }

    private List<com.bees360.report.grpc.api.report2web.ImageAnnotation> toImageAnnotationProto(
        List<ImageAnnotation> annotations) {
        if (CollectionUtils.isEmpty(annotations)) {
            return Collections.emptyList();
        }
        return annotations.stream().map(o -> com.bees360.report.grpc.api.report2web.ImageAnnotation.newBuilder()
            .setAnnotationId(o.getAnnotationId() + "")
            .setAnnotationPolygon(o.getAnnotationPolygon())
            .setAnnotationType(o.getAnnotationType())
            .setCenterPointX(o.getCenterPointX())
            .setCenterPointY(o.getCenterPointY())
            .setCreatedTime(o.getCreatedTime())
            .setFacetId(o.getFacetId())
            .setImageId(o.getImageId())
            .setSourceType(o.getSourceType())
            .setProjectId(o.getProjectId())
            .setUsageType(o.getUsageType())
            .setCategoryTagName(Optional.ofNullable(o.getCategoryTagName()).orElse(""))
            .setTagName(Optional.ofNullable(o.getTagName()).orElse(""))
            .setRemark(Optional.ofNullable(o.getRemark()).orElse(""))
            .setAnnotationKey(getAnnotationKey(o.getProjectId(), o.getAnnotationId() + ""))
            .setReportSort(o.getReportSort())
            .setOriginAnnotationId(Optional.ofNullable(o.getOriginAnnotationId()).orElse(""))
            .setAttribute(jsonToAttribute(o.getAttribute()))
            .build()).collect(Collectors.toList());
    }

    /**
     * 返回不包含完整图片的tag的标记数据
     */
    private List<ImageAnnotation> getImageAnnotationWithoutFullFaceTag(long projectId, String imageId, ProjectImage image) {
        if (StringUtils.isBlank(imageId)) {
            return Collections.emptyList();
        }
        var imageAnnotations = imageAnnotationTagService.getImageAnnotationTag(imageId);

        imageAnnotations = listSolidAnnotation(projectId, List.of(imageId), imageAnnotations, List.of(image));

        if (CollectionUtils.isEmpty(imageAnnotations)) {
            return Collections.emptyList();
        }
        return imageAnnotations.stream()
            .filter(o -> !Objects.equals(o.getFacetId(), AnnotationTagFaceIdEnum.FULL.getCode()))
            .sorted(Comparator.comparing(ImageAnnotation::getReportSort))
            .collect(Collectors.toList());
    }

    private void addSolidAnnotation(List<ImageAnnotation> imageAnnotations, String addedBy) {
        var annotations =
                imageAnnotations.stream()
                        .map(this::transferToSolidAnnotation)
                        .collect(Collectors.toList());
        var tags = imageAnnotationManager.saveAll(annotations, addedBy);
        var annotationIds =
                Iterables.toStream(tags)
                        .flatMap(
                                tag ->
                                        Iterables.toStream(tag.getAnnotation())
                                                .map(com.bees360.image.ImageAnnotation::getId))
                        .collect(Collectors.toList());
        for (var i = 0; i < imageAnnotations.size(); i++) {
            imageAnnotations.get(i).setAnnotationId(Long.parseLong(annotationIds.get(i)));
        }
    }

    private List<ImageAnnotation> listSolidAnnotation(long projectId, List<String> imageIds,
             List<ImageAnnotation> mysqlAnnotations, List<ProjectImage> images) {
        var imageMap = ListUtil.toMap(ProjectImage::getImageId, images);
        mysqlAnnotations.removeIf(a -> RECONFIGURATION_TAG.contains(String.valueOf(a.getAnnotationType()))
            && a.getFacetId() != AnnotationTagFaceIdEnum.FULL.getCode());
        var imageTags = imageTagManager.findByImageIds(imageIds);
        var annotations = new ArrayList<ImageAnnotation>();
        for (Map.Entry<String, Iterable<? extends ImageTag>> entry : imageTags.entrySet()) {
            var imageId = entry.getKey();
            var tags = entry.getValue();
            for (var tag : tags) {
                if (tag.getAnnotation() != null && RECONFIGURATION_TAG.contains(tag.getId())
                        && !com.google.common.collect.Iterables.isEmpty(tag.getAnnotation())) {
                    var tagAnnotations =
                            Iterables.toStream(tag.getAnnotation())
                                    .filter(a -> imageMap.containsKey(imageId))
                                    .map(a -> transferToAnnotation(projectId, imageId, tag, a, imageMap))
                                    .collect(Collectors.toList());
                    annotations.addAll(tagAnnotations);
                }
            }
        }
        imageAnnotationTagService.reloadImageTagDict(annotations);
        resetTestSquareRemark(annotations);
        if (CollectionUtils.isNotEmpty(annotations)) {
            // 避免传入list是定长list报 UnsupportedOperationException
            mysqlAnnotations = new ArrayList<>(mysqlAnnotations);
            var existsId = mysqlAnnotations.stream().map(ImageAnnotation::getAnnotationId).collect(Collectors.toList());
            annotations.removeIf(a -> existsId.contains(a.getAnnotationId()));
            mysqlAnnotations.addAll(annotations);
        }
        return mysqlAnnotations;
    }

    private void resetTestSquareRemark(List<ImageAnnotation> annotations) {
        var testSquareImages =
                annotations.stream()
                        .filter(a -> a.getAnnotationType() == Integer.parseInt(TEST_SQUARE_CODE))
                        .map(ImageAnnotation::getImageId)
                        .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(testSquareImages)) {
            return;
        }
        var imageAnnotations =
                imageAnnotationTagService.getImageAnnotationTagList(
                        Lists.newArrayList(testSquareImages));
        var imageAnnotationMap =
                imageAnnotations.stream()
                        .collect(Collectors.groupingBy(ImageAnnotation::getImageId));

        for (var a : annotations) {
            var imageId = a.getImageId();
            var polygon = GeometryUtil.parseGeometryText(a.getAnnotationPolygon());
            var hailCount = 0;
            if (imageAnnotationMap.containsKey(imageId)) {
                for (var anno : imageAnnotationMap.get(imageId)) {
                    var center = new Point(anno.getCenterPointX(), anno.getCenterPointY());
                    if (Objects.equals(anno.getAnnotationType(), ImageTagCodeDict.HAIL_DAMAGE)
                            && GraphicInnerPoint.isPointInGraphical(center, polygon)) {
                        hailCount++;
                    }
                }
            }
            a.setRemark(a.getRemark() + "=" + hailCount);
        }
    }

    private com.bees360.image.ImageAnnotation transferToSolidAnnotation(ImageAnnotation annotation) {
        var points = GeometryUtil.parseGeometryText(annotation.getAnnotationPolygon());
        var firstPoint = points.get(0);
        var lastPoint = points.get(points.size() - 1);
        if (Double.compare(firstPoint.getX(), lastPoint.getX()) == 0
            && Double.compare(firstPoint.getY(), lastPoint.getY()) == 0) {
            points.remove(lastPoint);
        }

        var polygon = Lists.transform(points, p -> com.bees360.api.common.Point.of(p.getX(), p.getY()));

        var circle = Circle.getInstance(polygon);
        return new ProtobufImageAnnotation(
            Tag.Annotation.newBuilder()
                    .setDescription(annotation.getRemark())
                    .addAllPolygon(Lists.transform(polygon,
                            p -> Message.PointMessage.newBuilder().setX(p.getX()).setY(p.getY()).build()))
                    .setCircle(Message.CircleMessage.newBuilder()
                            .setCenter(Message.PointMessage
                                    .newBuilder()
                                    .setX(circle.getCenter().getX())
                                    .setY(circle.getCenter().getX())
                                    .build())
                            .setRadius(circle.getRadius())
                            .build())
                    .setAttribute(AttributeMessageAdapter.jsonToAttribute(annotation.getAttribute()))
                    .build(),
            annotation.getImageId(),
            String.valueOf(annotation.getAnnotationType()));
    }

    private ImageAnnotation transferToAnnotation(
            long projectId,
            String imageId,
            ImageTag tag,
            com.bees360.image.ImageAnnotation annotation,
            Map<String, ProjectImage> imageMap) {
        var usageType = AnnotationUsageTypeEnum.DAMAGE_ANNOTATION.getCode();
        var points =
                Iterables.toStream(annotation.getPolygon())
                        .map(
                                p -> {
                                    var image = imageMap.get(imageId);
                                    var x = p.getX() > 1 ? p.getX() : p.getX() * image.getImageWidth();
                                    var y = p.getY() > 1 ? p.getY() : p.getY() * image.getImageHeight();
                                    return new Point(x, y);
                                })
                        .collect(Collectors.toList());
        var firstPoint = points.get(0);
        var lastPoint = points.get(points.size() - 1);
        if (Double.compare(firstPoint.getX(), lastPoint.getX()) != 0
                || Double.compare(firstPoint.getY(), lastPoint.getY()) != 0) {
            points.add(firstPoint);
        }
        // TODO zhaoshoushan 此处重构时应该改掉，应该去掉usage type直接用tag覆盖
        if (TEST_SQUARE_CODE.equals(tag.getId())) {
            usageType = AnnotationUsageTypeEnum.FRAME_RACTANGLE.getCode();
        }
        return ImageAnnotation.builder()
                .annotationId(Long.parseLong(annotation.getId()))
                .imageId(imageId)
                .facetId(AnnotationTagFaceIdEnum.NORMAL.getCode())
                .projectId(projectId)
                .annotationPolygon(GeometryUtil.polygonToText(points))
                .centerPointX(annotation.getCenter().getX())
                .centerPointY(annotation.getCenter().getY())
                .annotationType(Integer.parseInt(tag.getId()))
                .usageType(usageType)
                .sourceType(AnnotationSourceTypeEnum.TAG.getCode())
                .createdTime(tag.getCreatedAt().toEpochMilli())
                .remark(annotation.getDescription())
                .reportSort(Integer.MIN_VALUE)
                .originAnnotationId(annotation.getId())
                .build();
    }

    /**
     * 等到报告同步的缩略图逻辑修改，资源服务器或者s3上的路径即可修改为按类型划分文件夹
     */
    private AnnotatedImageVo buildAnnotatedImageVo(long projectId, String imageId, List<ImageAnnotation> annotations) {
        var annotatedId = IdWorkerInstancer.getIdWorkerInstance().nextId() + "";
        return AnnotatedImageVo.newBuilder()
            .setAnnotatedImageId(annotatedId)
            .setAnnotatedImageKey(getAnnotatedKey(projectId, annotatedId))
            .addAllImageAnnotations(toImageAnnotationProto(annotations)).setImageId(imageId)
            .build();
    }

    private String getAnnotationKey(long projectId, String annotationId) {
        return buildResourceKey(projectId, annotationId);
    }

    /**
     * 目前跟annotation规则一致，为了保存到origin目录下自动生成大小图 后面report取图逻辑一并修改的话可以有自定义的保存规则 所有没抽出getKey公有方法，可优化
     */
    private String getAnnotatedKey(long projectId, String annotatedId) {
        return buildResourceKey(projectId, annotatedId);
    }

    private String buildResourceKey(long projectId, String keyId) {
        return "project" + SPILT_SEPARATOR + projectId + SPILT_SEPARATOR + "images"
            + SPILT_SEPARATOR + ORIGIN_STR + SPILT_SEPARATOR + keyId + JPEG_STR;
    }

    private List<PointMessage> toPointMessageList(List<Point> points) {
        if (CollectionUtils.isEmpty(points)) {
            return Collections.emptyList();
        }
        return points.stream().map(o -> getPointMessage(o.getX(), o.getY())).collect(Collectors.toList());
    }
}
