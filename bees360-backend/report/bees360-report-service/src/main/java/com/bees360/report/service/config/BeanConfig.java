package com.bees360.report.service.config;

import com.bees360.image.ImageGroupManager;
import com.bees360.report.image.DefaultReportImageManager;
import com.bees360.report.image.ReportImageManager;
import com.bees360.report.service.ExternalInterface;
import com.bees360.report.service.util.summary.ReportSummaryConverter;
import com.bees360.report.service.util.summary.SummaryValidator;
import com.bees360.report.service.util.summary.SummaryValidatorCommon;
import com.bees360.report.service.util.summary.SummaryValidatorForFourPointSelfService;
import com.bees360.report.service.util.summary.SummaryValidatorForFourPointService;
import com.bees360.report.service.util.summary.SummaryValidatorProvider;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BeanConfig {

    @Bean
    public SummaryValidatorCommon summaryValidatorCommon(ExternalInterface externalInterface) {
        return new SummaryValidatorCommon(externalInterface);
    }

    @Bean
    public SummaryValidatorForFourPointService summaryValidatorForFourPointService() {
        return new SummaryValidatorForFourPointService();
    }

    @Bean
    public SummaryValidatorForFourPointSelfService summaryValidatorForFourPointSelfService() {
        return new SummaryValidatorForFourPointSelfService();
    }

    @Bean
    public SummaryValidator summaryValidator(SummaryValidatorProvider summaryValidatorCommon) {
        return new SummaryValidator(Lists.newArrayList(summaryValidatorCommon));
    }

    @Bean
    public SummaryValidator fullSummaryValidator(SummaryValidatorProvider summaryValidatorCommon,
                                             SummaryValidatorProvider summaryValidatorForFourPointService,
                                             SummaryValidatorProvider summaryValidatorForFourPointSelfService) {
        var providers = Lists.newArrayList(
            summaryValidatorCommon,
            summaryValidatorForFourPointService,
            summaryValidatorForFourPointSelfService);
        return new SummaryValidator(providers);
    }

    @Bean
    public ReportSummaryConverter reportSummaryConverter(ObjectMapper objectMapper) {
        return new ReportSummaryConverter(objectMapper);
    }

    @Bean
    ReportImageManager grpcReportImageManager(ImageGroupManager grpcImageGroupManager) {
        return new DefaultReportImageManager(grpcImageGroupManager);
    }
}
