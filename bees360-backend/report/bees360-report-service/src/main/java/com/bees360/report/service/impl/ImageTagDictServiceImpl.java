package com.bees360.report.service.impl;

import com.bees360.image.ImageTagCategoryEnum;
import com.bees360.image.tag.ImageTagDictProvider;
import com.bees360.internal.ai.entity.ImageTag;
import com.bees360.internal.ai.entity.ImageTagDirectoryVo;
import com.bees360.internal.ai.entity.dto.CodeNameDto;
import com.bees360.internal.ai.entity.enums.images.ImageTagTypeEnum;
import com.bees360.internal.ai.entity.ImageTagDict;
import com.bees360.report.service.ImageTagDictService;
import com.bees360.util.Iterables;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/30 16:13
 */
@Service
public class ImageTagDictServiceImpl implements ImageTagDictService {

    private static final int DEFAULT_TAG_DICT_TYPE = 3;

    private static final Set<ImageTagCategoryEnum> ANNOTATION_CATEGORIES =
            Set.of(
                    ImageTagCategoryEnum.DAMAGE,
                    ImageTagCategoryEnum.HAZARD,
                    ImageTagCategoryEnum.COMPONENT,
                    ImageTagCategoryEnum.FEEDBACK,
                    ImageTagCategoryEnum.CREDIT);

    @Autowired private ImageTagDictProvider imageTagDictProvider;

    @Override
    public List<ImageTagDict> getImageTagDict() {
        var sort = new AtomicInteger();
        return Iterables.toStream(imageTagDictProvider.getAll())
            .map(
                t -> {
                    var category = Optional.ofNullable(getCategoryByDisplay(t.getCategory()))
                        .orElse(getCategoryByDisplay(t.getDescription()));
                    var type = getTypeByCategory(category);
                    var tag = new ImageTagDict();
                    tag.setId(Long.valueOf(t.getId()));
                    // dictType没有什么用,旧数据都是3
                    tag.setDictType(DEFAULT_TAG_DICT_TYPE);
                    tag.setCode(Integer.valueOf(t.getId()));
                    tag.setName(t.getTitle());
                    tag.setCategory(category.getCode());
                    tag.setCategoryName(category.getDisplay());
                    tag.setType(type.getCode());
                    tag.setTypeName(type.getDisplay());
                    tag.setSort(sort.incrementAndGet());
                    return tag;
                })
            .collect(Collectors.toList());
    }

    private ImageTagTypeEnum getTypeByCategory(ImageTagCategoryEnum category) {
        if (ANNOTATION_CATEGORIES.contains(category)) {
            return ImageTagTypeEnum.ANNOTATION;
        }
        return Arrays.stream(ImageTagTypeEnum.values())
                .filter(e -> StringUtils.equals(e.getDisplay(), category.getDisplay()))
                .findFirst()
                .orElse(null);
    }

    private ImageTagCategoryEnum getCategoryByDisplay(String display) {
        return Arrays.stream(ImageTagCategoryEnum.values())
                .filter(e -> StringUtils.equals(e.getDisplay(), display))
                .findFirst()
                .orElse(null);
    }

    @Override
    public ImageTagDirectoryVo getImageTagDictVo() {
        ImageTagDirectoryVo tagVo = new ImageTagDirectoryVo();
        List<ImageTagDict> dictList = getImageTagDict();
        if (CollectionUtils.isEmpty(dictList)) {
            return tagVo;
        }

        Map<Integer, List<ImageTag>> dictMap =
                dictList.stream()
                        .collect(
                                Collectors.groupingBy(
                                        ImageTagDict::getType,
                                        Collectors.mapping(
                                                this::dictToImageTagVo, Collectors.toList())));
        tagVo.setCategory(dictMap.getOrDefault(ImageTagTypeEnum.CATEGORY.getCode(), List.of()));
        tagVo.setObject(dictMap.getOrDefault(ImageTagTypeEnum.OBJECT.getCode(), List.of()));
        tagVo.setLocation(dictMap.getOrDefault(ImageTagTypeEnum.LOCATION.getCode(), List.of()));
        tagVo.setDirection(dictMap.getOrDefault(ImageTagTypeEnum.DIRECTION.getCode(), List.of()));
        tagVo.setScope(dictMap.getOrDefault(ImageTagTypeEnum.SCOPE.getCode(), List.of()));
        tagVo.setAnnotation(dictMap.getOrDefault(ImageTagTypeEnum.ANNOTATION.getCode(), List.of()));
        tagVo.setReport(dictMap.getOrDefault(ImageTagTypeEnum.REPORT.getCode(), List.of()));
        tagVo.setOrientation(
                dictMap.getOrDefault(ImageTagTypeEnum.ORIENTATION.getCode(), List.of()));
        tagVo.setFloorLevel(dictMap.getOrDefault(ImageTagTypeEnum.FLOOR_LEVEL.getCode(), List.of()));
        tagVo.setNumber(dictMap.getOrDefault(ImageTagTypeEnum.NUMBER.getCode(), List.of()));
        return tagVo;
    }

    @Override
    public List<ImageTagDict> getImageTagDictByTagIds(List<Integer> tagIds) {
        List<ImageTagDict> tagDicts = getImageTagDict();
        if (CollectionUtils.isEmpty(tagDicts) || CollectionUtils.isEmpty(tagIds)) {
            return Collections.emptyList();
        }
        return getImageTagDict().stream().filter(o -> tagIds.contains(o.getCode())).collect(Collectors.toList());
    }

    private ImageTag dictToImageTagVo(ImageTagDict dict) {
        return new ImageTag(dict.getCode(), dict.getName(), new CodeNameDto(dict.getCategory(), dict.getCategoryName()),
            new CodeNameDto(dict.getType(), dict.getTypeName()));
    }
}
