package com.bees360.report.mapper;

import com.bees360.report.entity.ImageAnnotation;
import com.bees360.report.entity.vo.ADDeleteParameterVo;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface ImageAnnotationMapper {

    ImageAnnotation getById(@Param("annotationId") Long annotationId);

    List<ImageAnnotation> getByIds(@Param("annotationIds") Set<Long> annotationIds);

    void insertBatchByTagVo(List<ImageAnnotation> annotations);

    List<ImageAnnotation> listAnnotationsByImageIdAndSourceType(
        @Param("imageId") String imageId,
        @Param("sourceType") Integer sourceType);

    List<ImageAnnotation> listAnnotationsByImageIdListAndSourceType(
            @Param("imageIds") List<String> imageIds, @Param("sourceType") Integer sourceType);

    List<ImageAnnotation> listAnnotationTagByProjectIdAndSourceType(
            @Param("projectId") long projectId, @Param("sourceType") Integer sourceType);

    void batchDelete(@Param("annotationIds") Set<Long> annotationIds);

    void updateImageAnnotationsSort(@Param("imageId") String imageId,
        @Param("annotations") List<ImageAnnotation> sortList);

    List<ImageAnnotation> getAnnotationByUsageTypes(@Param("imageIds") List<String> imageIds,
        @Param("usageType") Integer usageType,
        @Param("sourceType") Integer sourceType);

    void deleteByUsageTypeAndSourceType(@Param("imageId") String imageId, @Param("usageType") Integer usageType,
        @Param("sourceType") Integer sourceType);

    List<ImageAnnotation> getByOriginAnnotationId(@Param("projectId") long projectId,
        @Param("originAnnotationId") String originAnnotationId);

    List<ImageAnnotation> getByOriginAnnotationIds(@Param("projectId") long projectId,
        @Param("originAnnotationIds") Set<String> originAnnotationIds);

    List<ImageAnnotation> listByProjectIdAndUsageType(@Param("projectId") long projectId, @Param("usageType") Integer usageType);

    List<ImageAnnotation> findADAnnotations(
        @Param("projectId") long projectId,
        @Param("parameter") ADDeleteParameterVo aDDeleteParameter);

    void deleteADAnnotations(
            @Param("projectId") long projectId,
            @Param("parameter") ADDeleteParameterVo aDDeleteParameter);

    List<ImageAnnotation> listByProjectIdAndAnnotationTypeAndSourceType(
        @Param("projectId") Long projectId,
        @Param("annotationType") List<Integer> annotationTypes,
        @Param("sourceType") List<Integer> sourceTypes);

    List<ImageAnnotation> getByAnnotationId(@Param("annotationIds") List<Long> annotationIds);
}
