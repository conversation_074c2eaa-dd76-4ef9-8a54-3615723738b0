package com.bees360.report.datasource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2019/11/13 11:03
 * @date 2021/05/24 18:03 share datasource with bees360-ai, so remove the datasource configuration
 */
//@Configuration
//@MapperScan(basePackages = "com.bees360.report.mapper", sqlSessionFactoryRef = "reportSqlSessionFactory")
public class DataSourceConfig {
    @Bean(name = "reportDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.report")
    public DataSource getDateSource2() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "reportSqlSessionFactory")
    public SqlSessionFactory reportSqlSessionFactory(@Qualifier("reportDataSource") DataSource datasource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(datasource);
//        bean.setTypeAliasesPackage("com.bees360.entity,com.bees360.entity.dto,com.bees360.entity.vo");
        bean.setConfigLocation(new PathMatchingResourcePatternResolver()
                .getResource("classpath:config/mybatis-config.xml"));
        bean.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath*:mappings/report/*.xml"));
        return bean.getObject();
    }

    @Bean("reportSqlSessionTemplate")
    public SqlSessionTemplate reportSqlSessionTemplate(@Qualifier("reportSqlSessionFactory") SqlSessionFactory sessionFactory) {
        return new SqlSessionTemplate(sessionFactory);
    }
}
