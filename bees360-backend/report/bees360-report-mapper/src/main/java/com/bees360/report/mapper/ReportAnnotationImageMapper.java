package com.bees360.report.mapper;

import com.bees360.entity.ReportAnnotationImage;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ReportAnnotationImageMapper {

    void insertBatch(@Param("reportAnnotationImages") List<ReportAnnotationImage> reportAnnotationImages);

    List<ReportAnnotationImage> listByProjectId(@Param("projectId") long projectId);

    List<String> listImageId(@Param("projectId") long projectId, @Param("reportType") int reportType);

    void deleteByProjectIdAndReportType(@Param("projectId") long projectId, @Param("reportType") int reportType);
}
