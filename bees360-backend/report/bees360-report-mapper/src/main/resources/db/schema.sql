/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `Bidding`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Bidding` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `overview` varchar(5000) DEFAULT NULL,
  `stories` int(11) DEFAULT NULL,
  `roof_primary_pitch` tinyint(2) DEFAULT NULL COMMENT 'the value should be (0-24)/12',
  `roof_perimeter` double DEFAULT NULL,
  `longest_length` double DEFAULT NULL,
  `roof_complexity` int(11) DEFAULT NULL COMMENT 'default unit is ',
  `roof_area` double DEFAULT NULL,
  `roof_boundary` geometry DEFAULT NULL COMMENT 'a polygon representing the edge of the roof',
  `map_rect` geometry DEFAULT NULL COMMENT 'Boundary on Google map.',
  `roof_rect` geometry DEFAULT NULL COMMENT 'the minimum enclosing rectangle of the edge of the roof',
  `company_name` varchar(100) DEFAULT NULL,
  `company_website` varchar(100) DEFAULT NULL,
  `industry_experience` int(11) DEFAULT NULL COMMENT '# of years in roofing industry',
  `shingle_grade` tinyint(4) DEFAULT NULL,
  `shingle_price` decimal(19,2) DEFAULT NULL,
  `high_roof` tinyint(1) DEFAULT NULL COMMENT 'the # of stories is more than 2',
  `high_steep_roof_charge` decimal(19,2) DEFAULT NULL,
  `total_price` decimal(19,2) DEFAULT NULL,
  `intro` varchar(1000) DEFAULT NULL,
  `modified_by` bigint(20) NOT NULL,
  `modified_time` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `modified_by` (`modified_by`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='save the data for square report and bidding';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CustomizedReportElement`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CustomizedReportElement` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `company_id` bigint(20) NOT NULL,
  `element_id` bigint(20) NOT NULL,
  `report_type` tinyint(4) NOT NULL,
  `type` tinyint(4) NOT NULL,
  `element_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'element_name',
  `element_value` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'element value',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `element_id` (`element_id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  CONSTRAINT `CustomizedReportElement_ibfk_1` FOREIGN KEY (`element_id`) REFERENCES `ReportImageElement` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CustomizedReportItem`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CustomizedReportItem` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `report_type` tinyint(4) NOT NULL,
  `item_type` tinyint(4) NOT NULL COMMENT '1:root age,2:roof material,3:damage severity,4:description',
  `value` varchar(2000) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `CustomizedReportItem_ibfk_1` (`project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CustomizedSIBCategory`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CustomizedSIBCategory` (
  `id` bigint(20) NOT NULL,
  `category_type` tinyint(2) NOT NULL COMMENT '0:root,1:subcategory,2:description',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `parent_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `EventHistory`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `EventHistory` (
  `event_id` bigint(20) NOT NULL,
  `project_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `status` int(11) NOT NULL,
  `status_time` bigint(20) NOT NULL,
  `description` varchar(100) DEFAULT '',
  `created_time` bigint(20) NOT NULL,
  `modified_by` bigint(20) NOT NULL,
  PRIMARY KEY (`event_id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `modified_by` (`modified_by`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `House`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `House` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `gps_location` point NOT NULL,
  `country` varchar(50) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `address` varchar(200) DEFAULT NULL,
  `zip_code` varchar(50) DEFAULT NULL,
  `overview` varchar(5000) DEFAULT NULL,
  `stories` int(11) DEFAULT NULL,
  `roof_primary_pitch` tinyint(2) DEFAULT NULL COMMENT 'the value should be (0-24)/12',
  `roof_perimeter` double DEFAULT NULL,
  `longest_length` double DEFAULT NULL,
  `roof_complexity` int(11) DEFAULT NULL COMMENT 'option from [1:Low, 2:]',
  `roof_area` double DEFAULT NULL,
  `roof_boundary` geometry DEFAULT NULL COMMENT 'a polygon representing the edge of the roof',
  `roof_rect` geometry DEFAULT NULL COMMENT 'the minimum enclosing rectangle of the edge of the roof',
  `modified_by` bigint(20) NOT NULL,
  `modified_time` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `modified_by` (`modified_by`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='save the data of the house to reduce the calculation';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `HouseCategory`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `HouseCategory` (
  `id` bigint(20) NOT NULL,
  `selector` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_index` int(4) NOT NULL,
  `category_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subcategory` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subcategory_index` int(4) DEFAULT NULL,
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `additional` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subcategory_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `version` int(10) NOT NULL DEFAULT '1',
  `type` int(8) NOT NULL DEFAULT '0' COMMENT '10:roof, 20: hot roof, 30:elevation, 40 hot elevation',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `HouseCategoryVersion`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `HouseCategoryVersion` (
  `id` bigint(20) NOT NULL,
  `type` tinyint(4) NOT NULL,
  `version` int(8) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `HouseImageSegmentType`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `HouseImageSegmentType` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `is_leaf` tinyint(1) NOT NULL COMMENT 'is leaf,0:false, 1:true',
  `value_type` tinyint(2) DEFAULT NULL COMMENT 'the value type, single election, check etc.',
  `unit` varchar(50) DEFAULT NULL COMMENT 'the input unit.',
  `code_type` tinyint(2) DEFAULT NULL COMMENT 'the code type, structures,direction etc.',
  `version` int(10) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `HouseImageUnderwriting`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `HouseImageUnderwriting` (
  `id` int(11) NOT NULL,
  `description` varchar(100) NOT NULL,
  `tips` varchar(200) DEFAULT NULL COMMENT 'the input unit.',
  `version` int(10) NOT NULL DEFAULT '1',
  `underwriting_type` tinyint(4) NOT NULL DEFAULT '20' COMMENT '10:Elevation,20:ExteriorHazards',
  `hint_image` varchar(256) DEFAULT NULL COMMENT 'hint image',
  `partial_type` int(11) DEFAULT '6' COMMENT 'Image Partial View Type, 1:Roof, 2:Elevation, 3:Interior,4:Garage,5:APS,6:Others',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `HouseSegmentValue`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `HouseSegmentValue` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'the segment value id.',
  `project_id` bigint(20) NOT NULL COMMENT 'the project id.',
  `code` varchar(100) NOT NULL COMMENT 'the segment value code.quot HouseImageSegment id.',
  `value` varchar(500) DEFAULT NULL COMMENT 'the value.',
  `additional` varchar(100) DEFAULT NULL COMMENT 'additional input',
  `value_type` tinyint(2) DEFAULT NULL COMMENT 'the value type, single election, check etc.',
  `code_type` tinyint(2) NOT NULL COMMENT 'the code type, structures,direction etc.',
  `root_id` bigint(20) DEFAULT NULL COMMENT 'the segment tree root id.',
  `is_damage` tinyint(1) DEFAULT NULL COMMENT '0:no damage 1:is damage.',
  `is_required` tinyint(1) DEFAULT '1',
  `type` int(8) NOT NULL DEFAULT '100' COMMENT 'the HouseCategory.type 10:roof, 15: Ridge cap, 20: Drip edge, 25:Valley, 30 Exhaust cap, 35: Pipe jack, 40: Attic vent, 45: Attic vents, 50: Ridge Vents, 55: Flashing, 60: Roof Vents, 65: Ventilator, 100: Elevation, 105: Window screen, 110: Window reglaze, 115: Window beading, 120: Gutters/Downspouts, 125: A/C fins, 130: Fascia, 135: Windows, 200: close up or overview(Unmodifiable)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ImageAnnotation`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ImageAnnotation` (
  `annotation_id` bigint(20) NOT NULL,
  `image_id` bigint(20) NOT NULL,
  `facet_id` int(11) NOT NULL,
  `project_id` bigint(20) NOT NULL,
  `annotation_polygon` geometry NOT NULL,
  `created_time` bigint(20) NOT NULL,
  `center_point` point NOT NULL,
  `annotation_type` int(11) NOT NULL,
  `usage_type` tinyint(4) DEFAULT '1',
  `generated_by` bigint(20) NOT NULL,
  `source_type` int(11) NOT NULL DEFAULT '1' COMMENT 'Annotation source type, 1:Original, 2:Mapping',
  PRIMARY KEY (`image_id`,`annotation_id`) USING BTREE,
  KEY `facet_id` (`facet_id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `generated_by` (`generated_by`) USING BTREE,
  SPATIAL KEY `sidx_image_geometry` (`annotation_polygon`),
  SPATIAL KEY `sidx_gps_location` (`center_point`),
  KEY `annotation_id` (`annotation_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ImageAnnotation2D`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ImageAnnotation2D` (
  `annotation_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `image_id` bigint(20) NOT NULL,
  `project_id` bigint(20) NOT NULL,
  `annotation_polygon` geometry NOT NULL,
  `created_time` bigint(20) NOT NULL,
  `center_point` point NOT NULL,
  `annotation_type` tinyint(4) NOT NULL,
  `usage_type` tinyint(4) DEFAULT '1',
  `generated_by` varchar(50) DEFAULT NULL COMMENT 'ai_user_id',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  `ai_user_id` varchar(50) DEFAULT NULL COMMENT 'ai_user_id',
  `ai_user_name` varchar(100) DEFAULT NULL COMMENT 'ai_user_name',
  PRIMARY KEY (`annotation_id`) USING BTREE,
  KEY `image_id` (`image_id`) USING BTREE,
  KEY `generated_by` (`generated_by`) USING BTREE,
  SPATIAL KEY `sidx_annotation_polygon` (`annotation_polygon`),
  SPATIAL KEY `sidx_center_point` (`center_point`),
  KEY `project_id` (`project_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=684 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ProjectCustomizedInfo`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ProjectCustomizedInfo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `column_code` int(8) NOT NULL,
  `column_value` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ProjectReportFile`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ProjectReportFile` (
  `report_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `report_type` int(11) NOT NULL,
  `report_word_file_name` varchar(256) DEFAULT NULL,
  `report_pdf_file_name` varchar(256) NOT NULL,
  `size` int(11) NOT NULL,
  `created_by` bigint(20) NOT NULL,
  `created_time` bigint(20) NOT NULL,
  `generation_status` tinyint(4) NOT NULL DEFAULT '1',
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`report_id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `created_by` (`created_by`) USING BTREE,
  KEY `report_type` (`report_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10316 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `QRTZ_BLOB_TRIGGERS`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_BLOB_TRIGGERS` (
  `SCHED_NAME` varchar(120) NOT NULL,
  `TRIGGER_NAME` varchar(190) NOT NULL,
  `TRIGGER_GROUP` varchar(190) NOT NULL,
  `BLOB_DATA` mediumblob,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
  KEY `SCHED_NAME` (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
  CONSTRAINT `QRTZ_BLOB_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `QRTZ_TRIGGERS` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `QRTZ_CALENDARS`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_CALENDARS` (
  `SCHED_NAME` varchar(120) NOT NULL,
  `CALENDAR_NAME` varchar(190) NOT NULL,
  `CALENDAR` mediumblob NOT NULL,
  PRIMARY KEY (`SCHED_NAME`,`CALENDAR_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `QRTZ_CRON_TRIGGERS`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_CRON_TRIGGERS` (
  `SCHED_NAME` varchar(120) NOT NULL,
  `TRIGGER_NAME` varchar(190) NOT NULL,
  `TRIGGER_GROUP` varchar(190) NOT NULL,
  `CRON_EXPRESSION` varchar(120) NOT NULL,
  `TIME_ZONE_ID` varchar(80) DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
  CONSTRAINT `QRTZ_CRON_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `QRTZ_TRIGGERS` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `QRTZ_FIRED_TRIGGERS`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_FIRED_TRIGGERS` (
  `SCHED_NAME` varchar(120) NOT NULL,
  `ENTRY_ID` varchar(95) NOT NULL,
  `TRIGGER_NAME` varchar(190) NOT NULL,
  `TRIGGER_GROUP` varchar(190) NOT NULL,
  `INSTANCE_NAME` varchar(190) NOT NULL,
  `FIRED_TIME` bigint(13) NOT NULL,
  `SCHED_TIME` bigint(13) NOT NULL,
  `PRIORITY` int(11) NOT NULL,
  `STATE` varchar(16) NOT NULL,
  `JOB_NAME` varchar(190) DEFAULT NULL,
  `JOB_GROUP` varchar(190) DEFAULT NULL,
  `IS_NONCONCURRENT` varchar(1) DEFAULT NULL,
  `REQUESTS_RECOVERY` varchar(1) DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`,`ENTRY_ID`),
  KEY `IDX_QRTZ_FT_TRIG_INST_NAME` (`SCHED_NAME`,`INSTANCE_NAME`),
  KEY `IDX_QRTZ_FT_INST_JOB_REQ_RCVRY` (`SCHED_NAME`,`INSTANCE_NAME`,`REQUESTS_RECOVERY`),
  KEY `IDX_QRTZ_FT_J_G` (`SCHED_NAME`,`JOB_NAME`,`JOB_GROUP`),
  KEY `IDX_QRTZ_FT_JG` (`SCHED_NAME`,`JOB_GROUP`),
  KEY `IDX_QRTZ_FT_T_G` (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
  KEY `IDX_QRTZ_FT_TG` (`SCHED_NAME`,`TRIGGER_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `QRTZ_JOB_DETAILS`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_JOB_DETAILS` (
  `SCHED_NAME` varchar(120) NOT NULL,
  `JOB_NAME` varchar(190) NOT NULL,
  `JOB_GROUP` varchar(190) NOT NULL,
  `DESCRIPTION` varchar(250) DEFAULT NULL,
  `JOB_CLASS_NAME` varchar(250) NOT NULL,
  `IS_DURABLE` varchar(1) NOT NULL,
  `IS_NONCONCURRENT` varchar(1) NOT NULL,
  `IS_UPDATE_DATA` varchar(1) NOT NULL,
  `REQUESTS_RECOVERY` varchar(1) NOT NULL,
  `JOB_DATA` mediumblob,
  PRIMARY KEY (`SCHED_NAME`,`JOB_NAME`,`JOB_GROUP`),
  KEY `IDX_QRTZ_J_REQ_RECOVERY` (`SCHED_NAME`,`REQUESTS_RECOVERY`),
  KEY `IDX_QRTZ_J_GRP` (`SCHED_NAME`,`JOB_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `QRTZ_LOCKS`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_LOCKS` (
  `SCHED_NAME` varchar(120) NOT NULL,
  `LOCK_NAME` varchar(40) NOT NULL,
  PRIMARY KEY (`SCHED_NAME`,`LOCK_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `QRTZ_PAUSED_TRIGGER_GRPS`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_PAUSED_TRIGGER_GRPS` (
  `SCHED_NAME` varchar(120) NOT NULL,
  `TRIGGER_GROUP` varchar(190) NOT NULL,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `QRTZ_SCHEDULER_STATE`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_SCHEDULER_STATE` (
  `SCHED_NAME` varchar(120) NOT NULL,
  `INSTANCE_NAME` varchar(190) NOT NULL,
  `LAST_CHECKIN_TIME` bigint(13) NOT NULL,
  `CHECKIN_INTERVAL` bigint(13) NOT NULL,
  PRIMARY KEY (`SCHED_NAME`,`INSTANCE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `QRTZ_SIMPLE_TRIGGERS`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_SIMPLE_TRIGGERS` (
  `SCHED_NAME` varchar(120) NOT NULL,
  `TRIGGER_NAME` varchar(190) NOT NULL,
  `TRIGGER_GROUP` varchar(190) NOT NULL,
  `REPEAT_COUNT` bigint(7) NOT NULL,
  `REPEAT_INTERVAL` bigint(12) NOT NULL,
  `TIMES_TRIGGERED` bigint(10) NOT NULL,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
  CONSTRAINT `QRTZ_SIMPLE_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `QRTZ_TRIGGERS` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `QRTZ_SIMPROP_TRIGGERS`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_SIMPROP_TRIGGERS` (
  `SCHED_NAME` varchar(120) NOT NULL,
  `TRIGGER_NAME` varchar(190) NOT NULL,
  `TRIGGER_GROUP` varchar(190) NOT NULL,
  `STR_PROP_1` varchar(512) DEFAULT NULL,
  `STR_PROP_2` varchar(512) DEFAULT NULL,
  `STR_PROP_3` varchar(512) DEFAULT NULL,
  `INT_PROP_1` int(11) DEFAULT NULL,
  `INT_PROP_2` int(11) DEFAULT NULL,
  `LONG_PROP_1` bigint(20) DEFAULT NULL,
  `LONG_PROP_2` bigint(20) DEFAULT NULL,
  `DEC_PROP_1` decimal(13,4) DEFAULT NULL,
  `DEC_PROP_2` decimal(13,4) DEFAULT NULL,
  `BOOL_PROP_1` varchar(1) DEFAULT NULL,
  `BOOL_PROP_2` varchar(1) DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
  CONSTRAINT `QRTZ_SIMPROP_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `QRTZ_TRIGGERS` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `QRTZ_TRIGGERS`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_TRIGGERS` (
  `SCHED_NAME` varchar(120) NOT NULL,
  `TRIGGER_NAME` varchar(190) NOT NULL,
  `TRIGGER_GROUP` varchar(190) NOT NULL,
  `JOB_NAME` varchar(190) NOT NULL,
  `JOB_GROUP` varchar(190) NOT NULL,
  `DESCRIPTION` varchar(250) DEFAULT NULL,
  `NEXT_FIRE_TIME` bigint(13) DEFAULT NULL,
  `PREV_FIRE_TIME` bigint(13) DEFAULT NULL,
  `PRIORITY` int(11) DEFAULT NULL,
  `TRIGGER_STATE` varchar(16) NOT NULL,
  `TRIGGER_TYPE` varchar(8) NOT NULL,
  `START_TIME` bigint(13) NOT NULL,
  `END_TIME` bigint(13) DEFAULT NULL,
  `CALENDAR_NAME` varchar(190) DEFAULT NULL,
  `MISFIRE_INSTR` smallint(2) DEFAULT NULL,
  `JOB_DATA` mediumblob,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`),
  KEY `IDX_QRTZ_T_J` (`SCHED_NAME`,`JOB_NAME`,`JOB_GROUP`),
  KEY `IDX_QRTZ_T_JG` (`SCHED_NAME`,`JOB_GROUP`),
  KEY `IDX_QRTZ_T_C` (`SCHED_NAME`,`CALENDAR_NAME`),
  KEY `IDX_QRTZ_T_G` (`SCHED_NAME`,`TRIGGER_GROUP`),
  KEY `IDX_QRTZ_T_STATE` (`SCHED_NAME`,`TRIGGER_STATE`),
  KEY `IDX_QRTZ_T_N_STATE` (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`,`TRIGGER_STATE`),
  KEY `IDX_QRTZ_T_N_G_STATE` (`SCHED_NAME`,`TRIGGER_GROUP`,`TRIGGER_STATE`),
  KEY `IDX_QRTZ_T_NEXT_FIRE_TIME` (`SCHED_NAME`,`NEXT_FIRE_TIME`),
  KEY `IDX_QRTZ_T_NFT_ST` (`SCHED_NAME`,`TRIGGER_STATE`,`NEXT_FIRE_TIME`),
  KEY `IDX_QRTZ_T_NFT_MISFIRE` (`SCHED_NAME`,`MISFIRE_INSTR`,`NEXT_FIRE_TIME`),
  KEY `IDX_QRTZ_T_NFT_ST_MISFIRE` (`SCHED_NAME`,`MISFIRE_INSTR`,`NEXT_FIRE_TIME`,`TRIGGER_STATE`),
  KEY `IDX_QRTZ_T_NFT_ST_MISFIRE_GRP` (`SCHED_NAME`,`MISFIRE_INSTR`,`NEXT_FIRE_TIME`,`TRIGGER_GROUP`,`TRIGGER_STATE`),
  CONSTRAINT `QRTZ_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) REFERENCES `QRTZ_JOB_DETAILS` (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ReportAnnotationImage`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ReportAnnotationImage` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `image_id` bigint(20) NOT NULL,
  `report_type` int(8) NOT NULL,
  `component_id` tinyint(4) NOT NULL,
  `caption` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'The name of the file that needs to be synchronized to the symbol.',
  `create_time` bigint(20) NOT NULL,
  `is_deleted` tinyint(1) NOT NULL,
  `page` int(8) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=185 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ReportImageElement`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ReportImageElement` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `component_id` tinyint(4) DEFAULT NULL,
  `element_type` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `image_id` bigint(20) NOT NULL,
  `parent_id` bigint(20) DEFAULT NULL,
  `report_type` tinyint(4) NOT NULL,
  `annotation_id` bigint(20) DEFAULT NULL,
  `annotation3d_id` bigint(20) DEFAULT NULL,
  `source_type` tinyint(4) DEFAULT '0' COMMENT '1 bees360 go app image, 0 drone image',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `image_id` (`image_id`) USING BTREE,
  KEY `OnSiteReportImageElement_ibfk_1` (`parent_id`) USING BTREE,
  KEY `OnSiteReportImageElement_ibfk_2` (`annotation_id`) USING BTREE,
  KEY `OnSiteReportImageElement_ibfk_3` (`annotation3d_id`) USING BTREE,
  CONSTRAINT `ReportImageElement_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `ReportImageElement` (`id`),
  CONSTRAINT `ReportImageElement_ibfk_2` FOREIGN KEY (`annotation_id`) REFERENCES `ImageAnnotation2D` (`annotation_id`),
  CONSTRAINT `ReportImageElement_ibfk_3` FOREIGN KEY (`annotation3d_id`) REFERENCES `ImageAnnotation` (`annotation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8492 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ReportMaterial`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ReportMaterial` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `material_type` tinyint(4) NOT NULL COMMENT '0:front template\r\n1:String 2:Damage title String data 3:measurement picture String data 4:measurement table data\r\n10:material 11:measurement length chart 12:measurementarea chart 13:measurement direction image\r\n20: image, 21: report one image, 22: report two image, 23:report close up image 24: report crop image\r\n30: line\r\n40: point\r\n50: background square',
  `position` point DEFAULT NULL,
  `data_source` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `check_is_implement` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_show_damage_type` tinyint(1) DEFAULT NULL,
  `size` int(8) DEFAULT NULL,
  `is_bold` tinyint(1) DEFAULT NULL,
  `color` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'for example (64,64,64) is gray. default is (0, 0, 0) black',
  `align` tinyint(2) DEFAULT NULL COMMENT '0:Element.ALIGN_LEFT, 1: Element.ALIGN_CENTER, 2: Element.ALIGN_RIGHT',
  `material_url` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `width` int(8) DEFAULT NULL,
  `height` int(8) DEFAULT NULL,
  `material_num` int(8) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ReportSummary`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ReportSummary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `report_type` tinyint(4) NOT NULL,
  `summary` varchar(5000) NOT NULL DEFAULT '' COMMENT 'a json type data',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'deletion mark',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='Summary data of report';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ReportTemplate`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ReportTemplate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `company_id` bigint(20) NOT NULL,
  `material_id` bigint(20) NOT NULL,
  `report_types` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'for example 5,7,8 belong to  Preliminary Damage Assessment Report, Highfly Evaluation Report and Real-time Damage Assessment Report, 0 is all report',
  `page_number` int(8) NOT NULL COMMENT 'normal 1: page one, 2:page two, 3: page next',
  `page_type` tinyint(2) NOT NULL COMMENT '1:single page, 2: Multiple page, 3:default page',
  `sort` int(8) NOT NULL,
  `version` int(8) NOT NULL,
  `create_time` bigint(20) NOT NULL,
  `is_delete` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ReportTemplate_ibfk_1` (`company_id`) USING BTREE,
  KEY `ReportTemplate_ibfk_2` (`material_id`) USING BTREE,
  CONSTRAINT `ReportTemplate_ibfk_1` FOREIGN KEY (`material_id`) REFERENCES `ReportMaterial` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `S3FileRecord`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `S3FileRecord` (
  `id` bigint(20) NOT NULL,
  `s3_key` varchar(200) NOT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT '0',
  `create_time` bigint(20) DEFAULT NULL,
  `version_id` int(11) NOT NULL DEFAULT '0',
  `is_deleted` tinyint(2) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `schema_migrations`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `schema_migrations` (
  `version` varchar(255) NOT NULL,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping routines for database 'Bees360Report'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed

--
-- Dbmate schema migrations
--

LOCK TABLES `schema_migrations` WRITE;
INSERT INTO `schema_migrations` (version) VALUES
  ('20191216102830'),
  ('20200109040109'),
  ('20200218125730'),
  ('20200312042657'),
  ('20200330080850'),
  ('20200421083531'),
  ('20200425042650'),
  ('20200608073140'),
  ('20200628031856'),
  ('20200723085027'),
  ('20200729101705'),
  ('20200821072244'),
  ('20200827030344');
UNLOCK TABLES;
