-- migrate:up
-- 重新定义 Electrical Panel, Water Heater, Supply Lines
delete from ImageTagDict where id in (151,152,153);

INSERT INTO ImageTagDict (id, dict_type, code, name, category, type,  sort) VALUES (151, 3, 521, 'Electrical Panel', 6, 5, null);
INSERT INTO ImageTagDict (id, dict_type, code, name, category, type,  sort) VALUES (152, 3, 519, 'Water Heater', 6, 5, null);
INSERT INTO ImageTagDict (id, dict_type, code, name, category, type,  sort) VALUES (153, 3, 520, 'Supply Lines', 6, 5, null);


INSERT INTO ImageTagDict (dict_type, code, name, category, type,  sort) VALUES (3, 522, 'Pool Pump', 6, 5, null);
INSERT INTO ImageTagDict (dict_type, code, name, category, type,  sort) VALUES (3, 523, 'Pool Heater', 6, 5, null);
INSERT INTO ImageTagDict (dict_type, code, name, category, type,  sort) VALUES (3, 524, 'HVAC', 6, 5, null);


-- Location,Component,Damage,Hazard 这些分类按首字母排序
update ImageTagDict t1 inner join
    (select t2.id, @rownum := @rownum + 1 as 'sort' from (
                                                             select id, @rownum := 0
                                                             from ImageTagDict
                                                             where type = 4
                                                             order by name asc
                                                         ) t2) t3
    on t3.id = t1.id
set t1.sort = t3.sort;


update ImageTagDict t1 inner join
    (select t2.id, @rownum := @rownum + 1 as 'sort' from (
                                                             select id, @rownum := 0
                                                             from ImageTagDict
                                                             where type = 5
                                                               and category = 7
                                                             order by name asc
                                                         ) t2) t3
    on t3.id = t1.id
set t1.sort = t3.sort;

update ImageTagDict t1 inner join
    (select t2.id, @rownum := @rownum + 1 as 'sort' from (
                                                             select id, @rownum := 0
                                                             from ImageTagDict
                                                             where type = 5
                                                               and category = 8
                                                             order by name asc
                                                         ) t2) t3
    on t3.id = t1.id
set t1.sort = t3.sort;

update ImageTagDict t1 inner join
    (select t2.id, @rownum := @rownum + 1 as 'sort' from (
                                                             select id, @rownum := 0
                                                             from ImageTagDict
                                                             where type = 5
                                                               and category = 8
                                                             order by name asc
                                                         ) t2) t3
    on t3.id = t1.id
set t1.sort = t3.sort;


-- migrate:down
delete from ImageTagDict where id in (151, 152, 153);
delete from ImageTagDict where code in (522, 523, 524);

INSERT INTO Bees360Report.ImageTagDict (id, dict_type, code, name, category, type,  sort) VALUES (151, 3, 520, 'Electrical Panel', 6, 5, null);
INSERT INTO Bees360Report.ImageTagDict (id, dict_type, code, name, category, type,  sort) VALUES (152, 3, 518, 'Water Heater', 6, 5, null);
INSERT INTO Bees360Report.ImageTagDict (id, dict_type, code, name, category, type,  sort) VALUES (153, 3, 519, 'Supply Lines', 6, 5, null);
