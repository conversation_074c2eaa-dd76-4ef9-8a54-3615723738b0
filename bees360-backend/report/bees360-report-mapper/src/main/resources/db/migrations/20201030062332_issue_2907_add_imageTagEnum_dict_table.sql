-- migrate:up
CREATE TABLE `ImageTagDict` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dict_type` int(4) DEFAULT NULL COMMENT '1:type; 2:category; 3:tag',
  `code` int(4) DEFAULT NULL COMMENT 'tagCode',
  `name` varchar(50) DEFAULT NULL COMMENT 'tagName',
  `category` int(4) DEFAULT NULL COMMENT 'categoryCode, refer id,name',
  `type` int(4)  DEFAULT NULL COMMENT 'typeCode, refer id,name',
  `created_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'data create time',
  `updated_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'data update time',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_ImageTagDict_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='imageTag dict';

/** =======type(大类)========= **/
insert into ImageTagDict(dict_type, code, name, category, type) values (1, 1, 'Object', null, null);
insert into ImageTagDict(dict_type, code, name, category, type) values (1, 2, 'Scope', null, null);
insert into ImageTagDict(dict_type, code, name, category, type) values (1, 3, 'Direction', null, null);
insert into ImageTagDict(dict_type, code, name, category, type) values (1, 4, 'Location', null, null);
insert into ImageTagDict(dict_type, code, name, category, type) values (1, 5, 'Annotation', null, null);

/** =======category(小类)========= **/
insert into ImageTagDict(dict_type, code, name, category, type) values (2, 0, '', null, null);
insert into ImageTagDict(dict_type, code, name, category, type) values (2, 1, 'Roof', null, null);
insert into ImageTagDict(dict_type, code, name, category, type) values (2, 2, 'Sign', null, null);
insert into ImageTagDict(dict_type, code, name, category, type) values (2, 3, 'Exterior', null, null);
insert into ImageTagDict(dict_type, code, name, category, type) values (2, 4, 'Content', null, null);
insert into ImageTagDict(dict_type, code, name, category, type) values (2, 5, 'Interior', null, null);
insert into ImageTagDict(dict_type, code, name, category, type) values (2, 6, 'Component', null, null);
insert into ImageTagDict(dict_type, code, name, category, type) values (2, 7, 'Damage', null, null);
insert into ImageTagDict(dict_type, code, name, category, type) values (2, 8, 'Hazard', null, null);

/** =======tag(标签)========= **/
/** =======Object========= **/
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 100, 'Roof', 1, 1);

insert into ImageTagDict(dict_type, code, name, category, type) values (3, 110, 'Sign', 2, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1101, 'Address Verification', 2, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1102, 'Dog Sign', 2, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1103, 'Historic Home Plaque', 2, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1104, 'Business Sign', 2, 1);

insert into ImageTagDict(dict_type, code, name, category, type) values (3, 120, 'Exterior', 3, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1201, 'Elevation', 3, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1202, 'Detached Garage', 3, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1203, 'Carport', 3, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1204, 'Storage Shed', 3, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1205, 'Fence', 3, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1206, 'Swimming Pool', 3, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1207, 'Pergola', 3, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1208, 'Gazebo', 3, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1209, 'Garden', 3, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1210, 'Foundation', 3, 1);

insert into ImageTagDict(dict_type, code, name, category, type) values (3, 130, 'Content', 4, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1301, 'Trampoline', 4, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1302, 'Basketball Hoop', 4, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1303, 'BBQ Grill', 4, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1304, 'Outdoor Furniture', 4, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1305, 'Outdoor Kitchen', 4, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1306, 'Toys', 4, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1307, 'Debris', 4, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1308, 'Diving Board', 4, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1309, 'Slide', 4, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1310, 'Tree House', 4, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1311, 'Pet', 4, 1);

insert into ImageTagDict(dict_type, code, name, category, type) values (3, 140, 'Interior', 5, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1401, 'Family Room', 5, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1402, 'Living Room', 5, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1403, 'Dining Room', 5, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1404, 'Bedroom', 5, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1405, 'Bathroom', 5, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1406, 'Kitchen', 5, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1407, 'Closet', 5, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1408, 'Corridor', 5, 1);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 1409, 'Staircase', 5, 1);

/** =======Scope========= **/
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 201, 'Overview', 0, 2);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 202, 'Closeup', 0, 2);

/** =======Direction========= **/
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 301, 'Front', 0, 3);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 302, 'Rear', 0, 3);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 303, 'Left', 0, 3);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 304, 'Right', 0, 3);

/** =======Location========= **/
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 401, 'Entry Level', 0, 4);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 402, '2nd Floor', 0, 4);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 403, '3rd Floor', 0, 4);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 404, '4th Floor or higher', 0, 4);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 405, 'Basement', 0, 4);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 406, 'Backyard', 0, 4);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 407, 'Garden Level', 0, 4);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 408, 'Attic', 0, 4);

/** =======Component========= **/
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 501, 'Solar Panel', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 502, 'Skylight', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 503, 'Attic', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 504, 'Satellite Dish', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 505, 'Window', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 506, 'Door', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 507, 'Gutter', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 508, 'Downspout', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 509, 'Eaves', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 510, 'Fascia', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 511, 'Soffit', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 512, 'HAVC', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 513, 'Garage', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 514, 'Breaker Panel', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 515, 'Supply Lines', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 516, 'Water Heater', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 517, 'Fireplace', 6, 5);

/** =======Damage========= **/
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 601, 'Hail Damage', 7, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 602, 'Wind Damage', 7, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 603, 'Non-storm related Damage', 7, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 604, 'Peeling Paint', 7, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 605, 'Missing Railing', 7, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 606, 'Missing Steps', 7, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 607, 'Broken Window', 7, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 608, 'Water Leak', 7, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 609, 'Other Damage', 7, 5);

/** =======HAZARD========= **/
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 701, 'Fireplace on Exterior', 8, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 702, 'Chimney through Roof', 8, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 703, 'Pest Activity', 8, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 704, 'Overgrown Vegetation', 8, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 705, 'Tree/Branch/Bush', 8, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 706, 'Abandoned Vehicle', 8, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 707, 'Cloudy Pool Water', 8, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 708, 'Security Bars', 8, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 709, 'Boarded Window/Door', 8, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 710, 'Wind Protection', 8, 5);


-- migrate:down
drop table ImageTagDict;
