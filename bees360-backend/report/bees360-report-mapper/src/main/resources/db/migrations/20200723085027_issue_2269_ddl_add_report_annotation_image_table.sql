-- migrate:up
CREATE TABLE `ReportAnnotationImage`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `image_id` bigint(20) NOT NULL,
  `report_type` int(8) NOT NULL,
  `component_id` tinyint(4) NOT NULL,
  `caption` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'The name of the file that needs to be synchronized to the symbol.',
  `page` int(8) NOT NULL,
  `create_time` bigint(20) NOT NULL,
  `is_deleted` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- migrate:down
DROP TABLE IF EXISTS `ReportAnnotationImage`;
