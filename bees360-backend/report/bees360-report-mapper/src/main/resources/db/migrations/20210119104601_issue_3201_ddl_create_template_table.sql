-- migrate:up
DROP TABLE IF EXISTS `Template`;
CREATE TABLE `Template`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Template name.',
  `template_key` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Template resource key.',
  `last_modified` bigint(20) NOT NULL COMMENT 'Last modified time.',
  `last_modified_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Last modified person.',
  `owner_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Template owner id.',
  `version` int(11) NULL DEFAULT NULL COMMENT 'Template version.',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Deletion mark',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `TemplateConfig`;
CREATE TABLE `TemplateConfig`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_id` bigint(20) NOT NULL COMMENT 'The ID of the template to which it belongs.',
  `config_key` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Template config resource key.',
  `config_index` int(11) NULL DEFAULT NULL COMMENT 'Template config index.',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 401 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `TemplatePrefab`;
CREATE TABLE `TemplatePrefab`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'The template prefet name.',
  `template_prefab_key` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Template prefab resoource key.',
  `screenshot` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Template prefab screenshot resoource key.',
  `update_time` bigint(20) NOT NULL COMMENT 'Last update time.',
  `update_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Last updated by.',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Deletion mark',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `TemplateScreenshot`;
CREATE TABLE `TemplateScreenshot`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_id` bigint(20) NOT NULL COMMENT 'The ID of the template to which it belongs.',
  `screenshot_key` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Template screenshot resource key.',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 87 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- migrate:down
DROP TABLE IF EXISTS `Template`;
DROP TABLE IF EXISTS `TemplateConfig`;
DROP TABLE IF EXISTS `TemplatePrefab`;
DROP TABLE IF EXISTS `TemplateScreenshot`;
