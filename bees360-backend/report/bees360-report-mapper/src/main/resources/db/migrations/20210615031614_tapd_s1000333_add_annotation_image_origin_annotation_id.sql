-- migrate:up

alter table AnnotationImage add column origin_annotation varchar(64) DEFAULT NULL COMMENT '来源的imageAnnotation';
alter table AnnotationImage add column type int(4) DEFAULT 0 COMMENT '0代表damage,1代表TestSquare';

ALTER TABLE AnnotationImage ADD INDEX AnnotationImage_annotation_key (annotation_key);
ALTER TABLE AnnotationImage ADD INDEX AnnotationImage_image_id (image_id);
ALTER TABLE AnnotationImage ADD INDEX AnnotationImage_origin_annotation (origin_annotation);

-- migrate:down

alter table AnnotationImage drop INDEX AnnotationImage_annotation_key;
alter table AnnotationImage drop INDEX AnnotationImage_image_id;
alter table AnnotationImage drop INDEX AnnotationImage_origin_annotation;

alter table AnnotationImage drop column origin_annotation;
alter table AnnotationImage drop column type;
