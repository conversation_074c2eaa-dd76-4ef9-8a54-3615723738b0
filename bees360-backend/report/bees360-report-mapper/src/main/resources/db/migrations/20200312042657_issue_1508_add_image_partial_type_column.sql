-- migrate:up
ALTER TABLE HouseImageUnderwriting ADD partial_type INTEGER(11) DEFAULT 6 COMMENT 'Image Partial View Type, 1:Roof, 2:Elevation, 3:Interior,4:Garage,5:APS,6:Others';
UPDATE HouseImageUnderwriting SET partial_type=2 WHERE id IN (10,30,50,70);
UPDATE HouseImageUnderwriting SET partial_type=3 WHERE id = 95;
UPDATE HouseImageUnderwriting SET partial_type=4 WHERE id = 185;
UPDATE HouseImageUnderwriting SET partial_type=5 WHERE id = 205;
UPDATE HouseImageUnderwriting SET partial_type=6 WHERE id = 220;

-- migrate:down
ALTER TABLE HouseImageUnderwriting DROP COLUMN partial_type;
