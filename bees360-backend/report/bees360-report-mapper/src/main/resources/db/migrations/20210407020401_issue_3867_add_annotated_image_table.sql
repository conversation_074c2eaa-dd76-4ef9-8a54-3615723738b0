-- migrate:up
CREATE TABLE `AnnotationImage` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                  `image_id` varchar(64) NOT NULL,
                                  `annotation_key` varchar(256) NOT NULL,
                                  `annotated_id` bigint(20) NOT NULL,
                                  `status` int(2) DEFAULT 0 COMMENT '-1代表删除，默认0',
                                  `created_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'data create time',
                                  `updated_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'data update time',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='AnnotationImage';

CREATE TABLE `AnnotatedImage` (
                                  `id` bigint(20) NOT NULL,
                                  `image_id` varchar(64) NOT NULL,
                                  `status` int(2) DEFAULT 0 COMMENT '-1代表删除，默认0',
                                  `created_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'data create time',
                                  `updated_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'data update time',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='AnnotatedImage';

-- migrate:down

drop table if exists AnnotationImage;
drop table if exists AnnotatedImage;
