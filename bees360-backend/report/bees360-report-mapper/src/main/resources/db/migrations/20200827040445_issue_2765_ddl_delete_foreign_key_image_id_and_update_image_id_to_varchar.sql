-- migrate:up
ALTER TABLE ReportImageElement MODIFY COLUMN image_id VARCHAR(256) NOT NULL;
ALTER TABLE ImageAnnotation MODIFY COLUMN image_id VARCHAR(256) NOT NULL;
ALTER TABLE ImageAnnotation2D MODIFY COLUMN image_id VARCHAR(256) NOT NULL;
ALTER TABLE ReportAnnotationImage MODIFY COLUMN image_id VARCHAR(256) NOT NULL;

ALTER TABLE ImageAnnotation ADD INDEX idx_image_id (image_id);

ALTER TABLE ReportAnnotationImage ADD INDEX idx_project_id (project_id);
ALTER TABLE ReportAnnotationImage ADD INDEX idx_image_id (image_id);

-- migrate:down
DROP INDEX idx_image_id ON ImageAnnotation;

DROP INDEX idx_project_id ON ReportAnnotationImage;
DROP INDEX idx_image_id ON ReportAnnotationImage;

ALTER TABLE ReportImageElement MODIFY COLUMN image_id BIGINT(20) NOT NULL;
ALTER TABLE ImageAnnotation MODIFY COLUMN image_id BIGINT(20) NOT NULL;
ALTER TABLE ImageAnnotation2D MODIFY COLUMN image_id BIGINT(20) NOT NULL;
ALTER TABLE ReportAnnotationImage MODIFY COLUMN image_id BIGINT(20) NOT NULL;
