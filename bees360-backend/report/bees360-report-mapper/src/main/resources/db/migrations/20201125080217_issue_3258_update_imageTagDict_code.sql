-- migrate:up
update ImageTagDict set name = 'Ordered Object' where id = 1;

alter table ImageTagDict add column sort int(4) DEFAULT NULL COMMENT '排序字段';


delete from ImageTagDict where dict_type = 3 and type = 1;
delete from ImageTagDict where dict_type = 3 and category = 6 and type = 5;

/** =======Object========= **/

insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1101, 'Address Verification', 2, 1, 1);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1102, 'Elevation', 3, 1, 2);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1104, 'Detached Garage', 3, 1, 4);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1105, 'Carport', 3, 1, 5);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1106, 'Fence', 3, 1, 7);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1107, 'Storage Shed', 3, 1, 8);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1108, 'Guest House', 3, 1, 9);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1109, 'Swimming Pool', 3, 1, 10);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1110, 'Gazebo', 3, 1, 14);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1111, 'Pergola', 3, 1, 15);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1112, 'Garden', 3, 1, 20);

insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 120, 'Content', 3, 1, 21);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1201, 'Breezeway Attached Garage', 4, 1, 3);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1202, 'Diving Board', 4, 1, 11);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1203, 'Slide', 4, 1, 12);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1204, 'Gazebo', 4, 1, 13);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1205, 'Trampoline', 4, 1, 17);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1206, 'Basketball Hoop', 4, 1, 18);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1207, 'Tree House', 4, 1, 19);

insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 130, 'Interior', 5, 1, 6);


insert into ImageTagDict(dict_type, code, name, category, type, sort) values (2, 9, 'Other Structure', null, null, null);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 140, 'Other Structure', 9, 1, 16);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1401, 'Pet', 9, 1, 22);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1402, 'Debris', 9, 1, 23);
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 1403, 'Roof', 9, 1, 24);

insert into ImageTagDict(dict_type, code, name, category, type) values (3, 711, 'Hazards', 8, 5);

insert into ImageTagDict(dict_type, code, name, category, type) values (3, 501, 'Foundation', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 502, 'Family Room', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 503, 'Living Room', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 504, 'Dinner Room', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 505, 'Bedroom', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 506, 'Bathroom', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 507, 'Kitchen', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 508, 'Closet', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 509, 'Corridor', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 510, 'Staircase', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 511, 'Sign', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 512, 'Dog Sign', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 513, 'Business Sign', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 514, 'Historic Home Plaque', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 515, 'BBQ Grill', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 516, 'OutDoor Furniture', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 517, 'Outdoor Kitchen', 6, 5);
insert into ImageTagDict(dict_type, code, name, category, type) values (3, 518, 'Toys', 6, 5);



-- migrate:down

alter table ImageTagDict drop column sort;
