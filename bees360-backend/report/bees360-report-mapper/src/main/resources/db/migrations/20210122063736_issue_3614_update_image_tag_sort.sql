-- migrate:up
delete from ImageTagDict where name = 'Hazards';

insert into ImageTagDict(dict_type, code, name, category, type) values (3, 711, 'Hazards', 8, 5);
update ImageTagDict set sort = 1 where dict_type = 3 and name = 'Address Verification';
update ImageTagDict set sort = 2 where dict_type = 3 and name = 'Roof';
update ImageTagDict set sort = 3 where dict_type = 3 and name = 'Elevation';
insert into ImageTagDict(dict_type, code, name, category, type, sort) values (3, 150, 'Exterior', 3, 1, 4);
update ImageTagDict set sort = 5 where dict_type = 3 and name = 'Interior';
update ImageTagDict set sort = 6 where dict_type = 3 and name = '<PERSON><PERSON>';
update ImageTagDict set sort = 7 where dict_type = 3 and name = 'Storage Shed';
update ImageTagDict set sort = 8 where dict_type = 3 and name = 'Swimming Pool';
update ImageTagDict t1 inner join
(select t2.id, @rownum := @rownum + 1 as 'sort' from (
                                               select id, @rownum := 8
                                               from ImageTagDict
                                               where name not in ('Hazards', 'Address Verification', 'Roof', 'Elevation', 'Exterior', 'Interior',
                                                                  'Fence', 'Storage Shed', 'Swimming Pool')
                                                 and sort is not null
                                               order by name asc
                                           ) t2) t3
on t3.id = t1.id
set t1.sort = t3.sort;

-- migrate:down
