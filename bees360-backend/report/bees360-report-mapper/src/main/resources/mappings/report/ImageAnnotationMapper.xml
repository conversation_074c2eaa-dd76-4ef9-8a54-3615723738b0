<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.report.mapper.ImageAnnotationMapper">

    <resultMap id="ImageAnnotationResult" type="com.bees360.report.entity.ImageAnnotation">
        <id column="annotation_id" jdbcType="BIGINT" property="annotationId" />
        <id column="image_id" jdbcType="VARCHAR" property="imageId" />
        <result column="facet_id" jdbcType="INTEGER" property="facetId" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="annotation_polygon" jdbcType="VARCHAR" property="annotationPolygon" />
        <result column="created_time" jdbcType="BIGINT" property="createdTime" />
        <result column="center_point_x" jdbcType="DOUBLE" property="centerPointX" />
        <result column="center_point_y" jdbcType="DOUBLE" property="centerPointY" />
        <result column="annotation_type" jdbcType="INTEGER" property="annotationType" />
        <result column="usage_type" jdbcType="INTEGER" property="usageType" />
        <result column="generated_by" jdbcType="BIGINT" property="generatedBy" />
        <result column="source_type" jdbcType="INTEGER" property="sourceType" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="report_sort" jdbcType="INTEGER" property="reportSort" />
        <result column="origin_annotation_id" jdbcType="VARCHAR" property="originAnnotationId" />
        <result column="confidence_level" jdbcType="DOUBLE" property="confidenceLevel"/>
    </resultMap>

    <sql id="imageAnnotationColumns">
        annotation_id, image_id, facet_id, project_id, ST_AsText(annotation_polygon) as annotation_polygon,
        created_time, ST_X(center_point) as center_point_x, ST_Y(center_point) as center_point_y, annotation_type,
        usage_type, generated_by, source_type, confidence_level, remark, report_sort, origin_annotation_id
    </sql>

    <sql id="USAGE_DAMAGE_ANNOTATION">1</sql>
    <sql id="USAGE_SCREENSHOT_RACTANGLE">2</sql>
    <sql id="USAGE_TEST_RACTANGLE">3</sql>

    <!-- annotation Tag 旧有接口过滤掉取这部分数据 -->
    <sql id="SOURCE_TYPE_ADD">4</sql>

    <sql id="ProjectImageReplace"> image_id IN
        <foreach collection="imageIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </sql>

    <select id="getById" resultMap="ImageAnnotationResult">
        select <include refid="imageAnnotationColumns" /> from ImageAnnotation
        where annotation_id = #{annotationId}
    </select>

    <select id="getByIds" resultMap="ImageAnnotationResult">
        select <include refid="imageAnnotationColumns" /> from ImageAnnotation
        where annotation_id in
            <foreach collection="annotationIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>

    <select id="listAnnotationsByImageIdAndSourceType" resultMap="ImageAnnotationResult">
        select <include refid="imageAnnotationColumns" /> from ImageAnnotation
        where image_id = #{imageId}
        <if test="sourceType != null">
            and source_type = #{sourceType}
        </if>
    </select>

    <select id="listAnnotationsByImageIdListAndSourceType" resultMap="ImageAnnotationResult">
        select <include refid="imageAnnotationColumns" /> from ImageAnnotation
        where <include refid="ProjectImageReplace" />
        <if test="sourceType != null">
            and source_type = #{sourceType}
        </if>
    </select>

    <select id="listAnnotationTagByProjectIdAndSourceType" resultMap="ImageAnnotationResult">
        select <include refid="imageAnnotationColumns" /> from ImageAnnotation
        where project_id = #{projectId}
        <if test="sourceType != null">
            and source_type = #{sourceType}
        </if>
    </select>

    <select id="getAnnotationByUsageTypes" resultMap="ImageAnnotationResult">
        select <include refid="imageAnnotationColumns" /> from ImageAnnotation
        where
        <choose>
            <when test="imageIds.size() == 1">
                image_id = #{imageIds[0]}
            </when>
            <otherwise>
                image_id in
                <foreach collection="imageIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        <if test="usageType != null">
            and usage_type = #{usageType}
        </if>
        <if test="sourceType != null">
            and source_type = #{sourceType}
        </if>
    </select>

    <select id="getByOriginAnnotationId" resultMap="ImageAnnotationResult">
        select <include refid="imageAnnotationColumns" /> from ImageAnnotation
        where project_id = #{projectId} and origin_annotation_id = #{originAnnotationId}
    </select>

    <select id="getByOriginAnnotationIds" resultMap="ImageAnnotationResult">
        select <include refid="imageAnnotationColumns" /> from ImageAnnotation
        where project_id = #{projectId} and origin_annotation_id in
        <foreach collection="originAnnotationIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="listByProjectIdAndUsageType" resultMap="ImageAnnotationResult">
        select <include refid="imageAnnotationColumns" /> from ImageAnnotation
        where project_id = #{projectId} and usage_type = #{usageType}
    </select>

    <insert id="insertBatchByTagVo" parameterType="java.util.List">
        insert into ImageAnnotation (annotation_id, image_id, facet_id, project_id, annotation_polygon,
        created_time, center_point, annotation_type, usage_type, generated_by, source_type, remark,
        origin_annotation_id, confidence_level)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.annotationId}, #{item.imageId}, #{item.facetId}, #{item.projectId},
            ST_GeomFromText(#{item.annotationPolygon}),
            #{item.createdTime}, Point(#{item.centerPointX}, #{item.centerPointY}),
            #{item.annotationType}, #{item.usageType}, #{item.generatedBy}, #{item.sourceType}, #{item.remark},
            #{item.originAnnotationId}, #{item.confidenceLevel})
        </foreach>
    </insert>

    <delete id="deleteByUsageTypeAndSourceType">
        delete from ImageAnnotation
        where image_id = #{imageId}
        <if test="usageType != null">
            and usage_type = #{usageType}
        </if>
        and source_type = #{sourceType}
    </delete>

    <delete id="batchDelete">
        delete from ImageAnnotation
        where annotation_id in
        <foreach collection="annotationIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <update id="updateImageAnnotationsSort" parameterType="java.util.List">
        update ImageAnnotation
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="report_sort=case" suffix="end,">
                <foreach collection="annotations" item="item" index="index">
                    when annotation_id=#{item.annotationId} then #{item.reportSort}
                </foreach>
            </trim>
        </trim>
        where image_id = #{imageId} and annotation_id in
        <foreach collection="annotations" item="item" index="index" open="(" close=")" separator=",">
            #{item.annotationId}
        </foreach>
    </update>

    <select id="findADAnnotations" resultMap="ImageAnnotationResult">
        select <include refid="imageAnnotationColumns" /> from ImageAnnotation
        where project_id = #{projectId}
        <if test="parameter.damageTypes != null and parameter.damageTypes.size > 0">
            and annotation_type in
            <foreach collection="parameter.damageTypes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.originAnnotationIds != null and parameter.originAnnotationIds.size > 0">
            and origin_annotation_id in
            <foreach collection="parameter.originAnnotationIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and confidence_level <![CDATA[<]]> #{parameter.level}
    </select>

    <delete id="deleteADAnnotations">
        delete from ImageAnnotation
        where project_id = #{projectId}
        <if test="parameter.damageTypes != null and parameter.damageTypes.size > 0">
            and annotation_type in
            <foreach collection="parameter.damageTypes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.originAnnotationIds != null and parameter.originAnnotationIds.size > 0">
            and origin_annotation_id in
            <foreach collection="parameter.originAnnotationIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and confidence_level <![CDATA[<]]> #{parameter.level}
    </delete>

    <select id="listByProjectIdAndAnnotationTypeAndSourceType" resultMap="ImageAnnotationResult">
        select <include refid="imageAnnotationColumns" /> from ImageAnnotation
        where project_id = #{projectId}
        <if test="annotationType != null and annotationType.size > 0">
            and annotation_type in
            <foreach collection="annotationType" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="sourceType != null and sourceType.size > 0">
            and source_type in
            <foreach collection="sourceType" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getByAnnotationId" resultMap="ImageAnnotationResult">
        select <include refid="imageAnnotationColumns" /> from ImageAnnotation
        where annotation_id in
        <foreach collection="annotationIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
