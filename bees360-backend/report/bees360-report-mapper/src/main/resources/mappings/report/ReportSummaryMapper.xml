<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bees360.report.mapper.ReportSummaryMapper">

    <resultMap id="baseResultMap" type="com.bees360.entity.ReportSummary">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="report_type" jdbcType="BIGINT" property="reportType" />
        <result column="summary" jdbcType="LONGVARCHAR" property="summary" />
        <result column="deleted" jdbcType="BIGINT" property="deleted" />
    </resultMap>

    <sql id="baseColumns">
        id, project_id, report_type, summary, deleted
    </sql>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into ReportSummary(project_id, report_type, summary, deleted)
        values (#{projectId}, #{reportType}, #{summary}, #{deleted});
    </insert>

    <update id="update">
        update ReportSummary set project_id = #{projectId}, report_type = #{reportType},
            summary = #{summary} where id = #{id};
    </update>

    <select id="listByProjectId" resultMap="baseResultMap">
        select <include refid="baseColumns" /> from ReportSummary
        where project_id = #{projectId} and deleted = 0;
    </select>

    <select id="getOne" resultMap="baseResultMap">
        select <include refid="baseColumns" /> from ReportSummary
        where project_id = #{projectId} and report_type = #{reportType} and deleted = 0;
    </select>
</mapper>
