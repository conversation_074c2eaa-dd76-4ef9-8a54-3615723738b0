package com.bees360.securityfirst.config;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.ProjectCreation;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.event.ClientReceivedOnResourceDeleted;
import com.bees360.mapper.ProjectCreationMapper;
import com.bees360.openapi.config.SecurityFirstResourceExporterConfig;
import com.bees360.openapi.securityfirst.SecurityFirstResourceProvider;
import com.bees360.resource.Resource;
import com.bees360.resource.transfer.ResourceTransferService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.listener.project.TransferReportOnProjectReturnToClient;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.service.properties.Bees360CompanyConfig.CompanyConfigItem;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.util.CollectionUtils;

import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Log4j2
@Import({
    SecurityFirstResourceExporterConfig.class,
})
@Configuration
@ConditionalOnProperty(prefix = "resource.app.transfer.resource-pool-security-first", name = "name")
public class SecurityFirstConfig {

    @Value("${resource.app.transfer.resource-pool-security-first.name}")
    private String securityFirstTransferName;

    @Bean
    SecurityFirstResourceProvider securityFirstResourceProvider(
        Function<String, Iterable<String>> exportableReportProvider,
        Function<String, Resource> summaryResourceProvider,
        Function<String, Resource> reportResourceProvider) {
        return new SecurityFirstResourceProvider(
            exportableReportProvider, summaryResourceProvider, reportResourceProvider);
    }

    @Bean
    TransferReportOnProjectReturnToClient securityFirstTransferResourceOnProjectReturnToClient(
        ResourceTransferService securityFirstResourceTransferService,
        Predicate<Long> securityFirstTransferPredicate) {

        return new TransferReportOnProjectReturnToClient(
            securityFirstResourceTransferService,
            securityFirstTransferPredicate);
    }

    @Bean
    Predicate<Long> securityFirstTransferPredicate(
            Predicate<Long> underwritingProjectTargetCompanyPredicate,
            @Qualifier("onlyOpenApiProjectPredicate") Optional<Predicate<Long>> onlyOpenApiProjectPredicate) {
        return onlyOpenApiProjectPredicate.orElse(id -> true)
            .and(underwritingProjectTargetCompanyPredicate);
    }

    /**
     * only the project with underwriting type and belongs to the companies could be transferred.
     */
    @Bean
    Predicate<Long> underwritingProjectTargetCompanyPredicate(
        ProjectService projectService,
        Bees360CompanyConfig bees360CompanyConfig) {

        final var companies = bees360CompanyConfig.getCompanies().stream()
            .filter(c -> StringUtils.equals(securityFirstTransferName, c.getTransferName()))
            .map(CompanyConfigItem::getId)
            .collect(Collectors.toSet());

        return (projectId) -> {
            var project = projectService.getById(projectId);
            var isUnderwriting = Optional.ofNullable(project.getServiceType())
                .map(ProjectServiceTypeEnum::getEnum)
                .map(ProjectServiceTypeEnum::getInspectionPurposeType)
                .map(InspectionPurposeTypeEnum.UNDERWRITING::equals)
                .orElse(false);
            if (!isUnderwriting) {
                log.info("Ignore to transfer resource since the service type {} of project {} isn't underwriting.",
                    project.getServiceType(), project.getProjectId());
                return false;
            }
            Set<Long> managerCompanies = null;
            try {
                managerCompanies = projectService.getProjectManageCompany(projectId);
            } catch (ServiceException e) {
                throw new IllegalStateException(e);
            }
            return CollectionUtils.containsAny(managerCompanies, companies);
        };
    }

    @Bean
    @ConditionalOnProperty(
        prefix = "bees360.feature.security-first.sftp.filter-only-openapi",
        name = "enabled",
        havingValue = "true")
    public Predicate<Long> onlyOpenApiProjectPredicate(ProjectCreationMapper creationMapper) {
        return (projectId) -> {
            var creation = creationMapper.getByProjectId(projectId);
            return Optional.ofNullable(creation)
                .map(ProjectCreation::getCreationChannel)
                .map(c -> CreationChannelType.OPENAPI.name().equals(c))
                .orElse(false);
        };
    }

    /**
     * listener to the deletion event of resource pool for security first and set the project status to client received.
     */
    @Bean
    ClientReceivedOnResourceDeleted securityFirstClientReceivedOnResourceDeleted(
        ProjectService projectService,
        ProjectStatusService projectStatusService) {
        return new ClientReceivedOnResourceDeleted(
            SecurityFirstResourceExporterConfig.SECURITY_FIRST_RESOURCE_POOL_EVENT_PREFIX,
            projectService,
            projectStatusService);
    }

}
