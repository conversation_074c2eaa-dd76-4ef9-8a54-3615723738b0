BEES360 NGINX
===

## 安装NGINX
	install:
	sudo apt-get install nginx

## 路由设计

	# index page
	location / {
	        try_files $uri $uri/ /main.html;
	}

	# backend upsteam
	location /bees360-web {
		try_files $uri @bees360web;
	}

	location @bees360web {
        internal;
        proxy_pass http://************:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_read_timeout 600s;
        proxy_set_header  Host $host;
        proxy_set_header  X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header  X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_redirect http://$host:8080 https://$host;
    }


## 修改配置文件

	vim /etc/nginx/sites-available/default

	#setting server config item

	# setting https and remove "listen 80"
	listen 443 ssl default_server;
    listen [::]:443 ssl default_server;

    ssl on;
	server_name test.bees360.com;
    ssl_certificate /etc/letsencrypt/live/test.bees360.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/test.bees360.com/privkey.pem;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;

	#modify site root directory
	root /home/<USER>/git_repository/bees360_deploy/client/dist;

	location / {
	        try_files $uri $uri/ /main.html;
	}

	# config backend upsteam
	location /bees360-web {
		try_files $uri @bees360web;
	}

	location @bees360web {
        internal;
        proxy_pass http://************:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_read_timeout 600s;
        proxy_set_header  Host $host;
        proxy_set_header  X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header  X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_redirect http://$host:8080 https://$host;
    }
