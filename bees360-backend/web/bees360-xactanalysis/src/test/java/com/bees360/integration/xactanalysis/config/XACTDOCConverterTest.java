package com.bees360.integration.xactanalysis.config;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.bees360.util.Xmls;
import com.bees360.integration.xactanalysis.callback.assignment.XACTDOC;

import lombok.SneakyThrows;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;

public class XACTDOCConverterTest {

    @SneakyThrows
    @Test
    void testXactdocXX() {
        var data = IOUtils.resourceToString("xml/export-assignment.XML", StandardCharsets.UTF_8, this.getClass().getClassLoader());
        var xactdoc = Xmls.toObject(new ByteArrayInputStream(data.getBytes(StandardCharsets.UTF_8)), XACTDOC.class);
        assertNotNull(xactdoc);
        assertNotNull(xactdoc.getXACTNETINFO());
        assertEquals("XACTNET.QUEUE", xactdoc.getXACTNETINFO().getRecipientsXNAddress());
    }
}
