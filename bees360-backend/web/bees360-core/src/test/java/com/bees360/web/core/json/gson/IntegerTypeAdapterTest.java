package com.bees360.web.core.json.gson;

import com.bees360.web.core.json.gson.IntegerTypeAdapter;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.jupiter.api.Test;

import lombok.Data;
import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
public class IntegerTypeAdapterTest {

    @Data
    public static class DataValue {
        private Integer integerNull;
        private Integer integerV;
        private int intZero;
        private int intV;
    }

    @Test
    public void convertDouble() {
        GsonBuilder gb = new GsonBuilder();
        gb.registerTypeAdapter(Integer.class, new IntegerTypeAdapter());
        gb.registerTypeAdapter(int.class, new IntegerTypeAdapter());
        Gson gson = gb.create();

        String json = "{\"integerNull\":\"\",\"integerV\":\"123 abc\",\"intZero\":\"\",\"intV\":\"456 def\"}";
        DataValue dataValue = gson.fromJson(json, DataValue.class);
        // System.out.println(dataValue);
        assertNull(dataValue.integerNull);
        assertEquals("123", "" + dataValue.integerV);
        assertEquals(0, dataValue.intZero);
        assertEquals(456, dataValue.intV);
    }
}
