package com.bees360.web.core.validation;

import com.github.javafaker.Faker;
import com.github.javafaker.PhoneNumber;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import java.util.Arrays;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 */
public class PhoneFormatterTest {

    PhoneValidator phoneValidator = PhoneValidator.getDefaultInstance();

    @Test
    public void formatPhone_usPhoneWithoutSign() {
        Faker faker = new Faker(Locale.US);
        PhoneNumber phoneNumber = faker.phoneNumber();
        int times = 20;
        while (times > 0) {
            String oriPhone = phoneNumber.cellPhone();
            String phone = phoneValidator.formatPhone(oriPhone);
            // System.out.println(oriPhone + " -> " + phone);
            assertformatMatch(phone);
            times--;
        }
    }

    @Test
    public void formatPhone_usPhoneWithSign() {
        Faker faker = new Faker(Locale.US);
        PhoneNumber phoneNumber = faker.phoneNumber();
        int times = 20;
        while (times > 0) {
            String oriPhone = phoneNumber.cellPhone();
            if (oriPhone.startsWith("1-")) {
                // 不测试以1-国家区号开始的格式，后面会重新添加国家区号
                continue;
            }
            oriPhone = "+1 " + oriPhone;
            String phone = phoneValidator.formatPhone(oriPhone);
            assertformatMatch(phone);
            times--;
        }
    }

    @Test
    public void formatPhone_usPhoneWithSignWithoutSpace() {
        Faker faker = new Faker(Locale.US);
        PhoneNumber phoneNumber = faker.phoneNumber();
        int times = 20;
        while (times > 0) {
            String oriPhone = phoneNumber.cellPhone();
            if (oriPhone.startsWith("1-")) {
                // 不测试以1-国家区号开始的格式，后面会重新添加国家区号
                continue;
            }
            oriPhone = "+1" + oriPhone;
            String phone = phoneValidator.formatPhone(oriPhone);
            // System.out.println(oriPhone + " -> " + phone);
            // assertformatMatch(phone);
            times--;
        }
    }

    @Test
    public void formatPhone_specialFormat() {
        String oriPhone = "************";
        String phone = phoneValidator.formatPhone(oriPhone);
        assertEquals("*************", phone);
        assertformatMatch(phone);

        oriPhone = "847 (400) (7027)";
        phone = phoneValidator.formatPhone(oriPhone);
        assertEquals("*************", phone);
        assertformatMatch(phone);

        oriPhone = "847 (HELP) (707)";
        phone = phoneValidator.formatPhone(oriPhone);
        assertEquals("*************", phone);
        assertformatMatch(phone);

        oriPhone = "****** (HELP) (707)--";
        phone = phoneValidator.formatPhone(oriPhone);
        assertEquals("*************", phone);
        assertformatMatch(phone);
    }

    @Test
    public void formatPhoneWithExtensionNumber() {
        // 如下号码中为 ******8312996 227的不同表达方式，其中的227为座机号
        String[] phones = {"******8312996x227", "******8312996 x227", "****** 831 2996 x227"};
        String phoneExpected = "******8312996x227";

        Arrays.stream(phones).forEach(p -> assertEquals(phoneExpected, phoneValidator.formatPhoneOrEmpty(p)));

        String phone = "******8312996227";
        // 不会自动分析出分机号
        assertEquals(phone, phoneValidator.formatPhoneOrEmpty(phone));
    }

    @Test
    public void testFormatChinaPhone() {
        String randomPhone =  "19171897826";
        PhoneValidator chinaPhoneValidator = PhoneValidator.getChinaPhoneValidator();
        Assertions.assertFalse(chinaPhoneValidator.isValid(randomPhone));
        String formatPhone = chinaPhoneValidator.formatPhone(randomPhone);
        Assertions.assertTrue(chinaPhoneValidator.isValid(formatPhone));
        Assertions.assertEquals("+86 " + randomPhone, chinaPhoneValidator.formatPhone(randomPhone));
    }

    @Test
    public void testFormatOnFormattedPhone() {
        String randomPhone = "1" + RandomStringUtils.randomNumeric(10);
        PhoneValidator chinaPhoneValidator = PhoneValidator.getChinaPhoneValidator();
        String formatPhone = chinaPhoneValidator.formatPhone(randomPhone);
        Assertions.assertEquals(formatPhone, chinaPhoneValidator.formatPhone(formatPhone));
    }

    private void assertformatMatch(String phone) {
        assertTrue(phone.matches("\\+1 [\\d\\w]+"));
    }
}
