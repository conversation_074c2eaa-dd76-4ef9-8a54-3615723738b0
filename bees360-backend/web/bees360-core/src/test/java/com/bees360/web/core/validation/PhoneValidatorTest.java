package com.bees360.web.core.validation;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import org.testng.annotations.Test;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;

import com.github.javafaker.Faker;

import static org.junit.jupiter.api.Assertions.assertTrue;
import com.github.javafaker.PhoneNumber;

/**
 * <AUTHOR>
 */
public class PhoneValidatorTest {

    private PhoneValidator validator = PhoneValidator.getDefaultInstance();

    @Test
    void testIsValid_us() {
        // 美国号码
        Faker faker = new Faker(Locale.US);
        // 没有国家区号开头
        for (String phone : phones(faker.phoneNumber(), 50)) {
            // 没有国家区号，默认会按照美国手机号码处理
            boolean valid = validator.isValidFormatted(phone);
//             System.out.println(phone + ": " + valid);
            assertTrue(valid);
        }
    }

    @Test
    void testIsValid_cn() {
        // 中国号码
        Faker faker = new Faker(Locale.CHINA);
        for (String phone : phones(faker.phoneNumber(), 50, 86)) {
            boolean valid = validator.isValid(phone);
            // System.out.println(phone + ": " + valid);
            assertTrue(valid);
        }
    }

    private List<String> phones(PhoneNumber phoneNumber, int num) {
        List<String> phones = new ArrayList<>();
        while (num > 0) {
            String phone = phoneNumber.cellPhone();
            if (phone.startsWith("1-")) {
                // 美国的手机号码格式中有一个会以国家区号开头
                continue;
            }
            phones.add(phone);
            num--;
        }
        return phones;
    }

    private List<String> phones(PhoneNumber phoneNumber, int num, int countryCode) {
        List<String> phones = new ArrayList<>();
        String countryCodePrefix = countryCode + "-";
        while (num > 0) {
            String phone = phoneNumber.cellPhone();
            if (phone.startsWith(countryCodePrefix)) {
                // 移除含有国家区码的结果
                continue;
            }
            phones.add("+" + countryCode + " " + phone);
            num--;
        }
        return phones;
    }

    @Test
    void testValidPhoneWithExtensionNumber() {
        // 如下号码中为 ************* 227的不同表达方式，其中的227为座机号
        String[] phones = {"*************x227", "************* x227", "******* 312 996x227", "+1 (913)831-2996x227"};

        Arrays.stream(phones).forEach(p -> assertTrue(validator.isValidFormatted(p)));

        assertTrue(validator.isValidFormatted("*************x227"));
    }

    private PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();

    @Test
    void testValidSpecialPhone() throws NumberParseException {
        String[] phones = {"18004-A-Child", "18004224453", "+86 18004224453", "+86 8004224453", "**************","**************", "**************",
            "18444163593", "1234567", "180042244533", "918004224453"};

        for (String phone : phones) {
            boolean result = validator.isValid(phone);
            System.out.println(phone + ": " + result);
        }
        System.out.println("-----------");
        for (String phone : phones) {
            System.out.println(phone + ": " + validator.formatPhoneOrEmpty(phone));
        }
        System.out.println("-----------");
        for (String phone : phones) {
            System.out.println(phone + ": " + phoneNumberUtil.parse(phone, "US"));
        }
    }

}
