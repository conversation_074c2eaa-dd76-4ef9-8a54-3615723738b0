package com.bees360.web.core.json.gson;

import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.JsonAdapter;

import lombok.Data;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR> Yang
 */
public class MapToStringListTypeAdapterTest {

    @Test
    public void deserialize() {
        GsonBuilder gb = new GsonBuilder();
        gb.registerTypeAdapter(Double.class, new DoubleTypeAdapter());
        Gson gson = gb.create();

        final String json =
            "{\"names\":{\"0\":\"Front\",\"undefined\":\"Left\"},\"rights\":[\"a\"],\"empty\":[],\"values\":[1,2,3],\"items\":[{\"name\":\"haha\"}],\"dVaule\":\"78d\"}";
        DataValue dataValue = gson.fromJson(json, DataValue.class);
        // System.out.println(dataValue);
        // System.out.println(gson.toJson(dataValue));
        assertEquals(Arrays.asList("Front", "Left"), dataValue.names);
        assertEquals(Arrays.asList("a"), dataValue.getRights());
        assertEquals(Arrays.asList(), dataValue.getEmpty());
        assertEquals(Arrays.asList(1, 2, 3), dataValue.values);
        assertEquals(1, dataValue.items.size());
        assertEquals(78, dataValue.dVaule, 0);
    }

    @Test
    public void deserializeNullInList() {
        GsonBuilder gb = new GsonBuilder();
        gb.registerTypeAdapter(Double.class, new DoubleTypeAdapter());
        Gson gson = gb.create();

        final String json =
            "{\"names\":{\"0\":\"Front\",\"undefined\":\"Left\", \"nullValue\": null},\"rights\":[\"a\", null]}";
        DataValue dataValue = gson.fromJson(json, DataValue.class);
        System.out.println(dataValue);
        System.out.println(gson.toJson(dataValue));
        assertEquals(Arrays.asList("Front", "Left"), dataValue.names);
        assertEquals(Arrays.asList("a"), dataValue.getRights());
    }

    @Test
    public void deserializeWithStrangeValue() {
        GsonBuilder gb = new GsonBuilder();
        gb.registerTypeAdapter(Double.class, new DoubleTypeAdapter());
        Gson gson = gb.create();

        final String json =
            "{\"names\":{\"0\":\"Front\",\"undefined\":12,\"nullValue\": null,\"double\":33.33,\"bool\":true},\"rights\":[\"a\", null]}";
        DataValue dataValue = gson.fromJson(json, DataValue.class);
        System.out.println(dataValue);
        System.out.println(gson.toJson(dataValue));
        assertEquals(Arrays.asList("Front", "12", "33.33", "true"), dataValue.names);
        assertEquals(Arrays.asList("a"), dataValue.getRights());
    }

    @Data
    public static class DataValue {
        @JsonAdapter(MapToStringListTypeAdapter.class)
        private List<String> names;
        @JsonAdapter(MapToStringListTypeAdapter.class)
        private List<String> rights;
        @JsonAdapter(MapToStringListTypeAdapter.class)
        private List<String> empty;
        private List<Integer> values;
        private List<DataValueItem> items;
        private Double dVaule;
    }

    @Data
    public static class DataValueItem {
        private String name;
    }
}
