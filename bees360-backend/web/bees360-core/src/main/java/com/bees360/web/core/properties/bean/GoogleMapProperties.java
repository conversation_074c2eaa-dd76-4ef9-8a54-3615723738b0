package com.bees360.web.core.properties.bean;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2019/12/14 14:30
 */
@Data
@NoArgsConstructor
@Component
@ConfigurationProperties("google.maps")
public class GoogleMapProperties {

    @Data
    @NoArgsConstructor
    public static class Access {
        /**
         * 如果在深圳本地测试，请求配置为true，如果连接vpn，配置为false。连接vpn时失败，则配置为true进行尝试。
         * 如果异常：java.net.ConnectException: Failed to connect to /0.0.0.0:8123，则表示代理问题。
         */
        private boolean proxy;

        @NotBlank
        private String apiKey;
    }

    @NonNull
    private Access access;

    /**
     * 系统启动时，尝试是否能够正常查询，默认不启动
     */
    private boolean testConnect;
}
