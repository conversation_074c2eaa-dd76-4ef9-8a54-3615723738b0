package com.bees360.web.core.properties.bean;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import lombok.NoArgsConstructor;

import lombok.Data;

import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @date 2019/12/11 16:29
 */
@Data
@NoArgsConstructor
public class CorsProperties {

    private static final String SEPARATOR = ",";
    private static final String ALL = "*";
    private static final long DEFAULT_MAX_AGE = 10 * 60L;

    /**
     * 后端放行的设置
     */
    @Data
    @NoArgsConstructor
    public static class Allowed {
        private List<String> origins = Arrays.asList(ALL);
        private List<String> methods = Arrays.asList(ALL);
        private List<String> headers = Arrays.asList(ALL);
        /**
         * 是否发送 Cookie 到后端，当origins中含有'*'时，不可设置为true。
         */
        private boolean credentials = false;

        public void setOrigins(String origins) {
            this.origins = splitList(origins);
        }

        public void setOrigins(List<String> origins) {
            this.origins = nullToEmpty(origins);
        }

        public void setMethods(String methods) {
            this.methods = splitList(methods);
        }

        public void setMethods(List<String> methods) {
            this.methods = nullToEmpty(methods);
        }

        public void setHeaders(String headers) {
            this.headers = splitList(headers);
        }

        public void setHeaders(List<String> headers) {
            this.headers = nullToEmpty(headers);
        }
    }

    /**
     * 后端返回给客户端
     */
    @Data
    @NoArgsConstructor
    public static class Exposed {
        private List<String> headers = Arrays.asList(ALL);

        public void setHeaders(String headers) {
            this.headers = splitList(headers);
        }

        public void setHeaders(List<String> headers) {
            this.headers = nullToEmpty(headers);
        }
    }

    /**
     * 后端放行的设置
     */
    private Allowed allowed = new Allowed();
    /**
     * 后端返回给客户端
     */
    private Exposed exposed = new Exposed();
    /**
     * 预检请求的有效期，单位为秒。 cors.preflight.maxage
     */
    private long maxAge = DEFAULT_MAX_AGE;

    private static List<String> splitList(String list) {
        if (ObjectUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(Arrays.asList(list.split(SEPARATOR)));
    }

    private static <T> List<T> nullToEmpty(List<T> list) {
        return list == null ? new ArrayList<>() : list;
    }
}
