package com.bees360.web.core.properties.bean;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2019/12/10 16:10
 */
@Data
@NoArgsConstructor
public class Bees360ServerProperties {
    /**
     * 后端所在的域名
     */
    @NotBlank
    private String host;
    /**
     * 客户端的域名
     */
    @NotBlank
    private String webHost;
}
