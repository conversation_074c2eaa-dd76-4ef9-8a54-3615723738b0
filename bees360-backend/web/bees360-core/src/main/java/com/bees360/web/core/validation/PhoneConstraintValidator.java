package com.bees360.web.core.validation;

import lombok.extern.slf4j.Slf4j;

import jakarta.validation.ConstraintValidator;

import org.springframework.util.ObjectUtils;
import jakarta.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 */
@Slf4j
public class PhoneConstraintValidator implements ConstraintValidator<Phone, String> {

    public static PhoneValidator validator = PhoneValidator.getDefaultInstance();

    private boolean validAfterFormatted;

    @Override
    public void initialize(Phone phone) {
        this.validAfterFormatted = phone.formatted();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (ObjectUtils.isEmpty(value)) {
            return true;
        }
        if (validAfterFormatted) {
            return validator.isValidFormatted(value);
        }
        return validator.isValid(value);
    }
}
