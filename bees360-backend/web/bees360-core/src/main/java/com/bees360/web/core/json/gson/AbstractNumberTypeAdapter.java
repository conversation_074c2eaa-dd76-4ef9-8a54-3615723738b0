package com.bees360.web.core.json.gson;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractNumberTypeAdapter<T extends Number> extends TypeAdapter<T> {

    private static Double readFirstDouble(JsonReader reader) throws IOException {
        if (reader.peek() == JsonToken.NULL) {
            reader.nextNull();
            return null;
        }
        String stringValue = reader.nextString();
        Double d = parseNumber(stringValue);
        return d;
    }

    private static Double parseNumber(String stringValue) {
        if (StringUtils.isEmpty(stringValue)) {
            return null;
        }
        // 转化为 23,456 为 23456
        stringValue = stringValue.replaceAll(",", "");
        // 提取第一个发现的double数值
        String regex = "[0-9]+(\\.[0-9]+)?";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(stringValue);
        if (!m.find()) {
            return null;
        }
        String stringDouble = m.group();
        try {
            Double value = Double.valueOf(stringDouble);
            return value;
        } catch (NumberFormatException e) {
            log.debug("fail to convert string `{}` to Double value.", stringDouble, e);
            return null;
        }
    }

    @Override
    public T read(JsonReader reader) throws IOException {
        Double d = readFirstDouble(reader);
        return d == null ? null : convertDouble(d);
    }

    protected abstract T convertDouble(double d);

    @Override
    public void write(JsonWriter writer, T value) throws IOException {
        if (value == null) {
            writer.nullValue();
            return;
        }
        writer.value(value);
    }
}
