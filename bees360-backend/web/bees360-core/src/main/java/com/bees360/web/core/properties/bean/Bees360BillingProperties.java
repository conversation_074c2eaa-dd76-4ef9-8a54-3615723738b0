package com.bees360.web.core.properties.bean;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/12/10 16:02
 */
@Data
@NoArgsConstructor
public class Bees360BillingProperties {

    @Data
    public static class BillingRecipient {

        private String addressLine1;

        private String addressLine2;

        private String city;

        private String state;

        private String zipCode;

        private String country;

        private String phone;

        private String email;

        private String companyName;

        private String website;

        private String logo;
    }

    @Data
    public static class BillingInvoice {
        private String description;
    }
    @NonNull
    private BillingRecipient recipient;
    @NonNull
    private BillingInvoice invoice;
}
