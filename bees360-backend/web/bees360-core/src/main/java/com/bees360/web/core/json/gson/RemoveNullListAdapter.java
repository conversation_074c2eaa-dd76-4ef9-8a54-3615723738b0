package com.bees360.web.core.json.gson;

import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import java.lang.reflect.Type;
import java.util.List;

public class RemoveNullListAdapter<T> implements JsonDeserializer<List<T>>, JsonSerializer<List<T>> {
    @Override
    public List<T> deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {

        if (json.isJsonArray()){
            JsonArray jsonArray = new JsonArray();
            json.getAsJsonArray().forEach(jsonElement -> {
                if (jsonElement.isJsonNull()){
                    return;
                }
                jsonArray.add(jsonElement);
            });
            return context.deserialize(jsonArray, typeOfT);
        }

        return context.deserialize(json, typeOfT);
    }

    @Override
    public JsonElement serialize(List<T> src, Type typeOfSrc, JsonSerializationContext context) {
        return context.serialize(src);
    }
}
