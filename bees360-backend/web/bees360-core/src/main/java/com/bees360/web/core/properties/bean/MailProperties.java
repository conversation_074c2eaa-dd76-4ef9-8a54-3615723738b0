package com.bees360.web.core.properties.bean;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Valid
@Component
@ConfigurationProperties("mail")
public class MailProperties {

    @NotNull
    private Map<String, @NotNull List<@Email @NotBlank String>> topicRecipients = new HashMap<>();

    private Set<String> templateUsingNewSender = new HashSet<>();
}
