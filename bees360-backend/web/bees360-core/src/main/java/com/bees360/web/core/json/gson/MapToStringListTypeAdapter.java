package com.bees360.web.core.json.gson;

import java.lang.reflect.Type;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

/**
 * 将对象转化为String List，目的是为了兼容 {"0": "Left", "2": "Right"} -> ["Left", "Right"]
 * 注意：该TypeAdpter会将列表中的null值移除。
 *
 * <AUTHOR> Yang
 */
public class MapToStringListTypeAdapter implements JsonDeserializer<List<String>>, JsonSerializer<List<String>> {

    @Override
    public List<String> deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
        throws JsonParseException {
        List<String> result = Collections.emptyList();
        if (json.isJsonNull()) {
            return result;
        }
        if (json.isJsonArray()) {
            result = context.deserialize(json, typeOfT);
        }
        if (json.isJsonObject()) {
            JsonObject jsonObject = json.getAsJsonObject();
            result = jsonObject.entrySet().stream().map(e -> {
                JsonElement jele = e.getValue();
                if (jele.isJsonNull()) {
                    return null;
                }
                return jele.getAsString();
            }).collect(Collectors.toList());
        }
        return removeEmpty(result);
    }

    private List<String> removeEmpty(List<String> list) {
        return list.stream().filter(e -> e != null).collect(Collectors.toList());
    }

    @Override
    public JsonElement serialize(List<String> src, Type typeOfSrc, JsonSerializationContext context) {
        return context.serialize(src);
    }
}
