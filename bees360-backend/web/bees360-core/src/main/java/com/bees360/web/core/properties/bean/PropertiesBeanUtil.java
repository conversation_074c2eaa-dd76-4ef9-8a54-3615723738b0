package com.bees360.web.core.properties.bean;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;

import org.springframework.util.ObjectUtils;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/12 20:46
 */
@Slf4j
public class PropertiesBeanUtil {

    private static final String SEPARATOR = ",";

    public static List<String> splitList(String list) {
        if (ObjectUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(Arrays.asList(list.split(SEPARATOR)));
    }

    public static List<Long> splitListToLong(String list) {
        List<String> strList = splitList(list);
        List<Long> result = new ArrayList<>(strList.size());
        for(String str: strList) {
            try {
                Long val = Long.parseLong(str);
                result.add(val);
            } catch (Exception e) {
                log.warn(e.getMessage(), e);
            }
        }
        return result;
    }

    public static List<Integer> splitListToInt(String list) {
        List<String> strList = splitList(list);
        List<Integer> result = new ArrayList<>(strList.size());
        for(String str: strList) {
            try {
                Integer val = Integer.parseInt(str);
                result.add(val);
            } catch (Exception e) {
                log.warn(e.getMessage(), e);
            }
        }
        return result;
    }

    public static List<Double> splitListToDouble(String list) {
        List<String> strList = splitList(list);
        List<Double> result = new ArrayList<>(strList.size());
        for(String str: strList) {
            try {
                Double val = Double.parseDouble(str);
                result.add(val);
            } catch (Exception e) {
                log.warn(e.getMessage(), e);
            }
        }
        return result;
    }
}
