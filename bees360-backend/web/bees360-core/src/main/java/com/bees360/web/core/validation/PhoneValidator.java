package com.bees360.web.core.validation;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
public class PhoneValidator {

    public static final String COUNTRY_CODE_PLUS_SIGN = "+";
    /**
     * 含有国家区号开头的正则表达式，如：+86 15987412560
     */
    public static final String PHONE_REGEX = "^\\+\\d{1,3} \\d+(x\\d+)?";
    public static final String PHONE_REGEX_US = "^\\+1 \\d{10}(x\\d+)?";
    public static final String PHONE_REGEX_CN = "^\\+86 \\d{11}";
    public static final String COUNTRY_CODE_US = "+1";
    public static final String COUNTRY_CODE_CN = "+86";
    public static final String REGION_CHINA = "CN";
    public static final String REGION_US = "US";
    private static final PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
    private static final PhoneValidator CHINA_PHONE_NUMBER_UTIL = new PhoneValidator(REGION_CHINA);

    private final String DEFAULT_REGION;

    /**
     *
     * @param defaultRegion @see com.google.i18n.phonenumbers.CountryCodeToRegionCodeMap
     */
    public PhoneValidator(String defaultRegion) {
        this.DEFAULT_REGION = defaultRegion;
    }

    public static PhoneValidator getDefaultInstance() {
        return new PhoneValidator(REGION_US);
    }

    public static PhoneValidator getChinaPhoneValidator() {
        return CHINA_PHONE_NUMBER_UTIL;
    }
    /**
     * 按照提供的区域码解析手机号码。比如"+86 15986781861"会按照中国的手机号码进行解析，解析为"+86 15986781861"；
     * 而"15986781861"会按照设置的区域码进行解析，如果设置的为"US"，则会解析为"*************"
     */
    public String formatPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return "";
        }
        try {
            PhoneNumber phoneNumber = parsePhone(phone);
            String phoneNumbers = phone.replaceAll("\\D", "");
            String resultPhone = phoneNumber.getNationalNumber() + "";
            String extension = "";
            // PhoneNumberUtil会将座机号的部分移除，处理是为了弥补座机号。比如 ************ x6 -> **************
            int resultPhoneIndex = phoneNumbers.indexOf(resultPhone);
            if (resultPhoneIndex >= 0) {
                extension = phoneNumbers.substring(resultPhoneIndex + resultPhone.length());
            }
            return format(String.valueOf(phoneNumber.getCountryCode()), String.valueOf(phoneNumber.getNationalNumber()), extension);
        } catch (NumberParseException e) {
            throw new IllegalArgumentException("`" + phone + "` isn't a valid phone.", e);
        }
    }

    private String format(String countryCode, String nationPhone, String extension) {
        countryCode = StringUtils.isEmpty(countryCode)? "": "+" + countryCode + " ";
        extension = StringUtils.isEmpty(extension)? "": "x" + extension;
        return countryCode + nationPhone + extension;
    }

    /**
     * 按照提供的区域码解析手机号码。比如"+86 15986781861"会按照中国的手机号码进行解析，解析为"+86 15986781861"；
     * 而"15986781861"会按照设置的区域码进行解析，如果设置的为"US"，则会解析为"*************"
     */
    public PhoneNumber parsePhone(String phone) throws NumberParseException {
        PhoneNumber phoneNumber = phoneNumberUtil.parse(phone, DEFAULT_REGION);
        return phoneNumber;
    }

    /**
     * @return 返回格式化结果，如果不是可格式化的手机号码，则返回空字符串。
     */
    public String formatPhoneOrEmpty(String phone) {
        try {
            return formatPhone(phone);
        } catch (Exception e) {
            log.debug("fail to format phone `{}`", phone, e);
            return "";
        }
    }

    public boolean isValid(String phone) {
        if (StringUtils.isEmpty(phone)) {
            return true;
        }
        if (StringUtils.startsWith(phone, COUNTRY_CODE_US)) {
            return phone.matches(PHONE_REGEX_US);
        }
        if (StringUtils.startsWith(phone, COUNTRY_CODE_CN)) {
            return phone.matches(PHONE_REGEX_CN);
        }
        return phone.matches(PHONE_REGEX);
    }
    /**
     * 要求必须以+号开头，表示前面携带国家区号。具体的号码会进行校验，是否符合国家区号指定国家的手机号码格式。
     */
    public boolean isValidFormatted(String phone) {
        try {
            phone = formatPhone(phone);
        } catch (Exception e) {
            return false;
        }
        return isValid(phone);
    }
}
