package com.bees360.web.core.properties.bean;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2019/12/12 21:09
 */
@Data
@NoArgsConstructor
@Component
@ConfigurationProperties("payment.square")
public class PaymentSquareProperties {
    @NotBlank
    private String applicationId;
    @NotBlank
    private String accessToken;
    @NotBlank
    private String locationId;
}
