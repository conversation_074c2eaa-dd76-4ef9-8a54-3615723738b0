package com.bees360.web.core.properties.bean;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2019/12/12 21:08
 */
@Data
@NoArgsConstructor
@Component
@ConfigurationProperties("payment.stripe")
public class PaymentStripeProperties {
    /**
     * 私钥
     */
    @NotBlank
    private String secretApiIKey;
    /**
     * 公钥
     */
    @NotBlank
    private String publishableKey;
    /**
     * 货币单位
     */
    private String currency;
    /**
     * 最小支付金额
     * ref: https://stripe.com/docs/currencies#minimum-and-maximum-charge-amounts
     */
    private int minimumAmount;
    /**
     * 最小支付金额
     * ref: https://stripe.com/docs/currencies#minimum-and-maximum-charge-amounts
     */
    private int maximumAmount;
}
