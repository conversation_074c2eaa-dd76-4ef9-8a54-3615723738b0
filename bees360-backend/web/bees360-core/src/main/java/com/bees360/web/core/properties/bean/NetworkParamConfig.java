package com.bees360.web.core.properties.bean;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019-12-18
 * @date 2019-12-11
 */
@Getter
@Component
public class NetworkParamConfig {

    @Value("${bees360.web.network.retry-times}")
    private int networkRetryTimes;

    @Value("${bees360.web.network.retry-interval}")
    private int networkRetryInterval;
}
