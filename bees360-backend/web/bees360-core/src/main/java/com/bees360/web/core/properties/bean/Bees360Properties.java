package com.bees360.web.core.properties.bean;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 利用 {@code @AllArgsConstructor} 注解，通过构造方法从IOC中构造该类实例
 *
 * <AUTHOR>
 * @date 2019/12/10 16:02
 */
@Data
@NoArgsConstructor
@Component
@ConfigurationProperties(Bees360Properties.BEES360_PROPERTIES_PREFIX)
public class Bees360Properties {

    /**
     * bees360 自定义配置属性应该以 "bees360" 作为前缀
     */
    public static final String BEES360_PROPERTIES_PREFIX = "bees360";

    /**
     * bees360 账单配置
     */
    private Bees360BillingProperties billing;
}
