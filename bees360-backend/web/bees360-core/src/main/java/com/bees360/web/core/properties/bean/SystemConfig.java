package com.bees360.web.core.properties.bean;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/08/27 16:42
 */
@Data
@Component
@ConfigurationProperties(prefix = "bees360.com.system-config")
public class SystemConfig {
    /**
     * 完整前端的域名（带端口号）
     */
    private String webClientAddress;

    /**
     * 后端服务器域名（含端口号和context-path)
     */
    private String webServer;

    //下载文件服务的域名,目前是http://api.bees360.io,后期可以是asw s3的域名,也可以是其它文件服务的可访问域名
    /**
     * 资源服务区域名（如图片服务器域名）
     */
    private String resourceServer;

    /**
     * 图片页面url模板
     */
    private String imagePageTemplate;

    /**
     * 报告页面url模板
     */
    private String reportPageTemplate;

    public String imagesViewLink(long projectId) {
        String imageOrigin = getOrigin();
        String imageUrlTemplate = getImagePageTemplate();
        String resultUrl = imageOrigin + imageUrlTemplate;
        return resultUrl.replace("{projectId}", String.valueOf(projectId));
    }

    public String reportsViewLink(long projectId) {
        String reportOrigin = getOrigin();
        String reportTemplateUrl = getReportPageTemplate();
        String resultUrl = reportOrigin + reportTemplateUrl;
        return resultUrl.replace("{projectId}", String.valueOf(projectId));
    }

    public String getOrigin() {
        String origin = getWebClientAddress();
        if (!origin.endsWith("/")) {
            origin += "/";
        }
        return origin;
    }
}
