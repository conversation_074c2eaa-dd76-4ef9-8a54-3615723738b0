package com.bees360.web.core.properties.bean;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/12/12 22:40
 */
@Data
@NoArgsConstructor
@Component
@ConfigurationProperties(Bees360Properties.BEES360_PROPERTIES_PREFIX + ".grpc")
public class GrpcProperties {
    @Data
    @NoArgsConstructor
    public static class Client {
        private String name;
        private int port;
    }

    @Data
    @NoArgsConstructor
    public static class Server {
        @Range(min = 1, max = 65535)
        private int port;
        private int imagesTransferThreadNum = 10;
        private Retry retry = new Retry();
    }

    @Data
    @NoArgsConstructor
    public static class Retry {
        private int maxInterval = 60;
        private int times = 10;

        public Retry(int maxInterval, int times) {
            this.maxInterval = maxInterval;
            this.times = times;
        }
    }

    @Data
    @NoArgsConstructor
    public static class Clients {
        private Client ai;
    }
    /**
     * 本机作为gprc的配置
     */
    @NotNull
    private Server server;
    /**
     * 本机链接的grpc客户端
     */
    private Clients clients;
}
