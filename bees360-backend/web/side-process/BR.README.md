BR.py 使用方法
===

调用例子如下：
```shell
python /var/bees360/www/process/BR.py  --address=@32.572358,-97.103033 --image_path=/var/bees360/www/maps/googlemaps/houseboudary/32.572358,-97.103033.png --save_json=/var/bees360/www/maps/googlemaps/houseboudary/32.572358,-97.103033.png.json
```

安装必要的包
```bash
sudo apt-get install python-dev
sudo apt autoremove
# numpy
sudo apt-get install python-numpy python-scipy python-matplotlib ipython ipython-notebook python-pandas python-sympy python-nose
```

```bash
# skimage
# http://scikit-image.org/docs/dev/install.html
sudo pip install --user scikit-image
```
如果报一下错误，则继续执行指令。
```
Traceback (most recent call last):
  File "/usr/bin/pip", line 11, in <module>
    sys.exit(main())
  File "/usr/lib/python2.7/dist-packages/pip/__init__.py", line 215, in main
    locale.setlocale(locale.LC_ALL, '')
  File "/usr/lib/python2.7/locale.py", line 581, in setlocale
    return _setlocale(category, locale)
locale.Error: unsupported locale setting
```

```bash
export LC_ALL=C
# https://my.oschina.net/FrankXin/blog/724668
```
如果报一下错误，则继续执行指令
```
Traceback (most recent call last):
  File "/usr/bin/pip", line 9, in <module>
    from pip import main
ImportError: cannot import name main
```
按照  https://blog.csdn.net/tintinetmilou/article/details/80091630 进行修改
```bash
from pip import __main__
if __name__ == '__main__':
    sys.exit(__main__._main())
```

```bash
pip install --user requests
```
