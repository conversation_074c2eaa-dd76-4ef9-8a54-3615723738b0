from os import path, makedirs
import numpy as np
from requests.utils import quote
from skimage.measure import find_contours, points_in_poly, approximate_polygon
from skimage import io
from skimage import color
import argparse
import json

parser = argparse.ArgumentParser(description='roadmap bid v1.0')

parser.add_argument('--address', default='empty',
                    help='address ')
parser.add_argument('--image_path', default='1.png',
                    help='image path ')
parser.add_argument('--save_json', default='1.json',
                    help='json file to save ')

args = parser.parse_args()

image_width = 300
image_height = 300

middle_x = image_width / 2.0
middle_y = image_height / 2.0

def getPixelCoordinatesOfBuildings(address, image_path, save_json, middle_x, middle_y):
    print("Ready to process " + image_path + " and the result will be saved into " + save_json)

    images = io.imread(image_path)
    images = color.rgb2gray(images)

    # Will create inverted binary image.
    images = np.where(images > np.mean(images), 0.0, 1.0)
    contours = find_contours(images, 0.1)

    for n, contourBuilding in enumerate(contours):
        if (contourBuilding[0, 1] == contourBuilding[-1, 1]) and (contourBuilding[0, 0] == contourBuilding[-1, 0]):
            # Check if it is inside any other polygon, so this will remove any additional elements.
            isInside = False
            skipPoly = False
            for othersPolygon in contours:
                isInside = points_in_poly(contourBuilding, othersPolygon)
                if all(isInside):
                    skipPoly = True
                    break
            results = {}
            if skipPoly == False:
                center_inside = points_in_poly(np.array([[middle_x, middle_y]]), contourBuilding)
                if center_inside:
                    # Approximate will generalize the polygon.
                    mainBuilding = approximate_polygon(contourBuilding, tolerance=1)
                    # plt.scatter(mainBuilding[:,0], mainBuilding[:,1])
                    xs = []
                    for x in mainBuilding[:,1]:
                        xs.append(x)

                    ys = []
                    for y in mainBuilding[:, 0]:
                        ys.append(y)

                    results['address'] = address
                    results['xs'] = xs
                    results['ys'] = ys
                    fp = open(save_json, "w")
                    print("Writing result into Json " + save_json)
                    fp.write(json.dumps(results))
                    fp.close()
                    # print('Main building: ' + str(mainBuilding))  # DEBUG
                    break

    print("Finish process " + image_path)

if __name__ == '__main__':
    address =args.address
    image_path =args.image_path
    save_json = args.save_json
    getPixelCoordinatesOfBuildings(address, image_path, save_json, middle_x, middle_y)
