<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.bees360.web</groupId>
		<artifactId>bees360</artifactId>
		<version>${revision}${changelist}</version>
	</parent>
	<artifactId>bees360-web-grpc</artifactId>
	<name>bees360-web-grpc</name>

	<dependencies>
		<dependency>
			<groupId>com.bees360.web</groupId>
			<artifactId>bees360-web-grpc-proto</artifactId>
		</dependency>

		<dependency>
			<groupId>com.bees360.web</groupId>
			<artifactId>bees360-mapper</artifactId>
		</dependency>

		<dependency>
			<groupId>com.bees360.web</groupId>
			<artifactId>bees360-utils</artifactId>
		</dependency>

		<dependency>
			<groupId>com.bees360.common</groupId>
			<artifactId>bees360-common</artifactId>
		</dependency>

        <dependency>
            <groupId>com.bees360.schedule</groupId>
            <artifactId>bees360-schedule</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-address-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-grpc-core</artifactId>
        </dependency>
    </dependencies>
</project>
