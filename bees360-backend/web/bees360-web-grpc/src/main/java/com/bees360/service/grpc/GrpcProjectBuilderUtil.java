package com.bees360.service.grpc;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.common.grpc.MessageBeanUtil;
import com.bees360.entity.EventHistory;
import com.bees360.entity.Member;
import com.bees360.entity.MissionAttachFile;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.ProjectQuiz;
import com.bees360.entity.ProjectStatusUser;
import com.bees360.entity.dto.CodeNameDto;
import com.bees360.entity.dto.ProjectStatusTimeLineDto;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.ProjectStatusVo;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectIdOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectImageOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceOuterClass;
import com.bees360.mapper.CompanyMapper;
import com.bees360.mapper.EventHistoryMapper;
import com.bees360.mapper.MemberMapper;
import com.bees360.mapper.MissionAttachFileMapper;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.ProjectQuizMapper;
import com.bees360.mapper.ProjectStatusMapper;
import com.bees360.user.UserProvider;
import com.bees360.util.user.UserAssemble;
import io.jsonwebtoken.lang.Collections;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GrpcProjectBuilderUtil {

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private ProjectImageMapper projectImageMapper;

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private ProjectStatusMapper projectStatusMapper;

    @Autowired
    private EventHistoryMapper eventHistoryMapper;

    @Autowired
    private MissionAttachFileMapper missionAttachFileMapper;

    @Autowired
    private ProjectQuizMapper projectQuizMapper;

    @Autowired
    private UserProvider userProvider;

    public ReportGenerateServiceOuterClass.Project buildAndSetGrpcProject(
            ReportGenerateServiceOuterClass.Project.Builder projectBuilder, com.bees360.entity.Project project,
            List<ProjectImageOuterClass.ProjectImage> grpcProjectImages) {
        try {
            ProjectStatusTimeLineDto timeLineDto = getSiteInspectedTimeAndCustomerContactedTime(project.getProjectId());
            String inspectedBy = getInspectedBy(project);
            return projectBuilder
                .setCreatedBy(buildUser(project.getCreatedBy()))
                .setInsuranceCompany(buildCompany(project.getInsuranceCompany()))
                .setRepairCompany(buildCompany(project.getRepairCompany()))
                .setMaterialProviderCompany(buildCompany(project.getMaterialProviderCompany()))
                .setCompanyId(buildCompany(project.getCompanyId()))
                .addAllImages(grpcProjectImages)
                .setSiteInspectedTime(timeLineDto.getSiteInspected().getCreatedTime())
                .setCustomerContactedTime(timeLineDto.getCustomerContacted().getCreatedTime())
                .setInspectedBy(inspectedBy)
                .build();
            //@formatter:on
        } catch (ServiceException e) {
            String message = "Failed to build project for projectId:" + project.getProjectId();
            throw new RuntimeException(message, e);
        }
    }

    private String getInspectedBy(Project project) {
        String inspectedBy = StringUtils.EMPTY;
        EventHistory eventHistory = eventHistoryMapper.getByStatus(project.getProjectId(),
            ProjectStatusEnum.PILOT_CHECKED_IN.getCode());
        if (Objects.nonNull(eventHistory)) {
            com.bees360.user.User user = userProvider.findUserById(eventHistory.getUserId() + "");
            if (Objects.nonNull(user)) {
                inspectedBy = user.getName();
            }
        }
        return inspectedBy;
    }

    public ProjectStatusTimeLineDto getSiteInspectedTimeAndCustomerContactedTime(long projectId) {
        ProjectStatusTimeLineDto timeLineDto = new ProjectStatusTimeLineDto();
        List<ProjectStatusUser> statuses = projectStatusMapper.listByProjectIdWithUser(projectId);

        setTimeLineData(timeLineDto::addStatusVo, statuses, NewProjectStatusEnum.SITE_INSPECTED);
        setTimeLineData(timeLineDto::addStatusVo, statuses, NewProjectStatusEnum.CUSTOMER_CONTACTED);

        return timeLineDto;
    }

    private void setTimeLineData(Consumer<ProjectStatusVo> timeLineConsumer, List<ProjectStatusUser> statuses,
        NewProjectStatusEnum statusEnum) {
        Optional<ProjectStatusUser> statusUser = ListUtil.findFirst(status -> status.getStatus() == statusEnum.getCode(), statuses);
        ProjectStatusVo status = new ProjectStatusVo();
        status.setCreatedTime(statusUser.orElse(new ProjectStatusUser()).getCreatedTime());
        status.setUserName(statusUser.orElse(new ProjectStatusUser()).getUserName());
        status.setStatus(new CodeNameDto(statusEnum.getCode(), statusEnum.getDisplay()));
        timeLineConsumer.accept(status);
    }

    public List<ProjectImageOuterClass.ProjectImage> buildAllGrpcProjectImages(long projectId) {
        var allGrpcProjectImages = new ArrayList<ProjectImageOuterClass.ProjectImage>();
        for (FileSourceTypeEnum fileSourceType : FileSourceTypeEnum.PROJECT_TYPES) {
            try {
                var grpcProjectImages = buildGrpcProjectImages(projectId, fileSourceType);
                allGrpcProjectImages.addAll(grpcProjectImages);
            } catch (RuntimeException e) {
                var message =
                    "Failed to buildGrpcProjectImages with projectId %d and fileSourceType %d";
                log.warn(message.formatted(projectId, fileSourceType.getCode()), e);
            }
        }
        return allGrpcProjectImages;
    }

    private List<ProjectImageOuterClass.ProjectImage> buildGrpcProjectImages(long projectId,
                                                                             FileSourceTypeEnum fileSourceTypeEnum) {
        if (projectId == 0 || fileSourceTypeEnum == null) {
            String message = "There are no images to be transmitted to ai with projectId " + projectId;
            throw new RuntimeException(message);
        }

        List<ProjectImage> projectImages = projectImageMapper.listImages(projectId, fileSourceTypeEnum.getCode());

        if (Collections.isEmpty(projectImages)) {
            return new ArrayList<>();
        }

        try {
            return buildGrpcProjectImages(projectImages);
        } catch (ServiceException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public com.bees360.entity.Project getProject(long projectId) {
        return projectMapper.getById(projectId);
    }

    public ReportGenerateServiceOuterClass.Project.Builder createGrpcProjectBuilder(com.bees360.entity.Project project) {
        ReportGenerateServiceOuterClass.Project.Builder projectBuilder = ReportGenerateServiceOuterClass.Project.newBuilder();
        try {
            MessageBeanUtil.copyBeanPropertiesToMessage(project, projectBuilder, ReportGenerateServiceOuterClass.Project.getDescriptor());
            projectBuilder.setAdjustedBy(getAdjusterName(project.getProjectId()));
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            String message = "Failed to build project for projectId " + project.getProjectId();
            throw new RuntimeException(message, e);
        }
        return projectBuilder;
    }

    /**
     * 获取项目所属的adjuster的姓名 firstName + lastName, eg: john smith
     *
     * @param projectId 项目ID
     * @return adjuster的姓名
     */
    public String getAdjusterName(long projectId) {
        Member adjuster = memberMapper.getActiveMemberByRole(projectId, RoleEnum.ADJUSTER.getRoleId());
        if (adjuster == null) {
            return "";
        }
        long userId = adjuster.getUserId();
        var user = userProvider.findUserById(userId + "");
        if (user != null) {
            return user.getName();
        }
        return "";
    }

    public ProjectIdOuterClass.ProjectId buildProjectIdRequest(long projectId) {
        return ProjectIdOuterClass.ProjectId.newBuilder().setProjectId(projectId).build();
    }

    public ReportGenerateServiceOuterClass.User buildUser(long userId) throws ServiceException {
        var user = UserAssemble.toWebUser(userProvider.findUserById(userId + ""));

        ReportGenerateServiceOuterClass.User.Builder grpcUserBuilder =
            ReportGenerateServiceOuterClass.User.newBuilder();
        try {
            MessageBeanUtil.copyBeanPropertiesToMessage(user, grpcUserBuilder, grpcUserBuilder.getDescriptor());
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            String message = "Failed to build user with userId " + userId;
            throw new ServiceException(MessageCode.SYSTEM_EXCEPTION, message);
        }
        return grpcUserBuilder.build();
    }

    public List<ProjectImageOuterClass.ProjectImage>
    buildGrpcProjectImages(List<com.bees360.entity.ProjectImage> projectImages) throws ServiceException {
        if (CollectionAssistant.isEmpty(projectImages)) {
            return null;
        }
        ProjectImageOuterClass.ProjectImage.Builder projectImageBuilder;
        List<ProjectImageOuterClass.ProjectImage> grpcProjectImages = new ArrayList<>();
        long projectId = 0;
        try {
            projectId = projectImages.get(0).getProjectId();
            for (com.bees360.entity.ProjectImage projectImage : projectImages) {
                projectImageBuilder = ProjectImageOuterClass.ProjectImage.newBuilder();
                MessageBeanUtil.copyBeanPropertiesToMessage(projectImage, projectImageBuilder,
                    projectImageBuilder.getDescriptor());
                grpcProjectImages.add(projectImageBuilder.build());
            }
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            String message = "Failed to build project images with projectId " + projectId;
            throw new ServiceException(MessageCode.SYSTEM_EXCEPTION, message);
        }
        return grpcProjectImages;
    }

    public ReportGenerateServiceOuterClass.Company buildCompany(Long companyId) throws ServiceException {
        com.bees360.entity.Company company = null;
        if (companyId != null) {
            company = companyMapper.getById(companyId);
        }
        try {
            ReportGenerateServiceOuterClass.Company.Builder companyBuilder = ReportGenerateServiceOuterClass.Company.newBuilder();
            if (company == null) {
                return companyBuilder.getDefaultInstanceForType();
            }
            MessageBeanUtil.copyBeanPropertiesToMessage(company, companyBuilder, ReportGenerateServiceOuterClass.Company.Builder.getDescriptor());
            return companyBuilder.build();
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            String message = "Failed to build company with companyId " + companyId;
            throw new ServiceException(MessageCode.SYSTEM_EXCEPTION, message);
        }
    }

    public ReportGenerateServiceOuterClass.QuestionnaireDataRequest buildQuestionnaireDataRequest(long projectId) {
        Project project = projectMapper.getById(projectId);
        List<ProjectQuiz> answers = projectQuizMapper.listLatestAnswers(projectId, Optional.ofNullable(project.getClaimType()).orElse(0));
        List<ReportGenerateServiceOuterClass.QuizAnswer> quizAnswers = answers.stream()
            .map(e -> ReportGenerateServiceOuterClass.QuizAnswer.newBuilder()
                .setQuizId(e.getQuizId())
                .setAnswer(e.getAnswer())
                .build())
            .collect(Collectors.toList());

        List<MissionAttachFile> attachFiles = missionAttachFileMapper.listByProjectId(projectId);
        List<ReportGenerateServiceOuterClass.Signature> signatures = attachFiles.stream()
            .filter(e -> Objects.equals(project.getMissionId(), e.getMissionId())
                || StringUtils.isBlank(e.getMissionId()))// StringUtils.isBlank(e.getMissionId())为兼容旧数据，下版本可以删除该条件
            .map(e -> ReportGenerateServiceOuterClass.Signature.newBuilder()
                .setType(e.getType())
                .setUrl(e.getUrl())
                .setUploadTime(e.getCreateTime())// 上传时间是file的创建时间
                .build())
            .collect(Collectors.toList());

        return ReportGenerateServiceOuterClass.QuestionnaireDataRequest.newBuilder()
            .setProjectId(projectId)
            .addAllQuizAnswers(quizAnswers)
            .addAllSignatures(signatures)
            .setUnsignedReason(Optional.ofNullable(project.getUnsignedReason()).orElse(""))
            .build();
    }
}
