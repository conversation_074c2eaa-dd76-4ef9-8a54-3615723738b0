package com.bees360.service.grpc.job;

import com.bees360.schedule.util.QuartzJobConstant;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;

/**
 * 创建return-to-client job
 */
public class ProjectChangedToReturnToClientJobDetail {

    public static JobDetail createJobDetail(long projectId, String endpoints, Class jobClass) {

        // 该jobName会限制该照片只能往指定的claim的特定路径上传一次
        String jobName = "grpc-project-return-to-client-%s-%d-%d".formatted(endpoints,
            projectId, System.currentTimeMillis());
        JobDataMap jobData = new JobDataMap();
        jobData.put("projectId", projectId);
        jobData.put("grpcEndpoints", endpoints);
        String description = "Make a grpc call to ai for cleaning data when project status is changed to return-to-client".formatted();
        // @formatter:off
        JobDetail job = JobBuilder.newJob(jobClass)
            .withIdentity(jobName, QuartzJobConstant.WebGroup.PROJECT_RETURN_TO_CLIENT)
            .usingJobData(jobData)
            .withDescription(description)
            .requestRecovery(true)
            .build();
        // @formatter:on
        return job;
    }
}
