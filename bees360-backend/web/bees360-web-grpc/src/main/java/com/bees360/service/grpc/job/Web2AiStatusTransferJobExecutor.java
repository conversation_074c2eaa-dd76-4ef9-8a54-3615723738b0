package com.bees360.service.grpc.job;

import com.bees360.entity.ProjectSyncLog;
import com.bees360.entity.enums.CompanyTypeEnum;
import com.bees360.grpc.GrpcApi;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectGenericServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectGenericServiceOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass;
import com.bees360.job.ExecutorConcurrentCount;
import com.bees360.job.registry.Web2AiStatusTransferJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.mapper.ProjectSyncLogMapper;
import com.bees360.service.grpc.AiGrpcClient;
import com.bees360.service.grpc.BuildProjectInfoConvertersService;
import com.bees360.service.grpc.GrpcProjectGenericService;
import com.bees360.service.grpc.GrpcStubConfig;
import io.grpc.StatusException;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.AbstractStub;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

@Log4j2
@Component
@ToString
public class Web2AiStatusTransferJobExecutor extends AbstractJobExecutor<Web2AiStatusTransferJob>
        implements ExecutorConcurrentCount {
    @Autowired private GrpcProjectGenericService grpcProjectGenericService;
    @Autowired private ProjectSyncLogMapper projectSyncLogMapper;
    @Autowired private BuildProjectInfoConvertersService buildProjectInfoConvertersService;
    private static final Integer DEFAULT_MAX_CONCURRENT_COUNT = 8;

    @Override
    public int getMaxConcurrentCount() {
        return DEFAULT_MAX_CONCURRENT_COUNT;
    }

    @Override
    protected void handle(Web2AiStatusTransferJob job) throws IOException {
        var projectId = job.getProjectId();
        var code = job.getCode();
        var type = job.getType();
        var endpoint = job.getEndpoint();
        var createdBy = job.getCreatedBy();
        var createdAt = job.getCreateAt();
        List<GrpcStubConfig> abstractStubs = grpcProjectGenericService.getStubs();
        for (GrpcStubConfig abstractStub : abstractStubs) {
            AiGrpcClient aiGrpcClient = abstractStub.getAiGrpcClient();
            // 创建job时以一个aiGrpcClient为一个job
            if (!endpoint.equals(aiGrpcClient.getEndpoints())) {
                continue;
            }
            log.info("Start to project '{}' status data from web to ai.", projectId);
            executeJob(
                    aiGrpcClient,
                    abstractStub.getGrpcStub(),
                    projectId,
                    code,
                    type,
                    createdBy,
                    createdAt);
            log.info("Successfully sync project '{}' status data from web to ai.", projectId);
        }
    }

    private void executeJob(
            AiGrpcClient aiGrpcClient,
            AbstractStub grpcStub,
            long projectId,
            Integer code,
            String type,
            String createdBy,
            Long createdAt)
            throws IOException {
        try {

            // 测试的case不同步到AI
            var project = buildProjectInfoConvertersService.getProject(projectId);
            if (CompanyTypeEnum.BEES360_TEST_CARRIER.equals(project.getRepairCompany())) {
                log.info("project don't need to sync to ai due to a test project {}", projectId);
                return;
            }

            ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub stub =
                    (ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub) grpcStub;

            ProjectGenericServiceOuterClass.ProjectStatusChangeRequest request =
                    ProjectGenericServiceOuterClass.ProjectStatusChangeRequest.newBuilder()
                            .setProjectId(projectId)
                            .setStatusCode(code)
                            .setType(type)
                            .setCreatedAt(Optional.ofNullable(createdAt).orElse(0L))
                            .setCreatedBy(Optional.ofNullable(createdBy).orElse(""))
                            .build();
            // 调用grpc
            StatusCodeResponseOuterClass.StatusCodeResponse statusCodeResponse =
                    stub.updateProjectStatusOnWebStatusChange(request).get();
            projectSyncLogMapper.insert(
                    ProjectSyncLog.builder()
                            .projectId(projectId)
                            .syncEvent(type)
                            .status(statusCodeResponse.getStatus() == 0 ? 1 : -1)
                            .build());
        } catch (InterruptedException ex) {
            var syncLog =
                    ProjectSyncLog.builder()
                            .projectId(projectId)
                            .syncEvent(type)
                            .status(-1)
                            .build();
            projectSyncLogMapper.insert(syncLog);
            Thread.currentThread().interrupt();
            throw new IllegalStateException(ex);
        } catch (ExecutionException ex) {
            var cause = ex.getCause();
            if (cause instanceof IOException exception) {
                throw exception;
            } else if (cause instanceof StatusRuntimeException || cause instanceof StatusException) {
                throw GrpcApi.translateException(cause);
            } else if (cause instanceof RuntimeException exception) {
                throw exception;
            } else {
                throw new IllegalStateException(cause);
            }
        } catch (StatusRuntimeException e) {
            throw GrpcApi.translateException(e);
        }
    }
}
