package com.bees360.service.grpc.impl;

import com.bees360.api.AbortedException;
import com.bees360.api.AlreadyExistsException;
import com.bees360.api.ApiException;
import com.bees360.api.CancelledException;
import com.bees360.api.DeadlineExceededException;
import com.bees360.api.FailedPreconditionException;
import com.bees360.api.InternalException;
import com.bees360.api.InvalidArgumentException;
import com.bees360.api.NotFoundException;
import com.bees360.api.OutOfRangeException;
import com.bees360.api.PermissionDeniedException;
import com.bees360.api.UnauthenticatedException;
import com.bees360.api.UnavailableException;
import com.bees360.api.UnimplementedException;
import com.bees360.api.UnknownException;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectClaimTagServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectTag;
import com.bees360.service.grpc.AiGrpcClient;
import com.bees360.service.grpc.ProjectClaimTagService;
import io.grpc.Status;
import io.grpc.StatusException;
import io.grpc.StatusRuntimeException;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;

@Log4j2
@Service("projectClaimTagService")
public class ProjectClaimTagServiceImpl implements ProjectClaimTagService {

    private final List<ProjectClaimTagServiceGrpc.ProjectClaimTagServiceBlockingStub>
            projectClaimTagServiceFutureStubs = new ArrayList<>();

    @Autowired private List<AiGrpcClient> aiGrpcClients;

    @PostConstruct
    public void initStub() {
        for (AiGrpcClient aiGrpcClient : aiGrpcClients) {
            projectClaimTagServiceFutureStubs.add(
                    ProjectClaimTagServiceGrpc.newBlockingStub(aiGrpcClient.getChannel()));
        }
    }

    @Override
    public List<ProjectTag.ProjectClaimTag> getTags(ProjectTag.ProjectClaimTagRequest request) {
        try {
            ProjectTag.ProjectClaimTagResponse response = projectClaimTagServiceFutureStubs.get(0).getTags(request);
            return response.getTagList();
        } catch (StatusRuntimeException e) {
            throw translateException(e);
        }
    }

    public static RuntimeException translateException(Throwable e) {
        if (e instanceof StatusException exception) {
            return translateException(exception);
        } else if (e instanceof StatusRuntimeException exception) {
            return translateException(exception);
        } else {
            return new IllegalStateException("Received unexpected exception.", e);
        }
    }

    public static RuntimeException translateException(StatusException e) {
        return translateException(e.getStatus());
    }

    public static RuntimeException translateException(StatusRuntimeException e) {
        return translateException(e.getStatus());
    }

    public static RuntimeException translateException(Status status) {
        Function<BiFunction<String, Exception, ApiException>, ApiException> newException =
            ctor -> ctor.apply(status.getDescription(), (Exception) status.getCause());

        switch (status.getCode()) {
            case OK:
                return null;
            case CANCELLED:
                return newException.apply(CancelledException::new);
            case UNKNOWN:
                return newException.apply(UnknownException::new);
            case RESOURCE_EXHAUSTED:
            case INVALID_ARGUMENT:
                return newException.apply(InvalidArgumentException::new);
            case DEADLINE_EXCEEDED:
                return newException.apply(DeadlineExceededException::new);
            case DATA_LOSS:
            case NOT_FOUND:
                return newException.apply(NotFoundException::new);
            case ALREADY_EXISTS:
                return newException.apply(AlreadyExistsException::new);
            case PERMISSION_DENIED:
                return newException.apply(PermissionDeniedException::new);
            case UNAUTHENTICATED:
                return newException.apply(UnauthenticatedException::new);
            case FAILED_PRECONDITION:
                return newException.apply(FailedPreconditionException::new);
            case ABORTED:
                return newException.apply(AbortedException::new);
            case OUT_OF_RANGE:
                return newException.apply(OutOfRangeException::new);
            case INTERNAL:
                return newException.apply(InternalException::new);
            case UNIMPLEMENTED:
                return newException.apply(UnimplementedException::new);
            case UNAVAILABLE:
                return newException.apply(UnavailableException::new);
            default:
                return new IllegalStateException(
                    "Unexpected gRPC status.", status.asRuntimeException());
        }
    }
}
