package com.bees360.service.grpc;

import com.bees360.common.constants.BaseConstant;
import com.bees360.common.thread.ThreadUtils;
import com.bees360.internal.ai.grpc.api.web2ai.FileStreamingServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.FileStreamingServiceOuterClass;
import com.google.protobuf.ByteString;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.Arrays;
import java.io.IOException;
import java.io.OutputStream;

/**
 * write bytes to grpc receiver
 *
 * <AUTHOR>
 * @date 2019-12-10
 */
@Slf4j
public class GrpcFileOutputStream extends OutputStream {

    private int bufferSize = BaseConstant.BUFFER_SIZE;
    private int nWrite = 0;
    private int chunkNumber;
    private int bytesWrite;
    private byte[] bytes = new byte[bufferSize];

    private AiGrpcClient aiGrpcClient;
    private FileStreamingServiceGrpc.FileStreamingServiceBlockingStub fileStreamingServiceStub;
    private String outputKey;
    private boolean closed;
    private int networkRetryTimes;
    private int networkRetryInterval;

    private boolean isSuccessive = true;

    public GrpcFileOutputStream(AiGrpcClient aiGrpcClient) {
        this.aiGrpcClient = aiGrpcClient;
        fileStreamingServiceStub = FileStreamingServiceGrpc.newBlockingStub(aiGrpcClient.getChannel());
        closed = false;
    }

    @Override
    public void write(int b) throws IOException {
        bytesWrite++;
        if (nWrite < bufferSize) {
            bytes[nWrite++] = (byte)b;
            return;
        }
        // write bytes to bees360-ai when buffer is full
        chunkNumber++;
        grpcWrite(bytes);

        nWrite = 0;
        bytes[nWrite++] = (byte)b;
    }

    @Override
    public void close() throws IOException {
        if (!closed) {
            if (nWrite > 0) {
                chunkNumber++;
                grpcWrite(bytes);
                grpcFinish();
                // 置零
                nWrite = 0;
                chunkNumber = 0;
            }
            closed = true;
            isSuccessive = false;
            bytes = null;
        }
    }

    /**
     * make a sync grpc call
     *
     * @param bytes
     * @throws IOException
     */
    private void grpcWrite(byte[] bytes) {
        int chunkOffset = (chunkNumber - 1) * bufferSize;
        if (nWrite == bytes.length) {
            grpcWrite(bytes, chunkOffset, nWrite, chunkNumber);
        } else if (nWrite < bytes.length) {
            grpcWrite(Arrays.copyOfRange(bytes, 0, nWrite), chunkOffset, nWrite, chunkNumber);
        } else {
            log.error("nWrite is great than bytes.length");
        }
    }

    private void grpcFinish() {
        grpcWrite(new byte[] {}, -1, -1, -1);
    }

    /**
     * make a sync grpc call with chunk bytes
     *
     * @param bytes
     * @param chunkOffset
     * @param chunkLength
     * @param chunkNumber
     * @throws IOException
     */
    private void grpcWrite(byte[] bytes, int chunkOffset, int chunkLength, int chunkNumber) {
        String message = "failed to make a rpc call to bees360-ai for the file " + outputKey;
        FileStreamingServiceOuterClass.FileStream fileStream;
        int times = networkRetryInterval;
        // stop to write bytes to bees360-ai when previous bytes transfer failed(after retry 3 times).
        if (!isSuccessive) {
            return;
        }
        try {
            while (times > 0) {
                try {
                    fileStream = buildFileStream(bytes, chunkOffset, chunkLength, chunkNumber);
                    FileStreamingServiceOuterClass.FileStreamResponse fileStreamResponse =
                        fileStreamingServiceStub.transfer(fileStream);
                    log.info("bees360-ai make a response with a status {}, and message is {}",
                        fileStreamResponse.getCode(), fileStreamResponse.getMessage());
                    if (fileStreamResponse.getCode() == FileStreamingServiceOuterClass.UploadStatusCode.OK) {
                        isSuccessive = true;
                        return;
                    } else if (fileStreamResponse.getCode() == FileStreamingServiceOuterClass.UploadStatusCode.FAILED) {
                        isSuccessive = false;
                        return;
                    }
                } catch (StatusRuntimeException ex) {
                    log.error(message, ex);
                    times--;
                    isSuccessive = false;
                }
            }
        } finally {
            fileStream = null;
        }
    }

    /**
     * build a fileChunk
     *
     * @param bytes
     * @param chunkOffset
     * @param chunkLength
     * @param chunkNumber
     * @return
     */
    private FileStreamingServiceOuterClass.FileStream buildFileStream(byte[] bytes, int chunkOffset, int chunkLength,
        int chunkNumber) {
        return FileStreamingServiceOuterClass.FileStream.newBuilder()
        // @formatter:off
                .setKey(outputKey)
                .setContent(ByteString.copyFrom(bytes))
                .setChunkOffset(chunkOffset)
                .setChunkLength(chunkLength)
                .setChunkNumber(chunkNumber)
                .build();
        // @formatter:on
    }

    public void setOutputKey(String outputKey) {
        this.outputKey = outputKey;
    }

    public void setNetworkRetryTimes(int networkRetryTimes) {
        this.networkRetryTimes = networkRetryTimes;
    }

    public void setNetworkRetryInterval(int networkRetryInterval) {
        this.networkRetryInterval = networkRetryInterval;
    }

    public long getBytesWrite() {
        return this.bytesWrite;
    }

    private void waitSomeSeconds(int seconds) {
        try {
            ThreadUtils.waitSomeSeconds(seconds);
        } catch (InterruptedException e) {
            log.error("InterruptedException occur when thread sleep.", e);
        }
    }
}
