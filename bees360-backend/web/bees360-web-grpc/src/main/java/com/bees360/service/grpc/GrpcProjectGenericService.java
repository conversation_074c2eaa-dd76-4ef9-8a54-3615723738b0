package com.bees360.service.grpc;

import com.bees360.base.exception.ServiceMessageException;
import com.bees360.entity.ProjectMessage;
import com.bees360.entity.enums.ProjectEventTypeEnum;
import com.bees360.entity.label.ProjectLabelDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/07/26
 */
public interface GrpcProjectGenericService {


    void updateProjectStatusOnWebStatusChange(long projectId, ProjectEventTypeEnum typeEnum);

    List<GrpcStubConfig> getStubs();

    void addAiProjectMessageOnWebChange(ProjectMessage message) throws ServiceMessageException;

    void deleteAiImagesOnWebDeleted(long projectId, List<String> imageIds, int deleteStatus)
            throws ServiceMessageException;

    Integer updateReportStatusAfterEntryRecordCheck(long projectId, String type, Integer status)
        throws ServiceMessageException;

    void updateProjectTagsOnWebChange(List<ProjectLabelDto> projectLabelDtos);

    void updateProjectInspectionTimeOnWebChange(long projectId, Long inspectionTime);
}
