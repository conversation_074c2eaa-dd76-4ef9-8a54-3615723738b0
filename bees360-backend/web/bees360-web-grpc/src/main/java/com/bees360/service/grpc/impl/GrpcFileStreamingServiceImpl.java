package com.bees360.service.grpc.impl;

import com.bees360.common.constants.Punctuation;
import com.bees360.common.file.FileUtils;
import com.bees360.common.file.chunk.FileChunk;
import com.bees360.common.file.chunk.FileChunkDto;
import com.bees360.common.file.chunk.FileChunkUtils;
import com.bees360.internal.ai.grpc.api.web2ai.FileStreamingServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.FileStreamingServiceOuterClass;
import com.bees360.service.grpc.FileChunksTransfer;
import com.bees360.service.grpc.AiGrpcClient;
import com.bees360.service.grpc.GrpcFileStreamingService;
import com.bees360.web.core.properties.bean.GrpcProperties;
import com.google.protobuf.ByteString;
import io.grpc.stub.StreamObserver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;
import java.io.File;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 通过grpc streaming传输大文件(project下的图片压缩包)
 */

@Service("grpcCompressedFileStreamingService")
public class GrpcFileStreamingServiceImpl implements GrpcFileStreamingService {

    private static final Logger log = LoggerFactory.getLogger(GrpcFileStreamingServiceImpl.class);

    @Autowired
    private GrpcProperties grpcProperties;

    private static Map<Long, Integer> latestChunkIndexMap = new HashMap<>();

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private Environment env;

    private ExecutorService executorService = null;


    @Autowired
    private List<AiGrpcClient> aiGrpcClients;

    private List<FileStreamingServiceGrpc.FileStreamingServiceStub> fileStreamingServiceStubList = new ArrayList<>();

    @PostConstruct
    public void init() {
        for(AiGrpcClient aiGrpcClient : aiGrpcClients) {
            fileStreamingServiceStubList.add(FileStreamingServiceGrpc.newStub(aiGrpcClient.getChannel()));
        }

        ThreadFactory threadFactory = new ThreadFactory() {
            private int threadCount = 1;

            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r);
                thread.setName("bees360-grpc-file-transfer-worker-" + threadCount++);
                return thread;
            }
        };
        GrpcProperties.Server grpcServer = grpcProperties.getServer();
        executorService = Executors.newFixedThreadPool(grpcServer.getImagesTransferThreadNum(), threadFactory);
    }

    /**
     * transfer stream
     *
     * @param projectId
     * @param archiveFilePath
     * @return
     */
    @Override
    public boolean transfer(long projectId, String archiveFilePath) {
        if (projectId == 0 || archiveFilePath == null) {
            return false;
        }

        File archiveFile = new File(archiveFilePath);
        if (archiveFile == null || archiveFile.length() == 0) {
            return false;
        }
        log.info("archiveFile path is:{}, size:{}", archiveFile, archiveFile.length());

        Map<Integer, byte[]> splitBytes = FileChunkUtils.splitFile(archiveFile);
        if (splitBytes == null || splitBytes.size() == 0) {
            return false;
        }

        try {
            List<byte[]> bytesList = splitBytes.values().stream().collect(Collectors.toList());
            transfer(projectId, bytesList);
        } catch (Exception e) {
            log.error("Exception occur in transfer() for projectId:" + projectId, e);
            return false;
        } finally {
            if (splitBytes != null) {
                splitBytes.clear();
            }
            try {
                FileUtils.deleteFile(archiveFilePath);
                String directory = archiveFilePath.substring(0, archiveFilePath.lastIndexOf(Punctuation.DASH));
                FileUtils.deleteDirectory(directory);
                log.info("delete downloaded images in directory {} finished", directory);
            } catch (Exception e) {
                log.error("bees360-web deleteDownloadFiles deleteDirectory failed", e);
            }
        }
        return true;
    }

    @Override
    public boolean transfer(long projectId, List<byte[]> bytesList) {
        if (bytesList == null || bytesList.size() == 0) {
            return false;
        }
        try {
            long timeStart = System.currentTimeMillis();
            int fileChunkBytesTotalLength = 0;
            for (byte[] bytes : bytesList) {
                fileChunkBytesTotalLength += bytes.length;
            }
            log.info("fileChunkBytesTotalLength:{} for projectId:{}", fileChunkBytesTotalLength, projectId);
            for (int i = 0; i < bytesList.size(); i++) {
                byte[] fileChunkBytes = bytesList.get(i);
                if (fileChunkBytes == null || fileChunkBytes.length == 0) {
                    String message = "fileChunkBytes:%d of fileChunkIndex:%d for projectId:%d is empty";
                    int fileChunkBytesLength = fileChunkBytes == null ? 0 : fileChunkBytes.length;
                    message = message.formatted(fileChunkBytesLength, i, projectId);
                    log.error(message);
                    throw new RuntimeException(message);
                }

                FileChunkDto fileChunkDto = buildFileChunkDto(projectId, Arrays.asList(buildFileChunk(i, fileChunkBytes)));
                FileChunksTransfer fileChunksThread =
                    applicationContext.getBean(FileChunksTransfer.class, fileChunkDto);
                String messageTemplate = "FileChunksTransfer execute failed for chunkIndex:%d of projectId:%d";
                String message = messageTemplate.formatted(i, projectId);
                try {
                    executorService.submit(fileChunksThread);
                } catch (Exception e) {
                    log.error(message, e);
                    throw new RuntimeException(message);
                }
            }
            long timeEnd = System.currentTimeMillis();
            log.info(
                "the total time: {} mills was spent for images transfer from bees360 to bees360-ai for projectId:{}",
                (timeEnd - timeStart), projectId);
        } catch (Exception e) {
            log.error("Exception occur in transfer() for projectId:" + projectId, e);
            return false;
        } finally {
            if (bytesList != null) {
                bytesList.clear();
            }
        }
        return true;
    }

    private FileChunkDto buildFileChunkDto(long projectId, List<FileChunk> fileChunks) {
        FileChunkDto fileChunkDto = new FileChunkDto();
        fileChunkDto.setProjectId(projectId);
        fileChunkDto.setFileChunks(fileChunks);
        return fileChunkDto;
    }

    private FileChunk buildFileChunk(int fileChunkIndex, byte[] fileChunkBytes) {
        return new FileChunk(fileChunkIndex, fileChunkBytes);
    }

    @Override
    public boolean transferFileChunk(long projectId, int fileChunkIndex, byte[] fileChunkBytes) {
        if (fileChunkBytes == null || fileChunkBytes.length == 0) {
            return false;
        }

        GrpcProperties.Retry retry = grpcProperties.getServer().getRetry();
        int index = 0;
        int times = retry.getTimes();
        boolean succeeded = false;
        while (times > 0) {
            log.info("transferFileChunk for projectId: {} chunkIndex: {} for {}  times", projectId, fileChunkIndex,
                ++index);

            CountDownLatch finishLatch = new CountDownLatch(1);
            CountDownLatch errorLatch = new CountDownLatch(1);
            StreamObserver<FileStreamingServiceOuterClass.CompressedFileChunk> requestObserver = null;
            StreamObserver<FileStreamingServiceOuterClass.UploadResponse> responseStreamObserver =
                buildUploadResponseObserver(projectId, finishLatch, errorLatch);
            for (FileStreamingServiceGrpc.FileStreamingServiceStub fileStreamingServiceStub: fileStreamingServiceStubList) {
                requestObserver = fileStreamingServiceStub.pull(responseStreamObserver);
            }
            requestObserver.onNext(buildCompressedFileChunk(projectId, fileChunkIndex, fileChunkBytes));
            requestObserver.onCompleted();

            log.info("projectId:{} fileChunkIndex:{} fileChunkBytes:{} finishLatch.count:{} errorLatch.count:{}",
                projectId, fileChunkIndex, fileChunkBytes.length, finishLatch.getCount(), errorLatch.getCount());
            // 没有出现错误
            if (errorLatch.getCount() == 1) {
                // 服务器端回调返回结束信息,本地调用正常结束，结束重试
                if (finishLatch.getCount() == 0) {
                    succeeded = true;
                }
                // 服务器端尚未回调完成，则一直等待(此处的等待时间可以设置大一点，否则返回值为false)
                else if (finishLatch.getCount() == 1) {
                    try {
                        succeeded = finishLatch.await(retry.getMaxInterval(), TimeUnit.SECONDS);
                    } catch (InterruptedException e) {
                        succeeded = false;
                        log.error("waiting for callback of response streamObserver failed for projectId:" + projectId,
                            e);
                    } catch (Exception e) {
                        succeeded = false;
                        log.error("waiting for callback of response streamObserver failed for projectId:" + projectId,
                            e);
                    }
                } else {
                    succeeded = false;
                    log.error("unexpected finishLatch count:{} for projectId:", finishLatch.getCount(), projectId);
                }
            }
            // TODO,如果由于网络中断，连接丢失，是否需要重新建立连接?
            else {
                succeeded = false;
                log.error(
                    "there is an error occur when transferFileChunk for projectId:{}, chunkIndex:{}, fileChunkBytes:{}",
                    projectId, fileChunkIndex, fileChunkBytes.length);
            }
            if (succeeded) {
                break;
            }
            times--;
            log.info("requestObserver.onCompleted() for client observer of projectId:{}", projectId);

            // 暂停1s
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.info("InterruptedException occur for projectId:" + projectId, e);
            }
        }
        return succeeded;
    }

    @SuppressWarnings(value = "unused")
    private void transferFileChunks(long projectId, Map<Integer, byte[]> fileChunkBytesMap) {
        if (fileChunkBytesMap == null || fileChunkBytesMap.size() == 0) {
            return;
        }
        int totalFileLength = 0;
        for (byte[] bytes : fileChunkBytesMap.values()) {
            totalFileLength += bytes.length;
        }
        log.info("total bytes :{} to be transformed for projectId:{}", totalFileLength, projectId);
        for (Map.Entry<Integer, byte[]> fileChunk : fileChunkBytesMap.entrySet()) {
            int fileChunkIndex = fileChunk.getKey();
            byte[] fileChunkBytes = fileChunk.getValue();
            int fileChunkBytesLength = fileChunkBytes == null ? 0 : fileChunkBytes.length;
            boolean succeeded = transferFileChunk(projectId, fileChunkIndex, fileChunkBytes);
            if (fileChunkBytes == null || fileChunkBytes.length == 0) {
                String message = "fileChunkBytes:%d of fileChunkIndex:%d for projectId:%d is empty";
                message = message.formatted(fileChunkBytesLength, fileChunkIndex, projectId);
                log.error(message);
                throw new RuntimeException(message);
            }
            if (!succeeded) {
                String message = "transfer file chunk of index %d for projectId:%d, byte length is:%d";
                message = message.formatted(fileChunkIndex, projectId, fileChunkBytesLength);
                log.error(message);
                throw new RuntimeException(message);
            }
        }
    }

    /**
     * buildCompressedFileChunk
     *
     * @param projectId
     * @param bytes
     * @return
     */
    private FileStreamingServiceOuterClass.CompressedFileChunk buildCompressedFileChunk(long projectId, int chunkIndex,
                                                                                        byte[] bytes) {
        if (projectId == 0 || bytes == null || bytes.length == 0) {
            return FileStreamingServiceOuterClass.CompressedFileChunk.newBuilder()
                .setChunk(FileStreamingServiceOuterClass.Chunk.newBuilder()).build();
        }
        return FileStreamingServiceOuterClass.CompressedFileChunk.newBuilder()
            // @formatter:off
            .setProjectId(projectId)
            .setChunkIndex(chunkIndex)
            .setChunk(FileStreamingServiceOuterClass.Chunk.newBuilder()
                .setContent(ByteString.copyFrom(bytes)))
            .build();
        // @formatter:on
    }

    private StreamObserver<FileStreamingServiceOuterClass.UploadResponse> buildUploadResponseObserver(long projectId,
                                                                                                      final CountDownLatch finishLatch, CountDownLatch errorLatch) {

        StreamObserver<FileStreamingServiceOuterClass.UploadResponse> streamObserver =
            new StreamObserver<FileStreamingServiceOuterClass.UploadResponse>() {
                @Override
                public void onNext(FileStreamingServiceOuterClass.UploadResponse uploadResponse) {
                    int uploadStatusCodeNumber = uploadResponse.getCode().getNumber();
                    int chunkIndex = uploadResponse.getChunkIndex();
                    log.info("status code: {}, message:{}, uploaded chunkIndex is {} for projectId:{}",
                        uploadResponse.getCode(), uploadResponse.getMessage(), chunkIndex, projectId);
                    // 服务器端接收到最新的流时返回客户端最新的chunkIndex
                    if (uploadStatusCodeNumber == FileStreamingServiceOuterClass.UploadStatusCode.OK.getNumber()
                        || uploadStatusCodeNumber == FileStreamingServiceOuterClass.UploadStatusCode.FAILED.getNumber()
                        || uploadStatusCodeNumber == FileStreamingServiceOuterClass.UploadStatusCode.UNKNOWN
                        .getNumber()) {
                        latestChunkIndexMap.put(projectId, chunkIndex);
                    }
                }

                @Override
                public void onError(Throwable t) {
                    log.error("upload file chunks failed for projectId:{}", projectId, t);
                    t.getMessage();
                    errorLatch.countDown();
                }

                @Override
                public void onCompleted() {
                    log.info("upload file chunks completed for projectId:{}", projectId);
                    finishLatch.countDown();
                }
            };
        return streamObserver;
    }

    public static void main(String[] args) {
        new GrpcFileStreamingServiceImpl().transferFileChunk(1003291, 1, new byte[] {});
    }
}
