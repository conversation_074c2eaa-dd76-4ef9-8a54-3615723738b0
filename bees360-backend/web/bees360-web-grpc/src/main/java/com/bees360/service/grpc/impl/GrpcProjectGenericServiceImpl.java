package com.bees360.service.grpc.impl;

import com.bees360.base.exception.ServiceMessageException;
import com.bees360.entity.ProjectMessage;
import com.bees360.entity.ProjectSyncLog;
import com.bees360.entity.enums.ProjectEventTypeEnum;
import com.bees360.entity.label.ProjectLabelDto;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectGenericServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectGenericServiceOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectGenericServiceOuterClass.ProjectInspectionTime;
import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass;
import com.bees360.service.grpc.AiGrpcClient;
import com.bees360.service.grpc.GrpcProjectGenericService;
import com.bees360.service.grpc.GrpcStubConfig;
import com.bees360.util.AssertUtil;
import io.grpc.stub.AbstractStub;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * 服务端
 *
 * <AUTHOR>
 * @date 2020-2-18 12:32:50
 */
@Component
public class GrpcProjectGenericServiceImpl implements GrpcProjectGenericService {

    private static final Logger log = LoggerFactory.getLogger(GrpcProjectGenericServiceImpl.class);

    @Autowired
    private List<AiGrpcClient> aiGrpcClients;

    private List<GrpcStubConfig> grpcStubConfigs;

    @PostConstruct
    public void initStub() {
        grpcStubConfigs = new ArrayList();
        for (AiGrpcClient aiGrpcClient : aiGrpcClients) {
            ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub stub = ProjectGenericServiceGrpc.newFutureStub(aiGrpcClient.getChannel());
            grpcStubConfigs.add(new GrpcStubConfig(stub, aiGrpcClient));
        }
    }

    @Override
    public List<GrpcStubConfig> getStubs() {
        return this.grpcStubConfigs;
    }

    @Override
    public void addAiProjectMessageOnWebChange(ProjectMessage message) throws ServiceMessageException {
        AssertUtil.notNull(message, "message is null");
        AssertUtil.notNull(message.getContent(), "message content is null");
        ProjectGenericServiceOuterClass.ProjectMessage request = ProjectGenericServiceOuterClass.ProjectMessage
            .newBuilder()
            .setProjectId(message.getProjectId())
            .setContent(message.getContent())
            .setId(message.getId())
            .setType(message.getType())
            .setTitle(Optional.ofNullable(message.getTitle()).orElse(""))
            .setCreateTime(message.getCreateTime())
            .setExtra(Optional.ofNullable(message.getExtra()).orElse(""))
            .build();
        for (GrpcStubConfig grpcStubConfig : grpcStubConfigs) {
            AbstractStub abstractStub = grpcStubConfig.getGrpcStub();

            ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub stub =
                (ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub)abstractStub;
            try {
                StatusCodeResponseOuterClass.StatusCodeResponse statusCodeResponse =
                    stub.addProjectMessageOnWebChange(request).get();
                log.info("make a grpc addAiProjectMessageOnWebChange for projectId " + request.getProjectId());
            } catch (Exception e) {
                log.error("Failed to make a grpc on addAiProjectMessageOnWebChange for projectId " + request.getProjectId(), e);
            }
        }
    }

    @Override
    public void deleteAiImagesOnWebDeleted(long projectId, List<String> imageIds, int deleteStatus)
            throws ServiceMessageException {
        AssertUtil.notNull(imageIds, "imageIds is null");
        ProjectGenericServiceOuterClass.ImagesDeletedMessage request =
                ProjectGenericServiceOuterClass.ImagesDeletedMessage.newBuilder()
                        .setProjectId(projectId)
                        .addAllImageIds(imageIds)
                        .setDeleteStatus(deleteStatus)
                        .build();
        for (GrpcStubConfig grpcStubConfig : grpcStubConfigs) {
            AbstractStub abstractStub = grpcStubConfig.getGrpcStub();

            ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub stub =
                    (ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub) abstractStub;
            try {
                StatusCodeResponseOuterClass.StatusCodeResponse statusCodeResponse =
                        stub.deleteAiImagesOnWebDeleted(request).get();
                log.info(
                        "make a grpc deleteAiImagesOnWebDeleted for projectId "
                                + request.getProjectId());
            } catch (Exception e) {
                log.error(
                        "Failed to make a grpc on deleteAiImagesOnWebDeleted for projectId "
                                + request.getProjectId(),
                        e);
            }
        }
    }

    @Override
    public Integer updateReportStatusAfterEntryRecordCheck(long projectId, String type,
        Integer status) throws ServiceMessageException {
        AssertUtil.notNull(projectId, "projectId is null");
        ProjectGenericServiceOuterClass.ReportStatusRecordChange request = ProjectGenericServiceOuterClass.ReportStatusRecordChange.newBuilder()
            .setProjectId(projectId).setType(type).setStatus(status).build();
        for (GrpcStubConfig grpcStubConfig : grpcStubConfigs) {
            AbstractStub abstractStub = grpcStubConfig.getGrpcStub();

            ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub stub =
                (ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub)abstractStub;
            try {
                StatusCodeResponseOuterClass.StatusCodeResponse statusCodeResponse =
                    stub.updateReportStatusAfterEntryRecordCheck(request).get();
                log.info("make a grpc updateReportStatusAfterEntryRecordCheck for projectId " + request.getProjectId());
                return statusCodeResponse.getStatus();
            } catch (Exception e) {
                log.error("Failed to make a grpc on updateReportStatusAfterEntryRecordCheck for projectId " + request.getProjectId(), e);
            }
        }
        return -1;
    }


    @Override
    public void updateProjectStatusOnWebStatusChange(long projectId, ProjectEventTypeEnum typeEnum) {
        ProjectGenericServiceOuterClass.ProjectStatusChangeRequest request = ProjectGenericServiceOuterClass.ProjectStatusChangeRequest
            .newBuilder()
            .setProjectId(projectId)
            .setStatusCode(typeEnum.getCode())
            .setType(typeEnum.getType())
            .build();

        for (GrpcStubConfig grpcStubConfig : grpcStubConfigs) {
            AbstractStub abstractStub = grpcStubConfig.getGrpcStub();

            ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub stub =
                (ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub)abstractStub;
            try {
                StatusCodeResponseOuterClass.StatusCodeResponse statusCodeResponse =
                    stub.updateProjectStatusOnWebStatusChange(request).get();
            } catch (Exception e) {
                log.error("Failed to make a grpc on updateProjectStatusOnWebStatusChange for projectId " + projectId, e);
            }
        }
    }

    @Override
    public void updateProjectTagsOnWebChange(List<ProjectLabelDto> projectLabelDtos) {

        ProjectGenericServiceOuterClass.ProjectTagChangedRequest.Builder projectTagChangeRequestBuilder = ProjectGenericServiceOuterClass.ProjectTagChangedRequest.newBuilder();
        for (ProjectLabelDto labelDto : projectLabelDtos){
            ProjectGenericServiceOuterClass.ProjectTag projectTag = ProjectGenericServiceOuterClass.ProjectTag.newBuilder().setProjectId(labelDto.getProjectId()).addAllLabelId(labelDto.getLabelIds()).build();
            projectTagChangeRequestBuilder.addProjectTags(projectTag);
        }
        ProjectGenericServiceOuterClass.ProjectTagChangedRequest request = projectTagChangeRequestBuilder.build();
        for (GrpcStubConfig grpcStubConfig : grpcStubConfigs) {
            AbstractStub abstractStub = grpcStubConfig.getGrpcStub();

            ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub stub =
                (ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub)abstractStub;
            try {
                StatusCodeResponseOuterClass.StatusCodeResponse statusCodeResponse =
                    stub.updateProjectTagsOnWebChanged(request).get();
                log.info("make a grpc updateProjectTagsOnWebChange " + "code: " + statusCodeResponse.getCode());
            } catch (Exception e) {
                log.error("Failed to make a grpc on updateProjectTagsOnWebChange ", e);
            }
        }
    }

    @Override
    public void updateProjectInspectionTimeOnWebChange(long projectId, Long inspectionTime) {
        ProjectGenericServiceOuterClass.ProjectInspectionTime.Builder requestBuilder = ProjectGenericServiceOuterClass.ProjectInspectionTime.newBuilder();
        requestBuilder.setProjectId(projectId);
        if (inspectionTime != null){
            requestBuilder.setInspectionTime(inspectionTime);
        }
        ProjectInspectionTime request = requestBuilder.build();
        for (GrpcStubConfig grpcStubConfig : grpcStubConfigs) {
            AbstractStub abstractStub = grpcStubConfig.getGrpcStub();

            ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub stub =
                (ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub)abstractStub;
            try {
                StatusCodeResponseOuterClass.StatusCodeResponse statusCodeResponse =
                    stub.updateProjectInspectionTimeOnWebChanged(request).get();
                log.info("make a grpc updateProjectInspectionTimeOnWebChanged " + "code: " + statusCodeResponse.getCode());
            } catch (Exception e) {
                log.error("Failed to make a grpc on updateProjectInspectionTimeOnWebChanged ", e);
            }
        }
    }
}
