package com.bees360.service.grpc.job;

import com.bees360.entity.ProjectSyncLog;
import com.bees360.entity.enums.CompanyTypeEnum;
import com.bees360.entity.enums.ProjectSyncPointEnum;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectImageOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass;
import com.bees360.mapper.ProjectSyncLogMapper;
import com.bees360.schedule.exception.RetryableException;
import com.bees360.schedule.job.retry.RetryableJob;
import com.bees360.schedule.job.retry.RetryableOperation;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandlers;
import com.bees360.service.grpc.AiGrpcClient;
import com.bees360.service.grpc.BuildProjectInfoConvertersService;
import com.bees360.service.grpc.GrpcProjectBuilderUtil;
import com.bees360.service.grpc.GrpcReportGenerateService;
import com.bees360.service.grpc.GrpcStubConfig;
import com.google.common.base.Throwables;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.AbstractStub;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * project等数据从web端同步到ai的job
 */
@Slf4j
@Deprecated
public class DataTransferredJob extends RetryableJob {

    @Autowired
    private GrpcReportGenerateService grpcReportGenerateService;

    @Autowired
    private GrpcProjectBuilderUtil grpcProjectBuilderUtil;

    @Autowired
    private BuildProjectInfoConvertersService buildProjectInfoConvertersService;

    @Autowired
    private ProjectSyncLogMapper projectSyncLogMapper;


    @Override
    protected RetryableOperation getRetryableOperation() {
        return (jobContext) -> {
            JobDetail jobDetail = jobContext.getJobDetail();
            JobDataMap jobDataMap = jobDetail.getJobDataMap();
            String key = jobDetail.getKey().getName();
            long projectId = jobDataMap.getLongValue("projectId");
            boolean imageSyncFlag = jobDataMap.getBooleanValue("imageSyncFlag");
            String syncPoint = jobDataMap.getString("syncPoint");
            String grpcEndpoints = jobDataMap.getString("grpcEndpoints");
            List<GrpcStubConfig> abstractStubs = grpcReportGenerateService.getStubs();
            for (GrpcStubConfig abstractStub : abstractStubs) {
                AiGrpcClient aiGrpcClient = abstractStub.getAiGrpcClient();
                //创建job时以一个aiGrpcClient为一个job
                if (!grpcEndpoints.equals(aiGrpcClient.getEndpoints())) {
                    continue;
                }
                executeJob(aiGrpcClient, abstractStub.getGrpcStub(), projectId, imageSyncFlag, syncPoint, key);
            }
            log.info("finish generateReport with grpc on projectId {}", projectId);
        };
    }

    /**
     * 执行job的业务逻辑和异常处理
     * @param aiGrpcClient
     * @param grpcStub
     * @param projectId
     * @param jobKey
     * @throws RetryableException
     */
    private void executeJob(AiGrpcClient aiGrpcClient, AbstractStub grpcStub, long projectId,
                            boolean imageSyncFlag, String syncPoint, String jobKey) throws RetryableException {
        try {

            ReportGenerateServiceGrpc.ReportGenerateServiceFutureStub stub =
                (ReportGenerateServiceGrpc.ReportGenerateServiceFutureStub) grpcStub;

            ReportGenerateServiceOuterClass.ReportGenerateServiceRequest.Builder builder = ReportGenerateServiceOuterClass.ReportGenerateServiceRequest.newBuilder();
            ReportGenerateServiceOuterClass.Project redisGrpcProject = buildRedisGrpcProject(projectId);
            com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel esGrpcProject = buildProjectInfoConvertersService.buildEsGrpcProject(projectId, syncPoint);
            if (StringUtils.isBlank(esGrpcProject.getInsuranceCompanyName()) && StringUtils.isBlank(esGrpcProject.getRepairCompanyName())) {
                return;
            }
            if (CompanyTypeEnum.BEES360_TEST_CARRIER.equals(esGrpcProject.getRepairCompany())) {
                log.info("project don't need to sync to ai due to a test project {}", projectId);
                return;
            }
            //调用grpc
            ReportGenerateServiceOuterClass.ReportGenerateServiceRequest reportGenerateRequest =
                builder.setProject(redisGrpcProject)
                    .setEsProject(esGrpcProject)
                    .setImageSyncFlag(imageSyncFlag)
                    .setSyncPoint(syncPoint)
                    .build();
            log.info("executeJob sync redisGrpcProjectId:{}, esGrpcProjectId:{}", redisGrpcProject.getProjectId(),
                esGrpcProject.getProjectId());
            StatusCodeResponseOuterClass.StatusCodeResponse statusCodeResponse =
                stub.generateReport(reportGenerateRequest).get();
            log.info("the status code of generateReport is {} on projectId {} to server {}", statusCodeResponse,
                projectId, aiGrpcClient.getEndpoints());
            projectSyncLogMapper.insert(ProjectSyncLog.builder().projectId(projectId).syncEvent(syncPoint)
                .status(statusCodeResponse.getStatus() == 0 ? 1 : -1).build());
        } catch (InterruptedException ex) {
            projectSyncLogMapper.insert(ProjectSyncLog.builder().projectId(projectId).syncEvent(syncPoint)
                .status(-1).build());
            log.error("failed to execute job with key {}", jobKey, ex);
        } catch (ExecutionException ex) {
            throwRetryableException(projectId, ex);
        } catch (Exception e) {
            throwRetryableException(projectId, e);
        }
    }

    private ReportGenerateServiceOuterClass.Project buildRedisGrpcProject(long projectId) {
        // get project from database
        com.bees360.entity.Project project = grpcProjectBuilderUtil.getProject(projectId);
        //build project grpc message
        ReportGenerateServiceOuterClass.Project.Builder grpcProjectBuilder = grpcProjectBuilderUtil.createGrpcProjectBuilder(project);
        // build grpc project image message
        List<ProjectImageOuterClass.ProjectImage> grpcProjectImages = grpcProjectBuilderUtil.buildAllGrpcProjectImages(projectId);
        // build and set grpc project message
        ReportGenerateServiceOuterClass.Project grpcProject = grpcProjectBuilderUtil.buildAndSetGrpcProject(grpcProjectBuilder, project, grpcProjectImages);
        return grpcProject;
    }
    @Override
    protected RetryExceptionHandler getRetryExceptionHandler() {
        final int RETRY_COUNT_TO_LOG_ERROR = 10;
        final RetryExceptionHandler loggerHandler = RetryExceptionHandlers.getLogErrorRetryException((ctx) -> {
            return ctx.getRetryCount() > RETRY_COUNT_TO_LOG_ERROR;
        }, (ctx) -> {
            String message = "Retry[%s]: Fail to transfer data on project (%s).";
            long projectId = ctx.getJobContext().getJobDetail().getJobDataMap().getLongValue("projectId");
            return message.formatted(ctx.getRetryCount(), projectId);
        });
        return loggerHandler;
    }

    private void throwRetryableException(long projectId, Exception e) throws RetryableException{
        Throwable cause = null;
        if(e instanceof ExecutionException) {
            cause = e.getCause();
        } else {
            cause = Throwables.getRootCause(e);
        }

        if (cause instanceof StatusRuntimeException sre) {
            if (sre.getStatus().getCode().value() == io.grpc.Status.UNAVAILABLE.getCode().value()) {
                throw new RetryableException(e);
            }
        } else {
            log.error("failed to make a generateReport grpc call with projectId {}", projectId, e);
        }
    }
}
