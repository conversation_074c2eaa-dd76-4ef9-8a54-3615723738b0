package com.bees360.service.grpc;

import com.bees360.common.file.chunk.FileChunk;
import com.bees360.common.file.chunk.FileChunkDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Callable;

@Component
@Scope("prototype")
public class FileChunksTransfer implements Callable {

	private static final Logger log = LoggerFactory.getLogger(FileChunksTransfer.class);

	@Autowired
	private GrpcFileStreamingService grpcFileStreamingService;

	private long projectId;
	private List<FileChunk> fileChunks;

	public FileChunksTransfer() {

	}

    public FileChunksTransfer(FileChunkDto fileChunkDto) {
		if(fileChunkDto != null) {
			this.projectId = fileChunkDto.getProjectId();
			this.fileChunks = fileChunkDto.getFileChunks();
		}
	}

    @Override
    public Boolean call() throws Exception {
		if(fileChunks == null || fileChunks.size() == 0) {
			return false;
		}
		for (FileChunk fileChunk : fileChunks) {
			boolean succeeded = grpcFileStreamingService.transferFileChunk(projectId, fileChunk.getIndex(),
                fileChunk.getBytes());
            if(!succeeded) {
            	String message = "transferFileChunk failed for projectId:%d chunkIndex:%d";
            	message = message.formatted(projectId, fileChunk.getIndex());
            	log.error(message);
            	throw new RuntimeException(message);
            }
		}
        return true;
    }
}
