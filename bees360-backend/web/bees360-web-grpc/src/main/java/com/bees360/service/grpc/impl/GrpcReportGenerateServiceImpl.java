package com.bees360.service.grpc.impl;

import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceGrpc;
import com.bees360.service.grpc.AiGrpcClient;
import com.bees360.service.grpc.GrpcReportGenerateService;
import com.bees360.service.grpc.GrpcStubConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/07/25
 */

@Service("grpcReportGenerateService")
public class GrpcReportGenerateServiceImpl implements GrpcReportGenerateService {

    private static final Logger log = LoggerFactory.getLogger(GrpcReportGenerateServiceImpl.class);

    @Autowired
    private List<AiGrpcClient> aiGrpcClients;

    private List<GrpcStubConfig> grpcStubConfigs;

    @PostConstruct
    public void initStub() {
        grpcStubConfigs = new ArrayList();
        for (AiGrpcClient aiGrpcClient : aiGrpcClients) {
            ReportGenerateServiceGrpc.ReportGenerateServiceFutureStub stub = ReportGenerateServiceGrpc.newFutureStub(aiGrpcClient.getChannel());
            grpcStubConfigs.add(new GrpcStubConfig(stub, aiGrpcClient));
            log.info("reportGenerateServiceFutureStub to bees360-ai with endpoint {} ", aiGrpcClient.getEndpoints());
        }
        log.info("the number of reportGenerateServiceFutureStub to bees360-ai is {}", grpcStubConfigs.size());
    }

    public List<GrpcStubConfig> getStubs() {
        return this.grpcStubConfigs;
    }
}
