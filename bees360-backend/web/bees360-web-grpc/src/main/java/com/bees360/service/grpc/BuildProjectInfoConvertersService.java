package com.bees360.service.grpc;

import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.ServiceMessageException;
import com.bees360.entity.Company;
import com.bees360.entity.Member;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.dto.ProjectQuizDto;
import com.bees360.entity.vo.CompanyCard;
import com.bees360.entity.vo.HistoryLogVo;
import com.bees360.entity.vo.ProjectStatusVo;
import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceOuterClass;

import java.util.List;

/**
 *  start get -> get java pojo
 *  start build -> get grpc message
 * <AUTHOR>
 * @date 2020/04/30 14:22
 */
public interface BuildProjectInfoConvertersService {

    Project getProject(long projectId);

    List<Member> getMembers(long projectId);

    Company getCompany(Long companyId);

    CompanyCard getCompanyCard(Long companyId);

    List<ProjectImage> getProjectImages(long projectId, int fileSourceType) throws ServiceException;

    List<HistoryLogVo> getEventHistory(long projectId);

    List<ReportGenerateServiceOuterClass.Member> buildMembers(long projectId) throws ServiceException;

    List<com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectStatusVo> buildProjectStatusVoList(List<ProjectStatusVo> timeLines) throws ServiceException;

    ReportGenerateServiceOuterClass.Company buildCompany(Long companyId) throws ServiceException;

    List<ProjectQuizDto> listProjectQuiz(long projectId) throws ServiceMessageException;

    com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel buildEsGrpcProject(long projectId, String syncPoint);

}
