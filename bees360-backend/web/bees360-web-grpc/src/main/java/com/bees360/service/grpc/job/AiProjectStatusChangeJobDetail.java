package com.bees360.service.grpc.job;

import com.bees360.entity.enums.ProjectEventTypeEnum;
import com.bees360.schedule.util.QuartzJobConstant;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;

/**
 * 将数据从web传输到ai的job信息
 */
@Deprecated
public class AiProjectStatusChangeJobDetail {

    public static JobDetail createJobDetail(long projectId, ProjectEventTypeEnum typeEnum, String createdBy,
        Long createdAt,
        String endpoints, Class jobClass) {
        String jobName = "grpc-ai-project-status-change-%s-%d-%d-%d".formatted(endpoints,
            projectId, typeEnum.getCode(), System.currentTimeMillis());
        JobDataMap jobData = new JobDataMap();
        jobData.put("projectId", projectId);
        jobData.put("code", typeEnum.getCode());
        jobData.put("type", typeEnum.getType());
        jobData.put("createdBy", createdBy);
        jobData.put("createdAt", createdAt);
        jobData.put("grpcEndpoints", endpoints);
        String description =
            "AiProjectStatusChangeJobDetail update project ai status on projectId %d".formatted(projectId);
        // @formatter:off
        JobDetail job = JobBuilder.newJob(jobClass)
            .withIdentity(jobName, QuartzJobConstant.WebGroup.PROJECT_UPDATE_AI_STATUS)
            .usingJobData(jobData)
            .withDescription(description)
            .requestRecovery(true)
            .build();
        // @formatter:on
        return job;
    }
}
