package com.bees360.service.grpc.impl;

import com.bees360.base.exception.ServiceException;
import com.bees360.common.grpc.GrpcClient;
import com.bees360.entity.enums.ProjectEventTypeEnum;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.Web2AiStatusTransferJob;
import com.bees360.mapper.ProjectSyncLogMapper;
import com.bees360.schedule.service.SchedulerManagerTransactionalDelegate;
import com.bees360.service.grpc.GrpcProjectGenericService;
import com.bees360.service.grpc.GrpcStubConfig;

import java.time.Duration;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static com.bees360.base.MessageCode.PARAM_INVALID;

/**
 * 服务端
 *
 * <AUTHOR>
 * @date 2020-2-18 12:32:50
 */
@Component
@Slf4j
public class GrpcProjectStatusChangeServiceComponent {

    @Autowired private SchedulerManagerTransactionalDelegate schedulerManagerTransactionalDelegate;

    @Autowired private GrpcProjectGenericService grpcProjectGenericService;

    @Autowired private ProjectSyncLogMapper projectSyncLogMapper;
    @Autowired private JobScheduler jobScheduler;
    private static final int RETRY_COUNT = 5;
    private static final float RETRY_INCREASE_FACTOR = 1.5F;
    private static final Duration RETRY_DELAY = Duration.ofMinutes(1);

    @Transactional
    public void updateProjectStatusToAi(long projectId, ProjectEventTypeEnum typeEnum) {
        scheduleJob(projectId, typeEnum, null, null);
    }

    @Transactional
    public void updateProjectStatusToAi(long projectId, ProjectEventTypeEnum typeEnum, Long createdAt) throws ServiceException {
        if (projectId == 0) {
            throw new ServiceException(PARAM_INVALID, "Project id can not be 0.");
        }

        scheduleJob(projectId, typeEnum, null, createdAt);
    }

    private void scheduleJob(
            long projectId, ProjectEventTypeEnum typeEnum, String createdBy, Long createdAt) {
        try {
            scheduleProjectStatusChangeTransferJob(projectId, typeEnum, createdBy, createdAt);
        } catch (Exception e) {
            String message =
                    "Failed to updateProjectStatusToAi for projectId %d when ProjectImageUploadFinishedEvent occur";
            message = message.formatted(projectId);
            log.error(message, e);
        }
    }

    @Transactional
    public void updateProjectStatusToAi(
            long projectId, ProjectEventTypeEnum typeEnum, String createdBy, Long createdAt) {
        assert projectId != 0;

        scheduleJob(projectId, typeEnum, createdBy, createdAt);
    }

    private void scheduleProjectStatusChangeTransferJob(
            long projectId, ProjectEventTypeEnum typeEnum, String createdBy, Long createdAt) {
        List<GrpcStubConfig> grpcStubConfigs = grpcProjectGenericService.getStubs();
        for (GrpcStubConfig grpcStubConfig : grpcStubConfigs) {
            GrpcClient grpcClient = grpcStubConfig.getAiGrpcClient();
            var transferJob = new Web2AiStatusTransferJob();
            transferJob.setProjectId(projectId);
            transferJob.setCode(typeEnum.getCode());
            transferJob.setType(typeEnum.getType());
            transferJob.setCreateAt(createdAt);
            transferJob.setCreatedBy(createdBy);
            transferJob.setEndpoint(grpcClient.getEndpoints());
            var job = JobPayloads.encode(String.valueOf(transferJob.hashCode()), transferJob);
            job = RetryableJob.of(job, RETRY_COUNT, RETRY_DELAY, RETRY_INCREASE_FACTOR);
            jobScheduler.schedule(job);
        }
    }
}
