package com.bees360.service.grpc.job;

import com.bees360.entity.ProjectSyncLog;
import com.bees360.entity.enums.CompanyTypeEnum;
import com.bees360.grpc.GrpcApi;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectImageOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass;
import com.bees360.job.ExecutorConcurrentCount;
import com.bees360.job.registry.Web2AiDataTransferJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.mapper.ProjectSyncLogMapper;
import com.bees360.service.grpc.AiGrpcClient;
import com.bees360.service.grpc.BuildProjectInfoConvertersService;
import com.bees360.service.grpc.GrpcProjectBuilderUtil;
import com.bees360.service.grpc.GrpcReportGenerateService;
import com.bees360.service.grpc.GrpcStubConfig;
import io.grpc.StatusException;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.AbstractStub;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;

@Slf4j
@Component
@ToString
public class Web2AiDataTransferredJobExecutor extends AbstractJobExecutor<Web2AiDataTransferJob>
        implements ExecutorConcurrentCount {
    @Autowired private GrpcReportGenerateService grpcReportGenerateService;
    @Autowired private GrpcProjectBuilderUtil grpcProjectBuilderUtil;
    @Autowired private BuildProjectInfoConvertersService buildProjectInfoConvertersService;
    @Autowired private ProjectSyncLogMapper projectSyncLogMapper;
    private static final Integer DEFAULT_MAX_CONCURRENT_COUNT = 8;
    @Override
    public int getMaxConcurrentCount() {
        return DEFAULT_MAX_CONCURRENT_COUNT;
    }

    @Override
    protected void handle(Web2AiDataTransferJob job) throws IOException {
        List<GrpcStubConfig> abstractStubs = grpcReportGenerateService.getStubs();
        long projectId = job.getProjectId();
        boolean imageSyncFlag = job.isImageSyncFlag();
        String endpoint = job.getEndpoint();
        var syncPoint = job.getSyncPoint();
        for (GrpcStubConfig abstractStub : abstractStubs) {
            AiGrpcClient aiGrpcClient = abstractStub.getAiGrpcClient();
            // 创建job时以一个aiGrpcClient为一个job
            if (!endpoint.equals(aiGrpcClient.getEndpoints())) {
                continue;
            }
            log.info("Start to sync project '{}' data from web to ai.", projectId);
            executeJob(
                    aiGrpcClient, abstractStub.getGrpcStub(), projectId, imageSyncFlag, syncPoint);
            log.info("Successfully sync project '{}' data from web to ai.", projectId);
        }
    }

    private void executeJob(
            AiGrpcClient aiGrpcClient,
            AbstractStub grpcStub,
            long projectId,
            boolean imageSyncFlag,
            String syncPoint)
            throws IOException {
        try {

            ReportGenerateServiceGrpc.ReportGenerateServiceFutureStub stub =
                    (ReportGenerateServiceGrpc.ReportGenerateServiceFutureStub) grpcStub;

            ReportGenerateServiceOuterClass.ReportGenerateServiceRequest.Builder builder =
                    ReportGenerateServiceOuterClass.ReportGenerateServiceRequest.newBuilder();
            ReportGenerateServiceOuterClass.Project redisGrpcProject =
                    buildRedisGrpcProject(projectId);
            com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel esGrpcProject =
                    buildProjectInfoConvertersService.buildEsGrpcProject(projectId, syncPoint);
            if (StringUtils.isBlank(esGrpcProject.getInsuranceCompanyName())
                    && StringUtils.isBlank(esGrpcProject.getRepairCompanyName())) {
                return;
            }
            if (CompanyTypeEnum.BEES360_TEST_CARRIER.equals(esGrpcProject.getRepairCompany())) {
                log.info("project don't need to sync to ai due to a test project {}", projectId);
                return;
            }
            // 调用grpc
            ReportGenerateServiceOuterClass.ReportGenerateServiceRequest reportGenerateRequest =
                    builder.setProject(redisGrpcProject)
                            .setEsProject(esGrpcProject)
                            .setImageSyncFlag(imageSyncFlag)
                            .setSyncPoint(syncPoint)
                            .build();

            StatusCodeResponseOuterClass.StatusCodeResponse statusCodeResponse =
                    stub.generateReport(reportGenerateRequest).get();
            var syncLog =
                    ProjectSyncLog.builder()
                            .projectId(projectId)
                            .syncEvent(syncPoint)
                            .status(statusCodeResponse.getStatus() == 0 ? 1 : -1)
                            .build();
            projectSyncLogMapper.insert(syncLog);
        } catch (InterruptedException ex) {
            var syncLog =
                    ProjectSyncLog.builder()
                            .projectId(projectId)
                            .syncEvent(syncPoint)
                            .status(-1)
                            .build();
            projectSyncLogMapper.insert(syncLog);
            Thread.currentThread().interrupt();
            throw new IllegalStateException(ex);
        } catch (ExecutionException ex) {
            var cause = ex.getCause();
            if (cause instanceof IOException exception) {
                throw exception;
            } else if (cause instanceof StatusRuntimeException || cause instanceof StatusException) {
                throw GrpcApi.translateException(cause);
            } else if (cause instanceof RuntimeException exception) {
                throw exception;
            } else {
                throw new IllegalStateException(cause);
            }
        } catch (StatusRuntimeException e) {
            throw GrpcApi.translateException(e);
        }
    }

    private ReportGenerateServiceOuterClass.Project buildRedisGrpcProject(long projectId) {
        // get project from database
        com.bees360.entity.Project project = grpcProjectBuilderUtil.getProject(projectId);
        // build project grpc message
        ReportGenerateServiceOuterClass.Project.Builder grpcProjectBuilder =
                grpcProjectBuilderUtil.createGrpcProjectBuilder(project);
        // build grpc project image message
        List<ProjectImageOuterClass.ProjectImage> grpcProjectImages =
                grpcProjectBuilderUtil.buildAllGrpcProjectImages(projectId);
        // build and set grpc project message
        return grpcProjectBuilderUtil.buildAndSetGrpcProject(
                grpcProjectBuilder, project, grpcProjectImages);
    }
}
