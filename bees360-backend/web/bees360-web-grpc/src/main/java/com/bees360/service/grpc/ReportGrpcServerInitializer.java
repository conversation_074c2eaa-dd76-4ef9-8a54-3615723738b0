package com.bees360.service.grpc;

import com.bees360.common.grpc.GrpcServerInitializer;
import com.bees360.grpc.ExceptionTranslateInterceptor;
import com.bees360.web.core.properties.bean.GrpcProperties;
import io.grpc.util.TransmitStatusRuntimeExceptionInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import io.grpc.BindableService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/28
 */
@Slf4j
@Component
public class ReportGrpcServerInitializer extends GrpcServerInitializer {

    public ReportGrpcServerInitializer(
            List<BindableService> services,
            GrpcProperties grpcProperties,
            @Value("${bees360.feature-switch.enable-default-grpc-server-initializer:false}") boolean enableGrpcDefaultServerInitializer) {
        super(
                services,
                grpcProperties.getServer().getPort(),
                enableGrpcDefaultServerInitializer
                        ? new ExceptionTranslateInterceptor()
                        : TransmitStatusRuntimeExceptionInterceptor.instance());
    }

    @Override
    public void run() {
        try {
            super.run();
        } catch (Exception e) {
            log.error("error start report grpc.", e);
        }
    }

    @Override
    public void showInfo() {
        StringBuilder gprcServerInfo = new StringBuilder();
        gprcServerInfo.append("web-backend Grpc Server started on port: ").append(getPort()).append("\n");
        gprcServerInfo.append("The following ai-backend Grpc Services is available: \n");
        for (BindableService service : getServices()) {
            gprcServerInfo.append("\t").append(service.getClass().getSimpleName()).append("\n");
        }
        log.info("ai-backend Gprc Server Info:\n" + gprcServerInfo);
    }
}
