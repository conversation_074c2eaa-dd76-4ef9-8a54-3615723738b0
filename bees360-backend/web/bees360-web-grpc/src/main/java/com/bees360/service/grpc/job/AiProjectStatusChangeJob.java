package com.bees360.service.grpc.job;

import com.bees360.entity.ProjectSyncLog;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectGenericServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectGenericServiceOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass;
import com.bees360.mapper.ProjectSyncLogMapper;
import com.bees360.schedule.exception.RetryableException;
import com.bees360.schedule.job.retry.RetryableJob;
import com.bees360.schedule.job.retry.RetryableOperation;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandlers;
import com.bees360.service.grpc.AiGrpcClient;
import com.bees360.service.grpc.GrpcProjectGenericService;
import com.bees360.service.grpc.GrpcStubConfig;
import com.google.common.base.Throwables;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.AbstractStub;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * project等数据从web端同步到ai的job
 */
@Slf4j
@Deprecated
public class AiProjectStatusChangeJob extends RetryableJob {

    @Autowired
    private GrpcProjectGenericService grpcProjectGenericService;

    @Autowired
    private ProjectSyncLogMapper projectSyncLogMapper;

    @Override
    protected RetryableOperation getRetryableOperation() {
        return (jobContext) -> {
            JobDetail jobDetail = jobContext.getJobDetail();
            JobDataMap jobDataMap = jobDetail.getJobDataMap();
            String key = jobDetail.getKey().getName();
            long projectId = jobDataMap.getLongValue("projectId");
            Integer code = jobDataMap.getInt("code");
            String type = jobDataMap.getString("type");
            Long createdAt = MapUtils.getLong(jobDataMap, "createdAt");
            String createdBy = MapUtils.getString(jobDataMap, "createdBy");
            String grpcEndpoints = jobDataMap.getString("grpcEndpoints");
            List<GrpcStubConfig> abstractStubs = grpcProjectGenericService.getStubs();
            for (GrpcStubConfig abstractStub : abstractStubs) {
                AiGrpcClient aiGrpcClient = abstractStub.getAiGrpcClient();
                //创建job时以一个aiGrpcClient为一个job
                if (!grpcEndpoints.equals(aiGrpcClient.getEndpoints())) {
                    continue;
                }
                executeJob(aiGrpcClient, abstractStub.getGrpcStub(), projectId, code, type, key, createdBy, createdAt);
            }
            log.info("finish update project ai status with grpc on projectId {}, code:{}", projectId, code);
        };
    }

    /**
     * 执行job的业务逻辑和异常处理
     *
     * @param aiGrpcClient
     * @param grpcStub
     * @param projectId
     * @param jobKey
     * @throws RetryableException
     */
    private void executeJob(AiGrpcClient aiGrpcClient, AbstractStub grpcStub, long projectId, Integer code, String type,
        String jobKey, String createdBy, Long createdAt) throws RetryableException {
        log.info("AiProjectStatusChangeJob on projectId:{} code:{}, createdAt:{}, createdBy:{}", projectId, code, createdAt, createdBy);
        try {
            ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub stub =
                (ProjectGenericServiceGrpc.ProjectGenericServiceFutureStub) grpcStub;

            ProjectGenericServiceOuterClass.ProjectStatusChangeRequest request = ProjectGenericServiceOuterClass.ProjectStatusChangeRequest
                .newBuilder()
                .setProjectId(projectId)
                .setStatusCode(code)
                .setType(type)
                .setCreatedAt(Optional.ofNullable(createdAt).orElse(0L))
                .setCreatedBy(Optional.ofNullable(createdBy).orElse(""))
                .build();
            //调用grpc
            StatusCodeResponseOuterClass.StatusCodeResponse statusCodeResponse = stub
                .updateProjectStatusOnWebStatusChange(request).get();
            projectSyncLogMapper.insert(ProjectSyncLog.builder().projectId(projectId).syncEvent(type)
                .status(statusCodeResponse.getStatus() == 0 ? 1 : -1).build());
            log.info("AiProjectStatusChangeJob on projectId:{} code:{}, statusCode:{}, endPoint:{}",
                projectId, code, statusCodeResponse, aiGrpcClient.getEndpoints());
        } catch (InterruptedException ex) {
            log.error("failed to execute job with key {}", jobKey, ex);
        } catch (ExecutionException ex) {
            throwRetryableException(projectId, ex);
        } catch (Exception e) {
            throwRetryableException(projectId, e);
        }
    }


    @Override
    protected RetryExceptionHandler getRetryExceptionHandler() {
        final int RETRY_COUNT_TO_LOG_ERROR = 10;
        final RetryExceptionHandler loggerHandler = RetryExceptionHandlers.getLogErrorRetryException((ctx) -> {
            return ctx.getRetryCount() > RETRY_COUNT_TO_LOG_ERROR;
        }, (ctx) -> {
            String message = "Retry[%s]: Fail to update ai project status on project (%s).";
            long projectId = ctx.getJobContext().getJobDetail().getJobDataMap().getLongValue("projectId");
            return message.formatted(ctx.getRetryCount(), projectId);
        });
        return loggerHandler;
    }

    private void throwRetryableException(long projectId, Exception e) throws RetryableException {
        Throwable cause = null;
        if (e instanceof ExecutionException) {
            cause = e.getCause();
        } else {
            cause = Throwables.getRootCause(e);
        }

        if (cause instanceof StatusRuntimeException sre) {
            if (sre.getStatus().getCode().value() == io.grpc.Status.UNAVAILABLE.getCode().value()) {
                throw new RetryableException(e);
            }
        } else {
            log.error("failed to make a AiProjectStatusChangeJob grpc call with projectId {}", projectId, e);
        }
    }
}
