package com.bees360.service.grpc.job;

import com.bees360.schedule.util.QuartzJobConstant;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;

/**
 * 将数据从web传输到ai的job信息
 */
@Deprecated
public class DataTransferredJobDetail {

    public static JobDetail createJobDetail(long projectId, boolean imageSyncFlag, String endpoints,
                                            String syncPoint, Class jobClass) {
        // 该jobName会限制该照片只能往指定的claim的特定路径上传一次
        String jobName = "grpc-data-transfer-%s-%d-%d".formatted(endpoints,
            projectId, System.currentTimeMillis());
        JobDataMap jobData = new JobDataMap();
        jobData.put("projectId", projectId);
        jobData.put("imageSyncFlag", imageSyncFlag);
        jobData.put("syncPoint", syncPoint);
        jobData.put("grpcEndpoints", endpoints);
        String description =
            "Transfer the project and image data from web to ai on projectId %d".formatted(projectId);
        // @formatter:off
        JobDetail job = JobBuilder.newJob(jobClass)
            .withIdentity(jobName, QuartzJobConstant.WebGroup.REPORT_DATA_TRANSFER)
            .usingJobData(jobData)
            .withDescription(description)
            .requestRecovery(true)
            .build();
        // @formatter:on
        return job;
    }
}
