package com.bees360.service.grpc.job;

import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass;
import com.bees360.schedule.exception.RetryableException;
import com.bees360.schedule.job.retry.RetryableJob;
import com.bees360.schedule.job.retry.RetryableOperation;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandlers;
import com.bees360.service.grpc.AiGrpcClient;
import com.bees360.service.grpc.GrpcProjectBuilderUtil;
import com.bees360.service.grpc.GrpcReportGenerateService;
import com.bees360.service.grpc.GrpcStubConfig;
import com.google.common.base.Throwables;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.AbstractStub;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * retry sending changeStatusToReturnToClient request to ai when failure occur
 */
@Slf4j
@Component
public class ProjectChangedToReturnToClientGrpcJob extends RetryableJob {

    @Autowired
    private GrpcReportGenerateService grpcReportGenerateService;

    @Autowired
    private GrpcProjectBuilderUtil grpcProjectBuilderUtil;

    @Override
    protected RetryableOperation getRetryableOperation() {
        return ctx -> {
            long projectId = ctx.getJobDetail().getJobDataMap().getLongValue("projectId");

            JobDetail jobDetail = ctx.getJobDetail();
            JobDataMap jobDataMap = jobDetail.getJobDataMap();
            String key = jobDetail.getKey().getName();

            String grpcEndpoints = jobDataMap.getString("grpcEndpoints");
            List<GrpcStubConfig> abstractStubs = grpcReportGenerateService.getStubs();
            for (GrpcStubConfig abstractStub : abstractStubs) {
                AiGrpcClient aiGrpcClient = abstractStub.getAiGrpcClient();
                //创建job时以一个aiGrpcClient为一个job
                if (!grpcEndpoints.equals(aiGrpcClient.getEndpoints())) {
                    continue;
                }
                executeJob(aiGrpcClient, abstractStub.getGrpcStub(), projectId, key);
            }
            log.info("finish generateReport with grpc on projectId {}", projectId);
        };
    }

    /**
     * 执行job的业务逻辑和异常处理
     *
     * @param aiGrpcClient
     * @param grpcStub
     * @param projectId
     * @param jobKey
     * @throws RetryableException
     */
    private void executeJob(AiGrpcClient aiGrpcClient, AbstractStub grpcStub, long projectId, String jobKey) throws RetryableException {
        try {
            ReportGenerateServiceGrpc.ReportGenerateServiceFutureStub stub =
                (ReportGenerateServiceGrpc.ReportGenerateServiceFutureStub) grpcStub;
            // remove data in cache on bees360-ai's cache
            StatusCodeResponseOuterClass.StatusCodeResponse statusCodeResponse =
                stub.changeStatusToReturnToClient(grpcProjectBuilderUtil.buildProjectIdRequest(projectId)).get();
            log.info("the status code of changeStatusToReturnToClient is {} to server {}", statusCodeResponse,
                aiGrpcClient.getEndpoints());
        } catch (InterruptedException ex) {
            log.error("failed to execute job with key {}", jobKey, ex);
        } catch (ExecutionException ex) {
            throwRetryableException(projectId, ex);
        } catch (Exception e) {
            throwRetryableException(projectId, e);
        }
    }

    @Override
    protected RetryExceptionHandler getRetryExceptionHandler() {
        final int RETRY_COUNT_TO_LOG_ERROR = 10;
        final RetryExceptionHandler loggerHandler = RetryExceptionHandlers.getLogErrorRetryException((ctx) -> {
            return ctx.getRetryCount() > RETRY_COUNT_TO_LOG_ERROR;
        }, (ctx) -> {
            String message = "Retry[%s]: Fail to clear data on project (%s) when project status is changed to return-to-client.";
            long projectId = ctx.getJobContext().getJobDetail().getJobDataMap().getLongValue("projectId");
            return message.formatted(ctx.getRetryCount(), projectId);
        });
        return loggerHandler;
    }

    private void throwRetryableException(long projectId, Exception e) throws RetryableException{
        Throwable cause = null;
        if(e instanceof ExecutionException) {
            cause = e.getCause();
        } else {
            cause = Throwables.getRootCause(e);
        }

        if (cause instanceof StatusRuntimeException sre) {
            if (sre.getStatus().getCode().value() == io.grpc.Status.UNAVAILABLE.getCode().value()) {
                throw new RetryableException(e);
            }
        } else {
            log.error("failed to make a generateReport grpc call with projectId {}", projectId, e);
        }
    }
}
