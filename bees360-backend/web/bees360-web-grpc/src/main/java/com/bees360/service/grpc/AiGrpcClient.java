package com.bees360.service.grpc;

import com.bees360.common.grpc.GrpcClient;
import com.bees360.common.grpc.GrpcConfig;
import io.grpc.ManagedChannel;

/**
 * <AUTHOR>
 * @date 2019/07/26
 */
public class AiGrpcClient extends GrpcClient{

    private ManagedChannel channel;

    public AiGrpcClient(GrpcConfig grpcConfig) {
        super(grpcConfig);
        this.channel = buildChannel();
    }

    public ManagedChannel getChannel() {
        return channel;
    }

    @Override
    public String toString() {
        StringBuffer buf = new StringBuffer();
        buf.append("bees360-web-backend-grpc-client service endpoints=").append(this.getEndpoints());
        return buf.toString();
    }
}
