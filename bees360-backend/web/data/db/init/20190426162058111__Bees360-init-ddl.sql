-- migrate:up
/*
 Navicat Premium Data Transfer

 Source Server         : prod-*************
 Source Server Type    : MySQL
 Source Server Version : 50725
 Source Host           : localhost:3306
 Source Schema         : Bees360

 Target Server Type    : MySQL
 Target Server Version : 50725
 File Encoding         : 65001

 Date: 17/04/2019 14:37:12
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for Adjuster
-- ----------------------------
DROP TABLE IF EXISTS `Adjuster`;
CREATE TABLE `Adjuster`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `operating_city_state` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `additional_operating_territories` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `designated_home_state_license` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `additional_license` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `years_of_experience` tinyint(4) NOT NULL,
  `more_than_100miles_traveled` tinyint(1) NOT NULL,
  `cat_event_deployed` tinyint(1) NOT NULL,
  `ai_roster_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  CONSTRAINT `Adjuster_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for AppVersion
-- ----------------------------
DROP TABLE IF EXISTS `AppVersion`;
CREATE TABLE `AppVersion`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `type` tinyint(4) NOT NULL,
  `is_display` tinyint(4) NULL DEFAULT NULL COMMENT 'Whether updates are displayed.',
  `is_compulsory` tinyint(1) NOT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Bidding
-- ----------------------------
DROP TABLE IF EXISTS `Bidding`;
CREATE TABLE `Bidding`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `overview` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `stories` int(11) NULL DEFAULT NULL,
  `roof_primary_pitch` tinyint(2) NULL DEFAULT NULL COMMENT 'the value should be (0-24)/12',
  `roof_perimeter` float NULL DEFAULT NULL COMMENT 'default unit is ft',
  `longest_length` float NULL DEFAULT NULL COMMENT 'The longest side of the roof.',
  `roof_complexity` int(11) NULL DEFAULT NULL COMMENT 'default unit is ',
  `roof_area` float NULL DEFAULT NULL COMMENT 'default unit is sqft',
  `roof_boundary` geometry NULL COMMENT 'a polygon representing the edge of the roof',
  `map_rect` geometry NULL COMMENT 'Boundary on Google map.',
  `roof_rect` geometry NULL COMMENT 'the minimum enclosing rectangle of the edge of the roof',
  `company_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `company_website` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `industry_experience` int(11) NULL DEFAULT NULL COMMENT '# of years in roofing industry',
  `shingle_grade` tinyint(4) NULL DEFAULT NULL,
  `shingle_price` float NULL DEFAULT NULL COMMENT 'Unit price for single($/SQ)',
  `high_roof` tinyint(1) NULL DEFAULT NULL COMMENT 'the # of stories is more than 2',
  `high_steep_roof_charge` float NULL DEFAULT NULL COMMENT 'Unit of price is $',
  `total_price` float NULL DEFAULT NULL COMMENT 'Unit of price is $',
  `intro` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `modified_by` bigint(20) NOT NULL,
  `modified_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `modified_by`(`modified_by`) USING BTREE,
  CONSTRAINT `Bidding_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Bidding_ibfk_2` FOREIGN KEY (`modified_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 640 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'save the data for square report and bidding' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for CellPhoneReportElement
-- ----------------------------
DROP TABLE IF EXISTS `CellPhoneReportElement`;
CREATE TABLE `CellPhoneReportElement`  (
  `project_id` bigint(20) NOT NULL,
  `component_id` tinyint(4) NOT NULL,
  `component_type` tinyint(4) NOT NULL,
  `inspection_category` tinyint(4) NOT NULL,
  `relative_position_type` tinyint(4) NOT NULL,
  `object_description_json` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `added_by` bigint(20) NOT NULL,
  `created_time` bigint(20) NOT NULL,
  INDEX `added_by`(`added_by`) USING BTREE,
  INDEX `CellPhoneReportElement_ibfk_1`(`project_id`) USING BTREE,
  INDEX `idx_component_id`(`component_id`) USING BTREE,
  INDEX `idx_inspection_category`(`inspection_category`) USING BTREE,
  INDEX `idx_relative_position_type`(`relative_position_type`) USING BTREE,
  CONSTRAINT `CellPhoneReportElement_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `CellPhoneReportElement_ibfk_2` FOREIGN KEY (`added_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for City
-- ----------------------------
DROP TABLE IF EXISTS `City`;
CREATE TABLE `City`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `region_id` bigint(20) NOT NULL,
  `city_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `gps_location` point NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_region_id`(`region_id`) USING BTREE,
  CONSTRAINT `City_ibfk_1` FOREIGN KEY (`region_id`) REFERENCES `Region` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 48147 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Comment
-- ----------------------------
DROP TABLE IF EXISTS `Comment`;
CREATE TABLE `Comment`  (
  `chat_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `content` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `created_time` bigint(20) NOT NULL,
  `content_type` int(11) NOT NULL,
  PRIMARY KEY (`chat_id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  FULLTEXT INDEX `ftidx_content`(`content`),
  CONSTRAINT `Comment_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Comment_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Company
-- ----------------------------
DROP TABLE IF EXISTS `Company`;
CREATE TABLE `Company`  (
  `company_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `company_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `company_type` int(11) NOT NULL,
  `website` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `logo` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `contact_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `phone` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  `created_time` bigint(20) NOT NULL DEFAULT 0,
  `updated_time` bigint(20) NOT NULL DEFAULT 0,
  PRIMARY KEY (`company_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1852 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ContactUs
-- ----------------------------
DROP TABLE IF EXISTS `ContactUs`;
CREATE TABLE `ContactUs`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `last_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `phone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `company` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `content` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 110 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Country
-- ----------------------------
DROP TABLE IF EXISTS `Country`;
CREATE TABLE `Country`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `country_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10002 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for CustomizedReportElement
-- ----------------------------
DROP TABLE IF EXISTS `CustomizedReportElement`;
CREATE TABLE `CustomizedReportElement`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `company_id` bigint(20) NOT NULL,
  `element_id` bigint(20) NOT NULL,
  `report_type` tinyint(4) NOT NULL,
  `type` tinyint(4) NOT NULL,
  `element_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'element_name',
  `element_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'element value',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `CustomizedReportElement_ibfk_1`(`element_id`) USING BTREE,
  INDEX `CustomizedReportElement_ibfk_2`(`project_id`) USING BTREE,
  CONSTRAINT `CustomizedReportElement_ibfk_1` FOREIGN KEY (`element_id`) REFERENCES `OnSiteReportImageElement` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `CustomizedReportElement_ibfk_2` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6229 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for CustomizedSIBCategory
-- ----------------------------
DROP TABLE IF EXISTS `CustomizedSIBCategory`;
CREATE TABLE `CustomizedSIBCategory`  (
  `id` bigint(20) NOT NULL,
  `category_type` tinyint(2) NOT NULL COMMENT '0:root,1:subcategory,2:description',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `parent_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for EventHistory
-- ----------------------------
DROP TABLE IF EXISTS `EventHistory`;
CREATE TABLE `EventHistory`  (
  `event_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `status` int(11) NOT NULL,
  `status_time` bigint(20) NOT NULL,
  `description` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `created_time` bigint(20) NOT NULL,
  `modified_by` bigint(20) NOT NULL,
  PRIMARY KEY (`event_id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `modified_by`(`modified_by`) USING BTREE,
  CONSTRAINT `EventHistory_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `EventHistory_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `EventHistory_ibfk_3` FOREIGN KEY (`modified_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 107113 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for House
-- ----------------------------
DROP TABLE IF EXISTS `House`;
CREATE TABLE `House`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `gps_location` point NOT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `state` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `city` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `address` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `zip_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `overview` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `stories` int(11) NULL DEFAULT NULL,
  `roof_primary_pitch` tinyint(2) NULL DEFAULT NULL COMMENT 'the value should be (0-24)/12',
  `roof_perimeter` float NULL DEFAULT NULL COMMENT 'default unit is ft',
  `longest_length` float NULL DEFAULT NULL COMMENT 'The longest side of the roof.',
  `roof_complexity` int(11) NULL DEFAULT NULL COMMENT 'option from [1:Low, 2:]',
  `roof_area` float NULL DEFAULT NULL COMMENT 'default unit is sqft',
  `roof_boundary` geometry NULL COMMENT 'a polygon representing the edge of the roof',
  `roof_rect` geometry NULL COMMENT 'the minimum enclosing rectangle of the edge of the roof',
  `modified_by` bigint(20) NOT NULL,
  `modified_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `modified_by`(`modified_by`) USING BTREE,
  CONSTRAINT `House_ibfk_1` FOREIGN KEY (`modified_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 625 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'save the data of the house to reduce the calculation' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for HouseCategory
-- ----------------------------
DROP TABLE IF EXISTS `HouseCategory`;
CREATE TABLE `HouseCategory`  (
  `id` bigint(20) NOT NULL,
  `selector` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_index` int(4) NOT NULL,
  `category_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subcategory_index` int(4) NULL DEFAULT NULL,
  `subcategory` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `additional` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for HouseImageSegmentType
-- ----------------------------
DROP TABLE IF EXISTS `HouseImageSegmentType`;
CREATE TABLE `HouseImageSegmentType`  (
  `id` int(11) NOT NULL,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `parent_id` int(11) NULL DEFAULT NULL,
  `is_leaf` tinyint(1) NOT NULL COMMENT 'is leaf,0:false, 1:true',
  `value_type` tinyint(2) NULL DEFAULT NULL COMMENT 'the value type, single election, check etc.',
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'the input unit.',
  `code_type` tinyint(2) NULL DEFAULT NULL COMMENT 'the code type, structures,direction etc.',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for HouseSegmentValue
-- ----------------------------
DROP TABLE IF EXISTS `HouseSegmentValue`;
CREATE TABLE `HouseSegmentValue`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'the segment value id.',
  `project_id` bigint(20) NOT NULL COMMENT 'the project id.',
  `code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'the segment value code.quot HouseImageSegment id.',
  `value` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'the value.',
  `additional` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'additional input',
  `value_type` tinyint(2) NULL DEFAULT NULL COMMENT 'the value type, single election, check etc.',
  `code_type` tinyint(2) NOT NULL COMMENT 'the code type, structures,direction etc.',
  `root_id` bigint(20) NULL DEFAULT NULL COMMENT 'the segment tree root id.',
  `is_damage` tinyint(1) NULL DEFAULT NULL COMMENT '0:no damage 1:is damage.',
  `is_required` tinyint(1) NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `HouseSegmentValue_ibfk_1`(`project_id`) USING BTREE,
  CONSTRAINT `HouseSegmentValue_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4215 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ImageAnnotation
-- ----------------------------
DROP TABLE IF EXISTS `ImageAnnotation`;
CREATE TABLE `ImageAnnotation`  (
  `annotation_id` bigint(20) NOT NULL,
  `image_id` bigint(20) NOT NULL,
  `facet_id` int(11) NOT NULL,
  `project_id` bigint(20) NOT NULL,
  `annotation_polygon` geometry NOT NULL,
  `created_time` bigint(20) NOT NULL,
  `center_point` point NOT NULL,
  `annotation_type` int(11) NOT NULL,
  `usage_type` tinyint(4) NULL DEFAULT 1,
  `generated_by` bigint(20) NOT NULL,
  PRIMARY KEY (`image_id`, `annotation_id`) USING BTREE,
  INDEX `facet_id`(`facet_id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `generated_by`(`generated_by`) USING BTREE,
  SPATIAL INDEX `sidx_image_geometry`(`annotation_polygon`),
  SPATIAL INDEX `sidx_gps_location`(`center_point`),
  CONSTRAINT `ImageAnnotation_ibfk_1` FOREIGN KEY (`image_id`) REFERENCES `ProjectImage` (`image_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ImageAnnotation_ibfk_3` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ImageAnnotation_ibfk_4` FOREIGN KEY (`generated_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ImageAnnotation2D
-- ----------------------------
DROP TABLE IF EXISTS `ImageAnnotation2D`;
CREATE TABLE `ImageAnnotation2D`  (
  `annotation_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `image_id` bigint(20) NOT NULL,
  `project_id` bigint(20) NOT NULL,
  `annotation_polygon` geometry NOT NULL,
  `created_time` bigint(20) NOT NULL,
  `center_point` point NOT NULL,
  `annotation_type` tinyint(4) NOT NULL,
  `usage_type` tinyint(4) NULL DEFAULT 1,
  `generated_by` bigint(20) NOT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`annotation_id`) USING BTREE,
  INDEX `image_id`(`image_id`) USING BTREE,
  INDEX `generated_by`(`generated_by`) USING BTREE,
  SPATIAL INDEX `sidx_annotation_polygon`(`annotation_polygon`),
  SPATIAL INDEX `sidx_center_point`(`center_point`),
  CONSTRAINT `ImageAnnotation2D_ibfk_1` FOREIGN KEY (`image_id`) REFERENCES `ProjectImage` (`image_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ImageAnnotation2D_ibfk_2` FOREIGN KEY (`generated_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 101685 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ImageFacet
-- ----------------------------
DROP TABLE IF EXISTS `ImageFacet`;
CREATE TABLE `ImageFacet`  (
  `facet_id` int(11) NOT NULL,
  `image_id` bigint(20) NOT NULL,
  `project_id` bigint(20) NOT NULL,
  `path_2d_full` geometry NOT NULL,
  `path_2d_seen` geometry NOT NULL,
  `path_2d_crsp_overview` geometry NOT NULL,
  `projected_angel` float NULL DEFAULT NULL,
  `abs_area_per_pixel` float NULL DEFAULT NULL,
  `center_point` point NOT NULL,
  `relevance_score` float NOT NULL,
  PRIMARY KEY (`facet_id`, `image_id`, `project_id`) USING BTREE,
  INDEX `image_id`(`image_id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  SPATIAL INDEX `sidx_path_2d_full`(`path_2d_full`),
  SPATIAL INDEX `sidx_path_2d_seen`(`path_2d_seen`),
  SPATIAL INDEX `sidx_path_2d_crsp_overview`(`path_2d_crsp_overview`),
  CONSTRAINT `ImageFacet_ibfk_1` FOREIGN KEY (`image_id`) REFERENCES `ProjectImage` (`image_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ImageFacet_ibfk_2` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Member
-- ----------------------------
DROP TABLE IF EXISTS `Member`;
CREATE TABLE `Member`  (
  `project_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `role` int(11) NOT NULL,
  `created_time` bigint(20) NOT NULL,
  `created_by` bigint(20) NOT NULL,
  `description` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`project_id`, `user_id`, `role`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `created_by`(`created_by`) USING BTREE,
  CONSTRAINT `Member_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Member_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Member_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Notification
-- ----------------------------
DROP TABLE IF EXISTS `Notification`;
CREATE TABLE `Notification`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NULL DEFAULT NULL,
  `created_time` bigint(20) NOT NULL,
  `project_status` int(11) NULL DEFAULT NULL,
  `recipient` bigint(20) NULL DEFAULT NULL,
  `content` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `recipient`(`recipient`) USING BTREE,
  CONSTRAINT `Notification_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Notification_ibfk_2` FOREIGN KEY (`recipient`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 44572 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for OnSiteReportImageElement
-- ----------------------------
DROP TABLE IF EXISTS `OnSiteReportImageElement`;
CREATE TABLE `OnSiteReportImageElement`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `component_id` tinyint(4) NULL DEFAULT NULL,
  `element_type` int(11) NOT NULL,
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `image_id` bigint(20) NOT NULL,
  `parent_id` bigint(20) NULL DEFAULT NULL,
  `report_type` tinyint(4) NOT NULL,
  `annotation_id` bigint(20) NULL DEFAULT NULL,
  `annotation3d_id` bigint(20) NULL DEFAULT NULL,
  `source_type` tinyint(4) NULL DEFAULT 0 COMMENT '1 bees360 go app image, 0 drone image',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `image_id`(`image_id`) USING BTREE,
  INDEX `OnSiteReportImageElement_ibfk_3`(`parent_id`) USING BTREE,
  INDEX `OnSiteReportImageElement_ibfk_5`(`annotation_id`) USING BTREE,
  CONSTRAINT `OnSiteReportImageElement_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `OnSiteReportImageElement_ibfk_2` FOREIGN KEY (`image_id`) REFERENCES `ProjectImage` (`image_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `OnSiteReportImageElement_ibfk_3` FOREIGN KEY (`parent_id`) REFERENCES `OnSiteReportImageElement` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `OnSiteReportImageElement_ibfk_5` FOREIGN KEY (`annotation_id`) REFERENCES `ImageAnnotation2D` (`annotation_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 249971 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for PartnerProgram
-- ----------------------------
DROP TABLE IF EXISTS `PartnerProgram`;
CREATE TABLE `PartnerProgram`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `inspection_service` tinyint(4) NULL DEFAULT NULL,
  `highfly_service` tinyint(4) NULL DEFAULT NULL,
  `first_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `last_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `phone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `company` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Permission
-- ----------------------------
DROP TABLE IF EXISTS `Permission`;
CREATE TABLE `Permission`  (
  `permission_id` int(11) NOT NULL,
  `parent_id` int(11) NULL DEFAULT NULL,
  `operation_id` int(11) NULL DEFAULT NULL,
  `permission_type` tinyint(4) NOT NULL,
  `permission_number` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `display` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`permission_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Project
-- ----------------------------
DROP TABLE IF EXISTS `Project`;
CREATE TABLE `Project`  (
  `project_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `policy_number` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `claim_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `claim_rcv` decimal(20, 5) NULL DEFAULT NULL,
  `claim_acv` decimal(20, 5) NULL DEFAULT NULL,
  `claim_type` int(11) NULL DEFAULT NULL,
  `created_time` bigint(20) NOT NULL,
  `damage_event_time` bigint(20) NOT NULL,
  `created_by` bigint(20) NOT NULL,
  `project_type` int(11) NOT NULL,
  `address` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `city` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `state` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `zip_code` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `gps_location` point NOT NULL,
  `asset_owner_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `asset_owner_phone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `asset_owner_email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `insurance_company` bigint(20) NULL DEFAULT NULL,
  `repair_company` bigint(20) NULL DEFAULT NULL,
  `material_provider_company` bigint(20) NULL DEFAULT NULL,
  `company_id` bigint(20) NULL DEFAULT NULL,
  `num_stories` int(5) NULL DEFAULT NULL,
  `edge_statistics` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `shingle_age` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `shingle_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `latest_status` int(11) NULL DEFAULT NULL,
  `description` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `claim_note` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `north` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `inspection_types` bigint(20) NULL DEFAULT NULL,
  `damage_severity` int(11) NULL DEFAULT 1001510,
  `inspection_time` bigint(20) NULL DEFAULT NULL,
  `is_booking` tinyint(1) NOT NULL,
  `contacter_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `contacter_email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `contacter_phone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `roof_estimated_area_item` tinyint(4) NULL DEFAULT NULL,
  `report_service_option` tinyint(4) NULL DEFAULT NULL,
  `need_pilot` tinyint(1) NOT NULL,
  `chimney` int(5) NULL DEFAULT 0,
  PRIMARY KEY (`project_id`) USING BTREE,
  INDEX `created_by`(`created_by`) USING BTREE,
  INDEX `insurance_company`(`insurance_company`) USING BTREE,
  INDEX `repair_company`(`repair_company`) USING BTREE,
  INDEX `material_provider_company`(`material_provider_company`) USING BTREE,
  INDEX `idx_zip_code`(`zip_code`) USING BTREE,
  INDEX `INDEX_PROJECT_COMPANY_ID`(`company_id`) USING BTREE,
  FULLTEXT INDEX `ftidx_location`(`address`, `city`, `state`, `country`, `zip_code`),
  CONSTRAINT `Project_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Project_ibfk_2` FOREIGN KEY (`insurance_company`) REFERENCES `Company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Project_ibfk_3` FOREIGN KEY (`repair_company`) REFERENCES `Company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Project_ibfk_4` FOREIGN KEY (`material_provider_company`) REFERENCES `Company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 22003 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ProjectCustomizedInfo
-- ----------------------------
DROP TABLE IF EXISTS `ProjectCustomizedInfo`;
CREATE TABLE `ProjectCustomizedInfo`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `column_code` int(8) NOT NULL,
  `column_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ProjectCustomizedInfo_ibfk_1`(`project_id`) USING BTREE,
  CONSTRAINT `ProjectCustomizedInfo_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 631 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ProjectEmail
-- ----------------------------
DROP TABLE IF EXISTS `ProjectEmail`;
CREATE TABLE `ProjectEmail`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email_event_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `email_title` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `email_content` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `target_role` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ProjectFacet
-- ----------------------------
DROP TABLE IF EXISTS `ProjectFacet`;
CREATE TABLE `ProjectFacet`  (
  `facet_id` int(11) NOT NULL,
  `project_id` bigint(20) NOT NULL,
  `component_id` int(11) NOT NULL,
  `name` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created_by` bigint(20) NOT NULL,
  `created_time` bigint(20) NOT NULL,
  `area` float NULL DEFAULT NULL,
  `area_unit` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `pitch` float NULL DEFAULT NULL,
  `3d_path` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `path_unit` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `path_type` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `shared_facet` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `plane_prop` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `plane_coef` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `is_high_roof` tinyint(1) NULL DEFAULT 0,
  `orientation` tinyint(4) NULL DEFAULT NULL,
  `damage_percent` tinyint(7) NULL DEFAULT NULL,
  PRIMARY KEY (`project_id`, `facet_id`) USING BTREE,
  INDEX `created_by`(`created_by`) USING BTREE,
  CONSTRAINT `ProjectFacet_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ProjectFacet_ibfk_2` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ProjectImage
-- ----------------------------
DROP TABLE IF EXISTS `ProjectImage`;
CREATE TABLE `ProjectImage`  (
  `image_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `file_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `file_name_lower_resolution` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `file_name_middle_resolution` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `file_size` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `upload_time` bigint(20) NOT NULL,
  `original_file_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `file_source_type` int(11) NOT NULL,
  `gps_location` point NOT NULL,
  `relative_altitude` double NULL DEFAULT NULL,
  `image_height` int(11) NOT NULL,
  `image_width` int(11) NOT NULL,
  `project_id` bigint(20) NOT NULL,
  `direction` int(11) NULL DEFAULT NULL,
  `orientation` tinyint(4) NULL DEFAULT NULL,
  `image_type` int(11) NULL DEFAULT NULL,
  `image_category` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `cam_property_matrix` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  `manually_annotated` tinyint(1) NOT NULL DEFAULT 0,
  `parent_id` bigint(20) NULL DEFAULT NULL,
  `annotation_image` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`image_id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `idx_upload_time`(`upload_time`) USING BTREE,
  INDEX `idx_original_file_name`(`original_file_name`) USING BTREE,
  SPATIAL INDEX `sidx_gps_location`(`gps_location`),
  CONSTRAINT `ProjectImage_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ProjectImage_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 271714 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ProjectImageReportElement
-- ----------------------------
DROP TABLE IF EXISTS `ProjectImageReportElement`;
CREATE TABLE `ProjectImageReportElement`  (
  `project_id` bigint(20) NOT NULL,
  `inspection_category` int(11) NOT NULL,
  `relative_position_type` int(11) NOT NULL,
  `object_description_json` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `added_by` bigint(20) NOT NULL,
  `created_time` bigint(20) NOT NULL,
  `component_id` tinyint(4) NOT NULL,
  `component_type` tinyint(4) NULL DEFAULT NULL,
  PRIMARY KEY (`project_id`, `component_id`, `inspection_category`, `relative_position_type`) USING BTREE,
  INDEX `added_by`(`added_by`) USING BTREE,
  CONSTRAINT `ProjectImageReportElement_ibfk_2` FOREIGN KEY (`added_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ProjectImageReportElement_ibfk_3` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ProjectReportFile
-- ----------------------------
DROP TABLE IF EXISTS `ProjectReportFile`;
CREATE TABLE `ProjectReportFile`  (
  `report_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `report_type` int(11) NOT NULL,
  `report_word_file_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `report_pdf_file_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `size` int(11) NOT NULL,
  `created_by` bigint(20) NOT NULL,
  `created_time` bigint(20) NOT NULL,
  `generation_status` tinyint(4) NOT NULL DEFAULT 1,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`report_id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `created_by`(`created_by`) USING BTREE,
  CONSTRAINT `ProjectReportFile_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ProjectReportFile_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 16905 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Region
-- ----------------------------
DROP TABLE IF EXISTS `Region`;
CREATE TABLE `Region`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `region_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `country_id` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_country_id`(`country_id`) USING BTREE,
  CONSTRAINT `Region_ibfk_1` FOREIGN KEY (`country_id`) REFERENCES `Country` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 10060 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ReportTask
-- ----------------------------
DROP TABLE IF EXISTS `ReportTask`;
CREATE TABLE `ReportTask`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `project_id` bigint(20) NOT NULL,
  `report_type` tinyint(4) NULL DEFAULT NULL,
  `is_deleted` tinyint(1) NULL DEFAULT NULL,
  `created_time` bigint(20) NULL DEFAULT NULL,
  `updated_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  CONSTRAINT `ReportTask_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ReportTask_ibfk_2` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 167 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for RolePermission
-- ----------------------------
DROP TABLE IF EXISTS `RolePermission`;
CREATE TABLE `RolePermission`  (
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`role_id`, `permission_id`) USING BTREE,
  INDEX `permission_id`(`permission_id`) USING BTREE,
  CONSTRAINT `RolePermission_ibfk_1` FOREIGN KEY (`permission_id`) REFERENCES `Permission` (`permission_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Roster
-- ----------------------------
DROP TABLE IF EXISTS `Roster`;
CREATE TABLE `Roster`  (
  `roster_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `last_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `state` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `country` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `gps_location` point NULL,
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `additional_operating_territories` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `operating_city_state` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `designated_home_state_license` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `additional_license` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `years_of_experience` tinyint(3) NOT NULL,
  `travel_radius` int(11) NOT NULL,
  `more_than_100miles_traveled` tinyint(1) NULL DEFAULT NULL,
  `cat_event_deployed` tinyint(1) NULL DEFAULT NULL,
  `message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `resume_url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `created_time` bigint(20) NULL DEFAULT NULL,
  `updated_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`roster_id`) USING BTREE,
  INDEX `IDX_USER_NAME`(`first_name`) USING BTREE,
  INDEX `IDX_PHONE`(`phone`) USING BTREE,
  INDEX `IDX_EMAIL`(`email`) USING BTREE,
  INDEX `IDX_OPERATING_CITY_STATE`(`operating_city_state`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ServicePrice
-- ----------------------------
DROP TABLE IF EXISTS `ServicePrice`;
CREATE TABLE `ServicePrice`  (
  `service_id` int(11) NOT NULL AUTO_INCREMENT,
  `service_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `service_price` float NULL DEFAULT NULL,
  `currency` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`service_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for SystemValue
-- ----------------------------
DROP TABLE IF EXISTS `SystemValue`;
CREATE TABLE `SystemValue`  (
  `service_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `country` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `service_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `service_value` decimal(20, 2) NULL DEFAULT NULL,
  `label` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`service_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for User
-- ----------------------------
DROP TABLE IF EXISTS `User`;
CREATE TABLE `User`  (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `last_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `qr_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `phone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `address` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `city` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `state` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `zip_code` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `avatar` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `company_id` bigint(20) NULL DEFAULT NULL,
  `employee_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `password` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `last_login_time` bigint(20) NULL DEFAULT NULL,
  `registration_time` bigint(20) NOT NULL,
  `active_status` int(11) NOT NULL,
  `gps_location` point NOT NULL,
  `most_recent_gps_location` point NOT NULL,
  `roles` bigint(20) NOT NULL DEFAULT 0,
  `role_application_status` bigint(20) NOT NULL DEFAULT 0,
  `certificate_list` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `discount_percent` decimal(5, 3) NULL DEFAULT 0.000,
  `free_on_trail_project_num` int(3) NULL DEFAULT 0,
  `new_customer_discount_percent` decimal(5, 3) NULL DEFAULT 0.000,
  `new_customer_discount_project_num` int(3) NULL DEFAULT 0,
  `wallet_balance` decimal(20, 3) NULL DEFAULT 0.000,
  `commission_balance` decimal(20, 3) NULL DEFAULT 0.000,
  `currency` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `inspection_service` tinyint(4) NULL DEFAULT NULL,
  `highfly_service` tinyint(4) NULL DEFAULT NULL,
  `order_service_time` bigint(20) NULL DEFAULT 0,
  `travel_radius` double NULL DEFAULT 0,
  `source` tinyint(4) NULL DEFAULT 0,
  PRIMARY KEY (`user_id`) USING BTREE,
  INDEX `company_id`(`company_id`) USING BTREE,
  INDEX `idx_email`(`email`) USING BTREE,
  INDEX `idx_phone`(`phone`) USING BTREE,
  INDEX `idx_city`(`city`) USING BTREE,
  INDEX `idx_state`(`state`) USING BTREE,
  INDEX `idx_roles`(`roles`) USING BTREE,
  SPATIAL INDEX `sidx_gps_location`(`gps_location`),
  SPATIAL INDEX `sidx_most_recent_gps_location`(`most_recent_gps_location`),
  CONSTRAINT `User_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `Company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 10653 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for UserPayment
-- ----------------------------
DROP TABLE IF EXISTS `UserPayment`;
CREATE TABLE `UserPayment`  (
  `payment_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `project_id` bigint(20) NULL DEFAULT 0,
  `total_fee_amount` decimal(20, 3) NULL DEFAULT 0.000,
  `paid_service_fee_amount` decimal(20, 3) NULL DEFAULT NULL,
  `tax` decimal(20, 3) NOT NULL DEFAULT 0.000,
  `payment_method` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `card_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `first_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `last_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `card_num` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `authorization_code` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `expiration_date` int(11) NULL DEFAULT NULL,
  `routing_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `account_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `currency` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `service_fee_type` tinyint(3) NULL DEFAULT NULL,
  `paid_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`payment_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1001431 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for UserReadReport
-- ----------------------------
DROP TABLE IF EXISTS `UserReadReport`;
CREATE TABLE `UserReadReport`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `project_id` bigint(20) NOT NULL,
  `report_id` bigint(20) NOT NULL,
  `report_type` int(11) NOT NULL,
  `is_deleted` tinyint(1) NOT NULL,
  `created_time` bigint(20) NOT NULL,
  `updated_time` bigint(20) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `report_id`(`report_id`) USING BTREE,
  CONSTRAINT `UserReadReport_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `UserReadReport_ibfk_2` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `UserReadReport_ibfk_3` FOREIGN KEY (`report_id`) REFERENCES `ProjectReportFile` (`report_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7249 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
