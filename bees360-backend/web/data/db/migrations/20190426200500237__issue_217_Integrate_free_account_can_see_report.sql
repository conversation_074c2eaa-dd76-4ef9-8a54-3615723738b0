-- migrate:up

/**
 * these tables is related to report generating :
 *
select * from Project where project_id = 20530;
select * from ProjectImage where project_id = 20530 and is_deleted = 0;
select * from ProjectFacet where project_id = 20530;
select * from ImageFacet where project_id = 20530;

select C.* from Company C  left join Project P on C.company_id = P.repair_company  where P.project_id = 20530;

select * from ImageAnnotation where project_id = 20530;
select * from ImageAnnotation2D where project_id = 20530;
select * from ProjectReportFile where project_id = 20530 and is_deleted = 0;

select * from ProjectCustomizedInfo where project_id = 20530;
select * from OnSiteReportImageElement where project_id = 20530;
select * from HouseSegmentValue  where project_id = 20530;
*/

-- Company
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email, is_deleted, created_time, updated_time) VALUES (1766, 'Pilot', 4, 'https://www.pilotcat.com/', NULL, NULL, NULL, NULL, 0, 1554718796921, 1554718796921);
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email, is_deleted, created_time, updated_time) VALUES (1260, 'Sutton Inspection Bureau', 1, 'https://www.sibfla.com', 'https://bees360.s3.amazonaws.com/company/1260/logo.jpg', 'tiana', '************', '<EMAIL>', 0, 1554718796921, 1554718796921);

-- User
INSERT INTO User(user_id, first_name, last_name, email, qr_code, phone, address, city, state, country, zip_code, avatar, company_id, employee_id, password, last_login_time, registration_time, active_status, gps_location, most_recent_gps_location, roles, role_application_status, certificate_list, discount_percent, free_on_trail_project_num, new_customer_discount_percent, new_customer_discount_project_num, wallet_balance, commission_balance, currency, inspection_service, highfly_service, order_service_time, travel_radius, source) VALUES (10091, 'Huiling', '', '<EMAIL>', NULL, '', '', '', '', '', '', 'https://bees360.s3.amazonaws.com/user/10091/avatar/1531113460688.png', 1260, '', '2664C6241F32DAC67EAB0C309C4BA11F', 1555725399170, 1520476419365, 1, ST_GeomFromText('POINT(0 0)'), ST_GeomFromText('POINT(0 0)'), 4611686018427388161, 0, NULL, 0.000, 0, 0.000, 0, 0.000, NULL, NULL, NULL, NULL, 0, 0, 0);
INSERT INTO User(user_id, first_name, last_name, email, qr_code, phone, address, city, state, country, zip_code, avatar, company_id, employee_id, password, last_login_time, registration_time, active_status, gps_location, most_recent_gps_location, roles, role_application_status, certificate_list, discount_percent, free_on_trail_project_num, new_customer_discount_percent, new_customer_discount_project_num, wallet_balance, commission_balance, currency, inspection_service, highfly_service, order_service_time, travel_radius, source) VALUES (10361, 'Pilot', '-Bees360', '<EMAIL>', NULL, '', '556 West Dallas Street', 'Houston', 'TX', 'US', '77002', 'https://s3.amazonaws.com/bees360/users/default/avatar.jpg', 1766, NULL, '7E908C9E5F29147AA9708DBB5BE9090A', 1555463286715, 1541495981408, 1, ST_GeomFromText('POINT(-95.3739004 29.7577847)'), ST_GeomFromText('POINT(0 0)'), 5, 0, NULL, 0.000, 0, 0.500, 10, 0.000, 0.000, NULL, NULL, NULL, 0, 30, 0);
INSERT INTO User(user_id, first_name, last_name, email, qr_code, phone, address, city, state, country, zip_code, avatar, company_id, employee_id, password, last_login_time, registration_time, active_status, gps_location, most_recent_gps_location, roles, role_application_status, certificate_list, discount_percent, free_on_trail_project_num, new_customer_discount_percent, new_customer_discount_project_num, wallet_balance, commission_balance, currency, inspection_service, highfly_service, order_service_time, travel_radius, source) VALUES (10362, 'Reviewer', '-Bees360', '<EMAIL>', NULL, '', '534 East 4th Street', 'Lancaster', 'TX', 'US', '75146', 'https://s3.amazonaws.com/bees360/users/default/avatar.jpg', 1062, NULL, '7E908C9E5F29147AA9708DBB5BE9090A', 1553250718497, 1541496279790, 1, ST_GeomFromText('POINT(-96.7528598 32.5952511)'), ST_GeomFromText('POINT(0 0)'), 65, 0, NULL, 0.000, 0, 0.500, 10, 0.000, 0.000, NULL, NULL, NULL, 0, 0, 0);

-- Project
INSERT INTO Project(project_id, policy_number, claim_number, claim_rcv, claim_acv, claim_type, created_time, damage_event_time, created_by, project_type, address, city, state, country, zip_code, gps_location, asset_owner_name, asset_owner_phone, asset_owner_email, insurance_company, repair_company, material_provider_company, company_id, num_stories, edge_statistics, shingle_age, shingle_type, latest_status, description, claim_note, north, inspection_types, damage_severity, inspection_time, is_booking, contacter_name, contacter_email, contacter_phone, roof_estimated_area_item, report_service_option, need_pilot, chimney) VALUES (20530, NULL, NULL, NULL, NULL, 2, 1541496420256, 1541347200000, 10361, 0, '5443 U.S. Highway 1', 'Fort Pierce', 'Florida', '美国', '34946', Point(-80.3629909, 27.53259200000001), 'cv', '18877571531', '<EMAIL>', NULL, NULL, NULL, 1766, NULL, NULL, NULL, NULL, 5, NULL, NULL, '[0.*********,0.99999213]', 2, 1001510, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, 1);

-- Member
INSERT INTO Member(project_id, user_id, role, created_time, created_by, description, is_deleted) VALUES (20530, 10361, -1, 1541496420256, 10361, '', 0);
INSERT INTO Member(project_id, user_id, role, created_time, created_by, description, is_deleted) VALUES (20530, 10362, 6, 1541496855157, 10091, '', 0);

-- ProjectReportFile
INSERT INTO ProjectReportFile(project_id, report_type, report_word_file_name, report_pdf_file_name, size, created_by, created_time, generation_status, is_read, is_deleted) VALUES (20530, 14, '', 'https://bees360.s3.amazonaws.com/project/20530/measurement/1543571218314QuickSquareReport.pdf', 141444, 10361, 1543571218314, 1, 0, 0);
INSERT INTO ProjectReportFile(project_id, report_type, report_word_file_name, report_pdf_file_name, size, created_by, created_time, generation_status, is_read, is_deleted) VALUES (20530, 13, '', 'https://bees360.s3.amazonaws.com/project/20530/measurement/1543571221409BidReport.pdf', 282333, 10361, 1543571221409, 1, 0, 0);
INSERT INTO ProjectReportFile(project_id, report_type, report_word_file_name, report_pdf_file_name, size, created_by, created_time, generation_status, is_read, is_deleted) VALUES (20530, 9, '', 'https://bees360.s3.amazonaws.com/project/20530/measurement/1550658976788SymbilityScanMeasurements.xml', 2134, 10362, 1550658976788, 1, 0, 0);
INSERT INTO ProjectReportFile(project_id, report_type, report_word_file_name, report_pdf_file_name, size, created_by, created_time, generation_status, is_read, is_deleted) VALUES (20530, 2, '', 'https://bees360.s3.amazonaws.com/project/20530/measurement/1550658976788MeasurementReport.pdf', 699102, 10362, 1550658976788, 3, 0, 0);
INSERT INTO ProjectReportFile(project_id, report_type, report_word_file_name, report_pdf_file_name, size, created_by, created_time, generation_status, is_read, is_deleted) VALUES (20530, 1, '', 'https://bees360.s3.amazonaws.com/project/20530/measurement/1550658976788DamageReport.pdf', 1220483, 10362, 1550658976788, 3, 0, 0);

--
