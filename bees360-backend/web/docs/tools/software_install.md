<!-- TOC -->

- [1. 基本配置](#1-%E5%9F%BA%E6%9C%AC%E9%85%8D%E7%BD%AE)
    - [1.1. 你需要一个公司的企业邮箱](#11-%E4%BD%A0%E9%9C%80%E8%A6%81%E4%B8%80%E4%B8%AA%E5%85%AC%E5%8F%B8%E7%9A%84%E4%BC%81%E4%B8%9A%E9%82%AE%E7%AE%B1)
    - [1.2. Github](#12-github)
        - [1.2.1. 申请及安装](#121-%E7%94%B3%E8%AF%B7%E5%8F%8A%E5%AE%89%E8%A3%85)
- [2. 后端开发](#2-%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91)
    - [2.1. JAVA JDK](#21-java-jdk)
    - [2.2. TOMCAT](#22-tomcat)
        - [2.2.1. 安装tomcat](#221-%E5%AE%89%E8%A3%85tomcat)
        - [2.2.2. 设置默认访问主页为指定项目](#222-%E8%AE%BE%E7%BD%AE%E9%BB%98%E8%AE%A4%E8%AE%BF%E9%97%AE%E4%B8%BB%E9%A1%B5%E4%B8%BA%E6%8C%87%E5%AE%9A%E9%A1%B9%E7%9B%AE)
        - [2.2.3. 配置路由表转换 (非必需的)](#223-%E9%85%8D%E7%BD%AE%E8%B7%AF%E7%94%B1%E8%A1%A8%E8%BD%AC%E6%8D%A2-%E9%9D%9E%E5%BF%85%E9%9C%80%E7%9A%84)
        - [2.2.4. 实例安全组开放http 80端口](#224-%E5%AE%9E%E4%BE%8B%E5%AE%89%E5%85%A8%E7%BB%84%E5%BC%80%E6%94%BEhttp-80%E7%AB%AF%E5%8F%A3)
    - [2.3. 安装maven](#23-%E5%AE%89%E8%A3%85maven)
    - [2.4. Amazon](#24-amazon)
        - [2.4.1. 连接到亚马逊EC2](#241-%E8%BF%9E%E6%8E%A5%E5%88%B0%E4%BA%9A%E9%A9%AC%E9%80%8Aec2)
        - [2.4.2. 用 FileZilla(免费开源的FTP软件) 连接AWS主机](#242-%E7%94%A8-filezilla%E5%85%8D%E8%B4%B9%E5%BC%80%E6%BA%90%E7%9A%84ftp%E8%BD%AF%E4%BB%B6-%E8%BF%9E%E6%8E%A5aws%E4%B8%BB%E6%9C%BA)
    - [2.5. 接口测试](#25-%E6%8E%A5%E5%8F%A3%E6%B5%8B%E8%AF%95)
        - [2.5.1. Postman](#251-postman)
            - [2.5.1.1. 概要](#2511-%E6%A6%82%E8%A6%81)
            - [2.5.1.2. 使用](#2512-%E4%BD%BF%E7%94%A8)
- [3. 前端开发](#3-%E5%89%8D%E7%AB%AF%E5%BC%80%E5%8F%91)
    - [3.1. 安装node.js以及npm](#31-%E5%AE%89%E8%A3%85nodejs%E4%BB%A5%E5%8F%8Anpm)
        - [3.1.1. 其他安装](#311-%E5%85%B6%E4%BB%96%E5%AE%89%E8%A3%85)
        - [3.1.2. 运行项目](#312-%E8%BF%90%E8%A1%8C%E9%A1%B9%E7%9B%AE)
    - [3.2. 安装cnpm](#32-%E5%AE%89%E8%A3%85cnpm)
    - [3.3. vue中引入jQuery和bootstrap](#33-vue%E4%B8%AD%E5%BC%95%E5%85%A5jquery%E5%92%8Cbootstrap)
        - [3.3.1. 引入jQuery：](#331-%E5%BC%95%E5%85%A5jquery%EF%BC%9A)
        - [3.3.2. 引入 bootstrap.css文件](#332-%E5%BC%95%E5%85%A5-bootstrapcss%E6%96%87%E4%BB%B6)
    - [3.4. 安装sublime text3](#34-%E5%AE%89%E8%A3%85sublime-text3)
- [4. AI Process](#4-ai-process)
    - [4.1. matlab installation](#41-matlab-installation)
    - [4.2. Statistical library](#42-statistical-library)
    - [4.3. hadoop mapreduce call matlab](#43-hadoop-mapreduce-call-matlab)
        - [4.3.1. install hadoop cluster](#431-install-hadoop-cluster)
        - [4.3.2. install matlab server](#432-install-matlab-server)
        - [4.3.3. write matlab function and mapreduce project and run mapreduce job](#433-write-matlab-function-and-mapreduce-project-and-run-mapreduce-job)
    - [4.4. NVIDIA GeForce GTX 1080](#44-nvidia-geforce-gtx-1080)
        - [4.4.1. 查询NVIDIA驱动](#441-%E6%9F%A5%E8%AF%A2nvidia%E9%A9%B1%E5%8A%A8)
        - [4.4.2. 安装驱动](#442-%E5%AE%89%E8%A3%85%E9%A9%B1%E5%8A%A8)
        - [4.4.3. 安装cuda8.0](#443-%E5%AE%89%E8%A3%85cuda80)
    - [4.5. OPENGL](#45-opengl)
        - [4.5.1. 安装 OPENGL](#451-%E5%AE%89%E8%A3%85-opengl)
        - [4.5.2. 测试](#452-%E6%B5%8B%E8%AF%95)
- [5. 其他](#5-%E5%85%B6%E4%BB%96)
    - [5.1. FTP服务器](#51-ftp%E6%9C%8D%E5%8A%A1%E5%99%A8)
        - [5.1.1. 目标](#511-%E7%9B%AE%E6%A0%87)
        - [5.1.2. 创建专门用于ftp的用户](#512-%E5%88%9B%E5%BB%BA%E4%B8%93%E9%97%A8%E7%94%A8%E4%BA%8Eftp%E7%9A%84%E7%94%A8%E6%88%B7)
        - [5.1.3. 安装vsftpd](#513-%E5%AE%89%E8%A3%85vsftpd)
        - [5.1.4. 启动/重启/停止 vsftpd](#514-%E5%90%AF%E5%8A%A8%E9%87%8D%E5%90%AF%E5%81%9C%E6%AD%A2-vsftpd)
        - [5.1.5. 访问ftp](#515-%E8%AE%BF%E9%97%AEftp)
            - [*******. 用浏览器访问](#5151-%E7%94%A8%E6%B5%8F%E8%A7%88%E5%99%A8%E8%AE%BF%E9%97%AE)
            - [*******. 用终端访问](#5152-%E7%94%A8%E7%BB%88%E7%AB%AF%E8%AE%BF%E9%97%AE)
        - [5.1.6. ec2 上ftp服务器安装补充](#516-ec2-%E4%B8%8Aftp%E6%9C%8D%E5%8A%A1%E5%99%A8%E5%AE%89%E8%A3%85%E8%A1%A5%E5%85%85)
            - [*******. vsfptd.conf文件配置补充](#5161-vsfptdconf%E6%96%87%E4%BB%B6%E9%85%8D%E7%BD%AE%E8%A1%A5%E5%85%85)
            - [*******. ec2实例安全组配置](#5162-ec2%E5%AE%9E%E4%BE%8B%E5%AE%89%E5%85%A8%E7%BB%84%E9%85%8D%E7%BD%AE)
            - [*******. 注意事项](#5163-%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9)
        - [5.1.7. 搭建不同用户拥有不同文件夹的ftp服务器 @zhenqi.li](#517-%E6%90%AD%E5%BB%BA%E4%B8%8D%E5%90%8C%E7%94%A8%E6%88%B7%E6%8B%A5%E6%9C%89%E4%B8%8D%E5%90%8C%E6%96%87%E4%BB%B6%E5%A4%B9%E7%9A%84ftp%E6%9C%8D%E5%8A%A1%E5%99%A8-zhenqili)
            - [*******. 安装vsftpd](#5171-%E5%AE%89%E8%A3%85vsftpd)
            - [*******. 安装db4.x-utils](#5172-%E5%AE%89%E8%A3%85db4x-utils)
            - [*******. 建立本地虚拟用户](#5173-%E5%BB%BA%E7%AB%8B%E6%9C%AC%E5%9C%B0%E8%99%9A%E6%8B%9F%E7%94%A8%E6%88%B7)
            - [*******. 建立虚拟用户数据库](#5174-%E5%BB%BA%E7%AB%8B%E8%99%9A%E6%8B%9F%E7%94%A8%E6%88%B7%E6%95%B0%E6%8D%AE%E5%BA%93)
            - [5.1.7.5. 建立基于vsftpd_login的PAM授权文件](#5175-%E5%BB%BA%E7%AB%8B%E5%9F%BA%E4%BA%8Evsftpdlogin%E7%9A%84pam%E6%8E%88%E6%9D%83%E6%96%87%E4%BB%B6)
            - [5.1.7.6. 设置用户权限](#5176-%E8%AE%BE%E7%BD%AE%E7%94%A8%E6%88%B7%E6%9D%83%E9%99%90)
            - [*******. 设置vsftpd.conf](#5177-%E8%AE%BE%E7%BD%AEvsftpdconf)
            - [*******. 创建对应的目录](#5178-%E5%88%9B%E5%BB%BA%E5%AF%B9%E5%BA%94%E7%9A%84%E7%9B%AE%E5%BD%95)
            - [*******. 重新启动vsftpd](#5179-%E9%87%8D%E6%96%B0%E5%90%AF%E5%8A%A8vsftpd)
            - [*******0. 日常维护](#51710-%E6%97%A5%E5%B8%B8%E7%BB%B4%E6%8A%A4)
            - [*******1. 注意事项](#51711-%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9)
- [6. teamviewer使用 @LiZhenqi](#6-teamviewer%E4%BD%BF%E7%94%A8-lizhenqi)
    - [6.1. 安装](#61-%E5%AE%89%E8%A3%85)
        - [6.1.1. Windows安装](#611-windows%E5%AE%89%E8%A3%85)
        - [6.1.2. 设置固定密码及如何使用](#612-%E8%AE%BE%E7%BD%AE%E5%9B%BA%E5%AE%9A%E5%AF%86%E7%A0%81%E5%8F%8A%E5%A6%82%E4%BD%95%E4%BD%BF%E7%94%A8)

<!-- /TOC -->
# 1. 基本配置
## 1.1. 你需要一个公司的企业邮箱
找公司IT人员生成公司邮箱账号 [名称拼音].[姓氏]@bees360.com

## 1.2. Github
### 1.2.1. 申请及安装
1. 使用公司的企业邮箱注册一个Github帐号
2. 安装git, 并设置SSH
参考: [Ubuntu下Git安装与使用](http://bohsu.blog.51cto.com/6966437/1230705)
参考: [github设置添加SSH](http://blog.csdn.net/binyao02123202/article/details/20130891)
```shell
cd ~/.ssh
ssh-keygen -t rsa -C "<<EMAIL>>"
# 一直 [ENTER]
# 拷贝 ~/.ssh/id_rsa.pub 到你的github账户
# 复制公钥
clip < ~/.ssh/id_rsa.pub
# 查看是否配置成功
ssh -T **************
```
3. 配置默认的远程仓库 (非必需的)
```
git remote set-<NAME_EMAIL>:bees360HR/bees360_website.git
```

# 2. 后端开发
## 2.1. JAVA JDK

1. 源码包准备：
首先到官网下载jdk，http://www.oracle.com/technetwork/java/javase/downloads/jdk8-downloads-2133151.html
2. 解压源码包
通过终端在/usr/local目录下新建java文件夹，命令行：
```
sudo mkdir /usr/local/java
```
然后将下载到压缩包拷贝到java文件夹中，命令行：
```
cd <jdk源码包所在目录>
cp jdk-8u25-linux-x64.tar.gz /usr/local/java
```
然后进入java目录，解压压缩包：
```
cd /usr/local/java
sudo tar xvf jdk-8u25-linux-x64.tar.gz
```
然后可以把压缩包删除：
```
sudo rm jdk-8u25-linux-x64.tar.gz
```
3. 设置jdk环境变量
这里采用全局设置方法，它是是所有用户的共用的环境变量
```
sudo vi ~/.bashrc
```
打开之后在末尾添加
```
export JAVA_HOME=/usr/local/java/jdk1.8.0_144
export JRE_HOME=${JAVA_HOME}/jre
export CLASSPATH=.:${JAVA_HOME}/lib:${JRE_HOME}/lib
export PATH=${JAVA_HOME}/bin:$PATH
```
4. 立即执行最新设置
```
cd
source ~/.bashrc
```
## 2.2. TOMCAT
### 2.2.1. 安装tomcat
详细安装过程参考: [ubuntu14.04 配置tomcat8](http://blog.csdn.net/xingjiarong/article/details/49386989)
```
JAVA_HOME=/usr/local/java/jdk1.8.0_144
JRE_HOME=${JAVA_HOME}/jre
CLASSPATH=.:${JAVA_HOME}/lib:${JRE_HOME}/lib
PATH=${JAVA_HOME}/bin:$PATH
TOMCAT_HOME=/usr/local/tomcat8
```
### 2.2.2. 设置默认访问主页为指定项目
在server.xml中找到
```xml
<Host name="localhost" appBase="webapps" unpackWARs="true" autoDeploy="true" xmlValidation="false" xmlNamespaceAware="false">
</Host>
```
在标签中间插入：
```xml
<Context path="" docBase="xbwl" debug="0" reloadable="true"/>
<!-- docBase="xbwl" xbwl即为指定的项目。 -->
```
完整如下：
```xml
<Host name="localhost" appBase="webapps" unpackWARs="true" autoDeploy="true" xmlValidation="false" xmlNamespaceAware="false">
    <Context path="" docBase="xbwl" debug="0" reloadable="true"/>
</Host>
```

### 2.2.3. 配置路由表转换 (非必需的)
参考: [Ubuntu14.04 配置 iptables 把80端口转到8080](http://blog.csdn.net/hanshileiai/article/details/47757217)

### 2.2.4. 实例安全组开放http 80端口

## 2.3. 安装maven
下载最新的Maven jar包，例如apache-maven-3.5.0-bin.tar.gz,并放入安装path，例如/usr/lib
```shell
cd <jdk源码包所在目录>
sudo cp apache-maven-3.5.3-bin.tar.gz /usr/lib
```
然后进入/usr/lin目录，解压压缩包：
```
cd /usr/lib
sudo tar xvf apache-maven-3.5.3-bin.tar.gz
```
然后可以把压缩包删除：
```
sudo rm apache-maven-3.5.3-bin.tar.gz
```
在 ~/.bashrc 的最低下添加如下内容：
```
export MAVEN_PATH=/usr/lib/apache-maven-3.5.3
PATH=$MAVEN_PATH/bin:$PATH
```

立刻加载修改后的设置
```shell
source ~/.bashrc
```
查看maven版本, 验证Maven的配置是否成功
```
mvn -v
```
## 2.4. Amazon
### 2.4.1. 连接到亚马逊EC2
1. 配置密钥文件
```shell
# 将密钥文件放到 ~/.ssh中
cp documents/amazon/amazon_key/Bees36020171006.pem ~/.ssh
```
2. 连接到EC2
```shell
ssh -i Bees36020171006.pem ubuntu@54.238.137.16
```
如果出现如下错误, 将 Bees36020171006.pem 设置为只读, 然后再尝试连接
```
@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
@         WARNING: UNPROTECTED PRIVATE KEY FILE!          @
@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
Permissions 0644 for 'Bees36020171006.pem' are too open.
It is required that your private key files are NOT accessible by others.
This private key will be ignored.
Load key "Bees36020171006.pem": bad permissions
Permission denied (publickey).
```
```shell
chmod 400 ~/.ssh/Bees36020171006.pem
ssh -i Bees36020171006.pem ubuntu@54.238.137.16
```

### 2.4.2. 用 FileZilla(免费开源的FTP软件) 连接AWS主机
了解: [官网](https://filezilla-project.org/) [百度百科](https://baike.baidu.com/item/FileZilla/10982464)
下载：[https://filezilla-project.org/](https://filezilla-project.org/download.php?type=client)
参考:
[用 FileZilla 连接 Amazon 的aws的主机](http://blog.csdn.net/michael1112/article/details/52537961)
[Ubuntu下FileZilla的安装[图文]](https://www.linuxidc.com/Linux/2011-03/33133.htm)
[用 FileZilla 连接 Amazon 的aws的主机](http://blog.csdn.net/michael1112/article/details/52537961)
[FileZilla 连接到 AWS](http://blog.csdn.net/maxu12345/article/details/45397701)
## 2.5. 接口测试
### 2.5.1. Postman
#### 2.5.1.1. 概要
推荐使用谷歌插件版本，因为该版本可以使用浏览器的Cookie, 方便测试登录之后才能操作的接口。
下载: [Postman](https://chrome.google.com/webstore/detail/postman/fhbjgbiflinjbdggehcddcbncdddomop?utm_source=chrome-app-launcher-info-dialog)
下载: [Postman Interceptor](https://chrome.google.com/webstore/detail/postman-interceptor/aicmkgpgakddgnaphhhpliifpcfhicfo)
#### 2.5.1.2. 使用
使用浏览器的Cookie, 打开谷歌插件版Postman，在菜单栏打开[Postman Interceptor]即可。

# 3. 前端开发
## 3.1. 安装node.js以及npm
安装node.js v8.1.2 - 如果你当前的版本过低，也可以执行如下指令进行升级
```shell
apt-get update -y
apt-get install -y build-essential curl
curl -sL https://deb.nodesource.com/setup_8.x | sudo -E bash -
apt-get install nodejs
```
参考: [Debian/Ubuntu快速安装Node.js最新v8.1.2版本方法](http://www.laozuo.org/10792.html)
如果是由于python版本升级为3.x.x导致安装失败，可以参考如下方法将python3切换回python2:
参考：[解决“ pre-removal脚本 返回错误状态1 的报错”](http://blog.csdn.net/ericcchen/article/details/72466065)
### 3.1.1. 其他安装
参考: [在ubuntu上安装最新稳定版本的node及npm](https://segmentfault.com/a/1190000007542620)
```shell
# 升级npm为最新版本
sudo npm install npm@latest -g

# 安装用于安装nodejs的模块n
sudo npm install -g n
# 然后通过n模块安装指定版本的nodejs，n模块更多介绍请参考官方文档
# 安装官方最新版本
sudo n latest
# 安装官方稳定版本
sudo n stable
# 安装官方最新LTS版本
sudo n lts
```

### 3.1.2. 运行项目
进入到项目根目录（bees360_website/client/)，执行如下指令即可运行项目。
```
npm install
npm run dev
```

## 3.2. 安装cnpm
```
$ npm install -g cnpm --registry=https://registry.npm.taobao.org
```
附: [淘宝 NPM 镜像](https://npm.taobao.org/)


## 3.3. vue中引入jQuery和bootstrap

### 3.3.1. 引入jQuery：
1. 在当前的Vue项目中运行如下指令, 安装 jquery 到项目中。
```shell
npm install jquery –-save-dev`
```
2. 修改webpack.base.conf.js两个地方：
	1) 加入var webpack = require(“webpack”)；
	2) 在module.exports的里面加入如下代码, 加到底部即可.
```js
plugins: [
	new webpack.ProvidePlugin({
		jQuery: “jquery”,
		$: “jquery”
	})
]
```

3. 在main.js中加入如下代码, 完成jQuery的引入。
```js
import $ from ‘jquery’
```

### 3.3.2. 引入 bootstrap.css文件
1. 在vue项目中运行如下代码, 安装bootstrap.
```shell
npm install bootstrap –-save-dev
```

2. 修改webpack.base.conf.js
```js
resolve: {
	extensions: [‘.js’, ‘.vue’, ‘.json’],
	alias: {
		‘vue$’: ‘vue/dist/vue.esm.js’,
		‘@’: resolve(‘src’),
		// 自己手动导入时需加下面这一句. 用 npm install bootstrap –-save 安装则不需要
		//‘bootstrap’:resolve(‘src/assets/bootstrap’),
	}
},
```

3. 在main.js中import
```js
import ‘bootstrap/js/bootstrap.min.js’
import ‘bootstrap/css/bootstrap.min.css’
```

## 3.4. 安装sublime text3
```shell
sudo add-apt-repository ppa:webupd8team/sublime-text-3
sudo apt-get update
sudo apt-get install sublime-text-installer
```

使用Ctrl+`快捷键或者通过View->Show Console菜单打开命令行，粘贴如下代码. 如果顺利的话，此时就可以在Preferences菜单下看到Package Settings和Package Control两个菜单了。
```
import  urllib.request,os;
pf='Package Control.sublime-package';
ipp=sublime.installed_packages_path();
urllib.request.install_opener(urllib.request.build_opener(urllib.request.ProxyHandler()));
open(os.path.join(ipp,pf),'wb').write(urllib.request.urlopen('http://sublime.wbond.net/'+pf.replace(' ','%20')).read())
```

附: 支持中文插件：[ConvertToUTF8](https://github.com/seanliang/ConvertToUTF8)

# 4. AI Process
## 4.1. matlab installation


1. download matlab-2017a from https://pan.baidu.com/s/1jHHEyj0  extract password: eem6
all zip files(about 10G) has downloaded and stored on <u>*************/f:/BaiduNetdiskDownload/2017a</u>
unzip password: <u>0daydown</u>.
You can use winscp transform the iso file to ubuntu system. In addition, we have a backup in <u>ftp://*************</u>. You can access it with name courier and password 7896321.


2. make an directory to store
```
mkdir ~/Matlab
```

3. Mount the R2017a_glnxa64_dvd1.iso in ubuntu
```
sudo mount -t auto -o loop R2017a_glnxa64_dvd1.iso ~/Matlab
```

4. begin installation
```
sudo ~/Matlab/install
```

5. choose offline verification and input register code in readme.txt(crack folder),which begin with 09806

6. when progress is 75%,installation page have a tip "insert iso file",input command below:
```
sudo mount -t auto -o loop R2017a_glnxa64_dvd2.iso ~/Matlab
```

7. click continue button,waiting installation finished.

8. crack process
```shell
# /usr/local/MATLAB/R2017a/ is the path you install the matlab
sudo mkdir /usr/local/MATLAB/R2017a/bin/licenses/
sudo cp Crack/license_standalone.lic  /usr/local/MATLAB/R2017a/bin/licenses/
sudo cp Crack/R2017a/bin/glnxa64/libmwservices.so /usr/local/MATLAB/R2017a/bin/glnxa64/
```
Here "Crack" is in the same directory with R2017a_glnxa64_dvd1.iso.

9. run matlab
```
sudo /usr/local/MATLAB/R2017a/bin/matlab
```

10. provide secret file
After run matlab, you will be asked to activate the software again. Just use the registration offline, and provide the file in **/usr/local/MATLAB/R2017a/bin/licenses/license_standalone.lic**.
11. install matlab-support
matlab-support will allow us start matlab with `sudo matlab` in anywhere.
```
sudo apt-get install matlab-support
```

reference: [ubuntu14.04安装matlab2017a](http://blog.csdn.net/m0_37407756/article/details/73187654)


## 4.2. Statistical library
a. Download gsl library for offical website. https://www.gnu.org/software/gsl/
b. Install process
```shell
cd <the folder>
./configure --prefix=(Put your directory to install)
make check
make
make install
```
c. put library to settings
```
export GSL_HOME=/usr/lib/gsl2.4
export PATH={GSL_HOME}/bin:$PATH
export LD_LIBRARY_PATH=$GSL_HOME/lib
```

## 4.3. hadoop mapreduce call matlab
### 4.3.1. install hadoop cluster

1. prepare 3 machine for hadoop cluster
```
*************  master-namenode
*************  slave-datanode
*************  slave-datanode
```
2. configue hostnames for each machine
Add following ip-name to /etc/hosts:
************* hadoop-master
************* hadoop-slave-stream
************* hadoop-slave-bees
```
sudo vim /etc/hosts
```

3. create hadoop user for each machine
```shell
# add an user name hadoop
sudo adduser hadoop
```

4. Enable “hadoop” user to password-less SSH login to slaves
```shell
sudo apt-get install openssh-server
# If you already had an id_rsa in ~/.ssh, you can provice another name, for example id_rsa_hadoop, to avoid to overwrite the id_rsa.
ssh-keygen -t rsa -P '' -f ~/.ssh/id_rsa
# If you provice anothe name before, you should change the name id_rsa.pub as well.
cat ~/.ssh/id_rsa.pub >>~/.ssh/authorized_keys

sudo vi /etc/ssh/sshd_config
# remove # and modify the config item value
# RSAAuthentication yes
# PubkeyAuthentication yes
# AuthorizedKeysFile      %h/.ssh/authorized_keys

chmod 600 ~/.ssh/authorized_keys
chmod -R 700 ~/.ssh
sudo service ssh restart
ssh locahost
# don't need password after first
# cp master's authorized_keys to slaves
scp .ssh/authorized_keys hadoop@hadoop-slave-stream:~/.ssh/authorized_keys_master
scp .ssh/authorized_keys hadoop@hadoop-slave-bees:~/.ssh/authorized_keys_master
# ssh to slaves
cat ~/.ssh/authorized_keys_master >> ~/.ssh/authorized_keys
```
5. install software needed by Hadoop
Here will add java jdk to the system. If the system has jdk already, you can skip the step.
```shell
sudo scp alex@*************:/home/<USER>/tools/jdk-8u151-linux-x64.tar.gz .
tar -xvzf jdk-8u151-linux-x64.tar.gz
sudo mv jdk1.8.0_151 /usr/local/java/

# set env path for java jdk
vim ~/.bashrc
```
add them to ~/.bashrc
```
export JAVA_HOME=/usr/local/java/jdk1.8.0_151
export JRE_HOME=${JAVA_HOME}/jre
export CLASSPATH=.:${JAVA_HOME}/lib:${JRE_HOME}/lib
export PATH=${JAVA_HOME}/bin:$PATH
```
6. install hadoop
copy hadoop-2.7.4.tar.gz from remote to local
```
sudo scp hadoop@*************:/home/<USER>/hadoop-2.7.4.tar.gz .
tar -xvzf hadoop-2.7.4.tar.gz
```
config hadoop
```
# configure enviroment variables for "hadoop" user
vim ~/.bashrc
```
add them to ~/.bashrc
```
export HADOOP_COMMON_HOME=$HOME/hadoop-2.7.4
export HADOOP_MAPRED_HOME=$HADOOP_COMMON_HOME
export HADOOP_HDFS_HOME=$HADOOP_COMMON_HOME
export YARN_HOME=$HADOOP_COMMON_HOME
export PATH=$PATH:$HADOOP_COMMON_HOME/bin
export PATH=$PATH:$HADOOP_COMMON_HOME/sbin
```
update hadoop-env.sh
```
export JAVA_HOME=/usr/local/java/jdk1.8.0_151
```
update mapred-env.sh
```
export JAVA_HOME=/usr/local/java/jdk1.8.0_151
```
update yarn-env.sh
```
export JAVA_HOME=/usr/local/java/jdk1.8.0_151
```
core-site.xml
```
<property>
	<name>fs.defaultFS</name>
	<value>hdfs://hadoop-master/</value>
	<description>NameNode URI</description>
</property>
```
yarn-site.xml
```
<property>
	<name>yarn.resourcemanager.hostname</name>
	<value>hadoop-master</value>
	<description>The hostname of the ResourceManager</description>
</property>
<property>
	<name>yarn.nodemanager.aux-services</name>
	<value>mapreduce_shuffle</value>
	<description>shuffle service for MapReduce</description>
</property>
<property>
<name>yarn.nodemanager.resource.memory-mb</name>
	<value>4096</value>
</property>
<property>
	<name>yarn.scheduler.minimum-allocation-mb</name>
	<value>2048</value>
</property>
<property>
	<name>yarn.nodemanager.resource.cpu-vcores</name>
	<value>1</value>
</property>
	<property>
	<name>yarn.nodemanager.vmem-pmem-ratio</name>
	<value>2.1</value>
</property>
```
hdfs-site.xml
```
<property>
	<name>dfs.datanode.data.dir</name>
	<value>file:/home/<USER>/hadoop-2.7.4/hdfs/data</value>
	<description>DataNode directory for storing data chunks.</description>
</property>

<property>
	<name>dfs.namenode.name.dir</name>
	<value>file:/home/<USER>/hadoop-2.7.4/hdfs/name</value>
	<description>NameNode directory for namespace and transaction logs storage.</description>
</property>

<property>
	<name>dfs.replication</name>
	<value>3</value>
	<description>Number of replication for each chunk.</description>
</property>
```
mapred-site.xml
```
<property>
	<name>mapreduce.framework.name</name>
	<value>yarn</value>
	<description>Execution framework.</description>
</property>
```
slaves
```
hadoop-slave-bees
hadoop-slave-stream
```
Duplicate Hadoop configuration files to all nodes
```
cd
for i in `cat hadoop-2.7.4/etc/hadoop/slaves`; do
	echo $i; rsync -avxP --exclude=logs hadoop-2.7.4/ $i:hadoop-2.7.4/;
done
```
check installation
```
hdfs namenode -format
sbin/start-all.sh
jps
hdfs dfsadmin -report
```

### 4.3.2. install matlab server


### 4.3.3. write matlab function and mapreduce project and run mapreduce job
1. write mutlab function,and use deploytool command,package a jar matlab.jar

2. create java project,which locate *************:/home/<USER>/github/mapred
      copy matlab.jar,javabuilder.jar to project/lib

3. export as mapreduce job named mapreducejob

4. execute hadoop command
```
HADOOP_CLASSPATH=$HADOOP_HOME/jobs/javabuilder.jar:$HADOOP_HOME/jobs/dbscanfun.jar bin/hadoop jar jobs/MatlabJob.jar DbScanJob /wc/input/wc.txt /wc/output
```

## 4.4. NVIDIA GeForce GTX 1080
### 4.4.1. 查询NVIDIA驱动

首先去官网(http://www.nvidia.com/Download/index.aspx?lang=en-us)查看适合自己显卡的驱动(不用下载runfile文件)

### 4.4.2. 安装驱动

1. 安装之前先卸载已经存在的驱动版本：
```shell
sudo apt-get remove --purge nvidia*
```
2. 把 nouveau 驱动加入黑名单并禁用用 nouveau 内核模块（如果新重装的系统，没有驱动起作用，忽略此步骤，执行下一步）
```
sudo vim /etc/modprobe.d/blacklist-nouveau.conf
```
在文件 blacklist-nouveau.conf 中加入如下内容：
```
blacklist nouveau
options nouveau modeset=0
```
保存退出，执行如下指令：
```
sudo update-initramfs -u
```
重启系统
```
sudo reboot
```
3. 若电脑是集成显卡（NVIDIA独立显卡忽略此步骤），需要在安装之前禁止一项：
```shell
sudo service lightdm stop
```

4. 执行以下指令安装驱动：
```
sudo add-apt-repository ppa:xorg-edgers/ppa
sudo apt-get update
sudo apt-get install nvidia-384 #注意在这里指定自己的驱动版本！
sudo service lightdm start
```
安装完成之后输入以下指令进行验证：
```
sudo nvidia-smi
```
若列出了GPU的信息列表则表示驱动安装成功，重启系统。

### 4.4.3. 安装cuda8.0

1. 首先在官网上(https://developer.nvidia.com/cuda-downloads)下载CUDA8，注意是runfile文件

2. 下载完成后执行以下命令：
```
sudo sh cuda_8.0.27_linux.run
```
注意：执行后会有一系列提示让你确认，但是注意，有个让你选择是否安装nvidia361驱动时，一定要选择否：
```
Install NVIDIA Accelerated Graphics Driver for Linux-x86_64 361.62?
```
因为前面我们已经安装了更加新的nvidia384，所以这里不要选择安装。其余的都直接默认或者选择是即可。
可能出现的错误：
当出现“unsupport complier”错误时，说明gcc版本太高，需要降低gcc版本。解决办法如下：
以gcc4.9与g++4.9为例
安装低版本gcc与g++：
```
sudo apt-get install gcc-4.9 g++-4.9
```
之后进入/usr/bin:
```
cd /usr/bin
```
先删除和gcc5.0关联的gcc:
```
sudo rm gcc
sudo rm g++
```
再建个软连接
```
sudo ln -s gcc-4.9 gcc
sudo ln -s g++-4.9 g++
```
然后重新安装。

3. 环境变量配置

打开~/.bashrc文件：
```
sudo gedit ~/.bashrc
```
将以下内容写入到~/.bashrc尾部：
```
export PATH=/usr/local/cuda-8.0/bin${PATH:+:${PATH}}
export LD_LIBRARY_PATH=/usr/local/cuda-8.0/lib64${LD_LIBRARY_PATH:+:${LD_LIBRARY_PATH}}
```
4. 测试CUDA的sammples
```
cd /usr/local/cuda-8.0/samples/1_Utilities/deviceQuery #由自己电脑目录决定
sudo make
sudo ./deviceQuery
```
如果显示一些关于GPU的信息，则说明安装成功。


## 4.5. OPENGL
### 4.5.1. 安装 OPENGL
1. 首先不可或缺的就是编译器与基本的函式库，如果系统没有安装的话，依照下面的方式安装：
```
sudo apt-get install build-essential
```
2. 安装OpenGL Library
```
sudo apt-get install libgl1-mesa-dev
```
3. 安装OpenGL Utilities
*OpenGL Utilities 是一组建构于 OpenGL Library 之上的工具组，提供许多很方便的函式，使 OpenGL 更强大且更容易使用。*
```
sudo apt-get install libglu1-mesa-dev
```
4. 安装OpenGL Utility Toolkit
*OpenGL Utility Toolkit 是建立在 OpenGL Utilities 上面的工具箱，除了强化了 OpenGL Utilities 的不足之外，也增加了 OpenGL 对于视窗介面支援。*
```
sudo apt-get install libglut-dev
```
注意：在这一步的时候，可能会出现以下情况，shell提示：
```
Reading package lists... Done
Building dependency tree
Reading state information... Done
E: Unable to locate package libglut-dev
```
解决: 使用如下指令替换上面的 `sudo apt-get install libglut-dev` 即可。
```
sudo apt-get install freeglut3-dev
```
`/usr/bin/ld: cannot find -lGL` 的解决方法：
```
sudo ln -s /usr/lib/libGL.so.1 /usr/lib/libGL.so
```

### 4.5.2. 测试
创建示例 test.c
```c
#include <GL/glut.h>

void init(void)
{
	glClearColor(0.0, 0.0, 0.0, 0.0);
	glMatrixMode(GL_PROJECTION);
	glOrtho(-5, 5, -5, 5, 5, 15);
	glMatrixMode(GL_MODELVIEW);
	gluLookAt(0, 0, 10, 0, 0, 0, 0, 1, 0);

	return;
}

void display(void)
{
	glClear(GL_COLOR_BUFFER_BIT);
	glColor3f(1.0, 0, 0);
	glutWireTeapot(3);
	glFlush();

	return;
}

int main(int argc, char *argv[])
{
	glutInit(&argc, argv);
	glutInitDisplayMode(GLUT_RGB | GLUT_SINGLE);
	glutInitWindowPosition(0, 0);
	glutInitWindowSize(300, 300);
	glutCreateWindow("OpenGL 3D View");
	init();
	glutDisplayFunc(display);
	glutMainLoop();

	return 0;
}
```
编译执行
```shell
# 编译程式
gcc -o test test.c -lGL -lGLU -lglut
# 执行
$ ./test
```

# 5. 其他
## 5.1. FTP服务器
参考: [ubuntu 14.04 下FTP服务器的搭建--锁定用户目录](http://www.cnblogs.com/bcsflilong/p/4200139.html)
参考: [Ubuntu下搭建FTP服务器](http://blog.csdn.net/njchenyi/article/details/8499555)

### 5.1.1. 目标
* 指定一个共享目录, 并锁定该目录
* 只有指定用户可以访问
* 可以上传下载


### 5.1.2. 创建专门用于ftp的用户
1. 创建用户
```shell
# 创建用于ftp的组
sudo groupadd ftp
# 创建home目录, courier为所想创建的用户
sudo mkdir -p /home/<USER>
# 添加不可登录系统, 仅用于ftp的用户
sudo useradd courier -g ftp -d /home/<USER>/sbin/nologin
# 设置密码
passwd courier
```
2. 创建用于ftp的目录
```shell
sudo mkdir -p /home/<USER>/public/ftp
# 设置该目录不可写, 这个后面会说明
sudo chmod a-w /home/<USER>/public/ftp
# 或者设置该目录为对设置的访问ftp的用户不可写
sudo chown -R root:root /home/<USER>/public/ftp/
```
### 5.1.3. 安装vsftpd
1. 安装
```
sudo apt-get install vsftpd
```
2. 配置
修改vsftpd的配置文件
```shell
sudo vim /etc/vsftpd.conf
```
```shell
# 禁止匿名访问
anonymous_enable=NO
# 接受本地用户
local_enable=YES
# 可以上传
write_enable=YES
# 启用在chroot_list_file的用户只能访问根目录
# 这个行的上面写明白, 设置为根目录的目录必需设置为不可写
chroot_list_enable=YES
chroot_list_file=/etc/vsftpd.chroot_list
# 设置根目录
local_root=/home/<USER>/public/ftp
```
添加受访问目录限制的用户
```shell
# 如果还没有/etc/vsftpd.chroot_list, 则需要手动创建
# sudo touch /etc/vsftpd.chroot_list
sudo echo "courier" >> /etc/vsftpd.chroot_list
```

3. 一些异常
**异常1: 530 Login incorrect**
* 解决方案1: [vsftpd: 530 Login incorrect](https://askubuntu.com/questions/413677/vsftpd-530-login-incorrect)
```bash
# Back up the config file before making a change;
sudo cp /etc/vsftpd.conf /etc/vsftpd.conf.back
#and then edit vsftpd.conf (with vi or nano)
sudo vim /etc/vsftpd.conf
# Then make the following change
pam_service_name=ftp
# Save your change and restart the ftp server
sudo service vsftpd restart
```
* 解决方案2: [Ubuntu下搭建FTP服务器](http://blog.csdn.net/njchenyi/article/details/8499555)
**异常2：550 Failed to open file**
* 将文件夹权限设为777

### 5.1.4. 启动/重启/停止 vsftpd
```shell
sudo service vsftpd start | restart | stop
```

### 5.1.5. 访问ftp
#### *******. 用浏览器访问
1) 用浏览器打开 ftp://************* 输入用户密码即可访问
2) 用浏览器打开 ***********************************
#### *******. 用终端访问
1. 访问
```shell
ftp *************
# 或者先打开ftp, 再打开指定ip, 并输入用户名, 密码
ftp
open *************
```
2. 基本操作
```shell
# 下载 get <远程文件> <本地文件>
get /upload/text.txt /home/<USER>/temp/textFromFtp.txt
# 上传 put <本地文件> <远程文件>
put /home/<USER>/temp/textFromFtp.txt /upload/text.txt
```
### 5.1.6. ec2 上ftp服务器安装补充
 参考 [Amazon EC2上配置FTP服务器](https://www.skyarch.cn/blog/aws/ftp-on-ec2/)
#### *******. vsfptd.conf文件配置补充
```
修改	anonymous_enable=NO	禁止匿名FTP用户的登录
修改	dirmessage_enable=NO	用户初次进入新目录时，显示该目录需要注意的内容，在这里配置为不显示
修改	ls_recurse_enable=YES	允许用户删除目录
追加	ascii_upload_enable=YES	允许以ASCII模式上传文件
追加	ascii_download_enable=YES	允许以ASCII模式下载文件
修改	chroot_local_user=YES	把本地用户的根目录改为各自的FTP根目录
修改	chroot_list_enable=YES	有效chroot_list。默认文件是chroot_list_file=/etc/vsftpd/chroot_list
修改	tcp_wrappers=NO	不使用tcp_wrappers做限制(使用AWS EC2的Security Group作限制)
修改	connect_from_port_20=NO	不使用ActiveFTP
修改	xferlog_std_format=NO	以vsftpd格式记录到日志文件，YES时是wu-ftpd格式
追加	pasv_enable=YES	使用PASV FTP
追加	pasv_addr_resolve=YES	在PASV模式下从主机名，获取连接IP地址
追加	pasv_address=主机IP地址	配置EC2的Elastic IP地址
追加	pasv_min_port=60001	在PASV模式是使用的最小端口号(在手顺2确认的没有使用的端口里选择)
追加	pasv_max_port=60100	在PASV模式是使用的最大端口号(在手顺2确认的没有使用的端口里选择)
追加	use_localtime=YES	使用主机的时间
追加	force_dot_files=YES	不隐藏.开头的文件
```
#### *******. ec2实例安全组配置
```
Type	      Protocol	     Port Range	         Source
Custom TCP   Rule TCP	      21	        0.0.0.0/0
Custom TCP   Rule TCP	    60001-60100	        0.0.0.0/0
```
#### *******. 注意事项
ec2上面的实例IP可能会变，要及时更新选项“pasv_address=主机IP地址”

### 5.1.7. 搭建不同用户拥有不同文件夹的ftp服务器 @zhenqi.li
参考 [Ubuntu中vsftpd实现不同用户不同权限](http://blog.csdn.net/gavin_dinggengjia/article/details/7538118)

#### *******. 安装vsftpd
```
sudo apt-get install vsftpd
```
#### *******. 安装db4.x-utils
参考 [linux下db4.x-util的安装问题](http://blog.csdn.net/sunnypotter/article/details/17581221)

```
1、下载安装文件：

db4.7-util_4.7.25-21_amd64.deb
下载地址直达：http://cz.archive.ubuntu.com/ubuntu/pool/universe/d/db4.7/

2、dpkg - i  db4.7-util_4.7.25-21_amd64.deb
即可
```
#### *******. 建立本地虚拟用户
```
#useradd -d /home/<USER>
指定目录为/home/<USER>
```
#### *******. 建立虚拟用户数据库
```
#mkdir /etc/vsftpd
#vim /etc/vsftpd/login.txt
login.txt文件内容：
user1
user1pass
user2
user2pass
admin
adminpass
注：奇数行为用户名，偶数行为密码。
#db4.7_load -T -t hash -f /etc/vsftpd/login.txt /etc/vsftpd/vsftpd_login.db
#chmod 600 /etc/vsftpd/vsftpd_login.db
```
#### 5.1.7.5. 建立基于vsftpd_login的PAM授权文件
```
#vim /etc/pam.d/vsftpd.vu
vsftpd.vu文件内容：
auth required pam_userdb.so db=/etc/vsftpd/vsftpd_login
account required pam_userdb.so db=/etc/vsftpd/vsftpd_login
```
#### 5.1.7.6. 设置用户权限
```
建立用来存放用户权限设置文件的目录
#mkdir /etc/vfstpd/bees360
并在该文件夹下依次建立与login.txt对应的帐号名称相同的文件
#vim user1
内容可以参考下面：
#[user1] 根目录为/home/<USER>/user1
anon_world_readable_only=NO
write_enable=YES
anon_mkdir_write_enable=YES
anon_upload_enable=YES
anon_other_write_enable=YES
local_root=/home/<USER>/user1

#[user2] 根目录为/home/<USER>/user2
anon_world_readable_only=NO
write_enable=YES
anon_mkdir_write_enable=YES
anon_upload_enable=YES
anon_other_write_enable=YES
local_root=/home/<USER>/user2

#[admin] 根目录为/home/<USER>
anon_world_readable_only=NO
write_enable=YES
anon_mkdir_write_enable=YES
anon_upload_enable=YES
anon_other_write_enable=YES
local_root=/home/<USER>

#可以继续添加
```

#### *******. 设置vsftpd.conf
```
#vim /etc/vsftpd.conf
修改内容如下：
listen=YES
#important
listen_ipv6=NO
connect_from_port_20=YES
ftpd_banner=Welcome to Bees360 FTP service.

anonymous_enable=NO
local_enable=YES
write_enable=NO
anon_upload_enable=NO
anon_mkdir_write_enable=NO
anon_other_write_enable=NO
chroot_local_user=YES

guest_enable=YES
#important
guest_username=bees360

pam_service_name=vsftpd.vu
#important
user_config_dir=/etc/vsftpd/bees360
xferlog_enable=YES
xferlog_file=/var/log/vsftpd.log

```
#### *******. 创建对应的目录
```
#mkdir /home/<USER>//chmod a-w for admin
#mkdir /home/<USER>/admin //chmod 777
#mkdir /home/<USER>/user1 //chmod a-w for user1
#mkdir /home/<USER>/user2 //chmod a-w for user2
#mkdir /home/<USER>/user1/upload //chmod 777
#mkdir /home/<USER>/user2/upload //chmod 777
#chown -R bees360:bees360 /home/<USER>

```

#### *******. 重新启动vsftpd
```
#service vsftpd restart
```
#### *******0. 日常维护
```
（1）更新帐号
	#db4.7_load -T -t hash -f /etc/vsftpd/login.txt /etc/vsftpd/vsftpd_login.db
（2）更新/etc/vsftpd/bees360/下不同用户对应的权限
（3）重启vsftpd
	#service vsftpd restart
```
#### *******1. 注意事项
	1. 只能在权限为777的文件夹进行删除和创建操作

# 6. teamviewer使用 @LiZhenqi

## 6.1. 安装
### 6.1.1. Windows安装
1. 下载 http://rj.baidu.com/soft/detail/18099.html?ald
2. 按提示安装即可

### 6.1.2. 设置固定密码及如何使用
1. 设置好固定密码
2. 在无人值守访问中，选择随windows一同启动teamviewer
3. 记录下ID和设置的密码，平时连接就用这个id加上密码
4. 参考 https://jingyan.baidu.com/article/d5a880ebaf063d13f147cc29.html
