Security配置说明
===

## Token的结构
```json
{
  "jti": "f5ff52d17fe84267909efea3f000103f",
  "sub": "guanron4g <PERSON>",
  "aud": "web",
  "iss": "www.bees360.com",
  "iat": 1540954488,
  "roleList": [
    {
      "id": 0,
      "name": "Homeowner"
    },
    {
      "id": 1,
      "name": "Adjuster"
    },
    {
      "id": 2,
      "name": "Pilot"
    },
    {
      "id": 6,
      "name": "Reviewer"
    },
    {
      "id": 8,
      "name": "Processor"
    },
    {
      "id": 62,
      "name": "Admin"
    }
  ],
  "userId": 10037,
  "email": "<EMAIL>",
  "phone": "*************",
  "name": "guanron4g <PERSON>",
  "avatar": "https://bees360.s3.amazonaws.com/user/10037/avatar/1522846336851.png"
}
```

## 配置SpEL
### 认证授权配置
可以参考bees360-web下的spring-security.xml中的内容进行配置，下面截取其中的案例进行说明。这里的**认证**指的是判断用户是否已经登录，**授权**指的是判断用户是否具有操作的权限。默认对所有的请求进行认证。

**无需认证**
```xml
<http pattern="/temp/users" security="none" />
```
**仅认证无需授权**
```xml
<intercept-url pattern="/projects" method="GET" access="permitAll"/>
```
**认证并授权**
```xml
<!-- 这里对于所有的路径变量必需利用正则表达式进行匹配 -->
<intercept-url pattern="/projects/{projectId:\d+}/abstract" method="GET" access="hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMemberIn(authentication, #projectId)"/>
```

### 配置规则
1. 所有的路径变量必需使用正则表达式进行匹配，以防路径匹配错误，如`/projects/aiProcess` 可以匹配 `/projects/{projectId}`，但不能匹配 `/projects/{projectId:\d+}`。这一点非常重要。
2. 对于敏感信息必需设置安全权限，以防恶意用户随意串改。如用户角色的修改以及金钱相关。

注： 合并分支之后，如果你有修改spring-security.xml，请务必在合并完成之后，仔细检查自己配置的路径，防止被其他人的配置覆盖。

### 常用表达式
以下是配置中常用的表达式
| 表达式 | 描述 |
| ----- | ---- |
| permitAll | 仅认证，不验证授权 |
| hasRole(role) | 认证授权，要求用户具有特定角色 |
| hasAnyRole([role]) | 认证授权，要求用户具有角色列表中的至少一个角色 |
| @SCAC.isMemberIn(authentication, #projectId) | 认证授权，要求用户为#project指定项目中的一员，authentication为认证通过得到的对象，#projectId为url路径中的{projectId} |
| @SCAC.isMemberWith(authentication, #projectId, [role]) | 认证授权，要求用户在#project指定项目中担任指定角色中的任意一个 |
| @SCAC.isMyself(authentication, #userId) | 认证授权，要求访问含有{userId}路径参数的url时，#userId指向当前用户 |

注: `@SCAC` 的具体实现为 `com.bees360.security.SecurityAccess`。
