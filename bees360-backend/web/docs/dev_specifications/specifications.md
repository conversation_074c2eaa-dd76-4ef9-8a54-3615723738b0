DEVELOPER SPECIFICATIONS
===

## Controller
### Controller返回结果无需再特地封装为ResponseJson对象
Controller返回结果无需再特地封装为ResponseJson对象，已经在`bees360-web.config.ControllerResponseHandler`进行了统一的封装处理。当然，如果你想写多点代码，将结果在`Controller`中封装为`Response`返回也不会产生任何问题。

<p style="text-align: right"> @guanrongYang 2018-11-23 </p>

## Mapper
### Mapper 中不需要再抛出异常Exception
Mapper接口中不需要再声明抛出Exception，Spring会将Mybatis的异常转化为相应的RuntimeException.

因为数据库异常是系统异常，所以我们一般都会做统一的提示处理。这个统一的处理已经在`bees360-web.config.ControllerExceptionHandler.allException`中进行了实现。有了这个实现，异常可以直接在 Controller 中进行抛出，也就没有必要在 Controller 进行异常处理了。

<p style="text-align: right"> @guanrongYang 2018-11-23 </p>
