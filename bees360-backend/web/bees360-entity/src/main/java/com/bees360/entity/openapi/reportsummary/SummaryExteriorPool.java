package com.bees360.entity.openapi.reportsummary;

import lombok.Data;

@Data
public class SummaryExteriorPool {
    /**
     * The type of swimming pool, such as ["In-ground, Attached Pool", "In-ground, Detached Pool",
     * "Above Ground, Attached Pool", "Above Ground, Detached Pool"].
     */
    private String type;

    /** The size of swimming pool, such as ["Small", "Medium", "Large"]. */
    private String size;

    /** If swimming pool cage discovered */
    private Boolean hasCage;

    /** The size of pool cage, such as ["Small", "Medium", "Large"]. */
    private String cageSize;
}
