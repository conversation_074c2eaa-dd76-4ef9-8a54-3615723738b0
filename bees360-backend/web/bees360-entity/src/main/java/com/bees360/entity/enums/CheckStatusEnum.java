package com.bees360.entity.enums;

import java.util.Arrays;

public enum CheckStatusEnum {
    NONE(0, "None"),
    CHECK_IN(1, "Check In"),
    CHECK_OUT(2, "Check Out"),

    ;
    final private int code;
    final private String display;

    CheckStatusEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    public int getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }

    public static CheckStatusEnum getEnum(Integer code) {
        return Arrays.stream(values()).filter(statusEnum -> code == statusEnum.code).findFirst().orElse(NONE);
    }
}
