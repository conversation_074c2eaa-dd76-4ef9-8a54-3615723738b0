package com.bees360.entity;
import java.io.Serial;
import java.io.Serializable;
import com.bees360.entity.enums.OperationEnum;
import com.bees360.entity.enums.PermissionTypeEnum;

public class Permission  implements Serializable {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;

	private static String PERMISSION_PREFIX = "OP";

	private long permissionId;
	private long parentId;
	private int operationId;
	private String permissionNumber;
	private int permissionType;
	private String display;

	private Permission parent;

	public long getPermissionId() {
		return permissionId;
	}

	public void setPermissionId(long permissionId) {
		this.permissionId = permissionId;
	}

	public long getParentId() {
		return parentId;
	}

	public void setParentId(long parentId) {
		this.parentId = parentId;
	}

	public int getOperationId() {
		return operationId;
	}

	public void setOperationId(int operationId) {
		this.operationId = operationId;
	}

	public String getPermissionNumber() {
		return permissionNumber;
	}

	public void setPermissionNum(String permissionNumber) {
		this.permissionNumber = permissionNumber;
	}

	public int getPermissionType() {
		return permissionType;
	}

	public void setPermissionType(int permissionType) {
		this.permissionType = permissionType;
	}

	public String getDisplay() {
		return display;
	}

	public void setDisplay(String display) {
		this.display = display;
	}

	public Permission getParent() {
		return parent;
	}

	public void setParent(Permission parent) {
		this.parent = parent;
	}

	public void setPermissionNumber(String permissionNumber) {
		this.permissionNumber = permissionNumber;
	}

	public String getPermissionName() {
		String operationName = OperationEnum.getEnum(operationId).getDisplay();
		String permissionName = PERMISSION_PREFIX + ":" + operationName + ":"
				+ PermissionTypeEnum.getEnum(this.permissionType).getDisplay() + "_"
				+ getWholeDisplay();
		return permissionName;
	}

	public String getWholeDisplay(){
		String parentDisplay = (parent == null || parent.getDisplay() == null) ? "" : parent.getDisplay();
		String tempDispay = this.getDisplay() == null ? "" : this.getDisplay();
		return "".equals(parentDisplay) ? tempDispay : parentDisplay + "_" + tempDispay;
	}
}
