package com.bees360.entity.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ProjectImageBoundaryDto {

	// Identifier of an image.
	private String imageId;

    // The direction of this image in a house,
	// it must be assigned a value through the Enum DirectionEnum.
	private int direction;
	// it must be assigned a value through the Enum ImageTypeEnum.
	private int imageType;
	// It is a 3*4 matrix used for 3D construction, i.e.,
	// specifically the coordinate space transformation.
	private String camPropertyMatrix;
}
