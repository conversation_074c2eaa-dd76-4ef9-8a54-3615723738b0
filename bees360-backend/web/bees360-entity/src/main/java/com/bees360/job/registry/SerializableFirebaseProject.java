package com.bees360.job.registry;

import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.firebase.FirebaseLeaveMessage;
import com.google.cloud.firestore.GeoPoint;
import java.io.Serial;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/8/25 11:42 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JobPayload
@ToString
public class SerializableFirebaseProject extends SerializableFirebaseData {
    @Serial
    private static final long serialVersionUID = 7129433308502247832L;
    // ###### 初始化字段，不可以被更新，Bees360与BeesPilot都是只读
    private String project_id;
    private long creator_id;
    private String creator_name;
    private long created_time;

    // ###### project 基本信息，Bees360需要更新，但是BeesPilot只读
    private String street_address;

    private String city;

    private String state;

    private String country;

    private String zipcode;

    private GeoPoint gps;

    private Boolean is_gps_approximate;

    private int fly_zone_code;

    /** 代理人的名字 */
    private String agent_name;

    /** 代理人的联系电话 */
    private String agent_phone;

    private String agent_email;

    private Integer claim_type;

    private int service_type;

    private Long insurance_company_id;

    private String insurance_company_name;

    /** insurance公司 Logo 的 S3 key */
    private String insurance_company_logo;

    /** 户主姓名 */
    private String insured_name;

    /** 户主电话 */
    private String insured_phone;

    /** 户主邮箱 */
    private String insured_email;

    private long update_time;

    private String policy_number;

    private String policy_effective_date;

    /** process by */
    private Long process_company_id;

    private String claim_number;

    private String year_build;

    private Long damage_event_time;

    private String process_company_name;

    /** process公司 Logo 的 S3 key */
    private String process_company_logo;

    /** 联系户主时的note信息, 初始化时需要同步 */
    private String note;

    /** 属于项目基本信息，但是BeesPilot可以修改 */
    private Long due_date;

    private String inspection_code;

    private Long expiration_time;

    // ###### 项目状态相关字段，Bees360与BeesPilot都可编辑

    private int project_status;

    private Long contact_time;

    private Long initial_contact_time;

    /**
     * project_status字段最近被修改的，只能是 {“Bees360", "Beespilot"}
     *
     * @see SystemTypeEnum
     */
    private String project_status_update_by;

    private long status_update_time;

    private String project_state;
    private String project_state_change_reason;

    // ###### 项目相关联字段，Bees360与BeesPilot都可编辑 TODO 以后这部分要尽量移除，Bees360不需要维护项目相关联的字段

    /** project所属的tag */
    private Long tag_id;

    private Long hover_job_id;

    /** hover账号 */
    private String hover_account;

    private List<FirebaseLeaveMessage> claim_leave_message;
}
