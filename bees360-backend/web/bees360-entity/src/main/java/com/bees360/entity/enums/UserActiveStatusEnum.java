package com.bees360.entity.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public enum UserActiveStatusEnum implements BaseCodeEnum{
	BANNED(-2, false),
	DELETED(-1, false),
	INACTIVE(0, false),
	ACTIVE(1, true)
	;

	private static Map<Integer, UserActiveStatusEnum> statusMap = new HashMap<Integer, UserActiveStatusEnum>();

	static {
		for(UserActiveStatusEnum status: UserActiveStatusEnum.values()) {
			statusMap.put(status.getCode(), status);
		}
	}

	private final int code;
	private final boolean enabled;

	UserActiveStatusEnum(int code, boolean enabled){
		this.code = code;
		this.enabled = enabled;
	}

	public static UserActiveStatusEnum getEnum(int code) {
		return statusMap.get(code);
	}

	@Override
	public String getDisplay(){
		return this.toString();
	}

	@Override
	public int getCode(){
		return code;
	}

	public static Set<Integer> codes() {
		return statusMap.keySet();
	}

	public boolean isEnabled() {
		return enabled;
	}

	public static void main(String[] args) {
		show();
	}

	private static void show() {
		System.out.println("|name|code|");
		System.out.println("|----|----|");
		for(UserActiveStatusEnum type: UserActiveStatusEnum.values()) {
			System.out.println("|" + type.getDisplay() + "|" + type.getCode() + "|" );
		}
	}
}
