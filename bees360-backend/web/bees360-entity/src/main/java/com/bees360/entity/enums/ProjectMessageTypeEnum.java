package com.bees360.entity.enums;

/**
 * TODO 该类应该使用api的ProjectInformation.InformationType替代
 */
public enum ProjectMessageTypeEnum implements BaseCodeEnum {
    ADMIN_NOTE(1, "Admin Note."),

    PILOT_FEEDBACK(2, "Pilot Feedback"),

    PILOT_EARLY_CHECKOUT(3, "Pilot Early Checkout"),

    PROJECT_CONTACT_QUIZ(4, "Contact Customer Quiz"),

    PROJECT_CONTACT_NOTE(5, "Contact Customer Note"),

    SWYFFT_PROJECT_BIND_NOTE(6, "Swyfft Project Bind"),

    INSPECTION_CODE(7, "iBees Inspection"),

    PHONE_CALLED_RECORD(8, "Pilot Called Record"),

    LEAVE_NOTE(9, "Leave Note"),

    ;

    ProjectMessageTypeEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }
    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    private final int code;
    private final String display;
}
