package com.bees360.entity.openapi.reportsummary;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SummaryInteriorPlumbing {
    /**
     * If there are any plumbing fixtures without shut off valves
     * Optional
     */
    private Boolean noShutoffValve;
    /**
     * If the water heater is older than 15 years
     * Optional
     */
    private Boolean hasOldWaterHeater;
    /**
     * If the water heater is rusting or in poor condition, has exposed wires or no TPR Valve
     * Optional
     */
    private Boolean hasPoorWaterHeaterCondition;

    /**
     * Indicates if there is evidence that property has galvanized steel pipes based on visual inspection.
     * Galvanized steel pipes may be prone to flow restrictions due to internal corrosion build up over time.
     * example: true
     */
    private Boolean hasGalvanizedPipes;

    private Boolean isUpdated;

    private Integer yearUpdated;

    private String systemUpdateStatus;

    private Boolean hasIneligiblePlumbing;
}
