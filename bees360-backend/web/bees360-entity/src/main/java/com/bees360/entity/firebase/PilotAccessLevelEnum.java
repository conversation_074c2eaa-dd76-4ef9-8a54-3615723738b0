package com.bees360.entity.firebase;

import com.bees360.entity.enums.UserActiveStatusEnum;

public enum PilotAccessLevelEnum {

    // 如果飞手被ban时,或者该账号被删除时，或者该飞手的角色被移除时是该状态
    BANED(-1),

    // 首次进入 APP 入口是 Training 页面，没有 Mission 访问权限
    // 如果飞手待审核时是该状态,该状态不能接受misson
    TRAINING(0),

    // 首次进入 APP 入口是 Mission 页面
    // 该状态可以接受mission
    MISSION(1);

    PilotAccessLevelEnum(int code) {
        this.code = code;
    }
    final private int code;

    public int getCode() {
        return code;
    }

    /**
     * 根据用户activeStatus获取pilot用户权限等级
     *
     * @param status
     * @return PilotAccessLevelEnum
     */
    public static PilotAccessLevelEnum getAccessLevelByUserActiveStatus(int status) {
        if (status == UserActiveStatusEnum.ACTIVE.getCode()) {
            return PilotAccessLevelEnum.MISSION;
        } else if (status == UserActiveStatusEnum.INACTIVE.getCode()) {
            return PilotAccessLevelEnum.TRAINING;
        } else {
            return PilotAccessLevelEnum.BANED;
        }
    }
}
