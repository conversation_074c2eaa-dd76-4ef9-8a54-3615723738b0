package com.bees360.entity;

import com.bees360.entity.enums.ReportTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ReportSummary implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一id
     */
    private long id;
    /**
     * 关联的Project的id
     */
    private long projectId;
    /**
     * 报告类型
     *
     * @see ReportTypeEnum#getCode()
     */
    private int reportType;
    /**
     * json字符串
     */
    private String summary;
    /**
     * 软删除标志
     */
    @JsonIgnore
    private boolean deleted;
}
