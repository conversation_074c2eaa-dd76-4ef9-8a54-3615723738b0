package com.bees360.entity.enums;

public enum  ActivitySourceEnum implements BaseCodeEnum{
    BEESPILOT_QUIZ(1,"BEESPILOT_QUIZ"),
    WEB(2,"WEB"),
    IBEES_GENERATION(3, "IBEES_GENERATION"),
    AI(4, "AI"),
    BEE<PERSON><PERSON>OT_DAMAGE(5, "BEESPILOT_DAMAGE"),
    BEE<PERSON><PERSON>OT(5, "BEESPILOT"),
    SENSITIVE_BEESPILOT(6, "SENSITIVE_BEESPILOT"),
    BEESPILOT_APP(7, "BEESPILOT_APP"),
    ;

    ActivitySourceEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    private final int code;
    private final String value;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return value;
    }

    public String getValue() {
        return value;
    }
}
