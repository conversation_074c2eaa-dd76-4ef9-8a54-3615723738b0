package com.bees360.entity;

import jakarta.validation.constraints.NotBlank;

import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 属于 bees360-client-connect 模块
 *
 * <AUTHOR>
 * @date 2020/04/01 09:49
 */
@Data
public class ExternalClient {
    private String clientId;
    /**
     * Client type, used to match different platforms
     * @see ExternalClientIntegratedEnum
     */
    @NotBlank
    @Length(max = 50)
    private String clientIntegrated;
    /**
     * Authenticate the client's username
     */
    @NotBlank
    @Length(max = 50)
    private String oauthUsername;
    /**
     * Authenticate the client's username
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @NotBlank
    @Length(max = 50)
    private String oauthPassword;

    private long createdTime;
    @JsonIgnore
    private boolean deleted;
}
