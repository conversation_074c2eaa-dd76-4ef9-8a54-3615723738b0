package com.bees360.entity.vo;

import java.util.List;

public class ProjectImageTinyVo {

	private String imageId;
	private String imageUrl;
	private int annotationType;
	private Integer[] annotationTypes;
	// The sources of this file: 0: drone image 1: cell-phone image …
	// it must be assigned a value through the Enum CELL_PHONE_IMAGE.
	private int fileSourceType;
	// it must be assigned a value through the Enum ImageTypeEnum.
	private int imageType;
	private String imageCategory;
	private List<ProjectImageTinyVoForImage> scopImageList;
	private int[] orientations;

	public ProjectImageTinyVo() {
		super();
	}
	public ProjectImageTinyVo(String imageId, String imageUrl, int annotationType) {
		super();
		this.imageId = imageId;
		this.imageUrl = imageUrl;
		this.annotationType = annotationType;
	}
	public ProjectImageTinyVo(String imageId, String imageUrl, int annotationType, Integer[] annotationTypes,
			int fileSourceType, int imageType, String imageCategory) {
		super();
		this.imageId = imageId;
		this.imageUrl = imageUrl;
		this.annotationType = annotationType;
		this.annotationTypes = annotationTypes;
		this.fileSourceType = fileSourceType;
		this.imageType = imageType;
		this.imageCategory = imageCategory;
	}

	public String getImageKey(){
	    return imageUrl;
    }

	public String getImageId() {
		return imageId;
	}
	public void setImageId(String imageId) {
		this.imageId = imageId;
	}
	public String getImageUrl() {
		return imageUrl;
	}
	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}
	public Integer[] getAnnotationTypes() {
		return annotationTypes;
	}
	public void setAnnotationTypes(Integer[] annotationTypes) {
		this.annotationTypes = annotationTypes;
	}
	public int getAnnotationType() {
		return annotationType;
	}
	public void setAnnotationType(int annotationType) {
		this.annotationType = annotationType;
	}
	public int getFileSourceType() {
		return fileSourceType;
	}
	public void setFileSourceType(int fileSourceType) {
		this.fileSourceType = fileSourceType;
	}
	public int getImageType() {
		return imageType;
	}
	public void setImageType(int imageType) {
		this.imageType = imageType;
	}
	public String getImageCategory() {
		return imageCategory;
	}
	public void setImageCategory(String imageCategory) {
		this.imageCategory = imageCategory;
	}
	public List<ProjectImageTinyVoForImage> getScopImageList() {
		return scopImageList;
	}
	public void setScopImageList(List<ProjectImageTinyVoForImage> scopImageList) {
		this.scopImageList = scopImageList;
	}
	public int[] getOrientations() {
		return orientations;
	}
	public void setOrientations(int[] orientations) {
		this.orientations = orientations;
	}

}
