package com.bees360.entity.vo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.bees360.entity.User;
import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.dto.IdNameListDto;
import com.bees360.entity.enums.RoleEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

public class UserViewVo {
	private String avatar;
	private String firstName;
	private String lastName;
	private String phone;
	private String email;

	private String zipCode;
	private String address;
	private String city;
	private String state;
	private String contry;
	private double lng;
	private double lat;

	private double travelRadius;

	private long registrationTime;
	private long roles;
	private long roleApplications;
	private String certificateList;
	private double walletBalance;
	private double discount;
	private int freeOnTrailProjectNum;
	private double newUserDiscount;
	private double newUserDiscountNum;
	private int activeStatus;

	private String companyName;
	private String companyLogo;
    /**
     * 是否颁发claim徽章
     * 表明飞手是否具有飞行Claim Case的资质
     */
    private Boolean hasClaimBadge;

    /**
     * 是否颁发underwriting徽章
     * 表明飞手是否具有飞行Underwriting Case的资质
     */
    private Boolean hasUnderwritingBadge;

    /**
     * 是否颁发Magicplan徽章
     * 表明飞手是否具有飞行Magicplan Case的资质
     */
    private Boolean hasMagicplanBadge;

	private List<Map<String, Object>> projectsPaid;
	private List<Map<String, Object>> projectsJoined;

    /** 评分 */
    private Double rating;
    @Setter
    @Getter
    private Boolean isSsoUser;
    @Setter
    @Getter
    private User.UserMFASettings userMfaSettings;
	/*  getter and setter */
	public String getAvatar() {
		return avatar;
	}
	public void setAvatar(String avatar) {
		this.avatar = avatar;
	}
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getZipCode() {
		return zipCode;
	}
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getContry() {
		return contry;
	}
	public void setContry(String contry) {
		this.contry = contry;
	}
	public double getLng() {
		return lng;
	}
	public void setLng(double lng) {
		this.lng = lng;
	}
	public double getLat() {
		return lat;
	}
	public void setLat(double lat) {
		this.lat = lat;
	}
	public double getTravelRadius() {
		return travelRadius;
	}
	public void setTravelRadius(double travelRadius) {
		this.travelRadius = travelRadius;
	}
	public long getRegistrationTime() {
		return registrationTime;
	}
	public void setRegistrationTime(long registrationTime) {
		this.registrationTime = registrationTime;
	}
	@JsonIgnore
	public String getCertificateList() {
		return certificateList;
	}
	public void setCertificateList(String certificateList) {
		this.certificateList = certificateList;
	}
	public double getWalletBalance() {
		return walletBalance;
	}
	public void setWalletBalance(double walletBalance) {
		this.walletBalance = walletBalance;
	}
	public double getDiscount() {
		return discount;
	}
	public void setDiscount(double discount) {
		this.discount = discount;
	}
	public int getFreeOnTrailProjectNum() {
		return freeOnTrailProjectNum;
	}
	public void setFreeOnTrailProjectNum(int freeOnTrailProjectNum) {
		this.freeOnTrailProjectNum = freeOnTrailProjectNum;
	}
	public double getNewUserDiscount() {
		return newUserDiscount;
	}
	public void setNewUserDiscount(double newUserDiscount) {
		this.newUserDiscount = newUserDiscount;
	}
	public double getNewUserDiscountNum() {
		return newUserDiscountNum;
	}
	public void setNewUserDiscountNum(double newUserDiscountNum) {
		this.newUserDiscountNum = newUserDiscountNum;
	}
	public int getActiveStatus() {
		return activeStatus;
	}
	public void setActiveStatus(int activeStatus) {
		this.activeStatus = activeStatus;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getCompanyLogo() {
		return companyLogo;
	}
	public void setCompanyLogo(String companyLogo) {
		this.companyLogo = companyLogo;
	}
	public List<Map<String, Object>> getProjectsPaid() {
		return projectsPaid;
	}
	public void setProjectsPaid(List<Map<String, Object>> projectsPaid) {
		this.projectsPaid = projectsPaid;
	}
	public List<Map<String, Object>> getProjectsJoined() {
		return projectsJoined;
	}
	public void setProjectsJoined(List<Map<String, Object>> projectsJoined) {
		this.projectsJoined = projectsJoined;
	}
	@JsonIgnore
	public long getRoles() {
		return roles;
	}
	public List<IdNameDto> getRoleList() {
		return listRoles(roles);
	}
	public void setRoles(long roles) {
		this.roles = roles;
	}
	public void setRoleApplications(long roleApplications) {
		this.roleApplications = roleApplications;
	}
	@JsonIgnore
	public long getRoleApplications() {
		return roleApplications;
	}
	public List<IdNameDto> getRoleApplicationList() {
		return listRoles(roleApplications);
	}
	public List<IdNameListDto> getCertificates() {
		User user = new User();
		user.setRoles(roles);
		user.setRoleApplicationStatus(roleApplications);
		user.setCertificateList(certificateList);
		Map<Integer, List<String>> roleCertificates = user.parseCertificates();
		List<IdNameListDto> result = new ArrayList<IdNameListDto>();
		for(Entry<Integer, List<String>> entry: roleCertificates.entrySet()) {
			RoleEnum role = RoleEnum.getEnum(entry.getKey());
			if(role == null || (!user.hasRole(role) && !user.isApplyingRole(role))) {
				continue;
			}
			IdNameListDto idNameList = new IdNameListDto(role.getCode(), role.getDisplay(), entry.getValue());
			result.add(idNameList);
		}
		return result;
	}
	/* special getter */
	public String getName(){
		return (firstName + " " + lastName).trim();
	}

	public List<IdNameDto> listRoles(long roles){
		List<RoleEnum> roleList = RoleEnum.listRoles(roles);
		List<IdNameDto> roleIdNames = new ArrayList<IdNameDto>(roleList.size());
		for(RoleEnum role: roleList){
			roleIdNames.add(new IdNameDto(role.getRoleId(), role.getDisplay()));
		}
		return roleIdNames;
	}
	public List<IdNameListDto> listRoleApplications(){
		User user = new User();
		user.setRoles(roles);
		user.setRoleApplicationStatus(roleApplications);
		user.setCertificateList(certificateList);
		Map<RoleEnum, List<String>> certificateMap = user.listRoleApplicationsWithCertificates();
		List<IdNameListDto> roleIdNameLists = new ArrayList<IdNameListDto>();
		for(Entry<RoleEnum, List<String>> entry: certificateMap.entrySet()) {
			RoleEnum roleEnum = entry.getKey();
			List<String> certificates = entry.getValue();
			roleIdNameLists.add(new IdNameListDto(roleEnum.getCode(), roleEnum.getDisplay(), certificates));
		}
		return roleIdNameLists;
	}

    public Boolean getHasClaimBadge() {
        return hasClaimBadge;
    }

    public void setHasClaimBadge(Boolean hasClaimBadge) {
        this.hasClaimBadge = hasClaimBadge;
    }

    public Boolean getHasUnderwritingBadge() {
        return hasUnderwritingBadge;
    }

    public void setHasUnderwritingBadge(Boolean hasUnderwritingBadge) {
        this.hasUnderwritingBadge = hasUnderwritingBadge;
    }

    public Boolean getHasMagicplanBadge() {
        return hasMagicplanBadge;
    }

    public void setHasMagicplanBadge(Boolean hasMagicplanBadge) {
        this.hasMagicplanBadge = hasMagicplanBadge;
    }

    public Double getRating() {
        return rating;
    }

    public void setRating(Double rating) {
        this.rating = rating;
    }

    @Override
	public String toString() {
		return "UserViewVo [avatar=" + avatar + ", firstName=" + firstName + ", lastName=" + lastName + ", phone="
				+ phone + ", email=" + email + ", zipCode=" + zipCode + ", address=" + address + ", city=" + city
				+ ", state=" + state + ", contry=" + contry + ", lng=" + lng + ", lat=" + lat + ", travelRadius="
				+ travelRadius + ", registrationTime=" + registrationTime + ", roles=" + roles + ", roleApplications="
				+ roleApplications + ", certificateList=" + certificateList + ", walletBalance=" + walletBalance
				+ ", discount=" + discount + ", freeOnTrailProjectNum=" + freeOnTrailProjectNum + ", newUserDiscount="
				+ newUserDiscount + ", newUserDiscountNum=" + newUserDiscountNum + ", activeStatus=" + activeStatus
				+ ", companyName=" + companyName + ", companyLogo=" + companyLogo + ", projectsPaid=" + projectsPaid
				+ ", projectsJoined=" + projectsJoined + ", rating=" + rating + "]";
	}
}
