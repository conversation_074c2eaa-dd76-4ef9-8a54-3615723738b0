package com.bees360.entity.enums;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * @see com.bees360.internal.ai.entity.enums.AiBotUserEnum;
 */
public enum AiBotUserEnum {

    AI_NEW_USER_ID("10000", "BeesAI"),
    WEB_NEW_USER_ID("20000", "BeesWeb"),
    UN_KNOWN_USER("30000", "Unknown User")
    ;

    private final String code;
    private final String display;


    AiBotUserEnum(String code, String display){
        this.code = code;
        this.display = display;
    }

    public String getCode() {
        return code;
    }

    public Integer getIntegerCode() {
        return Integer.valueOf(code);
    }

    public String getDisplay(){
        return display;
    }

    public static AiBotUserEnum getEnum(String code) {
        return Stream.of(AiBotUserEnum.values())
            .filter(o -> Objects.equals(code, o.getCode()))
            .findFirst()
            .orElse(null);

    }


}
