package com.bees360.entity.dto;

import java.util.List;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.vo.ProjectImageFacetPath;

/**
 * <AUTHOR>
 * @date 2019/09/11 11:47
 */
public class ProjectImageFullData {
    private ProjectImage image;
    // damage 标注
    private List<ImageAnnotationCellDto> damages;
    // frame 标注
    private List<ImageAnnotationCellDto> frames;
    // 统计每个frame中含有的damage标注的结果
    private List<DamageMeasurementFrame> measurementFrames;
    // 截图标注
    private List<ImageAnnotationCellDto> screenshots;
    // 图片上的面信息，一般为seen 2d path
    private List<ProjectImageFacetPath> facets;

    public ProjectImageFullData(ProjectImage image) {
        this.image = image;
    }

    public long getProjectId() {
        return image.getProjectId();
    }

    public String getImageId() {
        return image.getImageId();
    }

    public ProjectImage getImage() {
        return image;
    }

    public void setImage(ProjectImage image) {
        this.image = image;
    }

    public List<ImageAnnotationCellDto> getDamages() {
        return damages;
    }

    public void setDamages(List<ImageAnnotationCellDto> damages) {
        this.damages = damages;
    }

    public List<ImageAnnotationCellDto> getFrames() {
        return frames;
    }

    public void setFrames(List<ImageAnnotationCellDto> frames) {
        this.frames = frames;
    }

    public List<DamageMeasurementFrame> getMeasurementFrames() {
        return measurementFrames;
    }

    public void setMeasurementFrames(List<DamageMeasurementFrame> measurementFrames) {
        this.measurementFrames = measurementFrames;
    }

    public List<ImageAnnotationCellDto> getScreenshots() {
        return screenshots;
    }

    public void setScreenshots(List<ImageAnnotationCellDto> screenshots) {
        this.screenshots = screenshots;
    }

    public List<ProjectImageFacetPath> getFacets() {
        return facets;
    }

    public void setFacets(List<ProjectImageFacetPath> facets) {
        this.facets = facets;
    }

    @Override
    public String toString() {
        return "ProjectImageFullData{" + "image=" + image + ", damages=" + damages + ", frames=" + frames
            + ", measurementFrames=" + measurementFrames + ", screenshots=" + screenshots + ", facets=" + facets + '}';
    }
}
