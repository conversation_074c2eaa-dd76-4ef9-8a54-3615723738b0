package com.bees360.job.registry;

import com.bees360.entity.firebase.FirebaseFAA;
import com.bees360.entity.firebase.FirebaseLiabilityInsurance;
import com.bees360.entity.firebase.FirebasePilotAddress;
import com.bees360.entity.firebase.FirebasePilotBadge;
import com.bees360.entity.firebase.PilotAccessLevelEnum;
import com.google.cloud.Timestamp;
import java.io.Serial;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/** 飞手 */
@EqualsAndHashCode(callSuper = true)
@Data
@JobPayload
@ToString
public class SerializableFirebasePilot extends SerializableFirebaseData {

    @Serial
    private static final long serialVersionUID = -7810058244730167743L;

    /**
     * 用户权限等级，新用户是 0，审核通过的是 1，被 banned 的用户为 -1
     *
     * @see PilotAccessLevelEnum
     */
    private int accessLevel;

    /** 用户头像，s3 资源地址 */
    private String avatar;

    /** 用户邮箱 */
    private String email;

    /** 手机号，带国家前缀，如：************* */
    private String phone;

    private String firstName;

    private String lastName;

    /** 用户的地址，key */
    private FirebasePilotAddress homeAddress;

    /** 用户的活动半径，整数 */
    private double travelRadius;

    /** faa 证书的上传和审核记录 */
    private List<FirebaseFAA> faa;

    /** 飞手保单的上传和审核记录 */
    private List<FirebaseLiabilityInsurance> liabilityInsurance;

    /** Submitted，Rejected, Approved 三个状态（默认已提交） */
    private String status;

    /** status 为 Rejected 时，审核人员存的拒绝原因 */
    private String rejectReason;

    /** web 端使用的 userId，如：'10237'，区别于 firestore 的 uid */
    private String webUserId;

    /** Firestore 的时间戳格式，该条文档的创建时间 */
    private Timestamp createTime;

    /** Firestore 的时间戳格式，该条文档最近一次的更新时间 */
    private Timestamp updateTime;

    /** 飞手资质Map 飞手是否具有飞行Claim和Underwriting等的资质 */
    private Map<String, FirebasePilotBadge> badge;
}
