package com.bees360.entity.enums;

public enum StructrueEnum implements BaseCodeEnum{
	MAIN_DWELLING(0, "Main Dwelling"),
	DETACHED_STORAGE_SHED(1, "Detached Storage Shed"),
	DETACHED_GARAGE(2, "Detached Garage"),
	HORSE_STABLE(3, "Horse Stable"),
	GUEST_HOUSE(4, "Guest House"),
	// APS_STRUCTURE == OTHERS
	APS_STRUCTURE(5, "APS Structure");

	private final int code;
	private final String display;

	StructrueEnum(int code, String display){
		this.code = code;
		this.display = display;
	}
	@Override
	public int getCode() {
		return code;
	}
	@Override
	public String getDisplay() {
		return display;
	}

	public static StructrueEnum getEnum(int code){
		StructrueEnum[] structrues = StructrueEnum.values();
		for(StructrueEnum structrue: structrues){
			if(structrue.getCode() == code){
				return structrue;
			}
		}
		return null;
	}

}
