package com.bees360.job.registry;

import com.bees360.entity.firebase.BatchStatusEnum;
import java.io.Serial;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@JobPayload
@ToString
public class SerializableFirebaseBatch extends SerializableFirebaseData {

    @Serial
    private static final long serialVersionUID = -1678427260128787912L;

    /**
     * 该batch的状态
     *
     * @see BatchStatusEnum
     */
    private String status;

    private double basePay;

    private double extraPay;

    private Long planPaymentDate;

    private String note;

    private Long payTime;

    private long createdAt;

    private long updatedAt;

    private Boolean isDeleted = false;

    private Map<String, Object> project;

    private String reason;
}
