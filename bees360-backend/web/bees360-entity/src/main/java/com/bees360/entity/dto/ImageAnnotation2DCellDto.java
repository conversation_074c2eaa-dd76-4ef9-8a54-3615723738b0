package com.bees360.entity.dto;

import com.bees360.entity.BaseImageAnnotation;

public class ImageAnnotation2DCellDto extends ImageAnnotationCellDto{
	protected int sourceType;
    protected double confidenceLevel = BaseImageAnnotation.DEFAULT_CONFIDENCE_LEVEL;

	public ImageAnnotation2DCellDto() {}

	public ImageAnnotation2DCellDto(ImageAnnotationCellDto annotationCell, int sourceType) {
	    this(annotationCell, sourceType, BaseImageAnnotation.DEFAULT_CONFIDENCE_LEVEL);
	}

    public ImageAnnotation2DCellDto(ImageAnnotationCellDto annotationCell, int sourceType, double confidenceLevel) {
        this.annotationId = annotationCell.getAnnotationId();
        this.facetId = annotationCell.getFacetId();
        this.imageId = annotationCell.getImageId();
        this.longTypeId = annotationCell.getLongTypeId();
        this.p1 = annotationCell.getP1();
        this.p2 = annotationCell.getP2();
        this.p3 = annotationCell.getP3();
        this.p4 = annotationCell.getP4();

        this.sourceType = sourceType;
        this.usageType = annotationCell.getUsageType();
        this.confidenceLevel = confidenceLevel;
    }

	public int getSourceType() {
		return sourceType;
	}

	public void setSourceType(int sourceType) {
		this.sourceType = sourceType;
	}

    public double getConfidenceLevel() {
        return confidenceLevel;
    }

    public void setConfidenceLevel(double confidenceLevel) {
        this.confidenceLevel = confidenceLevel;
    }

    @Override
	public String toString() {
		return "ImageAnnotation2DCellDto [sourceType=" + sourceType + ", usageType=" + usageType + ", annotationId=" + annotationId + ", longTypeId="
				+ longTypeId + ", type=" + type + ", facetId=" + facetId + ", imageId=" + imageId + ", p1=" + p1
				+ ", p2=" + p2 + ", p3=" + p3 + ", p4=" + p4 + "]";
	}
}
