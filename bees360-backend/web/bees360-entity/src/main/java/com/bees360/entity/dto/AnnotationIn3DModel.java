package com.bees360.entity.dto;

public class AnnotationIn3DModel {

	protected int facetId;
	protected long annotationId;
	protected int type;
	protected int usageType;

	protected Point3D p1;
	protected Point3D p2;
	protected Point3D p3;
	protected Point3D p4;

	public Point[] points() {
		return new Point[]{p1, p2, p3, p4};
	}

	/* getter and setter */
	public int getFacetId() {
		return facetId;
	}
	public void setFacetId(int facetId) {
		this.facetId = facetId;
	}
	public long getAnnotationId() {
		return annotationId;
	}
	public void setAnnotationId(long annotationId) {
		this.annotationId = annotationId;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public int getUsageType() {
		return usageType;
	}
	public void setUsageType(int usageType) {
		this.usageType = usageType;
	}
	public Point3D getP1() {
		return p1;
	}
	public void setP1(Point3D p1) {
		this.p1 = p1;
	}
	public Point3D getP2() {
		return p2;
	}
	public void setP2(Point3D p2) {
		this.p2 = p2;
	}
	public Point3D getP3() {
		return p3;
	}
	public void setP3(Point3D p3) {
		this.p3 = p3;
	}
	public Point3D getP4() {
		return p4;
	}
	public void setP4(Point3D p4) {
		this.p4 = p4;
	}

	@Override
	public String toString() {
		return "AnnotationIn3DModel [facetId=" + facetId + ", annotationId=" + annotationId + ", type=" + type + ", usageType=" + usageType + ", p1="
				+ p1 + ", p2=" + p2 + ", p3=" + p3 + ", p4=" + p4 + "]";
	}
}
