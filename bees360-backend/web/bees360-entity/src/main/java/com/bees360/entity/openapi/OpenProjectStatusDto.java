package com.bees360.entity.openapi;

import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.validate.ProjectStatus;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class OpenProjectStatusDto {

    /**
     * project status value
     * @see com.bees360.entity.enums.NewProjectStatusEnum
     */
    @ProjectStatus(statuses = {
        NewProjectStatusEnum.CLIENT_RECEIVED,
        NewProjectStatusEnum.PROJECT_CANCELED,
        NewProjectStatusEnum.RECEIVE_ERROR})
    @NotBlank
    private String value;

    private String comment;
}
