package com.bees360.entity.openapi;

import com.bees360.entity.dto.BaseSearchOption;
import com.bees360.entity.dto.ProjectSearchOption;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.validate.ProjectServiceType;
import com.bees360.entity.validate.ProjectStatus;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

public class OpenProjectSearchOption {

    private static final String DEFAULT_SORT_KEY = "projectId";

    /**
     * @see NewProjectStatusEnum#getValue()
     */
    @ProjectStatus
    private String status;

    @Setter
    private String policyNumber;

    @Setter
    private String inspectionNumber;

    @Setter
    @ProjectServiceType
    private String serviceName;

    @Getter
    @Setter
    private IntegerRange createdTime;

    @NotNull
    @Range(min = 1, max = 500)
    private Integer limit = 100;

    /**
     * start project_id
     */
    @Getter
    @Setter
    private Long startId;

    /**
     * start project_id
     */
    @Getter
    @Setter
    private Long start;

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Data
    static class IntegerRange {
        // include
        private Integer start;
        // exclude
        private Integer end;
    }

    public ProjectSearchOption toProjectSearchOption(){

        final ProjectSearchOption projectSearchOption = new ProjectSearchOption();
        projectSearchOption.setSortKey(DEFAULT_SORT_KEY);
        projectSearchOption.setSortOrder(BaseSearchOption.EOrder.ASC);
        projectSearchOption.setPageIndex(1);
        projectSearchOption.setPageSize(limit);
        projectSearchOption.setStartProjectId(startId == null ? start : startId);
        final NewProjectStatusEnum projectStatus = NewProjectStatusEnum.getEnumByValue(status);
        projectSearchOption.setProjectStatus(projectStatus == null ? null : projectStatus.getCode());

        Optional.ofNullable(policyNumber)
            .filter(StringUtils::isNotEmpty)
            .ifPresent(projectSearchOption::setPolicyNumber);

        Optional.ofNullable(inspectionNumber)
            .filter(StringUtils::isNotEmpty)
            .ifPresent(projectSearchOption::setInspectionNumber);

        Optional.ofNullable(createdTime)
            .map(IntegerRange::getStart)
            .map(t -> t * 1000L)
            .ifPresent(projectSearchOption::setStartTime);

        Optional.ofNullable(createdTime)
            .map(IntegerRange::getEnd)
            .map(t -> t * 1000L)
            .ifPresent(projectSearchOption::setEndTime);

        Optional.ofNullable(serviceName)
            .filter(StringUtils::isNotEmpty)
            .map(ProjectServiceTypeEnum::getEnumByValue)
            .ifPresent(t -> projectSearchOption.setServiceTypes(List.of(t.getCode())));

        return projectSearchOption;
    }
}
