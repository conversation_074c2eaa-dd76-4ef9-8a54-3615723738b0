package com.bees360.entity.enums;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

public enum RealtimeElementTypeEnum implements BaseCodeEnum {
	OVERVIEW(10, "Overview of rooftop of the risk.", RealtimeElementBaseEnum.OVERVIEW_ROOFTOP,
			ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, true, null),
	FRONT(13, "Close-up Image of the Front Slope.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, true, OrientationEnum.FRONT),
	RIGHT(14, "Close-up Image of the Right Slope.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, true, OrientationEnum.RIGHT),
	BACK(15, "Close-up Image of the Back Slope.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, true, OrientationEnum.BACK),
	LEFT(16, "Close-up Image of the Left Slope.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, true, OrientationEnum.LEFT),
	CROP(17, "Associated {damageType} details of {direction} slope.", RealtimeElementBaseEnum.CROP,
			ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, true, null),
	SLOPE(18, "Overview of front of the risk.", RealtimeElementBaseEnum.SLOPE,
			ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, true, null),

	QUICK_DAMAGE_OVERVIEW(50, "Overview of rooftop of the risk.", RealtimeElementBaseEnum.OVERVIEW_ROOFTOP,
			ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT, false, null),
	QUICK_DAMAGE_CELLPHONE(51, "Cellphone.", RealtimeElementBaseEnum.CELLPHONE,
			ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT, false, null),
	QUICK_DAMAGE_CLOSE_UP(52, "Close-up image.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT, false, null),
	QUICK_DAMAGE_CROP(57, "Associated hail damage details.", RealtimeElementBaseEnum.CROP,
			ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT, false, null),
	QUICK_DAMAGE_SLOPE(58, "Overview of front of the risk.", RealtimeElementBaseEnum.SLOPE,
			ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT, false, null),

	PREVISIT_OVERVIEW_ROOFTOP(70, "Overview of rooftop of the risk.", RealtimeElementBaseEnum.OVERVIEW_ROOFTOP,
			ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT, false, null),
	PREVISIT_GOOGLE_MAP(71, "Google map.", RealtimeElementBaseEnum.CELLPHONE,
			ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT, false, null),
	PREVISIT_CLOSE_UP(72, "Close-up image.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT, false, null),
	PREVISIT_CROP(77, "Associated hail damage details.", RealtimeElementBaseEnum.CROP,
			ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT, false, null),
	PREVISIT_SLOPE(78, "Overview of front of the risk.", RealtimeElementBaseEnum.SLOPE,
			ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT, false, null),

	REALTIME_OVERVIEW(80, "Overview of rooftop of the risk.", RealtimeElementBaseEnum.OVERVIEW_ROOFTOP,
			ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT, false, null),
	REALTIME_FRONT_RISK(81, "Overview of front of the risk.", RealtimeElementBaseEnum.CELLPHONE,
			ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT, false, null),
	REALTIME_FRONT(83, "Close-up Image of the Front Slope.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT, false, OrientationEnum.FRONT),
	REALTIME_RIGHT(84, "Close-up Image of the Right Slope.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT, false, OrientationEnum.RIGHT),
	REALTIME_BACK(85, "Close-up Image of the Back Slope.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT, false, OrientationEnum.BACK),
	REALTIME_LEFT(86, "Close-up Image of the Left Slope.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT, false, OrientationEnum.LEFT),
	REALTIME_CROP(87, "Associated hail damage details of {direction} slope.", RealtimeElementBaseEnum.CROP,
			ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT, false, null),
	REALTIME_SLOPE(88, "Overview of front of the risk.", RealtimeElementBaseEnum.SLOPE,
			ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT, false, null),

	APP_DAMAGE_OVERVIEW(150, "Overview of the risk.", RealtimeElementBaseEnum.OVERVIEW_ROOFTOP,
			ReportTypeEnum.PROPERTY_IMAGE_REPORT, false, null),
	APP_DAMAGE_FRONT_RISK(151, "Overview of front of the risk.", RealtimeElementBaseEnum.CELLPHONE,
			ReportTypeEnum.PROPERTY_IMAGE_REPORT, false, null),
	APP_DAMAGE_CLOSE_UP(152, "Close-up image.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.PROPERTY_IMAGE_REPORT, false, null),
	APP_DAMAGE_FRONT(153, "Close-up Image of the Front Slope.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.PROPERTY_IMAGE_REPORT, false, OrientationEnum.FRONT),
	APP_DAMAGE_RIGHT(154, "Close-up Image of the Right Slope.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.PROPERTY_IMAGE_REPORT, false, OrientationEnum.RIGHT),
	APP_DAMAGE_BACK(155, "Close-up Image of the Back Slope.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.PROPERTY_IMAGE_REPORT, false, OrientationEnum.BACK),
	APP_DAMAGE_LEFT(156, "Close-up Image of the Left Slope.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.PROPERTY_IMAGE_REPORT, false, OrientationEnum.LEFT),
	APP_DAMAGE_CROP(157, "Associated hail damage details of {direction} slope.", RealtimeElementBaseEnum.CROP,
			ReportTypeEnum.PROPERTY_IMAGE_REPORT, false, null),
	APP_DAMAGE_SLOPE(158, "Overview of front of the risk.", RealtimeElementBaseEnum.SLOPE,
			ReportTypeEnum.PROPERTY_IMAGE_REPORT, false, null),

	INFRARED_DAMAGE_OVERVIEW(160, "Overview of rooftop of the risk.", RealtimeElementBaseEnum.OVERVIEW_ROOFTOP,
			ReportTypeEnum.INFRARED_DAMAGE_REPORT, false, null),
	INFRARED_DAMAGE_CELLPHONE(161, "Cellphone.", RealtimeElementBaseEnum.CELLPHONE,
			ReportTypeEnum.INFRARED_DAMAGE_REPORT, false, null),
	INFRARED_DAMAGE_CLOSE_UP(162, "Close-up image.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.INFRARED_DAMAGE_REPORT, false, null),
	INFRARED_DAMAGE_CROP(167, "Associated hail damage details.", RealtimeElementBaseEnum.CROP,
			ReportTypeEnum.INFRARED_DAMAGE_REPORT, false, null),
	INFRARED_DAMAGE_SLOPE(168, "Overview of front of the risk.", RealtimeElementBaseEnum.SLOPE,
			ReportTypeEnum.INFRARED_DAMAGE_REPORT, false, null),

	ROOF_ONLY_UNDERWRITING_OVERVIEW(170, "Overview of rooftop of the risk.", RealtimeElementBaseEnum.OVERVIEW_ROOFTOP,
			ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT, false, null),
	ROOF_ONLY_UNDERWRITING_CELLPHONE(171, "Cellphone.", RealtimeElementBaseEnum.CELLPHONE,
			ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT, false, null),
	ROOF_ONLY_UNDERWRITING_CLOSE_UP(172, "Close-up image.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT, false, null),
	ROOF_ONLY_UNDERWRITING_CROP(177, "Associated hail damage details.", RealtimeElementBaseEnum.CROP,
			ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT, false, null),
	ROOF_ONLY_UNDERWRITING_SLOPE(178, "Overview of front of the risk.", RealtimeElementBaseEnum.SLOPE,
			ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT, false, null),

	FULL_SCOPE_UNDERWRITING_OVERVIEW(180, "Overview of rooftop of the risk.", RealtimeElementBaseEnum.OVERVIEW_ROOFTOP,
			ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT, false, null),
	FULL_SCOPE_UNDERWRITING_CELLPHONE(181, "Cellphone.", RealtimeElementBaseEnum.CELLPHONE,
			ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT, false, null),
	FULL_SCOPE_UNDERWRITING_CLOSE_UP(182, "Close-up image.", RealtimeElementBaseEnum.CLOSE_UP,
			ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT, false, null),
	FULL_SCOPE_UNDERWRITING_CROP(187, "Associated hail damage details.", RealtimeElementBaseEnum.CROP,
			ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT, false, null),
	FULL_SCOPE_UNDERWRITING_SLOPE(188, "Overview of front of the risk.", RealtimeElementBaseEnum.SLOPE,
			ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT, false, null);

	private final int code;
	private final String display;

	private final RealtimeElementBaseEnum BASE_TYPE;
	private final ReportTypeEnum REPORT_TYPE;

	private final boolean isPackage;
	private final OrientationEnum orientation;

	RealtimeElementTypeEnum(int code, String display, RealtimeElementBaseEnum baseType,
							ReportTypeEnum reportType, boolean isPackage, OrientationEnum orientation){
		this.code = code;
		this.display = display;
		this.BASE_TYPE = baseType;
		this.REPORT_TYPE = reportType;
		this.isPackage = isPackage;
		this.orientation = orientation;
	}

    @Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public RealtimeElementBaseEnum getBaseType() {
		return BASE_TYPE;
	}

	public ReportTypeEnum getReportType() {
		return REPORT_TYPE;
	}

	public boolean getIsPackage() {
		return isPackage;
	}

    public OrientationEnum getOrientation() {
        return orientation;
    }

    public int getSlope() {
		if (REPORT_TYPE == ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT) {
			return RealtimeElementTypeEnum.QUICK_DAMAGE_SLOPE.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT) {
			return RealtimeElementTypeEnum.PREVISIT_SLOPE.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT) {
			return RealtimeElementTypeEnum.REALTIME_SLOPE.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.INFRARED_DAMAGE_REPORT) {
			return RealtimeElementTypeEnum.INFRARED_DAMAGE_SLOPE.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT) {
			return RealtimeElementTypeEnum.ROOF_ONLY_UNDERWRITING_SLOPE.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT) {
			return RealtimeElementTypeEnum.FULL_SCOPE_UNDERWRITING_SLOPE.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.PROPERTY_IMAGE_REPORT) {
			return RealtimeElementTypeEnum.APP_DAMAGE_SLOPE.getCode();
		} else {
			return RealtimeElementTypeEnum.SLOPE.getCode();
		}
	}

	public int getOverview() {
		if (REPORT_TYPE == ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT) {
			return RealtimeElementTypeEnum.QUICK_DAMAGE_OVERVIEW.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT) {
			return RealtimeElementTypeEnum.PREVISIT_OVERVIEW_ROOFTOP.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT) {
			return RealtimeElementTypeEnum.REALTIME_OVERVIEW.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.INFRARED_DAMAGE_REPORT) {
			return RealtimeElementTypeEnum.INFRARED_DAMAGE_OVERVIEW.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT) {
			return RealtimeElementTypeEnum.ROOF_ONLY_UNDERWRITING_OVERVIEW.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT) {
			return RealtimeElementTypeEnum.FULL_SCOPE_UNDERWRITING_OVERVIEW.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.PROPERTY_IMAGE_REPORT) {
			return RealtimeElementTypeEnum.APP_DAMAGE_OVERVIEW.getCode();
		} else {
			return RealtimeElementTypeEnum.OVERVIEW.getCode();
		}
	}

	public int getCrop() {
		if (REPORT_TYPE == ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT) {
			return RealtimeElementTypeEnum.QUICK_DAMAGE_CROP.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT) {
			return RealtimeElementTypeEnum.PREVISIT_CROP.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT) {
			return RealtimeElementTypeEnum.REALTIME_CROP.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.INFRARED_DAMAGE_REPORT) {
			return RealtimeElementTypeEnum.INFRARED_DAMAGE_CROP.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT) {
			return RealtimeElementTypeEnum.ROOF_ONLY_UNDERWRITING_CROP.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT) {
			return RealtimeElementTypeEnum.FULL_SCOPE_UNDERWRITING_CROP.getCode();
		} else if (REPORT_TYPE == ReportTypeEnum.PROPERTY_IMAGE_REPORT) {
			return RealtimeElementTypeEnum.APP_DAMAGE_CROP.getCode();
		} else {
			return RealtimeElementTypeEnum.CROP.getCode();
		}
	}

	public List<Integer> getCloseUp() {
		List<Integer> closeUpList = new ArrayList<>();
		if (REPORT_TYPE == ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT) {
			closeUpList.add(RealtimeElementTypeEnum.QUICK_DAMAGE_CLOSE_UP.getCode());
		} else if (REPORT_TYPE == ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT) {
			closeUpList.add(RealtimeElementTypeEnum.PREVISIT_CLOSE_UP.getCode());
		} else if (REPORT_TYPE == ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT) {
			closeUpList.add(RealtimeElementTypeEnum.REALTIME_FRONT.getCode());
			closeUpList.add(RealtimeElementTypeEnum.REALTIME_RIGHT.getCode());
			closeUpList.add(RealtimeElementTypeEnum.REALTIME_BACK.getCode());
			closeUpList.add(RealtimeElementTypeEnum.REALTIME_LEFT.getCode());
		} else if (REPORT_TYPE == ReportTypeEnum.INFRARED_DAMAGE_REPORT) {
			closeUpList.add(RealtimeElementTypeEnum.INFRARED_DAMAGE_CLOSE_UP.getCode());
		} else if (REPORT_TYPE == ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT) {
			closeUpList.add(RealtimeElementTypeEnum.ROOF_ONLY_UNDERWRITING_CLOSE_UP.getCode());
		} else if (REPORT_TYPE == ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT) {
			closeUpList.add(RealtimeElementTypeEnum.FULL_SCOPE_UNDERWRITING_CLOSE_UP.getCode());
		} else if (REPORT_TYPE == ReportTypeEnum.PROPERTY_IMAGE_REPORT) {
			closeUpList.add(RealtimeElementTypeEnum.APP_DAMAGE_FRONT.getCode());
			closeUpList.add(RealtimeElementTypeEnum.APP_DAMAGE_RIGHT.getCode());
			closeUpList.add(RealtimeElementTypeEnum.APP_DAMAGE_BACK.getCode());
			closeUpList.add(RealtimeElementTypeEnum.APP_DAMAGE_LEFT.getCode());
		} else {
			closeUpList.add(RealtimeElementTypeEnum.FRONT.getCode());
			closeUpList.add(RealtimeElementTypeEnum.RIGHT.getCode());
			closeUpList.add(RealtimeElementTypeEnum.BACK.getCode());
			closeUpList.add(RealtimeElementTypeEnum.LEFT.getCode());
		}
		return closeUpList;
	}

	public static int getOrientation(int elementCode) {
		int orientation = 0;
		if (elementCode == RealtimeElementTypeEnum.FRONT.getCode()
				|| elementCode == RealtimeElementTypeEnum.REALTIME_FRONT.getCode()
				|| elementCode == RealtimeElementTypeEnum.APP_DAMAGE_FRONT.getCode()) {
			orientation = OrientationEnum.FRONT.getCode();
		} else if (elementCode == RealtimeElementTypeEnum.RIGHT.getCode()
				|| elementCode == RealtimeElementTypeEnum.REALTIME_RIGHT.getCode()
				|| elementCode == RealtimeElementTypeEnum.APP_DAMAGE_RIGHT.getCode()) {
			orientation = OrientationEnum.RIGHT.getCode();
		} else if (elementCode == RealtimeElementTypeEnum.BACK.getCode()
				|| elementCode == RealtimeElementTypeEnum.REALTIME_BACK.getCode()
				|| elementCode == RealtimeElementTypeEnum.APP_DAMAGE_BACK.getCode()) {
			orientation = OrientationEnum.BACK.getCode();
		} else if (elementCode == RealtimeElementTypeEnum.LEFT.getCode()
				|| elementCode == RealtimeElementTypeEnum.REALTIME_LEFT.getCode()
				|| elementCode == RealtimeElementTypeEnum.APP_DAMAGE_LEFT.getCode()) {
			orientation = OrientationEnum.LEFT.getCode();
		}
		return orientation;
	}

	public static RealtimeElementTypeEnum getEnum(int code){
        return getElementTypeEnum(element -> element.code == code);
	}

	public static boolean hasImagePackage(int reportType) {
		RealtimeElementTypeEnum[] elementTypes = RealtimeElementTypeEnum.values();
		for(RealtimeElementTypeEnum elementType: elementTypes){
			if (elementType.getReportType().getCode() == reportType && elementType.getIsPackage()) {
				return true;
			}
		}
		return false;
	}

    public static RealtimeElementTypeEnum getCloseUp(int reportType) {
        return getElementTypeEnum(element -> element.getReportType().getCode() == reportType
            && element.getBaseType() == RealtimeElementBaseEnum.CLOSE_UP);
    }

    public static RealtimeElementTypeEnum getElementTypeEnum(Function<RealtimeElementTypeEnum, Boolean> check) {
        RealtimeElementTypeEnum[] elementTypes = RealtimeElementTypeEnum.values();
        RealtimeElementTypeEnum resultType = null;
        for(RealtimeElementTypeEnum elementType: elementTypes){
            if(check.apply(elementType)){
                resultType = elementType;
                break;
            }
        }
        return resultType;
    }

}
