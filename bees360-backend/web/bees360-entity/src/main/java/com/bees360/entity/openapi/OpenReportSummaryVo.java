package com.bees360.entity.openapi;

import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OpenReportSummaryVo {
    /**
     * 报告的id
     *
     * @see ProjectReportFile#getReportId()
     */
    private String id;
    /**
     * 报告类型名
     *
     * @see ProjectReportFile#getReportType()
     * @see ReportTypeEnum#getDisplay()
     */
    private String type;
    private OpenProjectReportSummaryVo project;
    private ReportSummaryVo summary;
}
