package com.bees360.entity.dto;

import java.math.BigDecimal;

import com.bees360.entity.enums.RoleEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CIIDto {
	private Long pilot;
	private Long adjuster;
	private Long reviewer;
	private Long processor;

	private Long inspectionTime;
	private String inspectionNumber;

	private Double claimRcv;
	private Double claimAcv;

	public static RoleEnum[] roles() {
		return new RoleEnum[]{RoleEnum.PILOT, RoleEnum.ADJUSTER, RoleEnum.REVIEWER, RoleEnum.PROCESSOR};
	}

	public Long getWorkerId(RoleEnum role) {
		switch(role) {
		case PILOT: {
			return getPilot();
		}
		case ADJUSTER: {
			return getAdjuster();
		}
		case REVIEWER: {
			return getReviewer();
		}
		case PROCESSOR: {
			return getProcessor();
		}
		default:
			return null;
		}
	}

	@Override
	public String toString() {
		return "CIIDto [pilot=" + pilot + ", adjuster=" + adjuster + ", reviewer=" + reviewer + ", processor="
				+ processor + ", inspectionTime=" + inspectionTime + ", claimRcv=" + claimRcv + ", claimAcv=" + claimAcv
				+ "]";
	}
}
