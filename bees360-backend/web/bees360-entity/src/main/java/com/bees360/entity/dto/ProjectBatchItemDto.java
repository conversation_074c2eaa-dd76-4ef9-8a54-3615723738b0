package com.bees360.entity.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.bees360.entity.Project;

import lombok.Data;
import org.springframework.beans.BeanUtils;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/12/19 15:53
 */
@Data
public class ProjectBatchItemDto extends ProjectBaseDto {

    /**
     * project在列表中的下标，由前端生成
     */
    @NotNull
    private Integer idx;

    /**
     * 保险公司名称，需要转化为 {@link Project#getInsuranceCompany()} ()}
     */
    @NotNull
    private String insuredBy = "";
    /**
     * 公司名称，需要转化为 {@link Project#getRepairCompany()}
     */
    @NotNull
    private String processedBy = "";
    /**
     * Adjuster 的 Email
     */
    @NotNull
    private String adjuster = "";
    /**
     * Pilot 的 Email
     */
    @NotNull
    private String pilot = "";
    /**
     * Visitors 的 Email 列表
     */
    @NotNull
    private List<String> visitors = new ArrayList<>();

    // =======================================
    // Project 属性

    /**
     * @see Project#isNeedPilot()
     */
    private boolean needPilot;
    /**
     * @see Project#getCustomizedParams()
     */
    private Map<String, Object> customizedParams;

    @Override
    public Project toProject() {
        Project project = new Project();
        BeanUtils.copyProperties(this, project);
        return project;
    }
}
