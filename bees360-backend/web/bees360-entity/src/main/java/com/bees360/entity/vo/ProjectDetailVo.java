package com.bees360.entity.vo;

import com.bees360.entity.dto.StringIdNameDto;
import java.time.LocalDate;
import java.util.List;

import com.bees360.entity.enums.NewProjectStatusEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ProjectDetailVo {
	private long projectId;
	private String state;
	private String city;
	private String address;
	private String zipCode;
	private String assetOwnerName;
	private String assetOwnerPhone;
	private String assetOwnerEmail;

	// Pilot
	private boolean needPilot;
	private boolean hasPilot;
	private Long inspectionTime;
	private long createdTime;

	// Images
	private List<String> images;
	private int imagesTotal;

	// Estimate
	private Integer claimType;
	private Integer projectType;
	private int damageSeverity;

	// Quick Square
	private double roofArea;
	private double roofPerimeter;
	private int primaryPitch;
	private int stories;

	private Integer projectStatus;

	private List<UserTinyVo> members;

	// Reports
	private List<StringIdNameDto> reports;

	private Integer daysOld;

	private Integer serviceType;

	private LocalDate policyEffectiveDate;

	private String policyNumber;

	private int flyZoneType;

	private String insuredBy;

	private String batchNo;

	@JsonIgnore
	public Integer getProjectStatus() {
		return projectStatus;
	}

	public Integer getProjectStatusCode() {
		NewProjectStatusEnum status = NewProjectStatusEnum.getEnum(projectStatus);
		return status == null? null: status.getCode();
	}

	public String getProjectStatusName() {
		NewProjectStatusEnum status = NewProjectStatusEnum.getEnum(projectStatus);
		return status == null? "": status.getDisplay();
	}

}
