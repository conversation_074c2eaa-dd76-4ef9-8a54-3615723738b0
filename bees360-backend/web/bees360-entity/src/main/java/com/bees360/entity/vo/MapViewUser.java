package com.bees360.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class MapViewUser {
	private long userId;
	private String firstName;
	private String lastName;
	private String avatar;
	private String email;
	private String phone;
	private long roles;
	private long companyId;
	private String companyName;
	private String companyLogo;
	private Double lng;
	private Double lat;
	private double travelRadius;
	private Double distance;

	public String getName() {
		return (firstName + " " + lastName).trim();
	}

	//* getter and setter *//
	public long getUserId() {
		return userId;
	}
	public void setUserId(long userId) {
		this.userId = userId;
	}
	@JsonIgnore
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	@JsonIgnore
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public String getAvatar() {
		return avatar;
	}
	public void setAvatar(String avatar) {
		this.avatar = avatar;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public long getRoles() {
		return roles;
	}
	public void setRoles(long roles) {
		this.roles = roles;
	}
	public long getCompanyId() {
        return companyId;
    }
    public void setCompanyId(long companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getCompanyLogo() {
		return companyLogo;
	}
	public void setCompanyLogo(String companyLogo) {
		this.companyLogo = companyLogo;
	}
	public Double getLng() {
		return lng;
	}
	public void setLng(Double lng) {
		this.lng = lng;
	}
	public Double getLat() {
		return lat;
	}
	public void setLat(Double lat) {
		this.lat = lat;
	}
	public double getTravelRadius() {
		return travelRadius;
	}
	public void setTravelRadius(double travelRadius) {
		this.travelRadius = travelRadius;
	}
	public Double getDistance() {
		return distance;
	}
	public void setDistance(Double distance) {
		this.distance = distance;
	}

    @Override
    public String toString() {
        return "MapViewUser [userId=" + userId + ", firstName=" + firstName + ", lastName=" + lastName + ", avatar="
            + avatar + ", email=" + email + ", phone=" + phone + ", roles=" + roles + ", companyId=" + companyId
            + ", companyName=" + companyName + ", companyLogo=" + companyLogo + ", lng=" + lng + ", lat=" + lat
            + ", travelRadius=" + travelRadius + ", distance=" + distance + "]";
    }
}
