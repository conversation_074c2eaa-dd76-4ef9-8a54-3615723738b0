package com.bees360.entity.vo;

import java.io.Serial;
import java.io.Serializable;

public class ProjectImageDirectionVo implements Serializable{

    @Serial
    private static final long serialVersionUID = -2098667689986732344L;

	private String imageId;
    // The direction of this image in a house,
	// it must be assigned a value through the Enum DirectionEnum.
	private int direction;
	private String fileNameLowerResolution;

	public ProjectImageDirectionVo() {
	}

	public ProjectImageDirectionVo(String imageId, int direction, String fileNameLowerResolution) {
		this.imageId = imageId;
		this.direction = direction;
		this.fileNameLowerResolution = fileNameLowerResolution;
	}

	public String getLowerResolutionImageS3Key(){
	    return fileNameLowerResolution;
    }

	public String getImageId() {
		return imageId;
	}
	public void setImageId(String imageId) {
		this.imageId = imageId;
	}
	public String getFileNameLowerResolution() {
		return fileNameLowerResolution;
	}
	public void setFileNameLowerResolution(String fileNameLowerResolution) {
		this.fileNameLowerResolution = fileNameLowerResolution;
	}
	public int getDirection() {
		return direction;
	}
	public void setDirection(int direction) {
		this.direction = direction;
	}

}
