package com.bees360.entity.openapi.client;

import com.bees360.entity.enums.openapi.GrantType;
import lombok.Builder;
import lombok.Data;

import java.util.Set;

@Data
@Builder
public class OpenApiClientSecret {

    private String clientName;
    private String clientId;
    private String clientSecret;
    private Long accessTokenValidateSeconds;
    private Long refreshTokenValidateSeconds;
    private Set<String> scopes;
    private Set<GrantType> grantTypes;

    public OpenApiClient toOpenApiClient(){
        final OpenApiClient client = new OpenApiClient();
        client.setClientName(this.clientName);
        client.setClientId(this.clientId);
        client.setClientSecret(this.getClientSecret());
        client.setAccessTokenValidateSeconds(this.accessTokenValidateSeconds);
        client.setRefreshTokenValidateSeconds(this.refreshTokenValidateSeconds);
        client.setGrantType(GrantType.getCode(this.grantTypes));
        client.setScopes(scopes);

        return client;
    }
}
