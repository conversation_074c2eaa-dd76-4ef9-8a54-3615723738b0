package com.bees360.entity.dto;

import java.util.List;

import com.bees360.entity.CustomizedSIBCategory;

public class CustomizedSIBCategoryDto extends CustomizedSIBCategory {

	private List<CustomizedSIBCategory> subCategory;

	private List<CustomizedSIBCategory> description;

	public CustomizedSIBCategoryDto() {
		super();
	}

	public CustomizedSIBCategoryDto(CustomizedSIBCategory category) {
		super();
		this.setId(category.getId());
		this.setCategoryType(category.getCategoryType());
		this.setCategory(category.getCategory());
		this.setParentId(category.getParentId());
	}

	public List<CustomizedSIBCategory> getSubCategory() {
		return subCategory;
	}

	public void setSubCategory(List<CustomizedSIBCategory> subCategory) {
		this.subCategory = subCategory;
	}

	public List<CustomizedSIBCategory> getDescription() {
		return description;
	}

	public void setDescription(List<CustomizedSIBCategory> description) {
		this.description = description;
	}

}
