package com.bees360.entity;

import java.io.Serializable;

public class Location implements Serializable{
	private String address;
	private String city;
	private String state;
	private String country;
	private String zipCode;
	private double longitude;
	private double latitude;

//	Getter and Setter
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}
	public String getZipCode() {
		return zipCode;
	}
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}
	public double getLongitude() {
		return longitude;
	}
	public void setLongitude(double longitude) {
		this.longitude = longitude;
	}
	public double getLatitude() {
		return latitude;
	}
	public void setLatitude(double latitude) {
		this.latitude = latitude;
	}

	@Override
	public String toString() {
		return "Location [address=" + address + ", city=" + city + ", state=" + state + ", country=" + country
				+ ", zipCode=" + zipCode + ", longitude=" + longitude + ", latitude=" + latitude + "]";
	}
}
