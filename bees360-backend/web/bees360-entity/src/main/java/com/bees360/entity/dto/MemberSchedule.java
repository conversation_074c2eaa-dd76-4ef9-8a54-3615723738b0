package com.bees360.entity.dto;

import java.util.List;
import java.util.Map;

public class MemberSchedule {

	private List<MemberScheduleItem> members;

	public static class MemberScheduleItem {
		private Long userId;
		private int roleId;
		private Map<String, Object> extendParams;

		public Long getUserId() {
			return userId;
		}
		public void setUserId(Long userId) {
			this.userId = userId;
		}
		public int getRoleId() {
			return roleId;
		}
		public void setRoleId(int roleId) {
			this.roleId = roleId;
		}
		public Map<String, Object> getExtendParams() {
			return extendParams;
		}
		public void setExtendParams(Map<String, Object> extendParams) {
			this.extendParams = extendParams;
		}

		@Override
		public String toString() {
			return "MemberScheduleItem [userId=" + userId + ", role=" + roleId + ", extendParams=" + extendParams + "]";
		}
	}

	public List<MemberScheduleItem> getMembers() {
		return members;
	}

	public void setMembers(List<MemberScheduleItem> members) {
		this.members = members;
	}

	@Override
	public String toString() {
		return "MemberSchedule [members=" + members + "]";
	}
}
