package com.bees360.entity.dto;

import java.util.List;

public class ProductPurchase {
	private List<Integer> productIds;
	private int discountType;
	private double price;
	private int paymentMethod;
	private String paymentToken;

	//** getter and setter **//
	public List<Integer> getProductIds() {
		return productIds;
	}
	public void setProductIds(List<Integer> productIds) {
		this.productIds = productIds;
	}
	public int getDiscountType() {
		return discountType;
	}
	public void setDiscountType(int discountType) {
		this.discountType = discountType;
	}
	public double getPrice() {
		return price;
	}
	public void setPrice(double price) {
		this.price = price;
	}
	public int getPaymentMethod() {
		return paymentMethod;
	}
	public void setPaymentMethod(int paymentMethod) {
		this.paymentMethod = paymentMethod;
	}
	public String getPaymentToken() {
		return paymentToken;
	}
	public void setPaymentToken(String paymentToken) {
		this.paymentToken = paymentToken;
	}

	@Override
	public String toString() {
		return "ProductPurchase [productIds=" + productIds + ", discountType=" + discountType + ", price=" + price
				+ ", paymentMethod=" + paymentMethod + ", paymentToken=" + paymentToken + "]";
	}
}
