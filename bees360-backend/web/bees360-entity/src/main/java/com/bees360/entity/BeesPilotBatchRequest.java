package com.bees360.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * BeesPilotStatus
 * <AUTHOR>
 * @since 2020/07/31
 */
@Data
public class BeesPilotBatchRequest {

    private BigDecimal basePay;

    private BigDecimal extraPay;

    private LocalDate planPaymentDate;

    private String note;

    private List<Long> projectIds;

    private Long projectId;

    private String batchNo;

    private LocalDate dueDate;

    private Long inspectionTime;
    /**
     * 是否默认飞手已确认接受
     */
    private Boolean isPendingAcceptance=false;

    /**
     * @see com.bees360.entity.firebase.BatchStatusEnum
     */
    @JsonIgnore
    private Integer status;

    @JsonIgnore
    private Boolean deleted;
}
