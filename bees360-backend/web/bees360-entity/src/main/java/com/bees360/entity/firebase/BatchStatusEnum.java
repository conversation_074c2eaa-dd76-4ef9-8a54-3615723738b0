package com.bees360.entity.firebase;

import lombok.Getter;

import java.util.Objects;

public enum  BatchStatusEnum {
    /**
     * 等待飞手确认Batch
     */
    PENDING("Pending", 1),

    /**
     * 飞手拒绝Batch
     */
    REJECTED( "Rejected", 2),

    /**
     * 飞手接受该Batch， 任务开始
     */
    ACCEPTED( "Accepted", 0);

    BatchStatusEnum(String value, int status) {
        this.status = status;
        this.value = value;
    }

    @Getter
    final private String value;
    @Getter
    final private int status;

    public static BatchStatusEnum getEnum(int status) {
        for (BatchStatusEnum batchStatusEnum : values()) {
            if (batchStatusEnum.getStatus() == status) {
                return batchStatusEnum;
            }
        }
        return ACCEPTED;
    }

    public static BatchStatusEnum getEnum(String status) {
        for (BatchStatusEnum batchStatusEnum : values()) {
            if (Objects.equals(batchStatusEnum.getValue(), status)) {
                return batchStatusEnum;
            }
        }
        return ACCEPTED;
    }
}
