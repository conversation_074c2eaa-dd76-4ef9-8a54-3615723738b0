package com.bees360.entity.stat.converter;

import com.bees360.entity.stat.dto.ProjectListDataDto;
import com.bees360.entity.stat.rule.CompanyStatRule;
import com.bees360.entity.stat.rule.CompanyStatRuleConfig;
import com.bees360.entity.stat.vo.list.ProjectCompletedListVo;
import com.bees360.entity.stat.vo.list.ProjectCreatedListVo;
import com.bees360.entity.stat.vo.list.ProjectIncompletedListVo;
import com.bees360.entity.stat.vo.list.ProjectRiskScoreListVo;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.Temporal;
import java.time.temporal.TemporalField;

public class StatListConverter {

    public static ProjectCreatedListVo toCreatedVo(ProjectListDataDto listDto){
        final ProjectCreatedListVo vo = new ProjectCreatedListVo();

        vo.setProjectId(listDto.getProjectId());
        vo.setPolicyNumber(listDto.getPolicyNumber());
        vo.setAddress(assembleAddress(listDto));
        vo.setInsuredName(listDto.getAssertOwnerName());
        vo.setInsuredPhone(listDto.getAssertOwnerPhone());
        vo.setDaysOld(listDto.getDaysOld());
        vo.setNewDaysOld(listDto.getNewDaysOld());
        vo.setLevel(daysOldLevel(listDto));
        return vo;
    }


    public static ProjectIncompletedListVo toIncmpletedVo(ProjectListDataDto listDto){
        final ProjectIncompletedListVo vo = new ProjectIncompletedListVo();

        vo.setProjectId(listDto.getProjectId());
        vo.setPolicyNumber(listDto.getPolicyNumber());
        vo.setAddress(assembleAddress(listDto));
        vo.setInsuredName(listDto.getAssertOwnerName());
        vo.setInsuredPhone(listDto.getAssertOwnerPhone());
        vo.setDaysOld(listDto.getDaysOld());
        vo.setLevel(daysOldLevel(listDto));
        vo.setNewDaysOld(listDto.getNewDaysOld());
        return vo;
    }


    public static ProjectCompletedListVo toCompletedVo(ProjectListDataDto listDto){
        final ProjectCompletedListVo vo = new ProjectCompletedListVo();

        vo.setProjectId(listDto.getProjectId());
        vo.setPolicyNumber(listDto.getPolicyNumber());
        vo.setAddress(assembleAddress(listDto));
        vo.setInsuredName(listDto.getAssertOwnerName());
        vo.setInsuredPhone(listDto.getAssertOwnerPhone());
        vo.setInspectedDate(listDto.getDateInspected());
        Long dateReturned = listDto.getDateReturned();
        LocalDate policyEffectiveDate = listDto.getPolicyEffectiveDate();
        Long createTime = listDto.getCreateTime();
        vo.setReturnedDate(dateReturned);
        vo.setPolicyEffectiveDate(policyEffectiveDate);
        vo.setCreatedDate(createTime);
        long days = (dateReturned - createTime)/1000/86400;
        if (policyEffectiveDate != null){
            days = Duration.between(
                policyEffectiveDate.atTime(0, 0, 0, 0).toInstant(ZoneOffset.UTC),
                Instant.ofEpochMilli(dateReturned)).toDays();
        }
        vo.setTurnaroundTimes(days < 0 ? 0 : days);

        return vo;
    }

    public static ProjectRiskScoreListVo toRiskScoreVo(ProjectListDataDto dto){

        final ProjectRiskScoreListVo vo = new ProjectRiskScoreListVo();

        vo.setProjectId(dto.getProjectId());
        vo.setPolicyNumber(dto.getPolicyNumber());
        vo.setAddress(assembleAddress(dto));
        vo.setInsuredName(dto.getAssertOwnerName());
        vo.setInsuredPhone(dto.getAssertOwnerPhone());
        vo.setScore(0D);
        if (dto.getScore() != null){
            vo.setScore(dto.getScore().doubleValue());
        }
        return vo;

    }
    private static String assembleAddress(ProjectListDataDto dto){

        String format = "%s, %s, %s %s";

        return format.formatted(dto.getAddress(), dto.getCity(), dto.getState(), dto.getZipCode());
    }

    private static Integer daysOldLevel(ProjectListDataDto dto){

        CompanyStatRule statRule = CompanyStatRuleConfig.getStatRule(dto.getInsuranceCompanyName(), dto.getServiceType());
        if (statRule.isDueSoon(dto.getNewDaysOld())){
            return 2;
        }
        if (statRule.isOverdue(dto.getNewDaysOld())){
            return 3;
        }
        return 1;
    }
}
