package com.bees360.entity.enums;

public enum HouseCategoryVersionEnum implements BaseCodeEnum{
	CATEGORY(1,"Category version");

	public static final int DEFAULT = -1;

	private final int code;
	private final String display;

	HouseCategoryVersionEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

}
