package com.bees360.entity.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DamageReportElementTypeEnum implements BaseCodeEnum {
	OVERVIEW(1, "Overview"),
	SLOPE(2, "Slope"),
	CLOSE_UP(3, "Close up"),
	CROP(4, "Crop");

	private final int code;
	private final String display;

	DamageReportElementTypeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public static DamageReportElementTypeEnum getEnum(int code){
		DamageReportElementTypeEnum[] damageReportElementTypes = DamageReportElementTypeEnum.values();
		for(DamageReportElementTypeEnum damageReportElementType: damageReportElementTypes){
			if(damageReportElementType.code == code){
				return damageReportElementType;
			}
		}
		return null;
	}
}
