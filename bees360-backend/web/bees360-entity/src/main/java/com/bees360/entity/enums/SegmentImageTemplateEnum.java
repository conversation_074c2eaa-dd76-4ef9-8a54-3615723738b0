package com.bees360.entity.enums;

import java.util.ArrayList;
import java.util.List;

import com.bees360.entity.dto.AppSegmentDto;

public enum SegmentImageTemplateEnum {
	MAIN_ROOF_LAYERS(30, "Roof Layer", 50, 10, 222200, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_DRIP_EDGE(20, "Drip Edge", 50, 10, 178990, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF, SegmentCategoryTypeEnum.ROOF_DRIP_EDGE, true),
	MAIN_ROOF_SHINGLE_GAUGE(57, "Shingle gauge", 50, 10, 222220, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF, SegmentCategoryTypeEnum.UNMODIFIABLE, false),
	MAIN_ROOF_RIDGE_CAP(40, "Ridge Cap", 50, 10, 180960, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF, SegmentCategoryTypeEnum.ROOF_RIDGE_CAP, false),
	MAIN_ROOF_OVERVIEW(10, "Valley", 50, 10, 182930, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF, SegmentCategoryTypeEnum.ROOF_VALLEY, false),
	MAIN_ROOF_SHINGLE_SIZE(55, "High profile ridge cap", 50, 10, 180970, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF, SegmentCategoryTypeEnum.ROOF_RIDGE_CAP, false),

	MAIN_ROOF_FRONT(60, "Front overview", 55, 10, 177460, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_FRONT_TEST_SQUARE(70, "Test Square", 55, 10, 222230, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_FRONT_CLOSE_UP_ONE(80, "Roof closeups", 55, 10, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_FRONT_CLOSE_UP_TWO(90, "Roof closeups", 55, 10, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	MAIN_ROOF_RIGHT(120, "Right overview", 60, 10, 177500, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_RIGHT_TEST_SQUARE(130, "Test Square", 60, 10, 222230, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_RIGHT_CLOSE_UP_ONE(140, "Roof closeups", 60, 10, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_RIGHT_CLOSE_UP_TWO(150, "Roof closeups", 60, 10, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	MAIN_ROOF_BACK(180, "Back overview", 65, 10, 177500, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_BACK_TEST_SQUARE(190, "Test Square", 65, 10, 222230, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_BACK_CLOSE_UP_ONE(200, "Roof closeups", 65, 10, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_BACK_CLOSE_UP_TWO(210, "Roof closeups", 65, 10, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	MAIN_ROOF_LEFT(240, "Left overview", 70, 10, 177500, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_LEFT_TEST_SQUARE(250, "Test Square", 70, 10, 222230, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_LEFT_CLOSE_UP_ONE(260, "Roof closeups", 70, 10, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ROOF_LEFT_CLOSE_UP_TWO(270, "Roof closeups", 70, 10, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	MAIN_ELEVATION_ADDRESS(300, "Address verification", 75, 10, 222330, SegmentValueTypeEnum.ADDRESS_IMAGES, SegmentCodeTypeEnum.ELEVATION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ELEVATION_OVERVIEW(310, "Risk overview", 75, 10, 222340, SegmentValueTypeEnum.OVERVIEW_RISK, SegmentCodeTypeEnum.ELEVATION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ELEVATION_FRONT(320, "Front overview", 80, 10, 222350, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ELEVATION_RIGHT(330, "Right overview", 85, 10, 222360, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ELEVATION_BACK(340, "Back overview", 90, 10, 222370, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	MAIN_ELEVATION_LEFT(350, "Left overview", 95, 10, 222380, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	GAZEBO_ROOF_LAYERS(510, "Roof Layer", 50, 15, 222200, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	GAZEBO_ROOF_FRONT(520, "Front overview", 55, 15, 177460, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GAZEBO_ROOF_FRONT_CLOSE_UP_ONE(530, "Roof closeups", 55, 15, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GAZEBO_ROOF_FRONT_CLOSE_UP_TWO(540, "Roof closeups", 55, 15, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	GAZEBO_ROOF_RIGHT(550, "Right overview", 60, 15, 177500, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GAZEBO_ROOF_RIGHT_CLOSE_UP_ONE(560, "Roof closeups", 60, 15, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GAZEBO_ROOF_RIGHT_CLOSE_UP_TWO(570, "Roof closeups", 60, 15, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	GAZEBO_ROOF_BACK(580, "Back overview", 65, 15, 177500, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GAZEBO_ROOF_BACK_CLOSE_UP_ONE(590, "Roof closeups", 65, 15, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GAZEBO_ROOF_BACK_CLOSE_UP_TWO(600, "Roof closeups", 65, 15, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	GAZEBO_ROOF_LEFT(610, "Left overview", 70, 15, 177500, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GAZEBO_ROOF_LEFT_CLOSE_UP_ONE(620, "Roof closeups", 70, 15, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GAZEBO_ROOF_LEFT_CLOSE_UP_TWO(630, "Roof closeups", 70, 15, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	GAZEBO_ELEVATION_OVERVIEW(640, "Risk overview", 75, 15, 222340, SegmentValueTypeEnum.OVERVIEW_RISK, SegmentCodeTypeEnum.ELEVATION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GAZEBO_ELEVATION_FRONT(650, "Front overview", 80, 15, 222350, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GAZEBO_ELEVATION_RIGHT(660, "Right overview", 85, 15, 222360, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GAZEBO_ELEVATION_BACK(670, "Back overview", 90, 15, 222370, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GAZEBO_ELEVATION_LEFT(680, "Left overview", 95, 15, 222380, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	GARAGE_FRONT(370, "Front overview", 80, 20, 222350, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GARAGE_RIGHT(380, "Right overview", 85, 20, 222360, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GARAGE_BACK(390, "Back overview", 90, 20, 222370, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	GARAGE_LEFT(400, "Left overview", 95, 20, 222380, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	STORAGE_FRONT(410, "Front overview", 80, 25, 222350, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	STORAGE_RIGHT(420, "Right overview", 85, 25, 222360, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	STORAGE_BACK(430, "Back overview", 90, 25, 222370, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	STORAGE_LEFT(440, "Left overview", 95, 25, 222380, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	BARN_ROOF_LAYERS(710, "Roof Layer", 50, 30, 222200, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ROOF_TYPE(715, "Roof Type", 50, 30, 222200, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF, SegmentCategoryTypeEnum.UNMODIFIABLE, false),

	BARN_ROOF_FRONT(720, "Front overview", 55, 30, 177460, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ROOF_FRONT_CLOSE_UP_ONE(730, "Roof closeups", 55, 30, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ROOF_FRONT_CLOSE_UP_TWO(740, "Roof closeups", 55, 30, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	BARN_ROOF_RIGHT(750, "Right overview", 60, 30, 177500, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ROOF_RIGHT_CLOSE_UP_ONE(760, "Roof closeups", 60, 30, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ROOF_RIGHT_CLOSE_UP_TWO(770, "Roof closeups", 60, 30, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	BARN_ROOF_BACK(780, "Back overview", 65, 30, 177500, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ROOF_BACK_CLOSE_UP_ONE(790, "Roof closeups", 65, 30, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ROOF_BACK_CLOSE_UP_TWO(800, "Roof closeups", 65, 30, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	BARN_ROOF_LEFT(810, "Left overview", 70, 30, 177500, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ROOF_LEFT_CLOSE_UP_ONE(820, "Roof closeups", 70, 30, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ROOF_LEFT_CLOSE_UP_TWO(830, "Roof closeups", 70, 30, 222210, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ROOF_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	BARN_ELEVATION_OVERVIEW(840, "Risk overview", 75, 30, 222340, SegmentValueTypeEnum.OVERVIEW_RISK, SegmentCodeTypeEnum.ELEVATION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ELEVATION_FRONT(850, "Front overview", 80, 30, 222350, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ELEVATION_RIGHT(860, "Right overview", 85, 30, 222360, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ELEVATION_BACK(870, "Back overview", 90, 30, 222370, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	BARN_ELEVATION_LEFT(880, "Left overview", 95, 30, 222380, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	FENCE_OVERVIEW(460, "Overview", 75, 35, 101290, SegmentValueTypeEnum.OVERVIEW_RISK, SegmentCodeTypeEnum.ELEVATION, SegmentCategoryTypeEnum.FENCE_OVERVIEW, true),
	FENCE_CLOSEUP(465, "Close up", 75, 35, 222320, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	OTHER_FRONT(470, "Front overview", 80, 40, 222350, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	OTHER_RIGHT(480, "Right overview", 85, 40, 222360, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	OTHER_BACK(490, "Back overview", 90, 40, 222370, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),
	OTHER_LEFT(500, "Left overview", 95, 40, 222380, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION, SegmentCategoryTypeEnum.UNMODIFIABLE, true),

	DAMAGE_OVERVIEW(0, "Overview", 0, 300, 222310, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION_CODE, SegmentCategoryTypeEnum.ELEVATION, true),
	DAMAGE_CLOSEUP(1, "Close up", 0, 300, 222320, SegmentValueTypeEnum.SYS_DIRECTION_IMAGES, SegmentCodeTypeEnum.ELEVATION_DIRECTION_CODE, SegmentCategoryTypeEnum.UNMODIFIABLE, true)
	;

	private final int code;
	private final String display;
	private final long segmentType;
	private final long category;
	private final long descriptionId;
	private final SegmentValueTypeEnum valueType;
	private final SegmentCodeTypeEnum codeType;
	private final SegmentCategoryTypeEnum categoryType;
	private final boolean isRequired;

	SegmentImageTemplateEnum(int code, String display, long segmentType, long category,
							 long descriptionId, SegmentValueTypeEnum valueType, SegmentCodeTypeEnum codeType,
							 SegmentCategoryTypeEnum categoryType, boolean isRequired) {
		this.code = code;
		this.display = display;
		this.segmentType = segmentType;
		this.category = category;
		this.descriptionId = descriptionId;
		this.valueType = valueType;
		this.codeType = codeType;
		this.categoryType = categoryType;
		this.isRequired = isRequired;
	}

	public static SegmentImageTemplateEnum getEnum(int code) {
		for(SegmentImageTemplateEnum templateEnum : SegmentImageTemplateEnum.values()) {
			if(code == templateEnum.getCode()) {
				return templateEnum;
			}
		}
		return null;
	}

	public static SegmentImageTemplateEnum getEnum(String display) {
		for(SegmentImageTemplateEnum templateEnum : SegmentImageTemplateEnum.values()) {
			if(display.equals(templateEnum.getDisplay())) {
				return templateEnum;
			}
		}
		return null;
	}

	public static List<SegmentImageTemplateEnum> getEnum(long category) {
		List<SegmentImageTemplateEnum> templateList = new ArrayList<>();
		for(SegmentImageTemplateEnum templateEnum : SegmentImageTemplateEnum.values()) {
			if(category == templateEnum.getCategory()) {
				templateList.add(templateEnum);
			}
		}
		return templateList;
	}

	// All conditions are met in the main structure.
	public static SegmentImageTemplateEnum getEnumBySegmentType(long segmentType) {
		for(SegmentImageTemplateEnum templateEnum : SegmentImageTemplateEnum.values()) {
			if(segmentType == templateEnum.getSegmentType()) {
				return templateEnum;
			}
		}
		return null;
	}

	public static SegmentImageTemplateEnum getEnumByCategory(long category) {
		for(SegmentImageTemplateEnum templateEnum : SegmentImageTemplateEnum.values()) {
			if(category == templateEnum.getCategory()) {
				return templateEnum;
			}
		}
		return null;
	}

	public static List<SegmentImageTemplateEnum> listAdditionalEnums() {
		List<SegmentImageTemplateEnum> templateList = new ArrayList<>();
		for(SegmentImageTemplateEnum templateEnum : SegmentImageTemplateEnum.values()) {
			if(0 == templateEnum.getSegmentType()) {
				templateList.add(templateEnum);
			}
		}
		return templateList;
	}

	public static boolean isInRoof(long segmentType) {
		return segmentType == MAIN_ROOF_OVERVIEW.getSegmentType() || segmentType == MAIN_ROOF_FRONT.getSegmentType()
				|| segmentType == MAIN_ROOF_BACK.getSegmentType() || segmentType == MAIN_ROOF_LEFT.getSegmentType()
				|| segmentType == MAIN_ROOF_RIGHT.getSegmentType();
	}

	/**
	 * get category hot type
	 * @param code
	 * @return
	 */
	public static int getCategoryType(String code) {
		String[] codes = code.split(AppSegmentDto.SEGMENT_CODE_SPLIT);
		if (codes.length < 4) {
			return SegmentCategoryTypeEnum.ELEVATION.getCode();
		}
		if (codes.length == 4) {
			SegmentImageTemplateEnum template = getEnumBySegmentType(Long.parseLong(codes[2]));
			if (template != null) {
				return template.getCategoryType().getCode();
			}
		}
		if (codes.length == 6) {
			SegmentImageTemplateEnum template = getEnumByCategory(Long.parseLong(codes[3]));
			if (template != null) {
				return template.getCategoryType().getCode();
			}
		}
		if (codes.length >= 6) {
			return SegmentCategoryTypeEnum.UNMODIFIABLE.getCode();
		} else if (isInRoof(Long.parseLong(codes[2]))) {
			return SegmentCategoryTypeEnum.ROOF.getCode();
		} else {
			return SegmentCategoryTypeEnum.ELEVATION.getCode();
		}
	}

	public int getCode() {
		return code;
	}

	public String getDisplay() {
		return display;
	}

	public long getSegmentType() {
		return segmentType;
	}

	public long getCategory() {
		return category;
	}

	public SegmentValueTypeEnum getValueType() {
		return valueType;
	}

	public SegmentCodeTypeEnum getCodeType() {
		return codeType;
	}

	public boolean getIsRequired() {
		return false;
	}

	public long getDescriptionId() {
		return descriptionId;
	}

	public SegmentCategoryTypeEnum getCategoryType() {
		return categoryType;
	}

}
