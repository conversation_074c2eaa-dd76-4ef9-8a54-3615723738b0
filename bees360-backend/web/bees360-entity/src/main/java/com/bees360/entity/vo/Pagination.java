package com.bees360.entity.vo;

/**
 * <AUTHOR>
 * @contributors
 * @date Apr 12, 2018 3:16:56 PM
 */
public class Pagination {
	private int pageIndex;
	private int pageSize;
	private long totalPage;
	private long sum;

	private final static int DEFAULT_START_INDEX = 1;

	public Pagination() {}

	public Pagination(int pageIndex, int pageSize) {
		this.pageIndex = pageIndex;
		this.pageSize = pageSize;
	}

	public Pagination(int pageIndex, int pageSize, int sum) {
		this.pageIndex = pageIndex;
		this.pageSize = pageSize;
		setSumAndGenaratePages(sum);
	}

	public Pagination(int pageIndex, int pageSize, int totalPage, int sum) {
		this.pageIndex = pageIndex;
		this.pageSize = pageSize;
		this.totalPage = totalPage;
		this.sum = sum;
	}

	public void setSumAndGenaratePages(long sum){
		this.sum = sum;
		genarateTotalPage();
	}

	public void genarateTotalPage() {
		if(pageSize == 0) {
			this.totalPage = 0;
			return;
		}
		this.totalPage = (sum + pageSize - 1) / pageSize;
	}
	public void empty() {
	    this.totalPage = 0;
	    this.sum = 0;
	}
	public static Pagination getEmpty(int pageIndex, int pageSize) {
		return new Pagination(pageIndex, pageSize, 0, 0);
	}

	public static int calculateIndexStart(int pageIndex, int pageSize) {
		return (pageIndex - 1) * pageSize;
	}

	/* getter and setter */
	public int getPageIndex() {
		return pageIndex;
	}
	public void setPageIndex(int pageIndex) {
		this.pageIndex = pageIndex;
	}
	public int getPageSize() {
		return pageSize;
	}
	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
	public long getTotalPage() {
		return totalPage;
	}
	public void setTotalPage(long totalPage) {
		this.totalPage = totalPage;
	}
	public long getSum() {
		return sum;
	}
	public void setSum(long sum) {
		this.sum = sum;
	}

	@Override
	public String toString() {
		return "PageInfo [pageIndex=" + pageIndex + ", pageSize=" + pageSize + ", totalPage=" + totalPage + ", sum="
				+ sum + "]";
	}
}
