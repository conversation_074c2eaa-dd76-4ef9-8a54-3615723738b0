package com.bees360.entity.dto;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;

import org.springframework.util.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

public class AppSegmentDto implements Serializable {
	public static final String SEGMENT_CODE_SPLIT = "-";
	public static final String SEGMENT_EMPTY_ROOT_INDEX = "{root}";
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 4294096856035812047L;

	//the code type, structures,direction etc.
	private int codeType;
	//the segment value code.quot HouseImageSegment id.
	private String code;
	//the value type, single election, check etc.
	private int valueType;
	//the value type, single election, check etc.
	private String value;

	private String valueName;

	private String unit;

	private String name;
	//is leaf,0:false, 1:true
	private boolean isLeaf;

	private boolean isDamage;

	private List<SegmentImageDto> images;

	private List<AppSegmentDto> data;

	public int getCodeType() {
		return codeType;
	}

	public void setCodeType(int codeType) {
		this.codeType = codeType;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	@JsonInclude(value=Include.NON_NULL)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@JsonInclude(value=Include.NON_NULL)
	public List<AppSegmentDto> getData() {
		return data;
	}

	public void setData(List<AppSegmentDto> data) {
		this.data = data;
	}

	@JsonInclude(value=Include.NON_NULL)
	public int getValueType() {
		return valueType;
	}

	public void setValueType(int valueType) {
		this.valueType = valueType;
	}

	@JsonInclude(value=Include.NON_NULL)
	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@JsonInclude(value=Include.NON_NULL)
	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	@JsonInclude(value=Include.NON_NULL)
	public List<SegmentImageDto> getImages() {
		return images;
	}

	public void setImages(List<SegmentImageDto> images) {
		this.images = images;
	}

	@JsonIgnore
	public boolean getIsLeaf() {
		return isLeaf;
	}

	public void setIsLeaf(boolean isLeaf) {
		this.isLeaf = isLeaf;
	}

	@JsonInclude(value=Include.NON_NULL)
	public String getValueName() {
		return valueName;
	}

	public void setValueName(String valueName) {
		this.valueName = valueName;
	}

	@JsonIgnore
	public boolean getIsDamage() {
		return isDamage;
	}

	public void setIsDamage(boolean isDamage) {
		this.isDamage = isDamage;
	}

	@JsonIgnore
	public long getLastCode() {
		if (ObjectUtils.isEmpty(this.code)) {
			return 0;
		}
		String[] codeStrs = this.code.split(SEGMENT_CODE_SPLIT);
		return Long.parseLong(codeStrs[codeStrs.length - 1]);
	}

	@JsonIgnore
	public long getParentLastCode() {
		if (ObjectUtils.isEmpty(this.code)) {
			return 0;
		}
		String[] codeStrs = this.code.split(SEGMENT_CODE_SPLIT);
		if (codeStrs.length < 3) {
			return 0;
		}
		return Long.parseLong(codeStrs[codeStrs.length - 2]);
	}

	@JsonIgnore
	public int getRootIndex() {
		if (this.code == null) {
			return 1;
		}
		String rootIndexStr = this.code.split(AppSegmentDto.SEGMENT_CODE_SPLIT)[0];
		return SEGMENT_EMPTY_ROOT_INDEX.equals(rootIndexStr) ? 1 : Integer.parseInt(rootIndexStr);
	}

	@Override
	public String toString() {
		return "AppSegmentDto [codeType=" + codeType + ", code=" + code + ", valueType=" + valueType + ", value="
				+ value + ", valueName=" + valueName + ", unit=" + unit + ", name=" + name + ", isLeaf=" + isLeaf
				+ ", isDamage=" + isDamage + ", images=" + images + ", data=" + data + "]";
	}

}
