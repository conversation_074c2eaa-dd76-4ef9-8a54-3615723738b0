package com.bees360.entity.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bees360.entity.dto.IdNameDescriptionDto;
import com.bees360.entity.dto.IdNameDto;

public enum DamageSeverityEnum implements BaseCodeEnum{
	SEVERE_DAMAGED(1001500, "Severe damage", "Overall condition of roof appears to be severe with several worn area noted at the time of inspection, general non-storm related noted."),
	MODERATE_DAMAGED(1001510, "Moderate damage", "Overall condition of roof appears to be moderate with several worn area noted at the time of inspection, general non-storm related noted."),
	MILD_DAMAGED(1001520, "Mild damage", "Overall condition of roof appears to be mild with several worn area noted at the time of inspection, general non-storm related noted."),
	NO_DAMAGE(1001530, "No damage", "No damage noted at the time of inspection.");

	private final int code;
	private final String display;
	private final String description;

	DamageSeverityEnum(int code, String display, String description){
		this.code = code;
		this.display = display;
		this.description = description;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public String getDescription() {
		return description;
	}

	public static List<Map<String, Object>> getValues(){
		DamageSeverityEnum[] severityEnums = DamageSeverityEnum.values();
		List<Map<String, Object>> severityList = new ArrayList<>(severityEnums.length);
		for(DamageSeverityEnum severityEnum: severityEnums){
			Map<String, Object> severityMap = new HashMap<>();
			severityMap.put("id", severityEnum.getCode());
			severityMap.put("name", severityEnum.getDisplay());
			severityMap.put("description", severityEnum.getDescription());
			severityList.add(severityMap);
		}
		return severityList;
	}

	public static boolean isSeverity(int code) {
		for(DamageSeverityEnum severity: DamageSeverityEnum.values()) {
			if (code == severity.getCode()) {
				return true;
			}
		}
		return false;
	}

	public static List<IdNameDto> enumsToIdNameDescription() {
		DamageSeverityEnum[] severityEnums = DamageSeverityEnum.values();
		List<IdNameDto> severityList = new ArrayList<>(severityEnums.length);
		for(DamageSeverityEnum denum: severityEnums){
			IdNameDescriptionDto dto = new IdNameDescriptionDto(denum.getCode(), denum.getDisplay(), denum.getDescription());
			severityList.add(dto);
		}
		return severityList;
	}

    public static DamageSeverityEnum getEnum(Integer code){
        if(code == null) {
            return null;
        }
        DamageSeverityEnum[] damageSeverities = DamageSeverityEnum.values();
        for(DamageSeverityEnum damageSeverity: damageSeverities){
            if(damageSeverity.code == code){
                return damageSeverity;
            }
        }
        return null;
    }

}
