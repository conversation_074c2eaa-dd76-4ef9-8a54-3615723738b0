package com.bees360.entity.enums;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

public enum AmericaStateEnums {
    AL("Alabama", "AL"),
    AK("Alaska", "AK"),
    AZ("Arizona", "AZ"),
    AR("Arkansas", "AR"),
    CA("California", "CA"),
    CO("Colorado", "CO"),
    CT("Connecticut", "CT"),
    DE("Delaware", "DE"),
    FL("Florida", "FL"),
    GA("Georgia", "GA"),
    HI("Hawaii", "HI"),
    ID("Idaho", "ID"),
    IL("Illinois", "IL"),
    IN("Indiana", "IN"),
    IA("Iowa", "IA"),
    KS("Kansas", "KS"),
    KY("Kentucky", "KY"),
    LA("Louisiana", "LA"),
    ME("Maine", "ME"),
    MD("Maryland", "MD"),
    MA("Massachusetts", "MA"),
    MI("Michigan", "MI"),
    MN("Minnesota", "MN"),
    MS("Mississippi", "MS"),
    MO("Missouri", "MO"),
    MT("Montana", "MT"),
    NE("Nebraska", "NE"),
    NV("Nevada", "NV"),
    NH("New Hampshire", "NH"),
    NJ("New Jersey", "NJ"),
    NM("New Mexico", "NM"),
    NY("New York", "NY"),
    NC("North Carolina", "NC"),
    ND("North Dakota", "ND"),
    OH("Ohio", "OH"),
    OK("Oklahoma", "OK"),
    OR("Oregon", "OR"),
    PA("Pennsylvania", "PA"),
    RI("Rhode Island", "RI"),
    SC("South Carolina", "SC"),
    SD("South Dakota", "SD"),
    TN("Tennessee", "TN"),
    TX("Texas", "TX"),
    UT("Utah", "UT"),
    VT("Vermont", "VT"),
    VA("Virginia", "VA"),
    WA("Washington", "WA"),
    WV("West Virginia", "WV"),
    WI("Wisconsin", "WI"),
    WY("Wyoming", "WY"),
    ;

    private final String fullName;
    private final String abbreviation;

    AmericaStateEnums(String fullName, String abbreviation) {
        this.fullName = fullName;
        this.abbreviation = abbreviation;
    }

    public String getFullName() {
        return fullName;
    }

    public String getAbbreviation() {
        return abbreviation;
    }

    public static AmericaStateEnums valueOfAbbreviation(String abbreviation) {
        for (AmericaStateEnums value : values()) {
            if (value.getAbbreviation().equals(abbreviation)) {
                return value;
            }
        }
        return null;
    }

    public static String abbreviationToFullName(String abbreviation) {
        AmericaStateEnums americaStateEnums = valueOfAbbreviation(abbreviation);
        if (americaStateEnums != null) {
            return americaStateEnums.getFullName();
        }
        return abbreviation;
    }

    public static AmericaStateEnums valueOfFullName(String fullName) {
        for (AmericaStateEnums value : values()) {
            if (value.getFullName().equals(fullName)) {
                return value;
            }
        }
        return null;
    }

    public static AmericaStateEnums valueOfState(String state) {
        return Optional.ofNullable(valueOfAbbreviation(state)).orElse(valueOfFullName(state));
    }
}
