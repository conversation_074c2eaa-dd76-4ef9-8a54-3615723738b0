package com.bees360.entity;
import java.io.Serial;
import java.io.Serializable;

import com.bees360.entity.enums.RoofComplexityEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class House implements Serializable {
    //'save the data of the house to reduce the calculation'

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1854598956261911982L;
	//primary key
	private long id;
	private double gpsLocationLatitude;
	private double gpsLocationLongitude;
	private String country;
	private String state;
	private String city;
	private String address;
	private String zipCode;
	//'the image cropped from google map'
	private String overview;
	private int stories;
	//'the value should be (0-24)/12'
	private int roofPrimaryPitch;
	//'default unit is ft'
	private double roofPerimeter;
	//'option from [1:Low, 2:]'
	private String roofComplexity;
	//'option from [1:Low, 2:]'
	private int complexityValue;
	//'default unit is sqft'
	private double roofArea;
	//'a polygon representing the edge of the roof'
	private String roofBoundary;
	//'the minimum enclosing rectangle of the edge of the roof'
	private String roofRect;
	//FOREIGN KEY(modified_by) REFERENCES User(user_id)
	private long modifiedBy;
	private long modifiedTime;

	private double longestLength;




	public String getRoofComplexity() {
		return roofComplexity;
	}
	public void setRoofComplexity(String roofComplexity) {
		this.roofComplexity = roofComplexity;
		this.complexityValue = RoofComplexityEnum.getEnum(roofComplexity).getCode();
	}
	@JsonIgnore
	public int getComplexityValue() {
		return complexityValue;
	}
	public void setComplexityValue(int complexityValue) {
		this.complexityValue = complexityValue;
		this.roofComplexity = RoofComplexityEnum.getEnum(complexityValue).getDisplay();
	}
	public double getGpsLocationLongitude() {
		return gpsLocationLongitude;
	}
	public void setGpsLocationLongitude(double gpsLocationLongitude) {
		this.gpsLocationLongitude = gpsLocationLongitude;
	}
	public double getGpsLocationLatitude() {
		return gpsLocationLatitude;
	}
	public void setGpsLocationLatitude(double gpsLocationLatitude) {
		this.gpsLocationLatitude = gpsLocationLatitude;
	}
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getZipCode() {
		return zipCode;
	}
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}
	public String getOverview() {
		return overview;
	}
	public void setOverview(String overview) {
		this.overview = overview;
	}
	public int getStories() {
		return stories;
	}
	public void setStories(int stories) {
		this.stories = stories;
	}
	public int getRoofPrimaryPitch() {
		return roofPrimaryPitch;
	}
	public void setRoofPrimaryPitch(int roofPrimaryPitch) {
		this.roofPrimaryPitch = roofPrimaryPitch;
	}
	public String getRoofBoundary() {
		return roofBoundary;
	}
	public void setRoofBoundary(String roofBoundary) {
		this.roofBoundary = roofBoundary;
	}
	public String getRoofRect() {
		return roofRect;
	}
	public void setRoofRect(String roofRect) {
		this.roofRect = roofRect;
	}
	public long getModifiedTime() {
		return modifiedTime;
	}
	public void setModifiedTime(long modifiedTime) {
		this.modifiedTime = modifiedTime;
	}
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public long getModifiedBy() {
		return modifiedBy;
	}
	public void setModifiedBy(long modifiedBy) {
		this.modifiedBy = modifiedBy;
	}
	public double getRoofPerimeter() {
		return roofPerimeter;
	}
	public void setRoofPerimeter(double roofPerimeter) {
		this.roofPerimeter = roofPerimeter;
	}
	public double getRoofArea() {
		return roofArea;
	}
	public void setRoofArea(double roofArea) {
		this.roofArea = roofArea;
	}
	public double getLongestLength() {
		return longestLength;
	}
	public void setLongestLength(double longestLength) {
		this.longestLength = longestLength;
	}
}
