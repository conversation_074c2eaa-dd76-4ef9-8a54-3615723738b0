package com.bees360.entity.dto;

public class LineDto {

	private Point3D start;

	private Point3D end;

	private int type;

	private double weight;

	public LineDto() {
		super();
	}

	public LineDto(Point3D start, Point3D end, int type, double weight) {
		super();
		this.start = start;
		this.end = end;
		this.type = type;
		this.weight = weight;
	}

	public Point3D getStart() {
		return start;
	}

	public void setStart(Point3D start) {
		this.start = start;
	}

	public Point3D getEnd() {
		return end;
	}

	public void setEnd(Point3D end) {
		this.end = end;
	}

	public double getWeight() {
		return weight;
	}

	public void setWeight(double weight) {
		this.weight = weight;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	@Override
	public String toString() {
		return "LineDto [start=" + start.toString() + ", end=" + end.toString() + ", type=" + type + ", weight=" + weight + "]";
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		LineDto other = (LineDto) obj;
		if (end == null) {
			if (other.end != null)
				return false;
		} else if (!end.equals(other.end))
			return false;
		if (start == null) {
			if (other.start != null)
				return false;
		} else if (!start.equals(other.start))
			return false;
		if (type != other.type)
			return false;
		if (Double.doubleToLongBits(weight) != Double.doubleToLongBits(other.weight))
			return false;
		return true;
	}

}
