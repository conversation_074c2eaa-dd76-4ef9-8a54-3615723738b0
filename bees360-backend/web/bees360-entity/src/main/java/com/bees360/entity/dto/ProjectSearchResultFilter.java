package com.bees360.entity.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ProjectSearchResultFilter {

    private boolean withCreator;

    private boolean withMainImage;

    private boolean withPilot;

    private boolean withProcessBy;

    private boolean withRoles;

    private boolean withStatus;

    private boolean withProjectTag;

    private boolean withHiveDistance;

    public static ProjectSearchResultFilter allInclude(){
        return ProjectSearchResultFilter.builder()
            .withCreator(true)
            .withMainImage(true)
            .withPilot(true)
            .withProcessBy(true)
            .withRoles(true)
            .withStatus(true)
            .withProjectTag(true)
            .withHiveDistance(true)
            .build();
    }

    public static ProjectSearchResultFilter nonInclude(){
        return ProjectSearchResultFilter.builder()
            .withCreator(false)
            .withMainImage(false)
            .withPilot(false)
            .withProcessBy(false)
            .withRoles(false)
            .withStatus(false)
            .withProjectTag(false)
            .withHiveDistance(false)
            .build();
    }
}
