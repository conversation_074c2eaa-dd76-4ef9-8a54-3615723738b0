package com.bees360.entity.normalize;

import com.bees360.entity.dto.ProjectBaseDto;
import com.bees360.entity.openapi.OpenProjectCreateVo;
import static com.bees360.entity.util.FunctionUtil.acceptNotNull;
import com.bees360.entity.vo.ProjectInsuredInfoVo;
import com.bees360.web.core.validation.PhoneValidator;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ProjectDataNormalizer {

    private static final PhoneValidator phoneValidator = PhoneValidator.getDefaultInstance();

    public static void normalize(ProjectBaseDto project) {
        if (Objects.isNull(project)) {
            return;
        }
        // 目前仅处理asset owner phone
//        acceptNotNull(project.getAgentPhone(), phoneValidator::formatPhoneOrEmpty, project::setAgentPhone);
        acceptNotNull(project.getAssetOwnerPhone(), phoneValidator::formatPhoneOrEmpty, project::setAssetOwnerPhone);
        acceptNotNull(project.getInsuredHomePhone(), phoneValidator::formatPhoneOrEmpty, project::setInsuredHomePhone);
        acceptNotNull(project.getInsuredWorkPhone(), phoneValidator::formatPhoneOrEmpty, project::setInsuredWorkPhone);
    }

    public static void normalize(OpenProjectCreateVo project) {
        if (Objects.isNull(project)) {
            return;
        }
//        acceptNotNull(project.getAgentPhone(), phoneValidator::formatPhoneOrEmpty, project::setAgentPhone);
        acceptNotNull(project.getInsuredPhone(), phoneValidator::formatPhoneOrEmpty, project::setInsuredPhone);
    }

    public static void normalize(ProjectInsuredInfoVo project) {
        if (Objects.isNull(project)) {
            return;
        }
        acceptNotNull(project.getAssetOwnerPhone(), phoneValidator::formatPhoneOrEmpty, project::setAssetOwnerPhone);
    }
}
