package com.bees360.entity.dto;

import java.util.List;

import com.bees360.entity.CustomizedReportElement;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

public class ReportElementResultDto {

	private Integer componentId;

	private String imageId;

	private List<CustomizedReportElement> customizedElements;

	private List<String> parentIds;

	private String caption;

	private Integer type;

	private Integer sourceType;

	private List<ScreenshotIdDto> cropImageList;

	private String associatedImageCaption;

	private String category;

	private String subCategory;

	private String description;

	@JsonInclude(value=Include.NON_NULL)
	public Integer getComponentId() {
		return componentId;
	}

	public void setComponentId(Integer componentId) {
		this.componentId = componentId;
	}

	@JsonInclude(value=Include.NON_NULL)
	public String getImageId() {
		return imageId;
	}

	public void setImageId(String imageId) {
		this.imageId = imageId;
	}

	@JsonInclude(value=Include.NON_NULL)
	public List<CustomizedReportElement> getCustomizedElements() {
		return customizedElements;
	}

	public void setCustomizedElements(List<CustomizedReportElement> customizedElements) {
		this.customizedElements = customizedElements;
	}

	@JsonInclude(value=Include.NON_NULL)
	public List<String> getParentIds() {
		return parentIds;
	}

	public void setParentIds(List<String> parentIds) {
		this.parentIds = parentIds;
	}

	@JsonInclude(value=Include.NON_NULL)
	public String getCaption() {
		return caption;
	}

	public void setCaption(String caption) {
		this.caption = caption;
	}

	@JsonInclude(value=Include.NON_NULL)
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	@JsonInclude(value=Include.NON_NULL)
	public Integer getSourceType() {
		return sourceType;
	}

	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}

	@JsonInclude(value=Include.NON_NULL)
	public List<ScreenshotIdDto> getCropImageList() {
		return cropImageList;
	}

	public void setCropImageList(List<ScreenshotIdDto> cropImageList) {
		this.cropImageList = cropImageList;
	}

	@JsonInclude(value=Include.NON_NULL)
	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	@JsonInclude(value=Include.NON_NULL)
	public String getSubCategory() {
		return subCategory;
	}

	public void setSubCategory(String subCategory) {
		this.subCategory = subCategory;
	}

	@JsonInclude(value=Include.NON_NULL)
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@JsonInclude(value=Include.NON_NULL)
	public String getAssociatedImageCaption() {
		return associatedImageCaption;
	}

	public void setAssociatedImageCaption(String associatedImageCaption) {
		this.associatedImageCaption = associatedImageCaption;
	}


}
