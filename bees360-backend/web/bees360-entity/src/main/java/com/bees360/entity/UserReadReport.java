package com.bees360.entity;

public class UserReadReport {
	private long id;
	private long userId;
	private long projectId;
	private String reportId;
	private int reportType;
	private boolean isDeleted;
	private long createdTime;
	private long updatedTime;

	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public long getUserId() {
		return userId;
	}
	public void setUserId(long userId) {
		this.userId = userId;
	}
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public String getReportId() {
		return reportId;
	}
	public void setReportId(String reportId) {
		this.reportId = reportId;
	}
	public int getReportType() {
		return reportType;
	}
	public void setReportType(int reportType) {
		this.reportType = reportType;
	}
	public boolean isDeleted() {
		return isDeleted;
	}
	public void setDeleted(boolean isDeleted) {
		this.isDeleted = isDeleted;
	}
	public long getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}
	public long getUpdatedTime() {
		return updatedTime;
	}
	public void setUpdatedTime(long updatedTime) {
		this.updatedTime = updatedTime;
	}

	@Override
	public String toString() {
		return "UserReadReport [id=" + id + ", userId=" + userId + ", projectId=" + projectId + ", reportId=" + reportId
				+ ", reportType=" + reportType + ", isDeleted=" + isDeleted + ", createdTime="
				+ createdTime + ", updatedTime=" + updatedTime + "]";
	}
}
