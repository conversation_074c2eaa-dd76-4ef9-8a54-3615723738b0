package com.bees360.entity.dto;

import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;

/**
 * 用于修改Project status
 *
 * <AUTHOR>
 * @since 2021/5/10
 */
@Data
@Builder
public class ProjectUpdateStatusDto implements Serializable {

    private long projectId;

    private long userId;

    /**
     * @see NewProjectStatusEnum
     */
    private int status;

    /**
     * @see SystemTypeEnum
     */
    private String systemType;

}
