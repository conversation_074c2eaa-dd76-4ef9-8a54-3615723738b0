package com.bees360.entity;

import java.io.Serial;
import java.io.Serializable;

public class SystemValue implements Serializable{
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;

	public static final String SYSTEM_VALUE_PRICEFORDAMAGEMEASUREMENTREPORT = "PriceForDamageMeasurementReport";
	public static final String SYSTEM_VALUE_PRICEFORONLYMEASUREMENTREPORT = "PriceForOnlyMeasurementReport";
	public static final String SYSTEM_VALUE_PRICEFORQUICKDAMAGEWEBREPORT = "PriceForQuickDamageWebReport";
	public static final String SYSTEM_VALUE_PRICEFORQUICKDAMAGEAPPREPORT = "PriceForQuickDamageAppReport";
	public static final String SYSTEM_VALUE_PRICEFORQUICKDAMAGESKYREPORT = "PriceForQuickDamageSkyReport";

	public static final String SYSTEM_VALUE_COMMISSIONRATE = "CommissionRate";
	public static final String SYSTEM_VALUE_PRICEFORGREATTHAN5000FEET = "PriceForGreatThan5000Feet";
	public static final String SYSTEM_VALUE_AREATHRESHOLDFORPRICE = "areaThresholdForPrice";

	public static final String SYSTEM_VALUE_TAXRATE = "TaxRate";

	public static final String SYSTEM_VALUE_FREEONTRAILPROJECTNUM = "FreeOnTrailProjectNum";
	public static final String SYSTEM_VALUE_NEWCUSTOMERDISCOUNTPERCENT = "newCustomerDiscountPercent";
	public static final String SYSTEM_VALUE_NEWCUSTOMERDISCOUNTPROJECTNUM = "newCustomerDiscountProjectNum";

	//2018-09-20 the price of report and pilot has changed
	public static final String SERVICE_KEY_ESTIMATED_ROOF_AREA = "EstimatedRoofArea";
	public static final String SERVICE_KEY_MEASUREMENT_AND_DAMAGE_REPORT_SERVICE = "EstimatedRoofArea-MeasurementAndDamageReportService";
	public static final String SERVICE_KEY_MEASUREMENT_REPORT = "EstimatedRoofArea-MeasurementReportService";
	public static final String SERVICE_KEY_QUICK_DAMAGE_REPORT = "EstimatedRoofArea-QuickDamageReportService";
	public static final String SERVICE_KEY_PILOT_SERVICE = "PilotService";

	public static final String SERVICE_KEY_PILOT_TAX_PERCENTAGE = "PilotTaxPercentage";
	public static final String SERVICE_KEY_REPORT_TAX_PERCENTAGE = "ReportTaxPercentage";
	public static final String SERVICE_KEY_PACKAGE_TAX_PERCENTAGE = "PackageTaxPercentage";

	//bidding system
	public static final String SERVICE_KEY_BIDDING_REPORT = "BiddingReportService";

	//highfly inspection
	public static final String SERVICE_KEY_HIGHFLY_REPORT = "HighflyReportService";

	/**
	 * 2019-03-25 new price
	 */
	public static final String SERVICE_KEY_REPORT_PRICE_REAL_TIME_DAMAGE_ASSESSMENT = "Report-Price-Real-Time-Damage-Assessment";
	public static final String SERVICE_KEY_REPORT_PRICE_PREMIUM_MEASUREMENT = "Report-Price-Premium-Measurement";
	public static final String SERVICE_KEY_REPORT_PRICE_PREMIUM_DAMAGE_ASSESSMENT = "Report-Price-Premium-Damage-Assessment";
	public static final String SERVICE_KEY_REPORT_PRICE_PROPERTY_IMAGE = "Report-Price-Property-Image";
	public static final String SERVICE_KEY_REPORT_PRICE_INFRARED_DAMAGE_ASSESSMENT = "Report-Price-Infrared-Damage-Assessment";
	public static final String SERVICE_KEY_REPORT_PRICE_PRELIMINARY_DAMAGE_ASSESSMENT = "Report-Price-Preliminary-Damage-Assessment";

	public static final String SERVICE_KEY_REPORT_PRICE_HIGHFLY_EVALUATION = "Report-Price-Highfly-Evaluation";
	//free
	public static final String SERVICE_KEY_REPORT_PRICE_REAL_TIME_QUICK_SQUARE = "Report-Price-Real-Time-Quick-Square";
	public static final String SERVICE_KEY_REPORT_PRICE_ON_SITE_BIDDING = "Report-Price-On-Site-Bidding";

	public static final String SERVICE_KEY_REPORT_PRICE_ROOF_ONLY_UNDERWRITING_REPORT = "Report-Price-Roof-Only-Underwriting-Report";
	public static final String SERVICE_KEY_REPORT_PRICE_FULL_SCOPE_UNDERWRITING_REPORT = "Report-Price-Full-Scope-Underwriting-Report";

	private long serviceId;
	private String country;
	private String serviceName;
	private double serviceValue;
	private String label;

	public long getServiceId() {
		return serviceId;
	}

	public void setServiceId(long serviceId) {
		this.serviceId = serviceId;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getServiceName() {
		return serviceName;
	}

	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	public double getServiceValue() {
		return serviceValue;
	}

	public void setServiceValue(double serviceValue) {
		this.serviceValue = serviceValue;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}
}
