package com.bees360.entity;
import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.bees360.entity.dto.MapPoint;
import com.bees360.entity.dto.Point;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class Bidding extends House {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1587787916252769725L;

	/*
	 * 'save the data for square report and bidding'
	 */
	//The foreign key
	private long projectId;

	private String companyName;
	private String companyWebsite;
	//'# of years in roofing industry'
	private int industryExperience;
	private int shingleGrade;
	//'Unit price for single($/sq.ft)'
	private double shinglePrice;
	//'the # of stories is more than 2'
	private boolean highRoof;
	// 'Unit of price is $'
	private double highSteepRoofCharge;
	//'Unit of price is $'
	private double totalPrice;
	private String intro;
	//'the minimum enclosing rectangle of the edge of the roof'
	private String roofRect;
	private List<Point> roofRectPoints;

	private String mapRect;

	private double planeRoofArea;


	public double getPlaneRoofArea() {
		return planeRoofArea;
	}
	public void setPlaneRoofArea(double planeRoofArea) {
		this.planeRoofArea = planeRoofArea;
	}
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getCompanyWebsite() {
		return companyWebsite;
	}
	public void setCompanyWebsite(String companyWebsite) {
		this.companyWebsite = companyWebsite;
	}
	public int getIndustryExperience() {
		return industryExperience;
	}
	public void setIndustryExperience(int industryExperience) {
		this.industryExperience = industryExperience;
	}

	public boolean getHighRoof() {
		return highRoof;
	}
	public void setHighRoof(boolean highRoof) {
		this.highRoof = highRoof;
	}
	public String getIntro() {
		return intro;
	}
	public void setIntro(String intro) {
		this.intro = intro;
	}

	public int getShingleGrade() {
		return shingleGrade;
	}
	public void setShingleGrade(int shingleGrade) {
		this.shingleGrade = shingleGrade;
	}
	@JsonIgnore
	public String getRoofRect() {
		return roofRect;
	}
	public void setRoofRect(String roofRect) {
		this.roofRect = roofRect;
	}
	public List<Point> getRoofRectPoints() {
		return roofRectPoints;
	}
	public void setRoofRectPoints(List<Point> roofRectPoints) {
		this.roofRectPoints = roofRectPoints;
	}
	@JsonIgnore
	public String getMapRect() {
		return mapRect;
	}
	public void setMapRect(String mapRect) {
		this.mapRect = mapRect;
	}
	public double getShinglePrice() {
		return shinglePrice;
	}
	public void setShinglePrice(double shinglePrice) {
		this.shinglePrice = shinglePrice;
	}
	public double getHighSteepRoofCharge() {
		return highSteepRoofCharge;
	}
	public void setHighSteepRoofCharge(double highSteepRoofCharge) {
		this.highSteepRoofCharge = highSteepRoofCharge;
	}
	public double getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(double totalPrice) {
		this.totalPrice = totalPrice;
	}
	public List<MapPoint> getMapPoints() {
		if (StringUtils.isEmpty(mapRect)) {
			return null;
		} else {
			return parseGeometryToMapPoints(mapRect);
		}
	}
	public void setMapPoints(List<MapPoint> mapPoints) {
		this.mapRect = polygonToText(mapPoints);
	}
	@Override
	public String toString() {
		return "Bidding [projectId=" + projectId + ", companyName=" + companyName + ", companyWebsite=" + companyWebsite
				+ ", industryExperience=" + industryExperience + ", shingleGrade=" + shingleGrade + ", shinglePrice="
				+ shinglePrice + ", highRoof=" + highRoof + ", highSteepRoofCharge=" + highSteepRoofCharge
				+ ", totalPrice=" + totalPrice + ", intro=" + intro + ", roofRect=" + roofRect + ", mapRect=" + mapRect
				+ "]";
	}

	// add by zhoushan.zhao, For the time being, I have not thought of a better way to write.
	private static List<MapPoint> parseGeometryToMapPoints(String geomText){
		if(geomText == null){
			return null;
		}
		List<MapPoint> points = new ArrayList<MapPoint>();
		// index of first digit
		int left = 0;
		while(left < geomText.length() && !Character.isDigit(geomText.charAt(left))){
			left ++;
		}
		// index of last digit
		int right = geomText.length() - 1;
		while(0 <= right && !Character.isDigit(geomText.charAt(right))){
			right --;
		}
		String pointString = geomText.substring(left, right + 1);
		String[] numberPairs = pointString.split(",");
		for(String numPair: numberPairs){
			MapPoint point = new MapPoint();
			String[] nums = numPair.trim().split("\\s+");
			point.setLat(Double.parseDouble(nums[0]));
			point.setLng(Double.parseDouble(nums[1]));
			points.add(point);
		}
		return points;
	}

	private static String polygonToText(List<MapPoint> points) {
		if(points == null || points.size() == 0){
			return null;
		}
		StringBuilder polygon = new StringBuilder("POLYGON((");
		for(int i = 0; i < points.size(); i ++){
			MapPoint p = points.get(i);
			polygon.append(p.getLat() + " " + p.getLng());
			if(i < points.size() - 1){
				polygon.append(",");
			}
		}
		MapPoint firstPoint = points.get(0);
		MapPoint lastPoint = points.get(points.size() - 1);
		if(firstPoint.getLat() != lastPoint.getLat() || firstPoint.getLng() != lastPoint.getLng()){
			polygon.append("," + firstPoint.getLat() + " " + firstPoint.getLng());
		}
		polygon.append("))");
		return polygon.toString();
	}
}
