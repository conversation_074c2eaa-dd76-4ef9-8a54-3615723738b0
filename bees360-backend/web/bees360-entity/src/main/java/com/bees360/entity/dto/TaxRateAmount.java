package com.bees360.entity.dto;

/**
 * tax的总额。用于统计某种Tax一共是多少钱。
 *
 * <AUTHOR>
 * @date 2019/09/27 19:40
 */
public class TaxRateAmount {
    private int taxRateId;
    private String taxRateName;
    private double taxRatePercentage;
    private double taxAmount;

    public int getTaxRateId() {
        return taxRateId;
    }

    public void setTaxRateId(int taxRateId) {
        this.taxRateId = taxRateId;
    }

    public String getTaxRateName() {
        return taxRateName;
    }

    public void setTaxRateName(String taxRateName) {
        this.taxRateName = taxRateName;
    }

    public double getTaxRatePercentage() {
        return taxRatePercentage;
    }

    public void setTaxRatePercentage(double taxRatePercentage) {
        this.taxRatePercentage = taxRatePercentage;
    }

    public double getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(double taxAmount) {
        this.taxAmount = taxAmount;
    }

    @Override
    public String toString() {
        return "TaxRateAmount{" + "taxRateId=" + taxRateId + ", taxRateName='" + taxRateName + '\''
            + ", taxRatePercentage=" + taxRatePercentage + ", taxAmount=" + taxAmount + '}';
    }
}
