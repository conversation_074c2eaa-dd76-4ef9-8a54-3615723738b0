package com.bees360.entity.dto;

import com.bees360.web.core.validation.Phone;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class UserRosterDto {

    // user base info

    private long userId;
    private long adjusterId;
    @NotBlank
    private String password;
    @NotBlank
    private String firstName;
    @NotBlank
	private String lastName;
    /**
     * adjuster注册不强制提供phone
     */
    @Phone
    private String phone = "";
    @NotBlank
    private String email;
    @NotBlank
    private String address;
    @NotBlank
	private String city;
    @NotBlank
    private String state;

	private String country;
	@NotBlank
	private String zipCode;

	private double lng;

	private double lat;


    private long rosterId;
    /**
     * license合法的州列表
     */
    @NotEmpty
    private List<String> activeLicenseState;

    /**
     * The certifications that the adjuster currently have
     */
    private String certifications;

    /**
     * 场地经验
     * 包括:1~3 years/3~5 years/5~10 years/10+ years
     *
     */
    @NotBlank
    private String fieldExperience;

    /**
     * 办公室经验
     * 包括:1~3 years/3~5 years/5~10 years/10+ years
     *
     */
    @NotBlank
    private String deskExperience;

    /**
     * 商业经验
     * 包括:1~3 years/3~5 years/5~10 years/10+ years
     *
     */
    private String commercialExperience;

    /**
     * xactimate平台经验 可以为空
     */
    private String xactimatePlatformExperience;

    /**
     * symbility 平台经验可以为空
     */
    private String symbilityPlatformExperience;

    /**
     * 其它平台经验 可以为空
     */
    private String otherPlatformExperience;

    // unused so far below

	private String designatedHomeStateLicense;
	private String additionalOperatingTerritories;
	private String additionalLicense;
	private int yearsOfExperience;
    private String operatingCityState;
	private String message;
	private String resumeUrl;
	/** unit miles */
	private int travelRadius;
	private boolean moreThan100MilesTraveled;
	private boolean catEventDeployed;

    private static final String SPLITER = "#";
    public String getActiveLicenseStateStr() {
        return String.join(SPLITER, this.activeLicenseState);
    }
	@Override
	public String toString() {
		return "UserRosterDto [firstName=" + firstName + ", lastName=" + lastName + ", email="
				+ email + ", address=" + address + ", city=" + city + ", state=" + state + ", country=" + country
				+ ", zipCode=" + zipCode + ", lng=" + lng + ", lat=" + lat + ", operatingCityState="
				+ activeLicenseState + ", designatedHomeStateLicense=" + designatedHomeStateLicense
				+ ", additionalOperatingTerritories=" + additionalOperatingTerritories + ", additionalLicense="
				+ additionalLicense + ", fieldExperience=" + fieldExperience+ ", deskExperience" + deskExperience
                + ", travelRadius=" + travelRadius
				+ ", message=" + message + ", resumeUrl=" + resumeUrl + ", moreThan100MilesTraveled="
				+ moreThan100MilesTraveled + ", catEventDeployed=" + catEventDeployed + ", password=" + password + "]";
	}
}
