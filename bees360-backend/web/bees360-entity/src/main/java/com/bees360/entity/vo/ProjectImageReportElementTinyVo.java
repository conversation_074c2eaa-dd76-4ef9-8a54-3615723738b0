package com.bees360.entity.vo;

import java.io.Serial;
import java.io.Serializable;

public class ProjectImageReportElementTinyVo implements Serializable {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = -4459879977367417750L;
	private int componentId;

	private String imageId;

	private String imageUrl;

	private String title;

	private Double weight;

	private int type;

    private int closeUpType;

	private int width;

	private int height;

	private Long annotationId;

	/**
	 * 1 is bees360 go app image, 0 is drone image
	 */
	private int sourceType;

	public ProjectImageReportElementTinyVo() {
		super();
	}

	public ProjectImageReportElementTinyVo(int componentId, String imageId, String imageUrl, String title, Double weight) {
		super();
		this.componentId = componentId;
		this.imageId = imageId;
		this.imageUrl = imageUrl;
		this.title = title;
		this.weight = weight;
	}

	public ProjectImageReportElementTinyVo(int componentId, String imageId, String imageUrl, String title, Double weight,
			int type, int width, int height, Long annotationId, int sourceType) {
		super();
		this.componentId = componentId;
		this.imageId = imageId;
		this.imageUrl = imageUrl;
		this.title = title;
		this.weight = weight;
		this.type = type;
		this.width = width;
		this.height = height;
		this.annotationId = annotationId;
		this.sourceType = sourceType;
	}

	public int getComponentId() {
		return componentId;
	}

	public void setComponentId(int componentId) {
		this.componentId = componentId;
	}

	public String getImageId() {
		return imageId;
	}

	public void setImageId(String imageId) {
		this.imageId = imageId;
	}

	public String getImageUrl() {
		return imageUrl;
	}

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	public Double getWeight() {
		return weight;
	}

	public void setWeight(Double weight) {
		this.weight = weight;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getWidth() {
		return width;
	}

	public void setWidth(int width) {
		this.width = width;
	}

	public int getHeight() {
		return height;
	}

	public void setHeight(int height) {
		this.height = height;
	}

	public Long getAnnotationId() {
		return annotationId;
	}

	public void setAnnotationId(Long annotationId) {
		this.annotationId = annotationId;
	}

	public int getSourceType() {
		return sourceType;
	}

	public void setSourceType(int sourceType) {
		this.sourceType = sourceType;
	}

    public int getCloseUpType() {
        return closeUpType;
    }

    public void setCloseUpType(int closeUpType) {
        this.closeUpType = closeUpType;
    }
}
