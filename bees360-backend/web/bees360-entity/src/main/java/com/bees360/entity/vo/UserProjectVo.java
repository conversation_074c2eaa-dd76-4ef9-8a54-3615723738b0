package com.bees360.entity.vo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.bees360.entity.User;
import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.dto.IdNameListDto;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.enums.UserActiveStatusEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class UserProjectVo {

	private final static String CERTIFICATE_SEPARATOR = ",";

	public UserProjectVo() {}

	private List<IdNameDto> listRoles(long roles){
		List<RoleEnum> roleList = RoleEnum.listRoles(roles);
		List<IdNameDto> roleIdNames = new ArrayList<IdNameDto>(roleList.size());
		for(RoleEnum role: roleList){
			roleIdNames.add(new IdNameDto(role.getRoleId(), role.getDisplay()));
		}
		return roleIdNames;
	}
	private List<IdNameListDto> listRoleApplications(){
		User user = new User();
		user.setRoleApplicationStatus(roleApplicationStatus);
		user.setCertificateList(certificateList);
		Map<RoleEnum, List<String>> certificateMap = user.listRoleApplicationsWithCertificates();
		List<IdNameListDto> roleIdNameLists = new ArrayList<IdNameListDto>();
		for(Entry<RoleEnum, List<String>> entry: certificateMap.entrySet()) {
			RoleEnum roleEnum = entry.getKey();
			List<String> certificates = entry.getValue();
			roleIdNameLists.add(new IdNameListDto(roleEnum.getCode(), roleEnum.getDisplay(), certificates));
		}
		return roleIdNameLists;
	}

	private long userId;
	private String firstName;
	private String lastName;
	private String email;
	private String phone;
	private String avatar;
	private long registrationTime;
	// 1: means active, 0: inactive, -1: delete, -2: banned
	private int activeStatus;

	private long roles;
	private long roleApplicationStatus;
	private String certificateList;

	private Long companyId;
	private String companyName;

	private int projectCount;
	private String projectIdsStr;

	private double discountPercent;
	private double newCustomerDiscountPercent;
	private int newCustomerDiscountProjectNum;

	public String[] getProjectIds() {
		return projectIdsStr == null ? new String[0] : projectIdsStr.split(CERTIFICATE_SEPARATOR);
	}

	public List<IdNameListDto> getRoleApplicationList(){
		return listRoleApplications();
	}
	// special getter for return to fond end view
	public List<IdNameDto> getRoleList(){
		return listRoles(roles);
	}
	public int getProjectCount() {
		return projectCount;
	}
	public void setProjectCount(int projectCount) {
		this.projectCount = projectCount;
	}
	@JsonIgnore
	public String getProjectIdsStr() {
		return projectIdsStr;
	}
	public void setProjectIds(String projectIds) {
		this.projectIdsStr = projectIds;
	}
	public long getUserId() {
		return userId;
	}
	public void setUserId(long userId) {
		this.userId = userId;
	}
	public String getName() {
		return firstName + " " + lastName;
	}
	@JsonIgnore
	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	@JsonIgnore
	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getAvatar() {
		return avatar;
	}
	public void setAvatar(String avatar) {
		this.avatar = avatar;
	}
	public long getRegistrationTime() {
		return registrationTime;
	}
	public void setRegistrationTime(long registrationTime) {
		this.registrationTime = registrationTime;
	}
	@JsonIgnore
	public long getRoleApplicationStatus() {
		return roleApplicationStatus;
	}

	public void setRoleApplicationStatus(long roleApplicationStatus) {
		this.roleApplicationStatus = roleApplicationStatus;
	}


	@JsonIgnore
	public String getCertificateList() {
		return certificateList;
	}

	public void setCertificateList(String certificateList) {
		this.certificateList = certificateList;
	}

	public IdNameDto getStatus() {
		UserActiveStatusEnum status = UserActiveStatusEnum.getEnum(activeStatus);
		IdNameDto idName = new IdNameDto(status.getCode(), status.getDisplay());
		return idName;
	}
	@JsonIgnore
	public int getActiveStatus() {
		return activeStatus;
	}
	public void setActiveStatus(int activeStatus) {
		this.activeStatus = activeStatus;
	}
	@JsonIgnore
	public long getRoles() {
		return roles;
	}
	public void setRoles(long roles) {
		this.roles = roles;
	}
	public Long getCompanyId() {
		return companyId;
	}
	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public double getDiscountPercent() {
		return discountPercent;
	}

	public void setDiscountPercent(double discountPercent) {
		this.discountPercent = discountPercent;
	}
	public double getNewCustomerDiscountPercent() {
		return newCustomerDiscountPercent;
	}

	public void setNewCustomerDiscountPercent(double newCustomerDiscountPercent) {
		this.newCustomerDiscountPercent = newCustomerDiscountPercent;
	}

	public int getNewCustomerDiscountProjectNum() {
		return newCustomerDiscountProjectNum;
	}

	public void setNewCustomerDiscountProjectNum(int newCustomerDiscountProjectNum) {
		this.newCustomerDiscountProjectNum = newCustomerDiscountProjectNum;
	}
}
