package com.bees360.entity.enums;

import java.util.ArrayList;
import java.util.List;

public enum SegmentImageDirectionEnum implements BaseCodeEnum {
	ROOF(0, "50", RealtimeElementTypeEnum.APP_DAMAGE_OVERVIEW, null, ImageTypeEnum.CELLPHONE_ROOF),
	ROOF_FRONT(1, "55", RealtimeElementTypeEnum.APP_DAMAGE_FRONT, OrientationEnum.FRONT, ImageTypeEnum.CELLPHONE_ROOF),
	ROOF_BACK(2, "60", RealtimeElementTypeEnum.APP_DAMAGE_BACK, OrientationEnum.RIGHT, ImageTypeEnum.CELLPHONE_ROOF),
	ROOF_LEFT(3, "65", RealtimeElementTypeEnum.APP_DAMAGE_LEFT, OrientationEnum.BACK, ImageTypeEnum.CELLPHONE_ROOF),
	ROOF_RIGHT(4, "70", RealtimeElementTypeEnum.APP_DAMAGE_RIGHT, OrientationEnum.LEFT, ImageTypeEnum.CELLPHONE_ROOF),
	ELEVATION_ADDRESS(5, "75", RealtimeElementTypeEnum.APP_DAMAGE_CLOSE_UP, null, ImageTypeEnum.ELEVATION),
	ELEVATION_FRONT(6, "80", RealtimeElementTypeEnum.APP_DAMAGE_SLOPE, OrientationEnum.FRONT, ImageTypeEnum.ELEVATION),
	ELEVATION_RIGHT(7, "85", RealtimeElementTypeEnum.APP_DAMAGE_SLOPE, OrientationEnum.RIGHT, ImageTypeEnum.ELEVATION),
	ELEVATION_BACK(8, "90", RealtimeElementTypeEnum.APP_DAMAGE_SLOPE, OrientationEnum.BACK, ImageTypeEnum.ELEVATION),
	ELEVATION_LEFT(9, "95", RealtimeElementTypeEnum.APP_DAMAGE_SLOPE, OrientationEnum.LEFT, ImageTypeEnum.ELEVATION),
    UNDERWRITING_ELEVATION_FRONT(10, "10", RealtimeElementTypeEnum.APP_DAMAGE_SLOPE, OrientationEnum.FRONT, ImageTypeEnum.ELEVATION),
    UNDERWRITING_ELEVATION_RIGHT(11, "30", RealtimeElementTypeEnum.APP_DAMAGE_SLOPE, OrientationEnum.RIGHT, ImageTypeEnum.ELEVATION),
    UNDERWRITING_ELEVATION_BACK(12, "50", RealtimeElementTypeEnum.APP_DAMAGE_SLOPE, OrientationEnum.BACK, ImageTypeEnum.ELEVATION),
    UNDERWRITING_ELEVATION_LEFT(13, "70", RealtimeElementTypeEnum.APP_DAMAGE_SLOPE, OrientationEnum.LEFT, ImageTypeEnum.ELEVATION);

	public static final int DEFAULT = -1;

	private final int code;
	private final String display;
	private final RealtimeElementTypeEnum realtimeElementType;
	private final OrientationEnum orientation;
	private final ImageTypeEnum imageType;

	SegmentImageDirectionEnum(int code, String display, RealtimeElementTypeEnum realtimeElementType,
			OrientationEnum orientation, ImageTypeEnum imageType){
		this.code = code;
		this.display = display;
		this.realtimeElementType = realtimeElementType;
		this.orientation = orientation;
		this.imageType = imageType;
	}

	public static SegmentImageDirectionEnum getEnum(int code) {
		for(SegmentImageDirectionEnum direction: SegmentImageDirectionEnum.values()) {
			if(direction.getCode() == code) {
				return direction;
			}
		}
		return null;
	}

	public static SegmentImageDirectionEnum getByDisplay(String display) {
		for(SegmentImageDirectionEnum direction: SegmentImageDirectionEnum.values()) {
			if(direction.getDisplay().equals(display)) {
				return direction;
			}
		}
		return null;
	}

    public static SegmentImageDirectionEnum getUnderwritingDisplay(String display) {
	    List<SegmentImageDirectionEnum> underwritingList = new ArrayList<>();
        underwritingList.add(UNDERWRITING_ELEVATION_FRONT);
        underwritingList.add(UNDERWRITING_ELEVATION_RIGHT);
        underwritingList.add(UNDERWRITING_ELEVATION_BACK);
        underwritingList.add(UNDERWRITING_ELEVATION_LEFT);
        for(SegmentImageDirectionEnum direction: underwritingList) {
            if(direction.getDisplay().equals(display)) {
                return direction;
            }
        }
        return null;
    }

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public RealtimeElementTypeEnum getRealtimeElementType() {
		return realtimeElementType;
	}

	public OrientationEnum getOrientation() {
		return orientation;
	}

	public ImageTypeEnum getImageType() {
		return imageType;
	}

}
