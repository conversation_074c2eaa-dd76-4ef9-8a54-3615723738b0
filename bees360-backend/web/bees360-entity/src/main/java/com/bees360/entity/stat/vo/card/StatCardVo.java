package com.bees360.entity.stat.vo.card;

import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.stat.vo.ClassifiedItem;
import lombok.Data;

import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class StatCardVo {

    private String cardName;
    private Integer type;
    private List<ServiceTypeVo> serviceTypes;
    private Integer count;
    private List<ClassifiedItem> items;
    private Double score;


    public void addDefaultServiceType(Set<Integer> defaultTypes){

        if (defaultTypes == null || defaultTypes.isEmpty()){
            return;
        }

        Set<Integer> exists = new HashSet<>();
        for (ServiceTypeVo serviceTypeVo : serviceTypes){
            final Integer serviceType = serviceTypeVo.getServiceType();
            if (exists.contains(serviceType)){
                continue;
            }
            exists.add(serviceType);
        }

        for (Integer search : defaultTypes){
            if (exists.contains(search)){
                continue;
            }

            final ServiceTypeVo serviceTypeVo = new ServiceTypeVo(search, ProjectServiceTypeEnum.getEnum(search).getValue(), 0, 0D);
            serviceTypes.add(serviceTypeVo);
        }

        serviceTypes.sort(Comparator.comparingInt(ServiceTypeVo::getServiceType).reversed());
    }
}
