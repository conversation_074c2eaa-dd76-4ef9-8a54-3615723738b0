package com.bees360.entity.dto;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.util.StringFieldUtil;

public class MapViewUserSearchOption extends BaseSearchOption{
	private Double centerLat;
	private Double centerLng;
	private Double radius;
	private String state;
	private String city;
	private String address;
	private String zipCode;
	private String name;
	private String email;
	private String phone;
	private String company;
	private Long companyId;
	private long rolesBitMap;

	private static Set<RoleEnum> rolesSearchable
		= new HashSet<RoleEnum>(Arrays.asList(RoleEnum.PILOT, RoleEnum.ADJUSTER));
	private static int DEFAULT_PAGE_INDEX = 1;

	public MapViewUserSearchOption() {
	    super();
	    this.setPageIndex(DEFAULT_PAGE_INDEX);
	    this.setPageSize(MAX_PAGESIZE);
	}

	public boolean isSearchGPS() {
		return centerLat != null && centerLng != null;
	}
	public void setRoles(String roles) {
		if(roles == null) {
			rolesBitMap = 0;
			return;
		}
		String[] roleIds = roles.split(",");
		for(String roleId: roleIds) {
			try {
				int id = Integer.parseInt(roleId);
				RoleEnum role = RoleEnum.getEnum(id);
				if(role == null || !isRoleSearchable(role)) {
					continue;
				}
				rolesBitMap |= role.getBitMap();
			} catch(Exception e) {
			}
		}
	}

	private boolean isRoleSearchable(RoleEnum role) {
		return rolesSearchable.contains(role);
	}

	public String getAddressRegex() {
	    return getFuzzyRegex(address);
    }

    public String getNameRegex() {
        return getFuzzyRegex(name);
    }

    public String getEmailRegex() {
        return getFuzzyRegex(email);
    }

    public String getPhoneRegex() {
        return getFuzzyRegex(phone);
    }

    public String getCompanyRegex() {
        return getFuzzyRegex(company);
    }

    private String getFuzzyRegex(String input) {
        if(input == null) {
            return null;
        }
        return StringFieldUtil.joinAndWrap("%", input.toCharArray());
    }

    //* getter and setter *//
	public Double getCenterLat() {
		return centerLat;
	}
	public void setCenterLat(Double centerLat) {
		if(centerLat == null || centerLat < -90 || 90 < centerLat) {
			this.centerLat = null;
		} else {
			this.centerLat = centerLat;
		}
	}
	public Double getCenterLng() {
		return centerLng;
	}
	public void setCenterLng(Double centerLng) {
		if(centerLng == null || centerLng < -180 || 180 < centerLng) {
			this.centerLng = null;
		} else {
			this.centerLng = centerLng;
		}
	}
	public Double getRadius() {
		return radius;
	}
	public void setRadius(Double radius) {
		this.radius = radius == null? null: (radius > 0? radius: 0);
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}
	public Long getCompanyId() {
        return companyId;
    }
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
    public long getRolesBitMap() {
		return rolesBitMap;
	}
	public void setRolesBitMap(long rolesBitMap) {
		this.rolesBitMap = rolesBitMap;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getZipCode() {
		return zipCode;
	}
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	@Override
    public void setSortKey(String sortKey) {
        this.sortKey = sortKey;
    }

	@Override
	public String toString() {
		return "MapViewUserSearchOption [centerLat=" + centerLat + ", centerLng=" + centerLng + ", radius=" + radius
				+ ", state=" + state + ", city=" + city + ", name=" + name + ", email=" + email + ", phone=" + phone
				+ ", company=" + company + ", rolesBitmap=" + rolesBitMap + "]";
	}
}
