package com.bees360.entity.dto;

import java.util.HashSet;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * @date 2019/06/10
 */
public class PaymentSearchOption extends BaseSearchOption {

    private Long userId;
    private Long projectId;

    // 支付方式 {@link PaymentMethodEnum#code}
    private Integer paymentMethod;
    // 支付渠道 {@link PaymentChannelEnum#code}
    private Integer channel;
    // 操作类型，表示收入或者支出 {@link PaymentOperationTypeEnum#code}
    private Integer operationType;
    // 支付时间区间查询
    private Long startTime;
    private Long endTime;

    private static Set<String> SORT_KEYS;
    private static String DEFAULT_SORT_KEY = "paidTime";
    private static EOrder DEFAULT_SORT_ORDER = EOrder.DESC;

    static {
        SORT_KEYS = new HashSet<String>();
        SORT_KEYS.add("userId");
        SORT_KEYS.add("projectId");
        SORT_KEYS.add("paymentMethod");
        SORT_KEYS.add("channel");
        SORT_KEYS.add("operationType");
        SORT_KEYS.add("paidTime");
    }

    public PaymentSearchOption() {
        super();
        setSortKey(DEFAULT_SORT_KEY);
        setSortOrder(DEFAULT_SORT_ORDER);
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Integer getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(Integer paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    @Override
    public void setSortKey(String sortKey) {
        this.sortKey = SORT_KEYS.contains(sortKey) ? sortKey : DEFAULT_SORT_KEY;
    }

    @Override
    public void setSortOrder(String sortOrder) {
        super.setSortOrder(sortOrder);
        if (this.sortOrder == null) {
            this.setSortOrder(DEFAULT_SORT_ORDER);
        }
    }

    @Override
    public void setSortOrder(EOrder sortOrder) {
        super.setSortOrder(sortOrder);
        if (this.sortOrder == null) {
            this.setSortOrder(DEFAULT_SORT_ORDER);
        }
    }

    @Override
    public String toString() {
        return "PaymentSearchOption [paymentMethod=" + paymentMethod + ", channel=" + channel + ", operationType="
            + operationType + ", startTime=" + startTime + ", endTime=" + endTime + ", pageIndex=" + pageIndex
            + ", pageSize=" + pageSize + ", sortKey=" + sortKey + ", sortOrder=" + sortOrder + "]";
    }
}
