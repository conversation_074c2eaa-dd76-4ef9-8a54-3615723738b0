package com.bees360.entity.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public enum ImageTypeEnum implements BaseCodeEnum{
	NOT_EDITABLE(-1, "Uneditable", ViewType.OTHER),
	<PERSON><PERSON><PERSON>(0, "Others", ViewType.OTHER),
	<PERSON>IGHBOR(1, "Neighbor", ViewType.DRONE_VIEW),
	OVERVIEW(2, "Overview", ViewType.DRONE_VIEW),
	BIRDVIEW(3, "Birdview", ViewType.DRONE_VIEW),
	CLOSEUP(4, "Closeup", ViewType.DRONE_VIEW),
	ADDRESS(5, "Address", ViewType.ADDRESS),
	E<PERSON>VA<PERSON><PERSON>(6, "Elevation", ViewType.ELEVATION),
	CELLPHONE_ROOF(7, "Go Roof", ViewType.ELEVATION),
	Overhead(8, "Overhead", ViewType.ELEVATION),
    COMMUNITY_GATE(9, "Gate of Community", ViewType.ELEVATION),
	<PERSON><PERSON><PERSON><PERSON>(10, "Zigzag", ViewType.DRONE_VIEW),
	ROOF_LAYER(11, "Roof Layer", ViewType.DRONE_VIEW),
	;

	public static enum ViewType{
		DRONE_VIEW,
		ADDRESS,
		ELEVATION,
		OTHER
		;
	}

	public static final int DEFAULT = -2;

	private final int code;
	private final String display;
	private final ViewType viewType;

	private final static Map<Integer, ImageTypeEnum> typeMap = new HashMap<Integer, ImageTypeEnum>();

	static {
		for(ImageTypeEnum type: ImageTypeEnum.values()) {
			typeMap.put(type.getCode(), type);
		}
	}

	ImageTypeEnum(int code, String display, ViewType viewType){
		this.code = code;
		this.display = display;
		this.viewType = viewType;
	}
	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public ViewType getViewType() {
		return viewType;
	}

	public static List<ImageTypeEnum> listByViewType(ViewType...types) {
			List<ImageTypeEnum> imageTypes = new ArrayList<ImageTypeEnum>();
			Set<ViewType> viewTypeFilter = new HashSet<ViewType>();
			for(ViewType type: types) {
				viewTypeFilter.add(type);
			}
			for(ImageTypeEnum imageType: ImageTypeEnum.values()) {
				if(viewTypeFilter.contains(imageType.getViewType())) {
					imageTypes.add(imageType);
				}
			}
		return imageTypes;
	}

	public static ImageTypeEnum getEnum(Integer claimTypeCode){
		return typeMap.get(claimTypeCode);
	}

	public static void main(String[] args) {
		show();
	}

	private static void show() {
		System.out.println("|name|code|");
		System.out.println("|----|----|");
		for(ImageTypeEnum type: ImageTypeEnum.values()) {
			System.out.println("|" + type.getDisplay() + "|" + type.getCode() + "|" );
		}
	}
}
