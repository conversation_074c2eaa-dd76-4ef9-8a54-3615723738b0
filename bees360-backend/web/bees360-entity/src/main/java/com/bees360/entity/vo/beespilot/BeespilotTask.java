package com.bees360.entity.vo.beespilot;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020/7/9 9:01 PM
 **/
@Data
public class BeespilotTask {
    /**
     * 任务类型
     */
    private int id;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 该任务是否完成
     */
    private boolean finished;
    /**
     * @see com.bees360.entity.enums.DroneFlyPatternEnum
     */
    private int droneFlyPattern;
    /**
     * 照片是否压缩
     */
    private boolean compressed;

    /**
     * ratio 压缩比
     * ratio 值是 (0-1]
     * 1表示不压缩,
     * 可能为空，空表示不压缩
     */
    private Double compressRatio;
}
