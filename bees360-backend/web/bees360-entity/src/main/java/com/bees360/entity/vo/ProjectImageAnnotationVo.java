package com.bees360.entity.vo;

import com.bees360.entity.ProjectImage;
import lombok.Data;

@Data
public class ProjectImageAnnotationVo extends ProjectImage {

    // the annotation types
    private Integer reportType;

    private String caption;

    private String alias;

    private boolean isInReport = true;

    public boolean getIsInReport() {
        return isInReport;
    }

    public void setIsInReport(boolean inReport) {
        isInReport = inReport;
    }

}
