package com.bees360.entity.firebase;

import com.bees360.entity.enums.BaseCodeEnum;
import com.bees360.entity.enums.ProcessStatusEnum;

import java.util.Arrays;

/**
 * 项目的状态 In Firebase
 */
public enum FirebaseProjectStatusEnum implements BaseCodeEnum {
    /**
     * 项目创建
     */
    NEW_PROJECT(10, "Project created"),
    /**
     * 联系客户
     */
    CUSTOMER_CONTACTED(30, "Customer Contacted"),
    /**
     * 项目预分配给飞手，等待飞手接收
     */
    PENDING_ACCEPTANCE(40, "Pending Acceptance"),
    /**
     * 取消分配
     */
    MISSION_CANCELLED(44, "Missing Cancelled"),

    /**
     * 飞手拒绝接受mission
     */
    MISSION_REJECTED(45, "Missing rejected"),

    /**
     * 项目已经分配给飞手
     */
    ASSIGNED_TO_PILOT(50, "Assigned to Pilot"),

    /**
     * project image需要重新拍摄
     */
    PROJECT_REWORK(60, "Project Rework"),
    /**
     * 现场检测完成，也就是采集照片完成
     */
    SITE_INSPECTED(70, "Site Inspected"),

    /**
     * 采集照片完成并全部上传到了我们平台上
     */
    IMAGE_UPLOADED(80, "Image Uploaded"),

    /**
     * 报告均已提交给客户，此时所有的报告的状态均为APPROVED状态
     */
    RETURNED_TO_CLIENT(90, "Returned to Client"),

    CLIENT_RECEIVED(100, "Client Received"),

    /**
     * client received error report.
     */
    RECEIVE_ERROR(101, "Receive Error"),

    /**
     * 项目取消
     */
    PROJECT_CANCELED(110, "Project Canceled"),
    ;

    /**
     * 不可变不可重复状态值
     */
    private final int code;

    /**
     * 可变状态值名称
     */
    private final String display;


    FirebaseProjectStatusEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    public int getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }

    public static FirebaseProjectStatusEnum getEnum(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values()).filter(status -> status.getCode() == code).findFirst().orElse(null);
    }

    @Override
    public String toString() {
        return getClass().getCanonicalName() + "[code: " + code + ", display: " + display + ']';
    }
}
