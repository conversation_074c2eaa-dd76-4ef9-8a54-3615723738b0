package com.bees360.entity.enums;

import com.bees360.project.Message.ProjectStatus;
import java.util.Arrays;
import java.util.Objects;
import java.util.function.Function;
import lombok.NonNull;

/**
 * 项目的状态
 *
 * <AUTHOR>
 * @date 2019/12/26 10:46
 */
public enum NewProjectStatusEnum implements BaseCodeEnum{
    /**
     * 项目创建成功
     */
    PROJECT_CREATED(ProjectStatus.PROJECT_CREATED, "Project Created", "Project Created", ProcessStatusEnum.NEW, true, 10),
    /**
     * 联系客户
     */
    CUSTOMER_CONTACTED(ProjectStatus.CUSTOMER_CONTACTED, "Customer Contacted", "Customer Contacted", ProcessStatusEnum.NEW, true, 30),

    /**
     * 项目预分配给飞手，等待飞手接收
     */
    PENDING_ACCEPTANCE(ProjectStatus.PENDING_ACCEPTANCE, "Pending Acceptance", "Pending Acceptance", ProcessStatusEnum.NEW, true, 40),

    /**
     * 项目已经分配给飞手
     */
    ASSIGNED_TO_PILOT(ProjectStatus.ASSIGNED_TO_PILOT, "Assigned to Pilot", "Assigned to Pilot", ProcessStatusEnum.NEW, true, 50),

    /**
     * project image需要重新拍摄
     */
    PROJECT_REWORK(ProjectStatus.PROJECT_REWORK, "Project Rework", "Project Rework", ProcessStatusEnum.NEW, true, 60),
    /**
     * 现场检测完成，也就是采集照片完成
     */
    SITE_INSPECTED(ProjectStatus.SITE_INSPECTED, "Site Inspected", "Site Inspected", ProcessStatusEnum.IMAGE_UPLOADED, true, 70),

    /**
     * ibees上传图片全部上传到了我们平台上
     */
    IBEES_UPLOADED(ProjectStatus.IBEES_UPLOADED, "IBees Uploaded", "IBees Uploaded", ProcessStatusEnum.IMAGE_UPLOADED, true, 75),

    /**
     * 采集照片完成并全部上传到了我们平台上
     */
    IMAGE_UPLOADED(ProjectStatus.IMAGE_UPLOADED, "Image Uploaded", "Image Uploaded", ProcessStatusEnum.IMAGE_UPLOADED, true, 80),

    /**
     * 报告均已提交给客户，此时所有的报告的状态均为APPROVED状态
     */
    RETURNED_TO_CLIENT(ProjectStatus.RETURNED_TO_CLIENT, "Returned to Client", "Returned to Client", ProcessStatusEnum.REPORT_APPROVED, true, 90),
    /**
     *
     */
    CLIENT_RECEIVED(ProjectStatus.CLIENT_RECEIVED, "Client Received", "Client Received", ProcessStatusEnum.CLIENT_RECEIVED, true, 100),

    RECEIVE_ERROR(ProjectStatus.RECEIVE_ERROR, "Receive Error", "Receive Error", ProcessStatusEnum.REPORT_APPROVED, true, 101),
    /**
     * 项目取消
     */
    PROJECT_CANCELED(ProjectStatus.PROJECT_CANCELED, -1, "Project Canceled", "Project Canceled", ProcessStatusEnum.CANCELED, true, 110)

    ;

    /**
     * 不可变不可重复状态值
     */
    private final ProjectStatus code;

    /**
     * 因为AI端定义状态的code值与web端的不一致，需要转换
     * TODO 此处是否需要统一。但是目前比较麻烦（因为数据记录的数据源不只有mysql）
     */
    private final int aiCode;
    /**
     * 不可变不可重复字符串状态值
     */
    private final String value;
    /**
     * 可变状态值名称
     */
    private final String display;
    private final ProcessStatusEnum processStatus;
    /**
     * timeline 中是否可显示
     */
    private boolean showInTimeline;
    /**
     * timeline 中的顺序，从小到大
     */
    private int order;


    NewProjectStatusEnum(@NonNull ProjectStatus code, String value, String display, ProcessStatusEnum processStatus,
        boolean showInTimeline, int order) {
        this.code = code;
        this.aiCode = code.getNumber();
        this.value = value;
        this.display = display;
        this.processStatus = processStatus;
        this.showInTimeline = showInTimeline;
        this.order = order;
    }

    NewProjectStatusEnum(@NonNull ProjectStatus code, int aiCode, String value, String display, ProcessStatusEnum processStatus,
        boolean showInTimeline, int order) {
        this.code = code;
        this.aiCode = aiCode;
        this.value = value;
        this.display = display;
        this.processStatus = processStatus;
        this.showInTimeline = showInTimeline;
        this.order = order;
    }

    public int getCode() {
        return code.getNumber();
    }

    public int getAiCode() {
        return aiCode;
    }

    public String getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    public boolean showInTimeline(){
        return showInTimeline;
    }

    public int getOrder(){
        return order;
    }

    public ProcessStatusEnum getProcessStatus() {
        return processStatus;
    }

    public static boolean containsAiCode(Integer aiCode) {
        return Objects.nonNull(getEnumByAiCode(aiCode));
    }

    public static NewProjectStatusEnum getEnum(Integer code) {
        return getStatusEnum(status -> Objects.equals(status.getCode(), code));
    }

    public static NewProjectStatusEnum getEnumByValue(String value) {
        return getStatusEnum(status -> Objects.equals(status.getValue(), value));
    }

    public static NewProjectStatusEnum getEnumByAiCode(Integer aiCode) {
        return getStatusEnum(status -> Objects.equals(status.getAiCode(), aiCode));
    }

    public static NewProjectStatusEnum getStatusEnum(Function<NewProjectStatusEnum, Boolean> matchFunction) {
        for (NewProjectStatusEnum status : values()) {
            if (matchFunction.apply(status)) {
                return status;
            }
        }
        return null;
    }

    public static boolean isProjectTaskCompleted(Integer code) {
        return Objects.nonNull(code) && Arrays.asList(IMAGE_UPLOADED.getCode(), RETURNED_TO_CLIENT.getCode(), CLIENT_RECEIVED.getCode()).contains(code);
    }

    public static boolean isAfterImageUploaded(Integer code) {
        return Objects.nonNull(code) && Arrays.asList(IMAGE_UPLOADED.getCode(), PROJECT_REWORK.getCode(),
            SITE_INSPECTED.getCode(), RETURNED_TO_CLIENT.getCode(), CLIENT_RECEIVED.getCode()
        ).contains(code);
    }

    public static boolean isProjectReturnToClient(Integer code) {
        return Objects.nonNull(code) && Arrays.asList(RETURNED_TO_CLIENT.getCode(), CLIENT_RECEIVED.getCode()).contains(code);
    }


    @Override
    public String toString() {
        return getClass().getCanonicalName() + "[code: " + code + ", display: " + display + ']';
    }
}
