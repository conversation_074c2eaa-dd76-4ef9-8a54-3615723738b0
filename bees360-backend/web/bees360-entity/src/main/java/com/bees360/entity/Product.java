package com.bees360.entity;

import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.productandpayment.PilotTypeEnum;
import com.bees360.entity.enums.productandpayment.ProductTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class Product {
	private int productId;
	private int productType;
	private int internalType;
	private int priceType;

	private Double price;
	private String subtitle;
	private String caption;
	private String url;

	@JsonIgnore
	private long createdTime;
	@JsonIgnore
	private long updatedTime;

	//** getter and setter **//
	public int getProductId() {
		return productId;
	}
	public void setProductId(int productId) {
		this.productId = productId;
	}
	public int getProductType() {
		return productType;
	}
	public void setProductType(int productType) {
		this.productType = productType;
	}
	public int getInternalType() {
		return internalType;
	}
	public void setInternalType(int internalType) {
		this.internalType = internalType;
	}
	public int getPriceType() {
		return priceType;
	}
	public void setPriceType(int priceType) {
		this.priceType = priceType;
	}
	public Double getPrice() {
		return price;
	}
	public void setPrice(Double price) {
		this.price = price;
	}
	public String getSubtitle() {
		return subtitle;
	}
	public void setSubtitle(String subtitle) {
		this.subtitle = subtitle;
	}
	public String getCaption() {
		return caption;
	}
	public void setCaption(String caption) {
		this.caption = caption;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public long getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}
	public long getUpdatedTime() {
		return updatedTime;
	}
	public void setUpdatedTime(long updatedTime) {
		this.updatedTime = updatedTime;
	}

	public String getProductName() {
		if(productType == ProductTypeEnum.REPORT.getCode()) {
			ReportTypeEnum reportType = ReportTypeEnum.getEnum(internalType);
			return reportType == null? "": reportType.getDisplay();
		} else if (productType == ProductTypeEnum.PILOT.getCode()) {
			PilotTypeEnum pilotType = PilotTypeEnum.getEnum(internalType);
			return pilotType == null? "": pilotType.getDisplay();
		}
		return "";
	}
}
