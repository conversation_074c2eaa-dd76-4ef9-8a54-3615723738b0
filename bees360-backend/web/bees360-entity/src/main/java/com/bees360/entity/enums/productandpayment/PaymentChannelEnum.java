package com.bees360.entity.enums.productandpayment;

import com.bees360.entity.enums.BaseCodeEnum;

public enum PaymentChannelEnum implements BaseCodeEnum {
	NONE(0, "None"),
	BALANCE(1, "Balance"),
	STRIPE(2, "Stripe"),
	SQUAREUP(3, "Squareup"),
	PAYPAL(4, "PayPal")
	;

	private final int code;
	private final String display;

	PaymentChannelEnum(int code, String display) {
		this.code = code;
		this.display = display;
	}

	public int getCode() {
		return code;
	}
	public String getDisplay() {
		return display;
	}

	public static PaymentChannelEnum getEnum(int code) {
		for(PaymentChannelEnum e: PaymentChannelEnum.values()) {
			if(e.getCode() == code) {
				return e;
			}
		}
		return null;
	}
}
