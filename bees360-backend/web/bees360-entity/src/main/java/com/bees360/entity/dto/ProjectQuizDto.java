package com.bees360.entity.dto;

import com.bees360.entity.enums.QuizTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020/4/10 12:11 PM
 **/
@Data
@ToString
public class ProjectQuizDto {
    /** used when Quiz type is multiple-choice or date-fill-in-the-blanks **/
    @JsonIgnore
    public static final String ANSWER_SPLITTER = "::";

    @JsonIgnore
    private static final String ANSWER_SPLITTER_DISPLAY = ", ";

    /** 补充说明分割 **/
    @JsonIgnore
    public static final String ANSWER_SPLITTER_COMMENT = "##";

    private long quizId;

    private long projectId;

    /** 题干 **/
    private String subject;

    /**
     * @see com.bees360.entity.enums.ClaimTypeEnum
     */
    private int claimType;

    /**
     * 题目类型
     * @see com.bees360.entity.enums.QuizTypeEnum
     */
    private int type;

    /**
     * 如果是题目类型`type`为选择题，此处为备选项
     * 如果题目类型为`type`为日期类型，此处为日期格式
     */
    private String[] choices;

    /** 题目排序号 **/
    private int sequence;

    /** 被问卷者最近一次回答的答案 **/
    private String[] answers;

    /** 被问卷者最近一次回答的答案 **/
    private String answer;

    /** 补充说明 **/
    private String comment;

    public void setAnswers(String[] answers) {
        this.answer = String.join(ANSWER_SPLITTER, answers);
        this.answers = answers;
    }

    public void setAnswer(String answer) {
        QuizTypeEnum typeEnum = QuizTypeEnum.valueOf(type);
        if (Objects.isNull(typeEnum)) {
            this.answer = answer;
            return;
        }
        switch (typeEnum) {
            case MULTI_CHOICE:
                this.answers = answer.split(ANSWER_SPLITTER);
                this.answer = String.join(ANSWER_SPLITTER_DISPLAY, this.answers);
                break;

            case FILL_MANY_DIGIT_BLANKS:
            case FILL_MANY_BLANKS:
                this.answers = answer.split(ANSWER_SPLITTER);
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < this.answers.length; i++) {
                    sb.append(this.answers[i]);
                    if (choices != null && i < this.choices.length) {
                        sb.append(" ").append(choices[i]);
                    }
                    sb.append(", ");
                }
                sb.delete(sb.length() - 2, sb.length());
                this.answer = sb.toString();
                break;

            case BOOLEAN_NO_BLANKS:
                String[] split = answer.split(ANSWER_SPLITTER_COMMENT);
                if (split.length >= 2) {
                    this.comment = split[1];
                }
                this.answers = split[0].split(ANSWER_SPLITTER);
                this.answer = answer;
                break;

            default:
                this.answers = answer.split(ANSWER_SPLITTER);
                this.answer = answer;
        }
    }
}
