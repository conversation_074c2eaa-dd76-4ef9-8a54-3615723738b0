package com.bees360.entity.vo;

import com.bees360.entity.dto.CodeNameDto;
import com.bees360.entity.dto.IdNameDto;

import com.bees360.entity.enums.NewProjectStatusEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/26 11:45
 */
@Data
@NoArgsConstructor
public class ProjectStatusVo {
    private CodeNameDto status;
    private Long createdTime;
    private String userName;

    private boolean showRework;

    public ProjectStatusVo(NewProjectStatusEnum status) {
        this.status = new CodeNameDto(status.getCode(), status.getDisplay());
    }

    @Data
    @NoArgsConstructor
    public static class ProjectStatusVoListWrapper {
        private List<ProjectStatusVo> statuses;

        public ProjectStatusVoListWrapper(List<ProjectStatusVo> statuses) {
            this.statuses = statuses;
        }
    }

    public boolean available() {
        return createdTime != null;
    }
}
