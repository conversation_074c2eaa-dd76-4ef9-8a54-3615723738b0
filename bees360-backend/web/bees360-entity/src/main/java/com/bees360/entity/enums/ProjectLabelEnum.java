package com.bees360.entity.enums;

import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Nullable;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public enum ProjectLabelEnum {
    CANCELLATION_TBD("Cancellation TBD", 1L),
    DENIED("Denied", 2L),
    NOT_CLOSED("Not Closed", 3L),
    WRONG_DOES_NOT_BELONG_TO_INSURED("Wrong #/ # Doesn't Belong To Insured", 4L),
    NO_RESPONSE_FROM_INSURED("No response from insured", 5L),
    NO_RESPONSE_FROM_PA("No response from PA", 6L),
    NO_RESPONSE_FROM_ROOFER("No response from roofer", 7L),
    DRONE_INSPECTION_DECLINE("Drone inspection declined", 8L),
    NO_LOR_ON_FILE("No LOR on file", 9L),
    IBEES_NOT_COMPLETED("IBees not completed", 10L),
    CANCELLATION_UNCONFIRMED("Cancellation Unconfirmed", 11L),
    PENDING_TO_RESCHEDULE("Pending to ReSchedule", 12L),
    PENDING_TO_SCHEDULE("Pending to schedule", 13L),
    RESTRICTED_AIRSPACE("Restricted airspace", 14L),
    INSURED_NOT_AT_HOME("Insured not at home", 15L),
    INCLEMENT_WEATHER("Inclement weather", 16L),
    PERSONAL_ACCIDENT("Personal accident", 17L),
    TECH_PROBLEMS("Tech problems", 18L),
    INSURED_COVID_QUARANTINE("Insured COVID quarantine", 19L),
    UNABLE_TO_REACH_INSURED_AFTER_MULTIPLE_ATTEMPTS("Unable to reach insured after multiple attempts", 20L),
    MISSING_INTERIOR_IMAGES("Missing interior images", 22L),
    DENIED_ON_LOCATION("Denied on location", 23L),
    MAGICPLAN_MISSING("Magicplan Missing", 24L),
    CANCELLATION_CONFIRMED("Cancellation Confirmed", 26L),
    ATTENTION_NEEDED_FOR_LOSS_TYPE("Attention Needed for Loss Type", 27L),
    INSPECTOR_NEEDED("Inspector Needed", 28L),
    FAMILY_EMERGENCY("Family emergency", 29L),
    POLICY_CANCELED("Policy Canceled", 30L),
    DENIED_INSURED_UNAWARE("Denied - Insured Unaware", 31L),
    DENIED_INSURED_CONSIDER_UNNECESSARY("Denied - Insured Consider Unnecessary", 32L),
    DENIED_COMPLETED_BY_SOMEONE_ELSE("Denied - Completed by Someone Else", 33L),
    DENIED_UNKNOWN_REASON("Denied - Insured didn't specify the reason", 34L),
    PILOT_REJECT_MISSION("Pilot Reject Mission", 35L),
    ;
    private String labelName;
    private Long labelId;

    private static final Set<ProjectLabelEnum> CANCELLATION_TAGS =
        Set.of(
            CANCELLATION_CONFIRMED,
            CANCELLATION_UNCONFIRMED,
            DENIED,
            DENIED_ON_LOCATION,
            UNABLE_TO_REACH_INSURED_AFTER_MULTIPLE_ATTEMPTS);
    private static final Set<Long> CANCELLATION_TAG_IDS =
            CANCELLATION_TAGS.stream().map(ProjectLabelEnum::getLabelId).collect(Collectors.toSet());


    private static final Set<Long> PENDING_SCHEDULE_TAGS =
            Set.of(
                    NO_LOR_ON_FILE.getLabelId(),
                    DRONE_INSPECTION_DECLINE.getLabelId());

    ProjectLabelEnum(String labelName, Long labelId) {
        this.labelName = labelName;
        this.labelId = labelId;
    }

    public String getLabelName() {
        return labelName;
    }

    public Long getLabelId() {
        return labelId;
    }

    public static boolean isCancellation(Long labelId) {
        return labelId != null && CANCELLATION_TAG_IDS.contains(labelId);
    }

    @Nullable
    public static ProjectLabelEnum getEnum(Long labelId) {
        for (var value : values()) {
            if (Objects.equals(labelId, value.getLabelId())) {
                return value;
            }
        }

        return null;
    }

    public static ProjectLabelEnum getEnumByCancelReason(String cancelReason) {
        return CANCELLATION_TAGS.stream()
                .filter(
                        tag ->
                                StringUtils.isNoneBlank(cancelReason)
                                        && StringUtils.equalsIgnoreCase(
                                                tag.getLabelName(), cancelReason))
                .findAny()
                .orElse(null);
    }

    public static boolean isPendingSchedule(Long labelId) {
        return labelId != null && PENDING_SCHEDULE_TAGS.contains(labelId);
    }

    public static ProjectLabelEnum getEnum(String labelName) {
        for (var value : values()) {
            if (Objects.equals(labelName, value.getLabelName())) {
                return value;
            }
        }

        return null;
    }
}
