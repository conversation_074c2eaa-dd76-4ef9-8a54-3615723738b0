package com.bees360.entity.vo;

import java.util.List;

import com.bees360.entity.enums.ImageTypeEnum;

/**
 * <AUTHOR>
 * @date 2019/09/16 12:10
 */
public class ImageCategory {

    /** @see ImageTypeEnum#getDisplay() **/
    private String type;
    /** @see ImageTypeEnum#getCode() **/
    private int code;
    private List<TinyImageVo> images;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public List<TinyImageVo> getImages() {
        return images;
    }

    public void setImages(List<TinyImageVo> images) {
        this.images = images;
    }

    @Override
    public String toString() {
        return "ImageCategory{" + "type='" + type + '\'' + ", code=" + code + ", images=" + images + '}';
    }
}
