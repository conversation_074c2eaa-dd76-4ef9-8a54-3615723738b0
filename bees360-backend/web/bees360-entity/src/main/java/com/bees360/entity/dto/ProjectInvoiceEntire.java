package com.bees360.entity.dto;

import com.bees360.entity.Invoice;
import com.bees360.entity.InvoiceDiscount;
import com.bees360.entity.InvoiceItem;

import java.util.List;

/**
 * Project的一个完整的Invoice所包含的所有信息。
 * <AUTHOR>
 * @date 2019/10/15
 */
public class ProjectInvoiceEntire {
    private Invoice invoice;
    private List<InvoiceItem> invoiceItems;
    private List<InvoiceDiscount> invoiceDiscounts;

    public Invoice getInvoice() {
        return invoice;
    }

    public void setInvoice(Invoice invoice) {
        this.invoice = invoice;
    }

    public List<InvoiceItem> getInvoiceItems() {
        return invoiceItems;
    }

    public void setInvoiceItems(List<InvoiceItem> invoiceItems) {
        this.invoiceItems = invoiceItems;
    }

    public List<InvoiceDiscount> getInvoiceDiscounts() {
        return invoiceDiscounts;
    }

    public void setInvoiceDiscounts(List<InvoiceDiscount> invoiceDiscounts) {
        this.invoiceDiscounts = invoiceDiscounts;
    }

    @Override
    public String toString() {
        return "ProjectInvoiceEntire{" + "invoice=" + invoice + ", invoiceItems=" + invoiceItems + ", invoiceDiscounts="
            + invoiceDiscounts + '}';
    }
}
