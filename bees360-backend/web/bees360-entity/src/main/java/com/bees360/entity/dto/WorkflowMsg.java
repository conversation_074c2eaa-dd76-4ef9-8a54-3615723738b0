package com.bees360.entity.dto;

import java.util.HashMap;
import java.util.Map;

import com.bees360.entity.Notification.NotificationType;

public class WorkflowMsg implements WebSocketMsgData{
	private long projectId;
	private long recipient;
	private String description;
	private int status;

	public static enum EStep {
		RANGING(1),
		SCOPING(2),
		PLANE(3),
		BOUNDARY(4),
		DAMAGE(5)
		;
		private final int code;

		EStep(int code) {
			this.code = code;
		}

		public int getCode() {
			return code;
		}
	}

	public WorkflowMsg() {}

	public WorkflowMsg(long projectId, long recipient, String description, EStep step) {
		this.projectId = projectId;
		this.recipient = recipient;
		this.description = description;
		this.status = step.getCode();
	}

	public WorkflowMsg(long projectId, long recipient, String description, int status) {
		this.projectId = projectId;
		this.recipient = recipient;
		this.description = description;
		this.status = status;
	}
	private final NotificationType type = NotificationType.WORKFLOW_NOTIFICATION;


	@Override
	public NotificationType getType() {
		return type;
	}

	@Override
	public Map<String, Object> toWebSocketMsgFieldMap() {
		Map<String, Object> websocketMap = new HashMap<String, Object>();
		websocketMap.put("projectId", projectId);
		websocketMap.put("recipient", recipient);
		websocketMap.put("description", description);
		websocketMap.put("status", status);
		return websocketMap;
	}

	/* getter and setter */
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public long getRecipient() {
		return recipient;
	}
	public void setRecipient(long recipient) {
		this.recipient = recipient;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(EStep step) {
		this.status = step.getCode();
	}
	public void setStatus(int status) {
		this.status = status;
	}
}
