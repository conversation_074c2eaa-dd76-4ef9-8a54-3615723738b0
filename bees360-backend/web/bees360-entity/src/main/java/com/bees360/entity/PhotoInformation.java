package com.bees360.entity;

public class PhotoInformation {
	private String url;
	private double absoluteAltitude;
	private double relativeAltitude;
	private boolean isVisited;
	private int cluster;
	private boolean isNoised;
	public PhotoInformation(){

	}
	public PhotoInformation(String url, double absoluteAltitude, double relativeAltitude){
		this.url = url;
		this.absoluteAltitude = absoluteAltitude;
		this.relativeAltitude = relativeAltitude;
		isVisited = false;
		cluster = 0;
		isNoised = false;
	}
	public double getAbsoluteAltitude() {
		return absoluteAltitude;
	}
	public void setAbsoluteAltitude(double absoluteAltitude) {
		this.absoluteAltitude = absoluteAltitude;
	}
	public double getRelativeAltitude() {
		return relativeAltitude;
	}
	public void setRelativeAltitude(double relativeAltitude) {
		this.relativeAltitude = relativeAltitude;
	}
	public boolean isVisited() {
		return isVisited;
	}
	public void setVisited(boolean isVisited) {
		this.isVisited = isVisited;
	}
	public int getCluster() {
		return cluster;
	}
	public void setCluster(int cluster) {
		this.cluster = cluster;
	}
	public boolean isNoised() {
		return isNoised;
	}
	public void setNoised(boolean isNoised) {
		this.isNoised = isNoised;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}

}
