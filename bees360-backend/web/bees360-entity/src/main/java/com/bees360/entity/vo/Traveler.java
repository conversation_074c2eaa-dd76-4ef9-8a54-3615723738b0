package com.bees360.entity.vo;

import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.user.User;
import lombok.Getter;


public class Traveler {
	private String userId;

    @Getter
    private Long uid;
	private String name;
	private String avatar;
	private String phone;
	private String email;
	private List<IdNameDto> roles;
	private Map<String, Double> baseGPS;

	private long companyId;
	private String companyName;
	private String companyLogo;

	private Double travelRadius;

	public Traveler() {
	}

	public Traveler(User user, List<RoleEnum> roles, Double travelRadius) {
		this.userId = user.getId();
		this.name = user.getName();
		this.avatar = Optional.ofNullable(user.getPhoto()).map(URL::toString).orElse(null);
		this.phone = user.getPhone();
		this.email = user.getEmail();
		setRolesByEnums(roles);
		this.travelRadius = travelRadius;
        this.uid = user.toMessage().getUid();
	}

	public void setWithTraveler(Traveler traveler) {
		setUserId(traveler.getUserId());
		setName(traveler.getName());
		setAvatar(traveler.getAvatar());
		setPhone(traveler.getPhone());
		setEmail(traveler.getEmail());
		setRoles(traveler.getRoles());
		setBaseGPS(traveler.getBaseGPS());
		setCompanyLogo(traveler.getCompanyLogo());
		setCompanyName(traveler.getCompanyName());

		setTravelRadius(traveler.getTravelRadius());
	}

	//* getter and setter *//
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getAvatar() {
		return avatar;
	}
	public void setAvatar(String avatar) {
		this.avatar = avatar;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public List<IdNameDto> getRoles() {
		return roles;
	}
	public void setRolesByEnums(List<RoleEnum> roles) {
		this.roles = new ArrayList<IdNameDto>();
		for(RoleEnum role: roles) {
			this.roles.add(new IdNameDto(role.getRoleId(), role.getDisplay()));
		}
	}
	public void setRoles(List<IdNameDto> roles) {
		this.roles = roles;
	}

	public long getCompanyId() {
        return companyId;
    }
    public void setCompanyId(long companyId) {
        this.companyId = companyId;
    }
    public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getCompanyLogo() {
		return companyLogo;
	}
	public void setCompanyLogo(String companyLogo) {
		this.companyLogo = companyLogo;
	}

	public void setBaseGPS(Map<String, Double> baseGPS) {
		this.baseGPS = baseGPS;
	}

	public Map<String, Double> getBaseGPS() {
		return baseGPS;
	}
	public void setBaseGPS(Double lat, Double lng) {
		Map<String, Double> baseGPS = new HashMap<String, Double>();
		baseGPS.put("lat", lat);
		baseGPS.put("lng", lng);
		this.baseGPS = baseGPS;
	}
	public Double getTravelRadius() {
		return travelRadius;
	}
	public void setTravelRadius(Double travelRadius) {
		this.travelRadius = travelRadius;
	}

	@Override
	public String toString() {
		return "Traveler [userId=" + userId + ", name=" + name + ", avatar=" + avatar + ", phone=" + phone + ", email="
				+ email + ", roles=" + roles + ", baseGPS=" + baseGPS + ", travelRadius=" + travelRadius + "]";
	}
}
