package com.bees360.entity;

import lombok.experimental.SuperBuilder;

/**
 *
 * <AUTHOR>
 * @contributors
 * @date 2017/12/21 15:04:08
 */
@SuperBuilder
public class ImageAnnotation extends BaseImageAnnotation {

	public ImageAnnotation() {
		super();
	}

	public ImageAnnotation(BaseImageAnnotation annotation) {
		this.annotationId = annotation.getAnnotationId();
		this.annotationPolygon = annotation.getAnnotationPolygon();
		this.annotationType = annotation.getAnnotationType();
		this.usageType = annotation.getUsageType();
		this.centerPointX = annotation.getCenterPointX();
		this.centerPointY = annotation.getCenterPointY();
		this.createdTime = annotation.getCreatedTime();
		this.facetId = annotation.getFacetId();
		this.generatedBy = annotation.getGeneratedBy();
		this.imageId = annotation.getImageId();
		this.projectId = annotation.getProjectId();
        this.sourceType = annotation.getSourceType();
	}

	public boolean isManuallyAnnotated() {
		return generatedBy != User.AI_ID;
	}

	@Override
	public String toString() {
		return "ImageAnnotation [annotationId=" + annotationId + ", imageId=" + imageId + ", facetId=" + facetId
				+ ", projectId=" + projectId + ", annotationPolygon=" + annotationPolygon + ", createdTime="
				+ createdTime + ", centerPointX=" + centerPointX + ", centerPointY=" + centerPointY
				+ ", annotationType=" + annotationType + ", usageType=" + usageType
                + ", generatedBy=" + generatedBy + ", sourceType=" + sourceType + "]";
	}
}
