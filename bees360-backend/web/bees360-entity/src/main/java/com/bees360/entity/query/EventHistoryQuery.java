package com.bees360.entity.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/7 4:44 PM
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EventHistoryQuery {
    private Long projectId;

    private List<Long> projectIdList;

    private Integer status;

    private List<Integer> statusList;
}
