package com.bees360.entity.dto;

import java.util.List;

public class ImagePlaceholderCarrier {
	private long projectId;
	private List<ImagePlaceholder> images;

	/* getter and setter */
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public List<ImagePlaceholder> getImages() {
		return images;
	}
	public void setImages(List<ImagePlaceholder> images) {
		this.images = images;
	}
	@Override
	public String toString() {
		return "ImagePlaceholderCarrier [projectId=" + projectId + ", images=" + images + "]";
	}
}
