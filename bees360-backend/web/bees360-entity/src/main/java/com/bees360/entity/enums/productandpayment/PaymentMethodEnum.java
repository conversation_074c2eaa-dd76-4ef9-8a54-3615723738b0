package com.bees360.entity.enums.productandpayment;

import java.util.Arrays;

import org.springframework.util.Assert;

public enum PaymentMethodEnum {

	BALANCE(0, "wallet", "Balance", PaymentChannelEnum.BALANCE),
	CREDIT_CARD(1, "credit_card", "Credit Card", PaymentChannelEnum.STRIPE),
	PAYPAL(2, "paypal", "PayPal", PaymentChannelEnum.PAYPAL), //UNUSED
	FREE_PROJECT(3, "free", "Free Project", PaymentChannelEnum.NONE);

	private final int paymentMethodId;
	// display 的值作为数据库与该Enum的关联值，请不要随意更改
	private final String display;
	private final String viewValue;
	private final PaymentChannelEnum paymentChannel;

	private PaymentMethodEnum(int paymentMethodId, String display, String viewValue,
			PaymentChannelEnum paymentChannel) {
		this.paymentMethodId = paymentMethodId;
		this.display = display;
		this.viewValue = viewValue;
		this.paymentChannel = paymentChannel;

		Assert.hasLength(display, "PaymentMethodEnum.display shouldn't be empty.");
		Assert.notNull(paymentChannel, "PaymentMethodEnum.paymentChannel shouldn't be null.");
	}

	public static PaymentMethodEnum getEnumById(int paymentMethodId) {
		return Arrays.asList(PaymentMethodEnum.values()).stream().filter(p -> p.getPaymentMethodId() == paymentMethodId)
				.findFirst().orElse(null);
	}

	public static PaymentMethodEnum getEnumByDisplay(String display) {
		return Arrays.asList(PaymentMethodEnum.values()).stream().filter(p -> p.getDisplay().equals(display))
				.findFirst().orElse(null);
	}

	public int getPaymentMethodId() {
		return paymentMethodId;
	}
	public String getDisplay() {
		return display;
	}
	public String getValue() {
		return getDisplay();
	}
	public String getViewValue() {
		return viewValue;
	}
	public PaymentChannelEnum getPaymentChannel() {
		return paymentChannel;
	}

	public static void main(String[] args) {
		PaymentMethodEnum paymentMethod = PaymentMethodEnum.getEnumById(333);
		System.out.println(paymentMethod);


		PaymentMethodEnum paymentMethod1 = PaymentMethodEnum.getEnumById(1);
		System.out.println(paymentMethod1);
	}
}
