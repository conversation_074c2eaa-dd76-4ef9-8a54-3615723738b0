package com.bees360.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 一道问卷题
 * <AUTHOR>
 * @since 2020/4/10 11:32 AM
 **/
@Data
public class Quiz {
    @JsonIgnore
    public final String SPLITTER = "::";
    /** 主键 **/
    private long quizId;

    /** 题干 **/
    private String subject;

    /**
     * 题目类型
     * @see com.bees360.entity.enums.QuizTypeEnum
     */
    private int type;

    /**
     * 如果是题目类型`type`为选择题，此处为备选项
     * 如果题目类型为`type`为日期类型，此处为日期格式
     */
    private String choices;

    /** 排序字段 **/
    @JsonIgnore
    private Integer sequence;

    public String[] getChoicesArray() {
        if (choices == null) {
            return null;
        }
        return choices.trim().split(SPLITTER);
    }
}
