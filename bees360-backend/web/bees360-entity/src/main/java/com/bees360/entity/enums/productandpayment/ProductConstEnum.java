package com.bees360.entity.enums.productandpayment;

import java.util.ArrayList;
import java.util.List;

import org.springframework.util.Assert;

import com.bees360.entity.enums.ReportTypeEnum;

public enum ProductConstEnum {
	// report service: 00***
	R_PREMIUM_MEASUREMENT_REPORT(2, ReportTypeEnum.PREMIUM_ROOF_MEASUREMENT_REPORT, PriceTypeEnum.FIXED_PRICE),
	R_PRELIMINARY_DAMAGE_ASSESSMENT_REPORT(3, ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT, PriceTypeEnum.FIXED_PRICE),
	R_ON_SITE_BIDDING_REPORT(4, ReportTypeEnum.BID_REPORT, PriceTypeEnum.FIXED_PRICE),
	R_REAL_TIME_QUICK_SQUARE_REPORT(5, ReportTypeEnum.QUICK_SQUARE_REPORT, PriceTypeEnum.FIXED_PRICE),
	R_HIGHFLY_EVALUATION_REPORT(6, ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT, PriceTypeEnum.FIXED_PRICE),
	R_REAL_TIME_DAMAGE_ASSESSMENT(7, ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT, PriceTypeEnum.FIXED_PRICE),
	R_PREMIUM_DAMAGE_ASSESSMENT_REPORT(8, ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, PriceTypeEnum.FIXED_PRICE),
	R_PROPERTY_IMAGE_REPORT(9, ReportTypeEnum.PROPERTY_IMAGE_REPORT, PriceTypeEnum.FIXED_PRICE),
	R_INFRARED_DAMAGE_REPORT(10, ReportTypeEnum.INFRARED_DAMAGE_REPORT, PriceTypeEnum.NEGOTIATED_PRICE),
	R_UNDERWRITING_REPORT(11, ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT, PriceTypeEnum.FIXED_PRICE),

	// pilot service: 10***
	P_NORMAL_PILOT(10001, PilotTypeEnum.NORMAL, PriceTypeEnum.FIXED_PRICE)
	;

	private final int id;
	private final ProductTypeEnum productType;
	private final int internalType;
	private final ReportTypeEnum reportType;
	private final PilotTypeEnum pilotType;
	private final PriceTypeEnum priceType;

	ProductConstEnum(int id, ReportTypeEnum internalType, PriceTypeEnum priceType) {
		this(id, ProductTypeEnum.REPORT, internalType.getCode(), internalType, null, priceType);

		Assert.notNull(internalType, "internalType must not be null");
		Assert.notNull(priceType, "priceType must not be null");
	}

	ProductConstEnum(int id, PilotTypeEnum internalType, PriceTypeEnum priceType) {
		this(id, ProductTypeEnum.PILOT, internalType.getCode(), null, internalType, priceType);

		Assert.notNull(internalType, "internalType must not be null");
		Assert.notNull(priceType, "priceType must not be null");
	}

	ProductConstEnum(int id, ProductTypeEnum productType, int internalType,
			ReportTypeEnum reportType, PilotTypeEnum pilotType, PriceTypeEnum priceType) {
		this.id = id;
		this.productType = productType;
		this.internalType = internalType;
		this.reportType = reportType;
		this.pilotType = pilotType;
		this.priceType = priceType;
	}

	public static List<ProductConstEnum> listByProductType(ProductTypeEnum type) {
		List<ProductConstEnum> services = new ArrayList<ProductConstEnum>();
		if(type == null) {
			return services;
		}
		for(ProductConstEnum service: values()) {
			if(service.getProductType().equals(type)) {
				services.add(service);
			}
		}
		return services;
	}

	//** getter and setter **//
	public int getId() {
		return id;
	}
	public ProductTypeEnum getProductType() {
		return productType;
	}
	public int getInternalType() {
		return internalType;
	}
	public ReportTypeEnum getReportType() {
		return reportType;
	}
	public PilotTypeEnum getPilotType() {
		return pilotType;
	}
	public PriceTypeEnum getPriceType() {
		return priceType;
	}

	public static void main(String[] args) {
		List<String> sqls = new ArrayList<String>();

		for(ProductConstEnum p: ProductConstEnum.values()) {
			String productName = p.getReportType() == null? p.getPilotType().getDisplay(): p.getReportType().getDisplay();
			String sql = "insert into Product(product_id, product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time) \n"
					+ "values(" + p.getId() + ", " + p.getProductType().getCode() + ", " + p.getInternalType() + ", '" + productName + "', " + p.getPriceType().getCode() + ", 0" + ", ''" + ", ''" + ", 1556187483366" + ", 1556187483366);";

			sqls.add(sql);
			System.out.println(sql);
			System.out.println();
		}
	}
}
