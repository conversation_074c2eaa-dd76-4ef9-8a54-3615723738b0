package com.bees360.entity;

import com.bees360.entity.enums.BaseCodeEnum;

public class ImageAnnotation2D extends BaseImageAnnotation{
	protected boolean deleted;

	public ImageAnnotation2D() {}

	public ImageAnnotation2D(BaseImageAnnotation annotation, boolean deleted) {
		this.annotationId = annotation.getAnnotationId();
		this.annotationPolygon = annotation.getAnnotationPolygon();
		this.annotationType = annotation.getAnnotationType();
		this.usageType = annotation.getUsageType();
		this.centerPointX = annotation.getCenterPointX();
		this.centerPointY = annotation.getCenterPointY();
		this.createdTime = annotation.getCreatedTime();
		this.deleted = deleted;
		this.facetId = annotation.getFacetId();
		this.generatedBy = annotation.getGeneratedBy();
		this.imageId = annotation.getImageId();
		this.projectId = annotation.getProjectId();
        this.sourceType = annotation.getSourceType();
	}

	public ImageAnnotation2D(BaseImageAnnotation annotation) {
		this(annotation, false);
	}

	/* getter and setter */
	public boolean getDeleted() {
		return deleted;
	}

	public void setDeleted(boolean deleted) {
		this.deleted = deleted;
	}

	@Override
	public String toString() {
		return "ImageAnnotation2D [deleted=" + deleted + ", annotationId=" + annotationId + ", imageId=" + imageId
				+ ", facetId=" + facetId + ", projectId=" + projectId + ", annotationPolygon=" + annotationPolygon
				+ ", createdTime=" + createdTime + ", centerPointX=" + centerPointX + ", centerPointY=" + centerPointY
				+ ", annotationType=" + annotationType + ", usageType=" + usageType + ", generatedBy=" + generatedBy + "]";
	}
}
