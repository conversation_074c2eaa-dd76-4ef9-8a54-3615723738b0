package com.bees360.entity.enums;

public enum HighflyServiceEnum implements BaseCodeEnum {
	GOLD(1, "Gold", 5000, 3.5D),
	SILVER(2, "Silver", 2500, 4.0D),
	BRONZE(3, "Bronze", 1000, 4.5D)
	;
	private final int code;
	private final String display;
	private final int volume;
	private final double price;

	HighflyServiceEnum(int code, String display,
			int volume, double reportPrice) {
		this.code = code;
		this.display = display;
		this.volume = volume;
		this.price = reportPrice;
	}

	public static HighflyServiceEnum getEnum(int code) {
		HighflyServiceEnum[] values = HighflyServiceEnum.values();
		for(HighflyServiceEnum s: values) {
			if(s.getCode() == code) {
				return s;
			}
		}
		return null;
	}

	public String getDetail() {
		return "volume: " + volume + ", price: $" + price;
	}

	/* getter and setter */
	@Override
	public int getCode() {
		return code;
	}
	@Override
	public String getDisplay() {
		return display;
	}
	public int getReportVolume() {
		return volume;
	}
	public double getReportPrice() {
		return price;
	}
}
