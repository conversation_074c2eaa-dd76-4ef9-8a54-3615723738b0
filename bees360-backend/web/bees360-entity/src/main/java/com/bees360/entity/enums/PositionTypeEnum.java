package com.bees360.entity.enums;

public enum PositionTypeEnum implements BaseCodeEnum{
	All(0, "All"),
	Front(1, "Front"),
	Back(2, "Back"),
	Left(3, "Left"),
	Right(4, "Right");

	private int code;
	private String display;

	PositionTypeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public static void main(String[] args){
		PositionTypeEnum[] enums = PositionTypeEnum.values();
		for(PositionTypeEnum e: enums){
			System.out.println(e.getCode() + ": " + e.getDisplay());
		}
	}

}
