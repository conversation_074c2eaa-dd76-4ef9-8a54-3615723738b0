package com.bees360.entity.vo.beespilot;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.dto.ProjectQuizDto;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/8/17 12:06 下午
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BeesPilotProjectDetailVo {
    private BeesPilotProjectInfo info;

    private List<BeespilotTask> task;

    private List<ProjectQuizDto> quiz;

    private List<ProjectImage> image;
}
