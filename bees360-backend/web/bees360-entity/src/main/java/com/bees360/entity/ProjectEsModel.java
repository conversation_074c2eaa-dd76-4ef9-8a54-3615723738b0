package com.bees360.entity;

import com.bees360.entity.dto.ProjectQuizDto;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.vo.CompanyCard;
import com.bees360.entity.vo.HistoryLogVo;
import com.bees360.entity.vo.ProjectInspectionInfoVo;
import com.bees360.entity.vo.ProjectStatusVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/04/30 10:43
 */
@Data
public class ProjectEsModel {

    private long projectId;

    private String policyNumber;

    private String claimNumber;

    private Integer claimType;

    private long createdTime;

    private long createdBy;

    private int projectType;

    private String address;

    private String city;

    private String state;

    private String country;

    private String zipCode;

    private String addressId;

    private String timeZone;

    private String assetOwnerName;

    private String assetOwnerPhone;

    private String assetOwnerEmail;

    private String inspectionNumber;

    private Long dueDate;

    private String inspectionType;

    private String description;

    private String claimNote;
    private String north;
    /**
     * related to com.bees360.entity.enums.ProcessStatus
     */
    private int latestStatus;

    /**
     * @see NewProjectStatusEnum#getCode()
     */
    private int projectStatus;

    /**
     * project status 最近修改时间
     */
    private Long statusUpdateTime;

    private int imageUploadStatus;

    private Long inspectionTypes;

    private Long inspectionTime;

    /**
     * project service type 订阅服务类型
     * @see com.bees360.entity.enums.ProjectServiceTypeEnum
     */
    private Integer serviceType;

    private String creatorName;

    /**
     * 代理信息
     */
    private String agent;
    /**
     * 代理人名称
     */
    private String agentContactName;
    private String agentPhone;
    private String agentEmail;

    private int flyZoneType;

    private String insuranceCompanyName;
    private String repairCompanyName;
    private String operatingCompany;

    private String customer;
    private String guideline;
    private String insuredHomePhone;
    private String insuredWorkPhone;
    private double gpsLocationLongitude;
    private double gpsLocationLatitude;
    private boolean isBooking;
    private int chimney;
    private Integer roofEstimatedAreaItem;
    private Integer reportServiceOption;
    private long damageEventTime;

    private String specialInstructions;
    private String specialInstructionComments;
    private String policyEffectiveDate;
    private String yearBuilt;

    private Integer payStatus;

    private String companyName;

    private String companyLogo;
    private String insuranceCompanyNameLogo;
    private String repairCompanyNameLogo;

    /**
     * 创建项目时指定订购的报告
     */
    private List<Integer> reportTypes;

    private List<Member> members;

    private List<ProjectStatusVo> timeLines;

    private List<ProjectQuizDto> projectQuiz;

    private long siteInspectedTime;

    private int droneImageCount;

    private int mobileImageCount;

    private double rotationDegree;

    private long customerContactedTime;

    private String inspectedBy;

    private Long insuranceCompany;

    private Long repairCompany;

    private String adjuster;
    private String adjusterCompany;
    private String adjusterPhone;
    private String adjusterEmail;

    private int hoverMarkStatus;
    private int plnarMarkStatus;

    private String pilotName;

    private List<Long> projectTags;

    private String catNumber;

    private String batchNo;

    private List<String> pilotFeedbacks;

    private String hoverJobId;
}
