package com.bees360.entity;

import com.bees360.entity.enums.DirectionEnum;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImagePartialViewTypeEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import com.bees360.entity.vo.TinyImageVo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.Objects;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import jakarta.annotation.Nullable;

@Slf4j
public class ProjectImage implements Cloneable, Serializable {

    public static final int DELETED = 1;
    public static final int COMPLETELY_DELETED = -1;

    // Identifier of an image.
    private String imageId;
    // The file name in the system for this image. This name is probably used by Amazon cloud services.
    private String fileName;
    // The file name for the same image with the middle resolution.
    private String fileNameMiddleResolution;
    // The file name for the same image with the smaller resolution.
    private String fileNameLowerResolution;
    // The url of  lower resolution image with annotations on it.
    private String annotationImage;
    private long fileSize;
    // Who uploaded this image.
    private long userId;
    private long uploadTime;
    // The local filename when this file is uploaded from some local device.
    private String originalFileName;
    // The sources of this file: 0: drone image 1: cell-phone image …
    // it must be assigned a value through the Enum FileSourceTypeEnum.
    private int fileSourceType;
    // The GPS position of this image when it is generated.
    private double gpsLocationLongitude;
    private double gpsLocationLatitude;

    private double relativeAltitude;

    private int imageHeight;
    private int imageWidth;
    // The project that is linked win this project image.
    private long projectId;

    // The direction of this image in a house,
    // it must be assigned a value through the Enum DirectionEnum.
    private int direction;
    // The orientation of this image in a house.
    // it must be assigned a value through the Enum OrientationEnum(LEFT, RIGHT, BACK, FRONT).
    private Integer orientation;
    // it must be assigned a value through the Enum ImageTypeEnum.
    private int imageType;

    private String imageCategory;

    @Getter
    @Setter
    private int categoryId;

    @Getter
    @Setter
    private Integer subCategoryId;

    // It is a 3*4 matrix used for 3D construction, i.e.,
    // specifically the coordinate space transformation.
    private String camPropertyMatrix;
    // Whether this image is softly deleted.  -1:Completely deleted
    private boolean deleted;

    private boolean manuallyAnnotated;

    private String parentId;

    private double weight;

    private long shootingTime;

    private Long imageSort;

    // 图片信息中的 TIFF/Orientation，默认值为1(Normal)
    private int tiffOrientation = 1;

    // it must be assigned a value through the Enum ImagePartialViewTypeEnum.
    private int partialType = ImagePartialViewTypeEnum.ROOF.getCode();

    private static final ProjectImage DEFAULT_IMAGE;

    //2020-06-09 default value 0: 该图片不需要参与3d建模; 1:参与3d建模; -1: 没有参与建模(建模算法剔除)
    private int in3DModel;

    private long updateTime;

    // 房间名称
    private String roomName;

    // 房间楼层
    private String floorLevel;

    // 序号、包含房间的序号
    private String number;

    // 损坏描述，不存储
    private String description;

    private String eTag;

    private String tagCategory;

    private String tagDescription;

    @Getter
    @Setter
    private String firebaseImageKey;

    @Getter
    @Setter
    private String note;

    public String getTagCategory() {
        return tagCategory;
    }

    public void setTagCategory(String tagCategory) {
        this.tagCategory = tagCategory;
    }

    public String getTagDescription() {
        return tagDescription;
    }

    public void setTagDescription(String tagDescription) {
        this.tagDescription = tagDescription;
    }

    static {
        DEFAULT_IMAGE = generateDefaultImage();
    }

    public ProjectImage() {
        super();
    }


    public ProjectImage(String imageId) {
        super();
        this.imageId = imageId;
    }

    public TinyImageVo toTinyImageVo() {
        return new TinyImageVo(this);
    }

    public static ProjectImage defaultImage() {
        if (null == DEFAULT_IMAGE) {
            return generateDefaultImage();
        }
        return (ProjectImage) DEFAULT_IMAGE.clone();
    }

    private static ProjectImage generateDefaultImage() {
        ProjectImage image = new ProjectImage();
        image.setFileName("");
        image.setFileNameMiddleResolution(null);
        image.setFileNameLowerResolution(null);
        image.setDirection(DirectionEnum.DEFAULT);
        image.setFileSize(0);
        image.setFileSourceType(FileSourceTypeEnum.DEFAULT);
        image.setGpsLocationLatitude(0.0);
        image.setGpsLocationLongitude(0.0);
        image.setImageHeight(0);
        image.setImageWidth(0);
        image.setImageType(ImageTypeEnum.DEFAULT);
        image.setFileSourceType(FileSourceTypeEnum.DEFAULT);
        image.setDeleted(false);
        image.setWeight(0);
        image.setPartialType(ImagePartialViewTypeEnum.ROOF.getCode());
        return image;
    }

    @Override
    public int hashCode() {
        return imageId.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof ProjectImage)) {
            return false;
        }
        ProjectImage image = (ProjectImage) obj;
        return Objects.equals(imageId, image.getImageId());
    }

    @Override
    public Object clone() {
        ProjectImage image = null;
        try {
            image = (ProjectImage) super.clone();
        } catch (CloneNotSupportedException e) {
            log.error("Fail to clone ProjectImage.", e);
        }

        return image;
    }

    // getter and setter

    public String getImageS3Key(){
        return fileName;
    }

    public String getMiddleResolutionImageS3Key(){
        return fileNameMiddleResolution;
    }

    public String getLowerResolutionImageS3Key(){
        return fileNameLowerResolution;
    }

    public String getAnnotationImageS3Key(){
        return annotationImage;
    }

    public long getShootingTime() {
        return shootingTime;
    }

    public void setShootingTime(long shootingTime) {
        this.shootingTime = shootingTime;
    }

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileNameLowerResolution() {
        return fileNameLowerResolution;
    }

    public void setFileNameLowerResolution(String fileNameLowerResolution) {
        this.fileNameLowerResolution = fileNameLowerResolution;
    }

    public String getFileNameMiddleResolution() {
        return fileNameMiddleResolution;
    }

    public void setFileNameMiddleResolution(String fileNameMiddleResolution) {
        this.fileNameMiddleResolution = fileNameMiddleResolution;
    }

    public String getAnnotationImage() {
        return annotationImage;
    }

    public void setAnnotationImage(String annotationImage) {
        this.annotationImage = annotationImage;
    }

    public String geteTag() {
        return eTag;
    }

    public void seteTag(String eTag) {
        this.eTag = eTag;
    }

    public String getETag() {
        return eTag;
    }

    public void setETag(String eTag) {
        this.eTag = eTag;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public long getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(long uploadTime) {
        this.uploadTime = uploadTime;
    }

    public String getOriginalFileName() {
        return originalFileName;
    }

    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }

    public int getFileSourceType() {
        return fileSourceType;
    }

    public void setFileSourceType(int fileSourceType) {
        this.fileSourceType = fileSourceType;
    }

    public double getGpsLocationLongitude() {
        return gpsLocationLongitude;
    }

    public void setGpsLocationLongitude(double gpsLocationLongitude) {
        this.gpsLocationLongitude = gpsLocationLongitude;
    }

    public double getGpsLocationLatitude() {
        return gpsLocationLatitude;
    }

    public void setGpsLocationLatitude(double gpsLocationLatitude) {
        this.gpsLocationLatitude = gpsLocationLatitude;
    }

    public double getRelativeAltitude() {
        return relativeAltitude;
    }

    public void setRelativeAltitude(double relativeAltitude) {
        this.relativeAltitude = relativeAltitude;
    }

    public int getImageHeight() {
        return imageHeight;
    }

    public void setImageHeight(int imageHeight) {
        this.imageHeight = imageHeight;
    }

    public int getImageWidth() {
        return imageWidth;
    }

    public void setImageWidth(int imageWidth) {
        this.imageWidth = imageWidth;
    }

    public long getProjectId() {
        return projectId;
    }

    public void setProjectId(long projectId) {
        this.projectId = projectId;
    }

    public int getDirection() {
        return direction;
    }

    public void setDirection(int direction) {
        this.direction = direction;
    }

    public Integer getOrientation() {
        return orientation;
    }

    public void setOrientation(Integer orientation) {
        this.orientation = orientation;
    }

    public int getImageType() {
        return imageType;
    }

    public void setImageType(int imageType) {
        this.imageType = imageType;
    }

    public String getImageCategory() {
        return imageCategory;
    }

    public void setImageCategory(String imageCategory) {
        this.imageCategory = imageCategory;
    }

    @JsonIgnore
    public String getCamPropertyMatrix() {
        return camPropertyMatrix;
    }

    public void setCamPropertyMatrix(String camPropertyMatrix) {
        this.camPropertyMatrix = camPropertyMatrix;
    }

    public boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public boolean isManuallyAnnotated() {
        return manuallyAnnotated;
    }

    public void setManuallyAnnotated(boolean manuallyAnnotated) {
        this.manuallyAnnotated = manuallyAnnotated;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public double getWeight() {
        return weight;
    }

    public void setWeight(double weight) {
        this.weight = weight;
    }

    public int getTiffOrientation() {
        return tiffOrientation;
    }

    public void setTiffOrientation(int tiffOrientation) {
        this.tiffOrientation = tiffOrientation;
    }

    public int getPartialType() {
        return partialType;
    }

    public void setPartialType(int partialType) {
        this.partialType = partialType;
    }

    public Long getImageSort() {
        return imageSort;
    }

    public void setImageSort(Long imageSort) {
        this.imageSort = imageSort;
    }

    public int getIn3DModel() {
        return in3DModel;
    }

    public void setIn3DModel(int in3DModel) {
        this.in3DModel = in3DModel;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    /**
     * 1.兼容之前版本的图片,in3DModel默认值为0,这种图片也需要参与映射和damage的关联
     * 2.3d建模成功之后,只有in3DModel=-1的情况下,该图片不做映射和damage的关联处理.
     *
     * @return
     */
    public boolean existIn3DModel() {
        return in3DModel == 1 || in3DModel == 0;
    }

    public static int convertBoolToIntForIn3DModel(boolean in3DModel) {
        return in3DModel ? 1 : -1;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFloorLevel() {
        return floorLevel;
    }

    public void setFloorLevel(String floorLevel) {
        this.floorLevel = floorLevel;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    @Override
    public String toString() {
        return "ProjectImage{" +
            "imageId='" + imageId + '\'' +
            ", fileName='" + fileName + '\'' +
            ", fileNameMiddleResolution='" + fileNameMiddleResolution + '\'' +
            ", fileNameLowerResolution='" + fileNameLowerResolution + '\'' +
            ", annotationImage='" + annotationImage + '\'' +
            ", fileSize=" + fileSize +
            ", userId=" + userId +
            ", uploadTime=" + uploadTime +
            ", originalFileName='" + originalFileName + '\'' +
            ", fileSourceType=" + fileSourceType +
            ", gpsLocationLongitude=" + gpsLocationLongitude +
            ", gpsLocationLatitude=" + gpsLocationLatitude +
            ", relativeAltitude=" + relativeAltitude +
            ", imageHeight=" + imageHeight +
            ", imageWidth=" + imageWidth +
            ", projectId=" + projectId +
            ", direction=" + direction +
            ", orientation=" + orientation +
            ", imageType=" + imageType +
            ", imageCategory='" + imageCategory + '\'' +
            ", camPropertyMatrix='" + camPropertyMatrix + '\'' +
            ", deleted=" + deleted +
            ", manuallyAnnotated=" + manuallyAnnotated +
            ", parentId='" + parentId + '\'' +
            ", weight=" + weight +
            ", shootingTime=" + shootingTime +
            ", imageSort=" + imageSort +
            ", tiffOrientation=" + tiffOrientation +
            ", partialType=" + partialType +
            ", in3DModel=" + in3DModel +
            ", updateTime=" + updateTime +
            ", roomName=" + roomName +
            ", description=" + description +
            ", floorLevel=" + floorLevel +
            ", roomNumber=" + number +
            ", firebaseImageKey=" + firebaseImageKey +
            '}';
    }
}
