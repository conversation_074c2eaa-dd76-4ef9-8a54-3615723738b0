package com.bees360.entity.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum CompanyTypeEnum implements BaseCodeEnum{

	INSURANCE_COMPANY(0, "Insurance Company"),
	REPAIR_COMPANY(1, "Repair Company"),
	MATERIAL_PROVIDER_COMPANY(2, "Material Provider Company"),
	OTHER(3, "Others"),
	CLAIM_COMPANY(4, "Claim Company"),
	UNDERWRITING_COMPANY(5, "Underwriting Company"),
	ROOFING_COMPANY(6, "Roofing Company")
	;

	private final int code;
	private final String display;

    public static final Long BEES360_TEST_CARRIER = 2744L;

	CompanyTypeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public String getDisplay(){
		return display;
	}

	public static CompanyTypeEnum getEnum(int code) {
		for(CompanyTypeEnum type: CompanyTypeEnum.values()) {
			if(type.getCode() == code) {
				return type;
			}
		}
		return null;
	}

	@Override
	@JsonValue
	public int getCode() {
		return code;
	}

	@Override
	public String toString(){
		return display;
	}

	public static void main(String[] args) {
		show();
	}

	private static void show() {
		System.out.println("|name|code|");
		System.out.println("|----|----|");
		for(CompanyTypeEnum type: CompanyTypeEnum.values()) {
			System.out.println("|" + type.getDisplay() + "|" + type.getCode() + "|" );
		}
	}
}
