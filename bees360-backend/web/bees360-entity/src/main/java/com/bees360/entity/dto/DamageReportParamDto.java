package com.bees360.entity.dto;

import com.bees360.entity.vo.ReportTemplateVo;
import java.util.List;
import java.util.Map;

import com.bees360.entity.Company;
import com.bees360.entity.CustomizedReportElement;
import com.bees360.entity.CustomizedReportItem;
import com.bees360.entity.HouseCategory;
import com.bees360.entity.ImageAnnotation;
import com.bees360.entity.ImageAnnotation2D;
import com.bees360.entity.ImageFacet;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectFacet;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.DamageSeverityEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.vo.OnsiteReportElementVo;
import com.bees360.entity.vo.ReportMeasurementParamVo;
import com.bees360.entity.vo.ReportPageTemplateVo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
public class DamageReportParamDto {

	private long userId;

    private String aiUserId;

	private long time;

	private boolean multipleStructure;

	private String folderPath;

	private String packagePath;

	private Project project;

	private Map<String, List<ImageAnnotation>> annotationMap;

	private Map<String, String[]> cellphoneImageParentUrlMap;

	private DamageSeverityEnum damageServer;

	private List<List<OnsiteReportElementVo>> reportPages;

	private String pdfFileName;

	private Company company;

	private Company insuredCompany;

	private Map<String, Object> param;

	private List<OnsiteReportElementVo> elementVos;

	private ProjectImage overview;

	private List<ProjectFacet> projectFacetList;

	private Map<Integer, Picture> picMap;

	private ReportTypeEnum reportType;

	private List<ReportPageTemplateVo> pageTemplateList;

    private ReportTemplateVo template;

    private Map<Long, ImageAnnotation2D> cropMap;

	private Map<Long, ImageAnnotation> cropTDMap;

	private String dateInspected;

	private Map<String, ImageAnnotation2D> mapping2dMap;

	private Map<String, List<DamageMeasurementFrame>> frameMap;

	private Map<String, Object> ciMap;

	private Map<Long, List<CustomizedReportElement>> elementIdCustomizedMap;

	private Map<Long, String> customizedSIBCategoryMap;

	private Map<String, List<List<Point>>> pointsMap;

	private Map<String, ProjectImage> projectImageMap;

	private Map<String, List<ImageFacet>> imageFacetsMap;

	private List<ProjectFacet> facetList;

	private ReportMeasurementParamVo measurementReportParam;

	private Map<Integer, List<HouseCategory>> houseCategoryMap;

	private boolean isAngleOptimization;

	private List<CustomizedReportItem> itemList;

    private List<ReportCommentDto> commentList;

    @Setter
    @Getter
    private String closeReason;

    private List<ProjectQuizDto> projectQuizList;

    private List<ProjectQuizSignatureDto> projectQuizSignatureList;

    private String unsignedReason;

    private String caseTypePrefix;

    public List<List<OnsiteReportElementVo>> getReportPages() {
        return reportPages;
    }

    public void setReportPages(List<List<OnsiteReportElementVo>> reportPages) {
        this.reportPages = reportPages;
    }

    public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public long getTime() {
		return time;
	}

	public void setTime(long time) {
		this.time = time;
	}

    public List<ReportCommentDto> getCommentList() {
        return commentList;
    }

    public void setCommentList(List<ReportCommentDto> commentList) {
        this.commentList = commentList;
    }

    public boolean getMultipleStructure() {
		return multipleStructure;
	}

	public void setMultipleStructure(boolean multipleStructure) {
		this.multipleStructure = multipleStructure;
	}

	public String getFolderPath() {
		return folderPath;
	}

	public void setFolderPath(String folderPath) {
		this.folderPath = folderPath;
	}

	public String getPackagePath() {
		return packagePath;
	}

	public void setPackagePath(String packagePath) {
		this.packagePath = packagePath;
	}

	public Project getProject() {
		return project;
	}

	public void setProject(Project project) {
		this.project = project;
	}

	public Map<String, List<ImageAnnotation>> getAnnotationMap() {
		return annotationMap;
	}

	public void setAnnotationMap(Map<String, List<ImageAnnotation>> annotationMap) {
		this.annotationMap = annotationMap;
	}

	public Map<String, String[]> getCellphoneImageParentUrlMap() {
		return cellphoneImageParentUrlMap;
	}

	public void setCellphoneImageParentUrlMap(Map<String, String[]> cellphoneImageParentUrlMap) {
		this.cellphoneImageParentUrlMap = cellphoneImageParentUrlMap;
	}

	public DamageSeverityEnum getDamageServer() {
		return damageServer;
	}

	public void setDamageServer(DamageSeverityEnum damageServer) {
		this.damageServer = damageServer;
	}

	public String getPdfFileName() {
		return pdfFileName;
	}

	public void setPdfFileName(String pdfFileName) {
		this.pdfFileName = pdfFileName;
	}

	public Map<String, Object> getParam() {
		return param;
	}

	public void setParam(Map<String, Object> param) {
		this.param = param;
	}

	public List<OnsiteReportElementVo> getElementVos() {
		return elementVos;
	}

	public void setElementVos(List<OnsiteReportElementVo> elementVos) {
		this.elementVos = elementVos;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public ProjectImage getOverview() {
		return overview;
	}

	public void setOverview(ProjectImage overview) {
		this.overview = overview;
	}

	public List<ProjectFacet> getProjectFacetList() {
		return projectFacetList;
	}

	public void setProjectFacetList(List<ProjectFacet> projectFacetList) {
		this.projectFacetList = projectFacetList;
	}

	public Map<Integer, Picture> getPicMap() {
		return picMap;
	}

	public void setPicMap(Map<Integer, Picture> picMap) {
		this.picMap = picMap;
	}

	public ReportTypeEnum getReportType() {
		return reportType;
	}

	public void setReportType(ReportTypeEnum reportType) {
		this.reportType = reportType;
	}

	public Map<Long, ImageAnnotation2D> getCropMap() {
		return cropMap;
	}

	public void setCropMap(Map<Long, ImageAnnotation2D> cropMap) {
		this.cropMap = cropMap;
	}

	public String getDateInspected() {
		return dateInspected;
	}

	public void setDateInspected(String dateInspected) {
		this.dateInspected = dateInspected;
	}

	public Map<String, ImageAnnotation2D> getMapping2dMap() {
		return mapping2dMap;
	}

	public void setMapping2dMap(Map<String, ImageAnnotation2D> mapping2dMap) {
		this.mapping2dMap = mapping2dMap;
	}

	public Map<String, List<DamageMeasurementFrame>> getFrameMap() {
		return frameMap;
	}

	public void setFrameMap(Map<String, List<DamageMeasurementFrame>> frameMap) {
		this.frameMap = frameMap;
	}

	public Map<String, Object> getCiMap() {
		return ciMap;
	}

	public void setCiMap(Map<String, Object> ciMap) {
		this.ciMap = ciMap;
	}

	public Company getInsuredCompany() {
		return insuredCompany;
	}

	public void setInsuredCompany(Company insuredCompany) {
		this.insuredCompany = insuredCompany;
	}

	public Map<Long, List<CustomizedReportElement>> getElementIdCustomizedMap() {
		return elementIdCustomizedMap;
	}

	public void setElementIdCustomizedMap(Map<Long, List<CustomizedReportElement>> elementIdCustomizedMap) {
		this.elementIdCustomizedMap = elementIdCustomizedMap;
	}

	public Map<Long, String> getCustomizedSIBCategoryMap() {
		return customizedSIBCategoryMap;
	}

	public void setCustomizedSIBCategoryMap(Map<Long, String> customizedSIBCategoryMap) {
		this.customizedSIBCategoryMap = customizedSIBCategoryMap;
	}

	public List<ReportPageTemplateVo> getPageTemplateList() {
		return pageTemplateList;
	}

	public void setPageTemplateList(List<ReportPageTemplateVo> pageTemplateList) {
		this.pageTemplateList = pageTemplateList;
	}

    public ReportTemplateVo getTemplate() {
        return template;
    }

    public void setTemplate(ReportTemplateVo template) {
        this.template = template;
    }

	public Map<Long, ImageAnnotation> getCropTDMap() {
		return cropTDMap;
	}

	public void setCropTDMap(Map<Long, ImageAnnotation> cropTDMap) {
		this.cropTDMap = cropTDMap;
	}

	public Map<String, List<List<Point>>> getPointsMap() {
		return pointsMap;
	}

	public void setPointsMap(Map<String, List<List<Point>>> pointsMap) {
		this.pointsMap = pointsMap;
	}

	public Map<String, ProjectImage> getProjectImageMap() {
		return projectImageMap;
	}

	public void setProjectImageMap(Map<String, ProjectImage> projectImageMap) {
		this.projectImageMap = projectImageMap;
	}

	public Map<String, List<ImageFacet>> getImageFacetsMap() {
		return imageFacetsMap;
	}

	public void setImageFacetsMap(Map<String, List<ImageFacet>> imageFacetsMap) {
		this.imageFacetsMap = imageFacetsMap;
	}

    public List<ProjectFacet> getFacetList() {
        return facetList;
    }

    public void setFacetList(List<ProjectFacet> facetList) {
        this.facetList = facetList;
    }

    public ReportMeasurementParamVo getMeasurementReportParam() {
		return measurementReportParam;
	}

	public void setMeasurementReportParam(ReportMeasurementParamVo measurementReportParam) {
		this.measurementReportParam = measurementReportParam;
	}

	public Map<Integer, List<HouseCategory>> getHouseCategoryMap() {
		return houseCategoryMap;
	}

	public void setHouseCategoryMap(Map<Integer, List<HouseCategory>> houseCategoryMap) {
		this.houseCategoryMap = houseCategoryMap;
	}

	public boolean getIsAngleOptimization() {
		return isAngleOptimization;
	}

	public void setIsAngleOptimization(boolean isAngleOptimization) {
		this.isAngleOptimization = isAngleOptimization;
	}

	public List<CustomizedReportItem> getItemList() {
		return itemList;
	}

	public void setItemList(List<CustomizedReportItem> itemList) {
		this.itemList = itemList;
	}

    public String getAiUserId() {
        return aiUserId;
    }

    public void setAiUserId(String aiUserId) {
        this.aiUserId = aiUserId;
    }

    public void setProjectQuizList(List<ProjectQuizDto> projectQuizList) {
        this.projectQuizList = projectQuizList;
    }

    public List<ProjectQuizDto> getProjectQuizList() {
        return projectQuizList;
    }

    public List<ProjectQuizSignatureDto> getProjectQuizSignatureList() {
        return projectQuizSignatureList;
    }

    public void setProjectQuizSignatureList(List<ProjectQuizSignatureDto> projectQuizSignatureList) {
        this.projectQuizSignatureList = projectQuizSignatureList;
    }

	public String getUnsignedReason() {
		return unsignedReason;
	}

	public void setUnsignedReason(String unsignedReason) {
		this.unsignedReason = unsignedReason;
	}

    public String getCaseTypePrefix() {
        return caseTypePrefix;
    }

    public void setCaseTypePrefix(String caseTypePrefix) {
        this.caseTypePrefix = caseTypePrefix;
    }
}
