package com.bees360.entity;

import com.bees360.activity.Activity;
import com.bees360.entity.enums.ProjectMessageTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.ToString;

import jakarta.annotation.Nullable;

/**
 * 项目留言
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ProjectMessage {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目主键ID
     */
    @NonNull
    private Long projectId;

    /**
     * 消息发送者ID
     */
    @NonNull
    private Long senderId;

    /**
     * 消息发送的标题
     */
    private String title;

    /**
     * 消息发送的内容
     */
    @NonNull
    private String content;

    /**
     * 创建时间
     */
    @NonNull
    private Long createTime;

    /**
     * 是否删除
     */
    private boolean deleted;

    /**
     * 消息类型
     * @see ProjectMessageTypeEnum
     */
    @NonNull
    private Integer type;

    /**
     * 补充信息
     */
    private String extra;

    /**
     * @see Activity#getSource()
     */
    @Nullable
    private String source;
}
