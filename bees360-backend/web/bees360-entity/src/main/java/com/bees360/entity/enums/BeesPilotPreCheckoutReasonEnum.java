package com.bees360.entity.enums;

public enum BeesPilotPreCheckoutReasonEnum {

    APP_IS_NOT_WORKING(1, "BeesPilot™ is not working"),

    RESTRICTED_AIRSPACE(2, "Restricted airspace"),

    NO_INTERNET_CONNECT(3, "No internet connection"),

    OTHERS(4, "Others"),
    ;
    BeesPilotPreCheckoutReasonEnum(Integer code, String display) {
        this.code = code;
        this.display = display;
    }
    private final Integer code;
    private final String display;

    public Integer getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }
}
