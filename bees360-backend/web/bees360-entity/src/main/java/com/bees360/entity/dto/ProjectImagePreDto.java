package com.bees360.entity.dto;

import com.bees360.entity.enums.BeesPilotPreCheckoutReasonEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/16 9:21 AM
 **/
@Data
public class ProjectImagePreDto {
    /**
     * @see com.bees360.entity.enums.ProjectStatusEnum#TASK_CHECKED_OUT
     * @see com.bees360.entity.enums.ProjectStatusEnum#TASK_CHECKED_IN
     */
    private List<ImageInfoRecord> imageInfoRecords;

    /**
     * 提前checkout的原因
     * @see BeesPilotPreCheckoutReasonEnum
     */
    private Integer preCheckoutReason;
}
