package com.bees360.entity.vo;

import com.bees360.entity.Company;
import com.bees360.entity.User;
import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.dto.IdNameListDto;
import com.bees360.entity.enums.CompanyTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

public class AccountProfile {
	private User user;
	private Company company;

    @Getter
    private AuthType authType;

	public AccountProfile(User user, Company company, AuthType authType) {
		this.user = user == null? new User(): user;
		this.company = company;
        this.authType = authType;
	}

	//** getter **//
	public long getUserId() {
		return user.getUserId();
	}
	public String getName() {
		return user.getName();
	}
	public String getFirstName() {
		return user.getFirstName();
	}
	public String getLastName() {
		return user.getLastName();
	}
	public String getEmail() {
		return user.getEmail();
	}
	public String getPhone() {
		return user.getPhone();
	}
	public int getActiveStatus() {
		return user.getActiveStatus();
	}
	public String getAvatar() {
		return user.getAvatar();
	}
	public String getZipCode() {
		return user.getZipCode();
	}
	public String getAddress() {
		return user.getAddress();
	}
	public String getCity() {
		return user.getCity();
	}
	public String getState() {
		return user.getState();
	}
	public String getCountry() {
		return user.getCountry();
	}
	public double getGpsLocationLatitude() {
		return user.getGpsLocationLatitude();
	}
	public double getGpsLocationLongitude() {
		return user.getGpsLocationLongitude();
	}
	public Long getCompanyId() {
		return company == null? null: company.getCompanyId();
	}
	public String getCompanyName() {
		return company == null? null: company.getCompanyName();
	}
	public String getCompanyLogo() {
		return company == null? null: company.getLogo();
	}
    public String getCompanyType(){
        return company == null ? null : CompanyTypeEnum.getEnum(company.getCompanyType()).getDisplay();
    }
    public String getCompanyWebsite() {
        return company == null ? null : company.getWebsite();
    }
	public String getEmployeeId() {
		return user.getEmployeeId();
	}
	public List<IdNameDto> getRoleList() {
		return user.getRoleList();
	}
	public List<IdNameDto> getRoleApplicationList() {
		return user.getRoleApplicationList();
	}
	public List<IdNameListDto> getCertificates() {
		Map<Integer, List<String>> roleCertificates = user.parseCertificates();
		List<IdNameListDto> result = new ArrayList<IdNameListDto>();
		for(Entry<Integer, List<String>> entry: roleCertificates.entrySet()) {
			RoleEnum role = RoleEnum.getEnum(entry.getKey());
			if(role == null || (!user.hasRole(role) && !user.isApplyingRole(role))) {
				continue;
			}
			IdNameListDto idNameList = new IdNameListDto(role.getCode(), role.getDisplay(), entry.getValue());
			result.add(idNameList);
		}
		return result;
	}

	public double getTravelRadius() {
		return user.getTravelRadius();
	}
	public Integer getHighflyService() {
		return user.getHighflyService();
	}
	public Integer getInspectionService() {
		return user.getInspectionService();
	}
	public long getOrderServiceTime() {
		return user.getOrderServiceTime();
	}

    public Double getRating() {
        return user.getRating();
    }

    public Boolean getIsSsoUser() {
        return user.getIsSsoUser();
    }

    public User.UserMFASettings getUserMfaSettings() {
        return user.getUserMfaSettings();
    }

	@Override
	public String toString() {
		return "AccountProfile [user=" + user + ", company=" + company + "]";
	}

    public enum AuthType {
        PASSWORD,
        SSO
    }
}
