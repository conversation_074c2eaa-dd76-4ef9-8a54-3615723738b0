package com.bees360.entity.enums;

import com.bees360.project.Message.IntegrationType;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.ServiceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2022/03/22
 */
@AllArgsConstructor
@Getter
public enum PipelineTaskEnum {
    // Picture
    VERIFY_ADDRESS("Verify Address", "verify_address"),
    TAKE_INTERIOR_IMAGES("Take Interior Images", "take_interior_images"),
    TAKE_EXTERIOR_IMAGES("Take Exterior Images", "take_exterior_images"),
    TAKE_DRONE_IMAGES("Take Drone Images", "take_drone_images"),
    TAKE_HOVER_IMAGES("Take Hover Images", "take_hover_images"),
    TAKE_PLNAR_IMAGES("Take Plnar Images", "take_plnar_images"),
    TAKE_MAGICPLAN_IMAGES("Take Magicplan Images", "take_magicplan_images"),
    UPLOAD_DRONE_IMAGES("Upload Drone Images", "upload_drone_images"),
    UPLOAD_MOBILE_IMAGE("Upload Mobile Image", "upload_mobile_images"),
    UPLOAD_EXTERIOR_IMAGES("Upload Exterior Image", "upload_exterior_images"),
    UPLOAD_INTERIOR_IMAGES("Upload Interior Image", "upload_interior_images"),
    UPLOAD_ADDRESS_IMAGES("Upload Address Images", "upload_address_images"),
    IMAGE_UPLOADED("Image Uploaded", "image_uploaded"),
    CREATE_HOVER("Create Hover", "create_hover"),
    CREATE_PLNAR("Create Plnar", "create_plnar"),
    CREATE_MAGICPLAN("Create Magicplan", "create_magicplan"),
    // Questionnaire
    FILL_OUT_FORM("Fill out form", "fill_out_form"),
    FILL_OUT_QUESTIONNAIRE("Fill out questionnaire", "fill_out_form"),
    // 3D
    LAUNCH_3D("Launch 3d", "_3d_modeling"),
    RANGING("Ranging", "ranging"),
    SCOPING("Scoping", "scoping"),
    PLANE("Plane", "plane"),
    BOUNDARY("Boundary", "boundary"),
    POST_BOUNDARY("Post-Boundary", "post_boundary"),
    REFINE("Refine", "refine"),
    // REPORT
    APPROVE_PREMIUM_MEASUREMENT_REPORT("Approve Premium Measurement Report", "approve_dar"),
    GENERATE_PREMIUM_MEASUREMENT_REPORT("Generate Premium Measurement Report", "generate_pmr"),
    SUBMIT_PREMIUM_MEASUREMENT_REPORT("Submit Premium Measurement Report", "submit_dar"),
    // Third Party Service
    FETCH_HOVER_REPORT("Fetch Hover Report", "fetch_hover_report"),
    FETCH_PLNAR_DATA("Fetch PLNAR Data", "fetch_plnar_data"),
    PLNAR_COMPLETE("PLNAR Complete", "plnar_complete"),
    // Job
    FETCH_ESTIMATE_REPORT("Fetch Estimate Report", "fetch_estimate_report"),
    // Client Receive
    CLIENT_RECEIVED("Client Received", "client_received"),
    // Close
    CLOSE("Close", "close"),
    ESTIMATE_COMPLETE("Estimate complete", "estimate_complete"),
    TAG_ELEVATION_OVERVIEWS("Tag elevation overviews", "tag_elevation_overviews"),
    UPLOAD_HOVER_IMAGES("Upload hover images", "upload_hover_images"),
    GENERATE_INVOICE("Generate invoice", "generate_inv"),
    WAIT_PLNAR_COMPLETE("Wait PLNAR Complete", "wait_plnar_complete"),
    WAIT_MAGICPLAN_COMPLETE("Wait Magicplan Complete", "wait_magicplan_complete"),
    WAIT_HOVER_COMPLETE("Wait Hover Complete", "wait_hover_complete"),
    UPLOAD_IBEES_IMAGE("Upload iBees Images", "upload_ibees_images"),
    TRANSFERRED_DATA_TO_WEB("Transferred data to web", "transferred_data_to_web"),
    PROJECT_CREATE("Project Create", "project_create"),
    CONTACT_INSURED_BY_DU("Contact Insured", "contact_insured_by_du"),
    DU_CONTACT_INSURED_CLAIMS("Contact Insured", "du_contact_insured_claims"),
    DU_CONTACT_INSURED_P4P("Contact Insured", "du_contact_insured_p4p"),
    DU_CONTACT_INSURED_WG("Contact Insured", "du_contact_insured_wg"),
    CONTACT_INSURED_BY_PILOT("Contact Insured", "contact_insured_by_pilot"),
    ASSIGN_TO_PILOT("Assign to Pilot", "assign_to_pilot"),
    ASSIGN_TO_PILOT_CLAIMS("Assign to Pilot", "assign_to_pilot_claims"),
    ASSIGN_TO_PILOT_P4P("Assign to Pilot", "assign_to_pilot_p4p"),
    ASSIGN_TO_PILOT_WG("Assign to Pilot", "assign_to_pilot_wg"),
    CREATE_MISSION("Create Mission", "create_mission"),
    CHECK_IN("Check In", "check_in"),
    CHECK_OUT("Check Out", "check_out"),
    TRANSFER_EXTERIOR_IMAGES_TO_AI("Transfer Exterior Images to AI", "transfer_exterior_images_to_ai"),
    KEEP_PROJECT_ON_TRACK("Keep Project on Tract", "keep_project_on_track"),
    CHECK_PILOT_AVAILABILITY("Check Pilot Availability", "pilot_recommendation"),
    ;

    private final String name;
    private final String key;

    private final static String COMPRESSION_REPORT_PREFIX = "compression_report_";
    private final static String UPLOADED_REPORTS_TO_PREFIX = "upload_reports_to_";
    private final static String UPLOADED_IMAGES_TO_PREFIX = "upload_images_to_";

    public static String getContactInsuredKey(ServiceTypeEnum type) {
        if (ProjectTypeEnum.CLAIM.equals(type.getProjectType())) {
            return DU_CONTACT_INSURED_CLAIMS.getKey();
        }
        if(ServiceTypeEnum.PREMIUM_FOUR_POINT.getCode() == type.getCode()){
            return DU_CONTACT_INSURED_P4P.getKey();
        }
        if (ServiceTypeEnum.WHITE_GLOVE.getCode() == type.getCode()) {
            return DU_CONTACT_INSURED_WG.getKey();
        }
        return null;
    }

    public static String getAssignToPilotKey(ServiceTypeEnum type) {
        if (ProjectTypeEnum.CLAIM.equals(type.getProjectType())) {
            return ASSIGN_TO_PILOT_CLAIMS.getKey();
        }
        if(ServiceTypeEnum.PREMIUM_FOUR_POINT.getCode() == type.getCode()){
            return ASSIGN_TO_PILOT_P4P.getKey();
        }
        if (ServiceTypeEnum.WHITE_GLOVE.getCode() == type.getCode()) {
            return ASSIGN_TO_PILOT_WG.getKey();
        }
        return null;
    }

    public static String getKeyCompressionReportType(ReportTypeEnum reportType) {
        return COMPRESSION_REPORT_PREFIX + StringUtils.lowerCase(reportType.getShortCut());
    }

    /**
     * the format of key looks like upload_reports_to_symbility or upload_reports_to_xactanalysis
     */
    public static String getKeyUploadReportsTo(IntegrationType integrationType) {
        return getKeyUploadTo(UPLOADED_REPORTS_TO_PREFIX, integrationType);
    }

    public static IntegrationType parseIntegrationFromKeyUploadReportsTo(String taskKey) {
        return parseIntegrationFromKeyUploadReportsTo(UPLOADED_REPORTS_TO_PREFIX, taskKey);
    }

    /**
     * the format of key looks like uploaded_images_to_symbility or uploaded_images_to_xactanalysis
     */
    public static String getKeyUploadImagesTo(IntegrationType integrationType) {
        return getKeyUploadTo(UPLOADED_IMAGES_TO_PREFIX, integrationType);
    }

    public static IntegrationType parseIntegrationFromKeyUploadImagesTo(String taskKey) {
        return parseIntegrationFromKeyUploadReportsTo(UPLOADED_IMAGES_TO_PREFIX, taskKey);
    }

    private static String getKeyUploadTo(String prefix, IntegrationType integrationType) {
        if (integrationType == IntegrationType.UNRECOGNIZED || integrationType == IntegrationType.INTEGRATION_UNRECOGNIZED) {
            throw new IllegalArgumentException("The integration type " + integrationType + " is unsupported.");
        }
        // remove the prefix to satisfy the key length constraint
        final String TYPE_PREFIX = "INTEGRATION_";
        String type = StringUtils.removeStartIgnoreCase(integrationType.name(), TYPE_PREFIX);
        return prefix + StringUtils.lowerCase(type);
    }

    private static IntegrationType parseIntegrationFromKeyUploadReportsTo(String prefix, String taskKey) {
        if (!StringUtils.startsWithIgnoreCase(taskKey, prefix)) {
            return null;
        }
        final String TYPE_PREFIX = "INTEGRATION_";
        String type = TYPE_PREFIX + StringUtils.removeStartIgnoreCase(taskKey, prefix);
        try {
            return IntegrationType.valueOf(StringUtils.upperCase(type));
        } catch (Exception e) {
            return null;
        }
    }

}
