package com.bees360.entity.dto;

import com.bees360.entity.Project;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.project.Message;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Nullable;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @contributors
 * @date Apr 12, 2018 8:03:24 PM
 */
@Data
public class ProjectSearchOption extends ProjectSearchOptionBase {
    private static final int NONEXISTENT_CLAIM_TYPE = Integer.MIN_VALUE;

    public static final String NEW_DAYS_OLD_KEY = "newDaysOld";
    public enum SearchTagEnum {
        RUSH_FOLDER,
        NOT_CONTACTED,
        UNASSIGNED,
        IMAGE_NOT_UPLOADED,
        IN_PROCESS,
        FOLLOW_UP;
    }
	private Long startTime;
	private Long endTime;
    private Long searchProjectId;
    private String searchAddress;
    private String searchClaimNumber;
    private Integer searchStatus;
    private Long searchCompanyId;
    private List<Long> searchCompanyIdList;
    private Long creator;
    private Boolean self;
    private Integer inspectionTypes;

    private Long startProjectId;

    /**
     * Inspection/Claim number
     */
    private String inspectionNumber;

    /**
     * multi search support of Inspection/Claim Number
     */
    private List<String> inspectionNumbers;

    /**
     *  Same as com.bees360.flyzone.FlyZoneType::getCode()
     */
    private Integer flyZoneType;
    private List<Integer> flyZoneTypes;
    private List<Long> projectIds;
    private Integer reportType;
    private Long memberUser;
    private List<Long> memberUserList;
    private List<Long> creatorList;
    // operations manager user list
    private List<String> operationsManagerList;
    private Long memberRole;
    private Long assignStartTime;
    private Long assignEndTime;
    private Boolean testCase;
    /**
     * @see Project#getPolicyNumber()
     */
    private String policyNumber;

    /**
     * multi search support of Policy Number
     */
    private List<String> policyNumbers;

    /**
     * @see Project#getAssetOwnerName()
     */
    private String insuredName;
    private String insuredPhone;
    private String address;
    private String city;
    private String state;
    private List<String> states;
    private String zipCode;
    private Long dueDateStart;
    private Long dueDateEnd;

    private Integer projectStatus;
    private List<Integer> projectStatusList;
    private List<Integer> projectExcludeStatusList;
    private Long projectStatusStartTime;
    private Long projectStatusEndTime;

    private Long inspectionStartTime;
    private Long inspectionEndTime;
    private Long scheduledTimeStart;
    private Long scheduledTimeEnd;
    private Integer[] inspectionPurposeTypes;
    private List<Integer> serviceTypes;

    private List<Long> projectLabels;
    private List<String> projectTags;

    private LocalDate policyEffectiveStartDate;
    private LocalDate policyEffectiveEndDate;
    private Integer daysOldStart;
    private Integer daysOldEnd;

    private Set<Long> overdueExcludeTagIds;

    private String batchNo;

    /**
     * add search tag for special search, eg: Rush folder search.
     * @see SearchTagEnum
     */
    private Set<String> searchTag;

    /**
     * 搜索projectId 大于或者等这个值的项目
     */
    private Long projectIdStart;

    private Boolean testFlag;

    // 从WEB还是IO进行的查询,default:WEB
    private String via = "WEB";

    public final String SEPARATOR = ",";

    public final Long NA_LABEL_SEARCH = -1L;

    private Set<Integer> claimTypes;

    private Set<Integer> projectTypes;

    private Integer contactRecordTimes;

    private Integer contactRecordType;

    private String contactInitiator;

    private String contactRecipient;

    private boolean deletedInclusion;
    private Boolean deleted;

    private Boolean paid;

    private List<Long> insuranceCompanyIdList;
    private List<Long> processCompanyIdList;

    private Integer newDaysOldStart;
    private Integer newDaysOldEnd;

    private Set<String> projectStateChangeReasons;
    private Set<String> projectStateChangeReasonIds;
    private Set<String> changeReasonGroups;
    private List<Message.ProjectMessage.ProjectState.ProjectStateEnum> projectStateList;

    private List<MemberRole> memberRoles;

    private List<String> operatingCompanyList;

    private List<String> airspaceStatuses;
    @Data
    public static class MemberRole{
        private String role;
        private String userId;
    }

    @Override
    public String getSortKey() {
        return sortKey;
	}

    @Override
    public void setSortKey(String sortKey) {
        this.sortKey = sortKey;
	}

    @Override
    public String getSortOrder() {
        return sortOrder;
	}

    @Override
    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
	}

    public Integer[] getInspectionPurposeTypes() {
        return inspectionPurposeTypes;
    }
    public void setInspectionPurposeTypes(String inspectionPurposeTypes) {
        this.inspectionPurposeTypes = checkPurposeTypes(inspectionPurposeTypes);
    }

    public Integer[] checkPurposeTypes(String inspectionPurposeTypes) {

        if (StringUtils.isBlank(inspectionPurposeTypes)) {
            return null;
        }
        String[] inspectionPurposeTypeStrs = inspectionPurposeTypes.split(SEPARATOR);
        List<Integer> typesList = new ArrayList<>();
        for (String inspectionPurposeTypeStr : inspectionPurposeTypeStrs) {
            if (isNumeric(inspectionPurposeTypeStr)) {
                int code = Integer.valueOf(inspectionPurposeTypeStr);
                typesList.add(code);
            }
        }
        Integer[] integers = new Integer[typesList.size()];
        for (int i = 0; i < typesList.size(); i++) {
            integers[i] = typesList.get(i);
        }
        return integers;
    }

    public static long getDaysOldCenterUtcOffset() {
        return Duration.between(ZonedDateTime.now(ZoneId.of("US/Central")).toLocalDateTime(), ZonedDateTime.now(ZoneId.of("UTC")).toLocalDateTime()).toHours();
    }

    public Map<String, Object> toMap() {
		Map<String, Object> searchMap = new HashMap<>();

        addIfNotBlankOrEmpty(searchMap, "startTime", startTime);
        addIfNotBlankOrEmpty(searchMap, "endTime", endTime);

        addIfNotBlankOrEmpty(searchMap, "startProjectId", startProjectId);

        addIfNotBlankOrEmpty(searchMap, "searchAddress", searchAddress);
        addIfNotBlankOrEmpty(searchMap, "searchClaimNumber", searchClaimNumber);

        addIfNotBlankOrEmpty(searchMap, "searchProjectId", searchProjectId);
        addIfNotBlankOrEmpty(searchMap, "searchLatestStatus", searchStatus);
        addIfNotBlankOrEmpty(searchMap, "searchCompanyId", searchCompanyId);
        addIfNotBlankOrEmpty(searchMap, "searchCompanyIdList", searchCompanyIdList);
		addIfNotBlankOrEmpty(searchMap, "creator", creator);
        addIfNotBlankOrEmpty(searchMap, "inspectionTypes", inspectionTypes);
        addIfNotBlankOrEmpty(searchMap, "flyZoneType", flyZoneType);
        addIfNotBlankOrEmpty(searchMap, "flyZoneTypes", flyZoneTypes);
        addIfNotBlankOrEmpty(searchMap, "reportType", reportType);
        addIfNotBlankOrEmpty(searchMap, "projectIds", projectIds);
        addIfNotBlankOrEmpty(searchMap, "memberUser", memberUser);
        addIfNotBlankOrEmpty(searchMap, "memberUserList", memberUserList);
        addIfNotBlankOrEmpty(searchMap, "memberRole", memberRole);
        addIfNotBlankOrEmpty(searchMap, "assignStartTime", assignStartTime);
        addIfNotBlankOrEmpty(searchMap, "assignEndTime", assignEndTime);

        addIfNotBlankOrEmpty(searchMap, "policyNumber", policyNumber);
        addIfNotBlankOrEmpty(searchMap, "policyNumbers", policyNumbers);
        addIfNotBlankOrEmpty(searchMap, "insuredName", insuredName);
        if (insuredPhone != null) {
            searchMap.put("insuredPhone", insuredPhone);
        }
        addIfNotBlankOrEmpty(searchMap, "address", address);
        addIfNotBlankOrEmpty(searchMap, "city", city);
        addIfNotBlankOrEmpty(searchMap, "state", state);
        addIfNotBlankOrEmpty(searchMap, "states", states);
        addIfNotBlankOrEmpty(searchMap, "zipCode", zipCode);
        addIfNotBlankOrEmpty(searchMap, "dueDateStart", dueDateStart);
        addIfNotBlankOrEmpty(searchMap, "dueDateEnd", dueDateEnd);
        addIfNotBlankOrEmpty(searchMap, "projectStatus", projectStatus);
        addIfNotBlankOrEmpty(searchMap, "projectStatusList", projectStatusList);
        addIfNotBlankOrEmpty(searchMap, "projectExcludeStatusList", projectExcludeStatusList);
        addIfNotBlankOrEmpty(searchMap, "inspectionStartTime", inspectionStartTime);
        addIfNotBlankOrEmpty(searchMap, "inspectionEndTime", inspectionEndTime);
        addIfNotBlankOrEmpty(searchMap, "scheduledTimeStart", scheduledTimeStart);
        addIfNotBlankOrEmpty(searchMap, "scheduledTimeEnd", scheduledTimeEnd);
        addIfNotBlankOrEmpty(searchMap, "inspectionPurposeTypes", inspectionPurposeTypes);
        addIfNotBlankOrEmpty(searchMap, "inspectionNumber", inspectionNumber);
        addIfNotBlankOrEmpty(searchMap, "inspectionNumbers", inspectionNumbers);
        addIfNotBlankOrEmpty(searchMap, "creatorList", creatorList);
        addIfNotBlankOrEmpty(searchMap, "claimTypes", getClaimTypes());
        addIfNotBlankOrEmpty(searchMap, "self", self, false);
        addIfNotBlankOrEmpty(searchMap, "serviceTypes", serviceTypes);
        addIfNotBlankOrEmpty(searchMap, "policyEffectiveStartDate", policyEffectiveStartDate);
        addIfNotBlankOrEmpty(searchMap, "policyEffectiveEndDate", policyEffectiveEndDate);
        addIfNotBlankOrEmpty(searchMap, "daysOldStart", daysOldStart);
        addIfNotBlankOrEmpty(searchMap, "daysOldEnd", daysOldEnd);
        addIfNotBlankOrEmpty(searchMap, "centerUtcOffset", getDaysOldCenterUtcOffset());
        addIfNotBlankOrEmpty(searchMap, "batchNo", batchNo);

        addIfNotBlankOrEmpty(searchMap, "naLabel", naLabel() ? NA_LABEL_SEARCH : null);
        addIfNotBlankOrEmpty(searchMap, "labelIds", getProjectLabels());
        addIfNotBlankOrEmpty(searchMap, "projectTags", projectTags);
        addIfNotBlankOrEmpty(searchMap, "operationsManagerList", operationsManagerList);
        addIfNotBlankOrEmpty(searchMap, "searchTag", searchTag);
        addIfNotBlankOrEmpty(searchMap, "projectIdStart", projectIdStart);
        addIfNotBlankOrEmpty(searchMap, "testFlag", testFlag);
        addIfNotBlankOrEmpty(searchMap, "projectTypes", projectTypes);
        addIfNotBlankOrEmpty(searchMap, "paid", paid);

        addIfNotBlankOrEmpty(searchMap, "insuranceCompanyIdList", insuranceCompanyIdList);
        addIfNotBlankOrEmpty(searchMap, "processCompanyIdList", processCompanyIdList);

        addIfNotBlankOrEmpty(searchMap, "newDaysOldStart", newDaysOldStart);
        addIfNotBlankOrEmpty(searchMap, "newDaysOldEnd", newDaysOldEnd);
        addIfNotBlankOrEmpty(searchMap, "projectStateChangeReasons", projectStateChangeReasons);
        addIfNotBlankOrEmpty(searchMap, "projectStateChangeReasonIds", projectStateChangeReasonIds);

        addIfNotBlankOrEmpty(searchMap, "projectStateList", projectStateList);
        addIfNotBlankOrEmpty(searchMap, "operatingCompanyList", operatingCompanyList);

        searchMap.put("deletedInclusion", deletedInclusion);
        addIfNotBlankOrEmpty(searchMap, "isDeleted", deleted);
        setBaseSearchOption(searchMap);
		return searchMap;
	}

	public List<Long> getProjectLabels(){
        if (projectLabels == null){
            return null;
        }
        return projectLabels.stream().filter(labelId -> !Objects.equals(labelId, NA_LABEL_SEARCH)).collect(Collectors.toList());
    }

    public boolean naLabel(){
        if (projectLabels == null){
            return false;
        }
        return projectLabels.contains(NA_LABEL_SEARCH);
    }

    public Set<Integer> getClaimTypes() {
        var claimTypesFromInspectionPurpose = getClaimTypesFromInspectionPurpose();
        if (CollectionUtils.isEmpty(claimTypes)) {
            return claimTypesFromInspectionPurpose;
        }
        if (CollectionUtils.isEmpty(claimTypesFromInspectionPurpose)) {
            return claimTypes;
        }
        var result = CollectionUtils.intersection(claimTypesFromInspectionPurpose, claimTypes);
        return result.isEmpty()? Set.of(NONEXISTENT_CLAIM_TYPE): Set.copyOf(result);
    }

    @Nullable
    private Set<Integer> getClaimTypesFromInspectionPurpose() {
        if (inspectionPurposeTypes == null) {
            return null;
        }
        Set<Integer> purposeType = new HashSet<>(Arrays.asList(inspectionPurposeTypes));
        Set<InspectionPurposeTypeEnum> serviceTypeEnums = purposeType.stream().map(InspectionPurposeTypeEnum::getEnum)
            .filter(Objects::nonNull).collect(Collectors.toSet());

        return CollectionUtils.isEmpty(purposeType)? new HashSet<>():
            serviceTypeEnums.stream().map(InspectionPurposeTypeEnum::getSubTypes)
                .flatMap(subTypes -> subTypes.stream().map(ClaimTypeEnum::getCode))
                .collect(Collectors.toSet());
    }

    private static boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[0-9]+");
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    @Override
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public void setPageSize(int pageSize, int max){
        setPageSize(Math.min(pageSize, max));
    }

    public void intersectProjectIds(@Nullable Iterable<Long> projectIds) {
        if (projectIds == null) {
            return;
        }
        if (this.projectIds == null) {
            this.projectIds = new ArrayList<>();
            CollectionUtils.addAll(this.projectIds, projectIds);
        } else {
            this.projectIds = new ArrayList<>(CollectionUtils.intersection(this.projectIds, projectIds));
        }
    }

    public void intersectProjectIds(Supplier<Iterable<Long>> projectIdsSupplier) {
       intersectProjectIds(projectIdsSupplier.get());
    }


    public static void main(String[] args) {
        System.out.println(new ProjectSearchOption());
    }
}
