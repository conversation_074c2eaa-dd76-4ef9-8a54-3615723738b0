package com.bees360.entity.vo.beespilot;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/7/7 12:08 PM
 **/
@Data
@NoArgsConstructor
public class BeesPilotProjectVo {
    private Long projectId;
    private Long createdTime;

    private String address;
    private String city;
    private String state;
    private String country;
    private String zipCode;

    private double gpsLocationLongitude;
    private double gpsLocationLatitude;

    /**
     * 该任务是否全部完成
     *
     * @see com.bees360.entity.enums.ProjectStatusEnum#TASK_CHECKED_IN
     * @see com.bees360.entity.enums.ProjectStatusEnum#TASK_CHECKED_OUT
     */
    private int checkStatus;

    /**
     * Same as com.bees360.flyzone.FlyZoneType::getCode()
     */
    private Integer flyZoneType;
    private Long inspectionTime;

    private Integer inspectionPurposeTypeCode;
    private String inspectionPurposeTypeName;

    /**
     * app端采集是否完成，只有总check out并且图片上传完成时，才为true
     */
    private boolean collectFinished;

    /**
     * 触发 checkout 的时间
     */
    private Long checkoutTime;

    /**
     * reviewImageUrl 图片的预览图url
     * 为Elevation Image 或者 Address Verification 图片
     */
    private String reviewImageUrl;
}
