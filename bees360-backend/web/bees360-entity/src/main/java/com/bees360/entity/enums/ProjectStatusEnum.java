package com.bees360.entity.enums;

import com.bees360.entity.dto.IdNameDto;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ProjectStatusEnum implements BaseCodeEnum {

    RECOVERED(-2, null, "Recovered"),
    CANCELED(-1, ProcessStatusEnum.CANCELED, "Canceled"),

	DELETE(0, ProcessStatusEnum.DELETED, "Deleted"),

	// the project was created
	NEW_PROJECT(1, ProcessStatusEnum.NEW, "New Project"),

	JOB_RECEIVED(2, null, "Job Received"),

    CLIENT_RECEIVED(3, ProcessStatusEnum.CLIENT_RECEIVED, "Client Received"),
    RECEIVE_ERROR(4, null, "Receive Error"),
    RETURNED_TO_CLIENT(5, null, "Returned to Client"),
    PROJECT_REWORK(18, null, "Project Rework"),

	PILOT_SCHEDULED(20, null, "Pilot Scheduled"),
	PILOT_CANCELED(21, null, "Pilot Canceled"),
	// The first time a policy holder is contacted
	POLICY_HOLDER_CONTACTED(22, null, "Policy Holder Contacted"),
	PILOT_CHECKED_IN(23, null, "Pilot Checked In"),
	PILOT_CHECKED_OUT(24, null, "Pilot Checked Out"),
	// The real time that the pilot do the inspection
	PILOT_INSPECTED(25, null, "Pilot Inspected"),
	// pilot uploaded all images, and pilot finished his job.
	IMAGE_UPLOADED(26, ProcessStatusEnum.IMAGE_UPLOADED, "Image Uploaded"),

	ADJUSTER_CLAIM_CHECKED_IN(27, null, "Adjuster Claim Checked In"),
	ADJUSTER_CLAIM_CHECKED_OUT(28, null, "Adjuster Claim Checked Out"),

	THREE_D_GENERATION_START(29, ProcessStatusEnum.IN_PROCESS, "3D Generation started"),
	THREE_D_GENERATION_FINISHED(30, ProcessStatusEnum.IN_PROCESS, "3D Generation Finished"),
	PRE_RANGING_FINISHED(31, ProcessStatusEnum.IN_PROCESS, "Pre-Ranging Finished"),
	RANGING_FINISHED(32, ProcessStatusEnum.IN_PROCESS, "Ranging Finished"),
	PRE_SCOPING_FINISHED(33, ProcessStatusEnum.IN_PROCESS, "Pre-Scoping Finished"),
	SCOPING_FINISHED(34, ProcessStatusEnum.IN_PROCESS, "Scoping Finished"),
	PRE_PLANE_FINISHED(35, ProcessStatusEnum.IN_PROCESS, "Pre-Plane Finished"),
	PLANE_FINISHED(36, ProcessStatusEnum.IN_PROCESS, "Plane Finished"),
	PRE_BOUNDARY_FINISHED(37, ProcessStatusEnum.IN_PROCESS, "Pre-Boundary Finished"),
	BOUNDARY_FINISHED(38, ProcessStatusEnum.IN_PROCESS, "Boundary Finished"),
	POST_BOUNDARY_FINISHED(39, ProcessStatusEnum.IN_PROCESS, "Post-Boundary Finished"),

	DAMAGE_ANNOTATED(40, null, "Damage Annotated"),

	ADJUSTER_UNDERWRITING_CHECKED_IN(41, null, "Underwriting Inspector Checked In"),
	ADJUSTER_UNDERWRITING_CHECKED_OUT(42, null, "Underwriting Inspector Checked Out"),

    /**
     * 任务开始 check in (包括bees go 和 bees drnoe)
     */
    TASK_CHECKED_IN(43, null, "Checked In"),
    /**
     * 所有任务结束 check out (包括bees go 和 bees drnoe)
     */
    TASK_CHECKED_OUT(44, null, "Checked out"),

	ADJUSTER_SCHEDULED(50, null, "Adjuster Scheduled"),
	ADJUSTER_CANCELLED(51, null, "Adjuster Cancelled"),
	ADJUSTER_FINISHED(52, null, "Adjuster Finished"),

	AD_STARTED(54, ProcessStatusEnum.IN_PROCESS, "Auto Detection Started"),
	AD_GENERATED(55, ProcessStatusEnum.IN_PROCESS, "Auto Detection Generated"),
	REALTIME_AD_STARTED(56, ProcessStatusEnum.IN_PROCESS, "Auto Detection Started"),
	REALTIME_AD_GENERATED(57, ProcessStatusEnum.IN_PROCESS, "Auto Detection Generated"),

	// Damage Report
	DAMAGE_REPORT_SUBMITTED(110, ProcessStatusEnum.UNDER_REVIEW, "Premium Damage Assessment Report Submitted"),
	DAMAGE_REPORT_GENERATED(62, ProcessStatusEnum.IN_PROCESS, "Premium Damage Assessment Report Generated"),
	DAMAGE_REPORT_APPROVED(111, ProcessStatusEnum.REPORT_APPROVED, "Premium Damage Assessment Report Approved"),
	DAMAGE_REPORT_DISAPPROVED(112, ProcessStatusEnum.IN_PROCESS, "Premium Damage Assessment Report Disapproved"),

	// Measurement Report
	MEASUREMENT_REPORT_SUBMITTED(121, ProcessStatusEnum.UNDER_REVIEW, "Premium Measurement Report Submitted"),
	MEASUREMENT_REPORT_GENERATED(60, ProcessStatusEnum.IN_PROCESS, "Premium Measurement Report Generated"),
	MEASUREMENT_REPORT_APPROVED(122, ProcessStatusEnum.REPORT_APPROVED, "Premium Measurement Report Approved"),
	MEASUREMENT_REPORT_DISAPPROVED(123, ProcessStatusEnum.IN_PROCESS, "Premium Measurement Report Disapproved"),

	// Image Report
	IMAGE_REPORT_SUBMITTED(124, ProcessStatusEnum.UNDER_REVIEW, "Premium Image Assessment Report Submitted"),
	IMAGE_REPORT_GENERATED(61, ProcessStatusEnum.IN_PROCESS, "Premium Image Assessment Report Generated"),
	IMAGE_REPORT_APPROVED(125, ProcessStatusEnum.REPORT_APPROVED, "Premium Image Assessment Report Approved"),
	IMAGE_REPORT_DISAPPROVED(126, ProcessStatusEnum.IN_PROCESS, "Premium Image Assessment Report Disapproved"),

	// Onsite Damage Report
	ONSITE_DAMAGE_REPORT_SUBMITTED(127, ProcessStatusEnum.UNDER_REVIEW, "Preliminary Damage Assessment Report Submitted"),
	ONSITE_DAMAGE_REPORT_GENERATED(67, ProcessStatusEnum.IN_PROCESS, "Preliminary Damage Assessment Report Generated"),
	ONSITE_DAMAGE_REPORT_APPROVED(128, ProcessStatusEnum.REPORT_APPROVED, "Preliminary Damage Assessment Report Approved"),
	ONSITE_DAMAGE_REPORT_DISAPPROVED(129, ProcessStatusEnum.IN_PROCESS, "Preliminary Damage Assessment Report Disapproved"),

	// Onsite Image Report
	ONSITE_IMAGE_REPORT_SUBMITTED(130, ProcessStatusEnum.UNDER_REVIEW, "Preliminary Image Assessment Report Report Submitted"),
	ONSITE_IMAGE_REPORT_GENERATED(68, ProcessStatusEnum.IN_PROCESS, "Preliminary Image Assessment Report Generated"),
	ONSITE_IMAGE_REPORT_APPROVED(131, ProcessStatusEnum.REPORT_APPROVED, "Preliminary Image Assessment Report Approved"),
	ONSITE_IMAGE_REPORT_DISAPPROVED(132, ProcessStatusEnum.IN_PROCESS, "Preliminary Image Assessment Report Disapproved"),

	// Damage Assessment Report
	DAMAGE_ASSESSMENT_REPORT_SUBMITTED(133, ProcessStatusEnum.UNDER_REVIEW, "Highfly Evaluation Report Submitted"),
	DAMAGE_ASSESSMENT_REPORT_GENERATED(100, ProcessStatusEnum.IN_PROCESS, "Highfly Evaluation Report Generated"),
	DAMAGE_ASSESSMENT_REPORT_APPROVED(134, ProcessStatusEnum.REPORT_APPROVED, "Highfly Evaluation Report Approved"),
	DAMAGE_ASSESSMENT_REPORT_DISAPPROVED(135, ProcessStatusEnum.IN_PROCESS, "Highfly Evaluation Report Disapproved"),

	// App Damage Report
	APP_DAMAGE_REPORT_SUBMITTED(150, ProcessStatusEnum.UNDER_REVIEW, "Property Image Report Submitted"),
	APP_DAMAGE_REPORT_GENERATED(151, ProcessStatusEnum.IN_PROCESS, "Property Image Report Generated"),
	APP_DAMAGE_REPORT_APPROVED(152, ProcessStatusEnum.REPORT_APPROVED, "Property Image Report Approved"),
	APP_DAMAGE_REPORT_DISAPPROVED(153, ProcessStatusEnum.IN_PROCESS, "Property Image Report Disapproved"),

	// Symbility XML
	SYMBILITY_XML_REPORT_GENERATED(155, null, "Symbility XML Report Generated"),

	WEATHER_REPORT_SUBMITTED(136, ProcessStatusEnum.UNDER_REVIEW, "Weather Report Submitted"),
	WEATHER_REPORT_GENERATED(63, ProcessStatusEnum.IN_PROCESS, "Weather Report Generated"),
	WEATHER_REPORT_APPROVED(137, ProcessStatusEnum.REPORT_APPROVED, "Weather Report Approved"),
	WEATHER_REPORT_DISAPPROVED(138, ProcessStatusEnum.IN_PROCESS, "Weather Report Disapproved"),

    ESTIMATE_REPORT_SUBMITTED(139, ProcessStatusEnum.UNDER_REVIEW, "Estimate Report Submitted"),
    ESTIMATE_REPORT_GENERATED(64, ProcessStatusEnum.IN_PROCESS, "Estimate Report Generated"),
    ESTIMATE_REPORT_APPROVED(140, ProcessStatusEnum.REPORT_APPROVED, "Estimate Report Approved"),
    ESTIMATE_REPORT_DISAPPROVED(141, ProcessStatusEnum.IN_PROCESS, "Estimate Report Disapproved"),

	DXF_REPORT_SUBMITTED(142, ProcessStatusEnum.UNDER_REVIEW, "Measurement DXF Report Submitted"),
	DXF_REPORT_GENERATED(143, ProcessStatusEnum.IN_PROCESS, "Measurement DXF Report Generated"),
	DXF_REPORT_APPROVED(144, ProcessStatusEnum.REPORT_APPROVED, "Measurement DXF Report Approved"),
	DXF_REPORT_DISAPPROVED(145, ProcessStatusEnum.IN_PROCESS, "Measurement DXF Report Disapproved"),

	BIDDING_SUBMITTED(147, ProcessStatusEnum.UNDER_REVIEW, "On-Site Bidding Report Submitted"),
	BIDDING_GENERATED(148, ProcessStatusEnum.IN_PROCESS, "On-Site Bidding Report Generated"),
	BIDDING_APPROVED(149, ProcessStatusEnum.REPORT_APPROVED, "On-Site Bidding Report Approved"),
	BIDDING_DISAPPROVED(167, ProcessStatusEnum.IN_PROCESS, "On-Site Bidding Report Disapproved"),

	QUICK_SQUARE_SUBMITTED(159, ProcessStatusEnum.UNDER_REVIEW, "Real-time Quick Square Report Submitted"),
	QUICK_SQUARE_GENERATED(160, ProcessStatusEnum.IN_PROCESS, "Real-time Quick Square Report Generated"),
	QUICK_SQUARE_APPROVED(165, ProcessStatusEnum.REPORT_APPROVED, "Real-time Quick Square Report Approved"),
	QUICK_SQUARE_DISAPPROVED(166, ProcessStatusEnum.IN_PROCESS, "Real-time Quick Square Report Disapproved"),

	// Realtime Damage Report
	REALTIME_APP_DAMAGE_REPORT_SUBMITTED(161, ProcessStatusEnum.UNDER_REVIEW, "Real-time Damage Assessment Report Submitted"),
	REALTIME_APP_DAMAGE_REPORT_GENERATED(162, ProcessStatusEnum.IN_PROCESS, "Real-time Damage Assessment Report Generated"),
	REALTIME_APP_DAMAGE_REPORT_APPROVED(163, ProcessStatusEnum.REPORT_APPROVED, "Real-time Damage Assessment Report Approved"),
	REALTIME_APP_DAMAGE_REPORT_DISAPPROVED(164, ProcessStatusEnum.IN_PROCESS, "Real-time Damage Assessment Report Disapproved"),

	INFRARED_DAMAGE_REPORT_SUBMITTED(168, ProcessStatusEnum.UNDER_REVIEW, "Infrared Damage Assessment Report Submitted"),
	INFRARED_DAMAGE_REPORT_GENERATED(169, ProcessStatusEnum.IN_PROCESS, "Infrared Damage Assessment Report Generated"),
	INFRARED_DAMAGE_REPORT_APPROVED(170, ProcessStatusEnum.REPORT_APPROVED, "Infrared Damage Assessment Report Approved"),
	INFRARED_DAMAGE_REPORT_DISAPPROVED(171, ProcessStatusEnum.IN_PROCESS, "Infrared Damage Assessment Report Disapproved"),

	ROOF_ONLY_UNDERWRITING_REPORT_SUBMITTED(172, ProcessStatusEnum.UNDER_REVIEW, "Roof-only Underwriting Report Submitted"),
	ROOF_ONLY_UNDERWRITING_REPORT_GENERATED(173, ProcessStatusEnum.IN_PROCESS, "Roof-only Underwriting Report Generated"),
	ROOF_ONLY_UNDERWRITING_REPORT_APPROVED(174, ProcessStatusEnum.REPORT_APPROVED, "Roof-only Underwriting Report Approved"),
	ROOF_ONLY_UNDERWRITING_REPORT_DISAPPROVED(175, ProcessStatusEnum.IN_PROCESS, "Roof-only Underwriting Report Disapproved"),

	FULL_SCOPE_UNDERWRITING_REPORT_SUBMITTED(176, ProcessStatusEnum.UNDER_REVIEW, "Full-scope Underwriting Report Submitted"),
	FULL_SCOPE_UNDERWRITING_REPORT_GENERATED(177, ProcessStatusEnum.IN_PROCESS, "Full-scope Underwriting Report Generated"),
	FULL_SCOPE_UNDERWRITING_REPORT_APPROVED(178, ProcessStatusEnum.REPORT_APPROVED, "Full-scope Underwriting Report Approved"),
	FULL_SCOPE_UNDERWRITING_REPORT_DISAPPROVED(179, ProcessStatusEnum.IN_PROCESS, "Full-scope Underwriting Report Disapproved"),

    LETTER_OF_CANCELLATION_REPORT_SUBMITTED(180, ProcessStatusEnum.UNDER_REVIEW, "Letter Of Cancellation Report Submitted"),
    LETTER_OF_CANCELLATION_REPORT_GENERATED(181, ProcessStatusEnum.IN_PROCESS, "Letter Of Cancellation Report Generated"),
    LETTER_OF_CANCELLATION_REPORT_APPROVED(182, ProcessStatusEnum.REPORT_APPROVED, "Letter Of Cancellation Report Approved"),
    LETTER_OF_CANCELLATION_REPORT_DISAPPROVED(183, ProcessStatusEnum.IN_PROCESS, "Letter Of Cancellation Report Disapproved"),

    CLAIM_DAMAGE_FORM_SUBMITTED(184, ProcessStatusEnum.UNDER_REVIEW, "Claim Damage Form Submitted"),
    CLAIM_DAMAGE_FORM_GENERATED(185, ProcessStatusEnum.IN_PROCESS, "Claim Damage FormGenerated"),
    CLAIM_DAMAGE_FORM_APPROVED(186, ProcessStatusEnum.REPORT_APPROVED, "Claim Damage Form Approved"),
    CLAIM_DAMAGE_FORM_DISAPPROVED(187, ProcessStatusEnum.IN_PROCESS, "Claim Damage Form Disapproved"),

    POST_CONSTRUCTION_REPORT_SUBMITTED(191, ProcessStatusEnum.UNDER_REVIEW, "Post-Construction Audit Report Submitted"),
    POST_CONSTRUCTION_REPORT_GENERATED(192, ProcessStatusEnum.IN_PROCESS, "Post-Construction Audit Report Generated"),
    POST_CONSTRUCTION_REPORT_APPROVED(193, ProcessStatusEnum.REPORT_APPROVED, "Post-Construction Audit Report Approved"),
    POST_CONSTRUCTION_REPORT_DISAPPROVED(194, ProcessStatusEnum.IN_PROCESS, "Post-Construction Audit Report Disapproved"),

    HOMEOWNER_SURVEY_REPORT_SUBMITTED(210, ProcessStatusEnum.UNDER_REVIEW, "Homeowner Inspection Survey Results Report Submitted"),
    HOMEOWNER_SURVEY_REPORT_GENERATED(211, ProcessStatusEnum.IN_PROCESS, "Homeowner Inspection Survey Results Report Generated"),
    HOMEOWNER_SURVEY_REPORT_APPROVED(212, ProcessStatusEnum.REPORT_APPROVED, "Homeowner Inspection Survey Results Report Approved"),
    HOMEOWNER_SURVEY_REPORT_DISAPPROVED(213, ProcessStatusEnum.IN_PROCESS, "Homeowner Inspection Survey Results Report Disapproved"),

    EUR_REPORT_SUBMITTED(214, ProcessStatusEnum.UNDER_REVIEW, "Express Underwriting Report Submitted"),
    EUR_REPORT_GENERATED(215, ProcessStatusEnum.IN_PROCESS, "Express Underwriting Report Generated"),
    EUR_REPORT_APPROVED(216, ProcessStatusEnum.REPORT_APPROVED, "Express Underwriting Report Approved"),
    EUR_REPORT_DISAPPROVED(217, ProcessStatusEnum.IN_PROCESS, "Express Underwriting Report Disapproved"),

    CHR_REPORT_SUBMITTED(218, ProcessStatusEnum.UNDER_REVIEW, "Custom Home Report Submitted"),
    CHR_REPORT_GENERATED(219, ProcessStatusEnum.IN_PROCESS, "Custom Home Report Generated"),
    CHR_REPORT_APPROVED(220, ProcessStatusEnum.REPORT_APPROVED, "Custom Home Report Approved"),
    CHR_REPORT_DISAPPROVED(221, ProcessStatusEnum.IN_PROCESS, "Custom Home Report Disapproved"),

	BEESSKETCH_REPORT_SUBMITTED(222, ProcessStatusEnum.UNDER_REVIEW, "BeesSketch Report Submitted"),
	BEESSKETCH_REPORT_GENERATED(223, ProcessStatusEnum.IN_PROCESS, "BeesSketch Report Generated"),
	BEESSKETCH_REPORT_APPROVED(224, ProcessStatusEnum.REPORT_APPROVED, "BeesSketch Report Approved"),
	BEESSKETCH_REPORT_DISAPPROVED(225, ProcessStatusEnum.IN_PROCESS, "BeesSketch Report Disapproved"),

	SNAP_REPORT_SUBMITTED(226, ProcessStatusEnum.UNDER_REVIEW, "Snap360 Report Submitted"),
	SNAP_REPORT_GENERATED(227, ProcessStatusEnum.IN_PROCESS, "Snap360 Report Generated"),
	SNAP_REPORT_APPROVED(228, ProcessStatusEnum.REPORT_APPROVED, "Snap360 Report Approved"),
	SNAP_REPORT_DISAPPROVED(229, ProcessStatusEnum.IN_PROCESS, "Snap360 Report Disapproved"),

	MACRO_XML_GENERATED(146, null, "Macro XML Report Generated"),

	CLAIM_REVIEWED(70, null, "Claim Reviewed"),

	// Claim report was submitted to insurance company
	CLAIM_SUBMITTED(71, null, "Claim Submitted"),

	CONSTRUCTION_SCHEDULED(80, null, "Construction Scheduled"),
	CONSTRUCTION_CANCELLED(81, null, "Construction Cancelled"),
	CONSTRUCTION_FINISHED(82, null, "Construction Finished"),

	// Claim Estimate was approved by insurance company
	COMPLETED(90, null, "Completed"),

    PART_IMAGE_UPLOADED(199, ProcessStatusEnum.IMAGE_UPLOADED, "Images Uploaded"),

    ALL_IMAGE_UPLOADED(200, ProcessStatusEnum.IMAGE_UPLOADED, "All image has been uploaded"),

    //stop 3d manually and forcibly, it is before Pre-Ranging
    THREE_D_GENERATION_STOPPED(999, ProcessStatusEnum.IN_PROCESS, "3D Generation Stopped"),
    //add a status between 3d-finished and preRange finished in order to start preRange dependently.
    PRE_RANGING_START(1000, ProcessStatusEnum.IN_PROCESS, "Pre-Ranging started"),
    PRE_RANGING_STOPPED(1001, ProcessStatusEnum.IN_PROCESS, "Pre-Ranging Stopped"),
    PRE_SCOPING_STOPPED(1002, ProcessStatusEnum.IN_PROCESS, "Pre-Scoping Stopped"),
    PRE_PLANE_STOPPED(1003, ProcessStatusEnum.IN_PROCESS, "Pre-Plane Stopped"),
    PRE_BOUNDARY_STOPPED(1004, ProcessStatusEnum.IN_PROCESS, "Pre-Boundary Stopped"),
    POST_BOUNDARY_STOPPED(1005, ProcessStatusEnum.IN_PROCESS, "Post-Boundary Stopped"),

    // ## start with 2000
    BEES_IS_NOT_WORKING(2000, null, "Checkout Reason: BeesPilot™ is not working"),
    RESTRICTED_AIRSPACE(2001, null, "Checkout Reason: Restricted airspace"),
    NO_INTERNET(2002, null, "Checkout Reason: No internet connection"),
    OTHERS(2003, null, "Checkout Reason: Others"),
    VERIFY_ADDRESS_CHECKED_IN(2004, null, "Verify address check in"),
    VERIFY_ADDRESS_CHECKED_OUT(2005, null, "Verify address check out"),
    FORM_CHECKED_IN(2006, null, "Form check in"),
    FORM_CHECKED_OUT(2007, null, "Form check out"),
    INTERIOR_CHECKED_IN(2008, null, "Take interior images check in"),
    INTERIOR_CHECKED_OUT(2009, null, "Take interior images check out"),
    EXTERIOR_CHECKED_IN(2010, null, "Take exterior images check in"),
    EXTERIOR_CHECKED_OUT(2011, null, "Take exterior images check out"),
    HOVER_CHECK_IN(2012, null, "Hover check in"),
    HOVER_CHECK_OUT(2013, null, "Hover check out"),
    PLANR_CHECK_IN(2014, null, "Planr check in"),
    PLANR_CHECK_OUT(2015, null, "Planr check out"),
    TAKE_COMMUNITY_GATE_CHECK_IN(2016, null, "Take community gate image check in"),
    TAKE_COMMUNITY_GATE_CHECK_OUT(2017, null, "Take community gate image check out"),
    REJECTED_INTERIOR_INSPECTION(2021, null, "Check Reason: The insured rejected the interior inspection"),
    INSURED_NOT_AT_HOME(2022, null, "Check Reason: The insured was not at home"),
    COVID_19_CONCERNS(2023, null, "Check Reason: COVID-19 concerns"),
    INSURED_NOT_MOVED_IN(2024, null, "Check Reason: The insured has not moved in yet"),
    AGGRESSIVE_ANIMAL(2025, null, "Check Reason: Aggressive animal was present"),
    SUSPICIOUS_ENVIRONMENT(2026, null, "Check Reason: Suspicious environment was present"),

    DATA_TRANSFERRED(1012, ProcessStatusEnum.IN_PROCESS, "Project Data Transferred"),

    PILOT_ASSIGNED(1101, null, "Assigned to Pilot"),
    PILOT_UNASSIGNED(1102, null, "Pilot Canceled"),

    UPDATE_SERVICE_TYPE(1103, null, "Project ServiceType Updated"),
    WAITING_FOR_ACCEPTANCE(1104, null, "Pending Acceptance")

    ;

    private static final String STOOPED = "Stopped";
	private final int code;
	private final String display;
	private final ProcessStatusEnum category;
	private final static List<ProjectStatusEnum> aiFlow;
	private final static List<ProjectStatusEnum> aiFlowFromUser;

	static{
		// [!important] Don't change the order
		aiFlow = new ArrayList<ProjectStatusEnum>();
		aiFlowFromUser = new ArrayList<ProjectStatusEnum>();

		aiFlow.add(ProjectStatusEnum.THREE_D_GENERATION_START);
		aiFlowFromUser.add(ProjectStatusEnum.THREE_D_GENERATION_START);

		//stop 3d manually and forcibly
        aiFlow.add(ProjectStatusEnum.THREE_D_GENERATION_STOPPED);
        aiFlowFromUser.add(ProjectStatusEnum.THREE_D_GENERATION_STOPPED);

		aiFlow.add(ProjectStatusEnum.THREE_D_GENERATION_FINISHED);
		aiFlow.add(ProjectStatusEnum.PRE_RANGING_START);
        aiFlowFromUser.add(ProjectStatusEnum.PRE_RANGING_START);

        //stop pre-ranging manually and forcibly
        aiFlow.add(ProjectStatusEnum.PRE_RANGING_STOPPED);
        aiFlowFromUser.add(ProjectStatusEnum.PRE_RANGING_STOPPED);

		aiFlow.add(ProjectStatusEnum.PRE_RANGING_FINISHED);

		aiFlow.add(ProjectStatusEnum.RANGING_FINISHED);
		aiFlowFromUser.add(ProjectStatusEnum.RANGING_FINISHED);

		//stop pre-scoping manually and forcibly
        aiFlow.add(ProjectStatusEnum.PRE_SCOPING_STOPPED);
        aiFlowFromUser.add(ProjectStatusEnum.PRE_SCOPING_STOPPED);
		aiFlow.add(ProjectStatusEnum.PRE_SCOPING_FINISHED);

		aiFlow.add(ProjectStatusEnum.SCOPING_FINISHED);
		aiFlowFromUser.add(ProjectStatusEnum.SCOPING_FINISHED);

        //stop pre-plane manually and forcibly
        aiFlow.add(ProjectStatusEnum.PRE_PLANE_STOPPED);
        aiFlowFromUser.add(ProjectStatusEnum.PRE_PLANE_STOPPED);
		aiFlow.add(ProjectStatusEnum.PRE_PLANE_FINISHED);

		aiFlow.add(ProjectStatusEnum.PLANE_FINISHED);
		aiFlowFromUser.add(ProjectStatusEnum.PLANE_FINISHED);

        //stop pre-boundary manually and forcibly
        aiFlow.add(ProjectStatusEnum.PRE_BOUNDARY_STOPPED);
        aiFlowFromUser.add(ProjectStatusEnum.PRE_BOUNDARY_STOPPED);

		aiFlow.add(ProjectStatusEnum.PRE_BOUNDARY_FINISHED);

		aiFlow.add(ProjectStatusEnum.BOUNDARY_FINISHED);
		aiFlowFromUser.add(ProjectStatusEnum.BOUNDARY_FINISHED);

        //stop pre-boundary manually and forcibly
        aiFlow.add(ProjectStatusEnum.POST_BOUNDARY_STOPPED);
        aiFlowFromUser.add(ProjectStatusEnum.POST_BOUNDARY_STOPPED);

		aiFlow.add(ProjectStatusEnum.POST_BOUNDARY_FINISHED);
	}

	ProjectStatusEnum(int code, ProcessStatusEnum category, String display){
		this.code = code;
		this.category = category;
		this.display = display;
	}

	public static ProjectStatusEnum getEnum(int code){
		ProjectStatusEnum[] statuses = ProjectStatusEnum.values();
		for(ProjectStatusEnum status: statuses){
			if(status.getCode() == code){
				return status;
			}
		}
		return null;
	}


    public static ProjectStatusEnum getStoppedStatusEnum(String statusName) {
	    assert statusName != null && !"".equals(statusName);
        ProjectStatusEnum[] statuses = ProjectStatusEnum.values();
        for(ProjectStatusEnum status: statuses){
            if(status.getDisplay().indexOf(statusName) >= 0 && isStoppedStatus(status)){
                return status;
            }
        }
        return null;
    }

    public static boolean isStoppedStatus(ProjectStatusEnum projectStatusEnum){
        return projectStatusEnum.getDisplay().endsWith(STOOPED);
    }

	@Override
	public int getCode(){
		return code;
	}
	public String getCategory(){
		return category == null? "": category.getDisplay();
	}
	public ProcessStatusEnum getProcessStatus() {
		return category;
	}
	@Override
	public String getDisplay(){
		return display;
	}


	//[start] category
	public static String codeToCategory(int code){
		ProjectStatusEnum status = getEnum(code);
		if(status == null){
			return "";
		}
		return status.getCategory();
	}

	public static List<ProjectStatusEnum> listForCategory(String category){
		List<ProjectStatusEnum> categories = new ArrayList<ProjectStatusEnum>();
		ProjectStatusEnum[] statuses = ProjectStatusEnum.values();
		for(ProjectStatusEnum status: statuses) {
			if(status.getCategory().equals(category)){
				categories.add(status);
			}
		}
		return categories;
	}

    /**
     * 该工程是否已经check out
     * @param code
     * @return
     */
    public static boolean isTaskCheckedOut(int code) {
	    return code == TASK_CHECKED_OUT.code;
    }
	public static List<Integer> listCodeForCategory(String category){
		List<Integer> categories = new ArrayList<Integer>();
		ProjectStatusEnum[] statuses = ProjectStatusEnum.values();
		for(ProjectStatusEnum status: statuses) {
			if(status.getCategory().equals(category)){
				categories.add(status.getCode());
			}
		}
		return categories;
	}
	//[end] category

	// [start] AiFlow
	public static ProjectStatusEnum firstAiFlow(){
		return aiFlow.get(0);
	}

	public static ProjectStatusEnum lastAiFlow(){
		return aiFlow.get(aiFlow.size() - 1);
	}

	public boolean isInAiFlow(){
		return aiFlow.contains(this);
	}

	public static boolean isInAiFlow(int code){
		ProjectStatusEnum status = getEnum(code);
		if(status == null) {
			return false;
		}
		return aiFlow.contains(status);
	}


	/**
	 * Judge whether the statusLeft is before statusRight,
	 * 		value null is after all ai flow status.
	 * @param statusLeft
	 * @param statusRight
	 * @return
	 * 		true statusLeft is before statusRight
	 * 		false statusLeft is not before statusRight
	 */
	public static boolean isBeforeInAIFlow(ProjectStatusEnum statusLeft,
			ProjectStatusEnum statusRight){
		// null status is after all aiflow status
		if(statusLeft == statusRight){
			return false;
		}
		if(statusLeft == null || statusRight == null){
			if (statusLeft == null){
				if(!statusRight.isInAiFlow()){
					throw new IllegalArgumentException("The status must belong to AIFlow.");
				}
				return false;
			} else {
				if(!statusLeft.isInAiFlow()){
					throw new IllegalArgumentException("The status must belong to AIFlow.");
				}
				return true;
			}
		}
		int left = -1;
		int right = -1;
		for(int i = 0; i < aiFlow.size(); i ++){
			ProjectStatusEnum status = aiFlow.get(i);
			if(status == statusLeft){
				left = i;
			}
			if(status == statusRight){
				right = i;
			}
		}
		if(left == -1 || right == -1){
			throw new IllegalArgumentException("The status must belong to AIFlow.");
		}
		return left < right;
	}

	public static ProjectStatusEnum nextAiFlowStatus(ProjectStatusEnum curStatus){
		if(curStatus == null){
			return firstAiFlow();
		}
		int curIndex = -1;
		for(int i = 0; i < aiFlow.size(); i ++){
			if(curStatus == aiFlow.get(i)){
				curIndex = i;
				break;
			}
		}
		if(curIndex == -1){
			return firstAiFlow();
		}
		if(curIndex == aiFlow.size() - 1){
			return null;
		}
		return aiFlow.get(curIndex + 1);
	}

    public static ProjectStatusEnum previousAiFlowStatus(ProjectStatusEnum curStatus){
        if(curStatus == null){
            return firstAiFlow();
        }
        int curIndex = -1;
        for(int i = 0; i < aiFlow.size(); i ++){
            if(curStatus == aiFlow.get(i)){
                curIndex = i;
                break;
            }
        }
        if(curIndex == -1){
            return firstAiFlow();
        }
        if(curIndex == 0){
            return null;
        }
        return aiFlow.get(curIndex - 1);
    }

	public boolean isAiRequestFromUser() {
		return aiFlowFromUser.contains(this);
	}
	// [end] AiFlow

	public IdNameDto toIdName() {
		return new IdNameDto(code, display);
	}

	public static void checkDuplicateCode() {
		Map<Integer, ProjectStatusEnum> codes = new HashMap<Integer, ProjectStatusEnum>();
		Map<Integer, List<ProjectStatusEnum>> duplicateCodes = new LinkedHashMap<Integer, List<ProjectStatusEnum>>();

		for(ProjectStatusEnum status: ProjectStatusEnum.values()) {
			if(codes.containsKey(status.getCode())) {
				if(duplicateCodes.containsKey(status.getCode())) {
					duplicateCodes.get(status.getCode()).add(status);
				} else {
					List<ProjectStatusEnum> statuses = Arrays.asList(status, codes.get(status.getCode()));
					duplicateCodes.put(status.getCode(), statuses);
				}
			} else {
				codes.put(status.getCode(), status);
			}
		}
		StringBuilder sb = new StringBuilder();
		sb.append("Duplicated Code: \n");
		for(Entry<Integer, List<ProjectStatusEnum>> entry: duplicateCodes.entrySet()) {
			sb.append("\t" + entry.getKey() + ": " + entry.getValue() + "\n");
		}
		System.err.println(sb.toString());
	}

	private static void getSettableCode() {
		List<Integer> codes = new ArrayList<Integer>();
		for(ProjectStatusEnum status: ProjectStatusEnum.values()) {
			codes.add(status.getCode());
		}
		Collections.sort(codes);
		int min = codes.get(0);
		int max = codes.get(codes.size() - 1);
		System.out.println("codes used: " + codes);
		System.out.println("codes usable:");
		System.out.print(" < " + min + ",");
		// <EMAIL> print the usable range
		int end = -1;
		for(int i = 0; i < codes.size(); i ++) {
			int start = codes.get(i) + 1;
			for(i ++; i < codes.size(); i ++) {
				if(start ==  codes.get(i)) {
					start ++;
				} else {
					end = codes.get(i) - 1;
					if(start == end) {
						System.out.print(start + ",");
					} else {
						System.out.print(start + "~" + end + ",");
					}
					i --;
					break;
				}
			}
		}
		System.out.print(max + " <");
	}

	public static void main(String[] args){
		ProjectStatusEnum[] statuses = ProjectStatusEnum.values();
		System.out.println("CODE\t" + "DISPLAY\t" + "CATEGORY\t" + "DESCRIPTION");
		for(ProjectStatusEnum status: statuses){
			System.out.println(status.getCode() + "\t" + status.getDisplay() + "\t" + status.getCategory() + "\t\r");
//			System.out.println(status.toString());
		}
		checkDuplicateCode();
		getSettableCode();
	}
}
