package com.bees360.entity.vo;

import com.bees360.entity.ProjectImage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/09/03 14:50
 */
public class ProjectImagePageResult {
    private List<? extends ProjectImage> images;
    private Pagination page;

    public ProjectImagePageResult() {

    }

    public ProjectImagePageResult(List<? extends ProjectImage> images, Pagination page) {
        this.images = images;
        this.page = page;
    }

    public List<? extends ProjectImage> getImages() {
        return images;
    }

    public void setImages(List<? extends ProjectImage> images) {
        this.images = images;
    }

    public Pagination getPage() {
        return page;
    }

    public void setPage(Pagination page) {
        this.page = page;
    }

    @Override
    public String toString() {
        return "ProjectImagePageResult{" + "images=" + images + ", page=" + page + '}';
    }
}
