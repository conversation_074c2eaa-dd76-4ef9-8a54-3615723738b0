package com.bees360.entity.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * define the bees360's permission ResourceType
 * <AUTHOR>
 *
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum PermissionTypeEnum implements BaseCodeEnum{
	MENU(1,"MENU")
	;

	private final int resourceTypeId;
	private final String display;

	PermissionTypeEnum(int resourceTypeId, String display) {
		this.resourceTypeId = resourceTypeId;
		this.display = display;
	}

	public static PermissionTypeEnum getEnum(int code){
		PermissionTypeEnum[] resourceTypes = PermissionTypeEnum.values();
		for(PermissionTypeEnum resourceType: resourceTypes){
			if(resourceType.getCode() == code){
				return resourceType;
			}
		}
		return null;
	}

	@Override
	public int getCode() {
		return resourceTypeId;
	}

	@Override
	public String getDisplay() {
		return display;
	}
}
