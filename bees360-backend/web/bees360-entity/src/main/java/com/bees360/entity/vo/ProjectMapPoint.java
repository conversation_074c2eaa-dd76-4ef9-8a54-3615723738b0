package com.bees360.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@NoArgsConstructor
public class ProjectMapPoint {
    private long projectId;
	private long inspectionTime;
	private int inspectionPurposeTypeCode;
	private String inspectionPurposeTypeName;
	private double gpsLocationLatitude;
	private double gpsLocationLongitude;
	private String address;
	private String city;
	private String state;
	private String zipCode;
	private String country;

    /**
     * field to get <code>inspectionPurposeTypeCode</code>
     *  and <code>inspectionPurposeTypeName</code>
     */
    @JsonIgnore
	private int claimType;
    /**
     * distance to center (gps location)
     * and it used for sort the result
     */
    @JsonIgnore
    private double toCenter;
}
