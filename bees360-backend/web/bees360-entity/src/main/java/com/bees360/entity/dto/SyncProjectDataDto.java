package com.bees360.entity.dto;

import com.bees360.entity.BsExportData;
import com.bees360.entity.CustomizedReportElement;
import com.bees360.entity.CustomizedReportItem;
import com.bees360.entity.ImageAnnotation;
import com.bees360.entity.ImageAnnotation2D;
import com.bees360.entity.ImageFacet;
import com.bees360.entity.OnsiteReportElement;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectFacet;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.ReportAnnotationImage;
import com.bees360.entity.ReportSummary;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

/**
 * 向WEB端同步的project相关数据
 *
 * <AUTHOR>
 * @since 2021/5/20
 */
@Data
@Builder
public class SyncProjectDataDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Project project;

    private List<ProjectReportFile> reportFiles;

    private List<ProjectImage> imageMessages;

    private List<ImageDeletedDto> imageDeletedList;

    private List<String> asynchronousImageIds;

    private List<OnsiteReportElement> reportElements;

    private List<ImageAnnotation> annotation3Ds;

    private List<ImageAnnotation2D> annotation2Ds;

    private List<ProjectFacet> facets;

    private List<ImageFacet> imageFacets;

    private List<CustomizedReportElement> customizedReportElements;

    private List<CustomizedReportItem> customizedReportItems;

    private List<ReportAnnotationImage> reportAnnotationImages;

    private Map<Long, Long> elementIdAnnotation3DIdMap;

    private BsExportData bsExportData;

    private List<ReportSummary> summaryList;
}
