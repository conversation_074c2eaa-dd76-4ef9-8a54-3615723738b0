package com.bees360.entity;

public class ProjectCustomizedInfo {
	private long id;
	private long projectId;
	// it must be assigned a value through the Enum SIBEnum.
	private int columnCode;
	private String columnValue;

	public ProjectCustomizedInfo() {
	}

	public ProjectCustomizedInfo(long id, long projectId, int columnCode, String columnValue) {
		this.id = id;
		this.projectId = projectId;
		this.columnCode = columnCode;
		this.columnValue = columnValue;
	}

	public ProjectCustomizedInfo(long projectId, int columnCode, String columnValue) {
		this(0, projectId, columnCode, columnValue);
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public int getColumnCode() {
		return columnCode;
	}

	public void setColumnCode(int columnCode) {
		this.columnCode = columnCode;
	}

	public String getColumnValue() {
		return columnValue;
	}

	public void setColumnValue(String columnValue) {
		this.columnValue = columnValue;
	}

	@Override
	public String toString() {
		return "ProjectCustomizedInfo [id=" + id + ", projectId=" + projectId + ", columnCode=" + columnCode
				+ ", columnValue=" + columnValue + "]";
	}

}
