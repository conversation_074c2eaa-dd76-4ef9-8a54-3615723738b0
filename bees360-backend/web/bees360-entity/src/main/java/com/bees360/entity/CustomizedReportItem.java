package com.bees360.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serial;
import java.io.Serializable;

public class CustomizedReportItem implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

	private long id;
	private long projectId;
	private int reportType;
	/**
	 * 1:roof age,2:roof material,3:damage severity,4:description
	 * it must be assigned a value through the Enum CustomizedItemTypeEnum.
	 */
	private int itemType;
	private String value;

	@JsonIgnore
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	@JsonIgnore
	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	@JsonIgnore
	public int getReportType() {
		return reportType;
	}

	public void setReportType(int reportType) {
		this.reportType = reportType;
	}

	public int getItemType() {
		return itemType;
	}

	public void setItemType(int itemType) {
		this.itemType = itemType;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@Override
	public String toString() {
		return "CustomizedReportItem [id=" + id + ", projectId=" + projectId
				+ ", reportType=" + reportType + ", itemType=" + itemType + ", value=" + value + "]";
	}

}
