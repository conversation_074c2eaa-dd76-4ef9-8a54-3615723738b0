package com.bees360.entity.firebase;

import com.bees360.entity.enums.BaseCodeEnum;
import com.bees360.entity.enums.ProjectHoverStatusEnum;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * hover 状态 https://gitlab.bees360.com/engineers/beespilot/issues/493
 **/
public enum HoverJobStatusEnum implements BaseCodeEnum {

    //Waiting for the contractor or homeowner to take pictures of the home and submit them.
    UPLOADING(10, "uploading"),

    //The uploaded data has been received and we're verifying the uploaded data and authorizing the uploaded user.
    PROCESSING_UPLOAD(20, "processing_upload"),

    //The uploaded data is being submitted to our processing pipeline.
    SUBMITTING(30, "submitting"),

    //We're turning the 2D images you uploaded into a photo realistic 3D model and measurements.
    WORKING(40, "working"),

    //Your account is on our waitlist. The job will stay in this state until we take your account of the waitlist.
    WAITLISTED(50, "waitlisted"),

    //The user that uploaded this job must get approval from their org's administrator before the job will progress.
    WAITING_APPROVAL(60, "waiting_approval"),

    //3D model and measurements are ready. When the job is in this state we're fetching the results from our processing pipeline.
    RETRIEVING(70, "retrieving"),

    //Apply partner specific branding and other final processing
    PROCESSING(80, "processing"),

    //We're collecting payment for the job
    PAYING(90, "paying"),

    //All done. Your results are paid for and available.
    COMPLETE(100, "complete"),

    //We weren't able to finish processing this job.
    FAILED(110, "failed"),

    //This job was cancelled before we finished processing it.
    CANCELLED(120, "cancelled"),

    //The client was unhappy with the job and we're sending it back to the processing pipeline for corrections.
    REQUESTING_CORRECTIONS(130, "requesting_corrections"),

    //More images were uploaded for the job after processing was done. Let's pre-process those images and get them ready for the pipeline.
    PROCESSING_UPLOAD_FOR_IMPROVEMENTS(140, "processing_upload_for_improvements"),

    // We've received and processed an upload for an already complete or failed job. In this state we're sending the job back to the processing pipeline for improvements.
    REQUESTING_IMPROVEMENTS(150, "requesting_improvements");

    HoverJobStatusEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    final private String display;

    final private int code;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    public static HoverJobStatusEnum getEnum(Integer code) {
        return Stream.of(HoverJobStatusEnum.values())
            .filter(o -> Objects.equals(code, o.getCode()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("HoverJobStatusEnum not found for code " + code));
    }

    /**
     * 转化成ProjectHoverStatusEnum
     *
     * @return
     */
    public ProjectHoverStatusEnum getProjectHoverStatusEnum() {
        switch (this) {
            case PROCESSING_UPLOAD:
            case RETRIEVING:
            case PROCESSING:
            case PAYING:
                return ProjectHoverStatusEnum.PROCESSING;
            case COMPLETE:
                return ProjectHoverStatusEnum.COMPLETED;
            case FAILED:
                return ProjectHoverStatusEnum.FAILED;
            default:
                return null;
        }
    }
}
