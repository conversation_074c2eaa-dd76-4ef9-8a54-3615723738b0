package com.bees360.entity.enums;

public enum AnnotationSourceTypeEnum implements BaseCodeEnum {
	ORIGINAL(1, "Original"),
	MAPPING(2, "Mapping"),
	TAG(3, "tag"),
    ADD(4, "ADD"),
    INFERA(5, "Infera"),
    INFERA_MAPPING(6, "Infera_mapping"),
    ;

	private final int code;
	private final String display;

	AnnotationSourceTypeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}
	@Override
	public String getDisplay() {
		return display;
	}

	public static AnnotationSourceTypeEnum getEnum(int code){
		AnnotationSourceTypeEnum[] types = AnnotationSourceTypeEnum.values();
		for(AnnotationSourceTypeEnum type: types){
			if(type.code == code){
				return type;
			}
		}
		return null;
	}

}
