package com.bees360.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;

/**
 * BeesPilotStatus
 * <AUTHOR>
 * @since 2020/07/31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BeesPilotBatchItemVo {

    private long id;

    private String batchNo;

    private Long projectId;

    private Timestamp createdAt;

    private Timestamp updatedAt;

    private Long userId;

    private BigDecimal basePay;

    private BigDecimal extraPay;

    private LocalDate planPaymentDate;

    private LocalDate dueDate;

    private String note;

    private Long payTime;

    private boolean isDeleted;

    /**
     * batch状态
     * @see com.bees360.entity.firebase.BatchStatusEnum
     */
    private int status;
    /**
     * 是否等待飞手确认
     */
    private boolean isPendingAcceptance;

    /**
     * 该batch关联的project id集合
     */
    private List<Long> projectIds;
}
