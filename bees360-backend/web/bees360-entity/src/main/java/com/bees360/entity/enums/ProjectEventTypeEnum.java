package com.bees360.entity.enums;

import java.util.Objects;
import java.util.stream.Stream;

public enum ProjectEventTypeEnum {
	DELETE(-2, "delete", true),
	CANCEL(-1, "cancel", true),
	CREATED(1, "created", true),
    RECOVERED(Integer.MIN_VALUE, "recovered", true),
	REWORK(60, "rework", true),
    IMAGE_UPLOADED(80, "imageUploaded", true),
    RETURN_TO_CLIENT(90, "returnToClient", true),
    ESTIMATE_COMPLETE(95, "estimateComplete", true),
    CLIENT_RECEIVED(100, "clientReceived", true)
    ;

	private final int code;
	private final String type;
	private final boolean isForce;
	ProjectEventTypeEnum(int code, String type, boolean isForce){
		this.code = code;
		this.type = type;
		this.isForce = isForce;
	}
	public int getCode() {
		return code;
	}

	public String getType() {
		return type;
	}

    public boolean isForce() {
        return isForce;
    }


    public static ProjectEventTypeEnum getEnum(int code) {
        return Stream.of(ProjectEventTypeEnum.values()).filter(o -> Objects.equals(code, o.getCode())).findFirst().orElse(null);
    }
}
