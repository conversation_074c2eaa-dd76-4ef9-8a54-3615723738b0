package com.bees360.entity.enums;

public enum SegmentValueTypeEnum implements BaseCodeEnum {
	TREE_ROOT(0, "Tree root"),
	ADDRESS_IMAGES(1, "Address Images"),
	OVERVIEW_RISK(2, "Overview Risk"),
	SYS_DIRECTION_IMAGES(3, "System Direction Images"),
	NORMAL_DIRECTION_IMAGES(4, "Normal Direction Image"),
	SINGLE_ELECTION(5, "Single Election"),
	RADIO_ELECTION(6, "Radio Election"),
	CHECKBOX(7, "Checkbox"),
	INPUT(8, "Input"),
	MULTIPLE_RADIO_ELECTION(9, "Multiple radio election"),
	INPUT_NUMBER(10, "Input number");

	private final int code;
	private final String display;

	SegmentValueTypeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	public static SegmentValueTypeEnum getEnum(int code) {
		for(SegmentValueTypeEnum valueType : SegmentValueTypeEnum.values()) {
			if(valueType.getCode() == code) {
				return valueType;
			}
		}
		return null;
	}

	public static boolean isImage(int code) {
		return code == ADDRESS_IMAGES.getCode() || code == SYS_DIRECTION_IMAGES.getCode()
				|| code == NORMAL_DIRECTION_IMAGES.getCode() || code == OVERVIEW_RISK.getCode();
	}

	public static boolean isSysImage(int code) {
		return code == ADDRESS_IMAGES.getCode() || code == SYS_DIRECTION_IMAGES.getCode()
				|| code == OVERVIEW_RISK.getCode();
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

}
