package com.bees360.entity.enums;

public enum ShingleLayerEnum implements BaseCodeEnum{

	SINGLE(1000096, "Single layer"),
	DOUBLE(1000098, "Double layer"),
	MULTI(1000099, "Multi layer");

	private final int code;
	private final String display;
	ShingleLayerEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

}
