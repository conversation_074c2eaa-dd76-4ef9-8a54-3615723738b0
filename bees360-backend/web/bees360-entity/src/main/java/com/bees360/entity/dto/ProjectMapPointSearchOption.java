package com.bees360.entity.dto;

import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import lombok.Data;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Search Object for query
 */
@Data
public class ProjectMapPointSearchOption extends ProjectSearchOptionBase {
	// -- project info ---
    /**
     *  project's created time start point
      */
	private Long startTime;

    /**
     * project's created time end point
     */
	private Long endTime;

    /**
     *  project's latest status
     */
	private Integer latestStatus;

	// --- location ---
    /**
     * format is x,y (longitude, latitude)
     * ex: -104.75102290000001,39.9755834
     * it used to construct pCenter(gps location)
     * @see #setCenter(String)
     * @see #setCenterP(Point)
     */

	private String center;

    /**
     * radius
     * unused so far!
     */
	private Integer radius;

    /**
     * assigned by <code>center</code> and shouldn't be assigned by client
     */
	private Point centerP = null;

    /**
     * as a query for projectId
     */
	private Long searchProjectId;

	/**
	 * as a query for address
	 */
	private String searchAddress;


	/**
     * project status list
     * assigned by string
     * @see #setProjectStatuses(String)
     */
	private List<Integer> projectStatuses;

    /**
     * assigned by string
     * @see #setInspectionPurposeTypes(String)
     *
     *  inspection purpose type list, the field assigned is for claims
     * @see #setClaimTypes()
     * @see InspectionPurposeTypeEnum
     */
	private List<Integer> inspectionPurposeTypes;

    /**
     * start point of inspectionTime
     */
	private Long inspectionStartTime;

    /**
     * end point of inspectionTime
     */
	private Long inspectionEndTime;

    /**
     * projectId list for exact query
     */
    private List<Long> projectIds;

    /**
     * used to query but do not provide by client
     * assigned by <code>inspectionPurposeTypes</code>
     * @see #inspectionPurposeTypes
     */
	private Set<Integer> claimTypes;

    public void setClaimTypes() {
        if (inspectionPurposeTypes == null) {
            return;
        }
        // don't filter null
        Set<InspectionPurposeTypeEnum> serviceTypeEnums = inspectionPurposeTypes.stream().map(InspectionPurposeTypeEnum::getEnum).collect(Collectors.toSet());
        if (serviceTypeEnums.contains(null)) {
            throw new RuntimeException("inspectionPurposeTypes is not valid");
        }
        this.claimTypes = serviceTypeEnums.stream().map(InspectionPurposeTypeEnum::getSubTypes)
                .flatMap(subTypes -> subTypes.stream().map(ClaimTypeEnum::getCode))
                .collect(Collectors.toSet());

    }

    public void setProjectStatuses(String projectStatues) {
        this.projectStatuses = getIntList(projectStatues);
    }

	private void generateLocationP(String location) {
		Point p = generatePoint(location);
		if(isGps(p)) {
			centerP = p;
		}
	}
	private Point generatePoint(String pointStr) {
        List<Double> pointList = getDoubleList(pointStr);
        if (pointList == null) {
            return null;
        }
        if (pointList.size() != 2) {
            throw  new IllegalArgumentException("gps location parameter is not valid");
        }
		return new Point(pointList.get(0), pointList.get(1));
	}

	private boolean isGps(Point p) {
		return p != null && (-180 <= p.getX() && p.getX() <= 180 & -90 <= p.getY() && p.getY() <= 90);
	}

	public void setCenter(String center) {
		this.center = center;
		generateLocationP(center);
	}

    public void setInspectionPurposeTypes(String inspectionPurposeTypes) {
        this.inspectionPurposeTypes = super.getIntList(inspectionPurposeTypes);
        this.setClaimTypes();

    }

	public String getAddressRegex() {
    	return createFuzzyRegex(searchAddress);
	}

	@Override
	public void setSortKey(String sortKey) {
		this.sortKey = sortKey;
	}
}
