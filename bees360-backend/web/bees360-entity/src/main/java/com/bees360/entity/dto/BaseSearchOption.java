package com.bees360.entity.dto;

import com.bees360.entity.vo.Pagination;

/**
 *
 * <AUTHOR>
 * @contributors
 * @date Apr 12, 2018 8:03:28 PM
 */
public abstract class BaseSearchOption {
	protected int pageIndex;
	protected int pageSize;
	protected String sortKey;
	protected String sortOrder;

	public static int MAX_PAGESIZE = 300;

	public static enum EOrder{
		ASC,
		DESC;

		public static EOrder getEnum(String value) {
			if(value == null) {
				return null;
			}
			for(EOrder key: EOrder.values()) {
				if(key.name().equals(value)) {
					return key;
				}
			}
			return null;
		}
	}

	public BaseSearchOption() {
		this(0, 0);
	}

	public BaseSearchOption(int pageIndex, int pageSize) {
		this(pageIndex, pageSize, null, null);
	}

	public BaseSearchOption(int pageIndex, int pageSize, String sortKey, String sortOrder) {
		this.pageIndex = pageIndex;
		this.pageSize = pageSize;
		this.sortKey = sortKey;
		this.sortOrder = sortOrder;
	}

	/**
	 * index should start from 0
	 * @return
	 */
	public int getIndexStart() {
		int indexStart = (pageIndex - 1) * pageSize;
		return indexStart;
	}

	/**
	 * @return a pagination with page index and page size
	 */
	public Pagination pagination() {
		Pagination pagination = new Pagination();
		pagination.setPageIndex(pageIndex);
		pagination.setPageSize(pageSize);
		return pagination;
	}

	public Pagination pagination(int sum) {
		Pagination pagination = pagination();
		pagination.setSumAndGenaratePages(sum);
		return pagination;
	}

	/* getter ans setter */
	public int getPageIndex() {
		return pageIndex;
	}

	/**
	 * if the pageIndex is lower than 1, the value of pageIndex will be 1
	 * @param pageIndex
	 */
	public void setPageIndex(int pageIndex) {
		this.pageIndex = pageIndex < 1? 1: pageIndex;
	}
	public int getPageSize() {
		return pageSize;
	}
	/**
	 * if the pageSize is large than <code>MAX_PAGESIZE</code>, the pageSize will be <code>MAX_PAGESIZE</code>
	 * @param pageSize
	 */
	public void setPageSize(int pageSize) {
		this.pageSize = pageSize > MAX_PAGESIZE? MAX_PAGESIZE: pageSize;
	}
	public String getSortKey() {
		return sortKey;
	}

	/**
	 * 由实现类约束sortKey的内容
	 */
	public abstract void setSortKey(String sortKey);

	public String getSortOrder() {
		return sortOrder;
	}
	public void setSortOrder(EOrder order) {
		if(order == null) {
			this.sortOrder = null;
		} else {
			this.sortOrder = order.name();
		}
	}
	public void setSortOrder(String sortOrder) {
		if(sortOrder == null) {
			this.sortOrder = null;
		} else {
			setSortOrder(EOrder.getEnum(sortOrder.toUpperCase()));
		}
	}

	@Override
	public String toString() {
		return "BaseSearchOption [pageIndex=" + pageIndex + ", pageSize=" + pageSize + ", sortKey=" + sortKey
				+ ", sortOrder=" + sortOrder + "]";
	}
}
