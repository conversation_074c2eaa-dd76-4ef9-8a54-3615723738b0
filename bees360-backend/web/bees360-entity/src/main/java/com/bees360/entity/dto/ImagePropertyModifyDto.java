package com.bees360.entity.dto;

import lombok.Data;

import java.util.List;

@Data
public class ImagePropertyModifyDto {

    private List<? extends ImagePropertyModifyItem> items;

    @Data
    public static class ImagePropertyModifyItem {

        private List<String> imageIds;

        /**
         * @see com.bees360.entity.enums.ImageTypeEnum
         */
        private Integer imageType;

        /**
         * @see com.bees360.entity.enums.ImagePartialViewTypeEnum
         */
        private Integer partialType;

        /**
         * @see com.bees360.entity.enums.OrientationEnum
         */
        private Integer orientation;

        /**
         * @see com.bees360.entity.enums.FileSourceTypeEnum
         */
        private Integer fileSourceType;
    }
}
