package com.bees360.entity.openapi.reportsummary;

import java.util.List;

import com.bees360.web.core.json.gson.MapToStringListTypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SummaryBldg {
    /**
     * Building overall condition, possible values include [ "Excellent", "Good", "Average", "Fair", "Poor" ].
     */
    private String overallCondition;
    /**
     * Dwelling type, possible values include [ "Single-family", "Multi-family", "Condo" ].
     */
    private String dwellingType;
    /**
     * Construction material, possible values include [ "Concrete Block", "Frame", "Fire Resistive" ].
     */
    private String construction;
    /**
     * Building partially or completed constructed over water
     * Optional
     */
    private Boolean constructionOverWater;
    /**
     * The type of garage, such as ["Attached", "Detached", "Built-in", "Carport", "None"].
     */
    private String garage;
    /**
     * The type of HVAC, such as ["Central", "Window Unit", "Split Unit", "Baseboard heating", "Gas forced air
     * heating"].
     */
    private String hvac;
    /**
     * The number of stories of the main dwelling
     */
    private Integer numStories;
    /**
     * List of any wind protections discovered, such as ["Impact Resistant Door", "High Impact Glass"]. If none
     * observed, the field will be absent or has null value.
     */
    @JsonAdapter(MapToStringListTypeAdapter.class)
    private List<String> windProtections;
    /**
     * Whether hurricane straps was discovered.
     */
    private Boolean hurricaneStraps;
    /**
     * If building is Designated Historic Home
     * Optional
     */
    private Boolean designatedHistoricHome;
    /**
     * The type of foundation, such as ["Slab on Grade", "Concrete Slab", "Basement"]
     */
    private String foundation;
    /**
     * Whether the building is manufactured or a mobile home.
     */
    private String manufacturedOrMobileHome;
    /**
     * If the property has noticeable exisiting exterior damage
     * Optional
     */
    private Boolean exteriorDamage;
    /**
     * If foundation damage discovered
     */
    private Boolean hasFoundationCracks;
    /**
     * If fence damage discovered
     */
    private Boolean hasFence;
    /**
     * If the property is under construction
     */
    private Boolean underConstruction;
    /**
     * If the property is under renovation
     */
    private Boolean underRenovation;

    private Boolean isDeveloperSpeculation;

    /**
     * The architectural style of the building, such as ["BiLevel", "TriLevel", "Bungalow", "Condo",
     * "Single-Wide", "Multi-Wide", "Multi Wide Starter Home", "Single Wide Starter Home", "Cape
     * Cod", "Colonial", "Ranch", "Raised Ranch", "Townhouse - End Unit", "Townhouse - Interior
     * Unit", "Victorian", "Contemporary"]
     */
    private String architecturalStyle;

    /** The number of vehicles that can be parked in the garage. */
    private Integer garageCapacity;
}
