package com.bees360.entity.openapi.reportsummary;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SummaryInteriorAppliance {
    private String productType;

    private String brand;

    private String modelNumber;

    private String serialNumber;

    private Integer manufacturedYear;

    private Integer manufacturedMonth;

    private Double age;

    private Double remainingLife;

    private Double failureRiskScore;

    private Double failureRiskScoreIfRecallFixed;

    private List<Map<String, Object>> recallData;

    private List<Map<String, Object>> classActionLawsuit;
}
