package com.bees360.entity.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/30 16:57
 */
@Data
public class ProjectSearchLatLngOption extends ProjectSearchOptionBase {
    private Long creator;
    private Integer searchStatus;
    private Long searchProjectId;
    private String searchClaimNumber;
    private String searchAddress;
    private Long searchCompanyId;
    private Long startTime;
    private Long endTime;
    private Long assignStartTime;
    private Long assignEndTime;
    private Integer inspectionTypes;
    private Long memberUser;
    private Integer memberRole;
    private String flyZoneTypes;
    private Integer projectStatus;

    public Map<String, Object> toMap() {
        Map<String, Object> paramsMap = new HashMap<>();

        addIfNotBlankOrEmpty(paramsMap, "creator", creator);
        addIfNotBlankOrEmpty(paramsMap, "searchLatestStatus", searchStatus);
        addIfNotBlankOrEmpty(paramsMap, "searchProjectId", searchProjectId);
        addIfNotBlankOrEmpty(paramsMap, "searchClaimNumber", searchClaimNumber);
        addIfNotBlankOrEmpty(paramsMap, "searchAddress", searchAddress);
        addIfNotBlankOrEmpty(paramsMap, "searchCompanyId", searchCompanyId);
        addIfNotBlankOrEmpty(paramsMap, "startTime", startTime);
        addIfNotBlankOrEmpty(paramsMap, "endTime", endTime);
        addIfNotBlankOrEmpty(paramsMap, "assignStartTime", assignStartTime);
        addIfNotBlankOrEmpty(paramsMap, "assignEndTime", assignEndTime);
        addIfNotBlankOrEmpty(paramsMap, "inspectionTypes", inspectionTypes);
        addIfNotBlankOrEmpty(paramsMap, "memberUser", memberUser);
        addIfNotBlankOrEmpty(paramsMap, "memberRole", memberRole);
        addIfNotBlankOrEmpty(paramsMap, "flyZoneTypes", flyZoneTypes);
        addIfNotBlankOrEmpty(paramsMap, "projectStatus", projectStatus);

        setBaseSearchOption(paramsMap);
        return paramsMap;
    }

    @Override
    public void setSortKey(String sortKey) {
        this.sortKey = sortKey;
    }
}
