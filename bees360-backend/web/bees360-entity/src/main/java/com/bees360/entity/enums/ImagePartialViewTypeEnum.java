package com.bees360.entity.enums;

import java.util.List;

public enum ImagePartialViewTypeEnum implements BaseCodeEnum {
    ROOF(1, "Roof"),
    ELEVATION(2, "Elevation"),
    INTERIOR(3, "Interior"),
    GARAGE(4, "Garage"),
    OTHER_STRUCTURES(5, "Other Structures"),
    OTHERS(6, "Contents"),
    ADDRESS(7, "Address verification"),
    ELECTRICAL_PANEL(8, "Electrical Panel"),
    PLUMBING_SYSTEM(9, "Plumbing System"),
    WATER_HEATER(10, "Water Heater"),
    HAZARDS(11, "Hazards"),
    ROOM_OVERVIEW(12, "Room Overview"),
    FLOOR_OVERVIEW(13, "Floor Overview"),
    CEILING_OVERVIEW(14, "Ceiling Overview"),
    DAMAGE_AREA(15, "Damage Area"),
    OTHER_ITEMS(16, "Other Items"),
    <PERSON><PERSON><PERSON>(17, "Selfie"),

    PROPERTY_OVERVIEW(28, "Property Overview"),

    <PERSON>AR<PERSON>(29, "Alarm"),
    GENERATOR(30, "Generator"),
    WATER_SHUTOFF_VALUE(31, "Water Shutoff Valve"),
    DRY_HYDRANTS(32, "Dry Hydrants"),
    HOME_SPRINKLERS(33, "Home Sprinklers"),
    FIRE_EXTINGUISHERS(34, "Fire Extinguishers"),
    FIRE_PROOF_CABINETS(35, "Fire Proof Cabinets"),
    FLAMMABLE_RAG(36, "Flammable Rag"),
    NO_SMOKING_SIGNS(37, "No Smoking Signs"),
    ;

    private final int code;
    private final String display;

    ImagePartialViewTypeEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    public static ImagePartialViewTypeEnum getEnum(int code) {
        for (ImagePartialViewTypeEnum typeEnum : values()) {
            if (typeEnum.code == code) {
                return typeEnum;
            }
        }
        return null;
    }

    /** 室内图片类型 */
    public static List<ImagePartialViewTypeEnum> getAllInteriorType() {
        return List.of(
                INTERIOR,
                ELECTRICAL_PANEL,
                PLUMBING_SYSTEM,
                WATER_HEATER,
                HAZARDS,
                ROOM_OVERVIEW,
                FLOOR_OVERVIEW,
                CEILING_OVERVIEW,
                DAMAGE_AREA,
                OTHER_ITEMS);
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }
}
