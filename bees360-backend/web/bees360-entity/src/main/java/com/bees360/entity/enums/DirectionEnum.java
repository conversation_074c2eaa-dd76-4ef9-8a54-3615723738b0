package com.bees360.entity.enums;

public enum DirectionEnum implements BaseCodeEnum{
	EAST(1,"East"),
	NORTH(2, "North"),
	WEST(3, "West"),
	SOUTH(4, "South");

	public static final int DEFAULT = -1;

	private final int code;
	private final String display;

	DirectionEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	public static DirectionEnum getEnum(int code) {
		for(DirectionEnum direction: DirectionEnum.values()) {
			if(direction.getCode() == code) {
				return direction;
			}
		}
		return null;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

}
