package com.bees360.entity.enums;

public enum BsExportDataRelatedTypeEnum {

    /**
     * 批量关键的公司模板信息
     */
    COMPANY_TEMPLATE("COMPANY_TEMPLATE", "batch create project template"),

    /**
     * editor页面编辑的excel数据记录
     */
    PROJECT_EDITOR_V2("project_editor_v2", "project_editor_v2"),

    /**
     * 每周三给指定人员发送batch统计邮件的人员email记录
     */
    WEEK_NOTIFY_ADMIN_EMAIL("WEEK_NOTIFY_ADMIN_EMAIL", "WEEK_NOTIFY_ADMIN_EMAIL"),
    /**
     * 设置web端飞手邮件是否发送开关
     */
    BEES_PILOT_EMAIL_SWITCH("BEES_PILOT_EMAIL_SWITCH", "BEES_PILOT_EMAIL_SWITCH"),

    /**

     * Swyfft Underwriting的case, 根据policyNumber设置note的设置,
     */
    SWYFFT_PROJECT_BIND("SWYFFT_PROJECT_BIND", "Swyfft Underwriting"),

    WEEK_NOTIFY_ADMIN_ROLE_WORK_EMAIL("WEEK_NOTIFY_ADMIN_ROLE_WORK_EMAIL", "WEEK_NOTIFY_ADMIN_ROLE_WORK_EMAIL")

    ;
    BsExportDataRelatedTypeEnum(String type, String display) {
        this.type = type;
        this.display = display;
    }
    private final String type;
    private final String display;

    public String getType() {
        return type;
    }

    public String getDisplay() {
        return display;
    }
}
