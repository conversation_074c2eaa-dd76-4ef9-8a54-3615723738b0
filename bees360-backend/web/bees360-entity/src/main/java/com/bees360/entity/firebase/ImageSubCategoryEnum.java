package com.bees360.entity.firebase;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/10/18 下午
 *
 * TODO 应该用BeesPath分类来取代这个分类
 **/
public enum ImageSubCategoryEnum {
    WASHING_MACHINE_OVERVIEW(9004, "Washing Machine Overview", 9004),
    WASHING_MACHINE_SUPPLY_LINES(9005, "Washing Machine Supply Lines", 9005),
    WASHING_MACHINE_SERIAL_NUMBER(9006, "Washing Machine Serial Number", 9006),
    DRYER_SERIAL_NUMBER(9007, "Dryer Serial Number", 9007),
    WATER_LEAK_DETECTION(9014, "Water Leak Detection", 31),
    DISHWASHER_OVERVIEW(9030, "Dishwasher Overview", 9030),
    DISHWASHER_DOOR_OPEN(9031, "Dishwasher Door Open", 9031),
    DISHWASHER_SERIAL_NUMBER(9032, "Dishwasher Serial Number", 9032),
    REFRI<PERSON>RATOR_OVERVIEW(9040, "Refrigerator Overview", 9040),
    REFRI<PERSON>RATOR_SERIAL_NUMBER(9041, "Refrigerator Serial Number", 9041),
    RANGE_OVERVIEW(9050, "Range Overview", 9050),
    RANGE_SERIAL_NUMBER(9051, "Range Serial Number", 9051),
    REVERSE_OSMOSIS_SYSTEM_OVERVIEW(9060, "Reverse Osmosis System Overview", 9060),
    REVERSE_OSMOSIS_SYSTEM_SERIAL_NUMBER(9061, "Reverse Osmosis System Serial Number", 9061),
    SOLAR_PANEL_BATTERY_BACK_UP(9070, "Solar Panel Battery Back Up", 9070),
    WATER_SHUT_OFF_PANEL(9080, "Water Shut Off Panel", 9080),
    WATER_LEAK_DETECTION_CERTIFICATE(31002, "Water Leak Detection Certificate", 31002),
    FIRE_ALARM(40000, "Fire Alarm", 40),
    FIRE_ALARM_CONTROL_PANEL(40001, "Fire Alarm Control Panel", 40001),
    FIRE_ALARM_CERTIFICATE(40002, "Fire Alarm Certificate", 40002),
    BURGLAR_ALARM(41000, "Burglar Alarm", 41),
    BURGLAR_ALARM_CONTROL_PANEL(41001, "Burglar Alarm Control Panel", 41001),
    BURGLAR_ALARM_CERTIFICATE(41002, "Burglar Alarm Certificate", 41002),
    FIRE_SPRINKLERS(42000, "Fire Sprinklers", 42),
    FIRE_SPRINKLERS_FLOW_SWITCH(42001, "Fire Sprinklers Flow Switch", 42001),
    FIRE_SPRINKLERS_CERTIFICATE(42002, "Fire Sprinklers Certificate", 42002),
    LOW_TEMP_SYSTEM(43000, "Low-Temp System", 43),
    LOW_TEMP_SYSTEM_CERTIFICATE(43001, "Low-Temp System Certificate", 43001),
    GAS_LEAK_DETECTION(44000, "Gas Leak Detection", 44),
    GAS_LEAK_DETECTION_CERTIFICATE(44001, "Gas Leak Detection Certificate", 44001),
    LEED_CERTIFICATE(45000, "Leed Certificate", 45),
    CARETAKER(48000, "Caretaker", 48),
    DOORMAN_24_HOUR(51000, "Doorman 24 Hour", 51),
    ELEVATOR(52000, "Elevator", 52),
    ELEVATOR_CONTROL_PANEL(52001, "Elevator Control Panel", 52001),
    ELEVATOR_LOBBY_CAMERAS(52002, "Elevator Lobby Cameras", 52002),
    ELEVATOR_CERTIFICATE(52003, "Elevator Certificate", 52003),
    ;

    private final int subCategoryId;
    private final String display;
    private final int partialType;

    ImageSubCategoryEnum(int subCategoryId, String display, int partialType) {
        this.subCategoryId = subCategoryId;
        this.display = display;
        this.partialType = partialType;
    }

    public static ImageSubCategoryEnum getEnum(Integer subCategoryId) {
        return Arrays.stream(ImageSubCategoryEnum.values())
            .filter(o -> Objects.equals(subCategoryId, o.getSubCategoryId()))
            .findFirst()
            .orElse(null);
    }

    public int getSubCategoryId() {
        return subCategoryId;
    }

    public String getDisplay() {
        return display;
    }

    public int getPartialType() {
        return partialType;
    }
}
