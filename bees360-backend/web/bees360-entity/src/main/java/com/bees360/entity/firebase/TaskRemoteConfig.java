package com.bees360.entity.firebase;

import lombok.Data;

import java.util.List;

@Data
public class TaskRemoteConfig {
    /**
     * checkout reason 的标题
     */
    String reasonTitle;
    List<Integer> reasonIds;
    List<Integer> quizIds;
    String displayName;
    String type;
    String taskId;
    /**
     * check in 之后timeline里的存的code
     */
    int checkInCode;

    /**
     * check in 之后timeline里的存的code
     */
    int checkOutCode;

    /**
     * 压缩比例
     */
    double compressRatio;

    /**
     * 图片是否压缩
     */
    boolean compressed;

    /**
     * 图片来源：手机或者无人机
     */
    String source;

    /**
     * 任务的Key
     */
    private String taskKey;
}
