package com.bees360.entity.validate;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import com.bees360.entity.enums.ProjectTypeEnum;

/**
 * <AUTHOR>
 */
@Constraint(validatedBy = {ProjectTypeValidators.ProjectTypeStringValidator.class,
    ProjectTypeValidators.ProjectTypeIntegerValidator.class, ProjectTypeValidators.ProjectTypeEnumValidator.class})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.PARAMETER})
public @interface ProjectType {

    /**
     * 指定允许的类型，默认为全部
     */
    ProjectTypeEnum[] types() default {};

    String message() default "invalid type";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
