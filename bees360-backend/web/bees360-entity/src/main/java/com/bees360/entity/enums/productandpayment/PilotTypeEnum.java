package com.bees360.entity.enums.productandpayment;

import java.util.HashMap;
import java.util.Map;

import com.bees360.entity.enums.BaseCodeEnum;

public enum PilotTypeEnum implements BaseCodeEnum {
	NORMAL(1, "normal pilot")
	;
	private final int code;
	private final String display;

	private static final Map<Integer, PilotTypeEnum> ENUM_MAP;

	static {
		ENUM_MAP = new HashMap<Integer, PilotTypeEnum>();
		for(PilotTypeEnum type: values()) {
			ENUM_MAP.put(type.getCode(), type);
		}
	}

	PilotTypeEnum(int code, String display) {
		this.code = code;
		this.display = display;
	}
	@Override
	public int getCode() {
		return code;
	}
	@Override
	public String getDisplay() {
		return display;
	}

	public static PilotTypeEnum getEnum(int code) {
		return ENUM_MAP.get(code);
	}
}
