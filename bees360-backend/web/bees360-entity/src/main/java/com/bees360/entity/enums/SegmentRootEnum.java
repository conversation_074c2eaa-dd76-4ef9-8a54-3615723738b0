package com.bees360.entity.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum SegmentRootEnum implements BaseCodeEnum{

	WINDOW(110, "Window"),
	GUTTER(120, "Gutter"),
	DOWNSPOUT(130, "Downspout"),
	HVAC_UNIT(140, "HVAC Unit(s)"),
	DOOR(150, "Door"),
	ATTACHED_GARAGE_DOOR(160, "Attached Garage Door"),
	FENCE(170, "Fence"),
	OBJECTS_ON_EXTERIOR(180, "Objects on Exterior");

	private final int code;
	private final String display;
	SegmentRootEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

}
