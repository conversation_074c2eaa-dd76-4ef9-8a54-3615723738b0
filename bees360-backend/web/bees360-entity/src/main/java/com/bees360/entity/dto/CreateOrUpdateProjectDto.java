package com.bees360.entity.dto;

import com.bees360.commons.springsupport.validation.Year;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CreateOrUpdateProjectDto {

    /** 用户不需要提交，但作为可赋值字段 **/
    private Long projectId;

    private String policyNumber;

    private String inspectionNumber;

    private Integer claimType;

    private Integer flyZoneType;

    private Integer serviceType;

    private Long damageEventTime;

    private LocalDate policyEffectiveDate;

    private Long dueDate;

    // ===== address =====

    private String address;

    private String city;

    private String state;

    private String country;

    private String zipCode;

    // ===== gps =====

    private Double gpsLocationLongitude;

    private Double gpsLocationLatitude;

    private Boolean gpsIsApproximate;

    // ===== building =====

    @Year
    private String yearBuilt;

    private Integer projectType;

    // ===== company =====

    private Long insuranceCompany;

    private Long repairCompany;
}
