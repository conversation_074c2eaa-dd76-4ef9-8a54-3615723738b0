package com.bees360.entity.dto;

import com.bees360.entity.enums.DirectionEnum;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ProjectImageContent implements Cloneable{
	Logger logger = LoggerFactory.getLogger(ProjectImageContent.class);

	// Identifier of an image.
	private String imageId;
	// The file name in the system for this image. This name is probably used by Amazon cloud sevices.
	private String fileName;
	// The file name for the same image with the middle resolution.
	private String fileNameMiddleResolution;
	// The file name for the same image with the smaller resolution.
	private String fileNameLowerResolution;
	// The url of  lower resolution image with annotations on it.
	private String annotationImage;
	private long fileSize;
	// Who uploaded this image.
	private long userId;
	private long uploadTime;
	// The local filename when this file is uploaded from some local device.
	private String originalFileName;
	// The sources of this file: 0: drone image 1: cell-phone image …
	// it must be assigned a value through the Enum FileSourceTypeEnum.
	private int fileSourceType;
	// The GPS position of this image when it is generated.
	private double gpsLocationLongitude;
	private double gpsLocationLatitude;

	private double relativeAltitude;

	private int imageHeight;
	private int imageWidth;
	// The project that is linked win this project image.
	private long projectId;

    // The direction of this image in a house,
	// it must be assigned a value through the Enum DirectionEnum.
	private int direction;
	// The orientation of this image in a house.
	// it must be assigned a value through the Enum OrientationEnum(LEFT, RIGHT, BACK, FRONT).
	private Integer orientation;
	// it must be assigned a value through the Enum ImageTypeEnum.
	private int imageType;
	private String imageCategory;

	// It is a 3*4 matrix used for 3D construction, i.e.,
	// specifically the coordinate space transformation.
	private String camPropertyMatrix;
	// Whether this image is softly deleted.
	private boolean deleted;

	private boolean manuallyAnnotated;

	private String parentId;

	private byte[] content;

	private static final ProjectImageContent DEFAULT_IMAGE;

	static {
		DEFAULT_IMAGE = generateDefaultImage();
	}

	public ProjectImageContent() {
		super();
	}


	public ProjectImageContent(String imageId) {
		super();
		this.imageId = imageId;
	}

//	TODO @xing.wang
//	public TinyImageVo toTinyImageVo(){
//		return new TinyImageVo(this);
//	}

	public static ProjectImageContent defaultImage(){
		ProjectImageContent image = (ProjectImageContent)DEFAULT_IMAGE.clone();
		if(image == null){
			return generateDefaultImage();
		}
		return image;
	}

	private static ProjectImageContent generateDefaultImage() {
		ProjectImageContent image = new ProjectImageContent();
		image.setFileName("");
		image.setFileNameMiddleResolution(null);
		image.setFileNameLowerResolution(null);
		image.setDirection(DirectionEnum.DEFAULT);
		image.setFileSize(0);
		image.setFileSourceType(FileSourceTypeEnum.DEFAULT);
		image.setGpsLocationLatitude(0.0);
		image.setGpsLocationLongitude(0.0);
		image.setImageHeight(0);
		image.setImageWidth(0);
		image.setImageType(ImageTypeEnum.DEFAULT);
		image.setFileSourceType(FileSourceTypeEnum.DEFAULT);
		image.setDeleted(false);
		return image;
	}

	@Override
	public int hashCode(){
		return imageId.hashCode();
	}

	@Override
	public boolean equals(Object obj){
		if(this == obj){
			return true;
		}
		if(!(obj instanceof ProjectImageContent)){
			return false;
		}
		ProjectImageContent image = (ProjectImageContent)obj;
		return Objects.equals(imageId, image.getImageId());
	}

	@Override
	public Object clone(){
		ProjectImageContent image = null;
		try {
			image = (ProjectImageContent)super.clone();
		} catch (CloneNotSupportedException e) {
			logger.error("Fail to clone ProjectImage.", e);
		}

		return image;
	}

	// getter and setter
	public String getImageId() {
		return imageId;
	}

	public void setImageId(String imageId) {
		this.imageId = imageId;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFileNameLowerResolution() {
		return fileNameLowerResolution;
	}

	public void setFileNameLowerResolution(String fileNameLowerResolution) {
		this.fileNameLowerResolution = fileNameLowerResolution;
	}

	public String getFileNameMiddleResolution() {
		return fileNameMiddleResolution;
	}

	public void setFileNameMiddleResolution(String fileNameMiddleResolution) {
		this.fileNameMiddleResolution = fileNameMiddleResolution;
	}

	public String getAnnotationImage() {
		return annotationImage;
	}

	public void setAnnotationImage(String annotationImage) {
		this.annotationImage = annotationImage;
	}

	public long getFileSize() {
		return fileSize;
	}

	public void setFileSize(long fileSize) {
		this.fileSize = fileSize;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public long getUploadTime() {
		return uploadTime;
	}

	public void setUploadTime(long uploadTime) {
		this.uploadTime = uploadTime;
	}

	public String getOriginalFileName() {
		return originalFileName;
	}

	public void setOriginalFileName(String originalFileName) {
		this.originalFileName = originalFileName;
	}

	public int getFileSourceType() {
		return fileSourceType;
	}

	public void setFileSourceType(int fileSourceType) {
		this.fileSourceType = fileSourceType;
	}

	public double getGpsLocationLongitude() {
		return gpsLocationLongitude;
	}

	public void setGpsLocationLongitude(double gpsLocationLongitude) {
		this.gpsLocationLongitude = gpsLocationLongitude;
	}

	public double getGpsLocationLatitude() {
		return gpsLocationLatitude;
	}

	public void setGpsLocationLatitude(double gpsLocationLatitude) {
		this.gpsLocationLatitude = gpsLocationLatitude;
	}

	public double getRelativeAltitude() {
		return relativeAltitude;
	}

	public void setRelativeAltitude(double relativeAltitude) {
		this.relativeAltitude = relativeAltitude;
	}

	public int getImageHeight() {
		return imageHeight;
	}

	public void setImageHeight(int imageHeight) {
		this.imageHeight = imageHeight;
	}

	public int getImageWidth() {
		return imageWidth;
	}

	public void setImageWidth(int imageWidth) {
		this.imageWidth = imageWidth;
	}

	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public int getDirection() {
		return direction;
	}

	public void setDirection(int direction) {
		this.direction = direction;
	}

	public Integer getOrientation() {
		return orientation;
	}

	public void setOrientation(Integer orientation) {
		this.orientation = orientation;
	}

	public int getImageType() {
		return imageType;
	}

	public void setImageType(int imageType) {
		this.imageType = imageType;
	}

	public String getImageCategory() {
		return imageCategory;
	}

	public void setImageCategory(String imageCategory) {
		this.imageCategory = imageCategory;
	}

	@JsonIgnore
	public String getCamPropertyMatrix() {
		return camPropertyMatrix;
	}

	public void setCamPropertyMatrix(String camPropertyMatrix) {
		this.camPropertyMatrix = camPropertyMatrix;
	}

	public boolean getDeleted() {
		return deleted;
	}

	public void setDeleted(boolean deleted) {
		this.deleted = deleted;
	}

	public boolean isManuallyAnnotated() {
		return manuallyAnnotated;
	}

	public void setManuallyAnnotated(boolean manuallyAnnotated) {
		this.manuallyAnnotated = manuallyAnnotated;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public byte[] getContent() {
		return content;
	}

	public void setContent(byte[] content) {
		this.content = content;
	}

	@Override
	public String toString() {
		return "ProjectImage [logger=" + logger + ", imageId=" + imageId + ", fileName=" + fileName
				+ ", fileNameMiddleResolution=" + fileNameMiddleResolution + ", fileNameLowerResolution="
				+ fileNameLowerResolution + ", annotationImage=" + annotationImage + ", fileSize=" + fileSize
				+ ", userId=" + userId + ", uploadTime=" + uploadTime + ", originalFileName=" + originalFileName
				+ ", fileSourceType=" + fileSourceType + ", gpsLocationLongitude=" + gpsLocationLongitude
				+ ", gpsLocationLatitude=" + gpsLocationLatitude + ", relativeAltitude=" + relativeAltitude
				+ ", imageHeight=" + imageHeight + ", imageWidth=" + imageWidth + ", projectId=" + projectId
				+ ", direction=" + direction + ", imageType=" + imageType + ", imageCategory=" + imageCategory
				+ ", camPropertyMatrix=" + camPropertyMatrix + ", deleted=" + deleted + ", manuallyAnnotated="
				+ manuallyAnnotated + ", parentId=" + parentId + "]";
	}

}
