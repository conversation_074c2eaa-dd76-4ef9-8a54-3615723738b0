package com.bees360.entity;

import java.io.Serial;
import java.io.Serializable;

public class HouseImageSegmentType implements Serializable {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 9125579225881492075L;
	//identifier of this class
	private long id;
	//The list of “name” can be found the paragraph after this table.
	private String name;
	//Its parent id
	private long parentId;
	//the value type, single election, check etc. see SegmentValueTypeEnum
	private int valueType;

	private String unit;
	//the code type, structures,direction etc.
	// it must be assigned a value through the Enum SegmentCodeTypeEnum.
	private int codeType;
	//is leaf,0:false, 1:true
	private boolean isLeaf;

	public HouseImageSegmentType() {
		super();
	}

	public HouseImageSegmentType(long id, String name, long parentId) {
		super();
		this.id = id;
		this.name = name;
		this.parentId = parentId;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public long getParentId() {
		return parentId;
	}

	public void setParentId(long parentId) {
		this.parentId = parentId;
	}

	public int getValueType() {
		return valueType;
	}

	public void setValueType(int valueType) {
		this.valueType = valueType;
	}

	public int getCodeType() {
		return codeType;
	}

	public void setCodeType(int codeType) {
		this.codeType = codeType;
	}

	public boolean getIsLeaf() {
		return isLeaf;
	}

	public void setIsLeaf(boolean isLeaf) {
		this.isLeaf = isLeaf;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	@Override
	public String toString() {
		return "HouseImageSegmentType [id=" + id + ", name=" + name + ", parentId=" + parentId + ", valueType="
				+ valueType + ", codeType=" + codeType + ", isLeaf=" + isLeaf + "]";
	}

}
