package com.bees360.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ReportTaskVo implements Comparable<ReportTaskVo> {
	private int reportType;
	private String reportName;
	private int generationStatus;
	private boolean needReview;
	private boolean isFree;
	private boolean isPaid;
	private boolean cancelable;

	public int getReportType() {
		return reportType;
	}
	public void setReportType(int reportType) {
		this.reportType = reportType;
	}
	public String getReportName() {
		return reportName;
	}
	public void setReportName(String reportName) {
		this.reportName = reportName;
	}
	public int getGenerationStatus() {
		return generationStatus;
	}
	public void setGenerationStatus(int generationStatus) {
		this.generationStatus = generationStatus;
	}
	public boolean isNeedReview() {
		return needReview;
	}
	public void setNeedReview(boolean needReview) {
		this.needReview = needReview;
	}
	@JsonProperty("isFree")
	public boolean isFree() {
		return isFree;
	}
	public void setFree(boolean isFree) {
		this.isFree = isFree;
	}
	@JsonProperty("isPaid")
	public boolean isPaid() {
		return isPaid;
	}
	public void setPaid(boolean isPaid) {
		this.isPaid = isPaid;
	}
	@JsonProperty("isCancelable")
	public boolean isCancelable() {
		return cancelable;
	}
	public void setCancelable(boolean cancelable) {
		this.cancelable = cancelable;
	}

	@Override
	public String toString() {
		return "ReportTaskVo [reportType=" + reportType + ", reportName=" + reportName + ", generationStatus="
				+ generationStatus + ", needReview=" + needReview + ", isFree=" + isFree + ", isPaid=" + isPaid
				+ ", cancelable=" + cancelable + "]";
	}

	@Override
	public int compareTo(ReportTaskVo task) {
		if(task == null) {
			return -1;
		}
		if(reportType > task.getReportType()) {
			return 1;
		} else if(reportType < task.getReportType()) {
			return -1;
		} else {
			return 0;
		}
	}
}
