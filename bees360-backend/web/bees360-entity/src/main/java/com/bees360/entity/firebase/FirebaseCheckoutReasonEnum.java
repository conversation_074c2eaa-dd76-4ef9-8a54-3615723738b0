package com.bees360.entity.firebase;

import com.bees360.entity.enums.BaseCodeEnum;

import java.util.Arrays;
import java.util.NoSuchElementException;

/**
 * @deprecated 改成从RemoteConfig中读取checkout reason
 * @see TaskCheckoutReasonRemoteConfig
 * @see com.bees360.entity.enums.RemoteConfigParameter#TASK_CHECKOUT_REASON
 * <AUTHOR>
 * @date 2020/11/19 15:19
 */
@Deprecated
public enum FirebaseCheckoutReasonEnum implements BaseCodeEnum {

    /**
     * 等待飞手确认
     */
    NOT_WORKING(1, "BeesPilot™ is not working"),
    RESTRICTED_AIRSPACE(2, "Restricted airspace"),
    INSURED(3, "Rejected by the insured"),
    NOT_AT_HOME(4, "No one at home"),
    COVID_CONCERNS(5, "COVID-19 concerns"),
    NOT_CLOSED(6, "Not closed yet"),
    AGGRESSIVE_ANIMAL(7, "Aggressive animal"),
    SUSPICIOUS_ENVIRONMENT(8, "Suspicious environment."),
    ADDRESS_VERIFICATION(9, "Address verification was not found onsite"),
    OTHERS(10, "Others"),
    ALREADY_COMPLETED(11, "Already completed"),
    WEBSITE(12, "Will upload later from the website.")

    ;

    public static FirebaseCheckoutReasonEnum getEnum(Integer code){
        return Arrays.stream(FirebaseCheckoutReasonEnum.values())
            .filter(o -> code == o.getCode())
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("FirebaseCheckoutReasonEnum is null. code:" + code));

    }

    FirebaseCheckoutReasonEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    final private String display;

    final private int code;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }
}
