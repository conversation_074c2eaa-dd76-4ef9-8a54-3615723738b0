package com.bees360.entity.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;

import org.springframework.util.ObjectUtils;

public class SegmentImageDto {

	private String code;

	private String url;

	private String category;

	private String subcategory;

	private String description;

	private long descriptionId;

	private String additional;

	/**
	 * 0:system, 1,add
	 */
	private int placeHolderType;

	private boolean isDamage;

	/**
	 * Only for pictures.0:not must shot 1:must shot
	 */
    private boolean isRequired;

	/**
	 * the category type, see table HouseCategory.type or SegmentCategoryTypeEnum
	 * 10:roof, 15: Ridge cap, 20: Drip edge, 25:Valley, 30 Exhaust cap, 35: Pipe jack, 45: Attic vents,
	 * 50: Ridge Vents, 55: Flashing, 60: Roof Vents, 65: Ventilator, 70: Roof slope, 100: Elevation,
	 * 105: Window screen, 110: Window reglaze, 115: Window beading, 120: Gutters/Downspouts, 125: A/C fins,
	 * 130: Fascia, 135: Windows, 140: Siding, 145: Soffit, 200: close up or overview(Unmodifiable)
	 */
	private int type;

	public SegmentImageDto() {
	}

	public SegmentImageDto(String code, String additional, String description, long descriptionId, int type) {
		this.code = code;
		this.additional = additional;
		this.description = description;
		this.descriptionId = descriptionId;
		this.type = type;

		this.placeHolderType = 1;
		this.isDamage = false;
		this.isRequired = false;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public int getPlaceHolderType() {
		return placeHolderType;
	}

	public void setPlaceHolderType(int placeHolderType) {
		this.placeHolderType = placeHolderType;
	}

	public String getAdditional() {
		return additional;
	}

	public void setAdditional(String additional) {
		this.additional = additional;
	}

	public boolean getIsDamage() {
		return isDamage;
	}

	public void setIsDamage(boolean isDamage) {
		this.isDamage = isDamage;
	}

	public boolean getIsRequired() {
		return isRequired;
	}

	public void setIsRequired(boolean isRequired) {
		this.isRequired = isRequired;
	}

	public long getDescriptionId() {
		return descriptionId;
	}

	public void setDescriptionId(long descriptionId) {
		this.descriptionId = descriptionId;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getSubcategory() {
		return subcategory;
	}

	public void setSubcategory(String subcategory) {
		this.subcategory = subcategory;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	@JsonIgnore
	public long getLastCode() {
		if (ObjectUtils.isEmpty(this.code)) {
			return 0;
		}
		String[] codeStrs = this.code.split(AppSegmentDto.SEGMENT_CODE_SPLIT);
		if (codeStrs.length < 2) {
			return 0;
		}
		return Long.parseLong(codeStrs[codeStrs.length - 1]);
	}

	@Override
	public String toString() {
		return "SegmentImageDto [code=" + code + ", url=" + url + ", category=" + category + ", subcategory="
				+ subcategory + ", description=" + description + ", descriptionId=" + descriptionId + ", additional="
				+ additional + ", placeHolderType=" + placeHolderType + ", isDamage=" + isDamage + ", isRequired="
				+ isRequired + ", type=" + type + "]";
	}

}
