package com.bees360.entity.vo;

import com.bees360.entity.enums.productandpayment.PriceStatusEnum;
import com.bees360.entity.enums.productandpayment.PriceTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;

public class ProductReportVo {
	private int reportType;
	private String reportName;
	/**
	 * 价格的类别，不同的价格类别对商品的支付有不同的影响
	 * @see PriceTypeEnum
	 */
	private int priceType;
	// 商品的价格只有在价格类型为价格确定类型时才会有确定的值，否则为null
	private Double price;
	private String url;
	private String caption;

	public int getReportType() {
		return reportType;
	}
	public void setReportType(int reportType) {
		this.reportType = reportType;
	}
	public String getReportName() {
		return reportName;
	}
	public void setReportName(String reportName) {
		this.reportName = reportName;
	}
	public int getPriceType() {
		return priceType;
	}
	public void setPriceType(int priceType) {
		this.priceType = priceType;
	}
	public Double getPrice() {
		return price;
	}
	public void setPrice(Double price) {
		this.price = price;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public String getCaption() {
		return caption;
	}
	public void setCaption(String caption) {
		this.caption = caption;
	}

	@JsonProperty("isFree")
	public boolean isFree() {
		PriceTypeEnum priceTypeEnum = PriceTypeEnum.getEnum(priceType);
		if(priceTypeEnum == null) {
			return false;
		}
		return priceTypeEnum.getDefaultPriceStatus() == PriceStatusEnum.CERTAIN
				&& price == 0;
	}
}
