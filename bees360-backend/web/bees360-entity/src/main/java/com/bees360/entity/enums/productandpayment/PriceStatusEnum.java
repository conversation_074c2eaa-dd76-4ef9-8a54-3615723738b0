package com.bees360.entity.enums.productandpayment;

import com.bees360.entity.enums.BaseCodeEnum;

public enum PriceStatusEnum implements BaseCodeEnum {
	UNCERTAIN(1, "uncertain"),
	CERTAIN(2, "certain")
	;

	private final int code;
	private final String display;

	PriceStatusEnum(int code, String display) {
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}
}
