package com.bees360.entity.openapi.reportsummary;

import java.util.List;
import java.util.Map;

import com.bees360.web.core.json.gson.MapToStringListTypeAdapter;
import com.bees360.web.core.json.gson.RemoveNullListAdapter;
import com.google.gson.annotations.JsonAdapter;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SummaryRoof {
    /**
     * Overall roof condition, possible values include [ "Excellent", "Good", "Average", "Fair", "Poor" ].
     */
    private String overallCondition;
    /**
     * Estimated roof age, possible values include [less than 1 year, 1 ~ 5 years, 5 - 10 years].
     */
    private String estAge;
    /**
     * Estimated roof remaining life, possible values include [less than 1 year, 1 ~ 5 years, 5 - 10 years].
     */
    private String estLife;
    /**
     * An object presenting roof geometry and its corresponding percentage. Possible properties include [ "Flat",
     * "Gable", "Hip" ]. The value of each property presents its corresponding percentage.
     */
    private Map<String, Integer> geometry;
    /**
     * List of dominant covering material, including [ "Asphalt", "Modified Bitumen", "Metal", "Wood", "Tile",
     * "Aluminum" ].
     */
    @JsonAdapter(MapToStringListTypeAdapter.class)
    private List<String> coveringMaterial;
    /**
     * Whether any solar panels present on roof.
     */
    private Boolean hasSolarPanel;
    /**
     * If roof has curling shingles
     * Optional
     */
    private Boolean hasCurlingShingles;
    /**
     * If roof has granular loss
     * Optional
     */
    private Boolean hasGranularLoss;
    /**
     * If roof has missing or damaged shingles
     * Optional
     */
    private Boolean hasMissingDamagedShingles;
    /**
     * If roof has been repaired with patches
     * Optional
     */
    private Boolean hasPatchedAreas;
    /**
     * If roof has a tarp
     * Optional
     */
    private Boolean hasTarp;
    /**
     * A Object that records the roof meterial and its corresponding percentage. Possible roof meterial properties
     * include [ "CompositeShingles", "BuildupRoofNoGravel", "ClayConcreteTiles", "LightMetalPanels",
     * "SinglePlyMembrane", "SinglePlyMembraneBallasted", "Slate", "StandingSeamMetalRoof", "WoodenShingles" ].
     */
    private Map<String, Integer> material;
    /**
     * List of comments to the roof.
     */
    @JsonAdapter(MapToStringListTypeAdapter.class)
    private List<String> comments;

    @JsonAdapter(RemoveNullListAdapter.class)
    private List<SummaryComparison> comparison;
    /**
     * If debris discovered on roof
     */
    private Boolean hasRoofDebris;

    /** The number of Chimneys. */
    private Integer chimneyCount;
}
