package com.bees360.entity.enums;

public enum RealtimeElementBaseEnum implements BaseCodeEnum {
	OVERVIEW_ROOFTOP(0, "Overview of rooftop of the risk."),
	CELLPHONE(1, "Cellphone."),
	CLOSE_UP(2, "Close-up image."),
	CROP(3, "Associated hail damage details."),
	SLOPE(4, "Overview of the slope.");

	private final int code;
	private final String display;

	RealtimeElementBaseEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public static RealtimeElementBaseEnum getEnum(int code){
		RealtimeElementBaseEnum[] onsiteElementTypes = RealtimeElementBaseEnum.values();
		for(RealtimeElementBaseEnum onsiteElementType: onsiteElementTypes){
			if(onsiteElementType.code == code){
				return onsiteElementType;
			}
		}
		return null;
	}
}
