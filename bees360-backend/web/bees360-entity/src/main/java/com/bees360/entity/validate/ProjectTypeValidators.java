package com.bees360.entity.validate;

import com.bees360.entity.enums.ProjectTypeEnum;

/**
 * <AUTHOR>
 */
public interface ProjectTypeValidators {

    public static class ProjectTypeStringValidator extends ProjectTypeValidator<String> {

        @Override
        protected ProjectTypeEnum toType(String value) {
            return ProjectTypeEnum.getEnumByValue(value);
        }
    }

    public static class ProjectTypeIntegerValidator extends ProjectTypeValidator<Integer> {

        @Override
        protected ProjectTypeEnum toType(Integer value) {
            return ProjectTypeEnum.getEnum(value);
        }
    }

    public static class ProjectTypeEnumValidator extends ProjectTypeValidator<ProjectTypeEnum> {

        @Override
        protected ProjectTypeEnum toType(ProjectTypeEnum value) {
            return value;
        }
    }
}
