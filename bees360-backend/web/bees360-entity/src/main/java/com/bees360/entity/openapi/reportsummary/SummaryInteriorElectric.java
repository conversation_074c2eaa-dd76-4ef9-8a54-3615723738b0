package com.bees360.entity.openapi.reportsummary;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SummaryInteriorElectric {
    /**
     * Ineligible panel, Split bus, fuse boxes, Stablok, Federal Pacific, GTE-Sylvania, Challenger or Zinsco
     * Optional
     */
    private Boolean hasIneligiblePanel;

    /**
     * Brand of the main electrical panel inspected. This indicates the manufacturer of the panel.
     * Common brands include Square D, General Electric, Siemens, etc.
     * example: Square D
     */
    private String panelBrand = "";

    private Boolean isUpdated;

    private Integer yearUpdated;

    private String systemUpdateStatus;
}
