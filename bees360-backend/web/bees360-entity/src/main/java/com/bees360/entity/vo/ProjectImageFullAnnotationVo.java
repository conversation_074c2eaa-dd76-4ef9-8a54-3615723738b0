package com.bees360.entity.vo;

import com.bees360.entity.dto.ImageAnnotationCellDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/09/11 15:01
 */
public class ProjectImageFullAnnotationVo {
    private String imageId;
    // TODO 设法使用一个统一的结构，而无需匹配多个不同的结构，这里使用多个结构的原因是不同接口对所需的字段不同
    private List<? extends ImageAnnotationCellDto> damages;
    private List<ProjectImageFacetPath> facets;
    private List<TestRectangle> frames;

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public List<? extends ImageAnnotationCellDto> getDamages() {
        return damages;
    }

    public void setDamages(List<? extends ImageAnnotationCellDto> damages) {
        this.damages = damages;
    }

    public List<ProjectImageFacetPath> getFacets() {
        return facets;
    }

    public void setFacets(List<ProjectImageFacetPath> facets) {
        this.facets = facets;
    }

    public List<TestRectangle> getFrames() {
        return frames;
    }

    public void setFrames(List<TestRectangle> frames) {
        this.frames = frames;
    }

    @Override
    public String toString() {
        return "ProjectImageFullAnnotationVo{" + "imageId=" + imageId + ", damages=" + damages + ", facets=" + facets
            + ", frames=" + frames + '}';
    }
}
