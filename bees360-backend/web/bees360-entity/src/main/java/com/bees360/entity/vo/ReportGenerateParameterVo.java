package com.bees360.entity.vo;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
public class ReportGenerateParameterVo {

    @Min(1)
    @Max(19)
    private int type;

    private boolean isAngleOptimization;

    public boolean getIsAngleOptimization() {
        return isAngleOptimization;
    }

    public void setIsAngleOptimization(boolean angleOptimization) {
        isAngleOptimization = angleOptimization;
    }
}
