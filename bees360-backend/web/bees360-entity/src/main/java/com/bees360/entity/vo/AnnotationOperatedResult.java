package com.bees360.entity.vo;

import com.bees360.entity.dto.ImageAnnotationCellDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/09/11 18:22
 */
public class AnnotationOperatedResult {
    // <EMAIL> 设法使用统一的数据结构
    private List<? extends ImageAnnotationCellDto> damages;
    private List<TestRectangle> frames;
    private List<ImageUrls> affectedImages;

    public List<? extends ImageAnnotationCellDto> getDamages() {
        return damages;
    }

    public void setDamages(List<? extends ImageAnnotationCellDto> damages) {
        this.damages = damages;
    }

    public List<TestRectangle> getFrames() {
        return frames;
    }

    public void setFrames(List<TestRectangle> frames) {
        this.frames = frames;
    }

    public List<ImageUrls> getAffectedImages() {
        return affectedImages;
    }

    public void setAffectedImages(List<ImageUrls> affectedImages) {
        this.affectedImages = affectedImages;
    }

    @Override
    public String toString() {
        return "AnnotationOperatedResult{" + "damages=" + damages + ", frames=" + frames + ", affectedImages="
            + affectedImages + '}';
    }
}
