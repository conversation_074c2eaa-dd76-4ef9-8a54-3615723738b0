package com.bees360.entity.validate;

import java.util.Arrays;
import java.util.List;

import jakarta.validation.ConstraintValidator;

import org.springframework.util.ObjectUtils;
import jakarta.validation.ConstraintValidatorContext;

import com.bees360.entity.enums.NewProjectStatusEnum;

public class ProjectStatusValidator implements ConstraintValidator<ProjectStatus, String> {

    private List<NewProjectStatusEnum> statuses;

    @Override
    public void initialize(ProjectStatus constraintAnnotation) {
        statuses = Arrays.asList(constraintAnnotation.statuses());
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (ObjectUtils.isEmpty(value)) {
            return true;
        }
        final NewProjectStatusEnum status = NewProjectStatusEnum.getEnumByValue(value);
        if (status == null) {
            return false;
        }
        if (statuses.size() == 0) {
            return true;
        }
        return statuses.contains(status);
    }
}
