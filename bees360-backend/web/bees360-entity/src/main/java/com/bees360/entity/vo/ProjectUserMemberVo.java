package com.bees360.entity.vo;

import com.bees360.entity.User;
import com.bees360.entity.enums.RoleEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

public class ProjectUserMemberVo {
    private long userId;
    private String firstName;
    private String lastName;
    private String phone;
    private String email;
    private Integer roleId;
    private String avatar;
    private long projectId;

    public ProjectUserMemberVo() {}

    public ProjectUserMemberVo(User user, RoleEnum role, long projectId) {
        this.userId = user.getUserId();
        this.avatar = user.getAvatar();
        this.email = user.getEmail();
        this.phone = user.getPhone();
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.roleId = role == null ? null : role.getCode();
        this.projectId = projectId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getName() {
        return (firstName + " " + lastName).trim();
    }

    @JsonIgnore
    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    @JsonIgnore
    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public String getRole() {
        if (roleId == null) {
            return "";
        }
        RoleEnum role = RoleEnum.getEnum(roleId);
        return role == null ? "" : role.getDisplay();
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public void setProjectId(long projectId) {
        this.projectId = projectId;
    }

    public long getProjectId() {
        return projectId;
    }

    @Override
    public String toString() {
        return "UserTinyVo [userId="
                + userId
                + ", firstName="
                + firstName
                + ", lastName="
                + lastName
                + ", phone="
                + phone
                + ", email="
                + email
                + ", roleId="
                + roleId
                + ", avatar="
                + avatar
                + ", projectId="
                + projectId
                + "]";
    }
}
