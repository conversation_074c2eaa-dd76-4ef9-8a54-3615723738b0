package com.bees360.entity.dto;

public class ScreenshotAnnotation {
	private String id;
	private Rectangle rect;
    /**
     * 截图类型
     *  @see com.bees360.entity.enums.AnnotationTypeEnum
     */
	private int type;

	public ScreenshotAnnotation(){}

	public ScreenshotAnnotation(String id, Rectangle rect) {
		this.id = id;
		this.rect = rect;
	}

	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public Rectangle getRect() {
		return rect;
	}
	public void setRect(Rectangle rect) {
		this.rect = rect;
	}

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
	public String toString() {
		return "ScreenshotAnnotation [id=" + id + ", rect=" + rect + "]";
	}
}
