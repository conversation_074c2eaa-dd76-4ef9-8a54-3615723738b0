package com.bees360.entity.validate;

import com.bees360.entity.enums.NewProjectStatusEnum;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.Arrays;
import java.util.List;

@Constraint(validatedBy = {ProjectStatusValidator.class})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.PARAMETER})
public @interface ProjectStatus {

    /**
     * 指定允许的statuses，默认为全部
     */
    NewProjectStatusEnum[] statuses() default {};

    String message() default "invalid project status value";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };

}
