package com.bees360.entity.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum FileSourceTypeEnum implements BaseCodeEnum{

	/**
	 * 无人机拍摄的图片
	 */
	DRONE_IMAGE(0, "Drone", "Full Damage Image", true, true, true),
	/**
	 * 手机拍摄的图片
	 */
	CELL_PHONE_IMAGE(1, "Mobile", "Mobile Image", true, false, false),
	/**
	 * 截图
	 */
	SCREENSHOT(2, "Screenshot", "Screenshot", false, false, false),
	/**
	 * PLACEHOLDER 为图片占位符，只有图片基本信息，但没有图片的二级制数据。该类型图片不是由用户拍摄的，所以 {@code tokenByUser}为false。
	 */
	PLACEHOLDER(3, "Placeholder", "Placeholder", false, false, false),
	/**
	 * TODO DRONE_PREVISIT_IMAGE 的value值暂不确定
	 */
	DRONE_PREVISIT_IMAGE(4, "Pre-visit Image", "Pre-visit Image", true,false, false),
	/**
	 * TODO DRONE_REALTIME_IMAGE 的value值暂不确定
	 */
	DRONE_REALTIME_IMAGE(5, "Realtime Image", "Realtime Image", true, false, true),
	/**
	 * 报告中生成/使用到的图片
	 */
    REPORT_IMAGE(6, "Annotated", "Report Image", false, false, false)
	;

	public static final int DEFAULT = -1;

    // 将来去掉file source type的时候，drone image和mobile image会移到project模块的project image中
    public static final Set<FileSourceTypeEnum> PROJECT_TYPES =
            Set.of(DRONE_IMAGE, CELL_PHONE_IMAGE);

	FileSourceTypeEnum(int code, String value, String display, boolean tokenByUser, boolean can3D, boolean canAD){
		this.code = code;
		this.value = value;
		this.display = display;
		this.tokenByUser = tokenByUser;
		this.can3D = can3D;
		this.canAD = canAD;
	}

	/**
	 * 唯一编码，不可随意变更
	 */
	private final int code;
	/**
	 * 唯一字符串值，不可随意变更
	 */
	private final String value;
	/**
	 * 用于显示，不确保唯一，且可能发生变化
	 */
	private final String display;
	/**
	 * 该类型图片由用户拍摄。
	 */
	private final boolean tokenByUser;
	private final boolean can3D;
	private final boolean canAD;

	@Override
	@JsonValue
	public int getCode() {
		return code;
	}

	public String getValue() {
		return value;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public boolean isTokenByUser() {
		return tokenByUser;
	}

	public boolean can3D() {
		return can3D;
	}

	public boolean canAD() {
		return canAD;
	}

	public boolean hasImageFile(){
		return code != FileSourceTypeEnum.PLACEHOLDER.getCode();
	}

    public static FileSourceTypeEnum getEnum(Integer type) {
		return valueOf(type);
	}

	public static FileSourceTypeEnum valueOf(Integer type){
		if(type == null) {
			return null;
		}
		FileSourceTypeEnum[] types = FileSourceTypeEnum.values();
		for(FileSourceTypeEnum typeEnum: types){
			if(typeEnum.getCode() == type){
				return typeEnum;
			}
		}
		return null;
	}

	public static List<FileSourceTypeEnum> listTokenByUser() {
		return Arrays.stream(values()).filter(t -> t.isTokenByUser()).collect(Collectors.toList());
	}

	public static boolean exist(int code) {
		return valueOf(code) != null;
	}

    /**
     * 判断图片是否是report所需要的种类
     * 报告中需要的图片种类为DRONE_IMAGE,CELL_PHONE_IMAGE
     * @return
     */
	public boolean isRequiredForReport() {
	    return this == DRONE_IMAGE || this == CELL_PHONE_IMAGE;
    }

	public static void main(String[] args) {
		System.out.println("|name|code|");
		System.out.println("|----|----|");
		for(FileSourceTypeEnum type: FileSourceTypeEnum.values()) {
			System.out.println("|" + type.getDisplay() + "|" + type.getCode() + "|");
		}
	}
}
