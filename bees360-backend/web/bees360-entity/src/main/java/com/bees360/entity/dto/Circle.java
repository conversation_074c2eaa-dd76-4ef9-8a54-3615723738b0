package com.bees360.entity.dto;

public class Circle {

	private double x;

	private double y;

	private double r;

	public Circle() {
		super();
	}

	public Circle(double x, double y, double r) {
		super();
		this.x = x;
		this.y = y;
		this.r = r;
	}

	public double getX() {
		return x;
	}

	public void setX(double x) {
		this.x = x;
	}

	public double getY() {
		return y;
	}

	public void setY(double y) {
		this.y = y;
	}

	public double getR() {
		return r;
	}

	public void setR(double r) {
		this.r = r;
	}

	public Point getCenterPoint() {
		return new Point(x, y);
	}

}
