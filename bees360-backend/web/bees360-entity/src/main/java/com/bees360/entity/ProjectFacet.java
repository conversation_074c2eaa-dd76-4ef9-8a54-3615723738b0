package com.bees360.entity;

import java.io.Serializable;

/**
 * ProjectFacet entity
 *
 * <AUTHOR>
 *
 */
@SuppressWarnings("serial")
public class ProjectFacet implements Serializable {

	public static final int INEXISTENT_FACET_ID = -1;

	// The identifier of the facet.
	private int facetId;
	private long projectId;
	// Some project can consists of multiple isolated/disconnected components,
	// while each component consists of multiple facets.

	private int componentId;
	private String name;
	// The user that generates/modifies this facet.
	private long createdBy;
	// The timestamp that this annotation is created.
	private long createdTime;
	// Created by AI, 1: Front, 2:Back, 3: Left, 4: Right
	private int orientation;
	// 0. damage_percent <= 25%, 1. 25% <= 75%, 2. 75% < = damage_percent
	// default as 0
	private Integer damagePercent;
	// The real area of this facet
	private double area;
	// “sqft” or “sqmt” (square feet or square meter), etc.
	private String areaUnit;
	// “sqft” or “sqmt” (square feet or square meter), etc.
	private double pitch;
	// Depending on the data, we can use string to store this 3d path, e.g.,
	// [[-19.23351328,19.16983818,18.32308133],
	// [-19.19685688,11.08251505,18.54117142],
	// [-27.85861902,27.70066715,12.93890151],
	// [-19.23351328,19.16983818,18.32308133].
	private String thdPath;
	// “ft” or “meter”, etc.
	private String pathUnit;
	// It is an array of type of the edge. Each element can be 1 : Ridge 2 : Hip
	// 3 : Valley 4 : Rake 5 : Eave 6 : Flashing. An example is [1, 2, 3, 4].
	private String pathType;
	// The neighbor facets sharing such edges. Note that when this edge is not
	// shared,
	// then the associated value is -1. An example can be [1, 5, 8, -1].
	private String sharedFacet;
	// The three plane vectors, e.g.,
	// [[0.099425,0.8289,-0.5566],[-0.97,0.2,-0.00354],[0.05,0.471,0.8356]].
	private String planeProp;
	// Storing (a, b, c, d) in ax+by+cz+d = 0.
	private String planeCoef;
	// Whether it is the high roof.
	private boolean isHighRoof;

	public ProjectFacet() {
		super();
	}

	public ProjectFacet(int facetId, long projectId) {
		super();
		this.facetId = facetId;
		this.projectId = projectId;
	}

	/* getter and setter */
	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public int getFacetId() {
		return facetId;
	}

	public void setFacetId(int facetId) {
		this.facetId = facetId;
	}

	public int getComponentId() {
		return componentId;
	}

	public void setComponentId(int componentId) {
		this.componentId = componentId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public long getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(long createdBy) {
		this.createdBy = createdBy;
	}

	public long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}

	public double getArea() {
		return area;
	}

	public void setArea(double area) {
		this.area = area;
	}

	public String getAreaUnit() {
		return areaUnit;
	}

	public void setAreaUnit(String areaUnit) {
		this.areaUnit = areaUnit;
	}

	public double getPitch() {
		return pitch;
	}

	public void setPitch(double pitch) {
		this.pitch = pitch;
	}

	public void setPitch(Float pitch) {
		this.pitch = pitch;
	}

	public String getThdPath() {
		return thdPath;
	}

	public void setThdPath(String thdPath) {
		this.thdPath = thdPath;
	}

	public String getPathUnit() {
		return pathUnit;
	}

	public void setPathUnit(String pathUnit) {
		this.pathUnit = pathUnit;
	}

	public String getPathType() {
		return pathType;
	}

	public void setPathType(String pathType) {
		this.pathType = pathType;
	}

	public String getSharedFacet() {
		return sharedFacet;
	}

	public void setSharedFacet(String sharedFacet) {
		this.sharedFacet = sharedFacet;
	}

	public String getPlaneProp() {
		return planeProp;
	}

	public void setPlaneProp(String planeProp) {
		this.planeProp = planeProp;
	}

	public String getPlaneCoef() {
		return planeCoef;
	}

	public void setPlaneCoef(String planeCoef) {
		this.planeCoef = planeCoef;
	}

	public boolean getIsHighRoof() {
		return isHighRoof;
	}

	public void setIsHighRoof(boolean isHighRoof) {
		this.isHighRoof = isHighRoof;
	}

	public Integer getDamagePercent() {
		return damagePercent;
	}

	public void setDamagePercent(Integer damagePercent) {
		this.damagePercent = damagePercent;
	}

	public int getOrientation() {
		return orientation;
	}

	public void setOrientation(int orientation) {
		this.orientation = orientation;
	}

	@Override
	public String toString() {
		return "ProjectFacet [facetId=" + facetId + ", projectId=" + projectId + ", componentId=" + componentId
				+ ", damagePercent=" + damagePercent + ", name=" + name + ", createdBy=" + createdBy + ", createdTime="
				+ createdTime + ", area=" + area + ", areaUnit=" + areaUnit + ", pitch=" + pitch + ", thdPath="
				+ thdPath + ", pathUnit=" + pathUnit + ", pathType=" + pathType + ", sharedFacet=" + sharedFacet
				+ ", planeProp=" + planeProp + ", planeCoef=" + planeCoef + ", isHighRoof=" + isHighRoof
				+ ", orientation=" + orientation + "]";
	}

}
