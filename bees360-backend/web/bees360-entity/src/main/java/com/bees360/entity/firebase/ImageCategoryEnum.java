package com.bees360.entity.firebase;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2020/8/26 7:39 下午
 *
 * TODO 应该用BeesPath分类来取代这个分类
 **/
public enum ImageCategoryEnum {
    ADDRESS_VERIFICATION(1, "Address Verification", 1, 7, 5, null),
    FRONT_ELEVATION(2, "Front Elevation", 1, 2, null, 1),
    BACK_ELEVATION(3, "Back Elevation", 1, 2, null, 2),
    LEFT_ELEVATION(4, "Left Elevation", 1, 2, null, 3),
    RIGHT_ELEVATION(5, "Right Elevation", 1, 2, null, 4),
    INTERIOR(6, "Interior", 1, 3, null, null),
    GARAGE(7, "Garage", 1, 4, null, null),
    APS(8, "Other Structures", 1, 5, null, null),
    CONTENTS(9, "Contents", 1, 6, null, null),

    OVERHEAD(10, "Roof Overhead", 0, 1, 2, null),
    BIRDVIEW(11, "Birdview", 0, 1, 3, null),
    CLOSEUP(12, "Closeup", 0, 1, 4, null),
    OTHERS(13, "Others", 0, 1, 0, null),
    ELECTRICAL_PANEL(14, "Electrical Panel", 1, 8, null, null),
    PLUMBING_SYSTEM(15, "Plumbing System", 1, 9, null, null),
    WATER_HEATER(16, "Water Heater", 1, 10, null, null),
    HAZARDS(17, "Hazards", 1, 11, null, null),
    COMMUNITY_GATE(18, "Gate of the Community", 1, 2, 9, 1),
    ROOM_OVERVIEW(19, "Room Overview", 1, 12, 0, null),
    FLOOR_OVERVIEW(20, "Floor Overview", 1, 13, 0, null),
    CEILING_OVERVIEW(21, "Ceiling Overview", 1, 14, 0, null),
    DAMAGED_AREA(22, "Damaged Area", 1, 15, 0, null),
    OTHER_ITEMS(23, "Other Items", 1, 16, 0, null),
    ZIGZAG(24, "Zigzag", 0, 6, 10, null),
    ROOF_LAYER(25, "Roof Layer", 0, 1, 11, null),
    SELFIE(26, "Selfie", 1, 17, null, null),

    ROOF_COMPONENTS(27, "Roof Components", 0, 1, 4, null),

    PROPERTY_OVERVIEW(28, "Property Overview", 0, 28, 2, null),

    ALARM(29, "Alarm", 1, 29, 2, null),
    GENERATOR(30, "Generator", 1, 30, 2, null),
    WATER_LEAK_DETECTION(31, "Water Leak Detection", 1, 31, 2, null),
    DRY_HYDRANTS(32, "Dry Hydrants", 1, 32, 2, null),
    HOME_SPRINKLERS(33, "Home Sprinklers", 1, 33, 2, null),
    FIRE_EXTINGUISHERS(34, "Fire Extinguishers", 1, 34, 2, null),
    FIRE_PROOF_CABINETS(35, "Fire Proof Cabinets", 1, 35, 2, null),
    FLAMMABLE_RAG(36, "Flammable Rag", 1, 36, 2, null),
    NO_SMOKING_SIGNS(37, "No Smoking Signs", 1, 37, 2, null),
    HAZARDOUS_ADJACENT_PROPERTY(39, "Hazardous Adjacent Property", 1, 39, null, null),
    FIRE_ALARM(40, "Fire Alarm", 1, 40, null, null),
    BURGLAR_ALARM(41, "Burglar Alarm", 1, 41, null, null),
    FIRE_SPRINKLERS(42, "Fire Sprinklers", 1, 42, null, null),
    LOW_TEMP_SYSTEM(43, "Low-Temp System", 1, 43, null, null),
    GAS_LEAK_DETECTION(44, "Gas Leak Detection", 1, 44, null, null),
    LEED_CERTIFICATE(45, "Leed Certificate", 1, 45, null, null),
    CARETAKER(48, "Caretaker", 1, 48, null, null),
    DOORMAN_24_HOUR(51, "Doorman 24 Hour", 1, 51, null, null),
    ELEVATOR(52, "Elevator", 1, 52, null, null),
    IMPACT_RATED_GLASS_MARK(53, "Impact Rated Glass Mark", 1, 53, null, null),
    WIND_LOAD_STICKER(54, "Wind Load Sticker", 1, 54, null, null),
    WINDOW_PROTECTION(55, "Window Protection", 1, 55, null, null),
    ;

    private final int categoryId;
    private final String display;
    private final int partialType;
    private final Integer imageType;
    private final Integer orientation;
    private final int fileSourceType;

    ImageCategoryEnum(int categoryId, String display, int fileSourceType, int partialType, Integer imageType,
        Integer orientation) {
        this.categoryId = categoryId;
        this.fileSourceType = fileSourceType;
        this.display = display;
        this.partialType = partialType;
        this.imageType = imageType;
        this.orientation = orientation;
    }

    public static ImageCategoryEnum getEnum(int categoryId) {
        return Arrays.stream(ImageCategoryEnum.values())
            .filter(o -> categoryId == o.getCategoryId())
            .findFirst()
            .orElse(CONTENTS);

    }

    public static ImageCategoryEnum getEnum(int fileSourceType, int partialType, int imageType, Integer orientation) {
        if (orientation == null) {
            orientation = 0;
        }
        switch (fileSourceType) {
            case 0:
                switch (partialType) {
                    case 1:
                        switch (imageType) {
                            case 2:
                                return OVERHEAD;
                            case 3:
                                return BIRDVIEW;
                            case 4:
                                return CLOSEUP;
                            default:
                                return OTHERS;
                        }
                }
            case 1:
                switch (partialType) {
                    case 2:
                        switch (imageType) {
                            case 2:
                            case 4:
                                switch (orientation) {
                                    case 1:
                                        return FRONT_ELEVATION;
                                    case 2:
                                        return BACK_ELEVATION;
                                    case 3:
                                        return LEFT_ELEVATION;
                                    case 4:
                                        return RIGHT_ELEVATION;
                                    default:
                                        return APS;
                                }
                            default:
                                return APS;
                        }

                    case 3:
                        return INTERIOR;
                    case 4:
                        return GARAGE;
                    case 6:
                        return CONTENTS;
                    case 7:
                        return ADDRESS_VERIFICATION;
                    case 8:
                        return ELECTRICAL_PANEL;
                    case 9:
                        return PLUMBING_SYSTEM;
                    case 10:
                        return WATER_HEATER;
                    case 11:
                        return HAZARDS;
                    case 19:
                        return ROOM_OVERVIEW;
                    case 20:
                        return FLOOR_OVERVIEW;
                    case 21:
                        return CEILING_OVERVIEW;
                    case 22:
                        return DAMAGED_AREA;
                    case 23:
                        return OTHER_ITEMS;
                    default:
                        return APS;
                }
        }
        return OTHERS;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public String getDisplay() {
        return display;
    }

    public int getPartialType() {
        return partialType;
    }

    public Integer getImageType() {
        return imageType;
    }

    public Integer getOrientation() {
        return orientation;
    }

    public int getFileSourceType() {
        return fileSourceType;
    }
}
