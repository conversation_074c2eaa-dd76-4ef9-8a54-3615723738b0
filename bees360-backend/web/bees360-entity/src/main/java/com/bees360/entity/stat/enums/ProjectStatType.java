package com.bees360.entity.stat.enums;

import java.util.Arrays;

public enum  ProjectStatType {

    PROJECT_CREATED(1, "of Projects Created"),
    PROJECT_INCOMPLETED(2, "of Projects Incompleted"),
    PROJECT_COMPLETED(3, "Client Received"),
    RISK_SCORE(4, "Median Risk Score"),
    PROJECT_CLOSE_OUT(5, "of Projects Close Out"),
    PROJECT_RETURNED(6, "Returned to Client"),
    ;

    private int code;
    private String display;

    ProjectStatType(int code, String display){
        this.code = code;
        this.display = display;
    }

    public int getCode(){
        return this.code;
    }

    public String getDisplay(){
        return this.display;
    }

    public static ProjectStatType getInstance(Integer code){
        if (code == null) {
            return null;
        }
        return Arrays.stream(ProjectStatType.values())
            .filter(projectStatType -> projectStatType.getCode() == code)
            .findFirst().orElse(null);
    }
}
