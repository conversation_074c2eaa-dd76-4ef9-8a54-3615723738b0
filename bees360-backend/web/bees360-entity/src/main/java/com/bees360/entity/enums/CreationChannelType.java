package com.bees360.entity.enums;

public enum CreationChannelType {
    /**
     * created from websites
     */
    WEB,
    /**
     * created from openapi
     */
    OPENAPI,

    RISK_CONTROL,

    LOSS_CONTROL_360;
    ;

    public static CreationChannelType from(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        try {
            return CreationChannelType.valueOf(value);
        } catch (Exception e) {
            return null;
        }
    }
}
