package com.bees360.entity.vo;

import com.bees360.entity.enums.BaseCodeEnum;

public enum AIStep implements BaseCodeEnum {

    CONSTRUCION_3D(1, "3D Generation"),
    PRE_RANGING(2, "Pre-Ranging"),    // pre scoping one
    PRE_SCOPIONG(3, "Pre-Scoping"),   // pre scoping two
    PRE_FACET(4, "Pre-Plane"),        // pre segmentation
    PRE_BOUNDARY(5, "Pre-Boundary"),  // pre boundary
    POST_BOUNDARY(6, "Post-Boundary") // post boundary
    ;

    private final int code;
    private final String display;

    AIStep(int code, String display) {
        this.code = code;
        this.display = display;
    }
    @Override
    public int getCode() {
        return code;
    }
    @Override
    public String getDisplay() {
        return display;
    }

    public static AIStep getAIStep(int code) {
        for(AIStep step : values()) {
            if(step.getCode() == code) {
                return step;
            }
        }
        return null;
    }

    public boolean is3DStep() {
        return this == CONSTRUCION_3D;
    }
}
