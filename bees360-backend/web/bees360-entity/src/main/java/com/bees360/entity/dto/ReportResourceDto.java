package com.bees360.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;

@Data
@NoArgsConstructor
public class ReportResourceDto {
    /**
     * 报告原文件
     */
    @NotEmpty
    private String report;
    /**
     * 报告原文件大小
     */
    @Positive
    private int size;
    /**
     * 报告压缩文件
     */
    @NotNull
    private String reportCompressed;
    /**
     * 报告压缩后大小
     */
    @PositiveOrZero
    private int sizeCompressed;
}
