package com.bees360.entity.enums;

/**
 * Discount的折扣类型。
 *
 * <AUTHOR>
 * @date 2019/09/27 17:35
 */
public enum DiscountOffTypeEnum implements BaseCodeEnum {
    /** 按照百分比进行折扣 **/
    PERCENTAGE_OFF(1, "Percentage Off"),
    /** 按照金额数目进行折扣 **/
    AMOUNT_OFF(2, "Amount Off")
    ;

    private final int code;
    private final String display;

    DiscountOffTypeEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    public static DiscountOffTypeEnum getEnum(int code) {
        for (DiscountOffTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
