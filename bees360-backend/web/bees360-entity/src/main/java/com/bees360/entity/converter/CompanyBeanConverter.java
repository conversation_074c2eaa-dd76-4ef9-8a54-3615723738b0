package com.bees360.entity.converter;

import com.bees360.entity.Company;
import com.bees360.entity.vo.CompanyVo;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/01/03 12:07
 */
public class CompanyBeanConverter {

    public static List<CompanyVo> toCompanyVoList(List<Company> companies) {
        if(companies == null) {
            return null;
        }
        List<CompanyVo> companyVos = new ArrayList<>(companies.size());
        for(Company company: companies) {
            companyVos.add(toCompanyVo(company));
        }
        return companyVos;
    }

    public static CompanyVo toCompanyVo(Company company) {
        if(company == null) {
            return null;
        }
        CompanyVo companyVo = new CompanyVo();
        BeanUtils.copyProperties(company, companyVo);
        return companyVo;
    }
}
