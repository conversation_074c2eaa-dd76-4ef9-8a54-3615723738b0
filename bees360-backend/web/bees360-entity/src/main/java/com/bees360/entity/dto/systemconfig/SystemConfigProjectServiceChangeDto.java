package com.bees360.entity.dto.systemconfig;

import com.bees360.entity.util.SystemConfigPrefix;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class SystemConfigProjectServiceChangeDto implements SystemConfigPrefix {

    public final static String KEY_PREFIX = "projectChange";

    @NotEmpty
    private List<ServiceChange> serviceChanges;

    @Override
    public String prefix() {
        return KEY_PREFIX;
    }

    @Data
    public static class ServiceChange {
        private String state;
        private int serviceFrom;
        private int serviceTo;
    }
}
