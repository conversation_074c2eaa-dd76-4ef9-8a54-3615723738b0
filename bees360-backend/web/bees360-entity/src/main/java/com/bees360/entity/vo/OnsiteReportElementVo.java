package com.bees360.entity.vo;

import com.bees360.entity.OnsiteReportElement;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.RealtimeElementTypeEnum;

public class OnsiteReportElementVo {
	/**
	 * primary key
	 */
	private long id;
	/**
	 * project id
	 */
	private long projectId;
	/**
	 * component id
	 */
	private Integer componentId;
	/**
	 * it must be assigned a value through the Enum RealtimeElementTypeEnum.
	 */
	private int elementType;
	/**
	 * the title of the image
	 */
	private String title;
	/**
	 * image id
	 */
	private String imageId;
	/**
	 * the parent id
	 */
	private Long parentId;

	private int imageHeight;

	private int imageWidth;
	/**
	 * the url of the image
	 */
	private String url;
	/**
	 * the url of the origin image url
	 */
	private String fileName;
	/**
	 * it must be assigned a value through the Enum getReportTypeEnum.
	 */
	private int reportType;
	/**
	 * image size
	 */
	private long fileSize;
	/**
	 * crop id
	 */
	private Long annotationId;
	/**
	 * 3d crop id
	 */
	private Long annotation3dId;

	/**
	 * 1 is bees360 go app image, 0 is drone image
	 */
	private int sourceType;

	public OnsiteReportElementVo() {
		super();
	}

	public OnsiteReportElementVo(int elementType, String title, String imageId, String url, String fileName, int imageHeight,
			int imageWidth, Integer componentId, Long annotationId, Long annotation3dId) {
		super();
		this.elementType = elementType;
		this.title = title;
		this.imageId = imageId;
		this.url = url;
		this.fileName = fileName;
		this.imageHeight = imageHeight;
		this.imageWidth = imageWidth;
		this.componentId = componentId;
		this.annotationId = annotationId;
		this.annotation3dId = annotation3dId;
	}

	public OnsiteReportElementVo(OnsiteReportElement element, ProjectImage image, String url) {
		super();
		this.id = element.getId();
		this.projectId = element.getId();
		this.elementType = element.getElementType();
		this.title = element.getTitle();
		this.imageId = element.getImageId();
		this.parentId = element.getParentId();
		this.imageHeight = image.getImageHeight();
		this.imageWidth = image.getImageWidth();
		this.url = url;
		this.fileName = image.getFileName();
		this.reportType = element.getReportType();
		this.fileSize = image.getFileSize();
		this.annotationId = element.getAnnotationId();
		this.annotation3dId = element.getAnnotation3dId();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public int getElementType() {
		return elementType;
	}

	public void setElementType(int elementType) {
		this.elementType = elementType;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getImageId() {
		return imageId;
	}

	public void setImageId(String imageId) {
		this.imageId = imageId;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public int getImageHeight() {
		return imageHeight;
	}

	public void setImageHeight(int imageHeight) {
		this.imageHeight = imageHeight;
	}

	public int getImageWidth() {
		return imageWidth;
	}

	public void setImageWidth(int imageWidth) {
		this.imageWidth = imageWidth;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public int getReportType() {
		return reportType;
	}

	public void setReportType(int reportType) {
		this.reportType = reportType;
	}

	public long getFileSize() {
		return fileSize;
	}

	public void setFileSize(long fileSize) {
		this.fileSize = fileSize;
	}

	public Integer getComponentId() {
		return componentId;
	}

	public void setComponentId(Integer componentId) {
		this.componentId = componentId;
	}

	public Long getAnnotationId() {
		return annotationId;
	}

	public void setAnnotationId(Long annotationId) {
		this.annotationId = annotationId;
	}

	public Long getAnnotation3dId() {
		return annotation3dId;
	}

	public void setAnnotation3dId(Long annotation3dId) {
		this.annotation3dId = annotation3dId;
	}

	public int getSourceType() {
		return sourceType;
	}

	public void setSourceType(int sourceType) {
		this.sourceType = sourceType;
	}

	public RealtimeElementTypeEnum getElementTypeEnum() {
		return RealtimeElementTypeEnum.getEnum(this.elementType);
	}

	@Override
	public String toString() {
		return "OnsiteReportElementVo [id=" + id + ", projectId=" + projectId + ", componentId=" + componentId
				+ ", elementType=" + elementType + ", title=" + title + ", imageId=" + imageId + ", parentId="
				+ parentId + ", imageHeight=" + imageHeight + ", imageWidth=" + imageWidth + ", url=" + url
				+ ", fileName=" + fileName + ", reportType=" + reportType + ", fileSize=" + fileSize + ", annotationId="
				+ annotationId + ", annotation3dId=" + annotation3dId + "]";
	}

}
