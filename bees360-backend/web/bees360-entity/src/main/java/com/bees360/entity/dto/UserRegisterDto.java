package com.bees360.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/6/11 10:52 AM
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class UserRegisterDto {
//    String account;
    String avatar;
    String firstName;
    String lastName;
    Long companyId;
    String password;
    String rePassword;
    long roles;
    double lng;
    double lat;
    String address;
    String city;
    String state;
    String zipCode;
    String country;
    /**
     * 用户的活动半径
     */
    double travelRadius;

    String email;
    String phone;
    String externalUserId;
}
