package com.bees360.entity.enums;

/**
 * Invoice的状态
 *
 * <AUTHOR>
 * @date 2019/10/15
 */
public enum InvoiceStatusEnum implements BaseCodeEnum {
    DELETE(1, "Deleted"),
    DRAFT(2, "Draft"),
    OPEN(3, "Open"),
    PAID(4, "Paid")
    ;

    private final int code;
    private final String display;

    InvoiceStatusEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    public static InvoiceStatusEnum getEnum(int code) {
        for(InvoiceStatusEnum status: values()) {
            if(status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
