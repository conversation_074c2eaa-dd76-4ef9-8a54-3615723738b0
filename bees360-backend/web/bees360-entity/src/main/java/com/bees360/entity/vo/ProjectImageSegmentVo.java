package com.bees360.entity.vo;

import com.bees360.entity.dto.Point;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class ProjectImageSegmentVo extends ProjectImageOnsiteVo implements Cloneable {

	private String category;

	private String subcategory;

	private String description;

	private String additional;

	private int valueType;

	private long[] codes;

	private String[] parentIds;

	private Point mapping2d;

	public ProjectImageSegmentVo(ProjectImageOnsiteVo image) {
		this.setImage(image.getImage());
		this.setAnnotationTypes(image.getAnnotationTypes());
		this.setScreenshots(image.getScreenshots());
		this.setAnnotations(image.getAnnotations());
		this.setMapping(image.getMapping());
		this.setArea(image.getArea());
		this.setFacetIds(image.getFacetIds());
		this.setMapping2d(image.getMapping2d());
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getSubcategory() {
		return subcategory;
	}

	public void setSubcategory(String subcategory) {
		this.subcategory = subcategory;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@JsonIgnore
	public int getValueType() {
		return valueType;
	}

	public void setValueType(int valueType) {
		this.valueType = valueType;
	}

	@JsonIgnore
	public long[] getCodes() {
		return codes;
	}

	public void setCodes(long[] codes) {
		this.codes = codes;
	}

	public String[] getParentIds() {
		return parentIds;
	}

	public void setParentIds(String[] parentIds) {
		this.parentIds = parentIds;
	}

	public Point getMapping2d() {
		return mapping2d;
	}

	public void setMapping2d(Point mapping2d) {
		this.mapping2d = mapping2d;
	}

	public String getAdditional() {
		return additional;
	}

	public void setAdditional(String additional) {
		this.additional = additional;
	}


}
