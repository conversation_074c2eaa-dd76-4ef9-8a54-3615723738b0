package com.bees360.entity.dto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProcessStatusEnum;

import lombok.ToString;
import org.springframework.util.CollectionUtils;

@ToString
@Data
public class ProjectSearchOptionForApp extends ProjectSearchOptionBase {
	public final String SEPARATOR = ",";

	private int startIndex;
	private Long userId;
	private Long startTime;
	private Long endTime;
	private Long projectId;
	private Long companyId;

	private String searchProjectId;

    /**
     * app 采集图片是否完成
     * 只有当 task check的状态为 {@link com.bees360.entity.enums.ProjectStatusEnum#TASK_CHECKED_OUT}
     * 且 imageUploadStatus为 {@link com.bees360.entity.enums.ImageUploadStatusEnum#ALL_UPLOADED} 时，才有true
     */
    private Boolean collectFinished;

	private String[] zipCodes;
	private String[] states;
	private int[] processStatuses;
	private int[] projectStatuses;
	private long[] insuranceCompanyIds;

	private String address;
	private Integer flyZoneType;
	private Long memberUser;
	private Integer memberRole;
	private Long assignStartTime;
	private Long assignEndTime;
	private List<Long> projectIds;

    private Long inspectionStartTime;
    private Long inspectionEndTime;

    private Integer[] inspectionPurposeTypes;

	public static enum EPAppSortKey {
		PID,  // projectId
		ADDR, // address
		CN,  // claim number
		CT;  // created time

		public static EPAppSortKey getEnum(String value) {
			if(value == null) {
				return null;
			}
			for(EPAppSortKey key: EPAppSortKey.values()) {
				if(key.name().equals(value)) {
					return key;
				}
			}
			return null;
		}
	}

	/* getter and setter */
	public void setSortKey(EPAppSortKey sortKey) {
		if(sortKey == null){
			this.sortKey = null;
		} else {
			this.sortKey = sortKey.name();
		}
	}

	@Override
	public void setSortKey(String sortKey) {
		if(sortKey == null){
			this.sortKey = null;
		} else {
			setSortKey(EPAppSortKey.getEnum(sortKey.toUpperCase()));
		}
	}
	public Long getProjectId() {
		return projectId;
	}
	public void setProjectId(Long projectId) {
		this.projectId = projectId;
	}

	public Long getCompanyId() {
		return companyId;
	}
	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public List<Long> getProjectIds() {
		return projectIds;
	}

	public void setProjectIds(List<Long> projectIds) {
		this.projectIds = projectIds;
	}

	public String[] getZipCodes() {
		return zipCodes;
	}
	public void setZipCodes(String zipCodes) {
		if(StringUtils.isNotBlank(zipCodes)){
			this.zipCodes = zipCodes.split(SEPARATOR);
		}
	}
	public String[] getStates() {
		return states;
	}
	public void setStates(String states) {
		if(StringUtils.isNotBlank(states)){
			this.states = states.split(SEPARATOR);
		}
	}
	public long[] getInsuranceCompanyIds() {
		return insuranceCompanyIds;
	}
	public void setInsuranceCompanyIds(String insuranceCompanyIds) {
		if(StringUtils.isNotBlank(insuranceCompanyIds)){
			String[] insuranceCompanyIdStrs = insuranceCompanyIds.split(SEPARATOR);
			this.insuranceCompanyIds = new long[insuranceCompanyIdStrs.length];
			for(int i = 0; i < insuranceCompanyIdStrs.length; i++){
				this.insuranceCompanyIds[i] = Long.valueOf(insuranceCompanyIdStrs[i]);
			}
		}
	}
	public int getStartIndex() {
		return startIndex;
	}

	public void setStartIndex() {
		this.startIndex = (pageIndex - 1) * pageSize;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getStartTime() {
		return startTime;
	}

	public void setStartTime(Long startTime) {
		this.startTime = startTime;
	}

	public Long getEndTime() {
		return endTime;
	}

	public void setEndTime(Long endTime) {
		this.endTime = endTime;
	}
	public int[] getStatuses() {
		return processStatuses;
	}
	public void setStatuses(String statuses) {
		Set<Integer> range = Arrays.stream(ProcessStatusEnum.values())
			.map(status -> status.getCode()).collect(Collectors.toSet());
		setStatuses(splitToInts(statuses, range));
	}

	public void setStatuses(int[] statuses) {
		this.processStatuses = statuses;
	}

	public int[] getProjectStatuses() {
		return projectStatuses;
	}
	public void setProjectStatuses(String projectStatuses) {
		Set<Integer> range = Arrays.stream(NewProjectStatusEnum.values())
			.map(status -> status.getCode()).collect(Collectors.toSet());
		setProjectStatuses(splitToInts(projectStatuses, range));
	}
	public void setProjectStatuses(int[] projectStatuses) {
		this.projectStatuses = projectStatuses;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Integer getFlyZoneType() {
		return flyZoneType;
	}

	public void setFlyZoneType(Integer flyZoneType) {
		this.flyZoneType = flyZoneType;
	}

	public Long getMemberUser() {
		return memberUser;
	}

	public void setMemberUser(Long memberUser) {
		this.memberUser = memberUser;
	}

	public Integer getMemberRole() {
		return memberRole;
	}

	public void setMemberRole(Integer memberRole) {
		this.memberRole = memberRole;
	}

	public Long getAssignStartTime() {
		return assignStartTime;
	}

	public void setAssignStartTime(Long assignStartTime) {
		this.assignStartTime = assignStartTime;
	}

	public Long getAssignEndTime() {
		return assignEndTime;
	}

	public void setAssignEndTime(Long assignEndTime) {
		this.assignEndTime = assignEndTime;
	}

    public Long getInspectionStartTime() {
        return inspectionStartTime;
    }

    public void setInspectionStartTime(Long inspectionStartTime) {
        this.inspectionStartTime = inspectionStartTime;
    }

    public Long getInspectionEndTime() {
        return inspectionEndTime;
    }

    public void setInspectionEndTime(Long inspectionEndTime) {
        this.inspectionEndTime = inspectionEndTime;
    }

	public Integer[] getInspectionPurposeTypes() {
		return inspectionPurposeTypes;
	}

    public void setInspectionPurposeTypes(String inspectionPurposeTypes) {
        this.inspectionPurposeTypes = checkPurposeTypes(inspectionPurposeTypes);
    }

    public Integer[] checkPurposeTypes(String inspectionPurposeTypes) {

        if (StringUtils.isBlank(inspectionPurposeTypes)) {
            return null;
        }
        String[] inspectionPurposeTypeStrs = inspectionPurposeTypes.split(SEPARATOR);
        List<Integer> typesList = new ArrayList<>();
        for (String inspectionPurposeTypeStr : inspectionPurposeTypeStrs) {
            if (isNumeric(inspectionPurposeTypeStr)) {
                int code = Integer.valueOf(inspectionPurposeTypeStr);
                typesList.add(code);
            }
        }
        Integer[] integers = new Integer[typesList.size()];
        for (int i = 0; i < typesList.size(); i++) {
            integers[i] = typesList.get(i);
        }
        return integers;
	}


	public Set<Integer> getClaimTypes() {
        if (inspectionPurposeTypes == null) {
            return null;
        }
	    Set<Integer> purposeType = new HashSet<>(Arrays.asList(inspectionPurposeTypes));

		Set<InspectionPurposeTypeEnum> serviceTypeEnums = purposeType.stream().map(InspectionPurposeTypeEnum::getEnum)
            .filter(Objects::nonNull).collect(Collectors.toSet());

		return CollectionUtils.isEmpty(purposeType)? new HashSet<>():
            serviceTypeEnums.stream().map(InspectionPurposeTypeEnum::getSubTypes)
            .flatMap(subTypes -> subTypes.stream().map(ClaimTypeEnum::getCode))
                .collect(Collectors.toSet());
	}

	public String getAddressRegex() {
		return createFuzzyRegex(address);
	}

	private int[] splitToInts(String intLinked, Set<Integer> intRange) {
		List<Integer> ints = new ArrayList<>();
		if(StringUtils.isNotBlank(intLinked)){
			String[] statusCodes = intLinked.split(SEPARATOR);
			for(int i = 0; i < statusCodes.length; i++){
				try {
					int code = Integer.valueOf(statusCodes[i]);
					if (intRange.contains(code)) {
						ints.add(code);
					}
				} catch(Exception e) {
					// do nothing
				}
			}
		}
		int[] result = new int[ints.size()];
		for(int i = 0; i < ints.size(); i ++) {
			result[i] = ints.get(i);
		}
		return result;
	}

	public void setPagination(int pageIndex, int pageSize) {
		this.pageIndex = pageIndex;
		this.pageSize = pageSize;
		setStartIndex();
	}

    private static boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[0-9]+");
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }
}
