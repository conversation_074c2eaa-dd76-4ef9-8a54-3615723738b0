package com.bees360.entity.enums;

public enum UserCreationTypeEnum implements BaseCodeEnum {

    REGISTER(0, "self register"),
    ADMIN_ADD(1, "admin added"),
    /**
     * ai-user 服务器token请求，而web后端不存在该用户时添加用户
     */
    AI_USER_BIND(2, "ai user bind"),

    SSO(3, "single sign on"),

    /**
     * 通过事件添加的用户
     */
    EVENT_ADD(4, "event added")
    ;

    private final int code;
    private final String display;

    UserCreationTypeEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }
}
