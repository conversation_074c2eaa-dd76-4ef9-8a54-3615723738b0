package com.bees360.entity;

import java.util.HashMap;
import java.util.Map;

import com.bees360.entity.dto.WebSocketMsgData;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class Notification implements WebSocketMsgData{
	private long notificationId;
	//The user receive the notification
	private long recipient;
	private long projectId;
	// It must be assigned a value through the Enum ProjectStatusEnum.
	private int projectStatus;
	//The content we send.
	private String content;
	//The time when the notification created;
	private long createdTime;

	public enum NotificationType {
		// the type for test websocket connection.
		CONNECTION_TYPE(0),
		//Just give the notification to user.
		SYSTEM_NOTIFICATION(1),
		//Control the show of web.
		WORKFLOW_NOTIFICATION(2),
		//the type for check heartbeat.
		HEARTBEAT_MECHANISM_TYPE(3),
		;

		private final int code;

		NotificationType(int code){
			this.code = code;
		}

		public int getCode() {
			return code;
		}
	}

	public Notification(){ }

	public Notification(long notificationId, long recipient, long projectId, int projectStatus,
			int type, String content, long createdTime){
		this.notificationId = notificationId;
		this.recipient = recipient;
		this.projectId = projectId;
		this.projectStatus = projectStatus;
		this.content = content;
		this.createdTime = createdTime;
	}

	private final NotificationType type = NotificationType.SYSTEM_NOTIFICATION;

	@JsonIgnore
	public NotificationType getType() {
		return type;
	}

	@Override
	public Map<String, Object> toWebSocketMsgFieldMap() {
		Map<String, Object> msg = new HashMap<String, Object>();
		msg.put("notificationId", notificationId);
		msg.put("projectId", projectId);
		msg.put("content", content);
		msg.put("createdTime", createdTime);
		return msg;
	}

	/* getter and setter */
	public long getNotificationId() {
		return notificationId;
	}
	public void setNotificationId(long notificationId) {
		this.notificationId = notificationId;
	}
	public long getRecipient() {
		return recipient;
	}
	public void setRecipient(long recipient) {
		this.recipient = recipient;
	}
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public int getProjectStatus() {
		return projectStatus;
	}
	public void setProjectStatus(int projectStatus) {
		this.projectStatus = projectStatus;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public long getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}

	@Override
	public String toString() {
		return "Notification [notificationId=" + notificationId + ", recipient=" + recipient + ", projectId="
				+ projectId + ", projectStatus=" + projectStatus + ", content=" + content + ", createdTime="
				+ createdTime + "]";
	}

}
