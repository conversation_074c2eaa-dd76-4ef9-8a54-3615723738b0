package com.bees360.entity.enums;

public enum InspectionServiceEnum implements BaseCodeEnum {
	GOLD(1, "Gold", 1000, 30.0D, 20.0D),
	SILVER(2, "Silver", 500, 35.0D, 30.0D),
	BRONZE(3, "Bronze", 200, 40.0D, 40.0D)
	;
	private final int code;
	private final String display;
	private final int volume;
	private final double reportPrice;
	private final double pilotPrice;

	InspectionServiceEnum(int code, String display,
			int volume, double reportPrice, double pilotPrice) {
		this.code = code;
		this.display = display;
		this.volume = volume;
		this.reportPrice = reportPrice;
		this.pilotPrice = pilotPrice;
	}

	public static InspectionServiceEnum getEnum(int code) {
		InspectionServiceEnum[] values = InspectionServiceEnum.values();
		for(InspectionServiceEnum s: values) {
			if(s.getCode() == code) {
				return s;
			}
		}
		return null;
	}

	public String getDetail() {
		return "volume: " + volume + ", report price: $" + reportPrice + ", pilot price: $" + pilotPrice;
	}

	/* getter and setter */
	@Override
	public int getCode() {
		return code;
	}
	@Override
	public String getDisplay() {
		return display;
	}
	public int getVolume() {
		return volume;
	}
	public double getReportPrice() {
		return reportPrice;
	}
	public double getPilotPrice() {
		return pilotPrice;
	}
}
