package com.bees360.entity.vo;

import com.bees360.entity.dto.SegmentImageUnderwritingDto;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImagePartialViewTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class SegmentImageUnderwritingVo {

    private long id;

    private String description;

    private String additional;

    private String hintImage;

    private int fileSourceType = FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode();

    private Integer partialType;

    private Integer imageType;

    private Integer orientation;

    public SegmentImageUnderwritingVo(SegmentImageUnderwritingDto segment) {
        this.id = segment.getDescriptionId();
        this.description = segment.getDescription();
        this.additional = segment.getAdditional();
        this.hintImage = segment.getHintImage();
        this.partialType = segment.getPartialType();
        this.orientation = segment.getOrientation();
    }
}
