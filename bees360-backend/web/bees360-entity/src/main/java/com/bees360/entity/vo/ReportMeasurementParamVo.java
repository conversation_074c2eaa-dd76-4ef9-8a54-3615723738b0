package com.bees360.entity.vo;

import java.util.List;
import java.util.Map;

import com.bees360.entity.dto.Point3D;

public class ReportMeasurementParamVo {

	private Map<Integer, double[]> lengthMap;

	private Map<Integer, double[]> areaMap;

	private Map<Integer, double[]> pitchMap;

	private List<Point3D[]> pointList;

	private List<int[]> typeList;

	private double[] border;

	private int[] edgeCount;

	private int[] areas;

	private int[] pitchs;

	private List<Double> pitchFacetList;

	public ReportMeasurementParamVo(Map<Integer, double[]> lengthMap, Map<Integer, double[]> areaMap,
			Map<Integer, double[]> pitchMap, List<Point3D[]> pointList,
			List<int[]> typeList, double[] border, int[] edgeCount, int[] areas, int[] pitchs,
			List<Double> pitchFacetList) {
		super();
		this.lengthMap = lengthMap;
		this.areaMap = areaMap;
		this.pitchMap = pitchMap;
		this.pointList = pointList;
		this.typeList = typeList;
		this.border = border;
		this.edgeCount = edgeCount;
		this.areas = areas;
		this.pitchs = pitchs;
		this.pitchFacetList = pitchFacetList;
	}

	public Map<Integer, double[]> getLengthMap() {
		return lengthMap;
	}

	public void setLengthMap(Map<Integer, double[]> lengthMap) {
		this.lengthMap = lengthMap;
	}

	public Map<Integer, double[]> getAreaMap() {
		return areaMap;
	}

	public void setAreaMap(Map<Integer, double[]> areaMap) {
		this.areaMap = areaMap;
	}

	public Map<Integer, double[]> getPitchMap() {
		return pitchMap;
	}

	public void setPitchMap(Map<Integer, double[]> pitchMap) {
		this.pitchMap = pitchMap;
	}

	public List<Point3D[]> getPointList() {
		return pointList;
	}

	public void setPointList(List<Point3D[]> pointList) {
		this.pointList = pointList;
	}

	public List<int[]> getTypeList() {
		return typeList;
	}

	public void setTypeList(List<int[]> typeList) {
		this.typeList = typeList;
	}

	public double[] getBorder() {
		return border;
	}

	public void setBorder(double[] border) {
		this.border = border;
	}

	public int[] getEdgeCount() {
		return edgeCount;
	}

	public void setEdgeCount(int[] edgeCount) {
		this.edgeCount = edgeCount;
	}

	public int[] getAreas() {
		return areas;
	}

	public void setAreas(int[] areas) {
		this.areas = areas;
	}

	public int[] getPitchs() {
		return pitchs;
	}

	public void setPitchs(int[] pitchs) {
		this.pitchs = pitchs;
	}

	public List<Double> getPitchFacetList() {
		return pitchFacetList;
	}

	public void setPitchFacetList(List<Double> pitchFacetList) {
		this.pitchFacetList = pitchFacetList;
	}

}
