package com.bees360.entity.enums.productandpayment;

import java.util.HashMap;
import java.util.Map;

import com.bees360.entity.enums.BaseCodeEnum;

public enum ProductTypeEnum implements BaseCodeEnum {
	REPORT(1, "report service"),
	PILOT(2, "pilot service")
	;
	private final int code;
	private final String display;

	private static Map<Integer, ProductTypeEnum> ENUM_MAP;

	static {
		ENUM_MAP = new HashMap<Integer, ProductTypeEnum>();
		for(ProductTypeEnum type: values()) {
			ENUM_MAP.put(type.getCode(), type);
		}
	}

	ProductTypeEnum(int code, String display) {
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public static ProductTypeEnum getEnum(int code) {
		return ENUM_MAP.get(code);
	}
}
