package com.bees360.entity.dto;

import java.util.List;

public class AnnotationsInImage {
	private String imageId;
	private List<ImageAnnotation2DCellDto> annotations;

	public String getImageId() {
		return imageId;
	}
	public void setImageId(String imageId) {
		this.imageId = imageId;
	}
	public List<ImageAnnotation2DCellDto> getAnnotations() {
		return annotations;
	}
	public void setAnnotations(List<ImageAnnotation2DCellDto> annotations) {
		this.annotations = annotations;
	}
	@Override
	public String toString() {
		return "AnnotationsInImage [imageId=" + imageId + ", annotations=" + annotations + "]";
	}
}
