package com.bees360.entity.openapi.reportsummary;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SummaryInterior {
    /**
     * Conditional of overall interior Excellent-5, Good-4, Average3, Fair-2, Poor-1
     * Required
     */
    private String overallCondition;
    /**
     * If active or inactive leaks are visible
     * Optional
     */
    private Boolean hasVisibleLeaks;
    /**
     * If there is existing interior damage is present
     * Optional
     */
    private Boolean hasExistingDamage;

    private SummaryInteriorPlumbing plumbing;

    private SummaryInteriorElectric electric;

    private SummaryInteriorFloorplan floorplan;

    private SummaryInteriorWaterHeater waterHeater;

    private SummaryInteriorFireAlarm fireAlarm;

    private SummaryInteriorBurglarAlarm burglarAlarm;

    private SummaryInteriorHeatingCooling heatingCooling;

    private List<SummaryInteriorAppliance> appliances = new ArrayList<>();

    /**
     * List of text comments to interior.
     */
    private List<@NotNull String> comments = new ArrayList<>();
}
