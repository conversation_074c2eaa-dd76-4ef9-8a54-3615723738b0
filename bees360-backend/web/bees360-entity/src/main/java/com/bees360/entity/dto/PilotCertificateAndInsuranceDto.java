package com.bees360.entity.dto;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用来同步飞手证书以保单数据到Firebase的DTO模型
 */
@Data
@ToString
public class PilotCertificateAndInsuranceDto {
    private long userId;
    private String licenseNumber;
    private String licenseKeyUrls;
    private Date licenseIssueDate;
    private Date licenseExpiryDate;

    private String insuranceNumber;
    private String insuranceKeyUrls;
    private Date insuranceExpiryDate;
    private BigDecimal insuranceAmount;
}
