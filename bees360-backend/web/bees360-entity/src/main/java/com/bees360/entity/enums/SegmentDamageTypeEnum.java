package com.bees360.entity.enums;

/**
 * <AUTHOR>
 * @date 2019/09/06 10:35
 */
public enum SegmentDamageTypeEnum implements BaseCodeEnum {
    WINDOW_SCREEN(30000, "Window screen"),
    WINDOW_REGLAZE(30010, "Window reglaze"),
    WINDOW_BEADING(30020, "Window beading"),
    WINDOWS(30030, "Windows"),
    GUTTERS(30040, "Gutters"),
    DOWNSPOUTS(30050, "Downspouts"),
    COMB_FINS(30060, "Comb A/C fins"),
    FASCIA_METAL(30070, "Fascia wrap/fascia metal"),
    SIDING(30080, "Siding"),
    SOFFIT(30090, "Soffit"),
    FRAME(30100, "Frame");

    private final int code;
    private final String display;

    SegmentDamageTypeEnum(int code, String display){
        this.code = code;
        this.display = display;
    }

    @Override
    public int getCode() {
        return code;
    }

    public long getCodeLong() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }
}
