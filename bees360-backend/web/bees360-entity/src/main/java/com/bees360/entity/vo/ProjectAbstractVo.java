package com.bees360.entity.vo;

import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.ProjectTypeEnum;

public class ProjectAbstractVo {
	private long projectId;
	private int projectType;
	private Double claimRcv;
	private Double claimAcv;
	private int claimType;

	private String assetOwnerName;
	private String assetOwnerPhone;
	private String assetOwnerEmail;

	private String insuranceCompany;
	private String policyNumber;

	//	Getter and Setter
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public String getProjectType() {
		ProjectTypeEnum type = ProjectTypeEnum.getEnum(projectType);
		return type == null? "": type.getDisplay();
	}
	public void setProjectType(int projectType) {
		this.projectType = projectType;
	}
	public Double getClaimRcv() {
		return claimRcv;
	}
	public void setClaimRcv(Double claimRcv) {
		this.claimRcv = claimRcv;
	}
	public Double getClaimAcv() {
		return claimAcv;
	}
	public void setClaimAcv(Double claimAcv) {
		this.claimAcv = claimAcv;
	}
	public String getClaimType() {
		ClaimTypeEnum type = ClaimTypeEnum.getEnum(claimType);
		return type == null? "": type.getDisplay();
	}
	public void setClaimType(int claimType) {
		this.claimType = claimType;
	}
	public String getAssetOwnerName() {
		return assetOwnerName;
	}
	public void setAssetOwnerName(String assetOwnerName) {
		this.assetOwnerName = assetOwnerName;
	}
	public String getAssetOwnerPhone() {
		return assetOwnerPhone;
	}
	public void setAssetOwnerPhone(String assetOwnerPhone) {
		this.assetOwnerPhone = assetOwnerPhone;
	}
	public String getAssetOwnerEmail() {
		return assetOwnerEmail;
	}
	public void setAssetOwnerEmail(String assetOwnerEmail) {
		this.assetOwnerEmail = assetOwnerEmail;
	}
	public String getInsuranceCompany() {
		return insuranceCompany;
	}
	public void setInsuranceCompany(String insuranceCompany) {
		this.insuranceCompany = insuranceCompany;
	}
	public String getPolicyNumber() {
		return policyNumber;
	}
	public void setPolicyNumber(String policyNumber) {
		this.policyNumber = policyNumber;
	}
	@Override
	public String toString() {
		return "ProjectAbstractVo [projectId=" + projectId + ", projectType=" + projectType + ", claimRcv=" + claimRcv
				+ ", claimAcv=" + claimAcv + ", claimType=" + claimType + ", assetOwnerName=" + assetOwnerName
				+ ", assetOwnerPhone=" + assetOwnerPhone + ", assetOwnerEmail=" + assetOwnerEmail
				+ ", insuranceCompany=" + insuranceCompany + ", policyNumber=" + policyNumber + "]";
	}

//	special getter
}
