package com.bees360.entity.enums;

import java.util.Arrays;

public enum ClaimNoteAddEnum {
    NONE(0, ""),
    GPS_IS_APPROXIMATE(1, "Google is not accurate in locating this address, please reconfirm with the householder")

    ;
    final private int code;
    final private String display;

    ClaimNoteAddEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    public int getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }

    public static ClaimNoteAddEnum getEnum(Integer code) {
        return Arrays.stream(values()).filter(statusEnum -> code == statusEnum.getCode()).findFirst().orElse(NONE);
    }
}
