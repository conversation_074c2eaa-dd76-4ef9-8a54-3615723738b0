package com.bees360.entity.openapi.reportsummary;

import java.util.List;

import com.bees360.web.core.json.gson.MapToStringListTypeAdapter;
import com.bees360.web.core.json.gson.RemoveNullListAdapter;
import com.google.gson.annotations.JsonAdapter;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SummaryRecommendation {
    private String text;
    @JsonAdapter(RemoveNullListAdapter.class)
    private List<SummaryImage> image;
}
