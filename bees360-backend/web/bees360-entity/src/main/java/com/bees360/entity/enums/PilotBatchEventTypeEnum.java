package com.bees360.entity.enums;

import java.util.Objects;
import java.util.stream.Stream;

public enum PilotBatchEventTypeEnum implements BaseCodeEnum{

    CREATED(1, "created", 1),
    UPDATED(2, "updated", null),
    DELETED(3, "deleted", -1),
    RECOVER(4, "recover", null),
    ;

    private final int code;
    private final String display;
    private final Integer status;

    PilotBatchEventTypeEnum(int code, String display, Integer status) {
        this.code = code;
        this.display = display;
        this.status = status;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    @Override
    public int getCode() {
        return code;
    }

    public Integer getStatus() {
        return status;
    }

    public static PilotBatchEventTypeEnum getEnum(int code) {
        return Stream.of(PilotBatchEventTypeEnum.values()).filter(o -> Objects.equals(code, o.getCode())).findFirst().orElse(null);
    }

    public static void check(int code){
        if (getEnum(code) == null){
            throw new IllegalArgumentException();
        }
    }


}
