package com.bees360.entity.dto;

import com.bees360.commons.springsupport.validation.Year;
import com.bees360.entity.AttachmentEntity;
import com.bees360.entity.Project;
import com.bees360.web.core.validation.Phone;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 创建项目最基本的信息
 * 创建项目最基本的信息，String类型值均转化为空字符串
 *
 * <AUTHOR>
 * @date 2019/12/19 17:02
 */
@Data
public class ProjectBaseDto {
    /**
     * @see Project#getAddress()
     */
    @NotBlank
    private String address;
    /**
     * @see Project#getCity()
     */
    @NotBlank
    private String city;
    /**
     * @see Project#getState()
     */
    @NotBlank
    private String state;
    /**
     * @see Project#getCountry()
     */
    @NotBlank
    private String country;
    /**
     * @see Project#getZipCode()
     */
    @NotBlank
    private String zipCode;
    /**
     * @see Project#getLng()
     */
    @NotNull
    private Double lng = 0D;
    /**
     * @see Project#getLat()
     */
    @NotNull
    private Double lat= 0D;
    /**
     * @see Project#getProjectType()
     */
    private Integer projectType;
    /**
     * @see Project#getClaimType()
     */
    private Integer claimType;
    /**
     * @see Project#getDamageEventTime()
     */
    private Long damageEventTime;

    // ======================================
    // 非必填字段
    /**
     * @see Project#getCustomer()
     */
    @NotNull
    private String customer = "";
    /**
     * @see Project#getPolicyNumber()
     */
    @NotNull
    private String policyNumber = "";

    private String policyType;
    /**
     * @see Project#getClaimNumber()
     */
    @NotNull
    private String claimNumber = "";
    /**
     * @see Project#getInspectionNumber()
     */
    @NotNull
    private String inspectionNumber = "";
    /**
     * @see Project#getInspectionType()
     */
    @NotNull
    private String inspectionType = "";
    /**
     * @see Project#getInspectionTime()
     */
    private Long inspectionTime;
    /**
     * @see Project#getFlyZoneType()
     */
    private int flyZoneType;
    /**
     * @see Project#getDueDate()
     */
    private Long dueDate;
    /**
     * @see Project#getAssetOwnerName()
     */

    // null if insuredFirstName exists or else not null required
    private String assetOwnerName = "";

    private String insuredFirstName = "";

    private String insuredMiddleName = "";

    private String insuredLastName = "";

    /**
     * @see Project#getAssetOwnerPhone()
     */
    @NotNull
    @Phone
    private String assetOwnerPhone = "";
    /**
     * @see Project#getAssetOwnerEmail()
     */
    @NotNull
    private String assetOwnerEmail = "";
    /**
     * @see Project#getInsuredHomePhone()
     */
    @NotNull
    @Phone
    private String insuredHomePhone = "";
    /**
     * @see Project#getInsuredWorkPhone()
     */
    @NotNull
    @Phone
    private String insuredWorkPhone = "";
    /**
     * @see Project#getAgent()
     */
    @NotNull
    private String agent = "";
    /**
     * @see Project#getAgentContactName()
     */
    @NotNull
    private String agentContactName = "";
    /**
     * @see Project#getAgentEmail()
     */
    @NotNull
    private String agentEmail = "";
    /**
     * @see Project#getAgentPhone()
     */
    @NotNull
    private String agentPhone = "";
    /**
     * @see Project#getGuideline()
     */
    @NotNull
    private String guideline = "";
    /**
     * @see Project#getDescription()
     */
    @NotNull
    private String description = "";

    /**
     * @see Project#testFlag
     */
    private boolean testFlag;

    private String specialInstructions;
    private String specialInstructionComments;
    private LocalDate policyEffectiveDate;
    @Year
    private String yearBuilt;

    private boolean gpsIsApproximate;

    private String claimNote;

    /**
     * @see Project#getServiceType()
     */
    @NotNull
    private Integer serviceType;

    private Boolean isRenewal;

    private List<String> supplementalServices;

    private String projectDivision;

    private Double dwellingCoverage;

    private Double otherStructureCoverage;

    private Double contentCoverage;

    private Double carrierProvidedLivingArea;

    /**
     * @see Project#getCatNumber()
     */
    @NotNull
    private String catNumber = "";

    private Integer catLevel;

    public Project toProject() {
        Project project = new Project();
        BeanUtils.copyProperties(this, project);
        return project;
    }

    @Data
    public static class WebComment {
        private String content;
        private List<AttachmentEntity> attachments;
    }

    private List<WebComment> comments;
}
