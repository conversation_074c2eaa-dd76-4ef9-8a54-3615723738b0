package com.bees360.entity.vo;

import com.bees360.entity.enums.StructrueEnum;

public class StructureVo {

	private Integer id;
	private Integer type;

	// Getter and Setter
	public String getName() {
		if (null != type) {
			StructrueEnum structrue = StructrueEnum.getEnum(type);
			return structrue.getDisplay() == null? "": structrue.getDisplay();
		} else {
			return "Structure " + id;
		}
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

}
