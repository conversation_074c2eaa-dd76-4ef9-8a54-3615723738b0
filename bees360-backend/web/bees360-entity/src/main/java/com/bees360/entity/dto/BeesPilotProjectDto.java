package com.bees360.entity.dto;

import com.bees360.entity.Project;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020/7/7 12:08 PM
 **/
@Data
public class BeesPilotProjectDto extends Project{
    // bees pilot 状态相关字段
    /**
     * 该任务是否全部完成
     *
     * @see com.bees360.entity.enums.CheckStatusEnum
     */
    private int checkStatus;

    /**
     * @see com.bees360.entity.enums.CheckStatusEnum
     */
    private int mobileCheckStatus;

    /**
     * bees drone check status
     * @see com.bees360.entity.enums.CheckStatusEnum
     */
    private int droneCheckStatus;

    /**
     * pilot complete quiz or not
     */
    private Boolean quizCompleted;

    /**
     * pilot complete verify address of the task or not
     */
    private Boolean addressVerified;

    /**
     * the timestamp when pilot checked out
     */
    private Long checkoutTime;

    private boolean imageUploaded;

    private Long imageUploadedTime;
    private Long lastUpdateTime;
}
