package com.bees360.entity.query;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/8/4 2:27 PM
 **/
@ToString
@Data
public class BeesPilotProjectQuery {
	private Long userId;
	private String searchProjectId;

    /**
     * app 采集图片是否完成
     * 只有当 task check的状态为 {@link com.bees360.entity.enums.ProjectStatusEnum#TASK_CHECKED_OUT}
     * 且 imageUploadStatus为 {@link com.bees360.entity.enums.ImageUploadStatusEnum#ALL_UPLOADED} 时，才有true
     */
    private Boolean collectFinished;

    /**
     * 飞手图片是否上传完成
     */
    private Boolean imageUploaded;

    private String address;
    private List<Long> projectIds;
    private Long inspectionStartTime;
    private Long inspectionEndTime;
    private Long lastCreateTime;

    public void setAddress(String address) {
        this.address = String.join("%",address.split("\\s|,") );
    }
}
