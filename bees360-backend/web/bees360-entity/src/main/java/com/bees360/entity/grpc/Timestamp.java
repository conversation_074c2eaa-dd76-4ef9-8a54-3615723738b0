package com.bees360.entity.grpc;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * 对应grpc google/protobuf/Timestamp
 *
 * <AUTHOR>
 */
@Data
public class Timestamp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // Represents seconds of UTC time since Unix epoch
    // 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to
    // 9999-12-31T23:59:59Z inclusive.
    private long seconds;

    // Non-negative fractions of a second at nanosecond resolution. Negative
    // second values with fractions must still have non-negative nanos values
    // that count forward in time. Must be from 0 to 999,999,999
    // inclusive.
    private int nanos;
}
