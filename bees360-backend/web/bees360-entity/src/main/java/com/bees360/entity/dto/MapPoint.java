package com.bees360.entity.dto;

public class MapPoint implements Cloneable{

	private double lat;

	private double lng;

	public MapPoint() {
		super();
	}
	public MapPoint(double lat, double lng) {
		super();
		this.lat = lat;
		this.lng = lng;
	}
	public double getLat() {
		return lat;
	}
	public void setLat(double lat) {
		this.lat = lat;
	}
	public double getLng() {
		return lng;
	}
	public void setLng(double lng) {
		this.lng = lng;
	}
	@Override
	public String toString() {
		return "MapPoint [lat=" + lat + ", lng=" + lng + "]";
	}
}
