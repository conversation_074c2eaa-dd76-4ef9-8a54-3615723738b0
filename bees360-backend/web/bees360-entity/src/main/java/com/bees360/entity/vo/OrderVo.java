package com.bees360.entity.vo;

import java.util.List;

public class OrderVo {

	private String orderId;
	//amount with tax
	private double totalFeeAmount;
	//amount without tax
	private double paidServiceFeeAmount;
	//tax
	private double tax;
	private double discountPercent;
	private String currency;
	private long createdTime;
	private int serviceFeeType;
	private String paymentMethod;
	private int paymentMethodId;
	private int discountType;
	private double totalDiscountMoney;

	//1:income,2:expense
	private int paymentType;

	private List<ItemVo> items;

	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	public double getPaidServiceFeeAmount() {
		return paidServiceFeeAmount;
	}
	public void setPaidServiceFeeAmount(double paidServiceFeeAmount) {
		this.paidServiceFeeAmount = paidServiceFeeAmount;
	}
	public double getTax() {
		return tax;
	}
	public void setTax(double tax) {
		this.tax = tax;
	}
	public double getTotalFeeAmount() {
		return totalFeeAmount;
	}
	public void setTotalFeeAmount(double totalFeeAmount) {
		this.totalFeeAmount = totalFeeAmount;
	}
	public int getServiceFeeType() {
		return serviceFeeType;
	}
	public void setServiceFeeType(int serviceFeeType) {
		this.serviceFeeType = serviceFeeType;
	}
	public double getDiscountPercent() {
		return discountPercent;
	}
	public void setDiscountPercent(double discountPercent) {
		this.discountPercent = discountPercent;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public long getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}
	public String getPaymentMethod() {
		return paymentMethod;
	}
	public void setPaymentMethod(String paymentMethod) {
		this.paymentMethod = paymentMethod;
	}
	public int getPaymentMethodId() {
		return paymentMethodId;
	}
	public void setPaymentMethodId(int paymentMethodId) {
		this.paymentMethodId = paymentMethodId;
	}

	public int getDiscountType() {
		return discountType;
	}
	public void setDiscountType(int discountType) {
		this.discountType = discountType;
	}
	public int getPaymentType() {
		return paymentType;
	}
	public void setPaymentType(int paymentType) {
		this.paymentType = paymentType;
	}

	public double getTotalDiscountMoney() {
		return totalDiscountMoney;
	}
	public void setTotalDiscountMoney(double totalDiscountMoney) {
		this.totalDiscountMoney = totalDiscountMoney;
	}
	public List<ItemVo> getItems() {
		return items;
	}
	public void setItems(List<ItemVo> items) {
		this.items = items;
	}

	@Override
	public String toString() {
		return "OrderVo [orderId=" + orderId + ", totalFeeAmount="
				+ totalFeeAmount + ", paidServiceFeeAmount=" + paidServiceFeeAmount + ", tax=" + tax
				+ ", discountPercent=" + discountPercent + ", currency=" + currency + ", createdTime=" + createdTime
				+ ", serviceFeeType=" + serviceFeeType + ", paymentMethod=" + paymentMethod + ", paymentMethodId="
				+ paymentMethodId + ", items=" + items;
	}
}
