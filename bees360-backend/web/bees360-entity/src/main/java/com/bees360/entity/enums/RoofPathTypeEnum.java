package com.bees360.entity.enums;

/**
 * <AUTHOR>
 * @date 2019/09/19 11:25
 */
public enum RoofPathTypeEnum implements BaseCodeEnum {
    // @formatter:off
    RIDGE(1, "Ridge"),
    HIP(2, "Hip"),
    VALLEY(3, "<PERSON>"),
    RAKE(4, "Rake"),
    <PERSON>VE(5, "Eave"),
    FLASHING(6, "Flashing")
    ;
    // @formatter:on

    private final int code;
    private final String display;

    RoofPathTypeEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    public static RoofPathTypeEnum getEnum(int code) {
        for(RoofPathTypeEnum type: values()) {
            if(code == type.getCode()) {
                return type;
            }
        }
        return null;
    }
}
