package com.bees360.entity.stat.converter;

import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.stat.dto.ProjectCardDataDto;
import com.bees360.entity.stat.dto.ProjectStatCardDto;
import com.bees360.entity.stat.enums.ProjectStatType;
import com.bees360.entity.stat.rule.CompanyStatRule;
import com.bees360.entity.stat.rule.CompanyStatRuleConfig;
import com.bees360.entity.stat.vo.ClassifiedItem;
import com.bees360.entity.stat.vo.card.ServiceTypeVo;
import com.bees360.entity.stat.vo.card.StatCardVo;
import com.bees360.entity.stat.vo.card.StatCreatedCardVo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

public class StatCardConverter {

    public static StatCreatedCardVo toCreatedCard(List<ProjectStatCardDto> dtos){

        final StatCreatedCardVo vo = new StatCreatedCardVo();
        vo.setCardName(ProjectStatType.PROJECT_CREATED.getDisplay());
        vo.setType(ProjectStatType.PROJECT_CREATED.getCode());

        AtomicInteger count = new AtomicInteger(0);
        final List<ServiceTypeVo> serviceTypeVos = dtos.stream()
            .filter(dto -> Objects.nonNull(dto.getServiceType()))
            .map(dto -> {
                count.addAndGet(dto.getCount());
                final Integer serviceType = dto.getServiceType();
                return new ServiceTypeVo(serviceType, ProjectServiceTypeEnum.getEnum(serviceType).getValue(), dto.getCount());
            })
                .sorted(Comparator.comparingInt(ServiceTypeVo::getServiceType).reversed())
            .collect(Collectors.toList());

        vo.setCount(count.get());
        vo.setServiceTypes(serviceTypeVos);

        return vo;
    }

    public static StatCardVo toIncompletedCard(List<ProjectCardDataDto> dtos){
        final StatCardVo vo = new StatCardVo();
        vo.setCardName(ProjectStatType.PROJECT_INCOMPLETED.getDisplay());
        vo.setType(ProjectStatType.PROJECT_INCOMPLETED.getCode());

        vo.setCount(dtos.size());

        final List<ServiceTypeVo> serviceTypeVos = dtos.stream()
            .collect(Collectors.groupingBy(ProjectCardDataDto::getServiceType))
            .entrySet().stream()
            .map(entry -> new ServiceTypeVo(entry.getKey(), ProjectServiceTypeEnum.getEnum(entry.getKey()).getValue(), entry.getValue().size()))
            .sorted(Comparator.comparingInt(ServiceTypeVo::getServiceType).reversed())
            .collect(Collectors.toList());
        vo.setServiceTypes(serviceTypeVos);

        LongAdder dueSoon = new LongAdder();
        LongAdder overdue = new LongAdder();
        dtos.stream().parallel().forEach(dto->{
            final Integer daysOld = dto.getDaysOld();
            final Integer projectStatus = dto.getProjectStatus();

            CompanyStatRule statRule = CompanyStatRuleConfig.getStatRule(dto.getInsuranceCompanyName(), dto.getServiceType());
            if (statRule.isDueSoon(daysOld)){
                dueSoon.increment();
            }
            if (statRule.isOverdue(daysOld)){
                overdue.increment();
            }
        });
        List<ClassifiedItem> items = new ArrayList<>();
        items.add(new ClassifiedItem("Due Soon", dueSoon.intValue()));
        items.add(new ClassifiedItem("Overdue", overdue.intValue()));
        vo.setItems(items);

        return vo;
    }

    public static StatCardVo toCompletedCard(List<ProjectStatCardDto> dtos){

        final StatCardVo vo = new StatCardVo();
        vo.setCardName(ProjectStatType.PROJECT_COMPLETED.getDisplay());
        vo.setType(ProjectStatType.PROJECT_COMPLETED.getCode());

        AtomicInteger count = new AtomicInteger(0);
        final List<ServiceTypeVo> serviceTypeVos = dtos.stream()
            .filter(dto -> dto.getServiceType() != null)
            .map(dto -> {
                count.addAndGet(dto.getCount());
                final Integer serviceType = dto.getServiceType();
                return new ServiceTypeVo(serviceType, ProjectServiceTypeEnum.getEnum(serviceType).getValue(), dto.getCount());
            })
            .sorted(Comparator.comparingInt(ServiceTypeVo::getServiceType).reversed())
            .collect(Collectors.toList());

        vo.setCount(count.get());
        vo.setServiceTypes(serviceTypeVos);

        return vo;
    }

    public static StatCardVo toReturnedCard(List<ProjectStatCardDto> dtos) {
        final var vo = new StatCardVo();
        vo.setCardName(ProjectStatType.PROJECT_RETURNED.getDisplay());
        vo.setType(ProjectStatType.PROJECT_RETURNED.getCode());

        var count = new AtomicInteger(0);
        var serviceTypeVos = dtos.stream()
            .filter(dto -> dto.getServiceType() != null)
            .map(dto -> {
                count.addAndGet(dto.getCount());
                var serviceType = dto.getServiceType();
                return new ServiceTypeVo(serviceType, ProjectServiceTypeEnum.getEnum(serviceType).getValue(), dto.getCount());
            })
            .sorted(Comparator.comparingInt(ServiceTypeVo::getServiceType).reversed())
            .collect(Collectors.toList());

        vo.setCount(count.get());
        vo.setServiceTypes(serviceTypeVos);
        return vo;
    }

    public static StatCardVo toRiskScoreCard(List<ProjectCardDataDto> dtos){

        final StatCardVo vo = new StatCardVo();
        vo.setCardName(ProjectStatType.RISK_SCORE.getDisplay());
        vo.setType(ProjectStatType.RISK_SCORE.getCode());

        vo.setScore(mediumScore(dtos));

        final List<ServiceTypeVo> serviceTypeVos = dtos.stream()
            .collect(Collectors.groupingBy(ProjectCardDataDto::getServiceType))
            .entrySet().stream()
            .map(dto -> {
                final Integer serviceType = dto.getKey();
                final List<ProjectCardDataDto> cardData = dto.getValue();
                return new ServiceTypeVo(serviceType, ProjectServiceTypeEnum.getEnum(serviceType).getValue(), cardData.size(), mediumScore(cardData));
            })
            .sorted(Comparator.comparingInt(ServiceTypeVo::getServiceType).reversed())
            .collect(Collectors.toList());

        vo.setServiceTypes(serviceTypeVos);

        return vo;
    }

    public static StatCardVo toCloseoutCard(List<ProjectStatCardDto> dtos) {

        final StatCardVo vo = new StatCardVo();
        vo.setCardName(ProjectStatType.PROJECT_CLOSE_OUT.getDisplay());
        vo.setType(ProjectStatType.PROJECT_CLOSE_OUT.getCode());

        AtomicInteger count = new AtomicInteger(0);
        final List<ServiceTypeVo> serviceTypeVos = dtos.stream()
            .filter(dto -> dto.getServiceType() != null)
            .map(dto -> {
                count.addAndGet(dto.getCount());
                final Integer serviceType = dto.getServiceType();
                return new ServiceTypeVo(serviceType, ProjectServiceTypeEnum.getEnum(serviceType).getValue(), dto.getCount());
            })
            .sorted(Comparator.comparingInt(ServiceTypeVo::getServiceType).reversed())
            .collect(Collectors.toList());

        vo.setCount(count.get());
        vo.setServiceTypes(serviceTypeVos);

        return vo;
    }

    private static Double mediumScore(List<ProjectCardDataDto> dtos){
        if (dtos == null || dtos.isEmpty()){
            return 0D;
        }
        List<ProjectCardDataDto> sortedDto = dtos.stream().filter(projectCardDataDto -> Objects.nonNull(projectCardDataDto.getScore()))
            .sorted(Comparator.comparingDouble(p -> p.getScore().doubleValue())).collect(Collectors.toList());
        int size = sortedDto.size();
        int mid = (size - 1) / 2;
        if (size % 2 == 0){
            return sortedDto.get(mid).getScore().add(sortedDto.get(mid + 1).getScore()).divide(new BigDecimal(2), RoundingMode.FLOOR).doubleValue();
        }
        return sortedDto.get(mid).getScore().doubleValue();
    }
}
