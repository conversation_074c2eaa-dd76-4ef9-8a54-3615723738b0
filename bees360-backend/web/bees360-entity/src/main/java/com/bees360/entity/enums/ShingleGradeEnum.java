package com.bees360.entity.enums;

public enum ShingleGradeEnum implements BaseCodeEnum{

	REGULAR(0, "Regular"),
	GOLD(1, "Gold"),
	PREMIUM(2, "Premium");

	private final int code;
	private final String display;
	ShingleGradeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public static ShingleGradeEnum getEnum(int code){
		ShingleGradeEnum[] shingleGradeTypes = ShingleGradeEnum.values();
		for(ShingleGradeEnum shingleGradeType: shingleGradeTypes){
			if(shingleGradeType.code == code){
				return shingleGradeType;
			}
		}
		return null;
	}

}
