package com.bees360.entity.dto;

import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.vo.ProjectStatusVo;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/01/06 18:31
 */
public class ProjectStatusTimeLineDto {

    private long projectId;

    private final TreeMap<NewProjectStatusEnum, ProjectStatusVo> timeline;

    private final Map<NewProjectStatusEnum, Integer> statusCount;

    private ProjectStatusVo firstReturnToClient;

    private ProjectStatusVo lastReturnToClient;

    private ProjectStatusVo lastClientReceived;

    public ProjectStatusTimeLineDto() {
        timeline = new TreeMap<>(Comparator.comparingInt(NewProjectStatusEnum::getOrder));
        statusCount = new HashMap<>();
        initTimeline();
    }

    public ProjectStatusTimeLineDto(long projectId) {
        this();
        this.projectId = projectId;
    }

    public ProjectStatusTimeLineDto(long projectId, List<ProjectStatusVo> statues) {
        this(projectId);
        addStatusVos(statues);
    }

    private void initTimeline(){
        Stream.of(NewProjectStatusEnum.values())
            .forEach(e -> addStatusVo(new ProjectStatusVo(e)));
    }

    private NewProjectStatusEnum getEnum(ProjectStatusVo statusVo){
        if (statusVo == null || statusVo.getStatus() == null){
            return null;
        }
        return NewProjectStatusEnum.getEnum(statusVo.getStatus().getCode());
    }

    public ProjectStatusVo getStatusVo(NewProjectStatusEnum projectStatusEnum){

        if (projectStatusEnum == null){
            return null;
        }

        return timeline.get(projectStatusEnum);
    }

    public List<ProjectStatusVo> toListShowInTimeline(){
        return new ArrayList<>(timeline.keySet()).stream()
            .filter(NewProjectStatusEnum::showInTimeline)
            .map(timeline::get).collect(Collectors.toList());
    }

    public List<ProjectStatusVo> toStatusList(){

        return new ArrayList<>(timeline.values());
    }

    public ProjectStatusVo getFirstAvailable(){

        return timeline.values().stream().filter(ProjectStatusVo::available).findFirst().orElse(null);
    }

    public ProjectStatusVo getLastAvailable(){

        return timeline.descendingMap().values().stream().filter(ProjectStatusVo::available).findFirst().orElse(null);
    }

    /**
     * statusVo.status.code 相同则会覆盖
     * @param statusVo
     */
    public void addStatusVo(final ProjectStatusVo statusVo){

        final NewProjectStatusEnum statusEnum = getEnum(statusVo);
        if (statusEnum == null){
            return;
        }

        if (statusEnum == NewProjectStatusEnum.RECEIVE_ERROR || statusEnum == NewProjectStatusEnum.CLIENT_RECEIVED){
            handleReceiveStatus(statusVo);
            return;
        }

        timeline.put(statusEnum, statusVo);
    }

    /**
     * 传入一个comparator，比较并插入比较值更小的一方个statusVo
     */
    public void addStatusVo(final ProjectStatusVo statusVo, Comparator<? super ProjectStatusVo> comparator){

        final NewProjectStatusEnum statusEnum = getEnum(statusVo);
        if (statusEnum == null){
            return;
        }

        final ProjectStatusVo current = timeline.get(statusEnum);

        if (current == null || !current.available()) {
            addStatusVo(statusVo);
            return;
        }
        if (comparator.compare(current, statusVo) > 0){
            addStatusVo(statusVo);
        }
    }

    // 统计状态的触发次数
    public void countStatusVo(ProjectStatusVo statusVo) {
        var statusEnum = getEnum(statusVo);
        Optional.ofNullable(statusEnum)
                .ifPresent(s -> {
                    statusCount.putIfAbsent(s, 0);
                    statusCount.computeIfPresent(s, (k, v) -> v + 1 );
                });
    }

    public Integer getStatusCount(NewProjectStatusEnum status) {
        return statusCount.getOrDefault(status, 0);
    }

    private void handleReceiveStatus(ProjectStatusVo statusVo){

        final ProjectStatusVo receiveError = timeline.get(NewProjectStatusEnum.RECEIVE_ERROR);
        final ProjectStatusVo clientReceived = timeline.get(NewProjectStatusEnum.CLIENT_RECEIVED);

        ProjectStatusVo current =
            receiveError == null || (receiveError.getCreatedTime() == null && clientReceived != null) ? clientReceived : receiveError;

        final NewProjectStatusEnum statusEnum = getEnum(statusVo);
        if (current == null){
            timeline.put(statusEnum, statusVo);
            return;
        }
        // client received 优先
        if (current.getCreatedTime() == null && statusEnum == NewProjectStatusEnum.CLIENT_RECEIVED){
            timeline.put(statusEnum, statusVo);
            return;
        }
        if (current.getCreatedTime() == null && statusVo.getCreatedTime() != null){
            timeline.put(statusEnum, statusVo);
            return;
        }
        if (current.getCreatedTime() != null && statusVo.getCreatedTime() != null){
            if (current.getCreatedTime() > statusVo.getCreatedTime()){
                return;
            }
            timeline.remove(getEnum(current));
            timeline.put(statusEnum, statusVo);
        }
    }

    public long getProjectId() {
        return projectId;
    }

    public void addStatusVos(Collection<ProjectStatusVo> statusVos){
        statusVos.forEach(this::addStatusVo);
    }

    public ProjectStatusVo getSiteInspected(){
        return getStatusVo(NewProjectStatusEnum.SITE_INSPECTED);
    }

    public ProjectStatusVo getCustomerContacted(){
        return getStatusVo(NewProjectStatusEnum.CUSTOMER_CONTACTED);
    }

    public ProjectStatusVo getProjectCreated(){
        return getStatusVo(NewProjectStatusEnum.PROJECT_CREATED);
    }

    public ProjectStatusVo getAssignedToPilot(){
        return getStatusVo(NewProjectStatusEnum.ASSIGNED_TO_PILOT);
    }

    public ProjectStatusVo getReturnToClient(){
        return getStatusVo(NewProjectStatusEnum.RETURNED_TO_CLIENT);
    }

    public ProjectStatusVo getClientReceived() {
        return getStatusVo(NewProjectStatusEnum.CLIENT_RECEIVED);
    }

    public ProjectStatusVo getImageUploaded() {
        return getStatusVo(NewProjectStatusEnum.IMAGE_UPLOADED);
    }

    public ProjectStatusVo getIBEESUploaded() {
        return getStatusVo(NewProjectStatusEnum.IBEES_UPLOADED);
    }

    public ProjectStatusVo getFirstReturnToClient() {
        return firstReturnToClient;
    }

    public void setFirstReturnToClient(ProjectStatusVo firstReturnToClient) {
        this.firstReturnToClient = firstReturnToClient;
    }

    public ProjectStatusVo getLastReturnToClient() {
        return lastReturnToClient;
    }

    public void setLastReturnToClient(ProjectStatusVo lastReturnToClient) {
        this.lastReturnToClient = lastReturnToClient;
    }

    public ProjectStatusVo getLastClientReceived() {
        return lastClientReceived;
    }

    public void setLastClientReceived(ProjectStatusVo lastClientReceived) {
        this.lastClientReceived = lastClientReceived;
    }


}
