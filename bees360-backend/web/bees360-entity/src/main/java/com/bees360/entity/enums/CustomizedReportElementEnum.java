package com.bees360.entity.enums;

import java.util.ArrayList;
import java.util.List;

public enum CustomizedReportElementEnum implements BaseCodeEnum {
	CATEGORY(0, "Category", "10"),
	SUB_CATEGORY(1, "Subcategory", "40"),
	DESCRIPTION(2, "Description", "120");

	private final int code;
	private final String display;
	private final String defaultValue;

	CustomizedReportElementEnum(int code, String display, String defaultValue){
		this.code = code;
		this.display = display;
		this.defaultValue = defaultValue;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public String getDefaultValue() {
		return defaultValue;
	}

	public static List<CustomizedReportElementEnum> getEnums() {
		List<CustomizedReportElementEnum> elementEnumList = new ArrayList<>();
		elementEnumList.add(CATEGORY);
		elementEnumList.add(SUB_CATEGORY);
		elementEnumList.add(DESCRIPTION);
		return elementEnumList;
	}

}
