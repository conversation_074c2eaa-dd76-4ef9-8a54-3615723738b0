package com.bees360.entity.dto;

import com.bees360.entity.enums.DiscountOffTypeEnum;
import com.bees360.entity.enums.productandpayment.GlobalDiscountTypeEnum;

/**
 * <AUTHOR>
 * @date 2019/10/09
 */
public class Discount {
    /** @see GlobalDiscountTypeEnum#getCode() **/
    private int discountType;
    private String discountName;
    /** @see DiscountOffTypeEnum#getCode() */
    private int offType;
    /**
     * 当 offType = {@link DiscountOffTypeEnum#AMOUNT_OFF} 时取该值，表示按照金额折扣
     */
    private double amountOff;
    /**
     * 百分比值，如25%，则表示为0.25
     * 当 offType = {@link DiscountOffTypeEnum#AMOUNT_OFF} 时取该值，表示按照金额折扣
     */
    private double percentageOff;

    public int getDiscountType() {
        return discountType;
    }

    public void setDiscountType(int discountType) {
        this.discountType = discountType;
    }

    public String getDiscountName() {
        return discountName;
    }

    public void setDiscountName(String discountName) {
        this.discountName = discountName;
    }

    public int getOffType() {
        return offType;
    }

    public void setOffType(int offType) {
        this.offType = offType;
    }

    public void setOffType(DiscountOffTypeEnum offType, double value) {
        this.offType = offType.getCode();
        if(offType == DiscountOffTypeEnum.PERCENTAGE_OFF) {
            this.setPercentageOff(value);
            this.setAmountOff(0);
        } else if (offType == DiscountOffTypeEnum.AMOUNT_OFF) {
            this.setAmountOff(value);
            this.setPercentageOff(0);
        }
    }

    public double getAmountOff() {
        return amountOff;
    }

    public void setAmountOff(double amountOff) {
        this.amountOff = amountOff;
    }

    public double getPercentageOff() {
        return percentageOff;
    }

    public void setPercentageOff(double percentageOff) {
        this.percentageOff = percentageOff;
    }

    @Override
    public String toString() {
        return "Discount{" + "discountType=" + discountType + ", discountName='" + discountName + '\'' + ", offType="
            + offType + ", amountOff=" + amountOff + ", percentageOff=" + percentageOff + '}';
    }
}
