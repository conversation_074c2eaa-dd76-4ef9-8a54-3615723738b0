package com.bees360.entity.enums;

import java.util.Objects;
import java.util.function.Predicate;

/**
 * 调查问卷附件
 */
public enum MissionAttachFileTypeEnum implements BaseCodeEnum {
    SIGNATURE_INSURED(1, "SignatureInsured"),      //户主签名

    ;

    private final int code;
    private final String display;

    MissionAttachFileTypeEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    @Override
    public int getCode() {
        return code;
    }

    public static MissionAttachFileTypeEnum valueOf(int code) {
        return valueOf(value -> Objects.equals(value.getCode(), code));
    }

    public static MissionAttachFileTypeEnum valueOfDisplay(String display) {
        return valueOf(value -> Objects.equals(value.getDisplay(), display));
    }

    public static MissionAttachFileTypeEnum valueOf(
        Predicate<MissionAttachFileTypeEnum> predicate) {
        for (MissionAttachFileTypeEnum value : values()) {
            if (predicate.test(value)) {
                return value;
            }
        }
        return null;
    }
}
