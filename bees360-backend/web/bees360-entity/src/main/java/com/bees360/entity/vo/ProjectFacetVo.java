package com.bees360.entity.vo;

import java.io.Serial;
import java.io.Serializable;

/**
 * ProjectFacetVo
 * <AUTHOR>
 *
 */
public class ProjectFacetVo implements Serializable {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = -5700852716436136800L;

	//The identifier of the facet.
	private long facetId;
	private String name;
	private double area;
	private double pitch;
	private Integer damagePercent;

	public ProjectFacetVo() {
		super();
	}

	public ProjectFacetVo(long facetId, long projectId) {
		super();
		this.facetId = facetId;
	}

	public ProjectFacetVo(long facetId, String name, double area, double pitch, Integer damagePercent) {
		super();
		this.facetId = facetId;
		this.name = name;
		this.area = area;
		this.pitch = pitch;
		this.damagePercent = damagePercent;
	}

	public long getFacetId() {
		return facetId;
	}

	public void setFacetId(long facetId) {
		this.facetId = facetId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public double getArea() {
		return area;
	}

	public void setArea(double area) {
		this.area = area;
	}

	public double getPitch() {
		return pitch;
	}

	public void setPitch(double pitch) {
		this.pitch = pitch;
	}

	public Integer getDamagePercent() {
		return damagePercent;
	}

	public void setDamagePercent(Integer damagePercent) {
		this.damagePercent = damagePercent;
	}

	@Override
	public String toString() {
		return "ProjectFacetVo [facetId=" + facetId + ", name=" + name + ", area=" + area + ", pitch=" + pitch
				+ ", damagePercent=" + damagePercent + "]";
	}

}
