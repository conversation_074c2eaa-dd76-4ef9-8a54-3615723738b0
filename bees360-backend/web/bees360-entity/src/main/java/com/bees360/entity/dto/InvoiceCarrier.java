package com.bees360.entity.dto;

import com.bees360.entity.enums.productandpayment.PaymentMethodEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/12
 */
public class InvoiceCarrier {
    private List<ProductSaleItem> saleItems;
    private Discount discount;
    /** @see PaymentMethodEnum#getPaymentMethodId() **/
    private int paymentMethod;

    public List<ProductSaleItem> getSaleItems() {
        return saleItems;
    }

    public void setSaleItems(List<ProductSaleItem> saleItems) {
        this.saleItems = saleItems;
    }

    public Discount getDiscount() {
        return discount;
    }

    public void setDiscount(Discount discount) {
        this.discount = discount;
    }

    public int getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(int paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    @Override
    public String toString() {
        return "InvoiceCarrier{" + "saleItems=" + saleItems + ", discount=" + discount + ", paymentMethod="
            + paymentMethod + '}';
    }
}
