package com.bees360.entity.vo;

public class ReportTemplateVo {

	private long id;

	private long companyId;

	private long materialId;

	private String reportTypes;

	// The report page where the material is located.
	private int pageNumber;

	// Type of each page. 1:single page, 2: Multiple page, 3:default page
	private int pageType;

	// Sorting of materials.
	private int sort;

	private Integer version;

	private Long createTime;

	private Integer isDeleted;

    // The material type of report template,
	// it must be assigned a value through the Enum ReportMaterialTypeEnum.
	private int materialType;

	// The position x
	private double pointX;

	// The position y
	private double pointY;

	// Define the source of the data.
	private String dataSource;

	// Whether an expression needs to be displayed.
	private String checkIsImplement;

	// Whether to display damage type.
	private boolean isShowDamageType;

	// Font size or picture size.
	private int size;

	// Whether the font is bold or not.
	private boolean isBold;

	// Font color.
	private String color;

	// Font alignment. 0:left 1:center 2:right
	private int align;

	// The url of material.
	private String materialUrl;

	// The width of image.
	private int width;

	// The height of image.
	private int height;

	// The number of material.
	private int materialNum;

	public ReportTemplateVo() {
		super();
	}

	public ReportTemplateVo(long id, long companyId, long materialId, String reportTypes, int pageNumber, int pageType,
			int sort, int materialType, double pointX, double pointY, String dataSource, String checkIsImplement,
			boolean isShowDamageType, int size, boolean isBold, String color, int align, String materialUrl, int width,
			int height, int materialNum) {
		super();
		this.id = id;
		this.companyId = companyId;
		this.materialId = materialId;
		this.reportTypes = reportTypes;
		this.pageNumber = pageNumber;
		this.pageType = pageType;
		this.sort = sort;
		this.materialType = materialType;
		this.pointX = pointX;
		this.pointY = pointY;
		this.dataSource = dataSource;
		this.checkIsImplement = checkIsImplement;
		this.isShowDamageType = isShowDamageType;
		this.size = size;
		this.isBold = isBold;
		this.color = color;
		this.align = align;
		this.materialUrl = materialUrl;
		this.width = width;
		this.height = height;
		this.materialNum = materialNum;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(long companyId) {
		this.companyId = companyId;
	}

	public long getMaterialId() {
		return materialId;
	}

	public void setMaterialId(long materialId) {
		this.materialId = materialId;
	}

	public String getReportTypes() {
		return reportTypes;
	}

	public void setReportTypes(String reportTypes) {
		this.reportTypes = reportTypes;
	}

	public int getPageNumber() {
		return pageNumber;
	}

	public void setPageNumber(int pageNumber) {
		this.pageNumber = pageNumber;
	}

	public int getPageType() {
		return pageType;
	}

	public void setPageType(int pageType) {
		this.pageType = pageType;
	}

	public int getMaterialType() {
		return materialType;
	}

	public void setMaterialType(int materialType) {
		this.materialType = materialType;
	}

	public double getPointX() {
		return pointX;
	}

	public void setPointX(double pointX) {
		this.pointX = pointX;
	}

	public double getPointY() {
		return pointY;
	}

	public void setPointY(double pointY) {
		this.pointY = pointY;
	}

	public void setPointY(int pointY) {
		this.pointY = pointY;
	}

	public String getDataSource() {
		return dataSource;
	}

	public void setDataSource(String dataSource) {
		this.dataSource = dataSource;
	}

	public boolean getIsShowDamageType() {
		return isShowDamageType;
	}

	public void setIsShowDamageType(boolean isShowDamageType) {
		this.isShowDamageType = isShowDamageType;
	}

	public int getSize() {
		return size;
	}

	public void setSize(int size) {
		this.size = size;
	}

	public boolean getIsBold() {
		return isBold;
	}

	public void setIsBold(boolean isBold) {
		this.isBold = isBold;
	}

	public String getColor() {
		return color;
	}

	public void setColor(String color) {
		this.color = color;
	}

	public int getAlign() {
		return align;
	}

	public void setAlign(int align) {
		this.align = align;
	}

	public String getMaterialUrl() {
		return materialUrl;
	}

	public void setMaterialUrl(String materialUrl) {
		this.materialUrl = materialUrl;
	}

	public int getWidth() {
		return width;
	}

	public void setWidth(int width) {
		this.width = width;
	}

	public int getHeight() {
		return height;
	}

	public void setHeight(int height) {
		this.height = height;
	}

	public int getMaterialNum() {
		return materialNum;
	}

	public void setMaterialNum(int materialNum) {
		this.materialNum = materialNum;
	}

	public int getSort() {
		return sort;
	}

	public void setSort(int sort) {
		this.sort = sort;
	}

	public String getCheckIsImplement() {
		return checkIsImplement;
	}

	public void setCheckIsImplement(String checkIsImplement) {
		this.checkIsImplement = checkIsImplement;
	}

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}
