package com.bees360.entity;

import com.bees360.entity.enums.InvoiceStatusEnum;
import com.bees360.entity.enums.productandpayment.PaymentMethodEnum;

/**
 * <AUTHOR> Guanrong
 * @date 2019/09/27 16:51
 */
public class Invoice {
    private long invoiceId;
    private long userId;
    private long companyId;
    private long projectId;

    private String invoiceTitle;
    private String description;
    /** {@link InvoiceStatusEnum#getCode()} **/
    private int status;

    // ===============================
    // 支付信息
    /**
     * 应付金额 amountDue = subtotal + taxAmount;
     */
    private double amountDue;
    /** subtotal = SUM(item.qty * item.price) - discountAmount; **/
    private double subtotal;

    private double taxAmount;
    private double discountAmount;

    /** {@link PaymentMethodEnum#getDisplay()} **/
    private String paymentMethod;

    // ===============================
    // 客户信息

    private String customerName;
    private String customerAddress;
    private String customerCity;
    private String customerState;
    private String customerZipCode;
    private String customerEmail;
    private String customerPhone;
    private String customerCompany;

    // ===============================
    // 时间信息
    private long createdTime;
    private long paidTime;
    private long dueTime;

    // ===============================
    // 其他信息
    private boolean isDeleted;

    // ===============================
    // Getter & Setter

    public long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(long companyId) {
        this.companyId = companyId;
    }

    public long getProjectId() {
        return projectId;
    }

    public void setProjectId(long projectId) {
        this.projectId = projectId;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public double getAmountDue() {
        return amountDue;
    }

    public void setAmountDue(double amountDue) {
        this.amountDue = amountDue;
    }

    public double getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(double subtotal) {
        this.subtotal = subtotal;
    }

    public double getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(double taxAmount) {
        this.taxAmount = taxAmount;
    }

    public double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerAddress() {
        return customerAddress;
    }

    public void setCustomerAddress(String customerAddress) {
        this.customerAddress = customerAddress;
    }

    public String getCustomerCity() {
        return customerCity;
    }

    public void setCustomerCity(String customerCity) {
        this.customerCity = customerCity;
    }

    public String getCustomerState() {
        return customerState;
    }

    public void setCustomerState(String customerState) {
        this.customerState = customerState;
    }

    public String getCustomerZipCode() {
        return customerZipCode;
    }

    public void setCustomerZipCode(String customerZipCode) {
        this.customerZipCode = customerZipCode;
    }

    public String getCustomerEmail() {
        return customerEmail;
    }

    public void setCustomerEmail(String customerEmail) {
        this.customerEmail = customerEmail;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public String getCustomerCompany() {
        return customerCompany;
    }

    public void setCustomerCompany(String customerCompany) {
        this.customerCompany = customerCompany;
    }

    public long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(long createdTime) {
        this.createdTime = createdTime;
    }

    public long getPaidTime() {
        return paidTime;
    }

    public void setPaidTime(long paidTime) {
        this.paidTime = paidTime;
    }

    public long getDueTime() {
        return dueTime;
    }

    public void setDueTime(long dueTime) {
        this.dueTime = dueTime;
    }

    public boolean isDeleted() {
        return isDeleted;
    }

    public void setDeleted(boolean deleted) {
        isDeleted = deleted;
    }

    @Override
    public String toString() {
        return "Invoice{" + "invoiceId=" + invoiceId + ", userId=" + userId + ", companyId=" + companyId
            + ", projectId=" + projectId + ", invoiceTitle='" + invoiceTitle + '\'' + ", description='" + description
            + '\'' + ", status=" + status + ", amountDue=" + amountDue + ", subtotal=" + subtotal + ", taxAmount="
            + taxAmount + ", discountAmount=" + discountAmount + ", paymentMethod='" + paymentMethod + '\''
            + ", customerAddress='" + customerAddress + '\'' + ", customerCity='" + customerCity + '\''
            + ", customerState='" + customerState + '\'' + ", customerZipCode='" + customerZipCode + '\''
            + ", customerEmail='" + customerEmail + '\'' + ", customerName='" + customerName + '\''
            + ", customerPhone='" + customerPhone + '\'' + ", customerCompany='" + customerCompany + '\''
            + ", createdTime=" + createdTime + ", paidTime=" + paidTime + ", dueTime=" + dueTime + ", isDeleted="
            + isDeleted + '}';
    }
}
