package com.bees360.entity.vo;

import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.enums.BaseCodeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class AIProcessProjectItem {
	private Long companyId;
	private long projectId;
	private long startTime;
	private AIStep aiStep;
	private String processor;

	/* getter and setter */
	public long getProjectId() {
		return projectId;
	}
	public Long getCompanyId() {
		return companyId;
	}
	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public long getStartTime() {
		return startTime;
	}
	public void setStartTime(long startTime) {
		this.startTime = startTime;
	}
	@JsonIgnore
	public AIStep getAIStep() {
		return aiStep;
	}
	public IdNameDto getStage() {
		return aiStep == null? null: new IdNameDto(aiStep.getCode(), aiStep.getDisplay());
	}
	public void setAIStep(AIStep aiStep) {
		this.aiStep = aiStep;
	}
	public String getProcessor() {
		return processor;
	}
	public void setProcessor(String processor) {
		this.processor = processor;
	}
}
