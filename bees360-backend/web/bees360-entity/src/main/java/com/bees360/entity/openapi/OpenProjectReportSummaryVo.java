package com.bees360.entity.openapi;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OpenProjectReportSummaryVo {
    private long id;
    private String inspectionNumber;
    private String serviceName;
    private String insuredName;
    /**
     * 秒级别的时间戳
     */
    private Integer inspectionTime;
    /**
     * 秒级别的时间戳
     */
    private Integer completionTime;

    private OpenProjectAddressVo address;

    private String policyNumber;

    private List<String> supplementalService;

    private Boolean isRenewal;
}
