package com.bees360.entity.stat.vo.chart;

import com.bees360.entity.stat.dto.ProjectStatChartDto;
import com.bees360.entity.vo.AddressVo;
import com.bees360.entity.vo.ProjectTinyVo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class StatMapVo {

    private String name;
    private List<Double> value;
    private String thumbnail;
    private AddressVo address;

    private static AddressVo buildAddress(ProjectStatChartDto dto){

        final AddressVo addressVo = new AddressVo();
        addressVo.setAddress(dto.getAddress());
        addressVo.setCity(dto.getCity());
        addressVo.setState(dto.getState());
        addressVo.setCountry(dto.getCountry());
        addressVo.setZipCode(dto.getZipCode());
        addressVo.setLat(dto.getGpsLocationLatitude());
        addressVo.setLng(dto.getGpsLocationLongitude());

        return addressVo;
    }
    private static AddressVo buildAddress(ProjectTinyVo dto){

        final AddressVo addressVo = new AddressVo();
        addressVo.setAddress(dto.getAddress());
        addressVo.setCity(dto.getCity());
        addressVo.setState(dto.getState());
        addressVo.setCountry(dto.getCountry());
        addressVo.setZipCode(dto.getZipCode());
        addressVo.setLat(dto.getGpsLocationLatitude());
        addressVo.setLng(dto.getGpsLocationLongitude());

        return addressVo;
    }
    public static List<StatMapVo> buildMapVo(List<ProjectStatChartDto> chartDtos){
        return chartDtos.stream().map(chartDto -> {

            StatMapVo mapPoint = new StatMapVo();
            mapPoint.setName(chartDto.getState());
            mapPoint.setAddress(buildAddress(chartDto));

            List<Double> lngLat = new ArrayList<>();
            lngLat.add(chartDto.getGpsLocationLongitude());
            lngLat.add(chartDto.getGpsLocationLatitude());

            mapPoint.setValue(lngLat);

            return mapPoint;
        }).collect(Collectors.toList());

    }
    public static List<StatMapVo> buildMapVoFrom(List<ProjectTinyVo> chartDtos){
        return chartDtos.stream().map(chartDto -> {

            StatMapVo mapPoint = new StatMapVo();
            mapPoint.setName(chartDto.getState());
            mapPoint.setAddress(buildAddress(chartDto));

            List<Double> lngLat = new ArrayList<>();
            lngLat.add(chartDto.getGpsLocationLongitude());
            lngLat.add(chartDto.getGpsLocationLatitude());

            mapPoint.setValue(lngLat);

            return mapPoint;
        }).collect(Collectors.toList());

    }
}
