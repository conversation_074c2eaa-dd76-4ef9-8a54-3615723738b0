package com.bees360.entity;

import lombok.Data;

@Data
public class Adjuster {
	private long id;
    private long userId;
    private long aiRosterId;
    private long rosterId;

    /**
     * license合法的州列表
     */
    private String activeLicenseState;

    /**
     * The certifications that the adjuster currently have
     */
    private String certifications;

    /**
     * 场地经验
     * 包括:1~3 years/3~5 years/5~10 years/10+ years
     *
     */
    private String fieldExperience;

    /**
     * 办公室经验
     * 包括:1~3 years/3~5 years/5~10 years/10+ years
     *
     */
    private String deskExperience;

    /**
     * 商业经验
     * 包括:1~3 years/3~5 years/5~10 years/10+ years
     *
     */
    private String commercialExperience;

    /**
     * xactimate平台经验 可以为空
     */
    private String xactimatePlatformExperience;

    /**
     * symbility 平台经验可以为空
     */
    private String symbilityPlatformExperience;

    /**
     * 其它平台经验 可以为空
     */
    private String otherPlatformExperience;

    /**
     * 文件名
     */
    private String licenseFiles = "";

    // unused field, given default value

    private String operatingCityState = "";
    private String additionalOperatingTerritories = "";
    private String designatedHomeStateLicense = "";
    private String additionalLicense = "";
    private Integer yearsOfExperience;
    private boolean moreThan100MilesTraveled;
    private boolean catEventDeployed;
}
