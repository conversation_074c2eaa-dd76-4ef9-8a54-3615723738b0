package com.bees360.entity.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 飞手审核状态
 */
public enum PilotReviewStatusEnum {
    SUBMITTED(1, "Submitted"),
    REJECTED(2, "Rejected"),
    APPROVED(3, "Approved"),

    ;


    private Integer code;
    private String status;

    /**
     * 审核失败原因-邮箱和电话号码不匹配
     */
    public static final String REJECTED_REASON_EMAIL_PHONE_NOT_MATCH = "The user has been registered and the email account number is inconsistent, please correct and resubmit.";

    /**
     * 审核失败原因-审核人审核不通过
     */
    public static final String REJECTED_REASON_REVIEWER_REFUSE = "Sorry, the pilot's application was not approved.";

    /**
     * 审核失败原因-资料不全
     */
    public static final String INCOMPLETE_INFORMATION = "Incomplete information";

    public static String getRejectMessage(String message) {
        return StringUtils.isEmpty(message) ? REJECTED_REASON_REVIEWER_REFUSE : message;
    }

    PilotReviewStatusEnum(Integer code, String status) {
        this.code = code;
        this.status = status;
    }

    public Integer getCode() {
        return code;
    }

    public String getStatus() {
        return status;
    }

}
