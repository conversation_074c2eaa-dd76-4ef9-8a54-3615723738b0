package com.bees360.entity;

import java.io.Serializable;

import com.bees360.entity.enums.CommentTypeEnum;

@SuppressWarnings("serial")
public class Comment implements Serializable {
	private long chatId;
	// The identifier of project
	private long projectId;
	// The content of the comment.
	private String content;
	// The identifier of user who makes this comment.
	private long userId;
	// The timestamp of the comment
	private long createdTime;
	/*
	 * The type of this comment:
	 * 0: a text comment;
	 * 1: an image, in this case, the content is a directory.
	 * 2: pdf file, and in this case, the content is a directory.
	 */
	private CommentTypeEnum type;

	public Comment() {
		super();
	}
	public Comment(long chatId) {
		super();
		this.chatId = chatId;
	}
	public long getChatId() {
		return chatId;
	}
	public void setChatId(long chatId) {
		this.chatId = chatId;
	}
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public long getUserId() {
		return userId;
	}
	public void setUserId(long userId) {
		this.userId = userId;
	}
	public long getTimestamp() {
		return createdTime;
	}
	public void setTimestamp(long createdTime) {
		this.createdTime = createdTime;
	}
	public CommentTypeEnum getType() {
		return type;
	}
	public void setType(CommentTypeEnum type) {
		this.type = type;
	}
}
