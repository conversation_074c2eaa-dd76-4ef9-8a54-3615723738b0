package com.bees360.entity.dto;

import com.bees360.entity.util.StringFieldUtil;

import java.util.HashSet;
import java.util.Set;

public class CompanySearchOption extends BaseSearchOption {

	private Long companyId;
	// using fuzzy search
	private String companyName;
	private Integer companyType;
	private Boolean autoPay;

	private static final Set<String> SORT_KEYS_RANGE;
	public static final int DEFAULT_PAGE_INDEX = 1;
	private static final int DEFAULT_PAGE_SIZE = 10;

	public CompanySearchOption() {
		setPageSize(DEFAULT_PAGE_SIZE);
		setPageIndex(DEFAULT_PAGE_INDEX);
	}

	static {
		SORT_KEYS_RANGE = new HashSet<String>();
		SORT_KEYS_RANGE.add("companyId");
		SORT_KEYS_RANGE.add("companyName");
		SORT_KEYS_RANGE.add("companyType");
		SORT_KEYS_RANGE.add("createdTime");
	}

	@Override
	public void setSortKey(String sortKey) {
		if(SORT_KEYS_RANGE.contains(sortKey)) {
			this.sortKey = sortKey;
		} else {
			this.sortKey = null;
		}
	}

	@Override
	public void setPageIndex(int pageIndex) {
		if(pageIndex < DEFAULT_PAGE_INDEX) {
			pageIndex = DEFAULT_PAGE_INDEX;
		}
		super.setPageIndex(pageIndex);
	}

	@Override
	public void setPageSize(int pageSize) {
		if(pageSize < DEFAULT_PAGE_SIZE) {
			pageSize = DEFAULT_PAGE_SIZE;
		}
		super.setPageSize(pageSize);
	}

	public String getCompanyNameRegex() {
	    if(companyName == null) {
            return null;
        }
	    return StringFieldUtil.joinAndWrap("%", companyName.toCharArray());
    }

	//** getter and setter **//
	public Long getCompanyId() {
		return companyId;
	}
	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public Integer getCompanyType() {
		return companyType;
	}
	public void setCompanyType(Integer companyType) {
		this.companyType = companyType;
	}
	public Boolean getAutoPay() {
		return autoPay;
	}
	public void setAutoPay(Boolean autoPay) {
		this.autoPay = autoPay;
	}
}
