package com.bees360.entity.openapi.reportsummary;

import lombok.Data;

@Data
public class SummaryCommunity {
    /**
     * The number of buildings in the community.
     */
    private Integer numBldg;

    /**
     * The number of residential units in the community.
     */
    private Integer numResidentialUnits;

    /**
     * If there is a balcony in the community.
     */
    private Boolean hasBalcony;

    /**
     * If pets are allowed in the community.
     */
    private Boolean petsAllowed;

    /**
     * If there is a tennis court in the community.
     */
    private Boolean hasTennisCourt;

    /**
     * If there is a playground in the community.
     */
    private Boolean hasPlayground;

    /**
     * If there is a basketball court in the community.
     */
    private Boolean hasBasketballCourt;
}
