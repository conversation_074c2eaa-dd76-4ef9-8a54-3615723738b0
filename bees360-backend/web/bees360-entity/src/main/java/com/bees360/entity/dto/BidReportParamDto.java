package com.bees360.entity.dto;

import com.bees360.entity.Bidding;
import com.bees360.entity.Company;
import com.bees360.entity.House;
import com.bees360.entity.Project;
import com.bees360.entity.User;
import com.bees360.entity.enums.ReportTypeEnum;

public class BidReportParamDto {

	private Project project;

	private String folderPath;

	private Company company;

	private User user;

	private House house;

	private Bidding bid;

	private long time;

	private ReportTypeEnum reportType;

	public BidReportParamDto() {
		super();
	}

	public BidReportParamDto(Project project, String folderPath, Company company,
			User user, House house, long time, ReportTypeEnum reportType) {
		this(project, folderPath, company, user, house, null, time, reportType);
	}

	public BidReportParamDto(Project project, String folderPath, Company company,
			User user, House house, Bidding bid, long time, ReportTypeEnum reportType) {
		super();
		this.project = project;
		this.folderPath = folderPath;
		this.company = company;
		this.user = user;
		this.house = house;
		this.bid = bid;
		this.time = time;
		this.reportType = reportType;
	}

	public Project getProject() {
		return project;
	}

	public void setProject(Project project) {
		this.project = project;
	}

	public String getFolderPath() {
		return folderPath;
	}

	public void setFolderPath(String folderPath) {
		this.folderPath = folderPath;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public House getHouse() {
		return house;
	}

	public void setHouse(House house) {
		this.house = house;
	}

	public Bidding getBid() {
		return bid;
	}

	public void setBid(Bidding bid) {
		this.bid = bid;
	}

	public long getTime() {
		return time;
	}

	public void setTime(long time) {
		this.time = time;
	}

	public ReportTypeEnum getReportType() {
		return reportType;
	}

	public void setReportType(ReportTypeEnum reportType) {
		this.reportType = reportType;
	}

}
