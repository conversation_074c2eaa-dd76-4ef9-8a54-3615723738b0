package com.bees360.entity.openapi.reportsummary;

import com.bees360.web.core.json.gson.MapToStringListTypeAdapter;
import com.bees360.web.core.json.gson.RemoveNullListAdapter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.gson.annotations.JsonAdapter;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ReportSummaryVo {
    /**
     * The built year as a four-digit integer.
     */
    private Integer yearBuilt;
    /**
     * The total living area in Sq.Ft..
     */
    private Double livingArea;
    /**
     * The lot size in acres.
     */
    private Double lotSize;
    private SummaryRisk risk;
    private SummaryBldg bldg;
    private SummaryRoof roof;
    private SummaryExterior exterior;
    private SummaryInterior interior;
    private SummaryFireProtection fireProtection;
    private SummaryCommunity community;
    private List<String> closeoutReasons;
    /**
     * List of additional structures on the property, such as ["Storage Shed", "Horse Stable", "Garden", "Barn"]
     */
    @JsonAdapter(MapToStringListTypeAdapter.class)
    private List<String> addlStructures;
    /**
     * List of text comments of hazards discovered
     */
    @JsonAdapter(MapToStringListTypeAdapter.class)
    private List<String> hazards;
    /**
     * List of recommendations. Each recommendation is an object with a text property, and an image property. text is a
     * String describing the recommendation, and image is an object that contains at least an id property that
     * represents the image's unique identifier.
     */
    @JsonAdapter(RemoveNullListAdapter.class)
    private List<SummaryRecommendation> recommendations;

    @JsonAdapter(RemoveNullListAdapter.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SummaryFactor> factors;

    @JsonAdapter(RemoveNullListAdapter.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SummaryHistory> history;

    /**
     * The root object containing all HOVER-related data.
     * HOVER is a technology that creates 3D models of properties from photos.
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Hover hover;
}
