package com.bees360.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class BeesPilotBatchItem {

    private long id;

    private String batchNo;

    private Long projectId;

    private Timestamp createdAt;

    private Timestamp updatedAt;

    private boolean isDeleted;

    public BeesPilotBatchItem(String batchNo, Long projectId) {
        this.batchNo = batchNo;
        this.projectId = projectId;
    }

    public String keyObjFormat() {
        return "{" +
            "batchNo='" + batchNo + '\'' +
            ", projectId=" + projectId +
            '}';
    }
}
