package com.bees360.entity;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * BeesPilotStatus
 * <AUTHOR>
 * @since 2020/07/31
 */
@Data
public class BeesPilotStatus implements Serializable {
    private Long statusId;

    private Long projectId;

    /**
     * 该任务是否全部完成
     *
     * @see com.bees360.entity.enums.CheckStatusEnum
     */
    private int checkStatus;

    /**
     * @see com.bees360.entity.enums.CheckStatusEnum
     */
    private int mobileCheckStatus;

    /**
     * bees drone check status
     * @see com.bees360.entity.enums.CheckStatusEnum
     */
    private int droneCheckStatus;

    /**
     * pilot upload all image or not
     * @see com.bees360.entity.enums.ImageUploadStatusEnum
     */
    private int imageUploadStatus;

    /**
     * 图片是否已全部上传
     */
    private boolean imageUploaded;

    /**
     * pilot complete quiz or not
     */
    private boolean quizCompleted;

    /**
     * pilot complete verify address of the task or not
     */
    private boolean addressVerified;

    /**
     * the reason code that pilot checkout in advance
     */
    private Integer preCheckoutReason;
    /**
     * the timestamp when pilot checked out
     */
    private Long checkoutTime;

    private Long imageUploadedTime;

    private Long lastUpdateTime;

    @Serial
    private static final long serialVersionUID = 23423423424L;
}
