package com.bees360.entity.firebase;

import com.google.cloud.Timestamp;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.GeoPoint;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2020/8/31 3:34 下午
 **/
@Data
public class FirebaseImage {
    private static final String GS_IMAGE_PREFIX = "gs://";
    private String fileName;
    // The file name for the same image with the middle resolution.
    private String fileNameMiddleResolution;
    // The file name for the same image with the smaller resolution.
    private String fileNameLowerResolution;
    // The local filename when this file is uploaded from some local device.
    private String originalFileName;

    private long fileSize;

    private Timestamp uploadTime;

    // The GPS position of this image when it is generated.
    private GeoPoint gps;

    private double relativeAltitude;

    private int imageHeight;
    private int imageWidth;

    // 图片状态: default
    private int status = 3;

    private Boolean deleted;

    private int categoryId;

    private Integer subCategoryId;

    private double weight;

    private Timestamp shootingTime;

    // 图片信息中的 TIFF/Orientation，默认值为1(Normal)
    private int tiffOrientation = 1;

    /**
     * 图片的方向
     * @see com.bees360.entity.enums.OrientationEnum
     */
    private Integer orientation;

    // unused field to suppress warning
    private String localPath;
    private String projectId;
    private String djiName;
    private String taskId;
    private Timestamp djiTime;

    private String roomName;

    /** 图片描述 */
    private String description;

    /** room reference */
    private DocumentReference room;

    // App端新增的字段BeesPath, 含义为image的存储路径
    private String beesPath;

    private String eTag;

    private String uploadBy;

    private String firebaseImageKey;

    private String note;

    private String trimGsImagePrefix(String key) {
        if (key != null && key.startsWith(GS_IMAGE_PREFIX)) {
            return key.replaceFirst(GS_IMAGE_PREFIX, StringUtils.EMPTY);
        }
        return key;
    }

    public String getFileName() {
        return trimGsImagePrefix(fileName);
    }

    public String getFileNameMiddleResolution() {
        return trimGsImagePrefix(fileNameMiddleResolution);
    }

    public String getFileNameLowerResolution() {
        return trimGsImagePrefix(fileNameLowerResolution);
    }
}
