package com.bees360.entity.pilot;

import lombok.Data;
import org.apache.commons.lang3.reflect.FieldUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 深圳聚蜂智能科技有限公司 版权所有 © Copyright 2019 Bees360
 *
 * @Description: 飞手模型
 * @Project: com.bees360.entity
 * @CreateDate: 2019/11/8
 * @Author: <a href="<EMAIL>">jiankang.xia</a>
 */
@Data
public class Pilot extends Base {


    private String account ;
    private String userName ;
    private Long userId ;
    private String phone ;
    private String email ;
    /**
     * 飞手 飞行半径
     */
    private Integer travelRadius;
    /**
     * 飞手证书执照
     */
    private String licenseKeyUrls;
    /**
     * 证书签发日期
     */
    private Date licenseIssueDate;
    /**
     * 证书过期时间
     */
    private Date licenseExpiryDate;
    /**
     * 飞手证书执照
     */
    private String insuranceKeyUrls;
    /**
     * 保单金额
     */
    private BigDecimal insuranceAmount;
    /**
     * 保单过期时间
     */
    private Date insuranceExpiryDate;
    /**
     * 保单承保日期
     */
    private Date insuranceUnderwritingDate;

    private String licenseNumber;

    private String insuranceNumber;

}
