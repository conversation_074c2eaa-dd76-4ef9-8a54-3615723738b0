package com.bees360.entity.dto;

import java.util.List;

public class IdValueTreeDto {

	private Long id;

	private String value;

	private List<IdValueTreeDto> children;

	public IdValueTreeDto() {}

	public IdValueTreeDto(Long id, String value) {
		this.id = id;
		this.value = value;
	}

	public IdValueTreeDto(Long id, String value, List<IdValueTreeDto> children) {
		super();
		this.id = id;
		this.value = value;
		this.children = children;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public List<IdValueTreeDto> getChildren() {
		return children;
	}

	public void setChildren(List<IdValueTreeDto> children) {
		this.children = children;
	}

	@Override
	public String toString() {
		return "IdValueTreeDto [id=" + id + ", value=" + value + ", children=" + children + "]";
	}

}
