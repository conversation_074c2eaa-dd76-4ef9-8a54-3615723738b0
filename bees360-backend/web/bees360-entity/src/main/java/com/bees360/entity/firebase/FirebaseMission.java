package com.bees360.entity.firebase;

import com.bees360.firebase.entity.FbCheckoutReason;
import com.bees360.firebase.entity.FirebaseCallRecord;
import com.bees360.firebase.entity.FirebaseFeedback;
import com.bees360.firebase.entity.FirebaseProject;
import com.bees360.firebase.entity.FirebaseTimeline;
import com.google.cloud.Timestamp;
import com.google.cloud.firestore.DocumentReference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/8/25 11:58 上午
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FirebaseMission {

    private FirebaseProject project;

    /**
     * @see MissionStateEnum#getCode()
     */
    private int status;

    private List<FirebaseTimeline> timeline;

    private Timestamp lastUpdateTime;

    private Timestamp assignedTime;

    /**
     * 该mission完成时间
     */
    private Timestamp completionTime;

    String reviewImage;

    private List<String> tasks;

    private Map<String, Boolean> taskStatus;

    private Boolean isDeleted = false;

    private String batchNo;

    private List<FbCheckoutReason> checkOutReason;

    private DocumentReference pilot;

    /** 飞手打电话记录 **/
    private List<FirebaseCallRecord> callRecord;
    /**
     * 飞手反馈
     */
    private List<FirebaseFeedback> feedback;

    /**
     * 用于存放deeplinks, key是准备打开的App,如plnar,value是对应的链接，
     * 如 https://snap.plnar.co/link/abcd
     */
    private Map<String, Object> deepLinks;

    /**
     * 用于显示的serviceType
     * @see  FirebaseProject#getServiceType()
     */
    private String displayServiceType;
}
