package com.bees360.entity.openapi.reportsummary;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SummaryRisk {
    /**
     * Overall risk condition, possible values include [ "Excellent", "Good", "Average", "Fair", "Poor" ].
     */
    private String overallCondition;
    /**
     * Status of area economy, possible values include [ "improving", "stable", "declining" ].
     */
    private String areaEconomy;
    /**
     * Type of neighborhood, possible values include [ "Urban", "Suburban", "Rural" ].
     */
    private String neighborhood;
    /**
     * Whether the property is located in a gated community.
     */
    private Boolean gatedCommunity;

    private Boolean locatedOnPavedRoad;
    /**
     * Whether the property is an isolated dwelling
     */
    private Boolean isolatedDwelling;
    /**
     * Whether the property is a seasonal dwelling.
     */
    private Boolean seasonalDwelling;
    /**
     * A string representing the type of business operation of the property, possible values include ["Farming",
     * "Gardening", "Professional Services"].
     */
    private String businessOperation;
    /**
     * Whether the property is vacant.
     */
    private Boolean vacant;
    /**
     * If the property is being rented (has tenants)
     */
    private Boolean rental;
    /**
     * If the property is currently for sale.
     */
    private Boolean forSale;
    /**
     * If the property is inaccessible for inspection.
     */
    private Boolean inaccessible;

    private Double hazardScore;

    private Boolean hasCrops;

    private Boolean hasHorses;

    private SummaryRiskWaterBody waterBody;
}
