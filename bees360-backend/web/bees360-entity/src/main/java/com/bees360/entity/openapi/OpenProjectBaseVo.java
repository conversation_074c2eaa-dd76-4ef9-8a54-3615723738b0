package com.bees360.entity.openapi;

import com.bees360.entity.Project;
import com.bees360.entity.validate.ProjectServiceType;
import com.bees360.entity.validate.ProjectType;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/01/19 10:43
 */
@Data
@NoArgsConstructor
public class OpenProjectBaseVo extends OpenProjectAddressVo {

    private Double lat;

    private Double lng;

    /**
     * {@link com.bees360.entity.enums.ProjectTypeEnum} 字符串类型code
     * @see Project#getProjectType()
     */
    @ProjectType
    private String houseType;
    /**
     * yyyy-MM-dd 格式时间，默认美国中部时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd", timezone = "US/Central", locale = "US")
    private Date dueDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd", timezone = "US/Central", locale = "US")
    private LocalDate policyEffectiveDate;
    @Digits(integer = 4, fraction = 0)
    private Integer yearBuilt;
    private String policyType = "";
    private String policyNumber = "";
    private String inspectionNumber = "";
    private String insuredName = "";
    private String insuredPhone = "";
    private String agentName = "";
    private String agentPhone = "";
    @NotBlank
    @ProjectServiceType
    private String serviceName = "";
    private String agentEmail = "";
    private String insuredEmail = "";
    @NotNull
    private String inspectionCode = "";
    @NotNull
    private String inspectionLink = "";
    /**
     * the company key of insurance company
     *
     * @see Project#getInsuranceCompany()
     */
    private String insuredBy = "";
    private String note = "";

    private Boolean isRenewal;

    private List<String> supplementalServices;

    private String division = "";

    private String divisionName = "";

    private String operatingCompany = "";

    private Double dwellingCoverage;

    private Double otherStructureCoverage;

    private Double contentCoverage;

    private Double carrierProvidedLivingArea;

//    @NotEmpty
//    private List<@Valid OpenReportVo> reports;

    public String getPolicyNumber() {
        return policyNumber == null ? "" : policyNumber;
    }

    public String getInspectionNumber() {
        return inspectionNumber == null ? "" : inspectionNumber;
    }

    public String getInsuredName() {
        return insuredName == null ? "" : insuredName;
    }

    public String getInsuredPhone() {
        return insuredPhone == null ? "" : insuredPhone;
    }

    public String getInsuredEmail() {
        return insuredEmail == null ? "" : insuredEmail;
    }

    public String getAgentName() {
        return agentName == null ? "" : agentName;
    }

    public String getAgentPhone() {
        return agentPhone == null ? "" : agentPhone;
    }

    public String getAgentEmail() {
        return agentEmail == null ? "" : agentEmail;
    }
}
