package com.bees360.entity.consts;

/**
 * <AUTHOR>
 * @since 2020/7/9 9:26 PM
 **/
public interface DataDictionaryConstant {
    /**
     * beesPilot任务 数据字典的namespace
     */
    String BEESPILOT_TASK_NAMESPACE = "BeespilotTask";
    /**
     * serviceType与beespilot任务对应关系 数据字典的namespace
     */
    String SERVICE_TYPE_TO_TASK_NAMESPACE = "ServiceType2Task";
    /**
     * 各个平台图片压缩比例 数据字典的namespace
     */
    String IMAGE_COMPRESS_CONST_NAMESPACE = "ImageCompressConst";

    /**
     * 无人机飞行模式 数据字典的namespace
     */
   String DRONE_FLY_PATTERN_NAMESPACE = "DroneFlyPattern";
}
