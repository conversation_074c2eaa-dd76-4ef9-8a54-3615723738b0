package com.bees360.entity.stat.search;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;

@Data
public class StatSearchOption {

    private Long from;
    private Long to;
    private List<String> states;
    private Double riskScoreMin;
    private Double riskScoreMax;

    public StatFullSearchOption toFullSearchOption(){
        StatFullSearchOption searchOption = new StatFullSearchOption();
        searchOption.setStates(states);
        searchOption.setStartTime(from);
        searchOption.setEndTime(to);

        if (riskScoreMin != null){
            searchOption.setRiskScoreMin(new BigDecimal(riskScoreMin));
        }
        if (riskScoreMax != null){
            searchOption.setRiskScoreMax(new BigDecimal(riskScoreMax));
        }

        return searchOption;

    }

}
