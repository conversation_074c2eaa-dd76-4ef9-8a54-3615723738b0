package com.bees360.entity.enums;

import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * the claim type of the project.
 * make sure all enums are from AnnotationTypeEnum
 * <AUTHOR>
 */
public enum ClaimTypeEnum implements BaseCodeEnum{
	HAIL(0, "Hail"),
	WIND(1, "Wind"),
	HAIL_WIND(2, "Hail & Wind"),
    FIRE(3, "Fire"),
    FLOOD(4, "Flood"),
    HURRICANE(9, "Hurricane"),
	UNDERWRITING_FIRST_INSPECTION(5, "Underwriting/First Inspection"),
	OTHERS(6, "Others")
    ;
	private final int code;
	private final String display;

	ClaimTypeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

    public static boolean isClaim(int claimType) {
        return UNDERWRITING_FIRST_INSPECTION.getCode() != claimType;
    }
    public static boolean isUnderwriting(int underwriting) {
        return UNDERWRITING_FIRST_INSPECTION.getCode() == underwriting;
    }

    public static boolean isTypeCanChange(Integer oldType,Integer newType) {
        var b = ClaimTypeEnum.isClaim(oldType) && ClaimTypeEnum.isClaim(newType);
        var c = ClaimTypeEnum.isUnderwriting(oldType) && ClaimTypeEnum.isUnderwriting(newType);
        return b | c;
    }

    @Override
	public String getDisplay(){
		return display;
	}

	@Override
	public int getCode() {
		return code;
	}

	public static ClaimTypeEnum getEnum(Integer claimTypeCode){
		return claimTypeCode == null? null: getEnum(claimTypeCode.intValue());
	}

	public static ClaimTypeEnum getEnum(int claimTypeCode){
		ClaimTypeEnum[] types = ClaimTypeEnum.values();
		for(ClaimTypeEnum type: types){
			if(type.getCode() == claimTypeCode){
				return type;
			}
		}
		return null;
	}

	public static boolean exist(Integer code) {
		return code != null && getEnum(code) != null;
	}

	public static void main(String[] args) {
		show();
	}

	private static void show() {
		System.out.println("|name|code|type|");
		System.out.println("|----|----|---|");
		for(ClaimTypeEnum type: ClaimTypeEnum.values()) {
		    String claimType = ClaimTypeEnum.isClaim(type.getCode())? "Claim": null;
		    String underwriting = ClaimTypeEnum.isUnderwriting(type.getCode())? "Underwriting": null;
		    String purpose = Stream.of(claimType, underwriting).filter(Objects::nonNull).collect(Collectors.joining(","));
			System.out.println("|" + type.getDisplay() + "|" + type.getCode() + "|" + purpose + "|" );
		}
	}
}
