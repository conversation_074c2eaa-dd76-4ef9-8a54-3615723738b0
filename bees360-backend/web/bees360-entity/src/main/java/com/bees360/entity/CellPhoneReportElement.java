package com.bees360.entity;

import java.io.Serial;
import java.io.Serializable;

public class CellPhoneReportElement implements Serializable {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 3479908970606127149L;
	//The foreign key referencing the id in Project
	private long projectId;
	//	0: Main Dwelling
	//	1: Detached Storage Shed
	//	2: Detached Garage
	//	3: Horse Stable
	//	4: Guest House
	//	5: APS Structure
	//	If there no suitable options(from 0 to 4) for user,  5 should be chosen.
	private int componentId;
	private int componentType;
	//	0: Roofing
	//	1: Exterior
	//	2: Interior
	private int inspectionCategory;
	//It means the relative position of this image for a project:
	//	0: BirdView
	//	1: WestView
	//	2: EastView
	//	3: NorthView
	//	4: SouthView
	//	5: LengthCAD
	//	6: AreaCAD
	//	7: PitchCAD
	//	8: NameCAD
	//	This field may not be used.
	private int relativePositionType;
	//The data for Report.
	private String objectDescriptionJson;
	//The user who adds this segment to the image.
	private long addedBy;
	//The timestamp when this image segment is created.
	private long createdTime;

	public CellPhoneReportElement() {
		super();
	}

	public CellPhoneReportElement(long projectId) {
		super();
		this.projectId = projectId;
	}

	public CellPhoneReportElement(long projectId, int componentId, int inspectionCategory, int relativePositionType,
			String objectDescriptionJson, long addedBy, long createdTime) {
		super();
		this.projectId = projectId;
		this.componentId = componentId;
		this.inspectionCategory = inspectionCategory;
		this.relativePositionType = relativePositionType;
		this.objectDescriptionJson = objectDescriptionJson;
		this.addedBy = addedBy;
		this.createdTime = createdTime;
	}

	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}

	public long getAddedBy() {
		return addedBy;
	}

	public void setAddedBy(long addedBy) {
		this.addedBy = addedBy;
	}

	public int getRelativePositionType() {
		return relativePositionType;
	}

	public void setRelativePositionType(int relativePositionType) {
		this.relativePositionType = relativePositionType;
	}

	public String getObjectDescriptionJson() {
		return objectDescriptionJson;
	}

	public void setObjectDescriptionJson(String objectDescriptionJson) {
		this.objectDescriptionJson = objectDescriptionJson;
	}

	public int getInspectionCategory() {
		return inspectionCategory;
	}

	public void setInspectionCategory(int inspectionCategory) {
		this.inspectionCategory = inspectionCategory;
	}

	public int getComponentId() {
		return componentId;
	}

	public void setComponentId(int componentId) {
		this.componentId = componentId;
	}

	public int getComponentType() {
		return componentType;
	}

	public void setComponentType(int componentType) {
		this.componentType = componentType;
	}

	@Override
	public String toString() {
		return "ProjectImageReportElement [projectId=" + projectId + ", componentId=" + componentId + ", componentType="
				+ componentType + ", inspectionCategory=" + inspectionCategory + ", relativePositionType="
				+ relativePositionType + ", objectDescriptionJson=" + objectDescriptionJson + ", addedBy=" + addedBy
				+ ", createdTime=" + createdTime + "]";
	}

}
