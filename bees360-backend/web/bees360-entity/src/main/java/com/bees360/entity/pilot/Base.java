package com.bees360.entity.pilot;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 深圳聚蜂智能科技有限公司 版权所有 © Copyright 2019 Bees360
 *
 * @Description:
 * @Project: com.bees360.entity.pilot
 * @CreateDate: 2019/11/8
 * @Author: <a href="<EMAIL>">jiankang.xia</a>
 */
@Data
public class Base {
    private Long id;
    /**
     * <pre>
     * 数据库字段:
     * is_enable
     * 字段描述:
     *  data status: 1-enabled,2-inactived,0-disabled ;字段的长度:2,是否必填:是。
     * </pre>
     */
    private Integer isEnable;
    /**
     * <pre>
     * 数据库字段:
     * is_delete
     * 字段描述:
     *  data logic delete status: 1-deleted,0-not deleted ;字段的长度:2,是否必填:是。
     * </pre>
     */
    private Integer isDelete;
    /**
     * <pre>
     * 数据库字段:
     * create_time
     * 字段描述:
     *  create_time;字段的长度:,是否必填:是。
     * </pre>
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * <pre>
     * 数据库字段:
     * create_by
     * 字段描述:
     *  created_by user;字段的长度:20,是否必填:否。
     * </pre>
     */
    private Integer createBy;
    /**
     * <pre>
     * 数据库字段:
     * update_time
     * 字段描述:
     *  update_time;字段的长度:,是否必填:否。
     * </pre>
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * <pre>
     * 数据库字段:
     * update_by
     * 字段描述:
     *  updated_by user;字段的长度:20,是否必填:否。
     * </pre>
     */
    private Integer updateBy;
    /**
     * <pre>
     * 数据库字段:
     * version
     * 字段描述:
     *  data version;字段的长度:10,是否必填:否。
     * </pre>
     */
    private Integer version;
}
