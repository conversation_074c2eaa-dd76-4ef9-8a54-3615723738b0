package com.bees360.entity.vo;

/**
 * {@link com.bees360.entity.Invoice} 对象返回给客户端的视图对象。
 *
 * <AUTHOR>
 * @date 2019/10/11 14:00
 */
public class InvoiceVo {
    private long projectId;
    private long invoiceId;
    private String title;
    private long createTime;
    private String resourceKey;

    public long getProjectId() {
        return projectId;
    }

    public void setProjectId(long projectId) {
        this.projectId = projectId;
    }

    public long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getResourceKey() {
        return resourceKey;
    }

    public void setResourceKey(String resourceKey) {
        this.resourceKey = resourceKey;
    }

    @Override
    public String toString() {
        return "InvoiceVo{" +
            "projectId=" + projectId +
            ", invoiceId=" + invoiceId +
            ", title='" + title + '\'' +
            ", createTime=" + createTime +
            ", s3Key='" + resourceKey + '\'' +
            '}';
    }
}
