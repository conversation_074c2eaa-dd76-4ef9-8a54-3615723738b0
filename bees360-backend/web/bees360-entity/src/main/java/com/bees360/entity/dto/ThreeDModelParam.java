package com.bees360.entity.dto;

import java.io.Serial;
import java.io.Serializable;

public class ThreeDModelParam implements Serializable{

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;

	private Integer imageSize = null;

	private int pmvsLevel = 1;

	private int pmvswSize = 7;

	private double pmvsThreshold = 0.75;

	private int pmvsMaxangle = 10;

	private double pmvsQuad = 2.0;

	public Integer getImageSize() {
		return imageSize;
	}

	public void setImageSize(Integer imageSize) {
		this.imageSize = imageSize;
	}

	public int getPmvsLevel() {
		return pmvsLevel;
	}

	public void setPmvsLevel(int pmvsLevel) {
		this.pmvsLevel = pmvsLevel;
	}

	public int getPmvswSize() {
		return pmvswSize;
	}

	public void setPmvswSize(int pmvswSize) {
		this.pmvswSize = pmvswSize;
	}

	public double getPmvsThreshold() {
		return pmvsThreshold;
	}

	public void setPmvsThreshold(double pmvsThreshold) {
		this.pmvsThreshold = pmvsThreshold;
	}

	public int getPmvsMaxangle() {
		return pmvsMaxangle;
	}

	public void setPmvsMaxangle(int pmvsMaxangle) {
		this.pmvsMaxangle = pmvsMaxangle;
	}

	public double getPmvsQuad() {
		return pmvsQuad;
	}

	public void setPmvsQuad(double pmvsQuad) {
		this.pmvsQuad = pmvsQuad;
	}
}
