package com.bees360.entity.enums;

/**
 * Report element image type enum
 * <AUTHOR>
 */
public enum ReportElementSourceTypeEnum implements BaseCodeEnum{

	REPORT_ELEMENT(0, "report element image"),
	BEES_GO_APP(1, "bees go app image");

	private final int code;
	private final String display;

	ReportElementSourceTypeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public String getDisplay(){
		return display;
	}

	@Override
	public int getCode() {
		return code;
	}
}
