package com.bees360.entity.enums;

import java.util.HashMap;
import java.util.Map;

public enum AudienceEnum {
	WEB(1,"web", 604800000L),
	MOBILE(2,"mobile", 7776000000L),
	TABLET(3,"tablet", 3000),
	UNKNOWN(4,"unknown", 3000);

	private final int code;
	private final String display;
	private final long jwtExp;

	private static Map<String, AudienceEnum> audMap = new HashMap<String, AudienceEnum>();
	static {
		for(AudienceEnum aud: AudienceEnum.values()) {
			audMap.put(aud.getDisplay(), aud);
		}
	}

	private AudienceEnum(int code, String display, long jwtExp) {
		this.code = code;
		this.display = display;
		this.jwtExp = jwtExp;
	}

	public static AudienceEnum getEnum(int code) {
		for(AudienceEnum requestSourceEnum : AudienceEnum.values()) {
			if(code == requestSourceEnum.getCode()) {
				return requestSourceEnum;
			}
		}
		//default unknow
		return UNKNOWN;
	}

	public static AudienceEnum getEnum(String display) {
		return audMap.containsKey(display)? audMap.get(display) : UNKNOWN;
	}

	public int getCode() {
		return code;
	}

	public String getDisplay() {
		return display;
	}

	public long getJwtExp() {
		return this.jwtExp;
	}
}
