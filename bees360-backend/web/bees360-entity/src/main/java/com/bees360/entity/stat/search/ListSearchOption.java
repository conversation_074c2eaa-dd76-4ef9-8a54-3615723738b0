package com.bees360.entity.stat.search;

import com.bees360.common.string.Strings;
import com.bees360.common.util.DateUtil;
import lombok.Data;
import org.springframework.util.StringUtils;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;

@Data
public class ListSearchOption extends PagedSearchOption {

    private Long from;
    private Long to;

    private List<String> states;

    @NotNull
    private Integer serviceType;
    @NotNull
    private Integer type;

    private Double riskScoreMin;
    private Double riskScoreMax;


    public StatFullSearchOption toFullSearchOption(){
        final StatFullSearchOption searchOption = new StatFullSearchOption();

        searchOption.setStartTime(from);
        searchOption.setEndTime(to);
        searchOption.setStates(states);
        searchOption.setCenterUtcOffset(DateUtil.getOffset(ZoneId.of("US/Central"), ZoneId.of("UTC")));
        searchOption.addServiceType(serviceType);

        searchOption.setPageIndex(this.getPageIndex());
        searchOption.setPageSize(this.getPageSize());
        searchOption.setSortKey(Strings.CamelCaseToUnderScore(this.getSortKey()));
        searchOption.setSortOrder(this.getSortOrder());
        searchOption.setType(this.getType());

        if (riskScoreMin != null){
            searchOption.setRiskScoreMin(new BigDecimal(riskScoreMin));
        }
        if (riskScoreMax != null){
            searchOption.setRiskScoreMax(new BigDecimal(riskScoreMax));
        }

        return searchOption;
    }
}
