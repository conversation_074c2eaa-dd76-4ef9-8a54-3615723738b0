package com.bees360.entity.enums;

public enum RoofComplexityEnum implements BaseCodeEnum{

	LOW(0, "Low"),
	MEDIUM(1, "Medium"),
	HIGH(2, "High");

	private final int code;
	private final String display;
	RoofComplexityEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public static RoofComplexityEnum getEnum(int code){
		RoofComplexityEnum[] roofComplexityTypes = RoofComplexityEnum.values();
		for(RoofComplexityEnum roofComplexityType: roofComplexityTypes){
			if(roofComplexityType.code == code){
				return roofComplexityType;
			}
		}
		return null;
	}

	public static RoofComplexityEnum getEnum(String display){
		RoofComplexityEnum[] roofComplexityTypes = RoofComplexityEnum.values();
		for(RoofComplexityEnum roofComplexityType: roofComplexityTypes){
			if(roofComplexityType.display.equals(display)){
				return roofComplexityType;
			}
		}
		return null;
	}

}
