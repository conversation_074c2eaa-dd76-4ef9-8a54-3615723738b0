package com.bees360.entity.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/8/6 9:22 AM
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectImageSearchOptionDto {

    private Long projectId;
    private Integer fileSourceType;
    private Integer imageType;
    private Integer partialType;
    private Integer excludeFileSourceType;
    private Boolean deleted;
    private Integer reportType;
    private Boolean needReportType;
    private List<Integer> fileSourceTypes;
    private List<Long> projectIdList;

    private Integer startIndex;
    private Integer pageSize;

    // It is not used in SQL.
    private Integer limit;
    private Integer pageIndex;
}
