package com.bees360.entity;

import com.bees360.entity.enums.productandpayment.GlobalDiscountTypeEnum;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/09/27 16:51
 */
public class InvoiceDiscount {
    private long id;
    private long invoiceId;
    /** {@link GlobalDiscountTypeEnum#getCode()} **/
    private int discountType;
    private String discountName;
    private int offType;
    private double percentageOff;
    private double amountOff;
    private double amount;
    private long createdTime;

    // ===================================
    // Getter & Setter

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public int getDiscountType() {
        return discountType;
    }

    public void setDiscountType(int discountType) {
        this.discountType = discountType;
    }

    public String getDiscountName() {
        return discountName;
    }

    public void setDiscountName(String discountName) {
        this.discountName = discountName;
    }

    public int getOffType() {
        return offType;
    }

    public void setOffType(int offType) {
        this.offType = offType;
    }

    public double getPercentageOff() {
        return percentageOff;
    }

    public void setPercentageOff(double percentageOff) {
        this.percentageOff = percentageOff;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public double getAmountOff() {
        return amountOff;
    }

    public void setAmountOff(double amountOff) {
        this.amountOff = amountOff;
    }

    public long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(long createdTime) {
        this.createdTime = createdTime;
    }

    @Override
    public String toString() {
        return "InvoiceDiscount{" + "id=" + id + ", invoiceId=" + invoiceId + ", discountType=" + discountType
            + ", discountName='" + discountName + '\'' + ", offType=" + offType + ", percentageOff=" + percentageOff
            + ", amountOff=" + amountOff + ", createdTime=" + createdTime + '}';
    }
}
