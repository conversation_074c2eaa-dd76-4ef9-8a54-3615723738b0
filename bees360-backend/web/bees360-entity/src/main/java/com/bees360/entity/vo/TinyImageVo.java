package com.bees360.entity.vo;

import com.bees360.entity.ProjectImage;
import java.util.Objects;

/**
 * the base information of ProjectImage
 * <AUTHOR>
 * @date 2017/12/18 12:33:57
 */
public class TinyImageVo implements View{
	private String imageId;
	private String originalFileName;
	private String fileName;
	private String fileNameMiddleResolution;
	private String fileNameLowerResolution;
	private String annotationImage;
	private long fileSize;
	private boolean manuallyAnnotated;
	private String parentId;

	private long shootingTime;

	public TinyImageVo() {}

	public TinyImageVo(ProjectImage image) {
		this.imageId = image.getImageId();
		this.originalFileName = image.getOriginalFileName();
		this.fileName = image.getFileName();
		this.fileNameLowerResolution = image.getFileNameLowerResolution();
		this.fileNameMiddleResolution = image.getFileNameMiddleResolution();
		this.annotationImage = image.getAnnotationImage();
		this.fileSize = image.getFileSize();
		this.manuallyAnnotated = image.isManuallyAnnotated();
		this.parentId = image.getParentId();
	}

	public String getImageS3Key(){
	    return fileName;
    }

    public String getMiddleResolutionImageS3Key(){
	    return fileNameMiddleResolution;
    }

    public String getLowerResolutionImageS3Key(){
	    return fileNameLowerResolution;
    }

    public String getAnnotationImageS3Key(){
	    return annotationImage;
	}

    public String getImageId() {
        return imageId;
    }
    public void setImageId(String imageId) {
        this.imageId = imageId;
    }
    public String getOriginalFileName() {
		return originalFileName;
	}
	public void setOriginalFileName(String originalFileName) {
		this.originalFileName = originalFileName;
	}
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	public String getFileNameMiddleResolution() {
		return fileNameMiddleResolution;
	}
	public void setFileNameMiddleResolution(String fileNameMiddleResolution) {
		this.fileNameMiddleResolution = fileNameMiddleResolution;
	}
	public String getFileNameLowerResolution() {
		return fileNameLowerResolution;
	}
	public void setFileNameLowerResolution(String fileNameLowerResolution) {
		this.fileNameLowerResolution = fileNameLowerResolution;
	}
	public String getAnnotationImage() {
		return annotationImage;
	}
	public void setAnnotationImage(String annotationImage) {
		this.annotationImage = annotationImage;
	}
	public long getFileSize() {
		return fileSize;
	}
	public void setFileSize(long fileSize) {
		this.fileSize = fileSize;
	}
	public boolean isManuallyAnnotated() {
		return manuallyAnnotated;
	}
	public void setManuallyAnnotated(boolean manuallyAnnotated) {
		this.manuallyAnnotated = manuallyAnnotated;
	}
	public String getParentId() {
		return parentId;
	}
	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public long getShootingTime() {
		return shootingTime;
	}

	public void setShootingTime(long shootingTime) {
		this.shootingTime = shootingTime;
	}

	@Override
	public int hashCode(){
        return imageId.hashCode();
	}

	@Override
	public boolean equals(Object obj){
		if(this == obj){
			return true;
		}
		if(!(obj instanceof TinyImageVo)){
			return false;
		}
		TinyImageVo tinyImage = (TinyImageVo)obj;
		return Objects.equals(imageId, tinyImage.getImageId());
	}

	@Override
	public String toString() {
		return "TinyImageVo [imageId=" + imageId + ", originalFileName=" + originalFileName + ", fileName=" + fileName
				+ ", fileNameMiddleResolution=" + fileNameMiddleResolution + ", fileNameLowerResolution="
				+ fileNameLowerResolution + ", annotationImage=" + annotationImage + ", fileSize=" + fileSize
				+ ", manuallyAnnotated=" + manuallyAnnotated + ", parentId=" + parentId + "]";
	}
}
