//  package com.bees360.entity.vo;
//
// import java.util.List;
// import java.util.Map;
//
// import com.bees360.entity.Project;
//
// /**
//  * <AUTHOR>
//  * @date 2019/07/15
//  */
// public class ProjectBatchItemVo {
//     // private Project project;
//
//     // 公司名称
//     private String processedBy;
//     // Adjuster 的 Email
//     private String adjuster;
//     // Pilot 的 Email
//     private String pilot;
//     // Visitors 的 Email 列表
//     private List<String> visitors;
//
//     public ProjectBatchItemVo() {
//         project = new Project();
//     }
//
//     //** getter and setter **//
//     public Project getProject() {
//         return project;
//     }
//
//
//     /**
//      * 2019.11.25新增字段 by xjk
//      */
//     private String inspectionNumber;
//     private long dueDate;
//     private String customer;
//     private String inspectionType;
//     private String agent;
//     private String agentEmail;
//     private String agentContactName;
//     private String agentPhone;
//     private String guideline;
//     private String insuredHomePhone;
//     private String insuredWorkPhone;
//
//     public String getInspectionNumber() {
//         return project.getInspectionNumber();
//     }
//
//     public void setInspectionNumber(String inspectionNumber) {
//         this.project.setInspectionNumber(inspectionNumber);
//     }
//
//     public long getDueDate() {
//         return project.getDueDate();
//     }
//
//     public void setDueDate(long dueDate) {
//         this.project.setDueDate(dueDate);
//
//     }
//
//     public String getCustomer() {
//         return project.getCustomer();
//     }
//
//     public void setCustomer(String customer) {
//         this.project.setCustomer(customer);
//     }
//
//     public String getInspectionType() {
//         return project.getInspectionType();
//     }
//
//     public void setInspectionType(String inspectionType) {
//         this.project.setInspectionType(inspectionType);
//     }
//
//     public String getAgent() {
//         return project.getAgent();
//     }
//
//     public void setAgent(String agent) {
//         this.project.setAgent(agent);
//     }
//
//     public String getAgentEmail() {
//         return project.getAgentEmail();
//     }
//
//     public void setAgentEmail(String agentEmail) {
//         this.project.setAgentEmail(agentEmail);
//     }
//
//     public String getAgentContactName() {
//         return project.getAgentContactName();
//     }
//
//     public void setAgentContactName(String agentContactName) {
//         this.project.setAgentContactName(agentContactName);
//     }
//
//     public String getAgentPhone() {
//         return project.getAgentPhone();
//     }
//
//     public void setAgentPhone(String agentPhone) {
//         this.project.setAgentPhone(agentPhone);
//     }
//
//     public String getGuideline() {
//         return project.getGuideline();
//     }
//
//     public void setGuideline(String guideline) {
//         this.project.setGuideline(guideline);
//     }
//
//     public String getInsuredHomePhone() {
//         return project.getInsuredHomePhone();
//     }
//
//     public void setInsuredHomePhone(String insuredHomePhone) {
//         this.project.setInsuredHomePhone(insuredHomePhone);
//     }
//
//     public String getInsuredWorkPhone() {
//         return project.getInsuredWorkPhone();
//     }
//
//     public void setInsuredWorkPhone(String insuredWorkPhone) {
//         this.project.setInsuredWorkPhone(insuredWorkPhone);
//     }
//
//     public String getProcessedBy() {
//         return processedBy;
//     }
//     public void setProcessedBy(String processedBy) {
//         this.processedBy = processedBy;
//     }
//     public String getAdjuster() {
//         return adjuster;
//     }
//     public void setAdjuster(String adjuster) {
//         this.adjuster = adjuster;
//     }
//     public String getPilot() {
//         return pilot;
//     }
//     public void setPilot(String pilot) {
//         this.pilot = pilot;
//     }
//     public List<String> getVisitors() {
//         return visitors;
//     }
//     public void setVisitors(List<String> visitors) {
//         this.visitors = visitors;
//     }
//
//     //** setter and getter of project **//
//     public String getPolicyNumber() {
//         return project.getPolicyNumber();
//     }
//     public void setPolicyNumber(String policyNumber) {
//         this.project.setPolicyNumber(policyNumber);
//     }
//     public String getClaimNumber() {
//         return project.getClaimNumber();
//     }
//     public void setClaimNumber(String claimNumber) {
//         this.project.setClaimNumber(claimNumber);
//     }
//     public Integer getClaimType() {
//         return project.getClaimType();
//     }
//     public void setClaimType(Integer claimType) {
//         this.project.setClaimType(claimType);
//     }
//     public long getDamageEventTime() {
//         return project.getDamageEventTime();
//     }
//     public void setDamageEventTime(long damageEventTime) {
//         this.project.setDamageEventTime(damageEventTime);
//     }
//     public int getProjectType() {
//         return project.getProjectType();
//     }
//     public void setProjectType(int projectType) {
//         this.project.setProjectType(projectType);
//     }
//     public String getAddress() {
//         return project.getAddress();
//     }
//     public void setAddress(String address) {
//         this.project.setAddress(address);
//     }
//     public String getCity() {
//         return project.getCity();
//     }
//     public void setCity(String city) {
//         this.project.setCity(city);
//     }
//     public String getState() {
//         return project.getState();
//     }
//     public void setState(String state) {
//         this.project.setState(state);
//     }
//     public String getCountry() {
//         return project.getCountry();
//     }
//     public void setCountry(String country) {
//         this.project.setCountry(country);
//     }
//     public String getZipCode() {
//         return project.getZipCode();
//     }
//     public void setZipCode(String zipCode) {
//         this.project.setZipCode(zipCode);
//     }
//     public double getLng() {
//         return project.getGpsLocationLongitude();
//     }
//     public void setLng(double lng) {
//         this.project.setGpsLocationLongitude(lng);
//     }
//     public double getLat() {
//         return project.getGpsLocationLatitude();
//     }
//     public void setLat(double lat) {
//         this.project.setGpsLocationLatitude(lat);
//     }
//     public String getAssetOwnerName() {
//         return project.getAssetOwnerName();
//     }
//     public void setAssetOwnerName(String assetOwnerName) {
//         this.project.setAssetOwnerName(assetOwnerName);
//     }
//     public String getAssetOwnerPhone() {
//         return project.getAssetOwnerPhone();
//     }
//     public void setAssetOwnerPhone(String assetOwnerPhone) {
//         this.project.setAssetOwnerPhone(assetOwnerPhone);
//     }
//     public String getAssetOwnerEmail() {
//         return project.getAssetOwnerEmail();
//     }
//     public void setAssetOwnerEmail(String assetOwnerEmail) {
//         this.project.setAssetOwnerEmail(assetOwnerEmail);
//     }
//     public String getDescription() {
//         return project.getDescription();
//     }
//     public void setDescription(String description) {
//         this.project.setDescription(description);
//     }
//     public int getFlyZoneType() {
//         return this.project.getFlyZoneType();
//     }
//     public void setFlyZoneType(int flyZoneType) {
//         this.project.setFlyZoneType(flyZoneType);
//     }
//     public Long getInspectionTime() {
//         return project.getInspectionTime();
//     }
//     public void setInspectionTime(Long inspectionTime) {
//         this.project.setInspectionTime(inspectionTime);
//     }
//     public boolean isNeedPilot() {
//         return project.isNeedPilot();
//     }
//     public void setNeedPilot(boolean needPilot) {
//         this.project.setNeedPilot(needPilot);
//     }
//     public Map<String, Object> getCustomizedParams() {
//         return project.getCustomizedParams();
//     }
//     public void setCustomizedParams(Map<String, Object> customizedParams) {
//         this.project.setCustomizedParams(customizedParams);
//     }
// }
