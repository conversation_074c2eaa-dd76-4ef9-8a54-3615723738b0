package com.bees360.entity.enums.openapi;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum GrantType {

    authorization_code(1, "authorization_code"),
    refresh_token(1<<1, "refresh_token"),
    client_credentials(1<<2, "client_credentials"),
    password(1<<3, "password"),
    implicit(1<<4, "implicit");

    private final int code;
    private final String desc;



    GrantType(int code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static GrantType getGrantType(String desc){
        for (GrantType grantType : GrantType.values()){
            if (grantType.getDesc().equals(desc)){
                return grantType;
            }
        }
        return null;
    }

    public static List<GrantType> getGrantTypes(int code){

        return Arrays.stream(GrantType.values())
            .filter(grantType -> (grantType.getCode() & code) != 0)
            .collect(Collectors.toList());
    }

    public static int getCode(Collection<GrantType> grantTypes){
        final Set<GrantType> typeSet = new HashSet<>(grantTypes);
        int code = 0;
        for (GrantType type : typeSet){
            code |= type.getCode();
        }
        return code;
    }

    public int getCode(){
        return this.code;
    }

    public String getDesc(){
        return this.desc;
    }
}
