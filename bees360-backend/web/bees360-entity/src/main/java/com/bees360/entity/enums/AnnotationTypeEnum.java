package com.bees360.entity.enums;

public enum AnnotationTypeEnum implements BaseCodeEnum {
    // Damage
    NO_DAMAGE(-1, "No Damage", "#A3F758", true, AnnotationUsageTypeEnum.DAMAGE_ANNOTATION),
    HAIL(0, "Hail Damage", "#9BD5FB", true, AnnotationUsageTypeEnum.DAMAGE_ANNOTATION),
    WIND(1, "Wind Damage", "#FDBF47", true, AnnotationUsageTypeEnum.DAMAGE_ANNOTATION),
    WEAR_TEAR(2, "Non-storm related Damage", "#FA4949", true, AnnotationUsageTypeEnum.DAMAGE_ANNOTATION),
    CREASED_SHINGLE(3, "Creased shingle", "#9DE462", true, AnnotationUsageTypeEnum.DAMAGE_ANNOTATION),
    BLISTERING(4, "Blistering", "#9B5BFF", true, AnnotationUsageTypeEnum.DAMAGE_ANNOTATION),
    OTHERS(5, "Others", "transparent", false, AnnotationUsageTypeEnum.DAMAGE_ANNOTATION),
    ROOF_PART(6, "Roof Part", "transparent", false, AnnotationUsageTypeEnum.DAMAGE_ANNOTATION),
    CRACKED(7, "Cracked/Damaged Tile", "#303A4A", true, AnnotationUsageTypeEnum.DAMAGE_ANNOTATION),
    ;

	private final int code;
	private final String display;
	private final String color;
	private final boolean needPaintedDamage;
	private final AnnotationUsageTypeEnum usageTypeEnum;
	private static final String defaultColor = "#00000";

    AnnotationTypeEnum(int code, String display, String color, boolean needPaintedDamage, AnnotationUsageTypeEnum usageTypeEnum) {
        this.code = code;
        this.display = display;
        this.color = color;
        this.needPaintedDamage = needPaintedDamage;
        this.usageTypeEnum = usageTypeEnum;
    }

    @Override
	public int getCode() {
		return code;
	}
	@Override
	public String getDisplay() {
		return display;
	}
	public String getColor(){
		return color;
	}

    public boolean isNeedPaintedDamage() {
        return needPaintedDamage;
    }

    public static AnnotationTypeEnum getEnum(int code){
		AnnotationTypeEnum[] types = AnnotationTypeEnum.values();
		for(AnnotationTypeEnum type: types){
			if(type.code == code){
				return type;
			}
		}
		return null;
	}

	public static String codeToColor(int code){
		AnnotationTypeEnum type = getEnum(code);
		if(type == null){
			return defaultColor;
		}
		return type.getColor();
	}

    public AnnotationUsageTypeEnum getUsageTypeEnum() {
        return usageTypeEnum;
    }

    public static void main(String[] args) {
		show();
	}

	private static void show() {
		System.out.println("|name|code|color|");
		System.out.println("|----|----|-----|");
		for(AnnotationTypeEnum type: AnnotationTypeEnum.values()) {
			System.out.println("|" + type.getDisplay() + "|" + type.getCode() + "|" + type.getColor() + "|");
		}
	}
}
