package com.bees360.entity.validate;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import com.bees360.entity.enums.ProjectServiceTypeEnum;

/**
 * <AUTHOR>
 */
@Constraint(validatedBy = {ProjectServiceTypeValidators.ProjectServiceTypeEnumValidator.class,
    ProjectServiceTypeValidators.ProjectServiceTypeIntegerValidator.class,
    ProjectServiceTypeValidators.ProjectServiceTypeStringValidator.class})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.PARAMETER})
public @interface ProjectServiceType {

    /**
     * 指定允许的类型，默认为全部
     */
    ProjectServiceTypeEnum[] types() default {};

    String message() default "invalid type";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
