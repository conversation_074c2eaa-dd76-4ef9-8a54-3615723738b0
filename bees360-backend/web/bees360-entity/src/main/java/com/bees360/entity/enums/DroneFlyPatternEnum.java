package com.bees360.entity.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DroneFlyPatternEnum implements BaseCodeEnum {

    /**
     * 自动飞行
     */
    AUTOMATICALLY_FLY(1, "Automatically Fly"),

    /**
     * 手动飞行
     */
    MANUALLY_FLY(2, "Manually Fly"),

    /**
     * 自动或者手动飞行
     */
    AUTOMATICALLY_OR_MANUALLY_FLY(3, "Automatically or manually Fly");
    DroneFlyPatternEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    private int code;
    private String display;
    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }
}
