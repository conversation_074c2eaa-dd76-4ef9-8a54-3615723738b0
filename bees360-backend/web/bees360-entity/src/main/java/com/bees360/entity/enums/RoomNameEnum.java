package com.bees360.entity.enums;

import org.apache.commons.collections4.map.CaseInsensitiveMap;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringTokenizer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @see com.bees360.internal.ai.entity.enums.images.RoomNameTagEnum
 * 房间名称
 */
public enum RoomNameEnum {
    BEDROOM(1, "Bedroom", "bed"),
    BATHROOM(2, "Bathroom", "bath"),
    DINING_ROOM(3, "Dining Room", "dining"),
    LIVING_ROOM(4, "Living Room", "living"),
    FAMILY_ROOM(5, "Family Room", "family"),
    OFFICE_ROOM(6, "Office Room", "office"),
    STUDY_ROOM(7, "Study Room", "study"),
    KITCH<PERSON>(8, "Kitchen", "kitchen"),
    CLOSET(9, "Closet", "closet"),
    LAUNDRY_ROOM(10, "Laundry Room", "laundry"),
    STAIRCASE(11, "Staircase", "staircase"),
    CORRIDOR(12, "Corridor", "corridor"),
    GARAGE(13, "Garage", "garage"),
    UTILITY_ROOM(14, "Utility Room", "utility"),
    MEDIA_ROOM(15, "Media Room", "media"),
    BREAKFAST_ROOM(16, "Breakfast Room", "breakfast"),
    OTHER_ROOM(17, "Other Room", null),
    ATTIC(18, "Attic", "attic"),
    ;

    private final int code;
    private final String display;
    private final String key;

    RoomNameEnum(int code, String display, String key) {
        this.code = code;
        this.display = display;
        this.key = key;
    }

    public static RoomNameEnum findByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
            .filter(o -> Objects.equals(code, o.getCode()))
            .findFirst()
            .orElse(OTHER_ROOM);
    }

    /** object list */
    public static List<String> objectIdList() {
        return List.of("Electrical_Panel_1", "Water_Heater_1");
    }

    /**
     *
     * 分词查找RoomName，只要找到关键字匹配，就可以找到相应的Enum
     *
     * @return RoomNameEnum
     */
    public static RoomNameEnum findByRoomName(String roomName) {
        if (null == roomName) {
            return null;
        }
        Map<String, RoomNameEnum> roomNameTagEnumMap =
            Arrays.stream(values())
                .collect(
                    Collectors.toMap(
                        RoomNameEnum::getKey, Function.identity(), (a, b) -> b));
        CaseInsensitiveMap<String, RoomNameEnum> map =
            new CaseInsensitiveMap<>(roomNameTagEnumMap);

        String room = roomName.replaceAll("(?i)(room)", " ");
        StringTokenizer st = new StringTokenizer(room, " \t\n\r\f/|,");
        while (st.hasMoreElements()) {
            RoomNameEnum roomNameEnum = map.get((String) st.nextElement());
            if (null != roomNameEnum) {
                return roomNameEnum;
            }
        }
        return OTHER_ROOM;
    }

    public int getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }

    public String getKey() {
        return key;
    }
}
