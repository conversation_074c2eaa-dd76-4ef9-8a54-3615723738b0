package com.bees360.entity.util;

/**
 * <AUTHOR>
 * @date 2020/02/25 22:15
 */
public class StringFieldUtil {

    public static String join(String delimit, char ... chs) {
        if(chs == null) {
            return null;
        }
        int length = chs.length;
        if(length == 0) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        result.append(chs[0]);
        for (int i = 1; i < length; i ++) {
            result.append(delimit).append(chs[i]);
        }
        return result.toString();
    }

    public static String joinAndWrap(String delimit, char ... chs) {
        if(chs == null) {
            return null;
        }
        if(chs.length == 0) {
            return "";
        }
        return delimit + join(delimit, chs) + delimit;
    }
}
