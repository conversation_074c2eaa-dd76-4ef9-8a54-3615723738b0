package com.bees360.entity.enums;

public enum RoofEstimatedAreaEnum implements BaseCodeEnum {
	AREA_0_40SQ(1,"less than 4000 sqft", 0, 4_000),
	AREA_40SQ_70SQ(2,"4000 to 7000 sqft", 4_000, 7_000),
	AREA_70SQ_150SQ(3,"7000 to 15000 sqft", 7_000, 15_000),
    AREA_150SQ_INFINITY(4,"larger than 15000 sqft", 15_000, Integer.MAX_VALUE)
    ;

	private final int code;
	private final String display;
	private final int lowerLimit;
	private final int upperLimit;

	RoofEstimatedAreaEnum(int code, String display, int lowerLimit, int upperLimit) {
		this.code = code;
		this.display = display;
		this.lowerLimit = lowerLimit;
		this.upperLimit = upperLimit;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public int getLowerLimit() {
		return lowerLimit;
	}

	public int getUpperLimit() {
		return upperLimit;
	}

	public static boolean exist(int code) {
		return getEnum(code) != null;
	}

	public static RoofEstimatedAreaEnum getEnum(int code) {
		RoofEstimatedAreaEnum[] areaEnums = RoofEstimatedAreaEnum.values();
		for(RoofEstimatedAreaEnum areaEnum: areaEnums){
			if(areaEnum.getCode() == code){
				return areaEnum;
			}
		}
		return null;
	}

	public static RoofEstimatedAreaEnum getEnumByValue(double value) {
		RoofEstimatedAreaEnum[] areaEnums = RoofEstimatedAreaEnum.values();
		for(RoofEstimatedAreaEnum areaEnum: areaEnums){
			if(areaEnum.getLowerLimit() < value && value <= areaEnum.getUpperLimit()){
				return areaEnum;
			}
		}
		return null;
	}

	private static void show() {
		System.out.println("|name|code|");
		System.out.println("|----|----|");
		for(RoofEstimatedAreaEnum e: RoofEstimatedAreaEnum.values()) {
			System.out.println("|" + e.getDisplay() + "|" + e.getCode() + "|");
		}
	}

	public static void main(String[] args) {
		show();
	}
}
