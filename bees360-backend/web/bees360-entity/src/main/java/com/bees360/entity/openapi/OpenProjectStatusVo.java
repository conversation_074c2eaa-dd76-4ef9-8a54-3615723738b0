package com.bees360.entity.openapi;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/01/19 18:55
 */
@Data
@NoArgsConstructor
public class OpenProjectStatusVo {
    private long id;
    private String status;

    @Data
    @NoArgsConstructor
    public static class OpenProjectStatusListVoWrapper {
       private List<OpenProjectStatusVo> project;

       public OpenProjectStatusListVoWrapper(List<OpenProjectStatusVo> statusVos) {
           this.project = statusVos;
       }

       public OpenProjectStatusListVoWrapper(OpenProjectStatusVo... statusVos) {
           this(new ArrayList<>(Arrays.asList(statusVos)));
       }
    }
}
