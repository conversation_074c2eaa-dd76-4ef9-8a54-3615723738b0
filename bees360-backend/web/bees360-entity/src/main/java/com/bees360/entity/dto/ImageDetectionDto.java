package com.bees360.entity.dto;

import java.util.List;
import java.util.Set;

import com.bees360.entity.BaseImageAnnotation;

public class ImageDetectionDto {
	/** 新增的Annotation **/
	private List<ImageAnnotationCellDto> annotationsAdded;
	/** 要删除的Annotation的id列表，js的long类型无法兼容java的long类型，这里用String接收 **/
	private Set<String> annotationsRemovedIds;
	/** 是否要将对一张图片的操作映射到其他图片 **/
	private Boolean sync;

	public boolean isEmpty() {
		return (annotationsAdded == null || annotationsAdded.isEmpty())
				&& (annotationsRemovedIds == null || annotationsRemovedIds.isEmpty());
	}

	//** getter and setter **//
	public List<ImageAnnotationCellDto> getAnnotationsAdded() {
		return annotationsAdded;
	}
	public void setAnnotationsAdded(List<ImageAnnotationCellDto> annotationsAdded) {
		this.annotationsAdded = annotationsAdded;
	}
	public Set<String> getAnnotationsRemovedIds() {
		return annotationsRemovedIds;
	}
	public void setAnnotationsRemovedIds(Set<String> annotationsRemovedIds) {
		this.annotationsRemovedIds = annotationsRemovedIds;
	}
	public Boolean getSync() {
		return sync == null ? true: sync;
	}
	public void setSync(Boolean sync) {
		this.sync = sync;
	}

	@Override
	public String toString() {
		return "ImageDetectionDto [annotationsAdded=" + annotationsAdded
				+ ", annotationsRemovedIds=" + annotationsRemovedIds + ", sync=" + sync + "]";
	}
}
