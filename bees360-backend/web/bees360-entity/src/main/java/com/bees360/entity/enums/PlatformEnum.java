package com.bees360.entity.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum PlatformEnum implements BaseCodeEnum {

    /**
     * web 平台
     */
    WEB(1, "Web Platform"),

    /**
     * android 平台
     */
    ANDROID(2, "Android Platform"),

    /**
     * ios 平台
     */
    IOS(3, "IOS Platform");
    PlatformEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    private int code;
    private String display;
    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }
}
