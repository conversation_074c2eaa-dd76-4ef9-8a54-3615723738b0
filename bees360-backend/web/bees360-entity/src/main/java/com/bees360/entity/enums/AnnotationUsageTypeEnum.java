package com.bees360.entity.enums;

public enum AnnotationUsageTypeEnum implements BaseCodeEnum{
	DAMAGE_ANNOTATION(1, "Damage Annotation"),
	SCREENSHOT_RACTANGLE(2, "Screenshot Ractangle"),
	FRAME_RACTANGLE(3, "FRAME Rectangle"),
	MAPPING_2D(4, "2D Mapping"),
    MAPPING_3D(5, "3D Mapping"),
    COMPONENT(6, "Component"),
    HAZARD(7, "Hazard")
	;
	private final int code;
	private final String display;

	AnnotationUsageTypeEnum(int code, String display) {
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

}
