package com.bees360.entity.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/10/26 19:07
 */
public enum ProjectSyncPointEnum {

    MANUAL_SYNC(1, "MA<PERSON>AL_SYNC", null),
    IMAGE_UPLOADED(2, "IMAGE_UPLOADED", NewProjectStatusEnum.IMAGE_UPLOADED),
    CUSTOM_CONTACTED(3, "CUSTOM_CONTACTED", NewProjectStatusEnum.CUSTOMER_CONTACTED),
    SITE_INSPECTED(4, "SITE_INSPECTED", NewProjectStatusEnum.SITE_INSPECTED),
    ASSIGNED_TO_PILOT(5, "ASSIGNED_TO_PILOT", NewProjectStatusEnum.ASSIGNED_TO_PILOT),
    IBEES_UPLOADED(6, "IBEES_UPLOADED", NewProjectStatusEnum.IBEES_UPLOADED),
    PROJECT_CREATED(7, "PROJECT_CREATED", NewProjectStatusEnum.PROJECT_CREATED)
    ;

    private final int code;
    private final String type;
    private final NewProjectStatusEnum statusEnum;

    ProjectSyncPointEnum(int code, String type, NewProjectStatusEnum statusEnum){
        this.code = code;
        this.type = type;
        this.statusEnum = statusEnum;
    }
    public int getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    public NewProjectStatusEnum getStatusEnum() {
        return statusEnum;
    }

    public static ProjectSyncPointEnum getEnum(int code) {
        return Stream.of(ProjectSyncPointEnum.values()).filter(o -> Objects.equals(code, o.getCode())).findFirst().orElse(null);
    }

    public static ProjectSyncPointEnum getEnumByType(String syncPoint) {
        return Stream.of(ProjectSyncPointEnum.values()).filter(o -> StringUtils.equals(syncPoint, o.getType())).findFirst().orElse(null);
    }

    public static Integer getNewProjectStatusCode(String syncPoint) {
        ProjectSyncPointEnum projectSyncPointEnum = getEnumByType(syncPoint);
        return projectSyncPointEnum == null ? null : Optional.ofNullable(projectSyncPointEnum.getStatusEnum()).map(NewProjectStatusEnum::getCode).orElse(null);
    }
}
