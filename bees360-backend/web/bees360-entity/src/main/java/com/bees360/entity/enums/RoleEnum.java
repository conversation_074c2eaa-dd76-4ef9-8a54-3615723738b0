package com.bees360.entity.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 *
 * <AUTHOR>
 *
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum RoleEnum implements BaseCodeEnum{
	VISITOR(-2, "Visitor", RoleType.PROJECT, Visual.PUB),   // showldn't map to a bitmap
	CREATOR(-1, "Creator", RoleType.PROJECT, Visual.PUB),   // showldn't map to a bitmap

	HOMEOWNER(0, "Homeowner", RoleType.CAREER, Visual.PUB), // bitmap = 1
	ADJUSTER(1, "Adjuster", RoleType.CAREER, Visual.SIN),  // bitmap = 2
	PILOT(2, "Pilot", RoleType.CAREER, Visual.PUB),        // bitmap = 4
	INSURER(3, "Insurer", RoleType.CAREER, Visual.SIN),    // bitmap = 8
	DISTRIBUTOR(4, "Distributor", RoleType.CAREER, Visual.SIN), // bitmap = 16
	ROOFER(5, "Roofer", RoleType.CAREER, Visual.SIN),           // bitmap = 32
	REVIEWER(6, "Reviewer", RoleType.CAREER, Visual.GOD),       // bitmap = 64
	CONTRACTOR(7, "Contractor", RoleType.CAREER, Visual.SIN),   // bitmap = 128
	PROCESSOR(8, "Processor", RoleType.CAREER, Visual.GOD),     // bitmap = 256
	SALESMAN(9, "Salesman", RoleType.SYSTEM, Visual.GOD),     // bitmap = 512
	OPERATIONS_MANAGER(10, "Operations Manager", RoleType.CAREER, Visual.PUB),     // bitmap = 1f024
    DESK_ADJUSTER(11, "Desk Adjuster", RoleType.CAREER, Visual.PUB),     // bitmap = 2048
	UNDERWRITER(13, "Underwriter", RoleType.CAREER, Visual.PUB),     // bitmap = 8192

	// <EMAIL>: add other roles
	SUPERADMIN(58, "Super Admin", RoleType.SYSTEM, Visual.GOD), // bitmap = 288230376151711744
	COMPANY_ADMIN(59, "Company Admin", RoleType.SYSTEM, Visual.GOD),      // bitmap = 576460752303423488 Company admin is for mamagement of his company affairs
	COMPANY_CUSTOMER_ADMIN(60, "Company Customer Admin", RoleType.SYSTEM, Visual.GOD),      // bitmap = 1152921504606846976 Company Customer Admin is for management of company customer
	DB_ADMIN(61, "DB Admin", RoleType.SYSTEM, Visual.GOD),      // bitmap = 2305843009213693952
	ADMIN(62, "Admin", RoleType.SYSTEM, Visual.GOD),            // bitmap = 4611686018427387904
	PRODUCER(12, "Producer", RoleType.CAREER, Visual.GOD),
	SCHEDULER(14, "Scheduler", RoleType.CAREER, Visual.GOD),
    FIELD_COORDINATOR(16, "Operation Coordinator", RoleType.CAREER, Visual.GOD),
    FIELD_MANAGER(18, "Operation Manager", RoleType.CAREER, Visual.GOD),
	;
	// 请不要使用 63, 负数的或运算在mysql中的结果与java的不同，
	// 比如在mysql中：-9223372036854775808 | 261 = 9223372036854776069，而在java中为：-9223372036854775547

    public static final String AUTHORITY_PREFIX = "ROLE_";

    public static enum RoleType {
		PROJECT,
		CAREER,
		SYSTEM
	}

	private static enum Visual {
		PUB, // public can view public
		SIN, // single can view public and himself
		GOD  // god can view all
		;
		public boolean canView(Visual visual) {
			if(visual == null) {
				return false;
			}
			switch(this) {
			case PUB: return visual == PUB;
			case SIN: return visual == PUB;
			case GOD: return true;
			default: return false;
			}
		}
	}


	public static Set<Integer> PROJECT_ROLES_VIEW_ALL_REPORTS = new HashSet<Integer>(
			Arrays.asList(RoleEnum.REVIEWER.getCode(), RoleEnum.ADJUSTER.getCode(), RoleEnum.PROCESSOR.getCode()));

	private final int roleId;
	private final String display;
	private final RoleType type;
	private final Visual visual;
	// <EMAIL>: it's necessary that to add a attribute `code` to record its bitmap?

	private static Map<Integer, RoleEnum> roleMap = new HashMap<Integer, RoleEnum>();

	static {
		for(RoleEnum r: RoleEnum.values()) {
			roleMap.put(r.getCode(), r);
		}
	}

	RoleEnum(int roleId, String display, RoleType type, Visual visual){
		this.roleId = roleId;
		this.display = display;
		this.type = type;
		this.visual = visual;
	}

	@Override
	@JsonValue
	public int getCode(){
		return roleId;
	}

	public int getRoleId(){
		return roleId;
	}

	@Override
	public String getDisplay(){
		return display;
	}

	public String getAuthorityDisplay() {
	    return AUTHORITY_PREFIX + name();
    }

	public RoleType getType() {
		return type;
	}

	public static List<RoleEnum> values(RoleType type) {
		RoleEnum[] roles = RoleEnum.values();
		if(type == null) {
			return new ArrayList<RoleEnum>(Arrays.asList(roles));
		}
		List<RoleEnum> roleList = new ArrayList<RoleEnum>();
		for(RoleEnum role: roles) {
			if(role.getType() == type) {
				roleList.add(role);
			}
		}
		return roleList;
	}

	public long getBitMap(){
		if(type == RoleType.PROJECT) {
			return 0;
		}
		long bit = 1L;
		for(int i = 0; i < roleId; i ++){
			bit <<= 1;
		}
		return bit;
	}

	public String toBitMapString(){
		if(type == RoleType.PROJECT) {
			return "0";
		}
		StringBuffer str = new StringBuffer();
		str.append('1');
		for(int i = 0; i < roleId; i ++){
			str.append('0');
		}
		return str.toString();
	}

	public static RoleEnum stringToRole(String roleStr){
		if(roleStr == null || roleStr.isEmpty()) {
			return null;
		}
		RoleEnum[] roles = RoleEnum.values();
		String strLower = roleStr.toLowerCase();
		for(RoleEnum role: roles){
			String display = role.getDisplay().toLowerCase();
			if(display.equals(strLower) || display.replaceAll(" ", "").equals(strLower)){
				return role;
			}
		}
		return null;
	}

	public static List<RoleEnum> listRoles(long roles){
		ArrayList<RoleEnum> list = new ArrayList<RoleEnum>();
		long bit = roles;
		bit = 1L;
		for(int i = 0; i < 64; i ++){
			if((bit & roles) == bit){
				RoleEnum role = RoleEnum.getEnum(i);
				if(role != null) {
					list.add(role);
				}
			}
			bit <<= 1;
		}
		return list;
	}

    public static long roleToBitMap(List<RoleEnum> roles){
        long roleBitMap = 0;
        for (RoleEnum role: roles) {
            roleBitMap |= role.getBitMap();
        }
        return roleBitMap;
    }

	public static RoleEnum getEnum(int code){
		return roleMap.get(code);
	}

	public static boolean exits(int code){
		return roleMap.get(code) != null;
	}

	public static List<RoleEnum> rolesVisible(int roleCode) {
		RoleEnum role = RoleEnum.getEnum(roleCode);
		return role == null? new ArrayList<RoleEnum>(): role.roleVisible();
	}

	public List<RoleEnum> roleVisible() {
		List<RoleEnum> roles = new ArrayList<RoleEnum>();
		for(RoleEnum role: RoleEnum.values()) {
			if(this.visual.canView(role.visual)){
				roles.add(role);
			}
		}
		return roles;
	}

	public static void main(String[] args){
		show();
	}

	private static void show() {
		System.out.println("|name|code|role type| bitmap |");
		System.out.println("|----|----|---|---|");
		for(RoleEnum role: RoleEnum.values()) {
            System.out.println(
                    "|"
                            + role.getDisplay()
                            + "|"
                            + role.getCode()
                            + "|"
                            + role.getType()
                            + "|"
                            + role.getBitMap()
                            + "|");

		}
	}

	public static boolean canViewAllReport(Integer role) {
		return PROJECT_ROLES_VIEW_ALL_REPORTS.contains(role);
	}
}
