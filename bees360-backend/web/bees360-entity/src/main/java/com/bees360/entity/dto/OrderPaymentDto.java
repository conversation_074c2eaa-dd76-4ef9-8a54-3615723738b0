package com.bees360.entity.dto;
import java.io.Serial;
import java.io.Serializable;

import com.bees360.entity.vo.CreditCardVo;
import com.bees360.entity.vo.OrderVo;

public class OrderPaymentDto implements Serializable{
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;

	private long paymentId;

	private long userId;

	private double paidServiceFeeAmount;
	private double tax;
	private double totalFeeAmount;

	private String paymentMethod;

	//credit card infomation
	private CreditCardVo creditCard;

	//banking or check
	private String routingNumber;
	private String accountNumber;
	private String currency;

	//fee type
	private int serviceFeeType;
	private long paidTime;

	private String successUrl;
	private String cancelUrl;

	//order infomation
	private OrderVo order;

	//temp
	private long projectId;
	private String address;

	//2018-12-08,add serviceFeeTypeName for payment record page
	private String serviceFeeTypeName;

	public long getPaymentId() {
		return paymentId;
	}

	public void setPaymentId(long paymentId) {
		this.paymentId = paymentId;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public double getPaidServiceFeeAmount() {
		return paidServiceFeeAmount;
	}

	public void setPaidServiceFeeAmount(double paidServiceFeeAmount) {
		this.paidServiceFeeAmount = paidServiceFeeAmount;
	}

	public String getPaymentMethod() {
		return paymentMethod;
	}

	public void setPaymentMethod(String paymentMethod) {
		this.paymentMethod = paymentMethod;
	}

	public CreditCardVo getCreditCard() {
		return creditCard;
	}

	public void setCreditCard(CreditCardVo creditCard) {
		this.creditCard = creditCard;
	}

	public String getRoutingNumber() {
		return routingNumber;
	}

	public void setRoutingNumber(String routingNumber) {
		this.routingNumber = routingNumber;
	}

	public String getAccountNumber() {
		return accountNumber;
	}

	public void setAccountNumber(String accountNumber) {
		this.accountNumber = accountNumber;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public long getPaidTime() {
		return paidTime;
	}

	public void setPaidTime(long paidTime) {
		this.paidTime = paidTime;
	}

	public String getSuccessUrl() {
		return successUrl;
	}

	public void setSuccessUrl(String successUrl) {
		this.successUrl = successUrl;
	}

	public String getCancelUrl() {
		return cancelUrl;
	}

	public void setCancelUrl(String cancelUrl) {
		this.cancelUrl = cancelUrl;
	}

	public OrderVo getOrder() {
		return order;
	}

	public void setOrder(OrderVo order) {
		this.order = order;
	}

	public double getTax() {
		return tax;
	}

	public void setTax(double tax) {
		this.tax = tax;
	}

	public double getTotalFeeAmount() {
		return totalFeeAmount;
	}

	public void setTotalFeeAmount(double totalFeeAmount) {
		this.totalFeeAmount = totalFeeAmount;
	}

	public int getServiceFeeType() {
		return serviceFeeType;
	}

	public void setServiceFeeType(int serviceFeeType) {
		this.serviceFeeType = serviceFeeType;
	}

	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getServiceFeeTypeName() {
		return serviceFeeTypeName;
	}

	public void setServiceFeeTypeName(String serviceFeeTypeName) {
		this.serviceFeeTypeName = serviceFeeTypeName;
	}
}
