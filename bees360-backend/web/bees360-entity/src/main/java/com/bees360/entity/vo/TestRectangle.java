package com.bees360.entity.vo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.bees360.entity.dto.DamageMeasurementFrame;
import com.bees360.entity.dto.Point;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.OrientationEnum;

public class TestRectangle implements View{
	private String annotationId;
	private int facetId;
	private Map<String, Point> frame;
	private Map<String, Object> orientation;
	private List<Map<String, Object>> damageCount;

	public TestRectangle(DamageMeasurementFrame measurementFrame) {
		this.annotationId = measurementFrame.getAnnotationId();
		this.facetId = measurementFrame.getFacetId();
		setFrame(measurementFrame);
		setOrientation(measurementFrame.getOrientation());
		setDamageCount(measurementFrame);
	}
	/* getter */
	public int getFacetId() {
		return facetId;
	}
	public String getAnnotationId() {
		return annotationId;
	}

	public void setAnnotationId(String annotationId) {
		this.annotationId = annotationId;
	}

	public Map<String, Point> getFrame() {
		return frame;
	}
	private void setFrame(DamageMeasurementFrame measurementFrame) {
		this.frame = new HashMap<String, Point>();
		this.frame.put("p1", measurementFrame.getP1());
		this.frame.put("p2", measurementFrame.getP2());
		this.frame.put("p3", measurementFrame.getP3());
		this.frame.put("p4", measurementFrame.getP4());
	}
	public Map<String, Object> getOrientation() {
		return orientation;
	}
	private void setOrientation(OrientationEnum orientation) {
		Map<String, Object> orientationMap = new HashMap<String, Object>();
		if(orientation != null) {
			orientationMap.put("code", orientation.getCode());
			orientationMap.put("name", orientation.getDisplay());
		}
		this.orientation = orientationMap;
	}
	public List<Map<String, Object>> getDamageCount() {
		return damageCount;
	}
	private void setDamageCount(DamageMeasurementFrame measurementFrame) {
		List<Map<String,Object>> damageCount = new ArrayList<Map<String,Object>>();
		for(Entry<Integer, Integer> entry: measurementFrame.getDamageCount().entrySet()) {
			ClaimTypeEnum type = ClaimTypeEnum.getEnum(entry.getKey());
			if(type == null) {
				continue;
			}
			Map<String, Object> damageCountEle = new HashMap<String, Object>();
			damageCountEle.put("code", type.getCode());
			damageCountEle.put("type", type.getDisplay());
			damageCountEle.put("count", entry.getValue());
			damageCount.add(damageCountEle);
		}
		this.damageCount = damageCount;
	}
	@Override
	public String toString() {
		return "TestRectangle [annotationId=" + annotationId + ", facetId=" + facetId + ", frame=" + frame
				+ ", orientation=" + orientation + ", damageCount=" + damageCount + "]";
	}
}
