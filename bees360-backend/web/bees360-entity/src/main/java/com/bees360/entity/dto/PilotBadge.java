package com.bees360.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PilotBadge {
    private long userId;
    /**
     * 是否颁发claim徽章
     * 表明飞手是否具有飞行Claim Case的资质
     */
    private Boolean hasClaimBadge;

    /**
     * 是否颁发underwriting徽章
     * 表明飞手是否具有飞行Underwriting Case的资质
     */
    private Boolean hasUnderwritingBadge;

    /**
     * 是否颁发underwriting徽章
     * 表明飞手是否具有飞行Underwriting Case的资质
     */
    private Boolean hasMagicplanBadge;

}
