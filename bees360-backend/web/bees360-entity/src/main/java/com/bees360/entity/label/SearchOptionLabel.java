package com.bees360.entity.label;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

@Data
public class SearchOptionLabel {

    private String id;

    private String userId;

    /**
     * where label belongs to, ie, IO,WEB,AI
     */
    private String domain;

    /**
     * display name of the label
     */
    private String name;

    /**
     * an api path or a phrase related to a UI module use to distinct a label
     */
    private String key;

    /**
     * the content of the search option
     */
    private String query;

    /**
     * about the detail of the searching label
     */
    private String description;

    /**
     * use for ordering label
     */
    private Integer order = 0;

}
