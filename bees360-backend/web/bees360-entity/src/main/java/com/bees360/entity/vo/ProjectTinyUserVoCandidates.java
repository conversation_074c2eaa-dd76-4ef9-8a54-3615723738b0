package com.bees360.entity.vo;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/04/09 17:10
 */
@Data
public class ProjectTinyUserVoCandidates {

    private List<UserTinyVo> pilots = Collections.emptyList();

    private List<UserTinyVo> processors = Collections.emptyList();

    private List<UserTinyVo> adjusters = Collections.emptyList();

    private List<UserTinyVo> reviewers = Collections.emptyList();

    private List<UserTinyVo> deskAdjusters = Collections.emptyList();

    private List<UserTinyVo> underwriter = Collections.emptyList();

}
