package com.bees360.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/8/6 5:55 下午
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageUploadStatusVo {
    private int uploadStatus;

    private boolean droneImagePartiallyUploaded;

    private boolean addressPartiallyVerified;

    private boolean mobileImagePartiallyUploaded;

    /**
     * 飞手的form(问卷） task是否还在进行中
     */
    private boolean formInProcess;
}
