package com.bees360.entity;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

public class OnsiteReportElement implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

	/**
	 * primary key
	 */
	private long id;
	/**
	 * project id
	 */
	private long projectId;
	/**
	 * the component id
	 */
	private Integer componentId;
	/**
	 * it must be assigned a value through the Enum RealtimeElementTypeEnum.
	 */
	private int elementType;
	/**
	 * the title of the image
	 */
	private String title;
	/**
	 * image id
	 */
	private String imageId;
	/**
	 * the parent id
	 */
	private Long parentId;
	/**
	 * it must be assigned a value through the Enum getReportTypeEnum.
	 */
	private int reportType;

	/**
	 * 0 is report element image, 1 is bees360 go app image
	 */
	private int sourceType;

	/**
	 * crop id
	 */
	private Long annotationId;

	/**
	 * 3d crop id
	 */
	private Long annotation3dId;

	private List<CustomizedReportElement> customizedElements;

	public OnsiteReportElement() {
		super();
	}

	public OnsiteReportElement(long projectId, int elementType, String title, String imageId, Long parentId,
			int reportType) {
		super();
		this.projectId = projectId;
		this.elementType = elementType;
		this.title = title;
		this.imageId = imageId;
		this.parentId = parentId;
		this.reportType = reportType;
	}

	public OnsiteReportElement(long projectId, int elementType, String title, String imageId, Long parentId,
			int reportType, Long annotationId) {
		super();
		this.projectId = projectId;
		this.elementType = elementType;
		this.title = title;
		this.imageId = imageId;
		this.parentId = parentId;
		this.reportType = reportType;
		this.annotationId = annotationId;
	}

	public OnsiteReportElement(long projectId, Integer componentId, int elementType, String title, String imageId,
			Long parentId, int reportType, Long annotationId, Long annotation3dId, int sourceType) {
		super();
		this.projectId = projectId;
		this.componentId = componentId;
		this.elementType = elementType;
		this.title = title;
		this.imageId = imageId;
		this.parentId = parentId;
		this.reportType = reportType;
		this.annotationId = annotationId;
		this.annotation3dId = annotation3dId;
		this.sourceType = sourceType;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public int getElementType() {
		return elementType;
	}

	public void setElementType(int elementType) {
		this.elementType = elementType;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getImageId() {
		return imageId;
	}

	public void setImageId(String imageId) {
		this.imageId = imageId;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public int getReportType() {
		return reportType;
	}

	public void setReportType(int reportType) {
		this.reportType = reportType;
	}

	public Integer getComponentId() {
		return componentId;
	}

	public void setComponentId(Integer componentId) {
		this.componentId = componentId;
	}

	public int getSourceType() {
		return sourceType;
	}

	public void setSourceType(int sourceType) {
		this.sourceType = sourceType;
	}

	public List<CustomizedReportElement> getCustomizedElements() {
		return customizedElements;
	}

	public void setCustomizedElements(List<CustomizedReportElement> customizedElements) {
		this.customizedElements = customizedElements;
	}

	public Long getAnnotationId() {
		return annotationId;
	}

	public void setAnnotationId(Long annotationId) {
		this.annotationId = annotationId;
	}

	public Long getAnnotation3dId() {
		return annotation3dId;
	}

	public void setAnnotation3dId(Long annotation3dId) {
		this.annotation3dId = annotation3dId;
	}

}
