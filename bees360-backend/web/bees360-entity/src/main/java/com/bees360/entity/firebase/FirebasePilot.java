package com.bees360.entity.firebase;

import com.google.cloud.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/8/26 5:26 下午
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FirebasePilot {
    private String email;

    private String firstName;

    private FirebasePilotAddress homeAddress;

    private String lastName;

    private String phone;

    private String avatar;

    private double travelRadius;

    /**
     * @see PilotAccessLevelEnum
     */
    private int accessLevel;

    /**
     * faa 证书的上传和审核记录
     */
    private List<FirebaseFAA> faa;

    /**
     * 飞手保单的上传和审核记录
     */
    private List<FirebaseLiabilityInsurance> liabilityInsurance;

    /**
     * Submitted，Rejected, Approved 三个状态（默认已提交）
     */
    private String status;

    /**
     * status 为 Rejected 时，审核人员存的拒绝原因
     */
    private String rejectReason;

    /**
     * web 端使用的 userId，如：'10237'，区别于 firestore 的 uid
     */
    private String webUserId;

    /**
     * Firestore 的时间戳格式，该条文档的创建时间
     */
    private Timestamp createTime;

    /**
     * Firestore 的时间戳格式，该条文档最近一次的更新时间
     */
    private Timestamp updateTime;

    /**
     * 飞手是否已请假，默认为false
     * true 已请假
     */
    private Boolean isOnLeave = false;
}
