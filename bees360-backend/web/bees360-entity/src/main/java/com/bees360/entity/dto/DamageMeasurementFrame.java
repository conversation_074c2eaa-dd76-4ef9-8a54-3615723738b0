package com.bees360.entity.dto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.OrientationEnum;
import com.bees360.entity.vo.TestRectangle;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class DamageMeasurementFrame {

	private String annotationId;
	private int facetId;

	private Point p1;
	private Point p2;
	private Point p3;
	private Point p4;

	private OrientationEnum orientation;

	private Map<Integer, Integer> damageCount;


	public void setP(int index, Point p){
		switch(index){
		case 1:{
			p1 = p;
			break;
		}
		case 2:{
			p2 = p;
			break;
		}
		case 3:{
			p3 = p;
			break;
		}
		case 4:{
			p4 = p;
			break;
		}
		default:{
			return;
		}
		}
	}

	public List<Point> points() {
		List<Point> points = new ArrayList<Point>(4);
		points.add(p1);
		points.add(p2);
		points.add(p3);
		points.add(p4);
		return points;
	}

	public Point center(){
		double centerPointX = 0;
		double centerPointY = 0;
		List<Point> points = points();
		for(Point point: points){
			centerPointX += point.getX();
			centerPointY += point.getY();
		}
		centerPointX /= points.size();
		centerPointY /= points.size();
		return new Point(centerPointX, centerPointY);
	}

	public TestRectangle toTestRectangle() {
		return new TestRectangle(this);
	}
	/* getter and setter */
	@JsonIgnore
	public Point getP(int index){
		switch(index){
		case 1:{
			return p1;
		}
		case 2:{
			return p2;
		}
		case 3:{
			return p3;
		}
		case 4:{
			return p4;
		}
		default:{
			return null;
		}
		}
	}
	public int getFacetId() {
		return facetId;
	}

	public void setFacetId(int facetId) {
		this.facetId = facetId;
	}

	public Point getP1() {
		return p1;
	}

	public void setP1(Point p1) {
		this.p1 = p1;
	}

	public Point getP2() {
		return p2;
	}

	public void setP2(Point p2) {
		this.p2 = p2;
	}

	public Point getP3() {
		return p3;
	}

	public void setP3(Point p3) {
		this.p3 = p3;
	}

	public Point getP4() {
		return p4;
	}

	public void setP4(Point p4) {
		this.p4 = p4;
	}

	public OrientationEnum getOrientation() {
		return orientation;
	}

	public void setOrientation(OrientationEnum orientation) {
		this.orientation = orientation;
	}

	public Map<Integer, Integer> getDamageCount() {
		return damageCount;
	}

	public void setDamageCount(Map<Integer, Integer> damageCount) {
		this.damageCount = damageCount;
	}
	public String getAnnotationId() {
		return annotationId;
	}

	public void setAnnotationId(String annotationId) {
		this.annotationId = annotationId;
	}

	@Override
	public Object clone(){
		DamageMeasurementFrame frame = new DamageMeasurementFrame();
		frame.setP1(p1.clone());
		frame.setP2(p2.clone());
		frame.setP3(p3.clone());
		frame.setP4(p4.clone());
		frame.setOrientation(orientation);
		Map<Integer, Integer> newDamageCount = new HashMap<Integer, Integer>();
		for(Integer key : damageCount.keySet()){
			newDamageCount.put(key, damageCount.get(key));
		}
		frame.setFacetId(facetId);
		frame.setDamageCount(newDamageCount);
		return frame;
	}

	@Override
	public String toString() {
		return "DamageMeasurementFrame [annotationId=" + annotationId + ", facetId=" + facetId + ", p1=" + p1 + ", p2="
				+ p2 + ", p3=" + p3 + ", p4=" + p4 + ", orientation=" + orientation + ", damageCount=" + damageCount
				+ "]";
	}

}
