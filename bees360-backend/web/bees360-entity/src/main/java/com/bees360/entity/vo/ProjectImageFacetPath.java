package com.bees360.entity.vo;

import com.bees360.entity.dto.Point;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/09/11 15:02
 */
public class ProjectImageFacetPath {
    // facet 的Id
    private int id;
    private int orientation;
    // [x, y]
    private double[] center;
    private List<Point> path;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getOrientation() {
        return orientation;
    }

    public void setOrientation(int orientation) {
        this.orientation = orientation;
    }

    public double[] getCenter() {
        return center;
    }

    public void setCenter(double[] center) {
        this.center = center;
    }

    public List<Point> getPath() {
        return path;
    }

    public void setPath(List<Point> path) {
        this.path = path;
    }

    @Override
    public String toString() {
        return "ProjectImageFacetPath{" + "id=" + id + ", orientation=" + orientation + ", center="
            + Arrays.toString(center) + ", path=" + path + '}';
    }
}
