package com.bees360.entity.validate;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import org.apache.commons.lang3.StringUtils;

import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.google.common.collect.Sets;

/**
 * <AUTHOR> Yang
 */
public abstract class ProjectServiceTypeValidator<T> implements ConstraintValidator<ProjectServiceType, T> {

    private Set<ProjectServiceTypeEnum> types;

    @Override
    public void initialize(ProjectServiceType constraintAnnotation) {
        types = Arrays.stream(ProjectServiceTypeEnum.values()).collect(Collectors.toSet());
    }

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        if ((value instanceof String string) && StringUtils.isBlank(string)) {
            return true;
        }
        ProjectServiceTypeEnum type = toType(value);
        return type != null && (allowAll() || types.contains(type));
    }

    private boolean allowAll() {
        return types.isEmpty();
    }

    protected abstract ProjectServiceTypeEnum toType(T value);
}
