package com.bees360.entity.dto;

import lombok.Data;
import org.springframework.util.StringUtils;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/16 9:21 AM
 **/
@Data
public class ImageInfoRecord {
    private Long projectId;
    private long id;
    @NotEmpty
    private String originalFileName;
    @NotNull
    private Long fileSize;
    @NotNull
    private Integer imageType;
    @NotNull
    private Integer partialType;
    @NotNull
    private Integer fileSourceType;

    /**
     * 是否上传到了S3
     */
    private boolean uploadedToS3;

    /**
     * 图片md5值 32位小写表示
     */
    private String md5;

    public void setMd5(String md5) {
        if (StringUtils.hasText(md5)) {
            this.md5 = md5.toLowerCase();
        }
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getOriginalFileName() {
        return originalFileName;
    }

    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public Integer getImageType() {
        return imageType;
    }

    public void setImageType(Integer imageType) {
        this.imageType = imageType;
    }

    public Integer getPartialType() {
        return partialType;
    }

    public void setPartialType(Integer partialType) {
        this.partialType = partialType;
    }

    public Integer getFileSourceType() {
        return fileSourceType;
    }

    public void setFileSourceType(Integer fileSourceType) {
        this.fileSourceType = fileSourceType;
    }

    public boolean isUploadedToS3() {
        return uploadedToS3;
    }

    public void setUploadedToS3(boolean uploadedToS3) {
        this.uploadedToS3 = uploadedToS3;
    }
}
