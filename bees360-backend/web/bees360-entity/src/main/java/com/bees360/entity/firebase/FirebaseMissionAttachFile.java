package com.bees360.entity.firebase;

import com.bees360.entity.enums.MissionAttachFileTypeEnum;
import com.google.cloud.Timestamp;
import lombok.Data;

/**
 * 附件
 */
@Data
public class FirebaseMissionAttachFile {

    private String id;

    private String url;

    /**
     * 图片base64数据，上传完毕后会删除
     */
    private String base64Data;

    /** 附件上传时间 **/
    private Timestamp uploadTime;

    /**
     * 类型
     * @see MissionAttachFileTypeEnum
     */
    private String type;

    /** 文件名，含后缀 **/
    private String originalFileName;

    private String localPath;

    /** true:已删除，false：未删除 **/
    private Boolean deleted;

    private Timestamp createTime;

}
