package com.bees360.entity.firebase;

import com.bees360.activity.ActivitySyncLog;
import com.bees360.activity.Comment;
import com.bees360.activity.Message.CommentMessage;
import com.bees360.activity.impl.AbstractActivity;
import com.bees360.project.base.Message;
import com.bees360.user.Pilot;
import com.bees360.util.DateTimes;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;

import jakarta.annotation.Nullable;
import java.time.Instant;
import java.util.List;
import java.util.Set;

public class FirebaseActivity extends AbstractActivity {
    @Getter private String scopeType;
    @Getter private Long bundleId;
    private String projectId; // 评论所属项目
    @Getter private String action; // 活动动作类型 类型分为CREATE, DELETE 和 CHANGE,
    private long createdAt; // 活动创建时间，时间戳
    @Getter private String createdBy; // 活动的触发人，默认用户是beespilot
    @Getter private String entityId; // 所属项目的实体
    @Getter private String entityType; // 该实体为Project, 或者为Project所属的一级实体。 比如项目Pilot，Image，Batch等
    @Getter private int entityCount; // 获取实体数量，有些实体与项目的对应关系为一对多
    private String filedName; //  获取被修改的字段名 当{@link Activity#getAction()}为CHANGE时, 表示字段被修改. 字段被修改会记录老值和新值, 老值和新值不能相同.
    // 用来兼容正确的单词拼写
    @Getter private String fieldName; //  获取被修改的字段名 当{@link Activity#getAction()}为CHANGE时, 表示字段被修改. 字段被修改会记录老值和新值, 老值和新值不能相同.
    private String filedType; //获取字段值类型，主要用于在前端显示.
    // 用来兼容正确的单词拼写
    @Getter private String fieldType; //获取字段值类型，主要用于在前端显示.
    @Getter private String value;
    @Getter private String oldValue;
    @Getter private String source; // 活动来源，默认值
    @Getter private final String level = "INFO"; // 活动来源，默认值
    @Getter private String entityUserGroupId;
    @Getter @Setter
    private Message.ProjectType projectType;
    private String pilotId;
    private String operationsManager;
    private String comment;
    @Getter private String entityName;
    @Getter private String fieldDisplayName;
    @Getter private Set<String> visibility;

    @Getter private List<FirebaseActivityAttachment> attachment;

    @Override
    public Pilot getPilot() {
        com.bees360.user.Message.UserMessage pilot =
                com.bees360.user.Message.UserMessage.getDefaultInstance();
        com.bees360.user.Message.UserMessage om =
                com.bees360.user.Message.UserMessage.getDefaultInstance();
        if (Strings.isNotBlank(pilotId)) {
            pilot = pilot.toBuilder().setId(pilotId).build();
        }
        if (Strings.isNotBlank(operationsManager)) {
            om = pilot.toBuilder().setId(operationsManager).build();
        }
        return Pilot.from(
                com.bees360.user.Message.PilotMessage.newBuilder()
                        .setPilotUser(pilot)
                        .setOperationsManager(om)
                        .build());
    }

    @Override
    public String getId() {
        return null;
    }

    @Nullable
    @Override
    public Comment getComment() {
        if (Strings.isBlank(comment)) {
            return null;
        }
        var builder = CommentMessage.newBuilder();
        builder.setProjectId(getProjectId())
                .setContent(comment)
                .setCreatedBy(
                        com.bees360.user.Message.UserMessage.newBuilder()
                                .setId(getCreatedBy())
                                .build())
                .setSource(getSource())
                .setCreatedAt(DateTimes.toTimestamp(getCreatedAt()));
        if (CollectionUtils.isNotEmpty(attachment)) {
            attachment.forEach(
                    attach ->
                            builder.addAttachment(
                                    CommentMessage.Attachment.newBuilder()
                                            .setFilename(attach.getFilename())
                                            .setUrl(attach.getKey())));
        }
        return Comment.from(builder.build());
    }

    @Override
    public Iterable<? extends ActivitySyncLog> getAllSyncLog() {
        return Lists.newArrayList();
    }

    public Instant getCreatedAt() {
        if (createdAt == 0) {
            createdAt = System.currentTimeMillis();
        }
        return Instant.ofEpochMilli(createdAt);
    }

    @Override
    public String getFiledName() {
        if (fieldName != null) {
            return fieldName;
        }
        return filedName;
    }

    @Override
    public String getFiledType() {
        if (fieldType != null) {
            return fieldType;
        }
        return filedType;
    }

    public Long getProjectId() {
        return Long.valueOf(projectId);
    }

    @Data
    private static class FirebaseActivityAttachment {
        private String filename;
        private String key;
        private String url;
    }
}
