package com.bees360.entity.vo;

import java.util.ArrayList;
import java.util.List;
import com.bees360.entity.Project;
import com.bees360.entity.enums.ReportTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

// <EMAIL> I am still consider whether ProjectInitializationVo is necessary or not
@Data
@NoArgsConstructor
public class ProjectInitializationVo {
	private long projectId;
	// type of loss
	private int claimType;
	// date of loss
	private Long damageEventTime;
	private Long inspectionTime;
	// type of house
	private Integer projectType;
	// policy holder name
	private String assetOwnerName;
	// policy holder phone
	private String assetOwnerPhone;
	// policy holder email
	private String assetOwnerEmail;

	private String address;
	private String city;
	private String state;
	private String zipCode;
	private String country;

	private double lng;
	private double lat;

	private String contacterName;
	private String contacterPhone;
	private String contacterEmail;
	private int roofEstimatedAreaItem;

	//private int reportServiceOption;
	private List<Integer> reportTypes;

	private boolean needPilot;

	public Project toProject() {
		Project project = new Project();
		project.setProjectId(projectId);
		project.setClaimType(claimType);
		project.setDamageEventTime(damageEventTime);
		project.setInspectionTime(inspectionTime);
		project.setProjectType(projectType);
		project.setAssetOwnerName(assetOwnerName);
		project.setAssetOwnerPhone(assetOwnerPhone);
		project.setAssetOwnerEmail(assetOwnerEmail);
		project.setAddress(address);
		project.setCity(city);
		project.setState(state);
		project.setZipCode(zipCode);
		project.setCountry(country);
		project.setLat(lat);
		project.setLng(lng);
		project.setContacterName(contacterName);
		project.setContacterPhone(contacterPhone);
		project.setContacterEmail(contacterEmail);
		project.setRoofEstimatedAreaItem(roofEstimatedAreaItem);
		//project.setReportServiceOption(reportServiceOption);
		project.setReportTypes(reportTypes);
		project.setNeedPilot(needPilot);
		return project;
	}

	public List<ReportTypeEnum> getReportTypeEnums() {
		List<ReportTypeEnum> reportTypeEnums = new ArrayList<>();
		if(reportTypes != null) {
			for(int reportType : reportTypes) {
				reportTypeEnums.add(ReportTypeEnum.getEnum(reportType));
			}
		}
		return reportTypeEnums;
	}

}
