package com.bees360.entity.enums;

import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/10/26 19:07
 */
public enum ProjectReportRecordEnum {

    HOVER(1, "HOVER" ),
    P<PERSON><PERSON><PERSON>(2, "PLNAR"),
    PROPERTY_IMAGE_REPORT(3, "PIR"),
    PREMIUM_DAMAGE_ASSESSMENT_REPORT(4, "DAR"),
    CLIENT_RECEIVED(5, "CLIENT_RECEIVED")
    ;

    private final int code;
    private final String type;

    ProjectReportRecordEnum(int code, String type){
        this.code = code;
        this.type = type;
    }
    public int getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    public static ProjectReportRecordEnum getEnumByType(String type) {
        return Stream.of(ProjectReportRecordEnum.values())
            .filter(o -> Objects.equals(type, o.getType()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("ProjectReportRecordEnum not found for type " + type));
    }

}
