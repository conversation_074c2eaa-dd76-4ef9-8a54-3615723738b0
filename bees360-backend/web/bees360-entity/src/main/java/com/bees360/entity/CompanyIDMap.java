package com.bees360.entity;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2020/8/5 1:29 下午
 **/
@Validated
@Data
public class CompanyIDMap {
    /**
     * Insurance Risk Services, Inc. 公司的ID
     */
    @NotNull
    Long Insurance_Risk_Services_Inc_ID = 2096L;

    /**
     * Associated Services Inspections - Commercial 公司的ID
     */
    @NotNull
    Long Associated_Services_Inspections_Commercial_ID = 1313L;

    /**
     * Allstate 公司的ID
     */
    @NotNull
    Long Allstate_ID = 1736L;

    /**
     * Swift 公司的ID
     */
    @NotNull
    Long Swift_ID = 2294L;

    /**
     * Allied Trust 公司ID
     */
    @NotNull
    Long Allied_Trust_ID = 2382L;

    @NotNull
    Long Velocity_ID = 2297L;

    @NotNull
    Long Swyfft_Underwriting;

    @NotNull
    Long Swyfft_Homeowner_Insurance;

    @NotNull
    Long UPC_Insurance;

    @NotNull
    Long Centauri_Insurance;

    @NotNull
    Long Security_First_Florida;

    @NotNull
    Long GeoVera_Holdings_Underwriting;

    @NotNull
    Long Olympus;

    @NotNull
    Long SageSure;

    @NotNull
    Long Mdow;

    @NotNull Long Canopius = 2776L;

    @NotNull
    Long berkleyOne = 2757L;

    @NotNull
    private Long theHartford;
}
