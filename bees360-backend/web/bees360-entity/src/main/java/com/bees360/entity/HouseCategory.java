package com.bees360.entity;

import com.bees360.entity.dto.HouseCategoryDto;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class HouseCategory {

	private long id;
	private int categoryIndex;
	private String category;
	private String categoryCode;
	private int subcategoryIndex;
	private String subcategory;
	private String selector;
	private String description;
	private String additional;
	private int type;

	public HouseCategory() {
		super();
	}

	public HouseCategory(HouseCategoryDto houseCategoryDto) {
		super();
		this.id = houseCategoryDto.getId();
		this.categoryIndex = houseCategoryDto.getCategoryIndex();
		this.category = houseCategoryDto.getCategory();
		this.categoryCode = houseCategoryDto.getCategoryCode();
		this.subcategoryIndex = houseCategoryDto.getSubcategoryIndex();
		this.subcategory = houseCategoryDto.getSubcategory();
		this.selector = houseCategoryDto.getSelector();
		this.description = houseCategoryDto.getDescription();
		this.additional = houseCategoryDto.getAdditional();
		this.type = houseCategoryDto.getType();
	}

	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getCategory() {
		return category;
	}
	public void setCategory(String category) {
		this.category = category;
	}

	@JsonIgnore
	public String getCategoryCode() {
		return categoryCode;
	}
	public void setCategoryCode(String categoryCode) {
		this.categoryCode = categoryCode;
	}

	@JsonIgnore
	public String getSelector() {
		return selector;
	}
	public void setSelector(String selector) {
		this.selector = selector;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}

	@JsonIgnore
	public int getCategoryIndex() {
		return categoryIndex;
	}
	public void setCategoryIndex(int categoryIndex) {
		this.categoryIndex = categoryIndex;
	}

	@JsonIgnore
	public String getAdditional() {
		return additional;
	}
	public void setAdditional(String additional) {
		this.additional = additional;
	}

	@JsonIgnore
	public int getSubcategoryIndex() {
		return subcategoryIndex;
	}
	public void setSubcategoryIndex(int subcategoryIndex) {
		this.subcategoryIndex = subcategoryIndex;
	}
	public String getSubcategory() {
		return subcategory;
	}
	public void setSubcategory(String subcategory) {
		this.subcategory = subcategory;
	}
	@Override
	public String toString() {
		return "HouseCategory [id=" + id + ", categoryIndex=" + categoryIndex + ", category=" + category
				+ ", categoryCode=" + categoryCode + ", subcategoryIndex=" + subcategoryIndex + ", subcategory="
				+ subcategory + ", selector=" + selector + ", description=" + description + ", additional=" + additional
				+ "]";
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}

}
