package com.bees360.entity.vo;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.DirectionEnum;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Slf4j
public class ProjectImageRealTimeVo implements Cloneable{
	Logger logger = LoggerFactory.getLogger(ProjectImageRealTimeVo.class);

	ProjectImage image;
	/**
	 * it must be assigned a value through the Enum RealtimeElementTypeEnum.
	 */
	private int elementType;

	private static final ProjectImageRealTimeVo DEFAULT_IMAGE;

	static {
		DEFAULT_IMAGE = generateDefaultImage();
	}

	public ProjectImageRealTimeVo() {
		super();
		image = new ProjectImage();
	}

	public ProjectImageRealTimeVo(String imageId) {
		super();
		image = new ProjectImage();
		image.setImageId(imageId);
	}

	public ProjectImageRealTimeVo(ProjectImage image) {
		this.image = image;
	}

	public TinyImageVo toTinyImageVo(){
		return image.toTinyImageVo();
	}

	public ProjectImage toImage() {
		return image;
	}

	public static ProjectImageRealTimeVo defaultImage(){
		ProjectImageRealTimeVo image = (ProjectImageRealTimeVo)DEFAULT_IMAGE.clone();
		if(image == null){
			return generateDefaultImage();
		}
		return image;
	}

	private static ProjectImageRealTimeVo generateDefaultImage() {
		ProjectImageRealTimeVo image = new ProjectImageRealTimeVo();
		image.setFileName("");
		image.setFileNameMiddleResolution(null);
		image.setFileNameLowerResolution(null);
		image.setDirection(DirectionEnum.DEFAULT);
		image.setFileSize(0);
		image.setFileSourceType(FileSourceTypeEnum.DEFAULT);
		image.setGpsLocationLatitude(0.0);
		image.setGpsLocationLongitude(0.0);
		image.setImageHeight(0);
		image.setImageWidth(0);
		image.setImageType(ImageTypeEnum.DEFAULT);
		image.setFileSourceType(FileSourceTypeEnum.DEFAULT);
		image.setDeleted(false);
		return image;
	}

	@Override
	public int hashCode(){
        return this.image.getImageId().hashCode();
	}

	@Override
	public boolean equals(Object obj){
		if(this == obj){
			return true;
		}
		if(!(obj instanceof ProjectImageRealTimeVo)){
			return false;
		}
		ProjectImageRealTimeVo image = (ProjectImageRealTimeVo)obj;
		return Objects.equals(this.image.getImageId(), image.getImageId());
	}

	@Override
	public Object clone(){
		ProjectImageRealTimeVo image = null;
		try {
			image = (ProjectImageRealTimeVo)super.clone();
		} catch (CloneNotSupportedException e) {
			logger.error("Fail to clone ProjectImage.", e);
		}
		return image;
	}

	// getter and setter

	public long getShootingTime() {
		return image.getShootingTime();
	}

	public void setShootingTime(long shootingTime) {
		this.image.setShootingTime(shootingTime);
	}

	public String getImageId() {
		return image.getImageId();
	}

	public void setImageId(String imageId) {
		image.setImageId(imageId);
	}

	public String getFileName() {
		return image.getFileName();
	}

	public void setFileName(String fileName) {
		image.setFileName(fileName);
	}

	public String getFileNameLowerResolution() {
		return image.getFileNameLowerResolution();
	}

	public void setFileNameLowerResolution(String fileNameLowerResolution) {
		image.setFileNameLowerResolution(fileNameLowerResolution);
	}

	public String getFileNameMiddleResolution() {
		return image.getFileNameMiddleResolution();
	}

	public void setFileNameMiddleResolution(String fileNameMiddleResolution) {
		image.setFileNameMiddleResolution(fileNameMiddleResolution);
	}

	public String getAnnotationImage() {
		return image.getAnnotationImage();
	}

	public void setAnnotationImage(String annotationImage) {
		image.setAnnotationImage(annotationImage);
	}

	public long getFileSize() {
		return image.getFileSize();
	}

	public void setFileSize(long fileSize) {
		image.setFileSize(fileSize);
	}

	public long getUserId() {
		return image.getUserId();
	}

	public void setUserId(long userId) {
		image.setUserId(userId);
	}

	public long getUploadTime() {
		return image.getUploadTime();
	}

	public void setUploadTime(long uploadTime) {
		image.setUploadTime(uploadTime);
	}

	public String getOriginalFileName() {
		return image.getOriginalFileName();
	}

	public void setOriginalFileName(String originalFileName) {
		image.setOriginalFileName(originalFileName);
	}

	public int getFileSourceType() {
		return image.getFileSourceType();
	}

	public void setFileSourceType(int fileSourceType) {
		image.setFileSourceType(fileSourceType);
	}

	public double getGpsLocationLongitude() {
		return image.getGpsLocationLongitude();
	}

	public void setGpsLocationLongitude(double gpsLocationLongitude) {
		image.setGpsLocationLongitude(gpsLocationLongitude);
	}

	public double getGpsLocationLatitude() {
		return image.getGpsLocationLatitude();
	}

	public void setGpsLocationLatitude(double gpsLocationLatitude) {
		image.setGpsLocationLatitude(gpsLocationLatitude);
	}

	public double getRelativeAltitude() {
		return image.getRelativeAltitude();
	}


	public void setRelativeAltitude(double relativeAltitude) {
		image.setRelativeAltitude(relativeAltitude);
	}


	public int getImageHeight() {
		return image.getImageHeight();
	}

	public void setImageHeight(int imageHeight) {
		image.setImageHeight(imageHeight);
	}

	public int getImageWidth() {
		return image.getImageWidth();
	}

	public void setImageWidth(int imageWidth) {
		image.setImageWidth(imageWidth);
	}

	public long getProjectId() {
		return image.getProjectId();
	}

	public void setProjectId(long projectId) {
		image.setProjectId(projectId);
	}

	public int getDirection() {
		return image.getDirection();
	}

	public void setDirection(int direction) {
		image.setDirection(direction);
	}

	public int getImageType() {
		return image.getImageType();
	}

	public void setImageType(int imageType) {
		image.setImageType(imageType);
	}

	public String getImageCategory() {
		return image.getImageCategory();
	}

	public void setImageCategory(String imageCategory) {
		image.setImageCategory(imageCategory);
	}

	@JsonIgnore
	public String getCamPropertyMatrix() {
		return image.getCamPropertyMatrix();
	}

	public void setCamPropertyMatrix(String camPropertyMatrix) {
		image.setCamPropertyMatrix(camPropertyMatrix);
	}

	public boolean getDeleted() {
		return image.getDeleted();
	}

	public void setDeleted(boolean deleted) {
		image.setDeleted(deleted);
	}

	public boolean isManuallyAnnotated() {
		return image.isManuallyAnnotated();
	}

	public void setManuallyAnnotated(boolean manuallyAnnotated) {
		image.setManuallyAnnotated(manuallyAnnotated);
	}

	public String getParentId() {
		return image.getParentId();
	}

	public void setParentId(String parentId) {
		image.setParentId(parentId);
	}

	public int getElementType() {
		return elementType;
	}

	public void setElementType(int elementType) {
		this.elementType = elementType;
	}

	public double getWeight() {
		return image.getWeight();
	}

	public void setWeight(double weight) {
		image.setWeight(weight);
	}

	@Override
	public String toString() {
		return "ProjectImageRealTimeVo [imageId=" + getImageId() + ", fileName=" + getFileName() + ", fileNameMiddleResolution="
				+ getFileNameMiddleResolution() + ", fileNameLowerResolution=" + getFileNameLowerResolution()
				+ ", annotationImage=" + getAnnotationImage() + ", fileSize=" + getFileSize() + ", userId=" + getUserId()
				+ ", uploadTime=" + getUploadTime() + ", originalFileName=" + getOriginalFileName() + ", fileSourceType="
				+ getFileSourceType() + ", gpsLocationLongitude=" + getGpsLocationLongitude() + ", gpsLocationLatitude="
				+ getGpsLocationLatitude() + ", relativeAltitude=" + getRelativeAltitude() + ", imageHeight=" + getImageHeight()
				+ ", imageWidth=" + getImageWidth() + ", projectId=" + getProjectId() + ", direction=" + getDirection()
				+ ", imageType=" + getImageType() + ", imageCategory=" + getImageCategory() + ", camPropertyMatrix="
				+ getCamPropertyMatrix() + ", deleted=" + getDeleted() + ", manuallyAnnotated=" + isManuallyAnnotated()
				+ ", parentId=" + getParentId() + ", elementType=" + elementType + "]";
	}

}
