package com.bees360.entity.vo;


import com.bees360.entity.AddressAirspace;
import com.bees360.entity.ProjectAirspace;
import com.bees360.entity.ProjectMessageVo;
import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.PayStatusEnum;
import com.bees360.entity.enums.ProcessStatusEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.label.ProjectLabel;
import com.bees360.project.Message;
import com.bees360.project.member.Member;
import com.bees360.project.tag.ProjectTag;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class ProjectTinyVo {
	private long projectId;
	private long createdTime;
	private long dueDate;

	private String address;
	private String city;
	private String state;
	private String country;
	private String zipCode;
	private String addressId;

	private String claimNumber;
	private String policyNumber;
    private String catNumber;
    private Integer catLevel;
	private String policyType;
    private Integer projectType;

	private long createdBy;
	private String creatorEmail;
	private String creatorPhone;
	private String creatorName;
	private String creatorCompanyLogo;

	private String mainImage;

	// related to status in ProcessStatus
	private int latestStatus;
	private Integer projectStatus;
	private Long projectStatusTime;

	private boolean isRead;
	private boolean isBooking;
	private boolean needPilot;
	private boolean hasPilot;

	private long inspectionTypes;

	private List<String> inspectionTypeList;

	private List<IdNameDto> roles;

	private double gpsLocationLongitude;

	private double gpsLocationLatitude;

	/**
	 * Same as com.bees360.flyzone.FlyZoneType::getCode()
	 */
	private int flyZoneType;

    private String timeZone;

	private String assetOwnerName;
	private String assetOwnerPhone;
	private String assetOwnerEmail;

	/** process by 的公司名称 **/
	private Long repairCompany;
	private String repairCompanyName;

	private Long insuranceCompany;
	private String insuranceCompanyName;
	private String insuranceCompanyLogo;

	private String pilot;
	private String pilotId;

	private ProjectStatusVo statusProjectCreated;
	private ProjectStatusVo statusCustomerContacted;
	private ProjectStatusVo statusAssignedToPilot;
	private ProjectStatusVo statusSiteInspected;
	private ProjectStatusVo statusReturnToClient;
	private ProjectStatusVo statusFirstReturnToClient;
	private ProjectStatusVo statusLastReturnToClient;
	private ProjectStatusVo statusClientReceived;
	private ProjectStatusVo statusImageUploaded;
	private ProjectStatusVo statusIBEESUploaded;

    private Integer projectCreatedCount;
    private Integer customerContactedCount;
    private Integer assignedToPilotCount;
    private Integer siteInspectedCount;
    private Integer imageUploadedCount;
    private Integer returnToClientCount;
    private Integer clientReceivedCount;

    private List<ProjectMessageVo> projectMessage;

    private Integer claimType;

    private Integer serviceType;

    private long inspectionTime;

    private int payStatus;
    private String payStatusName;

    private String inspectionNumber;

    private LocalDate policyEffectiveDate;

    private Integer daysOld;

    private String batchNo;

    private BigDecimal batchTotalPay;

    private BigDecimal riskScore;

    private boolean dueSoon;
    private boolean overdue;

    private LocalDate planPaymentDate;

    private boolean gpsIsApproximate;

    private List<ProjectLabel> projectLabels;

    private List<? extends ProjectTag> projectTags;

    // the latest user modify tag
    private String latestModifyTagUserName;
    private Long latestModifyTagTime;

    private Long operationsManagerId;
    /**
     * 联系户主的时间
     */
    private Instant customerContactedTime;
    /**
     * 首次尝试联系户主的时间
     */
    private Instant initialCustomerContactTime;

    // 与hive location 的距离miles
    private Float hiveDistance;

    private String claimNote;

    private Integer numberOfOutbuildings;

    private Integer numberOfInteriorRooms;

    private String externalAdjusterName;

    private Long scheduledTime;

    // 这个数据需要通过activity来计算
    private Long scheduledTimeChangedCount;

    private Long inspectionDueDate;

    private boolean deleted;

    private Integer newDaysOld;

    private String projectState;
    private String projectStateChangeReason;
    private String projectStateChangeReasonId;
    private String changeReasonGroup;

    private List<? extends Member> members;

    private List<String> similarProjects;
    private Map<String, List<Message.ProjectSimilarType>> similarProjectTypes;

    private String operatingCompany;

    // Aloft Airspace Info (Deprecated)
    private AddressAirspace airspace;

	// Aloft Airspace Info (New)
	private ProjectAirspace projectAirspace;

    private String agentName;
    private String agentPhone;
    private String agentEmail;

    public String getStatus() {
		ProcessStatusEnum status = ProcessStatusEnum.getEnum(latestStatus);
		return status == null? "": status.getDisplay();
	}

    public String getPayStatusName() {
        PayStatusEnum status = PayStatusEnum.getEnum(payStatus);
        return status == null? "": status.getDisplay();
    }

	@JsonIgnore
	public boolean getRead() {
		return isRead;
	}

	public boolean getIsBooking() {
		return isBooking;
	}

	public void setRolesWithRoleEnum(List<RoleEnum> roles) {
		if(roles == null) {
			setRoles(null);
			return;
		}
		List<IdNameDto> idNameRoles = new ArrayList<IdNameDto>();
		for(RoleEnum role: roles) {
			idNameRoles.add(new IdNameDto(role.getCode(), role.getDisplay()));
		}
		setRoles(idNameRoles);
	}

	@JsonIgnore
	public Integer getProjectStatus() {
		return projectStatus;
	}

	public Integer getProjectStatusCode() {
		NewProjectStatusEnum status = NewProjectStatusEnum.getEnum(projectStatus);
		return status == null? null: status.getCode();
	}

	public String getProjectStatusName() {
		NewProjectStatusEnum status = NewProjectStatusEnum.getEnum(projectStatus);
		return status == null? "": status.getDisplay();
	}

    public Integer getInspectionPurposeTypeCode() {
        ClaimTypeEnum claimTypeEnum = claimType == null? null: ClaimTypeEnum.getEnum(claimType);
        InspectionPurposeTypeEnum code = InspectionPurposeTypeEnum.getEnum(claimTypeEnum);
        return code == null? InspectionPurposeTypeEnum.UNKNOWN.getCode():
            code.getCode();
    }

    public String getServiceTypeName() {
        ProjectServiceTypeEnum serviceTypeEnum = serviceType == null? null: ProjectServiceTypeEnum.getEnum(serviceType);
        return serviceTypeEnum == null? "": serviceTypeEnum.getValue();
    }

    public String getInspectionPurposeTypeName() {
        ClaimTypeEnum claimTypeEnum = claimType == null? null: ClaimTypeEnum.getEnum(claimType);
        InspectionPurposeTypeEnum code = InspectionPurposeTypeEnum.getEnum(claimTypeEnum);
        return code == null? InspectionPurposeTypeEnum.UNKNOWN.getDisplay():
            code.getDisplay();
    }

}
