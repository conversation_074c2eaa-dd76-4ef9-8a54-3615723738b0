package com.bees360.entity.vo;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @contributors
 * @date Apr 12, 2018 3:24:13 PM
 * @param <E>
 */
public class PageResult<E> {

	private List<E> items;

	private Pagination page;

	public PageResult() {
	}

	public PageResult(int pageIndex, int pageSize) {
	    this.page = new Pagination(pageIndex, pageSize);
	    this.items  = new ArrayList<>();
    }

	public PageResult(List<E> items, Pagination page) {
		this.items = items;
		this.page = page;
	}

	public PageResult<E> empty() {
	    if(page != null) {
	        page.empty();
	    } else {
	        page = Pagination.getEmpty(0, 0);
	    }
		items = new ArrayList<E>();
		return this;
	}

	public Iterator<E> iterator(){
		return items.iterator();
	}

	// getter and setter
	public List<E> getItems() {
		return items;
	}
	public void setItems(List<E> items) {
		this.items = items;
	}
	public Pagination getPage() {
		return page;
	}
	public void setPage(Pagination page) {
		this.page = page;
	}
}
