package com.bees360.entity.dto;

import java.util.List;

public class ADMessage {
	private long projectId;
	private String jobId;
	private int jobStatus;
	private String msg;
	private Integer imageSourceType;
	private List<AnnotationsInImage> data;

	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public String getJobId() {
		return jobId;
	}
	public void setJobId(String jobId) {
		this.jobId = jobId;
	}
	public int getJobStatus() {
		return jobStatus;
	}
	public void setJobStatus(int jobStatus) {
		this.jobStatus = jobStatus;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public Integer getImageSourceType() {
		return imageSourceType;
	}
	public void setImageSourceType(Integer imageSourceType) {
		this.imageSourceType = imageSourceType;
	}
	public List<AnnotationsInImage> getData() {
		return data;
	}
	public void setData(List<AnnotationsInImage> data) {
		this.data = data;
	}
	public boolean isSuccess() {
		return jobStatus == 2;
	}

	@Override
	public String toString() {
		return "ADMessage [projectId=" + projectId + ", jobId=" + jobId + ", jobStatus=" + jobStatus + ", msg=" + msg
				+ ", fileSourceType=" + imageSourceType + ", data=" + data + "]";
	}
}
