package com.bees360.entity.dto;

import java.util.List;

import com.bees360.entity.CustomizedReportElement;

public class ReportElementDto {
	private String imageId;
	private Integer componentId;
	private List<String> parentIds;
	private Long screenshotId;
	private String caption;
	private int type;
	private String associatedImageCaption;
	private int sourceType;

	private List<CustomizedReportElement> customizedElements;

	private List<ReportElementDto> cropImageList;

	public String getImageId() {
		return imageId;
	}

	public void setImageId(String imageId) {
		this.imageId = imageId;
	}

	public String getCaption() {
		return caption;
	}

	public void setCaption(String caption) {
		this.caption = caption;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public String getAssociatedImageCaption() {
		return associatedImageCaption;
	}

	public void setAssociatedImageCaption(String associatedImageCaption) {
		this.associatedImageCaption = associatedImageCaption;
	}

	public List<ReportElementDto> getCropImageList() {
		return cropImageList;
	}

	public void setCropImageList(List<ReportElementDto> cropImageList) {
		this.cropImageList = cropImageList;
	}

	public Long getScreenshotId() {
		return screenshotId;
	}

	public void setScreenshotId(Long screenshotId) {
		this.screenshotId = screenshotId;
	}

	public Integer getComponentId() {
		return componentId;
	}

	public void setComponentId(Integer componentId) {
		this.componentId = componentId;
	}

	public List<String> getParentIds() {
		return parentIds;
	}

	public void setParentIds(List<String> parentIds) {
		this.parentIds = parentIds;
	}

	public int getSourceType() {
		return sourceType;
	}

	public void setSourceType(int sourceType) {
		this.sourceType = sourceType;
	}

	@Override
	public String toString() {
		return "ReportElementDto [imageId=" + imageId + ", componentId=" + componentId + ", parentIds=" + parentIds
				+ ", screenshotId=" + screenshotId + ", caption=" + caption + ", type=" + type
				+ ", associatedImageCaption=" + associatedImageCaption + ", sourceType=" + sourceType
				+ ", cropImageList=" + cropImageList + "]";
	}

	public List<CustomizedReportElement> getCustomizedElements() {
		return customizedElements;
	}

	public void setCustomizedElements(List<CustomizedReportElement> customizedElements) {
		this.customizedElements = customizedElements;
	}

}
