package com.bees360.entity.enums;

import java.util.Objects;
import lombok.Getter;

public enum SystemTypeEnum {
    BEES360("Bees360"),
    BEES_PILOT("BeesPilot"),
    BEES_AI("BeesAI")
    ;
    @Getter
    final String type;

    SystemTypeEnum(String type) {
        this.type = type;
    }

    public static SystemTypeEnum getEnum(String type) {
        SystemTypeEnum[] systemTypes = SystemTypeEnum.values();
        for (SystemTypeEnum systemType : systemTypes) {
            if (Objects.equals(type, systemType.getType())) {
                return systemType;
            }
        }
        return null;
    }
}
