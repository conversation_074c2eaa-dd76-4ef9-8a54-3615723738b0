package com.bees360.entity.enums;

import com.bees360.entity.CompanyIDMap;
import com.bees360.entity.Project;

import java.util.Arrays;

/**
 * 项目图片的整体上传状态
 */
public enum ImageUploadStatusEnum implements BaseCodeEnum {
    /**
     * 二进制表示为：0
     * 没上传过照片
     */
    WAIT_TO_UPLOAD(0, "Wait to Upload", (1 << 31) - 1),

    /**
     * 二进制表示为：01
     * 确认 VERIFY_ADDRESS 部分照片上传完成
     */
    VERIFY_ADDRESS_PARTIALLY_UPLOADED(1, "Verify Address Partially Uploaded", 3),

    /**
     * 二进制表示为：11
     * 确认 VERIFY_ADDRESS 全部照片上传完成
     */
    VERIFY_ADDRESS_ALL_UPLOADED(3, "Verify Address Uploaded", 3),

    /**
     * 二进制表示为： 01 00
     * 确认 MOBILE 照片部分上传
     */
    MOBILE_IMAGE_PARTIALLY_UPLOADED(4, "Verify Mobile Partially Uploaded", 12),

    /**
     * 二进制表示为：11 00
     * 确认 MOBILE 照片已全部上传
     */
    MOBILE_IMAGE_ALL_UPLOADED(12, "Mobile Image all Uploaded", 12),

    /**
     * 二进制表示为：01 00 00
     * 确认 DRONE 照片部分上传
     */
    DRONE_IMAGE_PARTIALLY_UPLOADED(16, "Drone Image Partially Uploaded", 48),

    /**
     * 二进制表示为：11 00 00
     * 确认 DRONE 照片已全部上传
     */
    DRONE_IMAGE_ALL_UPLOADED(48, "Drone Image all Uploaded", 48),

    /**
     * 二制表示为: 11 11 11
     * Drone 和 Mobile照片全部上传完成
     */
    ALL_IMAGE_UPLOADED(63, "All Image Uploaded", 48);

    final private int code;
    final private String display;
    final private int mask;
    ImageUploadStatusEnum(int status, String desc, int mask) {
        this.code = status;
        this.display = desc;
        this.mask = mask;
    }
    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getDisplay() {
        return this.display;
    }

    public static boolean contains(int code) {
        return getEnum(code) != null;
    }

    public int getMask() {
        return mask;
    }

    /**
     * 如果新增状态，数据库中的已全部上传完成会变成未上传完成，所以该值不应该用来判断整体状态，但是可以用来判断部分状态是否已完成。
     * 如：isMobileImageUploaded = (status & MOBILE_IMAGE_ALL_UPLOADED) == MOBILE_IMAGE_ALL_UPLOADED
     * 如：isMobilePartiallyImageUploaded = (status & MOBILE_IMAGE_ALL_UPLOADED) == MOBILE_IMAGE_PARTIALLY_UPLOADED
     *
     * @return if all image are already uploaded
     */
    public static int getNeedAllImageUpdateStatus(CompanyIDMap companyIDMap, Project project) {
        // TODO companyIDMap 暂时没有用到
        if (project != null &&
            project.getServiceType() != null &&
            project.getServiceType() == ProjectServiceTypeEnum.ROOF_ONLY_UNDERWRITING.getCode()) {
            return VERIFY_ADDRESS_ALL_UPLOADED.getCode() | DRONE_IMAGE_ALL_UPLOADED.getCode();
        }
        return Arrays.stream(values()).mapToInt(value -> value.code).reduce(0, (a, b) -> a | b);
    }
    public static ImageUploadStatusEnum getEnum(int code) {
        return Arrays.stream(values()).filter(status -> status.getCode() == code).findFirst().orElse(null);
    }

    static public boolean isMobileImageAllUploaded(int status) {
        return (status & MOBILE_IMAGE_ALL_UPLOADED.mask) == MOBILE_IMAGE_ALL_UPLOADED.code;
    }

    static public boolean isDroneImageAllUpload(int status) {
        return (status & DRONE_IMAGE_ALL_UPLOADED.mask) == DRONE_IMAGE_ALL_UPLOADED.code;
    }
    static public boolean isAddressVerifiedAllUpload(int status) {
        return (status & VERIFY_ADDRESS_ALL_UPLOADED.mask) == VERIFY_ADDRESS_ALL_UPLOADED.code;
    }
    static public boolean isMobileImagePartiallyUploaded(int status) {
        return (status & MOBILE_IMAGE_PARTIALLY_UPLOADED.mask) == MOBILE_IMAGE_PARTIALLY_UPLOADED.code;
    }

    static public boolean isDroneImagePartiallyUpload(int status) {
        return (status & DRONE_IMAGE_PARTIALLY_UPLOADED.mask) == DRONE_IMAGE_PARTIALLY_UPLOADED.code;
    }
    static public boolean isAddressVerifiedPartiallyUpload(int status) {
        return (status & VERIFY_ADDRESS_PARTIALLY_UPLOADED.mask) == VERIFY_ADDRESS_PARTIALLY_UPLOADED.code;
    }

    /**
     * 根据 ImageUploadStatusEnum 设置 图片上传状态
     *
     * @param originalStatus
     * @param status
     * @return
     */
    static public int setStatusByEnum(int originalStatus, ImageUploadStatusEnum status) {
        return (originalStatus & (~status.getMask())) | status.getCode();
    }


    public static void main(String[] args) {
        int origianalStatus = getNeedAllImageUpdateStatus(null, null);
        System.out.println(origianalStatus);
        origianalStatus = setStatusByEnum(origianalStatus, DRONE_IMAGE_ALL_UPLOADED);
        boolean isDroneImageAllUpload = isDroneImageAllUpload(origianalStatus);
        System.out.println(isDroneImageAllUpload);
        origianalStatus = setStatusByEnum(origianalStatus, MOBILE_IMAGE_ALL_UPLOADED);
        boolean isMobileImageAllUploaded = isMobileImageAllUploaded(origianalStatus);
        System.out.println(isMobileImageAllUploaded);
        origianalStatus = setStatusByEnum(origianalStatus, VERIFY_ADDRESS_ALL_UPLOADED);
        boolean isAddressVerifiedAllUpload = isAddressVerifiedAllUpload(origianalStatus);
        System.out.println(isAddressVerifiedAllUpload);

        origianalStatus = 0;
        origianalStatus = setStatusByEnum(origianalStatus, DRONE_IMAGE_PARTIALLY_UPLOADED);
        isDroneImageAllUpload = isDroneImageAllUpload(origianalStatus);
        System.out.println(isDroneImageAllUpload);
        origianalStatus = setStatusByEnum(origianalStatus, MOBILE_IMAGE_PARTIALLY_UPLOADED);
        isMobileImageAllUploaded = isMobileImageAllUploaded(origianalStatus);
        System.out.println(isMobileImageAllUploaded);
        origianalStatus = setStatusByEnum(origianalStatus, VERIFY_ADDRESS_PARTIALLY_UPLOADED);
        isAddressVerifiedAllUpload = isAddressVerifiedAllUpload(origianalStatus);
        System.out.println(isAddressVerifiedAllUpload);
        int originalStatus = 51;

        int a = (originalStatus & (~DRONE_IMAGE_PARTIALLY_UPLOADED.getMask())) | DRONE_IMAGE_PARTIALLY_UPLOADED.getCode();
    }
}
