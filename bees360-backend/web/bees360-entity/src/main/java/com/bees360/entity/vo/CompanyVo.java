package com.bees360.entity.vo;

import com.bees360.entity.Company;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/12/24 10:31
 */
@Data
public class CompanyVo {
    /**
     * @see Company#getCompanyId()
     */
    private long companyId;
    /**
     * @see Company#getCompanyName()
     */
    private String companyName;
    /**
     * @see Company#getCompanyType()
     */
    private int companyType;
    /**
     * @see Company#getPhone()
     */
    private String phone;
    /**
     * @see Company#getEmail()
     */
    private String email;
    /**
     * @see Company#getContactName()
     */
    private String contactName;
    /**
     * @see Company#getWebsite()
     */
    private String website;
    /**
     * @see Company#getLogo()
     */
    private String logo;
    /**
     * @see Company#getCreatedTime()
     */
    private long createdTime;
    /**
     * @see Company#getUpdatedTime()
     */
    private long updatedTime;
}
