package com.bees360.entity.enums;

/**
 * the bees go app mode type.
 *
 * <AUTHOR>
 */
public enum BeesGoModeEnum implements BaseCodeEnum{
	UNDERWRITING(1, "Underwriting"),
	CLAIM(2, "Claim");

	private final int code;
	private final String display;

	BeesGoModeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public String getDisplay(){
		return display;
	}

	@Override
	public int getCode() {
		return code;
	}

}
