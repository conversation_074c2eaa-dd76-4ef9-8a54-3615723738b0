package com.bees360.entity.enums;

import lombok.Getter;

public enum RemoteConfigParameter {

    CONTACT_QUESTION("contact_question"),
    TASK_CHECKOUT_REASON("task_checkout_reason"),
    TASK("task"),
    IBEES_TASK("ibees_task"),
    QUIZ("quiz"),                                   // 问卷调查题目
    CATEGORY_TO_TAB("category_to_tab"),

    ;
    @Getter
    final String name;

    RemoteConfigParameter(String name) {
        this.name = name;
    }
}
