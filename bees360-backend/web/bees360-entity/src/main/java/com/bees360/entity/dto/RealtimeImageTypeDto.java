package com.bees360.entity.dto;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RealtimeImageTypeDto implements Serializable{
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;

	// it must be assigned a value through the Enum RealtimeElementTypeEnum.
	// 2:Overview, 3:front, 4:back, 5:left, 6:right
	private int type;
	private int count;

	public RealtimeImageTypeDto(){}

	public RealtimeImageTypeDto(int type, int count) {
		super();
		this.type = type;
		this.count = count;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	@Override
	public String toString() {
		return "RealtimeImageTypeDto [type=" + type + ", count=" + count + "]";
	}

}
