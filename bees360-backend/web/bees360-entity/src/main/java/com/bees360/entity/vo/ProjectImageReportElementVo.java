package com.bees360.entity.vo;

import java.io.Serial;
import java.io.Serializable;

public class ProjectImageReportElementVo implements Serializable {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 3479908970606127149L;
	//The foreign key referencing the id in Project
	private long projectId;
	private int componentId;
	private int componentType;
	private int inspectionCategory;
	//It means the relative position of this image for a project:
	//	0: BirdView
	//	1: WestView
	//	2: EastView
	//	3: NorthView
	//	4: SouthView
	//	5: LengthCAD
	//	6: AreaCAD
	//	7: PitchCAD
	//	8: NameCAD
	//	This field may not be used.
	private int relativePositionType;
	private String objectDescriptionJson;
	//The user who adds this segment to the image.
	private long addedBy;
	//The timestamp when this image segment is created.
	private long createdTime;

	private int imageHeight;

	private int imageWidth;

	private String fileName;

	private String fileNameLowerResolution;

	private String fileNameMiddleResolution;

	public ProjectImageReportElementVo() {
		super();
	}

	public ProjectImageReportElementVo(long projectId) {
		super();
		this.projectId = projectId;
	}

	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}

	public long getAddedBy() {
		return addedBy;
	}

	public void setAddedBy(long addedBy) {
		this.addedBy = addedBy;
	}

	public int getRelativePositionType() {
		return relativePositionType;
	}

	public void setRelativePositionType(int relativePositionType) {
		this.relativePositionType = relativePositionType;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFileNameLowerResolution() {
		return fileNameLowerResolution;
	}

	public void setFileNameLowerResolution(String fileNameLowerResolution) {
		this.fileNameLowerResolution = fileNameLowerResolution;
	}

	public String getFileNameMiddleResolution() {
		return fileNameMiddleResolution;
	}

	public void setFileNameMiddleResolution(String fileNameMiddleResolution) {
		this.fileNameMiddleResolution = fileNameMiddleResolution;
	}

	public int getImageHeight() {
		return imageHeight;
	}

	public void setImageHeight(int imageHeight) {
		this.imageHeight = imageHeight;
	}

	public int getImageWidth() {
		return imageWidth;
	}

	public void setImageWidth(int imageWidth) {
		this.imageWidth = imageWidth;
	}

	public int getInspectionCategory() {
		return inspectionCategory;
	}

	public void setInspectionCategory(int inspectionCategory) {
		this.inspectionCategory = inspectionCategory;
	}

	public String getObjectDescriptionJson() {
		return objectDescriptionJson;
	}

	public void setObjectDescriptionJson(String objectDescriptionJson) {
		this.objectDescriptionJson = objectDescriptionJson;
	}

	public int getComponentId() {
		return componentId;
	}

	public void setComponentId(int componentId) {
		this.componentId = componentId;
	}

	public int getComponentType() {
		return componentType;
	}

	public void setComponentType(int componentType) {
		this.componentType = componentType;
	}

	@Override
	public String toString() {
		return "ProjectImageReportElementVo [projectId=" + projectId + ", componentId=" + componentId
				+ ", componentType=" + componentType + ", inspectionCategory=" + inspectionCategory
				+ ", relativePositionType=" + relativePositionType + ", objectDescriptionJson=" + objectDescriptionJson
				+ ", addedBy=" + addedBy + ", createdTime=" + createdTime + ", imageHeight=" + imageHeight
				+ ", imageWidth=" + imageWidth + ", fileName=" + fileName + ", fileNameLowerResolution="
				+ fileNameLowerResolution + ", fileNameMiddleResolution=" + fileNameMiddleResolution + "]";
	}

}
