package com.bees360.entity.query;

import com.bees360.entity.enums.NewProjectStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectFilterQuery {
    private Long insuranceCompany;
    private Long repairCompany;
    private Long materialProviderCompany;
    private Boolean isTestCase;
    private boolean filterInsuredCompanyNull;
    private boolean searchAssetOwnerWithoutPhone;
    private Collection<Integer> projectServiceType;

    /**
     * @see NewProjectStatusEnum#getCode()
     */
    private Collection<Integer> projectStatuses;
    private Collection<Integer> exclusiveStatuses;
    private Long projectStatusTimeStart;
    private Long projectStatusTimeEnd;
    private Long inspectionStartTime;
    private Long inspectionEndTime;

    private Set<Long> projectIds;

    private Long creatorId;
    private Long projectIdStart;
    private Long projectIdEnd;
    public void addProjectStatuses(Integer status) {
        if(projectStatuses == null) {
            projectStatuses = new HashSet<>();
        }
        projectStatuses.add(status);
    }
}
