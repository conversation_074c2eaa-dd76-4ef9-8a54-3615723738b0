package com.bees360.entity.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import com.bees360.entity.dto.ProjectQuizDto;
import com.bees360.entity.dto.ProjectQuizSignatureDto;
import lombok.Data;

import java.util.List;

/**
 * 前端向后端请求生成editor report 的 entity类
 *
 * <AUTHOR>
 * @date 2021/03/1 14:59
 */
@Data
public class ReportTypeKeyVo {

    private long projectId;

    private long time;

    private Boolean replace;

    private String base64Content;

    private int reportType;

    @NotBlank
    @Size(max=256)
    private String key;

    private String htmlFileRootPath;

    private long insuranceCompany;

    private String companyLogo;

    private String insuredName;

    private List<ProjectQuizDto> projectQuizDtoList;

    private List<ProjectQuizSignatureDto> projectQuizSignatureDtoList;

    private String unsignedReason;

    private String invoiceSummary;
}
