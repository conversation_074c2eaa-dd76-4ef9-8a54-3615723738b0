package com.bees360.entity.util;

import com.bees360.entity.dto.IdNameListDto;
import com.bees360.entity.enums.RoleEnum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/01/06 15:41
 */
public class UserCertificateParser {

    private static long EMPTY_ROLES = 0;
    private final static String ROLE_CERTIFICATE_SEPARATOR = ":";
    private final static String CERTIFICATE_SEPARATOR = ",";
    private final static String ROLE_APPLICATIONS_SEPARATOR = ";";

    public static String linkCertificates(int role, List<String> certificates) {
        if(certificates == null || certificates.size() == 0) {
            return "";
        }
        String links = role + ROLE_CERTIFICATE_SEPARATOR + certificates.get(0);
        for(int i = 1; i < certificates.size(); i ++) {
            links += CERTIFICATE_SEPARATOR + certificates.get(i);
        }
        links += ROLE_APPLICATIONS_SEPARATOR;
        return links;
    }

    public static List<IdNameListDto> parseToIdNameList(long roles, String certificateList) {
        Map<RoleEnum, List<String>> certificateMap = parseToRoleMapCertificates(roles, certificateList);

        List<IdNameListDto> roleIdNameLists = new ArrayList<>();
        for(Map.Entry<RoleEnum, List<String>> entry: certificateMap.entrySet()) {
            RoleEnum roleEnum = entry.getKey();
            List<String> certificates = entry.getValue();
            roleIdNameLists.add(new IdNameListDto(roleEnum.getCode(), roleEnum.getDisplay(), certificates));
        }
        return roleIdNameLists;
    }

    /**
     * @param roleCertificates a string with a roleId:[certificates] format
     * @return a string list of certificates
     */
    public static List<String> parseCertificates(String roleCertificates){
        if(roleCertificates == null || roleCertificates.length() == 0) {
            return new ArrayList<>();
        }
        String certificatesLink = roleCertificates.substring(roleCertificates.indexOf(ROLE_CERTIFICATE_SEPARATOR)+1);
        String[] certificates = certificatesLink.split(CERTIFICATE_SEPARATOR);
        List<String> certificatesList = new ArrayList<>();
        for(String certificate: certificates) {
            certificatesList.add(certificate);
        }
        return certificatesList;
    }

    /**
     * parse certificateList into role:[certificate] map
     * @return
     */
    public static Map<Integer, List<String>> parseToRoleMapCertificates(String certificateList){
        Map<Integer, List<String>> result = new HashMap<>();
        if(certificateList == null || certificateList.length() == 0) {
            return result;
        }
        String[] certificateStrs = certificateList.split(ROLE_APPLICATIONS_SEPARATOR);
        for(String roleCertificates: certificateStrs) {
            String[] roleCertificatesPair = roleCertificates.split(ROLE_CERTIFICATE_SEPARATOR);
            int role = Integer.parseInt(roleCertificatesPair[0]);
            List<String> certificates = parseCertificates(roleCertificates);
            result.put(role, certificates);
        }
        return result;
    }

    /**
     * list the roleApplications and each roleApplication will map to its certificates
     * @return
     */
    public static Map<RoleEnum, List<String>> parseToRoleMapCertificates(long roles, String certificateList){
        Map<RoleEnum, List<String>> result = new HashMap<>();
        List<RoleEnum> rolesApplying = RoleEnum.listRoles(roles);
        for(RoleEnum role: rolesApplying) {
            result.put(role, new ArrayList<>());
        }
        if(rolesApplying.isEmpty() || certificateList == null || certificateList.length() == 0){
            return result;
        }
        Map<Integer, List<String>> roleCertificatesPair = parseToRoleMapCertificates(certificateList);
        for(Map.Entry<Integer, List<String>> entry: roleCertificatesPair.entrySet()) {
            RoleEnum role = RoleEnum.getEnum(entry.getKey());
            if(result.containsKey(role)) {
                result.put(role, entry.getValue());
            }
        }
        return result;
    }
}
