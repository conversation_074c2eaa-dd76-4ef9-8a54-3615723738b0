package com.bees360.entity;

import com.bees360.entity.grpc.Timestamp;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportAnnotationImage implements Serializable {

    private long id;

    private long projectId;

    private String imageId;

    private int reportType;

    private int componentId;

    private String caption;

    private String alias;

    private int page;

    private Timestamp createTime;

}
