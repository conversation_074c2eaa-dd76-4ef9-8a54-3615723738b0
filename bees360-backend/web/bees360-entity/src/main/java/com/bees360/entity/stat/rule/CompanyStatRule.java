package com.bees360.entity.stat.rule;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public class CompanyStatRule {

    private String companyName;
    private Integer serviceType;

    private RangeRule<Integer> dueSoon;
    private RangeRule<Integer> overdue;

    public CompanyStatRule (RangeRule<Integer> dueSoon, RangeRule<Integer> overdue){
        this.dueSoon = dueSoon;
        this.overdue = overdue;
    }

    public boolean isDueSoon(Integer daysOld){

        return daysOld != null && dueSoon != null && dueSoon.validate(daysOld);
    }

    public boolean isOverdue(Integer daysOld){

        return daysOld != null && overdue != null && overdue.validate(daysOld);
    }
}
