package com.bees360.entity.enums;

import org.slf4j.Logger;

/**
 * <AUTHOR>
 */
public enum LogTagEnum {

    TRACE {
        @Override
        public void log(Logger logger, String format, Object... params) {
            logger.trace(format, params);
        }
    },

    DEBUG {
        @Override
        public void log(Logger logger, String format, Object... params) {
            logger.debug(format, params);
        }
    },

    INFO {
        @Override
        public void log(Logger logger, String format, Object... params) {
            logger.info(format, params);
        }
    },

    WARN {
        @Override
        public void log(Logger logger, String format, Object... params) {
            logger.warn(format, params);
        }
    },

    ERROR {
        @Override
        public void log(Logger logger, String format, Object... params) {
            logger.error(format, params);
        }
    };

    /**
     *
     * @param logger 日志输出的接口
     * @param format 日志格式，如: `error message: {}`
     * @param params 日志格式中的参数
     */
    public abstract void log(Logger logger, String format, Object... params);
}
