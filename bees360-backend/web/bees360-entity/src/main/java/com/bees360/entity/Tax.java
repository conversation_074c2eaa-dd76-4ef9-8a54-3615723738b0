package com.bees360.entity;

public class Tax {
	private int taxId;
	private String taxName;
	// 注意productType在这里的唯一性问题
	private int productType;
	// 单位为 1%
	private double taxRate;
	private boolean isDeleted;
	private long createdTime;
	private long updatedTime;

	//** getter and setter **//
	public int getTaxId() {
		return taxId;
	}
	public void setTaxId(int taxId) {
		this.taxId = taxId;
	}
	public String getTaxName() {
		return taxName;
	}
	public void setTaxName(String taxName) {
		this.taxName = taxName;
	}
	public int getProductType() {
		return productType;
	}
	public void setProductType(int productType) {
		this.productType = productType;
	}
	public double getTaxRate() {
		return taxRate;
	}
	public void setTaxRate(double taxRate) {
		this.taxRate = taxRate;
	}
	public boolean isDeleted() {
		return isDeleted;
	}
	public void setDeleted(boolean isDeleted) {
		this.isDeleted = isDeleted;
	}
	public long getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}
	public long getUpdatedTime() {
		return updatedTime;
	}
	public void setUpdatedTime(long updatedTime) {
		this.updatedTime = updatedTime;
	}

	@Override
	public String toString() {
		return "Tax [taxId=" + taxId + ", taxName=" + taxName + ", productType=" + productType + ", taxRate=" + taxRate
				+ ", isDeleted=" + isDeleted + ", createdTime=" + createdTime + ", updatedTime=" + updatedTime + "]";
	}
}
