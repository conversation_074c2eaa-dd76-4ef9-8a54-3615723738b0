package com.bees360.entity.enums;

import com.bees360.report.Report;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ReportTypeEnum implements BaseCodeEnum{

	PREMIUM_DAMAGE_ASSESSMENT_REPORT(1, "Premium Damage Assessment Report", "application/pdf", "pdf",
			true, false, true, true, false, true,
			ProjectStatusEnum.DAMAGE_REPORT_SUBMITTED,
			ProjectStatusEnum.DAMAGE_REPORT_GENERATED,
			ProjectStatusEnum.DAMAGE_REPORT_APPROVED,
			ProjectStatusEnum.DAMAGE_REPORT_DISAPPROVED, "DAR"),

	PREMIUM_ROOF_MEASUREMENT_REPORT(2, "Premium Measurement Report", "application/pdf", "pdf",
			true, false, true, true, false, false,
			ProjectStatusEnum.MEASUREMENT_REPORT_SUBMITTED,
			ProjectStatusEnum.MEASUREMENT_REPORT_GENERATED,
			ProjectStatusEnum.MEASUREMENT_REPORT_APPROVED,
			ProjectStatusEnum.MEASUREMENT_REPORT_DISAPPROVED, "PMR"),

	MACRO_XML_REPORT(4, "Macro XML Report", "application/zip", "zip",
			false, true, false, false, false, false,
			null,
			ProjectStatusEnum.MACRO_XML_GENERATED,
			null,
			null, "MXR"),

	QUICK_DAMAGE_ASSESSMENT_REPORT(5, "Preliminary Damage Assessment Report", "application/pdf", "pdf",
			true, false, true, true, false, true,
			ProjectStatusEnum.ONSITE_DAMAGE_REPORT_SUBMITTED,
			ProjectStatusEnum.ONSITE_DAMAGE_REPORT_GENERATED,
			ProjectStatusEnum.ONSITE_DAMAGE_REPORT_APPROVED,
			ProjectStatusEnum.ONSITE_DAMAGE_REPORT_DISAPPROVED, "QDAR"),

	QUICK_ROOF_EVALUATION_REPORT(7, "Highfly Evaluation Report", "application/pdf", "pdf",
			true, false, true, true, false, true,
			ProjectStatusEnum.DAMAGE_ASSESSMENT_REPORT_SUBMITTED,
			ProjectStatusEnum.DAMAGE_ASSESSMENT_REPORT_GENERATED,
			ProjectStatusEnum.DAMAGE_ASSESSMENT_REPORT_APPROVED,
			ProjectStatusEnum.DAMAGE_ASSESSMENT_REPORT_DISAPPROVED, "QRER"),

	REALTIME_ROOF_INSPECTION_REPORT(8, "Real-time Damage Assessment Report", "application/pdf", "pdf",
			true, false, true, true, false, true,
			ProjectStatusEnum.REALTIME_APP_DAMAGE_REPORT_SUBMITTED,
			ProjectStatusEnum.REALTIME_APP_DAMAGE_REPORT_GENERATED,
			ProjectStatusEnum.REALTIME_APP_DAMAGE_REPORT_APPROVED,
			ProjectStatusEnum.REALTIME_APP_DAMAGE_REPORT_DISAPPROVED, "RRIR"),

	SYMBILITY_SCAN_REPORT(9, "Symbility XML Report", "application/xml", "xml",
			false, true, false, false, false, false,
			null,
			ProjectStatusEnum.SYMBILITY_XML_REPORT_GENERATED,
			null,
			null, "SXR"),

	WEATHER_REPORT(10, "Weather Report", "application/pdf", "pdf",
			true, false, false, true, false, false,
			ProjectStatusEnum.WEATHER_REPORT_SUBMITTED,
			ProjectStatusEnum.WEATHER_REPORT_GENERATED,
			ProjectStatusEnum.WEATHER_REPORT_APPROVED,
			ProjectStatusEnum.WEATHER_REPORT_DISAPPROVED, "WR"),

    ESTIMATE_REPORT(11, "Estimate Report", "application/pdf", "pdf",
			true, false, false, true, false, true,
			ProjectStatusEnum.ESTIMATE_REPORT_SUBMITTED,
			ProjectStatusEnum.ESTIMATE_REPORT_GENERATED,
			ProjectStatusEnum.ESTIMATE_REPORT_APPROVED,
			ProjectStatusEnum.ESTIMATE_REPORT_DISAPPROVED, "NR"),

	DXF_REPORT(12, "Measurement DXF Report", "application/dxf", "dxf",
			true, false, false, false, false, true,
			ProjectStatusEnum.DXF_REPORT_SUBMITTED,
			ProjectStatusEnum.DXF_REPORT_GENERATED,
			ProjectStatusEnum.DXF_REPORT_APPROVED,
			ProjectStatusEnum.DXF_REPORT_DISAPPROVED, "DR"),

	BID_REPORT(13, "On-Site Bidding Report", "application/pdf", "pdf",
			false, false, true, true, false, true,
			ProjectStatusEnum.BIDDING_SUBMITTED,
			ProjectStatusEnum.BIDDING_GENERATED,
			ProjectStatusEnum.BIDDING_APPROVED,
			ProjectStatusEnum.BIDDING_DISAPPROVED, "BR"),

	QUICK_SQUARE_REPORT(14, "Real-time Quick Square Report", "application/pdf", "pdf",
			false, true, true, true, false, true,
			ProjectStatusEnum.QUICK_SQUARE_SUBMITTED,
			ProjectStatusEnum.QUICK_SQUARE_GENERATED,
			ProjectStatusEnum.QUICK_SQUARE_APPROVED,
			ProjectStatusEnum.QUICK_SQUARE_DISAPPROVED, "QSR"),

    PROPERTY_IMAGE_REPORT(15, "Property Image Report", "application/pdf", "pdf",
        true, false, true, true, false, true,
        ProjectStatusEnum.APP_DAMAGE_REPORT_SUBMITTED,
        ProjectStatusEnum.APP_DAMAGE_REPORT_GENERATED,
        ProjectStatusEnum.APP_DAMAGE_REPORT_APPROVED,
        ProjectStatusEnum.APP_DAMAGE_REPORT_DISAPPROVED, "PIR"),

	INFRARED_DAMAGE_REPORT(16, "Infrared Damage Assessment Report", "application/pdf", "pdf",
			true, false, true, true, false, true,
        ProjectStatusEnum.INFRARED_DAMAGE_REPORT_SUBMITTED,
        ProjectStatusEnum.INFRARED_DAMAGE_REPORT_GENERATED,
        ProjectStatusEnum.INFRARED_DAMAGE_REPORT_APPROVED,
        ProjectStatusEnum.INFRARED_DAMAGE_REPORT_DISAPPROVED, "IDAR"),

    ROOF_ONLY_UNDERWRITING_REPORT(17, "Roof-only Underwriting Report", "application/pdf", "pdf",
        true, false, true, true, false, true,
        ProjectStatusEnum.ROOF_ONLY_UNDERWRITING_REPORT_SUBMITTED,
        ProjectStatusEnum.ROOF_ONLY_UNDERWRITING_REPORT_GENERATED,
        ProjectStatusEnum.ROOF_ONLY_UNDERWRITING_REPORT_APPROVED,
        ProjectStatusEnum.ROOF_ONLY_UNDERWRITING_REPORT_DISAPPROVED, "ROR"),

    FULL_SCOPE_UNDERWRITING_REPORT(18, "Full-scope Underwriting Report", "application/pdf", "pdf",
        true, false, true, true, false, true,
        ProjectStatusEnum.FULL_SCOPE_UNDERWRITING_REPORT_SUBMITTED,
        ProjectStatusEnum.FULL_SCOPE_UNDERWRITING_REPORT_GENERATED,
        ProjectStatusEnum.FULL_SCOPE_UNDERWRITING_REPORT_APPROVED,
        ProjectStatusEnum.FULL_SCOPE_UNDERWRITING_REPORT_DISAPPROVED, "FUR"),

    LETTER_OF_CANCELLATION_REPORT(19, "Inspection Closeout Report", "application/pdf", "pdf",
        false, false, false, true, false, true,
        ProjectStatusEnum.LETTER_OF_CANCELLATION_REPORT_SUBMITTED,
        ProjectStatusEnum.LETTER_OF_CANCELLATION_REPORT_GENERATED,
        ProjectStatusEnum.LETTER_OF_CANCELLATION_REPORT_APPROVED,
        ProjectStatusEnum.LETTER_OF_CANCELLATION_REPORT_DISAPPROVED, "ICR"),

    CLAIM_DAMAGE_FORM(20, "Claim Damage Form", "application/pdf", "pdf",
        false, false, false, true, false, true,
        ProjectStatusEnum.CLAIM_DAMAGE_FORM_SUBMITTED,
        ProjectStatusEnum.CLAIM_DAMAGE_FORM_GENERATED,
        ProjectStatusEnum.CLAIM_DAMAGE_FORM_APPROVED,
        ProjectStatusEnum.CLAIM_DAMAGE_FORM_DISAPPROVED, "CDF"),
    /**
     * 重新检测报告，用于完成理赔之后检测屋顶修缮效果，从而辅助判断是否存在理赔欺诈行为（资金是否都用于修缮屋顶）。
     */
    POST_CONSTRUCTION_AUDIT_REPORT(21, "Post-Construction Audit Report", "application/pdf", "pdf",
        true, false, true, true, false, true,
        ProjectStatusEnum.POST_CONSTRUCTION_REPORT_SUBMITTED,
        ProjectStatusEnum.POST_CONSTRUCTION_REPORT_GENERATED,
        ProjectStatusEnum.POST_CONSTRUCTION_REPORT_APPROVED,
        ProjectStatusEnum.POST_CONSTRUCTION_REPORT_DISAPPROVED, "CRR"),

	HOVER_PRO_REPORT(22, "HOVER Pro Report", "application/pdf", "pdf",
		false, true, false, false, true, false,
		null,null, null, null, "HPR"),

	HOVER_ESX(23, "HOVER", "application/octet-stream", "esx",
		false, true, false, false, true, true,
		null, null, null, null, "HE"),

	XACT_ESX(24, "XACT", "application/octet-stream", "esx",
		false, true, false, false, true, true,
		null, null, null, null, "XE"),

    INVOICE(25, "Invoice", "application/pdf", "pdf",
        true, true, false, false, true, true,
        null, null, null, null, "INV"),

    MAGIC_PLAN(26, "MagicPlan report", "application/pdf", "pdf",
        false, true, false, false, true, false,
        null, null, null, null, "MP"),

    HOMEOWNER_SURVEY(27, "Homeowner Inspection Survey Results", "application/pdf", "pdf",
        false, true, false, false, false, true,
        ProjectStatusEnum.HOMEOWNER_SURVEY_REPORT_SUBMITTED,
        ProjectStatusEnum.HOMEOWNER_SURVEY_REPORT_GENERATED,
        ProjectStatusEnum.HOMEOWNER_SURVEY_REPORT_APPROVED,
        ProjectStatusEnum.HOMEOWNER_SURVEY_REPORT_DISAPPROVED, "HIS"),

    CHECKLIST(32, "Checklist", "application/pdf", "pdf",
        false, true, false, true, false, false,
        null, null, null, null, "CHECKLIST"),

    CLAIM(33, "Claim Report", "application/pdf", "pdf",
        false, true, false, false, false, true,
        null, null, null, null, "CLA"),

    GENERAL_LOSS_REPORT(34, "General Loss Report", "application/pdf", "pdf",
        false, true, false, true, false, true,
        null, null, null, null, "GLR"),

    DRONE_PHOTO_SHEET(37, "Drone Photo Sheet", "application/pdf", "pdf",
        false, true, false, true, false, true,
        null, null, null, null, "DPS"),

    MOBILE_PHOTO_SHEET(38, "Mobile Photo Sheet", "application/pdf", "pdf",
        false, true, false, true, false, true,
        null, null, null, null, "MPS"),

    PLNAR(39, "Plnar", "application/pdf", "pdf",
        false, true, false, true, false, true,
        null, null, null, null, "PLNR"),

    CUBICASA(40, "Cubicasa", "application/pdf", "pdf",
        false, true, false, true, false, true,
        null, null, null, null, "CUBI"),

    HOVER_TLA(41, "Hover TLA", "application/pdf", "pdf",
        false, true, false, true, false, true,
        null, null, null, null, "HTLA"),

    FSR_RCE(42, "Full-scope Underwriting Report with Recovery Cost Estimate", "application/pdf", "pdf",
        false, true, false, false, false, true,
        null, null, null, null, "FSR_RCE"),

    OCC(43, "OneClick Code Report", "application/pdf", "pdf",
        false, true, false, false, false, true,
        null, null, null, null, "OCC"),

    EVRP(44, "EagleView Report", "application/pdf", "pdf",
        false, true, false, false, false, true,
        null, null, null, null, "EVRP"),

    EVOB(45, "EV .OBJ", "model/obj", "OBJ",
        false, true, false, false, false, true,
        null, null, null, null, "EVOB"),

    EUR(46, "Express Underwriting Report", "application/pdf", "pdf",
        true, false, true, true, false, true,
        ProjectStatusEnum.EUR_REPORT_SUBMITTED,
        ProjectStatusEnum.EUR_REPORT_GENERATED,
        ProjectStatusEnum.EUR_REPORT_APPROVED,
        ProjectStatusEnum.EUR_REPORT_DISAPPROVED, "EUR"),
    CUBICASA_VIDEO_ZIP(47, "Cubicasa Video Zip", "application/zip", "zip",
       false, true, false, true, false, true,null, null, null, null, "CUBIVZ"),
    CUBICASA_VIDEO(48, "Cubicasa Video", "video/x-m4v", "m4v",
       false, true, false, true, false, true,null, null, null, null, "CUBIV"),
    HOSTA_SUMMARY_ZIP(49, "Hosta Summary Zip", "application/zip", "zip",
        false, true, false, true, false, true,null, null, null, null, "HOSTASZ"),
    HOSTA_MODEL_ZIP(50, "Hosta Model Zip", "application/zip", "zip",
        false, true, false, true, false, true,null, null, null, null, "HOSTAMZ"),
    CUSTOM_HOME_REPORT(51, "Policyholder Property Inspection Report", "application/pdf", "pdf",
        false, true, false, true, false, true,
        ProjectStatusEnum.CHR_REPORT_SUBMITTED,
        ProjectStatusEnum.CHR_REPORT_GENERATED,
        ProjectStatusEnum.CHR_REPORT_APPROVED,
        ProjectStatusEnum.CHR_REPORT_DISAPPROVED, "CHR"),

    SCHEDULING_ONLY_SUMMARY(52, "Scheduling Only Summary", "application/pdf", "pdf",
        true, false, true, true, true, true,
        null, null, null, null, "SOS"),

    BEESSKETCH(53, "BeesSketch", "application/pdf", "pdf",
			true, true, false, true, false, true,
			ProjectStatusEnum.BEESSKETCH_REPORT_SUBMITTED,
			ProjectStatusEnum.BEESSKETCH_REPORT_GENERATED,
			ProjectStatusEnum.BEESSKETCH_REPORT_APPROVED,
			ProjectStatusEnum.BEESSKETCH_REPORT_DISAPPROVED, "BSK"),

	SNAP(54, "Snap360 Report", "application/pdf", "pdf",
			true, false, true, true, false, true,
			ProjectStatusEnum.SNAP_REPORT_SUBMITTED,
			ProjectStatusEnum.SNAP_REPORT_GENERATED,
			ProjectStatusEnum.SNAP_REPORT_APPROVED,
			ProjectStatusEnum.SNAP_REPORT_DISAPPROVED, "SNAP360"),
    ;

    /**
     * 唯一id值
     */
	private final int code;
    /**
     * 页面中显示的值
     */
	private final String display;
    /**
     * 媒体类型
     */
	private final String contentType;
	private final String extension;
    /**
     * true 需要在页面中进行approved，false 则自动approved
     */
	private final boolean needApproved;
	private final boolean isFree;
	private final boolean isOrderable;
	private final boolean isReadable;
	/**
	 * 报告是否同步到Ai，与Ai{@link com.bees360.internal.ai.entity.enums.ReportTypeEnum#isSyncToWeb}
	 */
	private final boolean isSyncToAI;
	/**
	 * 是否展示在Report列表
	 */
	private final boolean isShow;

	private ProjectStatusEnum currentStatus;
	private String shortCut;

	public final ProjectStatusEnum SUBMITTED;
	public final ProjectStatusEnum GENERATED;
	public final ProjectStatusEnum APPROVED;
	public final ProjectStatusEnum REJECTED;


	private static Map<ProjectStatusEnum, ReportTypeEnum> STATUS_MAP_TYPE;

	public static List<ReportTypeEnum> REPORTS_NEED_3D;

	static {
		Map<ProjectStatusEnum, ReportTypeEnum> statusMapTypes
			= new HashMap<ProjectStatusEnum, ReportTypeEnum>();
		ReportTypeEnum[] types = ReportTypeEnum.values();
		for(ReportTypeEnum type: types) {
			if(type.SUBMITTED != null) {
				statusMapTypes.put(type.SUBMITTED, type);
			}
			if(type.GENERATED != null) {
				statusMapTypes.put(type.GENERATED, type);
			}
			if(type.APPROVED != null) {
				statusMapTypes.put(type.APPROVED, type);
			}
			if(type.REJECTED != null) {
				statusMapTypes.put(type.REJECTED, type);
			}
		}
		STATUS_MAP_TYPE = Collections.unmodifiableMap(statusMapTypes);

		REPORTS_NEED_3D = Collections.unmodifiableList(Arrays.asList(ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT,
				ReportTypeEnum.PREMIUM_ROOF_MEASUREMENT_REPORT));
	}

	ReportTypeEnum(int code, String display, String contentType, String extension,
			boolean needApproved, boolean isFree, boolean isOrderable, boolean isReadable, boolean isSyncToAI,
			boolean isShow, ProjectStatusEnum generated) {
		this(code, display, contentType, extension,
            needApproved, isFree, isOrderable, isReadable, isSyncToAI,
			isShow, null, generated, null, null, null);
	}

	ReportTypeEnum(int code, String display, String contentType, String extension,
			boolean needApproved, boolean isFree, boolean isOrderable, boolean isReadable,
			boolean isSyncToAI, boolean isShow,
			ProjectStatusEnum submitted, ProjectStatusEnum generated,
			ProjectStatusEnum approved, ProjectStatusEnum rejected, String shortCut){
		this.code = code;
		this.display = display;
		this.contentType = contentType;
		this.extension = extension;
		this.needApproved = needApproved;
		this.isFree = isFree;
		this.isOrderable = isOrderable;
		this.isReadable = isReadable;
        this.isSyncToAI = isSyncToAI;
        this.isShow = isShow;

		this.SUBMITTED = submitted;
		this.GENERATED = generated;
		this.APPROVED = approved;
		this.REJECTED = rejected;

		this.currentStatus = null;
		this.shortCut = shortCut;
	}

	public static ReportTypeEnum getType(ProjectStatusEnum status) {
		ReportTypeEnum type = STATUS_MAP_TYPE.get(status);
		if(type != null) {
			type.currentStatus = status;
		}
		return type;
	}

	public static boolean exist(Integer type) {
		return getEnum(type) != null;
	}

	public static ReportTypeEnum getEnum(Integer code) {
		if(code == null) {
			return null;
		}
		for(ReportTypeEnum type: ReportTypeEnum.values()) {
			if(type.getCode() == code) {
				return type;
			}
		}
		return null;
	}

	/**
	 * 是否需要同步到AI平台
	 */
	public static boolean needSyncToAi(Integer code) {
		ReportTypeEnum typeEnum = getEnum(code);
		if (typeEnum == null) {
			return false;
		}
		return typeEnum.isSyncToAI;
	}

	public List<FileSourceTypeEnum> listFileSourceTypesForReport() {
		if (this == ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT) {
			return Arrays.asList(FileSourceTypeEnum.DRONE_IMAGE);
		} else if (this == ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT) {
			return Arrays.asList(FileSourceTypeEnum.CELL_PHONE_IMAGE,
					FileSourceTypeEnum.DRONE_PREVISIT_IMAGE);
		} else if (this == ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT) {
			return Arrays.asList(FileSourceTypeEnum.CELL_PHONE_IMAGE,
					FileSourceTypeEnum.DRONE_REALTIME_IMAGE);
		} else {
			return Arrays.asList(FileSourceTypeEnum.CELL_PHONE_IMAGE,
					FileSourceTypeEnum.DRONE_IMAGE);
		}
	}

	@Override
	public int getCode() {
		return code;
	}
	@Override
	public String getDisplay() {
		return display;
	}
	public String getContentType() {
		return contentType;
	}

	public String getExtension() {
	    return extension;
    }

	public boolean needApproved() {
		return needApproved;
	}
	public boolean isFree() {
		return isFree;
	}
	public boolean isOrderable() {
		return isOrderable;
	}
	public boolean isReadable() {
		return isReadable;
	}

	public boolean isSyncToAI() {
		return isSyncToAI;
	}

	public boolean isShow() {
		return isShow;
	}

	public ProjectStatusEnum getCurStatus() {
		return currentStatus;
	}

    public String getShortCut() {
        return shortCut;
    }

    public static void main(String[] args) {
		show();
	}

	private static void show() {
		System.out.println("|name|code|contentType|isFree|needApproved|isOrderable|");
		System.out.println("|----|----|-----------|------|------------|-----------|");
		for(ReportTypeEnum type: ReportTypeEnum.values()) {
			System.out.println("|" + type.getDisplay() + "|" + type.getCode() + "|" + type.getContentType()
					+ "|" + type.isFree() + "|" + type.needApproved + "|" + type.isOrderable + "|");
		}
	}

    public static ReportTypeEnum getReportType(Report report) {
        ReportTypeEnum typeEnum = null;
        var reportTypes = ReportTypeEnum.values();
        for (var type : reportTypes) {
            if (StringUtils.equalsAny(
                report.getType(),
                type.name(),
                type.getShortCut(),
                type.getDisplay(),
                String.valueOf(type.getCode()))) {
                typeEnum = type;
            }
        }
        return typeEnum;
    }
}
