package com.bees360.entity.dto;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * ProjectFacetTypeParameterDto
 * <AUTHOR>
 *
 */
public class ProjectFacetTypeParameterDto implements Serializable {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = -5700852716436136800L;

	private List<ProjectFacetTypeDto> facetTypes;

	private boolean isGeneratReport;

	private boolean isAngleOptimization;

	public List<ProjectFacetTypeDto> getFacetTypes() {
		return facetTypes;
	}

	public void setFacetTypes(List<ProjectFacetTypeDto> facetTypes) {
		this.facetTypes = facetTypes;
	}

	public boolean getIsGeneratReport() {
		return isGeneratReport;
	}

	public void setIsGeneratReport(boolean isGeneratReport) {
		this.isGeneratReport = isGeneratReport;
	}

	public boolean getIsAngleOptimization() {
		return isAngleOptimization;
	}

	public void setIsAngleOptimization(boolean isAngleOptimization) {
		this.isAngleOptimization = isAngleOptimization;
	}

	@Override
	public String toString() {
		return "ProjectFacetTypeParameterDto [facetTypes=" + facetTypes + ", isGeneratReport=" + isGeneratReport
				+ ", isAngleOptimization=" + isAngleOptimization + "]";
	}

}
