package com.bees360.entity;

/**
 * <AUTHOR>
 * @date 2019/09/27 16:51
 */
public class InvoiceItem {
    private long itemId;
    private long invoiceId;
    private long productId;
    private String itemTitle;
    private String description;
    private int quantity;
    private double unitPrice;
    /** amount = quantity * unitPrice **/
    private double amount;
    private double discountAmount;
    // ===============================　
    // tax
    private Integer taxRateId;
    private String taxRateName;
    private double taxRatePercentage;
    private double taxAmount;

    // ===============================
    // 时间信息
    private long createdTime;

    public long getItemId() {
        return itemId;
    }

    public void setItemId(long itemId) {
        this.itemId = itemId;
    }

    public long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public long getProductId() {
        return productId;
    }

    public void setProductId(long productId) {
        this.productId = productId;
    }

    public String getItemTitle() {
        return itemTitle;
    }

    public void setItemTitle(String itemTitle) {
        this.itemTitle = itemTitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public Integer getTaxRateId() {
        return taxRateId;
    }

    public void setTaxRateId(Integer taxRateId) {
        this.taxRateId = taxRateId;
    }

    public String getTaxRateName() {
        return taxRateName;
    }

    public void setTaxRateName(String taxRateName) {
        this.taxRateName = taxRateName;
    }

    public double getTaxRatePercentage() {
        return taxRatePercentage;
    }

    public void setTaxRatePercentage(double taxRatePercentage) {
        this.taxRatePercentage = taxRatePercentage;
    }

    public double getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(double taxAmount) {
        this.taxAmount = taxAmount;
    }

    public long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(long createdTime) {
        this.createdTime = createdTime;
    }

    @Override
    public String toString() {
        return "InvoiceItem{" + "itemId=" + itemId + ", invoiceId=" + invoiceId + ", itemTitle='" + itemTitle + '\''
            + ", description='" + description + '\'' + ", quantity=" + quantity + ", unitPrice=" + unitPrice
            + ", amount=" + amount + ", taxRateId=" + taxRateId + ", taxRateName='" + taxRateName + '\''
            + ", taxRatePercentage=" + taxRatePercentage + ", taxAmount=" + taxAmount + ", createdTime=" + createdTime
            + '}';
    }
}
