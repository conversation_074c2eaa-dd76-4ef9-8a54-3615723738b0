package com.bees360.entity.enums;

/**
 * 问卷题类型枚举
 * {@link "https://gitlab.bees360.com/engineers/beespilot/issues/105"}
 * <AUTHOR>
 * @since 2020/4/10 11:32 AM
 */
public enum QuizTypeEnum implements BaseCodeEnum {
    /**
     * 判断题
     */
    BOOLEAN(1, "Boolean Type"),

    /**
     * 单选题
     */
    SINGLE_CHOICE(2, "Single Choice Type."),

    /**
     * 多选题
     */
    MULTI_CHOICE(3, "Multi choice."),
    /**
     * 日期类型，保存的回答日期应该为时间戳
     * 例如： MM/yyyy 表示答案显示的日期格式
     */
    DATE(4, "Date Type."),

    /**
     * 单填空题，回答为用户输入文本
     */
    FILL_A_BLANK(5, "Fill a Blank."),

    /**
     * 单数字填空题，回答为用户输入文本
     */
    FILL_A_DIGIT_BLANK(6, "Fill a digit Blank."),

    /**
     * 多空填空题，回答为用户输入文本
     */
    FILL_MANY_BLANKS(7, "Fill many Blanks"),

    /**
     * 多数字空填空题，回答为用户输入文本
     */
    FILL_MANY_DIGIT_BLANKS(8, "Fill many digit Blanks."),

    /**
     * 判断题+YES数字填空, 如果用户选择YES，则需要填空
     */
    BOOLEAN_YES_DIGIT_BLANKS(10, "Fill many digit Blanks."),

    /**
     * 判断题+NO数字填空, 如果用户选择NO，则需要填空
     */
    BOOLEAN_NO_DIGIT_BLANKS(11, "Fill many digit Blanks."),

    /**
     * 判断题+YES字符填空, 如果用户选择YES，则需要填空
     */
    BOOLEAN_YES_BLANKS(12, "Fill many digit Blanks."),

    /**
     * 判断题+NO字符填空, 如果用户选择NO，则需要填空
     */
    BOOLEAN_NO_BLANKS(13, "Fill many digit Blanks."),

    /**
     * 单选题+Others填空, 如果用户选择Others，则需要填空, 特殊类型
     */
    SINGLE_CHOICES_OTHERS_BLANKS(14, "Fill many digit Blanks."),

    /**
     * 单选特定选项后衍生出其他题目, e.g. 选择 Yes 会弹出其他题目让用户填写
     * App端叫QUIZ TYPE SELECT YES TO INPUT NUMBER，感觉这个名字很不通用
     */
    SINGLE_CHOICES_ASSOCIATION_OTHERS_QUIZ(20, "Selecting a specific option leads to other questions."),
;
    private int code;
    private String display;

    QuizTypeEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    public static QuizTypeEnum valueOf(int code) {
        for (QuizTypeEnum value : values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }
}
