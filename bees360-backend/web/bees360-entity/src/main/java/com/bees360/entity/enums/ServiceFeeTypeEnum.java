package com.bees360.entity.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public enum ServiceFeeTypeEnum {

	FULL_DAMAGE_MEASUREMENT_REPORT(1,"Premium Roof Measurement Report And Damage Report",
									Arrays.asList(ReportTypeEnum.PREMIUM_ROOF_MEASUREMENT_REPORT,
									ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT), true),
	PREMIUM_MEASUREMENT_REPORT(2, "Premium Measurement Report",
									Arrays.asList(ReportTypeEnum.PREMIUM_ROOF_MEASUREMENT_REPORT), false),
	//Preliminary Damage Assessment Report---QUICK_DAMAGE_REPORT
	PRELIMINARY_DAMAGE_ASSESSMENT_REPORT(3, "Preliminary Damage Assessment Report",
									Arrays.asList(ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT), false),
	ON_SITE_BIDDING_REPORT(4, "On-Site Bidding Report", Arrays.asList(ReportTypeEnum.BID_REPORT), false),
	REAL_TIME_QUICK_SQUARE_REPORT(5, "Real-Time Quick Square Report", Arrays.asList(ReportTypeEnum.QUICK_SQUARE_REPORT), false),

	//highfly inspection report
	HIGHFLY_EVALUATION_REPORT(6, "Highfly Evaluation Report", Arrays.asList(ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT), false),

	/**2019-03-25 added*/
	REAL_TIME_DAMAGE_ASSESSMENT(7, "Real-Time Damage Assessment Report", Arrays.asList(ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT), false),
	PREMIUM_DAMAGE_ASSESSMENT_REPORT(8, "Premium Damage Assessment Report", Arrays.asList(ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT), false),
	PROPERTY_IMAGE_REPORT(9, "Property Image Report", Arrays.asList(ReportTypeEnum.PROPERTY_IMAGE_REPORT), false),
	INFRARED_DAMAGE_ASSESSMENT(10, "Infrared Damage Assessment Report", Arrays.asList(ReportTypeEnum.INFRARED_DAMAGE_REPORT), false),
	ROOF_ONLY_UNDERWRITING_REPORT(11, "Roof-only underwriting report", Arrays.asList(ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT), false),
	FULL_SCOPE_UNDERWRITING_REPORT(12, "Full-scope underwriting report", Arrays.asList(ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT), false),
    LETTER_OF_CANCELLATION_REPORT(13, "Inspection Closeout Report", Arrays.asList(ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT), false)
	;

	private int serviceFeeTypeId;
	private String display;
	private boolean isPackage;
	private final List<ReportTypeEnum> reportTypes;

	private ServiceFeeTypeEnum(int serviceFeeTypeId, String display,List<ReportTypeEnum> reportTypes, boolean isPackage) {
		this.serviceFeeTypeId = serviceFeeTypeId;
		this.display = display;
		this.reportTypes = Collections.unmodifiableList(reportTypes == null? Arrays.asList(): reportTypes);
		this.isPackage = isPackage;

		if(!this.isPackage && this.reportTypes.size() > 1) {
			throw new AssertionError("The size of reports shouldn't have large than 1, "
					+ "since" + this.display + "(" + this.serviceFeeTypeId + ") isn't a package");
		}
	}

	public static ServiceFeeTypeEnum getEnum(int serviceFeeTypeId) {
		return Arrays.asList(ServiceFeeTypeEnum.values()).stream().filter(x -> x.getServiceFeeTypeId() == serviceFeeTypeId)
				.findFirst().orElse(null);
	}

	public static List<Integer> getServiceFeeTypeIds(){
		return Arrays.asList(ServiceFeeTypeEnum.values()).stream().map(ServiceFeeTypeEnum::getServiceFeeTypeId).collect(Collectors.toList());
	}

	public int getServiceFeeTypeId() {
		return serviceFeeTypeId;
	}

	public String getDisplay() {
		return display;
	}

	public List<ReportTypeEnum> getReportTypes() {
		return reportTypes;
	}

	public boolean isPackage() {
		return isPackage;
	}

	public void setPackage(boolean isPackage) {
		this.isPackage = isPackage;
	}

	public static ServiceFeeTypeEnum getServiceFeeTypeEnumByReportTypeWithoutPackage(ReportTypeEnum reportTypeEnum) {
		ServiceFeeTypeEnum[] serviceFeeTypeEnums = values();
		for(ServiceFeeTypeEnum serviceFeeTypeEnum : serviceFeeTypeEnums) {
			if(serviceFeeTypeEnum.isPackage()) {
				continue;
			}
			for(ReportTypeEnum reportType : serviceFeeTypeEnum.getReportTypes()) {
				if((reportType == reportTypeEnum)) {
					return serviceFeeTypeEnum;
				}
			}
		}
		return null;
	}

	public static void main(String[] args) {

	}
}
