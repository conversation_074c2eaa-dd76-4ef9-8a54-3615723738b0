package com.bees360.entity.vo;

public class ItemVo {

	private long orderId;
	private int paidProjectCount;
	private double totalFeeAmount;
	private double paidServiceFeeAmount;
	private double paidServiceFeeTaxPercentage;
	private double paidServiceFeeTax;
	private double discountPercent;
	private double price;
	private String currency;
	private long createdTime;
	private int serviceFeeType;

	private long projectId;
	private int paymentMethodId;
	private String paymentMethod;

	//property for
	private String name;
	private int quantity;


	public long getOrderId() {
		return orderId;
	}

	public void setOrderId(long orderId) {
		this.orderId = orderId;
	}


	public int getPaidProjectCount() {
		return paidProjectCount;
	}

	public void setPaidProjectCount(int paidProjectCount) {
		this.paidProjectCount = paidProjectCount;
	}

	public double getTotalFeeAmount() {
		return totalFeeAmount;
	}

	public void setTotalFeeAmount(double totalFeeAmount) {
		this.totalFeeAmount = totalFeeAmount;
	}

	public double getPaidServiceFeeAmount() {
		return paidServiceFeeAmount;
	}

	public void setPaidServiceFeeAmount(double paidServiceFeeAmount) {
		this.paidServiceFeeAmount = paidServiceFeeAmount;
	}

	public double getPaidServiceFeeTaxPercentage() {
		return paidServiceFeeTaxPercentage;
	}

	public void setPaidServiceFeeTaxPercentage(double paidServiceFeeTaxPercentage) {
		this.paidServiceFeeTaxPercentage = paidServiceFeeTaxPercentage;
	}

	public double getPaidServiceFeeTax() {
		return paidServiceFeeTax;
	}

	public void setPaidServiceFeeTax(double paidServiceFeeTax) {
		this.paidServiceFeeTax = paidServiceFeeTax;
	}

	public double getDiscountPercent() {
		return discountPercent;
	}

	public void setDiscountPercent(double discountPercent) {
		this.discountPercent = discountPercent;
	}

	public int getServiceFeeType() {
		return serviceFeeType;
	}

	public void setServiceFeeType(int serviceFeeType) {
		this.serviceFeeType = serviceFeeType;
	}

	public double getPrice() {
		return price;
	}

	public void setPrice(double price) {
		this.price = price;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}

	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public int getPaymentMethodId() {
		return paymentMethodId;
	}

	public void setPaymentMethodId(int paymentMethodId) {
		this.paymentMethodId = paymentMethodId;
	}

	public String getPaymentMethod() {
		return paymentMethod;
	}

	public void setPaymentMethod(String paymentMethod) {
		this.paymentMethod = paymentMethod;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	@Override
	public String toString() {
		return "ItemVo [orderId=" + orderId + ", paidProjectCount=" + paidProjectCount + ", totalFeeAmount="
				+ totalFeeAmount + ", paidServiceFeeAmount=" + paidServiceFeeAmount + ", paidServiceFeeTaxPercentage="
				+ paidServiceFeeTaxPercentage + ", paidServiceFeeTax=" + paidServiceFeeTax + ", discountPercent="
				+ discountPercent + ", price=" + price + ", currency=" + currency + ", createdTime=" + createdTime
				+ ", serviceFeeType=" + serviceFeeType + ", projectId=" + projectId + ", paymentMethodId="
				+ paymentMethodId + ", paymentMethod=" + paymentMethod + ", name=" + name + ", quantity=" + quantity
				+ "]";
	}
}
