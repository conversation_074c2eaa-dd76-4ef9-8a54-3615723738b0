package com.bees360.entity;
import java.io.Serial;
import java.io.Serializable;

import com.bees360.entity.enums.ReportGenerationStatusEnum;
import lombok.Getter;
import lombok.Setter;

public class ProjectReportFile implements Serializable {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1493219163431833271L;

	//Primary key
	private String reportId;
	//The foreign key
	private long projectId;
	//There are several types of the project report:
	//	1：Image report
	//	2：Estimation report
	private int reportType;
	private String reportWordFileName;
	private String reportPdfFileName;
	private String reportPdfCompressed;
	private int size;
	private int sizeCompressed;
    @Deprecated
	private long createdBy;
    @Getter
    @Setter
    private String createdByString;
	private long createdTime;
	/**
	 * Whether report has been read, true:read, false:not read
	 */
	private boolean isRead;
	private boolean isDeleted;
	private int generationStatus;

    /**
     * 存储真实的创建者 aiUserId（String），但是不保证该字段的存在性，如果字段为空，请使用 createdBy 字段。
     */
    private String aiUserId;

    private String fileType;

	public ProjectReportFile() {
		super();
	}

	public ProjectReportFile(long projectId) {
		super();
		this.projectId = projectId;
	}

	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}

	public long getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(long createdBy) {
		this.createdBy = createdBy;
	}

	public String getReportId() {
		return reportId;
	}

	public void setReportId(String reportId) {
		this.reportId = reportId;
	}

	public int getReportType() {
		return reportType;
	}

	public void setReportType(int reportType) {
		this.reportType = reportType;
	}

	public String getReportWordFileName() {
		return reportWordFileName;
	}

	public void setReportWordFileName(String reportWordFileName) {
		this.reportWordFileName = reportWordFileName;
	}

	public String getReportPdfFileName() {
		return reportPdfFileName;
	}

	public void setReportPdfFileName(String reportPdfFileName) {
		this.reportPdfFileName = reportPdfFileName;
	}

	public String getReportPdfCompressed() {
		return reportPdfCompressed;
	}

	public void setReportPdfCompressed(String reportPdfCompressed) {
		this.reportPdfCompressed = reportPdfCompressed;
	}

	public int getSize() {
		return size;
	}

	public void setSize(int size) {
		this.size = size;
	}

	public int getSizeCompressed() {
		return sizeCompressed;
	}

	public void setSizeCompressed(int sizeCompressed) {
		this.sizeCompressed = sizeCompressed;
	}

	public boolean getRead() {
		return isRead;
	}

	public void setRead(boolean isRead) {
		this.isRead = isRead;
	}

	public boolean getDeleted() {
		return isDeleted;
	}

	public void setDeleted(boolean isDeleted) {
		this.isDeleted = isDeleted;
	}
	public int getGenerationStatus() {
		return generationStatus;
	}
	public void setGenerationStatus(int generationStatus) {
		this.generationStatus = generationStatus;
	}
	public void setGenerationStatus(ReportGenerationStatusEnum generationStatus) {
		this.generationStatus = generationStatus.getCode();
	}

    public String getAiUserId() {
        return aiUserId;
    }

    public void setAiUserId(String aiUserId) {
        this.aiUserId = aiUserId;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    @Override
	public String toString() {
		return "ProjectReportFile [reportId=" + reportId + ", projectId=" + projectId + ", reportType=" + reportType
				+ ", reportWordFileName=" + reportWordFileName + ", reportPdfFileName=" + reportPdfFileName + ", size="
				+ size + ", createdBy=" + createdBy + ", createdTime=" + createdTime + ", isRead=" + isRead
				+ ", isDeleted=" + isDeleted + ", generationStatus=" + generationStatus + ", aiUserId=" + aiUserId + "]";
	}
}
