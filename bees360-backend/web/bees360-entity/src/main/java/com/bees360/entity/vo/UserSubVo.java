package com.bees360.entity.vo;

import java.util.List;

import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.dto.IdNameListDto;
import com.bees360.entity.enums.UserActiveStatusEnum;
import com.bees360.entity.util.UserCertificateParser;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
public class UserSubVo {
	private long userId;
	private String firstName;
	private String lastName;
	private String phone;
	private String email;
	private String companyName;
	private int activeStatus;
	private long registrationTime;
	private long roles;
	private long roleApplicationStatus;
	private String certificateList;
	private String address;
	private String city;
	private String state;
	private String zipCode;
	private double gpsLocationLatitude;
	private double gpsLocationLongitude;
    @Setter
    @Getter
    private Boolean isSsoUser;
	public long getUserId() {
		return userId;
	}
	public void setUserId(long userId) {
		this.userId = userId;
	}
	public String getName(){
		return (firstName + " " + lastName).trim();
	}
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	@JsonIgnore
	public IdNameDto getStatus() {
		UserActiveStatusEnum status = UserActiveStatusEnum.getEnum(activeStatus);
		IdNameDto idName = new IdNameDto(status.getCode(), status.getDisplay());
		return idName;
	}
	public int getActiveStatus() {
		return activeStatus;
	}
	public void setActiveStatus(int activeStatus) {
		this.activeStatus = activeStatus;
	}
	public long getRegistrationTime() {
		return registrationTime;
	}
	public void setRegistrationTime(long registrationTime) {
		this.registrationTime = registrationTime;
	}
	public List<IdNameListDto> getRoleList() {
		return UserCertificateParser.parseToIdNameList(roles, certificateList);
	}
	@JsonIgnore
	public long getRoles() {
		return roles;
	}
	public List<IdNameListDto> getRoleApplicationList() {
		return UserCertificateParser.parseToIdNameList(roleApplicationStatus, certificateList);
	}
	@JsonIgnore
	public long getRoleApplicationStatus() {
		return roleApplicationStatus;
	}
	@JsonIgnore
	public String getCertificateList() {
		return certificateList;
	}
	public void setRoles(long roles) {
		this.roles = roles;
	}
	public void setRoleApplicationStatus(long roleApplicationStatus) {
		this.roleApplicationStatus = roleApplicationStatus;
	}
	public void setCertificateList(String certificateList) {
		this.certificateList = certificateList;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getZipCode() {
		return zipCode;
	}
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}
	public double getGpsLocationLatitude() {
		return gpsLocationLatitude;
	}
	public void setGpsLocationLatitude(double gpsLocationLatitude) {
		this.gpsLocationLatitude = gpsLocationLatitude;
	}
	public double getGpsLocationLongitude() {
		return gpsLocationLongitude;
	}
	public void setGpsLocationLongitude(double gpsLocationLongitude) {
		this.gpsLocationLongitude = gpsLocationLongitude;
	}
}
