package com.bees360.entity.enums;

public enum ReportParameterTypeEnum implements BaseCodeEnum {
	INT(1, "i", int.class, ".intValue"),
	DOUBLE(2, "d", double.class, ".doubleValue"),
	FLOAT(3, "f", float.class, ".floatValue"),
	LONG(4, "l", long.class, ".longValue"),
	BYTE(5, "e", byte.class, ".byteValue"),
	SHORT(6, "s", short.class, ".shortValue"),
	BOOLEAN(7, "b", boolean.class, ".intValue"),
	INT_PACKAGE(8, "I", Integer.class, ".byteValue"),
	DOUBLE_PACKAGE(9, "D", Double.class, ".doubleValue"),
	FLOAT_PACKAGE(10, "F", Float.class, ".floatValue"),
	LONG_PACKAGE(11, "L", Long.class, ".longValue"),
	BYTE_PACKAGE(12, "E", Byte.class, ".byteValue"),
	SHORT_PACKAGE(13, "S", Short.class, ".shortValue"),
	BOOLEAN_PACKAGE(14, "B", Boolean.class, ".booleanValue");

	private int code;

	private String display;

	@SuppressWarnings("rawtypes")
	private Class paramClass;

	private String method;

	@SuppressWarnings("rawtypes")
	ReportParameterTypeEnum(int code, String display, Class paramClass, String method) {
		this.code = code;
		this.display = display;
		this.paramClass = paramClass;
		this.method = method;
	}

	@Override
	public int getCode() {
		return this.code;
	}

	@Override
	public String getDisplay() {
		return this.display;
	}

	@SuppressWarnings("rawtypes")
	public Class getParamClass() {
		return paramClass;
	}

	public String getMethod() {
		return this.method;
	}

	public static Object getDataByEnum(ReportParameterTypeEnum parameterType, String data) {
		Object result;
		switch (parameterType) {
		case INT:
		case INT_PACKAGE:
			result = Integer.parseInt(data);
			break;
		case DOUBLE:
		case DOUBLE_PACKAGE:
			result = Double.parseDouble(data);
			break;
		case FLOAT:
		case FLOAT_PACKAGE:
			result = Float.parseFloat(data);
			break;
		case LONG:
		case LONG_PACKAGE:
			result = Long.parseLong(data);
			break;
		case BYTE:
		case BYTE_PACKAGE:
			result = Byte.parseByte(data);
			break;
		case SHORT:
		case SHORT_PACKAGE:
			result = Short.parseShort(data);
			break;
		case BOOLEAN:
		case BOOLEAN_PACKAGE:
			result = !"0".equals(data);
			break;
		default:
			result = null;
			break;
		}
		return result;
	}

	public static ReportParameterTypeEnum getByDisplay(String display){
		ReportParameterTypeEnum[] materialTypes = ReportParameterTypeEnum.values();
		for(ReportParameterTypeEnum materialType: materialTypes){
			if(materialType.getDisplay().equals(display)){
				return materialType;
			}
		}
		return null;
	}

	public static Class getBaseValueMethod(String param){
		ReportParameterTypeEnum[] materialTypes = ReportParameterTypeEnum.values();
		for(ReportParameterTypeEnum materialType: materialTypes){
			if(param.indexOf(materialType.getMethod()) == (param.length() - materialType.getMethod().length())){
				return materialType.getParamClass();
			}
		}
		return null;
	}

}
