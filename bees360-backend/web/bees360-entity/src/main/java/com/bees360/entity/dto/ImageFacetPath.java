package com.bees360.entity.dto;

import com.bees360.entity.ImageFacet;
import com.bees360.entity.enums.OrientationEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/09/19 11:14
 */
public class ImageFacetPath {
    private int facetId;
    private ImageFacet imageFacet;
    private List<Point> path2DSeen;
    private List<Point> path2DFull;
    private Point centerOfPath2DSeen;
    private Point centerOfPath2DFull;

    private OrientationEnum orientation;

    public int getFacetId() {
        return facetId;
    }

    public void setFacetId(int facetId) {
        this.facetId = facetId;
    }

    public ImageFacet getImageFacet() {
        return imageFacet;
    }

    public void setImageFacet(ImageFacet imageFacet) {
        this.imageFacet = imageFacet;
    }

    public List<Point> getPath2DSeen() {
        return path2DSeen;
    }

    public void setPath2DSeen(List<Point> path2DSeen) {
        this.path2DSeen = path2DSeen;
    }

    public List<Point> getPath2DFull() {
        return path2DFull;
    }

    public void setPath2DFull(List<Point> path2DFull) {
        this.path2DFull = path2DFull;
    }

    public Point getCenterOfPath2DSeen() {
        return centerOfPath2DSeen;
    }

    public void setCenterOfPath2DSeen(Point centerOfPath2DSeen) {
        this.centerOfPath2DSeen = centerOfPath2DSeen;
    }

    public Point getCenterOfPath2DFull() {
        return centerOfPath2DFull;
    }

    public void setCenterOfPath2DFull(Point centerOfPath2DFull) {
        this.centerOfPath2DFull = centerOfPath2DFull;
    }

    public OrientationEnum getOrientation() {
        return orientation;
    }

    public void setOrientation(OrientationEnum orientation) {
        this.orientation = orientation;
    }
}
