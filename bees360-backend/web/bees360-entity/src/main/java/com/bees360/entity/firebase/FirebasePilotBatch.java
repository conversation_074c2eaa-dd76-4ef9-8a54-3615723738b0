package com.bees360.entity.firebase;

import com.google.cloud.firestore.DocumentReference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Map;

/**
 * <AUTHOR>
 * @since  2020/09/30 14:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FirebasePilotBatch {

    /**
     * 该batch的状态
     *
     * @see BatchStatusEnum
     */
    private String status;

    private double basePay;

    private double extraPay;

    private Long planPaymentDate;

    private String note;

    private Long payTime;

    private long createdAt;

    private long updateAt;

    private Boolean isDeleted = false;

    private DocumentReference pilot;

    private Map<String, DocumentReference> mission;

    private Map<String, Object> project;
}
