package com.bees360.entity.openapi.reportsummary;

import com.bees360.web.core.json.gson.RemoveNullListAdapter;
import com.google.gson.annotations.JsonAdapter;
import lombok.Data;

import java.util.List;

@Data
public class SummaryComparison {
    private String comment;
    private String change;
    private String direction;
    @JsonAdapter(RemoveNullListAdapter.class)
    private List<SummaryImage> image;
}
