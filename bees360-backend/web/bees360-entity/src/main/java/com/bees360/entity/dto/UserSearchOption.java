package com.bees360.entity.dto;

import java.util.List;

import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.util.StringFieldUtil;

public class UserSearchOption extends BaseSearchOption{
	private Long companyId;
	private Long userId;
	private String userName;
	private String email;
	private Long startTime;
	private Long endTime;
	private List<Integer> userStatuses;
	private String phone;
	private RoleEnum role;


	public static enum ESortKey{
		REGTIME,  // registration time
		COMN // company name
		;

		public static ESortKey getEnum(String value) {
			if(value == null) {
				return null;
			}
			for(ESortKey key: ESortKey.values()) {
				if(key.name().equals(value)) {
					return key;
				}
			}
			return null;
		}
	}

	/* getter and setter */
	public void setSortKey(ESortKey sortKey) {
		if(sortKey == null) {
			this.sortKey = null;
		} else {
			this.sortKey = sortKey.name();
		}
	}

	@Override
	public void setSortKey(String sortKey) {
		if(sortKey == null){
			this.sortKey = null;
		} else {
			setSortKey(ESortKey.getEnum(sortKey.toUpperCase()));
		}
	}

	public String getUserNameRegex() {
        return getFuzzyRegex(userName);
    }

    public String getEmailRegex() {
        return getFuzzyRegex(email);
    }

    public String getPhoneRegex() {
        return getFuzzyRegex(phone);
    }

    private String getFuzzyRegex(String input) {
        if(input == null) {
            return null;
        }
        return StringFieldUtil.joinAndWrap("%", input.toCharArray());
    }

    public Long getCompanyId() {
		return companyId;
	}
	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}
	public List<Integer> getUserStatuses() {
		return userStatuses;
	}
	public void setUserStatuses(List<Integer> userStatuses) {
		this.userStatuses = userStatuses;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public Long getStartTime() {
		return startTime;
	}
	public void setStartTime(Long startTime) {
		this.startTime = startTime;
	}
	public Long getEndTime() {
		return endTime;
	}
	public void setEndTime(Long endTime) {
		this.endTime = endTime;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public RoleEnum getRole() {
		return role;
	}
	public void setRole(RoleEnum role) {
		this.role = role;
	}
	public Long getRoleBitMap() {
		return role == null? null: this.role.getBitMap();
	}
	@Override
	public String toString() {
		return "UserSearchOption [companyId=" + companyId + ", userId=" + userId + ", userName=" + userName + ", email="
				+ email + ", startTime=" + startTime + ", endTime=" + endTime + ", userStatuses=" + userStatuses
				+ ", phone=" + phone + ", role=" + role + "]";
	}




}
