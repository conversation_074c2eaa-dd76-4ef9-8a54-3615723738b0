package com.bees360.entity.enums;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 报告的状态，两个状态值之间相差3，以为以后可能添加的状态预留空间。
 *
 * <AUTHOR>
 * @date 2019/11/06 10:58
 */
public enum ProjectReportStatusEnum {
    /** 报告等待图片的上传 **/
    WAIT_FOR_IMAGES(1),
    /** 报告已经开始处理 **/
    PROCESSING(4),
    /** 完成 **/
    COMPLETED(7)
    ;

    private final int code;
    private final static Map<Integer, ProjectReportStatusEnum> CODE_MAP;

    static {
        Map<Integer, ProjectReportStatusEnum> codeMap = new HashMap<>();
        for(ProjectReportStatusEnum status: values()) {
            codeMap.put(status.getCode(), status);
        }
        CODE_MAP = Collections.unmodifiableMap(codeMap);
    }

    ProjectReportStatusEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static ProjectReportStatusEnum getEnum(int code) {
        return CODE_MAP.get(code);
    }

    public static boolean exist(int code) {
        return getEnum(code) != null;
    }
}
