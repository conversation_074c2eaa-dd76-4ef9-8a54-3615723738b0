package com.bees360.entity.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/20 2:46 PM
 **/
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ProjectStatusQuery {
    private Long projectId;

    private Integer projectStatus;

    /**
     * 查询 projectStatus小于或者等于 <code>projectStatusLeft</code>的项目状态
     */
    private Integer projectStatusLeft;

    /**
     * 查询 projectStatus大于或者等于 <code>projectStatusRight</code>的项目状态
     */
    private Integer projectStatusRight;

    private List<Long> projectIds;

    /**
     * status 的创建时间查询区间的开始，不包含。
     */
    private Long createdTimeStart;

    /**
     * status 的创建时间查询区间的结束，不包含。
     */
    private Long createdTimeEnd;
}
