package com.bees360.entity.dto;

import com.bees360.entity.HouseCategory;

public class HouseCategoryDto {

	private long id;
	private int categoryIndex;
	private String category;
	private String categoryCode;
	private int subcategoryIndex;
	private String subcategory;
	private String selector;
	private String description;
	private String additional;
	private int type;

	public HouseCategoryDto() {
		super();
	}

	public HouseCategoryDto(HouseCategory houseCategory) {
		super();
		this.id = houseCategory.getId();
		this.categoryIndex = houseCategory.getCategoryIndex();
		this.category = houseCategory.getCategory();
		this.categoryCode = houseCategory.getCategoryCode();
		this.subcategoryIndex = houseCategory.getSubcategoryIndex();
		this.subcategory = houseCategory.getSubcategory();
		this.selector = houseCategory.getSelector();
		this.description = houseCategory.getDescription();
		this.additional = houseCategory.getAdditional();
		this.type = houseCategory.getType();
	}

	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getCategory() {
		return category;
	}
	public void setCategory(String category) {
		this.category = category;
	}
	public String getCategoryCode() {
		return categoryCode;
	}
	public void setCategoryCode(String categoryCode) {
		this.categoryCode = categoryCode;
	}
	public String getSelector() {
		return selector;
	}
	public void setSelector(String selector) {
		this.selector = selector;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public int getCategoryIndex() {
		return categoryIndex;
	}
	public void setCategoryIndex(int categoryIndex) {
		this.categoryIndex = categoryIndex;
	}
	public String getAdditional() {
		return additional;
	}
	public void setAdditional(String additional) {
		this.additional = additional;
	}
	public int getSubcategoryIndex() {
		return subcategoryIndex;
	}
	public void setSubcategoryIndex(int subcategoryIndex) {
		this.subcategoryIndex = subcategoryIndex;
	}
	public String getSubcategory() {
		return subcategory;
	}
	public void setSubcategory(String subcategory) {
		this.subcategory = subcategory;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	@Override
	public String toString() {
		return "HouseCategoryDto [id=" + id + ", categoryIndex=" + categoryIndex + ", category=" + category
				+ ", categoryCode=" + categoryCode + ", subcategoryIndex=" + subcategoryIndex + ", subcategory="
				+ subcategory + ", selector=" + selector + ", description=" + description + ", additional=" + additional
				+ ", type=" + type + "]";
	}

}
