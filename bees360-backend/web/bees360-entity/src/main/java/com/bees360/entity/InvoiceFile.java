package com.bees360.entity;

/**
 * <AUTHOR>
 * @date 2019/09/25 14:55
 */
public class InvoiceFile {
    private long id;
    private long projectId;
    private Long invoiceId;
    private String invoicePdfUrl;
    private String invoiceFileName;
    private long createdTime;
    private boolean isDeleted;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getProjectId() {
        return projectId;
    }

    public void setProjectId(long projectId) {
        this.projectId = projectId;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getInvoicePdfUrl() {
        return invoicePdfUrl;
    }

    public void setInvoicePdfUrl(String invoicePdfUrl) {
        this.invoicePdfUrl = invoicePdfUrl;
    }

    public String getInvoiceFileName() {
        return invoiceFileName;
    }

    public void setInvoiceFileName(String invoiceFileName) {
        this.invoiceFileName = invoiceFileName;
    }

    public long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(long createdTime) {
        this.createdTime = createdTime;
    }

    public boolean isDeleted() {
        return isDeleted;
    }

    public void setDeleted(boolean deleted) {
        isDeleted = deleted;
    }

    @Override
    public String toString() {
        return "InvoiceFile{" + "id=" + id + ", projectId=" + projectId + ", invoiceId=" + invoiceId
            + ", invoicePdfUrl='" + invoicePdfUrl + '\'' + ", invoiceFileName='" + invoiceFileName + '\''
            + ", createdTime=" + createdTime + ", isDeleted=" + isDeleted + '}';
    }
}
