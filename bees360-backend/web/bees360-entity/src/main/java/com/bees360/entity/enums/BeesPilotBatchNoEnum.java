package com.bees360.entity.enums;

import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2020/7/9 9:50 PM
 **/
public enum BeesPilotBatchNoEnum {

    COMMON_BATCH_NO("B", "bees:pilot.batchNo.")
    ;

    BeesPilotBatchNoEnum(String prefix, String key) {
        this.prefix = prefix;
        this.key = key;
    }

    public String getPrefix() {
        return prefix;
    }

    public String getKey() {
        return key;
    }

    final private String prefix;
    final private String key;


    public static BeesPilotBatchNoEnum getEnum(String prefix) {
        return Stream.of(BeesPilotBatchNoEnum.values())
            .filter(o -> Objects.equals(prefix, o.getPrefix()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("BeesPilotBatchNoEnum is null, prefix:" + prefix));
    }



}
