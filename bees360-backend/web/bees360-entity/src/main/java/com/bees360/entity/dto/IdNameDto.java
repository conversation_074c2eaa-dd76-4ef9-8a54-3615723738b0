package com.bees360.entity.dto;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class IdNameDto implements Serializable{
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;

	private long id;
	private String name;

	public IdNameDto(){}

	public IdNameDto(long id, String name){
		this.id = id;
		this.name = name;
	}

//	Getter and Setter
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	@Override
	public String toString() {
		return "IdNameDto [id=" + id + ", name=" + name + "]";
	}
}
