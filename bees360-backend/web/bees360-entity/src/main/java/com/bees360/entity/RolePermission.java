package com.bees360.entity;

import java.io.Serializable;

@SuppressWarnings("serial")
public class RolePermission implements Serializable {
	/* The definition is a little different with the roles defined in the “User” table:
	 * 0: 0 not a homeowner, and 1 means true;
	 * 1: 0 not an adjuster, and 1 means an adjuster;
	 * 2: 0 not a pilot, and 1 means a pilot;
	 * 3: the same as above to review the assembly line tasks;
	 * …
	 * 62: customer service;
	 * 63: system administrator;
    */
	// it must be assigned a value through the Enum RoleEnum.
	private int roleId;
	// The identifier of a permission.
	private long permissionId;
	// The identifier of a permission object.
	private Permission permission;

	public RolePermission() {
		super();
	}
	public RolePermission(int roleId, int permissionId) {
		super();
		this.roleId = roleId;
		this.permissionId = permissionId;
	}
	public int getRoleId() {
		return roleId;
	}
	public void setRoleId(int roleId) {
		this.roleId = roleId;
	}
	public long getPermissionId() {
		return permissionId;
	}
	public void setPermissionId(long permissionId) {
		this.permissionId = permissionId;
	}
	public Permission getPermission() {
		return permission;
	}
	public void setPermission(Permission permission) {
		this.permission = permission;
	}
}
