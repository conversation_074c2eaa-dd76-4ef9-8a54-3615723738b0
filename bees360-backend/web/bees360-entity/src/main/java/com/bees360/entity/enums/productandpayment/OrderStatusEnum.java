package com.bees360.entity.enums.productandpayment;

import com.bees360.entity.enums.BaseCodeEnum;

public enum OrderStatusEnum implements BaseCodeEnum {
	UNPAID(1, "unpaid"),
	PAID(2, "paid"),
	CANCELED(3, "canceled")
	;

	private int code;
	private String display;

	OrderStatusEnum(int code, String display) {
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}
}
