package com.bees360.entity.dto;

import lombok.Data;

@Data
public class MapLatLngResult {
    /**
     * 查询的gps是否是大概范围
     */
    private boolean latLngIsApproximate;
    /**
     * 查询结果的gps
     */
    private MapLatLng latLng;
    /**
     * 查找类型
     */
    private LatLngResultType resultType;

    public enum LatLngResultType {
        /**
         * 通过提供的完整地址查找
         */
        FULL_ADDRESS,
        /**
         * 通过提供的地址中的zip code查找
         */
        ZIP_CODE,
        /**
         * 通过提供的地址中的city和state查找
         */
        CITY_STATE
    }
}
