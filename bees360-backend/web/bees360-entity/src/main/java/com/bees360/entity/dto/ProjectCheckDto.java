package com.bees360.entity.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020/7/10 4:24 PM
 **/
@Data
public class ProjectCheckDto {
    /**
     * 该任务是否全部完成
     *
     * @see com.bees360.entity.enums.ProjectStatusEnum#TASK_CHECKED_IN
     * @see com.bees360.entity.enums.ProjectStatusEnum#TASK_CHECKED_OUT
     */
    private int checkStatus;

    /**
     * mobile check stastus
     * @see com.bees360.entity.enums.ProjectStatusEnum#ADJUSTER_UNDERWRITING_CHECKED_IN
     * @see com.bees360.entity.enums.ProjectStatusEnum#ADJUSTER_UNDERWRITING_CHECKED_OUT
     */
    private int mobileCheckStatus;

    /**
     *  drone check status
     * @see com.bees360.entity.enums.ProjectStatusEnum#PILOT_CHECKED_IN
     * @see com.bees360.entity.enums.ProjectStatusEnum#PILOT_CHECKED_OUT
     */
    private int droneCheckStatus;

    /**
     * 该工程触发 checkout 的时间
     */
    private Long checkoutTime;
}
