package com.bees360.entity.dto;

import com.bees360.entity.enums.ImagePartialViewTypeEnum;

import java.util.List;

public class SegmentImageUnderwritingDto extends SegmentImageDto {

	private int underwritingType;

	private String hintImage;

    // it must be assigned a value through the Enum ImagePartialViewTypeEnum.
    private int partialType = ImagePartialViewTypeEnum.OTHERS.getCode();

	private List<SegmentImageUnderwritingDto> images;

    private Integer orientation;

    public SegmentImageUnderwritingDto() {
    }

	public SegmentImageUnderwritingDto(String code, String additional, String description, long descriptionId,
						   int type, int underwritingType) {
		this.setCode(code);
		this.setAdditional(additional);
		this.setDescription(description);
		this.setDescriptionId(descriptionId);
		this.setType(type);
		this.underwritingType = underwritingType;

		this.setPlaceHolderType(1);
		this.setIsDamage(false);
		this.setIsRequired(false);
	}

	public int getUnderwritingType() {
		return underwritingType;
	}

	public void setUnderwritingType(int underwritingType) {
		this.underwritingType = underwritingType;
	}

    public String getHintImage() {
        return hintImage;
    }

    public void setHintImage(String hintImage) {
        this.hintImage = hintImage;
    }

    public List<SegmentImageUnderwritingDto> getImages() {
		return images;
	}

	public void setImages(List<SegmentImageUnderwritingDto> images) {
		this.images = images;
	}

    public int getPartialType() {
        return partialType;
    }

    public void setPartialType(int partialType) {
        this.partialType = partialType;
    }

    public Integer getOrientation() {
        return orientation;
    }

    public void setOrientation(Integer orientation) {
        this.orientation = orientation;
    }

    @Override
	public String toString() {
		return super.toString() + "SegmentImageUnderwritingDto{" +
				"underwritingType=" + underwritingType +
				", images=" + images +
				'}';
	}
}
