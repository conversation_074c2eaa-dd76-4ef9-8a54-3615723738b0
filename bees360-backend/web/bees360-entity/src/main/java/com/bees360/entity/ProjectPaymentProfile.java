package com.bees360.entity;

import java.io.Serial;
import java.io.Serializable;

public class ProjectPaymentProfile implements Serializable{

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;

	private long projectId;
	private int serviceFeeType;
	private double serviceFee;
	private String currency;
	private double paidDifference;
	private int paidStatus;

	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public int getServiceFeeType() {
		return serviceFeeType;
	}
	public void setServiceFeeType(int serviceFeeType) {
		this.serviceFeeType = serviceFeeType;
	}
	public double getServiceFee() {
		return serviceFee;
	}
	public void setServiceFee(double serviceFee) {
		this.serviceFee = serviceFee;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public double getPaidDifference() {
		return paidDifference;
	}
	public void setPaidDifference(double paidDifference) {
		this.paidDifference = paidDifference;
	}
	public int getPaidStatus() {
		return paidStatus;
	}
	public void setPaidStatus(int paidStatus) {
		this.paidStatus = paidStatus;
	}
}
