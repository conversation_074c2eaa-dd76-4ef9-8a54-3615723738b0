package com.bees360.entity.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/3/24 12:20 PM
 **/
@Data
public abstract class ProjectSearchOptionBase extends BaseSearchOption {

    private final String SEPARATOR = ",";
    private final String ANY_SPACE_REGEX = " +";

    /**
     * <code>statusUpdateTimeStart</code>过滤条件指定的ProjectStatus，必须与<code>statusUpdateTimeEnd</code> 一起使用才生效。
     *
     * 不提供setter
     */
    private Integer statusUpdateTimeProjectStatus;
    /**
     * <code>statusUpdateTimeStart</code> 对statusUpdateTime进行检索，必须与<code>statusUpdateTimeProjectStatus</code>一起使用才生效。
     * 表示对Project.projectStatus = <code>statusUpdateTimeProjectStatus</code>的项目进行过滤，过滤条件为Project.statusUpdateTime >
     * <code>statusUpdateTimeStart</code>。
     *
     * 不提供setter
     */
    private Long statusUpdateTimeStart;

    /**
     * 用于过滤该用户参与的项目
     */
    private Long curUserId;
    /**
     * 管理项目的公司。对应 {@link com.bees360.entity.Project#getRepairCompany()}
     */
    private Long managedBy;

    private List<Long> memberUserList;

    private List<Long> creatorList;

    public void partionFilterStatusUpdateTimeProjectStatus(Long statusUpdateTimeStart,
        Integer statusUpdateTimeProjectStatus) {
        this.statusUpdateTimeStart = statusUpdateTimeStart;
        this.statusUpdateTimeProjectStatus = statusUpdateTimeProjectStatus;
    }
    private void setStatusUpdateTimeStart(Long statusUpdateTimeStart) {
        this.statusUpdateTimeStart = statusUpdateTimeStart;
    }
    private void setStatusUpdateTimeProjectStatus(Integer statusUpdateTimeProjectStatus) {
        this.statusUpdateTimeProjectStatus = statusUpdateTimeProjectStatus;
    }
    private void setCurUserId(Long curUserId) {
        this.curUserId = curUserId;
    }
    public void additionalFilterCurUserId(Long curUserId) {
        this.curUserId = curUserId;
    }
    private void setManagedBy(Long managedBy) {
        this.managedBy = managedBy;
    }
    public void additionalFilterManagedBy(Long managedBy) {
        this.managedBy = managedBy;
    }

    protected void setBaseSearchOption(Map<String, Object> searchMap) {

        searchMap.put("pageIndex", pageIndex);
        searchMap.put("pageSize", pageSize);

        addIfNotBlankOrEmpty(searchMap, "sortKey", sortKey);
        addIfNotBlankOrEmpty(searchMap, "sortOrder", sortOrder);

        addIfNotBlankOrEmpty(searchMap, "curUserId", getCurUserId());
        addIfNotBlankOrEmpty(searchMap, "managedBy", getManagedBy());
        addIfNotBlankOrEmpty(searchMap, "statusUpdateTimeProjectStatus", getStatusUpdateTimeProjectStatus());
        addIfNotBlankOrEmpty(searchMap, "statusUpdateTimeStart", getStatusUpdateTimeStart());
    }

    protected String createFuzzyRegex(String value) {
        if(StringUtils.isBlank(value)) {
            return null;
        }
        String[] valueRegexParts = value.split(ANY_SPACE_REGEX);
        if(valueRegexParts.length == 0) {
            return null;
        }
        String fuzzyRegex = String.join("%", valueRegexParts);
        fuzzyRegex = fuzzyRegex.endsWith("%")? fuzzyRegex: fuzzyRegex + "%";
        fuzzyRegex = fuzzyRegex.startsWith("%")? fuzzyRegex: "%" + fuzzyRegex;
        return fuzzyRegex;
    }

    protected List<Integer> getIntList(String str) {
        if (StringUtils.isBlank(str.trim())) {
            return null;
        }
        String[] strings = str.split(SEPARATOR);
        List<Integer> list = new ArrayList<>();
        for (String string : strings) {
            String trimmedStr = string.trim();
            if (StringUtils.isNumeric(trimmedStr)) {
                int code = Integer.valueOf(trimmedStr);
                list.add(code);
            } else {
                throw new IllegalArgumentException("Parameter is invalid");
            }
        }
        return list;
    }

    protected List<Double> getDoubleList(String str) {
        if (StringUtils.isBlank(str.trim())) {
            return null;
        }
        String[] strings = str.split(SEPARATOR);
        List<Double> list = new ArrayList<>();
        for (String string : strings) {
            String trimmedStr = string.trim();
            try {
                list.add(Double.valueOf(trimmedStr));
            }catch (Exception e){
                throw new IllegalArgumentException("Parameter is invalid");
            }
        }
        return list;
    }

    protected <T> void addIfNotBlankOrEmpty(Map<String, Object> searchMap, String key, T value, T defaultValue) {
        if(!addIfNotBlankOrEmpty(searchMap, key, value)) {
            searchMap.put(key, defaultValue);
        }
    }

    protected boolean addIfNotBlankOrEmpty(Map<String, Object> searchMap, String key, Object value) {
        if(value == null) {
            // 忽略 null
            return false;
        }
        if((value instanceof String) && StringUtils.isBlank(value.toString())) {
            // 忽略空字符串
            return false;
        }
        if((value instanceof Collection collection) && collection.isEmpty()) {
            // 忽略空集合
            return false;
        }
        searchMap.put(key, value);
        return true;
    }

}
