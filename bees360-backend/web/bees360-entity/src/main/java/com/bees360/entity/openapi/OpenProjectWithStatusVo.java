package com.bees360.entity.openapi;

import com.bees360.entity.Project;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
public class OpenProjectWithStatusVo extends OpenProjectAddressVo {
    /**
     * project的id
     * @see Project#getProjectId()
     */
    private long id;
    /**
     * @see Project#getProjectStatus()
     * @see NewProjectStatusEnum#getValue()
     */
    private String status;

    /**
     * @see Project#getServiceType()
     * @see ProjectServiceTypeEnum#getValue()
     */
    @NotBlank
    private String serviceName = "";
    /**
     * @see Project#getPolicyNumber()
     */
    private String policyNumber;
    /**
     * @see Project#getInspectionNumber()
     */
    private String inspectionNumber;
}
