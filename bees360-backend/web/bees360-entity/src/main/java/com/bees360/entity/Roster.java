package com.bees360.entity;

import java.io.Serial;
import java.io.Serializable;

public class Roster implements Serializable {

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;
	private long rosterId;
    private String firstName;
    private String lastName;
    private String phone;
    private String email;
    private String mailingAddress;

    private String address;
    private String city;
    private String state;
    private String country;
    private String zipCode;
    private double lng;
    private double lat;

    private String operatingCityState = "";
    private String designatedHomeStateLicense = "";
    private String additionalOperatingTerritories = "";
    private String additionalLicense = "";
    private int yearsOfExperience;
    private String message = "";
    private String resumeUrl = "";

    //unit miles
    private int travelRadius;
    private boolean moreThan100MilesTraveled;
    private boolean catEventDeployed;

    private long createdTime;
    private long updatedTime;

    private String password;

    public long getRosterId() {
        return rosterId;
    }

    public void setRosterId(long rosterId) {
        this.rosterId = rosterId;
    }

    public String getUserName(){
        lastName = lastName == null ? "" : lastName;
        return (firstName + " " + lastName).trim();
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMailingAddress() {
        return mailingAddress;
    }

    public void setMailingAddress(String mailingAddress) {
        this.mailingAddress = mailingAddress;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public double getLng() {
        return lng;
    }

    public void setLng(double lng) {
        this.lng = lng;
    }

    public double getLat() {
        return lat;
    }

    public void setLat(double lat) {
        this.lat = lat;
    }

    public String getOperatingCityState() {
        return operatingCityState;
    }

    public void setOperatingCityState(String operatingCityState) {
        this.operatingCityState = operatingCityState;
    }

    public String getDesignatedHomeStateLicense() {
        return designatedHomeStateLicense;
    }

    public void setDesignatedHomeStateLicense(String designatedHomeStateLicense) {
        this.designatedHomeStateLicense = designatedHomeStateLicense;
    }

    public String getAdditionalOperatingTerritories() {
        return additionalOperatingTerritories;
    }

    public void setAdditionalOperatingTerritories(String additionalOperatingTerritories) {
        this.additionalOperatingTerritories = additionalOperatingTerritories;
    }

    public String getAdditionalLicense() {
        return additionalLicense;
    }

    public void setAdditionalLicense(String additionalLicense) {
        this.additionalLicense = additionalLicense;
    }

    public int getYearsOfExperience() {
        return yearsOfExperience;
    }

    public void setYearsOfExperience(int yearsOfExperience) {
        this.yearsOfExperience = yearsOfExperience;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getResumeUrl() {
        return resumeUrl;
    }

    public void setResumeUrl(String resumeUrl) {
        this.resumeUrl = resumeUrl;
    }

    public int getTravelRadius() {
        return travelRadius;
    }

    public void setTravelRadius(int travelRadius) {
        this.travelRadius = travelRadius;
    }

    public boolean isMoreThan100MilesTraveled() {
        return moreThan100MilesTraveled;
    }

    public void setMoreThan100MilesTraveled(boolean moreThan100MilesTraveled) {
        this.moreThan100MilesTraveled = moreThan100MilesTraveled;
    }

    public boolean isCatEventDeployed() {
        return catEventDeployed;
    }

    public void setCatEventDeployed(boolean catEventDeployed) {
        this.catEventDeployed = catEventDeployed;
    }

    public long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(long createdTime) {
        this.createdTime = createdTime;
    }

    public long getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(long updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
