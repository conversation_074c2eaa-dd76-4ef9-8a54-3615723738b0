package com.bees360.entity;

import java.io.Serializable;

@SuppressWarnings("serial")
public class Member implements Serializable {
	// The identifier of a project.
	private long projectId;
	// The identifier of a user related with a project.
	private long userId;
	// Referenced the definition of roles in User table; however, it is allowed to have only one role here.
	// When role is null, then this user can only view some property of this project.
	private Integer role;
	// The time that a user_id is added to relate with the project.
	private long createdTime;
	// The user who links this “user_id” to this “project_id”.
	private long createdBy;
	// Describes why this “user_id” is linked with this “project_id”. It is provided by created_by.
	private String description;

	private boolean deleted;

	// primary key of Member table is project_id-user_id-role, so role cannot be null.
	public static final int PROJECT_CREATER = -1;

	public Member() {
		super();
	}

	public Member(long projectId, long userId, int role) {
		super();
		this.projectId = projectId;
		this.userId = userId;
		this.role = role;
	}

	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public Integer getRole() {
		return role;
	}

	public void setRole(Integer role) {
		this.role = role;
	}

	public long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}

	public long getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(long createdBy) {
		this.createdBy = createdBy;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public boolean getDeleted(){
		return deleted;
	}

	public void setDeleted(boolean deleted){
		this.deleted = deleted;
	}

	@Override
	public String toString() {
		return "Member [projectId=" + projectId + ", userId=" + userId + ", role=" + role + ", createdTime="
				+ createdTime + ", createdBy=" + createdBy + ", description=" + description + ", deleted=" + deleted
				+ "]";
	}
}
