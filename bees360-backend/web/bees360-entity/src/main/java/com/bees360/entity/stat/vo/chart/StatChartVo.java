package com.bees360.entity.stat.vo.chart;

import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.stat.dto.ProjectStatChartDto;
import com.bees360.entity.stat.vo.ClassifiedItem;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Data
public class StatChartVo {

    private static int TOP_N = 10;

    // 预留 others
    private List<HistogramVo> histogram;
    private List<PieItem> pie;

    @Data
    public static class HistogramVo{
        private String state;
        private Integer count;
        private Double percentage;
        private List<ClassifiedItem> items;

        public void addItem(ClassifiedItem item){
            if (items == null){
                items = new ArrayList<>();
            }
            items.add(item);
        }
    }

    @Data
    @RequiredArgsConstructor
    public static class PieItem{
        private final String name;
        private final Double value;
    }

    public static StatChartVo buildVo(List<ProjectStatChartDto> chartDto){
        final StatChartVo statChartVo = new StatChartVo();
        final List<HistogramVo> histogramVos = buildHistogram(chartDto);

        statChartVo.setHistogram(histogramVos);

        statChartVo.setPie(buildPie(histogramVos));

        return statChartVo;
    }


    private static List<HistogramVo> buildHistogram(final List<ProjectStatChartDto> chartDto){

        List<HistogramVo> histogramVos = chartDto.stream()
            .collect(Collectors.groupingBy(ProjectStatChartDto::getState))
            .entrySet().stream()
            .map(entry->{

                final HistogramVo histogram = new HistogramVo();
                histogram.setState(entry.getKey());
                histogram.setCount(entry.getValue().size());
                histogram.setPercentage((double)entry.getValue().size() / chartDto.size() * 100);

                final List<ClassifiedItem> classifiedItems = entry.getValue().stream()
                    .collect(Collectors.groupingBy(ProjectStatChartDto::getServiceType))
                    .entrySet().stream()
                    .map(ent -> new ClassifiedItem(ProjectServiceTypeEnum.getEnum(ent.getKey()).getValue(), ent.getValue().size()))
                    .collect(Collectors.toList());

                histogram.setItems(classifiedItems);

                return histogram;
            }).sorted(Comparator.comparingInt(HistogramVo::getCount).reversed())
            .collect(Collectors.toList());

        return topAndOthers(histogramVos, TOP_N);
    }

    private static List<PieItem> buildPie(List<HistogramVo> histogramVos){
        return histogramVos.stream()
            .map(histogramVo -> new PieItem(histogramVo.getState(), histogramVo.getPercentage()))
            .collect(Collectors.toList());
    }

    private static List<HistogramVo> topAndOthers(List<HistogramVo> histogramVos, int topN){
        if (histogramVos.size() > topN){
            final List<HistogramVo> h = histogramVos.subList(0, topN + 1);
            final HistogramVo others = new HistogramVo();
            others.setState("others");
            others.setPercentage(0.0);
            others.setCount(0);
            Map<String, ClassifiedItem> itemMap = new HashMap<>();
            histogramVos.subList(topN, histogramVos.size()).forEach(histogramVo -> {
                final Integer othersCount = others.getCount();
                final Double othersPercentage = others.getPercentage();
                others.setCount(othersCount + histogramVo.getCount());
                others.setPercentage(othersPercentage + histogramVo.getPercentage());

                histogramVo.getItems().forEach(item->{
                    final ClassifiedItem ci = itemMap.get(item.getName());
                    if (ci == null){
                        itemMap.put(item.getName(), item);
                    }else{
                        final int count = ci.getCount() + item.getCount();
                        ci.setCount(count);
                    }
                });
            });
            others.setItems(new ArrayList<>(itemMap.values()));
            h.set(topN, others);
            return h;
        }else{
            return histogramVos;
        }
    }

    public void addDefaultIfAbsentState(List<String> states){

        if (states == null || states.isEmpty()){
            return;
        }

        final Set<String> existStates = histogram.stream().map(HistogramVo::getState).collect(Collectors.toSet());

        HashSet<String> sets = new HashSet<>(states);
        sets.removeAll(existStates);

        if (sets.isEmpty()){
            return;
        }

        sets.forEach(state ->{
            final HistogramVo histogramVo = new HistogramVo();
            histogramVo.setState(state);
            histogramVo.setCount(0);
            histogramVo.setPercentage(0d);
            histogramVo.setItems(new ArrayList<>());
            histogram.add(histogramVo);
        });
    }

    public void addDefaultIfAbsentServiceType(Set<Integer> serviceTypes){
        for (StatChartVo.HistogramVo histogramVo : histogram){
            final List<ClassifiedItem> items = histogramVo.getItems();
            List<Integer> includedServiceType = new ArrayList<>();
            for (ClassifiedItem item : items){
                final ProjectServiceTypeEnum serviceTypeEnum = ProjectServiceTypeEnum.getEnumByValue(item.getName());
                if (serviceTypeEnum == null){
                    continue;
                }
                includedServiceType.add(serviceTypeEnum.getCode());
            }
            for (Integer serviceType : serviceTypes){
                if (includedServiceType.contains(serviceType)){
                    continue;
                }
                histogramVo.addItem(new ClassifiedItem(ProjectServiceTypeEnum.getEnum(serviceType).getValue(), 0));
            }
        }
    }
}
