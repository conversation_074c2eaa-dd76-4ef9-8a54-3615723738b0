package com.bees360.entity;

import java.io.Serializable;

@SuppressWarnings("serial")
public class EventHistory implements Serializable, Cloneable {
	private long eventId;
	private long projectId;
	private long userId;
	// it must be assigned a value through the Enum ProjectStatusEnum.
	private int status;
	// The time that this event should happen.
	private Long statusTime;
	private String description;
	// The time this record is added to database
	private long createdTime;
	// The user_id who makes this record happen.
	private long modifiedBy;
    /**
     * the operation name of this event
     */
	private String statusName;

	private String title;

	public EventHistory() {
		super();
	}
	public EventHistory(long eventId) {
		super();
		this.eventId = eventId;
	}
	public long getEventId() {
		return eventId;
	}
	public void setEventId(long eventId) {
		this.eventId = eventId;
	}
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public long getUserId() {
		return userId;
	}
	public void setUserId(long userId) {
		this.userId = userId;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public Long getStatusTime() {
		return statusTime;
	}
	public void setStatusTime(Long statusTime) {
		this.statusTime = statusTime;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public long getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}
	public long getModifiedBy() {
		return modifiedBy;
	}
	public void setModifiedBy(long modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
	public Object clone() throws CloneNotSupportedException{
		EventHistory hisotry = (EventHistory)super.clone();
		return hisotry;
	}

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    @Override
    public String toString() {
        return "EventHistory{" +
            "eventId=" + eventId +
            ", projectId=" + projectId +
            ", userId=" + userId +
            ", status=" + status +
            ", statusTime=" + statusTime +
            ", description='" + description + '\'' +
            ", createdTime=" + createdTime +
            ", modifiedBy=" + modifiedBy +
            ", statusName='" + statusName + '\'' +
            ", title='" + title + '\'' +
            '}';
    }
}
