package com.bees360.entity.dto;

import java.io.Serial;
import java.io.Serializable;

/**
 * ProjectFacetTypeDto
 * <AUTHOR>
 *
 */
public class ProjectFacetTypeDto implements Serializable {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = -5700852716436136800L;

	//The identifier of the facet.
	private int facetId;

	private int[] pathType;

	public ProjectFacetTypeDto() {
		super();
	}

	public ProjectFacetTypeDto(int facetId, int[] pathType) {
		super();
		this.facetId = facetId;
		this.pathType = pathType;
	}

	public int getFacetId() {
		return facetId;
	}

	public void setFacetId(int facetId) {
		this.facetId = facetId;
	}

	public String getPathType() {
		StringBuilder types = new StringBuilder();
		types.append("[");
		for (int type : pathType) {
			types.append(type).append(",");
		}
		types.setLength(types.length() - 1);
		types.append("]");
		return types.toString();
	}

	public void setPathType(int[] pathType) {
		this.pathType = pathType;
	}

	@Override
	public String toString() {
		return "ProjectFacetTypeVo [facetId=" + facetId + ", pathType=" + getPathType() + "]";
	}

}
