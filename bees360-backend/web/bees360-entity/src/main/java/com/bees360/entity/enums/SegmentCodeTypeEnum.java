package com.bees360.entity.enums;

import java.util.ArrayList;
import java.util.List;

public enum SegmentCodeTypeEnum implements BaseCodeEnum {
	STRUCTURE(10, "Structure", new int[]{0}),
	ELEVATION(40, "General Information", new int[]{10}),
	ELEVATION_DIRECTION(50, "Elevation Direction", new int[]{10}),
	ROOF(20, "Roof Overall", new int[]{10}),
	ROOF_DIRECTION(30, "Roof Direction", new int[]{10}),
	GENERAL_CODE(60, "General Code", new int[]{40}),
	ROOF_DIRECTION_CODE(70, "Roof Direction Code", new int[]{30}),
	ELEVATION_DIRECTION_CODE(80, "Elevation Direction Code", new int[]{50});

	public static final int DEFAULT = -1;

	private final int code;
	private final String display;
	private final int[] parentsCode;

	SegmentCodeTypeEnum(int code, String display, int[] parentsCode){
		this.code = code;
		this.display = display;
		this.parentsCode = parentsCode;
	}

	public static SegmentCodeTypeEnum getEnum(int code) {
		for(SegmentCodeTypeEnum direction: SegmentCodeTypeEnum.values()) {
			if(direction.getCode() == code) {
				return direction;
			}
		}
		return null;
	}

	public static List<SegmentCodeTypeEnum> getChildTypes(int code) {
		List<SegmentCodeTypeEnum> codeTypeList = new ArrayList<>();
		for(SegmentCodeTypeEnum direction: SegmentCodeTypeEnum.values()) {
			for (int i = 0; i < direction.getParentsCode().length; i++) {
				if (direction.getParentsCode()[i] == code) {
					codeTypeList.add(direction);
					break;
				}
			}
		}
		return codeTypeList;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public int[] getParentsCode() {
		return parentsCode;
	}

}
