package com.bees360.entity.enums.productandpayment;

import java.util.HashMap;
import java.util.Map;

import com.bees360.entity.enums.BaseCodeEnum;

public enum PriceTypeEnum implements BaseCodeEnum {
	FIXED_PRICE(1, "fixed price", PriceStatusEnum.CERTAIN),
	RULE_PRICE(2, "rule price", PriceStatusEnum.UNCERTAIN),
	NEGOTIATED_PRICE(3, "negotiated price", PriceStatusEnum.UNCERTAIN),
	FLOOR_PRICE(4, "floor price", PriceStatusEnum.UNCERTAIN)
	;

	private final int code;
	private final String display;
	private final PriceStatusEnum defaultPriceStatus;

	private static final Map<Integer, PriceTypeEnum> ENUM_MAP;

	static {
		ENUM_MAP = new HashMap<Integer, PriceTypeEnum>();
		for(PriceTypeEnum type: values()) {
			ENUM_MAP.put(type.getCode(), type);
		}
	}

	PriceTypeEnum(int code, String display, PriceStatusEnum defaultPriceStatus) {
		this.code = code;
		this.display = display;
		this.defaultPriceStatus = defaultPriceStatus;
	}

	@Override
	public int getCode() {
		return code;
	}
	@Override
	public String getDisplay() {
		return display;
	}
	public PriceStatusEnum getDefaultPriceStatus() {
		return defaultPriceStatus;
	}
	public static PriceTypeEnum getEnum(int code) {
		return ENUM_MAP.get(code);
	}
}
