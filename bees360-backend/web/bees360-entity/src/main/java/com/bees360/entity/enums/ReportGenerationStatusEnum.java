package com.bees360.entity.enums;


import com.bees360.entity.dto.CodeNameDto;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 报告的生命周期
 * @see com.bees360.internal.ai.entity.enums.ReportGenerationStatusEnum
 */
public enum ReportGenerationStatusEnum implements BaseCodeEnum {
    /**
     * 默认值，0值不被使用。正常流程不会使用到该值
     */
    NOTCENERATED(0, "Not Generated", true),
    /**
     * 生命周期状态：报告已经生成，报告的初始状态
     */
    GENERATED(1, "Generated", true),
    /**
     * 生命周期状态：Processor生成并检查完成报告，提交到Reviewer检查
     */
    SUBMITTED(2, "Under Review", true),
    /**
     * 生命周期状态：Reviewer 审核通过报告，此时的报告可以返回给客户
     */
    APPROVED(3, "Approved", true),
    /**
     * 生命周期状态：报告审核不通过，需要Processor重新生成报告
     */
    DISAPPROVED(4, "Disapproved", false),

    /**
     * 过程状态：报告被上传到symbility
     */
    UPLOADED(5, "Uploaded", true),
    /**
     * 过程状态：报告生成失败
     */
    FAILED(6, "Failed", false),
    /**
     * 过程状态：报告开始生成
     */
    STARTED(7, "Started", false)
    // 本enum为报告生命周期状态定义，不要再增加过程状态了。
    ;

    private final int code;
    private final String display;
    private final boolean visible;

    ReportGenerationStatusEnum(int code, String display, boolean visible) {
        this.code = code;
        this.display = display;
        this.visible = visible;
    }

    public static ReportGenerationStatusEnum getEnum(int code) {
        for (ReportGenerationStatusEnum status : ReportGenerationStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    public static List<CodeNameDto> getVisibleDict() {
        return Arrays.stream(ReportGenerationStatusEnum.values())
            .filter(ReportGenerationStatusEnum::isVisible)
            .map(o -> new CodeNameDto(o.getCode(), o.getDisplay()))
            .collect(Collectors.toList());
    }

    public boolean isCompleted() {
        return this == APPROVED || this == UPLOADED;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    public boolean isVisible() {
        return visible;
    }

    public static void main(String[] args) {
        show();
    }

    private static void show() {
        System.out.println("|name|code|");
        System.out.println("|----|----|");
        for (ReportGenerationStatusEnum type : ReportGenerationStatusEnum.values()) {
            System.out.println("|" + type.getDisplay() + "|" + type.getCode() + "|");
        }
    }
}
