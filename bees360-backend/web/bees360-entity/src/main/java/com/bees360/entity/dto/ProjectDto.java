package com.bees360.entity.dto;

import com.bees360.entity.Project;
import lombok.Data;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

/**
 * 用于创建Project
 *
 * <AUTHOR>
 * @date 2019/12/19 17:37
 */
@Data
@ToString(callSuper = true)
public class ProjectDto extends ProjectBaseDto {

    /**
     * @see Project#getInsuranceCompany()
     */
    private Long insuranceCompany;
    /**
     * @see Project#getRepairCompany()
     */
    private Long repairCompany;

    private String creationChannel = "";

    private String operatingCompany;

    private boolean allowDuplication = true;

    private String cloneFrom;

    @Override
    public Project toProject() {
        Project project = new Project();
        BeanUtils.copyProperties(this, project);
        return project;
    }
}
