package com.bees360.entity.dto;

import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/04/16 16:57
 */
@Data
public class DjiPointInfo {
    private List<Area> areas;

    private String country;

    private double lat;

    private double lng;

    private int radius;

    private String status;

    // header 暂时用不上，注释以防止出现非String map String类型的返回值而导致错误
    // private Map<String, String> header;

    @Data
    public static class Area {
        private int area_id;

        private String name;

        private int type;

        private int shape;

        private double lat;

        private double lng;

        private int radius;

        private int level;

        private int begin_at;

        private int end_at;

        private String country;

        private String city;

        private String polygon_points;

        private String color;

        private String url;

        private List<SubAreas> sub_areas;

        private String description;

        private int height;

        private String address;

        private int data_source;
    }

    @Data
    public static class SubAreas {
        private String color;

        private int height;

        private int level;

        private double lat;

        private double lng;

        private int radius;

        /**
         * 多个polygon的集合
         * [
         *  [[a1, b1], [a2, b2],...,[an, bn]],
         *  [[c1, d1],...,[cn, dn]]
         * ]
         */
        private double[][][] polygon_points;

        private int shape;
    }
}
