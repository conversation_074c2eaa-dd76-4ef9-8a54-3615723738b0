package com.bees360.entity.enums;

public enum RealtimeUploadTypeEnum implements BaseCodeEnum{

	FIRST(1, "first upload"),
	SECOND(2, "second upload"),
	THIRD(3, "third upload"),
	ALL(4, "all upload");

	private final int code;
	private final String display;
	RealtimeUploadTypeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public static RealtimeUploadTypeEnum getEnum(int code){
		RealtimeUploadTypeEnum[] realtimeUploadTypes = RealtimeUploadTypeEnum.values();
		for(RealtimeUploadTypeEnum realtimeUploadType: realtimeUploadTypes){
			if(realtimeUploadType.code == code){
				return realtimeUploadType;
			}
		}
		return null;
	}

}
