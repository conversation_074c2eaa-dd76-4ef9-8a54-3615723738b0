package com.bees360.entity;

import com.bees360.entity.dto.Point;
import java.io.Serial;
import java.io.Serializable;

public class ImageFacet implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

	private int facetId;
	private String imageId;
	private long projectId;
	private String path2DFull;
	private String path2DSeen;
	private String path2DCrspOverview;
	private float projectedAngel;
	private double absAreaPerPixel;
	private double centerPointX;
	private double centerPointY;
	private double relevanceScore;

	public Point center() {
		return new Point(centerPointX, centerPointY);
	}
	/* getter and setter */
	public int getFacetId() {
		return facetId;
	}
	public void setFacetId(int facetId) {
		this.facetId = facetId;
	}
    public String getImageId() {
        return imageId;
    }
    public void setImageId(String imageId) {
        this.imageId = imageId;
    }
    public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public String getPath2DFull() {
		return path2DFull;
	}
	public void setPath2DFull(String path2dFull) {
		path2DFull = path2dFull;
	}
	public String getPath2DSeen() {
		return path2DSeen;
	}
	public void setPath2DSeen(String path2dSeen) {
		path2DSeen = path2dSeen;
	}
	public String getPath2DCrspOverview() {
		return path2DCrspOverview;
	}
	public void setPath2DCrspOverview(String path2dCrspOverview) {
		path2DCrspOverview = path2dCrspOverview;
	}
	public float getProjectedAngel() {
		return projectedAngel;
	}
	public void setProjectedAngel(float projectedAngel) {
		this.projectedAngel = projectedAngel;
	}
	public double getAbsAreaPerPixel() {
		return absAreaPerPixel;
	}
	public void setAbsAreaPerPixel(double absAreaPerPixel) {
		this.absAreaPerPixel = absAreaPerPixel;
	}
	public double getCenterPointX() {
		return centerPointX;
	}
	public void setCenterPointX(double centerPointX) {
		this.centerPointX = centerPointX;
	}
	public double getCenterPointY() {
		return centerPointY;
	}
	public void setCenterPointY(double centerPointY) {
		this.centerPointY = centerPointY;
	}
	public double getRelevanceScore() {
		return relevanceScore;
	}
	public void setRelevanceScore(double relevanceScore) {
		this.relevanceScore = relevanceScore;
	}

	@Override
	public String toString() {
		return "ImageFacet [facetId=" + facetId + ", imageId=" + imageId + ", projectId=" + projectId + ", path2DFull="
				+ path2DFull + ", path2DSeen=" + path2DSeen + ", path2DCrspOverview=" + path2DCrspOverview
				+ ", projectedAngel=" + projectedAngel + ", absAreaPerPixel=" + absAreaPerPixel + ", centerPointX="
				+ centerPointX + ", centerPointY=" + centerPointY + ", relevanceScore=" + relevanceScore + "]";
	}
}
