package com.bees360.entity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户的所有支付行为，该对象的数据字段会和第三方平台直接对接
 *
 * <AUTHOR>
 *
 */
public class UserPayment implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

	//** 基本信息 **//
	private long paymentId;
	private long userId;
	// project 默认为 0，非0值为有效值
	private long projectId;

	// 支付方式 {@link PaymentMethodEnum#display}
	private String paymentMethod;
	// 操作的类型：充值，消费 {@link PaymentOperationTypeEnum#code}
	private int operationType;

	//** 用户费用信息 **//
	// 总的费用 totalFeeAmount = paidServiceFeeAmount + tax
	private double totalFeeAmount;
	// 基本总费用
	private double paidServiceFeeAmount;
	// 税
	private double tax;

	//** 支付平台信息 **//
	// 支付平台 {@link PaymentChannelEnum#code}
	private int channel;
	// 交易流水号，可用于发起退款等操作
	private String transactionNo;

	//** 时间信息 **//
	private long paidTime;

	//** 附加信息 **//
	// 本次支付相关的 serviceFeeType，这个字段以后可能考虑去掉 {@link ServiceFeeTypeEnum#code}
	private int serviceFeeType;
	private String description;

	//** getter and setter **//
	public long getPaymentId() {
		return paymentId;
	}
	public void setPaymentId(long paymentId) {
		this.paymentId = paymentId;
	}
	public long getUserId() {
		return userId;
	}
	public void setUserId(long userId) {
		this.userId = userId;
	}
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public String getPaymentMethod() {
		return paymentMethod;
	}
	public void setPaymentMethod(String paymentMethod) {
		this.paymentMethod = paymentMethod;
	}
	public int getOperationType() {
		return operationType;
	}
	public void setOperationType(int operationType) {
		this.operationType = operationType;
	}
	public double getTotalFeeAmount() {
		return totalFeeAmount;
	}
	public void setTotalFeeAmount(double totalFeeAmount) {
		this.totalFeeAmount = totalFeeAmount;
	}
	public double getPaidServiceFeeAmount() {
		return paidServiceFeeAmount;
	}
	public void setPaidServiceFeeAmount(double paidServiceFeeAmount) {
		this.paidServiceFeeAmount = paidServiceFeeAmount;
	}
	public double getTax() {
		return tax;
	}
	public void setTax(double tax) {
		this.tax = tax;
	}
	public int getChannel() {
		return channel;
	}
	public void setChannel(int channel) {
		this.channel = channel;
	}
	public String getTransactionNo() {
		return transactionNo;
	}
	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}
	public long getPaidTime() {
		return paidTime;
	}
	public void setPaidTime(long paidTime) {
		this.paidTime = paidTime;
	}
	public int getServiceFeeType() {
		return serviceFeeType;
	}
	public void setServiceFeeType(int serviceFeeType) {
		this.serviceFeeType = serviceFeeType;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
}
