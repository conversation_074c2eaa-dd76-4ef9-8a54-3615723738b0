package com.bees360.entity;

import com.bees360.entity.dto.AppSegmentDto;
import com.fasterxml.jackson.annotation.JsonIgnore;

import org.springframework.util.ObjectUtils;

public class AppSegmentValue {
	private String code;
	private String imagesUrl;
	private String description;
	private String additional;
	private long descriptionId;
	private long categoryId;
	private long subcategoryIndex;
	private boolean isDamage;
	private long fileSize;
	private int imageHeight;
	private int imageWidth;
	private double gpsLocationLongitude;
	private double gpsLocationLatitude;
	private long shootingTime;
	private String originalFileName;
    // 图片信息中的 TIFF/Orientation，默认值为1(Normal)
    private int tiffOrientation = 1;
	public long getShootingTime() {
		return shootingTime;
	}
	public void setShootingTime(long shootingTime) {
		this.shootingTime = shootingTime;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getImagesUrl() {
		return imagesUrl;
	}
	public void setImagesUrl(String imagesUrl) {
		this.imagesUrl = imagesUrl;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public long getCategoryId() {
		return categoryId;
	}
	public void setCategoryId(long categoryId) {
		this.categoryId = categoryId;
	}
	public boolean getIsDamage() {
		return isDamage;
	}
	public void setIsDamage(boolean isDamage) {
		this.isDamage = isDamage;
	}
	public int getImageHeight() {
		return imageHeight;
	}
	public void setImageHeight(int imageHeight) {
		this.imageHeight = imageHeight;
	}
	public int getImageWidth() {
		return imageWidth;
	}
	public void setImageWidth(int imageWidth) {
		this.imageWidth = imageWidth;
	}
	public double getGpsLocationLongitude() {
		return gpsLocationLongitude;
	}
	public void setGpsLocationLongitude(double gpsLocationLongitude) {
		this.gpsLocationLongitude = gpsLocationLongitude;
	}
	public double getGpsLocationLatitude() {
		return gpsLocationLatitude;
	}
	public void setGpsLocationLatitude(double gpsLocationLatitude) {
		this.gpsLocationLatitude = gpsLocationLatitude;
	}
	public long getFileSize() {
		return fileSize;
	}
	public void setFileSize(long fileSize) {
		this.fileSize = fileSize;
	}
	public String getAdditional() {
		return additional;
	}
	public void setAdditional(String additional) {
		this.additional = additional;
	}
	public long getSubcategoryIndex() {
		return subcategoryIndex;
	}
	public void setSubcategoryIndex(long subcategoryIndex) {
		this.subcategoryIndex = subcategoryIndex;
	}
	public long getDescriptionId() {
		return descriptionId;
	}
	public void setDescriptionId(long descriptionId) {
		this.descriptionId = descriptionId;
	}
    public int getTiffOrientation() {
        return tiffOrientation;
    }
    public void setTiffOrientation(int tiffOrientation) {
        this.tiffOrientation = tiffOrientation;
    }
    @JsonIgnore
	public String getOriginalFileName() {
		return originalFileName;
	}
	public void setOriginalFileName(String originalFileName) {
		this.originalFileName = originalFileName;
	}
	public long getLastCode() {
		if (ObjectUtils.isEmpty(this.code)) {
			return 0;
		}
		String[] codeStrs = this.code.split(AppSegmentDto.SEGMENT_CODE_SPLIT);
		return Long.parseLong(codeStrs[codeStrs.length - 1]);
	}
	public long getParentLastCode() {
		if (ObjectUtils.isEmpty(this.code)) {
			return 0;
		}
		String[] codeStrs = this.code.split(AppSegmentDto.SEGMENT_CODE_SPLIT);
		if (codeStrs.length < 3) {
			if (codeStrs.length == 2) {
				return Long.parseLong(codeStrs[1]);
			}
			return 0;
		}
		return Long.parseLong(codeStrs[codeStrs.length - 2]);
	}
	@Override
	public String toString() {
		return "AppSegmentValue [code=" + code + ", imagesUrl=" + imagesUrl + ", description=" + description
				+ ", additional=" + additional + ", descriptionId=" + descriptionId + ", categoryId=" + categoryId
				+ ", subcategoryIndex=" + subcategoryIndex + ", isDamage=" + isDamage + ", fileSize=" + fileSize
				+ ", imageHeight=" + imageHeight + ", imageWidth=" + imageWidth + ", gpsLocationLongitude="
				+ gpsLocationLongitude + ", gpsLocationLatitude=" + gpsLocationLatitude + "]";
	}

}
