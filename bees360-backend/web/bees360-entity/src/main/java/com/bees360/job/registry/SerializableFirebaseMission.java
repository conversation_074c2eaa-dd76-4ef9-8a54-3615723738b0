package com.bees360.job.registry;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.io.Serial;
import java.util.Objects;


/**
 * 包装
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@JobPayload
@ToString(callSuper = true)
public class SerializableFirebaseMission extends SerializableFirebaseIBeesMission {

    @Serial
    private static final long serialVersionUID = -6463943951404205612L;
    private String missionType;

    enum MissionType {
        // 正常创建的mission, 有 project, 且 project 不是 test 的 case
        NORMAL("N"),
        // 从 Test Flight 创建的，目前没有 project
        TEST_FLIGHT("TF"),
        // 有 project, 但是 project 是 test 的 case
        TEST_PROJECT("TP"),
        ;
        @Getter
        private final String code;

        MissionType(String code){
            this.code = code;
        }
    }

    public boolean needIgnore() {
        return Objects.equals(MissionType.TEST_FLIGHT.getCode(), missionType);
    }
}
