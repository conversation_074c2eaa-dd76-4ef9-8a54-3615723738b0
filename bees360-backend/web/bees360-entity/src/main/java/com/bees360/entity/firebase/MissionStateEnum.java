package com.bees360.entity.firebase;

import com.bees360.entity.enums.BaseCodeEnum;

/**
 * <AUTHOR>
 * @since 2020/8/25 4:03 下午
 **/
public enum MissionStateEnum implements BaseCodeEnum {
    /**
     * 等待飞手确认
     */
    WAITING(-100, "waiting"),

    /**
     * 飞手拒绝
     */
    REFUSED(-50, "refused"),

    /**
     * 飞手接受该任务， 任务开始
     */
    NEW(0, "new"),
    /**
     * rework状态，rework之后会变成check in.
     */
    REWORK(50, "rework"),

    /**
     * 开始做任务
     */
    CHECK_IN(100, "check in"),

    /**
     * 拍摄任务完成
     */
    CHECK_OUT(200, "check out"),

    /**
     * 飞手上传了部分图片，但是由于网络原因或者无法下载无人机照片没有完成mission
     */
    COMPLETED_STUCK(300, "completed stuck"),

    /**
     * 飞手完成mission
     */
    COMPLETED(400, "completed"),
    ;

    MissionStateEnum(int code, String display) {
        this.code = code;
        this.display = display;
    }

    final private String display;

    final private int code;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }
}
