package com.bees360.job.registry;

import com.bees360.entity.firebase.MissionStateEnum;
import com.bees360.firebase.entity.FbCheckoutReason;
import com.bees360.firebase.entity.FirebaseCallRecord;
import com.bees360.firebase.entity.FirebaseFeedback;
import com.bees360.firebase.entity.FirebaseProject;
import com.bees360.firebase.entity.FirebaseTimeline;
import com.google.cloud.Timestamp;
import java.io.Serial;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;


/**
 * 包装
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@JobPayload
public class SerializableFirebaseIBeesMission extends SerializableFirebaseData {

    @Serial
    private static final long serialVersionUID = -6463943951404205612L;
    private FirebaseProject project;

    /**
     * @see MissionStateEnum#getCode()
     */
    private int status;

    private List<FirebaseTimeline> timeline;

    private Timestamp lastUpdateTime;

    private Timestamp assignedTime;

    /**
     * 该mission完成时间
     */
    private Timestamp completionTime;

    String reviewImage;

    private List<String> tasks;

    private Map<String, Boolean> taskStatus;

    private Boolean isDeleted = false;

    private String batchNo;

    private List<FbCheckoutReason> checkOutReason;

    private List<FirebaseCallRecord> callRecord;

    /**
     * 飞手反馈
     */
    private List<FirebaseFeedback> feedback;

    private Map<String, String> deepLinks;
}
