package com.bees360.entity.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum InspectionTypeEnum implements BaseCodeEnum{

	UNKNOWN(0,"Unknown"),					// bitmap = 1 project's inspectionTypes is unknown when user upload a image which is not from drone
	LOW_ALTITUDE(1,"Low Altitude"),			// bitmap = 2
	HIGH_ALTITUDE(2,"High Altitude"),		// bitmap = 4
	;
	private static int MIN_TYPE_ID = 0;
	private static int MAX_TYPE_ID = 63;

	private int code;
	private String display;

	private static Map<Integer, InspectionTypeEnum> staticMap = new HashMap<Integer, InspectionTypeEnum>();
	static {
		for(InspectionTypeEnum type: InspectionTypeEnum.values()) {
			staticMap.put(type.getCode(), type);
		}
	}

	private InspectionTypeEnum(int code, String display) {
		this.code = code;
		this.display = display;
	}

	public static InspectionTypeEnum getEnum(int inspectionType) {
		return staticMap.get(inspectionType);
	}

	public static List<InspectionTypeEnum> listInsepctionTypes(long insepctionTypes){
		ArrayList<InspectionTypeEnum> list = new ArrayList<InspectionTypeEnum>();
		long bit = 1L;
		for(int i = MIN_TYPE_ID; i <= MAX_TYPE_ID; i ++){
			if((bit & insepctionTypes) == bit){
				InspectionTypeEnum inspectionTypeEnum = getEnum(i);
				if(inspectionTypeEnum != null) {
					list.add(inspectionTypeEnum);
				}
			}
			bit <<= 1;
		}
		return list;
	}

	public static List<String> listDisplays(long insepctionTypes){
		ArrayList<String> list = new ArrayList<String>();
		long bit = 1L;
		for(int i = 0; i < 3; i ++){
			if((bit & insepctionTypes) == bit){
				InspectionTypeEnum inspectionTypeEnum = getEnum(i);
				if(inspectionTypeEnum != null) {
					list.add(inspectionTypeEnum.getDisplay());
				}
			}
			bit <<= 1;
		}
		return list;
	}

	public static long bitMapStringToLong(String bitMapString) {
		try {
			return Long.parseLong(bitMapString, 2);
		}catch(Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	@Override
	public int getCode() {
		return this.code;
	}
	@Override
	public String getDisplay() {
		return this.display;
	}

	public long getBitMap(){
		long bit = 1L;
		for(int i = 0; i < code; i ++){
			bit <<= 1;
		}
		return bit;
	}

	public static void main(String[] args) {
		System.out.println(bitMapStringToLong("111"));
		long t = bitMapStringToLong("111");
		List<InspectionTypeEnum> enums = listInsepctionTypes(t);
		for(InspectionTypeEnum e : enums) {
			System.out.println(e.getCode() + ": " + e.getDisplay() + ": " + Long.toBinaryString(e.getBitMap()));
		}
		System.out.println("=================");
		for(InspectionTypeEnum e : InspectionTypeEnum.values()) {
			System.out.println(e.getCode() + ": " + e.getDisplay() + ": " +
					e.getBitMap() + "(" + Long.toBinaryString(e.getBitMap())+ ")");
		}
	}
}
