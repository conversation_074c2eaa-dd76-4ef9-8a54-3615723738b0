package com.bees360.entity.enums;

public enum InspectionCategoryEnum implements BaseCodeEnum{
	Roofing(0, "Roofing"),
	Exterior(1, "Exterior"),
	Interior(2, "Interior");

	private int code;
	private String display;

	InspectionCategoryEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}
	@Override
	public String getDisplay() {
		return display;
	}

}
