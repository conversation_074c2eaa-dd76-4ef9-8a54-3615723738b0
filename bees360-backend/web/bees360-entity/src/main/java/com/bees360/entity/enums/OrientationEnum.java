package com.bees360.entity.enums;

import java.util.Comparator;
import java.util.Objects;
import java.util.function.Function;

public enum OrientationEnum implements BaseCodeEnum{
	FRONT(1, "Front", 1),
	BACK(2, "Rear", 3),
	LEFT(3, "Left", 4),
	RIGHT(4, "Right", 2);

	private final int code;
	private final String display;
	// The sorting rules are front, right, back, left.
    private final int sort;

	OrientationEnum(int code, String display, int sort){
		this.code = code;
		this.display = display;
        this.sort = sort;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

    public int getSort() {
        return sort;
    }

    public static Integer sortToCodeEnum(int orientation){
        OrientationEnum orientationEnum = OrientationEnum.getBySortEnum(orientation);
	    if (orientationEnum == null) {
	        return null;
        }
        return orientationEnum.getCode();
    }

    public static int getSortByCode(int orientation){
        OrientationEnum orientationEnum = OrientationEnum.getEnum(orientation);
        if (orientationEnum == null) {
            // 按照front， right， back， left排序，不存在orientation返回0。
            return 0;
        }
        return orientationEnum.getSort();
    }

	public static OrientationEnum getEnum(Integer code){
		if(code == null) {
			return null;
		}
		OrientationEnum[] orientations = OrientationEnum.values();
		for(OrientationEnum orientation: orientations){
			if(orientation.code == code){
				return orientation;
			}
		}
		return null;
	}

    /**
     * 按方向前又后左排序，没有方向的排在最后面
     *
     * @param function 获取orientation code的function
     * @param <T>      排序的Comparator
     */
    public static <T> Comparator<T> getComparator(Function<? super T, Integer> function) {
        return (o1, o2) -> {
            OrientationEnum orientation1 = getEnum(function.apply(o1));
            OrientationEnum orientation2 = getEnum(function.apply(o2));
            if (Objects.isNull(orientation1) && Objects.isNull(orientation2)) {
                return 0;
            }
            if (Objects.isNull(orientation2)) {
                return -1;
            }
            if (Objects.isNull(orientation1)) {
                return 1;
            }
            return orientation1.getSort() - orientation2.getSort();
        };
    }

    private static OrientationEnum getBySortEnum(int sort) {
        OrientationEnum[] orientations = OrientationEnum.values();
        for(OrientationEnum orientation: orientations){
            if(orientation.sort == sort){
                return orientation;
            }
        }
        return null;
    }

	public static void main(String[] args) {
		show();
	}

	private static void show() {
		System.out.println("|name|code|");
		System.out.println("|----|----|");
		for(OrientationEnum type: OrientationEnum.values()) {
			System.out.println("|" + type.getDisplay() + "|" + type.getCode() + "|");
		}
	}
}
