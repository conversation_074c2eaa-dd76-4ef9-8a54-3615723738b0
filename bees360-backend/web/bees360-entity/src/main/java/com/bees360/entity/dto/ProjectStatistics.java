package com.bees360.entity.dto;

import com.bees360.entity.Company;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

public class ProjectStatistics {

    /**
     * 统计每个公司项目创建情况
     */
    @Getter
    @Setter
    public static class ProjectStatsPerCompany {

        private CompanyVo company;

        private InspectionPurposeTypeEnum serviceType;

        /**
         * 已创建项目数量
         */
        private int projectCreated;

        /**
         * 分配飞手项目数量
         */
        private int projectAssigned;

        /**
         * 联系户主项目数量
         */
        private int projectContacted;

        public void setCompany(Company company) {
            this.company = CompanyVo.fromCompany(company);
        }

        @Getter
        static class CompanyVo {
            CompanyVo(long companyId, String companyName, String logo) {
                this.companyId = companyId;
                this.companyName = companyName;
                this.logo = logo;
            }

            static CompanyVo fromCompany(Company company) {
                return new CompanyVo(company.getCompanyId(), company.getCompanyName(), company.getLogo());
            }
            private final long companyId;
            private final String companyName;
            private final String logo;
        }
    }

    /**
     * 统计一段时间每个公司项目创建情况
     */
    @Getter
    @Setter
    public static class ProjectStatsPerCompanySummary extends ProjectStatisticSummary{

        public ProjectStatsPerCompanySummary(InspectionPurposeTypeEnum serviceType, long startDate, long endDate,
                                             List<ProjectStatsPerCompany> perCompanyStats, long totalProjectCreated,
                                             int totalProjectAssigned, int totalProjectContacted) {
            super(serviceType, startDate, endDate, totalProjectCreated);
            this.perCompanyStats = perCompanyStats;
            this.totalProjectAssigned = totalProjectAssigned;
            this.totalProjectContacted = totalProjectContacted;
        }

        private List<ProjectStatsPerCompany> perCompanyStats;

        private int totalProjectAssigned;

        private int totalProjectContacted;

        public static ProjectStatsPerCompanySummary emptySummary(InspectionPurposeTypeEnum serviceType, long startDate, long endDate) {
            return new ProjectStatsPerCompanySummary(serviceType, startDate, endDate, new ArrayList<>(),
                0, 0, 0);
        }
    }

    /**
     * 统计一段时间内项目的数量
     */
    @Getter
    @Setter
    public static class ProjectStatisticSummary extends TimeInterval {

        private InspectionPurposeTypeEnum serviceType;


        private long totalProjectCreated;

        public ProjectStatisticSummary(InspectionPurposeTypeEnum serviceType, long startDate, long endDate,
                                       long totalProjectCreated) {
            super(startDate, endDate);
            this.serviceType = serviceType;
            this.totalProjectCreated = totalProjectCreated;
        }

        public ProjectStatisticSummary(TimeInterval timeInterval, InspectionPurposeTypeEnum serviceType, long totalProjectCreated) {
            super(timeInterval.startDate, timeInterval.endDate);
            this.serviceType = serviceType;
            this.totalProjectCreated = totalProjectCreated;
        }
    }

    @Getter
    @AllArgsConstructor
    public static class TimeInterval implements Serializable{

        /**
         * 统计开始时间
         */
        private long startDate;

        /**
         * 统计结束时间
         */
        private long endDate;
    }

    /**
     * 当前项目状态总览
     */
    @Getter
    public static class ProjectLatestStatusMetrics {
        public ProjectLatestStatusMetrics(InspectionPurposeTypeEnum inspectionPurposeTypeEnum, long notContacted,
                                          long unassigned, long imageNotUnloaded, long inProcess, long followUp) {
            this.inspectionPurposeTypeEnum = inspectionPurposeTypeEnum;
            this.notContacted = notContacted;
            this.unassigned = unassigned;
            this.imageNotUnloaded = imageNotUnloaded;
            this.inProcess = inProcess;
            this.followUp = followUp;

        }

        private final InspectionPurposeTypeEnum inspectionPurposeTypeEnum;

        /**
         * 未联系户主项目数量
         */
        private final long notContacted;

        /**
         * 未分配飞手项目数量
         */
        private final long unassigned;

        /**
         * 未上传图片项目数量
         */
        private final long imageNotUnloaded;

        /**
         * 正在处理中项目数量
         */
        private final long inProcess;

        /**
         * 有问题需要追踪的项目数量
         */
        private final long followUp;

        public static Builder builder() {
            return new Builder();
        }

        @Getter
        public static class Builder {
            private InspectionPurposeTypeEnum inspectionType;
            private final AtomicLong notContacted = new AtomicLong(0);
            private final AtomicLong unassigned = new AtomicLong(0);
            private final AtomicLong imageNotUnloaded = new AtomicLong(0);
            private final AtomicLong inProcess = new AtomicLong(0);

            private final AtomicLong followUp = new AtomicLong(0);

            public Builder inspectionType(InspectionPurposeTypeEnum inspectionType) {
                this.inspectionType = inspectionType;
                return this;
            }
            public ProjectLatestStatusMetrics build() {
                return new ProjectLatestStatusMetrics(inspectionType, notContacted.get(),
                    unassigned.get(), imageNotUnloaded.get(), inProcess.get(), followUp.get());
            }
        }
    }

    @Getter
    public static class ProjectStatusServiceType {
        private long projectId;
        private int projectStatus;
        private long inspectionTime;
        private int serviceType;
    }
}
