package com.bees360.entity.firebase;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.NoSuchElementException;

/**
 * @deprecated 从RemoteConfig中读取Firebase相关的数据
 * @see com.bees360.entity.enums.RemoteConfigParameter
 * <AUTHOR>
 * @date 2020/11/19 15:19
 */
@Deprecated
public enum FirebaseTaskEnum {

    CLAIM_FORM("claim_form", FirebaseTaskReasonTitleEnum.FORM),
    UNDERWRITING_FORM("underwriting_form", FirebaseTaskReasonTitleEnum.FORM),
    CLAIM_MOBILE_ADDRESS_VERIFICATION("claim_mobile_address_verification", FirebaseTaskReasonTitleEnum.ADDRESS),
    UNDERWRITING_MOBILE_ADDRESS_VERIFICATION("underwriting_mobile_address_verification", FirebaseTaskReasonTitleEnum.ADDRESS),
    CLAIM_MOBILE_IMAGES("claim_mobile_images", FirebaseTaskReasonTitleEnum.MOBILE),
    UNDERWRITING_MOBILE_IMAGES("underwriting_mobile_images", FirebaseTaskReasonTitleEnum.MOBILE),
    CLAIM_INTERIOR_MOBILE_IMAGES("claim_interior_mobile_images", FirebaseTaskReasonTitleEnum.INTERIOR),
    UNDERWRITING_INTERIOR_MOBILE_IMAGES("underwriting_interior_mobile_images", FirebaseTaskReasonTitleEnum.INTERIOR),
    CLAIM_EXTERIOR_MOBILE_IMAGES("claim_exterior_mobile_images", FirebaseTaskReasonTitleEnum.EXTERIOR),
    UNDERWRITING_EXTERIOR_MOBILE_IMAGES("underwriting_exterior_mobile_images", FirebaseTaskReasonTitleEnum.EXTERIOR),
    CLAIM_DRONE_IMAGES("claim_drone_images", FirebaseTaskReasonTitleEnum.DRONE),
    UNDERWRITING_DRONE_IMAGES("underwriting_drone_images", FirebaseTaskReasonTitleEnum.DRONE),
    CLAIM_WIND_DRONE_IMAGES("claim_wind_drone_images", FirebaseTaskReasonTitleEnum.DRONE),
    CLAIM_OTHERS_DRONE_IMAGES("claim_others_drone_images", FirebaseTaskReasonTitleEnum.DRONE),
    UPC_CLAIM_FORM("upc_claim_form", FirebaseTaskReasonTitleEnum.FORM)

    ;

    public static FirebaseTaskEnum getEnum(String taskId){
        return Arrays.stream(FirebaseTaskEnum.values())
            .filter(o -> StringUtils.equals(taskId, o.getTaskId()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("FirebaseTaskEnum is null. taskId:" + taskId));

    }

    FirebaseTaskEnum(String taskId, FirebaseTaskReasonTitleEnum checkoutReasonTitle) {
        this.taskId = taskId;
        this.checkoutReasonTitle = checkoutReasonTitle;
    }

    final private FirebaseTaskReasonTitleEnum checkoutReasonTitle;

    final private String taskId;

    public String getTaskId() {
        return taskId;
    }

    public FirebaseTaskReasonTitleEnum getCheckoutReasonTitle() {
        return checkoutReasonTitle;
    }
}
