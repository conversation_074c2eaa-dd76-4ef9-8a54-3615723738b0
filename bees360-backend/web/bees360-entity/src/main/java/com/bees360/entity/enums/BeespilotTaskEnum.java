package com.bees360.entity.enums;

import com.bees360.entity.CompanyIDMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/7/9 9:50 PM
 **/
public enum  BeespilotTaskEnum implements BaseCodeEnum {

    /**
     * 拍摄验证地址
     */
    VERIFY_ADDRESS(1, "Verify address", FileSourceTypeEnum.CELL_PHONE_IMAGE),

    /**
     * 填写问卷调查
     */
    FILL_IN_FORM(4, "Fill out form", null),
    /**
     * 拍摄手机照片
     */
    TAKE_MOBILE_IMAGE(2, "Take mobile images", FileSourceTypeEnum.CELL_PHONE_IMAGE),
    /**
     * 拍摄无人机照片
     */
    TAKE_DRONE_IMAGE(3, "Take drone images", FileSourceTypeEnum.DRONE_IMAGE);
    BeespilotTaskEnum(int code, String display, FileSourceTypeEnum fileSourceType) {
        this.code = code;
        this.display = display;
        this.fileSourceType = fileSourceType;
    }

    public FileSourceTypeEnum getFileSourceType() {
        return fileSourceType;
    }

    final private int code;
    final private String display;
    final private FileSourceTypeEnum fileSourceType;

    public static BeespilotTaskEnum getEnum(int code) {
        for (BeespilotTaskEnum taskEnum : values()) {
            if (taskEnum.getCode() == code) {
                return taskEnum;
            }
        }
        return null;
    }
    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    public static List<BeespilotTaskEnum> listTaskEnum(CompanyIDMap companyIDMap, Long processedBy, Long insuredBy, Integer serviceType) {
        ProjectServiceTypeEnum typeEnum = ProjectServiceTypeEnum.getEnum(serviceType);
        List<BeespilotTaskEnum> fullTask = Arrays.stream(values()).collect(Collectors.toList());
        if (typeEnum == null) {
            return fullTask;
        }
        List<BeespilotTaskEnum> taskEnums = new ArrayList<>();
        switch (typeEnum) {
            case ROOF_ONLY_UNDERWRITING:
                taskEnums.add(VERIFY_ADDRESS);
                if (processedBy != null && processedBy.equals(companyIDMap.getInsurance_Risk_Services_Inc_ID()) &&
                    insuredBy != null && insuredBy.equals(companyIDMap.getVelocity_ID())) {
                    taskEnums.add(FILL_IN_FORM);
                }
                taskEnums.add(TAKE_DRONE_IMAGE);
                break;
            default:
                taskEnums = fullTask;
                break;
        }
        return taskEnums;
    }

}
