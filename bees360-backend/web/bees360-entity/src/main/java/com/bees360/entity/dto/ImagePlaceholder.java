package com.bees360.entity.dto;

public class ImagePlaceholder {
	private Long projectId;
	private String imageId;
	private String originalFileName;
	private long fileSize;

	public Long getProjectId() {
		return projectId;
	}
	public void setProjectId(Long projectId) {
		this.projectId = projectId;
	}
	public String getImageId() {
		return imageId;
	}
	public void setImageId(String imageId) {
		this.imageId = imageId;
	}
	public String getOriginalFileName() {
		return originalFileName;
	}
	public void setOriginalFileName(String originalFileName) {
		this.originalFileName = originalFileName;
	}
	public long getFileSize() {
		return fileSize;
	}
	public void setFileSize(long fileSize) {
		this.fileSize = fileSize;
	}

	@Override
	public String toString() {
		return "ImagePlaceholder [projectId=" + projectId + ", imageId=" + imageId + ", originalFileName="
				+ originalFileName + ", fileSize=" + fileSize + "]";
	}
}
