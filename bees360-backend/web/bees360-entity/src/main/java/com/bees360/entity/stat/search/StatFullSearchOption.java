package com.bees360.entity.stat.search;

import com.bees360.common.string.Strings;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Data
public class StatFullSearchOption extends PagedSearchOption {

    private Long companyId;
    private List<Long> companyIds;
    private Long createdBy;
    private List<String> states;
    private Set<Integer> serviceTypes;
    private Long startTime;
    private Long endTime;

    private Set<Integer> includeProjectStatus;
    private Set<Integer> excludeProjectStatus;

    private BigDecimal riskScoreMin;
    private BigDecimal riskScoreMax;

    private Long centerUtcOffset;

    // search option will find no projects when projectIds is empty
    private List<Long> projectIds;

    private Integer type;

    private String batchNo;

    private String policyNumber;

    private String insuredName;
    private String insuredPhone;
    private String address;
    private String city;
    private String insuredNameRegex;
    private String insuredPhoneRegex;
    private String addressRegex;
    private String cityRegex;
    private Long policyEffectiveStartDate;
    private Long inspectionStartTime;
    private Long inspectionEndTime;
    private Long scheduledTimeStart;
    private Long scheduledTimeEnd;
    private Long from;
    private Long to;
    private Integer serviceType;
    private List<Long> memberUserList;
    private Long memberRole;
    private List<Long> searchCompanyIdList;
    private List<Long> insuranceCompanyIdList;
    private List<Long> processCompanyIdList;
    private String inspectionNumber;
    private Integer newDaysOldStart;
    private Integer newDaysOldEnd;
    private Set<Integer> serviceTypesForChartAndCard;
    private String zipCode;
    private List<Long> creatorList;

    public void addServiceType(Integer serviceType) {
        if (serviceTypes == null) {
            serviceTypes = new HashSet<>();
        }
        this.serviceTypes.add(serviceType);
    }

    public void addServiceTypeForChartAndCard(Integer serviceType) {
        if (serviceTypesForChartAndCard == null) {
            serviceTypesForChartAndCard = new HashSet<>();
        }
        this.serviceTypesForChartAndCard.add(serviceType);
    }

    public void addIncludeProjectStatus(Integer projectStatus) {
        if (projectStatus == null) {
            return;
        }
        if (includeProjectStatus == null) {
            includeProjectStatus = new HashSet<>();
        }
        includeProjectStatus.add(projectStatus);
    }

    public void addExcludeProjectStatus(Integer projectStatus) {
        if (projectStatus == null) {
            return;
        }
        if (excludeProjectStatus == null) {
            excludeProjectStatus = new HashSet<>();
        }
        excludeProjectStatus.add(projectStatus);
    }

    public StatFullSearchOption newSearchOption() {
        final StatFullSearchOption newSearchOption = new StatFullSearchOption();
        BeanUtils.copyProperties(this, newSearchOption);
        return newSearchOption;
    }

    public void intersectProjectIds(@Nullable Iterable<Long> projectIds) {
        if (projectIds == null) {
            return;
        }
        if (this.projectIds == null) {
            this.projectIds = new ArrayList<>();
            CollectionUtils.addAll(this.projectIds, projectIds);
            // add -1 as project id to avoid skipping using id as search option
            if (!this.projectIds.contains(-1L)) {
                this.projectIds.add(-1L);
            }
        } else {
            this.projectIds =
                    new ArrayList<>(CollectionUtils.intersection(this.projectIds, projectIds));
            // add -1 as project id to avoid skipping using id as search option.
            if (!this.projectIds.contains(-1L)) {
                this.projectIds.add(-1L);
            }
        }
    }

    public static void initFullSearchOptions(StatFullSearchOption fullSearchOption) {

        fullSearchOption.setSortKey(Strings.CamelCaseToUnderScore(fullSearchOption.getSortKey()));
        if (fullSearchOption.getStartTime() == null && fullSearchOption.getFrom() != null) {
            fullSearchOption.setStartTime(fullSearchOption.getFrom());
        }
        if (fullSearchOption.getEndTime() == null && fullSearchOption.getTo() != null) {
            fullSearchOption.setEndTime(fullSearchOption.getTo());
        }
        if (fullSearchOption.getServiceType() != null && CollectionUtils.isNotEmpty(fullSearchOption.getServiceTypes())) {
            fullSearchOption.addServiceType(fullSearchOption.getServiceType());
        }
        var insuredPhone = fullSearchOption.getInsuredPhone();
        var insuredName = fullSearchOption.getInsuredName();
        var address = fullSearchOption.getAddress();
        var city = fullSearchOption.getCity();
        if (address != null) {
            fullSearchOption.setAddressRegex(createFuzzyRegex(address));
        }
        if (insuredName != null) {
            fullSearchOption.setInsuredNameRegex(createFuzzyRegex(insuredName));
        }
        if (insuredPhone != null) {
            fullSearchOption.setInsuredPhoneRegex(createFuzzyRegex(insuredPhone));
        }
        if (city != null) {
            fullSearchOption.setCityRegex(createFuzzyRegex(city));
        }
    }

    private static String createFuzzyRegex(String value) {
        final String ANY_SPACE_REGEX = " +";
        if (StringUtils.isBlank(value)) {
            return value;
        }
        String[] valueRegexParts = value.split(ANY_SPACE_REGEX);
        if (valueRegexParts.length == 0) {
            return "";
        }
        String fuzzyRegex = String.join("%", valueRegexParts);
        fuzzyRegex = fuzzyRegex.endsWith("%") ? fuzzyRegex : fuzzyRegex + "%";
        fuzzyRegex = fuzzyRegex.startsWith("%") ? fuzzyRegex : "%" + fuzzyRegex;
        return fuzzyRegex;
    }
}
