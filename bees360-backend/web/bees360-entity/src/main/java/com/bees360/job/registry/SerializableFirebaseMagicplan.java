package com.bees360.job.registry;

import com.google.cloud.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** magicplan job */
@EqualsAndHashCode(callSuper = true)
@Data
@JobPayload
@ToString
public class SerializableFirebaseMagicplan extends SerializableFirebaseData {

    private String missionId;

    private String projectId;

    /**
     * magicplan 对应的报告文件key<br>
     * gs://document/xxx/ARCPhJgXrR5EO5tI1tFozg
     */
    private String pdf;

    /** 文档最近一次的更新时间 */
    private Timestamp updateTime;
}
