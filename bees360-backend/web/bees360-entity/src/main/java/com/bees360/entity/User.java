package com.bees360.entity;
import java.io.Serial;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.util.UserCertificateParser;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/*
 * User table is one of the most The user table is super important. It stores all kinds of user information (e.g.,
 * home owner, adjuster, pilot, system administrators, customer services, etc).
 */
public class User implements java.io.Serializable {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;
	// private final static String ROLE_CERTIFICATE_SEPARATOR = ":";
	// private final static String CERTIFICATE_SEPARATOR = ",";
	// private final static String ROLE_APPLICATIONS_SEPARATOR = ";";

	public final static String DEFAULT_AVATAR = "users/default/avatar.jpg";

	public static final long AI_ID = 10000L;
	public static final long IBEES = 9998L;
	public static final long BEES_PILOT_SYSTEM = 9999L;

	public static final long BOOKER_ID = 9001;

	public User(long userId) {
		super();
		this.userId = userId;
	}

	public User() {
		super();
	}

	public void setRole(RoleEnum role){
		roles = role.getBitMap();
	}

	public void addRole(RoleEnum role){
		roles = roles | role.getBitMap();
	}

	public void removeRole(RoleEnum role){
		if(hasRole(role)) {
			roles = roles^role.getBitMap();
		}
	}

	public boolean hasRole(RoleEnum role){
		return role != null && (roles & role.getBitMap()) != 0;
	}

	public boolean hasAnyRole(RoleEnum ... roles){
		for(RoleEnum role: roles) {
			if(hasRole(role)) {
				return true;
			}
		}
		return false;
	}

	public List<RoleEnum> listRoles(){
		return RoleEnum.listRoles(roles);
	}

	public boolean applayRole(RoleEnum role, String certificate){
		return applayRole(role, Arrays.asList(certificate));
	}

	public boolean applayRole(RoleEnum role, List<String> certificates){
		if(role == null){
			return false;
		}
//		apply role
		roleApplicationStatus = roleApplicationStatus | role.getBitMap();
//		add certificates and replace old certificates if it exists
		Map<Integer, List<String>> roleCertificates = parseCertificates();

		if(certificates == null){
			certificates = new ArrayList<String>();
		}
		roleCertificates.put(role.getCode(), certificates);
		certificateList = linkCertificates(roleCertificates);
		return true;
	}

	public String linkCertificates(Map<Integer, List<String>> roleCertificates) {
		if(roleCertificates == null) {
			return "";
		}
		String certificates = "";
		for(Entry<Integer, List<String>> entry: roleCertificates.entrySet()) {
			certificates += linkCertificatesForRoleApplication(entry.getKey(), entry.getValue());
		}
		return certificates;
	}

	public boolean cancelRoleApplication(RoleEnum role) {
		if(role == null || !isApplyingRole(role)){
			return false;
		}
//		apply role
		roleApplicationStatus = roleApplicationStatus ^ role.getBitMap();
		return true;
	}

	private String linkCertificatesForRoleApplication(int role, List<String> certificates) {
		return UserCertificateParser.linkCertificates(role, certificates);
	}

	public boolean isApplyingRole(RoleEnum role){
		return (roleApplicationStatus & role.getBitMap()) != 0;
	}

//	special getter for return to fond end view
	public List<IdNameDto> getRoleList(){
		return listRoles(roles);
	}

	public List<IdNameDto> getRoleApplicationList(){
		return listRoles(roleApplicationStatus);
	}

	/**
	 * parse certificateList into role:[certificate] map
	 * @return
	 */
	public Map<Integer, List<String>> parseCertificates() {
		return UserCertificateParser.parseToRoleMapCertificates(certificateList);
	}

	/**
	 * list the roleApplications and each roleApplication will map to its certificates
	 * @return
	 */
	public Map<RoleEnum, List<String>> listRoleApplicationsWithCertificates(){
		return UserCertificateParser.parseToRoleMapCertificates(roleApplicationStatus, certificateList);
	}

	private List<IdNameDto> listRoles(long roles){
		List<RoleEnum> roleList = RoleEnum.listRoles(roles);
		List<IdNameDto> roleIdNames = new ArrayList<IdNameDto>(roleList.size());
		for(RoleEnum role: roleList){
			roleIdNames.add(new IdNameDto(role.getRoleId(), role.getDisplay()));
		}
		return roleIdNames;
	}

	@JsonIgnore
	public boolean isAI() {
		return AI_ID == userId;
	}

	@JsonIgnore
	public static boolean isAI(long userId) {
		return AI_ID == userId;
	}

	// The following attributes are the basic information of a user.
	// userId is the primary key of the User table.
	private long userId;
	// The name provided by a user. It can be a real user name or a sudo name.
	private String lastName;
	private String firstName;
	// Different users have different email.
	private String email;
	private String qrCode;
	// Different users have different phone.
	private String phone;
	// The address including the street and room/building number. It does not include city, state, zipcode and country.
	private String address;
	private String city;
	private String state;
	private String country;
	private String zipCode;
	// The directory of the avatar (an image) for the user.
	private String avatar;
	// The company for this user. For a homeowner, this can be null;
	// for adjuster and pilot, this can be its host company.
	private Long companyId;
	//It is used to receive the information when update user information.
	private String companyName;
	private String companyLogo;
	// The employee id for this user in the company. For a homeowner, this can be null;
	// for an adjuster and pilot, this can be its employee id in its company.
	private String employeeId;
	// The following attributes are related with security.
	private String password;

	// The timestamp for the user to login to the system in the last time.
	private long lastLoginTime;
	// Registration timestamp.
	private long registrationTime;
	// 1: means active, 0: inactive, -1: delete
	private int activeStatus;
	// The GPS address of this user, and it is fixed generally for a time interval, e.g., two years.
	private double gpsLocationLongitude;
	private double gpsLocationLatitude;

	// The most recent GPS address of this user. It is userful for some roles, e.g., pilots.
	private double mostRecentGpsLocationLongitude;
	private double mostRecentGpsLocationLatitude;
	/* It is a bitmap to represent the user roles. From right side, the meaning of each bit is:
	 *	0: 0 not a home owner, and 1 means true;
	 *	1: 0 not an adjuster, and 1 means an adjuster;
	 *	2: 0 not a pilot, and 1 means a pilot;
	 *	3: 0 not an insurance company employee, and 1 means not;
	 *	4: 0 not a distributor, and 1 means a distributor;
	 *	5: 0 not a roofing company employee, and 1 means a roofing company employee;
	 *  ...
	 *  62: customer service;
	 *  63: system administrator;
	 */
	private long roles;
	// Record the role application status of a user. For example, if roleApplicationStatus = 3, then the rightmost
	// and the second right most bit are 1s, which means that this user applies for being a project owner and also
	// an adjuster. If the associated bit is 0, then the associated role is not in application state.
	private long roleApplicationStatus;
	// The certificate list. It is mainly useful for pilot, since a pilot needs a certificate. In addition,
	// an insurance adjuster also needs some certificates.
	// 1:http://bees360.com/a.jpg;2:http://bees360.com/a.jpg;
	private String certificateList;

	private String insuranceKeyUrls;

	public String getInsuranceKeyUrls() {
		return insuranceKeyUrls;
	}

	public void setInsuranceKeyUrls(String insuranceKeyUrls) {
		this.insuranceKeyUrls = insuranceKeyUrls;
	}

	private Integer inspectionService;

	private Integer highflyService;

	private long orderServiceTime;

	private double travelRadius;

	private String stripeCustomer;

	//difine the role-->permissions mapping;
	private Map<RoleEnum,List<RolePermission>> rolePermissionsMap;
    /**
     * 是否颁发了claim徽章
     * 表明飞手是否具有飞行Claim Case的资质
     */
    private Boolean hasClaimBadge;

    /**
     * 是否颁发了underwriting徽章
     * 表明飞手是否具有飞行Underwriting Case的资质
     */
    private Boolean hasUnderwritingBadge;

    /**
     * 是否颁发了Magicplan徽章
     * 表明飞手是否具有飞行Underwriting Case的资质
     */
    private Boolean hasMagicplanBadge;

    /** 飞手评分 */
    private Double rating;

    /**
     * 外部系统的userId
     */
    @Deprecated
    private String externalUserId;
    @Setter
    @Getter
    private Boolean isSsoUser;
    @Setter
    @Getter
    private UserMFASettings userMfaSettings;
    @Data
    public static class UserMFASettings {
        private List<String> mfaSettingList;
        private String preferredMfa;
        private Boolean phoneNumberVerified;
        private Boolean emailVerified;
    }

    public String getExternalUserId() {
        return externalUserId;
    }

    public void setExternalUserId(String externalUserId) {
        this.externalUserId = externalUserId;
    }
// TODO@SomeEngineer: In the future, we will add some financial information here.

	// All functions related with such attributes are listed here.
	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getName() {
		return (firstName + " " + lastName).trim();
	}
	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getCompanyLogo() {
		return companyLogo;
	}

	public void setCompanyLogo(String companyLogo) {
		this.companyLogo = companyLogo;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getQrCode() {
		return qrCode;
	}
	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}
	public String getZipCode() {
		return zipCode;
	}
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}
	public String getAvatar() {
		return avatar;
	}
	public void setAvatar(String avatar) {
		this.avatar = avatar;
	}
	public Long getCompanyId() {
		return companyId;
	}
	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}
	public String getEmployeeId() {
		return employeeId;
	}
	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}

	@JsonIgnore
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}

	@JsonIgnore
	public long getLastLoginTime() {
		return lastLoginTime;
	}
	public void setLastLoginTime(long lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}

	@JsonIgnore
	public long getRegistrationTime() {
		return registrationTime;
	}
	public void setRegistrationTime(long registrationTime) {
		this.registrationTime = registrationTime;
	}
	public int getActiveStatus() {
		return activeStatus;
	}

	public void setActiveStatus(int activeStatus) {
		this.activeStatus = activeStatus;
	}
	public double getGpsLocationLongitude() {
		return gpsLocationLongitude;
	}
	public void setGpsLocationLongitude(double gpsLocationLongitude) {
		this.gpsLocationLongitude = gpsLocationLongitude;
	}
	public double getGpsLocationLatitude() {
		return gpsLocationLatitude;
	}
	public void setGpsLocationLatitude(double gpsLocationLatitude) {
		this.gpsLocationLatitude = gpsLocationLatitude;
	}
	public double getMostRecentGpsLocationLongitude() {
		return mostRecentGpsLocationLongitude;
	}
	public void setMostRecentGpsLocationLongitude(double mostRecentGpsLocationLongitude) {
		this.mostRecentGpsLocationLongitude = mostRecentGpsLocationLongitude;
	}
	public double getMostRecentGpsLocationLatitude() {
		return mostRecentGpsLocationLatitude;
	}
	public void setMostRecentGpsLocationLatitude(double mostRecentGpsLocationLatitude) {
		this.mostRecentGpsLocationLatitude = mostRecentGpsLocationLatitude;
	}

	@JsonIgnore
	public long getRoles() {
		return roles;
	}
	public void setRoles(long roles) {
		this.roles = roles;
	}

	@JsonIgnore
	public String getCertificateList() {
		return certificateList;
	}
	public void setCertificateList(String certificateList) {
		this.certificateList = certificateList;
	}

	@JsonIgnore
	public long getRoleApplicationStatus() {
		return roleApplicationStatus;
	}
	public void setRoleApplicationStatus(long roleApplicationStatus) {
		this.roleApplicationStatus = roleApplicationStatus;
	}

	@JsonIgnore
	public Map<RoleEnum, List<RolePermission>> getRolePermissionsMap() {
		return rolePermissionsMap;
	}

	public void setRolePermissionsMap(Map<RoleEnum, List<RolePermission>> rolePermissionsMap) {
		this.rolePermissionsMap = rolePermissionsMap;
	}

	public Integer getInspectionService() {
		return inspectionService;
	}
	public void setInspectionService(Integer inspectionService) {
		this.inspectionService = inspectionService;
	}
	public Integer getHighflyService() {
		return highflyService;
	}
	public void setHighflyService(Integer highflyService) {
		this.highflyService = highflyService;
	}
	public long getOrderServiceTime() {
		return orderServiceTime;
	}
	public void setOrderServiceTime(long orderServiceTime) {
		this.orderServiceTime = orderServiceTime;
	}
	public double getTravelRadius() {
		return travelRadius;
	}
	public void setTravelRadius(double travelRadius) {
		this.travelRadius = travelRadius;
	}
	public String getStripeCustomer() {
		return stripeCustomer;
	}
	public void setStripeCustomer(String stripeCustomer) {
		this.stripeCustomer = stripeCustomer;
	}

    public Boolean getHasClaimBadge() {
        return hasClaimBadge;
    }

    public void setHasClaimBadge(Boolean hasClaimBadge) {
        this.hasClaimBadge = hasClaimBadge;
    }

    public Boolean getHasUnderwritingBadge() {
        return hasUnderwritingBadge;
    }

    public void setHasUnderwritingBadge(Boolean hasUnderwritingBadge) {
        this.hasUnderwritingBadge = hasUnderwritingBadge;
    }

    public Boolean getHasMagicplanBadge() {
        return hasMagicplanBadge;
    }

    public void setHasMagicplanBadge(Boolean hasMagicplanBadge) {
        this.hasMagicplanBadge = hasMagicplanBadge;
    }

    public Double getRating() {
        return rating;
    }

    public void setRating(Double rating) {
        this.rating = rating;
    }

	@Override
	public String toString() {
		return "User [userId=" + userId + ", lastName=" + lastName + ", firstName=" + firstName + ", email=" + email
				+ ", qrCode=" + qrCode + ", phone=" + phone + ", address=" + address + ", city=" + city + ", state="
				+ state + ", country=" + country + ", zipCode=" + zipCode + ", avatar=" + avatar + ", companyId="
				+ companyId + ", companyName=" + companyName + ", companyLogo=" + companyLogo + ", employeeId="
				+ employeeId + ", password=" + password + ", lastLoginTime=" + lastLoginTime + ", registrationTime="
				+ registrationTime + ", activeStatus=" + activeStatus + ", gpsLocationLongitude=" + gpsLocationLongitude
				+ ", gpsLocationLatitude=" + gpsLocationLatitude + ", mostRecentGpsLocationLongitude="
				+ mostRecentGpsLocationLongitude + ", mostRecentGpsLocationLatitude=" + mostRecentGpsLocationLatitude
				+ ", roles=" + roles + ", roleApplicationStatus=" + roleApplicationStatus + ", certificateList="
				+ certificateList + ", inspectionService=" + inspectionService + ", highflyService=" + highflyService
				+ ", orderServiceTime=" + orderServiceTime + ", travelRadius=" + travelRadius + ", stripeCustomer="
				+ stripeCustomer +", rating=" + rating + "]";
	}

	public static void main(String[] args) {
		User user = new User();
		user.applayRole(RoleEnum.ADJUSTER, "adjust.pdf");
		user.applayRole(RoleEnum.PILOT, "pilot.pdf");
		user.applayRole(RoleEnum.ADJUSTER, "adjust-new.pdf");
		user.applayRole(RoleEnum.CONTRACTOR, Arrays.asList("a.pdf", "b.pdf"));
		user.applayRole(RoleEnum.PILOT, Arrays.asList("c.pdf", "d.pdf"));
		user.applayRole(RoleEnum.INSURER, Arrays.asList());

		System.out.println(user.getRoleApplicationStatus());
		System.out.println(RoleEnum.listRoles(user.getRoleApplicationStatus()));
		System.out.println(user.getCertificateList());
		System.out.println(user.listRoleApplicationsWithCertificates());
		user.cancelRoleApplication(RoleEnum.PILOT);
		System.out.println(user.listRoleApplicationsWithCertificates());
	}
}
