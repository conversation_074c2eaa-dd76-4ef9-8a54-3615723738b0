package com.bees360.entity.dto;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import com.bees360.entity.HouseImageSegmentType;

public class HouseImageSegmentTypeDto implements Serializable {

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 5686580594262662880L;

	private HouseImageSegmentType node;

	private List<HouseImageSegmentTypeDto> subnode;

	public HouseImageSegmentTypeDto() {
		super();
	}

	public HouseImageSegmentTypeDto(HouseImageSegmentType node, List<HouseImageSegmentTypeDto> subnode) {
		super();
		this.node = node;
		this.subnode = subnode;
	}

	public HouseImageSegmentType getNode() {
		return node;
	}

	public void setNode(HouseImageSegmentType node) {
		this.node = node;
	}

	public List<HouseImageSegmentTypeDto> getSubnode() {
		return subnode;
	}

	public void setSubnode(List<HouseImageSegmentTypeDto> subnode) {
		this.subnode = subnode;
	}

}
