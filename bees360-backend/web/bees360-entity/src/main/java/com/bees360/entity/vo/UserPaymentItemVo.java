package com.bees360.entity.vo;

import com.bees360.entity.enums.ServiceFeeTypeEnum;

public class UserPaymentItemVo {

	private long paymentId;
	private long userId;
	private long projectId;

	private double totalFeeAmount;
	private double paidServiceFeeAmount;
	private double tax;

	// 操作方式 {@link PaymentOperationTypeEnum#code}
	private int operationType;

	// 支付方式 {@link PaymentMethodEnum#display}
	private String paymentMethod;
	// {@link ServiceFeeTypeEnum#code}
	private int serviceFeeType;
	// 支付方式 {@link PaymentMethodEnum#viewValue}
	private String serviceFeeTypeName;
	private String currency;

	private long paidTime;
	private String description;
	private String address;

	//** getter and setter **//
	public long getPaymentId() {
		return paymentId;
	}
	public void setPaymentId(long paymentId) {
		this.paymentId = paymentId;
	}
	public long getUserId() {
		return userId;
	}
	public void setUserId(long userId) {
		this.userId = userId;
	}
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public double getTotalFeeAmount() {
		return totalFeeAmount;
	}
	public void setTotalFeeAmount(double totalFeeAmount) {
		this.totalFeeAmount = totalFeeAmount;
	}
	public double getPaidServiceFeeAmount() {
		return paidServiceFeeAmount;
	}
	public void setPaidServiceFeeAmount(double paidServiceFeeAmount) {
		this.paidServiceFeeAmount = paidServiceFeeAmount;
	}
	public double getTax() {
		return tax;
	}
	public void setTax(double tax) {
		this.tax = tax;
	}
	public int getOperationType() {
		return operationType;
	}
	public void setOperationType(int operationType) {
		this.operationType = operationType;
	}
	public String getPaymentMethod() {
		return paymentMethod;
	}
	public void setPaymentMethod(String paymentMethod) {
		this.paymentMethod = paymentMethod;
	}
	public int getServiceFeeType() {
		return serviceFeeType;
	}
	public void setServiceFeeType(int serviceFeeType) {
		this.serviceFeeType = serviceFeeType;
		ServiceFeeTypeEnum serviceFeeTypeEnum = ServiceFeeTypeEnum.getEnum(serviceFeeType);
		setServiceFeeTypeName(serviceFeeTypeEnum == null? "": serviceFeeTypeEnum.getDisplay());
	}
	public String getServiceFeeTypeName() {
		return serviceFeeTypeName;
	}
	public void setServiceFeeTypeName(String serviceFeeTypeName) {
		this.serviceFeeTypeName = serviceFeeTypeName;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public long getPaidTime() {
		return paidTime;
	}
	public void setPaidTime(long paidTime) {
		this.paidTime = paidTime;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
}
