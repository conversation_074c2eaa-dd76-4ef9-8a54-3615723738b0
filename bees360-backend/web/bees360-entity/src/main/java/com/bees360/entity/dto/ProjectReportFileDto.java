package com.bees360.entity.dto;

import com.bees360.entity.ProjectReportFile;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;

/**
 * <AUTHOR>
 * @date 2020/01/07 11:16
 */
@Data
@NoArgsConstructor
public class ProjectReportFileDto {
    /**
     * @see ProjectReportFile#getReportType()
     */
    private int reportType;
    /**
     * 报告保存的在资源服务器中的key值
     *
     * @see ProjectReportFile#getReportPdfFileName()
     */
    @NotBlank
    private String reportUrl;
    /**
     * @see ProjectReportFile#getSize()
     */
    @Positive
    private int size;
    /**
     * @see ProjectReportFile#getReportPdfCompressed()
     */
    @NotNull
    private String reportCompressed = "";
    /**
     * @see ProjectReportFile#getSizeCompressed()
     */
    @PositiveOrZero
    private int sizeCompressed;
}
