package com.bees360.entity.dto;

import com.bees360.entity.enums.OrientationEnum;
import com.bees360.entity.enums.RoofPathTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/09/19 11:40
 */
public class ProjectFacetPath {
    private int facetId;
    private int componentId;
    private List<Point3D> path3D;
    private List<RoofPathTypeEnum> pathTypes;
    private List<Integer> sharedFacets;
    private List<Double> planeProp;
    private List<Double> planeCoef;

    private OrientationEnum orientation;
    private double pitch;
    private double area;

    public int getFacetId() {
        return facetId;
    }

    public void setFacetId(int facetId) {
        this.facetId = facetId;
    }

    public int getComponentId() {
        return componentId;
    }

    public void setComponentId(int componentId) {
        this.componentId = componentId;
    }

    public List<Point3D> getPath3D() {
        return path3D;
    }

    public void setPath3D(List<Point3D> path3D) {
        this.path3D = path3D;
    }

    public List<RoofPathTypeEnum> getPathTypes() {
        return pathTypes;
    }

    public void setPathTypes(List<RoofPathTypeEnum> pathTypes) {
        this.pathTypes = pathTypes;
    }

    public List<Integer> getSharedFacets() {
        return sharedFacets;
    }

    public void setSharedFacets(List<Integer> sharedFacets) {
        this.sharedFacets = sharedFacets;
    }

    public List<Double> getPlaneProp() {
        return planeProp;
    }

    public void setPlaneProp(List<Double> planeProp) {
        this.planeProp = planeProp;
    }

    public List<Double> getPlaneCoef() {
        return planeCoef;
    }

    public void setPlaneCoef(List<Double> planeCoef) {
        this.planeCoef = planeCoef;
    }

    public OrientationEnum getOrientation() {
        return orientation;
    }

    public void setOrientation(OrientationEnum orientation) {
        this.orientation = orientation;
    }

    public double getPitch() {
        return pitch;
    }

    public void setPitch(double pitch) {
        this.pitch = pitch;
    }

    public double getArea() {
        return area;
    }

    public void setArea(double area) {
        this.area = area;
    }

    @Override
    public String toString() {
        return "ProjectFacetPath{" + "facetId=" + facetId + ", componentId=" + componentId + ", path3D=" + path3D
            + ", pathTypes=" + pathTypes + ", sharedFacets=" + sharedFacets + ", planeProp=" + planeProp
            + ", planeCoef=" + planeCoef + ", orientation=" + orientation + ", pitch=" + pitch + ", area=" + area + '}';
    }
}
