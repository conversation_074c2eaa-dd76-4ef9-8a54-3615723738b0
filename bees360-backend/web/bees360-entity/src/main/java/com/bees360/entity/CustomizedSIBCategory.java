package com.bees360.entity;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

public class CustomizedSIBCategory {

	private long id;
	/**
	 * 0:root,1:subcategory,2:description
	 */
	private int categoryType;
	private String categoryTypeName;
	private String category;
	private List<String> childrenName;
	private long parentId;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public int getCategoryType() {
		return categoryType;
	}

	public void setCategoryType(int categoryType) {
		this.categoryType = categoryType;
	}

	public long getParentId() {
		return parentId;
	}

	public void setParentId(long parentId) {
		this.parentId = parentId;
	}

	@JsonInclude(value=Include.NON_NULL)
	public String getCategoryTypeName() {
		return categoryTypeName;
	}

	public void setCategoryTypeName(String categoryTypeName) {
		this.categoryTypeName = categoryTypeName;
	}

	@JsonInclude(value=Include.NON_NULL)
	public List<String> getChildrenName() {
		return childrenName;
	}

	public void setChildrenName(List<String> childrenName) {
		this.childrenName = childrenName;
	}

}
