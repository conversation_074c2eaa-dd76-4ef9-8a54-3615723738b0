package com.bees360.entity;

import com.bees360.entity.enums.ImagePartialViewTypeEnum;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
public class HouseImageUnderwriting implements Serializable {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 9125579225881492075L;

	private long id;

	private String tips;

	private String description;

	private int version;

	private int underwritingType;

    private String hintImage;

    private Integer orientation;

    // it must be assigned a value through the Enum ImagePartialViewTypeEnum.
    private int partialType = ImagePartialViewTypeEnum.OTHERS.getCode();

	public HouseImageUnderwriting() {
		super();
	}

	public HouseImageUnderwriting(long id, String tips, String description, int underwritingType) {
		this.id = id;
		this.tips = tips;
		this.description = description;
		this.underwritingType = underwritingType;
	}

}
