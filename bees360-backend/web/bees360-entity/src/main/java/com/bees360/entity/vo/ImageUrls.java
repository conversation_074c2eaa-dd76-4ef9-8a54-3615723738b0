package com.bees360.entity.vo;

import com.bees360.entity.ProjectImage;

/**
 * <AUTHOR>
 * @date 2019/09/11 17:15
 */
public class ImageUrls {
    private String imageId;
    // 原图连接
    private String fileName;
    // 中等size图片链接
    private String fileNameMiddleResolution;
    // 最小size图片链接
    private String fileNameLowerResolution;
    // 做了标记的图片的链接
    private String annotationImage;

    public ImageUrls() {

    }

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileNameMiddleResolution() {
        return fileNameMiddleResolution;
    }

    public void setFileNameMiddleResolution(String fileNameMiddleResolution) {
        this.fileNameMiddleResolution = fileNameMiddleResolution;
    }

    public String getFileNameLowerResolution() {
        return fileNameLowerResolution;
    }

    public void setFileNameLowerResolution(String fileNameLowerResolution) {
        this.fileNameLowerResolution = fileNameLowerResolution;
    }

    public String getAnnotationImage() {
        return annotationImage;
    }

    public void setAnnotationImage(String annotationImage) {
        this.annotationImage = annotationImage;
    }

    public static ImageUrls fromProjectImage(ProjectImage image) {
        ImageUrls imageUrls = new ImageUrls();

        imageUrls.setImageId(image.getImageId());
        imageUrls.setFileName(image.getFileName());
        imageUrls.setFileNameMiddleResolution(image.getFileNameMiddleResolution());
        imageUrls.setFileNameLowerResolution(image.getFileNameLowerResolution());
        imageUrls.setAnnotationImage(image.getAnnotationImage());

        return imageUrls;
    }

    public String getImageS3Key(){
        return fileName;
    }

    public String getMiddleResolutionImageS3Key(){
        return fileNameMiddleResolution;
    }

    public String getLowerResolutionImageS3Key(){
        return fileNameLowerResolution;
    }

    public String getAnnotationImageS3Key(){
        return annotationImage;
    }

    @Override
    public String toString() {
        return "ImageUrls{" + "imageId=" + imageId + ", fileName='" + fileName + '\'' + ", fileNameMiddleResolution='"
            + fileNameMiddleResolution + '\'' + ", fileNameLowerResolution='" + fileNameLowerResolution + '\''
            + ", annotationImage='" + annotationImage + '\'' + '}';
    }
}
