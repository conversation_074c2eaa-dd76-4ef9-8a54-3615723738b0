package com.bees360.entity.vo;

import com.bees360.entity.enums.ProjectStatusEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

public class HistoryLogVo {
    private long projectId;
    private long userId;
	private int status;
	private String modifiedBy;
	private String user;
	private long statusTime;
	private long createdTime;

    private String description;

    private String title;

    // Getter and Setter

    public String getStatus() {
        return getDescription();
    }
    public void setStatus(int status) {
        this.status = status;
    }

    public String getModifiedBy() {
		return modifiedBy;
	}
	public void setModifiedBy(String modifiedBy) {
		this.modifiedBy = modifiedBy;
	}
	public String getUser() {
		return user;
	}
	public void setUser(String user) {
		this.user = user;
	}
	public long getStatusTime() {
		return statusTime;
	}
	public void setStatusTime(long statusTime) {
		this.statusTime = statusTime;
	}
	public long getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}
    public long getUserId() {
        return userId;
    }
    public void setUserId(long userId) {
        this.userId = userId;
    }
    public long getProjectId() {
        return projectId;
    }
    public void setProjectId(long projectId) {
        this.projectId = projectId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        String statusDisplay = Optional.ofNullable(ProjectStatusEnum.getEnum(status))
            .map(ProjectStatusEnum::getDisplay).orElse("");
        String desc = statusDisplay;
        if (StringUtils.isNotBlank(title)) {
            desc = desc + ";" +  title;
        }
        if (StringUtils.isNotBlank(description) && !StringUtils.equals(statusDisplay, description)) {
            desc = desc + ";" +  description;
        }
        return desc;
    }

    public void setDescription(String description) {
        this.description = description;
    }

}
