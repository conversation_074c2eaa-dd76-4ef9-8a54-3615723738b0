package com.bees360.entity.enums;

public enum ReportMaterialTypeEnum implements BaseCodeEnum {
	STRING_NORMAL(1, "The normal string"),
	STRING_MEASUREMENT_TITLE(2, "Damage title String data"),
	STRING_MEASUREMENT_PICTURE(3, "Measurement picture String data"),
	STRING_MEASUREMENT_TABLE(4, "Measurement table data"),
	MATERIAL_NORMAL(10, "Material"),
	MATERIAL_MEASUREMENT_LENGTH(11, "Measurement length chart"),
	MATERIAL_MEASUREMENT_AREA_PITCH(12, "Measurementarea chart"),
	MATERIAL_MEASUREMENT_DIRECTION_IMAGE(13, "Measurement direction image"),
	IMAGE_NORMAL(20, "Image"),
	IMAGE_DAMAGE_FRONT_ELEVATION(21, "Damage Rport one image"),
	IMAGE_DAMAGE_OVERVIEW(22, "Damage Report two image"),
	IMAGE_DAMAGE_CLOSEUP(23, "Damage Report close up image"),
	IMAGE_DAMAGE_CROP(24, "Damage Report crop image"),
	LINE_NORMAL(30, "Line"),
	POINT_NORMAL(40, "Point"),
	BACKGROUND_NORMAL(50, "Background square"),
	CALLING_METHOD(60, "Calling method");

	private int code;

	private String display;

	ReportMaterialTypeEnum(int code, String display) {
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return this.code;
	}

	@Override
	public String getDisplay() {
		return this.display;
	}

	public static ReportMaterialTypeEnum getByCode(int code){
		ReportMaterialTypeEnum[] materialTypes = ReportMaterialTypeEnum.values();
		for(ReportMaterialTypeEnum materialType: materialTypes){
			if(materialType.getCode() == code){
				return materialType;
			}
		}
		return null;
	}
}
