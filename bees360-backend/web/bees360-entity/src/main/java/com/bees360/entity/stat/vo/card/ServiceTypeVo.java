package com.bees360.entity.stat.vo.card;

import lombok.Data;

@Data
public class ServiceTypeVo {

    private Integer serviceType;
    private String display;
    private Integer count;
    private Double score;

    public ServiceTypeVo(Integer serviceType, String display, Integer count){
        this.serviceType = serviceType;
        this.display = display;
        this.count = count;
    }

    public ServiceTypeVo(Integer serviceType, String display, Double score){
        this.serviceType = serviceType;
        this.display = display;
        this.score = score;
    }

    public ServiceTypeVo(Integer serviceType, String display, Integer count, Double score){
        this.serviceType = serviceType;
        this.display = display;
        this.count = count;
        this.score = score;
    }
}
