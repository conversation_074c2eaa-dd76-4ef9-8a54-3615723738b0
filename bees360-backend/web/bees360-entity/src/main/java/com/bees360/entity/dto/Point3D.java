package com.bees360.entity.dto;

/**
 * Point class tools
 *
 * <AUTHOR>
 *
 */
public class Point3D extends Point {

	private double z;

	private int componentId;

	public Point3D() {
		super();
	}

	public Point3D(double x, double y) {
		super(x, y);
	}

	public Point3D(double x, double y, double z) {
		super(x, y);
		this.z = z;
	}

	public Point3D(double x, double y, double z, int componentId) {
		super(x, y);
		this.z = z;
		this.componentId = componentId;
	}

	public double getZ() {
		return z;
	}

	public void setZ(double z) {
		this.z = z;
	}

	public int getComponentId() {
		return componentId;
	}

	public void setComponentId(int componentId) {
		this.componentId = componentId;
	}

	@Override
	public double[] values(){
		return new double[]{x, y, z};
	}

	@Override
	public double[] toArray() {
		return values();
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		long temp;
		temp = Double.doubleToLongBits(x);
		result = prime * result + (int) (temp ^ (temp >>> 32));
		temp = Double.doubleToLongBits(y);
		result = prime * result + (int) (temp ^ (temp >>> 32));
		temp = Double.doubleToLongBits(z);
		result = prime * result + (int) (temp ^ (temp >>> 32));
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Point3D other = (Point3D) obj;
		if (Double.doubleToLongBits(x) != Double.doubleToLongBits(other.x))
			return false;
		if (Double.doubleToLongBits(y) != Double.doubleToLongBits(other.y))
			return false;
		if (Double.doubleToLongBits(z) != Double.doubleToLongBits(other.z))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "Point3D [z=" + z + ", componentId=" + componentId + ", x=" + x + ", y=" + y + "]";
	}

	@Override
	public Point3D clone() {
		return (Point3D) super.clone();
	}
}
