package com.bees360.entity.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ProjectTypeEnum implements BaseCodeEnum{
    Residential_Single_Family(0, "Residential Single Family", "Residential-Single Family"),
    Residential_Condo(1, "Residential Condo", "Residential-Condo"),
    RESIDENTIAL_TOWNHOUSE(2, "Residential Townhouse", "Residential-Townhouse"),
    RESIDENTIAL_MULTI_FAMILY(3, "Residential Multi Family", "Residential-Multi Family"),
    RESIDENTIAL_APARTMENTS(4, "Residential Apartments", "Residential-Apartments"),
    COMMERCIAL(5, "Commercial", "Commercial"),
    SINGLE_FAMILY_DETACHED(6, "Single Family Detached", "Single Family Detached"),
    SINGLE_FAMILY_ATTACHED(7, "Single Family Attached", "Single Family Attached"),
    DUPLEX(8, "Duplex", "Duplex"),
    TRIPLE(9, "Triplex", "Triplex"),
    ROW_HOME(10, "Row Home", "Row Home"),
    TWO_FAMILY_HOME(11, "2 Family Home", "2-Family Home"),
    THREE_FAMILY_HOME(12, "3 Family Home", "3-Family Home"),
    FOUR_FAMILY_HOME(13, "4 Family Home", "4-Family Home"),
    MOBILE_HOME(14, "Mobile Home", "Mobile Home"),
    CONDOMINIUM_DETACHED(15, "Condominium Detached", "Condominium Detached"),
    OTHER(16, "Other", "Other"),
    COMMERCIAL_APARTMENT(17, "Commercial Apartment", "Commercial-Apartment"),
    HABITATIONAL(18, "Habitational", "Habitational"),
    RESTAURANT(19, "Restaurant", "Restaurant"),
    GARAGE(20, "Garage", "Garage"),
    GAS_STATION(21, "Gas Station", "Gas Station"),
    GENERAL_COMMERCIAL(22, "General Commercial", "General Commercial")
	;
	private final int code;
	private final String value;
	private final String display;

	ProjectTypeEnum(int code, String value, String display){
		this.code = code;
		this.value = value;
		this.display = display;
	}

	public static ProjectTypeEnum getEnum(Integer code){
		if(code == null) {
			return null;
		}
		ProjectTypeEnum[] types = ProjectTypeEnum.values();
		for(ProjectTypeEnum type: types){
			if(type.getCode() == code){
				return type;
			}
		}
		return null;
	}
	public static boolean exist(Integer code) {
		return code != null && getEnum(code) != null;
	}

	public static ProjectTypeEnum getEnumByValue(String value) {
		for(ProjectTypeEnum status: values()) {
			if(status.getValue().equals(value)) {
				return status;
			}
		}
		return null;
	}

	public static boolean exist(String value) {
		return getEnumByValue(value) != null;
	}

	@Override
	public String getDisplay(){
		return display;
	}

	@Override
	@JsonValue
	public int getCode(){
		return code;
	}

	public String getValue() {
		return value;
	}

	public static void main(String[] args) {
		show();
	}

	private static void show() {
		System.out.println("|name|code|");
		System.out.println("|----|----|");
		for(ProjectTypeEnum type: ProjectTypeEnum.values()) {
			System.out.println("|" + type.getDisplay() + "|" + type.getCode() + "|");
		}
	}
}
