package com.bees360.entity.openapi;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/25 09:20
 */
@Data
public class OpenReportVo {
    private long id;
    /**
     * 报告文件名
     */
    @NotBlank
    private String reportName;

    @Data
    public static class OpenReportListWrapperVo<R> {
        @NotNull
        private List<R> report;

        public OpenReportListWrapperVo(List<R> report) {
            this.report = report;
        }

        public OpenReportListWrapperVo(R... report) {
            this.report = new ArrayList<>(Arrays.asList(report));
        }
    }

    @Data
    public static class OpenReportUrlVo {
        @NotBlank
        private String url;
    }
}
