package com.bees360.entity.vo;


public class ProjectImageTinyVoForImage {

	private String imageId;
	private String imageUrl;
	private String annotationImage;
	private String annotationTypes;
	// The sources of this file: 0: drone image 1: cell-phone image …
	// it must be assigned a value through the Enum CELL_PHONE_IMAGE.
	private int fileSourceType;
	// it must be assigned a value through the Enum ImageTypeEnum.
	private int imageType;
	private String imageCategory;
	// The local filename when this file is uploaded from some local device.
	private String originalFileName;

	private String parentId;

	public String getImageId() {
		return imageId;
	}
	public void setImageId(String imageId) {
		this.imageId = imageId;
	}
	public String getImageUrl() {
		return imageUrl;
	}
	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}
	public String getAnnotationImage() {
		return annotationImage;
	}
	public void setAnnotationImage(String annotationImage) {
		this.annotationImage = annotationImage;
	}
	public String getAnnotationTypes() {
		return annotationTypes;
	}
	public void setAnnotationTypes(String annotationTypes) {
		this.annotationTypes = annotationTypes;
	}
	public int getFileSourceType() {
		return fileSourceType;
	}
	public void setFileSourceType(int fileSourceType) {
		this.fileSourceType = fileSourceType;
	}
	public int getImageType() {
		return imageType;
	}
	public void setImageType(int imageType) {
		this.imageType = imageType;
	}
	public String getImageCategory() {
		return imageCategory;
	}
	public void setImageCategory(String imageCategory) {
		this.imageCategory = imageCategory;
	}
	public String getOriginalFileName() {
		return originalFileName;
	}
	public void setOriginalFileName(String originalFileName) {
		this.originalFileName = originalFileName;
	}
	public String getParentId() {
		return parentId;
	}
	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	@Override
	public String toString() {
		return "ProjectImageTinyVoForImage [imageId=" + imageId + ", imageUrl=" + imageUrl + ", annotationImage="
				+ annotationImage + ", annotationTypes=" + annotationTypes + ", fileSourceType=" + fileSourceType
				+ ", imageType=" + imageType + ", imageCategory=" + imageCategory + ", originalFileName="
				+ originalFileName + ", parentId=" + parentId + "]";
	}

}
