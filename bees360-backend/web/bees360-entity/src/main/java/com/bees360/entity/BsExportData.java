package com.bees360.entity;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * 导出数据存储，根据类型和Id查找对应的data
 * <AUTHOR>
 * @date 2020/05/19 15:08
 */
@Data
public class BsExportData implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private long id;

    /**
     * type 对应存储的id值， eg: projectId
     */
    private String relatedId;

    /**
     *  数据所属类型
     */
    private String relatedType;

    /**
     *  json数据
     */
    private String dataLog;

}
