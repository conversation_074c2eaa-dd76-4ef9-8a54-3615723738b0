package com.bees360.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CompanyInvoice {

    @ExcelProperty(value = "Project ID")
    private long projectId;

    @ExcelProperty(value = "Insured By")
    private String insuredBy;

    @ExcelProperty(value = "Bees Invoice #")
    private String beesInvoice;

    @ExcelProperty(value = "Claim/inspection #")
    private String inspectionNumber;

    @ExcelProperty(value = "Service Type")
    private String serviceType;

    @ExcelProperty(value = "External Adjuster")
    private String externalAdjuster;

    @ExcelProperty(value = "CAT #")
    private String catNumber;

    @ExcelProperty(value = "Canceled(Y/N)", converter = BooleanConverter.class)
    private boolean cancelled;

    @ExcelProperty(value = "PLNAR order", converter = BooleanConverter.class)
    private boolean hasPLNAR_order;

    @ExcelProperty(value = "# Room")
    private Integer room;

    @ExcelProperty(value = "$ Charge")
    private Double charge;

    @ExcelProperty(value = "Benchmark Order", converter = BooleanConverter.class)
    private boolean hasBenchmarkOrder;

    @ExcelProperty(value = "PCA Original Claim Canceled", converter = BooleanConverter.class)
    private Boolean PCAOriginalClaimCanceled;

    @ExcelProperty(value = "Client Received Time")
    private String clientReceived;

    public static class BooleanConverter implements Converter<Boolean> {
        @Override
        public Class<Boolean> supportJavaTypeKey() {
            return Boolean.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public Boolean convertToJavaData(
                ReadCellData cellData,
                ExcelContentProperty excelContentProperty,
                GlobalConfiguration globalConfiguration)
                throws Exception {
            return "Y".equals(cellData.getStringValue());
        }

        @Override
        public WriteCellData<String> convertToExcelData(
                Boolean aBoolean,
                ExcelContentProperty excelContentProperty,
                GlobalConfiguration globalConfiguration)
                throws Exception {
            if (aBoolean == null) {
                return new WriteCellData<>("--");
            }
            return new WriteCellData<>(aBoolean ? "Y" : "N");
        }
    }
}
