package com.bees360.entity.vo;
import java.io.Serial;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;

/**
 * ProjectFacetTypeVo
 * <AUTHOR>
 *
 */
@Builder
public class ProjectFacetTypeVo implements Serializable {
    /**
     *
     */
    @Serial
    private static final long serialVersionUID = -5700852716436136800L;

	//The identifier of the facet.
	private int facetId;

	private int componentId;

	private String thdPath;

	private String pathType;

    private double area;

    private double pitch;

    private String planeProp;

    private boolean isHighRoof;

	public int getFacetId() {
		return facetId;
	}

	public void setFacetId(int facetId) {
		this.facetId = facetId;
	}

    public double getArea() {
        return area;
    }

    public void setArea(double area) {
        this.area = area;
    }

    public double getPitch() {
        return pitch;
    }

    public void setPitch(double pitch) {
        this.pitch = pitch;
    }

    public String getPlaneProp() {
        return planeProp;
    }

    public void setPlaneProp(String planeProp) {
        this.planeProp = planeProp;
    }

    public boolean getIsHighRoof() {
        return isHighRoof;
    }

    public void setIsHighRoof(boolean highRoof) {
        isHighRoof = highRoof;
    }

    public double[][] getThdPath() {
		String[] thdPathStrs = thdPath.substring(2, thdPath.length() - 2).split("\\],\\[");
		double[][] points = new double[thdPathStrs.length][3];
		for (int i = 0; i < thdPathStrs.length; i++) {
			String[] pointStrs = thdPathStrs[i].split(",");
			for (int j = 0; j < pointStrs.length; j++) {
				points[i][j] = Double.parseDouble(pointStrs[j]);
			}
		}
		return points;
	}

	public void setThdPath(String thdPath) {
		this.thdPath = thdPath;
	}

	public int[] getPathType() {
		String[] typeStrs = pathType.substring(1, pathType.length() - 1).split(",");
		int[] types = new int[typeStrs.length];
		for (int i = 0; i < typeStrs.length; i++) {
			types[i] = (int)Double.parseDouble(typeStrs[i]);
		}
		return types;
	}

	public void setPathType(String pathType) {
		this.pathType = pathType;
	}

	@JsonIgnore
	public int getComponentId() {
		return componentId;
	}

	public void setComponentId(int componentId) {
		this.componentId = componentId;
	}

	@Override
	public String toString() {
		return "ProjectFacetTypeVo [facetId=" + facetId + ", thdPath=" + thdPath + ", pathType=" + pathType + "]";
	}

}
