package com.bees360.entity.enums;

import java.util.HashMap;
import java.util.Map;

public enum ProcessStatusEnum implements BaseCodeEnum{
	DELETED(0, "Deleted", false, null),
	NEW(1, "New Project", true, NewProjectStatusEnum.PROJECT_CREATED),
	IMAGE_UPLOADED(2, "Image Uploaded", true, NewProjectStatusEnum.SITE_INSPECTED),
	IN_PROCESS(3, "In Process", true, NewProjectStatusEnum.SITE_INSPECTED),
	UNDER_REVIEW(4, "Under Review", true, NewProjectStatusEnum.SITE_INSPECTED),
	REPORT_APPROVED(5, "Report Approved", true, NewProjectStatusEnum.RETURNED_TO_CLIENT),

    //  add for cancel & recover
    CANCELED(6, "Project Canceled",false, NewProjectStatusEnum.PROJECT_CANCELED),

    CLIENT_RECEIVED(7, "Client Received", true, NewProjectStatusEnum.CLIENT_RECEIVED)
	;

	private final int code;
	private final String display;
	private final boolean visible;
	private final NewProjectStatusEnum projectStatus;

	ProcessStatusEnum(int code, String display, boolean visible, NewProjectStatusEnum projectStatus) {
		this.code = code;
		this.display = display;
		this.visible = visible;
		this.projectStatus = projectStatus;
	}

	private static final Map<Integer, ProcessStatusEnum> statusMap
		= new HashMap<Integer, ProcessStatusEnum>();

	static {
		for(ProcessStatusEnum status: ProcessStatusEnum.values()) {
			statusMap.put(status.getCode(), status);
		}
	}

	@Override
	public String getDisplay() {
		return display;
	}
	@Override
	public int getCode() {
		return code;
	}
	public boolean isVisible() {
		return visible;
	}

	public NewProjectStatusEnum getProjectStatus() {
		return projectStatus;
	}

	public static ProcessStatusEnum getEnum(int code) {
		return statusMap.get(code);
	}

	public static void main(String[] args) {
		System.out.println("|name|code|");
		System.out.println("|----|----|");
		for(ProcessStatusEnum status: ProcessStatusEnum.values()) {
			System.out.println("|" + status.getDisplay() + "|" + status.getCode() + "|");
		}
	}
}
