package com.bees360.entity.firebase;

import com.google.cloud.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * faa 证书的上传和审核记录
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FirebaseFAA {

    /**
     * 证书在S3的地址
     */
    private String license;

    /**
     * 证书预览在S3的地址
     */
    private String licensePreview;

    /**
     * 证书编号
     */
    private String licenseNumber;

    /**
     * 证书签发时间
     */
    private Timestamp issuanceTime;

    /**
     * 截止时间
     */
    private Timestamp expireTime;

    /**
     * 用户最后一次修改FAA时间
     */
    private Timestamp lastModifyTime;

    /**
     * 管理员最后一次的留言
     */
    private String message;

    /**
     * 最后一次审核的管理员ID
     */
    private String managerId;

    /**
     * 管理员审核完成时间
     */
    private Timestamp completeTime;

    /**
     * 审核状态
     * Pending, Submitted，Rejected, Approved
     */
    private String status;

}
