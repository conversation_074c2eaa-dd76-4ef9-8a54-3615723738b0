package com.bees360.entity.stat.search;

import lombok.Data;

import java.util.Arrays;
import java.util.Objects;

@Data
public class PagedSearchOption {

    public static final int DEFAULT_PAGE_SIZE = 24;

    private int pageIndex = 1;
    private int pageSize;
    private String sortKey;
    private String sortOrder;


    public int getPageSize(){
        if (this.pageSize == 0){
            return DEFAULT_PAGE_SIZE;
        }
        return pageSize;
    }

    public int getStartIndex(){
        return (pageIndex - 1) * getPageSize();
    }

    public void setSortOrder(String sortOrder){
        this.sortOrder = SortOrder.getOrder(sortOrder);
    }

    public String getSortOrder(){
        return this.sortOrder;
    }

    public static enum SortOrder{
        DESC, ASC;

        public static String getOrder(String sortOrder){
            return Arrays.stream(SortOrder.values())
                .map(Enum::name)
                .filter(so ->Objects.equals(so, sortOrder) || Objects.equals(so.toLowerCase(), sortOrder))
                .map(String::toLowerCase)
                .findFirst()
                .orElse(null);
        }
    }
}
