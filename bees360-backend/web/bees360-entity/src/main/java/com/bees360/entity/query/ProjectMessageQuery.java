package com.bees360.entity.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * project留言消息查询类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectMessageQuery {
    private Long projectId;

    private List<Long> projectIds;

    private Boolean isDeleted;

    private Long senderId;

    private Integer type;

    public static ProjectMessageQuery getDefaultQuery() {
        ProjectMessageQuery query = new ProjectMessageQuery();
        query.setIsDeleted(false);
        return query;
    }
}
