package com.bees360.entity;

import com.bees360.commons.springsupport.validation.Year;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.PayStatusEnum;
import com.bees360.entity.enums.ProcessStatusEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ProjectTypeEnum;
import com.bees360.flyzone.FlyZoneType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Project implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

	private long projectId;
	private String missionId;

	private String policyNumber;

    // 应使用inspectionNumber
    @Deprecated
	private String claimNumber;
    /**
     * 来自symbility中的Claim Information的CAT number
     */
	private String catNumber = "";
    private Integer catLevel;
	/**
	 * use Double type instead of double type for claimRcv and claimAcv can be null
	 *	before they were inputed.
	 */
	private Double claimRcv;
	private Double claimAcv;
	/**
	 * it must be assigned a value through the Enum {@link ClaimTypeEnum#getCode()}.
	 */
	private Integer claimType;
	/**
	 * The time this record is created in the database.
	 */
	private long createdTime;
	/**
	 * The time that this damage happens to the project entity.
	 */
	private Long damageEventTime;
	/**
	 * The creator of this project. It can be the owner, pilot, adjuster, etc.
	 */
	private long createdBy;
	/**
	 * it must be assigned a value through the Enum {@link ProjectTypeEnum#getCode()}.
	 */
	private Integer projectType;
	/**
	 * The address including the street and room/building number.
	 * It does not include city, state, zipCode and country.
	 */
	private String address;
	private String city;
	private String state;
	private String country;
	private String zipCode;
	private double gpsLocationLongitude;
	private double gpsLocationLatitude;
	private String addressId;
	/**
	 * It references the id in the User. If the Project is not added by the asset owner, then this asset owner
	 * is added automatically with inactive state, so that email/short_message can be sent accordingly.
	 */
	private String assetOwnerName;
    private String insuredFirstName;
    private String insuredMiddleName;
    private String insuredLastName;
	private String assetOwnerPhone;
	private String assetOwnerEmail;
	private String inspectionNumber;
	private Long dueDate;
	/**
	 * 客户公司
	 */
	private String customer;
	/**
	 * 一段检测类型的描述，如：Dwelling with Drone roof
	 */
	private String inspectionType;
	private String agent;
	private String agentEmail;
	private String agentContactName;
	private String agentPhone;
	private String guideline;
	private String insuredHomePhone;
	private String insuredWorkPhone;

	/**
	 * The insurance company who provides the insurance for the project entity.
	 */
	private Long insuranceCompany;
	/**
	 * 该项目属于该公司管理。该字段不再表示repair_company，表示为processed_by
 	 */
	private Long repairCompany;
	/**
	 * The company who provides the materials.
	 */
	private Long materialProviderCompany;
	/**
	 * 默认为 created_by所在的公司，不做项目归属权标志，项目归属权见：repair_company
	 */
	private Long companyId;
	private String description;
    /**
     * Loss Description
     * 一段损失情况的描述，如：Loss Description: Very Highwinds removed lots of shingles off my
     * horse stalls and I believe some tabs off the roof on the house
     */
    private String claimNote;

	private String north;

	private String plnarURL;
	private Integer plnarStatus;

	private Long hoverJobId;
	/**
	 * @see com.bees360.entity.firebase.HoverJobStatusEnum
	 */
	private Integer hoverJobStatus;

	/**
	 * related to com.bees360.entity.enums.ProcessStatus
	 */
	private int latestStatus;

	/**
	 * @see NewProjectStatusEnum#getCode()
     * @see Project#statusUpdateTime
	 */
	private int projectStatus;

	/**
	 * inspectionType for project 1:High Altitude,2:Lower Altitude
	 * 按照位图法进行记录，也就是High Altitude: 010, Lower Altitude: 100
	 * 一个项目可以同时为多种类型
	 */
	private Long inspectionTypes;

	private Long inspectionTime;
	private Integer damageSeverity;

	private boolean isBooking;
	private String contacterName;
	private String contacterEmail;
	private String contacterPhone;
	private Integer roofEstimatedAreaItem;
	private Integer reportServiceOption;

	private boolean needPilot;

	private int chimney;

	/**
	 * 飞行区域类型/禁飞区类型
	 */
	private int flyZoneType;

	/**
	 * 项目上传的zip s3URL  add by xjk
	 */
	private String imagesArchiveUrl;

    /**
     * 图片上传的状态:
     * @see com.bees360.entity.enums.ImageUploadStatusEnum
     */
	private int imageUploadStatus;

	private double rotationDegree;

	/**
	 * project status 最近修改时间
     * @see Project#projectStatus
	 */
	private Long statusUpdateTime;

    /**
     * project service type 订阅服务类型
     * @see com.bees360.entity.enums.ProjectServiceTypeEnum
     */
    private Integer serviceType;

    private int payStatus;

	// ======================================
	// 辅助字段
	/**
	 * Project 自定义字段，改部分数据不会保存到Project表中，而是保存到ProjectCustomizedInfo中。
	 *
	 * @see ProjectCustomizedInfo
	 */
	private Map<String, Object> customizedParams;
	/**
	 * 创建项目时指定订购的报告
	 */
	private List<Integer> reportTypes;

    private String specialInstructions;
    private String specialInstructionComments;
    private LocalDate policyEffectiveDate;
    @Year
    private String yearBuilt;

    private String serviceTypeReason;

    private boolean gpsIsApproximate;
    /**
     * 该项目 adjuster 的姓名， firstName + lastName, eg: john smith
     */
    private String adjustedBy;

    /**
     * 户主的登录iBees的验证码
     */
    private String inspectionCode;

    /**
     * 自定义的链接，户主可以访问接链接跳转到iBees app中
     */
    private String inspectionLink;

    /**
     * inspectionCode的过期时间
     */
    private long expirationTime;

    /**
     * Field that distinguish from a normal case and a test case
     */
    private boolean testFlag;

    private String xactanalysisId;

	/**
	 * 需要签名报告中，未获取到签名的原因
	 */
	private String unsignedReason;

    /**
     * 联系户主的时间
     */
    private Instant customerContactedAt;
    /**
     * 首次尝试联系户主的时间
     */
    private Instant initialCustomerContactTime;

    public Project(long projectId) {
        super();
        this.projectId = projectId;
    }

    private String insuredBy;

    /**
     * The operating company who processes this case.
     * These companies are owned by American Family.
     */
    private String operatingCompany;

    private Integer livingArea;

    private Double dwellingCoverage;

    private Double otherStructureCoverage;

    private Double contentCoverage;

    private Boolean isRenewal;

    private List<String> supplementalServices;

    private String projectDivision = "";

    private String projectDivisionName = "";

    private String cloneFrom;

    private Double carrierProvidedLivingArea;

	@JsonIgnore
	public String getFullAddress() {
		return address + ", " + city + ", " + state + " " + zipCode;
	}
    @JsonIgnore
    public boolean isDeleted() {
        return latestStatus == ProcessStatusEnum.DELETED.getCode();
    }

	/**
	 * gpsLocationLongitude 缩略名
	 */
	public void setLng(double lng) {
		this.gpsLocationLongitude = lng;
	}
	public double getLng() {
		return this.gpsLocationLongitude;
	}

	/**
	 * gpsLocationLatitude 缩略名
	 */
	public void setLat(double lat) {
		this.gpsLocationLatitude = lat;
	}
	public double getLat() {
		return this.gpsLocationLatitude;
	}

	public void setLatestStatus(int latestStatus) {
		this.latestStatus = latestStatus;
	}
	public void setLatestStatus(ProcessStatusEnum latestStatus) {
		this.latestStatus = latestStatus.getCode();
	}
	@JsonProperty("isBooking")
	public boolean isBooking() {
		return isBooking;
	}
	public void setBooking(boolean isBooking) {
		this.isBooking = isBooking;
	}

	public void setCustomParams(Map<String, Object> customizedParams) {
		this.customizedParams = customizedParams;
	}

    public String getServiceTypeName() {
        return Optional.ofNullable(ProjectServiceTypeEnum.getEnum(serviceType)).map(ProjectServiceTypeEnum::getDisplay).orElse(null);
    }

    public String getServiceTypeValue(){
	    return Optional.ofNullable(ProjectServiceTypeEnum.getEnum(serviceType)).map(ProjectServiceTypeEnum::getValue).orElse(null);
    }

    public String getFlyZoneTypeName() {
        return Optional.ofNullable(FlyZoneType.getEnum(flyZoneType)).map(FlyZoneType::getDisplay).orElse(null);
    }

    public enum PlnarStatus{
	    DEFAULT(0, "default value"),
        SUBSCRIBED(1, "subscribed");

        private int status;
        private String desc;

        public int getStatus(){
            return this.status;
        }

        PlnarStatus(int status, String desc){
            this.status = status;
            this.desc = desc;
        }
    }

    public boolean isPaid(){
	    return Objects.equals(payStatus, PayStatusEnum.PAID.getCode());
    }

}
