package com.bees360.entity.enums;

import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/07/25 16:47
 */
public enum ProjectPlnarStatusEnum {

    NONE(0, "None"),
    LINK_RECEIVED(1, "Link Received"),
    PROCESSING(2, "Processing"),
    UPLOADED(3, "Uploaded To Symbility")
    ;

    private final Integer code;
    private final String display;

    ProjectPlnarStatusEnum(Integer code, String display) {
        this.code = code;
        this.display = display;
    }

    public static ProjectPlnarStatusEnum getEnum(Integer code){
        return Stream.of(ProjectPlnarStatusEnum.values())
            .filter(o -> Objects.equals(code, o.getCode()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("ProjectPlnarStatusEnum is null, value:" + code));
    }


    public Integer getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }
}
