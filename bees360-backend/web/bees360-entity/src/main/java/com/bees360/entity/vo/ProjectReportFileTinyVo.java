package com.bees360.entity.vo;

import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/** @see com.bees360.internal.ai.entity.vo.ProjectReportFileTinyVo */
public class ProjectReportFileTinyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1493219163431833271L;

	//Primary key
	private String reportId;
	/**
	 * it must be assigned a value through the Enum <code>getReportTypeEnum</code>.
	 */
	private int reportTypeCode;
	// Report link
	private String reportUrl;

    private String reportPdfCompressed;

	// The size of the file
	private int size;

	private int sizeCompressed;

    private String s3Key;

    /**
	 * it must be assigned a value through the Enum <code>ReportGenerationStatusEnum</code>.
	 */
	private int generationStatus;

	private ReportTypeEnum reportTypeEnum;
    /**
     * Whether report has been read, true:read, false:not read
     */
    private boolean isRead = true;
	private boolean isPaid;
	private boolean isReadable;
    private long createdTime;

    private String fileType;

    public ProjectReportFileTinyVo() {
		super();
	}

    public long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(long createdTime) {
        this.createdTime = createdTime;
    }

    @JsonIgnore
	public boolean isCompleted() {
		return ReportGenerationStatusEnum.getEnum(this.generationStatus).isCompleted();
	}

	public Boolean getNeedReview() {
		if(reportTypeEnum != null) {
			return reportTypeEnum.needApproved();
		}
		return null;
	}

	@JsonProperty("isFree")
	public Boolean isFree() {
		if(reportTypeEnum != null) {
			return reportTypeEnum.isFree();
		}
		return null;
	}
	//* getter and setter *//
	public String getReportId() {
		return reportId;
	}
	public void setReportId(String reportId) {
		this.reportId = reportId;
	}
	public int getReportType() {
		return reportTypeCode;
	}
	public void setReportType(int reportType) {
		this.reportTypeCode = reportType;
		this.reportTypeEnum = ReportTypeEnum.getEnum(reportType);
	}

    public String getS3Key() {
        return s3Key;
    }

    public void setS3Key(String s3Key) {
        this.s3Key = s3Key;
    }

    /**
     * 用于controller接口返回
     */
    public String getReport() {
        return reportUrl;
    }
    /**
     * 用于controller接口返回
     */
    public String getReportCompressed() {
	    return reportPdfCompressed;
    }
    @JsonIgnore
	public String getReportUrl() {
		return reportUrl;
	}
	public void setReportUrl(String reportUrl) {
		this.reportUrl = reportUrl;
	}
    @JsonIgnore
    public String getReportPdfCompressed() {
        return reportPdfCompressed;
    }
    public void setReportPdfCompressed(String reportPdfCompressed) {
        this.reportPdfCompressed = reportPdfCompressed;
    }
    public int getSize() {
		return size;
	}
	public void setSize(int size) {
		this.size = size;
	}
    public int getSizeCompressed() {
        return sizeCompressed;
    }
    public void setSizeCompressed(int sizeCompressed) {
        this.sizeCompressed = sizeCompressed;
    }
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
    public String getFileType() {
        if (StringUtils.isNotBlank(fileType)) {
            return fileType;
        }
		return reportTypeEnum == null? "": reportTypeEnum.getContentType();
	}
	public String getReportName() {
		return reportTypeEnum == null? "": reportTypeEnum.getDisplay();
	}
	public boolean getRead() {
		return isRead;
	}
	public void setRead(boolean isRead) {
		this.isRead = isRead;
	}
	public int getGenerationStatus() {
		return generationStatus;
	}
	public void setGenerationStatus(int generationStatus) {
		this.generationStatus = generationStatus;
	}
	@JsonProperty("isPaid")
	public boolean isPaid() {
		return isPaid;
	}
	public void setPaid(boolean isPaid) {
		this.isPaid = isPaid;
	}
	@JsonProperty("isReadable")
	public boolean isReadable() {
		return isReadable;
	}
	public void SetReadable(boolean isReadable) {
		this.isReadable = isReadable;
	}

	@Override
	public String toString() {
		return "ProjectReportFileTinyVo [reportId=" + reportId + ", reportTypeCode=" + reportTypeCode + ", reportUrl="
				+ reportUrl + ", isRead=" + isRead + ", size=" + size + ", generationStatus=" + generationStatus
				+ ", reportTypeEnum=" + reportTypeEnum + ", isPaid=" + isPaid + ", isReadable=" + isReadable + "]";
	}
}
