package com.bees360.entity.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import com.google.common.collect.Sets;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/03/19
 */

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ProjectServiceTypeEnum implements BaseCodeEnum{
    /**
     *
     */
    QUICK_INSPECT(0, "Limited Inspection", "Limited Inspection",
        Sets.newHashSet(ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, ReportTypeEnum.PROPERTY_IMAGE_REPORT),
        InspectionPurposeTypeEnum.CLAIM, "https://bees360.s3.amazonaws.com/app/beesgo/checklist/checklist.html"),
    /**
     *
     */
    FULL_ADJUSTMENT(1, "Full Inspection", "Full Inspection",
        Sets.newHashSet(ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, ReportTypeEnum.PROPERTY_IMAGE_REPORT),
		InspectionPurposeTypeEnum.CLAIM, "https://bees360.s3.amazonaws.com/app/beesgo/checklist/checklist.html"),
    /**
     *
     */
    ROOF_ONLY_UNDERWRITING(2, "Roof Only", "Roof Only Underwriting", ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT,
		InspectionPurposeTypeEnum.UNDERWRITING, "https://bees360.s3.amazonaws.com/app/beesgo/checklist/checklist.html"),
    /**
     *
     */
    EXTERIOR_UNDERWRITING(3, "Exterior", "Exterior Underwriting", ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT,
		InspectionPurposeTypeEnum.UNDERWRITING, "https://bees360.s3.amazonaws.com/app/beesgo/checklist/checklist.html"),
    /**
     *
     */
    FOUR_POINT_UNDERWRITING(4, "4-Point", "4-Point Underwriting", ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT,
		InspectionPurposeTypeEnum.UNDERWRITING, "https://bees360.s3.amazonaws.com/app/beesgo/checklist/checklist.html"),
    /**
     *
     */
    FOUR_POINT_SELF_UNDERWRITING(5, "4-Point Self", "4-Point Self Underwriting", ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT,
        InspectionPurposeTypeEnum.UNDERWRITING, "https://bees360.s3.amazonaws.com/app/beesgo/checklist/checklist.html"),
    /**
     * 当理赔完成，房屋修理完成之后，重新对房屋屋顶进行检测，确保房屋的屋顶按照理赔的结果进行修缮，以防屋主骗保。
     */
    POST_CONSTRUCTION_AUDIT(6, "Post-Construction Audit", "Post-Construction Audit", ReportTypeEnum.POST_CONSTRUCTION_AUDIT_REPORT,
        InspectionPurposeTypeEnum.CLAIM, ""),

    PREMIUM_FOUR_POINT_UNDERWRITING(7, "Premium 4-Point", "Premium 4-Point Underwriting", ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT,
        InspectionPurposeTypeEnum.UNDERWRITING, ""),

    EXPRESS_UNDERWRITING(
        8, "Express Underwriting", "Express Underwriting", ReportTypeEnum.EUR,
        InspectionPurposeTypeEnum.UNDERWRITING, ""),

    WHITE_GLOVE(
        9, "High Value Premium 4-point", "High Value Premium 4-point", ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT,
        InspectionPurposeTypeEnum.UNDERWRITING, ""),


    EXPRESS_INSPECTION(
        10,
        "Express Inspection",
        "Express Inspection",
        Set.of(ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT, ReportTypeEnum.PROPERTY_IMAGE_REPORT),
        InspectionPurposeTypeEnum.CLAIM,
        ""),

    SCHEDULING_ONLY(
        11,
        "Scheduling Only",
        "Scheduling Only",
        Set.of(ReportTypeEnum.SCHEDULING_ONLY_SUMMARY),
        InspectionPurposeTypeEnum.UNDERWRITING,
        ""),

    COMMERCIAL_UNDERWRITING(
        12,
        "Commercial Underwriting",
        "Commercial Underwriting",
        Set.of(ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT),
        InspectionPurposeTypeEnum.UNDERWRITING,
        ""),

    LIMITED_EXTERIOR_UNDERWRITING(
        13,
        "Limited Exterior",
        "Limited Exterior Underwriting",
        Set.of(ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT),
        InspectionPurposeTypeEnum.UNDERWRITING,
        ""),

	SNAP_360(
			14,
			"Snap360",
			"Snap360",
			Set.of(ReportTypeEnum.SNAP),
			InspectionPurposeTypeEnum.UNDERWRITING,
			""),

    PHOTO_ONLY_INSPECTION(
        15,
        "Photo Only Inspection",
        "Photo Only Inspection",
        Set.of(ReportTypeEnum.DRONE_PHOTO_SHEET, ReportTypeEnum.MOBILE_PHOTO_SHEET),
        InspectionPurposeTypeEnum.UNDERWRITING,
        ""
    )
    ;

    /**
     * 不可更改唯一属性
     */
	private final int code;
    /**
     * 不可更改属性唯一属性，与code类似
     */
	private final String value;
	private final String display;
	private final Set<ReportTypeEnum> reportTypes;
	private final InspectionPurposeTypeEnum inspectionPurposeType;
    private final String checklistUrl;

    private static final List<ProjectServiceTypeEnum> CLAIM_SERVICE_TYPE = Arrays.stream(values())
        .filter(serviceTypeEnum -> InspectionPurposeTypeEnum.CLAIM.equals(serviceTypeEnum.getInspectionPurposeType()))
        .collect(Collectors.toUnmodifiableList());

    private static final List<ProjectServiceTypeEnum> UNDERWRITING_SERVICE_TYPE = Arrays.stream(values())
        .filter(serviceTypeEnum -> InspectionPurposeTypeEnum.UNDERWRITING.equals(serviceTypeEnum.getInspectionPurposeType()))
        .collect(Collectors.toUnmodifiableList());

	ProjectServiceTypeEnum(int code, String value, String display, ReportTypeEnum reportTypeEnum,
		InspectionPurposeTypeEnum inspectionPurposeType, String url){
		this(code, value, display, Sets.newHashSet(reportTypeEnum), inspectionPurposeType, url);
	}

    ProjectServiceTypeEnum(int code, String value, String display, Set<ReportTypeEnum> reportTypes,
                           InspectionPurposeTypeEnum inspectionPurposeType, String url){
        this.code = code;
        this.value = value;
        this.display = display;
        this.reportTypes = Collections.unmodifiableSet(reportTypes);
        this.inspectionPurposeType = inspectionPurposeType;
        this.checklistUrl = url;
    }

	public static ProjectServiceTypeEnum getEnum(Integer code){
		if(code == null) {
			return null;
		}
        return Arrays.stream(ProjectServiceTypeEnum.values())
            .filter(t -> Objects.equals(t.getCode(), code))
            .findFirst()
            .orElse(null);
	}


	public static boolean exist(Integer code) {
		return code != null && getEnum(code) != null;
	}

	public static ProjectServiceTypeEnum getEnumByValue(String value) {
        // todo : temporary for white glove service name change, should be removed after complete change
        if (Objects.equals("White Glove Service", value)) {
            return ProjectServiceTypeEnum.WHITE_GLOVE;
        }
		for(ProjectServiceTypeEnum status: values()) {
			if(status.getValue().equals(value)) {
				return status;
			}
		}
		return null;
	}

    public static ProjectServiceTypeEnum getEnumByCaseInsensitiveValue(String value) {
        var trimValue = value.replace(" ", "");
        for (ProjectServiceTypeEnum type : values()) {
            if (type.getValue().replace(" ", "").equalsIgnoreCase(trimValue)) {
                return type;
            }
        }
        return null;
    }

    public static boolean isClaim(Integer serviceType) {
        return serviceType != null &&
            CLAIM_SERVICE_TYPE.contains(ProjectServiceTypeEnum.getEnum(serviceType));
    }

    public static boolean equals(Integer serviceTypeCode, ProjectServiceTypeEnum serviceType) {
        return serviceType != null
                && serviceTypeCode != null
                && Objects.equals(serviceTypeCode, serviceType.getCode());
    }

    public static Set<Integer> getClaimCodes() {
        return CLAIM_SERVICE_TYPE.stream().map(ProjectServiceTypeEnum::getCode).collect(Collectors.toUnmodifiableSet());
    }

    public static Set<Integer> getUnderwritingCodes() {
        return UNDERWRITING_SERVICE_TYPE.stream().map(ProjectServiceTypeEnum::getCode).collect(Collectors.toUnmodifiableSet());
    }

	public static boolean exist(String value) {
		return getEnumByValue(value) != null;
	}

	@Override
	public String getDisplay(){
		return display;
	}

	@Override
	@JsonValue
	public int getCode(){
		return code;
	}

	public String getChecklistUrl() { return checklistUrl;}
	public String getValue() {
		return value;
	}

    public Set<ReportTypeEnum> getReportTypes() {
        return reportTypes;
    }

    public InspectionPurposeTypeEnum getInspectionPurposeType() {
		return inspectionPurposeType;
	}

    public boolean containsReport(ReportTypeEnum type) {
        return reportTypes.contains(type);
    }

    public boolean containsReport(int reportType) {
        return reportTypes.stream().anyMatch(t -> Objects.equals(t.getCode(), reportType));
    }
}
