package com.bees360.entity;

import java.io.Serial;
import java.io.Serializable;

public class ImageAnnotationType implements Serializable {

    @Serial
    private static final long serialVersionUID = -6844439361804096803L;

	private long annotationTypeId;
	//	The annotation type name:
	//	ShingleDamage
	//	Facet: The facet on the roof.
	//	Corrosion: corrosion damage.
	//	Hail: hail damage.
	//	Wind: wind damage.
	//	Crack: crack damage.
	//	VisibleFacet
	private String annotationTypeName;
	//Whether this annotation can be shown on the image.
	private int shownOnImage;
	//Whether this annotation can be modified.
	private int editable;

	public ImageAnnotationType() {
		super();
	}

	public ImageAnnotationType(long annotationTypeId, String annotationTypeName, int shownOnImage, int editable) {
		super();
		this.annotationTypeId = annotationTypeId;
		this.annotationTypeName = annotationTypeName;
		this.shownOnImage = shownOnImage;
		this.editable = editable;
	}

	public long getAnnotationTypeId() {
		return annotationTypeId;
	}

	public void setAnnotationTypeId(long annotationTypeId) {
		this.annotationTypeId = annotationTypeId;
	}

	public String getAnnotationTypeName() {
		return annotationTypeName;
	}

	public void setAnnotationTypeName(String annotationTypeName) {
		this.annotationTypeName = annotationTypeName;
	}

	public int getShownOnImage() {
		return shownOnImage;
	}

	public void setShownOnImage(int shownOnImage) {
		this.shownOnImage = shownOnImage;
	}

	public int getEditable() {
		return editable;
	}

	public void setEditable(int editable) {
		this.editable = editable;
	}

	@Override
	public String toString() {
		return "ImageAnnotationType [annotationTypeId=" + annotationTypeId + ", annotationTypeName="
				+ annotationTypeName + ", shownOnImage=" + shownOnImage + ", editable=" + editable + "]";
	}

}
