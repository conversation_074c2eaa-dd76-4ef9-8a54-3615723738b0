package com.bees360.entity.openapi.reportsummary;

import lombok.Data;

import java.util.Map;

@Data
public class SummaryInteriorFloorplan {
    /**
     * Provides the count of bedrooms, bathrooms, closets, etc.
     */
    private Map<String, Integer> room;
    /**
     * If floor damage discovered. It indicates if damage is present in each type of room.
     */
    private Map<String, Boolean> hasDamage;
}
