package com.bees360.entity.vo;

import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProcessStatusEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ProjectTinyVoForApp {
	private long projectId;
	private long createdTime;

	private String address;
	private String city;
	private String state;
	private String country;
	private String zipCode;

	private String insurCompanyName;

	private String creatorEmail;
	private String creatorPhone;
	private String creatorName;

	private boolean isBooking;

	private boolean isFrontElevation;

	// include 3 images
	private String[] images;

	// related to status in HistoryEvent
	private int latestStatus;
	/**
	 * @see NewProjectStatusEnum#getCode()
	 */
    private Integer projectStatus;
	// related to status in HistoryEvent
	// ProjectStatusEnum.ADJUSTER_CLAIM_CHECKED_IN, ProjectStatusEnum.ADJUSTER_CLAIM_CHECKED_OUT
	// ProjectStatusEnum.ADJUSTER_UNDERWRITING_CHECKED_IN, ProjectStatusEnum.ADJUSTER_UNDERWRITING_CHECKED_OUT or 0
    /**
     * @see com.bees360.entity.enums.ProjectStatusEnum
     */
	private int checkStatusType;
	private int pilotCheckStatusType;
	private int checkStatus;

	private double gpsLocationLongitude;

	private double gpsLocationLatitude;

	/**
	 * Same as com.bees360.flyzone.FlyZoneType::getCode()
	 */
	private int flyZoneType;

	@JsonIgnore
    private Integer claimType;

    private long inspectionTime;

    public boolean getIsBooking() {
		return isBooking;
	}

	@JsonIgnore
	public int getLatestStatus() {
		return latestStatus;
	}

	@JsonIgnore
	public Integer getProjectStatus() {
		return projectStatus;
	}

	@JsonIgnore
	public boolean isFrontElevation() {
		return isFrontElevation;
	}

	public boolean getIsFrontElevation() {
		return isFrontElevation;
	}

	public void setIsFrontElevation(boolean isFrontElevation) {
		this.isFrontElevation = isFrontElevation;
	}

	public Integer getProjectStatusCode() {
		NewProjectStatusEnum status = NewProjectStatusEnum.getEnum(projectStatus);
		return status == null? null: status.getCode();
	}

	public String getProjectStatusName() {
        NewProjectStatusEnum status = NewProjectStatusEnum.getEnum(projectStatus);
        return status == null ? "" : status.getDisplay();
    }

	public String getStatus() {
		ProcessStatusEnum status = ProcessStatusEnum.getEnum(latestStatus);
		return status == null? "": status.getDisplay();
	}

	public Integer getInspectionPurposeTypeCode() {
		ClaimTypeEnum claimTypeEnum = claimType == null? null: ClaimTypeEnum.getEnum(claimType);
		InspectionPurposeTypeEnum code = InspectionPurposeTypeEnum.getEnum(claimTypeEnum);
		return code == null? InspectionPurposeTypeEnum.UNKNOWN.getCode():
            code.getCode();
	}

    public String getInspectionPurposeTypeName() {
        ClaimTypeEnum claimTypeEnum = claimType == null? null: ClaimTypeEnum.getEnum(claimType);
        InspectionPurposeTypeEnum code = InspectionPurposeTypeEnum.getEnum(claimTypeEnum);
        return code == null? InspectionPurposeTypeEnum.UNKNOWN.getDisplay():
            code.getDisplay();
    }


}
