package com.bees360.entity.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

public enum ProjectStateChangeReasonEnum {
    DENIED("DENIED", 2L, "Inspection was denied by the insured."),
    DENIED_ON_LOCATION("DENIED ON LOCATION", 23L, "Pilot was denied on location."),
    CANCELLATION_UNCONFIRMED(
            "CANCELLATION UNCONFIRMED",
            11L,
            "Insured stated he/she cancelled the policy, and declined the inspection."),
    CANCELLATION_CONFIRMED("CANCELLATION CONFIRMED", 26L, "Cancellation is confirmed."),
    UNABLE_TO_REACH_INSURED_AFTER_MULTIPLE_ATTEMPTS(
            "UNABLE TO REACH INSURED AFTER MULTIPLE ATTEMPTS",
            20L,
            "Unable to reach insured after multiple attempts. Inspection is closed out."),
    ;

    private final String changeReasonKey;
    private final Long opTagId;
    private final String reasonInCloseOutReport;

    ProjectStateChangeReasonEnum(
            String changeReasonKey, Long opTagId, String reasonInCloseOutReport) {
        this.changeReasonKey = changeReasonKey;
        this.opTagId = opTagId;
        this.reasonInCloseOutReport = reasonInCloseOutReport;
    }

    public static ProjectStateChangeReasonEnum getByOpTagId(Long opTagId) {
        if (opTagId == null) {
            return null;
        }
        return Arrays.stream(ProjectStateChangeReasonEnum.values())
                .filter(t -> Objects.equals(t.getOpTagId(), opTagId))
                .findFirst()
                .orElse(null);
    }

    public static ProjectStateChangeReasonEnum getChangeReasonByDisplayText(
            String changeReasonText) {
        if (changeReasonText == null) {
            return null;
        }
        return Arrays.stream(ProjectStateChangeReasonEnum.values())
                .filter(
                        t ->
                                StringUtils.equalsIgnoreCase(
                                        t.getChangeReasonKey(), changeReasonText))
                .findFirst()
                .orElse(null);
    }

    public String getChangeReasonKey() {
        return changeReasonKey;
    }

    public Long getOpTagId() {
        return opTagId;
    }

    public String getReasonInCloseOutReport() {
        return reasonInCloseOutReport;
    }
}
