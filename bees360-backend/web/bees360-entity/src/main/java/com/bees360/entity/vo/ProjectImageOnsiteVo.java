package com.bees360.entity.vo;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.dto.ImageAnnotationsDto;
import com.bees360.entity.dto.Point;
import com.bees360.entity.dto.ScreenshotAnnotation;
import com.bees360.entity.enums.ImagePartialViewTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class ProjectImageOnsiteVo implements Cloneable {

    private ProjectImage image;

	// the annotation types
	private Set<Integer> annotationTypes;

	// the annotation list
	private List<ScreenshotAnnotation> screenshots;

	private ImageAnnotationsDto annotations;

	private List<Map<String, Object>> mapping;

	private Point mapping2d;

	private String area;

    private List<String> note;

    // it must be assigned a value through the Enum ImagePartialViewTypeEnum.
    private int partialType = ImagePartialViewTypeEnum.ROOF.getCode();

	private List<Integer> facetIds;

	public ProjectImageOnsiteVo() {
		image = new ProjectImage();
	}

	public ProjectImageOnsiteVo(ProjectImage image) {
		this.image = image;
	}

	@JsonIgnore
	public String getCamPropertyMatrix() {
		return image.getCamPropertyMatrix();
	}

	public void setCamPropertyMatrix(String camPropertyMatrix) {
		this.image.setCamPropertyMatrix(camPropertyMatrix);
	}

	// getter and setter

	public long getShootingTime() {
		return this.image.getShootingTime();
	}

	public void setShootingTime(long shootingTime) {
		this.image.setShootingTime(shootingTime);
	}

	public String getImageId() {
		return image.getImageId();
	}

	public void setImageId(String imageId) {
		this.image.setImageId(imageId);
	}

	public String getFileName() {
		return image.getFileName();
	}

    public String getImageS3Key(){
	    return image.getImageS3Key();
    }

	public void setFileName(String fileName) {
		this.image.setFileName(fileName);
	}

	public String getFileNameLowerResolution() {
		return image.getFileNameLowerResolution();
	}

	public String getLowerResolutionImageS3Key(){
	    return image.getLowerResolutionImageS3Key();
    }

	public void setFileNameLowerResolution(String fileNameLowerResolution) {
		this.image.setFileNameLowerResolution(fileNameLowerResolution);
	}

	public String getFileNameMiddleResolution() {
		return image.getFileNameMiddleResolution();
	}

	public String getMiddleResolutionImageS3Key(){
	    return image.getMiddleResolutionImageS3Key();
    }

	public void setFileNameMiddleResolution(String fileNameMiddleResolution) {
		this.image.setFileNameMiddleResolution(fileNameMiddleResolution);
	}

	public String getAnnotationImage() {
		return image.getAnnotationImage();
	}

	public String getAnnotationImageS3Key(){
	    return image.getAnnotationImageS3Key();
    }

	public void setAnnotationImage(String annotationImage) {
		this.image.setAnnotationImage(annotationImage);
	}

	public long getFileSize() {
		return image.getFileSize();
	}

	public void setFileSize(long fileSize) {
		this.image.setFileSize(fileSize);
	}

	public long getUserId() {
		return image.getUserId();
	}

	public void setUserId(long userId) {
		this.image.setUserId(userId);
	}

	public long getUploadTime() {
		return image.getUploadTime();
	}

	public void setUploadTime(long uploadTime) {
		this.image.setUploadTime(uploadTime);
	}

	public String getOriginalFileName() {
		return image.getOriginalFileName();
	}

	public void setOriginalFileName(String originalFileName) {
		this.image.setOriginalFileName(originalFileName);
	}

	public int getFileSourceType() {
		return image.getFileSourceType();
	}

	public void setFileSourceType(int fileSourceType) {
		this.image.setFileSourceType(fileSourceType);
	}

	public double getGpsLocationLongitude() {
		return image.getGpsLocationLongitude();
	}

	public void setGpsLocationLongitude(double gpsLocationLongitude) {
		this.image.setGpsLocationLongitude(gpsLocationLongitude);
	}

	public double getGpsLocationLatitude() {
		return image.getGpsLocationLatitude();
	}

	public void setGpsLocationLatitude(double gpsLocationLatitude) {
		this.image.setGpsLocationLatitude(gpsLocationLatitude);
	}

	public int getImageHeight() {
		return image.getImageHeight();
	}

	public void setImageHeight(int imageHeight) {
		this.image.setImageHeight(imageHeight);
	}

	public int getImageWidth() {
		return image.getImageWidth();
	}

	public void setImageWidth(int imageWidth) {
		this.image.setImageWidth(imageWidth);
	}

	public long getProjectId() {
		return image.getProjectId();
	}

	public void setProjectId(long projectId) {
		this.image.setProjectId(projectId);
	}

	public int getDirection() {
		return image.getDirection();
	}

	public void setDirection(int direction) {
		this.image.setDirection(direction);
	}

    public int getOrientation() {
        if (image.getOrientation() == null) {
            return 0;
        }
        return image.getOrientation();
    }

	public void setOrientation(int orientation) {
		this.image.setOrientation(orientation);
	}

    public int getTiffOrientation() {
        return image.getTiffOrientation();
    }

    public void setTiffOrientation(int tiffOrientation) {
        image.setTiffOrientation(tiffOrientation);
    }


    public int getImageType() {
		return image.getImageType();
	}

	public void setImageType(int imageType) {
		this.image.setImageType(imageType);
	}

	public String getImageCategory() {
		return image.getImageCategory();
	}

	public void setImageCategory(String imageCategory) {
		this.image.setImageCategory(imageCategory);
	}

	public String getParentId() {
		return image.getParentId();
	}

	public void setParentId(String parentId) {
		this.image.setParentId(parentId);
	}

    public Long getImageSort() {
        return image.getImageSort();
    }

    public void setImageSort(Long imageSort) {
        this.image.setImageSort(imageSort);
    }

    public int getIn3DModel() {
        return this.image.getIn3DModel();
    }

    public void setIn3DModel(int in3DModel) {
        this.image.setIn3DModel(in3DModel);
    }

    public List<String> getNote() {
        return this.note;
    }

    public void setNote(List<String> note) {
        this.note = note;
    }

    @JsonIgnore
	public ProjectImage getImage() {
		return image;
	}

	public void setImage(ProjectImage image) {
		this.image = image;
	}

	public Set<Integer> getAnnotationTypes() {
		return annotationTypes;
	}

	public void setAnnotationTypes(Set<Integer> annotationTypes) {
		this.annotationTypes = annotationTypes;
	}

	public List<ScreenshotAnnotation> getScreenshots() {
		return screenshots;
	}

	public void setScreenshots(List<ScreenshotAnnotation> screenshots) {
		this.screenshots = screenshots;
	}

	public List<Map<String, Object>> getMapping() {
		return mapping;
	}

	public void setMapping(List<Map<String, Object>> mapping) {
		this.mapping = mapping;
	}

	public ImageAnnotationsDto getAnnotations() {
		return annotations;
	}

	public void setAnnotations(ImageAnnotationsDto annotations) {
		this.annotations = annotations;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public List<Integer> getFacetIds() {
		return facetIds;
	}

	public void setFacetIds(List<Integer> facetIds) {
		this.facetIds = facetIds;
	}

	public Point getMapping2d() {
		return mapping2d;
	}

	public void setMapping2d(Point mapping2d) {
		this.mapping2d = mapping2d;
	}

    public int getPartialType() {
        return partialType;
    }

    public void setPartialType(int partialType) {
        this.partialType = partialType;
    }

    @Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (!(obj instanceof ProjectImageOnsiteVo)) {
			return false;
		}
		ProjectImageOnsiteVo image = (ProjectImageOnsiteVo) obj;
		return Objects.equals(this.image.getImageId(), image.getImageId());
	}

	@Override
	public int hashCode() {
        return this.image.getImageId().hashCode();
	}

}
