package com.bees360.entity.vo;

import com.bees360.entity.label.ProjectLabel;
import com.bees360.web.core.validation.Phone;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/02/28 14:46
 */
@Data
@NoArgsConstructor
public class ProjectInsuredInfoVo extends AddressVo {
    /**
     * Policy Holder
     */
    private String assetOwnerName;

    private String insuredFirstName;

    private String insuredMiddleName;

    private String insuredLastName;
    /**
     * Policy Holder Phone
     */
    @Phone
    private String assetOwnerPhone;
    /**
     * Policy Holder Email
     */
    private String assetOwnerEmail;

    private String insuredHomePhone;

    private String insuredWorkPhone;

    /**
     * Type Of Property
     */
    private Integer projectType;

    private Long dueDate;
    /**
     * 管理员发送的note
     */
    private String claimNote;

    /**
     * 项目相关的留言
     */
    private String message;

    private String operatingCompany;

    private List<ProjectLabel> projectLabels;
}
