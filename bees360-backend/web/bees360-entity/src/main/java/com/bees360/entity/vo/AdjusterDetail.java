package com.bees360.entity.vo;

import java.util.List;

import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.enums.RoleEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class AdjusterDetail extends Traveler {
	private RoleEnum role = RoleEnum.ADJUSTER;

	public AdjusterDetail() {
	}

	public IdNameDto getRole() {
		return new IdNameDto(role.getRoleId(), role.getDisplay());
	}

	@JsonIgnore
	@Override
	public List<IdNameDto> getRoles() {
		return super.getRoles();
	}
}
