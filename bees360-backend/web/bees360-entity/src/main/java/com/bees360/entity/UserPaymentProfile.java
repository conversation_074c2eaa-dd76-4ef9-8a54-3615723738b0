package com.bees360.entity;

import java.io.Serial;
import java.io.Serializable;

public class UserPaymentProfile implements Serializable{

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;

	private long userId;
	private double discountPercent;
	private int freeOnTrailProjectNum;
	private double newCustomerDiscountPercent;
	private int newCustomerDiscountProjectNum;
	private double walletBalance;
	private double commissionBalance;
	private String currency;

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public double getDiscountPercent() {
		return discountPercent;
	}

	public void setDiscountPercent(double discountPercent) {
		this.discountPercent = discountPercent;
	}

	public int getFreeOnTrailProjectNum() {
		return freeOnTrailProjectNum;
	}

	public void setFreeOnTrailProjectNum(int freeOnTrailProjectNum) {
		this.freeOnTrailProjectNum = freeOnTrailProjectNum;
	}

	public double getNewCustomerDiscountPercent() {
		return newCustomerDiscountPercent;
	}

	public void setNewCustomerDiscountPercent(double newCustomerDiscountPercent) {
		this.newCustomerDiscountPercent = newCustomerDiscountPercent;
	}

	public int getNewCustomerDiscountProjectNum() {
		return newCustomerDiscountProjectNum;
	}

	public void setNewCustomerDiscountProjectNum(int newCustomerDiscountProjectNum) {
		this.newCustomerDiscountProjectNum = newCustomerDiscountProjectNum;
	}

	public double getWalletBalance() {
		return walletBalance;
	}

	public void setWalletBalance(double walletBalance) {
		this.walletBalance = walletBalance;
	}

	public double getCommissionBalance() {
		return commissionBalance;
	}

	public void setCommissionBalance(double commissionBalance) {
		this.commissionBalance = commissionBalance;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	@Override
	public String toString() {
		return "UserPaymentProfile [userId=" + userId + ", discountPercent=" + discountPercent
				+ ", freeOnTrailProjectNum=" + freeOnTrailProjectNum + ", newCustomerDiscountPercent="
				+ newCustomerDiscountPercent + ", newCustomerDiscountProjectNum=" + newCustomerDiscountProjectNum
				+ ", walletBalance=" + walletBalance + ", commissionBalance=" + commissionBalance + ", currency="
				+ currency + "]";
	}
}
