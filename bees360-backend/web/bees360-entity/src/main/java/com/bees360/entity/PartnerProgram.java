package com.bees360.entity;

public class PartnerProgram {
	private long id;
	private Integer inspectionService;
	private Integer highflyService;
	private String firstName;
	private String lastName;
	private String email;
	private String phone;
	private String company;

	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public Integer getInspectionService() {
		return inspectionService;
	}
	public void setInspectionService(Integer inspectionService) {
		this.inspectionService = inspectionService;
	}
	public Integer getHighflyService() {
		return highflyService;
	}
	public void setHighflyService(Integer highflyService) {
		this.highflyService = highflyService;
	}
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}

	@Override
	public String toString() {
		return "PartnerProgramVo [id=" + id + ", inspectionService=" + inspectionService + ", highflyService="
				+ highflyService + ", firstName=" + firstName + ", lastName=" + lastName + ", email=" + email
				+ ", phone=" + phone + ", company=" + company + "]";
	}
}
