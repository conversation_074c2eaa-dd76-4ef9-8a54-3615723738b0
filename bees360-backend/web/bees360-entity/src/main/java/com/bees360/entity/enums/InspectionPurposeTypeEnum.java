package com.bees360.entity.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/03/15 01:08
 */
public enum InspectionPurposeTypeEnum implements BaseCodeEnum {
    //@formatter:off
	UNKNOWN(0, "Unknown", false),
    CLAIM(1, "Claim", true,
	    ClaimTypeEnum.HAIL_WIND,
	    ClaimTypeEnum.HAIL,
	    ClaimTypeEnum.WIND,
        ClaimTypeEnum.FIRE,
        ClaimTypeEnum.FLOOD,
        ClaimTypeEnum.OTHERS,
        ClaimTypeEnum.HURRICANE),
	UNDERWRITING(2, "Underwriting", true,
		ClaimTypeEnum.UNDERWRITING_FIRST_INSPECTION);
    //@formatter:on

    static {
    	// 检查是否存在交叉包含
    	Map<ClaimTypeEnum, InspectionPurposeTypeEnum> allClaimTypes = new HashMap<>();
    	for(InspectionPurposeTypeEnum type: values()) {
    		for (ClaimTypeEnum claimType: type.subTypes) {
    			if(allClaimTypes.containsKey(claimType)) {
					throw new AssertionError(claimType + " has been contained by " + allClaimTypes.get(claimType));
			    }
			    allClaimTypes.put(claimType, type);
		    }
	    }
    	// 检查 InspectionPurposeTypeEnum 是否包含所有的 ClaimTypeEnum
    	if(allClaimTypes.size() != ClaimTypeEnum.values().length) {
			throw new AssertionError(InspectionPurposeTypeEnum.class + " should contains all of " + ClaimTypeEnum.class.getSimpleName());
	    }
    }

    private final int code;
    private final String display;
    private final boolean autoApproveReport;
    private final Set<ClaimTypeEnum> subTypes;
    private final ClaimTypeEnum defaultSubType;

    InspectionPurposeTypeEnum(int code, String display, boolean autoApproveReport, ClaimTypeEnum... subTypes) {
        this.code = code;
        this.display = display;
        this.autoApproveReport = autoApproveReport;
        this.subTypes = Collections.unmodifiableSet(new HashSet<>(Arrays.asList(subTypes)));
        this.defaultSubType = subTypes.length == 0? null: subTypes[0];
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDisplay() {
        return display;
    }

    public boolean isAutoApproveReport(){
        return autoApproveReport;
    }

    public Set<ClaimTypeEnum> getSubTypes() {
    	return subTypes;
    }

    public ClaimTypeEnum getDefaultSubType() {
    	return defaultSubType;
	}

	public static InspectionPurposeTypeEnum getEnum(Integer code) {
    	if(code == null) {
    		return null;
	    }
    	for(InspectionPurposeTypeEnum type: values()) {
    		if(type.getCode() == code) {
    			return type;
		    }
	    }
    	return null;
	}

    public static InspectionPurposeTypeEnum getEnum(ClaimTypeEnum type) {
    	if(type == null) {
    		return null;
	    }
    	for(InspectionPurposeTypeEnum serviceType: values()) {
    		if(serviceType.subTypes.contains(type)) {
    			return serviceType;
		    }
	    }
    	return null;
    }

    public static int getInspectionPurposeTypeCode(Integer claimType) {
        ClaimTypeEnum claimTypeEnum = ClaimTypeEnum.getEnum(claimType);
        InspectionPurposeTypeEnum code = InspectionPurposeTypeEnum.getEnum(claimTypeEnum);
        return code == null ? InspectionPurposeTypeEnum.UNKNOWN.getCode(): code.getCode();
    }


    public static String getInspectionPurposeTypeName(Integer claimType) {
        ClaimTypeEnum claimTypeEnum = ClaimTypeEnum.getEnum(claimType);
        InspectionPurposeTypeEnum code = InspectionPurposeTypeEnum.getEnum(claimTypeEnum);
        return code == null? InspectionPurposeTypeEnum.UNKNOWN.getDisplay():
            code.getDisplay();
    }
}
