package com.bees360.job.registry;

import com.bees360.entity.firebase.HoverJobStatusEnum;
import com.google.cloud.Timestamp;
import java.io.Serial;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Hover job <br>
 * https://gitlab.bees360.com/engineers/beespilot/-/issues/493
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JobPayload
@ToString
public class SerializableFirebaseHover extends SerializableFirebaseData {

    @Serial
    private static final long serialVersionUID = -3470615347626196075L;

    private String projectId;

    private String missionId;

    /** hover账户 */
    private String hoverAccount;

    /**
     * hover平台状态
     *
     * @see HoverJobStatusEnum
     */
    private Integer state;

    /** 文档最近一次的更新时间 */
    private Timestamp updateTime;

    /** state 最近一次的更新时间 */
    private Timestamp stateUpdateTime;

    /** ai端 hover状态ProjectHoverStatusEnum */
    private Integer bees360HoverState;

    /** deliverable_id */
    private Integer deliverableId;

    /**
     * hover_job 对应的 ESX 文件，complete 状态时会去取<br>
     * s3:report/ARCPhJgXrR5EO5tI1tFozg
     */
    private String exportESX;

    /**
     * hover_job 对应的 report 文件，complete 状态时会去取<br>
     * s3:report/ARCPhJgXrR5EO5tI1tFozg
     */
    private String report;
}
