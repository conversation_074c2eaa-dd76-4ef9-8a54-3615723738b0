package com.bees360.entity.dto;

import java.util.List;

import com.bees360.entity.HouseSegmentValue;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class HouseSegmentValueDto {

	private long id;

	private long projectId;

	private String code;

	/**
	 *  when valueType is SegmentValueTypeEnum.TREE_ROOT, the value is null
	 *  when valueType is SegmentValueTypeEnum image, the value is
	 *  HouseCategory.categoryIndex(categoryId),HouseCategory.subcategoryIndex(subcategoryId)
	 *  when valueType is SegmentValueTypeEnum.SINGLE_ELECTION or SegmentValueTypeEnum.RADIO_ELECTION
	 *  the value is item code.
	 *  when valueType is SegmentValueTypeEnum.CHECKBOX, the value is a splice of item code.
	 *  for example 282-10-55-11162,282-10-55-11163,282-10-55-11164
	 *  when valueType is SegmentValueTypeEnum.INPUT, The value is the input value.
	 */
	private String value;

	private String additional;
	// SegmentValueTypeEnum
	private int valueType;
	// SegmentCodeTypeEnum
	private int codeType;

	private long rootId;

	/**
	 * 0:no damage 1:is damage
	 */
	private boolean isDamage;

	/**
	 * Only for pictures.0:not must shot 1:must shot
	 */
	private boolean isRequired;

	private String category;

	private String subcategory;

	private String description;

	private long descriptionId;

	/**
	 * 0:system, 1,add
	 */
	private int placeHolderType;

	/**
	 * the category type, see table HouseCategory.type or SegmentCodeTypeEnum
	 * 10:roof, 15: Ridge cap, 20: Drip edge, 25:Valley, 30 Exhaust cap, 35: Pipe jack, 40: Attic vent,
	 * 45: Attic vents, 50: Ridge Vents, 55: Flashing, 60: Roof Vents, 65: Ventilator, 100: Elevation,
	 * 105: Window screen, 110: Window reglaze, 115: Window beading, 120: Gutters/Downspouts,
	 * 125: A/C fins, 130: Fascia, 135: Windows, 200: close up or overview(Unmodifiable)
	 */
	private int type;

	private List<HouseSegmentValueDto> data;

	public HouseSegmentValueDto() {
		super();
	}

	public void setHouseSegmentValue(HouseSegmentValue houseSegmentValue) {
		this.id = houseSegmentValue.getId();
		this.projectId = houseSegmentValue.getProjectId();
		this.code = houseSegmentValue.getCode();
		this.value = houseSegmentValue.getValue();
		this.additional = houseSegmentValue.getAdditional();
		this.valueType = houseSegmentValue.getValueType();
		this.codeType = houseSegmentValue.getCodeType();
		this.rootId = houseSegmentValue.getRootId();
		this.isDamage = houseSegmentValue.getIsDamage();
		this.isRequired = houseSegmentValue.getIsRequired();
	}

	public List<HouseSegmentValueDto> getData() {
		return data;
	}

	public void setData(List<HouseSegmentValueDto> data) {
		this.data = data;
	}

	public long getRootId() {
		return rootId;
	}

	public void setRootId(long rootId) {
		this.rootId = rootId;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getProjectId() {
		return projectId;
	}

	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public int getValueType() {
		return valueType;
	}

	public void setValueType(int valueType) {
		this.valueType = valueType;
	}

	public int getCodeType() {
		return codeType;
	}

	public void setCodeType(int codeType) {
		this.codeType = codeType;
	}

	public boolean getIsDamage() {
		return isDamage;
	}

	public void setIsDamage(boolean isDamage) {
		this.isDamage = isDamage;
	}

	public boolean getIsRequired() {
		return isRequired;
	}

	public void setIsRequired(boolean isRequired) {
		this.isRequired = isRequired;
	}


	public String getAdditional() {
		return additional;
	}

	public void setAdditional(String additional) {
		this.additional = additional;
	}

	@JsonIgnore
	public int getRootIndex() {
		String rootIndexStr = this.code.split(AppSegmentDto.SEGMENT_CODE_SPLIT)[0];
		return Integer.parseInt(rootIndexStr);
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public long getDescriptionId() {
		return descriptionId;
	}

	public void setDescriptionId(long descriptionId) {
		this.descriptionId = descriptionId;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getSubcategory() {
		return subcategory;
	}

	public void setSubcategory(String subcategory) {
		this.subcategory = subcategory;
	}

	public int getPlaceHolderType() {
		return placeHolderType;
	}

	public void setPlaceHolderType(int placeHolderType) {
		this.placeHolderType = placeHolderType;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	@Override
	public String toString() {
		return "HouseSegmentValueDto [id=" + id + ", projectId=" + projectId + ", code=" + code + ", value=" + value
				+ ", additional=" + additional + ", valueType=" + valueType + ", codeType=" + codeType + ", rootId="
				+ rootId + ", isDamage=" + isDamage + ", isRequired=" + isRequired + ", category=" + category
				+ ", subcategory=" + subcategory + ", description=" + description + ", descriptionId=" + descriptionId
				+ ", placeHolderType=" + placeHolderType + ", type=" + type + ", data=" + data + "]";
	}

}
