package com.bees360.entity;

import com.bees360.entity.firebase.BatchStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;

/**
 * BeesPilotStatus
 * <AUTHOR>
 * @since 2020/07/31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BeesPilotBatch {

    private String batchNo;

    private Long userId;

    private BigDecimal basePay;

    private BigDecimal extraPay;

    private LocalDate planPaymentDate;

    private String note;

    private Long payTime;

    private Long createdBy;

    private Timestamp createdAt;

    private Timestamp updatedAt;

    private List<BeesPilotBatchItem> batchItems;

    private LocalDate dueDate;

    private boolean isDeleted;

    /**
     * 飞手是否默认接受该Batch
     */
    private boolean isPendingAcceptance;

    /**
     * @see BatchStatusEnum#getStatus()
     */
    private int status;

    private String reason;
}
