package com.bees360.entity.enums.productandpayment;

import com.bees360.entity.enums.BaseCodeEnum;
import com.bees360.entity.enums.DiscountOffTypeEnum;

public enum GlobalDiscountTypeEnum implements BaseCodeEnum {
	NO_DISCOUNT(0, "No Discount", false, DiscountOffTypeEnum.PERCENTAGE_OFF),
	FREE_PROJECT_NUMBER(1, "Free Project Number", true, DiscountOffTypeEnum.PERCENTAGE_OFF),
	LIMITED_DISCOUNT(2, "Limited Discount", true, DiscountOffTypeEnum.PERCENTAGE_OFF),
	UNLIMITED_DISCOUNT(3, "Unlimited Discount", false, DiscountOffTypeEnum.PERCENTAGE_OFF)
	;

	private final int code;
	private final String display;
	private final boolean isNumberLimited;
	private final DiscountOffTypeEnum offType;

	GlobalDiscountTypeEnum(int code, String display, boolean isNumberLimited, DiscountOffTypeEnum offType) {
		this.code = code;
		this.display = display;
		this.isNumberLimited = isNumberLimited;
		this.offType = offType;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public boolean isNumberLimited() {
		return isNumberLimited;
	}

	public DiscountOffTypeEnum getOffType() {
		return offType;
	}

	public static GlobalDiscountTypeEnum getEnum(int code) {
		for(GlobalDiscountTypeEnum discountType: GlobalDiscountTypeEnum.values()) {
			if(discountType.getCode() == code) {
				return discountType;
			}
		}
		return null;
	}

	public static GlobalDiscountTypeEnum getEnum(int code, GlobalDiscountTypeEnum defaultType) {
		GlobalDiscountTypeEnum type = GlobalDiscountTypeEnum.getEnum(code);
		return type == null? defaultType: type;
	}
}
