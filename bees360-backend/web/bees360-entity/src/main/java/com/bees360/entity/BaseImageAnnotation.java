package com.bees360.entity;

import com.bees360.entity.dto.Point;
import com.bees360.entity.enums.AnnotationTypeEnum;
import java.io.Serial;
import java.io.Serializable;
import lombok.experimental.SuperBuilder;

@SuperBuilder
public class BaseImageAnnotation implements Comparable<BaseImageAnnotation>, Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

	// annotation share a same id with the same source annotation
	protected long annotationId;
	protected String imageId;
	protected int facetId = FACETID_NON_EXISTENT;
	protected long projectId;
	// a polygon string like: POLYGON((3 2,2.33 4, ... , 3 2))
	// the data got from mysql geometry data type.
	protected String annotationPolygon;
	protected long createdTime;
	protected double centerPointX;
	protected double centerPointY;
	protected int annotationType;
	protected int usageType;
	protected long generatedBy;
    protected double confidenceLevel = DEFAULT_CONFIDENCE_LEVEL;
    protected int sourceType = SOURCE_TYPE_DEFAULT;

	public final static int FACETID_NON_EXISTENT = -1;

    public final static double DEFAULT_CONFIDENCE_LEVEL = 1;

    public final static int SOURCE_TYPE_DEFAULT = 1;

    public BaseImageAnnotation() {
    }

    public Point center() {
		return new Point(centerPointX, centerPointY);
	}

	/* getter and setter */
	public long getAnnotationId() {
		return annotationId;
	}
	public void setAnnotationId(long annotationId) {
		this.annotationId = annotationId;
	}
	public String getImageId() {
		return imageId;
	}
	public void setImageId(String imageId) {
		this.imageId = imageId;
	}
	public int getFacetId() {
		return facetId;
	}
	public void setFacetId(int facetId) {
		this.facetId = facetId;
	}
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public String getAnnotationPolygon() {
		return annotationPolygon;
	}
	public void setAnnotationPolygon(String annotationPolygon) {
		this.annotationPolygon = annotationPolygon;
	}
	public long getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}
	public double getCenterPointX() {
		return centerPointX;
	}
	public void setCenterPointX(double centerPointX) {
		this.centerPointX = centerPointX;
	}
	public double getCenterPointY() {
		return centerPointY;
	}
	public void setCenterPointY(double centerPointY) {
		this.centerPointY = centerPointY;
	}
	public int getAnnotationType() {
		return annotationType;
	}
	public void setAnnotationType(int annotationType) {
		this.annotationType = annotationType;
	}
	public int getUsageType() {
		return usageType;
	}
	public void setUsageType(int usageType) {
		this.usageType = usageType;
	}
	public long getGeneratedBy() {
		return generatedBy;
	}
	public void setGeneratedBy(long generatedBy) {
		this.generatedBy = generatedBy;
	}
    public int getSourceType() {
        return sourceType;
    }
    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }
    public double getConfidenceLevel() {
        return confidenceLevel;
    }
    public void setConfidenceLevel(double confidenceLevel) {
        this.confidenceLevel = confidenceLevel;
    }

    @Override
    public int compareTo(BaseImageAnnotation another) {
	    // nature order with annotationId exception annotation type is ROOF_PART
	    int result = annotationId < another.getAnnotationId() ? -1 : 1;
        if (this.annotationType == AnnotationTypeEnum.ROOF_PART.getCode()
            && another.getAnnotationType() == AnnotationTypeEnum.ROOF_PART.getCode()) {
            return result;
        }
        if (this.annotationType == AnnotationTypeEnum.ROOF_PART.getCode()) {
            return -1;
        }
        if (another.getAnnotationType() == AnnotationTypeEnum.ROOF_PART.getCode()) {
            return 1;
        }
        return result;
    }
}
