package com.bees360.entity.vo;

import com.bees360.entity.dto.Point;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/09/16 12:17
 */
public class ImageCategoryMatchPixel {
    private Point pixel;
    private int facetId;
    private List<ImageCategory> categories;

    public Point getPixel() {
        return pixel;
    }

    public void setPixel(Point pixel) {
        this.pixel = pixel;
    }

    public int getFacetId() {
        return facetId;
    }

    public void setFacetId(int facetId) {
        this.facetId = facetId;
    }

    public List<ImageCategory> getCategories() {
        return categories;
    }

    public void setCategories(List<ImageCategory> categories) {
        this.categories = categories;
    }

    @Override
    public String toString() {
        return "ImageCategoryMatchPixel{" + "pixel=" + pixel + ", facetId=" + facetId + ", categories=" + categories
            + '}';
    }
}
