package com.bees360.entity.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/01/19 11:44
 */
@Data
public class MapAddress {
    private String country;
    private String state;
    private String city;
    private String zipCode;
    private String address;

    public String toFullAddress() {
        return nullToBlank(address) + ", " + nullToBlank(city) + ", " + nullToBlank(state) + " " + nullToBlank(zipCode)
            + ", " + nullToBlank(country);
    }

    private String nullToBlank(String value) {
        return value == null ? "" : value;
    }
}
