package com.bees360.entity.openapi.client;

import lombok.Data;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.StringJoiner;

@Data
public class OpenApiClient {

    public static final String SCOPE_SEPARATOR = ",";

    private Long id;
    private String clientName;
    private String clientId;
    private Long companyId;
    private String clientSecret;
    private Long accessTokenValidateSeconds;
    private Long refreshTokenValidateSeconds;
    // 以 , 分隔的字符串连接
    private String scopes;
    /**
     * 每个二进制位表示一个 GrantType
     * @see com.bees360.entity.enums.openapi.GrantType
     */
    private Integer grantType;
    private Long createAt;
    private Long updateAt;
    private Long addBy;
    private Long updateBy;

    public List<String> getScopeList(){
        return Arrays.asList(this.scopes.split(SCOPE_SEPARATOR));
    }

    public void setScopes(String scopes){
        this.scopes = scopes;
    }

    public void setScopes(Collection<String> scopes){
        final StringJoiner joiner = new StringJoiner(OpenApiClient.SCOPE_SEPARATOR);
        scopes.forEach(joiner::add);
        this.scopes = joiner.toString();
    }
}
