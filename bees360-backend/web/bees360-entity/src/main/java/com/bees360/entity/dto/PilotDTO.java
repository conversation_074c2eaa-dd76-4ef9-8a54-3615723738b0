package com.bees360.entity.dto;

import java.math.BigDecimal;
import java.util.Date;

import com.bees360.web.core.validation.Phone;
import lombok.Data;

/**
 * 深圳聚蜂智能科技有限公司 版权所有 © Copyright 2019 Bees360
 *
 * @Description: 飞手模型
 * @Project: com.bees360.entity.dto
 * @CreateDate: 2019/11/8
 * @Author: <a href="<EMAIL>">jiankang.xia</a>
 */
@Data
public class PilotDTO {

    private String companyName;
    private String account;
    private String password;
    private String rePassword;
    private String firstName;
    private String lastName;
    private String emailVerificationCode;
    private String phoneVerificationCode;
    @Phone
    private String phone;
    private String email;
    private String address;
    private String city;
    private String state;
    private String country;
    private String zipCode;
    private Integer audienceId;
    /**
     * 飞手 飞行半径
     */
    private Integer travelRadius;
    /**
     * 飞手证书执照
     */
    // private List<String> licenseKeyUrls;
    /**
     * 证书签发日期
     */
    private Date licenseIssueDate;
    /**
     * 证书过期时间
     */
    private Date licenseExpiryDate;
    /**
     * 飞手证书执照
     */
    // private List<String> insuranceKeyUrls;
    /**
     * 保单金额
     */
    private BigDecimal insuranceAmount;
    /**
     * 保单过期时间
     */
    private Date insuranceExpiryDate;
    /**
     * 保单承保日期
     */
    private Date insuranceUnderwritingDate;

    private String licenseNumber;

    private String insuranceNumber;

    private Double gpsLocationLongitude;

    private Double gpsLocationLatitude;

    private Boolean adjusterRole;

    private String adjusterLicenseState;

    private Long createBy;
}
