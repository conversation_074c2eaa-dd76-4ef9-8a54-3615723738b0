package com.bees360.entity.dto;

import com.bees360.entity.BaseImageAnnotation;
import java.util.ArrayList;
import java.util.List;

import com.bees360.entity.ProjectFacet;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class ImageAnnotationCellDto implements Cloneable{

	protected String annotationId;
	protected long longTypeId;

	// annotation type
	protected int type;
	protected int usageType;
	protected int facetId = ProjectFacet.INEXISTENT_FACET_ID;
	protected String imageId;
	// left top point
	protected Point p1;
	// left bottom point
	protected Point p2;
	// right bottom point
	protected Point p3;
	// right top point
	protected Point p4;

	protected double confidence;

	private Point center = null;

	public static final int POINT_NUMBER = 4;
	private List<Point> points;

    protected int sourceType = BaseImageAnnotation.SOURCE_TYPE_DEFAULT;

	public ImageAnnotationCellDto() {
		initPoints();
	}

	public ImageAnnotationCellDto(Point p1, Point p2, Point p3, Point p4) {
		this();
		this.setP1(p1);
		this.setP2(p2);
		this.setP3(p3);
		this.setP4(p4);
	}

	private void initPoints() {
		points = new ArrayList<Point>(POINT_NUMBER);
		for(int i = 0; i < POINT_NUMBER; i ++) {
			points.add(null);
		}
	}

	public void setP(int index, Point p){
		switch(index){
		case 1: {
			p1 = p;
			break;
		}
		case 2: {
			p2 = p;
			break;
		}
		case 3: {
			p3 = p;
			break;
		}
		case 4:{
			p4 = p;
			break;
		}
		default:{
			return;
		}
		}
		points.set(index - 1, p);
		this.center = null;
	}

	@JsonIgnore
	public Point getP(int index){
		switch(index){
		case 1:{
			return p1;
		}
		case 2:{
			return p2;
		}
		case 3:{
			return p3;
		}
		case 4:{
			return p4;
		}
		default:{
			return null;
		}
		}
	}

	public String getAnnotationId() {
		return annotationId;
	}
	public void setAnnotationId(String annotationId) {
		this.annotationId = annotationId;
	}
	@JsonIgnore
	public long getLongTypeId(){
		return longTypeId;
	}
	/**
	 * set annotationId and longTypeId at the same time.
	 * @param id
	 */
	public void setId(long id){
		this.annotationId = id + "";
		this.longTypeId = id;
	}

	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public int getFacetId() {
		return facetId;
	}
	public void setFacetId(int facetId) {
		this.facetId = facetId;
	}
	public String getImageId() {
		return imageId;
	}
	public void setImageId(String imageId) {
		this.imageId = imageId;
	}
	public Point getP1() {
		return p1;
	}
	public void setP1(Point p1) {
		setP(1, p1);
	}
	public Point getP2() {
		return p2;
	}
	public void setP2(Point p2) {
		setP(2, p2);
	}
	public Point getP3() {
		return p3;
	}
	public void setP3(Point p3) {
		setP(3, p3);
	}
	public Point getP4() {
		return p4;
	}
	public void setP4(Point p4) {
		setP(4, p4);
	}
	@JsonIgnore
	public double getConfidence() {
		return confidence;
	}

	public void setConfidence(double confidence) {
		this.confidence = confidence;
	}
	@JsonIgnore
	public int getUsageType() {
		return usageType;
	}

	public void setUsageType(int usageType) {
		this.usageType = usageType;
	}

	@Override
	public String toString() {
		return "ImageAnnotationCellDto [annotationId=" + annotationId + ", longTypeId=" + longTypeId + ", type=" + type
				+ ", facetId=" + facetId + ", usageType=" + usageType + ", imageId=" + imageId + ", p1=" + p1 + ", p2=" + p2 + ", p3=" + p3 + ", p4="
				+ p4 + ", confidence=" + confidence + ", points=" + points + "]";
	}

	public List<Point> points() {
		return points;
	}

	public void setPoints(List<Point> points){
		this.points.clear();
		this.points.addAll(points);
	}

    public int getSourceType() {
        return sourceType;
    }

    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }

    public void setPoints(){
		this.points.clear();
		points.add(p1);
		points.add(p2);
		points.add(p3);
		points.add(p4);
	}

	public Point center(){
		if(center == null) {
			double centerPointX = 0;
			double centerPointY = 0;
			List<Point> points = points();
			for(Point point: points){
				centerPointX += point.getX();
				centerPointY += point.getY();
			}
			centerPointX /= points.size();
			centerPointY /= points.size();
			center = new Point(centerPointX, centerPointY);
		}
		return center;
	}

	public ImageAnnotationCellDto move(Point vector) {
		for(Point p: points) {
			p.setX(p.getX() + vector.getX());
			p.setY(p.getY() + vector.getY());
		}
		center = null;
		return this;
	}

	public ImageAnnotationCellDto zoom(double ratio) {
		for(Point p: points) {
			p.setX(p.getX() * ratio);
			p.setY(p.getY() * ratio);
		}
		center = null;
		return this;
	}

	/**
	 * keep the center position, change the width and the height.
	 * @param ratio
	 * @return
	 */
	public ImageAnnotationCellDto zoomToCenter(double ratio) {
		Point oldC = center();
		zoom(ratio);
		Point newC = center();
		Point vactor = new Point(oldC.getX() - newC.getX(), oldC.getY() - newC.getY());
		move(vactor);
		return this;
	}

	@JsonIgnore
	public Rectangle getBounds() {
		Point leftTopP = new Point(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY);
		Point rightBottomP = new Point(Double.NEGATIVE_INFINITY, Double.NEGATIVE_INFINITY);
		for(Point p : points()){
			leftTopP.setX(Math.min(p.getX(), leftTopP.getX()));
			leftTopP.setY(Math.min(p.getY(), leftTopP.getY()));

			rightBottomP.setX(Math.max(p.getX(), rightBottomP.getX()));
			rightBottomP.setY(Math.max(p.getY(), rightBottomP.getY()));
		}
		int width = (int)Math.ceil(rightBottomP.getX() - leftTopP.getX());
		int height = (int)Math.ceil(rightBottomP.getY() - leftTopP.getY());
        return new Rectangle((int)leftTopP.getX(), (int)leftTopP.getY(), width, height);
	}

	@JsonIgnore
	public boolean isLegalAnnotation() {
		if(p1 == null || p2 == null || p3 == null || p4 == null) {
			return false;
		}
		if((Math.abs(p1.getX()) < 1e-9 && Math.abs(p1.getY()) < 1e-9) &&
				(Math.abs(p2.getX()) < 1e-9 && Math.abs(p2.getY()) < 1e-9) &&
				(Math.abs(p3.getX()) < 1e-9 && Math.abs(p3.getY()) < 1e-9) &&
				(Math.abs(p4.getX()) < 1e-9 && Math.abs(p4.getY()) < 1e-9)) {

			return false;
		}
		return true;
	}

	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((annotationId == null) ? 0 : annotationId.hashCode());
		result = prime * result + facetId;
		result = prime * result + imageId.hashCode();
		result = prime * result + (int) (longTypeId ^ (longTypeId >>> 32));
		result = prime * result + ((p1 == null) ? 0 : p1.hashCode());
		result = prime * result + ((p2 == null) ? 0 : p2.hashCode());
		result = prime * result + ((p3 == null) ? 0 : p3.hashCode());
		result = prime * result + ((p4 == null) ? 0 : p4.hashCode());
		result = prime * result + type;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ImageAnnotationCellDto other = (ImageAnnotationCellDto) obj;
		if (annotationId == null) {
			if (other.annotationId != null)
				return false;
		} else if (!annotationId.equals(other.annotationId))
			return false;
		if (facetId != other.facetId)
			return false;
		if (imageId != other.imageId)
			return false;
		if (longTypeId != other.longTypeId)
			return false;
		if (p1 == null) {
			if (other.p1 != null)
				return false;
		} else if (!p1.equals(other.p1))
			return false;
		if (p2 == null) {
			if (other.p2 != null)
				return false;
		} else if (!p2.equals(other.p2))
			return false;
		if (p3 == null) {
			if (other.p3 != null)
				return false;
		} else if (!p3.equals(other.p3))
			return false;
		if (p4 == null) {
			if (other.p4 != null)
				return false;
		} else if (!p4.equals(other.p4))
			return false;
		if (type != other.type)
			return false;
		return true;
	}

	@Override
	public Object clone() {
		ImageAnnotationCellDto cell = null;

		try {
			cell = (ImageAnnotationCellDto)super.clone();
		} catch (CloneNotSupportedException e) {
			// do nothing
			e.printStackTrace();
		}
		cell.initPoints();
		cell.setP(1, p1.clone());
		cell.setP(2, p2.clone());
		cell.setP(3, p3.clone());
		cell.setP(4, p4.clone());
		return cell;
	}

	public static void main(String[] args) {
		ImageAnnotationCellDto cell = new ImageAnnotationCellDto();
		for(int i = 0; i < ImageAnnotationCellDto.POINT_NUMBER; i ++) {
			cell.setP(i + 1, new Point(i, i));
		}
		ImageAnnotationCellDto cellClone = (ImageAnnotationCellDto) cell.clone();
		System.out.println("CLONE: " + cellClone);
		System.out.println("ORIGINAL: " + cell);
		System.out.println("CENTER: " + cell.center());
		cell.zoom(2);
		System.out.println("ZOOMED: " + cell);
		System.out.println("CENTER: " + cell.center());
		cell.move(new Point(1, 1));
		System.out.println("MOVED: " + cell);
		System.out.println("CENTER: " + cell.center());
		cell.setP1(new Point(10, 10));
		System.out.println("CHANGED P1: " + cell);
		System.out.println("CENTER: " + cell.center());
		System.out.println("CLONE: " + cellClone);
	}
}
