package com.bees360.entity.enums;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 *
 * <AUTHOR>
 *
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum OperationEnum implements BaseCodeEnum{
	LIST(-1, "LIST"),
	SEARCH(0, "SEARCH"),
	VIEW(1, "VIEW"),
	CREATE(2, "CREATE"),
	UPDATE(3, "UPDATE"),
	DELETE(4, "DELETE"),
	AUDIT(5, "AUDIT"),
	;

	private final int operationId;
	private final String display;

	OperationEnum(int operationId, String display){
		this.operationId = operationId;
		this.display = display;
	}

	public static OperationEnum getOperationById(int operationId){
		for(OperationEnum operationEnum: OperationEnum.values()){
			if(operationId == operationEnum.getOperationId()){
				return operationEnum;
			}
		}
		return null;
	}

	@Override
	@JsonValue
	public int getCode(){
		return operationId;
	}

	public int getOperationId() {
		return operationId;
	}

	@Override
	public String getDisplay(){
		return display;
	}

	public long getBitMap(){
		long bit = 1L;
		for(int i = 0; i < operationId; i ++){
			bit <<= 1;
		}
		return bit;
	}

	public String toBitMapString(){
		StringBuffer str = new StringBuffer();
		str.append('1');
		for(int i = 0; i < operationId; i ++){
			str.append('0');
		}
		return str.toString();
	}

	public static OperationEnum stringToOperation(String OperationStr){
		OperationEnum[] Operations = OperationEnum.values();
		for(OperationEnum Operation: Operations){
			String display = Operation.getDisplay().toLowerCase();
			String display2 = display.replaceAll(" ", "");
			String strLower = OperationStr.toLowerCase();
			if(display.equals(strLower) || display2.equals(strLower)){
				return Operation;
			}
		}
		return null;
	}

	public static List<OperationEnum> listOperations(long Operations){
		ArrayList<OperationEnum> list = new ArrayList<OperationEnum>();
		long bit = Operations;
		bit = 1L;
		for(int i = 0; i < 64; i ++){
			if((bit & Operations) == bit){
				list.add(OperationEnum.getOperationById(i));
			}
			bit <<= 1;
		}
		return list;
	}


	public static OperationEnum getEnum(int code){
		OperationEnum[] Operations = OperationEnum.values();
		for(OperationEnum operation: Operations){
			if(operation.getCode() == code){
				return operation;
			}
		}
		return null;
	}

	public static void main(String[] args){

	}
}
