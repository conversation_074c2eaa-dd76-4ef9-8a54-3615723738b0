package com.bees360.entity.enums;

import java.util.ArrayList;
import java.util.List;

import com.bees360.entity.dto.IdNameDto;

public enum RoofAgeEnum implements BaseCodeEnum{
	ONE_YEAR(1, "Under a year"),
	FIVE_YEAR(2, "1-5 years"),
	THREE_YEAR(3, "5-10 years"),
	FIFTEEN_YEAR(4, "10-15 years"),
	TWENTY_YEAR(5, "15-20 years"),
	MULTI_YEAR(6, "20+ years");

	private final int code;
	private final String display;

	RoofAgeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public static List<IdNameDto> getValues(){
		RoofAgeEnum[] ageEnums = RoofAgeEnum.values();
		List<IdNameDto> ageList = new ArrayList<>(ageEnums.length);
		for(RoofAgeEnum ageEnum: ageEnums){
			ageList.add(new IdNameDto(ageEnum.getCode(), ageEnum.getDisplay()));
		}
		return ageList;
	}

	public static boolean isRoofAge(int code) {
		for(RoofAgeEnum roofAge: RoofAgeEnum.values()) {
			if (code == roofAge.getCode()) {
				return true;
			}
		}
		return false;
	}

}
