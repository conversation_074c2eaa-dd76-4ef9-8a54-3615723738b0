package com.bees360.entity;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * BeesPilotStatus
 * <AUTHOR>
 * @since 2021/2/23
 */
@Data
public class BeesPilotStatusVo implements Serializable {
    private Long projectId;

    /**
     * 图片是否已全部上传
     */
    private boolean imageUploaded;

    /**
     * pilot complete quiz or not
     */
    private boolean quizCompleted;

    /**
     * pilot complete verify address of the task or not
     */
    private boolean addressVerified;

    /**
     * 是否beespilot所有的任务都已经完成
     * 包括手机照片拍摄和无人机照片拍摄以及form填写等。
     */
    private boolean isAllTaskCompleted;

    @Serial
    private static final long serialVersionUID = 23423423424L;
}
