package com.bees360.entity.vo;

import com.bees360.commons.springsupport.validation.Year;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2020/02/28 14:49
 */
@Data
@NoArgsConstructor
public class ProjectInspectionInfoVo {
    private String policyNumber;
    private String inspectionNumber;
    /**
     * 对应页面的: Type of Loss
     */
    private Integer claimType;
    /**
     * 对应页面的: Date of Loss
     */
    private Long damageEventTime;
    /**
     * 代理信息
     */
    private String agent;
    /**
     * 代理人名称
     */
    private String agentContactName;
    private String agentPhone;
    private String agentEmail;
    /**
     * 对应页面的: insuredBy
     */
    private CompanyCard insuranceCompany;
    /**
     * 对应页面的: processBy
     */
    private CompanyCard repairCompany;
    /**
     * 检测时间
     */
    private Long inspectionTime;
    private Long scheduledTime;
    private Long inspectionDueDate;

    private LocalDate policyEffectiveDate;

    @Year
    private String yearBuilt;

    private int payStatus;

    private String payStatusName;

    /**
     * 状态转变为siteInspected的时间
     * @see com.bees360.entity.enums.NewProjectStatusEnum#SITE_INSPECTED
     */
    private Long siteInspectedTime;

    private Instant initialCustomerContactedTime;

    private Integer cancelStatus;

    private String batchNo;
}
