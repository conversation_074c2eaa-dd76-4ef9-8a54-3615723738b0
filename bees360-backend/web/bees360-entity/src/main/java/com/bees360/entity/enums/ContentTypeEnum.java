package com.bees360.entity.enums;

/**
 * the enum for content type of Comment
 * <AUTHOR>
 * @contributors
 * @date 2017/12/20 9:46:14
 */
public enum ContentTypeEnum implements BaseCodeEnum{
	TEXT(0, "Text"),
	IMAGE(1, "Image"),
	PDF(2, "PDF");

	private final int code;
	private final String display;
	ContentTypeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}
	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

}
