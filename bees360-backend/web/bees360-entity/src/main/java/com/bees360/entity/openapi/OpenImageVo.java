package com.bees360.entity.openapi;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import lombok.Data;

@Data
public class OpenImageVo {

    /**
     * @see ProjectImage#getImageId()
     */
    private String id;
    /**
     * @see FileSourceTypeEnum#getValue()
     */
    private String source;
    /**
     * @see ProjectImage#getOrientation()
     */
    private String direction;
    /**
     * @see ImageTypeEnum#getDisplay()
     */
    private String type;
}
