package com.bees360.entity.enums;

public enum CustomizedItemTypeEnum implements BaseCodeEnum{
	ROOF_AGE(1, "root age"),
	ROOF_MATERIAL(2, "roof material"),
	DAMAGE_SEVERITY(3, "damage severity"),
	INSPECTION_DESCRIPTION(4, "description");

	private final int code;
	private final String display;

	CustomizedItemTypeEnum(int code, String display){
		this.code = code;
		this.display = display;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

}
