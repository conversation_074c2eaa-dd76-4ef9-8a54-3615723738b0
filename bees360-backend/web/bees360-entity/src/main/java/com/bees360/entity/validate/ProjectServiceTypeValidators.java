package com.bees360.entity.validate;

import com.bees360.entity.enums.ProjectServiceTypeEnum;

/**
 * <AUTHOR>
 */
public interface ProjectServiceTypeValidators {

    public static class ProjectServiceTypeIntegerValidator extends ProjectServiceTypeValidator<Integer> {

        @Override
        protected ProjectServiceTypeEnum toType(Integer value) {
            return value == null ? null : ProjectServiceTypeEnum.getEnum(value);
        }
    }

    public static class ProjectServiceTypeStringValidator extends ProjectServiceTypeValidator<String> {

        @Override
        protected ProjectServiceTypeEnum toType(String value) {
            return value == null ? null : ProjectServiceTypeEnum.getEnumByValue(value);
        }
    }

    public static class ProjectServiceTypeEnumValidator extends ProjectServiceTypeValidator<ProjectServiceTypeEnum> {

        @Override
        protected ProjectServiceTypeEnum toType(ProjectServiceTypeEnum value) {
            return value;
        }
    }
}
