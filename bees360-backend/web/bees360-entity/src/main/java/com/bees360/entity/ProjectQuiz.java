package com.bees360.entity;

import com.bees360.entity.dto.ProjectQuizDto;
import com.bees360.entity.enums.QuizTypeEnum;
import lombok.Data;

import java.util.Optional;

/**
 * ProjectQuiz表 存储某项目关联的问卷题的答案
 * <AUTHOR>
 * @since 2020/4/10 12:30 PM
 **/
@Data
public class ProjectQuiz {
    private long projectQuizId;

    private long quizId;

    private long projectId;

    /**
     * @see com.bees360.entity.enums.ClaimTypeEnum
     */
    private int claimType;

    private long companyId;

    private long createTime;

    private String[] answers;

    private String answer;

    public void setAnswers(QuizTypeEnum type, String[] answers, String comment) {
        this.answers = answers;
        this.answer = genAnswer(type, answers, comment);
    }

    public static String genAnswer(QuizTypeEnum type, String[] answers, String comment) {
        StringBuilder sb = new StringBuilder();
        switch (type) {
            case BOOLEAN_NO_BLANKS:
                sb.append(String.join(ProjectQuizDto.ANSWER_SPLITTER, answers));
                Optional.ofNullable(comment)
                    .ifPresent(e -> sb.append(ProjectQuizDto.ANSWER_SPLITTER_COMMENT).append(e));
                break;
            default:
                sb.append(String.join(ProjectQuizDto.ANSWER_SPLITTER, answers));
        }
        return sb.toString();
    }
}
