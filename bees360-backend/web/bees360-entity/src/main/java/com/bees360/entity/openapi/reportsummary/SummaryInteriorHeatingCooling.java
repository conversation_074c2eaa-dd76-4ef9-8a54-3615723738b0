package com.bees360.entity.openapi.reportsummary;

import lombok.Data;

import java.util.List;

@Data
public class SummaryInteriorHeatingCooling {

    /** Whether the property has space heater. */
    Boolean hasSpaceHeater;

    /** Whether the property has wood stove. */
    Boolean hasWoodStove;

    /** Whether the HVAC was updated. */
    Boolean isUpdated;

    /** The year in which the HVAC was updated. */
    Integer yearUpdated;

    /** Indicates the age and update status of the heating and cooling system in the property. */
    String systemUpdateStatus;
}
