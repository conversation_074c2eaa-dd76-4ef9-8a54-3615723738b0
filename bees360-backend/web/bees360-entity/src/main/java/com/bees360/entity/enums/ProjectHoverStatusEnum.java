package com.bees360.entity.enums;

import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/07/25 16:47
 */
public enum ProjectHoverStatusEnum {

    NONE(0, "None"),
    ORDERED(1, "Ordered"),
    PROCESSING(2, "Processing"),
    NOT_FOUND(3, "Not Found"),
    FAILED(4, "Failed"),
    UPLOADED(5, "Uploaded To Symbility"),
    UPLOADED_TO_XACT(6, "Uploaded to Xact"),
    COMPLETED(7, "Completed"),
    ;

    private final Integer code;
    private final String display;

    ProjectHoverStatusEnum(Integer code, String display) {
        this.code = code;
        this.display = display;
    }

    public static ProjectHoverStatusEnum getEnum(Integer code){
        return Stream.of(ProjectHoverStatusEnum.values())
            .filter(o -> Objects.equals(code, o.getCode()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("ProjectHoverStatusEnum is null, value:" + code));
    }


    public Integer getCode() {
        return code;
    }

    public String getDisplay() {
        return display;
    }
}
