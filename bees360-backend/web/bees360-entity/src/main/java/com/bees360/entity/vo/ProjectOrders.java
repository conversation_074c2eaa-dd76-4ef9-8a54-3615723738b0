package com.bees360.entity.vo;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ProjectOrders implements Iterable<ProjectOrder> {
	private List<ProjectOrder> items;
	private int discountType;
	// the sum of all subtotalPrice in items
	private double subtotalPrice;
	// the sum of all discount in items
	private double totalDiscount;
	// the sum of all tax in items
	// or subtotalPrice * totalTaxRate
	private double totalTax;
	private List<TaxLabel> taxLabels;
	// totalPrice = subtotalPrice + totalTax
	private double totalPrice;

	@Override
	public Iterator<ProjectOrder> iterator() {
		return items == null? new ArrayList<ProjectOrder>().iterator(): items.iterator();
	}

	//** getter and setter **//
	public List<ProjectOrder> getItems() {
		return items;
	}
	public void setItems(List<ProjectOrder> items) {
		this.items = items;
	}
	public int getDiscountType() {
		return discountType;
	}
	public void setDiscountType(int discountType) {
		this.discountType = discountType;
	}
	public double getSubtotalPrice() {
		return subtotalPrice;
	}
	public void setSubtotalPrice(double subtotalPrice) {
		this.subtotalPrice = subtotalPrice;
	}
	public double getTotalDiscount() {
		return totalDiscount;
	}
	public void setTotalDiscount(double totalDiscount) {
		this.totalDiscount = totalDiscount;
	}
	public double getTotalTax() {
		return totalTax;
	}
	public void setTotalTax(double totalTax) {
		this.totalTax = totalTax;
	}
	public List<TaxLabel> getTaxLabels() {
		return taxLabels;
	}

	public void setTaxLabels(List<TaxLabel> taxLabels) {
		this.taxLabels = taxLabels;
	}

	public double getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(double totalPrice) {
		this.totalPrice = totalPrice;
	}
}
