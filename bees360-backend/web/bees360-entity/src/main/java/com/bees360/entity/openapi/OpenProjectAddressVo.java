package com.bees360.entity.openapi;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
public class OpenProjectAddressVo {

    private String country = "US";
    @NotBlank
    private String state;
    @NotBlank
    private String city;
    @NotBlank
    private String zipcode;
    @NotBlank
    private String streetAddress;

    public String getCountry() {
        return country == null ? "US" : country;
    }
}
