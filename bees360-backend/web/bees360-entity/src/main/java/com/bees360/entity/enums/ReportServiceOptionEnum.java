package com.bees360.entity.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

public enum ReportServiceOptionEnum implements BaseCodeEnum {
	MEASUREMENT_AND_DAMAGE_REPORT(1, "Premium Roof Measurement Report And Damage Report",
			ServiceFeeTypeEnum.FULL_DAMAGE_MEASUREMENT_REPORT,
			Arrays.asList(ReportTypeEnum.PREMIUM_ROOF_MEASUREMENT_REPORT,
					ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT)),

	MEASUREMENT_REPORT(2, "Premium Roof Measurement Report",
			ServiceFeeTypeEnum.PREMIUM_MEASUREMENT_REPORT,
			Arrays.asList(ReportTypeEnum.PREMIUM_ROOF_MEASUREMENT_REPORT)),

	QUICK_DAMAGE_REPORT(3,"Quick Damage Report",
			ServiceFeeTypeEnum.PRELIMINARY_DAMAGE_ASSESSMENT_REPORT,
			Arrays.asList(ReportTypeEnum.QUICK_DAMAGE_ASSESSMENT_REPORT))
	;

    private final int code;
    private final String display;
    private final ServiceFeeTypeEnum serviceFeeType;
    private final List<ReportTypeEnum> reports;

    private static final Map<Integer, ReportServiceOptionEnum> reportServiceMap;
    static {
    	Map<Integer, ReportServiceOptionEnum> map = new HashMap<Integer, ReportServiceOptionEnum>();
    	for(ReportServiceOptionEnum option: ReportServiceOptionEnum.values()) {
    		map.put(option.getCode(), option);
		}
    	reportServiceMap = Collections.unmodifiableMap(map);
    }

    ReportServiceOptionEnum(int code, String display, ServiceFeeTypeEnum serviceFeeType,
    		List<ReportTypeEnum> reports) {
    	this.code = code;
    	this.display = display;
    	this.serviceFeeType = serviceFeeType;
    	this.reports = Collections.unmodifiableList(reports == null? Arrays.asList(): reports);
    }

    @Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public List<ReportTypeEnum> getReports() {
		return reports;
	}

	public ServiceFeeTypeEnum getServiceFeeType() {
		return serviceFeeType;
	}

	public static ReportServiceOptionEnum getEnum(int code) {
		return reportServiceMap.get(code);
	}

	public static ReportServiceOptionEnum getEnumByServiceFeeType(int serviceFeeType) {
		for(Entry<Integer, ReportServiceOptionEnum> entry : reportServiceMap.entrySet()) {
			if(entry.getValue().getServiceFeeType().getServiceFeeTypeId() == serviceFeeType) {
				return entry.getValue();
			}
		}
		return null;
	}


	public static boolean exist(int code) {
		return reportServiceMap.containsKey(code);
	}

	private static void show() {
		System.out.println("|name|code|reports|");
		System.out.println("|----|----|-------|");
		for(ReportServiceOptionEnum e: ReportServiceOptionEnum.values()) {
			List<ReportTypeEnum> rs = e.getReports();
			String[] reports = new String[rs.size()];
			for(int i = 0; i < rs.size(); i ++) {
				reports[i] = rs.get(i).getDisplay();
			}
			System.out.println("|" + e.getDisplay() + "|" + e.getCode() + "|" + Arrays.toString(reports) + "|");
		}
	}

	public static void main(String[] args) {
		show();
	}
}
