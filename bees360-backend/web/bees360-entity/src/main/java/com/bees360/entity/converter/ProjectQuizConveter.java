package com.bees360.entity.converter;

import com.bees360.entity.Quiz;
import com.bees360.entity.dto.ProjectQuizDto;
import com.bees360.entity.firebase.TaskQuizRemoteConfig;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/4/10 4:30 PM
 **/
public class ProjectQuizConveter {
    public static ProjectQuizDto projectQuizDto(long projectId, Quiz quiz) {
        if (quiz == null) {
            return null;
        }
        ProjectQuizDto dto = new ProjectQuizDto();
        dto.setProjectId(projectId);
        dto.setSubject(quiz.getSubject());
        dto.setQuizId(quiz.getQuizId());
        dto.setType(quiz.getType());
        dto.setSequence(quiz.getSequence());
        dto.setChoices(quiz.getChoicesArray());
        return dto;
    }

    public static List<ProjectQuizDto> projectQuizDtoList(long project, List<Quiz> quizzes) {
        if (CollectionUtils.isEmpty(quizzes)) {
            return new ArrayList<>();
        }
        List<ProjectQuizDto> dtos = new ArrayList<>(quizzes.size());
        quizzes.forEach(quiz -> dtos.add(projectQuizDto(project, quiz)));
        return dtos;
    }

    public static ProjectQuizDto quizConfig2ProjectQuiz(long projectId, TaskQuizRemoteConfig quiz) {
        if (quiz == null) {
            return null;
        }
        ProjectQuizDto dto = new ProjectQuizDto();
        dto.setProjectId(projectId);
        dto.setSubject(quiz.getText());
        dto.setQuizId(quiz.getQuizId());
        dto.setType(quiz.getType());
        dto.setChoices(quiz.getChoices().toArray(new String[0]));
        return dto;
    }

}
