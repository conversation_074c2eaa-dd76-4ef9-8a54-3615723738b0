package com.bees360.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

import org.springframework.util.ObjectUtils;
import java.sql.Timestamp;
import java.util.Objects;
import java.util.stream.Stream;

@Data
@NoArgsConstructor
public class ProjectScore {

    private Long id;
    private Long projectId;
    private BigDecimal score;
    private Timestamp createAt;
    private Timestamp updateAt;


    public ProjectScore(Long projectId, String overallCondition){
        this.projectId = projectId;
        initScore(overallCondition);
    }

    private void initScore(String overallCondition){
        OverallConditionRiskScore riskScore = OverallConditionRiskScore.riskScore(overallCondition);
        if (riskScore != null){
            this.score = riskScore.score;
            return;
        }
        this.score = new BigDecimal(0);
    }

    enum OverallConditionRiskScore {

        Excellent("Excellent", 1D),
        Good("Good", 2D),
        Average("Average", 3D),
        Fair("Fair", 4D),
        Poor("Poor", 5D)
        ;

        private String overallCondition;
        private BigDecimal score;

        OverallConditionRiskScore(String overallCondition, Double score){
            this.overallCondition = overallCondition;
            this.score = new BigDecimal(score);
        }

        static OverallConditionRiskScore riskScore(String overallCondition){
            if (ObjectUtils.isEmpty(overallCondition)){
                return null;
            }
            OverallConditionRiskScore[] riskScores = OverallConditionRiskScore.values();
            return Stream.of(riskScores).filter(score -> Objects.equals(score.overallCondition.toLowerCase(), overallCondition.toLowerCase()))
                .findFirst().orElse(null);
        }
    }
}
