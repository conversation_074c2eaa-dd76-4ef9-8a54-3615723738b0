package com.bees360.entity.enums.productandpayment;

import com.bees360.entity.enums.BaseCodeEnum;

public enum PaymentOperationTypeEnum implements BaseCodeEnum {
	RECHARGE(1, "Recharge"),
	EXPEND(2, "Expend")
	;
	private final int code;
	private final String display;

	PaymentOperationTypeEnum(int code, String display) {
		this.code = code;
		this.display = display;
	}

	public int getCode() {
		return code;
	}
	public String getDisplay() {
		return display;
	}
}
