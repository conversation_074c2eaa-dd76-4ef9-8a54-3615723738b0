package com.bees360.entity.dto;

import lombok.Data;

@Data
public class ImageTypeRelationModifierDto {

    private String imageId;

    /**
     * @see com.bees360.entity.enums.FileSourceTypeEnum
     */
    private int fileSourceType;

    /**
     * @see com.bees360.entity.enums.ImageTypeEnum
     */
    private Integer imageType;

    /**
     * @see com.bees360.entity.enums.ImagePartialViewTypeEnum
     */
    private Integer partialType;

    /**
     * @see com.bees360.entity.enums.OrientationEnum
     */
    private Integer orientation;

    public ImageTypeRelationModifierDto(String imageId, int fileSourceType) {
        super();
        this.imageId = imageId;
        this.fileSourceType = fileSourceType;
    }
}
