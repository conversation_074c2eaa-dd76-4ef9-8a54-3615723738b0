package com.bees360.entity.vo;

import java.util.List;

public class ProjectOrder {
	private int productId;
	private int productType;
	private int internalType;
	private String productName;
	private double price;
	private double discount;
	private List<DiscountLabel> discountLabels;
	// subtotalPrice * taxRate
	private double tax;
	private double taxRate;
	// subtotalPrice = price - discount
	private double subtotalPrice;
	// totalPrice = subtotalPrice + tax = price - discount + tax
	private double totalPrice;

	// ** getter and setter **//
	public int getProductId() {
		return productId;
	}
	public void setProductId(int productId) {
		this.productId = productId;
	}
	public int getProductType() {
		return productType;
	}
	public void setProductType(int productType) {
		this.productType = productType;
	}
	public int getInternalType() {
		return internalType;
	}
	public void setInternalType(int internalType) {
		this.internalType = internalType;
	}
	public String getProductName() {
		return productName;
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}
	public double getPrice() {
		return price;
	}
	public void setPrice(double price) {
		this.price = price;
	}
	public double getDiscount() {
		return discount;
	}
	public void setDiscount(double discount) {
		this.discount = discount;
	}
	public List<DiscountLabel> getDiscountLabels() {
		return discountLabels;
	}
	public void setDiscountLabels(List<DiscountLabel> discountLabels) {
		this.discountLabels = discountLabels;
	}
	public double getTax() {
		return tax;
	}
	public void setTax(double tax) {
		this.tax = tax;
	}
	public double getTaxRate() {
		return taxRate;
	}
	public void setTaxRate(double taxRate) {
		this.taxRate = taxRate;
	}
	public double getSubtotalPrice() {
		return subtotalPrice;
	}
	public void setSubtotalPrice(double subtotalPrice) {
		this.subtotalPrice = subtotalPrice;
	}
	public double getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(double totalPrice) {
		this.totalPrice = totalPrice;
	}
}
