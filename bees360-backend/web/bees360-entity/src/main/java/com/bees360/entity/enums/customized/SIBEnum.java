package com.bees360.entity.enums.customized;

import org.apache.commons.lang3.StringUtils;

// SIB_DAMAGE_REPORT_COLUMN
public enum SIBEnum {

	SIB_UNDERWRITER(1, "underwriter", "Underwriter"),
	SIB_AGENCY(2, "agency", "Agency"),
	SIB_CASE_NUMBER(3, "caseNumber", "Case Number"),
	SIB_CASE_TYPE(4, "caseType", "Case Type"),
	SIB_CUSTOMER_NAME(5, "customerName", "Customer Name"),
	SIB_INSURED(6, "insured", "Insured")
	;

	private final int code;
	private final String display;
	private final String name;

	private SIBEnum(int code, String display, String name) {
		this.code = code;
		this.display = display;
		this.name = name;
	}

	public static SIBEnum getEnum(int code) {
		for(SIBEnum templateEnum : SIBEnum.values()) {
			if(code == templateEnum.getCode()) {
				return templateEnum;
			}
		}
		return null;
	}

	public static SIBEnum getEnum(String display) {
		for(SIBEnum templateEnum : SIBEnum.values()) {
			if(StringUtils.equals(display, templateEnum.getDisplay())) {
				return templateEnum;
			}
		}
		return null;
	}

	public int getCode() {
		return code;
	}

	public String getDisplay() {
		return display;
	}

	public String getName() {
		return name;
	}

}
