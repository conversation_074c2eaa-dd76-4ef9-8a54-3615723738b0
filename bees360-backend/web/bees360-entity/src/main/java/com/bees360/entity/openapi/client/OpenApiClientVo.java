package com.bees360.entity.openapi.client;

import lombok.Data;

import java.util.List;

@Data
public class OpenApiClientVo {

    private String clientName;
    private String clientId;
    private String clientSecret;
    private List<String> scopes;

    public static OpenApiClientVo fromClient(OpenApiClient client){
        final OpenApiClientVo clientVo = new OpenApiClientVo();
        clientVo.setClientName(client.getClientName());
        clientVo.setClientId(client.getClientId());
        clientVo.setClientSecret(client.getClientSecret());
        clientVo.setScopes(client.getScopeList());

        return clientVo;
    }
}
