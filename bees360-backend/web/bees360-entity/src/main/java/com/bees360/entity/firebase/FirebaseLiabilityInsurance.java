package com.bees360.entity.firebase;

import com.google.cloud.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 飞手保单的上传和审核记录
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FirebaseLiabilityInsurance {

    /**
     * 保单在S3的地址
     */
    private String liabilityInsurance;

    /**
     * 保单的预览图在S3的地址
     */
    private String liabilityInsurancePreview;

    /**
     * 保金 ($million)
     */
    private Double amount;

    /**
     * 用户最后一次修改LiabilityInsurance时间
     */
    private Timestamp lastModifyTime;

    /**
     * 管理员最后一次的留言
     */
    private String message;

    /**
     * 最后一次审核的管理员ID
     */
    private String managerId;

    /**
     * 管理员审核完成时间
     */
    private Timestamp completeTime;

    /**
     * 审核状态
     * Pending, Submitted，Rejected, Approved
     */
    private String status;

}
