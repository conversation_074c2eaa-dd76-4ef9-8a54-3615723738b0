package com.bees360.entity.openapi;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/01/19 10:43
 */
@Data
@NoArgsConstructor
public class OpenProjectCreateVo extends OpenProjectBaseVo {

    private Boolean allowDuplication = true;

    public boolean getAllowDuplication() {
        return Optional.ofNullable(allowDuplication).orElse(true);
    }
}
