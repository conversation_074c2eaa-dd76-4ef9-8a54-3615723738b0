package com.bees360.entity.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class UserPilotVo {
	private long userId;
	private String firstName;
	private String lastName;
	private String phone;
	private String email;
	private String avatar;

	private String zipCode;
	private String address;
	private String city;
	private String state;
	private String country;
	private double lng;
	private double lat;

	private String licenseNumber;

	/**
	 * 保单金额
	 */
	private BigDecimal insuranceAmount;

	private double travelRadius;
}
