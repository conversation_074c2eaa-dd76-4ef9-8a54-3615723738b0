package com.bees360.entity.validate;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import com.bees360.entity.enums.ProjectTypeEnum;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

/**
 * <AUTHOR> Yang
 */
public abstract class ProjectTypeValidator<T> implements ConstraintValidator<ProjectType, T> {

    private Set<ProjectTypeEnum> types;

    @Override
    public void initialize(ProjectType constraintAnnotation) {
        types = Sets.newHashSet(constraintAnnotation.types());
    }

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        if ((value instanceof String string) && StringUtils.isBlank(string)) {
            return true;
        }
        ProjectTypeEnum type = toType(value);
        return type != null && (allowAll() || types.contains(type));
    }

    private boolean allowAll() {
        return types.isEmpty();
    }

    protected abstract ProjectTypeEnum toType(T value);
}
