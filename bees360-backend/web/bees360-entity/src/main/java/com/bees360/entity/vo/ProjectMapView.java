package com.bees360.entity.vo;

import com.bees360.entity.label.ProjectLabel;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ProjectMapView {
	private long projectId;
	private long companyId;
	private double lat;
	private double lng;
	private boolean hasPilot;
	private boolean hasAdjuster;
	private int claimType;
	/**
	 *  Same as com.bees360.flyzone.FlyZoneType::getCode()
	 */
	private int flyZoneType;

	private String batchNo;

	private Long insuranceCompany;

    private Integer serviceType;

    private String insuredBy;

    private boolean hasContacted;
    /**
     * 飞否等待飞手确认
     */
    private Boolean isPendingAcceptance;

    private List<ProjectLabel> projectLabels;

    @JsonIgnore
    private int batchStatus;

    /* getter and setter */
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public long getCompanyId() {
		return companyId;
	}
	public void setCompanyId(long companyId) {
		this.companyId = companyId;
	}
	public double getLat() {
		return lat;
	}
	public void setLat(double lat) {
		this.lat = lat;
	}
	public double getLng() {
		return lng;
	}
	public void setLng(double lng) {
		this.lng = lng;
	}
	public boolean isHasPilot() {
		return hasPilot;
	}
	public void setHasPilot(boolean hasPilot) {
		this.hasPilot = hasPilot;
	}
	public boolean isHasAdjuster() {
		return hasAdjuster;
	}
	public void setHasAdjuster(boolean hasAdjuster) {
		this.hasAdjuster = hasAdjuster;
	}

	public int getFlyZoneType() {
		return flyZoneType;
	}

	public void setFlyZoneType(int flyZoneType) {
		this.flyZoneType = flyZoneType;
	}

    public void setClaimType(Integer claimType) {
	    this.claimType = claimType;
    }

    public int getClaimType() {
	    return this.claimType;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Integer getServiceType() {
        return serviceType;
    }

    public void setServiceType(Integer serviceType) {
        this.serviceType = serviceType;
    }

    public String getInsuredBy() {
        return insuredBy;
    }

    public void setInsuredBy(String insuredBy) {
        this.insuredBy = insuredBy;
    }

    public Long getInsuranceCompany() {
        return insuranceCompany;
    }

    public void setInsuranceCompany(Long insuranceCompany) {
        this.insuranceCompany = insuranceCompany;
    }

    public boolean isHasContacted() {
        return hasContacted;
    }

    public void setHasContacted(boolean hasContacted) {
        this.hasContacted = hasContacted;
    }

    public Boolean getPendingAcceptance() {
        return isPendingAcceptance;
    }

    public void setPendingAcceptance(Boolean pendingAcceptance) {
        isPendingAcceptance = pendingAcceptance;
    }

    public int getBatchStatus() {
        return batchStatus;
    }

    public void setBatchStatus(int batchStatus) {
        this.batchStatus = batchStatus;
    }

    public List<ProjectLabel> getProjectLabels() {
        return projectLabels;
    }

    public void setProjectLabels(List<ProjectLabel> projectLabels) {
        this.projectLabels = projectLabels;
    }

    @Override
    public String toString() {
        return "ProjectMapView{" +
            "projectId=" + projectId +
            ", companyId=" + companyId +
            ", lat=" + lat +
            ", lng=" + lng +
            ", hasPilot=" + hasPilot +
            ", hasAdjuster=" + hasAdjuster +
            ", claimType=" + claimType +
            ", flyZoneType=" + flyZoneType +
            ", batchNo='" + batchNo + '\'' +
            ", insuranceCompany=" + insuranceCompany +
            ", serviceType=" + serviceType +
            ", insuredBy='" + insuredBy + '\'' +
            ", hasContacted=" + hasContacted +
            '}';
    }
}
