package com.bees360.job.registry;

import com.bees360.codec.SerializableObject;
import lombok.Data;
import lombok.ToString;

import jakarta.annotation.Nullable;

@JobPayload
@ToString
@SerializableObject
@Data
public class FirebaseMissionCompletedStuck {
    private String pilotId;
    private String missionPath;
    private String projectId;

    @Nullable
    public String getMissionId() {
        if (missionPath == null){
            return null;
        }

       return missionPath.substring(missionPath.lastIndexOf('/') + 1);
    }
}
