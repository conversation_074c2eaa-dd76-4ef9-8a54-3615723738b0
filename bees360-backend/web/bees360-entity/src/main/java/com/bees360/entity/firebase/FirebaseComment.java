package com.bees360.entity.firebase;

import com.alibaba.excel.util.StringUtils;
import com.bees360.activity.Comment;
import com.bees360.entity.AttachmentEntity;
import lombok.Getter;
import org.jsoup.internal.StringUtil;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

public class FirebaseComment implements Comment {
    @Getter private String scopeType;
    @Getter private Long bundleId;
    private String projectId; // 评论所属项目
    @Getter private String content; // 活动动作类型 类型分为CREATE, DELETE 和 CHANGE,
    private long createdAt; // 活动创建时间，时间戳
    @Getter private String createdBy; // 活动的触发人，默认用户是beespilot
    @Getter private String source; // 活动来源，默认值
    @Getter private String contentType = "text/plain"; // 内容类型
    private List<AttachmentEntity> attachment;
    @Override
    public Iterable<? extends Attachment> getAttachment() {
        return Optional.ofNullable(attachment).orElse(Collections.emptyList());
    }

    @Override
    public String getRichText() {
        return StringUtils.EMPTY;
    }

    @Override
    public String getTopic() {
        return StringUtils.EMPTY;
    }

    @Override
    public String getId() {
        return null;
    }

    @Override
    public String getReplyTo() {
        return null;
    }

    public Instant getCreatedAt() {
        return Instant.ofEpochMilli(createdAt);
    }

    @Override
    public Instant getUpdatedAt() {
        return getCreatedAt();
    }

    public Long getProjectId() {
        return Long.parseLong(projectId);
    }
}
