package com.bees360.entity.stat.dto;

import lombok.Data;

import java.math.BigDecimal;

import org.springframework.util.ObjectUtils;
import java.time.LocalDate;

@Data
public class ProjectListDataDto {

    private Long projectId;
    private Long insuranceCompany;
    private String insuranceCompanyName;
    private Integer serviceType;
    private Integer projectStatus;
    private String policyNumber;
    private String claimNumber;
    private String address;
    private String city;
    private String state;
    private String country;
    private String zipCode;
    private String assertOwnerName;
    private String assertOwnerPhone;
    private Integer daysOld;
    private Long dateInspected;
    private Long dateReturned;
    private LocalDate policyEffectiveDate;
    private Long createTime;
    private BigDecimal score;
    private Integer newDaysOld;


    public String getPolicyNumber(){
        if (ObjectUtils.isEmpty(policyNumber) && !ObjectUtils.isEmpty(claimNumber)){
            return claimNumber;
        }
        return policyNumber;
    }
}
