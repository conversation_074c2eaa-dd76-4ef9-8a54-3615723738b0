package com.bees360.entity.openapi;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectImage;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/12/25 09:19
 */
@Data
@NoArgsConstructor
public class OpenProjectVo extends OpenProjectBaseVo {
    private long id;

    @Data
    @NoArgsConstructor
    public static class OpenProjectVoWrapper {
        private OpenProjectVo project;

        public OpenProjectVoWrapper(OpenProjectVo project) {
            this.project = project;
        }
    }

    @Data
    @NoArgsConstructor
    public static class OpenProjectVoListWrapper<ProjectItem> {
        private List<ProjectItem> project;

        public OpenProjectVoListWrapper(List<ProjectItem> project) {
            this.project = project;
        }

        public OpenProjectVoListWrapper(ProjectItem... project) {
            this(new ArrayList<>(Arrays.asList(project)));
        }
    }

    @Data
    @NoArgsConstructor
    public static class OpenProjectReportVo {
        /**
         * project的id
         * @see Project#getProjectId()
         */
        private long id;
        /**
         * 报告列表
         */
        private List<OpenReportIdTypeVo> report;

        public OpenProjectReportVo(long id, List<OpenReportIdTypeVo> report) {
            this.id = id;
            this.report = report;
        }
    }

    @Data
    @NoArgsConstructor
    public static class OpenProjectImageVo {
        /**
         * project的id
         * @see Project#getProjectId()
         */
        private long id;
        private List<OpenImageVo> image;

        public OpenProjectImageVo(long id, List<OpenImageVo> image){
            this.id = id;
            this.image = image;
        }
    }
}
