package com.bees360.entity.dto;

import java.util.List;

import com.bees360.entity.ProjectImage;
/**
 *
 * <AUTHOR>
 * @date 2017.11.13
 *
 */
// <EMAIL>: Is the design reasonable?
public class ProjectImageUploadResponseDto {
	private List<ProjectImage> successImages;
	private List<ProjectImage> failImages;

	public List<ProjectImage> getSuccessImages() {
		return successImages;
	}
	public void setSuccessImages(List<ProjectImage> successImages) {
		this.successImages = successImages;
	}
	public List<ProjectImage> getFailImages() {
		return failImages;
	}
	public void setFailImages(List<ProjectImage> failImages) {
		this.failImages = failImages;
	}
}
