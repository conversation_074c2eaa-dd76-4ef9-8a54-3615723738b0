package com.bees360.entity.normalize;

import com.bees360.entity.vo.PhoneVo;
import com.bees360.web.core.validation.PhoneValidator;
import static com.bees360.entity.util.FunctionUtil.acceptNotNull;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class UserDataNormalizer {

    private static final PhoneValidator phoneValidator = PhoneValidator.getDefaultInstance();

    public static void normalize(PhoneVo phoneVo) {
        if (Objects.isNull(phoneVo)) {
            return;
        }
        acceptNotNull(phoneVo.getPhone(), phoneValidator::formatPhoneOrEmpty, phoneVo::setPhone);
    }
}
