package com.bees360.entity;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/08/22 18:58
 */
@Data
@Builder
public class DailyBatchProjectModel {

    private long pilotId;

    private String pilotName;

    /**
     * batch关联的project数量
     */
    private Integer projectCount;

    /**
     * batch相关信息
     */
    private BigDecimal basePay;

    private BigDecimal extraPay;

    private BigDecimal totalPay;

    private String playPaymentDate;

    private String dueDate;

    /**
     * 所有这些项目是否处于image uploaded及之后的状态 ，用Y/N 标识
     */
    private String taskCompleted;

    private String batchNo;

}
