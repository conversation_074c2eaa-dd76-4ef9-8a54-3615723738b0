package com.bees360.entity.stat.rule;

import com.bees360.entity.enums.ProjectServiceTypeEnum;

import java.util.Objects;

public class CompanyStatRuleConfig {

    private static final String SWYFFT_NAME = "Swyfft Underwriting";

    private static final CompanyStatRule SWYFFT_RULE = new CompanyStatRule(
        new RangeRule<>(10, 21),
        new RangeRule<>(21, Integer.MAX_VALUE));

    private static final CompanyStatRule DEFAULT_RULE = new CompanyStatRule(
        new RangeRule<>(10, 14),
        new RangeRule<>(14, Integer.MAX_VALUE));


    public static CompanyStatRule getStatRule(String company, Integer serviceType){
        if (Objects.equals(SWYFFT_NAME, company) && ProjectServiceTypeEnum.getEnum(serviceType) == ProjectServiceTypeEnum.FOUR_POINT_UNDERWRITING){
            return SWYFFT_RULE;
        }
        return DEFAULT_RULE;
    }
}
