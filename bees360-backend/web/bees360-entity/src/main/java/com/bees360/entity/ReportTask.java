package com.bees360.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ReportTask {
	private long id;
	private long userId;
	private long projectId;
	private int reportType;
	private boolean deleted;

	private long createdTime;
	private long updatedTime;

	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public long getUserId() {
		return userId;
	}
	public void setUserId(long userId) {
		this.userId = userId;
	}
	public long getProjectId() {
		return projectId;
	}
	public void setProjectId(long projectId) {
		this.projectId = projectId;
	}
	public int getReportType() {
		return reportType;
	}
	public void setReportType(int reportType) {
		this.reportType = reportType;
	}
	@JsonProperty("isDeleted")
	public boolean isDeleted() {
		return deleted;
	}
	public void setDeleted(boolean deleted) {
		this.deleted = deleted;
	}
	public long getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}
	public long getUpdatedTime() {
		return updatedTime;
	}
	public void setUpdatedTime(long updatedTime) {
		this.updatedTime = updatedTime;
	}

	@Override
	public String toString() {
		return "ReportTask [id=" + id + ", userId=" + userId + ", projectId=" + projectId
				+ ", reportType=" + reportType + ", deleted=" + deleted + ", createdTime=" + createdTime
				+ ", updatedTime=" + updatedTime + "]";
	}
}
