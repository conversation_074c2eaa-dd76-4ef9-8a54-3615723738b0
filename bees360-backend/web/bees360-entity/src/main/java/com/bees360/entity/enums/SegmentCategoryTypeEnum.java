package com.bees360.entity.enums;

import com.bees360.entity.dto.AppSegmentDto;

public enum SegmentCategoryTypeEnum implements BaseCodeEnum{

	ROOF(10, "Roof", 10, 111000),
	ROOF_RIDGE_CAP(15, "Ridge cap", 10, 180960),
	ROOF_DRIP_EDGE(20, "Drip edge", 10, 111000),
	ROOF_VALLEY(25, "Valley", 10, 111000),
	ROOF_ATTIC_VENTS(45, "Attic vents", 10, 111000),
	ROOF_RIDGE_VENTS(50, "Ridge Vents", 10, 111000),
	ROOF_FLASHING(55, "Flashing", 10, 111000),
	ROOF_VENTS(60, "Roof Vents", 10, 111000),
	ROOF_VENTILATOR(65, "Ventilator", 10, 111000),
	ROOF_SLOPE(70, "Roof Slope", 10, 111000),

	ELEVATION(100, "Elevation", 100, 111000),
	ELEVATION_WINDOW_SCREEN(105, "Window screen", 30000, 211690),
	ELEVATION_WINDOW_REGLAZE(110, "Window reglaze", 30010, 211500),
	ELEVATION_WINDOW_BEADING(115, "Window beading", 30020, 211480),
	ELEVATION_GUTTERS(120, "Gutters", 30040, 185400),
	ELEVATION_DOWNSPOUTS(120, "Downspouts", 30050, 185400),
	ELEVATION_A_C_FINS(125, "A/C fins", 30060, 135290),
	ELEVATION_FASCIA(130, "Fascia", 30070, 186050),
	ELEVATION_WINDOWS(135, "Windows", 30030, 209330),
	ELEVATION_SIDING(140, "Siding", 30080, 184800),
	ELEVATION_SOFFIT(145, "Soffit", 30090, 186010),

	FENCE_OVERVIEW(160, "Fence", 81690, 186010),

	UNMODIFIABLE(200, "Unmodifiable", 200, 101290),

	UNDERWRITING(300, "Unmodifiable", 300, 101290);

	private final int code;
	private final String display;
	private final long segmentType;
	private final long descriptionId;

	SegmentCategoryTypeEnum(int code, String display, long segmentType, long descriptionId){
		this.code = code;
		this.display = display;
		this.segmentType = segmentType;
		this.descriptionId = descriptionId;
	}

	public static SegmentCategoryTypeEnum getEnum(int code) {
		for(SegmentCategoryTypeEnum typeEnum : SegmentCategoryTypeEnum.values()) {
			if(code == typeEnum.getCode()) {
				return typeEnum;
			}
		}
		return null;
	}

	public static SegmentCategoryTypeEnum getBySegmentType(long segmentType) {
		for(SegmentCategoryTypeEnum typeEnum : SegmentCategoryTypeEnum.values()) {
			if(segmentType == typeEnum.getSegmentType()) {
				return typeEnum;
			}
		}
		return null;
	}

	/**
	 * get category hot type
	 * @param code
	 * @return
	 */
	public static SegmentCategoryTypeEnum getCategoryType(String code) {
		String[] codes = code.split(AppSegmentDto.SEGMENT_CODE_SPLIT);
		SegmentCategoryTypeEnum typeEnum = SegmentCategoryTypeEnum.getBySegmentType(Long.parseLong(codes[4]));
		if (null != typeEnum) {
			return typeEnum;
		}
		return SegmentCategoryTypeEnum.ELEVATION;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getDisplay() {
		return display;
	}

	public long getSegmentType() {
		return segmentType;
	}

	public long getDescriptionId() {
		return descriptionId;
	}

}
