package com.bees360.entity.vo;

import com.bees360.entity.dto.ImageDetectionDto;

/**
 * <AUTHOR>
 * @date 2019/09/11 20:08
 */
public class AnnotationOperated {

    private ImageDetectionDto damageAnnotations;
    private ImageDetectionDto frameAnnotations;

    public ImageDetectionDto getDamageAnnotations() {
        return damageAnnotations;
    }

    public void setDamageAnnotations(ImageDetectionDto damageAnnotations) {
        this.damageAnnotations = damageAnnotations;
    }

    public ImageDetectionDto getFrameAnnotations() {
        return frameAnnotations;
    }

    public void setFrameAnnotations(ImageDetectionDto frameAnnotations) {
        this.frameAnnotations = frameAnnotations;
    }

    @Override
    public String toString() {
        return "AnnotationOperated{" + "damageAnnotations=" + damageAnnotations + ", frameAnnotations="
            + frameAnnotations + '}';
    }
}
