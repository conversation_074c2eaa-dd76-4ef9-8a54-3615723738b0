package com.bees360.entity.vo;

import com.bees360.entity.enums.DiscountOffTypeEnum;
import com.bees360.entity.enums.productandpayment.GlobalDiscountTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 全局折扣。该类保存的类为可以对所有的报告进行折扣的折扣信息。
 *
 * <AUTHOR>
 *
 */
public class WalletGlobalDiscount {
	/** @see GlobalDiscountTypeEnum#getCode() **/
	private int discountType;
	private String discountName;
	/** 该Discount是否有限 **/
	private boolean isNumberLimited;
	/** @see DiscountOffTypeEnum#getCode() **/
	private int offType;
	private int num;
	/**
	 * 根据 offType 值的不同具有不同的意义。
	 *
	 * {@link DiscountOffTypeEnum#PERCENTAGE_OFF} 按照百分比折扣，25%，则值为 0.25；
	 * {@link DiscountOffTypeEnum#AMOUNT_OFF} 按照总额进行折扣扣除，$12.3，则为12.3。
	 *
	 ***/
	private double value;

	@JsonIgnore
	public boolean isAvariable() {
		return !isNumberLimited || num > 0;
	}

	//** getter and setter **//
	public int getDiscountType() {
		return discountType;
	}
	public void setDiscountType(int discountType) {
		this.discountType = discountType;
	}
	public String getDiscountName() {
		return discountName;
	}
	public void setDiscountName(String discountName) {
		this.discountName = discountName;
	}
	public boolean getIsNumberLimited() {
		return isNumberLimited;
	}
	public void setIsNumberLimited(boolean isNumberLimited) {
		this.isNumberLimited = isNumberLimited;
	}
	public int getOffType() {
		return offType;
	}
	public void setOffType(int offType) {
		this.offType = offType;
	}
	public int getNum() {
		return num;
	}
	public void setNum(int num) {
		this.num = num;
	}
	public double getValue() {
		return value;
	}
	public void setValue(double value) {
		this.value = value;
	}
}
