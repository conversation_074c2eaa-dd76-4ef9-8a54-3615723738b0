package com.bees360.entity.openapi.reportsummary;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;

import java.nio.charset.StandardCharsets;

class OpenApiReportSummaryVoTest {

    @SneakyThrows
    @Test
    void testReportSummaryVoJsonConvert() {
        var objectMapper = new ObjectMapper();
        var json = loadResource("report_summary_full_summary_only.json");
        var summary = objectMapper.readValue(json, ReportSummaryVo.class);
        var summaryJson = objectMapper.writeValueAsString(summary);
        JSONAssert.assertEquals(json, summaryJson, false);
    }

    @SneakyThrows
    @Test
    void testReportSummaryWithNullNewFieldVoJsonConvert() {
        var objectMapper = new ObjectMapper();
        var json = loadResource("report_summary_full_summary_with_null_new_field.json");
        var summary = objectMapper.readValue(json, ReportSummaryVo.class);
        var summaryJson = objectMapper.writeValueAsString(summary);
        var expectedJson = loadResource("report_summary_full_summary_with_null_new_field_expected.json");
        JSONAssert.assertEquals(expectedJson, summaryJson, false);
    }

    @SneakyThrows
    String loadResource(String name) {
        return IOUtils.resourceToString(name, StandardCharsets.UTF_8, this.getClass().getClassLoader());
    }
}
