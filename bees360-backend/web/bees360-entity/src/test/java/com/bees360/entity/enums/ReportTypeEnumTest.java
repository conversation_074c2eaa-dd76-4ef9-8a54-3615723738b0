package com.bees360.entity.enums;

import java.util.Arrays;
import java.util.Set;

import com.google.api.client.util.Sets;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 */
public class ReportTypeEnumTest {

    @Test
    public void codeShouldUnique() {
        // code should be unique
        Set<Integer> codes = Sets.newHashSet();
        // short cut should be unique
        Set<String> shortCuts = Sets.newHashSet();

        Arrays.stream(ReportTypeEnum.values()).forEach(
            t -> {
                assertTrue(codes.add(t.getCode()), "The code " + t.getCode() + " of status " + t + " exists.");
                assertTrue(shortCuts.add(t.getShortCut()), "The shortCut " + t.getShortCut() + " of status " + t + " exists.");
            });
    }
}
