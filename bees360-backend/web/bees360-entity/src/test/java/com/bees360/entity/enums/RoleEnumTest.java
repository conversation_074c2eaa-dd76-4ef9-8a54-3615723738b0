package com.bees360.entity.enums;

import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
public class RoleEnumTest {

    @Test
    public void roleBitMap() {
        List<RoleEnum> roles = Arrays.asList(RoleEnum.ADJUSTER, RoleEnum.COMPANY_ADMIN);
        long roleBitMap = RoleEnum.roleToBitMap(roles);
        List<RoleEnum> rolesAfter = RoleEnum.listRoles(roleBitMap);

        assertEquals(roles, rolesAfter);
    }

}
