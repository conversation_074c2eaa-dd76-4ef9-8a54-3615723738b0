package com.bees360.entity.dto;

import java.util.Arrays;
import java.util.Collection;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ProjectSearchOptionForAppTest {

	private String addressInput;
	private String expectedAddressRegex;

	private ProjectSearchOptionForApp searchOption;

    public void initProjectSearchOptionForAppTest(String addressInput, String expectedAddressRegex) {
		this.addressInput = addressInput;
		this.expectedAddressRegex = expectedAddressRegex;
	}

	@BeforeEach
	public void testUp() {
		searchOption = new ProjectSearchOptionForApp();
	}

	@AfterEach
	public void testDown() {

	}

	public static Collection addressInputs() {
		String ExpectedAddressRegex = "%TX%Irss%han%";

		return Arrays.asList(new Object[][] {
			{"TX Irss han", ExpectedAddressRegex},
			{"   TX Irss    han    ", ExpectedAddressRegex},
			{"TX Irss han        ", ExpectedAddressRegex},
			{"  TX Irss han", ExpectedAddressRegex},
			{"TX", "%TX%"},
			{"    ", null},
			{"  ****  ", "%****%"},
			{"", null},
			{null, null}
		});
	}

    @MethodSource("addressInputs")
    @ParameterizedTest
    public void testGetSearchAddressRegex(String addressInput, String expectedAddressRegex) {

        initProjectSearchOptionForAppTest(addressInput, expectedAddressRegex);

		searchOption.setAddress(addressInput);
		assertEquals(expectedAddressRegex, searchOption.getAddressRegex());
	}
}
