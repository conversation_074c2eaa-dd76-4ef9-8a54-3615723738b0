package com.bees360.entity.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

public class TestProjectStatusEnum {
	public static void main(String[] args) {
		Map<Integer, List<Integer>> statuses = new HashMap<Integer, List<Integer>>();
		for(ProcessStatusEnum s: ProcessStatusEnum.values()) {
			statuses.put(s.getCode(), new ArrayList<Integer>());
		}
		for(ProjectStatusEnum s: ProjectStatusEnum.values()) {
			if(s.getProcessStatus() != null) {
				statuses.get(s.getProcessStatus().getCode()).add(s.getCode());
			}
		}
		for(Entry<Integer, List<Integer>> entry: statuses.entrySet()) {
			System.out.println(entry.getKey() + ":" + entry.getValue());
		}
	}
}
