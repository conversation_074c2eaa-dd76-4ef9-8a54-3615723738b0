package com.bees360.entity.vo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.junit.jupiter.api.Test;

import com.bees360.entity.User;
import com.bees360.entity.dto.IdNameListDto;
import com.bees360.entity.enums.RoleEnum;

import static org.junit.jupiter.api.Assertions.*;

public class UsetViewVoTest {

	@Test
	public void testGetCertificates() {
		UserViewVo view = new UserViewVo();
		view.setRoleApplications(RoleEnum.REVIEWER.getBitMap());
		view.setCertificateList(RoleEnum.REVIEWER.getCode()
				+ ":https://xxxx/15603297421.png;");
		List<IdNameListDto> certificates = view.getCertificates();
		assertEquals(certificates.size(), 1);

		IdNameListDto certificate = certificates.get(0);

		assertEquals(certificate.getId(), RoleEnum.REVIEWER.getCode());
		assertEquals(certificate.getName(), RoleEnum.REVIEWER.getDisplay());
		assertEquals(certificate.getList().size(), 1);
		assertEquals(certificate.getList().get(0), "https://xxxx/15603297421.png");
	}

	@Test
	public void testGetCertificatesCaseMulti() {

		Map<Integer, List<String>> data = new HashMap<Integer, List<String>>();
		data.put(RoleEnum.ADJUSTER.getCode(), Arrays.asList("https://xxxx/1.jpg", "https://xxxx/2.jpg"));
		data.put(RoleEnum.REVIEWER.getCode(), Arrays.asList("https://xxxx/11.jpg", "https://xxxx/12.jpg"));

		Set<String> idNameLists = new HashSet<String>();

		User user = new User();
		for(Entry<Integer, List<String>> entry: data.entrySet()) {
			RoleEnum role = RoleEnum.getEnum(entry.getKey());
			user.applayRole(role, new ArrayList<String>(entry.getValue()));
			long id = (long)role.getCode();
			idNameLists.add(new IdNameListDto(id, role.getDisplay(), entry.getValue()).toString());
		}
		UserViewVo view = new UserViewVo();
		view.setRoleApplications(user.getRoleApplicationStatus());
		view.setCertificateList(user.getCertificateList());

		List<IdNameListDto> certificates = view.getCertificates();
		assertEquals(certificates.size(), 2);

		Iterator<IdNameListDto> it = certificates.iterator();
		while(it.hasNext()) {
			IdNameListDto dto = it.next();
			String dtoString = dto.toString();
			assertTrue(idNameLists.contains(dtoString));
			it.remove();
			idNameLists.remove(dtoString);

//			System.out.println(dtoString);
		}

		assertTrue(certificates.isEmpty());
		assertTrue(idNameLists.isEmpty());
	}
}
