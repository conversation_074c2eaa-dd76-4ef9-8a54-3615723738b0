package com.bees360.entity.dto;

import static org.junit.jupiter.api.Assertions.*;

import com.bees360.entity.enums.NewProjectStatusEnum;

import com.bees360.entity.vo.ProjectStatusVo;

import java.util.List;
import org.junit.jupiter.api.Test;

public class ProjectStatusTimeLineDtoTest {

    @Test
    public void toStatusList() {
        ProjectStatusTimeLineDto projectStatusTimeLineDto = new ProjectStatusTimeLineDto();

        ProjectStatusVo status = new ProjectStatusVo();
        status.setCreatedTime(System.currentTimeMillis());

        projectStatusTimeLineDto.addStatusVo(status);
        projectStatusTimeLineDto.addStatusVo(status);

        List<ProjectStatusVo> statusVoList = projectStatusTimeLineDto.toStatusList();
        assertFalse(statusVoList.isEmpty());
        for(ProjectStatusVo statusVo: statusVoList) {
            assertTrue(statusVo != null);
        }
    }

    @Test
    public void getFirstAvailable() {
        ProjectStatusTimeLineDto projectStatusTimeLineDto = new ProjectStatusTimeLineDto();

        ProjectStatusVo status = new ProjectStatusVo(NewProjectStatusEnum.CUSTOMER_CONTACTED);
        status.setCreatedTime(System.currentTimeMillis());

        projectStatusTimeLineDto.addStatusVo(status);

        final ProjectStatusVo status1 = new ProjectStatusVo(NewProjectStatusEnum.SITE_INSPECTED);
        status1.setCreatedTime(System.currentTimeMillis());
        projectStatusTimeLineDto.addStatusVo(status1);

        assertEquals(projectStatusTimeLineDto.getStatusVo(NewProjectStatusEnum.CUSTOMER_CONTACTED), projectStatusTimeLineDto.getFirstAvailable());
        assertEquals(projectStatusTimeLineDto.getStatusVo(NewProjectStatusEnum.SITE_INSPECTED), projectStatusTimeLineDto.getLastAvailable());
    }


    @Test
    public void testHandleReceive(){

        ProjectStatusTimeLineDto projectStatusTimeLineDto = new ProjectStatusTimeLineDto();

        projectStatusTimeLineDto.addStatusVo(null);

        projectStatusTimeLineDto.getStatusVo(null);

        ProjectStatusVo status = new ProjectStatusVo(NewProjectStatusEnum.RECEIVE_ERROR);
//        status.setCreatedTime(System.currentTimeMillis());
        projectStatusTimeLineDto.addStatusVo(status);

        status = new ProjectStatusVo(NewProjectStatusEnum.CLIENT_RECEIVED);
        status.setCreatedTime(System.currentTimeMillis());
        projectStatusTimeLineDto.addStatusVo(status);


        status = new ProjectStatusVo(NewProjectStatusEnum.RECEIVE_ERROR);
        status.setCreatedTime(System.currentTimeMillis());
        projectStatusTimeLineDto.addStatusVo(status);

        assertEquals(projectStatusTimeLineDto.getStatusVo(NewProjectStatusEnum.RECEIVE_ERROR), projectStatusTimeLineDto.getLastAvailable());

    }

    @Test
    public void getLastAvailable() {
        // test in getFirstAvailable()
    }
}
