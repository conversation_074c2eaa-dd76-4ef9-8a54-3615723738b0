package com.bees360.entity.enums;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 * @date 2020/04/25 09:17
 */
public class RealtimeElementTypeEnumTest {

	@Test
    public void getEnumTest() {
        RealtimeElementTypeEnum testType = RealtimeElementTypeEnum
            .getEnum(RealtimeElementTypeEnum.ROOF_ONLY_UNDERWRITING_CROP.getCode());
        assertEquals(testType, RealtimeElementTypeEnum.ROOF_ONLY_UNDERWRITING_CROP);
    }

    @Test
    public void getCloseUpTest() {
        RealtimeElementTypeEnum testType = RealtimeElementTypeEnum
            .getCloseUp(ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT.getCode());
        assertEquals(testType, RealtimeElementTypeEnum.ROOF_ONLY_UNDERWRITING_CLOSE_UP);
    }
}
