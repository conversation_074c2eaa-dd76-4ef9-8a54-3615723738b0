package com.bees360.entity.enums;

import com.google.api.client.util.Sets;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertTrue;
/**
 * <AUTHOR>
 */
public class ProjectStatusEnumTest {

    @Test
    public void theCodeShouldBeUnique() {
        final Set<Integer> codes = Sets.newHashSet();
        Arrays.stream(ProjectStatusEnum.values()).forEach(s -> {
            assertTrue(codes.add(s.getCode()), "The code " + s.getCode() + " of status " + s + " exists.");
        });
    }
}
