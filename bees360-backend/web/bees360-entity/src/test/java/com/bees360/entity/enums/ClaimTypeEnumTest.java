package com.bees360.entity.enums;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */
public class ClaimTypeEnumTest {

    @Test
    public void codeShouldUnique() {
        Set<Integer> codes = Arrays.stream(ClaimTypeEnum.values()).map(ClaimTypeEnum::getCode).collect(Collectors.toSet());
        assertEquals(ClaimTypeEnum.values().length, codes.size());
    }
}
