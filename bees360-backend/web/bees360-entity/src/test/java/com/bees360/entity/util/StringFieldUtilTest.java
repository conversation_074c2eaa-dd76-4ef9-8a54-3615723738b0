package com.bees360.entity.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class StringFieldUtilTest {

    @Test
    public void join_null() {
        char[] input = null;
        String expected = null;
        String result = StringFieldUtil.join("%", input);
        assertEquals(expected, result);
    }

    @Test
    public void join_empty() {
        char[] input = {};
        String expected = "";
        String result = StringFieldUtil.join("%", input);
        assertEquals(expected, result);
    }

    @Test
    public void join_oneChar() {
        char[] input = {'a'};
        String expected = "a";
        String result = StringFieldUtil.join("%", input);
        assertEquals(expected, result);
    }

    @Test
    public void join_moreChars() {
        char[] input = {'a', 'b'};
        String expected = "a%b";
        String result = StringFieldUtil.join("%", input);
        assertEquals(expected, result);
    }
}
