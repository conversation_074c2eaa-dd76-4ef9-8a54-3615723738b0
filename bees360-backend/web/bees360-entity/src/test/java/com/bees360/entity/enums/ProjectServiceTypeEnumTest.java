package com.bees360.entity.enums;

import com.google.api.client.util.Sets;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 */
public class ProjectServiceTypeEnumTest {

    @Test
    public void theCodeShouldBeUnique() {
        final Set<Integer> codes = Sets.newHashSet();
        final Set<String> values = Sets.newHashSet();
        Arrays.stream(ProjectServiceTypeEnum.values()).forEach(s -> {
            assertTrue(codes.add(s.getCode()), "The code " + s.getCode() + " of status " + s + " exists.");
            assertTrue(values.add(s.getValue()), "The value " + s.getValue() + " of status " + s + " exists.");
        });
    }
}
