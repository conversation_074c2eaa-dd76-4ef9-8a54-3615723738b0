package com.bees360.entity.vo;

import com.bees360.entity.openapi.OpenProjectCreateVo;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class OpenProjectCreateVoTest {

    @Test
    void testNullAttributes() {
        var vo = new OpenProjectCreateVo();
        vo.setCountry(null);
        vo.setPolicyNumber(null);
        vo.setInspectionNumber(null);
        vo.setInsuredName(null);
        vo.setInsuredPhone(null);
        vo.setInsuredEmail(null);
        vo.setAgentName(null);
        vo.setAgentPhone(null);
        vo.setAgentEmail(null);
        assertEquals("US", vo.getCountry());
        assertEquals("", vo.getPolicyNumber());
        assertEquals("", vo.getInspectionNumber());
        assertEquals("", vo.getInsuredName());
        assertEquals("", vo.getInsuredPhone());
        assertEquals("", vo.getInsuredEmail());
        assertEquals("", vo.getAgentName());
        assertEquals("", vo.getAgentPhone());
        assertEquals("", vo.getAgentEmail());
    }
}
