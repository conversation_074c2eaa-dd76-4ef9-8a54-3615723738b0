package com.bees360.entity;


import com.bees360.entity.enums.FileSourceTypeEnum;

public class ProjectImageTest {

//	@Test
	public void testClone(){
		ProjectImage image = ProjectImage.defaultImage();
		System.out.println("original default: " + image);
		image.setFileSourceType(FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode());
		image.setAnnotationImage("annotationImage");
		System.out.println("default modified: " + image);

		ProjectImage image2 = ProjectImage.defaultImage();
		System.out.println("new original default: " + image2);


		long start = System.currentTimeMillis();
		for(int i = 0; i < 1000; i ++){
			new ProjectImage();
		}
		long end = System.currentTimeMillis();
		System.out.println("new Spend Time: " + (end - start));
		start = System.currentTimeMillis();
		for(int i = 0; i < 1000; i ++){
			ProjectImage.defaultImage();
		}
		end = System.currentTimeMillis();
		System.out.println("new Spend Time: " + (end - start));
	}
}
