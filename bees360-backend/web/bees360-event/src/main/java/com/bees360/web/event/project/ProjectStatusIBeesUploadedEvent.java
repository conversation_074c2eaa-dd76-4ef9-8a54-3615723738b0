package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import lombok.ToString;

/**
 * @see com.bees360.entity.enums.NewProjectStatusEnum#IBEES_UPLOADED
 **/
@ToString(callSuper = true)
public class ProjectStatusIBeesUploadedEvent extends ProjectStatusEvent {
    public ProjectStatusIBeesUploadedEvent(Object source, Project project, ProjectStatus status) {
        super(source, project, status, "");
    }

    public ProjectStatusIBeesUploadedEvent(Object source, Project project, ProjectStatus status, String comment) {
        super(source, project, status, comment);
    }
}
