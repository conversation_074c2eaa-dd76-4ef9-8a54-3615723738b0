package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.enums.NewProjectStatusEnum;
import lombok.ToString;

/**
 * @see NewProjectStatusEnum#SITE_INSPECTED
 *
 * <AUTHOR>
 * @date 2020/04/20 12:41
 */
@ToString(callSuper = true)
public class ProjectStatusSiteInspectedEvent extends ProjectStatusEvent {

    public ProjectStatusSiteInspectedEvent(Object source, Project project, ProjectStatus status) {
        super(source, project, status, "");
    }

    public ProjectStatusSiteInspectedEvent(Object source, Project project, ProjectStatus status, String comment) {
        super(source, project, status, comment);
    }
}
