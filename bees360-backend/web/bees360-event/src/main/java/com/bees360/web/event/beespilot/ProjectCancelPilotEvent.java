package com.bees360.web.event.beespilot;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import jakarta.validation.constraints.NotNull;

/**
 * 取消分配到该项目{@link Long projectId}的飞手
 * <AUTHOR>
 * @since 2020/10/5 11:50 上午
 **/
public class ProjectCancelPilotEvent extends ApplicationEvent {
    @Getter
    private final Long projectId;
    @Getter
    private final Long pilotId;

    public ProjectCancelPilotEvent(Object source, @NotNull Long projectId, @NotNull long pilotId) {
        super(source);
        this.projectId = projectId;
        this.pilotId = pilotId;
    }
}
