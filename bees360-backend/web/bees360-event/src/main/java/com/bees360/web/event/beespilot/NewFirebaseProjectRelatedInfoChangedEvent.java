package com.bees360.web.event.beespilot;


import java.util.List;

import com.bees360.job.registry.SerializableFirebaseProject;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Firebase Project的related的数据有更改，应该更新到Firebase，如 pilot, contact_time, tag_id
 * @see SerializableFirebaseProject
 */
public class NewFirebaseProjectRelatedInfoChangedEvent extends ApplicationEvent {
    @Getter
    private final long projectId;
    @Getter
    private final List<Long> labels;
    public NewFirebaseProjectRelatedInfoChangedEvent(Object source, long projectId, List<Long> labels) {
        super(source);
        this.projectId = projectId;
        this.labels = labels;
    }
}
