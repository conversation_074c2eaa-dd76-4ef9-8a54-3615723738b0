package com.bees360.web.event.project;

import java.util.Collections;
import java.util.Date;
import java.util.Set;

import com.bees360.entity.Project;
import com.bees360.entity.enums.ImageUploadStatusEnum;
import com.bees360.entity.enums.NewProjectStatusEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 * @date 2020/04/11 12:21
 */
public abstract class ProjectEvent extends ApplicationEvent implements HasProject {

    private final Project project;

    /**
     *
     * @param source the object on which the event initially occurred (never {@code null})
     * @param project the project related to the event (never {@code null})
     */
    public ProjectEvent(@NonNull Object source, @NonNull Project project) {
        super(source);
        this.project = project;
    }

    @Override
    public Project getProject() {
        return project;
    }
}
