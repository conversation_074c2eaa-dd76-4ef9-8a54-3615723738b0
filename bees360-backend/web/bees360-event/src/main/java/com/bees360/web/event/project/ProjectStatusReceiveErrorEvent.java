package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import lombok.ToString;

@ToString(callSuper = true)
public class ProjectStatusReceiveErrorEvent extends ProjectStatusEvent {
    public ProjectStatusReceiveErrorEvent(Object source, Project project, ProjectStatus status) {
        super(source, project, status, "");
    }

    public ProjectStatusReceiveErrorEvent(Object source, Project project, ProjectStatus status, String comment) {
        super(source, project, status, comment);
    }
}
