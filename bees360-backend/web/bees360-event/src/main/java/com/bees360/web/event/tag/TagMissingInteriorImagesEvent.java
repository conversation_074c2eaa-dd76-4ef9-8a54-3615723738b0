package com.bees360.web.event.tag;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class TagMissingInteriorImagesEvent extends ApplicationEvent {

    private final long projectId;

    /** 是否存在interior图片 */
    private final boolean exists;

    public TagMissingInteriorImagesEvent(Object source, long projectId, boolean exists) {
        super(source);
        this.projectId = projectId;
        this.exists = exists;
    }
}
