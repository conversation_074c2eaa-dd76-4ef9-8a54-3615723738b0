package com.bees360.web.event.image;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 新图片上传事件
 */
public class ImageUploadedEventForActivity extends ApplicationEvent {
    /**
     * 此次上传的图片Key集合
     */
    @Getter
    private final Iterable<String> imageKeys;

    @Getter
    private final long projectId;
    @Getter
    private final long createdBy;

    public ImageUploadedEventForActivity(Object source, Iterable<String> imageKeys, long projectId, long createdBy) {
        super(source);
        this.imageKeys = imageKeys;
        this.projectId = projectId;
        this.createdBy = createdBy;
    }
}
