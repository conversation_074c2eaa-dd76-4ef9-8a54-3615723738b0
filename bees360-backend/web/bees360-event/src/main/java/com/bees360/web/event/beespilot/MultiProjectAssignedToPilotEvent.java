package com.bees360.web.event.beespilot;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.web.event.project.ProjectStatusEvent;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/04/20 12:40
 * @see NewProjectStatusEnum#ASSIGNED_TO_PILOT
 */
public class MultiProjectAssignedToPilotEvent extends ApplicationEvent {
    @Getter
    List<Long> projectIds;
    @Getter
    long pilotId;

    public MultiProjectAssignedToPilotEvent(Object source, @NotNull List<Long> projectIds, long userId) {
        super(source);
        this.pilotId = userId;
        this.projectIds = projectIds;
    }
}
