package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectMessage;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Project 被创建
 *
 * <AUTHOR>
 * @date 2020/04/20 12:10
 */
public class ProjectMessageCreatedEvent extends ApplicationEvent {

    @Getter
    private ProjectMessage projectMessage;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public ProjectMessageCreatedEvent(Object source) {
        super(source);
    }

    public ProjectMessageCreatedEvent(Object source, ProjectMessage projectMessage) {
        super(source);
        this.projectMessage = projectMessage;
    }
}
