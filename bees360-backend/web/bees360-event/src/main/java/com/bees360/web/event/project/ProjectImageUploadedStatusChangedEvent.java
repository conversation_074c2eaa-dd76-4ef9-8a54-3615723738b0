package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.enums.ImageUploadStatusEnum;

/**
 * <AUTHOR>
 * @date 2020/04/20 12:24
 */
public class ProjectImageUploadedStatusChangedEvent extends ProjectEvent {
    private final ImageUploadStatusEnum status;

    public ProjectImageUploadedStatusChangedEvent(Object source, Project project, ImageUploadStatusEnum status) {
        super(source, project);
        this.status = status;
    }

    public ImageUploadStatusEnum getStatus() {
        return status;
    }
}
