package com.bees360.web.event.account;

import com.bees360.entity.User;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/04/20 12:13
 */
public class UserPhoneChangedEvent extends UserEvent {
    @Getter
    private String fromPhone;
    @Getter
    private String toPhone;

    public UserPhoneChangedEvent(Object source, User user, String fromPhone, String toPhone) {
        super(source, user);
        this.fromPhone = fromPhone;
        this.toPhone = toPhone;
    }
}
