package com.bees360.web.event.project;

import com.bees360.entity.Project;

import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/04/20 12:22
 */
@ToString(callSuper = true)
public class ProjectInspectionTimeChangedEvent extends ProjectChangeEvent {

    /**
     * 触发操作的用户id，为null时候表示系统自动触发
     */
    private final Long userId;
    /**
     * 修改之前的时间
     */
    private final Long oldInspectionTime;
    /**
     * 修改之后的时间
     */
    private final Long newInspectionTime;

    public ProjectInspectionTimeChangedEvent(Object source, Long userId, Project project, Long oldInspectionTime,
                                             Long newInspectionTime) {
        super(source, project);
        this.userId = userId;
        this.oldInspectionTime = oldInspectionTime;
        this.newInspectionTime = newInspectionTime;
    }

    public ProjectInspectionTimeChangedEvent(Object source, Long userId, Project project, boolean changedFromFirebase,
            Long oldInspectionTime, Long newInspectionTime) {
        super(source, project, changedFromFirebase);
        this.userId = userId;
        this.oldInspectionTime = oldInspectionTime;
        this.newInspectionTime = newInspectionTime;
    }

    public Long getUserId() {
        return userId;
    }

    public Long getOldInspectionTime() {
        return oldInspectionTime;
    }

    public Long getNewInspectionTime() {
        return newInspectionTime;
    }
}
