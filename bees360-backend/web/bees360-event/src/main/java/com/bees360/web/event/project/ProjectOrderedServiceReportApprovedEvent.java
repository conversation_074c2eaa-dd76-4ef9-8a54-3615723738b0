package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.enums.ReportTypeEnum;
import lombok.Getter;
import org.springframework.lang.NonNull;

/**
 * 订阅的 Project Service 中的报告被审核通过，任意一个报告被审核通过均会触发该事件。
 *
 * <AUTHOR>
 * @date 2020/12/21 12:28
 */
public class ProjectOrderedServiceReportApprovedEvent extends ProjectEvent {

    @Getter
    private final long userId;

    @Getter
    private final ReportTypeEnum reportType;

    public ProjectOrderedServiceReportApprovedEvent(@NonNull Object source, @NonNull Project project,
            ReportTypeEnum reportType, @NonNull long userId) {
        super(source, project);
        this.reportType = reportType;
        this.userId = userId;
    }
}
