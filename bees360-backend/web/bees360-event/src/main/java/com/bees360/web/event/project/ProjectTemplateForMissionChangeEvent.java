package com.bees360.web.event.project;

import com.bees360.entity.Project;
import lombok.ToString;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 * @since 2020/8/22 11:36 上午
 **/
@ToString(callSuper = true)
public class ProjectTemplateForMissionChangeEvent extends ProjectChangeEvent {
    public ProjectTemplateForMissionChangeEvent(@NonNull Object source, @NonNull Project project) {
        super(source, project);
    }

    public ProjectTemplateForMissionChangeEvent(@NonNull Object source, @NonNull Project project, boolean changedFromFirebase) {
        super(source, project, changedFromFirebase);
    }
}
