package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import lombok.ToString;

/**
 * @see com.bees360.entity.enums.NewProjectStatusEnum#IMAGE_UPLOADED
 * <AUTHOR>
 * @since 2020/8/18 6:36 下午
 **/
@ToString(callSuper = true)
public class ProjectStatusImageUploadedEvent extends ProjectStatusEvent {
    public ProjectStatusImageUploadedEvent(Object source, Project project, ProjectStatus status) {
        super(source, project, status, "");
    }

    public ProjectStatusImageUploadedEvent(Object source, Project project, ProjectStatus status, String comment) {
        super(source, project, status, comment);
    }
}
