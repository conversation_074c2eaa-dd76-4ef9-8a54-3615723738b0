package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;
import org.springframework.context.ApplicationEvent;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 * @date 2020/04/14 10:51
 */
public abstract class ProjectReportEvent extends ProjectEvent {

    private final ProjectReportFile report;

    /**
     * @param source the object on which the event initially occurred (never {@code null})
     * @param project the project related to the event (never {@code null})
     * @param report the report related to the event (never {@code null})
     */
    public ProjectReportEvent(@NonNull  Object source, @NonNull Project project,
        @NonNull ProjectReportFile report) {
        super(source, project);
        this.report = report;
    }

    public ProjectReportFile getReport() {
        return report;
    }
}
