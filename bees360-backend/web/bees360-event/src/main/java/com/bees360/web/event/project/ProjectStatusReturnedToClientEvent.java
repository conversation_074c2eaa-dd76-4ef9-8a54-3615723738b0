package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.enums.NewProjectStatusEnum;
import lombok.ToString;
import org.springframework.lang.NonNull;

/**
 * @see NewProjectStatusEnum#RETURNED_TO_CLIENT
 *
 * <AUTHOR>
 * @date 2020/04/20 12:41
 */
@ToString(callSuper = true)
public class ProjectStatusReturnedToClientEvent extends ProjectStatusEvent {

    public ProjectStatusReturnedToClientEvent(@NonNull Object source, @NonNull Project project, ProjectStatus status) {
        super(source, project, status, "");
    }

    public ProjectStatusReturnedToClientEvent(@NonNull Object source, @NonNull Project project, ProjectStatus status, String comment) {
        super(source, project, status, comment);
    }
}
