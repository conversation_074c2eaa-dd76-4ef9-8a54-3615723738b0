package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/04/19 15:14
 */
@ToString
public abstract class ProjectStatusEvent extends ProjectEvent {

    private final ProjectStatus status;
    private final String comment;

    public ProjectStatusEvent(Object source, Project project, ProjectStatus status, String comment) {
        super(source, project);
        this.status = status;
        this.comment = comment;
    }

    public ProjectStatus getStatus() {
        return status;
    }

    public String getComment() {
        return comment;
    }
}
