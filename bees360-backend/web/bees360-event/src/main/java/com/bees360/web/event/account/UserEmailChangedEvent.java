package com.bees360.web.event.account;

import com.bees360.entity.User;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/04/20 12:12
 */
public class UserEmailChangedEvent extends UserEvent {
    @Getter
    private String fromEmail;
    @Getter
    private String toEmail;

    public UserEmailChangedEvent(Object source, User user, String fromEmail, String toEmail) {
        super(source, user);
        this.fromEmail = fromEmail;
        this.toEmail = toEmail;
    }
}
