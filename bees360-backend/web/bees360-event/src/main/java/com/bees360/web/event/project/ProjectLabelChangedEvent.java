package com.bees360.web.event.project;

import com.bees360.entity.enums.SystemTypeEnum;
import org.springframework.context.ApplicationEvent;

import java.util.List;

public class ProjectLabelChangedEvent extends ApplicationEvent {

    private final List<Long> projectLabels;
    private final Long projectId;
    private final SystemTypeEnum updateSource;

    public ProjectLabelChangedEvent(Object source, Long projectId, List<Long> projectLabels, SystemTypeEnum updateSource) {
        super(source);
        this.projectLabels = projectLabels;
        this.projectId = projectId;
        this.updateSource = updateSource;
    }

    public List<Long> getProjectLabels() {
        return projectLabels;
    }

    public Long getProjectId() {
        return projectId;
    }

    public SystemTypeEnum getUpdateSource() {
        return updateSource;
    }
}
