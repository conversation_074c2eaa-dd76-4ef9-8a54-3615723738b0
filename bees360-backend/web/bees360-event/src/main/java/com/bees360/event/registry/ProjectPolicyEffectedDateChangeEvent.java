package com.bees360.event.registry;

import com.bees360.codec.SerializableObject;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * Project的Effective Date发生更改
 * <AUTHOR>
 */
@Event
@SerializableObject
@Data
@Accessors(chain = true)
public class ProjectPolicyEffectedDateChangeEvent {
    /**
     * 发出该事件的项目
     */
    private long projectId;
    /**
     * 触发该事件的用户ID
     */
    private String operator;

    private LocalDate oldPolicyEffectiveDate;

    private LocalDate newPolicyEffectiveDate;
}
