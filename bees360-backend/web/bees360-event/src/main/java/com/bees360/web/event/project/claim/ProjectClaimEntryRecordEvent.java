package com.bees360.web.event.project.claim;

import com.bees360.entity.enums.ProjectHoverStatusEnum;
import com.bees360.entity.enums.ProjectPlnarStatusEnum;
import com.bees360.entity.enums.ProjectReportRecordEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2021/03/17 15:56
 */
public class ProjectClaimEntryRecordEvent extends ApplicationEvent {

    private long projectId;

    /**
     * @see ProjectReportRecordEnum
     */
    private String type;

    /**
     *
     * @see ProjectHoverStatusEnum
     * @see ProjectPlnarStatusEnum
     * @see ReportGenerationStatusEnum
     *
     */
    private Integer status;

    public ProjectClaimEntryRecordEvent(Object source, long projectId, String type,
        Integer status) {
        super(source);
        this.projectId = projectId;
        this.type = type;
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public long getProjectId() {
        return projectId;
    }

    public String getType() {
        return type;
    }
}
