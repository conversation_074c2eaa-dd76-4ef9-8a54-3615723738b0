package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.enums.SystemTypeEnum;
import lombok.Getter;

public class ProjectCanceledEvent extends ProjectEvent {

    @Getter
    private final long userId;

    @Getter
    private final SystemTypeEnum systemType;

    @Getter
    private final String cancelReason;

    public ProjectCanceledEvent(Object source, Project project, long userId, SystemTypeEnum systemType, String cancelReason) {
        super(source, project);
        this.userId = userId;
        this.systemType = systemType;
        this.cancelReason = cancelReason;
    }
}
