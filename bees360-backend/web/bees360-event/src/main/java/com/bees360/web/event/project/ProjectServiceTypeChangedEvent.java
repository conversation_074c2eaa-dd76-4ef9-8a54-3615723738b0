package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class ProjectServiceTypeChangedEvent extends ProjectChangedEvent {

    private final ProjectServiceTypeEnum before;
    private final ProjectServiceTypeEnum after;
    private final long projectId;
    public ProjectServiceTypeChangedEvent(
        Object source,
        ProjectServiceTypeEnum before,
        ProjectServiceTypeEnum after,
        long projectId, Project project, Project oldProject) {
        super(source, project, oldProject);
        this.before = before;
        this.after = after;
        this.projectId = projectId;
    }
}
