package com.bees360.web.event.account;

import com.bees360.entity.User;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2020/04/11 13:00
 */
public abstract class UserEvent extends ApplicationEvent implements HasUser {

    private final User user;

    public UserEvent(Object source, User user) {
        super(source);
        this.user = user;
    }

    @Override
    public User getUser() {
        return user;
    }
}
