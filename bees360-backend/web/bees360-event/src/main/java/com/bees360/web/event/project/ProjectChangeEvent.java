package com.bees360.web.event.project;

import com.bees360.entity.Project;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.lang.NonNull;


/**
 * 项目基本数据更新事件
 * <AUTHOR>
 * @since 2020/8/22 11:36 上午
 **/
@ToString
public class ProjectChangeEvent extends ProjectEvent {
    @Getter
    @Setter
    private boolean changedFromFirebase;
    public ProjectChangeEvent(@NonNull Object source, @NonNull Project project) {
        super(source, project);
    }

    public ProjectChangeEvent(Object source, Project project, boolean changedFromFirebase) {
        super(source, project);
        this.changedFromFirebase = changedFromFirebase;
    }
}
