package com.bees360.web.event.project;

import com.bees360.entity.Project;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 * @since 2020/9/1 11:45 上午
 **/
public class BeesPilotStatusEvent extends ProjectEvent {
    private final String taskId;
    public BeesPilotStatusEvent(@NonNull Object source, @NonNull Project project, String taskId) {
        super(source, project);
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }
}
