package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.User;
import com.bees360.entity.enums.NewProjectStatusEnum;
import lombok.ToString;

/**
 * @see NewProjectStatusEnum#ASSIGNED_TO_PILOT
 *
 * <AUTHOR>
 * @date 2020/04/20 12:40
 */
@ToString(callSuper = true)
public class ProjectStatusAssignedToPilotEvent extends ProjectStatusEvent {

    public ProjectStatusAssignedToPilotEvent(Object source, Project project, ProjectStatus status) {
        this(source, project, status, "");
    }

    public ProjectStatusAssignedToPilotEvent(Object source, Project project, ProjectStatus status, String comment) {
        super(source, project, status, comment);
    }
}
