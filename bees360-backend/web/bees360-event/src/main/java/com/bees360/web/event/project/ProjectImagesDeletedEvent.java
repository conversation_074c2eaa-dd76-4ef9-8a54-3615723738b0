package com.bees360.web.event.project;

import com.bees360.entity.enums.SystemTypeEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

public class ProjectImagesDeletedEvent extends ApplicationEvent {

    @Getter private long projectId;

    @Getter private List<String> imageIds;

    /** 0:Normal 1: Delete -1: Completely deleted */
    @Getter private int deleteStatus;

    @Getter private SystemTypeEnum systemType;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public ProjectImagesDeletedEvent(Object source) {
        super(source);
    }

    public ProjectImagesDeletedEvent(
            Object source, long projectId, List<String> imageIds, int deleteStatus) {
        this(source, projectId, imageIds, deleteStatus, SystemTypeEnum.BEES360);
    }

    public ProjectImagesDeletedEvent(
            Object source,
            long projectId,
            List<String> imageIds,
            int deleteStatus,
            SystemTypeEnum systemType) {
        super(source);
        this.projectId = projectId;
        this.imageIds = imageIds;
        this.deleteStatus = deleteStatus;
        this.systemType = systemType;
    }
}
