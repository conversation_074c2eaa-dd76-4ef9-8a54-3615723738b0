package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import lombok.Getter;

public class ProjectLatestStatusChangedEvent extends ProjectEvent {

    @Getter private final int oldstatus;
    @Getter private final int status;


    public ProjectLatestStatusChangedEvent(Object source, Project project, int oldstatus, int status) {
        super(source, project);
        this.oldstatus = oldstatus;
        this.status = status;
    }
}
