package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.enums.SystemTypeEnum;
import lombok.Getter;
import lombok.ToString;

@ToString(callSuper = true)
public class ProjectStatusClientReceivedEvent extends ProjectStatusEvent {

    @Getter
    private final long userId;

    @Getter
    private final SystemTypeEnum systemType;

    public ProjectStatusClientReceivedEvent(Object source, Project project, ProjectStatus status, long userId,
        SystemTypeEnum systemType) {
        this(source, project, status, userId, systemType, "");
    }

    public ProjectStatusClientReceivedEvent(Object source, Project project, ProjectStatus status, long userId,
                                            SystemTypeEnum systemType, String comment) {
        super(source, project, status, comment);
        this.userId = userId;
        this.systemType = systemType;
    }
}
