package com.bees360.web.event.account;

import com.bees360.entity.User;
import com.bees360.entity.enums.UserCreationTypeEnum;

/**
 * 创建账号
 *
 * <AUTHOR>
 * @date 2020/04/20 12:11
 */
public class UserCreatedEvent extends UserEvent {

    private final UserCreationTypeEnum creationType;

    public UserCreatedEvent(Object source, User user, UserCreationTypeEnum creationType) {
        super(source, user);
        this.creationType = creationType;
    }

    public UserCreationTypeEnum getCreationType(){
        return creationType;
    }
}
