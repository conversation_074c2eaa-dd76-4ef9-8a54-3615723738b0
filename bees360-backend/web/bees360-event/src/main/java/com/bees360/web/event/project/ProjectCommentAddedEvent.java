package com.bees360.web.event.project;

import com.bees360.activity.Comment;
import com.bees360.entity.Project;
import lombok.Getter;
import lombok.NonNull;

public class ProjectCommentAddedEvent extends ProjectEvent {
    @Getter
    private final Comment comment;
    public ProjectCommentAddedEvent(Object source, @NonNull Project project, @NonNull Comment comment) {
        super(source, project);
        this.comment = comment;
    }
}
