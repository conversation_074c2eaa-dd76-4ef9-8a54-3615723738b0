package com.bees360.web.event.project;

import com.bees360.entity.Project;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2020/8/22 11:36 上午
 **/
public class ProjectAddressChangeEvent extends ProjectChangeEvent {

    @Getter
    @Setter
    private Long updatedBy;

    @Getter
    @Setter
    private String oldAddress;

    @Getter
    @Setter
    private String newAddress;

    public ProjectAddressChangeEvent(Object source, Project project) {
        this(source, project, null, null, null);
    }

    public ProjectAddressChangeEvent(Object source, Project project, Long updatedBy, String oldAddress, String newAddress) {
        super(source, project);
        this.updatedBy = updatedBy;
        this.oldAddress = oldAddress;
        this.newAddress = newAddress;
    }
}
