package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.enums.NewProjectStatusEnum;
import lombok.ToString;

/**
 *  @see NewProjectStatusEnum#CUSTOMER_CONTACTED
 *
 * <AUTHOR>
 * @date 2020/04/20 12:39
 */
@ToString(callSuper = true)
public class ProjectStatusCustomerContactedEvent extends ProjectStatusEvent {

    public ProjectStatusCustomerContactedEvent(Object source, Project project, ProjectStatus status) {
        super(source, project, status, "");
    }

    public ProjectStatusCustomerContactedEvent(Object source, Project project, ProjectStatus status, String comment) {
        super(source, project, status, comment);
    }
}
