package com.bees360.web.event.project;

import com.bees360.entity.Project;

import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/04/20 12:22
 */
public class ProjectImagesUploadedEvent extends ProjectEvent {
    /**
     * 新上传的图片id
     */
    @Getter
    private final List<String> imageIds;

    public ProjectImagesUploadedEvent(Object source, Project project, List<String> imageIds) {
        super(source, project);
        this.imageIds = imageIds;
    }
}
