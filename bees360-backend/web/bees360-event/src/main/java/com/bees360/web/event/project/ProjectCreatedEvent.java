package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.enums.CreationChannelType;
import jakarta.annotation.Nullable;
import lombok.NonNull;

/**
 * Project 被创建
 *
 * <AUTHOR>
 * @date 2020/04/20 12:10
 */
public class ProjectCreatedEvent extends ProjectEvent {

    private final CreationChannelType creationChannel;

    public ProjectCreatedEvent(@NonNull Object source, @NonNull Project project,
            @Nullable CreationChannelType creationChannel) {
        super(source, project);
        this.creationChannel = creationChannel;
    }

    public CreationChannelType getCreationChannel() {
        return creationChannel;
    }
}
