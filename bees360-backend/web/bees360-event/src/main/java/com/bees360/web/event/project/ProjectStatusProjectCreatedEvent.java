package com.bees360.web.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.enums.NewProjectStatusEnum;
import lombok.Getter;
import lombok.ToString;

/**
 * @see NewProjectStatusEnum#PROJECT_CREATED
 *
 * <AUTHOR>
 * @date 2020/04/20 12:38
 */
@ToString(callSuper = true)
public class ProjectStatusProjectCreatedEvent extends ProjectStatusEvent {

    /**
     * 表明这个状态是否是从其它状态rollback到ProjectCreated状态的
     */
    @Getter
    private final boolean hasRollback;

    public ProjectStatusProjectCreatedEvent(Object source, Project project, ProjectStatus status, boolean hasRollback) {
        this(source, project, status, hasRollback, "");
    }

    public ProjectStatusProjectCreatedEvent(Object source, Project project, ProjectStatus status, boolean hasRollback, String comment) {
        super(source, project, status, comment);
        this.hasRollback = hasRollback;
    }
}
