Bees360 Event
===

###### `guanrong.yang`

模块说明
---

该模块只会定义事件，不会提供任何监听器相关的类，也不要在该模块下创建没有必要的监听器。请在需要的地方自己创建监听器，监听自己需要的类。

使用该模块，可以解决部分循环依赖的问题。

设计思路
---

首先需要区分"容器"和"实体"，"容器"是包含其他"实体"，而"实体"是具有完整属性的个体。"容器"本身也是一个"实体"。（与操作系统中的目录和文件是类似的）

Project是一个实体，那么Project自身属性的修改（比如inspectionTime，address）触发的事件均发生在Project上。Project也是一个容器，对容器中群体的修改（比如Image个数的增减，此时Project是Image的容器）触发的事件也是发生在Project上。

推荐使用主动通知的方式。谁做了修改，谁就应该发布相关的通知。因为修改的地方可能会很多处，一旦业务发生变动，难以确保全部都进行了修改。如果后续需要在原来的基础上增加新的功能，也可以简单地通过增加监听器的方式进行解决。
