rct:
  endpoint: http://localhost/rct
  auth:
    id: username
    secret: password
  dataset:
    id: 0
    contract:
      insured-by: 0
      processed-by: 0
    creator: 0
    service-type-mapping:
      path: "$.TaskType.Code"
      default-value: WHITE_GLOVE
      map:
        # Scheduling Only
        "exterior": SCHEDULING_ONLY
    supplemental-service-mapping:
      path: "$.TaskType.Code"
      map:
        "exterior": "RC Report"
  client:
    request:
      connectTimeout: PT60S
      callTimeout: PT180S
      followRedirects: true
      retryOnConnectionFailure: true
