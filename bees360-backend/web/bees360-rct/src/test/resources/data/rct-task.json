{"Id": "1681834937544889298", "AccountId": "1648574868899102040", "ExternalUniqueId": "1681834937544515503", "ReferenceNumber": 13372, "Requestor": "Bees 360", "IsLockedOnMobile": false, "RiskQuality": 10.0, "RiskQualityPercent": 100, "Severity": 0.0, "OriginalDueDate": "2023-05-19T00:00:00", "DateAssigned": "2023-04-18T00:00:00", "DateCompleted": null, "DateModified": "2023-04-18T15:42:08.494", "DateArchived": null, "Address": {"Id": "1648574868899569189", "Province": {"Id": 9, "Country": {"Id": 124, "Code": "2", "Name": "USA"}, "Code": "FL", "Name": "Florida"}, "AddressLine": "2780 S Ocean Blvd APT 310", "City": "Palm Beach", "PostalCode": "33480-5527", "Longitude": -80.0373136, "Latitude": 26.6205049, "County": "--"}, "LastAssignedLocation": null, "Underwriter": {"Id": "1569941746924555277", "UserType": {"Id": 12, "Ordinal": 5, "Caption": "Underwriter", "Code": "5", "IsArchived": false}, "Name": "<PERSON>", "ContactNumber": "Berkley One", "IsArchived": false, "Phone": "", "Fax": "", "Email": "<EMAIL>", "Notes": "", "YearsOfExperience": null, "CostPerTask": null, "HourlyRate": null}, "Consultant": {"Id": "166993168823261121", "UserType": {"Id": 13, "Ordinal": 6, "Caption": "Inspector", "Code": "6", "IsArchived": false}, "Name": "<PERSON>", "ContactNumber": "Berkley One", "IsArchived": false, "Phone": "", "Fax": "", "Email": "<EMAIL>", "Notes": "", "YearsOfExperience": null, "CostPerTask": null, "HourlyRate": null}, "Broker": null, "CustomAssignee": null, "WritingCompany": null, "Priority": {"Id": 100, "Ordinal": 1, "Caption": "Normal", "Code": "1", "IsArchived": false}, "TaskStatus": {"Id": 14, "Ordinal": 1, "Caption": "New Request", "Code": "1", "IsArchived": false}, "TaskType": {"Id": 2396, "ListId": 15, "Ordinal": 5, "Caption": "Vendor Exterior Inspection", "Code": "exterior", "IsArchived": false}, "Agency": {"Id": "1590132627566177399", "ExternalUniqueId": "10202-010", "Type": 4, "DateTimeStamp": "2022-03-29T01:27:48.924", "IsArchived": false, "AddressId": "1565269281715469224", "Name": "Advocate Brokerage Corp.", "Phone": "(*************", "Fax": null, "Notes": null, "DivisionId": 0, "Code": "(*************"}, "ExtensionFields": [{"FieldName": "Request Type", "Id": 1, "Value": {"Id": 2394, "Ordinal": 1, "Caption": "New Business", "Code": "newbusiness", "IsArchived": false}}, {"FieldName": "Start Time- Hour", "Id": 2, "Value": null}, {"FieldName": "Start Time - Minute", "Id": 3, "Value": null}, {"FieldName": "Duration - Hours", "Id": 4, "Value": null}, {"FieldName": "Contact Phone", "Id": 40, "Value": "(*************"}, {"FieldName": "Contact Email", "Id": 41, "Value": "<EMAIL>"}, {"FieldName": "Date of Visit", "Id": 47, "Value": null}, {"FieldName": "Coverage A Limit", "Id": 49, "Value": 0.0}, {"FieldName": "Coverage B - Other Structures", "Id": 50, "Value": 0.0}, {"FieldName": "Coverage A&A", "Id": 51, "Value": 975000.0}, {"FieldName": "Number of Claims", "Id": 53, "Value": 1.0}, {"FieldName": "Loss Cause", "Id": 54, "Value": "WIND"}, {"FieldName": "Loss Amount", "Id": 55, "Value": 0.0}, {"FieldName": "Roof Renovation Year", "Id": 56, "Value": "N/A"}, {"FieldName": "Roof Cover Type", "Id": 57, "Value": "N/A"}, {"FieldName": "Construction Type", "Id": 60, "Value": "Masonry"}, {"FieldName": "Exterior Wall", "Id": 65, "Value": "N/A"}, {"FieldName": "EIFS Installed", "Id": 66, "Value": "N/A"}, {"FieldName": "Year Built", "Id": 69, "Value": 1968.0}, {"FieldName": "Protection Class Code", "Id": 73, "Value": "1"}, {"FieldName": "Prior Carrier", "Id": 75, "Value": "N/A"}, {"FieldName": "Inspection Company", "Id": 86, "Value": null}, {"FieldName": "Contact Last Name", "Id": 92, "Value": "<PERSON><PERSON><PERSON>"}, {"FieldName": "Transaction Code", "Id": 93, "Value": "New Business"}, {"FieldName": "Customer Number", "Id": 94, "Value": "104635"}, {"FieldName": "Contact First Name", "Id": 95, "Value": "<PERSON>"}, {"FieldName": "Effective Date", "Id": 96, "Value": "2022-03-10T00:00:00"}, {"FieldName": "Burglar Alarm", "Id": 97, "Value": "None"}, {"FieldName": "Fire Alarm", "Id": 98, "Value": "None"}, {"FieldName": "Gas Shut Off", "Id": 99, "Value": "N/A"}, {"FieldName": "Gas Leak", "Id": 100, "Value": "N/A"}, {"FieldName": "Gate/Guard", "Id": 101, "Value": "No"}, {"FieldName": "Hurricane Shutters", "Id": 102, "Value": "N/A"}, {"FieldName": "Lighting Protection", "Id": 103, "Value": "N/A"}, {"FieldName": "Signal Continuity", "Id": 104, "Value": "N/A"}, {"FieldName": "Sprinkler System", "Id": 105, "Value": "No Sprinkler System"}, {"FieldName": "Temperature Monitoring", "Id": 106, "Value": "N/A"}, {"FieldName": "Water Monitoring", "Id": 107, "Value": "None"}, {"FieldName": "Whole House Backup Generator", "Id": 108, "Value": "N/A"}, {"FieldName": "Extended Perimeter Protection", "Id": 109, "Value": "N/A"}, {"FieldName": "Total Square Footage Living Area", "Id": 135, "Value": "1300"}, {"FieldName": "Producer <PERSON><PERSON>", "Id": 136, "Value": "<EMAIL>"}, {"FieldName": "Recommended Value", "Id": 138, "Value": "0"}, {"FieldName": "Number of Stories", "Id": 139, "Value": "8"}, {"FieldName": "Preferred Document Delivery", "Id": 140, "Value": "Email"}, {"FieldName": "Contact Method", "Id": 141, "Value": "Phone"}, {"FieldName": "Contact Time", "Id": 142, "Value": "Morning"}, {"FieldName": "Transaction Date", "Id": 143, "Value": "2022-03-10T00:00:00"}, {"FieldName": "Prior Inspection Report", "Id": 144, "Value": "No"}, {"FieldName": "New Purchase/Rent", "Id": 145, "Value": "Yes"}, {"FieldName": "Full Time Resident Caretaker", "Id": 146, "Value": "No"}, {"FieldName": "Cooling Renovation Year", "Id": 147, "Value": "0"}, {"FieldName": "Plumbing Renovation Year", "Id": 148, "Value": "0"}, {"FieldName": "Electrical Renovation Year", "Id": 149, "Value": "0"}, {"FieldName": "Heating Renovation Year", "Id": 150, "Value": "0"}, {"FieldName": "Automation Rule", "Id": 156, "Value": null}], "PolicyNumber": "CO04250772", "Description": "", "AgencyId": "1590132627566177399", "CustomAssigneeId": null, "WritingCompanyId": null, "RegionId": 0, "Notes": "", "PriorityId": 100, "DateRequired": "2023-05-19T00:00:00"}