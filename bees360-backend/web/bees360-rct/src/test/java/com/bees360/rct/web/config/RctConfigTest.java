package com.bees360.rct.web.config;

import com.bees360.sdk.rct.model.ListItemDto;
import com.bees360.sdk.rct.model.TaskDto;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

class RctConfigTest {

    @Test
    void testMappings() {
        var taskDto = new TaskDto();
        var type = new ListItemDto();
        type.setId(100);
        taskDto.setTaskType(type);

        final var DEFAULT_VALUE = "Good";
        final var ID_EXPECTED_VALUE = "Perfect";

        var mappings = new RctConfig.MappingStrategy();
        mappings.setDefaultValue(DEFAULT_VALUE);
        assertEquals(mappings.apply(taskDto), DEFAULT_VALUE);

        // return default if without mappings
        mappings.setPath("$.TaskType.Id");
        assertEquals(mappings.apply(taskDto), DEFAULT_VALUE);

        // return default if doesn't map
        mappings.setMap(Maps.newHashMap(Map.of("50", "Bad", "80", "Good")));
        assertEquals(mappings.apply(taskDto), DEFAULT_VALUE);

        // return expected value if mapping
        mappings.setMap(Maps.newHashMap(Map.of("50", "Bad", "80", "Good", "100", "Perfect")));
        assertEquals(mappings.apply(taskDto), ID_EXPECTED_VALUE);
    }

    @Test
    void testMappingsDefaultIfPathNotFound() {
        final var DEFAULT_VALUE = "Good";

        var mappings = new RctConfig.MappingStrategy();
        mappings.setPath("$.TaskType.Id");
        mappings.setDefaultValue(DEFAULT_VALUE);
        mappings.setMap(Maps.newHashMap(Map.of("100", "Perfect")));

        assertEquals(mappings.apply(new TaskDto()), DEFAULT_VALUE);
    }
}
