package com.bees360.web.rct;

import com.bees360.job.CreateProjectFromRctIntegrationJobExecutor;
import com.bees360.oauth.OAuthAutoRefreshToken;
import com.bees360.rct.web.config.RctConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * disable rct config之后，相关的类将不会被创建出来。
 */
@SpringBootTest(classes = RctConfig.class, properties = "rct.disable=true")
public class RctDisableTest {

    @Autowired
    Optional<OAuthAutoRefreshToken> rctOAuthToken;

    @Autowired
    Optional<CreateProjectFromRctIntegrationJobExecutor> createProjectFromRctIntegrationJobExecutor;

    @Test
    void testDisableRctConfig() {
        assertTrue(rctOAuthToken.isEmpty());
        assertTrue(createProjectFromRctIntegrationJobExecutor.isEmpty());
    }
}
