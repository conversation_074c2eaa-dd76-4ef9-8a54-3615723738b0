package com.bees360.web.rct;

import com.bees360.base.exception.ServiceException;
import com.bees360.contract.Contract;
import com.bees360.contract.ContractManager;
import com.bees360.customer.Customer;
import com.bees360.customer.Message;
import com.bees360.entity.Project;
import com.bees360.job.Job;
import com.bees360.project.Message.IntegrationMessage;
import com.bees360.entity.dto.ProjectDto;
import com.bees360.job.CreateProjectFromRctIntegrationJobExecutor;
import com.bees360.job.registry.CreateProjectFromRctIntegrationJob;
import com.bees360.oauth.OAuthGrant;
import com.bees360.oauth.OAuthRefreshableToken;
import com.bees360.oauth.OAuthToken;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.rct.RctOAuthTokenGranter;
import com.bees360.sdk.rct.ApiException;
import com.bees360.sdk.rct.JSON;
import com.bees360.sdk.rct.api.TasksApi;
import com.bees360.sdk.rct.model.TaskDto;
import com.bees360.service.ProjectService;
import com.bees360.rct.web.config.RctConfig;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ActiveProfiles;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.function.BiFunction;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@ActiveProfiles("test")
@SpringBootTest(classes = RctTest.Config.class)
public class RctTest {

    @Import({
        RctConfig.class,
    })
    @Configuration
    public static class Config {

        @Bean
        @Primary
        RctOAuthTokenGranter oAuthTokenGranter(RctOAuthTokenGranter rctOAuthTokenGranter) {
            var accessToken =
                OAuthToken.of(
                    "accessToken", LocalDateTime.now().minus(Duration.ofHours(5)), "scope");
            var refreshToken = OAuthRefreshableToken.of(accessToken, "xsrfToken");
            rctOAuthTokenGranter = Mockito.spy(rctOAuthTokenGranter);
            doReturn(refreshToken).when(rctOAuthTokenGranter).grant(any(OAuthGrant.class));
            return rctOAuthTokenGranter;
        }

        @Bean
        ProjectService projectService() {
            return Mockito.mock(ProjectService.class);
        }

        @Bean
        ExternalIntegrationManager externalIntegrationManager() {
             var externalIntegrationManager = Mockito.mock(ExternalIntegrationManager.class);
             return externalIntegrationManager;
        }

        @Bean
        ContractManager contractManager(RctConfig.RctProperties rctProperties) {
            var contractManager = Mockito.mock(ContractManager.class);
            var datasetContract = rctProperties.getDataset().getContract();
            var insuredBy = Customer.of(Message.CustomerMessage.newBuilder().setId(datasetContract.getInsuredBy()).build());
            var processedBy = Customer.of(Message.CustomerMessage.newBuilder().setId(datasetContract.getProcessedBy()).build());
            var contract = Contract.of("1", insuredBy, processedBy, List.of(), List.of());
            when(contractManager.findByCompanyId(eq(insuredBy.getId()), eq(processedBy.getId()))).thenReturn(contract);
            return contractManager;
        }
    }

    @Autowired
    CreateProjectFromRctIntegrationJobExecutor createProjectFromRctIntegrationJobExecutor;

    @Autowired
    BiFunction<String, TaskDto, ProjectDto> taskProjectConverter;

    @Autowired
    RctConfig.RctProperties rctProperties;

    @Autowired
    ExternalIntegrationManager externalIntegrationManager;

    @Autowired
    ProjectService projectService;

    @SpyBean
    TasksApi rctTasksApi;


    @Test
    void testTaskProjectConverter() throws IOException {

        var task = getTaskDto();
        ProjectDto project = taskProjectConverter.apply("dataset-id", task);

        assertEquals(project.getSupplementalServices(), List.of("RC Report"));
        assertContract(project);
        assertInspection(project, task);
        assertPolicy(project, task);
        assertUnderwriting(project);
        assertClaim(project);
        assertInsured(project);
        assertAgency(project, task);
        assertEquals(project.getClaimNote(), task.getNotes());
    }

    @Test
    void testCreateProject() throws IOException, ApiException, ServiceException {

        var taskId = "202304192146";

        var job = new CreateProjectFromRctIntegrationJob();
        var message = IntegrationMessage.newBuilder()
            .setId("1012")
            .setIntegrationType("RiskControl")
            .setReferenceNumber(taskId)
            .build();

        job.setProjectIntegration(ExternalIntegration.from(message));

        doReturn(getTaskDto()).when(rctTasksApi).getTaskById(eq(taskId), eq(null), eq(null));
        var projectCreated = new Project();
        projectCreated.setProjectId(1002);
        when(projectService.createProject(any(Long.class), any(ProjectDto.class))).thenReturn(projectCreated);
        doNothing().when(externalIntegrationManager).setProjectId(eq(message.getId()), eq(projectCreated.getProjectId() + ""));

        createProjectFromRctIntegrationJobExecutor.execute(Job.ofPayload(job));
    }

    private TaskDto getTaskDto() throws IOException {
        var taskJson = IOUtils.resourceToString("data/rct-task.json", StandardCharsets.UTF_8, this.getClass().getClassLoader());
        TaskDto task = JSON.deserialize(taskJson, TaskDto.class);
        return task;
    }

    private void assertContract(ProjectDto project) {
        var contract = rctProperties.getDataset().getContract();
        assertEquals(contract.getInsuredBy(), project.getInsuranceCompany() + "");
        assertEquals(contract.getProcessedBy(), project.getRepairCompany() + "");
    }

    private void assertInspection(ProjectDto project, TaskDto task) {
        assertEquals(task.getReferenceNumber() + "", project.getInspectionNumber());
    }

    private void assertPolicy(ProjectDto project, TaskDto task) {
        assertEquals(task.getPolicyNumber(), project.getPolicyNumber());
        assertEquals(LocalDate.of(2022, 3, 10), project.getPolicyEffectiveDate());

        var address = task.getAddress();
        assertEquals(address.getAddressLine(), project.getAddress());
        assertEquals(address.getCity(), project.getCity());
        assertEquals(address.getProvince().getCode(), project.getState());
        assertEquals(address.getProvince().getCountry().getName(), project.getCountry());
        assertEquals(address.getPostalCode(), project.getZipCode());
        assertEquals(address.getLatitude(), project.getLat());
        assertEquals(address.getLongitude(), project.getLng());

        // building
        assertEquals("1968", project.getYearBuilt());
    }

    private void assertUnderwriting(ProjectDto project) {
        assertEquals(ServiceTypeEnum.SCHEDULING_ONLY.getCode(), project.getServiceType());
        assertEquals(com.bees360.entity.enums.ClaimTypeEnum.UNDERWRITING_FIRST_INSPECTION.getCode(), project.getClaimType());
    }

    private void assertClaim(ProjectDto project) {
    }

    private void assertInsured(ProjectDto project) {
        assertEquals("Miriam Sirtoa", project.getAssetOwnerName());
        assertEquals("<EMAIL>", project.getAssetOwnerEmail());
        assertEquals("(*************", project.getAssetOwnerPhone());
    }

    private void assertAgency(ProjectDto project, TaskDto task) {
        var agency = task.getAgency();
        assertEquals(agency.getName(), project.getAgentContactName());
        assertEquals(agency.getPhone(), project.getAgentPhone());
    }
}
