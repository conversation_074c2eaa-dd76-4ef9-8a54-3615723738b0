package com.bees360.rct.web;

import static java.util.Optional.ofNullable;

import com.bees360.address.Address;
import com.bees360.address.Message.AddressMessage;
import com.bees360.building.Message.BuildingType;
import com.bees360.policy.Policy;
import com.bees360.project.Building;
import com.bees360.project.Contact;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.Message;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.claim.Claim;
import com.bees360.project.claim.ClaimTypeEnum;
import com.bees360.project.inspection.Inspection;
import com.bees360.project.underwriting.Underwriting;
import com.bees360.sdk.rct.model.CountryDto;
import com.bees360.sdk.rct.model.ProvinceDto;
import com.bees360.sdk.rct.model.TaskDto;

import com.bees360.util.Iterables;
import lombok.NonNull;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import jakarta.annotation.Nullable;

public class TaskAccessor {

    private final TaskDto task;
    private final Map<String, Object> extensionFields = new HashMap<>();

    private final Function<TaskDto, ServiceTypeEnum> serviceTypeMappings;

    private final Function<TaskDto, ClaimTypeEnum> claimTypeMappings;

    private final Function<TaskDto, BuildingType> buildingTypeMappings;

    private final ServiceTypeEnum serviceType;

    private final ClaimTypeEnum claimType;

    private final BuildingType buildingType;

    private final List<String> supplementalServices;

    public TaskAccessor(
            @NonNull TaskDto task,
            Function<TaskDto, Collection<String>> supplementalServiceMappings,
            Function<TaskDto, ServiceTypeEnum> serviceTypeMappings,
            Function<TaskDto, ClaimTypeEnum> claimTypeMappings,
            Function<TaskDto, BuildingType> buildingTypeMappings) {
        this.task = task;
        for (var item : ofNullable(task.getExtensionFields()).orElse(List.of())) {
            this.extensionFields.put(item.getFieldName(), item.getValue());
        }
        this.supplementalServices = Iterables.toList(supplementalServiceMappings.apply(task));
        this.serviceTypeMappings = serviceTypeMappings;
        serviceType = this.serviceTypeMappings.apply(task);
        this.claimTypeMappings = claimTypeMappings;
        claimType = this.claimTypeMappings.apply(task);
        this.buildingTypeMappings = buildingTypeMappings;
        this.buildingType = this.buildingTypeMappings.apply(task);
    }

    public String getId() {
        return task.getId();
    }

    public Inspection getInspection() {
        var inspectionNo = ofNullable(task.getReferenceNumber()).map(Object::toString).orElse("");
        var inspection =
                Inspection.InspectionBuilder.newBuilder().setInspectionNo(inspectionNo).build();
        return inspection;
    }

    public Policy getPolicy() {
        var policyNo = ofNullable(task.getPolicyNumber()).orElse("");
        var effectiveDate = getCustomField("Effective Date", value -> LocalDateTime.parse(value + "").toLocalDate());
        return Policy.PolicyBuilder.newBuilder()
                .setPolicyNo(policyNo)
                .setPolicyEffectiveDate(effectiveDate)
                .setAddress(getAddress())
                .setBuilding(getBuilding())
                .build();
    }

    public Building getBuilding() {
        var yearBuild = getCustomField("Year Built", value -> (int) ((double) value), null);
        return Building.of(null, buildingType, yearBuild, null);
    }

    @Nullable
    public Address getAddress() {
        var taskAddress = task.getAddress();
        if (taskAddress == null) {
            return null;
        }
        var builder = AddressMessage.newBuilder();
        ofNullable(taskAddress.getProvince())
                .map(ProvinceDto::getCountry)
                .map(CountryDto::getName)
                .ifPresent(builder::setCountry);
        ofNullable(taskAddress.getProvince())
                .map(ProvinceDto::getCode)
                .ifPresent(builder::setState);
        ofNullable(taskAddress.getCity()).ifPresent(builder::setCity);
        ofNullable(taskAddress.getCounty()).ifPresent(builder::setCounty);
        ofNullable(taskAddress.getAddressLine()).ifPresent(builder::setStreetAddress);
        ofNullable(taskAddress.getPostalCode()).ifPresent(builder::setZip);
        ofNullable(taskAddress.getLatitude()).ifPresent(builder::setLat);
        ofNullable(taskAddress.getLongitude()).ifPresent(builder::setLng);
        return Address.from(builder.build());
    }

    public Claim getClaim() {
        if (!ProjectTypeEnum.CLAIM.equals(serviceType.getProjectType())) {
            return null;
        }
        var claimNo = ofNullable(task.getReferenceNumber()).map(Object::toString).orElse("");
        return Claim.ClaimBuilder.newBuilder()
                .setClaimNo(claimNo)
                .setServiceType(serviceType)
                .setClaimType(claimType)
                .build();
    }

    public Underwriting getUnderwriting() {
        if (!ProjectTypeEnum.UNDERWRITING.equals(serviceType.getProjectType())) {
            return null;
        }
        return Underwriting.UnderwritingBuilder.newBuilder().setServiceType(serviceType).build();
    }

    public Collection<Contact> getContact() {
        return Stream.of(getAgency(), getInsured())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public Contact getAgency() {
        var taskAgency = task.getAgency();
        if (taskAgency == null) {
            return null;
        }
        var builder = Message.ProjectMessage.Contact.newBuilder();
        ofNullable(taskAgency.getName()).ifPresent(builder::setFullName);
        ofNullable(taskAgency.getPhone()).ifPresent(builder::setPrimaryPhone);
        var message = builder.build();
        if (Message.ProjectMessage.Contact.getDefaultInstance().equals(message)) {
            return null;
        }
        builder.setRole(ContactRoleEnum.AGENT.getName());
        return Contact.of(builder.build());
    }

    @Nullable
    public Contact getInsured() {
        var firstName = getCustomStringField("Contact First Name");
        var lastName = getCustomStringField("Contact Last Name");
        var email = getCustomStringField("Contact Email");
        var phone = getCustomStringField("Contact Phone");

        var builder = Message.ProjectMessage.Contact.newBuilder();
        builder.setFullName(StringUtils.trim(firstName + " " + lastName));
        builder.setPrimaryEmail(email);
        builder.setPrimaryPhone(phone);
        if (builder.build().equals(Message.ProjectMessage.Contact.getDefaultInstance())) {
            return null;
        }
        builder.setRole(ContactRoleEnum.INSURED.getName());
        return Contact.of(builder.build());
    }

    private <T> T getCustomField(String fieldName, Function<Object, T> converter, T defaultValue) {
        return ofNullable(extensionFields.get(fieldName)).map(converter).orElse(defaultValue);
    }

    private <T> T getCustomField(String fieldName, Function<Object, T> converter) {
        return getCustomField(fieldName, converter, null);
    }

    private String getCustomStringField(String fieldName) {
        return getCustomField(fieldName, value -> value.toString(), "");
    }

    public String getNotes() {
        return ofNullable(task.getNotes()).orElse("");
    }

    public List<String> getSupplementalServices() {
       return supplementalServices;
    }
}
