package com.bees360.job;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.dto.ProjectDto;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.job.registry.CreateProjectFromRctIntegrationJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.rct.RctApi;
import com.bees360.sdk.rct.api.TasksApi;
import com.bees360.sdk.rct.model.TaskDto;
import com.bees360.service.ProjectService;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.function.BiFunction;
import java.util.function.Function;

@Log4j2
public class CreateProjectFromRctIntegrationJobExecutor extends AbstractJobExecutor<CreateProjectFromRctIntegrationJob> {

    private final RctApi<TasksApi> taskRctApi;
    /**
     * get user id for dataset
     */
    private final Function<String, Long> creatorProvider;

    /**
     * convert ProjectDto with dataset and TaskDto
     */
    private final BiFunction<String, TaskDto, ProjectDto> taskProjectConverter;

    private final ProjectService projectService;

    private final ExternalIntegrationManager externalIntegrationManager;

    private final CreationChannelType CREATION_CHANNEL = CreationChannelType.RISK_CONTROL;

    public CreateProjectFromRctIntegrationJobExecutor(
        @NonNull RctApi<TasksApi> taskRctApi,
        @NonNull Function<String, Long> rctCreatorProvider,
        @NonNull BiFunction<String, TaskDto, ProjectDto> taskProjectConverter,
        @NonNull ProjectService projectService,
        @NonNull ExternalIntegrationManager externalIntegrationManager) {
        this.taskRctApi = taskRctApi;
        this.taskProjectConverter = taskProjectConverter;
        this.creatorProvider = rctCreatorProvider;
        this.projectService = projectService;
        this.externalIntegrationManager = externalIntegrationManager;
        log.info("Created {}", this);
    }

    @Override
    protected void handle(CreateProjectFromRctIntegrationJob job) throws IOException {
        log.info("Received job: {}", job);
        var integration = job.getProjectIntegration();
        var dataset = integration.getDataset();
        var taskId = integration.getReferenceNumber();

        var task = taskRctApi.apply(api -> api.getTaskById(taskId, null, null));
        var projectDto = taskProjectConverter.apply(dataset, task);
        projectDto.setCreationChannel(CREATION_CHANNEL.name());

        var createdBy = creatorProvider.apply(dataset);

        try {
            var projectCreated = projectService.createProject(createdBy, projectDto);
            externalIntegrationManager.setProjectId(integration.getId(), projectCreated.getProjectId() + "");
        } catch (ServiceException e) {
            throw new IllegalStateException(e);
        }
    }
}
