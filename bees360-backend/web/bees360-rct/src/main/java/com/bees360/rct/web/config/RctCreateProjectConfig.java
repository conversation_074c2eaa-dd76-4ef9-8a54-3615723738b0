package com.bees360.rct.web.config;

import com.bees360.building.Message.BuildingType;
import com.bees360.contract.Contract;
import com.bees360.contract.ContractManager;
import com.bees360.entity.dto.ProjectDto;
import com.bees360.job.CreateProjectFromRctIntegrationJobExecutor;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.claim.ClaimTypeEnum;
import com.bees360.rct.RctApi;
import com.bees360.sdk.rct.ApiClient;
import com.bees360.sdk.rct.api.TasksApi;
import com.bees360.sdk.rct.model.TaskDto;

import com.bees360.service.ProjectService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;

@Configuration
public class RctCreateProjectConfig {

    @Bean
    TasksApi rctTasksApi(ApiClient rctApiClient) {
        return new TasksApi(rctApiClient);
    }

    @Bean
    public CreateProjectFromRctIntegrationJobExecutor createProjectFromRctIntegrationJobExecutor(
                    TasksApi rctTasksApi,
                    Function<String, Long> rctCreatorProvider,
                    BiFunction<String, TaskDto, ProjectDto> rctTaskProjectConverter,
                    ProjectService projectService,
                    ExternalIntegrationManager externalIntegrationManager) {

        var taskRctApi = RctApi.of(rctTasksApi);
        return new CreateProjectFromRctIntegrationJobExecutor(
                taskRctApi,
                rctCreatorProvider,
                rctTaskProjectConverter,
                projectService,
                externalIntegrationManager);
    }

    @Bean
    BiFunction<String, TaskDto, ProjectDto> rctTaskProjectConverter(
        @Qualifier("rctSupplementalServiceMappings") Function<TaskDto, Collection<String>> rctSupplementalServiceMappings,
        @Qualifier("rctContractProvider") Function<String, Contract> rctContractProvider,
        Function<TaskDto, ServiceTypeEnum> rctServiceTypeMappings,
        Function<TaskDto, ClaimTypeEnum> rctClaimTypeMappings,
        Function<TaskDto, BuildingType> rctBuildingTypeMappings) {
        var converter = new TaskProjectConverter(
            rctSupplementalServiceMappings,
            rctContractProvider,
            rctServiceTypeMappings,
            rctClaimTypeMappings,
            rctBuildingTypeMappings);
        return converter::convert;
    }

    @Bean
    public Function<String, Contract> rctContractProvider(RctConfig.RctProperties rctProperties, ContractManager contractManager) {
        final var contract = rctProperties.getDataset().getContract();
        return (dataset) -> contractManager.findByCompanyId(contract.getInsuredBy(), contract.getProcessedBy());
    }

    @Bean
    public Function<String, Long> rctCreatorProvider(RctConfig.RctProperties rctProperties) {
        final var creator = rctProperties.getDataset().getCreator();
        return (dataset) -> creator;
    }

    @Bean
    public Function<TaskDto, Collection<String>> rctSupplementalServiceMappings(RctConfig.RctProperties rctProperties) {
        return task -> {
            var mapping = rctProperties.getDataset().getSupplementalServiceMapping();
            if (mapping == null) {
                return List.of();
            }
            var value = mapping.apply(task);
            return value == null? List.of(): List.of(value);
        };
    }

    @Bean
    public Function<TaskDto, ServiceTypeEnum> rctServiceTypeMappings(RctConfig.RctProperties rctProperties) {

        var mappings = rctProperties.getDataset().getServiceTypeMapping();

        return task -> {
            var value = mappings.apply(task);
            return ServiceTypeEnum.valueOf(value);
        };
    }

    @Bean
    public Function<TaskDto, ClaimTypeEnum> rctClaimTypeMappings() {
        return task -> ClaimTypeEnum.OTHERS;
    }

    @Bean
    public Function<TaskDto, BuildingType> rctBuildingTypeMappings() {
        return task -> BuildingType.RESIDENTIAL_SINGLE_FAMILY;
    }
}
