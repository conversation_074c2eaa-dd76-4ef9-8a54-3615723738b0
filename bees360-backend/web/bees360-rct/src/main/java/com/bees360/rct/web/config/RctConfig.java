package com.bees360.rct.web.config;

import com.bees360.rct.RctHttpClientConfig;
import com.bees360.rct.RctOAuthConfig;
import com.google.gson.Gson;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@Import({
    RctHttpClientConfig.class,
    RctCreateProjectConfig.class,
})
@Configuration
@ConditionalOnProperty(prefix = "rct", name = "disable", havingValue = "false", matchIfMissing = true)
public class RctConfig {

    @Bean
    @ConfigurationProperties(prefix = "rct")
    public RctProperties rctProperties() {
        return new RctProperties();
    }

    @Import({
        RctOAuthConfig.class,
    })
    @Configuration
    @ConditionalOnProperty(prefix = "rct.auth", name = "disable", havingValue = "false", matchIfMissing = true)
    public static class OAuthConfig {}

    @Data
    @Validated
    public static class MappingStrategy {

        private String path;
        private HashMap<String, String> map = new HashMap<>();
        private String defaultValue;

        private final Gson GSON = new Gson();

        public String apply(Object obj) {
            if (MapUtils.isEmpty(map)) {
                return defaultValue;
            }
            try {
                String value = JsonPath.parse(GSON.toJson(obj)).read(path, String.class);
                return map.getOrDefault(value, defaultValue);
            } catch (PathNotFoundException ex) {
                return defaultValue;
            }
        }
    }

    @Data
    @Validated
    public static class RctProperties {

        @Valid
        @NotNull
        private RctDatasetProperties dataset;

        @Data
        @Validated
        public static class RctDatasetProperties {

            @NotEmpty
            @NotNull
            private String id;

            @Valid
            @NotNull
            private RctContractProperties contract;
            @NotNull
            private Long creator;

            private MappingStrategy serviceTypeMapping;

            private MappingStrategy supplementalServiceMapping;
        }

        @Data
        @Validated
        public static class RctContractProperties {
            @NotEmpty
            @NotNull
            private String insuredBy;
            @NotEmpty
            @NotNull
            private String processedBy;
        }
    }
}
