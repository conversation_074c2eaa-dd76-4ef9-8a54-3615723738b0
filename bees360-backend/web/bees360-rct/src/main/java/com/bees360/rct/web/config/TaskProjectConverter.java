package com.bees360.rct.web.config;

import com.bees360.address.Address;
import com.bees360.building.Message;
import com.bees360.contract.Contract;
import com.bees360.customer.Customer;
import com.bees360.entity.dto.ProjectDto;
import com.bees360.policy.Policy;
import com.bees360.project.Building;
import com.bees360.project.Contact;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.claim.Claim;
import com.bees360.project.claim.ClaimTypeEnum;
import com.bees360.project.inspection.Inspection;
import com.bees360.project.underwriting.Underwriting;
import com.bees360.sdk.rct.model.TaskDto;
import com.bees360.util.DateTimes;
import com.bees360.rct.web.TaskAccessor;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.util.Collection;
import java.util.Optional;
import java.util.function.Function;

import static java.util.Optional.ofNullable;

@Log4j2
class TaskProjectConverter {
    private final Function<TaskDto, Collection<String>> supplementalServiceMappings;

    private final Function<String, Contract> contractProvider;

    private final Function<TaskDto, ServiceTypeEnum> serviceTypeMappings;

    private final Function<TaskDto, ClaimTypeEnum> claimTypeMappings;

    private final Function<TaskDto, Message.BuildingType> buildingTypeMappings;

    public TaskProjectConverter(
        @NonNull Function<TaskDto, Collection<String>> supplementalServiceMappings,
        @NonNull Function<String, Contract> contractProvider,
        @NonNull Function<TaskDto, ServiceTypeEnum> serviceTypeMappings,
        @NonNull Function<TaskDto, ClaimTypeEnum> claimTypeMappings,
        @NonNull Function<TaskDto, Message.BuildingType> buildingTypeMappings) {
        this.supplementalServiceMappings = supplementalServiceMappings;
        this.contractProvider = contractProvider;
        this.serviceTypeMappings = serviceTypeMappings;
        this.claimTypeMappings = claimTypeMappings;
        this.buildingTypeMappings = buildingTypeMappings;
        log.info("Created {}", this);
    }

    public ProjectDto convert(String dataset, TaskDto task) {
        var contract = contractProvider.apply(dataset);
        var taskAccessor = new TaskAccessor(
                task,
                supplementalServiceMappings,
                serviceTypeMappings,
                claimTypeMappings,
                buildingTypeMappings);

        return convertProjectDto(taskAccessor, contract);
    }

    private ProjectDto convertProjectDto(TaskAccessor taskAccessor, Contract contract) {
        var project = new ProjectDto();
        Optional.ofNullable(taskAccessor.getSupplementalServices()).ifPresent(project::setSupplementalServices);
        assembleInspection(project, taskAccessor.getInspection());
        assembleContract(project, contract);
        assemblePolicy(project, taskAccessor.getPolicy());
        assembleUnderwriting(project, taskAccessor.getUnderwriting());
        assembleClaim(project, taskAccessor.getClaim());
        assembleInsured(project, taskAccessor.getInsured());
        assembleAgency(project, taskAccessor.getAgency());

        project.setClaimNote(taskAccessor.getNotes());

        return project;
    }

    private void assembleInsured(ProjectDto project, Contact insured) {
        if (insured == null) {
            return;
        }
        ofNullable(insured.getFullName()).ifPresent(project::setAssetOwnerName);
        ofNullable(insured.getPrimaryEmail()).ifPresent(project::setAssetOwnerEmail);
        ofNullable(insured.getPrimaryPhone()).ifPresent(project::setAssetOwnerPhone);
    }

    private void assembleAgency(ProjectDto project, Contact agency) {
        if (agency == null) {
            return;
        }
        ofNullable(agency.getFullName()).ifPresent(project::setAgentContactName);
        ofNullable(agency.getPrimaryEmail()).ifPresent(project::setAgentEmail);
        ofNullable(agency.getPrimaryPhone()).ifPresent(project::setAgentPhone);
    }

    private void assembleInspection(ProjectDto project, Inspection inspection) {
        if (inspection == null) {
            return;
        }
        ofNullable(inspection.getInspectionNo()).ifPresent(project::setInspectionNumber);
    }

    private void assembleUnderwriting(ProjectDto project, Underwriting underwriting) {
        if (underwriting == null) {
            return;
        }
        project.setServiceType(underwriting.getServiceType().getCode());
        project.setClaimType(com.bees360.entity.enums.ClaimTypeEnum.UNDERWRITING_FIRST_INSPECTION.getCode());
    }

    private void assembleClaim(ProjectDto project, Claim claim) {
        if (claim == null) {
            return;
        }
        ofNullable(claim.getServiceType()).map(ServiceTypeEnum::getCode).ifPresent(project::setServiceType);
        ofNullable(claim.getClaimType()).map(ClaimTypeEnum::getCode).ifPresent(project::setClaimType);
        ofNullable(claim.getClaimNo()).ifPresent(project::setClaimNumber);
        ofNullable(claim.getDateOfLoss()).map(date -> DateTimes.toEpochMilli(date)).ifPresent(project::setDamageEventTime);
    }

    private void assembleContract(ProjectDto project, Contract contract) {
        if (contract == null) {
            return;
        }
        var insuredBy = ofNullable(contract.getInsuredBy()).map(Customer::getId).map(Long::parseLong).orElse(null);
        var processedBy = ofNullable(contract.getProcessedBy()).map(Customer::getId).map(Long::parseLong).orElse(null);

        project.setInsuranceCompany(insuredBy);
        project.setRepairCompany(processedBy);
    }

    private void assemblePolicy(ProjectDto project, Policy policy) {
        if (policy == null) {
            return;
        }
        ofNullable(policy.getPolicyNo()).ifPresent(project::setPolicyNumber);
        ofNullable(policy.getType()).ifPresent(project::setPolicyType);
        ofNullable(policy.getPolicyEffectiveDate()).ifPresent(project::setPolicyEffectiveDate);

        assembleAddress(project, policy.getAddress());
        assembleBuilding(project, policy.getBuilding());
    }

    private void assembleBuilding(ProjectDto project, Building building) {
        if (building == null) {
            return;
        }
        ofNullable(building.getYearBuilt()).map(Object::toString).ifPresent(project::setYearBuilt);
    }

    private void assembleAddress(ProjectDto project, Address address) {
        if (address == null) {
            return;
        }
        project.setAddress(address.getStreetAddress());
        project.setCity(address.getCity());
        project.setState(address.getState());
        project.setCountry(address.getCountry());
        project.setZipCode(address.getZip());
        project.setLat(address.getLat());
        project.setLng(address.getLng());
    }

}
