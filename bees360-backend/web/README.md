Bees360 Web
===

###### `guanrong.yang` `2019/07/22`

Bees360 为客户提供服务的Web项目后端。

Quick Start
---

创建目录
```bash
sudo mkdir -p /var/bees360/
sudo chmod -R 777 /var/bees360/
```

添加必要的环境变量
```bash
# 请确保环境变量添加后立即生效
# 可以将下列环境变量配置到IDE中或者 $HOME/.bashrc中
export ENV="dev" # 开发环境
export BEES360_SECRET_KEY="${SECRET_KEY}" # 请向管理员询问${SECRET_KEY}值
```

开启本地Redis
```shell
# start redis
redis-server
# set password
config set requirepass ${password} # 请向管理员询问开发环境Redis密码
```

启动
```shell script
# 开发环境启动
cd upstream/bees360-backend/
make
# 然后在IDE中启动Bees360WebApplication类的main方法
```

软件安装
---

### ghostScript

官网：[How to Install Ghostscript](https://www.ghostscript.com/doc/current/Install.htm#Install_Unix)
软件列表：[ghostpdl-downloads/releases](https://github.com/ArtifexSoftware/ghostpdl-downloads/releases)
使用版本：[ghostscript-9.53.3](https://github.com/ArtifexSoftware/ghostpdl-downloads/releases/download/gs9533/ghostscript-9.53.3-linux-x86_64.tgz)

Security
---

认证授权由模块`bees360-security`进行实现。

web项目有两套认证，一套授权。认证分为内部系统认证和Open Api认证。其中内部认证为自定义的认证方式，而Open Api的认证准许你OAuth2.0的认证方式。

### 内部系统

**授权配置**

内部系统的授权配置可在`InnerAuthorizeConfigProvider`中进行配置。此外，也可以自行对`AuthorizeConfigProvider`进行实现，并注入到Spring IOC容器中即可。

### Open Api

内部系统的授权配置可在`OpenApiAuthorizeConfigProvider`中进行配置。此外，也可以自行对`AuthorizeConfigProvider`进行实现，并注入到Spring IOC容器中即可。

Open Api 相关文档见：[bees360/openapi](https://gitlab.com/bees360/openapi)

配置文件
---

### Properties Bean

配置的properties或者yaml文件均保存在模块`bees360-web`的resource中，而自定义的配置文件会注入到`com.bees360.web.core.properties.bean`中的POJO中。

### 加密

属性文件中，值被`ENC(`和`)`包含的为`script/jasypt.sh`进行加密的后的值，而被`AES(`和`)`包含的，则为`com.bees360.util.secret.AESUtil`加密后的值。
```
