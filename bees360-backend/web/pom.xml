<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bees360</groupId>
        <artifactId>bees360-parent</artifactId>
        <version>${revision}${changelist}</version>
        <relativePath>../parent/pom.xml</relativePath>
    </parent>

    <groupId>com.bees360.web</groupId>
    <artifactId>bees360</artifactId>
    <packaging>pom</packaging>
    <name>bees360</name>
    <description>bees360-web-backend</description>

    <modules>
        <module>bees360-core</module>
        <module>bees360-service</module>
        <module>bees360-utils</module>
        <module>bees360-entity</module>
        <module>bees360-web</module>
        <module>bees360-mapper</module>
        <module>bees360-web-grpc</module>
        <module>bees360-web-grpc-proto</module>
        <module>bees360-event</module>
        <module>bees360-web-grpc-service</module>
        <module>bees360-manager-project</module>
        <module>bees360-securityfirst</module>
        <module>bees360-rct</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- sub modules started -->
            <dependency>
                <groupId>com.bees360.web</groupId>
                <artifactId>bees360-web-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.web</groupId>
                <artifactId>bees360-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.web</groupId>
                <artifactId>bees360-utils</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.web</groupId>
                <artifactId>bees360-entity</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.web</groupId>
                <artifactId>bees360-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.web</groupId>
                <artifactId>bees360-mapper</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.web</groupId>
                <artifactId>bees360-web-grpc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.web</groupId>
                <artifactId>bees360-web-grpc-proto</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.web</groupId>
                <artifactId>bees360-event</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.web</groupId>
                <artifactId>bees360-web-grpc-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bees360.web</groupId>
                <artifactId>bees360-manager-project</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- ##### common utils start #####-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- ##### common utils end #####-->

        <!--log4j2-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <!-- spring boot config file encryption and decryption -->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot</artifactId>
        </dependency>
    </dependencies>

</project>
