package com.bees360.util;

import com.bees360.entity.Project;
import com.bees360.entity.dto.ProjectDto;

public class AddressUtil {

    public static String assembleFullAddress(ProjectDto projectDto) {
        Project project = new Project();
        project.setCountry(projectDto.getCountry());
        project.setZipCode(projectDto.getZipCode());
        project.setState(projectDto.getState());
        project.setCity(projectDto.getCity());
        project.setAddress(projectDto.getAddress());
        return project.getFullAddress();
    }
}
