package com.bees360.util.maps.common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bees360.base.exception.NotImplementedException;
import com.bees360.util.maps.model.LatLngPoint;
import com.vividsolutions.jts.algorithm.MinimumDiameter;
import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.geom.GeometryFactory;

public class MapCommonUtil {

	private MapCommonUtil() {
		throw new AssertionError("MapCommonUtil couldn't be instantiated");
	}

	/**
	 *
	 * @param lat Latitudes can take any value between -90 and 90
	 * @param lng Longitude values can take any value between -180 and 180
	 * @return
	 */
	public static boolean checkLatLng(double lat, double lng) {
		return checkLat(lat) && checkLng(lng);
	}

	/**
	 *
	 * @param lat Latitudes can take any value between -90 and 90
	 * @return
	 */
	public static boolean checkLat(double lat) {
		return -90 <= lat && lat <= 90;
	}

	/**
	 *
	 * @param lng Longitude values can take any value between -180 and 180
	 * @return
	 */
	public static  boolean checkLng(double lng) {
		return -180 <= lng && lng <= 180;
	}

	public static double area(List<LatLngPoint> polygon) {
		throw new NotImplementedException();
	}

	public static List<LatLngPoint> getMiniRect(List<LatLngPoint> gpsPolygon) {
		Coordinate[] coordinates = new Coordinate[gpsPolygon.size()];
		for(int i = 0; i < gpsPolygon.size(); i ++) {
			LatLngPoint latLng = gpsPolygon.get(i);
			coordinates[i] = new Coordinate(latLng.getLat(), latLng.getLng());
		}
		Geometry polygon = new GeometryFactory().createPolygon(coordinates);
		Geometry mbr = MinimumDiameter.getMinimumRectangle(polygon.buffer(0.000045));
		return geomToPoints(mbr);
	}

	public static List<LatLngPoint> geomToPoints(Geometry geometry) {
		if(geometry == null) {
			return new ArrayList<LatLngPoint>();
		}
		Coordinate[] coordinates = geometry.getCoordinates();

		List<LatLngPoint> points = new ArrayList<LatLngPoint>(coordinates.length);

		for(int i  =  0; i < coordinates.length; i++) {
			points.add(new LatLngPoint(coordinates[i].y, coordinates[i].x));
		}
		return points;
	}

	/**
	 * create result map, The parameters must be arranged by key-value pairs.
	 * @param objs the key value
	 * @return map
	 */
	public static Map<String, Object> getResultMap(Object... objs) {
		return new HashMap<String, Object>(){
			private static final long serialVersionUID = -1857966932251105053L;
			{
				for (int i = 0; i < objs.length; i+=2) {
					put(objs[i].toString(), objs[i + 1]);
				}
			}
		};
	}

	/**
	 * calculat the distanch of two GPS points
	 * @param from
	 * @param to
	 * @return the distance of two GPS points. The unit of the result is mile.
	 */
	public static double getGPSDistance(LatLngPoint from, LatLngPoint to) {
		double earthRadius = 3959;
		double part1 = Math.cos( Math.toRadians(from.getLat()) )
		        * Math.cos( Math.toRadians(to.getLat()) )
		        * Math.cos( Math.toRadians(to.getLng()) - Math.toRadians(from.getLng()) );

		double part2 = Math.sin( Math.toRadians(from.getLat()) )
				* Math.sin( Math.toRadians(to.getLat()) );

		return earthRadius * Math.acos(part1 + part2);
	}

	public static double getGPSDistance(double lat1, double lng1, double lat2, double lng2) {
		return getGPSDistance(new LatLngPoint(lat1, lng1), new LatLngPoint(lat2, lng2));
	}

	public static void main(String[] args) {
		LatLngPoint from = new LatLngPoint(32.8474239, -96.8512869);
		LatLngPoint to = new LatLngPoint(32.8895519, -96.9144130);

		System.out.println(MapCommonUtil.getGPSDistance(from, to));
	}
}
