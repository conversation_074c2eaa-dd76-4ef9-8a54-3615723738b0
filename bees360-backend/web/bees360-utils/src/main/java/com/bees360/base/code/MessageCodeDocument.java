package com.bees360.base.code;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MessageCodeDocument {
    /**
     * 面向开发人员的可读信息，也可以是一个指向一个详细信息连接。
     * 解释了该错误并且给出可实施的解决方案。
     */
    String moreInfo() default "";

    /**
     * 类型，客户端可以针对不同的类型做不同的操作。
     *
     */
    ApiErrorType type();
}
