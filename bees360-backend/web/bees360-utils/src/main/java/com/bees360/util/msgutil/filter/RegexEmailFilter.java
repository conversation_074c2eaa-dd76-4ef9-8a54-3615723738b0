package com.bees360.util.msgutil.filter;

import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.thymeleaf.util.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import jakarta.annotation.Nonnull;

/**
 * <AUTHOR>
 */
public class RegexEmailFilter implements EmailFilter {

    private boolean includeAll = false;

    private boolean excludeAll = false;

    /**
     * 允许发送的邮箱。
     */
    private List<String> emailRegexInclude = Lists.newArrayList();
    /**
     * 不允许发送的邮箱，如果同时符合include，则exclude。
     */
    private List<Pattern> emailRegexExclude = Lists.newArrayList();
    /**
     * 允许发送的邮箱。
     */
    private Set<String> emailInclude = Sets.newHashSet();
    /**
     * 不允许发送的邮箱，如果同时出现在include中，则exclude。
     */
    private Set<String> emailExclude = Sets.newHashSet();

    public RegexEmailFilter() {
        this.includeAll = true;
    }

    public static RegexEmailFilter newFilterIncludeAll() {
        RegexEmailFilter filter = new RegexEmailFilter();
        filter.setRegexInclude(Lists.newArrayList("*"));
        return filter;
    }

    public static RegexEmailFilter newFilterExcludeAll() {
        RegexEmailFilter filter = new RegexEmailFilter();
        filter.setRegexExclude(Lists.newArrayList("*"));
        return filter;
    }

    @Override
    public List<String> filter(List<String> emails) {
        emails = Lists.newArrayList(emails);
        filterInclude(emails);
        filterExclude(emails);
        return emails;
    }

    private void filterInclude(List<String> emails) {
        if (includeAll || emails.isEmpty()) {
            return;
        }
        Iterator<String> iter = emails.iterator();
        while (iter.hasNext()) {
            String email = iter.next();
            boolean anyMatch = emailInclude.contains(email) || emailRegexInclude.stream().anyMatch(email::matches);
            if (!anyMatch) {
                iter.remove();
            }
        }
    }

    private void filterExclude(List<String> emails) {
        if (excludeAll) {
            emails.removeIf(e -> true);
            return;
        }
        if (CollectionUtils.isEmpty(emails)) {
            return;
        }
        if(!CollectionUtils.isEmpty(emailExclude)) {
            var iter = emails.iterator();
            while(iter.hasNext()) {
                if (emailExclude.contains(iter.next())) {
                    iter.remove();
                }
            }
        }
        for (Pattern pattern : emailRegexExclude) {
            excludeIfMatch(emails, pattern);
        }
    }

    private void excludeIfMatch(List<String> emails, Pattern emailPatterns) {
        Matcher matcher = emailPatterns.matcher("");
        Iterator<String> iter = emails.iterator();
        while (iter.hasNext()) {
            boolean match = matcher.reset(iter.next()).matches();
            if (match) {
                iter.remove();
            }
        }
    }

    private List<Pattern> toPattern(List<String> regexes) {
        return regexes.stream().map(Pattern::compile).collect(Collectors.toList());
    }

    public RegexEmailFilter setRegexInclude(@Nonnull List<String> regexInclude) {
        this.includeAll = regexInclude.stream().anyMatch(reg -> StringUtils.equals(reg, "*"));
        if (this.includeAll) {
            this.includeAll = true;
            this.emailRegexInclude = Lists.newArrayList();
        } else {
            this.emailRegexInclude = Lists.newArrayList(regexInclude);
        }
        return this;
    }

    public RegexEmailFilter setRegexExclude(@Nonnull List<String> regexExclude) {
        this.excludeAll = regexExclude.stream().anyMatch(reg -> StringUtils.equals(reg, "*"));
        if (this.excludeAll) {
            this.excludeAll = true;
            this.emailRegexExclude = Lists.newArrayList();
        } else {
            this.emailRegexExclude = toPattern(regexExclude);
        }
        return this;
    }

    public RegexEmailFilter setEmailInclude(@Nonnull List<String> emailInclude) {
        this.emailInclude = Sets.newHashSet(emailInclude);
        return this;
    }

    /**
     * @param emailExclude
     *            不发送邮件的email，
     */
    public RegexEmailFilter setEmailExclude(@Nonnull List<String> emailExclude) {
        this.emailExclude = Sets.newHashSet(emailExclude);
        return this;
    }

    private enum OptType {
        INCLUDE, EXCLUDE
    }
}
