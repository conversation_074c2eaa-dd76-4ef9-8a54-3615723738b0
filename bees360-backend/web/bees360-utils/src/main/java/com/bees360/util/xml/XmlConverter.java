package com.bees360.util.xml;

import org.apache.commons.io.FileUtils;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Unmarshaller;
import javax.xml.transform.stream.StreamSource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 */
public class XmlConverter {

    /**
     * xml转javaBean
     */
    public static <T> T toObject(File file, Class<T> c) throws IOException {
        try (InputStream inputStream = FileUtils.openInputStream(file)) {
            return toObject(inputStream, c);
        }
    }

    /**
     * xml转javaBean
     */
    public static <T> T toObject(String input, Class<T> c) throws IOException {
        try (InputStream inputStream = new ByteArrayInputStream(input.getBytes())) {
            return toObject(inputStream, c);
        }
    }

    /**
     * xml转javaBean
     */
    public static <T> T toObject(InputStream inputStream, Class<T> c) {
        try {
            JAXBContext context = JAXBContext.newInstance(c);
            Unmarshaller unmarshaller = context.createUnmarshaller();
            JAXBElement<T> t = unmarshaller.unmarshal(new StreamSource(inputStream), c);
            return t.getValue();
        } catch (JAXBException e) {
            throw new IllegalArgumentException("Unable to convert input to " + c.getName(), e);
        }
    }

    /**
     * xml转javaBean
     */
    public static String toXml(Object obj) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            JAXBContext ctx = JAXBContext.newInstance(obj.getClass());
            Marshaller marshaller = ctx.createMarshaller();
            marshaller.marshal(obj, outputStream);
            return outputStream.toString();
        } catch (JAXBException | IOException e) {
            throw new IllegalArgumentException("Unable to convert object of " + obj.getClass() + " to xml.", e);
        }
    }
}
