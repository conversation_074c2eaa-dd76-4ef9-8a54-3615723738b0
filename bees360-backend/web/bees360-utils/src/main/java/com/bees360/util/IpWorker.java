package com.bees360.util;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.List;

import com.bees360.util.file.FtpDownloader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * get the ip of USA from the file following:
 * 		ftp://ftp.arin.net/pub/stats/arin/delegated-arin-extended-latest
 * 		the format of echo line of the file:  registry|cc|type|start|value|date|status[|extensions...]
 * 		example: arin|US|ipv4|*******|65536|19881216|assigned|ab226dc561ca2adc9923b06dc9c8c0bf
 * 		calculate the range of ip of an country: startIp = start, endIp = start + value - 1
 *
 * ref: https://www.nowcoder.com/profile/3566692/codeBookDetail?submissionId=57191868
 */
// <EMAIL>: make it more save when init the data, and deal with the exception when init.
// <EMAIL>: 这个类需要重新修改，将所有的路径相关属性作为参数传入该类中，而不是通过 FileManager 进入
public abstract class IpWorker {

	private Logger logger = LoggerFactory.getLogger(IpWorker.class);

	protected int[][] ipRanges = null;

	private static class IpRange {
		public int start;
		public int end;

		public IpRange(int start, int end) {
			this.start = start;
			this.end = end;
		}

		@Override
		public String toString() {
			return "IpRange [start=" + start + ", end=" + end + "]";
		}
	}

	/**
	 * Initialize the ipRanges like copyOnWriteArray.
	 * Don't change a single element of the ipRanges for Thread safety.
	 */
	// String prefix = "arin|US|ipv4|";
	protected abstract void init();

	protected void initIpRanges(File ipsFile, String prefix){
		List<IpRange> ipRangeList = new ArrayList<IpRange>();
		BufferedReader reader;
		try {
			reader = new BufferedReader(new InputStreamReader(new FileInputStream(ipsFile)));
			String ipInfoStr = null;
			while ((ipInfoStr = reader.readLine()) != null) {
				if (!ipInfoStr.startsWith(prefix)) {
					continue;
				}
				String[] segments = ipInfoStr.split("\\|");
				String startIp = segments[3];
				String value = segments[4];
				int intIp = 0;
				int ipCount = 0;
				try {
					intIp = ipToInt(startIp);
					ipCount = Integer.valueOf(value);
				} catch (UnknownHostException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				ipRangeList.add(new IpRange(intIp, intIp + ipCount - 1));
			}
		} catch (FileNotFoundException e) {
			logger.error("Fail to initialize ipWorker, " + ipsFile.getAbsolutePath() + " dosen't exist", e);
		} catch (IOException e) {
			logger.error("Fail to initialize ipWorker during reading the file");
		}

		sortAsc(ipRangeList);
		int[][] ipRanges = new int[2][ipRangeList.size()];
		for(int i = 0; i < ipRangeList.size(); i ++){
			IpRange range = ipRangeList.get(i);
			ipRanges[0][i] = range.start;
			ipRanges[1][i] = range.end;
		}
		logger.info("Initialize IpWorker successfully, there are " + ipRangeList.size() + " ipRanges.");
		// this code and isUSAIp(String ip) make sure the operation initialize ipRanges and check ip thread safety.
		this.ipRanges = ipRanges;
	}

	public int ipRangesSize(){
		return ipRanges == null? 0: ipRanges[0].length;
	}

	/**
	 * ascending sort
	 */
	private void sortAsc(List<IpRange> ipRangeList){
		if(ipRangeList == null || ipRangeList.size() == 0){
			return;
		}
		ipRangeList.sort(new Comparator<IpRange>(){

			@Override
			public int compare(IpRange range1, IpRange range2) {
				// null will be the largest value
				if(range1 == null){
					if(range2 == null) {
						return 0;
					} else {
						return 1;
					}
				} else if (range2 == null){
					return -1;
				}
				if(range1.start == range2.start){
					return 0;
				}
				return range1.start > range2.start ? 1 : -1;
			}

		});
	}

	/**
	 * 每个ip可以划分为四个部分，每个部分的取值范围为[0~255]，byte的范围为[-128~127],
	 * 一个byte为8位，int为32位，正好 4 * length(byte) = length(int), 因而可以用一个int表示一个ip
	 */
	public static int ipToInt(String ip) throws UnknownHostException {
		final byte[] bytes = InetAddress.getByName(ip).getAddress();
		int addr = bytes[3] & 0xFF;
		addr |= ((bytes[2] << 8) & 0xFF00);
		addr |= ((bytes[1] << 16) & 0xFF0000);
		addr |= ((bytes[0] << 24) & 0xFF000000);
		return addr;
	}

	public static String intToIp(int ip) {
		final StringBuilder ipStr = new StringBuilder();
		ipStr.append(String.valueOf(ip >>> 24)).append(".");
		ipStr.append(String.valueOf(((ip & 0xFF0000) >>> 16))).append(".");
		ipStr.append(String.valueOf(((ip & 0xFF00) >>> 8))).append(".");
		ipStr.append(String.valueOf(ip & 0xFF));
		return ipStr.toString();
	}

	public static List<String> listLocalIPs() throws SocketException {
        List<String> ipList = new ArrayList<String>();
        NetworkInterface networkInterface;
        Enumeration<InetAddress> inetAddresses;
        InetAddress inetAddress;
        String ip;
        Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
        while (networkInterfaces.hasMoreElements()) {
            networkInterface = networkInterfaces.nextElement();
            inetAddresses = networkInterface.getInetAddresses();
            while (inetAddresses.hasMoreElements()) {
                inetAddress = inetAddresses.nextElement();
                if (inetAddress != null && inetAddress instanceof Inet4Address) {
                    ip = inetAddress.getHostAddress();
                    ipList.add(ip);
                }
            }
        }
        return ipList;
    }

	public static String getLocalIP() throws SocketException{
        Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
        while (networkInterfaces.hasMoreElements()) {
        	NetworkInterface networkInterface = networkInterfaces.nextElement();
        	Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
        	while (inetAddresses.hasMoreElements()) {
            	InetAddress inetAddress = inetAddresses.nextElement();
                if (inetAddress != null && inetAddress instanceof Inet4Address) {
                    return inetAddress.getHostAddress();
                }
            }
        }
        return null;
	}

	public boolean ipRangesToFile(File destFile) throws IOException {
		BufferedWriter writer = null;
        try {
        	writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(destFile, false)));

            for(int i = 0; i < ipRanges[0].length; i ++){
            	String line = intToIp(ipRanges[0][i]) + "|" + intToIp(ipRanges[1][i]) + "|" + ipRanges[0][i] + "|" + ipRanges[1][i];
            	writer.write(line);
            	writer.newLine();
            }
        	writer.flush();
        } finally {
            if(writer != null){
                try {
                	writer.close();
                	writer = null;
				} catch (IOException e) {
					e.printStackTrace();
				}
            }
        }
        return destFile.exists();
	}

	/**
	 * judge the ip belongs to USA or not. If the ipRanges is null, return true as default.
	 * @param ip the ip that need to be judged.
	 * @return
	 * 		true if the ip belongs to USA or fail to initialize ipRanges which make ipRanges null
	 * 		false if the ip dosen't belong to USA
	 * @throws UnknownHostException
	 */
	public boolean isInRanges(String ip) {
		return isInRanges(ipRanges, ip);
	}

	public int getRangesSize() {
		return ipRanges[0].length;
	}

	/**
	 *  judge the ip belongs to USA or not. If the ipRanges is null, return true as default.
	 * @param ipRanges the old ipRanges. This parameter the judgement safe,
	 * 		for it is the old ipRanges when updating the ipRanges.
	 * @param ip
	 * @return
	 * @throws UnknownHostException
	 */
	protected boolean isInRanges(int[][] ipRanges, String ip) {
		if(ipRanges == null || ipRanges.length != 2 || ipRanges[0].length == 0){
			logger.warn("IpWorker hasn't been normally initialized.");
			return true;
		}
		if(ip == null || ip.length() == 0){
			return false;
		}
		int ipValue = 0;
		try {
			ipValue = ipToInt(ip);
		} catch (UnknownHostException e) {
			return false;
		}
		int left = 0;
		int right = ipRanges[0].length - 1;
		// ipRanges[0][left] is the lowest ip
		// ipRanges[1][right] is the largest ip
		if(ipValue < ipRanges[0][left] || ipRanges[1][right] < ipValue){
			return false;
		}
		while (left <= right) {
			int middle = (left + right) / 2;
			if (ipRanges[0][middle] <= ipValue && ipValue <= ipRanges[1][middle]) {
				return true;
			} else if (ipRanges[0][middle] < ipValue) {
				left = middle + 1;
			} else {
				right = middle - 1;
			}
		}
		return false;
	}
}
