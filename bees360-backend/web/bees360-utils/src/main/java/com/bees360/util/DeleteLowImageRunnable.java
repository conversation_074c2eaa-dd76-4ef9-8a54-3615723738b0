package com.bees360.util;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.bees360.util.file.SimpleFileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DeleteLowImageRunnable implements Runnable {
	private static Logger logger = LoggerFactory.getLogger(DeleteLowImageRunnable.class);

	// 24 hours
	private static long EXPIRED_TIME_LONG_LIMITED = 24 * 3600000;

	/**
	 * <AUTHOR>
	 */
	@Override
	public void run() {
		deleteOldImages();
	}

	private void deleteOldImages(){
		File projectsRootDir = new File(FileManager.ROOT_PROJECTS_DIR);
		if (!projectsRootDir.exists()) {
			return;
		}
		logger.info("Try to delete the thumbnails in " + FileManager.ROOT_PROJECTS_DIR +
				", whose last modified time is 24h ago.");
		long now = System.currentTimeMillis();
		String[] projectIds = projectsRootDir.list();
		if(projectIds != null){
			for(String projectIdStr: projectIds){
				long projectId = -1;
				try{
					projectId = Long.parseLong(projectIdStr);
				} catch (Exception e){
					logger.info(projectsRootDir.getAbsolutePath() + projectIdStr + " is not an project dir.");
					continue;
				}
				// modify by shoushan.zhao
				String dirPath = FileManager.ROOT_PROJECT_DIR.replace("${projectId}", projectId + "");
				deleteImagesByProjectId(dirPath, projectId, now);
			}
		}
	}

	private static void deleteImagesByProjectId(String dirPath, long projectId, long now) {
		File dir = new File(dirPath);
		if(dir.exists() && dir.list() != null && dir.list().length > 0){
			boolean hasTimestamp = false;
			boolean hasDir = false;
			File[] files = dir.listFiles();
			for (File file : files) {
				if ("timestamp".equals(file.getName()) && file.isDirectory()) {
					hasTimestamp = true;
				}
				if (!"timestamp".equals(file.getName()) && file.isDirectory()) {
					hasDir = true;
				}
			}
			if (hasTimestamp) {
				String timestampUrl = dirPath;
				if (!File.separator.equals(timestampUrl.substring(timestampUrl.length() - 1))) {
					timestampUrl = timestampUrl + File.separator;
				}
				timestampUrl = timestampUrl + "timestamp" + File.separator + "timestamp";
				File timestamp = new File(timestampUrl);
				deleteExecution(projectId, now, dirPath, timestamp);
			} else if (hasDir) {
				for (File file : files) {
					if (file.isDirectory()) {
						deleteImagesByProjectId(file.getAbsolutePath(), projectId, now);
					}
				}
			}
		}
		if(dir.exists() && (dir.list() == null || dir.list().length <= 0)){
			SimpleFileUtil.removeDirectory(dirPath, true);
		}
	}

	private static void deleteExecution(long projectId, long now,
			String imageDirPath, File timestamp) {
		if(!timestamp.exists()){
			// There are not timestamp file.
			File imageDir = new File(imageDirPath);
			if(imageDir.exists()){
				String[] files = imageDir.list();
				if(files != null && files.length > 0){
					// There are some images, but without timestamp file.
					// Create an timestamp file to record the time.
					File timestampDir = timestamp.getParentFile();
					timestampDir.mkdirs();
					try {
						timestamp.createNewFile();
					} catch (IOException e) {
						logger.error("Fail to create timestamp file " + timestamp.getAbsolutePath());
					}
				}
			}
			return;
		}

		long lastDrawAnnotationTime = timestamp.lastModified();
		long interval = now - lastDrawAnnotationTime;
		if(EXPIRED_TIME_LONG_LIMITED < interval){
			// The last time to draw annotations 20 hours before, empty the direction.
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        Date date = new Date(lastDrawAnnotationTime);
			logger.info("The last time to draw annotations for project(" + projectId + ") is "
					+ sdf.format(date) + ", " + (interval / 3600000.0) + " hours ago.");
			logger.info("Delete all images in " + imageDirPath);
			SimpleFileUtil.removeDirectory(imageDirPath, true);
		}
	}
}
