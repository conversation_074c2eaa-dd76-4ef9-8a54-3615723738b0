package com.bees360.util.project;

import com.bees360.common.constants.Punctuation;
import com.google.common.base.Preconditions;
import org.springframework.stereotype.Component;

/**
 * 将open-api的请求路径转换成s3-key
 * convert domain/v1/projects/{projectId:\d+}/files/{filename:.+}
 * to project/projectId/images/archive/filename.zip
 */
@Component
public class ProjectImageArchiveKeyConverter {

    private static final String V1 = "v1/";
    private static final String FILES = "files";
    private static final String FILE = "file";
    private static final String PROJECTS = "projects";
    private static final String PROJECT = "project";
    private static final String LABEL_IMAGES_ARCHIVE = "images/archive";

    /**
     * 将url转换成s3 key
     * @param url https://www.bees360.com/bees360-web/v1/projects/32624/files/{filename:.+}(老版本的url)
     * @param domain https://www.bees360.com/bees360-web
     * @return
     */
    public String transform(String url, String domain) {
        Preconditions.checkNotNull(url, "url should not be null");
        //去掉域名
        if (url.indexOf(domain) >= 0) {
            url = url.replace(domain, "");
        }
        return transform(url);
    }

    /**
     * 将open-api的key转换成s3的key
     * @param key /v1/project/{projectId:\d+}/file/{filename:.+}
     * @return    project/1003872/images/archive/filename.zip
     */
    private String transform(String key) {
        Preconditions.checkNotNull(key, "key should not be null");

        //去掉"/"
        if(key.startsWith(Punctuation.SLASH)) {
            key = key.substring(1);
        }

        //去掉"v1"
        if(key.startsWith(V1)) {
            key = key.replace(V1, "");
        }

        //复数转成单数(projects-->project, files-->file)
        key = toSingular(key);

        //将"/file" 替换成 "/images/archive"
        if(key.indexOf(Punctuation.SLASH + FILE) > 0) {
            key = key.replace(Punctuation.SLASH + FILE, Punctuation.SLASH + LABEL_IMAGES_ARCHIVE);
        }
        return key;
    }

    /**
     * 复数转成单数(projects-->project, files-->file)
     * @param url
     * @return
     */
    private String toSingular(String url) {
        if(url.indexOf(PROJECTS + Punctuation.SLASH) >= 0) {
            url = url.replace(PROJECTS + Punctuation.SLASH, PROJECT + Punctuation.SLASH);
        }

        if(url.indexOf(FILES + Punctuation.SLASH) >= 0) {
            url = url.replace(FILES + Punctuation.SLASH, FILE + Punctuation.SLASH);
        }
        return url;
    }
}
