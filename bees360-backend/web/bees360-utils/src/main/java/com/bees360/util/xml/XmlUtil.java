package com.bees360.util.xml;

import lombok.extern.slf4j.Slf4j;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
@Slf4j
public class XmlUtil {

    public static XMLGregorianCalendar dateToXml(Date date) {
        return dateToXml(date, null);
    }

    public static XMLGregorianCalendar dateToXml(Date date,  TimeZone timeZone) {
        if (date == null) {
            return null;
        }
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTimeZone(timeZone);
        cal.setTime(date);
        XMLGregorianCalendar gc = null;
        try {
            gc = DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
        } catch (Exception e) {
            // do nothing
            log.warn("Fail to new XMLGregorianCalendar.");
        }
        return gc;
    }

    public static Date xmlToDate(XMLGregorianCalendar gc, TimeZone timeZone) {
        if (gc == null) {
            return null;
        }
        GregorianCalendar ca = gc.toGregorianCalendar();
        if (timeZone == null) {
            return ca.getTime();
        } else {
            ca.setTimeZone(timeZone);
            return ca.getTime();
        }
    }

    public static LocalDateTime xmlToDateTime(XMLGregorianCalendar gc, TimeZone timeZone) {
        if (gc == null) {
            return null;
        }
        GregorianCalendar ca = gc.toGregorianCalendar();
        ZoneId zoneId = (timeZone == null? ZoneId.systemDefault(): timeZone.toZoneId());
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(ca.getTimeInMillis()), zoneId);
    }
}
