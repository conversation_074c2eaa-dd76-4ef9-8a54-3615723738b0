package com.bees360.util;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

/**
 * <AUTHOR>
 * @date 2019/12/20 18:08
 */
public class UrlJoinParserUtils {

    public static List<String> parseListJson(String listJson) {
        if (StringUtils.isBlank(listJson)) {
            return new ArrayList<>();
        }
        List<String> list = new ArrayList<>();
        JSONArray jsonArray = JSON.parseArray(listJson);
        for (Object obj : jsonArray) {
            list.add(obj.toString());
        }
        return list;
    }

    public static String joinListJson(List<String> list) {
        if (list == null) {
            list = new ArrayList<>();
        }
        return JSONArray.toJSONString(list);
    }
}
