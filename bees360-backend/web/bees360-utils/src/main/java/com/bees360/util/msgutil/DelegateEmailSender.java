package com.bees360.util.msgutil;

import com.bees360.common.collections.CollectionAssistant;
import com.bees360.mail.MailMessage;
import com.bees360.mail.MailSender;
import com.bees360.util.msgutil.filter.EmailFilter;

import lombok.extern.log4j.Log4j2;

import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.Context;

import java.text.MessageFormat;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;

/**
 * Email sending delegate providing templated email capabilities.
 *
 * <p>Acts as an advanced wrapper for {@link MailSender} with key responsibilities:
 * <ul>
 *   <li>Template engine integration for content rendering</li>
 *   <li>Recipient address filtering and validation</li>
 *   <li>Email sending control</li>
 * </ul>
 */
@Log4j2
public class DelegateEmailSender {
    private final MailSender mailSender;
    private final ITemplateEngine templateEngine;
    private final String subtitlePrefix;
    private final boolean sendingEnabled;
    private final Properties subjectProperties;
    private final EmailFilter emailRecipientFilter;

    public DelegateEmailSender(MailSender mailSender, ITemplateEngine templateEngine, String subtitlePrefix, boolean sendingEnabled, Properties subjectProperties, EmailFilter emailRecipientFilter) {
        this.mailSender = mailSender;
        this.templateEngine = templateEngine;
        this.subtitlePrefix = subtitlePrefix;
        this.sendingEnabled = sendingEnabled;
        this.subjectProperties = subjectProperties;
        this.emailRecipientFilter = emailRecipientFilter;
        log.info("Created {}(mailSender={}, templateEngine={}, subtitlePrefix={}, sendingEnabled={}, subjectProperties={}, emailRecipientFilter={})", this, this.mailSender, this.templateEngine, this.subtitlePrefix, this.sendingEnabled, this.subjectProperties, this.emailRecipientFilter);
    }

    public void send(
        String recipient,
        String template,
        Map<String, Object> templateModel,
        Object[] subjectParams) {
        send(
            java.util.Collections.singletonList(recipient),
            template,
            templateModel,
            subjectParams,
            null);
    }

    public void send(
        String recipient,
        String template,
        Map<String, Object> templateModel,
        Object[] subjectParams,
        Map<String, String> attachments) {
        send(
            java.util.Collections.singletonList(recipient),
            template,
            templateModel,
            subjectParams,
            attachments);
    }

    /**
     * send a single email to each recipient in <code>to</code>. Any recipient will never know who
     * receive the email as well.
     */
    public void send(
        List<String> recipients,
        String template,
        Map<String, Object> templateModel,
        Object[] subjectParams) {
        send(recipients, template, templateModel, subjectParams, null);
    }

    public void oneShotSend(List<String> recipients, String template, Map<String, Object> templateModel, Object[] subjectParams) {
        recipients = filterRecipients(recipients);
        String content = createContent(template, templateModel);
        String subject = createSubject(template, subjectParams);
        if (recipients.isEmpty()) {
            log.info("No recipients provided, for email with subject: {}", subject);
            return;
        }


        var mailMessage = MailMessage.of(recipients, subject, content, null);
        mailSender.send(mailMessage);
    }

    public void send(List<String> recipients, String subject, Map<String, String> attachments) {
        // if content is empty, it will throw Invalid mail message in MailMessageFactory from solid
        doSend(subject, " ", recipients, attachments);
    }

    /**
     * send a single email to each recipient in <code>to</code>. Any recipient will never know who
     * receive the email as well.
     */
    public void send(
        List<String> to,
        String template,
        Map<String, Object> templateModel,
        Object[] subjectParams,
        Map<String, String> attachments) {
        to = filterRecipients(to);
        if (CollectionAssistant.isEmpty(to)) {
            log.info("The email {} is abort to send since the to is empty.", template);
            return;
        }

        String content = createContent(template, templateModel);
        String subject = createSubject(template, subjectParams);

        doSend(subject, content, to, attachments);
    }

    private List<String> filterRecipients(List<String> recipients) {
        // add by shoushan.zhao Use production data to publish the test environment to avoid sending the test environment email to external emails, see issue #3679
        if (Objects.nonNull(emailRecipientFilter)) {
            return emailRecipientFilter.filter(recipients);
        }
        return recipients;
    }

    private void doSend(String subject, String content, List<String> to, Map<String, String> attachments) {
        if (!sendingEnabled) {
            log.info("Email sending is disabled, Subject is {}, and content is {}.", subject, content);
            return;
        }

        for (String recipient : to) {
            var mailMessage = MailMessage.of(List.of(recipient), subject, content, attachments);
            mailSender.send(mailMessage);
        }
    }

    public String createSubject(String template, Object[] subjectParams) {
        String prefix = Optional.ofNullable(subtitlePrefix).map(pre -> pre + " ").orElse("");
        return prefix
            + MessageFormat.format(subjectProperties.getProperty(template), subjectParams);
    }

    public String createContent(String template, Map<String, Object> templateModel) {
        Context ctx = new Context(Locale.getDefault(), templateModel);
        return templateEngine.process(template + ".html", ctx);
    }
}
