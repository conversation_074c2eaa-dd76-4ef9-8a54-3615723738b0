package com.bees360.util.websocket;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import com.bees360.entity.Notification;
import com.bees360.entity.Notification.NotificationType;
import com.bees360.entity.dto.WebSocketMsgData;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;


/**
 * WebSocketHandler,before send the message ,the web client should create the websocket first.
 *  example in the web client: websocket = new WebSocket("ws://localhost:8080/bees360-web/websocket");
 *  "bees360-web" is the name of the project ;
 *  "websocket" is created in WebSocketConfig ;
 */
public class WebSocketHandler extends TextWebSocketHandler{

    private static Logger logger = LoggerFactory.getLogger(WebSocketHandler.class);

	private static final String USER_ID = "userId";

	private static final String JTI = "jti";

	private static final String CHECK_TOKEN_TYPE = "1";

	private static final String CLOSE_JTI_TYPE = "2";

	private static final String CHECK_HEARTBEAT_TYPE = "3";

	private static final String PONG_VALUE = "pong";

    private WebSocketDataManager webSocketDataManager;

    private TokenParser tokenParser;

    public WebSocketHandler(WebSocketDataManager webSocketDataManager, TokenParser tokenParser) {
        this.webSocketDataManager = webSocketDataManager;
        this.tokenParser = tokenParser;
    }

    /**
     * After the websocket created,save the userId and the session into the map
     * @param session
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        session.sendMessage(new SocketMsg("The websocket connected successfully!").textMessage());
    }

    /**
     * After the receive the message from the client, handle the message.
     * If code from the message is 1, check the token right or not.
     * If code from the message is 2, close all the websocket session belong to the jti which is from the message.
     * If code from the message is 3, send the pong signal to check the connection is still open or not.
     * @param session	The websocket session.
     * @param message	The message client send.
     */
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception{

    	String code = parseCodeFromMessage(session, message);
    	if(code.equals(CHECK_TOKEN_TYPE)){
    		checkToken(session, message);
    	}else if(code.equals(CLOSE_JTI_TYPE)){
    		String jti = (String)session.getAttributes().get(JTI);
        	closeOneJtiSession(jti);
    	}else if(code.equals(CHECK_HEARTBEAT_TYPE)){
    		checkHeartbeatMechanism(session);
    	}
    }

    /**Send the message to the client by the session.
     * @param session
     * @param message
     * @return
     */
    public boolean sendToOneSession(WebSocketSession session, WebSocketMessage<?> message) {
        if (session == null) {
        	return false;
        }
        if (!session.isOpen()){
        	return false;
        }
        try {
            session.sendMessage(message);
        } catch (IOException e) {
            logger.error("Fail to send notification to user with jti " + session, e);
            return false;
        }
        return true;
    }

    /**Send the message to the user whose id is userId.
     * @param userId
     * @param socketMsg
     * @return
     */
    public boolean sendMessageToUser(long userId, SocketMsg socketMsg) {
    	boolean result = true;
    	ArrayList<WebSocketSession> list = new ArrayList<WebSocketSession>();
    	list = webSocketDataManager.listSessionByUserId(userId);
    	TextMessage msg = socketMsg.textMessage();
    	Iterator<WebSocketSession> iterator = list.iterator();
    	while(iterator.hasNext()){
    		result &= sendToOneSession(iterator.next(), msg);
    	}
        return result;
    }

    /**
     * Send the message to the user whose id is userId.
     * @param userId
     * @param message
     * @return
     */
    public boolean sendMessageToUser(long userId, String message) {
    	return sendMessageToUser(userId, new SocketMsg(message));
    }

    /**Send the message to the user whose id is userId.
     * @param userId
     * @param message
     * @return
     */
    public boolean sendMessageToUser(long userId, WebSocketMsgData message) {
    	return sendMessageToUser(userId, new SocketMsg(message));
    }

    /**
     * Send the message to a group of users whose id included in usersId.
     * @param usersId
     * @param message
     * @return
     */
    public boolean sendMessageToGroup(long[] usersId,String message){
    	boolean groupSendSuccess = true;
    	for (long userId : usersId){
    		groupSendSuccess &= sendMessageToUser(userId, message);
    	}
    	return groupSendSuccess;
    }

    /**
     * Send message to all users.
     * @param message
     * @return true The messages was send to all users.
     * 			false Fail to send message to some users.
     */
    public boolean sendMessageToAllUsers(String message) {
        boolean result = true;
        ArrayList<WebSocketSession> list = new ArrayList<WebSocketSession>();
    	list = webSocketDataManager.listAllSession();
    	TextMessage msg = new SocketMsg(message).textMessage();
    	Iterator<WebSocketSession> iterator = list.iterator();
    	while(iterator.hasNext()){
    		result &= sendToOneSession(iterator.next(), msg);
    	}
        return  result;
    }

    /**Close all the websocekt session belong to the user whose id is userId.
     * @param userId
     * @return true Close all all the session belong to a user successfully.
     *         false Fail to close some session.
     */
    public boolean closeOneUserSession(long userId){
    	ArrayList<WebSocketSession> list = new ArrayList<WebSocketSession>();
    	list = webSocketDataManager.listSessionByUserId(userId);
    	Iterator<WebSocketSession> iterator = list.iterator();
    	while(iterator.hasNext()){
    		try {
				iterator.next().close();
			} catch (IOException e) {
				logger.error("Fail to close the session for user " + userId, e);
				return false;
			}
    	}
        return true;
    }

    /**
     * When it exists some error in transporting message, give some information, close the session.
     * @param session
     * @param exception
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception){
    	logger.debug("There are some error when transport the message!", exception);
    	if (session.isOpen()) {
            try {
				session.close();
			} catch (IOException e) {
				logger.error("Fail to close session in handleTransportError!", e);
			}
        }
    }

    /**
     * After close the connection, remove the users from the map.
     * @param session
     * @param status
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status){
    	if(!session.getAttributes().isEmpty()){
    		String jti = (String)session.getAttributes().get(JTI);
            long userId = (long)session.getAttributes().get(USER_ID);
        	if(jti != null && userId != 0){
        		webSocketDataManager.deleteOneSession(session);
        	}
    	}
    }

    /**Send the pong signal to client.
     * @param session
     */
    private void checkHeartbeatMechanism(WebSocketSession session){
    	Notification notification = new Notification();
    	notification.setContent(PONG_VALUE);
    	SocketMsg msg = new SocketMsg(NotificationType.HEARTBEAT_MECHANISM_TYPE,notification);
    	try {
			session.sendMessage(msg.textMessage());
		} catch (IOException e) {
			logger.error("Fail to send message in checkHeartbeatMechanism!", e);
		}
    }

    /**When a user log out, close all the websocket session belong to the jti.
     * @param jti
     * @throws Exception
     */
    private void closeOneJtiSession(String jti) throws Exception{
    	ArrayList<WebSocketSession> list = webSocketDataManager.listSessionByJti(jti);
    	Iterator<WebSocketSession> iterator = list.iterator();
    	while(iterator.hasNext()){
    		try {
				iterator.next().close();
			} catch (IOException e) {
				// do nothing
			}
    	}
    }

    /**
     * Check the token is useful or not. If not, send the information to the client and close the session.
     * Else, save the session.
     * @param session
     * @param message
     * @throws Exception
     */
    private void checkToken(WebSocketSession session, WebSocketMessage<?> message) throws Exception{
    	String token = parseTokeFromMessage(session, message);
    	if(token == null || token.trim().length() == 0) {
    		SocketMsg msg = new SocketMsg("The token is empty! The connection closed!");
    		session.sendMessage(msg.textMessage());
    		CloseStatus closeStatus = new CloseStatus(CloseStatus.SERVER_ERROR.getCode(), "The token is empty!");
    		session.close(closeStatus);
    		return;
    	}
    	Claims claims = null;
    	try{
    		 claims = parseClaims(token);
    	}catch(Exception e){
    		CloseStatus closeStatus = new CloseStatus(CloseStatus.SERVER_ERROR.getCode(),
    				"The token can not be analyzed!");
    		session.close(closeStatus);
    		return;
    	}

    	long activeTime = parseTokenToActiveTime(claims);
    	if(activeTime < System.currentTimeMillis() / 1000){
    		session.sendMessage(new SocketMsg("The token expired! The connection closed!").textMessage());
    		CloseStatus closeStatus = new CloseStatus(CloseStatus.SERVER_ERROR.getCode(), "The token expired!");
    		session.close(closeStatus);
    	}else{
    		String jti = parseTokenToJti(claims);
    		long userId = parseTokenToUserId(claims);
    		webSocketDataManager.saveOneSession(session, jti, userId);
    	}
    }

    private Claims parseClaims(String token) {
        return tokenParser.parseClaims(token);
    }

    private String parseTokeFromMessage(WebSocketSession session, WebSocketMessage<?> message) throws IOException {
    	if(message == null || message.getPayloadLength() == 0){
    		return null;
    	}
        JSONObject msgJson = new JSONObject(message.getPayload().toString());
    	if(!msgJson.has("token")) {
    		return null;
    	}
    	return msgJson.get("token").toString();
    }

    private String parseCodeFromMessage(WebSocketSession session, WebSocketMessage<?> message) throws IOException {
    	if(message == null || message.getPayloadLength() == 0){
    		return null;
    	}
    	JSONObject msgJson = new JSONObject(message.getPayload().toString());
    	if(!msgJson.has("code")) {
    		return null;
    	}
    	return msgJson.get("code").toString();
    }

	private String parseTokenToJti(Claims body) {
    	String jti = body.get("jti").toString();
    	return jti;
    }

	private Long parseTokenToActiveTime(Claims body){
    	Long activeTime = Long.parseLong(body.get("exp").toString());
    	return activeTime;
    }

	private Long parseTokenToUserId(Claims body){
    	Long activeTime = Long.parseLong(body.get("userId").toString());
    	return activeTime;
    }
}
