package com.bees360.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.List;

public class CollectionsUtil {

	public static <T> List<T> deepCopyList(List<T> src) throws IOException, ClassNotFoundException {
	    ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
	    ObjectOutputStream out = new ObjectOutputStream(byteOut);
	    out.writeObject(src);

	    ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
	    ObjectInputStream in = new ObjectInputStream(byteIn);
	    @SuppressWarnings("unchecked")
	    List<T> dest = (List<T>) in.readObject();
	    return dest;
	}
}
