package com.bees360.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtil {

	public static String randomString(int len){
		Random random = new Random();
		StringBuilder sb = new StringBuilder();
		for(int i = 0; i < len; i ++){
			int type = random.nextInt(3);
			switch(type){
			case 0:{
				// 数字
				sb.append(random.nextInt(10));
				break;
			}
			case 1:{
				// 小写字母
				char ch = (char) (random.nextInt(26) + 'a');
				sb.append(ch);
				break;
			}
			case 2:{
				// 大写字母
				char ch = (char) (random.nextInt(26) + 'A');
				sb.append(ch);
				break;
			}
			default :{
				i --;
			}
			}
		}
		return sb.toString();
	}

	public static String randomNumber(int len){
		Random random = new Random();
		StringBuilder sb = new StringBuilder();
		for(int i = 0; i < len; i ++){
			sb.append(random.nextInt(10));
		}
		return sb.toString();
	}

	public static double keepNDecimals(double num, int n){
		if(n < 0){
			return num;
		}
		return Double.parseDouble(String.format("%." + n + "f", num));
	}

	/*
	 * include min, exclude max
	 */
	public static int getRandomNum(int min, int max){
		if(max < min){
			int temp = max;
			max = min;
			min = temp;
		}
		Random random = new Random();
		return min + random.nextInt(max - min);
	}

	/*
	 * <AUTHOR>
	 * @data 2017/10/25 14:00
	 */
	public static String firstCharUpperOtherLower(String str){
		return Character.toUpperCase(str.charAt(0)) + str.toLowerCase().substring(1);
	}

	public static String encodeUrl(String rawUrl){
		try {
			return URLEncoder.encode(rawUrl, "gb2312");
		} catch (UnsupportedEncodingException e) {
//			e.printStackTrace();
		}
		return rawUrl;
	}

	public static boolean isEmail(String email){
		return email.contains("@");
	}

	public static boolean isNumeric(String str) {
		Pattern pattern = Pattern.compile("[0-9]*");
		Matcher isNum = pattern.matcher(str);
		if (!isNum.matches()) {
			return false;
		}
		return true;
	}

	public static void main(String[] args){
		int max = 100;
		int min = 50;
		for(int i = 0; i < 100; i ++){
			int num = getRandomNum(min, max);
			if(num < min || num > max){
				System.out.println("error");
				return;
			}
			System.out.println(num);
		}
//		System.out.println(keepNDecimals(15454.12345, 10));
		System.out.println("ok");
	}

	public static boolean isTrue(Boolean target){
        return target != null && target;
	}
}
