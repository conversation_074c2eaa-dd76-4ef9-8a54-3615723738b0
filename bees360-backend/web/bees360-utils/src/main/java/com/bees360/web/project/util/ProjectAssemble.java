package com.bees360.web.project.util;

import com.bees360.building.Message.BuildingMessage;
import com.bees360.building.Message.BuildingType;
import com.bees360.contract.Message;
import com.bees360.entity.dto.CreateOrUpdateProjectDto;
import com.bees360.project.Address;
import com.bees360.project.Building;
import com.bees360.project.Contact;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.GpsLocation;
import com.bees360.project.Message.ClaimType;
import com.bees360.project.Message.GpsLocationMessage;
import com.bees360.project.Message.IntegrationMessage;
import com.bees360.project.Message.IntegrationType;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.Message.ServiceType;
import com.bees360.project.Project;
import com.bees360.project.base.Message.AddressMessage;
import com.bees360.customer.Message.CustomerMessage;
import com.bees360.user.Message.UserMessage;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;
import com.bees360.web.core.constant.DateTimeConst;
import com.google.protobuf.util.Timestamps;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bees360.util.Defaults.nullIfEmpty;
import static com.bees360.util.Functions.acceptIfNotNull;

/**
 * <AUTHOR>
 */
public class ProjectAssemble {

    public static Project toProject(com.bees360.entity.Project projectDO, @Nullable Long inspectionCompletedTime, String timeZone) {
        return Project.of(toProjectMessage(projectDO, inspectionCompletedTime, timeZone));
    }

    public static Project from(com.bees360.entity.Project project) {
        return Project.of(toProjectMessage(project));
    }
    /**
     * @param timeZone 用于将long类型的时间字段转化为LocalDate，如果为空则使用 {@link DateTimeConst#DEFAULT_ZONE_ID}
     */
    public static ProjectMessage toProjectMessage(com.bees360.entity.Project projectDO, @Nullable Long inspectionCompletedTime,
            String timeZone) {
        if (Objects.isNull(projectDO)) {
            return null;
        }

        ProjectMessage.Builder builder = toProjectMessage(projectDO).toBuilder();
        acceptIfNotNull(builder::setDateOfLoss, projectDO.getDamageEventTime(), milli -> formatDateOfLoss(milli, timeZone));
        acceptIfNotNull(builder::setInspectionCompletedTime, inspectionCompletedTime, Timestamps::fromMillis);
        return builder.build();
    }

    public static ProjectMessage toProjectMessage(com.bees360.entity.Project projectDO) {
        ProjectMessage.Builder builder = ProjectMessage.newBuilder();

        acceptIfNotNull(builder::setPolicyNumber, projectDO.getPolicyNumber());
        acceptIfNotNull(builder::setInspectionNumber, projectDO.getInspectionNumber());
        acceptIfNotNull(builder::setPolicyEffectiveDate, projectDO.getPolicyEffectiveDate(), DateTimes::format);
        acceptIfNotNull(builder::setGpsIsApproximate, projectDO.isGpsIsApproximate());
        acceptIfNotNull(builder::setClaimType, Optional.ofNullable(projectDO.getClaimType()).map(ClaimType::forNumber).orElse(null));
        acceptIfNotNull(builder::setServiceType, Optional.ofNullable(projectDO.getServiceType()).map(ServiceType::forNumber).orElse(null));
        acceptIfNotNull(builder::setDueDate, projectDO.getDueDate(), Timestamps::fromMillis);
        acceptIfNotNull(builder::setLatestStatus, projectDO.getProjectStatus(), com.bees360.project.Message.ProjectStatus::forNumber);
        acceptIfNotNull(builder::setId, projectDO.getProjectId(), String::valueOf);
        acceptIfNotNull(builder::setCreateBy, projectDO.getCreatedBy(),
            userId -> UserMessage.newBuilder().setId(userId + "").build());
        acceptIfNotNull(builder::setCreateAt, projectDO.getCreatedTime(), Timestamps::fromMillis);

        acceptIfNotNull(builder::setGps, getGpsLocationMessage(projectDO));
        acceptIfNotNull(builder::setBuilding, getBuildingMessage(projectDO));
        acceptIfNotNull(builder::setAddress, getAddressMessage(projectDO));

        acceptIfNotNull(builder::setInspectionAppointmentTime, projectDO.getInspectionTime(), Timestamps::fromMillis);
        acceptIfNotNull(builder::setOperatingCompany, projectDO.getOperatingCompany());

        var contract = Message.ContractMessage.newBuilder();
        acceptIfNotNull(contract::setProcessedBy, projectDO.getRepairCompany(),
            companyId -> CustomerMessage.newBuilder().setId(companyId + "").build());
        acceptIfNotNull(contract::setInsuredBy, projectDO.getInsuranceCompany(),
            companyId -> CustomerMessage.newBuilder().setId(companyId + "").build());
        builder.setContract(contract);

        return builder.build();
    }

    public static com.bees360.entity.Project toProjectDO(Project project) {
        if (Objects.isNull(project)) {
            return null;
        }
        com.bees360.entity.Project projectDO = new com.bees360.entity.Project();
        projectDO.setPolicyNumber(project.getPolicyNumber());
        acceptIfNotNull(projectDO::setInspectionNumber, project.getInspectionNumber());
        acceptIfNotNull(projectDO::setClaimType, project.getClaimType(), t -> ClaimType.UNRECOGNIZED.equals(t)? null: t.getNumber());

        acceptIfNotNull(projectDO::setDamageEventTime, project.getDateOfLoss(), d -> convertDateOfLoss(d));
        acceptIfNotNull(projectDO::setServiceType, project.getServiceType(), t -> ServiceType.UNRECOGNIZED.equals(t)? null: t.getNumber());
        acceptIfNotNull(projectDO::setPolicyEffectiveDate, project.getPolicyEffectiveDate());
        acceptIfNotNull(projectDO::setGpsIsApproximate, project.getGpsIsApproximate());
        acceptIfNotNull(projectDO::setDueDate, project.getDueDate(), DateTimes::toEpochMilli);
        acceptIfNotNull(projectDO::setProjectStatus, project.getLatestStatus(), com.bees360.project.Message.ProjectStatus::getNumber);
        acceptIfNotNull(projectDO::setProjectId, project.getId(), IdConverter::toLong);
        acceptIfNotNull(projectDO::setRepairCompany, project.getProcessedBy(),
            company -> IdConverter.toLong(company.getId()));
        acceptIfNotNull(projectDO::setInsuranceCompany, project.getInsuredBy(),
            company -> IdConverter.toLong(company.getId()));
        acceptIfNotNull(projectDO::setCreatedBy, project.getCreateBy(),
            user -> Optional.ofNullable(IdConverter.toLong(user.getId())).orElse(0L));
        acceptIfNotNull(projectDO::setCreatedTime, project.getCreateAt(), DateTimes::toEpochMilli);
        setBuilding(projectDO, project.getBuilding());
        setAddress(projectDO, project.getAddress());
        setGpsLocation(projectDO, project.getGps());

        acceptIfNotNull(projectDO::setInspectionTime, project.getInspectionAppointmentTime(), DateTimes::toEpochMilli);
        return projectDO;
    }

    /**
     * @param timeZone 用于将long类型的时间字段转化为LocalDate，如果为空则使用 {@link DateTimeConst#DEFAULT_ZONE_ID}，如果为空则使用 {@link DateTimeConst#DEFAULT_ZONE_ID}
     */
    private static String formatDateOfLoss(Long timestamp, String timeZone) {
        LocalDate date = convertDateOfLoss(timestamp, timeZone);
        return date == null? null: DateTimes.format(date);
    }

    /**
     * @param timeZone 用于将long类型的时间字段转化为LocalDate，如果为空则使用 {@link DateTimeConst#DEFAULT_ZONE_ID}
     */
    private static LocalDate convertDateOfLoss(Long timestamp, String timeZone) {
        ZoneId zoneId = findTimeZone(timeZone, DateTimeConst.DEFAULT_ZONE_ID);
        return timestamp == null? null: DateTimes.fromEpochMilli(timestamp, zoneId).toLocalDate();
    }

    private static Long convertDateOfLoss(LocalDate date) {
        // 取中午12点，美国全境同一天
        LocalDateTime localDateTime = date.atTime(LocalTime.of(12, 0));
        return date == null? null: DateTimes.toEpochMilli(localDateTime, DateTimeConst.DEFAULT_ZONE_ID);
    }

    private static ZoneId findTimeZone(String timeZone, ZoneId defaultZoneId) {
        try {
            return StringUtils.isBlank(timeZone) ? defaultZoneId : ZoneId.of(timeZone);
        } catch(Exception e) {
            return defaultZoneId;
        }
    }

    public static CreateOrUpdateProjectDto toCreateOrUpdateProjectDto(Project project) {
        if (Objects.isNull(project)) {
            return null;
        }
        com.bees360.entity.Project projectDO = toProjectDO(project);
        CreateOrUpdateProjectDto projectDto = new CreateOrUpdateProjectDto();
        BeanUtils.copyProperties(projectDO, projectDto);
        return projectDto;
    }

    // ===== address =====

    public static Address getAddress(com.bees360.entity.Project projectDO) {
        if (Objects.isNull(projectDO)) {
            return null;
        }
        return Address.of(getAddressMessage(projectDO));
    }

    private static AddressMessage getAddressMessage(com.bees360.entity.Project projectDO) {
        if (Objects.isNull(projectDO)) {
            return null;
        }
        AddressMessage.Builder builder = AddressMessage.newBuilder();
        acceptIfNotNull(builder::setCountry, projectDO.getCountry());
        acceptIfNotNull(builder::setState, projectDO.getState());
        acceptIfNotNull(builder::setCity, projectDO.getCity());
        acceptIfNotNull(builder::setZipCode, projectDO.getZipCode());
        acceptIfNotNull(builder::setAddressLine1, projectDO.getAddress());
        acceptIfNotNull(builder::setAddressLine2, "");
        return builder.build();
    }

    public static void setAddress(com.bees360.entity.Project projectDO, Address address) {
        if (Objects.isNull(address)) {
            address = Address.of(AddressMessage.getDefaultInstance());
        }
        String projectAddress = Arrays.asList(address.getAddressLine1(), address.getAddressLine2()).stream()
            .filter(StringUtils::isNotBlank).collect(Collectors.joining(", "));
        projectDO.setAddress(projectAddress);
        projectDO.setCity(address.getCity());
        projectDO.setState(address.getState());
        projectDO.setCountry(address.getCountry());
        projectDO.setZipCode(address.getZipCode());
    }

    // ===== gps =====

    public static GpsLocation getGpsLocation(com.bees360.entity.Project projectDO) {
        return GpsLocation.of(getGpsLocationMessage(projectDO));
    }

    public static GpsLocationMessage getGpsLocationMessage(com.bees360.entity.Project projectDO) {
        Double lat = projectDO.getLat();
        Double lng = projectDO.getLng();
        if (!ObjectUtils.allNotNull(lat, lng)) {
            return null;
        }
        return GpsLocationMessage.newBuilder().setLat(lat).setLng(lng).build();
    }

    public static void setGpsLocation(com.bees360.entity.Project projectDO, GpsLocation gpsLocation) {
        gpsLocation =
            Objects.isNull(gpsLocation) ? GpsLocation.of(GpsLocationMessage.getDefaultInstance()) : gpsLocation;

        Optional.ofNullable(gpsLocation).ifPresent(gps -> {
            projectDO.setLat(gps.getLat());
            projectDO.setLng(gps.getLng());
        });
    }

    // ===== building =====

    public static Building getBuilding(com.bees360.entity.Project projectDO) {
        return Building.of(getBuildingMessage(projectDO));
    }

    private static BuildingMessage getBuildingMessage(com.bees360.entity.Project projectDO) {
        if (Objects.isNull(projectDO)) {
            return null;
        }
        BuildingMessage.Builder builder = BuildingMessage.newBuilder();

        String yearBuild = StringUtils.trimToNull(projectDO.getYearBuilt());
        acceptIfNotNull(builder::setYearBuilt, yearBuild, (v) -> Integer.parseInt(v));
        acceptIfNotNull(builder::setType, projectDO.getProjectType(), BuildingType::forNumber);

        return builder.build();
    }

    /**
     * 当building为null时，会设置com.bees360.entity.Project中的building字段为默认值
     */
    public static void setBuilding(com.bees360.entity.Project projectDO, @Nullable Building building) {
        if (Objects.isNull(building)) {
            building = Building.of(BuildingMessage.getDefaultInstance());
        }
        acceptIfNotNull(projectDO::setProjectType, building.getType(), t -> BuildingType.UNRECOGNIZED.equals(t)? null: t.getNumber());

        acceptIfNotNull(projectDO::setYearBuilt, building.getYearBuilt(), (v) -> v + "");
    }

    // ===== contact =====
    @Nullable
    public static Contact getAgent(com.bees360.entity.Project projectDO) {
        return Contact.of(getAgentContactMessage(projectDO));
    }

    public static List<Contact> getAgentAndInsured(com.bees360.entity.Project projectDO){
        List<Contact> contacts = new ArrayList<>();
        acceptIfNotNull(contacts::add, getAgent(projectDO));
        acceptIfNotNull(contacts::add, getInsured(projectDO));
        return contacts;
    }

    public static ProjectMessage.Contact getAgentContactMessage(com.bees360.entity.Project projectDO) {
        ProjectMessage.Contact.Builder builder = ProjectMessage.Contact.newBuilder();
        acceptIfNotNull(builder::setFullName, nullIfEmpty(projectDO.getAgentContactName()));
        acceptIfNotNull(builder::setPrimaryEmail, nullIfEmpty(projectDO.getAgentEmail()));
        acceptIfNotNull(builder::setPrimaryPhone, nullIfEmpty(projectDO.getAgentPhone()));

        if (builder.build().equals(ProjectMessage.Contact.getDefaultInstance())) {
            return builder.build();
        }
        builder.setRole(ProjectConstants.ContactRoleType.CONTACT_AGENT);
        return builder.build();
    }

    @Nullable
    public static Contact getInsured(com.bees360.entity.Project projectDO) {
        return Contact.of(getInsuredMessage(projectDO));
    }

    public static ProjectMessage.Contact getInsuredMessage(com.bees360.entity.Project projectDO) {

        ProjectMessage.Contact.Builder builder = ProjectMessage.Contact.newBuilder();
        acceptIfNotNull(builder::setFullName, nullIfEmpty(projectDO.getAssetOwnerName()));
        acceptIfNotNull(builder::setPrimaryEmail, nullIfEmpty(projectDO.getAssetOwnerEmail()));
        acceptIfNotNull(builder::setPrimaryPhone, nullIfEmpty(projectDO.getAssetOwnerPhone()));
        acceptIfNotNull(builder::addOtherPhone, nullIfEmpty(projectDO.getInsuredWorkPhone()));
        acceptIfNotNull(builder::addOtherPhone, nullIfEmpty(projectDO.getInsuredHomePhone()));
        acceptIfNotNull(builder::setFirstName, nullIfEmpty(projectDO.getInsuredFirstName()));
        acceptIfNotNull(builder::setMiddleName, nullIfEmpty(projectDO.getInsuredMiddleName()));
        acceptIfNotNull(builder::setLastName, nullIfEmpty(projectDO.getInsuredLastName()));
        if (builder.build().equals(ProjectMessage.Contact.getDefaultInstance())) {
            return builder.build();
        }
        builder.setRole(ProjectConstants.ContactRoleType.CONTACT_INSURED);
        return builder.build();
    }

    public static void setAgent(com.bees360.entity.Project projectDO, Contact contact) {
        projectDO.setAgentContactName(contact.getFullName());
        projectDO.setAgentEmail(contact.getPrimaryEmail());
        projectDO.setAgentPhone(contact.getPrimaryPhone());
    }

    public static void setInsured(com.bees360.entity.Project projectDO, Contact contact) {
        projectDO.setAssetOwnerName(contact.getFullName());
        projectDO.setAssetOwnerEmail(contact.getPrimaryEmail());
        projectDO.setAssetOwnerPhone(contact.getPrimaryPhone());
        var otherPhone = contact.getOtherPhone().iterator();
        if (otherPhone.hasNext()){
            projectDO.setInsuredWorkPhone(otherPhone.next());
        }
        if (otherPhone.hasNext()){
            projectDO.setInsuredHomePhone(otherPhone.next());
        }
    }

    public static boolean setContact(com.bees360.entity.Project projectDO, Contact contact) {
        String role = contact.getRole();
        if (StringUtils.isEmpty(role)) {
            return false;
        }
        switch (role) {
            case ProjectConstants.ContactRoleType.CONTACT_AGENT: {
                setAgent(projectDO, contact);
                return true;
            }
            case ProjectConstants.ContactRoleType.CONTACT_INSURED: {
                setInsured(projectDO, contact);
                return true;
            }
            default: {
                return false;
            }
        }
    }

    /**
     *
     * @return true 全部都进行了设置，false 存在任意一个没有设置
     */
    public static boolean setContact(com.bees360.entity.Project projectDO, Iterable<? extends Contact> contacts) {
        boolean cantactSet = true;
        for (Contact contact: contacts) {
            cantactSet &= setContact(projectDO, contact);
        }
        return cantactSet;
    }

    public static void cleanContact(com.bees360.entity.Project projectDO, String role) {
        Contact contact = Contact.of(ProjectMessage.Contact.newBuilder().setRole(role).build());
        setContact(projectDO, contact);
    }

    // ===== integration =====

    public static List<ExternalIntegration> toIntegration(com.bees360.entity.Project projectDO) {
        ExternalIntegration hover = getIntegrationHover(projectDO);

        return Arrays.asList(hover).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static void setIntegrationHover(com.bees360.entity.Project projectDO, ExternalIntegration integration) {
        if (StringUtils.isEmpty(integration.getReferenceNumber())) {
            projectDO.setHoverJobId(null);
        }
        projectDO.setHoverJobId(Long.parseLong(integration.getReferenceNumber()));
    }

    private static ExternalIntegration getIntegrationHover(com.bees360.entity.Project projectDO) {
        return ExternalIntegration.from(getIntegrationHoverMessage(projectDO));
    }

    private static IntegrationMessage getIntegrationHoverMessage(com.bees360.entity.Project projectDO) {
        Long hoverJobId = projectDO.getHoverJobId();
        if (Objects.isNull(hoverJobId)) {
            return null;
        }
        IntegrationMessage.Builder builder = IntegrationMessage.newBuilder();
        acceptIfNotNull(builder::setReferenceNumber, projectDO.getHoverJobId(), String::valueOf);

        builder.setIntegrationType(IntegrationType.INTEGRATION_HOVER.name());
        return builder.build();
    }

    private static <T> void addIfNotNull(Collection<T> collection, T item) {
        if (Objects.nonNull(item)) {
            collection.add(item);
        }
    }

    public static boolean contactEquals(@Nullable Contact contact1, @Nullable Contact contact2) {
        if (Objects.equals(contact1, contact2)) {
            return true;
        }
        if (contact1 == null || contact2 == null) {
            return false;
        }
        if (!Objects.equals(contact1.getRole(), contact2.getRole())) {
            return false;
        }
        if (!Objects.equals(contact1.getFullName(), contact2.getFullName())) {
            return false;
        }
        if (!Objects.equals(contact1.getPrimaryPhone(), contact2.getPrimaryPhone())) {
            return false;
        }
        if (!Objects.equals(contact1.getPrimaryEmail(), contact2.getPrimaryEmail())) {
            return false;
        }
        var otherPhoneSet1 = Iterables.toSet(contact1.getOtherPhone());
        var otherPhoneSet2 = Iterables.toSet(contact2.getOtherPhone());
        if (otherPhoneSet1.size() != otherPhoneSet2.size()
            || !otherPhoneSet2.containsAll(otherPhoneSet1)) {
            return false;
        }
        var otherEmailSet1 = Iterables.toSet(contact1.getOtherEmail());
        var otherEmailSet2 = Iterables.toSet(contact2.getOtherEmail());
        return otherEmailSet1.size() == otherEmailSet2.size()
            && otherEmailSet2.containsAll(otherEmailSet1);
    }
}
