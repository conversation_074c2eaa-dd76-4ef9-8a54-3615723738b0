package com.bees360.util.pdf;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;

/**
 * <AUTHOR>
 */
public interface PdfSizeReducer {
    int NO_DPI = -1;
    void reduceSizeWithDpi(File pdf, File output, int dpi) throws IOException;

    /**
     * @param pdf        需要压缩的报告
     * @param output     压缩之后的报告
     * @param limitSize  最大压缩后报告的文件大小
     * @param dpi        压缩pdf的初始dpi，如果初始压缩超过了limit，则会减少给dpi值
     * @throws IOException
     * @return 最终的dpi
     */
    int reduceSizeWithLimit(File pdf, File output, long limitSize, int dpi) throws IOException;

    /**
     * @param pdf        需要压缩的报告
     * @param output     压缩之后的报告
     * @param limitSize  最大压缩后报告的文件大小
     * @param dpi        压缩pdf的初始dpi，如果初始压缩超过了limit，则会减少给dpi值
     * @throws IOException
     * @return 最终的dpi
     */
    int reduceSizeWithLimit(File pdf, OutputStream output, long limitSize, int dpi) throws IOException;

    /**
     * 自动确定dpi，以取得最佳的压缩大小。
     *
     * @param pdf        需要压缩的报告
     * @param output     压缩之后的报告
     * @param limitSize  最大压缩后报告的文件大小
     * @throws IOException
     * @return 最终的dpi
     */
    int reduceSizeWithLimit(File pdf, File output, long limitSize) throws IOException;

    /**
     * 自动确定dpi，以取得最佳的压缩大小。
     *
     * @param pdf        需要压缩的报告
     * @param output     压缩之后的报告
     * @param limitSize  最大压缩后报告的文件大小
     * @throws IOException
     * @return 最终的dpi
     */
    int reduceSizeWithLimit(File pdf, OutputStream output, long limitSize) throws IOException;
}
