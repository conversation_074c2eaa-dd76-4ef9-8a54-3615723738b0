package com.bees360.util;

public enum UnitEnum {
	CENTIMETER("cm", 0.01, UnitCategory.LENGTH),
	METER("meter", 1, UnitCategory.LENGTH),
	FOOT("ft", 0.3048, UnitCategory.LENGTH),
	SQUARE_METER("sqmt", 1, UnitCategory.AREA),
	SQUARE_FOOT("sqft", 0.092903, UnitCategory.AREA)
	;

	private final String display;
	private final double toCenter;
	private final UnitCategory category;

	private static enum UnitCategory{
		LENGTH,
		AREA
	}

	UnitEnum(String display, double toMeter, UnitCategory category){
		this.display = display;
		this.toCenter = toMeter;
		this.category = category;
	}

	public String getDisplay(){
		return display;
	}

	public static UnitEnum getEnum(String display){
		UnitEnum[] units = UnitEnum.values();
		for(UnitEnum unit: units){
			if(unit.getDisplay().equals(display)){
				return unit;
			}
		}
		return null;
	}

	public double convert(UnitEnum unit) throws Exception{
		if(unit == null || category != unit.category) {
			throw new Exception("Fail to convert unit.");
		}
		return this.toCenter / unit.toCenter;
	}
	public static void main(String args[]){
		try {
			System.out.println(1 * UnitEnum.FOOT.convert(UnitEnum.METER));
			System.out.println(1 * UnitEnum.METER.convert(UnitEnum.FOOT));
			System.out.println(1 * UnitEnum.FOOT.convert(UnitEnum.SQUARE_FOOT));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
