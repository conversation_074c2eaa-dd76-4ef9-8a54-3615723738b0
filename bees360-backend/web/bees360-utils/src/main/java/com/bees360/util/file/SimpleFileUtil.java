package com.bees360.util.file;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

public class SimpleFileUtil {
	/**
	 * Remove the specified file. If it is a directory, it will not be deleted
	 * @param filePath the absolute path of the file you want to remove
	 * @return
	 * 		true The file not exist or success to delete the file
	 * 		false The file not exist or fail to delete the file
	 */
	public static boolean removeLocalFile(String filePath){
		File f = new File(filePath);
		if(f.exists()){
			if(f.isDirectory()){
				return false;
			}
			f.delete();
		}
		return !f.exists();
	}

	/**
	 * Remove all files in this directory. In addition, you can decide whether remove this directory or not.
	 * <AUTHOR>
	 * @param dirPath The absolute path of the directory you want to remove
	 * @param removeItself Whether delete this directory or not
	 * @return
	 * 		true The directory not exist or success to delete the file
	 * 		false The specified file is not an directory or fail to delete the specified directory
	 */
	public static boolean removeDirectory(String dirPath, boolean removeItself){
		File dir = new File(dirPath);
		if(!dir.exists()){
			return true;
		}
		if(!dir.isDirectory()){
			return false;
		}
		File[] files = dir.listFiles();
		for(File f: files){
			if(f.isDirectory()){
				removeDirectory(f.getAbsolutePath(), true);
			} else {
				f.delete();
			}
		}
		if(removeItself){
			dir.delete();
			return !dir.exists();
		} else {
			files = dir.listFiles();
			return files.length == 0;
		}
	}

	/**
	 * remove all files in this directory including this directory.
	 * <AUTHOR>
	 * @param dirPath The absolute path of the directory you want to remove
	 * @return
	 * 		true The directory not exist or success to empty the directory
	 * 		false The specified file is not an directory or fail to empty the specified directory
	 */
	public static boolean emptyDirectory(String dirPath){
		return removeDirectory(dirPath, false);

	}

	/**
	 * normalize the separator for file path
	 * If file separator is '\', replace '/' with '\'
	 * If file separator is '/' replace '\' with '/'
	 * @param filePath
	 * @return
	 */
	public static String normalizeFileSeparator(String filePath){
		if(filePath == null){
			return null;
		}
		char targetSeparor = File.separator.charAt(0);
		char oldSeparator = '\\';
		System.out.println();
		System.out.println(targetSeparor == oldSeparator);
		if(targetSeparor == oldSeparator){
			oldSeparator = '/';
		}
		return filePath.trim().replace(oldSeparator, targetSeparor);
	}

	public static String extractAsOneString(String filePath){
		BufferedReader reader = null;
		StringBuilder sb = new StringBuilder();
        try {
            reader = new BufferedReader(new InputStreamReader(new FileInputStream(filePath)));
            String line = null;
            while((line = reader.readLine()) != null){
            	sb.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if(reader != null){
                try {
                	reader.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
            }
        }
        return sb.toString();
	}

	/**
	 * Read the file, and add each line to a list which will be the result.
	 * <AUTHOR>
	 * @param filePath The path of the file which will be dealt with.
	 * @return a list whose each element is one line of the file
	 */
	public static List<String> getStringListFromFile(String filePath){
		BufferedReader reader = null;
		List<String> list = new ArrayList<String>();
        try {
            reader = new BufferedReader(new InputStreamReader(new FileInputStream(filePath)));
            String line = null;
            while((line = reader.readLine()) != null){
            	list.add(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if(reader != null){
                try {
                	reader.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
            }
        }
        return list;
	}

	/**
	 * create list from file, a line as an element. Null value will be ignore.
	 * If you want to ignore some line, you can implement {@code LineFormater.formatLineTo} to return null.
	 * @param filePath The path of the file you want to deal with
	 * @param formater an interface decide how to deal with each line of the file
	 * @return a list whose each element is from an line of the file
	 * @throws IOException
	 */
	public static <T> List<T> getStringListFromFile(String filePath, LineFormater<T> formater) throws IOException{
		BufferedReader reader = null;
		List<T> list = new ArrayList<T>();
        try {
            reader = new BufferedReader(new InputStreamReader(new FileInputStream(filePath)));
            String line = null;
            int index = 0;
            while((line = reader.readLine()) != null){
            	T t = formater.formatLineTo(index ++, line);
            	if(t != null){
            		list.add(t);
            	}
            }
        } finally {
        	if(reader != null) {
	        	try {
					reader.close();
					reader = null;
				} catch (IOException e) {
					e.printStackTrace();
				}
        	}
        }
        return list;
	}

	/**
	 * write strings in collection into to the file, each element of the collection will be look as a line.
	 * <AUTHOR>
	 * @param collection
	 * @param destFilePath
	 * @param append
	 * @throws IOException
	 */
	public static void strings2File(Collection<String> collection, String destFilePath, boolean append) throws IOException {
		objString2File(collection, destFilePath, append, new ObjectStringizer<String>(){
			@Override
			public String stringify(String t) {
				return t.toString();
			}
		});
	}

	public static <T> void objString2File(Collection<T> collection, String destFilePath, boolean append,
			ObjectStringizer<T> objectStringizer) throws IOException{

		BufferedWriter writer = null;
        try {
        	writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(destFilePath, append)));

            for(T t: collection){
            	String line = objectStringizer.stringify(t);
            	writer.write(line);
            	writer.newLine();
            }
        	writer.flush();
        } finally {
            if(writer != null){
                try {
                	writer.close();
                	writer = null;
				} catch (IOException e) {
					e.printStackTrace();
				}
            }
        }
	}

	/**
	 * <AUTHOR>
	 * @param srcFile The file you want to copy.
	 * @param destFile The copy path you want to save.
	 * @return true if copy success, false if copy fail
	 */
	public static boolean copyFileTo(File srcFile, File destFile){
		if(srcFile == null || !srcFile.exists() || destFile == null){
			return false;
		}
		if(srcFile.getAbsolutePath().equals(destFile.getAbsolutePath())){
			return true;
		}
		try {
			if(destFile.exists()){
				destFile.delete();
			}
			Files.copy(srcFile.toPath(), destFile.toPath());
		} catch (IOException e) {
			return false;
		}
		return true;
	}

	public static void main(String[] args) {
		String src = "/home/<USER>/图片/2323232.png";
		String dest = "/home/<USER>/图片/AIFlow2.png";
		boolean success = copyFileTo(new File(src), new File(dest));
		if(success) {
			System.out.println("SUCESS");
		}
	}

	public static String getExtension(File file) {
		return getExtension(file.getName());
	}

	public static String getExtension(String filePath) {
    	int lastPoint = filePath.lastIndexOf(".");
    	return lastPoint < 0? "": filePath.substring(lastPoint + 1);
	}

    public static String getExtensionFromContentType(String contentType, String defaultExtension) {
        if (StringUtils.isEmpty(contentType)) {
            return defaultExtension;
        }
        int index = contentType.lastIndexOf('/');
        if (index < 0) {
            return defaultExtension;
        }
        return contentType.substring(index + 1);
    }
}
