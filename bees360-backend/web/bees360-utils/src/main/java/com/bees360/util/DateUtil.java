package com.bees360.util;

import lombok.extern.slf4j.Slf4j;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;


@Slf4j
public class DateUtil {
	public static List<String> listTimeZones() {
		String s [] = TimeZone.getAvailableIDs();
    	return Arrays.asList(s);
	}

	public static long dateToStamp(String datestring) throws ParseException{
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;

    	try {
    		date = sdf1.parse(datestring);
		} catch (ParseException e) {
			datestring = datestring.replace('/', '-');
			date = sdf2.parse(datestring);
		}

        return date.getTime();
    }

	public static long getNow(){
		return System.currentTimeMillis();
	}

	public static String stampToDate(long s){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long lt = Long.valueOf(s);
        Date date = new Date(lt);
        return sdf.format(date);
    }

	public static String stampToDate(String s){
        return stampToDate(Long.valueOf(s));
    }

	public static long dateAddDaysToTimeStamp(Date date, int n){
		return dateAddDays(date, n).getTime();
	}

	public static Date dateAddDays(Date date, int n){
		Calendar dd = Calendar.getInstance();
        dd.setTime(date);
        dd.add(Calendar.DATE, n);
        return dd.getTime();
	}

	public static long timeSpan(Date start, Date end){
		return end.getTime() - start.getTime();
	}

	public static long nDaysToTimeStamp(int n){
		long timestamps = n * 24L * 60L * 60L * 1000L;
		return timestamps;
	}

	public static Date convertDate(String dateStr, String format,
			TimeZone timezone) throws ParseException {
		DateFormat dateFormat = new SimpleDateFormat(format);
		dateFormat.setTimeZone(timezone);
		return dateFormat.parse(dateStr);
	}

	public static String convertDate(String dateStr, String format,
			TimeZone from, TimeZone to) throws ParseException {
		DateFormat dateFormat = new SimpleDateFormat(format);
		dateFormat.setTimeZone(from);
		Date dateFrom = dateFormat.parse(dateStr);

		dateFormat.setTimeZone(to);
		return dateFormat.format(dateFrom);
	}

	public static String convertDate(Date date, String format, TimeZone to) throws ParseException {
		DateFormat dateFormat = new SimpleDateFormat(format);
		dateFormat.setTimeZone(to);
		return dateFormat.format(date);
	}

    public static String convertDate(long timeMills, String format, ZoneId zoneId) {
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timeMills), zoneId);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return zonedDateTime.format(formatter);
    }

    public static String getCurrentDate(String formatPattern, ZoneOffset offset) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(formatPattern);
        return ZonedDateTime.now(offset).format(formatter);
    }
}
