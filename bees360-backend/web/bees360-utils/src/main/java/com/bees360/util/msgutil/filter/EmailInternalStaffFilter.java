package com.bees360.util.msgutil.filter;

import com.bees360.common.collections.CollectionAssistant;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

public class EmailInternalStaffFilter implements EmailFilter {

    private static final String[] INTERNAL_MAIL_SUFFIX = { "@bees360.com" };

    @Override
    public List<String> filter(List<String> emails) {
        if (CollectionAssistant.isEmpty(emails)) {
            return new ArrayList<>();
        }

        // 上游数据可能来自Arrays.asList 或 Collections.singletonList, 由于这里两个List都是其内部类，其removeIf方法只是简单的
        // 抛出了UnsupportedOperationException，
        // 因此这里不能简单的写为 emails.removeIf(email -> !isInternalMail(email));
        return emails.stream().filter(this::isInternalMail).collect(Collectors.toList());
    }

    private boolean isInternalMail(String email) {
        for (String suffix : INTERNAL_MAIL_SUFFIX) {
            if (StringUtils.endsWithIgnoreCase(email, suffix)) {
                return true;
            }
        }
        return false;
    }

}
