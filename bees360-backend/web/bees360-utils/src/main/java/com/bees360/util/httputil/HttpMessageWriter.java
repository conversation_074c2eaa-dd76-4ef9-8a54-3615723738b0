package com.bees360.util.httputil;

import com.google.common.base.Preconditions;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import jakarta.servlet.http.HttpServletResponse;
import java.io.OutputStream;

@Component
public class HttpMessageWriter {

    /**
     * 向客户端输出错误提示信息
     *
     * @param httpServletResponse
     * @param httpStatus
     * @param content
     * @throws Exception
     */
    public void write(HttpServletResponse httpServletResponse, HttpStatus httpStatus, String content) throws Exception {
        Preconditions.checkNotNull(httpStatus, "http status should not be empty.");
        Preconditions.checkNotNull(content, "text should not be empty.");

        httpServletResponse.setStatus(httpStatus.value());
        httpServletResponse.setContentType("application/json");
        httpServletResponse.setCharacterEncoding("UTF-8");
        try (OutputStream outputStream = httpServletResponse.getOutputStream()) {
            outputStream.write(content.getBytes());
        }
    }
}
