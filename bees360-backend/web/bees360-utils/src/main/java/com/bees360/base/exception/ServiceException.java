package com.bees360.base.exception;

import java.io.Serial;

public class ServiceException extends Exception {

    @Serial
    private static final long serialVersionUID = 1L;

	// Exception code
	private String msgCode;

	// Exception info
	private String message;

    /**
     * 请使用ServiceException(String msgCode, String message)，方便日志的输出和查看。
     */
	@Deprecated
	public ServiceException(String msgCode) {
		super();
		this.msgCode = msgCode;
	}

	public ServiceException(String msgCode, String message) {
		super(message);
		this.msgCode = msgCode;
		this.message = message;
	}

	public ServiceException(String code, Throwable cause) {
        super(cause);
        this.msgCode = code;
    }

	public ServiceException(String code, String message, Throwable cause) {
        super(message, cause);
        this.msgCode = code;
        this.message = message;
    }

	public String getMsgCode() {
		return msgCode;
	}

	public void setMsgCode(String msgCode) {
		this.msgCode = msgCode;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}
}
