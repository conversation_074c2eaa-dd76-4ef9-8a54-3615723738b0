package com.bees360.util.system;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.Optional;

@Deprecated
public class SystemEnv {

	public static final String ENV_PROFILE_ACTIVE;
	public static final String ENV_BEES360_SECRET_KEY;

	static {
		// 如果发生 java.lang.NoClassDefFoundError: Could not initialize class xxx.xxx.xxx 类型的错误，可能是这里发生了异常
		ENV_PROFILE_ACTIVE = env("ENV");
		ENV_BEES360_SECRET_KEY = env("BEES360_SECRET_KEY");
	}

	private static String env(String envKey) {
		String envVal = getenv(envKey);
        return envVal;
	}

	public static String getenv(String envKey) {
		return System.getenv(envKey);
	}

	public static void main(String[] args) {
		String SPTRIP_PAYMENT_SECRET_KEY = SystemEnv.ENV_PROFILE_ACTIVE;
		System.out.println(SPTRIP_PAYMENT_SECRET_KEY);
	}
}
