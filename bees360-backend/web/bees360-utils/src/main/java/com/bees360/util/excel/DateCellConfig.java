package com.bees360.util.excel;

import java.util.TimeZone;

public class DateCellConfig {
	private String format;
	private TimeZone timezone;
	private TimeZone toTimezone;

	public DateCellConfig() {}

	public DateCellConfig(String format, TimeZone timezone, TimeZone toTimezone) {
		this.format = format;
		this.timezone = timezone;
		this.toTimezone = toTimezone;
	}

	public String getFormat() {
		return format;
	}
	public void setFormat(String format) {
		this.format = format;
	}
	public TimeZone getTimezone() {
		return timezone;
	}
	public void setTimezone(TimeZone timezone) {
		this.timezone = timezone;
	}
	public TimeZone getToTimezone() {
		return toTimezone;
	}
	public void setToTimezone(TimeZone toTimezone) {
		this.toTimezone = toTimezone;
	}

	@Override
	public String toString() {
		return "DateCellConfig [format=" + format + ", timezone=" + timezone + ", toTimezone=" + toTimezone + "]";
	}
}
