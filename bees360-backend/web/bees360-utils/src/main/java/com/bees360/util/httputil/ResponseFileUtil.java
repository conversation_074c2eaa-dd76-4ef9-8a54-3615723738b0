package com.bees360.util.httputil;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.bouncycastle.util.encoders.Base64Encoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @date 2019/09/25 15:04
 */
public class ResponseFileUtil {

    private static Logger logger = LoggerFactory.getLogger(ResponseFileUtil.class);

    public static void responseFileWithBase64(File file, String fileName, HttpServletResponse response) {

        // Setting the response header and the client to save the file name
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data");
        response.addHeader("Content-Disposition", "attachment;fileName=" + fileName);
        response.addHeader("Content-Encoding", "UTF-8");

        encodeBase64ToResponse(file, response);

    }

    private static void encodeBase64ToResponse(File file, HttpServletResponse response) {

        int length = calculateFileSizeEncodedBase64((int) file.length());
        response.addHeader("Content-Length", length + "");

        Base64Encoder encoder = new Base64Encoder();

        try (InputStream inputStream = new FileInputStream(file); OutputStream os = response.getOutputStream()) {
            byte[] b = IOUtils.toByteArray(inputStream);
            encoder.encode(b, 0, b.length, os);
        } catch (Exception e) {
            logger.error("Fail to fetch report file.", e);
            response.addHeader("Content-Length", "0");
        }
    }

    private static int calculateFileSizeEncodedBase64(int originalSize) {
        int modulus = originalSize % 3;
        int dataLength = originalSize - modulus;
        int length = (dataLength / 3) * 4 + ((modulus == 0) ? 0 : 4);

        return length;
    }
}
