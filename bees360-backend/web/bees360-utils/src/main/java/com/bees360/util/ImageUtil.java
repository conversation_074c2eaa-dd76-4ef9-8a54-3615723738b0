package com.bees360.util;
import java.awt.Graphics;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.List;

import javax.imageio.ImageIO;

import com.bees360.entity.dto.Rectangle;
import com.bees360.entity.enums.SegmentValueTypeEnum;
import com.bees360.entity.vo.ProjectImageSegmentVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ImageUtil {
	private static Logger logger = LoggerFactory.getLogger(ImageUtil.class);

    /**
     * zoom the image
     * @param originalImage The source image
     * @param ratio the zoom value
     * @return
     */
    public static BufferedImage zoomImage(BufferedImage originalImage, double ratio){
        int width = (int)(originalImage.getWidth() * ratio);
        int height = (int)(originalImage.getHeight() * ratio);

        BufferedImage newImage = new BufferedImage(width, height, originalImage.getType());
        Graphics g = newImage.getGraphics();
        g.drawImage(originalImage, 0, 0, width, height, null);
        return newImage;
    }

    public static BufferedImage crop(BufferedImage originalImage, Rectangle screenshot) {
    	int width = (int)screenshot.getWidth();
        int height = (int)screenshot.getHeight();

        BufferedImage newImage = new BufferedImage(width, height, originalImage.getType());
        Graphics g = newImage.getGraphics();
        g.drawImage(originalImage, -(int)screenshot.getX(), -(int)screenshot.getY(),
        		originalImage.getWidth(), originalImage.getHeight(), null);
        return newImage;
    }

    /**
     * zoom the image and out put to a new file
     * @param srcPath the path of the source image
     * @param newPath the path of the destination image
     * @param ratio the zoom value
     * @return true if success, otherwise false
     */
    public static boolean zoomImage(String srcPath, String newPath, double ratio){
        BufferedImage bufferedImage = null;
        File srcImage = new File(srcPath);
        try {
            if(srcImage.exists() && !srcImage.isDirectory() && srcImage.canRead()){
                bufferedImage = ImageIO.read(srcImage);
            } else {
            	return false;
            }
            bufferedImage = zoomImage(bufferedImage, ratio);
            ImageIO.write(bufferedImage, getFileExtision(srcImage), new File(newPath));
        } catch (IOException e) {
        	logger.error("Fail zoom image " + srcPath, e);
            return false;
        }
        return true;
    }

    public static Rectangle getImageBounds(String imagePath) throws IOException {
    	BufferedImage image = ImageIO.read(new File(imagePath));
    	Rectangle bounds = new Rectangle(0, 0, image.getWidth(), image.getHeight());
		return bounds;
    }

    private static String getFileExtision(File file) {
    	if(file == null) {
    		return "";
    	}
    	int lastPoint = file.getName().lastIndexOf(".");
    	return lastPoint < 0? "": file.getName().substring(lastPoint + 1);
    }

	/**
	 * sort by segment tree code, valueType and init parentIds.
	 * @param cellphoneImageList
	 */
    public static void sortSegmentImageByTree(List<ProjectImageSegmentVo> cellphoneImageList) {
		cellphoneImageList.sort((vo1, vo2) -> {
            long[] codes1 = vo1.getCodes();
            long[] codes2 = vo2.getCodes();
            int index = Math.min(codes1.length - 1, codes2.length - 1);
            int result = -1;
            boolean isSort = false;
            for (int i = 0; i < index; i++) {
                if (codes1[i] < codes2[i]) {
                    isSort = true;
                    break;
                }
                if (codes1[i] > codes2[i]) {
                    isSort = true;
                    result = 1;
                    break;
                }
            }
            if (!isSort && codes1.length > codes2.length) {
                isSort = true;
                result = 1;
            }
            if (!isSort && vo1.getValueType() > vo2.getValueType()) {
                result = 1;
            }
            return result;
        });

        String overviewRisk = StringUtils.EMPTY;
        String directionId = StringUtils.EMPTY;
		for (ProjectImageSegmentVo vo : cellphoneImageList) {
			if (vo.getValueType() == SegmentValueTypeEnum.OVERVIEW_RISK.getCode()) {
				overviewRisk = vo.getImageId();
				directionId = StringUtils.EMPTY;
			}
			if (vo.getValueType() == SegmentValueTypeEnum.SYS_DIRECTION_IMAGES.getCode()) {
				directionId = vo.getImageId();
			}
			vo.getParentIds()[0] = overviewRisk;
			vo.getParentIds()[1] = directionId;
		}
	}

    public static void main(String[] args) {
    	long start = System.currentTimeMillis();
    	int times = 20;
    	try {
	    	for(int i = 0; i < times; i ++) {
	    		Rectangle rect = getImageBounds("/home/<USER>/Downloads/1518329484296.JPG");
	    	}
    	} catch (IOException e) {
    		e.printStackTrace();
    	}
    	long end = System.currentTimeMillis();
    	System.out.println("time: " + (end - start) + ", per-time: " + ((end - start) / times));
        return;
    }

}
