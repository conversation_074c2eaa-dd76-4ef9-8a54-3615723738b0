package com.bees360.base.constants;

public enum EnvTypeEnum {

	ENV_TYPE_DEV("dev"),
	ENV_TYPE_STAG("stag"),
	ENV_TYPE_PROD("prod");

	private String type;
	private EnvTypeEnum(String type) {
		this.type = type;
	}

	public String getType() {
		return type;
	}

	public static EnvTypeEnum getEnvType(String type) {
		if(type == null) {
			return null;
		}
		for(EnvTypeEnum envType :EnvTypeEnum.values()) {
			if(type.equals(envType.getType())) {
				return envType;
			}
		}
		return null;
	}
}
