package com.bees360.util;

import java.awt.Color;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Objects;
import javax.imageio.ImageIO;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * picture compression
 *
 * <AUTHOR>
 *
 */
@Slf4j
public class PictureCompression {
    private static final long BIG_PICTURE_THRESHOLD = 5120000;

    public static void main(String[] args) throws IOException {
//        converterWhiteBackground(new File("/home/<USER>/Desktop/15992524382072259.png"), "jpg", new File("/home/<USER>/Desktop/15992524382072259test.jpg"));
        compressPicForScale("/home/<USER>/Desktop/15992524382072259.png", 10, 0.8);
//        System.out.println(isPng("aaa\\.png"));
    }

    public enum CompressType {
        ONCE(1),
        FILE_SIZE(2);
        private final int code;
        CompressType(int code) {
            this.code = code;
        }
        public int getCode() {
            return code;
        }
    }

    public static String compressPicForScale(String imagePath, long desFileSize, double accuracy) throws IOException {
        return compressPicForScale(imagePath, desFileSize, accuracy, CompressType.FILE_SIZE);
    }

    public static String compressPicForScale(String imagePath, long desFileSize, double accuracy, boolean isRename)
        throws IOException {
        return compressPicForScale(imagePath, desFileSize, accuracy, CompressType.FILE_SIZE, isRename);
    }

    public static String compressPicForScale(String imagePath, long desFileSize, double accuracy, CompressType type)
        throws IOException {
        return compressPicForScale(imagePath, desFileSize, accuracy, type, true);
    }

    /**
     * Compress a picture based on the specified size and specified accuracy
     *
     * @param imagePath Source picture address
     * @param desFileSize Specify the size of the picture, unit kb
     * @param accuracy The ratio of precision, recursive compression, suggested less than 0.9
     * @return Compressed file path.
     */
    public static String compressPicForScale(String imagePath, long desFileSize, double accuracy, CompressType type,
        boolean isRename)
        throws IOException {
        if (StringUtils.isEmpty(imagePath)) {
            return null;
        }
        File file = new File(imagePath);
        if (!file.exists()) {
            return null;
        }
        if (file.length() <= desFileSize * 1024) {
            return imagePath;
        }
        float ratio = 1f;
        if (file.length() > BIG_PICTURE_THRESHOLD) {
            // 如果图片较大，压缩率设置小一些减少压缩次数。
            ratio = 0.2f;
        }
        var removeSuffixImagePath = StringUtils.substringBeforeLast(imagePath, FilenameUtils.EXTENSION_SEPARATOR_STR);
        String resultPath = isRename ? removeSuffixImagePath + "tiny.jpg" : imagePath;
        String compressPath = imagePath;
        try {
            // png to jpg
            if (isPng(imagePath)) {
                converterWhiteBackground(new File(imagePath), "jpg", new File(resultPath));
                compressPath = resultPath;
            }
            Thumbnails.of(compressPath).scale(ratio).toFile(resultPath);
            // Recursively compress until the target file size is less than
            // desFileSize
            if (Objects.equals(type, CompressType.FILE_SIZE)) {
                compressPicCycle(resultPath, desFileSize, accuracy);
            }
        } catch (IOException e) {
            log.error("Failed to compress file. image:{}", imagePath, e);
            throw e;
        }
        return resultPath;
    }

	/**
	 * Compress a picture based on the specified size and specified accuracy
     * TODO shoushan.zhao 此方法名字写错了，且异常时不应直接返回null，应抛出IOException，应替换为compressPicForScale
	 *
	 * @param imagePath
	 *            Source picture address
	 * @param desFileSize
	 *            Specify the size of the picture, unit kb
	 * @param accuracy
	 *            The ratio of precision, recursive compression, suggested less
	 *            than 0.9
	 * @return
	 */
	@Deprecated
	public static String commpressPicForScale(String imagePath, long desFileSize, double accuracy) {
        try {
            return compressPicForScale(imagePath, desFileSize, accuracy);
        } catch (IOException e) {
            log.error("Failed to compress file. image:{}", imagePath, e);
            return null;
        }
	}

    private static void converterWhiteBackground(File imgfile, String format, File formatFile) throws IOException {
        BufferedImage bi = ImageIO.read(imgfile);
        // create a blank, RGB, same width and height, and a white background
        BufferedImage newBufferedImage = new BufferedImage(bi.getWidth(), bi.getHeight(), BufferedImage.TYPE_INT_RGB);
        newBufferedImage.createGraphics().drawImage(bi, 0, 0, Color.WHITE, null);
        ImageIO.write(newBufferedImage, format, formatFile);
    }

    private static boolean isPng(String imagePath) {
	    return StringUtils.isNotEmpty(imagePath) && imagePath.toUpperCase().endsWith("PNG");
    }

    private static void compressPicCycle(String resultPath, long desFileSize, double accuracy) throws IOException {
        if (compressImage(resultPath, desFileSize, accuracy)) {
            return;
        }
        compressPicCycle(resultPath, desFileSize, accuracy);
	}

    public static boolean compressImage(String resultPath, long desFileSize, double accuracy) throws IOException {
        File srcFileJPG = new File(resultPath);
        long srcFileSizeJPG = srcFileJPG.length();
        // 2、Judging the size, if less than 200KB, is not compressed;
        // if the greater is equal to 200KB, compression
        if (srcFileSizeJPG <= desFileSize * 1024) {
            return true;
        }
        // High calculation
        BufferedImage bim = ImageIO.read(srcFileJPG);
        int srcWidth = bim.getWidth();
        int srcHeight = bim.getHeight();
        int desWidth = new BigDecimal(srcWidth).multiply(new BigDecimal(accuracy)).intValue();
        int desHeight = new BigDecimal(srcHeight).multiply(new BigDecimal(accuracy)).intValue();

        Thumbnails.of(resultPath).size(desWidth, desHeight).outputQuality(accuracy).toFile(resultPath);
        return false;
    }

}
