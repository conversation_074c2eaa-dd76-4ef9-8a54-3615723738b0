package com.bees360.util.password;

import java.util.LinkedList;
import java.util.Random;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.bees360.util.AlphabetDigitGenerator;
import com.bees360.util.CommonUtil;
import com.google.common.base.Joiner;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;

public class PasswordUtil {
	private static int INITIAL_PASSWORD_LENGTH = 8;
	private static int INITIAL_PASSWORD_LETTER_LENGTH = 4;


	private static final String REGEX_EXPRESSION_PASSWORD = "^(?=.*[a-zA-Z])(?=.*[0-9])(.{8,})";


	public static String getInitialPassword(){
        String randomNumber = RandomStringUtils.randomNumeric(CommonUtil.getRandomNum(1, INITIAL_PASSWORD_LENGTH - 1));
        return generateRandomAlpha(randomNumber.toCharArray(), INITIAL_PASSWORD_LENGTH);
	}

	public static String getInitialPassword(int len){
	    return CommonUtil.randomString(len);
    }

	public static void main(String[] args){
		String s = "@ ./|*&^123qwe";
		String s1 = "@ ./|*&^";
		System.out.println(Pattern.matches(REGEX_EXPRESSION_PASSWORD, s));
		System.out.println(Pattern.matches(REGEX_EXPRESSION_PASSWORD, s1));

	}

//	<EMAIL>: change a more safe algorithms
	/**
	 * use to encrypt password instead of Md5Tool or other tools.
	 */
	public static String encrypt(String password) {
		return Md5Tool.getMd5(password);
	}

	public static String MD5(String s){
		return Md5Tool.MD5(s);
	}
	/**
	 * compare two password directly
	 *
	 * @param rawPassword
	 *        password before encrypted
	 * @param passwordEncrypted
	 *        password encrypted
	 *
	 * @return
	 */
	public static boolean passwordEquals(String rawPassword, String passwordEncrypted){
		if(rawPassword == passwordEncrypted){
			return true;
		}
		if(rawPassword == null || passwordEncrypted == null){
			return false;
		}
		return passwordEncrypted.equals(encrypt(rawPassword));
	}

	/**
	 * compare two password slowly to avoid to being hacked
	 * used in logining
	 * @param rawPassword: password before encrypted
	 * @param passwordEncrypted: password encrypted
	 * @return
	 */
	public static boolean isValidate(String rawPassword, String passwordEncrypted){
		if(rawPassword == passwordEncrypted){
			return true;
		}
		if(rawPassword == null || passwordEncrypted == null){
			return false;
		}
		String password = encrypt(rawPassword);
		int len = Math.min(password.length(), passwordEncrypted.length());
		boolean variable = true;
		for(int i = 0; i < len; i ++){
			variable = variable && (password.charAt(i) == passwordEncrypted.charAt(i));
		}
		return variable;
	}

	public static boolean isAvailablePassword(String password){
		if(password == null){
			return false;
		}
		return Pattern.matches(REGEX_EXPRESSION_PASSWORD, password);
	}

	/**
	 * the total length of password is 8
	 * the letter length of password is 4
	 * @return
	 */
	public static String generatePassword() {
		return AlphabetDigitGenerator.generateWithUppercase(INITIAL_PASSWORD_LENGTH, INITIAL_PASSWORD_LETTER_LENGTH);
	}


    /**
     * 生成一个随机的字符串,必须含有{@code mustContain}中的所有的字符。
     *
     * @param mustContain 必须全部含有的字符串
     * @param length 需要生成的字符串长度
     * @return 生成的随机字符串.
     */
    public static String generateRandomAlpha(char[] mustContain, int length) {
        int alphanumLen = length - mustContain.length;
        LinkedList<Character> randomKey =
            RandomStringUtils.randomAlphabetic(alphanumLen)
                .chars()
                .mapToObj(c -> (char) c)
                .collect(Collectors.toCollection(LinkedList::new));

        for (char ch : mustContain) {
            int index = RandomUtils.nextInt(0, randomKey.size() + 1);
            randomKey.add(index, ch);
        }
        return Joiner.on("").join(randomKey);
    }
}
