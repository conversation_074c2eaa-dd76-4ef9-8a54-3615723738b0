package com.bees360.util.httputil;

import com.google.common.base.Preconditions;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 重定向
 */
@Component
public class UrlRedirection {

    /**
     * 重定向指定的url
     *
     * @param httpServletResponse
     * @param httpStatus
     * @param contentType
     * @param url
     * @throws Exception
     */
    public void redirect(HttpServletResponse httpServletResponse, HttpStatus httpStatus, String contentType, String url) throws Exception {
        Preconditions.checkNotNull(httpStatus, "http status should not be empty.");
        Preconditions.checkNotNull(contentType, "content-type should not be empty.");
        Preconditions.checkNotNull(url, "url should not be empty.");
        httpServletResponse.setStatus(httpStatus.value());
        httpServletResponse.setContentType(contentType);
        httpServletResponse.setHeader("Location", url);
    }
}
