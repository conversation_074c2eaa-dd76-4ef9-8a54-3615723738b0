package com.bees360.util;

import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.Polygon;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.imageio.ImageIO;

import com.bees360.entity.dto.Point;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ImageMappingUtil {
	private static Logger logger = LoggerFactory.getLogger(ImageMappingUtil.class);

	public static boolean drawMappingPolygon(String srcFilePath, String destFilePath, double ratio, int width,
			int height, List<List<Point>> pointsList) throws Exception {
		BufferedImage bfImage = null;
		File srcFile = new File(srcFilePath);
		File destFile = new File(destFilePath);
		if (!srcFile.exists()) {
			logger.error("fail to draw image for file: " + srcFile.getAbsolutePath() + " not exist.");
			return false;
		}
		try {
			bfImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
			bfImage = ImageIO.read(srcFile);
			Graphics2D g = (Graphics2D)bfImage.getGraphics();
			g.setColor(Color.decode("#30B44E"));
			g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, 0.5f));
			if (null != pointsList && pointsList.size() > 0) {
				for (List<Point> pointList : pointsList) {
					if (pointList != null && pointList.size() > 0) {
						Polygon polygon = new Polygon();
						for (Point p : pointList) {
							int x = (int) Math.round(p.getX() * ratio);
							int y = (int) Math.round(p.getY() * ratio);
							polygon.addPoint(x, y);
						}
						g.fillPolygon(polygon);
					}
				}
			}
			String extension = getExtension(srcFilePath);
			// It can't create any directory
			ImageIO.write(bfImage, extension, destFile);
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	private static String getExtension(String filePath) {
		int lastPoint = filePath.lastIndexOf('.');
		if (lastPoint < 0) {
			return "JPG";
		} else {
			return filePath.substring(lastPoint + 1);
		}
	}

	public static void main(String[] args) throws Exception {
		String srcFilePath = "/home/<USER>/桌面/drow/1526089322471.jpeg";
		String destFilePath = "/home/<USER>/桌面/drow/1526089322472.jpeg";
		List<List<Point>> pointsList = new ArrayList<List<Point>>();
		List<Point> pointList = new ArrayList<>();
		Point p1 = new Point();
		p1.setX(1332.5280121810365);
		p1.setY(2276.503093746943);
		pointList.add(p1);
		Point p2 = new Point();
		p2.setX(1299.0903951569992);
		p2.setY(2236.088320502039);
		pointList.add(p2);
		Point p3 = new Point();
		p3.setX(1274.4280495224575);
		p3.setY(2260.2562285458503);
		pointList.add(p3);
		Point p4 = new Point();
		p4.setX(1332.5280121810365);
		p4.setY(2276.503093746943);
		pointList.add(p4);

		List<Point> pointList1 = new ArrayList<>();
		Point p11 = new Point();
		p11.setX(1316.7853229320342);
		p11.setY(1970.8867752583599);
		pointList1.add(p11);
		Point p12 = new Point();
		p12.setX(1590.587579207897);
		p12.setY(1658.07012580918);
		pointList1.add(p12);
		Point p13 = new Point();
		p13.setX(1174.9122037895888);
		p13.setY(1751.085250945659);
		pointList1.add(p13);
		Point p14 = new Point();
		p14.setX(1316.7853229320342);
		p14.setY(1970.8867752583599);
		pointList1.add(p14);

		List<Point> pointList2 = new ArrayList<>();
		Point p21 = new Point();
		p21.setX(1273.6769376708771);
		p21.setY(2260.019760938117);
		pointList2.add(p21);
		Point p22 = new Point();
		p22.setX(1298.4480336700763);
		p22.setY(2235.9159751491397);
		pointList2.add(p22);
		Point p23 = new Point();
		p23.setX(1317.661932126926);
		p23.setY(1970.785860510658);
		pointList2.add(p23);
		Point p24 = new Point();
		p24.setX(1176.6286113925585);
		p24.setY(1750.166717783814);
		pointList2.add(p24);
		Point p25 = new Point();
		p25.setX(1148.3740920521716);
		p25.setY(1748.043333165284);
		pointList2.add(p25);
		Point p26 = new Point();
		p26.setX(1116.4942804576112);
		p26.setY(2224.0095846461745);
		pointList2.add(p26);
		Point p27 = new Point();
		p27.setX(1273.6769376708771);
		p27.setY(2260.019760938117);
		pointList2.add(p27);
		pointsList.add(pointList);
		pointsList.add(pointList1);
		pointsList.add(pointList2);
		drawMappingPolygon(srcFilePath, destFilePath, 0.07309941520467836257, 5472, 3078, pointsList);
	}
}
