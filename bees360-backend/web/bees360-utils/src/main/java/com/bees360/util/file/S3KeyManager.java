package com.bees360.util.file;

import com.bees360.common.constants.Punctuation;
import com.bees360.util.CommonUtil;
import com.google.common.base.Preconditions;
import org.springframework.stereotype.Component;

/**
 * s3 key 管理器
 */
@Component
public class S3KeyManager {

    private static final String PROJECT_ID = "projectId";
    private static final String FILENAME = "filename";
    private static final String S3_KEY_IMAGES_ARCHIVE_PATH = "project/projectId/images/archive/filename";

    /**
     * 返回s3的归档包的完整路径
     *
     * @return
     */
    public String getImagesArchiveFileKey(long projectId, String fileName) {
        String key = S3_KEY_IMAGES_ARCHIVE_PATH.replace(PROJECT_ID, String.valueOf(projectId));
        key = key.replace(FILENAME, fileName);
        return key;
    }

    /**
     * 生成project image archive的文件名
     * @param projectId
     * @param address
     * @param ext
     * @return
     */
    public String generateImageArchiveFileName(long projectId, String address, String ext) {
        return projectId + Punctuation.UNDERLINE + address + Punctuation.UNDERLINE + CommonUtil.randomNumber(6) + ext;
    }

    public String getFileNameFromImageArchiveFileKey(String archiveFileKey) {
        Preconditions.checkNotNull(archiveFileKey, "archive file key should not be null.");
        if(archiveFileKey.lastIndexOf("/") > 0) {
            return archiveFileKey.substring(archiveFileKey.lastIndexOf("/") + 1);
        } else {
            return archiveFileKey;
        }
    }
}
