package com.bees360.util.maps.googlemaps;

import java.util.ArrayList;
import java.util.List;

import com.google.maps.GeoApiContext;
import com.google.maps.StaticMapsRequest;
import com.google.maps.internal.StringJoin;
import com.google.maps.internal.StringJoin.UrlValue;

public class ExtendStaticMapsRequest extends StaticMapsRequest {

	public ExtendStaticMapsRequest(GeoApiContext context) {
		super(context);
	}

	public StaticMapsRequest styles(Style style) {
		return this.paramAddToList("style", style.toUrlValue());
	}

	public StaticMapsRequest style(String style) {
		return this.paramAddToList("style", style);
	}

	/**
	 * Customize the presentation of the standard Google map by applying your own
	 * styles when using the Maps Static API. You can change the visual display of
	 * features such as roads, parks, built-up areas, and other points of interest.
	 * Change their color or style to emphasize particular content, complement
	 * surrounding content on the page, or even hide features completely. you can
	 * see more detail in
	 * https://developers.google.com/maps/documentation/maps-static/styling
	 *
	 * <AUTHOR>
	 * @date 2018-11-10
	 */
	public static class Style implements UrlValue {
		private String feature;
		private String element;
		private String hue;
		private Double lightness;
		private Double saturation;
		private Double gamma;
		private Boolean invertLightness;
		private String color;
		private String visibility;
		private Integer weight;

		/**
		 * more information: https://developers.google.com/maps/documentation/maps-static/styling#features
		 * @param feature the feature to
		 */
		public void feature(String feature) {
			this.feature = feature;
		}
		/**
		 * more information: https://developers.google.com/maps/documentation/maps-static/styling#elements
		 * @param element the element to
		 */
		public void element(String element) {
			this.element = element;
		}
		/**
		 * an RGB hex string of format #RRGGBB
		 * @param hue the hue to
		 */
		public void hue(String hue) {
			this.hue = hue;
		}
		/**
		 * (a floating point value between -100 and 100)
		 * indicates the percentage change in brightness of the element.
		 * @param lightness the lightness to
		 */
		public void lightness(double lightness) {
			this.lightness = lightness;
		}
		/**
		 * (a floating point value between -100 and 100)
		 * indicates the percentage change in intensity of the basic color to apply to the element.
		 * @param saturation the saturation to
		 */
		public void saturation(double saturation) {
			this.saturation = saturation;
		}
		/**
		 * (a floating point value between 0.01 and 10.0, where 1.0 applies no correction)
		 * indicates the amount of gamma correction to apply to the element.
		 * @param gamma the gamma to
		 */
		public void gamma(double gamma) {
			this.gamma = gamma;
		}
		/**
		 * (if true) inverts the existing lightness.
		 * @param invertLightness the invertLightness to
		 */
		public void invertLightness(boolean invertLightness) {
			this.invertLightness = invertLightness;
		}
		/**
		 * an RGB hex string of format 0xRRGGBB
		 * @param color the color to
		 */
		public void color(String color) {
			this.color = color;
		}
		/**
		 * (on, off, or simplified) indicates whether and how the element appears on the map.
		 * @param visibility the visibility to
		 */
		public void visibility(String visibility) {
			this.visibility = visibility;
		}
		/**
		 * (an integer value, greater than or equal to zero) s the weight of the feature, in pixels.
		 * @param weight the weight to
		 */
		public void weight(int weight) {
			this.weight = weight;
		}

		@Override
		public String toUrlValue() {
			List<String> urlParts = new ArrayList<>();

			if (feature != null) {
				urlParts.add("feature:" + feature);
			}
			if (element != null) {
				urlParts.add("element:" + element);
			}
			if(hue != null) {
				urlParts.add("hue:" + hue);
			}
			if(lightness != null) {
				urlParts.add("lightness:" + lightness);
			}
			if(saturation != null) {
				urlParts.add("saturation:" + saturation);
			}
			if(gamma != null) {
				urlParts.add("gamma:" + gamma);
			}
			if(invertLightness != null) {
				urlParts.add("invert_lightness:" + invertLightness);
			}
			if(gamma != null) {
				urlParts.add("gamma:" + gamma);
			}
			if (visibility != null) {
				urlParts.add("visibility:" + visibility);
			}
			if(color != null) {
				urlParts.add("color:" + color);
			}
			if (weight != null) {
				urlParts.add("weight" + weight);
			}
			return StringJoin.join('|', urlParts.toArray(new String[urlParts.size()]));
		}
	}
}
