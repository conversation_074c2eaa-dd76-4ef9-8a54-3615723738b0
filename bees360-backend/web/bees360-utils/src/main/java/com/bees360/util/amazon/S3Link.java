package com.bees360.util.amazon;

import java.util.Objects;

public class S3Link {
    private String s3Host;
    private String bucketName;
    private String key;

    private final String LINK_AMAZONS3 = "s3.amazonaws.com";
    private final String PREFIX_HTTPS = "https://";
    private final String PREFIX_HTTP = "http://";

    public S3Link(){
        s3Host = LINK_AMAZONS3;
        bucketName = "";
        key = "";
    }

    public S3Link(String s3Url){
        if(s3Url == null){
            return;
        }
        init(s3Url);
    }

    public S3Link(String bucketName, String key){
        this.s3Host = LINK_AMAZONS3;
        this.bucketName = bucketName;
        if(key.startsWith("/")){
            this.key = key.substring(1);
        } else {
            this.key = key;
        }
    }

    private void init(String s3Url){
        s3Url = removeHttpOrHttps(s3Url);
        if(s3Url.startsWith(LINK_AMAZONS3)) {
            initBucketInPathType(s3Url);
        } else {
            initBucketInHostType(s3Url);
        }
    }

    private String removeHttpOrHttps(String s3Url){
        if(s3Url.startsWith(PREFIX_HTTPS)){
            return s3Url.substring(PREFIX_HTTPS.length());
        } else if(s3Url.startsWith(PREFIX_HTTP)) {
            return s3Url.substring(PREFIX_HTTP.length());
        }
        return s3Url;
    }

    private void initBucketInPathType(String s3Url){
        s3Host = LINK_AMAZONS3;
        s3Url = s3Url.replace(LINK_AMAZONS3 + "/", "");
        int bucketIndex = s3Url.indexOf("/");
        if(bucketIndex < 0){
            bucketName = "";
            key = s3Url;
        } else {
            bucketName = s3Url.substring(0, bucketIndex);
            key = s3Url.substring(bucketIndex + 1);
        }
    }

    private void initBucketInHostType(String s3Url){
        s3Host = LINK_AMAZONS3;
        int bucketIndex = s3Url.indexOf(LINK_AMAZONS3);
        if(bucketIndex < 0){
            bucketName = "";
            key = s3Url;
        } else {
            bucketName = s3Url.substring(0, bucketIndex - 1);
            key = s3Url.substring(bucketIndex + LINK_AMAZONS3.length() + 1);
        }
        if(key.startsWith("/")){
            key = key.substring(1);
        }
    }

    public String toBucketInHostType(){
        return PREFIX_HTTPS + bucketName + "." + LINK_AMAZONS3 + "/" + key;
    }

    public String toBucketInPathType(){
        return PREFIX_HTTPS + LINK_AMAZONS3 + "/" + bucketName + "/" + key;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getS3Host() {
        return s3Host;
    }

    public void setS3Host(String s3Host) {
        this.s3Host = s3Host;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (!(o instanceof S3Link))
            return false;
        S3Link s3Link = (S3Link)o;
        return Objects.equals(getS3Host(), s3Link.getS3Host()) && Objects
            .equals(getBucketName(), s3Link.getBucketName()) && Objects.equals(getKey(), s3Link.getKey());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getS3Host(), getBucketName(), getKey());
    }

    @Override
    public String toString() {
        return "S3Link [s3Host=" + s3Host + ", bucketName=" + bucketName + ", key=" + key + "]";
    }
}
