package com.bees360.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * tool classes for attribute file manipulation access, add, modify Note: the
 * following method reads the property file and caches the problem. It doesn't
 * work when the property file is modified, InputStream in =
 * PropertiesUtils.class.getResourceAsStream("/application-${ENV}.properties"); Terms of
 * settlement： String savePath =
 * PropertiesUtils.class.getResource("/application-${ENV}.properties").getPath();
 *
 * [!important] don't provide any method to set properties.
 * If you must to, please provide a method to create unmodified map from properties.
 *
 * <AUTHOR> Email：<EMAIL> date：2017-10-12
 * @modified guanrong.yang date: 2017/11/17
 * @modified Starr date: 2018/4/1
 * just do one thing : reading property from existed application-${ENV}.properties,and only providing getProperty() api for caller
 *
 * @version 1.0v
 */
public class PropertiesUtils extends PropertiesStorage {

    public static Properties getResourcesProperties(String filePath) {
        return getProperties(PropertiesUtils.class, filePath);
    }

    public static Properties getProperties(Class<?> clazz, String filePath) {
        Properties properties = new Properties();
        try (InputStream is = clazz.getClassLoader().getResourceAsStream(filePath)) {
            properties.load(is);
        } catch (IOException e) {
            throw new RuntimeException("Fail to load [" + filePath + "] as a properties.", e);
        }
        return properties;
    }

    public static Properties getProperties(File file) {
        Properties properties = new Properties();
        FileInputStream fis;
        try {
            fis = new FileInputStream(file);
            properties.load(fis);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return properties;
    }
}
