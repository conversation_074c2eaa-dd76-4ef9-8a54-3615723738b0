package com.bees360.util.payment;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import com.bees360.entity.Product;
import com.bees360.entity.enums.productandpayment.ProductTypeEnum;

/**
 * <AUTHOR> Yang
 */
public class ProductStore implements Iterable<Product>{

	private List<Product> products;

	public ProductStore(List<Product> products) {
		this.products = products;
	}

	@Override
	public Iterator<Product> iterator() {
		return products == null? new ArrayList<Product>().iterator(): products.iterator();
	}

	public List<Product> filter(ProductTypeEnum productType) {
		if(productType == null) {
			new ArrayList<>();
		}
		return this.products.stream()
			.filter(p -> p.getProductType() == productType.getCode())
			.collect(Collectors.toList());
	}

	public Product getByType(ProductTypeEnum productType, int internalType) {
		if(productType == null) {
			return null;
		}
		for(Product p: products) {
			if(p.getProductType() == productType.getCode() && p.getInternalType() == internalType) {
				return p;
			}
		}
		return null;
	}

	public Product getById(int productId) {
		for(Product p: products) {
			if(p.getProductId() == productId) {
				return p;
			}
		}
		return null;
	}

	public boolean contains(ProductTypeEnum productType, int internalType) {
		return getByType(productType, internalType) != null;
	}

	public boolean contains(int productId) {
		return getById(productId) != null;
	}
}
