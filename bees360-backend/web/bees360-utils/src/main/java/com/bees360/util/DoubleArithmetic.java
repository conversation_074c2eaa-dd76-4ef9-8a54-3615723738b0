package com.bees360.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class DoubleArithmetic {
	public static double add(double d1, double d2) {
		BigDecimal b1 = new BigDecimal(Double.toString(d1));
        BigDecimal b2 = new BigDecimal(Double.toString(d2));
        return b1.add(b2).doubleValue();
	}

	public static double sub(double minuend, double subtrahend) {
		BigDecimal b1 = new BigDecimal(Double.toString(minuend));
        BigDecimal b2 = new BigDecimal(Double.toString(subtrahend));
        return b1.subtract(b2).doubleValue();
	}

	public static double mul(double d1, double d2) {
		BigDecimal b1 = new BigDecimal(Double.toString(d1));
        BigDecimal b2 = new BigDecimal(Double.toString(d2));
        return b1.multiply(b2).doubleValue();
	}

	public static double div(double dividend, double divisor) {
		BigDecimal b1 = new BigDecimal(Double.toString(dividend));
        BigDecimal b2 = new BigDecimal(Double.toString(divisor));
        return b1.divide(b2).doubleValue();
	}

	/**
	 *
	 * @param dividend
	 * @param divisor
	 * @param scale scale of the quotient to be returned.
	 * @return round off the results
	 */
	public static double div(double dividend, double divisor, int scale) {
		if(scale < 0) {
			throw new IllegalArgumentException("The scale must be a positive integer or zero");
		}
		BigDecimal b1 = new BigDecimal(Double.toString(dividend));
        BigDecimal b2 = new BigDecimal(Double.toString(divisor));
        return b1.divide(b2, scale, RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 *
	 * @param d
	 * @param scale scale of the quotient to be returned.
	 * @return round off the results
	 */
	public static double div(double d, int scale) {
		return div(d, 1, scale);
	}

	/**
	 * 遇到.5的情况时往上近似,例: 1.5 ->;2
	 * set scale default 2
	 * @param d
	 * @param scale
	 * @return
	 */
	public static double reserveFractionsUp(double d) {
		return roundHalfUp(d, 2);
	}

	/**
	 * 遇到.5的情况时往下近似,例: 1.5 -> 1
	 * set scale default 2
	 * @param d
	 * @param scale
	 * @return
	 */
	public static double reserveFractionsDown(double d) {
		return roundHalfDown(d, 2);
	}

	/**
	 * 默认对小数点后1位进行银行家四舍五入,不保留小数位数(主要是因为square支付时用分作单位四舍五入)
	 * @param d
	 * @return
	 */
	public static double roundHalfEven(double d) {
		return roundHalfEven(d, 0);
	}

	/**
	 * 银行家舍入法
	 * @param d
	 * @param scale
	 * @return
	 */
	public static double roundHalfEven(double d, int scale) {
		return new BigDecimal(Double.toString(d)).setScale(scale, RoundingMode.HALF_EVEN).doubleValue();
	}


	public static double roundHalfUp(double d, int scale) {
		return new BigDecimal(Double.toString(d)).setScale(scale, RoundingMode.HALF_UP).doubleValue();
	}

	public static double roundHalfDown(double d, int scale) {
		return new BigDecimal(Double.toString(d)).setScale(scale, RoundingMode.HALF_DOWN).doubleValue();
	}

	public static void main(String[] args) {
		double d = 2.475;
		int scale = 2;
		double r = roundHalfEven(d,scale);
		System.out.println("result:" + r);

		double d2 = 247.50;
		double r2 = roundHalfEven(d2,scale);
		System.out.println("result:" + r2);

		double changedAmount = 2706.25;
		double bchangedAmount = DoubleArithmetic.roundHalfEven(changedAmount);
		bchangedAmount = DoubleArithmetic.div(bchangedAmount, 100);
		System.out.println(bchangedAmount);
	}
}
