package com.bees360.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class DoubleArithmetic2 {

	private BigDecimal result;

	private static final int DEF_DIV_SCALE = 32;

	public DoubleArithmetic2(double d1) {
		result = new BigDecimal(Double.toString(d1));
	}

	public DoubleArithmetic2 add(double d2) {
		// 这里必须使用 BigDecimal(String val)的构造参数，不能使用BigDecimal(double val)
		// BigDecimal(double val) 依旧会存在double的精度问题
        BigDecimal b2 = new BigDecimal(Double.toString(d2));
        result = result.add(b2);
        return this;
	}

	public DoubleArithmetic2 sub(double subtrahend) {
        BigDecimal b2 = new BigDecimal(Double.toString(subtrahend));
        result = result.subtract(b2);
        return this;
	}

	public DoubleArithmetic2 subBy(double subtrahend) {
        BigDecimal b2 = new BigDecimal(Double.toString(subtrahend));
        result = b2.subtract(result);
        return this;
	}

	public DoubleArithmetic2 mul(double d2) {
        BigDecimal b2 = new BigDecimal(Double.toString(d2));
        result = result.multiply(b2);
        return this;
	}

	public DoubleArithmetic2 div(double divisor) {
        BigDecimal b2 = new BigDecimal(Double.toString(divisor));
        result = result.divide(b2, DEF_DIV_SCALE, RoundingMode.HALF_EVEN);
        return this;
	}

	public DoubleArithmetic2 divBy(double dividend) {
        BigDecimal b2 = new BigDecimal(Double.toString(dividend));
        result = b2.divide(result);
        return this;
	}

	public DoubleArithmetic2 clean() {
		result = new BigDecimal("0");
		return this;
	}

	public DoubleArithmetic2 reset(double d1) {
		result = new BigDecimal(Double.toString(d1));
		return this;
	}

	public BigDecimal result() {
		return result;
	}

	public double doubleResult() {
		return result.doubleValue();
	}

	public int intResult() {
		return result.intValue();
	}

	/**
	 * 四舍五入
	 * <pre>
	 * roundHalfUpResult(2.125, 2) = 2.13
	 * roundHalfUpResult(2.124, 2) = 2.12
	 * </pre>
	 * @param scale 保留的小数位数
	 * @return
	 */
	public double roundHalfUpResult(int scale) {
		return result.setScale(scale, RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 * 五舍六入
	 * <pre>
	 * roundHalfDownResult(2.125, 2) = 2.12
	 * roundHalfDownResult(2.126, 2) = 2.13
	 * </pre>
	 * @param scale 保留的小数位数
	 * @return
	 */
	public double roundHalfDownResult(int scale) {
		return result.setScale(scale, RoundingMode.HALF_DOWN).doubleValue();
	}

	/**
	 * 保留小数点后指定位数，且保留位后的小数位只要仍有值则进一
	 * <pre>
	 * roundUpResult(2.12000001, 2) = 2.13
	 * </pre>
	 * @param scale 保留的小数位数
	 * @return
	 */
	public double roundUpResult(int scale) {
		return result.setScale(scale, RoundingMode.UP).doubleValue();
	}
	/**
	 * 保留小数点后指定位数，且保留位后的小数位均舍弃
	 * <pre>
	 * roundDownResult(2.12999999, 2) = 2.12
	 * </pre>
	 * @param scale 保留的小数位数
	 * @return
	 */
	public double roundDownResult(int scale) {
		return result.setScale(scale, RoundingMode.DOWN).doubleValue();
	}

	/**
	 * 银行家算法
	 * 向最接近数字方向舍入，如果与两个相邻数字的距离相等，则向相邻的偶数舍入。
	 * <pre>
	 * roundHalfEven(2.124, 2) = 2.12
	 * roundHalfEven(2.125, 2) = 2.12
	 * roundHalfEven(2.126, 2) = 2.13
	 * roundHalfEven(2.136, 2) = 2.14
	 * </pre>
	 * @param scale
	 * @return
	 */
	public double roundHalfEven(int scale) {
		return result.setScale(scale, RoundingMode.HALF_EVEN).doubleValue();
	}

	public static void main(String[] args) {
		System.out.println(new DoubleArithmetic2(2.12000001).roundUpResult(2) + " = 2.13");
		System.out.println(new DoubleArithmetic2(2.12999999).roundDownResult(2) + " = 2.12");

		System.out.println(new DoubleArithmetic2(2.125).roundHalfUpResult(2) + " = 2.13");
		System.out.println(new DoubleArithmetic2(2.124).roundHalfUpResult(2) + " = 2.12");

		System.out.println(new DoubleArithmetic2(2.125).roundHalfDownResult(2) + " = 2.12");
		System.out.println(new DoubleArithmetic2(2.126).roundHalfDownResult(2) + " = 2.13");

		System.out.println(new DoubleArithmetic2(2.124).roundHalfEven(2) + " = 2.12");
		System.out.println(new DoubleArithmetic2(2.125).roundHalfEven(2) + " = 2.12");
		System.out.println(new DoubleArithmetic2(2.126).roundHalfEven(2) + " = 2.13");
		System.out.println(new DoubleArithmetic2(2.135).roundHalfEven(2) + " = 2.14");
	}
}
