package com.bees360.util.pdf.gs;

import com.bees360.util.file.SizeReduce;
import com.bees360.util.pdf.PdfSizeReducer;
import com.google.common.base.Preconditions;
import java.time.Duration;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;

/**
 * <AUTHOR>
 */
@Slf4j
public class GsPdfSizeReducer implements PdfSizeReducer, SizeReduce {

    private GsCaller gsCaller;

    private Duration gsTimeout = Duration.ofMinutes(25);
    private Duration timeout = Duration.ofMinutes(60);

    public GsPdfSizeReducer() {
        gsCaller = new GsCaller();
    }

    public void setTimeout(@Nullable Duration timeout) {
        this.timeout = timeout;
    }

    @Override
    public void reduceSizeWithDpi(File pdf, File output, int dpi) throws IOException {
        Preconditions.checkArgument(dpi > 0, "param `dpi` may not less than 0");

        gsCaller.reducePdfSize(pdf, output, dpi, gsTimeout);
    }

    @Override
    public int reduceSizeWithLimit(File pdf, File output, long limitSize, int maxDpi) throws IOException {
        Preconditions.checkArgument(limitSize > 0, "param `limitSize` may not less than 0");
        Preconditions.checkArgument(maxDpi > 0, "param `maxDpi` may not less than 0");

        if (limitSize >= pdf.length()) {
            log.info("file({}) will be copied to output({}) since its size({}) <= limit({}).", pdf, output, pdf.length(),
                limitSize);
            FileUtils.copyFile(pdf, output);
            return NO_DPI;
        }
        int leftDpi = 0;
        // 使得后续使用maxDpi进行压缩，同时避免了midDpi压缩时为0
        int rightDpi = maxDpi * 2;
        long pdfSize = pdf.length();
        int midDpi = -1;
        long start = System.currentTimeMillis();
        while (rightDpi - leftDpi > 1 && leftDpi < maxDpi) {
            failIfTimeout(System.currentTimeMillis() - start);
            midDpi = (leftDpi + rightDpi) / 2;
            reduceSizeWithDpi(pdf, output, midDpi);
            long midSize = output.length();

            log.info("Pdf file is compressed from {} to {} with dpi {}, while limitSize is {}. " +
                "(leftDpi: {}, rightDpi: {})", pdfSize, midSize, midDpi, limitSize, leftDpi, rightDpi);

            if (midSize == limitSize) {
                return midDpi;
            } else if (midSize < limitSize) {
                leftDpi = midDpi;
            } else {
                rightDpi = midDpi;
            }
        }
        if (leftDpi == 0) {
            // 每次压缩的结果都比目标size要大
            throw new IOException("Unable to reduce the size of the report to under " + limitSize + " byte.");
        }
        if(leftDpi != midDpi) {
            // leftDpi才是目标dpi，如果不等，则表示midSize > limitSize
            reduceSizeWithDpi(pdf, output, leftDpi);
        }

        long end = System.currentTimeMillis();
        log.info("Compressing pdf ({}) cost {} ms and final dpi is {}", pdf, (end - start), leftDpi);

        return leftDpi;
    }

    private void failIfTimeout(long cost) throws IOException {
        if (timeout == null) {
            return;
        }
        if (cost > timeout.toMillis()) {
            var message = "Gs Compress pdf timeout with cost {}ms and timeout {}ms".formatted(cost, timeout.toMillis());
            throw new IOException(message);
        }
    }

    @Override
    public int reduceSizeWithLimit(File pdf, OutputStream output, long limitSize, int maxDpi) throws IOException {
        Preconditions.checkArgument(limitSize > 0, "param `limitSize` may not less than 0");
        Preconditions.checkArgument(maxDpi > 0, "param `maxDpi` may not less than 0");

        File resultPdf = null;
        try {
            resultPdf = createTempOutputPdf(pdf);
            int finalDpi = reduceSizeWithLimit(pdf, resultPdf, limitSize, maxDpi);
            fileToOutput(resultPdf, output);
            log.info(resultPdf + " is created.");
            return finalDpi;
        } finally {
            // 删除临时文件，防止对磁盘的占用
            if (resultPdf != null && resultPdf.exists()) {
                resultPdf.delete();
                log.info(resultPdf + " is deleted.");
            }
        }
    }

    @Override
    public int reduceSizeWithLimit(File pdf, File output, long limitSize) throws IOException {
        Preconditions.checkArgument(limitSize > 0, "param `limitSize` may not less than 0");

        final int maxDpi = 300;
        return reduceSizeWithLimit(pdf, output, limitSize, maxDpi);
    }

    @Override
    public int reduceSizeWithLimit(File pdf, OutputStream output, long limitSize) throws IOException {
        Preconditions.checkArgument(limitSize > 0, "param `limitSize` may not less than 0");

        final int maxDpi = 300;
        return reduceSizeWithLimit(pdf, output, limitSize, maxDpi);
    }

    private File createTempOutputPdf(File pdf) {
        File tempDir = new File(FileUtils.getTempDirectory(), this.getClass().getSimpleName());
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
        File tempFile = new File(tempDir, System.currentTimeMillis() + "-" + pdf.getName());
        return tempFile;
    }

    private void fileToOutput(File file, OutputStream output) throws IOException {
        try (FileInputStream input = new FileInputStream(file)) {
            IOUtils.copy(input, output);
        }
    }

    @Override
    public boolean reduce(File input, File out, int limitSize) throws IOException {
        return NO_DPI != this.reduceSizeWithLimit(input, out, limitSize);
    }

    @Override
    public boolean reduce(File input, OutputStream out, int limitSize) throws IOException {
        return NO_DPI != this.reduceSizeWithLimit(input, out, limitSize);
    }
}
