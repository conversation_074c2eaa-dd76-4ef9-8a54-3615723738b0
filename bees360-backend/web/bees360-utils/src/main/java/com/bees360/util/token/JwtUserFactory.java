package com.bees360.util.token;

import java.util.List;
import java.util.stream.Collectors;

import com.bees360.entity.User;
import com.bees360.entity.dto.IdNameDto;

public final class JwtUserFactory {

    private JwtUserFactory() {}

    public static JwtUser create(User user) {
    	JwtUser jwtUser = new JwtUser();

    	jwtUser.setUserId(user.getUserId());
    	jwtUser.setName(user.getName());
    	jwtUser.setPhone(user.getPhone());
    	jwtUser.setEmail(user.getEmail());
    	jwtUser.setAvatar(user.getAvatar());

    	List<IdNameDto> roles = user.listRoles().stream()
    			.map(role -> new IdNameDto(role.getRoleId(), role.getDisplay()))
    			.collect(Collectors.toList());

    	jwtUser.setRoles(roles);

    	return jwtUser;
    }
}
