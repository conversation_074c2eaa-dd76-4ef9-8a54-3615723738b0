package com.bees360.util.enumsutil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.enums.BaseCodeEnum;

public class CodeEnumUtil {
//	transfer code to enum
	public static <E extends Enum<?> & BaseCodeEnum> E getEnumByCode(Class<E> enumClass, Integer code){
		if(code == null){
			return null;
		}
		E[] enumConstants = enumClass.getEnumConstants();
		for(E e: enumConstants){
			if(e.getCode() == code){
				return e;
			}
		}
		return null;
	}

	public static <E extends Enum<?> & BaseCodeEnum> List<IdNameDto> enumsToIdName(Class<E> enumClass){
		List<IdNameDto> list = new ArrayList<IdNameDto>();
		E[] enumConstants = enumClass.getEnumConstants();
		for(BaseCodeEnum codeEnum: enumConstants){
			list.add(new IdNameDto(codeEnum.getCode(), codeEnum.getDisplay()));
		}
		return list;
	}

    public static IdNameDto enumsToIdName(BaseCodeEnum codeEnum) {
        return new IdNameDto(codeEnum.getCode(), codeEnum.getDisplay());
    }

    public static List<IdNameDto> enumsToIdName(BaseCodeEnum[] enums, BaseCodeEnum stopCode) {
		List<IdNameDto> list = new ArrayList<IdNameDto>();
		for(BaseCodeEnum codeEnum: enums){
            if (stopCode.getCode() == codeEnum.getCode()) {
                continue;
			}
            list.add(new IdNameDto(codeEnum.getCode(), codeEnum.getDisplay()));
		}
		return list;
	}
}
