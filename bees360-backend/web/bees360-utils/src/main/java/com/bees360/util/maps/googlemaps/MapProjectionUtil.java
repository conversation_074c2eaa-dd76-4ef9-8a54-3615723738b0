package com.bees360.util.maps.googlemaps;

import com.bees360.util.maps.common.MercatorProjection;
import com.bees360.util.maps.model.LatLngPoint;
import com.bees360.util.maps.model.Pixel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/02/09 17:56
 */
public class MapProjectionUtil {

    private int width;
    private int heigh;

    /**
     * <code>zoom</code> (required if markers not present) defines the zoom
     * level of the map, which determines the magnification level of the map.
     *
     * 1: World, 5: Landmass/continent, 10: City, 15: Streets, 20: Buildings You
     * can see more detail from
     * https://developers.google.com/maps/documentation/maps-static/dev-guide#Zoomlevels
     */
    private int zoom;

    private MercatorProjection projection;

    public MapProjectionUtil(int width, int heigh, int zoom, int titleSize) {
        this.width = width;
        this.heigh = heigh;
        this.zoom = zoom;
        this.projection = new MercatorProjection(titleSize);
    }

    /**
     * Reverse the point in the image generated from this util
     */
    public LatLngPoint reversePointToMap(LatLngPoint imgCenterLatLng, Pixel imgPixel) {
        Pixel imageCenterInPixelCoord = projection.fromLatLngToPoint(imgCenterLatLng, zoom);
        return reversePointsToMap(imageCenterInPixelCoord, imgPixel);
    }

    private LatLngPoint reversePointsToMap(Pixel imageCenterInPixelCoord, Pixel imgPixel) {
        Pixel imageCenter = new Pixel(width / 2, heigh / 2);
        double pixelCoordOffsetX = imageCenterInPixelCoord.getX() - imageCenter.getX();
        double pixelCoordOffsetY = imageCenterInPixelCoord.getY() - imageCenter.getY();

        Pixel pixelInPixelCoord = new Pixel(
            imgPixel.getX() + pixelCoordOffsetX,
            imgPixel.getY() + pixelCoordOffsetY);

        return projection.fromPointToLatLng(pixelInPixelCoord, zoom);
    }

    public List<LatLngPoint> reversePointToMap(LatLngPoint imgCenterLatLng, List<Pixel> imgPixels) {
        Pixel imageCenterInPixelCoord = projection.fromLatLngToPoint(imgCenterLatLng, zoom);
        List<LatLngPoint> latLngs = new ArrayList<LatLngPoint>();
        for(Pixel imgPixel: imgPixels) {
            latLngs.add(reversePointsToMap(imageCenterInPixelCoord, imgPixel));
        }
        return latLngs;
    }
}
