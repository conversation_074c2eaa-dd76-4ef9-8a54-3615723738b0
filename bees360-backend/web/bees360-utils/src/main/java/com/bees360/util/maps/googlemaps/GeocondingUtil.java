package com.bees360.util.maps.googlemaps;

import com.bees360.base.exception.NotImplementedException;
import com.bees360.util.maps.model.LatLngPoint;
import com.bees360.util.maps.model.LatLngResult;
import com.bees360.util.maps.model.MapAddress;
import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.GeocodingApiRequest;
import com.google.maps.errors.ApiException;
import com.google.maps.model.AddressComponent;
import com.google.maps.model.AddressComponentType;
import com.google.maps.model.GeocodingResult;
import com.google.maps.model.LatLng;
import com.google.maps.model.LocationType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * <AUTHOR> <PERSON>
 * @date 2020/01/19 16:01
 */
@Slf4j
public class GeocondingUtil {

    private GeoApiContext context;

    GeocondingUtil(GeoApiContext context) {
        this.context = context;
    }

    public Optional<LatLngResult> address(String streetAddress, String city, String state, String zipCode, String country) {
        MapAddress mapAddress = new MapAddress();
        mapAddress.setCountry(country);
        mapAddress.setState(state);
        mapAddress.setCity(city);
        mapAddress.setZipCode(zipCode);
        mapAddress.setStreetAddress(streetAddress);
        String fullAddress = mapAddress.toFullAddress();

        GeocodingResult[] results;
        try {
            results = GeocodingApi.newRequest(context).address(fullAddress).await();
        } catch(ApiException e) {
            // 可能是配置错误导致无法正常访问
            // 也可能是账号没有和billing绑定在一起导致了错误
            // 输出 error 级别，需要人为干涉，防止一直请求失败
            log.error("unable to locate the address `{}`: {}", fullAddress, e.getMessage(), e);
            results = null;
        } catch (InterruptedException | IOException e) {
            // 可能是网络不好导致的问题
            log.warn("unable to locate the address `{}`: {}", fullAddress, e.getMessage(), e);
            return Optional.empty();
        }
        if(results == null || results.length == 0) {
            return Optional.empty();
        }
        LatLng latLng = results[0].geometry.location;
        LatLngPoint latLngPoint = new LatLngPoint(latLng.lat, latLng.lng);
        boolean latLngIsApproximate = judgeLatLngIsApproximate(results[0], mapAddress);

        LatLngResult latLngResult = new LatLngResult();
        latLngResult.setLatLng(latLngPoint);
        latLngResult.setLatLngIsApproximate(latLngIsApproximate);
        return Optional.ofNullable(latLngResult);
    }

    private boolean judgeLatLngIsApproximate(GeocodingResult result, MapAddress mapAddress) {
        LocationType locationType = result.geometry.locationType;
        if (locationType != LocationType.ROOFTOP && locationType != LocationType.GEOMETRIC_CENTER) {
            return true;
        }
        if (StringUtils.isBlank(mapAddress.getZipCode())) {
            // 没有提供zipCode
            return false;
        }
        for (AddressComponent component: result.addressComponents) {
            // 判断 zipCode 是否相等
            if (Arrays.stream(component.types).anyMatch(t -> t == AddressComponentType.POSTAL_CODE)) {
                return !Objects.equals(component.shortName, mapAddress.getZipCode());
            }
        }
        return true;
    }

    public MapAddress latlng(double lat, double lng) throws Exception {
        // GeocodingResult[] results = GeocodingApi.newRequest(context).latlng(new LatLng(lat, lng)).await();
        // AddressComponent[] addressComponents = results[0].addressComponents;
        // for(AddressComponent component: addressComponents) {
        //     System.out.println(component);
        // }
        throw new NotImplementedException();
    }
}
