### 使用  @DistributedScheduled

### 支持 @Schedule 的属性 fixedRate, fixedDelay, cron

### 使用方法
    注解在方法上
    每隔多久跑一次
    eg1: @Scheduled(fixedDelay = 5000)

    cron表达式
    eg2: @Scheduled(cron = "30 59 23 * * ?")

### 主要代码
```
if (scheduled.fixedRate() != -1) {
    //间隔任务   开始时间 -> 开始时间
    // 是否需要scheduled.fixedDelay() - 10
    redisTemplate.expire(redisKey, scheduled.fixedRate() - tt - 50, TimeUnit.MILLISECONDS);
    result = joinPoint.proceed();
} else if (scheduled.fixedDelay() != -1) {
    //间隔任务   结束时间 -> 开始时间
    result = joinPoint.proceed();
    redisTemplate.expire(redisKey, scheduled.fixedDelay() - 100, TimeUnit.MILLISECONDS);
} else if (!Objects.equals(scheduled.cron(), "")) {
    //定时任务  超时设置10分钟
    redisTemplate.expire(redisKey, 10 * 60, TimeUnit.SECONDS);
    result = joinPoint.proceed();
} else {
    log.error("Enable distributed scheduled on {}, fixedRate: {}, fixedDelay: {}, cron: {}", key,
            scheduled.fixedRate(), scheduled.fixedDelay(), scheduled.cron());
    redisTemplate.delete(redisKey);
    result = joinPoint.proceed();
}
```
