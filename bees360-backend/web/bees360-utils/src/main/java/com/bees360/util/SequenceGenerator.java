package com.bees360.util;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.bees360.util.SequenceGenerator.RandomSequenceEnum.COMMON_BATCH_NO;

/**
 * @author: linzy
 */
@Component
public class SequenceGenerator {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    public static final String CODE_SPILT = "-";

    public static final char[] RANDOM_CODE = "12ABCDEFGHI34JKLMN56OPQRS78TUVWXYZ9".toCharArray();
    public static final String DEFAULT_VALUE = "NONE";
    public static final SecureRandom secureRandom = new SecureRandom();
    public static final DateTimeFormatter DEFAULT_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyMM");
    public enum RandomSequenceEnum {
        IBEES_INSPECTION_CODE("ibees:inspection-code:", "", 6, 10 * 365L, TimeUnit.DAYS),
        COMMON_BATCH_NO("bees:pilot.batchNo.","B", 4, null, null);


        RandomSequenceEnum(String redisKeyPrefix, String sequencePrefix, int length, Long expirationTime, TimeUnit timeUnit) {
            this.redisKeyPrefix = redisKeyPrefix;
            this.sequencePrefix = sequencePrefix;
            this.length = length;
            this.expirationTime = expirationTime;
            this.expirationTimeUnit = timeUnit;
        }
        private final String redisKeyPrefix;
        private final String sequencePrefix;
        private final int length;
        private final Long expirationTime;
        private final TimeUnit expirationTimeUnit;

        public String getRedisKeyPrefix() {
            return redisKeyPrefix;
        }

        public String getSequencePrefix() {
            return sequencePrefix;
        }

        public int getLength() {
            return length;
        }

        public Long getExpirationTime() {
            return expirationTime;
        }

        public TimeUnit getExpirationTimeUnit() {
            return expirationTimeUnit;
        }
    }

    public String generateSequence(RandomSequenceEnum sequenceEnum) {
        return generateAndSaveSequence(sequenceEnum.redisKeyPrefix, sequenceEnum.sequencePrefix,
            sequenceEnum.expirationTime, sequenceEnum.expirationTimeUnit, sequenceEnum.length);
    }

    public String generateBeesBatchSequence() {
        // 新生成规则 sequencePrefix + dataStr + randomStr(length) eg: B20115H6D
        String dateStr = LocalDate.now().format(DEFAULT_DATE_FORMATTER);
        return generateAndSaveSequence(COMMON_BATCH_NO.redisKeyPrefix,
            COMMON_BATCH_NO.sequencePrefix + dateStr, COMMON_BATCH_NO.expirationTime, COMMON_BATCH_NO.expirationTimeUnit, COMMON_BATCH_NO.length);
    }


    private String generateAndSaveSequence(String redisPrefix, String sequencePrefix, Long expirationTime, TimeUnit timeUnit, int sequenceLength) {
        String sequence = sequencePrefix + generateSequence(sequenceLength);
        //相同碰撞概率还是存在，加上去重逻辑
        while (!saveSequence(redisPrefix, sequence, expirationTime, timeUnit)) {
            sequence = sequencePrefix + generateSequence(sequenceLength);
        }
        return sequence;
    }

    private boolean saveSequence(String prefix, String redisKey, Long expirationTime, TimeUnit timeUnit) {
        ValueOperations<String,String> valueOperations = redisTemplate.opsForValue();
        if (expirationTime == null || timeUnit == null) {
            return Optional.ofNullable(valueOperations.setIfAbsent(prefix + redisKey, DEFAULT_VALUE)).orElse(Boolean.FALSE);
        }
        return Optional.ofNullable(valueOperations.setIfAbsent(prefix + redisKey, DEFAULT_VALUE, expirationTime, timeUnit)).orElse(Boolean.FALSE);
    }

    private static String generateSequence(int length) {
        StringBuilder randomCode = new StringBuilder();
        // 用字符数组的方式随机
        char[] numArray = RANDOM_CODE;
        char lucky;
        for (int i = 0; i < length; i++) {
            lucky = numArray[secureRandom.nextInt(numArray.length)];
            randomCode.append(lucky);
        }
        return randomCode.toString();
    }

}
