package com.bees360.util.secret;

import java.io.ByteArrayOutputStream;
import java.security.AlgorithmParameters;
import java.security.SecureRandom;
import java.security.spec.KeySpec;
import java.util.Arrays;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import jakarta.xml.bind.DatatypeConverter;

public class AESCipher {

	public static final String ALG_AES = "AES";
	public static final String AES_AAP = "AES/CBC/PKCS5Padding";
	public static final String SHA1 = "PBKDF2WithHmacSHA1";

	public static String encrypt(String strToEncrypt, String myKey) {
		if (strToEncrypt == null || myKey == null) {
			return null;
		}
		try {
	        SecureRandom random = new SecureRandom();
	        byte[] salt = new byte[16];
	        random.nextBytes(salt);

	        SecretKeyFactory factory = SecretKeyFactory.getInstance(SHA1);
	        KeySpec spec = new PBEKeySpec(myKey.toCharArray(), salt, 65536, 256);
	        SecretKey tmp = factory.generateSecret(spec);
	        SecretKey secret = new SecretKeySpec(tmp.getEncoded(), ALG_AES);

	        Cipher cipher = Cipher.getInstance(AES_AAP);
	        cipher.init(Cipher.ENCRYPT_MODE, secret);
	        AlgorithmParameters params = cipher.getParameters();
	        byte[] iv = params.getParameterSpec(IvParameterSpec.class).getIV();
	        byte[] encryptedText = cipher.doFinal(strToEncrypt.getBytes("UTF-8"));

	        // concatenate salt + iv + ciphertext
	        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
	        outputStream.write(salt);
	        outputStream.write(iv);
	        outputStream.write(encryptedText);

	        // properly encode the complete ciphertext
	        return DatatypeConverter.printBase64Binary(outputStream.toByteArray());
	    } catch (Exception e) {
	        e.printStackTrace();
	        throw new RuntimeException("AES encrypt failed");
	    }
	}

	public static String descrypt(String strToDecrypt, String myKey) {
		if (strToDecrypt == null) {
			return null;
		}
		try {
	        byte[] ciphertext = DatatypeConverter.parseBase64Binary(strToDecrypt);
	        if (ciphertext.length < 48) {
	            return null;
	        }
	        byte[] salt = Arrays.copyOfRange(ciphertext, 0, 16);
	        byte[] iv = Arrays.copyOfRange(ciphertext, 16, 32);
	        byte[] ct = Arrays.copyOfRange(ciphertext, 32, ciphertext.length);

	        SecretKeyFactory factory = SecretKeyFactory.getInstance(SHA1);
	        KeySpec spec = new PBEKeySpec(myKey.toCharArray(), salt, 65536, 256);
	        SecretKey tmp = factory.generateSecret(spec);
	        SecretKey secret = new SecretKeySpec(tmp.getEncoded(), ALG_AES);
	        Cipher cipher = Cipher.getInstance(AES_AAP);

	        cipher.init(Cipher.DECRYPT_MODE, secret, new IvParameterSpec(iv));
	        byte[] plaintext = cipher.doFinal(ct);

	        return new String(plaintext, "UTF-8");
	    } catch (Exception e) {
	        e.printStackTrace();
	        throw new RuntimeException("AES descrypt failed");
	    }
	}

	public static void main(String[] args) {
		StringBuilder builder = new StringBuilder();
		builder.append("Usage: encrypt | descrypt(String text, String key) \n");
		builder.append("	encrypt | descrypt:			\n");
		builder.append("			encrypt or descrypt method name \n");
		builder.append("	text:	\n");
		builder.append("			text need to encrypt or descrypt \n");
		builder.append("	key:	\n");
		builder.append("			your secret key \n");


		if(args == null || args.length == 0) {
			System.out.println(builder.toString());
			System.exit(1);
		}
		String method = args[0];
		String text = args[1];
		String key = args[2];
		if(method == null || key == null || text == null) {
			System.out.println(builder.toString());
			System.exit(1);
		}
		if("encrypt".contentEquals(method)) {
			System.out.println("Encrypt Text:" + encrypt(text, key));
		}
		else if("descrypt".contentEquals(method)) {
			System.out.println("Decrypt Text:" + descrypt(text, key));
		}else {
			System.out.println(builder.toString());
		}
		System.exit(1);
	}
}
