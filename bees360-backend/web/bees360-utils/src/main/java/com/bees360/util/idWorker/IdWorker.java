package com.bees360.util.idWorker;

import java.util.Random;

/**
 * from https://github.com/twitter/snowflake
 * you must make sure the instance in the system has same workerId and datacenterId to avoid there two same lastTimestamp
 *
 * id is composed of:
 *  time - 41 bits (millisecond precision w/ a custom epoch gives us 69 years)
 *  configured machine id - 10 bits - gives us up to 1024 machines
 *  sequence number - 12 bits - rolls over every 4096 per machine (with protection to avoid rollover in the same ms)
 *
 *  every id is positive number.
 *  0|-time-41-|-datacenterId-5-|-workerId-5-|-sequence-12-|
 *
 * <AUTHOR>
 * @date 2017/12/22 18:39:23
 */
public class IdWorker {
	 private final long workerId;
     private final long datacenterId;
     private long sequence = 0L;

     // start time: 2017-12-22 18:39:23
     // never change this value for the id worker, it is the foundation of the id worker.
     private static final long twepoch = 1513939163000L;

     private static final long workerIdBits = 5L;
     private static final long datacenterIdBits = 5L;
     private static final long maxWorkerId = -1L ^ (-1L << (int)workerIdBits);
     private static final long maxDatacenterId = -1L ^ (-1L << (int)datacenterIdBits);
     private static final long sequenceBits = 12L;

     private static final long workerIdShift = sequenceBits;
     private static final long datacenterIdShift = sequenceBits + workerIdBits;
     private static final long timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;
     private static final long sequenceMask = -1L ^ (-1L << (int)sequenceBits);

     // record the last time using IdWorker to create id.
     private long lastTimestamp = -1L;

     IdWorker(long workerId, long datacenterId){

         // sanity check for workerId
         if (workerId > maxWorkerId || workerId < 0) {
        	 String msg = "worker Id can't be greater than %d or less than 0".formatted(maxWorkerId);
             throw new IllegalArgumentException(msg);
         }
         if (datacenterId > maxDatacenterId || datacenterId < 0) {
        	 String msg = "datacenter Id can't be greater than %d or less than 0".formatted(maxDatacenterId);
             throw new IllegalArgumentException(msg);
         }
         this.workerId = workerId;
         this.datacenterId = datacenterId;
     }

     public synchronized long nextId() {
    	 long timestamp = timeGen();
         // clock shouldn't move backward
         if (timestamp < lastTimestamp) {
        	 long timeSpan = lastTimestamp - timestamp;
        	 String msg = "Clock moved backwards.  Refusing to generate id for %d milliseconds".formatted(timeSpan);
             throw new RuntimeException(msg);
         }

         if (lastTimestamp == timestamp) {
        	 // run at the same time, sequence will increment
             sequence = (sequence + 1) & sequenceMask;
             if (sequence == 0) {
            	 // if all sequences were run out of, update time stamp to a new one
                 timestamp = tilNextMillis(lastTimestamp);
             }
         } else {
        	// last time stamp isn't now, reset sequence
             sequence = 0L;
         }
         // update last time stamp
         lastTimestamp = timestamp;
         return ((timestamp - twepoch) << (int)timestampLeftShift) |
        		 (datacenterId << (int)datacenterIdShift) |
        		 (workerId << (int)workerIdShift) | sequence;
     }

     private long tilNextMillis(long lastTimestamp) {
         long timestamp = timeGen();
         while (timestamp <= lastTimestamp) {
             timestamp = timeGen();
         }
         return timestamp;
     }

     private long timeGen(){
    	 return System.currentTimeMillis();
     }

     public static void main(String[] args) {
    	 IdWorker idWorker = new IdWorker(1, 1);
    	for(int i = 0; i < 100; i ++){
    		System.out.println(idWorker.nextId());
    	}
     }
}
