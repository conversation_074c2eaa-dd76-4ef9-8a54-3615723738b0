package com.bees360.util.report;

import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * file util
 *
 * <AUTHOR>
 *
 */
@Slf4j
public class FileUtil {

	/**
	 * Determine whether the folder exists, and the non existence is created
	 *
	 * @param file
	 * @return
	 */
	public static boolean judeDirExists(File file) {
		if (file.exists()) {
            return file.isDirectory();
		} else {
			file.mkdirs();
		}
		return true;
	}

	/**
	 * Delete all files under the specified folder
	 *
	 * @param path
	 * @return
	 */
	private static boolean delAllFile(String path) {
		boolean flag = false;
		File file = new File(path);
		if (!file.exists()) {
			return flag;
		}
		if (!file.isDirectory()) {
			return flag;
		}
		String[] tempList = file.list();
		File temp = null;
		for (int i = 0; i < tempList.length; i++) {
			if (path.endsWith(File.separator)) {
				temp = new File(path + tempList[i]);
			} else {
				temp = new File(path + File.separator + tempList[i]);
			}
			if (temp.isFile()) {
				temp.delete();
			}
			if (temp.isDirectory()) {
				// Delete the files in the folder first
				delAllFile(path + File.separator + tempList[i]);
				// Deleting empty folders
				delFolder(path + File.separator + tempList[i]);
				flag = true;
			}
		}
		return flag;
	}

	/**
	 * Delete all files under folders and folders
	 *
	 * @param folderPath
	 */
	public static void delFolder(String folderPath) {
		try {
			// Delete all the contents
			delAllFile(folderPath);
			String filePath = folderPath;
			filePath = filePath.toString();
			File myFilePath = new File(filePath);
			// Delete empty folders
			if (myFilePath.exists()) {
				myFilePath.delete();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
