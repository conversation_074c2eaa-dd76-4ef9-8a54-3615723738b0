package com.bees360.util.file;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.SocketException;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;

public class FtpDownloader {

	public boolean downloadArinIpFile() throws Exception{
		// source: ftp://ftp.arin.net/pub/stats/arin/delegated-arin-extended-latest
		// destination: /home/<USER>/project/data/bees360/data/delegated-arin-extended-latest.txt
		// Anonymous to connection remote FTP
		String ftpHost = "ftp.arin.net";
		String ftpUserName = "anonymous";
		String ftpPassword = "<EMAIL>";
		String ftpPath = "/pub/stats/arin/delegated-arin-extended-latest";

		String savePath = "/var/bees360/materials/delegated-arin-extended-latest";
		return FtpDownloader.downloadFtpFile(ftpHost, ftpUserName, ftpPassword, ftpPath, savePath);
	}

	public static boolean downloadFtpFile(String ftpHost, String ftpUserName, String ftpPassword, String ftpPath,
			String destFilePath) throws Exception{
		int lastSeperator = ftpPath.lastIndexOf("/");
		String ftpWorkingDirectory = ftpPath.substring(0,lastSeperator);
		String ftpDestFileName = ftpPath.substring(lastSeperator + 1);
		FTPClient ftpClient = null;
		OutputStream output = null;
		try {
			ftpClient = getFTPClient(ftpHost, ftpUserName, ftpPassword);
			ftpClient.setControlEncoding("UTF-8");
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
			ftpClient.enterLocalPassiveMode();
			ftpClient.changeWorkingDirectory(ftpWorkingDirectory);

			File localFile = new File(destFilePath);
			if(!localFile.exists()) {
				File localDir = localFile.getParentFile();
				if(!localDir.exists()) {
					localDir.mkdirs();
				}
			}
			output = new FileOutputStream(localFile);
			ftpClient.retrieveFile(ftpDestFileName, output);
			ftpClient.logout();
		} finally {
			if(output != null) {
				try {
					output.close();
				} catch (IOException e) {
//					e.printStackTrace();
				}
			}
		}
		return true;
	}

	private static FTPClient getFTPClient(String ftpHost, String ftpUserName, String ftpPassword)
			throws SocketException, IOException, Exception {
		FTPClient ftpClient = new FTPClient();
		ftpClient = new FTPClient();
		ftpClient.connect(ftpHost);
		ftpClient.login(ftpUserName, ftpPassword);
		if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {
			ftpClient.disconnect();
			throw new Exception("Can not connect to FTP: " + ftpHost);
		}
		return ftpClient;
	}
}
