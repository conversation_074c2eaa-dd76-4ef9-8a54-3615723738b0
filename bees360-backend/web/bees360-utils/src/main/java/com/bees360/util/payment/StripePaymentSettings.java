package com.bees360.util.payment;

import lombok.Data;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 */
@Data
public class StripePaymentSettings {

	private String secretApiKey;

	private String publishableKey;

	private String currency;

	private int minimumAmount;

	private int maximumAmount;

	public void setSecretApiKey(String secretApiKey) {
		Assert.hasLength(secretApiKey, "StripePaymentSettings.secretAPIKey  must not be null or empty");
		Assert.isTrue(secretApiKey.startsWith("sk_"), "Stripe secret key must start with \"sk_\"");

		this.secretApiKey = secretApiKey;
	}

	public void setPublishableKey(String publishableKey) {
		Assert.hasLength(publishableKey, "StripePaymentSettings.publishableKey  must not be null or empty");
		Assert.isTrue(publishableKey.startsWith("pk_"), "Stripe publishable key must start with \"pk_\"");

		this.publishableKey = publishableKey;
	}
}
