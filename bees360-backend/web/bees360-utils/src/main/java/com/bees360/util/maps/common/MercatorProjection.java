package com.bees360.util.maps.common;

import com.bees360.util.maps.model.LatLngPoint;
import com.bees360.util.maps.model.Pixel;

public class MercatorProjection {

	private int tileSize;

	public MercatorProjection(int tileSize) {
		this.tileSize = tileSize;
	}

	// The mapping between latitude, longitude and pixels is defined by the web
    // mercator projection.
	public Pixel fromLatLngToPoint(LatLngPoint latLng, int zoom) {
		int scale = 1 << zoom;

		double x = lng2Pixelx(latLng.getLng(), scale);
		double y = lat2Pixely(latLng.getLat(), scale);

		return new Pixel(x, y);
	}

	public LatLngPoint fromPointToLatLng(Pixel pixel, int zoom) {
		int scale = 1 << zoom;

		double lng = pixelx2Lng(pixel.getX(), scale);
		double lat = pixely2Lat(pixel.getY(), scale);
		return new LatLngPoint(lat, lng);
	}

	public double pixelx2Lng(double x, int scale) {
		x = x / tileSize;
	    return x / scale * 360.0 - 180;
	}

	public double pixely2Lat(double y, int scale) {
		y = y / tileSize;
		double n = Math.PI - (2.0 * Math.PI * y) / scale;
		return Math.toDegrees(Math.atan(Math.sinh(n)));
	}

	public double lng2Pixelx(double lng, int scale) {
		return (0.5 + lng / 360) * scale * tileSize;
	}

	public double lat2Pixely(double lat, int scale) {
		double siny = Math.sin(lat * Math.PI / 180);
		siny = Math.min(Math.max(siny, -0.9999), 0.9999);

		return tileSize * (0.5 - Math.log((1 + siny) / (1 - siny)) / (4 * Math.PI)) * scale;
	}

	public static void main(String[] args) {
		LatLngPoint gps = new LatLngPoint(41.85, -87.64999999999998);
		Pixel p = new Pixel(525, 761);
		int zoom = 3;

		MercatorProjection projection = new MercatorProjection(256);
		Pixel resultP = projection.fromLatLngToPoint(gps, zoom);
		LatLngPoint resultGps = projection.fromPointToLatLng(p, zoom);

		System.out.println(gps + " -> " + resultP + " ~> " + projection.fromPointToLatLng(resultP, zoom));
		System.out.println(p + " -> " + resultGps + " ~> " + projection.fromLatLngToPoint(resultGps, zoom));
	}
}
