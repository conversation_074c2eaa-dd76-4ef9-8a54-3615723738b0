package com.bees360.util.websocket;

import java.util.HashMap;
import java.util.Map;

import org.json.JSONObject;
import org.springframework.web.socket.TextMessage;

import com.bees360.entity.Notification.NotificationType;
import com.bees360.entity.dto.WebSocketMsgData;


public class SocketMsg{
	private final TextMessage textMessage;
	private final String payload;
	private final boolean last;

	private final static String TYPE_FIELD = "type";
	private final static String DATA_FIELD = "data";

	public SocketMsg(String data) {
		this(NotificationType.CONNECTION_TYPE, data);
	}

	public SocketMsg(NotificationType type, Object data) {
		this(type, data, true);
	}

	public SocketMsg(NotificationType type, Object data, boolean isLast) {
		Map<String, Object> msg = new HashMap<String, Object>();
		msg.put(TYPE_FIELD, type.getCode());
		msg.put(DATA_FIELD, data);
		this.payload = JSONObject.valueToString(data);
		this.last = isLast;
		this.textMessage = new TextMessage(payload,last);
	}

	public SocketMsg(WebSocketMsgData msg) {
		this(msg.getType(), msg.toWebSocketMsgFieldMap());
	}

	public TextMessage textMessage() {
		return this.textMessage;
	}
}
