package com.bees360.util.maps.model;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/01/19 16:55
 */
@Data
@NoArgsConstructor
public class MapAddress {
    private String country;
    private String state;
    private String city;
    private String zipCode;
    private String streetAddress;

    public String toFullAddress() {
        return nullToBlank(streetAddress) + ", " + nullToBlank(city) + ", " + nullToBlank(state) + " "
            + nullToBlank(zipCode) + ", " + nullToBlank(country);
    }

    private String nullToBlank(String value) {
        return value == null ? "" : value;
    }
}
