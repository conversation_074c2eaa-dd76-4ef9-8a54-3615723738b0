package com.bees360.util.token;

import java.util.List;

import com.bees360.entity.dto.IdNameDto;

/**
 *
 */
public class JwtUser {

    private long userId;
    private String name;
    private String phone;
    private String email;
    private String avatar;
    private List<IdNameDto> roles;

    public JwtUser() {}

    /* getter and setter */
	public long getUserId() {
		return userId;
	}
	public void setUserId(long userId) {
		this.userId = userId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getAvatar() {
		return avatar;
	}
	public void setAvatar(String avatar) {
		this.avatar = avatar;
	}
	public List<IdNameDto> getRoles() {
		return roles;
	}
	public void setRoles(List<IdNameDto> roles) {
		this.roles = roles;
	}

	@Override
	public String toString() {
		return "JwtUser [userId=" + userId + ", name=" + name + ", phone=" + phone + ", email=" + email + ", avatar="
				+ avatar + ", roles=" + roles + "]";
	}
}
