package com.bees360.util.idWorker;

/**
 *
 * <AUTHOR>
 * @contributors
 * @date 2017/12/22 10:05:14
 */
public class IdWorkerInstancer {
	// <EMAIL> need to be optimized
	// workerId: 0 ~ 2^6-1
	private static long workerId = 1;
	// datacenterId: 0 ~ 2^6-1
	private static long datacenterId = 1;
	// volatile 保存多线程环境下线程安全
	private static volatile IdWorker idWorker = null;

	public static IdWorker getIdWorkerInstance(){
		if(idWorker == null){
			synchronized(IdWorkerInstancer.class){
				if(idWorker == null){
					idWorker = new IdWorker(workerId, datacenterId);
				}
			}
		}
		return idWorker;
	}

	public static IdWorker getNewIdWorker(){
		return new IdWorker(workerId, datacenterId);
	}
}
