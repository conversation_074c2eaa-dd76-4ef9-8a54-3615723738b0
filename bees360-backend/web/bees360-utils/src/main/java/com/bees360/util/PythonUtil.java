package com.bees360.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

public class PythonUtil {

	private static Logger logger = LoggerFactory.getLogger(PythonUtil.class);

	private static ExecutorService se = Executors.newCachedThreadPool();

	public static boolean exec(String pyFilePath, List<String> params, boolean block, long timeout)
			throws IOException, InterruptedException {

		StringBuilder pythonCommand = new StringBuilder("python " + pyFilePath);
		if(params != null && params.size() > 0) {
			pythonCommand.append(" " + jointParams(params));
		}
		// Let the terminal doesn't close after the python file finishing running.
//		pythonCommand.append(";");
//		pythonCommand.append("exec bash;");

		logger.info("start execute: " + pythonCommand.toString());
		Process process = Runtime.getRuntime().exec(pythonCommand.toString());

		cleanStream(process.getInputStream(), true);
		cleanStream(process.getErrorStream(), true);

		if(block) {
			return process.waitFor(timeout, TimeUnit.MILLISECONDS);
		}
		logger.info("finish execute: " + pythonCommand.toString());
		return true;
	}

	private static String jointParams(List<String> params) {
		if(params == null || params.size() == 0) {
			return "";
		}
		StringBuilder sb = new StringBuilder();
		for(String param: params) {
			sb.append(" " + param);
		}
		return sb.toString();
	}

	private static void cleanStream(final InputStream in, boolean show) {
		se.execute(new Runnable() {
			public void run() {
				Reader reader = new InputStreamReader(in);
				BufferedReader br = new BufferedReader(reader);
				String line = null;
				try {
					while((line = br.readLine()) != null) {
						if(show) {
							logger.debug(line);
						}
					}
				} catch (IOException e) {
					logger.error("Fail to print message from python process", e);
				}
			}
		});
	}

	public static void main(String[] args) throws IOException, InterruptedException {
		List<String> params = Arrays.asList(
				"--address=@32.7385259,-97.4100865",
				"--image_path=/var/bees360/www/maps/googlemaps/houseboudary/32.7385259,-97.4100865.png",
				"--save_json=/var/bees360/www/maps/googlemaps/houseboudary/32.7385259,-97.4100865.png.json");
		boolean result = PythonUtil.exec("/var/bees360/www/process/BR.py", params, true, 3 * 60 * 1000);

		System.out.println(result);
	}
}
