package com.bees360.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.gson.Gson;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;

import com.bees360.entity.SystemConfig;
import com.bees360.entity.util.SystemConfigPrefix;
import com.bees360.util.exception.SystemConfigFailConvertException;

/**
 * <AUTHOR>
 * @date 2019/12/23 17:38
 */
public class SystemConfigConvertorUtil {

    private static final Gson GSON = new Gson();

    public final String KEY_SEPARATOR = ".";

    public static <T extends SystemConfigPrefix> SystemConfig toJsonType(T systemConfigObject) {
        String json = GSON.toJson(systemConfigObject);
        return new SystemConfig(systemConfigObject.prefix(), json);
    }

    public static <T extends SystemConfigPrefix> T fromJsonType(SystemConfig systemConfig, Class<T> systemConfigClass) {
        return GSON.fromJson(systemConfig.getConfigValue(), systemConfigClass);
    }

    public static <T extends SystemConfigPrefix> List<SystemConfig> toSystemConfigs(T systemConfigObject)
        throws SystemConfigFailConvertException {
        List<SystemConfig> systemConfigs = new ArrayList<>();
        if (systemConfigObject == null) {
            return systemConfigs;
        }
        String prefix = systemConfigObject.prefix();
        Map<String, String> propertiesMap = beanToMap(systemConfigObject);
        propertiesMap = addKeyPrefix(prefix, propertiesMap);
        for (Map.Entry<String, String> entry : propertiesMap.entrySet()) {
            systemConfigs.add(new SystemConfig(entry.getKey(), entry.getValue()));
        }
        return systemConfigs;
    }

    public static <T extends SystemConfigPrefix> T toSystemConfigDto(List<SystemConfig> systemConfigs,
        Class<T> systemConfigClass) throws SystemConfigFailConvertException {
        try {
            T systemConfigObject = systemConfigClass.newInstance();
            toSystemConfigDto(systemConfigs, systemConfigObject);
            return systemConfigObject;
        } catch (InstantiationException | IllegalAccessException e) {
            throw new SystemConfigFailConvertException(
                "The implement of SystemConfigPrefix " + systemConfigClass.getName() + " should provide a public no arg Constructor.", e);
        }
    }

    public static <T extends SystemConfigPrefix> void toSystemConfigDto(List<SystemConfig> systemConfigs,
        T systemConfigObject) throws SystemConfigFailConvertException {
        Map<String, String> properties = new HashMap<>();
        if (systemConfigObject == null) {
            return;
        }
        for (SystemConfig systemConfig : systemConfigs) {
            properties.put(systemConfig.getConfigKey(), systemConfig.getConfigValue());
        }
        properties = removeKeyPrefix(systemConfigObject.prefix(), properties);
        mapToBean(properties, systemConfigObject);
    }

    private static Map<String, String> addKeyPrefix(String prefix, Map<String, String> properties) {
        prefix = normalPrefix(prefix);

        Map<String, String> newMap = new HashMap<>(properties.size());
        for (Map.Entry<String, String> entry : properties.entrySet()) {
            newMap.put(prefix + entry.getKey(), entry.getValue());
        }
        return newMap;
    }

    private static Map<String, String> removeKeyPrefix(String prefix, Map<String, String> properties) {
        prefix = normalPrefix(prefix);
        Map<String, String> newMap = new HashMap<>(properties.size());
        for (Map.Entry<String, String> entry : properties.entrySet()) {
            String key = entry.getKey();
            if (key.startsWith(prefix)) {
                key = key.replaceFirst(prefix, "");
            }
            newMap.put(key, entry.getValue());
        }
        return newMap;
    }

    private static String normalPrefix(String prefix) {
        if (StringUtils.isEmpty(prefix)) {
            return "";
        } else {
            return prefix + ".";
        }
    }

    private static <T extends SystemConfigPrefix> Map<String, String> beanToMap(T systemConfigObject)
        throws SystemConfigFailConvertException {
        Map<String, String> result = new HashMap<>();
        if (systemConfigObject == null) {
            return result;
        }
        try {
            result = BeanUtils.describe(systemConfigObject);
            result.remove("class");
        } catch (Exception e) {
            throw new SystemConfigFailConvertException("Fail to convert " + systemConfigObject.getClass() + " to map.",
                e);
        }
        return result;
    }

    private static <T extends SystemConfigPrefix> void mapToBean(Map<String, String> properties, T bean)
        throws SystemConfigFailConvertException {
        if (properties == null || bean == null) {
            return;
        }
        try {
            BeanUtils.populate(bean, properties);
        } catch (Exception e) {
            throw new SystemConfigFailConvertException("Fail to convert map to " + bean.getClass(), e);
        }
    }
}
