package com.bees360.util.schedule.annotation;

import com.bees360.util.schedule.ScheduledConfig;
import com.bees360.util.RedisUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @date 2020/04/07
 */
@Aspect
@Slf4j
@AllArgsConstructor
@Component
public class ScheduledAspectAdvice {

    private static final String TASK_PRE = "com.bees360.cron.task_";

    @Autowired
    final RedisUtil redisUtil;

    @Autowired
    private ScheduledConfig scheduledConfig;

    @Pointcut("@annotation(org.springframework.scheduling.annotation.Scheduled)")
    private void anyMethod() {
    }//定义一个切入点


    @Around("anyMethod()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        long st = System.currentTimeMillis();
        Class<?> type = joinPoint.getSignature().getDeclaringType();
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();

        boolean distributedEnable = false;
        boolean executeEnable = false;
        DistributedScheduled distributedScheduled = type.getAnnotation(DistributedScheduled.class);
        if (distributedScheduled != null) {
            distributedEnable = distributedScheduled.distributedEnable();
            executeEnable = distributedScheduled.executeEnable();
        }
        distributedScheduled = method.getAnnotation(DistributedScheduled.class);
        if (distributedScheduled != null) {
            distributedEnable = distributedScheduled.distributedEnable();
            executeEnable = distributedScheduled.executeEnable();
        }
        if (scheduledConfig != null) {
            if (!executeEnable && scheduledConfig.getExecuteEnable() != null) {
                // 如果设置为false, 则赋值给executeEnable,移除掉定时器操作
                executeEnable = scheduledConfig.getExecuteEnable();
            }
            if (scheduledConfig.getDistributedEnable() != null && !scheduledConfig.getDistributedEnable()) {
                // 如果设置为false, 则赋值给executeEnable,移除掉定时器操作
                distributedEnable = scheduledConfig.getDistributedEnable();
            }
        }
        if (!executeEnable) {
            return null;
        }
        if (!distributedEnable) {
            return joinPoint.proceed();
        }

        Scheduled scheduled = method.getAnnotation(Scheduled.class);

        String key = type.getName() + "." + method.getName();

        //隔多久执行一次
        if (scheduled.fixedRate() == -1 && scheduled.fixedDelay() == -1 && Objects.equals(scheduled.cron(), "")) {
            log.error("Enable distributed scheduled on {}, but satisfy scheduled.fixedRate() == -1 && " +
                    "scheduled.fixedDelay() == -1 && Objects.equals(scheduled.cron(), \"\")", key);
            return joinPoint.proceed();
        }

        String redisKey = TASK_PRE + key;
        //使用increment 存在问题, 第二次运行increment会取消第一次执行后设置的expireTime
        boolean success = redisUtil.setIfAbsent(redisKey, System.currentTimeMillis() + "");
        if (!success) {
            Long expire = redisUtil.getExpire(redisKey);
            if (expire == null || expire < 0) {
                redisUtil.expire(redisKey, 5, TimeUnit.SECONDS);
            }
            return null;
        }
        Object result;
        long tt = System.currentTimeMillis() - st;
        //System.out.println(tt);
        if (scheduled.fixedRate() != -1) {
            //间隔任务   开始时间 -> 开始时间
            // 是否需要scheduled.fixedDelay() - 10
            redisUtil.expire(redisKey, scheduled.fixedRate() - tt - 50, TimeUnit.MILLISECONDS);
            result = catchCall(joinPoint);
        } else if (scheduled.fixedDelay() != -1) {
            //间隔任务   结束时间 -> 开始时间
            redisUtil.expire(redisKey, scheduled.fixedDelay() - 100, TimeUnit.MILLISECONDS);
            result = catchCall(joinPoint);
            redisUtil.expire(redisKey, scheduled.fixedDelay() - 100, TimeUnit.MILLISECONDS);
        } else if (!Objects.equals(scheduled.cron(), "")) {
            //定时任务  超时设置10分钟
            redisUtil.expire(redisKey, 10 * 60, TimeUnit.SECONDS);
            result = catchCall(joinPoint);
        } else {
            log.error("Enable distributed scheduled on {}, fixedRate: {}, fixedDelay: {}, cron: {}", key,
                    scheduled.fixedRate(), scheduled.fixedDelay(), scheduled.cron());
            redisUtil.delete(redisKey);
            result = catchCall(joinPoint);
        }
        return result;
    }


    Object catchCall(ProceedingJoinPoint joinPoint) {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        try {
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            log.error("scheduledAspectAdvice proceed method:{}, error," + method.getName(), throwable.getMessage(), throwable);
            return null;
        }
    }
}
