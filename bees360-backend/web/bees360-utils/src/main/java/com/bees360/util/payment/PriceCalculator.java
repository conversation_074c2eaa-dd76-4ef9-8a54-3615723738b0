package com.bees360.util.payment;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.bees360.util.DoubleArithmetic2;
import com.bees360.util.MapList;

public class PriceCalculator {

	private static int DEFAULT_PRICE_SCALE = 2;
	private static int DEFAULT_TAX_SCALE = 2;

	/**
	 * 计算税的值，taxAmount = price * taxRate / 100
	 * @param price 价格，当价格为 $75时，值为75
	 * @param taxRate 当税率为8.25%时，值为8.25
	 * @return
	 */
	public static double calTax(double price, double taxRate) {
		return new DoubleArithmetic2(price).mul(taxRate).div(100).roundHalfEven(DEFAULT_PRICE_SCALE);
	}

	/**
	 * 计算税的值，taxAmount = (price - discount) * taxRate / 100
	 * @param price 价格，当价格为 $75时，值为75
	 * @param discount 折扣的金额，当金额为 $25时，值为25
	 * @param taxRate 当税率为8.25%时，值为8.25
	 * @return
	 */
	public static double calTax(double price, double discount, double taxRate) {
		return new DoubleArithmetic2(price).sub(discount).mul(taxRate).div(100).roundHalfEven(DEFAULT_PRICE_SCALE);
	}

	/**
	 * 计算折扣总金额，discountAmount = price * discount
	 * @param price 价格
	 * @param discountRate 实际的折扣比例，如 25%，则值为 0.25
	 * @return
	 */
	public static double calDiscount(double price, double discountRate) {
		return new DoubleArithmetic2(price).mul(discountRate).roundHalfEven(DEFAULT_PRICE_SCALE);
	}

	/**
	 * 求和，amount = items[0] + items[1] + ... + items[items.size() - 1]
	 * @param items 值的列表
	 * @return
	 */
	public static double calSum(List<Double> items) {
		if(items.isEmpty()) {
			return 0D;
		}
		DoubleArithmetic2 dArithmetic2 = new DoubleArithmetic2(items.get(0));
		for(int i = 1; i < items.size(); i ++) {
			dArithmetic2.add(items.get(i));
		}
		return dArithmetic2.roundHalfEven(DEFAULT_PRICE_SCALE);
	}

	/**
	 * 求和，amount = items[0] + items[1] + ... + items[items.size() - 1]
	 * @param items 值的列表
	 * @return
	 */
	public static double calSum(double... items) {
		if(items.length == 0) {
			return 0;
		}
		DoubleArithmetic2 dArithmetic2 = new DoubleArithmetic2(items[0]);
		for(int i = 1; i < items.length; i ++) {
			dArithmetic2.add(items[i]);
		}
		return dArithmetic2.roundHalfEven(DEFAULT_PRICE_SCALE);
	}

	/**
	 * 计算总价格，totalPrice = price - discount + tax
	 * @param price 价格，当价格为 $75时，值为75
	 * @param discount 折扣，当折扣为 $75时，值为75
	 * @param tax 税，当税为 $75时，值为75
	 * @return
	 */
	public static double calTotalPrice(double price, double discount, double tax) {
		DoubleArithmetic2 dArithmetic2 = new DoubleArithmetic2(price);
		return dArithmetic2.sub(discount).add(tax).roundHalfEven(DEFAULT_PRICE_SCALE);
	}

	/**
	 * 小计，subtotal = price - discount
	 * @param price 价格，当价格为 $75时，值为75
	 * @param discountValue 折扣，当折扣为 $75时，值为75
	 */
	public static double calSubtotal(double price, double discountValue) {
		DoubleArithmetic2 dArithmetic2 = new DoubleArithmetic2(price);
		return dArithmetic2.sub(discountValue).roundHalfEven(DEFAULT_PRICE_SCALE);
	}

	/**
	 * 求积，result = price * quantity
	 */
	public static double calMul(double price, int quantity) {
		DoubleArithmetic2 dArithmetic2 = new DoubleArithmetic2(price);
		return dArithmetic2.mul(quantity).roundHalfEven(DEFAULT_PRICE_SCALE);
	}

	public static void main(String[] args) {
		System.out.println(0.29 / 3.5);
		System.out.println(new DoubleArithmetic2(10).div(20).doubleResult());
		System.out.println(new DoubleArithmetic2(20).div(10).doubleResult());
		System.out.println(new DoubleArithmetic2(0.29).div(3.5).result().toPlainString());

		System.out.println("---------------");
		Map<Double, Double> taxMapSubtotal = new HashMap<Double, Double>();
		taxMapSubtotal.put(8.25, 0D);
		taxMapSubtotal.put(6.25, 0D);
	}
}
