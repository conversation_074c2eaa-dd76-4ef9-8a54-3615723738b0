package com.bees360.util.websocket;

import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * Connect web client and web server
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private WebSocketDataManager webSocketDataManager;

    @Autowired
    private TokenParser tokenParser;

	@Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
		registry.addHandler(webSocketHandler(), "/websocket")
			.addInterceptors(new WebSocketInterceptor())
			.setAllowedOrigins("*");
    }

	@Bean
	public WebSocketHandler webSocketHandler() {
		// register WebSocketHandler to spring
		return new WebSocketHandler(webSocketDataManager, tokenParser);
	}

	@Bean
    public TokenParser tokenParser() {
	    return new TokenParser() {
            @Override
            public Claims parseClaims(String token) {
                throw new IllegalStateException("Not supported.");
            }
        };
    }
}
