package com.bees360.util;

import java.io.IOException;
import java.io.Serial;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public class DeleteImageServlet extends HttpServlet {

    @Serial
    private static final long serialVersionUID = 1L;
	private static long twentyFourHours = 86400000L;
	private static long oneHour = 3600000L;
	private static long tenMinutes = 600000L;
	private static long threeMinutes = 180000L;

	public void init() throws ServletException {
		ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
		long initDelay = getTimeMillis("00:00:00") - System.currentTimeMillis();
		initDelay = initDelay > 0 ? initDelay : twentyFourHours + initDelay;
		executor.scheduleAtFixedRate(new DeleteLowImageRunnable(), initDelay, twentyFourHours, TimeUnit.MILLISECONDS);
//		executor.scheduleAtFixedRate(new DeleteLowImageRunnable(), 0, 60000, TimeUnit.MILLISECONDS);
	}

	public void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
	}

	public void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
	}

	public void destory() {

	}

	private static long getTimeMillis(String time) {
	    try {
	        DateFormat dateFormat = new SimpleDateFormat("yy-MM-dd HH:mm:ss");
	        DateFormat dayFormat = new SimpleDateFormat("yy-MM-dd");
	        Date curDate = dateFormat.parse(dayFormat.format(new Date()) + " " + time);
	        return curDate.getTime();
	    } catch (Exception e) {
	        e.printStackTrace();
	    }
	    return 0;
	}
}
