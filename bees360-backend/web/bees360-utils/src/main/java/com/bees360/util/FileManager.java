package com.bees360.util;

import java.io.File;
import java.util.Date;

public class FileManager {

	public static final String ROOT_DIR = "/var/bees360/www/";
	public static final String ROOT_PROJECTS_DIR = ROOT_DIR + "projects/";

	public static final String ROOT_PROJECT_DIR = ROOT_DIR + "projects/${projectId}/";

	private static final String DIR_ANNOTATION_IMAGE = "images/annotation-view/";
	private static final String DIR_THUMBNAIL_IMAGE = "images/thumbnail-view/";
	private static final String DIR_MIDDLE_IMAGE = "images/middle-view/";
	private static final String DIR_ORIGINAL_IMAGE = "images/original-view/";
	// add by shoushan.zhao
	private static final String DIR_COMPANY_IMAGE = "images/company-view/";
	private static final String DIR_REPORT_IMAGE = "reports/report/";

	private static final String DIR_POST_BOUNDARY = "ai-process/post-boundary/";

	public static final String ARIN_IP_FILE = ROOT_DIR + "materials/delegated-arin-extended-latest";
	public static final String ARIN_IP_INT_FILE = ROOT_DIR + "materials/delegated-arin-extended-latest-int";

	public static final String APNIC_IP_FILE = ROOT_DIR + "materials/delegated-apnic-latest.txt";
	public static final String APNIC_IP_INT_FILE = ROOT_DIR + "materials/delegated-apnic-latest-int.txt";

	private static final String REPORT_SHARE_DIR = ROOT_DIR + "report/download/share/${projectId}/";

	public static final String HOUSE_BOUNDARY_GOOGLE_MAPS = ROOT_DIR + "maps/googlemaps/houseboudary";

	public static final String DIR_ROSTER_RESUME = ROOT_DIR + File.separator + "Roster" + File.separator + "Resume";

	public static String getTempDirToSaveOriginalImage(long projectId){
		return createProjectDir(projectId, DIR_ORIGINAL_IMAGE);
	}

	public static String getTempDirToSaveMiddleImage(long projectId){
		return createProjectDir(projectId, DIR_MIDDLE_IMAGE);
	}

	public static String getTempDirToSaveThumbnailImages(long projectId){
		return createProjectDir(projectId, DIR_THUMBNAIL_IMAGE);
	}

	public static String getTempDirToSaveAnnotationImages(long projectId){
		return createProjectDir(projectId, DIR_ANNOTATION_IMAGE);
	}

	public static String getTempPostBoundaryDir(long projectId) {
		return createProjectDir(projectId, DIR_POST_BOUNDARY);
	}

	// add by shoushan.zhao
	public static String getTempDirToSaveCompanyImages(long projectId) {
		return createProjectDir(projectId, DIR_COMPANY_IMAGE);
	}

	public static String getTempDirToSaveReports(long projectId) {
		return createProjectDir(projectId, DIR_REPORT_IMAGE);
	}

	public static String getOriginalTimeFilePath(long projectId){
		return getTempDirToSaveOriginalImage(projectId) + "timestamp/timestamp";
	}

	public static String getMiddleTimeFilePath(long projectId){
		return getTempDirToSaveMiddleImage(projectId) + "timestamp/timestamp";
	}

	public static String getLastDrawAnnotationTimeFilePath(long projectId){
		return getTempDirToSaveThumbnailImages(projectId) + "timestamp/timestamp";
	}

	public static String getCompanyTimeFilePath(long projectId){
		return getTempDirToSaveCompanyImages(projectId) + "timestamp/timestamp";
	}

	public static String getReportTimeFilePath(long projectId){
		return getTempDirToSaveReports(projectId) + "timestamp/timestamp";
	}

	private static String createProjectDir(long projectId, String subDir){
		return ROOT_PROJECT_DIR.replace("${projectId}", projectId + "") + subDir;
	}
	public static String getDirThumbnailImage() {
		return DIR_THUMBNAIL_IMAGE;
	}
	public static String getDirToShareReport(long projectId) {
		return REPORT_SHARE_DIR.replace("${projectId}", projectId + "-" + System.currentTimeMillis());
	}

    public static String getTempDirToSaveInvoice(long projectId) {
		return ROOT_PROJECT_DIR.replace("${projectId}", projectId + "") + "invoices/";
    }
}
