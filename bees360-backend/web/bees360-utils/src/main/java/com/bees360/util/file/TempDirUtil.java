package com.bees360.util.file;

import org.apache.commons.io.FileUtils;

import java.io.File;

/**
 * <AUTHOR>
 */
public class TempDirUtil {

    public static File getTempDir(Object lock, Class<?> clazz) {
        File dir = new File(FileUtils.getTempDirectory(), clazz.getName());
        if (dir.exists()) {
            return dir;
        }
        synchronized (lock) {
            if (dir.exists()) {
                return dir;
            }
            dir.mkdirs();
            return dir;
        }
    }
}
