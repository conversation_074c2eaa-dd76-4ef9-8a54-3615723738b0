package com.bees360.util;

import java.util.Iterator;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class RoundRobin<T> {

	private CopyOnWriteArrayList<T> members;
	private Iterator<T> memberIeterator;
	private final Lock lock = new ReentrantLock();


	public RoundRobin(CopyOnWriteArrayList<T> members) {
		this.members = members;
		if(this.members != null) {
			this.memberIeterator = members.iterator();
		}
	}

	public T next() {
		T member = null;
		try {
			lock.lock();
			//if we get end,start again
			if(!memberIeterator.hasNext()) {
				this.memberIeterator = members.iterator();
			}
			member = memberIeterator.next();
		}catch(Exception e){
			e.printStackTrace();
		}finally {
			lock.unlock();
		}
		return member;
	}


	public static void main(String[] args) {
		CopyOnWriteArrayList<String> members = new CopyOnWriteArrayList<>();

		RoundRobin<String> queueRoundRobin = new RoundRobin<String>(members);
		members.add("queue1");
		members.add("queue2");

		ExecutorService es = Executors.newFixedThreadPool(4);
		Thread t1 = new Thread(new Runnable() {
			@Override
			public void run() {
				System.out.println(Thread.currentThread().getName() + ":" + queueRoundRobin.next());
			}
		});

		es.submit(t1);
		es.submit(t1);
		es.submit(t1);
		es.submit(t1);
		es.submit(t1);
		es.submit(t1);
		es.shutdown();
	}
}
