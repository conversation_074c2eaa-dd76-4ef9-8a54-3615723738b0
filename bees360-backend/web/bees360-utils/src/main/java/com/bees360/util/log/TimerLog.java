package com.bees360.util.log;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.function.Predicate;
import org.slf4j.Logger;

/**
 * 可通过Logger来计算耗时，查看追踪链路
 *
 * <AUTHOR>
 */
public class TimerLog {

    private final Logger log;

    private static final String MASK = "${cost}";

    private Long startTime = null;

    private Long cost = 0L;

    /**
     * 打印info日志的条件
     */
    private Predicate<Long> printPredicate = (cost) -> true;

    /**
     * 打印error日志的条件
     */
    private Predicate<Long> printErrorPredicate = (cost) -> false;

    private final Map<String, Long> timerMap = new HashMap<>();

    protected static final InheritableThreadLocal<String> traceId = new InheritableThreadLocal<>();

    protected static final ThreadLocal<String> threadTraceId = ThreadLocal
        .withInitial(() -> "-" + Math.abs(new Random().nextLong()));

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

    public TimerLog(Logger log) {
        this(log, false);
    }

    public TimerLog(Logger log, boolean resetTraceId) {
        long nowTime = System.currentTimeMillis();
        if (resetTraceId || Objects.isNull(traceId.get())) {
            traceId.set(sdf.format(nowTime) + nowTime);
        }
        this.log = log;
    }

    public static TimerLog newTimerLog(Logger log) {
        return new TimerLog(log, true);
    }

    public static TimerLog getTimerLog(Logger log) {
        return new TimerLog(log);
    }

    /**
     * 设置触发日志打印的阈值
     */
    public void printMinCost(long minCost) {
        printPredicate = (cost) -> cost > minCost;
    }

    public void printErrorMinCost(long minCost) {
        printErrorPredicate = (cost) -> cost > minCost;
    }

    /**
     * 开始计时
     */
    public void start() {
        startTime = System.currentTimeMillis();
    }

    public void start(String timerName) {
        timerMap.put(timerName, System.currentTimeMillis());
    }

    /**
     * 暂停计时
     */
    public TimerLog pause() {
        if (startTime == null) {
            throw new IllegalStateException("startTime is null. Invoke start() at first.");
        }
        cost = System.currentTimeMillis() - startTime;
        return this;
    }

    public TimerLog pause(String timerName) {
        if (timerMap.get(timerName) == null) {
            throw new IllegalStateException("The timer could not be found.");
        }
        cost = System.currentTimeMillis() - timerMap.get(timerName);
        timerMap.remove(timerName);
        return this;
    }

    /**
     * 设置traceId，便于日志中跟踪调用链路，格式为：<主线程链路id>-<子线程链路id>
     */
    protected String setTraceId(String msg) {
        return "[" + traceId.get() + threadTraceId.get() + "] " + msg;
    }

    /**
     * 将指定字符替换为耗时时长
     */
    protected String replaceCost(String msg) {
        return msg.replace(MASK, String.valueOf(cost));
    }

    /**
     * 日志打印
     */
    public void print(String format) {
        format = setTraceId(replaceCost(format));
        if (printErrorPredicate.test(cost)) {
            log.error(format, new IllegalStateException());
            return;
        }
        if (printPredicate.test(cost)) {
            log.info(format);
        }
    }

    public void print(String format, Object arg) {
        format = setTraceId(replaceCost(format));
        if (printErrorPredicate.test(cost)) {
            log.error(format, arg, new IllegalStateException());
            return;
        }
        if (printPredicate.test(cost)) {
            log.info(format, arg);
        }
    }

    public void print(String format, Object arg1, Object arg2) {
        format = setTraceId(replaceCost(format));
        if (printErrorPredicate.test(cost)) {
            log.error(format, arg1, arg2, new IllegalStateException());
            return;
        }
        if (printPredicate.test(cost)) {
            log.info(format, arg1, arg2);
        }
    }

    public void print(String format, Object... arguments) {
        format = setTraceId(replaceCost(format));
        if (printErrorPredicate.test(cost)) {
            log.error(format, arguments, new IllegalStateException());
            return;
        }
        if (printPredicate.test(cost)) {
            log.info(format, arguments);
        }
    }

}
