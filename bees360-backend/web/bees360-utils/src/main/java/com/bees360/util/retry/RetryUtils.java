package com.bees360.util.retry;

import com.bees360.commons.springsupport.property.RetryProperties;

import org.springframework.retry.support.RetryTemplate;

/**
 * <AUTHOR>
 */
public class RetryUtils {

    public static RetryTemplate newRetryTemplate(RetryProperties retryProperties) {
        var retryTemplateBuilder =
                RetryTemplate.builder()
                        .maxAttempts(retryProperties.getMaxAttempts())
                        .exponentialBackoff(
                                retryProperties.getInitialInterval(),
                                retryProperties.getMultiplier(),
                                retryProperties.getMaxInterval())
                        .traversingCauses();
        for (Class<? extends Throwable> cls : retryProperties.getInclude()) {
            retryTemplateBuilder.retryOn(cls);
        }

        var template = retryTemplateBuilder.build();
        return template;
    }
}
