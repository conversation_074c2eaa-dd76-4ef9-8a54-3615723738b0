package com.bees360.util.pdf.gs;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;

/**
 * 更多文档见：https://www.ghostscript.com/doc/9.53.3/Use.htm
 *
 * <AUTHOR>
 */
@Slf4j
public class GsCaller {

    private final String SHELL = "gs -q -dNOPAUSE -dBATCH -dSAFER -sDEVICE=pdfwrite -dCompatibilityLevel=1.7 " +
        "-dEmbedAllFonts=false -dDownsampleColorImages=true -dDetectDuplicateImages=true " +
        "-dColorImageDownsampleType=/Bicubic -dColorImageResolution={dpi} " +
        "-dGrayImageDownsampleType=/Bicubic -dGrayImageResolution={dpi} " +
        "-dMonoImageDownsampleType=/Bicubic -dMonoImageResolution={dpi} " +
        "-sOutputFile={output} {input}";

    public void reducePdfSize(File input, File output, int dpi, @Nullable Duration timeout) throws IOException {

        final String shellToRun = SHELL.replace("{input}", formatFilePath(input.getAbsolutePath()))
            .replace("{output}", formatFilePath(output.getAbsolutePath()))
            .replace("{dpi}", dpi + "");
        String[] cmds = new String[]{"/bin/sh", "-c", shellToRun};

        long start = System.currentTimeMillis();
        Process process = Runtime.getRuntime().exec(cmds);

        devourProcessInputStream(process.getInputStream(), process.getErrorStream());

        int status = 0;
        try {
            if (timeout != null && !process.waitFor(timeout.toMillis(), TimeUnit.MILLISECONDS)) {
                process.destroyForcibly();
                throw new IOException("The process timeout with timeout " + timeout.toMillis() + "ms.");
            } else {
                status = process.waitFor();
            }
        } catch (InterruptedException e) {
            throw new IllegalStateException(e);
        }
        long end = System.currentTimeMillis();

        long spend = end - start;
        if (status != 0) {
            String message = spend + "ms, failed to call shell's command and the return status's is: " + status;
            throw new IllegalStateException(message);
        } else {
            log.info(spend + "ms, finish to exec: " + shellToRun);
        }
    }

    private String formatFilePath(String filePath) {
        // 防止出现空格
        filePath = "\"" + filePath + "\"";
        // 防止出现 %
        return filePath;
    }

    private static void devourProcessInputStream(InputStream in, InputStream error) {
        final boolean needPrint = false;
        devourMessage(in, needPrint);
        devourMessage(error, needPrint);
    }

    private static void devourMessage(final InputStream in, boolean needPrintMessage) {
        new Thread(() -> {
            Reader reader = new InputStreamReader(in);
            BufferedReader br = new BufferedReader(reader);

            String line = null;
            try {
                StringBuilder sb = new StringBuilder();
                while((line = br.readLine()) != null) {
                    // 消除缓存中的内容
                    if(needPrintMessage) {
                        sb.append("\t" + line + "\r\n");
                    }
                }
                if(needPrintMessage) {
                    log.debug(sb.toString());
                }
            } catch (IOException e) {
                log.error("Fail to print message from process", e);
            }
        }).start();
    }
}
