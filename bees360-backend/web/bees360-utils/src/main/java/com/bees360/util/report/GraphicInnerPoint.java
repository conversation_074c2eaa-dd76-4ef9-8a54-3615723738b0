package com.bees360.util.report;

import java.util.List;

import com.bees360.entity.dto.Point;

/**
 * Polygon processing tool class, including concave polygon processing
 *
 * <AUTHOR>
 *
 */
public class GraphicInnerPoint {

	/**
	 * Whether the point of judgment is in a polygon
	 *
	 * @param point
	 *            Detection point
	 * @param points
	 *            The vertex of a polygon
	 * @return Return true in the polygon, or return to false
	 */
	public static boolean isPointInGraphical(Point point, List<Point> points) {
		int n = points.size();
		// If the point is located on the vertex or edge of the polygon,
		// it is also done in the polygon and returns to true directly.
		boolean boundOrVertex = true;
		// cross points count of x
		int intersectCount = 0;
		// The tolerance of the time of comparison between the floating point
		// type and the 0
		double precision = 2e-10;
		// neighbour bound vertices
		Point p1, p2;
		// Current point
		Point p = point;
		// left vertex
		p1 = points.get(0);
		for (int i = 1; i <= n; ++i) {
			// check all rays
			if (p.equals(p1)) {
				// p is an vertex
				return boundOrVertex;
			}
			// right vertex
			p2 = points.get(i % n);
			// ray is outside of our interests
			if (p.getX() < Math.min(p1.getX(), p2.getX()) || p.getX() > Math.max(p1.getX(), p2.getX())) {
				p1 = p2;
				// next ray left point
				continue;
			}
			// ray is crossing over by the algorithm (common part of)
			if (p.getX() > Math.min(p1.getX(), p2.getX()) && p.getX() < Math.max(p1.getX(), p2.getX())) {
				// x is before of ray
				if (p.getY() <= Math.max(p1.getY(), p2.getY())) {
					// overlies on a horizontal ray
					if (p1.getX() == p2.getX() && p.getY() >= Math.min(p1.getY(), p2.getY())) {
						return boundOrVertex;
					}
					if (p1.getY() == p2.getY()) {
						// ray is vertical
						if (p1.getY() == p.getY()) {
							// overlies on a vertical ray
							return boundOrVertex;
						} else {
							// before ray
							++intersectCount;
						}
					} else {
						// cross point on the left side
						double xinters = (p.getX() - p1.getX()) * (p2.getY() - p1.getY()) / (p2.getX() - p1.getX())
								+ p1.getY();
						// cross point of y
						if (Math.abs(p.getY() - xinters) < precision) {
							// overlies on a ray
							return boundOrVertex;
						}
						if (p.getY() < xinters) {
							// before ray
							++intersectCount;
						}
					}
				}
			} else {
				// special case when ray is crossing through the vertex
				// p crossing over p2
				if (p.getX() == p2.getX() && p.getY() <= p2.getY()) {
					// next vertex
					Point p3 = points.get((i + 1) % n);
					// p.x lies between p1.x & p3.x
					if (p.getX() >= Math.min(p1.getX(), p3.getX()) && p.getX() <= Math.max(p1.getX(), p3.getX())) {
						++intersectCount;
					} else {
						intersectCount += 2;
					}
				}
			}
			// next ray left point
			p1 = p2;
		}
        return intersectCount % 2 != 0;
	}
}
