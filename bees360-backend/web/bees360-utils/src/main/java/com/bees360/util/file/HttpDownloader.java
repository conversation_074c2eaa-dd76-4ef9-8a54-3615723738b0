package com.bees360.util.file;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;

public class HttpDownloader {

	public static String download(String urlString, String destfile) throws IOException{
		File file = new File(destfile);
		return download(urlString, file.getParent(), file.getName());
	}

	public static String download(String urlString, String destDirPath, String filename) throws IOException{
		URL url = new URL(urlString);
		URLConnection conn = url.openConnection();
		byte[] bs = new byte[1024];
		int len;
		if(filename == null) {
			filename = getFileNameFromUrl(urlString);
		}
		File destFile = new File(destDirPath, filename);
		File destDir = destFile.getParentFile();
		if(!destDir.exists()) {
			destDir.mkdirs();
		}

		try ( InputStream input = conn.getInputStream();
			  OutputStream output = new FileOutputStream(destFile);) {
			while((len = input.read(bs)) != -1) {
				output.write(bs, 0, len);
			}
			output.flush();
		}
		return destFile.getAbsolutePath();
	}

	private static String getFileNameFromUrl(String urlString) {
		int index = urlString.lastIndexOf("/");
		if(index < 0) {
			return urlString;
		}
		return urlString.substring(index + 1);
	}
}
