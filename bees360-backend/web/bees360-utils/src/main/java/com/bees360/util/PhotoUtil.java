package com.bees360.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;


import com.bees360.entity.ProjectImage;

public class PhotoUtil {
	private int iterationItems;
	private int clustersNumber;
	private List<ProjectImage> imageList;
	private List<ArrayList<ProjectImage>> clusterList;
	private List<Double> clusterCenter;

	public List<ProjectImage> getCloseupList(List<ProjectImage> images){
		if(images.size() == 0 || images == null){
			return new ArrayList<ProjectImage>();
		}
		imageList = new ArrayList<ProjectImage>(images);
		iterationItems = 100;
		clustersNumber = 4;
		imageList.sort(new Comparator<ProjectImage>(){
			@Override
			public int compare(ProjectImage image1, ProjectImage image2) {
				//TimSort java.lang.IllegalArgumentException: Comparison method violates its general contract!
				//return image1.getRelativeAltitude() > image2.getRelativeAltitude() ? 1 : -1;
				return Double.compare(image1.getRelativeAltitude(), image2.getRelativeAltitude());
			}
		});
		prepareclusterCenter();
		prepareClusterList();
		clustering(1);
		return getCloseupList();
	}

	private void prepareclusterCenter(){
		clusterCenter = new ArrayList<Double>(clustersNumber);
		double min = imageList.get(0).getRelativeAltitude();
		double max = imageList.get(imageList.size() - 1).getRelativeAltitude();
		double num = (max - min) / 3;
		clusterCenter.add(min);
		clusterCenter.add(min + num);
		clusterCenter.add(min + num * 2);
		clusterCenter.add(max);
	}

	private void prepareClusterList(){
		clusterList = new ArrayList<ArrayList<ProjectImage>>(clustersNumber);
		for(int i = 0; i < clustersNumber; i++){
			clusterList.add(new ArrayList<ProjectImage>());
		}
	}

	private void clustering(int times){
		if(clusterCenter == null || clusterCenter.size() < 2){
			return;
		}
		Collections.shuffle(clusterCenter);
		for(ProjectImage image : imageList){
			int closest = Integer.MAX_VALUE;
			double closestValue = Double.MAX_VALUE;
			for(int i = 0; i < clustersNumber; i++){
				if(Math.abs(image.getRelativeAltitude()- clusterCenter.get(i)) < closestValue){
					closest = i;
					closestValue = Math.abs(image.getRelativeAltitude() - clusterCenter.get(i));
				}
			}
			clusterList.get(closest).add(image);
		}
		List<Double> nowCenter = new ArrayList<Double>(clustersNumber);
		for(List<ProjectImage> list : clusterList){
			nowCenter.add(getAverage(list));
		}
		//deal the exception: one cluster center is zero
		for(int i = 0; i < nowCenter.size(); i++){
			if(nowCenter.get(i) == 0){
				nowCenter.set(i, getAverage(imageList));
			}
		}
		if(times == iterationItems || clusterCenter.containsAll(nowCenter)){
			return;
		}else{
			for(List<ProjectImage> list : clusterList){
				list.clear();
			}
			clusterCenter = nowCenter;
			clustering(times + 1);
		}
	}

	private double getAverage(List<ProjectImage> list){
		if(list.size() == 0){
			return 0;
		}else{
			double sum = 0;
			for(ProjectImage image : list){
				sum += image.getRelativeAltitude();
			}
			return sum / list.size();
		}
	}

	private List<ProjectImage> getCloseupList(){
		//Collections.sort(imageList);
		List<ProjectImage> subImageList = new ArrayList<ProjectImage>();
		int num = imageList.size() / 4;
		num = (num == 0? imageList.size(): (num < 10 ? num * 2 : num));

		for(int i = 0; i < num; i++){
			subImageList.add(imageList.get(i));
		}
		int max = Integer.MIN_VALUE;
		List<ProjectImage> intersection = new ArrayList<ProjectImage>();
		for(int i = 0; i < clusterList.size(); i++){
			ArrayList<ProjectImage> list = clusterList.get(i);
			if(getIntersection(subImageList, list).size() > max){
				intersection = getIntersection(subImageList, list);
				max = getIntersection(subImageList, list).size();
			}
		}
		return intersection;
	}

	private List<ProjectImage> getIntersection(List<ProjectImage> list1, List<ProjectImage> list2){
		List<ProjectImage> list = new ArrayList<ProjectImage>();
		for(ProjectImage image1 : list1){
			for(ProjectImage image2 : list2){
				if(image1.getRelativeAltitude() == image2.getRelativeAltitude()){
					list.add(image1);
					break;
				}
			}
		}
		return list;
	}
}
