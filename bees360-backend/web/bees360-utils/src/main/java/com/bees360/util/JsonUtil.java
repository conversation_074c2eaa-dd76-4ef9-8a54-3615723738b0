package com.bees360.util;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;

public class JsonUtil {

    /**
     * 将类似 "[1, 2, 3, 4]" 的字符串转化为int数组
     *
     * @param ints json array 类型字符串，格式为: [a, b, ...]
     * @return 空数组，如果<code>intss</code>为null或者空字符串。正常的字符串则为正常的int数组结果
     */
    public static int[] jsonToIntArr(String ints) {
        if (StringUtils.isEmpty(ints)) {
            return new int[0];
        }
        JSONArray jsonArray = new JSONArray(ints);

        int[] array = new int[jsonArray.length()];
        for (int i = 0; i < jsonArray.length(); i++) {
            array[i] = jsonArray.getInt(i);
        }
        return array;
    }

    // <EMAIL> 使得能够保留更多的小数位
    /**
     * 将类似 "[12.2, 54.2, 9.1]" 的字符串转化为double数组
     *
     * @param jsonString json array 类型字符串，格式为: [a, b, ...]
     * @return 空数组，如果<code>jsonString</code>为null或者空字符串。正常的字符串则为正常的double数组结果
     * @throws RuntimeException 参数格式错误
     */
    public static double[] jsonToDoubleArr(String jsonString) {
        if (StringUtils.isEmpty(jsonString)) {
            return new double[0];
        }
        JSONArray jsonArray = new JSONArray(jsonString);

        double[] array = new double[jsonArray.length()];
        for (int i = 0; i < jsonArray.length(); i++) {
            array[i] = jsonArray.getDouble(i);
        }
        return array;
    }

    // <EMAIL> 使得能够保留更多的小数位
    /**
     * 将类似 "[[2.44, 1.33, 5], [7.33, 3.2, 1.9], [12.2, 54.2, 9.1]]" 的字符串转化为double数组 精度只能保留6位。
     *
     * @param jsonString json array 类型字符串，格式为: [[a1, b1, ...], ...]
     * @return 空数组，如果<code>jsonString</code>为null或者空字符串。正常的字符串则为正常的double数组结果
     * @throws RuntimeException 参数格式错误
     */
    public static double[][] jsonToDoubleArrays(String jsonString) {
        if (StringUtils.isEmpty(jsonString)) {
            return new double[0][];
        }
        JSONArray jsonArray = new JSONArray(jsonString);

        double[][] darr = new double[jsonArray.length()][];

        for (int i = 0; i < jsonArray.length(); i++) {
            JSONArray jarr = jsonArray.getJSONArray(i);
            darr[i] = new double[jarr.length()];
            for (int j = 0; j < jarr.length(); j++) {
                darr[i][j] = jarr.getDouble(j);
            }
        }
        return darr;
    }
}
