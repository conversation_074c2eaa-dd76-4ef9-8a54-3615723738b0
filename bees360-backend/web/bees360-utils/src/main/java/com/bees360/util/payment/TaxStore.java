package com.bees360.util.payment;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.bees360.entity.Tax;
import com.bees360.entity.enums.productandpayment.ProductTypeEnum;

public class TaxStore implements Iterable<Tax> {
	private List<Tax> taxes;

	public TaxStore(List<Tax> taxes) {
		this.taxes = taxes;
	}

	public Tax getById(int taxId) {
		return taxes.stream()
			.filter(tax -> tax.getTaxId() == taxId)
			.findFirst()
			.orElse(null);
	}

	/**
	 * there should be a tax for any product type.
	 * @param productType
	 * @return
	 */
	public Tax getByProductType(ProductTypeEnum productType) {
		if(productType == null) {
			return null;
		}
		return getByProductType(productType.getCode());
	}

	public Tax getByProductType(int productType) {
		for(Tax tax: taxes) {
			if(tax.getProductType() == productType) {
				return tax;
			}
		}
		return null;
	}

	@Override
	public Iterator<Tax> iterator() {
		return taxes == null? new ArrayList<Tax>().iterator(): taxes.iterator();
	}
}
