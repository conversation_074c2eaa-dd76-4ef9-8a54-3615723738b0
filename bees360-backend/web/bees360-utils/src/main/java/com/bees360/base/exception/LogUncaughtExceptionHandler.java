package com.bees360.base.exception;

import org.slf4j.Logger;

import java.lang.Thread.UncaughtExceptionHandler;

/**
 * only for the exception thrown from Executor.execute().
 * <AUTHOR>
 *
 */
public class LogUncaughtExceptionHandler implements UncaughtExceptionHandler {
	private Logger logger;

	public LogUncaughtExceptionHandler(Logger logger) {
		this.logger = logger;
	}

	@Override
	public void uncaughtException(Thread t, Throwable e) {
		logger.error(e.getMessage(), e);
	}
}
