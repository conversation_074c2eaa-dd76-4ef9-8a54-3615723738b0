package com.bees360.util.maps.googlemaps;

import com.google.maps.GeoApiContext;
import lombok.extern.slf4j.Slf4j;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/01/19 11:38
 */
@Slf4j
public class GoogleMapUtilBuilder {

    private static final int CONNECT_TIMEOUT_SECONDS = 3;
    private static final int READ_TIMEOUT = 3;
    private static final int MAX_RETRIES = 3;

    private String apiKey;
    private boolean needProxy;
    private int connectTimeoutSeconds = CONNECT_TIMEOUT_SECONDS;
    private int readTimeout = READ_TIMEOUT;
    private int maxRetries = MAX_RETRIES;

    private GeoApiContext context;

    public GoogleMapUtilBuilder(String apiKey) {
        this(apiKey, false);
    }

    public GoogleMapUtilBuilder(String apiKey, boolean needProxy) {
        this.apiKey = apiKey;
        this.needProxy = needProxy;
    }

    public GoogleMapUtilBuilder(GoogleApiConfig googleApiConfig) {
        this.apiKey = googleApiConfig.getApiKey();
        this.needProxy = googleApiConfig.isProxy();
    }

    public GoogleMapUtilBuilder(GeoApiContext context) {
        this.context = context;
    }

    public GoogleMapUtilBuilder init() throws Exception {
        if (context != null) {
            return this;
        }
        context = createContext(apiKey, connectTimeoutSeconds, readTimeout, maxRetries, needProxy);
        return this;
    }

    private GeoApiContext createContext(String apiKey, int connectTimeoutSeconds, int readTimeout, int maxRetries, boolean needProxy)
        throws Exception {
        try {
            GeoApiContext.Builder builder = new GeoApiContext.Builder();
            if(needProxy) {
                builder.proxy(new Proxy(Proxy.Type.HTTP,new InetSocketAddress(8123)));
            }
            return builder.apiKey(apiKey)
                .connectTimeout(connectTimeoutSeconds, TimeUnit.SECONDS)
                .readTimeout(readTimeout, TimeUnit.SECONDS)
                .maxRetries(maxRetries)
                .build();
        } catch(Exception e) {
            throw new Exception("GoogleMapService GeoApiContext initialized failed", e);
        }
    }

    private void checkContext() throws Exception {
        if(context == null) {
            throw new Exception("The init() method should be invoked successfully");
        }
    }

    public GoogleMapHouseBoundaryUtil buildGoogleMapHouseBoundaryUtil() {
        return new GoogleMapHouseBoundaryUtil(context);
    }

    public GoogleMapHouseBoundaryUtil buildGoogleMapHouseBoundaryUtil(int width, int heigh, int zoom, int scala) {
        return new GoogleMapHouseBoundaryUtil(context, width, heigh, zoom, scala);
    }

    public GeocondingUtil buildGeocodingUtil() {
        return new GeocondingUtil(context);
    }
}
