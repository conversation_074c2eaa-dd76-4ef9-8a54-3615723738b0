package com.bees360.util;

import org.apache.commons.lang3.StringUtils;

import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
public class PhoneFormatter {

    public static String simpleFormatUsPhone(String phone) {
        if (StringUtils.isEmpty(phone)) {
            return "";
        }
        if (StringUtils.startsWith(phone, "+")) {
            phone = StringUtils.substringAfter(phone, " ");
        }
        String extension = StringUtils.substringAfterLast(phone, "x");
        String number = StringUtils.substringBeforeLast(phone, "x");
        final int US_PHONE_LEN = 10;
        if (number.length() != US_PHONE_LEN) {
            return phone;
        }
        StringBuilder formattedPhone = new StringBuilder();
        char[] phoneChars = number.toCharArray();
        formattedPhone.append("(");
        IntStream.range(0, 3).forEach(i -> formattedPhone.append(phoneChars[i]));
        formattedPhone.append(") ");
        IntStream.range(3, 6).forEach(i -> formattedPhone.append(phoneChars[i]));
        formattedPhone.append("-");
        IntStream.range(6, phoneChars.length).forEach(i -> formattedPhone.append(phoneChars[i]));
        if (!StringUtils.isEmpty(extension)) {
            formattedPhone.append("x").append(extension);
        }
        return formattedPhone.toString();
    }

}
