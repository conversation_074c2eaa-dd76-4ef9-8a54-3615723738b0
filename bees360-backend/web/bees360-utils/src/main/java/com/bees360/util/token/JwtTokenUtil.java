package com.bees360.util.token;
import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;

import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.enums.AudienceEnum;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Clock;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.DefaultClock;

public class JwtTokenUtil implements Serializable {

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;

    private Clock clock = DefaultClock.INSTANCE;

    private String secret;

    private final SignatureAlgorithm signatureAlgorigthm = SignatureAlgorithm.HS512;

    private String iss;

    public JwtTokenUtil(String secret) {
    	this.secret = secret;
    }

    public JwtTokenUtil(String secret, String iss) {
    	this.secret = secret;
    	this.iss = iss;
    }

    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    public Date getIssuedAtDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getIssuedAt);
    }

    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    public String getAudienceFromToken(String token) {
        return getClaimFromToken(token, Claims::getAudience);
    }

    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = checkAndParseToken2Claims(token);
        return claimsResolver.apply(claims);
    }

    public long getUserIdFromToken(String token) {
    	throw new NotImplementedException();
    }

    public boolean isTokenExpired(String token) {
    	try {
    		checkAndParseToken2Claims(token);
    	} catch (ExpiredJwtException e) {
    		return true;
    	}
    	return false;
    }

    public Claims checkAndParseToken2Claims(String token) {
        return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
    }

    public JwtUser checkAndParseToken(String token) throws JwtException{
        try {
            Claims claims = checkAndParseToken2Claims(token);
            return claims2JwtUser(claims);
        } catch (JwtException e) {
        	throw e;
        }
    }

    public JwtUser claims2JwtUser(Claims claims) {
    	JwtUser jwtUser = new JwtUser();
    	jwtUser.setUserId(Long.parseLong(claims.get("userId").toString()));
    	jwtUser.setName(String.valueOf(claims.get("name")));
    	jwtUser.setAvatar(String.valueOf(claims.get("avatar")));
    	jwtUser.setPhone(String.valueOf(claims.get("phone")));
    	jwtUser.setEmail(String.valueOf(claims.get("email")));

        List<IdNameDto> roleList = new ArrayList<>();
    	JSONArray rolesJsonArray = JSONArray.parseArray(JSON.toJSONString(claims.get("roleList")));
    	rolesJsonArray.forEach(item -> {
			roleList.add(new IdNameDto(((JSONObject) item).getLong("id"), ((JSONObject) item).getString("name")));
		});
    	jwtUser.setRoles(roleList);
    	return jwtUser;
    }

    public String generateToken(JwtUser jwtUser, AudienceEnum audienceEnum) {
    	Map<String, Object> claims = jwtUser2Map(jwtUser);
        return doGenerateToken(claims, jwtUser.getName(), audienceEnum);
    }

    public String refreshToken(JwtUser jwtUser, String token) {
    	String audience = getAudienceFromToken(token);
        AudienceEnum audienceEnum = AudienceEnum.getEnum(audience);
        return generateToken(jwtUser, audienceEnum);
    }

    public Map<String, Object> jwtUser2Map(JwtUser jwtUser) {
    	 Map<String, Object> claims = new HashMap<String, Object>();
         claims.put("userId", jwtUser.getUserId());
         claims.put("name", jwtUser.getName());
         claims.put("avatar", jwtUser.getAvatar());
         claims.put("phone", jwtUser.getPhone());
         claims.put("email", jwtUser.getEmail());
         claims.put("roleList", jwtUser.getRoles());

         return claims;
    }

    private String doGenerateToken(Map<String, Object> claims, String subject, AudienceEnum audienceEnum) {
        final String jti = generateJti();

        final Date createdDate = clock.now();
        final Date expirationDate = calculateExpirationDate(createdDate, audienceEnum);

        return Jwts.builder()
                .setClaims(claims)
                .setId(jti)
                .setSubject(subject)
                .setAudience(audienceEnum.getDisplay())
                .setIssuedAt(createdDate)
                .setExpiration(expirationDate)
                .setIssuer(iss)
                .signWith(signatureAlgorigthm, secret)
                .compact();
    }

    private String generateJti() {
    	// use uuid to represent the jti to make sure every jti is unique.
    	UUID uuid = UUID.randomUUID();
		return uuid.toString().replace("-", "");
	}

    private Date calculateExpirationDate(Date createdDate, AudienceEnum audienceEnum) {
    	if(audienceEnum == null) {
    		audienceEnum = AudienceEnum.UNKNOWN;
    	}
    	return new Date(createdDate.getTime() + audienceEnum.getJwtExp());
    }
}
