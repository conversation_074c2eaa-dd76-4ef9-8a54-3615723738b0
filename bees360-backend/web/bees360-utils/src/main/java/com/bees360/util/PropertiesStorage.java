package com.bees360.util;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2019/12/15 01:16
 */
public class PropertiesStorage {

    private static Properties propertiesStorage = new Properties();

    public static boolean containsKey(String name) {
        return propertiesStorage.containsKey(name);
    }

    public static String getProperty(String key) {
        return propertiesStorage.getProperty(key);
    }

    public static void setProperties(Properties properties) {
        if(properties == null) {
            throw new NullPointerException("parameter [properties] shouldn't be null.");
        }
        propertiesStorage = properties;
    }

    public static void mergeProperties(Properties properties) {
        for(String name: properties.stringPropertyNames()) {
            propertiesStorage.put(name, properties.getProperty(name));
        }
    }

    public static void addProperties(String name, String value) {
        propertiesStorage.put(name, value);
    }

    public static String remove(String name) {
        Object result = propertiesStorage.remove(name);
        return result == null? null: result.toString();
    }
}
