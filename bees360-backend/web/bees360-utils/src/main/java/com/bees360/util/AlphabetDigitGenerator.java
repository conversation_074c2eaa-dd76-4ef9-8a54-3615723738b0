package com.bees360.util;

import com.google.common.primitives.Chars;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import java.util.Collections;
import java.util.List;
import java.util.Random;

/**
 *
 * <AUTHOR>
 *
 */
public class AlphabetDigitGenerator {


    public static String generateWithUppercase(int count, int letterCount){
		if (count == 0) {
			return null;
		}
		int numericCount = count - letterCount;
		char[] letterChars = generateRandomAlphabetics(letterCount);
		char[] numberChars = generateRandomNumerics(numericCount);

		char[] chars = new char[count];
		if (letterChars != null) {
			System.arraycopy(letterChars, 0, chars, 0, letterCount);
		}
		if (numberChars != null) {
			System.arraycopy(numberChars, 0, chars, letterCount, numericCount);
		}

		List<Character> charList = Chars.asList(chars);
		Collections.shuffle(charList);
		return StringUtils.join(charList.stream().toArray());
    }



    private static char[] generateRandomAlphabetics(int count) {
    	if(count == 0) {
    		return null;
    	}

    	String letter = RandomStringUtils.randomAlphabetic(count);
    	char[] letterChars = letter.toCharArray();

        boolean hasUpperLetter = false;
        for(int i=0; i< letterChars.length; i++){
            char originalChar = letterChars[i];
            if(Character.isUpperCase(originalChar)){
                hasUpperLetter = true;
                break;
            }
        }
        if(!hasUpperLetter){
            Random random = new Random();
            int index = random.nextInt(letterChars.length);
            char originalChar = letterChars[index];
            letterChars[index] = Character.toUpperCase(originalChar);
        }
        return letterChars;
    }


	private static char[] generateRandomNumerics(int count) {
		if (count == 0) {
			return null;
		}
		String number = RandomStringUtils.randomNumeric(count);
		return number.toCharArray();
	}


    public static void main(String[] args){
        System.out.println(generateWithUppercase(8, 3));
    }
}
