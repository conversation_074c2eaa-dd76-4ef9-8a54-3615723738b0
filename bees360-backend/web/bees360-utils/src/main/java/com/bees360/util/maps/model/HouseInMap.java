package com.bees360.util.maps.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class HouseInMap {
	// the boundary of the roof. The first row is xPoints, the second row is yPoints.
	private List<LatLngPoint> roofBoundary;

	public List<LatLngPoint> getRoofBoundary() {
		return roofBoundary;
	}
	public void setRoofBoundary(List<LatLngPoint> roofBoundary) {
		this.roofBoundary = roofBoundary;
	}

	@Override
	public String toString() {
		return "HouseInMap [roofBoundary=" + roofBoundary + "]";
	}
}
