package com.bees360.util;

import com.alibaba.fastjson.JSONObject;
import com.bees360.base.exception.ServiceException;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

@Component
public final class RedisUtil {

	private RedisTemplate<Serializable, Object> redisTemplate;

	public RedisUtil(RedisTemplate<Serializable, Object> redisTemplate) {
		this.redisTemplate = redisTemplate;
	}

	public synchronized void set(final Serializable key, Object value) throws Exception{
		ValueOperations<Serializable, Object> operations = redisTemplate.opsForValue();
		operations.set(key, value);
	}

    public synchronized Boolean setIfAbsent(final Serializable key, Object value) throws Exception{
        ValueOperations<Serializable, Object> operations = redisTemplate.opsForValue();
        return operations.setIfAbsent(key, value);
    }

	public synchronized void set(final Serializable key, Object value, long expireTime) throws Exception{
		ValueOperations<Serializable, Object> operations = redisTemplate.opsForValue();
		operations.set(key, value);
		redisTemplate.expire(key, expireTime, TimeUnit.MILLISECONDS);
	}

	public synchronized Object get(final Serializable key) throws Exception{
		Object result = null;
		ValueOperations<Serializable, Object> operations = redisTemplate.opsForValue();
		result = operations.get(key);
		return result;
	}

    public synchronized Long getExpire(final Serializable key) throws Exception{
        return redisTemplate.getExpire(key);
    }

	public synchronized void delete(final Serializable key) throws Exception{
		if (hasKey(key)) {
			redisTemplate.delete(key);
		}
	}

	/**
	 * some operation for hash
	 * @param key
	 * @return
	 */
	public synchronized Object hGet(Serializable key, Serializable hashKey) throws Exception{
		return redisTemplate.opsForHash().get(key, hashKey);
	}

	public synchronized void hPut(Serializable key, Serializable hashKey, Object value) throws Exception{
		redisTemplate.opsForHash().put(key, hashKey, value);
	}

	public synchronized void hDelete(Serializable key, Object... hashKeys) throws Exception{
		redisTemplate.opsForHash().delete(key, hashKeys);
	}

	public synchronized boolean hHasKey(final Serializable key, Object hashKey) throws Exception{
		return redisTemplate.opsForHash().hasKey(key, hashKey);
	}

	public synchronized boolean hasKey(final Serializable key) throws Exception{
		return redisTemplate.hasKey(key);
	}

	public synchronized boolean expire(final Serializable key, long timeout) throws Exception{
		return redisTemplate.expire(key, timeout, TimeUnit.MILLISECONDS);
	}

    public synchronized boolean expire(final Serializable key, long timeout, TimeUnit timeUnit) throws Exception{
        return redisTemplate.expire(key, timeout, timeUnit);
    }



    /**
     * add data to redis
     *
     * @param cacheKey the redis data key
     * @param t the redis data value
     * @param <T> the type
     * @throws ServiceException Data settings failed.
     */
    public synchronized <T> void setDataToRedis(String cacheKey, T t) throws Exception {
        set(cacheKey, JSONObject.toJSONString(t));
    }

    /**
     * list data from redis
     *
     * @param cacheKey the redis data key
     * @param clazz    the type
     * @param <T>      the type
     * @return the data
     * @throws ServiceException Data list failed.
     */
    public synchronized <T> List<T> listDataFromRedis(String cacheKey, Class<T> clazz) throws Exception {
        List<T> list = new ArrayList<>();
        Gson gson = new Gson();
        if (!hasKey(cacheKey)) {
            return list;
        }
        String listStr = (String) get(cacheKey);
        JsonArray jsonArray = new JsonParser().parse(listStr).getAsJsonArray();
        for (JsonElement jsonElement : jsonArray) {
            list.add(gson.fromJson(jsonElement, clazz));
        }
        return list;
    }
}
