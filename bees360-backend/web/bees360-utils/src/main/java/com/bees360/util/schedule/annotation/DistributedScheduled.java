package com.bees360.util.schedule.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/04/07
 */
@Documented
@Inherited
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface DistributedScheduled {
    /**
     * 是否为分布式定时器，false：等同于普通的spring定时器
     * @return
     */
    boolean distributedEnable() default true;

    /**
     *  定时器开关
     * @return
     */
    boolean executeEnable() default true;


}
