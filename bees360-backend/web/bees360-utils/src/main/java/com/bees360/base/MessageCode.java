package com.bees360.base;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Locale;

import com.bees360.base.code.ApiErrorType;
import com.bees360.base.code.MessageCodeDocument;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;

public interface MessageCode {
//  <EMAIL>: it should be changed to MessageNum ?

	// common message code 001-1000
	// system exception
	@MessageCodeDocument(type = ApiErrorType.INTERNAL)
	String SYSTEM_EXCEPTION = "1";

	@MessageCodeDocument(type = ApiErrorType.INTERNAL)
	//some error occur in database operation
	String DATABASE_EXCEPTION = "2";

	// send email failed
	@MessageCodeDocument(type = ApiErrorType.INTERNAL)
	String EMAIL_SEND_FAILED = "3";

	// cannot link to amazon
	@MessageCodeDocument(type = ApiErrorType.UNAVAILABLE)
	String LINK_AMAZON_FAIL = "4";

	//param is invalid
	@MessageCodeDocument(type = ApiErrorType.BAD_REQUEST)
	String PARAM_INVALID = "7";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String CONTACT_FOR_DETAILS = "8";

	@MessageCodeDocument(type = ApiErrorType.UNAVAILABLE)
	String S3_DOWNLOAD_FAIL = "100";

	@MessageCodeDocument(type = ApiErrorType.UNAVAILABLE)
	String S3_FAIL_UPLOAD_IMAGES = "102";

	@MessageCodeDocument(type = ApiErrorType.UNAVAILABLE)
	String S3_FAIL_UPLOAD_IMAGES_ARCHIVE = "103";

	@MessageCodeDocument(type = ApiErrorType.UNAVAILABLE)
	String S3_FAIL_ARCHIVE_IMAGES = "104";

	@MessageCodeDocument(type = ApiErrorType.BAD_REQUEST)
	String BAD_REQUEST = "400";

	@MessageCodeDocument(type = ApiErrorType.FORBIDDEN)
	String AUTHORIZE_FAILED = "403";

	// Request error
	@MessageCodeDocument(type = ApiErrorType.TOO_MANY_REQUESTS)
	String REQUEST_RATE_LIMIT = "700";

	@MessageCodeDocument(type = ApiErrorType.INVALID_ARGUMENT)
	String VALIDATION_FAILED = "701";

	@MessageCodeDocument(type = ApiErrorType.NOT_FOUND)
	String DATA_NON_EXIST = "703";

	@MessageCodeDocument(type = ApiErrorType.BAD_REQUEST)
	String RESOURCE_EXISTED = "705";

	// user message code 1001-2000
	//user has existed
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_HAS_EXISTED = "1001";

	//user name has existed
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_NAME_HAS_EXISTED = "1002";

	//user phone has existed
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PHONE_IS_TAKEN = "1003";

	/*
	 * user email has existed
	 */
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String EMAIL_IS_TAKEN = "1004";

	//user is not registed
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_IS_UNREGISTERED = "1005";

	//user's mail or phone is null
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_MAIL_OR_PHONE_CANNOT_BE_NULL = "1008";

	//user's password can not be null
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_PASSWORD_IS_NULL = "1009";

	//user's password is not correct
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_PASSWORD_INCORRECT = "1011";

	//user's mail or phone's format is incorrect
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String EMAIL_OR_PHONE_FORMAT_INCORRECT = "1012";

	//user's name or password is incorrect
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_NAME_OR_PASSWORD_IS_INCORRECT = "1013";

	//user's status is inactive, he need to reset his password to inactive user
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_IS_INACTIVE = "1014";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_NOT_EXISTED = "1016";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String INVITE_ASSETOWNER_FAIL = "1017";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PHONE_FORMAT_INCORRECT = "1018";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String EMAIL_FORMAT_INCORRECT = "1019";

	//user's status is inactive, he need to reset his password to inactive user
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_IS_DELETED = "1020";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PASSWORD_REPASSWORD_NOT_SAME = "1021";

	//user's password can not be null
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_PASSWORD_UNAVAILABLE = "1022";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String FIRST_NAME_OR_LAST_NAME_IS_EMPTY = "1023";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String FIRST_NAME_OR_LAST_NAME_TOO_LONG = "1024";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PHONE_MAIL_BOTH_EMPTY = "1025";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String EMPLOYEE_ID_TOO_LONG = "1026";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_HAS_ROLE = "1027";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_IS_APPLYING_ROLE = "1028";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String ADDRESS_TOO_LONG = "1029";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String ZIPCODE_TOO_LONG = "1030";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_ROLE_NOT_EXIST = "1031";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PHONE_BINDED_BY_OTHER = "1032";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String EMAIL_BINDED_BY_OTHER = "1033";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String EMAIL_OR_PHONE_NEED = "1034";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String EMAIL_NOT_BINDED = "1035";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PHONE_NOT_BINDED = "1036";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REDIS_OPERATION_FAILED = "1038";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String OPERATION_FORBIDDEN = "1040";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String EMAIL_CAN_NOT_NULL = "1041";

    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String PILOT_LOGIN_NOT_ALLOWED = "1042";

    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String SSO_LOGIN_ENFORCED = "1043";

	// company message code 2001-3000
	//company has existed
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String COMPANY_NAME_HAS_EXISTED = "2001";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String COMPANY_NOT_EXISTED = "2002";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String INSURANCE_COMPANY_NOT_EXISTED = "2003";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPAIR_COMPANY_NOT_EXISTED = "2004";

//	project image code 3001 - 4000
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String IMAGE_UPLOAD_ERROR = "3001";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PROJECT_TYPE_IS_NULL = "3002";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String CLAIM_TYPE_IS_NULL = "3004";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String CLAIM_CANNOT_MODIFIED = "3005";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String INSPECTION_TIME_INVALID = "3006";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String IMAGE_NOT_EXIST = "3008";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String SET_OVERVIEW_IMAGE_FAILED = "3010";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String THERE_IS_NO_OVERVIEW_IN_A_PROJECT = "3011";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String THERE_ARE_TWO_OR_MORE_OVERVIEWS_IN_A_PROJECT = "3012";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String IMAGE_OVERVIEW_NOT_EXIST = "3013";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String IMAGE_SOURCE_TYPE_NNOT = "3014";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String IMAGE_IS_EMPTY = "3015";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String IMAGE_SAVE_FILED = "3016";

    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String IMAGE_ADDRESS_VERIFICATION_SINGLE = "3018";

    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String IMAGE_NO_NEED_UPLOAD = "3019";

    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String IMAGE_USED_BY_REPORT = "3020";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PROJECT_SERVICE_TYPE_UPDATE_FAILED = "3017";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PROJECT_NOT_EXIST = "3100";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PROJECT_CANNOT_OPERATE = "3101";

//	message errer 4001 - 5000
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String FAIL_SEND_MESSAGE = "4001";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String MEMBER_CANNOT_CANCEL = "5002";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String MEMBER_CANNOT_SCHEDULE = "5003";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String ROLE_NOT_EXIST = "5004";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String DATA_FORMAT_ERROR = "5008";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String SECURITYCODE_INCORRECT = "5009";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String SECURITYCODE_INVALIDITY = "5010";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_CANNOT_CLOSE_PROJECT_PROCESSING = "5011";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String USER_IS_MEMBER = "5012";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String ROLE_CANNOT_BE_ASSIGNED = "5013";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PROJECT_HAS_ROLE_MEMBER = "5014";

    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String PROJECT_ARCHIVE_NOT_EXIST = "5016";

    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String PROJECT_DUPLICATION = "5017";

	// ai
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String THREED_REQUEST_FAILED = "6001";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String IS_NOT_NEXT_AI_STEP = "6002";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String AI_OPERATION_FORBIDDEN = "6003";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String WRONG_STATUS = "6004";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String AI_IS_RUNNING = "6005";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String AI_NOT_THERE_YET = "6007";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_IS_GENERATING = "6008";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String AI_PROCESS_FORBIDDEN = "6009";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String AI_PROCESS_FAIL = "6010";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String AD_NOT_GENERATED = "6101";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String AD_FILE_SOURCE_TYPE_FORBIDDEN = "6103";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String SELECTED_CAN_NOT_EMPTY = "6011";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String IMAGE_NOT_ENOUGH = "6012";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String IMAGE_CROP_ERROR = "6013";

	@MessageCodeDocument(type = ApiErrorType.UNAVAILABLE)
	String HADOOP_RANGING_COMMIT_FAIL = "7000";
	@MessageCodeDocument(type = ApiErrorType.UNAVAILABLE)
	String HADOOP_SCOPING_COMMIT_FAIL = "7001";
	@MessageCodeDocument(type = ApiErrorType.UNAVAILABLE)
	String HADOOP_PRE_PLANE_COMMIT_FAIL = "7002";
	@MessageCodeDocument(type = ApiErrorType.UNAVAILABLE)
	String HADOOP_BOUNDARY_COMMIT_FAIL = "7003";
	@MessageCodeDocument(type = ApiErrorType.UNAVAILABLE)
	String HADOOP_POSTBOUNDARY_COMMIT_FAIL = "7004";

	//payment module 8001-9000
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_PAYER_ID_CAN_NOT_BE_NULL = "8001";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_PAYMENT_METHOD_CAN_NOT_BE_NULL = "8002";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String CARD_NONCE_PARAM_REQUIRED = "8003";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_ORDER_CAN_NOT_BE_NULL = "8004";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_AMOUT_CAN_NOT_BE_NULL = "8005";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_PAYMENT_API_CALL_FAILED = "8006";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_REMAINING_WALLET_BALANCE_INSUFFICIENT = "8007";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_PAYMENT_METHOD_IS_NOT_SUPPORTED = "8008";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_PAYMENT_FAILED = "8010";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_PAYMENT_BOOKING_USERNAME_CAN_NOT_BE_NULL = "8011";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_PAYMENT_AMOUNT_IS_NOT_MATCHED = "8012";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_PAYMENT_SERVICE_IS_NOT_SUPPORT = "8013";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_PAYMENT_YOU_MUST_PAY_FOR_THE_SAME_PROJECT = "8014";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PAY_PAYMENT_REPORT_IS_NOT_SUPPORT = "8015";


    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String REPORT_NEED_COMPRESSED = "8999";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_IMAGE_NULL = "9001";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_COMPANY_NULL = "9002";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_COMPANY_LOGO_NULL = "9003";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_NOT_EXIST = "9004";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_BEEN_APPROVED = "9005";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_BEEN_DISAPPROVED = "9006";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_NO_ENOUGH_PIC = "9007";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_NOT_SUBMITTED = "9008";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_BEEN_SUBMITTED = "9009";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_QUICK_DAMAGE_NOELEMENT = "9010";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_PREVISIT_NOELEMENT = "9011";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_REALTIME_NOELEMENT = "9013";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_DATE_ERROR = "9012";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_BING_GENERATED = "9017";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_GENERATED_LATER = "9018";
    @MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
    String REPORT_CANNOT_APPROVE = "9021";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_CANCEL_FORBIDDEN = "9014";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String UNIT_PRICE = "9015";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String BID_TOTAL_PRICE_ERROR = "9016";
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String IMAGES_NOT_GENERATE = "9020";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String FACET_NOT_EXIST = "10001";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String ROLE_ID_BEYOND_LIMIT= "10054";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String APPLICATION_ROLE_ID_BEYOND_LIMIT= "10055";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String AUTO_DETECT_DAMAGE_HAS_STARTED = "10056";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String OVERVIEW_NOT_EXIST ="10057";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String REPORT_SERVICE_OPTION_IS_NULL = "12001";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String CONTACTER_NAME_IS_BLANK = "12002";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String CONTACTER_EMAIL_IS_BLANK = "12003";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String CONTACTER_PHONE_IS_BLANK = "12004";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String HOUSE_DATA_NOT_EXIST = "12005";

	@MessageCodeDocument(type = ApiErrorType.UNAVAILABLE)
	String FAIL_TO_GENERATE_FILE = "12010";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String DUPLICATE_WINDOWS = "12020";
	// order tasks
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PILOT_NEED_CANNT_CANCEL = "13000";

	// PRODUCT_**** 122****
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PRODUCT_NOT_EXIST = "1220001";

	// PRICE_**** 123****
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String PRICE_UNCERTAIN = "1230001";

	// TRANSACTION_**** 124****
	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String TRANSACTION_DISCOUNT_NOT_EXIST = "1240001";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String TRANSACTION_DISCOUNT_UNAVAILABLE = "1240002";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String TRANSACTION_PAYMENT_METHOD_NOT_EXIST = "1240003";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String TRANSACTION_PRICE_CHANGED = "1240004";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String TRANSACTION_PAYMENTMETHOD_NOT_SUPPORTED = "1240005";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String TRANSACTION_BALANCE_NOT_ENOUGH = "1240006";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String TRANSACTION_DISCOUNT_NOT_ENOUGH = "1240007";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String TRANSACTION_PRODUCT_DOUBLE_PAYMENT = "1240008";

	@MessageCodeDocument(type = ApiErrorType.FAILED_PRECONDITION)
	String TRANSACTION_CREDIT_CARD_FAIL_TO_PAY = "1240020";



    static String getMessage(MessageSource messageSource, String messageCode) {
		if(messageSource == null || messageCode == null) {
			return null;
		}
		return messageSource.getMessage(messageCode, null, Locale.ENGLISH);
	}

	@Slf4j
	class ErrorTypeOpt {

        static Map<String, ApiErrorType> messageCodeErrorTypeMap = new HashMap<>(256);

        static {
            initMessageCodeErrorTypeMap();
        }

        /**
         * 将错误码和错误类型的映射写入Map
         */
        static void initMessageCodeErrorTypeMap() {
            Field[] fields = MessageCode.class.getFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(MessageCodeDocument.class)) {
                    MessageCodeDocument messageCodeDocument = field.getAnnotation(MessageCodeDocument.class);
                    try {
                        String messageCode = (String) field.get(MessageCode.class);
                        messageCodeErrorTypeMap.put(messageCode, messageCodeDocument.type());
                    } catch (IllegalAccessException e) {
                        log.warn("Get MessageCode Failed. fieldName:{}", field.getName());
                    }
                }
            }
        }

        /**
         * 获取 messageCode 的类型
         */
        static ApiErrorType getErrorType(String messageCode) {
            return messageCodeErrorTypeMap.get(messageCode);
        }
    }

    /**
     * 获取 messageCode 的类型
     */
    static ApiErrorType getErrorType(String messageCode) {
        return ErrorTypeOpt.getErrorType(messageCode);
    }
}
