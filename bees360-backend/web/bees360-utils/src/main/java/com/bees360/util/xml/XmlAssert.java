package com.bees360.util.xml;

import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 */
public class XmlAssert {

    public static boolean xmlIsNodeEquals(String one, String two) {
        try (InputStream xmlOne = new ByteArrayInputStream(one.getBytes());
             InputStream xmlTwo = new ByteArrayInputStream(two.getBytes())){
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            dbf.setCoalescing(true);
            dbf.setIgnoringElementContentWhitespace(true);
            dbf.setIgnoringComments(true);

            DocumentBuilder db = dbf.newDocumentBuilder();

            Document doc1 = db.parse(xmlOne);
            doc1.normalizeDocument();

            Document doc2 = db.parse(xmlTwo);
            doc2.normalizeDocument();

            return doc1.isEqualNode(doc2);
        } catch (Exception e) {
            throw new IllegalArgumentException(e);
        }
    }
}
