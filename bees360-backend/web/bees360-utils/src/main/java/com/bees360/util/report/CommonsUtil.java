package com.bees360.util.report;

import java.io.File;

import com.bees360.util.ConstantUtil;
import org.apache.commons.lang3.StringUtils;

import com.bees360.entity.Project;

public class CommonsUtil {

    public static final String TIME_ZONE_US_CENTER = "US/Central";

    private static final String UPLOAD_KEY = "project/${projectId}/report/";

	private static String getAddress(Project project) {
		String address = StringUtils.EMPTY;
		if (StringUtils.isNotEmpty(project.getAddress())) {
			address += project.getAddress();
			if (StringUtils.isNotEmpty(project.getCity()) || StringUtils.isNotEmpty(project.getState())) {
				address += ConstantUtil.COMMA + ConstantUtil.BLACK_SPACE;
			}
			if (StringUtils.isNotEmpty(project.getCity())) {
				address += project.getCity();
				if (StringUtils.isNotEmpty(project.getState())) {
					address += ConstantUtil.COMMA + ConstantUtil.BLACK_SPACE + project.getState();
				}
			} else {
				if (StringUtils.isNotEmpty(project.getState())) {
					address += project.getState();
				}
			}
		} else {
			if (StringUtils.isNotEmpty(project.getCity())) {
				address = project.getCity();
				if (StringUtils.isNotEmpty(project.getState())) {
					address += ConstantUtil.COMMA + ConstantUtil.BLACK_SPACE + project.getState();
				}
			} else {
				if (StringUtils.isNotEmpty(project.getState())) {
					address += project.getState();
				}
			}
		}

		if (StringUtils.isNotEmpty(project.getZipCode())) {
			if (StringUtils.isNotEmpty(project.getAddress()) || StringUtils.isNotEmpty(project.getCity())
					|| StringUtils.isNotEmpty(project.getState())) {
				address += ConstantUtil.BLACK_SPACE + project.getZipCode();
			} else {
				address += project.getZipCode();
			}
		}
		return address;
	}

	public static String getPackageKey(long projectId, int reportType, String packageName) {
		return UPLOAD_KEY.replace("${projectId}", projectId + StringUtils.EMPTY) + reportType + File.separator + packageName;
	}

	public static String getPackageName(Project project, int reportType) {
		return project.getProjectId() + ConstantUtil.UNDERLINE + reportType + ConstantUtil.UNDERLINE + getAddress(project) + ".zip";
	}
}
