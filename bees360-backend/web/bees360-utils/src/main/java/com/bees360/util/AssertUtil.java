package com.bees360.util;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceMessageException;
import org.apache.commons.collections4.MapUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * 手动校检请求参数，或者操作是否正常
 * <AUTHOR>
 * @since 2020/3/19 5:57 PM
 **/
public class AssertUtil {
    private AssertUtil(){}

    /**
     * Assert <code>object</code> is not null
     * @param object the object to be checked
     * @param message that message will be sent to client
     */
    public static void notNull(Object object, String message) throws ServiceMessageException{
        assertTrue(Objects.nonNull(object), MessageCode.PARAM_INVALID, message);;
    }

    /**
     * Assert <code>object</code> is not null
     * @param code exception type code
     * @param message that message will be sent to client
     * @param object the object to be checked
     */
    public static void notNull(Object object, String code, String message) throws ServiceMessageException{
        assertTrue(Objects.nonNull(object), code, message);;
    }

    /**
     * Assert <code>str</code> is not empty and has text
     * @param message that message will be sent to client
     * @param str the string to be checked if it has text
     */
    public static void notEmpty(String str, String message) throws ServiceMessageException{
        assertTrue(StringUtils.hasText(str), MessageCode.PARAM_INVALID, message);
    }

    /**
     * Assert <code>array</code> is not empty and it`s size bigger than 0
     * @param message that message will be sent to client
     * @param array the array to be checked if it contains object
     */
    public static void notEmpty(Object[] array, String message) throws ServiceMessageException{
        assertTrue(array != null && array.length > 0, MessageCode.PARAM_INVALID, message);
    }

    /**
     * Assert <code>collection</code> is not empty
     * @param collection the object to be checked
     * @param message that message will be sent to client
     */
    public static void notEmpty(Collection<?> collection, String message) throws ServiceMessageException{
        assertTrue(!CollectionUtils.isEmpty(collection), MessageCode.PARAM_INVALID, message);
    }

    /**
     * Assert <code>map</code> is not empty
     * @param message that message will be sent to client
     * @param map the object to be checked if the map is empty
     */
    public static void notEmpty(Map<?,?> map, String message) throws ServiceMessageException{
        assertTrue(!MapUtils.isEmpty(map), MessageCode.PARAM_INVALID, message);
    }


    /**
     * Assert <code>flag</code> is true, when flag is not true then do exceptin
     * @param flag the boolean to be tested
     * @param message that message will be sent to client
     * @param code exception code
     * @see MessageCode
     */
    public static void assertTrue(boolean flag, String code, String message) throws ServiceMessageException{
        if (!flag) {
            doException(code, message);
        }
    }

    private static void doException(String code, String message) throws ServiceMessageException{
        throw new ServiceMessageException(code, message);
    }
}
