package com.bees360.util;

import java.util.regex.Pattern;

public class RegexUtil {

	//the regex expression of phone
	private static final String REGEX_EXPRESSION_PHONE = "^\\+?[1-9]{1}[0-9]{3,14}$";

	//the regex expression of email - can not match all email
//	private static final String REGEX_EXPRESSION_EMAIL = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";

	/**
	 * judge the String is phone format. define the phone format is "+{country code} {phone number}".
	 * @param phone
	 * @return
	 */
	public static boolean isPhone(String phone) {
		if(phone == null || phone.length() == 0) {
			return false;
		}
		// <EMAIL>: check the phone format more reasonable.
		if(phone.startsWith("+")) {
			phone = phone.substring(1).replaceAll("\\s+", "");
		}
        return Pattern.matches(REGEX_EXPRESSION_PHONE, phone);
    }

	public static boolean isEmail(String email) {
		if(email == null || email.length() == 0) {
			return false;
		}
//		return Pattern.matches(REGEX_EXPRESSION_EMAIL, email);
		return email.contains("@") && email.lastIndexOf("@") < (email.length() - 1);
	}

	public static boolean isPhoneOrEmail(String target){
		return isPhone(target) || isEmail(target);
	}

	public static void main(String[] args) {
		System.out.println(RegexUtil.isPhone("<EMAIL>"));
	}
}
