package com.bees360.util.msgutil;

public class EmailSenderSetting {
	private String host;
	private String port;
	private String protocol;

	private String senderName;
	private String senderEmail;
	private String senderPsw;

	public static EmailSenderSetting EMAIL_GOOGLE = new EmailSenderSetting("smtp.gmail.com", "465", "smtp");
	public static EmailSenderSetting EMAIL_163 = new EmailSenderSetting("smtp.163.com", "465", "smtp");
	public static EmailSenderSetting EMAIL_QQ = new EmailSenderSetting("smtp.qq.com", "465", "smtp");

	public EmailSenderSetting() {}

	public EmailSenderSetting(String host, String port, String protocol) {
		this.host = host;
		this.port = port;
		this.protocol = protocol;
	}

	public String getHost() {
		return host;
	}
	public void setHost(String host) {
		this.host = host;
	}
	public String getPort() {
		return port;
	}
	public void setPort(String port) {
		this.port = port;
	}
	public String getProtocol() {
		return protocol;
	}
	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}
	public String getSenderName() {
		return senderName;
	}
	public void setSenderName(String senderName) {
		this.senderName = senderName;
	}
	public String getSenderEmail() {
		return senderEmail;
	}
	public void setSenderEmail(String senderEmail) {
		this.senderEmail = senderEmail;
	}
	public String getSenderPsw() {
		return senderPsw;
	}
	public void setSenderPsw(String senderPsw) {
		this.senderPsw = senderPsw;
	}

	@Override
	public String toString() {
		return "EmailSenderSetting [host=" + host + ", port=" + port + ", protocol=" + protocol + ", senderName="
				+ senderName + ", senderEmail=" + senderEmail + ", senderPsw=" + senderPsw + "]";
	}
}
