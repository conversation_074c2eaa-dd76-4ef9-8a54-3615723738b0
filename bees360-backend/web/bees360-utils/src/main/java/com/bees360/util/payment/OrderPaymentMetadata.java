package com.bees360.util.payment;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

public class OrderPaymentMetadata {
	private Map<String, String> metadata;

	public OrderPaymentMetadata() {
		metadata = new HashMap<String, String>();
	}

	public Map<String, String> getMetadata() {
		return metadata;
	}

	public void setUserId(long userId) {
		metadata.put("userId", userId + "");
	}

	public void setOrderIds(List<Long> orderIds) {
		metadata.put("orderIds", StringUtils.join(orderIds, ","));
	}
}
