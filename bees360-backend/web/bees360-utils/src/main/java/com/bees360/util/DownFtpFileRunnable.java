package com.bees360.util;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.SocketException;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;

import com.maxmind.db.CHMCache;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.WebServiceClient;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.model.CountryResponse;
import com.maxmind.geoip2.record.Country;

public class DownFtpFileRunnable implements Runnable{

	@Override
	public void run() {
		// source: ftp://ftp.arin.net/pub/stats/arin/delegated-arin-extended-latest
		// destination: /home/<USER>/project/data/bees360/data/delegated-arin-extended-latest.txt
		// Anonymous to connection remote FTP
		String ftpHost = "ftp.arin.net";
		String ftpUserName = "anonymous";
		String ftpPassword = "<EMAIL>";
		String ftpPath = "/pub/stats/arin/";
		String localPath = "/home/<USER>/test/";
		String fileName = "delegated-arin-extended-latest";
		long start = System.currentTimeMillis();
		downloadFtpFile(ftpHost, ftpUserName, ftpPassword, ftpPath, localPath, fileName);
		long end = System.currentTimeMillis();
		System.out.println("spend time = " + (end - start) * 1.0 / 1000 + " s");
	}

	public static FTPClient getFTPClient(String ftpHost, String ftpUserName, String ftpPassword) {
		FTPClient ftpClient = new FTPClient();
		try {
			ftpClient = new FTPClient();
			ftpClient.connect(ftpHost);
			ftpClient.login(ftpUserName, ftpPassword);
			if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {
				System.out.println("Can not connect to FTP.");
				ftpClient.disconnect();
			} else {
				System.out.println("FTP connect successful.");
			}
		} catch (SocketException e) {
			e.printStackTrace();
			System.out.println("FTP's IP address may be wrong, please configure it correctly.");
		} catch (IOException e) {
			e.printStackTrace();
		}
		return ftpClient;
	}

	public static void downloadFtpFile(String ftpHost, String ftpUserName, String ftpPassword, String ftpPath,
			String localPath, String fileName) {

		FTPClient ftpClient = null;
		try {
			ftpClient = getFTPClient(ftpHost, ftpUserName, ftpPassword);
			ftpClient.setControlEncoding("UTF-8"); // 中文支持
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
			ftpClient.enterLocalPassiveMode();
			ftpClient.changeWorkingDirectory(ftpPath);

			File localDir = new File(localPath);
			if(!localDir.exists()) {
				localDir.mkdirs();
			}
			File localFile = new File(localPath + fileName);
			if(!localFile.exists()) {
				localFile.createNewFile();
			}
			OutputStream os = new FileOutputStream(localFile);
			ftpClient.retrieveFile(fileName, os);
			os.close();
			ftpClient.logout();
			System.out.println("down load successful.");
		} catch (FileNotFoundException e) {
			System.out.println("This file was not found : "+fileName);
			e.printStackTrace();
		} catch (SocketException e) {
			System.out.println("Can not connect this FTP.");
			e.printStackTrace();
		} catch (IOException e) {
			System.out.println("Read file error.");
			e.printStackTrace();
		}
	}
}
