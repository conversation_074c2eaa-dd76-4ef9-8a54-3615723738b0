package com.bees360.util.file;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.core.util.FileUtils;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class SizeReduceComposite implements SizeReduce {

    private Map<String, SizeReduce> typeMap = new ConcurrentHashMap<>();

    public SizeReduceComposite() {}

    public void add(String type, SizeReduce sizeReduce) {
        typeMap.put(StringUtils.upperCase(type), sizeReduce);
    }

    @Override
    public boolean reduce(File input, File out, int limitSize) throws IOException {
        String type = StringUtils.upperCase(FileUtils.getFileExtension(input));
        if (typeMap.containsKey(type)) {
            return typeMap.get(type).reduce(input, out, limitSize);
        }
        return false;
    }

    @Override
    public boolean reduce(File input, OutputStream out, int limitSize) throws IOException {
        String type = StringUtils.upperCase(FileUtils.getFileExtension(input));
        if (typeMap.containsKey(type)) {
            return typeMap.get(type).reduce(input, out, limitSize);
        }
        return false;
    }
}
