package com.bees360.util;

import java.io.File;
import java.io.IOException;

import com.bees360.util.file.HttpDownloader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IpWorkerCN extends IpWorker{
	private Logger logger = LoggerFactory.getLogger(IpWorkerCN.class);

	private static IpWorkerCN cnIpWorkerInstance = null;

	protected final String APNIC_IP_FILE = FileManager.APNIC_IP_FILE;
	protected final String APNIC_IP_INT_FILE = FileManager.APNIC_IP_INT_FILE;

	public static IpWorkerCN getInstance() {
		if (cnIpWorkerInstance == null) {
			synchronized (IpWorkerCN.class) {
				if (cnIpWorkerInstance == null) {
					cnIpWorkerInstance = new IpWorkerCN();
					cnIpWorkerInstance.init();
				}
			}
		}
		return cnIpWorkerInstance;
	}

	public boolean isCNIp(String ip) {
		return isInRanges(ip);
	}

	@Override
	protected void init(){
		File ipsFile = new File(APNIC_IP_FILE);
		if(!ipsFile.exists()){
			if(!downloadApnicIpFile()){
				logger.info("Fail to download apnic-ip-file");
				// keep the original ipRanges
				return;
			}
			logger.info("Download apnic-ip-file successfully");
		}

		String prefix = "apnic|CN|ipv4|";
		initIpRanges(ipsFile, prefix);
	}

	protected boolean downloadApnicIpFile(){
		String urlString = "http://ftp.apnic.net/apnic/stats/apnic/delegated-apnic-latest";
		try {
			HttpDownloader.download(urlString, APNIC_IP_FILE);
			return true;
		} catch (IOException e) {
			logger.error("Fail to download apnic-ip file from: " + urlString, e);
		}
		return false;
	}
}
