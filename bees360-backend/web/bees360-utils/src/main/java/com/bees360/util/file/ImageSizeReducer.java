package com.bees360.util.file;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Set;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;

import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;

import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.StringUtils;

/**
 * 通过 Thumbnails 实现的图片缩小，但执行效率比较慢。将16MB的图片缩小到15MB以下需要花费差不多30秒。
 *
 * <AUTHOR>
 */
public class ImageSizeReducer implements SelectedSizeReduce {

    private static final Set<String> TYPES_SUPPORTED = Sets.newHashSet("JPG", "JPEG", "PNG");

    /**
     * 得到更加理想的大小，但会消耗更多内存和需要更长时间
     */
    private boolean quality;

    public ImageSizeReducer() {
        this(false);
    }

    public ImageSizeReducer(boolean quality) {
        this.quality = quality;
    }

    @Override
    public boolean reduce(File input, File out, int limitSize) throws IOException {
        Preconditions.checkArgument(limitSize > 0, "Arg `limitSize` should be positive.");
        try (OutputStream outputStream = FileUtils.openOutputStream(out)) {
            return reduce(input, outputStream, limitSize);
        }
    }

    @Override
    public boolean reduce(File input, OutputStream out, int limitSize) throws IOException {
        Preconditions.checkArgument(limitSize > 0, "Arg `limitSize` should be positive.");
        long fileLength = input.length();
        if (fileLength <= limitSize) {
            FileUtils.copyFile(input, out);
            return true;
        }
        byte[] data = null;
        if (quality) {
            try (ByteArrayOutputStream outstream = new ByteArrayOutputStream()) {
                // 抹去图片信息
                Thumbnails.of(input).scale(1.0).useOriginalFormat().toOutputStream(outstream);
                if (outstream.size() <= limitSize) {
                    IOUtils.write(outstream.toByteArray(), out);
                    return true;
                }
                data = outstream.toByteArray();
                fileLength = data.length;
            }
        }
        final InputStream inputStream =
            data == null ? FileUtils.openInputStream(input) : new ByteArrayInputStream(data);
        try (inputStream) {
            // 等比缩小
            reduce(inputStream, out, fileLength, limitSize);
        }
        return true;
    }

    private double reduce(InputStream input, OutputStream out, long inputLeSize, int limitSize) throws IOException {
        double scale = calculateScale(inputLeSize, limitSize);
        Thumbnails.of(input).scale(scale).useOriginalFormat().toOutputStream(out);
        return scale;
    }

    /**
     * scale 是边缩小的比例，如果要将大小缩小1/n，则表示边要: limitSize = fileLength * 1 / n = perPixelSize * wide * high * 1 / n =
     * perPixelSize * (wide * 1 / 根号n) * (high * 1 / 根号n)
     *
     * 这是比较理想的情况，但有图图片处理像素点，还有其他附加信息，在转的过程中，会丢失附加信息，从而得到的为非理想最大值
     */
    private double calculateScale(long fileLength, int limitSize) {
        return Math.sqrt(limitSize * 1.0 / fileLength);
    }

    @Override
    public boolean support(File input) {
        String extension = FilenameUtils.getExtension(input.getName());
        return TYPES_SUPPORTED.contains(StringUtils.upperCase(extension));
    }
}
