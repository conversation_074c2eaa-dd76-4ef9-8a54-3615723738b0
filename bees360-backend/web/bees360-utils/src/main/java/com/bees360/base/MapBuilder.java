package com.bees360.base;

import java.util.HashMap;
import java.util.Map;

public class MapBuilder {

	public static class ResultMap {
		private Map<String, Object> result;

		public ResultMap() {
			result = new HashMap<String, Object>();
		}

		public ResultMap put(String key, Object value) {
			result.put(key, value);
			return this;
		}

		public Map<String, Object> result() {
			return result;
		}
	}

	/**
	 * don't instantiate this class.
	 */
	private MapBuilder() {
		throw new AssertionError();
	}

	public static ResultMap put(String key, Object value) {
		ResultMap resultMap = new ResultMap();
		return resultMap.put(key, value);
	}

	public static Map<String, Object> result(String key, Object value) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put(key, value);
		return result;
	}
}
