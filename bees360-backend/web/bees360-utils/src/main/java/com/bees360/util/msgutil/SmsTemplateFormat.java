package com.bees360.util.msgutil;

import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.util.Properties;
import java.util.ArrayList;
import java.util.List;

public class SmsTemplateFormat {

	private Properties templates;

	public SmsTemplateFormat() {
		this.templates = new Properties();
	}

	public SmsTemplateFormat(String propertiesFileName) {
		setSourceProperties(propertiesFileName);
	}

	public SmsTemplateFormat(Properties templates) {
		this.templates = templates;
	}

	public void setSourceProperties(String propertiesFileName) {
		this.templates = getProperties(propertiesFileName);
	}

	private Properties getProperties(String propertiesFileName) {
		Properties properties = new Properties();
		try (InputStream is = SmsTemplateFormat.class.getClassLoader().getResourceAsStream(propertiesFileName)) {
			properties.load(is);
		} catch (IOException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
		return properties;
	}

	public String getContent(String templateName) {
		if(!templates.containsKey(templateName)) {
			throw new IllegalArgumentException("Template Name '" + templateName + "' isn't a valid value.");
		}
		return templates.getProperty(templateName);
	}

	public String getContent(String templateName, Object[] params) {
		if(!templates.containsKey(templateName)) {
			throw new IllegalArgumentException("Template Name '" + templateName + "' isn't a valid value.");
		}
		String templateContent = templates.getProperty(templateName);
		if(params == null || params.length == 0) {
			return templateContent;
		}
		return MessageFormat.format(templateContent, params);
	}

	public List<String> listTemplateName() {
		return new ArrayList<String>(templates.stringPropertyNames());
	}

	public boolean containTemplate(String templateName) {
		return templates.containsKey(templateName);
	}

	public String getTemplate(String templateName) {
		return templates.getProperty(templateName);
	}
}
