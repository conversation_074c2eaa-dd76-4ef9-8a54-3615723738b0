package com.bees360.util.user;

import com.bees360.entity.Member;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.user.Message;
import com.bees360.user.User;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class Bees360UserUtils {

    private static final Pattern DIGITAL_ID = Pattern.compile("\\d{1,11}");

    public static final String FIREBASE_USER_PROVIDER_PREFIX = "FB:";

    public static final Long MAX_VALUE_OF_WEB_ID = 10000000L;

    public static UserTinyVo toUserTinyVo(User user, RoleEnum roleEnum) {
        if (user == null) {
            return null;
        }
        String id = user.getId();
        if (id == null) {
            throw new IllegalArgumentException("User id is null");
        }
        com.bees360.entity.User webUser = UserAssemble.toWebUser(user);
        return new UserTinyVo(webUser, roleEnum);
    }

    public static boolean isDigitalId(String id) {
        return DIGITAL_ID.matcher(id).matches();
    }

    public static boolean isWebUserId(String id) {
        return isDigitalId(id) && Long.parseLong(id) < MAX_VALUE_OF_WEB_ID;
    }

    public static String removePrefix(String id) {
        if (id == null) {
            return null;
        }
        int i = id.indexOf(":");
        if (i > 0) {
            return id.substring(i + 1);
        }
        return id;
    }

    public static Map<String, List<Integer>> memberRolesMap(List<Member> members) {
        return members.stream()
                .collect(
                        Collectors.toMap(
                                member -> member.getUserId() + "",
                                member -> {
                                    ArrayList<Integer> roles = new ArrayList<>();
                                    Integer role = member.getRole();
                                    roles.add(role);
                                    return roles;
                                },
                                (list, list1) -> {
                                    list.addAll(list1);
                                    return list;
                                }));
    }

    public static List<UserTinyVo> toUserTinyVo(
            List<? extends User> users, Map<String, List<Integer>> memberRolesMap) {
        return users.stream()
                .collect(Collectors.toMap(User::getId, Function.identity(), (k1, k2) -> k1))
                .values()
                .stream()
                .flatMap(
                        user -> {
                            String userId = user.getId();
                            var webUser = UserAssemble.toWebUser(user);
                            if (!memberRolesMap.containsKey(userId)) {
                                return null;
                            }
                            return memberRolesMap.get(userId).stream()
                                    .map(role -> new UserTinyVo(webUser, RoleEnum.getEnum(role)));
                        })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
