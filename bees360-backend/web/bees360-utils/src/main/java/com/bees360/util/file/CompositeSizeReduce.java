package com.bees360.util.file;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Map;

import com.google.common.base.Preconditions;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.core.util.FileUtils;

import com.google.common.collect.Maps;

import jakarta.annotation.Nonnull;

/**
 * <AUTHOR>
 */
public class CompositeSizeReduce implements SizeReduce {

    private Map<String, SizeReduce> typeMap;

    public CompositeSizeReduce() {
        typeMap = Maps.newHashMap();
    }

    public CompositeSizeReduce(Map<String, SizeReduce> typeMap) {
        this.typeMap = typeMap;
    }

    public CompositeSizeReduce add(String type, @Nonnull SizeReduce sizeReduce) {
        Preconditions.checkArgument(sizeReduce != null, "The param `sizeReduce` shouldn't be null.");
        typeMap.put(StringUtils.upperCase(type), sizeReduce);
        return this;
    }

    private SizeReduce getSizeReduce(File file) {
        String extension = FileUtils.getFileExtension(file);
        return typeMap.get(StringUtils.upperCase(extension));
    }

    @Override
    public boolean reduce(File input, File out, int limitSize) throws IOException {
        SizeReduce sizeReduce = getSizeReduce(input);
        return sizeReduce == null ? false : sizeReduce.reduce(input, out, limitSize);
    }

    @Override
    public boolean reduce(File input, OutputStream out, int limitSize) throws IOException {
        SizeReduce sizeReduce = getSizeReduce(input);
        return sizeReduce == null ? false : sizeReduce.reduce(input, out, limitSize);
    }
}
