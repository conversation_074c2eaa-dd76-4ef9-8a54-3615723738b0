package com.bees360.util;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;

import com.bees360.entity.enums.RoleEnum;

public class AuthorityUtil {
	private static Map<Integer, String[]> rolesWithModules;
	private static final String MODULES_PROPERTIES = "settings/accessable-modules.properties";

	static {
		init();
	}

	private static void init() {
		Properties props = PropertiesUtils.getResourcesProperties(MODULES_PROPERTIES);
		rolesWithModules = new HashMap<Integer, String[]>();
		for(RoleEnum role: RoleEnum.values()) {
			String modules = props.getProperty(role.getCode() + "");
			if(modules == null) {
				rolesWithModules.put(role.getCode(), new String[]{});
				continue;
			}
			modules = modules.trim();
			if(modules.length() == 0) {
				rolesWithModules.put(role.getCode(), new String[]{});
				continue;
			}
			rolesWithModules.put(role.getCode(), modules.split(","));
		}
	}

	public static List<String> listAccessableModules(RoleEnum role) {
		if(role == null) {
			return new ArrayList<String>();
		}
		return listAccessableModules(role.getCode());
	}

	public static List<String> listAccessableModules(int role) {
		String[] modules = rolesWithModules.get(role);
		return Arrays.asList(Arrays.copyOf(modules, modules.length));
	}

	public static void main(String[] args) {

	}
}
