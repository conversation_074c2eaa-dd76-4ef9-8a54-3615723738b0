package com.bees360.util.file;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringTool {

	public static List<Integer> extractInts(String line, String splitRegx) {
		return extractInts(line, splitRegx, null);
	}

	public static List<Integer> extractInts(String line, String splitRegx, Set<Integer> range) {
		if(line == null) {
			return new ArrayList<Integer>();
		}
		List<Integer> ints = new ArrayList<Integer>();
		String[] parts = line.split(splitRegx);
		for(String part: parts){
			try {
				Integer num = Integer.parseInt(part);
				if(range != null && range.size() > 0){
					if(range.contains(num)) {
						ints.add(num);
					}
				} else {
					ints.add(num);
				}
			} catch (Exception e) {
				continue;
			}
		}
		return ints;
	}

	public static List<Integer> extractInts(String line) {
		return extractInts(line, new HashSet<Integer>());
	}

	public static List<Integer> extractInts(String line, Set<Integer> range) {
		if(line == null) {
			return new ArrayList<Integer>();
		}
		List<Integer> ints = new ArrayList<Integer>();
		Pattern p = Pattern.compile("[0-9]+");
		Matcher m = p.matcher(line);
		while(m.find()) {
			int i = Integer.parseInt(m.group());
			if(range != null && range.size() > 0){
				if(range.contains(i)) {
					ints.add(i);
				}
			} else {
				ints.add(i);
			}
		}
		return ints;
	}

	public static List<Long> extractLongs(String line) {
		return extractLongs(line, new HashSet<Long>());
	}

	public static List<Long> extractLongs(String line, Set<Long> range) {
		if(line == null) {
			return new ArrayList<Long>();
		}
		List<Long> longs = new ArrayList<Long>();
		Pattern p = Pattern.compile("[0-9]+");
		Matcher m = p.matcher(line);
		while(m.find()) {
			long num = Long.parseLong(m.group());
			if(range != null && range.size() > 0){
				if(range.contains(num)) {
					longs.add(num);
				}
			} else {
				longs.add(num);
			}
		}
		return longs;
	}
}
