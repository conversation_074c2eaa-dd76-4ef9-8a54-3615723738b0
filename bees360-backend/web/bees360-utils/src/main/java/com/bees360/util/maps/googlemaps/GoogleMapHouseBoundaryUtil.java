package com.bees360.util.maps.googlemaps;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.imageio.ImageIO;

import com.bees360.util.maps.common.MapCommonUtil;
import com.bees360.util.maps.common.MercatorProjection;
import com.bees360.util.maps.model.LatLngPoint;
import com.bees360.util.maps.model.Pixel;
import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.StaticMapsRequest.ImageFormat;
import com.google.maps.StaticMapsRequest.StaticMapType;
import com.google.maps.errors.ApiException;
import com.google.maps.model.GeocodingResult;
import com.google.maps.model.LatLng;
import com.google.maps.model.Size;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * google map document: https://developers.google.com/maps/documentation/
 * google geocoding api:
 * google static map api: https://developers.google.com/maps/documentation/maps-static/intro
 *
 * The image get from API http://maps.googleapis.com/maps/api/staticmap?
 * and parameters following:
 * maptype=roadmap&zoom=YOUR_ZOOM&scale=YOUR_SCALA&center=YOUR_ADDRESS&size=YOUR_WIDTHxYOUR_HEIGH
 * &format=png32&style=feature:all|visibility:off
 * &style=feature:landscape.man_made|element:geometry.stroke|visibility:on|color:0x000000
 *
 * call google map rest API
 * <AUTHOR>
 *
 */
public class GoogleMapHouseBoundaryUtil extends MapProjectionUtil {

    private static final Logger logger = LoggerFactory.getLogger(GoogleMapHouseBoundaryUtil.class);

    private static final int DEFAULT_WIDTH = 300;
    private static final int DEFAULT_HEIGHT = 300;
    private static final int DEFAULT_ZOOM = 19;
    private static final int DEFAULT_SCALA = 1;

    private static int GOOGLE_TILE_SIZE = 256;

	/**
	 * <code>zoom</code> (required if markers not present) defines the zoom
	 * level of the map, which determines the magnification level of the map.
	 *
	 * 1: World, 5: Landmass/continent, 10: City, 15: Streets, 20: Buildings You
	 * can see more detail from
	 * https://developers.google.com/maps/documentation/maps-static/dev-guide#Zoomlevels
	 */
	private int zoom;
	/**
	 * affects the number of pixels that are returned. scale=2
	 * returns twice as many pixels as scale=1. The default value is 1. Accepted
	 * values are 2 and 4 (4 is only available to Google Maps APIs Premium Plan
	 * customers.)
	 *
	 * more information:
	 * https://developers.google.com/maps/documentation/maps-static/dev-guide#scale_values
	 */
	private int scala;
	// the default image width
	private int width;
	// the default image height
	private int heigh;

	private final GeoApiContext context;

	public GoogleMapHouseBoundaryUtil(GeoApiContext geoApiContext) {
        this(geoApiContext, DEFAULT_WIDTH, DEFAULT_HEIGHT, DEFAULT_ZOOM, DEFAULT_SCALA);
    }

	public GoogleMapHouseBoundaryUtil(GeoApiContext geoApiContext, int width, int heigh, int zoom, int scala) {
	    super(width, heigh, zoom, GOOGLE_TILE_SIZE);
		this.width = width;
		this.heigh = heigh;
		this.zoom = zoom;
		this.scala = scala;
		this.context = geoApiContext;
	}

	/**
	 * Save a google map static image.
	 * The image is A transparent image containing only the sides of the roof of the house.
	 *
	 * @param address the address need to be search
	 * @param destFile the path of the image which will use to save the result
	 * @return
	 */
	public LatLngPoint saveImage(String address, String destFile) {
		LatLngPoint latlng = geocoding(address);

		if(latlng == null) {
			return null;
		}
		if(!saveImage(latlng.getLat(), latlng.getLng(), destFile)) {
			return null;
		}
		return latlng;
	}

	public LatLngPoint geocoding(String address) {
		GeocodingResult[] results;
		try {
			results = GeocodingApi.newRequest(context).address(address).await();
		} catch (ApiException | InterruptedException | IOException e) {
			logger.error("GoogleMapHouseBoundaryUtil.saveImage failed:" + e.getMessage(),e);
			return null;
		}
		if(results == null || results.length == 0) {
			return null;
		}
		LatLng latLng = results[0].geometry.location;
		return new LatLngPoint(latLng.lat, latLng.lng);
	}

	/**
	 * save the image to the destFile.
	 * If any error occurs during the request, a transparent image is returned.
	 *
	 * @param staticMapsRequest
	 * @param destFile
	 * @return true if the image generated, false when it fail to fetch the image
	 */
	private boolean saveImage(ExtendStaticMapsRequest staticMapsRequest, String destFile) {
		try {
			ByteArrayInputStream bais = new ByteArrayInputStream(staticMapsRequest.await().imageData);
			BufferedImage bufferedImage = ImageIO.read(bais);
			String fileExt = getFileExtension(destFile, "PNG");
			ImageIO.write(bufferedImage, fileExt, new File(destFile));
			return true;
		} catch (Exception e) {
			logger.error("GoogleMapHouseBoundaryUtil.saveImage failed:" + e.getMessage(), e);
			e.printStackTrace();
			return false;
		}
	}

	/**
	 *
	 * @param lat Latitudes can take any value between -90 and 90
	 * @param lng Longitude values can take any value between -180 and 180
	 * @param destFile
	 * @return if the image generated, false when it fail to fetch the image
	 */
	public boolean saveImage(double lat, double lng, String destFile) {
		if(!MapCommonUtil.checkLatLng(lat, lng)) {
			return false;
		}
		ExtendStaticMapsRequest staticMapsRequest = generateStaticMapsRequest();
		staticMapsRequest.center(new LatLng(lat, lng));

		return saveImage(staticMapsRequest, destFile);
	}

	private String getFileExtension(String filePath, String defaultExt) {
		int lastP = filePath.lastIndexOf(".");
		if(lastP < 0) {
			return defaultExt;
		} else {
			return filePath.substring(lastP + 1);
		}
	}

	/**
	 *
	 * you can try in https://developers.google.com/maps/documentation/javascript/examples/maptype-styled-simple
	 * @return
	 */
	private ExtendStaticMapsRequest generateStaticMapsRequest() {
		ExtendStaticMapsRequest staticMapsRequest = new ExtendStaticMapsRequest(context);
		staticMapsRequest.size(new Size(width, heigh));
		staticMapsRequest.maptype(StaticMapType.roadmap);
		staticMapsRequest.zoom(zoom);
		staticMapsRequest.scale(scala);
		staticMapsRequest.format(ImageFormat.png32);
		staticMapsRequest.style("feature:all|visibility:off");
		staticMapsRequest.style("feature:landscape.man_made|element:geometry.stroke|visibility:on|color:0xffffff|weight:2");

		return staticMapsRequest;
	}
}
