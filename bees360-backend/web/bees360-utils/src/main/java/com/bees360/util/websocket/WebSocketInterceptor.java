package com.bees360.util.websocket;

import java.util.Map;

import jakarta.servlet.http.HttpSession;

import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

public class WebSocketInterceptor implements HandshakeInterceptor{

	@Override
	public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
			WebSocketHandler handler, Exception arg3) {
		// TODO Auto-generated method stub

	}

	@Override
	/**
     * Before handshake created save the userId which is saved in Session into the websocketsession
     * @param request
     * @param response
     * @param handler
     * @param attributes Set the attributes of the webSock
     * @return
     */
	public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
			WebSocketHandler handler, Map<String, Object> attributes) throws Exception {
/*		if (request instanceof ServletServerHttpRequest) {
            ServletServerHttpRequest serverHttpRequest = (ServletServerHttpRequest) request;
            HttpSession session = serverHttpRequest.getServletRequest().getSession();
            if (session != null) {
//                attributes.put("userId",session.getAttribute("userId"));
            }
        }*/
        return true;
	}

}
