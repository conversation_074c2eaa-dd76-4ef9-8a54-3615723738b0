package com.bees360.util;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.Set;

import com.bees360.entity.OnsiteReportElement;

public class OnsiteReportElementTreeNode {

	public Long id;
	public OnsiteReportElement data;
	public List<OnsiteReportElementTreeNode> childNodes;
	public boolean selected;

	public OnsiteReportElementTreeNode() {
		this(null);
	}
	public OnsiteReportElementTreeNode(OnsiteReportElement data) {
		this(data, new ArrayList<OnsiteReportElementTreeNode>());
	}
	public OnsiteReportElementTreeNode(OnsiteReportElement data, List<OnsiteReportElementTreeNode> children) {
		this.id = (data == null? null: data.getId());
		this.data = data;
		this.childNodes = children;
	}

	public boolean isVirtualRoot() {
		return data == null;
	}

	/**
	 * build a element tree with eles. If the parent of the element dosen't exist in eles,
	 * it's parent will be set to be null value.
	 * @param eles
	 * @return
	 */
	public static OnsiteReportElementTreeNode elementsToTree(List<OnsiteReportElement> eles) {
		Iterator<OnsiteReportElement> iter = eles.iterator();
		MapList<Long, OnsiteReportElementTreeNode> elementMap = new MapList<Long, OnsiteReportElementTreeNode>();
		Set<Long> idSet = new HashSet<Long>();
		for(OnsiteReportElement ele: eles) {
			idSet.add(ele.getId());
		}
		while(iter.hasNext()) {
			OnsiteReportElement ele = iter.next();
			if(!idSet.contains(ele.getParentId())) {
				ele.setParentId(null);
			}
			Long parentId = ele.getParentId();
			if(elementMap.containsKey(parentId)) {
				elementMap.get(parentId).add(new OnsiteReportElementTreeNode(ele));
			} else {
				elementMap.add(parentId, new OnsiteReportElementTreeNode(ele));
			}
		}

		OnsiteReportElementTreeNode root = new OnsiteReportElementTreeNode();
		attachChildren(root, elementMap);
		return root;
	}

	private static void attachChildren(OnsiteReportElementTreeNode root, MapList<Long, OnsiteReportElementTreeNode> elementMap) {
		if(!elementMap.containsKey(root.id)) {
			return;
		}
		root.childNodes = elementMap.get(root.id);
		for(OnsiteReportElementTreeNode node: root.childNodes) {
			attachChildren(node, elementMap);
		}
	}

	public List<List<Long>> listLevelIds() {
		List<List<Long>> levels = new ArrayList<List<Long>>();
		List<OnsiteReportElementTreeNode> children = new ArrayList<OnsiteReportElementTreeNode>();
		if(this.isVirtualRoot()) {
			children.addAll(this.childNodes);
		} else {
			children.add(this);
		}
		while(!children.isEmpty()) {
			List<Long> curLevel = new ArrayList<Long>();
			List<OnsiteReportElementTreeNode> nextChildren = new ArrayList<OnsiteReportElementTreeNode>();
			for(OnsiteReportElementTreeNode node: children) {
				curLevel.add(node.id);
				nextChildren.addAll(node.childNodes);
			}
			levels.add(curLevel);
			children = nextChildren;
		}
		return levels;
	}

	public List<List<Long>> selectAllSubNodes(Set<Long> startIds) {
		List<OnsiteReportElementTreeNode> selectTree = new ArrayList<OnsiteReportElementTreeNode>();
		List<OnsiteReportElementTreeNode> children = new ArrayList<OnsiteReportElementTreeNode>();
		if(this.isVirtualRoot()) {
			children.addAll(this.childNodes);
		} else {
			children.add(this);
		}

		while(!children.isEmpty()) {
			List<OnsiteReportElementTreeNode> nextChildren = new ArrayList<OnsiteReportElementTreeNode>();
			for(OnsiteReportElementTreeNode node: children) {
				if(startIds.contains(node.id)) {
					selectTree.add(node);
					continue;
				}
				nextChildren.addAll(node.childNodes);
			}
			children = nextChildren;
		}
		OnsiteReportElementTreeNode newRoot = new OnsiteReportElementTreeNode(null, selectTree);
		return newRoot.listLevelIds();
	}

	public List<String> showTree() {
		List<String> tree = new ArrayList<String>();
		Queue<OnsiteReportElementTreeNode> queue = new LinkedList<OnsiteReportElementTreeNode>();
		queue.add(this);
		while(!queue.isEmpty()) {
			OnsiteReportElementTreeNode curNode = queue.poll();
			List<String> children = new ArrayList<String>();
			for(OnsiteReportElementTreeNode child: curNode.childNodes) {
				children.add(child.id + "");
				queue.offer(child);
			}
			tree.add(curNode.id + "->" + String.join(",", children));
		}
		return tree;
	}

	public static void main(String[] args) {
		List<OnsiteReportElement> eles = new ArrayList<OnsiteReportElement>();
		OnsiteReportElement ele = new OnsiteReportElement(); ele.setId(1); ele.setParentId(null);
		eles.add(ele);
		ele = new OnsiteReportElement(); ele.setId(2); ele.setParentId(1L);
		eles.add(ele);
		ele = new OnsiteReportElement(); ele.setId(3); ele.setParentId(1L);
		eles.add(ele);
		ele = new OnsiteReportElement(); ele.setId(4); ele.setParentId(3L);
		eles.add(ele);

		ele = new OnsiteReportElement(); ele.setId(5); ele.setParentId(null);
		eles.add(ele);
		ele = new OnsiteReportElement(); ele.setId(6); ele.setParentId(5L);
		eles.add(ele);
		ele = new OnsiteReportElement(); ele.setId(7); ele.setParentId(6L);
		eles.add(ele);
		ele = new OnsiteReportElement(); ele.setId(8); ele.setParentId(6L);
		eles.add(ele);
		ele = new OnsiteReportElement(); ele.setId(9); ele.setParentId(8L);
		eles.add(ele);
		ele = new OnsiteReportElement(); ele.setId(10); ele.setParentId(5L);
		eles.add(ele);

		OnsiteReportElementTreeNode tree = OnsiteReportElementTreeNode.elementsToTree(eles);

		for(String line: tree.showTree()) {
			System.out.println(line);
		}

		Set<Long> selectSet = new HashSet<Long>();
		selectSet.add(3L);
		selectSet.add(6L);
		selectSet.add(8L);
		for(List<Long> ids: tree.listLevelIds()) {
			System.out.println(ids);
		}
		for(List<Long> ids: tree.selectAllSubNodes(selectSet)) {
			System.out.println(ids);
		}
	}
}
