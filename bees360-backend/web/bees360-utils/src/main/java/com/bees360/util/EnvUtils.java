package com.bees360.util;

import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/04/11 10:24
 */
public class EnvUtils {

    /**
     *  将system的env值或者property的相应值 设置到Spring变量当中，增加服务器容错机制
     *  给定默认这样开发环境可以不需要配置系统变量即可启动当前web程序, stag和prod需要做system key-value的服务器配置
     *  在启动程序里调用args赋值 args = EnvUtils.initEnvFromSystem(args, ENV_SYSTEM", "--spring.profiles.active=", "dev");
     * @param args
     * @param systemEnvKey   eg: ENV_SYSTEM  env的系统变量key值
     * @param programEnvKey -eg:  "-spring.profiles.active= "  spring config key
     * @param defaultEnv  dev
     * @return
     */
    public static String[] initEnvFromSystem(String[] args, String systemEnvKey, String programEnvKey, String defaultEnv) {
        String env = System.getenv(systemEnvKey);
        env = StringUtils.isNotBlank(env) ? env : System.getProperty(systemEnvKey);
        boolean hasEnv = false;
        for (int i = 0; i < args.length; i++) {
            if (args[i].contains(programEnvKey)) {
                hasEnv = true;
                if (StringUtils.isNotBlank(env)) {
                    args[i] = programEnvKey + env;
                }
            }
        }
        env = StringUtils.isNotBlank(env) ? env : defaultEnv;
        if (!hasEnv) {
            String[] newArgs = Arrays.copyOf(args, args.length + 1);
            newArgs[args.length] = programEnvKey + env;
            args = newArgs;
        }
        return args;
    }

    public static void initLog4jSystemProperties(Optional<String> appName, Optional<String> hostName,
                                                 Optional<String> hostIpAddr, Optional<String> currentDate) throws Exception {
        System.setProperty("springAppName", appName.orElse("bees360"));
        System.setProperty("localhostName", hostName.orElse(getLocalHostName()));
        System.setProperty("localIp", hostIpAddr.orElse(getLocalIp()));
        System.setProperty("currentDate", currentDate.orElse(DateUtil.getCurrentDate("yyyyMMdd'T'HHmmssSSS'Z'", ZoneOffset.UTC)));

    }

    public static String getLocalIp() throws Exception {
        return InetAddress.getLocalHost().getHostAddress();
    }

    public static String getLocalHostName() throws Exception {
        return InetAddress.getLocalHost().getHostName();
    }



}
