package com.bees360.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class Unit {
	public static enum LengthUnit implements BaseUnit<LengthUnit>{
		METER("meter", 1D),
		FOOT("ft", 0.3048D)
		;

		private final String display;
		private final double toReference;
		LengthUnit(String display, double toReference) {
			this.display = display;
			this.toReference = toReference;
		}
		@Override
		public double toReference() {
			return toReference;
		}
		@Override
		public String getDisplay() {
			return display;
		}
	}

	public static enum AreaUnit implements BaseUnit<AreaUnit>{
		SQUARE_METER("sqmt", 1D),
		SQUARE_FOOT("sqft", 0.09290304D)
		;

		private final String display;
		private final double toReference;
		AreaUnit(String display, double toReference) {
			this.display = display;
			this.toReference = toReference;
		}
		@Override
		public double toReference() {
			return toReference;
		}
		@Override
		public String getDisplay() {
			return display;
		}
	}

	interface BaseUnit<U extends BaseUnit<U>> {
		String getDisplay();
		double toReference();
	}

	public static <U extends Enum<?> & BaseUnit<U>> U toUnit(Class<U> enumClass, String display) {
		if(display == null) {
			return null;
		}
		U[] enumConstants = enumClass.getEnumConstants();
		for(U u: enumConstants){
			if(display.equals(u.getDisplay())){
				return u;
			}
		}
		return null;
	}

	public static <U extends Enum<?> & BaseUnit<U>> double convert(double value, U from, U to){
		return value * convert(from, to);
	}

	public static <U extends Enum<?> & BaseUnit<U>> double convert(U from, U to){
		BigDecimal r1 = new BigDecimal(Double.toString(from.toReference()));
		BigDecimal r2 = new BigDecimal(Double.toString(to.toReference()));
		return r1.divide(r2, 24, RoundingMode.HALF_UP).doubleValue();
	}

	public static void main(String[] args) {
		Unit.AreaUnit unit = Unit.toUnit(Unit.AreaUnit.class, "sqmt");
		System.out.println(unit);
		for(LengthUnit from: LengthUnit.values()) {
			for(LengthUnit to: LengthUnit.values()) {
				System.out.println("1 " + from.getDisplay() + "\t=\t" + Unit.convert(from, to) + " " + to.getDisplay());
			}
		}
		for(AreaUnit from: AreaUnit.values()) {
			for(AreaUnit to: AreaUnit.values()) {
				System.out.println("1 " + from.getDisplay() + "\t=\t" + Unit.convert(from, to) + " " + to.getDisplay() );
			}
		}
	}
}
