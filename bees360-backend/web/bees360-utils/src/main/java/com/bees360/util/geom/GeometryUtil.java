package com.bees360.util.geom;

import com.bees360.entity.dto.Point;

import java.awt.*;
import java.awt.geom.Area;
import java.awt.geom.Rectangle2D;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

public class GeometryUtil {

	/**
	 *
	 * @param geomText a string format like "POINT(-1 1)", "LINESTRING(0 0,1 1,2 2)" or "POLYGON((1 1,8 9,0 9,1 1))".
	 * 			Make sure that POLYGON must have only one ring.
	 * @return
	 */
	public static List<Point> parseGeometryText(String geomText){
		if(geomText == null){
			return null;
		}
		// <EMAIL>: should I throw exception if the geomText has a wrong format.
		List<Point> points = new ArrayList<Point>();
		// index of first digit
		int left = 0;
		while(left < geomText.length() && !Character.isDigit(geomText.charAt(left)) && geomText.charAt(left) != '-'){
			left ++;
		}
		// index of last digit
		int right = geomText.length() - 1;
		while(0 <= right && !Character.isDigit(geomText.charAt(right))){
			right --;
		}
		String pointString = geomText.substring(left, right + 1);
		String[] numberPairs = pointString.split(",");
		for(String numPair: numberPairs){
			Point point = new Point();
			String[] nums = numPair.trim().split("\\s+");
			point.setX(Double.parseDouble(nums[0]));
			point.setY(Double.parseDouble(nums[1]));
			points.add(point);
		}
		return points;
	}

	public static String polygonToText(List<Point> points) {
		if(points == null || points.size() == 0){
			return null;
		}
		StringBuilder polygon = new StringBuilder("POLYGON((");
		for(int i = 0; i < points.size(); i ++){
			Point p = points.get(i);
			polygon.append(p.getX() + " " + p.getY());
			if(i < points.size() - 1){
				polygon.append(",");
			}
		}
		Point firstPoint = points.get(0);
		Point lastPoint = points.get(points.size() - 1);
		if(firstPoint.getX() != lastPoint.getX() || firstPoint.getY() != lastPoint.getY()){
			// The last point is not same as first one. Append the first point to the list.
			polygon.append("," + firstPoint.getX() + " " + firstPoint.getY());
		}
		polygon.append("))");
		return polygon.toString();
	}

	public static Point calculateCentertPoint(List<Point> points) {
        if (points == null || points.size() == 0) {
            return null;
        }
        HashSet<Point> distinctPoints = new HashSet<>(points);
        double x = 0;
        double y = 0;
        for (Point p : distinctPoints) {
            x += p.getX();
            y += p.getY();
        }
        x = x / distinctPoints.size();
        y = y / distinctPoints.size();
        return new Point(x, y);
    }

	/**
	 *
	 * @param polygon1
	 * @param polygon2
	 * @return
	 */
	public static boolean retangleIntersect(List<Point> polygon1, List<Point> polygon2) {
		// Magnify data to reduce errors
		int scaling = 1000;
		Polygon p1 = generatePolygon(polygon1, scaling);
		Polygon p2 = generatePolygon(polygon2, scaling);

		Rectangle2D rectangle1 = new Area(p1).getBounds2D();
		Rectangle2D rectangle2 = new Area(p2).getBounds2D();
		return rectangle1.intersects(rectangle2);
	}

	/**
	 * judge the polygon1 and polygon2 is intersecting or not, both of which is a points list.
	 * @param polygon1 a points list
	 * @param polygon2 a points list
	 * @return
	 */
	public static boolean isIntersect(List<Point> polygon1, List<Point> polygon2) {
		// Magnify data to reduce errors
		int scaling = 1000;
		Polygon p1 = generatePolygon(polygon1, scaling);
		Polygon p2 = generatePolygon(polygon2, scaling);

		Area area = new Area(p1);
		area.intersect(new Area(p2));
		return !area.isEmpty();
	}

	private static Polygon generatePolygon(List<Point> polygonPoints, int scaling) {
		int[] xpoints = new int[polygonPoints.size()];
		int[] ypoints = new int[polygonPoints.size()];
		int npoint = polygonPoints.size();
		for(int i = 0; i < npoint; i ++) {
			xpoints[i] = (int) (scaling * polygonPoints.get(i).getX());
			ypoints[i] = (int) (scaling * polygonPoints.get(i).getY());
		}
		return new Polygon(xpoints, ypoints, npoint);
	}
}
