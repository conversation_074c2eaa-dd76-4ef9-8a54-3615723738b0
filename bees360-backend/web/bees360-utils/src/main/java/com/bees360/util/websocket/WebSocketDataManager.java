package com.bees360.util.websocket;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

@Component("webSockeDataManager")
public class WebSocketDataManager {


	//WebScocket Session Pool
    private static final Map<String, WebSocketSession> webSocketSessions;
    //One userId relate some jtis
    private static final Map<Long, CopyOnWriteArrayList<String>> userIdJti;
    //One jti relate some websocket sessions
    private static final Map<String, CopyOnWriteArrayList<String>> jtiSessionId;
    //Symbol of the userId
    private static final String USER_ID = "userId";

    private static final String JTI = "jti";

    private static Logger logger = LoggerFactory.getLogger(WebSocketHandler.class);

    static {
        webSocketSessions = new ConcurrentHashMap<String, WebSocketSession>();
        userIdJti = new ConcurrentHashMap<Long, CopyOnWriteArrayList<String>>();
        jtiSessionId = new ConcurrentHashMap<String, CopyOnWriteArrayList<String>>();
    }

   /* public ArrayList<String> listAllUserId(){
    	return
    }*/

    /**
     * Save one session into the connection pool.
     * @param session	The session which is saved.
     * @param jti	Use to differentiate different jti from the map jtiSessionId.
     * @param userId	Use to differentiate different user from the map webSocketSessions.
     * @return
     */
    public void saveOneSession(WebSocketSession session, String jti, Long userId) throws Exception{
		String sessionId = session.getId();
		if(userIdJti.containsKey(userId)){
			if(!userIdJti.get(userId).contains(jti)){
				userIdJti.get(userId).add(jti);
			}
		}else{
			CopyOnWriteArrayList<String> jtiList = new CopyOnWriteArrayList<String>(new ArrayList<String>());
			jtiList.add(jti);
			userIdJti.put(userId, jtiList);
		}
		if(jtiSessionId.containsKey(jti)){
			jtiSessionId.get(jti).add(sessionId);
		}else{
			CopyOnWriteArrayList<String> sessionIdList = new CopyOnWriteArrayList<String>(new ArrayList<String>());
			sessionIdList.add(sessionId);
			jtiSessionId.put(jti, sessionIdList);
		}
		webSocketSessions.put(sessionId, session);
		session.getAttributes().put("jti", jti);
		session.getAttributes().put("userId", userId);
		SocketMsg msg = new SocketMsg("Authentication passed! The connection saved!");
		session.sendMessage(msg.textMessage());
    }

    /**
     * Get the websocket session by the sessionId.
     * @param sessionId
     * @return	A websocket session.
     */
    public WebSocketSession getOneSeesion(String sessionId){
    	WebSocketSession session = webSocketSessions.get(sessionId);
    	return session;
    }

    /**
     * Get a list of the websocket sessions by the jti.
     * @param jti
     * @return	The list of the websocket sessions which are belong to the same jti.
     */
    public ArrayList<WebSocketSession> listSessionByJti(String jti){
    	ArrayList<WebSocketSession> list = new ArrayList<WebSocketSession>();
    	if(jtiSessionId.containsKey(jti)){
    		Iterator<String> iterator = jtiSessionId.get(jti).iterator();
    		while(iterator.hasNext()){
    			String sessionId = iterator.next();
    			list.add(getOneSeesion(sessionId));
    		}
    	}
    	return list;
    }

    /**
     * Get a list of the websocket sessions by the userId.
     * @param userId
     * @return	The list of the websocket sessions which are belong to the same user.
     */
    public ArrayList<WebSocketSession> listSessionByUserId(Long userId){
    	ArrayList<WebSocketSession> list = new ArrayList<WebSocketSession>();
    	if(userIdJti.containsKey(userId)){
    		Iterator<String> iterator = userIdJti.get(userId).iterator();
    		while(iterator.hasNext()){
    			String jti = iterator.next();
    			list.addAll(listSessionByJti(jti));
    		}
    	}
    	return list;
    }

    /**Get all the websocket session.
     * @return
     */
    public ArrayList<WebSocketSession> listAllSession(){
    	ArrayList<WebSocketSession> list = new ArrayList<WebSocketSession>();
    	Iterator<Entry<String, WebSocketSession>> iterator = webSocketSessions.entrySet().iterator();
    	while(iterator.hasNext()){
    		Entry<String, WebSocketSession> entry = iterator.next();
    		WebSocketSession session = entry.getValue();
    		list.add(session);
    	}
    	return list;
    }

    /**
     * Delete one websocket session.
     * @param session The session we want to delete.
     */
    public void deleteOneSession(WebSocketSession session){
    	boolean jtiIsNull = false;
    	String jti = getJtiBySession(session);
        long userId = getUserIdBySession(session);
        String sessionId = session.getId();
    	if(jti != null && userId != 0){
            Iterator<String> sessionIterator = jtiSessionId.get(jti).iterator();
            while(sessionIterator.hasNext()){
            	String sessionIdFromList = sessionIterator.next();
            	if(sessionIdFromList.equals(sessionId)){
            		//iterator.remove();
            		jtiSessionId.get(jti).remove(sessionIdFromList);
            		if(jtiSessionId.get(jti).isEmpty()){
            			jtiSessionId.remove(jti);
            			jtiIsNull = true;
            		}
            	}
            }
            if(jtiIsNull){
            	 Iterator<String> jtiIterator = userIdJti.get(userId).iterator();
                 while(jtiIterator.hasNext()){
                 	String jtiFromList = jtiIterator.next();
                 	if(jtiFromList.equals(jti)){
                 		//iterator.remove();
                 		userIdJti.get(userId).remove(jtiFromList);
                 		if(userIdJti.get(userId).isEmpty()){
                 			userIdJti.remove(userId);
                 		}
                 	}
                 }
            }
            webSocketSessions.remove(sessionId);
    	}
    }

    /**
     * Get the Jti by session
     * @param session
     * @return
     */
    public String getJtiBySession(WebSocketSession session) {
        try {
            String  jti = (String) session.getAttributes().get(JTI);
            return jti;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Get the userId by session
     * @param session
     * @return
     */
    public Long getUserIdBySession(WebSocketSession session) {
        try {
            Long  userId = (Long) session.getAttributes().get(USER_ID);
            return userId;
        } catch (Exception e) {
            return (long) 0;
        }
    }

	/**
	 * Show the data from the webSocketSessions, userIdJti and jtiSessionId.
	 */
	public void showData(){
		 System.out.println("webSocketSessions:" + webSocketSessions);
         System.out.println("userIdJti:" + userIdJti);
         System.out.println("jtiSessionId:" + jtiSessionId);
         System.out.println("********************************");
	}

}
