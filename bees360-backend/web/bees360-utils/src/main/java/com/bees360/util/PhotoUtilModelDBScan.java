package com.bees360.util;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.bees360.entity.PhotoInformation;
import com.drew.imaging.jpeg.JpegMetadataReader;
import com.drew.imaging.jpeg.JpegProcessingException;
import com.drew.metadata.Metadata;
import com.drew.metadata.xmp.XmpDirectory;

public class PhotoUtilModelDBScan {
	private double radius;
	private int minPhotoNumber;

	public PhotoUtilModelDBScan(double radius, int minPhotoNumber){
		this.radius = radius;
		this.minPhotoNumber = minPhotoNumber;
	}

	public void distinguishPhotos(String dirUrl) throws JpegProcessingException, IOException{
		File file = new File(dirUrl);
		File[] photos = file.listFiles();
		if(photos == null || photos.length == 0){
			return;
		}
		List<PhotoInformation> photoList = new ArrayList<PhotoInformation>();
		photoList = getPhotoList(photos);
		DBScan(photoList);
	}
	private List<PhotoInformation> getPhotoList(File[] photos) throws JpegProcessingException, IOException{
		List<PhotoInformation> photoList = new ArrayList<PhotoInformation>();
		for(File photo : photos){
			Metadata metadata = JpegMetadataReader.readMetadata(photo);
			Map<String, String> xmpMap = metadata.getFirstDirectoryOfType(XmpDirectory.class).getXmpProperties();
			double absoluteAltitude = Double.parseDouble(xmpMap.get("drone-dji:AbsoluteAltitude"));
			double ralativeAltitude = Double.parseDouble(xmpMap.get("drone-dji:RelativeAltitude"));
			String url = photo.getPath();
			PhotoInformation photoInformation = new PhotoInformation(url, absoluteAltitude, ralativeAltitude);
			photoList.add(photoInformation);
		}
		return photoList;
	}

	private void DBScan(List<PhotoInformation> photoList){
		int cluster = 1;
		for(PhotoInformation photo : photoList){
			if(photo.isVisited()){
				continue;
			}
			photo.setVisited(true);
			List<PhotoInformation> adjacentPhotoList = new ArrayList<PhotoInformation>();
			adjacentPhotoList = getAdjacentPhotoList(photo, photoList);
			if(adjacentPhotoList.size() < minPhotoNumber && adjacentPhotoList!=null){
				photo.setNoised(true);
			}else{
				photo.setCluster(cluster);
				for(int i = 0; i < adjacentPhotoList.size(); i++){
					/*System.out.println("i: " + i + "size: " + adjacentPhotoList.size());*/
					PhotoInformation adjacentPhoto = adjacentPhotoList.get(i);
					if(adjacentPhoto.getCluster() == 0){
						adjacentPhoto.setCluster(cluster);
						if(adjacentPhoto.isNoised()){
							adjacentPhoto.setNoised(false);
						}
					}
					if(!adjacentPhoto.isVisited()){
						adjacentPhoto.setVisited(true);
						List<PhotoInformation> adjacentAdjacentPhotoList = new ArrayList<PhotoInformation>();
						adjacentAdjacentPhotoList = getAdjacentPhotoList(adjacentPhoto, photoList);
						if(adjacentAdjacentPhotoList != null && adjacentAdjacentPhotoList.size() > minPhotoNumber){
							adjacentPhotoList.addAll(adjacentAdjacentPhotoList);
						}
					}
				}
				cluster++;
			}
		}
	}

	private List<PhotoInformation> getAdjacentPhotoList(PhotoInformation center, List<PhotoInformation> photoList){
		List<PhotoInformation> adjacentPhoto = new ArrayList<PhotoInformation>();
		for(PhotoInformation photo : photoList){
			double distance = getDistance(photo, center);
			if(distance < radius){
				adjacentPhoto.add(photo);
			}
		}
		return adjacentPhoto;
	}

	private double getDistance(PhotoInformation photo1, PhotoInformation photo2){
		double x1 = photo1.getAbsoluteAltitude();
		double y1 = photo1.getRelativeAltitude();
		double x2 = photo2.getAbsoluteAltitude();
		double y2 = photo2.getRelativeAltitude();
		return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));
	}
	public static void main(String args[]){
		PhotoUtilModelDBScan util = new PhotoUtilModelDBScan(3, 2);
		try {
			util.distinguishPhotos("/home/<USER>/HAH/photo/1000211/");
		} catch (JpegProcessingException | IOException e) {
			System.out.println(e.toString());
		}
	}
}
