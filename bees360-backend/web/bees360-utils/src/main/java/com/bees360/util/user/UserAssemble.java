package com.bees360.util.user;

import com.bees360.entity.enums.RoleEnum;
import com.bees360.resource.ResourceContext;
import com.bees360.user.Message.UserMessage;
import com.bees360.user.User;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import java.net.URL;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
public class UserAssemble {

    private static final String HTTP_PREFIX = "http://";
    private static final String HTTPS_PREFIX = "https://";

    public static final String AUTHORITY_PREFIX = "ROLE_";

    public static final String BEES360_USER_PREFIX = "B3";
    public static final String BEES360_USER_PREFIX_WITH_COLON = "B3:";

    public static User toUser(@Nullable com.bees360.entity.User user, @NotNull ResourceContext resourceContext) {
        if (Objects.isNull(user)) {
            return null;
        }
        String photo = StringUtils.trimToEmpty(user.getAvatar());
        if (StringUtils.isNotEmpty(photo) && !isHttpOrHttpsUrl(photo)) {
            photo = resourceContext.getAbsoluteURI(photo).toString();
        }
        return User.from(UserMessage.newBuilder()
            .setId(user.getUserId() + "")
            .setEmail(StringUtils.stripToEmpty(user.getEmail()))
            .setPhone(StringUtils.stripToEmpty(user.getPhone()))
            .setName(user.getName())
            .setPhoto(photo)
            .addAllAuthority(user.listRoles().stream().map(RoleEnum::getAuthorityDisplay).collect(Collectors.toList()))
            .build());
    }

    public static com.bees360.entity.User toWebUser(com.bees360.user.User user) {
        if (user == null) {
            return null;
        }
        com.bees360.entity.User webUser = new com.bees360.entity.User();
        String[] names = user.getName().trim().split("\\s+");
        if (names.length == 2) {
            webUser.setFirstName(names[0]);
            webUser.setLastName(names[1]);
        } else {
            webUser.setFirstName(StringUtils.trimToEmpty(user.getName()));
            webUser.setLastName("");
        }
        webUser.setEmail(StringUtils.trimToEmpty(user.getEmail()));
        webUser.setPhone(StringUtils.trimToEmpty(user.getPhone()));
        String photo = Optional.ofNullable(user.getPhoto()).map(URL::toString).orElse("");
        webUser.setAvatar(photo);
        user.getAllAuthority().stream().map(UserAssemble::authorityToRole)
            .filter(Objects::nonNull).forEach(webUser::addRole);
        String userId = user.getId();
        long webUserId;
        if (StringUtils.startsWith(userId, BEES360_USER_PREFIX_WITH_COLON)) {
            String substring = StringUtils.removeStart(userId, BEES360_USER_PREFIX_WITH_COLON);
            webUserId = Long.parseLong(substring);
        } else {
            String idWithoutPrefix = Bees360UserUtils.removePrefix(userId);
            boolean isWebUserId = Bees360UserUtils.isWebUserId(idWithoutPrefix);
            webUserId = isWebUserId ? Long.parseLong(idWithoutPrefix) : user.toMessage().getUid();
        }
        webUser.setUserId(webUserId);
        return webUser;
    }

    public static String authorityToRoleName(String authority) {
        return StringUtils.removeStart(authority, AUTHORITY_PREFIX);
    }

    public static @Nullable RoleEnum authorityToRole(String authority) {
        String roleName = authorityToRoleName(authority);
        try {
            return RoleEnum.valueOf(roleName);
        } catch (RuntimeException e) {
            return null;
        }
    }

    // TODO@ygr 在整理solid/user模块的时候会统一avatar的格式
    private static boolean isHttpOrHttpsUrl(String url) {
        return StringUtils.startsWithIgnoreCase(url, HTTP_PREFIX) || StringUtils.startsWithIgnoreCase(url, HTTPS_PREFIX);
    }

}
