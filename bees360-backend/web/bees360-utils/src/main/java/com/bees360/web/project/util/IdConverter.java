package com.bees360.web.project.util;

import org.apache.commons.lang3.StringUtils;

import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
public class IdConverter {

    public static Long toLong(String id) {
        try {
            return Objects.isNull(id)? null: Long.parseLong(id);
        } catch (Exception ex) {
            return null;
        }
    }

    public static Long toLongOrNotFound(String id, String entityName) {
        entityName = StringUtils.defaultIfEmpty(entityName, "Resource");

        try {
            return Long.parseLong(id);
        } catch (Exception ex) {
            throw new NoSuchElementException(entityName + "`" + id + "` not found.");
        }
    }

    public static <A, R> R parseOrNotFound(A id, String notFoundMessage, Function<A, R> parser) {
        try {
            return parser.apply(id);
        } catch (Exception ex) {
            throw new NoSuchElementException(notFoundMessage);
        }
    }

    public static Long toProjectId(String projectId) {
        return toLongOrNotFound(projectId, "Project");
    }

    public static Long toCompanyId(String companyId) {
        return toLongOrNotFound(companyId, "Company");
    }

    public static Long toStatusId(String statusId) {
        return toLongOrNotFound(statusId, "Status");
    }

    public static Long toTagId(String tagId) {
        return toLongOrNotFound(tagId, "Tag");
    }

    public static Long toUserId(String userId) {
        return toLongOrNotFound(userId, "User");
    }
}
