package com.bees360.util;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EnumToMap {
	/**
	 * Enumerate map with value as key of map, description as value of map
	 *
	 * @param enumT
	 * @param method
	 * @return enum mapcolloction
	 */
	public static <T> List<Map<String, String>> enumToMap(Class<T> enumT, String... methodNames) {
		List<Map<String, String>> enumList = new ArrayList<Map<String, String>>();
		Map<String, String> enumMap = null;
		if (!enumT.isEnum()) {
			return enumList;
		}
		T[] enums = enumT.getEnumConstants();
		if (enums == null || enums.length <= 0) {
			return enumList;
		}
		int count = methodNames.length;
		// Default interface value method
		String valueMathod = "getValue";
		// Default interface description method
		String desMathod = "getDescription";
		// Extension method
		if (count >= 1 && !"".equals(methodNames[0])) {
			valueMathod = methodNames[0];
		}
		if (count == 2 && !"".equals(methodNames[1])) {
			desMathod = methodNames[1];
		}
		for (int i = 0, len = enums.length; i < len; i++) {
			enumMap = new HashMap<String, String>();
			T tobj = enums[i];
			try {
				// Get the value value
				String resultValue = getMethodValue(valueMathod, tobj);
				if ("".equals(resultValue)) {
					continue;
				}
				// Get the description description value
				String resultDes = getMethodValue(desMathod, tobj);
				// If the description does not exist to get the value of the
				// property
				if ("".equals(resultDes)) {
					resultDes = tobj.toString();
				}
				enumMap.put("key", resultValue);
				enumMap.put("value", resultDes);
				enumList.add(enumMap);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return enumList;
	}

	/**
	 * According to the reflection, the method value is obtained by the method
	 * name, and the case is ignored.
	 *
	 * @param methodName
	 * @param obj
	 * @param args
	 * @return return value
	 */
	private static <T> String getMethodValue(String methodName, T obj, Object... args) {
		String resut = "";
		// boolean isHas = false;
		try {
			/********************************* start *****************************************/
			// Get the method array, here as long as the common method
			Method[] methods = obj.getClass().getMethods();
			if (methods.length <= 0) {
				return resut;
			}
			Method method = null;
			for (int i = 0, len = methods.length; i < len; i++) {
				// Overlooking the size writing method
				if (methods[i].getName().equalsIgnoreCase(methodName)) {
					// isHas = true;
					// If there is, the correct method name is taken out
					methodName = methods[i].getName();
					method = methods[i];
					break;
				}
			}
			/*************************** end ***********************************************/
			// Method method = obj.getClass().getDeclaredMethod(methodName);
			// Determination method
			if (method == null) {
				return resut;
			}
			// Method execution
			resut = method.invoke(obj, args).toString();
			if (resut == null) {
				resut = "";
			}
			// Return to the result
			return resut;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return resut;
	}
}
