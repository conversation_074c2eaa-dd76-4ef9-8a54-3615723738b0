package com.bees360.util.file;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;

/**
 * <AUTHOR>
 */
public interface SizeReduce {

    /**
     *
     * @param input 需要压缩的文件
     * @param out 压缩结果输出
     * @param limitSize 限制压缩结果的最大大小
     * @return true 表示成功压缩，结果已经输出到out；false表示压缩失败，结果无法从out中得到
     * @throws IOException
     */
    boolean reduce(File input, File out, int limitSize) throws IOException;

    /**
     *
     * @param input 需要压缩的文件
     * @param out 压缩结果输出
     * @param limitSize 限制压缩结果的最大大小
     * @return true 表示成功压缩，结果已经输出到out；false表示压缩失败，结果无法从out中得到
     * @throws IOException
     */
    boolean reduce(File input, OutputStream out, int limitSize) throws IOException;
}
