package com.bees360.util;

import java.io.File;

import com.bees360.util.file.FtpDownloader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IpWorkerUSA extends IpWorker {

	private Logger logger = LoggerFactory.getLogger(IpWorkerUSA.class);

	protected final String ARIN_IP_FILE = FileManager.ARIN_IP_FILE;
	protected final String ARIN_IP_INT_FILE = FileManager.ARIN_IP_INT_FILE;

	private static IpWorkerUSA usaIpWorkerInstance = null;

	public static IpWorkerUSA getInstance() {
		if (usaIpWorkerInstance == null) {
			synchronized (IpWorkerUSA.class) {
				if (usaIpWorkerInstance == null) {
					usaIpWorkerInstance = new IpWorkerUSA();
					usaIpWorkerInstance.init();
				}
			}
		}
		return usaIpWorkerInstance;
	}

	public boolean isUSAIp(String ip) {
		return isInRanges(ip);
	}

	@Override
	protected void init(){
		File ipsFile = new File(ARIN_IP_FILE);
		if(!ipsFile.exists()){
			if(!downloadArinIpFile()){
				logger.error("Fail to download arin-ip-file");
				// keep the original ipRanges
				return;
			}
			logger.info("Download arin-ip-file successfully");
		}

		String prefix = "arin|US|ipv4|";
		initIpRanges(ipsFile, prefix);
	}

	protected boolean downloadArinIpFile() {
		// source: ftp://ftp.arin.net/pub/stats/arin/delegated-arin-extended-latest
		// Anonymous to connection remote FTP
		String ftpHost = "ftp.arin.net";
		String ftpUserName = "anonymous";
		String ftpPassword = "<EMAIL>";
		String ftpPath = "/pub/stats/arin/delegated-arin-extended-latest";
		String url = "ftp://" + ftpHost + ftpPath;
		logger.info("Download arin-ip-file from " + url + " to " + ARIN_IP_FILE);
		try {
			return FtpDownloader.downloadFtpFile(ftpHost, ftpUserName, ftpPassword, ftpPath, ARIN_IP_FILE);
		} catch (Exception e) {
			logger.error("Fail to download arin-ip-file from: " + url);
		}
		return false;
	}
}
