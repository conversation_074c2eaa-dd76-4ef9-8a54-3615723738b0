package com.bees360.utils.maps;

import com.bees360.util.maps.googlemaps.GoogleApiConfig;
import com.bees360.util.maps.googlemaps.GoogleMapHouseBoundaryUtil;
import com.bees360.util.maps.googlemaps.GoogleMapUtilBuilder;

public class TestGoogleMapHouseBoundaryUtil {

    // 运行时，需要一个有效值
    private static String apiKey = "";
    private static boolean proxy = false;

    public static void main(String[] args) throws Exception {

        GoogleApiConfig googleApiConfig = new GoogleApiConfig();
        googleApiConfig.setApiKey(apiKey);
        googleApiConfig.setProxy(proxy);

        GoogleMapUtilBuilder googleMapsUtilBuilder = new GoogleMapUtilBuilder(googleApiConfig);
        googleMapsUtilBuilder.init();

        String address = "77713";
//        String address = "8325 Chappell Hill Dr, Beaumont, TX 77713";
//        String address = "2009 Calisto Way, Allen, TX 75013";
        String destFile = "/Users/<USER>/Desktop/test/googlemap.jpg";
        GoogleMapHouseBoundaryUtil gmu = googleMapsUtilBuilder.buildGoogleMapHouseBoundaryUtil();

        System.out.println(gmu.geocoding(address));
        // System.out.println(gmu.saveImage(address, destFile));
//        System.out.println(gmu.saveImage(33.1040038, -96.72908450000001, destFile));
    }
}
