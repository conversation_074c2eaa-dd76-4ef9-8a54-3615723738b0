package com.bees360.util;

import com.bees360.entity.SystemConfig;
import com.bees360.entity.util.SystemConfigPrefix;
import com.bees360.util.exception.SystemConfigFailConvertException;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class SystemConfigConvertorUtilTest {

    @Data
    public static class SystemConfigTestDto implements SystemConfigPrefix {
        private String home;
        private boolean autoPay;
        private int count;

        @Override
        public String prefix() {
            return "test";
        }
    }

    @Test
    public void toSystemConfigs() throws SystemConfigFailConvertException {
        SystemConfigTestDto systemConfigTestDto = new SystemConfigTestDto();
        systemConfigTestDto.setHome("ThisIsHome");
        systemConfigTestDto.setAutoPay(true);
        systemConfigTestDto.setCount(1243);
        List<SystemConfig> systemConfigList = SystemConfigConvertorUtil.toSystemConfigs(systemConfigTestDto);

        assertEquals(3, systemConfigList.size());

        SystemConfigTestDto newSystemConfigTestDto = new SystemConfigTestDto();
        SystemConfigConvertorUtil.toSystemConfigDto(systemConfigList, newSystemConfigTestDto);

        assertEquals(systemConfigTestDto, newSystemConfigTestDto);
    }
}
