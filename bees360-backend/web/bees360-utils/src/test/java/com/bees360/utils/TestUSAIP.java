package com.bees360.utils;

import java.net.UnknownHostException;

import com.bees360.util.IpWorker;
import com.bees360.util.IpWorkerCN;
import com.bees360.util.IpWorkerUSA;

public class TestUSAIP {

	public static void main(String[] args) throws UnknownHostException, InterruptedException {
//		test1();
//		test2();
		test3();
		System.out.println("52000 ip ranges cost space: " + 52000 * 4 * 2.0 / 1024 / 1024 +  "M");
	}

	public static void test1(){
		long memory1 = Runtime.getRuntime().freeMemory();
		int[][] a = new int[52000][2];
//		System.gc();
		long memory2 = Runtime.getRuntime().freeMemory();
		System.out.println(((memory1-memory2)*1.0/1024/1024) +" M");
	}
	public static void test2(){
		long memory1 = Runtime.getRuntime().freeMemory();
		Integer[][] a = new Integer[52000][2];
//		System.gc();
		long memory2 = Runtime.getRuntime().freeMemory();
		System.out.println(((memory1-memory2)*1.0/1024/1024) +" M");
	}

	public static void test3() throws InterruptedException, UnknownHostException{
		String BHSip = "************"; // not usa ip: Commonwealth of the Bahamas
		String USAip = "*************"; // usa ip
		String CNip = "***************"; // Chinese ip
		// TODO the storage calculate is wrong.
		System.gc();
		Thread.sleep(20000);
		long memory1 = Runtime.getRuntime().freeMemory();

		long start = System.currentTimeMillis();
		IpWorkerUSA ipWorkerUSA = IpWorkerUSA.getInstance();
		long end = System.currentTimeMillis();
		System.gc();
		Thread.sleep(20000);
		long memory2 = Runtime.getRuntime().freeMemory();
		System.out.println("init ipWorkerUSA spend time = "+(end-start)+" ms");
		long usaUseStorage = memory1 - memory2;
		System.out.println("IpWorkerUSA cost: " + ((usaUseStorage)*1.0/1024/1024) +" M");

		start = System.currentTimeMillis();
		IpWorkerCN ipWorkerCN = IpWorkerCN.getInstance();
		end = System.currentTimeMillis();
		System.out.println("init ipWorkerCN spend time = "+(end-start)+" ms");
		System.gc();
		Thread.sleep(20000);
		long memory3 = Runtime.getRuntime().freeMemory();
		long cnUseStorage = memory2 - memory3;
		System.out.println("IpWorkerCN cost: " + ((cnUseStorage)*1.0/1024/1024) +" M");
		System.out.println("IpWorkerUSA and IpWorkerCN cost: " + ((memory1 - memory3)*1.0/1024/1024) +" M");

		System.out.println();
		System.out.println("usa iprange numbers: " + ipWorkerUSA.ipRangesSize());
		System.out.println("cn iprange numbers: " + ipWorkerCN.ipRangesSize());
		System.out.println();
		System.out.println();
		start = System.currentTimeMillis();
		System.out.println(BHSip + "\tis not an IP of USA:\tfalse = " + ipWorkerUSA.isUSAIp(BHSip));
		System.out.println(CNip + "\tis not an IP of USA:\tfalse = " + ipWorkerUSA.isUSAIp(CNip));
		System.out.println(USAip + "\tis an IP of USA:\ttrue = " + ipWorkerUSA.isUSAIp(USAip));
		end = System.currentTimeMillis();
		System.out.println("checking 3 usa ips spends: " + (end - start) + " ms");

		start = System.currentTimeMillis();
		System.out.println(BHSip + "\tis not an IP of CN:\tfalse = " + ipWorkerCN.isCNIp(BHSip));
		System.out.println(USAip + "\tis not an IP of CN:\tfalse = " + ipWorkerCN.isCNIp(USAip));
		System.out.println(CNip + "\tis an IP of CN:\ttrue = " + ipWorkerCN.isCNIp(CNip));

		end = System.currentTimeMillis();
		System.out.println("checking 3 cn ips spends: " + (end - start) + " ms");
	}
}
