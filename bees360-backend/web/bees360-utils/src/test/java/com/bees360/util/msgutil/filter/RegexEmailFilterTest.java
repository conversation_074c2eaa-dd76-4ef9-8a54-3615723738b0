package com.bees360.util.msgutil.filter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 */
public class RegexEmailFilterTest {

    private final List<String> bees360Emails =
        Collections.unmodifiableList(Lists.newArrayList("<EMAIL>", "<EMAIL>", "<EMAIL>"));
    private final List<String> gmailEmail =
        Collections.unmodifiableList(Lists.newArrayList("<EMAIL>"));
    private final List<String> nineRealEmail =
        Collections.unmodifiableList(Lists.newArrayList("<EMAIL>", "<EMAIL>"));
    private final List<String> others = Collections
        .unmodifiableList(Lists.newArrayList("<EMAIL>", "<EMAIL>", "<EMAIL>", "guanrong.yang@bees360acom"));
    private final List<String> emails =
        Collections.unmodifiableList(Lists.newArrayList(Iterables.concat(bees360Emails, gmailEmail, nineRealEmail, others)));

    @Test
    public void newFilterIncludeAll() {
        RegexEmailFilter emailFilter = RegexEmailFilter.newFilterIncludeAll();
        List<String> result = emailFilter.filter(emails);
        assertFalse(result == emails);
        assertEquals(emails, result);
    }

    @Test
    public void testDefaultRegexEmailFilter() {
        RegexEmailFilter emailFilter = new RegexEmailFilter();
        List<String> result = emailFilter.filter(emails);
        assertFalse(result == emails);
        assertEquals(emails, result);
    }

    @Test
    public void newFilterExcludeAll() {
        RegexEmailFilter emailFilter = RegexEmailFilter.newFilterExcludeAll();
        List<String> result = emailFilter.filter(emails);
        assertEquals(Lists.newArrayList(), result);
    }

    @Test
    public void testIncludeAndExclude() {
        RegexEmailFilter emailFilter = new RegexEmailFilter();
        emailFilter.setRegexInclude(Lists.newArrayList(".*@bees360\\.com", ".*@9realms\\.co"));
        emailFilter.setEmailInclude(gmailEmail);

        List<String> result = emailFilter.filter(emails);
        assertEquals(Lists.newArrayList(Iterables.concat(bees360Emails, gmailEmail, nineRealEmail)), result);

        String email = "<EMAIL>";
        assertTrue(result.contains(email), email + " should be included.");
        emailFilter.setRegexExclude(Lists.newArrayList("guanrong.*"));
        result = emailFilter.filter(emails);
        assertFalse(result.contains(email), email + " shouldn't be included.");

        email = "<EMAIL>";
        assertTrue(result.contains(email), email + " should be included.");
        emailFilter.setEmailExclude(Lists.newArrayList(email));
        result = emailFilter.filter(emails);
        assertFalse(result.contains(email), email + " shouldn't be included.");

        emailFilter.setRegexExclude(Lists.newArrayList("*"));
        result = emailFilter.filter(emails);
        assertTrue(result.isEmpty(), "result should be empty after setting exclude-regex as *.");
    }

    @Test
    public void includeAllThenExcludeSome() {
        RegexEmailFilter emailFilter = new RegexEmailFilter();
        emailFilter.setRegexExclude(Lists.newArrayList(".*@bees360\\.com"));
        List<String> result = emailFilter.filter(emails);
        assertEquals(Lists.newArrayList(Iterables.concat(gmailEmail, nineRealEmail, others)), result);
    }

    @Test
    public void excludeOnlyOne() {
        final var email = "<EMAIL>";
        RegexEmailFilter emailFilter = new RegexEmailFilter();
        emailFilter.setEmailExclude(List.of(email));

        var result = emailFilter.filter(List.of(email));
        assertTrue(result.isEmpty());
    }

    @Test
    public void includeOnlyOne() {
        final var email = "<EMAIL>";
        RegexEmailFilter emailFilter = new RegexEmailFilter();
        emailFilter.setEmailInclude(List.of(email));

        var result = emailFilter.filter(List.of(email));
        assertEquals(List.of(email), result);
    }


    @Test
    public void includeAndExcludeOnlyOne() {
        final var email = "<EMAIL>";
        RegexEmailFilter emailFilter = new RegexEmailFilter();
        emailFilter.setEmailInclude(List.of(email));
        emailFilter.setEmailExclude(List.of(email));

        var result = emailFilter.filter(List.of(email));
        assertTrue(result.isEmpty());
    }
}
