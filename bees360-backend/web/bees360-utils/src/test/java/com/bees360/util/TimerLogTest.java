package com.bees360.util;

import com.bees360.util.log.TimerLog;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Test;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class TimerLogTest {

    ThreadPoolExecutor pool = new ThreadPoolExecutor(5, 5, 30, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    @Test
    public void testMultiThread() throws Exception {
        TimerLog timerLog = TimerLog.newTimerLog(log);
        timerLog.printMinCost(500);
        timerLog.printErrorMinCost(1000);

        timerLog.start();
        Thread.sleep(1200);
        timerLog.pause().print("timer pause. ${cost} ms");

        List<Future<Integer>> futureList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            futureList.add(pool.submit(new MyCallableTest()));
        }

        for (Future<Integer> f : futureList) {
            f.get();
        }
    }

    @Test
    public void testNotInvokeStart() {
        assertThrows(IllegalStateException.class, () -> {
            TimerLog timerLog = TimerLog.newTimerLog(log);
            timerLog.printMinCost(500);
            timerLog.printErrorMinCost(1000);

//        timerLog.start();
            Thread.sleep(1200);
            timerLog.pause().print("timer pause. ${cost} ms");
        });
    }

    @Test
    public void testNotFoundTimer() {
        assertThrows(IllegalStateException.class, () -> {
            TimerLog timerLog = TimerLog.newTimerLog(log);
            timerLog.printMinCost(500);
            timerLog.printErrorMinCost(1000);

//        timerLog.start("hello world");
            Thread.sleep(1200);
            timerLog.pause("hello world").print("timer pause. ${cost} ms");
        });
    }

    class MyTimerLog extends TimerLog {

        public MyTimerLog(Logger log) {
            super(log);
        }

        public MyTimerLog(Logger log, boolean resetTraceId) {
            super(log, resetTraceId);
        }

        String getTraceId() {
            return traceId.get();
        }

        String getThreadTraceId() {
            return threadTraceId.get();
        }
    }

    @Test
    public void testTraceId() {
        MyTimerLog timerLog = new MyTimerLog(log);

        assertNotNull(timerLog.getThreadTraceId());
        assertNotNull(timerLog.getTraceId());
    }

}
