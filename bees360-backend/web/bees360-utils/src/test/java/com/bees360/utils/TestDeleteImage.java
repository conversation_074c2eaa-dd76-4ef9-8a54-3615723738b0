package com.bees360.utils;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import javax.imageio.ImageIO;

import com.bees360.util.DeleteLowImageRunnable;
import com.bees360.util.DownFtpFileRunnable;

public class TestDeleteImage {

	public static void main(String[] args) throws IOException {

		ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
		long oneDay = 24 * 60 * 60 * 1000;
		long initDelay = getTimeMillis("00:00:00") - System.currentTimeMillis();
		initDelay = initDelay > 0 ? initDelay : oneDay + initDelay;
//		executor.scheduleAtFixedRate(new DeleteLowImageRunnable(), initDelay, oneDay, TimeUnit.MILLISECONDS);
//		executor.scheduleAtFixedRate(new DeleteLowImageRunnable(), 0, oneDay, TimeUnit.MILLISECONDS);
		executor.scheduleAtFixedRate(new DownFtpFileRunnable(), 0, oneDay, TimeUnit.MILLISECONDS);
//		File imageFile = new File("/tmp/bees360/projects/10028/images/thumbnail-view/1515036602728.JPG");
//		File imageFile = new File("/home/<USER>/test/8.JPG");
//		BufferedImage image = ImageIO.read(imageFile);
//		Path path = imageFile.toPath();
//		new DownFtpFileRunnable().run();
	}

	private static long getTimeMillis(String time) {
	    try {
	        DateFormat dateFormat = new SimpleDateFormat("yy-MM-dd HH:mm:ss");
	        DateFormat dayFormat = new SimpleDateFormat("yy-MM-dd");
	        Date curDate = dateFormat.parse(dayFormat.format(new Date()) + " " + time);

	        return curDate.getTime();
	    } catch (Exception e) {
	        e.printStackTrace();
	    }
	    return 0;
	}
}
