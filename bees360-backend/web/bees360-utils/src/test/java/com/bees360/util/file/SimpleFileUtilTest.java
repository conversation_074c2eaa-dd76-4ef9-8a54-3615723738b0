package com.bees360.util.file;

import java.io.File;
import org.junit.jupiter.api.Test;

public class SimpleFileUtilTest {

//	@Test
	public void TestNormalizeFileSeparator(){
		String str = "D:\\wangxing\\3DConstruction\\project\\10002\\images\\origin/workspace/pmvs/visualize/123456.jpg	D:\\wangxing\\3DConstruction\\project\\10002\\images\\origin\\987654.JPG";
		String path = SimpleFileUtil.normalizeFileSeparator(str);
		System.out.println(path);
	}

//	@Test
	public void TestRemoveDirectory(){
		String path = "/home/<USER>/xxx";
		boolean result = SimpleFileUtil.removeDirectory(path, false);
		System.out.println(result);
	}

	@Test
	public void TestCopyFile(){
		long start = System.currentTimeMillis();
		String srcPath = "/home/<USER>/图片/wrong-annotation (复件).png";
		String destPath = "/home/<USER>/图片/wrong-annotation (复件)-copy.png";
		boolean result = SimpleFileUtil.copyFileTo(new File(srcPath), new File(destPath));
		System.out.println(result);
		long end = System.currentTimeMillis();
		System.out.println("spend time: " + (end - start));
	}
}
