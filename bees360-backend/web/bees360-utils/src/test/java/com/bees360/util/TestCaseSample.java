package com.bees360.util;
import java.io.IOException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import java.io.File;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2019/09/06 09:29
 */
public class TestCaseSample {
    @TempDir
    public File temporaryFolder;

    @Test
    public void test() throws Exception {
        File child = newFolder(temporaryFolder, "grandparent", "parent", "child");

        assertEquals("child", child.getName());
        assertEquals("parent", child.getParentFile().getName());
        assertEquals("grandparent", child.getParentFile().getParentFile().getName());
        System.out.println(child.getAbsolutePath());

        System.out.println(File.createTempFile(getClass().getName(), ".xml"));

        File f = new File(temporaryFolder, getClass().getSimpleName());

        System.out.println(f.exists() + " :" + f);
    }

    private static File newFolder(File root, String... subDirs) throws IOException {
        String subFolder = String.join("/", subDirs);
        File result = new File(root, subFolder);
        if (!result.mkdirs()) {
            throw new IOException("Couldn't create folders " + root);
        }
        return result;
    }
}
