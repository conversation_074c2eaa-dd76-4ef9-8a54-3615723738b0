package com.bees360.utils;

import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import javax.imageio.ImageIO;

/**
 *  缩略图实现，将图片(jpg、bmp、png、gif等等)真实的变成想要的大小
 */
/*******************************************************************************
 * 缩略图类（通用） 本java类能将jpg、bmp、png、gif图片文件，进行等比或非等比的大小转换。 具体使用方法
 * compressPic(大图片路径,生成小图片路径,大图片文件名,生成小图片文名,生成小图片宽度,生成小图片高度,是否等比缩放(默认为true))
 */
public class CompressPicDemo {

	// 图片处理
	public static void compressPic(String inputDir, String outputDir, String inputFileName, String outputFileName)
			throws IOException {
		// 获得源文件
		File file = new File(inputDir + inputFileName);
		Image img = ImageIO.read(file);
		int newWidth = 400;
		int newHeight = 300;
		BufferedImage tag = new BufferedImage((int) newWidth, (int) newHeight, BufferedImage.TYPE_INT_RGB);
		/*
		 * Image.SCALE_SMOOTH 的缩略算法 生成缩略图片的平滑度的 优先级比速度高 生成的图片质量比较好 但速度慢
		 */
		tag.getGraphics().drawImage(img.getScaledInstance(newWidth, newHeight, Image.SCALE_SMOOTH), 0, 0, null);
		FileOutputStream out = new FileOutputStream(outputDir + outputFileName);
		// JPEGImageEncoder可适用于其他图片类型的转换
//		JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);
//		encoder.encode(tag);
		out.close();
	}

	// main测试
	// compressPic(大图片路径,生成小图片路径,大图片文件名,生成小图片文名,生成小图片宽度,生成小图片高度,是否等比缩放(默认为true))
	public static void main(String[] arg) {
		String dirPathSrc = "/home/<USER>/test/src/";
		String dirPathDest = "/home/<USER>/test/dest/";
		int width = 400;
		int height = 300;
		File dirPathFile = new File(dirPathSrc);
		File[] fileList = dirPathFile.listFiles();
		int fileLen = fileList.length;
		int count = 0;
		long start = System.currentTimeMillis();
		for (int i = 0; i < fileLen; i++) {
			File file = fileList[i];
			String fileName = file.getName();
			String imagePathSrc = dirPathSrc + fileName;
			String imagePathDest = dirPathDest + fileName;
			try {
				compressPic(dirPathSrc, dirPathDest, fileName, fileName);
				count++;
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		long end = System.currentTimeMillis();
		System.out.println("TestPictureCompression count = " + count + ", spend time = " + (end - start) + " ms");
	}
}
