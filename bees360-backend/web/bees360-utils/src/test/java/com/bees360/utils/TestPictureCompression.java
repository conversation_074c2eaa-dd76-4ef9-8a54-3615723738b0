package com.bees360.utils;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.FileImageOutputStream;
import java.awt.Color;
import java.awt.Graphics;
import java.awt.Image;

import net.coobird.thumbnailator.Thumbnails;

public class TestPictureCompression {

	public static void keepSizeAndReduceQuality(int width, int height, String imagePathSrc, String imagePathDest)
			throws IOException {

		BufferedImage image = ImageIO.read(new File(imagePathSrc));

		int imageWidth = image.getWidth();
		int imageHeight = image.getHeight();
		float picRatio = imageWidth / Float.parseFloat(imageHeight + "");
		Double picRatioUp = imageHeight / Double.parseDouble(imageWidth + "");
		float actualRatio = width / Float.parseFloat(height + "");
		if (actualRatio >= picRatio) {
			width = (int) (height * picRatio);
		} else {
			height = (int) (width * picRatioUp);
		}

		Thumbnails.of(imagePathSrc).scale(1f).outputQuality(0.8f).toFile(imagePathDest);
	}

	public static void keepSize(int width, int height, String imagePath, String resultPath) throws IOException {
		Thumbnails.of(imagePath).forceSize(width, height).toFile(resultPath);
	}

	public static void changeScale(int width, int height, String imagePath, String resultPath) throws IOException {
		Thumbnails.of(imagePath).scale(1.0f).toFile(resultPath);
	}

	public static void reduceImg(int width, int height, String imagePath, String resultPath) throws IOException {

		BufferedImage BI = ImageIO.read(new File(imagePath));
		Image image = BI.getScaledInstance(width, height, Image.SCALE_SMOOTH);

	}

	// 无效
	public static void compress(int width, int height, String imagePath, String resultPath) throws IOException {
		BufferedImage bi = ImageIO.read(new File(imagePath));

		Image image = bi.getScaledInstance(width, height, Image.SCALE_SMOOTH);
		BufferedImage tag = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

		Graphics g = tag.getGraphics();
		g.setColor(Color.RED);
		g.drawImage(image, 0, 0, null); // 绘制处理后的图
		g.dispose();

		ByteArrayOutputStream bOut = new ByteArrayOutputStream();
		ImageIO.write(tag, "JPG", bOut);
		FileImageOutputStream imageOutput = new FileImageOutputStream(new File(resultPath));
		imageOutput.write(bOut.toByteArray(), 0, bOut.toByteArray().length);
		imageOutput.close();
	}

//	// 效果不好
//	public static void zipImageFile(int width, int height, String imagePathSrc, String imagePathDest)
//			throws IOException {
//		File oldFile = new File(imagePathSrc);
//		File newFile = new File(imagePathDest);
//		float quality = 1.0f;
//		/** 对服务器上的临时文件进行处理 */
//		Image srcFile = ImageIO.read(oldFile);
//		int w = srcFile.getWidth(null);
//		// System.out.println(w);
//		int h = srcFile.getHeight(null);
//		// System.out.println(h);
//		double bili;
//		if (width > 0) {
//			bili = width / (double) w;
//			height = (int) (h * bili);
//		} else {
//			if (height > 0) {
//				bili = height / (double) h;
//				width = (int) (w * bili);
//			}
//		}
//		/** 宽,高设定 */
//		BufferedImage tag = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
//		tag.getGraphics().drawImage(srcFile, 0, 0, width, height, null);
//		// String filePrex = oldFile.getName().substring(0,
//		// oldFile.getName().indexOf('.'));
//		/** 压缩后的文件名 */
//		// newImage = filePrex + smallIcon+
//		// oldFile.getName().substring(filePrex.length());
//
//		/** 压缩之后临时存放位置 */
//		FileOutputStream out = new FileOutputStream(newFile);
//
//		JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);
//		JPEGEncodeParam jep = JPEGCodec.getDefaultJPEGEncodeParam(tag);
//		/** 压缩质量 */
//		jep.setQuality(quality, true);
//		encoder.encode(tag, jep);
//		out.close();
//	}

	public static void compressPic(int width, int height, String srcFilePath, String descFilePath)
			throws IOException {
		File file = null;
		BufferedImage src = null;
		FileOutputStream out = null;
		ImageWriter imgWrier;
		ImageWriteParam imgWriteParams;

		// 指定写图片的方式为 jpg
		imgWrier = ImageIO.getImageWritersByFormatName("jpg").next();
		imgWriteParams = new javax.imageio.plugins.jpeg.JPEGImageWriteParam(null);
		// 要使用压缩，必须指定压缩方式为MODE_EXPLICIT
		imgWriteParams.setCompressionMode(imgWriteParams.MODE_EXPLICIT);
		// 这里指定压缩的程度，参数qality是取值0~1范围内，
		imgWriteParams.setCompressionQuality(0.4f);
		imgWriteParams.setProgressiveMode(imgWriteParams.MODE_DISABLED);

		file = new File(srcFilePath);
		System.out.println(file.length());
		src = ImageIO.read(file);
		out = new FileOutputStream(descFilePath);

		imgWrier.reset();
		// 必须先指定 out值，才能调用write方法, ImageOutputStream可以通过任何
		// OutputStream构造
		imgWrier.setOutput(ImageIO.createImageOutputStream(out));
		// 调用write方法，就可以向输入流写图片
		imgWrier.write(null, new IIOImage(src, null, null), imgWriteParams);
		out.flush();
		out.close();
	}

	public static void main(String[] args) {
		String dirPathSrc = "/home/<USER>/test/src/";
		String dirPathDest = "/home/<USER>/test/dest/";
		int width = 400;
		int height = 300;
		File dirPathFile = new File(dirPathSrc);
		File[] fileList = dirPathFile.listFiles();
		int fileLen = fileList.length;
		int count = 0;
		long start = System.currentTimeMillis();
		for (int i = 0; i < fileLen; i++) {
			File file = fileList[i];
			String fileName = file.getName();
			String imagePathSrc = dirPathSrc + fileName;
			String imagePathDest = dirPathDest + fileName;
			try {
//				 keepSizeAndReduceQuality(width, height, imagePathSrc,
//				 imagePathDest);
				// keepSize(width, height, imagePathSrc, imagePathDest);
				// changeScale(width, height, imagePathSrc, imagePathDest);
//				 compressPicByQuality(width, height, imagePathSrc,
				// imagePathDest);
				// compress(width, height, imagePathSrc, imagePathDest);
//				zipImageFile(width, height, imagePathSrc, imagePathDest);
				compressPic(width, height, imagePathSrc, imagePathDest);
				count++;
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		long end = System.currentTimeMillis();
		System.out.println("TestPictureCompression count = " + count + ", spend time = " + (end - start) + " ms");
	}
}
