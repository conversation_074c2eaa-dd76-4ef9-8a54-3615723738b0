package com.bees360.util;

import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class UrlJoinParserUtilsTest {

    @Test
    public void testPilotLicenses() {
        List<String> licenses = Arrays.asList(
            "http://bees360.s3.amazon.com/license/xxxxx-1.pdf",
            "http://bees360.s3.amazon.com/license/xxxx-2.pdf"
        );
        String listJson = UrlJoinParserUtils.joinListJson(licenses);
        List<String> licensedParsed = UrlJoinParserUtils.parseListJson(listJson);
        // System.out.println(pilot.getLicenseKeyUrls());
        // System.out.println(licensedParsed);
        assertTrue(licenses.equals(licensedParsed));
    }
}
