package com.bees360.util.msgutil;

import java.util.Properties;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class SmsTemplateFormatTest {

	@Test
	public void test() {
		Properties templates = new Properties();
		String templateName = "testTemplate";
		String template = "{0,number,#} is a number, and {1} is a string";
		templates.put(templateName, template);

		SmsTemplateFormat format = new SmsTemplateFormat(templates);
		assertTrue(format.containTemplate(templateName));
		assertEquals(format.getTemplate(templateName), template);

		String content = format.getContent(templateName, new Object[] {100012, "A-B-C-DEFG"});
		assertEquals(content, "100012 is a number, and A-B-C-DEFG is a string");
	}
}
