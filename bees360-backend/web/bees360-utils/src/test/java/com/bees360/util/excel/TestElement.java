package com.bees360.util.excel;

import java.util.Date;

import com.bees360.util.excel.annotations.ColumnOrder;
import com.bees360.util.excel.annotations.DateConfig;
import com.bees360.util.excel.annotations.DateElement;

@DateConfig(format="yyyy-MM-dd HH:mm:ss")
public class TestElement {
	private int id = 1000;
	private Date date = new Date();
	private Date date2 = new Date();
	private String stDate = "2014-05-02 10:20:12";
	private long longDate = new Date().getTime();

	@ColumnOrder(1)
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	@ColumnOrder(2)
	@DateElement
	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	// date2 dosen't have the DateElement annotation
	@ColumnOrder(3)
	public Date getDate2() {
		return date2;
	}
	public void setDate2(Date date2) {
		this.date2 = date2;
	}
	@ColumnOrder(4)
	@DateElement
	public String getStDate() {
		return stDate;
	}
	public void setStDate(String stDate) {
		this.stDate = stDate;
	}
	@ColumnOrder(5)
	@DateElement
	public long getLongDate() {
		return longDate;
	}
	public void setLongDate(long longDate) {
		this.longDate = longDate;
	}

	@Override
	public String toString() {
		return "TestElement [id=" + id + ", stDate=" + stDate + ", longDate=" + longDate + "]";
	}

	public static void main(String[] args) {
		System.out.println(new Date().getTime());
	}
}
