package com.bees360.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
public class PhoneFormatterTest {

    @Test
    public void simpleFormatUsPhone() {
        assertEquals("(*************", PhoneFormatter.simpleFormatUsPhone("***********90"));
        assertEquals("(*************x23", PhoneFormatter.simpleFormatUsPhone("***********90x23"));
        assertEquals("(*************", PhoneFormatter.simpleFormatUsPhone("1234567890"));
        assertEquals("12345678", PhoneFormatter.simpleFormatUsPhone("12345678"));
        assertEquals("12345678", PhoneFormatter.simpleFormatUsPhone("***********"));
        assertEquals("12345678x23", PhoneFormatter.simpleFormatUsPhone("***********x23"));
    }
}
