package com.bees360.util.project;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class ProjectImageArchiveKeyConverterTest {

    @InjectMocks
    private ProjectImageArchiveKeyConverter projectImageArchiveKeyConverter;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test_web_transform_url() {
        String url = "https://www.bees360.com/bees360-web/v1/projects/32624/files/32624_19557 Kiker Drive, Winnie, TX 77665_130978.zip";
        String domain = "https://www.bees360.com/bees360-web";
        String returnedKey = "project/32624/images/archive/32624_19557 Kiker Drive, Winnie, TX 77665_130978.zip";
        assertEquals(returnedKey, projectImageArchiveKeyConverter.transform(url, domain));
    }

    @Test
    public void test_web_transform_key() {
        String url = "project/1003872/images/archive/1003872_12342 East Central Avenue, Sanger, CA 93657_904170.zip";
        String domain = "https://www.bees360.com/bees360-web";
        String returnedKey = "project/1003872/images/archive/1003872_12342 East Central Avenue, Sanger, CA 93657_904170.zip";
        assertEquals(returnedKey, projectImageArchiveKeyConverter.transform(url, domain));
    }

    @Test
    public void test_openapi_transform_url() {
        String url = "https://www.bees360.com/bees360-web/v1/projects/32624/files/32624_19557 Kiker Drive, Winnie, TX 77665_130978.zip";
        String domain = "https://www.bees360.com/bees360-web";
        String returnedKey = "project/32624/images/archive/32624_19557 Kiker Drive, Winnie, TX 77665_130978.zip";
        assertEquals(returnedKey, projectImageArchiveKeyConverter.transform(url, domain));
    }

    @Test
    public void test_openapi_transform_key() {
        String url = "/v1/project/1003872/images/archive/1003872_12342 East Central Avenue, Sanger, CA 93657_904170.zip";
        String domain = "https://www.bees360.com/bees360-web";
        String returnedKey = "project/1003872/images/archive/1003872_12342 East Central Avenue, Sanger, CA 93657_904170.zip";
        assertEquals(returnedKey, projectImageArchiveKeyConverter.transform(url, domain));
    }
}
