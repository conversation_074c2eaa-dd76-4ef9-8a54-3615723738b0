package com.bees360.util.amazon;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class S3LinkTest {

    @Test
    public void testS3Link(){
        String bucketName = "bees360";
        String key = "https://s3.amazonaws.com/" + bucketName+ "/project/10013/images/thumbnails/1517209967872.JPG";
        String key2 = "https://" + bucketName + ".s3.amazonaws.com/project/10013/images/thumbnails/1517209967872.JPG";
        String key3 = "project/10013/images/thumbnails/1517209967872.JPG";
        String key4 = "/project/10013/images/thumbnails/1517209967872.JPG";

        S3Link[] links = {
            new S3Link(key),
            new S3Link(key2),
            new S3Link(bucketName, key3),
            new S3Link(bucketName, key4)
        };
        S3Link sampleLink = links[0];
        for(S3Link s3Link: links) {
            assertTrue(sampleLink.equals(s3Link));
        }
    }

    @Test
    public void testS3Link2() {
        String key3 = "project/10013/images/thumbnails/1517209967872.JPG";
        String key4 = "/project/10013/images/thumbnails/1517209967872.JPG";

        S3Link[] links = {
            new S3Link(key3),
            new S3Link(key4)
        };
        S3Link sampleLink = links[0];
        for(S3Link s3Link: links) {
            System.out.println(s3Link);
            assertTrue(sampleLink.equals(s3Link));
        }
    }

    @Test
    public void test() {

        String host = "s3.amazonaws.com";
        String bucketName = "bees360";
        String key = "project/10013/images/thumbnails/1517209967872.JPG";

        String bucketInPath = "https://" + host + "/" + bucketName + "/" + key;
        String bucketInHost = "https://" + bucketName + "." + host + "/" + key;

        S3Link s3Link = new S3Link(bucketInPath);

        assertEquals(host, s3Link.getS3Host());
        assertEquals(bucketName, s3Link.getBucketName());
        assertEquals(key, s3Link.getKey());

        assertEquals(bucketInPath, s3Link.toBucketInPathType());
        assertEquals(bucketInHost, s3Link.toBucketInHostType());
    }
}
