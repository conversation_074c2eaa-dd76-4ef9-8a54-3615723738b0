package com.bees360.util;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.io.IOException;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

/**
 * <AUTHOR>
 * TODO 需要重构IpWorkerUSA类，移除对网络的依赖
 */
@Disabled
public class IpWorkerUSATest {

	// 数据来自ftp://ftp.arin.net/pub/stats/arin/delegated-arin-extended-latest
	private static final String[] USA_IPS = {
		"**********",
		"**********",
		"***********",
		"************",
		"***********"
	};

	// baidu.com -- Beijing
	private static final String BAIDU_IP = "***************";

	private static IpWorkerUSA ipWorker;

    @TempDir
    public File temporaryFolder;

	public File getTempFolder() {
		return temporaryFolder;
	}

	@BeforeAll
	public static void beforeClass() {
		ipWorker = IpWorkerUSA.getInstance();
	}

	@Test
	public void testGetRangesSize() {
		assertTrue(ipWorker.getRangesSize() > 0);
	}

	@Test
	public void testIsUSAIp() {
		for(String str: USA_IPS) {
			assertTrue(ipWorker.isUSAIp(str));
		}
		assertFalse(ipWorker.isUSAIp(BAIDU_IP));
	}

	@Test
	public void testIsInRanges() {
		for(String str: USA_IPS) {
			assertTrue(ipWorker.isUSAIp(str));
		}
		assertFalse(ipWorker.isInRanges(BAIDU_IP));
	}

	@Test
	public void testIpRangToFile() throws IOException {
		boolean result = ipWorker.ipRangesToFile(new File(getTempFolder(), getClass().getSimpleName()));
		assertTrue(result);
	}
}
