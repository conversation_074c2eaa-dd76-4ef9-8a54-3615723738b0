package com.bees360.util.file;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;

import lombok.extern.log4j.Log4j2;
import net.coobird.thumbnailator.Thumbnails;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

/**
 * <AUTHOR>
 */
@Log4j2
public class ImageSizeReducerTest {

    private final String JPG_PATH = "image/roof.jpg";
    @TempDir
    public File folder;
    private File targetDir;
    private ClassLoader classLoader;

    @BeforeEach
    public void beforeEach() throws IOException {
        classLoader = getClass().getClassLoader();
        targetDir = newFolder(folder, this.getClass().getName());
    }

    @Test
    public void reduceToFile() throws IOException {
        ImageSizeReducer imageSizeReducer = new ImageSizeReducer(true);
        File jpg = new File(classLoader.getResource(JPG_PATH).getFile());
        File target = new File(targetDir, "reduce-" + jpg.getName());
        int limitSize = (int)(jpg.length() / 3);
        imageSizeReducer.reduce(jpg, target, limitSize);
        log.info(jpg + ": " + jpg.length() + " -> " + target.length());
        assertTrue(target.length() <= limitSize,
            "The size of result should lower than " + limitSize + ", but " + target.length());
    }

    @Test
    public void reduceToStream() throws IOException {
        ImageSizeReducer imageSizeReducer = new ImageSizeReducer(true);
        File jpg = new File(classLoader.getResource(JPG_PATH).getFile());
        int limitSize = (int)(jpg.length() / 3);
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            imageSizeReducer.reduce(jpg, out, limitSize);
            log.info(jpg + ": " + jpg.length() + " -> " + out.size());
            assertTrue(out.size() <= limitSize,
                "The size of result should lower than " + limitSize + ", but " + out.size());
        }
    }

    @Test
    public void testWithoutQuality() throws IOException {
        ImageSizeReducer imageSizeReducer = new ImageSizeReducer();
        File jpg = new File(classLoader.getResource(JPG_PATH).getFile());
        File target = new File(targetDir, "reduce-no-quality-" + jpg.getName());
        int limitSize = (int)(jpg.length() / 3);
        imageSizeReducer.reduce(jpg, target, limitSize);
        log.info(jpg + ": " + jpg.length() + " -> " + target.length());
        assertTrue(target.length() <= limitSize,
                "The size of result should lower than " + limitSize + ", but " + target.length());
    }

    private static File newFolder(File root, String... subDirs) throws IOException {
        String subFolder = String.join("/", subDirs);
        File result = new File(root, subFolder);
        if (!result.mkdirs()) {
            throw new IOException("Couldn't create folders " + root);
        }
        return result;
    }
}
