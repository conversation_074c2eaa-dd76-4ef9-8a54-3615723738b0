package com.bees360.util;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import java.io.File;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

@Disabled
public class IpWorkerCNTest {

    // 数据来自ftp://ftp.arin.net/pub/stats/arin/delegated-arin-extended-latest
    private static final String[] USA_IPS = {
        "**********",
        "**********",
        "***********",
        "************",
        "***********"
    };

    // baidu.com -- Beijing
    private static final String[] CN_IPS = {
        "***************"
    };

    private static IpWorkerCN ipWorker;

    @TempDir
    public File temporaryFolder;

    public File getTempFolder() {
        return temporaryFolder;
    }

    @BeforeAll
    public static void beforeClass() {
        ipWorker = IpWorkerCN.getInstance();
    }

    @Test
    public void getRangesSize() {
        assertTrue(ipWorker.getRangesSize() > 0);
    }

    @Test
    public void isInRanges() {
        for(String cnIp: CN_IPS) {
            assertTrue(ipWorker.isInRanges(cnIp));
        }
        for(String usaIp: USA_IPS) {
            assertFalse(ipWorker.isInRanges(usaIp));
        }
    }

    @Test
    public void isCNIp() {
        for(String cnIp: CN_IPS) {
            assertTrue(ipWorker.isCNIp(cnIp));
        }
        for(String usaIp: USA_IPS) {
            assertFalse(ipWorker.isCNIp(usaIp));
        }
    }

    @Test
    public void ipRangesToFile() throws IOException {
        boolean result = ipWorker.ipRangesToFile(new File(getTempFolder(), getClass().getSimpleName()));
        assertTrue(result);
    }
}
