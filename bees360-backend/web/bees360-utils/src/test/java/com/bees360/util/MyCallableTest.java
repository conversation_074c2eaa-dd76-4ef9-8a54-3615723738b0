package com.bees360.util;


import com.bees360.util.log.TimerLog;
import java.util.concurrent.Callable;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MyCallableTest implements Callable<Integer> {

    @Override
    public Integer call() throws Exception {
        TimerLog timerLog = TimerLog.getTimerLog(log);
        timerLog.printMinCost(500);
        timerLog.printErrorMinCost(1000);
        timerLog.start();
        Thread.sleep(600);
        timerLog.pause().print("thread timer pause. ${cost} ms");
        return 1;
    }
}
