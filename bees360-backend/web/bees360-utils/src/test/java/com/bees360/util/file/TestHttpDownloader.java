package com.bees360.util.file;

import java.io.File;
import java.io.IOException;

import org.junit.jupiter.api.io.TempDir;

public class TestHttpDownloader {

    @TempDir
    public File temporaryFolder;

	private File getTestDirectory() {
		return temporaryFolder;
	}

	// @Test
	public void testDownload() {
		String urlString = "http://ftp.apnic.net/apnic/stats/apnic/delegated-apnic-latest";
		String destfile = new File(getTestDirectory(), "delegated-apnic-latest").getAbsolutePath();
		try {
			HttpDownloader.download(urlString, destfile);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
