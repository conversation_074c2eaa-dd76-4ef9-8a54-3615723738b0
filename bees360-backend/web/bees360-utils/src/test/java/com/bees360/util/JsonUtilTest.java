package com.bees360.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class JsonUtilTest {

    @Test
    public void jsonToIntArr() {
        String ints = "[1, 2, 3, 4, 5]";
        int[] expectedInts = {1, 2, 3, 4, 5};

        int[] result = JsonUtil.jsonToIntArr(ints);

        assertArrayEquals(expectedInts, result);
    }

    @Test
    public void jsonToDoubleArr() {
        String doubles = "[-0.0066077746344137805,0.45244773588349274,0.8917664400549519,-25.004865083496174]";
        double[] expectedDs = {-0.0066077746344137805,0.45244773588349274,0.8917664400549519,-25.004865083496174};

        double[] result = JsonUtil.jsonToDoubleArr(doubles);
        // Only six digits can be reserved at most
        assertArrayEquals(expectedDs, result, 0.000001);
    }

    @Test
    public void jsonToDoubleArrays() {
        String doubless = "[[12.543128219540955,10.906445905383459,22.614180009698686],[0.778113954242499,-0.44517945096247263,28.27133427250522]]";
        double[][] expectedDss = {{12.543128219540955,10.906445905383459,22.614180009698686},
            {0.778113954242499,-0.44517945096247263,28.27133427250522}};

        double[][] result = JsonUtil.jsonToDoubleArrays(doubless);
        assertEquals(expectedDss.length, result.length);
        for(int i = 0; i < result.length; i ++) {
            // Only six digits can be reserved at most
            assertArrayEquals(expectedDss[i], result[i], 0.000001);
        }
    }
}
