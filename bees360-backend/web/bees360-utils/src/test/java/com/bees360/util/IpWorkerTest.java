package com.bees360.util;

import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.List;
import java.util.Scanner;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <p>
 * ref: https://www.nowcoder.com/profile/3566692/codeBookDetail?submissionId=57191868<br />
 *
 * 转化值参考<br />
 * https://www.bejson.com/convert/ip2int/ 这里转出来的值为long，不是int，需要将long转为int就可以了<br />
 * </p>
 * <AUTHOR>
 * @date 2019/09/06 11:16
 */
public class IpWorkerTest {

    private static final String UCR_IP = "**************";
    private static final int UCR_IP_INT = long2Int(2850756741L);
    // google.com -- Ireland
    private static final String GOOGLE_IP = "**********";
    private static final int GOOGLE_IP_INT = long2Int(520962561L);
    // baidu.com -- Beijing
    private static final String BAIDU_IP = "***************";
    private static final int BAIDU_IP_INT = long2Int(2071818896L);

    private static int long2Int(long value) {
        return (int)value;
    }

    @Test
    public void testIntToIp() {
        assertEquals(UCR_IP, IpWorker.intToIp(UCR_IP_INT));
        assertEquals(GOOGLE_IP, IpWorker.intToIp(GOOGLE_IP_INT));
        assertEquals(BAIDU_IP, IpWorker.intToIp(BAIDU_IP_INT));
    }

    @Test
    public void testIpToInt() throws UnknownHostException {
        assertEquals(UCR_IP_INT, IpWorker.ipToInt(UCR_IP));
        assertEquals(GOOGLE_IP_INT, IpWorker.ipToInt(GOOGLE_IP));
        assertEquals(BAIDU_IP_INT, IpWorker.ipToInt(BAIDU_IP));
    }

    @Test
    public void testGetLocalIP() throws SocketException {
        IpWorker.getLocalIP();
    }

    @Test
    public void testListLocalIPs() throws SocketException {
        List<String> ips = IpWorker.listLocalIPs();
        assertFalse(ips.isEmpty());
    }
}
