package com.bees360.utils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Iterator;

import org.apache.commons.io.FileUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class AnyTest {
	public static void main(String[] args) throws IOException {
		File f = new File("/var/bees360/www/maps/googlemaps/houseboudary/32.573831,-97.101361.png.json");
		JSONObject json = JSONObject.parseObject(FileUtils.readFileToString(f, Charset.forName("utf8")));
		JSONArray arr = json.getJSONArray("xs");
		for(int i = 0; i < arr.size(); i ++) {
			double d = Double.parseDouble(arr.getString(i));
			System.out.println(0.999999999999999999D);
			System.out.println(arr.getString(i));
			System.out.println(d);
			System.out.println(arr.get(i) + " -> " + arr.getDouble(i));
		}
		System.out.println();
	}
}
