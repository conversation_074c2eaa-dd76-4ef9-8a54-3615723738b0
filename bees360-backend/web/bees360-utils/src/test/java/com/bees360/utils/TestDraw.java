package com.bees360.utils;

import java.awt.Color;
import java.awt.Graphics;
import java.awt.Polygon;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

import javax.imageio.ImageIO;

import com.bees360.entity.enums.AnnotationTypeEnum;

public class TestDraw {

	public static void main(String[] arg) throws IOException {

		long start = System.currentTimeMillis();
		int count = 0;
		double ratio = 1;
		int x = 0;
		int y = 0;
		for (int i = 0; i < 100; i++) {
			String localFilePath = "/tmp/bees360/projects/10028/annotation_images/1515035711989.JPG";
			File file = new File(localFilePath);
			BufferedImage bfImage = new BufferedImage(400, 400, BufferedImage.TYPE_INT_ARGB);
			bfImage = ImageIO.read(file);
			Graphics g = bfImage.getGraphics();
			Polygon polygon = new Polygon();
			x = 40;
			y = 40;
			polygon.addPoint(x, y);
			x = 160;
			y = 40;
			polygon.addPoint(x, y);
			x = 160;
			y = 160;
			polygon.addPoint(x, y);
			x = 40;
			y = 160;
			polygon.addPoint(x, y);
			int typeCode = 3;
			String color = AnnotationTypeEnum.codeToColor(typeCode);
			g.setColor(Color.decode(color));
			g.drawPolygon(polygon);
			count++;
			long end = System.currentTimeMillis();
			if (count == 10 || count == 50 || count == 100) {
				System.out.println("count = " + count + "   Spend time = " + (end - start) + " ms");
			}
			ImageIO.write(bfImage, "JPG", file);
		}

	}

}
