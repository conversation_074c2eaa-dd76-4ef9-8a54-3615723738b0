package com.bees360.config;

import com.bees360.project.DefaultProjectServiceUpgradeManager;
import com.bees360.project.Message;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectServiceEndpoint;
import com.bees360.project.ProjectServiceUpgradeManager;
import com.bees360.project.group.DefaultProjectGroupUpgradedManager;
import com.bees360.project.group.GrpcProjectGroupManager;
import com.bees360.project.group.ProjectGroupUpgradedManager;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.service.ProjectService;
import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.function.BiPredicate;

@Import({
    ProjectServiceEndpoint.class,
})
@Configuration
@ConditionalOnProperty(prefix = "project.service-upgrade", name = "enabled", havingValue = "true")
public class ProjectServiceUpgradedConfig {

    @Data
    static class ServiceUpgradeProperties {
        private Map<Integer, Set<Integer>> serviceTypeMapping = new HashMap<>();
        private String reopenChangeReason;
        private Set<String> updateStatusImageUploadedIfNotIn;
    }

    @Bean
    @ConfigurationProperties(prefix = "project.service-upgrade")
    ServiceUpgradeProperties serviceUpgradeProperties() {
        return new ServiceUpgradeProperties();
    }

    @Bean
    DefaultProjectGroupUpgradedManager defaultProjectGroupUpgradedManager(GrpcProjectGroupManager grpcProjectGroupManager) {
        return new DefaultProjectGroupUpgradedManager(grpcProjectGroupManager);
    }

    @Bean
    ProjectServiceUpgradeManager defaultProjectServiceUpgradeManager(
        ProjectIIManager projectIIManager,
        ProjectService projectService,
        ServiceUpgradeProperties properties,
        ProjectGroupUpgradedManager defaultProjectGroupUpgradedManager,
        ProjectStateManager projectStateManager,
        ProjectStatusManager projectStatusManager) {
        BiPredicate<Integer, Integer> upgradeServiceTypeCondition = (from, to) -> {
            var tos = properties.getServiceTypeMapping().get(from);
            return tos != null && tos.contains(to);
        };

        Set<Message.ProjectStatus> imageUploadedIfNotIn = new HashSet<>();
        if (properties.getUpdateStatusImageUploadedIfNotIn() != null) {
            properties.getUpdateStatusImageUploadedIfNotIn().forEach(name -> {
                imageUploadedIfNotIn.add(Message.ProjectStatus.valueOf(name));
            });
        }
        return new DefaultProjectServiceUpgradeManager(
            projectIIManager,
            projectService,
            upgradeServiceTypeCondition,
            defaultProjectGroupUpgradedManager,
            projectStateManager,
            projectStatusManager,
            imageUploadedIfNotIn,
            properties.getReopenChangeReason()
        );
    }
}
