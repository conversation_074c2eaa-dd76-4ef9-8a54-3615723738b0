package com.bees360.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import com.bees360.entity.converter.CompanyBeanConverter;
import com.bees360.entity.vo.CompanyVo;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bees360.base.MapBuilder;
import com.bees360.config.support.CurUserId;
import com.bees360.entity.Company;
import com.bees360.entity.User;
import com.bees360.service.CompanyService;
import com.bees360.service.UserService;

@RestController
@RequestMapping("/companies")
public class CompanyController {

	@Inject
	private CompanyService companyService;

	@Inject
	private UserService userService;

	/**
	 * list all companies
	 */
	@GetMapping("/default")
	public Map<String, Object> getDefaultCompany() throws Exception {
		Company	company = companyService.getDefaultCompany();
		String companyName = company == null ? "" : company.getCompanyName();
		return MapBuilder.result("companyName", companyName);
	}

	/**
	 * list all companies
	 */
	@GetMapping("")
	public List<CompanyVo> list(Integer type, String prefix, Integer number) throws Exception {
		List<Company> companies = companyService.listByTypeWithPrefix(type, prefix, number);
		return toCompanyVos(companies);
	}

	/**
	 * list companies with comanyId
	 */
	@GetMapping("/query")
	public List<CompanyVo> query(@CurUserId long userId, Integer type, String prefix, Integer number) throws Exception {
		List<Company> companyList = companyService.listByTypeWithPrefix(type, prefix, number);
		return toCompanyVos(companyList);
	}

	/**
	 * get companies start with prefix limited 20
	 */
//	<EMAIL>: used when frontend need
	@GetMapping("/some")
	public List<CompanyVo> listRelatedCompany(String prefix) throws Exception {
		prefix = prefix == null? "": prefix;
		List<Company> companies = companyService.listWithPrefix(prefix);
		return toCompanyVos(companies);
	}

	private List<CompanyVo> toCompanyVos(List<Company> companies) {
		return CompanyBeanConverter.toCompanyVoList(companies);
	}

    @GetMapping("/user/company")
    public CompanyVo getUserCompany(@CurUserId long userId) throws Exception {
        User user = userService.getUserById(userId);
        if (user == null || user.getCompanyId() == null) {
            return null;
        }
        Company company = companyService.getById(user.getCompanyId());
        CompanyVo companyVo = new CompanyVo();
        BeanUtils.copyProperties(company, companyVo);
        return companyVo;
    }
}
