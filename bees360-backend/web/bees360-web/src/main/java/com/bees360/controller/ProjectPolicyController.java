package com.bees360.controller;

import com.bees360.base.exception.ServiceException;
import com.bees360.project.ProjectIIRepository;
import com.bees360.service.ProjectService;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Log4j2
@RestController
@RequestMapping("project")
public class ProjectPolicyController {

    private final ProjectService projectService;
    private final ProjectIIRepository projectIIRepository;

    public ProjectPolicyController(ProjectService projectService, ProjectIIRepository projectIIRepository) {
        this.projectService = projectService;
        this.projectIIRepository = projectIIRepository;
        log.info("Created {}", this);
    }

    @PutMapping("/{projectId:\\d+}/policy/policy-type")
    public UpdatePolicyTypeDto updatePolicyType(@PathVariable long projectId, @RequestBody UpdatePolicyTypeDto request) throws ServiceException {
        var project = projectService.updatePolicyTypeAndPropertyType(projectId, request.policyType, request.propertyType);

        var policy = project.getPolicy();

        var dto = new UpdatePolicyTypeDto();
        dto.setPolicyType(policy.getType());
        dto.setPropertyType(policy.getBuilding().getType().getNumber());
        return dto;
    }

    @Data
    public static class UpdatePolicyTypeDto {
        private String policyType;
        private Integer propertyType;
    }
}
