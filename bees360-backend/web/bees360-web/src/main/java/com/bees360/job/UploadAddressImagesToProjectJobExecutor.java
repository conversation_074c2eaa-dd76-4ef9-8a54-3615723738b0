package com.bees360.job;

import com.bees360.address.Address;
import com.bees360.address.AddressImageResourceProvider;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.AiBotUserEnum;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.PipelineTaskEnum;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.ImageTagManager;
import com.bees360.job.registry.UploadAddressImageToProjectJob;
import com.bees360.job.util.AbstractAsyncJobExecutor;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.policy.Policy;
import com.bees360.project.ProjectIIRepository;
import com.bees360.resource.Resource;
import com.bees360.service.ProjectImageService;
import com.bees360.service.ProjectStatusService;
import com.bees360.util.Iterables;
import com.google.common.base.Preconditions;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.imaging.ImageReadException;
import org.apache.commons.imaging.Imaging;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.checkerframework.checker.nullness.qual.Nullable;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
public class UploadAddressImagesToProjectJobExecutor
    extends AbstractAsyncJobExecutor<UploadAddressImageToProjectJob> {
    public static final AiBotUserEnum ROBOT_USER = AiBotUserEnum.AI_NEW_USER_ID;
    public static final PipelineTaskEnum PIPELINE_TASK = PipelineTaskEnum.UPLOAD_ADDRESS_IMAGES;
    public static final NewProjectStatusEnum COMPLETE_PROJECT_STATUS =
        NewProjectStatusEnum.IMAGE_UPLOADED;
    private final ProjectIIRepository projectIIRepository;
    private final AddressImageResourceProvider addressImageResourceProvider;
    private final ProjectImageService projectImageService;
    private final ImageTagManager imageTagManager;
    private final PipelineService pipelineService;
    private final ProjectStatusService projectStatusService;
    /** 上传Resource并且返回按照一定规则生成的ResourceKey */
    private final Function<Resource, String> resourceUploader;
    /** 根据Resource生成对应的FileName,与Uploader的命名规则要保持一致, 用于过滤已经上传的AddressImages */
    private final Function<Resource, String> resourceImageFileNameConverter;

    private final FileSourceTypeEnum addressImageFileSourceType;
    private final ImageTypeEnum addressImageType;
    private final Iterable<ImageTagEnum> addressImageTags;

    public UploadAddressImagesToProjectJobExecutor(
        ProjectIIRepository projectIIRepository,
        AddressImageResourceProvider addressImageResourceProvider,
        ProjectImageService projectImageService,
        ImageTagManager imageTagManager,
        PipelineService pipelineService,
        ProjectStatusService projectStatusService,
        Function<Resource, String> resourceUploader,
        Function<Resource, String> resourceImageFileNameConverter,
        FileSourceTypeEnum addressImageFileSourceType,
        ImageTypeEnum addressImageType,
        Iterable<ImageTagEnum> addressImageTags) {
        this.projectIIRepository = projectIIRepository;
        this.addressImageResourceProvider = addressImageResourceProvider;
        this.projectImageService = projectImageService;
        this.imageTagManager = imageTagManager;
        this.pipelineService = pipelineService;
        this.projectStatusService = projectStatusService;
        this.resourceUploader = resourceUploader;
        this.resourceImageFileNameConverter = resourceImageFileNameConverter;
        this.addressImageFileSourceType = addressImageFileSourceType;
        this.addressImageType = addressImageType;
        this.addressImageTags = addressImageTags;
        log.info(
            "Created {}(projectIIRepository={}, addressImageResourceProvider={},"
                + " projectImageService={}, imageTagManager={}, pipelineService={},"
                + " projectStatusService={}, resourceUploader={},"
                + " resourceImageFileNameConverter={}, addressImageFileSourceType={},"
                + " addressImageType={}, addressImageTags={}).",
            this,
            this.projectIIRepository,
            this.addressImageResourceProvider,
            this.projectImageService,
            this.imageTagManager,
            this.pipelineService,
            this.projectStatusService,
            this.resourceUploader,
            this.resourceImageFileNameConverter,
            this.addressImageFileSourceType,
            this.addressImageType,
            this.addressImageTags);
    }

    @Override
    protected ListenableFuture<Void> accept(UploadAddressImageToProjectJob job) {
        var projectId = job.getProjectId();
        var taskKey = PIPELINE_TASK.getKey();
        var future =
            Futures.submit(
                () -> doUploadAddressImageToProject(job), MoreExecutors.directExecutor());
        // PipelineTaskStatus回调
        Futures.addCallback(
            future,
            new FutureCallback<>() {
                @Override
                public void onSuccess(@Nullable Void result) {
                    log.info(
                        "Set the project {} pipeline task {} to Done after uploaded all"
                            + " address images.",
                        projectId,
                        taskKey);
                    pipelineService.setTaskStatus(
                        projectId, taskKey, Message.PipelineStatus.DONE);
                }

                @Override
                public void onFailure(Throwable t) {
                    var message =
                        "Fail to upload address images for project %s: %s".formatted(
                            projectId, t.getMessage());
                    pipelineService.setTaskStatus(
                        projectId, taskKey, Message.PipelineStatus.ERROR, message);
                }
            },
            MoreExecutors.directExecutor());
        // ProjectStatus回调
        Futures.addCallback(
            future,
            new FutureCallback<>() {
                @Override
                public void onSuccess(@Nullable Void result) {
                    log.info(
                        "Set project {} status to {} after uploaded all address images.",
                        projectId,
                        COMPLETE_PROJECT_STATUS);
                    projectStatusService.changeOnImageUploaded(
                        ROBOT_USER.getIntegerCode().longValue(), Long.parseLong(projectId));
                }

                @Override
                public void onFailure(Throwable t) {
                    log.warn(
                        "Fail to upload address images for project {} in job {}.",
                        projectId,
                        job,
                        t);
                }
            },
            MoreExecutors.directExecutor());
        return future;
    }

    private void doUploadAddressImageToProject(UploadAddressImageToProjectJob job) {
        log.info(
            "Start to execute upload address images to project job {} by user {}.",
            job,
            ROBOT_USER);
        var projectId = job.getProjectId();
        var projectIdValue = Long.parseLong(projectId);
        try {
            var project = projectIIRepository.get(projectId);
            var addressId =
                Optional.ofNullable(project.getPolicy())
                    .map(Policy::getAddress)
                    .map(Address::getId)
                    .orElse(null);
            if (StringUtils.isEmpty(addressId)) {
                throw new IllegalStateException(
                    "The project %s is without any address.".formatted(projectId));
            }
            // 获取AddressImageResources
            var imageResources = addressImageResourceProvider.getAddressImages(addressId);
            imageResources = filterUploadedAddressImages(projectIdValue, imageResources);
            if (com.google.common.collect.Iterables.isEmpty(imageResources)) {
                log.info("All Address images for projectId {} already uploaded.", projectId);
                return;
            }

            // 保存AddressImageResources并打上Tag
            var projectImages =
                saveAddressImageResourcesToProject(
                    projectIdValue,
                    imageResources,
                    ROBOT_USER.getIntegerCode().longValue());
            var tags = Iterables.transform(addressImageTags, t -> String.valueOf(t.getCode()));
            var imageWithTags =
                Iterables.toStream(projectImages)
                    .collect(Collectors.toMap(ProjectImage::getImageId, k -> tags));
            imageTagManager.addAllImageTag(imageWithTags, ROBOT_USER.getCode());
        } catch (ServiceException e) {
            var message = "Fail to upload address images for project %s".formatted(projectId);
            throw new IllegalStateException(message, e);
        }
    }

    private Iterable<? extends Resource> filterUploadedAddressImages(
        Long projectId, Iterable<? extends Resource> imageResources) throws ServiceException {
        var uploadedImages =
            projectImageService
                .listImageByImageType(
                    projectId, addressImageFileSourceType, addressImageType)
                .stream()
                .map(ProjectImage::getOriginalFileName)
                .collect(Collectors.toList());
        // 剔除Uploaded Address Images
        return Iterables.filter(
            imageResources,
            resource ->
                !uploadedImages.contains(resourceImageFileNameConverter.apply(resource)));
    }

    private Iterable<ProjectImage> saveAddressImageResourcesToProject(
        Long projectId, Iterable<? extends Resource> imageResources, Long userId)
        throws ServiceException {
        List<Pair<String, ? extends Resource>> addressImageSources = new ArrayList<>();
        // 保存Address Images
        imageResources.forEach(
            resource -> {
                var key = resourceUploader.apply(resource);
                addressImageSources.add(Pair.of(key, resource));
            });
        // 转换为ProjectImage
        var projectImages =
            Iterables.toStream(addressImageSources)
                .map(
                    source ->
                        imageToProjectImage(
                            projectId,
                            source.getKey(),
                            source.getValue(),
                            userId))
                .collect(Collectors.toList());
        var responseDto =
            projectImageService.uploadImages(
                projectId, userId, addressImageFileSourceType, projectImages, false);
        return responseDto.getSuccessImages();
    }

    private ProjectImage imageToProjectImage(
        long projectId, String resourceKey, Resource imageResource, long userId) {
        log.info(
            "Start convert address image resource {} to project {} image.",
            resourceKey,
            projectId);
        var projectImage = ProjectImage.defaultImage();
        // 读取Resource设置相关属性
        var contentLength = imageResource.getMetadata().getContentLength();
        Preconditions.checkState(
            Objects.nonNull(contentLength),
            "The address image {} content length is null.",
            resourceKey);
        projectImage.setFileSize(imageResource.getMetadata().getContentLength());

        var imageSize =
            imageResource.apply(
                in -> {
                    try {
                        return Imaging.getImageSize(in, resourceKey);
                    } catch (ImageReadException e) {
                        throw new IllegalStateException(
                            "Cannot read project %s address image %s".formatted(
                                projectId, resourceKey),
                            e);
                    }
                });
        projectImage.setImageHeight(imageSize.height);
        projectImage.setImageWidth(imageSize.width);
        // FileName统一设置为ResourceKey
        projectImage.setFileName(resourceKey);
        projectImage.setFileNameLowerResolution(resourceKey);
        projectImage.setFileNameMiddleResolution(resourceKey);
        projectImage.setOriginalFileName(resourceImageFileNameConverter.apply(imageResource));
        // 设置ProjectImage关联属性
        projectImage.setProjectId(projectId);
        projectImage.setUserId(userId);
        projectImage.setFileSourceType(addressImageFileSourceType.getCode());
        projectImage.setImageType(addressImageType.getCode());
        projectImage.setShootingTime(Instant.now().toEpochMilli());
        log.info(
            "Convert address image resource {} to project {} image successfully(width={},"
                + " height={}, size={}).",
            resourceKey,
            projectId,
            projectImage.getImageWidth(),
            projectImage.getImageHeight(),
            projectImage.getFileSize());
        return projectImage;
    }
}
