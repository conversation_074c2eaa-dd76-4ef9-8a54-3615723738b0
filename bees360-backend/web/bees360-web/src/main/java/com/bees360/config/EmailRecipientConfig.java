package com.bees360.config;

import com.bees360.entity.enums.CreationChannelType;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.mapper.ProjectCreationMapper;
import com.bees360.project.member.RoleEnum;
import com.bees360.service.UserService;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Log4j2
@Configuration
public class EmailRecipientConfig {

    @Bean
    @ConditionalOnProperty(prefix = "app.web.project.mail.recipient.provider", name = "enabled", havingValue = "true")
    public Function<Long, Iterable<UserTinyVo>> projectMemberMailRecipientProvider(
        UserService userService,
        ProjectCreationMapper projectCreationMapper,
        @Value("${app.web.project.mail.recipient.member-role:CREATOR}") Set<RoleEnum> recipientMemberRoles) {

        var recipientRoles = recipientMemberRoles.stream().map(RoleEnum::getCode).collect(Collectors.toList());

        log.info("Created projectMemberMailRecipientProvider(userService={},recipientMemberRoles={})",
            userService, recipientMemberRoles);
        return projectId -> {
            var projectCreation = projectCreationMapper.getByProjectId(projectId);
            var isNotCreatedFromWeb = projectCreation != null && !CreationChannelType.WEB.name().equals(projectCreation.getCreationChannel());
            List<UserTinyVo> members = userService.listActiveMemberInProject(projectId);
            return members.stream()
                .filter(m -> recipientRoles.contains(m.getRoleId()))
                .filter(m -> StringUtils.isNotEmpty(m.getEmail()))
                .filter(m -> !(isNotCreatedFromWeb && StringUtils.endsWith(m.getEmail(), "@bees360.com")))
                .collect(Collectors.toList());
        };
    }
}
