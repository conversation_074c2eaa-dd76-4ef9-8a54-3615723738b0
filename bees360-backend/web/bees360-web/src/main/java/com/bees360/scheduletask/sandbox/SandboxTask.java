package com.bees360.scheduletask.sandbox;

import com.bees360.base.exception.ServiceException;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.util.UUIDUtil;
import com.bees360.entity.Company;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.User;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.bees360.entity.openapi.reportsummary.SummaryImage;
import com.bees360.entity.openapi.reportsummary.SummaryRecommendation;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.project.state.ProjectStateChangeReasonManager;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.report.Message;
import com.bees360.report.ReportManager;
import com.bees360.scheduletask.sandbox.sample.SandboxSample;
import com.bees360.scheduletask.sandbox.sample.SandboxSampleManager;
import com.bees360.scheduletask.sandbox.sample.SandboxSampleResource;
import com.bees360.service.CompanyService;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectReportService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.openapi.converter.ReportSummaryConvert;
import com.bees360.util.schedule.annotation.DistributedScheduled;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.cache.RemovalCause;
import com.google.common.cache.RemovalListener;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.zalando.fauxpas.ThrowingConsumer;

@Slf4j
@Component
@DistributedScheduled
@ConditionalOnProperty(prefix = "web.app.sandbox-task", name = "enabled", havingValue = "true")
public class SandboxTask implements InitializingBean {

    private static final String SANDBOX_PROVIDED_REPORT_INSPECTION_NUMBER_PREFIX = "report_provided";
    private static final String SANDBOX_CLOSEOUT_REPORT_INSPECTION_NUMBER_PREFIX = "closeout_report"; //inspection_number为此前缀的project会被识别为需要自动生成closeout report的project
    private static final long EXEC_INTERVAL = 42 * 1000;
    private static final long OP_DELAY = 60 * 1000;

    private static final Set<Integer> PROJECT_STATUS_EXCLUDED = ImmutableSet.of(
        NewProjectStatusEnum.RETURNED_TO_CLIENT.getCode(), NewProjectStatusEnum.PROJECT_CANCELED.getCode(),
        NewProjectStatusEnum.CLIENT_RECEIVED.getCode(), NewProjectStatusEnum.RECEIVE_ERROR.getCode());

    private List<ProjectImage> sandboxImages;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectReportFileService projectReportFileService;

    @Autowired
    private ProjectReportService projectReportService;

    @Autowired
    private ProjectImageMapper projectImageMapper;

    @Autowired
    private SandboxSampleManager sandboxSampleManager;

    @Autowired
    private Supplier<ProjectStateManager> projectStateManagerSupplier;

    @Autowired
    private ProjectStateChangeReasonManager projectStateChangeReasonManager;

    @Autowired
    private ProjectReportProvider projectReportProvider;

    @Autowired
    private ReportManager reportManager;

    @Autowired
    private ProjectStatusService projectStatusService;

    private static LoadingCache<Long, AtomicInteger> projectExceptionCounterCache = newProjectExceptionCounterCache();

    /**
     * 当同一个项目异常次数达到该值，则输出error日志。
     */
    private final int LOG_ERROR_PROJECT_EXCEPTION_COUNT = 3;

    /**
     * 用于统计处理失败的Project失败次数
     */
    private static LoadingCache<Long, AtomicInteger> newProjectExceptionCounterCache() {
        final int CACHE_MAX_SIZE = 1000;

        final RemovalListener<Long, AtomicInteger> logWhenRemoveListener = (removalNotification) -> {
            RemovalCause removalCause = removalNotification.getCause();
            Long key = removalNotification.getKey();
            int value = removalNotification.getValue().intValue();
            switch (removalCause) {
                case SIZE: {
                    log.error("Cache entry ({}, {}) is removed since the cache is out of capacity whose max size is {}.",
                        key, value, CACHE_MAX_SIZE);
                    return;
                }
                case EXPIRED: {
                    log.error("Cache entry ({}, {}) is removed since it's expired, " +
                            "resulting the project can't be processed properly.", key, value);
                    return;
                }
                default: {
                    log.debug("Cache entry ({}, {}) is removed since {}.", key, value, removalCause);
                }
            }
        };

        return CacheBuilder.newBuilder()
            // 目前仅仅系统测试，或者客户的基本测试使用到，1000的量完全够用
            .maximumSize(CACHE_MAX_SIZE)
            // 按照任务的执行，20分钟之内基本可以重试完成
            .expireAfterAccess(Duration.ofMinutes(20))
            .removalListener(logWhenRemoveListener)
            .build(new CacheLoader<Long, AtomicInteger>() {
                @Override
                public AtomicInteger load(Long key) {
                    // 不抛出异常，方便使用getUnchecked
                    return new AtomicInteger();
                }
            });
    }

    /**
     * 设置项目的状态为return_to_client，并上传报告和图片压缩包。利用User.AI_ID用户对报告进行支付，是的用户可以直接拿到项目资源。
     *
     * @throws ServiceException
     */
    @Scheduled(
        initialDelayString = "${scheduler.spring-scheduled.start-up-delay:-}",
        fixedRate = EXEC_INTERVAL)
    public void completeAutoProvidedProjectAndAddResourceForIt() throws ServiceException {
        log.info("Run scheduled completeAutoProvidedProjectAndAddResourceForIt");
        final List<Project> projects = getProjects(SANDBOX_PROVIDED_REPORT_INSPECTION_NUMBER_PREFIX, PROJECT_STATUS_EXCLUDED);

        processAutoProjects(projects, project -> {
            final Company company = companyService.getByUserId(project.getCreatedBy());
            final SandboxSample sample = sandboxSampleManager.getSampleResource(company, project);
            if (sample == null) {
                log.info("Not Sample found for project {} with company {}.", project.getProjectId(), company.getCompanyId());
                return;
            }
            addImages(project, sample.getProjectImages());
            final List<SandboxSampleResource.ReportResource> sampleReport = sample.getReport();

            if (sampleReport == null || sampleReport.isEmpty()) {
                log.info("Not Sample reports found for project {}.", project.getProjectId());
                return;
            }

            Set<ReportTypeEnum> reportTypes = ProjectServiceTypeEnum.getEnum(project.getServiceType()).getReportTypes();

            for (ReportTypeEnum reportType : reportTypes) {
                var reportResource = sample.getReport().get(0);
                var summary = sample.getReportSummaries().get(reportType);
                createThenApproveReport(project, reportType, reportResource, summary);
                log.info("sandbox: update status and add report for project {}", project.getProjectId());
            }
            if (projectService.projectServiceIsCompleted(project.getProjectId())) {
                projectStatusService.changeOnReturnedToClient(User.AI_ID, project.getProjectId(), "Change the status to Returned to Client because the project ");
            }
        });
    }

    private void createThenApproveReport(
            Project project,
            ReportTypeEnum reportType,
            SandboxSampleResource.ReportResource reportResource,
            ReportSummaryVo summary) throws ServiceException {
        var operatorUserId = User.AI_ID;
        var reportId = createReport(project, reportType, reportResource, summary, operatorUserId);
        approvedReport(project.getProjectId(), reportId, operatorUserId);
    }

    private String createReport(Project project, ReportTypeEnum reportType,
                                SandboxSampleResource.ReportResource sampleReport, ReportSummaryVo summary, long operatorUserId) {
        var report = Iterables.getFirst(projectReportProvider.find(project.getProjectId() + "", reportType.getCode() + "", null), null);
        if (report != null) {
            return report.getId();
        }
        var projectId = project.getProjectId();
        var summaryJson = createSummaryJson(projectId, summary);

        var reportFile = new ProjectReportFile();
        reportFile.setProjectId(projectId);
        reportFile.setReportType(reportType.getCode());
        reportFile.setReportPdfFileName(sampleReport.getReport());
        reportFile.setReportPdfCompressed(sampleReport.getReportCompressed());
        reportFile.setSize(sampleReport.getSize());
        reportFile.setSizeCompressed(sampleReport.getSizeCompressed());
        reportFile.setCreatedBy(operatorUserId);
        reportFile.setCreatedTime(System.currentTimeMillis());
        reportFile.setGenerationStatus(ReportGenerationStatusEnum.GENERATED);

        return projectReportFileService.save(reportFile, summaryJson);
    }

    private void approvedReport(
            long projectId,
            String reportId,
            long operatorUserId) throws ServiceException {
        var report = reportManager.findById(reportId);
        if(report.getStatus() == Message.ReportMessage.Status.GENERATED) {
            projectReportFileService.submitReport(projectId, operatorUserId, report.getId());
            report = reportManager.findById(reportId);
        }
        if(report.getStatus() != Message.ReportMessage.Status.APPROVED) {
            // 存在未审核的报告，进行审核
            reportManager.updateStatus(report.getId(), Message.ReportMessage.Status.APPROVED, String.valueOf(operatorUserId));
            // 正常触发report approved后的逻辑
            projectReportFileService.approveReport(projectId, operatorUserId, report.getId());
        }
    }

    private String createSummaryJson(long projectId, ReportSummaryVo summary) {
        if (summary == null) {
            return "{}";
        }
        assembleRecommendations(projectId, summary);
        return ReportSummaryConvert.toJson(summary);
    }

    /**
     * 定时将 inspection_number前缀 为 "report_closeout" 的 project 关闭，以自动生成 Closeout Report
     *
     * @throws ServiceException
     */
    @Scheduled(
        initialDelayString = "${scheduler.spring-scheduled.start-up-delay:-}",
        fixedRate = EXEC_INTERVAL)
    public void completeAutoCloseoutProjectAndAddResourceForIt() throws ServiceException {
        log.info("Run scheduled completeAutoCloseoutProjectAndAddResourceForIt");
        final List<Project> projects = getProjects(SANDBOX_CLOSEOUT_REPORT_INSPECTION_NUMBER_PREFIX, PROJECT_STATUS_EXCLUDED);
        final String autoChangeReason = "DENIED";
        var projectStateChangeReason = com.bees360.util.Iterables.toStream(
            projectStateChangeReasonManager
                .findByQuery(List.of(), List.of(autoChangeReason), List.of())).findFirst().orElse(null);
        if (Objects.isNull(projectStateChangeReason)) {
            throw new IllegalStateException("No change reason with key %s found".formatted(autoChangeReason));
        }
        processAutoProjects(projects, project -> {
            //close the project
            projectStateManagerSupplier.get().changeProjectState(String.valueOf(project.getProjectId()), ProjectStateEnum.PROJECT_CLOSE,
                projectStateChangeReason.getId(), String.valueOf(User.AI_ID));
            log.info("sandbox: project {} was closed", project.getProjectId());
        });
    }

    private void processAutoProjects(List<Project> projects, ThrowingConsumer<Project, Exception> projectConsumer) {
        for (Project project : projects) {
            try {
                projectConsumer.tryAccept(project);

                projectExceptionCounterCache.invalidate(project.getProjectId());
            } catch (Exception e) {
                int exceptionCount = projectExceptionCounterCache.getUnchecked(project.getProjectId()).incrementAndGet();
                if (exceptionCount >= LOG_ERROR_PROJECT_EXCEPTION_COUNT) {
                    log.error("unable to complete project ({}).", project.getProjectId(), e);
                } else {
                    log.warn("unable to complete project ({}).", project.getProjectId(), e);
                }
            }
        }
    }

    private void assembleRecommendations(long projectId, ReportSummaryVo reportSummaryVo) {
        final int IMAGE_COUNT_MAX = 6;
        List<SummaryRecommendation> recommendations = new ArrayList<>();
        SummaryRecommendation recommendation = new SummaryRecommendation();
        recommendation.setText("Removal of Diving Board/Slides");

        List<ProjectImage> images = projectImageMapper.listAll(projectId);
        int count = Math.min(IMAGE_COUNT_MAX, images.size());
        List<SummaryImage> summaryImages = new ArrayList<>();
        for(int i = 0; i < count; i ++) {
            SummaryImage summaryImage = new SummaryImage();
            summaryImage.setId(images.get(i).getImageId() + "");
            summaryImages.add(summaryImage);
        }
        recommendation.setImage(summaryImages);
        recommendations.add(recommendation);

        reportSummaryVo.setRecommendations(recommendations);
    }

    /**
     * add SandboxTask by shoushan 目前这个地方正在重构，后期会废除
     */
    @Deprecated
    private void addImages(Project project, List<ProjectImage> images) throws Exception {
        if (images == null || images.isEmpty()){
            return;
        }
        final List<ProjectImage> uploadedImages = projectImageMapper.getImageByProjectId(project.getProjectId());
        final Set<String> uploadedImageNames = uploadedImages.stream()
            .map(ProjectImage::getFileName)
            .collect(Collectors.toSet());

        final List<ProjectImage> imagesToInsert = images.stream()
            .filter(image -> !uploadedImageNames.contains(image.getFileName()))
            .collect(Collectors.toList());

        if (!CollectionAssistant.isEmpty(imagesToInsert)) {
            imagesToInsert.forEach(image -> image.setImageId(UUIDUtil.getImageUUID()));
            projectImageMapper.insertBaseInfoList(imagesToInsert);
        }
    }

    private List<Project> getProjects(String sandboxInspectionNumberPrefix, Set<Integer> projectStatusExcluded) {
        final List<Project> projects = projectMapper.listByInspectionNumberStartWith(sandboxInspectionNumberPrefix,
            projectStatusExcluded);

        List<Project> resultProjects = projects.stream().filter(p -> p.getServiceType() != null).collect(Collectors.toList());

        log.info("find {} projects with inspection number starting with ({}) and {} will be processed.",
            projects.size(), sandboxInspectionNumberPrefix, resultProjects.size());

        return resultProjects;
    }

    @Override
    public void afterPropertiesSet() {
        log.info("SandboxTask is enable.");
    }
}
