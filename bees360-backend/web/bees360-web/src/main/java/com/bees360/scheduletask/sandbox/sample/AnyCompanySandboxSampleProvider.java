package com.bees360.scheduletask.sandbox.sample;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Company;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.User;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Order(Ordered.LOWEST_PRECEDENCE)
@Component
public class AnyCompanySandboxSampleProvider extends ClassPathJsonImageSampleProvider {

    private static final Gson gson = new Gson();

    public AnyCompanySandboxSampleProvider() throws ServiceException {
        super();
    }

    @Override
    public boolean supports(Company company) {
        return company != null;
    }

    @Override
    protected String getJsonPath() {
        return SandboxSampleResource.COMMON_JSON_IMAGE_FILE_PATH;
    }

    @Override
    protected SandboxSampleResource.FileResource getImageArchive() {
        return SandboxSampleResource.CommonResources.IMAGE_ARCHIVE;
    }

    @Override
    protected List<SandboxSampleResource.ReportResource> getReports() {
        return Arrays.asList(
            SandboxSampleResource.CommonResources.REPORT_FULL_SCOPE_UNDERWRITING,
            SandboxSampleResource.CommonResources.REPORT_PREMIUM_DAMAGE_ASSESSMENT,
            SandboxSampleResource.CommonResources.REPORT_PREMIUM_ROOF_MEASUREMENT,
            SandboxSampleResource.CommonResources.REPORT_ROOF_ONLY_UNDERWRITING);
    }

    @Override
    protected List<ProjectImage> getImages(long projectId) {

        return  getImageSamples().stream()
            .peek(image -> {
                image.setProjectId(projectId);
                image.setUserId(User.AI_ID);
                image.setUploadTime(System.currentTimeMillis());
                image.setFileName(image.getFileName());
                image.setFileNameMiddleResolution(image.getFileNameMiddleResolution());
                image.setFileNameLowerResolution(image.getFileNameLowerResolution());
            })
            .collect(Collectors.toList());
    }

    @Override
    protected Map<ReportTypeEnum, ReportSummaryVo> getReportSummaries() {
        Map<ReportTypeEnum, ReportSummaryVo> reportSummaryMap = new HashMap<>();
        String summary = SandboxSampleResource.getReportSummaryJson();

        Arrays.stream(ReportTypeEnum.values()).forEach(
            r -> reportSummaryMap.put(r, gson.fromJson(summary, ReportSummaryVo.class)));

        return reportSummaryMap;
    }
}
