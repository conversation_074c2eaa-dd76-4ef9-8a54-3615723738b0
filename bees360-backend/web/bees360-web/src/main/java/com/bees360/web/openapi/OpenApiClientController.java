package com.bees360.web.openapi;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.config.support.CurUserId;
import com.bees360.entity.User;
import com.bees360.entity.openapi.client.OpenApiClient;
import com.bees360.entity.openapi.client.OpenApiClientApplyDto;
import com.bees360.entity.openapi.client.OpenApiClientUpdateDto;
import com.bees360.entity.openapi.client.OpenApiClientVo;
import com.bees360.entity.openapi.client.OpenApiSearchFilterOption;
import com.bees360.service.CompanyService;
import com.bees360.service.UserService;
import com.bees360.service.openapi.OpenApiClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/apis/credentials")
public class OpenApiClientController {

    @Autowired
    private OpenApiClientService openApiClientService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private UserService userService;


    @PutMapping("/{clientId}")
    public OpenApiClientVo updateClient(@CurUserId long userId, @PathVariable String clientId, @Validated @RequestBody OpenApiClientUpdateDto updateDto) throws ServiceException {

        checkUserClient(userId, clientId);

        return openApiClientService.update(userId, clientId, updateDto);
    }

    @DeleteMapping("/{clientId}")
    public void deleteClient(@CurUserId long userId, @PathVariable String clientId) throws ServiceException {

        checkUserClient(userId, clientId);

        openApiClientService.deleteByClientId(clientId);
    }

    /**
     * Company Admin 申请 OAuth Client
     * @param userId
     * @param secretDto
     * @return
     * @throws ServiceException
     */
    @PostMapping("")
    public OpenApiClientVo applyClient(@CurUserId long userId, @Validated @RequestBody OpenApiClientApplyDto secretDto) throws ServiceException {

        return openApiClientService.assignClient(userId, secretDto.getClientName());
    }

    /**
     * 暂不考虑分页
     */
    @GetMapping("")
    public List<OpenApiClientVo> listClients(@CurUserId long userId) throws ServiceException {

        final User user = userService.getUserById(userId);

        final OpenApiSearchFilterOption searchOption = OpenApiSearchFilterOption.builder().companyId(user.getCompanyId()).build();

        final List<OpenApiClient> openApiClients = openApiClientService.listClient(searchOption);
        return openApiClients.stream().map(OpenApiClientVo::fromClient).collect(Collectors.toList());
    }

    /**
     * user company 需要和 client company 相同
     * @param userId
     * @param clientId
     * @throws ServiceException
     */
    private void checkUserClient(long userId, String clientId) throws ServiceException {
        final User user = userService.getUserById(userId);
        final OpenApiClient client = openApiClientService.getByClientId(clientId);
        if (user == null || client == null){
            throw new ServiceException(MessageCode.PARAM_INVALID);
        }
        if(!Objects.equals(user.getCompanyId(), client.getCompanyId())){
            throw new ServiceException(MessageCode.PARAM_INVALID);
        }
    }
}
