package com.bees360.web.security.expression;

import jakarta.servlet.http.HttpServletRequest;

import org.springframework.security.core.Authentication;

/**
 * <AUTHOR>
 */
public interface ProjectSecurity {

    String REQUEST_ATTRI_ACCESSIBLE = "%s@IS_ACCESSIBLE".formatted(ProjectSecurity.class.toString());
    String REQUEST_ATTRI_MEMBER_IN = "%s@IS_MEMBER_IN".formatted(ProjectSecurity.class.toString());
    String REQUEST_ATTRI_MEMBER_WITH = "%s@IS_MEMBER_WITH".formatted(ProjectSecurity.class.toString());
    String REQUEST_ATTRI_MEMBER_CONTAINS = "%s@IS_MEMBER_CONTAINS".formatted(ProjectSecurity.class.toString());
    String REQUEST_ATTRI_MEMBER_EQUALS = "%s@IS_MEMBER_EQUALS".formatted(ProjectSecurity.class.toString());
    String REQUEST_ATTRI_MANAGED_BY = "%s@IS_MANAGED_BY ".formatted(ProjectSecurity.class.toString());

    /**
     * 当前用户可以访问这个项目
     */
    default boolean isProjectAccessible(Authentication authentication, long projectId) {
        return isProjectAccessible(null, authentication, projectId);
    }

    /**
     * 当前用户可以访问这个项目
     *
     * @param request 有助于缓存当前结果重复使用，可以为null
     */
    boolean isProjectAccessible(HttpServletRequest request, Authentication authentication, long projectId);

    /**
     * 当前用户是项目的成员
     */
    default boolean isMemberIn(Authentication authentication, long projectId) {
        return isMemberIn(null, authentication, projectId);
    }

    /**
     * 当前用户是项目的成员。
     *
     * @param request 有助于缓存当前结果重复使用，可以为null
     */
    boolean isMemberIn(HttpServletRequest request, Authentication authentication, long projectId);

    /**
     * judge whether the current user plays one of the specified roles in the specified project
     *
     * @param authentication
     *            the authentication created in {@link JwtDaoAuthenticationProvider#createSuccessAuthentication}, It
     *            represents the current user.
     * @param projectId
     *            the specified project
     * @param roles
     *            the specified roles
     * @return
     */
    default boolean isMemberWith(Authentication authentication, long projectId, String... roles) {
        return isMemberWith(null, authentication, projectId, roles);
    }

    /**
     * 当前用户是项目的成员，且“具有任意“给定角色
     *
     * @param request 有助于缓存当前结果重复使用，可以为null
     */
    boolean isMemberWith(HttpServletRequest request, Authentication authentication, long projectId, String... roles);

    /**
     * 当前用户是项目的成员，且“包含所有”给定角色
     *
     * @param request 有助于缓存当前结果重复使用，可以为null
     */
    boolean isMemberWithAll(HttpServletRequest request, Authentication authentication, long projectId, String... roles);

    /**
     * 当前用户是项目的成员，且该用户的角色“完全等于”给定的角色
     *
     * @param request 有助于缓存当前结果重复使用，可以为null
     */
    boolean isMemberEquals(HttpServletRequest request, Authentication authentication, long projectId, String... roles);

    /**
     * 该项目属于当前用户所在公司，且当前用户是公司的管理员
     */
    default boolean isManagedBy(Authentication authentication, long projectId) {
        return isManagedBy(null, authentication, projectId);
    }

    /**
     * 该项目属于当前用户所在公司，且当前用户是公司的管理员
     *
     * @param request 有助于缓存当前结果重复使用，可以为null
     */
    boolean isManagedBy(HttpServletRequest request, Authentication authentication, long projectId);

}
