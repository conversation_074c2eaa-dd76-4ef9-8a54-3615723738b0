package com.bees360.event;

import com.bees360.address.Address;
import com.bees360.address.AddressProvider;
import com.bees360.entity.Project;
import com.bees360.event.registry.AddressChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.ProjectService;
import com.bees360.web.event.project.ProjectAddressChangeEvent;
import lombok.extern.log4j.Log4j2;
import org.jooq.tools.StringUtils;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.bees360.util.Defaults.defaultIfNull;

/**
 * 监听地址变更事件，更新关联项目的地址信息并触发同步事件。
 */
@Log4j2
public class UpdateProjectAddressOnAddressChanged extends AbstractNamedEventListener<AddressChanged> {

    private final ApplicationEventPublisher publisher;

    private final ProjectService projectService;

    private final AddressProvider addressProvider;

    private final ProjectMapper projectMapper;

    public UpdateProjectAddressOnAddressChanged(
            ApplicationEventPublisher publisher,
            ProjectService projectService,
            AddressProvider addressProvider,
            ProjectMapper projectMapper) {
        this.publisher = publisher;
        this.projectService = projectService;
        this.addressProvider = addressProvider;
        this.projectMapper = projectMapper;
        log.info(
            "Created {}(publisher={},projectService={}.addressProvider={},projectMapper={})",
            this, publisher, projectService, projectMapper, projectMapper);
    }

    @Override
    public void handle(AddressChanged event) throws IOException {
        log.info("Received event: {}.", event);
        if (event.getOldAddress() == null || event.getNewAddress() == null) {
            return;
        }
        if (!isAddressComponentChanged(event.getOldAddress(), event.getNewAddress())) {
            return;
        }
        var address = addressProvider.findById(event.getNewAddress().getId());
        var projectIds = projectService.listProjectIdsByAddressId(address.getId());
        for (var projectId: projectIds) {
            var project = projectService.getById(projectId);
            updateProjectAddress(project, address);
        }
    }

    private boolean isAddressComponentChanged(Address oldAddress, Address newAddress) {
        return notEq(oldAddress.getAddress(), newAddress.getAddress())
            || notEq(oldAddress.getState(), newAddress.getState())
            || notEq(oldAddress.getCity(), newAddress.getCity())
            || notEq(oldAddress.getZip(), newAddress.getZip())
            || notEq(oldAddress.getLat(), newAddress.getLat())
            || notEq(oldAddress.getLng(), newAddress.getLng());
    }

    private void updateProjectAddress(Project project, Address address) {
        if (!isAddressChanged(project, address)) {
            return;
        }
        Map<String, Object> data = new HashMap<>();
        data.put("zipCode", address.getZip());
        data.put("city", address.getCity());
        data.put("address", address.getStreetAddress());
        data.put("state", address.getState());
        data.put("country", address.getCountry());
        data.put("addressId", address.getId());
        data.put("gpsLocationLatitude",  defaultIfNull(address.getLat(), 0.0d));
        data.put("gpsLocationLongitude", defaultIfNull(address.getLng(), 0.0d));
        data.put("gpsIsApproximate", defaultIfNull(address.isGpsApproximate(), true));
        log.info("Update address of project {} to {}.", project.getProjectId(), data);
        // 更新mysql
        projectMapper.updateByMap(project.getProjectId(), data);
        // 触发数据同步 -> 主要是firebase
        publisher.publishEvent(new ProjectAddressChangeEvent(this, projectService.getById(project.getProjectId())));
    }

    private boolean isAddressChanged(Project project, Address address) {
        // 创建项目的逻辑里面保留了street address，这里对street address做比较
        // 这里只关注特殊的几个字段，避免错误更新。
        return notEq(project.getCity(), address.getCity())
            || notEq(project.getState(), address.getState())
            || notEq(project.getZipCode(), address.getZip())
            || notEq(project.getLat(), address.getLat())
            || notEq(project.getLng(), address.getLng());
    }

    private boolean notEq(String left, String right) {
        return !StringUtils.equals(left, right);
    }

    private boolean notEq(Double left, Double right) {
        return !Objects.equals(left, right);
    }
}
