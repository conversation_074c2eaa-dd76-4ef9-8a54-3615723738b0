package com.bees360.config;

import com.bees360.service.util.ProjectSupplementalServiceTypeUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;

@Configuration
public class SupplementalServiceConfig {

    @Bean
    @ConfigurationProperties(prefix = "options.supplemental-service")
    public SupplementalServiceProperties supplementalServiceProperties() {
        return new SupplementalServiceProperties();
    }

    @Data
    public static class SupplementalServiceProperties {
        private List<String> typeList;
    }

    @Bean
    public ProjectSupplementalServiceTypeUtil projectSupplementalServiceTypeUtil(SupplementalServiceProperties supplementalServiceProperties) {
        var types = supplementalServiceProperties.getTypeList();
        return new ProjectSupplementalServiceTypeUtil(new HashSet<>(Optional.ofNullable(types).orElse(List.of())));
    }
}
