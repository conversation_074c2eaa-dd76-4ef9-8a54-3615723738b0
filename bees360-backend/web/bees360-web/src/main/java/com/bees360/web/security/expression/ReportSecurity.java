package com.bees360.web.security.expression;

import jakarta.servlet.http.HttpServletRequest;

import org.springframework.security.core.Authentication;

/**
 * <AUTHOR>
 */
public interface ReportSecurity {

    String REQUEST_ATTRI_IN_ACCESSIBLE_PROJECT = "%s@IN_ACCESSIBLE_PROJECT".formatted(ReportSecurity.class.getName());
    String REQUEST_ATTRI_PAID = "%s@PAID".formatted(ReportSecurity.class.getName());
    String REQUEST_ATTRI_READY = "%s@READY".formatted(ReportSecurity.class.getName());
    String REQUEST_ATTRI_LISTABLE = "%s@LISTABLE".formatted(ReportSecurity.class.getName());

    /**
     * 用户可以访问报告所在项目
     */
    boolean isReportInAccessibleProject(HttpServletRequest request, Authentication authentication, String reportId);

    /**
     * 报告已经被支付
     */
    boolean isReportPaid(HttpServletRequest request, Authentication authentication, String reportId);

    /**
     * 报告已经准备好了
     */
    boolean isReportReady(HttpServletRequest request, Authentication authentication, String reportId);

    /**
     * 报告基本信息可以被获取
     */
    boolean isReportListable(HttpServletRequest request, Authentication authentication, String reportId);
}
