package com.bees360.web;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 由于web项目的包级别太高了，如果直接从扫描com.bees360下的所有Spring Component，会导致一些问题，如会扫描到data模块的类。
 * 为了解决该问题，在该配置类中指定Spring扫描的位置。
 *
 * <AUTHOR>
 * @date 2019/12/12 14:49
 */
@Configuration
@ComponentScan(basePackageClasses = {
    // bees360-entity
    com.bees360.entity.PackageMarker.class,
    // bees360-service
    com.bees360.service.PackageMarker.class,
    // bees360-utils
    com.bees360.util.PackageMarker.class, com.bees360.base.PackageMarker.class,
    // bees360-web
    com.bees360.config.PackageMarker.class, com.bees360.controller.PackageMarker.class,
    com.bees360.scheduletask.PackageMarker.class,
    // bees360-web-grpc
    com.bees360.service.grpc.PackageMarker.class,
    // bees360-web-grpc-service
    com.bees360.web.grpc.service.PackageMarker.class
})
public class ComponentScanConfig {

}
