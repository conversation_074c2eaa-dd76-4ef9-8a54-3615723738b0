package com.bees360.web.hover;

import com.bees360.commons.hover.HoverClient;
import com.bees360.commons.hover.entity.HoverAuthorization;
import com.bees360.commons.hover.entity.JacksonObjectMapper;
import com.bees360.commons.hover.exception.HoverAuthenticationException;
import com.bees360.commons.hover.exception.HoverAuthorizationException;
import com.bees360.commons.hover.exception.HoverException;
import com.bees360.commons.hover.exception.HoverServerErrorException;
import com.bees360.service.MessageService;
import com.bees360.service.properties.Bees360WatcherProperties;
import com.bees360.util.RedisUtil;
import java.util.Collections;
import java.util.Set;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "hover.backend", name = "auto-refresh-token", havingValue = "true")
public class HoverAuthorizationBackend implements ApplicationListener<ContextRefreshedEvent> {

    private final DelayQueue<RefreshTask> refreshQueue;
    private final Thread refreshThread;

    public HoverAuthorizationBackend() {
        refreshThread = newThread();
        refreshQueue = new DelayQueue<>();
    }

    @Autowired
    private HoverClient hoverClient;
    @Autowired
    private MessageService messageService;

    @Autowired
    private Bees360WatcherProperties bees360WatcherProperties;

    @Value("${hover.auth.refresh-token-interval-second:1800}")
    private long tokenRefreshIntervalSecond;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        refreshThread.start();
        log.info("The thread `refreshThread` for refreshing hover token is created and started.");
    }

    public void retrieveToken(String code) {
        try {
            hoverClient.forAuthorization().retrieveToken(code);
        } catch (HoverException e) {
            Throwable cause = e.getCause();
            if (cause instanceof HoverAuthorizationException) {
                log.warn("Fail to Retrieve Token, code = {}", code, e);
                messageService
                    .sendToAdminForHoverError(Collections.singleton(bees360WatcherProperties.getAdminEmail()), null,
                        true, false);
                return;
            }
            log.error("Fail to Retrieve Token, code ={}", code, e);
        }
    }

    public void refreshToken() {
        doRefresh();
    }

    private void doRefresh() {
        try {
            log.info("Try to refresh hover token");
            hoverClient.forAuthorization().refreshToken();
            log.info("Finish to refresh hover token");
        } catch (HoverException e) {
            log.error("Fail to refresh hover Token", e);
            Throwable cause = e.getCause();
            if (cause instanceof HoverAuthenticationException) {
                messageService
                    .sendToAdminForHoverError(Collections.singleton(bees360WatcherProperties.getAdminEmail()), null,
                        true, false);
                return;
            }
        }
    }

    private Thread newThread() {
        Thread t = new Thread(() -> {
            // loop for refresh token
            for (; ; ) {
                if (refreshQueue.isEmpty()) {
                    doRefresh();
                    long currentSecond = TimeUnit.SECONDS.convert(System.currentTimeMillis(), TimeUnit.MILLISECONDS);
                    refreshQueue.offer(new RefreshTask(currentSecond + tokenRefreshIntervalSecond));
                }
                try {
                    RefreshTask delayed = refreshQueue.poll(tokenRefreshIntervalSecond / 2, TimeUnit.SECONDS);
                    if (delayed != null) {
                        doRefresh();
                        long currentSecond = TimeUnit.SECONDS
                            .convert(System.currentTimeMillis(), TimeUnit.MILLISECONDS);
                        refreshQueue.offer(new RefreshTask(currentSecond + tokenRefreshIntervalSecond));
                    }
                } catch (InterruptedException e) {
                    log.warn("Poll Delayed Refresh Task Interrupted", e);
                }
            }
        }, "refresh-hover-token-thread");
        t.setDaemon(true);

        return t;
    }

    static class RefreshTask implements Delayed {

        private final long nextTrigger;

        public RefreshTask(long nextTrigger) {
            this.nextTrigger = nextTrigger;
        }

        @Override
        public long getDelay(TimeUnit unit) {
            return unit.convert(nextTrigger, TimeUnit.SECONDS) - unit
                .convert(System.currentTimeMillis(), TimeUnit.MILLISECONDS);
        }

        @Override
        public int compareTo(Delayed o) {
            return getDelay(TimeUnit.NANOSECONDS) >= o.getDelay(TimeUnit.NANOSECONDS) ? 1 : -1;
        }
    }
}
