package com.bees360.config.filter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.servlet.Filter;

import org.springframework.util.ObjectUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * @see com.bees360.config.WebMvcConfig
 */
@Deprecated
public class CorsFilter implements Filter {

	private static List<String> corsWhitelist = new ArrayList<String>();

	private static String allowedMethods = null;
	private static String allowedHeaders = null;
	private static String exposedHeaders = null;
	private static String allowCredentials = null;
	private static String maxAge = null;

	private List<String> splitLine(String line) {
		if(ObjectUtils.isEmpty(line)) {
			return new ArrayList<>();
		}
        return Arrays.asList(line.split(","));
	}

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
        corsWhitelist = splitLine(filterConfig.getInitParameter("cors.allowed.origins"));
		allowedMethods = filterConfig.getInitParameter("cors.allowed.methods");
		allowedHeaders = filterConfig.getInitParameter("cors.allowed.headers");
		exposedHeaders = filterConfig.getInitParameter("cors.exposed.headers");
		allowCredentials = filterConfig.getInitParameter("cors.support.credentials");
		maxAge = filterConfig.getInitParameter("cors.preflight.maxage");
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
			throws IOException, ServletException {
		HttpServletRequest servletRequest =  (HttpServletRequest)request;
		HttpServletResponse  servletResponse = (HttpServletResponse)response;

		String origin = servletRequest.getHeader("Origin");

		if(origin != null) {
			//if origin is not match whitelist, set Access-Control-Allow-Origin default null
			for(String corsWhite : corsWhitelist) {
				if(corsWhite.equals(origin)) {
					servletResponse.setHeader("Access-Control-Allow-Origin", origin);
					break;
				}else if(corsWhite.indexOf("*") > 0){
					if(match(corsWhite, origin)) {
						servletResponse.setHeader("Access-Control-Allow-Origin", origin);
					}
				}
			}

			servletResponse.setHeader("Access-Control-Allow-Methods", allowedMethods);
			servletResponse.setHeader("Access-Control-Allow-Headers", allowedHeaders);
			servletResponse.addHeader("Access-Control-Expose-Headers", exposedHeaders);
			servletResponse.addHeader("Access-Control-Allow-Credentials", allowCredentials);
			servletResponse.setHeader("Access-Control-Max-Age", maxAge);
		}
		//TODO it comes from mobile app or postman(why there is no cors issue for them, if exists how to deal with this problem)
		// https://stackoverflow.com/questions/42094515/cors-security-with-mobile-apps
		else {
			servletResponse.setHeader("Access-Control-Allow-Origin", null);
		}

		if ("OPTIONS".equals(servletRequest.getMethod())) {
			servletResponse.setStatus(HttpServletResponse.SC_OK);
		} else {
			filterChain.doFilter(servletRequest, servletResponse);
		}
	}


	@Override
	public void destroy() {

	}


	private boolean match(String pattern , String origin) {
		Pattern p = Pattern.compile(pattern);
		Matcher matcher = p.matcher(origin);
		return matcher.matches();
	}
}
