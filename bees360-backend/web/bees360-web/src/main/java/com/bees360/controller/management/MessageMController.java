package com.bees360.controller.management;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.vo.ReportTypeVo;
import com.bees360.service.ProjectReportFileService;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@RequestMapping("/management/project/{projectId:\\d+}/message")
public class MessageMController {

    @Inject
    private ProjectReportFileService projectReportFileService;

    @PostMapping("/project-email")
    public void infoReportApprove(@PathVariable long projectId, @Valid @RequestBody ReportTypeVo reportTypeVo)
        throws ServiceException {
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportTypeVo.getReportType());
        projectReportFileService.infoMemberReportApprove(projectId, reportType, true);
    }

    @PostMapping("/user-email")
    public void infoSingleEmail(@PathVariable long projectId, @Valid @RequestBody ReportTypeVo reportTypeVo)
        throws ServiceException {
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportTypeVo.getReportType());
        projectReportFileService.infoUserReportApprove(projectId, reportTypeVo.getUserId(), reportType, true);
    }

}
