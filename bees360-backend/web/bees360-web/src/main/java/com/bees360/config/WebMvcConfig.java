package com.bees360.config;

import java.util.List;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.bees360.config.support.AntPathMatcherCorsConfiguration;
import com.bees360.config.support.CurUserIdMethodArgumentResolver;
import com.bees360.web.core.properties.bean.CorsProperties;

/**
 * <AUTHOR> Guanrong
 * @date 2019/12/05 22:30
 */
@Slf4j
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 请不要修改该名称，SpringSecurity需要通过该名称来设置Cors
     */
    private static final String CORS_FILTER_BEAN_NAME = "corsFilter";

    @Autowired
    private CurUserIdMethodArgumentResolver curUserIdMethodArgumentResolver;

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        // 解析@CurUserId注解的参数
        argumentResolvers.add(curUserIdMethodArgumentResolver);
    }

    @Bean("innerCorsProperties")
    @ConfigurationProperties(prefix = "cors.inner")
    public CorsProperties innerCorsProperties() {
        return new CorsProperties();
    }

    @Bean("openCorsProperties")
    @ConfigurationProperties(prefix = "cors.open")
    public CorsProperties openCorsProperties() {
        return new CorsProperties();
    }

    /**
     * 配置CorsFilter
     *
     * @param innerCorsProperties
     * @param openCorsProperties
     * @return
     */
    @Bean(CORS_FILTER_BEAN_NAME)
    public CorsFilter corsFilter(
        // public CorsConfigurationSource corsConfigurationSource (
        @Qualifier("innerCorsProperties") CorsProperties innerCorsProperties,
        @Qualifier("openCorsProperties") CorsProperties openCorsProperties) {

        // 路径也是 AntPathMarcher
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        // 暂时不设置对外开放的 CORS 配置
        // source.registerCorsConfiguration("/v1/**", createCorsConfig(openCorsProperties));
        source.registerCorsConfiguration("/**", createCorsConfig(innerCorsProperties));

        return new CorsFilter(source);
        // return source;
    }

    public CorsConfiguration createCorsConfig(CorsProperties corsProperties) {
        CorsConfiguration config = new AntPathMatcherCorsConfiguration();

        config.setAllowedOrigins(corsProperties.getAllowed().getOrigins());
        config.setAllowedHeaders(corsProperties.getAllowed().getHeaders());
        config.setAllowedMethods(corsProperties.getAllowed().getMethods());
        config.setAllowCredentials(corsProperties.getAllowed().isCredentials());

        config.setExposedHeaders(corsProperties.getExposed().getHeaders());

        return config;
    }
}
