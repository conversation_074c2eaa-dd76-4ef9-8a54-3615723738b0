package com.bees360.config;

import com.bees360.user.Group;
import com.bees360.user.GroupProvider;
import com.bees360.user.GrpcGroupManager;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.user.config.GrpcGroupManagerConfig;
import com.bees360.util.user.Bees360UserUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;

import java.util.Collections;

@Import(GrpcGroupManagerConfig.class)
@Configuration
public class GroupProviderConfig {

    @Primary
    @Bean
    public GroupProvider groupProvider(GrpcGroupManager grpcGroupManager, UserProvider userProvider) {
        return new ForwardGroupProvider(grpcGroupManager, userProvider);
    }

    static class ForwardGroupProvider implements GroupProvider {

        private final GroupProvider groupProvider;

        private final UserProvider userProvider;

        ForwardGroupProvider(GroupProvider groupProvider, UserProvider userProvider) {
            this.groupProvider = groupProvider;
            this.userProvider = userProvider;
        }

        @Override
        public Group findGroupById(String id) {
            return groupProvider.findGroupById(id);
        }

        @Override
        public Iterable<? extends Group> findGroupsByIds(Iterable<String> ids) {
            return groupProvider.findGroupsByIds(ids);
        }

        @Override
        public Iterable<? extends Group> findGroupByUserId(String userId) {
            final User userRetrieved = userProvider.findUserById(userId);
            if (userRetrieved == null) {
                return Collections.emptyList();
            }
            final String userRetrievedId = userRetrieved.getId();
            return groupProvider.findGroupByUserId(Bees360UserUtils.removePrefix(userRetrievedId));
        }

        @Override
        public Iterable<? extends Group> findGroupByName(String name) {
            return groupProvider.findGroupByName(name);
        }

        @Override
        public Iterable<? extends Group> findGroupByAuthority(String auth) {

            return groupProvider.findGroupByAuthority(auth);
        }

        @Override
        public Iterable<? extends Group> listGroups(String scope) {
            return groupProvider.listGroups(scope);
        }
    }
}
