package com.bees360.openapi;

import com.bees360.api.NotFoundException;
import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.UncheckedServiceException;
import com.bees360.entity.openapi.OpenReportSummaryVo;
import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.bees360.service.openapi.OpenReportService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Struct;
import com.google.protobuf.util.JsonFormat;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Nullable;
import java.util.NoSuchElementException;
import java.util.function.BinaryOperator;

@Log4j2
public class DefaultOpenApiReportProvider implements OpenApiReportProvider  {

    private final OpenReportService openReportService;

    private final BinaryOperator<String> lambdaReportSummaryConverter;

    private final ObjectMapper objectMapper;

    public DefaultOpenApiReportProvider(
        @NonNull OpenReportService openReportService,
        @NonNull BinaryOperator<String> lambdaReportSummaryConverter,
        @NonNull ObjectMapper objectMapper) {
        this.openReportService = openReportService;
        this.lambdaReportSummaryConverter = lambdaReportSummaryConverter;
        this.objectMapper = objectMapper;
        log.info("Created {}(openReportService={},lambdaReportSummaryConverter={},objectMapper={})",
            this,
            openReportService,
            lambdaReportSummaryConverter,
            objectMapper);
    }

    @Override
    public Message.ReportMessage getSummaryByReportId(String reportId) {
        return getSummaryByReportId(reportId, false);
    }

    public Message.ReportMessage getSummaryByReportId(String reportId, boolean withAdditionalProperties) {
        OpenReportSummaryVo reportSummaryVo = null;
        try {
            reportSummaryVo = openReportService.getReportSummary(reportId);
        } catch (ResourceNotFoundException | NoSuchElementException | NotFoundException e) {
            throw new NotFoundException("Report '%s' not found.".formatted(reportId));
        } catch (ServiceException e) {
           throw new UncheckedServiceException(e);
        }
        var summaryVo = reportSummaryVo.getSummary();
        reportSummaryVo.setSummary(null);
        var reportBuilder = convertReport(reportSummaryVo);
        if (summaryVo != null) {
            var summary = convertSummary(reportSummaryVo.getProject().getId(), reportId, summaryVo, withAdditionalProperties);
            reportBuilder.setSummary(summary);
        }
        return reportBuilder.build();
    }

    @Override
    public SummaryReport getReportSummaryById(String reportId) {
        var message = getSummaryByReportId(reportId, true);
        return SummaryReport.of(
            reportId,
            message.getType(),
            ProtoSummaryProject.from(message.getProject()),
            ProtoSummary.from(message.getSummary()));
    }

    private Message.ReportMessage.Builder convertReport(OpenReportSummaryVo reportSummaryVo) {
        var reportBuilder = Message.ReportMessage.newBuilder();
        try {
            var json = objectMapper.writeValueAsString(reportSummaryVo);
            JsonFormat.parser().ignoringUnknownFields().merge(json, reportBuilder);
            return reportBuilder;
        } catch (InvalidProtocolBufferException | JsonProcessingException e) {
            throw new IllegalStateException("Fail to parse json for report %s.".formatted(reportSummaryVo.getId()));
        }
    }

    @Nullable
    private Message.ReportMessage.Summary convertSummary(long projectId, String reportId, ReportSummaryVo summaryVo, boolean withAdditionalProperties) {
        if (summaryVo == null) {
            return null;
        }
        // 定制化请求调用lambda对summary进行处理后,替换原本的summary后返回
        try {
            var transformedReportSummary =
                lambdaReportSummaryConverter.apply(
                    objectMapper.writeValueAsString(summaryVo),
                    String.valueOf(projectId));
            if (StringUtils.isEmpty(transformedReportSummary)) {
                return null;
            }
            var summaryBuilder = Message.ReportMessage.Summary.newBuilder();
            JsonFormat.parser().ignoringUnknownFields().merge(transformedReportSummary, summaryBuilder);
            if (withAdditionalProperties) {
                var additionalPropertiesBuilder = Struct.newBuilder();
                JsonFormat.parser().ignoringUnknownFields().merge(transformedReportSummary, additionalPropertiesBuilder);
                summaryBuilder.setAdditionalProperties(additionalPropertiesBuilder.build());
            }
            return summaryBuilder.build();
        } catch (InvalidProtocolBufferException | JsonProcessingException e) {
            throw new IllegalStateException("Fail to parse json for report %s.".formatted(reportId), e);
        }
    }
}
