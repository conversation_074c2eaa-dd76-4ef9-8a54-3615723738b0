package com.bees360.scheduletask.sandbox.sample;

import com.bees360.entity.enums.ReportTypeEnum;
import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.springframework.lang.NonNull;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;

/**
 * <AUTHOR>
 */
public class SandboxSampleResource {

    public static String PROJECT_PACKAGE_PREFIX = "sample/";
    public static String COMMON_SUB_PREFIX = "common/";
    public static String SWYFFT_SUB_PREFIX = "swyfft_only/";
    public static String FILE_PREFIX = "sandbox_sample_";

    public static String SWYFFT_JSON_IMAGE_FILE_PATH = "sandbox/sample_swyfft_only_images.json";
    public static String COMMON_JSON_IMAGE_FILE_PATH = "sandbox/common_images.json";

    public static String SUMMARY_JSON_PATH = "sandbox/summary.json";

    public static String reportSummaryCached;

    @NoArgsConstructor
    public static class FileResource {
        @Getter
        private String key;
        @Getter
        private int size;

        public FileResource(String key, int size) {
            this.key = key;
            this.size = size;
        }
    }

    @NoArgsConstructor
    public static class ReportResource {
        @Getter
        private String report;
        @Getter
        private int size;
        @Getter
        private String reportCompressed;
        @Getter
        private int sizeCompressed;

        public ReportResource(String report, int size, String reportCompressed, int sizeCompressed) {
            this.report = report;
            this.size = size;
            this.reportCompressed = reportCompressed;
            this.sizeCompressed = sizeCompressed;
        }
    }

    /**
     * 仅仅提供给Swyfft公司样例文件
     */
    public static interface SwyfftResources {
        String SWYFFT_RESOURCE_PREFIX = PROJECT_PACKAGE_PREFIX + SWYFFT_SUB_PREFIX + FILE_PREFIX;
        /**
         * 报告文件
         */
        ReportResource REPORT = new ReportResource( SWYFFT_RESOURCE_PREFIX + "swyfft_only_report.pdf", 49495957,
            SWYFFT_RESOURCE_PREFIX + "swyfft_only_report_compressed.pdf", 4044793);
        /**
         * 图片压缩包
         */
        FileResource IMAGE_ARCHIVE = resource(SWYFFT_RESOURCE_PREFIX + "swyfft_only_image_archive.zip", 62558892);
    }

    /**
     * 公共可使用的资源
     */
    public static interface CommonResources {
        String COMMON_RESOURCE_PREFIX = PROJECT_PACKAGE_PREFIX + COMMON_SUB_PREFIX + FILE_PREFIX;
        /**
         * 报告文件
         *
         * @see ReportTypeEnum#FULL_SCOPE_UNDERWRITING_REPORT
         */
        ReportResource REPORT_FULL_SCOPE_UNDERWRITING = new ReportResource(
            COMMON_RESOURCE_PREFIX + "common_report_full_scope_underwriting.pdf", 12128193,
            COMMON_RESOURCE_PREFIX + "common_report_full_scope_underwriting_compressed.pdf", 5307074);
        /**
         * 报告文件
         *
         * @see ReportTypeEnum#PREMIUM_DAMAGE_ASSESSMENT_REPORT
         */
        ReportResource REPORT_PREMIUM_DAMAGE_ASSESSMENT = new ReportResource(
            COMMON_RESOURCE_PREFIX + "common_report_premium_damage_assessment.pdf", 2888951,
            COMMON_RESOURCE_PREFIX + "common_report_premium_damage_assessment_compressed.pdf", 2888951);
        /**
         * 报告文件
         *
         * @see ReportTypeEnum#PREMIUM_ROOF_MEASUREMENT_REPORT
         */
        ReportResource REPORT_PREMIUM_ROOF_MEASUREMENT = new ReportResource(
            COMMON_RESOURCE_PREFIX + "common_report_premium_roof_measurement.pdf", 932964,
            COMMON_RESOURCE_PREFIX + "common_report_premium_roof_measurement_compressed.pdf", 932964);
        /**
         * 报告文件
         *
         * @see ReportTypeEnum#ROOF_ONLY_UNDERWRITING_REPORT
         */
        ReportResource REPORT_ROOF_ONLY_UNDERWRITING = new ReportResource(
            COMMON_RESOURCE_PREFIX + "common_report_roof_only_underwriting.pdf", 8165504,
            COMMON_RESOURCE_PREFIX + "common_report_roof_only_underwriting_compressed.pdf", 8165504);

        FileResource IMAGE_ARCHIVE = resource(
            COMMON_RESOURCE_PREFIX + "common_image_archive.zip", 62558892);
    }

    private static FileResource resource(String key, int size) {
        return new FileResource(key, size);
    }

    /**
     * 从 {@code resource} 中获取文件名。{@code resource} 不可以为空。
     */
    public static String getResourceFileName(@NonNull String resource) {
        Preconditions.checkNotNull(resource, "param `resource` may not be null.");
        int lastDot = resource.lastIndexOf('/');
        if(lastDot < 0) {
            return resource;
        }
        return resource.substring(lastDot + 1);
    }

    public static String getReportSummaryJson() {
        return createReportSummaryString();
    }

    private synchronized static String createReportSummaryString() {
        if (reportSummaryCached != null) {
            return reportSummaryCached;
        }
        String sampleSummary = "";
        try (InputStream input = SandboxSampleResource.class.getClassLoader().getResourceAsStream(
            SandboxSampleResource.SUMMARY_JSON_PATH)) {
            sampleSummary = IOUtils.toString(input, Charset.forName("utf-8"));
        } catch (IOException e) {
            throw new IllegalStateException("Fail to get ReportSummary", e);
        }
        reportSummaryCached = sampleSummary;
        return reportSummaryCached;
    }

}
