package com.bees360.config;

import static com.bees360.entity.dto.CompanySearchOption.DEFAULT_PAGE_INDEX;
import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.address.Address;
import com.bees360.base.exception.ServiceException;
import com.bees360.building.config.GrpcBuildingManagerConfig;
import com.bees360.common.Message.SetDateRequest;
import com.bees360.common.Message.SetIntValueRequest;
import com.bees360.common.Message.SetStringValueRequest;
import com.bees360.contract.config.GrpcContractManagerConfig;
import com.bees360.customer.config.GrpcCustomerClientConfig;
import com.bees360.customer.config.GrpcDivisionClientConfig;
import com.bees360.entity.dto.ProjectSearchOption;
import com.bees360.entity.enums.AmericaStateEnums;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.ProjectTinyVo;
import com.bees360.entity.vo.request.ProjectServiceTypeParam;
import com.bees360.mapper.MemberMapper;
import com.bees360.om.config.GrpcOperationsManagerClientConfig;
import com.bees360.policy.config.GrpcPolicyMangerConfig;
import com.bees360.project.ContactManager;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.FullProjectProvider;
import com.bees360.project.GenericProjectCreator;
import com.bees360.project.GrpcGenericProjectCreator;
import com.bees360.project.GrpcProjectService;
import com.bees360.project.Message;
import com.bees360.project.Project;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.ProjectEndpoint;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.ProjectManager;
import com.bees360.project.ProjectOperationTagProvider;
import com.bees360.project.ProjectProvider;
import com.bees360.project.ProjectQuery;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.config.GrpcExternalIntegrationManagerConfig;
import com.bees360.project.config.GrpcGenericProjectCreatorConfig;
import com.bees360.project.config.GrpcProjectCatastropheClientConfig;
import com.bees360.project.config.GrpcProjectContactClientConfig;
import com.bees360.project.config.GrpcProjectDaysOldProviderConfig;
import com.bees360.project.config.GrpcProjectIIMangerConfig;
import com.bees360.project.config.GrpcProjectMemberClientConfig;
import com.bees360.project.config.GrpcProjectOperationTagManagerConfig;
import com.bees360.project.config.GrpcProjectPaymentManagerConfig;
import com.bees360.project.config.GrpcProjectRequestCancellationManagerConfig;
import com.bees360.project.config.GrpcProjectStateChangeReasonManagerConfig;
import com.bees360.project.config.GrpcProjectStateManagerConfig;
import com.bees360.project.config.GrpcProjectStatusManagerConfig;
import com.bees360.project.config.GrpcProjectTagManagerConfig;
import com.bees360.project.config.GrpcStateChangeReasonGroupManagerConfig;
import com.bees360.project.state.ChangeReasonFromCategoryProvider;
import com.bees360.project.state.ProjectStateChangeReasonManager;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.UserService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.user.UserProvider;
import com.bees360.util.DateTimes;
import com.bees360.util.Functions;
import com.bees360.web.project.MysqlProjectOperationTagProvider;
import com.bees360.web.project.MysqlProjectStateManager;
import com.bees360.web.project.SiGenericProjectCreator;
import com.bees360.web.project.SiProjectContactManager;
import com.bees360.web.project.SiProjectExternalIntegrationManager;
import com.bees360.web.project.SiProjectManager;
import com.google.protobuf.BoolValue;
import io.grpc.stub.StreamObserver;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import net.devh.boot.grpc.server.service.GrpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.util.CollectionUtils;

/** <AUTHOR> */
@Import({
    ProjectEndpoint.class,
    ProjectManagerConfig.GrpcProjectServiceNested.class,
    SiProjectManager.class,
    GrpcProjectIIMangerConfig.class,
    GrpcPolicyMangerConfig.class,
    GrpcBuildingManagerConfig.class,
    GrpcContractManagerConfig.class,
    GrpcProjectContactClientConfig.class,
    GrpcProjectCatastropheClientConfig.class,
    GrpcCustomerClientConfig.class,
    GrpcProjectStatusManagerConfig.class,
    GrpcProjectRequestCancellationManagerConfig.class,
    GrpcProjectMemberClientConfig.class,
    GrpcOperationsManagerClientConfig.class,
    GrpcProjectOperationTagManagerConfig.class,
    GrpcProjectStateManagerConfig.class,
    GrpcProjectStateChangeReasonManagerConfig.class,
    GrpcProjectDaysOldProviderConfig.class,
    GrpcProjectStateManagerConfig.class,
    GrpcProjectPaymentManagerConfig.class,
    GrpcExternalIntegrationManagerConfig.class,
    GrpcDivisionClientConfig.class,
    GrpcGenericProjectCreatorConfig.class,
    GrpcStateChangeReasonGroupManagerConfig.class,
    ChangeReasonFromCategoryProvider.class,
})
@Configuration
public class ProjectManagerConfig {

    @Autowired private UserProvider userProvider;
    @Autowired private ProjectService projectService;
    @Autowired private MemberMapper memberMapper;
    @Autowired private ContactManager contactManager;
    @Value("${bees360.ignore-project-id-end:}")
    private Long ignoreProjectIdEnd;

    @Configuration
    @Data
    @ConfigurationProperties(prefix = "bees360.rework-search-filter")
    @EnableConfigurationProperties
    static class ReworkSearchFilterProperties {
        List<Long> companyList;
    }

    @Bean
    @ConfigurationProperties(prefix = "project.state.change-reason-key-to-label")
    Map<String, ProjectLabelEnum> changeReasonKeyToLabel() {
        return new HashMap<>();
    }

    @Bean
    public Function<String, ProjectLabelEnum> changeReasonKeyToLabelEnumConverter(Map<String, ProjectLabelEnum> changeReasonKeyToLabel) {
        return changeReasonKey -> changeReasonKeyToLabel.getOrDefault(changeReasonKey, null);
    }

    @Bean
    public ProjectOperationTagProvider projectOperationTagProvider(ProjectLabelService projectLabelService) {
        return new MysqlProjectOperationTagProvider(projectLabelService);
    }

    @Bean(name = {"endpointProjectProvider", "grpcProjectServiceProvider"})
    public ProjectProvider projectProvider(ProjectManager projectManager,
                                           ReworkSearchFilterProperties searchProperties,
                                           ProjectOperationTagProvider projectOperationTagProvider,
                                           ExternalIntegrationManager siExternalIntegrationManager) {
        return new CompleteProjectProvider(
            projectManager,
            userProvider,
            projectService,
            ignoreProjectIdEnd,
            searchProperties,
            projectOperationTagProvider,
            siExternalIntegrationManager);
    }

    @Bean(name = "externalIntegrationManager")
    public ExternalIntegrationManager externalIntegrationManager(ExternalIntegrationManager grpcExternalIntegrationManager) {
        return grpcExternalIntegrationManager;
    }

    @Bean(name = "siExternalIntegrationManager")
    public ExternalIntegrationManager siExternalIntegrationManager(ProjectService projectService) {
        return new SiProjectExternalIntegrationManager(projectService);
    }

    @Primary
    @Bean
    public ContactManager siProjectContactManager() {
        return new SiProjectContactManager(projectService, memberMapper, userProvider, contactManager);
    }

    @Bean("webGenericProjectCreator")
    public SiGenericProjectCreator webGenericProjectCreator(ProjectService projectService, UserService userService) {
        return new SiGenericProjectCreator(projectService, userService);
    }

    @Bean("genericProjectCreator")
    public GenericProjectCreator genericProjectCreator(GrpcGenericProjectCreator grpcGenericProjectCreator) {
        return grpcGenericProjectCreator;
    }

    @Configuration
    @Import(GrpcProjectTagManagerConfig.class)
    static class ProjectTagManagerConfig {}

    @Bean
    public Supplier<ProjectStateManager> projectStateManagerSupplier(
        ProjectStateManager grpcProjectStateManager,
        UserService userService,
        ProjectStatusService projectStatusService,
        ProjectLabelService projectLabelService,
        ProjectStateChangeReasonManager projectStateChangeReasonManager,
        ProjectService projectService,
        ProjectIIRepository projectIIRepository,
        @Qualifier("closeReasonToOperationTagIdConverter")
                Optional<Function<String, Long>> closeReasonToOperationTagIdConverter,
        @Qualifier("changeReasonKeyToLabelEnumConverter")
                Function<String, ProjectLabelEnum> changeReasonKeyToLabelEnumConverter,
        Bees360FeatureSwitch featureSwitch) {

        ProjectStateManager projectStateManager =
                new MysqlProjectStateManager(
                        grpcProjectStateManager,
                        projectStatusService,
                        userService,
                        projectService,
                        projectIIRepository,
                        projectStateChangeReasonManager,
                        projectLabelService,
                        closeReasonToOperationTagIdConverter,
                        changeReasonKeyToLabelEnumConverter,
                        featureSwitch);

        return () -> projectStateManager;
    }

    @GrpcService
    @Log4j2
    static class GrpcProjectServiceNested extends GrpcProjectService {
        private final ProjectStateManager projectStateManager;
        private final GenericProjectCreator webGenericProjectCreator;
        private final ProjectService projectService;

        public GrpcProjectServiceNested(
            ProjectManager grpcProjectServiceManager,
            ProjectProvider grpcProjectServiceProvider,
            Supplier<ProjectStateManager> projectStateManagerSupplier,
            GenericProjectCreator webGenericProjectCreator,
            ProjectService projectService) {
            super(grpcProjectServiceManager, grpcProjectServiceProvider);
            this.projectStateManager = projectStateManagerSupplier.get();
            this.webGenericProjectCreator = webGenericProjectCreator;
            this.projectService = projectService;
            log.info(
                "Created '{}(grpcProjectServiceManager={},grpcProjectServiceProvider={},projectStateManager={}," +
                    "genericProjectCreator={}, projectService={}).'",
                this,
                grpcProjectServiceManager,
                grpcProjectServiceProvider,
                this.projectStateManager,
                this.webGenericProjectCreator,
                this.projectService);
        }

        @Override
        public void setMember(
                Message.MemberRequest request, StreamObserver<BoolValue> responseObserver) {
            log.debug("web setMember start, request:{}", request);
            try {
                projectService.changeMembersToMultiProject(
                        Long.parseLong(request.getOpUserId()),
                        Long.parseLong(request.getUserId()),
                    RoleEnum.valueOf(request.getRole()).getCode(),
                        List.of(Long.parseLong(request.getProjectId())));
            } catch (ServiceException e) {
                throw new IllegalArgumentException(e);
            }
            responseObserver.onNext(BoolValue.of(true));
            responseObserver.onCompleted();        }

        @Override
        public void changeProjectState(Message.ProjectStateChangeRequest request, StreamObserver<Message.ProjectOperationFeedback> responseObserver) {
            var projectState = request.getProjectState();
            var changeReason = projectState.getStateChangeReason().getId();
            var version =
                Optional.ofNullable(DateTimes.toInstant(projectState.getUpdatedAt()))
                    .map(Instant::toEpochMilli)
                    .orElse(null);

            var projectOperationFeedback = projectStateManager.changeProjectState(
                request.getProjectId(),
                projectState.getState(),
                changeReason,
                projectState.getUpdatedBy(),
                projectState.getComment(),
                version,
                request.getChangeForce());
            responseObserver.onNext(projectOperationFeedback.toMessage());
            responseObserver.onCompleted();
        }

        @Override
        public void createProject(Message.CreateProjectRequest request, StreamObserver<Message.ProjectMessage> responseObserver) {
            var result = webGenericProjectCreator.create(
                ProjectCreationRequest.from(request.getProject()),
                request.getAllowDuplication(),
                request.getCreationChannel()
            );
            responseObserver.onNext(result.toMessage());
            responseObserver.onCompleted();
        }

        @Override
        public void updatePolicyNumber(
            SetStringValueRequest request, StreamObserver<BoolValue> responseObserver) {
            var projectId = Long.parseLong(request.getId());
            var policyNumber = request.getValue();
            var requestBy = Long.parseLong(request.getRequestBy().getValue());

            try {
                var inspectionInfo = projectService.getInspectionInfo(projectId);
                inspectionInfo.setPolicyNumber(policyNumber);
                projectService.updateInspectionInfo(projectId, requestBy, inspectionInfo, false);
            } catch (ServiceException e) {
                throw new IllegalArgumentException(e);
            }
            responseObserver.onNext(BoolValue.of(true));
            responseObserver.onCompleted();
        }

        @Override
        public void updatePolicyEffectiveDate(
            SetDateRequest request, StreamObserver<BoolValue> responseObserver) {
            var projectId = Long.parseLong(request.getId());
            var effectiveDate = DateTimes.toLocalDate(request.getValue());
            var requestBy = Long.parseLong(request.getRequestBy().getValue());

            try {
                var inspectionInfo = projectService.getInspectionInfo(projectId);
                inspectionInfo.setPolicyEffectiveDate(effectiveDate);
                projectService.updateInspectionInfo(projectId, requestBy, inspectionInfo, false);
            } catch (ServiceException e) {
                throw new IllegalArgumentException(e);
            }
            responseObserver.onNext(BoolValue.of(true));
            responseObserver.onCompleted();
        }

        @Override
        public void updateAddress(
            Message.UpdateAddressRequest request, StreamObserver<BoolValue> responseObserver) {
            var projectId = Long.parseLong(request.getProjectId());
            var address = Address.from(request.getAddress());
            var requestBy = Long.parseLong(request.getRequestBy().getValue());

            try {
                var insuredInfo = projectService.getInsuredInfo(projectId);
                Functions.acceptIfNotNull(insuredInfo::setAddress, address.getStreetAddress());
                Functions.acceptIfNotNull(insuredInfo::setCity, address.getCity());
                Functions.acceptIfNotNull(insuredInfo::setState, address.getState());
                Functions.acceptIfNotNull(insuredInfo::setCountry, address.getCountry());
                Functions.acceptIfNotNull(insuredInfo::setZipCode, address.getZip());
                Functions.acceptIfNotNull(insuredInfo::setLat, address.getLat());
                Functions.acceptIfNotNull(insuredInfo::setLng, address.getLng());
                Functions.acceptIfNotNull(insuredInfo::setAddressId, address.getId());
                Functions.acceptIfNotNull(insuredInfo::setTimeZone, address.getTimeZone(), TimeZone::getID);
                projectService.updateInsuredInfo(projectId, requestBy, insuredInfo, false);
            } catch (ServiceException e) {
                throw new IllegalArgumentException(e);
            }
            responseObserver.onNext(BoolValue.of(true));
            responseObserver.onCompleted();
        }

        @Override
        public void updateYearBuilt(
            SetIntValueRequest request, StreamObserver<BoolValue> responseObserver) {
            var projectId = Long.parseLong(request.getId());
            var yearBuilt = request.getValue();
            var requestBy = Long.parseLong(request.getRequestBy().getValue());
            try {
                var inspectionInfo = projectService.getInspectionInfo(projectId);
                inspectionInfo.setYearBuilt(String.valueOf(yearBuilt));
                projectService.updateInspectionInfo(projectId, requestBy, inspectionInfo, false);
            } catch (ServiceException e) {
                throw new IllegalArgumentException(e);
            }
            responseObserver.onNext(BoolValue.of(true));
            responseObserver.onCompleted();
        }

        @Override
        public void updateServiceTypeById(
            Message.UpdateServiceTypeRequest request, StreamObserver<BoolValue> responseObserver) {
            var projectId = Long.parseLong(request.getProjectId());
            var serviceType = new ProjectServiceTypeParam();
            serviceType.setServiceType(request.getServiceType().getNumber());
            var updatedBy = Long.parseLong(request.getRequestBy().getValue());
            try {
                projectService.updateProjectServiceType(updatedBy, projectId, serviceType, false);
            } catch (ServiceException e) {
                throw new IllegalArgumentException(e);
            }
            responseObserver.onNext(BoolValue.of(true));
            responseObserver.onCompleted();
        }

        @Override
        public void updateInspectionNumber(
            SetStringValueRequest request, StreamObserver<BoolValue> responseObserver) {
            var projectId = Long.parseLong(request.getId());
            var inspectionNumber = request.getValue();
            var requestBy = Long.parseLong(request.getRequestBy().getValue());
            try {
                var inspectionInfo = projectService.getInspectionInfo(projectId);
                inspectionInfo.setInspectionNumber(inspectionNumber);
                projectService.updateInspectionInfo(projectId, requestBy, inspectionInfo, false);
            } catch (ServiceException e) {
                throw new IllegalArgumentException(e);
            }
            responseObserver.onNext(BoolValue.of(true));
            responseObserver.onCompleted();
        }

        @Override
        public void updatePolicyAndPropertyType(
            Message.UpdatePolicyTypeAndPropertyTypeRequest request,
            StreamObserver<BoolValue> responseObserver) {
            var projectId = Long.parseLong(request.getProjectId());
            var policyType = request.getPolicyType();
            var propertyType = request.getPropertyType().getValue();
            try {
                projectService.updatePolicyTypeAndPropertyType(projectId, policyType, propertyType, false);
            } catch (ServiceException e) {
                throw new IllegalArgumentException(e);
            }
            responseObserver.onNext(BoolValue.of(true));
            responseObserver.onCompleted();
        }
    }

    @Log4j2
    static class CompleteProjectProvider extends FullProjectProvider {
        private final UserProvider userProvider;
        private final ProjectService projectService;
        private final Long ignoreProjectIdEnd;
        private final ReworkSearchFilterProperties searchProperties;

        public CompleteProjectProvider(
            ProjectManager projectManager,
            UserProvider userProvider,
            ProjectService projectService, Long ignoreProjectIdEnd,
            ReworkSearchFilterProperties searchProperties,
            ProjectOperationTagProvider projectOperationTagProvider,
            ExternalIntegrationManager externalIntegrationManager) {
            super(projectManager, projectOperationTagProvider, externalIntegrationManager);
            this.userProvider = userProvider;
            this.projectService = projectService;
            this.ignoreProjectIdEnd = ignoreProjectIdEnd;
            this.searchProperties = searchProperties;
        }

        private static final int MAX_PAGE_SIZE = 5000;
        private static final int RUSH_FOLDER_DAYS_OLD_THRESHOLD = 5;

        /**
         * 数据量太大会导致请求超时, 谨慎使用该接口
         * 目前只返回projectId, address
         */
        @Override
        public Iterable<Project> getProject(ProjectQuery query) {
            ProjectSearchOption option = new ProjectSearchOption();
            option.setPageIndex(DEFAULT_PAGE_INDEX);
            option.setPageSize(MAX_PAGE_SIZE);

            if (query.getSearchTags() != null) {
                Set<String> searchTags = new HashSet<>();
                for (Message.ProjectQueryMessage.SearchTagEnum tag : query.getSearchTags()) {
                    // rework search tag转为查询project status, 同时需要过滤掉非指定的公司
                    if (Objects.equals(tag, Message.ProjectQueryMessage.SearchTagEnum.REWORK)) {
                        Optional.ofNullable(searchProperties)
                                .ifPresent(properties -> option.setSearchCompanyIdList(properties.getCompanyList()));
                        option.setProjectStatusList(List.of(NewProjectStatusEnum.PROJECT_REWORK.getCode()));
                        option.setTestFlag(false);
                    } else {
                        searchTags.add(tag.name());
                    }
                }

                if(searchTags.contains(ProjectSearchOption.SearchTagEnum.FOLLOW_UP.name())) {
                    var projectIds = projectService.getProjectIdsOfFollowUp();
                    option.setProjectIds(projectIds);
                }

                if(searchTags.contains(ProjectSearchOption.SearchTagEnum.RUSH_FOLDER.name())) {
                    Optional.ofNullable(ignoreProjectIdEnd)
                        .ifPresent(id -> option.setProjectIdStart(id + 1));
                }
                option.setSearchTag(searchTags);
            }

            List<ProjectTinyVo> projects;
            projects = projectService.getProjects(option);

            if (CollectionUtils.isEmpty(projects)) {
                return Collections.emptySet();
            }
            return toProject(projects);
        }

        private List<Project> toProject(List<ProjectTinyVo> projectTinyVos) {
            return projectTinyVos.stream()
                .map(
                    projectTinyVo -> {
                        var builder = com.bees360.project.Message.ProjectMessage.newBuilder();

                        builder.setId(String.valueOf(projectTinyVo.getProjectId()));

                        var address = com.bees360.project.base.Message.AddressMessage.newBuilder();
                        acceptIfNotNull(address::setCountry, projectTinyVo.getCountry());
                        var state = AmericaStateEnums.valueOfState(projectTinyVo.getState());
                        acceptIfNotNull(address::setState, state, AmericaStateEnums::getAbbreviation);
                        acceptIfNotNull(address::setCity, projectTinyVo.getCity());
                        acceptIfNotNull(address::setZipCode, projectTinyVo.getZipCode());
                        acceptIfNotNull(address::setAddressLine1, projectTinyVo.getAddress());
                        acceptIfNotNull(address::setAddressLine2, "");
                        builder.setAddress(address);

                        return Project.from(builder.build());
                    })
                .collect(Collectors.toList());
        }
    }
}
