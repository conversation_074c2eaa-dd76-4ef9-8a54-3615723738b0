package com.bees360.scheduletask.project;

import java.util.List;

import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;

/**
 * <AUTHOR>
 */
public class ExportProjectForSwyfftUtilNewNull implements ExportProjectForSwyfftUtilNew {

    private ExportProjectForSwyfftUtil util = new ExportProjectForSwyfftUtilV1();

    @Override
    public ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData fetchExcelData(List<Project> projects,
        List<BsExportData> bsExportDataList) {
        return util.fetchExcelData(projects, bsExportDataList);
    }
}
