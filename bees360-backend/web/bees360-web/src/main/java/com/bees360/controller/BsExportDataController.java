package com.bees360.controller;

import com.bees360.base.exception.ServiceMessageException;
import com.bees360.entity.BsExportData;
import com.bees360.service.BsExportDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/export-data")
public class BsExportDataController {

	@Autowired
    private BsExportDataService bsExportDataService;

	@GetMapping("/list")
	public List<BsExportData> list(String relatedId, String type) throws ServiceMessageException {
		return bsExportDataService.getExportData(relatedId,type);
	}

    @GetMapping()
    public BsExportData getByRelatedIdAndType(String relatedId, String type) throws ServiceMessageException {
        return bsExportDataService.getByRelatedIdAndType(relatedId,type);
    }

    @PutMapping()
    public BsExportData insertOrUpdateData(@RequestBody BsExportData param) throws ServiceMessageException {
	    return bsExportDataService.insertOrUpdateData(param);
    }

    @DeleteMapping("/{id:\\d+}")
    public void deleteExportData(@PathVariable long id) throws ServiceMessageException {
        bsExportDataService.deleteExportData(id);
    }

}
