package com.bees360.config;

import com.bees360.event.EventDispatcher;
import com.bees360.event.EventListener;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.RabbitEventDispatcher;
import com.bees360.event.RabbitEventPublisher;
import com.bees360.event.ServiceEventListenerRegistry;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import jakarta.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Log4j2
@Configuration
@Import({
    ServiceEventListenerRegistry.class
})
public class EventConfig {

    @Autowired
    private ObjectProvider<EventListener> eventListeners;

    @Autowired
    private EventDispatcher eventDispatcher;

    @PostConstruct
    public void postConstruct() {
        eventListeners.stream().forEach(listener -> {
            log.info("Try to enlist listener {}", listener);
            eventDispatcher.enlist(listener);
        });
    }

    @Import({RabbitEventPublisher.class, RabbitEventDispatcher.class})
    @Configuration
    @ConditionalOnProperty(name = "event", prefix = "type", havingValue = "rabbit", matchIfMissing = true)
    public static class RabbitEventConfig {

        @Bean
        @ConditionalOnProperty(prefix = "event", name = "rabbit.shutdown", havingValue = "graceful")
        RabbitEventDispatcherShutdownLifecycle rabbitEventDispatcherShutdownLifecycle(
            RabbitEventDispatcher rabbitEventDispatcher) {
            return new RabbitEventDispatcherShutdownLifecycle(rabbitEventDispatcher);
        }
    }

    @Import({InMemoryEventPublisher.class})
    @Configuration
    @ConditionalOnProperty(name = "event", prefix = "type", havingValue = "inmemory")
    public static class InMemoryConfig {

    }
}
