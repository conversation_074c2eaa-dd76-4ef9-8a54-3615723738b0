package com.bees360.config.checker;

import jakarta.inject.Inject;
import com.bees360.entity.Company;
import com.bees360.service.CompanyService;
import org.springframework.stereotype.Component;

@Component
public class CompanyChecker implements BasicChecker {

	@Inject
	private CompanyService companyService;

	@Override
	public void check(){
		checkInitialCompany();
	}

	public void checkInitialCompany() {
		boolean isEnterpriseCustomer = companyService.isCustomerContractSigned();

		if(isEnterpriseCustomer) {
			Company company = companyService.getDefaultCompany();
			if(company == null) {
				throw new RuntimeException("system has not yet initialize default company, server start failed");
			}
		}
	}
}
