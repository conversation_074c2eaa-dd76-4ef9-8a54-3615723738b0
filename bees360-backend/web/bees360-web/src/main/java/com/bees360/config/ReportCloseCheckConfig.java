package com.bees360.config;

import com.bees360.entity.Project;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.service.impl.ProjectReportFileServiceImpl;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.Iterables;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Configuration
@EnableConfigurationProperties
@Log4j2
public class ReportCloseCheckConfig {
    @Bean
    @ConfigurationProperties(prefix = "report.check.closeout-ignore-condition")
    Map<ProjectServiceTypeEnum, Set<ReportTypeEnum>> closeoutIgnoreCondition() {
        log.info("report.check.closeoutIgnoreCondition mapping");
        return new HashMap<>();
    }

    @Bean
    Predicate<Project> closeoutGeneratePredicate(
            Bees360FeatureSwitch bees360FeatureSwitch,
            Map<ProjectServiceTypeEnum, Set<ReportTypeEnum>> closeoutIgnoreCondition,
            ProjectReportProvider projectReportProvider) {
        return project -> {
            var serviceTypeEnum = ProjectServiceTypeEnum.getEnum(project.getServiceType());
            var isGenerate = ClaimTypeEnum.isUnderwriting(project.getClaimType());
            // 禁止 closeout report 生成校验，直接返回 true，即可以生成
            if (bees360FeatureSwitch.isDisableCloseoutReportGenerateCheck()) {
                return isGenerate;
            }
            log.info("check condition for closeout report generation: {}", closeoutIgnoreCondition);
            if (isGenerate && closeoutIgnoreCondition.containsKey(serviceTypeEnum)) {
                var sourceReportTypes =
                        Iterables.toStream(
                                        projectReportProvider.findAll(
                                                String.valueOf(project.getProjectId())))
                                .filter(
                                        report ->
                                                ReportGenerationStatusEnum.APPROVED.getCode()
                                                        == report.getStatus().getNumber())
                                .map(ProjectReportFileServiceImpl::getReportType)
                                .collect(Collectors.toList());

                var candidatesReportTypes =
                        closeoutIgnoreCondition.get(serviceTypeEnum).stream()
                                .map(ReportTypeEnum::getCode)
                                .collect(Collectors.toList());

                // After reports which specified in candidatesReportType generated, return to
                // avoid generation of closeout report
                return !CollectionUtils.containsAll(sourceReportTypes, candidatesReportTypes);
            }
            return isGenerate;
        };
    }
}
