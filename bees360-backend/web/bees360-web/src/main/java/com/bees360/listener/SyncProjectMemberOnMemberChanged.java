package com.bees360.listener;

import com.bees360.event.registry.ProjectMemberChanged;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.SyncProjectMemberJob;
import com.bees360.job.util.EventTriggeredJob;
import lombok.extern.log4j.Log4j2;

/**
 * 监听项目成员变更事件并触发同步项目成员信息的任务
 */
@Log4j2
public class SyncProjectMemberOnMemberChanged extends EventTriggeredJob<ProjectMemberChanged> {

    public SyncProjectMemberOnMemberChanged(JobScheduler jobScheduler) {
        super(jobScheduler);
    }

    @Override
    protected Job convert(ProjectMemberChanged projectMemberChanged) {
        var job = new SyncProjectMemberJob();
        job.setProjectId(Long.parseLong(projectMemberChanged.getProjectId()));
        job.setRoleId(projectMemberChanged.getRoleId());
        job.setUserId(projectMemberChanged.getUserTo());
        job.setCreatedBy(projectMemberChanged.getOperator());
        return Job.ofPayload(job);
    }
}
