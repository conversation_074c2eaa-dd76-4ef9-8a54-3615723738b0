package com.bees360.config;

import com.bees360.event.SyncImageFromFirebaseOnBeespilotImageUpload;
import com.bees360.job.FirebaseBatchChangedExecutorV2;
import com.bees360.job.FirebaseHoverChangedExecutorV2;
import com.bees360.job.FirebaseIBeesChangedExecutorV2;
import com.bees360.job.FirebaseIBeesMissionCompletedExecutorV2;
import com.bees360.job.FirebaseMagicplanChangedExecutorV2;
import com.bees360.job.FirebaseMissionChangedExecutorV2;
import com.bees360.job.FirebaseMissionCheckOutJobExecutor;
import com.bees360.job.FirebaseMissionCompletedStuckExecutorV2;
import com.bees360.job.FirebaseProjectChangedExecutorV2;

import com.bees360.job.SyncImageFromFirebaseJobExecutor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import(
        value = {
            FirebaseBatchChangedExecutorV2.class,
            FirebaseHoverChangedExecutorV2.class,
            FirebaseIBeesChangedExecutorV2.class,
            FirebaseIBeesMissionCompletedExecutorV2.class,
            FirebaseMagicplanChangedExecutorV2.class,
            FirebaseMissionChangedExecutorV2.class,
            FirebaseMissionCompletedStuckExecutorV2.class,
            FirebaseProjectChangedExecutorV2.class,
        })
public class FirebaseJobExecutorConfig {

    @Configuration
    @Import({
        FirebaseMissionCheckOutJobExecutor.class,
    })
    public static class FirebaseMissionCheckOutConfig {}

    @Configuration
    @Import({
        SyncImageFromFirebaseJobExecutor.class,
        SyncImageFromFirebaseOnBeespilotImageUpload.class,
    })
    public static class FirebaseMissionImageConfig {}
}
