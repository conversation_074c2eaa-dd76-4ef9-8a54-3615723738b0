package com.bees360.web.security.expression;

import jakarta.servlet.http.HttpServletRequest;

import org.springframework.security.core.Authentication;

/**
 * <AUTHOR>
 */
public interface UserSecurity {

    String REQUEST_ATTRI_MYSELF = "%s@MYSELF".formatted(SecurityAccess.class.toString());

    default boolean isMyself(Authentication authentication, long userId) {
        return isMyself(null, authentication, userId);
    }

    /**
     * 判断当前userId是否为当前用户
     * @param request 有助于缓存当前结果重复使用，可以为null
     */
    boolean isMyself(HttpServletRequest request, Authentication authentication, long userId);

    default boolean hasRole(Authentication authentication, String role) {
        return hasAnyRole(authentication, role);
    }

    boolean hasAnyRole(Authentication authentication, String... roles);
}
