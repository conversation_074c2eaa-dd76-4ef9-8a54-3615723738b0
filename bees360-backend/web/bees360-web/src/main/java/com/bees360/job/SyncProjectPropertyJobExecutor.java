package com.bees360.job;

import com.bees360.job.registry.SyncProjectPropertyJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.ProjectService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

@Log4j2
public class SyncProjectPropertyJobExecutor extends AbstractJobExecutor<SyncProjectPropertyJob> {
    private final ProjectService projectService;

    public SyncProjectPropertyJobExecutor(ProjectService projectService) {
        this.projectService = projectService;
        log.info("{} created: {ProjectService={}}", this, projectService);
    }

    @Override
    protected void handle(SyncProjectPropertyJob syncProjectPropertyJob) throws IOException {
        log.info(
                "Start to update project :{} propert :{}",
                syncProjectPropertyJob.getProjectId(),
                syncProjectPropertyJob.getNewBuildingType());
        projectService.updateProjectProperty(
                syncProjectPropertyJob.getProjectId(), syncProjectPropertyJob.getNewBuildingType());
    }
}
