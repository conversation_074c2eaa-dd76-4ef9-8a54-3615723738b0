package com.bees360.config.checker;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ConfigureChecker implements BasicChecker {

    @Value("${service.client.email}")
    private String serviceClientEmail;

	@Override
	public void check() {
		if (StringUtils.isEmpty(serviceClientEmail)) {
			throw new RuntimeException("the email of customer service cannot be empty");
		}
	}
}
