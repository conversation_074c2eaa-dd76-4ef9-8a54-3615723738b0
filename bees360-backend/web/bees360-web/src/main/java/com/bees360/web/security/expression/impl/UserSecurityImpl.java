package com.bees360.web.security.expression.impl;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;

import com.bees360.web.security.SecurityAttributeUtil;
import com.bees360.web.security.expression.UserSecurity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component("userSecurity")
public class UserSecurityImpl implements UserSecurity {

    @Override
    public boolean isMyself(HttpServletRequest request, Authentication authentication, long projectId) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_MYSELF,
            () -> judgeMyself(authentication, projectId));
    }

    private boolean judgeMyself(Authentication authentication, long userId) {
        Long userIdInAuth = SecurityAttributeUtil.getUserId(authentication);
        return userIdInAuth != null && userIdInAuth == userId;
    }

    @Override
    public boolean hasAnyRole(Authentication authentication, String... roles) {
        Collection<? extends GrantedAuthority> auths = authentication.getAuthorities();
        if (auths.isEmpty()) {
            return false;
        }
        Set<String> rolesInSecurity = auths.stream().map(r -> r.getAuthority()).collect(Collectors.toSet());
        for (String role : roles) {
            if (rolesInSecurity.contains(SecurityAttributeUtil.toRoleInSecurity(role))) {
                return true;
            }
        }
        return false;
    }
}
