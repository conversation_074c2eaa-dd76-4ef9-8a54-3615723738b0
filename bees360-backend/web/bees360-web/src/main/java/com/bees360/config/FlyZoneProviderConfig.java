package com.bees360.config;

import com.bees360.flyzone.DjiHttpFlyZoneQueryProvider;
import com.bees360.flyzone.FlyZoneType;
import com.bees360.flyzone.FlyZoneTypeProvider;
import com.bees360.http.HttpClient;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Log4j2
@Configuration
public class FlyZoneProviderConfig {

    @Bean("flyZoneTypeProvider")
    @ConditionalOnProperty(prefix = "address.fly-zone-type", name = "source", havingValue = "dji", matchIfMissing = true)
    public FlyZoneTypeProvider flyZoneTypeProvider(HttpClient httpClient) {
        var provider = new DjiHttpFlyZoneQueryProvider(httpClient);
        log.info("Created flyZoneTypeProvider({})", provider);
        return provider;
    }

    @Bean("flyZoneTypeProvider")
    @ConditionalOnProperty(prefix = "address.fly-zone-type", name = "source", havingValue = "fix")
    public FlyZoneTypeProvider fixFlyZoneTypeProvider(@Value("${address.fly-zone-type.fix:NONE}") FlyZoneType flyZoneType) {

        var provider = new FlyZoneTypeProvider() {
            @Override
            public FlyZoneType getFlyZoneType(double v, double v1) {
                return flyZoneType;
            }
        };

        log.info("Created flyZoneTypeProvider(provider={},flyZoneType={})", provider, flyZoneType);
        return provider;
    }
}
