package com.bees360.config;

import com.bees360.event.EventDispatcher;
import com.bees360.job.BatchSchedulePilotJobExecutor;
import com.bees360.job.JobScheduler;
import com.bees360.job.OpenProjectDataEmailExecutor;
import com.bees360.job.PilotMissionReworkJobExecutor;
import com.bees360.job.ProjectExportEmailSenderExecutor;
import com.bees360.job.ScheduledEmailExportEmailJobOnCronTrigger;
import com.bees360.job.SendEmailJobExecutor;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.msgutil.DelegateEmailSender;
import com.bees360.web.core.properties.bean.MailProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Map;

@Configuration
@Import(
        value = {
            BatchSchedulePilotJobExecutor.class,
            PilotMissionReworkJobExecutor.class,
            ProjectExportEmailSenderExecutor.class,
            OpenProjectDataEmailExecutor.class,
            FirebaseJobExecutorConfig.class,
        })
public class JobExecutorConfig {

    @Bean
    public SendEmailJobExecutor sendEmailJobExecutor(
        Map<String, DelegateEmailSender> emailSenderMap) {
        return new SendEmailJobExecutor(emailSenderMap);
    }

    @Bean
    ScheduledEmailExportEmailJobOnCronTrigger scheduledEmailExportEmailJobOnCronTrigger(
        JobScheduler jobScheduler,
        EventDispatcher eventDispatcher,
        MailProperties mailProperties,
        Bees360FeatureSwitch featureSwitch) {
        return new ScheduledEmailExportEmailJobOnCronTrigger(
                    jobScheduler,
                    eventDispatcher,
                    mailProperties.getTopicRecipients(),
                    featureSwitch);
    }
}
