package com.bees360.controller.management;

import jakarta.inject.Inject;

import com.bees360.config.support.CurUserId;
import com.bees360.entity.Product;
import com.bees360.entity.vo.ProductListVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bees360.service.ProductService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/24 17:54
 */
@RestController
@RequestMapping("/management/products")
public class ProductMController {

    @Inject
    private ProductService productService;

    @GetMapping("")
    public ProductListVo getProductManager(Integer productType, Boolean isPublished) throws Exception {
        List<Product> products = productService.listProducts(productType, isPublished);
        return new ProductListVo(products);
    }

    /**
     * 更新商品基本信息
     */
    @PutMapping("/{productId:\\d+}")
    public Product updateProduct(@CurUserId long userId, @PathVariable int productId, @RequestBody Product product)
        throws Exception {
        return productService.updateProductInfo(userId, productId, product);
    }
}
