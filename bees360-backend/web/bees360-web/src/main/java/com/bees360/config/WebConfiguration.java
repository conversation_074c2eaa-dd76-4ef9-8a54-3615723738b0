package com.bees360.config;

import com.bees360.service.grpc.AiGrpcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.beanvalidation.MethodValidationPostProcessor;

import jakarta.validation.Validator;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class WebConfiguration {

    @Autowired
    private Validator validator;

	@Bean
	public MethodValidationPostProcessor methodValidationPostProcessor() {
        MethodValidationPostProcessor methodValidationPostProcessor = new MethodValidationPostProcessor();
        methodValidationPostProcessor.setValidator(validator);
        return methodValidationPostProcessor;
	}

	@Bean
	public SettingInitialize settingInitialize() {
		return new SettingInitialize();
	}
}
