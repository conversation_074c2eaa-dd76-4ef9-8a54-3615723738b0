package com.bees360.config;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityEndpoint;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivitySyncManager;
import com.bees360.activity.Comment;
import com.bees360.activity.CommentEndpoint;
import com.bees360.activity.CommentManager;
import com.bees360.activity.CommentQuery;
import com.bees360.activity.GrpcActivityClient;
import com.bees360.activity.GrpcCommentClient;
import com.bees360.activity.Message;
import com.bees360.activity.ProjectActivityEndpoint;
import com.bees360.activity.ProjectCommentEndpoint;
import com.bees360.activity.config.GrpcActivityClientConfig;
import com.bees360.activity.config.GrpcCommentClientConfig;
import com.bees360.activity.impl.CompositeActivityManager;
import com.bees360.activity.impl.ResourceMetadataFillActivityManager;
import com.bees360.activity.impl.ResourceMetadataFillCommentManager;
import com.bees360.activity.impl.UserFillActivityManager;
import com.bees360.activity.impl.UserFillCommentManager;
import com.bees360.api.UnimplementedException;
import com.bees360.common.collections.ListUtil;
import com.bees360.commons.firebasesupport.entity.ContactQuestionRemoteConfig;
import com.bees360.commons.firebasesupport.service.RemoteConfigService;
import com.bees360.entity.Project;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.QuizTypeEnum;
import com.bees360.mapper.ProjectMapper;
import com.bees360.resource.GrpcResourceClient;
import com.bees360.resource.ResourceGetUrlProvider;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.ResourceUrlProvider;
import com.bees360.resource.util.ForwardingResourcePool;
import com.bees360.service.ProjectService;
import com.bees360.user.User;
import com.bees360.user.UserKeyProvider;
import com.bees360.user.UserProvider;
import com.bees360.user.config.GrpcUserKeyProviderConfig;
import com.bees360.user.util.ForwardingUser;
import com.bees360.user.util.ForwardingUserProvider;
import com.bees360.util.Iterables;
import com.bees360.web.event.project.NoInteriorDamageEvent;
import com.bees360.web.event.project.ProjectSubscribePlnarEvent;
import com.google.gson.Gson;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bees360.project.base.Message.ProjectType.CLAIM_TYPE;
import static com.bees360.project.base.Message.ProjectType.UNDERWRITING_TYPE;
import static com.bees360.project.base.Message.ProjectType.UNKNOWN_PROJECT_TYPE;

@Import(
        value = {
            ActivityEndpoint.class,
            CommentEndpoint.class,
            ProjectActivityEndpoint.class,
            ProjectCommentEndpoint.class,
            GrpcActivityClientConfig.class,
            GrpcCommentClientConfig.class,
            GrpcUserKeyProviderConfig.class,
        })
@Configuration
@Log4j2
public class ActivityConfig {

    @Autowired private ApplicationEventPublisher publisher;
    @Autowired private ProjectService projectService;
    @Autowired private RemoteConfigService remoteConfigService;

    @Bean
    @Primary
    public CompositeActivityManager activityManager(
        ProjectMapper projectMapper,
        GrpcActivityClient activityClient,
        UserKeyProvider userKeyProvider,
        UserProvider activityUserProvider,
        InnerResourcePool resourcePool) {
        ActivityManager userFillActivityManager =
                userFillActivityManager(
                        projectMapper,
                        activityClient,
                        userKeyProvider,
                    activityUserProvider);
        var fillResourceMetadataManager =
            new ResourceMetadataFillActivityManager(userFillActivityManager, resourcePool, resourcePool.asResourceUrlProvider());
        Map<String, ActivitySyncManager> compositeSyncManager = new ConcurrentHashMap<>();
        return new CompositeActivityManager(fillResourceMetadataManager, compositeSyncManager);
    }

    @Bean
    public InnerResourcePool innerResourcePool(GrpcResourceClient grpcResourceClient) {
        return new InnerResourcePool(
            grpcResourceClient, grpcResourceClient.asResourceUrlProvider());
    }

    @Bean
    public UserProvider activityUserProvider(UserProvider userProvider) {
        return new ForwardingUserProvider() {
            @Override
            protected UserProvider delegate() {
                return userProvider;
            }

            // UserFillActivityManager会批量获取用户ID，然后原来的用户ID作key来对用户分组，这里将user id的前缀去掉
            @Override
            public Iterable<? extends User> findUserById(Iterable<String> userIds) {
                Set<String> idSet = Iterables.toSet(userIds);
                ArrayList<User> userList = new ArrayList<>();
                Iterables.toStream(userProvider.findUserById(idSet))
                        .filter(Objects::nonNull)
                        .map(
                                user ->
                                        new ForwardingUser() {
                                            @Override
                                            protected User delegate() {
                                                return user;
                                            }
                                        })
                        .forEach(
                                user -> {
                                    com.bees360.user.Message.UserMessage userMessage =
                                            user.toMessage();
                                    String uid = userMessage.getUid() + "";
                                    if (idSet.contains(uid)
                                            && !Objects.equals(uid, userMessage.getId())) {
                                        User userWithUid =
                                                User.from(
                                                        userMessage.toBuilder()
                                                                .setId(userMessage.getUid() + "")
                                                                .build());
                                        userList.add(userWithUid);
                                    }
                                    userList.add(user);
                                });
                return userList.stream()
                        .collect(Collectors.toMap(User::getId, Function.identity(), (u1, u2) -> u1))
                        .values();
            }
        };
    }

    private ActivityManager userFillActivityManager(
        ProjectMapper projectMapper,
        ActivityManager activityManager,
        UserKeyProvider userKeyProvider,
        UserProvider activityUserProvider) {

        return new UserFillActivityManager(activityManager, userKeyProvider, activityUserProvider) {
            @Override
            public Iterable<String> submitActivities(Iterable<? extends Activity> activities) {
                Set<? extends Activity> activitySet = Iterables.toSet(activities);
                return super.submitActivities(
                        activitySet.stream().map(this::buildActivityWithSourceAndProjectType).collect(Collectors.toSet()));
            }

            @Override
            public String submitActivity(Activity originActivity) {
                // Set default source and project type
                var activity = buildActivityWithSourceAndProjectType(originActivity);
                return super.submitActivity(activity);
            }

            private Activity buildActivityWithSourceAndProjectType(Activity activity) {
                Message.ActivityMessage activityMessage = activity.toMessage();
                // 默认后端生成的activity的来源为WEB, 如果是前端创建的activity应该主动设置该值，否则该值会被自动设置为WEB
                if (StringUtils.isBlank(activity.getSource())) {
                    activityMessage =
                            activityMessage.toBuilder()
                                    .setSource(ActivitySourceEnum.WEB.getValue())
                                    .build();
                }
                // 如果是AI端触发的项目状态变更,且该状态是WEB端存在,则将其source设置为WEB,使得WEB可以看到该状态变更记录.
                String status = activity.getValue();
                if (Objects.equals(
                                activity.getEntityType(),
                                Message.ActivityMessage.EntityType.PROJECT.name())
                        && Objects.equals(
                                activity.getFiledName(),
                                Message.ActivityMessage.FieldName.STATUS.name())
                        && NewProjectStatusEnum.getEnumByValue(status) != null) {
                    activityMessage =
                            activityMessage.toBuilder()
                                    .setSource(ActivitySourceEnum.WEB.getValue())
                                    .build();
                }
                if (activity.getProjectType() == null
                        || UNKNOWN_PROJECT_TYPE.equals(activity.getProjectType())) {
                    Project project = projectMapper.getById(activity.getProjectId());
                    if (project != null && project.getClaimType() != null) {
                        activityMessage =
                                activityMessage.toBuilder()
                                        .setProjectType(
                                                ClaimTypeEnum.isClaim(project.getClaimType())
                                                        ? CLAIM_TYPE
                                                        : UNDERWRITING_TYPE)
                                        .build();
                    }
                }
                return Activity.of(activityMessage);
            }
        };
    }

    @Bean
    @Primary
    public CommentManager commentManager(
        final GrpcCommentClient commentClient, UserProvider activityUserProvider,
        InnerResourcePool resourcePool) {
        var manager =  new UserFillCommentManager(
                new CommentManager() {
                    @Override
                    public Comment findById(String s) {
                        return commentClient.findById(s);
                    }

                    @Override
                    public List<? extends Comment> getComments(CommentQuery commentQuery) {
                        return commentClient.getComments(commentQuery);
                    }

                    private String getUserId() {
                        Authentication authentication =
                                SecurityContextHolder.getContext().getAuthentication();
                        User user =
                                (User)
                                        Optional.ofNullable(authentication)
                                                .map(Authentication::getPrincipal)
                                                .orElse(null);
                        return Optional.ofNullable(user).map(User::getId).orElse(null);
                    }

                    @Override
                    @SuppressWarnings("unchecked")
                    public String addComment(Comment comment) {
                        if (StringUtils.isBlank(comment.getCreatedBy())) {
                            comment = comment.withCreatedBy(getUserId());
                        }
                        // 默认后端生成的comment的来源为WEB, 如果是前端创建的activity应该主动设置该值，否则该值会被自动设置为WEB
                        if (StringUtils.isBlank(comment.getSource())) {
                            comment =
                                    Comment.from(
                                            comment.toMessage().toBuilder()
                                                    .setSource(ActivitySourceEnum.WEB.getValue())
                                                    .build());
                        }
                        String id = null;
                        if (ActivitySourceEnum.BEESPILOT_QUIZ
                                .getValue()
                                .equals(comment.getSource())) {
                            // 同步问卷答案
                            String content =
                                    formatCustomerQuiz(
                                            comment.getProjectId(),
                                            new Gson()
                                                    .fromJson(comment.getContent(), HashMap.class));
                            if (StringUtils.isNotBlank(content)) {
                                comment =
                                        Comment.from(
                                                comment.toMessage().toBuilder()
                                                        .setContent(content)
                                                        .build());
                                id = commentClient.addComment(comment);
                            }
                            // If the content is empty, ignore.
                        } else {
                            id = commentClient.addComment(comment);
                        }
                        return Optional.ofNullable(id).orElse("");
                    }

                    @SneakyThrows
                    @Override
                    public String updateComment(Comment comment) {
                        return commentClient.updateComment(comment);
                    }

                    @SneakyThrows
                    @Override
                    public void deleteById(String s) {
                        Comment comment = findById(s);
                        commentClient.deleteById(s);
                    }

                    public String formatCustomerQuiz(long projectId, Map<String, Object> ans) {
                        if (!CollectionUtils.isEmpty(ans)) {
                            Map<String, ContactQuestionRemoteConfig> remoteConfigs =
                                    ListUtil.toMap(
                                            a -> a.getCode() + "",
                                            a -> a,
                                            remoteConfigService.getContactQuestion());
                            StringBuilder messages = new StringBuilder();
                            boolean shouldSubscribePlnar = false;
                            for (Map.Entry<String, Object> entry : ans.entrySet()) {
                                String id = entry.getKey();
                                ContactQuestionRemoteConfig remoteConfig = remoteConfigs.get(id);
                                if (remoteConfig == null) {
                                    continue;
                                }
                                String[] answers =
                                        setProjectCustomerContactQuestion(
                                                remoteConfig, entry.getValue(), messages);
                                // 如果问卷是ClaimDamageQuizCode 且回答不是No
                                if (ContactQuestionRemoteConfig.ClaimDamageQuizCode
                                                == remoteConfig.getCode()
                                        && answers.length > 0
                                        && !answers[0].equalsIgnoreCase("NO")) {
                                    shouldSubscribePlnar = true;
                                }
                            }
                            var project = projectService.getById(projectId);
                            if (shouldSubscribePlnar) {
                                publisher.publishEvent(
                                        new ProjectSubscribePlnarEvent(this, project));
                            } else {
                                publisher.publishEvent(
                                        new NoInteriorDamageEvent(this, project));
                            }

                            return messages.toString();
                        }
                        return null;
                    }

                    private String[] setProjectCustomerContactQuestion(
                            ContactQuestionRemoteConfig remoteConfig,
                            final Object value,
                            StringBuilder sb) {
                        if (value == null || StringUtils.isBlank(value.toString())) {
                            return new String[0];
                        }
                        String context = remoteConfig.getName();
                        // 插入题干
                        sb.append(context).append("\r\n");
                        String answer = value.toString();
                        List<String> choices = remoteConfig.getChoices();
                        String[] answers = answer.split("&&");
                        int type = remoteConfig.getType();
                        if ((QuizTypeEnum.FILL_A_DIGIT_BLANK.getCode() == type
                                || QuizTypeEnum.FILL_MANY_DIGIT_BLANKS.getCode() == type)) {

                            for (int i = 0; i < answers.length; i++) {
                                String _answer;
                                if (i == 0) {
                                    _answer = StringUtils.capitalize(answers[i]);
                                } else {
                                    _answer = answers[i];
                                }
                                sb.append(_answer).append(" ");
                                sb.append(getChoice(choices, i));
                                if (i != answers.length - 1) {
                                    sb.append(", ");
                                }
                            }
                        } else if ((QuizTypeEnum.FILL_MANY_BLANKS.getCode() == type
                                ||
                                // 字符填空
                                QuizTypeEnum.FILL_A_BLANK.getCode() == type)) {
                            for (int i = 0; i < answers.length; i++) {
                                String choice;
                                if (i == 0) {
                                    choice = StringUtils.capitalize(getChoice(choices, i));
                                } else {
                                    choice = getChoice(choices, i);
                                }
                                sb.append(choice).append(" ");
                                sb.append(answers[i]);
                                if (i != answers.length - 1) {
                                    sb.append(", ");
                                }
                            }
                        } else {
                            sb.append(StringUtils.capitalize(String.join(" ", answers)));
                        }
                        sb.append("\r\n\r\n");
                        return answers;
                    }

                    private String getChoice(List<String> choices, int index) {
                        if (CollectionUtils.isEmpty(choices) || index > choices.size() - 1) {
                            return " ";
                        }
                        return choices.get(index);
                    }
                },
                activityUserProvider);
        return new ResourceMetadataFillCommentManager(manager, resourcePool, resourcePool.asResourceUrlProvider());
    }

    static class InnerResourcePool extends ForwardingResourcePool implements ResourceUrlProvider {
        private final ResourcePool delegate;
        private final ResourceGetUrlProvider resourceGetUrlProvider;

        public InnerResourcePool(ResourcePool delegate, ResourceGetUrlProvider urlProvider) {
            this.delegate = delegate;
            this.resourceGetUrlProvider = urlProvider;
        }

        @Override
        public URL getGetUrl(String s) {
            return resourceGetUrlProvider.getGetUrl(s);
        }

        @Override
        protected ResourcePool delegate() {
            return delegate;
        }

        @Override
        public URL getPutUrl(String s, @Nullable ResourceMetadata resourceMetadata) {
            throw new UnimplementedException();
        }
    }
}
