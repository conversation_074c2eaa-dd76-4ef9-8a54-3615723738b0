package com.bees360.scheduletask.sandbox.sample;

import com.bees360.entity.Company;
import com.bees360.entity.Project;
import com.bees360.entity.User;
import com.bees360.resource.ResourcePool;
import com.google.gson.Gson;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Log4j2
@Order(Ordered.HIGHEST_PRECEDENCE)
@Component
public class ResourcePoolSandboxSampleProvider implements SandboxSampleProvider {

    private final ResourcePool resourcePool;
    private final Gson gson = new Gson();

    public ResourcePoolSandboxSampleProvider(ResourcePool resourcePool) {
        this.resourcePool = resourcePool;
        log.info("Created {}(resourcePool={})", this, resourcePool);
    }

    private String sandboxSampleResourceKey(Company company) {
        return "sample/customer/%s.json".formatted(company.getCompanyKey());
    }

    @Override
    public boolean supports(Company company) {
        var key = sandboxSampleResourceKey(company);
        var supported = resourcePool.containsKey(key);
        log.info("Resource {} found {} for customer {} with customer key {}", key, supported, company.getCompanyId(), company.getCompanyKey());
        return supported;
    }

    @Override
    public SandboxSample getSampleResource(Company company, Project project) {
        var key = sandboxSampleResourceKey(company);
        var resource = resourcePool.get(key);
        if (resource == null) {
            return null;
        }
        var json = resource.apply(in -> IOUtils.toString(in, StandardCharsets.UTF_8));
        var sample = gson.fromJson(json, SandboxSample.class);
        if (sample == null || CollectionUtils.isEmpty(sample.getProjectImages())) {
            return sample;
        }
        for (var image: sample.getProjectImages()) {
            image.setProjectId(project.getProjectId());
            image.setUserId(User.AI_ID);
            image.setUploadTime(System.currentTimeMillis());
        }
        return sample;
    }
}
