package com.bees360.controller;

import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.scheduletask.project.ScheduledCompanyProjectStatistics;
import com.bees360.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2019/07/23
 */
@RestController
@RequestMapping("/scheduling")
public class SchedulingController {

    @Autowired private ScheduledCompanyProjectStatistics scheduledCompanyProjectStatistics;

    @PostMapping("/proStatPerCom")
    public void doProjectStatSending() {
        scheduledCompanyProjectStatistics.sendCompanyProjectStats();
    }
}
