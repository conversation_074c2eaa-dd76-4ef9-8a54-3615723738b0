package com.bees360.config.jobsys;

import com.bees360.job.ExecutorConcurrentCount;
import com.bees360.job.JobExecutor;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.service.job.FirebaseProjectChangedExecutor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * 切分创建JobDispatcher和注册JobExecutor，防止出现循环依赖。
 *
 * <AUTHOR>
 */
@Log4j2
@Configuration
public class JobExecutorRegister {

    @Autowired private RabbitJobDispatcher jobDispatcher;

    @Autowired private List<JobExecutor> jobExecutors;

    @PostConstruct
    public void enlistJobExecutors() {
        jobExecutors.forEach(
                e -> {
                    log.info("Try to enlist executor: " + e);
                    // 设置FirebaseProjectChangedExecutor的并发数为当前处理器的两倍，尽可能快地同步firebase project
                    if (e instanceof FirebaseProjectChangedExecutor) {
                        jobDispatcher.enlist(e, 2 * Runtime.getRuntime().availableProcessors());
                    } else if (e instanceof ExecutorConcurrentCount count) {
                        jobDispatcher.enlist(e, count.getMaxConcurrentCount());
                    } else {
                        jobDispatcher.enlist(e);
                    }
                });
    }
}
