package com.bees360.config.project;

import com.bees360.listener.UpdateFirebaseParentChildProjectListener;
import com.bees360.project.config.GrpcProjectGroupManagerConfig;
import com.bees360.project.group.DefaultParentChildProjectManager;
import com.bees360.project.group.ParentChildProjectProvider;
import com.bees360.service.firebase.FirebaseProjectService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.function.Predicate;

@Configuration
@Import({
    GrpcProjectGroupManagerConfig.class,
    DefaultParentChildProjectManager.class,
})
public class ParentChildProjectConfig {

    @Bean("parentChildProjectPredicate")
    public Predicate<String> parentChildProjectPredicate(
        ParentChildProjectProvider parentChildProjectProvider) {
        return projectId -> parentChildProjectProvider.findByProjectId(projectId) != null;
    }

    @Bean("parentProjectPredicate")
    public Predicate<String> parentProjectPredicate(
        ParentChildProjectProvider parentChildProjectProvider) {
        return projectId -> {
            var group = parentChildProjectProvider.findByProjectId(projectId);
            return group != null && StringUtils.equals(group.getKey(), projectId);
        };
    }

    @Bean
    public UpdateFirebaseParentChildProjectListener updateFirebaseParentChildProjectListener(FirebaseProjectService firebaseProjectService) {
        return new UpdateFirebaseParentChildProjectListener(firebaseProjectService);
    }
}
