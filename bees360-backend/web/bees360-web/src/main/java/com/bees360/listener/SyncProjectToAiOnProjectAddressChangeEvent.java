package com.bees360.listener;

import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.UncheckedServiceException;
import com.bees360.service.ProjectService;
import com.bees360.web.event.project.ProjectAddressChangeEvent;
import lombok.extern.log4j.Log4j2;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.event.TransactionalEventListener;

@Log4j2
public class SyncProjectToAiOnProjectAddressChangeEvent  {

    private final ProjectService projectService;

    public SyncProjectToAiOnProjectAddressChangeEvent(ProjectService projectService) {
        this.projectService = projectService;
        log.info("Created {}(projectService={})", this, projectService);
    }

    @Async
    @TransactionalEventListener(fallbackExecution = true)
    public void syncProjectToAi(ProjectAddressChangeEvent event) {
        var projectId = event.getProject().getProjectId();
        log.info("Transfer data of project {} to ai on event {}.", projectId, event);
        try {
            projectService.transferDatasToAi(projectId, "ADDRESS_CHANGED");
        } catch (ServiceException e) {
           throw new UncheckedServiceException(e);
        }
    }
}
