package com.bees360.scheduletask.project;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.bees360.entity.Project;
import com.google.gson.annotations.SerializedName;

import lombok.Data;

/**
 * 导出文件 project/export/export_template_Swyfft_UW_form.xlsx的内容
 *
 * <AUTHOR>
 */
@Data
public class ExportProjectForSwyfft {
    @ExcelIgnore
    private long projectId;

    private String row1 = ""; // 1

    /**
     * 对应报告template中的 inspectionInfo.inspectionNumber 取值 {@link Project#getInspectionNumber()}
     */
    private String inspectionNumber = ""; // 2

    /**
     * 对应报告模板中的insuredInfo.assetOwnerName 取值 {@link Project#getAssetOwnerName()}
     */
    private String assetOwnerName = ""; // 3

    private String caseType = ""; // 4

    private String completionDate = ""; // 5

    private String outBuilding = ""; // 6
    /**
     * 对应报告模板中inspectionInfo.yearBuilt 取值 {@link Project#getYearBuilt()}
     */
    private String yearBuilt = ""; // 7

    private String hazardsPresent = ""; // 8

    private String areaEconomy = ""; // 9

    private String nuisancesPresent = ""; // 10

    private String overallBldgCondition = ""; // 11

    private String dwellingType = ""; // 12

    private String construction = ""; // 13

    private String descAddlStr = ""; // 14

    private String windProtection = ""; // 15

    private String divingBoardSlide = ""; // 16

    private String dmgBldg = ""; // 17

    private String petType = ""; // 18

    private String dogsAnimals = ""; // 19

    private String estimatedRoofAge = ""; // 20

    private String livingArea = ""; // 21

    private String estimatedRoofRMNGLife = ""; // 22

    private String foundation = ""; // 23

    private String gatedCommunity = ""; // 24

    private String hazardsComments = ""; // 25

    private String porch = ""; // 26

    private String exteriorHouseSatisfactory = ""; // 27

    private String hurricaneStraps = ""; // 28

    private String neighborhoop = ""; // 29

    private String stories = ""; // 30

    private String observedDamage = ""; // 31

    private String opinionJustification = ""; // 32

    private String other = ""; // 33

    private String otherRoof = ""; // 34

    private String outBuildingType = ""; // 35

    private String overallBldgAppearance = ""; // 36

    private String percentCompositeShingles = ""; // 37

    private String percentBuildupRoofNoGravel = ""; // 38

    private String percentClayConcreteTiles = ""; // 39

    private String percentFlat = ""; // 40

    private String percentGable = ""; // 41

    private String percentHip = ""; // 42

    private String percentLightMetalPanels = ""; // 43

    private String percentSinglePlyMembrane = ""; // 44

    private String percentSinglePlyMembraneBallasted = ""; // 45

    private String percentSlate = ""; // 46

    private String percentStandingSeamMetalRoof = ""; // 47

    private String percentWoodenShingles = ""; // 48

    private String poolCage = ""; // 49

    private String poolFence = ""; // 50

    private String swimmingPool = ""; // 51

    private String inspectionSummary = ""; // 52

    private String overallRoofCondition = ""; // 53

    private String commentToRoof = ""; // 54

    private String roofCovering = ""; // 55

    private String roofGeometry = ""; // 56

    private String riskOpinion = ""; // 57

    private String shutters = ""; // 58

    private String specialInstructionComments = ""; // 59
    /**
     * 对应报告模板中的 insuredInfo.claimNote 取值 {@link Project#getClaimNote()}
     */
    private String claimNote = ""; // 60
    /**
     * 对应报告模板中的 toDateString(inspectionInfo.inspectionTime) 取值 {@link Project#getInspectionTime()}
     * 请务必保持和模板中的占位符大小写一直，否则时间格式化将没有效果
     */
    @DateTimeFormat("MM/dd/yyyy")
    private Date inspectionTime = null;

    private String lotSize = ""; // 62

    private String visibleIndicationsofHurricane = ""; // 63

    @SerializedName("inspect_days_to_complete")
    private String inspectDaysToComplete = ""; // 64

    private String garageType = ""; // 65

    private String pets = ""; // 66

    private String dogSign = ""; // 67

    private String solarPanel = ""; // 68

    private String cuppingCurling = ""; // 69

    private String warpedRoofDecking = ""; // 70

    private String foundationDamages = ""; // 71
}
