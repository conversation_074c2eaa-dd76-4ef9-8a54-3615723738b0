package com.bees360.controller.management;

import java.util.List;

import com.bees360.service.export.PhotoExporter;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bees360.base.exception.ServiceException;
import com.bees360.service.export.ExportableDocument;
import com.bees360.service.export.ReportExporter;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Log4j2
@RestController
@RequestMapping("/management/export")
@RequiredArgsConstructor
public class ExportMController {

    private final ReportExporter reportExporter;

    private final PhotoExporter photoExporter;


    @GetMapping("/report")
    public List<ExportableDocument> listReports(@RequestParam long projectId) throws ServiceException {
        return reportExporter.collectReports(projectId);
    }

    @GetMapping("/images")
    public List<ExportableDocument> listImages(@RequestParam long projectId) throws ServiceException {
        return photoExporter.collectPhotos(projectId);
    }

}
