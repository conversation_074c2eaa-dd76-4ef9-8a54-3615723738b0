package com.bees360.controller;

import java.util.List;

import jakarta.inject.Inject;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bees360.entity.vo.HistoryLogVo;
import com.bees360.service.EventHistoryService;

/**
 * <AUTHOR>
 * @date 2019/09/23 16:53
 */
@RestController
@RequestMapping("/projects/{projectId:\\d+}")
public class ProjectLogController {

    @Inject
    EventHistoryService eventHistoryService;

    @GetMapping("/logs")
    public List<HistoryLogVo> listProjectLog(@PathVariable long projectId) throws Exception {
        return eventHistoryService.listHistoryLogs(projectId);
    }
}
