package com.bees360.config.checker;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.PostConstruct;

public class CheckerProcessor {

	private Logger logger = LoggerFactory.getLogger(getClass());

	private List<BasicChecker> checkers = new ArrayList<>();

	@PostConstruct
	public void check() {
		if(checkers == null) {
			return;
		}
		for (BasicChecker checker : checkers) {
			checker.check();

			logger.info(getClass().getSimpleName() + ": " + checker.getClass().getSimpleName() + " pass.");
		}
	}

	public List<BasicChecker> getCheckers() {
		return checkers;
	}

	public void addChecker(BasicChecker checker) {
		if(checkers == null) {
			checkers = new ArrayList<>();
			checkers.add(checker);
		} else {
			checkers.add(checker);
		}
	}

	public void setCheckers(List<BasicChecker> checkers) {
		this.checkers = checkers;
	}
}
