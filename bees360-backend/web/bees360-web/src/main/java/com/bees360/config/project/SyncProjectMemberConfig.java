package com.bees360.config.project;

import com.bees360.job.SyncProjectMemberJobExecutor;
import com.bees360.listener.SyncProjectMemberOnMemberChanged;
import com.bees360.mapper.MemberMapper;
import com.bees360.user.User;
import com.bees360.user.UserKeyProvider;
import com.bees360.user.UserProvider;
import java.util.function.Function;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        name = "sync-project-member",
        havingValue = "true",
        matchIfMissing = true)
@Configuration
@Import({
    SyncProjectMemberOnMemberChanged.class,
})
public class SyncProjectMemberConfig {

    @Bean
    SyncProjectMemberJobExecutor syncProjectMemberJobExecutor(
        MemberMapper memberMapper,
        UserProvider userProvider,
        UserKeyProvider userKeyProvider) {
        var findUserById = findUserById(userProvider, userKeyProvider);
        return new SyncProjectMemberJobExecutor(memberMapper, findUserById);
    }

    Function<String, User> findUserById(UserProvider userProvider, UserKeyProvider userKeyProvider) {
        return userId -> {
            var user = userProvider.findUserById(userId);
            if (user != null) {
                return user;
            }
            return userKeyProvider.findUserByKey(userId);
        };
    }
}
