package com.bees360.controller;

import com.bees360.base.MapBuilder;
import com.bees360.base.exception.ServiceMessageException;
import com.bees360.config.support.CurUserId;
import com.bees360.entity.ReportTemplateCreateParam;
import com.bees360.entity.vo.ReportTemplateVo;
import com.bees360.service.PreferenceService;
import java.util.List;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.inject.Inject;
import java.util.Map;

@RestController
@RequestMapping("/preferences")
public class PreferenceController {

	@Inject
	PreferenceService preferenceService;

	@GetMapping("/project-template")
	public Map<String, Object> customProject(@CurUserId long userId) {
		String customizedProjectCode = preferenceService.getCustomizedProjectCode(userId);
		return MapBuilder.result("templateCode", customizedProjectCode);
	}

	@PostMapping("/template")
    public void addProjectTemplate(@CurUserId long userId, @RequestBody ReportTemplateCreateParam param) throws ServiceMessageException {
        preferenceService.addProjectTemplate(userId, param);
    }

    @PostMapping("/company/template")
    public void addCompanyTemplate(@RequestBody List<ReportTemplateCreateParam> params) {
        preferenceService.addCompanyTemplates(params);
    }

    @GetMapping("/company/{companyId:\\d+}/template")
    public List<ReportTemplateVo> listCompanyTemplate(@PathVariable long companyId, int reportType) {
        return preferenceService.listCompanyTemplates(companyId, reportType);
    }

    @DeleteMapping("/template/{templateId:\\d+}")
    public void deleteProjectTemplate(@CurUserId long userId, @PathVariable long templateId) throws ServiceMessageException {
        preferenceService.deleteProjectTemplate(userId, templateId);
    }
}
