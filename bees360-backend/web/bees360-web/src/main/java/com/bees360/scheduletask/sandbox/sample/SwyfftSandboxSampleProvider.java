package com.bees360.scheduletask.sandbox.sample;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Company;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.User;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
@Component
public class SwyfftSandboxSampleProvider extends ClassPathJsonImageSampleProvider {

    private static final Set<String> companiesSupported = Collections.unmodifiableSet(
        Sets.newHashSet("Swyfft Underwriting", "Swyfft Insurance", "Swyfft Homeowners Insurance", "Bees360 for Swyfft"));

    private static final Gson gson = new Gson();

    public SwyfftSandboxSampleProvider() throws ServiceException {
        super();
    }

    @Override
    public boolean supports(Company company) {

        return company != null && companiesSupported.contains(company.getCompanyName());
    }

    @Override
    protected String getJsonPath() {
        return SandboxSampleResource.SWYFFT_JSON_IMAGE_FILE_PATH;
    }

    @Override
    protected SandboxSampleResource.FileResource getImageArchive() {
        return SandboxSampleResource.SwyfftResources.IMAGE_ARCHIVE;
    }

    @Override
    protected List<SandboxSampleResource.ReportResource> getReports() {
        return Collections.singletonList(SandboxSampleResource.SwyfftResources.REPORT);
    }

    @Override
    protected List<ProjectImage> getImages(long projectId){

        return  getImageSamples().stream()
            .peek(image -> {
                image.setProjectId(projectId);
                image.setUserId(User.AI_ID);
                image.setUploadTime(System.currentTimeMillis());
            })
            .collect(Collectors.toList());
    }

    @Override
    protected Map<ReportTypeEnum, ReportSummaryVo> getReportSummaries() {
        Map<ReportTypeEnum, ReportSummaryVo> reportSummaryMap = new HashMap<>();
        String summary = SandboxSampleResource.getReportSummaryJson();

        Arrays.stream(ReportTypeEnum.values()).forEach(
            r -> reportSummaryMap.put(r, gson.fromJson(summary, ReportSummaryVo.class)));

        return reportSummaryMap;
    }
}
