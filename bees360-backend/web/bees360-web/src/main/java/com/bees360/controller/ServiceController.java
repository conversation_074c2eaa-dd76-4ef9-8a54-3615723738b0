package com.bees360.controller;

import java.util.Map;
import jakarta.inject.Inject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.MessageSource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bees360.entity.ContactUs;
import com.bees360.entity.PartnerProgram;
import com.bees360.service.MessageService;
import com.bees360.service.ServiceService;


@RestController
@RequestMapping("/service")
public class ServiceController {

	@Inject
	MessageSource messageSource;

	@Inject
	MessageService messageService;

	@Inject
	ServiceService serviceService;

	@PostMapping(value = "/contact-us")
	public void contactUs(@RequestBody ContactUs contactUs) throws Exception {
		serviceService.contactUs(contactUs);
	}

	@PostMapping("/partner-program")
	public void postPartnerProgram(@RequestBody PartnerProgram partnerProgram) throws Exception {
		serviceService.postPartnerProgram(partnerProgram);
	}
}
