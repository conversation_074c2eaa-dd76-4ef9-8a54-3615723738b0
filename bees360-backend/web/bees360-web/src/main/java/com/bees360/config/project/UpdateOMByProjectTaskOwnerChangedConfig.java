package com.bees360.config.project;

import com.bees360.event.EventListener;
import com.bees360.event.util.EventListeners;
import com.bees360.event.util.PipelineTaskChangedListeners;
import com.bees360.service.MemberService;
import com.bees360.service.listener.project.UpdateOMByProjectTaskOwnerChangeListener;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@ConditionalOnProperty(
        prefix = "pipeline.om-change",
        value = "enabled",
        havingValue = "true",
        matchIfMissing = true)
@Configuration
public class UpdateOMByProjectTaskOwnerChangedConfig {
    @Data
    @ConfigurationProperties("pipeline.om-change")
    @EnableConfigurationProperties
    @Configuration
    public static class OMChangedByTaskOwnerChangeProperties {
        private final List<String> targetTasks;
    }

    @Bean
    EventListener updateOMByProjectTaskOwnerChangedListener(
            @Autowired MemberService memberService,
            @Autowired OMChangedByTaskOwnerChangeProperties properties) {
        var listener =
                new UpdateOMByProjectTaskOwnerChangeListener(
                        memberService, properties.getTargetTasks());
        return PipelineTaskChangedListeners.forwardToOwnerChangedListener(
                listener, EventListeners.getListenerName(listener.getClass()), null);
    }
}
