package com.bees360.controller.management;

import jakarta.inject.Inject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bees360.entity.Company;
import com.bees360.entity.dto.CompanySearchOption;
import com.bees360.entity.vo.PageResult;
import com.bees360.service.CompanyService;

@RestController
@RequestMapping("/management/companies")
public class CompanyMController {

	@Inject
	private CompanyService companyService;

	@GetMapping("")
	public PageResult<Company> pageCompanies(CompanySearchOption option) throws Exception {

		return companyService.pageCompanies(option);
	}

	@PostMapping("")
	public Company createCompany(@RequestBody Company company) throws Exception {
		return companyService.createCompany(company);
	}

	@GetMapping("/{companyId:\\d+}")
	public Company getCompany(@PathVariable long companyId) throws Exception {
		return companyService.getById(companyId);
	}

	@PutMapping("/{companyId:\\d+}")
	public Company updateCompany(@PathVariable long companyId, @RequestBody Company company) throws Exception {
		return companyService.updateCompany(companyId, company);
	}

	@PatchMapping("/{companyId:\\d+}/logo")
	public Company updateCompanyLogo(@PathVariable long companyId, @RequestBody Company company)
			throws Exception {
		return companyService.updateCompanyLogo(companyId, company.getLogo());
	}

    @PatchMapping("/{companyId:\\d+}/key")
    public Company updateCompanyKey(@PathVariable long companyId, @RequestBody Company company) {
        return companyService.updateCompanyKey(companyId, company.getCompanyKey());
    }
}
