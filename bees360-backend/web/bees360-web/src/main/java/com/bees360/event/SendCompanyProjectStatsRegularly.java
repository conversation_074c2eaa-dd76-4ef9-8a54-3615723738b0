package com.bees360.event;

import com.bees360.common.util.DateUtil;
import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.entity.dto.ProjectStatistics;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.event.registry.CronTriggerDailyAt9AmCst;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.MessageService;
import com.bees360.service.statistics.ProjectStatisticsService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Map;

/**
 * 定时发送公司项目统计数据的类，每天上午9点触发并汇总前一天的统计数据通过消息服务发送。
 */
@Log4j2
public class SendCompanyProjectStatsRegularly extends AbstractNamedEventListener<CronTriggerDailyAt9AmCst> {

    private static final String DEFAULT_TIME_ZONE = AmericaTimeZone.US_CENTRAL;

    private final ProjectStatisticsService projectStatisticsService;

    private final MessageService messageService;

    public SendCompanyProjectStatsRegularly(
            ProjectStatisticsService projectStatisticsService,
            MessageService messageService) {
        this.projectStatisticsService = projectStatisticsService;
        this.messageService = messageService;
        log.info("Created {}(projectStatisticsService={},messageService={})", this, projectStatisticsService, messageService);
    }

    @Override
    public void handle(CronTriggerDailyAt9AmCst event) throws IOException {
        final ZoneId zoneId = ZoneId.of(DEFAULT_TIME_ZONE);
        final LocalDate yesterday = LocalDate.now(zoneId).minusDays(1);
        final LocalDateTime start = LocalDateTime.of(yesterday, LocalTime.MIN);
        final LocalDateTime end = LocalDateTime.of(yesterday, LocalTime.MAX);
        final Instant instantStart = DateUtil.toInstant(start, zoneId);
        final Instant instantEnd = DateUtil.toInstant(end, zoneId);

        log.info("SendCompanyProjectStatsRegularly with time range({}, {}) triggered by event {}", instantStart, instantEnd, event);
        final Map<InspectionPurposeTypeEnum, ProjectStatistics.ProjectStatsPerCompanySummary> projectStatsSummaries =
            projectStatisticsService.projectStatsSummary(instantStart, instantEnd);
        messageService.sendProjectStatisticsPerCompany(projectStatsSummaries, yesterday);
    }
}
