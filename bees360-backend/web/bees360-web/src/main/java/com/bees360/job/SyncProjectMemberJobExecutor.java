package com.bees360.job;

import com.bees360.entity.Member;
import com.bees360.entity.User;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.job.registry.SyncProjectMemberJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.mapper.MemberMapper;
import com.bees360.util.DateUtil;
import com.bees360.util.user.UserAssemble;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class SyncProjectMemberJobExecutor extends AbstractJobExecutor<SyncProjectMemberJob> {
    private final MemberMapper memberMapper;
    private final List<Integer> syncMemberList =
            List.of(
                    RoleEnum.SCHEDULER.getCode(),
                    RoleEnum.FIELD_COORDINATOR.getCode(),
                    RoleEnum.FIELD_MANAGER.getCode());
    private final Function<String, com.bees360.user.User> findUserById;

    public SyncProjectMemberJobExecutor(
        MemberMapper memberMapper,
        Function<String, com.bees360.user.User> findUserById) {
        this.memberMapper = memberMapper;
        this.findUserById = findUserById;
        log.info(
                "Created {}(memberMapper:{},findUserById:{})",
                this,
                memberMapper,
                findUserById);
    }

    @Override
    protected void handle(SyncProjectMemberJob syncProjectMemberJob) throws IOException {
        log.info("Start to update project member on member changed :{}", syncProjectMemberJob);
        var projectId = syncProjectMemberJob.getProjectId();
        var roleId = syncProjectMemberJob.getRoleId();
        if (!syncMemberList.contains(roleId)) {
            return;
        }
        var curMemberId =
                memberMapper.listActiveMember(projectId).stream()
                        .filter(m -> Objects.equals(m.getRole(), roleId))
                        .map(Member::getUserId)
                        .findAny()
                        .orElse(null);
        // remove member
        if (syncProjectMemberJob.getUserId() == null && curMemberId != null) {
            memberMapper.delete(projectId, curMemberId, roleId);
            return;
        }

        var webUser = UserAssemble.toWebUser(findUserById.apply(syncProjectMemberJob.getUserId()));
        var webOpUser = UserAssemble.toWebUser(findUserById.apply(syncProjectMemberJob.getCreatedBy()));

        var webUserId = webUser.getUserId();
        if (curMemberId != null) {
            if (curMemberId == webUser.getUserId()) {
                return;
            }
            memberMapper.delete(projectId, curMemberId, roleId);
        }
        Member member = memberMapper.getMemberByRoleAndUserId(projectId, webUserId, roleId);
        if (member != null) {
            memberMapper.activeMember(projectId, webUserId, roleId);
        } else {
            var newMember = new Member();
            newMember.setProjectId(projectId);
            newMember.setCreatedBy(
                    Optional.ofNullable(webOpUser).map(User::getUserId).orElse(User.AI_ID));
            newMember.setCreatedTime(DateUtil.getNow());
            newMember.setDeleted(false);
            newMember.setDescription("");
            newMember.setRole(roleId);
            newMember.setUserId(webUserId);
            memberMapper.insert(newMember);
        }
    }
}
