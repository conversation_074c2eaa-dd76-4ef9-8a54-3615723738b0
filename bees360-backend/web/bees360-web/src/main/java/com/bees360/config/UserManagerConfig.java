package com.bees360.config;


import com.bees360.user.GrpcUserManager;
import com.bees360.user.GrpcUserProvider;
import com.bees360.user.UserProvider;
import com.bees360.user.config.GrpcUserManagerConfig;
import com.bees360.user.config.GrpcUserProviderConfig;
import jakarta.annotation.PostConstruct;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({GrpcUserProviderConditionalConfig.class, GrpcUserManagerConditionalConfig.class})
@Configuration
public class UserManagerConfig {

    /**
     * 如果后续将所有的用户都在user-app中汇集，则web端对外只需提供内部的用户，而不需包含grpcUserProvider外部用户
     */
    @Bean(name = {"grpcUserServiceUserProvider", "userEndpointUserProvider", "userProvider"})
    public UserProvider userProvider(GrpcUserProvider grpcUserProvider) {
        return grpcUserProvider;
    }

    @ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        value = "enable-user-key",
        havingValue = "false",
        matchIfMissing = true)
    @Bean(name = "pilotUserProvider")
    public UserProvider enableUserKeyPilotProvider(UserProvider userProvider) {
        return userProvider;
    }

    @ConditionalOnProperty(
        prefix = "bees360.feature-switch",
        value = "enable-user-key",
        havingValue = "true")
    @Bean(name = "pilotUserProvider")
    public UserProvider disableUserKeyPilotProvider(UserProvider grpcUserProvider) {
        return grpcUserProvider;
    }
}

@ConditionalOnProperty(prefix = "grpc.client.userProvider", name = "address")
@Configuration
@Import(GrpcUserProviderConfig.class)
class GrpcUserProviderConditionalConfig {
    @PostConstruct
    void logInfo() {
        System.out.println("initializing GrpcUserProvider success");
    }
}

@ConditionalOnProperty(
    prefix = "bees360.feature-switch",
    name = "disable-authing-user-manager",
    havingValue = "false",
    matchIfMissing = true)
@Configuration
@Import(GrpcUserManagerConfig.class)
class GrpcUserManagerConditionalConfig {

    @Bean(name = {"userManager", "grpcUserProvider"})
    GrpcUserManager userManager(GrpcUserManager grpcUserManager) {
        return grpcUserManager;
    }

    @PostConstruct
    void logInfo() {
        System.out.println("initializing GrpcUserManager success");
    }
}
