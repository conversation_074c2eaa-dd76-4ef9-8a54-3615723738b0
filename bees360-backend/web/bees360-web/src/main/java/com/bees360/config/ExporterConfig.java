package com.bees360.config;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.openapi.OpenReportIdTypeVo;
import com.bees360.entity.openapi.OpenReportSummaryVo;
import com.bees360.entity.openapi.OpenReportVo;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.openapi.OpenApiProjectService;
import com.bees360.service.openapi.OpenReportService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.ByteString;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ExporterConfig {

    /**
     * find summary resource with report id.
     */
    @Bean
    public Function<String, Resource> summaryResourceProvider(OpenReportService openReportService,
        ObjectMapper objectMapper) {
        return (reportId) -> {
            try {
                OpenReportSummaryVo summary = openReportService.getReportSummary(reportId);
                var reportSummary = new OpenReportVo.OpenReportListWrapperVo(summary);
                var summaryJson = objectMapper.writeValueAsString(reportSummary);
                return Resource.of(ByteString.copyFrom(summaryJson, StandardCharsets.UTF_8));
            } catch (ResourceNotFoundException e) {
                return null;
            } catch (Exception e) {
                throw new IllegalStateException("Fail to get report summary of report " + reportId, e);
            }
        };
    }

    /**
     * find report resource with report id.
     */
    @Bean
    public Function<String, Resource> reportResourceProvider(ProjectReportFileService projectReportFileService,
        ResourcePool resourcePool) {
        return (reportId) -> {
            var report = projectReportFileService.getById(reportId);
            if (report == null) {
                return null;
            }
            var resourceKey = report.getReportPdfFileName();
            return resourcePool.get(resourceKey);
        };
    }

    /**
     * find all exportable report with project id. All of the reports will be sent to customer.
     */
    @Bean
    public Function<String, Iterable<String>> exportableReportProvider(OpenApiProjectService openApiProjectService) {
        return (projectId) -> {
            List<OpenReportIdTypeVo> reports = null;
            try {
                reports = openApiProjectService.listProjectReport(Long.valueOf(projectId));
            } catch (ServiceException e) {
                throw new IllegalStateException("Fail to get report of project " + projectId, e);
            }
            return reports.stream().map(OpenReportIdTypeVo::getId).collect(Collectors.toList());
        };
    }

}
