package com.bees360.config.project;

import com.bees360.address.AddressImageResourceProvider;
import com.bees360.address.AddressProvider;
import com.bees360.address.GoogleAddressImageProvider;
import com.bees360.address.GoogleGeoApi;
import com.bees360.address.config.GoogleGeoApiConfig;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.ImageTagManager;
import com.bees360.job.UploadAddressImagesToProjectJobExecutor;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.ProjectIIRepository;
import com.bees360.resource.AliasResourceRepository;
import com.bees360.resource.Message;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.util.ContentTypes;
import com.bees360.service.ProjectImageService;
import com.bees360.service.ProjectStatusService;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;
import java.util.UUID;
import java.util.function.Function;

@Import({
    GoogleGeoApiConfig.class,
})
@Log4j2
@Configuration
@ConditionalOnProperty(
    prefix = "bees360.feature-switch",
    name = "enable-upload-address-images-to-project",
    havingValue = "true")
public class UploadAddressImageToProjectConfig {
    public static final Function<Resource, String> MD5_ETAG_RESOURCE_KEY_GENERATOR =
        (resource) -> {
            var metadata = resource.getMetadata();
            if (Message.MetadataMessage.getDefaultInstance().equals(metadata.toMessage())) {
                metadata = resource.apply(ResourceMetadata::extractFrom);
            }
            var key = AliasResourceRepository.createMD5Alias("", metadata);
            if (StringUtils.isEmpty(key)) {
                key = UUID.randomUUID().toString();
            }
            return key;
        };

    @Bean
    AddressImageResourceProvider addressImageResourceProvider(
        AddressProvider addressProvider, GoogleGeoApi googleGeoApi) {
        log.info("Created addressImageResourceProvider with addressProvider={}", addressProvider);
        return new GoogleAddressImageProvider(addressProvider::findById, googleGeoApi);
    }

    @Bean
    UploadAddressImagesToProjectJobExecutor uploadAddressImagesToProjectExecutor(
        ResourcePool resourcePool,
        ProjectIIRepository projectIIRepository,
        AddressImageResourceProvider addressImageResourceProvider,
        ProjectImageService projectImageService,
        ImageTagManager imageTagManager,
        PipelineService pipelineService,
        ProjectStatusService projectStatusService) {
        Function<Resource, String> addressImageResourceUploader =
            resource -> {
                var key = MD5_ETAG_RESOURCE_KEY_GENERATOR.apply(resource);
                resourcePool.put(key, resource);
                return key;
            };

        Function<Resource, String> addressImageResourceFileNameConverter =
            resource -> {
                var metadata = resource.getMetadata();
                var eTag = metadata.getETag().replaceAll("\"", "");
                var contentType = metadata.getContentType();
                return "elevation_" + eTag + ContentTypes.getFileExtension(contentType);
            };

        return new UploadAddressImagesToProjectJobExecutor(
            projectIIRepository,
            addressImageResourceProvider,
            projectImageService,
            imageTagManager,
            pipelineService,
            projectStatusService,
            addressImageResourceUploader,
            addressImageResourceFileNameConverter,
            FileSourceTypeEnum.DRONE_IMAGE,
            ImageTypeEnum.ELEVATION,
            List.of(ImageTagEnum.ELEVATION));
    }
}
