package com.bees360.controller;

import com.bees360.base.MessageCode;
import com.bees360.base.ResponseJson;
import com.bees360.base.exception.ServiceException;
import com.bees360.config.support.CurUserId;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.dto.EmailShareDto;
import com.bees360.entity.dto.ProjectReportFileDto;
import com.bees360.entity.dto.ReportTypeDto;
import com.bees360.entity.dto.UrlDto;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.RealtimeElementTypeEnum;
import com.bees360.entity.vo.ProjectReportFileTinyVo;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectReportService;
import com.bees360.service.ProjectStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/*
 * This controller should
 */
@Slf4j
@RestController
@RequestMapping("/projects/{projectId:\\d+}/reports")
public class ReportController {

	@Inject
	private ProjectReportService projectReportService;

	@Inject
	private ProjectReportFileService projectReportFileService;

    @Autowired
    private ProjectStatusService projectStatusService;

	@Inject
	private MessageSource messageSource;

	@PutMapping("/{reportId}/status/submitted")
	@ResponseBody
	public ResponseJson requestReview(@PathVariable long projectId, @CurUserId long userId, @PathVariable String reportId) {
		ResponseJson json = new ResponseJson();
		try {
			projectReportFileService.submitReport(projectId, userId, reportId);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	@PutMapping("/{reportId}/status/approved")
	@ResponseBody
    @Deprecated
    public ResponseJson approveReports(@PathVariable long projectId, @CurUserId long userId, @PathVariable String reportId) {
        ResponseJson json = new ResponseJson();
        try {
            List<ProjectStatus> projectStatus = projectStatusService.listProjectStatus(projectId);
            projectStatus.sort(Comparator.comparingLong(ProjectStatus::getCreatedTime));
            ProjectStatus status = projectStatus.get(projectStatus.size() - 1);
            if (status.isDeleted() || status.getStatus() == NewProjectStatusEnum.PROJECT_CANCELED.getCode()) {
                String msgCode = MessageCode.REPORT_CANNOT_APPROVE;
                json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
            } else {
                projectReportFileService.approveReport(projectId, userId, reportId);
            }
        } catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	@PutMapping("{reportId}/status/disapproved")
	@ResponseBody
	public ResponseJson rejectReports(@PathVariable long projectId, @CurUserId long userId, @PathVariable String reportId) {
		ResponseJson json = new ResponseJson();
		try {
			projectReportFileService.disapprovedReport(projectId, userId, reportId);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}


	@PutMapping("/{reportId}/status/reprocess")
	public void reprocessReports(@PathVariable long projectId, @CurUserId long userId, @PathVariable String reportId)
		throws Exception {
		projectReportFileService.reprocessReport(projectId, userId, reportId);
	}

    // 该功能应废弃
    @Deprecated
	@PatchMapping("/{reportId}/read")
	@ResponseBody
	public ResponseJson readReport(@PathVariable long projectId, @CurUserId long userId, @PathVariable String reportId) {
		return new ResponseJson();
	}

	@PostMapping("/{reportId}/sharing")
	@ResponseBody
	public ResponseJson shareReportByEmail(@RequestBody EmailShareDto emailShare,
			@PathVariable long projectId, @CurUserId long userId, @PathVariable String reportId){
		ResponseJson json = new ResponseJson();
		try {
			projectReportService.shareReport(projectId, reportId, userId, emailShare.getEmails());
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	/**
	 * get report files
	 * @param projectId
	 * @param type
	 * @return
	 */
	@GetMapping("")
	public List<ProjectReportFileTinyVo> getReportFiles(@PathVariable long projectId, @CurUserId long userId,
		Integer type) throws Exception {
		return projectReportFileService.getReportFiles(projectId, userId, type);
	}

	@Validated
	@PostMapping("")
	public ProjectReportFileTinyVo createReportFile(@CurUserId long userId, @PathVariable long projectId,
		@RequestBody @Valid ProjectReportFileDto projectReportFileDto) throws Exception {
		ProjectReportFile report = projectReportFileService.createOrReplaceReportFile(userId, projectId, projectReportFileDto);
		return projectReportFileService.getReportFiles(projectId, userId, report.getReportType())
			.stream().findFirst().orElse(null);
	}

	@GetMapping("/{reportType:\\d+}/file")
	public boolean downloadReportImagesArchive(@PathVariable long projectId,
											@PathVariable int reportType) throws ServiceException {
		ProjectReportFileTinyVo reportFile = projectReportFileService.getReportFile(projectId, reportType);
        if (Objects.isNull(reportFile) || !RealtimeElementTypeEnum.hasImagePackage(reportType)) {
			throw new ServiceException(MessageCode.IMAGE_NOT_EXIST);
		}
		try {
			if (!projectReportFileService.isPackageExist(projectId, reportType)) {
				throw new ServiceException(MessageCode.IMAGES_NOT_GENERATE);
			}
		} catch (ServiceException e) {
			throw e;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new ServiceException(MessageCode.BAD_REQUEST);
		}
		return true;
	}

	/**
	 * update report file url
	 * @param projectId
	 * @param reportId
	 * @param urlDto
	 * @return
	 */
	@PatchMapping("/{reportId}/report")
	@ResponseBody
	public ResponseJson updateReport(@PathVariable long projectId,
			@PathVariable String reportId, @RequestBody UrlDto urlDto){
		ResponseJson json = new ResponseJson();
        int update = projectReportService.updateReportKey(projectId, reportId, urlDto.getUrl());
        json.setData(update);
		return json;
	}

	/**
	 * upload a report
	 *
	 * @param projectId project id
	 * @param userId user id
	 * @param reportFile report file message
	 */
	@PostMapping(value = "/report")
	public ProjectReportFile insertReport(@PathVariable long projectId, @CurUserId long userId,
							 @RequestBody ProjectReportFile reportFile) throws ServiceException {
		return projectReportService.insertReport(projectId, userId, reportFile);
	}

    /**
     * get report types
     * @param projectId
     * @return
     */
    @GetMapping("types")
    @ResponseBody
    public ResponseJson getReportTypes(@PathVariable long projectId){
        ResponseJson json = new ResponseJson();
        Map<String, List<ReportTypeDto>> resultMap = new HashMap<>(1);
        try {
            List<ReportTypeDto> reportTypes = projectReportFileService.getReportTypes(projectId);
            resultMap.put("reportTypes", reportTypes);
            json.setData(resultMap);
        } catch (ServiceException e) {
            String msgCode = e.getMsgCode();
            json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
        }
        return json;
    }

    @DeleteMapping("/{reportId}")
    public void deleteReport(@PathVariable long projectId, @CurUserId long userId, @PathVariable String reportId)
        throws Exception {
        projectReportFileService.deleteReport(projectId, userId, reportId);
    }
}
