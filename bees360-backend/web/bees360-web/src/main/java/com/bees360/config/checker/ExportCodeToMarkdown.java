package com.bees360.config.checker;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import com.bees360.base.MessageCode;
import com.bees360.base.code.ApiErrorType;
import com.bees360.base.code.MessageCodeDocument;
import com.bees360.util.MapList;
import com.bees360.util.PropertiesUtils;

import javassist.Modifier;
import lombok.Data;

/**
 * <AUTHOR>
 */
public class ExportCodeToMarkdown {

    private static final String MESSAGE_FILE = "messages_en.properties";

    public static void main(String[] args) {
        ExportCodeToMarkdown markdown = new ExportCodeToMarkdown();
        String table = markdown.generateMarkdown();
        System.out.println(table);
    }

    public String generateMarkdown() {
        StringBuilder sb = new StringBuilder();
        sb.append("## Error Type\n\n");
        sb.append(generateErrorTypeTable());
        sb.append("\n");

        sb.append("## Code Message\n\n");
        List<CodeDocument> docs = generateCodeDocs();
        MapList<ApiErrorType, CodeDocument> codeCategories = new MapList<>(CodeDocument::getType, docs);
        for (Map.Entry<ApiErrorType, List<CodeDocument>> table : codeCategories.entrySet()) {
            ApiErrorType type = table.getKey();
            sb.append("### " + type.name() + "\n\n");
            sb.append(type.description() + "\n\n");
            List<CodeDocument> docsForType = table.getValue();
            docsForType.sort((o1, o2) -> {
                int code1 = Integer.parseInt(o1.getCode());
                int code2 = Integer.parseInt(o2.getCode());
                return Integer.compare(code1, code2);
            });
            sb.append(generateCodeMessage(docsForType));
            sb.append("\n");
        }
        return sb.toString();
    }

    public String generateErrorTypeTable() {
        StringBuilder sb = new StringBuilder();
        sb.append("type | description\n");
        sb.append("--- | ---\n");
        for (ApiErrorType type : ApiErrorType.values()) {
            sb.append(type.name() + " | " + type.description() + "\n");
        }
        return sb.toString();
    }

    public String generateCodeMessage(List<CodeDocument> docs) {
        StringBuilder sb = new StringBuilder();
        sb.append("code | message\n");
        sb.append("--- | ---\n");
        for (CodeDocument doc : docs) {
            sb.append(doc.getCode() + " | " + doc.getMessage() + "\n");
        }
        return sb.toString();
    }

    public List<CodeDocument> generateCodeDocs() {
        List<CodeDocument> docs;
        try {
            docs = initMessageCodes(MessageCode.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        List<CodeMessage> codeMessages = new ArrayList<>();
        // 判断设置的message code变量是否与文件中的code一一匹配
        // 判断message是否以 “.” 结尾
        Properties properites = PropertiesUtils.getResourcesProperties(MESSAGE_FILE);
        for (CodeDocument codDoc : docs) {
            codDoc.setMessage(properites.getProperty(codDoc.getCode()));
        }
        return docs;
    }

    private List<CodeDocument> initMessageCodes(Class<?> clazz) throws Exception {
        Set<String> messageCodes = new HashSet<String>();
        List<CodeDocument> docs = new ArrayList<>();
        Field[] fields = clazz.getFields();

        for(Field field : fields){
            String modifier = Modifier.toString(field.getModifiers());
            if("public static final".equals(modifier)) {
                if(!"java.lang.String".equals(field.getType().getCanonicalName())) {
                    continue;
                }
                Object codeValue = field.get(null);
                if(codeValue == null) {
                    throw new Exception("Message code shouldn't be null.");
                }
                String code = codeValue.toString();
                if(messageCodes.contains(code)) {
                    throw new Exception("There is a duplicated message code: " + codeValue);
                }
                messageCodes.add(code);
                if(!field.isAnnotationPresent(MessageCodeDocument.class)) {
                    throw new Exception("The code '" + code + "' should be annotated by @MessageCodeDocument.");
                }
                MessageCodeDocument doc = field.getAnnotation(MessageCodeDocument.class);
                docs.add(toCodeDoc(code, doc));
            }
        }
        return docs;
    }

    private CodeDocument toCodeDoc(String code, MessageCodeDocument doc) {
        CodeDocument codDoc = new CodeDocument();
        codDoc.setCode(code);
        codDoc.setMoreInfo(doc.moreInfo());
        codDoc.setType(doc.type());
        return codDoc;
    }

    @Data
    static class CodeMessage {
        private String code;
        private String message;
    }

    @Data
    static class CodeDocument {
        private String code;
        private ApiErrorType type;
        private String moreInfo;
        private String message;
    }
}
