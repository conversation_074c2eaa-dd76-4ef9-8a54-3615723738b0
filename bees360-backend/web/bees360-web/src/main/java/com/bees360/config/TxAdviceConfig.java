package com.bees360.config;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.aspectj.lang.annotation.Aspect;
import org.springframework.aop.Advisor;
import org.springframework.aop.aspectj.AspectJExpressionPointcut;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource;
import org.springframework.transaction.interceptor.RollbackRuleAttribute;
import org.springframework.transaction.interceptor.RuleBasedTransactionAttribute;
import org.springframework.transaction.interceptor.TransactionAttribute;
import org.springframework.transaction.interceptor.TransactionInterceptor;

/**
 * 通过AOP设置Service层的全局事务管理。
 *
 * <AUTHOR>
 * @date 2019/12/10 20:19
 */
@Aspect
@Configuration
public class TxAdviceConfig {

    /**
     * 配置方法过期时间，单位为秒；默认-1，永不超时。为了防止一些慢操作，这里设置为60s
     */
    private final static int TX_METHOD_TIME_OUT = 60;

    private static final String POITCUT_EXPRESSION =
            "within(com.bees360.service.impl.*Impl.*) && execution(public * com.bees360.service.impl.*Impl.*(..))";

    /**
     * 事务管理拦截器。 有文章说，该配置可能会因为其他切面导致失效，可通过设置order解决，这里设置order值为200
     */
    @Order(200)
    @Bean
    public TransactionInterceptor txAdvice(PlatformTransactionManager platformTransactionManager) {

        RuleBasedTransactionAttribute readOnlyRule = readOnlyRule();
        RuleBasedTransactionAttribute requireRule = requireRule();

        Map<String, TransactionAttribute> nameMap = new HashMap<>();
        /* 增、删和改操作均需要可回滚事务 */
        nameMap.put("add*", requireRule);
        nameMap.put("save*", requireRule);
        nameMap.put("insert*", requireRule);
        nameMap.put("update*", requireRule);
        nameMap.put("delete*", requireRule);
        nameMap.put("remove*", requireRule);

        /* 查找操作均设置为只读 */
        nameMap.put("get*", readOnlyRule);
        nameMap.put("query*", readOnlyRule);
        nameMap.put("find*", readOnlyRule);
        nameMap.put("select*", readOnlyRule);
        nameMap.put("count*", readOnlyRule);

        NameMatchTransactionAttributeSource source = new NameMatchTransactionAttributeSource();
        // 配置事务管理规则，nameMap声明具备需要管理事务的方法名。
        // 实际会调用方法 addTransactionalMethod(String, TransactionAttribute)
        source.setNameMap(nameMap);
        TransactionInterceptor transactionInterceptor = new TransactionInterceptor(platformTransactionManager, source);
        return transactionInterceptor;
    }

    /**
     * 只读事物、不做更新删除等
     */
    private RuleBasedTransactionAttribute readOnlyRule() {
        RuleBasedTransactionAttribute readOnlyRule = new RuleBasedTransactionAttribute();
        /* 设置当前事务是否为只读事务，true为只读*/
        readOnlyRule.setReadOnly(true);
        readOnlyRule.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        return readOnlyRule;
    }

    /**
     * 抛出异常后执行切点回滚。
     * <p>
     * 只要抛出 {@code Exception}，均会触发回滚。
     * </p>
     */
    private RuleBasedTransactionAttribute requireRule() {
        RuleBasedTransactionAttribute requireRule = new RuleBasedTransactionAttribute();
        /* 抛出异常后执行切点回滚 */
        requireRule.setRollbackRules(Collections.singletonList(new RollbackRuleAttribute(Exception.class)));
        requireRule.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        /* 设置事务失效时间，超时则执行回滚事务 */
        // requireRule.setTimeout(TX_METHOD_TIME_OUT);

        return requireRule;
    }

    /**
     * 设置 txAdvice 的切面
     */
    @Bean
    public Advisor txAdviceAdvisor(TransactionInterceptor transactionInterceptor) {
        AspectJExpressionPointcut pointcut = new AspectJExpressionPointcut();
        pointcut.setExpression(POITCUT_EXPRESSION);
        return new DefaultPointcutAdvisor(pointcut, transactionInterceptor);
    }
}
