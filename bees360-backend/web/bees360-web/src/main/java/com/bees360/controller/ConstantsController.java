package com.bees360.controller;

import com.bees360.base.ResponseJson;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.dto.ReportPrefixDto;
import com.bees360.service.ConstantsService;
import com.bees360.service.ProjectReportService;
import com.bees360.util.CommonUtil;
import com.bees360.util.IpWorkerCN;
import com.bees360.util.UrlUtil;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 *
 * @date 2017/12/08 19:36
 */
@RestController
@RequestMapping("/constants")
public class ConstantsController {

	@Inject
	ConstantsService constantsService;

    @Inject
    private ProjectReportService projectReportService;

	// <EMAIL>: try to rewrite this API with a more clean way
	@GetMapping(value = "/options")
	public ResponseJson listOptions(Boolean companyTypes, Boolean projectTypes, Boolean projectStatuses,
			Boolean claimTypes, Boolean roles, Boolean shingleTypes, Boolean direction,
			Boolean inspectionCategory, Boolean positionType, Boolean damageSeverity, Boolean fileSourceType,
			Boolean filterImages, Boolean shingleLayer, Boolean objectsOnRoofing, Boolean componentTypes,
			Boolean segmentRootTypes, Boolean processStatuses, Boolean roofAges, Boolean imageType, Boolean newProjectStatuses,
                                    Boolean serviceTypes, Boolean mobileImageCategory)
		throws ServiceException{

		ResponseJson json = new ResponseJson();
		Set<String> options = new HashSet<>();
		if(CommonUtil.isTrue(imageType)){
			options.add(ConstantsService.IMAGE_TYPE);
		}
		if(CommonUtil.isTrue(companyTypes)){
			options.add(ConstantsService.COMPANY_TYPES_OPTIONS);
		}
		if(CommonUtil.isTrue(companyTypes)){
			options.add(ConstantsService.COMPANY_TYPES_OPTIONS);
		}
		if(CommonUtil.isTrue(projectTypes)){
			options.add(ConstantsService.PROJECT_TYPES_OPTIONS);
		}
		if(CommonUtil.isTrue(projectStatuses)){
			options.add(ConstantsService.PROJECT_STATUS_OPTIONS);
		}
		if(CommonUtil.isTrue(claimTypes)){
			options.add(ConstantsService.CLAIM_TYPE_OPTIONS);
		}
		if(CommonUtil.isTrue(roles)){
			options.add(ConstantsService.ROLES_OPTIONS);
		}
		if(CommonUtil.isTrue(shingleTypes)){
			options.add(ConstantsService.SHINGLE_TYPE_OPTIONS);
		}
		if(CommonUtil.isTrue(inspectionCategory)){
			options.add(ConstantsService.INSPECTION_CATEGORY_OPTIONS);
		}
		if(CommonUtil.isTrue(positionType)){
			options.add(ConstantsService.POSITION_TYPE_OPTIONS);
		}
		if(CommonUtil.isTrue(damageSeverity)){
			options.add(ConstantsService.DAMAGE_SEVERITY_OPTIONS);
		}
		if(CommonUtil.isTrue(fileSourceType)){
			options.add(ConstantsService.FILE_SOURCE_TYPE_OPTIONS);
		}
		if(CommonUtil.isTrue(direction)){
			options.add(ConstantsService.DIRECTION_OPTIONS);
		}
		if(CommonUtil.isTrue(filterImages)){
			options.add(ConstantsService.FILTER_IMAGES_OPTIONS);
		}
		if(CommonUtil.isTrue(shingleLayer)){
			options.add(ConstantsService.SHINGLE_LAYER_OPTIONS);
		}
		if(CommonUtil.isTrue(objectsOnRoofing)){
			options.add(ConstantsService.OBJECTS_ON_ROOFING_OPTIONS);
		}
		if(CommonUtil.isTrue(componentTypes)){
			options.add(ConstantsService.PROJECT_COMPONENT_TYPE_OPTIONS);
		}
		if(CommonUtil.isTrue(segmentRootTypes)){
			options.add(ConstantsService.SEGMENT_ROOT_OPTIONS);
		}
		if(CommonUtil.isTrue(processStatuses)){
			options.add(ConstantsService.PROCESS_STATUS);
		}
		if(CommonUtil.isTrue(roofAges)){
			options.add(ConstantsService.ROOF_AGES);
		}
        if(CommonUtil.isTrue(serviceTypes)){
            options.add(ConstantsService.SERVICE_TYPES);
        }
        if(CommonUtil.isTrue(mobileImageCategory)){
            options.add(ConstantsService.MOBILE_IMAGE_CATEGORY);
        }
		addIfTrue(options, ConstantsService.NEW_PROJECT_STATUSES, newProjectStatuses);
		Map<String, Object> optionsMap = constantsService.getStaticOptionsMap(options);
		json.setData(optionsMap);
		return json;
	}

	private void addIfTrue(Set<String> options, String value, Boolean b) {
		if(CommonUtil.isTrue(b)) {
			options.add(value);
		}
	}

	/**
	 *
	 * @param request
	 * @return
	 */
	@GetMapping(value = "/ip")
	public ResponseJson checkIp(HttpServletRequest request) {
		ResponseJson json = new ResponseJson();
		json.setData(IpWorkerCN.getInstance().isCNIp(UrlUtil.getIpAddress(request)));
		return json;
	}

    @GetMapping("/report-prefix")
    public ReportPrefixDto getReportFilePrefix() {
        return projectReportService.getReportFilePrefix();
    }
}
