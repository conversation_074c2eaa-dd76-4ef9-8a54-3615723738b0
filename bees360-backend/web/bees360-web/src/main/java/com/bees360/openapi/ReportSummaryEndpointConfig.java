package com.bees360.openapi;

import com.bees360.openapi.config.ObjectMapperCustomizedConfig;
import com.bees360.service.openapi.OpenReportService;
import com.bees360.service.util.AWSLambdaSummaryConverterConfig;
import com.bees360.web.openapi.OpenApiReportManagerConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.function.BinaryOperator;

@Configuration
public class ReportSummaryEndpointConfig {

    @Import({
        OpenApiReportManagerConfig.class,
        OpenReportSummaryEndpoint.class,
        ObjectMapperCustomizedConfig.class,
    })
    @Configuration
    @ConditionalOnProperty(prefix = "app.web.openapi.summary", name = "type", havingValue = "with-openapi-report-provider")
    static class OpenReportSummaryEndpointConfig {}

    @Import({
        AWSLambdaSummaryConverterConfig.class,
        OpenApiReportManagerConfig.class,
    })
    @Configuration
    @ConditionalOnProperty(prefix = "app.web.openapi.summary", name = "type", havingValue = "original", matchIfMissing = true)
    static class OldOpenReportSummaryEndpointConfig {

        @Bean
        OldOpenReportSummaryEndpoint oldOpenReportSummaryEndpoint(
            @Qualifier("lambdaReportSummaryConverter") BinaryOperator<String> lambdaReportSummaryConverter,
            OpenReportService openReportService,
            ObjectMapper objectMapper) {
            return new OldOpenReportSummaryEndpoint(lambdaReportSummaryConverter, openReportService, objectMapper);
        }
    }
}
