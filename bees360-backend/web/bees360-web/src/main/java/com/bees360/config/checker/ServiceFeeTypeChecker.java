package com.bees360.config.checker;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;

import com.bees360.config.checker.BasicChecker;
import com.bees360.entity.ServiceFeeType;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.ServiceFeeTypeEnum;
import com.bees360.mapper.ServiceFeeTypeMapper;
import org.springframework.stereotype.Component;

@Component
public class ServiceFeeTypeChecker implements BasicChecker {

	@Inject
	private ServiceFeeTypeMapper serviceFeeTypeMapper;

	@Override
	public void check() {

		List<ServiceFeeType> types = serviceFeeTypeMapper.list();

		Set<String> pairs = new HashSet<String>();
		for(ServiceFeeType type: types) {
			pairs.add(createPair(type));
		}
		for(ServiceFeeTypeEnum type: ServiceFeeTypeEnum.values()) {
			for(ReportTypeEnum report: type.getReportTypes()) {
				String pair = createPair(type, report);
				if(!pairs.contains(pair)) {
					throw new RuntimeException("The serviceFeeType Pair (" + pair + ") "
							+ "is defined in ServiceFeeTypeEnum but undefined in database");
				}
			}
		}
	}

	public String createPair(ServiceFeeType type) {
		return type.getServiceFeeType() + ":" + type.getReportType();
	}

	public String createPair(ServiceFeeTypeEnum type, ReportTypeEnum reportType) {
		return type.getServiceFeeTypeId() + ":" + reportType.getCode();
	}
}
