package com.bees360.config.support;

import org.springframework.lang.Nullable;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.ObjectUtils;
import org.springframework.util.PathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.cors.CorsConfiguration;

/**
 * 利用 {@code AntPathMarcher} 匹配origin
 *
 * <AUTHOR>
 * @date 2019/12/11 09:19
 */
public class AntPathMatcherCorsConfiguration extends CorsConfiguration {

    private PathMatcher pathMatcher = new AntPathMatcher();

    @Nullable
    @Override
    public String checkOrigin(@Nullable String requestOrigin) {
        if (!StringUtils.hasText(requestOrigin)) {
            return null;
        }
        if (ObjectUtils.isEmpty(this.getAllowedOrigins())) {
            return null;
        }

        if (this.getAllowedOrigins().contains(ALL)) {
            if (!Boolean.TRUE.equals(this.getAllowCredentials())) {
                // allow-credentials = true 和 origin = * 不能同时出现
                return ALL;
            } else {
                return requestOrigin;
            }
        }

        String requestOriginLowerCase = requestOrigin.toLowerCase();
        for (String allowedOrigin : this.getAllowedOrigins()) {
            if (pathMatcher.match(allowedOrigin.toLowerCase(), requestOriginLowerCase)) {
                return requestOrigin;
            }
        }
        return null;
    }
}
