package com.bees360.web.security.expression.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.bees360.util.Iterables;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;

import com.bees360.internal.ai.exchange.ai2client.DashBoardProto;
import com.bees360.internal.ai.grpc.api.web2ai.Project;
import com.bees360.project.participant.ProjectParticipantProvider;
import com.bees360.service.ai.AiProjectService;
import com.bees360.web.security.util.AuthorityUtil;
import com.bees360.web.security.expression.ProjectSecurity;
import com.bees360.web.security.SecurityAttributeUtil;
import com.bees360.web.security.expression.UserSecurity;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Member;
import com.bees360.entity.User;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.service.MemberService;
import com.bees360.service.ProjectService;
import com.bees360.service.UserService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Yang
 */
@Slf4j
@Component("projectSecurity")
public class ProjectSecurityImpl implements ProjectSecurity {

    @Inject
    private MemberService memberService;
    @Inject
    private UserService userService;
    @Inject
    private ProjectService projectService;
    @Inject
    private AiProjectService aiProjectService;

    @Autowired
    private UserSecurity userSecurity;

    @Autowired
    private ProjectParticipantProvider participantProvider;

    private interface MemberJudgeType {
        int WITH_ANY = 1;
        int WITH_ALL = 2;
        int WITH_EQUALS = 3;
    }

    @Override
    public boolean isProjectAccessible(HttpServletRequest request, Authentication authentication, long projectId) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_ACCESSIBLE,
            () -> judgeAccessibleProject(authentication, projectId));
    }

    private boolean judgeAccessibleProject(Authentication authentication, long projectId) {
        boolean isAdmin = userSecurity.hasRole(authentication, RoleEnum.ADMIN.name());
        return isAdmin || isManagedBy(authentication, projectId) || isMemberIn(authentication, projectId);
    }

    @Override
    public boolean isMemberIn(HttpServletRequest request, Authentication authentication, long projectId) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_MEMBER_IN,
            () -> judgeMemberIn(authentication, projectId));
    }

    private boolean judgeMemberIn(Authentication authentication, long projectId) {
        Long userId = SecurityAttributeUtil.getUserId(authentication);
        if (userId == null) {
            return false;
        }
        try {
            List<Member> asMemberInProject =
                    memberService.listUserRolesInProject(projectId, userId);
            return !asMemberInProject.isEmpty()
                    || !getMemberInAiProjectIfRequestFromAi(userId, projectId).isEmpty()
                    || isParticipant(userId, projectId);
        } catch (ServiceException e) {
            return false;
        }
    }

    private boolean isParticipant(long userId, long projectId) {
        return Iterables.toStream(participantProvider.getParticipant(String.valueOf(projectId)))
                .map(com.bees360.user.User::getId)
                .anyMatch(id -> Objects.equals(id, String.valueOf(userId)));
    }

    @Override
    public boolean isMemberWith(HttpServletRequest request, Authentication authentication, long projectId,
        String... roles) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_MEMBER_WITH,
            () -> judgeMemberWith(authentication, projectId, MemberJudgeType.WITH_ANY, roles));
    }

    @Override
    public boolean isMemberWithAll(HttpServletRequest request, Authentication authentication, long projectId,
                                   String... roles) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_MEMBER_CONTAINS,
            () -> judgeMemberWith(authentication, projectId, MemberJudgeType.WITH_ALL, roles));
    }

    @Override
    public boolean isMemberEquals(HttpServletRequest request, Authentication authentication, long projectId,
                                  String... roles) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_MEMBER_CONTAINS,
            () -> judgeMemberWith(authentication, projectId, MemberJudgeType.WITH_EQUALS, roles));
    }

    public boolean judgeMemberWith(Authentication authentication, long projectId, int memberJudgeType, String... roles) {
        if (roles.length == 0) {
            return false;
        }
        Long userId = SecurityAttributeUtil.getUserId(authentication);
        if (userId == null) {
            return false;
        }
        List<Member> asMemberInProject = null;
        try {
            asMemberInProject = memberService.listUserRolesInProject(projectId, userId);
        } catch (ServiceException e) {
            throw new IllegalStateException(e);
        }
        Set<String> rolesUserRealWith = asMemberInProject.stream().
            map(m -> AuthorityUtil.toRoleInSecurity(RoleEnum.getEnum(m.getRole()))).collect(Collectors.toSet());
        return hasRoles(memberJudgeType, rolesUserRealWith, roles)
            || hasRoles(memberJudgeType, getMemberInAiProjectIfRequestFromAi(userId, projectId), roles);
    }

    private boolean hasRoles(int memberJudgeType, Set<String> rolesUserRealWith, String... roles) {
        switch (memberJudgeType) {
            case MemberJudgeType.WITH_ANY: {
                return Arrays.stream(roles).anyMatch(r -> rolesUserRealWith.contains(SecurityAttributeUtil.toRoleInSecurity(r)));
            }
            case MemberJudgeType.WITH_ALL: {
                return Arrays.stream(roles).allMatch(r -> rolesUserRealWith.contains(SecurityAttributeUtil.toRoleInSecurity(r)));
            }
            case MemberJudgeType.WITH_EQUALS: {
                return roles.length == rolesUserRealWith.size()
                    && Arrays.stream(roles).allMatch(r -> rolesUserRealWith.contains(SecurityAttributeUtil.toRoleInSecurity(r)));
            }
            default: {
                throw new IllegalStateException("MemberJudgeType `" + memberJudgeType + "` not supported.");
            }
        }
    }

    private Set<String> getMemberInAiProjectIfRequestFromAi(long userId, long projectId) {
        Project.ProjectEsModel projectEsModel = aiProjectService.findByProjectId(projectId);
        if (projectEsModel == null) {
            return Sets.newHashSet();
        }
        return projectEsModel.getMembersList().stream().filter(m -> StringUtils.equals(m.getId(), String.valueOf(userId)))
            .map(DashBoardProto.MemberInfo::getAuth).collect(Collectors.toSet());
    }

    @Override
    public boolean isManagedBy(HttpServletRequest request, Authentication authentication, long projectId) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_MANAGED_BY,
            () -> judgeManagedBy(authentication, projectId));
    }

    public boolean judgeManagedBy(Authentication authentication, long projectId) {
        Long userId = SecurityAttributeUtil.getUserId(authentication);
        if (userId == null) {
            return false;
        }
        try {
            User user = userService.getUserById(userId);
            if (user.getCompanyId() == null || !user.hasRole(RoleEnum.COMPANY_ADMIN)) {
                return false;
            }
            return projectService.isProjectManagedBy(projectId, user.getCompanyId());
        } catch (ResourceNotFoundException e) {
            return false;
        } catch (ServiceException e) {
            throw new IllegalStateException(e);
        }
    }
}
