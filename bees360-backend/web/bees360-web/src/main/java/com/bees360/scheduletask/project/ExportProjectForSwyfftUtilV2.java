package com.bees360.scheduletask.project;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.excel.EasyExcel;

import org.springframework.util.ObjectUtils;
import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;
import com.bees360.entity.dto.KeyValue;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

/**
 * <AUTHOR> Yang
 */
public class ExportProjectForSwyfftUtilV2 implements ExportProjectForSwyfftUtil {

    private final static String SHEET_NAME = "To Swyfft";

    private Gson gson = createGsonForV2();

    @Override
    public ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData fetchExcelData(List<Project> projects,
        List<BsExportData> bsExportDataList) {
        if (bsExportDataList.isEmpty()) {
            return new ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData(null, 0);
        }

        try {
            return exportExcelV2(bsExportDataList);
        } catch (IOException e) {
            String message = "Fail to export data to excel".formatted();
            throw new RuntimeException(message, e);
        }
    }

    private Gson createGsonForV2() {
        return new Gson();
    }

    private List<String> fetchProjectKeyOrders(BsExportData data) {

        Type listType = new TypeToken<List<KeyValue>>() {}.getType();
        List<KeyValue> keyValues = gson.fromJson(data.getDataLog(), listType);
        return keyValues.stream().map(kv -> kv.getKey()).collect(Collectors.toList());
    }

    private ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData exportExcelV2(List<BsExportData> dataList)
            throws IOException {
        List<List<String>> excelRows = new ArrayList<>();
        List<String> keyOrders = fetchProjectKeyOrders(dataList);
        Type listType = new TypeToken<List<KeyValue>>() {}.getType();

        excelRows.add(keyOrders);
        for (BsExportData data : dataList) {
            if (ObjectUtils.isEmpty(data.getDataLog())) {
                continue;
            }
            List<KeyValue> keyValues = gson.fromJson(data.getDataLog(), listType);
            Map<String, String> map = keyValueToMap(keyValues);
            List<String> row =
                keyOrders.stream().map(o -> map.containsKey(o) ? map.get(o) : "").collect(Collectors.toList());
            excelRows.add(row);
        }
        try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            EasyExcel.write(output).sheet(SHEET_NAME).doWrite(excelRows);
            // 第一行是title
            int dataRow = excelRows.size() - 1;
            return new ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData(output.toByteArray(), dataRow);
        }
    }

    private List<String> fetchProjectKeyOrders(List<BsExportData> dataList) {
        List<BsExportData> orderDataList =
            dataList.stream().sorted(Comparator.comparing(BsExportData::getId).reversed()).collect(Collectors.toList());

        List<String> orders = new ArrayList<>();
        for (BsExportData data : orderDataList) {
            // 找到第一个含有顺序的文件
            orders = fetchProjectKeyOrders(data);
            if (!orders.isEmpty()) {
                return orders;
            }
        }
        return orders;
    }

    private Map<String, String> keyValueToMap(List<KeyValue> keyValues) {
        Map<String, String> map = new HashMap<>(keyValues.size());
        for (KeyValue keyValue : keyValues) {
            map.put(keyValue.getKey(), keyValue.getValue());
        }
        return map;
    }

}
