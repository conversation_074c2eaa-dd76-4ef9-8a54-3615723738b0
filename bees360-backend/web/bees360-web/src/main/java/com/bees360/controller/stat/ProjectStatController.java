package com.bees360.controller.stat;

import com.bees360.base.exception.ServiceException;
import com.bees360.config.support.CurUserId;
import com.bees360.entity.stat.search.ListSearchOption;
import com.bees360.entity.stat.search.StatFullSearchOption;
import com.bees360.entity.stat.search.StatSearchOption;
import com.bees360.entity.stat.vo.StatProjectCardVo;
import com.bees360.entity.stat.vo.StatProjectVo;
import com.bees360.entity.stat.search.ChartSearchOption;
import com.bees360.entity.stat.vo.StatComplexVo;
import com.bees360.entity.stat.vo.list.PagedResultVo;
import com.bees360.service.stat.ProjectStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.bees360.entity.stat.search.StatFullSearchOption.initFullSearchOptions;

@RestController
@RequestMapping("/project/stat")
public class ProjectStatController {

    @Autowired
    private ProjectStatService projectStatService;


    @GetMapping("/card")
    public StatProjectCardVo projectCard(@CurUserId long userId, @Validated StatFullSearchOption searchOption) throws ServiceException {
        initFullSearchOptions(searchOption);
        return projectStatService.statProjectCard(userId, searchOption);

    }

    @GetMapping("/chart")
    public StatProjectVo projectChart(@CurUserId long userId, @Validated StatFullSearchOption searchOption) throws ServiceException{
        initFullSearchOptions(searchOption);
        return projectStatService.statProjectChart(userId, searchOption);
    }


    @GetMapping("/list")
    public PagedResultVo projectList(@CurUserId long userId, @Validated StatFullSearchOption searchOption) throws ServiceException{
        initFullSearchOptions(searchOption);
        return projectStatService.statProjectList(userId, searchOption);
    }

    @GetMapping("/complex")
    public StatComplexVo stateInfo(
            @CurUserId long userId, @Validated StatFullSearchOption searchOption)
            throws ServiceException {
        initFullSearchOptions(searchOption);
        return projectStatService.statProjectComplex(userId, searchOption);
    }
}
