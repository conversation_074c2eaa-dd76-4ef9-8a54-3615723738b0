package com.bees360.controller;

import static com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE;

import com.bees360.address.AddressHiveLocationProvider;
import com.bees360.api.UnauthenticatedException;
import com.bees360.base.MessageCode;
import com.bees360.base.ResponseJson;
import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.config.support.ControllerExceptionHandler;
import com.bees360.config.support.CurUserId;
import com.bees360.entity.Company;
import com.bees360.entity.EventHistory;
import com.bees360.entity.Project;
import com.bees360.entity.User;
import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.dto.MemberSchedule;
import com.bees360.entity.dto.MemberSchedule.MemberScheduleItem;
import com.bees360.entity.dto.ProjectBatchDto;
import com.bees360.entity.dto.ProjectCompositeStatusVo;
import com.bees360.entity.dto.ProjectDto;
import com.bees360.entity.dto.ProjectPaymentStatusDto;
import com.bees360.entity.dto.ProjectScoreDto;
import com.bees360.entity.dto.ProjectSearchOption;
import com.bees360.entity.enums.CompanyTypeEnum;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.PayStatusEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.ProjectSyncPointEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.normalize.ProjectDataNormalizer;
import com.bees360.entity.vo.Address;
import com.bees360.entity.vo.InvoiceFileVo;
import com.bees360.entity.vo.ProjectAbstractVo;
import com.bees360.entity.vo.ProjectDetailVo;
import com.bees360.entity.vo.ProjectGenerationInfoVo;
import com.bees360.entity.vo.ProjectInspectionInfoVo;
import com.bees360.entity.vo.ProjectInsuredInfoVo;
import com.bees360.entity.vo.ProjectLatLngVo;
import com.bees360.entity.vo.ProjectPageResultVo;
import com.bees360.entity.vo.ProjectPartialWrapper;
import com.bees360.entity.vo.ProjectServiceTypeVo;
import com.bees360.entity.vo.ProjectTinyUserVoCandidates;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.entity.vo.request.ProjectReworkReasonParam;
import com.bees360.entity.vo.request.ProjectServiceTypeParam;
import com.bees360.entity.vo.request.ProjectServiceTypeReasonParam;
import com.bees360.entity.vo.request.SendSmsPilotParam;
import com.bees360.project.group.ProjectGroupManager;
import com.bees360.project.state.ProjectStateChangeReasonManager;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.resource.ResourceUrlProvider;
import com.bees360.service.ContextProvider;
import com.bees360.service.ProjectImageArchiveService;
import com.bees360.service.ProjectScoreService;
import com.bees360.service.ProjectService;
import com.bees360.service.UserService;
import com.bees360.service.beespilot.BeesPilotStatusService;
import com.bees360.service.file.ResourceKeyUtil;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.ConstantUtil;
import com.bees360.util.Iterables;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

/**
 * <AUTHOR>
 * @date 2017.11.1
 */
@Validated
@Slf4j
@RestController
@RequestMapping("/projects")
public class ProjectController {

	@Inject
	private MessageSource messageSource;

	@Inject
	private ProjectService projectService;

	@Inject
	private UserService userService;

	@Inject
	ContextProvider springSecurityContextProvider;

	@Inject
	private ProjectImageArchiveService asyncProjectImageArchiveService;

	@Autowired
    private BeesPilotStatusService beesPilotStatusService;

    @Autowired
    private ProjectScoreService projectScoreService;

    @Autowired
    private ResourceKeyUtil resourceKeyUtil;

    @Autowired
    private ControllerExceptionHandler controllerExceptionHandler;

    @Autowired private ResourceUrlProvider resourceUrlProvider;

    @Autowired
    private AddressHiveLocationProvider addressHiveLocationProvider;
    @Autowired
    private ProjectStateManager projectStateManager;
    @Autowired
    private ProjectStateChangeReasonManager projectStateChangeReasonManager;
    @Autowired
    private Bees360FeatureSwitch bees360FeatureSwitch;

    @Autowired private ProjectGroupManager projectGroupManager;

    @GetMapping("")
	public ProjectPageResultVo getAbstractListWithSearch(@AuthenticationPrincipal com.bees360.user.User user, ProjectSearchOption searchOption)
		throws Exception {
        Long userId = Long.parseLong(user.getId());

	    searchOption.setPageSize(searchOption.getPageSize(), 1000);
		return projectService.pageTinyProjectsWithSearch(userId, searchOption);
	}

	@PostMapping("export")
	public ProjectPageResultVo exportProject(@CurUserId long userId,
		@RequestBody ProjectSearchOption searchOption) throws Exception {

        searchOption.setPageSize(searchOption.getPageSize(), 1000);

		return projectService.getExportProjectList(userId, searchOption);
	}

    @GetMapping("/{projectId:\\d+}/hive-location")
    public ResponseJson getHiveLocation(@PathVariable long projectId) {
        var project = projectService.getById(projectId);
        if (project.getAddressId() != null) {
            return new ResponseJson(
                    addressHiveLocationProvider.getHiveLocationDistance(project.getAddressId()));
        }
        return new ResponseJson();
    }

	@GetMapping("/{projectId:\\d+}/GPI")
	public ResponseJson getGeneralPropertyInfo(@PathVariable long projectId){
		ResponseJson json = new ResponseJson();
		try {
			Map<String, Object> map = projectService.getGeneralPropertyInfo(projectId);
			json.setData(map);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	/**
	 * 考虑到之前的接口没有增加校验，暂时不清楚对客户端的影响，因而暂时不做数据的校验
	 */
    @PostMapping("")
    public long createProject(@CurUserId long userId, @RequestBody @Valid ProjectDto projectDto) throws Exception {
        User user = userService.getUserById(userId);
        //if the user's role is just a PILOT, then the user should not be allowed to create a project.
        if (user.hasRole(RoleEnum.PILOT) && user.listRoles().size() == 1) {
            throw new UnauthenticatedException("Pilot is not allowed to create project.");
        }
        // only user of admin can set this field, other the default value false is set
        if (!user.hasRole(RoleEnum.ADMIN)) {
            projectDto.setTestFlag(false);
        }
        Optional.ofNullable(projectDto).ifPresent(ProjectDataNormalizer::normalize);
        projectDto.setCreationChannel(CreationChannelType.WEB.name());
        Project project = projectService.createProject(userId, projectDto);

        return project.getProjectId();
    }

    /**
     * 考虑到之前的接口没有增加校验，暂时不清楚对客户端的影响，因而暂时不做数据的校验
     */
    @PostMapping("/batch")
    public List<ProjectGenerationInfoVo<ResponseJson>> createProject(@CurUserId long userId,
        HttpServletResponse response, HttpServletRequest request, WebRequest webRequest,
        @RequestBody @Valid ProjectBatchDto projectBatch) throws Exception {

        projectBatch.getProjects().forEach(ProjectDataNormalizer::normalize);
        Map<Integer, Exception> resMap = projectService.createProjectBatch(userId, projectBatch);

        return resMap.entrySet().stream().map(e -> {
            Integer idx = e.getKey();
            var apiError = controllerExceptionHandler.handleException(response, request, webRequest, e.getValue());
            ResponseJson errorInfo = new ResponseJson(apiError.getCode(), apiError.getMessage(), apiError.getMetadata());
            return new ProjectGenerationInfoVo<>(idx, errorInfo);
        }).collect(Collectors.toList());
    }

    @DeleteMapping("/{projectId:\\d+}")
    public void deleteProject(@CurUserId long userId, @PathVariable long projectId)
            throws Exception {
        if (bees360FeatureSwitch.isEnableOpenCloseWrite()) {
            var testOrDemoCase = "TEST OR DEMO CASE";
            var projectStateChangeReason = Iterables.toStream(
                projectStateChangeReasonManager.findByQuery(List.of(), List.of(testOrDemoCase), List.of())
            ).findFirst().orElse(null);
            if (Objects.isNull(projectStateChangeReason)) {
                throw new IllegalStateException("No change reason with key %s found".formatted(testOrDemoCase));
            }
            projectStateManager.changeProjectState(
                String.valueOf(projectId),
                PROJECT_CLOSE,
                projectStateChangeReason.getId(),
                String.valueOf(userId),
                null,
                null);
        }
        projectService.deleteProject(projectId, userId);
    }

	@GetMapping("/{projectId:\\d+}/abstract")
	public ResponseJson getProjectDetail(@PathVariable Long projectId){
//		<EMAIL>: please fixed the bug that projectId is not a number
		ResponseJson json = new ResponseJson();

		ProjectAbstractVo projectAbstract = null;
		try {
			projectAbstract = projectService.getAbstractById(projectId);
			json.setData(projectAbstract);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	@PutMapping("/{projectId:\\d+}/GPI")
	public ResponseJson modifyGeneralPropertyInformation(@PathVariable long projectId, @RequestBody Project project){
		ResponseJson json = new ResponseJson();
//		long userId = (long)session.getAttribute("userId");
		long userId = springSecurityContextProvider.getUserIdFromContext();
		project.setProjectId(projectId);
		try {
			projectService.modifyGeneralPropertyInformation(project, userId);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

    @GetMapping("/{projectId:\\d+}/collaboration/candidates")
    public ProjectTinyUserVoCandidates listCollaborationCandidate(@PathVariable long projectId)
            throws ServiceException {
        ResponseJson json = new ResponseJson();

        long userId = springSecurityContextProvider.getUserIdFromContext();

        return projectService.listCollaborationCandidates(projectId, userId);
    }

    @GetMapping("/collaboration/candidates")
    public ResponseJson listCandidate(@CurUserId long userId) {
        ResponseJson json = new ResponseJson();

        try {
            ProjectTinyUserVoCandidates vo = projectService.listCollaborationCandidates(userId, true);
            json.setData(vo);
        } catch (ServiceException e) {
            String msgCode = e.getMsgCode();
            json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
        }
        return json;
    }

	@GetMapping("/{projectId:\\d+}/CI")
	public ResponseJson getCliamInformation(@PathVariable long projectId){
		ResponseJson json = new ResponseJson();

		try {
			Map<String, Object> ciMap = projectService.getCliamInformation(projectId);
			json.setData(ciMap);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	@PutMapping("/{projectId:\\d+}/CI")
	public ResponseJson modifyCliamInformation(@PathVariable long projectId, @RequestBody Project project){
		ResponseJson json = new ResponseJson();
		//long userId = (long)session.getAttribute("userId");
		long userId = springSecurityContextProvider.getUserIdFromContext();
		Map<String, Object> ciMap = new HashMap<String, Object>();
		ciMap.put("claimNumber", project.getClaimNumber());
		ciMap.put("policyNumber", project.getPolicyNumber());
		ciMap.put("claimType", project.getClaimType());
		ciMap.put("damageEventTime", project.getDamageEventTime());
		ciMap.put("claimNote", project.getClaimNote());
		try {
			projectService.modifyCliamInformation(projectId, userId, ciMap);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	@GetMapping("/{projectId:\\d+}/members")
	public List<UserTinyVo> listMemberInProject(@PathVariable long projectId) throws Exception {
		long userId = springSecurityContextProvider.getUserIdFromContext();
		return userService.listMemberInProject(userId, projectId);
	}

	@PutMapping("/{projectId:\\d+}/members")
	public void changeMemberToProject(@PathVariable long projectId,
			@RequestBody MemberSchedule members) throws Exception {
        var underWriterChanged =
                members.getMembers().stream()
                        .anyMatch(
                                m ->
                                        Objects.equals(
                                                RoleEnum.UNDERWRITER.getRoleId(), m.getRoleId()));
        if (underWriterChanged) {
            validateProjectNotBundle(projectId);
        }

		long userId = springSecurityContextProvider.getUserIdFromContext();
		projectService.changeMembers(userId, projectId, members);
	}

    @PutMapping("/-/member/{userId:\\d+}/{roleId:\\d+}")
    public void changeMemberToMultiProject(
            @CurUserId long curUserId,
            @PathVariable long userId,
            @PathVariable int roleId,
            @RequestBody List<Long> projectId) throws Exception {
        var underWriterChanged = Objects.equals(RoleEnum.UNDERWRITER.getRoleId(), roleId);
        if (underWriterChanged) {
            projectId.forEach(this::validateProjectNotBundle);
        }
        projectService.changeMembersToMultiProject(curUserId, userId, roleId, projectId);
    }

    @PutMapping("/{projectId:\\d+}/member")
	public void changeMemberToProject(@PathVariable long projectId,
			@RequestBody MemberScheduleItem member) throws Exception {
        var underWriterChanged = Objects.equals(RoleEnum.UNDERWRITER.getRoleId(), member.getRoleId());
        if (underWriterChanged) {
            validateProjectNotBundle(projectId);
        }
		long userId = springSecurityContextProvider.getUserIdFromContext();
		projectService.changeMember(userId, projectId, member);
	}

	@PostMapping("/{projectId:\\d+}/members/visitors")
	public ResponseJson inviteMember(@PathVariable long projectId, @RequestBody Map<String, String> emailMap){
		ResponseJson json = new ResponseJson();
		String email = emailMap.get("email");
		long userId = springSecurityContextProvider.getUserIdFromContext();
		UserTinyVo visitor = null;
		try {
			visitor = projectService.inviteVisitor(projectId, userId, email);
			json.setData(visitor);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	@DeleteMapping("/{projectId:\\d+}/members/visitors/{visitor}")
	public ResponseJson inviteMember(@PathVariable long projectId, @PathVariable long visitor){
		ResponseJson json = new ResponseJson();
		long userId = springSecurityContextProvider.getUserIdFromContext();
		try {
			projectService.deleteVisitor(projectId, userId, visitor);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	@GetMapping("/relation/companies")
	public List<Company> listRelativeCompanies(@CurUserId long userId, Integer companyType) throws Exception {
		CompanyTypeEnum type = null;
		if(companyType != null) {
			type = CompanyTypeEnum.getEnum(companyType);
		}
		return projectService.listCompanyDict(userId, type);
	}

	/**
	 * <AUTHOR>
	 * @return
	 * @throws ServiceException
	 */
	@GetMapping("/insurCompanyByUserId")
	public List<Company> listInsurCompanyByUserId(@CurUserId long userId) throws Exception {
		return projectService.listComaniesInProjectsUserTakePartIn(userId, CompanyTypeEnum.INSURANCE_COMPANY);
	}

	@PostMapping("/{projectId:\\d+}/images/archive")
	public ResponseJson archiveImages(@PathVariable long projectId) throws Exception{
		asyncProjectImageArchiveService.archiveImages(projectId);
		return new ResponseJson();
	}

	@GetMapping("/{projectId:\\d+}/modules")
	public ResponseJson listUserAccessableModules(@PathVariable long projectId) {
		ResponseJson json = new ResponseJson();
		long userId = springSecurityContextProvider.getUserIdFromContext();
		Set<String> modules;
		try {
			modules = projectService.listUserAccessableModules(projectId, userId);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			return new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("modules", modules);
		json.setData(resultMap);
		return json;
	}

	@GetMapping("/creators")
	public ResponseJson listAllProjectsCreators(){
		ResponseJson json = new ResponseJson();
		long userId = springSecurityContextProvider.getUserIdFromContext();
		List<UserTinyVo> creators = null;
		try {
			creators = projectService.listProjectsCreators(userId);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			return new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		List<Map<String, Object>> creatorInfos = new ArrayList<Map<String, Object>>();
		for(UserTinyVo user: creators) {
			creatorInfos.add(user.part());
		}
		json.setData(creatorInfos);
		return json;
	}

	/**
	 * pilot checked in
	 * @param projectId
	 * @return
	 */
	@PostMapping("/{projectId:\\d+}/statuses/pilotcheckedin")
	public ResponseJson confirmCheckedIn(@PathVariable long projectId, @RequestBody Map<String, Integer> formMap) {
		int type = formMap.get("type");
		ResponseJson json = new ResponseJson();
		long userId = springSecurityContextProvider.getUserIdFromContext();
		try {
			projectService.insertPilotCheckedStatus(projectId, userId, type, ProjectStatusEnum.PILOT_CHECKED_IN);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	@PostMapping("/{projectId:\\d+}/statuses/pilotcheckedout")
	public ResponseJson confirmCheckedOut(@PathVariable long projectId) {
		ResponseJson json = new ResponseJson();
		long userId = springSecurityContextProvider.getUserIdFromContext();
		try {
			projectService.insertPilotCheckedStatus(projectId, userId, ConstantUtil.PILOT_CHECKED_OUT_TYPE, ProjectStatusEnum.PILOT_CHECKED_OUT);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	/**
	 * pilot checked in
	 * @param projectId
	 * @return
	 */
	@PostMapping("/{projectId:\\d+}/statuses/adjustercheckedin")
	public void confirmAdjusterCheckedIn(@PathVariable long projectId, @CurUserId long userId,
										 @RequestBody Map<String, Integer> formMap) throws ServiceException {
		int type = formMap.get("type");
		projectService.insertPilotCheckedStatus(projectId, userId, ConstantUtil.ADJUSTER_CHECKED_IN_FULL,
				ProjectStatusEnum.getEnum(type));
	}

	@PostMapping("/{projectId:\\d+}/statuses/adjustercheckedout")
	public void confirmAdjusterCheckedOut(@PathVariable long projectId, @CurUserId long userId,
										  @RequestBody Map<String, Integer> formMap) throws ServiceException {
		int type = formMap.get("type");
		projectService.insertPilotCheckedStatus(projectId, userId, ConstantUtil.ADJUSTER_CHECKED_IN_QUICK,
				ProjectStatusEnum.getEnum(type));
	}

	@GetMapping("/{projectId:\\d+}/statuses/adjusterchecked")
	public Map<String, Object> getConfirmAdjusterChecked(@PathVariable long projectId) throws Exception {
		EventHistory checkedStatus = projectService.getAdjusterCheckedStatus(projectId);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("checkedStatus", checkedStatus);
		return resultMap;
	}

	@PostMapping("/latLngs")
	public ResponseJson updateProjectLatLngs(@RequestBody List<ProjectLatLngVo> projectLatLngVos) {
		ResponseJson json = new ResponseJson();
		try {
			projectService.updateProjectLatLngVos(projectLatLngVos);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			return new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	@GetMapping("/zeroLatLngs")
	public ResponseJson getAllZeroLatLngs() {
		ResponseJson json = new ResponseJson();
		List<Map<String, Object>> result = null;
		try {
			result = projectService.getAllZeroLatLngs();
			Map<String,Object> dataMap = new HashMap<>();
			dataMap.put("zeroLatLngs", result);
			json.setData(dataMap);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			return new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	@GetMapping("/{projectId:\\d+}/detail")
	public ProjectDetailVo getProjectDetail(@PathVariable long projectId) throws Exception {
		long userId = springSecurityContextProvider.getUserIdFromContext();
		return projectService.getProjectDetail(userId, projectId);
	}

    @GetMapping("/{projectId:\\d+}/auth/roles")
	public ResponseJson listMemberRole(@PathVariable long projectId) throws Exception {
	    // check project existence
	    projectService.getById(projectId);

		ResponseJson json = new ResponseJson();
		long userId = springSecurityContextProvider.getUserIdFromContext();
		List<IdNameDto> roles = null;
		try {
			roles = userService.listRolesUserPlay(projectId, userId);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode();
			return new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("roles", roles);
		json.setData(result);
		return json;
	}

	@PatchMapping("/{projectId:\\d+}/chimney")
	public ResponseJson updateChimney(@PathVariable long projectId,
			@RequestBody Project project) throws Exception {
		ResponseJson json = new ResponseJson();
		projectService.updateChimney(projectId, project.getChimney());
		return json;
	}

	@PatchMapping("/{projectId:\\d+}/rotationDegree")
	public void updateRotationDegree(@PathVariable long projectId, @RequestBody Project project) {
		projectService.updateRotationDegree(projectId, project.getRotationDegree());
	}

	@GetMapping("/{projectId:\\d+}/rotationDegree")
	public double getRotationDegree(@PathVariable long projectId) throws ServiceException {
		return projectService.getById(projectId).getRotationDegree();
	}

	@GetMapping("/{projectId:\\d+}/address")
	public Address getProjectAddress(@PathVariable long projectId) throws Exception {
		return projectService.getProjectAddress(projectId);
	}

	@PutMapping("/{projectId:\\d+}/address")
	public Address updateProjectAddress(@PathVariable long projectId, @RequestBody Address address)
			throws Exception {
		return projectService.updateProjectAddress(projectId, address);
	}

    /**
     * 获取下载项目压缩包链接和ResourceKey
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @GetMapping("/{projectId:\\d+}/images/zip-file")
    public ProjectPartialWrapper.ImagesArchiveUrlVo getProjectImagesZipFile(@PathVariable long projectId,
        @CurUserId long userId) throws Exception {
        String imagesArchiveKey = projectService.getImagesArchiveUrl(projectId, userId);
        // get signed url from s3
        URL signedUrl = resourceUrlProvider.getGetUrl(imagesArchiveKey);
        log.info("imagesArchiveUrl is {} before redirect.", signedUrl);
        // urlRedirection.redirect(httpServletResponse, HttpStatus.FOUND, "application/zip",
        // signedUrl);
        return new ProjectPartialWrapper.ImagesArchiveUrlVo(
                Optional.ofNullable(signedUrl).map(Objects::toString).orElse(null),
                imagesArchiveKey);
    }

    /**
     * 检验zip包是否存在
     * @param projectId
     * @return
     */
    @GetMapping("/{projectId:\\d+}/images/zip-file/existence")
    public ResponseJson isArchiveExisted(@PathVariable long projectId) throws Exception {
        boolean existed = projectService.isProjectImageArchiveExist(projectId);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("isExist", existed);
        return new ResponseJson(dataMap);
    }

	@GetMapping("/{projectId:\\d+}/insured-info")
    public ProjectInsuredInfoVo getInsuredInfo(@PathVariable long projectId) throws Exception {
        return projectService.getInsuredInfo(projectId);
    }

    @PutMapping("/{projectId:\\d+}/insured-info")
    public ProjectInsuredInfoVo updateInsuredInfo(@PathVariable long projectId,
        @CurUserId long userId,
        @Valid @RequestBody ProjectInsuredInfoVo insuredInfoVo) throws Exception {
	    Optional.ofNullable(insuredInfoVo).ifPresent(ProjectDataNormalizer::normalize);
        return projectService.updateInsuredInfo(projectId, userId, insuredInfoVo);
    }

    @GetMapping("/{projectId:\\d+}/inspection-info")
    public ProjectInspectionInfoVo getInspectionInfo(@PathVariable long projectId) throws Exception {
        return projectService.getInspectionInfo(projectId);
    }

    /**
     * inspectionTime won't be updated
     */
    @PutMapping("/{projectId:\\d+}/inspection-info")
    public ProjectInspectionInfoVo updateInspectionInfo(@PathVariable long projectId,
                                                        @CurUserId long userId,
        @Valid @RequestBody ProjectInspectionInfoVo inspectionInfoVo) throws Exception {
        return projectService.updateInspectionInfo(projectId, userId, inspectionInfoVo);
    }

    @GetMapping("/options")
    public Map<String, Object> getProjectsServiceOptions() throws Exception {
        return projectService.getProjectsServiceOptions();
    }

    @GetMapping("/{projectId:\\d+}/service")
    public ProjectServiceTypeVo getProjectServiceType(@PathVariable long projectId) throws Exception {
        return projectService.getProjectServiceType(projectId);
    }

    @PostMapping("/{projectId:\\d+}/service")
    public ProjectServiceTypeVo updateProjectServiceType(@CurUserId long userId, @PathVariable long projectId,
                                                         @RequestBody ProjectServiceTypeParam serviceTypeParam) throws Exception {
        return projectService.updateProjectServiceType(userId, projectId, serviceTypeParam);
    }

    @GetMapping("/{projectId:\\d+}/checklist")
    public ResponseJson getChecklistUrl(@PathVariable long projectId) throws Exception {
        return new ResponseJson(projectService.getChecklistUrl(projectId));
    }

    /**
     * this restfull api is just for complete lost data in ai, it is a non-routine operation only when data in redis is evicted.
     * @param projectId
     * @return
     * @throws Exception
     */
    @PutMapping("/{projectId:\\d+}/report/requiredData")
    public void transferDatasToAi(@PathVariable long projectId) throws Exception{
        projectService.transferDatasToAi(projectId, ProjectSyncPointEnum.MANUAL_SYNC.getType());
    }

    @PutMapping("/{projectId:\\d+}/status/payment")
    public void updatePaymentStatus(@CurUserId long userId, @PathVariable long projectId,
                                    @RequestBody ProjectPaymentStatusDto paymentStatusDto) throws ServiceException {
        final int payStatus = paymentStatusDto.getPayStatus();
        PayStatusEnum.check(payStatus);

        projectService.updatePayStatus(projectId, userId, payStatus);
    }

    @GetMapping("/{projectId:\\d+}/status/composite")
    public ProjectCompositeStatusVo getProjectCompositeStatus(@PathVariable long projectId) throws ServiceException {
        final Project project = projectService.getById(projectId);
        ProjectCompositeStatusVo vo = new ProjectCompositeStatusVo();
        if (project == null){
            throw new ServiceException(MessageCode.PARAM_INVALID);
        }
        vo.setCanceled(NewProjectStatusEnum.PROJECT_CANCELED == NewProjectStatusEnum.getEnum(project.getProjectStatus()));
        vo.setPaid(PayStatusEnum.PAID == PayStatusEnum.getEnum(project.getPayStatus()));
        vo.setPlnarSubscribed(Objects.equals(Project.PlnarStatus.SUBSCRIBED.getStatus(), project.getPlnarStatus()));

        return vo;
    }

    /**
     * This method is no longer called on Portal website
     */
    @Deprecated
    @PostMapping("/{projectId:\\d+}/rework")
    public void updateProjectToReworkStatus(@CurUserId long userId, @PathVariable long projectId,
                                            @Valid @RequestBody ProjectReworkReasonParam reworkReasonParam)
        throws Exception {
        projectService.updateProjectToReworkStatus(userId, projectId, reworkReasonParam, null);
    }

    @PostMapping("/{projectId:\\d+}/service-reason")
    public void updateProjectServiceTypeReason(@CurUserId long userId, @PathVariable long projectId,
                                                         @Valid @RequestBody ProjectServiceTypeReasonParam serviceTypeParam) throws Exception {
        projectService.updateProjectServiceTypeReason(userId, projectId, serviceTypeParam);
    }


    @PostMapping("/{projectId:\\d+}/score")
    public void projectScore(@CurUserId long userId, @PathVariable long projectId, @RequestBody ProjectScoreDto scoreDto) throws ServiceException {
        projectScoreService.createOrUpdateProjectScore(userId, projectId, scoreDto.getScore());
    }

    @PostMapping("/{projectId:\\d+}/notify-pilot")
    public void sendSmsTextToPilot(@CurUserId long userId, @PathVariable long projectId,
                                               @Valid @RequestBody SendSmsPilotParam pilotParam) throws Exception {
        projectService.sendSmsTextToPilot(userId, projectId, pilotParam);
    }

    @GetMapping("/{projectId:\\d+}/invoice")
    public InvoiceFileVo getInvoice(@PathVariable long projectId) throws ServiceException {
        InvoiceFileVo invoice = projectService.getInvoiceFile(projectId);
        if (invoice == null) {
            throw new ResourceNotFoundException("invoice file for project " + projectId + " not found.");
        }
        return invoice;
    }

    @PostMapping("/{projectId:\\d+}/resend-inspection-code")
    public void resendInspectionCode(@CurUserId long userId, @PathVariable long projectId) {
        projectService.resendInspectionCode(userId, projectId);
    }

    /**
     * Validate the project is not a bundle project.
     * Underwriter for bundle project is not allowed to be changed.
     *
     * @param projectId project id
     */
    private void validateProjectNotBundle(Long projectId) {
        var bundleGroup =
            projectGroupManager.findByProjectId(
                String.valueOf(projectId), "BUNDLE_PROJECT");
        if (bundleGroup != null) {
            throw new IllegalArgumentException(
                String.format("Cannot set UNDERWRITER for bundle project %s.", projectId));
        }
    }
}
