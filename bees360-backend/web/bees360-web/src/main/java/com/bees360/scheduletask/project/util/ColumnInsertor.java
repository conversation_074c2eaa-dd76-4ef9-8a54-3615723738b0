package com.bees360.scheduletask.project.util;

import com.google.common.base.Preconditions;
import lombok.Data;
import org.thymeleaf.util.StringUtils;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ColumnInsertor {

    private final static int COLS_END_INDEX = -1;

    /**
     * 从 {@code columns} 找到 {@code columnNameInPosition}的元素，然后将新元素{@code insert}插入到该元素的前面。
     * 如果找不到目标元素，则直接插入到最后。
     */
    public static void insertBefore(@Nonnull ColumnValue insert, @Nullable String columnNameInPosition,
            @Nonnull List<ColumnValue> columns) {
        Preconditions.checkNotNull(insert, "parameter `insert` may not be null.");
        int index = indexOf(columnNameInPosition, columns);
        insert(insert, index, columns);
    }

    /**
     * 从 {@code columns} 找到 {@code columnNameInPosition}的元素，然后将新元素{@code insert}插入到该元素的后面。
     * 如果找不到目标元素，则直接插入到最后。
     */
    public static void insertAfter(@Nonnull ColumnValue insert, @Nullable String columnNameInPosition,
            @Nonnull List<ColumnValue> columns) {
        Preconditions.checkNotNull(insert, "parameter `insert` may not be null.");
        int index = indexOf(columnNameInPosition, columns);
        if (index != COLS_END_INDEX) {
            index ++;
        }
        insert(insert, index, columns);
    }

    /**
     * 元素{@code insert}写入到位置 index，原元素往后移。
     */
    public static void insert(@Nonnull ColumnValue insert, int index, @Nonnull List<ColumnValue> columns) {
        if (index < 0) {
            columns.add(insert);
        } else {
            columns.add(index, insert);
        }
    }

    /**
     * @return 返回 {@code ColumnValue#getKeyName() == columnNameInPosition}的元素下标，找不到则返回 -1.
     */
    private static int indexOf(String columnNameInPosition, List<ColumnValue> columns) {
        int index = COLS_END_INDEX;
        if (Objects.isNull(columnNameInPosition)) {
            return index;
        }
        for (int i = 0; i < columns.size(); i ++) {
            ColumnValue column = columns.get(i);
            if (StringUtils.equals(column.getKeyName(), columnNameInPosition)) {
                index = i;
                break;
            }
        }
        return index;
    }
}
