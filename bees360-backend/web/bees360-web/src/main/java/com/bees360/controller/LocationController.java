package com.bees360.controller;

import java.util.List;

import jakarta.inject.Inject;

import org.springframework.context.MessageSource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.bees360.base.MessageCode;
import com.bees360.base.ResponseJson;
import com.bees360.base.exception.ServiceException;
import com.bees360.service.LocationService;

/*
 * <AUTHOR>
 * @date 2017.11.7
 */
@Controller
@RequestMapping("/location")
public class LocationController {
	@Inject
	private MessageSource messageSource;

	@Inject
	LocationService locationService;

	/**
	 * get no duplicated cities start with prefix limited 20
	 */
	@GetMapping("/cities")
	@ResponseBody
	public ResponseJson listRelatedCities(String prefix){
		ResponseJson json = new ResponseJson();
		List<String> cities = null;
		try {
			cities = locationService.listCitiesWithPrefix(prefix);
			json.setData(cities);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode() + "";
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

	/**
	 * get states start with prefix limited 20
	 */
	@GetMapping("/states")
	@ResponseBody
	public ResponseJson listRelatedStates(String prefix, Integer limit){
		ResponseJson json = new ResponseJson();
		List<String> states = null;
		limit = limit == null? -1: limit;
		try {
			states = locationService.listRegionsWithPrefix(prefix, limit);
			json.setData(states);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode() + "";
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}
	/**
	 * get countries start with prefix limited 20
	 */
	@GetMapping("/countries")
	@ResponseBody
	public ResponseJson listRelatedCountries(String prefix, Integer limit){
		ResponseJson json = new ResponseJson();
		limit = limit == null? -1: limit;
		List<String> countries = null;
		try {
			countries = locationService.listCountriesWithPrefix(prefix, limit);
			json.setData(countries);
		} catch (ServiceException e) {
			String msgCode = e.getMsgCode() + "";
			json = new ResponseJson(msgCode, MessageCode.getMessage(messageSource, msgCode));
		}
		return json;
	}

}
