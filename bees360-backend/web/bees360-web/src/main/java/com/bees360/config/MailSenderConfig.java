package com.bees360.config;

import com.bees360.entity.Project;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.mail.MailSender;
import com.bees360.mail.MailSenderProvider;
import com.bees360.mail.config.JobMailSenderConfig;
import com.bees360.service.properties.Bees360CompanyConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;
import java.util.function.BiFunction;

@Import({
    JobMailSenderConfig.class,
})
@Configuration
public class MailSenderConfig {

    @Bean("backendMailSender")
    MailSender backendSender(MailSenderProvider mailSenderProvider) {
        return mailSenderProvider.get("backend-mail-sender");
    }

    @Bean("doNotReplyMailSender")
    MailSender doNotReplyMailSender(MailSenderProvider mailSenderProvider) {
        return mailSenderProvider.get("do-not-reply-mail-sender");
    }

    @Bean("clientMailSender")
    MailSender clientMailSender(MailSenderProvider mailSenderProvider) {
        return mailSenderProvider.get("client-mail-sender");
    }

    @Bean("projectEmailSubscription")
    public BiFunction<Project, String, List<UserTinyVo>> projectEmailSubscription(
            Bees360CompanyConfig bees360CompanyConfig) {
        return bees360CompanyConfig::projectEmailSubscription;
    }
}
