package com.bees360.web.security.expression.impl;

import jakarta.servlet.http.HttpServletRequest;

import com.bees360.web.security.expression.ImageSecurity;
import com.bees360.web.security.expression.ProjectSecurity;
import com.bees360.web.security.expression.ReportSecurity;
import com.bees360.web.security.expression.SecurityAccess;
import com.bees360.web.security.expression.UserSecurity;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component("SCAC")
public class SecurityAccessImpl implements SecurityAccess {

    private final UserSecurity userSecurity;
    private final ProjectSecurity projectSecurity;
    private final ReportSecurity reportSecurity;
    private final ImageSecurity imageSecurity;

    @Override
    public boolean isImageInAccessibleProject(HttpServletRequest request, Authentication authentication, String imageId) {
        return imageSecurity.isImageInAccessibleProject(request, authentication, imageId);
    }

    @Override
    public boolean isProjectAccessible(HttpServletRequest request, Authentication authentication, long projectId) {
        return projectSecurity.isProjectAccessible(request, authentication, projectId);
    }

    @Override
    public boolean isMemberIn(HttpServletRequest request, Authentication authentication, long projectId) {
        return projectSecurity.isMemberIn(request, authentication, projectId);
    }

    @Override
    public boolean isMemberWith(HttpServletRequest request, Authentication authentication, long projectId,
        String... roles) {
        return projectSecurity.isMemberWith(request, authentication, projectId, roles);
    }

    @Override
    public boolean isMemberWithAll(HttpServletRequest request, Authentication authentication, long projectId, String... roles) {
        return projectSecurity.isMemberWithAll(request, authentication, projectId, roles);
    }

    @Override
    public boolean isMemberEquals(HttpServletRequest request, Authentication authentication, long projectId, String... roles) {
        return projectSecurity.isMemberEquals(request, authentication, projectId, roles);
    }

    @Override
    public boolean isManagedBy(HttpServletRequest request, Authentication authentication, long projectId) {
        return projectSecurity.isManagedBy(request, authentication, projectId);
    }

    @Override
    public boolean isReportInAccessibleProject(HttpServletRequest request, Authentication authentication,
        String reportId) {
        return reportSecurity.isReportInAccessibleProject(request, authentication, reportId);
    }

    @Override
    public boolean isReportPaid(HttpServletRequest request, Authentication authentication, String reportId) {
        return reportSecurity.isReportPaid(request, authentication, reportId);
    }

    @Override
    public boolean isReportReady(HttpServletRequest request, Authentication authentication, String reportId) {
        return reportSecurity.isReportReady(request, authentication, reportId);
    }

    @Override
    public boolean isReportListable(HttpServletRequest request, Authentication authentication, String reportId) {
        return reportSecurity.isReportListable(request, authentication, reportId);
    }

    @Override
    public boolean isMyself(HttpServletRequest request, Authentication authentication, long userId) {
        return userSecurity.isMyself(request, authentication, userId);
    }

    @Override
    public boolean hasAnyRole(Authentication authentication, String... roles) {
        return userSecurity.hasAnyRole(authentication, roles);
    }
}
