package com.bees360.config;

import com.bees360.event.RabbitEventDispatcher;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.SmartLifecycle;

/**
 * To make sure the RabbitEventDispatcher shutdown as quickly as possible.
 */
@Log4j2
public class RabbitEventDispatcherShutdownLifecycle implements SmartLifecycle {

    private final RabbitEventDispatcher eventDispatcher;
    private volatile boolean running = false;

    public RabbitEventDispatcherShutdownLifecycle(RabbitEventDispatcher eventDispatcher) {
        this.eventDispatcher = eventDispatcher;
        log.info("Created {}(eventDispatcher={})", this, eventDispatcher);
    }

    @Override
    public void start() {
        this.running = true;
    }

    @Override
    public void stop() {
        this.running = false;
        try {
            log.info("Try to close {}", this.eventDispatcher);
            this.eventDispatcher.close();
            log.info("Complete to close {}", this.eventDispatcher);
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
    }

    @Override
    public boolean isRunning() {
        return this.running;
    }
}
