package com.bees360.web.openapi;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.SystemException;
import com.bees360.base.exception.UncheckedServiceException;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.openapi.OpenReportVo;
import com.bees360.resource.GrpcResourceClient;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectReportService;
import com.bees360.service.ProjectService;
import com.bees360.service.openapi.OpenReportService;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.util.httputil.UrlRedirection;
import com.google.gson.Gson;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.UncheckedIOException;
import java.util.Optional;
import java.util.Set;

/**
 * get pdf/xml file from amazon s3
 *
 * <AUTHOR>
 * @date 2019-12-26
 */

@RestController
@RequestMapping("/v1/report")
@Slf4j
public class OpenApiReportController {

    private static final Gson gson = new Gson();

    @Autowired
    private ProjectReportFileService projectReportFileService;

    @Autowired
    private ProjectReportService projectReportService;

    @Autowired
    private OpenReportService openReportService;

    @Autowired
    private UrlRedirection urlRedirection;

    @Autowired
    private GrpcResourceClient grpcResourceClient;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private Bees360CompanyConfig bees360CompanyConfig;

    @GetMapping("/{reportId}/file")
    public void redirectTo(@PathVariable String reportId, @RequestParam(required = false) Boolean compressed,
            HttpServletResponse httpServletResponse) throws Exception {
        ProjectReportFile reportFile = projectReportFileService.getById(reportId);
        if (reportFile == null) {
            throw new ResourceNotFoundException("report " + reportId + " not found.");
        }
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportFile.getReportType());
        String reportUrl = getReportFileUrl(reportFile, compressed);
        log.debug("Openapi get the report {} get url: {}", reportId, reportUrl);
        urlRedirection.redirect(httpServletResponse, HttpStatus.TEMPORARY_REDIRECT, reportType.getContentType(),
            reportUrl);
    }

    private String getReportFileUrl(ProjectReportFile reportFile, Boolean compressed) {
        String reportKey = "";
        if (compressed == null) {
            if (isNeedCompressedReport(reportFile)) {
                reportKey = reportFile.getReportPdfCompressed();
            }
            if (StringUtils.isEmpty(reportKey)) {
                reportKey = reportFile.getReportPdfFileName();
            }
        } else {
            if (compressed) {
                reportKey = reportFile.getReportPdfCompressed();
                if (StringUtils.isEmpty(reportKey)) {
                    throw new ResourceNotFoundException("No compressed report found for " + reportFile.getReportId());
                }
            } else {
                reportKey = reportFile.getReportPdfFileName();
            }
        }

        try {
            return grpcResourceClient.asResourceUrlProvider().getGetUrl(reportKey).toString();
        } catch (UncheckedIOException ex) {
            throw new SystemException("fail to get report file url for report " + reportFile.getReportId());
        }
    }

    private boolean isNeedCompressedReport(ProjectReportFile reportFile) {
        try {
            Set<Long> managerCompanies = projectService.getProjectManageCompany(reportFile.getProjectId());
            // 获取报告配置中最小的压缩值
            Optional<Boolean> needCompressedFile = bees360CompanyConfig.getCompanies().stream()
                .filter(c -> managerCompanies.contains(c.getId()) && hasReportCompressedSizeConfig(c))
                .findAny()
                .map(company -> true);
            return needCompressedFile.orElse(false);
        } catch (ServiceException e) {
            throw new UncheckedServiceException(e.getMessage(), e);
        }
    }

    private boolean hasReportCompressedSizeConfig(Bees360CompanyConfig.CompanyConfigItem configItem) {
        return configItem.getReport() != null && configItem.getReport().getCompressedSize() != null;
    }

    @GetMapping("/{reportId}/url")
    public OpenReportVo.OpenReportUrlVo getProjectReportUrl(@PathVariable String reportId) {
        ProjectReportFile reportFile = projectReportFileService.getById(reportId);
        if (reportFile == null) {
            throw new ResourceNotFoundException("report " + reportId + " not found.");
        }
        String reportUrl = getReportFileUrl(reportFile, null);

        OpenReportVo.OpenReportUrlVo openReportUrlVo = new OpenReportVo.OpenReportUrlVo();
        openReportUrlVo.setUrl(reportUrl);
        return openReportUrlVo;
    }
}
