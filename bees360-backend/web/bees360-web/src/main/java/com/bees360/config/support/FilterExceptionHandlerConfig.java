package com.bees360.config.support;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 处理从filter中抛出的异常，确保返回的结构与 {@code ControllerExceptionHandler} 一致。
 */
@Configuration
public class FilterExceptionHandlerConfig {

    @Log4j2
    static class FilterExceptionHandleFilter extends OncePerRequestFilter {

        private final HandlerExceptionResolver resolver;

        FilterExceptionHandleFilter(@NonNull HandlerExceptionResolver resolver) {
            this.resolver = resolver;
            log.info("Created {}(resolver={})", this, resolver);
        }

        @Override
        protected void doFilterInternal(
            HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain) throws ServletException, IOException {
            try {
                filterChain.doFilter(request, response);
            } catch (Exception e) {
                resolver.resolveException(request, response, null, e);
            }
        }
    }

    @Bean
    public FilterRegistrationBean<FilterExceptionHandleFilter> exceptionFilterRegistration(
        @Qualifier("handlerExceptionResolver") HandlerExceptionResolver resolver) {
        FilterRegistrationBean<FilterExceptionHandleFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new FilterExceptionHandleFilter(resolver));
        registration.setName("filterExceptionHandleFilter");
        /* 这个序号要很小，保证 exceptionFilter 是所有过滤器链的入口 */
        registration.setOrder(Integer.MIN_VALUE);
        return registration;
    }
}
