package com.bees360.config.checker;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Properties;
import java.util.Set;

import com.bees360.base.MessageCode;
import com.bees360.util.PropertiesUtils;

import javassist.Modifier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class MessageCodeChecker implements BasicChecker {

	private Logger logger = LoggerFactory.getLogger(getClass());

	@Override
	public void check() {
		checkMessageCode();
	}

	private void checkMessageCode() {
		List<String> messageCodeProperitesFiles = Arrays.asList("messages_en.properties");
		Set<String> messageCodes;
		try {
			messageCodes = initMessageCodes(MessageCode.class);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		// 判断设置的message code变量是否与文件中的code一一匹配
		// 判断message是否以 “.” 结尾
		for(String file: messageCodeProperitesFiles) {
			Properties properites = PropertiesUtils.getResourcesProperties(file);

			for(String code: messageCodes) {
				if(!properites.containsKey(code)) {
					throw new RuntimeException("The code " + code + " in " + MessageCode.class
							+ " doesn't match a message");
				}
				String message = properites.getProperty(code);
				if(!message.endsWith(".")) {
					throw new RuntimeException("The message of code " + code + " in file " + file
							+ " should end with \".\".");
				}
			}
			for(Object code: properites.keySet()) {
				if(!messageCodes.contains(code.toString())) {
					logger.warn("There isn't code " + code + " in " + MessageCode.class + " for " + file);
				}
			}
		}
	}

	Set<String> initMessageCodes(Class<?> clazz) throws Exception {
		Set<String> messageCodes = new HashSet<String>();

        Field[] fields = clazz.getFields();

        for(Field field : fields){
        	String modifier = Modifier.toString(field.getModifiers());
        	if("public static final".equals(modifier)) {
        		if(!"java.lang.String".equals(field.getType().getCanonicalName())) {
        			continue;
        		}
        		Object codeValue = field.get(null);
        		if(codeValue == null) {
        			throw new Exception("Message code shouldn't be null.");
        		}
        		String code = codeValue.toString();
        		if(messageCodes.contains(code)) {
        			throw new Exception("Thera is a duplicated message code: " + codeValue);
        		}
        		messageCodes.add(code);
        	};
        }
        return messageCodes;
	}

	public static void main(String[] args) {
		BasicChecker checker = new MessageCodeChecker();
		checker.check();
		System.out.println(checker.getClass().getSimpleName());
	}
}
