package com.bees360.web.openapi;

import com.bees360.openapi.DefaultOpenApiReportProvider;
import com.bees360.service.openapi.OpenReportService;
import com.bees360.service.util.AWSLambdaSummaryConverterConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.function.BinaryOperator;

@Import({
    AWSLambdaSummaryConverterConfig.class,
})
@Configuration
public class OpenApiReportManagerConfig {

    @Bean
    DefaultOpenApiReportProvider openApiReportProvider(
        OpenReportService openReportService,
        @Qualifier("lambdaReportSummaryConverter") BinaryOperator<String> lambdaReportSummaryConverter,
        ObjectMapper objectMapper) {
        return new DefaultOpenApiReportProvider(openReportService, lambdaReportSummaryConverter, objectMapper);
    }
}
