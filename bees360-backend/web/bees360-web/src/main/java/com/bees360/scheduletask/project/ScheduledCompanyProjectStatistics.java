package com.bees360.scheduletask.project;

import com.bees360.common.util.DateUtil;
import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.entity.dto.ProjectStatistics;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.event.SendCompanyProjectStatsRegularly;
import com.bees360.service.MessageService;
import com.bees360.service.statistics.ProjectStatisticsService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

/**
 * using {@link SendCompanyProjectStatsRegularly} instead.
 */
@Log4j2
@Deprecated
@Component
public class ScheduledCompanyProjectStatistics {

    @Autowired private ProjectStatisticsService projectStatisticsService;

    @Autowired private MessageService messageService;

    private static final String DEFAULT_TIME_ZONE = AmericaTimeZone.US_CENTRAL;

    @Scheduled(cron = "${app.web.scheduled.send-company-project-stats.cron:0 0 9 * * *}", zone = DEFAULT_TIME_ZONE)
    public void sendCompanyProjectStats() {
        final ZoneId zoneId = ZoneId.of(DEFAULT_TIME_ZONE);
        final LocalDate yesterday = LocalDate.now(zoneId).minusDays(1);
        final LocalDateTime start = LocalDateTime.of(yesterday, LocalTime.MIN);
        final LocalDateTime end = LocalDateTime.of(yesterday, LocalTime.MAX);
        final Instant instantStart = DateUtil.toInstant(start, zoneId);
        final Instant instantEnd = DateUtil.toInstant(end, zoneId);

        log.info("SendCompanyProjectStats with time range({}, {})", instantStart, instantEnd);
        final Map<InspectionPurposeTypeEnum, ProjectStatistics.ProjectStatsPerCompanySummary> projectStatsSummaries =
            projectStatisticsService.projectStatsSummary(instantStart, instantEnd);
        messageService.sendProjectStatisticsPerCompany(projectStatsSummaries, yesterday);
    }


}
