package com.bees360.scheduletask.project;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.excel.EasyExcel;
import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;
import com.bees360.entity.dto.BsExportDataLogProjectEditorV3;
import com.bees360.entity.dto.KeyIndex;
import com.bees360.entity.dto.KeyValue;
import com.bees360.scheduletask.project.util.ColumnInsertor;
import com.bees360.scheduletask.project.util.ColumnValue;
import com.bees360.scheduletask.project.util.MapColumnValue;
import com.google.gson.Gson;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> Yang
 */
public class ExportProjectForSwyfftUtilV3 implements ExportProjectForSwyfftUtil {

    private final static String SHEET_NAME = "To Swyfft";

    private final Gson gson = createGson();

    private Gson createGson() {
        return new Gson();
    }

    @Override
    public ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData fetchExcelData(List<Project> projects,
        List<BsExportData> bsExportDataList) {

        if (bsExportDataList.isEmpty()) {
            return new ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData(null, 0);
        }

        try {
            return exportExcel(projects, bsExportDataList);
        } catch (IOException e) {
            String message = "Fail to export data to excel".formatted();
            throw new RuntimeException(message, e);
        }
    }

    private ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData exportExcel(List<Project> projects, List<BsExportData> dataList) throws IOException {
        List<List<Object>> excelRows = new ArrayList<>();
        List<ColumnValue> columnValues = fetchColumnValues(dataList);

        excelRows.add(columnValues.stream().map(ColumnValue::getKeyName).collect(Collectors.toList()));

        Map<String, BsExportData> exportDataMap = dataList.stream().collect(Collectors.toMap(BsExportData::getRelatedId, d -> d));
        for (Project project: projects) {
            BsExportData data = exportDataMap.get(project.getProjectId() + "");
            if (Objects.isNull(data) || ObjectUtils.isEmpty(data.getDataLog())) {
                continue;
            }
            BsExportDataLogProjectEditorV3 dataLog = gson.fromJson(data.getDataLog(), BsExportDataLogProjectEditorV3.class);
            Map<String, String> map = keyValueToMap(dataLog.getData());
            List<Object> row = new ArrayList<>(columnValues.size());
            columnValues.stream().forEach(col -> {
                boolean supported = false;
                supported |= col.ifSupport(map, val -> row.add(val));
                supported |= col.ifSupport(project, val -> row.add(val));
                if (!supported) {
                    row.add("");
                }
            });
            excelRows.add(row);
        }
        try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            EasyExcel.write(output).sheet(SHEET_NAME).doWrite(excelRows);
            // 第一行是title
            int dataRow = excelRows.size() - 1;
            return new ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData(output.toByteArray(), dataRow);
        }
    }

    private void addProjectColumns(List<ColumnValue> columnValues) {
        ColumnInsertor.insertAfter(new ColumnValue<>(Project.class, "State", Project::getState),
            "insuredInfo.assetOwnerName", columnValues);
        ColumnInsertor.insertAfter(new ColumnValue<>(Project.class, "Policy #", Project::getPolicyNumber),
            "insuredInfo.assetOwnerName", columnValues);
    }

    private List<String> fetchProjectKeyOrders(List<BsExportData> dataList) {
        List<BsExportData> orderDataList =
            dataList.stream().sorted(Comparator.comparing(BsExportData::getId).reversed()).collect(Collectors.toList());

        List<String> orders = new ArrayList<>();
        for (BsExportData data : orderDataList) {
            // 找到第一个含有顺序的文件
            orders = fetchProjectKeyOrders(data);
            if (!orders.isEmpty()) {
                return orders;
            }
        }
        return orders;
    }

    private List<ColumnValue> fetchColumnValues(List<BsExportData> dataList) {
        List<String> columns = fetchProjectKeyOrders(dataList);
        List<ColumnValue> columnValues = columns.stream().map(columnName -> new MapColumnValue(columnName)).collect(Collectors.toList());
        addProjectColumns(columnValues);
        return columnValues;
    }

    private List<String> fetchProjectKeyOrders(BsExportData data) {
        BsExportDataLogProjectEditorV3 dataLog = gson.fromJson(data.getDataLog(), BsExportDataLogProjectEditorV3.class);
        if(dataLog.getConfig() == null || CollectionUtils.isEmpty(dataLog.getConfig().getExport())) {
            return new ArrayList<>();
        }
        List<KeyIndex> keyIndices = dataLog.getConfig().getExport();
        keyIndices = keyIndices.stream().filter(k -> k != null).collect(Collectors.toList());
        keyIndices.sort(Comparator.comparing(KeyIndex::getIndex));
        List<String> order = keyIndices.stream().map(KeyIndex::getKey).collect(Collectors.toList());
        return order;
    }

    private Map<String, String> keyValueToMap(List<KeyValue> keyValues) {
        Map<String, String> map = new HashMap<>(keyValues.size());
        for (KeyValue keyValue : keyValues) {
            map.put(keyValue.getKey(), keyValue.getValue());
        }
        return map;
    }
}
