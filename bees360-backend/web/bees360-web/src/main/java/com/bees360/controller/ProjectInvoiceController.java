package com.bees360.controller;

import com.bees360.config.support.CurUserId;
import com.bees360.entity.Project;
import com.bees360.entity.vo.InvoiceFileVo;
import com.bees360.entity.vo.InvoiceVo;
import com.bees360.service.InvoiceService;
import com.bees360.service.ProjectService;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.service.properties.Bees360CompanyConfig.CompanyConfigItem;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/09/26 10:47
 */
@RestController
@RequestMapping("/projects/{projectId:\\d+}/invoices")
public class ProjectInvoiceController {

    private final static long NEW_INVOICE_ID = 0;

    private Logger logger = LoggerFactory.getLogger(ProjectInvoiceController.class);

    @Inject
    private InvoiceService invoiceService;

    @Inject
    private ProjectService projectService;

    @Inject
    private Bees360CompanyConfig bees360CompanyConfig;

    @Deprecated
    @GetMapping("/file")
    public void downloadInvoiceFile(@CurUserId long userId, @PathVariable long projectId, HttpServletResponse response) {
        invoiceService.fetchInvoiceFile(projectId, response);
    }

    @Deprecated
    @GetMapping("/{invoiceId:\\d+}/file")
    public void downloadInvoiceFile(@CurUserId long userId, @PathVariable long projectId,
        @PathVariable long invoiceId, HttpServletResponse response) {
        invoiceService.fetchInvoiceFile(projectId, invoiceId, response);
    }

    @GetMapping("")
    public List<InvoiceVo> listInvoice(@CurUserId long userId, @PathVariable long projectId) throws Exception {
        Project project = projectService.getById(projectId);
        if (isUsingNewInvoice(project)) {
            InvoiceFileVo newInvoice = projectService.getInvoiceFile(projectId);
            if (newInvoice == null) {
                return Lists.newArrayList();
            }
            InvoiceVo invoice = new InvoiceVo();
            invoice.setProjectId(projectId);
            invoice.setResourceKey(newInvoice.getInvoiceFile());
            invoice.setTitle(newInvoice.getFileName());
            invoice.setInvoiceId(NEW_INVOICE_ID);
            invoice.setCreateTime(0);

            return Lists.newArrayList(invoice);
        }
        return invoiceService.listInvoice(userId, projectId);
    }

    private boolean isUsingNewInvoice(Project project) {
        String shopCode = Optional.ofNullable(project).map(Project::getInsuranceCompany).map(bees360CompanyConfig::findConfig)
            .map(CompanyConfigItem::getShopCode).orElse(null);
        return StringUtils.isNotEmpty(shopCode);
    }
}
