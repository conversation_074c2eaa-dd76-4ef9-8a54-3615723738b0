package com.bees360.listener;

import com.bees360.event.registry.ProjectParentChildGroupUpdatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.firebase.FirebaseProjectService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Objects;

/**
 * 监听项目父子组更新事件并更新Firebase中的项目父子关系
 */

@Log4j2
public class UpdateFirebaseParentChildProjectListener extends AbstractNamedEventListener<ProjectParentChildGroupUpdatedEvent> {
    private final FirebaseProjectService firebaseProjectService;

    public UpdateFirebaseParentChildProjectListener(FirebaseProjectService firebaseProjectService) {
        this.firebaseProjectService = firebaseProjectService;
    }

    @Override
    public void handle(ProjectParentChildGroupUpdatedEvent event) throws IOException {
        log.info("Received ProjectParentChildGroupUpdatedEvent event: {}, start to update firebase project.", event);
        String groupKey = event.getGroupKey();
        if (event.isDeleted()) {
            groupKey = null;
        }
        log.info("Update firebase project {} with parent project: {}", event.getProjectId(), groupKey);
        firebaseProjectService.updateParentChildProject(Long.parseLong(event.getProjectId()), groupKey);
    }
}
