package com.bees360.controller.management;

import com.bees360.base.exception.ServiceException;
import com.bees360.config.support.CurUserId;
import com.bees360.entity.dto.management.ProjectStatusChangeDto;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.label.ProjectLabel;
import com.bees360.entity.label.ProjectLabelDto;
import com.bees360.service.ProjectHoverService;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import java.util.List;

@Log4j2
@Validated
@RestController
@RequestMapping("/management/project")
public class ProjectMController {

    @Inject
    private ProjectService projectService;

    @Inject
    private ProjectLabelService projectLabelService;

    @Autowired
    private ProjectHoverService projectHoverService;

    @PostMapping("/{projectId:\\d+}/status/cr")
    public void changeProjectStatus(@CurUserId long userId, @PathVariable long projectId, @RequestBody ProjectStatusChangeDto dto) throws ServiceException{
        final Integer code = dto.getCode();
        if (code == ProjectStatusChangeDto.CANCEL_CODE){
            projectService.cancelProject(projectId, userId, "");
            return;
        }
        if (code == ProjectStatusChangeDto.RECOVER_CODE){
            projectService.recoverProject(projectId, userId);
        }
    }

    @PutMapping("/label")
    public void changeLabel(@CurUserId long userId, @RequestBody ProjectLabelDto dto) {
        projectLabelService.markAfterEraseLabel(dto.getProjectId(), dto.getLabelIds(), userId,
            SystemTypeEnum.BEES360);
    }

    @GetMapping("/label")
    public List<ProjectLabel> labelList(){

        return projectLabelService.labelList();
    }

    @PostMapping("/{projectId:\\d+}/hover")
    public long createHoverJob(@PathVariable long projectId) throws Exception {
        return projectHoverService.createHoverJob(projectId);
    }
}
