package com.bees360.config.project;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;
import java.util.function.Function;

@Configuration
public class ProjectConfig {

    @Bean
    public Function<String, URI> convertAvatarToUri(
            @Value("${resource.client.base-url}") String baseUrl) {
        if (!baseUrl.endsWith("/")) {
            baseUrl = baseUrl + "/";
        }
        String finalBaseUrl = baseUrl;
        return avatar -> URI.create(finalBaseUrl).resolve(avatar);
    }
}
