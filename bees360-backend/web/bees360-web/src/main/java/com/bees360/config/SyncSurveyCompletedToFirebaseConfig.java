package com.bees360.config;

import com.bees360.event.SyncSurveyCompletedToFirebaseOnReportAdded;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.job.SyncSurveyCompletedToFirebaseJobExecutor;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({
    SyncSurveyCompletedToFirebaseJobExecutor.class,
    SyncSurveyCompletedToFirebaseOnReportAdded.class,
    AutoRegisterEventListenerConfig.class,
    AutoRegisterJobExecutorConfig.class,
})
@ConditionalOnProperty(
    prefix = "bees360.feature-switch", value = "enable-sync-survey-completed", havingValue = "true")
public class SyncSurveyCompletedToFirebaseConfig {}
