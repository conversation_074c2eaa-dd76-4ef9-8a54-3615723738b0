package com.bees360.web.security.expression.impl;

import jakarta.servlet.http.HttpServletRequest;

import com.bees360.web.security.expression.ImageSecurity;
import com.bees360.web.security.expression.ProjectSecurity;
import com.bees360.web.security.SecurityAttributeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import com.bees360.entity.ProjectImage;
import com.bees360.mapper.ProjectImageMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component("imageSecurity")
public class ImageSecurityImpl implements ImageSecurity {

    @Autowired
    private ProjectSecurity projectSecurity;

    @Autowired
    private ProjectImageMapper projectImageMapper;

    @Override
    public boolean isImageInAccessibleProject(HttpServletRequest request, Authentication authentication, String imageId) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_IN_ACCESSIBLE_PROJECT,
            () -> judgeImageInAccessibleProject(request, authentication, imageId));
    }

    private boolean judgeImageInAccessibleProject(HttpServletRequest request, Authentication authentication,
        String imageId) {
        ProjectImage image = projectImageMapper.getImageById(imageId);
        return image != null && projectSecurity.isProjectAccessible(request, authentication, image.getProjectId());
    }
}
