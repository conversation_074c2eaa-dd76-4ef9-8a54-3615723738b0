package com.bees360.config.properties;

import com.bees360.util.PropertiesUtils;
import com.google.common.base.CaseFormat;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.AbstractEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.Environment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.stream.StreamSupport;

/**
 * 将Spring中的properties加载到PropertiesUtil中。
 *
 * <AUTHOR>
 * @date 2019/12/15 00:26
 */
@Slf4j
@Component
public class PropertyLogger {

    @EventListener
    public void handleContextRefresh(ContextRefreshedEvent event) {
        final Environment env = event.getApplicationContext().getEnvironment();
        if (log.isDebugEnabled()) {
            log.debug("====== Environment and configuration ======");
            log.debug("Active profiles: {}", Arrays.toString(env.getActiveProfiles()));
        }
        final MutablePropertySources sources = ((AbstractEnvironment) env).getPropertySources();

        StreamSupport.stream(sources.spliterator(), false)
            .filter(ps -> ps instanceof EnumerablePropertySource)
            .map(ps -> ((EnumerablePropertySource) ps).getPropertyNames())
            .flatMap(Arrays::stream)
            .forEach(prop -> addProperty(prop, env.getProperty(prop)));
        if(log.isDebugEnabled()) {
            log.debug("===========================================");
        }
    }

    private void addProperty(String name, String value) {
        PropertiesUtils.addProperties(name, value);
        if(log.isDebugEnabled()) {
            log.debug("load property [{}] into PropertiesUtils", name);
        }
        CaseFormat[] caseFormats = {CaseFormat.LOWER_CAMEL, CaseFormat.LOWER_HYPHEN, CaseFormat.LOWER_UNDERSCORE};
        // 添加其他格式的key，提高容错性
        for(CaseFormat from: caseFormats) {
            for(CaseFormat to: caseFormats) {
                if(from.equals(to)) {
                    continue;
                }
                addPropertyWhenNotExist(from.to(to, name), value);
            }
        }
    }

    private void addPropertyWhenNotExist(String name, String value) {
        if(PropertiesUtils.containsKey(name)) {
            return;
        }
        PropertiesUtils.addProperties(name, value);

        if(log.isDebugEnabled()) {
            log.debug("load property [{}] into PropertiesUtils", name);
        }
    }
}
