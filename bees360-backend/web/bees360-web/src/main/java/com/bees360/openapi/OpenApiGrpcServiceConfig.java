package com.bees360.openapi;

import com.bees360.web.openapi.OpenApiReportManagerConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import({
    OpenApiReportManagerConfig.class,
})
@Configuration
public class OpenApiGrpcServiceConfig {

    @Bean
    GrpcOpenApiReportManagerService grpcOpenApiReportManagerService(OpenApiReportProvider openApiReportProvider) {
        return new GrpcOpenApiReportManagerService(openApiReportProvider);
    }
}
