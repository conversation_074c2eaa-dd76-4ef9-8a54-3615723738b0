package com.bees360.config;

import com.bees360.address.AddressProvider;
import com.bees360.event.UpdateProjectAddressOnAddressChanged;
import com.bees360.listener.SyncProjectToAiOnProjectAddressChangeEvent;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.ProjectService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "bees360.feature-switch", name = "enable-update-project-on-address-changed", havingValue = "true")
public class UpdateProjectAddressOnAddressChangedConfig {

    @Bean
    UpdateProjectAddressOnAddressChanged updateProjectAddressOnAddressChanged(
        ApplicationEventPublisher publisher,
        ProjectService projectService,
        AddressProvider addressProvider,
        ProjectMapper projectMapper) {
        return new UpdateProjectAddressOnAddressChanged(publisher, projectService, addressProvider, projectMapper);
    }

    @Bean
    SyncProjectToAiOnProjectAddressChangeEvent syncProjectToAiOnProjectAddressChangeEvent(ProjectService projectService) {
        return new SyncProjectToAiOnProjectAddressChangeEvent(projectService);
    }
}
