package com.bees360.controller;

import com.bees360.entity.dto.DataDictionary;
import com.bees360.entity.dto.DataDictionaryDto;
import com.bees360.entity.dto.DataDictionaryDtoList;
import com.bees360.service.DataDictionaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/8/18 5:05 下午
 **/
@RestController
@RequestMapping(value = "/config")
public class DataDictionaryController {

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @GetMapping(value = "/data-dictionary")
        public DataDictionaryDtoList getDataDictionaryList(@NotNull String namespace) {
        List<DataDictionaryDto> list = dataDictionaryService.listDataDictionary(namespace);
        DataDictionaryDtoList result = new DataDictionaryDtoList();
        result.setItems(list);
        return result;
    }

    @PostMapping(value = "/data-dictionary")
    public void putDataDictionary(@RequestBody @Valid DataDictionary dataDictionary) {
        dataDictionaryService.addDataDictionary(dataDictionary);
    }
}
