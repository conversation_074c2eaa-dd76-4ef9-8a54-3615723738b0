package com.bees360.listener;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.User;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.service.ProjectService;
import com.bees360.web.event.project.ProjectCreatedEvent;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.function.BiFunction;
import java.util.function.Function;

@Log4j2
public class UpdateInspectionNumberOnProjectCreated {

    private final ProjectService projectService;
    private final BiFunction<CreationChannelType, Project, String> inspectionNumberProvider;

    public UpdateInspectionNumberOnProjectCreated(
            @NonNull ProjectService projectService,
            @NonNull BiFunction<CreationChannelType, Project, String> inspectionNumberProvider) {
        this.projectService = projectService;
        this.inspectionNumberProvider = inspectionNumberProvider;
        log.info("Created {}(projectService={},inspectionNumberProvider={})",
            this,
            projectService,
            inspectionNumberProvider);
    }

    @TransactionalEventListener(fallbackExecution = true)
    public void updateInspectionNumberForSecurityFirst(ProjectCreatedEvent event) throws ServiceException {
        var projectId = event.getProject().getProjectId();
        var inspectionNumber = inspectionNumberProvider.apply(event.getCreationChannel(), event.getProject());
        if (inspectionNumber == null) {
            return;
        }
        var inspectionInfo = projectService.getInspectionInfo(projectId);
        inspectionInfo.setInspectionNumber(inspectionNumber);
        projectService.updateInspectionInfo(projectId, User.AI_ID, inspectionInfo);
    }
}
