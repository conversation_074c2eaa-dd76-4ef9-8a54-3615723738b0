package com.bees360.web.security.expression.impl;

import java.util.List;
import java.util.Objects;

import jakarta.servlet.http.HttpServletRequest;

import com.bees360.web.security.expression.ProjectSecurity;
import com.bees360.web.security.expression.ReportSecurity;
import com.bees360.web.security.SecurityAttributeUtil;
import com.bees360.web.security.expression.UserSecurity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectReportService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Yang
 */
@Slf4j
@Component("reportSecurity")
public class ReportSecurityImpl implements ReportSecurity {

    private static final String REQUEST_ATTRIBUTE_REPORT_FILE =
        "%s@REPORT_FILE".formatted(ReportSecurityImpl.class.toString());

    @Autowired
    private ProjectReportFileService projectReportFileService;

    @Autowired
    private ProjectReportService projectReportService;

    @Autowired
    private ProjectSecurity projectSecurity;

    @Autowired
    private UserSecurity userSecurity;

    @Override
    public boolean isReportInAccessibleProject(HttpServletRequest request, Authentication authentication,
        String reportId) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_IN_ACCESSIBLE_PROJECT,
            () -> judgeReportInAccessibleProject(request, authentication, reportId));
    }

    private boolean judgeReportInAccessibleProject(HttpServletRequest request, Authentication authentication,
        String reportId) {
        Long projectId = getProjectId(request, reportId);
        return projectId != null && projectSecurity.isProjectAccessible(request, authentication, projectId);
    }

    @Override
    public boolean isReportPaid(HttpServletRequest request, Authentication authentication, String reportId) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_PAID,
            () -> judgeReportPaid(request, authentication, reportId));
    }

    private boolean judgeReportPaid(HttpServletRequest request, Authentication authentication, String reportId) {
        Long projectId = getProjectId(request, reportId);
        List<ProjectReportFile> reportFiles = null;
        try {
            reportFiles = projectReportService.listPaidReport(projectId);
        } catch (ServiceException e) {
            throw new IllegalStateException(e);
        }
        return reportFiles.stream().anyMatch(r -> Objects.equals(r.getReportId(), reportId));
    }

    @Override
    public boolean isReportReady(HttpServletRequest request, Authentication authentication, String reportId) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_READY,
            () -> judgeReportReady(request, authentication, reportId));
    }

    private boolean judgeReportReady(HttpServletRequest request, Authentication authentication, String reportId) {
        ProjectReportFile reportFile = projectReportFileService.getById(reportId);
        return reportFile != null && reportFile.getGenerationStatus() == ReportGenerationStatusEnum.APPROVED.getCode();
    }

    @Override
    public boolean isReportListable(HttpServletRequest request, Authentication authentication, String reportId) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_LISTABLE,
            () -> judgeReportListable(request, authentication, reportId));
    }

    public boolean judgeReportListable(HttpServletRequest request, Authentication authentication, String reportId) {
        try {
            return isReportInAccessibleProject(request, authentication, reportId)
                && !isOnlyPilotOfProject(request, authentication, reportId) && isReportReady(request, authentication, reportId);
        } catch (ResourceNotFoundException e) {
            log.warn(e.getMessage(), e);
            return false;
        }
    }

    private boolean isOnlyPilotOfProject(HttpServletRequest request, Authentication authentication, String reportId)
        throws ResourceNotFoundException{
        Long projectId = getProjectId(request, reportId);
        if (projectId == null) {
            throw new ResourceNotFoundException("project of report `" + reportId + "` not found.");
        }
        return projectSecurity.isMemberEquals(request, authentication, projectId, RoleEnum.PILOT.name());
    }

    private ProjectReportFile getReportFile(HttpServletRequest request, String reportId) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRIBUTE_REPORT_FILE, () -> {
            return projectReportFileService.getById(reportId);
        });
    }

    private Long getProjectId(HttpServletRequest request, String reportId) {
        ProjectReportFile reportFile = getReportFile(request, reportId);
        return reportFile == null ? null : reportFile.getProjectId();
    }
}
