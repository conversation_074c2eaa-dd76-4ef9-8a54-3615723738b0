package com.bees360.config;

import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.project.report.UniqueProjectReportProvider;
import com.bees360.report.ReportGroupManager;
import com.bees360.report.ReportManager;
import com.bees360.report.config.GrpcReportGroupManagerConfig;
import com.bees360.report.config.GrpcReportManagerClientConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Import(
        value = {
            GrpcReportManagerClientConfig.class,
            GrpcReportGroupManagerConfig.class,
        })
@Configuration
public class ReportConfig {

    @Bean(name = {"projectReportManager", "projectReportProvider"})
    public ProjectReportManager projectReportManager(ReportGroupManager reportGroupManager, ReportManager reportManager) {
        return new DefaultProjectReportManager(reportGroupManager, reportManager);
    }

    @Bean(name = "uniqueProjectReportProvider")
    public UniqueProjectReportProvider uniqueProjectReportProvider(ProjectReportManager projectReportManager) {
        return new UniqueProjectReportProvider(projectReportManager);
    }
}
