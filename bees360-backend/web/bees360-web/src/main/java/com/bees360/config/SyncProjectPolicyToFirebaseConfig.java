package com.bees360.config;

import com.bees360.event.SyncProjectToFirebaseOnPolicyUpdatedEvent;
import com.bees360.service.firebase.FirebaseService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(
    value = "bees360.feature-switch.enabled-sync-firebase-policy-type-on-policy-updated",
    havingValue = "true")
public class SyncProjectPolicyToFirebaseConfig {

    @Bean
    SyncProjectToFirebaseOnPolicyUpdatedEvent syncProjectToFirebaseOnPolicyUpdatedEvent(FirebaseService firebaseService) {
        return new SyncProjectToFirebaseOnPolicyUpdatedEvent(firebaseService);
    }
}
