package com.bees360.web.hover;

import jakarta.servlet.http.HttpServletResponse;

import com.bees360.commons.hover.entity.HoverAuthorization;
import com.bees360.commons.hover.service.AuthorizationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/hover")
public class HoverAuthorizationEndpoint {


    @Autowired
    private AuthorizationService authorizationService;

    @Value("${hover.redirect-url}")
    private String redirectUrl;

    /**
     * 由hover平台调用，生成token保存到系统中
     */
    @GetMapping("/token")
    public void retrieveToken(@RequestParam String code, HttpServletResponse response) {

        authorizationService.retrieveToken(code);
        response.setStatus(HttpServletResponse.SC_MOVED_PERMANENTLY);
        response.setHeader("Location", redirectUrl);

    }

    @PutMapping("/token")
    public void refreshToken() {
        authorizationService.refreshToken();
    }
}
