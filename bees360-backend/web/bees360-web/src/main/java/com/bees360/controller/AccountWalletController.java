package com.bees360.controller;

import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bees360.base.MapBuilder;
import com.bees360.config.support.CurUserId;
import com.bees360.entity.vo.WalletGlobalDiscount;
import com.bees360.service.AccountWalletService;

@RestController
@RequestMapping("/account/wallet")
public class AccountWalletController {

	@Inject
	private AccountWalletService accountWalletService;

	@GetMapping("/discount")
	public Map<String, Object> getWalletDiscount(@CurUserId long userId) throws Exception {
		List<WalletGlobalDiscount> discounts = accountWalletService.listWalletDiscount(userId);
		return MapBuilder.result("globalDiscounts", discounts);
	}

	@GetMapping("/balance")
	public Map<String, Object> getBalance(@CurUserId long userId) throws Exception {
		double balance = accountWalletService.getWalletBalance(userId);
		return MapBuilder.result("balance", balance);
	}
}
