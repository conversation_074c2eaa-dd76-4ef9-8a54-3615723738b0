package com.bees360.openapi;

import com.bees360.entity.openapi.OpenReportSummaryVo;
import com.bees360.entity.openapi.OpenReportVo;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.service.openapi.OpenReportService;
import com.bees360.service.util.AWSLambdaSummaryConverterConfig;
import com.bees360.web.openapi.OpenApiReportManagerConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.spi.json.JacksonJsonNodeJsonProvider;
import com.jayway.jsonpath.spi.mapper.JacksonMappingProvider;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.context.annotation.Import;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.function.BinaryOperator;

@Log4j2
@Import({
    ProtoHttpMessageConverterConfig.class,
})
@RestController
@RequestMapping("/v1/report/{reportId}/summary")
public class OldOpenReportSummaryEndpoint {

    private static final Gson gson = new Gson();

    private static final com.jayway.jsonpath.Configuration configuration = com.jayway.jsonpath.Configuration.builder()
        .jsonProvider(new JacksonJsonNodeJsonProvider())
        .mappingProvider(new JacksonMappingProvider())
        .build();

    private final BinaryOperator<String> lambdaReportSummaryConverter;

    private final OpenReportService openReportService;

    private final ObjectMapper objectMapper;

    public OldOpenReportSummaryEndpoint(
            BinaryOperator<String> lambdaReportSummaryConverter,
            OpenReportService openReportService,
            ObjectMapper objectMapper) {
        this.lambdaReportSummaryConverter = lambdaReportSummaryConverter;
        this.openReportService = openReportService;
        this.objectMapper = objectMapper;
        log.info("Created {}(lambdaReportSummaryConverter={},openReportService={})", this, lambdaReportSummaryConverter, openReportService);
    }

    @GetMapping("")
    public OpenReportVo.OpenReportListWrapperVo getReportSummary(
        @PathVariable String reportId, @RequestParam(required = false) Boolean transform)
        throws Exception {
        OpenReportSummaryVo summaryVo = openReportService.getReportSummary(reportId);

        // 非定制化请求直接返回
        if (Boolean.FALSE.equals(transform)) {
            return new OpenReportVo.OpenReportListWrapperVo(summaryVo);
        }

        // 定制化请求调用lambda对summary进行处理后,替换原本的summary后返回
        var transformedReportSummary =
            lambdaReportSummaryConverter.apply(
                gson.toJson(summaryVo.getSummary()),
                String.valueOf(summaryVo.getProject().getId()));
        var json = objectMapper.writeValueAsString(summaryVo);
        var newSummary =
            JsonPath.using(configuration)
                .parse(formatLambdaSummaryJson(transformedReportSummary))
                .json();
        var newSummaryVo =
            JsonPath.using(configuration).parse(json).set("$.summary", newSummary).json();
        return new OpenReportVo.OpenReportListWrapperVo(newSummaryVo);
    }

    private String formatLambdaSummaryJson(String summary) {
        return StringEscapeUtils.unescapeCsv(summary.replaceAll("\n", ""));
    }

}
