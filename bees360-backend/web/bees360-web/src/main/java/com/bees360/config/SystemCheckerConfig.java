package com.bees360.config;

import com.bees360.config.checker.BasicChecker;
import com.bees360.config.checker.CheckerProcessor;
import com.bees360.config.checker.CompanyChecker;
import com.bees360.config.checker.ConfigureChecker;
import com.bees360.config.checker.MessageCodeChecker;
import com.bees360.config.checker.ServiceFeeTypeChecker;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 该Configurer用于配置系统初始化之后所需要要执行的checker
 *
 * <AUTHOR>
 * @date 2020/01/17 11:55
 */
@Configuration
public class SystemCheckerConfig {

    @Bean
    public CheckerProcessor checkerProcessor(List<BasicChecker> basicCheckers) {
        CheckerProcessor checkerProcessor = new CheckerProcessor();
        checkerProcessor.setCheckers(basicCheckers);
        return checkerProcessor;
    }
}
