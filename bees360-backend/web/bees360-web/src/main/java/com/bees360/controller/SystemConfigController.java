package com.bees360.controller;

import com.bees360.entity.SystemConfig;
import com.bees360.entity.dto.systemconfig.EmailRecipientConfig;
import com.bees360.entity.dto.systemconfig.SystemConfigProjectDto;
import com.bees360.entity.dto.systemconfig.SystemConfigProjectServiceChangeDto;
import com.bees360.service.SystemConfigService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/23 19:17
 */
@Validated
@RestController
@RequestMapping("/management/system-config")
public class SystemConfigController {

    @Inject
    private SystemConfigService systemConfigService;

    @GetMapping("")
    public List<SystemConfig> listWithPrefix(String prefix) {
        return systemConfigService.listWithPrefix(prefix);
    }

    @GetMapping("/project")
    public SystemConfigProjectDto getSystemConfigProject() {
        return systemConfigService.getSystemConfigProject();
    }

    @PutMapping("/project")
    public SystemConfigProjectDto updateSystemConfigProject(@RequestBody SystemConfigProjectDto systemConfigProjectDto) {
        return systemConfigService.updateSystemConfigProject(systemConfigProjectDto);
    }

    @GetMapping("/project-change")
    public SystemConfigProjectServiceChangeDto getSystemConfigProjectServiceChange() {
        return systemConfigService.getSystemConfigProjectServiceChange().orElse(null);
    }

    @PutMapping("/project-change")
    public SystemConfigProjectServiceChangeDto updateSystemConfigProject(
        @RequestBody @Valid SystemConfigProjectServiceChangeDto serviceChangeDto) {
        return systemConfigService.upsertSystemConfigProjectServiceChange(serviceChangeDto);
    }

    @PostMapping("/email-recipients")
    public void updateSystemConfig(@RequestBody EmailRecipientConfig recipientConfig) {
        final SystemConfig config =
            new SystemConfig(recipientConfig.prefix() + recipientConfig.getEmailType(), recipientConfig.getRecipients());
        systemConfigService.upsertSystemConfig(config);
    }
}
