package com.bees360.scheduletask.project;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class ExportProjectForSwyfftNewProcessor {

    public static List<ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData> exportToExcels(List<Project> projects,
        List<BsExportData> dataList) {
        List<ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData> excelDatas = new ArrayList<>();

        JsonParser jsonParser = new JsonParser();
        final String VERSION = "version";
        // 映射为 version - BsExportData List
        Map<String, List<BsExportData>> versionDataList = dataList.stream().collect(Collectors.groupingBy(ed -> {
            JsonObject jsonObject = jsonParser.parse(ed.getDataLog()).getAsJsonObject();
            if (!jsonObject.has(VERSION)) {
                return "";
            }
            return jsonObject.get(VERSION).getAsString();
        }));
        if (versionDataList.isEmpty()) {
            return excelDatas;
        }
        for (Map.Entry<String, List<BsExportData>> entry : versionDataList.entrySet()) {
            ExportProjectForSwyfftUtilNew utilNew = null;
            try {
                utilNew = ExportProjectForSwyfftUtilNewFactory.newInstance(entry.getKey());
            } catch (Exception ex) {
                List<String> relatedIds =
                    entry.getValue().stream().map(d -> d.getRelatedId()).collect(Collectors.toList());
                log.error("fail to export data for projects ({}) with relatedType ({})", relatedIds,
                    dataList.get(0).getRelatedType(), ex);
                continue;
            }
            List<BsExportData> value = entry.getValue();
            ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData excelData =
                utilNew.fetchExcelData(filterProjectsForExportData(projects, value), value);
            excelDatas.add(excelData);
        }
        return excelDatas;
    }

    private static List<Project> filterProjectsForExportData(List<Project> projects, List<BsExportData> dataList) {
        if (projects.size() == dataList.size()) {
            return projects;
        }
        Set<String> projectIds = dataList.stream().map(d -> d.getRelatedId()).collect(Collectors.toSet());
        return projects.stream().filter(p -> projectIds.contains(p.getProjectId() + "")).collect(Collectors.toList());
    }

}
