package com.bees360.web.security;

import com.bees360.web.security.util.AuthorityUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;

import java.util.Enumeration;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
public class SecurityAttributeUtil {

    private SecurityAttributeUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static boolean isAttributeSet(HttpServletRequest request, String attrName) {
        if (attrName == null) {
            return false;
        }
        Enumeration<String> enumeration = request.getAttributeNames();
        while (enumeration.hasMoreElements()) {
            String name = enumeration.nextElement();
            if (attrName.equals(name)) {
                return true;
            }
        }
        return false;
    }

    public static Long getUserId(Authentication authentication) {
        if (authentication == null || authentication.getPrincipal() == null) {
            log.debug("Authentication is null or no 'principal' in authentication: {}.", authentication);
            return null;
        }
        if (authentication.getPrincipal() instanceof co.realms9.bifrost.User user) {
            return Long.parseLong(user.getId());
        }
        if (authentication.getPrincipal() instanceof com.bees360.user.User user) {
            return Long.parseLong(user.getId());
        }
        log.warn("The 'principal' in authentication({}) is {} and not a instance of User Class.", authentication.getClass(), authentication.getPrincipal().getClass());
        return null;
    }

    public static <T> T getAndSetAttribute(HttpServletRequest request, String attrName, Supplier<T> supplier) {
        if(request == null) {
            return supplier.get();
        }
        if (SecurityAttributeUtil.isAttributeSet(request, attrName)) {
            return (T)request.getAttribute(attrName);
        }
        T result = supplier.get();
        request.setAttribute(attrName, result);
        return result;
    }

    public static String toRoleInSecurity(String role) {
        return AuthorityUtil.toRoleInSecurity(role);
    }
}
