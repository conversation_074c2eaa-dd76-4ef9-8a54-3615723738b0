package com.bees360.controller;

import com.bees360.entity.ProjectImage;
import com.bees360.mapper.ProjectImageMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Profile({"local", "stag"})
@RestController
@RequestMapping("/temp")
public class TempCopyImagesController {

    @Autowired
    private ProjectImageMapper projectImageMapper;

    @GetMapping("copy-images")
    public void copyImages(@RequestParam long from, @RequestParam long to) {
        List<ProjectImage> projectImages = projectImageMapper.listAll(from).stream().peek(projectImage -> {
            projectImage.setImageId(UUID.randomUUID().toString());
            projectImage.setProjectId(to);
        }).collect(Collectors.toList());
        projectImageMapper.insertBaseInfoList(projectImages);
    }
}
