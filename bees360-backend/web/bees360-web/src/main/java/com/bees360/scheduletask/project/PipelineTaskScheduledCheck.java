package com.bees360.scheduletask.project;

import com.bees360.entity.query.ProjectFilterQuery;
import com.bees360.mapper.ProjectMapper;
import com.bees360.pipeline.PipelineService;
import com.bees360.pipeline.PipelineTask;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.util.Iterables;
import com.bees360.util.schedule.annotation.DistributedScheduled;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bees360.entity.enums.PipelineTaskEnum.CHECK_IN;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_DRONE_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_EXTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_HOVER_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_INTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_MAGICPLAN_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_PLNAR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.UPLOAD_DRONE_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.UPLOAD_EXTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.UPLOAD_INTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.VERIFY_ADDRESS;
import static com.bees360.entity.enums.PipelineTaskEnum.WAIT_HOVER_COMPLETE;
import static com.bees360.entity.enums.PipelineTaskEnum.WAIT_MAGICPLAN_COMPLETE;
import static com.bees360.entity.enums.PipelineTaskEnum.WAIT_PLNAR_COMPLETE;
import static com.bees360.pipeline.Message.PipelineStatus.DONE;
import static com.bees360.pipeline.Message.PipelineStatus.ERROR;
import static com.bees360.pipeline.Message.PipelineStatus.IGNORED;
import static com.bees360.pipeline.Message.PipelineStatus.ONGOING;

@Component
@Log4j2
@DistributedScheduled
public class PipelineTaskScheduledCheck {
    @Autowired private ProjectMapper projectMapper;
    @Autowired private PipelineService pipelineService;

    /** 每10分钟执行一次, 查询检测时间为这10分钟内项目有没有准时到场检查。 */
    @Scheduled(
        initialDelayString = "${scheduler.spring-scheduled.start-up-delay:-}",
        fixedRate = 600000)
    void checkClaimCaseInspectInOnTime() {
        Instant endTime = Instant.now();
        var startTime = endTime.minus(10, ChronoUnit.MINUTES);
        var query =
                ProjectFilterQuery.builder()
                        .projectServiceType(
                                // 只查询claim的case
                                Set.of(
                                        ServiceTypeEnum.FULL_ADJUSTMENT.getCode(),
                                        ServiceTypeEnum.QUICK_INSPECT.getCode(),
                                        ServiceTypeEnum.POST_CONSTRUCTION_AUDIT.getCode()))
                        .inspectionStartTime(startTime.toEpochMilli())
                        .inspectionEndTime(endTime.toEpochMilli());
        List<Long> projectIds = projectMapper.listProjectId(query.build());
        for (Long projectId : projectIds) {
            String pipelineId = String.valueOf(projectId);
            try {
                log.info("Start to check project '{}' mission check in on time.", projectId);
                setTaskErrorIfOngoing(pipelineId, CHECK_IN.getKey());
                log.info("Successfully check project '{}' mission check in on time.", projectId);
            } catch (RuntimeException e) {
                log.error(
                        "Failed to check project '{}' mission task check in on time.",
                        projectId,
                        e);
            }
        }
    }

    /** 每10分钟执行一次, 查询检测时间已过1天的任务完成情况。 */
    @Scheduled(
        initialDelayString = "${scheduler.spring-scheduled.start-up-delay:-}",
        fixedRate = 600000)
    void checkClaimCaseTaskFinishedOnTime() {
        var endTime = Instant.now().minus(1, ChronoUnit.DAYS);
        var startTime = endTime.minus(10, ChronoUnit.MINUTES);
        var query =
                ProjectFilterQuery.builder()
                        .projectServiceType(
                                // 只查询claim的case
                                Set.of(
                                        ServiceTypeEnum.FULL_ADJUSTMENT.getCode(),
                                        ServiceTypeEnum.QUICK_INSPECT.getCode(),
                                        ServiceTypeEnum.POST_CONSTRUCTION_AUDIT.getCode()))
                        .inspectionStartTime(startTime.toEpochMilli())
                        .inspectionEndTime(endTime.toEpochMilli());
        List<Long> projectIds = projectMapper.listProjectId(query.build());
        for (Long projectId : projectIds) {
            String pipelineId = String.valueOf(projectId);
            try {
                log.info("Start to check project '{}' mission task finished on time.", projectId);
                setTaskErrorIfTaskNotFinished(pipelineId);
                log.info(
                        "Successfully check project '{}' mission task finished on time.",
                        projectId);
            } catch (RuntimeException e) {
                log.error(
                        "Failed to check project '{}' mission task finished on time.",
                        projectId,
                        e);
            }
        }
    }

    private void setTaskErrorIfTaskNotFinished(String pipelineId) {
        var pipeline = pipelineService.findById(pipelineId);
        if (pipeline == null) {
            return;
        }

        var taskStatus =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        // 是否完成check in, 如果没有check in,则跳过
        var checkedInFinished = isCheckedInFinished(taskStatus);
        if (!checkedInFinished) {
            return;
        }

        var uploadedExteriorImage = taskStatus.get(UPLOAD_EXTERIOR_IMAGES.getKey());
        if (isNotFinished(uploadedExteriorImage)) {
            setTaskErrorIfOngoing(pipelineId, VERIFY_ADDRESS.getKey(), taskStatus);
            setTaskErrorIfOngoing(pipelineId, TAKE_EXTERIOR_IMAGES.getKey(), taskStatus);
            setTaskErrorIfOngoing(pipelineId, UPLOAD_EXTERIOR_IMAGES.getKey(), taskStatus);
        }

        var uploadedInteriorImage = taskStatus.get(UPLOAD_INTERIOR_IMAGES.getKey());
        if (isNotFinished(uploadedInteriorImage)) {
            setTaskErrorIfOngoing(pipelineId, TAKE_INTERIOR_IMAGES.getKey(), taskStatus);
            setTaskErrorIfOngoing(pipelineId, UPLOAD_INTERIOR_IMAGES.getKey(), taskStatus);
        }

        var uploadedDroneImage = taskStatus.get(UPLOAD_DRONE_IMAGES.getKey());
        if (isNotFinished(uploadedDroneImage)) {
            setTaskErrorIfOngoing(pipelineId, TAKE_DRONE_IMAGES.getKey(), taskStatus);
            setTaskErrorIfOngoing(pipelineId, UPLOAD_DRONE_IMAGES.getKey(), taskStatus);
        }

        var waitHoverComplete = taskStatus.get(WAIT_HOVER_COMPLETE.getKey());
        if (isNotFinished(waitHoverComplete)) {
            setTaskErrorIfOngoing(pipelineId, TAKE_HOVER_IMAGES.getKey(), taskStatus);
        }

        var waitPlnarComplete = taskStatus.get(WAIT_PLNAR_COMPLETE.getKey());
        if (isNotFinished(waitPlnarComplete)) {
            setTaskErrorIfOngoing(pipelineId, TAKE_PLNAR_IMAGES.getKey(), taskStatus);
        }

        var waitMagicplan = taskStatus.get(WAIT_MAGICPLAN_COMPLETE.getKey());
        if (isNotFinished(waitMagicplan)) {
            setTaskErrorIfOngoing(pipelineId, TAKE_MAGICPLAN_IMAGES.getKey(), taskStatus);
        }
    }

    /**
     * check is the task is not finished.
     *
     * @param task task
     * @return return true only if the task is not finished, otherwise false.
     */
    private boolean isNotFinished(PipelineTask task) {
        if (task == null) {
            return false;
        }
        var status = task.getStatus();
        return !Objects.equals(status, DONE) && !Objects.equals(status, IGNORED);
    }

    /**
     * check if the {@link com.bees360.entity.enums.PipelineTaskEnum#CHECK_IN} task is finished.
     *
     * @param taskStatus pipeline task status
     * @return return true only if the task is already finished, otherwise false.
     */
    private boolean isCheckedInFinished(Map<String, ? extends PipelineTask> taskStatus) {
        var key = CHECK_IN.getKey();
        if (!taskStatus.containsKey(key)) {
            return false;
        }
        var status = taskStatus.get(key).getStatus();
        return Objects.equals(status, DONE) || Objects.equals(status, IGNORED);
    }

    private void setTaskErrorIfOngoing(String pipelineId, String key) {
        var pipeline = pipelineService.findById(pipelineId);
        if (pipeline == null) {
            return;
        }

        var taskStatus =
                Iterables.toStream(pipeline.getTask())
                        .collect(Collectors.toMap(PipelineTask::getKey, Function.identity()));
        setTaskErrorIfOngoing(pipelineId, key, taskStatus);
    }

    private void setTaskErrorIfOngoing(
            String pipelineId, String key, Map<String, ? extends PipelineTask> taskStatus) {
        if (!taskStatus.containsKey(key)
                || !Objects.equals(taskStatus.get(key).getStatus(), ONGOING)) {
            return;
        }

        try {
            pipelineService.setTaskStatus(pipelineId, key, ERROR);
        } catch (RuntimeException e) {
            log.error("Failed to set pipeline '{}' task '{}' to ERROR.", pipelineId, key);
        }
    }
}
