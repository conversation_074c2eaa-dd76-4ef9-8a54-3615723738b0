package com.bees360.scheduletask.sandbox.sample;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Company;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.bees360.mapper.ProjectImageMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public abstract class ClassPathJsonImageSampleProvider implements SandboxSampleProvider {

    private List<ProjectImage> imageSamples;

    public ClassPathJsonImageSampleProvider() throws ServiceException {
        imageSamples = loadJsonImages(getJsonPath());
    }

    /**
     * 获取 json 文件描述的 image sample
     *
     * @return
     */
    public List<ProjectImage> getImageSamples(){

        return imageSamples.stream().map(image ->{
            final ProjectImage projectImage = new ProjectImage();
            BeanUtils.copyProperties(image, projectImage);
            return projectImage;
        }).collect(Collectors.toList());

    }

    /**
     * 描述 image 的 json 文件路径
     *
     * @return
     */
    protected abstract String getJsonPath();

    private List<ProjectImage> loadJsonImages(String jsonPath) throws ServiceException {
        try(final InputStream input = this.getClass().getClassLoader().getResourceAsStream(jsonPath)) {
            if (input == null){
                throw new FileNotFoundException("file %s not found.".formatted(jsonPath));
            }
            final Gson gson = new Gson();
            final Type type = new TypeToken<List<ProjectImage>>() {}.getType();
            return gson.fromJson(new InputStreamReader(input, StandardCharsets.UTF_8), type);
        } catch (IOException e) {
            log.error("load json image error, json path: {}", jsonPath, e);
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public SandboxSample getSampleResource(Company company, Project project) {
        final SandboxSample sandboxResource = new SandboxSample();
        sandboxResource.setImageArchive(getImageArchive());
        sandboxResource.setReport(getReports());
        sandboxResource.setProjectImages(getImages(project.getProjectId()));
        sandboxResource.setReportSummaries(getReportSummaries());

        return sandboxResource;
    }

    protected abstract SandboxSampleResource.FileResource getImageArchive();

    protected abstract List<SandboxSampleResource.ReportResource> getReports();

    protected abstract List<ProjectImage> getImages(long projectId);

    protected abstract Map<ReportTypeEnum, ReportSummaryVo> getReportSummaries();
}
