package com.bees360.controller;

import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.entity.dto.ProjectStatistics;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.service.statistics.ProjectStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.bees360.entity.dto.ProjectStatistics.ProjectStatsPerCompanySummary;
import static com.bees360.entity.vo.ProjectDashboardVo.AnnualStatistics;

@RestController
@RequestMapping("/projects/dashboard")
public class ProjectDashBoardController {

    private final static ZoneId US_CENTRAL_ZONEID = ZoneId.of(AmericaTimeZone.US_CENTRAL);
    @Autowired private ProjectStatisticsService projectStatisticsService;

    @GetMapping("/perCompanySummary")
    public Map<String, Object>
    getPerCompanyStatsSummary(@RequestParam long startDate, @RequestParam long endDate) {
        final Instant startInstant = Instant.ofEpochMilli(startDate);
        final Instant endInstant = Instant.ofEpochMilli(endDate);
        final Map<InspectionPurposeTypeEnum, ProjectStatsPerCompanySummary> summary =
            projectStatisticsService.projectStatsSummary(startInstant, endInstant);

        return Map.of("startDate", startDate,
            "endDate", endDate,
            "claimSummary", summary.get(InspectionPurposeTypeEnum.CLAIM),
            "underwritingSummary", summary.get(InspectionPurposeTypeEnum.UNDERWRITING));
    }


    @GetMapping("/annualSummary")
    public List<AnnualStatistics> getAnnualSummary() {

        final LocalDate today = LocalDate.now(US_CENTRAL_ZONEID);
        final DayOfWeek dayOfWeek = today.getDayOfWeek();
        LocalDate startOfWeek = today.minusDays(dayOfWeek.getValue() - 1);
        LocalDate startOf53Week = startOfWeek.minusWeeks(53);
        final List<Map<InspectionPurposeTypeEnum, ProjectStatistics.ProjectStatisticSummary>> summaries =
            projectStatisticsService.getIntervalStatisticsSummary(startOf53Week, today,
                ZoneId.of(AmericaTimeZone.US_CENTRAL), 7);
        return summaries.stream().map(map -> {
            final ProjectStatistics.ProjectStatisticSummary claim = map.get(InspectionPurposeTypeEnum.CLAIM);
            final ProjectStatistics.ProjectStatisticSummary underwriting = map.get(InspectionPurposeTypeEnum.UNDERWRITING);
            return new AnnualStatistics(claim.getStartDate(),  claim.getEndDate(),
                claim.getTotalProjectCreated(),
                underwriting.getTotalProjectCreated());
        }).collect(Collectors.toList());
    }

    @GetMapping("/statusMetrics")
    public Map<String, Object> getProjectStatusMetrics() {
        final Map<InspectionPurposeTypeEnum, ProjectStatistics.ProjectLatestStatusMetrics> metricsMap =
            projectStatisticsService.getLatestProjectStatusMetrics();
        final ProjectStatistics.ProjectLatestStatusMetrics claims = metricsMap.get(InspectionPurposeTypeEnum.CLAIM);
        final ProjectStatistics.ProjectLatestStatusMetrics underwriting = metricsMap.get(InspectionPurposeTypeEnum.UNDERWRITING);
        return Map.of("claims", claims, "underwriting", underwriting);
    }
}
