package com.bees360.config.database;

import org.apache.ibatis.executor.ErrorContext;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * 将 MyBatis 的异常进行捕获输出，以方便调试
 *
 * <AUTHOR>
 * @date 2019/10/11 15:03
 */
public class NewSqlSessionFactoryBean extends SqlSessionFactoryBean {

    private Logger logger = LoggerFactory.getLogger(NewSqlSessionFactoryBean.class);

    @Override
    protected SqlSessionFactory buildSqlSessionFactory() throws Exception {
        try {
            return super.buildSqlSessionFactory();
        } catch (IOException e) {
            // 将 MyBatis 的异常进行捕获输出，以方便调试
            logger.error(e.getMessage(), e);
            throw e;
        } finally {
            ErrorContext.instance().reset();
        }
    }
}
