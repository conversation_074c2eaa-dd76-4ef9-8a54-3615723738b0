package com.bees360.controller;

import com.bees360.base.MapBuilder;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectMessage;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.entity.label.ProjectLabel;
import com.bees360.entity.label.ProjectLabelDto;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectMessageService;
import com.bees360.service.ProjectService;
import com.bees360.service.firebase.FirebasePilotService;
import com.bees360.service.firebase.FirebaseProjectService;
import com.bees360.service.grpc.GrpcProjectGenericService;
import jakarta.inject.Inject;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/temp")
public class TempController {

    @Inject
    private MessageSource messageSource;

    @Autowired
    private ProjectLabelService projectLabelService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private GrpcProjectGenericService grpcProjectGenericService;
    @Autowired
    private ProjectMessageService projectMessageService;
    @Autowired
    private FirebasePilotService firebasePilotService;
    @Autowired
    private FirebaseProjectService firebaseProjectService;

    @PostMapping("addActivity")
    public Map<String, Object> addActivity(@RequestBody Map<String, List<ProjectMessage>> map) {
        return MapBuilder.result("syncProjects", projectMessageService.addActivity(map.get("!@#qwert")));
    }

    @Deprecated
    @PostMapping("syncCatNumberToFirebase")
    public void syncCatNumberToFirebase(@RequestBody Map<String, List<Project>> map) {
        firebaseProjectService.syncCatNumberToFirebase(map.get("record"));
    }

    @Deprecated
    @PostMapping("syncProjectStatusToFirebase")
    public void syncProjectStatusToFirebase(String startEcho, String endEcho) {
        firebaseProjectService.recoverProjectStatusToFirebase(
            ZonedDateTime.parse(startEcho).toInstant(),
            ZonedDateTime.parse(endEcho).toInstant());
    }

    @Deprecated
    @PostMapping("recoverProjectLatestStatus")
    public void recoverProjectLatestStatus() {
        firebaseProjectService.recoverProjectLatestStatus();
    }

    @PostMapping("recoverProjectTimeline")
    @Deprecated
    private void recoverProjectTimeline(Long projectIdStart, Long projectIdEnd) {
        firebaseProjectService.recoverTimeline(projectIdStart, projectIdEnd);
    }

    @PostMapping("syncProjectToFirebase")
    private void syncToFirebase(@RequestBody List<Long> projectIds) throws ServiceException {
        for (Long projectId : projectIds) {
            firebaseProjectService.updateFirebaseProjectBaseInfo(projectId);
        }
    }

    @PostMapping("/sync-tag")
    public void syncProjectTags() {

        long end = System.currentTimeMillis();
        long start = end - ChronoUnit.DAYS.getDuration().multipliedBy(30).toMillis();

        List<Project> projects = projectService.listInTimeRange(start, end);
        if (CollectionUtils.isEmpty(projects)) {
            return;
        }
        List<Long> projectIds = projects.stream().map(Project::getProjectId).collect(Collectors.toList());
        List<BoundProjectLabel> boundProjectLabels = projectLabelService.projectLabelList(projectIds);
        if (CollectionUtils.isEmpty(boundProjectLabels)) {
            return;
        }
        List<ProjectLabelDto> dtos = new ArrayList<>();
        for (BoundProjectLabel boundProjectLabel : boundProjectLabels) {
            List<ProjectLabel> projectLabels = boundProjectLabel.getProjectLabels();
            if (CollectionUtils.isEmpty(projectLabels)) {
                continue;
            }
            List<Long> projectLabelIds = projectLabels.stream().map(ProjectLabel::getLabelId).collect(Collectors.toList());
            dtos.add(new ProjectLabelDto(boundProjectLabel.getProjectId(), projectLabelIds));
        }
        grpcProjectGenericService.updateProjectTagsOnWebChange(dtos);

    }
}
