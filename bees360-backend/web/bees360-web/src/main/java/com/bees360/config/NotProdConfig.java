package com.bees360.config;

import com.bees360.entity.CompanyIDMap;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.listener.UpdateInspectionNumberOnProjectCreated;
import com.bees360.service.ProjectService;
import com.bees360.web.core.constant.SpringProfiles;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Configuration
@Profile("!prod")
public class NotProdConfig {

    @Configuration
    @Profile({SpringProfiles.STAG, SpringProfiles.QA})
    public static class ProjectConfig {

        @Bean
        UpdateInspectionNumberOnProjectCreated updateInspectionNumberOnProjectCreated(
                ProjectService projectService,
                CompanyIDMap companyIDMap) {
            final Set<Long> targetCompanyIds = Stream.of(companyIDMap.getSecurity_First_Florida())
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            return new UpdateInspectionNumberOnProjectCreated(projectService, (creationChannelType, project) -> {
                var needProcess = CreationChannelType.OPENAPI.equals(creationChannelType)
                    && StringUtils.isEmpty(project.getInspectionNumber())
                    && Stream.of(project.getInsuranceCompany(), project.getRepairCompany()).anyMatch(targetCompanyIds::contains);
                if (needProcess) {
                    return "report_provided_" + System.currentTimeMillis();
                }
                // ignore update
                return null;
            });
        }
    }
}
