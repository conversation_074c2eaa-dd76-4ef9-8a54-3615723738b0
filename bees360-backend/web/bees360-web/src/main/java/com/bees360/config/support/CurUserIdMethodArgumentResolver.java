package com.bees360.config.support;

import jakarta.inject.Inject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import com.bees360.service.ContextProvider;

/**
 * 解析Controller方法中{@code @CurUserId}注解的long/Long类型的参数，将认证授权中得到的userId进行注入。
 * <AUTHOR>
 */
@Component
public class CurUserIdMethodArgumentResolver implements HandlerMethodArgumentResolver {

	@Autowired
	private ContextProvider springSecurityContextProvider;

	@Override
	public boolean supportsParameter(MethodParameter parameter) {
		 if (parameter.hasParameterAnnotation(CurUserId.class) && supportsParameterType(parameter.getParameterType())) {
	            return true;
	        }
	        return false;
	}

	private boolean supportsParameterType(Class<?> parameterType) {

	    Class<?>[] supportAnyTypes = {long.class, Long.class};

	    for(Class<?> supportType: supportAnyTypes) {
	        if(supportType.isAssignableFrom(parameterType)) {
	            return true;
            }
        }
	    return false;
    }

	@Override
	public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
			NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
		return springSecurityContextProvider.getUserIdFromContext();
	}
}
