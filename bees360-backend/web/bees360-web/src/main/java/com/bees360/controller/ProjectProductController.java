package com.bees360.controller;

import java.util.Map;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bees360.base.MapBuilder;
import com.bees360.entity.vo.ProductAssemble;
import com.bees360.service.ProjectProductService;

@RestController
@RequestMapping("/projects/{projectId:\\d+}/products")
public class ProjectProductController {

	@Inject
	private ProjectProductService projectProductService;

	@GetMapping("/assemble")
	public Map<String, Object> getAssembleProducts(@PathVariable long projectId) throws Exception{
		List<ProductAssemble> products = projectProductService.getProductAssemble(projectId);
		return MapBuilder.result("products", products);
	}
}
