package com.bees360.scheduletask;

import com.bees360.schedule.support.SchedulerProviderManager;
import com.bees360.scheduletask.project.ExportProjectSummeryForSwyfftUnderwritingJob;
import com.bees360.web.core.constant.DateTimeConst;
import lombok.extern.slf4j.Slf4j;
import org.quartz.CronScheduleBuilder;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class QuartzJobRegister {

    @Autowired
    private SchedulerProviderManager schedulerProviderManager;

    @Transactional(rollbackFor = SchedulerException.class)
    @PostConstruct
    public void unscheduleSwyfftProjectDailySummary() throws SchedulerException {
        Class<? extends Job> jobClass = ExportProjectSummeryForSwyfftUnderwritingJob.class;
        Scheduler scheduler = schedulerProviderManager.getProvider(jobClass).getScheduler();
        String jobId = "2972a305-1dc7-48b1-b7e4-3a032cc82a8c";
        JobKey jobKey = new JobKey(jobId, jobClass.getSimpleName());
        List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
        if(!triggers.isEmpty()) {
            scheduler.deleteJob(jobKey);
        }
    }
}
