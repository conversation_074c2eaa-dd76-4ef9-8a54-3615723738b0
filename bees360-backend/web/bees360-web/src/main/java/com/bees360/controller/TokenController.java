package com.bees360.controller;

import java.util.Map;

import jakarta.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bees360.commons.firebasesupport.service.RemoteConfigService;

@RestController
@RequestMapping("/tokens")
public class TokenController {

    @Resource
    RemoteConfigService remoteConfigService;

    @GetMapping("/remote-config")
    public Map<String, String> getChecklistUrl() throws Exception {
        remoteConfigService.refreshRemoteConfig();
        return RemoteConfigService.remoteConfigMap;
    }
}
