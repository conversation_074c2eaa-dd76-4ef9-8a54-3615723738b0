package com.bees360.web.security.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import com.bees360.entity.RolePermission;
import com.bees360.entity.enums.RoleEnum;

public class AuthorityUtil {

	private final static String ROLE_PREFIX = "ROLE_";

    public static List<GrantedAuthority> mapToGrantedAuthorities(List<String> roleDisplays, Map<RoleEnum, List<RolePermission>> rolePermissionMap) {
    	List<GrantedAuthority> disinctGrantedAuthoritys = new ArrayList<GrantedAuthority>();
    	if(roleDisplays == null && rolePermissionMap == null) {
    		return disinctGrantedAuthoritys;
    	}

    	//role symbols
    	List<GrantedAuthority> grantedAuthoritys =  new ArrayList<GrantedAuthority>();
    	List<GrantedAuthority> authoritys = roleDisplays.stream().map(roleDisplay -> new SimpleGrantedAuthority(roleDisplay))
    										.collect(Collectors.toList());
    	grantedAuthoritys.addAll(authoritys);

    	//authority symbols
    	if(rolePermissionMap != null && rolePermissionMap.size() > 0) {
	    	for(RoleEnum roleEnum : rolePermissionMap.keySet()) {
	    		List<RolePermission> rolePermissions = rolePermissionMap.get(roleEnum);
	    		//?How to design permission OP_OPERATION_RESOURCE
	    		List<GrantedAuthority> tempAuthoritys = rolePermissions.stream()
			            .map(rolePermission -> new SimpleGrantedAuthority(rolePermission.getPermission().getPermissionName()))
			            .collect(Collectors.toList());
	    		grantedAuthoritys.addAll(tempAuthoritys);
	    	}
    	}
    	disinctGrantedAuthoritys = grantedAuthoritys.stream().filter(distinctByKey(GrantedAuthority::getAuthority)).collect(Collectors.toList());
    	return disinctGrantedAuthoritys;
    }

    /**
     * remove duplicate elements
     * @param keyExtractor
     * @return
     */
    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        final Set<Object> seen = new HashSet<>();
        return t -> seen.add(keyExtractor.apply(t));
    }

    public static Collection<? extends GrantedAuthority> toRoleAuthorities(List<RoleEnum> roles) {
    	Collection<? extends GrantedAuthority> authorities = roles.stream()
				.map(role -> new SimpleGrantedAuthority(toRoleInSecurity(role)))
				.collect(Collectors.toList());

    	return authorities;
    }

    public static String toRoleInSecurity(RoleEnum role) {
    	return ROLE_PREFIX + role.name();
    }

    public static String toRoleInSecurity(String role) {
    	return ROLE_PREFIX + role;
    }

    public static void main(String[] args) {
		List<String> alpha = Arrays.asList("a","b","c","d");
		List<String> collect = alpha.stream().map(String::toUpperCase).collect(Collectors.toList());
		System.out.println(collect);
	}
}
