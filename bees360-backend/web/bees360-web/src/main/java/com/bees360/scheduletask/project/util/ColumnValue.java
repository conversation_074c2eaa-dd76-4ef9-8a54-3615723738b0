package com.bees360.scheduletask.project.util;

import java.util.function.Consumer;
import java.util.function.Function;

/**
 * Excel中一列的标题和值获取方法的定义。
 * <AUTHOR>
 */
public class ColumnValue<T> {

    private String keyName;
    private Class<T> typeClass;
    private Function<T, Object> columnValue;

    public ColumnValue(Class<T> typeClass, String keyName, Function<T, Object> columnValue) {
        this.typeClass = typeClass;
        this.keyName = keyName;
        this.columnValue = columnValue;
    }

    public String getKeyName() {
        return keyName;
    }

    public Object getValue(Object entry) {
        Object[] result = new Object[1];
        if (!ifSupport(entry, (value) -> result[0] = value)) {
            Class<?> clazz = entry == null? null: entry.getClass();
            throw new IllegalArgumentException(clazz + " is not supported.");
        }
        return result[0];
    }

    /**
     * 如果支持，则会执行传入的Consumer。
     *
     * @param entry 本次处理的实体对象
     * @param valueConsumer 本次
     */
    public boolean ifSupport(Object entry, Consumer<Object> valueConsumer) {
        if (isSupport(entry)) {
            valueConsumer.accept(columnValue == null? null: columnValue.apply((T) entry));
            return true;
        }
        return false;
    }

    /**
     * 能否处理该实体对象
     */
    public boolean isSupport(Object entry) {
        return typeClass.isInstance(entry);
    }
}
