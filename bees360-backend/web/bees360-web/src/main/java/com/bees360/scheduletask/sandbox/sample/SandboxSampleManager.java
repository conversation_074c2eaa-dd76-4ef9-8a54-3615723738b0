package com.bees360.scheduletask.sandbox.sample;

import com.bees360.entity.Company;
import com.bees360.entity.Project;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;
import org.springframework.stereotype.Component;

import java.util.List;

@Log4j2
@Component
public class SandboxSampleManager implements InitializingBean {

    private final List<SandboxSampleProvider> providers;

    public SandboxSampleManager(List<SandboxSampleProvider> providers) {
        this.providers = providers;
        log.info("Created {}(providers={})", this, providers);
    }

    public List<SandboxSampleProvider> getProviders(){
        return providers;
    }

    public SandboxSample getSampleResource(Company company, Project project){

        final SandboxSampleProvider provider = providers.stream()
            .filter(p -> p.supports(company)).findFirst().orElse(null);
        log.info("provider {} found for company {}", provider, company.getCompanyId());
        return provider == null ? null : provider.getSampleResource(company, project);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        checkState();

        AnnotationAwareOrderComparator.sort(providers);
    }

    private void checkState() {
        if (providers.isEmpty()) {
            throw new IllegalArgumentException(
                "a list of SandboxSampleProviders is required");
        }
    }
}
