package com.bees360.config.properties;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

@Data
@Validated
@Component
@ConfigurationProperties(prefix = "quartz")
public class QuartzSchedulerProperties {

    @Valid
    private SchedulerInstances scheduler;

    @Data
    public static class SchedulerInstances {

        @Valid
        private SchedulerInstance compressReport;
    }

    @Data
    public static class SchedulerInstance {

        @NotBlank
        private String name;

        @Positive
        private int threadCount;
    }
}
