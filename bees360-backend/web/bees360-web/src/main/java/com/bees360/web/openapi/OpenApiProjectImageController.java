package com.bees360.web.openapi;

import com.bees360.entity.openapi.OpenImageVo;
import com.bees360.entity.openapi.OpenProjectVo;
import com.bees360.service.openapi.OpenApiProjectImageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/project")
public class OpenApiProjectImageController {

    @Autowired
    private OpenApiProjectImageService openApiProjectImageService;

    @GetMapping("/{projectId:\\d+}/image")
    public OpenProjectVo.OpenProjectVoListWrapper listImage(@PathVariable long projectId) throws Exception{
        final List<OpenImageVo> openImageVos = openApiProjectImageService.listProjectImage(projectId);

        return new OpenProjectVo.OpenProjectVoListWrapper<>(new OpenProjectVo.OpenProjectImageVo(projectId, openImageVos));
    }
}
