package com.bees360.controller;

import com.bees360.entity.vo.LogInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * 拓展功能
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/extension")
public class ExtensionController {

    /**
     * 提供给前端将日志输出的日志系统
     */
    @PostMapping("/log")
    public void log(@RequestBody @Valid LogInfo logInfo) {
        logInfo.getTag().log(log, "source: {}, log: {}", logInfo.getSource(), logInfo.getLog());
    }
}
