package com.bees360.scheduletask.sandbox.sample;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.bees360.scheduletask.sandbox.sample.SandboxSampleResource.FileResource;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SandboxSample {

    private List<ProjectImage> projectImages;
    private FileResource imageArchive;
    private List<SandboxSampleResource.ReportResource> report;
    private Map<ReportTypeEnum, ReportSummaryVo> reportSummaries;
}
