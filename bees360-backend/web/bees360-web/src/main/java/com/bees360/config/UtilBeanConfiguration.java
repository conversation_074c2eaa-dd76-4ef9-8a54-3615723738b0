package com.bees360.config;

import com.bees360.common.file.ArchiveUtils;
import com.bees360.common.file.ArchiveUtilsFactory;
import com.bees360.common.grpc.GrpcConfig;
import com.bees360.common.grpc.GrpcHeaderInterceptor;
import com.bees360.common.httpclient.HttpClientConfig;
import com.bees360.entity.dto.SystemBillingSetting;
import com.bees360.service.grpc.AiGrpcClient;
import com.bees360.util.maps.googlemaps.GoogleMapUtilBuilder;
import com.bees360.util.msgutil.SmsTemplateFormat;
import com.bees360.util.payment.StripePaymentSettings;
import com.bees360.web.core.properties.bean.Bees360BillingProperties;
import com.bees360.web.core.properties.bean.Bees360Properties;
import com.bees360.web.core.properties.bean.GoogleMapProperties;
import com.bees360.web.core.properties.bean.PaymentStripeProperties;
import com.google.maps.GeoApiContext;
import io.grpc.ClientInterceptor;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> Yang
 */
@Slf4j
@Configuration
public class UtilBeanConfiguration {
    @Bean
    public StripePaymentSettings stripePaymentSettings(PaymentStripeProperties paymentStripeProperties) {
        StripePaymentSettings stripePaymentSettings = new StripePaymentSettings();
        BeanUtils.copyProperties(paymentStripeProperties, stripePaymentSettings);
        return stripePaymentSettings;
    }

	@Bean
	public SmsTemplateFormat smsTemplateFormat() {
		return new SmsTemplateFormat("message/sms.properties");
	}

	@Bean
	public ArchiveUtilsFactory archiveUtilsFactory() {
		return new ArchiveUtilsFactory();
	}

	@Bean
	public ArchiveUtils zipUtils() {
		return archiveUtilsFactory().createArchiveUtils(ArchiveUtilsFactory.ARCHIVE_ZIP);
	}

	@Bean
	public SystemBillingSetting systemBillingSetting(Bees360Properties bees360Properties) {
        Bees360BillingProperties bees360BillingProperties = bees360Properties.getBilling();
		SystemBillingSetting systemBillingSetting = new SystemBillingSetting();
		systemBillingSetting.setDefaultInvoiceDescription(bees360BillingProperties.getInvoice().getDescription());

		SystemBillingSetting.SystemBillingRecipient recipient = new SystemBillingSetting.SystemBillingRecipient();
		BeanUtils.copyProperties(bees360BillingProperties.getRecipient(), recipient);
		systemBillingSetting.setSystemBillingRecipient(recipient);

		return systemBillingSetting;
	}

	@Bean
	public ExecutorService imagesArchiveExecutor() {
		return Executors.newFixedThreadPool(1, new ThreadFactory() {
			@Override
			public Thread newThread(Runnable r) {
				Thread thread = new Thread(r);
				thread.setName("ProjectImagesArchiveThread");
				return thread;
			}
		});
	}

	@Bean
	public GoogleMapUtilBuilder googleMapUtilBuilder(GeoApiContext context) throws Exception {
        // Initialization failed, an exception is thrown, and the program is prevented from starting
		return new GoogleMapUtilBuilder(context).init();
	}

    @Bean
    public GeoApiContext geoApiContext(GoogleMapProperties googleMapProperties) throws Exception {

        final int CONNECT_TIMEOUT_SECONDS = 3;
        final int READ_TIMEOUT = 3;
        final int MAX_RETRIES = 3;

        String apiKey = googleMapProperties.getAccess().getApiKey();
        boolean proxy = googleMapProperties.getAccess().isProxy();
        // Initialization failed, an exception is thrown, and the program is prevented from starting
        return createContext(apiKey, CONNECT_TIMEOUT_SECONDS, READ_TIMEOUT, MAX_RETRIES, proxy);
    }

    private GeoApiContext createContext(String apiKey, int connectTimeoutSeconds, int readTimeout,
            int maxRetries, boolean needProxy)
        throws Exception {
        try {
            GeoApiContext.Builder builder = new GeoApiContext.Builder();
            if(needProxy) {
                builder.proxy(new Proxy(Proxy.Type.HTTP,new InetSocketAddress(8123)));
            }
            return builder.apiKey(apiKey)
                .connectTimeout(connectTimeoutSeconds, TimeUnit.SECONDS)
                .readTimeout(readTimeout, TimeUnit.SECONDS)
                .maxRetries(maxRetries)
                .build();
        } catch(Exception e) {
            throw new Exception("GoogleMapService GeoApiContext initialized failed", e);
        }
    }

	@Bean
    @ConfigurationProperties(prefix = "bees360.grpc.servers.ai")
    public GrpcConfig aiGrpcConfig() {
        return new GrpcConfig();
    }

    // bees360-ai grpc service's name and port
    @Bean
    public List<AiGrpcClient> aiGrpcClients(GrpcConfig aiGrpcConfig) throws Exception {
        assert aiGrpcConfig != null;
        List<AiGrpcClient> aiGrpcClientList = new ArrayList<>();

        String endpoints = aiGrpcConfig.getEndpoints();
        //If you want to send requests to multiple endpoints at the same time (separated by semicolons), each endpoint can be a load balancing group
        if(endpoints.indexOf(";") > 0) {
            String[] endpointArray = endpoints.split(";");
            for(String endpoint : endpointArray) {
                setGrpcConfig(aiGrpcConfig, endpoint);
                AiGrpcClient aiGrpcClient = new AiGrpcClient(aiGrpcConfig);
                aiGrpcClientList.add(aiGrpcClient);
            }
        } else {
            AiGrpcClient aiGrpcClient = new AiGrpcClient(aiGrpcConfig);
            aiGrpcClientList.add(aiGrpcClient);
        }
        return aiGrpcClientList;
    }

    private void setGrpcConfig(GrpcConfig grpcConfig, String endpointConf) throws Exception{
        if(StringUtils.isBlank(endpointConf)) {
            return;
        }
        String[] temps = endpointConf.split(":");
        if(temps.length < 2) {
            throw new Exception("grpc server config must contain name and port");
        }
        grpcConfig.setEndpoints(endpointConf);
    }

    @Bean
    public ClientInterceptor grpcHeaderInterceptor() {
        return new GrpcHeaderInterceptor();
    }

    @Bean
    @ConfigurationProperties(prefix = "http.client")
    public HttpClientConfig httpClientConfig() {
        return new HttpClientConfig();
    }
}
