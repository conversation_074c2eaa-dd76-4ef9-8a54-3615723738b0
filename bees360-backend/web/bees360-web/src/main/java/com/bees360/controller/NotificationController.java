package com.bees360.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import com.bees360.config.support.CurUserId;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.bees360.base.MessageCode;
import com.bees360.base.ResponseJson;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Notification;
import com.bees360.entity.vo.PageResult;
import com.bees360.service.ContextProvider;
import com.bees360.service.NotificationService;
import com.bees360.service.UserService;

@Controller
@RequestMapping("/notifications")
public class NotificationController {
	@Inject
	private MessageSource messageSource;

	@Inject
	private ContextProvider springSecurityContextProvider;

	@Inject
	private NotificationService notificationService;

	@Inject
	private UserService userService;

	@GetMapping("")
	@ResponseBody
	public PageResult<Notification> listAllNotification(@CurUserId long userId, @RequestParam int pageIndex,
		@RequestParam int pageSize) throws Exception {
		PageResult<Notification> notificationResultPage
			= notificationService.listNotifications(userId, pageIndex, pageSize);
		return notificationResultPage;
	}

	@DeleteMapping("/{notificationId:\\d+}")
	@ResponseBody
	public ResponseJson setOneNotificationRead(@PathVariable long notificationId){
		ResponseJson json = new ResponseJson();
		long userId = springSecurityContextProvider.getUserIdFromContext();
		try {
			notificationService.setOneNotificationRead(userId, notificationId);
		} catch (ServiceException e) {
			String messageCode = e.getMsgCode();
			json = new ResponseJson(messageCode, MessageCode.getMessage(messageSource, messageCode));
		}
		return json;
	}

	@DeleteMapping("")
	@ResponseBody
	public ResponseJson setAllNotificationRead(){
		ResponseJson json = new ResponseJson();
		long userId = springSecurityContextProvider.getUserIdFromContext();
		try {
			notificationService.setAllNotificationRead(userId);
		} catch (ServiceException e) {
			String messageCode = e.getMsgCode();
			json = new ResponseJson(messageCode, MessageCode.getMessage(messageSource, messageCode));
		}
		return json;
	}
}
