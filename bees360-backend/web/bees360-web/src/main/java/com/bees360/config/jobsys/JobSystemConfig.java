package com.bees360.config.jobsys;

import com.bees360.atomic.AtomicIntegerProvider;
import com.bees360.atomic.AtomicLongToIntAdapter;
import com.bees360.atomic.RedisAtomicLongProvider;
import com.bees360.event.EventDispatcher;
import com.bees360.event.EventPublisher;
import com.bees360.job.RabbitJobDispatcher;
import com.bees360.job.RabbitJobScheduler;
import com.bees360.rabbit.RabbitApi;
import com.bees360.rabbit.config.RabbitApiConfig;
import com.bees360.redis.config.RedissonConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({
    RedissonConfig.class,
    RedisAtomicLongProvider.class,
    AtomicLongToIntAdapter.class,
    RabbitApiConfig.class
})
public class JobSystemConfig {

    @Bean
    public RabbitJobDispatcher rabbitJobDispatcher(
        Rabbit<PERSON>pi rabbitApiForJobDispatcher,
        EventPublisher eventPublisher,
        EventDispatcher eventDispatcher,
        AtomicIntegerProvider retryCountProvider) {
        return new RabbitJobDispatcher(
            rabbitApiForJobDispatcher, eventPublisher, eventDispatcher, retryCountProvider);
    }

    @Bean
    public RabbitJobScheduler rabbitJobScheduler(
        RabbitApi rabbitApiForJobScheduler,
        EventPublisher eventPublisher,
        EventDispatcher eventDispatcher) {
        return new RabbitJobScheduler(rabbitApiForJobScheduler, eventPublisher, eventDispatcher);
    }
}
