package com.bees360.web.security.expression;

import jakarta.servlet.http.HttpServletRequest;

import com.bees360.web.security.SecurityAttributeUtil;
import org.springframework.security.core.Authentication;

/**
 * <AUTHOR>
 */
public interface ImageSecurity {

    String REQUEST_ATTRI_IN_ACCESSIBLE_PROJECT =
        "%s@IN_ACCESSIBLE_PROJECT".formatted(ImageSecurity.class.toString());
    String REQUEST_ATTRI_LISTABLE = "%s@LISTABLE".formatted(ImageSecurity.class.toString());

    /**
     * 用户可以访问报告所在项目
     */
    boolean isImageInAccessibleProject(HttpServletRequest request, Authentication authentication, String imageId);

    /**
     * 图片列表可以被获取。用户必须能够先访问该图片所在项目，然后才能获取该图片信息。
     */
    default boolean isImageListable(HttpServletRequest request, Authentication authentication, String imageId) {
        return SecurityAttributeUtil.getAndSetAttribute(request, REQUEST_ATTRI_LISTABLE,
            () -> isImageInAccessibleProject(request, authentication, imageId));
    }
}
