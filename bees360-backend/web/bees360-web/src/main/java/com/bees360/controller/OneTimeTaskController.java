package com.bees360.controller;

import com.bees360.entity.User;
import com.bees360.entity.dto.ProjectSearchOption;
import com.bees360.entity.vo.ProjectPageResultVo;
import com.bees360.entity.vo.ProjectTinyVo;
import com.bees360.policy.PolicyManager;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectIIManager;
import com.bees360.service.ProjectService;
import com.bees360.web.project.util.ProjectConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/** 发布时的一次性任务 发布完成后可以删除 */
@Slf4j
@RestController
@PreAuthorize("hasRole('ROLE_ADMIN')")
@RequestMapping("/temp/task")
public class OneTimeTaskController {

    @Autowired private ProjectService projectService;
    @Autowired private PolicyManager policyManager;
    @Autowired private ProjectIIManager projectIIManager;
    @Autowired private ContactManager siProjectContactManager;

    @Autowired
    @Qualifier("contactManager")
    private ContactManager contactManager;

    @Deprecated
    @PostMapping("/sync/contact-data")
    public void syncContactData(
            @AuthenticationPrincipal com.bees360.user.User user, Long projectId) {
        new Thread(
                        () -> {
                            try {
                                Long userId = Long.parseLong(user.getId());
                                var searchOption = new ProjectSearchOption();
                                searchOption.setPageSize(100);
                                searchOption.setPageIndex(1);
                                if (projectId != null) {
                                    searchOption.setSearchProjectId(projectId);
                                }

                                ProjectPageResultVo vo =
                                        projectService.pageTinyProjectsWithSearch(
                                                userId, searchOption);
                                var totalPage = vo.getPage().getTotalPage();
                                int index = 1;
                                while (index <= totalPage) {
                                    vo =
                                            projectService.pageTinyProjectsWithSearch(
                                                    userId, searchOption);
                                    vo.getProjects().forEach(this::syncContact);
                                    searchOption = new ProjectSearchOption();
                                    searchOption.setPageSize(100);
                                    searchOption.setPageIndex(++index);
                                }
                            } catch (Exception exception) {
                                log.info("contact data sync failed ", exception);
                            }
                        })
                .start();
    }

    @Deprecated
    @PostMapping("/sync/address-data")
    public void syncAddressData(
            @AuthenticationPrincipal com.bees360.user.User user, Long projectId) {
        new Thread(
                        () -> {
                            try {
                                Long userId = Long.parseLong(user.getId());
                                var searchOption = new ProjectSearchOption();
                                searchOption.setPageSize(100);
                                searchOption.setPageIndex(1);
                                if (projectId != null) {
                                    searchOption.setSearchProjectId(projectId);
                                }

                                ProjectPageResultVo vo =
                                        projectService.pageTinyProjectsWithSearch(
                                                userId, searchOption);
                                var totalPage = vo.getPage().getTotalPage();
                                int index = 1;
                                while (index <= totalPage) {
                                    vo =
                                            projectService.pageTinyProjectsWithSearch(
                                                    userId, searchOption);
                                    vo.getProjects().forEach(this::syncAddressId);
                                    searchOption = new ProjectSearchOption();
                                    searchOption.setPageSize(100);
                                    searchOption.setPageIndex(++index);
                                }
                            } catch (Exception exception) {
                                log.info("contact address sync failed ", exception);
                            }
                        })
                .start();
    }

    private void syncContact(ProjectTinyVo e) {
        try {
            log.info("sync scan contact to postgresql projectId {}", e.getProjectId());
            var contacts =
                    IterableUtils.toList(
                            siProjectContactManager.findByProjectId(
                                    String.valueOf(e.getProjectId())));
            var roleList =
                    List.of(
                            ProjectConstants.ContactRoleType.EXTERNAL_ADJUSTER,
                            ProjectConstants.ContactRoleType.CONTACT_AGENT,
                            ProjectConstants.ContactRoleType.CONTACT_INSURED);
            var existList =
                    IterableUtils.toList(
                            contactManager.findByProjectId(String.valueOf(e.getProjectId())));
            var addContact =
                    contacts.stream()
                            .filter(item -> roleList.contains(item.getRole()))
                            .filter(
                                    item ->
                                            existList.stream()
                                                    .noneMatch(
                                                            f ->
                                                                    Objects.equals(
                                                                            item.getRole(),
                                                                            f.getRole())))
                            .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(addContact)) {
                return;
            }
            addContact.forEach(
                    item ->
                            siProjectContactManager.addContact(
                                    String.valueOf(e.getProjectId()),
                                    item,
                                    String.valueOf(User.AI_ID)));
            log.info(
                    "sync contact to postgresql succeed projectId {} {}",
                    e.getProjectId(),
                    addContact);
        } catch (Exception exception) {
            log.info("sync contact to postgresql failed projectId {}", e.getProjectId(), exception);
        }
    }

    private void syncAddressId(ProjectTinyVo e) {
        try {
            log.info("sync scan address to postgresql projectId {}", e.getProjectId());
            if (e.getAddressId() == null) {
                return;
            }
            var projectII = projectIIManager.findById(String.valueOf(e.getProjectId()));
            if (projectII == null) {
                return;
            }
            var policy = policyManager.findById(projectII.getPolicy().getId());
            if (!Objects.equals(e.getAddressId(), policy.getAddress().getId())) {
                policyManager.updateAddressId(policy.getId(), e.getAddressId());
                log.info(
                        "sync address id to postgresql succeed projectId {} from {} to {}",
                        e.getProjectId(),
                        policy.getAddress().getId(),
                        e.getAddressId());
            }
        } catch (Exception exception) {
            log.info(
                    "sync address id to postgresql failed projectId {}",
                    e.getProjectId(),
                    exception);
        }
    }
}
