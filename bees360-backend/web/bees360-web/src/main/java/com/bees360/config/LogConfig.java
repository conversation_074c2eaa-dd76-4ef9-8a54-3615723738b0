package com.bees360.config;

import com.bees360.service.ContextProvider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.zalando.logbook.HeaderFilter;
import org.zalando.logbook.core.HeaderFilters;
import org.zalando.logbook.autoconfigure.LogbookProperties;

import java.util.Set;
import java.util.TreeSet;

@Configuration
public class LogConfig {

    @Configuration
    public static class LogbookConfig {

        @Bean
        public HeaderFilter headerFilter(LogbookProperties properties, ContextProvider springSecurityContextProvider) {
            var authorizationHeaderFilter = HeaderFilters.eachHeader((key, value) -> {
                if (!StringUtils.equalsIgnoreCase(key, "Authorization")) {
                    return value;
                }
                var userId = springSecurityContextProvider.getUserIdFromContext();
                 return userId + "";
           });

            final Set<String> headers = new TreeSet<>(String.CASE_INSENSITIVE_ORDER);
            headers.addAll(properties.getObfuscate().getHeaders());

            if (headers.isEmpty()) {
                return authorizationHeaderFilter;
            }
            return HeaderFilter.merge(
                authorizationHeaderFilter,
                HeaderFilters.replaceHeaders(headers, "xxx"));
        }
    }
}
