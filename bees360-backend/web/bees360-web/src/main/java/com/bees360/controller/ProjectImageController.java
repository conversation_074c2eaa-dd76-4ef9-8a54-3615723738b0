package com.bees360.controller;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.config.support.CurUserId;
import com.bees360.entity.dto.ProjectImageSearchOptionDto;
import com.bees360.entity.dto.ProjectImageUploadResponseDto;
import com.bees360.entity.dto.ProjectImagesListDto;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.vo.ImageIdsVo;
import com.bees360.entity.vo.ProjectImagePageResult;
import com.bees360.service.ProjectImageService;
import java.util.List;
import java.util.Map;
import jakarta.inject.Inject;
import jakarta.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@RequestMapping("/projects/{projectId:\\d+}/images")
public class ProjectImageController {

	@Inject
	private ProjectImageService projectImageService;

	@SuppressWarnings("unchecked")
	@PatchMapping("/existence")
	public void recoverDeletedImages(@PathVariable long projectId,
			@RequestBody Map<String, Object> map) throws Exception {
		List<String> imageIds;
		Boolean deleted;
		try {
			imageIds = (List<String>) map.get("imageIds");
			deleted = (Boolean)map.get("deleted");
		} catch (Exception e) {
			throw new ServiceException(MessageCode.PARAM_INVALID);
		}
		if(deleted == null) {
			throw new ServiceException(MessageCode.PARAM_INVALID);
		}
		projectImageService.updateImageDeletedStatus(projectId, imageIds, deleted);
	}

    @GetMapping("")
    public ProjectImagePageResult listDroneImageThumbnails(@PathVariable long projectId,
        ProjectImageSearchOptionDto queryParameter) throws Exception {
        queryParameter.setProjectId(projectId);
        return projectImageService.listTinyImagesWithPage(queryParameter);
    }

	@PostMapping("")
	public ProjectImageUploadResponseDto uploadImages(@CurUserId long userId, @PathVariable long projectId,
			@RequestBody ProjectImagesListDto projectImageListDto) throws Exception {

		FileSourceTypeEnum fileSourceType = null;
		if(projectImageListDto.getFileSourceType() != null) {
			fileSourceType = FileSourceTypeEnum.getEnum(projectImageListDto.getFileSourceType());
		}

		return projectImageService
            .uploadImages(projectId, userId, fileSourceType, projectImageListDto.getImages(), projectImageListDto.isUploaded());
	}

	@DeleteMapping("/{imageId}")
	public void deleteOneImage(@CurUserId long userId, @PathVariable long projectId,
			@PathVariable String imageId) throws Exception {
		projectImageService.deleteById(imageId,projectId, userId);
	}

    @DeleteMapping("/delete-completely")
    public void deleteCompletely(
            @PathVariable long projectId, @CurUserId long userId, @RequestBody @Valid ImageIdsVo imageIds)
            throws ServiceException {
        projectImageService.deleteCompletely(projectId, userId, imageIds);
    }
}
