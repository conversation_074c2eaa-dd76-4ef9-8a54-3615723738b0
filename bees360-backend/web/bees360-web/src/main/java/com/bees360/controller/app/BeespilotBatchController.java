package com.bees360.controller.app;

import com.bees360.base.exception.ServiceException;
import com.bees360.config.support.CurUserId;
import com.bees360.entity.BeesBatchQuery;
import com.bees360.entity.BeesPilotBatchItemVo;
import com.bees360.entity.BeesPilotBatchRequest;
import com.bees360.entity.SchedulePilotDto;
import com.bees360.entity.enums.BatchStatusResult;
import com.bees360.service.beespilot.BeesPilotBatchItemService;
import com.bees360.service.beespilot.BeesPilotBatchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/pilot/batch")
public class BeespilotBatchController {

    @Autowired
    private BeesPilotBatchService beesPilotBatchService;

    @Autowired
    private BeesPilotBatchItemService beesPilotBatchItemService;

    /**
     * get batchItemInfo by projectId
     *
     * @return category list
     */
    @GetMapping(value = "/projects/{projectId:\\d+}/batch-task")
    public BeesPilotBatchItemVo getPilotBatchTaskItemInfo(@PathVariable long projectId) throws ServiceException {
        return beesPilotBatchItemService.findByProjectId(projectId);
    }

    /**
     * 指定飞手是否有pending状态batch
     *
     * @param query
     * @return
     */
    @GetMapping(value = "/projects/pending-batch")
    public BatchStatusResult pendingBatch(@Valid BeesBatchQuery query) throws ServiceException{
        return beesPilotBatchService.getBatchStatusResource(query);
    }

    @GetMapping(value = "/projects/daily")
    public void dailySendPilotTaskEmail()
        throws ServiceException {
        beesPilotBatchService.dailySendPilotTaskEmail();
    }
    @GetMapping(value = "/projects/week")
    public void weekSendPilotTaskNeedPayedEmailToAdmin(String testDate)
        throws ServiceException {
        LocalDate curDate = LocalDate.parse(testDate);
        //上周六
        LocalDate startDate = curDate.minusDays(4);
        //这周五
        LocalDate endDate = curDate.plusDays(2);
        beesPilotBatchService.weekSendPilotTaskNeedPayedEmailToAdmin(curDate, startDate, endDate);
    }
}
