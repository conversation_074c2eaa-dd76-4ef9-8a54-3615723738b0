package com.bees360.scheduletask.project;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.bees360.common.excel.easyexcel.DateTimeZoneStringConverter;
import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;
import com.bees360.web.core.constant.DateTimeConst;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import lombok.extern.slf4j.Slf4j;

import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@Slf4j
public class ExportProjectForSwyfftUtilV1 implements ExportProjectForSwyfftUtil {

    private final static String EXPORT_EXCEL_TEMPLATE = "export/project/export_template_Swyfft_UW_form.xlsx";
    private final static ZoneId TIME_ZONE_ID = ZoneId.of(DateTimeConst.DEFAULT_TIME_ZONE_ID);
    private final static String SHEET_NAME = "To Swyfft";
    private final Gson gson = createGsonForV1();

    @Override
    public ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData fetchExcelData(List<Project> projects,
        List<BsExportData> bsExportDataList) {
        List<ExportProjectForSwyfft> datas = fetchDataV1(projects, bsExportDataList);
        ByteArrayOutputStream byteOutputStream = null;
        try {
            if (datas.isEmpty()) {
                return new ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData(null, 0);
            }
            byteOutputStream = exportExcel(datas);
            return new ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData(byteOutputStream.toByteArray(),
                datas.size());
        } catch (Exception e) {
            String message = "Fail to export data to excel".formatted();
            throw new RuntimeException(message, e);
        } finally {
            if (byteOutputStream != null) {
                try {
                    byteOutputStream.close();
                } catch (IOException e) {
                    // do nothing
                }
            }
        }
    }

    private List<ExportProjectForSwyfft> fetchDataV1(List<Project> projects, List<BsExportData> data) {
        if (data.isEmpty()) {
            return new ArrayList<>();
        }
        List<ExportProjectForSwyfft> summaries = assembleData(projects, data);
        return summaries;
    }

    private List<ExportProjectForSwyfft> assembleData(List<Project> projects, List<BsExportData> exportDataList) {
        Map<String, Project> projectMap =
            projects.stream().collect(Collectors.toMap(p -> p.getProjectId() + "", p -> p));
        List<ExportProjectForSwyfft> projectDatas = new ArrayList<>(projects.size());

        for (BsExportData exportData : exportDataList) {
            Project project = projectMap.get(exportData.getRelatedId());

            ExportProjectForSwyfft projectData = parseDataLog(gson, exportData.getDataLog());
            projectData.setProjectId(project.getProjectId());
            projectData.setInspectionNumber(project.getInspectionNumber());
            projectData.setAssetOwnerName(project.getAssetOwnerName());
            projectData.setYearBuilt(project.getYearBuilt());
            projectData.setClaimNote(project.getClaimNote());
            Date inspectionDate = project.getInspectionTime() == null ? null : new Date(project.getInspectionTime());
            projectData.setInspectionTime(inspectionDate);

            projectDatas.add(projectData);
        }
        return projectDatas;
    }

    private Gson createGsonForV1() {
        // 因为需要格式化的json的每个字段基本以大写字母开头，部分特殊的通过 @SerializedName 进行定义
        return new GsonBuilder().setFieldNamingStrategy(f -> {
            char[] cs = f.getName().toCharArray();
            cs[0] -= 32;
            return String.valueOf(cs);
        }).setPrettyPrinting().create();
    }

    private ExportProjectForSwyfft parseDataLog(Gson gson, String dataLog) {
        if (ObjectUtils.isEmpty(dataLog)) {
            return null;
        } else {
            try {
                return gson.fromJson(dataLog, ExportProjectForSwyfft.class);
            } catch (Exception e) {
                // 数据格式有问题，返回null，表示不存在数据
                log.error("Data Formatter Error: {}", e.getMessage(), e);
                return null;
            }
        }
    }

    private ByteArrayOutputStream exportExcel(List<ExportProjectForSwyfft> datas) throws IOException {
        ByteArrayOutputStream byteOutputStream = new ByteArrayOutputStream();
        try (InputStream template = this.getClass().getClassLoader().getResourceAsStream(EXPORT_EXCEL_TEMPLATE)) {
            // 设置默认导出时区
            ExcelWriter excelWriter =
                EasyExcel.write(byteOutputStream, ExportProjectForSwyfft.class).withTemplate(template)
                    .registerConverter(new DateTimeZoneStringConverter(TIME_ZONE_ID.getId())).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().sheetName(SHEET_NAME).build();
            // 强制换行，该设置会比较耗内存
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(datas, fillConfig, writeSheet);
            excelWriter.finish();
        }

        return byteOutputStream;
    }

}
