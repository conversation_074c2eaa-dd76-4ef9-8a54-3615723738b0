package com.bees360.web.security;

import com.bees360.auth.CustomTokenReader;
import com.bees360.auth.config.JwtResourceServerConfig;
import com.bees360.service.ContextProvider;
import com.bees360.util.password.PasswordUtil;
import java.util.Optional;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@Import({
    JwtResourceServerConfig.class,
    CustomTokenReader.class,
})
public class SecurityConfig {

    @Bean
    public ContextProvider contextProvider() {
        return new ContextProvider() {
            @Override
            public long getUserIdFromContext() {
                Long userId = SecurityAttributeUtil.getUserId(SecurityContextHolder.getContext().getAuthentication());
                return Optional.ofNullable(userId).orElse(0L);
            }
        };
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new PasswordEncoder() {
            @Override
            public String encode(CharSequence rawPassword) {
                return PasswordUtil.encrypt(String.valueOf(rawPassword));
            }

            @Override
            public boolean matches(CharSequence rawPassword, String encodedPassword) {
                return PasswordUtil.isValidate(String.valueOf(rawPassword), encodedPassword);
            }
        };
    }
}
