package com.bees360.scheduletask.project;

/**
 * <AUTHOR>
 */
public class ExportProjectForSwyfftUtilNewFactory {

    public static final String V_NULL = "";
    public static final String V1 = "1";

    public static ExportProjectForSwyfftUtilNew newInstance(String version) {
        if(V_NULL.equals(version)) {
            return new ExportProjectForSwyfftUtilNewNull();
        }
        if(V1.equals(version)) {
            return new ExportProjectForSwyfftUtilNewV1();
        }
        throw new IllegalArgumentException("instance of version `" + version + "` is undefined.");
    }
}
