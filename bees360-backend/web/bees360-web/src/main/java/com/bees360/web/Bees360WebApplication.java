package com.bees360.web;

import com.bees360.apikey.config.GrpcApiKeyManagerConfig;
import com.bees360.beespilot.batch.config.GrpcBeesPilotBatchClientConfig;
import com.bees360.boot.ExitableSpringApplication;
import com.bees360.commons.firebasesupport.config.annotation.EnableFirebaseConfig;
import com.bees360.commons.firebasesupport.config.annotation.EnableFirebaseHelperConfig;
import com.bees360.commons.hover.config.EnableHoverClient;
import com.bees360.commons.springsupport.concurrent.SpringAsyncConfig;
import com.bees360.commons.springsupport.concurrent.SpringScheduledConfig;
import com.bees360.config.ActuatorSecurityConfig;
import com.bees360.config.CustomerConfigs;
import com.bees360.config.MailSenderConfig;
import com.bees360.config.ProjectServiceUpgradedConfig;
import com.bees360.config.ScheduledEventConfig;
import com.bees360.config.project.ParentChildProjectConfig;
import com.bees360.contact.GrpcContactRecordClientConfig;
import com.bees360.customer.config.GrpcCustomerPolicyTypeManagerConfig;
import com.bees360.http.LogAndSuppressRequestRejectedExceptionFilter;
import com.bees360.http.config.ProtoHttpMessageConverterConfig;
import com.bees360.image.config.GrpcImageClientConfig;
import com.bees360.image.config.GrpcImageGroupClientConfig;
import com.bees360.image.config.GrpcImageNoteClientConfig;
import com.bees360.image.config.GrpcImageTagClientConfig;
import com.bees360.pilot.feedback.config.GrpcPilotFeedbackManagerConfig;
import com.bees360.pipeline.config.GrpcPipelineClientConfig;
import com.bees360.pipeline.config.GrpcPipelineDefClientConfig;
import com.bees360.pipeline.config.GrpcProjectPipelineConfigConfig;
import com.bees360.project.config.GrpcProjectInformationManagerConfig;
import com.bees360.project.config.GrpcProjectInvoiceProviderConfig;
import com.bees360.project.config.GrpcProjectParticipantClientConfig;
import com.bees360.project.config.GrpcProjectPolicyManagerConfig;
import com.bees360.project.config.GrpcSimilarProjectProviderConfig;
import com.bees360.project.config.ProjectHttpSecurityConfig;
import com.bees360.project.report.GrpcProjectReportJobManagerConfig;
import com.bees360.rct.web.config.RctConfig;
import com.bees360.schedule.EnableQuartzScheduler;
import com.bees360.securityfirst.config.SecurityFirstConfig;
import com.bees360.user.GrpcUserService;
import com.bees360.util.EnvUtils;
import com.bees360.web.openapi.OpenApiConfig;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.autoconfigure.GrpcServerSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.repository.configuration.EnableRedisRepositories;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR> Guanrong
 * @since 2019/12/04 17:13
 */
@Slf4j
@Configuration
@SpringBootApplication(exclude = {GrpcServerSecurityAutoConfiguration.class})
@EnableRedisRepositories(basePackages = "com.bees360.util")
@EnableEncryptableProperties
@EnableScheduling
@EnableQuartzScheduler
@EnableRetry
@EnableFirebaseConfig
@EnableFirebaseHelperConfig
@EnableHoverClient
@EnableAspectJAutoProxy
@Import({
    GrpcUserService.class,
    LogAndSuppressRequestRejectedExceptionFilter.class,
    ProtoHttpMessageConverterConfig.class,
    GrpcPipelineClientConfig.class,
    GrpcPipelineDefClientConfig.class,
    GrpcProjectPipelineConfigConfig.class,
    GrpcImageClientConfig.class,
    GrpcImageGroupClientConfig.class,
    GrpcImageTagClientConfig.class,
    GrpcImageNoteClientConfig.class,
    SecurityFirstConfig.class,
    GrpcProjectParticipantClientConfig.class,
    GrpcPilotFeedbackManagerConfig.class,
    GrpcContactRecordClientConfig.class,
    GrpcBeesPilotBatchClientConfig.class,
    GrpcProjectInvoiceProviderConfig.class,
    GrpcProjectInformationManagerConfig.class,
    GrpcProjectReportJobManagerConfig.class,
    MailSenderConfig.class,
    CustomerConfigs.class,
    GrpcApiKeyManagerConfig.class,
    GrpcSimilarProjectProviderConfig.class,
    ActuatorSecurityConfig.class,
    RctConfig.class,
    GrpcCustomerPolicyTypeManagerConfig.class,
    ScheduledEventConfig.class,
    OpenApiConfig.class,
    ParentChildProjectConfig.class,
    SpringAsyncConfig.class,
    SpringScheduledConfig.class,
    ProjectServiceUpgradedConfig.class,
    ProjectHttpSecurityConfig.class,
    GrpcProjectPolicyManagerConfig.class,
})
public class Bees360WebApplication {

    public static void main(String[] args) throws Exception {
        try {
            EnvUtils.initLog4jSystemProperties(
                    Optional.of("bees360-web"),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty());
            ExitableSpringApplication.run(Bees360WebApplication.class, args);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }
}
