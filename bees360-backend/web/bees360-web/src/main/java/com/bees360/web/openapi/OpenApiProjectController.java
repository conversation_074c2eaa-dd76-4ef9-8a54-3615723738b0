package com.bees360.web.openapi;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.config.support.CurUserId;
import com.bees360.entity.Project;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.openapi.OpenProjectCreateVo;
import com.bees360.entity.openapi.OpenProjectSearchOption;
import com.bees360.entity.openapi.OpenProjectStatusDto;
import com.bees360.entity.openapi.OpenProjectStatusVo;
import com.bees360.entity.openapi.OpenProjectVo;
import com.bees360.entity.openapi.OpenProjectWithStatusVo;
import com.bees360.entity.openapi.OpenReportIdTypeVo;
import com.bees360.resource.GrpcResourceClient;
import com.bees360.service.ProjectService;
import com.bees360.service.openapi.OpenApiProjectService;
import com.bees360.util.httputil.UrlRedirection;
import com.bees360.util.project.ProjectImageArchiveKeyConverter;
import com.bees360.web.core.properties.bean.SystemConfig;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Guanrong
 * @date 2019/12/24 17:05
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/v1/project")
public class OpenApiProjectController {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private OpenApiProjectService openApiProjectService;

    @Autowired
    private ProjectImageArchiveKeyConverter projectImageArchiveKeyConverter;

    @Autowired
    private UrlRedirection urlRedirection;

    @Autowired
    private SystemConfig systemConfig;

    @Autowired
    private GrpcResourceClient grpcResourceClient;

    public OpenApiProjectController() {
        log.info("Created {}", this);
    }

    @PostMapping("")
    public OpenProjectVo.OpenProjectVoListWrapper createProject(@CurUserId long userId,
        @RequestBody @Valid OpenProjectCreateVo projectVo) throws Exception {
        OpenProjectVo project = openApiProjectService.createProject(userId, projectVo);
        return new OpenProjectVo.OpenProjectVoListWrapper(project);
    }

    @GetMapping("")
    public OpenProjectVo.OpenProjectVoListWrapper getProject(@CurUserId long userId,
                                                             @Valid OpenProjectSearchOption searchOption) throws Exception {
        final List<OpenProjectWithStatusVo> openProjectWithStatusVos = openApiProjectService.searchProject(userId, searchOption);
        return new OpenProjectVo.OpenProjectVoListWrapper(openProjectWithStatusVos);
    }

    @GetMapping("/{projectId:\\d+}")
    public OpenProjectVo.OpenProjectVoListWrapper getProject(@CurUserId long userId,
        @PathVariable long projectId) throws Exception {
        OpenProjectVo projectVo = openApiProjectService.getProject(userId, projectId);
        return new OpenProjectVo.OpenProjectVoListWrapper(projectVo);
    }

    @GetMapping("/{projectId:\\d+}/status")
    public OpenProjectStatusVo.OpenProjectStatusListVoWrapper
        getProjectLatestStatus(@PathVariable long projectId) throws Exception {
        OpenProjectStatusVo statusVo = openApiProjectService.getProjectLatestStatus(projectId);
        return new OpenProjectStatusVo.OpenProjectStatusListVoWrapper(statusVo);
    }

    @PutMapping("/{projectId:\\d+}/status")
    public OpenProjectStatusVo.OpenProjectStatusListVoWrapper
        updateProjectStatus(@CurUserId long userId, @PathVariable long projectId,
                            @Valid @RequestBody OpenProjectStatusDto statusDto) throws Exception {

        final var projectStatus = NewProjectStatusEnum.getEnumByValue(statusDto.getValue());
        final var comment = StringUtils.trimToEmpty(statusDto.getComment());
        final var statusVo = openApiProjectService.updateProjectStatus(userId, projectId, projectStatus, comment);
        return new OpenProjectStatusVo.OpenProjectStatusListVoWrapper(statusVo);
    }

    @GetMapping("/{projectId:\\d+}/image/archive")
    public void downloadProjectImagesArchive(@PathVariable long projectId,
                                            HttpServletResponse httpServletResponse) throws Exception {
        Project project = projectService.getById(projectId);
        log.debug("Download project {} with images archive url {}", projectId, Optional.ofNullable(project).map(Project::getImagesArchiveUrl).orElse(""));
        if(project == null || StringUtils.isEmpty(project.getImagesArchiveUrl())) {
            throw new ResourceNotFoundException();
        }
        //TODO convert url to s3 key(be compatible with the old version)
        String imagesArchiveKey = projectImageArchiveKeyConverter.transform(project.getImagesArchiveUrl(), systemConfig.getWebServer());
        //special process with sample project
        imagesArchiveKey = processSampleObjectKey(imagesArchiveKey);
        // get signed url from s3
        var url = grpcResourceClient.asResourceUrlProvider().getGetUrl(imagesArchiveKey);
        if (url == null) {
            throw new ResourceNotFoundException();
        }
        var signedUrl = url.toString();
        log.info("Openapi get the project {} image archive get url: {} before redirect.", projectId, signedUrl);

        urlRedirection.redirect(httpServletResponse, HttpStatus.TEMPORARY_REDIRECT, "application/zip", signedUrl);
    }

    /**
     * 对sample的project进行特殊处理
     * @param imagesArchiveKey
     * @return
     */
    private String processSampleObjectKey(String imagesArchiveKey) {
        if(StringUtils.isEmpty(imagesArchiveKey)) {
            throw new ResourceNotFoundException("key `" + imagesArchiveKey + "` not found.");
        }
        return imagesArchiveKey;
    }

    @GetMapping("/{projectId:\\d+}/report")
    public OpenProjectVo.OpenProjectVoListWrapper listReports(@PathVariable long projectId)
        throws Exception {
        List<OpenReportIdTypeVo> reports = openApiProjectService.listProjectReport(projectId);
        List<OpenProjectVo.OpenProjectReportVo> projectReports =
            Arrays.asList(new OpenProjectVo.OpenProjectReportVo(projectId, reports));
        return new OpenProjectVo.OpenProjectVoListWrapper<>(projectReports);
    }
}
