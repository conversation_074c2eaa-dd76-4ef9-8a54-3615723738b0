package com.bees360.openapi;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Log4j2
@RestController
@RequestMapping(
    value = "/v1/report/{reportId}/summary",
    produces = MediaType.APPLICATION_JSON_VALUE)
public class OpenReportSummaryEndpoint {

    private final OpenApiReportProvider openApiReportProvider;

    public OpenReportSummaryEndpoint(OpenApiReportProvider openApiReportProvider) {
        this.openApiReportProvider = openApiReportProvider;
        log.info("Created {}(openApiReportProvider={})", this, openApiReportProvider);
    }

    @GetMapping("")
    public SummaryEndpointResult getReportSummary(@PathVariable String reportId) {
        var report = openApiReportProvider.getReportSummaryById(reportId);
        return new SummaryEndpointResult(report);
    }

    @Data
    public static class SummaryEndpointResult {
        private Iterable<? extends SummaryReport> report;

        public SummaryEndpointResult(Iterable<? extends SummaryReport> summaryReports) {
            this.report = summaryReports;
        }

        public SummaryEndpointResult(SummaryReport summaryReport) {
            this(List.of(summaryReport));
        }
    }
}
