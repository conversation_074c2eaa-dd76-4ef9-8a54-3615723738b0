package com.bees360.scheduletask.project;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.bees360.entity.ProjectStatus;
import com.bees360.entity.query.ProjectStatusQuery;
import com.bees360.mail.MailMessage;
import com.bees360.mail.MailSender;
import com.bees360.mapper.ProjectStatusMapper;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.service.properties.Bees360WatcherProperties;
import com.google.protobuf.ByteString;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.BsExportData;
import com.bees360.entity.Company;
import com.bees360.entity.Project;
import com.bees360.entity.dto.BsExportDataLogProjectEditorV3;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.query.ProjectFilterQuery;
import com.bees360.mapper.BsExportDataMapper;
import com.bees360.mapper.CompanyMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.UserService;
import com.bees360.web.core.constant.DateTimeConst;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Yang
 */
@Slf4j
public class ExportProjectSummeryForSwyfftUnderwritingJob extends QuartzJobBean {

    private final static String COMPANY_NAME = "Swyfft Underwriting";
    private final static String RELATED_TYPE_PROJECT_EDITOR = "project_editor";
    private final static String RELATED_TYPE_PROJECT_EDITOR_V2 = "project_editor_v2";
    private final static String EXPORT_EXCEL_FILE_NAME = "Bees360_Inspection_Completed_Deliverable_Info.xlsx";
    private final static ZoneId TIME_ZONE_ID = ZoneId.of(DateTimeConst.DEFAULT_TIME_ZONE_ID);

    private final long startTimeInMills;
    private final long endTimeInMills;

    @Autowired
    private ProjectMapper projectMapper;
    @Autowired
    private ProjectStatusMapper projectStatusMapper;
    @Autowired
    private CompanyMapper companyMapper;
    @Autowired
    private BsExportDataMapper bsExportDataMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private MailSender mailSender;
    @Autowired
    private ResourcePool resourcePool;

    @Autowired
    private Bees360WatcherProperties bees360WatcherProperties;

    @Value("${spring.mail.sender}")
    private String emailFrom;

    @Value("${spring.mail.subtitle-prefix}")
    private String subtitlePrefix;

    public ExportProjectSummeryForSwyfftUnderwritingJob() {
        startTimeInMills = yesterdayStart();
        endTimeInMills = startTimeInMills + 24 * 60 * 60 * 1000 - 1;
    }

    private long yesterdayStart() {
        ZonedDateTime yesterday = ZonedDateTime.of(LocalDate.now().minusDays(1), LocalTime.MIN, TIME_ZONE_ID);
        return yesterday.toInstant().toEpochMilli();
    }

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        exportDataAndSendEmail(context);
    }

    private void exportDataAndSendEmail(JobExecutionContext context) throws JobExecutionException {

        JobKey jobKey = context.getJobDetail().getKey();
        log.info("Job({}) start to execute.", jobKey);
        List<String> recipientEmails;
        try {
            recipientEmails = getRecipients();
        } catch (ServiceException e) {
            throw new RuntimeException("Something unexpected happened.", e);
        }
        if (recipientEmails.size() == 0) {
            log.info("JOB({}): There are not any Admin to receive exported data.", jobKey);
            return;
        }

        try {
            List<ExcelData> excelDataList = fetchExcelDataList();
            excelDataList.stream().filter(d -> d != null && d.getRowCount() > 0)
                .forEach(d -> log.info("excel is exported with {} rows.", d.getRowCount()));
            log.info("There are {} excels exported for Job ({})", excelDataList.size(), jobKey);
            send(recipientEmails, excelDataList);
            log.info("Job ({}) is completed to export excels and send emails.", jobKey);
        } catch (RuntimeException e) {
            String message = "JOB(%s): fail to export data of `%s` and send email.".formatted(
                context.getJobDetail().getKey(), COMPANY_NAME);
            throw new RuntimeException(message, e);
        } catch (IOException e) {
            String message = "JOB (%s): fail to create excel file.".formatted(context.getJobDetail().getKey());
            throw new RuntimeException(message, e);
        }
    }

    private List<ExcelData> fetchExcelDataList() throws IOException {
        // 检索符合条件的项目列表
        Company company = companyMapper.getByName(COMPANY_NAME);
        if (company == null) {
            log.warn("The company {} doesn't exist, please make sure the target company name is {}", COMPANY_NAME,
                COMPANY_NAME);
            return Collections.emptyList();
        }
        // 查找符合条件的project status
        ProjectStatusQuery query = new ProjectStatusQuery();
        query.setProjectStatus(NewProjectStatusEnum.RETURNED_TO_CLIENT.getCode());
        query.setCreatedTimeStart(startTimeInMills);
        query.setCreatedTimeEnd(endTimeInMills);

        List<ProjectStatus> statuses = projectStatusMapper.listWithQuery(query);
        if(statuses.isEmpty()) {
            return Collections.emptyList();
        }
        Set<Long> projectIds = statuses.stream().map(ProjectStatus::getProjectId).collect(Collectors.toSet());

        // 获取相关
        ProjectFilterQuery projectFilterQuery = new ProjectFilterQuery();
        projectFilterQuery.setInsuranceCompany(company.getCompanyId());
        projectFilterQuery.setProjectIds(projectIds);

        List<Project> projects = projectMapper.listProjectsWithFilter(projectFilterQuery);
        if (projects.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> projectStringIds = projects.stream().map(p -> p.getProjectId() + "").collect(Collectors.toList());
        return fetchExcelData(projects, projectStringIds);
    }

    private List<ExcelData> fetchExcelData(List<Project> projects, List<String> projectIds) {
        if(projects.isEmpty()) {
            return new ArrayList<>();
        }
        List<ExcelData> excelDatas = new ArrayList<>();
        excelDatas.add(fetchExcelDataV2(projects, projectIds));
        excelDatas.add(fetchExcelDataV3(projects, projectIds));
        excelDatas.addAll(fetchExcelDataWithNewDef(projects, projectIds));
        return excelDatas;
    }

    private List<ExcelData> fetchExcelDataWithNewDef(List<Project> projects, List<String> projectIds) {
        List<ExcelData> excelDatas = new ArrayList<>();
        final String relatedType = RELATED_TYPE_PROJECT_EDITOR;
        if(projects.isEmpty()) {
            return excelDatas;
        }
        List<BsExportData> dataList = bsExportDataMapper.listIn(projectIds, relatedType);
        return ExportProjectForSwyfftNewProcessor.exportToExcels(projects, dataList);
    }

    private ExcelData fetchExcelDataV2(List<Project> projects, List<String> projectIds) {
        ExportProjectForSwyfftUtil util = new ExportProjectForSwyfftUtilV2();
        List<BsExportData> dataList = projects.isEmpty()? new ArrayList<>():
            bsExportDataMapper.listIn(projectIds, RELATED_TYPE_PROJECT_EDITOR_V2);
        return util.fetchExcelData(projects, dataList);
    }

    private ExcelData fetchExcelDataV3(List<Project> projects, List<String> projectIds) {
        ExportProjectForSwyfftUtil util = new ExportProjectForSwyfftUtilV3();
        List<BsExportData> dataList = projects.isEmpty()? new ArrayList<>():
            bsExportDataMapper.listIn(projectIds, BsExportDataLogProjectEditorV3.RELATED_TYPE);
        return util.fetchExcelData(projects, dataList);
    }

    private List<String> getRecipients() throws ServiceException {
        String adminEmail = bees360WatcherProperties.getAdminEmail();
        if (ObjectUtils.isEmpty(adminEmail)) {
            return List.of();
        }
        return List.of(adminEmail);
    }

    private void send(List<String> recipients, List<ExcelData> excelDataList) throws RuntimeException {
        // 移除没有数据的ExcelData
        excelDataList =
            excelDataList.stream().filter(d -> d != null && d.getRowCount() > 0).collect(Collectors.toList());

        int dataCount = excelDataList.stream().mapToInt(ExcelData::getRowCount).sum();
        String subtitle = "Timer - Export data for Swyfft Underwriting Company";
        if (StringUtils.hasLength(subtitlePrefix)) {
            subtitle = subtitlePrefix + " " + subtitle;
        }

        String keyFormat = "project/summary/%s";
        var attachments = new HashMap<String, String>();
        for (int i = 0; i < excelDataList.size(); i++) {
            ExcelData excelData = excelDataList.get(i);
            ByteString data = ByteString.copyFrom(excelData.getFile());
            String indexPrefix = excelDataList.size() > 1 ? i + "_" : "";
            String attachmentName = indexPrefix + System.currentTimeMillis() + "_" + EXPORT_EXCEL_FILE_NAME;
            String key = String.format(keyFormat, attachmentName);
            resourcePool.put(key, Resource.of(data, ResourceMetadata.computeFrom(data)));
            attachments.put(attachmentName, key);
        }
        mailSender.send(MailMessage.of(recipients, subtitle, emailContext(dataCount), attachments));
    }

    private String emailContext(int dataCount) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("YYYY/MM/dd HH:mm:ss z");
        ZonedDateTime startTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(startTimeInMills), TIME_ZONE_ID);
        ZonedDateTime endTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(endTimeInMills), TIME_ZONE_ID);

        StringBuilder sb = new StringBuilder("The count of the data list: " + dataCount + ".\n\n");
        sb.append("Project status:\t" + NewProjectStatusEnum.RETURNED_TO_CLIENT.getDisplay() + "\n");
        sb.append("Time Range:\t[" + formatter.format(startTime) + ", " + formatter.format(endTime) + "]\n\n");
        return sb.toString();
    }

    @Data
    static class ExcelData {
        private byte[] file;
        private int rowCount;

        public ExcelData(byte[] file, int rowCount) {
            this.file = file;
            this.rowCount = rowCount;
        }
    }
}
