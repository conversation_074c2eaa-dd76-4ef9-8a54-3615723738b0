package com.bees360.config.support;

import com.bees360.api.Proto;
import com.bees360.base.MessageCode;
import com.bees360.base.ResponseJson;
import com.bees360.http.ProtoHttpMessageConverter;
import com.bees360.util.CollectionAssitant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.converter.protobuf.ProtobufHttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.regex.Pattern;

@ControllerAdvice
public class ControllerResponseHandler implements ResponseBodyAdvice<Object> {
	private Logger logger = LoggerFactory.getLogger(ControllerResponseHandler.class);

	/**
	 * It will supports all return types.
	 */
	@Override
	public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
		logger.debug("converterType: " + converterType);
		logger.debug("returnType.executable: "
            + returnType.getExecutable().getClass().getName() + "#" + returnType.getExecutable().getName());
		logger.debug("returnType: " + returnType.getClass());
		logger.debug("MappingJackson2HttpMessageConverter is assignable from converterType: "
				+ MappingJackson2HttpMessageConverter.class.isAssignableFrom(converterType));
		return true;
	}

	/**
	 * package the return data into the data field of <code>ResponseJson</code>
	 */
	@Override
	public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
			Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request,
			ServerHttpResponse response) {
		logger.debug("returnType: " + returnType);
		if(isOpenApi(request)) {
			return openApiResponse(body, request, response);
		} else {
			return internalResponse(selectedContentType, body, request, selectedConverterType);
		}
	}

	private boolean isOpenApi(ServerHttpRequest request) {
		ServletServerHttpRequest servletServerHttpRequest = (ServletServerHttpRequest) request;
		HttpServletRequest httpServletRequest = servletServerHttpRequest.getServletRequest();

		// /v1/xxxxx -> {"", "v1", "xxxxx"}
		String[] parts = httpServletRequest.getServletPath().split("/");
		String versionPattern = "v\\d{1}";
		return parts.length > 1 && Pattern.matches(versionPattern, parts[1]);
	}

	private Object internalResponse(MediaType mediaType, Object body, ServerHttpRequest request,
                                    Class<? extends HttpMessageConverter<?>> selectedConverterType) {
		ResponseJson json;
		if(body instanceof ResponseJson responseJson) {
			json = responseJson;
		} else if(body instanceof ApiError apiError) {
			json = new ResponseJson();
			json.setCode(apiError.getCode());
			if(!CollectionAssitant.isEmpty(apiError.getValidErrors())) {
				json.setMsg(apiError.getValidErrors().get(0).getMessage());
			} else {
				json.setMsg(apiError.getMessage());
			}
            if(!CollectionUtils.isEmpty(apiError.getMetadata())){
                json.setData(apiError.getMetadata());
            }
		} else if(mediaType.includes(ProtobufHttpMessageConverter.PROTOBUF) ||
            selectedConverterType.isAssignableFrom(ProtoHttpMessageConverter.class)) {
            // if response is protesobuf, then return body.
            return body;
        } else if (body instanceof com.google.protobuf.Message || body instanceof Proto) {
            return body;
        } else {
			json = new ResponseJson(body);
		}
		return json;
	}

	private Object openApiResponse(Object body, ServerHttpRequest request, ServerHttpResponse response) {
		if(body instanceof ResponseJson json) {
			if(json.success()) {
				return json.getData();
			} else {
				var error = OpenApiError.error(json.getMsg());
                logger.warn("Error result: {}", error);
                return error;
			}
		} else if (body instanceof ApiError apiError) {
			response.setStatusCode(apiError.getHttpStatus());

			String message;
			if(!CollectionAssitant.isEmpty(apiError.getValidErrors())) {
				message = apiError.getValidErrors().get(0).getMessage();
			} else {
				message = apiError.getMessage();
			}
			var error = OpenApiError.error(message);
            if(Objects.equals(apiError.getCode(), MessageCode.SYSTEM_EXCEPTION)) {
                logger.error("Error result: {}", error, new IllegalStateException(message));
            } else {
                logger.warn("Error result: {}", error);
            }
            return error;
        }
		return body;
	}
}
