package com.bees360.config;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;

import com.bees360.util.payment.StripePaymentSettings;
import com.stripe.Stripe;

public class SettingInitialize {

	@Inject
	private StripePaymentSettings stripePaymentSettings;

	@PostConstruct
	public void init() {
		initStripePayment();
	}

	public void initStripePayment() {
		Stripe.apiKey = stripePaymentSettings.getSecretApiKey();
	}
}
