package com.bees360.scheduletask.project;

import com.bees360.entity.User;
import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.mapper.label.ProjectLabelMapper;
import com.bees360.service.ProjectLabelService;
import com.bees360.util.schedule.annotation.DistributedScheduled;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@DistributedScheduled
public class ProjectLabelExpirationSchedule {

    private static final long INTERVAL = 15 * 60 * 1000;

    @Autowired
    private ProjectLabelMapper projectLabelMapper;

    @Autowired
    private ProjectLabelService projectLabelService;

    public ProjectLabelExpirationSchedule() {
       log.info("Created {}", this);
    }

    @Scheduled(
        initialDelayString = "${scheduler.spring-scheduled.start-up-delay:-}",
        fixedRate = INTERVAL)
    public void cleanProjectLabel() {
        Long labelId = ProjectLabelEnum.INCLEMENT_WEATHER.getLabelId();
        Instant endTime = Instant.now().minus(Duration.ofHours(24));
        List<BoundProjectLabel> labelsList = projectLabelMapper.getByLabelIdAndCreatedTime(
           labelId, Instant.EPOCH, endTime);
        if (labelsList.isEmpty()) {
            return;
        }
        List<Long> projectIds = labelsList.stream().map(BoundProjectLabel::getProjectId).collect(Collectors.toList());
        projectIds.forEach(
                projectId ->
                        projectLabelService.markAfterEraseLabel(
                                projectId, Collections.emptyList(), User.BEES_PILOT_SYSTEM, SystemTypeEnum.BEES360));
        log.info("finish clean expired label[{}] for projects: {}",
            ProjectLabelEnum.INCLEMENT_WEATHER.getLabelName(), projectIds);
    }
}
