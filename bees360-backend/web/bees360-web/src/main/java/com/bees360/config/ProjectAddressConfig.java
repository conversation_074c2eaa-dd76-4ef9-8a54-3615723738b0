package com.bees360.config;

import com.bees360.address.AddressManager;
import com.bees360.address.Message;
import com.bees360.entity.enums.ProjectTypeEnum;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Map;
import java.util.function.Function;

@Configuration
@RequiredArgsConstructor
@Log4j2
@Import({UpdateProjectAddressOnAddressChangedConfig.class,})
public class ProjectAddressConfig {
    private final AddressManager addressManager;

    @Bean
    Map<String, ProjectTypeEnum> hazardHubPropertyConvertMap() {
        return Map.of(
                "Single Family Residence",
                ProjectTypeEnum.Residential_Single_Family,
                "Condominium",
                ProjectTypeEnum.Residential_Condo,
                "Townhouse",
                ProjectTypeEnum.RESIDENTIAL_TOWNHOUSE,
                "Multi Family",
                ProjectTypeEnum.RESIDENTIAL_MULTI_FAMILY,
                "Apartment",
                ProjectTypeEnum.RESIDENTIAL_APARTMENTS,
                "Commercial",
                ProjectTypeEnum.COMMERCIAL);
    }

    @Bean
    Function<Message.AddressQueryRequest, ProjectTypeEnum> getPropertyByHazardHub(
            @Value("${bees360.project.hazardhub.enable:false}") boolean enabled,
            @Value("${bees360.project.hazardhub.property_path:$.property.use_code}")
                    String propertyPath,
            @Autowired Map<String, ProjectTypeEnum> hazardHubPropertyConvertMap) {
        if (!enabled) {
            return (addressQuery -> ProjectTypeEnum.Residential_Single_Family);
        }
        return addressQuery -> {
            var enhancedProperty =
                    addressManager
                            .findAddressEnhancedProperty(addressQuery)
                            .getEnhancedPropertyJson();
            try {
                log.info("get enhanced property :{}", enhancedProperty);
                String useCode = JsonPath.read(enhancedProperty, propertyPath);
                return hazardHubPropertyConvertMap.entrySet().stream()
                        .filter(entry -> StringUtils.containsIgnoreCase(useCode, entry.getKey()))
                        .findFirst()
                        .map(Map.Entry::getValue)
                        .orElse(ProjectTypeEnum.Residential_Single_Family);
            } catch (PathNotFoundException e) {
                log.info(
                        "cannot find property for address '{}' through HazardHub. ",
                        addressQuery,
                        e);
            }
            return ProjectTypeEnum.Residential_Single_Family;
        };
    }

    @Bean
    Function<Message.AddressQueryRequest, Integer> getLivingAreaByHazardHub(
            @Value("${bees360.project.hazardhub.enable:false}") boolean enabled,
            @Value("${bees360.project.hazardhub.cost_path:$.living_area.square_footage}")
                    String costPath) {
        if (!enabled) {
            return ((addressQuery) -> null);
        }
        return addressQuery -> {
            var replacementCosts =
                    addressManager
                            .findAddressReplacementCosts(addressQuery)
                            .getReplacementCostsJson();
            try {
                log.info("get cost json :{} ", replacementCosts);
                String projectLivingArea = JsonPath.read(replacementCosts, costPath);
                if (StringUtils.isNotBlank(projectLivingArea)
                        && StringUtils.isNumeric(projectLivingArea)) {
                    return Integer.parseInt(projectLivingArea);
                }
            } catch (PathNotFoundException e) {
                log.info(
                        "cannot find living area for address '{}' through HazardHub. ",
                        addressQuery,
                        e);
            }
            return null;
        };
    }
}
