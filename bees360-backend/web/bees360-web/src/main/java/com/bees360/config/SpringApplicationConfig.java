package com.bees360.config;

import com.bees360.entity.CompanyIDMap;
import com.bees360.mail.MailSender;
import com.bees360.util.msgutil.DelegateEmailSender;
import com.bees360.util.msgutil.filter.EmailFilter;
import com.bees360.util.msgutil.filter.RegexEmailFilter;
import com.bees360.util.msgutil.filter.RegexEmailFilterProperties;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.sql.init.dependency.DependsOnDatabaseInitialization;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.TransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.thymeleaf.TemplateEngine;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;

/**
 * 由spring-application.xml转化而来
 * <ul>
 *     <li>{@code @EnableTransactionManagement} 启动事务注解</li>
 *     <li>{@code @EnableScheduling 启动定时器注解@Scheduling}</li>
 *     <li>{@code @EnableAsync 启动异步任务注解 @Async}</li>
 * </ul>
 * <AUTHOR> Guanrong
 * @date 2019/12/05 22:41
 */
@Slf4j
@Configuration
@EnableScheduling
@EnableAsync(proxyTargetClass=true)
@EnableTransactionManagement
@EnableConfigurationProperties
@MapperScan(basePackageClasses = {com.bees360.mapper.PackageMarker.class})
public class SpringApplicationConfig {

    private final String EMAIL_SUBJECTS = "classpath:message/emailSubject.properties";
    // 是否发送邮件配置
    private @Value("${bees360.mail.sending.enabled:true}") boolean sendingEnabled;
    @Value("${mail.subtitle-prefix:}")
    private String subtitlePrefix;

    @Bean
    public ResourceBundleMessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasename("messages_en");

        return messageSource;
    }

    @Bean("dataSourceTransactionManager")
    @DependsOnDatabaseInitialization
    public DataSourceTransactionManager dataSourceTransactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Primary
    @Bean("transactionManager")
    public TransactionManager transactionManager(@Qualifier("dataSourceTransactionManager") TransactionManager dataSourceTransactionManager) {
        log.info("Use TransactionManager {} as global.", dataSourceTransactionManager);
        return dataSourceTransactionManager;
    }

    @Bean
    @ConfigurationProperties("mail.recipient-filter")
    public RegexEmailFilterProperties regexEmailFilterProperties() {
        return new RegexEmailFilterProperties();
    }

    @Bean
    public EmailFilter emailFilter(RegexEmailFilterProperties regexEmailFilterProperties) {
        log.info("EmailSender will be filter with: {}", regexEmailFilterProperties);
        RegexEmailFilter filter = new RegexEmailFilter();
        Optional.ofNullable(regexEmailFilterProperties.getRegexInclude()).ifPresent(filter::setRegexInclude);
        Optional.ofNullable(regexEmailFilterProperties.getEmailInclude()).ifPresent(filter::setEmailInclude);
        Optional.ofNullable(regexEmailFilterProperties.getRegexExclude()).ifPresent(filter::setRegexExclude);
        Optional.ofNullable(regexEmailFilterProperties.getEmailExclude()).ifPresent(filter::setEmailExclude);
        return filter;
    }

    @Bean
    public DelegateEmailSender springEmailSender(
        @Qualifier("clientMailSender") MailSender clientMailSender, TemplateEngine templateEngine,
        Optional<EmailFilter> emailFilter) throws IOException {
        return getDelegateEmailSender(clientMailSender, templateEngine,
            emailFilter);
    }

    @Bean
    public DelegateEmailSender springEmailSenderNoReply(
        @Qualifier("doNotReplyMailSender") MailSender doNotReplyMailSender, TemplateEngine templateEngine,
        Optional<EmailFilter> emailFilter)
        throws IOException {
        return getDelegateEmailSender(doNotReplyMailSender, templateEngine,
            emailFilter);
    }

    @Bean
    public DelegateEmailSender springEmailSenderBackend(
        @Qualifier("backendMailSender") MailSender backendMailSender, TemplateEngine templateEngine,
        Optional<EmailFilter> emailFilter)
        throws IOException {
        return getDelegateEmailSender(backendMailSender, templateEngine,
            emailFilter);
    }

    private DelegateEmailSender getDelegateEmailSender(
        MailSender mailSender,
        TemplateEngine templateEngine,
        Optional<EmailFilter> emailRecipientFilter) throws IOException {
        var subjectProperties = PropertiesLoaderUtils.loadProperties(new PathMatchingResourcePatternResolver().getResource(EMAIL_SUBJECTS));
        return new DelegateEmailSender(mailSender, templateEngine, subtitlePrefix, sendingEnabled, subjectProperties, emailRecipientFilter.orElse(null));
    }

    @ConfigurationProperties(prefix = "company-id-map")
    @Bean CompanyIDMap companyIDMap() {
        return new CompanyIDMap();
    }
}
