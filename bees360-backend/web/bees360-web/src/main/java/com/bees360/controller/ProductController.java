package com.bees360.controller;

import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import com.bees360.entity.enums.productandpayment.ProductTypeEnum;
import com.bees360.entity.vo.ProductListVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bees360.base.MapBuilder;
import com.bees360.entity.Product;
import com.bees360.entity.vo.ProductReportVo;
import com.bees360.service.ProductService;

@RestController
@RequestMapping("/products")
public class ProductController {

	@Inject
	ProductService productService;

	@GetMapping("")
	public ProductListVo listProducts(Integer productType, Boolean isPublished) throws Exception {
		List<Product> products = productService.listProducts(productType, isPublished);
		return new ProductListVo(products);
	}

	@GetMapping("/reports")
	public Map<String, Object> listReports() throws Exception {
		List<ProductReportVo> reports = productService.listOrderableReports();
		return MapBuilder.result("reports", reports);
	}
}
