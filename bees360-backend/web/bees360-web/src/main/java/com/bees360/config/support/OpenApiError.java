package com.bees360.config.support;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/12/24 16:49
 */
@Data
@NoArgsConstructor
public class OpenApiError {
    private OpenApiErrorMessage error;

    @Data
    @NoArgsConstructor
    public static class OpenApiErrorMessage {
        private String message = "";

        public OpenApiErrorMessage(String message) {
            this.message = message;
        }
    }

    public static OpenApiError error(String message) {
        OpenApiError openApiError = new OpenApiError();
        openApiError.setError(new OpenApiErrorMessage(message));
        return openApiError;
    }
}
