package com.bees360.config.support;

import com.bees360.api.ApiClientException;
import com.bees360.api.ApiException;
import com.bees360.api.InternalException;
import com.bees360.api.Message.ApiMessage;
import com.bees360.base.MessageCode;
import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.ServiceMessageException;
import com.bees360.base.exception.UncheckedServiceException;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.service.ContextProvider;
import com.bees360.service.MessageService;
import jakarta.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.CaseFormat;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Path;
import jakarta.validation.ValidationException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;

import static com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES;

@Log4j2
@ControllerAdvice
@ResponseBody
public class ControllerExceptionHandler {

    @Inject
    private MessageSource messageSource;

    @Inject
    private MessageService messageService;

    @Inject
    private ContextProvider springSecurityContextProvider;

    @Inject
    private ApiExceptionHandler apiExceptionHandler;

    @Autowired
    private ObjectMapper objectMapper;

    private String getMessageFromCode(String code) {
        return MessageCode.getMessage(messageSource, code);
    }

    @ExceptionHandler()
    public ApiError handleException(HttpServletResponse response, HttpServletRequest request, WebRequest webRequest,
        Exception ex) {
        if (ex instanceof ServiceMessageException exception) {
            return serviceMessageException(request, exception);
        }
        if (ex instanceof IllegalStateException exception) {
            return serviceMessageException(request, exception);
        }
        if (ex instanceof UncheckedServiceException exception) {
            return uncheckServiceException(request, exception);
        }
        if (ex instanceof ServiceException exception) {
            return serviceException(request, exception);
        }
        if (ex instanceof ResourceNotFoundException exception) {
            return handleResourceNotFoundException(request, exception);
        }
        if (ex instanceof MethodArgumentNotValidException exception) {
            return handleMethodArgumentNotValid(request, exception);
        }
        if (ex instanceof ConstraintViolationException exception) {
            return handleConstraintViolationException(request, exception);
        }
        if (ex instanceof BindException exception) {
            return handleBindException(request, exception);
        }
        if (ex instanceof MethodArgumentTypeMismatchException exception) {
            return methodArgumentTypeMismatchExceptionHandler(request, exception);
        }
        if (ex instanceof MissingServletRequestParameterException exception) {
            return exceptionHandle(request, exception);
        }
        if (ex instanceof HttpMediaTypeNotAcceptableException exception) {
            return handleHttpMediaTypeNotAcceptableException(request, exception);
        }
        if (ex instanceof HttpMediaTypeNotSupportedException exception) {
            return handleHttpMediaTypeNotSupportedException(request, exception);
        }
        if (ex instanceof HttpMessageNotReadableException exception) {
            return handlerHttpMessageNotReadableException(request, exception);
        }
        if (ex instanceof HttpMessageNotWritableException exception) {
            return handlerHttpMessageNotWritableException(request, exception);
        }
        if (ex instanceof HttpRequestMethodNotSupportedException exception) {
            return exceptionHandle(request, exception);
        }
        if (ex instanceof MissingServletRequestPartException exception) {
            return exceptionHandle(request, exception);
        }
        if (ex instanceof NoHandlerFoundException exception) {
            return exceptionHandle(request, exception);
        }
        if (ex instanceof MissingPathVariableException exception) {
            return exceptionHandle(request, exception);
        }
        if (ex instanceof ValidationException exception) {
            return exceptionHandle(request, exception);
        }
        if (ex instanceof NoSuchElementException exception) {
            return exceptionHandle(request, exception);
        }
        if (ex instanceof IllegalArgumentException exception) {
            return exceptionHandle(request, exception);
        }
        if (ex instanceof UnsupportedOperationException exception) {
            return exceptionHandle(request, exception);
        }
        if (ex instanceof ApiException exception) {
            return exceptionHandle(response, request, webRequest, exception);
        }
        if (ex instanceof HttpMessageConversionException exception) {
            return exceptionHandle(request, exception);
        }
        return handleAll(request, ex);
    }

    // ==================================================
    // Custom Exception

    public ApiError serviceMessageException(HttpServletRequest request, ServiceMessageException e) {
        logException(request, e, Level.WARN);
        return ApiError.badRequest(e.getMsgCode(), e.getMessage());
    }

    public ApiError serviceMessageException(HttpServletRequest request, IllegalStateException e) {
        return internalServiceError(request, e);
    }

    public ApiError uncheckServiceException(HttpServletRequest request, UncheckedServiceException e) {
        return serviceException(request, e.getCause());
    }

    public ApiError serviceException(HttpServletRequest request, ServiceException e) {
        if (MessageCode.SYSTEM_EXCEPTION.equals(e.getMsgCode())
            || MessageCode.DATABASE_EXCEPTION.equals(e.getMsgCode())) {
            return internalServiceError(request, e);
        }
        logException(request, e, Level.WARN);
        String msg = getMessageFromCode(e.getMsgCode());
        return ApiError.badRequest(e.getMsgCode(), msg);
    }

    public ApiError handleResourceNotFoundException(HttpServletRequest request, ResourceNotFoundException ex) {
        logException(request, ex, Level.WARN);
        return ApiError.notFound(ex.getMessage());
    }

    // ================= replace spring default exception ================== //

    // =========================================
    // 数据校验

    /**
     * MethodArgumentNotValidException: 实体类属性校验不通过
     *
     * 如: listUsersValid(@RequestBody @Valid UserFilterOption option)
     */
    public ApiError handleMethodArgumentNotValid(HttpServletRequest request, MethodArgumentNotValidException ex) {
        logException(request, ex, Level.WARN);
        List<ValidError> validErrors = validatorErrors(ex.getBindingResult(), ex);
        return validationFailed(validErrors);
    }

    private ApiError validationFailed(List<ValidError> validErrors) {
        ApiError apiError = ApiError.validationFailed(validErrors);
        apiError.setCode(MessageCode.VALIDATION_FAILED);
        return apiError;
    }

    private List<ValidError> validatorErrors(BindingResult result, Exception ex) {
        List<ValidError> validErrors = new ArrayList<>();
        for (FieldError error : result.getFieldErrors()) {
            validErrors.add(toFieldNotValidError(result, error));
        }
        // 这里可以对接口的数据校验进行拓展
        return validErrors;
    }

    /**
     * ConstraintViolationException: 直接对方法参数进行校验，校验不通过。
     *
     * 如: pageUsers(@RequestParam @Min(1)int pageIndex, @RequestParam @Max(100)int pageSize)
     */
    public ApiError handleConstraintViolationException(HttpServletRequest request, ConstraintViolationException ex) {
        logException(request, ex, Level.WARN);
        List<ValidError> validErrors = validErrors(ex);
        // 这里可以对接口的数据校验进行拓展
        return validationFailed(validErrors);
    }

    /**
     * BindException: 数据绑定异常
     *
     * 效果与MethodArgumentNotValidException类似，为MethodArgumentNotValidException的父类
     */
    public ApiError handleBindException(HttpServletRequest request, BindException ex) {
        logException(request, ex, Level.WARN);
        List<ValidError> validErrors = validatorErrors(ex.getBindingResult(), ex);
        return validationFailed(validErrors);
    }

    /**
     * 参数类型不匹配
     */
    public ApiError methodArgumentTypeMismatchExceptionHandler(HttpServletRequest request,
                                                               MethodArgumentTypeMismatchException ex) {
        logException(request, ex, Level.WARN);
        String message = "The parameter '" + ex.getName() + "' should of type '"
            + ex.getRequiredType().getSimpleName().toLowerCase() + "'";
        ValidError validError = new ValidError(ValidErrorType.TYPE_MISMATCH.getType(), ex.getName(), message);
        // 这里可以对接口的数据校验进行拓展
        return validationFailed(Arrays.asList(validError));
    }

    /**
     * 缺少必填字段
     */
    public ApiError exceptionHandle(HttpServletRequest request, MissingServletRequestParameterException ex) {
        logException(request, ex, Level.WARN);
        String message = "Required parameter '" + ex.getParameterName() + "' is not present";
        ValidError validError = new ValidError(ValidErrorType.MISSING_FIELD.getType(), ex.getParameterName(), message);
        // 这里可以对接口的数据校验进行拓展
        return validationFailed(Arrays.asList(validError));
    }

    private ValidError toFieldNotValidError(ConstraintViolation<?> constraintViolation) {
        Path.Node lastNode = null;
        for (Path.Node node : constraintViolation.getPropertyPath()) {
            lastNode = node;
        }
        String fieldName = lastNode.getName();
        String message = fieldName + ": " + constraintViolation.getMessage();
        return new ValidError(ValidErrorType.INVALID.getType(), fieldName, message);
    }

    private ValidError toFieldNotValidError(BindingResult result, FieldError error) {

        ValidErrorType validErrorType = ValidErrorType.INVALID;

        String message;
        if ("typeMismatch".equals(error.getCode())) {
            message = "The parameter '" + error.getField() + "' should of type '"
                + result.getFieldType(error.getField()).getSimpleName().toLowerCase() + "'";
            validErrorType = ValidErrorType.TYPE_MISMATCH;
        } else {
            message = error.getField() + ": " + error.getDefaultMessage();
        }

        return new ValidError(validErrorType.getType(), error.getField(), message);
    }

    private List<ValidError> validErrors(ConstraintViolationException ex) {
        List<ValidError> validErrors = new ArrayList<>();
        for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
            ValidError validError = toFieldNotValidError(violation);
            validErrors.add(validError);
        }
        return validErrors;
    }

    // -------------------------------

    /**
     * 返回值类型转化错误
     */
    public ApiError exceptionHandle(HttpServletRequest request, HttpMessageConversionException ex) {
        return internalServiceError(request, ex);
    }

    /**
     * 对应 Http 请求头的 accept 客户器端希望接受的类型和服务器端返回类型不一致。 这里虽然设置了拦截，但是并没有起到作用。需要通过http请求的流程来进一步确定原因。
     */
    public ApiError handleHttpMediaTypeNotAcceptableException(HttpServletRequest request,
                                                              HttpMediaTypeNotAcceptableException ex) {
        logException(request, ex, Level.WARN);
        StringBuilder messageBuilder =
            new StringBuilder().append("The media type is not acceptable.").append(" Acceptable media types are ");
        ex.getSupportedMediaTypes().forEach(t -> messageBuilder.append(t + ", "));
        HttpStatus httpStatus = HttpStatus.NOT_ACCEPTABLE;
        String code = httpStatus.value() + "";
        String message = messageBuilder.substring(0, messageBuilder.length() - 2);

        return new ApiError(httpStatus, code, message);
    }

    /**
     * 对应请求头的 content-type 客户端发送的数据类型和服务器端希望接收到的数据不一致
     */
    public ApiError handleHttpMediaTypeNotSupportedException(HttpServletRequest request,
                                                             HttpMediaTypeNotSupportedException ex) {
        logException(request, ex, Level.WARN);
        return ApiError.unsupportedMediaType(ex.getContentType(), ex.getSupportedMediaTypes());
    }

    /**
     * 前端发送过来的数据无法被正常处理 比如后天希望收到的是一个json的数据，但是前端发送过来的却是xml格式的数据或者是一个错误的json格式数据
     */
    public ApiError handlerHttpMessageNotReadableException(HttpServletRequest request,
                                                           HttpMessageNotReadableException ex) {
        logException(request, ex, Level.WARN);
        String message = "Problems parsing JSON";
        return ApiError.badRequest(HttpStatus.BAD_REQUEST.value() + "", message);
    }

    /**
     * 将返回的结果转化到响应的数据时候导致的问题。 当使用json作为结果格式时，可能导致的原因为序列化错误。 目前知道，如果返回一个没有属性的对象作为结果时，会导致该异常。
     */
    public ApiError handlerHttpMessageNotWritableException(HttpServletRequest request,
                                                           HttpMessageNotWritableException ex) {
        return internalServiceError(request, ex);
    }

    /**
     * 请求方法不支持
     */
    public ApiError exceptionHandle(HttpServletRequest request, HttpRequestMethodNotSupportedException ex) {
        logException(request, ex, Level.WARN);
        return ApiError.methodNotAllow(ex.getMethod(), ex.getSupportedHttpMethods());
    }

    /**
     * 文件上传时，缺少 file 字段
     */
    public ApiError exceptionHandle(HttpServletRequest request, MissingServletRequestPartException ex) {
        logException(request, ex, Level.WARN);
        String message = "Required request part '" + ex.getRequestPartName() + "' is not present";
        ApiError apiError = ApiError.badRequest("", message);
        apiError.setCode(apiError.getHttpStatus().value() + "");
        return apiError;
    }

    /**
     * 请求路径不存在
     */
    public ApiError exceptionHandle(HttpServletRequest request, NoHandlerFoundException ex) {
        logException(request, ex, Level.WARN);
        return ApiError.notFound(ex.getHttpMethod(), ex.getRequestURL());
    }

    /**
     * 缺少路径参数 Controller方法中定义了 @PathVariable(required=true) 的参数，但是却没有在url中提供
     */
    public ApiError exceptionHandle(HttpServletRequest request, MissingPathVariableException ex) {
        ApiError apiError = ApiError.badRequest("", "");
        apiError.setCode(apiError.getHttpStatus().value() + "");
        apiError.setMessage(apiError.getHttpStatus().getReasonPhrase());
        return apiError;
    }

    /** 校验执行过程的发生的异常 **/
    public ApiError exceptionHandle(HttpServletRequest request, ValidationException ex) {
        String message = "";
        if (ex.getCause() instanceof InvocationTargetException) {
            message = ex.getCause().getCause().getMessage();
        } else {
            message = "The content of request is invalid: " + ex.getMessage();
        }
        logException(request, ex, Level.WARN);
        return ApiError.badRequest(MessageCode.PARAM_INVALID, message);
    }

    public ApiError exceptionHandle(HttpServletRequest request, NoSuchElementException ex) {
        logException(request, ex, Level.WARN);
        return ApiError.notFound(ex.getMessage());
    }

    public ApiError exceptionHandle(HttpServletRequest request, IllegalArgumentException ex) {
        logException(request, ex, Level.WARN);
        return ApiError.badRequest(MessageCode.PARAM_INVALID, ex.getMessage());
    }

    public ApiError exceptionHandle(HttpServletRequest request, UnsupportedOperationException ex) {
        logException(request, ex, Level.WARN);
        logException(request, "Method not implemented", ex, Level.ERROR);
        return ApiError.notImplement();
    }

    public ApiError exceptionHandle(HttpServletResponse response, HttpServletRequest request, WebRequest webRequest,
            ApiException ex) {
        logException(request, ex, ex instanceof ApiClientException? Level.WARN: Level.ERROR);
        ResponseEntity<ApiMessage> responseEntity = apiExceptionHandler.handleAllException(response, request, webRequest, ex);
        var status = responseEntity.getStatusCode();
        var message = responseEntity.getBody().getStatus().getDescription();
        return new ApiError((HttpStatus)status, status.value() + "", message, convertUnescapeJsonMap(ex.getMetadata()));
    }

    //api exp中自带的metadata map，每个值都是一个转义后的json字符串，需要转换为对象
    private Map<String, Object> convertUnescapeJsonMap(Map<String, String> jsonMap) {
        if (CollectionUtils.isEmpty(jsonMap)) {
            return null;
        }
        Map<String, Object> objectMap = new HashMap<>();
        jsonMap.forEach((k, v) -> {
            Object o = null;
            try {
                o = objectMapper.readValue(v, Object.class);
            } catch (JsonProcessingException e) {
                throw new InternalException(e.getMessage());
            }
            objectMap.put(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, k), o);
        });
        return objectMap;
    }

    /**
     * 其他所有的异常
     */
    public ApiError handleAll(HttpServletRequest request, Exception ex) {
        return internalServiceError(request, ex);
    }

    private ApiError internalServiceError(HttpServletRequest request, Exception e) {

        logException(request, e, Level.ERROR);
//        emailInfo(errorMessage, e);

        String code = MessageCode.SYSTEM_EXCEPTION;
        String msg = getMessageFromCode(code);
        ApiError apiErrorHaha = ApiError.internalServerError();
        apiErrorHaha.setCode(code);
        apiErrorHaha.setMessage(msg);
        return apiErrorHaha;
    }

    private void logException(HttpServletRequest request, String message, Exception ex, Level level) {

        String requestInfo = generateRequestInfo(request);

        long userId = 0;
        try {
            userId = springSecurityContextProvider.getUserIdFromContext();
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }
        final String logExceptionFormat = "[EXIGENCE] %s: user %s, %s";
        String msg = StringUtils.isEmpty(message)? ex.getMessage(): message;
        String errorMessage = logExceptionFormat.formatted(requestInfo, userId, msg);

        log.log(level, errorMessage, ex);
    }

    private void logException(HttpServletRequest request, Exception ex, Level level) {
        logException(request, "", ex, level);
    }

    private String generateRequestInfo(HttpServletRequest request) {
        String method = request.getMethod();
        String pathInfo = request.getRequestURI();
        String requestInfo = method + " " + pathInfo;
        if("GET".equalsIgnoreCase(method)) {
            requestInfo += "?" + request.getQueryString();
        }
        return requestInfo;
    }

    private void emailInfo(String errorMessage, Exception e) {
         try {
            messageService.sendErrorToEngineer(errorMessage, e);
         } catch (Exception ex) {
             log.error("Fail to send exigence message to engineers", ex);
         }
    }

}
