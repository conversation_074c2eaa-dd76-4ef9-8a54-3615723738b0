package com.bees360.controller;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.config.support.CurUserId;
import com.bees360.entity.BeesPilotStatus;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.dto.CodeNameDto;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.vo.ProjectStatusVo;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.beespilot.BeesPilotStatusService;
import com.bees360.service.firebase.FirebaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Guanrong
 * @since  2019/12/26 14:36
 */
@RestController
@RequestMapping("/projects/{projectId:\\d+}/statuses")
public class ProjectStatusController {

    @Autowired
    private ProjectStatusService projectStatusService;

    @Autowired
    private ProjectService projectService;
    @Autowired FirebaseService firebaseService;
    @Autowired BeesPilotStatusService beesPilotStatusService;

    @GetMapping("")
    public ProjectStatusVo.ProjectStatusVoListWrapper getProjectStatusList(@PathVariable long projectId)
        throws Exception {
        List<ProjectStatusVo> statusVos = projectStatusService.listProjectStatusTimeLine(projectId);
        return new ProjectStatusVo.ProjectStatusVoListWrapper(statusVos);
    }

    /**
     * sort createTime desc
     * @param projectId project id
     * @return project list
     */
    @GetMapping("list")
    public ProjectStatusVo.ProjectStatusVoListWrapper getProjectStatusListByCreateTime(@PathVariable long projectId)
        throws Exception {
        List<ProjectStatusVo> statusVos = projectStatusService.listProjectStatusByCreateTime(projectId);
        return new ProjectStatusVo.ProjectStatusVoListWrapper(statusVos);
    }

    @PostMapping("/returned-to-client")
    public ProjectStatusVo changeOnReturnedToClient(@CurUserId long userId, @PathVariable long projectId){
        ProjectStatus projectStatus = projectStatusService.changeOnReturnedToClient(userId, projectId);
        return toStatusVo(projectStatus);
    }

    @PostMapping("/image-uploaded")
    public ProjectStatusVo changOnImageUploaded(@CurUserId long userId, @PathVariable long projectId)
        throws Exception {
        ProjectStatus projectStatus = projectStatusService.changeOnImageUploaded(userId, projectId);
        return toStatusVo(projectStatus);
    }

    @PostMapping("/client-received")
    public ProjectStatusVo changeOnClientReceived(@CurUserId long userId, @PathVariable long projectId) throws Exception{

        final Project project = projectService.getById(projectId);
        if (project == null || NewProjectStatusEnum.RETURNED_TO_CLIENT != NewProjectStatusEnum.getEnum(project.getProjectStatus())){
            throw new ServiceException(MessageCode.PARAM_INVALID);
        }
        final ProjectStatus projectStatus = projectStatusService.changeOnClientReceived(userId, projectId, "");
        return toStatusVo(projectStatus);
    }

    private List<ProjectStatusVo> toStatusVos(List<ProjectStatus> statuses) {
        List<ProjectStatusVo> statusVos = new ArrayList<>(statuses.size());
        for (ProjectStatus status : statuses) {
            statusVos.add(toStatusVo(status));
        }
        return statusVos;
    }

    private ProjectStatusVo toStatusVo(ProjectStatus status) {
        ProjectStatusVo statusVo = new ProjectStatusVo();
        statusVo.setCreatedTime(status.getCreatedTime());
        NewProjectStatusEnum projectStatusEnum = NewProjectStatusEnum.getEnum(status.getStatus());
        statusVo.setStatus(new CodeNameDto(projectStatusEnum.getCode(), projectStatusEnum.getDisplay()));
        return statusVo;
    }
}
