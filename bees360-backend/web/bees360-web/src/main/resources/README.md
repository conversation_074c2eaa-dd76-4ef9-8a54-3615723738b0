配置说明
===

###### `<PERSON><PERSON><PERSON>` `2019/12/13`

加载顺序
---

### Springboot 配置文件加载顺序

[Spring Boot uses a very particular PropertySource order that is designed to allow sensible overriding of values. Properties are considered in the following order:](https://docs.spring.io/spring-boot/docs/2.1.6.RELEASE/reference/html/boot-features-external-config.html#boot-features-external-config)
1.  [Devtools global settings properties](https://docs.spring.io/spring-boot/docs/2.1.6.RELEASE/reference/html/using-boot-devtools.html#using-boot-devtools-globalsettings "20.4 Global Settings") on your home directory (`~/.spring-boot-devtools.properties` when devtools is active).
2.  [`@TestPropertySource`](https://docs.spring.io/spring/docs/5.1.8.RELEASE/javadoc-api/org/springframework/test/context/TestPropertySource.html) annotations on your tests.
3.  `properties` attribute on your tests. Available on [`@SpringBootTest`](https://docs.spring.io/spring-boot/docs/2.1.6.RELEASE/api/org/springframework/boot/test/context/SpringBootTest.html) and the [test annotations for testing a particular slice of your application](https://docs.spring.io/spring-boot/docs/2.1.6.RELEASE/reference/html/boot-features-testing.html#boot-features-testing-spring-boot-applications-testing-autoconfigured-tests "46.3.8 Auto-configured Tests").
4.  Command line arguments.
5.  Properties from `SPRING_APPLICATION_JSON` (inline JSON embedded in an environment variable or system property).
6.  `ServletConfig` init parameters.
7.  `ServletContext` init parameters.
8.  JNDI attributes from `java:comp/env`.
9.  Java System properties (`System.getProperties()`).
10.  OS environment variables.
11.  A `RandomValuePropertySource` that has properties only in `random.*`.
12.  [Profile-specific application properties](https://docs.spring.io/spring-boot/docs/2.1.6.RELEASE/reference/html/boot-features-external-config.html#boot-features-external-config-profile-specific-properties "24.4 Profile-specific Properties") outside of your packaged jar (`application-{profile}.properties` and YAML variants).
13.  [Profile-specific application properties](https://docs.spring.io/spring-boot/docs/2.1.6.RELEASE/reference/html/boot-features-external-config.html#boot-features-external-config-profile-specific-properties "24.4 Profile-specific Properties") packaged inside your jar (`application-{profile}.properties` and YAML variants).
14.  Application properties outside of your packaged jar (`application.properties` and YAML variants).
15.  Application properties packaged inside your jar (`application.properties` and YAML variants).
16.  [`@PropertySource`](https://docs.spring.io/spring/docs/5.1.8.RELEASE/javadoc-api/org/springframework/context/annotation/PropertySource.html) annotations on your `@Configuration` classes.
17.  Default properties (specified by setting `SpringApplication.setDefaultProperties`).

[`SpringApplication` loads properties from `application.properties` files in the following locations and adds them to the Spring `Environment`:](https://docs.spring.io/spring-boot/docs/2.1.6.RELEASE/reference/html/boot-features-external-config.html#boot-features-external-config-application-property-files)
1.  A `/config` subdirectory of the current directory
2.  The current directory
3.  A classpath `/config` package
4.  The classpath root
The list is ordered by precedence (properties defined in locations higher in the list override those defined in lower locations).

**附加说明：**

先加载YAML后加载properties。如果相同的配置存在于两个文件中，最后会使用properties中的配置，最后读取的会覆盖先读取的。

### 自定义配置文件加载顺序
