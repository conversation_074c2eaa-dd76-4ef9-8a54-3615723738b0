ENV: local
BEES360_SECRET_KEY: notset

spring:
  datasource:
    username: root
    password: 123456

mail:
  recipient-filter:
    regexInclude:
      - .*@9realms\.co
  topic-recipients:
    projectReceiveError:
      - yang<PERSON><PERSON><PERSON>@9realms.co
    projectExportScheduled:
      - liu<PERSON><EMAIL>
  senders:
    backend-mail-sender: <EMAIL>
    do-not-reply-mail-sender: <EMAIL>
    client-mail-sender: <EMAIL>
spring.mail:
  host: smtp.163.com
  username: <EMAIL>
  password: ENC(RABJZOydX+t8ICL/tDQpiXYtdPzxaP7N2VnS4NBK//Y=)
  protocol: smtp
  port: 587
  default-encoding: UTF-8
  properties:
    mail:
      debug: true
      smtp:
        auth: true
        connectiontimeout: 60000
        timeout: 120000
        writetimeout: 60000
        ssl:
          enable: true
          trust: smtp.163.com
        starttls:
          enable: true
          required: true
          socketFactory.class: javax.net.ssl.SSLSocketFactory
      display:
        sendmail: <EMAIL>
        sendname: Bees360
  # sender（发送人名称）是自定义属性
  sender: Bees360-LOCAL<<EMAIL>>
  # subtitle-prefix 自定义属性
  subtitle-prefix: "[Bees360-LOCAL]"


spring.mail-no-reply:
  host: smtp.163.com
  username: <EMAIL>
  password: ENC(RABJZOydX+t8ICL/tDQpiXYtdPzxaP7N2VnS4NBK//Y=)
  protocol: smtp
  port: 587
  default-encoding: UTF-8
  properties:
    mail:
      debug: true
      smtp:
        auth: true
        connectiontimeout: 60000
        timeout: 120000
        writetimeout: 60000
        ssl:
          enable: true
          trust: smtp.163.com
        starttls:
          enable: true
          required: true
          socketFactory.class: javax.net.ssl.SSLSocketFactory
      display:
        sendmail: <EMAIL>
        sendname: Bees360
  # sender（发送人名称）是自定义属性
  sender: Bees360-LOCAL<<EMAIL>>
  # subtitle-prefix 自定义属性
  subtitle-prefix: "[Bees360-LOCAL]"

spring.mail-backend:
  host: smtp.163.com
  username: <EMAIL>
  password: ENC(RABJZOydX+t8ICL/tDQpiXYtdPzxaP7N2VnS4NBK//Y=)
  protocol: smtp
  port: 587
  default-encoding: UTF-8
  properties:
    mail:
      debug: true
      smtp:
        auth: true
        connectiontimeout: 60000
        timeout: 120000
        writetimeout: 60000
        ssl:
          enable: true
          trust: smtp.163.com
        starttls:
          enable: true
          required: true
          socketFactory.class: javax.net.ssl.SSLSocketFactory
      display:
        sendmail: <EMAIL>
        sendname: Bees360
  # sender（发送人名称）是自定义属性
  sender: Bees360-LOCAL<<EMAIL>>
  # subtitle-prefix 自定义属性
  subtitle-prefix: "[Bees360-LOCAL]"
# mail end

# ----------------------------------------------------
# 自定义属性

# 配置跨域
cors:
  inner:
    allowed:
      # ant path matcher
      origins:
        - http://localhost:*
        - http://192.168.20.*:*
        - http://192.168.10.*:*
        - http://127.0.0.1:*
        - http://tolocalhost.com:*
# cors end

# ====================================
# bees360

# the number of download image thread number
bees360.web.images.download.thread-num: 30

bees360:
  business:
    customer:
      company:
        name: Pilot
      contract:
        signed: false
  domain: http://127.0.0.1:4000
  login:
    page:
      url: /login
  pilot:
    files: /files/pilot
  scheduled:
    config:
      # 是否打开定时器
      execute-enable: true
      # 是否为分布式定时器
      distributed-enable: false
  company-config:
    companies:
      -
        # IRS
        id: 2096
        # client received through openapi
        auto-client-received: false
        report:
          # 1024进制的10MB
          compressed-size: 10000000
          generate-closeout-report: true
      -
        # Swyfft Underwriting
        id: 2357
        report:
          compressed-size: 4500000
          generate-closeout-report: true
          closeout-report:
            case-type-prefix: Swyfft Residential
        auto-client-received: false
        open-api:
          projectCreationPreHandler: updatePolicyEffectedDateIfPolicyExists
      -
        # Centauri Insurance
        id: 2331
        report:
          compressed-size: 10000000

      -
        # Security First Florida
        id: 2339
        auto-client-received: false
        report:
          generate-closeout-report: true
      -
        # UPC Insurance
        id: 2332
      -
        # Plymouth Rock
        id: 2335
        shop-code: Plymouth Rock
      -
        # Bees360 Auto Test
        id: 2323
        shop-code: UPC
      -
        # American Family
        id: 1748
      -
        # Olympus Insurance
        id: 2761
        shop-code: Olympus
        report:
          generate-closeout-report: true
      -
        # Utica
        id: 2749
        email-recipient-map:
          "[report_client_received]":
            - recipient-first-name: Uticafirst
              recipient-last-name: Inspections
              recipient-email: <EMAIL>
          "[report_approved]":
            - recipient-first-name: Uticafirst
              recipient-last-name: Inspections
              recipient-email: <EMAIL>
          "[report_generated]":
            - recipient-first-name: Uticafirst
              recipient-last-name: Inspections
              recipient-email: <EMAIL>
          "[reportApproved]":
            - recipient-first-name: Uticafirst
              recipient-last-name: Inspections
              recipient-email: <EMAIL>

  watcher:
    claim-paid: <EMAIL>
    adjuster-email: <EMAIL>
  ibees:
    inspectionLinkTemplate: http://dev.ibees.app/{code}?platform=web
  mail:
    sending:
      enabled: false
  company-invoice:
    enabled: false

# web service
logging:
  level:
    org.springframework.ws.server.endpoint.interceptor.PayloadLoggingInterceptor: DEBUG
    org.springframework.ws.soap.server.endpoint.interceptor.SoapEnvelopeLoggingInterceptor: DEBUG
    com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor: INFO
    org.springframework.web: DEBUG
    org.springframework.ws.client.MessageTracing.sent: DEBUG
    org.springframework.ws.server.MessageTracing.sent: DEBUG
    org.springframework.ws.client.MessageTracing.received: TRACE
    org.springframework.ws.server.MessageTracing.received: TRACE
send-ad-email-company-id: -1

company-id-map:
  Insurance_Risk_Services_Inc_ID: 2096
  Associated_Services_Inspections_Commercial_ID: 1313
  Allstate_ID: 1736
  # Swyfft Homeowners Insurance
  Swift_ID: 2294
  Allied_Trust_ID: 2382
  Velocity_ID: 2510
  Swyfft_Underwriting: 2357
  Swyfft_Homeowner_Insurance: 2294
  UPC_Insurance: 2315
  Centauri_Insurance: 2742
  Security_First_Florida: 2752
  GeoVera_Holdings_Underwriting: 2751
  Olympus: 2761
  SageSure: 2756
  Mdow: 2784
  Canopius: 2776

firebase:
  badge:
    underwritingBadgeId: 0DK31wvi8q5TMBqGpcEx
    claimBadgeId: 2tuDPBjAOM28IlKhtmrA
    magicplanBadgeId: 7RJ6CGsDVE2sfRTBCLwl
resource:
  client:
    base-url: http://dev-resource.bees360.com/resource


hover:
  backend:
    # disable the hover because the server will get the hover info from firebase, and the firebase will manage the hover info
    auto-refresh-token: false
  redirect-url: notset
auth:
  jwt:
    signingKey: ENC(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)
    verifierKey: "-----BEGIN CERTIFICATE-----\nMIIC/DCCAeSgAwIBAgIIOcybo4ESpmMwDQYJKoZIhvcNAQEFBQAwIDEeMBwGA1UE\nAxMVMTAzNzQxMjAwMzE3OTIwODg2ODQ1MCAXDTIxMDcyNzAzNDgxM1oYDzk5OTkx\nMjMxMjM1OTU5WjAgMR4wHAYDVQQDExUxMDM3NDEyMDAzMTc5MjA4ODY4NDUwggEi\nMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDnpfrDqFClLoVqAg9GcM0sEmIR\ni7BP+gC8KautIhPyrScuujFHZf8MHP1AezjVd2gcWCi1pGTTNUbeZH2xet5qqP/X\nGc2OsLFh9vAGOXQz1WtJWENv4ncazZu8uBeurVyJrNIaY0G3v3JYAUHKi/Wu0GCy\nt0bSwVYVlzH8IOwlZezuUmsoWMX56eI0CFrfA2WuGwvMJQzB7daN6XB68hGt+WFE\n18xXeJCKmyjhaFoY/KpWO1Z0g8/gLYLkPrC/oa6tNigENaK7aQmaPCJ3phwiDz2a\nWPNJ8evXMGVFIE5qUHG3aNEpA2Rru4pFA9+g3ZfjIu8VnQh1kLmL5dtw0x4pAgMB\nAAGjODA2MAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgeAMBYGA1UdJQEB/wQM\nMAoGCCsGAQUFBwMCMA0GCSqGSIb3DQEBBQUAA4IBAQCTlQLQOUEr5PHvG1Y9z7Hc\n+6MXFb7WxiRM7IcSEPA0BwkZM94BF5fojlHZ2ox335669wrGqGpkNLBqIMLuxw1b\n4N5wxeeS2M8fZeUjM7kaHT8ZE+27FzOY1NNbFn+dyUqonGM/1GlGL3hgOV4hkyzk\n3vderqQakqR7bYyUmjCgBcAvQahOpkJxX4/XgqzaDQBDGmhG1VOvyTJEyZtBX1GN\nnmsz9CzqClBgydf6bnjgm4Gh4CHI4YKL/7yGXAkk50JqYbhkAmlNcIbILryilTRu\nMZGX7oskns0vBqMtLmIPG5eH/KL3KqUS1KTxW33Ucca+WnmKQjNys2QKBVLR1Y+F\n-----END CERTIFICATE-----\n"
  clients:
    -
      clientId: bees360-com
      clientSecret: bees360-com-secret
      scope: bees360.com
      authorizedGrantTypes: ["password", "refresh_token"]
      accessTokenValiditySeconds: 604800
      refreshTokenValiditySeconds: 864000
    -
      clientId: swyfft_underwriting
      clientSecret: 7654321a+
      scope: swyfft_underwriting
      authorizedGrantTypes: ["password", "refresh_token"]
      accessTokenValiditySeconds: 600
      refreshTokenValiditySeconds: 86400
grpc:
  client:
    userProvider:
      address: static://bees360-user-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

rabbit:
  client:
    username: mq_username
    password: mq_password

# square payment
payment:
  square:
    application-id: notset
    access-token: notset
    location-id: notset
  stripe:
    secret-api-key: notset
    publishable-key: pk_notset

houseboundary:
  python: /var/bees360/www/process/BR.py
message:
  event:
    imageupload:
      recipients:
        email: ''
        sms: ''
  operations:
    recipients: ''
osm:
  nominatim:
    header:
      email: <EMAIL>
    server:
      url: https://nominatim.openstreetmap.org/
service:
  client:
    email: <EMAIL>
  contactus:
    recipients: ''
  partnerprogram:
    recipients: ''
system:
  feedback:
    engineer:
      emails: ''
tmp:
  dir:
    download:
      images: /var/bees360/tmp/download/images
    upload: /var/bees360/tmp
    upload.roster: /roster-user-resume
watch:
  project:
    create:
      emails: ''
    watcher:
      recipients: ''

rct:
  endpoint: http://localhost/rct
  auth:
    disable: true
    id: username
    secret: password
  dataset:
    id: 0
    contract: 0
    creator: 0
