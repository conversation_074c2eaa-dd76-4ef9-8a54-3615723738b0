#message_en.properties

####### message code for public 1-1000 #####
1=Internal Server Error.
2=Database exception.
3=Failed to send email.
4=Failed to connect AWS.
6=Data type does not exist.
7=Parameter is invalid.
8=The service does not exist. Please contact Customer Service for <NAME_EMAIL> or (281) 766-4398.

100=Failed to download file from AWS S3.
102=Failed to upload images to AWS S3.
103=Failed to upload images archive to AWS S3.
104=Failed to archive images. Please try again later.

400=Bad Request.
401=Failed to find JWT token.
402=Invalid JWT token.
403=The server understood the request but refuses to authorize it.
404=JWT token is timeout.

## for request error
700=Please slow down your operation.
701=Validation Failed.
703=Data doesn't Exist.
705=Data existed.

3016=Error saving picture.

####### message code for user 1001-2000 #####
1001=This account is taken. Please try another one.
1002=This username is already taken. Please try another one.
1003=This phone number is already taken. Please try another one.
1004=This email address is already taken. Please try another one.
1005=This account does not exist.
1007=Invalid email or phone number.
1008=That phone number or email need to be provided.
1009=Invalid password.
1011=The password is incorrect.
1012=Invalid email or phone number.
1013=The username or password is incorrect. Please try again.
1014=Inactive account.
1016=The account does not exist.
1017=Failed to invite policy holder to this project.
1018=Invalid phone number.
1019=Invalid email address.
1020=This account was deleted.
1021=Password does not match the record. Please try again.
1022=The password can contain any characters, but must contain at least an alphabet and a number, and the length must be at least 8 characters.
1023=First name or last name can not be empty.
1024=First name or last name is too long.
1025=Input at least the phone number or email address.
1026=Employee id is too long.
1027=This role has already assigned to this account.
1028=Customer is applying for the role.
1029=Address is too long.
1030=Zipcode is too long.
1031=This account does not have this role.
1032=This phone number is already taken. Please try another one.
1033=This email is already taken. Please try another one.
1034=Either phone number or email needs to link this account.
1035=No email is linked with this account.
1036=No phone number is linked with this account.
1038=Failed to run redis.
1040=This operation is not allowed.
1041=Email cannot be empty.
1042=Pilots are not allowed to log in on Bees360Web.
1043=Single Sign-On is enabled for this account. Please click the 'Login with SSO' link to log in.
####### message code for user 2001-3000 #####
2001=This company name has already existed. Please try another one.
2002=This company name does not exist.
2003=This insurance company does not exist.
2004=This roofing company does not exist.

3001=Failed to upload images to AWS S3.
3002=Invalid Project type.
3004=Invalid Claim type.
3005=Claim number cannot be modified.
3006=Inspection time is invalid.
3008=The image does not exist.
3010=Failed to set this image to be the overview image.
3011=No overview image in this project.
3012=There are at least two overview images in this project.
3013=Overview image dosen't exist.
3014=Invalid image type.
3015=There are no images.
3017=The serviceType can be updated only once.
3018=Only one address verification image is allowed.
3019=There are no images to upload.
3020=Image is used in report.

3100=The project dosen't exist.
3101=Project can not operate.

4001=Failed to send the message.

5002=Cannot remove member.
5003=Cannot schedule member.
5004=Role does not exist.
5008=Data format error.
5009=The security code is incorrect.
5010=The security code is invalid.
5011=Failed to deactivate your account because one of your projects is in process.
5012=This user is already one of the members in this project.
5013=This role can't be assigned.
5014=The project already has member of this role.
5015=This user is not one of the members in this project.
5016=Archive file does not exist.
5017=Duplicated projects exist.

6001=Failed to make a 3d-reconstruction grpc call to bees360-ai.
6002=Please follow the process of AI. This is the not the next step.
6003=This operation is not allowed.
6004=Status error.
6005=AI is running.
6007=The AI is still in process.
6008=Generating report now.
6009=Failed to process AI.
6010=AI process failed.
6011=You must select at least one facet.
6012=At least five images are required.
6013=Failed to generate screenshots.
6101=AD has started but not finished yet.
6103=The image type is not compatible with AD. Failed to process.
7000=Failed to commit Ranging to Hadoop.
7001=Failed to commit Scoping to Hadoop.
7002=Failed to commit PrePlane to Hadoop.
7003=Failed to commit Boundary to Hadoop.
7004=Failed to commit postBoundary to Hadoop.

####### message code for payment 8001-8999 #####
8001=Payer can not to be null.
8002=Please select payment method.
8003=The parameter cardNonce is required.
8004=Please order the service. Order cannot be empty.
8005=Invalid payment amount.
8006=Failed to pay.
8007=Remaining balance is insufficient.
8008=Payment method is invalid.
8010=Failed to pay.
8011=Please input the name.
8012=Our price has changed. Please refresh your page.
8013=The service is invalid.
8014=Project ID does not match.
8015=The report price is not determined yet.

# report
8999=Report should to be compressed, but it is not. Compression should finish in 30 minutes. Otherwise ask for support from engineers.
9001=Some images are missing to generate this report.
9002=Please select the company in Profile page. Otherwise, the logo will not display in the report.
9003=Report will not show the company logo as it is not provided.
9004=Report is not generated yet.
9005=Report is approved.
9006=Report is disapproved.
9007=No sufficient images to generate the report.
9008=The report is not submitted yet.
9009=Report can only be submitted once.
9010=Some images are missing to generate this report.
9011=Some images are missing to generate this report.
9012=Data format is wrong.
9013=Some images are missing to generate this report.
9014=This report cannot be canceled.
9015=Unit price must be greater than or equal to 0.
9016=Price does not match.
9017=The report is being generated.
9018=The report is being generated, please go to the report page and wait.
9020=Image has not been generated.
9021=Cannot approve report because this project is cancelled.

10001=The facet doesn't exist.
10054=This role is not allowed to apply.
10055=This role is not allowed to apply.
10056=AD has started.

10057=Overview image does not exist.

12001=This service is not offered.
12002=Please input contact name.
12003=Please input email.
12004=Please input phone number.
12005=Cannot get access to the property data.

12010=Failed to generate report.
12020=Two or more Review windows are currently open. Please refresh this page.
13000=Cannot remove Drone Pilot service from your order currently.

## product
1220001=The product doesn't exist.

## price
1230001=The price is not determined yet.

## transaction
1240001=This discount is not available.
1240002=Invalid discount for this account.
1240003=Invalid payment method.
1240004=The price does not match. Please refresh the page.
1240005=Invalid payment method.
1240006=Remaining balance is insufficient.
1240007=Invalid discount.
1240008=The product has already been paid.
1240020=Failed to pay. Please try again.
