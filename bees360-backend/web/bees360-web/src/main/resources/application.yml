# profile
server:
  #  address: 127.0.0.1
  port: 8080
  servlet:
    # Keep the original url path
    context-path: /bees360-web

spring:
  main:
    allow-circular-references: true
  profiles:
    active: ${ENV:local}
    include: actuator
  application:
    name: bees360-web
  transaction:
    defaultTimeout: 100
  # This property is deprecated: DispatcherServlet property is deprecated for removal and should no longer need to be configured
    # throw-exception-if-no-handler-found: true
  servlet:
    # File size limit for file upload
    multipart:
      max-file-size: 30MB
      max-request-size: 100MB
  jackson:
    serialization:
      # Starting from SpringBoot 2.0, when converting Java objects to JSON, the Date type field will be automatically converted to UTC format. You can notify the configuration to modify it to timestamp (milliseconds)
      write-dates-as-timestamps: true
  thymeleaf:
    prefix: classpath:message/thymeleaf/
    mode: HTML
    encoding: UTF-8
  # This property is deprecated and will be removed in future Spring Boot versions
        # This property is deprecated and will be removed in future Spring Boot versions
        # Maximum blocking waiting time of the connection pool (negative value means no limit) Default is -1
        # max-wait: -1ms
        # This property is deprecated and will be removed in future Spring Boot versions
        # Minimum number of idle connections in the connection pool. Default is 0
        # This property is deprecated and will be removed in future Spring Boot versions
        # Maximum number of idle connections in the connection pool. Default is 8
        # This property is deprecated and will be removed in future Spring Boot versions
        # Maximum number of connections in the connection pool (negative value means no limit) Default is 8
        # max-active: 100
        # Maximum number of idle connections in the connection pool. Default is 8
        # max-idle: 100
        # Minimum number of idle connections in the connection pool. Default is 0
        # min-idle: 0
        # This property is deprecated and will be removed in future Spring Boot versions
        # Maximum blocking waiting time of the connection pool (negative value means no limit) Default is -1
        # max-wait: -1ms
        # time-between-eviction-runs: 30000
  datasource:
    username: mysql_username
    url: ******************************************************************************************************************************************************************
    driver-class-name: org.mariadb.jdbc.Driver

    # HikariCP configuration
    hikari:
      minimum-idle: 5        # Minimum number of idle connections
      maximum-pool-size: 30  # Maximum number of connections in the pool
      idle-timeout: 30000    # Maximum amount of time that a connection can sit idle in the pool
      pool-name: SpringHikariCP  # The name of the connection pool
      max-lifetime: 2000000  # Maximum lifetime of a connection in the pool
      connection-timeout: 60000  # The maximum number of milliseconds that the driver will wait while trying to connect to the database
      connection-test-query: SELECT 1
  web.locale: en
  web.locale-resolver: fixed
  web.resources.add-mappings: false
  data.redis.database: 0
  data.redis.host: redis
  data.redis.lettuce.shutdown-timeout: 200ms
  data.redis.port: 6379
  data.redis.timeout: 5000  # SQL query to test connections

# redis end
# Configure mybatis
mybatis:
  config-location: classpath:config/mybatis-config.xml
  mapper-locations: classpath*:mappings/**/*.xml
# mybatis end

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    password: ${BEES360_SECRET_KEY}
    property:
      prefix: ENC(
      suffix: )
# jasypt end

# ----------------------------------------------------
# Custom properties

# Cross-domain For specific configurable items, see: com.bees360.config.properties.bean.CorsProperties.java
cors:
  inner:
    allowed:
      # origins are set by the specific profile configuration file
      origins:
      methods: GET,POST,HEAD,OPTIONS,PUT,DELETE,PATCH
      headers: Authorization,Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers
      credentials: true
    exposed:
      headers: Authorization,Access-Control-Allow-Origin,Access-Control-Allow-Credentials,X-Protobuf-Message
    max-age: 86400
  open:
    allowed:
      origins: '*'
      methods: GET,POST,HEAD,OPTIONS,PUT,DELETE,PATCH
      headers: Authorization,Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers
      credentials: false
    exposed:
      headers: Access-Control-Allow-Origin,Access-Control-Allow-Credentials
    max-age: 86400
# cors end

payment:
  stripe:
    #    publishable-key: ENC() # see secrets-${ENV}.properties
    #    secret-api-key: ENC()  # see secrets-${ENV}.properties
    minimum-amount: 50
    maximum-amount: 99999999
    currency: usd

grpc:
  server:
    port: 9899
  client:
    projectPolicyManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    activityManager:
      address: 'static://bees360-activity-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    commentManager:
      address: 'static://bees360-activity-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectTagManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    resourcePool:
      address: static://bees360-resource-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    groupManager:
      address: static://bees360-user-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    addressManager:
      address: static://bees360-address-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    addressFlyZoneTypeManager:
      address: static://bees360-address-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    pipelineService:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    pipelineDefService:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectPipelineConfigService:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectIIManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    policyManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    customerPolicyTypeManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    contractManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    buildingManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    hiveLocationManager:
      address: static://bees360-address-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectContactManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectCatastropheManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    customerManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    imageManager:
      address: static://bees360-image-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    reportManager:
      address: static://bees360-report-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectStatusManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectMemberManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectParticipantManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    operationsManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    pilotFeedbackManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectOperationTagManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    ssoRegistrationProvider:
      address: 'static://bees360-customer-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    contactRecordManager:
      address: 'static://bees360-project-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectStateManager:
      address: 'static://bees360-project-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    beespilotBatchClient:
      address: 'static://bees360-project-app-grpc:9898'
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectStateChangeReasonManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    stateChangeReasonGroupManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectDaysOldProvider:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectInvoiceManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectPaymentManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectIntegration:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    userKeyProvider:
      address: static://bees360-user-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectInformationManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    similarProjectProvider:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    divisionManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    userManager:
      address: static://bees360-user-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    genericProjectCreator:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectGroupManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    roleManager:
      address: static://bees360-bifrost-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    roleProvider:
      address: static://bees360-bifrost-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    roleAssignmentManager:
      address: static://bees360-bifrost-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    roleAssignmentProvider:
      address: static://bees360-bifrost-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    bifrostUserProvider:
      address: static://bees360-bifrost-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    bifrostUserManager:
      address: static://bees360-bifrost-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectCancellationManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    projectReportJobManager:
      address: static://bees360-project-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
    apiKeyManager:
      address: static://bees360-openapi-app-grpc:9898
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext

bees360:
  grpc:
    server:
      port: 9898
      retry:
        max-interval: 180
        # retry 10 times
        times: 10
      # the number of thread to transfer images
      images-transfer-thread-num: 10
    servers:
      ai:
        endpoints: bees360ai-grpc:9898
        maxRetryAttempts: 3
        keepAliveTime: 480
        keepAliveTimeout: 60
        initialBackoff: 0.5
        maxBackoff: 1
        backoffMultiplier: 2
      threed:
        endpoints: bees360threed-grpc:9898
        maxRetryAttempts: 3
        keepAliveTime: 480
        keepAliveTimeout: 60
        initialBackoff: 0.5
        maxBackoff: 1
        backoffMultiplier: 2
  security:
    oauth2:
      resource-path-pattern: /v1/**
      endpoint-urls:
        token-endpoint-url: /v1/oauth/token
        authorization-endpoint-url: /v1/oauth/authorize
        checkToken-endpoint-url: /v1/oauth/check
  scheduled:
    config:
      # Whether to turn on the timer
      execute-enable: true
      # Is it a distributed timer?
      distributed-enable: true
    enable:
      # Disable the automatic setting of the ImageUpload timer to prevent affecting the normal Project processing flow
      swyfft-project-daily-summary: false
  company-config:
    global:
      auto-client-received-config:
        service-type-exclude:
          - 11 # Scheduling Only
  # 账单设置
  billing:
    invoice:
      description: We really appreciate your business.
    recipient:
      addressLine1: 2825 Wilcrest Drive
      addressLine2: Suite 270
      city: Houston
      state: TX
      zipCode: 77042
      country: United States
      phone: ************
      email: <EMAIL>
      companyName: Bees360 Inc
      logo: common/Bees360_Logo.png
      website: www.bees360.com
  web:
    network:
      retry-times: 5
      retry-interval: 5

#log config
logging:
  config: classpath:config/logger/log4j2.yml

rct:
  client:
    request:
      connectTimeout: PT60S
      callTimeout: PT180S
      followRedirects: true
      retryOnConnectionFailure: true
    service-type-mapping:
      default-value: WHITE_GLOVE

rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password

redis:
  client:
    host: redis
    port: 6379
    database: 0
  locks:
    keyPrefix: lock/
    expirationTime: PT1S

springdoc:
  default-produces-media-type: application/json
  api-docs:
    enabled: true # Whether to enable the springdoc openapi endpoint. Default is true
    path: /openapi/v3/api-docs #springdoc openapi endpoint path default /v3/api-docs
  swagger-ui:
    enabled: true # Whether to enable swagger ui. Default is true
    path: /openapi/swagger-ui/index.html # Customize swagger access path. Default is swagger-ui.html

# http start
http:
  client:
    connect-timeout: 60000
    socket-timeout: 300000
    connection-request-timeout: 300000
    max-total: 200
    max-per-route: 20
    # httpclient configuration of resource server
    maxConnPerRoute: 16
    maxConnTotal: 64
    evictIdleConnectionsAfter: PT10M
    connectionTimeToLive: PT10M
    evictExpiredConnections: true
    redirectStrategy: always
    request:
      connectTimeout: PT15S
      connectionRequestTimeout: PT30S
      socketTimeout: PT15S
      redirectEnabled: true
      expectContinueEnabled: true
  request:
    access-security:
      companyStrategy:
        realms9: 2770
        forbiddenCompanyList: 1742
# http end

report:
  resource:
    prefix: report/
  # Report global configuration
  reportItemProperties:
    - # DAR
      report_type: 1
      compressed_size_limit: 15000000
    - # PIR
      report_type: 15
      compressed_size_limit: 15000000
  check:
    # do not generate closeout report condition
    closeout-ignore-condition:
      ROOF_ONLY_UNDERWRITING: [ROOF_ONLY_UNDERWRITING_REPORT]
      EXTERIOR_UNDERWRITING: [FULL_SCOPE_UNDERWRITING_REPORT]
      FOUR_POINT_UNDERWRITING: [FULL_SCOPE_UNDERWRITING_REPORT]
      FOUR_POINT_SELF_UNDERWRITING: [FULL_SCOPE_UNDERWRITING_REPORT]
      PREMIUM_FOUR_POINT_UNDERWRITING: [FULL_SCOPE_UNDERWRITING_REPORT]
      EXPRESS_UNDERWRITING: [EUR]
      WHITE_GLOVE: [FULL_SCOPE_UNDERWRITING_REPORT]


thread:
  async:
    maxPoolSize: 1
    corePoolSize: 1
    queueCapacity: 100
    waitTimeAfterRejected: 400
    maxRejectedTimes: 10

hover:
  job:
    customer-name: <EMAIL>
    customer-email: <EMAIL>

auth:
  passwordEncoder: custom



quartz:
  scheduler:
    compress-report:
      name: compress-report-scheduler
      thread-count: 10

project:
  state:
    # map: change-reason-key to label(ProjectLabelEnum)
    change-reason-key-to-label:
      "CANCELLATION CONFIRMED": CANCELLATION_CONFIRMED
      "CANCELLATION UNCONFIRMED": CANCELLATION_UNCONFIRMED
      "DENIED": DENIED
      "DENIED ON LOCATION": DENIED_ON_LOCATION
      "INSURED DECLINES DRONE INSPECTION": DENIED_ON_LOCATION
      "INSURED WITHDRAWS CLAIMS": DENIED_ON_LOCATION
      "INSURED REFUSES TO SCHEDULE": DENIED_ON_LOCATION
      "UNABLE TO REACH INSURED AFTER MULTIPLE ATTEMPTS": UNABLE_TO_REACH_INSURED_AFTER_MULTIPLE_ATTEMPTS
  service-upgrade:
    reopen-change-reason: "Requested by Client"
    update-status-image-uploaded-if-not-in: [ PROJECT_CREATED, CUSTOMER_CONTACTED, ASSIGNED_TO_PILOT, SITE_INSPECTED, IMAGE_UPLOADED, PROJECT_REWORK ]

image:
  solid:
    sync: true

bees360.com.system-config:
  # Image page url template
  image-page-template: projects/{projectId}/images
  # Report page url template
  report-page-template: projects/{projectId}/reports

pipeline:
  om-change:
    target-tasks: [ upload_drone_images, upload_exterior_images ]
    enabled: true

# ----------------------- logbook ---------------------
logging.level.org.zalando.logbook: TRACE

logbook:
  include:
    - /bees360-web/v1/**
  exclude:
    - /bees360-web/v1/oauth/**
  filter.enabled: true
  secure-filter.enabled: true
  format.style: json
  strategy: default
  obfuscate:
    headers:
      - X-Secret
    parameters:
      - access_token
      - refresh_token
      - password
  write:
    max-body-size: 101024

options:
  service-type:
    hiding: 8
  supplemental-service:
    type-list: ["RC Report", "Sinkhole", "Wildfire"]

http.request:
  matchers:
    # Users
    - method: "GET"
      path: "/users/{userId:\\d+}"
      access-rule: "hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMyself(authentication, #userId)"
    - method: "PUT"
      path: "/users/{userId:\\d+}"
      access-rule: "hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMyself(authentication, #userId)"
    - method: "PATCH"
      path: "/users/{userId:\\d+}/name"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "PUT"
      path: "/users/{userId:\\d+}/phone"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "GET"
      path: "/users/{userId:\\d+}/pilot"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "GET"
      path: "/users/{userId:\\d+}/user-view"
      access-rule: "hasAnyRole('SALESMAN', 'ADMIN')"
    - method: "PATCH"
      path: "/users/{userId:\\d+}/status"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "DELETE"
      path: "/users/{userId:\\d+}/roles"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "POST"
      path: "/users/{userId:\\d+}/roles"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "PATCH"
      path: "/users/{userId:\\d+}/roles"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "PATCH"
      path: "/users/{userId:\\d+}/roleApplication"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "PUT"
      path: "/users/{userId:\\d+}/discount"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "PUT"
      path: "/users/{userId:\\d+}/new-user-discount"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "GET"
      path: "/users/roles"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "GET"
      path: "/users/pilots"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "GET"
      path: "/users/fullinfo"
      access-rule: "hasAnyRole('SALESMAN', 'ADMIN')"
    - method: "GET"
      path: "/users/user-list"
      access-rule: "hasAnyRole('SALESMAN', 'ADMIN')"
    - method: "PUT"
      path: "/users/{userId:\\d+}/rating"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "*"
      path: "/users/{userId:\\d+}/**"
      access-rule: "hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMyself(authentication, #userId)"
    # user company info
    - method: "GET"
      path: "/companies/user/company"
      access-rule: "authenticated"
    # Activity
    - method: "GET"
      path: "/project/{projectId:\\d+}/activity/**"
      access-rule: "(hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectAccessible(#projectId)"
    - method: "GET"
      path: "/project/{projectId:\\d+}/comment/**"
      access-rule: "(hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectAccessible(#projectId)"
    - method: "*"
      path: "/project/{projectId:\\d+}/activity/**"
      access-rule: "(hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectAccessible(#projectId) and @PAS.isProjectOperable(#projectId)"
    - method: "*"
      path: "/activity/**"
      access-rule: "isAuthenticated()"
    - method: "*"
      path: "/project/{projectId:\\d+}/comment/**"
      access-rule: "(hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectAccessible(#projectId) and @PAS.isProjectOperable(#projectId)"
    - method: "*"
      path: "/comment/**"
      access-rule: "isAuthenticated()"

    # Projects
    - method: "DELETE"
      path: "/projects/{projectId:\\d+}"
      access-rule: "(hasAnyRole('ADMIN') or @SCAC.isMemberWith(authentication, #projectId, 'CREATOR') or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectOperable(#projectId)"
    - method: "GET"
      path: "/projects/{projectId:\\d+}/logs"
      access-rule: "hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMemberWith(authentication, #projectId, 'PROCESSOR', 'REVIEWER')"
    - method: "POST"
      path: "/projects/{projectId:\\d+}/status/pilotcheckedin"
      access-rule: "hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)"
    - method: "POST"
      path: "/projects/{projectId:\\d+}/status/pilotcheckedout"
      access-rule: "hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)"
    - method: "POST"
      path: "/projects/{projectId:\\d+}/statuses/customer-contacted"
      access-rule: "(hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectOperable(#projectId)"
    - method: "POST"
      path: "/projects/{projectId:\\d+}/statuses/returned-to-client"
      access-rule: "(hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectOperable(#projectId)"
    - method: "POST"
      path: "/projects/{projectId:\\d+}/service"
      access-rule: "(hasAnyRole('ADMIN') or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectOperable(#projectId)"
    - method: "POST"
      path: "/projects/{projectId:\\d+}/images/zip-file"
      access-rule: "hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)"
    - method: "DELETE"
      path: "/projects/{projectId:\\d+}/images/delete-completely"
      access-rule: "hasAnyRole('ADMIN') and @PAS.isProjectOperable(#projectId)"

    # Project AI Process
    - method: "POST"
      path: "/projects/{projectId:\\d+}/aiProcess/scopingTwo"
      access-rule: "hasAnyRole('ADMIN','SALESMAN') or @SCAC.isMemberWith(authentication, #projectId, 'PROCESSOR', 'REVIEWER')"
    - method: "POST"
      path: "/projects/{projectId:\\d+}/aiProcess/segmentation"
      access-rule: "hasAnyRole('ADMIN','SALESMAN') or @SCAC.isMemberWith(authentication, #projectId, 'PROCESSOR', 'REVIEWER')"
    - method: "POST"
      path: "/projects/{projectId:\\d+}/aiProcess/boundary/images"
      access-rule: "hasAnyRole('ADMIN','SALESMAN') or @SCAC.isMemberWith(authentication, #projectId, 'PROCESSOR', 'REVIEWER')"
    - method: "POST"
      path: "/projects/{projectId:\\d+}/aiProcess/boundary"
      access-rule: "hasAnyRole('ADMIN','SALESMAN') or @SCAC.isMemberWith(authentication, #projectId, 'PROCESSOR', 'REVIEWER')"

    # Project Reports
    - method: "DELETE"
      path: "/projects/{projectId:\\d+}/reports/{reportId}"
      access-rule: "(hasAnyRole('ADMIN') or @SCAC.isMemberWith(authentication, #projectId, 'PROCESSOR', 'REVIEWER', 'ADJUSTER')) and @PAS.isProjectOperable(#projectId)"
    - method: "PUT"
      path: "/projects/{projectId:\\d+}/reports/{reportId}/submitted"
      access-rule: "(hasAnyRole('ADMIN') or @SCAC.isMemberWith(authentication, #projectId, 'PROCESSOR', 'REVIEWER', 'ADJUSTER') or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectOperable(#projectId)"
    - method: "PUT"
      path: "/projects/{projectId:\\d+}/reports/status/approved"
      access-rule: "(hasAnyRole('ADMIN') or @SCAC.isMemberWith(authentication, #projectId, 'REVIEWER', 'ADJUSTER') or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectOperable(#projectId)"
    - method: "PUT"
      path: "/projects/{projectId:\\d+}/reports/status/disapproved"
      access-rule: "(hasAnyRole('ADMIN') or @SCAC.isMemberWith(authentication, #projectId, 'REVIEWER', 'ADJUSTER') or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectOperable(#projectId)"
    - method: "POST"
      path: "/projects/{projectId:\\d+}/reports"
      access-rule: "hasAnyRole('ADMIN') and @PAS.isProjectOperable(#projectId)"

    # Project Invoices
    - method: "GET"
      path: "/projects/{projectId:\\d+}/invoices"
      access-rule: "hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)"
    - method: "GET"
      path: "/projects/{projectId:\\d+}/invoices/file"
      access-rule: "hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)"
    - method: "GET"
      path: "/projects/{projectId:\\d+}/invoices/{projectId:\\d+}/file"
      access-rule: "hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)"

    # Project App
    - method: "POST"
      path: "/app/projects/{projectId:\\d+}/segments/categoryTree"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "POST"
      path: "/app/projects/{projectId:\\d+}/segments/underwriting-item"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "PUT"
      path: "/app/projects/{projectId:\\d+}/segments/underwriting-item"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "GET"
      path: "/app/projects/{projectId:\\d+}/droneParameter"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "*"
      path: "/app/projects/{projectId:\\d+}/**"
      access-rule: "hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)"

    # Project Member
    - method: "PUT"
      path: "/projects/{projectId:\\d+}/member"
      access-rule: "(hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectAccessible(#projectId) and @PAS.isProjectOperable(#projectId)"
    - method: "PUT"
      path: "/projects/-/member/{userId:\\d+}/{roleId:\\d+}"
      access-rule: "hasAnyRole('SALESMAN', 'ADMIN', 'COMPANY_ADMIN', 'SCHEDULER')"

    # Project Other
    - method: "GET"
      path: "/projects/zeroLatLngs"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "POST"
      path: "/projects/latLngs"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "GET"
      path: "/projects/aiProcess"
      access-rule: "hasAnyRole('ADMIN', 'PROCESSOR')"
    - method: "PATCH"
      path: "/projects/chimney"
      access-rule: "hasAnyRole('ADMIN', 'PROCESSOR')"
    - method: "PUT"
      path: "/projects/{projectId:\\d+}/CII"
      access-rule: "hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)"

    # Project Default
    - method: "GET"
      path: "/projects/{projectId:\\d+}/**"
      access-rule: "(hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectAccessible(#projectId)"
    - method: "*"
      path: "/projects/{projectId:\\d+}/**"
      access-rule: "(hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectAccessible(#projectId) and @PAS.isProjectOperable(#projectId)"

    # Project
    - method: "GET"
      path: "/project/{projectId:\\d+}/**"
      access-rule: "(hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectAccessible(#projectId)"
    - method: "*"
      path: "/project/{projectId:\\d+}/**"
      access-rule: "(hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)) and @PAS.isProjectAccessible(#projectId) and @PAS.isProjectOperable(#projectId)"

    # Payment
    - method: "PATCH"
      path: "/payment/projects/{projectId:\\d+}/extraPaymentFee"
      access-rule: "hasAnyRole('ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)"
    - method: "POST"
      path: "/payment/users/{userId:\\d+}/newCustomerDiscountAndNum"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "POST"
      path: "/payment/users/{userId:\\d+}/discount"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "*"
      path: "/payment/users/{userId:\\d+}/**"
      access-rule: "hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMyself(authentication, #userId)"

    # Transaction
    - method: "*"
      path: "/transaction/projects/{projectId:\\d+}/**"
      access-rule: "hasAnyRole('SALESMAN', 'ADMIN') or @SCAC.isMemberIn(authentication, #projectId) or @SCAC.isManagedBy(authentication, #projectId)"

    # House
    - method: "GET"
      path: "/house/map"
      access-rule: "hasAnyRole('ADMIN')"

    # App
    - method: "POST"
      path: "/app/version"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "POST"
      path: "/preferences/company/template"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "POST"
      path: "/preferences/company/{companyId:\\d+}/template"
      access-rule: "hasAnyRole('ADMIN')"

    # Management
    - method: "*"
      path: "/management/users/{userId:\\d+}/roles/admin"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "DELETE"
      path: "/management/users/{userId:\\d+}"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "*"
      path: "/management/project/{projectId:\\d+}/status/cr"
      access-rule: "hasAnyRole('ADMIN') or @SCAC.hasAnyRole(authentication, 'COMPANY_ADMIN')"
    - method: "*"
      path: "/management/project/label"
      access-rule: "hasAnyRole('ADMIN') or @SCAC.hasAnyRole(authentication, 'COMPANY_ADMIN')"
    - method: "*"
      path: "/management/project/{projectId:\\d+}/**"
      access-rule: "@PAS.isProjectAccessible(#projectId) and @PAS.isProjectOperable(#projectId)"

    # Admin Only
    - method: "*"
      path: "/scheduling/**"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "*"
      path: "/management/**"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "POST"
      path: "/config/**"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "POST"
      path: "/init/**"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "*"
      path: "/temp/**"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "POST"
      path: "/test/**"
      access-rule: "hasAnyRole('ADMIN')"

    # Open API Secret
    - method: "POST"
      path: "/apis/credentials"
      access-rule: "@SCAC.hasAnyRole(authentication, 'COMPANY_ADMIN')"
    - method: "PUT"
      path: "/apis/credentials"
      access-rule: "@SCAC.hasAnyRole(authentication, 'COMPANY_ADMIN')"
    - method: "DELETE"
      path: "/apis/credentials"
      access-rule: "@SCAC.hasAnyRole(authentication, 'COMPANY_ADMIN')"

    # Project Static
    - method: "*"
      path: "/project/stat/**"
      access-rule: "@SCAC.hasAnyRole(authentication, 'ADMIN', 'COMPANY_ADMIN', 'ADJUSTER', 'PROCESSOR', 'REVIEWER')"

    # Pilot Badge
    - method: "POST"
      path: "/pilot/badge"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "*"
      path: "/connect/**"
      access-rule: "hasRole('ADMIN')"
    - method: "POST"
      path: "/message"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "PUT"
      path: "/hover/token"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "*"
      path: "/xa/**"
      access-rule: "hasAnyRole('ADMIN')"
    - method: "*"
      path: "/integration/**"
      access-rule: "hasAnyRole('ADMIN')"

    #no-security:
    - method: "POST"
      path: "/users"
      access-rule: "permitAll"
    - method: "GET"
      path: "/app/version"
      access-rule: "permitAll"
    - method: "POST"
      path: "/users/firebase"
      access-rule: "permitAll"
    - method: "POST"
      path: "/test/firebase"
      access-rule: "permitAll"
    - method: "POST"
      path: "/users/adjuster"
      access-rule: "permitAll"
    - method: "POST"
      path: "/users/firebase"
      access-rule: "permitAll"
    - method: "*"
      path: "/websocket"
      access-rule: "permitAll"
    - method: "*"
      path: "/users/securityCode"
      access-rule: "permitAll"
    - method: "*"
      path: "/users/verification"
      access-rule: "permitAll"
    - method: "*"
      path: "/users/password"
      access-rule: "permitAll"
    - method: "*"
      path: "/users/existence"
      access-rule: "permitAll"
    - method: "*"
      path: "/users/adjusters"
      access-rule: "permitAll"
    - method: "*"
      path: "/constants/cdn-url"
      access-rule: "permitAll"
    - method: "*"
      path: "/constants/ip"
      access-rule: "permitAll"
    - method: "*"
      path: "/constants/options"
      access-rule: "permitAll"
    - method: "*"
      path: "/pilot"
      access-rule: "permitAll"
    - method: "*"
      path: "/service/**"
      access-rule: "permitAll"
    - method: "*"
      path: "/payment/square/credentials"
      access-rule: "permitAll"
    - method: "*"
      path: "/companies/default"
      access-rule: "permitAll"
    - method: "*"
      path: "/companies/some"
      access-rule: "permitAll"
    - method: "*"
      path: "/payment/prices"
      access-rule: "permitAll"
    - method: "*"
      path: "/payment/paySuccessCallback"
      access-rule: "permitAll"
    - method: "*"
      path: "/products/reports"
      access-rule: "permitAll"
    - method: "*"
      path: "/transaction/settings/api-keys"
      access-rule: "permitAll"
    - method: "*"
      path: "/location/**"
      access-rule: "permitAll"
    - method: "*"
      path: "/projects/{projectId:\\d+}/images/ad-annotations"
      access-rule: "permitAll"
    - method: "*"
      path: "/projects/{projectId:\\d+}/aiProcess/construction3D"
      access-rule: "permitAll"
    - method: "*"
      path: "/projects/{projectId:\\d+}/aiProcess/preranging"
      access-rule: "permitAll"
    - method: "*"
      path: "/projects/{projectId:\\d+}/aiProcess/prescoping"
      access-rule: "permitAll"
    - method: "*"
      path: "/projects/{projectId:\\d+}/aiProcess/preplane"
      access-rule: "permitAll"
    - method: "*"
      path: "/projects/{projectId:\\d+}/aiProcess/preBoundary"
      access-rule: "permitAll"
    - method: "*"
      path: "/projects/{projectId:\\d+}/aiProcess/postBoundary"
      access-rule: "permitAll"
    - method: "*"
      path: "/projects/{projectId:\\d+}/images/closeup"
      access-rule: "permitAll"
    - method: "*"
      path: "/projects/{projectId:\\d+}/images/closeup-noai"
      access-rule: "permitAll"
    - method: "*"
      path: "/symbility/**"
      access-rule: "permitAll"
    - method: "GET"
      path: "/hover/token"
      access-rule: "permitAll"
    #open-api:
    - method: "*"
      path: "/v1/project"
      access-rule: "isFullyAuthenticated()"
    - method: "*"
      path: "/v1/project/{projectId:\\d+}/**"
      access-rule: "@SCAC.isProjectAccessible(request, authentication, #projectId)"
    - method: "*"
      path: "/v1/report/{reportId}/file/**"
      access-rule: "@SCAC.isReportListable(request, authentication, #reportId)"
    - method: "*"
      path: "/v1/report/{reportId}/summary"
      access-rule: "@SCAC.isReportListable(request, authentication, #reportId)"
    - method: "*"
      path: "/v1/report/{reportId}/**"
      access-rule: "@SCAC.isReportListable(request, authentication, #reportId)"
    - method: "*"
      path: "/v1/image/{imageId}/**"
      access-rule: "@SCAC.isImageListable(request, authentication, #imageId)"
    - method: "*"
      path: "/webservices/**"
      access-rule: "permitAll"
scheduler:
  spring-scheduled:
    start-up-delay: PT15M
  quartz:
    start-up-delay: PT15M

# graceful shutdown
server.shutdown: graceful
spring.lifecycle.timeout-per-shutdown-phase: 30s

server.servlet.encoding.charset: UTF-8

server.servlet.encoding.enabled: true

server.servlet.encoding.force: true
