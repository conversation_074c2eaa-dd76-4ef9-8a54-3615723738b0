{"yearBuilt": 1945, "livingArea": 1560.0, "lotSize": 0.27, "risk": {"overallCondition": "Good", "areaEconomy": "Stable", "neighborhood": "Suburban", "gatedCommunity": false, "locatedOnPavedRoad": false, "isolatedDwelling": false, "seasonalDwelling": false, "businessOperation": "Farming", "vacant": false, "rental": true, "forSale": false, "inaccessible": false, "hasCropsOrHorses": true, "waterBody": {"type": "Lake", "distance": "less than 500 ft"}}, "bldg": {"overallCondition": "Good", "dwellingType": "Single Family Detached", "construction": "<PERSON>ame", "constructionOverWater": true, "garage": "Attached", "hvac": "Central", "numStories": 2, "windProtections": ["None observed"], "hurricaneStraps": false, "foundation": "Basement", "hasFoundationCracks": false, "manufacturedOrMobileHome": "Manufactured", "designatedHistoricHome": false, "exteriorDamage": true, "underRenovation": false, "isDeveloperSpeculation": true, "underConstruction": false, "hasFence": false, "architecturalStyle": "TriLevel", "garageCapacity": 2}, "roof": {"overallCondition": "Good", "estAge": "less than 1 year", "estLife": "5 ~ 10 years", "geometry": {"Flat": 50, "Gable": 20, "Hip": 30}, "coveringMaterial": ["<PERSON><PERSON><PERSON>"], "hasSolarPanel": false, "hasCurlingShingles": false, "hasGranularLoss": false, "hasMissingDamagedShingles": false, "hasPatchedAreas": false, "hasTarp": false, "hasRoofDebris": false, "material": {"CompositeShingles": 100, "BuildupRoofNoGravel": 0, "ClayConcreteTiles": 0, "LightMetalPanels": 0, "SinglePlyMembrane": 0, "SinglePlyMembraneBallasted": 0, "Slate": 0, "StandingSeamMetalRoof": 0, "WoodenShingles": 0}, "comments": ["Front Slope: Missing shingles.", "Left Slope: Missing shingles."], "comparison": [], "chimneyCount": 1}, "exterior": {"overallCondition": "Good", "siding": {"Hardiplank": 100}, "hasChimneyDamage": false, "hasSidingDamage": false, "hasPealingPaint": false, "hasMildewOrMoss": false, "hasWindowDamage": false, "hasWallCracks": false, "hasWaterDamage": false, "hasShutters": false, "hasPorch": true, "hasStairsWithoutHandRails": false, "hasYardDebris": false, "hasDiscardedVehicles": false, "hasTreeLimbs": false, "hasPoolWithoutFence": false, "hasDivingBoardOrSlide": false, "hasPoolCage": false, "numDogPresent": 2, "hasDogPresent": false, "petType": ["Dog - Labrador Retriever", "Dog - German Shepherd", "Cat"], "pet": {"dog": 2, "cat": 1, "horse": 0, "other": 0}, "hasDogSign": false, "hasPestActivity": false, "hasTrampoline": false, "hasPlaySwingSet": false, "hasTreeHouse": false, "hasBasketballHoop": false, "hasATV": false, "hasSkateboardOrBikeRamp": false, "hasDirtBike": false, "hasWatercraft": false, "hasPropaneOrFuelTank": false, "hasSwimmingPool": false, "hasAwning": false, "comments": ["Left Elevation: Broken window glass."], "comparison": [], "isEIFS": false, "hasPoolSelfLatchingGate": true, "poolCount": 1, "pool": [{"type": "In-ground, Detached Pool", "size": "Medium", "hasCage": true, "cageSize": "Medium"}]}, "interior": {"overallCondition": "Good", "hasVisibleLeaks": false, "hasExistingDamage": false, "comments": ["The interior appears to be under renovation."], "plumbing": {"noShutoffValve": false, "hasOldWaterHeater": false, "hasPoorWaterHeaterCondition": false, "hasGalvanizedPipes": true, "isUpdated": true, "yearUpdated": 1986, "systemUpdateStatus": "Original", "hasIneligiblePlumbing": true}, "electric": {"hasIneligiblePanel": false, "panelBrand": "Square D", "isUpdated": true, "yearUpdated": 1992, "systemUpdateStatus": "Fully Updated"}, "waterHeater": {"serialNumber": "", "age": "12 years"}, "burglarAlarm": {"hasCentralBurglarAlarm": false}, "fireAlarm": {"hasCentralFireAlarm": true}, "heatingCooling": {"hasSpaceHeater": true, "hasWoodStove": false, "isUpdated": true, "yearUpdated": 1996, "systemUpdateStatus": "Original"}, "floorplan": {"room": {"bedroom": 1, "bathroom": 1, "closet": 1}, "hasDamage": {"bedroom": false, "bathroom": false, "closet": false, "foyer": false, "livingRoom": false, "familyRoom": false, "diningRoom": false, "breakfastRoom": false, "kitchen": false, "officeOrStudyRoom": false, "laundryRoom": false, "attic": false, "garage": false}}, "appliances": [{"productType": "Air Conditioner", "brand": "York", "modelNumber": "B1HA03", "serialNumber": "NLLM11", "manufacturedYear": 2014, "manufacturedMonth": 1, "age": 10.75, "remainingLife": 8.25, "failureRiskScore": 3.7, "recall_data": [{"fix": "Consumers should stop using the air conditioner immediately.", "dueTo": "Injury", "hazard": "The engine posing a hazard.", "recallUrl": "https://recalls", "dateRecalled": "2014-01-15", "recallNumber": "375", "customerRecallStatus": "FIXED", "dateStartedBeingSold": "2013-01-30", "dateStoppedBeingSold": "2013-12-29"}], "classActionLawsuit": [{"caseName": "York", "caseNumber": "0110", "caseCountry": "US", "caseDistrict": "U.S.", "claimDeadline": "2017-12-20", "classActionUrl": "https://classactions", "classActionDescription": "The Plaintiffs claim that manufactures including air handlers and packaged HVAC units."}]}]}, "community": {"numBldg": 13, "numResidentialUnits": 126, "hasBalcony": true, "petsAllowed": false, "hasTennisCourt": true, "hasPlayground": false, "hasBasketballCourt": true}, "fireProtection": {"code": 2, "nearestFireStation": {"title": "Fire And Rescue Station", "type": "Full Time", "distance": "0.92 miles"}, "nearestFireHydrant": {"distance": "500 ~ 1000 ft."}}, "closeoutReasons": ["Inspection was denied by the insured."], "addlStructures": ["<PERSON>n"], "hazards": ["Fireplace on exterior of front slope was noted.", "The property is located about 237 ft. from the Laurel Lake.", "2 dogs was spoted."], "recommendations": [{"text": "Removal of Diving Board/Slides", "image": [{"id": "3f1d8a93-0404-4419-b42d-72e25ef5cde8"}, {"id": "4e90cae0-7c29-4e7e-befa-f8a1d930ed4d"}]}], "factors": [{"text": "Peeling paint was noted on the window frame.", "name": "PEELING PAINT - COMPONENT", "direction": "Right", "image": [{"id": "cC01X5CSREicqvbIGfIvlrf0fXhMqvty"}, {"id": "dusigdaj6TJH6upyBGpbcpJFBBJCee_s"}]}, {"text": "Security Bars were noted over the window(s).", "name": "SECURITY BARS ON WINDOWS/DOORS", "direction": "Right", "image": [{"id": "cC01X5CSREicqvbIGfIvlrf0fXhMqvty"}, {"id": "dusigdaj6TJH6upyBGpbcpJFBBJCee_s"}]}, {"text": "Possible foundation issues were noted including cracks or unlevel areas.", "name": "FOUNDATION DAMAGE", "direction": "Right", "image": [{"id": "cC01X5CSREicqvbIGfIvlrf0fXhMqvty"}, {"id": "dusigdaj6TJH6upyBGpbcpJFBBJCee_s"}]}, {"text": "Algae/Moss was noted on roof.", "name": "ALGAE/MOSS", "direction": "Front", "image": [{"id": "yU0naitN7W-G1JtQJZGxzNuo_pLrjQ4u"}, {"id": "VJVxJ3JJ7VYgp379_3Ev1MSb7-8-pxH4"}, {"id": "-aJF1SG5Nr1Gh6PphaTFu4wxHe-RIHXl"}]}, {"text": "Roof appears to have been repaired and/or patched.", "name": "PATCHED AREA", "direction": "Front", "image": [{"id": "yU0naitN7W-G1JtQJZGxzNuo_pLrjQ4u"}]}, {"text": "Algae/Moss was noted on roof.", "name": "ALGAE/MOSS", "direction": "Right", "image": [{"id": "Wz4SlzE6sCZUgaNpMD6gVm4sW_CyjzDn"}, {"id": "RDS8L1iOiV03-8Kzx2A6INFSN2q8VZvo"}]}, {"text": "<PERSON><PERSON><PERSON> was noted on roof.", "name": "DEBRIS ON ROOF", "direction": "Right", "image": [{"id": "Wz4SlzE6sCZUgaNpMD6gVm4sW_CyjzDn"}]}, {"text": "Algae/Moss was noted on roof.", "name": "ALGAE/MOSS", "direction": "Left", "image": [{"id": "tr7XvIRBn_kOdc1jVsbmzcjClOGS2pfA"}]}, {"text": "Algae/Moss was noted on roof.", "name": "ALGAE/MOSS", "direction": "Rear", "image": [{"id": "vQGcMiX9liEmmv-HpPgaACPbw7YKqpiE"}, {"id": "95uWOr4KsVmyQdv-YKT4MLuKkNYZpth5"}, {"id": "2HGUh_NTHNdv8LoONTd-3HwOEzIXaLsk"}]}, {"text": "Loose, damaged, or missing shingles were noted.", "name": "MISSING/DAMAGE SHINGLES", "direction": "Rear", "image": [{"id": "2HGUh_NTHNdv8LoONTd-3HwOEzIXaLsk"}]}, {"text": "Roof appears to have been repaired and/or patched.", "name": "PATCHED AREA", "direction": "Rear", "image": [{"id": "2HGUh_NTHNdv8LoONTd-3HwOEzIXaLsk"}]}, {"text": "Damage to garage(s) was noted at the time of inspection. It is recommended to contact garage service contractor to repair the garage as soon as possible.", "name": "GARAGE DAMAGE", "direction": null, "image": [{"id": "wQfYtYM4pomhuWup305NRg-uazSRikDP"}]}, {"text": "Fireplace on exterior of Rear slope was noted. It is recommended to conduct interior chimney sweep.", "name": "FIREPLACE ON EXTERIOR", "direction": "Rear", "image": [{"id": "TZt0CvzjNzy6Aidv-uYpwf9Z3ULXin0z"}, {"id": "qY5jaQigtlUxyC5ct9W5aKhbmjdtrhAq"}]}, {"text": "Milde<PERSON> was noted on the siding.", "name": "MILDEW/MOSS", "direction": "Left", "image": [{"id": "WZEwt4Zvesmo0TZir4iu-o4aTH6sp21q"}, {"id": "aApQwJ8YBSpO_6AJ16a_wZrDxtJDwrtz"}]}, {"text": "Peeling paint was noted on the door.", "name": "PEELING PAINT - COMPONENT", "direction": "Left", "image": [{"id": "WZEwt4Zvesmo0TZir4iu-o4aTH6sp21q"}, {"id": "aApQwJ8YBSpO_6AJ16a_wZrDxtJDwrtz"}]}, {"text": "Security Bars were noted over the window(s).", "name": "SECURITY BARS ON WINDOWS/DOORS", "direction": "Left", "image": [{"id": "WZEwt4Zvesmo0TZir4iu-o4aTH6sp21q"}, {"id": "aApQwJ8YBSpO_6AJ16a_wZrDxtJDwrtz"}]}, {"text": "HVAC unit(s) was noted in Good condition.", "name": "HVAC", "direction": "Rear", "image": [{"id": "YWgRx1rY1TNs6ZMhq3PXz3C2KYs-Jttb"}]}, {"text": "Window unit(s) was noted in Good condition.", "name": "HVAC", "direction": "Left", "image": [{"id": "GhD1GSE3D07MAQxggXfoLWXBGSoj3qpD"}]}, {"text": "The fence is noted to be in Good condition.", "name": "FENCE", "direction": null, "image": [{"id": "qY5jaQigtlUxyC5ct9W5aKhbmjdtrhAq"}, {"id": "wQfYtYM4pomhuWup305NRg-uazSRikDP"}]}, {"text": "Security bars were noted over windows/doors on Right elevation.", "name": "SECURITY BARS ON WINDOWS/DOORS", "direction": "Right", "image": [{"id": "dusigdaj6TJH6upyBGpbcpJFBBJCee_s"}]}, {"text": "Security bars were noted over windows/doors on Rear elevation.", "name": "SECURITY BARS ON WINDOWS/DOORS", "direction": "Rear", "image": [{"id": "fD6Zdhx9n6xx9U19hLMduki4PmtnXbVH"}, {"id": "2VynmX-i2q8DC4_08dQ1t3IxFtVUg--p"}]}, {"text": "Security bars were noted over windows/doors on Left elevation.", "name": "SECURITY BARS ON WINDOWS/DOORS", "direction": "Left", "image": [{"id": "YsPiOPo-yviMBFvTuefgbweef9UIGhIF"}, {"id": "GhD1GSE3D07MAQxggXfoLWXBGSoj3qpD"}]}], "history": [{"dateInspected": "2022-12-25", "policyNumber": "BEES20221225", "image": []}], "hover": {"hoverTla": [{"livingAreas": {"floors": {"entities": [{"area": 1217, "identifier": "1st_floor", "label": null, "measured": true, "floor": 1}, {"area": 918, "identifier": "2nd_floor", "label": null, "measured": true, "floor": 2}], "totalArea": 2135}}, "attachedStructures": {"attachedGarages": {"entities": [{"area": 395, "identifier": "garage_attached", "label": null, "measured": true, "cars": null}], "totalArea": 395}, "decks": {"entities": [{"area": 152, "identifier": "deck", "label": null, "measured": true}], "totalArea": 152}, "openPorches": {"entities": [{"area": 218, "identifier": "open_porch", "label": null, "measured": true}], "totalArea": 218}}}]}}