Configuration:
  status: off

  Properties: # 定义全局变量
    Property: #
      - name: PATTERN
        value: "%d{yyyy-MM-dd'T'HH:mm:ss.SSS'Z'}{UTC},[%thread],[%X{traceId}],%5p,%c,%L,%ex{short}{separator(|)},%replace{%m}{\n}{|},%ex{separator(|)}%n"
      - name: BASE_PATH
        value: /var/bees360/www/logs
      - name: LOG_FILE_NAME
        value: ${BASE_PATH}/${sys:localhostName}_${sys:springAppName}_${sys:currentDate}

  Appenders:
    Console:  #输出到控制台
      name: console
      target: SYSTEM_OUT
      PatternLayout:
        pattern: ${PATTERN}

    File:
      - name: FileAppender
        ignoreExceptions: false
        fileName: ${LOG_FILE_NAME}.log
        PatternLayout:
          pattern: ${PATTERN}

    Async:
      - name: asyncFileAppender
        AppenderRef:
          - ref: FileAppender
  Loggers:
    Root:
      level: INFO
      AppenderRef:
        - ref: console
        - ref: asyncFileAppender
    Logger:
      - name: org.springframework
        additivity: false
        level: INFO
        AppenderRef:
          - ref: console
      - name: org.mybatis
        additivity: false
        level: info
        AppenderRef:
          - ref: console
      - name: org.apache
        additivity: false
        level: info
        AppenderRef:
          - ref: console
      - name: com.gzedu
        additivity: false
        level: info
        AppenderRef:
          - ref: console
      - name: java.sql
        additivity: false
        level: info
        AppenderRef:
          - ref: console
      - name: freemarker
        additivity: false
        level: error
        AppenderRef:
          - ref: console
      - name: com.opensymphony
        additivity: false
        level: info
        AppenderRef:
          - ref: console
      - name: org.thymeleaf
        additivity: false
        level: info
        AppenderRef:
          - ref: console
      - name: com.google.cloud.firestore.CustomClassMapper
        additivity: false
        level: error
        AppenderRef:
          - ref: console
