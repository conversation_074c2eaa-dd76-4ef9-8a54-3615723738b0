auth:
  jwt:
    keyId: f45a691453a4bc0388931b029cfb95466f7b4dd8
    signingKey: |
      -----BEG<PERSON> PRIVATE KEY-----
      MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDnpfrDqFClLoVq
      Ag9GcM0sEmIRi7BP+gC8KautIhPyrScuujFHZf8MHP1AezjVd2gcWCi1pGTTNUbe
      ZH2xet5qqP/XGc2OsLFh9vAGOXQz1WtJWENv4ncazZu8uBeurVyJrNIaY0G3v3JY
      AUHKi/Wu0GCyt0bSwVYVlzH8IOwlZezuUmsoWMX56eI0CFrfA2WuGwvMJQzB7daN
      6XB68hGt+WFE18xXeJCKmyjhaFoY/KpWO1Z0g8/gLYLkPrC/oa6tNigENaK7aQma
      PCJ3phwiDz2aWPNJ8evXMGVFIE5qUHG3aNEpA2Rru4pFA9+g3ZfjIu8VnQh1kLmL
      5dtw0x4pAgMBAAECggEAawiYtRYM6T31HDXGbBRL4Oy/jn7eYR3RgB+6+NzxnaRf
      IeqbQXPB5I1ygPxOlue83bfoW4GRqruHutNw9nQg1+StjeyYu7EtzOemISIj7J1U
      nMehJU3dHa/aIVloa+gjhWOqktHA+E+H3Fz8UsPXqcYToBAqM9BsX76ENIzbwC0W
      i6vet7zHZbM0luS+OUt2r84oDDxErsRSZpZ8rrS3lL3aEMCGcZPSX+8o4hEsQQV2
      GP8Zjkzx6Qjs1ZlLn5nFMAqtdeA0qjYuhWeMCJ+4zZifx1QU6herNBOvTlnGUbnV
      Ky3dp23Se/sv2N9Zhq1KRXTXFgeVbQKPUFzMJof/3wKBgQD+TSzaKhfdWsK8gwxG
      yVKBPnbF7pSIqS2fVDR9aTLglMb5WCvhx5xPNwo7OgQl18ZH+rhFRG9vxq3QUvRA
      dnTVA2wACnGfoXR5pextqLW0KQQI+girhg6fxEBRuHB3+ouJuJlJbv4D/8amgoF5
      ehrtes1rHDltuSRkRa4d7/i/ZwKBgQDpMhH84vOMlUYG+ziBt0mpHcUrL4CW32uC
      Z0dzbu6WNWzmnp1sp9NCsZ3dUIjUEVB/ZKLr4HwwVarZDvVfXbPR+/9g2o9+1+fM
      jUtjBdmUuK8c55lVZVMrttUUWSEh8HeqxotzlAh64VOL4lZIW/izKwU6OyFWjKh9
      NAYV5UkL7wKBgQD7NXmtOg/BFrtwRXSHKLFcAzyJYplQNu9eiWwgxx7/Q3kbL8KJ
      kwoSJqjwwOsABxtUV5RYuLXskTN6q7elRl87Xdn+YFLGAlRxJztbSGHWyv21cGnI
      JvGjWda/45cWrmXRco3aQM6lXLH8IseMHNck6T6rJIKGpXIRq9PItxne2QKBgQDK
      wUQuks2b0DZg35FUB58+Mao7UEQH6h459b6EOVGsvKsBIaxSyuC+vlw6D0nIyX0q
      JsAZQsf35qRzsbPaxUgSLX7wIVheuy7KBZgkcvOpgaKQCTair0TPX1qJJK3QKpqB
      m7uoNG/jF4Nivtqyc3Uboy4aYUstS4M7yT35T7BhdQKBgDpz51Rzjrm1u3MpXGfO
      Pf/4XHOhXQGBGrFTTbxBn9TRQYc+OwHy3VWu+QeKeNXUISf7nNR+ze+QBEtnpQCh
      25nIvUQXiPedPjTePIQWlEHK3iyJEakqy5k6HDkjC3/3J1xmtp7Eo8zt1FA1Wtt3
      1FP0J4FDu8S9GdwBHAahn5H4
      -----END PRIVATE KEY-----
    verifierKey: |
      -----BEGIN CERTIFICATE-----
      MIIC/DCCAeSgAwIBAgIIOcybo4ESpmMwDQYJKoZIhvcNAQEFBQAwIDEeMBwGA1UE
      AxMVMTAzNzQxMjAwMzE3OTIwODg2ODQ1MCAXDTIxMDcyNzAzNDgxM1oYDzk5OTkx
      MjMxMjM1OTU5WjAgMR4wHAYDVQQDExUxMDM3NDEyMDAzMTc5MjA4ODY4NDUwggEi
      MA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDnpfrDqFClLoVqAg9GcM0sEmIR
      i7BP+gC8KautIhPyrScuujFHZf8MHP1AezjVd2gcWCi1pGTTNUbeZH2xet5qqP/X
      Gc2OsLFh9vAGOXQz1WtJWENv4ncazZu8uBeurVyJrNIaY0G3v3JYAUHKi/Wu0GCy
      t0bSwVYVlzH8IOwlZezuUmsoWMX56eI0CFrfA2WuGwvMJQzB7daN6XB68hGt+WFE
      18xXeJCKmyjhaFoY/KpWO1Z0g8/gLYLkPrC/oa6tNigENaK7aQmaPCJ3phwiDz2a
      WPNJ8evXMGVFIE5qUHG3aNEpA2Rru4pFA9+g3ZfjIu8VnQh1kLmL5dtw0x4pAgMB
      AAGjODA2MAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgeAMBYGA1UdJQEB/wQM
      MAoGCCsGAQUFBwMCMA0GCSqGSIb3DQEBBQUAA4IBAQCTlQLQOUEr5PHvG1Y9z7Hc
      +6MXFb7WxiRM7IcSEPA0BwkZM94BF5fojlHZ2ox335669wrGqGpkNLBqIMLuxw1b
      4N5wxeeS2M8fZeUjM7kaHT8ZE+27FzOY1NNbFn+dyUqonGM/1GlGL3hgOV4hkyzk
      3vderqQakqR7bYyUmjCgBcAvQahOpkJxX4/XgqzaDQBDGmhG1VOvyTJEyZtBX1GN
      nmsz9CzqClBgydf6bnjgm4Gh4CHI4YKL/7yGXAkk50JqYbhkAmlNcIbILryilTRu
      MZGX7oskns0vBqMtLmIPG5eH/KL3KqUS1KTxW33Ucca+WnmKQjNys2QKBVLR1Y+F
      -----END CERTIFICATE-----

  clients:
    - client_id: test_client_id
      client_secret: test_client_secret
      authorized_grant_types: password,refresh_token
      scope: READ,WRITE
      access_token_validity_seconds: 3600
      refresh_token_validity_seconds: 604800

http:
  cors:
    '[/user/me]':
      allowed_origins: http://foo.com
      allowed_headers: "*"
      allowed_methods: "*"
      allow_credentials: true
      max_age: 86400

spring:
  main:
    allow-bean-definition-overriding: true

logging:
  level:
    org.springframework.security: DEBUG
    com.bees360.web.security: DEBUG
