spring:
  main:
    allow-circular-references: true
  datasource:
    url: *********************************************************************************************************
    driver-class-name: org.mariadb.jdbc.Driver
    username: root
    password: 123456

bees360:
  feature-switch:
    enable-upload-address-images-to-project: true
    enable-sync-survey-completed: true

# 配置 mybatis
mybatis:
  config-location: classpath:config/mybatis-config.xml
  mapper-locations: classpath*:mappings/**/*.xml

logging:
  level:
    com.bees360.job.ProjectStatusSetImageUploadedJobExecutor: DEBUG

mail:
  topic-recipients:
    projectExportScheduled:
      - <EMAIL>
      - <EMAIL>
    projectExportScheduledMonthly:
      - <EMAIL>

google:
  geo:
    api-key: "fakeKey"
google-map:
    apiKey: "fakeKey"
