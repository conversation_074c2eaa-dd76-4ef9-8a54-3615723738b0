package com.bees360.config.project;

import com.bees360.event.registry.ProjectParentChildGroupUpdatedEvent;
import com.bees360.listener.UpdateFirebaseParentChildProjectListener;
import com.bees360.service.firebase.FirebaseProjectService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.time.Instant;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

public class UpdateFirebaseParentChildProjectTest {
    @Mock
    private FirebaseProjectService firebaseProjectService;

    @InjectMocks
    private UpdateFirebaseParentChildProjectListener listener;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testHandleParentProjectEvent() throws IOException {
        // Arrange
        String projectId = "12345";
        String groupKey = "12345"; // Same as projectId for parent project
        boolean isDeleted = false;

        ProjectParentChildGroupUpdatedEvent event = new ProjectParentChildGroupUpdatedEvent(
                projectId,
                groupKey,
                "GROUP_PARENT_CHILD",
                "10000",
                Instant.now().toString(),
                isDeleted
        );
        listener.handle(event);
        verify(firebaseProjectService).updateParentChildProject(eq(12345L), eq("12345"));
    }

    @Test
    public void testHandleChileProjectEvent() throws IOException {
        // Arrange
        String projectId = "12346";
        String groupKey = "12345"; // Same as projectId for parent project
        boolean isDeleted = false;

        ProjectParentChildGroupUpdatedEvent event = new ProjectParentChildGroupUpdatedEvent(
                projectId,
                groupKey,
                "GROUP_PARENT_CHILD",
                "10000",
                Instant.now().toString(),
                isDeleted
        );
        listener.handle(event);
        verify(firebaseProjectService).updateParentChildProject(eq(12346L), eq("12345"));
    }

    @Test
    public void testHandleDeleteEvent() throws IOException {
        // Arrange
        String projectId = "12346";
        String groupKey = "12345"; // Same as projectId for parent project
        boolean isDeleted = true;

        ProjectParentChildGroupUpdatedEvent event = new ProjectParentChildGroupUpdatedEvent(
                projectId,
                groupKey,
                "GROUP_PARENT_CHILD",
                "10000",
                Instant.now().toString(),
                isDeleted
        );
        listener.handle(event);
        verify(firebaseProjectService).updateParentChildProject(eq(12346L), eq(null));
    }
}
