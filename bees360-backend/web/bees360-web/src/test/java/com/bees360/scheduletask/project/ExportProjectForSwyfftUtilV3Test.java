package com.bees360.scheduletask.project;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.io.FileUtils;

import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;
import com.bees360.entity.dto.BsExportDataLogProjectEditorV3;

/**
 * <AUTHOR> Yang
 */
public class ExportProjectForSwyfftUtilV3Test {

    public static void main(String[] args) throws IOException {
        ExportProjectForSwyfftUtil util = new ExportProjectForSwyfftUtilV3();
        List<Project> projects = projects();
        List<BsExportData> dataList = dataList();
        ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData excelData = util.fetchExcelData(projects, dataList);

        File file = new File("/Users/<USER>/Desktop/tmp/bs_export_data_v3_" + System.currentTimeMillis() + ".xlsx");
        System.out.println("row count: " + excelData.getRowCount());
        System.out.println("File: " + file.getAbsolutePath());
        FileUtils.writeByteArrayToFile(file, excelData.getFile());
    }

    private static List<Project> projects() {
        return new ArrayList<>();
    }

    private static List<BsExportData> dataList() {
        BsExportData data = new BsExportData();
        data.setRelatedId("1");
        data.setRelatedType(BsExportDataLogProjectEditorV3.RELATED_TYPE);
        data.setDataLog(
            "{\"config\":{\"export\":[{\"key\":\"projectId\",\"index\":0},{\"key\":\"customize.OverallBldgCondition\",\"index\":1},{\"key\":\"customize.CompletionDate\",\"index\":2}]},\"data\":[{\"key\":\"customize.CompletionDate\",\"value\":\"None\"},{\"key\":\"customize.OverallBldgCondition\",\"value\":\"Good\"},{\"key\":\"customize.CaseType\",\"value\":\"Swyfft Residential Exterior\"}]}");
        return Arrays.asList(data);
    }
}
