package com.bees360.listener;

import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.service.MemberService;
import com.bees360.service.listener.project.UpdateOMByProjectTaskOwnerChangeListener;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.time.Instant;
import java.util.List;

public class TestUpdateOmByProjectTaskOwnerChanged {
    @Mock private MemberService memberService;
    private UpdateOMByProjectTaskOwnerChangeListener listener;
    private final List<String> targetTaskList = List.of("targetTask1", "targetTask2");

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        listener = new UpdateOMByProjectTaskOwnerChangeListener(memberService, targetTaskList);
    }

    @Test
    public void testReceiveTargetTaskAndShouldUpdateOm() throws Exception {
        var pipelineId = RandomUtils.nextLong();
        var ownerId = RandomStringUtils.randomAlphabetic(6);
        var taskDefKey = targetTaskList.get(RandomUtils.nextInt(0, 1));
        var updatedAt = Instant.now();
        var event = createEvent(ownerId, pipelineId, taskDefKey, updatedAt);
        // Assert listener can take the event
        listener.execute(event);
        Mockito.verify(memberService, Mockito.times(1))
                .saveOperationsManager(
                        Mockito.anyLong(), Mockito.any(), Mockito.any(), Mockito.anyLong());
    }

    @Test
    public void testReceiveOtherTaskAndShouldUpdateOm() throws Exception {
        var pipelineId = RandomUtils.nextLong();
        var ownerId = RandomStringUtils.randomAlphabetic(6);
        var taskDefKey = RandomStringUtils.randomAlphabetic(8);
        while (targetTaskList.contains(taskDefKey)) {
            taskDefKey = RandomStringUtils.randomAlphabetic(8);
        }
        var updatedAt = Instant.now();
        var event = createEvent(ownerId, pipelineId, taskDefKey, updatedAt);
        // Assert listener can take the event
        listener.execute(event);
        Mockito.verify(memberService, Mockito.times(0))
                .saveOperationsManager(
                        Mockito.anyLong(), Mockito.any(), Mockito.any(), Mockito.anyLong());
    }

    private PipelineTaskChanged createEvent(
            String userId, long pipelineId, String taskDefKey, Instant updatedAt) {
        PipelineTaskChanged event = new PipelineTaskChanged();
        var state = new PipelineTaskChanged.State();
        state.setOwnerId(userId);
        event.setPipelineId(String.valueOf(pipelineId));
        event.setState(state);
        event.setTaskDefKey(taskDefKey);
        event.setUpdatedAt(updatedAt);
        return event;
    }
}
