package com.bees360.controller;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.bees360.building.Message.BuildingMessage;
import com.bees360.building.Message.BuildingType;
import com.bees360.policy.Message.PolicyMessage;
import com.bees360.project.Message;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import com.bees360.service.ProjectService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

@Import({
    ProjectPolicyControllerTest.Config.class,
    ProjectPolicyController.class,
})
@WebMvcTest(ProjectPolicyController.class)
@AutoConfigureMockMvc(addFilters = false)
public class ProjectPolicyControllerTest {

    @Configuration
    static class Config {}

    @MockitoBean
    ProjectService projectService;

    @MockitoBean
    ProjectIIRepository projectIIRepository;

    @Autowired
    private MockMvc mockMvc;

    @Test
    void testUpdatePolicyTypeAndPropertyType() throws Exception {
        var projectId = 1L;
        var policyType = "Commercial";
        var propertyType = 5;
        var request = "{\"policyType\":\"%s\", \"propertyType\":%s}".formatted(policyType, propertyType);

        when(projectService.updatePolicyTypeAndPropertyType(eq(projectId), eq(policyType), eq(propertyType))).thenReturn(createProject(policyType, propertyType));

        mockMvc.perform(
            put("/project/%s/policy/policy-type".formatted(projectId))
            .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
                .content(request))
            .andExpect(status().isOk());

        verify(projectService).updatePolicyTypeAndPropertyType(eq(projectId), eq(policyType), eq(propertyType));
    }

    @SneakyThrows
    private ProjectII createProject(String policyType, Integer propertyType) {
        var building = BuildingMessage.newBuilder().setType(BuildingType.forNumber(propertyType));
        var policy = PolicyMessage.newBuilder()
            .setType(policyType)
            .setBuilding(building);
        var builder = Message.ProjectMessage.newBuilder().setPolicy(policy);
        return ProjectII.from(builder.build());
    }
}
