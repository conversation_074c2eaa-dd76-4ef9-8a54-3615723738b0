package com.bees360.scheduletask.project;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

import org.apache.commons.io.FileUtils;

import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;

/**
 * <AUTHOR> Yang
 */
public class ExportProjectForSwyfftNewProcessorTest {

    @Test
    public void exportToExcels() {
        List<ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData> datas =
            ExportProjectForSwyfftNewProcessor.exportToExcels(DataRepository.projects(), DataRepository.dataList());

        assertEquals(2, datas.size());
        assertEquals(1, datas.get(0).getRowCount());
        assertEquals(1, datas.get(1).getRowCount());
    }

    public static class DataRepository {

        private static List<BsExportData> dataList() {

            BsExportData dataOld = new BsExportData();
            dataOld.setRelatedId("1");
            dataOld.setRelatedType("project_editor");
            dataOld.setDataLog(
                "{\"DogSign\":\"Y\",\"InspectionSummary\":\"\",\"RiskOpinion\":\"good\",\"CommentToElevation\":\"\",\"CommentToRoof\":\"\",\"GeneralComments\":\"\"}");

            BsExportData dataNew = new BsExportData();
            dataNew.setRelatedId("2");
            dataNew.setRelatedType("project_editor");
            dataNew.setDataLog(
                "{\"version\":1,\"config\":{\"export\":[{\"key\":\"projectId\",\"index\":0},{\"key\":\"customize.OverallBldgCondition\",\"index\":1},"
                    + "{\"key\":\"customize.CompletionDate\",\"index\":2}]},\"data\":[{\"key\":\"customize.CompletionDate\",\"value\":\"None\"},"
                    + "{\"key\":\"customize.OverallBldgCondition\",\"value\":\"Good\"},{\"key\":\"customize.CaseType\",\"value\":\"Swyfft Residential Exterior\"}]}");

            return Arrays.asList(dataOld, dataNew);
        }

        private static List<Project> projects() {
            Project projectOld = new Project();
            projectOld.setProjectId(1L);
            projectOld.setCreatedBy(System.currentTimeMillis());
            projectOld.setInspectionNumber("1234567");
            projectOld.setAssetOwnerName("zhangsan");
            projectOld.setAssetOwnerEmail("<EMAIL>");
            projectOld.setYearBuilt("12");
            projectOld.setClaimNote("abababab");
            projectOld.setInspectionTime(System.currentTimeMillis());

            Project projectNew = new Project();
            projectNew.setProjectId(2L);
            projectNew.setCreatedBy(System.currentTimeMillis());
            projectNew.setInspectionNumber("1234567");
            projectNew.setAssetOwnerName("zhangsan");
            projectNew.setAssetOwnerEmail("<EMAIL>");
            projectNew.setYearBuilt("12");
            projectNew.setClaimNote("abababab");
            projectNew.setInspectionTime(System.currentTimeMillis());
            return Arrays.asList(projectOld, projectNew);
        }
    }

    public static void main(String[] args) throws IOException {
        List<ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData> datas =
            ExportProjectForSwyfftNewProcessor.exportToExcels(DataRepository.projects(), DataRepository.dataList());

        for(int i = 0; i < datas.size(); i ++) {
            ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData data = datas.get(i);
            File file = new File("/Users/<USER>/Desktop/tmp/bs_export_data_v2_" + i + "_" + System.currentTimeMillis() + ".xlsx");
            System.out.println("row count: " + data.getRowCount());
            System.out.println("File: " + file.getAbsolutePath());
            FileUtils.writeByteArrayToFile(file, data.getFile());
        }
    }
}
