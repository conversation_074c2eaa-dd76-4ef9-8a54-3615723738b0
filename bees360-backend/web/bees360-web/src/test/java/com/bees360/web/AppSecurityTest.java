package com.bees360.web;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bees360.config.ActuatorSecurityConfig;
import com.bees360.gateway.config.OpenApiConfig;
import com.bees360.job.autoconfig.JobExecutorAutoConfig;
import com.bees360.web.security.SecurityConfig;

import lombok.extern.log4j.Log4j2;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalManagementPort;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.test.context.ActiveProfiles;

@Log4j2
@SpringBootTest(
    classes = {
        SecurityConfig.class,
        OpenApiConfig.OpenapiSecurityConfig.class,
        ActuatorSecurityConfig.class,
}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@EnableWebSecurity
@ActiveProfiles({"security", "actuator", "app-security-test"})
@EnableAutoConfiguration(exclude = {
    DataSourceAutoConfiguration.class,
    JobExecutorAutoConfig.class,
} )
class AppSecurityTest {

    @Autowired
    TestRestTemplate testRestTemplate;

    @LocalManagementPort
    private int managementPort;

    @LocalServerPort
    private int serverPort;

    @Test
    void testMultiSecurityFilterChain_actuatorOK() {
        var url = "http://localhost:%s/monitor/actuator/health/liveness".formatted(managementPort);
        var result = testRestTemplate.getForEntity(url, String.class);
        assertEquals(200, result.getStatusCode().value());
        assertTrue(result.getBody().contains("{\"status\":\"UP\"}"));
    }

    @Test
    void testMultiSecurityFilterChain_openapiOk() {
        var result = testRestTemplate.getForEntity("http://localhost:%s/bees360-web/openapi/v3/api-docs".formatted(serverPort), String.class);
        assertEquals(200, result.getStatusCode().value());
        assertTrue(result.getBody().contains("\"openapi\":\"3.1.0\""));
    }

}
