package com.bees360.util.msgutil;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.mail.MailMessage;
import com.bees360.mail.MailSender;
import com.bees360.util.msgutil.filter.EmailFilter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.thymeleaf.ITemplateEngine;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * Unit tests for the DelegateEmailSender class.
 *
 * <p>Tests various functionality of the email sending service including: - Basic email sending to
 * single and multiple recipients - Email sending with attachments - Batch email operations - Email
 * filtering capabilities - Behavior when sending is disabled - Email content creation
 */
@ExtendWith(MockitoExtension.class)
class DelegateEmailSenderTest {

    @Mock private MailSender mailSender;

    @Mock private ITemplateEngine templateEngine;

    @Mock private EmailFilter emailRecipientFilter;

    private Properties subjectProperties;

    private DelegateEmailSender delegateEmailSender;

    private Map<String, Object> templateModel;
    private List<String> recipients;
    private String template;
    private Object[] subjectParams;

    /**
     * Setup method that runs before each test. Initializes mocks and configures the test environment.
     */
    @BeforeEach
    void setUp() throws IOException {
        MockitoAnnotations.initMocks(this);

        subjectProperties =
            PropertiesLoaderUtils.loadProperties(new PathMatchingResourcePatternResolver().getResource("message/emailSubject.properties"));
        delegateEmailSender = new DelegateEmailSender(mailSender, templateEngine, "Test", true, subjectProperties, emailRecipientFilter);

        // Prepare test data
        template = "testSendEmail";
        templateModel = new HashMap<>();
        templateModel.put("name", "John");
        templateModel.put("message", "Hello World");

        subjectParams = new Object[] {"Param1"};

        recipients = new ArrayList<>();
        recipients.add("<EMAIL>");
        recipients.add("<EMAIL>");

        when(emailRecipientFilter.filter(any())).thenAnswer(t -> t.getArguments()[0]);

        Mockito.when(templateEngine.process(eq(template + ".html"), any()))
            .thenReturn("<html><body>Test Email Content</body></html>");
    }

    /**
     * Tests the basic email sending functionality to a single recipient. Verifies that the email
     * content and subject are correctly populated and that the email is properly sent.
     */
    @Test
    void testSendWithSingleRecipient() {
        // Prepare
        String recipient = "<EMAIL>";

        // Execute
        delegateEmailSender.send(recipient, template, templateModel, subjectParams);

        // Verify
        ArgumentCaptor<MailMessage> mailMessageCaptor = ArgumentCaptor.forClass(MailMessage.class);
        verify(mailSender).send(mailMessageCaptor.capture());

        MailMessage capturedMessage = mailMessageCaptor.getValue();
        assertEquals(List.of(recipient), capturedMessage.getRecipients());
        assertEquals("Test Param1", capturedMessage.getSubject());
        assertEquals("<html><body>Test Email Content</body></html>", capturedMessage.getContent());
    }

    /**
     * Tests email sending to multiple recipients. Ensures all recipients receive the email with
     * correct content.
     */
    @Test
    void testSendWithMultipleRecipients() {
        // Execute
        delegateEmailSender.send(recipients, template, templateModel, subjectParams);

        // Verify
        ArgumentCaptor<MailMessage> mailMessageCaptor = ArgumentCaptor.forClass(MailMessage.class);
        verify(mailSender, times(2)).send(mailMessageCaptor.capture());

        List<MailMessage> capturedMessages = mailMessageCaptor.getAllValues();
        assertEquals(2, capturedMessages.size());

        assertEquals(List.of("<EMAIL>"), capturedMessages.get(0).getRecipients());
        assertEquals(List.of("<EMAIL>"), capturedMessages.get(1).getRecipients());
    }

    /**
     * Tests sending an email with file attachments. Verifies that attachments are properly included
     * in the email.
     */
    @Test
    void testSendWithAttachments() {
        // Prepare
        Map<String, String> attachments = new HashMap<>();
        attachments.put("attachment1.pdf", "path/to/attachment1.pdf");

        // Execute
        delegateEmailSender.send(recipients, template, templateModel, subjectParams, attachments);

        // Verify
        ArgumentCaptor<MailMessage> mailMessageCaptor = ArgumentCaptor.forClass(MailMessage.class);
        verify(mailSender, times(2)).send(mailMessageCaptor.capture());

        List<MailMessage> capturedMessages = mailMessageCaptor.getAllValues();
        assertEquals(attachments, capturedMessages.get(0).getAttachments());
    }

    /**
     * Tests the batch sending functionality. Verifies multiple emails can be sent in one operation.
     */
    @Test
    void testOneShotSend() {
        // When EmailFilter is used
        when(emailRecipientFilter.filter(recipients)).thenReturn(recipients);

        // Execute
        delegateEmailSender.oneShotSend(recipients, template, templateModel, subjectParams);

        // Verify
        ArgumentCaptor<MailMessage> mailMessageCaptor = ArgumentCaptor.forClass(MailMessage.class);
        verify(mailSender).send(mailMessageCaptor.capture());

        MailMessage capturedMessage = mailMessageCaptor.getValue();
        assertEquals(recipients, capturedMessage.getRecipients());
        assertEquals("Test Param1", capturedMessage.getSubject());
    }

    /**
     * Tests batch sending with empty recipients list. Ensures no emails are sent when no valid
     * recipients exist.
     */
    @Test
    void testOneShotSendWithEmptyRecipients() {
        // When recipients are empty after filtering
        when(emailRecipientFilter.filter(recipients)).thenReturn(new ArrayList<>());

        // Execute
        delegateEmailSender.oneShotSend(recipients, template, templateModel, subjectParams);

        // Verify that no emails were sent
        verify(mailSender, never()).send(any(MailMessage.class));
    }

    /**
     * Tests the behavior when email sending is disabled. Verifies that no emails are sent when the
     * sender is configured with sending disabled.
     */
    @Test
    void testSendingDisabled() {
        // Preparation
        var sender = new DelegateEmailSender(mailSender, templateEngine, "Test", false, subjectProperties, emailRecipientFilter);

        // Execution
        sender.send(recipients, template, templateModel, subjectParams);

        // Verify that mailSender was never called (logging logic in the tested class)
        verify(mailSender, never()).send(any(MailMessage.class));
    }

    /**
     * Tests the email recipient filtering functionality. Verifies that recipients are correctly
     * filtered according to configured filter criteria.
     */
    @Test
    void testEmailRecipientFiltering() {
        // When EmailFilter filters some recipients
        List<String> filteredRecipients = List.of("<EMAIL>");
        when(emailRecipientFilter.filter(recipients)).thenReturn(filteredRecipients);

        // Execute
        delegateEmailSender.send(recipients, template, templateModel, subjectParams);

        // Verify emails were only sent to filtered recipients
        ArgumentCaptor<MailMessage> mailMessageCaptor = ArgumentCaptor.forClass(MailMessage.class);
        verify(mailSender, times(1))
            .send(mailMessageCaptor.capture()); // Explicitly specify it should be called once

        MailMessage capturedMessage = mailMessageCaptor.getValue();
        assertEquals(List.of("<EMAIL>"), capturedMessage.getRecipients());
    }
}
