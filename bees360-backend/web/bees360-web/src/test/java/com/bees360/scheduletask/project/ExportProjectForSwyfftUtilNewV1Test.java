package com.bees360.scheduletask.project;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;

/**
 * <AUTHOR> Yang
 */
public class ExportProjectForSwyfftUtilNewV1Test {

    private static List<Project> projects(List<BsExportData> dataList) {
        return dataList.stream().map(d -> {
            Project p = new Project();
            p.setProjectId(Long.parseLong(d.getRelatedId()));
            return p;
        }).collect(Collectors.toList());
    }

    private static List<BsExportData> dataList() {
        BsExportData data = new BsExportData();
        data.setRelatedId("1");
        data.setRelatedType("project_editor");
        data.setDataLog(
            "{\"config\":{\"export\":[{\"key\":\"projectId\",\"index\":0},{\"key\":\"customize.OverallBldgCondition\",\"index\":1},{\"key\":\"customize.CompletionDate\",\"index\":2}]},\"data\":[{\"key\":\"customize.CompletionDate\",\"value\":\"None\"},{\"key\":\"customize.OverallBldgCondition\",\"value\":\"Good\"},{\"key\":\"customize.CaseType\",\"value\":\"Swyfft Residential Exterior\"}]}");
        return new ArrayList<>(Arrays.asList(data));
    }

    @Test
    public void fetchExcelData() {
        ExportProjectForSwyfftUtilNew util = new ExportProjectForSwyfftUtilNewV1();
        List<BsExportData> dataList = dataList();
        List<Project> projects = projects(dataList);
        ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData excelData = util.fetchExcelData(projects, dataList);

        assertTrue(projects.size() == dataList.size());
        assertEquals(dataList.size(), excelData.getRowCount());
        assertTrue(excelData.getFile().length > 0);

        Project p = new Project();
        p.setProjectId(1000000);
        projects.add(p);

        excelData = util.fetchExcelData(projects, dataList);

        assertTrue(projects.size() > dataList.size());
        assertEquals(dataList.size(), excelData.getRowCount());
        assertTrue(excelData.getFile().length > 0);

        projects = projects(dataList);
        BsExportData data = new BsExportData();
        data.setRelatedId("999");
        data.setRelatedType("project_editor");
        dataList.add(data);

        excelData = util.fetchExcelData(projects, dataList);

        assertTrue(projects.size() < dataList.size());
        assertEquals(projects.size(), excelData.getRowCount());
        assertTrue(excelData.getFile().length > 0);
    }
}
