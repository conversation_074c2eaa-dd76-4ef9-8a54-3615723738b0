package com.bees360.web.openapi;

import com.bees360.WebApplicationTestContext;
import com.bees360.entity.Project;
import com.bees360.service.ProjectService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import java.util.Optional;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2020-1-6
 */
@Disabled
public class OpenApiProjectControllerTest extends WebApplicationTestContext {

    @Mock
    private ProjectService projectService;

    @InjectMocks
    private OpenApiProjectController openApiProjectController;

    private MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        this.mockMvc = MockMvcBuilders.standaloneSetup(openApiProjectController).build();
    }

    @Test
    public void testDownloadProjectImagesArchive() throws Exception {
        String getProjectImagesUrl = "/v1/projects/projectId/images/archive";
        String imagesArchiveUrl =
            "/v1/projects/projectId/files/1003451_6411 Fannin Street, Houston, TX 77030_882535.zip";

        long projectId = 1003451l;
        getProjectImagesUrl = getProjectImagesUrl.replace("projectId", String.valueOf(projectId));
        imagesArchiveUrl = imagesArchiveUrl.replace("projectId", String.valueOf(projectId));

        Project project = new Project();
        project.setProjectId(projectId);
        project.setImagesArchiveUrl(imagesArchiveUrl);

        Mockito.when(projectService.getById(projectId)).thenReturn(Optional.of(project).get());
        // @formatter:off
        MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders
                .get(getProjectImagesUrl))
                .andExpect(status().is3xxRedirection())
                .andReturn();
        // @formatter:on
        Assertions.assertEquals("application/zip", result.getResponse().getContentType());
        String redirectedUrl = result.getResponse().getRedirectedUrl();
        Assertions.assertEquals(imagesArchiveUrl, redirectedUrl);
    }
}
