package com.bees360.config;

import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.event.registry.ProjectPolicyUpdatedEvent;
import com.bees360.service.firebase.FirebaseService;
import com.google.common.util.concurrent.MoreExecutors;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

@SpringBootTest(
    classes = {
        SyncProjectPolicyToFirebaseConfigTest.Config.class,
        AutoRegisterEventListenerConfig.class,
    }
)
@EnableConfigurationProperties
@ActiveProfiles("SyncProjectPolicyToFirebaseConfigTest")
public class SyncProjectPolicyToFirebaseConfigTest {

    @Import({
        SyncProjectPolicyToFirebaseConfig.class,
    })
    public static class Config {

        @Bean
        InMemoryEventPublisher inMemoryEventPublisher() {
            return new InMemoryEventPublisher(MoreExecutors.directExecutor());
        }
    }

    @MockBean
    FirebaseService firebaseService;

    @Autowired
    InMemoryEventPublisher inMemoryEventPublisher;

    @Test
    void testSyncPolicyToFirebaseIfProjectPolicyUpdated() {
        var event = new ProjectPolicyUpdatedEvent();
        event.setProjectIds(List.of("1", "2"));

        // run
        inMemoryEventPublisher.publish(event);

        verify(firebaseService).syncProjectToFirebase(eq(1L));
        verify(firebaseService).syncProjectToFirebase(eq(2L));
    }
}
