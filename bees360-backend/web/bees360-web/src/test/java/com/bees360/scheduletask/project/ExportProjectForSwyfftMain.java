package com.bees360.scheduletask.project;

import com.bees360.config.DateConvertConfig;
import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.deser.DeserializationProblemHandler;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.converter.Converter;

import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用于平时帮huiling在邮件发送异常的时候导出数据
 *
 * <AUTHOR> Yang
 */
public class ExportProjectForSwyfftMain {

    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        SimpleModule module = new SimpleModule("ExportProjectForSwyfftMainModule");
        module.addDeserializer(LocalDate.class, new StdDeserializer<LocalDate>(String.class) {

            @Override
            public LocalDate deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
                String str = jsonParser.getValueAsString();
                return LocalDate.parse(str, DateTimeFormatter.ofPattern("d/MM/yyyy"));
            }
        });
        module.addDeserializer(LocalDateTime.class, new StdDeserializer<LocalDateTime>(String.class) {

            @Override
            public LocalDateTime deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
                String str = jsonParser.getValueAsString();
                return LocalDateTime.parse(str, DateTimeFormatter.ofPattern("d/MM/yyyy HH:mm:ss"));
            }
        });

        module.addDeserializer(LocalTime.class, new StdDeserializer<LocalTime>(String.class) {

            @Override
            public LocalTime deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
                String str = jsonParser.getValueAsString();
                return LocalTime.parse(str, DateTimeFormatter.ofPattern("HH:mm:ss"));
            }
        });

        mapper.registerModule(module);
        mapper.addHandler(new DeserializationProblemHandler() {
            @Override
            public Object handleWeirdStringValue(DeserializationContext ctxt, Class<?> targetType, String valueToConvert, String failureMsg) throws IOException {
                boolean isBooleanType = (targetType == Boolean.class) || (targetType == Boolean.TYPE);
                if (isBooleanType && StringUtils.equalsAny(valueToConvert, "1", "0")) {
                    return StringUtils.equals(valueToConvert, "1");
                }
                return super.handleWeirdStringValue(ctxt, targetType, valueToConvert, failureMsg);
            }
        });
    }

    public static class DataRepository {

        private List<Project> projects;
        private List<BsExportData> exportData;

        private final File resourceDir; //dirs = new File("/Users/<USER>/Work/Bees360/data-export/");

        public DataRepository(File resourceDir) {
            this.resourceDir = resourceDir;
        }

        /**
         * set @projectStatus = 90;
         * set @createdTimeStart = 1607925600000;
         * set @createdTimeEnd = 1608011999000;
         * set @companyId = 2357;
         *
         * select * from Project where insurance_company = @companyId and project_id in (
         *     select project_id from ProjectStatus
         *         where status = @projectStatus and is_deleted = 0
         *             and created_time >= @createdTimeStart and created_time < @createdTimeEnd);
         */
        public List<Project> projects() {
            List<BsExportData> dataList = dataList();

            return dataList.stream().map(d -> {
                Project p = new Project();
                p.setProjectId(Long.parseLong(d.getRelatedId()));
                return p;
            }).collect(Collectors.toList());
        }

        private List<Project> projectsReal() {
            if (projects == null) {
                try {
                    projects = projects(new File(resourceDir, "project.json"));
                } catch (IOException e) {
                    throw new IllegalStateException(e);
                }
            }
            return projects;
        }

        /**
         * select * from bs_export_data where related_type = 'project_editor'
         *     and related_id in (
         *         select PS.project_id from ProjectStatus PS, Project P
         *             where PS.project_id = P.project_id and P.insurance_company = @companyId and PS.status = @projectStatus and PS.is_deleted = 0
         *                 and PS.created_time >= @createdTimeStart and PS.created_time < @createdTimeEnd);
         */
        public List<BsExportData> dataList() {
            if (exportData == null) {
                try {
                    exportData = dataList(new File(resourceDir, "bs_export_data.json"));
//                    exportData.sort((o1, o2) -> {
//                        Long o1L = Long.parseLong(o1.getRelatedId());
//                        Long o2L = Long.parseLong(o2.getRelatedId());
//                        return Long.compare(o1L, o2L);
//                    });
                } catch (IOException e) {
                    throw new IllegalStateException(e);
                }
            }
            return exportData;
        }

        private List<Project> projects(File json) throws IOException {
            List<Project> beanList = mapper.readValue(json, new TypeReference<List<Project>>() {});
            return beanList;
        }

        private List<BsExportData> dataList(File json) throws IOException {
            List<BsExportData> beanList = mapper.readValue(json, new TypeReference<List<BsExportData>>() {});
            return beanList;
        }

    }

    public static void main(String[] args) throws IOException {
        File resourceDir = new File("/Users/<USER>/Work/Bees360/data-export/20201215/");
        DataRepository dataRepository = new DataRepository(resourceDir);

        ExportProjectForSwyfftUtilNew util = new ExportProjectForSwyfftUtilNewV1();

        List<Project> projects = dataRepository.projectsReal();
        List<BsExportData> dataList = dataRepository.dataList();
        dataList.sort(Comparator.comparingLong(d -> Long.parseLong(d.getRelatedId())));
        Map<String, Project> projectMap = projects.stream().collect(Collectors.toMap(p -> p.getProjectId() + "", p -> p));
        dataList = dataList.stream().filter(d -> projectMap.containsKey(d.getRelatedId())).collect(Collectors.toList());
        projects = dataList.stream().map(d -> projectMap.get(d.getRelatedId())).collect(Collectors.toList());

        List<String> projectIds = projects.stream().map(Project::getProjectId).map(String::valueOf).collect(Collectors.toList());
        System.out.println(StringUtils.join(",", Arrays.toString(projectIds.toArray(new String[0]))));

        ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData excelData = util.fetchExcelData(projects, dataList);

        File excel = new File(resourceDir, resourceDir.getName() + "-for-swyfft.xlsx");
        FileUtils.writeByteArrayToFile(excel, excelData.getFile());

        System.out.println(dataList.size() + " lines is export to file " + excel);
    }
}
