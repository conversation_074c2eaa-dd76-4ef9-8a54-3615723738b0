package com.bees360.event;

import com.bees360.address.Address;
import com.bees360.address.AddressProvider;
import com.bees360.address.Message;
import com.bees360.entity.Project;
import com.bees360.event.registry.AddressChanged;
import com.bees360.listener.SyncProjectToAiOnProjectAddressChangeEvent;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.ProjectService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {UpdateProjectAddressOnAddressChangedTest.Config.class,})
class UpdateProjectAddressOnAddressChangedTest {

    @Configuration
    static class Config {

        @Bean
        ProjectService projectService() {
            return Mockito.mock(ProjectService.class);
        }

        @Bean
        SyncProjectToAiOnProjectAddressChangeEvent syncProjectToAiOnProjectAddressChangeEvent(ProjectService projectService) {
            return new SyncProjectToAiOnProjectAddressChangeEvent(projectService);
        }

        @Bean
        ProjectMapper projectMapper() {
            return Mockito.mock(ProjectMapper.class);
        }
    }

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectMapper projectMapper;

    @SpyBean
    private SyncProjectToAiOnProjectAddressChangeEvent syncProjectToAiOnProjectAddressChangeEvent;

    private ProjectService mockProjectService() {
        var projectService = Mockito.mock(ProjectService.class);
        when(projectService.listProjectIdsByAddressId(anyString())).thenReturn(List.of(1L));
        var project = new Project();
        project.setProjectId(1L);
        project.setCountry("US");
        project.setState("TX");
        project.setCity("Houston");
        project.setZipCode("");
        project.setAddress("2825 Wilcrest Drive, Suite 270");
        project.setLat(29.7335656);
        project.setLng(-95.5735331);
        when(projectService.getById(eq(1L))).thenReturn(project);
        return projectService;
    }

    @SneakyThrows
    @Test
    void testUpdateAddress() {
        var addressProvider = Mockito.mock(AddressProvider.class);
        var listener = new UpdateProjectAddressOnAddressChanged(eventPublisher, mockProjectService(), addressProvider, projectMapper);

        var projectId = 1L;
        var addressChangedFrom = Address.from(Message.AddressMessage.newBuilder()
            .setId("1")
            .setState("TX")
            .setCity("Houston")
            .build());
        var addressChangedTo = Address.from(Message.AddressMessage.newBuilder()
            .setId("1")
            .setState("Texas")
            .setCity("Houston")
            .build());

        when(addressProvider.findById(eq(addressChangedTo.getId()))).thenReturn(addressChangedTo);
        var event = new AddressChanged();
        event.setOldAddress(addressChangedFrom);
        event.setNewAddress(addressChangedTo);

        Mockito.reset(projectService);
        assertDoesNotThrow(() -> listener.handle(event));
        verify(projectService, times(1)).transferDatasToAi(eq(projectId), eq("ADDRESS_CHANGED"));
    }

    @SneakyThrows
    @Test
    void testNotChangedIfAddressSameInProject() {
        var addressProvider = Mockito.mock(AddressProvider.class);
        var listener = new UpdateProjectAddressOnAddressChanged(eventPublisher, mockProjectService(), addressProvider, projectMapper);

        var projectId = 1L;
        var addressChangedFrom = Address.from(Message.AddressMessage.newBuilder()
            .setId("1")
            .setState("TX")
            .setCity("Houston")
            .build());
        var addressChangedTo = Address.from(Message.AddressMessage.newBuilder()
            .setId("1")
            .setState("TX")
            .setCity("Houston")
            .setStreetAddress("2825 Wilcrest Drive, Suite 270")
            .setLat(29.7335656)
            .setLng(-95.5735331)
            .build());

        when(addressProvider.findById(eq(addressChangedTo.getId()))).thenReturn(addressChangedTo);
        var event = new AddressChanged();
        event.setOldAddress(addressChangedFrom);
        event.setNewAddress(addressChangedTo);

        Mockito.reset(projectService);
        assertDoesNotThrow(() -> listener.handle(event));
        verify(projectService, times(0)).transferDatasToAi(eq(projectId), eq("ADDRESS_CHANGED"));
    }

    @SneakyThrows
    @Test
    void testNotChangedIfCreateNewAddress() {
        var addressProvider = Mockito.mock(AddressProvider.class);

        var addressChangedTo = Address.from(Message.AddressMessage.newBuilder().setId("1").build());

        var listener = new UpdateProjectAddressOnAddressChanged(eventPublisher, mockProjectService(), addressProvider, projectMapper);

        var event = new AddressChanged();
        event.setOldAddress(null);
        event.setNewAddress(addressChangedTo);


        Mockito.reset(projectService);
        assertDoesNotThrow(() -> listener.handle(event));
        verify(projectService, times(0)).transferDatasToAi(anyLong(), anyString());
    }

    @SneakyThrows
    @Test
    void testNotChangedWhenDeleteAddress() {
        var addressProvider = Mockito.mock(AddressProvider.class);

        var addressChangedFrom = Address.from(Message.AddressMessage.newBuilder().setId("1").build());

        var listener = new UpdateProjectAddressOnAddressChanged(eventPublisher, mockProjectService(), addressProvider, projectMapper);

        var event = new AddressChanged();
        event.setOldAddress(addressChangedFrom);
        event.setNewAddress(null);

        Mockito.reset(projectService);
        assertDoesNotThrow(() -> listener.handle(event));
        verify(projectService, times(0)).transferDatasToAi(anyLong(), anyString());
    }

    @SneakyThrows
    @Test
    void testNotUpdateIfAddressComponentNotChanged() {
        var addressProvider = Mockito.mock(AddressProvider.class);

        var addressChangedFrom = Address.from(Message.AddressMessage.newBuilder()
            .setId("1")
            .setState("TX")
            .setCity("Houston")
            .build());
        var addressChangedTo = Address.from(addressChangedFrom.toMessage());

        var listener = new UpdateProjectAddressOnAddressChanged(eventPublisher, mockProjectService(), addressProvider, projectMapper);

        var event = new AddressChanged();
        event.setOldAddress(addressChangedFrom);
        event.setNewAddress(addressChangedTo);

        Mockito.reset(projectService);
        assertDoesNotThrow(() -> listener.handle(event));
        verify(projectService, times(0)).transferDatasToAi(anyLong(), anyString());
    }
}
