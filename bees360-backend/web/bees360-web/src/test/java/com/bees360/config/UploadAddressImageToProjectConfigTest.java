package com.bees360.config;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.address.Address;
import com.bees360.address.AddressImageResourceProvider;
import com.bees360.address.AddressProvider;
import com.bees360.base.exception.ServiceException;
import com.bees360.common.util.UUIDUtil;
import com.bees360.config.project.UploadAddressImageToProjectConfig;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.User;
import com.bees360.entity.dto.ProjectImageUploadResponseDto;
import com.bees360.entity.enums.AiBotUserEnum;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.image.ImageTagManager;
import com.bees360.job.Job;
import com.bees360.job.UploadAddressImagesToProjectJobExecutor;
import com.bees360.job.registry.UploadAddressImageToProjectJob;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.mapper.ProjectMapper;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.policy.Policy;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import com.bees360.resource.FileResourceRepository;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.util.ContentTypes;
import com.bees360.resource.util.Resources;
import com.bees360.service.ProjectImageService;
import com.bees360.service.ProjectStatusService;
import com.bees360.util.Iterables;
import com.google.common.util.concurrent.Futures;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
@SpringBootTest(classes = {TestConfig.class, UploadAddressImageToProjectConfigTest.Config.class,})
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
@Transactional
public class UploadAddressImageToProjectConfigTest {
    private static final String FILE_PATH = "/tmp/resource/UploadAddressImageToProjectJob/";
    private static final Function<Resource, String> ADDRESS_IMAGE_RESOURCE_FILE_NAME_CONVERTER = resource -> {
        var metadata = resource.getMetadata();
        var eTag = metadata.getETag().replaceAll("\"", "");
        var contentType = metadata.getContentType();
        return "elevation_" + eTag + ContentTypes.getFileExtension(contentType);
    };
    private static final AiBotUserEnum ROBOT_USER = AiBotUserEnum.AI_NEW_USER_ID;

    @Import({
        UploadAddressImageToProjectConfig.class,
        InMemoryJobScheduler.class,
    })
    @Configuration
    static class Config {

        @Bean
        AddressProvider addressProvider() {
            return mock(AddressProvider.class);
        }

        @Bean
        ResourcePool resourcePool() {
            return new FileResourceRepository(FILE_PATH);
        }

        @Bean
        ProjectIIRepository projectIIRepository() {
            return mock(ProjectIIRepository.class);
        }

        @Bean
        ProjectImageService projectImageService() {
            return mock(ProjectImageService.class);
        }

        @Bean
        ImageTagManager imageTagManager() {
            return mock(ImageTagManager.class);
        }

        @Bean
        PipelineService pipelineService() {
            return mock(PipelineService.class);
        }

        @Bean
        ProjectStatusService projectStatusService() {
            return mock(ProjectStatusService.class);
        }
    }

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private ProjectIIRepository projectIIRepository;

    @SpyBean
    private AddressImageResourceProvider addressImageResourceProvider;

    @Autowired
    private ProjectImageService projectImageService;

    @Autowired
    private ImageTagManager imageTagManager;

    @Autowired
    private PipelineService pipelineService;

    @Autowired
    private ProjectStatusService projectStatusService;

    @Autowired
    private UploadAddressImagesToProjectJobExecutor uploadAddressImagesToProjectJobExecutor;

    private String projectId;
    private String addressId;

    /**
     * Recreate the project each time to avoid confusion in Image count
     */
    @BeforeEach
    public void prepareProject() {
        var project = new Project();
        project.setAddress("2825 Wilcrest Drive, Suite 270");
        project.setCity("Houston");
        project.setState("TX");
        project.setCountry("US");
        project.setZipCode("77042");
        project.setAssetOwnerName("");
        project.setAssetOwnerEmail("");
        project.setAssetOwnerPhone("");
        project.setServiceType(ProjectServiceTypeEnum.FULL_ADJUSTMENT.getCode());
        project.setClaimType(ClaimTypeEnum.HAIL.getCode());
        project.setCatNumber("2022");
        project.setLatestStatus(ProjectStatusEnum.NEW_PROJECT.getCode());
        project.setCreatedBy(User.AI_ID);
        project.setCreatedTime(System.currentTimeMillis());
        projectMapper.insertBaseInfo(project);
        projectId = String.valueOf(project.getProjectId());
        addressId = RandomStringUtils.randomNumeric(3);
        var projectII = mockProjectII(addressId);
        when(projectIIRepository.get(projectId)).thenReturn(projectII);
    }

    @Test
    public void uploadAllAddressImagesTest() throws IOException, ServiceException {
        var userId = ROBOT_USER.getIntegerCode().longValue();
        var projectIdValue = Long.parseLong(projectId);
        var job = new UploadAddressImageToProjectJob(projectId);
        var addressImageResources = getImageResources(RandomUtils.nextInt(1, 2));
        var responseDto = generateDto(addressImageResources, projectIdValue, userId);
        doReturn(addressImageResources).when(addressImageResourceProvider).getAddressImages(addressId);
        doReturn(List.of()).when(projectImageService).listImageByImageType(anyLong(), any(FileSourceTypeEnum.class), any(ImageTypeEnum.class));
        doReturn(responseDto).when(projectImageService).uploadImages(anyLong(), anyLong(), any(FileSourceTypeEnum.class), any(), anyBoolean());

        var future = uploadAddressImagesToProjectJobExecutor.submit(Job.ofPayload(job));
        // wait job completed
        Futures.getUnchecked(future);
        verify(imageTagManager, times(1)).addAllImageTag(anyMap(), anyString());
        verify(pipelineService, times(1)).setTaskStatus(projectId, UploadAddressImagesToProjectJobExecutor.PIPELINE_TASK.getKey(), Message.PipelineStatus.DONE);
        verify(projectStatusService, times(1)).changeOnImageUploaded(userId, projectIdValue);
    }

    @Test
    public void uploadNoneRepeatAddressImagesTest() throws IOException, ServiceException {
        var userId = ROBOT_USER.getIntegerCode().longValue();
        var projectIdValue = Long.parseLong(projectId);
        var job = new UploadAddressImageToProjectJob(projectId);
        var addressImageResources = getImageResources(RandomUtils.nextInt(1, 2));
        var responseDto = generateDto(addressImageResources, projectIdValue, userId);
        var successImages = responseDto.getSuccessImages();
        doReturn(addressImageResources).when(addressImageResourceProvider).getAddressImages(addressId);
        doReturn(successImages).when(projectImageService).listImageByImageType(anyLong(), any(FileSourceTypeEnum.class), any(ImageTypeEnum.class));

        var future = uploadAddressImagesToProjectJobExecutor.submit(Job.ofPayload(job));
        // wait job completed
        Futures.getUnchecked(future);
        verify(projectImageService, times(0)).uploadImages(anyLong(), anyLong(), any(FileSourceTypeEnum.class), anyList(), anyBoolean());
        verify(imageTagManager, times(0)).addAllImageTag(anyMap(), anyString());
        verify(pipelineService, times(1)).setTaskStatus(projectId, UploadAddressImagesToProjectJobExecutor.PIPELINE_TASK.getKey(), Message.PipelineStatus.DONE);
        verify(projectStatusService, times(1)).changeOnImageUploaded(userId, projectIdValue);
    }

    private ProjectII mockProjectII(String addressId) {
        var project = mock(ProjectII.class);
        var policy = mock(Policy.class);
        var address = mock(Address.class);
        doReturn(addressId).when(address).getId();
        doReturn(address).when(policy).getAddress();
        doReturn(policy).when(project).getPolicy();
        return project;
    }

    private Iterable<? extends Resource> getImageResources(int size) throws IOException {
        List<Resource> resources = new ArrayList<>();
        var fileNameFormat = "address-image-%s.png";
        for (int i = 1; i <= size; i++) {
            var fileName = fileNameFormat.formatted(i);
            resources.add(Resources.fromFile(ResourceUtils.getFile("classpath:image/" + fileName)));
        }
        return resources;
    }

    private ProjectImageUploadResponseDto generateDto(Iterable<? extends Resource> imageResources, long projectId, long userId) {
        ProjectImageUploadResponseDto dto = new ProjectImageUploadResponseDto();
        var projectImages = Iterables.toStream(imageResources).map(resource -> imageToProjectImage(projectId, resource)).collect(Collectors.toList());
        dto.setSuccessImages(projectImages);
        return dto;
    }

    private ProjectImage imageToProjectImage(long projectId, Resource imageResource) {
        var projectImage = ProjectImage.defaultImage();
        projectImage.setOriginalFileName(ADDRESS_IMAGE_RESOURCE_FILE_NAME_CONVERTER.apply(imageResource));
        projectImage.setProjectId(projectId);
        projectImage.setFileSourceType(FileSourceTypeEnum.DRONE_IMAGE.getCode());
        projectImage.setImageType(ImageTypeEnum.ELEVATION.getCode());
        projectImage.setImageId(UUIDUtil.getImageUUID());
        return projectImage;
    }
}
