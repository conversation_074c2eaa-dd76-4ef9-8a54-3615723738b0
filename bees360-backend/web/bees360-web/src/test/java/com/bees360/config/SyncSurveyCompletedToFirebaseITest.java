package com.bees360.config;

import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.registry.ReportAdded;
import com.bees360.job.JobScheduler;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.ReportTypeEnum;
import com.bees360.service.firebase.FirebaseProjectService;
import com.google.common.util.concurrent.MoreExecutors;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.concurrent.Executor;

import static org.mockito.ArgumentMatchers.eq;

@SpringBootTest(classes = SyncSurveyCompletedToFirebaseITest.Config.class)
@TestPropertySource(properties = "spring.config.location = classpath:application-test.yml")
public class SyncSurveyCompletedToFirebaseITest {

    @Configuration
    @Import({
        SyncSurveyCompletedToFirebaseConfig.class,
        InMemoryEventPublisher.class,
        InMemoryJobScheduler.class,
    })
    static class Config {
        @Bean
        public Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @MockBean
    private ProjectReportProvider projectReportProvider;

    @MockBean
    private FirebaseProjectService firebaseProjectService;

    @Autowired
    private EventPublisher eventPublisher;

    @Autowired
    private JobScheduler jobScheduler;

    @Test
    void testSyncSurveyCompleted() {
        var projectId = "1" + RandomStringUtils.randomNumeric(8);
        var reportId = RandomStringUtils.randomAlphabetic(12);
        Mockito.when(projectReportProvider.findProjectId(eq(reportId)))
            .thenAnswer(e -> List.of(projectId));

        var event = new ReportAdded();
        event.setId(reportId);
        event.setReportType(ReportTypeEnum.HIS.getKey());

        eventPublisher.publish(event);

        Mockito.verify(firebaseProjectService, Mockito.times(1))
            .syncSurveyCompletedToFirebase(eq(Long.parseLong(projectId)));
    }

    @Test
    void testIgnoreWhenNotHISReport() {
        var projectId = "1" + RandomStringUtils.randomNumeric(8);
        var reportId = RandomStringUtils.randomAlphabetic(12);
        Mockito.when(projectReportProvider.findProjectId(eq(reportId)))
            .thenAnswer(e -> List.of(projectId));

        var event = new ReportAdded();
        event.setId(reportId);
        event.setReportType(ReportTypeEnum.FUR.getKey());

        eventPublisher.publish(event);

        Mockito.verify(firebaseProjectService, Mockito.never())
            .syncSurveyCompletedToFirebase(eq(Long.parseLong(projectId)));
    }
}
