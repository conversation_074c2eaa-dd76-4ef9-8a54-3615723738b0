package com.bees360.web.security;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.bees360.auth.CustomTokenReader;
import com.bees360.user.Message;
import com.bees360.user.User;
import com.bees360.config.WebMvcConfig;
import com.bees360.config.support.CurUserId;
import com.bees360.config.support.CurUserIdMethodArgumentResolver;
import com.bees360.web.security.expression.SecurityAccess;

import lombok.extern.log4j.Log4j2;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtClaimsSet;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.JwtEncoderParameters;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.RequestPostProcessor;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;

@Log4j2
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {
    SecurityConfigTest.Config.class,
    SecurityConfig.class,
    SecurityConfigTest.SecurityTestingEndpoint.class,
})
@TestPropertySource({"/application.yml", "/application-security.yml"})
@ActiveProfiles("security")
@EnableConfigurationProperties
@WebMvcTest
class SecurityConfigTest {

    @RestController
    public static class SecurityTestingEndpoint {

        @Autowired
        CustomTokenReader customTokenReader;

        @PatchMapping("/users/{id}/status")
        String getPrivate(@PathVariable long id) {
            return "uid:" + id;
        }

        @GetMapping("/users/user-list")
        String getUserList() {
            return "list";
        }

        @GetMapping("/projects/{projectId}/logs")
        String getProjectLogs(@PathVariable long projectId) {
            return "projectId:" + projectId;
        }

        @GetMapping("/user/company")
        String getCustomerId() {
            return customTokenReader.getByKey("company_id");
        }

        @GetMapping("/user/current-id")
        long getCurrentUserId(@CurUserId long userId) {
            return userId;
        }

        @GetMapping("/user/user")
        User getUser(@AuthenticationPrincipal User user) {
            return user;
        }

        @GetMapping("/user/bifrost")
        co.realms9.bifrost.User getBifrostUser(@AuthenticationPrincipal co.realms9.bifrost.User user) {
            return user;
        }

        @GetMapping("/projects/collaboration/candidates")
        List<String> getCandidates() {
            return List.of("ONE");
        }
    }

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private MockMvc mvc;

    @Autowired private JwtEncoder jwtEncoder;

    @Autowired
    private User userPrincipal;

    @Import({
        CurUserIdMethodArgumentResolver.class,
        WebMvcConfig.class,
    })
    @Configuration
    static class Config {

        @Bean("SCAC")
        public SecurityAccess securityAccess() {
            var access = Mockito.mock(SecurityAccess.class);
            when(access.isMemberWith(any(Authentication.class), eq(1000), eq("PROCESSOR"))).thenReturn(true);
            log.info("Created SCAC with {}", access);
            return access;
        }

        @Bean
        User userPrincipal() {
            return User.from(
                Message.UserMessage.newBuilder()
                    .setId("1000059")
                    .setEmail("<EMAIL>")
                    .setPhone("1234567890")
                    .setName("ygr")
                    .setPhoto("http://photo.photo.photo")
                    .build());
        }
    }

    @BeforeEach
    public void setup() {
        mvc = MockMvcBuilders
            .webAppContextSetup(context)
            .apply(springSecurity())
            .build();
    }

    @Test
    void testRequestNotFoundEndpoint_then401() throws Exception {
        mvc.perform(get("/not-found/endpoint")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isUnauthorized());
    }

    @Test
    void whenUnauthenticated_thenUnauthorized() throws Exception {
        mvc.perform(get("/users/user-list")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(username = "test-user")
    void whenAuthenticatedWithUser_then404() throws Exception {
        mvc.perform(get("/api/test")
                .with(user("test-user"))
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound());
    }

    @Test
    void whenAuthenticatedWithBearer_thenOk() throws Exception {
        var accessToken = createJwt("ROLE_ADMIN").getTokenValue();
        mvc.perform(get("/users/user-list")
                .with(bearerToken(accessToken))
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().string("list"));
    }

    @Test
    void testGetProjectWithCustomizedExpression() throws Exception {
        mvc.perform(get("/projects/1/logs")
                .with(user("test-user").authorities(new SimpleGrantedAuthority("ROLE_ADMIN")))
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().string("projectId:1"));
    }

    @Test
    void testCustomTokenReader() throws Exception {
        var accessToken = createJwt().getTokenValue();
        mvc.perform(
                get("/user/company")
                    .with(bearerToken(accessToken))
                    .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().string("2770"));
    }

    @Test
    void testCannotGetProjectLog() throws Exception {
        var accessToken = createJwt("ROLE_PILOT").getTokenValue();
        mvc.perform(get("/projects/1/logs")
                    .with(bearerToken(accessToken))
                    .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isForbidden());
    }

    @Test
    void testCannotAccessNotMemberIn_Failure() throws Exception {
        var accessToken = createJwt("ROLE_PROCESSOR").getTokenValue();
        mvc.perform(get("/projects/1/logs")
                .with(bearerToken(accessToken))
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isForbidden());
    }

    @Test
    void testAccessIsMemberOfProject_Success() throws Exception {
        var accessToken = createJwt("ROLE_PROCESSOR").getTokenValue();
        mvc.perform(get("/projects/1000/logs")
                .with(bearerToken(accessToken))
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isForbidden());
    }

    /**
     * Testing {@link CurUserIdMethodArgumentResolver} and {@link CurUserId}.
     */
    @Test
    void testGetCurrentUserId() throws Exception {
        var accessToken = createJwt().getTokenValue();
        mvc.perform(
                get("/user/current-id")
                    .with(bearerToken(accessToken)))
            .andExpect(status().isOk())
            .andExpect(content().string("1000059"));
    }

    /**
     * Testing {@link @AuthenticationPrincipal} and {@link User}.
     */
    @Test
    void testGetUser() throws Exception {
        var accessToken = createJwt().getTokenValue();
        var resultContent = mvc.perform(
                get("/user/user")
                    .with(bearerToken(accessToken)))
            .andExpect(status().isOk())
            .andReturn().getResponse().getContentAsString();
        assertTrue(resultContent.contains("\"username\":\"<EMAIL>\""));
    }

    /**
     * Testing {@link @AuthenticationPrincipal} and {@link User}.
     */
    @Test
    void testGetBifrostUser_expectedPrincipalNotBifrostUser() throws Exception {
        var accessToken = createJwt().getTokenValue();
        mvc.perform(
                get("/user/bifrost")
                    .with(bearerToken(accessToken)))
            .andExpect(status().isOk())
            .andExpect(content().string(""));
    }

    @Test
    void testProjectCandidates() throws Exception {
        var accessToken = createJwt("ROLE_ADMIN").getTokenValue();
        mvc.perform(
                get("/projects/collaboration/candidates")
                    .with(bearerToken(accessToken)))
            .andExpect(status().isOk())
            .andExpect(content().json("['ONE']"));
    }

    private static RequestPostProcessor bearerToken(String token) {
        return request -> {
            request.addHeader("Authorization", "Bearer " + token);
            return request;
        };
    }

    private Jwt createJwt(String... authorities) {
        var claims =
            JwtClaimsSet.builder()
                .claim("company_id", "2770")
                .claim("photo", userPrincipal.getPhoto())
                .claim("authorities", authorities)
                .claim("client_id", "bees360-com")
                .claim("phone", userPrincipal.getPhone())
                .claim("scope", "bees360.com")
                .claim("name", userPrincipal.getName())
                .claim("id", userPrincipal.getId())
                .claim("email", userPrincipal.getEmail())
                .claim("jti", "eQJrbw0jxhIo5r5nxU2_f3Jad9c")
                .claim("username", userPrincipal.getEmail())
                .build();
        var params = JwtEncoderParameters.from(claims);
        return jwtEncoder.encode(params);
    }
}
