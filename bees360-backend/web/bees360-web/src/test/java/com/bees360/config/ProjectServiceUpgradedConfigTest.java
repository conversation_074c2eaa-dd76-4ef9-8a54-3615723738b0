package com.bees360.config;

import com.bees360.project.Message;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectServiceEndpoint;
import com.bees360.project.ProjectServiceUpgradeManager;
import com.bees360.project.group.GrpcProjectGroupManager;
import com.bees360.project.group.ProjectGroupUpgradedManager;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.service.ProjectService;
import com.bees360.user.User;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(ProjectServiceEndpoint.class)
@Import(ProjectServiceUpgradedConfigTest.TestConfig.class)
@TestPropertySource(properties = {
    "grpc.server.port=0",
    "spring.config.location = classpath:application-ProjectServiceUpgradedConfigTest.yml"
})
@AutoConfigureMockMvc(addFilters = false)
@EnableWebSecurity
public class ProjectServiceUpgradedConfigTest {

    @Import(ProjectServiceUpgradedConfig.class)
    @Configuration
    static class TestConfig {
        @Bean
        GrpcProjectGroupManager grpcProjectGroupManager() {
            return mock(GrpcProjectGroupManager.class);
        }
    }

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ProjectIIManager projectIIManager;

    @MockBean
    private ProjectService projectService;

    @MockBean
    private ProjectStatusManager projectStatusManager;

    @MockBean
    private ProjectStateManager projectStateManager;

    @MockBean
    private ProjectGroupUpgradedManager projectGroupUpgradedManager;

    @MockBean
    private ProjectServiceUpgradeManager projectServiceUpgradeManager;

    @Autowired
    private ObjectMapper objectMapper;

    private User testUser;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = mock(User.class);
        when(testUser.getId()).thenReturn("2001");

        var auth = new TestingAuthenticationToken(testUser, null);
        SecurityContextHolder.getContext().setAuthentication(auth);
    }

    /**
     * 带有服务类型请求体的测试
     */
    @Test
    void testServiceUpgradeWithRequestBody() throws Exception {
        // 准备测试数据
        String projectId = "1001";
        int serviceTypeTo = 3;

        // 准备项目状态
        var stateProjectOpen = Message.ProjectMessage.ProjectState.newBuilder()
            .setState(Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_OPEN)
            .build();

        // 创建项目对象
        var projectII = ProjectII.from(Message.ProjectMessage.newBuilder()
                .setId(projectId)
                .setServiceType(Message.ServiceType.LIMITED_EXTERIOR_UNDERWRITING)
                .setCurrentState(stateProjectOpen)
                .build());

        // 设置mock行为
        when(projectIIManager.findById(projectId)).thenReturn(projectII);

        // 创建请求体
        String requestBody = "{\"serviceTypeTo\": " + serviceTypeTo + "}";

        // 执行请求并验证
        mockMvc.perform(
            put("/project/{projectId}/service/upgrade", projectId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .characterEncoding("UTF-8")
                .with(request -> {
                    request.setAttribute("user", testUser);
                    return request;
                })
        )
        .andExpect(status().isOk());

        verify(projectServiceUpgradeManager, times(1)).upgrade(nullable(String.class), eq(projectId), eq(serviceTypeTo));
    }
}
