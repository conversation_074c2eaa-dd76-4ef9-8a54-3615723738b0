package com.bees360.scheduletask.project;

import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
public class ExportProjectForSwyfftUtilNewNullTest {

    @Test
    public void fetchExcelData() {
        ExportProjectForSwyfftUtilNewNull util = new ExportProjectForSwyfftUtilNewNull();

        List<Project> projects = projects();
        List<BsExportData> dataList = dataList();

        ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData excelData = util.fetchExcelData(projects, dataList);

        assertEquals(dataList.size(), excelData.getRowCount());
    }

    private static List<BsExportData> dataList() {
        BsExportData data = new BsExportData();
        data.setRelatedId("1");
        data.setRelatedType("project_editor");
        data.setDataLog(
            "{\"DogSign\":\"Y\",\"InspectionSummary\":\"\",\"RiskOpinion\":\"good\",\"CommentToElevation\":\"\",\"CommentToRoof\":\"\",\"GeneralComments\":\"\"}");
        return Arrays.asList(data);
    }

    private static List<Project> projects() {
        Project project = new Project();
        project.setProjectId(1L);
        project.setCreatedBy(System.currentTimeMillis());
        project.setInspectionNumber("1234567");
        project.setAssetOwnerName("zhangsan");
        project.setAssetOwnerEmail("<EMAIL>");
        project.setYearBuilt("12");
        project.setClaimNote("abababab");
        project.setInspectionTime(System.currentTimeMillis());
        return Arrays.asList(project);
    }
}
