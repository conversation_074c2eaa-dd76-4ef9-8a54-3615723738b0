package com.bees360.scheduletask.project;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;
import org.apache.commons.io.FileUtils;

/**
 * <AUTHOR>
 */
public class ExportProjectForSwyfftUtilV2Test {

    public static void main(String[] args) throws IOException {
        ExportProjectForSwyfftUtil util = new ExportProjectForSwyfftUtilV2();
        List<Project> projects = projects();
        List<BsExportData> dataList = dataList();
        ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData excelData = util.fetchExcelData(projects, dataList);

        File file = new File("/Users/<USER>/Desktop/tmp/bs_export_data_v2_" + System.currentTimeMillis() + ".xlsx");
        System.out.println("row count: " + excelData.getRowCount());
        System.out.println("File: " + file.getAbsolutePath());
        FileUtils.writeByteArrayToFile(file, excelData.getFile());
    }

    private static List<BsExportData> dataList() {
        BsExportData data = new BsExportData();
        data.setRelatedId("1");
        data.setRelatedType("project_editor_v2");
        data.setDataLog(
            "[{\"key\":\"customize.Row1\",\"value\":\"None\"},{\"key\":\"inspectionInfo.inspectionNumber\",\"value\":\"None\"},{\"key\":\"insuredInfo.assetOwnerName\",\"value\":\"None\"},{\"key\":\"customize.CaseType\",\"value\":\"Swyfft Residential Exterior\"}]");
        return Arrays.asList(data);
    }

    private static List<Project> projects() {
        return new ArrayList<>();
    }
}
