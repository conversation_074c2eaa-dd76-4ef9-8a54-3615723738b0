package com.bees360.scheduletask.project;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.io.FileUtils;

import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;

/**
 * <AUTHOR>
 */
public class ExportProjectForSwyfftUtilV1Test {

    public static void main(String[] args) throws IOException {
        ExportProjectForSwyfftUtil util = new ExportProjectForSwyfftUtilV1();
        List<Project> projects = projects();
        List<BsExportData> dataList = dataList();
        ExportProjectSummeryForSwyfftUnderwritingJob.ExcelData excelData = util.fetchExcelData(projects, dataList);

        File file =
            new File("/Users/<USER>/Desktop/tmp/bs_export_data_v1_" + System.currentTimeMillis() + ".xlsx");
        System.out.println("row count: " + excelData.getRowCount());
        System.out.println("File: " + file.getAbsolutePath());
        FileUtils.writeByteArrayToFile(file, excelData.getFile());
    }

    private static List<BsExportData> dataList() {
        BsExportData data = new BsExportData();
        data.setRelatedId("1");
        data.setRelatedType("project_editor_v2");
        data.setDataLog(
            "{\"DogSign\":\"Y\",\"InspectionSummary\":\"\",\"RiskOpinion\":\"good\",\"CommentToElevation\":\"\",\"CommentToRoof\":\"\",\"GeneralComments\":\"\"}");
        return Arrays.asList(data);
    }

    private static List<Project> projects() {
        Project project = new Project();
        project.setProjectId(1L);
        project.setCreatedBy(System.currentTimeMillis());
        project.setInspectionNumber("1234567");
        project.setAssetOwnerName("zhangsan");
        project.setAssetOwnerEmail("<EMAIL>");
        project.setYearBuilt("12");
        project.setClaimNote("abababab");
        project.setInspectionTime(System.currentTimeMillis());
        return Arrays.asList(project);
    }
}
