package com.bees360.scheduletask.project;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * <AUTHOR> Yang
 */
public class ExportProjectForSwyfftUtilNewFactoryTest {

    @Test
    public void newInstance() {
        ExportProjectForSwyfftUtilNew utilNull = ExportProjectForSwyfftUtilNewFactory.newInstance(ExportProjectForSwyfftUtilNewFactory.V_NULL);
        ExportProjectForSwyfftUtilNew utilV1 = ExportProjectForSwyfftUtilNewFactory.newInstance(ExportProjectForSwyfftUtilNewFactory.V1);

        Assertions.assertTrue(utilNull.getClass().isAssignableFrom(ExportProjectForSwyfftUtilNewNull.class));
        Assertions.assertTrue(utilV1.getClass().isAssignableFrom(ExportProjectForSwyfftUtilNewV1.class));
    }

    @Test
    public void newInstance_exception() {
        assertThrows(IllegalArgumentException.class, () ->
            ExportProjectForSwyfftUtilNewFactory.newInstance(ExportProjectForSwyfftUtilNewFactory.V_NULL + "_non_provided"));
    }
}
