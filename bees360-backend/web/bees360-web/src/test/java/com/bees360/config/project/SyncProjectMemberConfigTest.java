package com.bees360.config.project;

import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.job.autoconfig.AutoRegisterJobExecutorConfig;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.user.Message;
import com.bees360.user.User;
import com.bees360.user.UserKeyProvider;
import com.bees360.user.UserProvider;
import com.bees360.entity.Member;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.event.registry.ProjectMemberChanged;
import com.bees360.job.SyncProjectMemberJobExecutor;
import com.bees360.mapper.MemberMapper;
import com.google.common.util.concurrent.MoreExecutors;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(
    classes = {
        SyncProjectMemberConfigTest.Config.class,
    },
    properties = {
        "bees360.feature-switch.sync-project-member=true"
    }
)
class SyncProjectMemberConfigTest {

    @Import({
        SyncProjectMemberConfig.class,
        AutoRegisterJobExecutorConfig.class,
        AutoRegisterEventListenerConfig.class,
    })
    @Configuration
    static class Config {

        @Bean
        InMemoryJobScheduler inMemoryJobScheduler() {
            return new InMemoryJobScheduler(MoreExecutors.directExecutor());
        }

        @Bean
        InMemoryEventPublisher inMemoryEventPublisher() {
            return new InMemoryEventPublisher(MoreExecutors.directExecutor());
        }
    }

    @Autowired
    SyncProjectMemberJobExecutor syncProjectMemberJobExecutor;

    @Autowired
    InMemoryEventPublisher inMemoryEventPublisher;

    @MockitoBean
    MemberMapper memberMapper;

    @MockitoBean(name = "userProvider")
    UserProvider userProvider;

    @MockitoBean
    UserKeyProvider userKeyProvider;

    @Test
    void testHandleJob() {
        var roleId = RoleEnum.SCHEDULER.getRoleId();

        var event = new ProjectMemberChanged();
        event.setProjectId("1");
        event.setRoleId(roleId);
        event.setUserTo("10023");
        event.setOperator("10078");

        var userTo = User.from(Message.UserMessage.newBuilder().setId(event.getUserTo()).setName("Good").build());
        var operator = User.from(Message.UserMessage.newBuilder().setId(event.getOperator()).setName("Good").build());

        var member = new Member();
        member.setUserId(20091);
        member.setRole(roleId);
        when(memberMapper.listActiveMember(1)).thenReturn(List.of(member));
        when(userProvider.findUserById(event.getUserTo())).thenReturn(userTo);
        when(userProvider.findUserById(event.getOperator())).thenReturn(null);
        when(userKeyProvider.findUserByKey(event.getUserTo())).thenReturn(userTo);
        when(userKeyProvider.findUserByKey(event.getOperator())).thenReturn(operator);

        inMemoryEventPublisher.publish(event);

        verify(userProvider).findUserById(event.getUserTo());
        verify(userProvider).findUserById(event.getOperator());
        verify(userKeyProvider, never()).findUserByKey(event.getUserTo());
        verify(userKeyProvider).findUserByKey(event.getOperator());
    }
}
