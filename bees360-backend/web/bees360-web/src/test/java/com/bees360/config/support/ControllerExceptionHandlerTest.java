package com.bees360.config.support;

import com.bees360.api.DeadlineExceededException;
import com.bees360.http.ApiExceptionHandler;
import com.bees360.service.ContextProvider;
import com.bees360.service.MessageService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.context.MessageSource;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.context.request.ServletWebRequest;

import java.io.IOException;
import java.io.UncheckedIOException;

import static org.junit.jupiter.api.Assertions.assertEquals;

class ControllerExceptionHandlerTest {

    @Mock
    private MessageSource messageSource;

    @Mock
    private MessageService messageService;

    @Mock
    private ContextProvider springSecurityContextProvider;

    @Spy
    private ApiExceptionHandler apiExceptionHandler = new ApiExceptionHandler();

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private ControllerExceptionHandler controllerExceptionHandler;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testHandleDeadlineExceededException() {
        var ex = new DeadlineExceededException("timeout");
        var request = new MockHttpServletRequest();
        var response = new MockHttpServletResponse();
        var webRequest = new ServletWebRequest(request);

        var result = controllerExceptionHandler.handleException(response, request, webRequest, ex);
        assertEquals(HttpStatus.SC_GATEWAY_TIMEOUT, result.getHttpStatus().value());
        assertEquals("timeout", result.getMessage());
    }

    @Test
    void testHandleUncheckedIOException() {
        var ex = new UncheckedIOException(new IOException());
        var request = new MockHttpServletRequest();
        var response = new MockHttpServletResponse();
        var webRequest = new ServletWebRequest(request);

        var result = controllerExceptionHandler.handleException(response, request, webRequest, ex);
        assertEquals(HttpStatus.SC_INTERNAL_SERVER_ERROR, result.getHttpStatus().value());
    }
}
