package com.bees360.config;

import com.bees360.customer.Customer;
import com.bees360.customer.CustomerProvider;
import com.bees360.customer.Message;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectCreation;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.mapper.ProjectCreationMapper;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.google.protobuf.BoolValue;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class BeanConfigTest {

    private ObjectMapper objectMapper = newObjectMapper();

    private ObjectMapper newObjectMapper() {
        var mapper = new ObjectMapper(new YAMLFactory());
        mapper.setPropertyNamingStrategy(PropertyNamingStrategy.KEBAB_CASE);
        return mapper;
    }

    @SneakyThrows
    @Test
    void testProjectAutoClientReceivedPredicate() {
        ProjectCreationMapper mapper = Mockito.mock(ProjectCreationMapper.class);
        when(mapper.getByProjectId(anyLong())).thenReturn(new ProjectCreation().setCreationChannel(CreationChannelType.WEB.name()));
        Bees360CompanyConfig companyConfig = objectMapper.readValue(loadResource("company-config.yaml"), Bees360CompanyConfig.class);
        // mock customerProvider
        CustomerProvider customerProvider = Mockito.mock(CustomerProvider.class);
        var customer = mock(Customer.class);
        when(customerProvider.findById(Mockito.anyString())).thenReturn(customer);
        when(customer.getAttributes()).thenReturn(Message.CustomerAttributes.newBuilder().build());

        when(customerProvider.findById(Mockito.anyString())).thenReturn(customer);

        var predicate = new BeanConfig().projectAutoClientReceivedPredicate(
            mapper, companyConfig, new Bees360FeatureSwitch(), (id) -> true, customerProvider);
        assertTrue(predicate.test(newProject(1L, ProjectServiceTypeEnum.WHITE_GLOVE)), "White glove is allowed when applying global config.");
        assertFalse(predicate.test(newProject(2L, ProjectServiceTypeEnum.WHITE_GLOVE)), "White glove isn't allowed when auto-client-received is false.");
        assertTrue(predicate.test(newProject(3L, ProjectServiceTypeEnum.WHITE_GLOVE)), "White glove is allowed when auto-client-received is true.");
        assertFalse(predicate.test(newProject(3L, ProjectServiceTypeEnum.SCHEDULING_ONLY)), "Scheduling only isn't allowed when auto-client-received is true and exclude Scheduling One.");

        assertTrue(predicate.test(newProject(5L, ProjectServiceTypeEnum.WHITE_GLOVE)));
        assertFalse(predicate.test(newProject(5L, ProjectServiceTypeEnum.SCHEDULING_ONLY)));


        when(mapper.getByProjectId(eq(100L))).thenReturn(new ProjectCreation().setCreationChannel(CreationChannelType.OPENAPI.name()));
        assertFalse(predicate.test(newProject(1L, ProjectServiceTypeEnum.EXPRESS_INSPECTION)), "Project from OPENAPI is not allowed.");

    }

    @SneakyThrows
    @Test
    void testProjectAutoClientReceivedPredicateByAttribute() {
        ProjectCreationMapper mapper = Mockito.mock(ProjectCreationMapper.class);
        when(mapper.getByProjectId(anyLong())).thenReturn(new ProjectCreation().setCreationChannel(CreationChannelType.WEB.name()));
        Bees360CompanyConfig companyConfig = objectMapper.readValue(loadResource("company-config.yaml"), Bees360CompanyConfig.class);
        // mock customerProvider
        CustomerProvider customerProvider = Mockito.mock(CustomerProvider.class);
        var customer = mock(Customer.class);
        when(customerProvider.findById("1")).thenReturn(customer);
        when(customer.getAttributes()).thenReturn(Message.CustomerAttributes.newBuilder().setAutoClientReceived(BoolValue.of(true)).build());
        var customer2 = mock(Customer.class);
        when(customerProvider.findById("2")).thenReturn(customer2);
        when(customer2.getAttributes()).thenReturn(Message.CustomerAttributes.newBuilder().setAutoClientReceived(BoolValue.of(false)).build());


        var predicate = new BeanConfig().projectAutoClientReceivedPredicate(
            mapper, companyConfig, new Bees360FeatureSwitch(), (id) -> true, customerProvider);
        assertTrue(predicate.test(newProject(1L, ProjectServiceTypeEnum.WHITE_GLOVE)), "White glove is allowed when applying global config.");
        assertFalse(predicate.test(newProject(2L, ProjectServiceTypeEnum.WHITE_GLOVE)), "White glove isn't allowed when auto-client-received is false.");
    }

    private Project newProject(Long insuredBy, ProjectServiceTypeEnum serviceType) {
        var project = new Project();
        project.setProjectId(100);
        project.setInsuranceCompany(insuredBy);
        project.setServiceType(serviceType.getCode());
        return project;
    }

    @SneakyThrows
    private static String loadResource(String name) {
        return IOUtils.resourceToString(name, StandardCharsets.UTF_8, BeanConfigTest.class.getClassLoader());
    }
}
