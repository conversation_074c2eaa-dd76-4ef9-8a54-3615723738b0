package com.bees360.event;

import com.bees360.codec.UniversalCodec;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.event.registry.CronTriggerDailyAt1205AmCst;
import com.bees360.event.registry.CronTriggerDay1MonthlyAt1AmCst;
import com.bees360.event.registry.CronTriggerMondayWeeklyAt1AmCst;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.ProjectExportEmailSenderExecutor;
import com.bees360.job.ScheduledEmailExportEmailJobOnCronTrigger;
import com.bees360.job.autoconfig.AutoRegisterJobExecutorConfig;
import com.bees360.job.registry.IntervalProjectExportEmail;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.service.email.ProjectDataEmailSender;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.web.core.properties.bean.MailProperties;
import com.google.common.util.concurrent.MoreExecutors;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.Executor;

@SpringBootTest(
        classes = {
            SplitSubscriptionScheduledEmailExportEmailJobOnCronTriggerTest.Config.class,
        },
        properties = {
            "spring.config.location = classpath:application-SplitSubscription.yml",
        })
@EnableConfigurationProperties
public class SplitSubscriptionScheduledEmailExportEmailJobOnCronTriggerTest {

    @Import({
        InMemoryEventPublisher.class,
        InMemoryJobScheduler.class,
        MailProperties.class,
        Bees360FeatureSwitch.class,
        AutoRegisterEventListenerConfig.class,
        AutoRegisterJobExecutorConfig.class,
    })
    @Configuration
    static class Config {
        @Bean
        Executor executor() {
            return MoreExecutors.directExecutor();
        }

        @Bean
        ProjectExportEmailSenderExecutor sendEmailJobExecutor() {
            var projectDataEmailSender = Mockito.mock(ProjectDataEmailSender.class);
            return new ProjectExportEmailSenderExecutor(projectDataEmailSender);
        }

        @Bean
        ScheduledEmailExportEmailJobOnCronTrigger scheduledEmailExportEmailJobOnCronTrigger(
                JobScheduler jobScheduler,
                EventDispatcher eventDispatcher,
                MailProperties mailProperties,
                Bees360FeatureSwitch featureSwitch) {
            return new ScheduledEmailExportEmailJobOnCronTrigger(
                    jobScheduler,
                    eventDispatcher,
                    mailProperties.getTopicRecipients(),
                    featureSwitch);
        }
    }

    @Autowired EventPublisher eventPublisher;

    @MockitoSpyBean JobScheduler jobScheduler;

    @BeforeEach
    void clearMockito() {
        Mockito.clearInvocations(jobScheduler);
    }

    @Test
    void testTriggerDailyEmail() {
        var captor = ArgumentCaptor.forClass(Job.class);
        var event = new CronTriggerDailyAt1205AmCst();
        event.setTriggerTime(Instant.now().truncatedTo(ChronoUnit.DAYS));
        eventPublisher.publish(event);
        Mockito.verify(jobScheduler, Mockito.times(1)).schedule(captor.capture());
        var mailJob =
                UniversalCodec.INSTANCE.decode(
                        captor.getValue().getPayload(), IntervalProjectExportEmail.class);
        Assertions.assertIterableEquals(List.of("<EMAIL>"), mailJob.getRecipients());
    }

    @Test
    void testTriggerWeeklyEmail() {
        var captor = ArgumentCaptor.forClass(Job.class);
        var event = new CronTriggerMondayWeeklyAt1AmCst();
        event.setTriggerTime(Instant.now().truncatedTo(ChronoUnit.DAYS));
        eventPublisher.publish(event);
        Mockito.verify(jobScheduler, Mockito.times(1)).schedule(captor.capture());
        var mailJob =
                UniversalCodec.INSTANCE.decode(
                        captor.getValue().getPayload(), IntervalProjectExportEmail.class);
        Assertions.assertIterableEquals(List.of("<EMAIL>"), mailJob.getRecipients());
    }

    @Test
    void testTriggerMonthlyEmail() {
        var captor = ArgumentCaptor.forClass(Job.class);
        var event = new CronTriggerDay1MonthlyAt1AmCst();
        event.setTriggerTime(Instant.now().truncatedTo(ChronoUnit.DAYS));
        eventPublisher.publish(event);
        Mockito.verify(jobScheduler, Mockito.times(1)).schedule(captor.capture());
        var mailJob =
                UniversalCodec.INSTANCE.decode(
                        captor.getValue().getPayload(), IntervalProjectExportEmail.class);
        Assertions.assertIterableEquals(List.of("<EMAIL>"), mailJob.getRecipients());
    }
}
