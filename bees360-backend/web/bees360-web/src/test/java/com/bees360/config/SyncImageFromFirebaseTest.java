package com.bees360.config;


import com.bees360.event.EventPublisher;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.event.config.AutoRegisterEventListenerConfig;
import com.bees360.event.registry.BeespilotImageUploadEvent;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.service.firebase.FirebaseMissionService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.google.common.util.concurrent.MoreExecutors;
import lombok.SneakyThrows;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.concurrent.Executor;

import static org.mockito.ArgumentMatchers.eq;

@SpringBootTest(
    classes = SyncImageFromFirebaseTest.Config.class,
    properties = {
        "spring.config.location = classpath:application-test.yml",
        "bees360.feature-switch.disable-handle-mission-image=true",
        "bees360.feature-switch.disable-handle-ibees-mission-image=true"})
public class SyncImageFromFirebaseTest {

    @Configuration
    @EnableConfigurationProperties
    @Import({
        FirebaseJobExecutorConfig.FirebaseMissionImageConfig.class,
        InMemoryEventPublisher.class,
        InMemoryJobScheduler.class,
        AutoRegisterEventListenerConfig.class,
        AutoRegisterJobExecutorConfig.class,
        Bees360FeatureSwitch.class,
    })
    static class Config {

        @Bean
        public Executor executor() {
            return MoreExecutors.directExecutor();
        }
    }

    @MockBean
    FirebaseService firebaseService;

    @MockBean
    FirebaseMissionService firebaseMissionService;

    @Autowired
    private EventPublisher eventPublisher;

    @SneakyThrows
    @Test
    void testSyncMissionImageFromFirebase() {
        var projectId = 1 + RandomStringUtils.randomNumeric(4);
        var pilotId = RandomStringUtils.randomAlphabetic(12);
        var missionPath = "mission/" + RandomStringUtils.randomAlphabetic(6);

        Mockito.when(firebaseService.toWebUserId(eq(pilotId))).thenAnswer(e -> 1234L);

        var event = new BeespilotImageUploadEvent();
        event.setPilotId(pilotId);
        event.setProjectId(projectId);
        event.setMissionPath(missionPath);

        eventPublisher.publish(event);

        Mockito.verify(firebaseMissionService, Mockito.timeout(2000).times(1))
            .handleMissionImage(
                eq(missionPath),
                eq(1234L),
                eq(Long.parseLong(projectId))
            );
    }

    @Test
    void testSyncIBeesMissionImageFromFirebase() {
        var projectId = 1 + RandomStringUtils.randomNumeric(4);
        var pilotId = RandomStringUtils.randomAlphabetic(12);
        var missionPath = "ibees_mission/" + RandomStringUtils.randomAlphabetic(6);

        var event = new BeespilotImageUploadEvent();
        event.setPilotId(pilotId);
        event.setProjectId(projectId);
        event.setMissionPath(missionPath);

        eventPublisher.publish(event);

        Mockito.verify(firebaseMissionService, Mockito.timeout(2000).times(1))
            .handleIBeesMissionImage(
                eq(missionPath),
                eq(Long.parseLong(projectId))
            );
    }
}
