package com.bees360.scheduletask.sandbox;

import com.bees360.scheduletask.sandbox.sample.SandboxSampleResource;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * <AUTHOR>
 */
public class SandboxSampleResourceTest {

    @Test
    public void getResourceFileName() {
        Map<String, String> map = Maps.newHashMap();
        map.put("/this/is/a/useful.jpg", "useful.jpg");
        map.put("this/is/a/useful.jpg", "useful.jpg");
        map.put("useful.jpg", "useful.jpg");
        map.put(".jpg", ".jpg");
        map.put("", "");
        for(Map.Entry<String, String> entry: map.entrySet()) {
            String resourceName = SandboxSampleResource.getResourceFileName(entry.getKey());
            Assertions.assertEquals(entry.getValue(), resourceName);
        }
    }

    @Test
    public void getResourceFileName_null() {
        assertThrows(NullPointerException.class, () ->
            SandboxSampleResource.getResourceFileName(null));
    }
}
