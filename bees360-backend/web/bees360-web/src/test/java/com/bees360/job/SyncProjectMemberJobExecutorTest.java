package com.bees360.job;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.entity.Member;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.job.registry.SyncProjectMemberJob;
import com.bees360.mapper.MemberMapper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.function.Function;

/**
 * testing for {@link SyncProjectMemberJobExecutor}
 */
class SyncProjectMemberJobExecutorTest {

    @Mock
    private MemberMapper memberMapper;

    @Mock
    private Function<String, com.bees360.user.User> findUserById;

    @InjectMocks
    private SyncProjectMemberJobExecutor executor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testHandle_AddNewMember() throws Exception {
        // Arrange
        SyncProjectMemberJob job = new SyncProjectMemberJob();
        job.setProjectId(1L);
        job.setRoleId(RoleEnum.SCHEDULER.getCode());
        job.setUserId("123");
        job.setCreatedBy("creator123");

        com.bees360.user.User mockUser = mock(com.bees360.user.User.class);
        when(mockUser.getId()).thenReturn("123");
        when(mockUser.getName()).thenReturn("the-user-123");
        when(findUserById.apply("123")).thenReturn(mockUser);
        when(findUserById.apply("creator123")).thenReturn(mockUser);

        when(memberMapper.listActiveMember(1L)).thenReturn(List.of());
        when(memberMapper.getMemberByRoleAndUserId(1L, 123, RoleEnum.SCHEDULER.getCode())).thenReturn(null);

        // Act
        executor.handle(job);

        // Assert
        ArgumentCaptor<Member> memberCaptor = ArgumentCaptor.forClass(Member.class);
        verify(memberMapper).insert(memberCaptor.capture());
        Member insertedMember = memberCaptor.getValue();
        assertEquals(1L, insertedMember.getProjectId());
        assertEquals(123, insertedMember.getUserId());
        assertEquals(RoleEnum.SCHEDULER.getCode(), insertedMember.getRole());
    }

    @Test
    void testHandle_RemoveMember() throws Exception {
        // Arrange
        SyncProjectMemberJob job = new SyncProjectMemberJob();
        job.setProjectId(1L);
        job.setRoleId(RoleEnum.SCHEDULER.getCode());
        job.setUserId(null);

        Member existingMember = new Member();
        existingMember.setUserId(123);
        existingMember.setRole(RoleEnum.SCHEDULER.getCode());

        when(memberMapper.listActiveMember(1L)).thenReturn(List.of(existingMember));

        // Act
        executor.handle(job);

        // Assert
        verify(memberMapper).delete(1L, 123, RoleEnum.SCHEDULER.getCode());
    }
}
