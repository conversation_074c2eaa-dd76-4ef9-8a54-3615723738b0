package com.bees360.web.openapi;

import com.bees360.base.exception.ServiceException;
import com.bees360.contract.ContractManager;
import com.bees360.entity.openapi.OpenProjectReportSummaryVo;
import com.bees360.entity.openapi.OpenReportSummaryVo;
import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.bees360.entity.openapi.reportsummary.SummaryRisk;
import com.bees360.openapi.Message;
import com.bees360.openapi.OpenReportSummaryEndpoint;
import com.bees360.openapi.ReportSummaryEndpointConfig;
import com.bees360.project.ProjectIIRepository;
import com.bees360.service.openapi.OpenReportService;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Struct;
import com.google.protobuf.Value;
import com.google.protobuf.util.JsonFormat;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;
import java.util.function.BinaryOperator;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Import({
    OpenReportSummaryEndpointTest.Config.class,
    ReportSummaryEndpointConfig.class,
})
@WebMvcTest(OpenReportSummaryEndpoint.class)
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.location = classpath:application-OpenReportSummaryEndpointTest.yaml")
class OpenReportSummaryEndpointTest {

    @Configuration
    static class Config {

        @Bean
        OpenReportService openReportService() throws ServiceException {
            var service = mock(OpenReportService.class);
            when(service.getReportSummary(anyString())).thenAnswer(arg -> {
                String reportId = arg.getArgument(0);
                var vo = new OpenReportSummaryVo();
                vo.setId(reportId);
                var project = new OpenProjectReportSummaryVo();
                project.setId(10001);
                project.setCompletionTime(1597629371);
                project.setInspectionTime(1597609827);
                vo.setProject(project);
                // only number type report id will return summary
                if (StringUtils.isNumeric(reportId)) {
                    var summary = new ReportSummaryVo();
                    var risk = new SummaryRisk();
                    risk.setOverallCondition("Good");
                    summary.setRisk(risk);
                    vo.setSummary(summary);
                }
                return vo;
            });
            return service;
        }

        @Bean
        ProjectIIRepository projectIIRepository(){
            return mock(ProjectIIRepository.class);
        }

        @Bean
        ContractManager contractManager() {
            return mock(ContractManager.class);
        }

        @Bean
        @Primary
        BinaryOperator<String> lambdaReportSummaryConverter() {
            return (summary, projectId) -> {
                var builder = Message.ReportMessage.Summary.newBuilder();
                try {
                    JsonFormat.parser().ignoringUnknownFields().merge(summary, builder);
                    builder.addFactors(
                        Message.ReportMessage.Summary.Factor.newBuilder()
                            .setDescription("Weather Description")
                            .setValue(Value.newBuilder().setStructValue(Struct.newBuilder().putFields("weather", Value.newBuilder().setStringValue("sunny").build())))
                    ).build();
                    return JsonFormat.printer().print(builder);
                } catch (InvalidProtocolBufferException e) {
                   throw new IllegalStateException(e);
                }
            };
        }
    }

    @Autowired
    private MockMvc mockMvc;

    @Test
    void testEmptySummary() {
        var reportId = RandomStringUtils.randomAlphabetic(6);
        var actualSummaryJson = getSummary(reportId);
        var expectedSummaryJson = loadResource("openapi/summary_test_empty_summary.json").replace("${reportId}", reportId);
        JSONAssert.assertEquals(expectedSummaryJson, actualSummaryJson, true);
    }

    @Test
    void testWithSummary() {
        var reportId = RandomStringUtils.randomNumeric(6);
        var actualSummaryJson = getSummary(reportId);
        var expectedSummaryJson = loadResource("openapi/summary_test_with_summary.json").replace("${reportId}", reportId);
        JSONAssert.assertEquals(expectedSummaryJson, actualSummaryJson, true);
    }

    @SneakyThrows
    private String getSummary(String reportId) {
        var url = "/v1/report/%s/summary".formatted(reportId);
        return mockMvc.perform(MockMvcRequestBuilders.get(url))
            .andExpect(status().isOk())
            .andReturn().getResponse().getContentAsString();
    }

    @SneakyThrows
    private String loadResource(String name) {
        return IOUtils.resourceToString(name, StandardCharsets.UTF_8, this.getClass().getClassLoader());
    }
}
