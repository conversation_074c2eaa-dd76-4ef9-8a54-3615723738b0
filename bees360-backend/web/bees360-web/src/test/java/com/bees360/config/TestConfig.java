package com.bees360.config;

import net.devh.boot.grpc.server.autoconfigure.GrpcServerSecurityAutoConfiguration;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Configuration;

/** 测试依赖,去除了redis只保留了mysql */
@Configuration
@EnableAutoConfiguration(
    exclude = {
        RedisAutoConfiguration.class,
        GrpcServerSecurityAutoConfiguration.class,
    })
@MapperScan(basePackageClasses = {com.bees360.mapper.PackageMarker.class})
public class TestConfig {}
