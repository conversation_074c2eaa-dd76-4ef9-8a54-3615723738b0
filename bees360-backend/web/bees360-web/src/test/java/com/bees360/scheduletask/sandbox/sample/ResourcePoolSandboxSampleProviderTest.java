package com.bees360.scheduletask.sandbox.sample;


import com.bees360.entity.Company;
import com.bees360.entity.Project;
import com.bees360.resource.InMemoryResourceRepository;
import com.bees360.resource.Resource;
import com.google.protobuf.ByteString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ResourcePoolSandboxSampleProviderTest {


    @Test
    void testResourcePoolSandboxSampleProvider() throws IOException {
        var resourcePool = new InMemoryResourceRepository(URI.create("inmemory://resource"));
        resourcePool.put("sample/customer/Sagesure.json", Resource.of(ByteString.copyFromUtf8(IOUtils.resourceToString("sandbox/sample/Sagesure.json", StandardCharsets.UTF_8, this.getClass().getClassLoader()))));
        ResourcePoolSandboxSampleProvider provider = new ResourcePoolSandboxSampleProvider(resourcePool);
        var company = new Company();
        var project = new Project();
        project.setProjectId(10001);
        company.setCompanyKey("Sagesure");
        assertTrue(provider.supports(company));
        var sample = provider.getSampleResource(company, project);

        assertTrue(CollectionUtils.isNotEmpty(sample.getProjectImages()));
        assertTrue(CollectionUtils.isNotEmpty(sample.getReport()));
        assertTrue(MapUtils.isNotEmpty(sample.getReportSummaries()));

        company.setCompanyKey("Sagesure-Fake");
        assertFalse(provider.supports(company));
        assertNull(provider.getSampleResource(company, project));
    }
}
