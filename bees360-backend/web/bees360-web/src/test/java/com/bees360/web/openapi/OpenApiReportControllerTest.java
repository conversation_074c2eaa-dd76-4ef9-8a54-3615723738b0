package com.bees360.web.openapi;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.bees360.web.core.properties.bean.BusinessConfig;
import com.bees360.web.core.properties.bean.SystemConfig;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.service.ProjectReportFileService;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.bees360.WebApplicationTestContext;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020-1-10
 */
@Disabled
public class OpenApiReportControllerTest extends WebApplicationTestContext {

    private static final String REPORT_ID = "10234";

    @Mock
    private ProjectReportFileService projectReportFileService;

    @Mock
    private SystemConfig systemConfig;

    @Mock
    private BusinessConfig businessConfig;

    @InjectMocks
    private OpenApiReportController openApiReportController;

    private MockMvc mockMvc;

    @Spy
    private ProjectReportFile projectReportFile;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        projectReportFile = createReportFile();
        this.mockMvc = MockMvcBuilders.standaloneSetup(openApiReportController).build();
    }

    @Test
    public void downloadReportTest() throws Exception {
        long contentLength = 323487L;

        String downloadUrl = "/v1/reports/reportId/1578645549037RoofOnlyUnderwritingDamageReport.pdf";
        downloadUrl = downloadUrl.replace("reportId", String.valueOf(REPORT_ID));

        MvcResult mvcResult = this.mockMvc
                .perform(MockMvcRequestBuilders.get(downloadUrl).contentType(MediaType.APPLICATION_OCTET_STREAM))
                .andDo(result -> IOUtils.copy(
                        Objects.requireNonNull(OpenApiReportControllerTest.class.getClassLoader()
                                .getResourceAsStream("pdf/1578645549037RoofOnlyUnderwritingDamageReport.pdf")),
                        result.getResponse().getOutputStream()))
                .andExpect(status().isOk())
                .andReturn();
        Assertions.assertEquals(contentLength, mvcResult.getResponse().getContentAsByteArray().length);
    }

    private ProjectReportFile createReportFile() {
        ProjectReportFile projectReportFile = new ProjectReportFile();
        projectReportFile.setReportId(REPORT_ID);
        projectReportFile.setReportPdfFileName("https://bees360.s3.amazonaws.com/project/1001862/report/1578645549037RoofOnlyUnderwritingDamageReport.pdf");
        projectReportFile.setReportType(ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT.getCode());
        projectReportFile.setSize(123);
        return projectReportFile;
    }
}
