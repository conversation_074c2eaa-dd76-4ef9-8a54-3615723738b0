package com.bees360.scheduletask.project.util;

import org.junit.jupiter.api.Test;

import lombok.Data;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
public class ColumnValueTest {

    @Test
    public void ifSupport() {
        ColumnValue columnValue = new ColumnValue<>(NameValue.class, "col-1", NameValue::getValue);
        NameValue nameValue = new NameValue("haha", "kuku");
        String value = (String) columnValue.getValue(nameValue);
        assertEquals("kuku", value);
        assertTrue(columnValue.isSupport(nameValue));
        assertFalse(columnValue.isSupport(new ColumnValueTest()));

        try {
            columnValue.getValue(new ColumnValueTest());
            fail("shouldn't support this type.");
        } catch (IllegalArgumentException ex) {
            // successful
        }

        columnValue.ifSupport(nameValue, (val) ->
            assertEquals("kuku", val));
        columnValue.ifSupport(new ColumnValueTest(), (val) ->
            fail("this type shouldn't be ssuport."));

        NameValue nullOne = null;
        try {
            columnValue.getValue(nullOne);
            fail("null value shouldn't support this type.");
        } catch (IllegalArgumentException ex) {
            // successful
        }
        columnValue.ifSupport(nullOne, (val) ->
            fail("null value shouldn't support this type."));
    }
}
