package com.bees360.scheduletask.project.util;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
public class ColumnInsertorTest {

    @Test
    public void insertBefore() {

        List<ColumnValue> columns = cols("c0", "c1", "c2");

        ColumnInsertor.insertBefore(col("n0"), "c0", columns);
        assertEquals(columns.size(), 4);
        assertEquals("n0,c0,c1,c2", columnsToString(columns));

        ColumnInsertor.insertBefore(col("n1"), "c1", columns);
        assertEquals(columns.size(), 5);
        assertEquals("n0,c0,n1,c1,c2", columnsToString(columns));

        ColumnInsertor.insertBefore(col("n2"), "c2", columns);
        assertEquals(columns.size(), 6);
        assertEquals("n0,c0,n1,c1,n2,c2", columnsToString(columns));

        ColumnInsertor.insertBefore(col("n3"), "nonexistent", columns);
        assertEquals(columns.size(), 7);
        assertEquals("n0,c0,n1,c1,n2,c2,n3", columnsToString(columns));

        ColumnInsertor.insertBefore(col("n4"), null, columns);
        assertEquals(columns.size(), 8);
        assertEquals("n0,c0,n1,c1,n2,c2,n3,n4", columnsToString(columns));
    }

    @Test
    public void insertAfter() {
        List<ColumnValue> columns = cols("c0", "c1", "c2");

        ColumnInsertor.insertAfter(col("n0"), "c0", columns);
        assertEquals(columns.size(), 4);
        assertEquals("c0,n0,c1,c2", columnsToString(columns));

        ColumnInsertor.insertAfter(col("n1"), "c1", columns);
        assertEquals(columns.size(), 5);
        assertEquals("c0,n0,c1,n1,c2", columnsToString(columns));

        ColumnInsertor.insertAfter(col("n2"), "c2", columns);
        assertEquals(columns.size(), 6);
        assertEquals("c0,n0,c1,n1,c2,n2", columnsToString(columns));

        ColumnInsertor.insertAfter(col("n3"), "nonexistent", columns);
        assertEquals(columns.size(), 7);
        assertEquals("c0,n0,c1,n1,c2,n2,n3", columnsToString(columns));

        ColumnInsertor.insertAfter(col("n4"), null, columns);
        assertEquals(columns.size(), 8);
        assertEquals("c0,n0,c1,n1,c2,n2,n3,n4", columnsToString(columns));
    }

    @Test
    public void insert() {
        List<ColumnValue> columns = cols("c0", "c1", "c2");

        ColumnInsertor.insert(col("n0"), 0, columns);
        assertEquals(columns.size(), 4);
        assertEquals("n0,c0,c1,c2", columnsToString(columns));

        ColumnInsertor.insert(col("n1"), 2, columns);
        assertEquals(columns.size(), 5);
        assertEquals("n0,c0,n1,c1,c2", columnsToString(columns));

        ColumnInsertor.insert(col("n2"), columns.size(), columns);
        assertEquals(columns.size(), 6);
        assertEquals("n0,c0,n1,c1,c2,n2", columnsToString(columns));

        try {
            ColumnInsertor.insert(col("n3"), columns.size() + 1, columns);
            fail("the index shouldn't out of rang.");
        } catch (IndexOutOfBoundsException ex) {
            // 这个方法应该抛出异常
        }
    }

    private String columnsToString(List<ColumnValue> columns) {
        return columns.stream().map(ColumnValue::getKeyName).collect(Collectors.joining(","));
    }

    private ColumnValue col(String name) {
        return new MapColumnValue(name);
    }

    private List<ColumnValue> cols(String... names) {
        return Arrays.stream(names).map(MapColumnValue::new).collect(Collectors.toList());
    }

}
