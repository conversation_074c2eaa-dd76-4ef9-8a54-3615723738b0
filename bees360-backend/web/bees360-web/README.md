Bees360 Web
====

## API 说明

### 时间格式

具体到日期（天）：yyyyMMdd，如：20200611
具体到时间（秒）：毫秒级别时间戳，如：1591860452257
没有日期的时间（时分秒）：HH:mm:ss，如：15:27:32

**开发规范：**

- Java里用LocalDate, LocalTime和LocalDateTime类处理时间相关字段，不要使用java.util.Date，这个类的设计有诸多问题，在Java8之后已经不推荐使用了；
- 后台服务和后台用到的数据库等等所有跑在服务器上的服务，全都默认时区设成UTC，后台的服务器本地时区也要设成UTC，这样避免因为服务器迁移导致的时区问题（测试和生产都要这样设置）；
- 后端和前端交互时（也就是转成json时），如果是date字段，应该统一用string交互，日期格式采用ISO-8601格式也就是YYYYMMDD；如果是datetime字段，应该用从19700101的毫秒数；
- 后端不对时间做任何时区处理（因为所有时间都是UTC），不做任何时区转换，所有时区处理都在前端以用户本地时区做转换；
- 数据库连接应该指定时区 `serverTimezone=UTC`

**开发配置：**

在IDEA的`Run/Debug Configuration > [你的应用] > Configuration > Environment > VM options`中配置以下JVM参数，使得程序的运行时区为`UTC`
```
-Duser.timezone=UTC
```
