package com.bees360.web.grpc.service.impl;

import com.bees360.base.exception.ServiceException;
import com.bees360.common.collections.ListUtil;
import com.bees360.entity.User;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.vo.ImageIdsVo;
import com.bees360.internal.ai.grpc.api.ai2web.ImageDeleteProto;
import com.bees360.internal.ai.grpc.api.ai2web.ImageDeleteServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass.StatusCodeResponse;
import com.bees360.service.ProjectImageService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ImageCompleteDeleteServiceImpl
        extends ImageDeleteServiceGrpc.ImageDeleteServiceImplBase {

    @Autowired private ProjectImageService projectImageService;

    @Override
    public void deleteImages(
            ImageDeleteProto.ImageDeleted request,
            StreamObserver<StatusCodeResponse> responseObserver) {
        long projectId = request.getProjectId();
        List<String> imageIds = request.getImageIdList();
        int deleteStatus = request.getDeleteStatus();
        log.info(
                "deleteImages of {} from ai. status:{}, ids:{}",
                projectId,
                deleteStatus,
                ListUtil.toString(imageIds));
        ImageIdsVo imageIdsVo = new ImageIdsVo();
        imageIdsVo.setImageIds(imageIds);
        try {
            projectImageService.deleteCompletely(
                    projectId, User.AI_ID, imageIdsVo, SystemTypeEnum.BEES_AI);
            StatusCodeResponse response =
                StatusCodeResponse.newBuilder()
                    .setStatus(0)
                    .setCode("OK")
                    .setMessage("success")
                    .build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (ServiceException e) {
            log.error(
                    "deleteImages error. projectId:{} status:{}, ids:{}",
                    projectId,
                    deleteStatus,
                    ListUtil.toString(imageIds),
                    e);
            responseObserver.onError(e);
        }
    }
}
