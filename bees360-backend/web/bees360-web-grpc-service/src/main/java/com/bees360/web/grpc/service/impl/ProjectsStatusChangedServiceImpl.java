package com.bees360.web.grpc.service.impl;

import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.entity.User;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.internal.ai.grpc.api.ai2web.ProjectsStatusProto.ProjectsReworkRequest;
import com.bees360.internal.ai.grpc.api.ai2web.ProjectsStatusProto.ProjectsStatusChangedRequest;
import com.bees360.internal.ai.grpc.api.ai2web.ProjectsStatusServiceGrpc.ProjectsStatusServiceImplBase;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass.StatusCodeResponse;
import com.bees360.schedule.job.retry.RetryableJobTriggerFactory;
import com.bees360.schedule.service.SchedulerManagerTransactionalDelegate;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.job.UpdateProjectStatusJob;
import io.grpc.stub.StreamObserver;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.quartz.TriggerBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 其他端批量修改project status同步到web端的grpc入口
 *
 * <AUTHOR>
 * @since 2021/4/26
 */
@Slf4j
@Service
public class ProjectsStatusChangedServiceImpl extends ProjectsStatusServiceImplBase {

    @Autowired
    private SchedulerManagerTransactionalDelegate schedulerManagerTransactionalDelegate;

    @Autowired
    private ProjectStatusService projectStatusService;

    @Override
    public void batchUpdateProjectStatus(ProjectsStatusChangedRequest request,
        StreamObserver<StatusCodeResponse> responseObserver) {
        List<Long> projectIds = request.getProjectIdList();
        if (CollectionAssistant.isEmpty(projectIds)) {
            return;
        }
        // TODO 由于目前WEB与AI的userId不统一，可能AI端的在WEB端查不到，所以此处用默认AI userId
        long userId = User.AI_ID;
        int aiStatus = request.getStatus();
        if (!NewProjectStatusEnum.containsAiCode(aiStatus)
            || !Arrays.asList(NewProjectStatusEnum.CLIENT_RECEIVED.getAiCode(),
            NewProjectStatusEnum.PROJECT_CANCELED.getAiCode()).contains(aiStatus)) {
            log.error("Unsupported status type. projectIds:{} aiStatus:{}", ListUtil.toString(projectIds), aiStatus);
            return;
        }
        int status = NewProjectStatusEnum.getEnumByAiCode(aiStatus).getCode();

        log.info("sync projects status from ai. projectIds:{}, status:{}", ListUtil.toString(projectIds), status);

        batchUpdateStatusByQuartzJob(projectIds, status, request.getSystemType(), userId);

        StatusCodeResponse response = StatusCodeResponse.newBuilder().setStatus(0)
            .setCode("OK").setMessage("success").build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    /**
     * Quartz批量更新project status。
     *
     * @param projectIds 待修改的project的id list
     * @param status     目前仅支持 NewProjectStatusEnum.CLIENT_RECEIVED和NewProjectStatusEnum.PROJECT_CANCELED
     * @param systemType 系统类型
     * @param userId     userId
     * @see NewProjectStatusEnum
     * @see SystemTypeEnum
     */
    private void batchUpdateStatusByQuartzJob(List<Long> projectIds, int status, String systemType, long userId) {
        long time = System.currentTimeMillis();
        JobKey jobKey = UpdateProjectStatusJob.createJobKey(status, time);
        JobDetail jobDetail = UpdateProjectStatusJob.createJobDetail(projectIds, status, systemType, userId, time);
        TriggerBuilder trigger =
            RetryableJobTriggerFactory.retryForeverTrigger(jobKey.getName(), jobKey.getGroup(), Duration.ofSeconds(10));
        trigger.forJob(jobKey);
        try {
            schedulerManagerTransactionalDelegate.addJob(jobDetail, false);
            schedulerManagerTransactionalDelegate.scheduleJob(UpdateProjectStatusJob.class, trigger.build());
        } catch (SchedulerException e) {
            log.error("Fail to schedule a job to update project status.", e);
        }
    }

    /**
     * ai project rework 通知web
     * @param request rework理由
     */
    @Override
    public void reworkProject(ProjectsReworkRequest request, StreamObserver<StatusCodeResponse> responseObserver) {
        log.info("ai project rework request projectId {}", request.getProjectId());
        projectStatusService.projectReworkOnAi(User.AI_ID, request.getProjectId(), request.getTitle(),
            request.getContent());

        StatusCodeResponse response = StatusCodeResponse.newBuilder().setStatus(0)
            .setCode("OK").setMessage("success").build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
