package com.bees360.web.grpc.service.impl;

import static com.bees360.entity.enums.AiBotUserEnum.AI_NEW_USER_ID;
import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.address.AddressProvider;
import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.common.grpc.MessageBeanUtil;
import com.bees360.common.protobuf.ProtoBeanUtils;
import com.bees360.commons.firebasesupport.service.RemoteConfigService;
import com.bees360.entity.BeesPilotBatch;
import com.bees360.entity.BeesPilotBatchItem;
import com.bees360.entity.Company;
import com.bees360.entity.EventHistory;
import com.bees360.entity.Member;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectEsModel;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.ProjectMessage;
import com.bees360.entity.ProjectQuiz;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.ProjectStatusUser;
import com.bees360.entity.User;
import com.bees360.entity.consts.MemberAuth;
import com.bees360.entity.converter.ProjectQuizConveter;
import com.bees360.entity.dto.ProjectQuizDto;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectHoverStatusEnum;
import com.bees360.entity.enums.ProjectMessageTypeEnum;
import com.bees360.entity.enums.ProjectPlnarStatusEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.ProjectSyncPointEnum;
import com.bees360.entity.enums.RemoteConfigParameter;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.firebase.TaskQuizRemoteConfig;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.entity.label.ProjectLabel;
import com.bees360.entity.query.ProjectMessageQuery;
import com.bees360.entity.vo.CompanyCard;
import com.bees360.entity.vo.HistoryLogVo;
import com.bees360.entity.vo.ProjectStatusVo;
import com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel.Builder;
import com.bees360.internal.ai.grpc.api.web2ai.ReportGenerateServiceOuterClass;
import com.bees360.mapper.BeesPilotBatchItemMapper;
import com.bees360.mapper.BeesPilotBatchMapper;
import com.bees360.mapper.CompanyMapper;
import com.bees360.mapper.EventHistoryMapper;
import com.bees360.mapper.MemberMapper;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.mapper.ProjectMessageMapper;
import com.bees360.mapper.ProjectQuizMapper;
import com.bees360.mapper.ProjectStatusMapper;
import com.bees360.mapper.label.ProjectLabelMapper;
import com.bees360.project.ContactManager;
import com.bees360.project.ProjectII;
import com.bees360.repository.Provider;
import com.bees360.service.ProjectService;
import com.bees360.service.UserService;
import com.bees360.service.grpc.BuildProjectInfoConvertersService;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import com.bees360.util.user.UserAssemble;
import com.google.common.base.Preconditions;
import com.google.gson.Gson;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2020/04/30 14:24
 */
@Slf4j
@Service
public class BuildProjectInfoConvertersServiceImpl implements BuildProjectInfoConvertersService {

    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private ProjectImageMapper projectImageMapper;

    @Autowired
    private EventHistoryMapper eventHistoryMapper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectQuizMapper projectQuizMapper;

    @Autowired
    private ProjectStatusMapper projectStatusMapper;

    @Autowired
    private ProjectLabelMapper projectLabelMapper;

    @Autowired
    private BeesPilotBatchItemMapper beesPilotBatchItemMapper;
    @Autowired
    private BeesPilotBatchMapper beesPilotBatchMapper;
    @Autowired
    private ProjectMessageMapper projectMessageMapper;

    @Autowired
    private RemoteConfigService remoteConfigService;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private ContactManager contactManager;

    @Autowired
    private Provider<ProjectII> projectIIProvider;

    @Autowired
    private AddressProvider addressProvider;

    @Autowired
    private UserService userService;

    @Value("${bees360.feature-switch.enable-fill-address-time-zone:true}")
    private boolean enableFillAddressTimeZone;

    private final static int QUIZ_CODE_FOR_CLAIM = 2294;

    private final static int QUIZ_CODE_FOR_UNDERWRITING = 2096;

    private final static String EXTERNAL_ADJUSTER = "External Adjuster";
    @Override
    public Project getProject(long projectId) {
        if (projectId == 0) {
            return null;
        }
        return projectService.getById(projectId);
    }

    @Override
    public List<Member> getMembers(long projectId) {
        if (projectId == 0) {
            return null;
        }
        return memberMapper.listActiveMember(projectId);
    }

    @Override
    public Company getCompany(Long companyId) {
        if (companyId != null) {
            return companyMapper.getById(companyId);
        }
        return null;
    }

    @Override
    public List<ProjectImage> getProjectImages(long projectId, int fileSourceType)
        throws ServiceException {
        List<ProjectImage> projectImages = null;
        try {
            projectImages = projectImageMapper.listImages(projectId, fileSourceType);
        } catch (Exception e) {
            String message = "Failed to listImages from ProjectImage with projectId " + projectId;
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION, message);
        }
        return projectImages;
    }

    @Override
    public List<HistoryLogVo> getEventHistory(long projectId) {
        var histories = eventHistoryMapper.listHistoryLog(projectId);
        Set<String> userIds = new HashSet<>();
        histories.forEach(
                h -> {
                    if (Objects.isNull(h)) {
                        return;
                    }
                    Optional.ofNullable(h.getUser()).ifPresent(userIds::add);
                    Optional.ofNullable(h.getModifiedBy()).ifPresent(userIds::add);
                });
        var userMap =
                Iterables.toStream(userProvider.findUserById(userIds))
                        .collect(
                                Collectors.toMap(
                                        com.bees360.user.User::getId,
                                        com.bees360.user.User::getName));
        histories.forEach(
                historyLogVo -> {
                    historyLogVo.setUser(
                            Optional.ofNullable(userMap.get(historyLogVo.getUser()))
                                    .orElse(AI_NEW_USER_ID.getDisplay()));
                    historyLogVo.setModifiedBy(
                            Optional.ofNullable(userMap.get(historyLogVo.getModifiedBy()))
                                    .orElse(AI_NEW_USER_ID.getDisplay()));
                });
        return histories;
    }

    @Override
    public List<ReportGenerateServiceOuterClass.Member> buildMembers(long projectId) throws ServiceException {
        if (projectId == 0) {
            return null;
        }
        List<ReportGenerateServiceOuterClass.Member> grpcMembers = new ArrayList<>();
        try {
            List<com.bees360.entity.Member> members = memberMapper.listActiveMember(projectId);
            for (com.bees360.entity.Member member : members) {
                ReportGenerateServiceOuterClass.Member.Builder builder = ReportGenerateServiceOuterClass.Member.newBuilder();
                MessageBeanUtil.copyBeanPropertiesToMessage(member, builder, ReportGenerateServiceOuterClass.Member.getDescriptor());
                grpcMembers.add(builder.build());
            }
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            String message = "Failed to build members with projectId " + projectId;
            throw new ServiceException(MessageCode.SYSTEM_EXCEPTION, message);
        }
        return grpcMembers;
    }

    @Override
    public List<com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectStatusVo> buildProjectStatusVoList(List<ProjectStatusVo> timeLines) throws ServiceException {
        return timeLines.stream().map(timeLine -> {
            com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectStatusVo.Builder timeLineBuilder = com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectStatusVo.newBuilder();
            try {
                ProtoBeanUtils.toProtoBean(timeLineBuilder, timeLine);
            } catch (IOException e) {
                log.error("buildProjectStatusVoList error");
            }
            return timeLineBuilder.build();
        }).collect(Collectors.toList());
    }


    @Override
    public ReportGenerateServiceOuterClass.Company buildCompany(Long companyId) throws ServiceException {
        com.bees360.entity.Company company = null;
        if (companyId != null) {
            company = companyMapper.getById(companyId);
        }
        try {
            ReportGenerateServiceOuterClass.Company.Builder companyBuilder = ReportGenerateServiceOuterClass.Company.newBuilder();
            if (company == null) {
                return companyBuilder.getDefaultInstanceForType();
            }
            MessageBeanUtil.copyBeanPropertiesToMessage(company, companyBuilder, ReportGenerateServiceOuterClass.Company.Builder.getDescriptor());
            return companyBuilder.build();
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            String message = "Failed to build company with companyId " + companyId;
            throw new ServiceException(MessageCode.SYSTEM_EXCEPTION, message);
        }
    }


    @Override
    public CompanyCard getCompanyCard(Long companyId) {
        if (Objects.isNull(companyId)) {
            return null;
        }
        Company company = companyMapper.getById(companyId);
        if (Objects.isNull(company)) {
            return null;
        }
        CompanyCard companyCard = new CompanyCard();
        companyCard.setCompanyId(company.getCompanyId());
        companyCard.setCompanyName(company.getCompanyName());
        companyCard.setEmail(company.getEmail());
        companyCard.setPhone(company.getPhone());
        return companyCard;
    }

    @Override
    public List<ProjectQuizDto> listProjectQuiz(long projectId){
        Project project = projectService.getById(projectId);
        Preconditions.checkNotNull(project, "The project[%s] does not exist.".formatted(projectId));
        ClaimTypeEnum claimType = ClaimTypeEnum.getEnum(project.getClaimType());
        // The project does not have a claim type
        Preconditions.checkNotNull(claimType, "This project has no claim type!");
        List<ProjectQuiz> projectQuizAnswers = projectQuizMapper.listLatestAnswers(projectId, project.getClaimType());

        // 获取题目
        List<TaskQuizRemoteConfig> quizList = remoteConfigService
            .getRemoteConfig(TaskQuizRemoteConfig.class, RemoteConfigParameter.QUIZ.getName());
        Map<Long, TaskQuizRemoteConfig> quizConfigMap = quizList.stream()
            .collect(Collectors.toMap(TaskQuizRemoteConfig::getQuizId, Function.identity(), (a, b) -> a));

        return projectQuizAnswers.stream().map(e -> {
            TaskQuizRemoteConfig quizConfig = quizConfigMap.get(e.getQuizId());
            ProjectQuizDto quizDto = ProjectQuizConveter.quizConfig2ProjectQuiz(projectId, quizConfig);
            quizDto.setAnswer(e.getAnswer());
            return quizDto;
        }).collect(Collectors.toList());
    }

    @Override
    public com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel buildEsGrpcProject(long projectId, String syncPoint) {
        Project project = getProject(projectId);
        if (project == null) {
            return com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel.getDefaultInstance();
        }
        ProjectEsModel esModel = buildProjectEsModel(project, syncPoint);
        com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel projectEsMessage = buildGrpcProjectEsModel(esModel);

        projectEsMessage = addMemberInfo(projectEsMessage);
        projectEsMessage = addBatchInfo(projectEsMessage);
        projectEsMessage = addTimelines(projectEsMessage);
        projectEsMessage = addProjectState(projectEsMessage);
        if (enableFillAddressTimeZone) {
            var timeZone =
                addressProvider.findById(projectEsMessage.getAddressId()).getTimeZone();
            if (timeZone != null) {
                projectEsMessage = projectEsMessage.toBuilder().setTimeZone(timeZone.getID()).build();
            }
        }
        return projectEsMessage;
    }

    private com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel addTimelines(
            com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel projectEsMessage) {
        var projectId = projectEsMessage.getProjectId();
        var builder = projectEsMessage.toBuilder();
        var statusList = projectStatusMapper.listByProjectIdsWithUser(List.of(projectId));
        ProjectStatusUser topPointer = null;
        var timelines = new ArrayList<com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectStatusVo>();
        for (ProjectStatusUser projectStatusUser : statusList) {
            if (topPointer != null && topPointer.getStatus() == projectStatusUser.getStatus()) {
                continue;
            }
            timelines.add(map(projectStatusUser));
            topPointer = projectStatusUser;
        }
        builder.addAllTimeLines(timelines);
        return builder.build();
    }

    private com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel addProjectState(
        com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel projectEsMessage) {
        var projectId = projectEsMessage.getProjectId();
        var projectII = projectIIProvider.get(String.valueOf(projectId));
        var currentState = projectII.getCurrentState();
        var builder = projectEsMessage.toBuilder();
        builder.setProjectState(currentState.getState().name().toLowerCase());
        builder.setProjectStateChangeReason(currentState.getStateChangeReason().getDisplayText());
        return builder.build();
    }

    private com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectStatusVo map(
            ProjectStatusUser status) {
        var builder =  com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectStatusVo.newBuilder();
        Optional.ofNullable(status.getUserName()).ifPresent(builder::setUserName);
        builder.setCreatedTime(status.getCreatedTime());
        builder.setStatus(
                        com.bees360.internal.ai.grpc.api.web2ai.Project.CodeNameDto.newBuilder()
                                .setCode(status.getStatus())
                                .setName(
                                        NewProjectStatusEnum.getEnum(status.getStatus())
                                                .getDisplay())
                                .build())
                .build();
        return builder.build();
    }

    private com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel addBatchInfo(
        com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel projectEsMessage) {

        Builder builder = projectEsMessage.toBuilder();

        BeesPilotBatchItem batchItem = beesPilotBatchItemMapper
            .listByProjectIds(Collections.singletonList(projectEsMessage.getProjectId()))
            .stream().findFirst().orElse(null);

        Optional.ofNullable(batchItem).ifPresent(item -> {
            BeesPilotBatch beesPilotBatch = beesPilotBatchMapper.getByBatchNo(item.getBatchNo());
            com.bees360.internal.ai.grpc.api.web2ai.Project.BeesPilotBatch.Builder batchBuilder = com.bees360.internal.ai.grpc.api.web2ai.Project.BeesPilotBatch
                .newBuilder().setBatchNo(beesPilotBatch.getBatchNo());
            if (beesPilotBatch.getBasePay() != null) {
                double basePay = beesPilotBatch.getBasePay().doubleValue();
                batchBuilder.setBasePay(basePay);
            }
            if (beesPilotBatch.getExtraPay() != null) {
                double extraPay = beesPilotBatch.getExtraPay().doubleValue();
                batchBuilder.setExtraPay(extraPay);
            }
            if (beesPilotBatch.getPayTime() != null) {
                batchBuilder.setPaymentDate(beesPilotBatch.getPayTime());
            }
            builder.setBatchInfo(batchBuilder.build());
        });
        return builder.build();
    }

    private com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel
    addMemberInfo(com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel projectEsMessage) {

        List<Member> members = memberMapper.listActiveMember(projectEsMessage.getProjectId());
        com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel.Builder builder = projectEsMessage.toBuilder();
        List<com.bees360.internal.ai.grpc.api.web2ai.Project.ExternalMember> externalMembers = new ArrayList<>();
        if (!CollectionUtils.isEmpty(members)) {
            members.forEach(o -> {
                if (Objects.equals(o.getRole(), RoleEnum.PILOT.getRoleId())) {

                    com.bees360.user.User user = userProvider.findUserById(o.getUserId() + "");
                    User pilotUser = UserAssemble.toWebUser(user);
                    if (Objects.nonNull(pilotUser)) {
                        String pilotName = pilotUser.getFirstName().concat(" ").concat(pilotUser.getLastName());
                        builder.setPilotName(pilotName);

                        com.bees360.internal.ai.grpc.api.web2ai.Project.ExternalMember external =
                            buildExternalMember(pilotName,
                                MemberAuth.PILOT,
                                pilotUser.getEmail(),
                                pilotUser.getPhone(),
                                pilotUser.getCompanyName());
                        externalMembers.add(external);
                    }
                }
                if (Objects.equals(o.getRole(), RoleEnum.CREATOR.getRoleId())) {
                    var creatorUser = userService.getUserById(o.getUserId());
                    if (Objects.nonNull(creatorUser)) {
                        String creatorName = creatorUser.getFirstName().concat(" ").concat(creatorUser.getLastName());
                        com.bees360.internal.ai.grpc.api.web2ai.Project.ExternalMember external =
                            buildExternalMember(creatorName,
                                MemberAuth.CREATOR,
                                creatorUser.getEmail(),
                                creatorUser.getPhone(),
                                creatorUser.getCompanyName());
                        externalMembers.add(external);
                    }
                }
                if (Objects.equals(o.getRole(), RoleEnum.OPERATIONS_MANAGER.getRoleId())) {
                    var operationsManager = userService.getUserById(o.getUserId());
                    if (Objects.nonNull(operationsManager)) {
                        com.bees360.internal.ai.grpc.api.web2ai.Project.ExternalMember external =
                            buildExternalMember(
                                operationsManager.getName(),
                                MemberAuth.OPERATIONS_MANAGER,
                                operationsManager.getEmail(),
                                operationsManager.getPhone(),
                                operationsManager.getCompanyName());
                        externalMembers.add(external);
                    }
                }

            });
        }
        if (StringUtils.isNotBlank(projectEsMessage.getAgentContactName())) {
            com.bees360.internal.ai.grpc.api.web2ai.Project.ExternalMember external =
                buildExternalMember(projectEsMessage.getAgentContactName(),
                    MemberAuth.AGENT,
                    projectEsMessage.getAgentEmail(),
                    projectEsMessage.getAgentPhone(),
                null);
            externalMembers.add(external);
        }
        if (StringUtils.isNotBlank(projectEsMessage.getAssetOwnerName())) {
            com.bees360.internal.ai.grpc.api.web2ai.Project.ExternalMember external =
                buildExternalMember(projectEsMessage.getAssetOwnerName(),
                    MemberAuth.INSURED,
                    projectEsMessage.getAssetOwnerEmail(),
                    projectEsMessage.getAssetOwnerPhone(),
                null);
            externalMembers.add(external);
        }
        var externalAdjuster = getExternalAdjuster(builder,projectEsMessage);
        if (externalAdjuster != null){
            externalMembers.add(externalAdjuster);
        }
        builder.addAllExternalMember(externalMembers);
        return builder.build();
    }

    private com.bees360.internal.ai.grpc.api.web2ai.Project.ExternalMember getExternalAdjuster(
                                     com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel.Builder builder,
                                     com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel projectEsMessage){
        var result = contactManager.findByProjectId(String.valueOf(projectEsMessage.getProjectId()));
        var contacts = Iterables.toStream(result).filter(contact -> contact.getRole().equals(EXTERNAL_ADJUSTER)).collect(Collectors.toList());
        if (!contacts.isEmpty()) {
            var contact = contacts.get(0);
            acceptIfNotNull(builder::setAdjuster, contact.getFullName());
            acceptIfNotNull(builder::setAdjusterPhone, contact.getPrimaryPhone());
            acceptIfNotNull(builder::setAdjusterEmail, contact.getPrimaryEmail());
            com.bees360.internal.ai.grpc.api.web2ai.Project.ExternalMember external =
                buildExternalMember(contact.getFullName(),
                    MemberAuth.EXTERNAL_ADJUSTER,
                    contact.getPrimaryEmail(),
                    contact.getPrimaryPhone(),
                    null);
            return external;
        }
        return null;
    }

    private com.bees360.internal.ai.grpc.api.web2ai.Project.ExternalMember buildExternalMember(String name, String auth,
        String email, String phone, String company) {
        return com.bees360.internal.ai.grpc.api.web2ai.Project.ExternalMember.newBuilder()
            .setName(Optional.ofNullable(name).orElse(""))
            .setAuth(auth)
            .setEmail(Optional.ofNullable(email).orElse(""))
            .setPhone(Optional.ofNullable(phone).orElse(""))
            .setCompany(Optional.ofNullable(company).orElse(""))
            .build();
    }

    private com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel buildGrpcProjectEsModel(ProjectEsModel esModel) {
        com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel.Builder messageBuilder = com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel.newBuilder();
        try {
            ProtoBeanUtils.toProtoBean(messageBuilder, esModel);
        } catch (Exception e) {
            String message = "buildProjectEsModel build error for projectId:" + esModel.getProjectId() + "e:" + e.getMessage();
            throw new RuntimeException(message, e);
        }
        return messageBuilder.build();
    }

    private ProjectEsModel buildProjectEsModel(Project project, String syncPoint){
        String policyEffectiveDate = Optional.ofNullable(project.getPolicyEffectiveDate()).map(String::valueOf).orElse(null);
        project.setPolicyEffectiveDate(null);
        ProjectEsModel esModel = new Gson().fromJson(new Gson().toJson(project), ProjectEsModel.class);
        esModel.setCatNumber(project.getCatNumber());
        esModel.setRotationDegree(project.getRotationDegree());
        esModel.setPolicyEffectiveDate(policyEffectiveDate);
        if (esModel.getCreatedBy() != 0) {
            User user = userService.getUserById(esModel.getCreatedBy());
            if (Objects.nonNull(user)) {
                esModel.setCreatorName(user.getFirstName() + " " + user.getLastName());
            }
        }

        // 获取timeLine, build vo
        List<ProjectStatusUser> statusUsers = projectStatusMapper.listByProjectIdWithUser(project.getProjectId());
        if (!CollectionUtils.isEmpty(statusUsers)) {
            Integer statusCode = ProjectSyncPointEnum.getNewProjectStatusCode(syncPoint);
            if (Objects.nonNull(statusCode)) {
                long statusUpdateTime = statusUsers.stream()
                    .filter(o -> Objects.equals(o.getStatus(), statusCode)
                        && !Objects.equals(o.getCreatedTime(), 0L))
                    .findFirst().map(ProjectStatus::getCreatedTime).orElse(0L);
                esModel.setStatusUpdateTime(statusUpdateTime);
            }
            // find the first site-inspected status time as siteInspectedTime
            long siteInspectedTime =
                    statusUsers.stream()
                            .filter(
                                    o ->
                                            Objects.equals(
                                                    o.getStatus(),
                                                    NewProjectStatusEnum.SITE_INSPECTED.getCode()))
                            .map(ProjectStatus::getCreatedTime)
                            .min(Long::compareTo)
                            .orElse(0L);
            esModel.setSiteInspectedTime(siteInspectedTime);
            var customerContactedTime =
                    Optional.ofNullable(project.getCustomerContactedAt())
                            .map(Instant::toEpochMilli)
                            .orElse(0L);
            esModel.setCustomerContactedTime(customerContactedTime);
            esModel.setInspectedBy(getInspectedBy(project.getProjectId()));
        }

        esModel.setAgent(project.getAgent());
        esModel.setAgentContactName(project.getAgentContactName());
        esModel.setAgentEmail(project.getAgentEmail());
        esModel.setAgentPhone(project.getAgentPhone());
        Optional.ofNullable(getCompany(project.getInsuranceCompany()))
            .ifPresent(insured -> {
                esModel.setInsuranceCompanyName(insured.getCompanyName());
                esModel.setInsuranceCompanyNameLogo(insured.getLogo());
            });
        Optional.ofNullable(getCompany(project.getRepairCompany()))
            .ifPresent(repair -> {
                esModel.setRepairCompanyName(repair.getCompanyName());
                esModel.setRepairCompanyNameLogo(repair.getLogo());
            });
        Optional.ofNullable(getCompany(project.getCompanyId()))
            .ifPresent(company -> {
                esModel.setCompanyName(company.getCompanyName());
                esModel.setCompanyLogo(company.getLogo());
            });

        // quiz
        List<ProjectQuizDto> projectQuizDtos = listProjectQuiz(project.getProjectId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(projectQuizDtos)) {
            esModel.setProjectQuiz(projectQuizDtos);
        }

        // image count
        esModel.setDroneImageCount(projectImageMapper.countImages(project.getProjectId(),
            FileSourceTypeEnum.DRONE_IMAGE.getCode(), null, false));
        esModel.setMobileImageCount(projectImageMapper.countImages(project.getProjectId(),
            FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode(), null, false));
        if (Objects.nonNull(project.getClaimType())) {
            esModel.setClaimType(project.getClaimType());
        }

        if (StringUtils.isNotBlank(project.getPlnarURL())) {
            esModel.setPlnarMarkStatus(ProjectPlnarStatusEnum.LINK_RECEIVED.getCode());
        }
        if (Objects.nonNull(project.getHoverJobId())) {
            esModel.setHoverJobId(String.valueOf(project.getHoverJobId()));
            esModel.setHoverMarkStatus(ProjectHoverStatusEnum.ORDERED.getCode());
        }

        // project labels
        List<BoundProjectLabel> projectLabels = projectLabelMapper
            .getProjectLabels(Collections.singletonList(project.getProjectId()));
        if (!CollectionUtils.isEmpty(projectLabels)) {
            List<ProjectLabel> labels = projectLabels.get(0).getProjectLabels();
            if (!CollectionUtils.isEmpty(labels)) {
                List<Long> projectLabelIds = labels.stream().map(ProjectLabel::getLabelId).collect(Collectors.toList());
                esModel.setProjectTags(projectLabelIds);
            }
        }

        BeesPilotBatchItem batchItem = beesPilotBatchItemMapper
            .listByProjectIds(Collections.singletonList(project.getProjectId()))
            .stream().findFirst().orElse(null);
        Optional.ofNullable(batchItem).ifPresent(o -> esModel.setBatchNo(o.getBatchNo()));

        List<String> pilotFeedbacks = getPilotFeedback(project.getProjectId());
        if (!CollectionUtils.isEmpty(pilotFeedbacks)) {
            esModel.setPilotFeedbacks(pilotFeedbacks);
        }

        return esModel;
    }

    private String getInspectedBy(long projectId) {
        String inspectedBy = StringUtils.EMPTY;
        EventHistory eventHistory = eventHistoryMapper.getByStatus(projectId,
            ProjectStatusEnum.PILOT_CHECKED_IN.getCode());
        if (Objects.nonNull(eventHistory)) {
            com.bees360.user.User user = userProvider.findUserById(eventHistory.getUserId() + "");
            if (Objects.nonNull(user)) {
                inspectedBy = user.getName();
            }
        }
        return inspectedBy;
    }

    private List<String> getPilotFeedback(long projectId) {
        Member activePilot = memberMapper.getActiveMemberByRole(projectId, RoleEnum.PILOT.getCode());
        if (activePilot != null) {
            long userId = activePilot.getUserId();
            List<ProjectMessage> messages = projectMessageMapper.listMessage(
                ProjectMessageQuery.builder()
                    .projectId(projectId)
                    .senderId(userId)
                    .type(ProjectMessageTypeEnum.PILOT_FEEDBACK.getCode())
                    .build());
            if (CollectionUtils.isEmpty(messages)) {
                return null;
            }
            return messages.stream().map(ProjectMessage::getContent).collect(Collectors.toList());
        }
        return null;
    }
}
