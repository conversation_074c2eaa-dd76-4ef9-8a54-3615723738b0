package com.bees360.web.grpc.service.impl;

import com.bees360.entity.User;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.entity.label.ProjectLabel;
import com.bees360.internal.ai.grpc.api.ai2web.ProjectTagProto;
import com.bees360.internal.ai.grpc.api.ai2web.ProjectTagServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass.StatusCodeResponse;
import com.bees360.service.ProjectLabelService;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class ProjectTagChangedServiceImpl extends ProjectTagServiceGrpc.ProjectTagServiceImplBase {

    @Autowired
    private ProjectLabelService projectLabelService;

    @Override
    public void updateProjectTags(ProjectTagProto.ProjectTagUpdateRequest request,
        StreamObserver<StatusCodeResponseOuterClass.StatusCodeResponse> responseObserver) {


        long projectId = request.getProjectId();
        List<Long> tags = request.getTagIdList();
        var userId = request.getUserId();

        log.info("sync project tags of {} from ai: {} by userId: {}", projectId, tags, userId);

        boolean needSync = true;
        List<ProjectLabel> oldLabels = projectLabelService.projectLabel(projectId)
            .map(BoundProjectLabel::getProjectLabels).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(oldLabels) && CollectionUtils.isEmpty(tags)){
            needSync = false;
        }
        if (!CollectionUtils.isEmpty(oldLabels)){
            List<Long> oldLabelIds = oldLabels.stream().map(ProjectLabel::getLabelId).collect(Collectors.toList());
            if (Objects.equals(tags, oldLabelIds)){
                needSync = false;
            }
        }
        if (needSync){
            // 兼容 request userId 字段上线，理论上 userId 不可能为 "".
            var opUserId = StringUtils.isBlank(userId) ? String.valueOf(User.AI_ID) : userId;
            projectLabelService.markAfterEraseLabel(projectId, tags, opUserId, SystemTypeEnum.BEES_AI);
        }

        StatusCodeResponse response = StatusCodeResponse.newBuilder().setStatus(0).setCode("OK").setMessage("success").build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();

    }
}
