package com.bees360.web.grpc.service.job;

import com.bees360.entity.dto.SyncProjectDataDto;
import com.bees360.schedule.exception.RetryableException;
import com.bees360.schedule.job.retry.RetryableJob;
import com.bees360.schedule.job.retry.RetryableOperation;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandlers;
import com.bees360.schedule.util.QuartzJobConstant;
import com.bees360.web.grpc.service.impl.ReportGrpcServerImpl;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * return to client
 */
@Slf4j
public class SyncProjectDataJob extends RetryableJob {
    @Setter
    private SyncProjectDataDto projectData;

    @Autowired
    private ReportGrpcServerImpl reportGrpcServerImpl;

    public static JobDetail createJobDetail(SyncProjectDataDto projectData) {
        long projectId = projectData.getProject().getProjectId();
        Class<? extends Job> jobClass = SyncProjectDataJob.class;
        String jobName = "sync-data-%d-%d".formatted(projectId, System.currentTimeMillis());
        JobDataMap jobData = new JobDataMap();
        jobData.put("projectData", projectData);
        String description = "Sync project data. projectId %d".formatted(projectId);

        return JobBuilder.newJob(jobClass)
            .withIdentity(jobName, QuartzJobConstant.WebGroup.SYNC_PROJECT_DATA)
            .usingJobData(jobData)
            .withDescription(description)
            .requestRecovery(true)
            .build();
    }

    @Override
    protected RetryableOperation getRetryableOperation() {
        return (jobContext) -> {
            log.info("execute sync project data job. projectId {}", projectData.getProject().getProjectId());
            long startTime = System.currentTimeMillis();
            try {
                reportGrpcServerImpl.updateData(projectData);
            } catch (Exception e) {
                throw new RetryableException(e);
            }
            log.info("execute sync project data job finished. projectId {} spend {}",
                projectData.getProject().getProjectId(), System.currentTimeMillis() - startTime);
        };
    }

    @Override
    protected RetryExceptionHandler getRetryExceptionHandler() {
        final int RETRY_COUNT_TO_LOG_ERROR = 3;
        final int RETRY_COUNT = 3;
        return RetryExceptionHandlers.newBuilder()
            .retryOnCause(Exception.class)
            .setStartToLogErrorRetryCount(RETRY_COUNT_TO_LOG_ERROR)
            .retryCount(RETRY_COUNT)
            .build();
    }

}
