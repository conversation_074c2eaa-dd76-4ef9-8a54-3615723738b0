package com.bees360.web.grpc.service.impl;

import com.bees360.internal.ai.grpc.api.web2ai.ProjectRoleWorkServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectRoleWorkServiceOuterClass;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass;
import com.bees360.service.MessageService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/11/28
 */
@Slf4j
@Component
public class ProjectRoleWorkServerImpl extends ProjectRoleWorkServiceGrpc.ProjectRoleWorkServiceImplBase {

    @Autowired
    private MessageService messageService;

    @Override
    public void calAiRoleWorkResult(ProjectRoleWorkServiceOuterClass.ProjectRoleWorkResult request,
                                    StreamObserver<StatusCodeResponseOuterClass.StatusCodeResponse> responseObserver) {
        log.info("ProjectRoleWorkServerImpl calAiRoleWorkResult startDate:{}", request.getStartDate());

        try {
            messageService.infoAdminRoleWorkWeeklyCount(request.getStartDate(), request.getEndDate(), request.getClaimList(),
                request.getUnderwritingList());

            StatusCodeResponseOuterClass.StatusCodeResponse response = StatusCodeResponseOuterClass.StatusCodeResponse
                .newBuilder().setStatus(0).setCode("OK").setMessage("success").build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
            log.info("calAiRoleWorkResult end, startDate:{}", request.getStartDate());
        } catch (Exception e) {
            log.error("Failed to calAiRoleWorkResult end, startDate:{} " + request.getStartDate(), e);
            responseObserver.onError(e);
            return;
        }
    }
}
