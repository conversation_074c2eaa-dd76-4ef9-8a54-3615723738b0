package com.bees360.web.grpc.service.impl;

import com.bees360.base.exception.ServiceException;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.common.grpc.MessageBeanUtil;
import com.bees360.entity.BsExportData;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.ReportAnnotationImage;
import com.bees360.entity.ReportSummary;
import com.bees360.entity.User;
import com.bees360.entity.dto.ImageDeletedDto;
import com.bees360.entity.dto.StringIdValueDto;
import com.bees360.entity.dto.SyncProjectDataDto;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.grpc.Timestamp;
import com.bees360.internal.ai.grpc.api.web2ai.StatusCodeResponseOuterClass.StatusCodeResponse;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.mapper.ReportAnnotationImageMapper;
import com.bees360.report.grpc.api.report2web.ExportData;
import com.bees360.report.grpc.api.report2web.ImageDeletedOuterClass.ImageDeleted;
import com.bees360.report.grpc.api.report2web.ProjectReportFileOuterClass;
import com.bees360.report.grpc.api.report2web.ReportImageMessageOuterClass.ReportImageMessage;
import com.bees360.report.grpc.api.report2web.ReportSummaryProto;
import com.bees360.report.grpc.api.report2web.UpdateReportServiceGrpc.UpdateReportServiceImplBase;
import com.bees360.report.grpc.api.report2web.UpdateReportServiceOuterClass.UpdateReportRequest;
import com.bees360.schedule.job.retry.RetryableJobTriggerFactory;
import com.bees360.schedule.service.SchedulerManagerTransactionalDelegate;
import com.bees360.service.BsExportDataService;
import com.bees360.service.EventHistoryService;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectService;
import com.bees360.service.ReportSummaryService;
import com.bees360.service.job.ReportAutoApprovedJob;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.web.event.project.ProjectReportFileUpdatedEvent;
import com.bees360.web.grpc.service.job.SyncProjectDataJob;
import com.google.protobuf.Descriptors;
import com.google.protobuf.GeneratedMessageV3.Builder;
import io.grpc.stub.StreamObserver;
import java.lang.reflect.InvocationTargetException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2019/11/28
 */
@Slf4j
@Component
public class ReportGrpcServerImpl extends UpdateReportServiceImplBase implements ApplicationEventPublisherAware {

    private static final int IMAGE_SEGMENT_LENGTH = 400;

    @Inject
    private ProjectService projectService;

    @Inject
    private EventHistoryService eventHistoryService;

    @Inject
    private ProjectImageMapper projectImageMapper;

    @Inject private ProjectReportFileService projectReportFileService;

    @Inject
    private ReportAnnotationImageMapper reportAnnotationImageMapper;

    @Autowired
    private BsExportDataService bsExportDataService;

    @Autowired
    private ReportSummaryService reportSummaryService;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Autowired
    private SchedulerManagerTransactionalDelegate schedulerManagerTransactionalDelegate;

    public ApplicationEventPublisher publisher;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher publisher) {
        this.publisher = publisher;
    }

    @Override
    public void updateReport(UpdateReportRequest request, StreamObserver<StatusCodeResponse> responseObserver) {
        printParameters(request);

        StatusCodeResponse response;

        // 构建需要同步的数据    start
        Project project = convertEntity(request.getReportProjectMessage().toBuilder(), Project::new);
        long projectId = project.getProjectId();
        List<ProjectReportFile> reportFiles = convertList(request.getProjectReportFilesList(),
            ProjectReportFileOuterClass.ProjectReportFile::toBuilder, ProjectReportFile::new);

        List<ProjectImage> imageMessages = getProjectImageMessages(request);
        List<ImageDeletedDto> imageDeletedList = getImageDeleted(request);
        List<String> asynchronousImageIds = getAsynchronousImageIds(projectId, imageMessages);

        List<ReportAnnotationImage> reportAnnotationImages = reportAnnotationImageList(
            request.getReportAnnotationImagesList());

        ExportData exportData = request.getExportData();

        try {
            BsExportData bsExportData = new BsExportData();
            if(exportData.isInitialized() && StringUtils.isNotBlank(exportData.getRelatedId())
                && StringUtils.isNotBlank(exportData.getRelatedType())) {
                MessageBeanUtil.copy(exportData, bsExportData);
            }

            List<ReportSummaryProto> summaryProtoList = request.getReportSummaryList();
            List<ReportSummary> summaryList = summaryProtoList.stream().map(s -> {
                ReportSummary rs = new ReportSummary();
                try {
                    MessageBeanUtil.copy(s, rs);
                } catch (Exception e) {
                    log.error("Fail to convert ReportSummaryProto to ReportSummary: {}", e.getMessage(), e);
                }
                return rs;
            }).collect(Collectors.toList());

            SyncProjectDataDto projectData =
                    SyncProjectDataDto.builder()
                            .project(project)
                            .reportFiles(reportFiles)
                            .imageMessages(imageMessages)
                            .imageDeletedList(imageDeletedList)
                            .asynchronousImageIds(asynchronousImageIds)
                            .reportAnnotationImages(reportAnnotationImages)
                            .bsExportData(bsExportData)
                            .summaryList(summaryList)
                            .build();

            // 接收完数据，启动quartz执行数据更新
            schedulerUpdateData(projectData);

            response = StatusCodeResponse.newBuilder().setStatus(0).setCode("OK").setMessage("success").build();
        } catch (Exception e) {
            log.error("bees360-web ProjectReportFileServiceImpl submit Report", e);
            response = StatusCodeResponse.newBuilder().setStatus(1).setCode("FAILED").setMessage("failed").build();
        }
        responseObserver.onNext(response);
        responseObserver.onCompleted();
        log.info("update report finished. projectId:" + project.getProjectId());
    }

    private void schedulerUpdateData(SyncProjectDataDto projectData) {
        long projectId = projectData.getProject().getProjectId();
        JobDetail jobDetail = SyncProjectDataJob.createJobDetail(projectData);
        Trigger trigger = RetryableJobTriggerFactory
            .retryForeverTrigger(jobDetail.getKey().getName(), jobDetail.getKey().getGroup(), Duration.ofMinutes(2))
            .build();
        try {
            if (!schedulerManagerTransactionalDelegate.checkExists(
                    jobDetail.getJobClass(), jobDetail.getKey())) {
                schedulerManagerTransactionalDelegate.scheduleJob(jobDetail, trigger);
            }
        } catch (SchedulerException e) {
            log.error("Fail to schedule sync project data Job projectId: {}, {}", projectId, e.getMessage(), e);
        }
    }

    @Transactional(rollbackFor = { Exception.class })
    public void updateData(SyncProjectDataDto projectData) throws Exception {
        Project project = projectData.getProject();
        List<ProjectReportFile> reportFiles = projectData.getReportFiles();
        List<ProjectImage> imageMessages = projectData.getImageMessages();
        List<ImageDeletedDto> imageDeletedList = projectData.getImageDeletedList();
        List<String> asynchronousImageIds = projectData.getAsynchronousImageIds();
        List<ReportAnnotationImage> reportAnnotationImages = projectData.getReportAnnotationImages();
        BsExportData bsExportData = projectData.getBsExportData();
        List<ReportSummary> summaryList = projectData.getSummaryList();
        long projectId = project.getProjectId();
        List<Integer> reportTypes = ListUtil.toList(ProjectReportFile::getReportType, reportFiles);

        long updateBaseDataStart = System.currentTimeMillis();

        // 更新Project中与report相关的数据
        projectService.updateReportRelevantData(project);

        // 更新Image与report相关的数据
        Map<String, String> imageOldIdAndNewIdList =
                insertAndUpdateImages(
                        projectId,
                        imageMessages,
                        reportTypes,
                        asynchronousImageIds,
                        imageDeletedList);

        // 更新report annotation images
        updateReportAnnotationImages(projectId, imageOldIdAndNewIdList, reportAnnotationImages);

        long updateBaseDataEnd = System.currentTimeMillis();
        log.info("Update project base data finished spend:{}", updateBaseDataEnd - updateBaseDataStart);

        // 更新project report files
        updateProjectReportFiles(projectId, reportFiles, summaryList);
        long updateReportFileEnd = System.currentTimeMillis();
        log.info("Update report file finished spend:{}", updateReportFileEnd - updateBaseDataEnd);

        saveExportData(bsExportData);
        saveReportSummary(summaryList);
        long updateSummaryEnd = System.currentTimeMillis();
        log.info("Update export data and summary data finished spend:{}", updateSummaryEnd - updateReportFileEnd);

        eventHistoryService.insertHistoryToProject(projectId, User.AI_ID, ProjectStatusEnum.DATA_TRANSFERRED,
            StringUtils.EMPTY);
    }

    private List<String> getAsynchronousImageIds(long projectId, List<ProjectImage> imageMessages) {
        Set<String> imageMessageIdSet = ListUtil.toSet(ProjectImage::getImageId, imageMessages);
        List<ProjectImage> allImages = projectImageMapper.listAllContainDeleted(projectId);
        return ListUtil.toList(image -> !imageMessageIdSet.contains(image.getImageId()),
            ProjectImage::getImageId, allImages);
    }

    private List<ProjectImage> getProjectImageMessages(UpdateReportRequest request) {
        List<ProjectImage> imageMessages = convertList(request.getReportImageMessagesList(),
            ReportImageMessage::toBuilder, ProjectImage::new);
        imageMessages.forEach(this::imageUrlToKey);
        return imageMessages;
    }

    private List<ImageDeletedDto> getImageDeleted(UpdateReportRequest request) {
        return convertList(
                request.getImageDeletedList(), ImageDeleted::toBuilder, ImageDeletedDto::new);
    }

    /**
     * 将图片的原图、中图以及小图可能存在的url改成key
     * @param image projectImage
     */
    private void imageUrlToKey(ProjectImage image) {
        image.setFileNameLowerResolution(image.getLowerResolutionImageS3Key());
        image.setFileNameMiddleResolution(image.getMiddleResolutionImageS3Key());
        image.setFileName(image.getImageS3Key());
    }

    private void updateProjectReportFiles(
            long projectId, List<ProjectReportFile> reportFiles, List<ReportSummary> summaryList)
            throws ServiceException {

        Project project = projectService.getById(projectId);
        List<ProjectReportFile> reportsNeedApproved = new ArrayList<>();
        for (ProjectReportFile reportFile : reportFiles) {
            ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportFile.getReportType());
            if (Objects.equals(reportType, ReportTypeEnum.INVOICE)) {
                if (bees360FeatureSwitch.isEnableHandleInvoice()) {
                    log.info("Update invoice {}", reportFile);
                    projectService.updateInvoiceFile(
                        projectId, User.AI_ID, reportFile.getReportPdfFileName());
                }
                continue;
            }
            if (StringUtils.isEmpty(reportFile.getReportPdfCompressed())) {
                reportFile.setReportPdfCompressed("");
                reportFile.setSizeCompressed(0);
            }
            var summary =
                    summaryList.stream()
                            .filter(s -> s.getReportType() == reportFile.getReportType())
                            .findAny()
                            .map(ReportSummary::getSummary)
                            .orElse(ReportSummaryService.NULL_SUMMARY);
            projectReportFileService.save(reportFile, summary);
            boolean needApproved = reportType.needApproved() && reportFile.getGenerationStatus() == ReportGenerationStatusEnum.APPROVED.getCode();
            if (needApproved) {
                var submittedStatus = ReportGenerationStatusEnum.SUBMITTED;
                reportFile.setGenerationStatus(submittedStatus);
                reportsNeedApproved.add(reportFile);
            }

            publisher.publishEvent(new ProjectReportFileUpdatedEvent(this, project, reportFile));

        }
        for (ProjectReportFile reportFile : reportsNeedApproved) {
            if (isReportNeedAutoApproved(project)) {
                autoApprovedReport(projectId, reportFile.getReportId());
            }
        }
    }

    private boolean isReportNeedAutoApproved(Project project) {
        ProjectServiceTypeEnum serviceTypeEnum = ProjectServiceTypeEnum.getEnum(project.getServiceType());
        return serviceTypeEnum != null && serviceTypeEnum.getInspectionPurposeType().isAutoApproveReport();
    }

    private void updateReportAnnotationImages(long projectId, Map<String, String> imageOldIdAndNewIdList,
        List<ReportAnnotationImage> reportAnnotationImages) {
        List<Integer> reportTypes = ListUtil.toList(ReportAnnotationImage::getReportType, reportAnnotationImages);
        if (CollectionAssistant.isEmpty(reportAnnotationImages) || CollectionAssistant.isEmpty(reportTypes)) {
            return;
        }
        reportAnnotationImageMapper.deleteByProjectIdAndReportTypes(projectId, reportTypes);
        reportAnnotationImages.forEach(imageAnnotationImage -> {
            if (imageOldIdAndNewIdList.containsKey(imageAnnotationImage.getImageId())) {
                imageAnnotationImage.setImageId(imageOldIdAndNewIdList.get(imageAnnotationImage.getImageId()));
            }
        });
        reportAnnotationImageMapper.insertBatch(reportAnnotationImages);
    }

    private Map<String, String> insertAndUpdateImages(
            long projectId,
            List<ProjectImage> imageMessages,
            List<Integer> reportTypes,
            List<String> asynchronousImageIds,
            List<ImageDeletedDto> imageDeletedList) {
        // AI端删除的image
        List<String> imageDeletedIdList =
                ListUtil.toList(ImageDeletedDto::getImageId, imageDeletedList);
        if (CollectionAssistant.isNotEmpty(imageDeletedIdList)) {
            batchConsumerData(
                    imageDeletedIdList,
                    images -> projectImageMapper.deleteImages(projectId, images),
                    IMAGE_SEGMENT_LENGTH);
        }

        if (!CollectionAssistant.isEmpty(imageMessages)) {
            imageMessages.sort(((o1, o2) -> (int) (o1.getUploadTime() - o2.getUploadTime())));
            batchConsumerData(
                    imageMessages,
                    images -> projectImageMapper.updateReportRelevantData(projectId, images),
                    IMAGE_SEGMENT_LENGTH);
            if (CollectionAssistant.isNotEmpty(reportTypes) && CollectionAssistant.isNotEmpty(asynchronousImageIds)) {
                batchConsumerData(
                        asynchronousImageIds,
                        images ->
                                projectImageMapper.deleteByReportTypesAndImageIds(
                                        projectId, reportTypes, images),
                        IMAGE_SEGMENT_LENGTH);
            }
        }
        Set<String> imageSet = ListUtil.toSet(ProjectImage::getImageId, projectImageMapper.getImageByProjectId(projectId));
        List<ProjectImage> insertImages = ListUtil
            .filter(image -> !imageSet.contains(image.getImageId()), imageMessages);
        List<StringIdValueDto> oldIdAndNewIdList = ListUtil.toList(image -> {
            StringIdValueDto oldIdAndNewId = new StringIdValueDto();
            oldIdAndNewId.setId(image.getImageId());
            return oldIdAndNewId;
        }, insertImages);
        insertImages.forEach(image -> image.setUserId(User.AI_ID));
        if (!CollectionAssistant.isEmpty(insertImages)) {
            batchConsumerData(
                    insertImages, projectImageMapper::insertBaseInfoList, IMAGE_SEGMENT_LENGTH);
        }
        for (int i = 0; i < insertImages.size(); i++) {
            oldIdAndNewIdList.get(i).setValue(insertImages.get(i).getImageId());
        }
        return ListUtil.toMap(StringIdValueDto::getId, StringIdValueDto::getValue, oldIdAndNewIdList);
    }

    private <T> void batchConsumerData(
            List<T> list, Consumer<List<T>> batchOperation, int segmentLength) {
        for (int i = 0; i < list.size(); i += segmentLength) {
            batchOperation.accept(list.subList(i, Math.min(i + segmentLength, list.size())));
        }
    }

    private void saveExportData(BsExportData bsExportData) throws Exception {
        if (Objects.isNull(bsExportData) || StringUtils.isBlank(bsExportData.getRelatedId())
            || StringUtils.isBlank(bsExportData.getRelatedType())) {
            return;
        }
        BsExportData oldExportData = bsExportDataService.getByRelatedIdAndType(bsExportData.getRelatedId(), bsExportData.getRelatedType());
        if(oldExportData != null) {
            bsExportData.setId(oldExportData.getId());
        }
        bsExportDataService.insertOrUpdateData(bsExportData);
    }

    private void saveReportSummary(List<ReportSummary> summaryList) {
        summaryList.forEach(rs -> reportSummaryService.upsert(rs));
    }

    private List<ReportAnnotationImage> reportAnnotationImageList(
        List<com.bees360.report.grpc.api.report2web.ReportAnnotationImageOuterClass.ReportAnnotationImage> list) {
        return ListUtil.toList(annotationImage -> ReportAnnotationImage.builder()
            .projectId(annotationImage.getProjectId())
            .imageId(annotationImage.getImageId())
            .reportType(annotationImage.getReportType())
            .componentId(annotationImage.getComponentId())
            .caption(annotationImage.getCaption())
            .alias(annotationImage.getAlias())
            .page(annotationImage.getPage())
            .createTime(convertEntity(annotationImage.getCreateTime().toBuilder(), Timestamp::new))
            .build(), list);
    }

    private static <R, K, T extends Builder<T>> List<R> convertList(List<K> list, Function<? super K, ? extends T> convertToBuilder,
                                                                    Supplier<R> supplier) {
        return ListUtil.toList(e -> convertEntity(convertToBuilder.apply(e), supplier), list);
    }

    private static <R, T extends Builder<T>> R convertEntity(Builder<T> t, Supplier<R> supplier) {
        R r = supplier.get();
        try {
            MessageBeanUtil.copy(t, r);
        } catch (InvocationTargetException | IllegalAccessException | NoSuchMethodException e) {
            log.error("bees360-ai ReportGrpcServerImpl.buildEntity convert data, data：" + t.toString(), e);
        }
        return r;
    }

    private void autoApprovedReport(long projectId, String reportId) {
        JobDetail jobDetail = ReportAutoApprovedJob.createJobDetail(projectId, reportId);
        JobKey jobKey = jobDetail.getKey();

        TriggerBuilder triggerBuilder = RetryableJobTriggerFactory.retryForeverTriggerStartAt(jobKey.getName(),
            jobKey.getGroup(), Duration.ofMinutes(6), DateUtils.addMilliseconds(new Date(), RandomUtils.nextInt(0, 5000)));
        Trigger trigger = triggerBuilder.forJob(jobKey).build();

        try {
            schedulerManagerTransactionalDelegate.scheduleJob(jobDetail, trigger);
        } catch (SchedulerException e) {
            log.error("Fail to scheduler job for auto approved report({}) of project ({})", reportId, projectId, e);
        }
    }

    /**
     * Print request parameters.
     *
     * @param request The request parameters.
     */
    private void printParameters(UpdateReportRequest request) {
        if (log.isDebugEnabled()) {
            StringBuilder sb = new StringBuilder();
            Map<Descriptors.FieldDescriptor, Object> messageFields = request.getAllFields();
            for (Descriptors.FieldDescriptor fieldDescriptor : messageFields.keySet()) {
                String field = fieldDescriptor.getName();
                Object value = messageFields.get(fieldDescriptor);
                if (fieldDescriptor.getJavaType() != null) {
                    sb.append("field:").append(field).append(",value").append(value);
                }
            }
            log.debug("ReportGrpcServerImpl#approveReport, debug, parameter is :" + sb);
        }
        String logInfo = "ReportGrpcServerImpl#approveReport, info, parameter is : " + "projectId:" + request
            .getReportProjectMessage().getProjectId()
            + ",reportFileSize:" + request.getProjectReportFilesCount()
            + ",reportImageSize:" + request.getReportImageMessagesCount()
            + ",imageDeletedCount:" + request.getImageDeletedCount()
            + ",reportAnnotationImageSize:" + request.getReportAnnotationImagesCount();
        log.info(logInfo);
    }

}
