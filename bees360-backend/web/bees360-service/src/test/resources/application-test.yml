rabbit:
  client:
    host: rabbitmq
    port: 5672
    use-nio: true
    automatic-recovery-enabled: true
    username: mq_username
    password: mq_password

image:
  resorter: INTERIOR_ROOM_NAME_PATH
  resorter-config:
    sorted-by-tag-path-weight:
      - tag-path-weight:
          hallway_or_entry_image: 1
          room_wall_overview_image: 2
          floor_overview_image: 3
          floor_transition_overview_image: 4
          ceiling_overview_image: 5
          ceiling_damage_image: 6
          room_wall_damage_image: 7

logging:
  level:
    com.bees360.service.util.ProjectImageReSorterConfig: debug

http:
  client:
    apache:
      maxConnPerRoute: 16
      maxConnTotal: 64
      evictIdleConnectionsAfter: PT10M
      connectionTimeToLive: PT10M
      evictExpiredConnections: true
      redirectStrategy: always
      request:
        connectTimeout: PT15S
        connectionRequestTimeout: PT30S
        socketTimeout: PT180S
        redirectEnabled: true
        expectContinueEnabled: true
        normalizeUri: true
  aws-lambda:
    enabled: true
    endpoint: http://localhost/aws/
    client-secret: client-secret
    customer-template-key:
      '[Tower Hill Insurance Group]': templateKey

project:
  close-out:
    disabled: false
    property:
      - operation-tag: DENIED
        close-reason: "DENIED"
        Reason-in-close-out-report: "Inspection was denied by the insured."
