{"yearBuilt": 2023, "livingArea": 0, "lotSize": 0, "factors": [{"name": "HAZARD_POWERSRG", "description": "Primary surge protectors observed", "value": false, "image": []}, {"name": "HAZARD_GENERATOR", "description": "Generator(s) observed or disclosed by insured", "value": false, "image": []}, {"name": "ELEC_CLOTH", "description": "Cloth-sheathed wiring observed or disclosed by insured", "value": false, "image": []}, {"name": "FIREPLACE_COAL", "description": "Coal burning stove observed", "value": false, "image": []}, {"name": "FIREPLACE_WOOD", "description": "Wood burning stove observed", "value": false, "image": []}, {"name": "FURNACE_TYPE", "description": "Describe furnace type", "value": "Other", "image": []}, {"name": "HAZARD_SPACEHEAT", "description": "Space heater observed", "value": false, "image": []}, {"name": "CONDENSER_CONDIT", "description": "Describe condition of the condenser unit", "image": [], "value": false}, {"name": "PROT_ALARM", "description": "Burglar and/or fire alarm observed or disclosed by insured", "value": true, "image": []}, {"name": "PROT_SPRINKLER", "description": "Sprinkler system observed", "value": false, "image": []}, {"name": "PROT_EXTINGUISHER", "description": "Fire extinguisher observed or disclosed by insured", "value": false, "image": []}, {"name": "PROT_DETECT", "description": "Smoke alarm observed", "value": false, "image": []}, {"name": "INTCRACK_FLOOR", "description": "Crackng is observed on the interior floors", "value": false, "image": []}, {"name": "INTCRACK_WALL", "description": "Cracking is observed on the interior walls", "value": false, "image": []}, {"name": "INTCRACK_CEILING", "description": "Cracking is observed on the interior ceilings", "value": false, "image": []}, {"name": "INTCRACK_WINDOW", "description": "Cracking or jamming is observed at or around the interior windows", "value": false, "image": []}, {"name": "INTCRACK_DOOR", "description": "Cracking or jamming is observed at or around the interior doors", "value": false, "image": []}, {"name": "HAZARD_EXPWIRING", "description": "Exposed wiring observed in the electrical panel", "value": false, "image": []}, {"name": "HAZARD_LOOSEWIRE", "description": "Loose wiring observed in the electrical panel", "value": false, "image": []}, {"name": "HAZARD_OVERFUSE", "description": "Overfusing observed in the electrical panel", "value": false, "image": []}, {"name": "HAZARD_PANELALT", "description": "Electrical panel has been altered", "value": false, "image": []}, {"name": "HVAC_WINDOW", "description": "Window A/C unit(s) observed", "value": false, "image": []}, {"name": "METER_DAMAGE", "description": "Electrical meter is damaged", "image": [], "value": false}, {"name": "METER_MISSING", "description": "Electrical meter is missing", "image": [], "value": false}, {"name": "METER_WIRES", "description": "Disconnected wires observed in electrical meter", "value": false, "image": []}, {"name": "OCC_CONDEMN", "description": "Home is condemned (confirmed by signage or insured confirmation)", "image": [], "value": false}, {"name": "PIPING_MATER2", "description": "Polybutylene piping material observed", "value": [], "image": []}, {"name": "INPIPE_MATER", "description": "Incoming piping material", "value": "", "image": []}, {"name": "OUTPIPE_MATER", "description": "Outgoing piping material", "value": "", "image": []}, {"name": "SUPLI_MATER", "description": "Supply lines material", "value": "", "image": []}, {"name": "INPIPE_CONDIT", "description": "Describe condition of the incoming piping", "value": "", "image": []}, {"name": "OUTPIPE_CONDIT", "description": "Describe condition of the outgoing piping", "value": "", "image": []}, {"name": "SUPLI_CONDIT", "description": "Describe condition of the supply lines", "value": "", "image": []}, {"name": "SCEXOTIC", "description": "Wild, exotic, or unusual animals on premises (non-farm animals)", "value": false, "image": []}, {"name": "SIDING_SUSPASBESTOS", "description": "Asbestos siding is suspected", "value": false, "image": []}, {"name": "SPEC1_PRESENT", "description": "Diving board or pool slide is observed", "value": false, "image": []}, {"name": "SPEC2_BICYCLE", "description": "Bicycle jump is observed", "value": false, "image": []}, {"name": "SPEC2_SKATEBOARD", "description": "Skateboard ramp is observed", "value": false, "image": []}, {"name": "SPEC2_TRAMPOLINE", "description": "Trampoline is observed", "value": false, "image": []}, {"name": "SPEC7_COMM", "description": "Home is attached to or converted from a commercial risk", "image": [], "value": false}, {"name": "TRAMPOLINE_MINOR", "description": "Trampoline minor is observed", "image": [], "value": false}, {"name": "TRAMPOLINE_POOR", "description": "Trampoline is in poor condition", "value": false, "image": []}, {"name": "TRAMPOLINE_UNFENCE", "description": "Trampoline is in an unfenced yard", "value": false, "image": []}, {"name": "TRAMPOLINE_UNLOCK", "description": "Trampoline is in a fenced yard with an unlocked gate", "value": false, "image": []}, {"name": "UNDERCONSTR_OTHER", "description": "Home is under construction/has ongoing renovations", "value": false, "image": []}, {"name": "ADDLINFO_3ORMORE", "description": "Isolation - is home visible to 3+ neighbors? If no, please provide photograph of the area", "value": false, "image": []}, {"name": "ADDLINFO_ACCESS", "description": "No road access to home/home must be accessed by all-terrain vehicle.", "value": false, "image": []}, {"name": "ADDLINFO_VISIBLE", "description": "Isolation - is home visible from the main road? If no, please provide photograph of the area", "value": false, "image": []}, {"name": "ADJACENT_OTHER", "description": "Off-property adjacent structures - other (please describe)", "value": false, "image": []}, {"name": "ADJACENT_POOR", "description": "Off-property adjacent structures in poor condition", "value": false, "image": []}, {"name": "AMP_LESS100", "description": "Panel amperage is less than 100", "image": [], "value": false}, {"name": "BRICK_COLLAPSE", "description": "Bricks are collapsing, impacting structural integrity", "value": false, "image": []}, {"name": "BRICK_CRACK", "description": "Bricks are cracking, impacting structural integrity", "value": false, "image": []}, {"name": "BRICK_CRUMBLE", "description": "Bricks are crumbling, impacting structural integrity", "value": false, "image": []}, {"name": "BRICK_LEAN", "description": "Bricks are leaning, impacting structural integrity", "value": false, "image": []}, {"name": "BRICK_OTHER", "description": "Other structural brick concern", "value": false, "image": []}, {"name": "BRICK_SEPARATE", "description": "Bricks are separating, impacting structural integrity", "value": false, "image": []}, {"name": "BRICK_TUCK", "description": "Bricks are tuckpointing, impacting structural integrity", "value": false, "image": []}, {"name": "BUS_DAYCARE", "description": "Daycare operations on the insured premises, either confirmed with yard signage or customer confirmation.", "value": false, "image": []}, {"name": "BUS_FARM", "description": "Farming operations on the insured premises, confirmed by observation of produce sales, farm employees, or farm animals on premises.  Do not flag if only produce growth with no employees or animals.", "value": false, "image": []}, {"name": "BUS_MAJOR", "description": "Business operations on the insured premises, confirmed by observation or disclosure of employees regularly on premises or property used for access to adjacent business property", "value": false, "image": []}, {"name": "CHIMNEY_COLLAPSE", "description": "Chimney is collapsing, impacting structural integrity", "value": false, "image": []}, {"name": "CHIMNEY_CRACK", "description": "Chimney is cracking, impacting structural integrity", "value": false, "image": []}, {"name": "CHIMNEY_CRUMBLE", "description": "Chimney is crumbling, impacting structural integrity", "value": false, "image": []}, {"name": "CHIMNEY_LEAN", "description": "Chimney is leaning, impacting structural integrity", "value": false, "image": []}, {"name": "CHIMNEY_MISSING", "description": "Chimney is missing bricks, impacting structural integrity", "value": false, "image": []}, {"name": "CHIMNEY_RUST", "description": "Chimney flue is rusted or damaged", "value": false, "image": []}, {"name": "CHIMNEY_SEPARATE", "description": "Chimney is separated from roof or wall, impacting structural integrity", "value": false, "image": []}, {"name": "CHIMNEY_TUCK", "description": "Chimney is tuckpointing, impacting structural integrity", "value": false, "image": []}, {"name": "CHIMNEY_OTHER", "description": "Other structural chimney concern", "value": false, "image": []}, {"name": "CONSTR_LED", "description": "Risks constructed with unconventional building materials or unusual building features (historical homes, log homes, a-frame homes, dome homes, asbestos siding, underground structures, structures built over water)", "value": false, "image": []}, {"name": "DETGARAGE_COLLAPSE", "description": "Detached garage is collapsing", "image": [], "value": false}, {"name": "DETGARAGE_DOOR", "description": "Detached garage has a door with major condition concerns where damaged or cannot be secured. Not solely cosmetic.", "image": [], "value": false}, {"name": "DETGARAGE_FOUND", "description": "Detached garage has foundation damage impacting the structural integrity.", "image": [], "value": false}, {"name": "DETGARAGE_OTHER", "description": "Other structural detached garage concern", "value": false, "image": []}, {"name": "DETGARAGE_ROOF", "description": "Detached garage roof has signs of lifting, curling, missing shingles or tiles or is in poor condition.", "image": [], "value": false}, {"name": "DETGARAGE_RUSTY", "description": "Detached garage is rusty", "image": [], "value": false}, {"name": "OCC_OTHER", "description": "Per the onsite interview, other occupancy concern", "value": "", "image": []}, {"name": "SPEC3_BITE", "description": "Insured discloses they have a pet with bite history", "image": [], "value": false}, {"name": "MH_PKNAME", "description": "Mobile home park name", "value": "", "image": []}, {"name": "SPEC5_ADDRESS", "description": "Address was corrected by inspector per insured direction", "value": "", "image": []}], "closeoutReasons": []}