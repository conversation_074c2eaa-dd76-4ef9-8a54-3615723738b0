package com.bees360.service.util;

import com.bees360.entity.ProjectImage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

@SpringJUnitConfig(classes = ProjectImageReSorterConfig.class)
@TestPropertySource("classpath:application-test.yml")
@SpringBootTest
@ActiveProfiles("test")
class ProjectImageReSorterTest {

    private long sortValue = Instant.now().toEpochMilli();
    private final AtomicLong imageIdProvider = new AtomicLong(0L);

    private List<ProjectImage> prepareImages() {
        List<ProjectImage> projectImages = new ArrayList<>();
        projectImages.add(generateImage("garage", "exterior/garage%2/20230711154243.jpg"));
        projectImages.add(generateImage(null, "exterior/garage%1/20230711154244.jpg"));
        projectImages.addAll(
            shuffleImage(
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/hallway_or_entry_image/1692855590841_Room_Overview_Android.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/hallway_or_entry_image/1692855590847_Room_Overview_Android.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/room_wall%1/room_wall_overview_image/20230711154241.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/room_wall%2/room_wall_overview_image/20230711154242.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/room_wall%2/room_wall_overview_image/20230711154245.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/room_wall%3/room_wall_overview_image/20230711154242.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/floor_overview_image/1692855681609_Floor_Overview_Android.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/floor_overview_image/1692855684033_Floor_Overview_Android.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/floor_transition_overview_image/1692855675633_Floor_Overview_Android.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/ceiling_overview_image/1692855694136_Ceiling_Overview_Android.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/ceiling_overview_image/1692855696413_Ceiling_Overview_Android.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/ceiling_damage_image/1692855704896_Damaged_Area_Android.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/room_wall%1/room_wall_damage_image/20230711154241.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/room_wall%2/room_wall_damage_image/20230711154246.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%1/room_wall%3/room_wall_damage_image/20230711154242.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%2/room_wall%1/room_wall_overview_image/20230711154250.jpg"),
                generateImage(
                    "bathroom",
                    "interior/bathroom%2/room_wall%2/room_wall_damage_image/20230711154252.jpg")));
        projectImages.add(generateImage(null, "exterior/garage%1/20230711154243.jpg"));
        projectImages.addAll(
            shuffleImage(
                generateImage(
                    "kitchen",
                    "interior/kitchen%1/room_wall%1/room_wall_damage_image/20230711154243.jpg"),
                generateImage(
                    "kitchen",
                    "interior/kitchen%1/room_wall%2/room_wall_damage_image/20230711154243.jpg"),
                generateImage(
                    "kitchen",
                    "interior/kitchen%1/room_wall%3/room_wall_damage_image/20230711154243.jpg"),
                generateImage("kitchen", "interior/kitchen%1/20230711154243.jpg")));
        projectImages.add(generateImage("garage", "exterior/garage%1/20230711154243.jpg"));
        return projectImages;
    }

    @Test
    void roomNamePathSortTest(@Autowired UnaryOperator<List<ProjectImage>> resorter) {
        // 此处imageId设置为期望排序后id，打乱后通过id检查排序是否正确
        imageIdProvider.set(0L);
        var projectImages = prepareImages();
        projectImages = resorter.apply(projectImages);

        for (int i = 0; i < projectImages.size(); i++) {
            Assertions.assertEquals(String.valueOf(i), projectImages.get(i).getImageId());
            if (i > 0) {
                Assertions.assertTrue(
                    projectImages.get(i).getImageSort()
                        > projectImages.get(i - 1).getImageSort());
            }
        }
    }

    @Test
    void roomDefaultSortTest() {
        // filter配置为Default时,不对任何图片进行排序,因此过滤出的image数量为0
        List<ProjectImage> projectImages = prepareImages();
        imageIdProvider.set(0L);
        projectImages.forEach(
            image -> image.setImageId(String.valueOf(imageIdProvider.getAndIncrement())));

        var filterList =
            projectImages.stream()
                .filter(ProjectImageReSortFilterEnum.DEFAULT.getFilter())
                .collect(Collectors.toList());
        Assertions.assertEquals(0, filterList.size());
    }

    private ProjectImage generateImage(String roomName, String imageCategory) {
        return generateImage(
            String.valueOf(imageIdProvider.getAndIncrement()), roomName, imageCategory);
    }

    private ProjectImage generateImage(String imageId, String roomName, String imageCategory) {
        var projectImage = ProjectImage.defaultImage();
        projectImage.setImageId(imageId);
        projectImage.setRoomName(roomName);
        projectImage.setImageCategory(imageCategory);
        projectImage.setImageSort(sortValue++);
        return projectImage;
    }

    private List<ProjectImage> shuffleImage(ProjectImage... images) {
        List<ProjectImage> tempList = Arrays.asList(images);
        Collections.shuffle(tempList);
        return tempList;
    }
}
