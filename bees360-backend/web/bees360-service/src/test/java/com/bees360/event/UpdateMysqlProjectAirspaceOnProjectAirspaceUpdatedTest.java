package com.bees360.event;

import com.bees360.event.registry.ProjectAirspaceUpdatedEvent;
import com.bees360.mapper.ProjectAirspaceMapper;
import lombok.SneakyThrows;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import java.time.Instant;
import java.util.List;

@ExtendWith(MockitoExtension.class)
public class UpdateMysqlProjectAirspaceOnProjectAirspaceUpdatedTest {
    private List<String> airspaceStatuses =
            List.of(
                    "clear",
                    "advisory_required",
                    "laanc_auto_approval",
                    "laanc_not_auto_approval",
                    "restricted",
                    "controlled_airspace",
                    "caution",
                    "unsupported_location");

    @Mock private ProjectAirspaceMapper projectAirspaceMapper;
    private UpdateMysqlProjectAirspaceOnProjectAirspaceUpdated listener;

    @BeforeEach
    public void init() {
        listener = new UpdateMysqlProjectAirspaceOnProjectAirspaceUpdated(projectAirspaceMapper);
    }

    @SneakyThrows
    @Test
    public void testHandle() {
        listener.handle(randomProjectAirspaceUpdatedEvent());
        Mockito.verify(projectAirspaceMapper, Mockito.times(1)).upsertByProjectId(Mockito.any());
    }

    private String randomString() {
        return RandomStringUtils.randomAlphanumeric(12);
    }

    private ProjectAirspaceUpdatedEvent randomProjectAirspaceUpdatedEvent() {
        ProjectAirspaceUpdatedEvent event = new ProjectAirspaceUpdatedEvent();
        event.setProjectId(randomString());
        event.setAirspaceTo(randomAirspace());
        event.setUpdatedAt(Instant.now().toEpochMilli());

        return event;
    }

    private ProjectAirspaceUpdatedEvent.Airspace randomAirspace() {
        ProjectAirspaceUpdatedEvent.Airspace airspace = new ProjectAirspaceUpdatedEvent.Airspace();
        airspace.setStatus(airspaceStatuses.get(RandomUtils.nextInt(0, airspaceStatuses.size())));
        airspace.setHeightCeiling(RandomUtils.nextInt(10, 1000));

        return airspace;
    }
}
