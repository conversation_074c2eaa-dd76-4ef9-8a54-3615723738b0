package com.bees360.firebase;

import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.SerializableFirebaseIBeesMission;
import com.bees360.job.registry.SerializableFirebaseIBeesMissionV2;
import com.bees360.service.firebase.FirebaseMissionService;
import com.bees360.job.FirebaseIBeesChangedExecutorV2;
import com.google.cloud.firestore.Firestore;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.IOException;
import java.time.Duration;

import static com.bees360.firebase.BaseJobUtil.createFirebaseIBeesMission;

@SpringBootTest
public class FirebaseIBeesChangedExecutorV2Test {

    @Configuration
    @Import({
        FirebaseIBeesChangedExecutorV2.class
    })
    static class Config {}

    @MockBean public FirebaseMissionService firebaseMissionService;

    @MockBean public Firestore firestore;

    @Autowired private FirebaseIBeesChangedExecutorV2 iBeesChangedExecutor;

    @Test
    void testConvert() throws IOException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var iBeesMissionV2 = createFirebaseIBeesMission(projectId);
        var job = JobPayloads.encode(String.valueOf(iBeesMissionV2.hashCode()), iBeesMissionV2);
        job = RetryableJob.of(job, 3, Duration.ofMinutes(1), 1.5F);
        iBeesChangedExecutor.execute(job);
        ArgumentCaptor<SerializableFirebaseIBeesMission> argumentCaptor = ArgumentCaptor.forClass(SerializableFirebaseIBeesMission.class);
        Mockito.verify(firebaseMissionService).handleIBeesMission(argumentCaptor.capture(), Mockito.any());
        verify(iBeesMissionV2, argumentCaptor.getValue());
    }

    private void verify(SerializableFirebaseIBeesMissionV2 actual, SerializableFirebaseIBeesMission expected){
        Assertions.assertEquals(expected.getProject().getProjectId(), actual.getProject().getProjectId());
        Assertions.assertEquals(
            expected.getProject().getAddress().getCity(), actual.getProject().getAddress().getCity());
        Assertions.assertEquals(
            expected.getProject().getAddress().getCountry(), actual.getProject().getAddress().getCountry());
        Assertions.assertEquals(
            expected.getProject().getAddress().getFlyZone(), actual.getProject().getAddress().getFlyZone());
        Assertions.assertEquals(
            expected.getProject().getAddress().getGps(), actual.getProject().getAddress().getGps());
        Assertions.assertEquals(
            expected.getProject().getAddress().getState(), actual.getProject().getAddress().getState());
        Assertions.assertEquals(
            expected.getProject().getAddress().getStreetAddress(),
            actual.getProject().getAddress().getStreetAddress());
        Assertions.assertEquals(
            expected.getProject().getAddress().getZipcode(), actual.getProject().getAddress().getZipcode());

        Assertions.assertEquals(expected.getLastUpdateTime(), actual.getLastUpdateTime());
        Assertions.assertEquals(expected.getStatus(), actual.getStatus());
        Assertions.assertEquals(expected.getIsDeleted(), actual.getIsDeleted());
        Assertions.assertEquals(expected.getTasks(), actual.getTasks());
        Assertions.assertEquals(expected.getTaskStatus(), actual.getTaskStatus());

        var taskReason1 = expected.getCheckOutReason().get(0).getTaskReason().get(0);
        var taskReason2 = actual.getCheckOutReason().get(0).getTaskReason().get(0);
        Assertions.assertEquals(taskReason1.getReasonId(), taskReason2.getReasonId());
        Assertions.assertEquals(taskReason1.getTaskId(), taskReason2.getTaskId());
        Assertions.assertTrue(
            expected.getCheckOutReason().get(0).getTime().getSeconds()
                <= actual.getCheckOutReason().get(0).getTime().getSeconds());
        Assertions.assertEquals(taskReason1.getText(), taskReason2.getText());

        var callRecord1 = expected.getCallRecord().get(0);
        var callRecord2 = actual.getCallRecord().get(0);
        Assertions.assertEquals(callRecord1.getType(), callRecord2.getType());
        Assertions.assertEquals(callRecord1.getAction(), callRecord2.getAction());
        Assertions.assertTrue(
            callRecord1.getTime().getSeconds() <= callRecord2.getTime().getSeconds());
        Assertions.assertEquals(callRecord1.getId(), callRecord2.getId());
        Assertions.assertEquals(callRecord1.getInsuredPhone(), callRecord2.getInsuredPhone());
        Assertions.assertEquals(callRecord1.getAnswerLevel(), callRecord2.getAnswerLevel());
        Assertions.assertEquals(callRecord1.getInputAnswer(), callRecord2.getInputAnswer());

        Assertions.assertEquals(
            expected.getFeedback().get(0).getContent(), actual.getFeedback().get(0).getContent());
        Assertions.assertEquals(
            expected.getFeedback().get(0).getTemplate().getTemplateName(),
            actual.getFeedback().get(0).getTemplate().getTemplateName());
        Assertions.assertEquals(
            expected.getFeedback().get(0).getMessage(), actual.getFeedback().get(0).getMessage());
        Assertions.assertTrue(
            expected.getFeedback().get(0).getCreatedTime().getSeconds()
                <= actual.getFeedback().get(0).getCreatedTime().getSeconds());
    }
}
