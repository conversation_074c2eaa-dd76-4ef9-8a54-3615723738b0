package com.bees360.firebase;

import com.bees360.firebase.entity.FirebaseProject;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.SerializableFirebaseBatch;
import com.bees360.job.registry.SerializableFirebaseBatchV2;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.job.FirebaseBatchChangedExecutorV2;

import com.google.cloud.firestore.Firestore;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.Map;

@SpringBootTest
public class FirebaseBatchChangedExecutorV2Test {

    @Configuration
    @Import({
        FirebaseBatchChangedExecutorV2.class
    })
    static class Config {}

    @MockBean public FirebaseService firebaseService;

    @MockBean public Firestore firestore;

    @Autowired private FirebaseBatchChangedExecutorV2 batchChangedExecutor;

    @Test
    void testConvert() throws IOException {
        var batchV2 = createFirebaseBatch();
        var job = JobPayloads.encode(String.valueOf(batchV2.hashCode()), batchV2);
        job = RetryableJob.of(job, 3, Duration.ofMinutes(1), 1.5F);
        batchChangedExecutor.execute(job);
        ArgumentCaptor<SerializableFirebaseBatch> argumentCaptor = ArgumentCaptor.forClass(SerializableFirebaseBatch.class);
        Mockito.verify(firebaseService).syncBatchToWeb(argumentCaptor.capture(), Mockito.any());
        verify(batchV2, argumentCaptor.getValue());
    }

    public static SerializableFirebaseBatchV2 createFirebaseBatch() {
        SerializableFirebaseBatchV2 entity = new SerializableFirebaseBatchV2();
        entity.setStatus("Accepted");
        entity.setBasePay(0.0D);
        FirebaseProject project = new FirebaseProject();
        project.setProjectId("1234");
        entity.setProject(Map.of("test1", project, "test2", project));
        entity.setCreatedAt(Instant.now().toEpochMilli());
        entity.setUpdatedAt(Instant.now().toEpochMilli());
        return entity;
    }

    private void verify(SerializableFirebaseBatchV2 expected, SerializableFirebaseBatch actual){
        Assertions.assertEquals(expected.getStatus(), actual.getStatus());
        // SerializableFirebaseBatch经过encoder和decoder后Project中value字段名会从camel case被改为snake case。因为这里的逻辑只用到keySet，所以暂没有影响。
        Assertions.assertEquals(expected.getProject().keySet(), actual.getProject().keySet());
        Assertions.assertEquals(expected.getBasePay(), actual.getBasePay());
        Assertions.assertEquals(expected.getCreatedAt(), actual.getCreatedAt());
        Assertions.assertEquals(expected.getUpdatedAt(), actual.getUpdatedAt());
    }
}
