package com.bees360.event;

import com.bees360.activity.ActivityManager;
import com.bees360.commons.firebasesupport.service.RemoteConfigService;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.ProjectQuiz;
import com.bees360.entity.firebase.TaskQuizRemoteConfig;
import com.bees360.event.registry.ProjectCreatedEvent;
import com.bees360.job.JobDispatcher;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.ImageCloneJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.mapper.ProjectImageTagMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.ProjectQuizMapper;
import com.bees360.project.Message;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.user.User;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.StringValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Slf4j
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = CloneProjectPostProcessingOnProjectCreatedTest.Config.class)
class CloneProjectPostProcessingOnProjectCreatedTest {

    @Configuration
    static class Config {
        @Bean
        ProjectIIManager projectIIManager() {
            return mock(ProjectIIManager.class);
        }

        @Bean
        ProjectImageMapper projectImageMapper() {
            return mock(ProjectImageMapper.class);
        }

        @Bean
        ProjectStatusManager projectStatusManager() {
            return mock(ProjectStatusManager.class);
        }

        @Bean
        ProjectImageTagMapper projectImageTagMapper() {
            return mock(ProjectImageTagMapper.class);
        }

        @Bean
        ProjectMapper projectMapper() {
            return mock(ProjectMapper.class);
        }

        @Bean
        ProjectQuizMapper projectQuizMapper() {
            return mock(ProjectQuizMapper.class);
        }

        @Bean
        RemoteConfigService remoteConfigService() {
            return mock(RemoteConfigService.class);
        }

        @Bean
        ActivityManager activityManager() {
            return mock(ActivityManager.class);
        }

        @Bean(name = {"jobDispatcher", "jobDispatcher"})
        InMemoryJobScheduler inMemoryJobScheduler() {
            return new InMemoryJobScheduler(MoreExecutors.directExecutor());
        }
    }

    @Autowired
    private ProjectIIManager projectIIManager;

    @Autowired
    private ProjectImageMapper projectImageMapper;

    @Autowired
    private ProjectStatusManager projectStatusManager;

    @Autowired
    private JobDispatcher jobDispatcher;

    @Autowired
    private JobScheduler jobScheduler;

    @Autowired
    private ProjectImageTagMapper projectImageTagMapper;

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private ProjectQuizMapper projectQuizMapper;

    @Autowired
    private RemoteConfigService remoteConfigService;

    @Autowired
    private ActivityManager activityManager;

    private CloneProjectPostProcessingOnProjectCreated listener;

    @BeforeEach
    void setUp() {
        Mockito.reset(projectIIManager);
        Mockito.reset(projectImageMapper);
        Mockito.reset(projectStatusManager);
        Mockito.reset(projectImageTagMapper);
        Mockito.reset(projectMapper);
        Mockito.reset(projectQuizMapper);
        Mockito.reset(remoteConfigService);
        Mockito.reset(activityManager);
        listener = new CloneProjectPostProcessingOnProjectCreated(
            projectIIManager,
            projectImageMapper,
            jobScheduler,
            projectStatusManager,
            projectImageTagMapper,
            projectMapper,
            projectQuizMapper,
            remoteConfigService,
            activityManager
        );
    }

    @Test
    void handle_ShouldReturnEarly_WhenCloneTypeIsNotProcessorTraining() {
        // Given
        var userId = String.valueOf(RandomUtils.nextLong());
        String cloneFrom = String.valueOf(RandomUtils.nextLong());
        String projectId = String.valueOf(RandomUtils.nextLong());

        var event = new ProjectCreatedEvent();
        event.setProjectId(projectId);

        var project = mock(com.bees360.project.ProjectII.class);
        when(projectIIManager.findById(projectId)).thenReturn(project);
        when(project.getCreateBy()).thenReturn(User.from(com.bees360.user.Message.UserMessage.newBuilder().setId(userId).build()));
        when(project.getMetadata()).thenReturn(
            Message.ProjectMessage.Metadata.newBuilder()
                .setCloneFrom(StringValue.of(cloneFrom))
                .build()
        ); // 不是 PROCESSOR_TRAINING

        // When
        assertDoesNotThrow(() -> listener.handle(event));

        // Then
        verify(projectImageMapper, never()).listAll(anyLong());
    }

    @Test
    void handle_ShouldReturnEarly_WhenSourceProjectHasNoImages() throws Exception {
        // Given
        var userId = String.valueOf(RandomUtils.nextLong());
        String cloneFrom = String.valueOf(RandomUtils.nextLong());
        String projectId = String.valueOf(RandomUtils.nextLong());

        var event = new ProjectCreatedEvent();
        event.setProjectId(projectId);

        var project = mock(com.bees360.project.ProjectII.class);
        when(projectIIManager.findById(projectId)).thenReturn(project);
        when(project.getCreateBy()).thenReturn(User.from(com.bees360.user.Message.UserMessage.newBuilder().setId(userId).build()));
        when(project.getMetadata()).thenReturn(
            Message.ProjectMessage.Metadata.newBuilder()
                .setCloneFrom(StringValue.of(cloneFrom))
                .setCloneType(Message.CloneType.PROCESSOR_TRAINING)
                .build()
        );

        var oldProject = mock(com.bees360.entity.Project.class);
        when(projectMapper.getById(Long.parseLong(cloneFrom))).thenReturn(oldProject);

        when(projectImageMapper.listAll(Long.parseLong(cloneFrom))).thenReturn(Collections.emptyList());

        // When
        assertDoesNotThrow(() -> listener.handle(event));

        // Then
        verify(projectImageMapper, times(1)).listAll(Long.parseLong(cloneFrom));
    }

    @Test
    void handle_ShouldCloneImagesAndScheduleJob_WhenSourceProjectHasImages() throws Exception {
        // Given
        var userId = String.valueOf(RandomUtils.nextLong());
        String cloneFrom = String.valueOf(RandomUtils.nextLong());
        String projectId = String.valueOf(RandomUtils.nextLong());

        var event = new ProjectCreatedEvent();
        event.setProjectId(projectId);

        var project = mock(com.bees360.project.ProjectII.class);
        when(projectIIManager.findById(projectId)).thenReturn(project);
        when(project.getCreateBy()).thenReturn(User.from(com.bees360.user.Message.UserMessage.newBuilder().setId(userId).build()));
        when(project.getMetadata()).thenReturn(
            Message.ProjectMessage.Metadata.newBuilder()
                .setCloneFrom(StringValue.of(cloneFrom))
                .setCloneType(Message.CloneType.PROCESSOR_TRAINING)
                .build()
        );

        ProjectImage oldImage = mock(ProjectImage.class);
        ProjectImage clonedImage = mock(ProjectImage.class);
        when(oldImage.clone()).thenReturn(clonedImage);
        when(oldImage.getImageId()).thenReturn("old-image-id");
        when(projectImageMapper.listAll(Long.parseLong(cloneFrom))).thenReturn(List.of(oldImage));

        var oldProject = mock(com.bees360.entity.Project.class);
        when(projectMapper.getById(Long.parseLong(cloneFrom))).thenReturn(oldProject);

        var projectQuiz1 =  randomProjectQuiz(cloneFrom, 1, "NO");
        var projectQuiz2 =  randomProjectQuiz(cloneFrom, 3, "NO");
        var projectQuiz3 =  randomProjectQuiz(cloneFrom, 2, "NO");
        when(projectQuizMapper.listLatestAnswers(eq(Long.parseLong(cloneFrom)), anyInt())).thenReturn(List.of(projectQuiz1, projectQuiz2, projectQuiz3));

        var taskQuizRemoteConfig1 = randomTaskQuizRemoteConfig(1);
        var taskQuizRemoteConfig2 = randomTaskQuizRemoteConfig(2);
        var taskQuizRemoteConfig3 = randomTaskQuizRemoteConfig(3);
        when(remoteConfigService.getRemoteConfig(any(), any())).thenReturn(List.of(taskQuizRemoteConfig1, taskQuizRemoteConfig2, taskQuizRemoteConfig3));

        // init job executor
        var countDownLatch = new CountDownLatch(1);
        var jobExecutor =
            new AbstractJobExecutor<ImageCloneJob>() {
                @Override
                public void handle(ImageCloneJob job) {
                    log.info("Received job: {}", job);
                    assertNotNull(job);
                    assertEquals(userId, job.getOperator());
                    assertEquals(1, job.getImages().size());
                    countDownLatch.countDown();
                    log.info("Finished job: {}", job);
                }
            };
        jobDispatcher.enlist(jobExecutor);

        // When
        listener.handle(event);

        // assert clone project post-processing job
        boolean finished = countDownLatch.await(2, TimeUnit.SECONDS);
        Assertions.assertTrue(finished);

        verify(projectImageMapper, times(1)).listAll(anyLong());
//        verify(projectImageMapper, times(1)).insertBaseInfoList(anyList());
//        verify(projectStatusManager, times(1)).updateStatus(
//            projectId,
//            com.bees360.project.Message.ProjectStatus.IMAGE_UPLOADED,
//            userId
//        );
    }

    private TaskQuizRemoteConfig randomTaskQuizRemoteConfig(long quizId) {
        var taskQuizRemoteConfig = new TaskQuizRemoteConfig();
        taskQuizRemoteConfig.setQuizId(quizId);
        taskQuizRemoteConfig.setType(1);
        taskQuizRemoteConfig.setText("Is this house for rent?");
        taskQuizRemoteConfig.setChoices(List.of("NO", "YES"));
        return taskQuizRemoteConfig;
    }

    private ProjectQuiz randomProjectQuiz(String project, int quizId, String answer) {
        var projectQuiz =  new ProjectQuiz();
        projectQuiz.setProjectId(Long.parseLong(project));
        projectQuiz.setQuizId(quizId);
        projectQuiz.setClaimType(0);
        projectQuiz.setAnswer(answer);
        return projectQuiz;
    }

    @Test
    void handle_ShouldThrowException_WhenCloneFromIsEmpty() {
        // Given
        var userId = String.valueOf(RandomUtils.nextLong());
        String projectId = String.valueOf(RandomUtils.nextLong());

        var event = new ProjectCreatedEvent();
        event.setProjectId(projectId);

        var project = mock(com.bees360.project.ProjectII.class);
        when(projectIIManager.findById(projectId)).thenReturn(project);
        when(project.getCreateBy()).thenReturn(User.from(com.bees360.user.Message.UserMessage.newBuilder().setId(userId).build()));
        when(project.getMetadata()).thenReturn(Message.ProjectMessage.Metadata.newBuilder().setCloneType(Message.CloneType.PROCESSOR_TRAINING).build()); // Empty cloneFrom

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> listener.handle(event));
        assertTrue(exception.getMessage().contains("Clone from project id is blank"));
    }
}
