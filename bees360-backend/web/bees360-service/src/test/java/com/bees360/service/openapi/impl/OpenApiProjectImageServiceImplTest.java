package com.bees360.service.openapi.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.DirectionEnum;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImagePartialViewTypeEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import com.bees360.entity.enums.OrientationEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.openapi.OpenImageVo;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.service.ReportSummaryService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.junit.jupiter.api.Test;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OpenApiProjectImageServiceImplTest {

    @Mock
    private ProjectImageMapper projectImageMapper;

    @Mock
    private ReportSummaryService reportSummaryService;

    @InjectMocks
    private OpenApiProjectImageServiceImpl openApiProjectImageService;

    @Test
    public void listProjectImage() throws ServiceException {

        final long projectId = 1;
        int imageCount = RandomUtils.nextInt(100, 300);
        var allImages = images(projectId, imageCount);
        var imageInSummary = imageInSummary(projectId, allImages);
        var imageIdInSummary = imageIdInSummary(imageInSummary);
        when(projectImageMapper.listAllContainDeleted(eq(projectId))).thenReturn(allImages);
        when(reportSummaryService.listImagesInSummary(eq(projectId))).thenReturn(imageInSummary);

//        allImages.forEach(i -> System.out.println(String.format("id: %s, deleted: %s", i.getImageId(), i.getDeleted())));

        List<OpenImageVo> imagesResult = openApiProjectImageService.listProjectImage(projectId);

        var idImage = allImages.stream().collect(Collectors.toMap(ProjectImage::getImageId, Function.identity()));

        for (var imgVo: imagesResult) {
            var image = idImage.get(imgVo.getId());
//            System.out.println(imgVo + ": deleted?" + image.getDeleted() + ", summary?" + imageIdInSummary.contains(imgVo.getId()));
            assertEquals(FileSourceTypeEnum.getEnum(image.getFileSourceType()).getValue(), imgVo.getSource());
            assertEquals(OrientationEnum.getEnum(image.getOrientation()).getDisplay(), imgVo.getDirection());
            assertTrue(!image.getDeleted() || imageIdInSummary.contains(imgVo.getId()));
            if (image.getParentId() != null && FileSourceTypeEnum.REPORT_IMAGE.getCode() == image.getFileSourceType()) {
                image = idImage.get(image.getParentId());
            }
            assertEquals(ImageTypeEnum.getEnum(image.getImageType()).getDisplay(), imgVo.getType());
        }
        // verify all images in summary returned
        var resultImageIds = imagesResult.stream().map(OpenImageVo::getId).collect(Collectors.toSet());
        for (var entry: imageInSummary.entrySet()) {
            for (var id: entry.getValue()) {
                assertTrue(resultImageIds.contains(id), "Image " + id + " in summary should all be included.");
            }
        }
    }

    private Set<String> imageIdInSummary(Map<ReportTypeEnum, Set<String>> imageInSummary) {
       var ids = new HashSet<String>();
       for (var entry: imageInSummary.entrySet()) {
            ids.addAll(entry.getValue());
       }
       return ids;
    }

    private Map<ReportTypeEnum, Set<String>> imageInSummary(long projectId, List<ProjectImage> allImage) {
        var imageIds = allImage.stream().map(ProjectImage::getImageId).collect(Collectors.toSet());
        Map<ReportTypeEnum, Set<String>> map = new HashMap<>();
        var reports = random(List.of(ReportTypeEnum.values()), true);
        imageIds = randomImages(imageIds, RandomUtils.nextInt(1, imageIds.size() / 2));
        for (var report: reports) {
            int count = RandomUtils.nextInt(1, imageIds.size());
            map.put(report, randomImages(imageIds, count));
        }
        return map;
    }

    private Set<String> randomImages(Set<String> allImage, int count) {
        return Sets.newHashSet(random(Lists.newArrayList(allImage), count));
    }

    private List<ProjectImage> images(long projectId, int count) {
        var ids = imageIds(count);
        List<ProjectImage> images = new ArrayList<>();
        List<ProjectImage> parentImages = new ArrayList<>();

        for (var id: ids) {
            FileSourceTypeEnum fileSourceType = randomFileSourceType();
            var image = image(projectId, id, fileSourceType, parentImages);
            images.add(image);
            if (fileSourceType != FileSourceTypeEnum.REPORT_IMAGE) {
                parentImages.add(image);
            }
        }
        return images;
    }

    private ProjectImage image(long projectId, String imageId, FileSourceTypeEnum fileSourceType, List<ProjectImage> parentImages) {

        var image = new ProjectImage();
        image.setProjectId(projectId);
        image.setImageId(imageId);
        image.setImageType(random(ImageTypeEnum.values()).getCode());
        image.setFileSourceType(fileSourceType.getCode());
        image.setPartialType(random(ImagePartialViewTypeEnum.values()).getCode());
        image.setOrientation(random(OrientationEnum.values()).getCode());
        image.setDirection(random(DirectionEnum.values()).getCode());
        image.setFileName(RandomStringUtils.randomAlphanumeric(17));
        image.setOriginalFileName(RandomStringUtils.randomAlphanumeric(17));
        image.setDeleted(RandomUtils.nextInt(0, 10) == 7);
        if (FileSourceTypeEnum.REPORT_IMAGE == fileSourceType) {
            var parentId = parentImages.isEmpty()? null: parentImages.get(RandomUtils.nextInt(0, parentImages.size())).getParentId();
            image.setParentId(parentId);
        }
        return image;
    }

    private FileSourceTypeEnum randomFileSourceType() {
        return random(new FileSourceTypeEnum[]{FileSourceTypeEnum.DRONE_IMAGE, FileSourceTypeEnum.CELL_PHONE_IMAGE,
            FileSourceTypeEnum.REPORT_IMAGE});
    }

    private <T> T random(T[] array) {
        return array[RandomUtils.nextInt(0, array.length)];
    }

    private <T> Collection<T> random(List<T> list, boolean notEmpty) {
        if (list.isEmpty()) {
            return Lists.newArrayList();
        }
        int min = notEmpty? 1: 0;
        return random(list, RandomUtils.nextInt(min, list.size()));
    }

    private <T> Collection<T> random(List<T> list, int count) {
        var tmpList = new ArrayList<>(list);
        if (count >= list.size()) {
            return tmpList;
        }
        Collections.shuffle(tmpList);
        return tmpList.subList(0, count);
    }

    private Set<String> imageIds(int count) {
        var set = new HashSet<String>();
        while(set.size() < count) {
            set.add(RandomStringUtils.randomAlphanumeric(7));
        }
        return set;
    }
}
