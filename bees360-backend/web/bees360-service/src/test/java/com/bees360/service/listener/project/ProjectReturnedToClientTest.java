package com.bees360.service.listener.project;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.InMemoryEventPublisher;
import com.bees360.job.config.AutoRegisterJobExecutorConfig;
import com.bees360.job.util.InMemoryJobScheduler;
import com.bees360.mapper.MemberMapper;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.Pipeline;
import com.bees360.pipeline.PipelineService;
import com.bees360.service.EventHistoryService;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.ProjectSyncLogService;
import com.bees360.service.beespilot.BeesPilotBatchItemService;
import com.bees360.service.beespilot.BeesPilotStatusService;
import com.bees360.service.grpc.GrpcProjectGenericService;
import com.bees360.service.grpc.impl.GrpcProjectStatusChangeServiceComponent;
import com.bees360.service.job.ProjectReturnToClientJobExecutor;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.web.event.project.ProjectOrderedServiceReportApprovedEvent;
import com.google.common.util.concurrent.MoreExecutors;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.List;
import java.util.stream.Collectors;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(
    classes = {
        ProjectReturnedToClientTest.Config.class,
        Bees360FeatureSwitch.class,
    },
    properties = {
        "bees360.feature-switch.enable-returned-to-client-trigger-by-pipeline-task=true"
    })
@EnableConfigurationProperties
class ProjectReturnedToClientTest {

    @Import({
        ProjectChangeListener.class,
        ProjectReturnToClientJobExecutor.class,
        AutoRegisterJobExecutorConfig.class,
    })
    @Configuration
    static class Config {

        @Bean(name = {"eventPublisher", "eventDispatcher"})
        InMemoryEventPublisher inMemoryEventPublisher() {
            return new InMemoryEventPublisher(MoreExecutors.directExecutor());
        }

        @Bean(name = {"jobDispatcher", "jobDispatcher"})
        InMemoryJobScheduler inMemoryJobScheduler() {
            return new InMemoryJobScheduler(MoreExecutors.directExecutor());
        }
    }

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    @MockBean
    ProjectStatusService projectStatusService;

    @MockBean
    BeesPilotBatchItemService beesPilotBatchItemService;

    @MockBean
    MemberMapper memberMapper;

    @MockBean
    GrpcProjectStatusChangeServiceComponent statusChangeServiceComponent;

    @MockBean
    ProjectService projectService;

    @MockBean
    GrpcProjectGenericService grpcProjectGenericService;

    @MockBean
    ProjectSyncLogService projectSyncLogService;

    @MockBean
    BeesPilotStatusService beesPilotStatusService;
    @MockBean
    PipelineService pipelineService;

    @MockBean
    ProjectLabelService projectLabelService;

    @MockBean
    EventHistoryService eventHistoryService;

    /* closeout 时，正常returned to client
     * 相关类： {@link ProjectReturnToClientJobExecutor}, {@link ProjectChangeListener#returnToClientOnProjectOrderedServiceReportApprovedEvent}
     */
    @Test
    void testReturnToClientOnProjectOrderedServiceReportApprovedEvent() throws ServiceException {
        long projectId = 10013;
        when(projectService.projectServiceIsCompleted(projectId)).thenReturn(true);

        var project = new Project();
        project.setProjectId(projectId);
        var event = new ProjectOrderedServiceReportApprovedEvent(
            this,
            project,
            ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT,
            1000);

        applicationEventPublisher.publishEvent(event);

        verify(projectStatusService).changeOnReturnedToClient(1000, projectId);
    }

    /* closeout 时，正常returned to client
     * 相关类： {@link ProjectReturnToClientJobExecutor}, {@link ProjectChangeListener#returnToClientOnProjectOrderedServiceReportApprovedEvent}
     */
    @Test
    void testReturnToClientOnProjectOrderedServiceReportApprovedEventWithPipelineTask() throws ServiceException {
        var projectId = 10038;
        when(projectService.projectServiceIsCompleted(projectId)).thenReturn(true);

        var pipeline = createPipeline(List.of("returned_to_client"));
        when(pipelineService.findById(projectId + "")).thenReturn(pipeline);

        var project = new Project();
        project.setProjectId(projectId);

        var event = new ProjectOrderedServiceReportApprovedEvent(
            this,
            project,
            ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT,
            1000);

        applicationEventPublisher.publishEvent(event);

        verify(projectStatusService, never()).changeOnReturnedToClient(1000, projectId);
    }

    private Pipeline createPipeline(List<String> taskKeys) {
        var tasks = taskKeys.stream().map(key -> Message.PipelineMessage.Task.newBuilder()
            .setKey(key).build()).collect(Collectors.toList());

        return Pipeline.from(Message.PipelineMessage.newBuilder().addAllTask(tasks).build());
    }
}
