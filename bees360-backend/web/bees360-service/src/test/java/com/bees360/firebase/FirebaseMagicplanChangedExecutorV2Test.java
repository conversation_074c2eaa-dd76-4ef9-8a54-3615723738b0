package com.bees360.firebase;

import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.SerializableFirebaseMagicplan;
import com.bees360.job.registry.SerializableFirebaseMagicplanV2;
import com.bees360.service.firebase.FirebaseMagicplanService;
import com.bees360.job.FirebaseMagicplanChangedExecutorV2;
import com.google.cloud.firestore.Firestore;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.IOException;
import java.time.Duration;

import static com.bees360.firebase.BaseJobUtil.createFirebaseMagicplan;

@SpringBootTest
public class FirebaseMagicplanChangedExecutorV2Test {

    @Configuration
    @Import({
        FirebaseMagicplanChangedExecutorV2.class
    })
    static class Config {}

    @MockBean public FirebaseMagicplanService firebaseMagicplanService;

    @MockBean public Firestore firestore;

    @Autowired private FirebaseMagicplanChangedExecutorV2 executor;

    @Test
    void testConvert() throws IOException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var magicplanV2 = createFirebaseMagicplan(projectId);
        var job = JobPayloads.encode(String.valueOf(magicplanV2.hashCode()), magicplanV2);
        job = RetryableJob.of(job, 3, Duration.ofMinutes(1), 1.5F);
        executor.execute(job);
        ArgumentCaptor<SerializableFirebaseMagicplan> argumentCaptor = ArgumentCaptor.forClass(SerializableFirebaseMagicplan.class);
        Mockito.verify(firebaseMagicplanService).handleMagicplan(argumentCaptor.capture());
        verify(magicplanV2, argumentCaptor.getValue());
    }

    private void verify(SerializableFirebaseMagicplanV2 expected, SerializableFirebaseMagicplan actual){
        Assertions.assertEquals(expected.getMissionId(), actual.getMissionId());
        Assertions.assertEquals(expected.getProjectId(), actual.getProjectId());
        Assertions.assertEquals(expected.getPdf(), actual.getPdf());
        Assertions.assertEquals(expected.getUpdateTime(), actual.getUpdateTime());
    }
}
