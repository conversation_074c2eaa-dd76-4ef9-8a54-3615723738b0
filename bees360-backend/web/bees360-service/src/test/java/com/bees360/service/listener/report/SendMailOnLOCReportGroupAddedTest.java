package com.bees360.service.listener.report;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.report.Report;
import com.bees360.report.ReportProvider;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.Optional;
import java.util.function.Function;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SendMailOnLOCReportGroupAddedTest {

    @Mock private ProjectReportFileService projectReportFileService;

    @Mock private Optional<Function<Long, Iterable<UserTinyVo>>> projectMemberMailRecipientProvider;

    @Mock private ProjectService projectService;

    @Mock private ReportProvider reportProvider;

    @InjectMocks private SendMailOnLOCReportGroupAdded listener;

    private final String reportId = "123";
    private final String projectId = "789";
    private ReportGroupAdded validEvent;
    private Report mockReport;
    private Project mockProject;

    @BeforeEach
    void setUp() {
        // 只初始化对象，不设置任何存根
        validEvent = new ReportGroupAdded();
        validEvent.setId("testReportGroupId");
        validEvent.setReportId(reportId);
        validEvent.setGroupType(DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE);
        validEvent.setGroupKey(projectId);
        validEvent.setCreatedBy("123");

        mockReport = mock(Report.class);
        mockProject = mock(Project.class);
    }

    private void setupCommonStubsForLOC() {
        // 设置公共存根
        when(projectMemberMailRecipientProvider.isPresent()).thenReturn(false);
        when(mockProject.getCreatedBy()).thenReturn(123L);
        lenient().when(mockProject.getProjectId()).thenReturn(Long.parseLong(projectId));
        when(mockReport.getType()).thenReturn(com.bees360.report.ReportTypeEnum.ICR.getKey());
        when(reportProvider.get(reportId)).thenReturn(mockReport);
        when(projectService.getById(Long.parseLong(projectId))).thenReturn(mockProject);
    }

    @Test
    void handleWhenInvalidGroupTypeShouldSkip() throws IOException {
        ReportGroupAdded invalidEvent = new ReportGroupAdded();
        invalidEvent.setId("testReportGroupId");
        invalidEvent.setReportId(reportId);
        invalidEvent.setGroupType("INVALID_TYPE");
        invalidEvent.setGroupKey(projectId);
        invalidEvent.setCreatedBy("123");

        listener.handle(invalidEvent);

        verifyNoInteractions(projectReportFileService);
    }

    @Test
    void handleWhenWrongReportTypeShouldSkip() throws IOException {
        when(mockReport.getType()).thenReturn(com.bees360.report.ReportTypeEnum.FUR.getKey());
        when(reportProvider.get(reportId)).thenReturn(mockReport);

        listener.handle(validEvent);

        verifyNoInteractions(projectReportFileService);
    }

    @Test
    void handleWhenConditionsMetShouldSendEmail() throws IOException, ServiceException {
        setupCommonStubsForLOC();

        listener.handle(validEvent);

        verify(projectReportFileService)
                .infoUserReportApprove(
                        Long.parseLong(projectId),
                        123L,
                        ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT,
                        false);
    }

    @Test
    void handleWhenEmailServiceThrowsExceptionShouldNotPropagate()
            throws IOException, ServiceException {
        setupCommonStubsForLOC();

        doThrow(new ServiceException("Email error"))
                .when(projectReportFileService)
                .infoUserReportApprove(anyLong(), anyLong(), any(), anyBoolean());

        listener.handle(validEvent);

        verify(projectReportFileService)
                .infoUserReportApprove(
                        Long.parseLong(projectId),
                        123L,
                        ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT,
                        false);
    }
}
