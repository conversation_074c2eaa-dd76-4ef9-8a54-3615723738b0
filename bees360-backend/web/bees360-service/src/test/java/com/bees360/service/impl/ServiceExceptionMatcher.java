package com.bees360.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.hamcrest.Description;
import org.hamcrest.TypeSafeMatcher;

import com.bees360.base.exception.ServiceException;

public class ServiceExceptionMatcher extends TypeSafeMatcher<ServiceException> {

	private final String code;

	public ServiceExceptionMatcher(String code) {
		this.code = code;
	}

	@Override
	public void describeTo(Description description) {
		description.appendText("code equals to \"" + code + "\"");
	}

	@Override
	protected boolean matchesSafely(ServiceException item) {
		return StringUtils.equals(item.getMsgCode(), code);
	}

	@Override
    public void describeMismatchSafely(ServiceException item, Description description) {
        description.appendText("was ").appendValue(item.getMsgCode());
    }
}
