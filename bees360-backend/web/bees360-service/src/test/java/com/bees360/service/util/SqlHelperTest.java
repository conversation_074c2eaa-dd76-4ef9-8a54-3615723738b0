package com.bees360.service.util;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import java.util.Random;

public class SqlHelperTest {

    @Test
    public void testPhoneFuzzyRegex() {
        var phone = RandomStringUtils.randomAlphanumeric(1) + RandomStringUtils.randomNumeric(1, 10);
        var value = SqlHelper.createPhoneFuzzyRegex(phone);

        var numberLen = phone.chars().filter(c -> c >= 48 && c <= 57).count();
        var expectedCharCount = (numberLen - 1)/3 + 2;
        Assertions.assertEquals(expectedCharCount, value.chars().filter(c -> c == '%').count());
    }
}
