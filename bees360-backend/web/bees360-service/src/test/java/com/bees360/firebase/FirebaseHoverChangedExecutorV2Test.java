package com.bees360.firebase;

import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.SerializableFirebaseHover;
import com.bees360.job.registry.SerializableFirebaseHoverV2;
import com.bees360.service.firebase.FirebaseHoverService;
import com.bees360.job.FirebaseHoverChangedExecutorV2;
import com.google.cloud.firestore.Firestore;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.IOException;
import java.time.Duration;

import static com.bees360.firebase.BaseJobUtil.createFirebaseHover;

@SpringBootTest
public class FirebaseHoverChangedExecutorV2Test {

    @Configuration
    @Import({
        FirebaseHoverChangedExecutorV2.class
    })
    static class Config {}

    @MockBean public FirebaseHoverService firebaseHoverService;

    @MockBean public Firestore firestore;

    @Autowired private FirebaseHoverChangedExecutorV2 hoverChangedExecutor;

    @Test
    void testConvert() throws IOException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var hoverV2 = createFirebaseHover(projectId);
        var job = JobPayloads.encode(String.valueOf(hoverV2.hashCode()), hoverV2);
        job = RetryableJob.of(job, 3, Duration.ofMinutes(1), 1.5F);
        hoverChangedExecutor.execute(job);
        ArgumentCaptor<SerializableFirebaseHover> argumentCaptor = ArgumentCaptor.forClass(SerializableFirebaseHover.class);
        Mockito.verify(firebaseHoverService).handleHoverJob(argumentCaptor.capture(), Mockito.any());
        verify(hoverV2, argumentCaptor.getValue());
    }

    private void verify(SerializableFirebaseHoverV2 expected, SerializableFirebaseHover actual){
        Assertions.assertEquals(expected.getProjectId(), actual.getProjectId());
        Assertions.assertEquals(expected.getState(), actual.getState());
        Assertions.assertEquals(expected.getUpdateTime(), actual.getUpdateTime());
        Assertions.assertEquals(expected.getBees360HoverState(), actual.getBees360HoverState());
        Assertions.assertEquals(expected.getDeliverableId(), actual.getDeliverableId());
    }
}
