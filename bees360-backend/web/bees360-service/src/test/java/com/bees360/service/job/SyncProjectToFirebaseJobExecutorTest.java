package com.bees360.service.job;

import com.bees360.entity.Project;
import com.bees360.job.registry.SyncProjectToFirebaseJob;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.Message;
import com.bees360.service.ProjectService;
import com.bees360.service.firebase.FirebaseProjectService;
import com.bees360.service.firebase.FirebaseService;
import com.google.api.core.ApiFutures;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@ExtendWith(MockitoExtension.class)
public class SyncProjectToFirebaseJobExecutorTest {

    private final FirebaseService firebaseService = Mockito.mock(FirebaseService.class);
    private final Firestore firestore = Mockito.mock(Firestore.class);
    private final ProjectService projectService = Mockito.mock(ProjectService.class);
    private final FirebaseProjectService firebaseProjectService =
            Mockito.mock(FirebaseProjectService.class);
    private final ExternalIntegrationManager externalIntegrationManager =
            Mockito.mock(ExternalIntegrationManager.class);
    private DocumentReference documentReference = Mockito.mock(DocumentReference.class);
    private SyncProjectToFirebaseJobExecutor executor;
    private Long projectId;
    private Project project;
    private String bundleId;

    @BeforeEach
    void setUp() {
        projectId = RandomUtils.secure().randomLong();
        bundleId = RandomStringUtils.secure().nextNumeric(6);
        project = new Project(projectId);
        String documentPath = "project/" + projectId;
        ExternalIntegration integration =
                ExternalIntegration.from(
                        Message.IntegrationMessage.newBuilder()
                                .setProjectId(String.valueOf(projectId))
                                .setIntegrationType("BATCH")
                                .setReferenceNumber("bundle-" + bundleId + "-xxxxxxxxxx")
                                .build());

        Mockito.doReturn(documentReference).when(firestore).document(documentPath);
        Mockito.doReturn(ApiFutures.immediateFuture(null))
                .when(documentReference)
                .set(Mockito.any(), Mockito.any());
        Mockito.doReturn(documentPath).when(firebaseService).getProjectDocumentPath(projectId);
        Mockito.doReturn(Optional.of(project)).when(projectService).findById(projectId);
        Mockito.doReturn(new HashMap<>())
                .when(firebaseProjectService)
                .getAllFieldsInProject(project);
        Mockito.doReturn(List.of(integration))
                .when(externalIntegrationManager)
                .findAllByProjectId(String.valueOf(projectId));

        executor =
                new SyncProjectToFirebaseJobExecutor(
                        firebaseService,
                        firestore,
                        projectService,
                        firebaseProjectService,
                        externalIntegrationManager);
    }

    @Test
    void testInitFirebaseProject() throws IOException {
        ArgumentCaptor<Map<String, Object>> argumentCaptor = ArgumentCaptor.forClass(Map.class);
        SyncProjectToFirebaseJob job = new SyncProjectToFirebaseJob();
        job.setProjectId(projectId);
        job.setUpdated(false);
        executor.handle(job);
        Mockito.verify(externalIntegrationManager, Mockito.times(1))
                .findAllByProjectId(String.valueOf(projectId));
        Mockito.verify(documentReference).set(argumentCaptor.capture(), Mockito.any());
        String actualBundleId = (String) argumentCaptor.getValue().get("bundle_id");
        Assertions.assertNotNull(actualBundleId);
        Assertions.assertEquals(bundleId, actualBundleId);
    }

    @Test
    void testUpdateFirebaseProject() throws IOException {
        ArgumentCaptor<Map<String, Object>> argumentCaptor = ArgumentCaptor.forClass(Map.class);

        SyncProjectToFirebaseJob job = new SyncProjectToFirebaseJob();
        job.setProjectId(projectId);
        job.setUpdated(true);
        executor.handle(job);
        Mockito.verify(externalIntegrationManager, Mockito.never())
                .findAllByProjectId(String.valueOf(projectId));
        Mockito.verify(documentReference).set(argumentCaptor.capture(), Mockito.any());
        String actualBundleId = (String) argumentCaptor.getValue().get("bundle_id");
        Assertions.assertNull(actualBundleId);
    }
}
