package com.bees360.service.properties;

import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.OptionalInt;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class ReportGlobalPropertiesTest {

    @Test
    public void getReportCompressedSizeLimit() {
        ReportGlobalProperties reportGlobalProperties = new ReportGlobalProperties();
        List<ReportGlobalProperties.ReportItemProperty> reportItemProperties = new ArrayList<>();

        ReportGlobalProperties.ReportItemProperty item = new ReportGlobalProperties.ReportItemProperty();
        item.setReportType(ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT.getCode());
        item.setCompressedSizeLimit(1);
        reportItemProperties.add(item);

        ReportGlobalProperties.ReportItemProperty item2 = new ReportGlobalProperties.ReportItemProperty();
        item2.setReportType(ReportTypeEnum.PROPERTY_IMAGE_REPORT.getCode());
        item2.setCompressedSizeLimit(2);
        reportItemProperties.add(item2);

        ReportGlobalProperties.ReportItemProperty item3 = new ReportGlobalProperties.ReportItemProperty();
        item3.setReportType(ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT.getCode());
        item3.setServiceType(ProjectServiceTypeEnum.PREMIUM_FOUR_POINT_UNDERWRITING.getCode());
        item3.setCompressedSizeLimit(3);
        reportItemProperties.add(item3);

        reportGlobalProperties.setReportItemProperties(reportItemProperties);

        OptionalInt sizeLimit = reportGlobalProperties.getReportCompressedSizeLimit(
            ReportTypeEnum.PROPERTY_IMAGE_REPORT.getCode());
        assertEquals(item2.getCompressedSizeLimit(), sizeLimit.getAsInt());

        sizeLimit = reportGlobalProperties.getReportCompressedSizeLimit(
            ReportTypeEnum.PROPERTY_IMAGE_REPORT.getCode(),
            ProjectServiceTypeEnum.PREMIUM_FOUR_POINT_UNDERWRITING.getCode());
        assertEquals(item2.getCompressedSizeLimit(), sizeLimit.getAsInt());

        sizeLimit = reportGlobalProperties.getReportCompressedSizeLimit(
            ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT.getCode());
        assertTrue(sizeLimit.isEmpty());

        sizeLimit = reportGlobalProperties.getReportCompressedSizeLimit(
            ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT.getCode(),
            ProjectServiceTypeEnum.PREMIUM_FOUR_POINT_UNDERWRITING.getCode());
        assertEquals(item3.getCompressedSizeLimit(), sizeLimit.getAsInt());

    }
}
