package com.bees360.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.bees360.entity.UserPayment;

public class TransactionServiceTest {

	public static void printUserPayment() {
		System.out.println(JSON.toJSONString(new UserPayment(), new ValueFilter() {

			@Override
			public Object process(Object object, String name, Object value) {
				if(value == null) {
					return "";
				}
				return value;
			}

		}));
	}

	public static void main(String[] args) {
		printUserPayment();
	}
}
