package com.bees360.firebase;

import com.bees360.job.RetryableJob;
import com.bees360.job.registry.FirebaseIBeesMissionCompletedV2;
import com.bees360.job.registry.JobPayloads;
import com.bees360.service.firebase.FirebaseMissionService;
import com.bees360.job.FirebaseIBeesMissionCompletedExecutorV2;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.IOException;
import java.time.Duration;

@SpringBootTest
public class FirebaseIBeesMissionCompletedExecutorV2Test {

    @Configuration
    @Import({
        FirebaseIBeesMissionCompletedExecutorV2.class
    })
    static class Config {}

    @MockBean public FirebaseMissionService firebaseMissionService;

    @Autowired private FirebaseIBeesMissionCompletedExecutorV2 executor;

    @Test
    void testConvert() throws IOException {
        FirebaseIBeesMissionCompletedV2 missionCompletedV2 =
            new FirebaseIBeesMissionCompletedV2();
        missionCompletedV2.setMissionPath("test path");
        missionCompletedV2.setProjectId("1234");
        var job = JobPayloads.encode(String.valueOf(missionCompletedV2.hashCode()), missionCompletedV2);
        job = RetryableJob.of(job, 3, Duration.ofMinutes(1), 1.5F);
        executor.execute(job);
        ArgumentCaptor<String> argumentCaptor1th = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Long> argumentCaptor2th = ArgumentCaptor.forClass(Long.class);
        Mockito.verify(firebaseMissionService).handleIBeesMissionCompleted(argumentCaptor1th.capture(), argumentCaptor2th.capture());
        Assertions.assertEquals(missionCompletedV2.getMissionPath(), argumentCaptor1th.getValue());
        Assertions.assertEquals(Long.parseLong(missionCompletedV2.getProjectId()), argumentCaptor2th.getValue());
    }
}
