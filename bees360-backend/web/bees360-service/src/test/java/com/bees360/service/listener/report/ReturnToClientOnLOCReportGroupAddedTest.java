package com.bees360.service.listener.report;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.job.JobScheduler;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.report.Report;
import com.bees360.report.ReportProvider;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

@ExtendWith(MockitoExtension.class)
public class ReturnToClientOnLOCReportGroupAddedTest {

    @Mock
    private JobScheduler jobScheduler;

    @Mock
    private ReportProvider reportProvider;

    @InjectMocks
    private ReturnToClientOnLOCReportGroupAdded listener;

    private final String reportId = "123";
    private final String projectId = "789";
    private ReportGroupAdded validEvent;
    private Report mockReport;

    @BeforeEach
    void setUp() {
        validEvent = new ReportGroupAdded();
        validEvent.setId("testReportGroupId");
        validEvent.setReportId(reportId);
        validEvent.setGroupType(DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE);
        validEvent.setGroupKey(projectId);
        validEvent.setCreatedBy("123");
        mockReport = mock(Report.class);
    }

    @Test
    void handleWhenInvalidGroupTypeShouldSkip() throws IOException {
        var invalidEvent = new ReportGroupAdded();
        invalidEvent.setId("testReportGroupId");
        invalidEvent.setReportId(reportId);
        invalidEvent.setGroupType("INVALID_TYPE");
        invalidEvent.setGroupKey(projectId);
        invalidEvent.setCreatedBy("123");

        listener.handle(invalidEvent);

        verifyNoInteractions(reportProvider, jobScheduler);
    }

    @Test
    void handleWhenWrongReportTypeShouldSkip() throws IOException {
        when(reportProvider.get(reportId)).thenReturn(mockReport);
        when(mockReport.getType()).thenReturn(com.bees360.report.ReportTypeEnum.FUR.getKey());

        listener.handle(validEvent);

        verify(reportProvider).get(reportId);
        verifyNoInteractions(jobScheduler);
    }

    @Test
    void handleWhenCorrectReportTypeShouldScheduleJob() throws IOException {
        when(reportProvider.get(reportId)).thenReturn(mockReport);
        when(mockReport.getType()).thenReturn(com.bees360.report.ReportTypeEnum.ICR.getKey());

        listener.handle(validEvent);

        Mockito.verify(jobScheduler, Mockito.times(1)).schedule(any());
    }
}
