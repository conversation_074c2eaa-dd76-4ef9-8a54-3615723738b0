package com.bees360.firebase;

import static com.bees360.firebase.BaseJobUtil.createFirebaseMission;

import com.bees360.atomic.LockProvider;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.SerializableFirebaseMission;
import com.bees360.job.registry.SerializableFirebaseMissionV2;
import com.bees360.service.firebase.FirebaseMissionService;
import com.bees360.job.FirebaseMissionChangedExecutorV2;
import com.google.cloud.firestore.Firestore;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.IOException;
import java.time.Duration;

@SpringBootTest
public class FirebaseMissionChangedExecutorV2Test {

    @Configuration
    @Import({
        FirebaseMissionChangedExecutorV2.class
    })
    static class Config {}

    @MockBean public FirebaseMissionService firebaseMissionService;

    @MockBean public Firestore firestore;

    @MockBean public LockProvider missionLockProvider;

    @Autowired private FirebaseMissionChangedExecutorV2 executor;

    @Test
    void testConvert() throws IOException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        String pilotId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var missionV2 = createFirebaseMission(projectId, pilotId);
        var job = JobPayloads.encode(String.valueOf(missionV2.hashCode()), missionV2);
        job = RetryableJob.of(job, 3, Duration.ofMinutes(1), 1.5F);
        executor.execute(job);
        ArgumentCaptor<SerializableFirebaseMission> argumentCaptor = ArgumentCaptor.forClass(SerializableFirebaseMission.class);
        Mockito.verify(firebaseMissionService).handlePilotMission(argumentCaptor.capture(), Mockito.any());
        verify(missionV2, argumentCaptor.getValue());
    }

    private void verify(SerializableFirebaseMissionV2 expected, SerializableFirebaseMission actual){
        Assertions.assertEquals(expected.getMissionType(), actual.getMissionType());
        Assertions.assertEquals(expected.getLastUpdateTime(), actual.getLastUpdateTime());
        Assertions.assertEquals(expected.getStatus(), actual.getStatus());
        Assertions.assertEquals(expected.getPilotId(), actual.getPilotId());
        Assertions.assertEquals(expected.getProject().getProjectId(), actual.getProject().getProjectId());
        Assertions.assertEquals(expected.getTasks(), actual.getTasks());
        Assertions.assertEquals(expected.getTaskStatus(), actual.getTaskStatus());
    }
}
