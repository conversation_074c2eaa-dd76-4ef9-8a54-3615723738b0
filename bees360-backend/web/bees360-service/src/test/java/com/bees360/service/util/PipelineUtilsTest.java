package com.bees360.service.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.bees360.pipeline.Message.PipelineMessage;
import com.bees360.pipeline.Message.PipelineMessage.Task;
import com.bees360.pipeline.Pipeline;
import org.junit.jupiter.api.Test;

class PipelineUtilsTest {

    @Test
    void testFindTask() {
        assertNull(PipelineUtils.findTask(null, "KEY"));
        assertNull(PipelineUtils.findTask(newPipeline(), "KEY"));
        assertNull(PipelineUtils.findTask(newPipeline("KEY_1"), "KEY"));
        assertEquals("KEY_1", PipelineUtils.findTask(newPipeline("KEY_1"), "KEY_1").getKey());
    }

    private Pipeline newPipeline(String... taskKeys) {
        var builder = PipelineMessage.newBuilder();
        for (var key: taskKeys) {
            builder.addTask(Task.newBuilder().setKey(key));
        }
        return Pipeline.from(builder.build());
    }
}
