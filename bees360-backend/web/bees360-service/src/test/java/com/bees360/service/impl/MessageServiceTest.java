package com.bees360.service.impl;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver;

import com.bees360.entity.Project;
import com.bees360.entity.User;
import com.bees360.entity.enums.ReportTypeEnum;

public class MessageServiceTest {

    @Test
	public void testThemeleaf() {
		Project project = new Project();

		User creator = new User();
		creator.setEmail("<EMAIL>");

		List<String> reports = Arrays.asList(ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT.getDisplay(),
				ReportTypeEnum.BID_REPORT.getDisplay());
		Map<String, Object> model = new HashMap<String, Object>();
		model.put("projectId", project.getProjectId());
		model.put("address", project.getFullAddress());
		model.put("account", creator.getEmail() == null? creator.getPhone(): creator.getEmail());
		model.put("name", creator.getName());
		model.put("needPilot", project.isNeedPilot());
		model.put("reports", reports);

		ClassLoaderTemplateResolver templateResolver = new ClassLoaderTemplateResolver();
		templateResolver.setPrefix("message/thymeleaf/");
		templateResolver.setTemplateMode("HTML");
		templateResolver.setCharacterEncoding("UTF-8");

		TemplateEngine templateEngine = new TemplateEngine();
		templateEngine.setTemplateResolver(templateResolver);

		Context ctx = new Context(Locale.getDefault(), model);
		String content = templateEngine.process("orderTaskInitial" + ".html", ctx);
		System.out.println(content);
	}
}
