package com.bees360.service.util;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.function.Function;

import static com.bees360.entity.enums.ProjectLabelEnum.DENIED;
import static org.junit.jupiter.api.Assertions.assertEquals;

@SpringBootTest(
        classes = {
            ProjectCloseConfig.class,
        },
        properties = "spring.config.location = classpath:application-test.yml")
@EnableConfigurationProperties
class ProjectCloseConfigTest {

    @Autowired
    @Qualifier("closeReasonToOperationTagIdConverter")
    Function<String, Long> closeReasonToOperationTagIdConverter;

    @Test
    void testContactViewInWebShouldOk() {
        var labelId = closeReasonToOperationTagIdConverter.apply("DENIED");
        assertEquals(labelId, DENIED.getLabelId());
    }
}
