package com.bees360.event;

import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.event.registry.InteriorImageUploadedEvent;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.UserService;
import java.io.IOException;
import java.time.Instant;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class RemoveTagOnProjectInteriorImageUploadedTest {
    private InteriorImageUploadedEvent uploadedEvent;
    private long userId;
    @Mock private ProjectLabelService projectLabelService;
    @Mock private UserService userService;
    private RemoveTagOnProjectInteriorImageUploaded listener;

    @BeforeEach
    public void init() {
        listener = new RemoveTagOnProjectInteriorImageUploaded(projectLabelService, userService);
        uploadedEvent = new InteriorImageUploadedEvent();
        uploadedEvent.setProjectId(String.valueOf(randomLong()));
        uploadedEvent.setUpdatedBy(randomString());
        uploadedEvent.setOperationTime(Instant.now().toEpochMilli());
        userId = randomLong();
        Mockito.when(userService.toWebUserId(Mockito.anyString())).thenReturn(userId);
    }

    @Test
    public void testRemoveTagOnProjectInteriorImageUploaded() throws IOException {
        listener.handle(uploadedEvent);
        Mockito.verify(projectLabelService, Mockito.times(1))
            .removeLabel(
                userId,
                Long.valueOf(uploadedEvent.getProjectId()),
                ProjectLabelEnum.IBEES_NOT_COMPLETED);
    }

    private String randomString() {
        return RandomStringUtils.randomAlphanumeric(12);
    }

    private long randomLong() {
        return RandomUtils.nextLong();
    }
}
