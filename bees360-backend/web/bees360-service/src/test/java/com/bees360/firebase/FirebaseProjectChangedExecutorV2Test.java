package com.bees360.firebase;

import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.SerializableFirebaseProject;
import com.bees360.job.registry.SerializableFirebaseProjectV2;
import com.bees360.service.firebase.FirebaseProjectService;

import com.bees360.job.FirebaseProjectChangedExecutorV2;
import com.google.cloud.firestore.Firestore;
import com.google.common.util.concurrent.ListeningExecutorService;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.IOException;
import java.time.Duration;

import static com.bees360.firebase.BaseJobUtil.createFirebaseProjectEntity;

@SpringBootTest
public class FirebaseProjectChangedExecutorV2Test {

    @Configuration
    @Import({
        FirebaseProjectChangedExecutorV2.class
    })
    static class Config {}

    @MockBean public FirebaseProjectService firebaseProjectService;

    @MockBean public Firestore firestore;

    @MockBean public ListeningExecutorService executor;

    @Autowired private FirebaseProjectChangedExecutorV2 projectChangedExecutor;

    @Test
    void testConvert() throws IOException {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var projectV2 = createFirebaseProjectEntity(projectId);
        var job = JobPayloads.encode(String.valueOf(projectV2.hashCode()), projectV2);
        job = RetryableJob.of(job, 3, Duration.ofMinutes(1), 1.5F);
        projectChangedExecutor.execute(job);
        ArgumentCaptor<SerializableFirebaseProject> argumentCaptor = ArgumentCaptor.forClass(SerializableFirebaseProject.class);
        Mockito.verify(firebaseProjectService).handleFirebaseProject(argumentCaptor.capture(), Mockito.any());
        verify(projectV2, argumentCaptor.getValue());
    }

    private void verify(SerializableFirebaseProjectV2 expected, SerializableFirebaseProject actual){
        Assertions.assertEquals(expected.getProject_id(), actual.getProject_id());
        Assertions.assertEquals(expected.getCreator_id(), actual.getCreator_id());
        Assertions.assertEquals(expected.getCreator_name(), actual.getCreator_name());
        Assertions.assertEquals(expected.getPilotId(), actual.getPilotId());
        Assertions.assertEquals(expected.getCreated_time(), actual.getCreated_time());
        Assertions.assertEquals(expected.getUpdate_time(), actual.getUpdate_time());
        Assertions.assertEquals(expected.getNote(), actual.getNote());
    }
}
