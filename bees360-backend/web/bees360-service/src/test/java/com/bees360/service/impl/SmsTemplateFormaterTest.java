package com.bees360.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.bees360.util.msgutil.SmsTemplateFormat;

/**
 * 对应 sms.properties 的模板内容的测试
 *
 * <AUTHOR>
 * @date 2019/06/03 18:16
 *
 */
public class SmsTemplateFormaterTest {

	private SmsTemplateFormat smsTemplateFormat;

	@BeforeEach
	public void before() {
		smsTemplateFormat = new SmsTemplateFormat("message/sms.properties");
	}

	@Test
	public void testSecurityCode() {
		String contentExpected = "[Bees360] Your security code is 151212. This code is valid for 2 minutes.";
		testSmsTemplate(contentExpected, "securityCode", new Object[] {"151212"});
	}

	private void testSmsTemplate(String contentExpected, String templateName, Object[] params) {
		String content = smsTemplateFormat.getContent(templateName, params);
		assertEquals(content, contentExpected);
	}

	private void testSmsTemplate(String contentExpected, String templateName) {
		String content = smsTemplateFormat.getContent(templateName);
		assertEquals(content, contentExpected);
	}

	@Test
	public void testSendToNonSystemUser() {
		String contentExpected = "[Bees360] Your project has been created. "
				+ "Please login your account at www.bees360.com using this phone number and password (*********) and check it out. "
				+ "Please remember to reset you password after login.";
		testSmsTemplate(contentExpected, "sendToNonSystemUser", new Object[] {"*********"});
	}

	@Test
	public void testSendToSystemUser() {
		String contentExpected = "[Bees360] Your project has been created. "
				+ "Please login your account at www.bees360.com to check it out.";
		testSmsTemplate(contentExpected, "sendToSystemUser");
	}

	@Test
	public void testErrorToEngineer() {
		String contentExpected = "[Bees360] EXIGENCY: From server 18.17.218.25. System Error. Exception message is System Unexpected Error.";
		String templateName = "errorToEngineer";
		Object[] params = new Object[] {"18.17.218.25", "System Error", "System Unexpected Error"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testAccountBinded() {
		String contentExpected = "[Bees360] Per your request, we have linked this phone number with your Bees360 account.";
		String templateName = "accountBinded";

		testSmsTemplate(contentExpected, templateName);
	}

	@Test
	public void testAccountChanged() {
		String contentExpected = "[Bees360] The phone number associated with your account has been changed. "
				+ "The old <NAME_EMAIL>. The new <NAME_EMAIL>.";
		String templateName = "accountChanged";
		Object[] params = new Object[] {"<EMAIL>", "<EMAIL>"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testImageUploadedSendToCreator() {
		String contentExpected = "[Bees360] There are 25 images was uploaded to the project 100252 at 13211 U.S. 75, Dewey, OK 74029 in Bees360 platform.";
		String templateName = "imageUploadedSendToCreator";
		Object[] params = new Object[] {25, 100252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testImageUploadedSendToStaffs() {
		String contentExpected = "[Bees360] There are 25 images was uploaded to the project 100252 at 13211 U.S. 75, Dewey, OK 74029 on Bees360 platform.";
		String templateName = "imageUploadedSendToStaffs";
		Object[] params = new Object[] {25, 100252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testImageUploadedStatusIsAutoSet() {
		String contentExpected = "[Bees360] The status of 3 projects 100252,100253,100254 are automatically changed to Image Uploaded.";
		String templateName = "imageUploadedStatusIsAutoSet";
		Object[] params = new Object[] {3, "100252,100253,100254"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testReportApproved() {
		String contentExpected = "[Bees360] Premium Damage Assessment Report for project 1000252 at 13211 U.S. 75, Dewey, OK 74029 is approved. "
				+ "You can get access to it under Report tab on Bees360 platform. Thank you for your business!";
		String templateName = "reportApproved";
		Object[] params = new Object[] {"Premium Damage Assessment Report", 1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testRoleApplicationResult() {
		String contentExpected = "[Bees360] Your application for the new role(s) Pilot,Reviewer is approved.";
		String templateName = "roleApplicationResult";
		Object[] params = new Object[] {StringUtils.join(new String[] {"Pilot", "Reviewer"}, ",") , "approved"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testPilotRoleApplicationApproved() {
		String contentExpected = "[Bees360] Your application for pilot role is approved.";
		String templateName = "pilotRoleApplicationApproved";
		Object[] params = new Object[] {StringUtils.join(new String[] {"Pilot", "Reviewer"}, ",") , "approved"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testInviteAsVisitor() {
		String contentExpected = "[Bees360] Bob invite you to project 1000252 at 13211 U.S. 75, Dewey, OK 74029.";
		String templateName = "inviteAsVisitor";
		Object[] params = new Object[] {"Bob", 1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testReportSubmitted() {
		String contentExpected = "[Bees360] Premium Damage Assessment Report for the project 1000252 at 13211 U.S. 75, Dewey, OK 74029 has been submitted.";
		String templateName = "reportSubmitted";
		Object[] params = new Object[] {"Premium Damage Assessment Report", 1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testReportDisapproved() {
		String contentExpected = "[Bees360] Premium Damage Assessment Report for project 1000252 at 13211 U.S. 75, Dewey, OK 74029 is disapproved.";
		String templateName = "reportDisapproved";
		Object[] params = new Object[] {"Premium Damage Assessment Report", 1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testAdminRealtimeReport() {
		String contentExpected = "[Bees360] The Premium Damage Assessment Report for project 1000252 at 13211 U.S. 75, Dewey, OK 74029 has been generated. "
				+ "Please assign Processor and Reviewer as soon as possible.";
		String templateName = "adminRealtimeReport";
		Object[] params = new Object[] {"Premium Damage Assessment Report", 1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testProcessorRealtimeReport() {
		String contentExpected = "[Bees360] The Premium Damage Assessment Report for project 1000252 has been generated. "
				+ "Please review and submit this report as soon as possible.";
		String templateName = "processorRealtimeReport";
		Object[] params = new Object[] {"Premium Damage Assessment Report", 1000252};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testMemberArranged() {
		String contentExpected = "[Bees360] NEW JOB! You are assigned to be the Premium Damage Assessment Report "
				+ "of the project 1000252 at 13211 U.S. 75, Dewey, OK 74029.";
		String templateName = "memberArranged";
		Object[] params = new Object[] {"Premium Damage Assessment Report", 1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testMemberArrangementCancel() {
		String contentExpected = "[Bees360] Your Bees360 assignment for project 1000252 at 13211 U.S. 75, Dewey, OK 74029 has been removed. Thank you.";
		String templateName = "memberArrangementCancel";
		Object[] params = new Object[] {1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testInspectionTimeChangedIsTimeFrom() {
		String contentExpected = "[Bees360] The inspection time for your assignment for project 1000252 at 13211 U.S. 75, Dewey, OK 74029 has been changed.";
		String templateName = "inspectionTimeChangedIsTimeFrom";
		Object[] params = new Object[] {1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testInspectionTimeChangedIsTimeToAndTimeFrom() {
		String contentExpected = "[Bees360] The inspection time for your assignment for project 1000252 "
				+ "at 13211 U.S. 75, Dewey, OK 74029 has been changed from 5:34 pm on 5/3/2019 to 6:34 pm on 5/3/2019.";
		String templateName = "inspectionTimeChangedIsTimeToAndTimeFrom";
		Object[] params = new Object[] {1000252, "13211 U.S. 75, Dewey, OK 74029", "5:34 pm on 5/3/2019", "6:34 pm on 5/3/2019"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testInspectionTimeChangedIsTimeTo() {
		String contentExpected = "[Bees360] The inspection time for your assignment for project 1000252 "
				+ "at 13211 U.S. 75, Dewey, OK 74029 has been scheduled at 6:34 pm on 5/3/2019.";
		String templateName = "inspectionTimeChangedIsTimeTo";
		Object[] params = new Object[] {1000252, "13211 U.S. 75, Dewey, OK 74029", "6:34 pm on 5/3/2019"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testProjectDeleted() {
		String contentExpected = "[Bees360] The project 1000252 at 13211 U.S. 75, Dewey, OK 74029 has been deleted by Bob.";
		String templateName = "projectDeleted";
		Object[] params = new Object[] {1000252, "13211 U.S. 75, Dewey, OK 74029", "Bob"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testSendToAdminToAsignPilot() {
		String contentExpected = "[Bees360] Bob requests a Pilot for project 1000252 at 13211 U.S. 75, Dewey, OK 74029."
				+ " Please assign the Pilot for the project as soon as possible.";
		String templateName = "sendToAdminToAsignPilot";
		Object[] params = new Object[] {"Bob", 1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testRequestCancel() {
		String contentExpected = "[Bees360] The request of assigning a Pilot for Project 1000252 at 13211 U.S. 75, Dewey, OK 74029 has been canceled by Bob.";
		String templateName = "requestCancel";
		Object[] params = new Object[] {1000252, "13211 U.S. 75, Dewey, OK 74029", "Bob"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testorderTaskInitialReports() {
		String contentExpected = "[Bees360] Bob (<EMAIL>) requests Premium Measurement Report,Premium Damage Assessment Report "
				+ "for project 1000252 at 13211 U.S. 75, Dewey, OK 74029.";
		String templateName = "orderTaskInitial-reports";
		Object[] params = new Object[] {"Bob", "<EMAIL>", String.join(",",
				Arrays.asList("Premium Measurement Report", "Premium Damage Assessment Report")), 1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testOrderTaskInitialPilot() {
		String contentExpected = "[Bees360] Bob (<EMAIL>) requests a Pilot for project 1000252 at 13211 U.S. 75, Dewey, OK 74029.";
		String templateName = "orderTaskInitial-pilot";
		Object[] params = new Object[] {"Bob", "<EMAIL>",
				1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testOrderTaskInitialReportsPilot() {
		String contentExpected = "[Bees360] Bob (<EMAIL>) requests Premium Measurement Report,Premium Damage Assessment Report "
				+ "and a Pilot for project 1000252 at 13211 U.S. 75, Dewey, OK 74029.";
		String templateName = "orderTaskInitial-reports-pilot";
		Object[] params = new Object[] {"Bob", "<EMAIL>", String.join(",",
				Arrays.asList("Premium Measurement Report", "Premium Damage Assessment Report")), 1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testOrderTasksChanged() {
		String contentExpected = "[Bees360] Bob (<EMAIL>) changes the order of the project 1000252 at 13211 U.S. 75, Dewey, OK 74029.";
		String templateName = "orderTasksChanged";
		Object[] params = new Object[] {"Bob", "<EMAIL>", 1000252, "13211 U.S. 75, Dewey, OK 74029"};

		testSmsTemplate(contentExpected, templateName, params);
	}

	@Test
	public void testUserRoleLost() {
		String contentExpected = "[Bees360] Your Pilot,Reviewer role is deactivated.";
		String templateName = "userRoleLost";
		Object[] params = new Object[] {String.join(",", Arrays.asList("Pilot", "Reviewer"))};

		testSmsTemplate(contentExpected, templateName, params);
	}
}
