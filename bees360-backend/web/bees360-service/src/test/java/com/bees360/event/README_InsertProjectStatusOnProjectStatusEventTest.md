# InsertProjectStatusOnProjectStatusEvent 测试文档

## 概述

`InsertProjectStatusOnProjectStatusEventTest` 是为 `InsertProjectStatusOnProjectStatusEvent` 类编写的完整单元测试套件。该测试类采用了嵌套测试结构，包含单元测试和集成测试两个部分。

## 测试结构

### 1. UnitTests (单元测试)
使用 `@ExtendWith(MockitoExtension.class)` 进行纯单元测试，所有依赖都通过 Mock 对象模拟。

#### 测试场景包括：

**正常流程测试：**
- `handle_ShouldInsertProjectStatusAndPublishEvent_WhenValidEvent()` - 验证正常情况下的插入和事件发布
- `handle_ShouldHandleValidProjectIdParsing()` - 验证大数字项目ID的解析
- `handle_ShouldHandleZeroTimestamp()` - 验证零时间戳的处理
- `handle_ShouldHandleNegativeStatus()` - 验证负数状态的处理

**异常处理测试：**
- `handle_ShouldThrowException_WhenUserNotExists()` - 验证用户不存在时抛出异常
- `handle_ShouldThrowException_WhenInvalidProjectId()` - 验证无效项目ID时抛出异常
- `handle_ShouldHandleMapperException()` - 验证数据库操作异常的处理
- `handle_ShouldHandlePublishEventException()` - 验证事件发布异常的处理

**边界条件测试：**
- `handle_ShouldHandleEmptyProjectId()` - 验证空项目ID的处理
- `handle_ShouldHandleNullUpdatedBy()` - 验证空用户ID的处理

**业务逻辑测试：**
- `handle_ShouldNotInsert_WhenIsRollback()` - 验证回滚状态的处理（当前为占位测试）

### 2. IntegrationTests (集成测试)
使用 `@ExtendWith(SpringExtension.class)` 和 `@ContextConfiguration` 进行集成测试，验证在Spring上下文中的行为。

#### 测试场景包括：
- `handle_ShouldWorkInSpringContext()` - 验证在Spring容器中的正常工作

## 测试覆盖的功能点

### 核心功能验证：
1. **项目状态插入** - 验证 `ProjectStatus` 对象的正确创建和插入
2. **用户ID转换** - 验证 AI 用户ID 到 Web 用户ID 的转换
3. **事件发布** - 验证状态变更事件的正确发布
4. **异常处理** - 验证各种异常情况的正确处理

### 数据验证：
- 项目ID的正确解析和设置
- 用户ID的正确转换和设置
- 状态码的正确设置
- 创建时间的正确转换（Instant 到 epoch milliseconds）

### 依赖交互验证：
- `UserService.toWebUserId()` 的调用
- `ProjectStatusMapper.insert()` 的调用
- `ProjectStatusService.publishProjectStatusChangeEvent()` 的调用

## 使用的测试技术

### Mock 框架：
- **Mockito** - 用于创建 Mock 对象和验证交互
- **ArgumentCaptor** - 用于捕获和验证传递给 Mock 方法的参数

### 断言框架：
- **JUnit 5 Assertions** - 用于各种断言验证
- **assertThrows** - 用于异常测试
- **assertDoesNotThrow** - 用于正常流程测试

### Spring 测试：
- **@ContextConfiguration** - 用于配置测试上下文
- **@Configuration** 和 **@Bean** - 用于定义测试配置

## 运行测试

### 运行所有测试：
```bash
mvn test -Dtest=InsertProjectStatusOnProjectStatusEventTest
```

### 运行特定测试组：
```bash
# 只运行单元测试
mvn test -Dtest=InsertProjectStatusOnProjectStatusEventTest$UnitTests

# 只运行集成测试
mvn test -Dtest=InsertProjectStatusOnProjectStatusEventTest$IntegrationTests
```

## 注意事项

1. **回滚逻辑测试** - 当前 `isRollback` 在源代码中硬编码为 `false`，相关测试需要在实现回滚逻辑后进行调整

2. **事件对象模拟** - `ProjectStatusHistoryInserted` 事件对象通过 Mock 创建，实际使用时需要确保事件对象的字段和方法与实际实现一致

3. **依赖版本** - 测试依赖于 JUnit 5、Mockito 和 Spring Test 框架，确保项目中包含相应的依赖

## 扩展建议

1. **性能测试** - 可以添加性能测试来验证大量事件处理的性能
2. **并发测试** - 可以添加并发测试来验证多线程环境下的行为
3. **参数化测试** - 可以使用 `@ParameterizedTest` 来测试不同的状态码和场景
4. **测试数据构建器** - 可以创建测试数据构建器来简化测试数据的创建
