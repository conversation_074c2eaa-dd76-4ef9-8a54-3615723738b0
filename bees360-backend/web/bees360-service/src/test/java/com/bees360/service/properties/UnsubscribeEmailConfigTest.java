package com.bees360.service.properties;

import com.bees360.entity.Project;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.service.ProjectService;
import com.bees360.service.UnsubscribeEmailService;
import org.junit.jupiter.api.Test;
import org.mockito.stubbing.Answer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = UnsubscribeEmailConfigTest.Config.class)
@TestPropertySource(properties = "spring.config.location = classpath:application-UnsubscribeEmailConfigTest.yaml")
class UnsubscribeEmailConfigTest {

    @Import({
        Bees360CompanyConfig.class,
        UnsubscribeEmailConfig.class,
    })
    @Configuration
    static class Config {

        @Bean
        ProjectService projectService() {
            var projectService = mock(ProjectService.class);
            Answer<Project> answer = args -> {
                Long projectId = args.getArgument(0);
                Project project = new Project();
                project.setServiceType(ServiceTypeEnum.FULL_ADJUSTMENT.getCode());
                project.setProjectId(projectId);
                project.setInsuranceCompany(1005L);
                return project;
            };
            when(projectService.findById(anyLong())).thenAnswer(answer);
            when(projectService.getById(anyLong())).thenAnswer(answer);
            return projectService;
        }
    }

    @Autowired
    UnsubscribeEmailService unsubscribeEmailService;

    @Test
    void testUnsubscribeEmailByCustomerAndEmailConfig() {
        assertTrue(unsubscribeEmailService.isUnsubscribedByProject("report_approved", 10012));
        assertFalse(unsubscribeEmailService.isUnsubscribed("report_client_received", 10012));
        assertTrue(unsubscribeEmailService.isUnsubscribedByProject("report_generated", 10012));
    }
}
