package com.bees360.service.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.bees360.contract.Contract;
import com.bees360.contract.ContractManager;
import com.bees360.customer.Customer;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.function.BinaryOperator;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.util.ResourceUtils;

@SpringJUnitConfig(classes = AWSLambdaSummaryConverterTest.Config.class)
@TestPropertySource("classpath:application-test.yml")
@SpringBootTest
@ActiveProfiles({"test", "override"})
@EnableConfigurationProperties
class AWSLambdaSummaryConverterTest {

    @Import({
        AWSLambdaSummaryConverterConfig.class,
    })
    @Configuration
    static class Config {
        @Bean
        ProjectIIRepository projectIIRepository() {
            return mock(ProjectIIRepository.class);
        }

        @Bean
        ContractManager contractManager() {
            return mock(ContractManager.class);
        }

        @Bean("lambdaReportSummaryConverter")
        @ConditionalOnProperty(prefix = "http.aws-lambda", value = "enabled", havingValue = "true")
        BinaryOperator<String> testLambdaReportSummaryConverter() throws IOException {
            var processedReportSummary = ResourceUtils.getFile("classpath:processedSummary.json");
            var processedSummary = FileUtils.readFileToString(processedReportSummary, StandardCharsets.UTF_8);
            return (summary, projectId) -> processedSummary;
        }
    }

    @Autowired ProjectIIRepository projectIIRepository;

    @Autowired ContractManager contractManager;

    @Autowired BinaryOperator<String> lambdaReportSummaryConverter;

    @Test
    void lambdaReportSummaryConvertTest() throws IOException {
        var reportSummary = ResourceUtils.getFile("classpath:summary.json");
        var originalSummary = FileUtils.readFileToString(reportSummary, StandardCharsets.UTF_8);
        var processedReportSummary = ResourceUtils.getFile("classpath:processedSummary.json");
        var processedSummary = FileUtils.readFileToString(processedReportSummary, StandardCharsets.UTF_8);

        var projectId = RandomStringUtils.randomNumeric(4);
        var contract = mockContract("Tower Hill Insurance Group", null);
        var project = mockProjectII(contract);
        when(projectIIRepository.get(projectId)).thenReturn(project);
        when(contractManager.findById(contract.getId())).thenReturn(contract);

        // 调用converter
        var transformedReportSummary =
            lambdaReportSummaryConverter.apply(originalSummary, projectId);
        assertEquals(processedSummary, transformedReportSummary);
    }

    private Customer mockCustomer(String companyKey) {
        var customer = mock(Customer.class);
        doReturn(companyKey).when(customer).getCompanyKey();
        return customer;
    }

    private Contract mockContract(String insuredBy, String processedBy) {
        var contract = mock(Contract.class);
        var id = RandomStringUtils.randomNumeric(3);
        doReturn(id).when(contract).getId();
        doReturn(mockCustomer(insuredBy)).when(contract).getInsuredBy();
        doReturn(mockCustomer(processedBy)).when(contract).getProcessedBy();
        return contract;
    }

    private ProjectII mockProjectII(Contract contract) {
        var projectII = mock(ProjectII.class);
        doReturn(contract).when(projectII).getContract();
        return projectII;
    }
}
