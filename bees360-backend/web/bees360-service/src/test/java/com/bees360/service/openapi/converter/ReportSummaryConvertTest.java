package com.bees360.service.openapi.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.springframework.test.util.AssertionErrors.assertNotNull;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;

import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.google.gson.Gson;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;

/**
 * <AUTHOR> Yang
 */
public class ReportSummaryConvertTest {

    @Test
    public void toSummaryVo() {
        // String summary = "{\"livingArea\":\"1,872 sq.ft.\",\"lotSize\":\"0.16 acres\"}";
        String summary =
            "{\"livingArea\":\"872 sq.ft.\",\"lotSize\":\"0.16 acres\",\"exterior\":{\"siding\":{\"BrickVeneer\":0,\"Wood\":4,\"Hardiplank\":0,\"Stucco\":0,\"ConcreteBlock\":0,\"WoodShake\":0,\"Aluminum\":0,\"Vinyl\":90,\"Matal\":0,\"Log\":0,\"Asbestos\":0,\"StoneVeneer\":10},\"hasPorch\":false,\"hasShutters\":false,\"comments\":{\"0\":\"Front Elevation: Damage in the driveway and/or sidewalk was noted. \",\"undefined\":\"Left Elevation: Damage was noted to the siding. \"}},\"roof\":{\"material\":{\"CompositeShingles\":100,\"BuildupRoofNoGravel\":0,\"ClayConcreteTiles\":0,\"Slate\":0,\"SinglePlyMembrane\":0,\"LightMetalPanels\":0,\"StandingSeamMetalRoof\":0,\"WoodenShingles\":0,\"ModifiedBitumen\":0,\"Matal\":0},\"geometry\":{\"HipValley\":0,\"Flat\":0,\"Gable\":100,\"Hip\":0,\"Dormer\":0,\"Mansard\":0,\"Saltbox\":0,\"Combination\":0},\"estAge\":\"15 ~ 20 years\",\"coveringMaterial\":{\"0\":\"Asphalt\"},\"estLife\":\"10 ~ 15 years\",\"overallCondition\":\"Fair\",\"hasSolarPanel\":false,\"comments\":{\"0\":\"Loose, damaged, or missing shingles/tiles were noted. \",\"undefined\":\"Rear Slope: Loose, damaged, or missing shingles/tiles were noted. \"}},\"risk\":{\"vacant\":false,\"isolatedDwelling\":false,\"seasonalDwelling\":false,\"gatedCommunity\":false,\"businessOperation\":\"None observed\",\"neighborhood\":\"Suburban\",\"areaEconomy\":\"Stable\",\"opinion\":\"good\",\"overallCondition\":\"good\"},\"bldg\":{\"dwellingType\":\"Single Family Detached\",\"hvac\":\"Window unit\",\"foundation\":\"Concrete Slab\",\"construction\":\"Frame\",\"numStories\":1,\"windProtections\":{\"0\":\"None observed\"},\"hurricaneStraps\":\"Unable to determine\",\"overallCondition\":\"Good\",\"garage\":\"None\"},\"yearBuilt\":1962,\"addlStructures\":[],\"recommendations\":[{\"image\":[{\"id\":1134914}],\"text\":\"A swimming pool was noted at the Rear of the dwelling. It is recommended that self-closing door to access swimming pool should be installed.\"},{\"text\":\"A storage shed was noted in Good condition. It is recommended to contact a professional contractor to repair the structure as soon as possible.\",\"image\":[{\"id\":1138708},{\"id\":1134922}]},{\"text\":\"A yacht and wharf was noted.\",\"image\":[]},{\"text\":\"Chimney through roof on the UnknownFrontRightRearLeft slope was noted. It is recommended to conduct interior chimney sweep.\",\"image\":[{\"id\":1134916},{\"id\":1138724}]}]}";
        ReportSummaryVo summaryVo = ReportSummaryConvert.toSummaryVo(summary);
        assertEquals(872.0, summaryVo.getLivingArea(), 0);
        assertEquals(0.16, summaryVo.getLotSize(), 0);
        List<String> comments = summaryVo.getExterior().getComments();
        assertEquals(2, comments.size());

        assertEquals(Arrays.asList("Front Elevation: Damage in the driveway and/or sidewalk was noted. ",
            "Left Elevation: Damage was noted to the siding. "), comments);

        // show(summaryVo);
    }

    @Test
    public void t() {
        ReportSummaryVo summaryVo = ReportSummaryConvert.toSummaryVo("");
        assertNull(summaryVo, "summary vo should be null if json is empty");

        summaryVo = ReportSummaryConvert.toSummaryVo("{}");
        assertNotNull("summary vo shouldn't be null if json is {}", summaryVo);
    }

    @SneakyThrows
    @Test
    void convertJsonToVo() {
        var json = IOUtils.resourceToString("summary.json", StandardCharsets.UTF_8, this.getClass().getClassLoader());
        var reportSummary = ReportSummaryConvert.toSummaryVo(json);
        var jsonRecreated = ReportSummaryConvert.toJson(reportSummary);
        JSONAssert.assertEquals(json, jsonRecreated, false);
    }
}
