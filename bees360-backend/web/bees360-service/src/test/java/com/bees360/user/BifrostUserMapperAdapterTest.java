package com.bees360.user;


import static org.mockito.Mockito.when;

import co.realms9.bifrost.UserManager;
import com.bees360.entity.User;
import com.google.protobuf.BoolValue;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class BifrostUserMapperAdapterTest {

    @Mock private UserManager bifrostUserManager;

    @InjectMocks private BifrostUserMapperAdapter adapter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testGetUserById() {
        var webUserId = 100001L;
        var userId = String.valueOf(webUserId);
        var bifrostUser = Mockito.mock(co.realms9.bifrost.User.class);
        when(bifrostUser.getName()).thenReturn("UName");
        var user = new User();
        var userMFASettings =
                co.realms9.bifrost.user.Message.MFASettings.newBuilder()
                        .addAllMfaSetting(
                                List.of(
                                        co.realms9.bifrost.user.Message.MFASettings.MFAType
                                                .EMAIL_OTP))
                        .setPreferredMfa(
                                co.realms9.bifrost.user.Message.MFASettings.MFAType.EMAIL_OTP)
                        .setEmailVerified(BoolValue.of(true))
                        .setPhoneNumberVerified(BoolValue.of(false))
                        .build();

        when(bifrostUser.isExternalUser()).thenReturn(false);
        when(bifrostUserManager.getUserById(userId)).thenReturn(bifrostUser);
        when(bifrostUserManager.getUserMFASettings(userId)).thenReturn(userMFASettings);

        var actualUser = adapter.getUserById(webUserId);
        var webUserMFASettings = actualUser.getUserMfaSettings();
        Assertions.assertEquals(1, webUserMFASettings.getMfaSettingList().size());
        Assertions.assertTrue(
                webUserMFASettings
                        .getMfaSettingList()
                        .contains(
                                co.realms9.bifrost.user.Message.MFASettings.MFAType.EMAIL_OTP
                                        .name()));
        Assertions.assertTrue(webUserMFASettings.getEmailVerified());
        Assertions.assertFalse(webUserMFASettings.getPhoneNumberVerified());
        Assertions.assertFalse(actualUser.getIsSsoUser());
    }
}
