package com.bees360.service.impl;

import java.awt.Polygon;
import java.awt.geom.Area;
import java.awt.geom.Rectangle2D;
import java.util.Arrays;
import java.util.List;

import com.bees360.entity.ImageAnnotation;
import com.bees360.entity.dto.AnnotationIn3DModel;
import com.bees360.entity.dto.ImageAnnotationCellDto;
import com.bees360.entity.dto.Point;
import com.bees360.util.geom.GeometryUtil;

public class GeometryTest {
	public static boolean disJointDefinitely(ImageAnnotationCellDto annotation, ImageAnnotationCellDto newAnnotation) {
		List<Point> points = annotation.points();
		int npoint = points.size();
		for(Point p: newAnnotation.points()) {
			int isInX = 0;
			int isInY = 0;
			for(int i = 0; i < npoint; i ++) {
				Point judgedPoint = points.get(i);
				isInX += p.getX() < judgedPoint.getX()? -1: 1;
				isInY += p.getY() < judgedPoint.getY()? -1: 1;
			}
			// if |isInX| = npoint, all the points in annotation is on left or right of the point p in newAnnotation
			// if |isInY| = npoint, all the points in annotation is on below or above of the point p in newAnnotation
			// if |isInX| < npoint and |isInY| < npoint, it is possible that the point P is in the annotation
			if(Math.abs(isInX) < npoint && Math.abs(isInY) < npoint) {
				return false;
			}
		}
		// Maybe the point p is in annotations
		return true;
	}

	private static Polygon generatePolygon(List<Point> polygonPoints, int scaling) {
		int[] xpoints = new int[polygonPoints.size()];
		int[] ypoints = new int[polygonPoints.size()];
		int npoint = polygonPoints.size();
		for(int i = 0; i < npoint; i ++) {
			xpoints[i] = (int) (scaling * polygonPoints.get(i).getX());
			ypoints[i] = (int) (scaling * polygonPoints.get(i).getY());
		}
		return new Polygon(xpoints, ypoints, npoint);
	}

	public static void main(String[] args) {
		ImageAnnotationCellDto a = new ImageAnnotationCellDto();
		a.setP1(new Point(3835.4096285289024, 380.087324239125));
		a.setP2(new Point(3296.0543027018443, 376.39018574242857));
		a.setP3(new Point(3290.6248125645293, 935.1620786725421));
		a.setP4(new Point(3829.4277958908406, 939.4789280393151));


		ImageAnnotationCellDto b = new ImageAnnotationCellDto();
		b.setP1(new Point(3636.074730884647, 951.8459070612149));
		b.setP2(new Point(3430.6811908546847, -167.5037055901122));
		b.setP3(new Point(2933.7152485949423, -156.3892531471041));
		b.setP4(new Point(3138.1435966783506, 961.9870614197665));

		System.out.println(disJointDefinitely(a, b));

		List<Point> polygon1 = a.points();
		List<Point> polygon2 = b.points();

		long start = System.currentTimeMillis();
		for(int i = 0; i < 100000; i ++) {
			GeometryUtil.isIntersect(polygon1, polygon2);
		}
		long end = System.currentTimeMillis();
		System.out.println(end - start);

		start = System.currentTimeMillis();
		for(int i = 0; i < 100000; i ++) {
			GeometryUtil.retangleIntersect(polygon1, polygon2);
		}
		end = System.currentTimeMillis();
		System.out.println(end - start);
	}
}
