package com.bees360.service;

import com.bees360.entity.vo.ProjectImageAnnotationVo;
import com.bees360.service.export.impl.PhotoExporterImpl;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class PhotoExporterImplTest {
    @Test
    void testComparator() throws IOException {
        var gson = new Gson();
        var jsonFiles = List.of("test-images-1.json", "test-images-2.json");
        for (String jsonFilePath : jsonFiles) {
            var jsonStr =
                IOUtils.resourceToString(
                    jsonFilePath, Charset.forName("utf-8"), this.getClass().getClassLoader());
            List<ProjectImageAnnotationVo> images =
                gson.fromJson(
                    jsonStr, new TypeToken<List<ProjectImageAnnotationVo>>() {}.getType());

            Assertions.assertDoesNotThrow(() -> images.sort(PhotoExporterImpl.newImageComparator()));
        }
    }

}
