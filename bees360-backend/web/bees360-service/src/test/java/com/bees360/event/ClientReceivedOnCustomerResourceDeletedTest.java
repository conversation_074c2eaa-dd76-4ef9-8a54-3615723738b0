package com.bees360.event;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bees360.event.registry.ResourceDeleted;
import com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum;
import com.bees360.project.Message.ProjectStatus;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.state.ProjectState;
import com.bees360.service.ProjectStatusService;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.function.Function;
import java.util.function.Predicate;

@ExtendWith(MockitoExtension.class)
public class ClientReceivedOnCustomerResourceDeletedTest {

    @Mock private ProjectStatusService projectStatusService;
    @Mock private ProjectIIRepository projectIIRepository;
    private final String towerHillNamespace = "tower_hill";
    private final Function<String, String> towerHillDeliverableResourceKeyDecoder =
            deliverableResourceKey -> StringUtils.split(deliverableResourceKey, "_")[1];
    private final Predicate<String> isProjectNeedToBeReceived =
            projectId -> {
                var project = projectIIRepository.get(projectId);
                var state = project.getCurrentState().getState();
                var status = project.getLatestStatus();
                // Project未关闭且不是CLIENT_RECEIVED才更新状态
                return !ProjectStateEnum.PROJECT_CLOSE.equals(state)
                        && !ProjectStatus.CLIENT_RECEIVED.equals(status);
            };

    @Test
    public void testTowerHillResourceDeleted() throws IOException {
        var listener = createResourceDeletedListener(towerHillNamespace);
        var projectId = RandomStringUtils.randomNumeric(4);
        var event = createResourceDeletedEvent(projectId);
        var project =
                mockProject(
                        randomProjectStateWithoutClose(),
                        randomProjectStatusWithoutClientReceived());
        when(projectIIRepository.get(projectId)).thenReturn(project);
        listener.handle(event);
        verify(projectStatusService, times(1))
                .changeOnClientReceived(anyLong(), anyLong(), anyString());
    }

    @Test
    public void testTowerHillResourceDeletedOnInCorrectState() throws IOException {
        var listener = createResourceDeletedListener(towerHillNamespace);
        var projectId = RandomStringUtils.randomNumeric(4);
        var event = createResourceDeletedEvent(projectId);
        // Project Close
        var projectState = mock(ProjectState.class);
        when(projectState.getState()).thenReturn(ProjectStateEnum.PROJECT_CLOSE);
        var project = mockProject(projectState, randomProjectStatusWithoutClientReceived());
        when(projectIIRepository.get(projectId)).thenReturn(project);
        listener.handle(event);
        verify(projectStatusService, times(0))
                .changeOnClientReceived(anyLong(), anyLong(), anyString());
        // Project Client Received
        project = mockProject(randomProjectStateWithoutClose(), ProjectStatus.CLIENT_RECEIVED);
        when(projectIIRepository.get(projectId)).thenReturn(project);
        listener.handle(event);
        verify(projectStatusService, times(0))
                .changeOnClientReceived(anyLong(), anyLong(), anyString());
    }

    private ClientReceivedOnCustomerResourceDeleted createResourceDeletedListener(
            String namespace) {
        return new ClientReceivedOnCustomerResourceDeleted(
                namespace,
                projectStatusService,
                towerHillDeliverableResourceKeyDecoder,
                isProjectNeedToBeReceived);
    }

    private String getFormatDate() {
        return new SimpleDateFormat("MMddyyyy").format(new Date());
    }

    private ResourceDeleted createResourceDeletedEvent(String projectId) {
        return new ResourceDeleted(
                "BEES"
                        + RandomStringUtils.randomNumeric(5)
                        + "_"
                        + projectId
                        + "_"
                        + getFormatDate()
                        + ".pdf");
    }

    private ProjectState randomProjectStateWithoutClose() {
        var states = ProjectStateEnum.values();
        // 排除两个UNRECOGNIZED和CLIENT_RECEIVED
        var index = RandomUtils.nextInt(1, states.length - 1);
        if (ProjectStateEnum.PROJECT_CLOSE.equals(states[index])) {
            index--;
        }
        var state = states[index];
        var projectState = mock(ProjectState.class);
        when(projectState.getState()).thenReturn(state);
        return projectState;
    }

    private ProjectStatus randomProjectStatusWithoutClientReceived() {
        var statuses = ProjectStatus.values();
        // 排除两个UNRECOGNIZED和CLIENT_RECEIVED
        var index = RandomUtils.nextInt(1, statuses.length - 1);
        if (ProjectStatus.CLIENT_RECEIVED.equals(statuses[index])) {
            index--;
        }
        return statuses[index];
    }

    private ProjectII mockProject(ProjectState projectState, ProjectStatus projectStatus) {
        var project = mock(ProjectII.class);
        when(project.getCurrentState()).thenReturn(projectState);
        when(project.getLatestStatus()).thenReturn(projectStatus);
        return project;
    }
}
