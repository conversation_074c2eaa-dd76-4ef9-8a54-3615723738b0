package com.bees360.firebase;

import com.bees360.atomic.LockProvider;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.FirebaseMissionCompletedStuckV2;
import com.bees360.job.registry.JobPayloads;
import com.bees360.service.firebase.FirebaseMissionService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.job.FirebaseMissionCompletedStuckExecutorV2;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.stream.IntStream;

@SpringBootTest
public class FirebaseMissionCompletedStuckExecutorV2Test {

    @Configuration
    @Import({
        FirebaseMissionCompletedStuckExecutorV2.class
    })
    static class Config {}

    @MockBean public FirebaseMissionService firebaseMissionService;

    @MockBean public FirebaseService firebaseService;

    @MockBean public LockProvider missionLockProvider;

    @Autowired private FirebaseMissionCompletedStuckExecutorV2 executor;

    @Test
    void testConvert() throws IOException {
        FirebaseMissionCompletedStuckV2 completedStuckJob =
            new FirebaseMissionCompletedStuckV2();
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        String pilotId = String.valueOf(RandomUtils.nextLong(10000, 10000000));
        var updateTime = Instant.now();
        completedStuckJob.setPilotId(pilotId);
        completedStuckJob.setMissionPath("test path");
        completedStuckJob.setProjectId(projectId);
        completedStuckJob.setStatusUpdateTime(updateTime);
        var job = JobPayloads.encode(String.valueOf(completedStuckJob.hashCode()), completedStuckJob);
        job = RetryableJob.of(job, 3, Duration.ofMinutes(1), 1.5F);
        Mockito.when(firebaseService.toWebUserId(pilotId)).thenAnswer(e -> Long.parseLong(pilotId));
        executor.execute(job);
        ArgumentCaptor<String> argumentCaptor1th = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Long> argumentCaptor2th = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Long> argumentCaptor3th = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Long> argumentCaptor4th = ArgumentCaptor.forClass(Long.class);
        Mockito.verify(firebaseMissionService).handleMissionCompletedStuck(
            argumentCaptor1th.capture(),
            argumentCaptor2th.capture(),
            argumentCaptor3th.capture(),
            argumentCaptor4th.capture());
        Assertions.assertEquals(completedStuckJob.getMissionPath(), argumentCaptor1th.getValue());
        Assertions.assertEquals(Long.parseLong(completedStuckJob.getPilotId()), argumentCaptor2th.getValue());
        Assertions.assertEquals(Long.parseLong(completedStuckJob.getProjectId()), argumentCaptor3th.getValue());
        Assertions.assertEquals(updateTime.toEpochMilli(), argumentCaptor4th.getValue());
    }
}
