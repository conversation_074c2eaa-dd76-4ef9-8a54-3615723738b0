package com.bees360.service;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.RoomNameEnum;
import com.bees360.entity.firebase.FirebaseRoom;
import com.bees360.service.impl.RoomServiceImpl;
import com.google.cloud.Timestamp;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

public class RoomTest {

    @Test
    public void ImageSetRoomTest() {
        RoomService service = new RoomServiceImpl();

        var bedRoomImage = createEmptyProjectImage();
        service.imageSetRoomFromFirebaseRoom(createBedRoom(), bedRoomImage);

        Assertions.assertEquals(RoomNameEnum.BEDROOM.getDisplay(), bedRoomImage.getRoomName());
        Assertions.assertEquals("1", bedRoomImage.getNumber());
        Assertions.assertEquals("Entry Level", bedRoomImage.getFloorLevel());

        var otherRoomImage = createEmptyProjectImage();
        service.imageSetRoomFromFirebaseRoom(createOtherRoom(), otherRoomImage);
        Assertions.assertEquals("Other Room", otherRoomImage.getRoomName());
        Assertions.assertEquals("1", otherRoomImage.getNumber());
        Assertions.assertEquals("Entry Level", otherRoomImage.getFloorLevel());

        var objectImage = createEmptyProjectImage();
        service.imageSetRoomFromFirebaseRoom(createWaterHeater(), objectImage);
        Assertions.assertEquals(RoomNameEnum.BATHROOM.getDisplay(), objectImage.getRoomName());
        Assertions.assertEquals("1", objectImage.getNumber());
        Assertions.assertEquals("Entry Level", objectImage.getFloorLevel());
    }

    private Map<String, FirebaseRoom> createRoomMap() {
        var map = new HashMap<String, FirebaseRoom>();
        map.put("Bedroom", createBedRoom());
        map.put("Other Room", createOtherRoom());
        return map;
    }

    private FirebaseRoom createBedRoom() {
        FirebaseRoom room = new FirebaseRoom();
        room.setId(1);
        room.setRoomNumber("1");
        room.setFloorLevel("Entry Level");
        room.setName("Bedroom 1");
        room.setRoomId("Bedroom_1");
        room.setLocation(RoomNameEnum.BEDROOM.getDisplay());
        room.setCreateTime(Timestamp.now());
        return room;
    }

    private FirebaseRoom createOtherRoom() {
        FirebaseRoom room = new FirebaseRoom();
        room.setId(2);
        room.setRoomNumber("1");
        room.setFloorLevel("Entry Level");
        room.setName("Unknown Room");
        room.setCreateTime(Timestamp.now());
        return room;
    }

    private FirebaseRoom createWaterHeater() {
        FirebaseRoom room = new FirebaseRoom();
        room.setId(3);
        room.setRoomNumber("1");
        room.setFloorLevel("Entry Level");
        room.setName("Water Heater 1");
        room.setRoomId("Water_Heater_1");
        room.setLocation(RoomNameEnum.BATHROOM.getDisplay());
        room.setCreateTime(Timestamp.now());
        return room;
    }

    private ProjectImage createEmptyProjectImage() {
        return new ProjectImage();
    }
}
