package com.bees360.service.impl;

import com.bees360.event.EventPublisher;
import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.contract.ContractManager;
import com.bees360.customer.CustomerManager;
import com.bees360.entity.Company;
import com.bees360.entity.enums.CompanyTypeEnum;
import com.bees360.mapper.CompanyMapper;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.isA;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CompanyServiceImplTest {

    @Mock
    CompanyMapper companyMapper;

    @Mock
    EventPublisher eventPublisher;

    @InjectMocks
    private CompanyServiceImpl companyService;

    @Mock
    private CustomerManager customerManager;

    @Mock
    private ContractManager contractManager;

    private static @NotNull Company copyToNewCompany(Company oldCompany) {
        String newCompanyName = oldCompany.getCompanyName() + "-NEW";

        Company newCompany = new Company();
        newCompany.setCompanyId(oldCompany.getCompanyId());
        newCompany.setCompanyName(newCompanyName);
        newCompany.setCompanyType(oldCompany.getCompanyType());
        newCompany.setCompanyKey(oldCompany.getCompanyKey());
        newCompany.setPhone(oldCompany.getPhone());
        newCompany.setEmail(oldCompany.getEmail());
        newCompany.setContactName(oldCompany.getContactName());
        newCompany.setWebsite(oldCompany.getWebsite());
        newCompany.setLogo(oldCompany.getLogo());
        newCompany.setIsDeleted(oldCompany.getIsDeleted());
        newCompany.setUpdatedTime(oldCompany.getUpdatedTime() + 1000);
        newCompany.setCreatedTime(oldCompany.getUpdatedTime());
        return newCompany;
    }

    public Company newCompany(long companyId) {
        var company = new Company();
        // Create a company object
        company = new Company();
        company.setCompanyId(companyId);
        company.setCompanyName("A company" + companyId);
        company.setCompanyKey("A key");
        company.setCompanyType(CompanyTypeEnum.INSURANCE_COMPANY.getCode());
        company.setPhone("15451212124");
        company.setEmail("<EMAIL>");
        company.setContactName("Her");
        company.setWebsite("https://company.com");
        company.setLogo("companies/" + companyId + "/logo/logo.png");
        company.setIsDeleted(false);
        company.setUpdatedTime(*********);
        company.setCreatedTime(*********);

        return company;
    }

    /**
     * Test method CompanyServiceImpl$updateCompany</br>
     * </br>
     * Case:</br>
     * The updated target company name does not exist in the database</br>
     *
     * @throws ServiceException
     */
    @Test
    public void testUpdateCompanyCaseOfUnmodified() throws ServiceException {
        var oldCompany = newCompany(1L);
        // stubbing
        when(companyMapper.getById(oldCompany.getCompanyId())).thenReturn(oldCompany);
        when(companyMapper.update(isA(Company.class))).thenReturn(Integer.valueOf(1));

        Company company = new Company();
        company.setLogo("companies/3343/logo.png");

        // implement
        companyService.updateCompany(oldCompany.getCompanyId(), company);

        // Verify "execution"
        verify(companyMapper, times(2)).getById(oldCompany.getCompanyId());
        verify(companyMapper, never()).getByName(oldCompany.getCompanyName());
        verify(companyMapper, times(1)).update(isA(Company.class));
    }

    /**
     * Test method CompanyServiceImpl$updateCompany</br>
     * </br>
     * Case:</br>
     * The updated target company name does not exist in the database</br>
     *
     * @throws ServiceException
     */
    @Test
    public void testUpdateCompanyCaseOfNewCompanyNameNotExisted() throws ServiceException {
        var oldCompany = newCompany(2L);
        var newCompany = copyToNewCompany(oldCompany);

        // stubbing
        when(companyMapper.getById(oldCompany.getCompanyId())).thenReturn(oldCompany, newCompany);

        // You have to assume that this update operation is successful
        when(companyMapper.update(isA(Company.class))).thenReturn(Integer.valueOf(1));
        // The new company name does not exist in the database
        when(companyMapper.getByName(newCompany.getCompanyName())).thenReturn(null);

        // implement
        companyService.updateCompany(oldCompany.getCompanyId(), newCompany);

        // Verify "execution"
        verify(companyMapper, times(2)).getById(oldCompany.getCompanyId());
        verify(companyMapper, times(1)).getByName(newCompany.getCompanyName());
        verify(companyMapper, times(1)).update(isA(Company.class));
    }

    /**
     * Test method CompanyServiceImpl$updateCompany</br>
     * </br>
     * Case:</br>
     * The updated target company name already exists in the database</br>
     *
     * @throws ServiceException
     */
    @Test
    public void testUpdateCompanyCaseOfNewCompanyNameExisted() throws ServiceException {
        var oldCompany = newCompany(9L);

        var newCompany = copyToNewCompany(oldCompany);

        // stubbing
        when(companyMapper.getById((oldCompany.getCompanyId()))).thenReturn(oldCompany);
        // The new company name already exists in the database
        when(companyMapper.getByName(newCompany.getCompanyName())).thenReturn(newCompany);

        // implement
        var exception = assertThrows(ServiceException.class, () -> {
            companyService.updateCompany(oldCompany.getCompanyId(), newCompany);
        });
        assertEquals(MessageCode.COMPANY_NAME_HAS_EXISTED, exception.getMsgCode());

        // Verify "execution"
        verify(companyMapper, times(1)).getById(oldCompany.getCompanyId());
        verify(companyMapper, times(1)).getByName(newCompany.getCompanyName());
        verify(companyMapper, never()).update(isA(Company.class));
    }

    @Test
    public void testCreateCompany() throws ServiceException {
        var oldCompany = newCompany(4L);

        when(companyMapper.insert(oldCompany)).thenReturn(1);
        when(companyMapper.getByName(oldCompany.getCompanyName())).thenReturn(null, oldCompany);

        companyService.createCompany(oldCompany);

        verify(companyMapper, times(2)).getByName(oldCompany.getCompanyName());
        verify(companyMapper, times(1)).insert(oldCompany);
    }

    @Test
    public void testCreateCompanyCaseNameExisted() throws ServiceException {
        var oldCompany = newCompany(5L);
        Company companyWithSameName = new Company();
        companyWithSameName.setCompanyName(oldCompany.getCompanyName());

        when(companyMapper.getByName(oldCompany.getCompanyName())).thenReturn(companyWithSameName);

        // Expected exception check
        var exception = assertThrows(ServiceException.class, () -> {
            companyService.createCompany(oldCompany);
        });
        assertEquals(MessageCode.COMPANY_NAME_HAS_EXISTED, exception.getMsgCode());

        verify(companyMapper, times(1)).getByName(oldCompany.getCompanyName());
        verify(companyMapper, never()).insert(oldCompany);
    }

    @Test
    public void testUpdateCompanyKey() throws ServiceException {
        var oldCompany = newCompany(6L);
        String newCompanyKey = oldCompany.getCompanyKey() + "-NEW";

        when(companyMapper.updateCompanyKey(anyLong(), anyString(), anyLong())).thenReturn(1);
        when(companyMapper.getById(oldCompany.getCompanyId())).thenReturn(oldCompany);

        // implement
        companyService.updateCompanyKey(oldCompany.getCompanyId(), newCompanyKey);

        // Verify "execution"
        verify(companyMapper, times(1)).updateCompanyKey(anyLong(), anyString(), anyLong());
    }
}
