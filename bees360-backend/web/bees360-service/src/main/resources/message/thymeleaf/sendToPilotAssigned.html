<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:th="http://www.thymeleaf.org">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>sendToPilotAssigned</title>
</head>

<body style="margin: 0; padding: 0;">
<table border="0" cellpadding="0" cellspacing="0" width="100%">
	<tr>
		<td>
			<table align="left" border="0" cellpadding="0" cellspacing="0" width="600" style="border-collapse: collapse;">
				<tr>
					<td>
						Dear <span th:utext="${name}">lucy</span>,<br /><br />
					</td>
				</tr>
				<tr>
					<td>
						You are assigned to be the <b>PILOT</b>
						for the following <span th:utext="${#lists.size(projects)}">45</span> <span th:utext="${projectOrProjects}">projects</span>
                        in <b>Batch #<span th:utext="${batchNo}"></span>.</b> Here are the details:<br/><br/>
<!--                        <span th:if="${not #strings.isEmpty(totalPay)}">Total pay is <b>$<span th:utext="${totalPay}"></b>.</span></span>-->
<!--                        <span th:if="${not #strings.isEmpty(dueDate)} or ${not #strings.isEmpty(planPayDate)} ">This Batch </span><span th:if="${not #strings.isEmpty(dueDate)}">is due on <b><span th:utext="${dueDate}"></span></b></span>-->
<!--                        <span th:if="${not #strings.isEmpty(planPayDate)}"> and will be paid on <b><span th:utext="${planPayDate}"> once all projects in this batch are completed by the due date</span></b>. </span>-->
                        Base pay: <b>$<span th:utext="${basePay}?:_">0</span></b><br/>
                        Extra pay: <b>$<span th:utext="${extraPay}?:_">0</span></b><br/>
                        Total pay: <b>$<span th:utext="${totalPay}?:_">0</span></b><br/>
                        Due date: <b><span th:utext="${dueDate}"></span></b><br/>
                        Pay date: <b><span th:utext="${planPayDate}"></span></b><br/>
                    </td>
				</tr>
				<tr>
					<td><br /></td>
				</tr>
                <tr>
                    <td th:if="${not #strings.isEmpty(note)}"><b>Note: </b><span th:utext="${note}"></span></td>
                </tr>
                <tr>
                    <td><br/></td>
                </tr>
				<tr>
					<td>
						<table style="border:1px solid #000; border-collapse:collapse;" width="800">
							<tr style="border:1px solid #000;">
								<th style="border:1px solid #000;">Project ID</th>
								<th style="border:1px solid #000;">Address</th>
								<th style="border:1px solid #000;">Service Type</th>
								<th style="border:1px solid #000;">Insured By</th>
								<th style="border:1px solid #000;">Airspace Restrictions</th>
							</tr>
							<tr  style="border:1px solid #000;"th:each="project: ${projects}">
								<td style="border:1px solid #000;" th:utext="${project.projectId}"></td>
								<td style="border:1px solid #000;" th:utext="${project.fullAddress}"></td>
								<td style="border:1px solid #000;" th:utext="${project.serviceTypeName}"></td>
								<td style="border:1px solid #000;" th:utext="${project.insuredBy}"></td>
								<td style="border:1px solid #000;" th:utext="${project.flyZoneTypeName}"></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td><br /></td>
				</tr>
				<tr>
					<td>
                        <p th:fragment="footer">
                            <span>Bees360 Operations Team<br/>
                            <a href="https://www.bees360.com">www.bees360.com</a></span>
                        </p>
<!--						<span th:include="_footer.html :: footer">FOOTER</span>-->
					</td>
				</tr>
			</table>
		</td>
	</tr>
</table>
</body>
</html>
