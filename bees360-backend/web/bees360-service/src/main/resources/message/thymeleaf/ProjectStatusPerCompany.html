<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>ProjectStatisticsPerCompany</title>
</head>
<body>
    <p>Daily Summary - <span th:utext="${dateStr}">2020/12/25</span></p>
    <div>
        <p>Here is the summary for claims on <span th:utext="${dateStr}">2020/12/25</span>:</p>
        <table style="border-collapse: collapse;" >
            <thead>
            <tr>
                <td style="border:1px solid #000; text-align: center">Company Name</td>
                <td style="border:1px solid #000; text-align: center">Service Line</td>
                <td style="border:1px solid #000; text-align: center">#Projects Created</td>
                <td style="border:1px solid #000; text-align: center">#Projects Assigned</td>
                <td style="border:1px solid #000; text-align: center">#Projects Contacted</td>
            </tr>
            </thead>
            <tbody th:if="${claimSummary} !=null">
            <tr th:each="projectStat:${claimSummary.perCompanyStats}">
                <td style="border:1px solid #000; text-align: center" th:utext="${projectStat.company.companyName}">
                    Example Company
                </td>
                <td style="border:1px solid #000; text-align: center" th:utext="${projectStat.serviceType}">Example
                    Service
                </td>
                <td style="border:1px solid #000; text-align: center" th:utext="${projectStat.projectCreated}">0</td>
                <td style="border:1px solid #000; text-align: center" th:utext="${projectStat.projectAssigned}">0</td>
                <td style="border:1px solid #000; text-align: center" th:utext="${projectStat.projectContacted}">0</td>
            </tr>
            <tr>
                <td style="border:1px solid #000; text-align: center">Total</td>
                <td style="border:1px solid #000; text-align: center"></td>
                <td style="border:1px solid #000; text-align: center" th:utext="${claimSummary.totalProjectCreated}">0</td>
                <td style="border:1px solid #000; text-align: center" th:utext="${claimSummary.totalProjectAssigned}">0</td>
                <td style="border:1px solid #000; text-align: center" th:utext="${claimSummary.totalProjectContacted}">0</td>
            </tr>
            </tbody>
            <tbody th:if="${claimSummary} == null">
            <tr>
                <td style="border:1px solid #000; text-align: center">Total</td>
                <td style="border:1px solid #000; text-align: center"></td>
                <td style="border:1px solid #000; text-align: center">0</td>
                <td style="border:1px solid #000; text-align: center">0</td>
                <td style="border:1px solid #000; text-align: center">0</td>
            </tr>
            </tbody>
        </table>
    </div>


    <div>
        <p>Here is the summary for underwriting on <span th:utext="${dateStr}">2020/12/25</span>:</p>
        <table style="border-collapse: collapse;" >
            <thead>
            <tr>
                <td style="border:1px solid #000; text-align: center">Company Name</td>
                <td style="border:1px solid #000; text-align: center">Service Line</td>
                <td style="border:1px solid #000; text-align: center">#Projects Created</td>
                <td style="border:1px solid #000; text-align: center">#Projects Assigned</td>
                <td style="border:1px solid #000; text-align: center">#Projects Contacted</td>
            </tr>
            </thead>
            <tbody th:if="${underWritingSummary} != null">
            <tr th:each="projectStat:${underWritingSummary.perCompanyStats}">
                <td style="border:1px solid #000; text-align: center" th:utext="${projectStat.company.companyName}">Example Company</td>
                <td style="border:1px solid #000; text-align: center" th:utext="${projectStat.serviceType}">Example Service</td>
                <td style="border:1px solid #000; text-align: center" th:utext="${projectStat.projectCreated}">0</td>
                <td style="border:1px solid #000; text-align: center" th:utext="${projectStat.projectAssigned}">0</td>
                <td style="border:1px solid #000; text-align: center" th:utext="${projectStat.projectContacted}">0</td>
            </tr>
            <tr>
                <td style="border:1px solid #000; text-align: center">Total</td>
                <td style="border:1px solid #000; text-align: center"></td>
                <td style="border:1px solid #000; text-align: center" th:utext="${underWritingSummary.totalProjectCreated}">0</td>
                <td style="border:1px solid #000; text-align: center" th:utext="${underWritingSummary.totalProjectAssigned}">0</td>
                <td style="border:1px solid #000; text-align: center" th:utext="${underWritingSummary.totalProjectContacted}">0</td>
            </tr>
            </tbody>
            <tbody th:if="${underWritingSummary} == null">
            <tr>
                <td style="border:1px solid #000; text-align: center">Total</td>
                <td style="border:1px solid #000; text-align: center"></td>
                <td style="border:1px solid #000; text-align: center">0</td>
                <td style="border:1px solid #000; text-align: center">0</td>
                <td style="border:1px solid #000; text-align: center">0</td>
            </tr>
            </tbody>
        </table>
    </div>
</body>
</html>
