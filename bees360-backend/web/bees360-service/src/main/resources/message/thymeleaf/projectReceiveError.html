<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>projectReceiveError</title>
</head>

<body>
<div>
    <p>
        The status of the project <span th:utext="${#numbers.formatInteger(projectId, 0)}">10000</span> was changed to <b>Receive Error</b>.
    </p>
    <p th:if="${!#strings.isEmpty(comment)}">
        With comment: <span th:utext="${comment}">This is the comment.</span>
    </p>
    <p th:include="_footer.html :: footer">FOOTER</p>
</div>
</body>
</html>
