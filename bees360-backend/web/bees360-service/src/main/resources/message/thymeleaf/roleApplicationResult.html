<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>roleApplicationResult</title>
</head>

<body>
	<div>
		Dear <span th:utext="${name}">Lucy</span>,<br/><br/>
		Your application for the new <span th:utext="${#lists.size(roles) > 1? 'roles': 'role'}">roles</span> 
		<b th:utext="${#strings.listJoin(roles,',')}">Adjuster</b> <span th:utext="${#lists.size(roles) > 1? 'are': 'is'}">are</span>
		<span th:utext="${isPassed? 'approved': 'disapproved'}">approved</span>.<br/>
		<p th:if="${isPassed}==false">
			The reason is: <span th:utext="${comment}">No attachment was uploaded.</span>
		</p>
		<p th:include="_footer.html :: footer">FOOTER</p>
	</div>
</body>
</html>
