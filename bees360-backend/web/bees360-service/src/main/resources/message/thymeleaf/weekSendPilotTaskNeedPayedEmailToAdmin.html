<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>weekSendPilotTaskNeedPayedEmailToAdmin</title>
</head>

<body>
	<div>
		<p>
			Dear Admin,<br/><br/>
			The following batches request a payment check.
            <table style="border:1px solid #000; border-collapse:collapse;" width="1000">
                <tr style="border:1px solid #000;">
                    <th style="border:1px solid #000;">Pilot Name</th>
                    <th style="border:1px solid #000;">Mission Number</th>
                    <th style="border:1px solid #000;"># of Projects</th>
                    <th style="border:1px solid #000;">Base</th>
                    <th style="border:1px solid #000;">Extra</th>
                    <th style="border:1px solid #000;">Total</th>
                    <th style="border:1px solid #000;">Payment Date</th>
                    <th style="border:1px solid #000;">Due Date</th>
                    <th style="border:1px solid #000;">Return To Client</th>
                </tr>
                <tr  style="border:1px solid #000; text-align:center;"th:each="project: ${projectModelList}">
                    <td style="border:1px solid #000; " th:utext="${project.pilotName}"></td>
                    <td style="border:1px solid #000; " th:utext="${project.batchNo}"></td>
                    <td style="border:1px solid #000; " th:utext="${project.projectCount}"></td>
                    <td style="border:1px solid #000; " th:utext="${project.basePay}"></td>
                    <td style="border:1px solid #000; " th:utext="${project.extraPay}"></td>
                    <td style="border:1px solid #000; " th:utext="${project.totalPay}"></td>
                    <td style="border:1px solid #000; " th:utext="${project.playPaymentDate}"></td>
                    <td style="border:1px solid #000;" th:utext="${project.dueDate}"></td>
                    <td style="border:1px solid #000; " th:utext="${project.taskCompleted}"></td>
                </tr>
            </table>
        <br/><br/>
        <table style="border:1px solid #000; border-collapse:collapse;" width="600">
            <tr style="border:1px solid #000;">
                <th style="border:1px solid #000;">Mission Number</th>
                <th style="border:1px solid #000;">Associated Project ID</th>
                <th style="border:1px solid #000;">Service Type</th>
                <th style="border:1px solid #000;">Return To Client</th>
            </tr>
            <tr  style="border:1px solid #000;text-align:center;"th:each="statusModel: ${statusModelList}">
                <td style="border:1px solid #000;" th:utext="${statusModel.batchNo}"></td>
                <td style="border:1px solid #000;" th:utext="${statusModel.projectId}"></td>
                <td style="border:1px solid #000;" th:utext="${statusModel.serviceTypeName}"></td>
                <td style="border:1px solid #000;" th:utext="${statusModel.returnToClient}"></td>
            </tr>
        </table>
		</p>
		<p th:include="_footer.html :: footer">FOOTER</p>
	</div>
</body>
</html>
