<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:th="http://www.thymeleaf.org">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>rosterApplicationReceived</title>
</head>
<body>
	<div>
		<table>
			<tr>
				<th align="left">Name:</th><td><span th:utext="${roster.firstName} + ' ' + ${roster.lastName}"><PERSON></span></td>
			</tr>
			<tr>
				<th align="left">Phone No:</th><td><span th:utext="${roster.phone}"></span>*********</td>
			</tr>
			<tr>
				<th align="left">Email:</th><td><span th:utext="${roster.email}"></span><EMAIL></td>
			</tr>
			<tr>
				<th align="left">Mailing Address:</th>
				<td>
					<span th:utext="${roster.address} + ', ' + ${roster.city} + ', ' + ${roster.state} + ' ' + ${roster.zipCode} + ', ' + ${roster.country}">
						2509 Burdette Street, New Orleans, LA 70125, USA
					</span>
				</td>
			</tr>
			<tr>
				<th align="left">Travel Radius(miles):</th><td><span th:utext="${roster.travelRadius}">30</span></td>
			</tr>
			<tr>
				<th align="left">Would like to travel more than 100 miles from your mailing address?: </th><td><span th:utext="${roster.moreThan100MilesTraveled}">true</span></td>
			</tr>
			<tr>
				<th align="left">Would like to be deployed in a CAT event?:</th><td><span th:utext="${roster.catEventDeployed}">false</span></td>
			</tr>
			<tr>
				<th align="left">Operating City & State: </th><td><span th:utext="${roster.operatingCityState}">shenzhen</span></td>
			</tr>
			<tr>
				<th align="left">Designated Home State & Licence:</th><td><span th:utext="${roster.designatedHomeStateLicense}">65148564fd</span></td>
			</tr>
			<tr>
				<th align="left">Additional Operating Territoties:</th><td><span th:utext="${roster.additionalOperatingTerritories}">shanghai</span></td>
			</tr>
			<tr>
				<th align="left">Additional Licenses or Certifications: </th><td><span th:utext="${roster.additionalLicense}">https://s3.amazonaws.com/bees360/roster-user-resume/13954785486/pattern-examples.pdf</span></td>
			</tr>
			<tr>
				<th align="left">Years Of Experience:</th><td><span th:utext="${roster.yearsOfExperience}">3</span></td>
			</tr>
			<tr>
				<th align="left">Message: </th><td><span th:utext="${roster.message}">May I join your team?</span></td>
			</tr>
		</table>
	</div>
</body>
</html>
