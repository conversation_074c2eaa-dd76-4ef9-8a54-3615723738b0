<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>orderTaskInitial</title>
</head>

<body>
	<div>
		<p>
			Dear Admin,<br/><br/>
			<span th:utext="${name}">Tom</span> (<span th:utext="${account}"><EMAIL></span>) creates project
			<b th:utext="${projectId}">12368</b>
			at <b th:utext="${address}">2509 Burdette Street, New Orleans, LA 70125</b>.
		</p>
		<p th:if="!${#lists.isEmpty(reports)}">
			He requests the following reports:<br/>
			<ul>
				<li th:each="report: ${reports}"><b th:utext="${report}">Damage Report</b></li>	
			</ul>
		</p>
		<p th:if="${needPilot}">
			He requests a <b>Pilot</b> for the project. Please assign a Pilot for this project as soon as possible.
		</p>
		<p th:include="_footer.html :: footer">FOOTER</p>
	</div>
</body>
</html>
