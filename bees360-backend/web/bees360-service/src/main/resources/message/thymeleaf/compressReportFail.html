<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>compressReportFail</title>
</head>

<body>
<div>
    Fail to compress report of project:<br/>
    <p>
        ProjectId: <span th:utext=${projectId}>100012</span><br/>
        ReportId: <span th:utext=${reportId}>42sNV0nmXr726kIY6RkwUWDY_iOf4BgO</span><br/>
        ReportKey: <span th:utext=${reportKey}>project/100012/report/1608366803030Full-scopeUnderwritingReport.pdf</span><br/>
    </p>
    --------------------------------------------------<br />
    <pre th:utext=${exception}>
        java.lang.IndexOutOfBoundsException:
			 Index: 0, Size: 0 at java.util.ArrayList.rangeCheck(ArrayList.java:657) at
			 java.util.ArrayList.set(ArrayList.java:448) at com.bees360.service.impl.ServiceServiceImpl.contactUs(ServiceServiceImpl.java:107) at
			 sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) at
			 sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) at
			 sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) at
    </pre>
</div>
</body>
</html>
