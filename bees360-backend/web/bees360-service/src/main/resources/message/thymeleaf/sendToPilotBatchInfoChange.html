<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>sendToPilotBatchInfoChange</title>
</head>

<body style="margin: 0; padding: 0;">
<table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
        <td>
            <table align="left" border="0" cellpadding="0" cellspacing="0" width="600" style="border-collapse: collapse;">
                <tr>
                    <td>
                        Dear <span th:utext="${name}">lucy</span>,<br /><br />
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Batch #<span th:utext="${batchNo}"></span></b>  has been modified.
                        <span th:if="${not #strings.isEmpty(totalPay)}"> Its total pay is <b>$<span th:utext="${totalPay}"></b></span>.</span>
                        <span th:if="${not #strings.isEmpty(dueDate)}"> Due date is <b><span th:utext="${dueDate}"></span></b></span>
                        <span th:if="${not #strings.isEmpty(planPayDate)}">It will be paid on <b><span th:utext="${planPayDate}"> once completed by the due date</span></b>. </span>
                    </td>
                </tr>
                <tr>
                    <td><br /></td>
                </tr>
                <tr>
                    <td th:if="${not #strings.isEmpty(note)}"><b>Note: </b><span th:utext="${note}"></span>.</td>
                </tr>

                <tr>
                    <td><br /></td>
                </tr>
                <tr>
                    <td>
                        <p th:fragment="footer">
                            <span>Bees360 Operations Team<br/>
                            <a href="https://www.bees360.com">www.bees360.com</a></span>
                        </p>
                        <!--						<span th:include="_footer.html :: footer">FOOTER</span>-->
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
</body>
</html>
