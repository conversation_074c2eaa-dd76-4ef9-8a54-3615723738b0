<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:th="http://www.thymeleaf.org">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>imageUploadedStatusIsAutoSet</title>
</head>

<body style="margin: 0; padding: 0;">
<table border="0" cellpadding="0" cellspacing="0" width="100%">
	<tr>
		<td>
			<table align="left" border="0" cellpadding="0" cellspacing="0" width="600" style="border-collapse: collapse;">
				<tr>
					<td>
						The status of <span th:utext="${#lists.size(projects)}">125</span> project are automatically changed to "Image Upload".
					</td>
				</tr>
				<tr>
					<td><br /></td>
				</tr>
				<tr>
					<td>
						<table border="1px solid #000;" style="border-collapse:collapse;" width="600">
							<thead style="border:1px solid #000;">
								<tr style="border:1px solid #000;">
									<th style="border:1px solid #000;">Index</th>
									<th style="border:1px solid #000;">Project Id</th>
									<th style="border:1px solid #000;">Address</th>
								</tr>
							</thead>
							<tbody>
								<tr style="border:1px solid #000;" th:each="project,stat: ${projects}">
									<td style="border:1px solid #000;" th:utext="${stat.count}">1</td><td th:utext="${project.projectId}">1000253</td><td th:utext="${project.address}">1229 Luna Ln, Garland, Texas 75044</td>
								<tr>
							</tbody>
						</table>
					</td>
				</tr>
				<tr>
					<td><br /></td>
				</tr>
				<tr>
					<td>
						<span th:include="_footer.html :: footer">FOOTER</span>
					</td>
				</tr>
			</table>
		</td>
	</tr>
</table>
</body>
</html>
