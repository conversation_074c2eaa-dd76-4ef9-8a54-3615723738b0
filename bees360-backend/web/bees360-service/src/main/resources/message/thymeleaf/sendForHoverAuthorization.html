<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>sendForHoverAuthorization</title>
</head>

<body>
<div>
    <p>
        Dear admin,<br/><br/>

        The system failed to automatically subscribe to the service of the hover. <span th:if="${projectId}">The Project ID is: <b th:utext="${projectId}">1000012</b></span>.<br/><br/>

        The reason is that the authorization failed. You need to log in to the hover account manually and perform the following operations:<br/><br/>

        Step 1: log in to the personal page of the hover account (<b><EMAIL></b>)<br/><br/>

        Step 2: click setting to enter the interface, find the sidebar of integrations to enter, finally find the option of private team integrations, and click to jump to the interface (<a href="https://hover.to/ui/#/hvr/settings/integrations">https://hover.to/ui/#/hvr/settings/integrations</a>)
        <br/><br/>

        Step 3: click the allow button to agree. After the success, it will automatically return to the bees360 interface<br/><br/>
    </p>
    <p th:include="_footer.html :: footer">FOOTER</p>
</div>
</body>
</html>
