<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>orderTasksChanged</title>
</head>

<body>
	<div>
		<p>
			<span th:utext="${name}">Tom</span> (<span th:utext="${email}"><EMAIL></span>) change the order 
			of project <b th:utext="${projectId}">1000252</b> at <b th:utext="${address}">2509 Burdette Street, New Orleans, LA 70125</b>
			<p th:if="!${#lists.isEmpty(addReports)}">
				New reports ordered:<br/>
				<ul>
					<li th:each="report: ${addReports}"><b th:utext="${report}">Damage Report</b></li>	
				</ul>
			</p>
			<p th:if="!${#lists.isEmpty(removeReports)}">
				Reports remove:<br/>
				<ul>
					<li th:each="report: ${removeReports}"><b th:utext="${report}">Measurement Report</b></li>	
				</ul>
			</p>
			<p th:if="${needPilotChange}">
				Change pilot request: <b th:if="${needPilot}">Request</b><b th:unless="${needPilot}">Cancel</b>
			</p>
		</p>
		<p th:include="_footer.html :: footer">FOOTER</p>
	</div>
</body>
</html>
