<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>errorToEngineer</title>
</head>

<body>
	<div>
		From Server: <span th:utext=${localIp}>127.0.0.1</span><br />
		message:<br />
        <span th:utext=${message}>Exception</span>
		--------------------------------------------------<br />
		<span th:utext=${sw}>java.lang.IndexOutOfBoundsException:
			 Index: 0, Size: 0 at java.util.ArrayList.rangeCheck(ArrayList.java:657) at 
			 java.util.ArrayList.set(ArrayList.java:448) at com.bees360.service.impl.ServiceServiceImpl.contactUs(ServiceServiceImpl.java:107) at 
			 sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) at 
			 sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) at 
			 sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) at 
		</span>
	</div>
</body>
</html>
