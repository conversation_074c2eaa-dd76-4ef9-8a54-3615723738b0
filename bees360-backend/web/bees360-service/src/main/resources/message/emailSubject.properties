# securityCode
securityCode=Your security code is {0}
sendToNonSystemUser=Your new project has been created
sendToSystemUser=Your new project has been created
# message
errorToEngineer=EXIGENCY: {0}
accountBinded=Your email has been linked to your Bees360 account
# name,email
shareReport={0} <{1}> shared a report with you in Bees360 platform
accountChanged=Changes on your Bees360 account
# projectId
imageUploadedSendToCreator=Images for project {0,number,#} have been received
# projectId
imageUploadedSendToStaffs=Your new project {0,number,#} is received.
# projectNum
imageUploadedStatusIsAutoSet=The status of {0,number,#} projects are automatically changed to Image Uploaded
# reportName,projectId
reportApproved={0} for project {1,number,#} is approved
#
pilotRoleApplicationApproved=Your Application for Pilot Role Is Approved!
# role/roles,approved/disapproved
roleApplicationResult=Your application for the new {0} is {1}
# name,projectId
inviteAsVisitor={0} invites you to project {1,number,#}
# projectId
reportSubmitted=The report for project {0,number,#} has been submitted
# reportName, project id
reportDisapproved=The {0} for project {1,number,#} is disapproved
# firstName,lastName,email,phone,company
contactUs=[CONTACT US] From {0} {1}({2}, {3}, {4})
#reportName,projectId
adminRealtimeReport=The {0} for project {1,number,#} has been generated
#reportName,projectId
processorRealtimeReport=The {0} for project {1,number,#} has been generated
#role, projectId
memberArranged=NEW JOB! You are assigned to be the {0} of the project {1,number,#}
memberArrangementCancel=Removal of Your Bees360 Assignment
# projectId, address
inspectionTimeChangedIsTimeFrom=The inspection time for project {0,number,#} at {1} has been changed
# projectId, address
inspectionTimeChangedIsTimeToAndTimeFrom=The inspection time for project {0,number,#} at {1} has been changed
# ProjectId,Address,Name
projectDeleted=The project {0,number,#} at {1} has been deleted by {2}
creatorBooking=Bees360 Order Confirmation
workerBookingCreated=Inspection has been ordered
reportsForBooking=Your reports are ready
# name, email,phone,company
partnerProgram=[PARTNER PROGRAM] From {0}({1}, {2}, {3})
partnerProgramRecieved=Bees360 has received your Partner Program application
# name, email
projectCreated={0} ({1}) creates a project
#projectId, address
sendToAdminToAsignPilot=Please assign a pilot for project {0,number,#} at {1} as soon as possible
# a/an, role, projectNum, project/projects
sendToMemberAssigned=You are assigned to be {0} {1} for {2,number,#} {3}
# projectNum, project/projects
sendToMemberInvited=You are invited into {0,number,#} {1} as a Visitor
#projectId
requestCancel=The request of assigning a Pilot for project {0,number,#} has been canceled
# projectId, address
orderTaskInitial=The project {0,number,#} at {1} requests Reports or Pilot
# name, email, projectNum
projectsCreated={0}({1}) create {2} projects in the Bees360 platform
# name, email, projectId, address
orderTasksChanged={0} ({1}) changes the order of project {2,number,#} at {3}
reportTasksChanged=Order Modification of Your Bees360 Report
reportTasksAdded=Order Confirmation of Your Bees360 Report
reportTasksRemoved=Order Cancellation of Your Bees360 Report
userRoleLost=Your {0} role is deactivated
roleApplicationRequest=A role application has been submitted
rosterApplicationReceived={0} {1} has joined the roster. Please follow up as soon as possible
rosterApplicationBees360Account=Welcome to Bees360! We have created an account for you
pilotRegistration= Welcome to Bees360 Pilot Network
# send AD to house owner when the inspection scheduled
sendADToHouseOwner= Your claim inspection has been scheduled
userCreated=Welcome to Bees360
sendToPilotAssigned=You are assigned to be a Pilot for {0,number,#} {1}
sendToPilotDailyNotify=You have not uploaded the following {0,number,#} {1}
sendToPilotBatchInfoChange=Your Batch Was Modified
sendToAdminOnPilotAcceptOrReject={0} {1} {2}
pilotFeedback=New feedback by {0} about #{1}

emergencyCaseNotice=Loss Type Verification Claim {0}, Project {1}
projectReceiveError=Project Receive Error
infoAdminOnPilotUpdateLicenses=Pilot License Updated
weekSendPilotTaskNeedPayedEmailToAdmin= Payment Summary {0} (Between {1})

compressReportFail=Fail to compress report

sendOnClaimPaid=Claim {0} has been paid
alertFailToLookUpFlyZoneType=Fail to look up fly zone type for project {0,number,#}



sendForHoverAuthorization=Authorization failure for subscription to hover service
sendForHoverError=Subscription to the hover service failed due to an internal exception
sendForHoverContact=Hover platform exceptions cause subscription failure

infoAdminRoleWorkWeeklyCount=Workload statistics completed this week: {0}
sendFailToCreateInvoice=Failed to create invoice file

pilotBannedNotification=[Urgent] Projects Need to Reassign Due to Banned Pilot(s)

ProjectStatusPerCompany=Daily Jobs Stats - {0}
