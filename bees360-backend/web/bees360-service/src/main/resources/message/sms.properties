#########################################
#         SMS MESSAGE TEMPLATE
#########################################
# If you change the template, please update com.bees360.service.impl.SmsTemplateFormat as well.
#
# securityCode
securityCode=[Bees360] Your security code is {0}. This code is valid for 2 minutes.
sendToNonSystemUser=[Bees360] Your project has been created. Please login your account at www.bees360.com using this phone number and password ({0}) and check it out. Please remember to reset you password after login.
sendToSystemUser=[Bees360] Your project has been created. Please login your account at www.bees360.com to check it out.
# serverIp, message, exception message
errorToEngineer=[Bees360] EXIGENCY: From server {0}. {1}. Exception message is {2}.
accountBinded=[Bees360] Per your request, we have linked this phone number with your Bees360 account.
# oldAccount, newAccount
accountChanged=[Bees360] The phone number associated with your account has been changed. The old number was {0}. The new number is {1}.
# imageNum, projectId, project address
imageUploadedSendToCreator=[Bees360] There are {0,number,#} images was uploaded to the project {1,number,#} at {2} in Bees360 platform.
# imageNum, projectId, project address
imageUploadedSendToStaffs=[Bees360] There are {0,number,#} images was uploaded to the project {1,number,#} at {2} on Bees360 platform.
# project num, projectIds join
imageUploadedStatusIsAutoSet=[Bees360] The status of {0,number,#} projects {1} are automatically changed to Image Uploaded.
# report name, projectId, project address
reportApproved=[Bees360] {0} for project {1,number,#} at {2} is approved. You can get access to it under Report tab on Bees360 platform. Thank you for your business!
# roles, review result
pilotRoleApplicationApproved=[Bees360] Your application for pilot role is approved.
roleApplicationResult=[Bees360] Your application for the new role(s) {0} is {1}.
# invitor, projectId, address
inviteAsVisitor=[Bees360] {0} invite you to project {1,number,#} at {2}.
# report, projectId, address
reportSubmitted=[Bees360] {0} for the project {1,number,#} at {2} has been submitted.
# report, projectId, address
reportDisapproved=[Bees360] {0} for project {1,number,#} at {2} is disapproved.
# reportName,projectId, address, username
adminRealtimeReport=[Bees360] The {0} for project {1,number,#} at {2} has been generated. Please assign Processor and Reviewer as soon as possible.
# reportName,projectId
processorRealtimeReport=[Bees360] The {0} for project {1,number,#} has been generated. Please review and submit this report as soon as possible.
# role,projectId,address
memberArranged=[Bees360] NEW JOB! You are assigned to be the {0} of the project {1,number,#} at {2}.
# projectId, address
memberArrangementCancel=[Bees360] Your Bees360 assignment for project {0,number,#} at {1} has been removed. Thank you.
# projectId, address
inspectionTimeChangedIsTimeFrom=[Bees360] The inspection time for your assignment for project {0,number,#} at {1} has been changed.
# projectId, address, fromTime, toTime
inspectionTimeChangedIsTimeToAndTimeFrom=[Bees360] The inspection time for your assignment for project {0,number,#} at {1} has been changed from {2} to {3}.
# address, toTime
inspectionTimeChangedIsTimeTo=[Bees360] The inspection time for your assignment for project {0,number,#} at {1} has been scheduled at {2}.
# projectId, address, username
projectDeleted=[Bees360] The project {0,number,#} at {1} has been deleted by {2}.
# projectId, address, username
sendToAdminToAsignPilot=[Bees360] {0} requests a Pilot for project {1,number,#} at {2}. Please assign the Pilot for the project as soon as possible.
# projectId, address, operatorName
requestCancel=[Bees360] The request of assigning a Pilot for Project {0,number,#} at {1} has been canceled by {2}.
# username, account, reports, projectId, address
orderTaskInitial-reports=[Bees360] {0} ({1}) requests {2} for project {3,number,#} at {4}.
# username, account, projectId, address
orderTaskInitial-pilot=[Bees360] {0} ({1}) requests a Pilot for project {2,number,#} at {3}.
# username, account, reports, projectId, address
orderTaskInitial-reports-pilot=[Bees360] {0} ({1}) requests {2} and a Pilot for project {3,number,#} at {4}.
# name, email, projectId, address
orderTasksChanged=[Bees360] {0} ({1}) changes the order of the project {2,number,#} at {3}.
userRoleLost=[Bees360] Your {0} role is deactivated.
sendADToHouseOwner= Your claim inspection has been scheduled
