package com.bees360.service.impl;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.SystemException;
import com.bees360.common.uri.URINameUtil;
import com.bees360.entity.Invoice;
import com.bees360.entity.InvoiceFile;
import com.bees360.entity.Member;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.InvoiceVo;
import com.bees360.mapper.InvoiceFileMapper;
import com.bees360.mapper.InvoiceMapper;
import com.bees360.mapper.MemberMapper;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.bees360.service.InvoiceService;
import com.bees360.util.FileManager;
import com.bees360.util.amazon.S3Link;
import com.bees360.util.httputil.ResponseFileUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Guanrong
 * @date 2019/09/25 14:40
 */
@Service
public class InvoiceServiceImpl implements InvoiceService {

    @Inject
    private InvoiceFileMapper invoiceFileMapper;

    @Inject
    private InvoiceMapper invoiceMapper;

    @Inject
    private MemberMapper memberMapper;

    @Autowired
    private ResourcePool resourcePool;

    /**
     * 将<code>Invoice</code>对象转化为<code>InvoiceVo</code>对象。
     */
    private InvoiceVo invoiceToVo(Invoice invoice, InvoiceFile invoiceFile) {
        if(invoice == null) {
            return null;
        }
        InvoiceVo invoiceVo = new InvoiceVo();
        invoiceVo.setInvoiceId(invoice.getInvoiceId());
        invoiceVo.setProjectId(invoice.getProjectId());
        invoiceVo.setTitle(invoice.getInvoiceTitle());
        invoiceVo.setCreateTime(invoice.getCreatedTime());
        if (Objects.nonNull(invoiceFile)) {
            invoiceVo.setResourceKey(invoiceFile.getInvoicePdfUrl());
        }
        return invoiceVo;
    }

    @Override
    public List<InvoiceVo> listInvoice(long userId, long projectId) throws ServiceException {
        List<Member> members = memberMapper.listAllByUser(projectId, userId);
        if (members.size() == 1 && members.get(0).getRole() == RoleEnum.PILOT.getCode()) {
            // 该用户为项目的pilot，且不担任其他任何角色
            return new ArrayList<>();
        }
        List<Invoice> invoices = invoiceMapper.listByProjectId(projectId);

        List<InvoiceVo> invoiceVos = new ArrayList<>(invoices.size());
        for(Invoice invoice: invoices) {
            InvoiceFile invoiceFile = invoiceFileMapper.getByProjectIdAndInvoiceId(projectId, invoice.getInvoiceId());
            InvoiceVo invoiceVo = invoiceToVo(invoice, invoiceFile);
            invoiceVos.add(invoiceVo);
        }
        return invoiceVos;
    }

    private File getTempDirToSaveInvoice(long projectId) {
        return new File(FileManager.getTempDirToSaveInvoice(projectId));
    }

    @Override
    public void fetchInvoiceFile(long projectId, HttpServletResponse response) {

        InvoiceFile invoiceFile = invoiceFileMapper.getInvoiceFileForProject(projectId);
        if(invoiceFile == null) {
            throw new ResourceNotFoundException();
        }

        File invoiceLocalFile = downloadInvoiceFile(invoiceFile);

        ResponseFileUtil.responseFileWithBase64(invoiceLocalFile, invoiceFile.getInvoicePdfUrl(), response);
    }

    @Override
    public void fetchInvoiceFile(long projectId, long invoiceId, HttpServletResponse response) {

        InvoiceFile invoiceFile = invoiceFileMapper.getByProjectIdAndInvoiceId(projectId, invoiceId);
        if(invoiceFile == null) {
            throw new ResourceNotFoundException();
        }

        File invoiceLocalFile = downloadInvoiceFile(invoiceFile);

        ResponseFileUtil.responseFileWithBase64(invoiceLocalFile, invoiceFile.getInvoicePdfUrl(), response);
    }

    /**
     * 从文件服务器现在Invoice文件，并返回下载到本地的文件对象。
     */
    private File downloadInvoiceFile(InvoiceFile invoiceFile) {
        String localFileName = URINameUtil.getUrlName(invoiceFile.getInvoicePdfUrl());
        File filePath = new File(getTempDirToSaveInvoice(invoiceFile.getProjectId()), localFileName);
        if(!filePath.exists()) {
            // 只有在本地没有的时候才进行下载
            try {
                String key = new S3Link(invoiceFile.getInvoicePdfUrl()).getKey();
                Resource resource =
                        resourcePool.get(key);
                if (resource == null) {
                    throw new IllegalStateException("The invoice file '%s' is not found.".formatted(key));
                }
                try(FileOutputStream outputStream = new FileOutputStream(filePath)) {
                    resource.writeTo(outputStream);
                }
            } catch (IOException e) {
                throw new SystemException(e.getMessage(), e);
            }
        }
        return filePath;
    }
}
