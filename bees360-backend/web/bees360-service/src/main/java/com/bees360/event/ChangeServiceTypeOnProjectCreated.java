package com.bees360.event;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.User;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.vo.request.ProjectServiceTypeParam;
import com.bees360.event.registry.ProjectCreate;
import com.bees360.event.util.AbstractNamedEventListener;

import com.bees360.mapper.ProjectMapper;
import com.bees360.service.ProjectService;
import com.bees360.service.properties.Bees360CompanyConfig;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;

/**
 * 监听项目创建事件，根据理赔备注和保险公司配置判断是否需要将服务类型更改为快速检查类型，并更新项目服务类型和记录活动日志。
 */
@Log4j2
public class ChangeServiceTypeOnProjectCreated
        extends AbstractNamedEventListener<ProjectCreate>{

    private final Bees360CompanyConfig bees360CompanyConfig;
    private final ProjectMapper projectMapper;
    private ProjectService projectService;
    private ActivityManager activityManager;

    public ChangeServiceTypeOnProjectCreated(
            @NonNull Bees360CompanyConfig bees360CompanyConfig,
            @NonNull ProjectMapper projectMapper,
            @NonNull ProjectService projectService,
            @NonNull ActivityManager activityManager) {
        this.bees360CompanyConfig = bees360CompanyConfig;
        this.projectMapper = projectMapper;
        this.projectService = projectService;
        this.activityManager = activityManager;
        log.info("Created '{}'", this);
    }

    @Override
    public void handle(ProjectCreate projectCreatedEvent) throws IOException {
        var projectId = Long.parseLong(projectCreatedEvent.getProject().getId());
        var project = projectMapper.getById(projectId);
        var claimNote = project.getClaimNote();
        var insuranceCompany = project.getInsuranceCompany();
        var serviceType = project.getServiceType();
        log.info(
                "The project {}`s claimNote {} is insuredBy {},which serviceType is{}",
                projectId,
                claimNote,
                insuranceCompany,
                ProjectServiceTypeEnum.getEnum(serviceType));
        var quickInspectNeeded = isQuickInspectCase(claimNote, insuranceCompany, serviceType);
        log.info("The project {} is quickInspectNeeded? {}", projectId, quickInspectNeeded);
        if (quickInspectNeeded) {
            // 将服务类型改为Quick Inspect
            updateServiceType(
                    projectId, serviceType, ProjectServiceTypeEnum.QUICK_INSPECT.getCode());
        }
    }

    private Boolean isQuickInspectCase(
            String claimNote, Long insuranceCompany, Integer serviceType) {
        if (Objects.isNull(insuranceCompany)
                || StringUtils.isEmpty(claimNote)
                || !ProjectServiceTypeEnum.equals(
                        serviceType, ProjectServiceTypeEnum.FULL_ADJUSTMENT)) {
            return false;
        }
        // 获取保险公司配置
        var configItem = bees360CompanyConfig.findConfig(insuranceCompany);
        var quickInspectPattern =
                Optional.ofNullable(configItem)
                        .map(Bees360CompanyConfig.CompanyConfigItem::getQuickInspectCaseFilterRegex)
                        .orElse(null);
        if (Objects.isNull(quickInspectPattern)) {
            return false;
        }
        Matcher quickInspectMatcher = quickInspectPattern.matcher(claimNote);
        return quickInspectMatcher.find();
    }

    private void updateServiceType(long projectId, int oldServiceType, int serviceType) {
        ProjectServiceTypeParam param = new ProjectServiceTypeParam();
        param.setProjectIds(Collections.singletonList(projectId));
        param.setServiceType(serviceType);
        var oldServiceTypeName = ProjectServiceTypeEnum.getEnum(oldServiceType).getDisplay();
        var serviceTypeName = ProjectServiceTypeEnum.getEnum(serviceType).getValue();
        try {
            projectService.updateProjectServiceType(User.AI_ID, projectId, param);
            activityManager.submitActivity(
                    Activity.from(
                            Message.ActivityMessage.newBuilder()
                                    .setProjectId(projectId)
                                    .setAction(Message.ActivityMessage.ActionType.CHANGE.name())
                                    .setEntity(
                                            Message.ActivityMessage.Entity.newBuilder()
                                                    .setType(
                                                            Message.ActivityMessage.EntityType
                                                                    .PROJECT
                                                                    .name())
                                                    .setId(String.valueOf(projectId))
                                                    .build())
                                    .setField(
                                            Message.ActivityMessage.Field.newBuilder()
                                                    .setType(
                                                            Message.ActivityMessage.FieldType.STRING
                                                                    .name())
                                                    .setName("SERVICE_TYPE")
                                                    .setValue(serviceTypeName)
                                                    .setOldValue(oldServiceTypeName)
                                                    .build())
                                    .setCreatedBy(
                                            com.bees360.user.Message.UserMessage.newBuilder()
                                                    .setId(String.valueOf(User.AI_ID))
                                                    .build())
                                    .setComment(
                                            Message.CommentMessage.newBuilder()
                                                    .setContent(
                                                            "Reason: Description of Loss and/or Instructions contain the 'Quick Inspect/Limited Inspect' keyword.")
                                                    .setProjectId(projectId)
                                                    .build())
                                    .setSource(ActivitySourceEnum.WEB.getValue())
                                    .build()));
        } catch (ServiceException e) {
            log.error(
                    "Failed to update project '{}' service type '{}'",
                    projectId,
                    ProjectServiceTypeEnum.getEnum(serviceType),
                    e);
        }
    }
}
