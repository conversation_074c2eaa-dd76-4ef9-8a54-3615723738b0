package com.bees360.service.job;

import com.bees360.job.registry.SerializableFirebaseHover;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseHoverService;
import com.google.cloud.firestore.Firestore;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;

import static com.bees360.service.job.FirebaseMissionChangedExecutor.translateExceptionAndThrow;

@Log4j2
@Component
@ToString
public class FirebaseHoverChangedExecutor extends AbstractJobExecutor<SerializableFirebaseHover> {

    private final FirebaseHoverService firebaseHoverService;
    private final Firestore firestore;

    public FirebaseHoverChangedExecutor(FirebaseHoverService firebaseHoverService, Firestore firestore) {
        this.firebaseHoverService = firebaseHoverService;
        this.firestore = firestore;

        log.info("Created '{}'", this);
    }

    @Override
    protected void handle(SerializableFirebaseHover hover) throws IOException {
        try {
            log.info("Start to handle hover '{}' '{}'", hover.getId(), hover);
            firebaseHoverService.handleHoverJob(hover, hover.getId());
            log.info("Successfully handle hover '{}'", hover.getId());
        } catch (RuntimeException e) {
            log.warn("Failed to handle hover '{}'.", hover, e);
            translateExceptionAndThrow(e);
        }
    }
}
