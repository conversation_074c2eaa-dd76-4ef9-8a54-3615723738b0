package com.bees360.event;

import com.bees360.entity.User;
import com.bees360.event.registry.CustomerContactedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.UserService;
import lombok.extern.log4j.Log4j2;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.Instant;
import java.util.Optional;

/**
 * 监听客户联络事件并更新项目状态和联络时间
 */
/** 联络客户触发事件监听器 */
@Log4j2
public class CustomerContactedEventListener extends AbstractNamedEventListener<CustomerContactedEvent> {

    private final UserService userService;

    private final ProjectStatusService projectStatusService;

    private final ProjectService projectService;

    public CustomerContactedEventListener(UserService userService,
                                          ProjectStatusService projectStatusService, ProjectService projectService) {
        this.userService = userService;
        this.projectStatusService = projectStatusService;
        this.projectService = projectService;
        log.info("Created '{}'", this);
    }

    @Override
    @Transactional
    public void handle(CustomerContactedEvent customerContactedEvent) throws IOException {
        log.info(
                "customer of projectId {} is contacted by user {}.",
                customerContactedEvent.getProjectId(),
                customerContactedEvent.getContactedBy());
        Long userId = userService.toWebUserId(customerContactedEvent.getContactedBy());
        userId = Optional.ofNullable(userId).orElse(User.BEES_PILOT_SYSTEM);
        var projectId = Long.parseLong(customerContactedEvent.getProjectId());
        var contactedTime = customerContactedEvent.getContactedAt();
        projectStatusService.changeOnCustomerContacted(userId, projectId, contactedTime);
        projectService.setCustomerContactedTime(projectId, Instant.ofEpochMilli(contactedTime));
    }
}
