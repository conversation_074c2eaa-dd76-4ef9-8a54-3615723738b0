package com.bees360.service.beespilot;

import com.bees360.entity.BeesPilotStatus;
import com.bees360.entity.enums.CheckStatusEnum;
import com.bees360.entity.enums.ImageUploadStatusEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.query.BeesPilotStatusQuery;

import java.util.List;

/**
 * 管理Bees Pilot各个状态
 * 包括：手机图片，无人机图片等的完成情况
 * 该服务不负责触发其它状态，其它状态触发由其调用者负责
 * <AUTHOR>
 * @since 2020/7/31 3:13 下午
 **/
public interface BeesPilotStatusService {
    /**
     * 获取Pilot 工作完成状态
     * @param projectId 项目ID
     * @return Bees Pilot工作完成状态
     */
    BeesPilotStatus getBeesPilotStatus(long projectId);

    /**
     * translate ProjectStatus into CheckStatus
     * @param projectId 项目ID
     * @param projectStatusEnum projectStatusEnum check status
     */
    BeesPilotStatus checkByProjectStatus(long projectId, ProjectStatusEnum projectStatusEnum);


    /**
     * drone check in or check out
     * image check in or check out
     * @param projectId 项目id
     * @param checkStatusEnum check status
     */
    BeesPilotStatus updateCheck(long projectId, CheckStatusEnum checkStatusEnum);

    /**
     * drone check in or check out
     * image check in or check out
     * @param projectId 项目id
     * @param preCheckoutReason beesPilot checkout reason in advanced
     */
    void beesPilotCheckout(long projectId, Integer preCheckoutReason);

    /**
     * quiz finished
     * @param projectId 项目ID
     * @param completed completed or not
     */
    BeesPilotStatus quizCompleted(long projectId, boolean completed);

    /**
     * 以增量形式修改 image upload status
     * 如果 <code>status</code> equals {@link ImageUploadStatusEnum#getNeedAllImageUpdateStatus(com.bees360.entity.CompanyIDMap, com.bees360.entity.Project)},
     * {@link ProjectStatusEnum#IMAGE_UPLOADED} should be triggered.
     * @param userId
     * @param projectId 项目ID
     * @param status 图片上传状态
     */
    BeesPilotStatus addImageUploadStatus(long userId, long projectId, int status);

    /**
     * address verified
     *
     * @param projectId 项目ID
     * @param verified  verified address
     */
    BeesPilotStatus addressVerified(long projectId, boolean verified);

    /**
     * pilot finished all its job
     *
     * @param projectId project
     */
    BeesPilotStatus pilotCompleted(long projectId);

    void resetBeespilotStatus(long projectId);

    void initStatus(long projectId);

    int getNeedAllImageUpdateStatus(long projectId);

    boolean isFinishedUpload(long projectId, int status);
}
