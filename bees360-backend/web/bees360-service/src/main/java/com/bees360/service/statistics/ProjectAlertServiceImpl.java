package com.bees360.service.statistics;

import com.bees360.entity.Company;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectQuiz;
import com.bees360.entity.ProjectScore;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.mapper.CompanyMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.ProjectQuizMapper;
import com.bees360.mapper.ProjectScoreMapper;
import com.bees360.mapper.ProjectStatusMapper;
import com.bees360.mapper.ReportSummaryMapper;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.service.ReportSummaryService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.Iterables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class ProjectAlertServiceImpl implements ProjectAlertService {

    private final ProjectMapper projectMapper;

    private final ProjectStatusMapper projectStatusMapper;

    private final ProjectScoreMapper projectScoreMapper;

    private final ReportSummaryMapper reportSummaryMapper;

    private final CompanyMapper companyMapper;

    private final ProjectQuizMapper projectQuizMapper;

    private ProjectReportProvider projectReportProvider;

    private Bees360FeatureSwitch bees360FeatureSwitch;

    private static final int QUIZ_ID_BUILDING_OCCUPANCY = 7;

    private static final int QUIZ_ID_OCCUPANCY_REASON = 2005;

    private static final String QUIZ_ANSWER_OCCUPANCY_VACANT = "VACANT";
    private static final int ALERT_SCORE_THRESHOLD = 4;
    private static final String SCORED_ALERT_HAZARD_NOTICED = "Alerted risk score";

    private static final String WORD_SPACE = "[\\s\\w\\(\\)]*";
    private static final String HEAD = ".*[\".](?<COMMENT>.*(";
    private static final String TAIL = ").*?)[\".].*";

    // Roof shingle(s) appears to show damage from tree branches
    private static final Pattern SHINGLE_DAMAGE_FROM_TREE_BRANCH =
            Pattern.compile(
                    HEAD + String.join(WORD_SPACE, "shingle", "damage", "tree", "branch") + TAIL,
                    Pattern.CASE_INSENSITIVE);
    // Possible storm damage on roof was noted
    private static final Pattern STORM_DAMAGE_ROOF =
            Pattern.compile(
                    HEAD + String.join(WORD_SPACE, "storm", "damage", "roof") + TAIL,
                    Pattern.CASE_INSENSITIVE);
    // Possible hail or impact damage was noted
    private static final Pattern HAIL_DAMAGE =
            Pattern.compile(
                    HEAD + String.join(WORD_SPACE, "hail", "damage") + TAIL,
                    Pattern.CASE_INSENSITIVE);
    // Roof is tarred on (Front/Right/Rear/Left) slope(s)
    private static final Pattern ROOF_TARRED_SLOPE =
            Pattern.compile(
                    HEAD + String.join(WORD_SPACE, "roof", "tarred", "slope") + TAIL,
                    Pattern.CASE_INSENSITIVE);
    // Roof appears to have been repaired and/or patched
    private static final Pattern ROOF_PATCHED_REPAIRED =
            Pattern.compile(
                    HEAD + String.join(WORD_SPACE, "roof", "repaired", "and/or", "patched") + TAIL,
                    Pattern.CASE_INSENSITIVE);
    // Broken window glass was noted
    private static final Pattern BROKEN_WINDOW_GLASS =
            Pattern.compile(
                    HEAD + String.join(WORD_SPACE, "broken", "window", "glass") + TAIL,
                    Pattern.CASE_INSENSITIVE);
    // Electric panel was noted in (Excellent/Good/Average/Fair/Poor) condition, with without
    // eligible panel
    private static final Pattern ELECTRIC_PANEL =
            Pattern.compile(
                    HEAD + String.join(WORD_SPACE, "electric", "panel", "ineligible") + TAIL,
                    Pattern.CASE_INSENSITIVE);
    // Boarded windows/doors were noted on (Front/Right/Rear/Left) elevation.
    private static final Pattern BOARDED_WINDOW_DOOR_ELEVATION =
            Pattern.compile(
                    HEAD + String.join(WORD_SPACE, "boarded", "windows/doors", "elevation") + TAIL,
                    Pattern.CASE_INSENSITIVE);
    // Debris was noted in the yard. It is recommended to remove and clean the debris as soon as
    // possible
    private static final Pattern DEBRIS_YARD =
            Pattern.compile(
                    HEAD + String.join(WORD_SPACE, "debris", "yard") + TAIL,
                    Pattern.CASE_INSENSITIVE);
    // Mold was noted at the time of inspection
    private static final Pattern MOLD_NOTED =
            Pattern.compile(
                    HEAD + String.join(WORD_SPACE, "mold") + TAIL, Pattern.CASE_INSENSITIVE);

    public ProjectAlertServiceImpl(
            ProjectMapper projectMapper,
            ProjectStatusMapper projectStatusMapper,
            ProjectScoreMapper projectScoreMapper,
            ReportSummaryMapper reportSummaryMapper,
            CompanyMapper companyMapper,
            ProjectQuizMapper projectQuizMapper,
            @Qualifier("uniqueProjectReportProvider") ProjectReportProvider uniqueProjectReportProvider,
            Bees360FeatureSwitch bees360FeatureSwitch) {
        this.projectMapper = projectMapper;
        this.projectStatusMapper = projectStatusMapper;
        this.projectScoreMapper = projectScoreMapper;
        this.reportSummaryMapper = reportSummaryMapper;
        this.companyMapper = companyMapper;
        this.projectQuizMapper = projectQuizMapper;
        this.projectReportProvider = uniqueProjectReportProvider;
        this.bees360FeatureSwitch = bees360FeatureSwitch;
        log.info(
            "Created {}(projectMapper={}, projectStatusMapper={}, projectScoreMapper={}, reportSummaryMapper={}" +
                ", companyMapper={}, projectQuizMapper={}, projectReportProvider={}, bees360FeatureSwitch={}).",
            this,
            this.projectMapper,
            this.projectStatusMapper,
            this.projectScoreMapper,
            this.reportSummaryMapper,
            this.companyMapper,
            this.projectQuizMapper,
            this.projectReportProvider,
            this.bees360FeatureSwitch);
    }

    @Override
    public List<SwyfftUnderwritingAlert> getSwyfftUnderwritingAlert(
            Instant startTime, Instant endTime) {

        List<Project> clientReceived =
                getProjectsToBeChecked(NewProjectStatusEnum.CLIENT_RECEIVED, startTime, endTime);
        List<Project> returnedToClient =
                getProjectsToBeChecked(NewProjectStatusEnum.RETURNED_TO_CLIENT, startTime, endTime);
        // all project of time interval to be checked
        Map<Long, Project> projectIdToProject =
                Stream.concat(clientReceived.stream(), returnedToClient.stream())
                        .collect(Collectors.toMap(Project::getProjectId, Function.identity()));

        if (projectIdToProject.isEmpty()) {
            return Collections.emptyList();
        }
        ArrayList<Long> projectIdsToBeChecked = new ArrayList<>(projectIdToProject.keySet());
        // query the vacant status of projects
        Map<Long, String> projectIdToOccupancy =
                projectQuizMapper
                        .listLatestAnswersOfQuizType(
                                projectIdsToBeChecked, QUIZ_ID_BUILDING_OCCUPANCY)
                        .stream()
                        .filter(projectQuiz -> projectQuiz.getAnswer() != null)
                        .collect(
                                Collectors.toMap(
                                        ProjectQuiz::getProjectId, ProjectQuiz::getAnswer));
        Map<Long, String> projectIdToOccupancyReason =
                projectQuizMapper
                        .listLatestAnswersOfQuizType(
                                projectIdsToBeChecked, QUIZ_ID_OCCUPANCY_REASON)
                        .stream()
                        .filter(projectQuiz -> projectQuiz.getAnswer() != null)
                        .collect(
                                Collectors.toMap(
                                        ProjectQuiz::getProjectId, ProjectQuiz::getAnswer));

        // select project risk score of all projects to be checked
        Map<Long, Integer> projectIdToScore =
                projectScoreMapper.listByProjectIds(projectIdsToBeChecked).stream()
                        .collect(
                                Collectors.toMap(
                                        ProjectScore::getProjectId,
                                        projectScore -> projectScore.getScore().intValue()));
        // calculate projects whose risk score is above the threshold
        List<SwyfftUnderwritingAlert> scoredAlerts =
                projectIdToScore.entrySet().stream()
                        .filter(entry -> entry.getValue() >= ALERT_SCORE_THRESHOLD)
                        .map(
                                entry -> {
                                    Project project = projectIdToProject.remove(entry.getKey());
                                    if (project == null) {
                                        return null;
                                    }
                                    return new SwyfftUnderwritingAlert(
                                            project.getProjectId() + "",
                                            project.getPolicyNumber(),
                                            project.getInspectionNumber(),
                                            project.getAssetOwnerName(),
                                            project.getState(),
                                            entry.getValue(),
                                            projectIdToOccupancy.remove(project.getProjectId()),
                                            StringUtils.trimToEmpty(
                                                    projectIdToOccupancyReason.get(
                                                            project.getProjectId())),
                                            SCORED_ALERT_HAZARD_NOTICED);
                                })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
        // extract alerted comments of projects
        Map<Long, String> projectIdToComment =
                commentedAlert(new ArrayList<>(projectIdToProject.keySet()));
        log.info("commentedAlert size {} comment {}", projectIdToComment.size(), projectIdToComment);

        List<SwyfftUnderwritingAlert> commentedAlert =
                projectIdToComment.entrySet().stream()
                        .map(
                                entry -> {
                                    Project project = projectIdToProject.remove(entry.getKey());
                                    Integer score = projectIdToScore.get(project.getProjectId());
                                    return new SwyfftUnderwritingAlert(
                                            project.getProjectId() + "",
                                            project.getPolicyNumber(),
                                            project.getInspectionNumber(),
                                            project.getAssetOwnerName(),
                                            project.getState(),
                                            score,
                                            projectIdToOccupancy.remove(project.getProjectId()),
                                            StringUtils.trimToEmpty(
                                                    projectIdToOccupancyReason.get(
                                                            project.getProjectId())),
                                            entry.getValue());
                                })
                        .collect(Collectors.toList());

        List<Long> projectIdsOfTheVacant =
                projectIdToOccupancy.entrySet().stream()
                        .filter(
                                entry ->
                                        StringUtils.equals(
                                                QUIZ_ANSWER_OCCUPANCY_VACANT, entry.getValue()))
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());
        List<SwyfftUnderwritingAlert> vacantAlert =
                projectIdsOfTheVacant.stream()
                        .map(
                                projectId -> {
                                    Project project = projectIdToProject.get(projectId);
                                    Integer score = projectIdToScore.get(projectId);
                                    return new SwyfftUnderwritingAlert(
                                            projectId + "",
                                            project.getPolicyNumber(),
                                            project.getInspectionNumber(),
                                            project.getAssetOwnerName(),
                                            project.getState(),
                                            score,
                                            QUIZ_ANSWER_OCCUPANCY_VACANT,
                                            StringUtils.trimToEmpty(
                                                    projectIdToOccupancyReason.get(
                                                            project.getProjectId())),
                                            SCORED_ALERT_HAZARD_NOTICED);
                                })
                        .collect(Collectors.toList());

        LinkedList<SwyfftUnderwritingAlert> list = new LinkedList<>();
        list.addAll(scoredAlerts);
        list.addAll(commentedAlert);
        list.addAll(vacantAlert);
        scoredAlerts.addAll(commentedAlert);
        scoredAlerts.addAll(vacantAlert);
        return list;
    }

    private List<Project> getProjectsToBeChecked(
            NewProjectStatusEnum status, Instant start, Instant end) {
        Company SwyfftUnderwriting = companyMapper.getByName("Swyfft Underwriting");
        if (SwyfftUnderwriting == null) {
            throw new IllegalStateException("No company is found with name of Swyfft Underwriting");
        }
        List<Long> projectIds =
                projectStatusMapper.getProjectsByStatusTime(
                        status.getCode(), start.toEpochMilli(), end.toEpochMilli());
        if (projectIds.isEmpty()) {
            return Collections.emptyList();
        }
        return projectMapper.listProjects(projectIds).stream()
                .filter(project -> project.getProjectStatus() == status.getCode())
                .filter(
                        project ->
                                ProjectServiceTypeEnum.getUnderwritingCodes()
                                        .contains(project.getServiceType()))
                .filter(
                        project ->
                                Objects.equals(
                                        SwyfftUnderwriting.getCompanyId(),
                                        project.getInsuranceCompany()))
                .collect(Collectors.toList());
    }

    private Map<Long, String> commentedAlert(List<Long> projectIds) {
        if (projectIds.isEmpty()) {
            return Collections.emptyMap();
        }
        if (bees360FeatureSwitch.isEnableNewReportQueryWhenGetProjectAlert()) {
            try {
                return commentedAlertWithNewReportQuery(projectIds);
            } catch (Exception e) {
                log.error("Fail to get commented alert with new report query, fallback to old query.", e);
            }
        }

        HashMap<Long, String> map = new HashMap<>();
        reportSummaryMapper.listByProjectIds(projectIds).stream()
                .filter(reportSummary -> StringUtils.isNotBlank(reportSummary.getSummary()))
                .forEach(
                        reportSummary -> {
                            String comment = extractAlertComment(reportSummary.getSummary());
                            log.trace("project {} reportType {} comment '{}'", reportSummary.getProjectId(), reportSummary.getReportType(), comment);
                            if (comment != null) {
                                map.put(reportSummary.getProjectId(), comment);
                            }
                        });
        return map;
    }

    private HashMap<Long, String> commentedAlertWithNewReportQuery(List<Long> projectIds) {
        HashMap<Long, String> map = new HashMap<>();
        var projectIdStrList = projectIds.stream().map(String::valueOf).collect(Collectors.toList());
        log.info("Need to query reports for projects(size={}) {}", projectIdStrList.size(), projectIdStrList);
        var projectReportMap = projectReportProvider.findByProjectIdsWithoutResources(projectIdStrList);
        log.info("Got reports for projects(size={}) {}", projectReportMap.size(), projectReportMap.keySet());
        projectReportMap.forEach(
            (projectId, projectReports) -> {
                Iterables.toStream(projectReports)
                    .filter(report -> StringUtils.isNotBlank(report.getSummary().getSummary()))
                    .filter(report -> !ReportSummaryService.NULL_SUMMARY.equals(report.getSummary().getSummary()))
                    .forEach(
                        report -> {
                            String comment = extractAlertComment(report.getSummary().getSummary());
                            log.trace("project {} reportType {} comment '{}'", projectId, report.getType(), comment);
                            if (comment != null) {
                                map.put(Long.parseLong(projectId), comment);
                            }
                        });
            });
        return map;
    }

    public static String extractAlertComment(String summary) {
        List<Pattern> patternList =
                List.of(ELECTRIC_PANEL, BOARDED_WINDOW_DOOR_ELEVATION, DEBRIS_YARD, MOLD_NOTED);
        for (Pattern pattern : patternList) {
            Matcher matcher = pattern.matcher(summary);
            if (matcher.matches()) {
                return matcher.group("COMMENT")
                        .strip()
                        .replaceAll("(\\\\n)+", " ")
                        .replaceAll("\\s+", " ");
            }
        }
        return null;
    }
}
