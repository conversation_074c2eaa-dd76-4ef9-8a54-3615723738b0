package com.bees360.service.retry;

import com.bees360.base.MessageCode;
import com.bees360.base.code.ApiErrorType;
import com.bees360.base.exception.ServiceException;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.RetryContext;
import org.springframework.retry.policy.SimpleRetryPolicy;

/**
 * 批量生成project的重试策略，判断Exceptions是否符合retry的要求
 *
 * <AUTHOR>
 */
@Slf4j
public class CreateBeeProjectRetryPolicy extends SimpleRetryPolicy {

    public CreateBeeProjectRetryPolicy(int maxAttempts, Map<Class<? extends Throwable>, Boolean> retryableExceptions) {
        super(maxAttempts, retryableExceptions, false);
    }

    private boolean checkException(RetryContext context) {
        boolean res = false;
        Throwable t = context.getLastThrowable();
        if (t instanceof ServiceException exception) {
            String messageCode = exception.getMsgCode();
            ApiErrorType errorType = MessageCode.getErrorType(messageCode);
            if (!Objects.equals(errorType, ApiErrorType.FAILED_PRECONDITION)
                && !Objects.equals(errorType, ApiErrorType.BAD_REQUEST)
                && !Objects.equals(errorType, ApiErrorType.INVALID_ARGUMENT)) {
                //不是由于传参引起的异常，重试
                res = true;
            }
        }
        return res;
    }

    @Override
    public boolean canRetry(RetryContext context) {
        Throwable lastThrowable = context.getLastThrowable();
        if (lastThrowable == null) {
            return super.canRetry(context);
        } else {
            return super.canRetry(context) && checkException(context);
        }
    }
}
