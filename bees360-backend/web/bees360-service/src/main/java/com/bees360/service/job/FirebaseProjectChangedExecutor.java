package com.bees360.service.job;

import com.bees360.job.AsyncJobExecutor;
import com.bees360.job.registry.SerializableFirebaseProject;
import com.bees360.job.util.AbstractAsyncJobExecutor;
import com.bees360.service.firebase.FirebaseProjectService;
import com.google.cloud.firestore.Firestore;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.concurrent.Executors;

import static com.bees360.service.job.FirebaseMissionChangedExecutor.translateExceptionAndThrow;

@Log4j2
@ToString
@Component
public class FirebaseProjectChangedExecutor
        extends AbstractAsyncJobExecutor<SerializableFirebaseProject>
        implements AsyncJobExecutor {
    private final FirebaseProjectService firebaseProjectService;
    private final Firestore firestore;
    private final ListeningExecutorService executor;

    public FirebaseProjectChangedExecutor(
            FirebaseProjectService firebaseMissionService, Firestore firestore) {
        this.firebaseProjectService = firebaseMissionService;
        this.firestore = firestore;
        this.executor =
                MoreExecutors.listeningDecorator(
                        Executors.newFixedThreadPool(
                                2 * Runtime.getRuntime().availableProcessors()));
        log.info("Created '{}'", this);
    }

    @Override
    protected ListenableFuture<Void> accept(SerializableFirebaseProject project) {
        return executor.submit(() -> run(project));
    }

    private Void run(SerializableFirebaseProject project) {

        try {
            log.info("Start to handle project '{}' '{}'", project.getId(), project);
            firebaseProjectService.handleFirebaseProject(project, project.getId());
            log.info("Successfully handle project '{}'", project.getId());
        } catch (RuntimeException e) {
            log.error("Failed to handle project '{}'", project, e);
            try {
                translateExceptionAndThrow(e);
            } catch (IOException ioException) {
                throw new UncheckedIOException(ioException);
            }
        }
        return null;
    }
}
