package com.bees360.service.firebase;

import com.bees360.commons.firebasesupport.entity.DocumentData;
import com.bees360.entity.User;
import com.bees360.job.registry.SerializableFirebasePilot;
import java.util.concurrent.ExecutionException;

public interface FirebasePilotService {

    String PILOT_COLLECTION = "pilot";

    /**
     * 根据email查询pilotDoc
     * @param email 邮箱
     * @return pilot Doc
     */
    DocumentData<SerializableFirebasePilot> getDocByEmail(String email);

    /**
     * 根据Phone查询pilotDoc
     *
     * @param phone 手机
     * @return pilot Doc
     */
    DocumentData<SerializableFirebasePilot> getDocByPhone(String phone);

    /**
     * 同步User数据到pilot
     * @param user
     */
    void syncPilot(User user) throws ExecutionException, InterruptedException;

    void syncPilotRemoval(User user) ;
}
