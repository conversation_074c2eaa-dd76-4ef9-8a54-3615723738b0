package com.bees360.event;

import com.bees360.activity.Activities;
import com.bees360.activity.ActivityManager;
import com.bees360.address.AddressManager;
import com.bees360.entity.User;
import com.bees360.event.registry.ProjectAddressFormattedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听项目地址格式化事件并处理，更新项目地址信息到活动管理器。
 */
@Log4j2
public class ProjectAddressFormattedEventListener
        extends AbstractNamedEventListener<ProjectAddressFormattedEvent> {
    private final ActivityManager activityManager;
    private final AddressManager addressManager;

    public ProjectAddressFormattedEventListener(ActivityManager activityManager, AddressManager addressManager) {
        this.activityManager = activityManager;
        this.addressManager = addressManager;
        log.info("Created :{} with activityManager :{}", this, activityManager);
    }

    @Override
    public void handle(ProjectAddressFormattedEvent event) throws IOException {
        log.info("Start to handle project address formatted event :{}", event);
        com.bees360.activity.Message.ActivityMessage.Field.Builder builder =
                com.bees360.activity.Message.ActivityMessage.Field.newBuilder()
                        .setName(
                                com.bees360.activity.Message.ActivityMessage.FieldName.ADDRESS
                                        .name())
                        .setType(
                                com.bees360.activity.Message.ActivityMessage.FieldType.STRING
                                        .name());

        var formattedAddress= addressManager.findById(event.getFormattedAddressId());
        builder.setValue(formattedAddress.getAddress());
        builder.setOldValue(event.getOriginalAddress());
        var smartyFormatActivity =
                Activities.changeProjectField(
                        event.getProjectId(), String.valueOf(User.AI_ID), builder.build());
        activityManager.submitActivity(smartyFormatActivity);
        log.info("Manager to submit project address formatted activity :{}", builder.build());
    }
}
