package com.bees360.user;


import co.realms9.bifrost.Namespace;
import co.realms9.bifrost.RoleAssignmentManager;
import co.realms9.bifrost.RoleManager;
import co.realms9.bifrost.UserManager;
import co.realms9.bifrost.UserQuery;
import co.realms9.bifrost.user.Message;
import com.bees360.entity.User;
import com.bees360.util.Functions;
import com.bees360.util.Iterables;
import com.bees360.util.ProtoStructAdapter;
import com.bees360.util.user.UserAssemble;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Value;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * {@link
 * <p>当该适配器开关打开后,创建web用户时会用 bifrost 系统创建的用户 id，这么做将保证 web系统用户和 bifrost用户 id 统一。当开关未开启时则不会有此特性。
 * 该适配器功能开关打开必要条件，为 web用户都迁移到了 bifrost且 id 相同。
 */
@Log4j2
public class BifrostUserMapperAdapter {
    private final UserManager bifrostUserManager;
    private final UserProvider userProvider;
    private final RoleManager roleManager;
    private final RoleAssignmentManager roleAssignmentManager;

    private static final String COMPANY_ID = "company_id";
    private static final String BUILD_IN_USER_POOL_ID = "1";
    private static final String WEB_APP_ID = "2";
    static final Namespace WEB_NAMESPACE = Namespace.from(BUILD_IN_USER_POOL_ID, WEB_APP_ID);

    public BifrostUserMapperAdapter(
        UserManager bifrostUserManager,
        UserProvider userProvider,
        RoleManager roleManager,
        RoleAssignmentManager roleAssignmentManager) {
        this.bifrostUserManager = bifrostUserManager;
        this.userProvider = userProvider;
        this.roleManager = roleManager;
        this.roleAssignmentManager = roleAssignmentManager;

        log.info(
                "Created '{}'(bifrostUserManager={}, userService={}, roleManager={}, roleAssignmentManager={})",
                this,
                this.bifrostUserManager,
                this.roleManager,
                this.roleAssignmentManager);
    }

    public List<User> getUserByCompanyId(Long companyId, Boolean isDisabled) {
        var requestBuilder = Message.UserQueryRequest.newBuilder()
            .setNamespace(WEB_NAMESPACE.toMessage());
        if (companyId != null && companyId != 0) {
            requestBuilder.setCustomAttributeFilter(ProtoStructAdapter.mapToStruct(Map.of(COMPANY_ID, "" + companyId)));
        }
        if (isDisabled != null) {
            requestBuilder.setDisabled(BoolValue.newBuilder().setValue(isDisabled));
        }
        var query = UserQuery.from(requestBuilder.build());
        var users = bifrostUserManager.findAndCountByQuery(query);
        var userIds = Iterables.transform(users.getUsers(), co.realms9.bifrost.User::getId);
        return Iterables.toStream(userProvider.findUserById(userIds)).map(UserAssemble::toWebUser).collect(Collectors.toList());
    }

    /**
     *  Set additional <code>is_sso_user</code>、<code>user_mfa_settings</code> for <code>User</code>
     * @param webUserId web user id
     * @return User
     */
    public User getUserById(long webUserId) {
        var bifrostUserId = String.valueOf(webUserId);
        var bifrostUser = bifrostUserManager.getUserById(bifrostUserId);

        if (bifrostUser.isExternalUser()) {
            return buildWebUser(bifrostUser, null);
        } else {
            var mfaSettings = new User.UserMFASettings();
            var settings = bifrostUserManager.getUserMFASettings(bifrostUserId);
            mfaSettings.setMfaSettingList(
                settings.getMfaSettingList().stream()
                    .map(Message.MFASettings.MFAType::name)
                    .collect(Collectors.toList()));
            if (!Objects.equals(settings.getPreferredMfa(), Message.MFASettings.MFAType.NONE)) {
                mfaSettings.setPreferredMfa(settings.getPreferredMfa().name());
            }
            if (settings.hasEmailVerified()) {
                mfaSettings.setEmailVerified(settings.getEmailVerified().getValue());
            }
            if (settings.hasPhoneNumberVerified()) {
                mfaSettings.setPhoneNumberVerified(settings.getPhoneNumberVerified().getValue());
            }
            return buildWebUser(bifrostUser, mfaSettings);
        }
    }

    public Iterable<User> getUserById(Iterable<Long> userIds) {
        var userStrIds = Iterables.transform(userIds, String::valueOf);
        var users = bifrostUserManager.findUserById(userStrIds);
        return Iterables.transform(users, u -> buildWebUser(u, null));
    }

    private User buildWebUser(co.realms9.bifrost.User bifrostUser, User.UserMFASettings mfaSettings) {
        var user = new User();
        var nameParts = splitName(bifrostUser.getName());
        user.setIsSsoUser(bifrostUser.isExternalUser());

        Functions.acceptIfNotNull(user::setUserId, bifrostUser.getId(), Long::parseLong);
        Functions.acceptIfNotNull(user::setFirstName, nameParts.getKey());
        Functions.acceptIfNotNull(user::setLastName, nameParts.getValue());
        Functions.acceptIfNotNull(user::setEmail, bifrostUser.getEmail());
        Functions.acceptIfNotNull(user::setPhone, bifrostUser.getPhone());
        Functions.acceptIfNotNull(user::setAvatar, bifrostUser.getPhotoUrl(), URL::toString);
        Functions.acceptIfNotNull(user::setUserMfaSettings, mfaSettings);
        var customAttribute = bifrostUser.getCustomAttribute();
        var companyId =
            ProtoStructAdapter.jsonToStruct(customAttribute)
                .getFieldsOrDefault(COMPANY_ID, Value.getDefaultInstance());
        if (StringUtils.isNotEmpty(companyId.getStringValue()) && StringUtils.isNumeric(companyId.getStringValue())) {
            Functions.acceptIfNotNull(
                user::setCompanyId, companyId, v -> Long.parseLong(v.getStringValue()));
        }
        return user;
    }

    private Pair<String, String> splitName(String name) {
        var index = name.lastIndexOf(" ");
        return index == -1
                ? Pair.of(name, "")
                : Pair.of(name.substring(0, index), name.substring(index + 1));
    }
}
