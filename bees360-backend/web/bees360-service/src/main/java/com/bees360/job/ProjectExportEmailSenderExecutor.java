package com.bees360.job;

import com.bees360.job.registry.IntervalProjectExportEmail;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.email.ProjectDataEmailSender;

import java.io.IOException;
import java.time.Instant;
import java.util.List;

public class ProjectExportEmailSenderExecutor extends AbstractJobExecutor<IntervalProjectExportEmail> {

    private final ProjectDataEmailSender projectDataEmailSender;

    public ProjectExportEmailSenderExecutor(ProjectDataEmailSender projectDataEmailSender) {
        this.projectDataEmailSender = projectDataEmailSender;
    }

    @Override
    protected void handle(IntervalProjectExportEmail intervalProjectExportEmail) throws IOException {
        Instant startTime = intervalProjectExportEmail.getStartTime();
        Instant endTime = intervalProjectExportEmail.getEndTime();
        IntervalProjectExportEmail.Interval interval = intervalProjectExportEmail.getInterval();
        List<String> recipients = intervalProjectExportEmail.getRecipients();
        projectDataEmailSender.sendEmailOfProjectDataWithinInterval(startTime, endTime, interval, recipients);
    }
}
