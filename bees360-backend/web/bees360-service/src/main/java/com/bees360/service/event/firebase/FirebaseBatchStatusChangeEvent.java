package com.bees360.service.event.firebase;

import com.bees360.entity.firebase.FirebasePilotBatch;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;
import org.springframework.lang.NonNull;

/**
 * batch status改变，Accepted 或者 Rejected.
 * @date 2020/08/27 14:18
 */
public class FirebaseBatchStatusChangeEvent extends ApplicationEvent {
    @Getter
    final FirebasePilotBatch batch;
    @Getter
    final String batchNo;
    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public FirebaseBatchStatusChangeEvent(@NonNull Object source, @NonNull FirebasePilotBatch batch, @NonNull String batchNo) {
        super(source);
        this.batch = batch;
        this.batchNo = batchNo;
    }
}
