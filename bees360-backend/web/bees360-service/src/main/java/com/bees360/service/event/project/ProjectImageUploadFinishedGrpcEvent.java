package com.bees360.service.event.project;

import com.bees360.service.event.Bees360Event;

public class ProjectImageUploadFinishedGrpcEvent extends Bees360Event {
    private long projectId;

    private boolean imageSyncFlag;

    /**
     * 同步节点
     */
    private String syncPoint;

    public ProjectImageUploadFinishedGrpcEvent(Object source, long projectId) {
        super(source);
        this.projectId = projectId;
    }

    public ProjectImageUploadFinishedGrpcEvent(Object source, long projectId, boolean imageSyncFlag) {
        super(source);
        this.projectId = projectId;
        this.imageSyncFlag = imageSyncFlag;
    }

    public ProjectImageUploadFinishedGrpcEvent(Object source, long projectId, boolean imageSyncFlag, String syncPoint) {
        super(source);
        this.projectId = projectId;
        this.imageSyncFlag = imageSyncFlag;
        this.syncPoint = syncPoint;
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }

    public long getProjectId() {
        return this.projectId;
    }

    public boolean isImageSyncFlag() {
        return imageSyncFlag;
    }

    public String getSyncPoint() {
        return syncPoint;
    }
}
