package com.bees360.service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.dto.DataDictionary;
import com.bees360.entity.dto.DataDictionaryDto;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/8/18 12:13 下午
 **/
public interface DataDictionaryService {
    DataDictionary getByNamespaceAndCode(String namespace, String code);

    List<DataDictionaryDto> listDataDictionary(String namespace);

    List<DataDictionary> listByNamespace(String namespace);

    void addDataDictionary(DataDictionary dataDictionary);

    void updateOrAdd(DataDictionary dataDictionary) throws ServiceException;
}
