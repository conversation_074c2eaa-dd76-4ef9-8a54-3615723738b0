package com.bees360.service.impl;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.stereotype.Service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Product;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.enums.productandpayment.PriceStatusEnum;
import com.bees360.entity.enums.productandpayment.PriceTypeEnum;
import com.bees360.entity.enums.productandpayment.ProductTypeEnum;
import com.bees360.entity.vo.ProductAssemble;
import com.bees360.service.ProductService;
import com.bees360.service.ProjectProductService;
import com.bees360.service.ProjectReportService;
import com.bees360.util.payment.ProductStore;

@Service
public class ProjectProductServiceImpl implements ProjectProductService {

	@Inject
	private ProductService productService;

	@Inject
	private ProjectReportService projectReportService;

	@Override
	public List<ProductAssemble> getProductAssemble(long projectId) throws ServiceException {
		List<ProductAssemble> productAssembles = new ArrayList<ProductAssemble>();

		List<Product> products = productService.listAllProducts();
		ProductStore productStore = new ProductStore(products);
		List<ProjectReportFile> unpaidReports = projectReportService.listUnpaidReports(projectId);

		for(ProjectReportFile r: unpaidReports) {
			Product p = productStore.getByType(ProductTypeEnum.REPORT, r.getReportType());
			if(p == null) {
				continue;
			}
			productAssembles.add(assembleProduct(p));
		}

		return productAssembles;
	}

	private ProductAssemble assembleProduct(Product product) {
		ProductAssemble p = new ProductAssemble();
		p.setProductId(product.getProductId());
		p.setProductType(product.getProductType());
		p.setInternalType(product.getInternalType());
		p.setProductName(product.getProductName());
		p.setPriceType(product.getPriceType());
		p.setPrice(product.getPrice());
		p.setCaption(product.getCaption());
		p.setUrl(product.getUrl());

		PriceTypeEnum priceType = PriceTypeEnum.getEnum(product.getPriceType());

		if(priceType == PriceTypeEnum.FIXED_PRICE) {
			p.setPriceStatus(PriceStatusEnum.CERTAIN.getCode());
		} else {
			p.setPriceStatus(PriceStatusEnum.UNCERTAIN.getCode());
		}
		return p;
	}
}
