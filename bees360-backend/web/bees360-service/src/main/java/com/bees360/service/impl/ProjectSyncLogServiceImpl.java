package com.bees360.service.impl;

import com.bees360.entity.ProjectSyncLog;
import com.bees360.mapper.ProjectSyncLogMapper;
import com.bees360.service.ProjectSyncLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/12/10 11:12
 */
@Service
public class ProjectSyncLogServiceImpl implements ProjectSyncLogService {

    @Autowired
    private ProjectSyncLogMapper projectSyncLogMapper;

    @Override
    public void insert(ProjectSyncLog log) {
        projectSyncLogMapper.insert(log);
    }

    @Override
    public boolean existedSync(Long projectId) {
        return projectSyncLogMapper.existedSync(projectId, null);
    }

    @Override
    public boolean existedSync(Long projectId, String point) {
        return projectSyncLogMapper.existedSync(projectId, point);
    }

    @Override
    public boolean successSync(Long projectId, String point) {
        return projectSyncLogMapper.successSync(projectId, point);
    }
}
