package com.bees360.service.listener.report;

import com.bees360.api.InvalidArgumentException;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.web.event.report.QuizCompletedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class QuizListener {

    @Autowired private PipelineService pipelineService;

    static String FORM_KEY = "fill_out_form";

    @Async
    @EventListener
    public void changeTaskStatus(QuizCompletedEvent event) {
        log.info("Quiz completed projectId {}", event.getProjectId());
        try {
            pipelineService.setTaskStatus(
                    String.valueOf(event.getProjectId()), FORM_KEY, Message.PipelineStatus.DONE);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Project does not config pipeline projectId {}", event.getProjectId());
        } catch (Exception e) {
            log.error("Quiz change task status failed {}", event.getProjectId(), e);
        }
    }
}
