package com.bees360.service.impl;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectTaskService;
import jakarta.inject.Inject;
import java.util.Objects;
import org.springframework.stereotype.Service;

@Service("projectTaskService")
public class ProjectTaskServiceImpl implements ProjectTaskService {

    @Inject
    ProjectMapper projectMapper;

    @Inject
    ProjectReportFileService projectReportFileService;

    @Override
    public boolean checkProjectReportFileApproved(long userId, long projectId, Integer reportTypeCode) throws ServiceException {
        Project project = projectMapper.getById(projectId);
        if (Objects.isNull(project) || Objects.isNull(project.getServiceType())) {
            return false;
        }
        ProjectServiceTypeEnum serviceTypeEnum = ProjectServiceTypeEnum.getEnum(project.getServiceType());
        if (!serviceTypeEnum.containsReport(reportTypeCode)) {
            return false;
        }
        var reportFile = projectReportFileService.getReportFile(projectId, reportTypeCode);

        return Objects.nonNull(reportFile) && reportFile.getGenerationStatus() == ReportGenerationStatusEnum.APPROVED.getCode();
    }
}
