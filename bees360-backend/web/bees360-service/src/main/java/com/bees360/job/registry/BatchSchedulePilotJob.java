package com.bees360.job.registry;

import lombok.Data;

import java.util.List;
@Data
@JobPayload
public class BatchSchedulePilotJob {
    // the projects that pilot member to be assigned
    private List<String> projectIds;
    // the pilot that to be assigned
    private String pilotId;
    // the user that operates this batch
    private String operationUserId;
    private Long inspectionTime;
    // whether the pilot accept the mission or not
    private Boolean pilotAccepted;
    private long version;
    // whether to assign pilot or remove pilot
    private boolean isAssignment;
}
