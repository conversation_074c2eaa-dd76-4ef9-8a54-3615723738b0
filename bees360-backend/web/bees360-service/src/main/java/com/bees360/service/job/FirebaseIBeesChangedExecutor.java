package com.bees360.service.job;

import com.bees360.job.registry.SerializableFirebaseIBeesMission;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseMissionService;
import com.google.cloud.firestore.Firestore;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;

import static com.bees360.service.job.FirebaseMissionChangedExecutor.translateExceptionAndThrow;

@Log4j2
@Component
@ToString
public class FirebaseIBeesChangedExecutor
        extends AbstractJobExecutor<SerializableFirebaseIBeesMission> {
    private final FirebaseMissionService firebaseMissionService;
    private final Firestore firestore;

    public FirebaseIBeesChangedExecutor(FirebaseMissionService firebaseMissionService, Firestore firestore) {
        this.firebaseMissionService = firebaseMissionService;
        this.firestore = firestore;

        log.info("Created '{}'", this);
    }

    @Override
    protected void handle(SerializableFirebaseIBeesMission mission) throws IOException {
        try {
            log.info("Start to handle ibees mission '{}' '{}'", mission.getId(), mission);
            firebaseMissionService.handleIBeesMission(mission, mission.getId());
            log.info("Successfully handle ibees mission '{}'", mission.getId());
        } catch (RuntimeException e) {
            log.error("Failed to handle ibees mission '{}'", mission.getId(), e);
            translateExceptionAndThrow(e);
        }
    }
}
