package com.bees360.service;

import java.util.List;
import java.util.Map;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.RolePermission;
import com.bees360.entity.enums.RoleEnum;

public interface RolePermissionService {

	/**
	 * get role permissions by user roles
	 * @param roles
	 * @return
	 * @throws ServiceException
	 */
	public Map<RoleEnum,List<RolePermission>> getRolePermissionsByRoles(long roles) throws ServiceException;

	/**
	 * get role permissions by single roleId
	 * @param roleId
	 * @return
	 * @throws ServiceException
	 */
	public List<RolePermission> getRolePermissions(int roleId) throws ServiceException;

	/**
	 *
	 * @param roleId
	 * @return
	 */
	public List<RolePermission> getRolePermissionsByRoleIds(List<Integer> roleIds) throws ServiceException;
}
