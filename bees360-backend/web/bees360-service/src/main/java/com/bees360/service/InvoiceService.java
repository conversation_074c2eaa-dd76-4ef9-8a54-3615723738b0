package com.bees360.service;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.vo.InvoiceVo;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/09/25 14:40
 */
public interface InvoiceService {

    /**
     * 列举项目中的所有invoice
     */
    List<InvoiceVo> listInvoice(long userId, long projectId) throws ServiceException;

    /**
     * 下载项目唯一的invoice文件，将文件内容编码为Base64字符串，并写入到{@link HttpServletResponse#getOutputStream()} 中返回。
     *
     * @throws ResourceNotFoundException 不存在该文件
     */
    void fetchInvoiceFile(long projectId, HttpServletResponse response);

    /**
     * 下载项目中指定的的invoice文件，将文件内容编码为Base64字符串，
     * 并写入到{@link HttpServletResponse#getOutputStream()} 中返回。
     *
     * @throws ResourceNotFoundException 不存在该文件
     */
    void fetchInvoiceFile(long projectId, long invoiceFileId, HttpServletResponse response);

}
