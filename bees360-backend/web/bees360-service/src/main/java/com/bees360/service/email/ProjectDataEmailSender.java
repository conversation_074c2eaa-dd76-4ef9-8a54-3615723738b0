package com.bees360.service.email;

import com.bees360.job.registry.IntervalProjectExportEmail;

import java.time.Instant;
import java.util.List;

public interface ProjectDataEmailSender {

    void sendEmailOfProjectDataWithinInterval(Instant start, Instant end, IntervalProjectExportEmail.Interval interval,
                                              List<String> recipients);

    void sendEmailOfOpenProjectData(String subject, List<String> recipients);
}
