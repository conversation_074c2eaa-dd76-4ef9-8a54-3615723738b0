package com.bees360.service.listener.firebase;

import com.bees360.entity.firebase.MissionStateEnum;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.FirebaseIBeesMissionCompleted;
import com.bees360.job.registry.FirebaseMissionCompletedStuck;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.SerializableFirebaseIBeesMission;
import com.bees360.job.registry.SerializableFirebaseMission;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.DateTimes;
import com.google.cloud.Timestamp;
import com.google.cloud.firestore.DocumentChange;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.bees360.service.listener.firebase.FirebaseSnapshotListener.fetch;
import static com.bees360.service.listener.firebase.FirebaseSnapshotListener.reRegistry;

/** TODO 这个里面的逻辑要迁移到EventApp中 */
@Slf4j
@Component
public class FirebaseMissionSnapshotListener {
    @Autowired private Firestore firestore;
    private final Set<String> visitedSet = Sets.newConcurrentHashSet();
    @Autowired private JobScheduler jobScheduler;
    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;
    private static final String ON_I_BEES_MISSION_COMPLETED = "ON_I_BEES_MISSION_COMPLETED";
    private static final String ON_MISSION_COMPLETED_STUCK = "ON_MISSION_COMPLETED_STUCK";
    @PostConstruct
    public void changeOnPilotMissionCompletedStuck() {
        if(!bees360FeatureSwitch.isEnableChangeOnPilotMissionCompletedStuck()){
            return;
        }
        firestore
            .collection(FirebaseService.MISSION_COLLECTION)
            .whereEqualTo("isDeleted", false)
            .whereEqualTo("status", MissionStateEnum.COMPLETED_STUCK.getCode())
            .whereGreaterThanOrEqualTo("lastUpdateTime", Timestamp.fromProto(DateTimes.toTimestamp(getAMonthAgo())))
            .addSnapshotListener(
                (snapshots, e) -> {
                    try {
                        if (!visitedSet.contains(ON_MISSION_COMPLETED_STUCK)) {
                            visitedSet.add(ON_MISSION_COMPLETED_STUCK);
                            log.info("Visited '{}' on application start up.", ON_MISSION_COMPLETED_STUCK);
                            return;
                        }
                        if (snapshots == null || e != null) {
                            Optional.ofNullable(e)
                                .ifPresent(
                                    (error) ->
                                        log.warn(
                                            "Listen failed with error: {} on collection {}, and it will be reRegister.",
                                            e.getMessage(), ON_MISSION_COMPLETED_STUCK));
                            reRegistry(this::changeOnPilotMissionCompletedStuck, FirebaseService.MISSION_COLLECTION);
                            return;
                        }
                        snapshots.getDocumentChanges().stream()
                            .filter(
                                doc ->
                                    // 过滤掉REMOVED事件
                                    !DocumentChange.Type.REMOVED.equals(
                                        doc.getType()))
                            .map(DocumentChange::getDocument)
                            .forEach(this::publishMissionCompleteStuck);
                    } catch (Exception e1) {
                        log.error("Unknown error when handle Pilot mission", e1);
                    }
                });
    }

    @PostConstruct
    public void changeOnIBeesMissionCompleted() {
        if(!bees360FeatureSwitch.isEnableChangeOnIBeesMissionCompleted()){
            return;
        }
        firestore
            .collection(FirebaseService.IBEES_MISSION_COLLECTION)
            .whereEqualTo("isDeleted", false)
            .whereEqualTo("status", MissionStateEnum.COMPLETED.getCode())
            .addSnapshotListener(
                (snapshots, e) -> {
                    if (!visitedSet.contains(ON_I_BEES_MISSION_COMPLETED)) {
                        visitedSet.add(ON_I_BEES_MISSION_COMPLETED);
                        log.info("Visited '{}' on application start up.", ON_I_BEES_MISSION_COMPLETED);
                        return;
                    }
                    try {
                        if (snapshots == null || e != null) {
                            Optional.ofNullable(e)
                                .ifPresent(
                                    (error) ->
                                        log.warn(
                                            "Listen failed with error: {} on collection {}, and it will be reRegister.",
                                            e.getMessage(), FirebaseService.IBEES_MISSION_COLLECTION));
                            reRegistry(this::changeOnIBeesMissionCompleted, FirebaseService.IBEES_MISSION_COLLECTION);
                            return;
                        }
                        snapshots.getDocumentChanges().stream()
                            .filter(
                                doc ->
                                    DocumentChange.Type.ADDED.equals(
                                        doc.getType()))
                            .map(DocumentChange::getDocument)
                            .forEach(this::publishIBeesMissionComplete);
                    } catch (Exception e1) {
                        log.error("Unknown error when handle IBees mission", e1);
                    }
                });
    }

    public static Instant getAMonthAgo() {
        return Instant.now().plus(-30, ChronoUnit.DAYS);
    }

    private void publishMissionCompleteStuck(QueryDocumentSnapshot snapshot) {
        try {
            SerializableFirebaseMission firebaseMission =
                fetch(SerializableFirebaseMission.class, snapshot);
            if (firebaseMission.needIgnore()) {
                log.info("Ignored mission '{}'.", firebaseMission);
                return;
            }
            FirebaseMissionCompletedStuck missionJob = new FirebaseMissionCompletedStuck();
            missionJob.setPilotId(firebaseMission.getPilotId());
            missionJob.setMissionPath(snapshot.getReference().getPath());
            missionJob.setProjectId(firebaseMission.getProject().getProjectId());
            Job job = JobPayloads.encode(String.valueOf(missionJob.hashCode()), missionJob);
            jobScheduler.schedule(RetryableJob.of(job, 5, Duration.ofSeconds(5), 2F));
        } catch (RuntimeException e) {
            log.error(
                "Failed to schedule mission'(path={},data={})' completed stuck job.",
                snapshot.getReference().getPath(),
                snapshot.getData());
        }
    }

    private void publishIBeesMissionComplete(QueryDocumentSnapshot snapshot) {
        try {
            SerializableFirebaseIBeesMission firebaseMission =
                fetch(SerializableFirebaseIBeesMission.class, snapshot);
            FirebaseIBeesMissionCompleted missionJob = new FirebaseIBeesMissionCompleted();
            missionJob.setMissionPath(snapshot.getReference().getPath());
            missionJob.setProjectId(firebaseMission.getProject().getProjectId());
            Job job = JobPayloads.encode(String.valueOf(missionJob.hashCode()), missionJob);
            jobScheduler.schedule(RetryableJob.of(job, 5, Duration.ofSeconds(5), 2F));
        } catch (RuntimeException e) {
            log.error(
                "Failed to scheduler IBees mission'(path={},data={})' completed stuck job.",
                snapshot.getReference().getPath(),
                snapshot.getData());
        }
    }
}
