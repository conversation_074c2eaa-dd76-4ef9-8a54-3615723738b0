package com.bees360.event;

import com.bees360.event.registry.ProjectPolicyUpdatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.firebase.FirebaseService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听项目策略更新事件并将更新后的项目同步到Firebase服务
 */
@Log4j2
public class SyncProjectToFirebaseOnPolicyUpdatedEvent extends AbstractNamedEventListener<ProjectPolicyUpdatedEvent> {

    private final FirebaseService firebaseService;

    public SyncProjectToFirebaseOnPolicyUpdatedEvent(
            FirebaseService firebaseService) {
        this.firebaseService = firebaseService;
        log.info("Created {}(firebaseService={})", this, firebaseService);
    }

    @Override
    public void handle(ProjectPolicyUpdatedEvent event) throws IOException {
        for (var projectId: event.getProjectIds()) {
            firebaseService.syncProjectToFirebase(Long.parseLong(projectId));
        }
    }
}
