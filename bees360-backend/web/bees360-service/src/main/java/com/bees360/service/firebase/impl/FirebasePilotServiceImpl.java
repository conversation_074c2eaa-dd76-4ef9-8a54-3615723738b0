package com.bees360.service.firebase.impl;

import com.bees360.commons.firebasesupport.entity.DocumentData;
import com.bees360.commons.firebasesupport.entity.QueryModel;
import com.bees360.commons.firebasesupport.service.FirebaseHelper;
import com.bees360.entity.User;
import com.bees360.entity.dto.PilotBadge;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.firebase.FirebaseFAA;
import com.bees360.entity.firebase.FirebaseLiabilityInsurance;
import com.bees360.entity.firebase.FirebasePilotAddress;
import com.bees360.entity.firebase.FirebasePilotBadge;
import com.bees360.entity.firebase.PilotAccessLevelEnum;
import com.bees360.entity.pilot.Pilot;
import com.bees360.job.registry.SerializableFirebasePilot;
import com.bees360.mapper.pilot.PilotMapper;
import com.bees360.service.UserService;
import com.bees360.service.firebase.FirebasePilotService;
import com.bees360.util.UrlJoinParserUtils;
import com.google.api.core.ApiFuture;
import com.google.cloud.Timestamp;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.GeoPoint;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.cloud.firestore.SetOptions;
import com.google.cloud.firestore.WriteResult;
import com.google.common.base.Joiner;
import com.google.common.util.concurrent.MoreExecutors;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/** firebase pilot相关业务 */
@Service
@Slf4j
public class FirebasePilotServiceImpl implements FirebasePilotService {

    @Autowired private PilotMapper pilotMapper;

    @Autowired private FirebaseHelper firebaseHelper;
    @Autowired private Firestore firestore;
    @Autowired private UserService userService;

    @Value("${firebase.badge.claimBadgeId:}")
    private String claimBadgeId;

    @Value("${firebase.badge.underwritingBadgeId:}")
    private String underwritingBadgeId;

    @Value("${firebase.badge.magicplanBadgeId:}")
    private String magicplanBadgeId;

    /**
     * 根据email查询pilotDoc
     *
     * @param email 邮箱
     * @return pilot Doc
     */
    @Override
    public DocumentData<SerializableFirebasePilot> getDocByEmail(String email) {
        QueryModel queryModel = new QueryModel("email", email);
        List<DocumentData<SerializableFirebasePilot>> dataList =
                firebaseHelper.getDataBySingleEqualQuery(
                        PILOT_COLLECTION, queryModel, SerializableFirebasePilot.class);
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        return dataList.get(0);
    }

    /**
     * 根据Phone查询pilotDoc
     *
     * @param phone 手机
     * @return pilot Doc
     */
    @Override
    public DocumentData<SerializableFirebasePilot> getDocByPhone(String phone) {
        QueryModel queryModel = new QueryModel("phone", phone);
        List<DocumentData<SerializableFirebasePilot>> dataList =
                firebaseHelper.getDataBySingleEqualQuery(
                        PILOT_COLLECTION, queryModel, SerializableFirebasePilot.class);
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        return dataList.get(0);
    }

    /**
     * 同步User数据到pilot
     *
     * @param user
     */
    @Override
    public void syncPilot(User user) throws ExecutionException, InterruptedException {
        if (!user.hasRole(RoleEnum.PILOT)) {
            return;
        }
        if (!checkPilotExistInFirebase(user)) {
            addPilotToFirebase(user);
        } else {
            DocumentReference pilotDocument = getPilotReferenceByWebUserId(user.getUserId());
            updatePilotBase(pilotDocument, user);
        }
    }

    @Override
    public void syncPilotRemoval(User user) {
        if (!checkPilotExistInFirebase(user)) {
            return;
        }
        firestore
            .collection(PILOT_COLLECTION)
            .document(String.valueOf(user.getUserId()))
            .update("accessLevel", -1);
    }

    private boolean checkPilotExistInFirebase(User user) {
        boolean pilotExist = false;
        try {
            DocumentReference pilotDocument = getPilotReferenceByWebUserId(user.getUserId());
            SerializableFirebasePilot firebasePilot =
                pilotDocument.get().get().toObject(SerializableFirebasePilot.class);
            pilotExist = firebasePilot != null;
        } catch (ExecutionException | InterruptedException e) {
            log.error("Exception occurred while checking firebase pilot", e);
        }
        return pilotExist;
    }

    private DocumentReference getPilotReferenceByWebUserId(long userId)
            throws ExecutionException, InterruptedException {
        ApiFuture<QuerySnapshot> snapshotApiFuture =
                firestore.collection(PILOT_COLLECTION).whereEqualTo("webUserId", userId + "").get();
        List<QueryDocumentSnapshot> snapshots = snapshotApiFuture.get().getDocuments();
        if (CollectionUtils.isEmpty(snapshots)) {
            return firestore.collection(PILOT_COLLECTION).document(userId + "");
        }
        return firestore.collection(PILOT_COLLECTION).document(snapshots.get(0).getId());
    }

    private Map<String, Object> getPilotBaseMap(User user) {
        Map<String, Object> map = new HashMap<>();
        int accessLevel = PilotAccessLevelEnum.getAccessLevelByUserActiveStatus(user.getActiveStatus())
            .getCode();
        if (!user.hasRole(RoleEnum.PILOT)) {
            accessLevel = PilotAccessLevelEnum.BANED.getCode();
        }
        FirebasePilotAddress.FirebasePilotAddressBuilder addressBuilder =
                FirebasePilotAddress.builder()
                        .gps(
                                new GeoPoint(
                                        user.getGpsLocationLatitude(),
                                        user.getGpsLocationLongitude()));
        Optional.ofNullable(user.getCity()).ifPresent(addressBuilder::city);
        Optional.ofNullable(user.getCountry()).ifPresent(addressBuilder::country);
        Optional.ofNullable(user.getAddress()).ifPresent(addressBuilder::streetAddress);
        Optional.ofNullable(user.getZipCode()).ifPresent(addressBuilder::zipcode);
        Optional.ofNullable(user.getState()).ifPresent(addressBuilder::state);
        map.put("email", user.getEmail());
        map.put("firstName", user.getFirstName());
        map.put("lastName", user.getLastName());
        map.put("phone", user.getPhone());
        map.put("travelRadius", user.getTravelRadius());
        map.put("avatar", user.getAvatar());
        map.put("homeAddress", addressBuilder.build());
        map.put("webUserId", String.valueOf(user.getUserId()));
        map.put("accessLevel",accessLevel);
        map.put("rating", user.getRating());
        return map;
    }

    private void updatePilotBase(DocumentReference pilotReference, User user) {
        Map<String, Object> map = getPilotBaseMap(user);
        pilotReference.update(map);
    }

    private void addPilotToFirebase(User user) throws ExecutionException, InterruptedException {
        Map<String, Object> map = createPilotMap(user);
        ApiFuture<WriteResult> future =
                firestore
                        .collection(PILOT_COLLECTION)
                        .document(String.valueOf(user.getUserId()))
                        .create(map);
        log.info("Success add pilot '{}' at '{}'", user.getUserId(), future.get().getUpdateTime());
    }

    private Map<String, Object> createPilotMap(User user) {
        Map<String, Object> map = getPilotBaseMap(user);
        map.put("isOnLeave", false);
        map.put("updateTime", Timestamp.now());
        map.put(
                "badge",
                mergeBadge(
                        new HashMap<>(),
                        PilotBadge.builder()
                                .hasClaimBadge(user.getHasClaimBadge())
                                .hasUnderwritingBadge(user.getHasUnderwritingBadge())
                                .hasMagicplanBadge(user.getHasMagicplanBadge())
                                .build()));
        Pilot pilot = pilotMapper.queryByUserId(user.getUserId());
        Optional.ofNullable(user.getCertificateList())
                .map(
                        url ->
                                getFaa(
                                        url,
                                        Optional.ofNullable(pilot)
                                                .map(Pilot::getInsuranceNumber)
                                                .orElse(null),
                                        Optional.ofNullable(pilot)
                                                .map(Pilot::getLicenseIssueDate)
                                                .map(Timestamp::of)
                                                .orElse(null),
                                        Optional.ofNullable(pilot)
                                                .map(Pilot::getInsuranceExpiryDate)
                                                .map(Timestamp::of)
                                                .orElse(null)))
                .ifPresent(faa -> map.put("faa", Collections.singletonList(faa)));
        Optional.ofNullable(user.getInsuranceKeyUrls())
                .map(
                        url ->
                                getInsurance(
                                        url,
                                        Optional.ofNullable(pilot)
                                                .map(Pilot::getInsuranceAmount)
                                                .orElse(null)))
                .ifPresent(faa -> map.put("liabilityInsurance", Collections.singletonList(faa)));
        return map;
    }

    private Map<String, FirebasePilotBadge> mergeBadge(
            Map<String, FirebasePilotBadge> badgeMap, PilotBadge pilotBadge) {
        if (badgeMap == null) {
            badgeMap = new HashMap<>();
        }
        final Map<String, FirebasePilotBadge> finalBadgeMap = badgeMap;
        Optional.ofNullable(pilotBadge.getHasClaimBadge())
                .ifPresent(
                        flag ->
                                finalBadgeMap.compute(
                                        claimBadgeId,
                                        (id, badge) -> {
                                            if (badge == null) {
                                                badge = new FirebasePilotBadge();
                                            }
                                            badge.setIsEnabled(flag);
                                            badge.setUpdateTime(Timestamp.now());
                                            return badge;
                                        }));
        Optional.ofNullable(pilotBadge.getHasUnderwritingBadge())
                .ifPresent(
                        flag ->
                                finalBadgeMap.compute(
                                        underwritingBadgeId,
                                        (id, badge) -> {
                                            if (badge == null) {
                                                badge = new FirebasePilotBadge();
                                            }
                                            badge.setIsEnabled(flag);
                                            badge.setUpdateTime(Timestamp.now());
                                            return badge;
                                        }));
        Optional.ofNullable(pilotBadge.getHasMagicplanBadge())
                .ifPresent(
                        flag ->
                                finalBadgeMap.compute(
                                        magicplanBadgeId,
                                        (id, badge) -> {
                                            if (badge == null) {
                                                badge = new FirebasePilotBadge();
                                            }
                                            badge.setIsEnabled(flag);
                                            badge.setUpdateTime(Timestamp.now());
                                            return badge;
                                        }));
        return finalBadgeMap;
    }

    private FirebaseFAA getFaa(
            String licenseKeyUrl,
            String licenseNumber,
            Timestamp licenseIssueDate,
            Timestamp licenseExpiryDate) {
        if (StringUtils.isBlank(licenseKeyUrl)) {
            return null;
        }
        if (StringUtils.isNotBlank(licenseKeyUrl)) {
            licenseKeyUrl = licenseKeyUrl.substring(licenseKeyUrl.indexOf(":") + 1);
            licenseKeyUrl = licenseKeyUrl.substring(0, licenseKeyUrl.lastIndexOf(";"));
            String[] urls = licenseKeyUrl.split("[,;](\\d:)?(\\s+)?(?=user)");
            licenseKeyUrl = urls[urls.length - 1];
        }
        FirebaseFAA firebaseFAA = new FirebaseFAA();
        firebaseFAA.setLicensePreview(licenseKeyUrl);
        firebaseFAA.setLicense(licenseKeyUrl);
        firebaseFAA.setExpireTime(licenseExpiryDate);
        firebaseFAA.setIssuanceTime(licenseIssueDate);
        firebaseFAA.setLicenseNumber(licenseNumber);
        firebaseFAA.setStatus("Approved");
        return firebaseFAA;
    }

    private FirebaseLiabilityInsurance getInsurance(String insuranceUrl, BigDecimal amount) {
        if (StringUtils.isBlank(insuranceUrl)) {
            return null;
        }
        if (StringUtils.isNotBlank(insuranceUrl)) {
            insuranceUrl = insuranceUrl.replace("[", "").replace("]", "").replaceAll("\"", "");
            String[] urls = insuranceUrl.split("[,;](\\d:)?(\\s+)?(?=user)");
            insuranceUrl = urls[urls.length - 1];
        }
        FirebaseLiabilityInsurance insurance = new FirebaseLiabilityInsurance();
        insurance.setAmount(Optional.ofNullable(amount).map(BigDecimal::doubleValue).orElse(null));
        insurance.setLiabilityInsurancePreview(insuranceUrl);
        insurance.setLiabilityInsurance(insuranceUrl);
        insurance.setStatus("Approved");
        return insurance;
    }
}
