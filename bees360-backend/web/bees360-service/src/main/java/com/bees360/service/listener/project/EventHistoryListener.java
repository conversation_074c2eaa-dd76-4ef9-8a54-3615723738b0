package com.bees360.service.listener.project;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.EventHistory;
import com.bees360.entity.Project;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.service.ContextProvider;
import com.bees360.service.EventHistoryService;
import com.bees360.service.beespilot.BeesPilotBatchItemService;
import com.bees360.web.event.project.ProjectCanceledEvent;
import com.bees360.web.event.project.ProjectCreatedEvent;
import com.bees360.web.event.project.ProjectDeletedEvent;
import com.bees360.web.event.project.ProjectEvent;
import com.bees360.web.event.project.ProjectRecoveredEvent;
import com.bees360.web.event.project.ProjectStatusClientReceivedEvent;
import com.bees360.web.event.project.ProjectStatusReceiveErrorEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.dao.DeadlockLoserDataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR> Guanrong
 * @date 2020/04/16 10:26
 */
@Slf4j
@Component
public class EventHistoryListener {

    @Autowired
    private EventHistoryService eventHistoryService;

    @Autowired
    private ContextProvider springSecurityContextProvider;

    @Autowired
    private BeesPilotBatchItemService beesPilotBatchItemService;

    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void insertHistoryOnProjectCreatedEvent(ProjectCreatedEvent event) throws ServiceException {
        long userId = event.getProject().getCreatedBy();
        logInfo(event, userId,"insert NEW_PROJECT eventHistory.");
        Project project = event.getProject();

        insertHistory(project.getProjectId(), ProjectStatusEnum.NEW_PROJECT, userId, userId);

        if (StringUtils.isNoneBlank(project.getClaimNumber())) {
            insertHistory(project.getProjectId(), ProjectStatusEnum.JOB_RECEIVED, userId,
                userId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void insertHistoryOnProjectDeletedEvent(ProjectDeletedEvent event) throws ServiceException {
        long userId =
            Optional.of(springSecurityContextProvider.getUserIdFromContext())
                .filter(l -> l != 0)
                .orElse(event.getOperator());

        logInfo(event, userId,"insert DELETE eventHistory.");
        Project project = event.getProject();

        insertHistory(project.getProjectId(), ProjectStatusEnum.DELETE, userId,
            userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void insertHistoryOnClientReceivedStatus(ProjectStatusClientReceivedEvent event) throws ServiceException {
        long userId = event.getUserId();
        logInfo(event, userId, "insert Client Received eventHistory.");
        final Project project = event.getProject();
        insertHistory(project.getProjectId(), ProjectStatusEnum.CLIENT_RECEIVED, userId, userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void insertHistoryOnReceiveError(ProjectStatusReceiveErrorEvent event) throws ServiceException {

        long userId = springSecurityContextProvider.getUserIdFromContext();
        logInfo(event, userId, "");

        final Project project = event.getProject();
        insertHistory(project.getProjectId(), ProjectStatusEnum.RECEIVE_ERROR, userId, userId);

    }

    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void insertHistoryOnProjectCanceled(ProjectCanceledEvent event) throws ServiceException {
        long userId = event.getUserId();

        logInfo(event, userId,"insert CANCELED eventHistory.");
        Project project = event.getProject();

        insertHistory(project.getProjectId(), ProjectStatusEnum.CANCELED, userId,
            userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void insertHistoryOnProjectRecovered(ProjectRecoveredEvent event) throws ServiceException {
        long userId = springSecurityContextProvider.getUserIdFromContext();

        logInfo(event, userId,"insert RECOVERED eventHistory.");

        Project project = event.getProject();

        insertHistory(project.getProjectId(), ProjectStatusEnum.RECOVERED, userId,
            userId);
    }

    private void logInfo(ProjectEvent event, Long userId, String message) {
        String sourceName = event.getSource().getClass().getName();
        String eventName = event.getClass().getSimpleName();
        long projectId = event.getProject().getProjectId();

        log.info("Event(name:{}, source: {}, project: {}) triggered by {}: {}", eventName, sourceName, projectId,
            userId, message);
    }

    private void insertHistory(long projectId, ProjectStatusEnum projectStatus, long userId, long modifyBy) {

        long now = System.currentTimeMillis();

        EventHistory newProjectHistory = new EventHistory();
        newProjectHistory.setProjectId(projectId);
        newProjectHistory.setUserId(userId);
        newProjectHistory.setStatus(projectStatus.getCode());
        newProjectHistory.setStatusTime(now);
        newProjectHistory.setModifiedBy(modifyBy);
        newProjectHistory.setCreatedTime(now);
        newProjectHistory.setDescription("");
        int retryCount = 0;
        int maxRetries = 3;
        while(retryCount < maxRetries) {
            try {
                eventHistoryService.insertHistoryToProject(newProjectHistory);
                break;
            } catch (DeadlockLoserDataAccessException e) {
                retryCount ++;
                if(retryCount >= maxRetries) {
                    throw e;
                }
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException ex) {
                    throw new RuntimeException(ex);
                }
            }
        }

    }

}
