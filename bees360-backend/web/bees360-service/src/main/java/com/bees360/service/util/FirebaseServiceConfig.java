package com.bees360.service.util;

import com.bees360.contact.ContactRecordManager;
import com.bees360.contact.ContactRecordTimes;
import com.bees360.project.ProjectIIRepository;
import com.bees360.util.Iterables;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@Configuration
@Log4j2
public class FirebaseServiceConfig {
    @Autowired ProjectIIRepository projectIIRepository;
    @Autowired ContactRecordManager contactRecordManager;

    private static final String PILOT = "PILOT";
    private static final String INSURED = "INSURED";
    private static final String AGENT = "AGENT";
    private static final String TAG = "TAG";
    private static final String CLOSE_REASON = "CLOSE_REASON";

    @Data
    static class FeedbackProperty {
        private String feedbackKey;
        private Long operationTagId;
        private String closeReason;
        private Boolean isOverDefaultTag = true;
        private Boolean isOverDefaultCloseReason = true;
        private Map<String, Integer> defaultTagContactTimes = new HashMap<>();
        private Map<String, Integer> defaultCloseReasonContactTimes = new HashMap<>();
        private List<FeedbackCheckProperty> checkProperties;

        @Data
        static class FeedbackCheckProperty {
            private String companyKey;
            private Boolean isOverStandardTag;
            private Boolean isOverStandardCloseReason;
            private Map<String, Integer> tagContactTimes = new HashMap<>();
            private Map<String, Integer> closeReasonContactTimes = new HashMap<>();
        }
    }

    @Data
    static class CallRecordProperty {
        private String callRecordKey;
        private Long operationTagId;
        private String closeReason;
    }

    @Bean
    @ConfigurationProperties(prefix = "firebase.service.feedback.property")
    public List<FeedbackProperty> feedbackProperty() {
        return new ArrayList<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "firebase.service.call-record.property")
    public List<CallRecordProperty> callRecordProperty() {
        return new ArrayList<>();
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "firebase.service.feedback",
            name = "disabled",
            havingValue = "false")
    public BiFunction<String, String, Long> projectFeedback2TagIdConverter(
            List<FeedbackProperty> firebaseFeedbackProperty) {
        var template2PropertyMap =
                firebaseFeedbackProperty.stream()
                        .collect(
                                Collectors.toMap(
                                        FeedbackProperty::getFeedbackKey, Function.identity()));
        return (projectId, template) -> {
            log.info(
                    "Get operation tag for feedback :{} in map :{}",
                    template,
                    template2PropertyMap);
            if (!template2PropertyMap.containsKey(template)) {
                return null;
            }
            var feedbackProperty = template2PropertyMap.get(template);
            if (feedbackContactTimesChecker(feedbackProperty, projectId, TAG)) {
                return feedbackProperty.getOperationTagId();
            }
            return null;
        };
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "firebase.service.feedback",
            name = "disabled",
            havingValue = "false")
    public BiFunction<String, String, String> projectFeedback2CloseReasonConverter(
            List<FeedbackProperty> firebaseFeedbackProperty) {
        var template2PropertyMap =
                firebaseFeedbackProperty.stream()
                        .collect(
                                Collectors.toMap(
                                        FeedbackProperty::getFeedbackKey, Function.identity()));
        return (projectId, template) -> {
            log.info(
                    "Get close reason for feedback :{} in map :{}", template, template2PropertyMap);
            if (!template2PropertyMap.containsKey(template)) {
                return null;
            }
            var feedbackProperty = template2PropertyMap.get(template);
            if (feedbackContactTimesChecker(feedbackProperty, projectId, CLOSE_REASON)) {
                return feedbackProperty.getCloseReason();
            }
            return null;
        };
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "firebase.service.call-record",
            name = "disabled",
            havingValue = "false")
    public BiFunction<String, String, Long> callRecord2OperationTagConverter(
            List<CallRecordProperty> firebaseCallRecordProperty) {
        var template2PropertyMap =
                firebaseCallRecordProperty.stream()
                        .collect(
                                Collectors.toMap(
                                        CallRecordProperty::getCallRecordKey, Function.identity()));
        return (projectId, template) -> {
            log.info(
                    "Get operation tag for call record :{} in map :{}",
                    template,
                    template2PropertyMap);
            return Optional.ofNullable(template2PropertyMap.get(template))
                    .map(CallRecordProperty::getOperationTagId)
                    .orElse(null);
        };
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "firebase.service.call-record",
            name = "disabled",
            havingValue = "false")
    public BiFunction<String, String, String> callRecord2ChangeReasonConverter(
            List<CallRecordProperty> firebaseCallRecordProperty) {
        var template2PropertyMap =
                firebaseCallRecordProperty.stream()
                        .collect(
                                Collectors.toMap(
                                        CallRecordProperty::getCallRecordKey, Function.identity()));
        return (projectId, template) -> {
            log.info(
                    "Get close reason for template :{} in map :{}", template, template2PropertyMap);
            return Optional.ofNullable(template2PropertyMap.get(template))
                    .map(CallRecordProperty::getCloseReason)
                    .orElse(null);
        };
    }

    private Boolean feedbackContactTimesChecker(
            FeedbackProperty property, String projectId, String field) {
        var checkProperties = property.getCheckProperties();
        if (checkProperties == null
                && property.getDefaultCloseReasonContactTimes().isEmpty()
                && property.getDefaultTagContactTimes().isEmpty()) {
            // 不需要进行校验
            return true;
        }
        var project = projectIIRepository.findById(projectId);
        var company = project.getContract().getInsuredBy();
        if (company == null) {
            return true;
        }
        var companyKey = company.getCompanyKey();
        Map<String, FeedbackProperty.FeedbackCheckProperty> company2ContactTimesMap =
                new HashMap<>();
        if (checkProperties != null) {
            company2ContactTimesMap =
                    checkProperties.stream()
                            .collect(
                                    Collectors.toMap(
                                            FeedbackProperty.FeedbackCheckProperty::getCompanyKey,
                                            Function.identity()));
        }
        var companyProperty = company2ContactTimesMap.get(companyKey);
        boolean isOverStandard;
        if (StringUtils.equals(field, TAG)) {
            isOverStandard =
                    companyProperty == null || companyProperty.getIsOverStandardTag() == null
                            ? property.getIsOverDefaultTag()
                            : companyProperty.getIsOverStandardTag();
        } else {
            isOverStandard =
                    companyProperty == null
                                    || companyProperty.getIsOverStandardCloseReason() == null
                            ? property.getIsOverDefaultCloseReason()
                            : companyProperty.getIsOverStandardCloseReason();
        }
        var insuredContactTimesStandard =
                getContactTimesFromProperty(companyProperty, INSURED, field, property);
        var agentContactTimesStandard =
                getContactTimesFromProperty(companyProperty, AGENT, field, property);

        var contactRecordTimes =
                Iterables.toStream(
                                contactRecordManager.getContactRecordTimesByProjectId(
                                        List.of(projectId)))
                        .filter(record -> record.getProjectId().equals(projectId))
                        .collect(Collectors.toList());
        var contactInsuredTimes = getContactTimes(PILOT, INSURED, contactRecordTimes);
        var contactAgentTimes = getContactTimes(PILOT, AGENT, contactRecordTimes);
        var checkValue =
                checkValue(
                        isOverStandard,
                        contactInsuredTimes,
                        insuredContactTimesStandard,
                        contactAgentTimes,
                        agentContactTimesStandard);
        log.info(
                "get contact record times :{} and contact time for insured :{} and agent :{} and standard for insured :{} and agent :{} and isOverStandard :{} get check value :{}",
                contactRecordTimes.stream()
                        .map(ContactRecordTimes::toMessage)
                        .collect(Collectors.toList()),
                contactInsuredTimes,
                contactAgentTimes,
                insuredContactTimesStandard,
                agentContactTimesStandard,
                isOverStandard,
                checkValue);
        return checkValue;
    }

    private Boolean checkValue(
            Boolean isOver,
            Integer actual1,
            Integer expected1,
            Integer actual2,
            Integer expected2) {
        // 任意联系情况低于要求，或所有联系情况高于要求。
        return (isOver && actual1 >= expected1 && actual2 >= expected2)
                || (!isOver && (actual1 < expected1 || actual2 < expected2));
    }

    private Integer getContactTimesFromProperty(
            FeedbackProperty.FeedbackCheckProperty companyProperty,
            String contactTo,
            String field,
            FeedbackProperty property) {
        Map<String, Integer> contactMap;
        if (companyProperty == null) {
            contactMap =
                    StringUtils.equals(field, TAG)
                            ? property.getDefaultTagContactTimes()
                            : property.getDefaultCloseReasonContactTimes();
        } else {
            contactMap =
                    StringUtils.equals(field, TAG)
                            ? companyProperty.getTagContactTimes()
                            : companyProperty.getCloseReasonContactTimes();
        }
        return contactMap.getOrDefault(contactTo, 0);
    }

    private Integer getContactTimes(
            String contactFrom,
            String contactTo,
            List<? extends ContactRecordTimes> contactRecordTimes) {
        return contactRecordTimes.stream()
                .filter(
                        record ->
                                record.getContactFrom().equals(contactFrom)
                                        && record.getContactTo().equals(contactTo))
                .findAny()
                .map(ContactRecordTimes::getContactTimes)
                .orElse(0);
    }
}
