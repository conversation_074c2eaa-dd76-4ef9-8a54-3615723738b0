package com.bees360.service.converter;

import com.bees360.entity.Company;
import com.bees360.entity.Project;
import com.bees360.entity.dto.BeesPilotProjectDto;
import com.bees360.entity.enums.CheckStatusEnum;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.entity.enums.PayStatusEnum;
import com.bees360.entity.enums.ProcessStatusEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.vo.ProjectInspectionInfoVo;
import com.bees360.entity.vo.ProjectInsuredInfoVo;
import com.bees360.entity.vo.ProjectServiceTypeVo;
import com.bees360.entity.vo.beespilot.BeesPilotProjectInfo;
import com.bees360.entity.vo.beespilot.BeesPilotProjectVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Guanrong
 * @date 2020/02/28 16:05
 */
public class ProjectConverter {

    public static ProjectInsuredInfoVo toProjectInsuredInfoVo(Project project) {
        if(project == null) {
            return null;
        }
        ProjectInsuredInfoVo insuredInfoVo = new ProjectInsuredInfoVo();
        insuredInfoVo.setAssetOwnerName(project.getAssetOwnerName());
        insuredInfoVo.setAssetOwnerEmail(project.getAssetOwnerEmail());
        insuredInfoVo.setAssetOwnerPhone(project.getAssetOwnerPhone());
        insuredInfoVo.setInsuredHomePhone(project.getInsuredHomePhone());
        insuredInfoVo.setInsuredWorkPhone(project.getInsuredWorkPhone());

        insuredInfoVo.setAddress(project.getAddress());
        insuredInfoVo.setCity(project.getCity());
        insuredInfoVo.setState(project.getState());
        insuredInfoVo.setZipCode(project.getZipCode());
        insuredInfoVo.setCountry(project.getCountry());
        insuredInfoVo.setLat(project.getLat());
        insuredInfoVo.setLng(project.getLng());

        insuredInfoVo.setProjectType(project.getProjectType());
        insuredInfoVo.setDueDate(project.getDueDate());

        String claimNote = "";
        if (StringUtils.isNotBlank(project.getSpecialInstructions()) &&
            (StringUtils.isBlank(project.getClaimNote()) || !project.getClaimNote().contains("specialInstructions"))) {
            claimNote = claimNote + "specialInstructions: " + project.getSpecialInstructions() + "\r\n";
        }
        if (StringUtils.isNotBlank(project.getSpecialInstructionComments()) &&
            (StringUtils.isBlank(project.getClaimNote()) || !project.getClaimNote().contains("specialInstructionComments"))) {
            claimNote = claimNote + "specialInstructionComments: " + project.getSpecialInstructionComments() + "\r\n";
        }
        if (StringUtils.isNotBlank(project.getClaimNote())) {
            claimNote = claimNote + project.getClaimNote();
        }

        claimNote = handleProjectServiceNote(claimNote);

        insuredInfoVo.setClaimNote(claimNote);
        insuredInfoVo.setOperatingCompany(project.getOperatingCompany());
        return insuredInfoVo;
    }

    public static ProjectInspectionInfoVo toProjectInspectionInfoVo(Project project, Company insuranceCompany,
            Company repairCompany) {
        if(project == null) {
            return null;
        }
        ProjectInspectionInfoVo inspectionInfoVo = new ProjectInspectionInfoVo();
        inspectionInfoVo.setPolicyNumber(project.getPolicyNumber());
        inspectionInfoVo.setInspectionNumber(project.getInspectionNumber());
        inspectionInfoVo.setClaimType(project.getClaimType());
        inspectionInfoVo.setDamageEventTime(project.getDamageEventTime());
        inspectionInfoVo.setAgent(project.getAgent());
        inspectionInfoVo.setAgentContactName(project.getAgentContactName());
        inspectionInfoVo.setAgentEmail(project.getAgentEmail());
        inspectionInfoVo.setAgentPhone(project.getAgentPhone());
        inspectionInfoVo.setInsuranceCompany(CompanyConverter.toCompanyCard(insuranceCompany));
        inspectionInfoVo.setRepairCompany(CompanyConverter.toCompanyCard(repairCompany));
        inspectionInfoVo.setInspectionTime(project.getInspectionTime());
        inspectionInfoVo.setPolicyEffectiveDate(project.getPolicyEffectiveDate());
        inspectionInfoVo.setYearBuilt(project.getYearBuilt());
        inspectionInfoVo.setPayStatus(project.getPayStatus());
        if (Objects.nonNull(project.getPayStatus())) {
            inspectionInfoVo.setPayStatusName(PayStatusEnum.getEnum(project.getPayStatus()) == null ? "":
                PayStatusEnum.getEnum(project.getPayStatus()).getDisplay());
        }

        // set project cancel status
        if (ProcessStatusEnum.CANCELED == ProcessStatusEnum.getEnum(project.getLatestStatus())){
            inspectionInfoVo.setCancelStatus(project.getLatestStatus());
        }

        return inspectionInfoVo;
    }

    public static ProjectServiceTypeVo toProjectServiceTypeVo(Project project) {
        if(project == null) {
            return null;
        }
        ProjectServiceTypeVo serviceTypeVo = new ProjectServiceTypeVo();
        serviceTypeVo.setProjectId(project.getProjectId());
        Optional.ofNullable(project.getServiceType()).ifPresent(e -> {
            serviceTypeVo.setServiceType(e);
            serviceTypeVo.setServiceName(ProjectServiceTypeEnum.getEnum(e) == null ? "" :ProjectServiceTypeEnum.getEnum(e).getDisplay());
        });
        return serviceTypeVo;
    }

    public static BeesPilotProjectVo toBeesPilotVo(BeesPilotProjectDto project) {
        if(project == null) {
            return null;
        }
        BeesPilotProjectVo beesPilotProjectVo = new BeesPilotProjectVo();
        BeanUtils.copyProperties(project, beesPilotProjectVo);
        Integer claimType = project.getClaimType();
        beesPilotProjectVo.setInspectionPurposeTypeCode(InspectionPurposeTypeEnum.getInspectionPurposeTypeCode(claimType));
        beesPilotProjectVo.setInspectionPurposeTypeName(InspectionPurposeTypeEnum.getInspectionPurposeTypeName(claimType));
        CheckStatusEnum checkStatusEnum = CheckStatusEnum.getEnum(project.getCheckStatus());
        int checkStatus = 0;
        if (checkStatusEnum == CheckStatusEnum.CHECK_IN) {
            checkStatus = ProjectStatusEnum.TASK_CHECKED_IN.getCode();
        } else if (checkStatusEnum == CheckStatusEnum.CHECK_OUT) {
            checkStatus = ProjectStatusEnum.TASK_CHECKED_OUT.getCode();
        }
        beesPilotProjectVo.setCheckStatus(checkStatus);
        beesPilotProjectVo.setCollectFinished(project.getCheckStatus() == CheckStatusEnum.CHECK_OUT.getCode()
            && project.isImageUploaded());
        return beesPilotProjectVo;
    }

    public static BeesPilotProjectInfo toProjectInfo(Project project, Company company) {
        BeesPilotProjectInfo beesPilotProjectInfo = new BeesPilotProjectInfo();
        beesPilotProjectInfo.setAssetOwnerEmail(project.getAssetOwnerEmail());
        beesPilotProjectInfo.setAssetOwnerName(project.getAssetOwnerName());
        beesPilotProjectInfo.setAssetOwnerPhone(project.getAssetOwnerPhone());
        beesPilotProjectInfo.setInspectionTime(project.getInspectionTime());
        Integer serviceType = project.getServiceType();
        beesPilotProjectInfo.setServiceName(ProjectServiceTypeEnum.getEnum(serviceType) == null ?
            "" : ProjectServiceTypeEnum.getEnum(serviceType).getDisplay());
        String claimNote = "";
        if (Objects.nonNull(project.getServiceType()) &&
            (StringUtils.isBlank(project.getClaimNote()) || !project.getClaimNote().contains("serviceType"))) {
            claimNote = "serviceType: " + ProjectServiceTypeEnum.getEnum(project.getServiceType()).getDisplay() + "\r\n";
        }
        if (StringUtils.isNotBlank(project.getSpecialInstructions()) &&
            (StringUtils.isBlank(project.getClaimNote()) || !project.getClaimNote().contains("specialInstructions"))) {
            claimNote = claimNote + "specialInstructions: " + project.getSpecialInstructions() + "\r\n";
        }
        if (StringUtils.isNotBlank(project.getSpecialInstructionComments()) &&
            (StringUtils.isBlank(project.getClaimNote()) || !project.getClaimNote().contains("specialInstructionComments"))) {
            claimNote = claimNote + "specialInstructionComments: " + project.getSpecialInstructionComments() + "\r\n";
        }
        if (StringUtils.isNotBlank(project.getClaimNote())) {
            claimNote = claimNote + project.getClaimNote();
        }
        beesPilotProjectInfo.setClaimNotes(claimNote);
        if (company != null) {
            beesPilotProjectInfo.setInsuredBy(company.getCompanyName());
        }
        return beesPilotProjectInfo;
    }

    private static String handleProjectServiceNote(String claimNote) {
        if (StringUtils.isBlank(claimNote)) {
            return "";
        }
        List<String> noteList = Arrays.stream(StringUtils.split(claimNote, "\n"))
            .filter(note -> !StringUtils.startsWith(note, "serviceType"))
            .collect(Collectors.toList());
        return StringUtils.join(noteList, "\n");
    }



    public static void main(String[] args) {
        String str = "serviceType: test\n";
        System.out.println(handleProjectServiceNote(str));
        String note = "serviceType: test\r\n";
        System.out.println(handleProjectServiceNote(note));
        String test = "serviceType: test\nspecialInstructionComments:hahahaha";
        System.out.println(handleProjectServiceNote(test));
        String test2 = "serviceType: test\r\nspecialInstructionComments:hahahaha";
        System.out.println(handleProjectServiceNote(test2));
    }

}
