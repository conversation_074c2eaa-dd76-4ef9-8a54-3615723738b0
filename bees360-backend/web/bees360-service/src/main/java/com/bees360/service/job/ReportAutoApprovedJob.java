package com.bees360.service.job;

import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.User;
import com.bees360.schedule.exception.RetryableException;
import com.bees360.schedule.job.retry.RetryableJob;
import com.bees360.schedule.job.retry.RetryableOperation;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandlers;
import com.bees360.service.ProjectReportFileService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Approve 指定的报告，失败时进行重试。
 */
@Slf4j
public class ReportAutoApprovedJob extends RetryableJob {

    @Autowired
    private ProjectReportFileService projectReportFileService;

    @Setter
    private long projectId;

    @Setter
    private String reportId;

    public static JobKey createJobKey(long projectId, String reportId) {
        String keyFormat = "%s@%s@%s";
        String keyName = keyFormat.formatted(projectId, reportId, System.currentTimeMillis());
        return new JobKey(keyName, ReportAutoApprovedJob.class.getSimpleName());
    }

    public static JobDetail createJobDetail(long projectId, String reportId) {
        Class<? extends Job> jobClass = ReportAutoApprovedJob.class;

        JobDataMap jobData = new JobDataMap();
        jobData.put("projectId", projectId);
        jobData.put("reportId", reportId);

        String description = "auto approve report(%s) of project (%s)".formatted(reportId, projectId);

        return JobBuilder.newJob(jobClass)
            .withIdentity(createJobKey(projectId, reportId))
            .usingJobData(jobData)
            .withDescription(description)
            .requestRecovery()
            .storeDurably()
            .build();
    }

    @Override
    protected RetryableOperation getRetryableOperation() {
        return (ctx) -> {
            ProjectReportFile reportFile = projectReportFileService.getById(projectId, reportId);
            if (reportFile == null) {
                log.info("abort: report({}) of project({}) has been deleted.", reportId, projectId);
                return;
            }
            try {
                projectReportFileService.doApproveReport(projectId, User.AI_ID, reportId);
            } catch (Exception e) {
                String message = "Fail to approve report(" + reportId + ") of project(" + projectId + ")";
                throw new RetryableException(message, e);
            }
            log.info("finish to approve report({}) of project({})", reportId, projectId);
        };
    }

    @Override
    protected RetryExceptionHandler getRetryExceptionHandler() {
        // 重试10次
        return RetryExceptionHandlers.getSimpleRetryExceptionHandler(30);
    }

}
