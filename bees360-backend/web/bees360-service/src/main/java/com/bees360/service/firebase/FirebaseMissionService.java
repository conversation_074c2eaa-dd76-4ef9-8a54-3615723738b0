package com.bees360.service.firebase;

import com.bees360.base.exception.ServiceException;
import com.bees360.job.registry.SerializableFirebaseIBeesMission;
import com.bees360.job.registry.SerializableFirebaseMission;

/**
 * mission相关业务
 */
public interface FirebaseMissionService {

    /**
     * 将飞手上传的图片等数据同步到web端
     *
     * @param mission mission数据
     * @param missionId mission表ID
     */
    void handlePilotMission(SerializableFirebaseMission mission, String missionId);

    void handleMissionCompleted(
        SerializableFirebaseMission mission, String missionPath, long pilotId,long completedBy, long projectId, Long updateTime);

    void handleMissionCompletedStuck(String missionPath, long pilotId, long projectId);

    void handleMissionCompletedStuck(String missionPath, long pilotId, long projectId, Long updateTime);

    void handleIBeesMissionCompleted(String missionPath, long projectId);

    void handleMissionCheckOut(String missionPath, String pilotId, long projectId, Long statusUpdateTime);

    /**
     * 将IBees上传的图片等数据同步到web端
     *  @param mission mission数据
     * @param missionId mission主键ID
     */
    void handleIBeesMission(SerializableFirebaseIBeesMission mission, String missionId);

    void handleIBeesMissionImage(String missionPath, long projectId);

    void handleMissionImage(String missionPath, long pilotId, long projectId) throws ServiceException;
}
