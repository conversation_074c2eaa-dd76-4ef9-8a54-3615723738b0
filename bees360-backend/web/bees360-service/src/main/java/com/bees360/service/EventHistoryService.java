package com.bees360.service;

import java.util.List;
import java.util.Map;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.EventHistory;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.vo.EventHistoryStatusLogVo;
import com.bees360.entity.vo.HistoryLogVo;
import com.bees360.util.DateUtil;

public interface EventHistoryService {

    /**
     * insert an event history to the project, and update the latestStatus of the project
     *
     * @param history a complete event history with a exist project id
     */
    void insertHistoryToProject(EventHistory history);

    void insertHistoryToProject(long projectId, long userId, ProjectStatusEnum status, String description)
        throws ServiceException;

    //	for structure of table, the following method can not be implemented
//	EventHistory getLatestEventHistory(long projectId, long userId, RoleEnum role) throws ServiceException;
//	for structure of table, the following method can not be implemented
//	List<EventHistory> getEventHistory(long projectId, RoleEnum role) throws ServiceException;
    boolean existStatus(long projectId, ProjectStatusEnum finishedStatus) throws ServiceException;

    Map<Integer, EventHistory> listHistoryIn(long projectId, List<ProjectStatusEnum> historyTypeIds) throws ServiceException;

    /**
     * get the EventHistory of the Project according to the status
     *
     * @throws ServiceException
     */
    EventHistory getByStatus(long projectId, ProjectStatusEnum status) throws ServiceException;

    EventHistory getLastEventHistory(long projectId);

    /**
     * select all event histories in specified project, and return the result order by createdTime desc
     *
     * @param projectId specify the project
     * @return a event history list ordered by createdTime desc
     * @throws ServiceException
     */
    List<HistoryLogVo> listHistoryLogs(long projectId) throws ServiceException;

    ProjectStatusEnum getLatestADStatus(long projectId, long userId) throws ServiceException;

    ProjectStatusEnum getLatestRealtimeADStatus(long projectId, long userId) throws ServiceException;

    List<EventHistory> listHistorySortByCreateTimeDesc(long projectId) throws ServiceException;

    List<EventHistoryStatusLogVo> listLogTimeLineForBeesGo(long projectId) throws ServiceException;

    /**
     * insert an event history to the project, and update the latestStatus of the project
     * @param projectStatusEnum a complete event history with a exist project id
     * @param projectId project id
     * @param userId userId
     * @throws ServiceException
     */
    default void insertHistoryToProject(ProjectStatusEnum projectStatusEnum, long projectId, long userId) throws ServiceException {
        insertHistoryToProject(getEventHistory(projectStatusEnum, projectId, userId));
    }

    default void insertHistoryToProject(ProjectStatusEnum projectStatusEnum, long projectId, long userId, long createTime) throws ServiceException {
        insertHistoryToProject(getEventHistory(projectStatusEnum, projectId, userId, createTime));
    }

    default EventHistory getEventHistory(ProjectStatusEnum projectStatusEnum, long projectId, long userId) {
        long now = DateUtil.getNow();
        return getEventHistory(projectStatusEnum, projectId, userId, now);
    }

    default EventHistory getEventHistory(ProjectStatusEnum projectStatusEnum, long projectId, long userId, long createTime) {
        EventHistory history = new EventHistory();
        history.setProjectId(projectId);
        history.setUserId(userId);
        history.setStatus(projectStatusEnum.getCode());
        history.setStatusTime(createTime);
        history.setModifiedBy(userId);
        history.setCreatedTime(createTime);
        history.setStatusName(projectStatusEnum.getDisplay());
        return history;
    }

    default EventHistory getEventHistory(ProjectStatusEnum projectStatusEnum, long projectId, long userId, String desc) {
        long now = DateUtil.getNow();
        EventHistory history = new EventHistory();
        history.setProjectId(projectId);
        history.setUserId(userId);
        history.setStatus(projectStatusEnum.getCode());
        history.setStatusTime(now);
        history.setModifiedBy(userId);
        history.setCreatedTime(now);
        history.setDescription(desc);
        return history;
    }

    default EventHistory getEventHistory(ProjectStatusEnum projectStatusEnum, long projectId,
                                         long userId, String title, String desc) {
        long now = DateUtil.getNow();
        EventHistory history = new EventHistory();
        history.setProjectId(projectId);
        history.setUserId(userId);
        history.setStatus(projectStatusEnum.getCode());
        history.setStatusTime(now);
        history.setModifiedBy(userId);
        history.setCreatedTime(now);
        history.setDescription(desc);
        history.setTitle(title);
        return history;
    }

    void insertHistoryByImageUpload(long projectId, long userId, int imageCount) throws ServiceException;
}
