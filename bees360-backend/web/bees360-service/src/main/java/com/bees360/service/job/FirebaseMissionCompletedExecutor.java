package com.bees360.service.job;

import com.bees360.atomic.LockProvider;
import com.bees360.entity.User;
import com.bees360.job.registry.FirebaseMissionCompleted;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseMissionService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.user.UserKeyProvider;
import com.bees360.user.UserProvider;
import com.bees360.util.user.UserAssemble;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;

import static com.bees360.service.job.FirebaseMissionChangedExecutor.translateExceptionAndThrow;

@Log4j2
@Component
public class FirebaseMissionCompletedExecutor
        extends AbstractJobExecutor<FirebaseMissionCompleted> {

    private final FirebaseMissionService firebaseMissionService;
    private final FirebaseService firebaseService;
    private final LockProvider missionLockProvider;
    private final UserKeyProvider userKeyProvider;
    private final UserProvider userProvider;

    public FirebaseMissionCompletedExecutor(
            FirebaseMissionService firebaseMissionService,
            UserKeyProvider userKeyProvider,
            UserProvider userProvider,
            FirebaseService firebaseService,
            LockProvider missionLockProvider) {
        this.firebaseMissionService = firebaseMissionService;
        this.firebaseService = firebaseService;
        this.userKeyProvider = userKeyProvider;
        this.userProvider = userProvider;
        this.missionLockProvider = missionLockProvider;

        log.info(
                "Created '{}(firebaseMissionService={},firebaseService={},missionLockProvider={})",
                this,
                this.firebaseMissionService,
                this.firebaseService,
                this.missionLockProvider);
    }

    @Override
    protected void handle(FirebaseMissionCompleted mission) throws IOException {
        var id = mission.getMission().getId();
        try (var ignored = missionLockProvider.lock(id)) {
            long pilotId = firebaseService.toWebUserId(mission.getPilotId());
            var completedBy =
                    Optional.ofNullable(mission.getCompletedBy())
                            .map(
                                    u ->
                                            Optional.ofNullable(userKeyProvider.findUserByKey(u))
                                                    .orElseGet(() -> userProvider.findUserById(u)))
                            .map(UserAssemble::toWebUser)
                            .map(User::getUserId)
                            .orElse(pilotId);
            firebaseMissionService.handleMissionCompleted(
                    mission.getMission(),
                    mission.getMissionPath(),
                    pilotId,
                    completedBy,
                    Long.parseLong(mission.getProjectId()),
                    mission.getCompletedAt());
            log.info("Successfully handle completed mission '{}'.", mission);
        } catch (RuntimeException e) {
            log.warn("Failed to handle mission completed event for '{}'.", mission, e);
            translateExceptionAndThrow(e);
        }
    }
}
