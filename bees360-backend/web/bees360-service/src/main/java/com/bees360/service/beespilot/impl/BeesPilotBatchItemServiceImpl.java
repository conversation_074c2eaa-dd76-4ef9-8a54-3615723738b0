package com.bees360.service.beespilot.impl;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.BeesPilotBatch;
import com.bees360.entity.BeesPilotBatchItem;
import com.bees360.entity.BeesPilotBatchItemVo;
import com.bees360.entity.enums.PilotBatchEventTypeEnum;
import com.bees360.entity.firebase.BatchStatusEnum;
import com.bees360.mapper.BeesPilotBatchItemMapper;
import com.bees360.service.beespilot.BeesPilotBatchItemService;
import com.bees360.service.beespilot.BeesPilotBatchService;
import com.bees360.service.event.pilot.PilotBatchChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BeesPilotBatchItemServiceImpl implements BeesPilotBatchItemService {


    @Autowired
    private BeesPilotBatchItemMapper beesPilotBatchItemMapper;

    @Autowired
    private BeesPilotBatchService beesPilotBatchService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Override
    public List<BeesPilotBatchItem> listByProjectIds(List<Long> projectIds) {
        return beesPilotBatchItemMapper.listByProjectIds(projectIds);
    }

    @Override
    public List<BeesPilotBatchItemVo> listByProjectIdsWithBatchInfo(List<Long> projectIds) {
        List<BeesPilotBatchItemVo> vos = beesPilotBatchItemMapper.listByProjectIdsWithBatchInfo(projectIds);
        vos.forEach(vo -> vo.setPendingAcceptance(vo.getStatus() == BatchStatusEnum.PENDING.getStatus()));
        return vos;
    }

    @Override
    public BeesPilotBatchItemVo findByProjectId(long projectId) {
        BeesPilotBatchItemVo vo = listByProjectIdsWithBatchInfo(Collections.singletonList(projectId))
            .stream().findFirst().orElse(null);
        if (Objects.isNull(vo)) {
            return null;
        }
        List<BeesPilotBatchItem> items = beesPilotBatchItemMapper.listByBatchNo(vo.getBatchNo());
        vo.setProjectIds(items.stream().map(BeesPilotBatchItem::getProjectId).collect(Collectors.toList()));
        return vo;
    }

    @Override
    public BeesPilotBatchItem findByProjectIdWithoutDeletedCheck(long projectId) {
        return listByProjectIdsWithoutDeletedCheck(Collections.singletonList(projectId))
            .stream().findFirst().orElse(null);
    }

    @Override
    public void deleteByBatchNo(String batchNo) {
        beesPilotBatchItemMapper.deleteByBatchNo(batchNo);
    }

    @Override
    public void deleteByProjectIds(List<Long> projectIds) {
        List<BeesPilotBatchItem> itemList = listByProjectIds(projectIds);
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        beesPilotBatchItemMapper.deleteByProjectIds(projectIds);
        Set<String> batchNoSet = itemList.stream().map(BeesPilotBatchItem::getBatchNo).collect(Collectors.toSet());
        batchNoSet.forEach(o -> {
            try {
                if (CollectionUtils.isEmpty(listByBatchNo(o))) {
                    // 如果子单不存在了，删除主单
                    beesPilotBatchService.deleteByBatchNo(o);
                }
            } catch (ServiceException e) {
                log.error("deleteByProjectIds batchNo:{}, error:{}, msg:{}", o, e.getStackTrace(), e.getMessage());
            }
        });
    }

    @Override
    public List<BeesPilotBatchItem> listByBatchNo(String batchNo) throws ServiceException {
        if (StringUtils.isBlank(batchNo)) {
            throw new ServiceException(MessageCode.PARAM_INVALID);
        }
        return beesPilotBatchItemMapper.listByBatchNo(batchNo);
    }

    @Override
    public List<BeesPilotBatchItem> listByBatchNoWithoutDeletedCheck(String batchNo) throws ServiceException {
        if (StringUtils.isBlank(batchNo)) {
            throw new ServiceException(MessageCode.PARAM_INVALID);
        }
        return beesPilotBatchItemMapper.listByBatchNoWithoutDeletedCheck(batchNo);
    }

    @Override
    public void addBeesPilotBatchItem(List<BeesPilotBatchItem> itemList) {
        beesPilotBatchItemMapper.batchInsert(itemList);
    }

    @Override
    public List<BeesPilotBatchItem> listByProjectIdsWithoutDeletedCheck(List<Long> projectIds) {
        return beesPilotBatchItemMapper.listByProjectIdsWithoutDeletedCheck(projectIds);
    }

    @Override
    public void updateBatchDeletedStatus(long projectId, long pilotId, Boolean isDeleted) throws ServiceException {
        String batchNo = null;
        if (isDeleted) {
            batchNo = Optional.ofNullable(findByProjectId(projectId)).map(BeesPilotBatchItemVo::getBatchNo).orElse(null);
        } else {
            //这是project删除的时候只有projectId，没有batchNo；
            List<BeesPilotBatchItem> items = listByProjectIdsWithoutDeletedCheck(Arrays.asList(projectId));
            if (CollectionUtils.isNotEmpty(items))  {
                batchNo = items.stream().max(Comparator.comparing(BeesPilotBatchItem::getId))
                    .map(BeesPilotBatchItem::getBatchNo).orElse(null);
            }
        }

        beesPilotBatchItemMapper.updateBatchItemDeletedStatusByProjectIds(batchNo, Collections.singletonList(projectId),  isDeleted);
        PilotBatchEventTypeEnum typeEnum = PilotBatchEventTypeEnum.DELETED;
        if (!isDeleted) {
            typeEnum = PilotBatchEventTypeEnum.RECOVER;
        }
        if (StringUtils.isBlank(batchNo)) {
            return;
        }
        BeesPilotBatch batch = beesPilotBatchService.getByBatchNoWithoutDeletedCheck(batchNo);

        publisher.publishEvent(new PilotBatchChangeEvent(this, batch.getUserId(), batch,
            Collections.singletonList(projectId), typeEnum));

        if (isDeleted && CollectionUtils.isNotEmpty(batch.getBatchItems()) && batch.getBatchItems().stream().anyMatch(o -> !o.isDeleted())) {
            //当前item project删除，batch如果有其他item，则不删除主单
            return;
        }
        beesPilotBatchService.updatePilotBatchDeletedStatus(batchNo, isDeleted);

    }

    @Override
    public String getBatchNo(long projectId) {
        List<BeesPilotBatchItem> items = listByProjectIdsWithoutDeletedCheck(Arrays.asList(projectId));
        if (CollectionUtils.isNotEmpty(items))  {
            return items.stream().max(Comparator.comparing(BeesPilotBatchItem::getId))
                    .map(BeesPilotBatchItem::getBatchNo).orElse(null);
        }
        return null;
    }
}
