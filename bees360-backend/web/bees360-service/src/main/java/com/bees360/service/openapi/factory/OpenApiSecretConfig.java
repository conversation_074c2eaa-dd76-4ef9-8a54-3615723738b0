package com.bees360.service.openapi.factory;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiSecretConfig {

    @Bean
    @ConditionalOnMissingBean(ClientIdGenerator.class)
    public ClientIdGenerator clientIdGenerator(){
        return new DefaultClientIdGenerator();
    }

    @Bean
    @ConditionalOnMissingBean(ClientSecretGenerator.class)
    public ClientSecretGenerator clientSecretGenerator(){
        return new DefaultClientSecretGenerator();
    }

    @Bean
    public OpenApiClientSecretFactory clientSecretFactory(){
        return new OpenApiClientSecretFactory(clientIdGenerator(), clientSecretGenerator());
    }
}
