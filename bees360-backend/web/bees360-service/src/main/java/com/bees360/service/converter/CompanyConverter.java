package com.bees360.service.converter;

import com.bees360.entity.Company;
import com.bees360.entity.vo.CompanyCard;

/**
 * <AUTHOR>
 * @date 2020/02/28 22:24
 */
public class CompanyConverter {

    public static CompanyCard toCompanyCard(Company company) {
        if(company == null) {
            return null;
        }
        CompanyCard companyCard = new CompanyCard();
        companyCard.setCompanyId(company.getCompanyId());
        companyCard.setCompanyName(company.getCompanyName());
        companyCard.setEmail(company.getEmail());
        companyCard.setPhone(company.getPhone());
        return companyCard;
    }
}
