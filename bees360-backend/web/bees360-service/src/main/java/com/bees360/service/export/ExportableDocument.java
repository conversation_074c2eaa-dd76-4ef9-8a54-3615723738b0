package com.bees360.service.export;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ExportableDocument {

    public static final String TYPE_REPORT = "REPORT";
    public static final String TYPE_PHOTO = "IMAGE";

    private String id;

    private String type;
    /**
     * 原文件名
     */
    @NotNull
    private String originalName = "";
    /**
     * 文件重命名
     */
    @NotNull
    private String fileName = "";
    /**
     * 对图片的描述
     */
    @NotNull
    private String description = "";
    /**
     * 标签，可用于分类
     */
    @NotNull
    private List<String> tags = new ArrayList<>();
    /**
     * 文件位置
     */
    @NotNull
    private String location = "";
    /**
     * 文件大小
     */
    private long size;
    /**
     * 创建时间
     */
    private long createTime;

    private String resourceSrc;
}
