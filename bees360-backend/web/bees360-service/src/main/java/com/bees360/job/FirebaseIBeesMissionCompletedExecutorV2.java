package com.bees360.job;

import com.bees360.job.registry.FirebaseIBeesMissionCompleted;
import com.bees360.job.registry.FirebaseIBeesMissionCompletedV2;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseMissionService;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.beanutils.BeanUtils;
import java.io.IOException;
import static com.bees360.service.util.FirebaseExceptionTranslation.translateExceptionAndThrow;

@Log4j2
public class FirebaseIBeesMissionCompletedExecutorV2
        extends AbstractJobExecutor<FirebaseIBeesMissionCompletedV2> {

    private final FirebaseMissionService firebaseMissionService;

    public FirebaseIBeesMissionCompletedExecutorV2(FirebaseMissionService firebaseMissionService) {
        this.firebaseMissionService = firebaseMissionService;

        log.info("Created '{}(firebaseMissionService={})", this, this.firebaseMissionService);
    }

    @Override
    protected void handle(FirebaseIBeesMissionCompletedV2 missionV2) throws IOException {
        FirebaseIBeesMissionCompleted mission;
        try{
            mission = convert(missionV2);
        } catch (IllegalArgumentException e){
            log.error("Failed to handle ibees mission '{}'.", missionV2, e);
            return;
        }

        try {
            firebaseMissionService.handleIBeesMissionCompleted(
                    mission.getMissionPath(),
                    Long.parseLong(mission.getProjectId()));
            log.info("Successfully handle completed IBees mission '{}'.", mission);
        } catch (RuntimeException e) {
            log.warn("Failed to handle ibees mission '{}'", mission, e);
            translateExceptionAndThrow(e);
        }
    }

    private FirebaseIBeesMissionCompleted convert(FirebaseIBeesMissionCompletedV2 missionV2) throws IllegalArgumentException {
        FirebaseIBeesMissionCompleted mission = new FirebaseIBeesMissionCompleted();
        try {
            BeanUtils.copyProperties(mission, missionV2);
        } catch (Exception e) {
            throw new IllegalArgumentException(
                "Failed to convert FirebaseIBeesMissionCompletedV2 to FirebaseIBeesMissionCompleted.", e);
        }
        return mission;
    }
}
