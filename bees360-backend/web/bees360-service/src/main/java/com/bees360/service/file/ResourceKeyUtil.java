package com.bees360.service.file;

import com.bees360.resource.ResourceContext;
import org.thymeleaf.util.StringUtils;

import java.net.URI;

public class ResourceKeyUtil {

    private ResourceContext resourceContext;

    public ResourceKeyUtil(ResourceContext resourceContext) {
        this.resourceContext = resourceContext;
    }

    public String urlToKey(String url) {
        if (isUrl(url)) {
            return resourceContext.getRelativePath(URI.create(url));
        }
        return url;
    }

    public String toKey(String urlOrKey) {
        if (isUrl(urlOrKey)) {
            return urlToKey(urlOrKey);
        }
        return urlOrKey;
    }

    public boolean isUrl(String urlOrKey) {
        return StringUtils.startsWith(urlOrKey, "http://") || StringUtils.startsWith(urlOrKey, "https://");
    }

    public boolean isKey(String urlOrKey) {
        return !isUrl(urlOrKey);
    }

    public String parseName(String keyOrUrl) {
        if(keyOrUrl == null) {
            return "";
        }
        int index = keyOrUrl.lastIndexOf('/');
        if(index < 0) {
            return keyOrUrl;
        }
        return keyOrUrl.substring(index + 1);
    }
}
