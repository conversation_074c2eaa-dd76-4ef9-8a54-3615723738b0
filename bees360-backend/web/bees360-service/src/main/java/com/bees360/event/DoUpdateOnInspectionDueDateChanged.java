package com.bees360.event;

import com.bees360.entity.ProjectInspectionSchedule;
import com.bees360.entity.User;
import com.bees360.event.registry.InspectionDueDateChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectInspectionScheduleMapper;
import com.bees360.service.ActivityService;
import com.bees360.service.ProjectService;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;

/**
 * 监听检查截止日期变更事件并更新项目检查计划和相关服务
 */
@Log4j2
public class DoUpdateOnInspectionDueDateChanged extends AbstractNamedEventListener<InspectionDueDateChanged> {

    private final ProjectInspectionScheduleMapper projectInspectionScheduleMapper;

    private final ActivityService activityService;

    private final ProjectService projectService;

    public DoUpdateOnInspectionDueDateChanged(@NonNull ProjectInspectionScheduleMapper projectInspectionScheduleMapper,
                                              @NonNull ActivityService activityService,
                                              @NonNull ProjectService projectService) {
        this.projectInspectionScheduleMapper = projectInspectionScheduleMapper;
        this.activityService = activityService;
        this.projectService = projectService;
        log.info("Create listener {}", this.getClass().getName());
    }

    @Override
    public void handle(InspectionDueDateChanged inspectionDueDateChanged) throws IOException {
        Long dueDate = inspectionDueDateChanged.getDueDate();
        long projectId = Long.parseLong(inspectionDueDateChanged.getProjectId());
        ProjectInspectionSchedule projectInspectionSchedule = projectInspectionScheduleMapper.getByProjectId(projectId);
        Long oldDueDate = Optional.ofNullable(projectInspectionSchedule)
            .map(ProjectInspectionSchedule::getDueDate).orElse(null);
        if (!Objects.equals(dueDate, oldDueDate)) {
            projectInspectionScheduleMapper.updateDueDate(projectId, dueDate);
            projectService.updateInspectionDueDate(User.BEES_PILOT_SYSTEM, projectId, dueDate);
            activityService.dueDateChanged(projectId, inspectionDueDateChanged.getUpdatedBy(),
                dueDate, oldDueDate, inspectionDueDateChanged.getReason());
        }
    }
}
