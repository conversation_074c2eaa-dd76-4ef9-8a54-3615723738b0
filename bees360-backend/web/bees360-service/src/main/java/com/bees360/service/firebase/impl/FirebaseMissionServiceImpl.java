package com.bees360.service.firebase.impl;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.base.exception.ServiceException;
import com.bees360.commons.firebasesupport.service.RemoteConfigService;
import com.bees360.entity.Member;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.User;
import com.bees360.entity.enums.ImagePartialViewTypeEnum;
import com.bees360.entity.enums.PipelineTaskEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.RemoteConfigParameter;
import com.bees360.entity.firebase.CategoryToTabRemoteConfig;
import com.bees360.entity.firebase.FirebaseRoom;
import com.bees360.entity.firebase.MissionStateEnum;
import com.bees360.entity.firebase.TaskRemoteConfig;
import com.bees360.firebase.entity.FbCheckoutReason;
import com.bees360.firebase.entity.FirebaseTimeline;
import com.bees360.job.registry.SerializableFirebaseIBeesMission;
import com.bees360.job.registry.SerializableFirebaseMission;
import com.bees360.mapper.ProjectMapper;
import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.pipeline.Pipeline;
import com.bees360.pipeline.PipelineService;
import com.bees360.pipeline.PipelineTask;
import com.bees360.project.ProjectIIManager;
import com.bees360.service.MemberService;
import com.bees360.service.ProjectImageService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.beespilot.BeesPilotStatusService;
import com.bees360.service.firebase.FirebaseMissionService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.Iterables;
import com.bees360.util.user.UserAssemble;
import com.bees360.web.event.image.DroneImageUploadedEvent;
import com.bees360.web.event.image.ExteriorImageUploadedEvent;
import com.bees360.web.event.image.InteriorImageUploadedEvent;
import com.bees360.web.event.image.MobileImageUploadedEvent;
import com.bees360.web.event.project.InteriorDamageEvent;
import com.bees360.web.event.tag.TagMagicplanMissingAddedEvent;
import com.bees360.web.event.tag.TagMissingInteriorImagesEvent;
import com.google.cloud.firestore.Firestore;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.bees360.entity.enums.PipelineTaskEnum.CHECK_IN;
import static com.bees360.entity.enums.PipelineTaskEnum.CREATE_HOVER;
import static com.bees360.entity.enums.PipelineTaskEnum.CREATE_PLNAR;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_HOVER_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_INTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_PLNAR_IMAGES;
import static com.bees360.pipeline.Message.PipelineStatus.DONE;
import static com.bees360.pipeline.Message.PipelineStatus.ERROR;
import static com.bees360.pipeline.Message.PipelineStatus.IGNORED;
import static com.bees360.pipeline.Message.PipelineStatus.ONGOING;
import static com.bees360.util.user.Bees360UserUtils.FIREBASE_USER_PROVIDER_PREFIX;

/**
 * Mission 业务
 *
 * <p>包含PilotMission和IBeesMission
 */
@Log4j2
@Service
public class FirebaseMissionServiceImpl implements FirebaseMissionService {

    @Autowired private ProjectService projectService;
    @Autowired private Firestore firestore;
    @Autowired private ProjectStatusService projectStatusService;
    @Autowired private MemberService memberService;
    @Autowired private FirebaseService firebaseService;
    @Autowired private ProjectImageService projectImageService;
    @Autowired private BeesPilotStatusService beesPilotStatusService;
    @Autowired private ProjectMapper projectMapper;

    @Autowired private ActivityManager activityManager;
    @Autowired private RemoteConfigService remoteConfigService;
    @Autowired private ApplicationEventPublisher publisher;

    private static final String UPC_FORM_TASK_ID = "upc_claim_form";
    @Autowired private PipelineService pipelineService;
    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;
    @Autowired private ProjectIIManager projectIIManager;

    /**
     * 将飞手上传的图片等数据同步到web端
     *
     * <p>迁移 from FirebaseService
     *
     * @param mission mission数据
     * @param missionId mission表ID
     */
    @Override
    public void handlePilotMission(SerializableFirebaseMission mission, String missionId) {
        long projectId = Long.parseLong(mission.getProject().getProjectId());
        String missionPilotId = mission.getPilotId();
        // transfer userId to uid
        long pilotId = firebaseService.toWebUserId(missionPilotId);
        boolean isMissionActive = isMissionActive(pilotId, projectId);
        if (isMissionActive) {
            setPipelineTaskByMission(mission);
        }

        if (mission.getStatus() < MissionStateEnum.NEW.getCode()) {
            return;
        }
        log.info("Start to handle mission {} status {}", missionId, mission.getStatus());

        syncMissionBaseInfo(projectId, missionId);
        log.info("handle mission :{} with call result :{}", mission, mission.getCallRecord());
        if (!bees360FeatureSwitch.isDisableFirebaseSyncCallRecord()) {
            firebaseService.syncCallRecord(mission.getCallRecord(), projectId, pilotId, missionPilotId);
        }
        firebaseService.feedBackChanged(mission, missionId);
        try {
            // 同步 log 到 web
            List<FirebaseTimeline> timelines = mission.getTimeline();
            firebaseService.syncTimeline(projectId, timelines, pilotId);

            // 插入问卷调查
            firebaseService.syncQuizAnswers(
                    projectId,
                    firestore
                            .document(getMissionDocumentPath(missionId))
                            .collection(FirebaseService.QUIZ));

            // 同步checkout reason
            if (MissionStateEnum.COMPLETED.getCode() != mission.getStatus()) {
                // 插入checkoutReason
                List<FbCheckoutReason> checkoutReasons = mission.getCheckOutReason();
                try {
                    firebaseService.addCheckoutReason(projectId, pilotId, checkoutReasons);
                } catch (RuntimeException e) {
                    log.error("Sync mission '{}' checkout reason failed.", missionId, e);
                }
            }

            // 插入签名附件
            firebaseService.syncAttachFiles(projectId, missionId);
            syncUnsignedReason(projectId, mission.getCheckOutReason());

            // mission.getStatus() >= MissionStateEnum.COMPLETED_STUCK.getCode()时会有单独的图片同步
            // 做单独的图片同步是为了确保在项目状态变更时图片已经同步到了web端，这是因为项目状态同步到ai时需要将图片同步到ai端。
            // 实际上应该有单独的图片服务，提供统一的图片接口。
            if (mission.getStatus() < MissionStateEnum.COMPLETED_STUCK.getCode()
                    && !bees360FeatureSwitch.isDisableHandleMissionImage()) {
                handleMissionImage(
                        getMissionDocumentPath(missionId),
                        pilotId,
                        projectId);
            }

            setPipelineTaskStatus(mission, projectId);

            autoTagOperationTag(mission, projectId);
        } catch (ServiceException e) {
            String message = "Failed to handle mission '%s'".formatted(missionId);
            log.error(message, e);
        }
    }

    private void setPipelineTaskByMission(SerializableFirebaseIBeesMission mission) {
        long projectId = Long.parseLong(mission.getProject().getProjectId());
        String pipelineId = String.valueOf(projectId);
        Pipeline pipeline = pipelineService.findById(pipelineId);
        if (pipeline == null) {
            return;
        }

        List<String> missionTasks = mission.getTasks();
        Set<String> taskKeys =
            missionTasks.stream()
                        .map(this::getTaskKey)
                        .filter(Strings::isNotBlank)
                        .collect(Collectors.toSet());

        if (taskKeys.contains(TAKE_INTERIOR_IMAGES.getKey())) {
            publisher.publishEvent(
                    new InteriorDamageEvent(this, projectService.getById(projectId)));
            // pipeline maybe have changed after publish this event.
            pipeline = pipelineService.getById(pipelineId);
        }

        var userId = mission.getPilotId();
        var pipelineTaskOwnerMap =
            Iterables.toStream(pipeline.getTask())
                .collect(Collectors.toMap(PipelineTask::getKey, PipelineTask::getOwnerId));

        var pipelineTaskMap =
            Iterables.toStream(pipeline.getTask())
                .collect(Collectors.toMap(PipelineTask::getKey, PipelineTask::getStatus));
        // 由于deep link创建失败之后随时都可能再次创建成功,所以每次mission改变都需要检测deep link是否创建成功。
        checkDeepLinks(pipelineId, missionTasks, mission.getDeepLinks(), pipelineTaskMap);

        if (Objects.equals(mission.getAssignedTime(), mission.getLastUpdateTime())) {
            // set pipeline task to error if it is finished on mission create,
            // and then set check in to ongoing to reset these tasks.
            taskKeys.forEach(key -> setTaskErrorIfFinished(pipelineId, key, pipelineTaskMap));
            setTaskStatusIfNot(pipelineId, CHECK_IN.getKey(), pipelineTaskMap, ONGOING);
            return;
        }

        // check in
        if (Objects.equals(mission.getStatus(), MissionStateEnum.CHECK_IN.getCode())) {
            setTaskDoneIfNotFinished(pipelineId, CHECK_IN.getKey(), pipelineTaskMap);
        }

        // set task status to done
        var taskStatusMap =
            mission.getTaskStatus().entrySet().stream()
                .filter(task -> mission.getTasks().contains(task.getKey()))
                .filter(e -> Strings.isNotBlank(getTaskKey(e.getKey())))
                .collect(
                    Collectors.toMap(
                            k -> getTaskKey(k.getKey()),
                            Map.Entry::getValue,
                            (oldValue, newValue) -> oldValue && newValue));
        taskStatusMap.entrySet().stream()
                .filter(Map.Entry::getValue)
                .forEach(e -> setTaskStatusIfNot(pipelineId, e.getKey(), pipelineTaskMap, DONE));

        // rework
        if (Objects.equals(mission.getStatus(), MissionStateEnum.REWORK.getCode())) {
            // set pipeline task to error if it is finished on mission rework,
            // and then set check in to ERROR to reset these tasks.
            taskKeys.forEach(key -> setTaskErrorIfFinished(pipelineId, key, pipelineTaskMap));
            setTaskStatusIfNot(pipelineId, CHECK_IN.getKey(), pipelineTaskMap, ERROR);
        }
    }

    private void checkDeepLinks(
            String projectId,
            List<String> missionTaskKey,
            Map<String, String> deepLinks,
            Map<String, PipelineStatus> taskStatusMap) {
        Set<String> taskKeys =
                missionTaskKey.stream()
                        .map(this::getTaskKey)
                        .filter(Strings::isNotBlank)
                        .collect(Collectors.toSet());
        if (taskKeys.contains(TAKE_HOVER_IMAGES.getKey())
                || missionTaskKey.stream().anyMatch(s -> s.endsWith("hover"))) {
            if (Strings.isBlank(deepLinks.get("hover"))) {
                setTaskErrorIfNotFinished(projectId, CREATE_HOVER.getKey(), taskStatusMap);
            } else {
                setTaskStatusIfNot(projectId, CREATE_HOVER.getKey(), taskStatusMap, DONE);
            }
        }

        if (taskKeys.contains(TAKE_PLNAR_IMAGES.getKey())
                || missionTaskKey.stream().anyMatch(s -> s.endsWith("plnar"))) {
            if (Strings.isBlank(deepLinks.get("plnar"))) {
                setTaskErrorIfNotFinished(projectId, CREATE_PLNAR.getKey(), taskStatusMap);
            } else {
                setTaskStatusIfNot(projectId, CREATE_PLNAR.getKey(), taskStatusMap, DONE);
            }
        }
    }

    private void setTaskErrorIfFinished(
            String pipelineId, String taskKey, Map<String, PipelineStatus> statusMap) {
        if (Objects.equals(DONE, statusMap.get(taskKey))
                || Objects.equals(IGNORED, statusMap.get(taskKey))) {
            setTaskStatusIfNot(pipelineId, taskKey, statusMap, ERROR);
        }
    }

    private void setTaskErrorIfNotFinished(
        String pipelineId, String taskKey, Map<String, PipelineStatus> statusMap) {
        if (!Objects.equals(DONE, statusMap.get(taskKey))
                && !Objects.equals(IGNORED, statusMap.get(taskKey))) {
            setTaskStatusIfNot(pipelineId, taskKey, statusMap,  ERROR);
        }
    }

    private void setTaskDoneIfNotFinished(
        String pipelineId, String taskKey, Map<String, PipelineStatus> statusMap) {
        if (!Objects.equals(DONE, statusMap.get(taskKey))
            && !Objects.equals(IGNORED, statusMap.get(taskKey))) {
            setTaskStatusIfNot(pipelineId, taskKey, statusMap, DONE);
        }
    }

    private void setTaskStatusIfNot(
        String pipelineId, String taskKey, Map<String, PipelineStatus> statusMap, PipelineStatus target) {
        if (!statusMap.containsKey(taskKey)) {
            return;
        }
        if (!Objects.equals(target, statusMap.get(taskKey))) {
            setPipelineTaskStatus(pipelineId, taskKey, target);
        }
    }

    private String getTaskKey(String missionTaskId) {
        Map<String, TaskRemoteConfig> map =
                remoteConfigService
                        .getRemoteConfig(
                                TaskRemoteConfig.class, RemoteConfigParameter.TASK.getName())
                        .stream()
                        .collect(Collectors.toMap(TaskRemoteConfig::getTaskId, a -> a));

        if (map.containsKey(missionTaskId)) {
            return map.get(missionTaskId).getTaskKey();
        }

        map =
                remoteConfigService
                        .getRemoteConfig(
                                TaskRemoteConfig.class, RemoteConfigParameter.IBEES_TASK.getName())
                        .stream()
                        .collect(Collectors.toMap(TaskRemoteConfig::getTaskId, a -> a));

        if (map.containsKey(missionTaskId)) {
            return map.get(missionTaskId).getTaskKey();
        }
        log.warn("Cannot found pipeline task key for mission task '{}'", missionTaskId);
        return null;
    }

    private void setPipelineTaskStatus(SerializableFirebaseMission mission, long projectId) {
        if (mission.getStatus() != MissionStateEnum.COMPLETED_STUCK.getCode()) {
            return;
        }
        try {
            List<String> droneTaskId = remoteConfigService.getDroneTaskId();
            List<String> mobileTaskId = remoteConfigService.getMobileTaskId();
            boolean uploadedDrone =
                    mission.getTaskStatus().entrySet().stream()
                            .filter(e -> droneTaskId.contains(e.getKey()))
                            .allMatch(e -> Boolean.TRUE.equals(e.getValue()));
            boolean uploadedMobile =
                    mission.getTaskStatus().entrySet().stream()
                            .filter(e -> mobileTaskId.contains(e.getKey()))
                            .allMatch(e -> Boolean.TRUE.equals(e.getValue()));
            if (uploadedDrone) {
                publisher.publishEvent(new DroneImageUploadedEvent(this, projectId));
            }
            if (uploadedMobile) {
                publisher.publishEvent(new MobileImageUploadedEvent(this, projectId));
            }
        } catch (Exception e) {
            log.error("pipeline task status update error missionId {}", mission.getId());
        }
    }

    /** 当符合条件时自动给project打上标签 */
    private void autoTagOperationTag(SerializableFirebaseMission mission, long projectId) {
        // 只有当400状态时检测，即完成所有任务
        if (mission.getStatus() != MissionStateEnum.COMPLETED.getCode()) {
            return;
        }
        try {
            // 除了roof only 和exterior, mission任务存在interior任务时，检测是否存在interior图片，不存在将打上Missing interior
            // images标签
            var taskIdMapConfig = remoteConfigService.getTaskIdMapRemoteConfig();
            var tasks = mission.getTasks();

            missingInteriorImages(projectId, taskIdMapConfig.getInteriorTaskIdList(), tasks);
            magicplanTag(mission, projectId, taskIdMapConfig.getMagicplanTaskIdList(), tasks);
        } catch (Exception e) {
            log.error("Firebase mission auto tag failed, projectId {}", projectId, e);
        }
    }

    /** 自动打missing interior images 标签 */
    private void missingInteriorImages(
            long projectId, List<String> InteriorTaskIdList, List<String> tasks) {
        log.info("missing interior images handle project {}", projectId);
        var project = projectService.getById(projectId);
        List<Integer> serviceType =
                List.of(
                        ProjectServiceTypeEnum.ROOF_ONLY_UNDERWRITING.getCode(),
                        ProjectServiceTypeEnum.EXTERIOR_UNDERWRITING.getCode());
        boolean matchType = !serviceType.contains(project.getServiceType());
        boolean hasTask = tasks.stream().anyMatch(InteriorTaskIdList::contains);
        if (matchType && hasTask) {
            boolean existInterior =
                    projectImageService.existByPartialType(
                            projectId, ImagePartialViewTypeEnum.getAllInteriorType());
            if (existInterior) {
                publisher.publishEvent(new TagMissingInteriorImagesEvent(this, projectId, true));
            } else {
                publisher.publishEvent(new TagMissingInteriorImagesEvent(this, projectId, false));
            }
        }
    }

    /** 自动打magicplan missing标签 */
    private void magicplanTag(
            SerializableFirebaseMission mission,
            long projectId,
            List<String> magicplanTaskIdList,
            List<String> tasks) {
        log.info("missing magicplan handle project {}", projectId);
        boolean hasMagicplan = tasks.stream().anyMatch(magicplanTaskIdList::contains);
        if (hasMagicplan) {
            var taskStatus = mission.getTaskStatus();
            var magicplanFinish =
                    taskStatus.entrySet().stream()
                            .filter(e -> magicplanTaskIdList.contains(e.getKey()))
                            .map(Map.Entry::getValue)
                            .allMatch(Boolean.TRUE::equals);
            if (!magicplanFinish) {
                publisher.publishEvent(new TagMagicplanMissingAddedEvent(this, projectId));
            }
        }
    }

    /**
     * 将IBees上传的图片等数据同步到web端
     *
     * <p>迁移 from FirebaseService
     *
     * @param mission mission数据
     * @param missionId mission主键ID
     */
    @Override
    public void handleIBeesMission(SerializableFirebaseIBeesMission mission, String missionId) {
        log.info("Start to handle IBeesMission {} status {}", missionId, mission.getStatus());
        setPipelineTaskByMission(mission);
        long projectId = Long.parseLong(mission.getProject().getProjectId());
        // 同步 log 到 web
        List<FirebaseTimeline> timelines = mission.getTimeline();
        firebaseService.syncTimeline(projectId, timelines, User.BEES_PILOT_SYSTEM);

        if (MissionStateEnum.COMPLETED_STUCK.getCode() == mission.getStatus()
                && !bees360FeatureSwitch.isDisableHandleIbeesMissionImage()) {
            handleIBeesMissionImage(getIBeesMissionDocumentPath(missionId), projectId);
        }
        // 插入问卷调查
        firebaseService.syncQuizAnswers(
                projectId,
                firestore
                        .document(getIBeesMissionDocumentPath(missionId))
                        .collection(FirebaseService.QUIZ));
    }

    @Override
    public void handleMissionCompleted(
        SerializableFirebaseMission mission, String missionPath, long pilotId, long completedBy, long projectId, Long updateTime) {
        try {
            if (!bees360FeatureSwitch.isDisableHandleMissionImage()) {
                handleMissionImage(missionPath, pilotId, projectId);
            }

            if (!isMissionActive(pilotId, projectId)) {
                log.warn("Mission '{}' is with pilot '{}' is inactive", missionPath, pilotId);
                return;
            }

            // 图片上传任务设置成完成
            publisher.publishEvent(new DroneImageUploadedEvent(this, projectId));
            publisher.publishEvent(new MobileImageUploadedEvent(this, projectId));
            publisher.publishEvent(new InteriorImageUploadedEvent(this, projectId));
            publisher.publishEvent(new ExteriorImageUploadedEvent(this, projectId));
            // 如果之前没有变成ImageUploaded状态，则触发 image uploaded
            // 飞手状态设置为全部已完成
            beesPilotStatusService.pilotCompleted(projectId);

            // only project open can change project status to image uploaded.
            if (checkIfProjectOpen(projectId)) {
                projectStatusService.changeOnImageUploaded(completedBy, projectId, updateTime);
            }
        } catch (ServiceException e) {
            String message = "Failed to handle mission '%s'".formatted(missionPath);
            log.error(message, e);
        }
    }

    private boolean checkIfProjectOpen(long projectId) {
        var project = projectIIManager.findById(String.valueOf(projectId));
        return com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum
            .PROJECT_CLOSE != project.getCurrentState().getState();
    }

    private static String getPilotIdWithoutPrefix(String userId) {
        return Optional.ofNullable(userId)
                .map(
                        s ->
                                s.replaceFirst(
                                                UserAssemble.BEES360_USER_PREFIX_WITH_COLON,
                                                Strings.EMPTY)
                                        .replaceFirst(FIREBASE_USER_PROVIDER_PREFIX, Strings.EMPTY))
                .orElse(Strings.EMPTY);
    }

    @Override
    public void handleMissionCheckOut(String missionPath, String pilotId, long projectId, Long statusUpdateTime) {
        var webPilotId = firebaseService.toWebUserId(pilotId);
        if(!isMissionActive(webPilotId, projectId) || !checkIfProjectOpen(projectId)) {
            return;
        }

        projectStatusService.changeOnSiteInspected(webPilotId, projectId, statusUpdateTime);
        log.info("Successfully update project {} status to Site Inspected due to mission check out.", projectId);
    }

    @Override
    public void handleMissionCompletedStuck(String missionPath, long pilotId, long projectId) {
        handleMissionCompletedStuck(missionPath, pilotId, projectId, null);
    }

    @Override
    public void handleMissionCompletedStuck(String missionPath, long pilotId, long projectId, Long updateTime) {
        try {
            if (!bees360FeatureSwitch.isDisableHandleMissionImage()) {
                handleMissionImage(missionPath, pilotId, projectId);
            }
            if (isMissionActive(pilotId, projectId)) {
                // 图片上传任务设置成完成
                publisher.publishEvent(new DroneImageUploadedEvent(this, projectId));
                publisher.publishEvent(new InteriorImageUploadedEvent(this, projectId));
                publisher.publishEvent(new ExteriorImageUploadedEvent(this, projectId));

                if(checkIfProjectOpen(projectId)){
                    projectStatusService.changeOnSiteInspected(pilotId, projectId, updateTime);
                }
            }
        } catch (ServiceException e) {
            String message = "Failed to handle mission '%s'".formatted(missionPath);
            log.error(message, e);
        }
    }

    /**
     * 只有当mission所属的飞手为当前project的飞手时，该mission才是active的
     *
     * @param pilotId pilot id
     * @param projectId project id
     * @return if the mission is active.
     */
    private boolean isMissionActive(long pilotId, long projectId) {
        Member member = memberService.getActivePilot(projectId);
        return member != null && Objects.equals(member.getUserId(), pilotId);
    }

    @Override
    public void handleIBeesMissionCompleted(String missionPath, long projectId) {
        if (!bees360FeatureSwitch.isDisableHandleIbeesMissionImage()) {
            handleIBeesMissionImage(missionPath, projectId);
        }
        projectStatusService.changeOnIBeesUploaded(User.BEES_PILOT_SYSTEM, projectId);
        setPipelineTaskStatus(
                String.valueOf(projectId), PipelineTaskEnum.UPLOAD_IBEES_IMAGE.getKey(), DONE);
    }

    private void setPipelineTaskStatus(
            String pipelineId, String key, com.bees360.pipeline.Message.PipelineStatus status) {
        try {
            pipelineService.setTaskStatus(pipelineId, key, status);
        } catch (RuntimeException e) {
            log.warn("Failed to set pipeline '{}' task '{}' to '{}'.", pipelineId, key, status, e);
        }
    }

    public void handleIBeesMissionImage(String missionPath, long projectId) {
        // 同步图片到web
        Map<String, FirebaseRoom> roomMap = firebaseService.getRoomMap(missionPath);
        // 同步图片到web
        List<ProjectImage> images =
                firebaseService.getUploadedProjectImages(
                        projectId,
                        User.BEES_PILOT_SYSTEM,
                        firestore.document(missionPath).collection("image"),
                        roomMap);
        if (!CollectionUtils.isEmpty(images)) {
            try {
                projectImageService.uploadImagesFromFirebase(
                        projectId, User.BEES_PILOT_SYSTEM, images, true);
            } catch (ServiceException e) {
                log.error(
                        "Failed to save iBees mission '{}' images '{}' to bees360",
                        missionPath,
                        Arrays.toString(images.toArray()),
                        e);
            }
        }
    }

    /** from FirebaseService */
    public void handleMissionImage(String missionPath, long pilotId, long projectId) throws ServiceException {
        // 同步图片到web
        Map<String, FirebaseRoom> roomMap = firebaseService.getRoomMap(missionPath);
        List<ProjectImage> images =
                firebaseService.getUploadedProjectImages(
                        projectId,
                        pilotId,
                        firestore.document(missionPath).collection("image"),
                        roomMap);


        // filter deleted images from firebase
        images =
            images.stream()
                .filter(image -> !image.getDeleted())
                .collect(Collectors.toUnmodifiableList());

        if (!CollectionUtils.isEmpty(images)) {
            List<ProjectImage> uploadImages =
                    projectImageService.uploadImagesFromFirebase(projectId, pilotId, images, false);
            if (bees360FeatureSwitch.isDisableUploadImageActivity()) {
                var missionId = getMissionIdFromPath(missionPath);
                uploadImages.stream()
                        .collect(Collectors.groupingBy(ProjectImage::getUserId))
                        .forEach(
                                (uploadBy, list) -> {
                                    var missionImages =
                                            list.stream()
                                                    .collect(
                                                            Collectors.groupingBy(
                                                                    a ->
                                                                            getMainImageCategory(
                                                                                    a
                                                                                            .getCategoryId(),
                                                                                    a
                                                                                            .getSubCategoryId())));
                                    missionImages.forEach(
                                            (mainCategory, ls) ->
                                                    submitActivity(
                                                            projectId,
                                                            String.valueOf(uploadBy),
                                                            missionId,
                                                            ls.size(),
                                                            mainCategory));
                                });
            }
        }
    }

    private static final String SOURCE = "WEB";
    private static final String IMAGE = "IMAGE";
    private static final String UPLOADED = "uploaded";
    private static final String FIELD_DISPLAY_NAME = "images";
    private static final String ENTITY_TYPE = "MISSION";
    private static final String ENTITY_NAME = "mission";
    private static final int CONTENT_ID = 9;

    private String getMainImageCategory(int categoryId, Integer subCategoryId) {
        var remoteConfigs =
                remoteConfigService.remoteConfigObject(
                        CategoryToTabRemoteConfig.class,
                        RemoteConfigParameter.CATEGORY_TO_TAB.getName());
        if (categoryId == CONTENT_ID) {
            if (Objects.isNull(subCategoryId)) {
                return "exterior images";
            }
            return "interior images";
        }

        if (remoteConfigs.getDrone().contains(categoryId)) {
            return "drone images";
        }

        if (remoteConfigs.getInterior().contains(categoryId)) {
            return "interior images";
        }

        if (remoteConfigs.getExterior().contains(categoryId)) {
            return "exterior images";
        }

        throw new IllegalStateException("Unknown category found %s" + categoryId);
    }

    private void submitActivity(long projectId, String userId, String missionId, int imageSize, String fieldDisplayName) {
        var activity =
            Activity.from(
                Message.ActivityMessage.newBuilder()
                    .setProjectId(projectId)
                    .setEntity(
                        Message.ActivityMessage.Entity.newBuilder()
                            .setId(missionId)
                            .setType(ENTITY_TYPE)
                            .setName(ENTITY_NAME)
                            .build())
                    .setAction(UPLOADED)
                    .setCreatedBy(
                        com.bees360.user.Message.UserMessage.newBuilder()
                            .setId(userId)
                            .build())
                    .setField(
                        Message.ActivityMessage.Field.newBuilder()
                            .setName(IMAGE)
                            .setDisplayName(fieldDisplayName)
                            .setValue(String.valueOf(imageSize))
                            .build())
                    .setSource(SOURCE)
                    .build());
        activityManager.submitActivity(activity);
    }
    private void syncMissionBaseInfo(long projectId, String missionId) {
        Map<String, Object> map = new HashMap<>();
        map.put("missionId", missionId);
        projectMapper.update(projectId, map);
    }

    /** 同步未签名原因 */
    private void syncUnsignedReason(long projectId, List<FbCheckoutReason> checkoutReasons) {
        log.info("Sync unsigned reason projectId {}", projectId);
        if (CollectionUtils.isEmpty(checkoutReasons)) {
            log.info("Checkout reasons is empty projectId {}", projectId);
            return;
        }

        AtomicReference<String> checkOutReason = new AtomicReference<>();

        for (FbCheckoutReason fbCheckoutReason : checkoutReasons) {
            if (CollectionUtils.isEmpty(fbCheckoutReason.getTaskReason())) {
                continue;
            }
            Optional<FbCheckoutReason.TaskReason> first =
                    fbCheckoutReason.getTaskReason().stream()
                            .filter(e -> UPC_FORM_TASK_ID.equals(e.getTaskId()))
                            .findFirst();
            first.ifPresent(
                    e -> checkOutReason.set(e.getText()));
        }

        Map<String, Object> map = new HashMap<>();
        map.put("unsignedReason", checkOutReason.get());
        projectMapper.update(projectId, map);
    }

    private String getMissionIdFromPath(String missionPath) {
        return missionPath.replaceFirst(FirebaseService.MISSION_COLLECTION + "/", Strings.EMPTY);
    }
    private String getMissionDocumentPath(String missionId) {
        return FirebaseService.MISSION_COLLECTION + "/" + missionId;
    }

    private String getIBeesMissionDocumentPath(String missionId) {
        return FirebaseService.IBEES_MISSION_COLLECTION + "/" + missionId;
    }
}
