package com.bees360.service.stat;

import com.bees360.entity.stat.enums.ProjectStatType;
import com.bees360.entity.stat.search.StatFullSearchOption;
import com.bees360.entity.stat.vo.StatComplexVo;
import com.bees360.entity.stat.vo.card.StatCardVo;
import com.bees360.entity.stat.vo.chart.StatChartVo;
import com.bees360.entity.stat.vo.chart.StatMapVo;
import com.bees360.entity.stat.vo.list.PagedResultVo;

import java.util.List;

public interface StatComplexService {

    ProjectStatType getType();

    PagedResultVo getProjectList(StatFullSearchOption searchOption);

    /**
     * 获取卡片统计数据
     *
     * @param searchOption 统计条件
     * @return
     */
    StatCardVo getCardStat(StatFullSearchOption searchOption);

    /**
     * 获取图表统计数据
     * @param searchOption  统计条件
     * @return
     */
    StatChartVo getChartStat(StatFullSearchOption searchOption);

    /**
     * 获取地图数据
     * @param fullSearchOption
     * @return
     */
    List<StatMapVo> getMapData(StatFullSearchOption fullSearchOption);

    StatComplexVo getStatComplexInfo(StatFullSearchOption fullSearchOption);

}
