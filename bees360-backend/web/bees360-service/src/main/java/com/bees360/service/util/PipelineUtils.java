package com.bees360.service.util;

import com.bees360.pipeline.Pipeline;
import com.bees360.pipeline.PipelineTask;
import jakarta.annotation.Nullable;
import org.apache.commons.lang3.StringUtils;

public class PipelineUtils {

    /**
     * @return null if pipeline is null or the task not found in pipeline
     */
    public static PipelineTask findTask(@Nullable Pipeline pipeline, String taskKey) {
        if (pipeline == null) {
            return null;
        }
        for (var task : pipeline.getTask()) {
            if (StringUtils.equals(task.getKey(), taskKey)) {
                return task;
            }
        }
        return null;
    }
}
