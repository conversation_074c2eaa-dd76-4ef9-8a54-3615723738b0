package com.bees360.service.util;

import org.mybatis.spring.MyBatisSystemException;
import org.springframework.dao.DataAccessResourceFailureException;

import java.io.IOException;
import java.io.UncheckedIOException;

public class FirebaseExceptionTranslation {
    public static void translateExceptionAndThrow(RuntimeException e) throws IOException {
        if (e instanceof MyBatisSystemException) {
            Throwable cause = e.getCause();
            if (cause instanceof DataAccessResourceFailureException) {
                throw new IOException(e);
            }
        }
        if (e instanceof IllegalStateException) {
            throw new IOException(e);
        }
        if (e instanceof UncheckedIOException exception) {
            throw exception.getCause();
        }

        throw e;
    }
}
