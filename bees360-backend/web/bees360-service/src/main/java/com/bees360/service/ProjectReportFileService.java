package com.bees360.service;

import com.bees360.entity.enums.ReportTypeEnum;
import java.util.List;
import java.util.OptionalInt;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.dto.ProjectReportFileDto;
import com.bees360.entity.dto.ReportTypeDto;
import com.bees360.entity.vo.ProjectReportFileTinyVo;

/**
 * report file service
 * <AUTHOR>
 *
 */
public interface ProjectReportFileService {

	List<ProjectReportFile> getAllReportFiles(long projectId);

	List<ProjectReportFileTinyVo> getReportFiles(long projectId, long userId, Integer reportType) throws ServiceException;

	ProjectReportFileTinyVo getReportFile(long projectId, Integer reportType);

	void submitReport(long projectId, long userId, String reportId) throws ServiceException;

	void approveReport(long projectId, long userId, String reportId) throws ServiceException;

    void doApproveReport(long projectId, long userId, String reportId) throws ServiceException;

    OptionalInt getReportSizeLimit(ProjectReportFile reportFile) throws ServiceException;

    void updateReportFile(ProjectReportFile projectReportFile);

    void updateReportFile(ProjectReportFile projectReportFile, String summary);

    void deleteReport(long projectId, long userId, ReportTypeEnum reportType);

    int update(long projectId, ProjectReportFile reportFile);

    void infoUserReportApprove(long projectId, long userId, ReportTypeEnum reportType,
        boolean withReportAttachment) throws ServiceException;

    void infoMemberReportApprove(long projectId, ReportTypeEnum reportType, boolean withReportAttachment)
        throws ServiceException;

	void disapprovedReport(long projectId, long userId, String reportId) throws ServiceException;

	void reprocessReport(long projectId, long userId, String reportId) throws ServiceException;

	List<ReportTypeDto> getReportTypes(long projectId) throws ServiceException;

	void deleteReport(long projectId, long userId, String reportId) throws ServiceException;

	ProjectReportFile createOrReplaceReportFile(long userId, long projectId, ProjectReportFileDto projectReportFileDto)
			throws ServiceException;

    String save(ProjectReportFile reportFile);

    String save(ProjectReportFile reportFile, String summary);

    ProjectReportFile getById(String reportId);

    ProjectReportFile getById(long projectId, String reportId);

	boolean isPackageExist(long projectId, int reportType) throws Exception;

    String getReportImageFileKey(long projectId, int reportType);
}
