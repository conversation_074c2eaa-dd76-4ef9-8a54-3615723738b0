package com.bees360.service.firebase;

import com.bees360.entity.AppNotification;
import com.google.api.core.ApiFuture;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.firebase.messaging.ApnsConfig;
import com.google.firebase.messaging.Aps;
import com.google.firebase.messaging.BatchResponse;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.MulticastMessage;
import com.google.firebase.messaging.Notification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/8/24 2:09 下午
 **/
@Service(value = "appNotificationService")
@Slf4j
public class AppNotificationServiceImpl implements AppNotificationService {

    @Autowired
    private Firestore firestore;
    @Override
    public boolean send(AppNotification notification) {
        List<String> registrationList = getRegistrationList(notification.getUserId()+"");
        if (!CollectionUtils.isEmpty(registrationList)) {
            if (registrationList.size() == 1) {
                return send(registrationList.get(0), notification.getNotification());
            } else {
                return send(registrationList, notification.getNotification());
            }
        }
        return false;
    }

    private boolean send(String registrationToken, Notification notification) {
        Message message = Message.builder()
            .setNotification(notification)
            // TODO 用配置文件 配置消息
            .setApnsConfig(ApnsConfig.builder().setAps(Aps.builder().setSound("default").build()).build())
            .setToken(registrationToken).build();
        try {
            String response = FirebaseMessaging.getInstance().send(message);
            log.debug("Successfully sent message: " + response);
        } catch (FirebaseMessagingException e) {
            log.info("Send message to [" + registrationToken + "] failed.");
            return false;
        }
        return true;
    }

    private boolean send(List<String> registrationToken, Notification notification) {
        MulticastMessage message = MulticastMessage.builder()
            .setNotification(notification)
            .setApnsConfig(ApnsConfig.builder().setAps(Aps.builder().setSound("default").build()).build())
            .addAllTokens(registrationToken).build();
        try {
            BatchResponse response = FirebaseMessaging.getInstance().sendMulticast(message);
            log.debug("Successfully sent message: " + response.getSuccessCount());
        } catch (FirebaseMessagingException e) {
            log.info("Send message to [" + registrationToken + "] failed.");
            return false;
        }
        return true;
    }

    public List<String> getRegistrationList(String userId) {
        ApiFuture<QuerySnapshot> query = firestore.collection(USER_REGISTRATION_COLLECTION).whereEqualTo("userId", userId).get();
        QuerySnapshot querySnapshot;
        try {
            querySnapshot = query.get();
            List<QueryDocumentSnapshot> documents = querySnapshot.getDocuments();
            return documents.stream().map(document -> document.getString("FCMToken")).collect(Collectors.toList());
        } catch (InterruptedException | ExecutionException e) {
            log.info("Get registration id failed.");
        }
        return new ArrayList<>();
    }
}
