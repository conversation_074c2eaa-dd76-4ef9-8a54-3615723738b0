package com.bees360.event;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.commons.firebasesupport.service.RemoteConfigService;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.ProjectQuiz;
import com.bees360.entity.converter.ProjectQuizConveter;
import com.bees360.entity.dto.ProjectQuizDto;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.RemoteConfigParameter;
import com.bees360.entity.firebase.TaskQuizRemoteConfig;
import com.bees360.event.registry.ProjectCreatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.ImageCloneJob;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.mapper.ProjectImageTagMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.ProjectQuizMapper;
import com.bees360.project.Message;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.util.DateUtil;
import com.bees360.util.SecureTokens;
import com.google.common.base.Preconditions;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;
import jakarta.annotation.Nullable;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.bees360.activity.Message.ActivityMessage;

import static com.bees360.service.impl.ProjectImageServiceImpl.GROUP_TYPE_PROJECT;


@Log4j2
public class CloneProjectPostProcessingOnProjectCreated extends AbstractNamedEventListener<ProjectCreatedEvent> {

    private final ProjectIIManager projectIIManager;

    private final ProjectImageMapper projectImageMapper;

    private final JobScheduler jobScheduler;

    private final ProjectStatusManager projectStatusManager;

    private final ProjectImageTagMapper projectImageTagMapper;

    private final ProjectMapper projectMapper;

    private final ProjectQuizMapper projectQuizMapper;

    private final RemoteConfigService remoteConfigService;

    private final ActivityManager activityManager;

    public CloneProjectPostProcessingOnProjectCreated(
        ProjectIIManager projectIIManager,
        ProjectImageMapper projectImageMapper,
        JobScheduler jobScheduler,
        ProjectStatusManager projectStatusManager,
        ProjectImageTagMapper projectImageTagMapper,
        ProjectMapper projectMapper,
        ProjectQuizMapper projectQuizMapper,
        RemoteConfigService remoteConfigService,
        ActivityManager activityManager
    ) {
        this.projectIIManager = projectIIManager;
        this.projectImageMapper = projectImageMapper;
        this.jobScheduler = jobScheduler;
        this.projectStatusManager = projectStatusManager;
        this.projectImageTagMapper = projectImageTagMapper;
        this.projectMapper = projectMapper;
        this.projectQuizMapper = projectQuizMapper;
        this.remoteConfigService = remoteConfigService;
        this.activityManager = activityManager;
        log.info("Created {}(projectIIManager={}, projectImageMapper={}, jobScheduler={}, " +
                "projectStatusManager={}, projectImageTagMapper={}, projectMapper={}, projectQuizMapper={}, " +
                "remoteConfigService={}, activityManager={}).",
            this,
            this.projectIIManager,
            this.projectImageMapper,
            this.jobScheduler,
            this.projectStatusManager,
            this.projectImageTagMapper,
            this.projectMapper,
            this.projectQuizMapper,
            this.remoteConfigService,
            this.activityManager);
    }

    @Override
    public void handle(ProjectCreatedEvent event) throws IOException {
        var projectId = event.getProjectId();
        var project = projectIIManager.findById(projectId);
        var creator = project.getCreateBy().getId();
        var metadata = project.getMetadata();
        if (metadata.getCloneType() != Message.CloneType.PROCESSOR_TRAINING) {
            return;
        }

        var cloneFrom = metadata.getCloneFrom().getValue();
        Preconditions.checkArgument(
            StringUtils.isNoneBlank(cloneFrom),
            "Clone from project id is blank. project id: {}", projectId);

        log.info("Start clone project post processing. cloneFrom:{} projectId:{}", cloneFrom, projectId);


        // 克隆 ProjectQuiz 表里的问卷数据
        cloneProjectQuiz(cloneFrom, projectId);

        // get old project image list
        var oldProjectImages = projectImageMapper.listAll(Long.parseLong(cloneFrom));
        if (CollectionUtils.isEmpty(oldProjectImages)) {
            log.info("Project({}) has no images", cloneFrom);
            return;
        }

        // 处理 project image 数据
        var newProjectImages = new ArrayList<ProjectImage>();
        Map<String, Set<String>> imageGroupMap = Map.of(
            GROUP_TYPE_PROJECT,
            Set.of(projectId)
        );
        var cloneImages = new ArrayList<ImageCloneJob.CloneImage>();
        for (ProjectImage oldImage : oldProjectImages) {
            var oldImageId = oldImage.getImageId();
            var newImageId = SecureTokens.generateRandomBase64Token();

            var newImage = (ProjectImage) oldImage.clone();
            newImage.setProjectId(Long.parseLong(projectId));
            newImage.setImageId(newImageId);

            cloneImages.add(new ImageCloneJob.CloneImage(oldImageId, newImageId, imageGroupMap));
            newProjectImages.add(newImage);
        }
        // save solid project image
        var job = new ImageCloneJob(cloneImages, String.valueOf(creator));
        var future = jobScheduler.schedule(Job.ofPayload(job));

        Futures.addCallback(
            future,
            new FutureCallback<>() {
                @Override
                public void onSuccess(@Nullable Void result) {
                    log.info("Success to clone project {} image with job {}.", projectId, job);
                    // save web project image
                    projectImageMapper.insertBaseInfoList(newProjectImages);
                    // 将image_category字段插入ProjectImageTag表
                    List<ProjectImage> imageWithTags = newProjectImages.stream()
                            .filter(i -> StringUtils.isNotEmpty(i.getImageCategory()))
                            .collect(Collectors.toList());
                    if (!imageWithTags.isEmpty()) {
                        projectImageTagMapper.insertImageCategoryList(imageWithTags);
                    }
                    // 触发 project status - Image uploaded
                    projectStatusManager.updateStatus(projectId, com.bees360.project.Message.ProjectStatus.IMAGE_UPLOADED, String.valueOf(creator));
                    log.info("Successfully clone project images. cloneFrom:{} projectId:{}", cloneFrom, projectId);
                }

                @Override
                public void onFailure(Throwable t) {
                    log.error("Failed to clone project %s image with job %s.".formatted(projectId, job), t);
                }
            },
            MoreExecutors.directExecutor());
    }

    private void cloneProjectQuiz(String sourceProjectId, String targetProjectId) {
        // get old project quiz list
        var sourceProject = projectMapper.getById(Long.parseLong(sourceProjectId));
        int projectClaimType = Optional.ofNullable(sourceProject.getClaimType()).orElse(0);
        var quizList = projectQuizMapper.listLatestAnswers(Long.parseLong(sourceProjectId), projectClaimType);
        long now = DateUtil.getNow();
        quizList.forEach(quiz -> {
            quiz.setClaimType(projectClaimType);
            quiz.setCreateTime(now);
            quiz.setProjectId(Long.parseLong(targetProjectId));
        });

        // save target project quiz
        if (CollectionUtils.isNotEmpty(quizList)) {
            log.debug("Clone project quiz list. projectId:{} quizList:{}", targetProjectId, quizList);
            projectQuizMapper.batchInsertAnswer(quizList);

            // submit activity
            submitActivityOnProjectQuizClone(Long.parseLong(targetProjectId), quizList);
        }
    }

    private void submitActivityOnProjectQuizClone(long projectId, List<ProjectQuiz> quizList) {
        var projectQuizDtos = listProjectQuizDto(projectId, quizList).stream()
            .map(quizDto -> String.join("\n", quizDto.getSubject(), quizDto.getAnswer()))
            .collect(Collectors.joining("\n\n"));

        var activityMessage = ActivityMessage.newBuilder();
        activityMessage
            .setProjectId(projectId)
            .setAction(ActivityMessage.ActionType.CREATE.name())
            .setCreatedBy(
                com.bees360.user.Message.UserMessage.newBuilder()
                    .setId(String.valueOf(com.bees360.entity.User.AI_ID))
                    .build())
            .setSource(ActivitySourceEnum.AI.getValue())
            .setEntity(
                ActivityMessage.Entity.newBuilder()
                    .setType(ActivityMessage.EntityType.COMMENT.name())
                    .build())
            .setComment(
                com.bees360.activity.Message.CommentMessage.newBuilder()
                    .setContent(projectQuizDtos)
                    .setProjectId(projectId)
                    .setSource(ActivitySourceEnum.AI.getValue())
                    .build());
        activityManager.submitActivity(Activity.of(activityMessage.build()));
    }

    public List<ProjectQuizDto> listProjectQuizDto(long projectId, List<ProjectQuiz> projectQuizAnswers){
        // 获取题目
        List<TaskQuizRemoteConfig> quizList = remoteConfigService
            .getRemoteConfig(TaskQuizRemoteConfig.class, RemoteConfigParameter.QUIZ.getName());
        Map<Long, TaskQuizRemoteConfig> quizConfigMap = quizList.stream()
            .collect(Collectors.toMap(TaskQuizRemoteConfig::getQuizId, Function.identity(), (a, b) -> a));

        return projectQuizAnswers.stream().map(e -> {
            TaskQuizRemoteConfig quizConfig = quizConfigMap.get(e.getQuizId());
            ProjectQuizDto quizDto = ProjectQuizConveter.quizConfig2ProjectQuiz(projectId, quizConfig);
            quizDto.setAnswer(e.getAnswer());
            return quizDto;
        }).sorted(Comparator.comparingLong(ProjectQuizDto::getQuizId)).collect(Collectors.toList());
    }
}
