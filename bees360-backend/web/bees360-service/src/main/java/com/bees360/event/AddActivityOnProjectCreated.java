package com.bees360.event;

import com.bees360.activity.Activities;
import com.bees360.activity.ActivityManager;
import com.bees360.entity.ProjectMessage;
import com.bees360.entity.enums.ProjectMessageTypeEnum;
import com.bees360.event.registry.ProjectCreate;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.Project;
import com.bees360.service.ProjectMessageService;
import com.bees360.service.ProjectService;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.Optional;

/**
 * 该类是一个事件监听器，主要功能是在项目创建时添加相关活动记录和生成声明备注的评论。
 * TODO 重构claim note时将这个监听器转移到solid
 */
@Log4j2
public class AddActivityOnProjectCreated extends AbstractNamedEventListener<ProjectCreate> {

    private final ActivityManager activityManager;
    private final ProjectService projectService;
    private final ProjectMessageService projectMessageService;

    public AddActivityOnProjectCreated(
        @NonNull ActivityManager activityManager,
        @NonNull ProjectService projectService,
        @NonNull ProjectMessageService projectMessageService) {
        this.activityManager = activityManager;
        this.projectService = projectService;
        this.projectMessageService = projectMessageService;
        log.info("Created {}", this);
    }

    @Override
    public void handle(ProjectCreate event) throws IOException {
        var project = event.getProject();
        // 确保activity的顺序
        createActivityOfProjectCreated(project);
        createActivityOfClaimNote(project.getId());
    }

    private void createActivityOfProjectCreated(Project project) {
        var projectId = Long.parseLong(project.getId());
        var createdBy = project.getCreateBy().getId();
        var activity = Activities.createProject(projectId, createdBy,
            Optional.ofNullable(project.getCreateAt()).map(d -> d.toInstant(ZoneOffset.UTC)).orElse(Instant.now()));
        activityManager.submitActivity(activity);
    }

    private void createActivityOfClaimNote(String projectIdStr) {
        var projectId = Long.parseLong(projectIdStr);
        var project = projectService.getById(projectId);
        if (StringUtils.isBlank(project.getClaimNote())) {
            return;
        }
        generateCommentWhenProjectCreated(project.getCreatedBy(), project.getProjectId(), project.getClaimNote());
    }

    private void generateCommentWhenProjectCreated(
        long userId, long projectId, @NonNull String comment) {
        projectMessageService.addMessage(
            ProjectMessage.builder()
                .senderId(userId)
                .type(ProjectMessageTypeEnum.ADMIN_NOTE.getCode())
                .content(comment)
                .createTime(System.currentTimeMillis())
                .projectId(projectId)
                .title(ProjectMessageTypeEnum.ADMIN_NOTE.getDisplay())
                .build());
    }

}
