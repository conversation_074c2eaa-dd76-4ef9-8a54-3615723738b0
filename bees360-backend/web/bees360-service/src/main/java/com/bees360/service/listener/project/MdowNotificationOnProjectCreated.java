package com.bees360.service.listener.project;

import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.UncheckedServiceException;
import com.bees360.entity.CompanyIDMap;
import com.bees360.entity.Project;
import com.bees360.entity.User;
import com.bees360.service.MessageService;
import com.bees360.service.UserService;
import com.bees360.service.properties.Bees360WatcherProperties;
import com.bees360.util.CollectionsUtil;
import com.bees360.web.event.project.ProjectCreatedEvent;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

@Component
@Log4j2
public class MdowNotificationOnProjectCreated {

    private static final String COMPANY_NAME_MDOW = "Mdow";

    private final MessageService messageService;

    private final UserService userService;

    private final CompanyIDMap companyIDMap;

    private final Bees360WatcherProperties watcherProperties;

    public MdowNotificationOnProjectCreated(MessageService messageService, UserService userService,
                                            CompanyIDMap companyIDMap, Bees360WatcherProperties watcherProperties) {
        this.messageService = messageService;
        this.userService = userService;
        this.companyIDMap = companyIDMap;
        this.watcherProperties = watcherProperties;
        log.info("start spring event listener: {}", this.getClass().getName());
    }

    @EventListener
    public void sendEmailToCustomerOnProjectCreated(ProjectCreatedEvent projectCreatedEvent) {
        Project project = projectCreatedEvent.getProject();
        Long insuredCompany = project.getInsuranceCompany();
        Long repairCompany = project.getRepairCompany();

        Long companyId = companyIDMap.getMdow();
        if (companyId == null || (!Objects.equals(insuredCompany, companyId)
            && !Objects.equals(repairCompany, companyId))) {
            return;
        }

        List<String> recipients = watcherProperties.getProjectCreatedCustomer().get(COMPANY_NAME_MDOW);
        if (CollectionUtils.isEmpty(recipients)) {
            return;
        }
        log.info("start to send notification email to company: {}, for project:{}", companyId,
            project.getProjectId());
        User user = userService.getUserById(project.getCreatedBy());
        messageService.sendProjectCreated(user, project, recipients);
    }
}
