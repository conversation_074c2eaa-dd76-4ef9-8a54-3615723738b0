package com.bees360.service.firebase;

import com.bees360.activity.Comment;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.firebase.FirebaseProjectStatusEnum;
import com.bees360.job.registry.SerializableFirebaseProject;
import com.bees360.project.Contact;
import com.bees360.project.state.ProjectState;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

public interface FirebaseProjectService {
    /**
     * 将Firebase项目更新到Web
     * @param firebaseProject firebase项目信息
     * @param projectId
     */
    void handleFirebaseProject(SerializableFirebaseProject firebaseProject, String projectId);

    /**
     *  更新Firebase项目字段，该字段属于Bees360 web项目的基本数据
     *  如：city, state，country等
     *  @see SerializableFirebaseProject
     * @param projectId
     */
    void updateFirebaseProjectBaseInfo(long projectId) throws ServiceException;
    /**
     * 创建项目时 同步project信息到cloud firestore
     *
     * @param project
     */
    void initFirebaseProject(Project project);

    /**
     * 更新Firebase项目字段，该字段属于Bees360 web项目的相关数据,如果 tag_id
     *
     * @see SerializableFirebaseProject
     * @param projectId
     */
    void updateFirebaseProjectRelateInfo(long projectId) throws ServiceException;

    Map<String, Object> getAllFieldsInProject(Project project);
    /**
     *  更新Firebase项目字段，该字段属于Bees360 web项目的状态相关的数据
     *  如：project_status, pilot等
     *  @see SerializableFirebaseProject
     * @param project
     * @param statusEnum
     */
    void updateFirebaseProjectStatus(Project project, FirebaseProjectStatusEnum statusEnum) throws ServiceException;

    void updateFirebaseProjectLatestStatus(Project project);

    void syncSurveyCompletedToFirebase(long projectId);

    void syncServiceCompletedToFirebase(long projectId);

    /**
     * 修复数据的接口，之后可以删除
     * @param projects
     */
    @Deprecated(since = "v20210707")
    void syncCatNumberToFirebase(List<Project> projects);

    /**
     * 修复数据的接口，之后可以删除
     */
    @Deprecated(since = "v20220112")
    void recoverProjectLatestStatus();

    void initLossDescription(Project project, Comment comment);

    @Deprecated
    void recoverTimeline(Long projectStart, Long projectIdEnd);

    void syncProjectStateToFirebase(long projectId, ProjectState state);

    @Deprecated
    void recoverProjectStatusToFirebase(Instant startTime, Instant endTime);

    void updateContacts(long projectId, Iterable<? extends Contact> contacts);

    /**
     * Updates the parent project association of a project in Firebase.
     *
     * <p>If {@code parentProjectId} is null, the "parent_project_id" field
     * will be removed from the Firestore document. Otherwise, it will be
     * updated to the specified parent project ID.</p>
     *
     * @param projectId       The unique identifier of the current project (non-null)
     * @param parentProjectId The ID of the parent project (null indicates disassociation)
     */
    void updateParentChildProject(long projectId, String parentProjectId);
}
