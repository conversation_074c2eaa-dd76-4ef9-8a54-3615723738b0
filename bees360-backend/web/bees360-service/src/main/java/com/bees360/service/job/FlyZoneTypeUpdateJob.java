package com.bees360.service.job;

import com.bees360.entity.Project;
import com.bees360.job.util.AbstractRetryableJob;
import com.google.gson.Gson;
import com.google.protobuf.ByteString;
import lombok.NonNull;
import org.apache.commons.codec.digest.DigestUtils;

import jakarta.annotation.Nonnull;
import java.nio.charset.StandardCharsets;
import java.time.Duration;


public class FlyZoneTypeUpdateJob extends AbstractRetryableJob {

    private final String id;

    public final static String DEFAULT_NAME = "update_project_fly_zone";

    private final int retryCount;

    private final Duration retryDelay;

    private final byte[] payload;

    public FlyZoneTypeUpdateJob(Project project) {
        this(project, 5, Duration.ofSeconds(20L));
    }

    @lombok.Builder
    public FlyZoneTypeUpdateJob(Project project, int retryCount, Duration retryDelay) {
        this.payload = new Gson().toJson(project).getBytes(StandardCharsets.UTF_8);
        this.id = DEFAULT_NAME + "-" + project.getProjectId() + "-" + DigestUtils.md5Hex(payload);
        this.retryCount = retryCount;
        this.retryDelay = retryDelay;
    }

    @Override
    public int getRetryCount() {
        return retryCount;
    }

    @Nonnull
    @Override
    public Duration getRetryDelay() {
        return retryDelay;
    }

    @Override
    public float getRetryDelayIncreaseFactor() {
        return 1;
    }

    @Nonnull
    @Override
    public String getId() {
        return id;
    }

    @Nonnull
    @Override
    public String getName() {
        return DEFAULT_NAME;
    }

    @Override
    public @NonNull ByteString getPayload() {
        return ByteString.copyFrom(payload);
    }
}
