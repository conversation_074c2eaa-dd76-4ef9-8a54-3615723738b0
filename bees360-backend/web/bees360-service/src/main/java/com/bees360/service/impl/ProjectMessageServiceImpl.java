package com.bees360.service.impl;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivityQuery;
import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.activity.Message;
import com.bees360.entity.ProjectMessage;
import com.bees360.entity.query.ProjectMessageQuery;
import com.bees360.mapper.ProjectMessageMapper;
import com.bees360.project.information.Message.ProjectInformation.InformationType;
import com.bees360.project.information.ProjectInformation;
import com.bees360.project.information.ProjectInformationManager;
import com.bees360.service.CommentService;
import com.bees360.service.ProjectMessageService;
import com.bees360.web.event.project.ProjectMessageCreatedEvent;
import com.google.protobuf.util.Timestamps;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2020/12/11 14:19
 */
@Service
@Log4j2
public class ProjectMessageServiceImpl implements ProjectMessageService {

    @Autowired private ProjectMessageMapper projectMessageMapper;

    @Autowired private ApplicationEventPublisher publisher;

    @Autowired private CommentService commentService;

    @Autowired private ActivityManager activityManager;

    @Autowired private ProjectInformationManager projectInformationManager;

    @Autowired private CommentManager commentManager;

    @Deprecated
    @Override
    public List<Long> addActivity(List<ProjectMessage> messages) {
        List<Long> projectIds = new ArrayList<>();
        for (ProjectMessage message : messages) {
            if (activityManager
                    .getActivities(
                            ActivityQuery.builder()
                                    .projectId(message.getProjectId())
                                    .entityType(Message.ActivityMessage.EntityType.COMMENT.name())
                                    .build())
                    .stream()
                    .noneMatch(a -> a.getComment().getContent().equals(message.getContent()))) {
                commentService.addComment(
                        message.getProjectId(),
                        message.getSenderId() + "",
                        message.getContent(),
                        message.getCreateTime());
                projectIds.add(message.getProjectId());
            }
        }
        return projectIds;
    }

    @Override
    @Transactional
    public void addMessageAndSyncToAi(ProjectMessage message) {
        addMessage(message);
        addProjectInformation(message);
        publisher.publishEvent(new ProjectMessageCreatedEvent(this, message));
        log.info("Successfully add project message {}.", message);
    }

    @Override
    @Transactional
    public void addMessageAndSyncToAiWithoutActivity(ProjectMessage message) {
        projectMessageMapper.insert(message);
        addProjectInformation(message);
        publisher.publishEvent(new ProjectMessageCreatedEvent(this, message));
        log.info("Successfully add project message {}.", message);
    }

    @Override
    public void addMessage(ProjectMessage message) {
        projectMessageMapper.insert(message);
        // 把Message记录到activity
        var projectId = message.getProjectId();
        var content = message.getContent();
        var createdBy = String.valueOf(message.getSenderId());
        long createAt = message.getCreateTime();
        var source = message.getSource();
        var comment =
                Comment.from(
                        projectId,
                        createdBy,
                        content,
                        Timestamps.fromMillis(createAt),
                        Timestamps.fromMillis(createAt));
        if (StringUtils.isNotBlank(source)) {
            comment = Comment.from(comment.toMessage().toBuilder().setSource(source).build());
        }
        commentManager.addComment(comment);
    }

    @Override
    public List<ProjectMessage> listMessage(ProjectMessageQuery query) {
        return projectMessageMapper.listMessage(query);
    }

    @Override
    public ProjectMessage getLatestMessage(ProjectMessageQuery query) {
        return projectMessageMapper.getLatestMessage(query);
    }

    @Override
    public int delete(ProjectMessageQuery query) {
        return projectMessageMapper.delete(query);
    }

    private void addProjectInformation(ProjectMessage message) {
        var type = InformationType.forNumber(message.getType()) == null ?
                    InformationType.UNDEFINED :InformationType.forNumber(message.getType());
        var builder = com.bees360.project.information.Message.ProjectInformation.newBuilder()
            .setProjectId(String.valueOf(message.getProjectId()))
            .setContent(message.getContent())
            .setCreatedBy(String.valueOf(message.getSenderId()))
            .setType(type);
        acceptIfNotNull(builder::setTitle, message.getTitle());
        projectInformationManager.addInformation(ProjectInformation.from(builder.build()));
    }

}
