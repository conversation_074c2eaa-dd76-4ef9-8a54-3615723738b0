package com.bees360.service.job;

import com.bees360.job.registry.SerializableFirebaseBatch;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseService;
import com.google.cloud.firestore.Firestore;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;

import static com.bees360.service.job.FirebaseMissionChangedExecutor.translateExceptionAndThrow;

@Log4j2
@Component
@ToString
public class FirebaseBatchChangedExecutor extends AbstractJobExecutor<SerializableFirebaseBatch> {
    private final FirebaseService firebaseService;
    private final Firestore firestore;

    public FirebaseBatchChangedExecutor(FirebaseService firebaseService, Firestore firestore) {
        this.firebaseService = firebaseService;
        this.firestore = firestore;

        log.info("Created '{}'", this);
    }

    @Override
    protected void handle(SerializableFirebaseBatch batch) throws IOException {
        try {
            log.info("Start to handle batch '{}' '{}'", batch.getId(), batch);
            firebaseService.syncBatchToWeb(batch, batch.getId());
            log.info("Successfully handle batch '{}'", batch.getId());
        } catch (RuntimeException e) {
            log.warn("Failed to handle batch '{}'.", batch, e);
            translateExceptionAndThrow(e);
        }
    }
}
