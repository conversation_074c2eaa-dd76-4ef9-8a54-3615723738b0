package com.bees360.service.impl;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.activity.CommentQuery;
import com.bees360.activity.Message;
import com.bees360.base.MessageCode;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.image.GroupImageSource;
import com.bees360.image.Image;
import com.bees360.image.ImageGroupManager;
import com.bees360.image.ImageNoteManager;
import com.bees360.image.ImageSource;
import com.bees360.image.ImageTagCategoryEnum;
import com.bees360.image.ImageTagEnum;
import com.bees360.image.ImageTagProvider;
import com.bees360.image.grpc.ProtobufImageNote;
import com.bees360.image.Message.ImageMessage;
import com.bees360.mapper.ProjectImageTagMapper;
import com.bees360.service.event.project.ProjectImageUploadEvent;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.user.Message.UserMessage;
import com.bees360.base.exception.AccessDatabaseException;
import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.common.resource.ResourceKeyManager;
import com.bees360.common.util.UUIDUtil;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.dto.ImageInfoRecord;
import com.bees360.entity.dto.ProjectImageSearchOptionDto;
import com.bees360.entity.dto.ProjectImageUploadResponseDto;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.DirectionEnum;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImagePartialViewTypeEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import com.bees360.entity.enums.ImageUploadStatusEnum;
import com.bees360.entity.enums.InspectionTypeEnum;
import com.bees360.entity.enums.OrientationEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.query.ImageRecordInfoQuery;
import com.bees360.entity.vo.ImageIdsVo;
import com.bees360.entity.vo.Pagination;
import com.bees360.entity.vo.ProjectImageAnnotationVo;
import com.bees360.entity.vo.ProjectImagePageResult;
import com.bees360.mapper.ImageInfoRecordMapper;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.service.EventHistoryService;
import com.bees360.service.ProjectImageService;
import com.bees360.service.ProjectService;
import com.bees360.service.ReportSummaryService;
import com.bees360.service.beespilot.BeesPilotStatusService;
import com.bees360.util.DateUtil;
import com.bees360.util.Iterables;
import com.bees360.web.event.image.ImageUploadedEventForActivity;
import com.bees360.web.event.project.ProjectImagesDeletedEvent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.inject.Inject;
import jakarta.validation.constraints.NotEmpty;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

@Service("projectImageService")
public class ProjectImageServiceImpl implements ProjectImageService {

	private Logger logger = LoggerFactory.getLogger(ProjectImageServiceImpl.class);

    private static final int ORIGINAL_FILE_NAME_LENGTH = 100;

    private static final String[] NOT_REQUIRED_SYNC_IMAGE_GROUP_KEYS = {"selfie_image"};

	@Inject
	ProjectImageMapper projectImageMapper;

	@Inject
	ProjectService projectService;

    @Autowired
    private BeesPilotStatusService beesPilotStatusService;
    @Autowired
    private ImageInfoRecordMapper imageInfoRecordMapper;

    @Autowired
    private EventHistoryService eventHistoryService;

    @Autowired
    private ReportSummaryService reportSummaryService;

    @Autowired
    private ApplicationEventPublisher publisher;
    @Autowired
    private ResourcePool resourcePool;

    @Autowired
    private CommentManager commentManager;

    @Autowired private ActivityManager activityManager;

    @Autowired
    private ProjectImageTagMapper projectImageTagMapper;

    @Autowired private ImageNoteManager imageNoteManager;

    @Autowired private ImageGroupManager grpcImageGroupClient;

    @Autowired private ImageTagProvider imageTagProvider;
    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Autowired
    @Qualifier("projectImageReSorter")
    private UnaryOperator<List<ProjectImage>> projectImageReSorter;

    @Value("${image.solid.sync:false}")
    private boolean saveSolidData;

    private static final String EMPTY_TAG_CATEGORY = "default";

    public static final String GROUP_TYPE_PROJECT = "GROUP_PROJECT";
    private static final String GROUP_TYPE_FIREBASE = "GROUP_FIREBASE_IMAGE_KEY";
    private static final String GROUP_TYPE_PILOT = "GROUP_IMAGE_PILOT_ID";

    private static final Set<Integer> DIRECTION_TAGS =
            Arrays.stream(ImageTagEnum.values())
                    .filter(t -> t.getCategory().equals(ImageTagCategoryEnum.DIRECTION))
                    .map(ImageTagEnum::getCode)
                    .collect(Collectors.toSet());

	@Override
	public void deleteById(String imageId, long projectId, long userId) throws ServiceException {
		// <EMAIL> consider if it's necessary to delete the data related with the image?
		try {
			ProjectImage image = projectImageMapper.getById(projectId, imageId);
			if(image == null) {
				return;
			}
			judgeReportDeletable(projectId, Arrays.asList(imageId));

			FileSourceTypeEnum type = FileSourceTypeEnum.getEnum(image.getFileSourceType());
			if(type == FileSourceTypeEnum.DRONE_IMAGE || type == FileSourceTypeEnum.CELL_PHONE_IMAGE) {
				// screenshot
				deleteScreenshotsOfImages(projectId, Collections.singletonList(imageId), userId);
			}
			// image
			projectImageMapper.deleteById(projectId, imageId);
            publisher.publishEvent(
                    new ProjectImagesDeletedEvent(
                            this,
                            projectId,
                            Collections.singletonList(imageId),
                            ProjectImage.DELETED));
		} catch(AccessDatabaseException e) {
			logger.error("PROJECT[" + projectId + "] " + e.getMessage(), e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		} catch(ServiceException e) {
			logger.error("PROJECT[" + projectId + "] Fail to delete onsite report elements", e);
			throw e;
		} catch (Exception e){
			logger.error("PROJECT[" + projectId + "] Fail to delete one image and the data related with the image", e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

    /**
     * 判断是否可以删除项目中指定的图片
     */
    private void judgeReportDeletable(long projectId, List<String> imageIds) throws ServiceException {
        // 判断图片是否已经在summary中出现
        Map<ReportTypeEnum, Set<String>> imagesInReport = reportSummaryService.listImagesInSummary(projectId);
        for (Set<String> images: imagesInReport.values()) {
            if (CollectionUtils.containsAny(images, imageIds)) {
                throw new ServiceException(MessageCode.IMAGE_USED_BY_REPORT);
            }
        }
    }

    private void deleteScreenshotsOfImages(long projectId, List<String> imageIds, long userId) throws AccessDatabaseException {
		if(imageIds == null || imageIds.isEmpty()) {
			return;
		}
		if(imageIds.size() == 1) {
            String imageId = imageIds.get(0);
			try {
				List<String> screenshotIds = projectImageMapper.listScreenshotIds(projectId, imageId);
				if(screenshotIds.isEmpty()) {
					return;
				}
				projectImageMapper.deleteScreenshotForImage(projectId, imageId);
			} catch (Exception e) {
				String msg = "Fail to delete onsiteReport elements or delete images for project " + projectId
						+ " with imageId " + imageId;
				throw new AccessDatabaseException(msg, e);
			}
		} else {
			try {
				List<String> screenshotIds = projectImageMapper.listScreenshotIdsIn(projectId, imageIds);
				if(screenshotIds.isEmpty()) {
					return;
				}
				projectImageMapper.deleteScreenshotIn(projectId, imageIds);
			} catch (Exception e) {
				String msg = "Fail to delete onsiteReport elements or delete images for project " + projectId
						+ " with imageIds + " + imageIds;
				throw new AccessDatabaseException(msg, e);
			}
		}
	}

    @Override
    public void deleteCompletely(
            long projectId, long userId, ImageIdsVo imageIds, SystemTypeEnum systemType)
            throws ServiceException {
        judgeReportDeletable(projectId, imageIds.getImageIds());
        // images
        projectImageMapper.deleteCompletely(projectId, imageIds.getImageIds());
        publisher.publishEvent(
                new ProjectImagesDeletedEvent(
                        this,
                        projectId,
                        imageIds.getImageIds(),
                        ProjectImage.COMPLETELY_DELETED,
                        systemType));
        if (!Objects.equals(systemType, SystemTypeEnum.BEES_AI)) {
            submitActivityOnCompletelyDeleteImages(
                projectId, String.valueOf(userId), imageIds.getImageIds().size());
        }
    }

    @Override
    public void deleteCompletely(long projectId, long userId, ImageIdsVo imageIds) throws ServiceException {
        deleteCompletely(projectId, userId, imageIds, SystemTypeEnum.BEES360);
    }

    private void submitActivityOnCompletelyDeleteImages(long projectId, String userId, int count) {
        UserMessage createdBy = UserMessage.newBuilder().setId(userId).build();
        Message.ActivityMessage.Builder activityMessage = Message.ActivityMessage.newBuilder();
        activityMessage
            .setAction("completely deleted")
            .setCreatedBy(createdBy)
            .setProjectId(projectId)
            .setSource(ActivitySourceEnum.WEB.getValue())
            .setEntity(
                Message.ActivityMessage.Entity.newBuilder()
                    .setType(Message.ActivityMessage.EntityType.IMAGE.name())
                    .setCount(count)
                    .build());
        activityManager.submitActivity(Activity.of(activityMessage.build()));
    }

    private List<ProjectImage> listImagesForArchive(long projectId) {
        List<ProjectImage> projectImages = projectImageMapper.listImagesByProjectIdExcludePlaceholder(projectId);

        if (projectImages == null || projectImages.size() == 0) {
            return Collections.emptyList();
        }
        return projectImages.stream().filter(projectImage -> {
            FileSourceTypeEnum fileSourceType = FileSourceTypeEnum.getEnum(projectImage.getFileSourceType());
            return fileSourceType != null && fileSourceType.isRequiredForReport();
        }).collect(Collectors.toList());
    }

    @Override
    public List<String> listImageKeysByProjectIdForArchive(long projectId) {
        List<ProjectImage> images = listImagesForArchive(projectId);
        return images.stream().map(img -> img.getFileName()).distinct().collect(Collectors.toList());
    }

	@Override
	public List<ProjectImage> listAll(long projectId) {
        var images = projectImageMapper.listAll(projectId);
        setTypeByTag(images);
		return images;
	}

    @Override
    public boolean existByPartialType(long projectId,List<ImagePartialViewTypeEnum> types) {
        var typeList =
                types.stream().map(ImagePartialViewTypeEnum::getCode).collect(Collectors.toList());
        return projectImageMapper.existByPartialType(projectId, typeList);
    }

    @Override
    public List<ProjectImage> uploadImagesFromFirebase(long projectId, long userId, @NotEmpty List<ProjectImage> images, boolean addActivity) throws ServiceException {
        images.forEach(
                a -> {
                    a.setProjectId(projectId);
                    if (a.getUserId() == 0) {
                        a.setUserId(userId);
                    }
                });
        images = removeDuplicated(images);
        if (bees360FeatureSwitch.isEnableNotRequiredSyncImage()) {
            removeNotRequiredSyncImages(images);
        }
        images = getNewImagesAndUpdateOldImages(projectId, images);
        addActivityDamageComment(projectId, images);
        // 没有图片要上传了，但是之前上传过。
        if (CollectionUtils.isEmpty(images)) {
            return Collections.emptyList();
        }
        images.forEach(this::initImageId);
        batchInsertIntoDatabase(projectId, userId, images, addActivity);
        if (!CollectionUtils.isEmpty(images)) {
            publisher.publishEvent(new ProjectImageUploadEvent(this, projectId));
            projectService.confirmImageUploaded(projectId, userId, InspectionTypeEnum.LOW_ALTITUDE.getBitMap());
            return images;
        }
        return Collections.emptyList();
    }

    private void removeNotRequiredSyncImages(List<ProjectImage> images) {
        images.removeIf(i -> org.apache.commons.lang3.StringUtils
            .containsAny(i.getImageCategory(), NOT_REQUIRED_SYNC_IMAGE_GROUP_KEYS));
    }

    @Override
	public ProjectImageUploadResponseDto uploadImages(long projectId, long userId,
			FileSourceTypeEnum sourceType, List<ProjectImage> images, Boolean isUploaded) throws ServiceException {
		ProjectImageUploadResponseDto imageDto = new ProjectImageUploadResponseDto();
		if(sourceType == null) {
			throw new ServiceException(MessageCode.IMAGE_SOURCE_TYPE_NNOT);
		}
		if(images == null || images.size() == 0){
			imageDto.setFailImages(new ArrayList<>());
			imageDto.setSuccessImages(new ArrayList<>());
			return imageDto;
		}
		images.forEach(image ->  image.setFileSourceType(sourceType.getCode()));
        uploadNormalImages(projectId, userId, images, imageDto, isUploaded);
        return imageDto;
	}


    /**
     * 去除上传的images中重复的图片（根据fileName去重）
     *
     * @param images
     */
    private List<ProjectImage> removeDuplicated(List<ProjectImage> images) {
        if (CollectionUtils.isEmpty(images)) {
            return Collections.emptyList();
        }

        return new ArrayList<>(images.stream()
            .collect(Collectors.toMap(ProjectImage::getFileName, k -> k, (v1, v2) -> v1))
            .values());
    }

    private void uploadNormalImages(long projectId, long userId, List<ProjectImage> images,
        ProjectImageUploadResponseDto imageDto, Boolean isUploaded) throws ServiceException {
        images = removeDuplicated(images);
        List<ProjectImage> failImages = new ArrayList<>();
        // check if the images has been saved in Amazon S3
        List<ProjectImage> successImages = checkImagesInS3(projectId, images, failImages);
        // add userId to each image and complete their path in AmazonS3
        long uploadTime = DateUtil.getNow();
        for (ProjectImage image : successImages) {
            FileSourceTypeEnum sourceType = FileSourceTypeEnum.getEnum(image.getFileSourceType());
            ImageTypeEnum imageType = ImageTypeEnum.getEnum(image.getImageType());

            // web上传mobile image时没有传partialType，目前手机拍的都是elevation image。
            if (Objects.equals(sourceType, FileSourceTypeEnum.CELL_PHONE_IMAGE)
                && Objects.equals(image.getPartialType(), ImagePartialViewTypeEnum.ROOF.getCode())) {
                image.setPartialType(ImagePartialViewTypeEnum.OTHERS.getCode());
            }

            image.setUserId(userId);
            image.setProjectId(projectId);
            image.setUploadTime(uploadTime);
            image.setAnnotationImage(null);
            image.setDeleted(false);
            image.setImageSort(image.getShootingTime());

            image.setImageType(imageType == null ? ImageTypeEnum.OTHER.getCode() : imageType.getCode());
            if (image.getOrientation() != null) {
                OrientationEnum orientation = OrientationEnum.getEnum(image.getOrientation());
                image.setOrientation(orientation == null ? null : orientation.getCode());

                if (imageType == ImageTypeEnum.ELEVATION && orientation != null) {
                    // the image should contains mobile elevation images
                    throw new ServiceException(MessageCode.PARAM_INVALID);
                }
            }
            if (Objects.equals(sourceType, FileSourceTypeEnum.PLACEHOLDER)) {
                image.setFileName("");
                image.setFileNameMiddleResolution(null);
                image.setFileNameLowerResolution(null);
            } else {
                image.setFileName(image.getFileName());
                image.setFileNameMiddleResolution(image.getFileNameMiddleResolution());
                image.setFileNameLowerResolution(image.getFileNameLowerResolution());
            }
        }

        List<ProjectImage> uploadedImages = removeSavedImages(projectId, successImages);
        if (CollectionAssistant.isEmpty(successImages)) {
            throw new ServiceException(MessageCode.IMAGE_NO_NEED_UPLOAD);
        }

        handleOverviewAndAddressImage(projectId, userId, successImages);

        batchInsertIntoDatabase(projectId, userId, successImages, true);
        eventHistoryService.insertHistoryByImageUpload(projectId, userId, successImages.size());
        int imageUpdateStatus;
        if (isUploaded != null) {
            // isUpload 为 true 是Bees Drone调用的接口，这里直接触发site inspected状态，并触发同步逻辑
            if (isUploaded) {
                projectService.confirmImageUploaded(projectId, userId, InspectionTypeEnum.LOW_ALTITUDE.getBitMap());
                beesPilotStatusService.addImageUploadStatus(userId, projectId, ImageUploadStatusEnum.DRONE_IMAGE_ALL_UPLOADED.getCode()
                    | ImageUploadStatusEnum.VERIFY_ADDRESS_ALL_UPLOADED.getCode()).getImageUploadStatus();
            }
            // isUpload 为 false 是web端调用情况
            // 这里不需要处理图片上传状态，且不需要 触发图片已上传完成
        } else {
            // Bees pilot 会触发这里的逻辑
            // 根据checkout时上传的图片信息，判断是否上传完成
            projectService.confirmImageUploaded(projectId, userId, InspectionTypeEnum.LOW_ALTITUDE.getBitMap());
            imageUpdateStatus = getImageUploadStatus(projectId, successImages);
            beesPilotStatusService.addImageUploadStatus(userId, projectId, imageUpdateStatus);
        }

        publisher.publishEvent(new ProjectImageUploadEvent(this, projectId));

        successImages.addAll(uploadedImages);
        imageDto.setFailImages(failImages);
        imageDto.setSuccessImages(successImages);
    }

    private void handleOverviewAndAddressImage(long projectId, long userId, List<ProjectImage> images)
        throws ServiceException {
        boolean hasDroneOverview = false;
        boolean hasAddressVerify = false;
        for (ProjectImage image : images) {
            FileSourceTypeEnum sourceType = FileSourceTypeEnum.getEnum(image.getFileSourceType());
            ImageTypeEnum imageType = ImageTypeEnum.getEnum(image.getImageType());
            ImagePartialViewTypeEnum partialType = ImagePartialViewTypeEnum.getEnum(image.getPartialType());

            // address   start
            boolean isAddressVerify = Objects.equals(imageType, ImageTypeEnum.ADDRESS)
                || Objects.equals(partialType, ImagePartialViewTypeEnum.ADDRESS);
            if (isAddressVerify) {
                image.setPartialType(ImagePartialViewTypeEnum.ADDRESS.getCode());
                image.setImageType(ImageTypeEnum.ADDRESS.getCode());
                if (hasAddressVerify) {
                    image.setPartialType(ImagePartialViewTypeEnum.OTHERS.getCode());
                    image.setImageType(ImageTypeEnum.OTHER.getCode());
                }
                hasAddressVerify = true;
            }
            // address   end

            // overview   start
            boolean isDroneOverview = Objects.equals(sourceType, FileSourceTypeEnum.DRONE_IMAGE) && Objects.equals(imageType, ImageTypeEnum.OVERVIEW);
            if (isDroneOverview && hasDroneOverview || Objects.isNull(sourceType)) {
                // the image should contains overview images
                image.setImageType(ImageTypeEnum.BIRDVIEW.getCode());
            }
            if (isDroneOverview) {
                hasDroneOverview = true;
            }
            // overview   end
        }

        if (hasDroneOverview) {
            setDroneOverviewToBirdview(projectId);
        }
        if (hasAddressVerify) {
            deleteAddressVerification(projectId, userId);
            beesPilotStatusService.addressVerified(projectId, true);
        }
    }

    /**
     * 返回此次上传的 新增的上传图片状态
     * @param projectId
     * @param successUploadedImages
     * @return
     */
    private int getImageUploadStatus(long projectId, List<ProjectImage> successUploadedImages) {
        int oldImageUploadStatus = beesPilotStatusService.getBeesPilotStatus(projectId).getImageUploadStatus();
        List<ImageInfoRecord> records = imageInfoRecordMapper.listImageInfo(
            ImageRecordInfoQuery.builder().projectId(projectId).uploadedToS3(false).build());
        Set<String> uploadedImageName = successUploadedImages.stream().map(ProjectImage::getOriginalFileName).collect(Collectors.toSet());
        List<ImageInfoRecord> uploadedToS3Record = new ArrayList<>();
        int newImageUploadStatus = 0;
        for (ImageInfoRecord record : records) {
            int fileSourceType = record.getFileSourceType();
            if (uploadedImageName.contains(record.getOriginalFileName())) {
                record.setUploadedToS3(true);
                uploadedToS3Record.add(record);
                if (record.getPartialType() == ImagePartialViewTypeEnum.ADDRESS.getCode() &&
                    !ImageUploadStatusEnum.isAddressVerifiedPartiallyUpload(newImageUploadStatus)) {
                    newImageUploadStatus = newImageUploadStatus | ImageUploadStatusEnum.VERIFY_ADDRESS_ALL_UPLOADED.getCode();
                }
                else if (fileSourceType == FileSourceTypeEnum.DRONE_IMAGE.getCode() &&
                    !ImageUploadStatusEnum.isDroneImagePartiallyUpload(newImageUploadStatus)) {
                    newImageUploadStatus = newImageUploadStatus | ImageUploadStatusEnum.DRONE_IMAGE_ALL_UPLOADED.getCode();
                } else if (fileSourceType == FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode() &&
                    !ImageUploadStatusEnum.isDroneImagePartiallyUpload(newImageUploadStatus)) {
                    newImageUploadStatus = newImageUploadStatus | ImageUploadStatusEnum.MOBILE_IMAGE_ALL_UPLOADED.getCode();
                }
            } else {
                if (fileSourceType == FileSourceTypeEnum.DRONE_IMAGE.getCode()) {
                    oldImageUploadStatus = ImageUploadStatusEnum.setStatusByEnum(newImageUploadStatus, ImageUploadStatusEnum.DRONE_IMAGE_PARTIALLY_UPLOADED);
                    newImageUploadStatus = ImageUploadStatusEnum.setStatusByEnum(newImageUploadStatus, ImageUploadStatusEnum.DRONE_IMAGE_PARTIALLY_UPLOADED);
                } else if (fileSourceType == FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode()) {
                    oldImageUploadStatus = ImageUploadStatusEnum.setStatusByEnum(newImageUploadStatus, ImageUploadStatusEnum.MOBILE_IMAGE_PARTIALLY_UPLOADED);
                    newImageUploadStatus = ImageUploadStatusEnum.setStatusByEnum(newImageUploadStatus, ImageUploadStatusEnum.MOBILE_IMAGE_ALL_UPLOADED);
                }
            }
        }
        for (ImageInfoRecord record : uploadedToS3Record) {
            imageInfoRecordMapper.updateRecord(projectId, record);
        }
        return oldImageUploadStatus | newImageUploadStatus;
    }
    private void setDroneOverviewToBirdview(long projectId) {
        List<ProjectImage> imageList = projectImageMapper.listByImageTypeAndFileSourceType(projectId,
            ImageTypeEnum.OVERVIEW.getCode(), FileSourceTypeEnum.DRONE_IMAGE.getCode(), false);
        if (!imageList.isEmpty()) {
            // In theory, there's only one overview.
            for (ProjectImage image : imageList) {
                projectImageMapper.updateImageType(projectId, image.getImageId(), ImageTypeEnum.BIRDVIEW.getCode());
            }
        }
    }

    /**
     * 如果图片已经在数据库存在了, 就进行更新操作
     *
     * @param projectId    project id
     * @param uploadImages 待上传的image
     */
    @Override
    public List<ProjectImage> getNewImagesAndUpdateOldImages(long projectId, List<ProjectImage> uploadImages) {
        if (CollectionUtils.isEmpty(uploadImages)) {
            return Collections.emptyList();
        }
        List<ProjectImage> images = projectImageMapper.listAllContainDeleted(projectId);
        Map<String, ProjectImage> existedImageMap = ListUtil.toMap(ProjectImage::getFileName, images);
        return uploadImages.stream()
                .filter(image -> isNewImage(image, existedImageMap, projectId))
                .collect(Collectors.toUnmodifiableList());
    }

    private boolean isNewImage(ProjectImage image, Map<String, ProjectImage> existedImageMap, long projectId) {
        boolean isNewImage = true;
        String key = image.getFileName();
        if (existedImageMap.containsKey(key)) {
            isNewImage = false;
            ProjectImage existedImage = existedImageMap.get(key);
            if (image.getFileSourceType() != existedImage.getFileSourceType()
                || image.getPartialType() != existedImage.getPartialType()
                || image.getImageType() != existedImage.getImageType()
                || !Objects.equals(image.getRoomName(), existedImage.getRoomName())
                || !Objects.equals(image.getFloorLevel(), existedImage.getFloorLevel())
                || !Objects.equals(image.getNumber(), existedImage.getNumber())
                || image.getDeleted() != existedImage.getDeleted()) {
                image.setImageId(existedImage.getImageId());
                image.setProjectId(projectId);
                // 判断照片是否有更改
                // 如果web删除了该图片，则应该将该图片设置为已删除
                if (existedImage.getDeleted()) {
                    image.setDeleted(true);
                }
                projectImageMapper.updateImageProperty(projectId, image);
            }
         }
        return isNewImage;
    }

    /**
     * 添加activity damage comment
     *
     * @param projectId 项目Id
     * @param uploadImages 图片列表
     */
    private void addActivityDamageComment(long projectId, List<ProjectImage> uploadImages) {
        if (CollectionUtils.isEmpty(uploadImages)) {
            logger.info("add damage comment has no image projectId {}", projectId);
            return;
        }

        List<? extends Comment> comments =
                commentManager.getComments(
                        CommentQuery.builder()
                                .projectId(projectId)
                                .source(ActivitySourceEnum.BEESPILOT_DAMAGE.getValue())
                                .build());
        Map<String, String> existedCommentMap =
                comments.stream()
                        .map(Comment::getContent)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(), Function.identity(), (a, b) -> b));

        // 获取图片中去重后的damage信息
        Map<String, ProjectImage> damageMap =
                uploadImages.stream()
                        .filter(e -> null != e.getRoomName())
                        .filter(e -> null != e.getDescription())
                        .collect(
                                Collectors.toMap(
                                        this::getDamageComment, Function.identity(), (a, b) -> b));

        // 有新增的damage信息就新增上去
        damageMap.forEach(
                (a, b) -> {
                    if (existedCommentMap.containsKey(a)) {
                        return;
                    }
                    Comment comment =
                            Comment.from(
                                    projectId, String.valueOf(b.getUserId()), getDamageComment(b));
                    Message.CommentMessage.Builder builder = comment.toMessage().toBuilder();
                    builder.setSource(ActivitySourceEnum.BEESPILOT_DAMAGE.getValue());
                    if (bees360FeatureSwitch.isEnableSystemComment()) {
                        activityManager.submitActivity(
                                CommentServiceImpl.from(Comment.from(builder.build())));
                    } else {
                        commentManager.addComment(Comment.from(builder.build()));
                    }
                });
    }

    private String getDamageComment(ProjectImage projectImage) {
        return projectImage.getRoomName() + ": " + projectImage.getDescription();
    }

    /**
     * 移除uploadImages中已上传的image
     *
     * @param projectId project id
     * @param uploadImages 待上传的image
     * @return uploadImages中已上传的image
     */
    private List<ProjectImage> removeSavedImages(long projectId, List<ProjectImage> uploadImages) {
        List<ProjectImage> images = listAll(projectId);
        Map<String, ProjectImage> saveImages = ListUtil.toMap(image -> ResourceKeyManager.parseFileName(image.getFileName()), images);
        List<ProjectImage> uploadedImages = new ArrayList<>();
        for (ProjectImage image : uploadImages) {
            if (saveImages.containsKey(ResourceKeyManager.parseFileName(image.getFileName()))) {
                uploadedImages.add(saveImages.get(ResourceKeyManager.parseFileName(image.getFileName())));
            }
        }
        uploadImages.removeIf(image -> saveImages.containsKey(ResourceKeyManager.parseFileName(image.getFileName())));
        return uploadedImages;
    }

    private void deleteAddressVerification(long projectId, long userId) throws ServiceException {
        try {
            List<ProjectImage> addressVerificationImages = projectImageMapper
                .listByPartialType(projectId, ImagePartialViewTypeEnum.ADDRESS.getCode());
            if(!CollectionAssistant.isEmpty(addressVerificationImages)) {
                // existed: update the address verification image
                // There is only one theory.
                for (ProjectImage addressImage : addressVerificationImages) {
                    deleteById(addressImage.getImageId(), projectId, userId);
                }
            }
        } catch (Exception e) {
            logger.error("Fail to upload mobile image in front elevation to the project " + projectId, e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION, e);
        }
    }

	/**
	 * @param projectId
	 * @param imagesUploaded: the images uploaded by users.
	 * @param failImages: images not in AmazonS3 by judged.
	 * @return successImages: images has been upload to AmazonS3 by frontend.
     */
	private List<ProjectImage> checkImagesInS3(long projectId, List<ProjectImage> imagesUploaded,
			List<ProjectImage> failImages) {
        List<ProjectImage> successImages = new ArrayList<>();
        if (CollectionAssistant.isEmpty(imagesUploaded)) {
            return successImages;
        }
        List<ProjectImage> imageNeedChecked = new ArrayList<>();
        for (ProjectImage image : imagesUploaded) {
            if (StringUtils.isBlank(image.getFileName())) {
                failImages.add(image);
                logger.warn("image ({}) from client has a blank filename.", image.getImageId());
            } else {
                imageNeedChecked.add(image);
            }
        }

        checkImagesInS3OneByOne(imageNeedChecked, successImages, failImages);
        return successImages;
	}

	/**
	 * check image in S3 or not one by one.
	 * <AUTHOR>
	 * @param imageNeedChecked
	 * @param successImages
	 * @param failImages
	 */
	private void checkImagesInS3OneByOne(List<ProjectImage> imageNeedChecked,
			List<ProjectImage> successImages, List<ProjectImage> failImages){
		for(ProjectImage image: imageNeedChecked){
			if(image == null){
				continue;
			}
			try {
                ResourceMetadata resourceMetadata = resourcePool.head(image.getFileName());
                if (resourceMetadata != null && Objects.equals(image.getFileSize(),resourceMetadata.getContentLength())){
					successImages.add(image);
				} else {
					failImages.add(image);
                    logger.warn("Fail to check image " + image + " in S3 or not");
				}
			} catch (Exception e) {
				failImages.add(image);
				logger.error("Fail to check image " + image + " in S3 or not", e);
			}
		}
	}

    private void batchInsertIntoDatabase(long projectId, long userId, List<ProjectImage> images, boolean addActivity) {
	    if (CollectionAssistant.isEmpty(images)) {
            return;
        }
        // 根据注入策略对特定image进行排序,赋予初始顺序
        images = projectImageReSorter.apply(images);

        List<String> keys = new ArrayList<>();
        images.forEach(image -> {
            initImageId(image);
            keys.add(image.getImageS3Key());
            keys.add(image.getMiddleResolutionImageS3Key());
            keys.add(image.getLowerResolutionImageS3Key());
        });
        if (saveSolidData) {
            saveSolidImages(projectId, userId, images);
        }
        if (!bees360FeatureSwitch.isDisableUploadImageActivity() ||
            addActivity) {
            ProjectImage image = images.get(0);
            publisher.publishEvent(new ImageUploadedEventForActivity(this, keys, image.getProjectId(),image.getUserId()));
        }
        // 确认正常触发图片同步之后才能将图片插入数据库
        // 将图片的image url 改成key
        images.forEach(this::imageUrlToKey);
        // TODO zhaoshoushan originalFileName应该去掉，前后端应该都没有再用了。
        //  暂时先解决长度过长导致的问题(see bug 项目组-202305-0234)
        images.forEach(this::cropOriginalFileName);
        projectImageMapper.insertBaseInfoList(images);

        // 将image_category字段插入ProjectImageTag表
        List<ProjectImage> imageWithTags = images.stream()
                                              .filter(i -> StringUtils.isNotEmpty(i.getImageCategory()))
                                              .collect(Collectors.toList());
        if (!imageWithTags.isEmpty()) {
            projectImageTagMapper.insertImageCategoryList(imageWithTags);
        }
    }

    private void saveSolidImages(long projectId, long userId, List<ProjectImage> images) {
        long start = System.currentTimeMillis();
        List<String> imageIds = null;
        try {
            imageIds = createSolidGroupImageReturnId(projectId, userId, images);
        } catch (IllegalArgumentException | IllegalStateException e) {
            logger.error(
                    "Fail to save images to solid.projectId {}, images {}",
                    projectId,
                    ListUtil.toString(images),
                    e);
        }

        if (imageIds == null || images.size() != imageIds.size()) {
            logger.error(
                    "The number of saved images does not match.projectId {}, images {}",
                    projectId,
                    ListUtil.toString(images));
            return;
        }

        for (int i = 0; i < images.size(); i++) {
            images.get(i).setImageId(imageIds.get(i));
        }

        if (bees360FeatureSwitch.isEnableSaveImageNote()) {
            var imageNotes =
                    images.stream()
                            .filter(i -> StringUtils.isNotBlank(i.getNote()))
                            .map(
                                    i -> {
                                        var userMessage =
                                                UserMessage.newBuilder()
                                                        .setId(String.valueOf(userId))
                                                        .build();
                                        var noteMessage =
                                                ImageMessage.Note.newBuilder()
                                                        .setImageId(i.getImageId())
                                                        .setNote(i.getNote())
                                                        .setCreatedBy(userMessage)
                                                        .build();
                                        return new ProtobufImageNote(noteMessage);
                                    })
                            .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(imageNotes)) {
                imageNoteManager.saveAll(imageNotes);
            }
        }

        logger.info(
                "Initialize image completed. It takes {} seconds.",
                System.currentTimeMillis() - start);
    }

    private List<String> createSolidGroupImageReturnId(
            long projectId, long userId, List<ProjectImage> images) {
        var projectGroup =
                GroupImageSource.Group.builder()
                        .groupType(GROUP_TYPE_PROJECT)
                        .groupKey(String.valueOf(projectId))
                        .build();
        var groupImageSources =
                Iterables.transform(
                        images,
                        image -> {
                            var firebaseImageKey = image.getFirebaseImageKey();
                            List<GroupImageSource.Group> groupList = new ArrayList<>();
                            groupList.add(projectGroup);
                            var tagCategory =
                                    StringUtils.isEmpty(image.getImageCategory())
                                            ? EMPTY_TAG_CATEGORY
                                            : image.getImageCategory();
                            ImageSource imageSource;
                            if (bees360FeatureSwitch.isEnableSaveImageShootingTime()) {
                                imageSource =
                                    ImageSource.of(
                                        image.getFileName(), StringUtils.EMPTY, tagCategory,
                                        getShootingTimeInstant(image.getShootingTime()));
                            } else {
                                imageSource =
                                    ImageSource.of(
                                        image.getFileName(), StringUtils.EMPTY, tagCategory);
                            }
                            if (StringUtils.isNotBlank(firebaseImageKey)) {
                                var firebaseGroup =
                                        GroupImageSource.Group.builder()
                                                .groupType(GROUP_TYPE_FIREBASE)
                                                .groupKey(firebaseImageKey)
                                                .build();
                                groupList.add(firebaseGroup);
                            }
                            if (bees360FeatureSwitch.isEnableSaveImagePilot()) {
                                var imagePilotGroup =
                                    GroupImageSource.Group.builder()
                                        .groupType(GROUP_TYPE_PILOT)
                                        .groupKey(String.valueOf(image.getUserId()))
                                        .build();
                                groupList.add(imagePilotGroup);
                            }
                            return GroupImageSource.of(imageSource, groupList);
                        });
        return Iterables.toStream(
                        grpcImageGroupClient.createGroupImage(
                                groupImageSources, String.valueOf(userId)))
                .map(Image::getId)
                .collect(Collectors.toList());
    }

    private Instant getShootingTimeInstant(long shootingTime) {
        if (shootingTime > 0) {
            return Instant.ofEpochMilli(shootingTime);
        }
        return null;
    }

    private void cropOriginalFileName(ProjectImage image) {
        var originalFileName = image.getOriginalFileName();
        if (StringUtils.isBlank(originalFileName)) {
            return;
        }
        image.setOriginalFileName(
                StringUtils.substring(
                        originalFileName,
                        Math.max(0, originalFileName.length() - ORIGINAL_FILE_NAME_LENGTH)));
    }

    /**
     * 将图片的原图、中图以及小图改成key
     * @param image
     */
    private void imageUrlToKey(ProjectImage image) {
        image.setFileNameLowerResolution(image.getLowerResolutionImageS3Key());
        image.setFileNameMiddleResolution(image.getMiddleResolutionImageS3Key());
        image.setFileName(image.getImageS3Key());
    }
    private <T extends ProjectImage> void initImageId(T image) {
        if (StringUtils.isEmpty(image.getImageId())) {
            image.setImageId(UUIDUtil.getImageUUID());
        }
    }

	@Override
	public List<ProjectImage> listImageByImageType(long projectId,
			FileSourceTypeEnum fileSourceType, ImageTypeEnum imageType) throws ServiceException {
		List<ProjectImage> images;
		try {
			images = projectImageMapper.listImageByImageType(projectId,
					fileSourceType.getCode(), imageType.getCode());
		} catch (Exception e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
		return images;
	}

    @Override
    public ProjectImagePageResult listTinyImagesWithPage(ProjectImageSearchOptionDto queryParameter)
        throws ServiceException {
        boolean needPage = queryParameter.getLimit() == null &&
            queryParameter.getPageIndex() != null && queryParameter.getPageSize() != null;

        if (queryParameter.getLimit() != null) {
            queryParameter.setStartIndex(0);
            queryParameter.setPageSize(queryParameter.getLimit());
        } else if (needPage) {
            Integer pageIndex = queryParameter.getPageIndex();
            Integer pageSize = queryParameter.getPageSize();
            int startIndex = (pageIndex - 1) * pageSize;
            queryParameter.setStartIndex(startIndex);
        }
        int annotationImageType = FileSourceTypeEnum.REPORT_IMAGE.getCode();
        boolean needReportType = Objects.equals(queryParameter.getFileSourceType(), annotationImageType)
            || (!CollectionAssistant.isEmpty(queryParameter.getFileSourceTypes())
            && queryParameter.getFileSourceTypes().contains(annotationImageType));
        queryParameter.setNeedReportType(needReportType);

        ProjectImagePageResult pageResult = new ProjectImagePageResult();
        int sum;
        try {
            List<ProjectImageAnnotationVo> images = projectImageMapper.listImagesPage(queryParameter);

            setTypeByTag(images);

            pageResult.setImages(images);
            if (needPage) {
                sum = projectImageMapper.countImagesPageTotal(queryParameter);
                Pagination page = new Pagination(queryParameter.getPageIndex(), queryParameter.getPageSize());
                page.setSumAndGenaratePages(sum);
                pageResult.setPage(page);
            }
        } catch (Exception e) {
            logger.error(MessageCode.DATABASE_EXCEPTION, e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
        }
        return pageResult;
    }

    private void setTypeByTag(List<? extends ProjectImage> images) {
        if (CollectionUtils.isEmpty(images)) {
            return;
        }
        var imageIds =
                images.stream().map(this::getParentIdOrOriginId).collect(Collectors.toList());
        var imageTags = imageTagProvider.findByImageIds(imageIds);
        if (CollectionUtils.isEmpty(imageTags)) {
            return;
        }

        for (var image: images) {
            // 很久以前的数据parentId有空字符串
            var id = getParentIdOrOriginId(image);
            if (!imageTags.containsKey(id)) {
                continue;
            }

            var tags =
                    Iterables.toStream(imageTags.get(id))
                            .map(t -> Integer.parseInt(t.getId()))
                            .collect(Collectors.toSet());
            Optional.ofNullable(getOrientationByTag(tags))
                    .ifPresentOrElse(
                            t -> image.setOrientation(t.getCode()),
                            () -> image.setOrientation(null));
            Optional.ofNullable(getDirectionByTag(tags))
                    .ifPresentOrElse(
                            t -> image.setDirection(t.getCode()),
                            () -> image.setDirection(DirectionEnum.DEFAULT));
            Optional.ofNullable(getImageTypeByTag(tags)).ifPresent(t -> image.setImageType(t.getCode()));
            Optional.ofNullable(getPartialTypeByTag(tags)).ifPresent(t -> image.setPartialType(t.getCode()));
        }
    }

    private String getParentIdOrOriginId(ProjectImage image) {
        if (image.getFileSourceType() != FileSourceTypeEnum.REPORT_IMAGE.getCode()) {
            return image.getImageId();
        }
        return StringUtils.isNotBlank(image.getParentId())
            ? image.getParentId()
            : image.getImageId();
    }

    private OrientationEnum getOrientationByTag(Set<Integer> tags) {
        if (tags.contains(ImageTagEnum.FRONT.getCode()) || tags.contains(ImageTagEnum.FR.getCode())) {
            return OrientationEnum.FRONT;
        } else if (tags.contains(ImageTagEnum.RIGHT.getCode()) || tags.contains(ImageTagEnum.RR.getCode())) {
            return OrientationEnum.RIGHT;
        } else if (tags.contains(ImageTagEnum.REAR.getCode()) || tags.contains(ImageTagEnum.RL.getCode())) {
            return OrientationEnum.BACK;
        } else if (tags.contains(ImageTagEnum.LEFT.getCode()) || tags.contains(ImageTagEnum.FL.getCode())) {
            return OrientationEnum.LEFT;
        }
        return null;
    }

    private DirectionEnum getDirectionByTag(Set<Integer> tags) {
        if (tags.contains(ImageTagEnum.NORTH.getCode())) {
            return DirectionEnum.NORTH;
        } else if (tags.contains(ImageTagEnum.EAST.getCode())) {
            return DirectionEnum.EAST;
        } else if (tags.contains(ImageTagEnum.SOUTH.getCode())) {
            return DirectionEnum.SOUTH;
        } else if (tags.contains(ImageTagEnum.WEST.getCode())) {
            return DirectionEnum.WEST;
        }
        return null;
    }

    private ImageTypeEnum getImageTypeByTag(Set<Integer> tags) {
        if (tags.contains(ImageTagEnum.CLOSEUP.getCode())) {
            return ImageTypeEnum.CLOSEUP;
        } else if (tags.contains(ImageTagEnum.ZIGZAG.getCode())) {
            return ImageTypeEnum.ZIGZAG;
        } else if (tags.contains(ImageTagEnum.ROOF_LAYER.getCode())) {
            return ImageTypeEnum.ROOF_LAYER;
        } else if (tags.contains(ImageTagEnum.ELEVATION.getCode())) {
            return ImageTypeEnum.ELEVATION;
        } else if (tags.contains(ImageTagEnum.OVERVIEW.getCode())) {
            var imageType = ImageTypeEnum.OVERVIEW;
            HashSet<Integer> resSet = new HashSet<>(DIRECTION_TAGS);
            resSet.retainAll(tags);
            if (!CollectionUtils.isEmpty(resSet)) {
                imageType = ImageTypeEnum.BIRDVIEW;
            }
            return imageType;
        }
        return ImageTypeEnum.OTHER;
    }

    private ImagePartialViewTypeEnum getPartialTypeByTag(Set<Integer> tags) {
        if (tags.contains(ImageTagEnum.ROOF.getCode())) {
            return ImagePartialViewTypeEnum.ROOF;
        } else if (tags.contains(ImageTagEnum.ELEVATION.getCode())) {
            return ImagePartialViewTypeEnum.ELEVATION;
        } else if (tags.contains(ImageTagEnum.INTERIOR.getCode())) {
            var partialType = ImagePartialViewTypeEnum.INTERIOR;
            if (tags.contains(ImageTagEnum.ELECTRICAL_PANEL.getCode())) {
                partialType = ImagePartialViewTypeEnum.ELECTRICAL_PANEL;
            } else if (tags.contains(ImageTagEnum.SUPPLY_LINES.getCode())) {
                partialType = ImagePartialViewTypeEnum.PLUMBING_SYSTEM;
            } else if (tags.contains(ImageTagEnum.WATER_HEATER.getCode())) {
                partialType = ImagePartialViewTypeEnum.WATER_HEATER;
            } else if (tags.contains(ImageTagEnum.DAMAGE.getCode())) {
                partialType = ImagePartialViewTypeEnum.DAMAGE_AREA;
            } else if (tags.contains(ImageTagEnum.OTHER_ITEMS.getCode())) {
                partialType = ImagePartialViewTypeEnum.OTHER_ITEMS;
            } else if (tags.contains(ImageTagEnum.OVERVIEW.getCode())) {
                partialType = ImagePartialViewTypeEnum.ROOM_OVERVIEW;
                if (tags.contains(ImageTagEnum.FLOOR.getCode())) {
                    partialType = ImagePartialViewTypeEnum.FLOOR_OVERVIEW;
                } else if (tags.contains(ImageTagEnum.CEILING.getCode())) {
                    partialType = ImagePartialViewTypeEnum.CEILING_OVERVIEW;
                }
            }
            return partialType;
        } else if (tags.contains(ImageTagEnum.DETACHED_GARAGE.getCode())) {
            return ImagePartialViewTypeEnum.GARAGE;
        } else if (tags.contains(ImageTagEnum.OTHER_STRUCTURE.getCode())) {
            return ImagePartialViewTypeEnum.OTHER_STRUCTURES;
        } else if (tags.contains(ImageTagEnum.CONTENT.getCode())) {
            return ImagePartialViewTypeEnum.OTHERS;
        } else if (tags.contains(ImageTagEnum.ADDRESS_VERIFICATION.getCode())) {
            return ImagePartialViewTypeEnum.ADDRESS;
        } else if (tags.contains(ImageTagEnum.HAZARDS.getCode())) {
            return ImagePartialViewTypeEnum.HAZARDS;
        }
        return ImagePartialViewTypeEnum.OTHERS;
    }

	@Override
	public void updateImageDeletedStatus(long projectId, List<String> imageIds, boolean deleted) {
		if(imageIds == null || imageIds.size() == 0){
			return;
		}
        projectImageMapper.updateImageDeleted(projectId, imageIds, deleted);
	}

	@Override
	public int countImages(long projectId, Integer fileSourceType, boolean isDeleted) {
		return projectImageMapper.countImages(projectId, fileSourceType, null, isDeleted);
	}

    @Override
    public ProjectImage getImageById(String imageId) {
        ProjectImage projectImage = projectImageMapper.getImageById(imageId);
        if (projectImage == null) {
            throw new ResourceNotFoundException("Image is not Found");
        }
        return projectImage;
    }
}
