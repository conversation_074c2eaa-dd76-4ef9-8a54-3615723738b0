package com.bees360.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.User;
import com.bees360.service.UserCacheService;
import com.bees360.util.RedisUtil;

@Service("userCacheService")
public class UserCacheServiceImpl implements UserCacheService{

	public static final String REDIS_KEY_USER = "user";
	public static final String SEPERATOR = ":";
	public static final String REDIS_KEY_USER_FIELD_ACCOUNT = "user:account";
	public static final String REDIS_KEY_USER_FIELD_SECURITYCODE = "user:securityCode";
	public static final String REDIS_KEY_USER_FIELD_SECURITYCODE_AVAILABLETIME = "user:securityCodeAvailableTime";

	private static final Logger logger = LoggerFactory.getLogger(UserCacheServiceImpl.class);

	@Autowired
	RedisUtil redisUtil;

	@Override
	public User getUser(long userId) throws ServiceException {
		Object userObject = null;
		try {
			userObject = redisUtil.get(REDIS_KEY_USER + SEPERATOR + String.valueOf(userId));
		}catch(Exception e) {
			logger.error("UserCacheServiceImpl.getUser failed:",e);
			throw new ServiceException(MessageCode.REDIS_OPERATION_FAILED);
		}
		if(userObject == null) {
			throw new ServiceException(MessageCode.REDIS_OPERATION_FAILED);
		}
		User user = JSONObject.parseObject(userObject.toString(),User.class);
		return user;
	}


	public void setUser(long userId, User user, long timeout) throws ServiceException{
		try {
			redisUtil.set(REDIS_KEY_USER + SEPERATOR + String.valueOf(userId), JSONObject.toJSONString(user), timeout);
		}catch(Exception e) {
			logger.error("UserCacheServiceImpl.setUser failed:",e);
			throw new ServiceException(MessageCode.REDIS_OPERATION_FAILED);
		}
	}

	public void deleteUser(long userId) throws ServiceException{
		try {
			redisUtil.delete(REDIS_KEY_USER + SEPERATOR + String.valueOf(userId));
		}catch(Exception e) {
			logger.error("UserCacheServiceImpl.getUser failed:",e);
			throw new ServiceException(MessageCode.REDIS_OPERATION_FAILED);
		}
	}

	@Override
	public String getSecurityCode(String account) throws ServiceException {
		try {
			Object securityCodeInRedis = redisUtil.hGet(REDIS_KEY_USER + SEPERATOR + account, REDIS_KEY_USER_FIELD_SECURITYCODE);
			return securityCodeInRedis == null ? null : (String)securityCodeInRedis;
		}catch(Exception e) {
			logger.error("UserCacheServiceImpl.getSecurityCode failed:",e);
			throw new ServiceException(MessageCode.REDIS_OPERATION_FAILED);
		}
	}

	@Override
	public long getSecurityCodeAvailableTime(String account) throws ServiceException {
		try {
			Object availableTimeInRedis = redisUtil.hGet(REDIS_KEY_USER + SEPERATOR + account, REDIS_KEY_USER_FIELD_SECURITYCODE_AVAILABLETIME);
			return availableTimeInRedis == null ? 0 : (long)availableTimeInRedis;
		}catch(Exception e) {
			logger.error("UserCacheServiceImpl.getSecurityCodeAvailableTime failed:",e);
			throw new ServiceException(MessageCode.REDIS_OPERATION_FAILED);
		}
	}

	@Override
	public boolean hasAccount(String account) throws ServiceException {
		try {
			Boolean existed = redisUtil.hasKey(REDIS_KEY_USER + SEPERATOR + account);
			return existed == null ? false : existed;
		}catch(Exception e) {
			logger.error("UserCacheServiceImpl.hasAccount failed:",e);
			throw new ServiceException(MessageCode.REDIS_OPERATION_FAILED);
		}
	}

	@Override
	public void deleteAccount(String account) throws ServiceException {
		try {
			redisUtil.delete(REDIS_KEY_USER + SEPERATOR + account);
		}catch(Exception e) {
			logger.error("UserCacheServiceImpl.deleteAccount failed:",e);
			throw new ServiceException(MessageCode.REDIS_OPERATION_FAILED);
		}
	}

	@Override
	public void setSecurityCodeInfo(String account, String securityCode, long availableTime, long timeout) throws ServiceException {
		try {
			redisUtil.hPut(REDIS_KEY_USER + SEPERATOR + account, REDIS_KEY_USER_FIELD_ACCOUNT, account);
			redisUtil.hPut(REDIS_KEY_USER + SEPERATOR + account, REDIS_KEY_USER_FIELD_SECURITYCODE, securityCode);
			redisUtil.hPut(REDIS_KEY_USER + SEPERATOR + account, REDIS_KEY_USER_FIELD_SECURITYCODE_AVAILABLETIME, availableTime);
			redisUtil.expire(REDIS_KEY_USER + SEPERATOR + account,timeout);
		}catch(Exception e) {
			logger.error("UserCacheServiceImpl.getSecurityCodeAvailableTime failed:",e);
			throw new ServiceException(MessageCode.REDIS_OPERATION_FAILED);
		}
	}
}
