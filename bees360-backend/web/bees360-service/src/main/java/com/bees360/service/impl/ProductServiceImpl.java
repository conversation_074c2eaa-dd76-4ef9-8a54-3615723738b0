package com.bees360.service.impl;

import java.util.*;

import jakarta.inject.Inject;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.entity.vo.ProductListVo;
import com.bees360.util.DateUtil;
import org.springframework.stereotype.Service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Product;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.productandpayment.ProductTypeEnum;
import com.bees360.entity.vo.ProductReportVo;
import com.bees360.mapper.ProductMapper;
import com.bees360.service.ProductService;
import com.bees360.util.payment.ProductStore;

@Service("productService")
public class ProductServiceImpl implements ProductService {

	@Inject
	private ProductMapper productMapper;

	// TODO 这部分以后在做商品中心的时候移到数据库中做实现，取字段名为 is_published
	private static Set<Integer> REPORT_PRODUCT_PUBLISHED = new HashSet<>(Arrays.asList(
		ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT.getCode(),
		ReportTypeEnum.PREMIUM_ROOF_MEASUREMENT_REPORT.getCode(),
		ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT.getCode(),
		ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT.getCode(),
		ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT.getCode()
	));

	@Override
	public List<ProductReportVo> listOrderableReports() throws ServiceException {
		List<Product> products = productMapper.list(null);
		ProductStore productStore = new ProductStore(products);
		List<ProductReportVo> productReports = new ArrayList<ProductReportVo>();

		for(ReportTypeEnum type: ReportTypeEnum.values()) {
			Product product = productStore.getByType(ProductTypeEnum.REPORT, type.getCode());
			if(!type.isOrderable() || product == null) {
				continue;
			}
			ProductReportVo productReportVo = new ProductReportVo();
			productReportVo.setReportType(type.getCode());
			productReportVo.setReportName(type.getDisplay());
			productReportVo.setPriceType(product.getPriceType());
			productReportVo.setPrice(product.getPrice());
			productReportVo.setUrl(product.getUrl());
			productReportVo.setCaption(product.getCaption());

			productReports.add(productReportVo);
		}

		return productReports;
	}

	@Override
	public List<Product> listAllProducts() throws ServiceException {
		return productMapper.list(null);
	}

	@Override
	public List<Product> listProducts(Integer productType, Boolean isPublished) throws ServiceException {
		List<Product> products = productMapper.list(productType);
		return isPublished == null? products: filterPublishedProduct(products, isPublished);
	}

	@Override
	public List<Product> listPublishedProducts(Integer productType) throws ServiceException {
		return listProducts(productType, true);
	}

	/**
	 * 过滤出已经发布的商品
	 */
	private List<Product> filterPublishedProduct(List<Product> products, boolean isPublished) {
		List<Product> productList = new ArrayList<>();
		for(Product p: products) {
			if(isPublishedProduct(p) == isPublished) {
				productList.add(p);
			}
		}
		return productList;
	}

	private boolean isPublishedProduct(Product product) {
		return product.getProductType() == ProductTypeEnum.REPORT.getCode()
			&& REPORT_PRODUCT_PUBLISHED.contains(product.getInternalType());
	}

	@Override
	public Product updateProductInfo(long userId, int productId, Product product) throws ServiceException {
		Product productInDb = productMapper.getById(productId);
		if(productInDb == null) {
			throw new ResourceNotFoundException();
		}
		productInDb.setPriceType(product.getPriceType());
		productInDb.setPrice(product.getPrice());
		productInDb.setSubtitle(product.getSubtitle());
		productInDb.setCaption(product.getCaption());
		productInDb.setUrl(product.getUrl());
		productInDb.setUpdatedTime(DateUtil.getNow());
		productMapper.updateProductInfo(product);

		return productMapper.getById(productId);
	}
}
