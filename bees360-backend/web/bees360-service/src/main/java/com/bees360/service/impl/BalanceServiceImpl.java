package com.bees360.service.impl;

import jakarta.inject.Inject;

import org.springframework.stereotype.Service;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.UserPaymentProfile;
import com.bees360.entity.dto.PaymentResult;
import com.bees360.entity.enums.productandpayment.PaymentChannelEnum;
import com.bees360.entity.vo.ProjectOrders;
import com.bees360.service.BalanceService;
import com.bees360.service.payment.UserPaymentProfileService;

@Service("balanceService")
public class BalanceServiceImpl implements BalanceService {

	@Inject
	private UserPaymentProfileService userPaymentProfileService;

	@Override
	public PaymentResult payForOrders(long userId, ProjectOrders projectOrders) throws ServiceException {
		UserPaymentProfile userPaymentProfile = userPaymentProfileService.getUserPaymentProfile(userId);
		if(userPaymentProfile.getWalletBalance() < projectOrders.getTotalPrice()) {
			throw new ServiceException(MessageCode.TRANSACTION_BALANCE_NOT_ENOUGH);
		}
		double walletBalance = userPaymentProfile.getWalletBalance() - projectOrders.getTotalPrice();
		// <EMAIL> add transaction record
		// <EMAIL> remove the currency
		userPaymentProfileService.updateUserPaymentProfileByBalance(userId, walletBalance, userPaymentProfile.getCurrency());
		return new PaymentResult(PaymentChannelEnum.BALANCE.getCode());
	}
}
