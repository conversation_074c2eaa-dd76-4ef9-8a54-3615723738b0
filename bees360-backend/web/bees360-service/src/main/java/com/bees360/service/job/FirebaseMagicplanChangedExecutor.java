package com.bees360.service.job;

import com.bees360.job.registry.SerializableFirebaseMagicplan;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseMagicplanService;
import com.google.cloud.firestore.Firestore;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;

import static com.bees360.service.job.FirebaseMissionChangedExecutor.translateExceptionAndThrow;

@Log4j2
@Component
@ToString
public class FirebaseMagicplanChangedExecutor
        extends AbstractJobExecutor<SerializableFirebaseMagicplan> {

    private final FirebaseMagicplanService firebaseMagicplanService;
    private final Firestore firestore;

    public FirebaseMagicplanChangedExecutor(
            FirebaseMagicplanService firebaseMagicplanService, Firestore firestore) {
        this.firebaseMagicplanService = firebaseMagicplanService;
        this.firestore = firestore;

        log.info("Created '{}'", this);
    }

    @Override
    protected void handle(SerializableFirebaseMagicplan magicplan) throws IOException {
        try {
            log.info("Start to handle magicplan '{}' '{}'", magicplan.getId(), magicplan);
            firebaseMagicplanService.handleMagicplan(magicplan);
            log.info("Successfully handle magicplan '{}'", magicplan.getId());
        } catch (RuntimeException e) {
            log.warn("Failed to handle magicplan '{}'.", magicplan, e);
            translateExceptionAndThrow(e);
        }
    }
}
