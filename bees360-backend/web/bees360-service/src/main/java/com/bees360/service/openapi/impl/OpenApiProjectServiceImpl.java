package com.bees360.service.openapi.impl;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.ServiceMessageException;
import com.bees360.customer.DivisionManager;
import com.bees360.entity.Company;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.dto.ProjectDto;
import com.bees360.entity.dto.ProjectSearchResultFilter;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.openapi.OpenProjectCreateVo;
import com.bees360.entity.openapi.OpenProjectSearchOption;
import com.bees360.entity.openapi.OpenProjectStatusVo;
import com.bees360.entity.openapi.OpenProjectVo;
import com.bees360.entity.openapi.OpenProjectWithStatusVo;
import com.bees360.entity.openapi.OpenReportIdTypeVo;
import com.bees360.entity.vo.ProjectPageResultVo;
import com.bees360.policy.Coverage;
import com.bees360.policy.Message;
import com.bees360.project.ProjectIIRepository;
import com.bees360.service.CompanyService;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.openapi.OpenApiProjectService;
import com.bees360.service.openapi.ProjectCreationPrehandle;
import com.bees360.service.openapi.converter.ProjectConverter;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.service.util.ProjectRuleUtil;
import com.bees360.service.util.ProjectSupplementalServiceTypeUtil;
import com.bees360.util.Functions;
import com.bees360.util.Iterables;
import com.google.common.collect.Sets;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Guanrong
 * @date 2019/12/26 16:11
 */
@Slf4j
@Service
public class OpenApiProjectServiceImpl implements OpenApiProjectService {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectStatusService projectStatusService;

    @Autowired
    private ProjectReportFileService projectReportFileService;

    @Autowired
    private ProjectCreationPrehandle projectCreationPrehandle;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private ProjectSupplementalServiceTypeUtil projectSupplementalServiceTypeUtil;

    @Autowired
    private DivisionManager divisionManager;

    @Autowired
    private ProjectIIRepository projectIIProvider;

    @Autowired
    private Bees360CompanyConfig companyConfig;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Transactional(rollbackFor = {ServiceException.class})
    @Override
    public OpenProjectVo createProject(long userId, OpenProjectCreateVo projectCreateVo) throws ServiceException {
        if(ProjectServiceTypeEnum.getEnumByValue(projectCreateVo.getServiceName()) == null) {
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, "serviceName: must be one of options.");
        }

        if (!projectSupplementalServiceTypeUtil.checkValid(projectCreateVo.getSupplementalServices())) {
            throw new ServiceMessageException(MessageCode.PARAM_INVALID, "supplementalService: must be on of options");
        }

        if(StringUtils.isNoneEmpty(projectCreateVo.getDivision())) {
            ensureDivisionExistence(projectCreateVo, userId);
        }

        projectCreateVo.setPolicyNumber(StringUtils.trim(projectCreateVo.getPolicyNumber()));
        projectCreateVo.setPolicyType(projectCreateVo.getPolicyType());
        Project project = projectCreationPrehandle.handle(userId, projectCreateVo);
        if (project != null) {
            return assembleProductVo(project);
        }
        // create project
        project = createProjectNotOrderReport(userId, projectCreateVo);
        // order report
        // ProjectCart projectCart = orderReport(userId, project, projectCreateVo.getReports());
        return assembleProductVo(project);
    }

    /**
     * Make sure the division's existence
     */
    private void ensureDivisionExistence(OpenProjectCreateVo projectCreateVo, long userId) {
        Company company = companyService.getByUserId(userId);
        var customerId = String.valueOf(company.getCompanyId());
        var divisions = Iterables.toList(divisionManager.findByKey(customerId, List.of(projectCreateVo.getDivision())));
        if (CollectionUtils.isEmpty(divisions)) {
            var divisionName = StringUtils.defaultIfEmpty(projectCreateVo.getDivisionName(), projectCreateVo.getDivision());
            var divisionId = divisionManager.create(customerId, projectCreateVo.getDivision(), divisionName);
            projectCreateVo.setDivision(divisionId);
        } else {
            projectCreateVo.setDivision(divisions.get(0).getId());
        }
    }

    private Project createProjectNotOrderReport(long userId, OpenProjectCreateVo projectCreateVo)
        throws ServiceException {
        var insuredBy = getInsuredBy(projectCreateVo.getInsuredBy());
        var processedBy = getProcessedByFromUserCompany(userId);
        ProjectDto projectDto = ProjectConverter.toProjectDto(projectCreateVo, insuredBy, processedBy);
        Integer claimType = ProjectRuleUtil.checkClaimTypeAndReturnDefault(projectDto.getServiceType(), projectDto.getClaimType());
        projectDto.setClaimType(claimType);
        projectDto.setCreationChannel(CreationChannelType.OPENAPI.name());
        Project project = projectService.createProject(userId, projectDto);
        return project;
    }

    private Long getInsuredBy(String insuredBy) throws ServiceMessageException {
        String insuredKey = StringUtils.trimToEmpty(insuredBy);
        Long insuranceCompany = null;
        if (StringUtils.isNotBlank(insuredKey)) {
            insuranceCompany =
                Optional.ofNullable(companyService.getIdByKey(insuredKey))
                    .orElseThrow(
                        () ->
                            new ServiceMessageException(
                                MessageCode.PARAM_INVALID,
                                "Company %s not found.".formatted(insuredKey)));
        }
        return insuranceCompany;
    }

    /**
     * 根据创建者的公司获取该公司配置的默认processed by
     * TODO 临时方案，应该用更通用的逻辑替代
     */
    private Long getProcessedByFromUserCompany(long userId) {
        var userCompany = companyService.getByUserId(userId);

        if (userCompany == null) {
            return null;
        }

        var companyDataset = companyConfig.findConfig(userCompany.getCompanyId());
        if (companyDataset == null || companyDataset.getContract() == null) {
            return null;
        }

        return Optional.ofNullable(companyDataset.getContract().getProcessedBy())
            .map(Long::parseLong)
            .orElse(null);
    }

    private OpenProjectVo assembleProductVo(Project project) throws ServiceException {

        if (project == null) {
            return null;
        }

        String insuredByCompanyName = "";
        if (Objects.nonNull(project.getInsuranceCompany()))
            insuredByCompanyName =
                    Optional.ofNullable(companyService.getById(project.getInsuranceCompany()))
                            .map(Company::getCompanyName)
                            .orElse("");

        var projectII = projectIIProvider.findById(String.valueOf(project.getProjectId()));
        if (projectII != null){
            var policy = projectII.getPolicy();
            project.setIsRenewal(policy.isRenewal());
            project.setSupplementalServices(
                Optional.ofNullable(projectII.getSupplementalService()).map(Iterables::toList).orElse(List.of()));
            var coverage = policy.getCoverage();
            if (coverage != null) {
                for (Coverage c : coverage) {
                    if (Message.CoverageType.DWELLING.name().equals(c.getType())) {
                        project.setDwellingCoverage(c.getAmount().doubleValue());
                    }
                    if (Message.CoverageType.OTHER_STRUCTURE.name().equals(c.getType())) {
                        project.setOtherStructureCoverage(c.getAmount().doubleValue());
                    }
                    if (Message.CoverageType.CONTENT.name().equals(c.getType())) {
                        project.setContentCoverage(c.getAmount().doubleValue());
                    }
                }
            }

            if (projectII.getProjectDivision() != null) {
                var divisionId = projectII.getProjectDivision().getId();
                var userCompany = companyService.getByUserId(project.getCreatedBy());
                var divisions = divisionManager.findByKey(String.valueOf(userCompany.getCompanyId()), List.of());
                var optionalDivision = Iterables.toStream(divisions)
                    .filter(d -> StringUtils.equals(d.getId(), divisionId))
                    .findFirst();
                if (optionalDivision.isPresent()) {
                    var division = optionalDivision.get();
                    project.setProjectDivision(division.getKey());
                    project.setProjectDivisionName(division.getName());
                }
            }
            Functions.acceptIfNotNull(project::setCarrierProvidedLivingArea, projectII.getCarrierProvidedLivingArea(), BigDecimal::doubleValue);
        }
        return ProjectConverter.toOpenProjectVo(project, insuredByCompanyName, projectII.getPolicy());
    }

    @Override
    public OpenProjectVo getProject(long userId, long projectId) throws ServiceException {
        Project project = projectService.getById(projectId);
        return assembleProductVo(project);
    }

    @Override
    public List<OpenProjectWithStatusVo> searchProject(long userId, OpenProjectSearchOption searchOption) throws ServiceException {
        var filter = ProjectSearchResultFilter.builder().withStatus(true).build();
        var projectSearchOption = searchOption.toProjectSearchOption();
        if (bees360FeatureSwitch.isEnableNewOpenapiSearch()) {
            var projectTinyVos = projectService.searchProjectsForOpenapi(userId, projectSearchOption, filter);
            return projectTinyVos.stream().map(ProjectConverter::toOpenProjectVo).collect(Collectors.toList());
        }

        final ProjectPageResultVo pageResultVo = projectService.pageTinyProjectSearchWithFilter(userId,
            projectSearchOption, filter);

        return pageResultVo.getProjects().stream().map(ProjectConverter::toOpenProjectVo).collect(Collectors.toList());
    }

    private List<ProjectReportFile> listVisitableReports(long projectId) throws ServiceException {

        List<ProjectReportFile> reportFiles = projectReportFileService.getAllReportFiles(projectId);

        return reportFiles.stream()
            .filter(r -> r.getGenerationStatus() == ReportGenerationStatusEnum.APPROVED.getCode())
            .collect(Collectors.toList());
    }

    @Override
    public OpenProjectStatusVo getProjectLatestStatus(long projectId) throws ServiceException {
        Project project = projectService.getById(projectId);
        NewProjectStatusEnum projectStatusEnum = NewProjectStatusEnum.getEnum(project.getProjectStatus());
        OpenProjectStatusVo projectStatusVo = new OpenProjectStatusVo();
        projectStatusVo.setId(projectId);
        projectStatusVo.setStatus(projectStatusEnum.getValue());
        return projectStatusVo;
    }

    @Override
    public List<OpenReportIdTypeVo> listProjectReport(long projectId)throws ServiceException {
        Project project = projectService.getById(projectId);
        ProjectServiceTypeEnum serviceType = ProjectServiceTypeEnum.getEnum(project.getServiceType());
        if(serviceType == null) {
            return new ArrayList<>();
        }
        List<ProjectReportFile> reportFiles = listVisitableReports(projectId);
        var reportTypes = Sets.newHashSet(Iterables.transform(serviceType.getReportTypes(), ReportTypeEnum::getCode));
        reportTypes.add(ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT.getCode());
        reportTypes.add(ReportTypeEnum.CUSTOM_HOME_REPORT.getCode());

        List<OpenReportIdTypeVo> result = reportFiles.stream()
            .filter(r -> reportTypes.contains(r.getReportType()))
            .map(r -> new OpenReportIdTypeVo(r.getReportId(), ReportTypeEnum.getEnum(r.getReportType()).getDisplay()))
            .collect(Collectors.toList());
        return result;
    }

    @Override
    public OpenProjectStatusVo updateProjectStatus(long userId, long projectId, NewProjectStatusEnum projectStatus,
                                                   String comment) throws ServiceException {

        final Project project = projectService.getById(projectId);
        if (project == null){
            throw new ResourceNotFoundException();
        }

        OpenProjectStatusVo statusVo = new OpenProjectStatusVo();
        statusVo.setId(projectId);
        statusVo.setStatus(projectStatus.getValue());

        if (project.getProjectStatus() == projectStatus.getCode()) {
            return statusVo;
        }
        // todo 分支太多
        if (projectStatus == NewProjectStatusEnum.CLIENT_RECEIVED && canChangeToClientReceived(project.getProjectStatus())){

            projectStatusService.changeOnClientReceived(userId, projectId, comment);

        }else if (projectStatus == NewProjectStatusEnum.PROJECT_CANCELED){
            if(bees360FeatureSwitch.isEnableRequestCancel()) {
                projectStatusService.requestCancel(userId, projectId, SystemTypeEnum.BEES360, comment);
            } else {
                projectStatusService.changeOnProjectCanceled(userId, projectId, SystemTypeEnum.BEES360, comment);
            }
        }else if (projectStatus == NewProjectStatusEnum.RECEIVE_ERROR){

            projectStatusService.changeOnReceiveError(userId, projectId, comment);
        }
        else{
            // 其他类型暂不处理
            throw new ServiceException(MessageCode.PARAM_INVALID);
        }

        return statusVo;
    }

    public boolean canChangeToClientReceived(int code){
        final NewProjectStatusEnum statusEnum = NewProjectStatusEnum.getEnum(code);
        return statusEnum == NewProjectStatusEnum.RETURNED_TO_CLIENT ||
            statusEnum == NewProjectStatusEnum.RECEIVE_ERROR;
    }
}
