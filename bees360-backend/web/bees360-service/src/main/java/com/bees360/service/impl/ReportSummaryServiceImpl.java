package com.bees360.service.impl;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.bees360.entity.ReportSummary;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.bees360.entity.openapi.reportsummary.SummaryComparison;
import com.bees360.entity.openapi.reportsummary.SummaryExterior;
import com.bees360.entity.openapi.reportsummary.SummaryImage;
import com.bees360.entity.openapi.reportsummary.SummaryRoof;
import com.bees360.mapper.ReportSummaryMapper;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.report.Report;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ReportSummaryService;
import com.bees360.service.event.report.ReportSummaryChangedEvent;
import com.bees360.service.openapi.converter.ReportSummaryConvert;
import com.bees360.util.Iterables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> Yang
 */
@Slf4j
@Service
public class ReportSummaryServiceImpl implements ReportSummaryService, ApplicationEventPublisherAware {

    @Autowired
    private ReportSummaryMapper reportSummaryMapper;

    @Autowired private ProjectReportManager projectReportManager;

    @Autowired private ProjectReportFileService projectReportFileService;

    private ApplicationEventPublisher eventPublisher;

    @Override
    public ReportSummary getOne(long projectId, int reportType) {
        var reports =
            projectReportManager.find(
                String.valueOf(projectId), String.valueOf(reportType), null);
        var reportList = Iterables.toList(reports);
        if (CollectionUtils.isEmpty(reportList)
            || StringUtils.equals(reportList.get(0).getSummary().getSummary(), NULL_SUMMARY)) {
            return null;
        }
        return convertSummaryToWEBSummary(projectId, reportType, reportList.get(0));
    }

    private ReportSummary convertSummaryToWEBSummary(
            long projectId, int reportType, Report report) {
        if (StringUtils.equals(NULL_SUMMARY, report.getSummary().getSummary())) {
            return null;
        }
        var summary = new ReportSummary();
        summary.setProjectId(projectId);
        summary.setReportType(reportType);
        summary.setDeleted(false);
        summary.setSummary(report.getSummary().getSummary());
        return summary;
    }

    @Override
    public void upsert(ReportSummary summary) {
        var projectId = summary.getProjectId();
        var reportType = summary.getReportType();
        ReportSummary oldSummary = reportSummaryMapper.getOne(projectId, reportType);
        if (oldSummary != null) {
            summary.setId(oldSummary.getId());
            reportSummaryMapper.update(summary);
            return;
        }
        try {
            reportSummaryMapper.insert(summary);
        } catch (DuplicateKeyException e) {
            ReportSummary one = reportSummaryMapper.getOne(projectId, reportType);
            summary.setId(one.getId());
            reportSummaryMapper.update(summary);
        }
        publishEvent(summary);
    }

    @Override
    public Map<ReportTypeEnum, Set<String>> listImagesInSummary(long projectId) {
        var reports = projectReportFileService.getAllReportFiles(projectId);

        Map<ReportTypeEnum, Set<String>> imageIdsInReport = new HashMap<>();
        for (var report: reports) {
            var summary = getOne(projectId, report.getReportType());
            if (summary == null || StringUtils.isBlank(summary.getSummary())) {
                continue;
            }
            ReportSummaryVo summaryVo = ReportSummaryConvert.toSummaryVo(summary.getSummary());
            if (summaryVo == null) {
                continue;
            }
            Set<String> imageIds = getImageInSummary(summaryVo);

            ReportTypeEnum reportType = ReportTypeEnum.getEnum(summary.getReportType());
            imageIdsInReport.put(reportType, imageIds);
        }
        return imageIdsInReport;
    }

    private Set<String> getImageInSummary(ReportSummaryVo summaryVo) {
        if (summaryVo == null) {
            return Set.of();
        }
        Set<String> imageIds = new HashSet<>();
        // images in recommendation
        Optional.ofNullable(summaryVo.getRecommendations()).stream()
            .filter(Objects::nonNull)
            .flatMap(List::stream)
            .forEach(r -> addImageIds(r.getImage(), imageIds));
        // images in history
        Optional.ofNullable(summaryVo.getHistory()).stream()
            .filter(Objects::nonNull)
            .flatMap(List::stream)
            .forEach(h -> addImageIds(h.getImage(), imageIds));
        // image in comparison
        final var comparisonListList = new ArrayList<List<SummaryComparison>>();
        Optional.ofNullable(summaryVo.getRoof())
            .map(SummaryRoof::getComparison)
            .ifPresent(comparisonListList::add);
        Optional.ofNullable(summaryVo.getExterior())
            .map(SummaryExterior::getComparison)
            .ifPresent(comparisonListList::add);

        comparisonListList.stream()
            .filter(Objects::nonNull)
            .flatMap(List::stream)
            .forEach(c -> addImageIds(c.getImage(), imageIds));
        return imageIds;
    }

    private void addImageIds(List<SummaryImage> images, Set<String> imageIds) {
        if (images == null) {
            return;
        }
        images.stream()
            .filter(Objects::nonNull)
            .filter(img -> img != null && StringUtils.isNotBlank(img.getId()))
            .map(SummaryImage::getId)
            .forEach(imageIds::add);
    }

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.eventPublisher = applicationEventPublisher;
    }

    private void publishEvent(ReportSummary summary){
        eventPublisher.publishEvent(new ReportSummaryChangedEvent(this, summary));
    }
}
