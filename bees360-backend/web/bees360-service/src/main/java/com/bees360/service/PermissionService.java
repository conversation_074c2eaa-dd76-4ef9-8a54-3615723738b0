package com.bees360.service;

import java.util.List;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Permission;

public interface PermissionService {

	/**
	 * get all permissions
	 * @return
	 * @throws ServiceException
	 */
	public List<Permission> getAllPermissions() throws ServiceException;

	/**
	 *
	 * @param permissionIds
	 * @return
	 */
	public List<Permission> getPermissionsByPermissionIds(List<Long> permissionIds) throws ServiceException;
}
