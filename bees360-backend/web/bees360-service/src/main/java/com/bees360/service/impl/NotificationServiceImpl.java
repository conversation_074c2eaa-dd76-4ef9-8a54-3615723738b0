package com.bees360.service.impl;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.AccessDatabaseException;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Notification;
import com.bees360.entity.Project;
import com.bees360.entity.dto.WorkflowMsg;
import com.bees360.entity.dto.WorkflowMsg.EStep;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.PageResult;
import com.bees360.entity.vo.Pagination;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.mapper.NotificationMapper;
import com.bees360.service.NotificationService;
import com.bees360.util.websocket.WebSocketHandler;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service("notificationService")
public class NotificationServiceImpl implements NotificationService{
	private Logger logger = LoggerFactory.getLogger(NotificationServiceImpl.class);

	@Inject
	private WebSocketHandler webSocketHandler;

	@Inject
	NotificationMapper notificationMapper;

	@Override
	public PageResult<Notification> listNotifications(long userId, int pageIndex, int pageSize)throws ServiceException{

		PageResult<Notification> page = new PageResult<Notification>();
		if(pageIndex <= 0 || pageSize <= 0) {
			return page.empty();
		}
		List<Notification> notifications = null;
		Pagination pagination = new Pagination(pageIndex, pageSize);
		int startIndex = Pagination.calculateIndexStart(pageIndex, pageSize);
		try {
			notifications = notificationMapper.listPage(userId, startIndex, pageSize);
			int sum = notificationMapper.count(userId);
			page.setItems(notifications);
			pagination.setSum(sum);
			pagination.genarateTotalPage();
		} catch (Exception e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
		page.setItems(notifications);
		page.setPage(pagination);
		return page;
	}

	@Override
	public void setAllNotificationRead(long userId) throws ServiceException {
		try {
			notificationMapper.deleteAllByUserId(userId);
		} catch (Exception e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

	@Override
	public void setOneNotificationRead(long userId, long notificationId) throws ServiceException {
		try {
			notificationMapper.deleteOne(userId, notificationId);
		} catch (Exception e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

	/* -------------[START] push system notification ------------- */
    @Override
    public void notifyImageUploaded(long projectId, long userId) throws ServiceException {
        String content = "The images has been uploaded, Reports will be ready within 24-48 hours.";
        sendSystemNotification(projectId, userId, content, ProjectStatusEnum.IMAGE_UPLOADED);
    }

	@Override
	public void notifyReportApproved(long projectId, long userId, ReportTypeEnum reportType) throws ServiceException {
		if(reportType.APPROVED == null) {
			return;
		}
		String content = "The " + reportType.getDisplay() + " has been approved";
		// TODO@DoneSpeak use image report generated as the status of report generated
		sendSystemNotification(projectId, userId, content, reportType.APPROVED);
	}
	/* -------------[END] push system notification ------------- */

	/* ------------ [START] workflow control ---------------- */
	@Override
	public void allowRanging(long projectId, long userId) throws ServiceException {
		String description = "PRE-RANGING has been finished, please allow user to continue RANGING.";
		// TODO@DoneSpeak should send the the user who has the ability to do ai process and is online.
		sendWorkflowNotification(projectId, userId, description, EStep.RANGING);
	}

	@Override
	public void allowScoping(long projectId, long userId) throws ServiceException {
		String description = "PRE-SCOPINT has been finished, please allow user to continue SCOPING.";
		// TODO@DoneSpeak should send the the user who has the ability to do ai process and is online.
		sendWorkflowNotification(projectId, userId, description, EStep.SCOPING);
	}

	@Override
	public void allowPlane(long projectId, long userId) throws ServiceException {
		String description = "PRE-FACET has been finished, please allow user to continue FACET.";
		// TODO@DoneSpeak should send the the user who has the ability to do ai process and is online.
		sendWorkflowNotification(projectId, userId, description, EStep.PLANE);
	}

	@Override
	public void allowBoundary(long projectId, long userId) throws ServiceException {
		String description = "PRE-BOUNDARY has been finished, please allow user to continue BOUNDARY.";
		// TODO@DoneSpeak should send the the user who has the ability to do ai process and is online.
		sendWorkflowNotification(projectId, userId, description, EStep.BOUNDARY);
	}

	@Override
	public void allowDamage(long projectId, long userId) throws ServiceException {
		String description = "POST-BOUNDARY has been finished, please allow user to continue DAMAGE.";
		// TODO@DoneSpeak should send the the user who has the ability to do ai process and is online.
		sendWorkflowNotification(projectId, userId, description, EStep.DAMAGE);
	}
	/* ------------ [END] workflow control ---------------- */

	private void sendSystemNotification(long projectId, long userId, String content, ProjectStatusEnum status) throws ServiceException {
		Notification notification;
		try {
			notification = generateSystemNotify(projectId, userId, content, status);
		} catch (AccessDatabaseException e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
		webSocketHandler.sendMessageToUser(userId, notification);
	}

	/**
	 *
	 * @param projectId
	 * @param userId
	 * @param description
	 * @param status shouldn't be null
	 * @throws ServiceException
	 */
	private void sendWorkflowNotification(long projectId, long userId, String description, EStep status) throws ServiceException {

		WorkflowMsg workflowMsg = new WorkflowMsg(projectId, userId, description, status);

		webSocketHandler.sendMessageToUser(userId, workflowMsg);
	}

	private Notification generateSystemNotify(long projectId, long userId, String content, ProjectStatusEnum status)
			throws AccessDatabaseException {

		Notification notification = generateNotification(projectId, userId, content, status);

		try {
			notificationMapper.insertNotification(notification);
		} catch (Exception e) {
			String msg = "Fail to insert notification for project " + projectId + " of user " + userId;
			logger.error(msg, e);
			throw new AccessDatabaseException(msg, e);
		}

		return notification;
	}

	private Notification generateNotification(long projectId, long userId, String content,
											  ProjectStatusEnum status) {
		long now = new Date().getTime();
		Notification notification = new Notification();
		notification.setRecipient(userId);
		notification.setProjectId(projectId);
		if(status != null) {
			notification.setProjectStatus(status.getCode());
		}
		notification.setContent(content);
		notification.setCreatedTime(now);
		return notification;
	}

	@Override
	public void notifyRoleApplicationResult(long userId, int roleId, boolean isPassed, String comment) throws ServiceException {
		String content = null;
		if(isPassed == true){
			content = "Your application for the new role " + RoleEnum.getEnum(roleId).getDisplay() + " is approved!";
		}else{
			content = "Your application for the new role " + RoleEnum.getEnum(roleId).getDisplay()
					+ " is disapproved! The reason is: " + comment;
		}
		sendSystemNotification(userId, content);

	}

	@Override
	public void notifyReportSubmitted(long projectId,
									  ReportTypeEnum reportType, UserTinyVo reviewer) throws ServiceException {
		if(reportType.SUBMITTED == null) {
			return;
		}
		String content = "The " + reportType.getDisplay() + " has been submitted.";
		sendSystemNotification(projectId, reviewer.getUserId(), content, reportType.SUBMITTED);
	}

	@Override
	public void notifyReportDisapproved(long projectId,
										ReportTypeEnum reportType, UserTinyVo reviewer) throws ServiceException {
		if(reportType.REJECTED == null) {
			return;
		}

		String content = "The " + reportType.getDisplay() + " has been Disapproved.";
		sendSystemNotification(projectId, reviewer.getUserId(), content, reportType.REJECTED);
	}


	@Override
	public void notifyAdminRealtimeReportGenerated(long projectId, List<UserTinyVo> admins) throws ServiceException {
		ReportTypeEnum realtimeReport = ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT;

		String content = "The " + realtimeReport.getDisplay() + " has been Generated. "
				+ "Please assign the processor and reviewer for the project in time on the Bees360 platform.";
		for(UserTinyVo admin: admins) {
			sendSystemNotification(projectId, admin.getUserId(), content, realtimeReport.REJECTED);
		}
	}

	@Override
	public void notifyProcessorRealtimeReportGenerated(long projectId, UserTinyVo processor) throws ServiceException {
		ReportTypeEnum realtimeReport = ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT;

		String content = "The " + realtimeReport.getDisplay() + " has been Generated. "
				+ "Please check the report and submit it in time on the Bees360 platform.";
		sendSystemNotification(projectId, processor.getUserId(), content, realtimeReport.REJECTED);
	}

	@Override
	public void notifyMemberArranged(long projectId, UserTinyVo recipient, RoleEnum role)
			throws ServiceException {
		StringBuilder content = new StringBuilder();

		content.append("You are assigned to be the " + role.getDisplay() +
				" of the project " + projectId + ".");
		sendSystemNotification(projectId, recipient.getUserId(), content.toString(), null);
	}

	@Override
	public void notifyMemberArrangementCancel(Project project, UserTinyVo worker, RoleEnum role)
			throws ServiceException {
		StringBuilder content = new StringBuilder()
				.append("Your assignment at " + project.getFullAddress() + " has been canceled.");
		sendSystemNotification(project.getProjectId(), worker.getUserId(), content.toString(), null);
	}

	@Override
	public void notifyProjectDeleted(Project project, UserTinyVo operator, UserTinyVo recipient)
			throws ServiceException {
		String content = "The project(" + project.getProjectId() + ") at "
				+ project.getFullAddress() + " has been deleted by " + operator.getName() + ".";
		sendSystemNotification(recipient.getUserId(), content);
	}

	private String formatInspectionTime(Date date) {

		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		String timeStr = calendar.get(Calendar.HOUR) + ":" + calendar.get(Calendar.MINUTE);
		String amPm = calendar.get(Calendar.HOUR) == calendar.get(Calendar.HOUR_OF_DAY)? "am": "pm";
		String yearDate = calendar.get(Calendar.DATE) + "/" + calendar.get(Calendar.MONTH)
				+ "/" + calendar.get(Calendar.YEAR);
		return timeStr + " " + amPm + " on " + yearDate;
	}

	private void sendSystemNotification(long userId, String content) throws ServiceException {
		Notification notification;
		try {
			notification = generateSystemNotify(userId, content);
		} catch (AccessDatabaseException e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
		webSocketHandler.sendMessageToUser(userId, notification);
	}
	private Notification generateSystemNotify(long userId, String content)
			throws AccessDatabaseException {

		Notification notification = generateNotification(userId, content);
		try {
			notificationMapper.insertRoleApplicationNotification(notification);
		} catch (Exception e) {
			String msg = "Fail to insert notification of user " + userId;
			logger.error(msg, e);
			throw new AccessDatabaseException(msg, e);
		}
		return notification;
	}
	private Notification generateNotification(long userId, String content) {
		long now = new Date().getTime();
		Notification notification = new Notification();
		notification.setRecipient(userId);
		notification.setContent(content);
		notification.setCreatedTime(now);
		return notification;
	}

	@Override
	public void notifyRoleLosed(long userId, List<RoleEnum> roles) throws ServiceException {
		List<String> roleNames = new ArrayList<String>();
		for(RoleEnum role: roles) {
			roleNames.add(role.getDisplay().toUpperCase());
		}
		String content = "You have lost role(s) " + String.join(",", roleNames);
		sendSystemNotification(userId, content);
	}

	@Override
	public void infoMemberArranged(UserTinyVo user, RoleEnum role, List<Project> projects) throws ServiceException {
		String content = "You are assigned to be a " + role.getDisplay() + " for " + projects.size() + " projects.";
		sendSystemNotification(user.getUserId(), content);
	}

	@Override
	public void infoMemberInvited(UserTinyVo user, List<Project> projects) throws ServiceException {
		String content = "You are invited into " + projects.size() + " projects as a Visitor.";
		sendSystemNotification(user.getUserId(), content);
	}
}
