package com.bees360.service.listener.tag;

import com.bees360.entity.enums.AiBotUserEnum;
import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.service.ProjectLabelService;
import com.bees360.web.event.tag.TagMagicplanMissingAddedEvent;
import com.bees360.web.event.tag.TagMagicplanMissingRemovedEvent;
import com.bees360.web.event.tag.TagMissingInteriorImagesEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class OperationTagListener {

    @Autowired private ProjectLabelService projectLabelServiceImpl;

    @EventListener
    public void tagMissingInteriorImagesEvent(TagMissingInteriorImagesEvent event) {
        log.info(
                "Automatic tag missing interior image projectId {}, {}",
                event.getProjectId(),
                event.isExists());
        Optional<BoundProjectLabel> projectLabel =
                projectLabelServiceImpl.projectLabel(event.getProjectId());
        if (event.isExists()) {
            handleRemoveMissingInteriorTag(event, projectLabel);
        } else {
            handleAddMissingInteriorTag(event, projectLabel);
        }
    }

    @EventListener
    public void tagMagicplanMissingEventAdded(TagMagicplanMissingAddedEvent event) {
        log.info("Automatic tag magicplan missing projectId {}", event.getProjectId());
        Optional<BoundProjectLabel> projectLabel =
                projectLabelServiceImpl.projectLabel(event.getProjectId());
        handleAddMagicplanMissingTag(event.getProjectId(), projectLabel);
    }

    @EventListener
    public void tagMagicplanMissingEventRemoved(TagMagicplanMissingRemovedEvent event) {
        log.info("Automatic removed tag magicplan missing projectId {}", event.getProjectId());
        Optional<BoundProjectLabel> projectLabel =
                projectLabelServiceImpl.projectLabel(event.getProjectId());
        handleRemoveMagicplanMissingTag(event.getProjectId(), projectLabel);
    }

    private void handleAddMissingInteriorTag(
            TagMissingInteriorImagesEvent event, Optional<BoundProjectLabel> projectLabel) {
        if (projectLabel.isPresent()) {
            return;
        }

        projectLabelServiceImpl.markAfterEraseLabel(
                event.getProjectId(),
                List.of(ProjectLabelEnum.MISSING_INTERIOR_IMAGES.getLabelId()),
                AiBotUserEnum.AI_NEW_USER_ID.getIntegerCode(),
                SystemTypeEnum.BEES360);
        log.info("Automatic tag missing interior image succeed projectId {}", event.getProjectId());
    }

    private void handleRemoveMissingInteriorTag(
            TagMissingInteriorImagesEvent event, Optional<BoundProjectLabel> projectLabel) {
        if (projectLabel.isEmpty()) {
            return;
        }
        BoundProjectLabel label = projectLabel.get();
        if (CollectionUtils.isEmpty(label.getProjectLabels())) {
            return;
        }
        boolean hasMissingTag =
                label.getProjectLabels().stream()
                        .noneMatch(
                                tag ->
                                        ProjectLabelEnum.MISSING_INTERIOR_IMAGES
                                                .getLabelId()
                                                .equals(tag.getLabelId()));
        if (hasMissingTag) {
            return;
        }

        projectLabelServiceImpl.markAfterEraseLabel(
                event.getProjectId(),
                List.of(),
                AiBotUserEnum.AI_NEW_USER_ID.getIntegerCode(),
                SystemTypeEnum.BEES360);
        log.info(
                "Automatic removed tag missing interior image succeed projectId {}",
                event.getProjectId());
    }

    private void handleRemoveMagicplanMissingTag(
            long projectId, Optional<BoundProjectLabel> projectLabel) {
        if (projectLabel.isEmpty()) {
            return;
        }
        BoundProjectLabel label = projectLabel.get();
        if (CollectionUtils.isEmpty(label.getProjectLabels())) {
            return;
        }
        boolean hasNoMissingTag =
                label.getProjectLabels().stream()
                        .noneMatch(
                                tag ->
                                        ProjectLabelEnum.MAGICPLAN_MISSING
                                                .getLabelId()
                                                .equals(tag.getLabelId()));
        if (hasNoMissingTag) {
            return;
        }

        projectLabelServiceImpl.markAfterEraseLabel(
                projectId,
                List.of(),
                AiBotUserEnum.AI_NEW_USER_ID.getIntegerCode(),
                SystemTypeEnum.BEES360);
        log.info("Automatic removed tag Magicplan Missing succeed projectId {}", projectId);
    }

    private void handleAddMagicplanMissingTag(
            long projectId, Optional<BoundProjectLabel> projectLabel) {
        if (projectLabel.isPresent()) {
            return;
        }

        projectLabelServiceImpl.markAfterEraseLabel(
                projectId,
                List.of(ProjectLabelEnum.MAGICPLAN_MISSING.getLabelId()),
                AiBotUserEnum.AI_NEW_USER_ID.getIntegerCode(),
                SystemTypeEnum.BEES360);
        log.info("Automatic tag magicplan missing succeed projectId {}", projectId);
    }
}
