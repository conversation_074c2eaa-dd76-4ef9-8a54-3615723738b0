package com.bees360.service.listener.project;

import com.bees360.entity.Project;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.transfer.ResourceTransferService;
import com.bees360.web.event.project.ProjectStatusReturnedToClientEvent;
import java.util.function.Predicate;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.event.EventListener;

@Log4j2
public class TransferReportOnProjectReturnToClient {

    private final static String KEY_PREFIX = "Bees360";

    private final ResourceTransferService resourceTransferService;
    /**
     * test project id if it can be transferred.
     */
    private final Predicate<Long> transferablePredicate;

    public TransferReportOnProjectReturnToClient(
        ResourceTransferService resourceTransferService,
        Predicate<Long> transferablePredicate) {
        this.resourceTransferService = resourceTransferService;
        this.transferablePredicate = transferablePredicate;
    }

    @EventListener
    public void transfer(ProjectStatusReturnedToClientEvent event) {
        var project = event.getProject();
        log.info("Received Event({})", event);
        if (!transferablePredicate.test(project.getProjectId())) {
            log.info("Ignore to transfer project {}", project.getProjectId());
            return;
        }
        transferReport(project);
        transferSummary(project);
    }

    private void transferReport(Project project) {
        final var key = "%s_%s_%s.pdf".formatted(KEY_PREFIX, project.getPolicyNumber(), project.getProjectId());
        log.info("Try to transfer report with key {} with resourceTransferService({}).", key, resourceTransferService);
        resourceTransferService.transfer(key, ResourceMetadata.empty());
    }

    private void transferSummary(Project project) {
        final var key = "%s_%s_%s.json".formatted(KEY_PREFIX, project.getPolicyNumber(), project.getProjectId());
        log.info("Try to transfer summary with key {} with resourceTransferService({}).", key, resourceTransferService);
        resourceTransferService.transfer(key, ResourceMetadata.empty());
    }
}
