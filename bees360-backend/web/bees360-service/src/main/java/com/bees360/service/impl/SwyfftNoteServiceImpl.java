package com.bees360.service.impl;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Company;
import com.bees360.entity.ProjectMessage;
import com.bees360.entity.User;
import com.bees360.entity.enums.BsExportDataRelatedTypeEnum;
import com.bees360.entity.enums.ProjectMessageTypeEnum;
import com.bees360.service.CompanyService;
import com.bees360.service.ProjectMessageService;
import com.bees360.service.SwyfftNoteService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/01/15 16:31
 */
@Service
public class SwyfftNoteServiceImpl implements SwyfftNoteService {

    @Autowired
    private ProjectMessageService projectMessageService;

    @Autowired
    private CompanyService companyService;

    @Override
    public void createMessageOnSwyfftBindProject(long projectId, long insuranceCompany, String note) throws ServiceException {
        Company company = companyService.getById(insuranceCompany);
        if (StringUtils.isBlank(note) || Objects.isNull(company)
            || !StringUtils.equals(company.getCompanyName(), BsExportDataRelatedTypeEnum.SWYFFT_PROJECT_BIND.getDisplay())) {
            return;
        }
        ProjectMessage message = ProjectMessage.builder().createTime(System.currentTimeMillis())
            .content(note)
            .projectId(projectId)
            .senderId(User.AI_ID)
            .title(ProjectMessageTypeEnum.ADMIN_NOTE.getDisplay())
            .type(ProjectMessageTypeEnum.SWYFFT_PROJECT_BIND_NOTE.getCode())
            .build();
        projectMessageService.addMessageAndSyncToAi(message);
    }
}
