package com.bees360.service;

import java.util.List;

import jakarta.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson.JSONObject;
import com.bees360.entity.Permission;
import com.bees360.entity.SystemValue;
import com.bees360.service.PermissionService;
import com.bees360.service.payment.SystemValueService;
import com.bees360.util.CacheKeys;
import com.bees360.util.RedisUtil;

@Component
public class CacheInitializer{

	private static final Logger logger = LoggerFactory.getLogger(CacheInitializer.class);

	@Autowired
	RedisUtil redisUtil;

	@Autowired
	PermissionService permissionService;

	@Autowired
	SystemValueService systemValueService;


	@PostConstruct
	public void init() {
		logger.info("CacheInitializer init start ...");
		// load user:permissions from database;
		loadPermissions();

		// load system values
		loadSystemValues();
		logger.info("CacheInitializer init end ...");
	}


	public void loadPermissions() {
		logger.info("CacheInitializer load Permissions start ...");
		try {
			List<Permission> permissions = permissionService.getAllPermissions();
			redisUtil.set(CacheKeys.REDIS_KEY_USER_PERMISSIONS, JSONObject.toJSONString(permissions));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		logger.info("CacheInitializer load Permissions end ...");
	}

	public void loadSystemValues() {
		logger.info("CacheInitializer load SystemValues start ...");
		try {
			List<SystemValue> systemValues = systemValueService.getAllSystemValues();
			//redisUtil.set(CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS,systemValues);
			for(SystemValue systemValue : systemValues) {
				redisUtil.set(CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS + "_" + systemValue.getServiceName(), JSONObject.toJSONString(systemValue));
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		logger.info("CacheInitializer load SystemValues end ...");
	}
}
