package com.bees360.service.email.impl;

import com.bees360.job.registry.IntervalProjectExportEmail;
import com.bees360.mail.MailMessage;
import com.bees360.mail.MailSender;
import com.bees360.service.email.ProjectDataEmailSender;
import com.bees360.service.export.ProjectDataExcelProvider;
import com.bees360.service.export.impl.ProjectDataExcelProviderImpl;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.DateTimes;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Log4j2
@Component
public class ProjectDataEmailSenderImpl implements ProjectDataEmailSender {

    private final MailSender mailSender;

    private final ProjectDataExcelProvider projectDataExcelProvider;

    private static final String TITLE_TEMPLATE_UNDERWRITING = "%s %s statistics: %s to %s";

    private final Bees360FeatureSwitch featureSwitch;

    public ProjectDataEmailSenderImpl(MailSender backendMailSender,
                                      ProjectDataExcelProvider projectDataExcelProvider,
                                      Bees360FeatureSwitch featureSwitch) {
        this.mailSender = backendMailSender;
        this.projectDataExcelProvider = projectDataExcelProvider;
        this.featureSwitch = featureSwitch;
    }

    @Override
    public void sendEmailOfProjectDataWithinInterval(Instant start, Instant end, IntervalProjectExportEmail.Interval interval,
                                                     List<String> recipients) {
        String underwritingReturnToClient = projectDataExcelProvider.underwritingReturnToClientBetween(start, end);
        String allProjects = projectDataExcelProvider.projectCreatedBetween(start, end);
        var attachments = List.of(underwritingReturnToClient, allProjects);

        if(featureSwitch.isEnableOpenCloseWrite()) {
            var openProjects = projectDataExcelProvider.allProjectOpen();
            attachments = new ArrayList<>(attachments);
            attachments.add(openProjects);
        }

        String subject = subject(start, end, "All project statistics", interval);
        ListenableFuture<Void> future = sendEmail(subject, attachments, recipients);

        logWhenSucceed(List.of(future));
    }

    @Override
    public void sendEmailOfOpenProjectData(String subject, List<String> recipients) {
        var openProjectExcel = projectDataExcelProvider.allProjectOpen();
        var future = sendEmail(subject, List.of(openProjectExcel), recipients);
        logWhenSucceed(List.of(future));
    }

    private ListenableFuture<Void> sendEmail(String subject, List<String> files, List<String> recipients) {
        log.info("Start to send email to recipients:{}, title:{}", recipients, subject);
        Map<String, String> attachments = files.stream().collect(Collectors.toMap(k -> k, v -> v));
        MailMessage mailMsg = MailMessage.of(recipients, subject, subject, attachments);
        return mailSender.send(mailMsg);
    }

    private static String subject(Instant start, Instant end, String exportAbout,
                                  IntervalProjectExportEmail.Interval interval) {

        String startDate = DateTimes.fromEpochMilli(start.toEpochMilli(), DateTimes.DEFAULT_US_ZONE_ID)
            .format(ProjectDataExcelProviderImpl.DATE);
        String endDate = DateTimes.fromEpochMilli(end.toEpochMilli(), DateTimes.DEFAULT_US_ZONE_ID)
            .format(ProjectDataExcelProviderImpl.DATE);
        return TITLE_TEMPLATE_UNDERWRITING.formatted(exportAbout, interval.getValue(), startDate, endDate);
    }

    private void logWhenSucceed(List<ListenableFuture<Void>> futures) {
        Futures.whenAllSucceed(futures).run(() -> {
            log.info("Finish sending email of project export data.");
        }, MoreExecutors.directExecutor());
    }
}
