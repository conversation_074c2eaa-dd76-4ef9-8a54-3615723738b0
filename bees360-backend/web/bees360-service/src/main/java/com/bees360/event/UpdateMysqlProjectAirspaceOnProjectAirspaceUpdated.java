package com.bees360.event;

import com.bees360.entity.ProjectAirspace;
import com.bees360.event.registry.ProjectAirspaceUpdatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectAirspaceMapper;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听ProjectAirspaceUpdatedEvent事件并更新MySQL中的ProjectAirspace数据
 */
@Log4j2
public class UpdateMysqlProjectAirspaceOnProjectAirspaceUpdated
        extends AbstractNamedEventListener<ProjectAirspaceUpdatedEvent> {
    private final ProjectAirspaceMapper projectAirspaceMapper;

    public UpdateMysqlProjectAirspaceOnProjectAirspaceUpdated(
            ProjectAirspaceMapper projectAirspaceMapper) {
        this.projectAirspaceMapper = projectAirspaceMapper;

        log.info("Created '{}(projectAirspaceMapper={})'", this, projectAirspaceMapper);
    }

    @Override
    public void handle(ProjectAirspaceUpdatedEvent event) throws IOException {
        ProjectAirspace projectAirspace = new ProjectAirspace();
        projectAirspace.setProjectId(event.getProjectId());
        projectAirspace.setStatus(event.getAirspaceTo().getStatus());
        projectAirspace.setHeightCeiling(event.getAirspaceTo().getHeightCeiling());
        projectAirspaceMapper.upsertByProjectId(projectAirspace);
    }
}
