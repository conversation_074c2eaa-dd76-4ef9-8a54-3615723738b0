package com.bees360.service.util;

import com.bees360.api.AbortedException;
import com.bees360.api.InternalException;
import com.bees360.api.UnavailableException;
import com.bees360.codec.GsonCodec;
import com.bees360.contract.ContractManager;
import com.bees360.customer.Customer;
import com.bees360.http.ApacheHttpClient;
import com.bees360.http.HttpClient;
import com.bees360.http.RetryHttpClient;
import com.bees360.http.config.ApacheHttpClientConfig;
import com.bees360.oauth.OAuthHttpClient;
import com.bees360.oauth.OAuthToken;
import com.bees360.project.ProjectIIRepository;
import com.bees360.util.Functions;
import com.google.gson.Gson;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.io.IOException;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.BinaryOperator;
import java.util.function.UnaryOperator;
import java.util.stream.Stream;

@Log4j2
@Configuration
@Import({ApacheHttpClientConfig.class})
@EnableConfigurationProperties
public class AWSLambdaSummaryConverterConfig {
    public static final GsonCodec gsonCodec = GsonCodec.INSTANCE;
    public static final Gson gson = new Gson();

    @Data
    @ConfigurationProperties(prefix = "http.aws-lambda")
    @Configuration
    public static class AWSLambdaProperties {
        private Map<String, String> customerTemplateKey = new HashMap<>();
        private String endpoint = "";
        private String clientSecret = "";
    }

    @Bean("awsLambdaHttpClient")
    public HttpClient awsLambdaHttpClient(
        ApacheHttpClient httpClient, AWSLambdaProperties properties) {
        var retryProperties =
            RetryHttpClient.RetryProperties.builder()
                .maxAttempts(5)
                .initialInterval(2000)
                .multiplier(2)
                .maxInterval(20000)
                .include(
                    List.of(
                        UnavailableException.class,
                        IOException.class,
                        IllegalStateException.class,
                        InternalException.class,
                        AbortedException.class))
                .build();
        var oauthToken = OAuthToken.of(properties.getClientSecret(), LocalDateTime.MAX, null);
        return new RetryHttpClient(new OAuthHttpClient(httpClient, oauthToken), retryProperties);
    }

    @Bean("awsLambdaRequestConverter")
    public BiFunction<String, String, HttpPost> awsLambdaRequestConverter(
        AWSLambdaProperties properties) {
        var uri = URI.create(properties.getEndpoint());
        return (summary, templateKey) -> {
            var request = new HttpPost(uri);
            summary = StringUtils.isBlank(summary) ? "{}" : summary;
            var formData = String.format("{\"projectSummary\":{},\"reportSummary\":%s}", summary);
            var map = new HashMap<String, Object>();
            map.put("formTemplateId", templateKey);
            map.put("formData", formData);
            var requestJson = gsonCodec.encode(map).toStringUtf8();
            request.setEntity(new StringEntity(requestJson, ContentType.APPLICATION_JSON));
            return request;
        };
    }

    @Bean("projectCompanyKeyProvider")
    public UnaryOperator<String> projectCompanyKeyProvider(
        ProjectIIRepository projectIIRepository, ContractManager contractManager) {
        return projectId -> {
            var project = projectIIRepository.get(projectId);
            var contract = contractManager.findById(project.getContract().getId());
            return Stream.of(contract.getInsuredBy(), contract.getProcessedBy())
                .filter(Objects::nonNull)
                .map(Customer::getCompanyKey)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
        };
    }

    /** 使用lambdaFunction根据customerKey对summary进行转换 */
    @Bean("lambdaReportSummaryConverter")
    @ConditionalOnProperty(prefix = "http.aws-lambda", value = "enabled", havingValue = "true")
    public BinaryOperator<String> lambdaReportSummaryConverter(
        @Qualifier("awsLambdaRequestConverter")
        BiFunction<String, String, HttpPost> awsLambdaRequestConverter,
        @Qualifier("projectCompanyKeyProvider") UnaryOperator<String> projectCompanyKeyProvider,
        HttpClient awsLambdaHttpClient,
        AWSLambdaProperties properties) {
        UnaryOperator<String> templateKeyProvider = properties.getCustomerTemplateKey()::get;

        BinaryOperator<String> converter =
            (summary, projectId) -> {
                // 未配置对应templateKey时summary不做处理
                var customerKey = projectCompanyKeyProvider.apply(projectId);
                var templateKey = templateKeyProvider.apply(customerKey);
                if (StringUtils.isBlank(templateKey)) {
                    return summary;
                }
                // 调用lambdaFunction
                var request = awsLambdaRequestConverter.apply(summary, templateKey);
                var response =
                    awsLambdaHttpClient.execute(
                        request,
                        Functions.combine(
                            rsp ->
                                HttpClient.throwApiExceptionNon2xxResponse(
                                    rsp,
                                    HttpClient::convertResponseToString),
                            HttpClient::convertResponseToString));
                var formDataRsp = gson.fromJson(response, FormDataResponse.class);
                return formDataRsp.getData();
            };
        log.info("Created lambda report summary resource converter {}.", converter);
        return converter;
    }

    /** 开关关闭时summary不做处理 */
    @Bean("lambdaReportSummaryConverter")
    @ConditionalOnProperty(
        prefix = "http.aws-lambda",
        value = "enabled",
        havingValue = "false",
        matchIfMissing = true)
    public BinaryOperator<String> originReportSummaryConverter() {
        return (summary, projectId) -> summary;
    }

    @Data
    static class FormDataResponse {
        private String data;
        private String message;
    }
}
