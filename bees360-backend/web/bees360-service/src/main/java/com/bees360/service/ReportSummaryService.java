package com.bees360.service;

import com.bees360.entity.ReportSummary;
import com.bees360.entity.enums.ReportTypeEnum;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface ReportSummaryService {

    String NULL_SUMMARY = "{}";

    ReportSummary getOne(long projectId, int reportType);

    void upsert(ReportSummary summary);

    /**
     * @return key为报告类型，value为imageId列表的Map
     */
    Map<ReportTypeEnum, Set<String>> listImagesInSummary(long projectId);
}
