package com.bees360.job;

import com.bees360.commons.lang.time.DateTimeUtil;
import com.bees360.event.EventDispatcher;
import com.bees360.event.registry.CronTriggerDailyAt1205AmCst;
import com.bees360.event.registry.CronTriggerDay1MonthlyAt1AmCst;
import com.bees360.event.registry.CronTriggerDay1MonthlyAt3AmCst;
import com.bees360.event.registry.CronTriggerMondayWeeklyAt1AmCst;
import com.bees360.job.registry.IntervalProjectExportEmail;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.OpenProjectExportEmail;
import com.bees360.job.util.EventTriggeredJob;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.DateTimes;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.CollectionUtils;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalTime;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Map;

@Log4j2
public class ScheduledEmailExportEmailJobOnCronTrigger {

    private static final int RETRY_COUNT = 5;

    private static final Duration WAIT_INTERVAL = Duration.ofMinutes(1);

    private static final float RETRY_INCREMENT_FACTOR = 1.5F;

    private final String SCHEDULED_PROJECT_EXPORT_TOPIC = "projectExportScheduled";
    private final String SCHEDULED_PROJECT_EXPORT_DAILY_TOPIC = "projectExportScheduledDaily";
    private final String SCHEDULED_PROJECT_EXPORT_WEEKLY_TOPIC = "projectExportScheduledWeekly";
    private final String SCHEDULED_PROJECT_EXPORT_MONTHLY_TOPIC = "projectExportScheduledMonthly";

    private final String OPEN_PROJECT_EXPORT_TOPIC = "openProjectExport";

    private final Map<String, @NotNull List<@Email @NotBlank String>> recipients;

    public ScheduledEmailExportEmailJobOnCronTrigger(JobScheduler jobScheduler,
                                                     EventDispatcher eventDispatcher,
                                                     Map<String, @NotNull List<@Email @NotBlank String>> recipients,
                                                     Bees360FeatureSwitch featureSwitch) {
        WeeklyProjectDataExporter weeklyProjectDataExporter = new WeeklyProjectDataExporter(jobScheduler);
        eventDispatcher.enlist(weeklyProjectDataExporter);
        MonthlyProjectDataExporter monthlyProjectDataExporter = new MonthlyProjectDataExporter(jobScheduler);
        eventDispatcher.enlist(monthlyProjectDataExporter);
        var dailyProjectDataExporter = new DailyProjectDataExporter(jobScheduler);
        eventDispatcher.enlist(dailyProjectDataExporter);
        this.recipients = recipients;

        // open/close 开关关闭时才发这封邮件
        if (!featureSwitch.isEnableOpenCloseWrite()) {
            MonthlyOpenProjectDataExporter monthlyOpenProjectDataExporter = new MonthlyOpenProjectDataExporter(jobScheduler);
            eventDispatcher.enlist(monthlyOpenProjectDataExporter);
        }
    }

    class MonthlyProjectDataExporter extends EventTriggeredJob<CronTriggerDay1MonthlyAt1AmCst> {

        public MonthlyProjectDataExporter(JobScheduler jobScheduler) {
            super(jobScheduler);
        }

        @Override
        protected Job convert(CronTriggerDay1MonthlyAt1AmCst cronTriggerDay1MonthlyAt1AmCst) {
            Instant triggerTime = cronTriggerDay1MonthlyAt1AmCst.getTriggerTime();
            Instant firstDayOfLastMonth = triggerTime.atZone(DateTimes.DEFAULT_US_ZONE_ID)
                .with(temporal -> temporal.with(ChronoField.DAY_OF_MONTH, 1)
                    .minus(1, ChronoUnit.MONTHS))
                .with(LocalTime.MIN).toInstant();
            Instant lastDayOfLastMonth = firstDayOfLastMonth.atZone(DateTimes.DEFAULT_US_ZONE_ID)
                .with(temporal -> temporal.plus(1, ChronoUnit.MONTHS)
                    .minus(1, ChronoUnit.SECONDS))
                .toInstant();

            List<String> recipients = getRecipients(SCHEDULED_PROJECT_EXPORT_MONTHLY_TOPIC);
            if (CollectionUtils.isEmpty(recipients)) {
                recipients = getRecipients(SCHEDULED_PROJECT_EXPORT_TOPIC);
            }

            IntervalProjectExportEmail job = new IntervalProjectExportEmail(firstDayOfLastMonth, lastDayOfLastMonth,
                IntervalProjectExportEmail.Interval.MONTHLY, recipients);
            log.info("start to send email of monthly project export data from {} to {}",
                firstDayOfLastMonth, lastDayOfLastMonth);
            return RetryableJob.of(JobPayloads.encode(job), RETRY_COUNT, WAIT_INTERVAL, RETRY_INCREMENT_FACTOR);
        }

        @Override
        protected boolean filter(CronTriggerDay1MonthlyAt1AmCst event) {
            return filterEmptyRecipients(SCHEDULED_PROJECT_EXPORT_TOPIC) || filterEmptyRecipients(SCHEDULED_PROJECT_EXPORT_MONTHLY_TOPIC);
        }
    }

    class WeeklyProjectDataExporter extends EventTriggeredJob<CronTriggerMondayWeeklyAt1AmCst> {

        WeeklyProjectDataExporter(JobScheduler jobScheduler) {
            super(jobScheduler);
        }

        @Override
        protected Job convert(CronTriggerMondayWeeklyAt1AmCst cronTriggerMondayWeeklyAt1AmCst) {
            Instant triggerTime = cronTriggerMondayWeeklyAt1AmCst.getTriggerTime();
            Instant lastMonday = triggerTime.atZone(DateTimes.DEFAULT_US_ZONE_ID)
                .with(TemporalAdjusters.previous(DayOfWeek.MONDAY)).with(LocalTime.MIN).toInstant();
            Instant lastSunday = lastMonday.plus(Duration.ofDays(7)).minus(Duration.ofSeconds(1));

            List<String> recipients = getRecipients(SCHEDULED_PROJECT_EXPORT_WEEKLY_TOPIC);
            if (CollectionUtils.isEmpty(recipients)) {
                recipients = getRecipients(SCHEDULED_PROJECT_EXPORT_TOPIC);
            }

            IntervalProjectExportEmail job = new IntervalProjectExportEmail(lastMonday, lastSunday,
                IntervalProjectExportEmail.Interval.WEEKLY, recipients);
            log.info("start to send email of weekly project export data from {} to {}",
                lastMonday, lastSunday);
            return RetryableJob.of(JobPayloads.encode(job), RETRY_COUNT, WAIT_INTERVAL, RETRY_INCREMENT_FACTOR);
        }

        @Override
        protected boolean filter(CronTriggerMondayWeeklyAt1AmCst event) {
            return filterEmptyRecipients(SCHEDULED_PROJECT_EXPORT_TOPIC) || filterEmptyRecipients(SCHEDULED_PROJECT_EXPORT_WEEKLY_TOPIC);
        }
    }

    class DailyProjectDataExporter extends EventTriggeredJob<CronTriggerDailyAt1205AmCst> {

        public DailyProjectDataExporter(JobScheduler jobScheduler) {
            super(jobScheduler);
        }

        @Override
        protected Job convert(CronTriggerDailyAt1205AmCst event) {
            Instant dayStart = DateTimeUtil.getStartOfDay(-1, DateTimes.DEFAULT_US_ZONE_ID);
            Instant dayEnd = DateTimeUtil.getStartOfDay(0, DateTimes.DEFAULT_US_ZONE_ID).minusSeconds(1);

            List<String> recipients = getRecipients(SCHEDULED_PROJECT_EXPORT_DAILY_TOPIC);
            if (CollectionUtils.isEmpty(recipients)) {
                recipients = getRecipients(SCHEDULED_PROJECT_EXPORT_TOPIC);
            }

            IntervalProjectExportEmail job =
                    new IntervalProjectExportEmail(
                            dayStart,
                            dayEnd,
                            IntervalProjectExportEmail.Interval.DAILY,
                            recipients);
            log.info("start to send email of daily project export data from {} to {}",
                dayStart, dayEnd);
            return RetryableJob.of(JobPayloads.encode(job), RETRY_COUNT, WAIT_INTERVAL, RETRY_INCREMENT_FACTOR);
        }

        @Override
        protected boolean filter(CronTriggerDailyAt1205AmCst event) {
            return filterEmptyRecipients(SCHEDULED_PROJECT_EXPORT_TOPIC)
                    || filterEmptyRecipients(SCHEDULED_PROJECT_EXPORT_DAILY_TOPIC);
        }
    }

    class MonthlyOpenProjectDataExporter extends EventTriggeredJob<CronTriggerDay1MonthlyAt3AmCst> {

        MonthlyOpenProjectDataExporter(JobScheduler jobScheduler) {
            super(jobScheduler);
        }

        @Override
        protected Job convert(CronTriggerDay1MonthlyAt3AmCst event) {
            var job = new OpenProjectExportEmail();
            var recipients = getRecipients(OPEN_PROJECT_EXPORT_TOPIC);
            job.setRecipients(recipients);
            log.info("start to send email of monthly open project export data.");
            return RetryableJob.of(JobPayloads.encode(job), RETRY_COUNT, WAIT_INTERVAL, RETRY_INCREMENT_FACTOR);
        }

        @Override
        protected boolean filter(CronTriggerDay1MonthlyAt3AmCst event) {
            return filterEmptyRecipients(OPEN_PROJECT_EXPORT_TOPIC);
        }
    }

    private List<String> getRecipients(String topic) {
        return recipients.get(topic);
    }

    private boolean filterEmptyRecipients(String topic) {
        return !CollectionUtils.isEmpty(recipients.get(topic));
    }
}
