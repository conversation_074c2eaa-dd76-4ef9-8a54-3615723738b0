package com.bees360.event;

import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.event.registry.ProjectStatusChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.UserService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Collections;

/**
 * 监听项目状态变更事件，根据状态清除项目标签。
 */
@Log4j2
public class ClearProjectTagsOnProjectStatusChanged extends AbstractNamedEventListener<ProjectStatusChanged> {

    private final ProjectLabelService projectLabelService;

    private final UserService userService;

    public ClearProjectTagsOnProjectStatusChanged(ProjectLabelService projectLabelService, UserService userService) {
        this.projectLabelService = projectLabelService;
        this.userService = userService;
    }

    @Override
    public void handle(ProjectStatusChanged event) throws IOException {
        log.info("Listening event {} for clear project tags.", event);
        if(event.getStatus() != NewProjectStatusEnum.IMAGE_UPLOADED.getCode()
            && event.getStatus() != NewProjectStatusEnum.PROJECT_CANCELED.getCode()) {
            return;
        }

        var projectId = Long.parseLong(event.getProjectId());
        var userId = userService.toWebUserId(event.getUpdatedBy());
        if(userId == null) {
            var msg = String.format("Failed to update project %s status " +
                "due to user %s not exists in web.", projectId, event.getUpdatedBy());
            throw new IllegalStateException(msg);
        }

        projectLabelService.markAfterEraseLabel(projectId, Collections.emptyList(), userId,
            SystemTypeEnum.BEES360);
    }
}
