package com.bees360.service.export.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.bees360.entity.Project;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.service.ProjectImageService;
import com.bees360.service.ProjectService;
import com.bees360.service.export.ExportableDocument;
import com.bees360.service.export.PhotoExporter;
import com.google.api.client.util.Lists;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.dto.ProjectImageSearchOptionDto;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.vo.ProjectImageAnnotationVo;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.service.ProjectReportFileService;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
@RequiredArgsConstructor
public class PhotoExporterImpl implements PhotoExporter {

    private static final Comparator<? super ProjectImageAnnotationVo> IMAGE_ALIAS_CAPTION_COMPARATOR = newImageComparator();

    private final ProjectImageService projectImageService;
    private final ProjectImageMapper projectImageMapper;
    private final ProjectService projectService;
    private final ProjectReportFileService projectReportFileService;

    public static Comparator<? super ProjectImageAnnotationVo> newImageComparator() {
        return (o1, o2) -> {
            if (StringUtils.isBlank(o1.getAlias()) && StringUtils.isBlank(o2.getAlias())) {
                if (StringUtils.isBlank(o1.getCaption()) && StringUtils.isBlank(o2.getCaption())) {
                    return StringUtils.compareIgnoreCase(o1.getOriginalFileName(), o2.getOriginalFileName());
                }
                return StringUtils.compareIgnoreCase(o1.getCaption(), o2.getCaption());
            }
            String[] ones = o1.getAlias().split("-");
            String[] twos = o2.getAlias().split("-");
            for (int i = 0; i < ones.length && i < twos.length; i++) {
                int one = StringUtils.isNumeric(ones[i].trim()) ? Integer.parseInt(ones[i].trim()) : 99999;
                int two = StringUtils.isNumeric(twos[i].trim()) ? Integer.parseInt(twos[i].trim()) : 99999;
                if (Objects.equals(one, two)) {
                    continue;
                }
                return Integer.compare(one, two);
            }

            return StringUtils.compareIgnoreCase(o1.getAlias(), o2.getAlias());
        };
    }

    @Override
    public List<ExportableDocument> collectPhotos(long projectId) {
        return collectPhotos(projectId, null);
    }

    @Override
    public List<ExportableDocument> collectPhotos(long projectId, BiConsumer<ProjectImageAnnotationVo, ExportableDocument> docModifier) {
        Project project = getProject(projectId);
        ProjectServiceTypeEnum serviceTypeEnum = ProjectServiceTypeEnum.getEnum(project.getServiceType());
        if (serviceTypeEnum == null) {
            log.warn("Project({}): Empty list will be the result, since the service type is unknown.", projectId);
            return Lists.newArrayList();
        }
        Set<ReportTypeEnum> reportTypes = serviceTypeEnum.getReportTypes();

        List<ProjectImageAnnotationVo> imagesSelected = selectImagesUsingInReport(projectId, reportTypes);
        List<ProjectImageAnnotationVo> imagesNotInReport = selectImagesNotInReport(projectId, imagesSelected);

        imagesSelected.addAll(imagesNotInReport);

        List<ExportableDocument> resultDocs = convertImageToDoc(imagesSelected, this::convertImageToDoc);
        if (docModifier != null) {
            for (int i = 0; i < resultDocs.size(); i++) {
                docModifier.accept(imagesSelected.get(i), resultDocs.get(i));
            }
        }
        return distinctDocByFilename(resultDocs);
    }

    private Project getProject(long projectId) {
        return projectService.getById(projectId);
    }

    private List<ProjectImageAnnotationVo> selectImagesUsingInReport(long projectId, Set<ReportTypeEnum> reportTypes) {

        List<ProjectImageAnnotationVo> imagesSelected = new ArrayList<>();
        List<ProjectReportFile> reportFiles = projectReportFileService.getAllReportFiles(projectId);

        for (ProjectReportFile reportFile : reportFiles) {
            if (!reportTypes.contains(ReportTypeEnum.getEnum(reportFile.getReportType()))) {
                continue;
            }
            List<ProjectImageAnnotationVo> projectImages = listImagesByReportType(projectId, reportFile.getReportType())
                .stream().filter(image -> !StringUtils.isAnyBlank(image.getTagCategory(), image.getTagDescription()))
                .collect(Collectors.toList());

            imagesSelected.addAll(projectImages);
        }
        imagesSelected.sort(IMAGE_ALIAS_CAPTION_COMPARATOR);

        return imagesSelected;
    }

    /**
     * 添加没有参与生成report的images
     *
     * @param projectId
     *            project id
     * @param imagesInReport
     *            参与生成report的待上传的image
     */
    private List<ProjectImageAnnotationVo> selectImagesNotInReport(long projectId,
        List<ProjectImageAnnotationVo> imagesInReport) {
        List<ProjectImage> images = projectImageService.listAll(projectId);

        Set<String> imageIdsExclude = Stream.concat(imagesInReport.stream().map(ProjectImageAnnotationVo::getImageId),
            imagesInReport.stream().map(ProjectImageAnnotationVo::getParentId)).collect(Collectors.toSet());

        List<ProjectImageAnnotationVo> notInReportImageList = new ArrayList<>();
        for (ProjectImage image : images) {
            if (imageIdsExclude.contains(image.getImageId())) {
                continue;
            }
            if (!isDroneCloseupOrMobile(image) || !hasTagCategoryAndTagDescription(image)) {
                continue;
            }
            ProjectImageAnnotationVo imageAnnotationVo = new ProjectImageAnnotationVo();
            BeanUtils.copyProperties(image, imageAnnotationVo);
            imageAnnotationVo.setCaption(image.getTagDescription());
            imageAnnotationVo.setIsInReport(false);

            notInReportImageList.add(imageAnnotationVo);
        }
        notInReportImageList.sort(Comparator.comparingLong(ProjectImageAnnotationVo::getShootingTime));

        final Map<String, Integer> nameCounter = new HashMap<>();
        notInReportImageList.forEach(image -> {
            String alias = createNewFileName(image, nameCounter);
            image.setAlias(alias);
        });

        return notInReportImageList;
    }

    private String createNewFileName(ProjectImageAnnotationVo imageAnnotationVo, Map<String, Integer> fileSourceTypeCount) {

        FileSourceTypeEnum fileSourceType = FileSourceTypeEnum.getEnum(imageAnnotationVo.getFileSourceType());
        String fileType = fileSourceType.getDisplay();
        if (fileSourceType == FileSourceTypeEnum.DRONE_IMAGE) {
            fileType = "Roof";
        }
        ImageTypeEnum imageType = ImageTypeEnum.getEnum(imageAnnotationVo.getImageType());
        String nameCounterKey = fileSourceType + "_" + imageType;
        int count = fileSourceTypeCount.compute(nameCounterKey, (k, v) -> v == null? 1: v + 1);
        String extension = FilenameUtils.getExtension(imageAnnotationVo.getFileName());
        String imageTypeName = (imageType == null? "-": imageType.getDisplay());
        String alias = "%s %s %s.%s".formatted(fileType, imageTypeName, count, extension);
        return alias;
    }

    private boolean hasTagCategoryAndTagDescription(ProjectImage image) {
        return !StringUtils.isAnyBlank(image.getTagCategory(), image.getTagDescription());
    }

    private boolean isDroneCloseupOrMobile(ProjectImage image) {
        boolean isDroneCloseup = Objects.equals(FileSourceTypeEnum.DRONE_IMAGE.getCode(), image.getFileSourceType())
            && Objects.equals(ImageTypeEnum.CLOSEUP.getCode(), image.getImageType());

        return isDroneCloseup
            || Objects.equals(FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode(), image.getFileSourceType());
    }

    private List<ProjectImageAnnotationVo> listImagesByReportType(long projectId, int reportType) {
        ProjectImageSearchOptionDto queryParameter = ProjectImageSearchOptionDto.builder().projectId(projectId)
            .fileSourceType(FileSourceTypeEnum.REPORT_IMAGE.getCode()).reportType(reportType).needReportType(true)
            .deleted(false).build();
        return projectImageMapper.listImagesPage(queryParameter);
    }

    private List<ExportableDocument> convertImageToDoc(List<ProjectImageAnnotationVo> images,
            Function<ProjectImageAnnotationVo, ExportableDocument> convertImageToDoc) {
        return images.stream().map(convertImageToDoc).collect(Collectors.toList());
    }

    private ExportableDocument convertImageToDoc(ProjectImageAnnotationVo image) {
        String folder = generateFolder(image);
        // @formatter:off
        ExportableDocument doc = new ExportableDocument()
            .setId(image.getImageId())
            .setType(ExportableDocument.TYPE_PHOTO)
            .setOriginalName(image.getOriginalFileName())
            .setFileName(getFileName(image))
            .setDescription(image.getCaption())
            .setLocation(folder)
            .setSize(image.getFileSize())
            .setCreateTime(image.getUploadTime())
            .setResourceSrc(image.getImageS3Key());
        // @formatter:on
        return doc;
    }

    private String generateFolder(ProjectImageAnnotationVo image) {
        return image.getTagCategory();
    }

    private String getFileName(ProjectImageAnnotationVo image) {
        String key = image.getFileName();
        String result = StringUtils.substringAfterLast(key, "/");
        String filename = StringUtils.isEmpty(result) ? key : result;
        if (StringUtils.isNotBlank(image.getAlias())) {
            String extension = FilenameUtils.getExtension(filename);
            String dotExtension = "." + extension;
            filename = image.getAlias();
            if (StringUtils.isNotEmpty(extension) && !StringUtils.endsWith(filename, dotExtension)) {
                filename = filename + dotExtension;
            }
        }
        return formatFilename(filename);
    }

    private String formatFilename(String filename) {
        String charsForbidden = "[/\\\\?\\*:\"<>|]";
        String result = RegExUtils.replaceAll(filename, charsForbidden, "_");
        return result;
    }

    private List<ExportableDocument> distinctDocByFilename(List<ExportableDocument> images) {
        Set<String> set = new HashSet<>();
        return images.stream().filter(doc -> set.add(doc.getFileName())).collect(Collectors.toList());
    }
}
