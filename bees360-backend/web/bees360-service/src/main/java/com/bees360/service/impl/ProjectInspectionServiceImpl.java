package com.bees360.service.impl;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.CommentManager;
import com.bees360.activity.Message;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectInspection;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.mapper.ProjectInspectionMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.ProjectInspectionService;
import com.bees360.util.SequenceGenerator;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service(value = "projectInspectionService")
public class ProjectInspectionServiceImpl implements ProjectInspectionService {

    @Resource private ProjectInspectionMapper projectInspectionMapper;
    @Autowired private SequenceGenerator sequenceGenerator;
    @Autowired private CommentManager commentManager;
    @Autowired private ActivityManager activityManager;
    @Autowired private ProjectMapper projectMapper;

    @Value("${bees360.ibees.inspectionLinkTemplate}")
    private String inspectionLinkTemplate;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProjectInspection addProjectInspection(long creatorId, long projectId) {
        long now = System.currentTimeMillis();
        long expirationTime =
                now
                        + TimeUnit.MILLISECONDS.convert(
                                SequenceGenerator.RandomSequenceEnum.IBEES_INSPECTION_CODE
                                        .getExpirationTime(),
                                SequenceGenerator.RandomSequenceEnum.IBEES_INSPECTION_CODE
                                        .getExpirationTimeUnit());
        String inspectionCode =
                sequenceGenerator.generateSequence(
                        SequenceGenerator.RandomSequenceEnum.IBEES_INSPECTION_CODE);
        ProjectInspection projectInspection =
                ProjectInspection.builder()
                        .inspectionCode(inspectionCode)
                        .createTime(now)
                        .expirationTime(expirationTime)
                        .projectId(projectId)
                        .build();
        addProjectInspection(creatorId, projectInspection);
        return projectInspection;
    }

    @Override
    public void addProjectInspection(long creatorId, ProjectInspection inspection) {
        inspection.setCreateTime(System.currentTimeMillis());
        projectInspectionMapper.deleteByProjectId(inspection.getProjectId());
        projectInspectionMapper.insert(inspection);
        sendCreateProjectInspectionActivity(creatorId, inspection.getProjectId(), inspection.getInspectionCode());
    }

    @Override
    public void sendCreateProjectInspectionActivity(long creatorId, long projectId, String inspectionCode) {
        Project project = projectMapper.getById(projectId);
        if (project == null) {
            return;
        }
        Message.CommentMessage.Builder commentMessage = Message.CommentMessage.newBuilder()
            .setProjectId(projectId)
            .setContent(
                getProjectInspectionContent(inspectionCode))
            .setSource(ActivitySourceEnum.IBEES_GENERATION.getValue())
            .setCreatedBy(
                com.bees360.user.Message.UserMessage.newBuilder()
                    .setId(String.valueOf(creatorId))
                    .build());
        activityManager.submitActivity(
                Activity.of(
                        Message.ActivityMessage.newBuilder()
                                .setProjectId(projectId)
                                .setSource(ActivitySourceEnum.IBEES_GENERATION.getValue())
                                .setCreatedBy(
                                        com.bees360.user.Message.UserMessage.newBuilder()
                                                .setId(String.valueOf(creatorId))
                                                .build())
                                .setAction(Message.ActivityMessage.ActionType.SEND.name())
                                .setEntity(
                                        Message.ActivityMessage.Entity.newBuilder()
                                                .setType(Message.ActivityMessage.EntityType.INSURED.name())
                                                .setId(Optional.ofNullable(project.getAssetOwnerName()).orElse(Strings.EMPTY))
                                                .build())
                                .setField(
                                        Message.ActivityMessage.Field.newBuilder()
                                                .setType(Message.ActivityMessage.FieldType.STRING.name())
                                                .setName(Message.ActivityMessage.FieldName.INSPECTION_CODE.name())
                                                .setValue(inspectionCode)
                                                .build())
                                .setComment(commentMessage)
                                .build()));
    }

    private String getProjectInspectionContent(String inspectionCode) {
        return "Inspection Link: \t"
                + UriComponentsBuilder.fromUriString(inspectionLinkTemplate)
                        .buildAndExpand(inspectionCode)
                        .toUriString();
    }
}
