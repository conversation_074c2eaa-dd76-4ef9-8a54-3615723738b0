package com.bees360.job;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.vo.request.ProjectReworkReasonParam;
import com.bees360.job.registry.SetProjectStatusReworkJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.ProjectService;
import com.bees360.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

@Log4j2
@ToString
@RequiredArgsConstructor
public class PilotMissionReworkJobExecutor extends AbstractJobExecutor<SetProjectStatusReworkJob> {
    private final ProjectService projectService;
    private final UserService userService;

    @Override
    protected void handle(SetProjectStatusReworkJob job) throws IOException {
        ProjectReworkReasonParam reworkReasonParam = new ProjectReworkReasonParam();
        reworkReasonParam.setTitle(job.getReworkTitle());
        reworkReasonParam.setContent(job.getReworkContent());
        var projectId = Long.parseLong(job.getProjectId());
        var opUserId = userService.toWebUserId(job.getOperationUserId());
        try {
            log.info(
                    "Start pilot mission rework from opUser '{}' in project '{}' with rework param '{}'.",
                    opUserId,
                    projectId,
                    reworkReasonParam);
            projectService.updateProjectToReworkStatus(opUserId, projectId, reworkReasonParam, job.getOperationTime());
        } catch (ServiceException e) {
            log.error(
                    "Fail pilot mission rework from opUser '{}' in project '{}' with rework param '{}'.",
                    opUserId,
                    projectId,
                    reworkReasonParam,
                    e);
        }
    }
}
