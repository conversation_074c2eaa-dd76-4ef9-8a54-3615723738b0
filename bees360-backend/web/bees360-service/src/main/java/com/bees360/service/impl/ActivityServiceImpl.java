package com.bees360.service.impl;

import com.bees360.activity.Activities;
import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivityQuery;
import com.bees360.activity.Comment;
import com.bees360.activity.Message;
import com.bees360.commons.lang.time.AmericaTimeZone;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.event.registry.InspectionDueDateChanged;
import com.bees360.event.registry.InspectionScheduledTimeChanged;
import com.bees360.schedule.job.retry.RetryableJob;
import com.bees360.schedule.job.retry.RetryableJobTriggerFactory;
import com.bees360.schedule.job.retry.RetryableOperation;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;
import com.bees360.schedule.service.SchedulerManagerTransactionalDelegate;
import com.bees360.schedule.util.QuartzJobConstant;
import com.bees360.service.ActivityService;
import com.bees360.service.CommentService;
import com.bees360.web.core.format.DateFormat;
import com.google.protobuf.ByteString;
import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;
import lombok.NonNull;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Nullable;
import java.io.UncheckedIOException;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ActivityServiceImpl implements ActivityService {
    @Autowired private ActivityManager activityManager;
    @Autowired private SchedulerManagerTransactionalDelegate schedulerManagerTransactionalDelegate;
    @Autowired private CommentService commentService;

    // 与XaAssignmentCallbackService的createActivityForOperatingCompany()写入的comment保持一致
    private static final String OPERATING_COMPANY = "Operating Company";
    private static final String RESCHEDULE_REASON_TITLE = "Reschedule Reason\n";
    private static final String CHANGE_REASON_TITLE = "Change Reason\n";
    private static final String REASON_PATTERN = "Reason: %s";
    private static final String DETAILS_PATTERN = "Details: %s";
    private static final String CONTACT_OPTION_PATTERN = "Contact Insured: %s";

    @Override
    public void projectStatusChanged(long projectId, long creator, @NonNull String status, String comment) {
        Activity activity = Activities.changeProjectField(
            projectId,
            creator + "",
            Message.ActivityMessage.Field.newBuilder()
                .setName(Message.ActivityMessage.FieldName.STATUS.name())
                .setValue(status).build());

        if (StringUtils.isNotBlank(comment)) {
            Timestamp now = Timestamps.fromMillis(System.currentTimeMillis());
            var commentMsg = Comment.from(projectId, creator + "", comment, now, now).toMessage();
            activity = Activity.of(activity.toMessage().toBuilder().setComment(commentMsg).build());
        }

        execAsyncRetryable(activity);
    }

    @Override
    public void batchAmountChanged(
            long projectId,
            long creator,
            @NonNull String batchNo,
            @NonNull BigDecimal amount,
            @NonNull BigDecimal oldAmount) {
        if (amount.compareTo(oldAmount) == 0) {
            return;
        }
        Activity activity =
                Activities.changeField(
                        projectId,
                        creator + "",
                        Message.ActivityMessage.Entity.newBuilder()
                                .setType(Message.ActivityMessage.EntityType.BATCH.name())
                                .setId(batchNo)
                                .build(),
                        Message.ActivityMessage.Field.newBuilder()
                                .setOldValue(oldAmount.toString())
                                .setValue(amount.toString())
                                .setName(Message.ActivityMessage.FieldName.AMOUNT.name())
                                .setType(Message.ActivityMessage.FieldType.FLOAT.name())
                                .build());
        execAsyncRetryable(activity);
    }

    @Override
    public void assignedPilot(long projectId, long creator, long pilotId) {
        execAsyncRetryable(Activities.assignedPilot(projectId, creator + "", pilotId + ""));
    }

    @Override
    public void cancelAssignedPilot(long projectId, long creator, long pilotId) {
        Activity activity = Activities.cancelAssignedPilot(projectId, creator + "", pilotId + "");
        execAsyncRetryable(activity);
    }

    @Override
    public void inspectionTimeChanged(
        long projectId,
        String creator,
        @Nullable Long inspectionTime,
        @Nullable Long oldInspectionTime,
        @Nullable InspectionScheduledTimeChanged.Reason reason,
        long updatedTime) {
        if (Objects.equals(inspectionTime, oldInspectionTime)) {
            return;
        }
        Message.ActivityMessage.Field.Builder builder =
                Message.ActivityMessage.Field.newBuilder()
                        .setName(Message.ActivityMessage.FieldName.INSPECTION_TIME.name())
                        .setType(Message.ActivityMessage.FieldType.TIMESTAMP_MILLS.name())
                        .setDisplayName("SCHEDULED INSPECTION TIME");
        Optional.ofNullable(inspectionTime).ifPresent(a -> builder.setValue(a + ""));
        Optional.ofNullable(oldInspectionTime).ifPresent(a -> builder.setOldValue(a + ""));
        Message.CommentMessage comment = reasonToComment(reason);
        var createdAt = updatedTime == 0? Instant.now(): Instant.ofEpochMilli(updatedTime);
        Activity activity = Activities.changeProjectField(projectId, creator, builder.build(), createdAt, comment);
        execAsyncRetryable(activity);
    }

    @Override
    public void dueDateChanged(
        long projectId,
        String creator,
        @Nullable Long dueDate,
        @Nullable Long oldDueDate,
        @Nullable InspectionDueDateChanged.Reason reason) {
        if (Objects.equals(dueDate, oldDueDate)) {
            return;
        }
        Message.ActivityMessage.Field.Builder builder =
            Message.ActivityMessage.Field.newBuilder()
                .setName(Message.ActivityMessage.FieldName.DUE_DATE.name())
                .setType(Message.ActivityMessage.FieldType.TIMESTAMP_MILLS.name());
        Optional.ofNullable(dueDate).ifPresent(a -> builder.setValue(a + ""));
        Optional.ofNullable(oldDueDate).ifPresent(a -> builder.setOldValue(a + ""));
        Message.CommentMessage comment = reasonToComment(reason);
        Activity activity = Activities.changeProjectField(projectId, creator, builder.build(), Instant.now(), comment);
        execAsyncRetryable(activity);
    }

    @Override
    public void policyEffectiveDateChanged(
        long projectId,
        String creator,
        @Nullable LocalDate newPolicyEffectiveDate,
        @Nullable LocalDate oldPolicyEffectiveDate) {
        if (Objects.equals(newPolicyEffectiveDate, oldPolicyEffectiveDate)) {
            return;
        }
        creator = Optional.ofNullable(creator).orElse("");
        Message.ActivityMessage.Field.Builder builder =
            Message.ActivityMessage.Field.newBuilder()
                .setName(Message.ActivityMessage.FieldName.POLICY_EFFECTIVE_DATE.name())
                .setType(Message.ActivityMessage.FieldType.STRING.name());
        Optional.ofNullable(newPolicyEffectiveDate).ifPresent(a -> builder.setValue(DateFormat.format(a)));
        Optional.ofNullable(oldPolicyEffectiveDate).ifPresent(a -> builder.setOldValue(DateFormat.format(a)));
        Activity activity = Activities.changeProjectField(projectId, creator, builder.build());
        execAsyncRetryable(activity);
    }

    @Override
    public void acceptBatch(long projectId, long pilotId, @NonNull String batchNo) {
        Activity activity = Activities.acceptBatch(projectId, pilotId + "", batchNo);
        execAsyncRetryable(activity);
    }

    @Override
    public void rejectBatch(long projectId, long pilotId, @NonNull String batchNo, String reason) {
        Activity activity = Activities.rejectBatch(projectId, String.valueOf(pilotId), batchNo);
        Message.ActivityMessage message =
                activity.toMessage().toBuilder()
                        .setComment(
                                Message.CommentMessage.newBuilder()
                                        .setProjectId(projectId)
                                        .setContent(reason)
                                        .setCreatedBy(
                                                com.bees360.user.Message.UserMessage.newBuilder()
                                                        .setId(String.valueOf(pilotId))
                                                        .build())
                                        .setSource(ActivitySourceEnum.SENSITIVE_BEESPILOT.getValue())
                                        .build())
                        .setSource(ActivitySourceEnum.SENSITIVE_BEESPILOT.getValue())
                        .build();
        execAsyncRetryable(Activity.of(message));
    }

    @Override
    public void subscribePlnar(long projectId, long createdBy, @NonNull String plnarUrl) {
        execAsyncRetryable(Activities.subscribePlnar(projectId, createdBy + "", plnarUrl));
    }

    @Override
    public void subscribePlnarOnFailed(long projectId, long createdBy) {
        execAsyncRetryable(Activities.subscribePlnarOnFailed(projectId, createdBy + ""));
    }

    @Override
    public void subscribeHover(long projectId, long createdBy, @NonNull String hoverJobId) {
        execAsyncRetryable(Activities.subscribeHover(projectId, createdBy + "", hoverJobId));
    }

    @Override
    public void subscribeHoverOnFailed(long projectId, long createdBy) {
        execAsyncRetryable(Activities.subscribeHoverOnFailed(projectId, createdBy + ""));
    }

    @Override
    public void uploadImages(long projectId, long createdBy, int imageLength) {
        execAsyncRetryable(Activities.createImage(projectId, createdBy + "", imageLength));
    }

    @Override
    public String getNote(long projectId) {
        List<? extends Activity> list =
                activityManager.getActivities(
                        ActivityQuery.builder()
                                .projectId(projectId)
                                .platform("APP")
                                .build());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd, hh:mm a z", Locale.US);
        List<String> comments =
                list.stream()
                        .filter(activity -> Objects.nonNull(activity.getComment()))
                        .map(activity -> map(activity, activity.getComment().getContent(), formatter))
                        .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(comments)) {
            return null;
        }

        comments.stream()
            .filter(s -> s.contains(OPERATING_COMPANY))
            .findFirst()
            .ifPresent(s -> comments.add(0, comments.remove(comments.indexOf(s))));

        return comments.stream()
                .filter(StringUtils::isNotBlank)
                .map(
                        s ->
                                Optional.of(s)
                                        .filter(ss -> !ss.endsWith("\n"))
                                        .map(s1 -> s1 + "\r\n")
                                        .orElse(s))
                .collect(Collectors.joining("\r\n"));
    }

    private Message.CommentMessage reasonToComment(InspectionScheduledTimeChanged.Reason reason) {
        if(reason == null) {
            return null;
        }
        return builderComment(
                reason.getReason(),
                reason.getDetails(),
                reason.getContactOptionLabel(),
                RESCHEDULE_REASON_TITLE);
    }

    private Message.CommentMessage reasonToComment(InspectionDueDateChanged.Reason reason) {
        if (reason == null) {
            return null;
        }
        return builderComment(reason.getReason(), reason.getDetails(), null, CHANGE_REASON_TITLE);
    }

    private Message.CommentMessage builderComment(
            String reason, String details, String contactOptionLabel, String reasonTitle) {
        if (StringUtils.isBlank(reason)
                && StringUtils.isBlank(details)
                && StringUtils.isBlank(contactOptionLabel)) {
            return null;
        }
        String content = reasonTitle;
        if(StringUtils.isNotBlank(reason)) {
            content += "\n" + REASON_PATTERN.formatted(reason);
        }
        if(StringUtils.isNotBlank(details)) {
            content += "\n" + DETAILS_PATTERN.formatted(details);
        }
        if (StringUtils.isNotBlank(contactOptionLabel)) {
            content += "\n" + CONTACT_OPTION_PATTERN.formatted(contactOptionLabel);
        }
        return Message.CommentMessage.newBuilder()
                .setContent(content)
                .setContentType("text/plain")
                .build();
    }


    private String map(Activity activity, String content, DateTimeFormatter formatter) {
        if (Message.ActivityMessage.EntityType.INSURED.name().equals(activity.getEntityType())
            && Message.ActivityMessage.FieldName.REINSPECTION_TIME.name().equals(activity.getFiledName())) {
            long reinspectionTime = Long.parseLong(activity.getValue());
            ZonedDateTime localDate =
                ZonedDateTime.ofInstant(
                            Instant.ofEpochMilli(reinspectionTime),
                            TimeZone.getTimeZone(AmericaTimeZone.US_CENTRAL).toZoneId());
            content = content.concat("\r\nRescheduled for ").concat(localDate.format(formatter));
        }
        return content;
    }

    @Async
    @Override
    public void execAsyncRetryable(@NonNull Activity activity) {
        try {
            activityManager.submitActivity(activity);
        } catch (IllegalStateException e) {
            log.error("Failed to submit activity '{}' and it will not be retried.", activity, e);
        } catch (UncheckedIOException e) {
            log.error(
                    "Failed to submit activity '{}' and it will be retried after 5 minutes.",
                    activity,
                    e);
            JobDetail jobDetail = createJobDetail(activity);
            JobKey jobKey = jobDetail.getKey();
            // 五分钟之后同步
            Trigger trigger =
                    RetryableJobTriggerFactory.retryForeverTriggerStartAt(
                                    jobKey.getName(),
                                    jobKey.getGroup(),
                                    Duration.ofMinutes(10),
                                    DateUtils.addMinutes(new Date(), 5))
                            .build();
            try {
                schedulerManagerTransactionalDelegate.scheduleJob(jobDetail, trigger);
            } catch (SchedulerException schedulerException) {
                String message =
                    "Failed to submit quartz job to trigger activity '%s'.".formatted(activity);
                log.error(message, schedulerException);
                throw new IllegalStateException(message, schedulerException);
            }
        }
    }

    public JobDetail createJobDetail(Activity activity) {
        String jobName =
            "activity-submit-trigger-%s-%s-%s-%s".formatted(
                // 用project action creator + 时间 作为jobName
                activity.getProjectId(),
                activity.getAction(),
                activity.getCreatedBy(),
                activity.getCreatedAt());
        JobDataMap jobData = new JobDataMap();
        jobData.put("projectId", activity.getProjectId());
        jobData.put("creator", activity.getCreatedBy());
        jobData.put("byteString", activity.toByteString());
        String description = "Trigger to submit activity '%s'.".formatted(activity);

        return JobBuilder.newJob(ActivityServiceImpl.ActivitySubmitRetry.class)
                .withIdentity(jobName, QuartzJobConstant.WebGroup.ACTIVITY_SUBMIT_RETRY)
                .usingJobData(jobData)
                .withDescription(description)
                .requestRecovery(true)
                .build();
    }

    @Slf4j
    private static class ActivitySubmitRetry extends RetryableJob {
        @Autowired private ActivityManager activityManager;
        @Setter private Long projectId;
        @Setter private String creator;
        @Setter private ByteString byteString;

        @Override
        protected RetryableOperation getRetryableOperation() {
            return ctx -> {
                Message.ActivityMessage message = Message.ActivityMessage.parseFrom(byteString);
                log.info(
                        "Start to submit project '{}' activity '{}' created by '{}'.",
                        projectId,
                        message.toString(),
                        creator);
                activityManager.submitActivity(Activity.of(message));
                log.info(
                        "Success to submit project '{}' activity '{}' created by '{}'.",
                        projectId,
                        message.toString(),
                        creator);
            };
        }

        @Override
        protected RetryExceptionHandler getRetryExceptionHandler() {
            return ctx -> {
                // 最多重试10交次
                if (ctx.getRetryCount() > 10) {
                    log.error(
                            "Failed to submit project '{}' activity created by {} and it will stop retry.",
                            projectId,
                            creator);
                    return false;
                }
                log.error(
                        "Failed to submit project '{}' activity created by {} and it will be retried.",
                        projectId,
                        creator);
                return true;
            };
        }
    }
}
