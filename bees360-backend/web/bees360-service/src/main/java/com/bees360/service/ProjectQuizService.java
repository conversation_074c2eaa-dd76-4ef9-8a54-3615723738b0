package com.bees360.service;

import com.bees360.base.exception.ServiceMessageException;
import com.bees360.entity.ProjectQuiz;
import com.bees360.entity.dto.ProjectQuizDto;

import java.util.List;

/**
 * 项目问卷相关服务
 * <AUTHOR>
 * @since 2020/4/10 2:07 PM
 **/
public interface ProjectQuizService {
    /**
     * 批量插入问卷回答
     *
     * @param quizList  回答列表
     * @param projectId project id
     */
    void batchInsertAnswer(long projectId, List<ProjectQuiz> quizList) throws ServiceMessageException;

    /**
     * 查询项目问卷列表,包含最近一次的回答
     */
    List<ProjectQuizDto> listProjectQuiz(long projectId) throws ServiceMessageException;

}
