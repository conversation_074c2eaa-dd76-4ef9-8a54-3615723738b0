package com.bees360.service.beespilot.impl;

import com.bees360.api.InvalidArgumentException;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.BeesPilotStatus;
import com.bees360.entity.CompanyIDMap;
import com.bees360.entity.Project;
import com.bees360.entity.enums.CheckStatusEnum;
import com.bees360.entity.enums.ImageUploadStatusEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.mapper.BeesPilotStatusMapper;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.pipeline.PipelineTask;
import com.bees360.service.EventHistoryService;
import com.bees360.service.MemberService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.beespilot.BeesPilotStatusService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.util.Iterables;
import com.bees360.web.event.report.QuizCompletedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Set;
import java.util.stream.Collectors;

import static com.bees360.entity.enums.PipelineTaskEnum.CHECK_IN;
import static com.bees360.entity.enums.PipelineTaskEnum.CREATE_HOVER;
import static com.bees360.entity.enums.PipelineTaskEnum.CREATE_PLNAR;
import static com.bees360.entity.enums.PipelineTaskEnum.FILL_OUT_FORM;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_DRONE_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_EXTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_HOVER_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_INTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_MAGICPLAN_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.TAKE_PLNAR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.UPLOAD_DRONE_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.UPLOAD_EXTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.UPLOAD_INTERIOR_IMAGES;
import static com.bees360.entity.enums.PipelineTaskEnum.VERIFY_ADDRESS;

/**
 * <AUTHOR>
 * @since 2020/7/31 3:13 下午
 **/
@Slf4j
@Service("beesPilotStatusService")
public class BeesPilotStatusServiceImpl implements BeesPilotStatusService, ApplicationEventPublisherAware {

    @Resource
    private BeesPilotStatusMapper beesPilotStatusMapper;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private CompanyIDMap companyIDMap;
    @Autowired
    private ProjectStatusService projectStatusService;
    @Autowired
    private EventHistoryService eventHistoryService;
    @Autowired
    private FirebaseService firebaseService;
    @Autowired
    private MemberService memberService;
    @Autowired
    private PipelineService pipelineService;
    private ApplicationEventPublisher publisher;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.publisher = applicationEventPublisher;
    }

    @Override
    public BeesPilotStatus getBeesPilotStatus(long projectId) {
        BeesPilotStatus status = beesPilotStatusMapper.getByProjectId(projectId);
        if (status == null) {
            status = new BeesPilotStatus();
            status.setProjectId(projectId);
            status.setLastUpdateTime(System.currentTimeMillis());
            beesPilotStatusMapper.insert(status);
        }
        return status;
    }

    private BeesPilotStatus updateMobileCheck(long projectId, CheckStatusEnum checkStatusEnum) {
        BeesPilotStatus status = getBeesPilotStatus(projectId);;
        status.setMobileCheckStatus(checkStatusEnum.getCode());
        return updateOrInsert(status);
    }


    private BeesPilotStatus updateDroneCheck(long projectId, CheckStatusEnum checkStatusEnum) {
        BeesPilotStatus status = getBeesPilotStatus(projectId);;
        status.setDroneCheckStatus(checkStatusEnum.getCode());
        return updateOrInsert(status);
    }

    @Override
    public BeesPilotStatus updateCheck(long projectId, CheckStatusEnum checkStatusEnum) {
        BeesPilotStatus status = getBeesPilotStatus(projectId);
        status.setCheckStatus(checkStatusEnum.getCode());
        if (CheckStatusEnum.CHECK_OUT.equals(checkStatusEnum)) {
            status.setCheckoutTime(System.currentTimeMillis());
        }
        return updateOrInsert(status);
    }

    @Override
    public void beesPilotCheckout(long projectId, Integer preCheckoutReason) {
        BeesPilotStatus status = getBeesPilotStatus(projectId);
        status.setCheckStatus(CheckStatusEnum.CHECK_OUT.getCode());
        status.setCheckoutTime(System.currentTimeMillis());
        status.setPreCheckoutReason(preCheckoutReason);
        updateOrInsert(status);
    }

    @Override
    public BeesPilotStatus checkByProjectStatus(long projectId, ProjectStatusEnum projectStatusEnum) {
        switch (projectStatusEnum) {
            case TASK_CHECKED_IN:
                return updateCheck(projectId, CheckStatusEnum.CHECK_IN);
            case TASK_CHECKED_OUT:
                return updateCheck(projectId, CheckStatusEnum.CHECK_OUT);
            case PILOT_CHECKED_IN:
                return updateDroneCheck(projectId, CheckStatusEnum.CHECK_IN);
            case PILOT_CHECKED_OUT:
                return updateDroneCheck(projectId, CheckStatusEnum.CHECK_OUT);
            case ADJUSTER_UNDERWRITING_CHECKED_IN:
                return updateMobileCheck(projectId, CheckStatusEnum.CHECK_IN);
            case ADJUSTER_UNDERWRITING_CHECKED_OUT:
                return updateMobileCheck(projectId, CheckStatusEnum.CHECK_OUT);
            default:
                throw new IllegalArgumentException(projectStatusEnum.getDisplay());
        }
    }

    @Override
    public BeesPilotStatus pilotCompleted(long projectId) {
        if (memberService.getActiveMemberByRole(projectId, RoleEnum.PILOT.getCode()) != null) {
            BeesPilotStatus status = getBeesPilotStatus(projectId);
            completeAllStatus(status);
            return updateOrInsert(status);
        }
        return null;
    }

    @Override
    public void resetBeespilotStatus(long projectId) {
        BeesPilotStatus newStatus = new BeesPilotStatus();
        newStatus.setProjectId(projectId);
        updateOrInsert(newStatus);
    }


    @Override
    public void initStatus(long projectId) {
        BeesPilotStatus newStatus = new BeesPilotStatus();
        newStatus.setProjectId(projectId);
        beesPilotStatusMapper.insert(newStatus);
    }

    private BeesPilotStatus completeAllStatus(BeesPilotStatus status) {
        int checkOutCode = CheckStatusEnum.CHECK_OUT.getCode();
        status.setCheckStatus(checkOutCode);
        status.setDroneCheckStatus(checkOutCode);
        status.setMobileCheckStatus(checkOutCode);
        status.setImageUploadStatus(getNeedAllImageUpdateStatus(status.getProjectId()));
        status.setQuizCompleted(true);
        status.setAddressVerified(true);
        status.setImageUploaded(true);
        if (status.getCheckoutTime() == null) {
            log.warn("The project [{}] BeesPilot Status image uploaded time is missing.", status.getProjectId());
            status.setCheckoutTime(System.currentTimeMillis());
        }
        if (status.getImageUploadedTime() == null) {
            log.warn("The project [{}] BeesPilot Status image uploaded time is missing.", status.getProjectId());
            status.setImageUploadedTime(System.currentTimeMillis());
        }
        return status;
    }
    @Override
    public BeesPilotStatus quizCompleted(long projectId, boolean completed) {
        BeesPilotStatus status = getBeesPilotStatus(projectId);
        status.setQuizCompleted(completed);
        publisher.publishEvent(new QuizCompletedEvent(this, projectId));
        return updateOrInsert(status);
    }

    @Override
    public BeesPilotStatus addImageUploadStatus(long userId, long projectId, int imageUpdateStatus) {
        BeesPilotStatus status = getBeesPilotStatus(projectId);
        status.setImageUploadStatus(status.getImageUploadStatus() | imageUpdateStatus);
        imageUploadStatusChanged(userId, projectId, status);
        updateOrInsert(status);
        return status;
    }

    private void imageUploadStatusChanged(long userId, long projectId, BeesPilotStatus status) {
        long now = System.currentTimeMillis();
        if (isFinishedUpload(projectId, status.getImageUploadStatus())) {
            if (!status.isImageUploaded()) {
                status.setImageUploaded(true);
                status.setImageUploadedTime(now);
            }
            try {
                eventHistoryService.insertHistoryToProject(ProjectStatusEnum.ALL_IMAGE_UPLOADED, projectId, userId);
            } catch (ServiceException e) {
                log.error("Generate all image uploaded completed failed. Project is " + projectId, e);
            }
        } else {
            status.setImageUploaded(false);
        }
        if (ImageUploadStatusEnum.isDroneImageAllUpload(status.getImageUploadStatus())) {
            status.setDroneCheckStatus(CheckStatusEnum.CHECK_OUT.getCode());
        }
        if (ImageUploadStatusEnum.isMobileImageAllUploaded(status.getImageUploadStatus())) {
            status.setMobileCheckStatus(CheckStatusEnum.CHECK_OUT.getCode());
        }
        if (ImageUploadStatusEnum.isAddressVerifiedAllUpload(status.getImageUploadStatus())) {
            status.setAddressVerified(true);
        }

        setPipelineTaskStatus(projectId, status);
    }

    private void setPipelineTaskStatus(long projectId, BeesPilotStatus status) {
        var droneUploaded =
                ImageUploadStatusEnum.isDroneImageAllUpload(status.getImageUploadStatus());
        var mobileUploaded =
                ImageUploadStatusEnum.isMobileImageAllUploaded(status.getImageUploadStatus());
        var pipelineId = String.valueOf(projectId);
        var pipeline = pipelineService.findById(pipelineId);
        if (pipeline == null) {
            return;
        }
        var taskSet =
                Iterables.toStream(pipeline.getTask())
                        .map(PipelineTask::getKey)
                        .collect(Collectors.toSet());
        if (isFinishedUpload(projectId, status.getImageUploadStatus())|| (droneUploaded && mobileUploaded)) {
            setAllMissionRelateTask(taskSet, pipelineId);
        } else if (droneUploaded) {
            if (taskSet.contains(CHECK_IN.getKey())) {
                setTaskStatus(pipelineId, CHECK_IN.getKey());
            }
            setDroneRelateTaskDone(taskSet, pipelineId);
        } else if (mobileUploaded) {
            if (taskSet.contains(CHECK_IN.getKey())) {
                setTaskStatus(pipelineId, CHECK_IN.getKey());
            }
            setMobileRelateTaskDone(taskSet, pipelineId);
        }
    }

    private void setAllMissionRelateTask(Set<String> taskSet, String pipelineId) {
        if (taskSet.contains(CHECK_IN.getKey())) {
            setTaskStatus(pipelineId, CHECK_IN.getKey());
        }
        setOtherRelateTaskDone(taskSet, pipelineId);
        setDroneRelateTaskDone(taskSet, pipelineId);
        setMobileRelateTaskDone(taskSet, pipelineId);
    }

    private void setOtherRelateTaskDone(Set<String> taskSet, String pipelineId) {
        if (taskSet.contains(CREATE_HOVER.getKey())) {
            setTaskStatus(pipelineId, CREATE_HOVER.getKey());
        }
        if (taskSet.contains(CREATE_PLNAR.getKey())) {
            setTaskStatus(pipelineId, CREATE_PLNAR.getKey());
        }
        if (taskSet.contains(FILL_OUT_FORM.getKey())) {
            setTaskStatus(pipelineId, FILL_OUT_FORM.getKey());
        }
        if (taskSet.contains(TAKE_HOVER_IMAGES.getKey())) {
            setTaskStatus(pipelineId, TAKE_HOVER_IMAGES.getKey());
        }
        if (taskSet.contains(TAKE_PLNAR_IMAGES.getKey())) {
            setTaskStatus(pipelineId, TAKE_PLNAR_IMAGES.getKey());
        }
        if (taskSet.contains(TAKE_MAGICPLAN_IMAGES.getKey())) {
            setTaskStatus(pipelineId, TAKE_MAGICPLAN_IMAGES.getKey());
        }
    }
    private void setMobileRelateTaskDone(Set<String> taskSet, String pipelineId) {
        if (taskSet.contains(VERIFY_ADDRESS.getKey())) {
            setTaskStatus(pipelineId, VERIFY_ADDRESS.getKey());
        }
        if (taskSet.contains(TAKE_EXTERIOR_IMAGES.getKey())) {
            setTaskStatus(pipelineId, TAKE_EXTERIOR_IMAGES.getKey());
        }
        if (taskSet.contains(TAKE_INTERIOR_IMAGES.getKey())) {
            setTaskStatus(pipelineId, TAKE_INTERIOR_IMAGES.getKey());
        }

        if (taskSet.contains(UPLOAD_EXTERIOR_IMAGES.getKey())) {
            setTaskStatus(pipelineId, UPLOAD_EXTERIOR_IMAGES.getKey());
        }
        if (taskSet.contains(UPLOAD_INTERIOR_IMAGES.getKey())) {
            setTaskStatus(pipelineId, UPLOAD_INTERIOR_IMAGES.getKey());
        }

    }

    private void setDroneRelateTaskDone(Set<String> taskSet, String pipelineId) {
        if (taskSet.contains(TAKE_DRONE_IMAGES.getKey())) {
            setTaskStatus(pipelineId, TAKE_DRONE_IMAGES.getKey());
        }
        if (taskSet.contains(UPLOAD_DRONE_IMAGES.getKey())) {
            setTaskStatus(pipelineId, UPLOAD_DRONE_IMAGES.getKey());
        }
    }

    private void setTaskStatus(
        String pipelineId, String key) {
        try {
            pipelineService.setTaskStatus(pipelineId, key, Message.PipelineStatus.DONE);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Failed to set pipeline '{}' task '{}' to '{}'.", pipelineId, key, Message.PipelineStatus.DONE, e);
        } catch (RuntimeException e) {
            log.error("Failed to set pipeline '{}' task '{}' to '{}'.", pipelineId, key, Message.PipelineStatus.DONE, e);
        }
    }

    @Override
    public BeesPilotStatus addressVerified(long projectId, boolean verified) {
        BeesPilotStatus status = getBeesPilotStatus(projectId);
        status.setAddressVerified(verified);
        return updateOrInsert(status);
    }

    // TODO 为了兼容旧数据暂时保留insert逻辑，新的数据会在项目创建时调用 #initStatus 接口
    private BeesPilotStatus updateOrInsert(BeesPilotStatus beesPilotStatus) {
        beesPilotStatus.setLastUpdateTime(System.currentTimeMillis());
        if (!beesPilotStatusMapper.updateByProjectId(beesPilotStatus)) {
            beesPilotStatusMapper.insert(beesPilotStatus);
        }
        return beesPilotStatus;
    }

    public int getNeedAllImageUpdateStatus(long projectId) {
        Project project = null;
        project = projectService.getById(projectId);
        return ImageUploadStatusEnum.getNeedAllImageUpdateStatus(companyIDMap, project);
    }

    public boolean isFinishedUpload(long projectId, int status) {
        int needAllStatu = getNeedAllImageUpdateStatus(projectId);
        return (status & needAllStatu) == needAllStatu;
    }
}
