package com.bees360.event;

import com.bees360.entity.enums.CreationChannelType;
import com.bees360.event.registry.ProjectCreatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ProjectService;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;

/**
 * 监听项目创建事件并发布对应的Spring事件
 */
@Log4j2
public class PublishSpringCreatedEventOnProjectCreated extends AbstractNamedEventListener<ProjectCreatedEvent> {

    private final ApplicationEventPublisher publisher;

    private final ProjectService projectService;

    public PublishSpringCreatedEventOnProjectCreated(ApplicationEventPublisher publisher, ProjectService projectService) {
        this.publisher = publisher;
        this.projectService = projectService;
        log.info("Created {}.", this);
    }

    @Override
    public void handle(ProjectCreatedEvent event) throws IOException {
        var projectId = Long.parseLong(event.getProjectId());
        var project = projectService.getById(projectId);

        publisher.publishEvent(
            new com.bees360.web.event.project.ProjectCreatedEvent(
                this, project, CreationChannelType.from(event.getCreationChannel())));
    }
}
