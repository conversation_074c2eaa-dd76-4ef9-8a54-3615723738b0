package com.bees360.service;

import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.entity.label.ProjectLabel;

import java.util.List;
import java.util.Optional;

public interface ProjectLabelService {

    List<ProjectLabel> labelList();

    default void markAfterEraseLabel(long projectId, List<Long> labels, long userId, SystemTypeEnum updateSource) {
        markAfterEraseLabel(projectId, labels, String.valueOf(userId), updateSource);
    }

    void markAfterEraseLabel(long projectId, List<Long> labels, String userId, SystemTypeEnum updateSource);

    List<BoundProjectLabel> projectLabelList(List<Long> projectIds);

    Optional<BoundProjectLabel> projectLabel(Long projectId);

    void removeLabel(Long userId, Long projectId, ProjectLabelEnum projectLabel);
}
