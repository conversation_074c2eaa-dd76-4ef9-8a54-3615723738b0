package com.bees360.service.openapi;

import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.UncheckedServiceException;
import com.bees360.entity.Company;
import com.bees360.entity.Project;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.openapi.OpenProjectCreateVo;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.CompanyService;
import com.bees360.service.ProjectService;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.service.properties.Bees360CompanyConfig.CompanyConfigItem;
import com.bees360.service.properties.Bees360CompanyConfig.OpenApiConfig;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service(ProjectCreationPrehandle.CREATION_PRE_HANDLER_NAME)
@RequiredArgsConstructor
public class ProjectCreationPrehandle {

    protected final static String CREATION_PRE_HANDLER_NAME = "updatePolicyEffectedDateIfPolicyExists";
    private final static long THIRTY_DAYS_IN_MILLIS = 30L * 24 * 60 * 60 * 1000;

    private final ProjectService projectService;
    private final CompanyService companyService;
    private final ProjectMapper projectMapper;
    private final Bees360CompanyConfig bees360CompanyConfig;

    public Project handle(long userId, OpenProjectCreateVo projectCreateVo) {
        Company company = companyService.getByUserId(userId);
        CompanyConfigItem companyConfigItem = bees360CompanyConfig.findConfig(company.getCompanyId());
        String preHandler = Optional.ofNullable(companyConfigItem).map(CompanyConfigItem::getOpenApi)
            .map(OpenApiConfig::getProjectCreationPreHandler).orElse(null);
        if (!CREATION_PRE_HANDLER_NAME.equals(preHandler)) {
            return null;
        }
        List<Project> projects = listProjects(company, projectCreateVo);
        if (CollectionUtils.isEmpty(projects)) {
            return null;
        }
        Project finalProject = null;
        for (Project project: projects) {
            if (!Objects.equals(project.getPolicyEffectiveDate(), projectCreateVo.getPolicyEffectiveDate())) {
                projectService.updatePolicyEffectedDate(project.getProjectId(), projectCreateVo.getPolicyEffectiveDate());
            }
            finalProject = project;
        }
        return finalProject == null? null: projectService.getById(finalProject.getProjectId());
    }

    private List<Project> listProjects(Company company, OpenProjectCreateVo projectCreateVo) {
        String policyNumber = projectCreateVo.getPolicyNumber();
        long startTime = System.currentTimeMillis() - THIRTY_DAYS_IN_MILLIS;
        long endTime = System.currentTimeMillis();
        // 找到一个月内创建的具有相同policyNumber的project
        List<Project> projects = projectMapper.listByPolicyNumber(policyNumber, company.getCompanyId(), startTime, endTime);
        return projects.stream()
            .filter(p -> p.getLatestStatus() != ProjectStatusEnum.CANCELED.getCode())
            .filter(p -> p.getLatestStatus() != ProjectStatusEnum.DELETE.getCode())
            .filter(p -> p.getProjectStatus() != NewProjectStatusEnum.PROJECT_CANCELED.getCode())
            .collect(Collectors.toList());
    }
}
