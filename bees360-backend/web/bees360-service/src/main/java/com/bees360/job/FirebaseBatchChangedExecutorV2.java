package com.bees360.job;

import com.bees360.job.registry.SerializableFirebaseBatch;
import com.bees360.job.registry.SerializableFirebaseBatchV2;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseService;
import com.google.cloud.firestore.Firestore;

import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.beanutils.BeanUtils;
import java.io.IOException;

import static com.bees360.service.util.FirebaseExceptionTranslation.translateExceptionAndThrow;

@Log4j2
@ToString
public class FirebaseBatchChangedExecutorV2 extends AbstractJobExecutor<SerializableFirebaseBatchV2> {
    private final FirebaseService firebaseService;
    private final Firestore firestore;

    public FirebaseBatchChangedExecutorV2(FirebaseService firebaseService, Firestore firestore) {
        this.firebaseService = firebaseService;
        this.firestore = firestore;

        log.info("Created '{}'", this);
    }

    @Override
    protected void handle(SerializableFirebaseBatchV2 batchV2) throws IOException {
        SerializableFirebaseBatch batch;
        try{
            batch = convert(batchV2);
        } catch (IllegalArgumentException e){
            log.error("Failed to handle batch '{}'.", batchV2, e);
            return;
        }

        try {
            log.info("Start to handle batch '{}' '{}'", batch.getId(), batch);
            firebaseService.syncBatchToWeb(batch, batch.getId());
            log.info("Successfully handle batch '{}'", batch.getId());
        } catch (RuntimeException e) {
            log.warn("Failed to handle batch '{}'.", batch, e);
            translateExceptionAndThrow(e);
        }
    }

    private SerializableFirebaseBatch convert(SerializableFirebaseBatchV2 batchV2) throws IllegalArgumentException {
        SerializableFirebaseBatch batch = new SerializableFirebaseBatch();
        try {
            BeanUtils.copyProperties(batch, batchV2);
        } catch (Exception e) {
            throw new IllegalArgumentException(
                "Failed to convert SerializableFirebaseHoverV2 to SerializableFirebaseHover.", e);
        }
        return batch;
    }
}
