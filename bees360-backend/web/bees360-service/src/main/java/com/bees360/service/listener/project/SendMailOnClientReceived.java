package com.bees360.service.listener.project;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.MessageService;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectTaskService;
import com.bees360.service.UserService;
import com.bees360.util.Iterables;
import com.bees360.web.event.project.ProjectStatusClientReceivedEvent;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

@Log4j2
@Component
@RequiredArgsConstructor
public class SendMailOnClientReceived {

    private final MessageService messageService;

    private final ProjectReportFileService projectReportFileService;

    private final ProjectTaskService projectTaskService;

    private final ProjectMapper projectMapper;

    private final long NO_USE_FOR_USER_ID = -1L;

    private final Optional<Function<Long, Iterable<UserTinyVo>>> projectMemberMailRecipientProvider;

    private final UserService userService;

    @TransactionalEventListener(fallbackExecution = true)
    public void sendMailOnClientReceived(ProjectStatusClientReceivedEvent event) {
        Project project = event.getProject();
        if (!ClaimTypeEnum.isClaim(project.getClaimType())) {
            return;
        }
        long projectId = project.getProjectId();
        log.info("send mail to recipients of project:{}", projectId);
        try {
            List<ProjectReportFile> allReportFiles = projectReportFileService.getAllReportFiles(projectId);
            if (CollectionUtils.isEmpty(allReportFiles)) {
                return;
            }
            List<ProjectReportFile> reportsToBeSent = new ArrayList<>();
            for (ProjectReportFile reportFile : allReportFiles) {
                if (checkReportCanBeSent(reportFile)) {
                    reportsToBeSent.add(reportFile);
                }
            }
            if (CollectionUtils.isEmpty(reportsToBeSent)) {
                return;
            }
            List<UserTinyVo> emailRecipients = getEmailRecipients(projectId);
            if (!CollectionUtils.isEmpty(emailRecipients)) {
                boolean needImagesZipLink = isProjectNeedToSendImagesZipLink(projectId, reportsToBeSent);
                messageService.infoReportOnClientReceived(projectId, emailRecipients, reportsToBeSent, needImagesZipLink);
            }
        } catch (ServiceException e) {
            log.error("send mail on client received error, projectId:{}, operation system type:{}",
                projectId, Optional.ofNullable(event.getSystemType()).map(SystemTypeEnum::getType).orElse(""), e);
        }
    }

    private List<UserTinyVo> getEmailRecipientsDeprecated(long projectId) {
        List<UserTinyVo> emailRecipients = new ArrayList<>();
        List<UserTinyVo> members = userService.listActiveMemberInProject(projectId);

        // remove duplicated member if there has the same user id
        // remove the pilot member.
        Set<Long> recipientIds = new HashSet<>();
        members.forEach(member -> {
            if (!recipientIds.contains(member.getUserId()) && member.getRoleId() != RoleEnum.PILOT.getCode()) {
                emailRecipients.add(member);
                recipientIds.add(member.getUserId());
            }
        });
        return emailRecipients;
    }

    private List<UserTinyVo> getEmailRecipients(long projectId) {
        if (projectMemberMailRecipientProvider.isPresent()) {
            var recipients = Iterables.toList(projectMemberMailRecipientProvider.get().apply(projectId));
            log.info("Members {} of project {} will receive info for client-received",
                () -> Iterables.transform(recipients, UserTinyVo::getUserId),
                () -> projectId);
            return new ArrayList<>(recipients);
        }
        return getEmailRecipientsDeprecated(projectId);
    }

    private boolean isProjectNeedToSendImagesZipLink(long projectId, List<ProjectReportFile> reportFiles) {
        return isReportNeedImagesZipLink(reportFiles)
            && !StringUtils.isEmpty(projectMapper.getImagesArchiveUrl(projectId));
    }

    /**
     * 判断是否要在邮件中增加图片压缩包下载链接
     */
    private boolean isReportNeedImagesZipLink(List<ProjectReportFile> reports) {
        // 必须是Under writing 类型报告
        Set<Integer> reportsNeed = new HashSet<>(Arrays.asList(
            ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT.getCode(),
            ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT.getCode()));
        for (ProjectReportFile report : reports) {
            if (!reportsNeed.contains(report.getReportType())) {
                return false;
            }
        }
        return true;
    }


    private boolean checkReportCanBeSent(ProjectReportFile reportFile) throws ServiceException {
        return projectTaskService.checkProjectReportFileApproved(NO_USE_FOR_USER_ID, reportFile.getProjectId(),
            reportFile.getReportType())
            && ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT.getCode() != reportFile.getReportType();
    }

}
