package com.bees360.service.listener.message;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.project.member.MemberManager;
import com.bees360.service.ContextProvider;
import com.bees360.service.MessageService;
import com.bees360.service.UserService;
import com.bees360.user.UserProvider;
import com.bees360.util.user.UserAssemble;
import com.bees360.web.event.project.ProjectDeletedEvent;
import com.bees360.web.event.project.ProjectEvent;
import com.bees360.web.event.project.ProjectStatusReceiveErrorEvent;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 2020/04/16 10:53
 */
@Slf4j
@Component
public class EmailMessageListener {

    @Autowired
    private MessageService messageService;

    @Autowired
    private ContextProvider springSecurityContextProvider;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private MemberManager memberManager;

    @Autowired
    private UserService userService;

    @TransactionalEventListener(fallbackExecution = true)
    public void sendEmailToMembersOnProjectDeletedEvent(ProjectDeletedEvent event) throws ServiceException {
        long userId =
            Optional.of(springSecurityContextProvider.getUserIdFromContext())
                .filter(l -> l != 0)
                .orElse(event.getOperator());
        logInfo(event, userId, "Send email to the members");

        Project project = event.getProject();

        var members = userService.listActiveMemberInProject(project.getProjectId());
        var currentUser = UserAssemble.toWebUser(userProvider.findUserById(userId + ""));

        RoleEnum thisUserRole = null;
        // admin 角色覆盖 creator
        if(currentUser.hasRole(RoleEnum.ADMIN)) {
            thisUserRole = RoleEnum.ADMIN;
        }
        UserTinyVo operator = new UserTinyVo(currentUser, thisUserRole);
        for(var m: members) {
            messageService.infoProjectDeleted(project, operator, m);
        }
    }

    private void logInfo(ProjectEvent event, long userId, String message) {
        String sourceName = event.getSource().getClass().getName();
        String eventName = event.getClass().getSimpleName();
        long projectId = event.getProject().getProjectId();

        log.info("Event(name:{}, source: {}, project: {}) triggered by {}: {}", eventName, sourceName, projectId,
            userId, message);
    }

    @TransactionalEventListener(fallbackExecution = true)
    public void sendEmailOnProjectReceiveError(ProjectStatusReceiveErrorEvent event){
        messageService.infoProjectReceiveError(event.getProject(), event.getComment());
    }
}
