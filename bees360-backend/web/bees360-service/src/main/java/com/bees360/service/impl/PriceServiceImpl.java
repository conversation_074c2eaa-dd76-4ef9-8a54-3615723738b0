package com.bees360.service.impl;

import java.util.HashMap;
import java.util.Map;
import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.enums.ReportServiceOptionEnum;
import com.bees360.entity.enums.RoofEstimatedAreaEnum;
import com.bees360.entity.enums.ServiceFeeTypeEnum;
import com.bees360.entity.vo.Money;
import com.bees360.service.payment.PriceService;
import com.bees360.service.payment.SystemValueCacheService;

@Service("priceService")
public class PriceServiceImpl implements PriceService{

	private static final Logger logger = LoggerFactory.getLogger(PriceServiceImpl.class);

	@Inject
	private SystemValueCacheService systemValueCacheService;

	@Override
	public Map<String,Object> getReportPrice(int serviceFeeTypeId) throws ServiceException{
		Money reportPrice = systemValueCacheService.getReportServicePrice(serviceFeeTypeId);
		Map<String,Object> priceMap = new HashMap<>();
		priceMap.put("price", reportPrice.getValue());
		priceMap.put("currency", reportPrice.getCurrency());
		return priceMap;
	}

	@Override
	public Money getReportPrice(int serviceFeeTypeId, double area) throws ServiceException {
		ServiceFeeTypeEnum serviceFeeTypeEnum = ServiceFeeTypeEnum.getEnum(serviceFeeTypeId);
		if(ServiceFeeTypeEnum.FULL_DAMAGE_MEASUREMENT_REPORT == serviceFeeTypeEnum
				|| ServiceFeeTypeEnum.PREMIUM_MEASUREMENT_REPORT == serviceFeeTypeEnum
				|| ServiceFeeTypeEnum.PRELIMINARY_DAMAGE_ASSESSMENT_REPORT == serviceFeeTypeEnum) {
			//v2:modify price strategy to in accordance with price in order page
			RoofEstimatedAreaEnum roofEstimatedAreaEnum = null;
			if(area > 0) {
				roofEstimatedAreaEnum = RoofEstimatedAreaEnum.getEnumByValue(area);
			}
			//if area is 0,Calculate price according to the minimum area
			else {
				roofEstimatedAreaEnum = RoofEstimatedAreaEnum.AREA_0_40SQ;
			}
			ReportServiceOptionEnum reportServiceOptionEnum = ReportServiceOptionEnum.getEnumByServiceFeeType(serviceFeeTypeId);
			return getReportPrice(roofEstimatedAreaEnum.getCode(),reportServiceOptionEnum.getCode());
		}
		//bidding report
		else if(ServiceFeeTypeEnum.REAL_TIME_QUICK_SQUARE_REPORT == serviceFeeTypeEnum
				|| ServiceFeeTypeEnum.ON_SITE_BIDDING_REPORT == serviceFeeTypeEnum) {
			if(ServiceFeeTypeEnum.ON_SITE_BIDDING_REPORT == serviceFeeTypeEnum) {
				return systemValueCacheService.getBiddingReportPrice();
			}else {
				return new Money(0);
			}
		}
		//highfly inspection
		else if(ServiceFeeTypeEnum.HIGHFLY_EVALUATION_REPORT == serviceFeeTypeEnum) {
			return systemValueCacheService.getHighflyReportPrice();
		}
		//2019-03-25 added: Infrared Damage Assessment Report: $2.5/SQ
		else if(ServiceFeeTypeEnum.INFRARED_DAMAGE_ASSESSMENT == serviceFeeTypeEnum) {
			return systemValueCacheService.getReportServicePrice(serviceFeeTypeId);
		}
		else {
			logger.warn("the price of " + serviceFeeTypeEnum.getDisplay() + " has not defined yet");
			throw new ServiceException(MessageCode.PAY_PAYMENT_REPORT_IS_NOT_SUPPORT);
		}
	}

	@Override
	public Money getReportPrice(int roofEstimatedAreaItem, int reportServiceOption) throws ServiceException {
		Money money = systemValueCacheService.getReportServicePrice(roofEstimatedAreaItem,reportServiceOption);
		if(money == null) {
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
		return money;
	}
}
