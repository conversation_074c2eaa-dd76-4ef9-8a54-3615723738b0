package com.bees360.service.listener.project;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.entity.Member;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.ProjectMessage;
import com.bees360.entity.ProjectSyncLog;
import com.bees360.entity.User;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectEventTypeEnum;
import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.entity.enums.ProjectSyncPointEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.entity.label.ProjectLabel;
import com.bees360.entity.label.ProjectLabelDto;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.ImageDeleteJob;
import com.bees360.mapper.MemberMapper;
import com.bees360.pipeline.PipelineService;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectSyncLogService;
import com.bees360.service.beespilot.BeesPilotBatchItemService;
import com.bees360.service.beespilot.BeesPilotStatusService;
import com.bees360.service.event.project.ProjectImageUploadEvent;
import com.bees360.service.grpc.GrpcProjectGenericService;
import com.bees360.service.grpc.impl.GrpcProjectStatusChangeServiceComponent;
import com.bees360.service.job.ProjectReturnToClientJob;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.Iterables;
import com.bees360.web.event.beespilot.ProjectCancelPilotEvent;
import com.bees360.web.event.project.ProjectCanceledEvent;
import com.bees360.web.event.project.ProjectCreatedEvent;
import com.bees360.web.event.project.ProjectDeletedEvent;
import com.bees360.web.event.project.ProjectImagesDeletedEvent;
import com.bees360.web.event.project.ProjectInspectionTimeChangedEvent;
import com.bees360.web.event.project.ProjectLabelChangedEvent;
import com.bees360.web.event.project.ProjectMessageCreatedEvent;
import com.bees360.web.event.project.ProjectOrderedServiceReportApprovedEvent;
import com.bees360.web.event.project.ProjectRecoveredEvent;
import com.bees360.web.event.project.ProjectReworkWebEvent;
import com.bees360.web.event.project.ProjectStatusAssignedToPilotEvent;
import com.bees360.web.event.project.ProjectStatusClientReceivedEvent;
import com.bees360.web.event.project.ProjectStatusCustomerContactedEvent;
import com.bees360.web.event.project.ProjectStatusIBeesUploadedEvent;
import com.bees360.web.event.project.ProjectStatusImageUploadedEvent;
import com.bees360.web.event.project.ProjectStatusSiteInspectedEvent;
import com.bees360.web.event.project.claim.ProjectClaimEntryRecordEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bees360.entity.enums.ProjectLabelEnum.NO_RESPONSE_FROM_INSURED;
import static com.bees360.entity.enums.ProjectLabelEnum.PENDING_TO_RESCHEDULE;
import static com.bees360.entity.enums.ProjectLabelEnum.PENDING_TO_SCHEDULE;
import static com.bees360.entity.enums.ProjectLabelEnum.PILOT_REJECT_MISSION;

/**
 * <AUTHOR>
 * @date 2020/07/25 10:16
 */
@Slf4j
@Component
public class ProjectChangeListener {

    @Autowired
    private BeesPilotBatchItemService beesPilotBatchItemService;

    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private GrpcProjectStatusChangeServiceComponent statusChangeServiceComponent;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private GrpcProjectGenericService grpcProjectGenericService;

    @Autowired
    private ProjectSyncLogService projectSyncLogService;

    @Autowired
    private BeesPilotStatusService beesPilotStatusService;
    @Autowired
    private JobScheduler jobScheduler;
    @Autowired
    private PipelineService pipelineService;

    @Autowired
    private ProjectLabelService projectLabelService;

    @Autowired
    private Bees360FeatureSwitch featureSwitch;

    private static final Set<Long> LABELS_TO_ERASE_ON_DUE_DATE_CHANGE =
        Stream.of(PENDING_TO_SCHEDULE, PENDING_TO_RESCHEDULE, NO_RESPONSE_FROM_INSURED, PILOT_REJECT_MISSION)
            .map(ProjectLabelEnum::getLabelId).collect(Collectors.toSet());

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void syncToAiOnProjectCreatedEvent(ProjectCreatedEvent event) throws ServiceException {
        Project project = event.getProject();
        if (project == null) {
            return;
        }
        log.info("syncToAiOnProjectCreatedEvent projectId:{}", project.getProjectId());
        projectService.transferDatasToAi(project.getProjectId(), ProjectSyncPointEnum.PROJECT_CREATED.getType());
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void initBeesPilotStatus(ProjectCreatedEvent event) {
        Project project = event.getProject();
        beesPilotStatusService.initStatus(project.getProjectId());
    }


    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void syncToAiOnProjectDeletedEvent(ProjectDeletedEvent event) throws ServiceException {
        Project project = event.getProject();
        if (project == null) {
            return;
        }
        log.info("syncToAiOnProjectDeletedEvent projectId:{}", project.getProjectId());
        try {
            statusChangeServiceComponent.updateProjectStatusToAi(project.getProjectId(),
                ProjectEventTypeEnum.DELETE);
        } catch (Exception e) {
            log.error("syncToAiOnProjectDeletedEvent projectId:{}, msg:{}", event.getProject().getProjectId(), e.getMessage(), e);
            throw new ServiceException(MessageCode.SYSTEM_EXCEPTION, e);
        }

    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void syncToAiOnProjectCanceledEvent(ProjectCanceledEvent event) throws ServiceException {
        Project project = event.getProject();
        if (Objects.equals(event.getSystemType(), SystemTypeEnum.BEES_AI)) {
            log.info(
                "ProjectCanceledEvent: The data comes from AI, and there is no need to synchronize to AI. projectId:{}",
                project.getProjectId());
            // 如果修改的数据源来源于AI端，则不需要再次同步回AI端
            return;
        }
        try {
            if (project == null) {
                return;
            }
            log.info("syncToAiOnProjectCanceledEvent projectId:{}", project.getProjectId());
            statusChangeServiceComponent.updateProjectStatusToAi(project.getProjectId(), ProjectEventTypeEnum.CANCEL);
        } catch (Exception e) {
            log.error("syncToAiOnProjectCanceledEvent projectId:{}, msg:{}", event.getProject().getProjectId(), e.getMessage(), e);
            throw new ServiceException(MessageCode.SYSTEM_EXCEPTION, e);
        }
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void syncToAiOnProjectRecoveredEvent(ProjectRecoveredEvent event) throws ServiceException {
        try {
            Project project = event.getProject();
            if (project == null) {
                return;
            }
            log.info("syncToAiOnProjectRecoveredEvent projectId:{}", project.getProjectId());

            statusChangeServiceComponent.updateProjectStatusToAi(project.getProjectId(),
                ProjectEventTypeEnum.RECOVERED);
        } catch (Exception e) {
            log.error("syncToAiOnProjectRecoveredEvent projectId:{}, msg:{}", event.getProject().getProjectId(), e.getMessage(), e);
            throw new ServiceException(MessageCode.SYSTEM_EXCEPTION, e);
        }
    }



    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void updateBatchStatusOnProjectDeleted(ProjectDeletedEvent event) throws ServiceException{
        Project project = event.getProject();
        if (project == null) {
            return;
        }
        Member member = memberMapper.getActiveMemberByRole(project.getProjectId(), RoleEnum.PILOT.getCode());
        if (member == null) {
            return;
        }
        beesPilotBatchItemService.updateBatchDeletedStatus(project.getProjectId(), member.getUserId(), true);
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void updateBatchStatusOnProjectRecover(ProjectRecoveredEvent event) throws ServiceException{
        Project project = event.getProject();
        if (project == null) {
            return;
        }
        log.info("Received ProjectRecoveredEvent for project {}", project.getProjectId());
        Member member = memberMapper.getActiveMemberByRole(project.getProjectId(), RoleEnum.PILOT.getCode());
        if (member == null) {
            return;
        }
        beesPilotBatchItemService.updateBatchDeletedStatus(project.getProjectId(), member.getUserId(),false);
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void syncToAiOnProjectReWorkEvent(ProjectReworkWebEvent event) throws ServiceException{
        try {
            Project project = event.getProject();
            if (project == null) {
                return;
            }
            log.info("syncToAiOnProjectReWorkEvent projectId:{}", project.getProjectId());
            statusChangeServiceComponent.updateProjectStatusToAi(project.getProjectId(),
                ProjectEventTypeEnum.REWORK, event.getCreatedTime());
        } catch (Exception e) {
            log.error("syncToAiOnProjectReWorkEvent projectId:{}, msg:{}", event.getProject().getProjectId(), e.getMessage(), e);
            throw new ServiceException(MessageCode.SYSTEM_EXCEPTION, e);
        }
    }

    @Async
    @TransactionalEventListener(fallbackExecution = true)
    @EventListener
    public void syncToAiOnProjectStatusImageUploadedEvent(ProjectStatusImageUploadedEvent event) throws ServiceException{
        Project project = event.getProject();
        if (project == null) {
            return;
        }
        log.info("syncToAiOnProjectStatusImageUploadedEvent projectId:{}", project.getProjectId());
        projectService.transferDatasToAi(project.getProjectId(), ProjectSyncPointEnum.IMAGE_UPLOADED.getType());
    }

    @Async
    @TransactionalEventListener(fallbackExecution = true)
    @EventListener
    public void syncToAiOnProjectProjectStatusIBeesUploadedEvent(ProjectStatusIBeesUploadedEvent event) throws ServiceException{
        Project project = event.getProject();
        if (project == null) {
            return;
        }
        log.info("syncToAiOnProjectProjectStatusIBeesUploadedEvent projectId:{}", project.getProjectId());
        projectService.transferDatasToAi(project.getProjectId(), ProjectSyncPointEnum.IBEES_UPLOADED.getType());
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void syncToAiOnProjectMessageCreatedEvent(ProjectMessageCreatedEvent event) throws ServiceException{
        ProjectMessage message = event.getProjectMessage();
        if (message == null) {
            return;
        }
        log.info("syncToAiOnProjectMessageCreatedEvent projectId:{}", message.getProjectId());
        grpcProjectGenericService.addAiProjectMessageOnWebChange(message);
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void syncToAiOnProjectStatusCustomerContactedEvent(ProjectStatusCustomerContactedEvent event) throws ServiceException{
        Project project = event.getProject();
        if (project == null) {
            return;
        }
        log.info("syncToAiOnProjectStatusCustomerContactedEvent projectId:{}", project.getProjectId());
        projectService.transferDatasToAi(project.getProjectId(), ProjectSyncPointEnum.CUSTOM_CONTACTED.getType());
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void syncToAiOnProjectStatusAssignedToPilotEvent(ProjectStatusAssignedToPilotEvent event) throws ServiceException{
        Project project = event.getProject();
        if (project == null) {
            return;
        }
        log.info("syncToAiOnProjectStatusAssignedToPilotEvent projectId:{}", project.getProjectId());
        projectService.transferDatasToAi(project.getProjectId(), ProjectSyncPointEnum.ASSIGNED_TO_PILOT.getType());
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void syncToAiOnProjectStatusSiteInspectedEvent(ProjectStatusSiteInspectedEvent event) throws ServiceException{
        Project project = event.getProject();
        if (project == null) {
            return;
        }
        log.info("syncToAiOnProjectStatusSiteInspectedEvent projectId:{}", project.getProjectId());
        projectService.transferDatasToAi(project.getProjectId(), ProjectSyncPointEnum.SITE_INSPECTED.getType());
    }

    @EventListener
    public void syncToAiOnProjectImageUploadFinishedEvent(ProjectImageUploadEvent event) throws ServiceException {
        log.info("syncToAiOnProjectImageUploadEvent projectId:{}", event.getProjectId());

        // If project status is Image Uploaded, trigger image uploaded sync.
        var project = projectService.getById(event.getProjectId());
        if (project.getProjectStatus() == NewProjectStatusEnum.IMAGE_UPLOADED.getCode()) {
            projectService.transferDatasToAi(event.getProjectId(), ProjectSyncPointEnum.IMAGE_UPLOADED.getType());
            return;
        }

        // 这里使用SITE_INSPECTED是为了避免AI端记录过多的sync log
        projectService.transferDatasToAi(event.getProjectId(), ProjectSyncPointEnum.SITE_INSPECTED.getType());
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void syncToAiOnProjectImagesDeletedEvent(ProjectImagesDeletedEvent event) throws ServiceException{
        if (Objects.equals(event.getSystemType(), SystemTypeEnum.BEES_AI)) {
            return;
        }
        log.info("syncToAiOnProjectImagesDeletedEvent projectId:{}", event.getProjectId());
        //        projectService.transferDatasToAi(project.getProjectId(),
        // ProjectSyncPointEnum.IMAGE_UPLOADED.getType());
        grpcProjectGenericService.deleteAiImagesOnWebDeleted(
                event.getProjectId(), event.getImageIds(), event.getDeleteStatus());
    }

    @Async
    @EventListener
    public void syncToFirebaseOnProjectImagesDeletedEvent(ProjectImagesDeletedEvent event) {
        long projectId = event.getProjectId();
        List<String> imageIds = event.getImageIds();
        if (!Objects.equals(ProjectImage.COMPLETELY_DELETED, event.getDeleteStatus())
                || CollectionAssistant.isEmpty(imageIds)) {
            return;
        }
        log.info("syncToFirebaseOnProjectImagesDeletedEvent projectId:{}", projectId);

        ImageDeleteJob imageDeleteJob = new ImageDeleteJob(projectId, imageIds);
        jobScheduler.schedule(
                RetryableJob.of(Job.ofPayload(imageDeleteJob), 5, Duration.ofSeconds(20), 1.2F));
    }

    @TransactionalEventListener(fallbackExecution = true)
    public void returnToClientOnProjectOrderedServiceReportApprovedEvent(ProjectOrderedServiceReportApprovedEvent event)
        throws ServiceException {
        if (featureSwitch.isDisableReturnedToClient()) {
            return;
        }
        if (featureSwitch.isEnableReturnedToClientTriggerByPipelineTask()
                && isReturnedToClientTriggerByPipelineTask(event.getProject().getProjectId())) {
            return;
        }
        Project project = event.getProject();
        long userId = event.getUserId();
        long projectId = project.getProjectId();
        if (projectService.projectServiceIsCompleted(projectId)) {
            // 只有项目的service完成的时候，才可以设置状态为return to client
            log.info("The project {} is completed when report {} is approved by user {}.", projectId, event.getReportType(), userId);
            doReturnToClient(userId, projectId);
        } else {
            log.info("Though report {} is approved by user {}, but the project {} isn't completed.", event.getReportType(), userId, projectId);
        }
    }

    private void doReturnToClient(long userId, long projectId) {
        Job job = RetryableJob.of(
            new ProjectReturnToClientJob(userId, projectId), 100, Duration.ofMinutes(10), null);
        jobScheduler.schedule(job);
    }

    private boolean isReturnedToClientTriggerByPipelineTask(long projectId) {
        var pipeline = pipelineService.findById(projectId + "");
        if (pipeline == null) {
            return false;
        }
        var returnedToClientTask = Iterables.toStream(pipeline.getTask())
            .filter(t -> "returned_to_client".equals(t.getKey()))
            .findFirst().orElse(null);
        return returnedToClientTask != null;
    }

    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void syncToAiOnProjectClaimEntryRecordEventEvent(ProjectClaimEntryRecordEvent event) throws ServiceException{
        long projectId = event.getProjectId();
        String type = event.getType();
        log.info("syncToAiOnProjectClaimEntryRecordEventEvent projectId:{}, type:{}, status:{}",
            projectId, type, event.getStatus());
        Integer status = grpcProjectGenericService
            .updateReportStatusAfterEntryRecordCheck(projectId, type, event.getStatus());
        projectSyncLogService.insert(ProjectSyncLog.builder().projectId(projectId).syncEvent(type)
            .status(status == 0 ? 1 : -1).build());
    }


    @Async
    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void syncToAiOnProjectStatusClientReceivedEvent(ProjectStatusClientReceivedEvent event) throws ServiceException{
        Project project = event.getProject();
        if (Objects.equals(event.getSystemType(), SystemTypeEnum.BEES_AI)) {
            // 如果修改的数据源来源于AI端，则不需要再次同步回AI端
            log.info(
                "ProjectStatusClientReceivedEvent: The data comes from AI, and there is no need to synchronize to AI. projectId:{}",
                project.getProjectId());
            return;
        }
        if (project == null) {
            return;
        }
        log.info("syncToAiOnProjectStatusClientReceivedEvent projectId:{}", project.getProjectId());
        statusChangeServiceComponent.updateProjectStatusToAi(project.getProjectId(),
            ProjectEventTypeEnum.CLIENT_RECEIVED, null, event.getStatus().getCreatedTime());
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void syncToAiOnProjectLabelAdded(ProjectLabelChangedEvent event){
        Long projectId = event.getProjectId();
        SystemTypeEnum updateSource = event.getUpdateSource();
        if (SystemTypeEnum.BEES_AI == updateSource){
            return;
        }
        log.info("syncToAiOnProjectStatusClientReceivedEvent projectId:{}", projectId);
        grpcProjectGenericService.updateProjectTagsOnWebChange(Collections.singletonList(new ProjectLabelDto(event.getProjectId(), event.getProjectLabels())));
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void syncToAiOnProjectInspectionTimeChangedEvent(ProjectInspectionTimeChangedEvent event) throws ServiceException {
        Project project = event.getProject();
        log.info("syncToAiOnProjectInspectionTimeChangedEvent projectId:{}", project.getProjectId());
        grpcProjectGenericService.updateProjectInspectionTimeOnWebChange(project.getProjectId(), project.getInspectionTime());
    }

    @Async
    @EventListener
    public void eraseProjectLabelOnProjectInspectionTimeChange(ProjectInspectionTimeChangedEvent event) {
        Project project = event.getProject();
        log.info("inspection time change start to erase target labels");
        cleanOperationTagsOnDueDateChange(project.getProjectId());
    }

    /**
     * project cancel飞手触发事件
     */
    @Async
    @EventListener
    public void projectCancelPilotEvent(ProjectCancelPilotEvent event) throws ServiceException {
        log.info(
                "project cancel pilot event projectId {} pilotId {}",
                event.getProjectId(),
                event.getPilotId());
        projectService.transferDatasToAi(
                event.getProjectId(), ProjectSyncPointEnum.MANUAL_SYNC.getType());
    }

    private void cleanOperationTagsOnDueDateChange(long projectId) {
        Optional<BoundProjectLabel> boundProjectLabel = projectLabelService.projectLabel(projectId);
        if (boundProjectLabel.isEmpty()) {
            return;
        }
        List<Long> targetLabels = boundProjectLabel.get().getProjectLabels().stream().map(ProjectLabel::getLabelId)
            .filter(LABELS_TO_ERASE_ON_DUE_DATE_CHANGE::contains).collect(Collectors.toList());
        if (!targetLabels.isEmpty()) {
            projectLabelService.markAfterEraseLabel(projectId, Collections.emptyList(),
                User.BEES_PILOT_SYSTEM, SystemTypeEnum.BEES360);
        }
    }
}
