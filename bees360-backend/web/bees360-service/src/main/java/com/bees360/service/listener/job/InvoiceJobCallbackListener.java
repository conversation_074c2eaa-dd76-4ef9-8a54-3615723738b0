package com.bees360.service.listener.job;

import com.bees360.event.EventPublisher;
import com.bees360.event.registry.ProjectInvoiceAdded;
import com.bees360.event.registry.ProjectInvoiceUpdatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * 监听job执行完成事件，目前通过jobName和jobId分别调用不同的方法
 *
 * <AUTHOR>
 */
@Log4j2
@Component
@ConditionalOnProperty(prefix = "bees360.feature-switch", name = "enable-new-invoice-process")
@AllArgsConstructor
public class InvoiceJobCallbackListener extends AbstractNamedEventListener<ProjectInvoiceAdded> {

    private final EventPublisher eventPublisher;

    @Override
    public void handle(ProjectInvoiceAdded invoice) {
        log.info("Update report when projectInvoiceAdded :{}", invoice);
        if (null == invoice.getProjectId()) {
            log.info("Not a claim invoice No {}", invoice.getInvoiceNo());
            return;
        }
        long projectId = Long.parseLong(invoice.getProjectId());
        eventPublisher.publish(new ProjectInvoiceUpdatedEvent(projectId, invoice.getFileKey()));
    }
}
