package com.bees360.event;

import com.bees360.event.registry.BatchRemovePilotEvent;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.BatchSchedulePilotJob;
import com.bees360.job.util.EventTriggeredJob;

import java.time.Duration;

/**
 * 将BatchRemovePilotEvent事件转换为可重试的BatchSchedulePilotJob任务
 */
public class BatchRemovePilotEvent2Job extends EventTriggeredJob<BatchRemovePilotEvent> {
    public BatchRemovePilotEvent2Job(JobScheduler jobScheduler) {
        super(jobScheduler);
    }

    private static final Integer RETRY_COUNT = 5;
    private static final Duration RETRY_DELAY = Duration.ofSeconds(1);
    private static final Float RETRY_DELAY_INCREASE_FACTOR = 2F;

    @Override
    protected Job convert(BatchRemovePilotEvent event) {
        var job = new BatchSchedulePilotJob();
        job.setProjectIds(event.getProjectIds());
        job.setOperationUserId(event.getOperationUserId());
        job.setVersion(event.getOperationTime());
        job.setAssignment(false);
        return RetryableJob.of(
                Job.ofPayload(job), RETRY_COUNT, RETRY_DELAY, RETRY_DELAY_INCREASE_FACTOR);
    }
}
