package com.bees360.service.impl;

import static com.bees360.base.MessageCode.PARAM_INVALID;
import static com.bees360.entity.dto.ProjectSearchOption.NEW_DAYS_OLD_KEY;
import static com.bees360.entity.enums.NewProjectStatusEnum.ASSIGNED_TO_PILOT;
import static com.bees360.entity.enums.NewProjectStatusEnum.CLIENT_RECEIVED;
import static com.bees360.entity.enums.NewProjectStatusEnum.CUSTOMER_CONTACTED;
import static com.bees360.entity.enums.NewProjectStatusEnum.IMAGE_UPLOADED;
import static com.bees360.entity.enums.NewProjectStatusEnum.PROJECT_CANCELED;
import static com.bees360.entity.enums.NewProjectStatusEnum.PROJECT_CREATED;
import static com.bees360.entity.enums.NewProjectStatusEnum.PROJECT_REWORK;
import static com.bees360.entity.enums.NewProjectStatusEnum.RECEIVE_ERROR;
import static com.bees360.entity.enums.NewProjectStatusEnum.RETURNED_TO_CLIENT;
import static com.bees360.entity.enums.NewProjectStatusEnum.SITE_INSPECTED;
import static com.bees360.util.ConstantUtil.BEES360_COMPANY_ID;
import static com.bees360.util.Defaults.defaultIfNull;
import static com.bees360.web.project.util.ProjectConstants.ContactRoleType.EXTERNAL_ADJUSTER;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivityQuery;
import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.address.AddressHiveLocationProvider;
import com.bees360.address.AddressManager;
import com.bees360.base.MessageCode;
import com.bees360.base.exception.AccessDatabaseException;
import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.ServiceMessageException;
import com.bees360.base.exception.UncheckedServiceException;
import com.bees360.beespilot.batch.BeesPilotBatchProvider;
import com.bees360.catastrophe.ClaimCatastrophe;
import com.bees360.contact.ContactRecordManager;
import com.bees360.contact.util.ContactRecordUtil;
import com.bees360.entity.AddressAirspace;
import com.bees360.entity.BeesPilotBatchItem;
import com.bees360.entity.BeesPilotBatchItemVo;
import com.bees360.entity.Company;
import com.bees360.entity.CompanyIDMap;
import com.bees360.entity.EventHistory;
import com.bees360.entity.Member;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectAirspace;
import com.bees360.entity.ProjectInspection;
import com.bees360.entity.ProjectMessage;
import com.bees360.entity.ProjectMessageVo;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.ProjectTimeline;
import com.bees360.entity.User;
import com.bees360.entity.converter.CompanyBeanConverter;
import com.bees360.entity.dto.CodeNameDto;
import com.bees360.entity.dto.CreateOrUpdateProjectDto;
import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.dto.IdValue;
import com.bees360.entity.dto.MemberSchedule;
import com.bees360.entity.dto.MemberSchedule.MemberScheduleItem;
import com.bees360.entity.dto.ProjectBaseDto;
import com.bees360.entity.dto.ProjectBatchDto;
import com.bees360.entity.dto.ProjectBatchItemDto;
import com.bees360.entity.dto.ProjectDto;
import com.bees360.entity.dto.ProjectImageSearchOptionDto;
import com.bees360.entity.dto.ProjectReportFileDto;
import com.bees360.entity.dto.ProjectSearchOption;
import com.bees360.entity.dto.ProjectSearchOption.SearchTagEnum;
import com.bees360.entity.dto.ProjectSearchOptionBase;
import com.bees360.entity.dto.ProjectSearchResultFilter;
import com.bees360.entity.dto.ProjectStatusTimeLineDto;
import com.bees360.entity.dto.StringIdNameDto;
import com.bees360.entity.dto.systemconfig.SystemConfigBees360Dto;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.AmericaStateEnums;
import com.bees360.entity.enums.BsExportDataRelatedTypeEnum;
import com.bees360.entity.enums.ClaimNoteAddEnum;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.CompanyTypeEnum;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.InspectionTypeEnum;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.PayStatusEnum;
import com.bees360.entity.enums.ProcessStatusEnum;
import com.bees360.entity.enums.ProjectMessageTypeEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.ProjectSyncPointEnum;
import com.bees360.entity.enums.ProjectTypeEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.enums.RoleEnum.RoleType;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.entity.label.ProjectLabel;
import com.bees360.entity.query.ProjectFilterQuery;
import com.bees360.entity.query.ProjectMessageQuery;
import com.bees360.entity.stat.rule.CompanyStatRule;
import com.bees360.entity.stat.rule.CompanyStatRuleConfig;
import com.bees360.entity.vo.Address;
import com.bees360.entity.vo.AddressVo;
import com.bees360.entity.vo.CompanyVo;
import com.bees360.entity.vo.HistoryLogVo;
import com.bees360.entity.vo.InvoiceFileVo;
import com.bees360.entity.vo.Pagination;
import com.bees360.entity.vo.ProjectAbstractVo;
import com.bees360.entity.vo.ProjectCalSelectVo;
import com.bees360.entity.vo.ProjectDetailVo;
import com.bees360.entity.vo.ProjectImageAnnotationVo;
import com.bees360.entity.vo.ProjectImageTinyVoForApp;
import com.bees360.entity.vo.ProjectInspectionInfoVo;
import com.bees360.entity.vo.ProjectInsuredInfoVo;
import com.bees360.entity.vo.ProjectLatLngVo;
import com.bees360.entity.vo.ProjectPageResultVo;
import com.bees360.entity.vo.ProjectReportFileTinyVo;
import com.bees360.entity.vo.ProjectServiceTypeVo;
import com.bees360.entity.vo.ProjectTinyUserVoCandidates;
import com.bees360.entity.vo.ProjectTinyVo;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.entity.vo.request.ProjectReworkReasonParam;
import com.bees360.entity.vo.request.ProjectServiceTypeParam;
import com.bees360.entity.vo.request.ProjectServiceTypeReasonParam;
import com.bees360.entity.vo.request.SendSmsPilotParam;
import com.bees360.event.EventPublisher;
import com.bees360.event.registry.ProjectAddressFormattedEvent;
import com.bees360.event.registry.ProjectPolicyEffectedDateChangeEvent;
import com.bees360.flyzone.FlyZoneType;
import com.bees360.flyzone.FlyZoneTypeProvider;
import com.bees360.mapper.AddressAirspaceMapper;
import com.bees360.mapper.BeesPilotStatusMapper;
import com.bees360.mapper.CompanyMapper;
import com.bees360.mapper.MemberMapper;
import com.bees360.mapper.ProjectAirspaceMapper;
import com.bees360.mapper.ProjectCustomizedInfoMapper;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.mapper.ProjectInspectionScheduleMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.ProjectOperatingCompanyMapper;
import com.bees360.mapper.ProjectStateMapper;
import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.pipeline.PipelineService;
import com.bees360.policy.Policy;
import com.bees360.policy.PolicyManager;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.project.ProjectDaysOldProvider;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectPaymentManager;
import com.bees360.project.ProjectPolicyManager;
import com.bees360.project.SimilarProjectProvider;
import com.bees360.project.claim.ProjectCatastropheManager;
import com.bees360.project.member.MemberManager;
import com.bees360.project.report.ProjectReportJobManager;
import com.bees360.project.state.ChangeReasonFromCategoryProvider;
import com.bees360.project.state.ProjectStateChangeReason;
import com.bees360.project.state.ProjectStateChangeReasonManager;
import com.bees360.project.state.ProjectStateProvider;
import com.bees360.project.status.ProjectStatusProvider;
import com.bees360.project.tag.Message;
import com.bees360.project.tag.Message.ProjectTagType;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagManager;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.service.ActivityService;
import com.bees360.service.CommentService;
import com.bees360.service.CompanyService;
import com.bees360.service.ConstantSettingService;
import com.bees360.service.ContextProvider;
import com.bees360.service.EventHistoryService;
import com.bees360.service.MemberService;
import com.bees360.service.MessageService;
import com.bees360.service.NotificationService;
import com.bees360.service.ProjectInspectionService;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectMessageService;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectReportService;
import com.bees360.service.ProjectScoreService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.ProjectTaskService;
import com.bees360.service.SystemConfigService;
import com.bees360.service.UserService;
import com.bees360.service.beespilot.BeesPilotBatchItemService;
import com.bees360.service.beespilot.BeesPilotBatchService;
import com.bees360.service.beespilot.BeesPilotStatusService;
import com.bees360.service.converter.ProjectConverter;
import com.bees360.service.event.project.ProjectImageUploadFinishedGrpcEvent;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.service.member.PostgresMemberManagerAdapter;
import com.bees360.service.projectII.ProjectIIServiceAdapter;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.service.threadpool.AsyncCreateBeeObjectThreadPool;
import com.bees360.service.threadpool.AsyncCreateBeeObjectThreadPool.OutputParam;
import com.bees360.service.util.ProjectRuleUtil;
import com.bees360.service.util.SqlHelper;
import com.bees360.user.BifrostUserMapperAdapter;
import com.bees360.user.UserProvider;
import com.bees360.util.AssertUtil;
import com.bees360.util.AuthorityUtil;
import com.bees360.util.CollectionAssitant;
import com.bees360.util.DateUtil;
import com.bees360.util.Iterables;
import com.bees360.util.Messages;
import com.bees360.util.log.TimerLog;
import com.bees360.util.project.ProjectImageArchiveKeyConverter;
import com.bees360.util.user.Bees360UserUtils;
import com.bees360.util.user.UserAssemble;
import com.bees360.web.core.properties.bean.SystemConfig;
import com.bees360.web.event.beespilot.ProjectCancelPilotEvent;
import com.bees360.web.event.project.ProjectAddressChangeEvent;
import com.bees360.web.event.project.ProjectChangeEvent;
import com.bees360.web.event.project.ProjectDeletedEvent;
import com.bees360.web.event.project.ProjectInspectionNumberChangedEvent;
import com.bees360.web.event.project.ProjectInspectionTimeChangedEvent;
import com.bees360.web.event.project.ProjectInsureCompanyChangedEvent;
import com.bees360.web.event.project.ProjectLatestStatusEvent;
import com.bees360.web.event.project.ProjectRecoveredEvent;
import com.bees360.web.event.project.ProjectRepairCompanyChangedEvent;
import com.bees360.web.event.project.ProjectServiceTypeChangedEvent;
import com.bees360.web.event.project.ProjectTemplateForMissionChangeEvent;
import com.bees360.web.project.util.ProjectAssemble;
import com.bees360.web.project.util.ProjectConstants;
import com.google.cloud.firestore.Firestore;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Streams;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import jakarta.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;

@Service("projectService")
public class ProjectServiceImpl implements ProjectService, ApplicationEventPublisherAware {

    @Value("${bees360.ibees.inspectionLinkTemplate}")
    private String inspectionLinkTemplate;

    @Autowired
    @Qualifier("CompanyAdminAssignableMemberRole")
    private List<RoleEnum> companyAdminAssignableRoles;

    private Logger logger = LoggerFactory.getLogger(ProjectServiceImpl.class);

    @Inject private ProjectMapper projectMapper;
    @Resource private ProjectMessageService projectMessageService;

    @Inject private UserService userService;

    @Inject private CompanyService companyService;

    @Inject private CompanyMapper companyMapper;

    @Inject private MessageService messageService;

    @Inject private MemberMapper memberMapper;

    @Inject private EventHistoryService eventHistoryService;

    @Inject private NotificationService notificationService;

    @Inject private ProjectImageMapper projectImageMapper;

    @Inject private ProjectReportService projectReportService;

    @Inject private ProjectReportFileService projectReportFileService;

    @Inject private ProjectTaskService projectTaskService;

    @Inject private ProjectCustomizedInfoMapper projectCustomizedInfoMapper;

    @Inject private SystemConfigService systemConfigService;

    @Inject private ProjectStatusService projectStatusService;
    @Resource private FirebaseService firebaseService;
    @Inject private ContextProvider springSecurityContextProvider;

    @Autowired private ProjectService projectService;
    @Autowired private BeesPilotStatusService beesPilotStatusService;
    @Autowired private BeesPilotStatusMapper beesPilotStatusMapper;

    @Autowired private MemberService memberService;

    @Autowired private ProjectImageArchiveKeyConverter projectImageArchiveKeyConverter;

    @Autowired private SystemConfig systemConfig;

    public ApplicationEventPublisher publisher;

    @Autowired private BeesPilotBatchItemService beesPilotBatchItemService;

    @Autowired private ProjectScoreService projectScoreService;

    @Autowired private BeesPilotBatchService beesPilotBatchService;

    @Autowired private ProjectLabelService projectLabelService;

    @Autowired private ConstantSettingService constantSettingService;

    @Autowired private ProjectInspectionService projectInspectionService;

    @Autowired private ActivityService activityService;

    @Autowired private CommentService commentService;

    @Autowired private CompanyIDMap companyIDMap;

    @Autowired private AsyncCreateBeeObjectThreadPool asyncCreateBeeObjectThreadPool;

    @Inject private MessageSource messageSource;

    @Autowired private FlyZoneTypeProvider flyZoneTypeProvider;

    @Autowired private ResourcePool resourcePool;

    @Autowired private EventPublisher eventPublisher;

    @Autowired private UserProvider userProvider;

    @Autowired private ProjectTagManager projectTagManager;

    @Autowired private AddressManager addressManager;

    @Autowired private ProjectIIServiceAdapter projectIIServiceAdapter;

    @Autowired private ProjectOperatingCompanyMapper operatingCompanyMapper;

    @Autowired private Firestore firestore;
    @Value("${bees360.ignore-project-id-end:}")
    private Long ignoreProjectIdEnd;

    @Autowired private AddressHiveLocationProvider addressHiveLocationProvider;

    @Autowired private PipelineService pipelineService;

    @Autowired private ProjectCatastropheManager projectCatastropheManager;
    @Autowired private CommentManager commentManager;
    @Autowired private ContactManager contactManager;
    @Autowired private PolicyManager policyManager;
    @Autowired private Bees360CompanyConfig bees360CompanyConfig;

    @Autowired private ProjectIIManager projectIIManager;

    @Autowired private PostgresMemberManagerAdapter postgresMemberManagerAdapter;
    @Autowired private ProjectInspectionScheduleMapper projectInspectionScheduleMapper;

    @Autowired private ContactRecordManager contactRecordManager;

    @Autowired private BeesPilotBatchProvider beesPilotBatchProvider;

    @Autowired private ProjectDaysOldProvider daysOldProvider;
    @Autowired private ProjectStateChangeReasonManager stateChangeReasonManager;
    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;
    @Autowired private ProjectStateProvider projectStateProvider;

    @Autowired private Bees360FeatureSwitch featureSwitch;

    @Autowired private ProjectPaymentManager projectPaymentManager;

    @Autowired private MemberManager memberManager;

    @Autowired private SimilarProjectProvider similarProjectProvider;
    @Autowired private ActivityManager activityManager;
    @Autowired private AddressAirspaceMapper addressAirspaceMapper;
    @Autowired private ProjectAirspaceMapper projectAirspaceMapper;
    @Autowired private ExternalIntegrationManager externalIntegrationManager;

    @Autowired private ProjectStateMapper projectStateMapper;

    @Autowired private ProjectStateChangeReasonManager projectStateChangeReasonManager;

    @Autowired private ChangeReasonFromCategoryProvider changeReasonFromCategoryProvider;

    @Autowired
    @Qualifier("closeoutGeneratePredicate")
    private Predicate<Project> closeoutGeneratePredicate;

    @Autowired private ProjectStatusProvider projectStatusProvider;

    @Autowired
    private BifrostUserMapperAdapter bifrostUserMapperAdapter;

    @Autowired
    private ProjectPolicyManager projectPolicyManager;

    @Autowired ProjectReportJobManager projectReportJobManager;

    private static final String PIPELINE_TASK_PILOT_RECOMMENDATION_KEY = "pilot_recommendation";

    private static final Set<String> PROJECT_TAGS_OF_FOLLOW_UP_IMAGE_UPLOADED =
        Set.of("missing images.", "Missing roof closeups.");

    //Operation Tag: Denied, Wrong #/ # doesn't belong to insured, Drone inspection declined,
    // Pending to Reschedule, Pending to Schedule
    private static final List<Long> PROJECT_LABELS_OF_FOLLOW_UP_CUSTOMER_CONTACTED = List.of(2L, 4L, 8L, 12L, 13L);

    private static final Set<Integer> EXCLUDE_STATUS_OF_FOLLOW_UP_SEARCH =
        Stream.of(RETURNED_TO_CLIENT, CLIENT_RECEIVED, RECEIVE_ERROR, PROJECT_CANCELED)
            .map(NewProjectStatusEnum::getCode).collect(Collectors.toSet());

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher publisher) {
        this.publisher = publisher;
    }

    @Override
    public Optional<Project> findById(long projectId) {
        var project = projectMapper.getById(projectId);

        // Fill in the latest project status data
        if (Objects.nonNull(project)) {
            var status = projectStatusProvider.getStatus(String.valueOf(projectId));
            project.setProjectStatus(status.getStatus().getNumber());
            project.setStatusUpdateTime(status.getUpdatedAt().toEpochMilli());
        }

        return Optional.ofNullable(project);
    }

    @Override
    public Project getById(long projectId)  {
        return findById(projectId).orElseThrow(
            () -> new ResourceNotFoundException("project %s not found".formatted(projectId))
        );
    }

    @Override
    public List<Project> listByInsuranceCompanyAndStatus(long companyId, Integer status, long createdTime) {
        return projectMapper.listByInsuranceCompanyAndStatus(companyId, status, createdTime);
    }

    @Override
    public List<Project> listInTimeRange(long createTimeStart, long createTimeEnd) {
        if (createTimeStart == 0 || createTimeEnd == 0) {
            return Collections.emptyList();
        }
        return projectMapper.listInTimeRange(createTimeStart, createTimeEnd);
    }

    @Override
    public List<Project> listByIds(List<Long> ids) {
        return projectMapper.listProjects(ids);
    }

    @Override
    public List<Long> listProjectIdsByAddressId(String addressId) {
        return projectMapper.listProjectIdsByAddressId(addressId);
    }

    @Override
    public Map<String, Object> getGeneralPropertyInfo(long projectId) throws ServiceException {

        Project project = getById(projectId);
        Map<String, Object> projectInfo = createGeneralPropertyInformationFromProject(project);

        return projectInfo;
    }

    @Override
    public void modifyCliamInformation(long projectId, long userId, Map<String, Object> ciMap)
            throws ServiceException {
        String claimNumber = (String) ciMap.get("claimNumber");
        Map<String, Object> newCiMap = new HashMap<String, Object>();
        if (StringUtils.isNotBlank(claimNumber)) {
            // try to modify claimNumber
            Project project = getById(projectId);
            if (StringUtils.isBlank(project.getClaimNumber())) {
                newCiMap.put("claimNumber", claimNumber);

                long now = DateUtil.getNow();

                EventHistory history = new EventHistory();
                history.setProjectId(projectId);
                history.setUserId(userId);
                history.setStatus(ProjectStatusEnum.JOB_RECEIVED.getCode());
                history.setStatusTime(now);
                history.setModifiedBy(userId);
                history.setCreatedTime(now);
                history.setDescription("");
                eventHistoryService.insertHistoryToProject(history);
            } else if (!project.getClaimNumber().equals(claimNumber)) {
                throw new ServiceException(MessageCode.CLAIM_CANNOT_MODIFIED);
            } else {

            }
        }
        newCiMap.put("policyNumber", ciMap.get("policyNumber"));
        newCiMap.put("damageEventTime", ciMap.get("damageEventTime"));
        newCiMap.put("claimType", ciMap.get("claimType"));
        newCiMap.put("claimNote", ciMap.get("claimNote"));
        newCiMap.put("projectId", projectId);
        try {
            projectMapper.updateClaimInfo(newCiMap);
        } catch (Exception e) {
            logger.error(MessageCode.DATABASE_EXCEPTION, e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
        }
    }

    @Override
    public Map<String, Object> getCliamInformation(long projectId) throws ServiceException {
        Map<String, Object> ciMap = new HashMap<>();
        Project project = getById(projectId);

        ciMap.put("claimNumber", project.getClaimNumber());
        ciMap.put("policyNumber", project.getPolicyNumber());
        ciMap.put("claimType", project.getClaimType());
        ciMap.put("damageEventTime", project.getDamageEventTime());
        ciMap.put("claimNote", project.getClaimNote());

        ciMap.put("inspectionNumber", project.getInspectionNumber());

        List<ProjectStatusEnum> historyTypeIds = new ArrayList<ProjectStatusEnum>();
        historyTypeIds.add(ProjectStatusEnum.IMAGE_UPLOADED);
        historyTypeIds.add(ProjectStatusEnum.ADJUSTER_FINISHED);
        historyTypeIds.add(ProjectStatusEnum.POLICY_HOLDER_CONTACTED);
        historyTypeIds.add(ProjectStatusEnum.CLAIM_SUBMITTED);
        historyTypeIds.add(ProjectStatusEnum.JOB_RECEIVED);
        historyTypeIds.add(ProjectStatusEnum.COMPLETED);
        historyTypeIds.add(ProjectStatusEnum.NEW_PROJECT);
        historyTypeIds.add(ProjectStatusEnum.PILOT_CHECKED_IN);

        Map<Integer, EventHistory> histories =
                eventHistoryService.listHistoryIn(projectId, historyTypeIds);
        EventHistory pilotCheckedIn = histories.get(ProjectStatusEnum.PILOT_CHECKED_IN.getCode());

        ciMap.put("inspectedBy", null);
        ciMap.put("dateInspected", null);
        ciMap.put("adjustedBy", null);
        ciMap.put("dateAdjusted", null);
        ciMap.put("dateContacted", null);
        ciMap.put("dateSubmitted", null);
        ciMap.put("dateReceived", null);
        ciMap.put("dateCompleted", null);
        ciMap.put("dateEntered", null);

        if (pilotCheckedIn != null) {
            User inspectedBy = userService.getUserById(pilotCheckedIn.getUserId());
            ciMap.put("inspectedBy", inspectedBy.getName());
            ciMap.put("dateInspected", pilotCheckedIn.getStatusTime());
        }
        EventHistory adjusterFinished =
                histories.get(ProjectStatusEnum.ADJUSTER_FINISHED.getCode());
        if (adjusterFinished != null) {
            User adjuster = userService.getUserById(adjusterFinished.getUserId());
            ciMap.put("adjustedBy", adjuster.getName());
            ciMap.put("dateAdjusted", adjusterFinished.getStatusTime());
        }
        EventHistory policyHolderContacted =
                histories.get(ProjectStatusEnum.POLICY_HOLDER_CONTACTED.getCode());
        if (policyHolderContacted != null) {
            ciMap.put("dateContacted", policyHolderContacted.getStatusTime());
        }
        EventHistory claimSubmitted = histories.get(ProjectStatusEnum.CLAIM_SUBMITTED.getCode());
        if (claimSubmitted != null) {
            ciMap.put("dateSubmitted", claimSubmitted.getStatusTime());
        }
        EventHistory jobRecieved = histories.get(ProjectStatusEnum.JOB_RECEIVED.getCode());
        if (jobRecieved != null) {
            ciMap.put("dateReceived", jobRecieved.getStatusTime());
        }
        EventHistory completed = histories.get(ProjectStatusEnum.COMPLETED.getCode());
        if (completed != null) {
            ciMap.put("dateComleted", completed.getStatusTime());
        }
        EventHistory newProject = histories.get(ProjectStatusEnum.NEW_PROJECT.getCode());
        if (newProject != null) {
            ciMap.put("dateEntered", newProject.getStatusTime());
        }

        ProjectStatusTimeLineDto projectStatusTimeLine =
                projectStatusService.getProjectStatusTimeLine(projectId);
        if (Objects.nonNull(projectStatusTimeLine)) {
            if (Objects.nonNull(projectStatusTimeLine.getSiteInspected())) {
                Long siteInspectedTime = projectStatusTimeLine.getSiteInspected().getCreatedTime();
                ciMap.put("siteInspectedTime", siteInspectedTime);
                ciMap.put("dateInspected", siteInspectedTime);
            }
            if (Objects.nonNull(projectStatusTimeLine.getCustomerContacted())) {
                Long customerContactedTime =
                        projectStatusTimeLine.getCustomerContacted().getCreatedTime();
                ciMap.put("customerContactedTime", customerContactedTime);
            }
        }
        return ciMap;
    }

    @Override
    public void modifyGeneralPropertyInformation(Project project, long modifiedBy)
            throws ServiceException {
        // <EMAIL>: change the method of checking to checking with
        // spring-hibernate
        // check some special info of project
        checkProjectInfo(project, true);

        // <EMAIL>: Maybe we don't need to add or change the Assert Owner member when we
        // modify GPI
        // changeAssetOwner(project, modifiedBy);

        try {
            // <EMAIL> Here will be changed in the near future for the unreasonable
            // implementation
            User user = userService.getUserById(modifiedBy);
            Project oldProject = projectMapper.getById(project.getProjectId());

            if (!user.hasRole(RoleEnum.ADMIN)) {
                project.setRepairCompany(oldProject.getRepairCompany());
            }
            projectMapper.updateProjectBaseInfor(project);

            // publish ProjectInsureCompanyChangedEvent or ProjectRepairCompanyChangedEvent for
            Project newProject = projectMapper.getById(project.getProjectId());
            if (!Objects.equals(
                    newProject.getInsuranceCompany(), oldProject.getInsuranceCompany())) {
                publisher.publishEvent(new ProjectInsureCompanyChangedEvent(this, newProject, oldProject));
            }
            if (!Objects.equals(newProject.getRepairCompany(), oldProject.getRepairCompany())) {
                publisher.publishEvent(new ProjectRepairCompanyChangedEvent(this, newProject, oldProject));
            }
            if (!Objects.equals(newProject.getLat(), oldProject.getLat())
                    || !Objects.equals(newProject.getLng(), oldProject.getLng())) {
                publisher.publishEvent(new ProjectAddressChangeEvent(this, newProject));
            } else {
                publisher.publishEvent(new ProjectChangeEvent(this, newProject));
            }
        } catch (Exception e) {
            logger.error(MessageCode.DATABASE_EXCEPTION, e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
        }
    }

    private boolean twoValueEquals(Object o1, Object o2) {
        if (o1 == o2) {
            return true;
        }
        if (o1 == null || o2 == null) {
            return false;
        }
        return o1.equals(o2);
    }

    private void arrangeWorkmanAndInfoHim(
            Project project, long arrangerId, Long exWorkerId, Long workerId, RoleEnum role, boolean sendEmailRequired)
            throws ServiceException {
        arrangeWorkmanAndInfoHim(project, arrangerId, exWorkerId, workerId, role, sendEmailRequired, (userId) -> userProvider.findUserById(String.valueOf(userId)));
    }

    private void arrangeWorkmanAndInfoHim(
        Project project, long arrangerId, Long exWorkerId, Long workerId, RoleEnum role, boolean sendEmailRequired, Function<Long, com.bees360.user.User> userCacheProvider)
        throws ServiceException {
        // if exWorkerId and workerId are both null
        //		or the value of exWorkerId and workerId aren't null but is equal, exWorker and worker is
        // a same person.
        boolean isSameWorker =
            (exWorkerId == null || workerId == null)
                ? twoValueEquals(exWorkerId, workerId)
                : exWorkerId.equals(workerId);
        if (isSameWorker) {
            return;
        }
        // Check if the worker need to be arranged or cancel arrangement of this role
        // Check if this worker has this kind of this role?
        if (exWorkerId == null) {
            // workerId will has a not null value
            com.bees360.user.User user = userCacheProvider.apply(workerId);
            User worker = UserAssemble.toWebUser(user);
            if (worker == null) {
                throw new ServiceException(MessageCode.USER_NOT_EXISTED);
            }
            UserTinyVo tinyWorker = new UserTinyVo(worker, role);
            // the worker must have this role
            if (!worker.hasRole(role)) {
                throw new ServiceException(MessageCode.USER_ROLE_NOT_EXIST);
            }
            assignAndInfo(project, arrangerId, tinyWorker, role, sendEmailRequired);
        } else {
            // UserTinyVo tinyExWorker = new UserTinyVo(exWorker, role);
            com.bees360.user.User user = userCacheProvider.apply(exWorkerId);
            if (user == null) {
                throw new IllegalStateException("User not found with id:" + exWorkerId);
            }
            UserTinyVo tinyExWorker = Bees360UserUtils.toUserTinyVo(user, role);
            if (workerId == null) {
                // cancel the assignment of this role member
                cancelAssignmentAndInfo(project, arrangerId, tinyExWorker, role);
            } else {
                // workerId will has a not null value
                user = userCacheProvider.apply(workerId);
                User worker = UserAssemble.toWebUser(user);
                if (worker == null) {
                    throw new ServiceException(MessageCode.USER_NOT_EXISTED);
                }
                UserTinyVo tinyWorker = new UserTinyVo(worker, role);

                // cancel the assignment of this role member
                cancelAssignmentAndInfo(project, arrangerId, tinyExWorker, role);
                // assign a new member for this role
                assignAndInfo(project, arrangerId, tinyWorker, role, sendEmailRequired);
            }
        }
    }

    /**
     * insert a new member with this role to the project, and then info him that he has been assign
     * as the role member of the project. Be careful, this method will insert this role user
     * directly without any judgement.
     *
     * @param project
     * @param arrangerId
     * @param worker the new member user
     * @param role the role of the member
     * @param sendEmailRequired
     * @throws ServiceException
     */
    private void assignAndInfo(Project project, long arrangerId, UserTinyVo worker, RoleEnum role, boolean sendEmailRequired)
            throws ServiceException {
        long workerId = worker.getUserId();
        long projectId = project.getProjectId();
        try {
            Member oldMember =
                    memberMapper.getMemberByRoleAndUserId(projectId, workerId, role.getCode());
            if (oldMember != null) {
                // the user used to be this role in the project, but was deleted.
                memberMapper.activeMember(projectId, workerId, role.getCode());
            } else {
                // arrange the user to be the role of the project
                Member newMember = new Member();
                newMember.setProjectId(projectId);
                newMember.setCreatedBy(arrangerId);
                newMember.setCreatedTime(DateUtil.getNow());
                newMember.setDeleted(false);
                newMember.setDescription("");
                newMember.setRole(role.getCode());
                newMember.setUserId(workerId);
                memberMapper.insert(newMember);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION, e);
        }

        if (RoleEnum.PILOT.equals(role)) {
            // Do not trigger project status changes here
            //			projectStatusService.changeOnWaitingForAcceptance(arrangerId,
            // project.getProjectId());
        } else {
            // info this worker that he has been assign as the role of the project
            // 新分配飞手调用新接口，用新的邮件模板，这里分配或者修改角色，非飞手才发送邮件
            if(sendEmailRequired) {
                messageService.infoMemberArranged(projectId, worker, role);
            }
            notificationService.notifyMemberArranged(projectId, worker, role);
            // postgres pilot member is not set by this method
            postgresMemberManagerAdapter.setMember(projectId, workerId, role.getCode(), arrangerId);
        }
    }

    /**
     * cancel the assignment of the role member, and then info him that He is no longer in the role.
     * Be careful, this method will delete this role user directly without any judgement.
     *
     * @param project
     * @param arrangerId
     * @param worker
     * @param role
     * @throws ServiceException
     */
    private void cancelAssignmentAndInfo(
            Project project, long arrangerId, UserTinyVo worker, RoleEnum role)
            throws ServiceException {
        long projectId = project.getProjectId();
        try {
            memberMapper.delete(projectId, worker.getUserId(), role.getCode());
        } catch (Exception e) {
            logger.error(
                    "Fail to delete "
                            + role.getDisplay()
                            + " member "
                            + worker.getUserId()
                            + " from project "
                            + projectId);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION, e);
        }
        // Is the status of this project to be confirmed by the pilot?
        boolean isPending =
                role == RoleEnum.PILOT
                        && project.getProjectStatus()
                                == NewProjectStatusEnum.PENDING_ACCEPTANCE.getCode();

        if (role == RoleEnum.PILOT) {
            if (!isPending) {
                publisher.publishEvent(
                        new ProjectCancelPilotEvent(this, projectId, worker.getUserId()));
            }
        } else {
            // postgres pilot member is not removed by this method
            postgresMemberManagerAdapter.remove(projectId, role.getRoleId(), arrangerId);
        }
        if (!isPending) {
            messageService.infoMemberArrangementCancel(project, worker, role);
            notificationService.notifyMemberArrangementCancel(project, worker, role);
        }
    }

    @Override
    public List<HistoryLogVo> listEventHistory(long projectId) throws ServiceException {
        List<HistoryLogVo> histories = new ArrayList<HistoryLogVo>();
        histories = eventHistoryService.listHistoryLogs(projectId);
        return histories;
    }

    @Override
    public ProjectAbstractVo getAbstractById(long projectId) throws ServiceException {
        ProjectAbstractVo project = null;
        try {
            project = projectMapper.getAbstractById(projectId);
        } catch (Exception ex) {
            logger.error(MessageCode.DATABASE_EXCEPTION, ex);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
        }
        return project;
    }

    /**
     * Only for openapi temporarily
     */
    @Override
    public List<ProjectTinyVo> searchProjectsForOpenapi(
        long userId, ProjectSearchOption searchOption, ProjectSearchResultFilter filter) throws ServiceException {
        User user = userService.getUserById(userId);
        setProjectSearchOptionBase(user, searchOption);

        Map<String, Object> searchOptionMap = searchOption.toMap();
        searchOptionMap.put("userId", userId);
        boolean self = (boolean) searchOptionMap.get("self");
        if (self) {
            searchOptionMap.put("curUserId", userId);
        }
        preProcessSearchMap(searchOptionMap);

        return projectMapper.listProjectsForOpenapi(searchOptionMap);
    }

    @Override
    public List<ProjectTinyVo> getProjects(ProjectSearchOption options) {
        Map<String, Object>  map = options.toMap();
        preProcessSearchMap(map);
        return projectMapper.listTinyProjectWithSearch(map);
    }

    @Override
    public ProjectPageResultVo pageTinyProjectsWithSearch(
            long userId, ProjectSearchOption searchOption) throws ServiceException {

        ProjectSearchResultFilter filter = ProjectSearchResultFilter.allInclude();
        filter.setWithMainImage(false);
        filter.setWithHiveDistance(false);
        return pageTinyProjectSearchWithFilter(userId, searchOption, filter);
    }

    @Override
    public ProjectPageResultVo pageTinyProjectSearchWithFilter(
            long userId, ProjectSearchOption searchOption, ProjectSearchResultFilter filter)
            throws ServiceException {

        int pageIndex = searchOption.getPageIndex();
        int pageSize = searchOption.getPageSize();
        var sortOrder = searchOption.getSortOrder();
        var sortKey = searchOption.getSortKey();

        Set<String> searchTags = searchOption.getSearchTag();
        if (searchOption.getProjectIdStart() == null
            && ignoreProjectIdEnd != null
            && searchTags != null
            && searchTags.contains(ProjectSearchOption.SearchTagEnum.RUSH_FOLDER.name())) {
            searchOption.setProjectIdStart(ignoreProjectIdEnd + 1);
        }

        if (searchTags != null && searchTags.contains(SearchTagEnum.FOLLOW_UP.name())) {
            searchOption.setProjectIds(getProjectIdsOfFollowUp());
        }

        if (pageIndex <= 0 || pageSize <= 0) {
            return new ProjectPageResultVo(new ArrayList(), Pagination.getEmpty(0, 0));
        }

        if (checkAndSetChangeReasonCondition(searchOption)) {
            return new ProjectPageResultVo(new ArrayList(), Pagination.getEmpty(0, 0));
        }

        User user = userService.getUserById(userId);
        setProjectSearchOptionBase(user, searchOption);
        // if the role of user is PILOT only, the the projects of PENDING_ACCEPTANCE will not be
        // selected.
        if (user.hasRole(RoleEnum.PILOT) && user.listRoles().size() == 1) {
            searchOption.setProjectExcludeStatusList(
                    Collections.singletonList(NewProjectStatusEnum.PENDING_ACCEPTANCE.getCode()));
        }
        Map<String, Object> searchOptionMap = searchOption.toMap();
        searchOptionMap.put("userId", userId);
        boolean self = (boolean) searchOptionMap.get("self");
        if (self) {
            // curUserId limits access to only projects in which the user has participated
            searchOptionMap.put("curUserId", userId);
        }
        preProcessSearchMap(searchOptionMap);
        setOperationsManagerMemberSearch(searchOption, searchOptionMap);

        searchOption.intersectProjectIds(Optional.ofNullable(searchOption.getProjectTags())
            .map(this::findProjectIdByTags).orElse(null));

        searchOption.intersectProjectIds(Optional.ofNullable(searchOption.getContactRecordType())
            .map(t -> findProjectIdByContactRecord(t, searchOption.getContactRecordTimes()))
            .orElse(null));

        searchOption.intersectProjectIds(
                Optional.ofNullable(searchOption.getContactInitiator())
                        .map(
                                t ->
                                        findProjectIdByContactRecord(
                                                searchOption.getContactInitiator(),
                                                searchOption.getContactRecipient(),
                                                searchOption.getContactRecordTimes()))
                        .orElse(null));

        searchOption.intersectProjectIds(
            Optional.ofNullable(searchOption.getProjectStateList())
                .map(this::findProjectIdByProjectState)
                .orElse(null));

        searchOption.intersectProjectIds(
            findProjectIdByNewDaysOld(
                searchOption.getNewDaysOldStart(), searchOption.getNewDaysOldEnd()));

        searchOption.intersectProjectIds(
            Optional.ofNullable(searchOption.getProjectStateChangeReasons())
                .map(this::findProjectIdByChangeReason)
                .orElse(null));

        searchOption.intersectProjectIds(
            Optional.ofNullable(searchOption.getProjectStateChangeReasonIds())
                .map(this::findProjectIdByChangeReasonId)
                .orElse(null)
        );

        searchOption.intersectProjectIds(
                findProjectIdByMember(searchOption.getMemberRoles(), searchOption.getVia()));

        List<Integer> projectStatusList = searchOption.getProjectStatusList();
        if (!CollectionUtils.isEmpty(projectStatusList)) {
            if (ObjectUtils.allNotNull(searchOption.getProjectStatusStartTime(), searchOption.getProjectStatusEndTime())) {
                List<Long> projectIds = projectStatusService.listProjectByStatusListAndTime(
                        projectStatusList,
                        searchOption.getProjectStatusStartTime(),
                        searchOption.getProjectStatusEndTime());
                searchOption.intersectProjectIds(projectIds);
                // disable project status filter
                searchOption.setProjectStatusList(null);
                searchOptionMap.put("projectStatusList", null);
            }
        }
        if (searchOption.getProjectIds() != null && searchOption.getProjectIds().isEmpty()) {
            // return empty list since the project ids is required filter and its size is 0.
            return new ProjectPageResultVo(new ArrayList(), Pagination.getEmpty(pageIndex, pageSize));
        }
        searchOptionMap.put("projectIds", searchOption.getProjectIds());

        if (!CollectionUtils.isEmpty(searchOption.getOverdueExcludeTagIds())) {
            searchOptionMap.put("overdueInsuranceCompany", companyIDMap.getSwyfft_Underwriting());
            searchOptionMap.put("overdueExcludeTagIds", searchOption.getOverdueExcludeTagIds());
        }

        if (!user.hasAnyRole(RoleEnum.ADMIN, RoleEnum.COMPANY_ADMIN)) {
            searchOptionMap.put("excludeStatus", ProcessStatusEnum.CANCELED.getCode());
        }

        if (!user.hasRole(RoleEnum.ADMIN)) {
            searchOptionMap.put("deletedInclusion", false);
        }

        var airspaceStatuses = searchOption.getAirspaceStatuses();
        if (airspaceStatuses != null && !airspaceStatuses.isEmpty()) {
            // Remove all null values in airspaceStatuses
            if (airspaceStatuses.removeAll(Lists.newArrayList(null, "null"))) {
                searchOptionMap.put("isAirspaceStatusesContainsNull", true);
            }
            searchOptionMap.put("airspaceStatuses", searchOption.getAirspaceStatuses());
        }

        var projectIds =
                projectMapper.listProjectIdWithSearch(searchOptionMap).stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());


        Map<Long, Integer> daysOldMap = null;
        if (Objects.equals(sortKey, NEW_DAYS_OLD_KEY)) {
            daysOldMap =
                    daysOldProvider.findProjectDaysOld(projectIds).entrySet().stream()
                            .collect(
                                    Collectors.toMap(
                                            e -> Long.parseLong(e.getKey()), Map.Entry::getValue));
            searchOptionMap = preProcessProjectDaysOld(
                    daysOldMap, sortOrder, pageSize, pageIndex);
        }

        List<ProjectTinyVo> tinyProjects = projectMapper.listTinyProjectWithSearch(searchOptionMap);

        assembleSearchProjects(tinyProjects, userId, filter, searchOption.getVia());

        if (daysOldMap == null) {
            // find days old by result project id
            var ids =
                    tinyProjects.stream()
                            .map(p -> String.valueOf(p.getProjectId()))
                            .collect(Collectors.toList());
            daysOldMap =
                    daysOldProvider.findProjectDaysOld(ids).entrySet().stream()
                            .collect(
                                    Collectors.toMap(
                                            e -> Long.parseLong(e.getKey()), Map.Entry::getValue));
        }

        tinyProjects = setProjectNewDaysOld(tinyProjects, daysOldMap, sortKey, sortOrder);

        addExternalAdjusterInfo(tinyProjects);

        Pagination page = new Pagination(pageIndex, pageSize, projectIds.size());

        return new ProjectPageResultVo(tinyProjects, page);
    }

    /**
     * If both change_reason and change_reason_group filter conditions are not empty,
     * Then when all the reasons contained in change_reason_group do not intersect with change_reason, it should return empty.
     *
     * @param searchOption param
     * @return true: return empty; false: query ES
     */
    private boolean checkAndSetChangeReasonCondition(ProjectSearchOption searchOption) {
        var changeReasons = searchOption.getProjectStateChangeReasons();
        var changeReasonIds = getChangeReasonIdByDisplayText(changeReasons);
        if (CollectionUtils.isNotEmpty(changeReasons) && CollectionUtils.isEmpty(changeReasonIds)) {
            // The searched displayText does not exist: return empty project list
            return true;
        }
        searchOption.setProjectStateChangeReasons(null);

        var groupReasonIds = getChangeReasonIdByGroup(searchOption.getChangeReasonGroups());

        if (CollectionUtils.isEmpty(changeReasonIds) && CollectionUtils.isEmpty(groupReasonIds)) {
            return false;
        }

        if (CollectionUtils.isNotEmpty(changeReasonIds) ^ CollectionUtils.isNotEmpty(groupReasonIds)) {
            changeReasonIds.addAll(groupReasonIds);
            searchOption.setProjectStateChangeReasonIds(changeReasonIds);
            return false;
        }

        var intersection = CollectionUtils.intersection(changeReasonIds, groupReasonIds);
        if (CollectionUtils.isNotEmpty(intersection)) {
            searchOption.setProjectStateChangeReasonIds(new HashSet<>(intersection));
            return false;
        }
        // All reasons contained in change_reason_group do not intersect with change_reason
        return true;
    }

    private Set<String> getChangeReasonIdByDisplayText(Set<String> changeReasons) {
        var changeReasonIds = new HashSet<String>();
        if (CollectionUtils.isNotEmpty(changeReasons)) {
            changeReasonIds.addAll(Iterables.toStream(projectStateChangeReasonManager.findByQuery(List.of(), List.of(), changeReasons))
                .map(ProjectStateChangeReason::getId).collect(Collectors.toList()));
        }
        return changeReasonIds;
    }

    private Set<String> getChangeReasonIdByGroup(Set<String> changeReasonGroups) {
        var groupReasonIds = new HashSet<String>();
        if (CollectionUtils.isNotEmpty(changeReasonGroups)) {
            for (String changeReasonGroup : changeReasonGroups) {
                var reasonIds = Iterables.toStream(changeReasonFromCategoryProvider.findChangeReasonByGroup(changeReasonGroup))
                    .map(ProjectStateChangeReason::getId)
                    .collect(Collectors.toList());
                groupReasonIds.addAll(reasonIds);
            }
        }
        return groupReasonIds;
    }

    /**
     * After finding the projectId and daysOld that meet the conditions according to the query conditions, filter out the projects that meet the conditions according to the paging information, and use the id that meets the conditions as the query conditions again.
     *
     * @param daysOldMap The final filtered projectId, map of daysOld
     * @param sortOrder sorting order
     */
    private Map<String, Object> preProcessProjectDaysOld(
            Map<Long, Integer> daysOldMap, String sortOrder, int pageSize, int pageIndex) {
        Comparator<? super Map.Entry<Long, Integer>> sortFunc = Map.Entry.comparingByValue();
        if (Objects.equals(sortOrder, "descending")) {
            sortFunc = sortFunc.reversed();
        }
        var resultMap =
                daysOldMap.entrySet().stream()
                        .sorted(sortFunc)
                        .skip((long) pageSize * (pageIndex - 1))
                        .limit(pageSize)
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        Map<String, Object> searchOptionMap = new HashMap<>();
        searchOptionMap.put("projectIds", resultMap.keySet());
        searchOptionMap.put("startIndex", 1);
        searchOptionMap.put("pageSize", pageSize);
        return searchOptionMap;
    }

    private void setOperationsManagerMemberSearch(ProjectSearchOption searchOption, Map<String, Object> searchMap) {
        if (CollectionUtils.isEmpty(searchOption.getOperationsManagerList())) {
            return;
        }

        List<Long> userList = new ArrayList<>();
        searchOption.getOperationsManagerList().forEach(
            id -> {
                var user = userProvider.findUserById(id);
                Optional.ofNullable(UserAssemble.toWebUser(user))
                    .ifPresent(u -> userList.add(u.getUserId()));
            }
        );

        searchMap.put("operationsManagerList", userList);
    }

    private void addExternalAdjusterInfo(List<ProjectTinyVo> tinyProjects) {
        tinyProjects.forEach(proj -> {

            String externalAdjuster = Streams.stream(contactManager.findByProjectId(String.valueOf(proj.getProjectId())))
                .filter(contact -> EXTERNAL_ADJUSTER.equals(contact.getRole()))
                .map(Contact::getFullName)
                .findAny()
                .orElse("");

            proj.setExternalAdjusterName(externalAdjuster);
        });

    }

    private void fillCatLevel(List<ProjectTinyVo> tinyProjects) {
        if (CollectionUtils.isEmpty(tinyProjects) || !bees360FeatureSwitch.isEnableExportCatLevel()) {
            return;
        }
        Iterable<String> projectIds = Iterables.transform(tinyProjects, p -> Long.toString(p.getProjectId()));
        Map<String, ClaimCatastrophe> projectIdToCat = projectCatastropheManager.findByProjectIds(projectIds);

        tinyProjects.forEach(p -> {
            Optional.ofNullable(projectIdToCat.get(Long.toString(p.getProjectId())))
                .ifPresent(cat -> p.setCatLevel(cat.getLevel()));
        });
    }

    /**
     * Find the projectId of the user and the corresponding role, and find the intersection if there are multiple projects
     */
    private List<Long> findProjectIdByMember(List<ProjectSearchOption.MemberRole> list, String via) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<String> projectList = new ArrayList<>();
        // it will retain only the elements that are common to both projectList and temp.
        for (ProjectSearchOption.MemberRole memberRole : list) {
            if (StringUtils.isAnyBlank(memberRole.getUserId(), memberRole.getRole())) {
                continue;
            }
            List<String> temp;
            var role = com.bees360.project.member.RoleEnum.valueOf(memberRole.getRole());
            if ("IO".equals(via)) {
                temp =
                        Iterables.toList(
                                memberManager.findProjectIdByRoleAndUserId(
                                        memberRole.getUserId(), role));
            } else {
                temp =
                        memberMapper
                                .listProjectIdWithActiveMember(
                                        role.getCode(), Long.parseLong(memberRole.getUserId()))
                                .stream()
                                .map(String::valueOf)
                                .collect(Collectors.toList());
            }
            if (projectList.isEmpty()) {
                projectList.addAll(temp);
            } else {
                projectList.retainAll(temp);
            }
        }
        return projectList.stream().map(Long::parseLong).collect(Collectors.toList());
    }

    private List<Long> findProjectIdByContactRecord(Integer contactRecordType, Integer times) {
        var type = com.bees360.contact.Message.ContactRecordMessage.ContactRecordType.forNumber(contactRecordType);
        var from = ContactRecordUtil.getContactFrom(type);
        var to = ContactRecordUtil.getContactTo(type);

        // Search all if the user does not enter the number of times
        var realTimes = Optional.ofNullable(times).orElse(-1);
        return findProjectIdByContactRecord(from, to, realTimes);
    }

    private List<Long> findProjectIdByContactRecord(
            String contactInitiator, String contactRecipient, Integer times) {
        Preconditions.checkArgument(
                Objects.nonNull(contactInitiator) || Objects.nonNull(contactRecipient),
                "Contact initiator and contact recipient shouldn't be both null.");
        // Search all if the user does not enter the number of times
        var realTimes = Optional.ofNullable(times).orElse(-1);
        return Iterables.toStream(
                        contactRecordManager.findProjectByContactRecordQuery(
                                contactInitiator, contactRecipient, realTimes))
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    private List<Long> findProjectIdByProjectState(
            List<com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum>
                    projectStateList) {
        var ids = projectStateProvider.findProjectByProjectState(projectStateList);
        return Iterables.toStream(ids).map(Long::parseLong).collect(Collectors.toList());
    }

    private List<Long> findProjectIdByNewDaysOld(
            @Nullable Integer newDaysOldStart, @Nullable Integer newDaysOldEnd) {
        if (newDaysOldStart == null && newDaysOldEnd == null) {
            return null;
        }
        return Iterables.toStream(
                        daysOldProvider.findProjectByDaysOld(newDaysOldStart, newDaysOldEnd))
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    private List<Long> findProjectIdByChangeReason(Iterable<String> projectStateChangeReasons) {
        return Iterables.toStream(
                        stateChangeReasonManager.findProjectByStateChangeReason(
                                projectStateChangeReasons))
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    private List<Long> findProjectIdByChangeReasonId(Iterable<String> changeReasonIds) {
        if (CollectionUtils.isEmpty(Iterables.toList(changeReasonIds))) {
            return null;
        }
        return projectStateMapper.findProjectIdByChangeReasonIds(Iterables.toList(changeReasonIds));
    }

    private List<Long> findProjectIdByTags(Iterable<String> tagIds) {
        if (com.google.common.collect.Iterables.isEmpty(tagIds)) {
            return List.of();
        }
        var projectIds = projectTagManager.findAllProjectIdByTagId(tagIds)
            .stream().map(Long::parseLong).collect(Collectors.toList());
        return projectIds;
    }

    private void assembleSearchProjects(
            List<ProjectTinyVo> tinyProjects,
            long userId,
            ProjectSearchResultFilter filter,
            String via)
            throws ServiceException {
        if (CollectionUtils.isEmpty(tinyProjects)) {
            return;
        }
        List<Long> projectIds = new ArrayList<Long>();
        for (ProjectTinyVo project : tinyProjects) {
            project.setInspectionTypeList(
                    InspectionTypeEnum.listDisplays(project.getInspectionTypes()));
            projectIds.add(project.getProjectId());
        }

        if (filter.isWithCreator()) {
            setCreatorForProjects(tinyProjects, projectIds);
        }
        if (filter.isWithMainImage()) {
            setMainImageForProjects(tinyProjects, projectIds);
        }
        if (filter.isWithPilot()) {
            setPilotForProjects(tinyProjects, projectIds);
        }
        if (filter.isWithProcessBy()) {
            setProcessedByForProjects(tinyProjects, projectIds);
        }
        if (filter.isWithStatus()) {
            setProjectStatuses(tinyProjects, projectIds);
        }

        if (filter.isWithRoles()) {
            setRolesForProjects(tinyProjects, userId);
        }
        setProjectPilotBatchInfo(tinyProjects);
        //        setProjectRiskScore(tinyProjects);

        setProjectLabelInfo(tinyProjects);

        setProjectCreatorCompany(tinyProjects);

        setInsuranceCompany(tinyProjects);

        setProjectLatestStatus(tinyProjects);

        setFeedbackMessage(tinyProjects);

        setNumberOfOutbuildingsAndInteriorRooms(tinyProjects);

        setMember(tinyProjects, via);

        if ("IO".equals(via)) {
            var similarProjectMap = findSimilarProjects(tinyProjects);
            setSimilarProjects(tinyProjects, similarProjectMap);
            setSimilarProjectTypes(tinyProjects, similarProjectMap);
        }

        setTimeZone(tinyProjects);

        setAddressAirspace(tinyProjects);

        setProjectAirspace(tinyProjects);

        if (filter.isWithProjectTag()) {
            setProjectTags(tinyProjects, via);
        }
        if (filter.isWithHiveDistance()) {
            setHiveLocation(tinyProjects);
        }

        setProjectStateChangeReason(tinyProjects);
        setChangeReasonGroup(tinyProjects);
    }

    @Deprecated
    private void setAddressAirspace(List<ProjectTinyVo> tinyVos) {
        if (CollectionUtils.isEmpty(tinyVos)) {
            return;
        }

        var addressIds =
            tinyVos.stream()
                .map(ProjectTinyVo::getAddressId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        // Get Airspace through AddressAirspaceMapper
        List<AddressAirspace> airspaceList = addressAirspaceMapper.getByAddressIds(addressIds);
        if (airspaceList == null || airspaceList.isEmpty()) {
            return;
        }
        var addressAirspaceMap = airspaceList.stream()
            .collect(Collectors.toMap(AddressAirspace::getAddressId, airspace -> airspace));
        tinyVos.forEach(p -> {
            if (p.getAddressId() == null) {
                return;
            }
            var airspace = addressAirspaceMap.get(p.getAddressId());
            if (Objects.nonNull(airspace)) {
                AddressAirspace airspaceToReturn = new AddressAirspace();
                airspaceToReturn.setStatus(airspace.getStatus());
                airspaceToReturn.setHeightCeiling(airspace.getHeightCeiling());

                p.setAirspace(airspaceToReturn);
            }
        });
    }

    private void setProjectAirspace(List<ProjectTinyVo> tinyVos) {
        if (CollectionUtils.isEmpty(tinyVos)) {
            return;
        }

        var projectIds = tinyVos.stream()
                .map(p -> String.valueOf(p.getProjectId()))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        // Get Airspace through ProjectAirspaceManager
        List<ProjectAirspace> airspaceList = projectAirspaceMapper.getByProjectIds(projectIds);
        if (airspaceList == null || airspaceList.isEmpty()) {
            return;
        }
        var projectId2AirspaceMap =
                airspaceList.stream().collect(Collectors.toMap(ProjectAirspace::getProjectId, airspace -> airspace));
        tinyVos.forEach(p -> {
            var airspace = projectId2AirspaceMap.get(String.valueOf(p.getProjectId()));
            if (Objects.nonNull(airspace)) {
                ProjectAirspace airspaceToReturn = new ProjectAirspace();
                airspaceToReturn.setStatus(airspace.getStatus());
                airspaceToReturn.setHeightCeiling(airspace.getHeightCeiling());

                p.setProjectAirspace(airspaceToReturn);
            }
        });
    }

    /**
     * Set StateChangeReason to prevent the MySQL project_state table from needing to be fully refreshed once the display text is renamed
     *
     * @param tinyVos project returned by mysql
     */
    private void setProjectStateChangeReason(List<ProjectTinyVo> tinyVos) {
        var changeReasonIds = tinyVos.stream().map(ProjectTinyVo::getProjectStateChangeReasonId)
            .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        var changeReasonIdToDisplayText = getChangeReasonIdToDisplayText(changeReasonIds);
        tinyVos.forEach(
            project -> project.setProjectStateChangeReason(
                changeReasonIdToDisplayText.getOrDefault(project.getProjectStateChangeReasonId(), project.getProjectStateChangeReason())
            ));
    }

    private Map<String, String> getChangeReasonIdToDisplayText(Iterable<String> changeReasonIds) {
        return Iterables.toStream(projectStateChangeReasonManager.findByQuery(changeReasonIds, List.of(), List.of()))
            .collect(Collectors.toMap(ProjectStateChangeReason::getId, ProjectStateChangeReason::getDisplayText));
    }

    private void setChangeReasonGroup(List<ProjectTinyVo> tinyVos) {
        var reasonGroups = Iterables.toList(changeReasonFromCategoryProvider.listReasonGroup());
        var reasonIdToGroup = new HashMap<String, String>();
        for (String reasonGroup : reasonGroups) {
            Iterables.toStream(changeReasonFromCategoryProvider.findChangeReasonByGroup(reasonGroup)).forEach(e -> reasonIdToGroup.put(e.getId(), reasonGroup));
        }
        tinyVos.forEach(project -> project.setChangeReasonGroup(reasonIdToGroup.get(project.getProjectStateChangeReasonId())));
    }

    private void setTimeZone(List<ProjectTinyVo> tinyVos) {
        if (CollectionUtils.isEmpty(tinyVos) || !featureSwitch.isEnableFillTimeZoneWhenGetProject()) {
            return;
        }
        var addressIds =
                tinyVos.stream()
                        .map(ProjectTinyVo::getAddressId)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
        if (addressIds.isEmpty()) {
            return;
        }
        var address = addressManager.findAllById(addressIds);
        var addressIdTimeZoneIdMap =
                Iterables.toStream(address)
                        .filter(a -> a.getTimeZone() != null)
                        .collect(
                                Collectors.toMap(
                                        com.bees360.address.Address::getId,
                                        a -> a.getTimeZone().getID(),
                                        (k1, k2) -> k1));
        for (var project : tinyVos) {
            var addressId = project.getAddressId();
            if (addressIdTimeZoneIdMap.containsKey(addressId)) {
                project.setTimeZone(addressIdTimeZoneIdMap.get(addressId));
            }
        }
    }

    private Map<String, Map<String, List<com.bees360.project.Message.ProjectSimilarType>>> findSimilarProjects
        (List<ProjectTinyVo> tinyVos) {
        var projectIdList =
            tinyVos.stream()
                .map(vo -> String.valueOf(vo.getProjectId()))
                .collect(Collectors.toList());
        return similarProjectProvider.getSimilarProjectsByProjectId(projectIdList);
    }

    private void setSimilarProjects(
        List<ProjectTinyVo> tinyVos,
        Map<String, Map<String, List<com.bees360.project.Message.ProjectSimilarType>>> similarProjectMap) {
        var expectedSimilarTypes = List.of(
            com.bees360.project.Message.ProjectSimilarType.SERVICE_TYPE,
            com.bees360.project.Message.ProjectSimilarType.CREATION_DATE);
        tinyVos.forEach(
                project -> {
                    var projectId = String.valueOf(project.getProjectId());
                    var similarProjects = similarProjectMap.get(projectId);
                    project.setSimilarProjects(
                            Optional.ofNullable(similarProjects)
                                    .map(similarMap -> similarMap.entrySet().stream()
                                        .filter(
                                            entry -> new HashSet<>(entry.getValue()).containsAll(expectedSimilarTypes))
                                        .map(Map.Entry::getKey)
                                        .collect(Collectors.toList()))
                                    .orElse(Collections.emptyList()));
                });
    }

    private void setSimilarProjectTypes(
        List<ProjectTinyVo> tinyVos,
        Map<String, Map<String, List<com.bees360.project.Message.ProjectSimilarType>>> similarProjectMap) {
        tinyVos.forEach(
            project -> {
                var projectId = String.valueOf(project.getProjectId());
                project.setSimilarProjectTypes(similarProjectMap.getOrDefault(projectId, Map.of()));
            }
        );
    }

    private List<ProjectTinyVo> setProjectNewDaysOld(
            List<ProjectTinyVo> tinyVos, Map<Long, Integer> daysOldMap, String sortKey, String sortOrder) {
        logger.debug(
                "Start to set new days old with days old map : "
                        + daysOldMap
                        + " and sort order : "
                        + sortOrder);

        // if new days old map is not empty, should sort the list by new days old
        Comparator<ProjectTinyVo> comparator =
                Comparator.comparing(p -> daysOldMap.getOrDefault(p.getProjectId(), 0));
        if (Objects.equals(sortOrder, "desc")) {
            comparator = comparator.reversed();
        }
        if (Objects.equals(sortKey, NEW_DAYS_OLD_KEY)) {
            tinyVos = tinyVos.stream().sorted(comparator).collect(Collectors.toList());
        }
        tinyVos.forEach(p -> p.setNewDaysOld(daysOldMap.getOrDefault(p.getProjectId(), 0)));
        return tinyVos;
    }

    private void setNumberOfOutbuildingsAndInteriorRooms(List<ProjectTinyVo> tinyVos) {
        tinyVos.forEach(
            project ->
                Optional.ofNullable(projectIIManager.findById(String.valueOf(project.getProjectId())))
                        .ifPresent(projectII -> {
                            project.setNumberOfOutbuildings(projectII.getNumberOfOutbuildings());
                            project.setNumberOfInteriorRooms(projectII.getNumberOfInteriorRooms());
                            }
                        )
            );
    }

    private void setMember(List<ProjectTinyVo> tinyVos, String via) {
        if (!"IO".equals(via)) {
            setWebMember(tinyVos);
            return;
        }
        var projectList =
                tinyVos.stream()
                        .map(ProjectTinyVo::getProjectId)
                        .map(String::valueOf)
                        .collect(Collectors.toList());
        var map = memberManager.findByProjectIds(projectList);
        tinyVos.forEach(
                project -> {
                    var members = map.get(String.valueOf(project.getProjectId()));
                    project.setMembers(IterableUtils.toList(members));
                });
    }

    private void setWebMember(List<ProjectTinyVo> tinyVos) {
        var projectList =
                tinyVos.stream().map(ProjectTinyVo::getProjectId).map(String::valueOf).collect(Collectors.toList());
        var projectMapMembers = memberManager.findByProjectIds(projectList);
        tinyVos.forEach(
                project -> {
                    project.setMembers(Iterables.toList(projectMapMembers.get(project.getProjectId() + "")));
                });
    }

    private void setHiveLocation(List<ProjectTinyVo> tinyVos) {
        tinyVos.forEach(
                project -> {
                    if (StringUtils.isNotBlank(project.getAddressId())) {
                        var hiveDistance =
                                addressHiveLocationProvider.getHiveLocationDistance(
                                        project.getAddressId());
                        if (hiveDistance != null) {
                            project.setHiveDistance(hiveDistance.getDistanceMiles());
                        }
                    }
                });
    }

    /**
     * Assembly label
     * @param tinyVos project
     * @param via entry: WEB or IO
     */
    private void setProjectTags(List<ProjectTinyVo> tinyVos, String via) {
        if("IO".equals(via)){
            setProjectTagsIO(tinyVos);
        }else {
            setProjectTagsWEB(tinyVos);
        }
    }

    private void setProjectTagsIO(List<ProjectTinyVo> tinyVos) {
        // The io end obtains the bees360 tag
        tinyVos.stream()
                .filter(e -> Objects.nonNull(e.getClaimType()))
                .forEach(
                        project -> {
                            Message.ProjectTagType type =
                                    ClaimTypeEnum.isClaim(project.getClaimType())
                                            ? Message.ProjectTagType.CLAIM
                                            : Message.ProjectTagType.UNDERWRITING;
                            var projectTags =
                                    projectTagManager.findByProjectId(
                                            String.valueOf(project.getProjectId()),
                                            type,
                                            BEES360_COMPANY_ID);
                            project.setProjectTags(projectTags);
                        });
    }

    private void setProjectTagsWEB(List<ProjectTinyVo> tinyVos) {
        var projectIds =
                tinyVos.stream()
                        .map(ProjectTinyVo::getProjectId)
                        .map(String::valueOf)
                        .collect(Collectors.toList());
        Map<String, List<? extends ProjectTag>> map =
                projectTagManager.findAllByProjectIdAndType(
                        projectIds, Message.ProjectTagType.UNKNOWN);
        tinyVos.forEach(
                project -> {
                    var companyId =
                            project.getInsuranceCompany() == null
                                    ? null
                                    : String.valueOf(project.getInsuranceCompany());
                    List<? extends ProjectTag> list =
                            map
                                    .getOrDefault(String.valueOf(project.getProjectId()), List.of())
                                    .stream()
                                    .filter(tag -> Objects.equals(tag.getCompanyId(), companyId))
                                    .collect(Collectors.toList());
                    if (list.size() > 0) {
                        var latestTag =
                                list.stream()
                                        .max(Comparator.comparing(ProjectTag::getAddedAt))
                                        .get();
                        var user = userProvider.getUser(latestTag.getAddedBy());
                        project.setLatestModifyTagUserName(user.getName());
                        project.setLatestModifyTagTime(latestTag.getAddedAt().toEpochMilli());
                    }
                    project.setProjectTags(list);
                });
    }

    private void setFeedbackMessage(List<ProjectTinyVo> tinyVos) {

        List<Long> projectIds =
                tinyVos.stream().map(ProjectTinyVo::getProjectId).collect(Collectors.toList());

        List<ProjectMessage> projectMessages =
                projectMessageService.listMessage(
                        ProjectMessageQuery.builder()
                                .isDeleted(false)
                                .projectIds(projectIds)
                                .type(ProjectMessageTypeEnum.PILOT_FEEDBACK.getCode())
                                .build());

        if (CollectionUtils.isEmpty(projectMessages)) {
            tinyVos.forEach(vo -> vo.setProjectMessage(Collections.emptyList()));
        }

        Map<Long, List<ProjectMessageVo>> groupMessageVo =
                projectMessages.stream()
                        .collect(
                                Collectors.groupingBy(
                                        ProjectMessage::getProjectId,
                                        Collectors.mapping(
                                                projectMessage -> {
                                                    ProjectMessageVo projectMessageVo =
                                                            new ProjectMessageVo();
                                                    projectMessageVo.setContent(
                                                            projectMessage.getContent());
                                                    projectMessageVo.setCreateTime(
                                                            projectMessage.getCreateTime());
                                                    projectMessageVo.setTitle(
                                                            projectMessage.getTitle());
                                                    projectMessageVo.setSender(
                                                            projectMessage
                                                                    .getSenderId()
                                                                    .toString());
                                                    return projectMessageVo;
                                                },
                                                Collectors.toList())));
        tinyVos.forEach(
                vo -> {
                    vo.setProjectMessage(
                            groupMessageVo.getOrDefault(
                                    vo.getProjectId(), Collections.emptyList()));
                });
    }

    private void setProjectLabelInfo(List<ProjectTinyVo> tinyProjects) {

        final List<Long> projectIds =
                tinyProjects.stream().map(ProjectTinyVo::getProjectId).collect(Collectors.toList());
        List<BoundProjectLabel> boundProjectLabels =
                projectLabelService.projectLabelList(projectIds);
        if (CollectionUtils.isEmpty(boundProjectLabels)) {
            tinyProjects.forEach(
                    projectTinyVo -> projectTinyVo.setProjectLabels(Collections.emptyList()));
            return;
        }
        Map<Long, List<ProjectLabel>> projectLabelMap =
                boundProjectLabels.stream()
                        .collect(
                                Collectors.toMap(
                                        BoundProjectLabel::getProjectId,
                                        BoundProjectLabel::getProjectLabels));
        tinyProjects.forEach(
                projectTinyVo -> {
                    projectTinyVo.setProjectLabels(
                            projectLabelMap.getOrDefault(
                                    projectTinyVo.getProjectId(), Collections.emptyList()));
                });

        setDueStatus(tinyProjects);
    }

    private void setInsuranceCompany(List<ProjectTinyVo> tinyVos) {
        Set<Long> companyIds =
                tinyVos.stream()
                        .map(ProjectTinyVo::getInsuranceCompany)
                        .collect(Collectors.toSet());
        List<Company> companies = companyService.listIn(companyIds);
        if (CollectionUtils.isEmpty(companies)) {
            return;
        }
        Map<Long, Company> companyMap =
                companies.stream().collect(Collectors.toMap(Company::getCompanyId, c -> c));
        tinyVos.forEach(
                tinyVo -> {
                    Company company = companyMap.get(tinyVo.getInsuranceCompany());
                    if (company == null) {
                        return;
                    }
                    tinyVo.setInsuranceCompanyName(company.getCompanyName());
                    tinyVo.setInsuranceCompanyLogo(company.getLogo());
                });
    }

    private void setProjectCreatorCompany(List<ProjectTinyVo> tinyVos) {
        Set<Long> creatorIds =
                tinyVos.stream().map(ProjectTinyVo::getCreatedBy).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(creatorIds)) {
            return;
        }
        var creators = bifrostUserMapperAdapter.getUserById(creatorIds);
        Map<Long, Long> userCompanyMap =
                Iterables.toStream(creators)
                        .filter(user -> Objects.nonNull(user.getCompanyId()))
                        .collect(Collectors.toMap(User::getUserId, User::getCompanyId));
        if (MapUtils.isEmpty(userCompanyMap)) {
            return;
        }
        Set<Long> companyIds = new HashSet<>(userCompanyMap.values());
        List<Company> companies = companyService.listIn(companyIds);
        if (CollectionUtils.isEmpty(companies)) {
            return;
        }
        Map<Long, Company> companyMap =
                companies.stream().collect(Collectors.toMap(Company::getCompanyId, c -> c));
        for (ProjectTinyVo vo : tinyVos) {
            Long companyId = userCompanyMap.get(vo.getCreatedBy());
            if (companyId == null) {
                continue;
            }
            Company company = companyMap.get(companyId);
            if (company == null) {
                continue;
            }
            vo.setCreatorCompanyLogo(company.getLogo());
        }
    }

    private void setProjectLatestStatus(List<ProjectTinyVo> tinyVos) {
        Set<Long> projectIds = tinyVos.stream().map(ProjectTinyVo::getProjectId).collect(Collectors.toSet());
        Map<Long, Map<NewProjectStatusEnum, ProjectStatus>> map = projectStatusService.listProjectStatusLatestRecord(projectIds);
        tinyVos.forEach(
                p -> {
                    Map<NewProjectStatusEnum, ProjectStatus> statusMap = map.get(p.getProjectId());
                    if (statusMap != null) {
                        NewProjectStatusEnum status = NewProjectStatusEnum.getEnum(p.getProjectStatusCode());
                        Optional.ofNullable(statusMap.get(status)).ifPresent(projectStatus -> {
                            long createdTime = projectStatus.getCreatedTime();
                            p.setProjectStatusTime(createdTime);
                        });
                    }
                });
    }

    private void setDueStatus(List<ProjectTinyVo> projectTinyVos) {
        if (CollectionUtils.isEmpty(projectTinyVos)) {
            return;
        }
        Set<Long> companyIds =
                projectTinyVos.stream()
                        .map(ProjectTinyVo::getInsuranceCompany)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
        List<Company> companies = companyService.listIn(companyIds);

        Map<Long, Company> companyMap =
                Optional.ofNullable(companies)
                        .map(
                                m ->
                                        m.stream()
                                                .collect(
                                                        Collectors.toMap(
                                                                Company::getCompanyId, o -> o)))
                        .orElseGet(HashMap::new);

        projectTinyVos.forEach(
                pro -> {
                    Company company = companyMap.get(pro.getInsuranceCompany());
                    CompanyStatRule statRule =
                            Optional.ofNullable(company)
                                    .map(
                                            c ->
                                                    CompanyStatRuleConfig.getStatRule(
                                                            c.getCompanyName(),
                                                            pro.getServiceType()))
                                    .orElseGet(
                                            () ->
                                                    CompanyStatRuleConfig.getStatRule(
                                                            null, pro.getServiceType()));
                    pro.setDueSoon(statRule.isDueSoon(pro.getDaysOld()));
                    pro.setOverdue(statRule.isOverdue(pro.getDaysOld()));
                });
    }

    private void setProjectPilotBatchInfo(List<ProjectTinyVo> tinyProjects) {
        if (CollectionUtils.isEmpty(tinyProjects)) {
            return;
        }
        List<Long> projectIds =
                tinyProjects.stream().map(ProjectTinyVo::getProjectId).collect(Collectors.toList());
        List<BeesPilotBatchItemVo> batchItems =
                beesPilotBatchItemService.listByProjectIdsWithBatchInfo(projectIds);

        Map<Long, BeesPilotBatchItemVo> idBatchNoMap =
            batchItems.stream()
                .collect(
                    Collectors.toMap(
                        BeesPilotBatchItemVo::getProjectId,
                        o -> o,
                        (v1, v2) ->
                            v1.getCreatedAt().compareTo(v2.getCreatedAt()) >= 0 ? v1 : v2));

        var batches = beesPilotBatchProvider
            .findByProjectIds(
                projectIds.stream().map(String::valueOf).collect(Collectors.toList()));
        var batchMap = Iterables.toStream(batches)
                                .collect(
                                    Collectors.toMap(
                                        com.bees360.beespilot.batch.BeesPilotBatch::getBatchNo,
                                        Function.identity()));

        tinyProjects.forEach(
            pro -> Optional.ofNullable(idBatchNoMap.get(pro.getProjectId()))
                .ifPresent(o ->
                {
                    pro.setBatchNo(o.getBatchNo());
                    pro.setPlanPaymentDate(o.getPlanPaymentDate());
                    Optional.ofNullable(batchMap.get(pro.getBatchNo())).ifPresent(
                        batch -> {
                            var batchItem = Iterables.toStream(batch.getBatchItems())
                                .filter(b ->
                                    StringUtils.equals(b.getProjectId(), String.valueOf(pro.getProjectId())))
                                .findFirst();
                            var totalPay = batchItem.map(
                                    i -> {
                                        var basePay = new BigDecimal(String.valueOf(i.getBasePay()));
                                        var extraPay = new BigDecimal(String.valueOf(i.getExtraPay()));
                                        return basePay.add(extraPay);
                                    })
                                .orElse(new BigDecimal(0));
                            pro.setBatchTotalPay(totalPay.setScale(2, RoundingMode.HALF_UP));
                        }
                    );
                }));
    }

    /**
     * Determine whether the user has only the specified role.
     *
     * @param user
     * @param roleRequired Required role
     * @param rolesAllowed Other roles that can be possessed
     * @return
     */
    private static boolean hasOnlyRolesIn(
            User user, RoleEnum roleRequired, RoleEnum... rolesAllowed) {
        if (roleRequired != null && !user.hasRole(roleRequired)) {
            return false;
        }
        Set<RoleEnum> roleSet = new HashSet<>();
        roleSet.addAll(Arrays.asList(rolesAllowed));
        roleSet.add(roleRequired);

        for (RoleEnum role : user.listRoles()) {
            if (!roleSet.contains(role)) {
                return false;
            }
        }
        return true;
    }

    private void setCreatorForProjects(List<ProjectTinyVo> projects, List<Long> projectIds)
            throws AccessDatabaseException {
        if (projects.isEmpty()) {
            return;
        }
        Set<Long> creatorUserIds =
                projects.stream().map(p -> p.getCreatedBy()).collect(Collectors.toSet());
        if (creatorUserIds.isEmpty()) {
            return;
        }

        var users = bifrostUserMapperAdapter.getUserById(creatorUserIds);
        Map<Long, User> userIdMapUser = new HashMap<>();
        for (User user : users) {
            userIdMapUser.put(user.getUserId(), user);
        }
        for (ProjectTinyVo projectTinyVo : projects) {
            User user = userIdMapUser.get(projectTinyVo.getCreatedBy());
            if (user == null) {
                continue;
            }
            projectTinyVo.setCreatorName(user.getName());
            projectTinyVo.setCreatorEmail(user.getEmail());
            projectTinyVo.setCreatorPhone(user.getPhone());
        }
    }

    private void setMainImageForProjects(List<ProjectTinyVo> projects, List<Long> projectIds)
            throws AccessDatabaseException {
        if (null == projects || projects.isEmpty() || null == projectIds || projectIds.isEmpty()) {
            return;
        }
        List<ProjectImageTinyVoForApp> projectImageTinyVosForApp;
        try {
            List<Integer> fileSourceTypes = exhibitImageFileSourceTypes();
            projectImageTinyVosForApp =
                    projectMapper.listTinyProjectImageVoForApp(projectIds, fileSourceTypes);
        } catch (Exception e) {
            throw new AccessDatabaseException(
                    "Fail to list tiny projectImage for projects " + projectIds, e);
        }
        if (projectImageTinyVosForApp.isEmpty()) {
            return;
        }
        Map<Long, ProjectImageTinyVoForApp> projectImageMap =
                new HashMap<Long, ProjectImageTinyVoForApp>(projectIds.size());
        for (ProjectImageTinyVoForApp vo : projectImageTinyVosForApp) {
            if (!projectImageMap.containsKey(vo.getProjectId())) {
                if (null != vo.getFileNameLowerResolution()
                        && !vo.getFileNameLowerResolution().isEmpty()) {
                    projectImageMap.put(vo.getProjectId(), vo);
                }
            } else {
                if (vo.getFileSourceType() != FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode()
                        && projectImageMap.get(vo.getProjectId()).getFileSourceType()
                                == FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode()) {
                    projectImageMap.replace(vo.getProjectId(), vo);
                }
            }
        }
        for (ProjectTinyVo vo : projects) {
            if (projectImageMap.containsKey(vo.getProjectId())) {
                vo.setMainImage(
                        projectImageMap.get(vo.getProjectId()).getFileNameLowerResolution());
            }
        }
    }

    private void setPilotForProjects(List<ProjectTinyVo> tinyProjects, List<Long> projectIds) {
        if (tinyProjects == null || projectIds == null || projectIds.isEmpty()) {
            return;
        }
        Map<Long, ProjectTinyVo> projectMap = new HashMap<>(tinyProjects.size());
        for (ProjectTinyVo p : tinyProjects) {
            projectMap.put(p.getProjectId(), p);
            p.setPilot("");
        }
        List<Member> pilotMembers =
                memberMapper.listActiveMembersIn(projectMap.keySet(), RoleEnum.PILOT.getCode());
        if (pilotMembers.isEmpty()) {
            return;
        }
        Set<String> pilotUserIds =
                pilotMembers.stream().map(p -> p.getUserId() + "").collect(Collectors.toSet());
        Map<Long, String> pgUserIdMap = new HashMap<>();
        Map<Long, String> userNameMap = new HashMap<>();
        Iterables.toStream(userProvider.findUserById(pilotUserIds))
                .forEach(
                        u -> {
                            var webUser = UserAssemble.toWebUser(u);
                            var webUserId = webUser.getUserId();
                            var webUserName = webUser.getName();
                            var userId = u.getId();
                            // save postgres userId, and then set it to pilotId
                            if (Objects.equals(userId, String.valueOf(webUserId))) {
                                pgUserIdMap.putIfAbsent(webUserId, userId);
                                userNameMap.putIfAbsent(webUserId, webUserName);
                            } else {
                                pgUserIdMap.put(webUserId, userId);
                                userNameMap.put(webUserId, webUserName);
                            }
                        });
        for (Member member : pilotMembers) {
            var memberId = member.getUserId();
            ProjectTinyVo projectTinyVo = projectMap.get(member.getProjectId());
            projectTinyVo.setHasPilot(true);
            projectTinyVo.setPilot(userNameMap.getOrDefault(memberId, ""));
            projectTinyVo.setPilotId(pgUserIdMap.getOrDefault(memberId, null));
        }
    }

    private void setProcessedByForProjects(List<ProjectTinyVo> tinyProjects, List<Long> projectIds)
            throws ServiceException {
        if (CollectionAssitant.isEmpty(tinyProjects) || CollectionAssitant.isEmpty(projectIds)) {
            return;
        }
        Set<Long> companyIds =
                tinyProjects.stream().map(p -> p.getRepairCompany()).collect(Collectors.toSet());
        if (companyIds.isEmpty()) {
            return;
        }
        List<Company> companies = companyMapper.listIn(companyIds);
        Map<Long, Company> idMapCompany =
                companies.stream().collect(Collectors.toMap(Company::getCompanyId, c -> c));
        for (ProjectTinyVo projectTinyVo : tinyProjects) {
            if (projectTinyVo.getRepairCompany() == null
                    || !idMapCompany.containsKey(projectTinyVo.getRepairCompany())) {
                continue;
            }
            Company company = idMapCompany.get(projectTinyVo.getRepairCompany());
            projectTinyVo.setRepairCompany(company.getCompanyId());
            projectTinyVo.setRepairCompanyName(company.getCompanyName());
        }
    }

    private void setRolesForProjects(List<ProjectTinyVo> tinyProjects, long userId)
            throws ServiceException {
        if (tinyProjects == null || tinyProjects.isEmpty()) {
            return;
        }
        Map<Long, ProjectTinyVo> projectMap = new HashMap<Long, ProjectTinyVo>(tinyProjects.size());
        for (ProjectTinyVo p : tinyProjects) {
            projectMap.put(p.getProjectId(), p);
            p.setRoles(new ArrayList<>());
        }
        List<IdValue> roleLinkeds = null;
        try {
            roleLinkeds = memberMapper.listRolesGroupByProject(userId, projectMap.keySet());
        } catch (Exception e) {
            logger.error("Fail to list role linked for user " + userId, e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
        }
        for (IdValue roleLinked : roleLinkeds) {
            long projectId = roleLinked.getId();
            List<Integer> roleCodes = splitToInt(roleLinked.getValue(), ",");
            List<IdNameDto> roles = new ArrayList<IdNameDto>();
            for (Integer code : roleCodes) {
                RoleEnum role = RoleEnum.getEnum(code);
                roles.add(new IdNameDto(role.getCode(), role.getDisplay()));
            }
            projectMap.get(projectId).setRoles(roles);
        }
    }

    private void setProjectStatuses(List<ProjectTinyVo> tinyProjects, List<Long> projectIds)
            throws ServiceException {
        if (CollectionAssitant.isEmpty(tinyProjects) || CollectionAssitant.isEmpty(projectIds)) {
            return;
        }

        Map<Long, ProjectStatusTimeLineDto> ProjectStatusTimeLineDtoMap =
                projectStatusService.listProjectStatusTimeLines(projectIds);

        for (ProjectTinyVo project : tinyProjects) {
            ProjectStatusTimeLineDto timeline =
                    ProjectStatusTimeLineDtoMap.get(project.getProjectId());
            timeline =
                    (timeline == null
                            ? new ProjectStatusTimeLineDto(project.getProjectId())
                            : timeline);
            var siteInspected = timeline.getSiteInspected();
            var imageUploaded = timeline.getImageUploaded();
            if (siteInspected != null && !siteInspected.available() && imageUploaded != null) {
                siteInspected.setCreatedTime(imageUploaded.getCreatedTime());
            }
            if (imageUploaded != null
                    && !imageUploaded.available()
                    && siteInspected != null
                    && siteInspected.available()) {
                imageUploaded.setCreatedTime(siteInspected.getCreatedTime());
            }
            project.setStatusProjectCreated(timeline.getProjectCreated());
            project.setStatusCustomerContacted(timeline.getCustomerContacted());
            project.setStatusAssignedToPilot(timeline.getAssignedToPilot());
            project.setStatusSiteInspected(siteInspected);
            project.setStatusReturnToClient(timeline.getReturnToClient());
            project.setStatusImageUploaded(imageUploaded);
            project.setStatusIBEESUploaded(timeline.getIBEESUploaded());
            project.setStatusFirstReturnToClient(timeline.getFirstReturnToClient());
            project.setStatusLastReturnToClient(timeline.getLastReturnToClient());
            project.setStatusClientReceived(timeline.getClientReceived());

            project.setProjectCreatedCount(timeline.getStatusCount(PROJECT_CREATED));
            project.setCustomerContactedCount(timeline.getStatusCount(CUSTOMER_CONTACTED));
            project.setAssignedToPilotCount(timeline.getStatusCount(ASSIGNED_TO_PILOT));
            project.setSiteInspectedCount(timeline.getStatusCount(SITE_INSPECTED));
            project.setImageUploadedCount(timeline.getStatusCount(IMAGE_UPLOADED));
            project.setReturnToClientCount(timeline.getStatusCount(RETURNED_TO_CLIENT));
            project.setClientReceivedCount(timeline.getStatusCount(CLIENT_RECEIVED));
        }
    }

    private List<Integer> splitToInt(String line, String separator) {
        List<Integer> ints = null;
        if (StringUtils.isNotBlank(line)) {
            String[] eles = line.split(separator);
            ints = new ArrayList<Integer>(eles.length);
            for (int i = 0; i < eles.length; i++) {
                String ele = eles[i];
                ints.add(Integer.parseInt(ele));
            }
        }
        return ints == null ? new ArrayList<Integer>() : ints;
    }

    private static boolean canUserViewAllProjects(User user) {
        RoleEnum[] viewAllProjectsRole =
                new RoleEnum[] {RoleEnum.SALESMAN, RoleEnum.ADMIN, RoleEnum.SUPERADMIN};
        return user.hasAnyRole(viewAllProjectsRole);
    }

    /**
     * insert project to database create an member, creater to the project add eventHistory,
     * NEW_PROJECT to project invite the asset owner of the project, which will create a member,
     * homeowner to the project.
     */
    @Override
    @Transactional(rollbackFor = ServiceException.class)
    public Project createProject(long userId, ProjectDto projectDto) throws ServiceException {
        Project project = projectDto.toProject();
        setContractByUserCompany(userId, project);
        Project result;
        result = projectIIServiceAdapter.createProjectNew(
            userId, project, projectDto.getPolicyType(), CreationChannelType.from(projectDto.getCreationChannel()), projectDto.isAllowDuplication());

        var comments = projectDto.getComments();
        if (!CollectionUtils.isEmpty(comments)) {
            var projectId = result.getProjectId();
            comments.stream().map(c -> map(c, userId, projectId)).forEach(this::addComment);
        }

        return result;
    }

    private void addComment(Comment comment) {
        if (bees360FeatureSwitch.isEnableSystemComment()) {
            activityManager.submitActivity(
                CommentServiceImpl.from(comment));
        } else {
            commentManager.addComment(comment);
        }
    }

    private Comment map(ProjectBaseDto.WebComment comment, long userIdLong, long projectId) {
        var userId = String.valueOf(userIdLong);
        var source = ActivitySourceEnum.WEB.getValue();
        var commentBuilder = com.bees360.activity.Message.CommentMessage.newBuilder();
        commentBuilder.addAllAttachment(
                comment.getAttachments().stream()
                        .map(Comment.Attachment::toMessage)
                        .collect(Collectors.toList()));
        commentBuilder.setProjectId(projectId);
        commentBuilder.setSource(source);
        commentBuilder.setContent(comment.getContent());
        return Comment.from(commentBuilder.build());
    }

    private void setContractByUserCompany(long userId, Project project) throws ServiceException {
        if (ObjectUtils.allNotNull(
            project.getRepairCompany(),
            project.getCompanyId(),
            project.getInsuranceCompany())) {
            return;
        }

        Company company = companyService.getByUserId(userId);
        if (company != null) {
            if (project.getRepairCompany() == null
                && !companyService.isBees360Company(company.getCompanyId())) {
                // Only companies other than Bees360 will set this value
                project.setRepairCompany(company.getCompanyId());
            }

            if (project.getCompanyId() == null || project.getCompanyId() == 0) {
                project.setCompanyId(company.getCompanyId());
            }
            // If insureCompany is not specified when creating a project and the company is of insurance company type, then
            initProjectInfoWithInsureCompany(project, company);
        }
    }

    @Transactional
    @Override
    public void initProjectData(Project project, CreationChannelType creationChannel) throws ServiceException{
        long now = DateUtil.getNow();
        project.setCreatedTime(now);
        project.setLatestStatus(ProcessStatusEnum.NEW.getCode());

        var createdBy = project.getCreatedBy();

        var oldAddress = project.getAddress();
        var oldCity = project.getCity();
        var oldState = project.getState();
        var oldZipcode = project.getZipCode();
        var address = addressManager.findById(project.getAddressId());
        if (address != null) {
            project.setAddress(address.getStreetAddress());
            project.setCity(address.getCity());
            project.setState(address.getState());
            project.setCountry(address.getCountry());
            project.setLat(Optional.ofNullable(address.getLat()).orElse(0.0));
            project.setLng(Optional.ofNullable(address.getLng()).orElse(0.0));
            Optional.ofNullable(address.getZip()).ifPresent(project::setZipCode);
            project.setGpsIsApproximate(Optional.ofNullable(address.isGpsApproximate()).orElse(true));
        }

        if (!bees360FeatureSwitch.isEnableCreatedEvent()) {
            modifyClaimNote(project);
        }

        projectMapper.insertBaseInfo(project);

        // Temporarily update the project created status directly here.
        projectMapper.updateProjectStatus(
                project.getProjectId(), PROJECT_CREATED.getCode(), Instant.now().toEpochMilli(), null);

        Member creator = new Member();
        creator.setProjectId(project.getProjectId());
        creator.setUserId(createdBy);
        creator.setRole(Member.PROJECT_CREATER);
        creator.setCreatedTime(now);
        creator.setCreatedBy(project.getCreatedBy());
        creator.setDescription("");
        creator.setDeleted(false);
        memberMapper.insert(creator);

        if (StringUtils.isNotBlank(project.getOperatingCompany())) {
            operatingCompanyMapper.insert(project.getOperatingCompany(), project.getProjectId());
        }

        generateInspectionCodeIf4PointUnderwriting(createdBy, project);

        if (!bees360FeatureSwitch.isEnableCreatedEvent()) {
            checkProjectAddressFormatted(oldAddress, oldCity, oldState, oldZipcode, project);
        }
    }

    /**
     * Check if the project address is corrected. If the city, state, and zipcode of the project change after normalization, then judge the project
     * The address is corrected, and an event is issued to record an activity.
     *
     * @param oldAddress old address
     * @param oldCity old city
     * @param oldState old state
     * @param oldZipcode old zipcode
     * @param project The project after normalization
     */
    private void checkProjectAddressFormatted(
            String oldAddress,
            String oldCity,
            String oldState,
            String oldZipcode,
            Project project) {
        logger.info(
                "Start to check address with old address :{}, city :{}, state :{}, zipcode :{} and new project :{}",
                oldAddress,
                oldCity,
                oldState,
                oldZipcode,
                project);
        if (project.getZipCode() == null
                || (StringUtils.equals(oldCity, project.getCity())
                        && StringUtils.equals(oldState, project.getState())
                        && !isZipcodeCorrected(oldZipcode, project.getZipCode()))) {
            return;
        }
        String originalAddress = oldAddress + ", " + oldCity + ", " + oldState + " " + oldZipcode;
        var projectAddressFormattedEvent = new ProjectAddressFormattedEvent();
        projectAddressFormattedEvent.setFormattedAddressId(project.getAddressId());
        projectAddressFormattedEvent.setOriginalAddress(originalAddress);
        projectAddressFormattedEvent.setProjectId(project.getProjectId());
        logger.info("Start to publish event :{}", projectAddressFormattedEvent);
        eventPublisher.publish(projectAddressFormattedEvent);
    }

    /**
     * Determine whether the original zipcode has been corrected: origin == null, formatted == null: return false; origin == null, formatted
     * != null: return true; origin != null, formatted == null: thrown exception
     *
     * @return whether the original zipcode is corrected
     * @exception IllegalArgumentException origin != null, formatted == null
     */
    private boolean isZipcodeCorrected(String origin, String formatted) {
        if (origin == null) {
            return formatted != null;
        }
        if (formatted == null) {
            throw new IllegalArgumentException(
                "Formatted zipcode is null from origin %s".formatted(origin));
        }
        return !origin.contains(formatted) && !formatted.contains(origin);
    }

    /**
     * If insuredFirstName is not empty, you need to retrieve the fullName stored in postgres and update the assetOwnerName in the Project table
     */
    private void updateInsuredName(Project project) {
        if(project.getInsuredFirstName() == null) {
            return;
        }
        updateInsuredName(project.getProjectId());
    }

    private void updateInsuredName(Long projectId) {
        var contacts = contactManager.findByProjectId(String.valueOf(projectId));
        var insured = Iterables.toStream(contacts)
            .filter(c -> StringUtils.equals(c.getRole(), ProjectConstants.ContactRoleType.CONTACT_INSURED))
            .findFirst().orElse(null);

        if(insured == null) {
            return;
        }

        Map<String, Object> map = new HashMap<>();
        map.put("assetOwnerName", insured.getFullName());
        projectMapper.updateByMap(projectId, map);
    }

    /**
     * 如果是4-point underwriting的case需要生成 inspection code.
     *
     * @param project
     */
    private void generateInspectionCodeIf4PointUnderwriting(long userId, Project project) {
        if (Objects.equals(
                project.getServiceType(),
                ProjectServiceTypeEnum.FOUR_POINT_SELF_UNDERWRITING.getCode())) {
            long projectId = project.getProjectId();
            ProjectInspection projectInspection =
                    projectInspectionService.addProjectInspection(userId, projectId);
            // set inspectionCode and expirationTime to project
            project.setInspectionCode(projectInspection.getInspectionCode());
            project.setInspectionLink(
                    UriComponentsBuilder.fromUriString(inspectionLinkTemplate)
                            .buildAndExpand(projectInspection.getInspectionCode())
                            .toUriString());
            project.setExpirationTime(projectInspection.getExpirationTime());
        }
    }

    private String insurePrefixEndWithSlash(@NonNull String prefix) {
        if (prefix.endsWith("/")) {
            return prefix;
        }
        return prefix + '/';
    }

    private com.bees360.address.Address normalizedAddress(AddressVo addressVo) {
        if (addressVo == null) {
            return null;
        }

        String fullAddress =
                addressVo.getAddress()
                        + ", "
                        + addressVo.getCity()
                        + ", "
                        + addressVo.getState()
                        + " "
                        + addressVo.getZipCode();
        var isGpsApproximate = Stream.of(addressVo.getLat(), addressVo.getLng()).anyMatch(Objects::isNull);
        var address =
            com.bees360.address.Address.AddressBuilder.newBuilder()
                .setAddress(fullAddress)
                .setZip(addressVo.getZipCode())
                .setCity(addressVo.getCity())
                .setState(addressVo.getState())
                .setCountry(addressVo.getCountry())
                .setLat(addressVo.getLat())
                .setLng(addressVo.getLng())
                .setStreetAddress(addressVo.getAddress())
                .setIsGpsApproximate(isGpsApproximate)
                .build();
        var formattedAddress = addressManager.normalize(address);
        if (formattedAddress != null) {
            return formattedAddress;
        }
        return address;
    }

    private void initProjectInfoWithInsureCompany(Project project, Company company) {
        if (Objects.isNull(project.getInsuranceCompany())
                && Objects.equals(
                        company.getCompanyType(), CompanyTypeEnum.INSURANCE_COMPANY.getCode())) {
            project.setInsuranceCompany(company.getCompanyId());
            SystemConfigBees360Dto systemConfigBees360Dto =
                    systemConfigService.getSystemConfigBees360();
            project.setRepairCompany(systemConfigBees360Dto.getBees360CompanyId());
        }
    }

    private void modifyClaimNote(Project project) {
        if (project.isGpsIsApproximate()) {
            project.setClaimNote(
                    Optional.ofNullable(project.getClaimNote())
                            .map(
                                    note ->
                                            note.concat("\r\n")
                                                    .concat(
                                                            ClaimNoteAddEnum.GPS_IS_APPROXIMATE
                                                                    .getDisplay()))
                            .orElse(ClaimNoteAddEnum.GPS_IS_APPROXIMATE.getDisplay()));
        }
        addNoteByPolicyNumber(project);
    }

    private void addNoteByPolicyNumber(Project project) {
        final var LENGTH_POLICY_NUMBER_CODE = 2;
        if (StringUtils.length(project.getPolicyNumber()) < LENGTH_POLICY_NUMBER_CODE
                || Objects.isNull(project.getInsuranceCompany())) {
            return;
        }
        String prefixPolicyNumber = project.getPolicyNumber().substring(0, LENGTH_POLICY_NUMBER_CODE);
        String note =
                constantSettingService.getSettingConstant(
                        prefixPolicyNumber,
                        BsExportDataRelatedTypeEnum.SWYFFT_PROJECT_BIND.getType());
        if (StringUtils.isBlank(note)) {
            return;
        }
        if (StringUtils.isNotBlank(project.getClaimNote())) {
            note = project.getClaimNote() + "\r\n" + note;
        }
        project.setClaimNote(note);
    }

    @Deprecated
    @Override
    public Project createProject(long userId, ProjectDto projectDto, boolean bePilot)
            throws ServiceException {
        Project project = createProject(userId, projectDto);
        long projectId = project.getProjectId();
        if (bePilot) {
            try {
                com.bees360.user.User user = userProvider.findUserById(userId + "");
                if (user == null) {
                    throw new ServiceException("No user can be found with userId:" + userId);
                }
                // arrange the user to be the role of the project
                Member newMember = new Member();
                newMember.setProjectId(projectId);
                newMember.setCreatedBy(userId);
                newMember.setCreatedTime(DateUtil.getNow());
                newMember.setDeleted(false);
                newMember.setDescription("");
                newMember.setRole(RoleEnum.PILOT.getCode());
                newMember.setUserId(userId);
                memberMapper.insert(newMember);
                postgresMemberManagerAdapter.setMember(projectId, userId, RoleEnum.PILOT.getCode(), userId);

            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
            }
        }
        return project;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteProject(long projectId, long userId) throws ServiceException {
        // get the base data and check the auth of the user
        User user = userService.getUserById(userId);
        Project project = projectMapper.getById(projectId);
        RoleEnum thisUserRole = null;
        if (project.getCreatedBy() == userId) {
            thisUserRole = RoleEnum.CREATOR;
        }
        // admin role overrides creator
        if (user.hasRole(RoleEnum.ADMIN)) {
            thisUserRole = RoleEnum.ADMIN;
        }
        if (thisUserRole == null
                || (RoleEnum.CREATOR.equals(thisUserRole)
                        && project.getProjectStatus()
                                != NewProjectStatusEnum.PROJECT_CREATED.getCode())) {
            throw new ServiceException(MessageCode.AUTHORIZE_FAILED);
        }
        // Project deletion is now implemented through EventHistory. For the operation part, see
        // EventHistoryListener#insertHistoryOnProjectDeletedEvent
        // When deleting a project, the project status is changed to cancel because many businesses are based on the project status. If the project status is not changed to cancel, data errors may occur.
        projectStatusService.changeOnProjectCanceled(userId, projectId, SystemTypeEnum.BEES360, "");
        publisher.publishEvent(new ProjectDeletedEvent(this, project, userId));
        projectIIManager.deleteById(String.valueOf(projectId));
    }

    @Override
    @Transactional
    public void cancelProject(long projectId, long userId, String cancelReason) throws ServiceException {
        cancelProject(projectId, userId, SystemTypeEnum.BEES360, true, cancelReason);
    }

    @Override
    @Transactional
    public void cancelProject(
            long projectId, long userId, SystemTypeEnum systemType, boolean authChecked, String cancelReason)
            throws ServiceException {
        User user = userService.getUserById(userId);
        Project project = projectMapper.getById(projectId);
        // The company administrator cannot cancel the status after imageUpload
        boolean companyAdmin =
                user.hasRole(RoleEnum.COMPANY_ADMIN) && !user.hasRole(RoleEnum.ADMIN);
        NewProjectStatusEnum projectStatusEnum =
                NewProjectStatusEnum.getEnum(project.getProjectStatus());
        if (authChecked
                && companyAdmin
                && NewProjectStatusEnum.isAfterImageUploaded(project.getProjectStatus())) {
            throw new ServiceMessageException(
                    MessageCode.OPERATION_FORBIDDEN,
                    "This project cannot be canceled, current status: {}"
                            + projectStatusEnum.getDisplay());
        }
        if (NewProjectStatusEnum.PROJECT_CANCELED == projectStatusEnum) {
            return;
        }

        projectStatusService.changeOnProjectCanceled(userId, projectId, systemType, cancelReason);
    }

    @Override
    public void recoverProject(long projectId, long userId) throws ServiceException {

        Project project = projectMapper.getById(projectId);

        List<EventHistory> eventHistories =
                eventHistoryService.listHistorySortByCreateTimeDesc(projectId);
        ProjectStatusEnum firstNotCanceled = ProjectStatusEnum.CANCELED;
        for (EventHistory history : eventHistories) {
            ProjectStatusEnum historyEnum = ProjectStatusEnum.getEnum(history.getStatus());
            if (historyEnum == null) {
                continue;
            }
            if (ProjectStatusEnum.CANCELED == historyEnum
                    || ProjectStatusEnum.RECOVERED == historyEnum
                    || ProjectStatusEnum.DELETE == historyEnum
                    || ProjectStatusEnum.RETURNED_TO_CLIENT == historyEnum
                    || ProjectStatusEnum.CLIENT_RECEIVED == historyEnum) {
                continue;
            }
            if (historyEnum.getProcessStatus() == null) {
                continue;
            }
            firstNotCanceled = historyEnum;
            break;
        }

        updateLatestStatus(projectId, firstNotCanceled.getProcessStatus());

        publisher.publishEvent(new ProjectRecoveredEvent(this, project));

        projectStatusService.changeOnProjectRecovered(userId, projectId);
    }

    @Override
    public void updateLatestStatus(long projectId, ProcessStatusEnum status) {
        if (status == null) {
            return;
        }
        boolean updated = projectMapper.updateLatestStatus(projectId, status.getCode());
        if (updated) {
            publisher.publishEvent(new ProjectLatestStatusEvent(this, projectMapper.getById(projectId)));
        }
    }

    @Override
    public int countProjectInProcessing(long userId) throws ServiceException {
        Map<String, Object> userIdMap = new HashMap<String, Object>();
        userIdMap.put("userId", userId);
        userIdMap.put(
                "processingStatusList",
                Arrays.asList(
                        ProcessStatusEnum.IN_PROCESS.getCode(),
                        ProcessStatusEnum.UNDER_REVIEW.getCode(),
                        ProcessStatusEnum.REPORT_APPROVED.getCode()));
        userIdMap.put("curUserId", userId);
        return projectMapper.countTinyProjectWithSearch(userIdMap);
    }

    /** format some data of map */
    private void preProcessSearchMap(Map<String, Object> searchMap) {
        if (searchMap.get("pageIndex") != null) {
            Integer pageIndex = (Integer) searchMap.get("pageIndex");
            Integer pageSize = (Integer) searchMap.get("pageSize");

            int startIndex = (pageIndex - 1) * pageSize;

            searchMap.put("pageIndex", pageIndex);
            searchMap.put("pageSize", pageSize);
            searchMap.put("startIndex", startIndex);
        }
        if (searchMap.get("sortKey") != null) {
            String sortKey = (String) searchMap.get("sortKey");
            if ("projectId".equals(sortKey)) {
                searchMap.put("sortKey", "project_id");
            } else if ("fullAddr".equals(sortKey)) {
                searchMap.put("sortKey", "address");
            } else if ("claimNumber".equals(sortKey)) {
                searchMap.put("sortKey", "claim_number");
            } else if ("status".equals(sortKey)) {
                searchMap.put("sortKey", "latest_status");
            } else if ("createdTime".equals(sortKey)) {
                searchMap.put("sortKey", "created_time");
            } else if ("creator".equals(sortKey)) {
                searchMap.put("sortKey", "creator");
            } else if ("daysOld".equals(sortKey)) {
                searchMap.put("sortKey", "days_old");
            } else if ("riskScore".equals(sortKey)) {
                searchMap.put("sortKey", "score");
            }
            String sortOrder = (String) searchMap.get("sortOrder");
            if ("descending".equals(sortOrder) || "desc".equals(sortOrder)) {
                sortOrder = "desc";
            } else {
                sortOrder = "asc";
            }
            searchMap.put("sortOrder", sortOrder);
        }
        createFuzzyNumber(searchMap, "searchProjectId");
        createFuzzyRegex(searchMap, "searchAddress");

        createFuzzyRegex(searchMap, "insuredName");
        createFuzzyPhone(searchMap, "insuredPhone");
        createFuzzyRegex(searchMap, "address");
        createFuzzyRegex(searchMap, "city");

        String state = (String) searchMap.get("state");
        if (StringUtils.isNotBlank(state)) {
            searchMap.put("fullNameState", AmericaStateEnums.abbreviationToFullName(state));
        }

        var rawStates = searchMap.get("states");
        if (rawStates instanceof List) {
            var states = (List<String>) rawStates;
            searchMap.put("fullNameStates", states.stream().map(AmericaStateEnums::abbreviationToFullName).collect(Collectors.toSet()));
        }

        Boolean paid = (Boolean) searchMap.get("paid");
        if (paid != null) {
            searchMap.put("pay_status", paid ? PayStatusEnum.PAID.getCode() : PayStatusEnum.UNPAID.getCode());
        }
    }

    private void createFuzzyNumber(Map<String, Object> searchMap, final String key) {
        if (!searchMap.containsKey(key)) {
            return;
        }
        final String REGEX = "Regex";
        String numberValue = String.valueOf(searchMap.get(key));
        if (!StringUtils.isNumeric(numberValue)) {
            searchMap.remove(key);
        } else {
            searchMap.put(key + REGEX, SqlHelper.createNumberFuzzyRegex(numberValue));
        }
    }

    private void createFuzzyRegex(Map<String, Object> searchMap, final String key) {
        if (!searchMap.containsKey(key)) {
            return;
        }
        final String REGEX = "Regex";
        String value = String.valueOf(searchMap.get(key));
        searchMap.put(key + REGEX, SqlHelper.createFuzzyRegex(value));
    }

    private void createFuzzyPhone(Map<String, Object> searchMap, final String key) {
        if (!searchMap.containsKey(key)) {
            return;
        }
        final String REGEX = "Regex";
        String numberValue = String.valueOf(searchMap.get(key));
        searchMap.put(key + REGEX, SqlHelper.createPhoneFuzzyRegex(numberValue));
    }

    private void checkProjectInfo(Project project, boolean ignoreClaimType)
            throws ServiceException {
        // <EMAIL> check the company type
        // check projectType of project
        if (!ignoreClaimType) {
            ProjectRuleUtil.checkClaimTypeAndSetDefault(project);
        }
        if (project.getProjectType() != null && !ProjectTypeEnum.exist(project.getProjectType())) {
            throw new ServiceException(MessageCode.PROJECT_TYPE_IS_NULL);
        }

        // check insuranceCompany and repairedCompany is variable or not
        if (project.getInsuranceCompany() != null) {
            long insuranceCompanyId = project.getInsuranceCompany();
            Company insuranceCompany = companyService.getById(insuranceCompanyId);
            if (insuranceCompany == null) {
                throw new ServiceException(MessageCode.INSURANCE_COMPANY_NOT_EXISTED);
            }
        }
        if (project.getRepairCompany() != null) {
            long repairCompanyId = project.getRepairCompany();
            Company repairCompany = companyService.getById(repairCompanyId);
            if (repairCompany == null) {
                throw new ServiceException(MessageCode.REPAIR_COMPANY_NOT_EXISTED);
            }
        }

        //year built
        if (StringUtils.isNotBlank(project.getYearBuilt())) {
            if(!NumberUtils.isDigits(project.getYearBuilt())){
                throw new ServiceMessageException(PARAM_INVALID, "YearBuilt should be a number");
            }
        }
    }

    /**
     * pick up some fields from Project which will be shown in GPI(General Property Information)
     *
     * @throws ServiceException
     */
    private Map<String, Object> createGeneralPropertyInformationFromProject(Project project)
            throws ServiceException {
        Map<String, Object> map = new HashMap<String, Object>();
        if (project == null) {
            return map;
        }
        // Policy Holder
        map.put("assetOwnerName", project.getAssetOwnerName());
        map.put("assetOwnerPhone", project.getAssetOwnerPhone());
        map.put("assetOwnerEmail", project.getAssetOwnerEmail());

        map.put("projectType", project.getProjectType());
        map.put("address", project.getAddress());
        map.put("city", project.getCity());
        map.put("state", project.getState());
        map.put("country", project.getCountry());
        map.put("zipCode", project.getZipCode());
        map.put("flyZoneType", project.getFlyZoneType());

        map.put("inspectionNumber", project.getInspectionNumber());
        map.put("dueDate", project.getDueDate());
        map.put("customer", project.getCustomer());
        map.put("inspectionType", project.getInspectionType());
        map.put("agent", project.getAgent());
        map.put("agentEmail", project.getAgentEmail());
        map.put("agentContactName", project.getAgentContactName());
        map.put("agentPhone", project.getAgentPhone());
        map.put("guideline", project.getGuideline());
        map.put("insuredHomePhone", project.getInsuredHomePhone());
        map.put("insuredWorkPhone", project.getInsuredWorkPhone());

        CompanyVo insuranceCompany = null;
        if (null != project.getInsuranceCompany()) {
            insuranceCompany =
                    CompanyBeanConverter.toCompanyVo(
                            companyService.getById(project.getInsuranceCompany()));
        }
        CompanyVo repairCompany = null;
        if (null != project.getRepairCompany()) {
            repairCompany =
                    CompanyBeanConverter.toCompanyVo(
                            companyService.getById(project.getRepairCompany()));
        }
        map.put("insuranceCompany", insuranceCompany);
        map.put("repairCompany", repairCompany);

        map.put("description", project.getDescription());
        map.put("lng", project.getLng());
        map.put("lat", project.getLat());

        map.put("isBooking", project.isBooking());
        map.put("needPilot", project.isNeedPilot());
        map.put("roofEstimatedAreaItem", project.getRoofEstimatedAreaItem());
        map.put("reportServiceOption", project.getReportServiceOption());
        map.put("chimney", project.getChimney());
        map.put("claimType", project.getClaimType());
        return map;
    }

    @Override
    public List<Company> listComaniesInProjectsUserTakePartIn(
            long userId, CompanyTypeEnum companyType) throws ServiceException {
        User user = userService.getUserById(userId);
        Integer typeCode = companyType == null ? null : companyType.getCode();
        Long curUserId = null;
        Long managedBy = null;
        if (!canUserViewAllProjects(user)) {
            curUserId = user.getUserId();
        }
        if (user.hasRole(RoleEnum.COMPANY_ADMIN) && Objects.nonNull(user.getCompanyId())) {
            managedBy = user.getCompanyId();
        }
        List<Company> companies =
                projectMapper.listComaniesInProjects(typeCode, curUserId, managedBy);
        return companies;
    }

    @Override
    public List<Company> listCompanyDict(long userId, CompanyTypeEnum companyType)
            throws ServiceException {
        User user = userService.getUserById(userId);
        Integer typeCode = companyType == null ? null : companyType.getCode();
        List<Company> companies;
        if (canUserViewAllProjects(user)) {
            companies = companyService.listByType(typeCode);
        } else {
            // Non-administrators return the company to which the current user belongs
            companies = Optional.ofNullable(companyService.getByUserId(userId)).map(List::of)
                .orElse(Collections.emptyList());
        }
        return companies.stream()
                .map(
                        c -> {
                            Company company = new Company();
                            company.setCompanyId(c.getCompanyId());
                            company.setCompanyType(c.getCompanyType());
                            company.setCompanyName(c.getCompanyName());
                            return company;
                        })
                .collect(Collectors.toList());
    }

    @Override
    public void confirmImageUploadedByWeb(
            long projectId, long userId, long inspectionTypes, int fileSourceType)
            throws ServiceException {
        // web端主动触发图片同步逻辑
        confirmImageUploaded(projectId, userId, inspectionTypes);
    }

    @Override
    public void confirmImageUploaded(long projectId, long userId, long inspectionTypes)
            throws ServiceException {
        Project project = null;
        int imageNum = 0;
        try {
            project = projectMapper.getById(projectId);
            List<Integer> fileSourceTypes =
                    FileSourceTypeEnum.listTokenByUser().stream()
                            .map(t -> t.getCode())
                            .collect(Collectors.toList());
            imageNum =
                    projectImageMapper.countImagesForMultiTypes(
                            project.getProjectId(), fileSourceTypes, null, false);
            projectMapper.updateInspectionTypes(projectId, inspectionTypes);
        } catch (Exception e) {
            logger.error(
                    "Fail to access database when create (images uploads) message for project "
                            + projectId,
                    e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
        }

        // info staffs
        messageService.infoStaffsImageUploaded(project, imageNum);

        notificationService.notifyImageUploaded(projectId, project.getCreatedBy());
    }

    @Override
    public void insertPilotCheckedStatus(
            long projectId, long userId, int type, ProjectStatusEnum projectStatus)
            throws ServiceException {
        // TODO 不应该加入这个限制
        //		if (projectStatus == ProjectStatusEnum.ADJUSTER_CLAIM_CHECKED_IN
        //				|| projectStatus == ProjectStatusEnum.ADJUSTER_CLAIM_CHECKED_OUT
        //				|| projectStatus == ProjectStatusEnum.ADJUSTER_UNDERWRITING_CHECKED_IN
        //				|| projectStatus == ProjectStatusEnum.ADJUSTER_UNDERWRITING_CHECKED_OUT) {
        //			checked(projectId, projectStatus);
        //		}

        // record the event history
        eventHistoryService.insertHistoryToProject(projectStatus, projectId, userId);
        // record pilot status
        beesPilotStatusService.checkByProjectStatus(projectId, projectStatus);
    }

    @Override
    public EventHistory getAdjusterCheckedStatus(long projectId) throws ServiceException {
        List<EventHistory> checkStatus =
                eventHistoryService.listHistorySortByCreateTimeDesc(projectId);
        for (EventHistory checkedStatus : checkStatus) {
            if (checkedStatus.getStatus() == ProjectStatusEnum.ADJUSTER_CLAIM_CHECKED_IN.getCode()
                    || checkedStatus.getStatus()
                            == ProjectStatusEnum.ADJUSTER_CLAIM_CHECKED_OUT.getCode()
                    || checkedStatus.getStatus()
                            == ProjectStatusEnum.ADJUSTER_UNDERWRITING_CHECKED_IN.getCode()
                    || checkedStatus.getStatus()
                            == ProjectStatusEnum.ADJUSTER_UNDERWRITING_CHECKED_OUT.getCode()) {
                return checkedStatus;
            }
        }
        return null;
    }

    @Override
    public Set<String> listUserAccessableModules(long projectId, long userId)
            throws ServiceException {
        // special for admin and salesman
        User user = null;
        List<Member> members = null;
        Set<String> modules = new HashSet<String>();
        try {
            user = userService.getUserById(userId);
            if (user.hasRole(RoleEnum.ADMIN)) {
                modules.addAll(AuthorityUtil.listAccessableModules(RoleEnum.ADMIN));
                return modules;
            }
            if (user.hasRole(RoleEnum.SALESMAN)) {
                modules.addAll(AuthorityUtil.listAccessableModules(RoleEnum.SALESMAN));
                return modules;
            }
            members = memberMapper.listActiveMembersByUserId(projectId, userId);
        } catch (Exception e) {
            logger.error(MessageCode.DATABASE_EXCEPTION, e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
        }
        for (Member member : members) {
            int role = member.getRole();
            modules.addAll(AuthorityUtil.listAccessableModules(role));
        }
        return modules;
    }

    @Override
    public List<UserTinyVo> listProjectsCreators(long userId) throws ServiceException {

        User user = userService.getUserById(userId);
        Long curUserId = null;
        Long managedBy = null;

        // If you are not a bees360 account and are not an admin, you can only see creators in the same company
        if (!canUserViewAllProjects(user)) {
            curUserId = user.getUserId();
        }

        if (Objects.nonNull(user.getCompanyId()) && !isUserInBees360(user)) {
            managedBy = user.getCompanyId();
        }
        var creatorIds = projectMapper.listAllCreators();
        if (curUserId != null && managedBy != null) {
            // return creator belongs to the managedBy
            var users = bifrostUserMapperAdapter.getUserByCompanyId(managedBy, null);
            return users.stream()
                .filter(u -> creatorIds.contains(u.getUserId())).map(UserTinyVo::new)
                .sorted(Comparator.comparing(UserTinyVo::getName))
                .collect(Collectors.toList());
        }
        return Iterables.toList(Iterables.transform(bifrostUserMapperAdapter.getUserById(creatorIds), UserTinyVo::new));
    }

    private boolean isUserInBees360(User user) {
        var company = user.getCompanyId();
        return company == Long.parseLong(BEES360_COMPANY_ID);
    }

    @Override
    public UserTinyVo inviteVisitor(long projectId, long inviter, String email)
            throws ServiceException {
        if (email == null) {
            throw new ServiceException(MessageCode.EMAIL_CAN_NOT_NULL);
        }
        var users = Iterables.toList(userProvider.findUserByEmail(email));
        User visitor = UserAssemble.toWebUser(users.get(0));
        if (visitor == null) {
            throw new ServiceException(MessageCode.USER_NOT_EXISTED);
        }
        inviteVisitor(projectId, inviter, visitor.getUserId());
        return new UserTinyVo(visitor, RoleEnum.VISITOR);
    }

    @Override
    public void inviteVisitor(long projectId, long visitorUserId) throws ServiceException {
        long userId = springSecurityContextProvider.getUserIdFromContext();
        inviteVisitor(projectId, userId, visitorUserId);
    }

    @Override
    public void inviteVisitor(long projectId, long inviter, long visitorUserId)
            throws ServiceException {

        List<Member> visitors;
        try {
            visitors = memberMapper.listAllByUser(projectId, visitorUserId);
        } catch (Exception e) {
            String msg = "Fail to list members of the project " + projectId;
            logger.error(msg, e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION, e);
        }

        boolean everBeVisitor = false;
        for (Member member : visitors) {
            if (!member.getDeleted()) {
                throw new ServiceException(MessageCode.USER_IS_MEMBER);
            }
            if (member.getRole() == RoleEnum.VISITOR.getCode()) {
                everBeVisitor = true;
            }
        }
        if (everBeVisitor) {
            try {
                memberMapper.activeMember(projectId, visitorUserId, RoleEnum.VISITOR.getCode());
            } catch (Exception e) {
                String msg = "Fail to inser active member" + inviter + " in project " + projectId;
                logger.error(msg, e);
                throw new ServiceException(MessageCode.DATABASE_EXCEPTION, e);
            }
        } else {
            Member member = new Member();
            member.setProjectId(projectId);
            member.setCreatedBy(inviter);
            member.setUserId(visitorUserId);
            member.setRole(RoleEnum.VISITOR.getCode());
            member.setCreatedTime(System.currentTimeMillis());
            member.setDeleted(false);
            member.setDescription("VISITOR");
            try {
                memberMapper.insert(member);
            } catch (Exception e) {
                String msg =
                        "Fail to inser visitor member" + inviter + " into project " + projectId;
                logger.error(msg, e);
                throw new ServiceException(MessageCode.DATABASE_EXCEPTION, e);
            }
        }
        messageService.infoInviteAsVisitor(projectId, inviter, visitorUserId);
    }

    @Override
    public void deleteVisitor(long projectId, long visitor) throws ServiceException {
        long userId =
            Optional.of(springSecurityContextProvider.getUserIdFromContext())
                .filter(l -> l != 0)
                .orElse(User.AI_ID);
        deleteVisitor(projectId, userId, visitor);
    }

    @Override
    public void deleteVisitor(long projectId, long userId, long visitor) throws ServiceException {
        memberMapper.delete(projectId, visitor, RoleEnum.VISITOR.getCode());
    }

    @Override
    public ProjectTinyUserVoCandidates listCollaborationCandidates(
            long userId, boolean allowAdminSearchAll) throws ServiceException {

        User user = userService.getUserById(userId);
        boolean isAdmin = user.hasRole(RoleEnum.ADMIN) || user.hasRole(RoleEnum.SUPERADMIN);
        boolean isCompanyAdmin = user.hasRole(RoleEnum.COMPANY_ADMIN);

        long companyId = Optional.ofNullable(user.getCompanyId()).orElse(0L);
        if (!isAdmin || isCompanyAdmin) {
            // companyAdmin role only can assign the company users
            if (user.getCompanyId() == null) {
                return new ProjectTinyUserVoCandidates();
            }
            companyId = user.getCompanyId();
        }
        if (allowAdminSearchAll && isAdmin) {
            companyId = 0L;
        }
        List<User> userList = bifrostUserMapperAdapter.getUserByCompanyId(companyId, false);
        if (CollectionUtils.isEmpty(userList)) {
            return new ProjectTinyUserVoCandidates();
        }
        ProjectTinyUserVoCandidates tinyUserVoCandidates = new ProjectTinyUserVoCandidates();
        List<User> tempUserList;
        if (isAdmin) {
            tempUserList = getUsersWithRoleFilter(userList, RoleEnum.PILOT);
            tinyUserVoCandidates.setPilots(userVoReferToTinyUserVo(tempUserList, RoleEnum.PILOT));
            tempUserList = getUsersWithRoleFilter(userList, RoleEnum.PROCESSOR);
            tinyUserVoCandidates.setProcessors(
                    userVoReferToTinyUserVo(tempUserList, RoleEnum.PROCESSOR));
            tempUserList = getUsersWithRoleFilter(userList, RoleEnum.ADJUSTER);
            tinyUserVoCandidates.setAdjusters(
                userVoReferToTinyUserVo(tempUserList, RoleEnum.ADJUSTER));

        }

        boolean companyCustomCondition = companyCustomConditions(companyId);
        if (isAdmin || isCompanyAdmin || companyCustomCondition) {
            tempUserList = getUsersWithRoleFilter(userList, RoleEnum.DESK_ADJUSTER);
            tinyUserVoCandidates.setDeskAdjusters(
                    userVoReferToTinyUserVo(tempUserList, RoleEnum.DESK_ADJUSTER));
            tempUserList = getUsersWithRoleFilter(userList, RoleEnum.UNDERWRITER);
            tinyUserVoCandidates.setUnderwriter(
                    userVoReferToTinyUserVo(tempUserList, RoleEnum.UNDERWRITER));
            tempUserList = getUsersWithRoleFilter(userList, RoleEnum.REVIEWER);
            tinyUserVoCandidates.setReviewers(
                userVoReferToTinyUserVo(tempUserList, RoleEnum.REVIEWER));
        }
        return tinyUserVoCandidates;
    }

    @Override
    public ProjectTinyUserVoCandidates listCollaborationCandidates(long projectId, long userId)
            throws ServiceException {
        return listCollaborationCandidates(userId, false);
    }

    private List<UserTinyVo> userVoReferToTinyUserVo(List<User> tempUserList, RoleEnum roleEnum) {
        return tempUserList.stream()
                .map(tmpUser -> new UserTinyVo(tmpUser, roleEnum))
                .collect(Collectors.toList());
    }

    private List<User> getUsersWithRoleFilter(List<User> userList, RoleEnum roleEnum) {
        return userList.stream()
                .filter(user -> user.hasRole(roleEnum))
                .sorted(Comparator.comparing(User::getName))
                .collect(Collectors.toList());
    }

    private boolean companyCustomConditions(long companyId) {

        if (Objects.equals(companyIDMap.getSecurity_First_Florida(), companyId)) {
            return true;
        }
        return false;
    }

    @Override
    public void setProjectSearchOptionBase(User user, ProjectSearchOptionBase searchOption) {
        if (hasOnlyRolesIn(user, RoleEnum.PILOT, RoleEnum.HOMEOWNER)) {
            // If the user is a pilot role, you need to filter items in the return_to_client status.
            long tenDaysBefore = Instant.now().minus(10, ChronoUnit.DAYS).toEpochMilli();
            searchOption.partionFilterStatusUpdateTimeProjectStatus(
                    tenDaysBefore, NewProjectStatusEnum.RETURNED_TO_CLIENT.getCode());
        }
        if (!canUserViewAllProjects(user)) {
            searchOption.additionalFilterCurUserId(user.getUserId());
        }
        if (user.hasRole(RoleEnum.COMPANY_ADMIN) && Objects.nonNull(user.getCompanyId())) {
            searchOption.additionalFilterManagedBy(user.getCompanyId());
        }
    }

    @Override
    public void updateProjectToReworkStatus(
            long userId, long projectId, ProjectReworkReasonParam reworkReasonParam, Long updateTime) {
        Project project = projectMapper.getById(projectId);
        if (project == null) {
            throw new ResourceNotFoundException();
        }

        projectStatusService.projectReworkOnWeb(
                userId, projectId, reworkReasonParam.getTitle(), reworkReasonParam.getContent(), updateTime);
    }

    @Override
    public List<Map<String, Object>> getAllZeroLatLngs() throws ServiceException {
        return projectMapper.listAllZeroLatLngsAddress();
    }

    @Override
    public void updateProjectLatLngVos(List<ProjectLatLngVo> projectLatLngVos)
            throws ServiceException {
        if (projectLatLngVos == null || projectLatLngVos.size() == 0) {
            return;
        }
        List<Long> projectIds = new ArrayList<>();
        for (ProjectLatLngVo projectLatLngVo : projectLatLngVos) {
            long projectId = projectLatLngVo.getProjectId();
            projectMapper.updateProjectLatLng(
                    projectId, projectLatLngVo.getLat(), projectLatLngVo.getLng());
            projectIds.add(projectId);
        }
        List<Project> projects = projectMapper.listProjects(projectIds);
        projects.forEach(
                project -> publisher.publishEvent(new ProjectAddressChangeEvent(this, project)));
    }

    @Override
    public Map<Integer, Exception> createProjectBatch(long userId, ProjectBatchDto projectBatchVo)
            throws ServiceException {
        TimerLog timerLog = TimerLog.newTimerLog(logger);
        timerLog.start("totalCost");

        Map<Integer, Exception> exceptionProjectsMap = new LinkedHashMap<>();
        List<ProjectBatchItemDto> projectBatchItemDtos = projectBatchVo.getProjects();
        if (projectBatchItemDtos == null) {
            throw new ServiceException(PARAM_INVALID);
        }
        if (projectBatchItemDtos.isEmpty()) {
            return exceptionProjectsMap;
        }

        // Cache the map of the queried company information
        Map<String, Company> companyCache = new HashMap<>();
        Map<Integer, Future<OutputParam>> futureMap = new LinkedHashMap<>();

        for (ProjectBatchItemDto projectBatchItemDto : projectBatchItemDtos) {
            if (isProjectDuplicated(projectBatchVo.getBatchId(), projectBatchItemDto)) {
                logger.info("Create project skipped due to already created. batchId:{} idx:{}.",
                    projectBatchVo.getBatchId(), projectBatchItemDto.getIdx());
                continue;
            }

            Project project = projectBatchItemDto.toProject();

            // Find companies related to project creation
            addCompanyToProject(project, projectBatchItemDto, companyCache);

            //Asynchronous processing start
            Future<OutputParam> projectFuture =
                    asyncCreateBeeObjectThreadPool.submit(
                            userId, project, projectBatchItemDto, projectBatchVo.isAllowDuplication(), this::asyncCreateProject);
            futureMap.put(projectBatchItemDto.getIdx(), projectFuture);
            //Asynchronous processing end
        }

        // Future receives the thread pool execution result
        futureMap.forEach(
                (idx, f) -> {
                    try {
                        Project project = f.get().getProject();
                        logger.info(
                                "Create project success. userId:{} batchId:{} idx:{} projectId:{}",
                                userId,
                                projectBatchVo.getBatchId(),
                                idx,
                                project.getProjectId());

                        saveExternalIntegration(project.getProjectId(),
                                                project.getInsuranceCompany(),
                                                projectBatchVo.getBatchId(),
                                                idx);
                    } catch (ExecutionException e) {
                        exceptionProjectsMap.put(idx, (Exception) e.getCause());
                        logger.error(
                            "Create project failed. userId:{} batchId:{}, idx:{} error:{}.",
                            userId,
                            projectBatchVo.getBatchId(),
                            idx,
                            e.getMessage());
                    } catch (Exception e) {
                        exceptionProjectsMap.put(idx, e);
                        logger.error(
                            "Create project failed. userId:{} batchId:{}, idx:{} error:{}.",
                            userId,
                            projectBatchVo.getBatchId(),
                            idx,
                            e.getMessage());
                    }
                });

        timerLog.start();
        timerLog.pause("totalCost").print("Total cost: ${cost} ms");

        return exceptionProjectsMap;
    }

    private void saveExternalIntegration(long projectId, Long companyId, String batchId, int index) {
        if (StringUtils.isEmpty(batchId)) {
            return;
        }

        try {
            var referenceNumber = generateReferenceNumber(batchId, index);
            externalIntegrationManager.save(
                ExternalIntegration.from(
                    com.bees360.project.Message.IntegrationMessage.newBuilder()
                        .setDataset(
                            Optional.ofNullable(companyId)
                                .map(id -> companyMapper.getById(id).getCompanyKey())
                                .orElse(""))
                        .setIntegrationType("BATCH")
                        .setProjectId(String.valueOf(projectId))
                        .setReferenceNumber(referenceNumber)
                        .build()
                )
            );
        } catch (Exception e) {
            logger.error("Failed to save project in project_integration due to {}.", e.getMessage());
        }
    }

    private boolean isProjectDuplicated(String batchId, ProjectBatchItemDto project) {
        if (StringUtils.isEmpty(batchId)) {
            return false;
        }

        var referenceNumber = generateReferenceNumber(batchId, project.getIdx());
        return externalIntegrationManager.findByReference("BATCH", referenceNumber)
            != null;
    }

    private String generateReferenceNumber(String batchId, int index) {
        return batchId + "-" + index;
    }

    /** Part of batch creation of project asynchronous operations */
    private OutputParam asyncCreateProject(
            long userId, Project project, ProjectBatchItemDto projectBatchItemDto, boolean allowDuplication)
            throws ServiceException {
        TimerLog timerLog = TimerLog.getTimerLog(logger);
        timerLog.printMinCost(500);
        timerLog.printErrorMinCost(30000);

        OutputParam outputParam = new OutputParam();

        timerLog.start();
        var policyType = Optional.ofNullable(projectBatchItemDto.getPolicyType()).orElse("");
        // 在数据库中创建项目记录 -> 此时project对象已经有 projectId
        setContractByUserCompany(userId, project);
        projectIIServiceAdapter.createProjectNew(
            userId, project, policyType, CreationChannelType.WEB, allowDuplication);
        timerLog.pause().print("initProjectData() cost: ${cost} ms");

        outputParam.setProject(project);
        return outputParam;
    }

    /**
     * If there are companies that meet the conditions in <code>companyCache</code> and the database, and the company is required to be added in <code>projectBatchItemVo</code>, set it to
     * <code>project</code>。
     *
     * @param project
     * @param projectBatchItemDto carries the information of the company that needs to be arranged
     * @param companyCache The map that has been found and can match company information from company name
     * @throws ServiceException
     */
    private void addCompanyToProject(
            Project project,
            ProjectBatchItemDto projectBatchItemDto,
            Map<String, Company> companyCache) throws ServiceException {

        if (!StringUtils.isEmpty(StringUtils.trim(projectBatchItemDto.getProcessedBy()))) {
            String companyName = StringUtils.trim(projectBatchItemDto.getProcessedBy());
            Company processedBy = companyCache.get(companyName);
            if (processedBy == null) {
                processedBy = companyService.getByName(companyName);
                if (processedBy == null) {
                    throw new ServiceMessageException(
                            MessageCode.COMPANY_NOT_EXISTED,
                            "The company '" + companyName + "' dosen't exist.");
                }
                // 取消对公司类型的限制，允许将非repair company设置到Project.repairCompany中
                // if(processedBy.getCompanyType() != CompanyTypeEnum.REPAIR_COMPANY.getCode()) {
                //     throw new ServiceMessageException(MessageCode.REPAIR_COMPANY_NOT_EXISTED,
                // "The company '"
                //         + companyName + "' couldn't be added as processedBy.");
                // }
                companyCache.put(companyName, processedBy);
            }
            project.setRepairCompany(processedBy.getCompanyId());
        }
        if (StringUtils.isNotBlank(StringUtils.trim(projectBatchItemDto.getInsuredBy()))) {
            String insuredBy = StringUtils.trim(projectBatchItemDto.getInsuredBy());
            Company insuredCompany = companyCache.get(insuredBy);
            if (insuredCompany == null) {
                insuredCompany = companyService.getByName(insuredBy);
                if (insuredCompany == null) {
                    throw new ServiceMessageException(
                            MessageCode.COMPANY_NOT_EXISTED,
                            "The company '" + insuredBy + "' dosen't exist.");
                }
                companyCache.put(insuredBy, insuredCompany);
            }
            project.setInsuranceCompany(insuredCompany.getCompanyId());
        }
    }

    @Override
    public ProjectDetailVo getProjectDetail(long userId, long projectId) throws ServiceException {
        long centerUtcOffset = ProjectSearchOption.getDaysOldCenterUtcOffset();
        ProjectCalSelectVo project = projectMapper.getCalSelectVoById(centerUtcOffset, projectId);

        ProjectImageSearchOptionDto queryParameter =
                ProjectImageSearchOptionDto.builder()
                        .projectId(projectId)
                        .deleted(false)
                        .excludeFileSourceType(FileSourceTypeEnum.PLACEHOLDER.getCode())
                        .startIndex(0)
                        .pageSize(3)
                        .fileSourceTypes(exhibitImageFileSourceTypes())
                        .build();
        List<ProjectImageAnnotationVo> images = projectImageMapper.listImagesPage(queryParameter);
        int imagesTotal = projectImageMapper.countImagesPageTotal(queryParameter);

        List<ProjectReportFileTinyVo> reports =
                projectReportFileService.getReportFiles(projectId, userId, null);
        List<UserTinyVo> members = userService.listMemberInProject(userId, projectId);
        BeesPilotBatchItem batchItem =
                beesPilotBatchItemService
                        .listByProjectIds(Collections.singletonList(projectId))
                        .stream()
                        .findFirst()
                        .orElse(null);
        return jointProjectDetail(project, members, images, imagesTotal, reports, batchItem);
    }

    private List<Integer> exhibitImageFileSourceTypes() {
        List<Integer> fileSourceTypes = new ArrayList<Integer>();
        fileSourceTypes.add(FileSourceTypeEnum.DRONE_IMAGE.getCode());
        fileSourceTypes.add(FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode());
        fileSourceTypes.add(FileSourceTypeEnum.DRONE_PREVISIT_IMAGE.getCode());
        fileSourceTypes.add(FileSourceTypeEnum.DRONE_REALTIME_IMAGE.getCode());

        return fileSourceTypes;
    }

    private ProjectDetailVo jointProjectDetail(
            ProjectCalSelectVo project,
            List<UserTinyVo> members,
            List<ProjectImageAnnotationVo> images,
            int imagesTotal,
            List<ProjectReportFileTinyVo> reports,
            BeesPilotBatchItem batchItem)
            throws ServiceException {
        // base info
        ProjectDetailVo detail = new ProjectDetailVo();
        detail.setProjectId(project.getProjectId());
        detail.setState(project.getState());
        detail.setCity(project.getCity());
        detail.setAddress(project.getAddress());
        detail.setZipCode(project.getZipCode());
        detail.setAssetOwnerName(project.getAssetOwnerName());
        detail.setAssetOwnerEmail(project.getAssetOwnerEmail());
        detail.setAssetOwnerPhone(project.getAssetOwnerPhone());
        detail.setProjectStatus(project.getProjectStatus());
        detail.setCreatedTime(project.getCreatedTime());
        detail.setDaysOld(project.getDaysOld());
        detail.setServiceType(project.getServiceType());
        detail.setPolicyEffectiveDate(project.getPolicyEffectiveDate());
        detail.setFlyZoneType(project.getFlyZoneType());
        detail.setPolicyNumber(project.getPolicyNumber());
        Long insuranceCompany = project.getInsuranceCompany();
        String insuredBy = null;
        if (insuranceCompany != null) {
            Company company = companyService.getById(insuranceCompany);
            if (company != null) {
                insuredBy = company.getCompanyName();
            }
        }
        detail.setInsuredBy(insuredBy);
        // Pilot
        detail.setNeedPilot(project.isNeedPilot());
        detail.setHasPilot(false);
        for (UserTinyVo m : members) {
            if (m.getRoleId() == RoleEnum.PILOT.getCode()) {
                detail.setHasPilot(true);
                break;
            }
        }

        detail.setMembers(members);
        detail.setInspectionTime(project.getInspectionTime());

        // Images
        List<String> thumbnails = new ArrayList<String>();
        for (ProjectImageAnnotationVo image : images) {
            thumbnails.add(image.getFileNameLowerResolution());
        }
        detail.setImages(thumbnails);
        detail.setImagesTotal(imagesTotal);

        // Estimate
        detail.setClaimType(project.getClaimType());
        detail.setProjectType(project.getProjectType());
        detail.setDamageSeverity(project.getDamageSeverity());
        // reports
        List<StringIdNameDto> reportInfos = new ArrayList<>();
        for (ProjectReportFileTinyVo report : reports) {
            reportInfos.add(new StringIdNameDto(report.getReportId(), report.getReportName()));
        }
        detail.setReports(reportInfos);
        Optional.ofNullable(batchItem).ifPresent(o -> detail.setBatchNo(o.getBatchNo()));
        return detail;
    }

    @Override
    public void changeMembers(long userId, long projectId, MemberSchedule members)
            throws ServiceException {
        List<MemberScheduleItem> memberItems = members.getMembers();
        if (memberItems == null || memberItems.isEmpty()) {
            return;
        }
        List<Member> curMembers = memberMapper.listActiveMember(projectId);
        Project project = projectMapper.getById(projectId);

        // do not assign pilot for canceled project
        if (NewProjectStatusEnum.PROJECT_CANCELED
                == NewProjectStatusEnum.getEnum(project.getProjectStatus())) {
            throw new ServiceException(MessageCode.PROJECT_CANNOT_OPERATE);
        }

        Map<Integer, Member> curRoleMemberMap = new HashMap<Integer, Member>();
        for (Member m : curMembers) {
            curRoleMemberMap.put(m.getRole(), m);
        }
        Set<Integer> roleAssigned = new HashSet<Integer>();
        for (MemberScheduleItem scheduleItem : memberItems) {
            Integer roleId = scheduleItem.getRoleId();
            if (roleAssigned.contains(roleId)) {
                continue;
            }
            roleAssigned.add(roleId);
            RoleEnum role = (roleId == null ? null : RoleEnum.getEnum(roleId));
            if (role == null
                    || !roleCanBeAssignedToProject(role)
                    || !checkUserRoleAssignmentAuthority(userId, role)) {
                continue;
            }
            Member m = curRoleMemberMap.get(roleId);
            Long curMemberUserId = (m == null ? null : m.getUserId());
            if (scheduleItem.getUserId() == null) {
                arrangeWorkmanAndInfoHim(project, userId, curMemberUserId, null, role, false);
                continue;
            }
            arrangeWorkmanAndInfoHim(
                    project, userId, curMemberUserId, scheduleItem.getUserId(), role, false);
        }
    }

    @Override
    public void changeMembersToMultiProject(long curUserId, long targetUserId,  int roleId, List<Long> projectIds) throws ServiceException {
        if(CollectionUtils.isEmpty(projectIds)) {
            return;
        }
        User curUser = userService.getUserById(curUserId);
        boolean justCompanyAdmin = isNotAdminButCompanyAdmin(curUser);

        MemberScheduleItem item = new MemberScheduleItem();
        item.setUserId(targetUserId);
        item.setRoleId(roleId);

        List<Project> projects = projectMapper.listProjects(projectIds);

        if (justCompanyAdmin
            && (!canChangeAllTargetProject(curUser, projects) || !canAssignTargetUser(curUser, targetUserId))) {
            throw new ServiceException(MessageCode.ROLE_CANNOT_BE_ASSIGNED);
        }

        Map<Long, com.bees360.user.User> userMap = new HashMap<>();
        Function<Long, com.bees360.user.User> userCacheProvider =
            userId -> userMap.computeIfAbsent(userId, key -> userProvider.findUserById(String.valueOf(userId)));
        for(long projectId : projectIds) {
            changeMemberWithoutRoleCheck(curUserId, projectId, item, userCacheProvider);
        }
    }

    @Override
    public void changeMember(long userId, long projectId, MemberScheduleItem scheduleItem)
        throws ServiceException {
        User user = userService.getUserById(userId);
        if(!user.hasAnyRole(RoleEnum.ADMIN, RoleEnum.SALESMAN) && user.hasRole(RoleEnum.COMPANY_ADMIN)) {
            changeMemberByCompanyAdmin(userId, projectId, scheduleItem);
        } else {
            changeMemberWithoutRoleCheck(userId, projectId, scheduleItem);
        }
    }

    private void changeMemberByCompanyAdmin(long userId, long projectId, MemberScheduleItem scheduleItem) throws ServiceException {
        User curUser = userService.getUserById(userId);
        if (!canAssignTargetUser(curUser, scheduleItem.getUserId())) {
            throw new ServiceException(MessageCode.ROLE_CANNOT_BE_ASSIGNED);
        }

        changeMemberWithoutRoleCheck(userId, projectId, scheduleItem);
    }

    /**
     * Check whether it is a deletion operation, or whether the operator operates the company's projects and members
     *
     * @param curUser operator
     * @param targetUserId The person being operated on
     */
    private boolean canAssignTargetUser(User curUser, Long targetUserId) {
        // delete member
        if (targetUserId == null) {
            return true;
        }
        User targetUser = userService.getUserById(targetUserId);
        if(targetUser == null) {
            throw new UncheckedServiceException(new ServiceException(MessageCode.USER_NOT_EXISTED));
        }
        return Objects.equals(curUser.getCompanyId(), targetUser.getCompanyId());
    }

    /**
     * Check whether the operator is operating our company's project
     *
     * @param curUser operator
     * @param project project
     */
    private boolean canChangeTargetProject(User curUser, Project project) {
        return Objects.equals(curUser.getCompanyId(), project.getInsuranceCompany())
            || Objects.equals(curUser.getCompanyId(), project.getRepairCompany());
    }

    /**
     * Check whether the operator is operating our company's project
     *
     * @param curUser operator
     * @param projects projects
     */
    private boolean canChangeAllTargetProject(User curUser, List<Project> projects) {
        return projects.stream().allMatch(project -> canChangeTargetProject(curUser, project));
    }

    private boolean isNotAdminButCompanyAdmin(User user) {
        return !user.hasAnyRole(RoleEnum.ADMIN, RoleEnum.SALESMAN)
            && user.hasRole(RoleEnum.COMPANY_ADMIN);
    }

    @Override
    public void changeMemberWithoutRoleCheck(
            long userId, long projectId, MemberScheduleItem scheduleItem) throws ServiceException {
        changeMemberWithoutRoleCheck(userId, projectId, scheduleItem, uid -> userProvider.findUserById(String.valueOf(uid)));
    }

    private void changeMemberWithoutRoleCheck(
        long userId, long projectId, MemberScheduleItem scheduleItem, Function<Long, com.bees360.user.User> userCacheProvider) throws ServiceException {
        int roleId = scheduleItem.getRoleId();
        RoleEnum role = RoleEnum.getEnum(roleId);
        Member curMember = memberMapper.getActiveMemberByRole(projectId, roleId);
        Project project = projectMapper.getById(projectId);

        Long curMemberUserId = (curMember == null ? null : curMember.getUserId());
        if (scheduleItem.getUserId() == null) {
            arrangeWorkmanAndInfoHim(project, userId, curMemberUserId, null, role, false);
            if (Objects.nonNull(curMemberUserId)) {
                eventHistoryService.insertHistoryToProject(
                    ProjectStatusEnum.PILOT_UNASSIGNED, projectId, userId);
            }
            return;
        }
        arrangeWorkmanAndInfoHim(project, userId, curMemberUserId, scheduleItem.getUserId(), role, false, userCacheProvider);
    }

    private boolean checkUserRoleAssignmentAuthority(long userId, RoleEnum role) {
        User user = userService.getUserById(userId);
        if (user.isAI()) {
            return true;
        }
        boolean isAdmin = user.hasRole(RoleEnum.ADMIN) || user.hasRole(RoleEnum.SUPERADMIN);
        boolean isCompanyAdmin = user.hasRole(RoleEnum.COMPANY_ADMIN);
        if (isAdmin) {
            return true;
        }
        long companyId = Optional.ofNullable(user.getCompanyId()).orElse(0L);
        boolean companyCustomCondition = companyCustomConditions(companyId);
        return (isCompanyAdmin
                        && companyAdminAssignableRoles.contains(role))
                || companyCustomCondition;
    }

    @Override
    public void updateProjectCompanyId(long userId, long oldCompanyId, long newCompanyId) {
        if (userId == 0 || newCompanyId == 0) {
            return;
        }
        try {
            projectMapper.updateProjectCompanyId(userId, oldCompanyId, newCompanyId);
        } catch (Exception e) {
            String message =
                    new StringBuffer(100)
                            .append("failed to update project's companId from ")
                            .append(oldCompanyId)
                            .append(" to ")
                            .append(newCompanyId)
                            .toString();
            logger.error(message);
            throw new RuntimeException(message);
        }
    }

    private boolean roleCanBeAssignedToProject(RoleEnum role) {
        return (role != null) && (role.getType() == RoleType.CAREER);
    }

    @Override
    public void updateChimney(long projectId, int chimney) {
        projectMapper.updateChimney(projectId, chimney);
    }

    @Override
    public void updateRotationDegree(long projectId, double rotationDegree) {
        projectMapper.updateRotationDegree(projectId, rotationDegree);
    }

    @Override
    public Address getProjectAddress(long projectId) throws ServiceException {
        Project project = projectMapper.getById(projectId);
        Address address = new Address();
        address.setAddress(project.getAddress());
        address.setCity(project.getCity());
        address.setState(project.getState());
        address.setZipCode(project.getZipCode());
        address.setLat(project.getGpsLocationLatitude());
        address.setLng(project.getGpsLocationLongitude());
        return address;
    }

    @Override
    public Address updateProjectAddress(long projectId, Address address) throws ServiceException {
        Map<String, Object> fields = new HashMap<String, Object>();
        fields.put("address", address.getAddress());
        fields.put("city", address.getCity());
        fields.put("state", address.getState());
        fields.put("zipCode", address.getZipCode());
        fields.put("gpsLocationLatitude", address.getLat());
        fields.put("gpsLocationLongitude", address.getLng());

        projectMapper.update(projectId, fields);
        Project latest = projectMapper.getById(projectId);
        publisher.publishEvent(new ProjectAddressChangeEvent(this, latest));
        return getProjectAddress(projectId);
    }

    @Override
    public void updateImagesArchiveUrl(long projectId, String imagesArchiveUrl)
            throws ServiceException {

        try {
            projectMapper.updateImagesArchiveUrl(projectId, imagesArchiveUrl);
        } catch (Exception e) {
            logger.error("Fail to updateImagesArchiveUrl for project " + projectId, e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION, e);
        }
    }

    @Override
    public String getImagesArchiveUrl(long projectId, long userId) {
        Project project = projectMapper.getById(projectId);
        if (project == null) {
            throw new ResourceNotFoundException();
        }
        String imageArchiveUrl = projectMapper.getImagesArchiveUrl(projectId);
        if (StringUtils.isEmpty(imageArchiveUrl)) {
            throw new ResourceNotFoundException("The archive is not found.");
        }

        String imageArchiveKey =
                projectImageArchiveKeyConverter.transform(
                        imageArchiveUrl, systemConfig.getWebServer());
        boolean existed = resourcePool.head(imageArchiveKey) != null;
        if (!existed) {
            throw new ResourceNotFoundException(imageArchiveKey + " is not found.");
        }
        return imageArchiveKey;
    }

    @Override
    public ProjectInsuredInfoVo getInsuredInfo(long projectId) throws ServiceException {
        Project project = getById(projectId);
        ProjectInsuredInfoVo insuredInfoVo = ProjectConverter.toProjectInsuredInfoVo(project);

        Optional<BoundProjectLabel> projectLabel = projectLabelService.projectLabel(projectId);
        projectLabel.ifPresent(
                boundProjectLabel ->
                        insuredInfoVo.setProjectLabels(boundProjectLabel.getProjectLabels()));
        return insuredInfoVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectInsuredInfoVo updateInsuredInfo(
            long projectId, long userId, ProjectInsuredInfoVo insuredInfoVo)
            throws ServiceException {
        return updateInsuredInfo(projectId, userId, insuredInfoVo, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectInsuredInfoVo updateInsuredInfo(
            long projectId, long userId, ProjectInsuredInfoVo insuredInfoVo, boolean saveSolidData)
            throws ServiceException {
        Project project = projectMapper.getById(projectId);
        AssertUtil.notNull(project, "The project is not existed.");
        Map<String, Object> data = new HashMap<>();

        String newPlace =
                insuredInfoVo.getAddress() + ", "
                        + insuredInfoVo.getCity() + ", "
                        + insuredInfoVo.getState() + " "
                        + insuredInfoVo.getZipCode();
        String oldPlace =
                project.getAddress() + ", "
                        + project.getCity() + ", "
                        + project.getState() + " "
                        + project.getZipCode();
        insuredInfoVo.setAddressId(project.getAddressId());
        var oldAddress = insuredInfoVo.getAddress();
        var oldCity = insuredInfoVo.getCity();
        var oldState = insuredInfoVo.getState();
        var oldZipcode = insuredInfoVo.getZipCode();
        if (!Objects.equals(newPlace, oldPlace)) {
            var addressVo = normalizedAddress(insuredInfoVo);
            if (bees360FeatureSwitch.isEnableCorrectZipcode()) {
                insuredInfoVo.setZipCode(addressVo.getZip());
                insuredInfoVo.setCity(addressVo.getCity());
                insuredInfoVo.setState(addressVo.getState());
                insuredInfoVo.setCountry(addressVo.getCountry());
                if (addressVo.getTimeZone() != null) {
                    insuredInfoVo.setTimeZone(addressVo.getTimeZone().getID());
                }
            }
            data.put("state", addressVo.getState());
            data.put("country", addressVo.getCountry());
            data.put("addressId", addressVo.getId());
            data.put("gpsIsApproximate", defaultIfNull(addressVo.isGpsApproximate(), true));
            insuredInfoVo.setAddressId(addressVo.getId());
            insuredInfoVo.setLat(defaultIfNull(addressVo.getLat(), 0.0d));
            insuredInfoVo.setLng(defaultIfNull(addressVo.getLng(), 0.0d));
        }
        data.put("zipCode", insuredInfoVo.getZipCode());
        data.put("city", insuredInfoVo.getCity());
        data.put("address", insuredInfoVo.getAddress());
        data.put("gpsLocationLatitude", insuredInfoVo.getLat());
        data.put("gpsLocationLongitude", insuredInfoVo.getLng());
        data.put("timeZone", insuredInfoVo.getTimeZone());

        data.put("assetOwnerName", insuredInfoVo.getAssetOwnerName());
        data.put("assetOwnerEmail", insuredInfoVo.getAssetOwnerEmail());
        data.put("assetOwnerPhone", insuredInfoVo.getAssetOwnerPhone());
        data.put("projectType", insuredInfoVo.getProjectType());
        data.put("dueDate", insuredInfoVo.getDueDate());

        var oldInsuredInfo = getInsuredInfo(projectId);
        if (saveSolidData) {
            projectIIServiceAdapter.updateProjectInsuredInfo(projectId, insuredInfoVo, oldInsuredInfo);
        }

        projectMapper.updateByMap(projectId, data);
        var newProject = projectMapper.getById(projectId);
        newProject.setInsuredFirstName(insuredInfoVo.getInsuredFirstName());
        newProject.setInsuredMiddleName(insuredInfoVo.getInsuredMiddleName());
        newProject.setInsuredLastName(insuredInfoVo.getInsuredLastName());

        if (saveSolidData) {
            addOrUpdateAgentAndInsuredInContactManager(String.valueOf(userId), project, newProject);
        }
        updateInsuredName(newProject);

        publisher.publishEvent(new ProjectAddressChangeEvent(this, newProject, userId, oldPlace, newPlace));
        projectService.transferDatasToAi(projectId, ProjectSyncPointEnum.MANUAL_SYNC.getType());
        checkProjectAddressFormatted(oldAddress, oldCity, oldState, oldZipcode, newProject);
        return getInsuredInfo(projectId);
    }

    @Override
    public ProjectInspectionInfoVo getInspectionInfo(long projectId) throws ServiceException {
        Project project = getById(projectId);
        Company insuranceCompany =
                project.getInsuranceCompany() == null
                        ? null
                        : companyMapper.getById(project.getInsuranceCompany());
        Company repairCompany =
                project.getRepairCompany() == null
                        ? null
                        : companyMapper.getById(project.getRepairCompany());
        ProjectInspectionInfoVo infoVo =
                ProjectConverter.toProjectInspectionInfoVo(
                        project, insuranceCompany, repairCompany);
        var projectStatusTimeLine = projectStatusService.listProjectStatusTimeLines(List.of(projectId)).get(projectId);
        if (projectStatusTimeLine != null) {
            if (projectStatusTimeLine.getSiteInspected() != null) {
                Long siteInspectedTime = projectStatusTimeLine.getSiteInspected().getCreatedTime();
                infoVo.setSiteInspectedTime(siteInspectedTime);
            }
        }
        Optional.ofNullable(project.getInitialCustomerContactTime())
                .ifPresent(infoVo::setInitialCustomerContactedTime);
        Optional.ofNullable(beesPilotBatchItemService.findByProjectId(projectId))
                .ifPresent(o -> infoVo.setBatchNo(o.getBatchNo()));
        var inspectionScheduled = projectInspectionScheduleMapper.getByProjectId(projectId);
        Optional.ofNullable(inspectionScheduled).ifPresent(data -> {
            infoVo.setScheduledTime(data.getScheduledTime());
            infoVo.setInspectionDueDate(data.getDueDate());
        });
        return infoVo;
    }

    @Override
    public ProjectInspectionInfoVo updateInspectionInfo(
            long projectId, long userId, ProjectInspectionInfoVo inspectionInfoVo)
            throws ServiceException {
        return updateInspectionInfo(projectId, userId, inspectionInfoVo, true);
    }

    @Override
    public ProjectInspectionInfoVo updateInspectionInfo(
            long projectId, long userId, ProjectInspectionInfoVo inspectionInfoVo, boolean saveSolidData)
            throws ServiceException {
        ProjectInspectionInfoVo existInfo = projectService.getInspectionInfo(projectId);
        Map<String, Object> data = new HashMap<>();
        data.put("policyNumber", inspectionInfoVo.getPolicyNumber());
        data.put("inspectionNumber", inspectionInfoVo.getInspectionNumber());

        Integer claimType;
        if ((claimType = inspectionInfoVo.getClaimType()) != null) {
            if (!ClaimTypeEnum.exist(claimType)) {
                throw new ServiceMessageException(
                        PARAM_INVALID, "Param claimType doesn't have a value: " + claimType);
            }
            data.put("claimType", claimType);
        }

        data.put("damageEventTime", inspectionInfoVo.getDamageEventTime());

        data.put("agent", inspectionInfoVo.getAgent());
        data.put("agentContactName", inspectionInfoVo.getAgentContactName());
        data.put("agentEmail", inspectionInfoVo.getAgentEmail());
        data.put("agentPhone", inspectionInfoVo.getAgentPhone());
        data.put("policyEffectiveDate", inspectionInfoVo.getPolicyEffectiveDate());
        data.put("yearBuilt", inspectionInfoVo.getYearBuilt());

        projectIIServiceAdapter.checkUpdateProjectInspectionInfo(
                projectId, inspectionInfoVo, existInfo);
        if (saveSolidData) {
            projectIIServiceAdapter.updateProjectInspectionInfo(
                    projectId, inspectionInfoVo, existInfo);
        }
        Project oldProject = projectMapper.getById(projectId);

        projectMapper.updateByMap(projectId, data);

        var userIdStr = String.valueOf(userId);
        var project = projectMapper.getById(projectId);
        if (saveSolidData) {
            addOrUpdateAgentAndInsuredInContactManager(userIdStr, oldProject, project);
        }
        dispatchEventsWhenProjectChanged(userIdStr, oldProject, project);
        publisher.publishEvent(
                new ProjectTemplateForMissionChangeEvent(this, projectMapper.getById(projectId)));

        projectService.transferDatasToAi(projectId, ProjectSyncPointEnum.MANUAL_SYNC.getType());
        return getInspectionInfo(projectId);
    }

    private void addOrUpdateAgentAndInsuredInContactManager(
            String userId, Project oldProject, Project newProject) {
        var projectId = String.valueOf(oldProject.getProjectId());
        var newAgent = ProjectAssemble.getAgent(newProject);
        var oldAgent = ProjectAssemble.getAgent(oldProject);
        if (!ProjectAssemble.contactEquals(newAgent, oldAgent)) {
            addOrUpdateAgentOrInsured(userId, projectId, newAgent);
        }
        var newInsured = ProjectAssemble.getInsured(newProject);
        var oldInsured = ProjectAssemble.getInsured(oldProject);
        if (!ProjectAssemble.contactEquals(newInsured, oldInsured)) {
            addOrUpdateAgentOrInsured(userId, projectId, newInsured);
        }
    }

    private void addOrUpdateAgentOrInsured(String userId, String projectId, Contact newContact) {
        if (newContact == null) {
            return;
        }
        var role = newContact.getRole();
        var contacts = Iterables.toList(contactManager.findByProjectId(projectId));
        // Considering there is only one agent or insured
        var contactInContactRepository =
                contacts.stream()
                        .filter(c -> role.equals(c.getRole()) && Objects.nonNull(c.getId()))
                        .findAny()
                        .orElse(null);
        var newContactMsg = Messages.toShortDebugString(newContact.toMessage());
        if (contactInContactRepository == null) {
            logger.info("Start to add project '{}' contact '{}'", projectId, newContactMsg);
            contactManager.addContact(projectId, newContact, userId);
        } else if (!ProjectAssemble.contactEquals(newContact, contactInContactRepository)) {
            logger.info("Start to update project '{}' contact '{}'", projectId, newContactMsg);
            var contactId = contactInContactRepository.getId();
            if (contactId == null) {
                logger.error(
                        "Failed to update project '{}' contact {}  to contactManager because contactId is null.",
                        projectId,
                        Messages.toShortDebugString(contactInContactRepository.toMessage()));
                return;
            }
            newContact = Contact.of(newContact.toMessage().toBuilder().setId(contactId).setIsPrimary(contactInContactRepository.isPrimary()).build());
            contactManager.updateContact(newContact, userId);
        }
    }

    private void dispatchEventsWhenProjectChanged(String userId, Project oldProject, Project newProject) {

        if (!Objects.equals(newProject.getInspectionNumber(), oldProject.getInspectionNumber())) {
            publisher.publishEvent(new ProjectInspectionNumberChangedEvent(this, newProject));
        }
        if (!Objects.equals(newProject.getInsuranceCompany(), oldProject.getInsuranceCompany())) {
            publisher.publishEvent(new ProjectInsureCompanyChangedEvent(this, newProject, oldProject));
        }
        if (!Objects.equals(newProject.getRepairCompany(), oldProject.getRepairCompany())) {
            publisher.publishEvent(new ProjectRepairCompanyChangedEvent(this, newProject, oldProject));
        }
        if (isProjectAddressChanged(oldProject, newProject)) {
            publisher.publishEvent(new ProjectAddressChangeEvent(this, newProject));
        }
        if (!Objects.equals(newProject.getPolicyEffectiveDate(), oldProject.getPolicyEffectiveDate())) {
            LocalDate oldPolicyEffectiveDate = oldProject.getPolicyEffectiveDate();
            LocalDate newPolicyEffectiveDate = newProject.getPolicyEffectiveDate();
            ProjectPolicyEffectedDateChangeEvent event = new ProjectPolicyEffectedDateChangeEvent()
                .setProjectId(newProject.getProjectId())
                .setOperator(userId)
                .setOldPolicyEffectiveDate(oldPolicyEffectiveDate)
                .setNewPolicyEffectiveDate(newPolicyEffectiveDate);
            eventPublisher.publish(event);
        }
    }

    private boolean isProjectAddressChanged(Project oldProject, Project newProject) {
        boolean gpsSame =
                Objects.equals(newProject.getLat(), oldProject.getLat())
                        && Objects.equals(newProject.getLng(), oldProject.getLng());
        if (!gpsSame) {
            return true;
        }
        boolean addressSame =
                Objects.equals(newProject.getAddress(), oldProject.getAddress())
                        && Objects.equals(newProject.getCity(), oldProject.getCity())
                        && Objects.equals(newProject.getState(), oldProject.getState())
                        && Objects.equals(newProject.getZipCode(), oldProject.getZipCode())
                        && Objects.equals(newProject.getCountry(), oldProject.getCountry());

        return !addressSame;
    }

    @Override
    public void updateInspectionDueDate(long operator, long projectId, Long inspectionTime) {
        Project project = projectMapper.getById(projectId);
        // Get old and new values
        Long oldInspectionTime = project.getInspectionTime();
        // Update the InspectionTime of the project
        project.setInspectionTime(inspectionTime);
        projectMapper.updateInspectionTime(projectId, inspectionTime);
        // Update postgres synchronously
        LocalDate inspectionDueDate = inspectionTime == null ?
            null :  LocalDate.ofInstant(Instant.ofEpochMilli(inspectionTime), ZoneId.of("US/Central"));
        projectIIManager.updateDueDate(String.valueOf(projectId), inspectionDueDate);
        publishInspectionTimeChangedAndTransferToAi(operator, oldInspectionTime, inspectionTime, project);
    }

    @Override
    public void updateInspectionScheduledTime(long operator, long projectId, Long inspectionTime) {
        Project project = projectMapper.getById(projectId);
        // Get old and new values
        Long oldInspectionTime = project.getInspectionTime();
        // Update the InspectionTime of the project
        project.setInspectionTime(inspectionTime);
        projectMapper.updateInspectionTime(projectId, inspectionTime);
        // Update postgres synchronously
        Instant inspectionTimestamp = inspectionTime == null ? null : Instant.ofEpochMilli(inspectionTime);
        projectIIManager.updateScheduledTime(String.valueOf(projectId), inspectionTimestamp);
        publishInspectionTimeChangedAndTransferToAi(operator, oldInspectionTime, inspectionTime, project);
    }

    private void publishInspectionTimeChangedAndTransferToAi(long operator, Long oldInspectionTime, Long inspectionTime, Project project) {
        boolean operatedFromFirebase = isOperationFromFirebase(operator);
        if (!Objects.equals(oldInspectionTime, inspectionTime)) {
            publisher.publishEvent(new ProjectInspectionTimeChangedEvent(this, operator, project, operatedFromFirebase,
                oldInspectionTime, inspectionTime));
        }
        try {
            projectService.transferDatasToAi(project.getProjectId(), ProjectSyncPointEnum.MANUAL_SYNC.getType());
        } catch (ServiceException e) {
            throw new IllegalStateException("Failed to transfer data to ai.", e);
        }
    }

    private boolean isOperationFromFirebase(long operator) {
        return operator == User.BEES_PILOT_SYSTEM;
    }

    @Override
    public Map<String, Object> getProjectsServiceOptions() {
        List<CodeNameDto> optionsList =
            Arrays.stream(ProjectServiceTypeEnum.values()).map(o -> {
                CodeNameDto options = new CodeNameDto();
                options.setCode(o.getCode());
                options.setName(o.getDisplay());
                return options;
            }).collect(Collectors.toList());
        return ImmutableMap.of("serviceTypes", optionsList);
    }

    @Override
    public ProjectServiceTypeVo getProjectServiceType(long projectId) throws ServiceException {
        Project project = getById(projectId);
        return ProjectConverter.toProjectServiceTypeVo(project);
    }

    @Override
    public ProjectServiceTypeVo updateProjectServiceType(
            long userId, long projectId, ProjectServiceTypeParam serviceTypeParam)
            throws ServiceException {
        return updateProjectServiceType(userId, projectId, serviceTypeParam, true);
    }

    @Override
    public ProjectServiceTypeVo updateProjectServiceType(
            long userId, long projectId, ProjectServiceTypeParam serviceTypeParam, boolean saveSolidData)
            throws ServiceException {
        if (serviceTypeParam.getServiceType() == null) {
            throw new ServiceMessageException(PARAM_INVALID, "serviceType is null");
        }
        Project project = projectMapper.getById(projectId);
        if (Objects.isNull(project)) {
            throw new ServiceMessageException(MessageCode.PROJECT_NOT_EXIST, "project not exist");
        }
        return doUpdateProjectServiceType(userId, projectId, serviceTypeParam, saveSolidData);
    }

    private ProjectServiceTypeVo doUpdateProjectServiceType(
            long userId, long projectId, ProjectServiceTypeParam serviceTypeParam, boolean saveSolidData)
            throws ServiceException {
        Project oldProject = projectMapper.getById(projectId);
        if (Objects.isNull(oldProject)) {
            throw new ServiceMessageException(MessageCode.PROJECT_NOT_EXIST, "project not exist");
        }
        Integer oldServiceType = oldProject.getServiceType();
        Map<String, Object> data = new HashMap<>();
        data.put("serviceType", serviceTypeParam.getServiceType());
        projectMapper.updateByMap(projectId, data);
        logger.info(
                "LOOK: the service type of project {} is {}: {}",
                projectId,
                serviceTypeParam.getServiceType(),
                serviceTypeParam);
        if (saveSolidData) {
            projectIIServiceAdapter.updateServiceType(
                projectId, oldServiceType, serviceTypeParam.getServiceType());
        }
        var project = projectMapper.getById(projectId);
        publisher.publishEvent(
                new ProjectTemplateForMissionChangeEvent(
                        this, project, userId == User.BEES_PILOT_SYSTEM));
        publisher.publishEvent(
                new ProjectServiceTypeChangedEvent(
                        this,
                        ProjectServiceTypeEnum.getEnum(oldServiceType),
                        ProjectServiceTypeEnum.getEnum(serviceTypeParam.getServiceType()),
                        projectId, project, oldProject));
        projectService.transferDatasToAi(projectId, ProjectSyncPointEnum.MANUAL_SYNC.getType());
        return getProjectServiceType(projectId);
    }

    @Override
    public boolean isProjectManagedBy(long projectId, long companyId) throws ServiceException {
        Set<Long> managerCompanies = getProjectManageCompany(projectId);
        return managerCompanies.contains(companyId);
    }

    @Override
    public Set<Long> getProjectManageCompany(long projectId) throws ServiceException {
        Project project = projectMapper.getById(projectId);
        if (project == null) {
            throw new ResourceNotFoundException("project " + projectId + " not found.");
        }
        List<Long> managerCompanies =
                Arrays.asList(project.getRepairCompany(), project.getInsuranceCompany());

        return managerCompanies.stream().filter(c -> c != null).collect(Collectors.toSet());
    }

    @Override
    public void updateReportRelevantData(Project project) {
        projectMapper.updateReportRelevantData(project);
    }

    @Override
    public String getChecklistUrl(long projectId) throws ServiceException {
        Project project = projectMapper.getById(projectId);
        AssertUtil.notNull(project, MessageCode.PRODUCT_NOT_EXIST, "project is not existed");
        ProjectServiceTypeEnum serviceTypeEnum =
                ProjectServiceTypeEnum.getEnum(project.getServiceType());
        // serviceTypeEnum shouldn`t be null
        if (serviceTypeEnum == null) {
            return null;
        }
        return serviceTypeEnum.getChecklistUrl();
    }

    @Override
    public ProjectPageResultVo getExportProjectList(long userId, ProjectSearchOption searchOption)
            throws ServiceException {
        ProjectSearchResultFilter filter = ProjectSearchResultFilter.allInclude();
        filter.setWithMainImage(false);
        ProjectPageResultVo pageResult = pageTinyProjectSearchWithFilter(userId, searchOption, filter);;

        if (CollectionUtils.isEmpty(pageResult.getProjects())) {
            return pageResult;
        }
        fillPolicyType(pageResult.getProjects());
        fillScheduledTimeChangedCount(pageResult.getProjects());
        fillCatLevel(pageResult.getProjects());
        return pageResult;
    }

    private void fillPolicyType(List<ProjectTinyVo> tinyProjects) {
        Set<String> projectIds = tinyProjects.stream().map(project -> project.getProjectId() + "")
            .collect(Collectors.toSet());
        Map<Long, Policy> projectId2Policy = Iterables.toStream(projectIIManager.findAllById(projectIds))
            .filter(projectII -> projectII.getId() != null)
            .filter(projectII -> projectII.getPolicy() != null)
            .collect(Collectors.toMap(projectII -> Long.parseLong(projectII.getId()),
                ProjectII::getPolicy));
        tinyProjects.forEach(projectTinyVo -> {
            long projectId = projectTinyVo.getProjectId();
            Optional.ofNullable(projectId2Policy.get(projectId))
                .ifPresent(policy -> projectTinyVo.setPolicyType(policy.getType()));
        });
    }

    private void fillScheduledTimeChangedCount(List<ProjectTinyVo> tinyProjects) {
        if (CollectionUtils.isEmpty(tinyProjects) || !bees360FeatureSwitch.isEnableExportActivityData()) {
            return;
        }

        tinyProjects.forEach(p -> {
            var query = ActivityQuery.builder()
                        .projectId(p.getProjectId())
                        .fieldName(com.bees360.activity.Message.ActivityMessage.FieldName.INSPECTION_TIME.name())
                        .build();
            var activities = activityManager.getActivities(query);

            // Filter out cases where scheduled time is canceled
            var count = Optional.ofNullable(activities)
                .map(
                    ac -> ac.stream()
                            .filter(a -> StringUtils.isNoneEmpty(a.getValue()))
                            .count()).orElse(0L);
            p.setScheduledTimeChangedCount(count);
        });
    }

    @Override
    public void updatePayStatus(long projectId, long userId, int status) throws ServiceException {
        Project project = projectMapper.getById(projectId);
        if (project == null) {
            throw new ServiceException(MessageCode.PROJECT_NOT_EXIST);
        }
        if (status == PayStatusEnum.PAID.getCode()) {
            projectPaymentManager.setProjectsPaid(List.of(String.valueOf(projectId)));
        } else {
            projectPaymentManager.setProjectsUnPaid(List.of(String.valueOf(projectId)));
        }
    }

    @Override
    public void updatePayStatus(List<Long> projectIds, int status) {
        projectMapper.updatePayStatus(projectIds, status);
    }

    @Override
    public void transferDatasToAi(long projectId, String type) {
        publisher.publishEvent(
                new ProjectImageUploadFinishedGrpcEvent(this, projectId, true, type));
    }

    @Override
    public boolean isProjectImageArchiveExist(long projectId) {
        String imageArchiveUrl = projectMapper.getImagesArchiveUrl(projectId);
        if (StringUtils.isEmpty(imageArchiveUrl)) {
            return false;
        }
        String imageArchiveS3Key =
                projectImageArchiveKeyConverter.transform(
                        imageArchiveUrl, systemConfig.getWebServer());
        return resourcePool.head(imageArchiveS3Key) != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectServiceTypeReason(
            long userId, long projectId, ProjectServiceTypeReasonParam serviceTypeParam)
            throws ServiceException {
        Map<String, Object> data = new HashMap<>();
        data.put("serviceTypeReason", serviceTypeParam.getReason());
        projectMapper.updateByMap(projectId, data);
        EventHistory eventHistory =
                eventHistoryService.getEventHistory(
                        ProjectStatusEnum.UPDATE_SERVICE_TYPE,
                        projectId,
                        userId,
                        serviceTypeParam.getReason());
        eventHistoryService.insertHistoryToProject(eventHistory);
    }

    @Override
    public void sendSmsTextToPilot(long userId, long projectId, SendSmsPilotParam pilotParam)
            throws ServiceException {

        if (StringUtils.isBlank(StringUtils.trim(pilotParam.getNotifyMsg()))) {
            throw new ServiceException(MessageCode.OPERATION_FORBIDDEN);
        }
        User creator = userService.getUserById(userId);

        Project project = projectMapper.getById(projectId);
        if (Objects.isNull(project)) {
            throw new ServiceMessageException(MessageCode.PROJECT_NOT_EXIST, "project not exist");
        }
        boolean isAdmin = creator.hasRole(RoleEnum.ADMIN) || creator.hasRole(RoleEnum.SUPERADMIN);
        if (!isAdmin) {
            throw new ServiceException(MessageCode.OPERATION_FORBIDDEN);
        }

        Member member = memberMapper.getActiveMemberByRole(projectId, RoleEnum.PILOT.getCode());
        if (Objects.isNull(member)) {
            throw new ServiceMessageException(PARAM_INVALID, "project pilot not found");
        }
        User user = userService.getUserById(member.getUserId());
        if (Objects.isNull(user) || StringUtils.isBlank(user.getPhone())) {
            throw new ServiceMessageException(PARAM_INVALID, "Pilot or Phone not found");
        }
        messageService.sendSmsToNotifyPilot(user, pilotParam.getNotifyMsg());
    }

    @Override
    public void updateCatNumber(long projectId, String catNumber) {
        Map<String, Object> map = new HashMap<>();
        map.put("catNumber", catNumber);
        projectMapper.updateByMap(projectId, map);
        if (StringUtils.isNotBlank(catNumber)) {
            projectCatastropheManager.addProjectCatastrophe(String.valueOf(projectId), catNumber);
        }
        publisher.publishEvent(new ProjectChangeEvent(this, getById(projectId)));
    }

    // update fly zone type, it should not update from firebase.
    @Override
    public void updateFlyZoneType(long projectId, FlyZoneType flyZoneType) throws ServiceException {
        Map<String, Object> fields = new HashMap<>();
        fields.put("flyZoneType", flyZoneType.getCode());
        if (flyZoneType == FlyZoneType.RESTRICTED_ZONE) {
            fields.put("dueDate", null);
        }
        projectMapper.updateByMap(projectId, fields);
        publisher.publishEvent(new ProjectChangeEvent(this, getById(projectId), false));
    }

    @Override
    public int updateHoverJobId(Long projectId, Long hoverJobId) throws ServiceException {
        if (Objects.isNull(hoverJobId)) {
            throw new ServiceException(PARAM_INVALID, "hover job id can not be null");
        }
        int update = projectMapper.updateHoverJobId(projectId, hoverJobId);

        String content = "jobId: %d for hover is created successfully".formatted(hoverJobId);
        ProjectMessage message =
                ProjectMessage.builder()
                        .createTime(System.currentTimeMillis())
                        .content(content)
                        .projectId(projectId)
                        .senderId(User.AI_ID)
                        .title(ProjectMessageTypeEnum.ADMIN_NOTE.getDisplay())
                        .type(ProjectMessageTypeEnum.ADMIN_NOTE.getCode())
                        .build();
        projectMessageService.addMessageAndSyncToAi(message);

        publisher.publishEvent(new ProjectChangeEvent(this, getById(projectId)));
        return update;
    }

    @Transactional
    @Override
    public void updateProject(CreateOrUpdateProjectDto projectDto) {
        Long projectId = projectDto.getProjectId();
        Preconditions.checkArgument(Objects.nonNull(projectId), "Project's id is required.");

        Project project = projectMapper.getById(projectId);
        projectMapper.updateProject(projectDto);
        Project projectModified = projectMapper.getById(projectId);
        dispatchEventsWhenProjectChanged(getUserId(), project, projectModified);
    }

    private String getUserId() {
        long userId = springSecurityContextProvider.getUserIdFromContext();
        String userIdStr = (userId == 0? "": userId + "");
        return userIdStr;
    }

    @Override
    public boolean projectServiceIsCompleted(long projectId) throws ServiceException {
        Project project = projectMapper.getById(projectId);
        ProjectServiceTypeEnum projectServiceType =
                ProjectServiceTypeEnum.getEnum(project.getServiceType());
        List<ProjectReportFile> reports = projectReportFileService.getAllReportFiles(projectId);
        Set<Integer> reportsCompleted =
                reports.stream()
                        .filter(
                                r ->
                                        ReportGenerationStatusEnum.getEnum(r.getGenerationStatus())
                                                .isCompleted())
                        .map(r -> r.getReportType())
                        .collect(Collectors.toSet());

        return projectServiceType.getReportTypes().stream()
                .allMatch(t -> reportsCompleted.contains(t.getCode()));
    }

    @Override
    public void updateInvoiceFile(long projectId, long userId, String invoiceFile) throws ServiceException {
        logger.info("Change the invoice of {} to {}.", projectId, invoiceFile);
        if (StringUtils.isBlank(invoiceFile)) {
            projectReportFileService.deleteReport(projectId, userId, ReportTypeEnum.INVOICE);
            return;
        }
        ProjectReportFileTinyVo reportFile =
                projectReportFileService.getReportFile(projectId, ReportTypeEnum.INVOICE.getCode());
        if (Objects.nonNull(reportFile) && StringUtils.equals(reportFile.getReportUrl(), invoiceFile)) {
            return;
        }

        ResourceMetadata metadata = resourcePool.head(invoiceFile);
        if (metadata == null) {
            throw new ResourceNotFoundException("Invoice file %s not found.".formatted(invoiceFile));
        }

        ProjectReportFileDto report = new ProjectReportFileDto();
        report.setReportType(ReportTypeEnum.INVOICE.getCode());
        report.setReportUrl(invoiceFile);
        report.setSize(Math.toIntExact(metadata.getContentLength()));
        projectReportFileService.createOrReplaceReportFile(userId, projectId, report);
    }

    @Override
    public InvoiceFileVo getInvoiceFile(long projectId) {
        ProjectReportFileTinyVo reportFile =
                projectReportFileService.getReportFile(projectId, ReportTypeEnum.INVOICE.getCode());
        Project project = getById(projectId);
        String invoiceKey =
                Optional.ofNullable(reportFile)
                        .map(ProjectReportFileTinyVo::getReportUrl)
                        .orElse("");
        return StringUtils.isBlank(invoiceKey)
                ? null
                : new InvoiceFileVo()
                        .setFileName("invoice - " + project.getInspectionNumber() + ".pdf")
                        .setInvoiceFile(invoiceKey);
    }

    @Override
    public void updatePolicyEffectedDate(long projectId, LocalDate policyEffectiveDate) {
        Project project = getById(projectId);
        Map<String, Object> fields = new HashMap<>();
        fields.put("policyEffectiveDate", policyEffectiveDate);
        projectMapper.updateByMap(projectId, fields);
        projectIIServiceAdapter.updatePolicyEffectiveDate(projectId, policyEffectiveDate);
        dispatchEventsWhenProjectChanged(getUserId(), project, projectMapper.getById(projectId));
    }

    @Override
    public boolean isPilotJobCompleted(long projectId) {
        ProjectStatusTimeLineDto timeline = null;
        try {
            timeline = projectStatusService.getProjectStatusTimeLine(projectId);
        } catch (ServiceException ex) {
            throw new UncheckedServiceException(ex);
        }
        boolean imageUpload = Optional.ofNullable(timeline).map(ProjectStatusTimeLineDto::getImageUploaded).map(s -> s.getCreatedTime() != null).orElse(false);
        if (!imageUpload) {
            imageUpload = Optional.ofNullable(timeline).map(ProjectStatusTimeLineDto::getIBEESUploaded).map(s -> s.getCreatedTime() != null).orElse(false);
        }
        // pilot job completed after image uploaded
        return imageUpload;
    }

    private static final String INSPECTION_CODE_COLLECTION = "inspection_code";

    @Override
    public void resendInspectionCode(long userId, long projectId) {
        var project = projectService.findById(projectId);
        var inspectionCode = project.map(Project::getInspectionCode).orElse(null);
        if (inspectionCode == null) {
            throw new IllegalStateException("The project '%s' is not supported to resend inspection code.".formatted(projectId));
        }
        var now = System.currentTimeMillis();
        var result =
                firestore
                        .collection(INSPECTION_CODE_COLLECTION)
                        .document(inspectionCode)
                        .update(
                                Map.of(
                                        "webProjectId",
                                        String.valueOf(projectId),
                                        // webTrigger field represent triggered by web
                                        "webTrigger",
                                        now,
                                        "updateTime",
                                        now));
        try {
            logger.info(
                    "Successfully triggered project '{}' resend inspection code '{} at '{}'",
                    projectId,
                    inspectionCode,
                    result.get().getUpdateTime());
        } catch (InterruptedException | ExecutionException e) {
            throw new IllegalStateException(
                "Failed to trigger project %s resend inspection code %s".formatted(
                    projectId, inspectionCode),
                    e);
        }
        projectInspectionService.sendCreateProjectInspectionActivity(
                userId, projectId, inspectionCode);
    }

    @Override
    public List<Long> getProjectIdsOfFollowUp() {
        ArrayList<ProjectTinyVo> list = new ArrayList<>();
        final int pageSize = 1000;
        Supplier<ProjectSearchOption> projectSearchOptionSupplier = () -> {
            ProjectSearchOption projectSearchOption = new ProjectSearchOption();
            projectSearchOption.setServiceTypes(new ArrayList<>(ProjectServiceTypeEnum.getClaimCodes()));
            projectSearchOption.setPageSize(pageSize);
            projectSearchOption.setPageIndex(1);
            projectSearchOption.setTestFlag(Boolean.FALSE);
            return projectSearchOption;
        };

        Function<ProjectSearchOption, List<ProjectTinyVo>> getByPage = (option) -> {
            List<ProjectTinyVo> projects;
            List<ProjectTinyVo> total = new ArrayList<>();
            do {
                projects = this.getProjects(option);
                total.addAll(projects);
                option.setPageIndex(option.getPageIndex() + 1);
            } while (projects.size() == pageSize);
            return total;
        };

        //Latest status is Customer Contacted: Output claims tagged Wrong number tag, Pending to Schedule,
        //Pending to Reschedule, Denied or Denied Drone Inspection
        ProjectSearchOption projectSearchOption = projectSearchOptionSupplier.get();
        projectSearchOption.setProjectLabels(PROJECT_LABELS_OF_FOLLOW_UP_CUSTOMER_CONTACTED);
        projectSearchOption.setProjectStatus(NewProjectStatusEnum.CUSTOMER_CONTACTED.getCode());
        list.addAll(getByPage.apply(projectSearchOption));

        //The latest status is Assigned to Pilot, Site Inspected, and Rework: Output claims with a value greater than or equal to 1 between the current date and the Inspection Scheduled date
        List<Integer> statusList = List.of(ASSIGNED_TO_PILOT.getCode(), SITE_INSPECTED.getCode(), PROJECT_REWORK.getCode());
        projectSearchOption = projectSearchOptionSupplier.get();
        projectSearchOption.setProjectStatusList(statusList);
        Instant now = Instant.now();
        projectSearchOption.setInspectionEndTime(now.minus(Duration.ofDays(1)).toEpochMilli());
        list.addAll(getByPage.apply(projectSearchOption));

        // The latest status is Image Uploaded: Claims with tag Missing images or missing roof closeups.
        Set<String> projectTagIds = getProjectTagIds(PROJECT_TAGS_OF_FOLLOW_UP_IMAGE_UPLOADED);
        List<Long> projectIdsFromTags = projectTagManager.findAllProjectIdByTagId(projectTagIds)
            .stream().map(Long::parseLong).collect(Collectors.toList());
        projectSearchOption = projectSearchOptionSupplier.get();
        projectSearchOption.setProjectIds(projectIdsFromTags);
        projectSearchOption.setProjectStatus(NewProjectStatusEnum.IMAGE_UPLOADED.getCode());
        list.addAll(getByPage.apply(projectSearchOption));

        // For claims with DaysOld greater than or equal to 3 days, the status is still Project Created or Customer Contacted
        projectSearchOption = projectSearchOptionSupplier.get();
        projectSearchOption.setProjectStatusList(List.of(PROJECT_CREATED.getCode(), CUSTOMER_CONTACTED.getCode()));
        if (bees360FeatureSwitch.isEnableOpenCloseRead()) {
            projectSearchOption.setNewDaysOldStart(3);
        } else {
            projectSearchOption.setDaysOldStart(3);
        }
        list.addAll(getByPage.apply(projectSearchOption));

        // search cases whose insured phone is empty
        ProjectFilterQuery query = new ProjectFilterQuery();
        query.setSearchAssetOwnerWithoutPhone(true);
        query.setIsTestCase(false);
        query.setProjectServiceType(ProjectServiceTypeEnum.getClaimCodes());
        query.setExclusiveStatuses(EXCLUDE_STATUS_OF_FOLLOW_UP_SEARCH);
        Company company = companyService.getByName("American Family");
        if (company == null) {
            throw new IllegalStateException("No company named 'American Family'");
        }
        query.setInsuranceCompany(company.getCompanyId());
        List<Long> projectIdsOfMissingInsuredPhone = projectMapper.listProjectId(query);

        // search cases with project label: Attention Needed for Loss Type: id=27
        projectSearchOption = projectSearchOptionSupplier.get();
        projectSearchOption.setProjectLabels(List.of(27L));
        list.addAll(getByPage.apply(projectSearchOption));


        // search cases with Pipeline task PilotRecommendation status of ERROR, which means
        // that no recommended pilots can be found for those case.
        Stream<Long> projectIdsStreamOfPilotShortage =
                Iterables.toStream(
                                pipelineService.findPipelineIdByTask(
                                        PIPELINE_TASK_PILOT_RECOMMENDATION_KEY,
                                        null,
                                        PipelineStatus.ERROR,
                                        null,
                                        null))
                        .map(Long::valueOf);

        Stream<Long> subStream = Stream.concat(projectIdsStreamOfPilotShortage, projectIdsOfMissingInsuredPhone.stream());

        return Stream.concat(list.stream().map(ProjectTinyVo::getProjectId),
            subStream).distinct().collect(Collectors.toList());
    }

    private Set<String> getProjectTagIds(Set<String> projectTagNames) {
        HashSet<String> set = new HashSet<>();
        for (String tagName: projectTagNames) {
            Iterable<? extends ProjectTag> tags =
                projectTagManager.findByTitle(tagName, BEES360_COMPANY_ID, ProjectTagType.CLAIM);
            Set<String> tagIds = Iterables.toStream(tags).map(ProjectTag::getId).collect(Collectors.toSet());
            set.addAll(tagIds);
        }
        return set;
    }

    @Override
    public void setProjectTimeline(ProjectTimeline timeline) {
        projectMapper.upsertProjectTimeline(timeline);
    }

    @Nullable
    @Override
    public String getPolicyType(long projectId, String claimNote, Long insuranceCompany, Integer claimType) {

        // TODO consider the policy type could not be changed
        //  and it is extracted from claim note so far,
        //  we fetch the policyType from claimNote priority to improve performance.
        var policyType = getPolicyTypeFromClaimNote(claimNote, insuranceCompany, claimType);

        if (Strings.isNotEmpty(policyType)) {
            return policyType;
        }

        var projectII = projectIIManager.findById(String.valueOf(projectId));
        if (projectII == null) {
            return null;
        }

        var policy = projectII.getPolicy();
        policyType = policy.getType();
        if (Strings.isNotEmpty(policyType)) {
            return policyType;
        }

        var policyId = policy.getId();
        return Optional.ofNullable(policyId)
            .filter(Strings::isNotEmpty)
            .map(policyManager::findById)
            .map(Policy::getType).orElse(null);
    }

    private String getPolicyTypeFromClaimNote(
        String claimNote, Long insuranceCompany, Integer claimType) {
        if (Objects.isNull(insuranceCompany)
            || StringUtils.isEmpty(claimNote)
            || Objects.isNull(claimType)
            || !ClaimTypeEnum.isClaim(claimType)) {
            return Strings.EMPTY;
        }
        // Get insurance company configuration
        var configItem = bees360CompanyConfig.findConfig(insuranceCompany);
        var policyTypePattern =
            Optional.ofNullable(configItem)
                .map(Bees360CompanyConfig.CompanyConfigItem::getPolicyTypeExtractRegex)
                .orElse(null);
        if (Objects.isNull(policyTypePattern)) {
            return Strings.EMPTY;
        }
        Matcher policyTypeMatcher = policyTypePattern.matcher(claimNote + "\n");
        // Apply the rule to the string and search for substrings that match the rule
        if (policyTypeMatcher.find()) {
            // Get the PolicyType group result after matching
            return policyTypeMatcher.group("PolicyType").trim();
        }
        return Strings.EMPTY;
    }

    @Override
    public void updateProjectProperty(long projectId, Integer projectProperty) {
        projectMapper.updateProjectProperty(projectId, projectProperty);
    }

    @Override
    public void handelCloseReport(long projectId) throws ServiceException {
        Project project = getById(projectId);
        if (closeoutGeneratePredicate.test(project)) {
            logger.info("check passed, generate closeout report, projectId:{}", projectId);
            projectReportJobManager.start(
                String.valueOf(projectId),
                ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT.getShortCut(),
                null,
                null,
                true,
                String.valueOf(User.AI_ID));
        }

        if (!projectService.isPilotJobCompleted(projectId)) {
            projectStatusService.updateBatchStatusOnProjectCanceled(project);
        }
    }

    @Override
    public ProjectII updatePolicyTypeAndPropertyType(
            long projectId, String policyType, Integer propertyType) throws ServiceException {
        return updatePolicyTypeAndPropertyType(projectId, policyType, propertyType, true);
    }

    @Override
    public ProjectII updatePolicyTypeAndPropertyType(
            long projectId, String policyType, Integer propertyType, boolean saveSolidData)
            throws ServiceException {
        logger.debug("Try to update policy type and property type: projectId={}, policyType={}, propertyType={}", projectId, policyType, propertyType);
        if (saveSolidData) {
            projectPolicyManager.updatePolicyTypeAndPropertyType(projectId + "", policyType, propertyType);
        }
        var project = projectIIManager.findById(projectId + "");
        Map<String, Object> data = new HashMap<>();
        data.put("projectType", project.getPolicy().getBuilding().getType().getNumber());
        projectMapper.updateByMap(projectId, data);

        projectService.transferDatasToAi(projectId, ProjectSyncPointEnum.MANUAL_SYNC.getType());
        publisher.publishEvent(new ProjectChangeEvent(this, getById(projectId)));
        return project;
    }
}
