package com.bees360.service.payment;

import java.util.Map;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.vo.Money;

public interface SystemValueCacheService {

	public double getCommissionRate() throws ServiceException;

	public Money getPriceForGreatThan5000Feet() throws ServiceException;

	public double getAreaThresholdForPrice() throws ServiceException;

	public double getTaxRate() throws ServiceException;

	public double getNewCustomerDiscountPercent() throws ServiceException;

	public int getNewCustomerDiscountProjectNum() throws ServiceException;

	public int getFreeOnTrailProjectNum() throws ServiceException;


	public Map<String,Object> getPilotAndReportPrices() throws ServiceException;

	public Money getReportServicePrice(int roofEstimatedAreaItem, int reportServiceOption) throws ServiceException;

	public Money getPilotServicePrice()throws ServiceException;

	public Map<String,Object> getPilotAndReportTaxPercentage() throws ServiceException;

	public double getPilotTaxPercentage() throws ServiceException;

	public double getReportTaxPercentage() throws ServiceException;

	public Money getBiddingReportPrice() throws ServiceException;

	public Money getHighflyReportPrice() throws ServiceException;

	/**
	 * 2019-03-25 report price changed: there is no relation to area except for INFRARED DAMAGE ASSESSMENT
	 * @param serviceFeeTypeId
	 * @return
	 * @throws ServiceException
	 */
	public Money getReportServicePrice(int serviceFeeTypeId) throws ServiceException;
}
