package com.bees360.service.impl;

import java.util.List;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.UserPayment;
import com.bees360.mapper.payment.UserPaymentMapper;
import com.bees360.service.payment.UserPaymentService;

/**
 *
 * <AUTHOR>
 *
 */
@Service("userPaymentService")
public class UserPaymentServiceImpl implements UserPaymentService{

	private static final Logger logger = LoggerFactory.getLogger(UserPaymentServiceImpl.class);

	@Inject
	UserPaymentMapper userPaymentMapper;

	@Override
	public void addUserPayments(List<UserPayment> userPayments) throws ServiceException{
		if(userPayments == null || userPayments.isEmpty()) {
			return;
		}
		userPaymentMapper.inserts(userPayments);
	}

	@Override
	public void addUserPayment(UserPayment userPayment) throws ServiceException {
		userPaymentMapper.insert(userPayment);
	}

	@Override
	public List<UserPayment> listUserPaymentsByProjectId(long projectId) throws ServiceException{
		List<UserPayment> userPayments = userPaymentMapper.listUserPaymentsByProjectId(projectId, null);
		return userPayments;
	}

	@Override
	public List<UserPayment> getUserPayments(long userId, long projectId) throws ServiceException{
		List<UserPayment> userPayments = userPaymentMapper.getUserPaymentsByProjectId(userId, projectId);
		return userPayments;
	}
}
