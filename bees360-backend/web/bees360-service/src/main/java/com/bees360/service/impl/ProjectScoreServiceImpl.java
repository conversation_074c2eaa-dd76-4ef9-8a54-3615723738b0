package com.bees360.service.impl;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectScore;
import com.bees360.event.EventPublisher;
import com.bees360.event.registry.ProjectScoreUpdateEvent;
import com.bees360.mapper.ProjectScoreMapper;
import com.bees360.service.ProjectScoreService;
import com.bees360.service.ProjectService;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
@Log4j2
public class ProjectScoreServiceImpl implements ProjectScoreService {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectScoreMapper projectScoreMapper;

    @Autowired
    private EventPublisher eventPublisher;

    @Override
    public void createOrUpdateProjectScore(long userId, long projectId, Double score) throws ServiceException {

        final Project project = projectService.getById(projectId);
        if (project == null){
            return;
        }

        ProjectScore projectScore = projectScoreMapper.getByProjectId(projectId);
        if (projectScore == null){
            projectScore = new ProjectScore();
            projectScore.setProjectId(projectId);
            projectScore.setScore(BigDecimal.valueOf(score));
            projectScoreMapper.insert(projectScore);
        } else {
            projectScore.setScore(BigDecimal.valueOf(score));
            projectScoreMapper.update(projectScore);
        }

        publishProjectScoreEvent(projectScore);
        log.info("Successfully update project {} with score {}", projectId, score);
    }

    private void publishProjectScoreEvent(ProjectScore projectScore) {
        var event = new ProjectScoreUpdateEvent();
        event.setProjectId(String.valueOf(projectScore.getProjectId()));
        event.setScore(projectScore.getScore());
        eventPublisher.publish(event);
    }

    @Override
    public List<ProjectScore> listByProjectIds(@Param("projectIds") List<Long> projectIds) {
        return projectScoreMapper.listByProjectIds(projectIds);
    }
}
