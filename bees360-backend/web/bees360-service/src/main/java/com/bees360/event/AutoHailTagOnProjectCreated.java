package com.bees360.event;

import com.bees360.entity.enums.AiBotUserEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.event.registry.ProjectCreate;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectMapper;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.claim.ClaimTypeEnum;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagManager;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.util.ConstantUtil;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;

/**
 * 自动为符合条件的项目添加HailReportNeeded标签的监听器类
 */
@Log4j2
public class AutoHailTagOnProjectCreated extends AbstractNamedEventListener<ProjectCreate> {

    private final Bees360CompanyConfig bees360CompanyConfig;
    private final ProjectTagManager projectTagManager;
    private final ProjectMapper projectMapper;
    private static final String title = "Hail report NEEDED";

    public AutoHailTagOnProjectCreated(
            @NonNull Bees360CompanyConfig bees360CompanyConfig,
            @NonNull ProjectTagManager projectTagManager,
            @NonNull ProjectMapper projectMapper) {
        this.bees360CompanyConfig = bees360CompanyConfig;
        this.projectTagManager = projectTagManager;
        this.projectMapper = projectMapper;
        log.info("Created '{}'", this);
    }

    @Override
    public void handle(ProjectCreate projectCreatedEvent) throws IOException {
        var projectId = Long.parseLong(projectCreatedEvent.getProject().getId());
        var project = projectMapper.getById(projectId);
        var serviceType = project.getServiceType();
        var claimType = project.getClaimType();
        if (!ProjectServiceTypeEnum.isClaim(serviceType)
                || Objects.equals(ServiceTypeEnum.POST_CONSTRUCTION_AUDIT.getCode(), serviceType)) {
            return;
        }

        var claimNote = project.getClaimNote();
        var insuranceCompany = project.getInsuranceCompany();

        var hailReportNeeded =
                checkNeedHailReport(claimNote, insuranceCompany, claimType);
        log.info("The project {} is hailReportNeeded? {}", projectId, hailReportNeeded);
        if (hailReportNeeded) {
            addProjectHailTag(projectId);
        }
    }

    /**
     * 自动打HailReportNeeded标签 <br>
     * 1、claim type是hail 或者 hail&wind的 <br>
     * 2、activity中存在关键字Loss Description 的
     */
    private boolean checkNeedHailReport(String claimNote, Long insuranceCompany, Integer claimType) {
        // 获取保险公司配置
        var configItem = bees360CompanyConfig.findConfig(insuranceCompany);
        var autoTagHailNeeded =
                Optional.ofNullable(configItem)
                        .map(Bees360CompanyConfig.CompanyConfigItem::isAutoTagHailNeeded)
                        .orElse(true);
        // 读取配置是否启动hailNeeded的标签
        if(!autoTagHailNeeded){
            return false;
        }

        // hail and hail&wind will add hail_report_needed_tag directly
        if (List.of(ClaimTypeEnum.HAIL.getCode(), ClaimTypeEnum.HAIL_WIND.getCode())
            .contains(claimType)) {
            return true;
        }
        if (Objects.isNull(insuranceCompany) || StringUtils.isEmpty(claimNote)) {
            return false;
        }

        var hailNeededPattern = bees360CompanyConfig.getHailNeededRegex();
        var lossDescPattern =
                Optional.ofNullable(configItem)
                        .map(Bees360CompanyConfig.CompanyConfigItem::getLossDescExtractRegex)
                        .orElse(null);
        if (Objects.isNull(hailNeededPattern)) {
            return false;
        }
        // 如果该公司还没有总结出loss description的规律，则直接去全文找
        if (Objects.isNull(lossDescPattern)) {
            return hailNeededPattern.matcher(claimNote).find();
        }
        Matcher lossDescMatcher = lossDescPattern.matcher(claimNote + "\n\n");
        // 将规则作用到字符串上，并进行符合规则的子串查找
        if (lossDescMatcher.find()) {
            // 获取匹配后LossDescription组结果
            String lossDescription = lossDescMatcher.group("LossDescription");
            return hailNeededPattern.matcher(lossDescription).find();
        }
        return false;
    }

    private void addProjectHailTag(long projectId) {
        Iterable<? extends ProjectTag> hailReportsNeeded =
                projectTagManager.findByTitle(
                        title,
                        ConstantUtil.BEES360_COMPANY_ID,
                        com.bees360.project.tag.Message.ProjectTagType.CLAIM);
        List<? extends ProjectTag> projectTags = IterableUtils.toList(hailReportsNeeded);
        log.info(
                "The project {}`s projectTags is {} and its size is {}",
                projectId,
                projectTags,
                projectTags.size());
        if (!CollectionUtils.isEmpty(projectTags)) {
            projectTagManager.addProjectTag(
                    String.valueOf(projectId),
                    List.of(projectTags.get(0).getId()),
                    AiBotUserEnum.AI_NEW_USER_ID.getCode(),
                    "AI");
            log.info("auto add TagHailReportNeeded tag successful, projectId {}", projectId);
        }
    }
}
