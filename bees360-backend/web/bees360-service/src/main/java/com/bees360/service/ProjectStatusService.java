package com.bees360.service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.dto.ProjectStatusTimeLineDto;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.vo.ProjectStatusVo;

import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/26 14:27
 */
public interface ProjectStatusService {

    List<ProjectStatus> listProjectStatus(long projectId);


    Map<Long, Map<NewProjectStatusEnum, ProjectStatus>> listProjectStatusLatestRecord(Collection<Long> projectIds);

    /**
     * 返回所有的 {@link com.bees360.entity.enums.NewProjectStatusEnum}，按照 {@link NewProjectStatusEnum#getCode()} 生序排序。
     * 如果数据库中已经存储有 {@link ProjectStatus}，则将该部分放在前面返回。每个{@link NewProjectStatusEnum#getCode()} 有且只有一个。
     *
     * @param projectId
     * @return
     * @throws ServiceException
     */
    List<ProjectStatusVo> listProjectStatusTimeLine(long projectId) throws ServiceException;

    ProjectStatusTimeLineDto getProjectStatusTimeLine(long projectId) throws ServiceException;

    Map<Long, ProjectStatusTimeLineDto> listProjectStatusTimeLines(List<Long> projectIds) throws ServiceException;

    void changeOnCustomerContacted(long userId, long projectId, long contactTime);

    void changeOnWaitingForAcceptance(long userId, long projectId);

    void changeOnAssignedToPilot(long userId, long projectId);

    void changeOnAssignedToPilot(long userId, long projectId, Long updateTime);

    /**
     * 这个接口由Portal内部调用
     */
    ProjectStatus changeOnImageUploaded(long userId, long projectId);

    void changeOnImageUploaded(long userId, long projectId, Long updateTime);

    void changeOnIBeesUploaded(long userId, long projectId);

    void changeOnCancelPilot(long userId, long projectId) throws ServiceException;

    void changeOnSiteInspected(long userId, long projectId, Long updateTime);

    ProjectStatus changeOnReturnedToClient(long userId, long projectId);
    ProjectStatus changeOnReturnedToClient(long userId, long projectId, @Nullable String  comment);

    void changeOnProjectCanceled(long userId, long projectId, SystemTypeEnum systemType,
                                 String cancelReason) throws ServiceException;

    void changeOnProjectRecovered(long userId, long projectId) throws ServiceException;

    ProjectStatus changeOnClientReceived(long userId, long projectId, String comment);

    ProjectStatus changeOnClientReceived(long userId, long projectId, SystemTypeEnum systemType, String comment);

    void changeOnReceiveError(long userId, long projectId, String comment) throws ServiceException;

    void projectReworkOnAi(long userId, long projectId, String reason, String content);

    void projectReworkOnWeb(long userId, long projectId, String reason, String content, Long updateTime);

    /**
     * 将项目状态回滚到CustomerContacted
     * @param projectId 项目ID
     */
    void rollProjectStatusToCustomerContacted(long projectId) throws ServiceException;

    void updateBatchStatusOnProjectCanceled(Project project) throws ServiceException;

    List<Long> listProjectByStatusListAndTime(List<Integer> projectStatusList, long startTime, long endTime);

    List<ProjectStatusVo> listProjectStatusByCreateTime(long projectId) throws ServiceException;

    void publishProjectStatusChangeEvent(ProjectStatus projectStatus, boolean isRollback);

    void insertProjectStatus(long projectId, long userId, int status, long statusUpdateTime);

    ProjectStatus requestCancel(long userId, long projectId, SystemTypeEnum systemType,
                                String cancelReason);
}
