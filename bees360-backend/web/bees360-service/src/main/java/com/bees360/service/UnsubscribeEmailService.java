package com.bees360.service;

public interface UnsubscribeEmailService {

    /**
     * 指定公司是否已退订指定邮件模板
     * @param templateKey 邮件模板
     * @param company 公司
     * @return 是否退订
     */
    boolean isUnsubscribed(String templateKey, long company);

    /**
     * 指定项目的公司是否已退订指定邮件模板
     * @param templateKey 邮件模板
     * @param projectId 项目
     * @return 是否退订
     */
    boolean isUnsubscribedByProject(String templateKey, long projectId);
}
