package com.bees360.service.impl;

import com.bees360.common.file.ZipUtils;
import com.bees360.common.uri.AddressUrl;
import com.bees360.entity.Project;
import com.bees360.job.Job;
import com.bees360.job.JobFuture;
import com.bees360.job.JobScheduler;
import com.bees360.job.registry.ResourceArchiveJob;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.resource.ResourceUrlProvider;
import com.bees360.resource.archive.ResourceArchiveRenameRule;
import com.bees360.service.ProjectImageArchiveService;
import com.bees360.service.ProjectImageService;
import com.bees360.service.ProjectService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.Iterables;
import com.bees360.util.Uris;
import com.bees360.util.file.S3KeyManager;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class ProjectImageArchiveServiceImpl implements ProjectImageArchiveService {

    @Autowired
    private JobScheduler jobScheduler;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectImageService projectImageService;

    @Autowired
    private S3KeyManager s3KeyManager;

    @Autowired
    private Executor imagesArchiveExecutor;

    @Autowired private ResourceUrlProvider resourceUrlProvider;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Autowired
    private ResourcePool resourcePool;

    private static final Base64.Encoder base64 = Base64.getEncoder();

    public static final String JOB_NAME = "archive_files";

    // minimum Gb size to generate a images zip
    @Value("${project.image.archive.minimum-size:1}")
    private Long minimumSize;

    @Override
    public void archiveImages(long projectId) throws Exception {
        Project project = projectService.getById(projectId);
        String address = project.getFullAddress();
        List<String> keys = projectImageService.listImageKeysByProjectIdForArchive(projectId);
        archiveImages(projectId, address, keys);
    }

    @Override
    public void archiveImages(long projectId, String address, List<String> imageKeys) {
        if(!checkNeedToArchiveImages(projectId, imageKeys)){
            return;
        }
        address = AddressUrl.replace(address);
        String fileName = s3KeyManager.generateImageArchiveFileName(projectId, address, ZipUtils.SUFFIX_ZIP);
        String outputKey = s3KeyManager.getImagesArchiveFileKey(projectId, fileName);
        outputKey = outputKey.replaceAll(",\\s*", ",").replace(" ", "_").replace("'", "");

        Job job;
        if (bees360FeatureSwitch.isEnableResetArchiveFilename()) {
            job =
                    ResourceArchiveJob.of(
                            Iterables.toStream(imageKeys)
                                    .collect(
                                            Collectors.toMap(
                                                    Function.identity(), this::getFilename)),
                            outputKey);
        } else {
            job =
                    ResourceArchiveJob.of(
                            imageKeys, outputKey, ResourceArchiveRenameRule.useFilename());
        }

        JobFuture future = jobScheduler.schedule(job);
        log.info("Project image archive job fired, projectId:{}", projectId);
        callback(future, projectId, outputKey);
    }

    private String getFilename(String key) {
        var filename = ResourceArchiveRenameRule.useFilename().apply(key);
        var extension = FilenameUtils.getExtension(filename);
        if (StringUtils.isBlank(extension)) {
            var getUrl = resourceUrlProvider.getGetUrl(key).toString();
            filename = Uris.getFilename(getUrl);
        }
        return filename;
    }

    private void callback(JobFuture future, long projectId, String archiveFileKey) {

        Futures.addCallback(future, new FutureCallback<>() {

            @Override
            public void onSuccess(@Nullable Void result) {
                try {
                    projectService.updateImagesArchiveUrl(projectId, archiveFileKey);
                    log.info("Project images archive finished, projectId:{}", projectId);
                } catch (Exception e) {
                    //service exception transfer
                    throw new RuntimeException(e);
                }
            }

            @Override
            public void onFailure(Throwable t) {
                log.error("An error occur while try to archive image, projectId:{}", projectId, t);
            }
        }, imagesArchiveExecutor);
    }

    private boolean checkNeedToArchiveImages(long projectId, List<String> imageKeys) {
        if (imageKeys == null || imageKeys.isEmpty()) {
            return false;
        }

        long totalBytes = imageKeys.stream()
                .mapToLong(key -> {
                    try {
                        return Optional.ofNullable(resourcePool.head(key))
                                .map(ResourceMetadata::getContentLength)
                                .orElse(0L);
                    } catch (Exception e) {
                        log.warn("Failed to get size for key: {}", key, e);
                        return 0L;
                    }
                })
                .sum();

        // Convert bytes to GB (1 GB = 1024 * 1024 * 1024 bytes)
        double totalGb = totalBytes / (1024.0 * 1024.0 * 1024.0);

        log.debug("Project {}: total size of images: {} GB, minimum required: {} GB",
                projectId, totalGb, minimumSize);
        return totalGb >= minimumSize;
    }
}
