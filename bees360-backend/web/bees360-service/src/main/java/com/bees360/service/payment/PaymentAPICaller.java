package com.bees360.service.payment;

import java.util.Map;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.vo.CreditCardVo;
import com.bees360.entity.vo.OrderVo;

public interface PaymentAPICaller {

	/**
	 * call third party payment api
	 * @param userId : long
	 * @param creditCardVo
	 * @param orderVo
	 * @param paramMap
	 * @throws ServiceException
	 */
	public void payByCreditCard(long userId, CreditCardVo creditCardVo, OrderVo orderVo, Map<String,String> paramMap) throws ServiceException;

	/**
	 * this interface is for unRegistered user and pay by credit_card_nonce in booking page
	 * @param bookingUserName
	 * @param cardNonce
	 * @param orderVo
	 * @param paramMap
	 * @throws ServiceException
	 */
	public void payByCreditCard(String cardNonce, String orderId, long amount) throws ServiceException;


	public String payByPage(long userId, OrderVo orderVo, Map<String,String> paramMap) throws ServiceException;


	public String execute(String paymentId, String payerId) throws ServiceException;


	public Map<String,Object> parsePaymentJson(long userId, String paymentCallbackJson) throws ServiceException;
}
