package com.bees360.service.impl;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourcePool;
import com.bees360.service.UrlDataService;
import com.google.protobuf.ByteString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/01/19 11:04
 */
@Slf4j
@Service
public class UrlDataServiceImpl implements UrlDataService {
    @Autowired
    private ResourcePool resourcePool;
    @Override
    public List<String> uploadCertificate(MultipartFile[] certificates, String[] fileName, String keyPrefix) throws ServiceException {
        try {
            List<String> s3Keys = new ArrayList<>(certificates.length);
            for (int i = 0; i < certificates.length; i++) {
                String s3key = toS3Key(keyPrefix, fileName[i]);
                resourcePool.put(s3key, Resource.of(ByteString.copyFrom(certificates[i].getBytes())));
                s3Keys.add(s3key);
            }
            return s3Keys;
        } catch (Exception e) {
            log.warn("uploadCertificate certificates fail", e);
            throw new ServiceException(MessageCode.S3_FAIL_UPLOAD_IMAGES);
        }
    }

    private String toS3Key(String keyPrefix, String fileName) {
        if (!keyPrefix.endsWith("/")) {
            keyPrefix = keyPrefix + '/';
        }
        if (keyPrefix.startsWith("/")) {
            keyPrefix = keyPrefix.substring(1);
        }
        return keyPrefix + fileName;
    }
}
