package com.bees360.service.listener.report;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.service.ProjectService;
import com.bees360.web.event.project.ProjectLabelChangedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReportGenerateListener {

    @Autowired private ProjectService projectService;

    // 打了 CANCELLATION 的operation tag会生成 LOCReport
    // 当项目close时触发也会触发生成LOCReport  @see com.bees360.event.CloseProjectOnStateClose
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void generateLOCReportWhenLabelChangedEvent(ProjectLabelChangedEvent event)
            throws ServiceException {
        if (event.getProjectLabels().stream().noneMatch(ProjectLabelEnum::isCancellation)) {
            return;
        }
        projectService.handelCloseReport(event.getProjectId());
    }
}
