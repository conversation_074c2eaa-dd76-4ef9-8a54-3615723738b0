package com.bees360.event;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.enums.ProjectSyncPointEnum;
import com.bees360.event.registry.ContactChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.ProjectService;
import com.bees360.web.project.util.ProjectConstants;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.bees360.util.Defaults.defaultIfNull;

/**
 * 监听联系人变更事件并更新相关项目信息
 */
@Log4j2
public class UpdateProjectOnContactChangedEvent extends AbstractNamedEventListener<ContactChangedEvent> {

    private final ProjectService projectService;

    private final ProjectMapper projectMapper;

    public UpdateProjectOnContactChangedEvent(ProjectService projectService, ProjectMapper projectMapper) {
        this.projectMapper = projectMapper;
        this.projectService = projectService;
        log.info("Created {}(projectService={}, projectMapper={}).", this, this.projectService, this.projectMapper);
    }

    @Override
    public void handle(ContactChangedEvent event) throws IOException {
        var projectId = Long.parseLong(event.getProjectId());
        var newContact = event.getNewValue();
        if(Objects.isNull(newContact)) {
            return;
        }

        Map<String, Object> data = new HashMap<>();
        if (StringUtils.equals(newContact.getRole(), ProjectConstants.ContactRoleType.CONTACT_INSURED)) {
            data.put("assetOwnerName", newContact.getFullName());
            data.put("assetOwnerEmail", defaultIfNull(newContact.getPrimaryEmail(), ""));
            data.put("assetOwnerPhone", defaultIfNull(newContact.getPrimaryPhone(), ""));
        } else if (StringUtils.equals(newContact.getRole(), ProjectConstants.ContactRoleType.CONTACT_AGENT)) {
            data.put("agentContactName", newContact.getFullName());
            data.put("agentEmail", newContact.getPrimaryEmail());
            data.put("agentPhone", newContact.getPrimaryPhone());
        }

        if (!data.isEmpty()) {
            projectMapper.updateByMap(projectId, data);
            try {
                projectService.transferDatasToAi(projectId, ProjectSyncPointEnum.MANUAL_SYNC.getType());
            } catch (ServiceException e) {
                log.error("Transfer data to ai failed after updating project contact info with event {}.", event);
            }
            log.info("Successfully update project contact with event {}", event);
        }
    }
}
