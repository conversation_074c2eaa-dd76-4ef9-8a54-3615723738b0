package com.bees360.service.openapi;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.openapi.OpenProjectCreateVo;
import com.bees360.entity.openapi.OpenProjectSearchOption;
import com.bees360.entity.openapi.OpenProjectStatusVo;
import com.bees360.entity.openapi.OpenProjectVo;
import com.bees360.entity.openapi.OpenProjectWithStatusVo;
import com.bees360.entity.openapi.OpenReportIdTypeVo;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/26 16:10
 */
public interface OpenApiProjectService {

    OpenProjectVo createProject(long userId, OpenProjectCreateVo projectVo) throws ServiceException;

    OpenProjectVo getProject(long userId, long projectId) throws ServiceException;

    List<OpenProjectWithStatusVo> searchProject(long userId, OpenProjectSearchOption searchOption) throws ServiceException;

    OpenProjectStatusVo getProjectLatestStatus(long projectId) throws ServiceException;

    List<OpenReportIdTypeVo> listProjectReport(long projectId) throws ServiceException;

    OpenProjectStatusVo updateProjectStatus(long userId, long projectId, NewProjectStatusEnum projectStatus, String comment) throws ServiceException;
}
