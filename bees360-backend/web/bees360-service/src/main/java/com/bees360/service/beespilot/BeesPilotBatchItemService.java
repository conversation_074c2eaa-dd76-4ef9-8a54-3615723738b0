package com.bees360.service.beespilot;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.BeesPilotBatchItem;
import com.bees360.entity.BeesPilotBatchItemVo;

import java.util.List;

/**
 *
 * 飞手批量账单管理
 **/
public interface BeesPilotBatchItemService {


    List<BeesPilotBatchItem> listByProjectIds(List<Long> projectIds);

    List<BeesPilotBatchItem> listByBatchNo(String batchNo) throws ServiceException;

    List<BeesPilotBatchItem> listByBatchNoWithoutDeletedCheck(String batchNo) throws ServiceException;

    void addBeesPilotBatchItem(List<BeesPilotBatchItem> itemList);

    List<BeesPilotBatchItemVo> listByProjectIdsWithBatchInfo(List<Long> projectIds);

    BeesPilotBatchItemVo findByProjectId(long projectId);

    BeesPilotBatchItem findByProjectIdWithoutDeletedCheck(long projectId);

    void deleteByBatchNo(String batchNo);

    void deleteByProjectIds(List<Long> projectIds);

    List<BeesPilotBatchItem> listByProjectIdsWithoutDeletedCheck(List<Long> projectIds);

    void updateBatchDeletedStatus(long projectId, long pilotId, Boolean isDeleted) throws ServiceException;

    /**
     * 返回最近分配的batchNo 该batchNo可能被删除了
     * @param projectId
     * @return
     */
    String getBatchNo(long projectId);
}
