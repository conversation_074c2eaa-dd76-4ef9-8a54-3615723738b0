package com.bees360.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.SystemValue;
import com.bees360.mapper.payment.SystemValueMapper;
import com.bees360.service.payment.SystemValueService;

@Service("systemValueService")
public class SystemValueServiceImpl implements SystemValueService{

	private static final Logger logger = LoggerFactory.getLogger(SystemValueServiceImpl.class);

	@Autowired
	SystemValueMapper systemValueMapper;

	@Override
	public List<SystemValue> getAllSystemValues() throws ServiceException {
		List<SystemValue> systemValues = null;
		try {
			systemValues = systemValueMapper.getAllSystemValues();
		}catch(Exception e) {
			logger.error("SystemValueServiceImpl.getAllSystemValues() failed",e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
		return systemValues;
	}
}
