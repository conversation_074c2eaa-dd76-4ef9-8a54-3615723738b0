package com.bees360.event;

import com.bees360.api.InvalidArgumentException;
import com.bees360.entity.enums.PipelineTaskEnum;
import com.bees360.event.registry.ProjectInvoiceUpdatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听项目发票更新事件并完成相关流水线任务
 */
@Log4j2
public class CompleteInvoiceTaskOnProjectInvoiceUpdated
        extends AbstractNamedEventListener<ProjectInvoiceUpdatedEvent> {
    private final PipelineService pipelineService;

    public CompleteInvoiceTaskOnProjectInvoiceUpdated(@NonNull PipelineService pipelineService) {
        this.pipelineService = pipelineService;
        log.info("Created '{}(pipelineService={})'", this, this.pipelineService);
    }

    @Override
    public void handle(ProjectInvoiceUpdatedEvent projectInvoiceUpdatedEvent)
            throws IOException {
        var key = PipelineTaskEnum.GENERATE_INVOICE.getKey();
        var pipelineId = String.valueOf(projectInvoiceUpdatedEvent.getProjectId());
        try {
            pipelineService.setTaskStatus(pipelineId, key, Message.PipelineStatus.DONE);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Failed to set pipeline '{}' task '{}' to DONE", pipelineId, key, e);
        } catch (RuntimeException e) {
            log.error("Failed to set pipeline '{}' task '{}' to DONE", pipelineId, key, e);
        }
    }
}
