package com.bees360.service.payment;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.UserPaymentProfile;
import com.bees360.entity.vo.WalletGlobalDiscount;

import com.bees360.entity.enums.productandpayment.GlobalDiscountTypeEnum;

public interface UserPaymentProfileService {

	/**
	 * 返回在用户所含有的Discount中推荐的Discount。
	 * 该接口只会返回 {@link GlobalDiscountTypeEnum#UNLIMITED_DISCOUNT} 和 {@link GlobalDiscountTypeEnum#LIMITED_DISCOUNT}
	 * 类型的折扣。
	 */
	WalletGlobalDiscount getRecommendedDiscount(long userId) throws ServiceException;

	/**
	 * get User payment Profile infomation
	 * @param userId
	 * @return
	 * @throws ServiceException
	 */
	public UserPaymentProfile getUserPaymentProfile(long userId) throws ServiceException;

	/**
	 * update User payment profile
	 * @param userPaymentProfile
	 * @throws ServiceException
	 */
	public void updateUserPaymentProfileByBalance(long userId, double walletBalance, String currency) throws ServiceException;

	/**
	 * update User payment profile
	 * @param userPaymentProfile
	 * @throws ServiceException
	 */
	public void updateUserPaymentProfileByCommission(long userId, double commissionBalance, String currency) throws ServiceException;


	/**
	 * set free project number for trail
	 * @param userId
	 * @param freeOnTrailProjectNum
	 * @throws ServiceException
	 */
	public void updateFreeOnTrailProjectNum(long userId, int freeOnTrailProjectNum) throws ServiceException;

	/**
	 * update user discount percent
	 * @param userId
	 * @param discountPercent
	 * @throws ServiceException
	 */
	public void updateUserDiscountPercent(long userId, double discountPercent) throws ServiceException;


	/**
	 * update on trail and discount info for new customer
	 * @param userId
	 * @param freeOnTrailProjectNum:  project number for on trail
	 * @param newCustomerDiscountPercent:
	 * @param newCustomerDiscountProjectNum
	 * @throws ServiceException
	 */
	public void updateNewCustomerDiscountAndProjectNum(long userId, int freeOnTrailProjectNum, double newCustomerDiscountPercent, int newCustomerDiscountProjectNum) throws ServiceException;

	/**
	 * update new customer discount percent and project num
	 * @param userId
	 * @param newCustomerDiscountPercent
	 * @param projectNum
	 * @throws ServiceException
	 */
	public void updateNewCustomerDiscountAndProjectNum(long userId, double newCustomerDiscountPercent, int newCustomerDiscountProjectNum) throws ServiceException;
}
