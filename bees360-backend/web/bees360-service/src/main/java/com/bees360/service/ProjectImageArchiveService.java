package com.bees360.service;

import com.bees360.base.exception.ServiceException;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-1-8
 */
public interface ProjectImageArchiveService {

    /**
     * 生成项目压缩包
     * @param projectId
     * @throws ServiceException
     */
    public void archiveImages(long projectId) throws Exception;

    /**
     * 生成项目压缩包
     * @param projectId
     * @param address
     * @param imageKeys
     * @throws ServiceException
     */
    public void archiveImages(long projectId, String address, List<String> imageKeys) throws Exception;
}
