package com.bees360.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.bees360.entity.vo.EventHistoryStatusLogVo;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.EventHistory;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.vo.HistoryLogVo;
import com.bees360.mapper.EventHistoryMapper;
import com.bees360.service.EventHistoryService;
import com.bees360.service.ProjectService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import static com.bees360.entity.enums.AiBotUserEnum.AI_NEW_USER_ID;

@Service("eventHistoryService")
public class EventHistoryServiceImpl implements EventHistoryService{

	private Logger logger = LoggerFactory.getLogger(ProjectServiceImpl.class);

	@Inject
	EventHistoryMapper eventHistoryMapper;

	@Inject
	ProjectService projectService;

    @Inject
    UserProvider userProvider;

	private static Comparator eventHistoryOrderByTimeDesc;

	static {
		eventHistoryOrderByTimeDesc = (Comparator<EventHistory>)(h1, h2) -> {
			if(h1 == null){
				return 1;
			} else if(h2 == null){
				return -1;
			} else if (h1.getCreatedTime() == h2.getCreatedTime()){
				return 0;
			} else if (h1.getCreatedTime() < h2.getCreatedTime()){
				return 1;
			} else {
				return -1;
			}
		};
	}

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public void insertHistoryToProject(EventHistory history) {
		if (history == null) {
			return;
		}
		eventHistoryMapper.insert(history);
		long projectId = history.getProjectId();
		ProjectStatusEnum projectStatus = ProjectStatusEnum.getEnum(history.getStatus());
		if (projectStatus.getProcessStatus() != null) {
			projectService.updateLatestStatus(projectId, projectStatus.getProcessStatus());
		}
	}

    @Override
    public void insertHistoryToProject(long projectId, long userId, ProjectStatusEnum status, String description)
        throws ServiceException {
        if(status == null) {
            return;
        }
        EventHistory history = new EventHistory();
        history.setProjectId(projectId);
        history.setUserId(userId);
        history.setStatus(status.getCode());
        history.setStatusTime(System.currentTimeMillis());
        history.setModifiedBy(userId);
        history.setCreatedTime(System.currentTimeMillis());
        history.setDescription(description);

        insertHistoryToProject(history);
    }

    @Override
	public boolean existStatus(long projectId, ProjectStatusEnum status) throws ServiceException {
		try{
			return eventHistoryMapper.existStatus(projectId, status.getCode());
		} catch (Exception e){
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

	@Override
	public Map<Integer, EventHistory> listHistoryIn(long projectId, List<ProjectStatusEnum> historyTypeIds) throws ServiceException {
		try{
			List<Integer> statuses = new ArrayList<Integer>(historyTypeIds.size());
			for(ProjectStatusEnum status: historyTypeIds){
				statuses.add(status.getCode());
			}
			List<EventHistory> histories = eventHistoryMapper.listHistoryIn(projectId, statuses);
			Map<Integer, EventHistory> historyMap = new HashMap<Integer, EventHistory> ();
			for(EventHistory history: histories){
				historyMap.put(history.getStatus(), history);
			}
			return historyMap;
		} catch (Exception e){
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

    @Override
    public EventHistory getLastEventHistory(long projectId) {
        return eventHistoryMapper.getLastEventHistory(projectId);
    }

	@Override
	public EventHistory getByStatus(long projectId, ProjectStatusEnum status) throws ServiceException{
		EventHistory history = null;
		if(status == null){
			return null;
		}
		try{
			history = eventHistoryMapper.getByStatus(projectId, status.getCode());
		} catch (Exception e){
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
		return history;
	}

	@Override
	public List<HistoryLogVo> listHistoryLogs(long projectId) throws ServiceException {
		List<HistoryLogVo> histories = null;
		try {
			histories = getHistoryLogUser(projectId);
		} catch (Exception e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
		return histories;
	}

	@Override
	public ProjectStatusEnum getLatestADStatus(long projectId, long userId) throws ServiceException {
		List<EventHistory> histories = listHistorySortByCreateTimeDesc(projectId);
		for(EventHistory history: histories) {
			if(history.getStatus() == ProjectStatusEnum.AD_STARTED.getCode()) {
				return ProjectStatusEnum.AD_STARTED;
			} else if(history.getStatus() == ProjectStatusEnum.AD_GENERATED.getCode()) {
				return ProjectStatusEnum.AD_GENERATED;
			}
		}
		return null;
	}

	@Override
	public ProjectStatusEnum getLatestRealtimeADStatus(long projectId, long userId) throws ServiceException {
		List<EventHistory> histories = listHistorySortByCreateTimeDesc(projectId);
		for(EventHistory history: histories) {
			if(history.getStatus() == ProjectStatusEnum.REALTIME_AD_STARTED.getCode()) {
				return ProjectStatusEnum.REALTIME_AD_STARTED;
			} else if(history.getStatus() == ProjectStatusEnum.REALTIME_AD_GENERATED.getCode()) {
				return ProjectStatusEnum.REALTIME_AD_GENERATED;
			}
		}
		return null;
	}

	@Override
	public List<EventHistory> listHistorySortByCreateTimeDesc(long projectId) throws ServiceException {
		List<EventHistory> histories = eventHistoryMapper.listAll(projectId);
		// sort histories order by created time desc
		Collections.sort(histories, eventHistoryOrderByTimeDesc);
		return histories;
	}

	@Override
	public List<EventHistoryStatusLogVo> listLogTimeLineForBeesGo(long projectId) throws ServiceException {
		List<EventHistory> eventHistories = listHistorySortByCreateTimeDesc(projectId);
		Set<Integer> eventHistoryStatusLimit = new HashSet<>(Arrays.asList(
			ProjectStatusEnum.NEW_PROJECT.getCode(),
			ProjectStatusEnum.ADJUSTER_CLAIM_CHECKED_OUT.getCode(),
			ProjectStatusEnum.ADJUSTER_CLAIM_CHECKED_IN.getCode(),
			ProjectStatusEnum.ADJUSTER_UNDERWRITING_CHECKED_OUT.getCode(),
			ProjectStatusEnum.ADJUSTER_UNDERWRITING_CHECKED_IN.getCode(),
			ProjectStatusEnum.PILOT_CHECKED_OUT.getCode(),
			ProjectStatusEnum.PILOT_CHECKED_IN.getCode(),
			ProjectStatusEnum.IMAGE_UPLOADED.getCode()));

		List<EventHistoryStatusLogVo> logs = new ArrayList<>(eventHistories.size());
		for(EventHistory h: eventHistories) {
			if(!eventHistoryStatusLimit.contains(h.getStatus())) {
				continue;
			}
			ProjectStatusEnum status = ProjectStatusEnum.getEnum(h.getStatus());
			EventHistoryStatusLogVo log = new EventHistoryStatusLogVo(status.getCode(), status.getDisplay(), h.getCreatedTime());
			logs.add(log);
		}
		return logs;
	}

    @Override
    public void insertHistoryByImageUpload(long projectId, long userId, int imageCount) throws ServiceException {
        if (imageCount <= 0) {
            return;
        }
        String desc = Objects.equals(imageCount, 1) ? imageCount + " image" : imageCount + " images";
        desc = desc + " have been uploaded this time";
        EventHistory eventHistory = getEventHistory(ProjectStatusEnum.PART_IMAGE_UPLOADED, projectId, userId, desc);
        insertHistoryToProject(eventHistory);

    }

    private List<HistoryLogVo> getHistoryLogUser(long projectId) {
        var historyLogVos = eventHistoryMapper.listHistoryLog(projectId);
        Set<String> userIds = new HashSet<>();
        historyLogVos.forEach(
                h -> {
                    if (Objects.isNull(h)) {
                        return;
                    }
                    Optional.ofNullable(h.getUser()).ifPresent(userIds::add);
                    Optional.ofNullable(h.getModifiedBy()).ifPresent(userIds::add);
                });
        var userMap =
                Iterables.toStream(userProvider.findUserById(userIds))
                        .collect(Collectors.toMap(User::getId, User::getName));
        historyLogVos.forEach(
                historyLogVo -> {
                    historyLogVo.setUser(
                            Optional.ofNullable(userMap.get(historyLogVo.getUser()))
                                    .orElse(AI_NEW_USER_ID.getDisplay()));
                    historyLogVo.setModifiedBy(
                            Optional.ofNullable(userMap.get(historyLogVo.getModifiedBy()))
                                    .orElse(AI_NEW_USER_ID.getDisplay()));
                });
        return historyLogVos;
    }
}
