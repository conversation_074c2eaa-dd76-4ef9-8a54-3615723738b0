package com.bees360.event;

import com.bees360.entity.Project;
import com.bees360.entity.User;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.event.registry.CustomerContactedInitiallyEvent;
import com.bees360.event.registry.ProjectAssignedToPilotEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import lombok.extern.log4j.Log4j2;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.bees360.entity.enums.NewProjectStatusEnum.CUSTOMER_CONTACTED;

/**
 * 监听 project_status 转换为 assigned_to_pilot 的事件，然后判断此 project
 * 是否为 claim 或 premium 4-point inspection,
 * 同时是否存在 customer_contacted_status，若不存在则插入此状态
 */
@Log4j2
public class AddCustomerContactedOnProjectStatusAssignedToPilot
    extends AbstractNamedEventListener<ProjectAssignedToPilotEvent> {
    private final EventPublisher eventPublisher;
    private final ProjectService projectService;
    private final ProjectStatusService projectStatusService;

    private final Set<Integer> ignoreServiceTypes;

    public AddCustomerContactedOnProjectStatusAssignedToPilot(
        EventPublisher eventPublisher,
        ProjectService projectService,
        ProjectStatusService projectStatusService,
        Set<ProjectServiceTypeEnum> ignoreServiceTypes) {
        this.eventPublisher = eventPublisher;
        this.projectService = projectService;
        this.projectStatusService = projectStatusService;
        if (ignoreServiceTypes == null) {
            this.ignoreServiceTypes = Set.of();
        } else {
            this.ignoreServiceTypes = ignoreServiceTypes.stream().map(ProjectServiceTypeEnum::getCode).collect(Collectors.toSet());
        }
        log.info("Created {} (eventPublisher={} " +
            "projectService={} projectStatusService={}, ignoreServiceTypes={}).",
                this, this.eventPublisher,
                    this.projectService, this.projectStatusService, ignoreServiceTypes);
    }

    @Override
    @Transactional
    public void handle(ProjectAssignedToPilotEvent event) throws IOException {
        var projectId = event.getProjectId();
        var projectIdLong = Long.parseLong(projectId);

        Project project = projectService.getById(projectIdLong);
        //判断Null
        if (Objects.isNull(project) || Objects.isNull(project.getServiceType())) {
            return;
        }

        if (ignoreServiceTypes.contains(project.getServiceType())) {
            return;
        }

        //判断是否为 claim 或 premium 4-point inspection 或者 已经存在 customer_contacted_status
        if (!isClaimOrPremiumFourPointUnderwriting(project.getServiceType())
            || isContainCustomerContactedStatus(projectIdLong)) {
            return;
        }

        //提前100ms
        var contactedTime = event.getUpdatedAt().minusMillis(100);
        var userId = User.BEES_PILOT_SYSTEM;
        projectStatusService.insertProjectStatus(
            projectIdLong, userId, CUSTOMER_CONTACTED.getCode(), contactedTime.toEpochMilli());

        //如果InitialCustomerContactTime为空，则补上
        if (Objects.isNull(project.getInitialCustomerContactTime())) {
            projectService.setInitialCustomerContactedTime(projectIdLong, contactedTime);
        }

        CustomerContactedInitiallyEvent contactedInitiallyEvent = new CustomerContactedInitiallyEvent();
        contactedInitiallyEvent.setProjectId(projectId);
        contactedInitiallyEvent.setContactedBy(String.valueOf(userId));
        contactedInitiallyEvent.setInitialContactTime(contactedTime.toEpochMilli());
        eventPublisher.publish(contactedInitiallyEvent);

        log.info("Added customer_contacted status to project {} on Assigned To Pilot Event", projectId);
    }

    /**
     * 判断是否为 claim 或 premium 4-point inspection
     */
    boolean isClaimOrPremiumFourPointUnderwriting(Integer serviceType) {
        return ProjectServiceTypeEnum.isClaim(serviceType)
            || ProjectServiceTypeEnum.equals(serviceType, ProjectServiceTypeEnum.PREMIUM_FOUR_POINT_UNDERWRITING);
    }

    /**
     * 判断项目是否存在 customer_contacted_status
     */
    boolean isContainCustomerContactedStatus(long projectId) {
        return projectStatusService.listProjectStatus(projectId)
            .stream().anyMatch(s -> CUSTOMER_CONTACTED.getCode() == s.getStatus());
    }
}
