package com.bees360.project;

import com.bees360.entity.vo.request.ProjectServiceTypeParam;
import com.bees360.project.group.ProjectGroupUpgradedManager;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.service.ProjectService;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;

import java.util.Set;
import java.util.function.BiPredicate;

@Log4j2
public class DefaultProjectServiceUpgradeManager implements ProjectServiceUpgradeManager {

    private final ProjectIIManager projectIIManager;
    private final ProjectService projectService;
    private final BiPredicate<Integer, Integer> upgradeServiceTypeCondition;
    private final ProjectGroupUpgradedManager projectGroupUpgradedManager;
    private final ProjectStateManager projectStateManager;
    private final ProjectStatusManager projectStatusManager;
    private final Set<Message.ProjectStatus> imageUploadedIfNotIn;
    private final String reopenChangeReason;

    public DefaultProjectServiceUpgradeManager(
            ProjectIIManager projectIIManager,
            ProjectService projectService,
            BiPredicate<Integer, Integer> upgradeServiceTypeCondition,
            ProjectGroupUpgradedManager projectGroupUpgradedManager,
            ProjectStateManager projectStateManager,
            ProjectStatusManager projectStatusManager,
            Set<Message.ProjectStatus> imageUploadedIfNotIn,
            String reopenChangeReason) {
        this.projectIIManager = projectIIManager;
        this.projectService = projectService;
        this.upgradeServiceTypeCondition = upgradeServiceTypeCondition;
        this.projectGroupUpgradedManager = projectGroupUpgradedManager;
        this.projectStateManager = projectStateManager;
        this.projectStatusManager = projectStatusManager;
        this.imageUploadedIfNotIn = imageUploadedIfNotIn;
        this.reopenChangeReason = reopenChangeReason;
        log.info("Created {}(projectIIManager={},projectService={},upgradeServiceTypeCondition={},projectGroupUpgradedManager={},projectStateManager={},projectStatusManager={})",
            this, projectIIManager, projectService, upgradeServiceTypeCondition, projectGroupUpgradedManager, projectStateManager, projectStatusManager);
    }

    @SneakyThrows
    @Override
    public void upgrade(String userId, String projectId, int serviceTypeTo) {
        var project = projectIIManager.findById(projectId);
        // upgrade service type
        var serviceType = project.getServiceType();
        if (!upgradeServiceTypeCondition.test(serviceType.getCode(), serviceTypeTo)) {
            throw new IllegalArgumentException(String.format("The service type %s cannot be upgraded.", serviceType.getName()));
        }

        reopenIfProjectClosed(userId, project);

        var param = new ProjectServiceTypeParam();
        param.setServiceType(serviceTypeTo);

        projectService.updateProjectServiceType(
            Long.parseLong(userId),
            Long.parseLong(project.getId()),
            param);
        setImageUploaded(project, userId);
        projectGroupUpgradedManager.setProjectUpgraded(userId, project.getId(), serviceType.getCode(), serviceTypeTo);
    }

    private void setImageUploaded(ProjectII project, String userId) {
        if (!imageUploadedIfNotIn.contains(project.getLatestStatus())) {
            projectStatusManager.updateStatus(project.getId(), Message.ProjectStatus.IMAGE_UPLOADED, userId);
        }
    }

    private void reopenIfProjectClosed(String userId, ProjectII project) {
        if (project.getCurrentState().getState() == Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE) {
            projectStateManager.changeProjectState(project.getId(), Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_OPEN, reopenChangeReason, userId);
        }
    }
}
