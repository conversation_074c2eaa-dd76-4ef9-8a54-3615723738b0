package com.bees360.service.impl;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Message;
import com.bees360.api.InvalidArgumentException;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.entity.label.ProjectLabel;
import com.bees360.mapper.label.ProjectLabelMapper;
import com.bees360.project.ProjectOperationTagManager;
import com.bees360.project.state.ProjectStateChangeReason;
import com.bees360.project.state.ProjectStateChangeReasonManager;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.UserService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.Iterables;
import com.bees360.web.event.beespilot.NewFirebaseProjectRelatedInfoChangedEvent;
import com.bees360.web.event.project.ProjectLabelChangedEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProjectLabelServiceImpl
        implements ProjectLabelService, ApplicationEventPublisherAware {

    @Autowired private ProjectLabelMapper projectLabelMapper;
    private ApplicationEventPublisher publisher;
    @Autowired private ProjectService projectService;
    @Autowired private ProjectStatusService projectStatusService;
    @Autowired private ActivityManager activityManager;
    @Autowired private ProjectOperationTagManager projectOperationTagManager;
    @Autowired private UserService userService;
    @Autowired private ProjectStateManager projectStateManager;
    @Autowired private ProjectStateChangeReasonManager projectStateChangeReasonManager;
    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;
    private static final String OPERATION_TAG = "Operation Tag";
    private static final String TAG_ID = "tagId";

    private static final String DENIED = "DENIED";
    private static final String DENIED_ON_LOCATION="DENIED ON LOCATION";
    private static final String CANCELLATION_UNCONFIRMED="CANCELLATION UNCONFIRMED";
    private static final String CANCELLATION_CONFIRMED="CANCELLATION CONFIRMED";
    private static final String UNABLE_TO_REACH_INSURED_AFTER_MULTIPLE_ATTEMPTS=
        "UNABLE TO REACH INSURED AFTER MULTIPLE ATTEMPTS";
    private static final String UNKNOWN="UNKNOWN";

    // project label 就是 project operation tag
    @Autowired
    @Qualifier("projectOperationTagAddedChecker")
    Optional<BiPredicate<Long, Long>> projectOperationTagAddedChecker;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.publisher = applicationEventPublisher;
    }

    @Override
    public List<ProjectLabel> labelList() {
        return projectLabelMapper.listLabels();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markAfterEraseLabel(
        long projectId, List<Long> labels, String userId, SystemTypeEnum updateSource) {
        var updatedLabels = getCheckedLabelsInProject(labels, projectId);
        log.info("get updatedLabels :{} from labels :{} by userId:{}", updatedLabels, labels, userId);
        // 筛选后的labels为空，且原labels不为空，不修改当前label
        if(CollectionUtils.isEmpty(updatedLabels) && CollectionUtils.isNotEmpty(labels)) {
            return;
        }
        labels = updatedLabels;
        labels = Optional.ofNullable(labels).orElse(Collections.emptyList());
        Optional<BoundProjectLabel> oldLabel = projectLabel(projectId);
        // tag id 只能有一个
        Long newTagId = labels.size() > 0 ? labels.get(0) : null;
        Long oldTagId =
                oldLabel.map(BoundProjectLabel::getProjectLabels).orElse(new ArrayList<>()).stream()
                        .map(ProjectLabel::getLabelId)
                        .findFirst()
                        .orElse(null);
        if (Objects.equals(newTagId, oldTagId)) {
            return;
        }
        eraseAllLabel(projectId, userId);
        markLabelsForProject(projectId, labels, userId);

        rollBackToCustomerContactedOnCondition(projectId, newTagId, oldTagId);
        switch (updateSource) {
            case BEES360:
            case BEES_AI:
                publisher.publishEvent(
                        new NewFirebaseProjectRelatedInfoChangedEvent(this, projectId, labels));
                publisher.publishEvent(
                        new ProjectLabelChangedEvent(this, projectId, labels, updateSource));
                submitChangedTagActivity(
                        Optional.ofNullable(newTagId).map(String::valueOf).orElse(""),
                        Optional.ofNullable(oldTagId).map(String::valueOf).orElse(""),
                        projectId,
                        userId);
                break;
            case BEES_PILOT:
                publisher.publishEvent(
                        new ProjectLabelChangedEvent(this, projectId, labels, updateSource));
                break;
            default:
                throw new IllegalArgumentException(
                        "illegal project label update source" + updateSource);
        }
    }

    private ProjectStateChangeReason findStateChangeReasonByKey(String changeReasonKey) {
        var projectStateChangeReason = Iterables.toStream(
            projectStateChangeReasonManager.findByQuery(List.of(), List.of(changeReasonKey), List.of())).findFirst().orElse(null);
        if (Objects.isNull(projectStateChangeReason)) {
            throw new IllegalStateException("No change reason with key %s found".formatted(changeReasonKey));
        }
        return projectStateChangeReason;
    }

    private void changeProjectState(String projectId,
                                    com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum stateEnum,
                                    String changeReason, String changedBy) {
        try{
            projectStateManager.changeProjectState(projectId,stateEnum, changeReason, changedBy,null,null);
        } catch (InvalidArgumentException | IllegalArgumentException e) {
            log.warn(
                "Failed to update project {} state(state={}, reason={}) by {}",
                projectId,
                stateEnum,
                changeReason,
                changedBy,
                e);
        }
    }

    @Deprecated
    @Nullable
    static String getChangeReasonFromOperationTag(Long tagId) {
        var tagEnum = ProjectLabelEnum.getEnum(tagId);
        if (tagEnum == null) {
            return null;
        }

        switch (tagEnum) {
            case DENIED:
                return DENIED;
            case DENIED_ON_LOCATION:
                return DENIED_ON_LOCATION;
            case CANCELLATION_UNCONFIRMED:
                return CANCELLATION_UNCONFIRMED;
            case CANCELLATION_CONFIRMED:
                return CANCELLATION_CONFIRMED;
            case UNABLE_TO_REACH_INSURED_AFTER_MULTIPLE_ATTEMPTS:
                return UNABLE_TO_REACH_INSURED_AFTER_MULTIPLE_ATTEMPTS;
        }
        return null;
    }

    private void submitChangedTagActivity(
            String newTagId, String oldTagId, long projectId, String createdBy) {
        activityManager.submitActivity(
                Activity.of(
                        Message.ActivityMessage.newBuilder()
                                .setAction(Message.ActivityMessage.ActionType.CHANGE.name())
                                .setEntity(
                                        Message.ActivityMessage.Entity.newBuilder()
                                                .setType(
                                                        Message.ActivityMessage.EntityType.PROJECT
                                                                .name())
                                                .setCount(1)
                                                .setId(String.valueOf(projectId))
                                                .build())
                                .setField(
                                        Message.ActivityMessage.Field.newBuilder()
                                                .setType(
                                                        Message.ActivityMessage.FieldType.INTEGER
                                                                .name())
                                                .setDisplayName(OPERATION_TAG)
                                                .setName(TAG_ID)
                                                .setValue(Optional.ofNullable(newTagId).orElse(""))
                                                .setOldValue(
                                                        Optional.ofNullable(oldTagId).orElse(""))
                                                .build())
                                .setProjectId(projectId)
                                .setSource(ActivitySourceEnum.WEB.getValue())
                                .setCreatedBy(
                                        com.bees360.user.Message.UserMessage.newBuilder()
                                                .setId(createdBy)
                                                .build())
                                .build()));
    }
    /**
     * 如果直接打上了 NO_LOR_ON_FILE 或者是 DRONE_INSPECTION_DECLINE直接将项目回滚到 【Customer Contacted】或者 【Project
     * Created】
     *
     * <p>当项目状态为ReturnToClient(Client Received 或者是 Return To Client)且取消了 CANCELLATION label
     * 将项目状态回滚到【Customer Contacted】或者 【Project Created】
     *
     * @param projectId 项目ID
     * @param newLabelId 要设置的LabelID
     * @param oldLabelId 原Label值
     */
    private void rollBackToCustomerContactedOnCondition(long projectId, Long newLabelId, Long oldLabelId) {
        if (ProjectLabelEnum.isPendingSchedule(newLabelId)) {
            rollProjectStatusToCustomerContactedOrLater(projectId);
            return;
        }

        // remove these operations tag will roll back project status to customer contacted or later
        if (ProjectLabelEnum.isCancellation(oldLabelId)
                && !ProjectLabelEnum.isCancellation(newLabelId)) {
            Project project = projectService.getById(projectId);
            if (NewProjectStatusEnum.isProjectReturnToClient(project.getProjectStatus())) {
                rollProjectStatusToCustomerContactedOrLater(projectId);
            }
        }
    }

    private void rollProjectStatusToCustomerContactedOrLater(long projectId) {
        try {
            projectStatusService.rollProjectStatusToCustomerContacted(projectId);
        } catch (ServiceException e) {
            throw new IllegalStateException(
                "Failed to roll back project '%s' to customer contacted.".formatted(projectId),
                    e);
        }
    }

    private void markLabelsForProject(long projectId, List<Long> labels, String userId) {

        if (labels == null || labels.isEmpty()) {
            return;
        }

        Optional<BoundProjectLabel> boundProjectLabel = projectLabel(projectId);
        if (boundProjectLabel.isEmpty()) {
            projectLabelMapper.markLabelsForProject(projectId, labels);
            labels.forEach(labelId -> projectOperationTagManager.updateByProjectIdAndTagId(
                String.valueOf(projectId), String.valueOf(labelId), userId));
        } else {
            List<ProjectLabel> exists = boundProjectLabel.get().getProjectLabels();
            Set<Long> existsIdSet =
                    exists.stream().map(ProjectLabel::getLabelId).collect(Collectors.toSet());
            List<Long> labels2Add =
                    labels.stream()
                            .filter(id -> !existsIdSet.contains(id))
                            .collect(Collectors.toList());
            projectLabelMapper.markLabelsForProject(projectId, labels2Add);
            labels2Add.forEach(labelId -> projectOperationTagManager.updateByProjectIdAndTagId(
                String.valueOf(projectId), String.valueOf(labelId), userId));
        }
    }

    private List<Long> getCheckedLabelsInProject(List<Long> labels, Long projectId) {
        if (CollectionUtils.isEmpty(labels)) {
            return labels;
        }
        return labels.stream().filter(
                label ->
                    projectOperationTagAddedChecker
                        .map(checker -> checker.test(projectId, label))
                        .orElse(true))
            .collect(Collectors.toList());
    }

    private void eraseLabelsForProject(long projectId, List<Long> labelIds) {
        projectLabelMapper.eraseLabelsForProject(projectId, labelIds);
    }

    private void eraseAllLabel(long projectId, String userId) {
        Optional<BoundProjectLabel> labels = projectLabel(projectId);
        labels.ifPresent(label -> {
            List<ProjectLabel> labelsToDelete = label.getProjectLabels();
            if (!CollectionUtils.isEmpty(labelsToDelete)) {
                labelsToDelete.forEach(labelToDelete -> projectOperationTagManager.deleteByProjectIdAndTagId(
                    String.valueOf(projectId), String.valueOf(labelToDelete.getLabelId()), userId));
            }
        });
        projectLabelMapper.eraseAllForProject(projectId);
    }

    @Override
    public List<BoundProjectLabel> projectLabelList(List<Long> projectIds) {
        return projectLabelMapper.getProjectLabels(projectIds);
    }

    @Override
    public Optional<BoundProjectLabel> projectLabel(Long projectId) {
        List<BoundProjectLabel> projectLabels =
                projectLabelMapper.getProjectLabels(Collections.singletonList(projectId));
        if (projectLabels == null || projectLabels.isEmpty()) {
            return Optional.empty();
        }
        return Optional.of(projectLabels.get(0));
    }

    @Override
    public void removeLabel(Long userId, Long projectId, ProjectLabelEnum projectLabel) {
        // 这里实际上project id 只会对应一个project label.
        List<ProjectLabel> tags = projectLabel(projectId)
            .map(BoundProjectLabel::getProjectLabels)
            .orElse(new ArrayList<>());

        tags.stream()
            .filter(label -> label.getLabelId() == projectLabel.getLabelId())
            .findFirst()
            .ifPresent(
                l -> markAfterEraseLabel(projectId, Collections.emptyList(), userId,
                    SystemTypeEnum.BEES360)
            );
    }

}
