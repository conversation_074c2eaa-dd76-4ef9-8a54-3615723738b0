package com.bees360.service.projectII;

import static com.bees360.base.MessageCode.PARAM_INVALID;
import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.address.Address;
import com.bees360.base.exception.ServiceMessageException;
import com.bees360.building.Message;
import com.bees360.catastrophe.ClaimCatastrophe;
import com.bees360.contract.Contract;
import com.bees360.contract.ContractManager;
import com.bees360.customer.Customer;
import com.bees360.customer.CustomerPolicyTypeManager;
import com.bees360.customer.Division;
import com.bees360.entity.Project;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.entity.vo.ProjectInspectionInfoVo;
import com.bees360.entity.vo.ProjectInsuredInfoVo;
import com.bees360.mapper.ProjectMapper;
import com.bees360.pipeline.PipelineService;
import com.bees360.pipeline.ProjectPipelineConfigService;
import com.bees360.policy.Coverage;
import com.bees360.policy.Policy;
import com.bees360.policy.PolicyManager;
import com.bees360.project.Building;
import com.bees360.project.BuildingManager;
import com.bees360.project.GenericProjectCreator;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.claim.Claim;
import com.bees360.project.inspection.Inspection;
import com.bees360.project.member.Member;
import com.bees360.project.member.RoleEnum;
import com.bees360.project.underwriting.Underwriting;
import com.bees360.service.member.PostgresMemberManagerAdapter;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.user.User;
import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;
import com.bees360.util.Null;
import com.bees360.web.event.project.ProjectCreatedEvent;
import com.bees360.web.project.util.ProjectAssemble;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.google.protobuf.StringValue;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/** 将web端的project业务请求转换成 solid里project模块的请求 */
@Log4j2
@Service
public class ProjectIIServiceAdapter {

    @Autowired private ProjectIIManager projectIIManager;
    @Autowired private PolicyManager policyManager;
    @Autowired private CustomerPolicyTypeManager customerPolicyTypeManager;
    @Autowired private ContractManager contractManager;
    @Autowired private BuildingManager buildingManager;
    @Autowired private PostgresMemberManagerAdapter postgresMemberManagerAdapter;
    @Autowired private GenericProjectCreator genericProjectCreator;
    @Autowired private ProjectMapper projectMapper;
    @Autowired private ProjectPipelineConfigService projectPipelineConfigService;
    @Autowired private PipelineService pipelineService;
    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;
    @Autowired private ApplicationEventPublisher publisher;
    @Value("${project.creation.default-building-type:RESIDENTIAL_SINGLE_FAMILY}")
    Message.BuildingType defaultBuildingType;

    public void createProject(long userId, Project project, String policyType, boolean allowDuplication) {
        // create policy, claim or underwriting or and inspection
        Policy policy = createPolicy(project, policyType);
        Contract contract = getContract(project.getInsuranceCompany(), project.getRepairCompany());
        Inspection inspection = createInspection(project);
        if (ClaimTypeEnum.isClaim(project.getClaimType())) {
            createClaimProject(userId, project, policy, contract, inspection, allowDuplication);
        } else {
            createUnderwritingProject(userId, project, policy, contract, inspection, allowDuplication);
        }
    }

    /**
     *  这里要挂起上下文的事务，避免mysql的数据不可见
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Project createProjectNew(
        long userId, Project project, String policyType, CreationChannelType creationChannel, boolean isAllowDuplication) {
        var projectCreationRequest = assembleProjectRequest(project, userId, policyType);
        var result = genericProjectCreator.create(projectCreationRequest, isAllowDuplication, creationChannel.name());
        var projectId = Long.parseLong(result.getId());
        project.setProjectId(projectId);

        if (!bees360FeatureSwitch.isEnableCreatedEvent()) {
            createPipeline(project);

            publisher.publishEvent(new ProjectCreatedEvent(this, projectMapper.getById(projectId), creationChannel));
        }

        log.info("Project Creation: create project {}.", result.toMessage());
        return projectMapper.getById(projectId);
    }

    private ProjectCreationRequest assembleProjectRequest(Project project, long createdBy, String policyType) {
        Policy policy = getPolicy(project, policyType);

        var builder = ProjectCreationRequest.ProjectBuilder.newBuilder();
        builder.setPolicy(policy);
        builder.setIsTestCase(project.isTestFlag());

        // set claim or underwriting
        if (ClaimTypeEnum.isClaim(project.getClaimType())) {
            builder.setProjectType(ProjectTypeEnum.CLAIM);
            builder.setClaim(getClaimProject(project));
            if (StringUtils.isNoneEmpty(project.getCatNumber())) {
                var cat = ClaimCatastrophe.from(project.getCatNumber(), project.getCatLevel());
                builder.setCatastrophe(cat);
            }
        } else {
            builder.setProjectType(ProjectTypeEnum.UNDERWRITING);
            builder.setUnderwriting(getUnderwritingProject(project));
        }


        var contractBuilder = Contract.ContractBuilder.newBuilder();
        Optional.ofNullable(project.getInsuranceCompany())
                .ifPresent(i ->
                    contractBuilder.setInsuredBy(
                        Customer.of(
                            com.bees360.customer.Message.CustomerMessage.newBuilder()
                                .setId(String.valueOf(i))
                                .build())));
        Optional.ofNullable(project.getRepairCompany())
                .ifPresent(i ->
                    contractBuilder.setProcessedBy(
                        Customer.of(
                            com.bees360.customer.Message.CustomerMessage.newBuilder()
                                .setId(String.valueOf(i))
                                .build())));
        builder.setContract(contractBuilder.build());
        builder.setCreatedBy(String.valueOf(createdBy));
        builder.setNote(project.getClaimNote());

        // set inspection
        builder.setInspection(createInspection(project));

        var contact = ProjectAssemble.getAgentAndInsured(project);
        builder.setContacts(contact);

        var member = getCreator(createdBy);
        builder.setMembers(List.of(member));

        // set external info
        builder.setOperatingCompany(project.getOperatingCompany());
        if (StringUtils.isNoneEmpty(project.getProjectDivision())) {
            builder.setProjectDivision(
                Division.from(
                com.bees360.customer.Message.CustomerMessage.Division.newBuilder()
                    .setId(project.getProjectDivision())
                    .build()));
        }

        // set metadata
        var projectMetadataBuilder = ProjectMessage.Metadata.newBuilder();
        acceptIfNotNull(projectMetadataBuilder::setCloneFrom, project.getCloneFrom(), StringValue::of);
        builder.setMetadata(projectMetadataBuilder.build());
        return builder.build();
    }

    private Member getCreator(long userId) {
        return new Member() {
            @Override
            public User getUser() {
                return User.from(
                    com.bees360.user.Message.UserMessage.newBuilder()
                        .setId(String.valueOf(userId))
                        .build()
                );
            }

            @Override
            public String getRole() {
                return RoleEnum.CREATOR.getValue();
            }
        };
    }

    /** 检测是否可以更新InspectionInfo相关信息 */
    public void checkUpdateProjectInspectionInfo(
            long projectId, ProjectInspectionInfoVo targetInfo, ProjectInspectionInfoVo existInfo)
            throws ServiceMessageException {
        var projectII = projectIIManager.findById(String.valueOf(projectId));
        if (projectII == null) {
            log.info("Update project, can not found project projectId '{}'", projectId);
            return;
        }
        // can not change claim to underwriting
        if (!ClaimTypeEnum.isTypeCanChange(targetInfo.getClaimType(), existInfo.getClaimType())) {
            throw new ServiceMessageException(PARAM_INVALID, "Can not change project type");
        }
        // year built
        if (!Objects.equals(existInfo.getYearBuilt(), targetInfo.getYearBuilt())
                && StringUtils.isNotBlank(targetInfo.getYearBuilt())) {
            if (!NumberUtils.isDigits(targetInfo.getYearBuilt())) {
                throw new ServiceMessageException(PARAM_INVALID, "YearBuilt should be a number");
            }
        }
        var existRepairCompany = Null.of(() -> existInfo.getRepairCompany().getCompanyId());
        var targetRepairCompany = Null.of(() -> targetInfo.getRepairCompany().getCompanyId());
        if (!Objects.equals(existRepairCompany, targetRepairCompany)) {
            throw new ServiceMessageException(PARAM_INVALID, "Process company can't be changed");
        }
        var existInsuranceCompany = Null.of(() -> existInfo.getInsuranceCompany().getCompanyId());
        var targetInsuranceCompany = Null.of(() -> targetInfo.getInsuranceCompany().getCompanyId());
        if (!Objects.equals(existInsuranceCompany, targetInsuranceCompany)) {
            throw new ServiceMessageException(PARAM_INVALID, "Insurance company can't be changed");
        }
    }

    /** 更新InspectionInfo相关信息 */
    public void updateProjectInspectionInfo(
            long projectId, ProjectInspectionInfoVo targetInfo, ProjectInspectionInfoVo existInfo) {
        var projectII = projectIIManager.findById(String.valueOf(projectId));
        if (projectII == null) {
            log.info("Update project, can not found project projectId '{}'", projectId);
            return;
        }

        // claim type
        if (!Objects.equals(existInfo.getClaimType(), targetInfo.getClaimType())) {
            projectIIManager.updateClaimType(
                    projectII.getId(),
                    com.bees360.project.claim.ClaimTypeEnum.valueOf(targetInfo.getClaimType()));
        }

        // date of loss
        if (!Objects.equals(existInfo.getDamageEventTime(), targetInfo.getDamageEventTime())) {
            var dateOfLoss =
                    targetInfo.getDamageEventTime() == null
                            ? null
                            : LocalDate.ofInstant(
                                    Instant.ofEpochMilli(targetInfo.getDamageEventTime()),
                                    ZoneId.systemDefault());
            projectIIManager.updateDateOfLoss(projectII.getId(), dateOfLoss);
        }

        var policyId = projectII.getPolicy().getId();
        var policy = policyManager.findById(policyId);
        // policy no
        if (!Objects.equals(existInfo.getPolicyNumber(), targetInfo.getPolicyNumber())) {
            policyManager.updatePolicyNo(policyId, targetInfo.getPolicyNumber());
        }
        // policy effective date
        if (!Objects.equals(
                existInfo.getPolicyEffectiveDate(), targetInfo.getPolicyEffectiveDate())) {
            policyManager.updatePolicyEffectiveDate(policyId, targetInfo.getPolicyEffectiveDate());
        }

        // year built
        if (!Objects.equals(existInfo.getYearBuilt(), targetInfo.getYearBuilt())
                && StringUtils.isNotBlank(targetInfo.getYearBuilt())) {
            var yearBuilt = Integer.parseInt(targetInfo.getYearBuilt());
            buildingManager.updateYearBuilt(policy.getBuilding().getId(), yearBuilt);
        }

        // If project is claim, update claim number.
        if (Objects.equals(ProjectTypeEnum.CLAIM, projectII.getProjectType())
                && !Objects.equals(projectII.getClaimNo(), targetInfo.getInspectionNumber())) {
            projectIIManager.updateClaimNo(projectII.getId(), targetInfo.getInspectionNumber());
        }

        if (!Objects.equals(existInfo.getInspectionNumber(), targetInfo.getInspectionNumber())
                || !Objects.equals(
                        projectII.getInspectionNo(), targetInfo.getInspectionNumber())) {
            projectIIManager.updateInspectionNo(
                    projectII.getId(), targetInfo.getInspectionNumber());
        }
    }

    /** 更新InsuredInfo相关信息 */
    public void updateProjectInsuredInfo(
            long projectId,
            ProjectInsuredInfoVo insuredInfoVo,
            ProjectInsuredInfoVo oldInsuredInfo) {
        var projectII = projectIIManager.findById(String.valueOf(projectId));
        if (projectII == null) {
            return;
        }
        Policy policy = policyManager.findById(projectII.getPolicy().getId());
        if (!Objects.equals(insuredInfoVo.getProjectType(), oldInsuredInfo.getProjectType())
                && insuredInfoVo.getProjectType() != null) {
            buildingManager.updateBuildingType(
                    policy.getBuilding().getId(),
                    Message.BuildingType.forNumber(insuredInfoVo.getProjectType()));
        }
        var addressId = policy.getAddress().getId();
        if (!Objects.equals(insuredInfoVo.getAddressId(), addressId)) {
            policyManager.updateAddressId(policy.getId(), insuredInfoVo.getAddressId());
        }
    }

    public void updatePolicyEffectiveDate(long projectId, LocalDate policyEffectiveDate) {
        var projectII = projectIIManager.findById(String.valueOf(projectId));
        if (projectII == null) {
            return;
        }
        policyManager.updatePolicyEffectiveDate(projectII.getId(), policyEffectiveDate);
    }

    /** 更新serviceType */
    public void updateServiceType(long projectId, Integer oldServiceType, Integer newServiceType) {
        var projectII = projectIIManager.findById(String.valueOf(projectId));
        if (projectII == null) {
            return;
        }
        if (!Objects.equals(oldServiceType, newServiceType)) {
            projectIIManager.updateServiceType(
                    String.valueOf(projectId), ServiceTypeEnum.valueOf(newServiceType));
        }
    }

    /** 检查contract是否正确 */
    public boolean checkContract(Long insuranceCompany, Long processCompany) {
        return getContract(insuranceCompany, processCompany) != null;
    }

    private Underwriting getUnderwritingProject(Project project) {
        return Underwriting.UnderwritingBuilder.newBuilder()
                .setServiceType(ServiceTypeEnum.valueOf(project.getServiceType()))
                .setSupplementalService(project.getSupplementalServices())
                .build();
    }

    /** 创建underwriting项目 */
    private void createUnderwritingProject(
            long userId, Project project, Policy policy, Contract contract, Inspection inspection, boolean allowDuplication) {
        var underwriting = getUnderwritingProject(project);
        projectIIManager.create(
                String.valueOf(project.getProjectId()),
                underwriting,
                inspection,
                contract.getId(),
                policy.getId(),
                String.valueOf(userId),
                allowDuplication,
                project.isTestFlag());
    }

    private Claim getClaimProject(Project project) {
        return Claim.ClaimBuilder.newBuilder()
                .setClaimNo(project.getInspectionNumber())
                .setServiceType(ServiceTypeEnum.valueOf(project.getServiceType()))
                .setClaimType(
                    com.bees360.project.claim.ClaimTypeEnum.valueOf(
                        project.getClaimType()))
                .setDateOfLoss(
                    Optional.ofNullable(project.getDamageEventTime())
                        .map(
                            e ->
                                LocalDate.ofInstant(
                                    Instant.ofEpochMilli(
                                        project
                                            .getDamageEventTime()),
                                    ZoneId.systemDefault()))
                        .orElse(null))
                .setSupplementalService(project.getSupplementalServices())
                .build();
    }

    /** 创建Claim项目 */
    private void createClaimProject(
            long userId, Project project, Policy policy, Contract contract, Inspection inspection, boolean allowDuplication) {
        log.info("Create project with supplemental services {}", project.getSupplementalServices());
        var claim = getClaimProject(project);
        projectIIManager.create(
                String.valueOf(project.getProjectId()),
                claim,
                inspection,
                contract.getId(),
                policy.getId(),
                String.valueOf(userId),
                allowDuplication,
                project.isTestFlag());
    }

    /** 创建检查单 */
    private Inspection createInspection(Project project) {
        if (project.getInspectionNumber() == null && project.getInspectionTime() == null) {
            return null;
        }
        return Inspection.InspectionBuilder.newBuilder()
                .setInspectionNo(project.getInspectionNumber())
                .setAppointmentTime(
                        Optional.ofNullable(project.getInspectionTime())
                                .map(Instant::ofEpochMilli)
                                .orElse(null))
                .setDueDate(
                    Optional.ofNullable(project.getDueDate())
                            .map(d -> DateTimes.toLocalDate(Instant.ofEpochMilli(d)))
                            .orElse(null))
                .setCarrierProvidedLivingArea(project.getCarrierProvidedLivingArea())
                .build();
    }

    /** 查询是存在合约 */
    private Contract getContract(Long insuranceCompany, Long processCompany) {
        var insuredId = String.valueOf(insuranceCompany);
        var processedId = String.valueOf(processCompany);
        return contractManager.findByCompanyId(
                Objects.equals(insuredId, "null") ? null : insuredId,
                Objects.equals(processedId, "null") ? null : processedId);
    }

    /** 创建policy保单 */
    private Policy getPolicy(Project project, String policyType) {
        // 由于 policyType 需要关联公司, 移除了 policyManager#create 在 policyType 不存在时创建 policyType, policyType 创建移到创建 policy 之前
        if (project.getInsuranceCompany() != null && StringUtils.isNotEmpty(policyType)) {
            var customerId = String.valueOf(project.getInsuranceCompany());
            if (!Iterables.contains(customerPolicyTypeManager.listCustomerPolicyType(customerId), policyType)) {
                customerPolicyTypeManager.createPolicyType(customerId, List.of(policyType));
            }
        }

        var address = getAddress(project);
        var buildingBuilder = Building.BuildingBuilder.newBuilder();
        buildingBuilder.setBuildingType(Optional.ofNullable(project.getProjectType())
            .map(Message.BuildingType::forNumber)
            .orElse(defaultBuildingType));
        Optional.ofNullable(project.getLivingArea())
                .ifPresent(buildingBuilder::setLivingArea);
        if (StringUtils.isNotBlank(project.getYearBuilt())) {
            int year = Integer.parseInt(project.getYearBuilt());
            buildingBuilder.setYearBuilt(year);
        }

        var coverage = createCoverage(project);
        return Policy.PolicyBuilder.newBuilder()
                        .setBuilding(buildingBuilder.build())
                        .setAddress(address)
                        .setIsRenewal(project.getIsRenewal())
                        .setCoverage(coverage)
                        .setPolicyNo(project.getPolicyNumber())
                        .setPolicyEffectiveDate(project.getPolicyEffectiveDate())
                        .setType(policyType)
                        .build();
    }

    private Address getAddress(Project project) {
        if (project.getAddressId() != null) {
            return Address.AddressBuilder.newBuilder().setId(project.getAddressId()).build();
        }

        var addressBuilder = Address.AddressBuilder.newBuilder()
            .setStreetAddress(project.getAddress())
            .setCity(project.getCity())
            .setState(project.getState())
            .setCountry(project.getCountry())
            .setZip(project.getZipCode())
            .setLat(project.getLat())
            .setLng(project.getLng());
        return addressBuilder.build();
    }

    private Policy createPolicy(Project project, String policyType) {
        var policy = getPolicy(project, policyType);
        var address =  policy.getAddress();
        var building = buildingManager.create(policy.getBuilding(), address.getId());
        return policyManager.create(
            policy.getPolicyNo(),
            policy.getPolicyEffectiveDate(),
            address.getId(),
            building.getId(),
            policy.getType(),
            policy.getCoverage(),
            policy.isRenewal());
    }

    private List<Coverage> createCoverage(Project project) {
        if (Objects.isNull(project.getDwellingCoverage())) {
            return null;
        }

        List<Coverage> coverages = new ArrayList<>();
        var dwellingCoverage = Coverage.from(
            com.bees360.policy.Message.PolicyMessage.CoverageMessage.newBuilder()
                .setType(com.bees360.policy.Message.CoverageType.DWELLING.name())
                .setAmount(project.getDwellingCoverage())
                .build());
        coverages.add(dwellingCoverage);
        Optional.ofNullable(project.getOtherStructureCoverage())
            .ifPresent(
                c -> {
                    var otherStructureCoverage = Coverage.from(
                        com.bees360.policy.Message.PolicyMessage.CoverageMessage.newBuilder()
                            .setType(com.bees360.policy.Message.CoverageType.OTHER_STRUCTURE.name())
                            .setAmount(project.getOtherStructureCoverage())
                            .build());
                    coverages.add(otherStructureCoverage);
                }
            );
        Optional.ofNullable(project.getContentCoverage())
            .ifPresent(
                c -> {
                    var contentCoverage = Coverage.from(
                        com.bees360.policy.Message.PolicyMessage.CoverageMessage.newBuilder()
                            .setType(com.bees360.policy.Message.CoverageType.CONTENT.name())
                            .setAmount(project.getContentCoverage())
                            .build());
                    coverages.add(contentCoverage);
                }
            );

        return coverages;
    }

    public void setProjectDivision(long projectId, String divisionId) {
        projectIIManager.setCustomerDivision(String.valueOf(projectId), divisionId);
    }

    public void createPipeline(Project project) {
        var projectId = String.valueOf(project.getProjectId());
        var insuredBy =
            Optional.ofNullable(project.getInsuranceCompany())
                .map(String::valueOf)
                .orElse(null);
        var serviceType = project.getServiceType();

        var processedBy =
            Optional.ofNullable(project.getRepairCompany()).map(String::valueOf).orElse(null);
        var pipelineDefKey =
            projectPipelineConfigService.findPipelineDefKey(
                insuredBy, processedBy, serviceType);
        if (Strings.isBlank(pipelineDefKey)) {
            log.warn("There is no pipeline definition found for project '{}'", projectId);
            return;
        }

        pipelineService.createPipeline(projectId, pipelineDefKey);
    }
}
