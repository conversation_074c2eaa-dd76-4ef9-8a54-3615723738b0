package com.bees360.service.listener.report;

import com.bees360.entity.User;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.report.ReportProvider;

import com.bees360.service.job.ProjectReturnToClientJob;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Duration;
import java.util.Objects;

/**
 * 监听报告组添加事件，当接收到特定类型的报告组时，调度一个项目返回客户端的任务。
 */
@Log4j2
@Component
public class ReturnToClientOnLOCReportGroupAdded
        extends AbstractNamedEventListener<ReportGroupAdded> {

    private final JobScheduler jobScheduler;

    private final ReportProvider reportProvider;

    public ReturnToClientOnLOCReportGroupAdded(
            JobScheduler jobScheduler,
            ReportProvider reportProvider) {
        this.jobScheduler = jobScheduler;
        this.reportProvider = reportProvider;
        log.info(
                "Created {}(jobScheduler={}, reportProvider={}).",
                this,
                jobScheduler,
                reportProvider);
    }

    @Override
    public void handle(ReportGroupAdded event) throws IOException {
        log.info("Listening event {} for return to client.", event);
        if (!DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE.equalsIgnoreCase(
                event.getGroupType())) {
            return;
        }
        var report = reportProvider.get(event.getReportId());
        var reportType = ReportTypeEnum.getReportType(report);
        if (!Objects.equals(reportType, ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT)) {
            return;
        }
        var projectId = Long.parseLong(event.getGroupKey());
        Job job =
                RetryableJob.of(
                        new ProjectReturnToClientJob(User.AI_ID, projectId),
                        100,
                        Duration.ofMinutes(10),
                        null);
        jobScheduler.schedule(job);
    }
}
