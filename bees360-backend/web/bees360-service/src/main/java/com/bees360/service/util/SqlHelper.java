package com.bees360.service.util;

import com.google.common.base.Splitter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2019/12/18 17:42
 */
public class SqlHelper {

    private static final String ANY_SPACE_REGEX = " +";

    private static final String ANY_NON_NUMBER_REGEX = "\\D";

    private SqlHelper() {
        throw new AssertionError("Can't create instance.");
    }

    public static String createFuzzyRegex(String value) {
        if(StringUtils.isBlank(value)) {
            return value;
        }
        String[] valueRegexParts = value.split(ANY_SPACE_REGEX);
        if(valueRegexParts.length == 0) {
            return "";
        }
        String fuzzyRegex = String.join("%", valueRegexParts);
        fuzzyRegex = fuzzyRegex.endsWith("%")? fuzzyRegex: fuzzyRegex + "%";
        fuzzyRegex = fuzzyRegex.startsWith("%")? fuzzyRegex: "%" + fuzzyRegex;
        return fuzzyRegex;
    }

    public static String createFuzzyRegex(Long value) {
        return value == null? null: "%" + value + "%";
    }

    public static String createNumberFuzzyRegex(String value) {
        return value == null? null: "%" + value + "%";
    }

    public static String createPhoneFuzzyRegex(String value) {
        if(StringUtils.isBlank(value)) {
            return value;
        }

        String[] valueRegexParts = value.split(ANY_NON_NUMBER_REGEX);
        if (valueRegexParts.length == 0) {
            return "";
        }

        var result = new StringBuilder("%");
        for (String valueRegexPart : valueRegexParts) {
            if (valueRegexPart.length() == 0){
                continue;
            }

            if (valueRegexPart.length() >= 4) {
                result.append(splitPhoneString(valueRegexPart)).append("%");
            } else {
                result.append(valueRegexPart).append("%");
            }
        }

        return result.length() == 1 ? "" : result.toString();
    }

    private static String splitPhoneString(String value) {
        var parts = Splitter.fixedLength(3).split(value);
        return String.join("%", parts);
    }
}
