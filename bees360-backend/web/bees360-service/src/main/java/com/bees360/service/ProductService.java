package com.bees360.service;

import java.util.List;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Product;
import com.bees360.entity.enums.productandpayment.ProductTypeEnum;
import com.bees360.entity.vo.ProductReportVo;
import com.bees360.entity.vo.ProductListVo;

public interface ProductService {

	List<ProductReportVo> listOrderableReports() throws ServiceException;

	List<Product> listPublishedProducts(Integer productType) throws ServiceException;

	List<Product> listProducts(Integer productType, Boolean orderable) throws ServiceException;

	List<Product> listAllProducts() throws ServiceException;

    Product updateProductInfo(long userId, int productId, Product product) throws ServiceException;
}
