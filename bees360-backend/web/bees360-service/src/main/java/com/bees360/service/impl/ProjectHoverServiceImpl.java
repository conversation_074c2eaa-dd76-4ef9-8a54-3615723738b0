package com.bees360.service.impl;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.commons.hover.HoverClient;
import com.bees360.commons.hover.entity.CreateJobRequestEntity;
import com.bees360.commons.hover.entity.CreateJobResponseEntity;
import com.bees360.entity.Project;
import com.bees360.entity.User;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.service.ProjectHoverService;
import com.bees360.service.ProjectService;
import com.bees360.service.UserService;
import com.bees360.service.properties.HoverProperties;
import com.google.common.base.Preconditions;
import jakarta.annotation.Nonnull;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProjectHoverServiceImpl implements ProjectHoverService {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private UserService userService;

    @Autowired
    private HoverClient hoverClient;

    @Autowired
    private HoverProperties hoverProperties;

    @Override
    public long createHoverJob(long projectId) throws Exception {
        Project project = projectService.getById(projectId);
        List<UserTinyVo> pilots = userService.listMemberInProject(projectId, RoleEnum.PILOT);
        Long pilotId = pilots.stream().map(u -> u.getUserId()).findFirst().orElse(null);
        User pilot = pilotId == null? null: userService.getUserById(pilotId);
        if (Objects.isNull(pilot)) {
            throw new ServiceException(MessageCode.PARAM_INVALID, "pilot doesn't exist.");
        }
        return createHoverJob(project, pilot);
    }

    @Override
    public long createHoverJob(long projectId, long pilotId) throws Exception {
        Project project = projectService.getById(projectId);
        User pilot = userService.getUserById(pilotId);
        if (Objects.isNull(pilot)) {
            throw new ServiceException(MessageCode.PARAM_INVALID, "pilot `" + pilotId + "` doesn't exist.");
        }
        return createHoverJob(project, pilot);
    }

    private long createHoverJob(@Nonnull Project project, @Nonnull User pilot) throws Exception {
        Preconditions.checkNotNull(project, "project shouldn't be null.");
        Preconditions.checkNotNull(pilot, "pilot shouldn't be null.");

        long projectId = project.getProjectId();
        long pilotId = pilot.getUserId();

        HoverProperties.HoverJob hoverJobProperties = hoverProperties.getJob();

        CreateJobRequestEntity jobEntity = CreateJobRequestEntity.builder()
//                .name(project.getInspectionNumber())
            .name(project.getAssetOwnerName())
            .customer_name(StringUtils.defaultIfEmpty(hoverJobProperties.getCustomerName(), ""))
            .customer_email(StringUtils.defaultIfEmpty(hoverJobProperties.getCustomerEmail(), ""))
            .customer_phone(StringUtils.defaultIfEmpty(hoverJobProperties.getCustomerPhone(), ""))
            .location_line_1(project.getAddress())
            .location_city(project.getCity())
            .location_region(project.getState())
            .location_country(project.getCountry())
            .location_postal_code(project.getZipCode())
            .location_lat(project.getLat())
            .location_lon(project.getLng())
            .deliverable_id(CreateJobRequestEntity.COMPLETE_DELIVERABLE_ID)
            .external_identifier(StringUtils.defaultIfEmpty(project.getInspectionNumber(), ""))
            .current_user_email(pilot.getEmail())
            .build();
        log.info("try to create hover job for project {}, pilotId = {}", projectId, pilotId);
        CreateJobResponseEntity responseEntity = hoverClient.forJob().createJob(jobEntity);

        Long hoverJobId = responseEntity.getJob().getId();
        projectService.updateHoverJobId(projectId, hoverJobId);

        return hoverJobId;
    }
}
