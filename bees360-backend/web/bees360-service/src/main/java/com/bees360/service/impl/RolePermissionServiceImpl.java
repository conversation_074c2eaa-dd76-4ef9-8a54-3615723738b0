package com.bees360.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Permission;
import com.bees360.entity.RolePermission;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.mapper.RolePermissionMapper;
import com.bees360.service.PermissionCacheService;
import com.bees360.service.RolePermissionService;

@Service("rolePermissionService")
public class RolePermissionServiceImpl implements RolePermissionService {

	private Logger logger = LoggerFactory.getLogger(RolePermissionServiceImpl.class);

	@Inject
	private RolePermissionMapper rolePermissionMapper;

	@Inject
	private PermissionCacheService permissionCacheService;

	@Override
	public Map<RoleEnum, List<RolePermission>> getRolePermissionsByRoles(long roles) throws ServiceException {
		Map<RoleEnum, List<RolePermission>> rolePermissionMap = null;
		List<RoleEnum> roleEnums = RoleEnum.listRoles(roles);
		List<Integer> roleIds = roleEnums.stream().map(RoleEnum::getRoleId).collect(Collectors.toList());
		List<RolePermission> rolePermissions = getRolePermissionsByRoleIds(roleIds);

		//set permssion for role
		try {
			if (rolePermissions != null && rolePermissions.size() > 0) {
				Map<Long,Permission> permissionMap = permissionCacheService.getPermissionMap();
				logger.info("permissionMap:" + permissionMap);
				rolePermissions.stream().forEach(
						rolePermission -> {
							long permissionId = rolePermission.getPermissionId();
							rolePermission.setPermission(permissionMap.get(permissionId));
							long parentId = rolePermission.getPermission().getParentId();
							rolePermission.getPermission().setParent(permissionMap.get(parentId));
						}
				);
			}
		}catch(Exception e) {
			logger.error(e.getMessage(), e);
		}
		rolePermissionMap = getRolePermissionMap(rolePermissions);
		return rolePermissionMap;
	}

	@Override
	public List<RolePermission> getRolePermissionsByRoleIds(List<Integer> roleIds) throws ServiceException {
		if(roleIds == null || roleIds.size()==0) {
			return new ArrayList<>();
		}
		try {
			return rolePermissionMapper.getByRoleIds(roleIds);
		} catch (Exception e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

	@Override
	public List<RolePermission> getRolePermissions(int roleId) throws ServiceException {
		try {
			return rolePermissionMapper.getByRoleId(roleId);
		} catch (Exception e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

	private Map<RoleEnum, List<RolePermission>> getRolePermissionMap(List<RolePermission> rolePermissions) {
		if (rolePermissions == null || rolePermissions.size() == 0) {
			return null;
		}

		Map<RoleEnum, List<RolePermission>> rolePermissionMap = new HashMap<RoleEnum, List<RolePermission>>();
		for (RolePermission rolePermission : rolePermissions) {
			int roleId = rolePermission.getRoleId();
			RoleEnum roleEnum = RoleEnum.getEnum(roleId);

			if (rolePermissionMap.get(roleEnum) == null) {
				rolePermissionMap.put(roleEnum, new ArrayList<RolePermission>());
			}
			rolePermissionMap.get(roleEnum).add(rolePermission);
		}
		return rolePermissionMap;
	}
}
