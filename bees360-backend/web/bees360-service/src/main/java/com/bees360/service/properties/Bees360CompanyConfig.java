package com.bees360.service.properties;

import com.bees360.entity.Project;
import com.bees360.entity.enums.ProjectTypeEnum;
import com.bees360.entity.vo.UserTinyVo;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Data
@Component
@Log4j2
@ConfigurationProperties(prefix = "bees360.company-config")
public class Bees360CompanyConfig {

    /**
     * 全局配置，当公司有自定义配置时，使用公司配置
     */
    private final CompanyConfigGlobal global = new CompanyConfigGlobal();
    /**
     * 公司自定义配置
     */
    private final List<CompanyConfigItem> companies = new ArrayList<>();

    @Data
    public static class CompanyConfigGlobal {
        /**
         * 项目完成之后（指return to client)，自动变更状态为 client received
         */
        private boolean autoClientReceived = true;
        private CompanyAutoClientReceivedConfig autoClientReceivedConfig = new CompanyAutoClientReceivedConfig();
    }

    @Data
    public static class CompanyAutoClientReceivedConfig {
        // 默认为不排除任何
        private Set<Integer> serviceTypeExclude = new HashSet<>();

        public boolean isServiceTypeHit(Integer serviceType) {
            return !serviceTypeExclude.contains(serviceType);
        }
    }

    private Pattern hailNeededRegex;

    public void setHailNeededRegex(String pattern) {
        this.hailNeededRegex = Pattern.compile(pattern);
    }

    @Data
    public static class CompanyConfigItem {
        /**
         * company id
         */
        @Min(1)
        private long id;
        /**
         * 报告配置选项
         */
        private CompanyConfigReport report;
        /**
         * hover 配置选项
         */
        private CompanyConfigHover hover;
        /**
         * 项目完成之后（指return to client)，自动变更状态为 client received
         */
        private Boolean autoClientReceived;
        /**
         * 决定生成的invoice项价格
         */
        private String shopCode = "";
        /**
         * open api配置
         */
        private final OpenApiConfig openApi = new OpenApiConfig();
        /**
         * 用来表示是否需要自动打hailNeededTag
         */
        private boolean autoTagHailNeeded = true;
        /**
         * 用来抽取损失描述de正则表达式
         */
        private Pattern lossDescExtractRegex;
        /**
         * 用来判断是否是需要被紧急处理的项目的正则
         */
        private Pattern emergencyCaseFilterRegex;
        /**
         * 用来判断是否是需要被快速检查的项目的正则
         */
        private Pattern quickInspectCaseFilterRegex;
        /**
         * 用来抽取policyType正则表达式
         */
        private Pattern policyTypeExtractRegex;

        /**
         * Configure how to map the project type.
         */
        private ProjectTypeMappingProperties projectTypeMapping;

        private String transferName;

        private Map<String, List<CompanyRecipientInfo>> emailRecipientMap;

        public void setLossDescExtractRegex(String pattern) {
            this.lossDescExtractRegex = Pattern.compile(pattern);
        }

        public void setEmergencyCaseFilterRegex(String pattern) {
            this.emergencyCaseFilterRegex = Pattern.compile(pattern);
        }

        public void setQuickInspectCaseFilterRegex(String pattern) {
            this.quickInspectCaseFilterRegex = Pattern.compile(pattern);
        }

        public void setPolicyTypeExtractRegex(String pattern) {
            this.policyTypeExtractRegex = Pattern.compile(pattern);
        }

        private ContractConfig contract;

        private Set<String> unsubscribeEmail;
    }


    @Data
    @Valid
    public static class ProjectTypeMappingProperties {
        @NotEmpty
        private List<String> sourcePolicyType;
        @NotNull
        private ProjectTypeEnum targetProjectType;
    }

    @Data
    public static class CompanyConfigReport {
        /**
         * 报告需要压缩，该值为报告压缩的最大值
         */
        @Min(1)
        private Integer compressedSize;

        private boolean generateCloseoutReport = false;

        private CloseoutReport closeoutReport;
    }

    @Data
    public static class CloseoutReport {
        private String caseTypePrefix;
    }

    @Data
    public static class CompanyConfigHover{
        private Boolean subscribe;
    }

    @Data
    public static class OpenApiConfig {
        /**
         * 通过open api创建项目时会先通过制定的preHandler处理
         */
        private String projectCreationPreHandler;
    }

    @Data
    public static class ContractConfig {
        private List<String> insuredBy;
        private String processedBy;
    }

    @Nullable
    public CompanyConfigItem findConfig(Long companyId) {
        return companies.stream()
                .filter(c -> Objects.equals(c.getId(), companyId))
                .findFirst()
                .orElse(null);
    }

    @Data
    public static class CompanyRecipientInfo {
        private String recipientFirstName;
        private String recipientLastName;
        private String recipientEmail;
    }

    public List<UserTinyVo> projectEmailSubscription(Project project, String template) {
        log.info("Start to get subscription from project :{} in :{}", project, template);
        return getCompanies().stream()
                .filter(
                        c ->
                                Objects.equals(c.getId(), project.getInsuranceCompany())
                                        || Objects.equals(c.getId(), project.getRepairCompany()))
                .flatMap(
                        co -> {
                            var emailRecipientMap = co.getEmailRecipientMap();
                            if (Objects.isNull(emailRecipientMap)) {
                                return null;
                            }
                            var recipientInfos =
                                    emailRecipientMap.getOrDefault(
                                            template, Collections.emptyList());
                            return recipientInfos.stream()
                                    .map(
                                            info -> {
                                                var email = info.getRecipientEmail();
                                                if (Objects.isNull(email)) {
                                                    return null;
                                                }
                                                log.info("find user send email :{}", email);
                                                var userInfo = new UserTinyVo();
                                                userInfo.setEmail(email);
                                                userInfo.setFirstName(
                                                        Optional.ofNullable(
                                                                        info.getRecipientFirstName())
                                                                .orElse(""));
                                                userInfo.setLastName(
                                                        Optional.ofNullable(
                                                                        info.getRecipientLastName())
                                                                .orElse(""));
                                                return userInfo;
                                            });
                        })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
