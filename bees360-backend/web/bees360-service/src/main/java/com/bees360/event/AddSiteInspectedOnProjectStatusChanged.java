package com.bees360.event;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.User;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.event.registry.ProjectStatusChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectMapper;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.service.ProjectImageService;
import com.bees360.service.ProjectStatusService;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.stream.Collectors;

import static com.bees360.entity.enums.NewProjectStatusEnum.SITE_INSPECTED;

/**
 * 监听项目状态变更事件，当项目状态达到特定条件时自动添加现场检查状态记录。
 */
@Log4j2
public class AddSiteInspectedOnProjectStatusChanged extends AbstractNamedEventListener<ProjectStatusChanged> {

    private final ProjectMapper projectMapper;

    private final ProjectImageService projectImageService;

    private final ProjectStatusService projectStatusService;

    public AddSiteInspectedOnProjectStatusChanged(ProjectMapper projectMapper,
                                                  ProjectImageService projectImageService,
                                                  ProjectStatusService projectStatusService){
        this.projectMapper = projectMapper;
        this.projectImageService = projectImageService;
        this.projectStatusService = projectStatusService;
        log.info("Created {}(projectMapper={}, projectImageService={}" +
            "projectStatusService={}).",
            this,
            this.projectMapper,
            this.projectImageService,
            this.projectStatusService);
    }


    @Override
    public void handle(ProjectStatusChanged event) throws IOException{
        if(event.getStatus() < NewProjectStatusEnum.IMAGE_UPLOADED.getCode()) {
            return;
        }

        var projectId = Long.parseLong(event.getProjectId());
        var project = projectMapper.getById(projectId);

        // express service type忽略
        if (project == null
            || project.getServiceType() == ServiceTypeEnum.EXPRESS_UNDERWRITING.getCode()
            || checkIfSiteInspectedStatusExists(projectId)) {
            return;
        }

        var siteInspectedTime = getImageMiddleShootingTime(projectId);
        if (siteInspectedTime == null) {
            log.warn("Fail to set SITE_INSPECTED status for project {} due to images not exist.", projectId);
            return;
        }

        projectStatusService.insertProjectStatus(
            projectId, User.BEES_PILOT_SYSTEM, SITE_INSPECTED.getCode(), siteInspectedTime);
        log.info("Successfully add SITE_INSPECTED status for project {} with event {}.", projectId, event);
    }

    private boolean checkIfSiteInspectedStatusExists(Long projectId) {
        return projectStatusService.listProjectStatus(projectId)
            .stream().anyMatch(s -> s.getStatus() == SITE_INSPECTED.getCode());
    }

    private Long getImageMiddleShootingTime(Long projectId) {
        var images = projectImageService.listAll(projectId);

        if (CollectionUtils.isEmpty(images)) {
            return null;
        }

        var timeList = images.stream().map(ProjectImage::getShootingTime).collect(Collectors.toList());

        var max = Collections.max(timeList);
        var min = Collections.min(timeList);
        return Math.floorDiv(Math.addExact(min, max), 2);
    }
}
