package com.bees360.event;

import com.bees360.event.registry.ActivityChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectMapper;
import com.bees360.web.event.project.ProjectCommentAddedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;

/**
 * 移除spring事物逻辑，将相关逻辑拆分到以下类：
 * {@link UpdateProjectNoteOnActivityChanged}
 * {@link InitLossDescriptionOnActivityChanged}
 */
@Deprecated
public class PublishProjectCommentAddedOnActivityAdded extends AbstractNamedEventListener<ActivityChangedEvent> {
    @Autowired
    private ApplicationEventPublisher publisher;
    @Autowired private ProjectMapper projectMapper;

    @Override
    public void handle(ActivityChangedEvent activityChangedEvent) throws IOException {
        var activity = activityChangedEvent.getSource();
        var comment = activity.getComment();
        if (comment == null){
            return;
        }

        var projectId = comment.getProjectId();
        var project = projectMapper.getById(projectId);

        // 这里可能project还没被创建出来
        if (project == null) {
            return;
        }

        publisher.publishEvent(new ProjectCommentAddedEvent(this, project, comment));
    }
}
