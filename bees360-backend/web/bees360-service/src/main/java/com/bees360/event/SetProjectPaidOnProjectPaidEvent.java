package com.bees360.event;

import com.bees360.event.registry.ProjectPaid;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ProjectService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 监听项目支付事件并更新项目支付状态为已支付
 */
@Log4j2
public class SetProjectPaidOnProjectPaidEvent extends AbstractNamedEventListener<ProjectPaid> {

    private static final int PAID_STATUS = 1;

    private final ProjectService projectService;

    public SetProjectPaidOnProjectPaidEvent(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Override
    public void handle(ProjectPaid projectPaid) throws IOException {
        List<Long> projectIds = projectPaid.getProjectIds().stream()
            .map(Long::parseLong).collect(Collectors.toList());
        projectService.updatePayStatus(projectIds, PAID_STATUS);
        log.info("Set project status paid for projects: {}", projectIds);
    }
}
