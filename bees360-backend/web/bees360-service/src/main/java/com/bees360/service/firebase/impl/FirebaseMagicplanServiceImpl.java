package com.bees360.service.firebase.impl;

import com.bees360.common.util.UUIDUtil;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.enums.AiBotUserEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.job.registry.SerializableFirebaseMagicplan;
import com.bees360.mapper.ProjectMapper;
import com.bees360.resource.ResourcePool;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.firebase.FirebaseMagicplanService;
import com.bees360.web.event.tag.TagMagicplanMissingRemovedEvent;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/** Magicplan 业务 */
@Log4j2
@Service
public class FirebaseMagicplanServiceImpl implements FirebaseMagicplanService {

    @Autowired private ProjectMapper projectMapper;
    @Autowired private ProjectReportFileService projectReportFileService;
    @Autowired private ResourcePool resourcePool;
    @Autowired private ApplicationEventPublisher publisher;

    @Override
    public void handleMagicplan(SerializableFirebaseMagicplan magicplan) {
        // don't need to handle if projectId or pdf is null
        if (StringUtils.isAnyBlank(magicplan.getProjectId(), magicplan.getPdf())) {
            log.warn(
                    "magicplan handle has no projectId or report, magicplan id {}",
                    magicplan.getId());
            return;
        }
        var projectId = Long.parseLong(magicplan.getProjectId());
        Project project = projectMapper.getById(projectId);
        if (project == null) {
            log.warn("magicplan handle, project not found, magicplan id {}", magicplan.getId());
            return;
        }

        var fileInfo = resourcePool.head(magicplan.getPdf());
        if (fileInfo == null) {
            throw new IllegalArgumentException(
                "magicplan report file not found projectId '%s', key '%s'".formatted(
                    projectId, magicplan.getPdf()));
        }
        var report =
                projectReportFileService.getReportFile(
                        projectId, ReportTypeEnum.MAGIC_PLAN.getCode());
        if (report != null) {
            var existsReportHead = resourcePool.head(report.getReportUrl());
            if (existsReportHead != null && StringUtils.equals(existsReportHead.getETag(), fileInfo.getETag())) {
                log.info("magicplan report file not change projectId {}", projectId);
                // 存在且地址相同
                return;
            }
        }

        ProjectReportFile file =
                genProjectReportFile(
                        magicplan.getPdf(),
                        projectId,
                        ReportTypeEnum.MAGIC_PLAN,
                        fileInfo.getContentLength().intValue());
        projectReportFileService.updateReportFile(file);

        // remove operation tag
        publisher.publishEvent(new TagMagicplanMissingRemovedEvent(this, projectId));
    }

    private ProjectReportFile genProjectReportFile(
            String urlKey, long projectId, ReportTypeEnum type, Integer size) {
        ProjectReportFile file = new ProjectReportFile();
        file.setReportId(UUIDUtil.getReportUUID());
        file.setProjectId(projectId);
        file.setCreatedBy(AiBotUserEnum.AI_NEW_USER_ID.getIntegerCode());
        file.setCreatedTime(System.currentTimeMillis());
        file.setDeleted(false);
        file.setRead(false);
        file.setGenerationStatus(ReportGenerationStatusEnum.GENERATED.getCode());
        file.setReportType(type.getCode());
        file.setReportWordFileName("");
        file.setReportPdfFileName(urlKey);
        file.setReportPdfCompressed("");
        file.setSize(size);
        return file;
    }
}
