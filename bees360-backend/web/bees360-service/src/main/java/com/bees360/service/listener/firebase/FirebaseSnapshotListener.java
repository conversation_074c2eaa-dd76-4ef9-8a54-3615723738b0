package com.bees360.service.listener.firebase;

import static com.bees360.project.base.Message.ProjectType.CLAIM_TYPE;
import static com.bees360.project.base.Message.ProjectType.UNDERWRITING_TYPE;
import static com.bees360.service.listener.firebase.FirebaseMissionSnapshotListener.getAMonthAgo;


import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.activity.Message;
import com.bees360.entity.Project;
import com.bees360.entity.User;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.firebase.ActivityRecord;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.SerializableFirebaseBatch;
import com.bees360.job.registry.SerializableFirebaseData;
import com.bees360.job.registry.SerializableFirebaseHover;
import com.bees360.job.registry.SerializableFirebaseIBeesMission;
import com.bees360.job.registry.SerializableFirebaseMagicplan;
import com.bees360.job.registry.SerializableFirebaseMission;
import com.bees360.job.registry.SerializableFirebaseProject;
import com.bees360.service.ProjectService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.DateTimes;
import com.google.cloud.Timestamp;
import com.google.cloud.firestore.DocumentChange;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import com.google.gson.Gson;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import jakarta.annotation.PostConstruct;

/**
 * TODO 这个里面的逻辑要迁移到EventApp中
 *
 * <AUTHOR>
 * @since 2020/08/24 18:48
 */
@Slf4j
@Component
public class FirebaseSnapshotListener {
    @Autowired private Firestore firestore;
    @Autowired private CommentManager commentManager;
    @Autowired private ActivityManager activityManager;
    @Autowired private ProjectService projectService;
    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;
    private final Set<String> visitedSet = Sets.newConcurrentHashSet();
    @Autowired private JobScheduler jobScheduler;
    private final Duration retryDelay = Duration.ofMinutes(1);
    private static final String ACTIVITY_MAGICPLAN_DISPATCH_FAILED =
            "ACTIVITY_MAGICPLAN_DISPATCH_FAILED";
    private static final String ACTIVITY_RECORD_ADDED = "ACTIVITY_RECORD_ADDED";

    @PostConstruct
    public void changeOnPilotMission() {
        if(!bees360FeatureSwitch.isEnableChangeOnPilotMission()){
            return;
        }
        firestore
                .collection(FirebaseService.MISSION_COLLECTION)
                .whereEqualTo("isDeleted", false)
                .whereGreaterThanOrEqualTo("lastUpdateTime", Timestamp.fromProto(DateTimes.toTimestamp(getAMonthAgo())))
                .addSnapshotListener(
                        (snapshots, e) -> {
                            try {
                                if (!visitedSet.contains(FirebaseService.MISSION_COLLECTION)) {
                                    visitedSet.add(FirebaseService.MISSION_COLLECTION);
                                    log.info(
                                            "Visited '{}' on application start up.",
                                            FirebaseService.MISSION_COLLECTION);
                                    return;
                                }
                                if (snapshots == null || e != null) {
                                    Optional.ofNullable(e)
                                            .ifPresent(
                                                    (error) ->
                                                        log.warn(
                                                            "Listen failed with error: {} on collection {}, and it will be reRegister.",
                                                            error.getMessage(), FirebaseService.MISSION_COLLECTION));
                                    reRegistry(
                                            this::changeOnPilotMission,
                                            FirebaseService.MISSION_COLLECTION);
                                    return;
                                }
                                snapshots.getDocumentChanges().stream()
                                        .map(DocumentChange::getDocument)
                                        .forEach(
                                                queryDocumentSnapshot ->
                                                        handleFirebaseData(
                                                                SerializableFirebaseMission.class,
                                                                queryDocumentSnapshot));
                            } catch (Exception e1) {
                                log.error("Unknown error when handle Pilot mission", e1);
                            }
                        });
    }

    @PostConstruct
    public void changeOnIBeesMission() {
        if(!bees360FeatureSwitch.isEnableChangeOnIBeesMission()){
            return;
        }
        firestore
                .collection(FirebaseService.IBEES_MISSION_COLLECTION)
                .whereEqualTo("isDeleted", false)
                .addSnapshotListener(
                        (snapshots, e) -> {
                            if (!visitedSet.contains(FirebaseService.IBEES_MISSION_COLLECTION)) {
                                visitedSet.add(FirebaseService.IBEES_MISSION_COLLECTION);
                                log.info(
                                        "Visited '{}' on application start up.",
                                        FirebaseService.IBEES_MISSION_COLLECTION);
                                return;
                            }
                            try {
                                if (snapshots == null || e != null) {
                                    Optional.ofNullable(e)
                                            .ifPresent(
                                                    (error) ->
                                                        log.warn(
                                                            "Listen failed with error: {} on collection {}, and it will be reRegister.",
                                                            error.getMessage(), FirebaseService.IBEES_MISSION_COLLECTION));
                                    reRegistry(
                                            this::changeOnIBeesMission,
                                            FirebaseService.IBEES_MISSION_COLLECTION);
                                    return;
                                }
                                snapshots.getDocumentChanges().stream()
                                        .map(DocumentChange::getDocument)
                                        .forEach(
                                                queryDocumentSnapshot ->
                                                        handleFirebaseData(
                                                                SerializableFirebaseIBeesMission
                                                                        .class,
                                                                queryDocumentSnapshot));
                            } catch (Exception e1) {
                                log.error("Unknown error when handle IBees mission", e1);
                            }
                        });
    }

    @PostConstruct
    public void changeOnProject() {
        if(!bees360FeatureSwitch.isEnableChangeOnProject()){
            return;
        }
        firestore
                .collection(FirebaseService.PROJECT_COLLECTION)
                .whereGreaterThanOrEqualTo(
                        "update_time", getAMonthAgo().toEpochMilli())
                .addSnapshotListener(
                        (snapshots, e) -> {
                            try {
                                if (!visitedSet.contains(FirebaseService.PROJECT_COLLECTION)) {
                                    visitedSet.add(FirebaseService.PROJECT_COLLECTION);
                                    log.info(
                                            "Visited '{}' on application start up.",
                                            FirebaseService.PROJECT_COLLECTION);
                                    return;
                                }
                                if (snapshots == null || e != null) {
                                    Optional.ofNullable(e)
                                            .ifPresent(
                                                    (error) ->
                                                            log.warn(
                                                                    "Listen failed with error: {} on collection {}, and it will be reRegister.",
                                                                    error.getMessage(), FirebaseService.PROJECT_COLLECTION));
                                    reRegistry(
                                            this::changeOnProject,
                                            FirebaseService.PROJECT_COLLECTION);
                                    return;
                                }
                                snapshots.getDocumentChanges().stream()
                                        .map(DocumentChange::getDocument)
                                        .forEach(
                                                queryDocumentSnapshot ->
                                                        handleFirebaseData(
                                                                SerializableFirebaseProject.class,
                                                                queryDocumentSnapshot));
                            } catch (Exception e1) {
                                log.error("Unknown error when handle firebase project", e1);
                            }
                        });
    }

    @PostConstruct
    public void changeOnBatch() {
        if(!bees360FeatureSwitch.isEnableChangeOnBatch()){
            return;
        }
        firestore
                .collection(FirebaseService.PILOT_BATCH_COLLECTION)
                .addSnapshotListener(
                        (snapshots, e) -> {
                            try {
                                if (!visitedSet.contains(FirebaseService.PILOT_BATCH_COLLECTION)) {
                                    visitedSet.add(FirebaseService.PILOT_BATCH_COLLECTION);
                                    log.info(
                                            "Visited '{}' on application start up.",
                                            FirebaseService.PILOT_BATCH_COLLECTION);
                                    return;
                                }
                                if (snapshots == null || e != null) {
                                    Optional.ofNullable(e)
                                            .ifPresent(
                                                    (error) ->
                                                            log.warn(
                                                                    "Listen failed with error: {} on collection {}, and it will be reRegister.",
                                                                    error.getMessage(),
                                                                    FirebaseService
                                                                            .PILOT_BATCH_COLLECTION));
                                    reRegistry(
                                            this::changeOnBatch,
                                            FirebaseService.PILOT_BATCH_COLLECTION);
                                    return;
                                }
                                snapshots.getDocumentChanges().stream()
                                        .map(DocumentChange::getDocument)
                                        .forEach(
                                                queryDocumentSnapshot ->
                                                        handleFirebaseData(
                                                                SerializableFirebaseBatch.class,
                                                                queryDocumentSnapshot));
                            } catch (IllegalArgumentException illegalArgumentException) {
                                log.warn("Firebase data is invalid.", illegalArgumentException);
                            } catch (RuntimeException runtimeException) {
                                log.error(
                                        "Unknown error when handle batch mission",
                                        runtimeException);
                            }
                        });
    }

    @PostConstruct
    public void changeOnHoverJob() {
        if(!bees360FeatureSwitch.isEnableChangeOnHoverJob()){
            return;
        }
        firestore
            .collection(FirebaseService.HOVER_JOB)
            .addSnapshotListener(
                (snapshots, exception) -> {
                    try {
                        if (!visitedSet.contains(FirebaseService.HOVER_JOB)) {
                            visitedSet.add(FirebaseService.HOVER_JOB);
                            log.info(
                                "Visited '{}' on application start up.",
                                FirebaseService.HOVER_JOB);
                            return;
                        }

                        if (Objects.isNull(snapshots)) {
                            return;
                        }
                        if (Objects.nonNull(exception)) {
                            log.warn(
                                "Listen failed with error: {} on collection {}, and it will be reRegister.",
                                exception.getMessage(), FirebaseService.HOVER_JOB);
                            reRegistry(
                                this::changeOnHoverJob,
                                FirebaseService.HOVER_JOB);
                            return;
                        }
                        snapshots.getDocumentChanges().stream()
                            .filter(
                                s ->
                                    Objects.equals(
                                        s.getType(),
                                        DocumentChange.Type.MODIFIED))
                            .map(DocumentChange::getDocument)
                            .forEach(
                                queryDocumentSnapshot ->
                                    handleFirebaseData(
                                        SerializableFirebaseHover.class,
                                        queryDocumentSnapshot));
                    } catch (Exception e) {
                        log.error("Unknown error when handle hover job", e);
                    }
                });
    }

    @PostConstruct
    public void changeOnMagicPlan() {
        if(!bees360FeatureSwitch.isEnableChangeOnMagicPlan()){
            return;
        }
        firestore
                .collection(FirebaseService.MAGICPLAN)
                .addSnapshotListener(
                        (snapshots, exception) -> {
                            try {
                                if (!visitedSet.contains(FirebaseService.MAGICPLAN)) {
                                    visitedSet.add(FirebaseService.MAGICPLAN);
                                    log.info(
                                            "Visited '{}' on application start up.",
                                            FirebaseService.MAGICPLAN);
                                    return;
                                }

                                if (Objects.isNull(snapshots)) {
                                    return;
                                }
                                if (Objects.nonNull(exception)) {
                                    log.warn(
                                            "Listen failed with error: {} on collection {}, and it will be reRegister.",
                                            exception.getMessage(),
                                            FirebaseService.MAGICPLAN);
                                    reRegistry(
                                            this::changeOnMagicPlan, FirebaseService.MAGICPLAN);
                                    return;
                                }
                                snapshots.getDocumentChanges().stream()
                                        .filter(
                                                s ->
                                                        Objects.equals(
                                                                s.getType(),
                                                                DocumentChange.Type.MODIFIED))
                                        .map(DocumentChange::getDocument)
                                        .forEach(
                                                queryDocumentSnapshot ->
                                                        handleFirebaseData(
                                                                SerializableFirebaseMagicplan.class,
                                                                queryDocumentSnapshot));
                            } catch (Exception e) {
                                log.error("Unknown error when handle MAGICPLAN", e);
                            }
                        });
    }

    /** TODO 这个监听要去掉，统一用 {@linkplain #activityRecordAdded()}来同步beespilot.io的activity */
    @PostConstruct
    public void activityMagicplanDispatchFailed() {
        if(!bees360FeatureSwitch.isEnableActivityMagicplanDispatchFailed()){
            return;
        }
        firestore
                .collection(FirebaseService.ACTIVITY_RECORD)
                // 目前只记录MagicplanDispatchFailed的activity
                .whereEqualTo("activity_name", "MagicplanDispatchFailed")
                .addSnapshotListener(
                        (snapshots, exception) -> {
                            try {
                                if (!visitedSet.contains(ACTIVITY_MAGICPLAN_DISPATCH_FAILED)) {
                                    visitedSet.add(ACTIVITY_MAGICPLAN_DISPATCH_FAILED);
                                    log.info(
                                            "Visited '{}' on application start up.",
                                            ACTIVITY_MAGICPLAN_DISPATCH_FAILED);

                                    return;
                                }

                                if (Objects.isNull(snapshots)) {
                                    return;
                                }
                                if (Objects.nonNull(exception)) {
                                    log.warn(
                                            "Listen failed with error: {} on collection {} with {}, and it will be reRegister.",
                                            exception.getMessage(),
                                            FirebaseService.ACTIVITY_RECORD,
                                            "activity_name=MagicplanDispatchFailed");
                                    reRegistry(
                                            this::activityMagicplanDispatchFailed,
                                            FirebaseService.ACTIVITY_RECORD);
                                    return;
                                }

                                snapshots.getDocumentChanges().stream()
                                        .filter(
                                                s ->
                                                        Objects.equals(
                                                                s.getType(),
                                                                DocumentChange.Type.ADDED))
                                        .map(DocumentChange::getDocument)
                                        // 为了防止服务重启后读取全部数据导致数据异常,将读取数据的时间最长比数据创建时间晚五分钟
                                        .filter(this::isValidDoc)
                                        .map(this::toActivity)
                                        .forEach(activityManager::submitActivity);
                            } catch (Exception e) {
                                log.error("Unknown error when handle hover job", e);
                            }
                        });
    }

    /**
     * 将firebase的activity record的activity同步到web的activity中 参考 <code>
     * https://gitlab.bees360.com/engineers/beespilot/-/issues/401</code>
     */
    @PostConstruct
    public void activityRecordAdded() {
        if(!bees360FeatureSwitch.isEnableActivityRecordAdded()){
            return;
        }
        firestore
                .collection(FirebaseService.ACTIVITY_RECORD)
                .whereEqualTo("activity_name", ActivityRecord.webActivity)
                .whereGreaterThanOrEqualTo("create_time", Timestamp.fromProto(DateTimes.toTimestamp(getAMonthAgo())))
                .addSnapshotListener(
                        (snapshots, exception) -> {
                            try {
                                if (!visitedSet.contains(ACTIVITY_RECORD_ADDED)) {
                                    visitedSet.add(ACTIVITY_RECORD_ADDED);
                                    log.info(
                                            "Visited '{}' on application start up.",
                                            ACTIVITY_RECORD_ADDED);
                                    return;
                                }

                                if (Objects.isNull(snapshots)) {
                                    return;
                                }
                                if (Objects.nonNull(exception)) {
                                    log.warn(
                                        "Listen failed with error: {} on collection {} with {}, and it will be reRegister.",
                                        exception.getMessage(), FirebaseService.ACTIVITY_RECORD, "activity_name=WebActivity");
                                    reRegistry(
                                        this::activityRecordAdded,
                                        FirebaseService.ACTIVITY_RECORD);
                                }

                                snapshots.getDocumentChanges().stream()
                                        .filter(
                                                s ->
                                                        Objects.equals(
                                                                s.getType(),
                                                                DocumentChange.Type.ADDED))
                                        .map(DocumentChange::getDocument)
                                        .filter(this::isValidDoc)
                                        .map(doc -> doc.toObject(ActivityRecord.class))
                                        .map(ActivityRecord::getContent)
                                        .map(this::fillProjectType)
                                        .forEach(activityManager::submitActivity);
                            } catch (Exception e) {
                                log.error("Unknown error when handle hover job", e);
                            }
                        });
    }

    private Activity fillProjectType(Activity activity) {
        long projectId = activity.getProjectId();
        Message.ActivityMessage.Builder builder = activity.toMessage().toBuilder();
        Optional<Project> project = projectService.findById(projectId);
        project.ifPresent(
                p ->
                        builder.setProjectType(
                                ProjectServiceTypeEnum.isClaim(p.getServiceType())
                                        ? CLAIM_TYPE
                                        : UNDERWRITING_TYPE));
        return Activity.from(builder.build());
    }

    // 为了防止服务重启后读取全部数据导致数据异常,将读取数据的时间最长比数据创建时间晚五分钟
    @SuppressWarnings("ConstantConditions")
    private boolean isValidDoc(DocumentSnapshot doc) {
        return doc.exists()
                && (doc.getReadTime().getSeconds() - doc.getCreateTime().getSeconds())
                        < Duration.ofMinutes(5).toSeconds();
    }

    private Activity toActivity(DocumentSnapshot doc) {
        Timestamp createdAt = (Timestamp) doc.get("create_time");
        String projectId = (String) doc.get("content.project_id");
        Preconditions.checkArgument(projectId != null, "Project Id should not be null");

        return Activity.of(
                Message.ActivityMessage.newBuilder()
                        .setSource(ActivitySourceEnum.BEESPILOT.getValue())
                        .setEntity(
                                Message.ActivityMessage.Entity.newBuilder()
                                        .setType(
                                                Message.ActivityMessage.EntityType.MAGICPLAN.name())
                                        .build())
                        .setProjectId(Long.parseLong(projectId))
                        .setCreatedAt(
                                Optional.ofNullable(createdAt)
                                        .map(Timestamp::toProto)
                                        .orElseGet(() -> Timestamp.now().toProto()))
                        .setCreatedBy(
                                com.bees360.user.Message.UserMessage.newBuilder()
                                        .setId(User.BEES_PILOT_SYSTEM + "")
                                        .build())
                        .build());
    }

    private <T extends SerializableFirebaseData> void handleFirebaseData(
            Class<T> tClass, QueryDocumentSnapshot snapshot) {
        String id = snapshot.getId();
        T data = fetch(tClass, snapshot);
        if (data instanceof SerializableFirebaseMission firebaseMission){
            if (firebaseMission.needIgnore()) {
                log.info("Ignored mission '{}'.", firebaseMission);
                return;
            }
        }
        log.info("Start to handle firebase data '{}'", id);
        schedule(data, id);
    }

    @SuppressWarnings("unchecked")
    public static <T extends SerializableFirebaseData> T fetch(
            Class<T> tClass, QueryDocumentSnapshot snapshot) {
        String id = snapshot.getId();
        T data;
        try {
            data = snapshot.toObject(tClass);
            if (data instanceof SerializableFirebaseBatch batch) {
                // fix Gson ignoring map entries with value=null, to fix it in solid project
                if (batch.getProject() != null) {
                    Map<String, Object> projectMap = new HashMap<>();
                    batch.getProject()
                            .forEach(
                                    (key, value) ->
                                            projectMap.put(
                                                    key, Optional.ofNullable(value).orElse(key)));
                    batch.setProject(projectMap);
                    data = (T) batch;
                }
            }
            Object pilot = snapshot.get("pilot");
            if (Objects.nonNull(pilot) && pilot instanceof DocumentReference pilotDocument) {
                data.setPilotId(pilotDocument.getId());
            } else if (pilot instanceof String) {
                data.setPilotId(pilot.toString());
            }

            Object om = snapshot.get("operations_manager");
            if (om instanceof DocumentReference omDocument) {
                data.setOperationsManagerId(omDocument.getId());
            } else if (om instanceof String) {
                data.setOperationsManagerId(om.toString());
            }

            data.setId(id);
            return data;
        } catch (RuntimeException e) {
            log.error("'{}' data is invalid.", id, e);
            throw new IllegalArgumentException(e);
        }
    }

    private <T extends SerializableFirebaseData> void schedule(T data, String id) {
        try {
            data.setId(id);
            var job = JobPayloads.encode(String.valueOf(data.hashCode()), data);
            int retryCount = 3;
            float retryDelayIncreaseFactor = 1.5F;
            job = RetryableJob.of(job, retryCount, retryDelay, retryDelayIncreaseFactor);

            jobScheduler.schedule(job);
        } catch (RuntimeException e) {
            log.error("Failed to schedule job '{}' '{}", data.getId(), data, e);
        }
    }

    static void reRegistry(Runnable listener, String collection) {
        log.info("Starting to re-register firebase listener on '{}'.", collection);
        listener.run();
    }
}
