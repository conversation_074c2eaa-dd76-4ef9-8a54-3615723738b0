package com.bees360.service.ai;

import com.bees360.internal.ai.grpc.ProjectServiceGrpc;
import com.bees360.internal.ai.grpc.api.web2ai.Project.ProjectEsModel;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectIdOuterClass.ProjectId;
import com.bees360.service.grpc.AiGrpcClient;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 因为ai grpc client太多了，这里使用一个分装类集成所有的ai grpc client
 *
 * <AUTHOR>
 */
@Service
public class AiProjectService {

    @Autowired
    private List<AiGrpcClient> aiGrpcClients;

    public ProjectEsModel findByProjectId(long projectId) {
        for (AiGrpcClient aiGrpcClient : aiGrpcClients) {
            ProjectServiceGrpc.ProjectServiceFutureStub stub = ProjectServiceGrpc.newFutureStub(aiGrpcClient.getChannel());
            ProjectEsModel esModel = findProject(projectId, stub);
            if (esModel != null) {
                return esModel;
            }
        }
        return null;
    }

    private ProjectEsModel findProject(long projectId, ProjectServiceGrpc.ProjectServiceFutureStub stub) {
        ProjectId idWrap = ProjectId.newBuilder().setProjectId(projectId).build();
        Retryer<ProjectEsModel> retryer = RetryerBuilder.<ProjectEsModel>newBuilder()
            .retryIfException()
            .withWaitStrategy(WaitStrategies.fixedWait(1, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterAttempt(3))
            .build();
        try {
            return retryer.call(() -> stub.findById(idWrap).get());
        } catch (ExecutionException | RetryException e) {
            throw new IllegalStateException(e);
        }
    }
}
