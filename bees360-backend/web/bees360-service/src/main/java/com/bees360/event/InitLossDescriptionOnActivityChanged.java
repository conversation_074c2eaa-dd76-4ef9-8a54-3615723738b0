package com.bees360.event;

import com.bees360.event.registry.ActivityChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.firebase.FirebaseProjectService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听活动变更事件并初始化项目损失描述
 */
@Log4j2
public class InitLossDescriptionOnActivityChanged extends AbstractNamedEventListener<ActivityChangedEvent> {

    private final FirebaseProjectService firebaseProjectService;

    private final ProjectMapper projectMapper;

    public InitLossDescriptionOnActivityChanged(FirebaseProjectService firebaseProjectService, ProjectMapper projectMapper) {
        this.firebaseProjectService = firebaseProjectService;
        this.projectMapper = projectMapper;
        log.info("created {}, firebaseProjectService {}, projectMapper {}", this, firebaseProjectService, projectMapper);
    }

    @Override
    public void handle(ActivityChangedEvent activityChangedEvent) throws IOException {
        var activity = activityChangedEvent.getSource();
        var comment = activity.getComment();
        if (comment == null) {
            return;
        }

        var projectId = comment.getProjectId();
        var project = projectMapper.getById(projectId);


        // 这里可能project还没被创建出来
        if (project == null) {
            return;
        }

        projectId = activityChangedEvent.getSource().getProjectId();
        log.info("Start to init lossDescription for project '{}' on comment {} added.", projectId, comment);
        firebaseProjectService.initLossDescription(project, comment);
    }
}
