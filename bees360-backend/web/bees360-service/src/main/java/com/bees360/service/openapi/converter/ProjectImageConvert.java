package com.bees360.service.openapi.converter;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import com.bees360.entity.enums.OrientationEnum;
import com.bees360.entity.openapi.OpenImageVo;

/**
 * <AUTHOR>
 */
public class ProjectImageConvert {

    public static OpenImageVo toOpenImageVo(ProjectImage image) {
        final OpenImageVo imageVo = new OpenImageVo();
        imageVo.setId(String.valueOf(image.getImageId()));
        final OrientationEnum direction = OrientationEnum.getEnum(image.getOrientation());
        imageVo.setDirection(direction == null ? null : direction.getDisplay());
        final FileSourceTypeEnum fileSourceType = FileSourceTypeEnum.getEnum(image.getFileSourceType());
        imageVo.setSource(fileSourceType == null ? "" : fileSourceType.getValue());
        final ImageTypeEnum imageType = ImageTypeEnum.getEnum(image.getImageType());
        imageVo.setType(imageType == null ? "" : imageType.getDisplay());

        return imageVo;
    }
}
