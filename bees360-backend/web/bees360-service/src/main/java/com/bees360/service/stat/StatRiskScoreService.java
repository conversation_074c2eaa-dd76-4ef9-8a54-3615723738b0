package com.bees360.service.stat;

import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.stat.converter.StatCardConverter;
import com.bees360.entity.stat.converter.StatListConverter;
import com.bees360.entity.stat.dto.ProjectCardDataDto;
import com.bees360.entity.stat.dto.ProjectListDataDto;
import com.bees360.entity.stat.dto.ProjectStatChartDto;
import com.bees360.entity.stat.enums.ProjectStatType;
import com.bees360.entity.stat.search.StatFullSearchOption;
import com.bees360.entity.stat.vo.StatComplexVo;
import com.bees360.entity.stat.vo.StatProjectCardVo;
import com.bees360.entity.stat.vo.StatProjectVo;
import com.bees360.entity.stat.vo.card.StatCardVo;
import com.bees360.entity.stat.vo.chart.StatChartVo;
import com.bees360.entity.stat.vo.chart.StatMapVo;
import com.bees360.entity.stat.vo.list.PagedResultVo;
import com.bees360.entity.stat.vo.list.ProjectListVo;
import com.bees360.entity.vo.Pagination;
import com.bees360.mapper.stat.ProjectStatMapper;
import com.bees360.project.ProjectDaysOldProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.bees360.service.stat.StatDaysOldUtil.NEW_DAYS_OLD;
import static com.bees360.service.stat.StatDaysOldUtil.preProcessDaysOldOption;
import static com.bees360.service.stat.StatDaysOldUtil.setProjectNewDaysOld;

@Service
public class StatRiskScoreService implements StatComplexService {

    private static final Logger logger = LoggerFactory.getLogger(StatRiskScoreService.class);

    @Autowired
    private ProjectStatMapper projectStatMapper;

    @Autowired private ProjectDaysOldProvider daysOldProvider;

    @Override
    public StatCardVo getCardStat(StatFullSearchOption searchOption){

        addProjectStatusOption(searchOption);

        final List<ProjectCardDataDto> riskScoreCardStatData = projectStatMapper.getRiskScoreCardStatData(searchOption);

        final StatCardVo cardVo = StatCardConverter.toRiskScoreCard(riskScoreCardStatData);

        cardVo.addDefaultServiceType(searchOption.getServiceTypes());

        return cardVo;
    }

    @Override
    public StatChartVo getChartStat(StatFullSearchOption searchOption){

        addProjectStatusOption(searchOption);
        final List<ProjectStatChartDto> chartDtos = projectStatMapper.riskScoreChartStat(searchOption);

        return StatChartVo.buildVo(chartDtos);
    }

    @Override
    public List<StatMapVo> getMapData(StatFullSearchOption fullSearchOption){

        addProjectStatusOption(fullSearchOption);
        return StatMapVo.buildMapVo(projectStatMapper.riskScoreChartStat(fullSearchOption));
    }

    @Override
    public PagedResultVo getProjectList(StatFullSearchOption searchOption){

        addProjectStatusOption(searchOption);
        return getPageResult(searchOption);
    }

    @Override
    public ProjectStatType getType() {
        return ProjectStatType.RISK_SCORE;
    }

    @Override
    public StatComplexVo getStatComplexInfo(StatFullSearchOption fullSearchOption) {
        var complexVo = new StatComplexVo();
        // add stat project list
        addProjectStatusOption(fullSearchOption);
        var pageResult = getPageResult(fullSearchOption);
        complexVo.setList(pageResult);
        fullSearchOption.setServiceTypes(fullSearchOption.getServiceTypesForChartAndCard());

        // add incomplete stat card vo
        var statProjectCard = new StatProjectCardVo();
        var cardVo =
                StatCardConverter.toRiskScoreCard(
                        projectStatMapper.getRiskScoreCardStatData(fullSearchOption));
        cardVo.addDefaultServiceType(fullSearchOption.getServiceTypes());
        complexVo.setCard(statProjectCard);
        // add stat project chart
        var statProjectChart = new StatProjectVo();
        var statType = ProjectStatType.getInstance(fullSearchOption.getType());
        if (statType == null) {
            complexVo.setChart(statProjectChart);
            return complexVo;
        }
        var chartStat = projectStatMapper.riskScoreChartStat(fullSearchOption);
        var chartVo = StatChartVo.buildVo(chartStat);
        chartVo.addDefaultIfAbsentState(fullSearchOption.getStates());
        chartVo.addDefaultIfAbsentServiceType(fullSearchOption.getServiceTypes());
        statProjectChart.setChart(chartVo);
        var mapData = StatMapVo.buildMapVo(projectStatMapper.riskScoreChartStat(fullSearchOption));
        statProjectChart.setMap(mapData);
        statProjectChart.setCard(cardVo);
        complexVo.setChart(statProjectChart);
        return complexVo;
    }

    private PagedResultVo getPageResult(StatFullSearchOption searchOption) {
                var pageIndex = searchOption.getPageIndex();
        var pageSize = searchOption.getPageSize();
        var sortOrder = searchOption.getSortOrder();
        var sortKey = searchOption.getSortKey();

        Map<String, Integer> daysOldMap = null;
        var projectIds =
                projectStatMapper.searchRiskScoreId(searchOption).stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());
        if (Objects.equals(sortKey, NEW_DAYS_OLD)) {
            daysOldMap = daysOldProvider.findProjectDaysOld(projectIds);
            searchOption = preProcessDaysOldOption(daysOldMap, sortOrder, pageIndex, pageSize);
        }

        List<ProjectListDataDto> dtos = projectStatMapper.searchRiskScoreList(searchOption);

        if (daysOldMap == null) {
            var ids = dtos.stream().map(p -> String.valueOf(p.getProjectId())).collect(Collectors.toList());
            daysOldMap = daysOldProvider.findProjectDaysOld(ids);

        }

        dtos = setProjectNewDaysOld(dtos, daysOldMap, sortOrder, sortKey);

        final List<ProjectListVo> vos = dtos.stream().map(StatListConverter::toRiskScoreVo).collect(Collectors.toList());

        Pagination pagination = new Pagination(searchOption.getPageIndex(), searchOption.getPageSize(), projectIds.size());

        PagedResultVo result = new PagedResultVo();
        result.setResult(vos);
        result.setPage(pagination);

        return result;
    }

    private void addProjectStatusOption(StatFullSearchOption searchOption){
        searchOption.addIncludeProjectStatus(NewProjectStatusEnum.RETURNED_TO_CLIENT.getCode());
        searchOption.addIncludeProjectStatus(NewProjectStatusEnum.CLIENT_RECEIVED.getCode());
    }
}
