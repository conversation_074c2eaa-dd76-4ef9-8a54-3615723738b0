package com.bees360.event;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.entity.User;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.event.registry.ResourceDeleted;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import java.io.IOException;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

/**
 * 处理资源删除事件，更新项目状态为客户端已接收。
 */
@Log4j2
public class ClientReceivedOnResourceDeleted extends AbstractNamedEventListener<ResourceDeleted> {

    private static final String EVENT_NAME_SEPARATOR = ".";

    private final String namespace;
    private final ProjectStatusService projectStatusService;
    private final ProjectService projectService;

    public ClientReceivedOnResourceDeleted(
            @Nullable String namespace,
            @NonNull ProjectService projectService,
            @NonNull ProjectStatusService projectStatusService) {
        this.namespace = namespace;
        this.projectService = projectService;
        this.projectStatusService = projectStatusService;

        log.info("Created '{}(namespace={}, projectStatusService={})'", this, namespace, projectStatusService);
    }

    @Override
    public void handle(ResourceDeleted resourceDeleted) throws IOException {
        var projectId = parseProjectId(resourceDeleted.getKey());
        var message = "Change status since the resource %s has been received.".formatted(resourceDeleted.getKey());
        if (isProjectNeedToBeReceived(projectId)) {
            projectStatusService.changeOnClientReceived(User.AI_ID, projectId, message);
        }
    }

    private boolean isProjectNeedToBeReceived(long projectId) {
        try {
            var project = projectService.getById(projectId);
            return project.getProjectStatus() != NewProjectStatusEnum.CLIENT_RECEIVED.getCode();
        } catch (ResourceNotFoundException ex) {
            return false;
        }
    }

    private Long parseProjectId(String key) {
        var start = StringUtils.lastIndexOf(key, "_") + 1;
        var end = StringUtils.lastIndexOf(key, ".");
        var projectId = StringUtils.substring(key, start, end);
        return Long.valueOf(projectId);
    }

    @Override
    public String getEventName() {
        return namespace == null? super.getEventName(): namespace + EVENT_NAME_SEPARATOR + super.getEventName();
    }
}
