package com.bees360.event;

import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.firebase.FirebaseProjectService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听项目状态变更事件并同步更新Firebase项目状态
 */
@Log4j2
public class ChangeFirebaseProjectStateOnStateChanged extends AbstractNamedEventListener<ProjectStateChangedEvent> {
    public ChangeFirebaseProjectStateOnStateChanged(FirebaseProjectService firebaseProjectService) {
        this.firebaseProjectService = firebaseProjectService;
        log.info("Created '({}, firebaseProjectService={})'", this, this.firebaseProjectService);
    }

    private final FirebaseProjectService firebaseProjectService;
    @Override
    public void handle(ProjectStateChangedEvent event) throws IOException {
        firebaseProjectService.syncProjectStateToFirebase(event.getProjectId(), event.getCurrentState());
    }
}
