package com.bees360.job;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.SchedulePilotDto;
import com.bees360.entity.User;
import com.bees360.job.registry.BatchSchedulePilotJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.UserService;
import com.bees360.service.beespilot.BeesPilotBatchService;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/** Assignment pilot or remove pilot. */
@Log4j2
@ToString
@RequiredArgsConstructor
public class BatchSchedulePilotJobExecutor extends AbstractJobExecutor<BatchSchedulePilotJob> {
    private final BeesPilotBatchService beesPilotBatchService;
    private final UserService userService;

    @Override
    protected void handle(BatchSchedulePilotJob job) throws IOException {
        var opUserId = job.getOperationUserId();
        Long webOpUserId = Optional.ofNullable(opUserId).map(userService::toWebUserId).orElse(null);
        var pilotId = job.getPilotId();
        var projectIds =
                job.getProjectIds().stream().map(Long::parseLong).collect(Collectors.toList());
        var pilotAccept = job.getPilotAccepted();
        var inspectionTime = job.getInspectionTime();
        var version = job.getVersion();
        if (webOpUserId == null) {
            log.warn(
                    "Except opUserId but get null when assign pilot '{}' to project '{}'"
                            + "using opUserId='{}' instead.",
                    pilotId,
                    projectIds,
                    User.BEES_PILOT_SYSTEM);
            webOpUserId = User.BEES_PILOT_SYSTEM;
        }
        if (job.isAssignment()) {
            doAssignPilot(webOpUserId, version, projectIds, pilotAccept, pilotId, inspectionTime);
        } else {
            doRemovePilot(webOpUserId, version, projectIds);
        }
    }

    private void doAssignPilot(
            Long webOpUserId,
            Long version,
            List<Long> projectIds,
            Boolean pilotAccept,
            String pilotId,
            Long inspectionTime) {

        if (pilotAccept == null) {
            log.warn(
                    "Except isPilotAccepted but get null when assign pilot '{}' to project '{}', "
                            + "using isPilotAccepted=true instead.",
                    pilotId,
                    projectIds);
            pilotAccept = true;
        }

        var request = new SchedulePilotDto();
        request.setProjectIds(projectIds);
        request.setDueDate(inspectionTime);
        request.setIsPendingAcceptance(!pilotAccept);
        request.setVersion(version);

        try {
            log.info(
                    "Start to assign pilot '{}' to projects '{}' at version '{}'.",
                    pilotId,
                    projectIds,
                    version);
            beesPilotBatchService.createPilotBatchTask(webOpUserId, pilotId, request);
        } catch (ServiceException e) {
            log.error(
                    "Failed to assign pilot '{}' to project '{}' at version '{}'.",
                    pilotId,
                    projectIds,
                    version,
                    e);
        }
    }

    private void doRemovePilot(Long webOpUserId, Long version, List<Long> projectIds) {
        if (webOpUserId == null) {
            log.warn(
                    "Except opUserId but get null when remove pilot from project '{}'"
                            + "using opUserId='{}' instead.",
                    projectIds,
                    User.BEES_PILOT_SYSTEM);
            webOpUserId = User.BEES_PILOT_SYSTEM;
        }
        log.info("Start to remove pilot from projects '{}' at version '{}'.", projectIds, version);
        beesPilotBatchService.cancelAssignedAndRemovePilot(webOpUserId, projectIds, version);
    }
}
