package com.bees360.service.beespilot;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.BeesBatchQuery;
import com.bees360.entity.BeesPilotBatch;
import com.bees360.entity.SchedulePilotDto;
import com.bees360.entity.User;
import com.bees360.entity.enums.BatchStatusResult;
import com.bees360.entity.enums.PilotBatchEventTypeEnum;

import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.util.List;

/**
 *
 * 飞手批量账单管理
 **/
public interface BeesPilotBatchService {
    /**
     * 直接创建batch数据，不更改项目状态
     * @param beesPilotBatch batch信息
     * @param projectIds 项目集合
     */
    void updateOrAddBatchDirectly(BeesPilotBatch beesPilotBatch, List<Long> projectIds);

    void createPilotBatchTask(long creatorId, String pilotId, SchedulePilotDto batchRequest) throws ServiceException;

    void cancelAssignedAndRemovePilot(long operator, long projectId) throws ServiceException;

    void cancelAssignedAndRemovePilot(long operator, List<Long> projectId, @Nullable Long version);

    BatchStatusResult getBatchStatusResource(BeesBatchQuery query) throws ServiceException;

    void notifyPilotAfterBatchCreated(long pilotId, BeesPilotBatch batch, PilotBatchEventTypeEnum batchEventTypeEnum) throws ServiceException;

    void dailySendPilotTaskEmail();

    void deleteByBatchNo(String batchNo) throws ServiceException;

    BeesPilotBatch getByBatchNoWithoutDeletedCheck(String batchNo) throws ServiceException;

    void updatePilotBatchDeletedStatus(String batchNo, Boolean isDeleted) ;


    void weekSendPilotTaskNeedPayedEmailToAdmin(LocalDate curDate, LocalDate startDate, LocalDate endDate);
}
