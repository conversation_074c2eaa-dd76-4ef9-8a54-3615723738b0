package com.bees360.job;

import com.bees360.job.registry.SendEmailJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.util.CollectionAssitant;
import com.bees360.util.msgutil.DelegateEmailSender;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Preconditions;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Log4j2
public class SendEmailJobExecutor extends AbstractJobExecutor<SendEmailJob> {

    private final Map<String, DelegateEmailSender> senderMap;

    private final ObjectMapper JSON = new ObjectMapper();

    public SendEmailJobExecutor(@NonNull Map<String, DelegateEmailSender> senderMap) {
        for (var entry: senderMap.entrySet()) {
            if (entry.getValue() == null) {
                String message = "The SpringEmailSender of {} must not be null.".formatted(entry.getKey());
                throw new IllegalArgumentException(message);
            }
        }
        this.senderMap = Collections.unmodifiableMap(senderMap);

        log.info("{} created: {}", this.getClass(), this.senderMap.keySet());
    }

    @Override
    protected void handle(SendEmailJob job) throws IOException {
        if (CollectionAssitant.isEmpty(job.getRecipients())) {
            log.info("Abort to send email {} since the recipients is empty.", job.getTemplate());
            return;
        }
        Preconditions.checkArgument(senderMap.containsKey(job.getSender()), "There is not a sender named %s.", job.getSender());

        TypeReference<Map<String, Object>> typeRef = new TypeReference<>() {};
        Map<String, Object> templateModel = job.getTemplateData() == null? new HashMap<>():
            JSON.readValue(job.getTemplateData().toString(), typeRef);
        Object[] subjectPara = Optional.ofNullable(job.getSubjectData()).map(list -> list.toArray()).orElse(new String[0]);

        senderMap.get(job.getSender()).oneShotSend(job.getRecipients(), job.getTemplate(), templateModel, subjectPara);
    }
}
