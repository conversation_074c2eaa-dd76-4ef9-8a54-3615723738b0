package com.bees360.service.retry;

import java.util.Collections;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.interceptor.RetryInterceptorBuilder;
import org.springframework.retry.interceptor.RetryOperationsInterceptor;

/**
 * retry自定义拦截器配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableRetry
public class RetryConfig {

    /**
     * 批量生成project的retry拦截器
     */
    @Bean
    public RetryOperationsInterceptor retryCreateBeeProjectInterceptor() {
        return RetryInterceptorBuilder.stateless()
            .retryPolicy(new CreateBeeProjectRetryPolicy(2, Collections.singletonMap(Exception.class, true)))
            .build();
    }

}
