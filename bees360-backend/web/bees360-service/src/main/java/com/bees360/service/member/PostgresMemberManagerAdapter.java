package com.bees360.service.member;

import com.bees360.project.member.MemberManager;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Log4j2
@Service
public class PostgresMemberManagerAdapter {
    @Autowired private MemberManager memberManager;

    public void setMember(long projectId, String userId, int roleId, long opUserId) {
        var roleEnum = com.bees360.project.member.RoleEnum.valueOf(roleId);
        Optional.ofNullable(roleEnum)
                .ifPresent(
                        role ->
                                memberManager.setMember(
                                        String.valueOf(projectId),
                                        userId,
                                        role,
                                        String.valueOf(opUserId)));
    }

    public void setMember(long projectId, long webUserId, int roleId, long opUserId) {
        setMember(projectId, String.valueOf(webUserId), roleId, opUserId);
    }

    public void remove(long projectId, int roleId, long opUserId) {
        var roleEnum = com.bees360.project.member.RoleEnum.valueOf(roleId);
        Optional.ofNullable(roleEnum)
                .ifPresent(
                        role ->
                                memberManager.removeMember(
                                        String.valueOf(projectId),
                                        role,
                                        String.valueOf(opUserId)));
    }
}
