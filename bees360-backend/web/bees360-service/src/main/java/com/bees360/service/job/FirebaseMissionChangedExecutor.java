package com.bees360.service.job;

import com.bees360.api.UnavailableException;
import com.bees360.atomic.LockProvider;
import com.bees360.job.registry.SerializableFirebaseMission;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseMissionService;
import com.google.cloud.firestore.Firestore;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.dao.DeadlockLoserDataAccessException;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UncheckedIOException;

@Log4j2
@Component
@ToString
public class FirebaseMissionChangedExecutor
        extends AbstractJobExecutor<SerializableFirebaseMission> {
    private final FirebaseMissionService firebaseMissionService;
    private final Firestore firestore;
    private final LockProvider missionLockProvider;

    public FirebaseMissionChangedExecutor(
            FirebaseMissionService firebaseMissionService,
            Firestore firestore,
            LockProvider missionLockProvider) {
        this.firebaseMissionService = firebaseMissionService;
        this.firestore = firestore;
        this.missionLockProvider = missionLockProvider;

        log.info(
                "Created '{}(firebaseMissionService={},firestore={},missionLockProvider={}'",
                this,
                this.firebaseMissionService,
                this.firestore,
                this.missionLockProvider);
    }

    @Override
    protected void handle(SerializableFirebaseMission mission) throws IOException {
        var id = mission.getId();
        try (var ignored = missionLockProvider.lock(id)) {
            log.info("Start to handle mission '{}' '{}'", id, mission);
            firebaseMissionService.handlePilotMission(mission, id);
            log.info("Successfully handle mission '{}'", id);
        } catch (DeadlockLoserDataAccessException e) {
            throw new UnavailableException(e.getMessage(), e);
        } catch (RuntimeException e) {
            log.warn("Failed to handle mission '{}' completed event", mission, e);
            translateExceptionAndThrow(e);
        }
    }

    public static void translateExceptionAndThrow(RuntimeException e) throws IOException {
        if (e instanceof MyBatisSystemException) {
            Throwable cause = e.getCause();
            if (cause instanceof DataAccessResourceFailureException) {
                throw new IOException(e);
            }
        }
        if (e instanceof IllegalStateException) {
            throw new IOException(e);
        }
        if (e instanceof UncheckedIOException exception) {
            throw exception.getCause();
        }

        throw e;
    }
}
