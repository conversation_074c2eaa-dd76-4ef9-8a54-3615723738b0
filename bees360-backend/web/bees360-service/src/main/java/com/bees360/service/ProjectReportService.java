package com.bees360.service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.dto.ReportPrefixDto;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @contributors
 * @date 2017/12/19 11:49:53
 */
public interface ProjectReportService {
	/**
	 * update report key
	 * @param projectId
	 * @param reportKey
	 */
	int updateReportKey(long projectId, String reportId, String reportKey);

	void shareReport(long projectId, String reportId, long senderId, List<String> emails) throws ServiceException;

	List<ProjectReportFile> listPaidReport(long projectId) throws ServiceException;

	List<ProjectReportFile> listUnpaidReports(long projectId) throws ServiceException;

	ProjectReportFile insertReport(long projectId, long userId, ProjectReportFile reportFile) throws ServiceException;

    ReportPrefixDto getReportFilePrefix();
}
