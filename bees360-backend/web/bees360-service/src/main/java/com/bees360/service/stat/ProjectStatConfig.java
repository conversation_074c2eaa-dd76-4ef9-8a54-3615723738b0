package com.bees360.service.stat;

import com.bees360.entity.stat.enums.ProjectStatType;
import com.bees360.entity.stat.vo.StatProjectCardVo;
import com.bees360.entity.stat.vo.card.StatCardVo;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

@Configuration
public class ProjectStatConfig {
    @Bean
    public Map<ProjectStatType, BiConsumer<StatProjectCardVo, StatCardVo>>
            updateProjectCardByType() {
        Map<ProjectStatType, BiConsumer<StatProjectCardVo, StatCardVo>> updateProjectCardByType =
                new HashMap<>();
        updateProjectCardByType.put(ProjectStatType.PROJECT_CREATED, StatProjectCardVo::setCreated);
        updateProjectCardByType.put(
                ProjectStatType.PROJECT_COMPLETED, StatProjectCardVo::setCompleted);
        updateProjectCardByType.put(
                ProjectStatType.PROJECT_INCOMPLETED, StatProjectCardVo::setIncompleted);
        updateProjectCardByType.put(
                ProjectStatType.PROJECT_CLOSE_OUT, StatProjectCardVo::setCloseout);
        updateProjectCardByType.put(ProjectStatType.RISK_SCORE, StatProjectCardVo::setRiskScore);
        return updateProjectCardByType;
    }
}
