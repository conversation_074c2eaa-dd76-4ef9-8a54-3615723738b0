package com.bees360.job;

import com.bees360.job.registry.SyncSurveyCompletedToFirebaseJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseProjectService;
import com.bees360.util.Iterables;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

@Log4j2
public class SyncSurveyCompletedToFirebaseJobExecutor extends AbstractJobExecutor<SyncSurveyCompletedToFirebaseJob> {

    private final FirebaseProjectService firebaseProjectService;

    public SyncSurveyCompletedToFirebaseJobExecutor(FirebaseProjectService firebaseProjectService) {
        this.firebaseProjectService = firebaseProjectService;
        log.info("Created {}.", this);
    }

    @Override
    protected void handle(SyncSurveyCompletedToFirebaseJob job) throws IOException {
        log.info("Received job {}.", job);
        var projectIds = job.getProjectIds();
        if (Iterables.isEmpty(projectIds)) {
            return;
        }

        for (String projectId : projectIds) {
            firebaseProjectService.syncSurveyCompletedToFirebase(Long.parseLong(projectId));
        }
        log.info("Successfully sync survey completed to firebase with projects {}.", projectIds);
    }
}
