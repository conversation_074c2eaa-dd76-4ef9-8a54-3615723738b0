package com.bees360.service;

import java.util.List;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.vo.WalletGlobalDiscount;

public interface AccountWalletService {

	List<WalletGlobalDiscount> listWalletDiscount(long userId) throws ServiceException;

	WalletGlobalDiscount getWalletGlobalDiscount(long userId, int discountType) throws ServiceException;

	/**
	 * 减少指定折扣
	 * <AUTHOR>
	 *
	 * @param curUserId 当前操作用户
	 * @param discountType 折扣类型
	 * @param num 减少的个数
	 * @throws ServiceException
	 */
	int subDiscount(long curUserId, int discountType, int num) throws ServiceException;

	double getWalletBalance(long userId) throws ServiceException;
}
