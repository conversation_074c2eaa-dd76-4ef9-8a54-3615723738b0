package com.bees360.service.impl;

import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.bees360.entity.enums.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.HouseImageSegmentType;
import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.enums.RoleEnum.RoleType;
import com.bees360.mapper.HouseImageSegmentTypeMapper;
import com.bees360.service.AppProjectService;
import com.bees360.service.ConstantsService;
import com.bees360.util.ConstantUtil;
import com.bees360.util.enumsutil.CodeEnumUtil;

@Service("consantsService")
public class ConstantsServiceImpl implements ConstantsService{

	@Inject
	HouseImageSegmentTypeMapper houseImageSegmentTypeMapper;

    @Value("${options.service-type.hiding:}")
    private Set<Integer> hidingServicesType = Set.of();

	@Inject
	private AppProjectService appProjectService;

	private static Comparator<IdNameDto> NAME_ORDER_COMPARATOR = (o1, o2) -> {
		if(o1 == null && o2 == null) {
			return 0;
		} else if (o1 == null) {
			return -1;
		} else if (o2 == null) {
			return 1;
		}
		if(o1.getName() == null && o2.getName() == null) {
			return 0;
		} else if (o1.getName() == null) {
			return -1;
		} else if (o2.getName() == null) {
			return 1;
		}
		return StringUtils.compare(o1.getName(), o2.getName());
	};

	@Override
	public Map<String, Object> getStaticOptionsMap(Set<String> options) throws ServiceException{
		Map<String, Object> optionsMap = new HashMap<>();
		if(options == null){
			return optionsMap;
		}
		if(options.contains(IMAGE_TYPE)){
			List<IdNameDto> imageTypes = CodeEnumUtil.enumsToIdName(ImageTypeEnum.class);
			optionsMap.put(IMAGE_TYPE, imageTypes);
		}
		if(options.contains(COMPANY_TYPES_OPTIONS)){
			List<IdNameDto> companyTypes = CodeEnumUtil.enumsToIdName(CompanyTypeEnum.values(), CompanyTypeEnum.OTHER);
			companyTypes.sort(NAME_ORDER_COMPARATOR);
			companyTypes.add(CodeEnumUtil.enumsToIdName(CompanyTypeEnum.OTHER));
			optionsMap.put(COMPANY_TYPES_OPTIONS, companyTypes);
		}
		if(options.contains(PROJECT_TYPES_OPTIONS)){
			List<IdNameDto> projectTypes = CodeEnumUtil.enumsToIdName(ProjectTypeEnum.class);
			optionsMap.put(PROJECT_TYPES_OPTIONS, projectTypes);
		}
		if(options.contains(PROJECT_STATUS_OPTIONS)){
			List<IdNameDto> projectStatus = CodeEnumUtil.enumsToIdName(ProjectStatusEnum.class);
			optionsMap.put(PROJECT_STATUS_OPTIONS, projectStatus);
		}
		if(options.contains(CLAIM_TYPE_OPTIONS)){
			List<IdNameDto> claimTypes = CodeEnumUtil.enumsToIdName(ClaimTypeEnum.class);
			optionsMap.put(CLAIM_TYPE_OPTIONS, claimTypes);
		}
		if(options.contains(INSPECTION_CATEGORY_OPTIONS)){
			List<IdNameDto> claimTypes = CodeEnumUtil.enumsToIdName(InspectionCategoryEnum.class);
			optionsMap.put(INSPECTION_CATEGORY_OPTIONS, claimTypes);
		}
		if(options.contains(POSITION_TYPE_OPTIONS)){
			List<IdNameDto> claimTypes = CodeEnumUtil.enumsToIdName(PositionTypeEnum.class);
			optionsMap.put(POSITION_TYPE_OPTIONS, claimTypes);
		}
		if(options.contains(DAMAGE_SEVERITY_OPTIONS)){
			List<IdNameDto> claimTypes = DamageSeverityEnum.enumsToIdNameDescription();
			optionsMap.put(DAMAGE_SEVERITY_OPTIONS, claimTypes);
		}
		if(options.contains(FILE_SOURCE_TYPE_OPTIONS)){
			List<IdNameDto> fileSourceType = CodeEnumUtil.enumsToIdName(FileSourceTypeEnum.class);
			optionsMap.put(FILE_SOURCE_TYPE_OPTIONS, fileSourceType);
		}
		if(options.contains(DIRECTION_OPTIONS)){
			List<IdNameDto> direction = CodeEnumUtil.enumsToIdName(DirectionEnum.class);
			optionsMap.put(DIRECTION_OPTIONS, direction);
		}
		if(options.contains(SHINGLE_LAYER_OPTIONS)){
			List<IdNameDto> direction = CodeEnumUtil.enumsToIdName(ShingleLayerEnum.class);
			optionsMap.put(SHINGLE_LAYER_OPTIONS, direction);
		}
		if(options.contains(FILTER_IMAGES_OPTIONS)){
			List<IdNameDto> fileSourceType = CodeEnumUtil.enumsToIdName(FileSourceTypeEnum.class);
			// Avoid ID conflict
			for (IdNameDto idNameDto : fileSourceType) {
				idNameDto.setId(idNameDto.getId());
			}
			optionsMap.put(FILTER_IMAGES_OPTIONS, fileSourceType);
		}
		if(options.contains(ROLES_OPTIONS)){
			List<IdNameDto> rolesParts = new ArrayList<>();
			// remove System role
			for(RoleEnum role: RoleEnum.values(RoleType.CAREER)){
				rolesParts.add(new IdNameDto(role.getCode(), role.getDisplay()));
			}
			optionsMap.put(ROLES_OPTIONS, rolesParts);
		}
		if(options.contains(SHINGLE_TYPE_OPTIONS)){
			List<HouseImageSegmentType> segmentTypes = appProjectService.listSegmentType();
			List<IdNameDto> roofMaterial = new ArrayList<>();
			for (HouseImageSegmentType segmentType : segmentTypes) {
				if (segmentType.getParentId() == ConstantUtil.SEGMENT_ROOF_MATERIAL) {
					roofMaterial.add(new IdNameDto(segmentType.getId(), segmentType.getName()));
				}
			}
			optionsMap.put(SHINGLE_TYPE_OPTIONS, roofMaterial);
		}
		List<HouseImageSegmentType> segmentTypeList = new ArrayList<>(0);
		if (options.contains(OBJECTS_ON_ROOFING_OPTIONS)) {
			segmentTypeList = houseImageSegmentTypeMapper.getImageSegmentTypes();
		}
		if (options.contains(OBJECTS_ON_ROOFING_OPTIONS)) {
			List<IdNameDto> objectsOnRoofings = new ArrayList<>(12);
			for (HouseImageSegmentType houseImageSegmentType : segmentTypeList) {
				if (houseImageSegmentType.getParentId() == ConstantUtil.SEGMENT_TYPE_OBJECTS_ON_ROOFING) {
					IdNameDto idName = new IdNameDto(houseImageSegmentType.getId(), houseImageSegmentType.getName());
					objectsOnRoofings.add(idName);
				}
			}
			optionsMap.put(OBJECTS_ON_ROOFING_OPTIONS, objectsOnRoofings);
		}
		if(options.contains(PROJECT_COMPONENT_TYPE_OPTIONS)){
			List<IdNameDto> componentTypes = CodeEnumUtil.enumsToIdName(StructrueEnum.class);
			optionsMap.put(PROJECT_COMPONENT_TYPE_OPTIONS, componentTypes);
		}
		if(options.contains(SEGMENT_ROOT_OPTIONS)){
			List<IdNameDto> segmentRoolOptions = CodeEnumUtil.enumsToIdName(SegmentRootEnum.class);
			optionsMap.put(SEGMENT_ROOT_OPTIONS, segmentRoolOptions);
		}
		if(options.contains(PROCESS_STATUS)){
			List<IdNameDto> processStatuses = new ArrayList<>();
			for(ProcessStatusEnum status: ProcessStatusEnum.values()) {
				if(status.isVisible()) {
					processStatuses.add(new IdNameDto(status.getCode(), status.getDisplay()));
				}
			}
			optionsMap.put(PROCESS_STATUS, processStatuses);
		}
		if(options.contains(ROOF_AGES)){
			List<IdNameDto> direction = CodeEnumUtil.enumsToIdName(RoofAgeEnum.class);
			optionsMap.put(ROOF_AGES, direction);
		}
		if(options.contains(NEW_PROJECT_STATUSES)) {
			List<IdNameDto> statuses = Arrays.stream(NewProjectStatusEnum.values())
				.map(s -> new IdNameDto(s.getCode(), s.getDisplay()))
				.collect(Collectors.toList());
			optionsMap.put(NEW_PROJECT_STATUSES, statuses);
		}
        if(options.contains(SERVICE_TYPES)) {
            Supplier<ProjectServiceTypeEnum[]> serviceTypeSupplier = () -> {
                var types = ProjectServiceTypeEnum.values();
                return Arrays.stream(types).filter(t -> !hidingServicesType.contains(t.getCode())).toArray(ProjectServiceTypeEnum[]::new);
            };
            putOptionsToMap(optionsMap, SERVICE_TYPES, serviceTypeSupplier);
        }
        if(options.contains(MOBILE_IMAGE_CATEGORY)) {
            putOptionsToMap(optionsMap, MOBILE_IMAGE_CATEGORY, ImagePartialViewTypeEnum::values, t -> t != ImagePartialViewTypeEnum.ROOF);
        }
		return optionsMap;
	}

    private <T extends BaseCodeEnum> void putOptionsToMap(Map<String, Object> optionsMap, String name,
                                                          Supplier<T[]> tArraySupplier) {
        putOptionsToMap(optionsMap, name, tArraySupplier, t -> true);
    }

    private <T extends BaseCodeEnum> void putOptionsToMap(Map<String, Object> optionsMap, String name,
                                                          Supplier<T[]> tArraySupplier, Predicate<? super T> predicate) {
        List<IdNameDto> idNames = Arrays.stream(tArraySupplier.get())
            .filter(predicate)
            .map(t -> new IdNameDto(t.getCode(), t.getDisplay()))
            .collect(Collectors.toList());
        optionsMap.put(name, idNames);
    }
}
