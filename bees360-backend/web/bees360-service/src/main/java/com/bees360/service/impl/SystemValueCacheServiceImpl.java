package com.bees360.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSONObject;
import com.bees360.base.Constants;
import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.SystemValue;
import com.bees360.entity.enums.ReportServiceOptionEnum;
import com.bees360.entity.enums.RoofEstimatedAreaEnum;
import com.bees360.entity.enums.ServiceFeeTypeEnum;
import com.bees360.entity.vo.Money;
import com.bees360.service.payment.SystemValueCacheService;
import com.bees360.util.CacheKeys;
import com.bees360.util.CurrencyUtil;
import com.bees360.util.RedisUtil;

@Service("systemValueCacheService")
public class SystemValueCacheServiceImpl implements SystemValueCacheService{

	public static final String UNDERLINE = "_";
	public static final String MIDLINE = "-";

	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_PRICEFORDAMAGEMEASUREMENTREPORT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SYSTEM_VALUE_PRICEFORDAMAGEMEASUREMENTREPORT;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_PRICEFORONLYMEASUREMENTREPORT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SYSTEM_VALUE_PRICEFORONLYMEASUREMENTREPORT;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_PRICEFORQUICKDAMAGEWEBREPORT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SYSTEM_VALUE_PRICEFORQUICKDAMAGEWEBREPORT;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_PRICEFORQUICKDAMAGEAPPREPORT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SYSTEM_VALUE_PRICEFORQUICKDAMAGEAPPREPORT;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_PRICEFORQUICKDAMAGESKYREPORT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SYSTEM_VALUE_PRICEFORQUICKDAMAGESKYREPORT;

	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_COMMISSIONRATE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SYSTEM_VALUE_COMMISSIONRATE;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_PRICEFORGREATTHAN5000FEET = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SYSTEM_VALUE_PRICEFORGREATTHAN5000FEET;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_AREATHRESHOLDFORPRICE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SYSTEM_VALUE_AREATHRESHOLDFORPRICE;

	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_TAXRATE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS + UNDERLINE
			+ SystemValue.SYSTEM_VALUE_TAXRATE;

	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_FREEONTRAILPROJECTNUM = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SYSTEM_VALUE_FREEONTRAILPROJECTNUM;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_NEWCUSTOMERDISCOUNTPERCENT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SYSTEM_VALUE_NEWCUSTOMERDISCOUNTPERCENT;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_NEWCUSTOMERDISCOUNTPROJECTNUM = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SYSTEM_VALUE_NEWCUSTOMERDISCOUNTPROJECTNUM;

	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_PILOT_SERVICE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_PILOT_SERVICE;

	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_PILOT_TAX_PERCENTAGE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_PILOT_TAX_PERCENTAGE;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_TAX_PERCENTAGE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_REPORT_TAX_PERCENTAGE;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_PACKAGE_TAX_PERCENTAGE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_PACKAGE_TAX_PERCENTAGE;

	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_ESTIMATED_ROOF_AREA = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_ESTIMATED_ROOF_AREA;


	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_MEASUREMENT_AND_DAMAGE_REPORT_SERVICE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_MEASUREMENT_AND_DAMAGE_REPORT_SERVICE;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_MEASUREMENT_REPORT_SERVICE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_MEASUREMENT_REPORT;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_QUICK_DAMAGE_REPORT_SERVICE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_QUICK_DAMAGE_REPORT;

	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_BIDDING_REPORT_SERVICE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_BIDDING_REPORT;

	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_HIGHFLY_REPORT_SERVICE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_HIGHFLY_REPORT;

	//2019-03-25 price is changed
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_REAL_TIME_DAMAGE_ASSESSMENT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_REPORT_PRICE_REAL_TIME_DAMAGE_ASSESSMENT;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_FULL_SCOPE_UNDERWRITING_REPORT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_REPORT_PRICE_FULL_SCOPE_UNDERWRITING_REPORT;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_ROOF_ONLY_UNDERWRITING_REPORT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
		+ UNDERLINE + SystemValue.SERVICE_KEY_REPORT_PRICE_ROOF_ONLY_UNDERWRITING_REPORT;

	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_HIGHFLY_EVALUATION = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_REPORT_PRICE_HIGHFLY_EVALUATION;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_INFRARED_DAMAGE_ASSESSMENT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_REPORT_PRICE_INFRARED_DAMAGE_ASSESSMENT;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_ON_SITE_BIDDING = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_REPORT_PRICE_ON_SITE_BIDDING;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_PRELIMINARY_DAMAGE_ASSESSMENT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_REPORT_PRICE_PRELIMINARY_DAMAGE_ASSESSMENT;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_PREMIUM_DAMAGE_ASSESSMENT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_REPORT_PRICE_PREMIUM_DAMAGE_ASSESSMENT;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_PREMIUM_MEASUREMENT = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_REPORT_PRICE_PREMIUM_MEASUREMENT;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_PROPERTY_IMAGE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_REPORT_PRICE_PROPERTY_IMAGE;
	public static final String REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_REAL_TIME_QUICK_SQUARE = CacheKeys.REDIS_KEY_SYSTEM_VALUE_KEYS
			+ UNDERLINE + SystemValue.SERVICE_KEY_REPORT_PRICE_REAL_TIME_QUICK_SQUARE;

	private final String REPORT_IS_NOT_SUPPORTED;

	private static final Logger logger = LoggerFactory.getLogger(SystemValueCacheServiceImpl.class);

	@Autowired
	RedisUtil redisUtil;

    public SystemValueCacheServiceImpl(Environment environment) {
        String clientEmail = environment.getProperty("service.client.email");
        this.REPORT_IS_NOT_SUPPORTED = "the report is not supportted by system, please contact to " + clientEmail;
    }

	@Override
	public double getCommissionRate() throws ServiceException {
		SystemValue systemValue = getSystemValue(REDIS_KEY_SYSTEM_VALUE_KEYS_COMMISSIONRATE);
		return systemValue == null ? 0 : (double)systemValue.getServiceValue();
	}

	/**
	 * 2018-10-16 tax rate has changed from 6.25% to 8.25% along with migration from paypal to square
	 */
	@Override
	public double getTaxRate() throws ServiceException {
		SystemValue systemValue = getSystemValue(REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_TAX_PERCENTAGE);
		return systemValue == null ? 0 : (double)systemValue.getServiceValue();
	}

	@Override
	public Money getPriceForGreatThan5000Feet() throws ServiceException {
		SystemValue systemValue = getSystemValue(REDIS_KEY_SYSTEM_VALUE_KEYS_PRICEFORGREATTHAN5000FEET);
		double value = systemValue == null ? 0 : (double)systemValue.getServiceValue();
		String currency = systemValue.getCountry().equals(Constants.COUNTRY_US) ? Constants.CURRENCY_USD : null;
		return new Money(value, currency);
	}

	@Override
	public double getAreaThresholdForPrice() throws ServiceException {
		SystemValue systemValue = getSystemValue(REDIS_KEY_SYSTEM_VALUE_KEYS_AREATHRESHOLDFORPRICE);
		return systemValue == null ? 0 : (double)systemValue.getServiceValue();
	}


	private SystemValue getSystemValue(String key) throws ServiceException {
		SystemValue systemValue = new SystemValue();
		try {
			systemValue =  convertJsonToObject(redisUtil.get(key).toString());
		} catch (Exception e) {
			logger.error("Fail to get value from redis with key: " + key, e);
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
		return systemValue;
	}


	@Override
	public int getFreeOnTrailProjectNum() throws ServiceException {
		SystemValue systemValue = getSystemValue(REDIS_KEY_SYSTEM_VALUE_KEYS_FREEONTRAILPROJECTNUM);
		return systemValue == null ? 0 : (int)systemValue.getServiceValue();
	}

	@Override
	public double getNewCustomerDiscountPercent() throws ServiceException {
		SystemValue systemValue = getSystemValue(REDIS_KEY_SYSTEM_VALUE_KEYS_NEWCUSTOMERDISCOUNTPERCENT);
		return systemValue == null ? 0 : (double)systemValue.getServiceValue();
	}

	@Override
	public int getNewCustomerDiscountProjectNum() throws ServiceException {
		SystemValue systemValue = getSystemValue(REDIS_KEY_SYSTEM_VALUE_KEYS_NEWCUSTOMERDISCOUNTPROJECTNUM);
		return systemValue == null ? 0 : (int)systemValue.getServiceValue();
	}


	@Override
	public Map<String,Object> getPilotAndReportPrices() throws ServiceException{
		Map<String, Object> priceMap = new HashMap<>();
		try {
			// 1.get pilot service price
			SystemValue pilotServiceSystemValue = getSystemValue(REDIS_KEY_SYSTEM_VALUE_KEYS_PILOT_SERVICE);
			Map<String, Object> pilotServiceMap = convertSystemValueToMap(pilotServiceSystemValue);
			priceMap.put("pilotService", pilotServiceMap);

			// 2.get EstimatedRoofArea enums
			List<Map<String, Object>> roofEstimatedAreas = new ArrayList<Map<String, Object>>();
			RoofEstimatedAreaEnum[] roofEstimatedAreaEnums = RoofEstimatedAreaEnum.values();
			for (int length = roofEstimatedAreaEnums.length, i = length - 1; i >= 0; i--) {
				RoofEstimatedAreaEnum roofEstimatedAreaEnum = roofEstimatedAreaEnums[i];
				if(roofEstimatedAreaEnum == RoofEstimatedAreaEnum.AREA_150SQ_INFINITY) {
					continue;
				}
				String roofEstimatedAreaKey = String.valueOf(roofEstimatedAreaEnum.getCode());
				SystemValue roofEstimatedAreaSystemValue = (SystemValue) redisUtil
						.get(REDIS_KEY_SYSTEM_VALUE_KEYS_ESTIMATED_ROOF_AREA + MIDLINE + roofEstimatedAreaKey);
				// setting checkbox info of area
				Map<String, Object> roofEstimatedAreaMap = new HashMap<>();
				roofEstimatedAreas.add(roofEstimatedAreaMap);
				roofEstimatedAreaMap.put("key", roofEstimatedAreaSystemValue.getServiceName());
				roofEstimatedAreaMap.put("label", roofEstimatedAreaSystemValue.getLabel());

				// 3.get reportServiceOption under EstimatedRoofArea enum
				List<SystemValue> reportSystemValues = new ArrayList<>();
				ReportServiceOptionEnum[] reportServiceOptionEnums = ReportServiceOptionEnum.values();
				for (ReportServiceOptionEnum reportServiceOptionEnum : reportServiceOptionEnums) {
					String reportServiceOptionKey = String.valueOf(reportServiceOptionEnum.getCode());
					// setting report prices
					switch(reportServiceOptionEnum) {
						case MEASUREMENT_AND_DAMAGE_REPORT:{
							String measurementAndDamageReportKey = REDIS_KEY_SYSTEM_VALUE_KEYS_MEASUREMENT_AND_DAMAGE_REPORT_SERVICE
									+ MIDLINE + roofEstimatedAreaKey + MIDLINE + reportServiceOptionKey;
							reportSystemValues.add(getSystemValue(measurementAndDamageReportKey));
							continue;
						}
						case MEASUREMENT_REPORT:{
							String measurementReportKey = REDIS_KEY_SYSTEM_VALUE_KEYS_MEASUREMENT_REPORT_SERVICE + MIDLINE
									+ roofEstimatedAreaKey + MIDLINE + reportServiceOptionKey;
							reportSystemValues.add(getSystemValue(measurementReportKey));
							continue;
						}
						case QUICK_DAMAGE_REPORT:{
							String quickDamageReportKey = REDIS_KEY_SYSTEM_VALUE_KEYS_QUICK_DAMAGE_REPORT_SERVICE + MIDLINE
									+ roofEstimatedAreaKey + MIDLINE + reportServiceOptionKey;
							reportSystemValues.add(getSystemValue(quickDamageReportKey));
							continue;
						}
						default:
							continue;
					}
				}
				List<Map<String, Object>> reportSystemValueMaps = convertSystemValueToMap(reportSystemValues);
				roofEstimatedAreaMap.put("reportServiceOptions", reportSystemValueMaps);
			}

			priceMap.put("roofEstimatedAreas", roofEstimatedAreas);
		} catch (Exception e) {
			logger.error("SystemValueServiceImpl getPilotAndReportPrice() failed:", e);
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
		return priceMap;
	}

	@Override
	public Money getReportServicePrice(int roofEstimatedAreaItem, int reportServiceOption) throws ServiceException{
		ReportServiceOptionEnum reportServiceOptionEnum = ReportServiceOptionEnum.getEnum(reportServiceOption);
		String reportServiceOptionKey = "";
		switch (reportServiceOptionEnum) {
			case MEASUREMENT_AND_DAMAGE_REPORT:{
				reportServiceOptionKey = REDIS_KEY_SYSTEM_VALUE_KEYS_MEASUREMENT_AND_DAMAGE_REPORT_SERVICE;
				break;
			}
			case MEASUREMENT_REPORT:{
				reportServiceOptionKey = REDIS_KEY_SYSTEM_VALUE_KEYS_MEASUREMENT_REPORT_SERVICE;
				break;
			}
			case QUICK_DAMAGE_REPORT:{
				reportServiceOptionKey = REDIS_KEY_SYSTEM_VALUE_KEYS_QUICK_DAMAGE_REPORT_SERVICE;
			}
			default:
				break;
		}
		SystemValue reportServiceOptionSystemValue = null;
		try {
			reportServiceOptionSystemValue = getSystemValue(reportServiceOptionKey + MIDLINE
					+ String.valueOf(roofEstimatedAreaItem) + MIDLINE + String.valueOf(reportServiceOption));
		} catch (Exception e) {
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}

		Money money = null;
		if(reportServiceOptionSystemValue != null) {
			money = new Money(reportServiceOptionSystemValue.getServiceValue(),CurrencyUtil.converCountryToCurrency(reportServiceOptionSystemValue.getCountry()));
		}
		return money;
	}

	@Override
	public Money getReportServicePrice(int serviceFeeTypeId) throws ServiceException{
		if(serviceFeeTypeId == 0) {
			logger.error(REPORT_IS_NOT_SUPPORTED + ",serviceFeeTypeId is:" + serviceFeeTypeId);
			throw new ServiceException(MessageCode.CONTACT_FOR_DETAILS, "the report is not supported," + REPORT_IS_NOT_SUPPORTED);
		}

		ServiceFeeTypeEnum serviceFeeTypeEnum = ServiceFeeTypeEnum.getEnum(serviceFeeTypeId);
		if(ServiceFeeTypeEnum.getEnum(serviceFeeTypeId) == null) {
			logger.error(REPORT_IS_NOT_SUPPORTED + ",serviceFeeTypeId is:" + serviceFeeTypeId);
			throw new ServiceException(MessageCode.CONTACT_FOR_DETAILS, "the report is not supported," + REPORT_IS_NOT_SUPPORTED);
		}

		String  reportPriceKey = null;
		switch(serviceFeeTypeEnum) {

			case REAL_TIME_DAMAGE_ASSESSMENT:
				reportPriceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_REAL_TIME_DAMAGE_ASSESSMENT;
				break;
			case PREMIUM_DAMAGE_ASSESSMENT_REPORT:
				reportPriceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_PREMIUM_DAMAGE_ASSESSMENT;
				break;
			case PREMIUM_MEASUREMENT_REPORT:
				reportPriceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_PREMIUM_MEASUREMENT;
				break;

			case PROPERTY_IMAGE_REPORT:
				reportPriceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_PROPERTY_IMAGE;
				break;
			case INFRARED_DAMAGE_ASSESSMENT:
				reportPriceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_INFRARED_DAMAGE_ASSESSMENT;
				break;
			case PRELIMINARY_DAMAGE_ASSESSMENT_REPORT:
				reportPriceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_PRELIMINARY_DAMAGE_ASSESSMENT;
				break;

			case HIGHFLY_EVALUATION_REPORT:
				reportPriceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_HIGHFLY_EVALUATION;
				break;
			case REAL_TIME_QUICK_SQUARE_REPORT:
				reportPriceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_REAL_TIME_QUICK_SQUARE;
				break;

			//ON_SITE_BIDDING_REPORT is free
			case ON_SITE_BIDDING_REPORT:
				reportPriceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_ON_SITE_BIDDING;
				break;
			case ROOF_ONLY_UNDERWRITING_REPORT:
				reportPriceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_ROOF_ONLY_UNDERWRITING_REPORT;
				break;
			case FULL_SCOPE_UNDERWRITING_REPORT:
				reportPriceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_PRICE_FULL_SCOPE_UNDERWRITING_REPORT;
				break;
			default:
				break;
		}

		if(reportPriceKey == null) {
			logger.error(REPORT_IS_NOT_SUPPORTED + ",serviceFeeTypeId is:" + serviceFeeTypeId);
			throw new ServiceException(MessageCode.CONTACT_FOR_DETAILS, "the report is not supported," + REPORT_IS_NOT_SUPPORTED);
		}
		SystemValue reportServiceOptionSystemValue = null;
		try {
			reportServiceOptionSystemValue = getSystemValue(reportPriceKey);
		} catch (Exception e) {
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
		Money money = null;
		if(reportServiceOptionSystemValue != null) {
			money = new Money(reportServiceOptionSystemValue.getServiceValue(),CurrencyUtil.converCountryToCurrency(reportServiceOptionSystemValue.getCountry()));
		}
		return money;
	}

	@Override
	public Money getPilotServicePrice()throws ServiceException{
		String pilotServiceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_PILOT_SERVICE;
		double pilotServicePrice = 0;
		String currency = null;
		try {
			SystemValue systemValue = getSystemValue(pilotServiceKey);
			pilotServicePrice = systemValue.getServiceValue();
			currency = CurrencyUtil.converCountryToCurrency(systemValue.getCountry());
		} catch (Exception e) {
			logger.error("SystemValueServiceImpl.getPilotServicePrice failed:",e);
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
		return new Money(pilotServicePrice,currency);
	}

	private List<Map<String,Object>> convertSystemValueToMap(List<SystemValue> systemValues) {
		if(systemValues == null || systemValues.size() == 0) {
			return null;
		}
		List<Map<String,Object>> systemValueMaps = new ArrayList<Map<String,Object>>();
		for(SystemValue systemValue : systemValues) {
			systemValueMaps.add(convertSystemValueToMap(systemValue));
		}
		return systemValueMaps;
	}

	private Map<String,Object> convertSystemValueToMap(SystemValue systemValue){
		Map<String,Object> systemValueMap = new HashMap<>();
		if(systemValue == null) {
			return systemValueMap;
		}
		systemValueMap.put("id", systemValue.getServiceId());
		systemValueMap.put("value", systemValue.getServiceValue());
		systemValueMap.put("label", systemValue.getLabel());
		String currency = CurrencyUtil.converCountryToCurrency(systemValue.getCountry());
		systemValueMap.put("currency", currency);
		return systemValueMap;
	}

	@Override
	public Map<String, Object> getPilotAndReportTaxPercentage() throws ServiceException {
		Map<String,Object> taxMap  = new HashMap<>();
		try {
			double pilotTaxPercentage = getSystemValue(REDIS_KEY_SYSTEM_VALUE_KEYS_PILOT_TAX_PERCENTAGE).getServiceValue();
			taxMap.put("pilotTaxPercentage", pilotTaxPercentage);
			double reportTaxPercentage = getSystemValue(REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_TAX_PERCENTAGE).getServiceValue();
			taxMap.put("reportTaxPercentage", reportTaxPercentage);
		} catch (Exception e) {
			logger.error("SystemValueServiceImpl.getPilotAndReportTax failed:",e);
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
		return taxMap;
	}

	@Override
	public double getPilotTaxPercentage() throws ServiceException {
		double pilotTaxPercentage = 0;
		try {
			pilotTaxPercentage = getSystemValue(REDIS_KEY_SYSTEM_VALUE_KEYS_PILOT_TAX_PERCENTAGE).getServiceValue();
		} catch (Exception e) {
			logger.error("SystemValueServiceImpl.getPilotTaxPercentage failed:",e);
			throw new ServiceException(MessageCode.REDIS_OPERATION_FAILED);
		}
		return pilotTaxPercentage;
	}

	@Override
	public double getReportTaxPercentage() throws ServiceException {
		double reportTaxPercentage = 0;
		try {
			reportTaxPercentage = getSystemValue(REDIS_KEY_SYSTEM_VALUE_KEYS_REPORT_TAX_PERCENTAGE).getServiceValue();
		} catch (Exception e) {
			logger.error("SystemValueServiceImpl.getReportTaxPercentage failed:",e);
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
		return reportTaxPercentage;
	}

	@Override
	public Money getBiddingReportPrice() throws ServiceException {
		String biddingReportServiceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_BIDDING_REPORT_SERVICE;
		double biddingReportServicePrice = 0;
		String currency = null;
		try {
			SystemValue systemValue = getSystemValue(biddingReportServiceKey);
			biddingReportServicePrice = systemValue.getServiceValue();
			currency = CurrencyUtil.converCountryToCurrency(systemValue.getCountry());
		} catch (Exception e) {
			logger.error("SystemValueServiceImpl.getBiddingReportPrice failed:",e);
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
		return new Money(biddingReportServicePrice,currency);
	}


	private SystemValue convertJsonToObject(String json) {
		if(json == null || "".equals(json.trim())) {
			return new SystemValue();
		}
		SystemValue systemValue = null;
		try {
			systemValue = JSONObject.parseObject(json, SystemValue.class);
		}catch(Exception e) {
			logger.error("convert json string to SystemValue object failed,", e);
		}
		return systemValue;
	}

	public Money getHighflyReportPrice() throws ServiceException{
		String highflyReportServiceKey = REDIS_KEY_SYSTEM_VALUE_KEYS_HIGHFLY_REPORT_SERVICE;
		double highReportServicePrice = 0;
		String currency = null;
		try {
			SystemValue systemValue = getSystemValue(highflyReportServiceKey);
			highReportServicePrice = systemValue.getServiceValue();
			currency = CurrencyUtil.converCountryToCurrency(systemValue.getCountry());
		} catch (Exception e) {
			logger.error("SystemValueServiceImpl.getHighflyReportPrice failed:",e);
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
		return new Money(highReportServicePrice,currency);
	}
}
