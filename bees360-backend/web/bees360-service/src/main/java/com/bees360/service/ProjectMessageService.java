package com.bees360.service;

import com.bees360.entity.ProjectMessage;
import com.bees360.entity.query.ProjectMessageQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since  2020/12/11 14:18
 */
public interface ProjectMessageService {

    /**
     * 该接口是用来修复activity历史数据的,后续将移除该接口
     * @param messages 消息
     * @return 被恢复activity的项目ID
     */
    @Deprecated
    List<Long> addActivity(List<ProjectMessage> messages);

    /**
     * 项目留言
     * @param message 项目留言
     */
    void addMessage(ProjectMessage message);

    /**
     * 项目留言并且同步到AI
     * @param message 项目留言
     */
    void addMessageAndSyncToAi(ProjectMessage message);

    void addMessageAndSyncToAiWithoutActivity(ProjectMessage message);

    /**
     * 查询项目留言
     *
     * @param query 项目ID
     * @return 项目问卷回答列表
     */
    List<ProjectMessage> listMessage(@Param(value = "query") ProjectMessageQuery query);


    ProjectMessage getLatestMessage(@Param(value = "query") ProjectMessageQuery query);

    /**
     * 根据查询条件删除消息
     * @param query 查询类
     * @return 删除数据的条数
     */
    int delete(@Param(value = "query") ProjectMessageQuery query);
}
