package com.bees360.job;

import com.bees360.job.registry.FirebaseMissionCheckOutJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseMissionService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * This class is for handle mission check out update from firebase.
 */
@Log4j2
public class FirebaseMissionCheckOutJobExecutor extends AbstractJobExecutor<FirebaseMissionCheckOutJob> {

    private final FirebaseMissionService firebaseMissionService;

    public FirebaseMissionCheckOutJobExecutor(FirebaseMissionService firebaseMissionService) {
        this.firebaseMissionService = firebaseMissionService;
        log.info("Created {}.", this);
    }

    @Override
    protected void handle(FirebaseMissionCheckOutJob job) throws IOException {
        var projectId = Long.parseLong(job.getProjectId());
        var statusUpdateTime = job.getStatusUpdateTime().toEpochMilli();
        firebaseMissionService.handleMissionCheckOut(
            job.getMissionPath(), job.getPilotId(), projectId, statusUpdateTime);

        log.info("Successfully handle mission '{}' check out.", job.getMissionPath());
    }
}
