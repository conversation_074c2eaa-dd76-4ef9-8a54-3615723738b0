package com.bees360.service.listener.firebase;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.ImageTypeEnum;
import com.bees360.entity.enums.OrientationEnum;
import com.bees360.entity.firebase.FirebaseImage;
import com.bees360.entity.firebase.FirebaseRoom;
import com.bees360.entity.firebase.ImageCategoryEnum;
import com.bees360.event.registry.FirebaseImageUploaded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ProjectImageService;
import com.bees360.service.RoomService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.service.firebase.impl.FirebaseServiceImpl;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.service.util.DocumentReferenceAdapter;
import com.bees360.service.util.ProjectImageReSorterConfig;
import com.google.cloud.Timestamp;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import com.google.gson.Gson;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Component;

/**
 * 监听Firebase图片上传事件，将Firebase图片数据转换为项目图片并更新数据库
 */
@Import({ProjectImageReSorterConfig.class,})
@Log4j2
@Component
public class FirebaseImageChangedListener extends AbstractNamedEventListener<FirebaseImageUploaded> {

    private final ProjectImageService projectImageService;
    private final FirebaseService firebaseService;
    private final RoomService roomService;
    private final Bees360FeatureSwitch bees360FeatureSwitch;

    private final Gson gson;

    public FirebaseImageChangedListener(
        ProjectImageService projectImageService,
        FirebaseService firebaseService,
        RoomService roomService,
        Bees360FeatureSwitch bees360FeatureSwitch, Firestore firestore) {
        this.projectImageService = projectImageService;
        this.firebaseService = firebaseService;
        this.roomService = roomService;
        this.bees360FeatureSwitch = bees360FeatureSwitch;
        gson =
                new Gson()
                        .newBuilder()
                        .registerTypeAdapter(
                                DocumentReference.class, new DocumentReferenceAdapter(firestore))
                        .create();
        log.info(
                "Created '{}(projectImageService={}, firebaseService={})'",
                this,
                projectImageService,
                firebaseService);
    }

    @Override
    public void handle(FirebaseImageUploaded image) {
        log.info("Start to handle image update '{}'.", image);
        List<FirebaseImage> firebaseImages = fetchImage(image.getDocumentsChanged());
        if (CollectionUtils.isEmpty(firebaseImages)) {
            return;
        }
        List<ProjectImage> projectImages =
                firebaseImages.stream()
                        .filter(Objects::nonNull)
                        .filter(newImage -> !newImage.getDeleted())
                        .map(this::FirebaseImage2ProjectImage)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        for (ProjectImage projectImage : projectImages) {
            projectImageService.getNewImagesAndUpdateOldImages(
                    projectImage.getProjectId(), List.of(projectImage));
        }
    }

    /**
     * @see com.bees360.service.firebase.impl.FirebaseServiceImpl #getProjectImages(long, long, List,
     *     Map)
     * @param firebaseImage
     * @return ProjectImage
     */
    private ProjectImage FirebaseImage2ProjectImage(FirebaseImage firebaseImage) {
        ProjectImage projectImage = null;
        try {
            // 没有projectId的图片是训练的mission使用的图片，不需要同步
            if (StringUtils.isBlank(firebaseImage.getProjectId())
                    || "null".equals(firebaseImage.getProjectId())) {
                return null;
            }
            ImageCategoryEnum categoryEnum =
                    ImageCategoryEnum.getEnum(firebaseImage.getCategoryId());
            projectImage = new ProjectImage();
            BeanUtils.copyProperties(firebaseImage, projectImage);
            projectImage.setFileSourceType(categoryEnum.getFileSourceType());
            var partialType =
                bees360FeatureSwitch.isEnableGetPartialTypeBySubCategory()
                    ? FirebaseServiceImpl.getPartialType(firebaseImage.getSubCategoryId(), categoryEnum)
                    : categoryEnum.getPartialType();
            projectImage.setPartialType(partialType);
            projectImage.setOrientation(categoryEnum.getOrientation());
            projectImage.setUploadTime(formatTime(firebaseImage.getUploadTime()));
            projectImage.setShootingTime(formatTime(firebaseImage.getShootingTime()));
            projectImage.setProjectId(Long.parseLong(firebaseImage.getProjectId()));
            projectImage.setImageSort(projectImage.getShootingTime());
            projectImage.setDescription(firebaseImage.getDescription());

            Integer imageType = categoryEnum.getImageType();
            if (imageType == null) {
                imageType = ImageTypeEnum.CLOSEUP.getCode();
            }
            projectImage.setImageType(imageType);

            if (firebaseImage.getGps() != null) {
                projectImage.setGpsLocationLatitude(firebaseImage.getGps().getLatitude());
                projectImage.setGpsLocationLongitude(firebaseImage.getGps().getLongitude());
            }

            OrientationEnum orientationEnum =
                    OrientationEnum.getEnum(firebaseImage.getOrientation());
            if (orientationEnum != null) {
                projectImage.setOrientation(orientationEnum.getCode());
            }

            if (firebaseImage.getRoom() != null) {
                FirebaseRoom room = firebaseService.getRoom(firebaseImage.getRoom());
                roomService.imageSetRoomFromFirebaseRoom(room, projectImage);
            }
        } catch (Exception e) {
            log.error("Failed to update image '{}'", firebaseImage, e);
        }
        return projectImage;
    }

    private long formatTime(Timestamp timestamp) {
        if (timestamp == null) {
            return 0L;
        }
        return timestamp.toDate().getTime();
    }

    private List<FirebaseImage> fetchImage(List<String> jsons) {
        return jsons.stream()
                .map(this::fetch)
                .filter(Objects::nonNull)
                .collect(Collectors.toUnmodifiableList());
    }

    private FirebaseImage fetch(String json) {
        try {
            return gson.fromJson(json, FirebaseImage.class);
        } catch (RuntimeException e) {
            log.error(
                    "Failed to decode image '{}', it will not be retried, please solve it"
                            + " manually.",
                    json,
                    e);
        }
        return null;
    }
}
