package com.bees360.service.impl;

import com.bees360.base.exception.LogUncaughtExceptionHandler;
import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.commons.lang.time.AmericaStateTimeZoneEnum;
import com.bees360.commons.lang.time.TimeUtil;
import com.bees360.entity.BeesPilotBatch;
import com.bees360.entity.BsExportData;
import com.bees360.entity.Company;
import com.bees360.entity.ContactUs;
import com.bees360.entity.DailyBatchProjectModel;
import com.bees360.entity.DailyBatchStatusModel;
import com.bees360.entity.PartnerProgram;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.User;
import com.bees360.entity.dto.ProjectStatistics;
import com.bees360.entity.enums.BsExportDataRelatedTypeEnum;
import com.bees360.entity.enums.HighflyServiceEnum;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.entity.enums.InspectionServiceEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.firebase.entity.FirebaseFeedback;
import com.bees360.internal.ai.grpc.api.web2ai.ProjectRoleWorkServiceOuterClass;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.SendEmailJob;
import com.bees360.mail.MailSender;
import com.bees360.mail.util.MailMessageFactory;
import com.bees360.mapper.BeesPilotBatchItemMapper;
import com.bees360.mapper.MemberMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.SystemConfigMapper;
import com.bees360.resource.ResourcePool;
import com.bees360.service.BsExportDataService;
import com.bees360.service.CompanyService;
import com.bees360.service.MessageService;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectService;
import com.bees360.service.UnsubscribeEmailService;
import com.bees360.service.UserService;
import com.bees360.service.properties.Bees360WatcherProperties;
import com.bees360.service.statistics.ProjectAlertService;
import com.bees360.user.UserProvider;
import com.bees360.util.DateUtil;
import com.bees360.util.FileManager;
import com.bees360.util.file.S3KeyManager;
import com.bees360.util.msgutil.DelegateEmailSender;
import com.bees360.util.msgutil.SmsTemplateFormat;
import com.bees360.util.report.CommonsUtil;
import com.bees360.util.report.FileUtil;
import com.bees360.web.core.properties.bean.MailProperties;
import com.bees360.web.core.properties.bean.SystemConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import jakarta.inject.Inject;
import java.io.File;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.eclipse.jetty.util.URIUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @description send message to email or phone
 * @date 2017/11/17 7:40:05
 */
// <EMAIL>: need to modify all methods, consider use a thread pool to manage all the message sender.
@Log4j2
@Service("messageService")
public class MessageServiceImpl implements MessageService {

    private Logger logger = LoggerFactory.getLogger(MessageServiceImpl.class);
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    private final String EMAIL_SENDER_NO_REPLAY = "springEmailSenderNoReply";
    private final String EMAIL_SENDER_BACKEND = "springEmailSenderBackend";
    private final String EMAIL_SENDER = "springEmailSender";

    @Value("${bees360.domain}")
    private String bees360Domain;
    @Value("${bees360.login.page.url}")
    private String bees360LoginPageUrl;
    @Value("${service.client.email}")
    private String serviceClientEmail;
    @Value("${tmp.dir.upload}")
    private String tmpDirUpload;

    @Inject
    private ProjectMapper projectMapper;

    @Inject
    private ProjectService projectService;

    @Inject ProjectReportFileService projectReportFileService;

    @Inject
    private DelegateEmailSender springEmailSender;

    @Autowired
    private DelegateEmailSender springEmailSenderNoReply;

    @Autowired
    private DelegateEmailSender springEmailSenderBackend;

    @Inject
    private SmsTemplateFormat smsTemplateFormat;

    @Inject
    private SystemConfig systemConfig;

    private ExecutorService emailPool;

    @Autowired
    private S3KeyManager s3KeyManager;

    @Autowired
    private ResourcePool resourcePool;

    @Autowired
    private BsExportDataService bsExportDataService;

    @Autowired
    private Bees360WatcherProperties bees360WatcherProperties;

    @Autowired
    MemberMapper memberMapper;

    @Autowired
    UserProvider userProvider;

    @Autowired
    BeesPilotBatchItemMapper beesPilotBatchItemMapper;

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Autowired
    private ProjectAlertService projectAlertService;

    @Autowired
    private MailProperties mailProperties;

    @Autowired
    private JobScheduler jobScheduler;

    @Autowired
    private Environment env;

    @Autowired
    private MailSender doNotReplyMailSender;

    @Autowired
    private MailSender clientMailSender;

    @Autowired
    private MailMessageFactory mailMessageFactory;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private UnsubscribeEmailService unsubscribeEmailService;

    @Autowired
    private UserService userService;

    private PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();

    @Autowired
    private BiFunction<Project, String, List<UserTinyVo>> projectEmailSubscription;
    private static final Gson gson = new Gson();

    private final List<String> ENGINEER_EMAILS;
    private final String SERVICE_EMAIL;
    private final List<String> CONTACT_US_RECIPIENTS;
    private final List<String> PARTNERPROGRAM_RECIPIENTS;
    private final List<String> PROJECT_WATCHERS;
    private final List<String> EVENT_IMAGEUPLOADED_RECIPIENTS_EMAIL;
    private final List<String> EVENT_IMAGEUPLOADED_RECIPIENTS_SMS;
    // operations群组
    private final List<String> MESSAGE_OPERATIONS_RECIPIENTS;

    public MessageServiceImpl(Environment environment) {
        this.env=environment;

        ENGINEER_EMAILS = Arrays.asList(env.getProperty("system.feedback.engineer.emails").split(","));
        CONTACT_US_RECIPIENTS = Arrays.asList(env.getProperty("service.contactus.recipients").split(","));
        PARTNERPROGRAM_RECIPIENTS = Arrays
            .asList(env.getProperty("service.partnerprogram.recipients").split(","));
        SERVICE_EMAIL = env.getProperty("service.client.email");
        PROJECT_WATCHERS = Arrays.asList(env.getProperty("watch.project.watcher.recipients").split(","));
        EVENT_IMAGEUPLOADED_RECIPIENTS_EMAIL = Arrays.asList(
            env.getProperty("message.event.imageupload.recipients.email").split(","));
        EVENT_IMAGEUPLOADED_RECIPIENTS_SMS = Arrays.asList(
            env.getProperty("message.event.imageupload.recipients.sms").split(","));
        MESSAGE_OPERATIONS_RECIPIENTS = Arrays.asList(
            env.getProperty("message.operations.recipients").split(","));

        init();
    }

    private void init() {

        ThreadFactory executorThreadFactory = new BasicThreadFactory.Builder()
            .namingPattern("emailsender-threadpool-%d")
            .uncaughtExceptionHandler(new LogUncaughtExceptionHandler(logger))
            .build();

        // please use execute() instead of submit()
        // for LogUncaughtExceptionHandler can only catch exception from execute().
        emailPool = Executors.newCachedThreadPool(executorThreadFactory);
    }

    @Override
    public void sendErrorToEngineer(final String message, final Throwable e) throws ServiceException {
        logger.warn(message, e);
    }

    @Override
    public void shareReport(long projectId, String reportId, long sharerId, List<String> recipients) {

        User sharer = userService.getUserById(sharerId);
        ProjectReportFile report = projectReportFileService.getById(projectId, reportId);
        Project project = projectMapper.getById(projectId);
        if (report == null) {
            throw new ResourceNotFoundException("report " + reportId + " of project " + projectId + " not found.");
        }
        // TODO@ replay it
        String shareDir = getShareDir(projectId);
        String filePath;
        try {
            // <EMAIL> use another method to download the files
            ReportTypeEnum reportType = ReportTypeEnum.getEnum(report.getReportType());
            String reportUrl = getReportKey(report, true);
            filePath = downloadReport(shareDir, project, reportType, reportUrl);
//            File attachment = new File(filePath);
            String userAccount = sharer.getEmail() == null ? sharer.getPhone() : sharer.getEmail();
            Map<String, Object> model = new HashMap<>();
            model.put("name", sharer.getName());
            model.put("reportName", reportType.getDisplay());
            model.put("address", project.getFullAddress());
            springEmailSender.send(recipients, "shareReport", model, new Object[]{sharer.getName(), userAccount},
                Map.of(filePath, reportUrl));
        } catch (Exception e) {
            logger.error("Fail to share report of the project:" + projectId + " to the recipient:" + recipients, e);
        } finally {
            FileUtil.delFolder(shareDir);
        }
    }

    private String getReportKey(ProjectReportFile report, boolean compressedReport) {
        String reportKey = report.getReportPdfFileName();
        if (compressedReport) {
            reportKey = StringUtils.isEmpty(report.getReportPdfCompressed()) ? report.getReportPdfFileName() :
                report.getReportPdfCompressed();
        }
        return reportKey;
    }

    /**
     * Structure the dir of the report which will be send
     *
     * @return
     * <AUTHOR>
     * <AUTHOR>
     */
    private String getShareDir(long projectId) {
        return FileManager.getDirToShareReport(projectId);
    }

    private String downloadReport(String shareDir, Project project,
        ReportTypeEnum reportType, String reportUrl) {
        if (reportType == null || StringUtils.isBlank(reportUrl)) {
            return null;
        }
        String fileType = getReportFileExtension(reportType);
        File emailDir = new File(shareDir);
        String fileName = null;
        String address = project.getAddress();
        String city = project.getCity();
        String state = project.getState();
        if (FileUtil.judeDirExists(emailDir)) {
            if (address != null && city != null && state != null) {
                fileName = reportType.getDisplay() + " Report - " + project.getFullAddress() + fileType;
            } else {
                fileName = reportType.getDisplay() + " Report - " + project.getProjectId() + fileType;
            }

//            Resource resource = resourcePool.get(reportUrl);
//            File localTargetFile = new File(shareDir + fileName);
//            resource.accept(input -> FileUtils.copyToFile(input, localTargetFile));
        }
        return shareDir + fileName;
    }

    @Override
    public void infoStaffsImageUploaded(Project project, int imageNum) {
        boolean disable = true;
        if (disable) {
            // 暂时关闭该功能
            return;
        }
        final long projectId = project.getProjectId();
        emailPool.execute(new Runnable() {
            @Override
            public void run() {

                List<String> recipients = Collections.singletonList(bees360WatcherProperties.getAdminEmail());
                if (CollectionUtils.isEmpty(recipients)) {
                    logger.info("recipients for `imageUploadedSendToStaffs` is empty.");
                    return;
                } else {
                    Map<String, Object> model = new HashMap<String, Object>();
                    model.put("projectId", project.getProjectId());
                    model.put("address", project.getFullAddress());
                    model.put("imageNum", imageNum);

                    try {
                        springEmailSenderNoReply
                            .send(recipients, "imageUploadedSendToStaffs", model, new Object[]{projectId});
                    } catch (RuntimeException e) {
                        logger.error("Fail to send (uploaded images) email to " + recipients, e);
                    }
                }
                infoStaffsImageUploadedWithSMS(project, imageNum);
            }
        });
    }

    private void infoStaffsImageUploadedWithSMS(Project project, int imageNum) {
        for (String sms : EVENT_IMAGEUPLOADED_RECIPIENTS_SMS) {
            String[] parts = sms.split(":");
            if (parts.length < 2) {
                logger.warn("EVENT_IMAGEUPLOADED_RECIPIENTS_EMAIL format error: " + Arrays.toString(parts));
                continue;
            }
            String userName = parts[0];
            String huiling = parts[1];

            User user = new User();
            user.setFirstName(userName);

            String content = smsTemplateFormat.getContent("imageUploadedSendToStaffs", new Object[]{
                imageNum, project.getProjectId(), project.getFullAddress()});
            sendSms(huiling, content);
        }
    }

    private void infoStaffsImageUploadedAutoSetWithSMS(List<Project> projects) {
        final int projectLimitSize = 60;

        for (int i = 0; i < projects.size(); i += projectLimitSize) {
            int toIndex = Math.min(i + projectLimitSize, projects.size());
            List<Project> subProjects = projects.subList(i, toIndex);
            List<Long> projectIds = new ArrayList<Long>();
            for (Project p : subProjects) {
                projectIds.add(p.getProjectId());
            }
            String content = smsTemplateFormat.getContent("imageUploadedStatusIsAutoSet", new Object[]{
                projectIds.size(), StringUtils.join(projectIds, ",")
            });

            for (String sms : EVENT_IMAGEUPLOADED_RECIPIENTS_SMS) {
                String[] parts = sms.split(":");
                if (parts.length < 2) {
                    logger.warn("EVENT_IMAGEUPLOADED_RECIPIENTS_EMAIL format error: " + sms);
                    continue;
                }
                String phone = parts[1];

                sendSms(phone, content.toString());
            }
        }
    }

    @Override
    public void infoReportApproved(long projectId, List<UserTinyVo> recipients, ReportTypeEnum reportType,
        ProjectReportFile report, boolean withImagesZipLink) throws ServiceException {
        boolean isUnderwritingReport = Arrays.stream(ProjectServiceTypeEnum.values())
            .filter(s -> InspectionPurposeTypeEnum.UNDERWRITING.equals(s.getInspectionPurposeType()))
            .anyMatch(s -> s.containsReport(reportType));

        if (isUnderwritingReport || ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT.equals(reportType)) {
            infoReportApprovedWithNewTemplate(projectId, recipients, reportType, report, withImagesZipLink);
        } else {
            infoReportApprovedWithImageZip(projectId, recipients, reportType, report, withImagesZipLink);
        }
    }

    @Override
    public void infoReportOnClientReceived(long projectId, List<UserTinyVo> recipients, List<ProjectReportFile> reports,
                                           boolean withImagesZipLink) {
        String templateKey = "report_client_received";
        if(unsubscribeEmailService.isUnsubscribedByProject(templateKey, projectId)) {
            return;
        }
        logger.info("start to send :{} to project :{}", templateKey, projectId);
        List<String> reportLinks = reportLinks(reports);
        final Project project = projectMapper.getById(projectId);
        var specifiedRecipients = projectEmailSubscription.apply(project, templateKey);
        recipients.addAll(specifiedRecipients);
        final var insuredByName = Optional.ofNullable(project.getInsuranceCompany()).map(companyService::getById)
            .map(Company::getCompanyName).orElse("");

        var map = new HashMap<String, Object>();
            map.put("completedType", "Completed");
            map.put("insuredBy", insuredByName);
            map.put("policyNumber", project.getPolicyNumber());
            map.put("insuredName", project.getAssetOwnerName());
            map.put("projectId", project.getProjectId());
            map.put("address", project.getFullAddress());
            map.put("reportsViewLink", systemConfig.reportsViewLink(projectId));
            map.put("reportUrls", reportLinks);

        if (withImagesZipLink && StringUtils.isNotEmpty(project.getImagesArchiveUrl())) {
            String imagesZipLink = getImageZipLink(projectId,
                s3KeyManager.getFileNameFromImageArchiveFileKey(project.getImagesArchiveUrl()));
            map.put("imagesZipLink", imagesZipLink);
            map.put("imagesViewLink", systemConfig.imagesViewLink(projectId));
        }

        for (final UserTinyVo user : recipients) {
            if (StringUtils.isBlank(user.getEmail())) {
                continue;
            }
            map.put("username", user.getName());
            var variablesJson = gson.toJson(map);
            var mail = mailMessageFactory.create(List.of(user.getEmail()), templateKey, variablesJson, Map.of());
            doNotReplyMailSender.send(mail);
        }
    }

    private List<String> reportLinks(List<ProjectReportFile> reportFiles) {
        return reportFiles.stream().map(reportFile -> {
            int reportTypeCode = reportFile.getReportType();
            ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportTypeCode);
            return reportLink(reportFile.getProjectId(), reportFile.getReportId(),
                reportType.getDisplay(), reportType.getExtension());
        }).collect(Collectors.toList());
    }

    private void infoReportApprovedWithNewTemplate(long projectId, List<UserTinyVo> recipients, ReportTypeEnum reportType,
        ProjectReportFile report, boolean withImagesZipLink) {
        String templateKey = "report_approved";
        recipients = Lists.newArrayList(recipients);

        if (ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT.equals(reportType)) {
            // ignore image zip link because it's unnecessary for closeout report
            withImagesZipLink = false;
            templateKey = "report_generated";
        }

        if(unsubscribeEmailService.isUnsubscribedByProject(templateKey, projectId)) {
            log.info("Ignore to send email({}) for project {} since unsubscription.", templateKey, projectId);
            return;
        }
        final Project project = projectMapper.getById(projectId);
        logger.info("start to send :{} to project :{}", templateKey, projectId);
        var specifiedRecipients = projectEmailSubscription.apply(project, templateKey);
        logger.info("get recipient :{} ", specifiedRecipients);
        recipients.addAll(specifiedRecipients);

        if (CollectionUtils.isEmpty(recipients)) {
            log.info("Ignore to send email({}) for project {} since empty recipients.", templateKey, projectId);
            return;
        }

        final var reportUrl = reportLink(report.getProjectId(), report.getReportId(),
            reportType.getDisplay(), reportType.getExtension());
        final var completedType = ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT.equals(reportType)? "Close-Out": "Completed";
        final var insuredByName = Optional.ofNullable(project.getInsuranceCompany()).map(companyService::getById)
            .map(Company::getCompanyName).orElse("");

        var map = new HashMap<String, Object>();
        map.put("completedType", completedType);
        map.put("insuredBy", insuredByName);
        map.put("policyNumber", project.getPolicyNumber());
        map.put("insuredName", project.getAssetOwnerName());
        map.put("projectId", project.getProjectId());
        map.put("address", project.getFullAddress());
        map.put("reportName", reportType.getDisplay());
        map.put("reportsViewLink", systemConfig.reportsViewLink(projectId));
        map.put("reportUrl", reportUrl);

        if (withImagesZipLink && StringUtils.isNotEmpty(project.getImagesArchiveUrl())) {
            String imagesZipLink = getImageZipLink(projectId,
                s3KeyManager.getFileNameFromImageArchiveFileKey(project.getImagesArchiveUrl()));
            map.put("imagesZipLink", imagesZipLink);
            map.put("imagesViewLink", systemConfig.imagesViewLink(projectId));
        }

        for (final UserTinyVo user : recipients) {
            if (StringUtils.isBlank(user.getEmail())) {
                continue;
            }
            map.put("username", user.getName());
            var variablesJson = gson.toJson(map);
            var mail = mailMessageFactory.create(List.of(user.getEmail()), templateKey, variablesJson, Map.of());
            log.info("info member report approved {}, {}, {}", projectId, reportType.getCode(), user.getEmail());
            doNotReplyMailSender.send(mail);
        }
    }

    private void infoReportApprovedWithImageZip(long projectId, List<UserTinyVo> recipients, ReportTypeEnum reportType,
        ProjectReportFile report, boolean withImagesZipLink) throws ServiceException {
        if (recipients == null || recipients.isEmpty()) {
            return;
        }
        recipients = Lists.newArrayList(recipients);
        final Project project = projectMapper.getById(projectId);
        List<String> attachmentUrls = new ArrayList<>();

        CountDownLatch downloadLatch = new CountDownLatch(1);
        CountDownLatch sendMailLatch = new CountDownLatch(recipients.size());

        if (report != null) {
            final String reportKey = getReportKey(report, true);
            logger
                .debug("project " + projectId + ": try to send email to " + recipients.size() + " recipients, report: "
                    + reportKey + " withImagesZipLink: " + withImagesZipLink);
            // construct a single thread to download file and delete it.
            emailPool.execute(() -> {
//                int retry = 3;
//                String filePath = downloadReport(project, reportType, reportUrl, retry);
//                if (filePath != null && new File(filePath).exists()) {
//                    // 下载成功
//                    attachments.add(new File(filePath));
//                } else {
//                    logger.error("Fail to download report " + reportUrl + ". Current retry = " + retry);
//                }

                // for replacing attachments with links
                String suffix = getReportFileExtension(reportType);
                attachmentUrls
                    .add(reportLink(report.getProjectId(), report.getReportId(), reportType.getDisplay(), suffix));

                downloadLatch.countDown();
                try {
                    // wait send email task complete then delete the file 'filePath'
                    // the longest wait time is 6 hours
                    sendMailLatch.await(6, TimeUnit.HOURS);
                } catch (InterruptedException e) {
                    logger.warn("Deleting file thread is interrupted", e);
                } finally {
                    // 邮件发送完成之后，要删除下载下来的文件
//                    if (filePath != null) {
//                        SimpleFileUtil.removeLocalFile(filePath);
//                    }
                }
            });
        } else {
            logger.debug("project " + projectId + ": try to send email to " + recipients.size() +
                " recipients without reports");
            downloadLatch.countDown();
        }

        final String templateName = "reportApproved";
        if(unsubscribeEmailService.isUnsubscribedByProject(templateName, projectId)) {
            log.info("Ignore to send email {} for project {} since unsubscription.", templateName, projectId);
            return;
        }
        logger.info("start to send :{} to project :{}", templateName, projectId);
        var specifiedRecipients = projectEmailSubscription.apply(project, templateName);
        recipients.addAll(specifiedRecipients);
        Object[] subjectParams = new Object[]{reportType.getDisplay(), project.getProjectId()};
        String imagesZipLink = "";
        if (!StringUtils.isEmpty(project.getImagesArchiveUrl())) {
            imagesZipLink = getImageZipLink(projectId,
                s3KeyManager.getFileNameFromImageArchiveFileKey(project.getImagesArchiveUrl()));
        }

        for (final UserTinyVo user : recipients) {
            String email = user.getEmail();
            if (StringUtils.isNotBlank(email)) {
                Map<String, Object> model = new HashMap<>();
                model.put("name", user.getName());
                model.put("reportName", reportType.getDisplay());
                model.put("projectId", project.getProjectId());
                model.put("address", project.getFullAddress());
                model.put("withImagesZipLink", withImagesZipLink);
                model.put("imagesZipLink", imagesZipLink);

                // for replacing attachments with links
                model.put("reportsLink", attachmentUrls);
                model.put("reportsViewLink", systemConfig.reportsViewLink(projectId));
                model.put("imagesViewLink", systemConfig.imagesViewLink(projectId));

                emailPool.execute(() -> {
                    try {
                        downloadLatch.await();
                        logger.info("Try to send project %s email to %s with template reportApproved".formatted(
                            projectId, user.getEmail()));
                        springEmailSender.send(user.getEmail(), templateName, model, subjectParams, Map.of());
                        logger.info("Finish to send project %s email to %s with template reportApproved".formatted(
                            projectId, user.getEmail()));
                    } catch (RuntimeException e) {
                        logger.error("Fail to send email to info user " + user.getUserId()
                            + " that the reposts has been approved.", e);
                    } catch (InterruptedException e) {
                        logger.warn("Sending email thread is interrupted", e);
                    } finally {
                        sendMailLatch.countDown();
                    }
                });
            } else {
                sendMailLatch.countDown();
            }
        }

        // if the user is admin then send sms
        Object[] smsParams = new Object[]{reportType.getDisplay(), project.getProjectId(), project.getFullAddress()};
        String smsContent = smsTemplateFormat.getContent(templateName, smsParams);
        for (final UserTinyVo user : recipients) {
            sendSmsOnlyAdmin(user, smsContent);
        }
    }

    private String getReportFileExtension(ReportTypeEnum reportType) {
        String fileType = ".pdf";
        if (null != reportType && StringUtils.isNotEmpty(reportType.getContentType())) {
            String contentType = reportType.getContentType();
            fileType = "." + contentType.substring(contentType.indexOf("/") + 1);
        }
        return fileType;
    }

    private String getImageZipLink(long projectId, String fileName) {
        String imageZipLink = systemConfig.getOrigin();
        if (fileName != null) {
            fileName = StringUtils.replace(URIUtil.encodePath(fileName), "&", "%26");
            return imageZipLink + "download/images?projectId=" + projectId + "&fileName=" + fileName;
        } else {
            return imageZipLink + "download/images?projectId=" + projectId;
        }
    }

    private String reportLink(long projectId, String reportId, String reportName, String suffix) {
        String reportOrigin = systemConfig.getOrigin();
        return reportOrigin + "download/files?type=report&projectId=" + projectId + "&reportId=" + reportId
            + "&fileName=" + reportName + suffix;
    }

    @Override
    public void infoInviteAsVisitor(long projectId, long inviteeId, long userId) throws ServiceException {
        User user = userService.getUserById(userId);
        User invitee = userService.getUserById(inviteeId);
        Project project = projectService.getById(projectId);
        final String templateName = "inviteAsVisitor";
        String email = user.getEmail();
        if (StringUtils.isNotBlank(email)) {
            Object[] subjectParams = new Object[]{invitee.getName(), project.getProjectId()};
            Map<String, Object> model = new HashMap<>(4);
            model.put("name", invitee.getName());
            model.put("projectId", project.getProjectId());
            model.put("address", project.getFullAddress());
            asyncSendEmail(email, templateName, model, subjectParams);
        }
        // non admin user doesn't need to send sms
        String phone = user.getPhone();
        if (StringUtils.isNotBlank(phone) && user.hasRole(RoleEnum.ADMIN)) {
            Object[] smsParams = new Object[]{invitee.getName(), project.getProjectId(), project.getFullAddress()};
            String content = smsTemplateFormat.getContent(templateName, smsParams);
            asyncSendSms(phone, content);
        }
    }

    @Override
    public void infoReportSubmitted(long projectId,
        ReportTypeEnum reportType, UserTinyVo recipient) throws ServiceException {
        final Project project = projectService.getById(projectId);
        final String templateName = "reportSubmitted";
        Map<String, Object> model = new HashMap<>();
        model.put("name", recipient.getName());
        model.put("reportName", reportType.getDisplay());
        model.put("projectId", project.getProjectId());
        model.put("address", project.getFullAddress());
        model.put("serviceEmail", SERVICE_EMAIL);
        Object[] subjectParams = new Object[]{project.getProjectId()};
        Object[] smsParams = new Object[]{reportType.getDisplay(), project.getProjectId(), project.getFullAddress()};
        String content = smsTemplateFormat.getContent(templateName, smsParams);
        sendEmailSmsOnlyAdmin(recipient, templateName, model, subjectParams, content);
    }

    @Override
    public void infoReportDisapproved(long projectId,
        ReportTypeEnum reportType, UserTinyVo recipient) throws ServiceException {
        final Project project = projectService.getById(projectId);
        final String templateName = "reportDisapproved";
        Map<String, Object> model = new HashMap<>();
        model.put("name", recipient.getName());
        model.put("reportName", reportType.getDisplay());
        model.put("projectId", project.getProjectId());
        model.put("address", project.getFullAddress());
        model.put("serviceEmail", SERVICE_EMAIL);
        Object[] subjectParams = new Object[]{reportType.getDisplay(), project.getProjectId()};
        Object[] smsParams = new Object[]{reportType.getDisplay(), project.getProjectId(), project.getFullAddress()};
        String content = smsTemplateFormat.getContent(templateName, smsParams);
        sendEmailSmsOnlyAdmin(recipient, templateName, model, subjectParams, content);
    }

    @Override
    public void sendContactUS(ContactUs contactUs) throws ServiceException {
        final String firstName = contactUs.getFirstName();
        final String lastName = contactUs.getLastName();
        final String email = contactUs.getEmail();
        final String phone = contactUs.getPhone();
        final String company = contactUs.getCompany();
        final String content = contactUs.getContent();

        emailPool.execute(new Runnable() {
            @Override
            public void run() {
                contactUs.setContent(formatContent(contactUs.getContent()));

                Map<String, Object> model = new HashMap<String, Object>();
                model.put("contactUs", contactUs);
                try {
                    springEmailSender.send(CONTACT_US_RECIPIENTS, "contactUs", model,
                        new Object[]{firstName, lastName, email, phone, company});
                } catch (RuntimeException e) {
                    logger.error("Fail to send [CONTACT US] From " + firstName + " " + lastName + " "
                        + "(" + email + ", " + phone + ", " + company + ") with content :" + content, e);
                }
            }

            private String formatContent(String content) {
                return content == null ? null : content.replaceAll("(\r\n|\r|\n|\n\r)", "<br/>");
            }
        });
    }

    @Override
    public void infoMemberArranged(long projectId, UserTinyVo recipient, RoleEnum role)
        throws ServiceException {
        final Project project = projectService.getById(projectId);
        String templateName = "memberArranged";
        Map<String, Object> model = new HashMap<String, Object>();
        model.put("name", recipient.getName());
        model.put("role", role.getDisplay());
        model.put("projectId", project.getProjectId());
        model.put("address", project.getFullAddress());
        Object[] subjectParams = new Object[]{role.getDisplay(), project.getProjectId()};
        Object[] smsParams = new Object[]{role.getDisplay(), project.getProjectId(), project.getFullAddress()};
        String content = smsTemplateFormat.getContent(templateName, subjectParams);
        sendEmailSmsOnlyAdmin(recipient, templateName, model, smsParams, content);
    }

    @Override
    public void infoMemberArrangementCancel(Project project, UserTinyVo recipient, RoleEnum role)
        throws ServiceException {
        if (!checkCanSendPilotEmail(Collections.singletonList(recipient.getEmail()))) {
            return;
        }
        emailPool.execute(new Runnable() {

            @Override
            public void run() {

                String content = null;
                if (!StringUtils.isBlank(recipient.getEmail())) {
                    Map<String, Object> model = new HashMap<String, Object>();
                    model.put("name", recipient.getName());
                    model.put("projectId", project.getProjectId());
                    model.put("address", project.getFullAddress());
                    try {
                        springEmailSender.send(recipient.getEmail(), "memberArrangementCancel", model, null);
                    } catch (RuntimeException e) {
                        logger.error("Fail to send email to " + recipient.getEmail());
                    }
                } else {
                    // send sms message only when the user dosen't have a email
                    if (!StringUtils.isBlank(recipient.getPhone())) {
                        content = smsTemplateFormat.getContent("memberArrangementCancel",
                            new Object[]{project.getProjectId(), project.getFullAddress()});
                        sendSms(recipient.getPhone(), content);
                    }
                }
            }
        });
    }

    @Override
    public void infoProjectDeleted(Project project, UserTinyVo operator, UserTinyVo recipient)
        throws ServiceException {
        emailPool.execute(new Runnable() {
            @Override
            public void run() {
                String content = null;
                if (!StringUtils.isBlank(recipient.getEmail())) {
                    Map<String, Object> model = new HashMap<String, Object>();
                    model.put("recipientName", recipient.getName());
                    model.put("projectId", project.getProjectId());
                    model.put("address", project.getFullAddress());
                    model.put("operator", operator.getName());
                    try {
                        springEmailSender.send(recipient.getEmail(), "projectDeleted", model,
                            new Object[]{project.getProjectId(), project.getFullAddress(), operator.getName()});
                    } catch (RuntimeException e) {
                        logger.error("Fail to send email to " + recipient.getEmail());
                    }
                    return;
                }
                // send sms message only when the user dosen't have a email
                if (!StringUtils.isBlank(recipient.getPhone())) {
                    content = smsTemplateFormat.getContent("projectDeleted",
                        new Object[]{project.getProjectId(), project.getFullAddress(), operator.getName()});
                    sendSms(recipient.getPhone(), content);
                }
            }
        });
    }

    @Override
    public void infoPartnerProgram(PartnerProgram partnerProgram) throws ServiceException {
        final String firstName = partnerProgram.getFirstName();
        final String lastName = partnerProgram.getLastName();
        final String email = partnerProgram.getEmail();
        final String phone = partnerProgram.getPhone();
        final String company = partnerProgram.getCompany();

        final InspectionServiceEnum inspectionService = partnerProgram.getInspectionService() == null ? null :
            InspectionServiceEnum.getEnum(partnerProgram.getInspectionService());
        final HighflyServiceEnum highflyService = partnerProgram.getHighflyService() == null ? null :
            HighflyServiceEnum.getEnum(partnerProgram.getHighflyService());

        final String name = firstName + " " + lastName;

        emailPool.execute(new Runnable() {
            @Override
            public void run() {
                String inspectionServiceInfo = inspectionService == null ? "" :
                    inspectionService.getDisplay() + " (" + inspectionService.getDetail() + ")";
                String highflyServiceInfo = highflyService == null ? "" :
                    highflyService.getDisplay() + " (" + highflyService.getDetail() + ")";
                Map<String, Object> model = new HashMap<String, Object>();
                model.put("partnerProgram", partnerProgram);
                model.put("inspectionServiceInfo", inspectionServiceInfo);
                model.put("highflyServiceInfo", highflyServiceInfo);
                try {
                    springEmailSender.send(PARTNERPROGRAM_RECIPIENTS, "partnerProgram", model,
                        new Object[]{name, email, phone, company});
                } catch (RuntimeException e) {
                    logger.error(
                        "Fail to send [PARTNER PROGRAM] From " + name + " (" + email + ", " + phone + ", " + company,
                        e);
                }
            }
        });
    }

    @Override
    public void infoPartnerProgramRecieved(PartnerProgram partnerProgram) throws ServiceException {
        String firstName = partnerProgram.getFirstName() == null ? "" : partnerProgram.getFirstName();
        String lastName = partnerProgram.getLastName() == null ? "" : partnerProgram.getLastName();

        final String name = (firstName + " " + lastName).trim();
        final String email = partnerProgram.getEmail();

        emailPool.execute(new Runnable() {
            @Override
            public void run() {
                Map<String, Object> model = new HashMap<String, Object>();
                model.put("name", name);
                model.put("now", getNow());
                try {
                    springEmailSender.send(email, "partnerProgramRecieved", model, null);
                } catch (RuntimeException e) {
                    logger.error("Fail to send partner program reviced message to partner" + email, e);
                }
            }

            private String getNow() {
                Date date = new Date();
                String dateStr = "";
                try {
                    dateStr = DateUtil.convertDate(date, "MMMM dd, yyyy", TimeZone.getTimeZone("US/Central"));
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
                return dateStr;
            }
        });
    }

    @Override
    public void sendProjectCreated(User creator, Project project, List<String> recipients) {

        if (CollectionUtils.isEmpty(recipients)) {
            return;
        }

        if (mailProperties.getTemplateUsingNewSender().contains("project_created")) {
            Map<String, Object> variables = Maps.newHashMap();
            variables.put("creatorName", creator.getName());
            variables.put("creatorEmail", creator.getEmail());
            variables.put("projectId", project.getProjectId());
            variables.put("address", project.getFullAddress());

            var variablesJson = gson.toJson(variables);
            var mail = mailMessageFactory.create(recipients, "project_created", variablesJson, Map.of());
            clientMailSender.send(mail);
            return;
        }

        emailPool.execute(new Runnable() {
            @Override
            public void run() {

                if (!CollectionUtils.isEmpty(recipients)) {
                    Map<String, Object> model = new HashMap<String, Object>();
                    model.put("name", creator.getName());
                    model.put("email", creator.getEmail());
                    model.put("projectId", project.getProjectId());
                    model.put("address", project.getFullAddress());
                    try {
                        springEmailSender.send(recipients, "projectCreated", model,
                            new Object[]{creator.getName(), creator.getEmail()});
                    } catch (RuntimeException e) {
                        logger.error("Fail to send email to: {}", String.join(",", recipients), e);
                    }
                }
            }
        });
    }

    @Override
    public void sendAdToHouseOwner(Project project, User pilot) {
        if (!checkCanSendPilotEmail(Collections.singletonList(pilot.getEmail()))) {
            return;
        }
        final String template = "sendADToHouseOwner";

        String email = project.getAssetOwnerEmail();
        Long inspectionTime = project.getInspectionTime();
        if (StringUtils.isEmpty(email) || inspectionTime == null) {
            return;
        }
        String state = project.getState();
        String address;
        AmericaStateTimeZoneEnum stateEnum = AmericaStateTimeZoneEnum.getEnum(project.getState());
        if (stateEnum != null) {
            // get state full name
            state = stateEnum.getStateName();
        }
        address = project.getAddress() + ", " + project.getCity() + ", " + state + " " + project.getZipCode();
        Map<String, Object> model = new HashMap<>();
        model.put("houseOwner", project.getAssetOwnerName());
        model.put("claimNumber", project.getInspectionNumber());
        model.put("time", TimeUtil.formatToUSTime(inspectionTime, project.getState()));
        model.put("address", address);
        model.put("inspector", pilot.getFirstName() + " " + pilot.getLastName());
        try {
            springEmailSenderNoReply.send(email, template, model, new Object[]{});
        } catch (Exception e) {
            logger
                .error("Fail to send AD email [" + email + "] to house owner[" + project.getAssetOwnerName() + "]", e);
        }
    }

    /**
     * 发送邮件通知用户，如果用户是admin角色，则还需发送短信
     *
     * @param recipient     接收邮件的用户
     * @param template      邮件（短信）模板名称
     * @param templateModel 邮件模板内容
     * @param subjectParams 邮件主题参数
     * @param content       短信内容
     */
    private void sendEmailSmsOnlyAdmin(UserTinyVo recipient, String template, Map<String, Object> templateModel,
        Object[] subjectParams, String content) {
        String email = recipient.getEmail();
        if (StringUtils.isNotBlank(email)) {
            asyncSendEmail(email, template, templateModel, subjectParams);
        }

        sendSmsOnlyAdmin(recipient, content);
    }

    /**
     * 如果用户是admin角色，则给其发送短信
     *
     * @param recipient 接收短信的用户
     * @param content   短信内容
     */
    private void sendSmsOnlyAdmin(UserTinyVo recipient, String content) {
        if (Objects.isNull(recipient) || Objects.isNull(recipient.getRoleId())) {
            return;
        }
        RoleEnum roleEnum = RoleEnum.getEnum(recipient.getRoleId());
        String phone = recipient.getPhone();

        // if the user is not admin, then return
        if (roleEnum == null || !roleEnum.equals(RoleEnum.ADMIN) || StringUtils.isEmpty(phone)) {
            return;
        }

        asyncSendSms(phone, content);
    }

    private void asyncSendEmail(String email, String template, Map<String, Object> templateModel,
        Object[] subjectParams) {
        emailPool.execute(() -> {
            try {
                springEmailSender.send(email, template, templateModel, subjectParams);
            } catch (RuntimeException e) {
                logger.error("Fail to send email to " + email, e);
            }
        });
    }

    private void asyncSendSms(String phone, String content) {
        emailPool.execute(() -> {
            try {
                sendSms(phone, content);
            } catch (IllegalArgumentException e) {
                logger.error("Fail to send sms to " + phone, e);
            }
        });
    }

    private void sendSms(String phone, String content) {
       // do nothing
    }

    @Override
    public void sendToPilotArranged(UserTinyVo recipient, List<Project> projects, BeesPilotBatch pilotBatch) {
        if (recipient == null || !checkCanSendPilotEmail(Collections.singletonList(recipient.getEmail()))) {
            return;
        }
        emailPool.execute(new Runnable() {
            @Override
            public void run() {
                if (StringUtils.isEmpty(recipient.getEmail())) {
                    logger.info("User " + recipient.getUserId() + " dosen't have a email.");
                    return;
                }
                try {
                    String projectOrProjects = projects.size() > 1 ? "projects" : "project";

                    Map<String, Object> model = new HashMap<>();
                    model.put("name", recipient.getName());
                    model.put("projects", projects);
                    model.put("projectOrProjects", projectOrProjects);
                    model.put("batchNo", pilotBatch.getBatchNo());
                    BigDecimal totalPrice = pilotBatch.getBasePay();
                    if (Objects.nonNull(pilotBatch.getExtraPay())) {
                        totalPrice = totalPrice.add(pilotBatch.getExtraPay());
                    }
                    model.put("totalPay", totalPrice);
                    model.put("basePay", pilotBatch.getBasePay());
                    model.put("extraPay", pilotBatch.getExtraPay());

                    String planPayDate = null;
                    if (Objects.nonNull(pilotBatch.getPlanPaymentDate())) {
                        planPayDate = pilotBatch.getPlanPaymentDate().format(DateTimeFormatter.ofPattern("MM/dd"));
                    }
                    model.put("planPayDate", planPayDate);
                    model.put("note", pilotBatch.getNote());
//                    String dueDate = null;
//                    if (Objects.nonNull(pilotBatch.getDueDate())) {
//                        dueDate = pilotBatch.getDueDate().format(DateTimeFormatter.ofPattern("MM/dd"));
//                    }
                    String inspectionTime = null;
                    Optional<Long> optional = projects.stream().map(Project::getInspectionTime).filter(Objects::nonNull)
                        .max(Long::compareTo);
                    if (optional.isPresent()) {
                        inspectionTime = DateUtil
                            .convertDate(optional.get(), "MM/dd", ZoneId.of(CommonsUtil.TIME_ZONE_US_CENTER));
                    }
                    model.put("dueDate", inspectionTime);
                    Object[] subjectParams = new Object[]{projects.size(), projectOrProjects};

                    springEmailSender.send(recipient.getEmail(), "sendToPilotAssigned", model, subjectParams);
                } catch (RuntimeException e) {
                    logger.error("Fail to send email to " + recipient.getEmail(), e);
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("Fail to send email to " + recipient.getEmail(), e);
                }
            }
        });
    }

    @Override
    public void sendToPilotDailyNotify(UserTinyVo recipient, List<Project> projects) {
        if (recipient == null || !checkCanSendPilotEmail(Collections.singletonList(recipient.getEmail()))) {
            return;
        }
        emailPool.execute(new Runnable() {
            @Override
            public void run() {
                if (StringUtils.isEmpty(recipient.getEmail())) {
                    logger.info("User " + recipient.getUserId() + " dosen't have a email.");
                    return;
                }
                try {
                    Map<String, Object> model = new HashMap<>();
                    model.put("name", recipient.getName());
                    model.put("projects", projects);
                    String projectOrProjects = projects.size() > 1 ? "projects" : "project";

                    Object[] subjectParams = new Object[]{projects.size(), projectOrProjects};

                    springEmailSender.send(recipient.getEmail(), "sendToPilotDailyNotify", model, subjectParams);
                } catch (RuntimeException e) {
                    logger.error("Fail to send email to " + recipient.getEmail(), e);
                } catch (Exception e) {
                    logger.error("Fail to send email to " + recipient.getEmail(), e);
                }
            }
        });
    }

    @Override
    public void infoProjectReceiveError(Project project, String comment) {
        final String TEMPLATE_NAME = "projectReceiveError";

        var recipients = mailProperties.getTopicRecipients().get(TEMPLATE_NAME);
        if (recipients.isEmpty()) {
            log.info("Abort to send email {}, since the recipients is empty.", TEMPLATE_NAME);
            return;
        }

        var templateData = new JsonObject();
        templateData.addProperty("projectId", project.getProjectId());
        templateData.addProperty("comment", comment);

        var sendEmailJob = new SendEmailJob()
            .setTemplate(TEMPLATE_NAME)
            .setSender(EMAIL_SENDER_NO_REPLAY)
            .setRecipients(recipients)
            .setSubjectData(Lists.newArrayList(project.getProjectId() + "", comment))
            .setTemplateData(templateData);

        var job = RetryableJob.of(Job.ofPayload(sendEmailJob), 5, Duration.ofMinutes(5), null);
        log.info("Create job SendEmailJob to send email {} for project {}", TEMPLATE_NAME, project.getProjectId());
        jobScheduler.schedule(job);
    }

    @Override
    public void sendSmsToNotifyPilot(User user, String content) throws ServiceException {
        if (Objects.isNull(user) || !checkCanSendPilotEmail(Collections.singletonList(user.getEmail()))) {
            return;
        }
        if (StringUtils.isNotBlank(user.getPhone())) {
            sendSms(user.getPhone(), content);
        }
    }

    @Override
    public void infoAdminPilotFeedback(User pilot, UserTinyVo recipient, Long projectId, FirebaseFeedback feedback) {
        if (!checkCanSendPilotEmail(Collections.singletonList(recipient.getEmail()))) {
            return;
        }
        emailPool.execute(() -> {

            try {
                Map<String, Object> model = new HashMap<>();
                model.put("pilot", pilot.getName());
                model.put("content", feedback.getMessage());

                model.put("createAt", DateUtil.convertDate(
                    feedback.getCreatedTime().toDate().getTime(),
                    "HH:mm a z MM/dd/YYYY", ZoneId.of("US/Central")));

                Object[] subjectParams = new Object[]{pilot.getName(), projectId.toString()};

                springEmailSender.send(recipient.getEmail(), "pilotFeedback", model, subjectParams);

            } catch (RuntimeException e) {
                logger.error("Fail to send email to " + recipient.getEmail(), e);
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("Fail to send email to " + recipient.getEmail(), e);
            }


        });
    }

    private boolean checkCanSendPilotEmail(List<String> emails) {
        if (CollectionUtils.isEmpty(emails)) {
            return false;
        }
        BsExportData settingConfig = bsExportDataService
            .getByRelatedIdAndType(BsExportDataRelatedTypeEnum.BEES_PILOT_EMAIL_SWITCH.getType(),
                BsExportDataRelatedTypeEnum.BEES_PILOT_EMAIL_SWITCH.getDisplay());
        if (Objects.isNull(settingConfig)) {
            return false;
        }
        return StringUtils.equals(settingConfig.getDataLog(), "true");
    }

    @Override
    public void weekSendPilotTaskNeedPayedEmailToAdmin(List<String> adminEmails, String curDate, String startDate,
        String endDate, List<DailyBatchProjectModel> projectModelList, List<DailyBatchStatusModel> statusModelList) {
        if (!checkCanSendPilotEmail(adminEmails)) {
            return;
        }

        emailPool.execute(new Runnable() {

            @Override
            public void run() {
                Map<String, Object> model = new HashMap<String, Object>();
                model.put("projectModelList", projectModelList);
                model.put("statusModelList", statusModelList);
                Object[] subjectParams = new Object[]{curDate, startDate + "-" + endDate};

                try {
                    springEmailSender.send(adminEmails, "weekSendPilotTaskNeedPayedEmailToAdmin", model,
                        subjectParams);
                    logger.info("weekSendPilotTaskNeedPayedEmailToAdmin send end. emails:{}, curDate:{}", adminEmails,
                        curDate);
                } catch (RuntimeException e) {
                    logger.error("weekSendPilotTaskNeedPayedEmailToAdmin Fail to send email to " + adminEmails);
                }
            }
        });
    }

    @Override
    public void sendToAdminForHoverError(Collection<String> recipients, Long projectId,
        boolean hoverAuthorizationError, boolean hoverServerError) {
        emailPool.execute(() -> {

            String template = "sendForHoverError";
            Map<String, Object> model = new HashMap<>();
            model.put("projectId", projectId);
            if (hoverAuthorizationError) {
                template = "sendForHoverAuthorization";
            } else if (hoverServerError) {
                template = "sendForHoverContact";
            }
            try {
                springEmailSender.send(new ArrayList<>(recipients), template, model, null);
            } catch (RuntimeException e) {
                logger.error("Fail to send email '{}' to {}, projectId: {}", template, recipients, projectId, e);
            }
        });
    }

    @Override
    public void infoAdminRoleWorkWeeklyCount(String startDate, String endDate,
                                             List<ProjectRoleWorkServiceOuterClass.ProjectWorkItem> claimList,
                                             List<ProjectRoleWorkServiceOuterClass.ProjectWorkItem> underwritingList) {

        List<String> adminEmails = bsExportDataService.getExportData(null, BsExportDataRelatedTypeEnum.WEEK_NOTIFY_ADMIN_ROLE_WORK_EMAIL.getType())
            .stream().map(BsExportData::getDataLog).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adminEmails)) {
            return;
        }

        Object[] subjectParams = new Object[]{startDate + " to " + endDate};
        emailPool.execute(() -> {
            Map<String, Object> model = new HashMap<>();
            model.put("startDate", startDate);
            model.put("endDate", endDate);
            model.put("claimList", claimList);
            model.put("underwritingList", underwritingList);
            try {
                springEmailSenderNoReply.send(adminEmails, "infoAdminRoleWorkWeeklyCount", model, subjectParams);
            } catch (RuntimeException e) {
                logger.error("Fail to send email 'infoAdminRoleWorkWeeklyCount' to {}, startDate: {}", adminEmails,
                    startDate, e);
            }
        });
    }

    @Override
    public void sendProjectStatisticsPerCompany
            (Map<InspectionPurposeTypeEnum, ProjectStatistics.ProjectStatsPerCompanySummary> summaries, LocalDate statisticsDate) {
        List<String> statisticsEmailRecipient = bees360WatcherProperties.getStatistics();
        final com.bees360.entity.SystemConfig config = systemConfigMapper.getOne("email.recipients.company-stats");
        if (config != null && StringUtils.isNotBlank(config.getConfigValue())) {
            final String[] ls = config.getConfigValue().split(",");
            statisticsEmailRecipient.addAll(Arrays.asList(ls));
        }
        final List<String> recipients = statisticsEmailRecipient.stream().filter(StringUtils::isNotBlank).distinct()
            .collect(Collectors.toList());
        if (recipients.isEmpty()) {
            log.info("Abort to send email since recipients is empty.");
            return;
        }
        ProjectStatistics.ProjectStatsPerCompanySummary claimSummary = summaries.get(InspectionPurposeTypeEnum.CLAIM);
        ProjectStatistics.ProjectStatsPerCompanySummary underWritingSummary =
            summaries.get(InspectionPurposeTypeEnum.UNDERWRITING);

        final HashMap<String, Object> model = new HashMap<>();
        model.put("claimSummary", claimSummary);
        model.put("underWritingSummary", underWritingSummary);

        emailPool.execute(() -> {
            final String dateStr = dateTimeFormatter.format(statisticsDate);
            model.put("dateStr", dateStr);
            springEmailSenderBackend.oneShotSend(recipients, "ProjectStatusPerCompany", model, new Object[]{dateStr});
        });
    }

    @Override
    public void sendEmailNoticeOnEmergencyCase(String content, Project project) {
        final String TEMPLATE_NAME = "emergencyCaseNotice";
        var recipients = mailProperties.getTopicRecipients().get(TEMPLATE_NAME);
        if (recipients.isEmpty()) {
            log.info("Abort to send email {}, since the recipients is empty.", TEMPLATE_NAME);
            return;
        }
        var templateData = new JsonObject();
        templateData.addProperty("claimNumber", project.getInspectionNumber());
        templateData.addProperty("content", content);
        templateData.addProperty("address", project.getFullAddress());

        var sendEmailJob =
                new SendEmailJob()
                        .setTemplate(TEMPLATE_NAME)
                        .setSender(EMAIL_SENDER_BACKEND)
                        .setRecipients(recipients)
                        .setSubjectData(
                                Lists.newArrayList(
                                        project.getInspectionNumber(),
                                        String.valueOf(project.getProjectId())))
                        .setTemplateData(templateData);

        var job = RetryableJob.of(Job.ofPayload(sendEmailJob), 5, Duration.ofMinutes(5), null);
        log.info(
                "Create job SendEmailJob to send email {} for project {}",
                TEMPLATE_NAME,
                project.getProjectId());
        jobScheduler.schedule(job);
    }
}
