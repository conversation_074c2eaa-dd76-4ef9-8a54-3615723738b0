package com.bees360.service.util;

import com.bees360.activity.ActivityManager;
import com.bees360.activity.ActivityQuery;
import com.bees360.activity.Comment;
import com.bees360.entity.enums.ProjectLabelEnum;

import lombok.Data;

import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiPredicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Configuration
@Log4j2
public class ProjectOperationTagConfig {

    @Data
    static class OperationTagProperty {
        private ProjectLabelEnum operationTag;
        private Pattern projectOperationTagFilterRegex;
    }

    @Bean
    @ConfigurationProperties(prefix = "project.operation-tag.checker.property")
    public List<OperationTagProperty> projectOperationTagProperty() {
        return new ArrayList<>();
    }

    @Bean
    @ConditionalOnProperty(
            prefix = "project.operation-tag.checker",
            name = "disabled",
            havingValue = "false")
    public BiPredicate<Long, Long> projectOperationTagAddedChecker(
            ActivityManager activityManager, List<OperationTagProperty> projectOperationTagProperty) {
        log.info("Get project operation check from property :{}", projectOperationTagProperty);
        return (projectId, operationTagId) -> {
            var filter =
                    projectOperationTagProperty.stream()
                            .filter(
                                    property ->
                                            Objects.equals(
                                                    property.getOperationTag().getLabelId(),
                                                    operationTagId))
                            .findFirst()
                            .map(OperationTagProperty::getProjectOperationTagFilterRegex)
                            .orElse(null);
            if (filter == null) {
                return true;
            }
            var contents =
                    activityManager
                            .getActivities(ActivityQuery.builder().projectId(projectId).build())
                            .stream()
                            .map(
                                    a ->
                                            Optional.ofNullable(a.getComment())
                                                    .map(Comment::getContent)
                                                    .orElse(null))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
            return contents.stream()
                            .filter(content -> filter.matcher(content).matches())
                            .findAny()
                            .orElse(null)
                    != null;
        };
    }
}
