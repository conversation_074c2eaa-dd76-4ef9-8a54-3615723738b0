package com.bees360.service;

import com.bees360.base.exception.ServiceMessageException;
import com.bees360.entity.BsExportData;

import java.util.List;
import java.util.Optional;

public interface BsExportDataService {

	List<BsExportData> getExportData(String relatedId, String type);

    BsExportData insertOrUpdateData(BsExportData param) throws ServiceMessageException;

    void updateExportData(BsExportData param) throws ServiceMessageException;

    void deleteExportData(long logId) throws ServiceMessageException;

    BsExportData getByRelatedIdAndType(String relatedId, String type);
}
