package com.bees360.job;

import com.bees360.job.registry.SerializableFirebaseProject;
import com.bees360.job.registry.SerializableFirebaseProjectV2;
import com.bees360.job.util.AbstractAsyncJobExecutor;
import com.bees360.service.firebase.FirebaseProjectService;
import com.google.cloud.firestore.Firestore;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;

import lombok.ToString;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.beanutils.BeanUtils;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.concurrent.Executors;

import static com.bees360.service.util.FirebaseExceptionTranslation.translateExceptionAndThrow;

@Log4j2
@ToString
public class FirebaseProjectChangedExecutorV2
        extends AbstractAsyncJobExecutor<SerializableFirebaseProjectV2>
        implements AsyncJobExecutor {
    private final FirebaseProjectService firebaseProjectService;
    private final Firestore firestore;
    private final ListeningExecutorService executor;

    public FirebaseProjectChangedExecutorV2(
            FirebaseProjectService firebaseMissionService, Firestore firestore) {
        this.firebaseProjectService = firebaseMissionService;
        this.firestore = firestore;
        this.executor =
                MoreExecutors.listeningDecorator(
                        Executors.newFixedThreadPool(
                                2 * Runtime.getRuntime().availableProcessors()));
        log.info("Created '{}'", this);
    }

    @Override
    protected ListenableFuture<Void> accept(SerializableFirebaseProjectV2 project) {
        return executor.submit(() -> run(project));
    }

    private Void run(SerializableFirebaseProjectV2 projectV2) throws IOException {
        SerializableFirebaseProject project;
        try{
            project = convert(projectV2);
        } catch (IllegalArgumentException e){
            log.error("Failed to handle project '{}'.", projectV2, e);
            return null;
        }

        try {
            log.info("Start to handle project '{}' '{}'", project.getId(), project);
            firebaseProjectService.handleFirebaseProject(project, project.getId());
            log.info("Successfully handle project '{}'", project.getId());
        } catch (RuntimeException e) {
            log.error("Failed to handle project '{}'", project, e);
            translateExceptionAndThrow(e);
        }
        return null;
    }

    private SerializableFirebaseProject convert(SerializableFirebaseProjectV2 projectV2) throws IllegalArgumentException {
        SerializableFirebaseProject project = new SerializableFirebaseProject();
        try {
            BeanUtils.copyProperties(project, projectV2);
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to convert SerializableFirebaseProjectV2 to SerializableFirebaseProject.", e);
        }
        return project;
    }
}
