package com.bees360.service.stat;

import com.bees360.entity.stat.enums.ProjectStatType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@RequiredArgsConstructor
public class StatProjectServiceFactory {

    private final Map<ProjectStatType, StatComplexService> listServiceMap = new ConcurrentHashMap<>();

    private final List<StatComplexService> listServices;

    @PostConstruct
    public void init(){
        register();
    }

    private void register(){

        for (StatComplexService listService : listServices){
            listServiceMap.put(listService.getType(), listService);
        }
    }

    public StatComplexService getStatComplexService(ProjectStatType type){
        return listServiceMap.get(type);
    }
}
