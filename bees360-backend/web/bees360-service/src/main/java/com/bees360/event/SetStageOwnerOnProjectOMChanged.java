package com.bees360.event;

import com.bees360.base.Constants;
import com.bees360.event.registry.ProjectMemberChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.PipelineService;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.util.Strings;

import java.io.IOException;
import java.util.Optional;

/**
 * 监听项目成员变更事件，当操作用户为运营经理时，设置指定管道的检查阶段负责人为用户ID。
 */
@Log4j2
public class SetStageOwnerOnProjectOMChanged
        extends AbstractNamedEventListener<ProjectMemberChangedEvent> {
    private final PipelineService pipelineService;
    private static final int INSPECTION_STAGE = 20;

    public SetStageOwnerOnProjectOMChanged(PipelineService pipelineService) {
        this.pipelineService = pipelineService;
        log.info("Created {}(pipelineService={})", this, this.pipelineService);
    }

    @Override
    public void handle(ProjectMemberChangedEvent event) throws IOException {
        var auth = event.getAuth();
        var userId = Optional.ofNullable(event.getUserTo()).orElse(Strings.EMPTY);
        if (!Constants.AUTH_OPERATIONS_MANAGER.equals(auth)) {
            return;
        }

        var pipelineId = event.getProjectId();
        var pipeline = pipelineService.findById(pipelineId);
        if (pipeline == null) {
            return;
        }

        pipelineService.setStageOwner(pipelineId, INSPECTION_STAGE, userId);
        log.info(
                "Successfully set pipeline '{}' stage '{}' owner to {}.",
                pipelineId,
                INSPECTION_STAGE,
                userId);
    }
}
