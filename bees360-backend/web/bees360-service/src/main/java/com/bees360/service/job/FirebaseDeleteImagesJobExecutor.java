package com.bees360.service.job;

import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.job.Job;
import com.bees360.job.registry.ImageDeleteJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.service.firebase.FirebaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class FirebaseDeleteImagesJobExecutor extends AbstractJobExecutor<ImageDeleteJob> {

    @Autowired private FirebaseService firebaseService;

    @Autowired private ProjectImageMapper projectImageMapper;

    @Override
    protected void handle(ImageDeleteJob imageDeleteJob) throws IOException {
        long projectId = imageDeleteJob.getProjectId();
        List<String> imageIds = imageDeleteJob.getImageIds();
        log.info(
                "Delete firebase images:{} of project {}.", ListUtil.toString(imageIds), projectId);

        List<String> originalFileNames =
                projectImageMapper.listOriginalFileNameIncludeCompleteDeleted(
                        projectId,
                        imageIds,
                        Arrays.asList(
                                FileSourceTypeEnum.DRONE_IMAGE.getCode(),
                                FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode()));
        if (CollectionAssistant.isEmpty(originalFileNames)) {
            return;
        }
        firebaseService.deleteFirebaseImagesByOriginalFileNames(projectId, originalFileNames);
    }
}
