package com.bees360.service.util;

import com.bees360.entity.enums.ProjectStateChangeReasonEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

import static com.bees360.entity.enums.ProjectStateChangeReasonEnum.getChangeReasonByDisplayText;
import static com.bees360.entity.enums.ProjectStateChangeReasonEnum.getByOpTagId;

public class ProjectStateChangeReasonUtil {
    public static String getChangeReasonByOperationTagId(Long operationTagId) {
        return Optional.ofNullable(getByOpTagId(operationTagId))
                .map(ProjectStateChangeReasonEnum::getChangeReasonKey)
                .orElse(null);
    }

    public static String getCloseOutReasonByChangeReason(String changeReason) {
        var stateChangeReasonEnum = getChangeReasonByDisplayText(changeReason);
        return Optional.ofNullable(stateChangeReasonEnum)
                .map(ProjectStateChangeReasonEnum::getReasonInCloseOutReport)
                .orElse(changeReason);
    }

    public static String getCloseOutReasonByOperationTagId(Long operationTagId) {
        var stateChangeReasonEnum = getByOpTagId(operationTagId);
        return Optional.ofNullable(stateChangeReasonEnum)
                .map(ProjectStateChangeReasonEnum::getReasonInCloseOutReport)
                .orElse(StringUtils.EMPTY);
    }
}
