package com.bees360.service.properties;

import com.bees360.entity.enums.RoleEnum;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static com.bees360.entity.enums.RoleEnum.REVIEWER;
import static com.bees360.entity.enums.RoleEnum.DESK_ADJUSTER;
import static com.bees360.entity.enums.RoleEnum.UNDERWRITER;

@Component
@Log4j2
@Configuration
public class AssignProjectMemberConfig {

    @Data
    @ConfigurationProperties(prefix = "bees360.company-config.role.company-admin")
    @EnableConfigurationProperties
    static class Properties {
        /**
         * default company admin assignable roles: REVIEWER, DESK_ADJUSTER, UNDERWRITER
         */
        private List<Integer> assignableRole = List.of(REVIEWER.getRoleId(), DESK_ADJUSTER.getRoleId(), UNDERWRITER.getRoleId());
    }

    @Bean("CompanyAdminAssignableMemberRole")
    public List<RoleEnum> companyAdminAssignableMemberRole(Properties properties) {
        return properties.assignableRole.stream().map(RoleEnum::getEnum).collect(Collectors.toList());
    }
}
