package com.bees360.service;

import java.util.List;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Member;

import jakarta.annotation.Nullable;

public interface MemberService {

	/**
	 * get involved project for current userId
	 * @param userId
	 * @return
	 * @throws ServiceException
	 */
	List<Member> listUserRolesInProject(long projectId, long userId)throws ServiceException;

    List<Member> listActiveUserInProjectsWithRoles(List<Long> projectIds, int roleId);

    Member getActiveMemberByRole(long projectId, int role);

    Member getActivePilot(long projectId);

    Member getMemberByRoleAndUserId(long projectId, long userId, int role);

    void saveOperationsManager(long projectId, String userId, String createdBy, @Nullable Long version);
}
