package com.bees360.service;

import java.util.List;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Notification;
import com.bees360.entity.Project;
import com.bees360.entity.User;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.PageResult;
import com.bees360.entity.vo.UserTinyVo;

public interface NotificationService {

	void notifyImageUploaded(long projectId, long userId) throws ServiceException;

	void notifyReportApproved(long projectId, long userId, ReportTypeEnum reportType) throws ServiceException;

	void notifyRoleApplicationResult(long userId, int roleId, boolean isPassed, String comment) throws ServiceException;

	/* ------------ [start] workflow control ---------------- */
	public void allowRanging(long projectId, long userId) throws ServiceException;

	public void allowScoping(long projectId, long userId) throws ServiceException;

	public void allowPlane(long projectId, long userId) throws ServiceException;

	public void allowBoundary(long projectId, long userId) throws ServiceException;

	public void allowDamage(long projectId, long userId) throws ServiceException;
	/* ------------ [end] workflow control ---------------- */

	/**
	 * @param userId	The notification belong to the user whose id is userId.
	 * @param isRead	If isRead is ture, get the notification read.
	 * 					If isRead is false, get the notification not read.
	 * 					If isRead is null,get all notification.
	 * @return	A list of Notification.
	 * @throws ServiceException
	 */
	public PageResult<Notification> listNotifications(long userId, int pageIndex, int pageSize) throws ServiceException;

	public void setAllNotificationRead(long userId) throws ServiceException;

	public void setOneNotificationRead(long userId, long notificationId) throws ServiceException;

	void notifyReportSubmitted(long projectId, ReportTypeEnum reportType, UserTinyVo reviewer) throws ServiceException;

	void notifyReportDisapproved(long projectId, ReportTypeEnum reportType, UserTinyVo reviewer) throws ServiceException;

	void notifyAdminRealtimeReportGenerated(long projectId, List<UserTinyVo> admins) throws ServiceException;

	void notifyProcessorRealtimeReportGenerated(long projectId, UserTinyVo processor) throws ServiceException;

	void notifyMemberArranged(long projectId, UserTinyVo tinyProcessor, RoleEnum reviewer) throws ServiceException;

	void notifyMemberArrangementCancel(Project project, UserTinyVo worker, RoleEnum role) throws ServiceException;

	void notifyProjectDeleted(Project project, UserTinyVo operator, UserTinyVo recipient) throws ServiceException;

	void notifyRoleLosed(long userId, List<RoleEnum> roles) throws ServiceException;

    void infoMemberArranged(UserTinyVo user, RoleEnum role, List<Project> projects) throws ServiceException;

    void infoMemberInvited(UserTinyVo user, List<Project> projects) throws ServiceException;
}
