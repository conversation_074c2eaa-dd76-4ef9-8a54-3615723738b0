package com.bees360.service.listener.firebase;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.BeesPilotBatch;
import com.bees360.entity.BeesPilotBatchItem;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.User;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.PilotBatchEventTypeEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.firebase.BatchStatusEnum;
import com.bees360.entity.firebase.FirebaseProjectStatusEnum;
import com.bees360.mapper.MemberMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.pipeline.PipelineDefService;
import com.bees360.pipeline.PipelineService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.beespilot.BeesPilotBatchItemService;
import com.bees360.service.beespilot.BeesPilotBatchService;
import com.bees360.service.event.pilot.PilotBatchChangeEvent;
import com.bees360.service.firebase.FirebasePilotService;
import com.bees360.service.firebase.FirebaseProjectService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.AssertUtil;
import com.bees360.web.event.account.PilotAuthorityRemovedEvent;
import com.bees360.web.event.account.UserApplicationRejectedEvent;
import com.bees360.web.event.account.UserBannedEvent;
import com.bees360.web.event.account.UserEvent;
import com.bees360.web.event.beespilot.NewFirebaseProjectRelatedInfoChangedEvent;
import com.bees360.web.event.project.ProjectChangeEvent;
import com.bees360.web.event.project.ProjectCommentAddedEvent;
import com.bees360.web.event.project.ProjectCreatedEvent;
import com.bees360.web.event.project.ProjectLatestStatusEvent;
import com.bees360.web.event.project.ProjectReportFileUpdatedEvent;
import com.bees360.web.event.project.ProjectStatusEvent;
import com.bees360.web.event.project.ProjectStatusProjectCreatedEvent;
import com.bees360.web.event.project.ProjectStatusReturnedToClientEvent;
import com.bees360.web.event.project.claim.ProjectClaimCustomerQuizAddedEvent;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2020/8/25 7:10 下午
 **/
@Slf4j
@Component
public class FirebaseServiceListener {

    @Resource
    private FirebaseProjectService firebaseProjectService;
    @Resource
    private FirebaseService firebaseService;
    @Resource
    private MemberMapper memberMapper;
    @Resource
    private BeesPilotBatchItemService beesPilotBatchItemService;
    @Resource
    private BeesPilotBatchService beesPilotBatchService;

    @Autowired private ProjectService projectService;

    @Resource
    private ProjectStatusService projectStatusService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private FirebasePilotService firebasePilotService;
    @Autowired
    private PipelineService pipelineService;
    @Autowired
    private PipelineDefService pipelineDefService;
    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private Bees360FeatureSwitch featureSwitch;

    // ####### bees360 web project数据变更 #######
    /**
     * 当项目创建时，同步项目到firebase
     * @param event
     * @throws ServiceException
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void addProjectToFirebase(ProjectCreatedEvent event) throws ServiceException {
        Project project = event.getProject();
        AssertUtil.notNull(project, "project is null");
        try {
            firebaseProjectService.initFirebaseProject(project);
            log.info("Success init firebase project '{}'", project.getProjectId());
        } catch (Exception e) {
            String message = "Failed init firebase project '%s'".formatted(project.getProjectId());
            log.error(message, e);
        }
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void syncFirebaseOnReportGeneratedEvent(ProjectReportFileUpdatedEvent event) {
        if (featureSwitch.isEnableSyncSurveyCompleted()) {
            return;
        }

        ProjectReportFile reportFile = event.getReport();
        if (Objects.equals(reportFile.getReportType(), ReportTypeEnum.HOMEOWNER_SURVEY.getCode())) {
            firebaseProjectService.syncSurveyCompletedToFirebase(event.getProject().getProjectId());
        }
    }

    @EventListener
    public void syncServiceCompletedOnReturnToClient(ProjectStatusReturnedToClientEvent event)
            throws ServiceException {
        Project project = event.getProject();
        long projectId = project.getProjectId();
        if (projectService.projectServiceIsCompleted(projectId)) {
            firebaseProjectService.syncServiceCompletedToFirebase(projectId);
        }
    }

    /**
     * 更新Firebase Project的related的信息，如 pilot, contact_time, tag_id
     * @param event
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void firebaseProjectRelatedInfoChangedEvent(NewFirebaseProjectRelatedInfoChangedEvent event) throws ServiceException {
        long projectId = event.getProjectId();
        var project = projectService.findById(projectId);
        if (project.isEmpty()) {
            return;
        }
        try {
            firebaseProjectService.updateFirebaseProjectRelateInfo(projectId);
            log.info("Success update project '{}' related info into firebase.", projectId);
        } catch (RuntimeException e) {
            log.error("Failed to update project '{}' related info into firebase.", projectId, e);
        }
    }

    /**
     * 当项目信息改变时，同步项目信息到firebase （包括project表和 mission表里的project字段）
     * 更新Firebase Project
     * @param event 项目信息改变事件
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void projectChange(ProjectChangeEvent event) throws ServiceException {
        Project project = event.getProject();
        long projectId = project.getProjectId();
        try {
            if (!event.isChangedFromFirebase()) {
                firebaseProjectService.updateFirebaseProjectBaseInfo(projectId);
            }
            log.info("Success update project '{}' to firebase.", projectId);
        } catch (Exception e) {
            log.error("Failed to update project '{}' to firebase.", projectId, e);
        }
    }
    /**
     * 当项目信息改变时，同步项目信息到firebase （包括project表和 mission表里的project字段）
     * 更新Firebase Project
     * @param event 项目信息改变事件
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void projectLatestStatusChanged(ProjectLatestStatusEvent event) {
        Project project = event.getProject();
        try {
            firebaseProjectService.updateFirebaseProjectLatestStatus(project);
            log.info(
                    "Successfully update project '{}' latest status '{}' to firebase.",
                    project.getProjectId(),
                    project.getLatestStatus());
        } catch (Exception e) {
            log.error("Failed to update project '{}' latest status {}.",   project.getProjectId(),
                project.getLatestStatus(), e);
        }
    }

    /**
     * bees360 web端项目状态更改，并同步到firebase project表中
     * @param event 项目状态变更事件
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void projectStatusChange(ProjectStatusEvent event) {
        long projectId = event.getProject().getProjectId();
        int status = event.getStatus().getStatus();
        try {
            if (event instanceof ProjectStatusProjectCreatedEvent createdEvent) {
                if (!createdEvent.isHasRollback()) {
                    return;
                }
            }

            FirebaseProjectStatusEnum firebaseProjectStatusEnum =
                FirebaseProjectStatusEnum.getEnum(status);
            firebaseProjectService.updateFirebaseProjectStatus(
                event.getProject(), firebaseProjectStatusEnum);
        } catch (Exception e) {
            log.error("Failed to update project '{}' status '{}' to firebase", projectId, status,  e);
        }
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void projectNoteChanged(ProjectCommentAddedEvent event) {
        Long projectId = null;
        try {
            projectId = event.getProject().getProjectId();
            firebaseService.updateNoteToFirebase(projectId);
        } catch (RuntimeException e) {
            log.error("Failed to update note to project '{}'", projectId, e);
        }
    }


    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void initLossDescription(ProjectCommentAddedEvent event) {
        Long projectId = null;
        try {
            projectId = event.getProject().getProjectId();
            log.info("Start to init lossDescription for project '{}' on comment {} added.", projectId, event.getComment().getContent());
            firebaseProjectService.initLossDescription(event.getProject(), event.getComment());
        } catch (RuntimeException e) {
            log.error("Failed to init lossDescription for project '{}'", projectId, e);
        }
    }

    @Async
    @EventListener
    public void publishProjectClaimCustomerQuizAddedEvent(ProjectCommentAddedEvent event) {
        var comment = event.getComment();
        var content = comment.getContent();
        var projectId = event.getProject().getProjectId();
        var source = comment.getSource();
        if (!Objects.equals(source, ActivitySourceEnum.BEESPILOT_QUIZ.getValue())) {
            return;
        }

        publisher.publishEvent(
                new ProjectClaimCustomerQuizAddedEvent(
                        this, projectService.getById(projectId), content));
        log.info(
                "Successfully publish ProjectClaimCustomerQuizAddedEvent on project '{}' comment '{}' added.",
                projectId,
                content);
    }

    // ####### bees360 web 飞手数据变更 #######
    /**
     * 同步飞手数据到firebase
     * @param event
     */
    @EventListener
    public void syncPilot(UserEvent event) {
        User user = event.getUser();
        log.info("Start to sync pilot '{}' into firebase.", user.getUserId());
        if (event instanceof PilotAuthorityRemovedEvent ||
            event instanceof UserBannedEvent ||
            event instanceof UserApplicationRejectedEvent) {
            firebasePilotService.syncPilotRemoval(user);
        }
        try {
            firebasePilotService.syncPilot(user);
            log.info("Success to sync pilot '{}' into firebase.", user.getUserId());
        } catch (Exception e) {
            String message = "Failed to sync pilot '%s' to firebase.".formatted(user.getUserId());
            log.error(message, e);
        }

    }

    // ####### bees360 web batch数据变更 #######
    /**
     * web端分配batch, 修改batch,以及删除batch后同步到firebase
     * // TODO 删除mission的逻辑要去掉
     * @param event batch修改事件
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void syncToFirebaseOnStatusChangeEvent(PilotBatchChangeEvent event) throws ServiceException {
        String batchNo = null;
        try {
            batchNo = event.getBatch().getBatchNo();
            List<Long> projectIds = event.getProjectIds();
            if (Objects.isNull(event.getBatch()) || CollectionUtils.isEmpty(projectIds)) {
                return;
            }
            AssertUtil.notNull(batchNo, "Batch number should not be null. Event is "+ event.toString());
            log.info("syncToFirebaseOnStatusChangeEvent pilotBatch:{}, typeEnum:{}", event.getBatch(), event.getTypeEnum());
            for (Long projectId : projectIds) {
                if (Objects.equals(event.getTypeEnum(), PilotBatchEventTypeEnum.DELETED)) {
                    try {
                        BeesPilotBatch batch = beesPilotBatchService.getByBatchNoWithoutDeletedCheck(batchNo);
                        if (batch != null
                                && batch.getStatus() == BatchStatusEnum.PENDING.getStatus()) {
                            List<Long> projects =
                                    beesPilotBatchItemService.listByBatchNo(batchNo).stream()
                                        .map(BeesPilotBatchItem::getProjectId).collect(Collectors.toList());
                            for (Long curProjectId : projects) {
                                if (!Objects.equals(projectId, curProjectId)) {
                                    beesPilotBatchService.cancelAssignedAndRemovePilot(User.BEES_PILOT_SYSTEM, curProjectId);
                                }
                            }
                        }
                        // 删除项目batch的子单
                        beesPilotBatchItemService.deleteByProjectIds(Collections.singletonList(projectId));
                        log.info("BeesPilot mission '{}' canceled success.", firebaseService.getMissionDocumentKey(projectId, batchNo));
                    } catch (Exception e) {
                        log.error("BeesPilot mission '{}' canceled failed.", firebaseService.getMissionDocumentKey(projectId, batchNo), e);
                        throw e;
                    }
                }
            }
        } catch (Exception e) {
            String message = "Create or update batch %s and mission failed on event %s".formatted(batchNo, event);
            log.error(message, e);
            // 异步线程处理器捕获异常后发送邮件
            throw new ServiceException(message, e);
        }
        log.info("syncToFirebaseOnStatusChangeEvent end pilotBatch:{}, typeEnum:{} .", event.getBatch(), event.getTypeEnum());
    }
}
