package com.bees360.service.stat;

import com.bees360.base.exception.ServiceException;
import com.bees360.common.util.DateUtil;
import com.bees360.entity.CompanyIDMap;
import com.bees360.entity.User;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.entity.stat.enums.ProjectStatType;
import com.bees360.entity.stat.search.StatFullSearchOption;
import com.bees360.entity.stat.vo.StatProjectCardVo;
import com.bees360.entity.stat.vo.StatProjectVo;
import com.bees360.entity.stat.vo.StatComplexVo;
import com.bees360.entity.stat.vo.card.StatCardVo;
import com.bees360.entity.stat.vo.chart.StatChartVo;
import com.bees360.entity.stat.vo.list.PagedResultVo;
import com.bees360.entity.stat.vo.list.ProjectListVo;
import com.bees360.project.Message;
import com.bees360.project.ProjectDaysOldProvider;
import com.bees360.project.ProjectDaysOldQuery;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.UserService;

import com.bees360.util.DateTimes;
import com.bees360.util.Iterables;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bees360.entity.stat.enums.ProjectStatType.PROJECT_CREATED;
import static com.bees360.util.Functions.acceptIfNotNull;

@Service
@Log4j2
public class ProjectStatServiceImpl implements ProjectStatService {

    @Autowired private UserService userService;

    @Autowired private ProjectLabelService projectLabelService;

    @Autowired private StatProjectServiceFactory statProjectServiceFactory;

    @Autowired private CompanyIDMap companyIDMap;

    @Autowired private ProjectDaysOldProvider projectDaysOldProvider;

    @Autowired
    private Map<ProjectStatType, BiConsumer<StatProjectCardVo, StatCardVo>> updateProjectCardByType;

    @Override
    public StatComplexVo statProjectComplex(long userId, StatFullSearchOption fullSearchOption)
            throws ServiceException {
        fullSearchOption.setCenterUtcOffset(
                DateUtil.getOffset(ZoneId.of("US/Central"), ZoneId.of("UTC")));

        log.info("Received stat full searchOptions :{}", fullSearchOption);

        final User user = userService.getUserById(userId);
        addUserOption(user, fullSearchOption);

        var newDaysOldStart = fullSearchOption.getNewDaysOldStart();
        var newDaysOldEnd = fullSearchOption.getNewDaysOldEnd();
        if (newDaysOldStart != null || newDaysOldEnd != null) {
            buildDaysOldQuery(fullSearchOption);
        }

        var startCardVoMap = getProjectCard(user, fullSearchOption.newSearchOption());
        addDefaultServiceTypeFroChartAndCardOption(user, fullSearchOption);
        var serviceType = ProjectStatType.getInstance(fullSearchOption.getType());
        var service = statProjectServiceFactory.getStatComplexService(serviceType);
        var complexVo = service.getStatComplexInfo(fullSearchOption);
        assembleProjectLabel(complexVo.getList().getResult());
        complexVo.setCard(startCardVoMap);

        return complexVo;
    }

    @Override
    public StatProjectCardVo statProjectCard(long userId, StatFullSearchOption fullSearchOption)
            throws ServiceException {

        final User user = userService.getUserById(userId);
        addUserOption(user, fullSearchOption);

        return getProjectCard(user, fullSearchOption);
    }

    private StatProjectCardVo getProjectCard(User user, StatFullSearchOption fullSearchOption) {
        addDefaultServiceTypeOption(user, fullSearchOption);
        final StatProjectCardVo cardVo = new StatProjectCardVo();

        final var createdCard =
                statProjectServiceFactory
                        .getStatComplexService(PROJECT_CREATED)
                        .getCardStat(fullSearchOption.newSearchOption());

        var incompletedCardVo =
                statProjectServiceFactory
                        .getStatComplexService(ProjectStatType.PROJECT_INCOMPLETED)
                        .getCardStat(fullSearchOption.newSearchOption());
        var returnedCardVo =
                statProjectServiceFactory
                        .getStatComplexService(ProjectStatType.PROJECT_RETURNED)
                        .getCardStat(fullSearchOption.newSearchOption());

        var completedCardVo =
                statProjectServiceFactory
                        .getStatComplexService(ProjectStatType.PROJECT_COMPLETED)
                        .getCardStat(fullSearchOption.newSearchOption());

        var riskScoreVo =
                statProjectServiceFactory
                        .getStatComplexService(ProjectStatType.RISK_SCORE)
                        .getCardStat(fullSearchOption.newSearchOption());

        var closeoutCardVo =
                statProjectServiceFactory
                        .getStatComplexService(ProjectStatType.PROJECT_CLOSE_OUT)
                        .getCardStat(fullSearchOption.newSearchOption());

        cardVo.setCreated(createdCard);
        cardVo.setIncompleted(incompletedCardVo);
        cardVo.setReturned(returnedCardVo);
        cardVo.setCompleted(completedCardVo);
        cardVo.setRiskScore(riskScoreVo);
        cardVo.setCloseout(closeoutCardVo);

        return cardVo;
    }

    @Override
    public PagedResultVo statProjectList(
            long userID, @Validated StatFullSearchOption fullSearchOption) throws ServiceException {

        final User user = userService.getUserById(userID);
        addUserOption(user, fullSearchOption);

        final ProjectStatType statType = ProjectStatType.getInstance(fullSearchOption.getType());
        if (statType == null) {
            return null;
        }
        final StatComplexService listService =
                statProjectServiceFactory.getStatComplexService(statType);
        if (listService == null) {
            return null;
        }

        PagedResultVo projectList = listService.getProjectList(fullSearchOption);
        assembleProjectLabel(projectList.getResult());

        return projectList;
    }

    private PagedResultVo getStatProjectList(
            Integer searchOptionType, StatFullSearchOption fullSearchOption) {
        var statType = ProjectStatType.getInstance(searchOptionType);
        if (statType == null) {
            return null;
        }
        final StatComplexService listService =
                statProjectServiceFactory.getStatComplexService(statType);
        if (listService == null) {
            return null;
        }

        PagedResultVo projectList = listService.getProjectList(fullSearchOption);
        assembleProjectLabel(projectList.getResult());

        return projectList;
    }

    private void assembleProjectLabel(List<ProjectListVo> listVos) {
        if (listVos.isEmpty()) {
            return;
        }
        List<Long> projectIds =
                listVos.stream().map(ProjectListVo::getProjectId).collect(Collectors.toList());
        List<BoundProjectLabel> labels = projectLabelService.projectLabelList(projectIds);
        if (labels == null || labels.isEmpty()) {
            return;
        }
        Map<Long, BoundProjectLabel> boundLabelMap =
                labels.stream().collect(Collectors.toMap(BoundProjectLabel::getProjectId, o -> o));
        listVos.forEach(
                vo -> {
                    BoundProjectLabel label = boundLabelMap.get(vo.getProjectId());
                    if (label == null) {
                        return;
                    }
                    vo.setLabels(label.getProjectLabels());
                });
    }

    @Override
    public StatProjectVo statProjectChart(long userId, StatFullSearchOption fullSearchOption)
            throws ServiceException {

        final User user = userService.getUserById(userId);
        addUserOption(user, fullSearchOption);

        addDefaultServiceTypeOption(user, fullSearchOption);

        final StatProjectVo statProjectVo = new StatProjectVo();
        final ProjectStatType statType = ProjectStatType.getInstance(fullSearchOption.getType());
        if (statType == null) {
            return statProjectVo;
        }
        final StatComplexService complexService =
                statProjectServiceFactory.getStatComplexService(statType);

        if (complexService != null) {
            statProjectVo.setChart(complexService.getChartStat(fullSearchOption.newSearchOption()));

            addChartDefaultIfAbsent(statProjectVo.getChart(), fullSearchOption.newSearchOption());

            statProjectVo.setMap(complexService.getMapData(fullSearchOption.newSearchOption()));
            statProjectVo.setCard(complexService.getCardStat(fullSearchOption.newSearchOption()));
        }

        return statProjectVo;
    }

    private void addUserOption(User user, StatFullSearchOption searchOption)
            throws ServiceException {

        long swyfftUnderwriting = companyIDMap.getSwyfft_Underwriting();
        long securityFirst = companyIDMap.getSecurity_First_Florida();
        long olympus = companyIDMap.getOlympus();
        long sageSure = companyIDMap.getSageSure();
        long mdow = companyIDMap.getMdow();
        long berkleyOne = companyIDMap.getBerkleyOne();
        long theHartford = companyIDMap.getTheHartford();

        if (user.hasRole(RoleEnum.ADMIN)) {
            return;
        }

        searchOption.setCompanyIds(null);
        searchOption.setCompanyId(user.getCompanyId());

        var createdByNotLimited = Set.of(olympus, sageSure, swyfftUnderwriting, mdow, berkleyOne);
        if (!user.hasRole(RoleEnum.COMPANY_ADMIN)
                && !createdByNotLimited.contains(user.getCompanyId())) {
            searchOption.setCreatedBy(user.getUserId());
        }
    }

    private void addDefaultServiceTypeOption(User user, StatFullSearchOption searchOption) {
        if (CollectionUtils.isNotEmpty(searchOption.getServiceTypes())) {
            return;
        }

        long olympus = companyIDMap.getOlympus();
        if (Objects.equals(user.getCompanyId(), olympus)) {
            searchOption.addServiceType(
                    ProjectServiceTypeEnum.PREMIUM_FOUR_POINT_UNDERWRITING.getCode());
        }
        // 当前只针对 4-Point 和 Exterior 进行统计
        searchOption.addServiceType(ProjectServiceTypeEnum.FOUR_POINT_UNDERWRITING.getCode());
        searchOption.addServiceType(ProjectServiceTypeEnum.EXTERIOR_UNDERWRITING.getCode());
        searchOption.addServiceType(ProjectServiceTypeEnum.FOUR_POINT_SELF_UNDERWRITING.getCode());
        searchOption.addServiceType(
                ProjectServiceTypeEnum.PREMIUM_FOUR_POINT_UNDERWRITING.getCode());
        searchOption.addServiceType(searchOption.getServiceType());
    }

    private void addDefaultServiceTypeFroChartAndCardOption(
            User user, StatFullSearchOption searchOption) {
        if (searchOption.getServiceType() != null) {
            Set<Integer> serviceTypeSet = new HashSet<>();
            serviceTypeSet.add(searchOption.getServiceType());
            searchOption.setServiceTypes(serviceTypeSet);
        }

        if (CollectionUtils.isNotEmpty(searchOption.getServiceTypesForChartAndCard())){
            return;
        }

        long olympus = companyIDMap.getOlympus();
        if (Objects.equals(user.getCompanyId(), olympus)) {
            searchOption.addServiceTypeForChartAndCard(
                    ProjectServiceTypeEnum.PREMIUM_FOUR_POINT_UNDERWRITING.getCode());
        }
        // 当前只针对 4-Point 和 Exterior 进行统计
        searchOption.addServiceTypeForChartAndCard(
                ProjectServiceTypeEnum.FOUR_POINT_UNDERWRITING.getCode());
        searchOption.addServiceTypeForChartAndCard(
                ProjectServiceTypeEnum.EXTERIOR_UNDERWRITING.getCode());
        searchOption.addServiceTypeForChartAndCard(
                ProjectServiceTypeEnum.FOUR_POINT_SELF_UNDERWRITING.getCode());
        searchOption.addServiceTypeForChartAndCard(
                ProjectServiceTypeEnum.PREMIUM_FOUR_POINT_UNDERWRITING.getCode());
    }

    private void addChartDefaultIfAbsent(StatChartVo chartVo, StatFullSearchOption searchOption) {
        chartVo.addDefaultIfAbsentState(searchOption.getStates());
        chartVo.addDefaultIfAbsentServiceType(searchOption.getServiceTypes());
    }

    private void buildDaysOldQuery(StatFullSearchOption searchOption) {
        var companyIds = searchOption.getCompanyIds();
        if (companyIds == null) {
            companyIds =
                    Optional.ofNullable(searchOption.getCompanyId()).map(List::of).orElse(null);
        }
        var start = searchOption.getStartTime();
        var startTime = Optional.ofNullable(start).map(Instant::ofEpochMilli).orElse(null);
        var end = searchOption.getEndTime();
        var endTime = Optional.ofNullable(end).map(Instant::ofEpochMilli).orElse(null);
        var query = Message.ProjectDaysOldRequest.newBuilder();
        if (CollectionUtils.isNotEmpty(companyIds)) {
            var companies =
                    Iterables.toStream(companyIds)
                            .map(String::valueOf)
                            .collect(Collectors.toList());
            query.addAllCompanyId(companies);
        }
        acceptIfNotNull(query::setProjectCreatedStart, startTime, DateTimes::toTimestamp);
        acceptIfNotNull(query::setProjectCreatedEnd, endTime, DateTimes::toTimestamp);
        acceptIfNotNull(query::setDaysOldStart, searchOption.getNewDaysOldStart(), Int32Value::of);
        acceptIfNotNull(query::setDaysOldEnd, searchOption.getNewDaysOldEnd(), Int32Value::of);
        var projectByDaysOld =
                Iterables.toStream(
                                projectDaysOldProvider.findProjectByDaysOldQuery(
                                        ProjectDaysOldQuery.from(query.build())))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
        log.info("Find {} project in dashboard from days old.", projectByDaysOld.size());
        searchOption.intersectProjectIds(projectByDaysOld);
    }
}
