package com.bees360.event;

import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.event.registry.ProjectIBeesUploadedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.UserService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 当前ibees uploaded状态触发时移除ibees not completed tag
 */
@Log4j2
public class RemoveTagOnProjectIBeesUploaded extends AbstractNamedEventListener<ProjectIBeesUploadedEvent> {

    private final ProjectLabelService projectLabelService;

    private final UserService userService;

    public RemoveTagOnProjectIBeesUploaded(ProjectLabelService projectLabelService, UserService userService) {
        this.projectLabelService = projectLabelService;
        this.userService = userService;
    }

    /**
     * 当前ibees uploaded状态触发时移除ibees not completed tag
     */
    @Override
    public void handle(ProjectIBeesUploadedEvent event) throws IOException {
        var projectId = Long.parseLong(event.getProjectId());

        var userId = userService.toWebUserId(event.getUpdatedBy());
        if(userId == null) {
            var msg = String.format("Failed to update project %s status " +
                "due to user %s not exists in web.", projectId, event.getUpdatedBy());
            throw new IllegalStateException(msg);
        }

        projectLabelService.removeLabel(userId,projectId,ProjectLabelEnum.IBEES_NOT_COMPLETED);
        log.info("Remove project tag for project {} successfully by IBeesUploadedEvent.", projectId);
    }
}
