package com.bees360.service.event.report;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.ProjectScore;
import com.bees360.entity.ReportSummary;
import com.bees360.entity.User;
import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.bees360.service.ProjectScoreService;
import com.bees360.service.openapi.converter.ReportSummaryConvert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Optional;

@Component
public class ReportSummaryChangedEventListener {

    @Autowired
    private ProjectScoreService projectScoreService;

    /**
     * 不包含 delete 的情况
     * @param event
     * @throws ServiceException
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void changeRiskScoreOnSummaryChanged(ReportSummaryChangedEvent event) throws ServiceException {
        ReportSummary reportSummary = event.getReportSummary();
        if (reportSummary == null || !StringUtils.hasLength(reportSummary.getSummary())){
            return;
        }

        long projectId = reportSummary.getProjectId();
        if (projectId == 0){
            return;
        }

        ReportSummaryVo reportSummaryVo = ReportSummaryConvert.toSummaryVo(reportSummary.getSummary());
        if (reportSummaryVo == null || reportSummaryVo.getRisk() == null){
            return;
        }

        // If hazard score is not null, take it as project score.
        var hazardScore = getHazardScore(reportSummaryVo);
        if (hazardScore != null) {
            projectScoreService.createOrUpdateProjectScore(User.AI_ID, projectId, hazardScore);
            return;
        }

        String overallCondition = getOverallCondition(reportSummaryVo);
        if (ObjectUtils.isEmpty(overallCondition)){
            return;
        }
        ProjectScore projectScore = new ProjectScore(reportSummary.getProjectId(), overallCondition);
        projectScoreService.createOrUpdateProjectScore(User.AI_ID, projectScore.getProjectId(), projectScore.getScore().doubleValue());
    }

    private Double getHazardScore(ReportSummaryVo reportSummaryVo) {
        return reportSummaryVo.getRisk().getHazardScore();
    }

    private String getOverallCondition(ReportSummaryVo reportSummaryVo){
        String overallCondition = "";
        if (reportSummaryVo.getRisk() != null){
            overallCondition = reportSummaryVo.getRisk().getOverallCondition();
        }
        if (ObjectUtils.isEmpty(overallCondition) && reportSummaryVo.getBldg() != null){
            overallCondition = reportSummaryVo.getBldg().getOverallCondition();
        }
        if (ObjectUtils.isEmpty(overallCondition) && reportSummaryVo.getRoof() != null){
            overallCondition = reportSummaryVo.getRoof().getOverallCondition();
        }
        if (ObjectUtils.isEmpty(overallCondition) && reportSummaryVo.getExterior() != null){
            overallCondition = reportSummaryVo.getExterior().getOverallCondition();
        }
        if (ObjectUtils.isEmpty(overallCondition) && reportSummaryVo.getInterior() != null){
            overallCondition = reportSummaryVo.getInterior().getOverallCondition();
        }

        return overallCondition;
    }

}
