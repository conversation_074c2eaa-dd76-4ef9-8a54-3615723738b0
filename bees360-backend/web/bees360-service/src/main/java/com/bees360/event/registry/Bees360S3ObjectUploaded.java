package com.bees360.event.registry;

import lombok.Getter;

@Event(value = "s3_object_uploaded")
public class Bees360S3ObjectUploaded {
    @Getter private final String key;
    @Getter private final String eTag;
    @Getter private final long contentLength;
    @Getter private final String bucket;

    public Bees360S3ObjectUploaded(String key, String eTag, long contentLength) {
        this.key = key;
        this.eTag = eTag;
        this.contentLength = contentLength;
        this.bucket = "bees360";
    }
}
