package com.bees360.service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Company;
import com.bees360.entity.EventHistory;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectTimeline;
import com.bees360.entity.User;
import com.bees360.entity.dto.CreateOrUpdateProjectDto;
import com.bees360.entity.dto.MemberSchedule;
import com.bees360.entity.dto.MemberSchedule.MemberScheduleItem;
import com.bees360.entity.dto.ProjectBatchDto;
import com.bees360.entity.dto.ProjectDto;
import com.bees360.entity.dto.ProjectSearchOption;
import com.bees360.entity.dto.ProjectSearchOptionBase;
import com.bees360.entity.dto.ProjectSearchResultFilter;
import com.bees360.entity.enums.CompanyTypeEnum;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.entity.enums.ProcessStatusEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.vo.Address;
import com.bees360.entity.vo.HistoryLogVo;
import com.bees360.entity.vo.InvoiceFileVo;
import com.bees360.entity.vo.ProjectAbstractVo;
import com.bees360.entity.vo.ProjectDetailVo;
import com.bees360.entity.vo.ProjectInspectionInfoVo;
import com.bees360.entity.vo.ProjectInsuredInfoVo;
import com.bees360.entity.vo.ProjectLatLngVo;
import com.bees360.entity.vo.ProjectPageResultVo;
import com.bees360.entity.vo.ProjectServiceTypeVo;
import com.bees360.entity.vo.ProjectTinyUserVoCandidates;
import com.bees360.entity.vo.ProjectTinyVo;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.entity.vo.request.ProjectReworkReasonParam;
import com.bees360.entity.vo.request.ProjectServiceTypeParam;
import com.bees360.entity.vo.request.ProjectServiceTypeReasonParam;
import com.bees360.entity.vo.request.SendSmsPilotParam;
import com.bees360.flyzone.FlyZoneType;
import com.bees360.project.ProjectII;
import com.bees360.project.ServiceTypeEnum;
import jakarta.annotation.Nullable;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public interface ProjectService {

    Optional<Project> findById(long projectId);

    /**
     *
     * @throws ServiceException
     * @throws com.bees360.base.exception.ResourceNotFoundException project不存在
     */
    Project getById(long projectId);

	List<Project> listByIds(List<Long> ids);

    List<Long> listProjectIdsByAddressId(String addressId);

    List<Project> listByInsuranceCompanyAndStatus(long companyId, Integer status, long createdTime);

    List<Project> listInTimeRange(long createTimeStart, long createTimeEnd);

	void modifyGeneralPropertyInformation(Project project, long userId) throws ServiceException;

	Map<String, Object> getCliamInformation(long projectId) throws ServiceException;

	void modifyCliamInformation(long projectId, long userId, Map<String, Object> ciMap) throws ServiceException;

	ProjectAbstractVo getAbstractById(long projectId) throws ServiceException;

	ProjectPageResultVo pageTinyProjectsWithSearch(long userId, ProjectSearchOption options) throws ServiceException;

	List<ProjectTinyVo> getProjects(ProjectSearchOption options);

    ProjectPageResultVo pageTinyProjectSearchWithFilter(long userId, ProjectSearchOption searchOption, ProjectSearchResultFilter filter) throws ServiceException;

    List<ProjectTinyVo> searchProjectsForOpenapi(long userId, ProjectSearchOption searchOption, ProjectSearchResultFilter filter) throws ServiceException;

	Project createProject(long userId, ProjectDto projectDto) throws ServiceException;

	Project createProject(long userId, ProjectDto projectDto, boolean bePilot) throws ServiceException;

	void updateLatestStatus(long projectId, ProcessStatusEnum status);

	void deleteProject(long projectId, long userId) throws ServiceException;

	void cancelProject(long projectId, long userId, String cancelReason) throws ServiceException;

    void cancelProject(long projectId, long userId, SystemTypeEnum systemType, boolean authCheck,
                       String cancelReason) throws ServiceException;

	void recoverProject(long projectId, long userId) throws ServiceException;

	int countProjectInProcessing(long userId) throws ServiceException;

	List<HistoryLogVo> listEventHistory(long projectId) throws ServiceException;

//	List<Company> listInsurCompanyByUserId(long userId) throws ServiceException;

	List<Company> listComaniesInProjectsUserTakePartIn(long userId, CompanyTypeEnum companyType)
			throws ServiceException;

    /**
     * confirm all images have already uploaded
     * @param projectId project id
     * @param userId operate user
     * @param inspectionTypes inspection type
     * @throws ServiceException exception
     */
	void confirmImageUploaded(long projectId, long userId, long inspectionTypes) throws ServiceException;

    /**
     *web端主动触发图片同步逻辑
     */
	void confirmImageUploadedByWeb(long projectId, long userId, long inspectionTypes, int fileSourceType) throws ServiceException;

	void insertPilotCheckedStatus(long projectId, long userId, int type, ProjectStatusEnum projectStatu) throws ServiceException;

	Set<String> listUserAccessableModules(long projectId, long userId) throws ServiceException;

	List<UserTinyVo> listProjectsCreators(long userId) throws ServiceException;

	UserTinyVo inviteVisitor(long projectId, long inviter, String email) throws ServiceException;

    void inviteVisitor(long projectId, long visitorUserId)
        throws ServiceException;

    void inviteVisitor(long projectId, long inviter, long visitorUserId)
        throws ServiceException;

    void deleteVisitor(long projectId, long visitor) throws ServiceException;

    void deleteVisitor(long projectId, long userId, long visitor) throws ServiceException;

	List<Map<String,Object>> getAllZeroLatLngs() throws ServiceException;

	void updateProjectLatLngVos(List<ProjectLatLngVo> projectLatLngVos) throws ServiceException;

    ProjectTinyUserVoCandidates listCollaborationCandidates(long projectId, long userId) throws ServiceException;

    ProjectTinyUserVoCandidates listCollaborationCandidates(
            long userId, boolean allowAdminSearchAll) throws ServiceException;

	Map<String, Object> getGeneralPropertyInfo(long projectId) throws ServiceException;

    Map<Integer, Exception> createProjectBatch(long userId, ProjectBatchDto projectBatchVo) throws ServiceException;

	ProjectDetailVo getProjectDetail(long userId, long projectId) throws ServiceException;

	EventHistory getAdjusterCheckedStatus(long projectId) throws ServiceException;

    void initProjectData(Project project, CreationChannelType creationChannel) throws ServiceException;

	void changeMembers(long userId, long projectId, MemberSchedule members) throws ServiceException;

    void changeMembersToMultiProject(long curUserId, long targetUserId,  int roleId, List<Long> projectIds) throws ServiceException;

    void changeMember(long userId, long projectId, MemberScheduleItem scheduleItem) throws ServiceException;

    void changeMemberWithoutRoleCheck(long userId, long projectId, MemberScheduleItem scheduleItem) throws ServiceException;

    /**
	 * update oldCompanyId to newCompanyId for the projects which are created by userId
	 * @param userId
	 * @param oldCompanyId
	 * @param newCompanyId
	 */
	void updateProjectCompanyId(long userId, long oldCompanyId, long newCompanyId);

	void updateChimney(long projectId, int chimney);

	void updateRotationDegree(long projectId, double rotationDegree);

	Address getProjectAddress(long projectId) throws ServiceException;

	Address updateProjectAddress(long projectId, Address address) throws ServiceException;

	void updateImagesArchiveUrl(long projectId, String imagesArchiveUrl) throws ServiceException;

	/**
	 * 下载项目zip包的地址
     * 如果改用户不是项目组成员则无权限查看
	 * @param projectId 项目ID
	 * @return 下载项目zip包的地址
	 */
	String getImagesArchiveUrl(long projectId, long userId) throws ServiceException;

    ProjectInsuredInfoVo getInsuredInfo(long projectId) throws ServiceException;

    ProjectInsuredInfoVo updateInsuredInfo(long projectId, long userId, ProjectInsuredInfoVo insuredInfoVo) throws ServiceException;

    ProjectInsuredInfoVo updateInsuredInfo(
        long projectId, long userId, ProjectInsuredInfoVo insuredInfoVo, boolean saveSolidData)
        throws ServiceException;

    ProjectInspectionInfoVo getInspectionInfo(long projectId) throws ServiceException;

    ProjectInspectionInfoVo updateInspectionInfo(long projectId, long userId,ProjectInspectionInfoVo inspectionInfoVo) throws ServiceException;

    ProjectInspectionInfoVo updateInspectionInfo(
        long projectId,
        long userId,
        ProjectInspectionInfoVo inspectionInfoVo,
        boolean saveSolidData)
        throws ServiceException;

    void updateInspectionDueDate(long operator, long projectId, Long inspectionTime);

    void updateInspectionScheduledTime(long operator, long projectId, Long inspectionTime);

    Map<String, Object> getProjectsServiceOptions();

    ProjectServiceTypeVo getProjectServiceType(long projectId) throws ServiceException;

    ProjectServiceTypeVo updateProjectServiceType(long userId, long projectId, ProjectServiceTypeParam serviceTypeParam) throws ServiceException ;

    ProjectServiceTypeVo updateProjectServiceType(
        long userId,
        long projectId,
        ProjectServiceTypeParam serviceTypeParam,
        boolean saveSolidData)
        throws ServiceException;

    boolean isProjectManagedBy(long projectId, long companyId) throws ServiceException;

    Set<Long> getProjectManageCompany(long projectId) throws ServiceException;

    void updateReportRelevantData(Project project);

    /**
     * 获取项目所属的 checklist 文件的url地址
     * 根据项目的`serviceType`去判断返回其所属checklst文件的url地址
     * @see com.bees360.entity.enums.ProjectServiceTypeEnum
     * @param project 项目主键
     * @return checklist 文件的url地址
     */
	String getChecklistUrl(long project) throws ServiceException;

	ProjectPageResultVo getExportProjectList(long userId, ProjectSearchOption searchOption) throws ServiceException;


    void updatePayStatus(long projectId, long userId, int status) throws ServiceException;

    void updatePayStatus(List<Long> projectIds, int status);


    /**
     * transfer all base data need by report to ai, including project, project images, company and etc.
     * @param projectId 项目ID
     */
    void transferDatasToAi(long projectId, String type) throws ServiceException;

    List<Company> listCompanyDict(long userId, CompanyTypeEnum companyType)
        throws ServiceException;

    /**
     * check user authority and set filter with searchOption
     * @param user the user who want to query
     * @param searchOption search query
     */
    void setProjectSearchOptionBase(User user, ProjectSearchOptionBase searchOption);

    /**
     * 检查项目下的zip-file s3 key是否存在
     * @param projectId 项目ID
     * @return 项目下的zip-file s3 key是否存在
     */
    boolean isProjectImageArchiveExist(long projectId) throws ServiceException;

    void updateProjectToReworkStatus(
        long userId, long projectId, ProjectReworkReasonParam reworkReasonParam, Long updateTime) throws ServiceException;

    void updateProjectServiceTypeReason(long userId, long projectId, ProjectServiceTypeReasonParam serviceTypeParam) throws ServiceException;

    void sendSmsTextToPilot(long userId, long projectId, SendSmsPilotParam pilotParam) throws ServiceException;

    void updateCatNumber(long projectId, String catNumber);

    void updateFlyZoneType(long projectId, FlyZoneType flyZoneType) throws ServiceException;

    int updateHoverJobId(Long projectId, Long hoverJobId) throws ServiceException;

    void updateProject(CreateOrUpdateProjectDto createOrUpdateProjectDto);

    boolean projectServiceIsCompleted(long projectId) throws ServiceException;

    void updateInvoiceFile(long projectId, long userId, String invoiceFile) throws ServiceException;

    InvoiceFileVo getInvoiceFile(long projectId);

    void updatePolicyEffectedDate(long projectId, LocalDate policyEffectiveDate);

    boolean isPilotJobCompleted(long projectId);

    void resendInspectionCode(long userId, long project);

    List<Long> getProjectIdsOfFollowUp();


    default void setCustomerContactedTime(long projectId, Instant customerContactedTime) {
        setProjectTimeline(
            ProjectTimeline.builder()
                .projectId(projectId)
                .customerContactedTime(customerContactedTime)
                .build());
    }

    default void setInitialCustomerContactedTime(long projectId, Instant initialContactTime) {
        setProjectTimeline(
            ProjectTimeline.builder()
                .projectId(projectId)
                .initialCustomerContactTime(initialContactTime)
                .build());
    };

    void setProjectTimeline(ProjectTimeline timeline);

    /**
     * This method is designed for fast getting policy type from claim note.
     * If the policy type is not found from claim note then fetch it from policy repository.
     * @param projectId project id
     * @param claimNote project claim note
     * @param insuranceCompany insurance company id
     * @param claimType claim type
     * @return policy type, fetch the policy type from claim note, otherwise from policy repository, maybe null.
     */
    @Nullable
    String getPolicyType(long projectId, String claimNote, Long insuranceCompany, Integer claimType);

    void updateProjectProperty(long projectId, Integer projectProperty);

    void handelCloseReport(long projectId) throws ServiceException;

    ProjectII updatePolicyTypeAndPropertyType(long projectId, String policyType, Integer propertyType) throws ServiceException;

    ProjectII updatePolicyTypeAndPropertyType(
        long projectId, String policyType, Integer propertyType, boolean saveSolidData)
        throws ServiceException;
}
