package com.bees360.service.openapi;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.openapi.client.OpenApiClient;
import com.bees360.entity.openapi.client.OpenApiClientUpdateDto;
import com.bees360.entity.openapi.client.OpenApiClientVo;
import com.bees360.entity.openapi.client.OpenApiSearchFilterOption;

import java.util.List;

public interface OpenApiClientService {

    OpenApiClientVo createClient(long userId, OpenApiClient secret);

    OpenApiClientVo assignClient(long userId, String clientName) throws ServiceException;

    OpenApiClientVo update(long userId, String clientId, OpenApiClientUpdateDto secretDto) throws ServiceException;

    void delete(long id);

    void deleteByClientId(String clientId);

    OpenApiClient getByClientId(String clientId);

    List<OpenApiClient> listClient(OpenApiSearchFilterOption search) throws ServiceException;
}
