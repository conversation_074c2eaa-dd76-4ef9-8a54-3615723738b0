package com.bees360.service.stat;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.stat.search.StatFullSearchOption;
import com.bees360.entity.stat.search.StatSearchOption;
import com.bees360.entity.stat.vo.StatProjectCardVo;
import com.bees360.entity.stat.vo.StatProjectVo;
import com.bees360.entity.stat.vo.StatComplexVo;
import com.bees360.entity.stat.vo.list.PagedResultVo;
import com.bees360.entity.stat.search.ChartSearchOption;
import com.bees360.entity.stat.search.ListSearchOption;

public interface ProjectStatService {

    StatProjectCardVo statProjectCard(long userId, StatFullSearchOption searchOption) throws ServiceException;

    PagedResultVo statProjectList(long userID, StatFullSearchOption searchOption) throws ServiceException;

    StatProjectVo statProjectChart(long userId, StatFullSearchOption searchOption) throws ServiceException;

    StatComplexVo statProjectComplex(long userId, StatFullSearchOption searchOption) throws ServiceException;
}
