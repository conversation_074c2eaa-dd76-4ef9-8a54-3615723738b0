package com.bees360.service.impl;

import com.bees360.activity.ActivityManager;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.EventHistory;
import com.bees360.entity.Member;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.ProjectStatusUser;
import com.bees360.entity.dto.CodeNameDto;
import com.bees360.entity.dto.ProjectStatusTimeLineDto;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.query.ProjectStatusQuery;
import com.bees360.entity.vo.ProjectStatusVo;
import com.bees360.mapper.MemberMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.ProjectStatusMapper;
import com.bees360.project.Message;
import com.bees360.project.ProjectII;
import com.bees360.project.state.ProjectRequestCancellationManager;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.repository.Provider;
import com.bees360.service.ActivityService;
import com.bees360.service.EventHistoryService;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.beespilot.BeesPilotBatchItemService;
import com.bees360.service.beespilot.BeesPilotBatchService;
import com.bees360.service.beespilot.BeesPilotStatusService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.AssertUtil;
import com.bees360.web.event.project.ProjectCanceledEvent;
import com.bees360.web.event.project.ProjectReworkAiEvent;
import com.bees360.web.event.project.ProjectReworkWebEvent;
import com.bees360.web.event.project.ProjectStatusAssignedToPilotEvent;
import com.bees360.web.event.project.ProjectStatusClientReceivedEvent;
import com.bees360.web.event.project.ProjectStatusCustomerContactedEvent;
import com.bees360.web.event.project.ProjectStatusEvent;
import com.bees360.web.event.project.ProjectStatusIBeesUploadedEvent;
import com.bees360.web.event.project.ProjectStatusImageUploadedEvent;
import com.bees360.web.event.project.ProjectStatusProjectCanceledEvent;
import com.bees360.web.event.project.ProjectStatusProjectCreatedEvent;
import com.bees360.web.event.project.ProjectStatusProjectReworkEvent;
import com.bees360.web.event.project.ProjectStatusReceiveErrorEvent;
import com.bees360.web.event.project.ProjectStatusReturnedToClientEvent;
import com.bees360.web.event.project.ProjectStatusSiteInspectedEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bees360.entity.enums.NewProjectStatusEnum.CUSTOMER_CONTACTED;
import static com.bees360.entity.enums.NewProjectStatusEnum.PROJECT_REWORK;
import static com.bees360.entity.enums.NewProjectStatusEnum.SITE_INSPECTED;

/**
 * 确保每次状态改变都应该有状态改变事件
 * @see ProjectStatusEvent
 * <AUTHOR> Guanrong
 * @date 2019/12/26 14:31
 */
@Service
@Slf4j
public class ProjectStatusServiceImpl implements ProjectStatusService, ApplicationEventPublisherAware {

    @Autowired
    private ProjectStatusMapper projectStatusMapper;

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private EventHistoryService eventHistoryService;

    @Autowired
    private ProjectService projectService;
    @Autowired
    private BeesPilotStatusService beesPilotStatusService;
    @Autowired
    private BeesPilotBatchService beesPilotBatchService;
    @Resource
    private BeesPilotBatchItemService beesPilotBatchItemService;
    @Resource
    private MemberMapper memberMapper;
    private ApplicationEventPublisher publisher;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private ActivityManager activityManager;
    @Autowired
    private ProjectLabelService projectLabelService;

    @Autowired
    private ProjectStatusManager projectStatusManager;

    @Autowired
    private ProjectRequestCancellationManager projectRequestCancellationManager;

    @Autowired private Provider<ProjectII> projectIIProvider;

    @Autowired private Bees360FeatureSwitch featureSwitch;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.publisher = applicationEventPublisher;
    }

    @Override
    public List<ProjectStatus> listProjectStatus(long projectId){
        return projectStatusMapper.listByProjectId(projectId);
    }

    @Override
    public Map<Long, Map<NewProjectStatusEnum, ProjectStatus>> listProjectStatusLatestRecord(Collection<Long> projectIds) {
        ArrayList<Long> projectIdList = new ArrayList<>(projectIds);
        return projectStatusMapper.listByProjectIds(projectIdList).stream()
            .collect(Collectors.groupingBy(ProjectStatus::getProjectId,
                Collectors.filtering(
                    projectStatus -> NewProjectStatusEnum.getEnum(projectStatus.getStatus()) != null,
                    Collectors.toMap(
                        projectStatus -> NewProjectStatusEnum.getEnum(projectStatus.getStatus()),
                        Function.identity(),
                        (status0, status1) -> {
                            if (status0.getCreatedTime() < status1.getCreatedTime()) {
                                return status1;
                            } else {
                                return status0;
                            }
                        }))));
    }

    private Map<Long, ProjectStatusVo> listProjectStatusRecord(List<Long> projectIds, NewProjectStatusEnum projectStatus, boolean latest) {
        if (CollectionUtils.isEmpty(projectIds)){
            return new HashMap<>();
        }
        final List<ProjectStatusUser> projectStatusList = projectStatusMapper.listByProjectIdsWithUser(projectIds);
        if (CollectionUtils.isEmpty(projectStatusList)){
            return new HashMap<>();
        }
        final Map<Long, List<ProjectStatusUser>> projectStatusMap = projectStatusList.stream().filter(p -> p.getStatus() == projectStatus.getCode())
            .collect(Collectors.groupingBy(ProjectStatusUser::getProjectId));

        Map<Long, ProjectStatusVo> map = new HashMap<>();
        projectIds.forEach(id ->{
            final ProjectStatusVo statusVo = new ProjectStatusVo(projectStatus);
            final List<ProjectStatusUser> projectStatusUsers = projectStatusMap.get(id);
            if (!CollectionUtils.isEmpty(projectStatusUsers)){
                if (latest) {
                    projectStatusUsers.sort(Comparator.comparingLong(ProjectStatusUser::getCreatedTime).reversed());
                } else {
                    projectStatusUsers.sort(Comparator.comparingLong(ProjectStatusUser::getCreatedTime));
                }
                statusVo.setUserName(projectStatusUsers.get(0).getUserName());
                statusVo.setCreatedTime(projectStatusUsers.get(0).getCreatedTime());
            }
            map.put(id, statusVo);
        });
        return map;
    }

    @Override
    public List<ProjectStatusVo> listProjectStatusTimeLine(long projectId) throws ServiceException {

        return getProjectStatusTimeLine(projectId).toListShowInTimeline().stream()
            .filter(statusVo -> {
                final int code = statusVo.getStatus().getCode();
                // 尚未 cancel 的 project 不需要在 timeline 中展示 cancel 状态
                return code != NewProjectStatusEnum.PROJECT_CANCELED.getCode() || statusVo.available();
            })
            .filter(status -> Objects.nonNull(status.getCreatedTime()))
            .collect(Collectors.toList());
    }

    public List<ProjectStatusVo> assembleStatusTimeLine(List<ProjectStatus> statuses) {

        Map<Integer, ProjectStatusVo> statusesTimeLine = new LinkedHashMap<>();

        Arrays.stream(NewProjectStatusEnum.values()).forEach(s -> {
            if(!statusesTimeLine.containsKey(s.getCode())) {
                statusesTimeLine.put(s.getCode(), toStatusVo(s));
            }
        });

        statuses.sort(Comparator.comparingLong(ProjectStatus::getCreatedTime));
        statuses.forEach(s -> statusesTimeLine.put(s.getStatus(), toStatusVo(s)));
        return new ArrayList<>(statusesTimeLine.values());
    }

    private List<ProjectStatusVo> assembleStatusToTimeLineWithUser(List<ProjectStatusUser> statuses) {

        Map<Integer, ProjectStatusVo> statusesTimeLine = new LinkedHashMap<>();

        Arrays.stream(NewProjectStatusEnum.values()).forEach(s -> {
            if(!statusesTimeLine.containsKey(s.getCode())) {
                statusesTimeLine.put(s.getCode(), toStatusVo(s));
            }
        });
        statuses.sort(Comparator.comparingLong(ProjectStatusUser::getCreatedTime));
        statuses.forEach(s -> {
            ProjectStatusVo vo = toStatusVoWithUser(s);
            if (vo != null) {
                statusesTimeLine.put(s.getStatus(), vo);
            }
        });
        return new ArrayList<>(statusesTimeLine.values());
    }

    @Override
    public ProjectStatusTimeLineDto getProjectStatusTimeLine(long projectId) throws ServiceException {
        List<ProjectStatusUser> statuses = projectStatusMapper.listByProjectIdWithUser(projectId);
        List<ProjectStatusVo> projectStatusVos = assembleStatusToTimeLineWithUser(statuses);
        return new ProjectStatusTimeLineDto(projectId, projectStatusVos);
    }

    @Override
    public Map<Long, ProjectStatusTimeLineDto> listProjectStatusTimeLines(List<Long> projectIds) {

        List<ProjectStatusUser> statuses = projectStatusMapper.listByProjectIdsWithUser(projectIds);
        return statuses.stream()
            .reduce(
                new HashMap<>(),
                (map, projectStatusUser) -> {
                    long projectId = projectStatusUser.getProjectId();
                    map.computeIfAbsent(projectId, ProjectStatusTimeLineDto::new);
                    map.computeIfPresent(
                        projectId,
                        (id, timeLine) -> {
                            NewProjectStatusEnum status =
                                NewProjectStatusEnum.getEnum(
                                    projectStatusUser.getStatus());
                            ProjectStatusVo projectStatusVo =
                                toStatusVoWithUser(projectStatusUser);
                            timeLine.addStatusVo(projectStatusVo,
                                Comparator.comparing(ProjectStatusVo::getCreatedTime));
                            timeLine.countStatusVo(projectStatusVo);
                            // 统计 Image uploaded 的时间改为最后一次触发该状态的时间
                            // @see https://gitlab.bees360.com/engineers/bumble-bee/issues/-/issues/182
                            // 统计 Image uploaded 的时间改为第一次触发该状态的时间
                            // @see https://gitlab.bees360.com/engineers/process/-/issues/1
                            if (Objects.equals(NewProjectStatusEnum.IMAGE_UPLOADED,status) &&
                                projectStatusVo != null &&
                                timeLine.getImageUploaded().getCreatedTime() > projectStatusVo.getCreatedTime()) {
                                timeLine.addStatusVo(projectStatusVo);
                            }

                            if (NewProjectStatusEnum.RETURNED_TO_CLIENT.equals(
                                status)) {
                                if (timeLine.getFirstReturnToClient() == null) {
                                    timeLine.setFirstReturnToClient(projectStatusVo);
                                } else if (timeLine.getFirstReturnToClient()
                                    .getCreatedTime()
                                    > projectStatusUser.getCreatedTime()) {
                                    timeLine.setFirstReturnToClient(projectStatusVo);
                                }

                                if (timeLine.getLastReturnToClient() == null) {
                                    timeLine.setLastReturnToClient(projectStatusVo);
                                } else if (timeLine.getLastReturnToClient()
                                    .getCreatedTime()
                                    < projectStatusUser.getCreatedTime()) {
                                    timeLine.setLastReturnToClient(projectStatusVo);
                                }
                            }

                            if (NewProjectStatusEnum.CLIENT_RECEIVED.equals(status)) {
                                if (timeLine.getLastClientReceived() == null) {
                                    timeLine.setLastClientReceived(projectStatusVo);
                                } else if (timeLine.getLastClientReceived()
                                    .getCreatedTime()
                                    < projectStatusUser.getCreatedTime()) {
                                    timeLine.setLastClientReceived(projectStatusVo);
                                }
                            }

                            return timeLine;
                        });

                    return map;
                },
                (map, map1) -> {
                    map.putAll(map1);
                    return map;
                });
    }

    private ProjectStatusVo toStatusVo(ProjectStatus status) {
        NewProjectStatusEnum projectStatusEnum = NewProjectStatusEnum.getEnum(status.getStatus());
        ProjectStatusVo statusVo = toStatusVo(projectStatusEnum);
        statusVo.setCreatedTime(status.getCreatedTime());
        return statusVo;
    }

    private ProjectStatusVo toStatusVoWithUser(ProjectStatusUser statusUser) {
        NewProjectStatusEnum projectStatusEnum = NewProjectStatusEnum.getEnum(statusUser.getStatus());
        if (projectStatusEnum == null) {
            log.warn("Unknown project status code '{}'", statusUser.getStatus());
            return null;
        }
        ProjectStatusVo statusVo = toStatusVo(projectStatusEnum);
        statusVo.setCreatedTime(statusUser.getCreatedTime());
        statusVo.setUserName(statusUser.getUserName());
        return statusVo;
    }


    private ProjectStatusVo toStatusVo(NewProjectStatusEnum status) {
        ProjectStatusVo statusVo = new ProjectStatusVo();

        statusVo.setStatus(new CodeNameDto(status.getCode(), status.getDisplay()));
        return statusVo;
    }

    @Override
    public void changeOnCustomerContacted(long userId, long projectId, long contactTime){
        if (featureSwitch.isDisableInspectStatusUpdate()) {
            return;
        }

        statusOnChange(userId, projectId, CUSTOMER_CONTACTED, false, "", contactTime);
    }

    @Override
    public void changeOnWaitingForAcceptance(long userId, long projectId) {
        if (featureSwitch.isDisableInspectStatusUpdate()) {
            return;
        }
        statusOnChange(userId, projectId, NewProjectStatusEnum.PENDING_ACCEPTANCE, true, "", null);
    }

    @Override
    public void changeOnAssignedToPilot(long userId, long projectId) {
        changeOnAssignedToPilot(userId, projectId, null);
    }


    @Override
    public void changeOnAssignedToPilot(long userId, long projectId, Long updateTime) {
        if (featureSwitch.isDisableInspectStatusUpdate()) {
            return;
        }

        // 该方法不校验状态变更的合法性，该方法会将其它状态变更到 ASSIGNED_TO_PILOT 状态.
        statusOnChange(userId, projectId,  NewProjectStatusEnum.ASSIGNED_TO_PILOT, true, "", updateTime);
    }

    @Override
    public ProjectStatus changeOnImageUploaded(long userId, long projectId) {
        return statusOnChange(
            userId, projectId, NewProjectStatusEnum.IMAGE_UPLOADED, false, "", null);
    }

    @Override
    public void changeOnImageUploaded(long userId, long projectId, Long updateTime) {
        if (featureSwitch.isDisableInspectStatusUpdate()) {
            return;
        }

        statusOnChange(
            userId, projectId, NewProjectStatusEnum.IMAGE_UPLOADED, false, "", updateTime);
    }


    @Override
    public void changeOnIBeesUploaded(long userId, long projectId) {
        if (featureSwitch.isDisableInspectStatusUpdate()) {
            return;
        }

        statusOnChange(userId, projectId, NewProjectStatusEnum.IBEES_UPLOADED);
    }

    /**
     *
     * 将项目状态回到PENDING_ACCEPTANCE状态以前
     * **/
    @Override
    public void changeOnCancelPilot(long userId, long projectId) {
        if (featureSwitch.isDisableInspectStatusUpdate()) {
            return;
        }

        var project = projectService.getById(projectId);
        if (project.getProjectStatus() != NewProjectStatusEnum.ASSIGNED_TO_PILOT.getCode()
            && project.getProjectStatus() != NewProjectStatusEnum.PENDING_ACCEPTANCE.getCode()) {
            log.info(
                "Skipped to rollback project '{}' status to customer contacted.",
                projectId);
            return;
        }
        List<ProjectStatus> statuses =
                projectStatusMapper.listByProjectId(projectId).stream()
                        .filter(
                                status ->
                                        status.getStatus()
                                                < NewProjectStatusEnum.PENDING_ACCEPTANCE.getCode())
                        .collect(Collectors.toUnmodifiableList());
        ProjectStatus status =
                statuses.stream()
                        .max(Comparator.comparingInt(ProjectStatus::getStatus))
                        .orElse(null);
        if (status != null) {
            status.setUserId(userId);
            status.setCreatedTime(System.currentTimeMillis());
            rollbackProjectStatusAndPublishStatusChangeEvent(status);
        }
    }

    @Override
    public void changeOnSiteInspected(long userId, long projectId, Long updateTime) {
        if (featureSwitch.isDisableInspectStatusUpdate()) {
            return;
        }

        statusOnChange(userId, projectId, SITE_INSPECTED, false, "", updateTime);
    }

    @Override
    public ProjectStatus changeOnReturnedToClient(long userId, long projectId) {
        return changeOnReturnedToClient(userId,projectId, Strings.EMPTY);
    }

    @Override
    public ProjectStatus changeOnReturnedToClient(long userId, long projectId, @Nullable String comment) {
        // 订阅服务相关的report approve后需要把Project状态调整到ReturnedToClient（ClientReceived的也需要调整回来）

        return statusOnChange(
            userId, projectId, NewProjectStatusEnum.RETURNED_TO_CLIENT, true, comment, null);
    }

    @Override
    public ProjectStatus changeOnClientReceived(long userId, long projectId, String comment) {
        return changeOnClientReceived(userId, projectId, SystemTypeEnum.BEES360, comment);
    }

    @Override
    public ProjectStatus changeOnClientReceived(long userId, long projectId, SystemTypeEnum systemType, String comment) {
        return statusOnChange(userId, projectId, NewProjectStatusEnum.CLIENT_RECEIVED,
            false, comment, null);
    }

    @Override
    public void changeOnReceiveError(long userId, long projectId, String comment) throws ServiceException {
        statusOnChange(userId, projectId, NewProjectStatusEnum.RECEIVE_ERROR,
            false, comment, null);
    }

    private ProjectStatus projectReworkBase(long userId, long projectId, String title, String content, Long updateTime) {
        log.info("projectStatusService project rework projectId {}, userId {}, title {}, content {}", projectId, userId,
            title, content);
        var reworkMsg = getReworkMessage(title, content);
        var projectStatus = statusOnChange(userId, projectId, PROJECT_REWORK, true, reworkMsg, updateTime);
        beesPilotStatusService.resetBeespilotStatus(projectId);
        EventHistory eventHistory = eventHistoryService.getEventHistory(ProjectStatusEnum.PROJECT_REWORK,
            projectId, userId, title, content);
        eventHistoryService.insertHistoryToProject(eventHistory);

        return projectStatus;
    }

    private static String getReworkMessage(String title, String content) {
        String commentContent = "Rework Reason\r\n\r\nTitle: %s".formatted(title);
        if ( Strings.isNotBlank(content)){
            commentContent = commentContent.concat("\r\nContent: %s".formatted(content));
        }
        return commentContent;
    }

    /**
     * 从Ai发起project rework申请
     */
    @Override
    public void projectReworkOnAi(long userId, long projectId, String title, String content) {
        ProjectStatus projectStatus =  projectReworkBase(userId, projectId, title, content, null);
        publisher.publishEvent(new ProjectReworkAiEvent(this, projectService.getById(projectId), title, content));
    }

    /**
     * 从web发起project rework申请
     */
    @Override
    public void projectReworkOnWeb(long userId, long projectId, String title, String content, Long updateTime) {
        ProjectStatus projectStatus = projectReworkBase(userId, projectId, title, content, updateTime);

        var event = new ProjectReworkWebEvent(this, projectService.getById(projectId), title, content);
        event.setCreatedTime(projectStatus.getCreatedTime());
        publisher.publishEvent(event);
    }

    @Override
    public void changeOnProjectCanceled(long userId, long projectId, SystemTypeEnum systemType,
                                        String cancelReason) throws ServiceException {
        ProjectStatus status = statusOnChange(userId, projectId, NewProjectStatusEnum.PROJECT_CANCELED,
                                                false, cancelReason, null);
        Project project = projectService.getById(projectId);
        if (!isPilotJobCompleted(projectId)) {
            updateBatchStatusOnProjectCanceled(project);
        }
        // cancel project
        publisher.publishEvent(
            new ProjectCanceledEvent(this, projectService.getById(projectId), userId, systemType, cancelReason));
    }

    private boolean isPilotJobCompleted(long projectId) {
        return projectService.isPilotJobCompleted(projectId);
    }

    @Override
    public void rollProjectStatusToCustomerContacted(long projectId) throws ServiceException {
        updateBatchStatusOnProjectCanceled(projectService.getById(projectId));
        ProjectStatusQuery query = ProjectStatusQuery.builder()
            .projectId(projectId)
            // it will search status contains [Customer Contacted] and [Project Created]
            .projectStatusLeft(CUSTOMER_CONTACTED.getCode())
            .build();
        List<ProjectStatus> projectStatuses = projectStatusMapper.listWithQuery(query);
        AssertUtil.notEmpty(projectStatuses, "Project status must contains project created but it does not.");
        ProjectStatus projectStatus = projectStatuses.get(0);
        rollbackProjectStatusAndPublishStatusChangeEvent(projectStatus);
    }

    @Override
    public void updateBatchStatusOnProjectCanceled(Project project) throws ServiceException {
        AssertUtil.notNull(project, "project is null");
        Member member = memberMapper.getActiveMemberByRole(project.getProjectId(), RoleEnum.PILOT.getCode());
        if (member == null) {
            return;
        }
        log.info("Start to delete batch when project '{}' is canceled", project.getProjectId());
        beesPilotBatchItemService.updateBatchDeletedStatus(project.getProjectId(), member.getUserId(), true);
    }

    @Override
    public void changeOnProjectRecovered(long userId, long projectId) throws ServiceException {
        List<ProjectStatus> statusList = listProjectStatus(projectId);

        Iterator<ProjectStatus> it = statusList.iterator();
        while (it.hasNext()){
            ProjectStatus next = it.next();
            var status = next.getStatus();
            if (status == NewProjectStatusEnum.PROJECT_CANCELED.getCode()
                    || status == NewProjectStatusEnum.ASSIGNED_TO_PILOT.getCode()
                    || status == NewProjectStatusEnum.PENDING_ACCEPTANCE.getCode()
                    || status == NewProjectStatusEnum.RETURNED_TO_CLIENT.getCode()
                    || status == NewProjectStatusEnum.CLIENT_RECEIVED.getCode()) {
                it.remove();
            }
        }

        List<ProjectStatusVo> projectStatusVos = assembleStatusTimeLine(statusList);
        ProjectStatusTimeLineDto timeLineDto = new ProjectStatusTimeLineDto(projectId, projectStatusVos);
        ProjectStatusVo statusVo = timeLineDto.getLastAvailable();
        if(statusVo != null) {
            rollbackProjectStatusAndPublishStatusChangeEvent(statusVo, projectId, userId);
        }
    }

    @Override
    public List<Long> listProjectByStatusListAndTime(List<Integer> projectStatusList, long startTime, long endTime) {
        if (!CollectionUtils.isEmpty(projectStatusList)) {
            return projectStatusMapper.getProjectsByStatusLisAndTime(projectStatusList, startTime, endTime);
        }
        return new ArrayList<>();
    }

    public void statusOnChange(long userId, long projectId, NewProjectStatusEnum targetStatus) {
        statusOnChange(userId, projectId, targetStatus, false, "", null);
    }

    private ProjectStatus statusOnChange(long userId, long projectId, NewProjectStatusEnum targetStatus,
                                         boolean forceUpdate, String comment, Long updateTime) {
        ProjectStatus projectStatus = new ProjectStatus();
        projectStatus.setUserId(userId);
        projectStatus.setProjectId(projectId);
        projectStatus.setStatus(targetStatus.getCode());
        projectStatus.setCreatedTime(
            Optional.ofNullable(updateTime).orElse(System.currentTimeMillis()));

        updateProjectStatus(projectStatus, comment);
        return projectStatus;
    }

    private void rollbackProjectStatusAndPublishStatusChangeEvent(ProjectStatusVo statusVo, long projectId, long userId) {
        ProjectStatus projectStatus = new ProjectStatus();
        projectStatus.setCreatedTime(statusVo.getCreatedTime());
        projectStatus.setProjectId(projectId);
        projectStatus.setUserId(userId);
        projectStatus.setStatus(statusVo.getStatus().getCode());

        rollbackProjectStatusAndPublishStatusChangeEvent(projectStatus);
    }

    private void rollbackProjectStatusAndPublishStatusChangeEvent(ProjectStatus projectStatus) {
        rollbackStatus(projectStatus);
    }

    /**
     * 将项目状态回滚
     *
     * @param status 目标状态
     */
    private void rollbackStatus(ProjectStatus status) {
        var projectId = String.valueOf(status.getProjectId());
        var curStatus = projectStatusManager.getStatus(projectId);
        var result = projectStatusManager.rollbackStatus(
            projectId,
            com.bees360.project.Message.ProjectStatus.forNumber(status.getStatus()),
            String.valueOf(status.getUserId()),
            curStatus.getVersion());
    }

    private void updateProjectStatus(ProjectStatus status, String comment) {
        projectStatusManager.updateStatus(String.valueOf(status.getProjectId()),
            com.bees360.project.Message.ProjectStatus.forNumber(status.getStatus()),
            String.valueOf(status.getUserId()),
            comment,
            Instant.ofEpochMilli(status.getCreatedTime()));
    }

    @Override
    public void insertProjectStatus(long projectId, long userId, int status, long statusUpdateTime) {
        var projectStatus = new ProjectStatus();
        projectStatus.setProjectId(projectId);
        projectStatus.setUserId(userId);
        projectStatus.setStatus(status);
        projectStatus.setCreatedTime(statusUpdateTime);

        updateProjectStatus(projectStatus, "");
    }

    @Override
    public ProjectStatus requestCancel(long userId, long projectId, SystemTypeEnum systemType, String cancelReason) {
        projectRequestCancellationManager.requestCancel(String.valueOf(userId), String.valueOf(projectId), Message.CancellationOptionEnum.CANCEL_FOR_PERSONAL_REASON, "openAPI");
        return projectStatusMapper.getById(projectId);
    }

    @Override
    public List<ProjectStatusVo> listProjectStatusByCreateTime(long projectId) throws ServiceException {
        List<ProjectStatusUser> projectStatuses = projectStatusMapper.listByProjectIdWithUser(projectId);
        if (CollectionUtils.isEmpty(projectStatuses)) {
            return Collections.emptyList();
        }
        List<ProjectStatusVo> statusVoList = projectStatuses.stream().map(this::toStatusVoWithUser).filter(Objects::nonNull).collect(Collectors.toList());
        List<ProjectStatusVo> statusResult = new ArrayList<>();
        for (int i = 0; i < statusVoList.size(); i++) {
            if (Objects.equals(i, 0)) {
                statusResult.add(statusVoList.get(i));
            } else {
                if (!Objects.equals(statusResult.get(statusResult.size() - 1).getStatus().getCode(),
                    statusVoList.get(i).getStatus().getCode())) {
                    statusResult.add(statusVoList.get(i));
                }
            }
        }
        Project project = projectService.getById(projectId);
        Long createTime = statusResult.stream().filter(o -> Objects.equals(o.getStatus().getCode(), SITE_INSPECTED.getCode()))
            .findFirst().map(ProjectStatusVo::getCreatedTime).orElse(null);
        if (Objects.nonNull(createTime)) {
            statusResult.forEach(o -> {
                if (Objects.equals(o.getCreatedTime(), createTime)
                    && Objects.nonNull(project) && !Objects.equals(project.getProjectStatus(), NewProjectStatusEnum.PROJECT_REWORK.getCode())) {
                    o.setShowRework(true);
                }
            });
        }

        return statusResult;
    }

    /**
     * 当项目状态变更到指定状态时，发送该状态的变更事件
     *
     * @param projectStatus 事件状态模型
     * @param isRollback    是否为回滚操作
     */
    @Override
    public void publishProjectStatusChangeEvent(ProjectStatus projectStatus, boolean isRollback) {
        var event = getProjectStatusEvent(projectStatus, isRollback);
        Optional.ofNullable(event).ifPresent(publisher::publishEvent);
    }

    private ProjectStatusEvent getProjectStatusEvent(ProjectStatus projectStatus, boolean isRollback) {
        NewProjectStatusEnum status = NewProjectStatusEnum.getEnum(projectStatus.getStatus());
        ProjectStatusEvent projectStatusEvent;
        Project project = projectService.getById(projectStatus.getProjectId());
        if (status == null) {
            return null;
        }

        switch (status) {
            case PROJECT_CREATED:
                projectStatusEvent =
                        new ProjectStatusProjectCreatedEvent(this, project, projectStatus, isRollback);
                break;
            case CUSTOMER_CONTACTED:
                projectStatusEvent =
                        new ProjectStatusCustomerContactedEvent(this, project, projectStatus);
                break;
            case ASSIGNED_TO_PILOT:
                projectStatusEvent =
                        new ProjectStatusAssignedToPilotEvent(this, project, projectStatus);
                break;
            case PROJECT_REWORK:
                projectStatusEvent =
                        new ProjectStatusProjectReworkEvent(this, project, projectStatus);
                break;
            case SITE_INSPECTED:
                projectStatusEvent =
                        new ProjectStatusSiteInspectedEvent(this, project, projectStatus);
                break;
            case IBEES_UPLOADED:
                projectStatusEvent =
                        new ProjectStatusIBeesUploadedEvent(this, project, projectStatus);
                break;
            case IMAGE_UPLOADED:
                projectStatusEvent =
                        new ProjectStatusImageUploadedEvent(this, project, projectStatus);
                break;
            case RETURNED_TO_CLIENT:
                projectStatusEvent =
                        new ProjectStatusReturnedToClientEvent(this, project, projectStatus);
                break;
            case CLIENT_RECEIVED:
                projectStatusEvent =
                        new ProjectStatusClientReceivedEvent(this, project, projectStatus, projectStatus.getUserId(), SystemTypeEnum.BEES360);
                break;
            case RECEIVE_ERROR:
                projectStatusEvent =
                        new ProjectStatusReceiveErrorEvent(this, project, projectStatus);
                break;
            case PROJECT_CANCELED:
                projectStatusEvent =
                        new ProjectStatusProjectCanceledEvent(this, project, projectStatus);
                break;

            default:
                // 状态变更要发送状态变更的事件
                projectStatusEvent = new ProjectStatusEvent(this, project, projectStatus, "") {};
        }
        return projectStatusEvent;
    }
}
