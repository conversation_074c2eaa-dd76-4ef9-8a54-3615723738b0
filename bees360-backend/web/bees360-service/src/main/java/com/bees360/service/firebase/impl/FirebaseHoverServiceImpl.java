package com.bees360.service.firebase.impl;

import com.bees360.api.InvalidArgumentException;
import com.bees360.common.util.UUIDUtil;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.enums.AiBotUserEnum;
import com.bees360.entity.enums.PipelineTaskEnum;
import com.bees360.entity.enums.ProjectHoverStatusEnum;
import com.bees360.entity.enums.ProjectReportRecordEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.firebase.HoverJobStatusEnum;
import com.bees360.job.registry.SerializableFirebaseHover;
import com.bees360.mapper.ProjectMapper;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.firebase.FirebaseHoverService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.web.event.project.claim.ProjectClaimEntryRecordEvent;
import java.util.Objects;
import java.util.Optional;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import static com.bees360.entity.enums.ReportTypeEnum.HOVER_ESX;
import static com.bees360.entity.enums.ReportTypeEnum.HOVER_PRO_REPORT;
import static com.bees360.pipeline.Message.PipelineStatus.DONE;
import static com.bees360.pipeline.Message.PipelineStatus.ERROR;
import static com.bees360.pipeline.Message.PipelineStatus.READY;

/** hoverJob 业务 */
@Log4j2
@Service
public class FirebaseHoverServiceImpl implements FirebaseHoverService {

    @Autowired private ProjectMapper projectMapper;
    @Autowired private ProjectReportFileService projectReportFileService;
    @Autowired private ApplicationEventPublisher publisher;
    @Autowired private ResourcePool resourcePool;

    @Autowired private PipelineService pipelineService;

    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @Override
    public void handleHoverJob(SerializableFirebaseHover hoverJob, String hoverJobId) {
        if (StringUtils.isBlank(hoverJob.getProjectId())) {
            log.error(
                    "handle hover job, projectId is null , hoverJobId {}",
                    hoverJobId,
                    new IllegalArgumentException("Hover missing projectId"));
            return;
        }
        Project project = projectMapper.getById(Long.parseLong(hoverJob.getProjectId()));
        if (project == null) {
            log.warn("handle hover job, project not found, hoverJobId {}", hoverJobId);
            return;
        }

        // hover状态变更
        var projectId = project.getProjectId();
        var state = hoverJob.getState();
        updateHoverStatus(project, hoverJob.getState(), hoverJobId);

        boolean fetchFailed = false;
        try {
            saveReportFile(projectId, HOVER_PRO_REPORT, hoverJob.getReport());
        } catch (Exception e) {
            log.error("save hover report failed projectId {}", projectId, e);
            fetchFailed = true;
        }

        try {
            saveReportFile(projectId, HOVER_ESX, hoverJob.getExportESX());
        } catch (Exception e) {
            log.error("save hover ESX report failed projectId {}", projectId, e);
            fetchFailed = true;
        }
        if (Objects.equals(state, HoverJobStatusEnum.COMPLETE.getCode())) {
            var key = PipelineTaskEnum.FETCH_HOVER_REPORT.getKey();
            var pipelineId = String.valueOf(projectId);
            if (fetchFailed) {
                setHoverTaskStatus(pipelineId, key, ERROR);
            } else {
                setHoverTaskStatus(pipelineId, key, DONE);
            }
        }
    }

    /**
     * 保存report文件
     *
     * @param projectId
     * @param reportType 报告类型
     * @param key report url key
     */
    private void saveReportFile(long projectId, ReportTypeEnum reportType, String key) {
        log.info(
                "save report projectId {} reportType {} key {}",
                projectId,
                reportType.getCode(),
                key);
        if (key == null) {
            log.info("report file key is null projectId {}", projectId);
            return;
        }

        var report = projectReportFileService.getReportFile(projectId, reportType.getCode());

        ResourceMetadata head = resourcePool.head(key);
        if (head == null) {
            throw new IllegalArgumentException(
                "report file not found projectId '%s', key '%s'".formatted(projectId, key));
        }
        if (report != null) {
            var existsReportHead = resourcePool.head(report.getReportUrl());
            if (existsReportHead != null && StringUtils.equals(existsReportHead.getETag(), head.getETag())) {
                log.info("report file not change projectId {}", projectId);
                // 存在且地址相同
                return;
            }
        }

        ProjectReportFile file =
                genProjectReportFile(
                        key,
                        projectId,
                        reportType,
                        Optional.ofNullable(head.getContentLength()).map(Long::intValue).orElse(0));
        projectReportFileService.updateReportFile(file);
    }

    /**
     * hover状态变更
     *
     * @param hoverState hover平台最新状态
     * @param hoverJobId hover平台Id
     */
    private void updateHoverStatus(Project project, Integer hoverState, String hoverJobId) {
        log.info(
                "update hover status projectId {}, hoverJobId {}",
                project.getProjectId(),
                hoverJobId);
        if (bees360FeatureSwitch.isEnableCheckHoverStateIdentical()
            && (hoverState == null || Objects.equals(project.getHoverJobStatus(), hoverState))) {
            log.info(
                    "project's hover job hoverState is null or not change hoverJobId {}",
                    hoverJobId);
            return;
        }

        // 状态保存到project
        HoverJobStatusEnum statusEnum = HoverJobStatusEnum.getEnum(hoverState);
        projectMapper.updateHoverJobStatus(project.getProjectId(), statusEnum.getCode());

        var pipelineId = String.valueOf(project.getProjectId());
        var key = PipelineTaskEnum.WAIT_HOVER_COMPLETE.getKey();
        switch (statusEnum) {
            case COMPLETE:
                setHoverTaskStatus(pipelineId, PipelineTaskEnum.UPLOAD_HOVER_IMAGES.getKey(), DONE);
                setHoverTaskStatus(pipelineId, key, DONE);
                break;
            case PROCESSING:
                setHoverTaskStatus(pipelineId, key, READY);
                break;
            case PROCESSING_UPLOAD:
            case PROCESSING_UPLOAD_FOR_IMPROVEMENTS:
                setHoverTaskStatus(pipelineId, PipelineTaskEnum.UPLOAD_HOVER_IMAGES.getKey(), DONE);
                break;
            case CANCELLED:
            case FAILED:
                setHoverTaskStatus(pipelineId, key, ERROR);
                break;
        }
        ProjectHoverStatusEnum projectHoverStatusEnum = statusEnum.getProjectHoverStatusEnum();
        if (projectHoverStatusEnum == null) {
            log.info("hover status change don't need to publish hoverJobId {}", hoverJobId);
            return;
        }
        // 发布event，更新ai端的project状态
        publisher.publishEvent(
                new ProjectClaimEntryRecordEvent(
                        this,
                        project.getProjectId(),
                        ProjectReportRecordEnum.HOVER.getType(),
                        projectHoverStatusEnum.getCode()));
    }

    private void setHoverTaskStatus(String pipelineId, String key, Message.PipelineStatus status) {
        try {
            pipelineService.setTaskStatus(pipelineId, key, status);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Failed to set pipeline '{}' task '{}' to DONE", pipelineId, key);
        } catch (RuntimeException e) {
            log.error("Failed to set pipeline '{}' task '{}' to DONE", pipelineId, key);
        }
    }

    private ProjectReportFile genProjectReportFile(
            String urlKey, long projectId, ReportTypeEnum type, Integer size) {
        ProjectReportFile file = new ProjectReportFile();
        file.setReportId(UUIDUtil.getReportUUID());
        file.setProjectId(projectId);
        file.setCreatedBy(AiBotUserEnum.AI_NEW_USER_ID.getIntegerCode());
        file.setCreatedTime(System.currentTimeMillis());
        file.setDeleted(false);
        file.setRead(false);
        file.setGenerationStatus(ReportGenerationStatusEnum.GENERATED.getCode());
        file.setReportType(type.getCode());
        file.setReportWordFileName("");
        file.setReportPdfFileName(urlKey);
        file.setReportPdfCompressed("");
        file.setSize(size);
        return file;
    }
}
