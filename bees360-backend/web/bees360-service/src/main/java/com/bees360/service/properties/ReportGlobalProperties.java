package com.bees360.service.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Report CompressedSize GlobalProperties
 */
@Data
@Component
@ConfigurationProperties(prefix = "report")
public class ReportGlobalProperties {

    private List<ReportItemProperty> reportItemProperties = new ArrayList<>();

    @Data
    public static class ReportItemProperty {
        private int reportType;
        @Nullable
        private Integer serviceType;
        private Integer compressedSizeLimit;
    }

    /**
     * 根据reportType获得报告压缩大小限制, 查找不限定service type的配置
     */
    public OptionalInt getReportCompressedSizeLimit(int reportType) {
        return getReportCompressedSizeLimit(reportType, null);
    }

    /**
     * 指定service type，如果service type不存在，则返回不限定service type的结果
     */
    public OptionalInt getReportCompressedSizeLimit(int reportType, Integer serviceType) {
        List<ReportItemProperty> items = reportItemProperties.stream()
            .filter(e -> Objects.equals(e.getReportType(), reportType))
            .filter(e -> Objects.nonNull(e.getCompressedSizeLimit()))
            .collect(Collectors.toList());
        OptionalInt result = OptionalInt.empty();
        if (serviceType != null) {
            result = items.stream()
                .filter(e -> Objects.equals(e.getServiceType(), serviceType))
                .mapToInt(ReportItemProperty::getCompressedSizeLimit)
                .min();
        }
        if (result.isEmpty()) {
            // using min when unlimited
            result = items.stream()
                .filter(e -> e.getServiceType() == null)
                .mapToInt(ReportItemProperty::getCompressedSizeLimit)
                .min();
        }
        return result;
    }
}
