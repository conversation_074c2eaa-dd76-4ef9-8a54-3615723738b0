package com.bees360.service.listener.project;

import com.bees360.event.registry.ProjectPolicyEffectedDateChangeEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ActivityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;

/**
 * 监听项目策略生效日期变更事件并处理相关业务逻辑
 */
@Log4j2
@Component
@RequiredArgsConstructor
public class ProjectPolicyEffectedDateChangeEventListener extends AbstractNamedEventListener<ProjectPolicyEffectedDateChangeEvent> {

    private final static String LISTENER_NAME = "projectPolicyEffectedDateChangeEventListener";

    private final ActivityService activityService;

    @Override
    public String getName() {
        return LISTENER_NAME;
    }

    @Override
    public void handle(ProjectPolicyEffectedDateChangeEvent event) throws IOException {
        if (Objects.equals(event.getNewPolicyEffectiveDate(), event.getOldPolicyEffectiveDate())) {
            return;
        }
        activityService.policyEffectiveDateChanged(event.getProjectId(), event.getOperator(),
            event.getNewPolicyEffectiveDate(), event.getOldPolicyEffectiveDate());
    }
}
