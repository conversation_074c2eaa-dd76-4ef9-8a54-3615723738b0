package com.bees360.service.openapi.converter;

import com.bees360.entity.openapi.reportsummary.SummaryFactor;
import com.bees360.entity.openapi.reportsummary.SummaryImage;
import com.bees360.entity.openapi.reportsummary.SummaryRecommendation;
import com.bees360.web.core.json.gson.AbstractNumberTypeAdapter;
import com.bees360.web.core.json.gson.ByteTypeAdapter;
import com.bees360.web.core.json.gson.FloatTypeAdapter;
import com.bees360.web.core.json.gson.IntegerTypeAdapter;
import com.bees360.web.core.json.gson.ShortTypeAdapter;
import com.google.gson.ToNumberPolicy;
import com.google.gson.ToNumberStrategy;
import org.apache.commons.lang3.StringUtils;

import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yang
 */
@Slf4j
public class ReportSummaryConvert {
    private static final Gson gson = newGson();

    private static Gson newGson() {
        GsonBuilder gb = new GsonBuilder();
        gb.setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE);
        registerNumberTypeAdapter(gb);
        Gson gson = gb.create();
        return gson;
    }

    private static void registerNumberTypeAdapter(GsonBuilder gsonBuilder) {
        gsonBuilder.registerTypeAdapter(Double.class, new DoubleTypeAdapter());
        gsonBuilder.registerTypeAdapter(double.class, new DoubleTypeAdapter());
        gsonBuilder.registerTypeAdapter(Float.class, new FloatTypeAdapter());
        gsonBuilder.registerTypeAdapter(float.class, new FloatTypeAdapter());
        gsonBuilder.registerTypeAdapter(Integer.class, new IntegerTypeAdapter());
        gsonBuilder.registerTypeAdapter(int.class, new IntegerTypeAdapter());
        gsonBuilder.registerTypeAdapter(Short.class, new ShortTypeAdapter());
        gsonBuilder.registerTypeAdapter(short.class, new ShortTypeAdapter());
        gsonBuilder.registerTypeAdapter(Byte.class, new ByteTypeAdapter());
        gsonBuilder.registerTypeAdapter(byte.class, new ByteTypeAdapter());
    }

    public static ReportSummaryVo toSummaryVo(String summary) {
        if (StringUtils.isBlank(summary)) {
            return null;
        }
        // TODO 注意多余或者缺少的属性的赋值
        final ReportSummaryVo reportSummaryVo = gson.fromJson(summary, ReportSummaryVo.class);
        filterRecommendation(reportSummaryVo);
        filterFactor(reportSummaryVo);
        return reportSummaryVo;
    }

    public static String toJson(ReportSummaryVo reportSummaryVo) {
        if (reportSummaryVo == null) {
            return null;
        }
        return gson.toJson(reportSummaryVo);
    }

    public static class DoubleTypeAdapter extends AbstractNumberTypeAdapter<Double> {
        @Override
        protected Double convertDouble(double d) {
            return d;
        }
    }

    private static void filterFactor(ReportSummaryVo summaryVo) {
        List<SummaryFactor> factors = summaryVo.getFactors();
        if (CollectionUtils.isEmpty(factors)) {
            summaryVo.setFactors(Collections.EMPTY_LIST);
            return;
        }
        List<SummaryFactor> filteredFactors = factors.stream()
            .filter(Objects::nonNull)
            .peek(fac -> fac.setName(Optional.ofNullable(fac.getName()).filter(StringUtils::isNotBlank).orElse("")))
            .peek(fac -> fac.setText(Optional.ofNullable(fac.getText()).filter(StringUtils::isNotBlank).orElse("")))
            .peek(fac -> {
                List<SummaryImage> images = fac.getImage();
                if (CollectionUtils.isEmpty(images)) {
                    fac.setImage(Collections.EMPTY_LIST);
                    return;
                }
                List<SummaryImage> filteredImgList = images.stream()
                    .filter(Objects::nonNull)
                    .filter(img -> StringUtils.isNotEmpty(img.getId()))
                    .collect(Collectors.toList());
                fac.setImage(filteredImgList);
            })
            .collect(Collectors.toList());
        summaryVo.setFactors(filteredFactors);
    }

    private static void filterRecommendation(ReportSummaryVo summaryVo){
        final List<SummaryRecommendation> recommendations = summaryVo.getRecommendations();
        if (recommendations == null){
            summaryVo.setRecommendations(Collections.EMPTY_LIST);
            return;
        }
        final List<SummaryRecommendation> filteredRecommendations = recommendations.stream()
            .filter(Objects::nonNull)
            .peek(recommendation -> {
                final List<SummaryImage> image = recommendation.getImage();
                if (image == null){
                    recommendation.setImage(Collections.EMPTY_LIST);
                    return;
                }
                final List<SummaryImage> list = image.stream()
                    .filter(Objects::nonNull)
                    .filter(img -> !StringUtils.isEmpty(img.getId()))
                    .collect(Collectors.toList());
                recommendation.setImage(list);
            })
            .collect(Collectors.toList());
        summaryVo.setRecommendations(filteredRecommendations);
    }

}
