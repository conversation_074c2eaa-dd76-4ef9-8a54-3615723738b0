package com.bees360.job;

import com.bees360.api.UnavailableException;
import com.bees360.atomic.LockProvider;
import com.bees360.job.registry.SerializableFirebaseMission;
import com.bees360.job.registry.SerializableFirebaseMissionV2;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseMissionService;
import com.google.cloud.firestore.Firestore;

import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.dao.DeadlockLoserDataAccessException;
import java.io.IOException;

import static com.bees360.service.util.FirebaseExceptionTranslation.translateExceptionAndThrow;


@Log4j2
@ToString
public class FirebaseMissionChangedExecutorV2
        extends AbstractJobExecutor<SerializableFirebaseMissionV2> {
    private final FirebaseMissionService firebaseMissionService;
    private final Firestore firestore;
    private final LockProvider missionLockProvider;

    public FirebaseMissionChangedExecutorV2(
            FirebaseMissionService firebaseMissionService,
            Firestore firestore,
            LockProvider missionLockProvider) {
        this.firebaseMissionService = firebaseMissionService;
        this.firestore = firestore;
        this.missionLockProvider = missionLockProvider;

        log.info(
                "Created '{}(firebaseMissionService={},firestore={},missionLockProvider={}'",
                this,
                this.firebaseMissionService,
                this.firestore,
                this.missionLockProvider);
    }

    @Override
    protected void handle(SerializableFirebaseMissionV2 missionV2) throws IOException {
        SerializableFirebaseMission mission;
        try{
            mission = convert(missionV2);
        } catch (IllegalArgumentException e){
            log.error("Failed to handle mission '{}' completed event.", missionV2, e);
            return;
        }

        var id = mission.getId();
        try (var ignored = missionLockProvider.lock(id)) {
            log.info("Start to handle mission '{}' '{}'", id, mission);
            firebaseMissionService.handlePilotMission(mission, id);
            log.info("Successfully handle mission '{}'", id);
        } catch (DeadlockLoserDataAccessException e) {
            throw new UnavailableException(e.getMessage(), e);
        } catch (RuntimeException e) {
            log.warn("Failed to handle mission '{}' completed event", mission, e);
            translateExceptionAndThrow(e);
        }
    }

    private SerializableFirebaseMission convert(SerializableFirebaseMissionV2 missionV2) throws IllegalArgumentException {
        SerializableFirebaseMission mission = new SerializableFirebaseMission();
        try {
            BeanUtils.copyProperties(mission, missionV2);
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to convert SerializableFirebaseMissionV2 to SerializableFirebaseMission.", e);
        }
        return mission;
    }
}
