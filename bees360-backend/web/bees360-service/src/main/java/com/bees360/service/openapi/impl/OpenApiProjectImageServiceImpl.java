package com.bees360.service.openapi.impl;

import com.bees360.mapper.ProjectImageMapper;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.service.ReportSummaryService;
import com.bees360.util.MapList;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.openapi.OpenImageVo;
import com.bees360.service.openapi.OpenApiProjectImageService;
import com.bees360.service.openapi.converter.ProjectImageConvert;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OpenApiProjectImageServiceImpl implements OpenApiProjectImageService {

    @Autowired
    private ProjectImageMapper projectImageMapper;

    @Autowired
    private ReportSummaryService reportSummaryService;

    @Override
    public List<OpenImageVo> listProjectImage(long projectId) throws ServiceException {
        var projectImages = projectImageMapper.listAllContainDeleted(projectId);
        updateAnnotatedImageTypeAsParent(projectImages);

        removeDeletedAndNotInSummary(projectId, projectImages);

        return projectImages.stream()
            .filter(this::isImageReturnedInOpenApi)
            .map(ProjectImageConvert::toOpenImageVo).collect(Collectors.toList());
    }

    private void updateAnnotatedImageTypeAsParent(List<ProjectImage> projectImages) {
        var idImage = projectImages.stream().collect(Collectors.toMap(ProjectImage::getImageId,
            Function.identity(), (i1, i2) -> i1));
        projectImages.stream().filter(img -> img.getFileSourceType() == FileSourceTypeEnum.REPORT_IMAGE.getCode())
            .forEach(img -> img.setImageType(idImage.getOrDefault(img.getParentId(), img).getImageType()));
    }

    private void removeDeletedAndNotInSummary(long projectId, List<ProjectImage> projectImages) {
        Map<ReportTypeEnum, Set<String>> reportWithImages = reportSummaryService.listImagesInSummary(projectId);
        var imageIds = reportWithImages.values().stream().flatMap(Set::stream).collect(Collectors.toSet());

        projectImages.removeIf(img -> img.getDeleted() && !imageIds.contains(img.getImageId()));
    }

    private boolean isImageReturnedInOpenApi(ProjectImage image) {
        if (StringUtils.isBlank(image.getFileName())) {
            return false;
        }
        return image.getFileSourceType() == FileSourceTypeEnum.DRONE_IMAGE.getCode()
            || image.getFileSourceType() == FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode()
            || image.getFileSourceType() == FileSourceTypeEnum.REPORT_IMAGE.getCode();
    }
}
