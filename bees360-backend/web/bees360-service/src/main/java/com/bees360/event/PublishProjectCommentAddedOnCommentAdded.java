package com.bees360.event;

import com.bees360.event.registry.CommentChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ProjectService;
import com.bees360.web.event.project.ProjectCommentAddedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;

/**
 * 移除spring事物逻辑，将相关逻辑拆分到以下类：
 * {@link UpdateProjectNoteOnCommentChanged}
 * {@link InitLossDescriptionOnCommentChanged}
 */
@Deprecated
public class PublishProjectCommentAddedOnCommentAdded
        extends AbstractNamedEventListener<CommentChangedEvent> {
    @Autowired private ApplicationEventPublisher publisher;
    @Autowired private ProjectService projectService;

    @Override
    public void handle(CommentChangedEvent commentChangedEvent) throws IOException {
        var comment = commentChangedEvent.getSource();
        var projectId = comment.getProjectId();
        var project = projectService.getById(projectId);

        // case还没被创建出来
        if (project == null) {
            return;
        }

        publisher.publishEvent(new ProjectCommentAddedEvent(this, project, comment));
    }

    @Override
    public String getRoutingKey() {
        return "mongo_collection_changed.comment.upsert";
    }
}
