package com.bees360.service.firebase;

import com.bees360.mapper.ProjectMapper;
import com.bees360.schedule.exception.RetryableException;
import com.bees360.schedule.job.retry.RetryableJob;
import com.bees360.schedule.job.retry.RetryableOperation;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;
import com.bees360.schedule.util.QuartzJobConstant;
import com.google.api.core.ApiFuture;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.SetOptions;
import com.google.cloud.firestore.WriteResult;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.ExecutionException;
import java.util.function.Supplier;

@Slf4j
public class SyncProjectToFirebaseRetryJob extends RetryableJob {
    @Autowired private Firestore firestore;
    @Autowired private FirebaseProjectService firebaseProjectService;
    @Autowired private ProjectMapper projectMapper;
    @Setter private long projectId;
    @Setter private String documentPath;

    @Override
    protected RetryableOperation getRetryableOperation() {
        return ctx -> {
            DocumentReference reference = firestore.document(documentPath);
            Object data =
                    firebaseProjectService.getAllFieldsInProject(projectMapper.getById(projectId));
            WriteResult result = fetch(() -> reference.set(data, SetOptions.merge()));
            log.info(
                    "Success sync project '{}' to firebase at '{}'.",
                    projectId,
                    result.getUpdateTime());
        };
    }

    @Override
    protected RetryExceptionHandler getRetryExceptionHandler() {
        return ctx -> {
            String message = "Failed to sync data '%s' to firebase.".formatted(projectId);
            if (ctx.getRetryCount() > 20) {
                log.error(message, ctx.getLastEx());
                return false;
            }
            log.warn(message, ctx.getLastEx());
            return true;
        };
    }

    private WriteResult fetch(Supplier<ApiFuture<WriteResult>> function) throws RetryableException {
        try {
            WriteResult writeResult = function.get().get();
            if (writeResult == null) {
                throw new RetryableException("WriteResult is null");
            }
            return writeResult;
        } catch (InterruptedException | ExecutionException e) {
            throw new RetryableException(e);
        } catch (RuntimeException e) {
            log.error("Failed sync project '{}' to firebase.", projectId);
            throw e;
        }
    }

    public static JobDetail createJobDetail(long projectId, String documentPath) {
        String jobName =
            "transfer-project-%s-to-firebase-%s".formatted(
                projectId, System.currentTimeMillis());
        JobDataMap jobData = new JobDataMap();
        jobData.put("projectId", projectId);
        jobData.put("documentPath", documentPath);
        String description = "Trigger sync project %s to firebase.".formatted(projectId);

        return JobBuilder.newJob(SyncProjectToFirebaseRetryJob.class)
                .withIdentity(jobName, QuartzJobConstant.WebGroup.PROJECT_TRANSFER_TO_FIREBASE)
                .usingJobData(jobData)
                .withDescription(description)
                .requestRecovery(true)
                .build();
    }
}
