//package com.bees360.service.payment;
//
//import java.util.List;
//import java.util.Map;
//import com.bees360.base.exception.ServiceException;
//import com.bees360.entity.vo.CreditCardVo;
//import com.bees360.entity.vo.OrderVo;
//
//public interface PaymentService {
//
//	/**
//	 * check whether the project paid or not
//	 * true : is paid, false : not paid
//	 * @param projectId
//	 * @return
//	 * @throws ServiceException
//	 */
//	public List<Integer> getServiceFeeTypes(long userId, long projectId) throws ServiceException;
//
//
//	/**
//	 * check whether the project paid or not
//	 * @param userId
//	 * @param projectId
//	 * @return
//	 * @throws ServiceException
//	 */
//	public boolean isPaid(long userId, long projectId) throws ServiceException;
//
//	/**
//	 * @param user : user object must contain userId or userName
//	 * @param orderVo
//	 * @param creditCardVo : creditCardVo is null when pay by square
//	 * @param paramMap
//	 * @throws ServiceException
//	 */
//	public void payByCreditCard(long userId, OrderVo orderVo, CreditCardVo creditCardVo, Map<String, String> paramMap)
//			throws ServiceException;
//
//
//	/**
//	 * pay by Wallet Balance
//	 * @param userId
//	 * @param orderVo
//	 * @param paramMap
//	 * @return
//	 * @throws ServiceException
//	 */
//	public Map<String,Object> payByWalletBalance(long userId, OrderVo orderVo, Map<String,String> paramMap)throws ServiceException;
//
//
//	/**
//	 * get paypal payment page info
//	 * @param orderVo
//	 * @param paramMap
//	 * @return
//	 * @throws ServiceException
//	 */
//	public Map<String,Object> payByPage(long userId, OrderVo orderVo, Map<String,String> paramMap)throws ServiceException;
//
//
//	/**
//	 *
//	 * @param orderVo
//	 * @throws ServiceException
//	 */
//	public void paySuccessCallback(String paymentId, String payerId, Map<String,Object> userMap) throws ServiceException;
//
//
//	/**
//	 * pay by project number
//	 * @param userId
//	 * @param orderVo
//	 * @param paramMap
//	 * @return
//	 * @throws ServiceException
//	 */
//	public Map<String,Object> payByProjectNumber(long userId, OrderVo orderVo, Map<String,String> paramMap)throws ServiceException;
//}
