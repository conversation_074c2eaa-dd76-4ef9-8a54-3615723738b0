package com.bees360.service.payment;

import java.util.Map;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.vo.Money;

public interface PriceService {

	/**
	 * Get Report Price by report type
	 * @param serviceFeeType
	 * @return
	 * @throws ServiceException
	 */
	public Map<String,Object> getReportPrice(int serviceFeeType) throws ServiceException;

	/**
	 * get price for project which not yet define reportServiceOption
	 * @param serviceFeeType reportType
	 * @param area
	 * @return
	 * @throws ServiceException
	 */
	public Money getReportPrice(int serviceFeeType, double area) throws ServiceException;


	/**
	 * get report service price for project which already define roofEstimatedAreaItem and reportServiceOption
	 * @param roofEstimatedAreaItem
	 * @param reportServiceOption
	 * @return
	 * @throws ServiceException
	 */
	public Money getReportPrice(int roofEstimatedAreaItem, int reportServiceOption) throws ServiceException;
}
