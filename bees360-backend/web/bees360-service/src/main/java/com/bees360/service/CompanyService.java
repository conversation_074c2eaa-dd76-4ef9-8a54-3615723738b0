package com.bees360.service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Company;
import com.bees360.entity.dto.CompanySearchOption;
import com.bees360.entity.vo.PageResult;
import java.util.List;
import java.util.Set;

public interface CompanyService {

    String BEES360_COMPANY_LIST_CACHE_KEY = "bees360CompanyList";

	public List<Company> listAll() throws ServiceException;

	public List<Company> listByType(Integer companyType) throws ServiceException;

	public List<Company> listWithPrefix(String prefix) throws ServiceException;

	/**
	 * list the companies according the type of company and the prefix of the name.
	 * 	Limit the number of the result by number
	 * @param companyType
	 * @param prefix
	 * @param number
	 * @return
	 * @throws ServiceException
	 */
	public List<Company> listByTypeWithPrefix(Integer companyType, String prefix, Integer number)
			throws ServiceException;

	public Company getById(long companyId);

	public Company getByName(String companyName);

    Long getIdByKey(String companyKey);

	public Company createCompany(String companyName) throws ServiceException;

	public Company createCompany(Company company) throws ServiceException;

	public Company updateCompany(long companyId, Company company) throws ServiceException;

	boolean isCompanyExist(String companyName) throws ServiceException;

	PageResult<Company> pageCompanies(CompanySearchOption option) throws ServiceException;

	Company getByUserId(long userId);

	boolean isCustomerContractSigned();

	Company getDefaultCompany();

    /**
     * 修改公司logo
     * @param companyId
     * @param logo
     * @return
     */
    public Company updateCompanyLogo(long companyId, String logo) throws ServiceException;

    List<Company> listIn(Set<Long> companyIdUserIn);

    boolean isBees360Company(long companyId) throws ServiceException;

    Company updateCompanyKey(long companyId, String companyKey);
}
