package com.bees360.service.event.project;

import com.bees360.entity.Member;
import com.bees360.entity.Project;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.mapper.MemberMapper;
import com.bees360.service.MessageService;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.user.UserAssemble;
import com.bees360.web.event.project.ProjectStatusAssignedToPilotEvent;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/7/23 6:45 PM
 **/
@Slf4j
@Component
public class ProjectStatusAssignedToPilotEventHandler {
    @Autowired
    private MessageService messageService;
    @Resource
    private MemberMapper memberMapper;
    @Resource
    private UserProvider userProvider;

    @EventListener
    private void changeOnAssignedToPilot(ProjectStatusAssignedToPilotEvent event) {
        Project project = event.getProject();
        sendADEmailToHouseOwner(project);
    }

    @Value("${send-ad-email-company-id}")
    private Set<Long> insuranceCompany;
    /**
     * 当第一次分配飞手时，给户主发送邮件
     * 只发送insurance company在 ${send-ad-email-company-id}中配置的且项目为claim的case
     * @param project case
     */
    private void sendADEmailToHouseOwner(Project project) {
        if (! insuranceCompany.contains(project.getInsuranceCompany())
            || project.getClaimType() == ClaimTypeEnum.UNDERWRITING_FIRST_INSPECTION.getCode()) {
            return;
        }
        long projectId = project.getProjectId();
        List<Member> allPilotMembers = memberMapper.listMembersUserIn(Collections.singleton(projectId), null, RoleEnum.PILOT.getCode(), null);
        List<Member> deletePilotMembers = allPilotMembers.stream().filter(Member::getDeleted).collect(Collectors.toList());
        Member activePilotMember = allPilotMembers.stream().filter(a -> !a.getDeleted()).findFirst().orElse(null);
        if (deletePilotMembers.isEmpty() && activePilotMember != null) {
            User user = userProvider.findUserById(activePilotMember.getUserId() + "");
            com.bees360.entity.User pilot = UserAssemble.toWebUser(user);
            messageService.sendAdToHouseOwner(project, pilot);
        }
    }

}
