package com.bees360.service.firebase.impl;

import static com.bees360.entity.enums.SystemTypeEnum.BEES360;
import static com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_CLOSE;

import com.bees360.base.constants.EmailConstant;
import com.bees360.base.exception.ServiceException;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.common.util.PhoneUtils;
import com.bees360.commons.firebasesupport.service.RemoteConfigService;
import com.bees360.entity.BeesPilotBatch;
import com.bees360.entity.EventHistory;
import com.bees360.entity.Member;
import com.bees360.entity.MissionAttachFile;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.ProjectMessage;
import com.bees360.entity.ProjectQuiz;
import com.bees360.entity.User;
import com.bees360.entity.enums.ActivitySourceEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import com.bees360.entity.enums.MissionAttachFileTypeEnum;
import com.bees360.entity.enums.OrientationEnum;
import com.bees360.entity.enums.ProjectMessageTypeEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.QuizTypeEnum;
import com.bees360.entity.enums.RemoteConfigParameter;
import com.bees360.entity.firebase.BatchStatusEnum;
import com.bees360.entity.firebase.FirebaseImage;
import com.bees360.entity.firebase.FirebaseMissionAttachFile;
import com.bees360.entity.firebase.FirebaseQuiz;
import com.bees360.entity.firebase.FirebaseRoom;
import com.bees360.entity.firebase.ImageCategoryEnum;
import com.bees360.entity.firebase.ImageSubCategoryEnum;
import com.bees360.entity.firebase.TaskQuizRemoteConfig;
import com.bees360.entity.firebase.TaskRemoteConfig;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.entity.label.ProjectLabel;
import com.bees360.entity.query.ProjectMessageQuery;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.event.EventPublisher;
import com.bees360.event.registry.PilotCallRecordAddedEvent;
import com.bees360.event.registry.PilotFeedbackAddedEvent;
import com.bees360.firebase.FirebaseApi;
import com.bees360.firebase.entity.FbCheckoutReason;
import com.bees360.firebase.entity.FirebaseCallRecord;
import com.bees360.firebase.entity.FirebaseFeedback;
import com.bees360.firebase.entity.FirebaseTimeline;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.SerializableFirebaseBatch;
import com.bees360.job.registry.SerializableFirebaseMission;
import com.bees360.job.registry.SyncProjectToFirebaseJob;
import com.bees360.mapper.MissionAttachFileMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.ProjectQuizMapper;
import com.bees360.project.state.ProjectStateChangeReasonManager;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.schedule.job.retry.RetryableJobTriggerFactory;
import com.bees360.schedule.service.SchedulerManagerTransactionalDelegate;
import com.bees360.service.ActivityService;
import com.bees360.service.EventHistoryService;
import com.bees360.service.MemberService;
import com.bees360.service.MessageService;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectMessageService;
import com.bees360.service.RoomService;
import com.bees360.service.UserService;
import com.bees360.service.beespilot.BeesPilotBatchService;
import com.bees360.service.beespilot.BeesPilotStatusService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.service.firebase.SyncProjectToFirebaseRetryJob;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.service.properties.FirebaseTransferProperties;
import com.bees360.service.util.FirebaseServiceConfig;
import com.bees360.service.util.ProjectImageReSorterConfig;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import com.bees360.util.user.UserAssemble;
import com.google.api.core.ApiFuture;
import com.google.cloud.Timestamp;
import com.google.cloud.firestore.CollectionReference;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.GeoPoint;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.cloud.firestore.SetOptions;
import com.google.cloud.firestore.WriteResult;
import com.google.common.collect.ImmutableMap;
import com.google.protobuf.ByteString;
import com.google.protobuf.util.Timestamps;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.ExecutionException;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.logging.log4j.util.Strings;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2020/8/25 3:46 下午
 */
@Import({ProjectImageReSorterConfig.class, FirebaseServiceConfig.class})
@Service
@Slf4j
public class FirebaseServiceImpl implements FirebaseService {
    @Resource private ProjectMapper projectMapper;
    @Autowired private BeesPilotStatusService beesPilotStatusService;
    @Autowired private ProjectQuizMapper projectQuizMapper;
    @Autowired private Firestore firestore;
    @Autowired private EventHistoryService eventHistoryService;

    @Autowired private BeesPilotBatchService beesPilotBatchService;

    @Autowired private MessageService messageService;
    @Autowired private ProjectMessageService projectMessageService;
    @Autowired private RemoteConfigService remoteConfigService;
    @Resource private ActivityService activityService;
    @Resource private ProjectLabelService projectLabelService;
    @Resource private SchedulerManagerTransactionalDelegate schedulerManagerTransactionalDelegate;
    @Autowired private MissionAttachFileMapper missionAttachFileMapper;
    @Autowired private ResourcePool resourcePool;
    @Autowired private UserProvider userProvider;
    @Autowired private MemberService memberService;
    @Autowired private RoomService roomService;
    @Autowired private EventPublisher eventPublisher;
    @Autowired private UserService userService;
    @Autowired private JobScheduler jobScheduler;
    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;
    @Autowired private FirebaseTransferProperties firebaseTransferProperties;
    @Autowired(required = false)
    private Supplier<ProjectStateManager> projectStateManagerSupplier;

    @Autowired
    private ProjectStateChangeReasonManager projectStateChangeReasonManager;

    @Autowired
    @Qualifier("projectFeedback2TagIdConverter")
    private Optional<BiFunction<String, String, Long>> projectFeedback2TagIdConverter;

    @Autowired
    @Qualifier("projectFeedback2CloseReasonConverter")
    private Optional<BiFunction<String, String, String>> projectFeedback2CloseReasonConverter;

    @Autowired
    @Qualifier("callRecord2OperationTagConverter")
    private Optional<BiFunction<String, String, Long>> callRecord2OperationTagConverter;

    @Autowired
    @Qualifier("callRecord2ChangeReasonConverter")
    private Optional<BiFunction<String, String, String>> callRecord2ChangeReasonConverter;

    public void syncProjectToFirebaseWithFailedRetry(
            long projectId, String documentPath, Map<String, Object> fields, boolean updated) {
        log.debug("Try to update firebase document for project: {}, fields: {}.", projectId, fields);
        DocumentReference reference = firestore.document(documentPath);
        try {
            // 如果是要更新项目，项目文档不存在则新建文档
            WriteResult result;
            if (updated) {
                result = reference.update(fields).get();
            } else {
                result = reference.set(fields, SetOptions.merge()).get();
            }
            if (result == null) {
                // 触发quartz job
                throw new IllegalStateException("WriteResult is null.");
            }
            log.info(
                    "Success sync project '{}' to firebase at '{}'.",
                    projectId,
                    result.getUpdateTime());
        } catch (InterruptedException | ExecutionException | IllegalStateException e) {
            log.warn("Failed to sync project '{}' into firebase.", projectId, e);
            if (bees360FeatureSwitch.isEnableRabbitJobSyncFirebaseProject()) {
                syncProjectToFirebase(projectId);
                return;
            }
            JobDetail jobDetail =
                    SyncProjectToFirebaseRetryJob.createJobDetail(projectId, documentPath);
            JobKey jobKey = jobDetail.getKey();
            // 一分钟之后同步
            Trigger trigger =
                    RetryableJobTriggerFactory.retryForeverTriggerStartAt(
                                    jobKey.getName(),
                                    jobKey.getGroup(),
                                    Duration.ofMinutes(5),
                                    DateUtils.addMinutes(new Date(), 1))
                            .build();
            try {
                schedulerManagerTransactionalDelegate.scheduleJob(jobDetail, trigger);
            } catch (SchedulerException schedulerException) {
                String message =
                    "Failed to schedule quartz job to sync project '%s' to firebase".formatted(
                        projectId);
                log.error(message, schedulerException);
                throw new IllegalStateException(message, e);
            }
        }
    }

    public void syncProjectToFirebase(long projectId, boolean updated) {
        var synProjectToFirebaseJob = new SyncProjectToFirebaseJob();
        synProjectToFirebaseJob.setProjectId(projectId);
        synProjectToFirebaseJob.setUpdated(updated);
        var job = Job.ofPayload(synProjectToFirebaseJob);
        var retryableJob =
                RetryableJob.of(
                        job,
                        firebaseTransferProperties.getRetryCount(),
                        firebaseTransferProperties.getRetryDelay(),
                        firebaseTransferProperties.getRetryDelayIncreaseFactor());
        jobScheduler.schedule(retryableJob);
    }

    @Override
    public void updateNoteToFirebase(long projectId) {
        String note = activityService.getNote(projectId);
        var documentPath  = getProjectDocumentPath(projectId);
        var reference = firestore.document(documentPath).get();
        if (Strings.isBlank(note) || !FirebaseApi.fetch(reference).exists()) {
            return;
        }
        syncProjectToFirebaseWithFailedRetry(
                projectId, documentPath, ImmutableMap.of("note", note), true);
    }

    private String getMissionDocumentPath(String missionId) {
        return MISSION_COLLECTION + "/" + missionId;
    }

    @Override
    public String getProjectDocumentPath(long projectId) {
        return PROJECT_COLLECTION + '/' + projectId;
    }

    @Override
    public String getMissionDocumentKey(long projectId, String batchNo) {
        return batchNo + "_" + projectId;
    }

    @SuppressWarnings("unchecked")
    public void syncQuizAnswers(long projectId, CollectionReference quizReference) {
        // try catch, 防止影响其他业务
        try {
            List<FirebaseQuiz> latestAnswers = getQuizAnswersByProjectId(projectId, quizReference);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(latestAnswers)) return;

            // 现存的最新答案
            Project project = projectMapper.getById(projectId);
            List<ProjectQuiz> existingAnswers =
                    projectQuizMapper.listLatestAnswers(
                            projectId, Optional.ofNullable(project.getClaimType()).orElse(0));
            Map<Long, ProjectQuiz> projectQuizMap =
                    existingAnswers.stream()
                            .collect(
                                    Collectors.toMap(
                                            ProjectQuiz::getQuizId,
                                            Function.identity(),
                                            (a, b) -> a));

            // 获取题目类型
            List<TaskQuizRemoteConfig> quizList =
                    remoteConfigService.getRemoteConfig(
                            TaskQuizRemoteConfig.class, RemoteConfigParameter.QUIZ.getName());
            Map<Long, Integer> quizConfigMap =
                    quizList.stream()
                            .collect(
                                    Collectors.toMap(
                                            TaskQuizRemoteConfig::getQuizId,
                                            TaskQuizRemoteConfig::getType,
                                            (a, b) -> a));

            List<ProjectQuiz> newAnswers = new ArrayList<>();
            long now = new Date().getTime();
            latestAnswers.forEach(
                    e -> {
                        ProjectQuiz projectQuiz = projectQuizMap.get(e.getId());
                        QuizTypeEnum quizTypeEnum =
                                QuizTypeEnum.valueOf(quizConfigMap.get(e.getId()));
                        if (Objects.isNull(quizTypeEnum)) {
                            return;
                        }
                        // 无，插入新的答案
                        if (Objects.isNull(projectQuiz)) {
                            newAnswers.add(
                                    genProjectQuiz(
                                            e,
                                            projectId,
                                            project.getClaimType(),
                                            now,
                                            quizTypeEnum));
                            return;
                        }

                        // 已存在答案,更新答案
                        String answer =
                                ProjectQuiz.genAnswer(
                                        quizTypeEnum,
                                        e.getAnswers().toArray(new String[0]),
                                        e.getComment());
                        if (!Objects.equals(answer, projectQuiz.getAnswer())) {
                            projectQuizMapper.updateAnswers(projectQuiz.getProjectQuizId(), answer);
                        }
                    });
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(newAnswers)) {
                projectQuizMapper.batchInsertAnswer(newAnswers);
                beesPilotStatusService.quizCompleted(projectId, true);
            }
        } catch (Exception e) {
            log.error("sync quiz answers error projectId {}", projectId, e);
        }
    }

    private ProjectQuiz genProjectQuiz(
            FirebaseQuiz v, long projectId, Integer claimType, long createTime, QuizTypeEnum type) {
        ProjectQuiz projectQuiz = new ProjectQuiz();
        projectQuiz.setAnswers(type, v.getAnswers().toArray(new String[0]), v.getComment());
        projectQuiz.setQuizId(v.getId());
        projectQuiz.setProjectId(projectId);
        projectQuiz.setClaimType(claimType);
        projectQuiz.setCreateTime(createTime);
        return projectQuiz;
    }

    /**
     * 根据projectId获取问卷答案列表
     *
     * @param projectId
     * @param quizReference
     * @return project对应的问卷答案
     */
    private List<FirebaseQuiz> getQuizAnswersByProjectId(
            long projectId, CollectionReference quizReference) {
        List<FirebaseQuiz> latestAnswers = new ArrayList<>();
        try {
            ApiFuture<QuerySnapshot> futureResult = quizReference.get();
            List<QueryDocumentSnapshot> documentSnapshotList = futureResult.get().getDocuments();
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(documentSnapshotList)) {
                return Collections.emptyList();
            }
            latestAnswers =
                    documentSnapshotList.stream()
                            .map(e -> e.toObject(FirebaseQuiz.class))
                            .collect(Collectors.toList());
        } catch (ExecutionException | InterruptedException e) {
            log.error("query quiz answers failed projectId {}", projectId, e);
        }
        return latestAnswers;
    }

    public List<ProjectImage> getUploadedProjectImages(
            long projectId,
            long pilotId,
            CollectionReference firebaseImages,
            Map<String, FirebaseRoom> roomMap) {
        return getUploadedProjectImages(
                getProjectImages(
                        projectId, pilotId, getProjectImageSnapshots(firebaseImages), roomMap));
    }

    @Override
    public List<ProjectImage> getUploadedProjectImages(List<ProjectImage> images) {
        return images.stream()
                .filter(image -> image.getUploadTime() != 0)
                .collect(Collectors.toUnmodifiableList());
    }

    private List<QueryDocumentSnapshot> getProjectImageSnapshots(
            CollectionReference firebaseImages) {
        try {
            return firebaseImages.get().get().getDocuments();
        } catch (InterruptedException | ExecutionException e) {
            String message =
                "Get snapshot of firebase image documents '%s' failed".formatted(
                    firebaseImages.getId());
            log.error(message, e);
            throw new IllegalStateException(message, e);
        }
    }

    private List<ProjectImage> getProjectImages(
            long projectId,
            long pilotId,
            List<QueryDocumentSnapshot> imageDocuments,
            Map<String, FirebaseRoom> roomMap) {
        Set<Integer> overviewExisted = new HashSet<>();
        List<ProjectImage> images = new ArrayList<>();
        for (QueryDocumentSnapshot imageSnapshot : imageDocuments) {
            FirebaseImage firebaseImage = imageSnapshot.toObject(FirebaseImage.class);
            computeFirebaseImageKey(firebaseImage);
            Timestamp uploadTime = firebaseImage.getUploadTime();
            Timestamp shootTime = firebaseImage.getShootingTime();
            long uploadTimeLong = 0L;
            if (uploadTime != null) {
                uploadTimeLong = uploadTime.toDate().getTime();
            }
            long shootTimeLong = 0L;
            if (shootTime != null) {
                shootTimeLong = shootTime.toDate().getTime();
            }
            ImageCategoryEnum categoryEnum =
                    ImageCategoryEnum.getEnum(firebaseImage.getCategoryId());
            // 如果图片分类不存在，应该报错。
            if (categoryEnum == null) {
                log.error(
                        "Failed got project {} image {} category {}",
                        projectId,
                        firebaseImage.getFileName(),
                        firebaseImage.getCategoryId());
                continue;
            }
            Integer imageType = categoryEnum.getImageType();
            if (imageType == null) {
                if (!overviewExisted.contains(categoryEnum.getCategoryId())) {
                    imageType = ImageTypeEnum.OVERVIEW.getCode();
                    overviewExisted.add(categoryEnum.getCategoryId());
                } else {
                    imageType = ImageTypeEnum.CLOSEUP.getCode();
                }
            }
            ProjectImage image = new ProjectImage();
            BeanUtils.copyProperties(firebaseImage, image);
            image.setFileSourceType(categoryEnum.getFileSourceType());

            image.setImageType(imageType);
            var partialType =
                    bees360FeatureSwitch.isEnableGetPartialTypeBySubCategory()
                            ? getPartialType(firebaseImage.getSubCategoryId(), categoryEnum)
                            : categoryEnum.getPartialType();
            image.setPartialType(partialType);
            image.setOrientation(categoryEnum.getOrientation());
            image.setUploadTime(uploadTimeLong);
            image.setShootingTime(shootTimeLong);
            var uploadBy =
                    Optional.ofNullable(firebaseImage.getUploadBy())
                            .map(userService::toWebUserId)
                            .orElse(null);

            if (!bees360FeatureSwitch.isEnableSaveImagePilot()) {
                image.setUserId(Optional.ofNullable(uploadBy).orElse(pilotId));
            } else {
                // 认为任务的飞手是上传图片用户
                image.setUserId(pilotId);
            }
            image.setProjectId(projectId);
            image.setImageSort(shootTimeLong);

            // 当前ImageCategory字段未使用，因此存储在该字段里，用于将BeesPath同步给AI端用于tag的解析
            image.setImageCategory(firebaseImage.getBeesPath());
            image.setCategoryId(firebaseImage.getCategoryId());
            image.setSubCategoryId(firebaseImage.getSubCategoryId());
            GeoPoint gps = firebaseImage.getGps();
            if (gps != null) {
                image.setGpsLocationLatitude(gps.getLatitude());
                image.setGpsLocationLongitude(gps.getLongitude());
            }

            OrientationEnum orientationEnum =
                    OrientationEnum.getEnum(firebaseImage.getOrientation());
            if (orientationEnum != null) {
                image.setOrientation(orientationEnum.getCode());
            }
            image.setDescription(firebaseImage.getDescription());

            // room tag
            if (firebaseImage.getRoom() != null) {
                roomService.imageSetRoomFromFirebaseRoom(
                        roomMap.get(firebaseImage.getRoom().getPath()), image);
            }
            log.debug("Get project image :{} from firebase.", image);

            images.add(image);
        }
        return images;
    }

    /**
     * get partial type by subCategory and category
     * @param subCategoryId subCategory Id
     * @param category category enum
     * @return partialType
     */
    public static int getPartialType(Integer subCategoryId, ImageCategoryEnum category) {
        if (subCategoryId == null) {
            return category.getPartialType();
        }
        // subCategory是更细的分类，如果存在subCategory存在应该有限取用
        return Optional.ofNullable(ImageSubCategoryEnum.getEnum(subCategoryId))
                .map(ImageSubCategoryEnum::getPartialType)
                .orElse(category.getPartialType());
    }

    private void computeFirebaseImageKey(FirebaseImage firebaseImage) {
        var projectId = firebaseImage.getProjectId();
        var originalFileName = firebaseImage.getOriginalFileName();
        var frebaseImagekey = DigestUtils.md5Hex(String.join("_", projectId, originalFileName));
        firebaseImage.setFirebaseImageKey(frebaseImagekey);
    }

    public void addCheckoutReason(
            long projectId, long pilotId, List<FbCheckoutReason> checkoutReasons) {
        if (!CollectionUtils.isEmpty(checkoutReasons)) {
            // 取出历史checkoutReason记录的数据
            List<Long> messageTimes =
                    projectMessageService
                            .listMessage(
                                    ProjectMessageQuery.builder()
                                            .isDeleted(false)
                                            .projectId(projectId)
                                            .type(
                                                    ProjectMessageTypeEnum.PILOT_EARLY_CHECKOUT
                                                            .getCode())
                                            .build())
                            .stream()
                            .map(ProjectMessage::getCreateTime)
                            .collect(Collectors.toList());
            // 得到一条最新的数据
            FbCheckoutReason checkoutReason =
                    checkoutReasons.stream()
                            .filter(
                                    o ->
                                            CollectionUtils.isEmpty(messageTimes)
                                                    || !messageTimes.contains(
                                                            o.getTime().toSqlTimestamp().getTime()))
                            .max(Comparator.comparing(FbCheckoutReason::getTime))
                            .orElse(null);
            if (Objects.nonNull(checkoutReason)) {
                Map<String, TaskRemoteConfig> taskRemoteConfigMap =
                        remoteConfigService
                                .getRemoteConfig(
                                        TaskRemoteConfig.class,
                                        RemoteConfigParameter.TASK.getName())
                                .stream()
                                .collect(Collectors.toMap(TaskRemoteConfig::getTaskId, a -> a));
                String note =
                        checkoutReason.getTaskReason().stream()
                                .map(
                                        o ->
                                                taskRemoteConfigMap
                                                                .get(o.getTaskId())
                                                                .getReasonTitle()
                                                        + ": "
                                                        + o.getText())
                                .collect(Collectors.joining(";\r\n"));
                ProjectMessage message =
                        ProjectMessage.builder()
                                .createTime(checkoutReason.getTime().toSqlTimestamp().getTime())
                                .content(note)
                                .projectId(projectId)
                                .senderId(pilotId)
                                .type(ProjectMessageTypeEnum.PILOT_EARLY_CHECKOUT.getCode())
                                .build();
                if (bees360FeatureSwitch.isEnabledSourceInCheckout()) {
                    message.setSource(ActivitySourceEnum.BEESPILOT_APP.getDisplay());
                }
                projectMessageService.addMessageAndSyncToAi(message);
            }
        }
    }

    @Override
    public void syncCallRecord(List<FirebaseCallRecord> callRecords, long projectId, long webPilotId, String pilotId) {
        if (CollectionUtils.isEmpty(callRecords)) {
            return;
        }
        ProjectMessageQuery query =
                ProjectMessageQuery.builder()
                        .senderId(webPilotId)
                        .projectId(projectId)
                        .type(ProjectMessageTypeEnum.PHONE_CALLED_RECORD.getCode())
                        .build();
        ProjectMessage latestMessage = projectMessageService.getLatestMessage(query);
        long latestMessageTime =
                Optional.ofNullable(latestMessage).map(ProjectMessage::getCreateTime).orElse(0L);
        for (FirebaseCallRecord record : callRecords) {
            long calledInMills = Timestamps.toMillis(record.getTime().toProto());
            if (latestMessageTime < calledInMills) {
                String content =
                    "Called the insured on %s".formatted(
                        PhoneUtils.format(record.getInsuredPhone()));
                ProjectMessage message =
                        ProjectMessage.builder()
                                .content(content)
                                .createTime(calledInMills)
                                .projectId(projectId)
                                .type(ProjectMessageTypeEnum.PHONE_CALLED_RECORD.getCode())
                                .senderId(webPilotId)
                                .build();
                // 存到project message 表的目的目前只是记录message的最后记录时间
                projectMessageService.addMessageAndSyncToAiWithoutActivity(message);
                publishCallRecordEvent(projectId, pilotId, record);
                log.info("disable new sync call record function :{}", bees360FeatureSwitch.isDisableNewFirebaseSyncFunction());
                if (!bees360FeatureSwitch.isDisableNewFirebaseSyncFunction()
                        && enabledNewFirebaseSyncFunctionForPilot(webPilotId)) {
                    handleProjectByCallResult(projectId, record.getId(), webPilotId);
                }
            }
        }
    }

    // 为部分飞手打开new firebase同步功能。默认所有飞手均打开此功能。控制这部分功能是否生效由其他开关负责。
    private boolean enabledNewFirebaseSyncFunctionForPilot(long pilot) {
        var enabledPilots = bees360FeatureSwitch.getEnableNewFirebasePilot();
        return CollectionUtils.isEmpty(enabledPilots) || enabledPilots.contains(pilot);
    }

    private void publishCallRecordEvent(long projectId, String createdBy, FirebaseCallRecord callRecord) {
        var event = new PilotCallRecordAddedEvent();
        event.setCreatedBy(createdBy);
        event.setProjectId(String.valueOf(projectId));
        event.setType(callRecord.getType());
        event.setName(callRecord.getName());
        event.setCreatedTime(Timestamps.toMillis(callRecord.getTime().toProto()));
        event.setContent(callRecord.getContent());

        eventPublisher.publish(event);
        log.info("Publish webhook event {} by firebase call record update.", event);
    }

    private void publishPilotFeedbackEvent(long projectId, String createdBy, FirebaseFeedback feedback) {
        var event = new PilotFeedbackAddedEvent();
        event.setProjectId(String.valueOf(projectId));
        event.setCreatedBy(createdBy);
        event.setContent(feedback.getContent());
        event.setCreatedTime(Timestamps.toMillis(feedback.getCreatedTime().toProto()));

        eventPublisher.publish(event);
        log.info("Publish webhook event {} by firebase feedback update.", event);
    }

    public void feedBackChanged(SerializableFirebaseMission mission, String missionId) {
        List<FirebaseFeedback> firebaseFeedbacks = mission.getFeedback();
        try {
            long projectId = Long.parseLong(mission.getProject().getProjectId());
            String pilotId = mission.getPilotId();
            long webPilotId = toWebUserId(pilotId);
            syncFeedback(projectId, webPilotId, pilotId, firebaseFeedbacks);
        } catch (Exception e) {
            String message =
                "Sync feedback of mission '%s' into web failed.".formatted(missionId);
            log.error(message, e);
        }
    }

    @Override
    public void syncFeedback(long projectId, long webPilotId, String pilotId, List<FirebaseFeedback> feedbacks) {
        insertFeedbackToProjectMessage(projectId, webPilotId, pilotId, feedbacks);
    }

    private void insertFeedbackToProjectMessage(
            long projectId, long webPilotId, String pilotId, List<FirebaseFeedback> feedbacks) {
        if (!CollectionUtils.isEmpty(feedbacks)) {
            ProjectMessageQuery query =
                    ProjectMessageQuery.builder()
                            .isDeleted(false)
                            .projectId(projectId)
                            .senderId(webPilotId)
                            .type(ProjectMessageTypeEnum.PILOT_FEEDBACK.getCode())
                            .build();
            long lastestMessageTime =
                    Optional.ofNullable(projectMessageService.getLatestMessage(query))
                            .map(ProjectMessage::getCreateTime)
                            .orElse(0L);
            for (FirebaseFeedback feedback : feedbacks) {
                try {
                    long feedbackCreateTime = feedback.getCreatedTime().toDate().getTime();
                    if (feedbackCreateTime > lastestMessageTime) {
                        log.info("Start sync feedback of pilot '{}' into web.", webPilotId);
                        projectMessageService.addMessageAndSyncToAiWithoutActivity(
                                ProjectMessage.builder()
                                        .projectId(projectId)
                                        .senderId(webPilotId)
                                        .type(ProjectMessageTypeEnum.PILOT_FEEDBACK.getCode())
                                        .content(feedback.getMessage())
                                        .createTime(feedbackCreateTime)
                                        .build());
                        publishPilotFeedbackEvent(projectId, pilotId, feedback);
                        infoPilotFeedback(webPilotId, projectId, feedback);
                        if (!bees360FeatureSwitch.isDisableNewFirebaseSyncFunction()
                                && enabledNewFirebaseSyncFunctionForPilot(webPilotId)) {
                            handleProjectByFeedback(projectId, feedback.getTemplate(), webPilotId);
                        } else {
                            updateProjectTag(projectId, feedback.getTemplate(), webPilotId);
                        }
                        log.info("Success sync feedback of pilot '{}' into web.", webPilotId);
                    }
                } catch (Exception e) {
                    String message =
                        "Insert feedback into project message failed. Project is '%s', pilot is '%s'".formatted(
                            projectId, webPilotId);
                    log.error(message, e);
                }
            }
        }
    }
    private boolean isPilotActive(long pilotId, long projectId) {
        Member member = memberService.getActivePilot(projectId);
        return member != null && Objects.equals(member.getUserId(), pilotId);
    }

    private void updateProjectTag(long projectId, FirebaseFeedback.Template template, long pilotId) {
        if (!isPilotActive(pilotId, projectId)) {
            return;
        }
        if (template != null) {
            String feedbackTagName = template.getTemplateName();
            FirebaseFeedback.TemplateEnum labelEnum = FirebaseFeedback.TemplateEnum.getEnum(feedbackTagName);
            Optional.ofNullable(labelEnum)
                .map(FirebaseFeedback.TemplateEnum::getTagId)
                .ifPresent(tagId -> doUpdateProjectTag(projectId, tagId, pilotId));
        }
    }

    private void handleProjectByFeedback(long projectId, FirebaseFeedback.Template template, long pilotId) {
        if (!isPilotActive(pilotId, projectId)) {
            return;
        }
        log.info("Start to handle project :{} by feedback :{}", projectId, template);
        if (template != null) {
            String feedbackTagName = template.getTemplateName();
            var tagId =
                    projectFeedback2TagIdConverter
                            .map(
                                    function ->
                                        function.apply(
                                                    String.valueOf(projectId), feedbackTagName))
                            .orElse(null);
            if (tagId != null) {
                doUpdateProjectTag(projectId, tagId, pilotId);
            } else if (projectStateManagerSupplier != null) {
                log.info("start to handle feedback :{} in project :{}", feedbackTagName, projectId);
                projectFeedback2CloseReasonConverter
                        .map(function -> function.apply(String.valueOf(projectId), feedbackTagName))
                        .ifPresent(
                                closeReason ->
                                        projectStateManagerSupplier
                                                .get()
                                                .changeProjectState(
                                                        String.valueOf(projectId),
                                                        PROJECT_CLOSE,
                                                        findStateChangeReasonIdByKey(closeReason),
                                                        String.valueOf(pilotId),
                                                        null,
                                                        null));
            }
        }
    }

    private String findStateChangeReasonIdByKey(String changeReasonKey) {
        var projectStateChangeReason = Iterables.toStream(
            projectStateChangeReasonManager.findByQuery(List.of(), List.of(changeReasonKey), List.of())).findFirst().orElse(null);
        if (Objects.isNull(projectStateChangeReason)) {
            throw new IllegalStateException("No change reason with key %s found".formatted(changeReasonKey));
        }
        return projectStateChangeReason.getId();
    }

    private void handleProjectByCallResult(long projectId, String callRecordKey, long pilotId) {
        if (!isPilotActive(pilotId, projectId)) {
            return;
        }
        log.info(
                "start to update project :{} operation tag by call record :{}",
                projectId,
                callRecordKey);
        if (callRecordKey != null) {
            var operationTag =
                    callRecord2OperationTagConverter
                            .map(
                                    function ->
                                            function.apply(
                                                    String.valueOf(projectId), callRecordKey))
                            .orElse(null);
            var closeReason =
                    callRecord2ChangeReasonConverter
                            .map(
                                    function ->
                                            function.apply(
                                                    String.valueOf(projectId), callRecordKey))
                            .orElse(null);
            if (operationTag != null) {
                doUpdateProjectTag(projectId, operationTag, pilotId);
            } else if (closeReason != null && projectStateManagerSupplier != null) {
                projectStateManagerSupplier
                        .get()
                        .changeProjectState(
                                String.valueOf(projectId),
                                PROJECT_CLOSE,
                                findStateChangeReasonIdByKey(closeReason),
                                String.valueOf(pilotId),
                                null,
                                null);
            }
        }
    }

    private void doUpdateProjectTag(long projectId, Long projectTag, long pilotId) {
        List<Long> newTag =
            Optional.ofNullable(projectTag)
                .map(Collections::singletonList)
                .orElse(Collections.emptyList());
        List<Long> projectLabels =
            projectLabelService
                .projectLabel(projectId)
                .map(BoundProjectLabel::getProjectLabels)
                .map(
                    a ->
                        a.stream()
                            .map(ProjectLabel::getLabelId)
                            .collect(Collectors.toList()))
                .orElseGet(Collections::emptyList);
        if (!Objects.equals(newTag, projectLabels)) {
            log.info(
                "Start to update project '{}' tag from '{}' to '{}'.",
                projectId,
                Arrays.toString(projectLabels.toArray()),
                Arrays.toString(newTag.toArray()));
            projectLabelService.markAfterEraseLabel(projectId, newTag, pilotId, BEES360);
            log.info(
                "Success to update project '{}' tag from '{}' to '{}'.",
                projectId,
                Arrays.toString(projectLabels.toArray()),
                Arrays.toString(newTag.toArray()));
        }
    }

    private void infoPilotFeedback(Long pilotId, Long projectId, FirebaseFeedback feedback) {
        com.bees360.user.User user = userProvider.findUserById(pilotId + "");
        if (user == null) {
            log.warn("User not found with id:" + pilotId);
            return;
        }
        User pilot = UserAssemble.toWebUser(user);
        UserTinyVo recipient = new UserTinyVo();
        recipient.setEmail(EmailConstant.ADMIN_FOR_PILOT_EMAIL);
        messageService.infoAdminPilotFeedback(pilot, recipient, projectId, feedback);
    }

    @Override
    public synchronized void syncBatchToWeb(SerializableFirebaseBatch batch, String batchId) {
        // 目前只是简单地将batch从firebase同步到web
        List<Long> projects =
                batch.getProject().keySet().stream()
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
        log.info("Start to sync batch '{}' status '{}' to web.", batchId, batch.getStatus());
        beesPilotBatchService.updateOrAddBatchDirectly(ofBeesPilotBatch(batch, batchId), projects);
        log.info("Success sync batch '{}' status '{}' to web.", batchId, batch.getStatus());
    }

    private BeesPilotBatch ofBeesPilotBatch(SerializableFirebaseBatch batch, String batchId) {
        BatchStatusEnum batchStatusEnum = BatchStatusEnum.getEnum(batch.getStatus());
        return BeesPilotBatch.builder()
                .batchNo(batchId)
                .userId(toWebUserId(batch.getPilotId()))
                .basePay(BigDecimal.valueOf(batch.getBasePay()))
                .extraPay(BigDecimal.valueOf(batch.getExtraPay()))
                .planPaymentDate(
                        Optional.ofNullable(batch.getPlanPaymentDate())
                                .map(
                                        a ->
                                                Instant.ofEpochMilli(a)
                                                        .atZone(TimeZone.getDefault().toZoneId())
                                                        .toLocalDate())
                                .orElse(null))
                .note(batch.getNote())
                .payTime(batch.getPayTime())
                .createdBy(User.BEES_PILOT_SYSTEM)
                .updatedAt(java.sql.Timestamp.from(Instant.ofEpochMilli(batch.getUpdatedAt())))
                .isDeleted(Optional.ofNullable(batch.getIsDeleted()).orElse(Boolean.FALSE))
                .createdAt(java.sql.Timestamp.from(Instant.ofEpochMilli(batch.getCreatedAt())))
                .isPendingAcceptance(
                        Objects.equals(batch.getStatus(), BatchStatusEnum.PENDING.getValue()))
                .status(batchStatusEnum.getStatus())
                .reason(batch.getReason())
                .build();
    }

    public void syncTimeline(long projectId, List<FirebaseTimeline> timelines, long userId) {
        if (!CollectionUtils.isEmpty(timelines)) {
            long lastCreatedTime = 0;
            EventHistory history = eventHistoryService.getLastEventHistory(projectId);
            if (history != null) {
                lastCreatedTime = history.getCreatedTime();
            }

            for (FirebaseTimeline timeline : timelines) {
                long createdTime = timeline.getCreatedTime().toDate().getTime();
                ProjectStatusEnum projectStatusEnum = null;
                if (lastCreatedTime < createdTime) {
                    try {
                        projectStatusEnum = ProjectStatusEnum.getEnum(timeline.getCode());
                        eventHistoryService.insertHistoryToProject(
                                projectStatusEnum, projectId, userId, createdTime);
                    } catch (RuntimeException | ServiceException e) {
                        // 同步timeline失败不需要抛出异常，不影响正常流程
                        log.warn(
                                "Failed to sync timeline '{}' '{}' to web.It will not infect the normal process.",
                                timeline,
                                projectStatusEnum);
                    }
                }
            }
        }
    }

    /**
     * 同步问卷附件
     *
     * @param projectId
     * @param missionId
     */
    public void syncAttachFiles(long projectId, String missionId) {
        try {
            log.info("sync attach file begin projectId {} missionId {}", projectId, missionId);
            List<FirebaseMissionAttachFile> files = getFirebaseMissionAttachFiles(missionId);
            if (CollectionUtils.isEmpty(files)) {
                log.info("attach file is empty projectId {} missionId {}", projectId, missionId);
                return;
            }

            List<MissionAttachFile> insertList = new ArrayList<>();

            files.forEach(
                    file ->
                            // data为空说明还未上传到 s3和web端
                            Optional.ofNullable(file.getBase64Data())
                                    .ifPresent(
                                            e -> {
                                                uploadMissionAttachFile(
                                                        file.getUrl(), file.getBase64Data());
                                                wipeOutMissionFileData(missionId, file.getId());
                                                insertList.add(
                                                        genMissionAttachFile(
                                                                file, projectId, missionId));
                                            }));

            if (CollectionUtils.isEmpty(insertList)) {
                log.info(
                        "insert attach file is empty projectId {} missionId {}",
                        projectId,
                        missionId);
                return;
            }
            missionAttachFileMapper.batchInsert(insertList);
            log.info("attach file sync successful projectId {} missionId {}", projectId, missionId);
        } catch (Exception e) {
            log.error("sync mission attach files failed projectId {}", projectId, e);
        }
    }

    /**
     * 获取问卷附件
     *
     * @param missionId
     * @return
     */
    private List<FirebaseMissionAttachFile> getFirebaseMissionAttachFiles(String missionId) {
        try {
            ApiFuture<QuerySnapshot> futureResult =
                    firestore
                            .document(getMissionDocumentPath(missionId))
                            .collection(ATTACH_FILE)
                            .get();
            List<QueryDocumentSnapshot> documentSnapshotList = futureResult.get().getDocuments();

            if (CollectionUtils.isEmpty(documentSnapshotList)) {
                return List.of();
            }
            return documentSnapshotList.stream()
                    .map(
                            e -> {
                                FirebaseMissionAttachFile file =
                                        e.toObject(FirebaseMissionAttachFile.class);
                                file.setId(e.getId());
                                return file;
                            })
                    .filter(e -> !e.getDeleted())
                    .collect(Collectors.toList());
        } catch (ExecutionException | InterruptedException e) {
            log.error("query mission attach files failed missionId {}", missionId, e);
        }
        return List.of();
    }

    /**
     * 上传mission attach file到s3
     *
     * @param urlKey
     * @param fileData base64 data
     */
    private void uploadMissionAttachFile(String urlKey, String fileData) {
        String data = StringUtils.split(fileData, ",")[1];
        byte[] imageByteArray = Base64.getDecoder().decode(data);
        com.bees360.resource.Resource resource =
                com.bees360.resource.Resource.of(ByteString.copyFrom(imageByteArray));
        ResourceMetadata metadata =
                resource.getMetadata().toBuilder()
                        .setContentType(ContentType.IMAGE_PNG.getMimeType())
                        .build();
        resource = com.bees360.resource.Resource.of(ByteString.copyFrom(imageByteArray), metadata);
        resourcePool.put(urlKey, resource);
    }

    /**
     * 清除mission里的file data
     *
     * @param missionId
     * @param attachFileId
     */
    private void wipeOutMissionFileData(String missionId, String attachFileId) {
        Map<String, Object> map = new HashMap();
        map.put("base64Data", null);

        DocumentReference docRef =
                firestore
                        .collection(MISSION_COLLECTION)
                        .document(missionId)
                        .collection("attach_file")
                        .document(attachFileId);
        try {
            ApiFuture<WriteResult> result = docRef.update(map);
            result.get();
        } catch (Exception e) {
            log.error(
                    "Fail to wipe out mission attach file data mission({}, {}).",
                    missionId,
                    attachFileId,
                    e);
        }
    }

    private MissionAttachFile genMissionAttachFile(
            FirebaseMissionAttachFile t, long projectId, String missionId) {
        MissionAttachFile v = new MissionAttachFile();
        v.setProjectId(projectId);
        v.setMissionId(missionId);
        v.setUrl(t.getUrl());
        v.setType(MissionAttachFileTypeEnum.valueOfDisplay(t.getType()).getCode());
        v.setDeleted(t.getDeleted());
        v.setUploadTime(t.getUploadTime().toSqlTimestamp().getTime());
        v.setCreateTime(t.getCreateTime().toSqlTimestamp().getTime());
        return v;
    }

    @Override
    public long toWebUserId(String pilotId) {
        com.bees360.user.User user = userProvider.findUserById(pilotId);
        if (user == null) {
            throw new IllegalStateException("pilot with id:" + pilotId + " can't be found");
        }
        return UserAssemble.toWebUser(user).getUserId();
    }

    @Override
    public Map<String, FirebaseRoom> getRoomMap(String missionPath) {
        Map<String, FirebaseRoom> roomMap = Collections.emptyMap();
        try {

            ApiFuture<QuerySnapshot> futureResult =
                    firestore.document(missionPath).collection("room").get();
            List<QueryDocumentSnapshot> documentSnapshotList = futureResult.get().getDocuments();

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(documentSnapshotList)) {
                return roomMap;
            }
            roomMap =
                    documentSnapshotList.stream()
                            .collect(
                                    Collectors.toMap(
                                            e -> e.getReference().getPath(),
                                            e -> e.toObject(FirebaseRoom.class)));
        } catch (InterruptedException | ExecutionException e) {
            log.error("Get mission room map failed, mission {}", missionPath, e);
        }
        return roomMap;
    }

    @Override
    public FirebaseRoom getRoom(DocumentReference roomReference) {
        try {

            return roomReference.get().get().toObject(FirebaseRoom.class);
        } catch (InterruptedException | ExecutionException e) {
            log.error("Get room failed, room path {}", roomReference.getPath(), e);
        }
        return null;
    }

    @Override
    public void deleteFirebaseImagesByOriginalFileNames(
            long projectId, List<String> originalFileNames) {
        try {
            for (String originalFileName : originalFileNames) {
                List<QueryDocumentSnapshot> documents =
                        firestore
                                .collectionGroup("image")
                                .whereEqualTo("originalFileName", originalFileName)
                                .whereEqualTo("projectId", String.valueOf(projectId))
                                .get()
                                .get()
                                .getDocuments();
                if (CollectionAssistant.isEmpty(documents)) {
                    continue;
                }
                for (QueryDocumentSnapshot document : documents) {
                    document.getReference().update("deleted", true);
                }
            }
        } catch (ExecutionException | InterruptedException e) {
            String message = "Failed to delete firebase images. images(%s)";
            message = message.formatted(ListUtil.toString(originalFileNames));
            throw new IllegalStateException(message, e);
        }
    }
}
