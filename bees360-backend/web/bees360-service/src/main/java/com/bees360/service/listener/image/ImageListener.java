package com.bees360.service.listener.image;

import com.bees360.api.InvalidArgumentException;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.PipelineTaskEnum;
import com.bees360.pipeline.PipelineService;
import com.bees360.service.ActivityService;
import com.bees360.service.ProjectImageService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.Iterables;
import com.bees360.web.event.image.DroneImageUploadedEvent;
import com.bees360.web.event.image.ExteriorImageUploadedEvent;
import com.bees360.web.event.image.ImageUploadedEventForActivity;
import com.bees360.web.event.image.InteriorImageUploadedEvent;
import com.google.common.collect.Streams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Objects;

import static com.bees360.pipeline.Message.PipelineStatus.DONE;
import static com.bees360.pipeline.Message.PipelineStatus.PENDING;

@Slf4j
@Component
public class ImageListener {
    @Resource private ActivityService activityService;
    @Autowired private PipelineService pipelineService;
    @Autowired private ProjectImageService projectImageService;
    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;

    @EventListener
    public void newImageUploaded(ImageUploadedEventForActivity event) {
        Iterable<String> imageKeys = event.getImageKeys();
        Object[] images = Streams.stream(imageKeys).toArray();

        // 记录用户上传的图片
        long projectId = event.getProjectId();
        long createdBy = event.getCreatedBy();
        // 因为图片中有原图小图和中图，所以需要除以3
        activityService.uploadImages(projectId, createdBy, images.length / 3);
    }

    @EventListener
    public void changeDroneTaskStatus(DroneImageUploadedEvent event) {
        try {
            var droneImages =
                    projectImageService.countImages(
                            event.getProjectId(), FileSourceTypeEnum.DRONE_IMAGE.getCode(), false);
            if (droneImages > 0) {
                log.info(
                        "Upload drone images finish projectId {}, count {}",
                        event.getProjectId(),
                        droneImages);
                setTaskDoneIfNotPending(
                        event.getProjectId(), PipelineTaskEnum.TAKE_DRONE_IMAGES.getKey());
                setTaskDoneIfNotPending(
                        event.getProjectId(), PipelineTaskEnum.UPLOAD_DRONE_IMAGES.getKey());
            }
        } catch (Exception e) {
            log.error(
                    "Drone image uploaded, change task status failed {}", event.getProjectId(), e);
        }
    }

    @EventListener
    public void changeExteriorTaskStatus(ExteriorImageUploadedEvent event) {
        try {
            var cellPhoneImages =
                    projectImageService.countImages(
                            event.getProjectId(),
                            FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode(),
                            false);
            if (cellPhoneImages > 0) {
                log.info(
                        "Upload exterior images finish projectId {}, count {}",
                        event.getProjectId(),
                        cellPhoneImages);

                setTaskDoneIfNotPending(
                        event.getProjectId(), PipelineTaskEnum.TAKE_EXTERIOR_IMAGES.getKey());
                setTaskDoneIfNotPending(
                        event.getProjectId(), PipelineTaskEnum.UPLOAD_EXTERIOR_IMAGES.getKey());
            }
        } catch (Exception e) {
            log.error(
                    "Drone image uploaded, change task status failed {}", event.getProjectId(), e);
        }
    }

    @EventListener
    public void changeInteriorTaskStatus(InteriorImageUploadedEvent event) {
        try {
            var cellPhoneImages =
                    projectImageService.countImages(
                            event.getProjectId(),
                            FileSourceTypeEnum.CELL_PHONE_IMAGE.getCode(),
                            false);
            if (cellPhoneImages > 0) {
                log.info(
                        "Upload interior images finish projectId {}, count {}",
                        event.getProjectId(),
                        cellPhoneImages);
                setTaskDoneIfNotPending(
                        event.getProjectId(), PipelineTaskEnum.TAKE_INTERIOR_IMAGES.getKey());
                setTaskDoneIfNotPending(
                        event.getProjectId(), PipelineTaskEnum.UPLOAD_INTERIOR_IMAGES.getKey());
            }
        } catch (Exception e) {
            log.error(
                    "Interior image uploaded, change task status failed {}",
                    event.getProjectId(),
                    e);
        }
    }

    private void setTaskDoneIfNotPending(long projectId, String key) {
        try {
            var pipelineId = String.valueOf(projectId);
            var pipeline = pipelineService.findById(pipelineId);
            if (pipeline == null) {
                return;
            }

            Iterables.toStream(pipeline.getTask())
                    .filter(task -> task.getKey().equals(key))
                    .filter(
                            task ->
                                    bees360FeatureSwitch
                                                    .isEnableSetUploadTaskDoneFromPendingWhenImageUploaded()
                                            || !Objects.equals(task.getStatus(), PENDING))
                    .findAny()
                    .ifPresent(task -> pipelineService.setTaskStatus(pipelineId, key, DONE));
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Failed to set pipeline {} to DONE.", projectId, e);
        } catch (RuntimeException e) {
            log.error("Failed to set pipeline {} to DONE.", projectId, e);
        }
    }
}
