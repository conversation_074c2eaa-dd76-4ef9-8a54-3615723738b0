package com.bees360.service.properties;

import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.service.ProjectService;
import com.bees360.service.UnsubscribeEmailService;
import com.bees360.service.impl.UnsubscribeEmailServiceImpl;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiPredicate;

@Component
@Log4j2
@Configuration
@EnableConfigurationProperties
public class UnsubscribeEmailConfig {

    @Data
    @ConfigurationProperties(prefix = "bees360.company-config")
    static class UnsubscribeEmailProperties {
        /**
         * key: 模板名  value: 退订该模板的公司
         */
        private Map<String, List<Long>> emailUnsubscribe;
    }

    @Data
    @ConfigurationProperties(prefix = "app.web.emails")
    static class EmailProperties {
        private Set<String> disableTemplateKeys = new HashSet<>();
    }

    @Bean
    public UnsubscribeEmailProperties unsubscribeEmailProperties() {
        return new UnsubscribeEmailProperties();
    }

    @Bean
    EmailProperties emailProperties() {
        return new EmailProperties();
    }

    @Bean
    public UnsubscribeEmailService unsubscribeEmailService(
        UnsubscribeEmailProperties unsubscribeEmailProperties,
        EmailProperties emailProperties,
        ProjectService projectService,
        Bees360CompanyConfig bees360CompanyConfig) {

        UnsubscribeEmailService unsubscribeEmailService = unsubscribeEmailService(projectService, bees360CompanyConfig);
        UnsubscribeEmailService claimUnsubscribeEmailService = claimUnsubscribeEmailService(unsubscribeEmailProperties, projectService);
        var unsubscribeEmailServices = List.of(unsubscribeEmailService, claimUnsubscribeEmailService);

        var projectConditionUnsubscribeEmailPredicate = projectConditionUnsubscribeEmailPredicate(projectService);
        var disableTemplateKeys = emailProperties.getDisableTemplateKeys();

        var service = new UnsubscribeEmailService() {

            @Override
            public boolean isUnsubscribed(String templateKey, long company) {
                if (disableTemplateKeys.contains(templateKey)) {
                    log.debug("Template key {} has been disabled.", templateKey);
                    return true;
                }
                return unsubscribeEmailServices.stream().anyMatch(service -> service.isUnsubscribed(templateKey, company));
            }

            @Override
            public boolean isUnsubscribedByProject(String templateKey, long projectId) {
                if (disableTemplateKeys.contains(templateKey)) {
                    log.debug("Template key {} has been disabled.", templateKey);
                    return true;
                }
                if (projectConditionUnsubscribeEmailPredicate.test(templateKey, projectId)) {
                    return true;
                }
                return unsubscribeEmailServices.stream().anyMatch(service -> service.isUnsubscribedByProject(templateKey, projectId));
            }
        };

        log.info("Created {}(disableTemplateKeys={})", service, disableTemplateKeys);
        return service;
    }

    private BiPredicate<String, Long> projectConditionUnsubscribeEmailPredicate(ProjectService projectService) {
        final var targetTemplateKeys = Set.of("reportApproved", "report_approved", "report_generated", "project_completed", "report_client_received");
        final var unsubscribeServiceTypes = Set.of(ProjectServiceTypeEnum.SCHEDULING_ONLY.getCode());
        log.info(
            "created projectConditionUnsubscribeEmailPredicate(targetTemplateKey={},unsubscribeServiceTypes={})",
            targetTemplateKeys,
            unsubscribeServiceTypes);
        return (templateKey, projectId) -> {
            if (!targetTemplateKeys.contains(templateKey)) {
                return false;
            }
            var project = projectService.getById(projectId);
            return unsubscribeServiceTypes.contains(project.getServiceType());
        };
    }

    private UnsubscribeEmailService unsubscribeEmailService(ProjectService projectService, Bees360CompanyConfig bees360CompanyConfig) {
        Map<String, List<Long>> templateToUnsubscribedCompany = Maps.newHashMap();
        if (bees360CompanyConfig.getCompanies() != null) {
            for (var c: bees360CompanyConfig.getCompanies()) {
                if (c.getUnsubscribeEmail() == null) {
                    continue;
                }
                for (var templateKey: c.getUnsubscribeEmail()) {
                    var list = templateToUnsubscribedCompany.computeIfAbsent(templateKey, key -> new ArrayList<>());
                    list.add(c.getId());
                }
            }
        }
        return new UnsubscribeEmailServiceImpl(templateToUnsubscribedCompany, projectService, null);
    }

    private UnsubscribeEmailService claimUnsubscribeEmailService(UnsubscribeEmailProperties unsubscribeEmailProperties,
                                                                 ProjectService projectService) {
        Map<String, List<Long>> templateToUnsubscribedCompany =
            unsubscribeEmailProperties.getEmailUnsubscribe() != null ? unsubscribeEmailProperties.getEmailUnsubscribe() : Map.of();
        return new UnsubscribeEmailServiceImpl(templateToUnsubscribedCompany, projectService, InspectionPurposeTypeEnum.CLAIM);
    }
}
