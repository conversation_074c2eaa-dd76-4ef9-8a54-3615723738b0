package com.bees360.service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.User;
import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.UserTinyVo;
import java.util.List;

public interface UserService {

	/**
	 * get an user no matter what active_status he is
	 *
	 * @param userId
	 * @return an user with any active_status
	 * @throws ServiceException
	 */
	User getUserById(long userId);

	List<UserTinyVo> listMemberInProject(long projectId, RoleEnum role);

	List<UserTinyVo> listMemberInProject(long userId, long projectId) throws ServiceException;

    List<IdNameDto> listRolesUserPlay(long projectId, long userId) throws ServiceException;

    /**
     * Convert string userId to Long webUserId
     * Directly parse the userId as Long, no longer use User<PERSON>rovider to convert
     * @param userIdString
     * @return
     */
    Long toWebUserId(String userIdString);

    List<UserTinyVo> listActiveMemberInProject(long projectId);

}
