package com.bees360.service.impl;

import com.bees360.entity.BsExportData;
import com.bees360.service.BsExportDataService;
import com.bees360.service.ConstantSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/01/15 16:59
 */
@Service
public class ConstantSettingServiceImpl implements ConstantSettingService {

    @Autowired
    private BsExportDataService bsExportDataService;

    @Override
    public String getSettingConstant(String code, String type) {
        return Optional.ofNullable(bsExportDataService.getByRelatedIdAndType(code, type))
            .map(BsExportData::getDataLog).orElse("");
    }
}
