package com.bees360.service.job;

import com.bees360.base.exception.ServiceException;
import com.bees360.common.collections.ListUtil;
import com.bees360.entity.Project;
import com.bees360.entity.dto.ProjectUpdateStatusDto;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.schedule.exception.RetryableException;
import com.bees360.schedule.job.retry.BatchRetryableJob;
import com.bees360.schedule.job.retry.JobData;
import com.bees360.schedule.job.retry.RetryableOperation;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandlers;
import com.bees360.schedule.util.QuartzJobConstant;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import java.util.List;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * update project status job, Each failure will be repeated 9 times.
 *
 * <AUTHOR>
 * @since 2021/4/26
 */
@Slf4j
public class UpdateProjectStatusJob extends BatchRetryableJob<ProjectUpdateStatusDto> {

    @Getter
    @Setter
    private List<JobData<ProjectUpdateStatusDto>> jobDataList;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectStatusService projectStatusService;

    public static JobKey createJobKey(int status, long time) {
        Class<? extends Job> jobClass = UpdateProjectStatusJob.class;
        // 该jobName会限制该压缩包只能往指定的claim上传一次
        String jobName = "%s@%s@%s".formatted(jobClass.getSimpleName(), status, time);
        return new JobKey(jobName, QuartzJobConstant.WebGroup.PROJECT_UPDATE_STATUS);
    }

    public static JobDetail createJobDetail(List<Long> projectIds, int status, String systemType, long userId,
        long time) {
        Class<? extends Job> jobClass = UpdateProjectStatusJob.class;

        List<JobData<ProjectUpdateStatusDto>> jobDataList = ListUtil.toList(projectId -> {
            ProjectUpdateStatusDto projectUpdateStatusDto = ProjectUpdateStatusDto.builder()
                .projectId(projectId)
                .userId(userId)
                .status(status)
                .systemType(systemType)
                .build();
            return new JobData<>(projectUpdateStatusDto);
        }, projectIds);

        JobDataMap jobData = new JobDataMap();
        jobData.put("jobDataList", jobDataList);
        String description = "Update project status. first projectId:%d project count :%d status %d".formatted(
            projectIds.get(0), projectIds.size(), status);

        return JobBuilder.newJob(jobClass)
            .withIdentity(createJobKey(status, time))
            .usingJobData(jobData)
            .withDescription(description)
            .requestRecovery()
            .storeDurably()
            .build();
    }

    @Override
    protected RetryExceptionHandler getRetryExceptionHandler() {
        final int RETRY_COUNT_TO_LOG_ERROR = 3;
        final int RETRY_COUNT = 3;
        return RetryExceptionHandlers.newBuilder()
            .retryOnCause(ServiceException.class)
            .setStartToLogErrorRetryCount(RETRY_COUNT_TO_LOG_ERROR)
            .retryCount(RETRY_COUNT)
            .build();
    }

    @Override
    protected RetryableOperation getItemRetryableOperation(JobData<ProjectUpdateStatusDto> jobData) {
        ProjectUpdateStatusDto projectUpdateStatusDto = jobData.getT();
        return (JobExecutionContext context) -> {
            long start = System.currentTimeMillis();
            try {
                executeJob(projectUpdateStatusDto);
            } catch (Exception e) {
                throw new RetryableException(e);
            }
            log.info("update project status. projectId:{} status:{} spent:{}", projectUpdateStatusDto.getProjectId(),
                projectUpdateStatusDto.getStatus(), start - System.currentTimeMillis());
        };
    }

    @Override
    protected RetryExceptionHandler getItemRetryExceptionHandler() {
        return getRetryExceptionHandler();
    }

    /**
     * 执行job的业务逻辑和异常处理
     *
     * @param projectUpdateStatusDto
     * @throws RetryableException
     */
    private void executeJob(ProjectUpdateStatusDto projectUpdateStatusDto) throws RetryableException {
        long projectId = projectUpdateStatusDto.getProjectId();
        long userId = projectUpdateStatusDto.getUserId();
        int status = projectUpdateStatusDto.getStatus();
        String systemType = projectUpdateStatusDto.getSystemType();
        try {
            Project project = projectService.getById(projectId);
            if (Objects.equals(project.getProjectStatus(), status)) {
                return;
            }
            SystemTypeEnum systemTypeEnum = SystemTypeEnum.getEnum(systemType);
            // 目前此job只支持CLIENT_RECEIVED和PROJECT_CANCELED
            if (Objects.equals(NewProjectStatusEnum.CLIENT_RECEIVED.getCode(), status)) {
                projectStatusService.changeOnClientReceived(userId, projectId, systemTypeEnum, "");
            } else if (Objects.equals(NewProjectStatusEnum.PROJECT_CANCELED.getCode(), status)) {
                projectService.cancelProject(projectId, userId, systemTypeEnum, true, "");
            }
        } catch (ServiceException e) {
            throw new RetryableException(e);
        }
    }
}
