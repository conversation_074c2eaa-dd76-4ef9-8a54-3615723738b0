package com.bees360.service.impl;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.UserPaymentProfile;
import com.bees360.entity.vo.Money;
import com.bees360.service.payment.PresentedStrategyService;
import com.bees360.service.payment.SystemValueCacheService;
import com.bees360.service.payment.UserPaymentProfileService;
import com.bees360.util.payment.PaymentUtil;

@Service("presentedStrategyService")
public class PresentedStrategyServiceImpl implements PresentedStrategyService{

	private static final Logger logger = LoggerFactory.getLogger(PresentedStrategyServiceImpl.class);

	@Inject
	UserPaymentProfileService userPaymentProfileService;

	@Inject
	SystemValueCacheService systemValueCacheService;

	@Override
	public void execute(long userId, int freeProjectCount) throws ServiceException {
		if(freeProjectCount <= 0) {
			return;
		}
		try {
			//recharge freeProjectCount * price (including tax) for user
//			Money priceForSingleReport = systemValueCacheService.getPriceForDamageMeasurementReport();
			Money priceForSingleReport = null;
			double walletBalance = freeProjectCount * priceForSingleReport.getValue();
			double tax = systemValueCacheService.getTaxRate() * walletBalance;
			UserPaymentProfile userPaymentProfileInDb = userPaymentProfileService.getUserPaymentProfile(userId);
			walletBalance = userPaymentProfileInDb.getWalletBalance() + walletBalance + tax;
			walletBalance = PaymentUtil.getValueByPrecision(walletBalance, 2);

			logger.info("system present user " + userId + "  money:" + walletBalance);
			userPaymentProfileService.updateUserPaymentProfileByBalance(userId, walletBalance, priceForSingleReport.getCurrency());
		}catch(ServiceException e) {
			throw e;
		}catch(Exception e) {
			logger.error(e.getMessage(), e);
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
	}

}
