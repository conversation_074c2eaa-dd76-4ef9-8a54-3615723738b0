package com.bees360.service.event.pilot;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.enums.PilotBatchEventTypeEnum;
import com.bees360.service.beespilot.BeesPilotBatchService;
import com.bees360.service.firebase.FirebaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/07/24 17:15
 */
@Slf4j
@Component
public class PilotBatchChangeEventListener {

    @Autowired
    private BeesPilotBatchService beesPilotBatchService;

    public void notifyPilotAfterBatchCreated(PilotBatchChangeEvent event) throws ServiceException {
        try {
            beesPilotBatchService.notifyPilotAfterBatchCreated(event.getPilotId(), event.getBatch(), event.getTypeEnum());
        } catch (Exception e) {
            log.error("notifyPilotAfterBatchCreated sendEmail failed. msg:{}, event:{}", e.getMessage(), event, e);
        }
    }
}
