package com.bees360.service.impl;

import java.util.List;
import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Permission;
import com.bees360.mapper.PermissionMapper;
import com.bees360.service.PermissionService;

@Service("permissionService")
public class PermissionServiceImpl implements PermissionService{

	private Logger logger = LoggerFactory.getLogger(PermissionServiceImpl.class);

	@Inject
	private PermissionMapper permissionMapper;


	public List<Permission> getAllPermissions() throws ServiceException{
		try {
			return permissionMapper.getAllPermissions();
		} catch (Exception e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

	@Override
	public List<Permission> getPermissionsByPermissionIds(List<Long> permissionIds) throws ServiceException{
		if(permissionIds == null || permissionIds.size() == 0) {
			return null;
		}
		try {
			return permissionMapper.getByPermissionIds(permissionIds);
		} catch (Exception e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}
}
