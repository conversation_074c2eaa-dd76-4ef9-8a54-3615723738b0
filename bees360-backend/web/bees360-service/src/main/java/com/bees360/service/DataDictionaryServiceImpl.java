package com.bees360.service;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.dto.DataDictionary;
import com.bees360.entity.dto.DataDictionaryDto;
import com.bees360.mapper.DataDictionaryMapper;
import com.bees360.util.AssertUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/8/18 2:08 下午
 **/
@Service
public class DataDictionaryServiceImpl implements DataDictionaryService {
    @Resource
    private DataDictionaryMapper dataDictionaryMapper;

    @Override
    public DataDictionary getByNamespaceAndCode(String namespace, String code) {
        return dataDictionaryMapper.getByNamespaceAndCode(namespace, code);
    }

    @Override
    public List<DataDictionaryDto> listDataDictionary(String namespace) {
        List<DataDictionary> dictionaryList = dataDictionaryMapper.listByNamespace(namespace);
        List<DataDictionaryDto> dtos = new ArrayList<>();
        dictionaryList.sort(Comparator.comparingInt(DataDictionary::getSequence));
        for (DataDictionary dataDictionary : dictionaryList) {
            DataDictionaryDto dto = new DataDictionaryDto();
            BeanUtils.copyProperties(dataDictionary, dto);
            dtos.add(dto);
        }
        return dtos;
    }

    @Override
    public List<DataDictionary> listByNamespace(String namespace) {
        return dataDictionaryMapper.listByNamespace(namespace);
    }

    @Override
    public void addDataDictionary(DataDictionary dataDictionary) {
        dataDictionaryMapper.insert(dataDictionary);
    }

    @Override
    public void updateOrAdd(DataDictionary dataDictionary) throws ServiceException {
        AssertUtil.notEmpty(dataDictionary.getNamespace(), MessageCode.PARAM_INVALID);
        AssertUtil.notEmpty(dataDictionary.getCode(), MessageCode.PARAM_INVALID);
        AssertUtil.notEmpty(dataDictionary.getName(), MessageCode.PARAM_INVALID);
        AssertUtil.notNull(dataDictionary.getValue(), MessageCode.PARAM_INVALID);
        if (!dataDictionaryMapper.updateByNamespaceAndCode(dataDictionary)) {
            dataDictionaryMapper.insert(dataDictionary);
        }
    }
}
