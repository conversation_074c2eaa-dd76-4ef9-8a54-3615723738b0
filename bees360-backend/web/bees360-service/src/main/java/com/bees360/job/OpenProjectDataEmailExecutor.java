package com.bees360.job;

import com.bees360.job.registry.OpenProjectExportEmail;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.email.ProjectDataEmailSender;

import java.io.IOException;
import java.util.List;

public class OpenProjectDataEmailExecutor extends AbstractJobExecutor<OpenProjectExportEmail> {

    private final ProjectDataEmailSender projectDataEmailSender;

    private final String OPEN_PROJECT_SUBJECT = "Open Cases statistics";

    public OpenProjectDataEmailExecutor(ProjectDataEmailSender projectDataEmailSender) {
        this.projectDataEmailSender = projectDataEmailSender;
    }

    @Override
    protected void handle(OpenProjectExportEmail openProjectExportEmail) throws IOException {
        List<String> recipients = openProjectExportEmail.getRecipients();
        projectDataEmailSender.sendEmailOfOpenProjectData(OPEN_PROJECT_SUBJECT, recipients);
    }
}
