package com.bees360.service.impl;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceMessageException;
import com.bees360.common.collections.ListUtil;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectQuiz;
import com.bees360.entity.Quiz;
import com.bees360.entity.converter.ProjectQuizConveter;
import com.bees360.entity.dto.ProjectQuizDto;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.QuizTypeEnum;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.ProjectQuizMapper;
import com.bees360.service.ProjectQuizService;
import com.bees360.service.beespilot.BeesPilotStatusService;
import com.bees360.util.AssertUtil;
import com.bees360.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * project quiz service
 *
 * <AUTHOR>
 * @since 2020/4/10 2:14 PM
 **/
@Service
public class ProjectQuizServiceImpl implements ProjectQuizService {
    @Resource
    private ProjectQuizMapper projectQuizMapper;

    @Resource
    private ProjectMapper projectMapper;
    @Autowired
    private BeesPilotStatusService beesPilotStatusService;

    @Override
    public void batchInsertAnswer(long projectId, List<ProjectQuiz> quizList)
        throws ServiceMessageException {
        List<ProjectQuizDto> projectQuizList = listProjectQuiz(projectId);
        // check Answer
        checkAnswer(projectQuizList, quizList);
        beesPilotStatusService.quizCompleted(projectId, quizList.size()==projectQuizList.size());

        Project project = projectMapper.getById(projectId);
        AssertUtil.notNull(project, "The project[%s] does not exist.".formatted(projectId));
        long now = DateUtil.getNow();
        quizList.forEach(quiz -> {
            quiz.setClaimType(project.getClaimType());
            quiz.setCreateTime(now);
            quiz.setProjectId(projectId);
        });
        projectQuizMapper.batchInsertAnswer(quizList);
    }

    private final static int QUIZ_CODE_FOR_CLAIM = 2294;
    private final static int QUIZ_CODE_FOR_UNDERWRITING = 2096;
    @Override
    public List<ProjectQuizDto> listProjectQuiz(long projectId) throws ServiceMessageException {
        Project project = projectMapper.getById(projectId);
        AssertUtil.notNull(project, "The project[%s] does not exist.".formatted(projectId));
        ClaimTypeEnum claimType = ClaimTypeEnum.getEnum(project.getClaimType());
        int quizCode = QUIZ_CODE_FOR_CLAIM;
        if (claimType != null && ClaimTypeEnum.UNDERWRITING_FIRST_INSPECTION.equals(claimType)) {
            quizCode = QUIZ_CODE_FOR_UNDERWRITING;
        }
        List<Quiz> quizzes = projectQuizMapper.listCompanyQuiz(quizCode);
        if (CollectionUtils.isEmpty(quizzes)) {
            return new ArrayList<>();
        }
        List<ProjectQuiz> projectQuizAnswers = projectQuizMapper.listLatestAnswers(projectId, Optional.ofNullable(project.getClaimType()).orElse(0));
        List<ProjectQuizDto> projectQuizzes = ProjectQuizConveter.projectQuizDtoList(projectId, quizzes);
        if (!CollectionUtils.isEmpty(projectQuizAnswers)) {
            Map<String, ProjectQuiz> answerMap =
                // key is `quizId + projectId` and get newest quiz answer
                projectQuizAnswers.stream().collect(
                    Collectors.toMap(a -> "" + a.getQuizId() + a.getProjectId(), a -> a, (k1, k2) -> {
                        if (k1.getCreateTime() > k2.getCreateTime()) {
                            return k1;
                        }
                        return k2;
                    }));
            for (ProjectQuizDto quiz : projectQuizzes) {
                String key = "" + quiz.getQuizId() + quiz.getProjectId();
                ProjectQuiz answer = answerMap.get(key);
                // set answer if exist
                if (answer != null) {
                    quiz.setAnswer(answer.getAnswer());
                }
            }
        }
        return projectQuizzes;
    }


    /**
     * check if the answer is valid
     *
     * @param quizList quiz answer list
     * @throws ServiceMessageException invalid exception
     */
    private void checkAnswer(List<ProjectQuizDto> projectQuizList, List<ProjectQuiz> quizList) throws ServiceMessageException {
        AssertUtil.notEmpty(quizList, "The answer list of the quiz can't be empty!");
        AssertUtil.notEmpty(projectQuizList, "The project doesn't have quiz to save");
        Map<Long, ProjectQuizDto> quizDtoMap = ListUtil.toMap(ProjectQuizDto::getQuizId, projectQuizList);
        for (ProjectQuiz quiz : quizList) {
            AssertUtil.notNull(quiz, "The answer to the quiz can't be empty!");
            long quizId = quiz.getQuizId();
            AssertUtil.assertTrue(quizId != 0, MessageCode.PARAM_INVALID, "The id of the quiz can`t be 0!");
            String[] answers = quiz.getAnswers();
            AssertUtil.notEmpty(answers, "The answer to the quiz can`t be empty!");
            // 检查选择题
            ProjectQuizDto quizDto = quizDtoMap.get(quiz.getQuizId());
            AssertUtil.notNull(quizDto, "This quiz[" + quiz.getQuizId() + "] is not existed");
            int type = quizDto.getType();
            if (QuizTypeEnum.SINGLE_CHOICE.getCode() == type ||
                QuizTypeEnum.MULTI_CHOICE.getCode() == type ||
                QuizTypeEnum.BOOLEAN.getCode() == quizDto.getType()) {
                String[] choices = quizDto.getChoices();
                boolean containAns = false;
                for (String answer : answers) {
                    for (String choice : choices) {
                        if (choice.trim().equals(answer)) {
                            containAns = true;
                            break;
                        }
                    }
                }
                AssertUtil.assertTrue(containAns, MessageCode.PARAM_INVALID,
                    "The answer[" + Arrays.toString(answers) + "] does not belong to the choices. The quiz id is " + quizId);
            }

            // 查询填空题中的数字
            if (QuizTypeEnum.FILL_A_DIGIT_BLANK.getCode() == type ||
                QuizTypeEnum.FILL_MANY_DIGIT_BLANKS.getCode() == type) {
                String[] choices = quizDto.getChoices();
                // answer should be numeric
                for (String s : answers) {
                    try {
                        Integer.parseInt(s);
                    } catch (NumberFormatException e) {
                        throw new ServiceMessageException(MessageCode.PARAM_INVALID, "Only numeric digit supported.");
                    }
                }
            }
        }
    }
}
