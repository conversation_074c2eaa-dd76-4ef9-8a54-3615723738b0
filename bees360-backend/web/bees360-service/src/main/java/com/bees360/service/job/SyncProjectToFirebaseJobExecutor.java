package com.bees360.service.job;

import com.bees360.firebase.FirebaseApi;
import com.bees360.grpc.GrpcApi;
import com.bees360.job.registry.SyncProjectToFirebaseJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.service.ProjectService;
import com.bees360.service.firebase.FirebaseProjectService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.util.Functions;
import com.bees360.util.Iterables;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.SetOptions;

import io.grpc.StatusRuntimeException;

import lombok.ToString;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

@Log4j2
@Component
@ToString
public class SyncProjectToFirebaseJobExecutor
        extends AbstractJobExecutor<SyncProjectToFirebaseJob> {
    public static final String PROJECT_INTEGRATION_TYPE_BATCH = "BATCH";

    private final FirebaseService firebaseService;
    private final Firestore firestore;
    private final ProjectService projectService;
    private final FirebaseProjectService firebaseProjectService;
    private final ExternalIntegrationManager externalIntegrationManager;

    public SyncProjectToFirebaseJobExecutor(
            FirebaseService firebaseService,
            Firestore firestore,
            ProjectService projectService,
            FirebaseProjectService firebaseProjectService,
            ExternalIntegrationManager externalIntegrationManager) {
        this.firebaseService = firebaseService;
        this.firestore = firestore;
        this.projectService = projectService;
        this.firebaseProjectService = firebaseProjectService;
        this.externalIntegrationManager = externalIntegrationManager;

        log.info("Created '{}'", this);
    }

    @Override
    protected void handle(SyncProjectToFirebaseJob job) throws IOException {
        try {
            var projectId = job.getProjectId();
            var path = firebaseService.getProjectDocumentPath(projectId);
            DocumentReference reference = firestore.document(path);
            var project = projectService.findById(projectId);
            if (project.isEmpty()) {
                // TODO 由于事务问题，Project可能还没有保存到数据库中, 这里需要抛出IOException,触发重试
                throw new IOException("The project '%s' is not found.".formatted(projectId));
            }
            Map<String, Object> data = firebaseProjectService.getAllFieldsInProject(project.get());
            // if trigger by init project, try to find bundle id
            if (!job.isUpdated()) {
                Functions.acceptIfNotNull(
                        bundleId -> data.put("bundle_id", bundleId),
                        findProjectBundleBatchId(projectId));
            }
            log.debug(
                    "Try to update firebase document for project: {}, fields: {}.",
                    projectId,
                    data);
            FirebaseApi.exec(() -> reference.set(data, SetOptions.merge()));
            log.info("Success sync project '{}' to firebase.", projectId);
        } catch (StatusRuntimeException e) {
            // TODO 因为目前grpc client没有很好的捕获 StatusRuntimeException,所以这里需要将它转换为系统默认的异常
            throw GrpcApi.translateException(e);
        }
    }

    /**
     * same as {@link
     * com.bees360.service.firebase.impl.FirebaseProjectServiceImpl.findProjectBundleBatchId}. </br>
     * Query the project bundle batch ID. This method is mainly used during project creation, as the
     * project bundle group data may be incomplete at that time. For other periods, the project
     * bundle group data should be used as the standard source.
     */
    private String findProjectBundleBatchId(Long projectIdLong) {
        String projectId = String.valueOf(projectIdLong);
        // try to find bundle id from project bundle batch
        // bundle project batch if format bundle-{bundleId}-{requestMD5}
        String[] bundleBatchIdParts =
                Iterables.toStream(externalIntegrationManager.findAllByProjectId(projectId))
                        .filter(
                                i ->
                                        StringUtils.equals(
                                                i.getIntegrationType(),
                                                PROJECT_INTEGRATION_TYPE_BATCH))
                        .filter(i -> StringUtils.contains(i.getReferenceNumber(), "bundle"))
                        .findFirst()
                        .map(i -> StringUtils.split(i.getReferenceNumber(), "-"))
                        .orElse(null);

        if (bundleBatchIdParts != null && bundleBatchIdParts.length > 2) {
            return bundleBatchIdParts[1];
        }

        return null;
    }
}
