package com.bees360.job;

import com.bees360.atomic.LockProvider;
import com.bees360.job.registry.FirebaseMissionCompletedStuckV2;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseMissionService;
import com.bees360.service.firebase.FirebaseService;

import lombok.extern.log4j.Log4j2;
import java.io.IOException;
import java.time.Instant;
import java.util.Optional;

import static com.bees360.service.util.FirebaseExceptionTranslation.translateExceptionAndThrow;

@Log4j2
public class FirebaseMissionCompletedStuckExecutorV2
        extends AbstractJobExecutor<FirebaseMissionCompletedStuckV2> {

    private final FirebaseMissionService firebaseMissionService;
    private final FirebaseService firebaseService;
    private final LockProvider missionLockProvider;

    public FirebaseMissionCompletedStuckExecutorV2(
            FirebaseMissionService firebaseMissionService,
            FirebaseService firebaseService,
            LockProvider missionLockProvider) {
        this.firebaseMissionService = firebaseMissionService;
        this.firebaseService = firebaseService;
        this.missionLockProvider = missionLockProvider;

        log.info(
                "Created '{}(firebaseMissionService={},firebaseService={},missionLockProvider={})",
                this,
                this.firebaseMissionService,
                this.firebaseService,
                this.missionLockProvider);
    }

    @Override
    protected void handle(FirebaseMissionCompletedStuckV2 mission) throws IOException {
        var id = mission.getMissionId();
        if (id == null) {
            log.error("Null mission id found when handle mission '{}' stuck status.", mission);
            return;
        }
        try (var ignore = missionLockProvider.lock(id)) {
            long pilotId = firebaseService.toWebUserId(mission.getPilotId());
            firebaseMissionService.handleMissionCompletedStuck(
                    mission.getMissionPath(),
                    pilotId,
                    Long.parseLong(mission.getProjectId()),
                    Optional.ofNullable(mission.getStatusUpdateTime())
                            .map(Instant::toEpochMilli)
                            .orElse(null));
            log.info("Successfully handle completed stuck mission '{}'.", mission);
        } catch (RuntimeException e) {
            log.warn("Failed to handle mission '{}' completed stuck event", mission, e);
            translateExceptionAndThrow(e);
        }
    }
}
