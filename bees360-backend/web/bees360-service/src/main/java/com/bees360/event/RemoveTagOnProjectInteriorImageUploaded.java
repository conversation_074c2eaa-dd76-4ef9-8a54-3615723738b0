package com.bees360.event;

import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.event.registry.InteriorImageUploadedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.UserService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听内部图片上传事件并移除项目未完成标签
 */
@Log4j2
public class RemoveTagOnProjectInteriorImageUploaded
    extends AbstractNamedEventListener<InteriorImageUploadedEvent> {

    private final ProjectLabelService projectLabelService;

    private final UserService userService;

    public RemoveTagOnProjectInteriorImageUploaded(
        ProjectLabelService projectLabelService, UserService userService) {
        this.projectLabelService = projectLabelService;
        this.userService = userService;
    }

    /** 当前interior image uploaded状态触发时移除ibees not completed tag */
    @Override
    public void handle(InteriorImageUploadedEvent event) throws IOException {
        var projectId = Long.parseLong(event.getProjectId());

        var userId = userService.toWebUserId(event.getUpdatedBy());
        if (userId == null) {
            var msg =
                "Failed to get web user id due to user %s not exists in web.".formatted(
                    event.getUpdatedBy());

            throw new IllegalStateException(msg);
        }

        projectLabelService.removeLabel(userId, projectId, ProjectLabelEnum.IBEES_NOT_COMPLETED);
        log.info(
            "Remove project tag for project {} successfully by InteriorImageUploadedEvent.",
            projectId);
    }
}
