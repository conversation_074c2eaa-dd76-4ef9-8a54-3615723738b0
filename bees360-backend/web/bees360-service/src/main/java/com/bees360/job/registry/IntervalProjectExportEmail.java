package com.bees360.job.registry;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Value;

import java.time.Instant;
import java.util.List;

@Value
@JobPayload
public class IntervalProjectExportEmail {

     Instant startTime;

     Instant endTime;

     Interval interval;

     List<String> recipients;

     @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
     public enum Interval {
         WEEKLY("Weekly"),
         MONTHLY("Monthly"),
         DAILY("Daily");

         @Getter
         private final String value;
     }
}
