package com.bees360.service.stat;

import com.bees360.entity.stat.dto.ProjectListDataDto;
import com.bees360.entity.stat.search.StatFullSearchOption;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

class StatDaysOldUtil {

    public static String NEW_DAYS_OLD = "new_days_old";

    private static final Logger logger = LoggerFactory.getLogger(StatDaysOldUtil.class);

    /**
     * 在根据查询条件查询出符合条件的projectId及其daysOld后，根据分页信息筛选出符合条件的project，并将符合条件的id重新作为查询的条件。
     *
     * @param daysOldMap 根据查询条件查询出的projectId，daysOld的map
     * @param sortOrder 排序顺序
     */
    static StatFullSearchOption preProcessDaysOldOption(
            Map<String, Integer> daysOldMap, String sortOrder, int pageIndex, int pageSize) {
        Comparator<? super Map.Entry<String, Integer>> sortFunc = Map.Entry.comparingByValue();
        if (Objects.equals(sortOrder, "desc")) {
            sortFunc = sortFunc.reversed();
        }
        var resultMap =
                daysOldMap.entrySet().stream()
                        .sorted(sortFunc)
                        .skip((long) pageSize * (pageIndex - 1))
                        .limit(pageSize)
                        .collect(
                                Collectors.toMap(
                                        e -> Long.parseLong(e.getKey()), Map.Entry::getValue));
        // When project id is empty, the mapper is supposed to search no projects
        // other than not using project id as its search option,
        // add -1 as project id to avoid skipping using id as search option.
        var projects = new HashSet<>(resultMap.keySet());
        projects.add(-1L);
        var searchOption = new StatFullSearchOption();
        searchOption.setPageIndex(1);
        searchOption.setPageSize(pageSize);
        searchOption.intersectProjectIds(projects);
        logger.info(
                "Processed filtered map is :"
                        + Arrays.toString(projects.toArray())
                        + " and search option is :"
                        + searchOption);
        return searchOption;
    }

    /**
     * 将查询结果根据projectId设置对应的daysOld
     *
     * @param projects projects
     * @param filteredMap 最终筛选出的projectId，daysOld的map
     * @param sortOrder the sort order, asc or desc
     * @param sortKey
     */
    static List<ProjectListDataDto> setProjectNewDaysOld(
            List<ProjectListDataDto> projects,
            Map<String, Integer> filteredMap,
            String sortOrder,
            String sortKey) {
        var sortedList = projects;
        if (Objects.equals(sortKey, NEW_DAYS_OLD)) {
            Comparator<ProjectListDataDto> comparator =
                    Comparator.comparing(
                            p -> filteredMap.getOrDefault(String.valueOf(p.getProjectId()), 0));
            // should sort reversed if sortOrder is desc
            if (Objects.equals(sortOrder, "desc")) {
                comparator = comparator.reversed();
            }
            sortedList = projects.stream().sorted(comparator).collect(Collectors.toList());
        }

        sortedList.forEach(
                p ->
                        p.setNewDaysOld(
                                filteredMap.getOrDefault(String.valueOf(p.getProjectId()), 0)));
        logger.info("Sorted List :" + sortedList);
        return sortedList;
    }
}
