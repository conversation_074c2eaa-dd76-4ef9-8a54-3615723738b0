package com.bees360.service.util;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.ServiceMessageException;
import com.bees360.entity.Project;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;

/**
 * <AUTHOR>
 */
public class ProjectRuleUtil {

    public static void checkClaimTypeAndSetDefault(Project project) throws ServiceException {
        Integer finalClaimType = checkClaimTypeAndReturnDefault(project.getServiceType(), project.getClaimType());
        project.setClaimType(finalClaimType);
    }

    public static void checkClaimType(Project project) throws ServiceException {
        checkClaimTypeAndReturnDefault(project.getServiceType(), project.getClaimType());
    }

    public static Integer checkClaimTypeAndReturnDefault(Integer serviceType, Integer claimTypeCode)
        throws ServiceException {
        if (serviceType == null) {
            return claimTypeCode;
        }
        ProjectServiceTypeEnum serviceTypeEnum = ProjectServiceTypeEnum.getEnum(serviceType);
        if (serviceTypeEnum == null) {
            return claimTypeCode;
        }
        if (claimTypeCode == null) {
            ClaimTypeEnum defaultClaimType = serviceTypeEnum.getInspectionPurposeType().getDefaultSubType();
            return defaultClaimType == null ? null : defaultClaimType.getCode();
        }
        ClaimTypeEnum claimTypeEnum = ClaimTypeEnum.getEnum(claimTypeCode);
        Set<ClaimTypeEnum> claimTypeSet = serviceTypeEnum.getInspectionPurposeType().getSubTypes();
        if (claimTypeSet.contains(claimTypeEnum)) {
            return claimTypeCode;
        }
        List<String> claimTypeNames = claimTypeSet.stream().map(t -> t.getDisplay()).collect(Collectors.toList());
        String message = "type of loss should be one of " + claimTypeNames
            + " when service type is " + serviceTypeEnum.getValue();
        throw new ServiceMessageException(MessageCode.PARAM_INVALID, message);
    }
}
