package com.bees360.service.listener.project;

import com.bees360.entity.User;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.listener.Listener;
import com.bees360.service.MemberService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.List;

@Log4j2
public class UpdateOMByProjectTaskOwnerChangeListener implements Listener<PipelineTaskChanged> {

    private final MemberService memberService;

    private final List<String> targetTaskList;

    public UpdateOMByProjectTaskOwnerChangeListener(
            MemberService memberService, List<String> targetTaskList) {
        this.memberService = memberService;
        this.targetTaskList = targetTaskList;
        log.info(
                "Created :{} with member service :{} and targetTaskList :{}",
                this,
                memberService,
                targetTaskList);
    }

    @Override
    public void execute(PipelineTaskChanged event) throws IOException {
        if (!targetTaskList.contains(event.getTaskDefKey())) {
            return;
        }
        log.info("Received OM changed by task owner changed event, start to update OM :{}", event);
        var projectId = event.getPipelineId();
        var OMId = event.getState().getOwnerId();
        // save new owner id as OM
        memberService.saveOperationsManager(
                Long.parseLong(projectId),
                OMId,
                String.valueOf(User.BEES_PILOT_SYSTEM),
                event.getUpdatedAt().toEpochMilli());
    }
}
