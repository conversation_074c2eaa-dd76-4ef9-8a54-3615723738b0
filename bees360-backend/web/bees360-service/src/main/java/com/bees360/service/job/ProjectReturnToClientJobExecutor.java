package com.bees360.service.job;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.job.Job;
import com.bees360.job.JobExecutor;
import com.bees360.service.EventHistoryService;
import com.bees360.service.ProjectStatusService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Log4j2
@Component(ProjectReturnToClientJobExecutor.EXECUTOR_NAME)
public class ProjectReturnToClientJobExecutor implements JobExecutor {

    @Autowired
    private ProjectStatusService projectStatusService;

    @Autowired
    private EventHistoryService eventHistoryService;

    public static final String EXECUTOR_NAME = "projectReturnToClientJobExecutor";

    @Override
    public String getName() {
        return EXECUTOR_NAME;
    }

    @Override
    public void execute(Job job) throws IOException {
        ProjectReturnToClientJob payload = ProjectReturnToClientJob.decode(job.getPayload());
        long userId = payload.getUserId();
        long projectId = payload.getProjectId();

        log.info("execute return to client job. projectId {}", projectId);
        try {
            projectStatusService.changeOnReturnedToClient(userId, projectId);
            eventHistoryService.insertHistoryToProject(projectId, userId, ProjectStatusEnum.RETURNED_TO_CLIENT,
                null);
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
    }
}
