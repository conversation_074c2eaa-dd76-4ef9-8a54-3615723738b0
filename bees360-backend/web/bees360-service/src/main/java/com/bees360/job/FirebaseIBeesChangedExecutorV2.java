package com.bees360.job;

import com.bees360.job.registry.SerializableFirebaseIBeesMission;
import com.bees360.job.registry.SerializableFirebaseIBeesMissionV2;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseMissionService;
import com.google.cloud.firestore.Firestore;

import lombok.ToString;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.beanutils.BeanUtils;

import java.io.IOException;

import static com.bees360.service.util.FirebaseExceptionTranslation.translateExceptionAndThrow;

@Log4j2
@ToString
public class FirebaseIBeesChangedExecutorV2
        extends AbstractJobExecutor<SerializableFirebaseIBeesMissionV2> {
    private final FirebaseMissionService firebaseMissionService;
    private final Firestore firestore;

    public FirebaseIBeesChangedExecutorV2(FirebaseMissionService firebaseMissionService, Firestore firestore) {
        this.firebaseMissionService = firebaseMissionService;
        this.firestore = firestore;

        log.info("Created '{}'", this);
    }

    @Override
    protected void handle(SerializableFirebaseIBeesMissionV2 missionV2) throws IOException {
        SerializableFirebaseIBeesMission mission;
        try{
            mission = convert(missionV2);
        } catch (IllegalArgumentException e){
            log.error("Failed to handle ibees mission '{}'.", missionV2, e);
            return;
        }

        try {
            log.info("Start to handle ibees mission '{}' '{}'", mission.getId(), mission);
            firebaseMissionService.handleIBeesMission(mission, mission.getId());
            log.info("Successfully handle ibees mission '{}'", mission.getId());
        } catch (RuntimeException e) {
            log.error("Failed to handle ibees mission '{}'", mission.getId(), e);
            translateExceptionAndThrow(e);
        }
    }

    private SerializableFirebaseIBeesMission convert(SerializableFirebaseIBeesMissionV2 missionV2) throws IllegalArgumentException {
        SerializableFirebaseIBeesMission mission = new SerializableFirebaseIBeesMission();
        try {
            BeanUtils.copyProperties(mission, missionV2);
        } catch (Exception e) {
            throw new IllegalArgumentException(
                "Failed to convert SerializableFirebaseIBeesMissionV2 to SerializableFirebaseIBeesMission.", e);
        }
        return mission;
    }
}
