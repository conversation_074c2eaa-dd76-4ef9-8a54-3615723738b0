package com.bees360.service.impl;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.stereotype.Service;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.UserPaymentProfile;
import com.bees360.entity.enums.productandpayment.GlobalDiscountTypeEnum;
import com.bees360.entity.vo.WalletGlobalDiscount;
import com.bees360.mapper.payment.UserPaymentProfileMapper;
import com.bees360.service.AccountWalletService;
import com.bees360.util.DoubleArithmetic;

@Service("accountWalletService")
public class AccountWalletServiceImpl implements AccountWalletService {

	@Inject
	private UserPaymentProfileMapper userPaymentProfileMapper;

	@Override
	public List<WalletGlobalDiscount> listWalletDiscount(long userId) throws ServiceException {
		UserPaymentProfile userPaymentProfile = userPaymentProfileMapper.getUserPaymentProfile(userId);
		return assembleWalletDiscount(userPaymentProfile);
	}

	@Override
	public WalletGlobalDiscount getWalletGlobalDiscount(long userId, int discountType) throws ServiceException {
		if(GlobalDiscountTypeEnum.getEnum(discountType) == null) {
			return null;
		}
		List<WalletGlobalDiscount> discounts = listWalletDiscount(userId);
		for(WalletGlobalDiscount discount: discounts) {
			if(discount.getDiscountType() == discountType) {
				return discount;
			}
		}
		return null;
	}

	private List<WalletGlobalDiscount> assembleWalletDiscount(UserPaymentProfile userPaymentProfile) {
		List<WalletGlobalDiscount> discounts = new ArrayList<WalletGlobalDiscount>();

		if(userPaymentProfile.getFreeOnTrailProjectNum() > 0) {
			WalletGlobalDiscount walletDiscount = new WalletGlobalDiscount();
			GlobalDiscountTypeEnum type = GlobalDiscountTypeEnum.FREE_PROJECT_NUMBER;
			walletDiscount.setDiscountType(type.getCode());
			walletDiscount.setDiscountName(type.getDisplay());
			walletDiscount.setIsNumberLimited(type.isNumberLimited());
			walletDiscount.setOffType(type.getOffType().getCode());
			walletDiscount.setNum(userPaymentProfile.getFreeOnTrailProjectNum());
			// FREE_PROJECT_NUMBER 的折扣为按照百分比进行折扣，折扣率为 100%
			walletDiscount.setValue(1);
			discounts.add(walletDiscount);
		}
		if(userPaymentProfile.getNewCustomerDiscountProjectNum() > 0) {
			WalletGlobalDiscount walletDiscount = new WalletGlobalDiscount();
			GlobalDiscountTypeEnum type = GlobalDiscountTypeEnum.LIMITED_DISCOUNT;
			walletDiscount.setDiscountType(type.getCode());
			walletDiscount.setDiscountName(type.getDisplay());
			walletDiscount.setIsNumberLimited(type.isNumberLimited());
			walletDiscount.setOffType(type.getOffType().getCode());
			walletDiscount.setNum(userPaymentProfile.getNewCustomerDiscountProjectNum());
			walletDiscount.setValue(userPaymentProfile.getNewCustomerDiscountPercent());

			discounts.add(walletDiscount);
		}
		if(userPaymentProfile.getDiscountPercent() > 0) {
			WalletGlobalDiscount walletDiscount = new WalletGlobalDiscount();
			GlobalDiscountTypeEnum type = GlobalDiscountTypeEnum.UNLIMITED_DISCOUNT;
			walletDiscount.setDiscountType(type.getCode());
			walletDiscount.setDiscountName(type.getDisplay());
			walletDiscount.setOffType(type.getOffType().getCode());
			walletDiscount.setIsNumberLimited(type.isNumberLimited());
			// UNLIMITED_DISCOUNT 类型折扣与用户所含有的该折扣的数目无关
			walletDiscount.setNum(0);
			walletDiscount.setValue(userPaymentProfile.getDiscountPercent());

			discounts.add(walletDiscount);
		}
		return discounts;
	}

	@Override
	public int subDiscount(long curUserId, int discountType, int num) throws ServiceException {
		GlobalDiscountTypeEnum discountTypeEnum = GlobalDiscountTypeEnum.getEnum(discountType);
		if(discountTypeEnum == null) {
			throw new ServiceException(MessageCode.TRANSACTION_DISCOUNT_NOT_EXIST);
		}
		if(discountTypeEnum == GlobalDiscountTypeEnum.NO_DISCOUNT) {
			return 0;
		}
		WalletGlobalDiscount discount = getWalletGlobalDiscount(curUserId, discountType);
		if(discount == null || !discount.isAvariable()) {
			throw new ServiceException(MessageCode.TRANSACTION_DISCOUNT_UNAVAILABLE);
		}
		UserPaymentProfile userPaymentProfile = userPaymentProfileMapper.getUserPaymentProfile(curUserId);

		switch(discountTypeEnum) {
		case FREE_PROJECT_NUMBER:
			if(userPaymentProfile.getFreeOnTrailProjectNum() < num) {
				throw new ServiceException(MessageCode.TRANSACTION_DISCOUNT_NOT_ENOUGH);
			}
			userPaymentProfileMapper.subFreeOnTrailProjectNum(curUserId, num);
			return userPaymentProfile.getFreeOnTrailProjectNum() - num;
		case LIMITED_DISCOUNT:
			if(userPaymentProfile.getNewCustomerDiscountProjectNum() < num) {
				throw new ServiceException(MessageCode.TRANSACTION_DISCOUNT_NOT_ENOUGH);
			}
			userPaymentProfileMapper.subNewCustomerDiscountProjectNum(curUserId, num);
			return userPaymentProfile.getNewCustomerDiscountProjectNum() - num;
		default: {
			// do nothing
			return 0;
		}
		}
	}

	@Override
	public double getWalletBalance(long userId) throws ServiceException {
		UserPaymentProfile userPaymentProfile = userPaymentProfileMapper.getUserPaymentProfile(userId);
		return userPaymentProfile.getWalletBalance();
	}
}
