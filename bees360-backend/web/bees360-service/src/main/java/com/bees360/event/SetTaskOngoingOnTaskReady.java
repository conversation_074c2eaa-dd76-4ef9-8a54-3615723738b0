package com.bees360.event;

import com.bees360.api.InvalidArgumentException;
import com.bees360.event.registry.PipelineTaskChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.Message;
import com.bees360.pipeline.PipelineService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Objects;
import java.util.Set;

import static com.bees360.pipeline.Message.PipelineStatus.ONGOING;

/**
 * 监听PipelineTaskChanged事件，当任务状态为READY且未被删除时，将指定任务的状态设置为ONGOING。
 */
@Log4j2
public class SetTaskOngoingOnTaskReady extends AbstractNamedEventListener<PipelineTaskChanged>{
    private final Set<String> taskKeys;
    private final PipelineService pipelineService;

    public SetTaskOngoingOnTaskReady(Set<String> taskKeys, PipelineService pipelineService) {
        this.taskKeys = taskKeys;
        this.pipelineService = pipelineService;

        log.info(
                "Created '{}(taskKeys={}, pipelineService={})'",
                this,
                this.taskKeys,
                pipelineService);
    }

    @Override
    public void handle(PipelineTaskChanged pipelineTaskChanged) throws IOException {
        var state = pipelineTaskChanged.getState();
        var key = pipelineTaskChanged.getTaskDefKey();
        if (!taskKeys.contains(key)) {
            return;
        }

        if (!Objects.equals(state.getStatus(), Message.PipelineStatus.READY) || state.isDeleted()) {
            return;
        }

        var pipelineId = pipelineTaskChanged.getPipelineId();
        try {
            pipelineService.setTaskStatus(pipelineId, key, ONGOING);
            log.info("Successfully set pipeline '{}' key '{}' to '{}'", pipelineId, key, ONGOING);
        } catch (IllegalArgumentException | InvalidArgumentException e) {
            log.warn("Failed set pipeline '{}' key '{}' to '{}'", pipelineId, key, ONGOING, e);
        }
    }
}
