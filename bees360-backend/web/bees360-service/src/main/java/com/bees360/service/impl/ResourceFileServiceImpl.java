package com.bees360.service.impl;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.service.ResourceFileService;
import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.Objects;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class ResourceFileServiceImpl implements ResourceFileService {

    @Autowired
    private ResourcePool resourcePool;

    @Override
    public long getReportContentLength(String resourceKey) throws IOException {
        ResourceMetadata metadata;
        try {
            metadata = resourcePool.head(resourceKey);
        } catch (UncheckedIOException ex) {
            throw ex.getCause();
        }
        if (Objects.isNull(metadata) || Objects.isNull(metadata.getContentLength())) {
            throw new ResourceNotFoundException("report key:" + resourceKey + " not found.");
        }
        return metadata.getContentLength();
    }
}
