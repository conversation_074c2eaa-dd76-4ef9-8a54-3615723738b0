package com.bees360.event;

import com.bees360.event.registry.ProjectOperatingCompanyChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectOperatingCompanyMapper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

/**
 * 监听项目运营公司变更事件并更新MySQL数据库中的运营公司信息
 */
@Log4j2
public class UpdateMySqlOnOperatingCompanyChanged extends AbstractNamedEventListener<ProjectOperatingCompanyChanged> {
    private final ProjectOperatingCompanyMapper projectOperatingCompanyMapper;

    public UpdateMySqlOnOperatingCompanyChanged(
        ProjectOperatingCompanyMapper projectOperatingCompanyMapper) {
        this.projectOperatingCompanyMapper = projectOperatingCompanyMapper;
        log.info("Created '{}(projectOperatingCompanyMapper={})'", this, projectOperatingCompanyMapper);
    }

    @Override
    public void handle(ProjectOperatingCompanyChanged projectOperatingCompanyChanged) throws IOException {
        var projectId = Long.parseLong(projectOperatingCompanyChanged.getProjectId());
        var operatingCompany = projectOperatingCompanyChanged.getOperatingCompany();
        String oldOperatingCompany = projectOperatingCompanyMapper.getByProjectId(projectId);
        if (StringUtils.isNotEmpty(oldOperatingCompany)
            && !oldOperatingCompany.equals(operatingCompany)) {
            projectOperatingCompanyMapper.insert(operatingCompany, projectId);
        }
        if (StringUtils.isEmpty(oldOperatingCompany)) {
            projectOperatingCompanyMapper.insert(operatingCompany, projectId);
        }
    }
}
