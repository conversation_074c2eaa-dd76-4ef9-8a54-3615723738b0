package com.bees360.service.export.impl;

import com.alibaba.excel.EasyExcel;
import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.UncheckedServiceException;
import com.bees360.entity.ProjectMessageVo;
import com.bees360.entity.User;
import com.bees360.entity.dto.ProjectSearchOption;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProcessStatusEnum;
import com.bees360.entity.label.ProjectLabel;
import com.bees360.entity.vo.ProjectPageResultVo;
import com.bees360.entity.vo.ProjectStatusVo;
import com.bees360.entity.vo.ProjectTinyVo;
import com.bees360.flyzone.FlyZoneType;
import com.bees360.project.Message;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.project.ServiceTypeEnum;
import com.bees360.project.tag.ProjectTag;
import com.bees360.resource.Resource;
import com.bees360.resource.ResourceMetadata;
import com.bees360.resource.ResourcePool;
import com.bees360.service.ProjectService;
import com.bees360.service.export.ProjectDataExcelProvider;
import com.bees360.util.DateTimes;
import com.bees360.util.Functions;
import com.bees360.util.GeneratingInputStream;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.OutputStream;
import java.io.UncheckedIOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.stream.Collectors;


@Log4j2
@Component
public class ProjectDataExcelProviderImpl implements ProjectDataExcelProvider {

    private static final String EXCEL_TEMPLATE_FILE = "export/project/project_export_template.xlsx";

    private static final String CLAIMS_CLIENT_RECEIVED_EXCEL_FILE = "CL_Client_Received_%s_to_%s.xlsx";

    private static final String UNDERWRITING_RETURN_TO_CLIENT_EXCEL_FILE = "UW_Return_To_Client_%s_to_%s.xlsx";

    private static final String PROJECT_CREATED_BETWEEN_EXCEL_FILE = "project_created_between_%s_to_%s.xlsx";

    private static final String PROJECT_OPEN_EXCEL_FILE = "open_cases_statistics.xlsx";

    private static final String EXCEL_CONTENT_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

    private static final List<Integer> CLAIM_SERVICE_TYPES = Arrays.stream(ServiceTypeEnum.values())
        .filter(serviceTypeEnum -> serviceTypeEnum.getProjectType().equals(ProjectTypeEnum.CLAIM))
        .map(ServiceTypeEnum::getCode).collect(Collectors.toList());

    private static final List<Integer> UNDERWRITING_SERVICE_TYPES = Arrays.stream(ServiceTypeEnum.values())
        .filter(serviceTypeEnum -> serviceTypeEnum.getProjectType().equals(ProjectTypeEnum.UNDERWRITING))
        .map(ServiceTypeEnum::getCode).collect(Collectors.toList());

    public static final DateTimeFormatter DATE_TIME = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm");

    public static final DateTimeFormatter DATE = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    private final ResourcePool resourcePool;
    private final ProjectService projectService;

    public ProjectDataExcelProviderImpl(ResourcePool resourcePool, ProjectService projectService) {
        this.resourcePool = resourcePool;
        this.projectService = projectService;
    }

    @Override
    public String claimsClientReceivedBetween(Instant startTime, Instant endTime) {
        ProjectSearchOption searchOption = new ProjectSearchOption();
        searchOption.setProjectStatusStartTime(startTime.toEpochMilli());
        searchOption.setProjectStatusEndTime(endTime.toEpochMilli());
        searchOption.setProjectStatusList(List.of(NewProjectStatusEnum.CLIENT_RECEIVED.getCode()));
        searchOption.setServiceTypes(CLAIM_SERVICE_TYPES);
        return createExportFile(CLAIMS_CLIENT_RECEIVED_EXCEL_FILE, startTime, endTime, searchOption);
    }

    @Override
    public String underwritingReturnToClientBetween(Instant startTime, Instant endTime) {
        ProjectSearchOption searchOption = new ProjectSearchOption();
        searchOption.setProjectStatusStartTime(startTime.toEpochMilli());
        searchOption.setProjectStatusEndTime(endTime.toEpochMilli());
        searchOption.setProjectStatusList(List.of(NewProjectStatusEnum.RETURNED_TO_CLIENT.getCode()));
        searchOption.setServiceTypes(UNDERWRITING_SERVICE_TYPES);
        return createExportFile(UNDERWRITING_RETURN_TO_CLIENT_EXCEL_FILE, startTime, endTime, searchOption);
    }

    @Override
    public String projectCreatedBetween(Instant startTime, Instant endTime) {
        ProjectSearchOption searchOption = new ProjectSearchOption();
        searchOption.setStartTime(startTime.toEpochMilli());
        searchOption.setEndTime(endTime.toEpochMilli());
        return createExportFile(PROJECT_CREATED_BETWEEN_EXCEL_FILE, startTime, endTime, searchOption);
    }

    @Override
    public String allProjectOpen() {
        ProjectSearchOption searchOption = new ProjectSearchOption();
        searchOption.setProjectStateList(
            List.of(Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_OPEN,
                    Message.ProjectMessage.ProjectState.ProjectStateEnum.PROJECT_PAUSE));
        return createExportFile(PROJECT_OPEN_EXCEL_FILE, searchOption);
    }

    private String createExportFile(String fileNameTemplate, Instant startTime,
                                    Instant endTime, ProjectSearchOption searchOption) {
        ZoneId cst = DateTimes.DEFAULT_US_ZONE_ID;
        String startDate = DateTimes.format(startTime.atZone(cst).toLocalDate());
        String endDate = DateTimes.format(endTime.atZone(cst).toLocalDate());
        String fileName = fileNameTemplate.formatted(startDate, endDate);
        return createExportFile(fileName, searchOption);
    }

    private String createExportFile(String fileName, ProjectSearchOption searchOption) {
        final List<ProjectTinyVo> projectTinyVos;
        try {
            projectTinyVos = searchData(searchOption);
        } catch (ServiceException e) {
            throw new UncheckedServiceException(e);
        }
        try(GeneratingInputStream input = new GeneratingInputStream(out -> writeExcel(projectTinyVos, out))) {
            Resource resource = Resource.of(input,
                ResourceMetadata.newBuilder().setContentType(EXCEL_CONTENT_TYPE).build());
            resourcePool.put(fileName, resource);
        } catch (IOException e) {
            throw new UncheckedIOException(
                "Failed to upload excel file:%s to resource pool".formatted(fileName), e);
        }

        log.info("Finish upload project export file {} with projects size {}.", fileName, projectTinyVos.size());
        return fileName;
    }

    private void writeExcel(List<ProjectTinyVo> projects, OutputStream outputStream) {
        List<ExportProjectData> list = projects.stream().map(tiny -> {
            ExportProjectData export = new ExportProjectData();
            BeanUtils.copyProperties(tiny, export);
            Functions.acceptIfNotNull(export::setCreatedDate,
                tiny.getCreatedTime(), ProjectDataExcelProviderImpl::fromEpoch);
            Functions.acceptIfNotNull(export::setFirstCompletionDate,
                tiny.getStatusFirstReturnToClient(), ProjectDataExcelProviderImpl::fromProjectStatus);
            Functions.acceptIfNotNull(export::setLastCompletionDate,
                tiny.getStatusLastReturnToClient(), ProjectDataExcelProviderImpl::fromProjectStatus);
            String airspace = Optional.ofNullable(FlyZoneType.getEnum(tiny.getFlyZoneType()))
                .map(FlyZoneType::getDisplay).orElse("");
            export.setAirspace(airspace);
            Functions.acceptIfNotNull(export::setInitialContactTime,
                tiny.getInitialCustomerContactTime(), ProjectDataExcelProviderImpl::fromInstant);
            Functions.acceptIfNotNull(export::setCustomerContactedTimeStr,
                tiny.getStatusCustomerContacted(), ProjectDataExcelProviderImpl::fromProjectStatus);
            Functions.acceptIfNotNull(export::setAssignedToPilotTime,
                tiny.getStatusAssignedToPilot(), ProjectDataExcelProviderImpl::fromProjectStatus);
            Functions.acceptIfNotNull(export::setInspectionDeadline,
                tiny.getInspectionDueDate(), ProjectDataExcelProviderImpl::fromEpoch);
            Functions.acceptIfNotNull(export::setInspectionScheduledTime,
                tiny.getScheduledTime(), ProjectDataExcelProviderImpl::fromEpoch);
            Functions.acceptIfNotNull(export::setSiteInspectedTime,
                tiny.getStatusSiteInspected(), ProjectDataExcelProviderImpl::fromProjectStatus);
            Functions.acceptIfNotNull(export::setImageUploadedTime,
                tiny.getStatusImageUploaded(), ProjectDataExcelProviderImpl::fromProjectStatus);
            Functions.acceptIfNotNull(export::setIBeesUploadedTime,
                tiny.getStatusIBEESUploaded(), ProjectDataExcelProviderImpl::fromProjectStatus);
            Functions.acceptIfNotNull(export::setClientReceivedTime,
                tiny.getStatusClientReceived(), ProjectDataExcelProviderImpl::fromProjectStatus);
            Functions.acceptIfNotNull(export::setPaymentDate, tiny.getPlanPaymentDate(), DATE::format);
            Functions.acceptIfNotNull(export::setPolicyEffectiveDate, tiny.getPolicyEffectiveDate(), DATE::format);
            Functions.acceptIfNotNull(export::setTimeZone,
                DateTimes.DEFAULT_US_ZONE_ID.getDisplayName(TextStyle.FULL, Locale.ENGLISH));
            Functions.acceptIfNotNull(export::setPaid, tiny.getPayStatusName());
            String canceledStatus = tiny.getLatestStatus() == ProcessStatusEnum.CANCELED.getCode() ? "Y" : "N";
            Functions.acceptIfNotNull(export::setCanceled, canceledStatus);
            Functions.acceptIfNotNull(export::setDeleted, tiny.isDeleted() ? "Y" : "N");
            Functions.acceptIfNotNull(export::setTags,
                tiny.getProjectTags(), ProjectDataExcelProviderImpl::parseProjectTag);
            Functions.acceptIfNotNull(export::setOperationTag,
                tiny.getProjectLabels(), ProjectDataExcelProviderImpl::parseProjectLabel);
            Functions.acceptIfNotNull(export::setTagCreatedTime,
                tiny.getLatestModifyTagTime(), ProjectDataExcelProviderImpl::fromEpoch);
            Functions.acceptIfNotNull(export::setPilotFeedBack,
                tiny.getProjectMessage(), ProjectDataExcelProviderImpl::parsePilotFeedBack);
            Functions.acceptIfNotNull(export::setChangeReasonGroup,
                tiny.getChangeReasonGroup());
            Functions.acceptIfNotNull(export::setProjectStateChangeReason,
                tiny.getProjectStateChangeReason());
            return export;
        }).collect(Collectors.toList());
        ClassLoader classLoader = this.getClass().getClassLoader();
        EasyExcel.write().withTemplate(classLoader.getResourceAsStream(EXCEL_TEMPLATE_FILE))
            .file(outputStream).sheet().doFill(list);
    }

    private List<ProjectTinyVo> searchData(ProjectSearchOption searchOption) throws ServiceException {
        int pageSize = 1000;
        searchOption.setPageSize(pageSize);
        searchOption.setDeletedInclusion(true);
        final ArrayList<ProjectTinyVo> list = new ArrayList<>();
        for (int page = 1;;page++) {
            searchOption.setPageIndex(page);
            ProjectPageResultVo exportProject =
                projectService.getExportProjectList(User.BEES_PILOT_SYSTEM, searchOption);
            List<ProjectTinyVo> projects = exportProject.getProjects();
            if (projects.isEmpty()) {
                break;
            }
            list.addAll(projects);
        }
        return list;
    }

    @Getter
    @Setter
    static class ExportProjectData {

        private long projectId;
        private String insuranceCompanyName;
        private String repairCompanyName;
        private String policyNumber;
        private String policyType;
        private String inspectionNumber;
        private Integer numberOfOutbuildings;
        private Integer numberOfInteriorRooms;

        private String assetOwnerName;
        private String assetOwnerPhone;
        private String assetOwnerEmail;
        private String creatorEmail;
        private String creatorPhone;
        private String creatorName;
        private String pilot;
        private String externalAdjusterName;

        private String address;
        private String city;
        private String state;
        private String country;
        private String zipCode;
        private double gpsLocationLongitude;
        private double gpsLocationLatitude;
        private String airspace;

        private String batchNo;
        private BigDecimal riskScore;
        private String catNumber;

        private Float hiveDistance;
        private BigDecimal batchTotalPay;

        private String serviceTypeName;

        private String createdDate;
        private String firstCompletionDate;
        private String lastCompletionDate;
        private String projectStatusName;
        private String initialContactTime;
        private String customerContactedTimeStr;
        private String assignedToPilotTime;
        private String inspectionDeadline;
        private String inspectionScheduledTime;
        private String siteInspectedTime;
        private String imageUploadedTime;
        private String iBeesUploadedTime;
        private String clientReceivedTime;
        private String paymentDate;
        private String policyEffectiveDate;
        private Integer daysOld;
        private String timeZone;

        private String paid;
        private String canceled;
        private String deleted;
        private String operationTag;
        private String tags;
        private String tagCreatedTime;
        private String latestModifyTagUserName;
        private String pilotFeedBack;
        private String changeReasonGroup;
        private String projectStateChangeReason;
    }

    private static String parsePilotFeedBack(List<ProjectMessageVo> projectMessageVos) {
        return projectMessageVos.stream().map(ProjectMessageVo::getContent).collect(Collectors.joining(", "));
    }

    private static String parseProjectLabel(List<ProjectLabel> projectLabels) {
        return projectLabels.stream().map(ProjectLabel::getLabelName).collect(Collectors.joining(", "));
    }

    private static String parseProjectTag(List<? extends ProjectTag> projectTags) {
        return projectTags.stream().map(ProjectTag::getTitle).collect(Collectors.joining(", "));
    }

    static String fromInstant(Instant instant) {
        if (instant == null) {
            return "";
        }
        return fromEpoch(instant.toEpochMilli());
    }

    static String fromProjectStatus(ProjectStatusVo projectStatusVo) {
        if (projectStatusVo == null) {
            return "";
        }
        return fromEpoch(projectStatusVo.getCreatedTime());
    }


    static String fromEpoch(Long millis) {
        if (millis == null) {
            return "";
        }
        LocalDateTime localDateTime = DateTimes.fromEpochMilli(millis, DateTimes.DEFAULT_US_ZONE_ID);
        return DATE_TIME.format(localDateTime);
    }
}
