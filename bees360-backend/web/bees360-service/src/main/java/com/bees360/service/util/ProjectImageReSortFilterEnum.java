package com.bees360.service.util;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.firebase.ImageCategoryEnum;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;

import java.util.function.Predicate;

@Log4j2
@Getter
public enum ProjectImageReSortFilterEnum {
    /** 只对interiorImage进行排序 */
    INTERIOR_ROOM_NAME_PATH(
            image ->
                    StringUtils.startsWithIgnoreCase(
                            image.getImageCategory(), ImageCategoryEnum.INTERIOR.getDisplay())),
    /** 不改变任何顺序,即不对任何图片进行排序 */
    DEFAULT(path -> false);
    /** 过滤出需要排序的image */
    private final Predicate<ProjectImage> filter;

    ProjectImageReSortFilterEnum(Predicate<ProjectImage> filter) {
        this.filter = filter;
    }
}
