package com.bees360.service;

import java.util.Map;
import java.util.Set;

import com.bees360.base.exception.ServiceException;

public interface ConstantsService {
	public static final String COMPANY_TYPES_OPTIONS = "companyTypes";
	public static final String PROJECT_TYPES_OPTIONS = "projectTypes";
	public static final String PROJECT_STATUS_OPTIONS = "projectStatuses";
	public static final String CLAIM_TYPE_OPTIONS = "claimTypes";
	public static final String ROLES_OPTIONS = "roles";
	public static final String SHINGLE_TYPE_OPTIONS = "shingleTypes";
	public static final String INSPECTION_CATEGORY_OPTIONS = "inspectionCategory";
	public static final String POSITION_TYPE_OPTIONS = "positionType";
	public static final String DAMAGE_SEVERITY_OPTIONS = "damageSeverity";
	public static final String FILE_SOURCE_TYPE_OPTIONS = "fileSourceType";
	public static final String FILTER_IMAGES_OPTIONS = "filterImages";
	public static final String DIRECTION_OPTIONS = "directionEnum";
	public static final String SHINGLE_LAYER_OPTIONS = "shingleLayer";
	public static final String OBJECTS_ON_ROOFING_OPTIONS = "objectsOnRoofing";
	public static final String PROJECT_COMPONENT_TYPE_OPTIONS = "componentTypes";
	public static final String SEGMENT_ROOT_OPTIONS = "segmentRootTypes";
	public static final String PROCESS_STATUS = "processStatuses";
	public static final String ROOF_AGES = "roofAges";
	public static final String IMAGE_TYPE = "imageType";
	public static final String SERVICE_TYPES = "serviceTypes";
	String NEW_PROJECT_STATUSES = "newProjectStatuses";
    String MOBILE_IMAGE_CATEGORY = "mobileImageCategory";

	public Map<String, Object> getStaticOptionsMap(Set<String> options) throws ServiceException;
}
