package com.bees360.event;

import com.bees360.activity.Activities;
import com.bees360.activity.Activity;
import com.bees360.activity.Message;
import com.bees360.event.registry.ProjectAddressChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ActivityService;
import com.google.protobuf.FieldType;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * This class is for submit an activity when project address changed event happens.
 */
@Log4j2
public class AddActivityOnProjectAddressChangedEvent extends AbstractNamedEventListener<ProjectAddressChangedEvent> {

    private final ActivityService activityService;

    public AddActivityOnProjectAddressChangedEvent(ActivityService activityService) {
        this.activityService = activityService;
    }

    @Override
    public void handle(ProjectAddressChangedEvent event) throws IOException {
        log.info("Received event: {}", event);
        var field = Message.ActivityMessage.Field.newBuilder()
            .setName(Message.ActivityMessage.FieldName.ADDRESS.name())
            .setType(Message.ActivityMessage.FieldType.STRING.name())
            .setOldValue(event.getOldAddress())
            .setValue(event.getNewAddress())
            .build();
        var activity = Activities.changeProjectField(
            Long.parseLong(event.getProjectId()), event.getUpdatedBy(), field, event.getUpdatedAt());
        activityService.execAsyncRetryable(activity);
    }
}
