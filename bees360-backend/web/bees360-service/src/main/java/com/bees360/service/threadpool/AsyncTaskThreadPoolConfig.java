package com.bees360.service.threadpool;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 线程池配置
 *
 * <AUTHOR>
 * @date 2021/06/04 12:11
 */
@Configuration
@EnableAsync
public class AsyncTaskThreadPoolConfig {

    private static final int CORE_POOL_SIZE = 10;
    private static final int MAXIMUM_POOL_SIZE = CORE_POOL_SIZE;
    private static final long KEEP_ALIVE_TIME = 30L;
    private static final BlockingQueue<Runnable> BLOCKING_QUEUE = new LinkedBlockingDeque<>();

    /**
     * 批量生成报告的线程池
     */
    @Bean("createBeeProjectThreadPool")
    public Executor createBeeProjectThreadPool() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_POOL_SIZE, MAXIMUM_POOL_SIZE,
            KEEP_ALIVE_TIME, TimeUnit.SECONDS, BLOCKING_QUEUE);
        //超时回收核心线程
        executor.allowCoreThreadTimeOut(true);
        return executor;
    }

}
