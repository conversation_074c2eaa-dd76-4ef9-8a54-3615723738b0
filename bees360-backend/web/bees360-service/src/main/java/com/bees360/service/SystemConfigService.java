package com.bees360.service;

import com.bees360.entity.SystemConfig;
import com.bees360.entity.dto.systemconfig.EmailRecipientConfig;
import com.bees360.entity.dto.systemconfig.SystemConfigBees360Dto;
import com.bees360.entity.dto.systemconfig.SystemConfigProjectDto;
import com.bees360.entity.dto.systemconfig.SystemConfigProjectServiceChangeDto;
import com.bees360.entity.util.SystemConfigPrefix;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> <PERSON>
 * @date 2019/12/23 19:52
 */
public interface SystemConfigService {
    List<SystemConfig> listAll();

    List<SystemConfig> listWithPrefix(String prefix);

    SystemConfigProjectDto getSystemConfigProject();

    SystemConfigProjectDto updateSystemConfigProject(SystemConfigProjectDto systemConfigProjectDto);

    SystemConfigBees360Dto getSystemConfigBees360();

    SystemConfigBees360Dto updateSystemConfigBees360(SystemConfigBees360Dto systemConfigBees360Dto);

    Optional<SystemConfigProjectServiceChangeDto> getSystemConfigProjectServiceChange();

    SystemConfigProjectServiceChangeDto upsertSystemConfigProjectServiceChange(
        SystemConfigProjectServiceChangeDto serviceChangeDto);

    <T extends SystemConfigPrefix> void updateSystemConfig(T systemConfigObject);

    void upsertSystemConfig(SystemConfig config);
}
