package com.bees360.service.util;

import com.bees360.entity.ProjectImage;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.map.CaseInsensitiveMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

@Log4j2
@Configuration
@EnableConfigurationProperties
public class ProjectImageReSorterConfig {

    @Data
    static class ReSorterProperties {
        /** 需要保证权重优先级一致,因此通过List注入 */
        private List<TagPathWeightGroup> sortedByTagPathWeight = new ArrayList<>();
        /** 默认值,当在配置中找不到对应权重时,权重值为该值 */
        private Integer defaultWeight = 999;
        /** 分隔符,既是imageCategory的分隔符也是最终权重值的拼接分隔符 */
        private String weightDelimiter = "/";

        @Data
        static class TagPathWeightGroup {
            private Map<String, Integer> tagPathWeight = new CaseInsensitiveMap<>();
        }
    }

    @Bean
    @ConfigurationProperties(prefix = "image.resorter-config")
    ReSorterProperties reSorterProperties() {
        return new ReSorterProperties();
    }

    @Bean("projectImageRoomComparator")
    Comparator<ProjectImage> projectImageRoomComparator() {
        // 提取可能在beesPath中跟随的roomNumber,不存在则转换为默认值0
        Function<ProjectImage, Integer> roomNumberConverter =
            projectImage ->
                Optional.ofNullable(
                        StringUtils.substringBetween(
                            projectImage.getImageCategory(),
                            projectImage.getRoomName() + "%",
                            "/"))
                    .map(Integer::valueOf)
                    .orElse(0);
        return Comparator.comparing(ProjectImage::getRoomName).thenComparing(roomNumberConverter);
    }

    /**
     * 权重计算逻辑如下: <br>
     * 通过分隔符切割出每段子路径,根据配置逐个优先级计算子路径对应权重值 <br>
     * 同一优先级中匹配到多个权重值则取最小值 <br>
     * 最终将各优先级的权重值拼接为最终权重值字符串 <br>
     */
    @Bean("projectImageCategoryWeightConverter")
    Function<ProjectImage, String> projectImageCategoryWeightConverter(
        ReSorterProperties reSorterProperties) {
        var tagPathWeightGroups =
            reSorterProperties.getSortedByTagPathWeight().stream()
                .map(ReSorterProperties.TagPathWeightGroup::getTagPathWeight)
                .collect(Collectors.toList());
        var defaultWeight = reSorterProperties.getDefaultWeight();
        var weightDelimiter = reSorterProperties.getWeightDelimiter();

        Function<ProjectImage, String> converter =
            projectImage -> {
                var tagPaths = projectImage.getImageCategory().split(weightDelimiter);
                return tagPathWeightGroups.stream()
                    .map(
                        tagPathWeightGroup ->
                            Arrays.stream(tagPaths)
                                .map(
                                    path ->
                                        tagPathWeightGroup.getOrDefault(
                                            path, defaultWeight))
                                .reduce(Integer::min)
                                .orElse(defaultWeight))
                    .map(weight -> "%03d".formatted(weight))
                    .reduce((w1, w2) -> StringUtils.joinWith(weightDelimiter, w1, w2))
                    .orElse("");
            };
        log.info(
            "Created projectImageCategoryWeightConverter {}"
                + "(tagPathWeightGroups={}, defaultWeight={}, delimiter={}).",
            converter,
            tagPathWeightGroups,
            defaultWeight,
            weightDelimiter);
        return converter;
    }

    @Bean
    public UnaryOperator<List<ProjectImage>> projectImageReSorter(
        @Value("${image.resorter:DEFAULT}") ProjectImageReSortFilterEnum projectImageReSorter,
        @Qualifier("projectImageRoomComparator")
        Comparator<ProjectImage> projectImageRoomComparator,
        @Qualifier("projectImageCategoryWeightConverter")
        Function<ProjectImage, String> projectImageCategoryWeightConverter) {
        var filter = projectImageReSorter.getFilter();

        UnaryOperator<List<ProjectImage>> reSorter =
            projectImages -> {
                var images = new ArrayList<>(projectImages);
                // 先按照imageSort排序避免出现原本顺序混乱的问题
                images.sort(Comparator.comparing(ProjectImage::getImageSort));
                var filterList = images.stream().filter(filter).collect(Collectors.toList());
                log.debug("Start resort images: {}.", () -> filterList);
                // 保留原本imageSort
                var originSortList =
                    filterList.stream()
                        .map(ProjectImage::getImageSort)
                        .collect(Collectors.toList());

                // 计算projectImage的category权重值
                var imageWeights =
                    filterList.stream()
                        .collect(
                            Collectors.toMap(
                                ProjectImage::getImageCategory,
                                projectImageCategoryWeightConverter,
                                (k1, k2) -> k1));

                // 先比较roomName&roomNumber,再比较category权重,最后再根据字典升序排序
                var comparator =
                    projectImageRoomComparator
                        .thenComparing(
                            image -> imageWeights.get(image.getImageCategory()))
                        .thenComparing(
                            ProjectImage::getImageCategory,
                            StringUtils::compareIgnoreCase);
                filterList.sort(comparator);
                // 排序后位置发生变换,重新赋值imageSort使得排序真正生效
                var imageIterator = filterList.iterator();
                var originSortIterator = originSortList.iterator();
                while (imageIterator.hasNext()) {
                    imageIterator.next().setImageSort(originSortIterator.next());
                }
                images.sort(Comparator.comparing(ProjectImage::getImageSort));
                log.debug("After resort images: {}.", () -> images);
                return images;
            };

        log.info(
            "Create project image reSorter {}(filter={}, projectImageRoomComparator={},"
                + " projectImageCategoryWeightConverter={}).",
            reSorter,
            filter,
            projectImageRoomComparator,
            projectImageCategoryWeightConverter);
        return reSorter;
    }
}
