package com.bees360.event;

import com.bees360.event.registry.InteriorDamageStatusChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.pipeline.PipelineDefService;
import com.bees360.pipeline.PipelineService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听内部损坏状态变化事件，根据损坏状态更新管道定义。
 */
@Log4j2
public class ChangePipelineOnInteriorDamage
        extends AbstractNamedEventListener<InteriorDamageStatusChangedEvent> {

    public static String CLAIM_INTERIOR_PIPELINE_DEF_PREFIX = "interior_";
    private final PipelineService pipelineService;
    private final PipelineDefService pipelineDefService;

    public ChangePipelineOnInteriorDamage(
            PipelineService pipelineService, PipelineDefService pipelineDefService) {
        this.pipelineService = pipelineService;
        this.pipelineDefService = pipelineDefService;
        log.info(
                "Created '{}(pipelineService={}, pipelineDefService={})",
                this,
                pipelineService,
                pipelineDefService);
    }

    @Override
    public void handle(InteriorDamageStatusChangedEvent event) throws IOException {
        var pipelineId = event.getProjectId();
        var defKey = pipelineService.findPipelineDefKey(pipelineId);
        if (defKey == null) {
            return;
        }
        var hasInteriorDamage = event.isHasInteriorDamage();
        if (hasInteriorDamage) {
            addInteriorDamageToPipeline(pipelineId, defKey);
        } else {
            removeInteriorDamageFromPipeline(pipelineId, defKey);
        }
    }

    private void addInteriorDamageToPipeline(String pipelineId, String defKey) {
        if (defKey.startsWith(CLAIM_INTERIOR_PIPELINE_DEF_PREFIX)) {
            log.info(
                    "Skipped to change pipeline '(id={}, key={})' definition because it is already changed.",
                    pipelineId,
                    defKey);
            return;
        }

        var newDefKey = CLAIM_INTERIOR_PIPELINE_DEF_PREFIX + defKey;
        var def = pipelineDefService.findByKey(newDefKey);
        if (def == null) {
            log.info(
                    "Skipped to change pipeline '{}' definition key to '{}' because it is not found.",
                    pipelineId,
                    newDefKey);
            return;
        }

        pipelineService.changePipelineDef(pipelineId, newDefKey);
        log.info(
                "Successfully change pipeline {} definition key from '{}' to '{}'.",
                pipelineId,
                defKey,
                newDefKey);
    }

    private void removeInteriorDamageFromPipeline(String pipelineId, String defKey) {
        if (!defKey.startsWith(CLAIM_INTERIOR_PIPELINE_DEF_PREFIX)) {
            log.info(
                    "Skipped to change pipeline '(id={}, key={})' definition because it is already changed.",
                    pipelineId,
                    defKey);
            return;
        }

        var newDefKey = defKey.replaceFirst(CLAIM_INTERIOR_PIPELINE_DEF_PREFIX, "");
        var def = pipelineDefService.findByKey(newDefKey);
        if (def == null) {
            log.info(
                    "Skipped to change pipeline '{}' definition key to '{}' because it is not found.",
                    pipelineId,
                    newDefKey);
            return;
        }

        pipelineService.changePipelineDef(pipelineId, newDefKey);
        log.info(
                "Successfully change pipeline {} definition key from '{}' to '{}'.",
                pipelineId,
                defKey,
                newDefKey);
    }
}
