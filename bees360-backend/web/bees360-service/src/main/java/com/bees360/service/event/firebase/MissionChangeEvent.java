package com.bees360.service.event.firebase;

import com.bees360.entity.firebase.FirebaseMission;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;
import org.springframework.lang.NonNull;


/**
 * mission数据被修改
 * <AUTHOR>
 * @date 2020/08/27 14:18
 */
public class MissionChangeEvent extends ApplicationEvent {
    @Getter
    private final FirebaseMission mission;
    @Getter
    private final String missionId;
    @Getter
    @Setter
    private int tryTime = 0;
    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public MissionChangeEvent(@NonNull Object source, @NonNull FirebaseMission mission, @NonNull String missionId) {
        super(source);
        this.mission = mission;
        this.missionId = missionId;
    }
}
