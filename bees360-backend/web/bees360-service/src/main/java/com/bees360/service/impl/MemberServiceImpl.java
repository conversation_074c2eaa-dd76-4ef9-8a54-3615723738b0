package com.bees360.service.impl;

import static com.bees360.base.Constants.ROLE_OPERATIONS_MANAGER;

import com.bees360.base.Constants;
import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Member;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.event.EventPublisher;
import com.bees360.event.registry.OperationsManagerAssignedEvent;
import com.bees360.event.registry.ProjectMemberChangedEvent;
import com.bees360.mapper.MemberMapper;
import com.bees360.service.MemberService;
import com.bees360.service.member.PostgresMemberManagerAdapter;
import com.bees360.user.UserProvider;
import com.bees360.util.user.UserAssemble;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("memberService")
@Log4j2
public class MemberServiceImpl implements MemberService {

    private static final Logger logger = LoggerFactory.getLogger(MemberServiceImpl.class);

    @Inject
    private MemberMapper memberMapper;

    @Autowired
    private PostgresMemberManagerAdapter memberManagerAdapter;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private EventPublisher eventPublisher;

    @Override
    public List<Member> listUserRolesInProject(long projectId, long userId) throws ServiceException {
        try {
            return memberMapper.listActiveMembersByUserId(projectId, userId);
        } catch (Exception e) {
            logger.error(MessageCode.DATABASE_EXCEPTION, e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
        }
    }

    @Override
    public List<Member> listActiveUserInProjectsWithRoles(List<Long> projectIds, int roleId) {
        return memberMapper.listMembersUserIn(projectIds, null, roleId, false);
    }

    @Override
    public Member getActiveMemberByRole(long projectId, int role) {
        return memberMapper.getActiveMemberByRole(projectId, role);
    }

    @Override
    public Member getActivePilot(long projectId) {
        return memberMapper.getActiveMemberByRole(projectId, RoleEnum.PILOT.getCode());
    }

    @Override
    public Member getMemberByRoleAndUserId(long projectId, long userId, int role) {
        return memberMapper.getMemberByRoleAndUserId(projectId, userId, role);
    }

    @Override
    public void saveOperationsManager(long projectId, String userId, String createdBy, @Nullable Long version) {
        var existedOM =
            memberMapper.getActiveMemberByRole(
                projectId, RoleEnum.OPERATIONS_MANAGER.getRoleId());

        var user = userProvider.findUserById(userId);
        if(Objects.isNull(user)) {
            log.info("Save operations manager member error due to operations manager user {} not exists", userId);
            return;
        }

        if (!user.getAllAuthority().contains(ROLE_OPERATIONS_MANAGER)) {
            log.info("Save operations manager member error due to user {} lacking OM auth.", user);
            return;
        }
        var webUser = UserAssemble.toWebUser(user);
        if (existedOM != null && existedOM.getUserId() == webUser.getUserId()) {
            return;
        }

        if (existedOM != null) {
            memberMapper.delete(projectId, existedOM.getUserId(), RoleEnum.OPERATIONS_MANAGER.getRoleId());
        }

        Member member = new Member();
        member.setCreatedBy(Long.parseLong(createdBy));
        member.setCreatedTime(version == null ? Instant.now().toEpochMilli() : version);
        member.setUserId(webUser.getUserId());
        member.setProjectId(projectId);
        member.setDescription("Operations Manager");
        member.setRole(RoleEnum.OPERATIONS_MANAGER.getCode());

        existedOM = memberMapper.getMemberByRoleAndUserId(projectId, member.getUserId(), member.getRole());
        if (existedOM == null) {
            memberMapper.insert(member);
        } else {
            memberMapper.activeMember(projectId, member.getUserId(), member.getRole());
        }

        publishOperationsManagerAssignedEvent(String.valueOf(projectId), userId, createdBy, version);
        publishOperationManagerMemberChangedEvent(String.valueOf(projectId), userId, createdBy);
        log.info("Successfully set project {} operations manager with user {}", projectId, userId);
    }

    private void publishOperationsManagerAssignedEvent(String projectId, String userId, String createdBy, Long version) {
        var event = new OperationsManagerAssignedEvent(projectId, userId, createdBy, version);
        eventPublisher.publish(event);
        log.info("Publish event({}) from operations manager member set.", event);
    }

    private void publishOperationManagerMemberChangedEvent(String projectId, String userId, String createdBy) {
        var event = new ProjectMemberChangedEvent();
        event.setProjectId(projectId);
        event.setUserTo(userId);
        event.setAuth(Constants.AUTH_OPERATIONS_MANAGER);
        event.setOperator(createdBy);
        eventPublisher.publish(event);
        log.info("Publish event({}) from operations manager member set.", event);
    }
}
