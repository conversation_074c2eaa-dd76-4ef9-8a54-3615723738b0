package com.bees360.service.openapi.converter;

import com.bees360.entity.Project;
import com.bees360.entity.dto.ProjectDto;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ProjectTypeEnum;
import com.bees360.entity.openapi.OpenProjectAddressVo;
import com.bees360.entity.openapi.OpenProjectCreateVo;
import com.bees360.entity.openapi.OpenProjectReportSummaryVo;
import com.bees360.entity.openapi.OpenProjectVo;
import com.bees360.entity.openapi.OpenProjectWithStatusVo;
import com.bees360.entity.vo.ProjectStatusVo;
import com.bees360.entity.vo.ProjectTinyVo;
import com.bees360.policy.Policy;
import java.util.Date;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @date 2020/01/19 18:13
 */
public class ProjectConverter {

    /**
     *
     * @param insuredBy the company id of {@link Project#getInsuranceCompany()}
     */
    public static ProjectDto toProjectDto(OpenProjectCreateVo openProjectVo, Long insuredBy, Long processedBy) {
        ProjectDto projectDto = new ProjectDto();
        projectDto.setCountry(openProjectVo.getCountry());
        projectDto.setState(openProjectVo.getState());
        projectDto.setCity(openProjectVo.getCity());
        projectDto.setZipCode(openProjectVo.getZipcode());
        projectDto.setAddress(openProjectVo.getStreetAddress());
        ProjectTypeEnum projectType = ProjectTypeEnum.getEnumByValue(openProjectVo.getHouseType());
        projectDto.setProjectType(projectType == null? null: projectType.getCode());
        Date dueDate = openProjectVo.getDueDate();
        projectDto.setDueDate(dueDate == null? null: dueDate.getTime());
        projectDto.setPolicyType(openProjectVo.getPolicyType());
        projectDto.setPolicyNumber(openProjectVo.getPolicyNumber());
        projectDto.setInspectionNumber(openProjectVo.getInspectionNumber());
        projectDto.setAssetOwnerName(openProjectVo.getInsuredName());
        projectDto.setAssetOwnerPhone(openProjectVo.getInsuredPhone());
        projectDto.setAgentContactName(openProjectVo.getAgentName());
        projectDto.setAgentPhone(openProjectVo.getAgentPhone());
        ProjectServiceTypeEnum serviceTypeEnum = ProjectServiceTypeEnum.getEnumByValue(openProjectVo.getServiceName());
        projectDto.setServiceType(serviceTypeEnum == null? null: serviceTypeEnum.getCode());
        projectDto.setAgentEmail(openProjectVo.getAgentEmail());
        projectDto.setAssetOwnerEmail(openProjectVo.getInsuredEmail());
        projectDto.setPolicyEffectiveDate(openProjectVo.getPolicyEffectiveDate());
        projectDto.setYearBuilt(openProjectVo.getYearBuilt() == null ? "" : String.valueOf(openProjectVo.getYearBuilt()));
        projectDto.setInsuranceCompany(insuredBy);
        projectDto.setRepairCompany(processedBy);
        projectDto.setClaimNote(openProjectVo.getNote());
        projectDto.setAllowDuplication(openProjectVo.getAllowDuplication());
        projectDto.setIsRenewal(openProjectVo.getIsRenewal());
        projectDto.setSupplementalServices(openProjectVo.getSupplementalServices());
        projectDto.setProjectDivision(openProjectVo.getDivision());
        projectDto.setOperatingCompany(openProjectVo.getOperatingCompany());
        projectDto.setDwellingCoverage(openProjectVo.getDwellingCoverage());
        projectDto.setOtherStructureCoverage(openProjectVo.getOtherStructureCoverage());
        projectDto.setContentCoverage(openProjectVo.getContentCoverage());
        projectDto.setCarrierProvidedLivingArea(openProjectVo.getCarrierProvidedLivingArea());
        return projectDto;
    }

    /**
     *
     * @param insuredBy The company name {@link Project#insuranceCompany}
     */
    public static OpenProjectVo toOpenProjectVo(Project project, String insuredBy, Policy policy) {
        OpenProjectVo openProjectVo = new OpenProjectVo();
        openProjectVo.setId(project.getProjectId());
        openProjectVo.setCountry(project.getCountry());
        openProjectVo.setState(project.getState());
        openProjectVo.setCity(project.getCity());
        openProjectVo.setZipcode(project.getZipCode());
        openProjectVo.setLat(project.getLat());
        openProjectVo.setLng(project.getLng());
        openProjectVo.setStreetAddress(project.getAddress());
        ProjectTypeEnum houseType = ProjectTypeEnum.getEnum(project.getProjectType());
        openProjectVo.setHouseType(houseType == null? "": houseType.getValue());
        Date dueDate = project.getDueDate() == null? null: new Date(project.getDueDate());
        openProjectVo.setDueDate(dueDate);
        openProjectVo.setPolicyType(policy.getType());
        openProjectVo.setPolicyNumber(project.getPolicyNumber());
        openProjectVo.setInspectionNumber(project.getInspectionNumber());
        openProjectVo.setInsuredName(project.getAssetOwnerName());
        openProjectVo.setInsuredPhone(project.getAssetOwnerPhone());
        openProjectVo.setInsuredEmail(project.getAssetOwnerEmail());
        openProjectVo.setAgentName(project.getAgentContactName());
        openProjectVo.setAgentPhone(project.getAgentPhone());
        openProjectVo.setAgentEmail(project.getAgentEmail());
        ProjectServiceTypeEnum serviceTypeEnum = ProjectServiceTypeEnum.getEnum(project.getServiceType());
        openProjectVo.setServiceName(serviceTypeEnum == null? "": serviceTypeEnum.getValue());
        openProjectVo.setYearBuilt(StringUtils.isBlank(project.getYearBuilt()) ? null : Integer.valueOf(project.getYearBuilt()));
        openProjectVo.setPolicyEffectiveDate(project.getPolicyEffectiveDate());
        Optional.ofNullable(project.getInspectionCode()).ifPresent(openProjectVo::setInspectionCode);
        Optional.ofNullable(project.getInspectionLink()).ifPresent(openProjectVo::setInspectionLink);
        openProjectVo.setInsuredBy(insuredBy);
        openProjectVo.setNote(project.getClaimNote());
        openProjectVo.setIsRenewal(project.getIsRenewal());
        openProjectVo.setSupplementalServices(project.getSupplementalServices());
        openProjectVo.setDivision(project.getProjectDivision());
        openProjectVo.setDivisionName(project.getProjectDivisionName());
        openProjectVo.setOperatingCompany(project.getOperatingCompany());
        openProjectVo.setDwellingCoverage(project.getDwellingCoverage());
        openProjectVo.setOtherStructureCoverage(project.getOtherStructureCoverage());
        openProjectVo.setContentCoverage(project.getContentCoverage());
        openProjectVo.setCarrierProvidedLivingArea(project.getCarrierProvidedLivingArea());
        return openProjectVo;
    }

    public static OpenProjectWithStatusVo toOpenProjectVo(ProjectTinyVo projectTinyVo){

        OpenProjectWithStatusVo withStatusVo = new OpenProjectWithStatusVo();
        withStatusVo.setId(projectTinyVo.getProjectId());
        withStatusVo.setStreetAddress(projectTinyVo.getAddress());
        withStatusVo.setCity(projectTinyVo.getCity());
        withStatusVo.setState(projectTinyVo.getState());
        withStatusVo.setZipcode(projectTinyVo.getZipCode());
        withStatusVo.setCountry(projectTinyVo.getCountry());
        final ProjectServiceTypeEnum serviceTYpeEnum = ProjectServiceTypeEnum.getEnum(projectTinyVo.getServiceType());
        withStatusVo.setServiceName(serviceTYpeEnum == null ? "" : serviceTYpeEnum.getValue());
        NewProjectStatusEnum projectStatus = NewProjectStatusEnum.getEnum(projectTinyVo.getProjectStatus());
        Assert.state(projectStatus != null, "status of project " + projectTinyVo.getProjectId() + " may not be null.");
        withStatusVo.setStatus(projectStatus.getValue());
        withStatusVo.setPolicyNumber(projectTinyVo.getPolicyNumber());
        withStatusVo.setInspectionNumber(projectTinyVo.getInspectionNumber());
        ProjectStatusVo statusReturnToClient = projectTinyVo.getStatusReturnToClient();
        Optional.ofNullable(statusReturnToClient).map(ProjectStatusVo::getCreatedTime)
            .ifPresent(time -> withStatusVo.setCompletionTime((int) (time / 1000)));

        return withStatusVo;
    }

    public static OpenProjectReportSummaryVo toOpenProjectReportSummaryVo(Project project, Long inspectionTime, Long completionTime) {
        OpenProjectReportSummaryVo summaryProject = new OpenProjectReportSummaryVo();
        summaryProject.setId(project.getProjectId());
        summaryProject.setInspectionNumber(project.getInspectionNumber());
        ProjectServiceTypeEnum serviceType = ProjectServiceTypeEnum.getEnum(project.getServiceType());
        summaryProject.setServiceName(serviceType == null? "": serviceType.getValue());
        summaryProject.setInsuredName(nullToEmpty(project.getAssetOwnerName()));
        Integer inspectionTimeSec = (inspectionTime == null? null: (int)(inspectionTime / 1000));
        summaryProject.setInspectionTime(inspectionTimeSec);
        Integer completionTimeSec = completionTime == null? null: (int)(completionTime / 1000);
        summaryProject.setCompletionTime(completionTimeSec);

        var address = getOpenAddressVo(project);
        summaryProject.setAddress(address);
        summaryProject.setPolicyNumber(project.getPolicyNumber());
        summaryProject.setSupplementalService(project.getSupplementalServices());
        summaryProject.setIsRenewal(project.getIsRenewal());
        return summaryProject;
    }

    private static OpenProjectAddressVo getOpenAddressVo(Project project) {
        var address = new OpenProjectAddressVo();
        address.setCountry(project.getCountry());
        address.setStreetAddress(project.getAddress());
        address.setCity(project.getCity());
        address.setState(project.getState());
        address.setZipcode(project.getZipCode());
        return address;
    }

    private static String nullToEmpty(String value) {
        return nonNullOrElse(value, "");
    }

    private static <T> T nonNullOrElse(T value, T elseValue) {
        return Optional.ofNullable(value).orElse(elseValue);
    }
}
