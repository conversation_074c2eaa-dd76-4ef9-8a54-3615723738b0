package com.bees360.service.openapi.factory;

import com.bees360.entity.enums.openapi.GrantType;
import com.bees360.entity.openapi.client.OpenApiClientSecret;

import java.util.Set;

public final class OpenApiClientSecretFactory {

    private final ClientIdGenerator clientIdGenerator;
    private final ClientSecretGenerator clientSecretGenerator;

    private static final Long ACCESS_TOKEN_VALIDATE_SECONDS = 600L;
    private static final Long REFRESH_TOKEN_VALIDATE_SECONDS = 86400L;

    public OpenApiClientSecretFactory(ClientIdGenerator idGenerator, ClientSecretGenerator secretGenerator){
        this.clientIdGenerator = idGenerator;
        this.clientSecretGenerator = secretGenerator;
    }

    public OpenApiClientSecret getClientSecret(String name, Set<String> scopes, Set<GrantType> grantTypes,
                                               Long accessTokenValidateSeconds, Long refreshTokenValidateSeconds){

        return OpenApiClientSecret.builder()
            .clientName(name)
            .clientId(clientIdGenerator.getClientId())
            .clientSecret(clientSecretGenerator.getSecret())
            .scopes(scopes)
            .grantTypes(grantTypes)
            .accessTokenValidateSeconds(accessTokenValidateSeconds)
            .refreshTokenValidateSeconds(refreshTokenValidateSeconds)
            .build();
    }

    public OpenApiClientSecret getClientSecret(String name, Set<String> scopes, Set<GrantType> grantTypes){
        return getClientSecret(name, scopes, grantTypes, ACCESS_TOKEN_VALIDATE_SECONDS, REFRESH_TOKEN_VALIDATE_SECONDS);
    }
}
