package com.bees360.event;

import com.bees360.event.registry.PilotMissionRework;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.SetProjectStatusReworkJob;
import com.bees360.job.util.EventTriggeredJob;

import java.time.Duration;

/**
 * 将PilotMissionRework事件转换为可重试的SetProjectStatusReworkJob任务
 */
public class PilotMissionRework2Job extends EventTriggeredJob<PilotMissionRework> {
    public PilotMissionRework2Job(JobScheduler jobScheduler) {
        super(jobScheduler);
    }

    private static final Integer RETRY_COUNT = 5;
    private static final Duration RETRY_DELAY = Duration.ofSeconds(1);
    private static final Float RETRY_DELAY_INCREASE_FACTOR = 2F;

    @Override
    protected Job convert(PilotMissionRework pilotMissionRework) {
        var job = new SetProjectStatusReworkJob();
        job.setProjectId(pilotMissionRework.getProjectId());
        job.setReworkTitle(pilotMissionRework.getReworkTitle());
        job.setReworkContent(pilotMissionRework.getReworkContent());
        job.setOperationUserId(pilotMissionRework.getOperationUserId());
        job.setOperationTime(pilotMissionRework.getOperationTime());
        return RetryableJob.of(
                Job.ofPayload(job), RETRY_COUNT, RETRY_DELAY, RETRY_DELAY_INCREASE_FACTOR);
    }
}
