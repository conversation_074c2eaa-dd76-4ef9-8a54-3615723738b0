package com.bees360.service.listener.report;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.event.registry.ReportGroupAdded;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.project.report.DefaultProjectReportManager;
import com.bees360.report.ReportProvider;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectService;

import com.google.common.collect.Iterables;
import lombok.extern.log4j.Log4j2;

import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * 监听报告组添加事件并发送邮件通知项目创建者
 */
@Log4j2
@Component
public class SendMailOnLOCReportGroupAdded extends AbstractNamedEventListener<ReportGroupAdded> {

    private final ProjectReportFileService projectReportFileService;

    private final Optional<Function<Long, Iterable<UserTinyVo>>> projectMemberMailRecipientProvider;

    private final ProjectService projectService;

    private final ReportProvider reportProvider;

    public SendMailOnLOCReportGroupAdded(
            ProjectReportFileService projectReportFileService,
            Optional<Function<Long, Iterable<UserTinyVo>>> projectMemberMailRecipientProvider,
            ProjectService projectService,
            ReportProvider reportProvider) {
        this.projectReportFileService = projectReportFileService;
        this.projectMemberMailRecipientProvider = projectMemberMailRecipientProvider;
        this.projectService = projectService;
        this.reportProvider = reportProvider;
        log.info(
                "Created {}(projectReportFileService={}, projectMemberMailRecipientProvider={}, projectService={}, reportProvider={}).",
                this,
                projectReportFileService,
                projectMemberMailRecipientProvider,
                projectService,
                reportProvider);
    }

    @Override
    public void handle(ReportGroupAdded event) throws IOException {
        log.info("Listening event {} for send mail.", event);
        if (!DefaultProjectReportManager.PROJECT_REPORT_GROUP_TYPE.equalsIgnoreCase(
                event.getGroupType())) {
            return;
        }
        var report = reportProvider.get(event.getReportId());
        var reportType = ReportTypeEnum.getReportType(report);

        if (!ReportTypeEnum.LETTER_OF_CANCELLATION_REPORT.equals(reportType)) {
            return;
        }
        var projectId = Long.parseLong(event.getGroupKey());
        var reportId = report.getId();
        var project = projectService.getById(projectId);
        Long recipientUserId = project.getCreatedBy();
        if (projectMemberMailRecipientProvider.isPresent()) {
            var creators =
                    Iterables.filter(
                            projectMemberMailRecipientProvider.get().apply(project.getProjectId()),
                            u -> Objects.equals(RoleEnum.CREATOR.getRoleId(), u.getRoleId()));
            var creator = Iterables.getFirst(creators, null);
            if (creator == null) {
                log.warn(
                        "Ignore to send email to creator when report {}({}) of project {} generated.",
                        reportType,
                        reportId,
                        projectId);
                return;
            } else {
                recipientUserId = creator.getUserId();
            }
        }
        try {
            projectReportFileService.infoUserReportApprove(
                    projectId, recipientUserId, reportType, false);
        } catch (ServiceException e) {
            log.error(
                    "Fail to send email when report {}({}) of project {} generated.",
                    reportType,
                    reportId,
                    projectId,
                    e);
        }
    }
}
