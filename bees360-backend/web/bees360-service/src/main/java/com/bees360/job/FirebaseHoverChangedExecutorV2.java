package com.bees360.job;

import com.bees360.job.registry.SerializableFirebaseHover;
import com.bees360.job.registry.SerializableFirebaseHoverV2;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseHoverService;
import com.google.cloud.firestore.Firestore;

import lombok.ToString;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.beanutils.BeanUtils;

import java.io.IOException;

import static com.bees360.service.util.FirebaseExceptionTranslation.translateExceptionAndThrow;

@Log4j2
@ToString
public class FirebaseHoverChangedExecutorV2 extends AbstractJobExecutor<SerializableFirebaseHoverV2> {

    private final FirebaseHoverService firebaseHoverService;
    private final Firestore firestore;

    public FirebaseHoverChangedExecutorV2(FirebaseHoverService firebaseHoverService, Firestore firestore) {
        this.firebaseHoverService = firebaseHoverService;
        this.firestore = firestore;

        log.info("Created '{}'", this);
    }

    @Override
    protected void handle(SerializableFirebaseHoverV2 hoverV2) throws IOException {
        SerializableFirebaseHover hover;
        try{
            hover = convert(hoverV2);
        } catch (IllegalArgumentException e){
            log.error("Failed to handle hover '{}'.", hoverV2, e);
            return;
        }

        try {
            log.info("Start to handle hover '{}' '{}'", hover.getId(), hover);
            firebaseHoverService.handleHoverJob(hover, hover.getId());
            log.info("Successfully handle hover '{}'", hover.getId());
        } catch (RuntimeException e) {
            log.warn("Failed to handle hover '{}'.", hover, e);
            translateExceptionAndThrow(e);
        }
    }

    private SerializableFirebaseHover convert(SerializableFirebaseHoverV2 hoverV2) throws IllegalArgumentException {
        SerializableFirebaseHover hover = new SerializableFirebaseHover();
        try {
            BeanUtils.copyProperties(hover, hoverV2);
        } catch (Exception e) {
            throw new IllegalArgumentException(
                "Failed to convert SerializableFirebaseHoverV2 to SerializableFirebaseHover.", e);
        }
        return hover;
    }
}
