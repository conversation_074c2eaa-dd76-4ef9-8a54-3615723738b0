package com.bees360.service.impl;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Company;
import com.bees360.entity.Member;
import com.bees360.entity.User;
import com.bees360.entity.dto.IdNameDto;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.mapper.MemberMapper;
import com.bees360.project.member.MemberManager;
import com.bees360.service.CompanyService;
import com.bees360.service.UserService;
import com.bees360.user.BifrostUserMapperAdapter;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import com.bees360.util.user.Bees360UserUtils;
import com.bees360.util.user.UserAssemble;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Log4j2
@Service("userService")
public class UserServiceImpl implements UserService {

    @Inject
    private MemberMapper memberMapper;

    @Inject
    private CompanyService companyService;

    @Autowired
    private BifrostUserMapperAdapter bifrostUserMapperAdapter;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private MemberManager memberManager;

	/**
	 * @throws ServiceException
	 * 		   	<p>code = DATABASE_EXCEPTION if there something wrong with mysql or system.</p>
	 * 		   	<p>code = USER_NOT_EXISTED if the user is not exist in database.</p>
	 */
    @Override
    public User getUserById(long id) {
        var user = bifrostUserMapperAdapter.getUserById(id);
        if (user == null) {
            return null;
        }
        if (user.getCompanyId() != null) {
            Company company = companyService.getById(user.getCompanyId());
            if(company != null) {
                user.setCompanyName(company.getCompanyName());
                user.setCompanyLogo(company.getLogo());
            }
        }
        if (CollectionUtils.isEmpty(user.getRoleList())) {
            getUserRoles(id).forEach(user::addRole);
        }
        return user;
    }

    private Iterable<RoleEnum> getUserRoles(long userId) {
        var user = userProvider.getUser(userId + "");
        return user.getAllAuthority().stream()
            .map(UserAssemble::authorityToRole)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    @Override
    public Long toWebUserId(String userIdString) {
        return StringUtils.isEmpty(userIdString)? null: Long.parseLong(userIdString);
    }

    @Override
    public List<UserTinyVo> listActiveMemberInProject(long projectId) {
        var idString = projectId + "";
        var members = memberManager.findByProjectIds(List.of(idString)).getOrDefault(idString, List.of());
        return Iterables.toStream(members).map( m ->
            new UserTinyVo(UserAssemble.toWebUser(m.getUser()), UserAssemble.authorityToRole(m.getRole()))).collect(Collectors.toList());
    }

    @Override
    public List<UserTinyVo> listMemberInProject(long projectId, RoleEnum role) {
		List<UserTinyVo> users = null;
        List<Member> members;
        if (role == null) {
            members = memberMapper.listActiveMember(projectId);
        } else {
            members = memberMapper.listActiveMembersByRole(projectId, role.getRoleId());
        }
        Map<String, List<Integer>> memberRolesMap = Bees360UserUtils.memberRolesMap(members);
        List<? extends com.bees360.user.User> fetchedUsers =
                Iterables.toList(userProvider.findUserById(memberRolesMap.keySet()));
        users = Bees360UserUtils.toUserTinyVo(fetchedUsers, memberRolesMap);

        sortMembers(users);

        return users;
	}

    private void sortMembers(List<UserTinyVo> users) {
        Collections.sort(users, new Comparator<UserTinyVo>() {
            @Override
            public int compare(UserTinyVo user1, UserTinyVo user2) {
                if(StringUtils.equals(user1.getRole(), user2.getRole())) {
                    return 0;
                }
                if(user1.getRoleId() == RoleEnum.VISITOR.getCode()) {
                    return 1;
                }
                if(user2.getRoleId() == RoleEnum.VISITOR.getCode()) {
                    return -1;
                }
                return 0;
            }

        });
    }

    @Override
    public List<UserTinyVo> listMemberInProject(long userId, long projectId) throws ServiceException {
        var user = getUserById(userId);
		List<UserTinyVo> members = listMemberInProject(projectId,null);

		List<RoleEnum> roles = user.listRoles();
		Set<RoleEnum> visiableRoles = new HashSet<RoleEnum>();
		for(RoleEnum r: roles) {
			// collect all roles the user can view
			visiableRoles.addAll(r.roleVisible());
		}
		List<UserTinyVo> visiableMembers = new ArrayList<UserTinyVo>();
		for(UserTinyVo m: members) {
			if(visiableRoles.contains(RoleEnum.getEnum(m.getRoleId())) || m.getUserId() == user.getUserId()) {
				// filter the member the user can view
				visiableMembers.add(m);
			}
		}
		return visiableMembers;
	}

    @Override
    public List<IdNameDto> listRolesUserPlay(long projectId, long userId) throws ServiceException {
        List<IdNameDto> roles = new ArrayList<>();
        List<Member> members;
        try {
            members = memberMapper.listActiveMembersByUserId(projectId, userId);
        } catch (Exception e) {
            log.error("Fail to get user {}.", userId, e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
        }
        for(Member m: members) {
            RoleEnum role = RoleEnum.getEnum(m.getRole());
            roles.add(new IdNameDto(role.getCode(), role.getDisplay()));
        }
        return roles;
    }
}
