package com.bees360.service.impl;

import com.bees360.api.InternalException;
import com.bees360.base.MessageCode;
import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.SystemException;
import com.bees360.entity.EventHistory;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.User;
import com.bees360.entity.UserPayment;
import com.bees360.entity.dto.DamageReportParamDto;
import com.bees360.entity.dto.ReportMessageDto;
import com.bees360.entity.dto.ReportPrefixDto;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.ServiceFeeTypeEnum;
import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.bees360.entity.openapi.reportsummary.SummaryRisk;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.payment.UserPaymentMapper;
import com.bees360.service.EventHistoryService;
import com.bees360.service.MessageService;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectReportService;
import com.bees360.service.ResourceFileService;
import com.bees360.web.event.project.ProjectReportFileUpdatedEvent;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import jakarta.inject.Inject;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Service;

@Slf4j
@Service("projectReportService")
public class ProjectReportServiceImpl implements ProjectReportService, ApplicationEventPublisherAware {

    @Inject private ProjectMapper projectMapper;

    @Autowired private ProjectReportFileService projectReportFileService;

    @Inject private UserPaymentMapper userPaymentMapper;

    @Inject private EventHistoryService eventHistoryService;

    @Inject private MessageService messageService;

    @Inject private ResourceFileService resourceFileService;

    @Value("${report.resource.prefix}")
    private String reportFileKeyPrefix;

    @Autowired private ObjectMapper objectMapper;

    private final String CLOSEOUT_OVERALL_CONDITION = "Closeout";

    private ApplicationEventPublisher publisher;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.publisher = applicationEventPublisher;
    }

    private ProjectReportFile updateReportMessage(DamageReportParamDto reportParam, ReportMessageDto reportMessage)
        throws Exception {
        if (Objects.isNull(reportMessage) || reportMessage.getSize() <= 0) {
            return null;
        }
        ReportTypeEnum reportType = reportParam.getReportType();

        String reportKey = reportMessage.getPdf();

        long projectId = reportParam.getProject().getProjectId();
        long userId = reportParam.getUserId();

        ReportGenerationStatusEnum reportStatus = isAutoApproved(reportType) ?
            ReportGenerationStatusEnum.APPROVED : ReportGenerationStatusEnum.GENERATED;

        ProjectReportFile projectReportFile = new ProjectReportFile();
        projectReportFile.setProjectId(projectId);
        projectReportFile.setCreatedBy(userId);
        projectReportFile.setCreatedTime(reportParam.getTime());
        projectReportFile.setRead(false);
        projectReportFile.setDeleted(false);

        projectReportFile.setGenerationStatus(reportStatus);
        projectReportFile.setReportType(reportType.getCode());
        projectReportFile.setReportWordFileName("");
        projectReportFile.setReportPdfFileName(reportKey);
        projectReportFile.setSize(reportMessage.getSize());
        projectReportFile.setReportPdfCompressed(StringUtils.defaultIfEmpty(reportMessage.getPdfCompressed(), ""));
        if (StringUtils.isEmpty(projectReportFile.getReportPdfCompressed())) {
            projectReportFile.setSizeCompressed(0);
        } else {
            projectReportFile.setSizeCompressed(reportMessage.getSizeCompressed());
        }

        updateReportFileWithCloseoutReason(projectReportFile, reportMessage.getCloseoutReason());

        long end = System.currentTimeMillis();

        EventHistory history = createHistoryEntity(projectId, userId, end, reportType.GENERATED, "");
        eventHistoryService.insertHistoryToProject(history);
        if (isAutoApproved(reportType)) {
            EventHistory approvedHistory = createHistoryEntity(projectId, User.AI_ID, System.currentTimeMillis(),
                reportType.APPROVED, "");
            eventHistoryService.insertHistoryToProject(approvedHistory);
        }

        return projectReportFile;
    }

    private void updateReportFileWithCloseoutReason(
            ProjectReportFile projectReportFile, String closeoutReason) {
        projectReportFileService.updateReportFile(
                projectReportFile, generateSummaryJson(closeoutReason));
    }

    private String generateSummaryJson(String closeoutReason){
        ReportSummaryVo reportSummaryVo = new ReportSummaryVo();
        SummaryRisk risk = new SummaryRisk();
        risk.setOverallCondition(CLOSEOUT_OVERALL_CONDITION);
        reportSummaryVo.setRisk(risk);
        reportSummaryVo.setCloseoutReasons(List.of(closeoutReason));
        try {
            return objectMapper.writeValueAsString(reportSummaryVo);
        } catch (JsonProcessingException e) {
            throw new InternalException(e.getMessage(),e);
        }
    }

    @Override
    public ReportPrefixDto getReportFilePrefix() {
        return ReportPrefixDto.builder().prefix(reportFileKeyPrefix).build();
    }

    private boolean isAutoApproved(ReportTypeEnum reportType) {
        return !reportType.needApproved() && reportType.APPROVED != null;
    }

    @Override
    public int updateReportKey(long projectId, String reportId, String reportKey) {
        ProjectReportFile reportFile = projectReportFileService.getById(projectId, reportId);
        if (reportFile == null) {
            throw new ResourceNotFoundException("report " + reportId + " not found.");
        }
        log.info("Try to replace reportKey of report ({}) from {} to {}", reportId, reportFile.getReportPdfFileName(),
            reportKey);
        try {
            long reportSize = resourceFileService.getReportContentLength(reportKey);
            reportFile.setSize((int) reportSize);
            reportFile.setReportPdfFileName(reportKey);
            // 设置压缩的报告为空
            reportFile.setSizeCompressed(0);
            reportFile.setReportPdfCompressed("");
        } catch (IOException ex) {
            throw new SystemException("fail to get size of report " + reportId, ex);
        }
        int effectiveRow = projectReportFileService.update(projectId, reportFile);

        Project project = projectMapper.getById(reportFile.getProjectId());
        publisher.publishEvent(new ProjectReportFileUpdatedEvent(this, project, reportFile));
        return effectiveRow;
    }

    @Override
    public void shareReport(long projectId, String reportId, long senderId,
        List<String> recipients) throws ServiceException {
        messageService.shareReport(projectId, reportId, senderId, recipients);
    }

    private static EventHistory createHistoryEntity(long projectId, long userId, long time,
        ProjectStatusEnum status, String description) {
        if (status == null) {
            return null;
        }
        EventHistory history = new EventHistory();
        history.setProjectId(projectId);
        history.setUserId(userId);
        history.setStatus(status.getCode());
        history.setStatusTime(time);
        history.setModifiedBy(userId);
        history.setCreatedTime(time);
        history.setDescription(description);
        return history;
    }

    @Override
    public List<ProjectReportFile> listPaidReport(long projectId) {
        return listReports(projectId, true);
    }

    @Override
    public List<ProjectReportFile> listUnpaidReports(long projectId) {
        return listReports(projectId, false);
    }

    @Override
    public ProjectReportFile insertReport(long projectId, long userId, ProjectReportFile reportFile)
        throws ServiceException {
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportFile.getReportType());
        if (reportType == null) {
            throw new ServiceException(MessageCode.PARAM_INVALID, "report type is valid.");
        }
        DamageReportParamDto reportParam = new DamageReportParamDto();
        reportParam.setUserId(userId);
        reportParam.setProject(new Project(projectId));
        reportParam.setTime(System.currentTimeMillis());
        reportParam.setReportType(reportType);

        ReportMessageDto reportMessage = ReportMessageDto.builder()
            .pdf(reportFile.getReportPdfFileName())
            .size(reportFile.getSize())
            .build();
        try {
            return updateReportMessage(reportParam, reportMessage);
        } catch (Exception e) {
            log.error("upload report error. projectId:" + projectId, e);
            throw new ServiceException(MessageCode.BAD_REQUEST);
        }
    }

    private List<ProjectReportFile> listReports(long projectId, boolean isPaid) {
        List<ProjectReportFile> generatedReportFiles = projectReportFileService.getAllReportFiles(projectId);
        // use to check if the report has been paid or not
        List<UserPayment> userPayments = userPaymentMapper.listUserPaymentsByProjectId(projectId, null);
        Set<Integer> reportsPaid = parseReportType(userPayments);
        generatedReportFiles.removeIf(r -> isPaid != reportsPaid.contains(r.getReportType()));
        return generatedReportFiles;
    }

    private Set<Integer> parseReportType(List<UserPayment> userPayments) {
        Set<Integer> reportsPaid = new HashSet<>();
        for (UserPayment userPayment : userPayments) {
            ServiceFeeTypeEnum serviceFee = ServiceFeeTypeEnum.getEnum(userPayment.getServiceFeeType());
            for (ReportTypeEnum reportPaid : serviceFee.getReportTypes()) {
                reportsPaid.add(reportPaid.getCode());
            }
        }
        return reportsPaid;
    }
}
