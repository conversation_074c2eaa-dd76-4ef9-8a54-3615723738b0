package com.bees360.service.payment;

import java.util.List;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.UserPayment;

public interface UserPaymentService {

	/**
	 * save user payment info
	 * @param userPayment
	 * @throws ServiceException
	 */
	public void addUserPayment(UserPayment userPayment) throws ServiceException;


	/**
	 * batch insert
	 * @param userPaymentList
	 * @throws ServiceException
	 */
	public void addUserPayments(List<UserPayment> userPaymentList) throws ServiceException;


	/**
	 * get UserPayment list by projectId
	 * @param projectId
	 * @return
	 * @throws ServiceException
	 */
	public List<UserPayment> listUserPaymentsByProjectId(long projectId) throws ServiceException;

	/**
	 *
	 * @param userId
	 * @param projectId
	 * @return
	 * @throws ServiceException
	 */
	public List<UserPayment> getUserPayments(long userId, long projectId) throws ServiceException;
}
