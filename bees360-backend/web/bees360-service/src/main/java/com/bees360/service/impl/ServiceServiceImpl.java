package com.bees360.service.impl;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.ContactUs;
import com.bees360.entity.PartnerProgram;
import com.bees360.mapper.ContactUsMapper;
import com.bees360.mapper.PartnerProgramMapper;
import com.bees360.service.MessageService;
import com.bees360.service.ServiceService;

@Service("ServiceService")
public class ServiceServiceImpl implements ServiceService {
	private final Logger logger = LoggerFactory.getLogger(ServiceServiceImpl.class);

	@Inject
	private MessageService messageService;

	@Inject
	private ContactUsMapper contactUsMapper;

	@Inject
	private PartnerProgramMapper partnerProgramMapper;

	@Override
	public void contactUs(ContactUs concactUs) throws ServiceException {
		concactUs.setId(0);
		contactUsMapper.insert(concactUs);
		messageService.sendContactUS(concactUs);
	}

	@Override
	public void postPartnerProgram(PartnerProgram partnerProgram) throws ServiceException {
		partnerProgramMapper.insert(partnerProgram);
		messageService.infoPartnerProgram(partnerProgram);
		messageService.infoPartnerProgramRecieved(partnerProgram);
	}
}
