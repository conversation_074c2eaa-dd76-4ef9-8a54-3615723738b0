package com.bees360.service.impl;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.AccessDatabaseException;
import com.bees360.base.exception.ServiceException;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.common.collections.ListUtil;
import com.bees360.entity.EventHistory;
import com.bees360.entity.Member;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.ReportSummary;
import com.bees360.entity.User;
import com.bees360.entity.dto.IdTypeDto;
import com.bees360.entity.dto.ProjectReportFileDto;
import com.bees360.entity.dto.ReportTypeDto;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.PipelineTaskEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.ProjectReportFileTinyVo;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.event.EventPublisher;
import com.bees360.mapper.MemberMapper;
import com.bees360.mapper.ProjectImageMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.pipeline.Message.PipelineStatus;
import com.bees360.pipeline.Pipeline;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.report.ProjectReportManager;
import com.bees360.report.Message.ReportMessage.Resource.Type;
import com.bees360.report.Message.ReportMessage.Status;
import com.bees360.report.Report;
import com.bees360.report.ReportManager;
import com.bees360.resource.ResourcePool;
import com.bees360.service.EventHistoryService;
import com.bees360.service.MessageService;
import com.bees360.service.NotificationService;
import com.bees360.service.ProjectImageArchiveService;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectTaskService;
import com.bees360.service.ReportSummaryService;
import com.bees360.service.UserService;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.service.properties.ReportGlobalProperties;
import com.bees360.service.util.PipelineUtils;
import com.bees360.util.DateUtil;
import com.bees360.util.Iterables;
import com.bees360.util.SecureTokens;
import com.bees360.util.report.CommonsUtil;
import com.bees360.web.event.project.ProjectOrderedServiceReportApprovedEvent;
import com.bees360.web.event.project.ProjectReportFileUpdatedEvent;
import jakarta.inject.Inject;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.OptionalInt;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
@Service("projectReportFileService")
public class ProjectReportFileServiceImpl implements ProjectReportFileService, ApplicationEventPublisherAware {

    @Autowired private ReportManager reportManager;

    @Autowired private ProjectReportManager projectReportManager;

    @Autowired private ReportSummaryService reportSummaryService;

    @Inject private EventHistoryService eventHistoryService;

    @Inject private MessageService messageService;

    @Inject private NotificationService notificationService;

    @Inject private MemberMapper memberMapper;

    @Inject private ProjectMapper projectMapper;

    @Inject private ProjectService projectService;

    @Autowired private ReportGlobalProperties reportGlobalProperties;

    @Autowired private Bees360CompanyConfig bees360CompanyConfig;

    @Inject private ProjectImageMapper projectImageMapper;

    @Inject private ProjectImageArchiveService asyncProjectImageArchiveService;

    @Inject private ProjectTaskService projectTaskService;

    private ApplicationEventPublisher publisher;

    @Autowired private ResourcePool resourcePool;

    @Autowired private EventPublisher eventPublisher;

    @Autowired private PipelineService pipelineService;

    @Autowired private Optional<Function<Long, Iterable<UserTinyVo>>> projectMemberMailRecipientProvider;

    @Autowired
    private UserService userService;

	@Override
	public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
		this.publisher = applicationEventPublisher;
	}

    @Override
    public ProjectReportFileTinyVo getReportFile(long projectId, Integer reportType) {
        List<ProjectReportFileTinyVo> reports = getReportFiles(projectId, reportType);
        if (CollectionUtils.isEmpty(reports)) {
            return null;
        }
        return reports.get(0);
    }

	@Override
	public List<ProjectReportFileTinyVo> getReportFiles(long projectId, long userId, Integer reportType) throws ServiceException {

        List<ProjectReportFileTinyVo> reports = getReportFiles(projectId, reportType);
        if (CollectionAssistant.isEmpty(reports)) {
            return new ArrayList<>();
        }
        User user = userService.getUserById(userId);
        if (!canUserViewAllReports(user, projectId)) {
            filterApprovedReports(reports);
        }
        reports.forEach(report -> report.setS3Key(
            StringUtils.isEmpty(report.getReportPdfCompressed()) ? report.getReportUrl()
                : report.getReportPdfCompressed()));

		reports = filterReport(reports);
		attachPaidInfo(reports);
		attachReadableInfo(user, projectId, reports);
		return reports;
	}

    /**
	 * 过滤web端不进行展示的报告
	 */
	private List<ProjectReportFileTinyVo> filterReport(List<ProjectReportFileTinyVo> reports) {
		return reports.stream()
			.filter(e -> ReportTypeEnum.getEnum(e.getReportType()).isShow())
			.collect(Collectors.toList());
	}

	private boolean canUserViewAllReports(User user, long projectId) {

		if(user.hasAnyRole(RoleEnum.ADMIN, RoleEnum.SALESMAN)) {
			// admin and salesman can view all reports
			return true;
		}
		List<Member> roles = memberMapper.listActiveMembersByUserId(projectId, user.getUserId());

		for(Member member: roles) {
			if(RoleEnum.canViewAllReport(member.getRole())) {
				// reviewer and processor of the project can view all reports
				return true;
			}
		}
		return false;
	}

	private void filterApprovedReports(List<ProjectReportFileTinyVo> reports) {
		Iterator<ProjectReportFileTinyVo> iter = reports.iterator();
		while(iter.hasNext()) {
			ProjectReportFileTinyVo r = iter.next();
			ReportTypeEnum reportType = ReportTypeEnum.getEnum(r.getReportType());
			if(reportType.needApproved() && r.getGenerationStatus() != ReportGenerationStatusEnum.APPROVED.getCode()) {
				// other user can only view the approved reports
				iter.remove();
			}
		}
	}

	private void attachPaidInfo(List<ProjectReportFileTinyVo> reports) {
        reports.forEach(report -> report.setPaid(true));
	}

	private void attachReadableInfo(User user, long projectId, List<ProjectReportFileTinyVo> reports) {
		if(canUserViewAllReports(user, projectId)) {
			for(ProjectReportFileTinyVo report: reports) {
				report.SetReadable(true);
			}
			return;
		}
		for(ProjectReportFileTinyVo report: reports) {
			boolean readable = report.isFree() || report.isPaid();
			report.SetReadable(readable);
		}
	}

	@Override
	public void submitReport(long projectId, long userId, String reportId) throws ServiceException {
		ProjectReportFile report = getById(projectId, reportId);
		if(report == null) {
			throw new ServiceException(MessageCode.REPORT_NOT_EXIST);
		}
		ReportTypeEnum reportType = ReportTypeEnum.getEnum(report.getReportType());
		ReportGenerationStatusEnum generationStatus = ReportGenerationStatusEnum.getEnum(report.getGenerationStatus());
		switch(generationStatus) {
		case GENERATED:
		case DISAPPROVED: {
			try {
				updateReportGenerationStatus(projectId, userId, reportId,
						ReportGenerationStatusEnum.SUBMITTED, reportType.SUBMITTED);
			} catch (AccessDatabaseException e) {
				throw new ServiceException(MessageCode.DATABASE_EXCEPTION, e);
			}
			notifyReviewerReportSubmitted(projectId, reportType);
		}
		case SUBMITTED: {
			return;
		}
		case APPROVED: {
			throw new ServiceException(MessageCode.REPORT_BEEN_APPROVED);
		}
		default: {
        }
		}
	}

	private void notifyReviewerReportSubmitted(long projectId, ReportTypeEnum reportType) throws ServiceException {
		var members = userService.listActiveMemberInProject(projectId);
		for(UserTinyVo member: members) {
			if(member.getRoleId() == RoleEnum.REVIEWER.getCode()) {
				messageService.infoReportSubmitted(projectId, reportType, member);
				notificationService.notifyReportSubmitted(projectId, reportType, member);
			}
		}
	}

	@Transactional(rollbackFor = ServiceException.class)
	@Override
    @Deprecated
	public void approveReport(long projectId, long userId, String reportId) throws ServiceException {
		ProjectReportFile report = getById(projectId, reportId);
		if(report == null) {
			throw new ServiceException(MessageCode.REPORT_NOT_EXIST);
		}
		ReportGenerationStatusEnum reportStatus = ReportGenerationStatusEnum.getEnum(report.getGenerationStatus());

		switch(reportStatus) {
		case GENERATED: {
			throw new ServiceException(MessageCode.REPORT_NOT_SUBMITTED);
		}
		case SUBMITTED: {
            // 获取最新的 ProjectReportFile
            doApproveReport(projectId, userId, reportId);
            return;
		}
		case DISAPPROVED: {
			throw new ServiceException(MessageCode.REPORT_BEEN_DISAPPROVED);
		}
		case APPROVED: {
			return;
		}
		default: {
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
		}
	}

    private boolean isReportCompressed(ProjectReportFile reportFile) throws ServiceException {
        if (!isReportNeedCompress(reportFile)) {
            return true;
        }
        return reportFile.getSizeCompressed() > 0;
    }

    private boolean isReportNeedCompress(ProjectReportFile reportFile) throws ServiceException {
        OptionalInt minReportCompressedSize = getReportSizeLimit(reportFile);
        return minReportCompressedSize.isPresent() && reportFile.getSize() > minReportCompressedSize.getAsInt();
    }

    private void updateCompressionReportTaskStatus(ProjectReportFile report, boolean successful) {
        var reportTypeCode = report.getReportType();
        var projectId = report.getProjectId();
        var reportType = ReportTypeEnum.getEnum(reportTypeCode);
        String taskKey = PipelineTaskEnum.getKeyCompressionReportType(reportType);
        log.info("Try to update the status of the pipeline task {} of project {}.", taskKey, projectId);
        Pipeline pipeline = pipelineService.findById(projectId + "");
        if (pipeline == null) {
            log.info("Abort to update pipeline task {}, since the project {} doesn't have a pipeline.",
                taskKey, projectId);
            return;
        }
        if (PipelineUtils.findTask(pipeline, taskKey) == null) {
            log.info("The pipeline of project {} doesn't have task {}.", projectId, taskKey);
            return;
        }
        var status = successful ? PipelineStatus.DONE: PipelineStatus.ERROR;
        log.info("Try to update the status of task {} in project {} to {}", taskKey, projectId, status);
        try {
            pipelineService.setTaskStatus(projectId + "", taskKey, status);
        } catch (Exception e) {
            // maybe the task is not ready.
            log.warn("Fail to update the status of the task {} in project {} to {}.", taskKey, projectId, status, e);
        }
    }

    @Override
    public OptionalInt getReportSizeLimit(ProjectReportFile reportFile) throws ServiceException {
        Set<Long> managerCompanies = projectService.getProjectManageCompany(reportFile.getProjectId());
        Project project = projectMapper.getById(reportFile.getProjectId());
        // 获取报告配置中最小的压缩值
        OptionalInt minReportCompressedSize = bees360CompanyConfig.getCompanies().stream()
            .filter(c -> managerCompanies.contains(c.getId()) && hasReportCompressedSizeConfig(c))
            .mapToInt(c -> c.getReport().getCompressedSize()).min();

        // 获取报告类型全局配置压缩值
        OptionalInt globalCompressedSizeLimit = reportGlobalProperties.getReportCompressedSizeLimit(
            reportFile.getReportType(), project.getServiceType());
        if (globalCompressedSizeLimit.isPresent()) {
            // 取得min(企业配置压缩大小,全局配置压缩大小)
            minReportCompressedSize = OptionalInt.of(Math.min(minReportCompressedSize.orElse(globalCompressedSizeLimit.getAsInt()),
                globalCompressedSizeLimit.getAsInt()));
        }
        return minReportCompressedSize;
    }

    private boolean hasReportCompressedSizeConfig(Bees360CompanyConfig.CompanyConfigItem configItem) {
        return configItem.getReport() != null && configItem.getReport().getCompressedSize() != null;
    }

    private void archiveImages(long userId, long projectId, ProjectReportFile report) throws ServiceException {

        if(!checkOrderedProjectReportFileApproved(userId, projectId, report.getReportType())){
            return;
        }

        try {
            asyncProjectImageArchiveService.archiveImages(projectId);
        } catch (Exception e) {
            log.error("An error occurred while trying to submit archive file task, projectId:{}", projectId, e);
        }
    }

    // This function will not update report status but still handle report approved issue.
    @Override
	public void doApproveReport(long projectId, long userId, String reportId) throws ServiceException {
        ProjectReportFile report = getById(projectId, reportId);
        var isCompressed = isReportCompressed(report);

        updateCompressionReportTaskStatus(report, isCompressed);
        if (!isCompressed) {
            String message = "report " + report.getReportId() + " need to be compressed, but didn't.";
            throw new ServiceException(MessageCode.REPORT_NEED_COMPRESSED, message);
        }

		ReportTypeEnum reportType = ReportTypeEnum.getEnum(report.getReportType());
		if(reportType.needApproved()) {
            dealWithAfterReportApproved(userId, projectId, reportType);
            archiveImages(userId, projectId, report);
        }
    }

	/**
	 * 当一个报告被审核通过之后需要处理的一些事情
	 *
	 * @param userId 操作本次请求的用户
	 */
	private void dealWithAfterReportApproved(long userId, long projectId, ReportTypeEnum reportType)
		throws ServiceException {
		if(checkOrderedProjectReportFileApproved(userId, projectId, reportType.getCode())) {
            Project project = projectMapper.getById(projectId);
            publisher.publishEvent(new ProjectOrderedServiceReportApprovedEvent(this, project, reportType, userId));
		}
		infoMemberReportApprove(projectId, reportType, true);
	}

	private boolean checkOrderedProjectReportFileApproved(long userId, long projectId, Integer reportTypeCode) throws ServiceException {
		return projectTaskService.checkProjectReportFileApproved(userId, projectId, reportTypeCode);
	}

	/**
	 *
	 * @param projectId
	 * @param userId
	 * @param reportId
	 * @param reportGenerationStatus
	 * @param projectStatus 该值不为空时，写入新的EventHistory
     */
	private void updateReportGenerationStatus(long projectId, long userId, String reportId,
			ReportGenerationStatusEnum reportGenerationStatus, ProjectStatusEnum projectStatus)
			throws AccessDatabaseException {
		if(projectStatus != null) {
			long now = DateUtil.getNow();
			EventHistory history = new EventHistory();
			history.setProjectId(projectId);
			history.setUserId(userId);
			history.setStatus(projectStatus.getCode());
			history.setStatusTime(now);
			history.setModifiedBy(userId);
			history.setCreatedTime(now);
			history.setDescription("");
			eventHistoryService.insertHistoryToProject(history);
		}
		try {
			updateReportGenerationStatus(reportId, reportGenerationStatus.getCode(), String.valueOf(userId));
		} catch (Exception e) {
			throw new AccessDatabaseException("Fail to update generation status of report "
					+ reportId + " in project " + projectId, e);
		}
	}

    @Override
    public void infoUserReportApprove(long projectId, long userId, ReportTypeEnum reportType,
        boolean withReportAttachment) throws ServiceException {
        List<UserTinyVo> members = userService.listActiveMemberInProject(projectId);
        members.removeIf(user -> !Objects.equals(user.getUserId(), userId));
        if (CollectionAssistant.isEmpty(members)) {
            throw new ServiceException(MessageCode.USER_NOT_EXISTED, "User not exist.");
        }

        ProjectReportFile report = getByType(projectId, reportType.getCode());
        if (Objects.isNull(report)) {
            throw new ServiceException(MessageCode.REPORT_NOT_EXIST, "Report not exist.");
        }

        messageService.infoReportApproved(projectId, members, reportType, report, withReportAttachment);
    }

    @Override
    public void infoMemberReportApprove(long projectId, ReportTypeEnum reportType, boolean withReportAttachment)
        throws ServiceException {
        log.info("info member report approved {}, {}", projectId, reportType.getCode());
        Project project = projectMapper.getById(projectId);
        if (ClaimTypeEnum.isClaim(project.getClaimType())) {
            return;
        }
        var emailRecipients = getMembersForReportApprovedInfo(projectId);

        ProjectReportFile report = null;
        // 下载文件并添加
        if(withReportAttachment) {
            report = getByType(projectId, reportType.getCode());
        }
        boolean withImagesZipLink = isEmailWithImageZipLink(projectId, withReportAttachment, report);

        log.info("info member report approved {}, {}, {}", projectId, reportType.getCode(), ListUtil.toString(emailRecipients));
        messageService.infoReportApproved(projectId, emailRecipients, reportType, report, withImagesZipLink);
	}

    private List<UserTinyVo> getMembersForReportApprovedInfo(long projectId) {
        if (projectMemberMailRecipientProvider.isPresent()) {
            var recipients = Iterables.toList(projectMemberMailRecipientProvider.get().apply(projectId));
            log.info("Members {} of project {} will receive info of report approval.",
                () -> Iterables.transform(recipients, UserTinyVo::getUserId),
                () -> projectId);
            return recipients;
        }

        List<UserTinyVo> emailRecipients = new ArrayList<>();
        UserTinyVo pilot = null;
        List<UserTinyVo> members = userService.listActiveMemberInProject(projectId);

        // remove duplicated member if there has the same user id
        // remove the pilot member.
        Set<Long> recipientIds = new HashSet<>();
        for(UserTinyVo member: members) {
            if(!recipientIds.contains(member.getUserId())) {
                if(member.getRoleId() == RoleEnum.PILOT.getCode()) {
                    // 飞手不接收有附件的邮件
                    pilot = member;
                    continue;
                }
                if(pilot != null && member.getUserId() == pilot.getUserId()) {
                    // 如果pilot还担任其他的角色，则以其他角色为准
                    pilot = null;
                }
                emailRecipients.add(member);
                recipientIds.add(member.getUserId());
            }
        }
        return emailRecipients;
    }

	/**
	 * 判断是否要在邮件中增加图片压缩包下载链接
	 */
	private boolean isEmailWithImageZipLink(long projectId, boolean withReportAttachment, ProjectReportFile report) {

        if(report == null || !withReportAttachment) {
        	// 邮件中必须含有报告附件
			return false;
		}
		// 必须是Under writing 类型报告
        Set<Integer> reportsNeed = new HashSet<>(Arrays.asList(
        	ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT.getCode(),
			ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT.getCode()));

		if(!reportsNeed.contains(report.getReportType())) {
			return false;
		}

		// 同时必须满足数据库中已经含有图片压缩文件路径，利用boolean的特性，可以在withImagesZipLink为false时不执行后面的语句
		return !StringUtils.isEmpty(projectMapper.getImagesArchiveUrl(projectId));
	}

	@Override
	public void disapprovedReport(long projectId, long userId, String reportId) throws ServiceException {

		ProjectReportFile report = getById(projectId, reportId);
		if(report == null) {
			throw new ServiceException(MessageCode.REPORT_NOT_EXIST);
		}
		ReportGenerationStatusEnum reportStatus = ReportGenerationStatusEnum.getEnum(report.getGenerationStatus());

		switch(reportStatus) {
		case GENERATED: {
			throw new ServiceException(MessageCode.REPORT_NOT_SUBMITTED);
		}
		case SUBMITTED: {
			ReportTypeEnum reportType = ReportTypeEnum.getEnum(report.getReportType());
			try {
				updateReportGenerationStatus(projectId, userId, reportId,
						ReportGenerationStatusEnum.DISAPPROVED, reportType.REJECTED);
			} catch (AccessDatabaseException e) {
				throw new ServiceException(MessageCode.DATABASE_EXCEPTION, e);
			}

			infoProcessorReportDisapproved(projectId, reportType);
			return ;
		}
		case DISAPPROVED: {
			return;
		}
		case APPROVED: {
			throw new ServiceException(MessageCode.REPORT_BEEN_APPROVED);
		}
		default: {
			throw new ServiceException(MessageCode.SYSTEM_EXCEPTION);
		}
		}
	}

	@Override
	public void reprocessReport(long projectId, long userId, String reportId) throws ServiceException {
		ProjectReportFile report = getById(projectId, reportId);
		if(report == null) {
			throw new ServiceException(MessageCode.REPORT_NOT_EXIST);
		}
		ReportTypeEnum reportType = ReportTypeEnum.getEnum(report.getReportType());
		// 将项目的状态重新恢复为Generated
		updateReportGenerationStatus(projectId, userId, reportId,
			ReportGenerationStatusEnum.GENERATED, null);
	}

	public void infoProcessorReportDisapproved(long projectId, ReportTypeEnum reportType) throws ServiceException {
		List<UserTinyVo> members = userService.listActiveMemberInProject(projectId);

		for(UserTinyVo user: members) {
			if(user.getRoleId() == RoleEnum.PROCESSOR.getCode()) {
				messageService.infoReportDisapproved(projectId, reportType, user);
				notificationService.notifyReportDisapproved(projectId, reportType, user);
			}
		}
	}

    @Override
    public List<ReportTypeDto> getReportTypes(long projectId) throws ServiceException {
        List<IdTypeDto> fileSourceTypes;
        List<ReportTypeDto> resultTypes = new ArrayList<>();
        try {
            fileSourceTypes = projectImageMapper.listImageFileSourceTypes(projectId);
        } catch (Exception e) {
            log.error("Fail to list ReportTypes in project:{}", projectId, e);
            throw new ServiceException(MessageCode.DATABASE_EXCEPTION, e);
        }

        Set<Integer> fileSourceTypeSet = ListUtil.toSet(IdTypeDto::getType, fileSourceTypes);
        if (CollectionAssistant.isNotEmpty(fileSourceTypeSet)) {
            addBaseReportType(resultTypes);
        }
        if (fileSourceTypeSet.contains(FileSourceTypeEnum.DRONE_PREVISIT_IMAGE.getCode())) {
            addReportType(resultTypes, ReportTypeEnum.QUICK_ROOF_EVALUATION_REPORT);
        }
        if (fileSourceTypeSet.contains(FileSourceTypeEnum.DRONE_REALTIME_IMAGE.getCode())) {
            addReportType(resultTypes, ReportTypeEnum.REALTIME_ROOF_INSPECTION_REPORT);
        }
        return resultTypes;
    }

    private void addBaseReportType(List<ReportTypeDto> resultTypes) {
        addReportType(resultTypes, ReportTypeEnum.PREMIUM_DAMAGE_ASSESSMENT_REPORT);
        addReportType(resultTypes, ReportTypeEnum.PROPERTY_IMAGE_REPORT);
        addReportType(resultTypes, ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT);
        addReportType(resultTypes, ReportTypeEnum.FULL_SCOPE_UNDERWRITING_REPORT);
    }

    private void addReportType(List<ReportTypeDto> resultTypes, ReportTypeEnum resultType) {
		ReportTypeDto report = new ReportTypeDto(resultType.getCode(), resultType.getDisplay());
		resultTypes.add(report);
	}

	@Override
	public ProjectReportFile createOrReplaceReportFile(long userId, long projectId,
		ProjectReportFileDto projectReportFileDto)
			throws ServiceException {
		int reportTypeCode = projectReportFileDto.getReportType();
		ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportTypeCode);
		if (reportType == null) {
			throw new ServiceException(MessageCode.PARAM_INVALID, "invalid report type");
		}
		ProjectReportFile newProjectReportFile = updateReportFile(userId, projectId, projectReportFileDto);
		// 写入相关的 EventHistory
		updateReportGenerationStatus(projectId, userId, newProjectReportFile.getReportId(),
			ReportGenerationStatusEnum.GENERATED, reportType.GENERATED);
		return newProjectReportFile;
	}

    @Override
	public boolean isPackageExist(long projectId, int reportType) {
        return resourcePool.head(getReportImageFileKey(projectId, reportType)) != null;
    }

	@Override
	public String getReportImageFileKey(long projectId, int reportType) {
		Project project = projectMapper.getById(projectId);
		String packageName = CommonsUtil.getPackageName(project, reportType);
		return CommonsUtil.getPackageKey(projectId, reportType, packageName);
	}

	private ProjectReportFile updateReportFile(long userId, long projectId, ProjectReportFileDto projectReportFileDto) {

		ProjectReportFile projectReportFile = new ProjectReportFile();
		projectReportFile.setProjectId(projectId);
		projectReportFile.setCreatedBy(userId);
		projectReportFile.setCreatedTime(System.currentTimeMillis());
		projectReportFile.setRead(false);
		projectReportFile.setDeleted(false);

		projectReportFile.setGenerationStatus(ReportGenerationStatusEnum.GENERATED.getCode());
		projectReportFile.setReportType(projectReportFileDto.getReportType());
		projectReportFile.setReportWordFileName("");
		projectReportFile.setReportPdfFileName(projectReportFileDto.getReportUrl());
		projectReportFile.setSize(projectReportFileDto.getSize());
		projectReportFile.setReportPdfCompressed(projectReportFileDto.getReportCompressed());
		projectReportFile.setSizeCompressed(projectReportFileDto.getSizeCompressed());
		updateReportFile(projectReportFile);

		return projectReportFile;
	}

    @Override
    public String save(ProjectReportFile projectReportFile) {
        var projectId = projectReportFile.getProjectId();
        var reportType = projectReportFile.getReportType();
        var summary =
            Optional.ofNullable(reportSummaryService.getOne(projectId, reportType))
                .map(ReportSummary::getSummary)
                .orElse(ReportSummaryService.NULL_SUMMARY);
        summary = StringUtils.isBlank(summary) ? ReportSummaryService.NULL_SUMMARY : summary;
        return save(projectReportFile, summary);
    }

    @Override
    public String save(ProjectReportFile reportFile, String summary) {
        var projectId = reportFile.getProjectId();
        var reportType = reportFile.getReportType();
        var resourceUri = reportFile.getReportPdfFileName();
        var createdBy = String.valueOf(reportFile.getCreatedBy());
        var existsReportId = reportFile.getReportId();
        String reportId = reportFile.getReportId();
        try {
            if (StringUtils.isBlank(existsReportId) || reportManager.findById(existsReportId) == null) {
                var report =
                    reportManager.create(
                        String.valueOf(reportType),
                        resourceUri,
                        summary,
                        StringUtils.EMPTY,
                        createdBy);
                reportId = report.getId();
                projectReportManager.add(String.valueOf(projectId), reportId, createdBy);
            }
        } catch (Exception e) {
            log.info("update report data create report[projectId: %d, reportType: %d] failed.".formatted(projectId, reportType), e);
            reportId = SecureTokens.generateRandomBase64Token();
        }
        reportFile.setReportId(reportId);

        // mysql summary的代码还有通过projectIds查询
        try {
            var reportSummary = new ReportSummary();
            reportSummary.setProjectId(projectId);
            reportSummary.setDeleted(false);
            reportSummary.setReportType(reportType);
            if (StringUtils.equals(ReportSummaryService.NULL_SUMMARY, summary)) {
                reportSummary.setSummary(StringUtils.EMPTY);
            } else {
                reportSummary.setSummary(summary);
            }
            reportSummaryService.upsert(reportSummary);
        } catch (Exception e) {
            log.warn("save project {} report type {} to mysql failed.", projectId, reportType, e);
        }
        return reportId;
    }

    @Override
    public ProjectReportFile getById(String reportId) {
        var report = reportManager.findById(reportId);
        return Optional.ofNullable(report)
                .map(
                        r -> {
                            long projectId = findProjectIdFromPgsql(reportId);
                            return convertReportToWEBReport(projectId, r);
                        })
                .orElseThrow(() -> new NoSuchElementException("The report does not exist."));
    }

    private long findProjectIdFromPgsql(String reportId) {
        var projectIds = projectReportManager.findProjectId(reportId);
        if (com.google.common.collect.Iterables.isEmpty(projectIds)) {
            throw new IllegalArgumentException(
                "The projectId of report %s does not exist.".formatted(reportId));
        }
        return Long.parseLong(Iterables.toList(projectIds).get(0));
    }

    @Override
    public ProjectReportFile getById(long projectId, String reportId) {
        var report = reportManager.findById(reportId);
        return Optional.ofNullable(report)
            .map(r -> convertReportToWEBReport(projectId, r))
            .orElseThrow(() -> new NoSuchElementException("The report does not exist."));
    }

    private ProjectReportFile getByType(long projectId, int reportType) {
        var reports =
            projectReportManager.find(
                String.valueOf(projectId), String.valueOf(reportType), null);
        if (com.google.common.collect.Iterables.isEmpty(reports)) {
            return null;
        }
        var resultReports = convertReportToWEBReports(projectId, reports);
        return filterWebReportFile(resultReports).get(0);
    }

    private List<ProjectReportFileTinyVo> getReportFiles(long projectId, Integer reportType) {
        List<ProjectReportFile> reportFiles;
        if (reportType == null) {
            reportFiles = getAllReportFiles(projectId);
        } else {
            reportFiles =
                    Optional.ofNullable(getByType(projectId, reportType))
                            .map(List::of)
                            .orElse(new ArrayList<>());
        }
        if (CollectionUtils.isEmpty(reportFiles)) {
            return new ArrayList<>();
        }
        return reportFiles.stream().map(r -> {
            var tinyReport = new ProjectReportFileTinyVo();
            tinyReport.setReportId(r.getReportId());
            tinyReport.setReportType(r.getReportType());
            tinyReport.setReportUrl(r.getReportPdfFileName());
            tinyReport.setRead(true);
            tinyReport.setReportPdfCompressed(r.getReportPdfCompressed());
            tinyReport.setCreatedTime(r.getCreatedTime());
            tinyReport.setSize(r.getSize());
            tinyReport.setSizeCompressed(r.getSizeCompressed());
            tinyReport.setGenerationStatus(r.getGenerationStatus());
            tinyReport.setPaid(true);
            tinyReport.setS3Key(r.getReportPdfFileName());
            tinyReport.setReportId(r.getReportId());
            tinyReport.setFileType(r.getFileType());
            return tinyReport;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ProjectReportFile> getAllReportFiles(long projectId) {
        var reports = projectReportManager.findAll(String.valueOf(projectId));
        var projectReports = convertReportToWEBReports(projectId, reports);
        return filterWebReportFile(projectReports).stream()
            .sorted(Comparator.comparingInt(ProjectReportFile::getReportType))
            .collect(Collectors.toList());
    }

    private List<ProjectReportFile> filterWebReportFile(List<ProjectReportFile> reportFiles) {
        List<ProjectReportFile> filterReports = new ArrayList<>();
        if (CollectionUtils.isEmpty(reportFiles)) {
            return filterReports;
        }
        var reportTypeGroup =
                reportFiles.stream()
                        .collect(Collectors.groupingBy(ProjectReportFile::getReportType));
        filterReports =
                reportTypeGroup.values().stream()
                        .map(this::getApprovedOrNewReport)
                        .collect(Collectors.toList());
        return filterReports;
    }

    private ProjectReportFile getApprovedOrNewReport(List<ProjectReportFile> reportFiles) {
        var reports =
                reportFiles.stream()
                        .filter(
                                report ->
                                        report.getGenerationStatus()
                                                == ReportGenerationStatusEnum.APPROVED.getCode())
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(reports)) {
            reports = reportFiles;
        }
        reports.sort((o1, o2) -> Long.compare(o2.getCreatedTime(), o1.getCreatedTime()));
        return reports.get(0);
    }

    private void updateReportGenerationStatus(String reportId, int status, String updatedBy) {
        Optional.ofNullable(Status.forNumber(status))
            .ifPresent(value -> reportManager.updateStatus(reportId, value, updatedBy));
    }

    @Override
    // this is to make sure that report file and summary will be added to db atomically
    public void updateReportFile(ProjectReportFile projectReportFile) {
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(projectReportFile.getReportType());
        deleteReport(projectReportFile.getProjectId(), projectReportFile.getCreatedBy(), reportType);

        // TODO 需要考虑是否通过projectReportService.submitReport()进行处理
        save(projectReportFile);

        Project project = projectMapper.getById(projectReportFile.getProjectId());
        publisher.publishEvent(new ProjectReportFileUpdatedEvent(this, project, projectReportFile));
    }

    @Override
    // this is to make sure that report file and summary will be added to db atomically
    public void updateReportFile(ProjectReportFile projectReportFile, String summary) {
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(projectReportFile.getReportType());
        deleteReport(projectReportFile.getProjectId(), projectReportFile.getCreatedBy(), reportType);

        save(projectReportFile, summary);

        Project project = projectMapper.getById(projectReportFile.getProjectId());
        publisher.publishEvent(new ProjectReportFileUpdatedEvent(this, project, projectReportFile));
    }

    @Override
    public int update(long projectId, ProjectReportFile reportFile) {
        var reportId = reportFile.getReportId();
        var url = reportFile.getReportPdfFileName();
        log.info("update report " + reportId + " url " + url);
        // 目前这个功能就是重新生成report, 只有pdf
        var report = getById(projectId, reportId);
        deleteReport(projectId, reportFile.getCreatedBy(), reportId);
        report.setReportPdfFileName(url);
        report.setSize(reportFile.getSize());
        report.setReportId(null);
        save(report);
        reportFile.setReportId(report.getReportId());
        return 1;
    }

    @Override
    public void deleteReport(long projectId, long userId, ReportTypeEnum reportType) {
        var reports =
            projectReportManager.find(
                String.valueOf(projectId), String.valueOf(reportType.getCode()), null);
        var reportIds = Iterables.toStream(reports).map(Report::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(reportIds)) {
            projectReportManager.delete(String.valueOf(projectId), reportIds, String.valueOf(userId));
        }
    }

    @Override
    public void deleteReport(long projectId, long userId, String reportId) {
        projectReportManager.delete(String.valueOf(projectId), List.of(reportId), String.valueOf(userId));
    }

    private List<ProjectReportFile> convertReportToWEBReports(long projectId, Iterable<? extends Report> reports) {
        return Iterables.toStream(reports)
            .filter(report -> report.getResourceUrl().containsKey(Type.ORIGIN))
            .map(report -> convertReportToWEBReport(projectId, report))
            .collect(Collectors.toList());
    }

    private ProjectReportFile convertReportToWEBReport(long projectId, Report report) {
        if (!report.getResourceUrl().containsKey(Type.ORIGIN)) {
            return null;
        }
        var resourceMap = report.getResource();
        var resourceKeyMap = report.getResourceKey();
        var size =
            Optional.ofNullable(resourceMap.get(Type.ORIGIN))
                .map(r -> r.getMetadata().getContentLength())
                .orElse(0L);
        var contentType =
            Optional.ofNullable(resourceMap.get(Type.ORIGIN))
                .map(r -> r.getMetadata().getContentType())
                .orElse("");
        var sizeCompressed =
            Optional.ofNullable(resourceMap.get(Type.COMPRESSED))
                .map(r -> r.getMetadata().getContentLength())
                .orElse(0L);

        var reportFile = new ProjectReportFile();
        reportFile.setReportId(report.getId());
        reportFile.setReportType(getReportType(report));
        reportFile.setReportPdfFileName(resourceKeyMap.get(Type.ORIGIN));
        reportFile.setReportPdfCompressed(
                Optional.ofNullable(resourceKeyMap.get(Type.COMPRESSED)).orElse(StringUtils.EMPTY));
        reportFile.setGenerationStatus(report.getStatus().getNumber());
        reportFile.setRead(true);
        reportFile.setDeleted(false);
        reportFile.setProjectId(projectId);
        reportFile.setCreatedTime(report.getCreatedAt().toEpochMilli());
        reportFile.setSize(Math.toIntExact(size));
        reportFile.setSizeCompressed(Math.toIntExact(sizeCompressed));
        reportFile.setCreatedBy(User.AI_ID);
        reportFile.setFileType(contentType);
        return reportFile;
    }

    public static Integer getReportType(Report report) {
        Integer mysqlType = null;
        var reportTypes = ReportTypeEnum.values();
        for (var type : reportTypes) {
            if (StringUtils.equalsAny(
                    report.getType(),
                    type.name(),
                    type.getShortCut(),
                    type.getDisplay(),
                    String.valueOf(type.getCode()))) {
                mysqlType = type.getCode();
            }
        }
        return mysqlType;
    }
}
