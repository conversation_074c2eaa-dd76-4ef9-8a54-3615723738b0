package com.bees360.service.event.report;

import com.bees360.entity.ReportSummary;
import com.bees360.service.event.Bees360Event;
import lombok.Getter;

public class ReportSummaryChangedEvent extends Bees360Event {

    @Getter
    private ReportSummary reportSummary;

    public ReportSummaryChangedEvent(Object source, ReportSummary reportSummary) {
        super(source);
        this.reportSummary = reportSummary;
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }
}
