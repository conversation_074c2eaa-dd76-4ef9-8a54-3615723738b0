package com.bees360.service.threadpool;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.dto.ProjectBatchItemDto;
import com.bees360.entity.vo.UserTinyVo;
import java.util.List;
import java.util.concurrent.Future;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 异步操作，提高批量生成project的速度
 *
 * <AUTHOR>
 * @date 2021/06/04 12:11
 */
@Slf4j
@Component
public class AsyncCreateBeeObjectThreadPool {

    @FunctionalInterface
    public interface AsyncFunction<T, P1, P2, P3, P4> {

        T exec(P1 userId, P2 project, P3 projectBatchItemDto, P4 allowDuplication) throws ServiceException;
    }

    /**
     * 异步生成project，线程池见{@link AsyncTaskThreadPoolConfig#createBeeProjectThreadPool()}
     */
    @Async("createBeeProjectThreadPool")
    @Retryable(interceptor = "retryCreateBeeProjectInterceptor")
    @Transactional(rollbackFor = ServiceException.class)
    public Future<OutputParam> submit(long userId, Project project, ProjectBatchItemDto projectBatchItemDto, boolean allowDuplication,
        AsyncFunction<OutputParam, Long, Project, ProjectBatchItemDto, Boolean> function) throws ServiceException {
        return new AsyncResult<>(function.exec(userId, project, projectBatchItemDto, allowDuplication));
    }

    /**
     * 返回类型
     */
    public static class OutputParam {

        Project project;
        List<UserTinyVo> memberUsers;

        public Project getProject() {
            return project;
        }

        public void setProject(Project project) {
            this.project = project;
        }

        public List<UserTinyVo> getMemberUsers() {
            return memberUsers;
        }

        public void setMemberUsers(List<UserTinyVo> memberUsers) {
            this.memberUsers = memberUsers;
        }
    }

}
