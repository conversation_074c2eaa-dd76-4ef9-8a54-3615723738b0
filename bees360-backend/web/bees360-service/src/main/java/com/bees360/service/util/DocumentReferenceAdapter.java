package com.bees360.service.util;

import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import java.lang.reflect.Type;

public class DocumentReferenceAdapter implements JsonDeserializer<DocumentReference> {

    private final Firestore firestore;

    public DocumentReferenceAdapter(Firestore firestore) {
        this.firestore = firestore;
    }

    @Override
    public DocumentReference deserialize(
            JsonElement json, Type typeOfT, JsonDeserializationContext context)
            throws JsonParseException {
        JsonObject reference = json.getAsJsonObject();
        JsonArray pathArray =
                reference.get("path").getAsJsonObject().get("segments").getAsJsonArray();

        StringBuilder path = new StringBuilder();
        pathArray
                .iterator()
                .forEachRemaining(
                        jsonElement -> path.append(jsonElement.getAsString()).append("/"));
        path.delete(path.length() - 1, path.length());
        return firestore.document(path.toString());
    }
}
