package com.bees360.service.event.pilot;

import com.bees360.entity.BeesPilotBatch;
import com.bees360.entity.enums.PilotBatchEventTypeEnum;
import com.bees360.service.event.Bees360Event;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/08/22 14:13
 */
public class PilotBatchChangeEvent extends Bees360Event {

    @Getter
    private long pilotId;

    @Getter
    private BeesPilotBatch batch;

    @Getter
    private List<Long> projectIds;

    @Getter
    private PilotBatchEventTypeEnum typeEnum;

    public PilotBatchChangeEvent(Object source, long pilotId, BeesPilotBatch batch, List<Long> projectIds, PilotBatchEventTypeEnum typeEnum) {
        super(source);
        this.pilotId = pilotId;
        this.batch = batch;
        this.projectIds = projectIds;
        this.typeEnum = typeEnum;
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }

}
