package com.bees360.service.listener.project;

import com.bees360.entity.enums.AiBotUserEnum;
import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.service.ProjectLabelService;
import com.bees360.web.event.project.ProjectServiceTypeChangedEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class ProjectServiceTypeListener {

    @Autowired private ProjectLabelService projectLabelServiceImpl;

    /** 服务类型从4-Point、4-Point self修改成exterior之后，去除missing interior images 标签 */
    @Async
    @EventListener
    public void tagMissingInteriorImagesListener(ProjectServiceTypeChangedEvent event) {
        log.info(
                "Project service type changed, automatic remove 'missing interior image' projectId {}, before {}, after {}",
                event.getProjectId(),
                event.getBefore(),
                event.getAfter());

        List<ProjectServiceTypeEnum> beforeType =
                List.of(
                        ProjectServiceTypeEnum.FOUR_POINT_UNDERWRITING,
                        ProjectServiceTypeEnum.FOUR_POINT_SELF_UNDERWRITING,
                        ProjectServiceTypeEnum.PREMIUM_FOUR_POINT_UNDERWRITING);
        List<ProjectServiceTypeEnum> afterType =
                List.of(
                        ProjectServiceTypeEnum.EXTERIOR_UNDERWRITING,
                        ProjectServiceTypeEnum.ROOF_ONLY_UNDERWRITING);
        if (beforeType.contains(event.getBefore()) && afterType.contains(event.getAfter())) {
            handleRemoveMissingInteriorTag(event.getProjectId());
        }
    }

    private void handleRemoveMissingInteriorTag(long projectId) {
        Optional<BoundProjectLabel> projectLabel = projectLabelServiceImpl.projectLabel(projectId);
        if (projectLabel.isEmpty()) {
            return;
        }
        BoundProjectLabel label = projectLabel.get();
        if (CollectionUtils.isEmpty(label.getProjectLabels())) {
            return;
        }
        boolean hasMissingTag =
                label.getProjectLabels().stream()
                        .noneMatch(
                                tag ->
                                        ProjectLabelEnum.MISSING_INTERIOR_IMAGES
                                                .getLabelId()
                                                .equals(tag.getLabelId()));
        if (hasMissingTag) {
            return;
        }

        projectLabelServiceImpl.markAfterEraseLabel(
                projectId,
                List.of(),
                AiBotUserEnum.AI_NEW_USER_ID.getIntegerCode(),
                SystemTypeEnum.BEES360);
        log.info("Automatic removed tag missing interior image succeed projectId {}", projectId);
    }
}
