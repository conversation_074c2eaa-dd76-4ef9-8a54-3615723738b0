package com.bees360.event;

import com.bees360.event.registry.BeespilotImageUploadEvent;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.SyncImageFromFirebaseJob;
import com.bees360.job.util.EventTriggeredJob;
import lombok.extern.log4j.Log4j2;

import java.time.Duration;

/**
 * 监听BeespilotImageUploadEvent事件并触发SyncImageFromFirebaseJob任务，用于从Firebase同步图片数据
 */
@Log4j2
public class SyncImageFromFirebaseOnBeespilotImageUpload extends EventTriggeredJob<BeespilotImageUploadEvent> {

    public SyncImageFromFirebaseOnBeespilotImageUpload(JobScheduler jobScheduler) {
        super(jobScheduler);
        log.info("Created {}.", this);
    }

    @Override
    protected Job convert(BeespilotImageUploadEvent event) {
        var payload = new SyncImageFromFirebaseJob();
        payload.setMissionPath(event.getMissionPath());
        payload.setPilotId(event.getPilotId());
        payload.setProjectId(event.getProjectId());
        return RetryableJob.of(Job.ofPayload(payload), 3, Duration.ofSeconds(3), 1.0F);
    }
}
