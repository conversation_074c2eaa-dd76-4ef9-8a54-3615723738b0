package com.bees360.service.impl;

import com.bees360.activity.Activity;
import com.bees360.activity.ActivityManager;
import com.bees360.activity.Comment;
import com.bees360.activity.CommentManager;
import com.bees360.activity.Message;
import com.bees360.schedule.job.retry.RetryableJob;
import com.bees360.schedule.job.retry.RetryableJobTriggerFactory;
import com.bees360.schedule.job.retry.RetryableOperation;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;
import com.bees360.schedule.service.SchedulerManagerTransactionalDelegate;
import com.bees360.schedule.util.QuartzJobConstant;
import com.bees360.service.CommentService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.google.protobuf.ByteString;
import com.google.protobuf.util.Timestamps;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UncheckedIOException;
import java.time.Duration;
import java.util.Date;

@Service
@Slf4j
public class CommentServiceImpl implements CommentService {
    private final static String SYSTEM_NOTE = "SYSTEM_NOTE";

    @Autowired
    private ActivityManager activityManager;
    @Autowired
    private CommentManager commentManager;
    @Autowired
    private Bees360FeatureSwitch bees360FeatureSwitch;
    @Autowired
    private SchedulerManagerTransactionalDelegate schedulerManagerTransactionalDelegate;
    @Override
    public void addComment(long projectId, String createdBy, String content, long createAt) {
        execRetryable(Comment.from(projectId, createdBy, content, Timestamps.fromMillis(createAt), Timestamps.fromMillis(createAt)));
    }

    private void execRetryable(Comment comment) {
        try {
            if (bees360FeatureSwitch.isEnableSystemComment()) {
                activityManager.submitActivity(from(comment));
            } else {
                commentManager.addComment(comment);
            }
        } catch (IllegalStateException e) {
            log.error("Failed to submit comment '{}' and it will not be retried.", comment, e);
        } catch (UncheckedIOException e) {
            log.error("Failed to submit comment '{}' and it will be retried after 5 minutes.", comment, e);
            JobDetail jobDetail = createJobDetail(comment);
            JobKey jobKey = jobDetail.getKey();
            // 五分钟之后同步
            Trigger trigger = RetryableJobTriggerFactory
                .retryForeverTriggerStartAt(jobKey.getName(), jobKey.getGroup(), Duration.ofMinutes(10), DateUtils.addMinutes(new Date(), 5)).build();
            try {
                schedulerManagerTransactionalDelegate.scheduleJob(jobDetail, trigger);
            } catch (SchedulerException schedulerException) {
                String message = "Failed to submit quartz job to trigger comment '%s'.".formatted(comment);
                log.error(message, schedulerException);
                throw new IllegalStateException(message, schedulerException);
            }
        }
    }

    public static Activity from(Comment comment) {
        var activityBuilder = Message.ActivityMessage.newBuilder();
        var commentMsg = comment.toMessage();
        activityBuilder.setAction(Message.ActivityMessage.ActionType.CREATE.name());
        activityBuilder.setEntity(
                Message.ActivityMessage.Entity.newBuilder().setType(SYSTEM_NOTE).build());
        activityBuilder.setProjectId(commentMsg.getProjectId());
        activityBuilder.setCreatedBy(commentMsg.getCreatedBy());
        activityBuilder.setSource(commentMsg.getSource());
        activityBuilder.setCreatedAt(commentMsg.getCreatedAt());
        activityBuilder.setComment(commentMsg);
        return Activity.from(activityBuilder.build());
    }

    public JobDetail createJobDetail(Comment comment) {
        String jobName = "comment-submit-trigger-%s-%s-%s-%s".formatted(
            // 用project action creator + 时间 作为jobName
            comment.getProjectId(), comment.getCreatedBy(), comment.getCreatedAt(), comment.getUpdatedAt());
        JobDataMap jobData = new JobDataMap();
        jobData.put("projectId", comment.getProjectId());
        jobData.put("creator", comment.getCreatedBy());
        jobData.put("byteString", comment.toByteString());
        String description = "Trigger to submit comment '%s'.".formatted(comment);

        return JobBuilder.newJob(CommentServiceImpl.CommentSubmitRetry.class)
            .withIdentity(jobName, QuartzJobConstant.WebGroup.COMMENT_SUBMIT_RETRY)
            .usingJobData(jobData)
            .withDescription(description)
            .requestRecovery(true)
            .build();
    }

    @Slf4j
    private static class CommentSubmitRetry extends RetryableJob {
        @Autowired
        private ActivityManager activityManager;
        @Autowired
        private CommentManager commentManager;
        @Autowired
        private Bees360FeatureSwitch bees360FeatureSwitch;
        @Setter
        private Long projectId;
        @Setter
        private String creator;
        @Setter
        private ByteString byteString;

        @Override
        protected RetryableOperation getRetryableOperation() {
            return ctx -> {
                Message.CommentMessage message = Message.CommentMessage.parseFrom(byteString);
                log.info(
                        "Start to submit  project '{}' comment '{}' created by '{}'.",
                        projectId,
                        message.toString(),
                        creator);
                if (bees360FeatureSwitch.isEnableSystemComment()) {
                    activityManager.submitActivity(from(Comment.from(message)));
                } else {
                    commentManager.addComment(Comment.from(message));
                }
                log.info(
                        "Success to submit project '{}' comment '{}' created by '{}'.",
                        projectId,
                        message.toString(),
                        creator);
            };
        }

        @Override
        protected RetryExceptionHandler getRetryExceptionHandler() {
            return ctx -> {
                if (ctx.getRetryCount() > 10) {
                    log.error("Failed to submit project '{}' submit created by {} and it will stop retry", projectId, creator);
                    return false;
                }
                log.error("Failed to submit project '{}' submit created by {} and it will be retried.", projectId, creator);
                return true;
            };
        }
    }
}
