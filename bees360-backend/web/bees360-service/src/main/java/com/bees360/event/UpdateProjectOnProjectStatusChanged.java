package com.bees360.event;

import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.ProjectStatus;
import com.bees360.event.registry.ProjectStatusChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectMapper;
import com.bees360.project.Message;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.UserService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听项目状态变更事件并更新项目状态，处理回滚操作时发布对应的事件。
 */
@Log4j2
public class UpdateProjectOnProjectStatusChanged extends AbstractNamedEventListener<ProjectStatusChanged> {

    private final ProjectMapper projectMapper;

    private final UserService userService;

    private final ProjectStatusService projectStatusService;

    public UpdateProjectOnProjectStatusChanged(ProjectMapper projectMapper, UserService userService, ProjectStatusService projectStatusService) {
        this.projectMapper = projectMapper;
        this.userService = userService;
        this.projectStatusService = projectStatusService;
    }

    @Override
    public void handle(ProjectStatusChanged event) throws IOException {
        var projectId = Long.parseLong(event.getProjectId());
        var updatedAt = event.getUpdatedAt().toEpochMilli();
        var status = event.getStatus();

        // filter PROJECT CREATED status, it will be updated in project creation transaction.
        if (status == NewProjectStatusEnum.PROJECT_CREATED.getCode() && !event.isRollback()) {
            return;
        }

        var userId = userService.toWebUserId(event.getUpdatedBy());
        if (userId == null) {
            var msg = String.format("Failed to update project %s status " +
                "due to user %s not exists in web.", projectId, event.getUpdatedBy());
            throw new IllegalStateException(msg);
        }

        projectMapper.updateProjectStatus(projectId, status, updatedAt, event.getVersion());

        // rollback会触发ProjectStatusChanged事件
        // 这里为了兼容当前web端的逻辑，发出对应的application event
        if (event.isRollback()) {
            var projectStatus = new ProjectStatus();
            projectStatus.setStatus(status);
            projectStatus.setProjectId(projectId);
            projectStatus.setUserId(userId);
            projectStatus.setCreatedTime(updatedAt);
            projectStatusService.publishProjectStatusChangeEvent(projectStatus, true);
        }
        log.info("Successfully update project {} status to {}.", projectId, status);
    }
}
