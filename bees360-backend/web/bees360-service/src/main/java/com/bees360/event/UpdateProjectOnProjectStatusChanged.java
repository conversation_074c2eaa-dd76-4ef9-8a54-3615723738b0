package com.bees360.event;

import com.bees360.entity.ProjectStatus;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.event.registry.ProjectStatusChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectMapper;
import com.bees360.project.Message;
import com.bees360.project.status.ProjectStatusProvider;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.UserService;
import com.bees360.util.Iterables;
import com.google.api.client.util.Preconditions;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Optional;

/**
 * 监听项目状态变更事件并更新项目状态，处理回滚操作时发布对应的事件。
 */
@Log4j2
public class UpdateProjectOnProjectStatusChanged extends AbstractNamedEventListener<ProjectStatusChanged> {

    private final ProjectMapper projectMapper;

    private final UserService userService;

    private final ProjectStatusService projectStatusService;

    private final ProjectStatusProvider projectStatusProvider;

    public UpdateProjectOnProjectStatusChanged(ProjectMapper projectMapper, UserService userService, ProjectStatusService projectStatusService,  ProjectStatusProvider projectStatusProvider) {
        this.projectMapper = projectMapper;
        this.userService = userService;
        this.projectStatusService = projectStatusService;
        this.projectStatusProvider = projectStatusProvider;
    }

    @Override
    public void handle(ProjectStatusChanged event) throws IOException {
        var projectId = Long.parseLong(event.getProjectId());
        long updatedAt = event.getUpdatedAt().toEpochMilli();
        var status = event.getStatus();
        var isRollback = event.isRollback();
        var version = event.getVersion();
        /* TODO
         这个字段从原来的 rollback status 旧 timestamp 改成新的 当前时间的  timestamp
         如果需要排除 mysql 的影响，需要读取 status history list 找到 updated_at <= version 的最新的 status history updated_at
         */
        if (isRollback) {
            var statusHistory = projectStatusProvider.getStatusHistory(String.valueOf(projectId));
            var projectStatus = Iterables.toStream(statusHistory)
                .filter(s -> s.getUpdatedAt().toEpochMilli() <= version)
                .sorted((o1, o2) -> o2.getUpdatedAt().compareTo(o1.getUpdatedAt()))
                .filter(s -> s.getStatus().getNumber() == status)
                .findFirst();
            Preconditions.checkArgument(
                projectStatus.isPresent(), "Failed to find project status history for project %s status %s.", projectId, status);
            updatedAt = projectStatus.get().getUpdatedAt().toEpochMilli();
            log.info("Rollback project {} status to {} at {} (event updatedAt {})", projectId, status, updatedAt, event.getUpdatedAt().toEpochMilli());
        }

        // filter PROJECT CREATED status, it will be updated in project creation transaction.
        if (status == NewProjectStatusEnum.PROJECT_CREATED.getCode() && !isRollback) {
            return;
        }

        var userId = userService.toWebUserId(event.getUpdatedBy());
        if (userId == null) {
            var msg = String.format("Failed to update project %s status " +
                "due to user %s not exists in web.", projectId, event.getUpdatedBy());
            throw new IllegalStateException(msg);
        }

        projectMapper.updateProjectStatus(projectId, status, updatedAt, version);

        // todo 后续需要把这里 publishEvent 移到 UpdateProjectOnProjectStatusChanged 统一触发
        // rollback会触发ProjectStatusChanged事件
        // 这里为了兼容当前web端的逻辑，发出对应的application event
        if (event.isRollback()) {
            var projectStatus = new ProjectStatus();
            projectStatus.setStatus(status);
            projectStatus.setProjectId(projectId);
            projectStatus.setUserId(userId);
            projectStatus.setCreatedTime(updatedAt);
            projectStatusService.publishProjectStatusChangeEvent(projectStatus, true);
        }
        log.info("Successfully update project {} status to {}.", projectId, status);
    }
}
