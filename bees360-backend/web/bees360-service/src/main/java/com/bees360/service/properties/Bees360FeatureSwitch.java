package com.bees360.service.properties;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "bees360.feature-switch")
public class Bees360FeatureSwitch {
    private boolean enableOpenCloseRead;
    private boolean enableOpenCloseWrite;

    private boolean disableUploadImageActivity;

    private boolean enableSystemComment;

    private boolean enableNotRequiredSyncImage = true;

    private boolean enableSyncFirebaseContact = true;

    private boolean enableHandleInvoice;

    private boolean enableFillTimeZoneWhenGetProject;

    private boolean enableCorrectZipcode = false;

    private boolean enableExportActivityData = true;

    private boolean enableExportCatLevel = true;

    private boolean enableRabbitJobInitFirebaseProject;

    private boolean enableRabbitJobSyncFirebaseProject;

    private boolean disableCloseoutReportGenerateCheck = false;

    private boolean enableResetArchiveFilename = true;

    private boolean disableNewFirebaseSyncFunction = true;

    /**
     * 是否启动Authing管理用户，启动后用户信息将不再被web管理，只是作为被同步的数据源，用于web的用户读取
     */
    private boolean disableAuthingUserManager = true;

    private boolean enableGetPartialTypeBySubCategory = true;

    private boolean enableSaveImagePilot = false;

    private boolean enableSaveImageNote = true;

    private boolean enabledSourceInCheckout = true;

    private List<Long> enableNewFirebasePilot = new ArrayList<>();

    private boolean enableChangeOnPilotMission = true;

    private boolean enableChangeOnIBeesMission = true;

    private boolean enableChangeOnProject = true;

    private boolean enableChangeOnBatch = true;

    private boolean enableChangeOnHoverJob = true;

    private boolean enableChangeOnMagicPlan = true;

    private boolean enableActivityMagicplanDispatchFailed = true;

    private boolean enableActivityRecordAdded = true;

    private boolean enableChangeOnPilotMissionCompletedStuck = true;

    private boolean enableChangeOnIBeesMissionCompleted = true;

    private boolean enableCreatedEvent = false;

    private boolean enableSaveImageShootingTime = true;

    private boolean enableFilterCloseoutReportVisibleRange = true;

    private boolean disableReturnedToClient = false;

    private boolean enableReturnedToClientTriggerByPipelineTask = false;

    private boolean enableSyncSurveyCompleted = false;

    private boolean enableParentChildProject= false;

    private boolean enableCloseOutToCancelled = false;

    private boolean enabledUpdateXaTransactionId = false;

    private boolean disableFirebaseSyncCallRecord = false;

    // 是否开启 web 端用户同步更新到 bifrost
    private boolean enabledBifrostUserMapperAdapter = false;

    private boolean disableInspectStatusUpdate = false;

    private boolean disableHandleMissionImage = false;

    private boolean enableRequestCancel = false;

    private boolean enableSetUploadTaskDoneFromPendingWhenImageUploaded = false;

    private boolean disableHandleIbeesMissionImage = false;

    private boolean enableNewOpenapiSearch = false;

    private boolean enableNewReportQueryWhenGetProjectAlert = false;

    private boolean enableCheckHoverStateIdentical = false;
}
