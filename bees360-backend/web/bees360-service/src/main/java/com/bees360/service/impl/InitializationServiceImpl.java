package com.bees360.service.impl;

import com.bees360.service.InitializationService;
import com.bees360.service.grpc.ReportGrpcServerInitializer;
import com.bees360.util.ConstantUtil;
import com.bees360.util.DeleteLowImageRunnable;
import com.bees360.util.IpWorkerCN;
import com.bees360.util.IpWorkerUSA;
import com.bees360.util.RedisUtil;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("initializationServiceImpl")
public class InitializationServiceImpl implements InitializationService{

	private Logger logger = LoggerFactory.getLogger(InitializationServiceImpl.class);

	private static long TWENTY_FOUR_HOURS = 24 * 60 * 60 * 1000;

	@Autowired
	private RedisUtil redisUtil;

	@Autowired
	private ReportGrpcServerInitializer reportGrpcServerInitializer;

	@Override
	@PostConstruct
	public void init(){
		startDeleteImagesSchedule();
		startDeleteReportGenerateStatus();
		reportGrpcServerInitializer.run();
//		startUpdateUSAIPSchedule();
	}

	/**
	 * TODO shoushan.zhao Is it reasonable to do so?
	 */
	private void startDeleteReportGenerateStatus() {
		try {
			if (redisUtil.hasKey(ConstantUtil.REDIS_REPORT_STATUS)) {
				redisUtil.delete(ConstantUtil.REDIS_REPORT_STATUS);
			}
		} catch (Exception e) {
			logger.error("delete report generate status failed.", e);
			e.printStackTrace();
		}
	}

	private void startDeleteImagesSchedule(){
		ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
		// an hour
		long initDelay = 0;
		// an hour
		long period = 3600000;
		logger.info("Start Schedule to delete thumbnail: start after " + initDelay + " ms, and run every hours.");
		Runnable runnable = new DeleteLowImageRunnable();
		executor.scheduleAtFixedRate(runnable, initDelay, period, TimeUnit.MILLISECONDS);
	}

	private void startUpdateUSAIPSchedule(){
		// initialize ipWorker at the first time.
		IpWorkerUSA.getInstance();
		IpWorkerCN.getInstance();

		ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);

		long initDelay = TWENTY_FOUR_HOURS;
		try {
			// delay to the next midnight
			initDelay += getTodayMidNight() - System.currentTimeMillis();
		} catch (Exception e) {
			logger.info("Fail to get time delay to midnight. delay 24 hours to start.");
		}

		executor.scheduleAtFixedRate(new Runnable(){
			@Override
			public void run() {
				// update ipWorker for updating the latest ip ranges of USA.
				IpWorkerUSA.getInstance();
				IpWorkerCN.getInstance();
			}
		}, initDelay, TWENTY_FOUR_HOURS, TimeUnit.MILLISECONDS);
	}

	private void initBaseUser(){
		// create BeesAI
		// create default Admin
	}

	private long getTodayMidNight() throws ParseException{
		DateFormat dateFormat = new SimpleDateFormat("yy-MM-dd HH:mm:ss");
        DateFormat dayFormat = new SimpleDateFormat("yy-MM-dd");
        Date todayMidNight = dateFormat.parse(dayFormat.format(new Date()) + " " + "00:00:00");
        return todayMidNight.getTime();
	}

	public static void main(String[] args){
		InitializationServiceImpl initialization = new InitializationServiceImpl();
		initialization.init();
	}
}
