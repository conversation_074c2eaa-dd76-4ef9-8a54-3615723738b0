//package com.bees360.service.payment;
//
//import java.util.List;
//import java.util.Map;
//import com.bees360.base.exception.ServiceException;
//import com.bees360.entity.enums.ReportTypeEnum;
//import com.bees360.entity.vo.OrderVo;
//
//public interface BookingPaymentService extends PaymentService{
//
//	/**
//	 * getCredentials
//	 * @return
//	 * @throws ServiceException
//	 */
//	public Map<String,String> getCredentials() throws ServiceException;
//
//
//	/**
//	 * 2019-03-26 redefined pay interface order inspection page
//	 * @param bookingContactName
//	 * @param reportTypeEnums
//	 * @param cardNonce
//	 * @param totalAmount
//	 * @param currency
//	 * @return
//	 * @throws ServiceException
//	 */
//	public OrderVo payByCreditCardForUnRegisteredUser(String bookingContactName, List<ReportTypeEnum> reportTypeEnums, String cardNonce,
//			double totalAmount, String currency) throws ServiceException;
//
//	/**
//	 *
//	 * @param reportTypeEnums
//	 * @return
//	 * @throws ServiceException
//	 */
//	public OrderVo createOrderVo(List<ReportTypeEnum> reportTypeEnums)throws ServiceException;
//
//	public OrderVo reCalculateOrder(long userId, OrderVo userOrderVo, boolean discountCalculated) throws ServiceException;
//}
