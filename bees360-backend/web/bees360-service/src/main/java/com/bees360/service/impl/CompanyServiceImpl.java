package com.bees360.service.impl;

import static com.bees360.util.Functions.acceptIfNotNull;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.common.NumberUtils;
import com.bees360.common.collections.CollectionAssistant;
import com.bees360.contract.ContractManager;
import com.bees360.customer.Customer;
import com.bees360.customer.CustomerManager;
import com.bees360.customer.Message;
import com.bees360.customer.Message.CustomerMessage.CustomerRole;
import com.bees360.entity.Company;
import com.bees360.entity.dto.CompanySearchOption;
import com.bees360.entity.dto.systemconfig.SystemConfigBees360Dto;
import com.bees360.entity.enums.CompanyTypeEnum;
import com.bees360.entity.vo.PageResult;
import com.bees360.entity.vo.Pagination;
import com.bees360.event.EventPublisher;
import com.bees360.mapper.CompanyMapper;
import com.bees360.service.CompanyService;
import com.bees360.service.SystemConfigService;
import com.bees360.service.UserService;
import com.bees360.util.CollectionAssitant;
import com.bees360.util.DateUtil;
import com.bees360.util.RedisUtil;
import com.bees360.util.enumsutil.CodeEnumUtil;
import com.google.common.base.Preconditions;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("companyService")
public class CompanyServiceImpl implements CompanyService {

	private Logger logger = LoggerFactory.getLogger(CompanyServiceImpl.class);

	private final int NUMBER_LIMIT = Integer.MAX_VALUE;

	@Autowired
	CompanyMapper companyMapper;

	@Autowired
	private SystemConfigService systemConfigService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired private EventPublisher eventPublisher;

    @Autowired private CustomerManager customerManager;

    @Autowired private ContractManager contractManager;

    @Autowired
    private UserService userService;

    private static final String BEES360 = "Bees360";
    private static final String BEES360TEST = "Bees360 Test Carrier";

    @Value("${bees360.business.customer.contract.signed:false}")
    private boolean customerContractSigned;
    @Value("${bees360.business.customer.company.name}")
    private String customerCompanyName;

    @PostConstruct
    public void updateBees360CompanyCache() {
        List<Company> bees360Companies = companyMapper
            .listWithPrefix(Company.BEES360_COMPANY_NAME_PREFIX.toLowerCase() + "%", NUMBER_LIMIT);
        try {
            if (CollectionAssistant.isEmpty(bees360Companies)) {
                throw new Exception("Bees360 company does not exist in the database.");
            }
            if (redisUtil.hasKey(BEES360_COMPANY_LIST_CACHE_KEY)) {
                redisUtil.delete(BEES360_COMPANY_LIST_CACHE_KEY);
            }
            redisUtil.setDataToRedis(BEES360_COMPANY_LIST_CACHE_KEY, bees360Companies);
        } catch (Exception e) {
            logger.error("bees360 company not exists.", e);
            // Bees360 company must exist in the system.
            System.exit(0);
        }
    }

	@Override
	public List<Company> listAll() throws ServiceException {
		try {
			return companyMapper.listAll();
		} catch (Exception e) {
			logger.error("Fail to list all companies.", e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

	@Override
	public List<Company> listByType(Integer companyType) throws ServiceException{
		if(companyType != null && CodeEnumUtil.getEnumByCode(CompanyTypeEnum.class, companyType) == null){
			return new ArrayList<>();
		}
		try {
			if(companyType == null){
				return companyMapper.listAll();
			} else {
				return companyMapper.listByType(companyType);
			}
		} catch (Exception e) {
			logger.error("Fail to list companies by type.", e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}

	}

	@Override
	public List<Company> listWithPrefix(String prefix) throws ServiceException{
		try {
			return companyMapper.listWithPrefix(prefix + "%", NUMBER_LIMIT);
		} catch (Exception e) {
			logger.error("Fail to list companies with prefix.", e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

	@Override
	public Company getById(long companyId) {
		return companyMapper.getById(companyId);
	}

	@Override
	public Company getByName(String companyName) {
		return companyMapper.getByName(companyName);
	}

    @Override
    public Long getIdByKey(String companyKey) {
        return companyMapper.getIdByKey(companyKey);
    }

    @Override
    @Transactional
	public Company createCompany(String companyName) throws ServiceException {
		Company company = new Company();
		company.setCompanyName(companyName);
		company.setCompanyType(CompanyTypeEnum.OTHER.getCode());
		company.setPhone("");
		company.setEmail("");
		company.setContactName("");
		company.setWebsite("");
		company.setLogo("");
		return createAndSyncCompany(company);
	}

    @Override
    @Transactional
    public Company createCompany(Company company) throws ServiceException {
        return createAndSyncCompany(company);
    }

    /** 创建并且同步公司到postgres上 */
    private Company createAndSyncCompany(Company company) throws ServiceException {
        // 检查company的名称是否存在
        Company companyWithSameName = companyMapper.getByName(company.getCompanyName());
        if (companyWithSameName != null) {
            throw new ServiceException(MessageCode.COMPANY_NAME_HAS_EXISTED);
        }
        long now = DateUtil.getNow();
        company.setIsDeleted(false);
        company.setCreatedTime(now);
        company.setUpdatedTime(now);
        company.setLogo(company.getLogo());
        if (StringUtils.isEmpty(company.getCompanyKey())) {
            company.setCompanyKey(company.getCompanyName());
        }
        companyMapper.insert(company);

        createCustomerAndContract(company.getCompanyName());
        return company;
    }

    /**
     * 同步在postgres创建customer以及其默认的contract
     * customer即company;
     * contract则是公司之间的协议关系，包括一个insured customer和一个 processed customer；
     *
     */
    private void createCustomerAndContract(String name) {
        var company = companyMapper.getByName(name);

        var customer = Message.CustomerMessage.newBuilder()
                                .setId(String.valueOf(company.getCompanyId()))
                                .setName(company.getCompanyName())
                                .setKey(company.getCompanyKey());
        acceptIfNotNull(customer::setWebsite, company.getWebsite());
        acceptIfNotNull(customer::setLogo, company.getLogo());
        acceptIfNotNull(customer::addAllRole, transformToRoles(company.getCompanyType()));
        customerManager.createCustomer(Customer.of(customer.build()));
        logger.info("Add customer {} {} by create company.", customer.getId(), customer.getName());

        // insurance company默认会有2个contract，insured customer是自身，processed customer是bees360
        if (CompanyTypeEnum.INSURANCE_COMPANY.getCode() == company.getCompanyType()) {
            var bees360Company = customerManager.findByName(BEES360);
            var bees360TestCompany = customerManager.findByName(BEES360TEST);
            Optional.ofNullable(bees360Company).ifPresent(b -> contractManager.create(customer.getId(), b.getId()));
            Optional.ofNullable(bees360TestCompany).ifPresent(b -> contractManager.create(customer.getId(), b.getId()));
            logger.info("Add contract for customer {} {}", customer.getId(), customer.getName());
        }
    }

    private List<CustomerRole> transformToRoles(int companyType) {
        CompanyTypeEnum type = CompanyTypeEnum.getEnum(companyType);
        if (type != null && type.equals(CompanyTypeEnum.REPAIR_COMPANY)) {
            return List.of(CustomerRole.UNDERWRITING_SERVICE_PROVIDER, CustomerRole.ADJUSTING_SERVICE_PROVIDER);
        }
        return List.of(CustomerRole.INSURANCE_CARRIER);
    }

    @Override
	public boolean isCompanyExist(String companyName) throws ServiceException {
		Company company = companyMapper.getByName(companyName);
		return company != null && (!company.getIsDeleted());
	}

	@Override
	public List<Company> listByTypeWithPrefix(Integer companyType, String prefix, Integer number)
			throws ServiceException {
		prefix = prefix == null ? "" : prefix;
		prefix += "%";
		number = number == null || number > NUMBER_LIMIT ? NUMBER_LIMIT : number;
		try{
			if(companyType == null){
				return companyMapper.listWithPrefix(prefix, number);
			}else{
				return companyMapper.listByTypeWithPrefix(companyType, prefix, number);
			}
		}catch(Exception e){
			logger.error("Fail to list companies by type with prefix.", e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

	@Override
	public boolean isCustomerContractSigned() {
		boolean isCompanyInitializedRequired = false;
		try {
			isCompanyInitializedRequired = customerContractSigned;
		}
		catch(Exception e) {
			logger.error("load config from setting.properties failed.", e);
		}
		return isCompanyInitializedRequired;
	}

	@Override
	public Company getDefaultCompany() {
		String companyName = null;
		try {
			if(isCustomerContractSigned()) {
				companyName = customerCompanyName;
			}
		}catch(Exception e) {
			String message =  "config item bees360.business.company.name from setting.properties is null";
			logger.error(message, e);
			throw new RuntimeException(message);
		}
		//there is no default company
		if(companyName == null) {
			return null;
		}
		return companyMapper.getByName(companyName);
	}

	@Override
	public Company updateCompany(long companyId, Company company) throws ServiceException {
		Company oldCompany = companyMapper.getById(companyId);
		if(oldCompany == null) {
			throw new ServiceException(MessageCode.COMPANY_NOT_EXISTED);
		}
		if(!StringUtils.equals(oldCompany.getCompanyName(), company.getCompanyName())) {
			// 修改公司，需要判断新的公司名是否已经存在
			if(isCompanyExist(company.getCompanyName())) {
				throw new ServiceException(MessageCode.COMPANY_NAME_HAS_EXISTED);
			}
		}
		company.setCompanyId(companyId);
		// 该接口不修改logo
		// company.setLogo(formatCompanyLogo(company.getLogo(), oldCompany.getLogo()));
		company.setLogo(oldCompany.getLogo());
		company.setUpdatedTime(DateUtil.getNow());
		company.setCompanyName(StringUtils.trimToEmpty(company.getCompanyName()));
		companyMapper.update(company);

		var newCompany = companyMapper.getById(companyId);
        updateCustomer(newCompany);
        return newCompany;
	}

	private String formatCompanyLogo(String newLogo, String oldLogo) {
		if(StringUtils.equals(newLogo, oldLogo)) {
			// logo 没有被修改
			return newLogo;
		}
		// logo 被修改了
		if(newLogo != null && !"".equals(newLogo)) {
			// 前端传入s3 key值
			return newLogo;
		}
		return "";
	}

	@Override
	public PageResult<Company> pageCompanies(CompanySearchOption option) throws ServiceException {
		List<Company> companies = companyMapper.listByOption(option);
		int sum = companyMapper.countByOption(option);
		Pagination pagination = new Pagination(option.getPageIndex(), option.getPageSize(), sum);
		return new PageResult<Company>(companies, pagination);
	}

    @Override
    public Company getByUserId(long userId) {
       var user = userService.getUserById(userId);
       return Optional.ofNullable(user.getCompanyId()).map(this::getById).orElse(null);
    }

    @Override
    public Company updateCompanyLogo(long companyId, String logo) throws ServiceException {
        if(StringUtils.isEmpty(logo)) {
            throw new ServiceException(MessageCode.PARAM_INVALID);
        }
        logo = formatCompanyLogo(logo, "");
        companyMapper.updateLogo(companyId, logo, DateUtil.getNow());

        var company =  companyMapper.getById(companyId);
        updateCustomer(company);
        return company;
    }

    private void updateCustomer(Company company) {
        var customer = Message.CustomerMessage.newBuilder()
            .setId(String.valueOf(company.getCompanyId()))
            .setName(company.getCompanyName())
            .setKey(company.getCompanyKey());
        acceptIfNotNull(customer::setWebsite, company.getWebsite());
        acceptIfNotNull(customer::setLogo, company.getLogo());
        acceptIfNotNull(customer::addAllRole, transformToRoles(company.getCompanyType()));
        customerManager.updateCustomer(Customer.of(customer.build()));
    }

	@Override
	public List<Company> listIn(Set<Long> companyIdUserIn) {
		if(CollectionAssitant.isEmpty(companyIdUserIn)) {
			return new ArrayList<>();
		}
		return companyMapper.listIn(companyIdUserIn);
	}

	@Override
	public boolean isBees360Company(long companyId) throws ServiceException {
		SystemConfigBees360Dto systemConfigBees360Dto = systemConfigService.getSystemConfigBees360();
		return NumberUtils.equals(companyId, systemConfigBees360Dto.getBees360CompanyId());
	}

    @Override
    @Transactional
    public Company updateCompanyKey(long companyId, String companyKey) {
        Preconditions.checkArgument(!StringUtils.isEmpty(companyKey), MessageCode.PARAM_INVALID);
        Company company = companyMapper.getById(companyId);
        Preconditions.checkArgument(company != null, MessageCode.COMPANY_NOT_EXISTED);
        // 如果company key和原本的一样，直接返回
        if (company.getCompanyKey().equals(companyKey)) {
            return company;
        }
        // 如果有数据库冲突，updateCompanyKey返回0，无法对customer进行更新
        if(companyMapper.updateCompanyKey(companyId, companyKey, DateUtil.getNow()) == 0) {
            throw new IllegalArgumentException("Company key already existed, please change the key and try again.");
        }
        company.setCompanyKey(companyKey);
        updateCustomer(company);
        return company;
    }
}
