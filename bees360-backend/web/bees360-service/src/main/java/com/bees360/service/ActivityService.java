package com.bees360.service;

import com.bees360.activity.Activity;
import com.bees360.event.registry.InspectionDueDateChanged;
import com.bees360.event.registry.InspectionScheduledTimeChanged;
import lombok.NonNull;

import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;

public interface ActivityService {
    /**
     * 记录项目状态变更活动
     * @param projectId 项目ID
     * @param creator 触发人
     * @param status 更改后的项目状态
     */
    void projectStatusChanged(long projectId, long creator, @NonNull String status, String comment);

    /**
     * 记录inspection time的变更事件
     * @param projectId 项目ID
     * @param creator 触发人
     * @param inspectionTime 变更后的inspection time
     * @param oldInspectionTime 变更前的inspection time
     */
    void inspectionTimeChanged(long projectId, String creator, @Nullable Long inspectionTime,
                               @Nullable Long oldInspectionTime,
                               @Nullable InspectionScheduledTimeChanged.Reason reason,
                               long updatedTime);

    /**
     * Create record of due date changed event.
     * @param projectId project id
     * @param creator user that update the due date
     * @param dueDate project due date
     * @param oldDueDate existed due date
     */
    void dueDateChanged(long projectId, String creator, @Nullable Long dueDate, @Nullable Long oldDueDate, @Nullable InspectionDueDateChanged.Reason reason);

    /**
     * 记录Batch金额变更事件
     * @param projectId 项目ID
     * @param creator 触发人
     * @param batchNo Batch No.
     * @param amount 变更后的金额
     * @param oldAmount 变更前的金额
     */
    void batchAmountChanged(long projectId, long creator, @NonNull String batchNo,
                            @NonNull BigDecimal amount,
                            @NonNull  BigDecimal oldAmount);

    /**
     * 记录分配飞手事件
     * @param projectId 项目ID
     * @param creator 发配人
     * @param pilotId 飞手ID
     */
    void assignedPilot(long projectId, final long creator, long pilotId);

    /**
     * 记录取消分配事件
     * @param projectId 项目ID
     * @param creator 取消分配人
     * @param pilotId 飞手ID
     */
    void cancelAssignedPilot(long projectId, long creator, long pilotId);

    void policyEffectiveDateChanged(long projectId, String creator,
        @Nullable LocalDate newPolicyEffectiveDate, @Nullable LocalDate oldPolicyEffectiveDate);

    /**
     * 记录飞手接收Batch活动
     * @param projectId 项目ID
     * @param pilotId 飞手ID
     * @param batchNo Batch No.
     */
    void acceptBatch(long projectId, long pilotId,@NonNull String batchNo);

    /**
     * 记录飞手拒绝Batch
     * @param projectId 项目ID
     * @param pilotId 飞手ID
     * @param batchNo Batch No.
     */
    void rejectBatch(long projectId, long pilotId,@NonNull String batchNo, String reason);

    /**
     * 记录成功订阅Plnar服务
     * @param projectId 项目ID
     * @param createdBy 订阅人
     * @param plnarUrl 创建成功后得到的PLNAR URL
     */
    void subscribePlnar(long projectId, long createdBy,@NonNull String plnarUrl);

    /**
     * 记录失败订阅Plnar服务
     * @param projectId 项目ID
     * @param createdBy 订阅人
     */
    void subscribePlnarOnFailed(long projectId, long createdBy);

    /**
     * 记录失败订阅Hover服务
     * @param projectId 项目ID
     * @param createdBy 订阅人
     * @param hoverJobId 创建成功后得到的Hover Job ID
     */
    void subscribeHover(long projectId, long createdBy,@NonNull String hoverJobId);

    /**
     * 记录失败订阅Hover服务
     * @param projectId 项目ID
     * @param createdBy 订阅人
     */
    void subscribeHoverOnFailed(long projectId, long createdBy);

    /**
     * 记录图片上传
     * @param projectId 项目ID
     * @param createdBy 图片上传人
     * @param imageCount 上传图片的张数
     */
    void uploadImages(long projectId, long createdBy, int imageCount);

    /**
     * 异步执行活动记录，失败可重试。
     * 不可重试时会打印错误日志
     * @param activity 要记录的日志
     */
    void execAsyncRetryable(@NonNull Activity activity);

    /**
     * 将comment记录组合成note返回
     * @param projectId
     * @return 返回note
     */
    String getNote(long projectId);
}
