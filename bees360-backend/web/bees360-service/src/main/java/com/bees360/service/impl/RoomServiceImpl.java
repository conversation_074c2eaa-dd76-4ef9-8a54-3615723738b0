package com.bees360.service.impl;

import com.bees360.entity.ProjectImage;
import com.bees360.entity.enums.RoomNameEnum;
import com.bees360.entity.firebase.FirebaseRoom;
import com.bees360.service.RoomService;
import org.springframework.stereotype.Service;

import java.util.Optional;

/** room service */
@Service
public class RoomServiceImpl implements RoomService {

    /** Test com.bees360.service.RoomTest#ImageSetRoomTest() */
    @Override
    public void imageSetRoomFromFirebaseRoom(FirebaseRoom room, ProjectImage image) {
        if (room == null) {
            return;
        }
        // 是object类型，且没有location的进行过滤
        if (room.getRoomId() != null
                && RoomNameEnum.objectIdList().contains(room.getRoomId())
                && room.getLocation() == null) {
            return;
        }

        // 优先读取location为名称
        RoomNameEnum roomNameEnum;
        if (room.getLocation() != null) {
            roomNameEnum = RoomNameEnum.findByRoomName(room.getLocation());
        } else {
            roomNameEnum = RoomNameEnum.findByRoomName(room.getName());
        }

        Optional.ofNullable(roomNameEnum)
                .ifPresent(e -> image.setRoomName(roomNameEnum.getDisplay()));
        image.setFloorLevel(room.getFloorLevel());
        image.setNumber(room.getRoomNumber());
    }
}
