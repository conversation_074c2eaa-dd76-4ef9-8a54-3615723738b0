package com.bees360.service.impl;

import com.bees360.base.exception.ServiceMessageException;
import com.bees360.entity.ReportTemplateCreateParam;
import com.bees360.entity.User;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.vo.ReportTemplateVo;
import com.bees360.mapper.ReportMaterialMapper;
import com.bees360.mapper.ReportTemplateMapper;
import com.bees360.service.PreferenceService;
import com.bees360.service.UserService;
import com.bees360.util.AssertUtil;
import jakarta.inject.Inject;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("preferenceService")
public class PreferenceServiceImpl implements PreferenceService {

	@Inject
	private UserService userService;

	@Inject
	private ReportTemplateMapper reportTemplateMapper;

	@Autowired
    private ReportMaterialMapper reportMaterialMapper;

	@Override
	public String getCustomizedProjectCode(long userId) {
		User user = userService.getUserById(userId);
		if (null != user.getCompanyId() && !user.getCompanyId().equals(0L)) {
			List<ReportTemplateVo> reportTemplateList = reportTemplateMapper.listTemplateByCompanyIdAndReportType(
					user.getCompanyId(), ReportTypeEnum.ROOF_ONLY_UNDERWRITING_REPORT.getCode());
			if (null != reportTemplateList && !reportTemplateList.isEmpty()) {
				return reportTemplateList.get(0).getDataSource();
			}
		}
		return null;
	}

    @Override
    public void addProjectTemplate(long userId, ReportTemplateCreateParam param) throws ServiceMessageException {
	    AssertUtil.notNull(param.getCompanyId(), "companyId is null");
        reportMaterialMapper.insert(param);
        param.setMaterialId(param.getId());
        reportTemplateMapper.insert(param);
    }

    @Override
    public void addCompanyTemplates(List<ReportTemplateCreateParam> params) {
        reportTemplateMapper.insertBatch(params);
    }

    @Override
    public void deleteProjectTemplate(long userId, long templateId) {
        reportTemplateMapper.deleteProjectTemplate(templateId);
    }

    @Override
    public List<ReportTemplateVo> listCompanyTemplates(long companyId, int reportType) {
        return reportTemplateMapper.listTemplateByCompanyIdAndReportType(companyId, reportType);
    }
}
