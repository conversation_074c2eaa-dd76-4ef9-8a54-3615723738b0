package com.bees360.service.impl;

import com.bees360.entity.SystemConfig;
import com.bees360.entity.dto.systemconfig.SystemConfigBees360Dto;
import com.bees360.entity.dto.systemconfig.SystemConfigProjectDto;
import com.bees360.entity.dto.systemconfig.SystemConfigProjectServiceChangeDto;
import com.bees360.entity.util.SystemConfigPrefix;
import com.bees360.mapper.SystemConfigMapper;
import com.bees360.service.SystemConfigService;
import com.bees360.util.SystemConfigConvertorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.thymeleaf.util.StringUtils;

import jakarta.inject.Inject;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Guanrong
 * @date 2019/12/23 19:52
 */
@Slf4j
@Service
public class SystemConfigServiceImpl implements SystemConfigService {

    @Inject
    private SystemConfigMapper systemConfigMapper;

    @Override
    public List<SystemConfig> listAll() {
        return systemConfigMapper.listAll();
    }

    @Override
    public List<SystemConfig> listWithPrefix(String prefix) {
        if (StringUtils.isEmpty(prefix)) {
            return systemConfigMapper.listAll();
        }
        return systemConfigMapper.listWithPrefix(prefix);
    }

    @Override
    public SystemConfigProjectDto getSystemConfigProject() {
        return fetchSystemConfigDto(SystemConfigProjectDto.class);
    }

    @Override
    public SystemConfigProjectDto updateSystemConfigProject(SystemConfigProjectDto systemConfigProjectDto) {
        updateSystemConfig(systemConfigProjectDto);
        return getSystemConfigProject();
    }

    @Override
    public SystemConfigBees360Dto getSystemConfigBees360() {
        return fetchSystemConfigDto(SystemConfigBees360Dto.class);
    }

    @Override
    public SystemConfigBees360Dto updateSystemConfigBees360(SystemConfigBees360Dto systemConfigBees360Dto) {
        updateSystemConfig(systemConfigBees360Dto);
        return getSystemConfigBees360();
    }

    @Override
    public Optional<SystemConfigProjectServiceChangeDto> getSystemConfigProjectServiceChange() {
        SystemConfig config = systemConfigMapper.getOne(SystemConfigProjectServiceChangeDto.KEY_PREFIX);
        if (config == null) {
           return Optional.empty();
        }
        return Optional.ofNullable(SystemConfigConvertorUtil.fromJsonType(config, SystemConfigProjectServiceChangeDto.class));
    }

    @Override
    public SystemConfigProjectServiceChangeDto upsertSystemConfigProjectServiceChange(
        SystemConfigProjectServiceChangeDto serviceChangeDto) {
        SystemConfig systemConfig = SystemConfigConvertorUtil.toJsonType(serviceChangeDto);
        if (systemConfigMapper.getOne(systemConfig.getConfigKey()) == null) {
            systemConfigMapper.insert(systemConfig);
        } else {
            systemConfigMapper.update(systemConfig);
        }
        return getSystemConfigProjectServiceChange().orElse(null);
    }

    public <T extends SystemConfigPrefix> T fetchSystemConfigDto(Class<T> systemConfigClass) {
        List<SystemConfig> systemConfigs = listAll();
        return SystemConfigConvertorUtil.toSystemConfigDto(systemConfigs, systemConfigClass);
    }

    @Override
    public <T extends SystemConfigPrefix> void updateSystemConfig(T systemConfigObject) {
        List<SystemConfig> systemConfigs = SystemConfigConvertorUtil.toSystemConfigs(systemConfigObject);
        systemConfigMapper.updateBatch(systemConfigs);
    }

    @Override
    public void upsertSystemConfig(SystemConfig config) {
        final SystemConfig configFound = systemConfigMapper.getOne(config.getConfigKey());
        if (configFound == null) {
            systemConfigMapper.insert(config);
        } else {
            systemConfigMapper.update(config);
        }
    }
}
