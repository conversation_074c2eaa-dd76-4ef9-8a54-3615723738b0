package com.bees360.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Permission;
import com.bees360.service.PermissionCacheService;
import com.bees360.service.PermissionService;
import com.bees360.util.CacheKeys;
import com.bees360.util.RedisUtil;

@Service("permissionCacheService")
public class PermissionCacheServiceImpl implements PermissionCacheService{

	private static final Logger logger = LoggerFactory.getLogger(PermissionCacheServiceImpl.class);

	@Autowired
	RedisUtil redisUtil;

	@Inject
	private PermissionService permissionService;


	@Override
	public List<Permission> getPermissions() throws ServiceException {
		List<Permission> permissions = null;
		try {
			Object permissionObject = redisUtil.get(CacheKeys.REDIS_KEY_USER_PERMISSIONS);
			if(null == permissionObject) {
				permissions = permissionService.getAllPermissions();
				redisUtil.set(CacheKeys.REDIS_KEY_USER_PERMISSIONS, permissionsToJson(permissions));
			}else {
				permissions = jsonToPermissions(permissionObject.toString());
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return permissions;
	}

	@Override
	public Map<Long, Permission> getPermissionMap() throws ServiceException {
		List<Permission> permissions = getPermissions();
		if(permissions == null) {
			return null;
		}
		return permissions.stream().map(p -> p).collect(Collectors.toMap(Permission::getPermissionId, c -> c));
	}

	private String permissionsToJson(List<Permission> permissions) {
		if(permissions == null || permissions.size() == 0) {
			return null;
		}
		return JSONObject.toJSONString(permissions);
	}

	private List<Permission> jsonToPermissions(String json){
		if(json == null || "".equals(json)) {
			return null;
		}
		try {
			return JSONObject.parseArray(json, Permission.class);
		}catch(Exception e) {
			logger.error("convert json string to List<Permission> failed,", e);
		}
		return null;
	}
}
