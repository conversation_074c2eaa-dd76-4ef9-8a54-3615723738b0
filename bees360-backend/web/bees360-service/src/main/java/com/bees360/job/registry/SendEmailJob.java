package com.bees360.job.registry;

import com.google.gson.JsonObject;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@JobPayload
public class SendEmailJob {

    private String sender;

    private String template;
    private List<String> recipients;
    // json array
    private List<String> subjectData;
    // json string
    private JsonObject templateData;
}
