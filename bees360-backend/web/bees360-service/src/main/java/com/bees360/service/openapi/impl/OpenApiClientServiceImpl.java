package com.bees360.service.openapi.impl;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Company;
import com.bees360.entity.User;
import com.bees360.entity.enums.openapi.GrantType;
import com.bees360.entity.openapi.client.OpenApiClient;
import com.bees360.entity.openapi.client.OpenApiClientSecret;
import com.bees360.entity.openapi.client.OpenApiClientUpdateDto;
import com.bees360.entity.openapi.client.OpenApiClientVo;
import com.bees360.entity.openapi.client.OpenApiSearchFilterOption;
import com.bees360.mapper.openapi.OpenApiClientMapper;
import com.bees360.service.CompanyService;
import com.bees360.service.UserService;
import com.bees360.service.openapi.OpenApiClientService;
import com.bees360.service.openapi.factory.ClientIdGenerator;
import com.bees360.service.openapi.factory.OpenApiClientSecretFactory;
import com.bees360.util.password.PasswordUtil;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Service
public class OpenApiClientServiceImpl implements OpenApiClientService {

    @Autowired
    private OpenApiClientMapper clientMapper;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private UserService userService;

    @Autowired
    private OpenApiClientSecretFactory clientSecretFactory;

    @Override
    public OpenApiClientVo createClient(long userId, OpenApiClient client) {

        client.setAddBy(userId);
        client.setUpdateBy(userId);
        client.setCreateAt(System.currentTimeMillis());
        client.setUpdateAt(System.currentTimeMillis());
        clientMapper.insert(client);

        return OpenApiClientVo.fromClient(client);

    }

    @Override
    public OpenApiClientVo assignClient(long userId, String clientName) throws ServiceException {

        final User user = userService.getUserById(userId);

        OpenApiClient client = defaultApiClient(clientName, user);

        return createClient(userId, client);
    }

    @Override
    public OpenApiClientVo update(long userId, String clientId, OpenApiClientUpdateDto updateDto) throws ServiceException {

        final OpenApiClient client = clientMapper.getByClientId(clientId);
        if (client == null){
            throw new ServiceException(MessageCode.PARAM_INVALID);
        }
        clientMapper.update(buildUpdateApiClient(userId, client.getId(), updateDto));

        return OpenApiClientVo.fromClient(clientMapper.getById(client.getId()));
    }

    private OpenApiClient buildUpdateApiClient(long userId, long id, OpenApiClientUpdateDto updateDto){
        final OpenApiClient client = new OpenApiClient();
        client.setId(id);
        BeanUtils.copyProperties(updateDto, client);
        client.setUpdateBy(userId);
        client.setUpdateAt(System.currentTimeMillis());

        return client;
    }

    @Override
    public void delete(long id) {
        clientMapper.delete(id);
    }

    @Override
    public void deleteByClientId(String clientId) {
        clientMapper.deleteByClientId(clientId);
    }

    @Override
    public OpenApiClient getByClientId(String clientId) {
        return clientMapper.getByClientId(clientId);
    }

    @Override
    public List<OpenApiClient> listClient(OpenApiSearchFilterOption search) throws ServiceException {

        return clientMapper.search(search);
    }

    private OpenApiClient defaultApiClient(String clientName, User user){

        final OpenApiClientSecret clientSecret = clientSecretFactory.getClientSecret(
            clientName, createScopes(user),
            new HashSet<>(Arrays.asList(GrantType.refresh_token, GrantType.password)));

        final OpenApiClient client = clientSecret.toOpenApiClient();
        client.setCompanyId(user.getCompanyId());

        return client;
    }

    private Set<String> createScopes(User user) {
        String scope = user.getCompanyName().trim().replaceAll("[\\s,\\,]+", "_");
        return Collections.singleton(scope);
    }
}
