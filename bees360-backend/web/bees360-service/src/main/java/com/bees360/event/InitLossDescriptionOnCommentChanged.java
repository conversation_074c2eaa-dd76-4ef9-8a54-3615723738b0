package com.bees360.event;

import com.bees360.event.registry.CommentChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.firebase.FirebaseProjectService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听评论变更事件并初始化项目损失描述
 */
@Log4j2
public class InitLossDescriptionOnCommentChanged extends AbstractNamedEventListener<CommentChangedEvent> {

    private final FirebaseProjectService firebaseProjectService;

    private final ProjectMapper projectMapper;

    public InitLossDescriptionOnCommentChanged(FirebaseProjectService firebaseProjectService, ProjectMapper projectMapper) {
        this.firebaseProjectService = firebaseProjectService;
        this.projectMapper = projectMapper;
        log.info("create {}, FirebaseProjectService {}, ProjectMapper {}", this, firebaseProjectService, projectMapper);
    }


    @Override
    public void handle(CommentChangedEvent commentChangedEvent) throws IOException {
        var comment = commentChangedEvent.getSource();
        var projectId = comment.getProjectId();
        var project = projectMapper.getById(projectId);

        // case还没被创建出来
        if (project == null) {
            return;
        }
        projectId = commentChangedEvent.getSource().getProjectId();
        log.info("Start to init lossDescription for project '{}' on comment {} added.", projectId, comment);
        firebaseProjectService.initLossDescription(project, comment);
    }
}
