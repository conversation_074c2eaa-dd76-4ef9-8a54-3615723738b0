package com.bees360.service.job;

import com.bees360.codec.GsonCodec;
import com.bees360.job.util.AbstractJob;
import com.google.protobuf.ByteString;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
public class ProjectReturnToClientJob extends AbstractJob {

    private final static GsonCodec codec = new GsonCodec();
    @Getter
    private long userId;
    @Getter
    private long projectId;

    public ProjectReturnToClientJob(long userId, long projectId) {
        this.userId = userId;
        this.projectId = projectId;
    }

    public static ProjectReturnToClientJob decode(ByteString byteString) {
        return codec.decode(byteString, ProjectReturnToClientJob.class);
    }

    public ByteString encode() {
        return codec.encode(this);
    }

    @Override
    public String getId() {
        return projectId + "";
    }

    @Override
    public String getName() {
        return ProjectReturnToClientJobExecutor.EXECUTOR_NAME;
    }

    @Override
    public @NonNull ByteString getPayload() {
        return encode();
    }
}
