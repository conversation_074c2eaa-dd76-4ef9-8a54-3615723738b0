package com.bees360.service.event.project;

import com.bees360.service.event.Bees360Event;

public class ProjectImageUploadEvent extends Bees360Event {

    private long projectId;

    public ProjectImageUploadEvent(Object source, long projectId) {
        super(source);
        this.projectId = projectId;
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }

    public long getProjectId() {
        return this.projectId;
    }
}
