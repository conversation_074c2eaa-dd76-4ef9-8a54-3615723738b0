package com.bees360.service.event.message;

import com.bees360.base.exception.ServiceException;
import com.bees360.service.MessageService;
import com.bees360.service.event.Bees360EventHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 */
@Component
public class EmailSendEventHandler implements Bees360EventHandler<EmailSendEvent> {

    private static final Logger log = LoggerFactory.getLogger(EmailSendEventHandler.class);

    @Autowired
    private MessageService messageService;

    /**
     * send email to system admin
     *
     * @param event
     */
    @Override
    public void onApplicationEvent(EmailSendEvent event) {
        Exception e = (Exception) event.getSource();
        try {
            messageService.sendErrorToEngineer(
                "EmailSendEventHandler was trigged, there is an error occur in bees360-web system", e);
        } catch (ServiceException ex) {
            log.error("send email failed", ex);
        }
    }
}
