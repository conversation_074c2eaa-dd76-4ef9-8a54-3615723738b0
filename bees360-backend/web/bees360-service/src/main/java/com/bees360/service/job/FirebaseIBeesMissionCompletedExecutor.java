package com.bees360.service.job;

import com.bees360.job.registry.FirebaseIBeesMissionCompleted;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseMissionService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;

import static com.bees360.service.job.FirebaseMissionChangedExecutor.translateExceptionAndThrow;

@Log4j2
@Component
public class FirebaseIBeesMissionCompletedExecutor
        extends AbstractJobExecutor<FirebaseIBeesMissionCompleted> {

    private final FirebaseMissionService firebaseMissionService;

    public FirebaseIBeesMissionCompletedExecutor(FirebaseMissionService firebaseMissionService) {
        this.firebaseMissionService = firebaseMissionService;

        log.info("Created '{}(firebaseMissionService={})", this, this.firebaseMissionService);
    }

    @Override
    protected void handle(FirebaseIBeesMissionCompleted mission) throws IOException {
        try {
            firebaseMissionService.handleIBeesMissionCompleted(
                    mission.getMissionPath(),
                    Long.parseLong(mission.getProjectId()));
            log.info("Successfully handle completed IBees mission '{}'.", mission);
        } catch (RuntimeException e) {
            log.warn("Failed to handle ibees mission '{}'", mission, e);
            translateExceptionAndThrow(e);
        }
    }
}
