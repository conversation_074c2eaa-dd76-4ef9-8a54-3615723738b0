package com.bees360.service.listener.message;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.User;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.service.ContextProvider;
import com.bees360.service.NotificationService;
import com.bees360.service.UserService;
import com.bees360.web.event.project.ProjectDeletedEvent;
import com.bees360.web.event.project.ProjectEvent;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2020/04/16 10:54
 */
@Slf4j
@Component
public class NotificationListener {

    @Autowired
    private UserService userService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private ContextProvider springSecurityContextProvider;

    @Transactional(rollbackFor = Exception.class)
    @EventListener
    public void notifyMembersOnProjectDeleted(ProjectDeletedEvent event) throws ServiceException {
        long userId =
            Optional.of(springSecurityContextProvider.getUserIdFromContext())
                .filter(l -> l != 0)
                .orElse(event.getOperator());
        logInfo(event, userId, "notify the members of the project");

        Project project = event.getProject();

        List<UserTinyVo> members = userService.listActiveMemberInProject(project.getProjectId());
        User user = userService.getUserById(userId);

        UserTinyVo operator = new UserTinyVo(user, null);
        for(UserTinyVo m: members) {
            notificationService.notifyProjectDeleted(project, operator, m);
        }
    }

    private void logInfo(ProjectEvent event, long userId, String message) {
        String sourceName = event.getSource().getClass().getName();
        String eventName = event.getClass().getSimpleName();
        long projectId = event.getProject().getProjectId();

        log.info("Event(name:{}, source: {}, project: {}) triggered by {}: {}", eventName, sourceName, projectId,
            userId, message);
    }
}
