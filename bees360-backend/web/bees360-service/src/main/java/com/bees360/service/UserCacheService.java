package com.bees360.service;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.User;

public interface UserCacheService {

	/**
	 * putting user into redis after login,and setting cache expiration time
	 * @param userId key
	 * @param user   value
	 * @param timeout millisencond
	 * @throws ServiceException
	 */
	public void setUser(long userId, User user, long timeout) throws ServiceException;


	/**
	 * get user object from cache
	 * @param userId
	 * @return
	 * @throws ServiceException
	 */
	public User getUser(long userId) throws ServiceException;

	/**
	 * delete User from cache
	 * @param userId
	 * @return
	 * @throws ServiceException
	 */
	public void deleteUser(long userId) throws ServiceException;


	public void setSecurityCodeInfo(String account, String securityCode, long availableTime, long timeout) throws ServiceException;

	public String getSecurityCode(String account) throws ServiceException;

	public long getSecurityCodeAvailableTime(String account) throws ServiceException;


	public boolean hasAccount(String account) throws ServiceException;

	public void deleteAccount(String account) throws ServiceException;
}
