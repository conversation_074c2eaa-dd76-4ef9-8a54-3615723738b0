package com.bees360.service.listener.project;

import com.bees360.entity.enums.CreationChannelType;
import com.bees360.mapper.ProjectCreationMapper;
import com.bees360.web.event.project.ProjectCreatedEvent;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SaveCreationChannelOnProjectCreated {

    private final ProjectCreationMapper projectCreationMapper;

    @EventListener
    public void saveProjectCreationChannel(ProjectCreatedEvent event) {

        long projectId = event.getProject().getProjectId();
        String creationChannel = Optional.ofNullable(event.getCreationChannel()).map(CreationChannelType::name)
            .orElse("");
        projectCreationMapper.insert(projectId, creationChannel);
    }
}
