package com.bees360.service;

import com.bees360.entity.enums.ImagePartialViewTypeEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.entity.vo.ImageIdsVo;
import java.util.List;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.ProjectImage;
import com.bees360.entity.dto.*;
import com.bees360.entity.enums.FileSourceTypeEnum;
import com.bees360.entity.enums.ImageTypeEnum;
import com.bees360.entity.vo.ProjectImagePageResult;

/**
 *
 * <AUTHOR>
 * @contributors
 * @date 2017/12/19 11:49:53
 */
public interface ProjectImageService {
	/**
	 * It will not only delete the specified image, but also the data related with the image.
	 * @param imageId
	 * @param projectId
	 * @throws ServiceException
	 */
	void deleteById(String imageId, long projectId, long userId) throws ServiceException;

    void deleteCompletely(long projectId, long userId, ImageIdsVo imageIds) throws ServiceException;

    void deleteCompletely(
            long projectId, long userId, ImageIdsVo imageIds, SystemTypeEnum systemType)
            throws ServiceException;

    List<String> listImageKeysByProjectIdForArchive(long projectId) throws ServiceException;

	List<ProjectImage> listAll(long projectId);

	List<ProjectImage> listImageByImageType(long projectId,
			FileSourceTypeEnum fileSourceType, ImageTypeEnum imageType) throws ServiceException;

    /** 是否存在相应partial类型的图片 */
    boolean existByPartialType(long projectId, List<ImagePartialViewTypeEnum> types);

	ProjectImagePageResult listTinyImagesWithPage(ProjectImageSearchOptionDto queryParameter) throws ServiceException;

	void updateImageDeletedStatus(long projectId, List<String> imageIds, boolean deleted);

	ProjectImageUploadResponseDto uploadImages(long projectId, long userId,
			FileSourceTypeEnum sourceType, List<ProjectImage> images, Boolean isUploaded) throws ServiceException;

    /**
     * 从firebase的 cloud store 同步图片到web,
     * 如果此次有新上传的照片返回true
     * @param projectId
     * @param userId
     * @param images
     * @throws ServiceException
     */
    List<ProjectImage> uploadImagesFromFirebase(long projectId, long userId, List<ProjectImage> images, boolean addActivity) throws ServiceException;

    int countImages(long projectId, Integer fileSourceType, boolean isDeleted) ;

    ProjectImage getImageById(String imageId);

    List<ProjectImage> getNewImagesAndUpdateOldImages(
            long projectId, List<ProjectImage> uploadImages);
}
