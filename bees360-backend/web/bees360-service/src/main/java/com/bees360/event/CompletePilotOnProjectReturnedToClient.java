package com.bees360.event;

import com.bees360.event.registry.ProjectReturnedToClientEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.beespilot.BeesPilotStatusService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听项目返回客户端事件并完成蜜蜂试点状态更新
 */
@Log4j2
public class CompletePilotOnProjectReturnedToClient extends AbstractNamedEventListener<ProjectReturnedToClientEvent> {

    private final BeesPilotStatusService beesPilotStatusService;

    public CompletePilotOnProjectReturnedToClient(BeesPilotStatusService beesPilotStatusService) {
        this.beesPilotStatusService = beesPilotStatusService;
    }

    @Override
    public void handle(ProjectReturnedToClientEvent event) throws IOException {
        log.info("Listening event {} for complete bees pilot status.", event);
        var projectId = Long.parseLong(event.getProjectId());
        beesPilotStatusService.pilotCompleted(projectId);
    }
}
