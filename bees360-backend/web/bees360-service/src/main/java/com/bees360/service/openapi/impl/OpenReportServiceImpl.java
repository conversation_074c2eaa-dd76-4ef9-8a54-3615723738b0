package com.bees360.service.openapi.impl;

import com.bees360.base.exception.ResourceNotFoundException;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.ReportSummary;
import com.bees360.entity.dto.ProjectStatusTimeLineDto;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.entity.openapi.OpenProjectReportSummaryVo;
import com.bees360.entity.openapi.OpenReportSummaryVo;
import com.bees360.entity.openapi.reportsummary.ReportSummaryVo;
import com.bees360.entity.vo.ProjectStatusVo;
import com.bees360.policy.Policy;
import com.bees360.project.ProjectIIManager;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.ReportSummaryService;
import com.bees360.service.ProjectService;
import com.bees360.service.openapi.OpenReportService;
import com.bees360.service.openapi.converter.ProjectConverter;
import com.bees360.service.openapi.converter.ReportSummaryConvert;
import com.bees360.util.Iterables;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Yang
 */
@Service
public class OpenReportServiceImpl implements OpenReportService {

    @Autowired
    private ReportSummaryService reportSummaryService;

    @Autowired
    private ProjectReportFileService projectReportFileService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectStatusService projectStatusService;

    @Autowired
    private ProjectIIManager projectIIManager;

    @Override
    public OpenReportSummaryVo getReportSummary(String reportId) throws ServiceException {
        // 判断报告是否存在
        ProjectReportFile report = projectReportFileService.getById(reportId);
        if(report == null) {
            throw new ResourceNotFoundException("report `" + reportId + "` not found.");
        }
        Project project = projectService.getById(report.getProjectId());
        long projectId = project.getProjectId();

        var projectII = projectIIManager.findById(String.valueOf(projectId));
        project.setSupplementalServices(
            Optional.ofNullable(projectII.getSupplementalService()).map(Iterables::toList).orElse(List.of()));
        Optional.ofNullable(projectII.getPolicy())
                .map(Policy::isRenewal)
                .ifPresent(project::setIsRenewal);

        ProjectStatusTimeLineDto timeline = projectStatusService.getProjectStatusTimeLine(projectId);
        Long inspectionTime = getInspectionTime(timeline);
        Long completionTime = getCompletionTime(timeline);
        OpenProjectReportSummaryVo summaryProject = ProjectConverter.toOpenProjectReportSummaryVo(project,
            inspectionTime, completionTime);

        ReportSummaryVo summaryVo = null;
        ReportSummary projectReportSummary = reportSummaryService.getOne(report.getProjectId(),
            report.getReportType());
        if(projectReportSummary != null && !ObjectUtils.isEmpty(projectReportSummary.getSummary())) {
            summaryVo = ReportSummaryConvert.toSummaryVo(projectReportSummary.getSummary());
        }

        OpenReportSummaryVo reportSummary = new OpenReportSummaryVo();
        reportSummary.setId(report.getReportId());
        reportSummary.setType(ReportTypeEnum.getEnum(report.getReportType()).getDisplay());
        reportSummary.setProject(summaryProject);
        reportSummary.setSummary(summaryVo);

        return reportSummary;
    }

    private Long getInspectionTime(ProjectStatusTimeLineDto timeline) throws ServiceException {
        ProjectStatusVo statusVo = timeline.getSiteInspected();
        return statusVo == null? null: statusVo.getCreatedTime();
    }

    private Long getCompletionTime(ProjectStatusTimeLineDto timeline) throws ServiceException {
        ProjectStatusVo statusVo = timeline.getReturnToClient();
        return statusVo == null? null: statusVo.getCreatedTime();
    }
}
