package com.bees360.job;

import com.bees360.base.exception.ServiceException;
import com.bees360.job.registry.SyncImageFromFirebaseJob;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseMissionService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * This class is for fetching images from image collection of firebase mission or ibees mission.
 */
@Log4j2
public class SyncImageFromFirebaseJobExecutor extends AbstractJobExecutor<SyncImageFromFirebaseJob> {

    private final FirebaseMissionService firebaseMissionService;

    private final FirebaseService firebaseService;

    private final Bees360FeatureSwitch featureSwitch;

    public SyncImageFromFirebaseJobExecutor(FirebaseMissionService firebaseMissionService,
                                            FirebaseService firebaseService,
                                            Bees360FeatureSwitch featureSwitch) {
        this.firebaseMissionService = firebaseMissionService;
        this.firebaseService = firebaseService;
        this.featureSwitch = featureSwitch;
        log.info("Created {}.", this);
    }


    @Override
    protected void handle(SyncImageFromFirebaseJob job) throws IOException {
        log.info("Received job {}.", job);
        var missionPath = job.getMissionPath();
        var projectId = Long.parseLong(job.getProjectId());

        try {
            if (isIbeesMission(missionPath)) {
                handleIbeesMissionImage(missionPath, projectId);
                return;
            }

            if (featureSwitch.isDisableHandleMissionImage()){
                var pilotId = firebaseService.toWebUserId(job.getPilotId());
                firebaseMissionService.handleMissionImage(missionPath, pilotId, projectId);
                log.info("Successfully handle mission {} image for project {}.", missionPath, projectId);
            }

        } catch (ServiceException e) {
            throw new IllegalStateException(
                "Fail to sync image from firebase: project id %s with mission %s.".formatted(
                    projectId, missionPath), e);
        }
    }

    private void handleIbeesMissionImage(String missionPath, Long projectId) {
        if (featureSwitch.isDisableHandleIbeesMissionImage()) {
            firebaseMissionService.handleIBeesMissionImage(missionPath, projectId);
            log.info("Successfully handle ibees mission {} image for project {}.",
                missionPath, projectId);
        }
    }

    private boolean isIbeesMission(String missionPath) {
        return missionPath.startsWith("ibees_mission");
    }
}
