package com.bees360.service.util;

import com.bees360.entity.enums.ProjectLabelEnum;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

@Configuration
@Log4j2
public class ProjectCloseConfig {
    @Data
    static class CloseOutProperty {
        private ProjectLabelEnum operationTag;
        private String closeReason;
        private String reasonInCloseOutReport;
    }

    @Bean
    @ConfigurationProperties(prefix = "project.close-out.property")
    public List<CloseOutProperty> closeProperties() {
        return new ArrayList<>();
    }

    @Bean
    @ConditionalOnProperty(prefix = "project.close-out", name = "disabled", havingValue = "false")
    public Function<String, Long> closeReasonToOperationTagIdConverter(
            List<CloseOutProperty> closeProperties) {
        log.info("CloseReasonToOperationTagIdConverter from properties :{}.", closeProperties);
        return closeReason ->
                closeProperties.stream()
                        .filter(p -> Objects.equals(p.getCloseReason(), closeReason))
                        .findFirst()
                        .map(CloseOutProperty::getOperationTag)
                        .map(ProjectLabelEnum::getLabelId)
                        .orElse(null);
    }
}
