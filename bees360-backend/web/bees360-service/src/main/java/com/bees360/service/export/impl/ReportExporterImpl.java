package com.bees360.service.export.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;

import com.google.api.client.util.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ReportGenerationStatusEnum;
import com.bees360.entity.enums.ReportTypeEnum;
import com.bees360.service.ProjectReportFileService;
import com.bees360.service.ProjectService;
import com.bees360.service.export.ExportableDocument;
import com.bees360.service.export.ReportExporter;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
@RequiredArgsConstructor
public class ReportExporterImpl implements ReportExporter {

    public final String TAG_COMPRESSED = "COMPRESSED";
    private final ProjectService projectService;
    private final ProjectReportFileService projectReportFileService;

    /**
     * 导出报告文件，按照默认规则进行过滤，按照默认方式命名
     *
     * @param projectId
     * @return
     * @throws ServiceException
     */
    @Override
    public List<ExportableDocument> collectReports(long projectId) {
        return collectReports(projectId, null);
    }

    /**
     * 导出报告文件，按照默认规则进行过滤
     *
     * @param projectId
     * @param customFileName
     *            报告文件重命名
     * @return
     * @throws ServiceException
     */
    @Override
    public List<ExportableDocument> collectReports(long projectId,
        BiFunction<Project, ProjectReportFile, String> customFileName) {
        Project project = getProject(projectId);
        ProjectServiceTypeEnum serviceTypeEnum = ProjectServiceTypeEnum.getEnum(project.getServiceType());
        if (serviceTypeEnum == null) {
            log.warn("Project({}): Empty list will be the result, since the service type is unknown.");
            return Lists.newArrayList();
        }
        Set<Integer> reportTypes =
            serviceTypeEnum.getReportTypes().stream().map(ReportTypeEnum::getCode).collect(Collectors.toSet());
        return collectReports(projectId, r -> {
            return reportTypes.contains(r.getReportType())
                && ReportGenerationStatusEnum.getEnum(r.getGenerationStatus()).isCompleted();
        }, customFileName);
    }

    /**
     * 导出报告文件
     *
     * @param projectId
     * @param filter
     *            对结果进行过滤
     * @param customFileName
     *            自定义文件名
     * @return
     */
    @Override
    public List<ExportableDocument> collectReports(long projectId, Predicate<ProjectReportFile> filter,
        @Nullable BiFunction<Project, ProjectReportFile, String> customFileName) {
        Project project = getProject(projectId);
        List<ProjectReportFile> reports = projectReportFileService.getAllReportFiles(projectId);
        return reports.stream().filter(filter).map(r -> toDocument(project, r, true, customFileName))
            .collect(Collectors.toList());
    }

    private Project getProject(long projectId) {
        return projectService.getById(projectId);
    }

    private ExportableDocument toDocument(Project project, ProjectReportFile reportFile,
        boolean selectCompressedIfExist, @Nullable BiFunction<Project, ProjectReportFile, String> customFileName) {
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportFile.getReportType());

        String filename =
            (customFileName == null ? createFileName(project, reportFile) : customFileName.apply(project, reportFile));

        ExportableDocument doc = new ExportableDocument()
            .setId(reportFile.getReportId())
            .setType(ExportableDocument.TYPE_REPORT)
            .setFileName(filename)
            .setDescription("")
            .setCreateTime(reportFile.getCreatedTime());

        if (selectCompressedIfExist && StringUtils.isNotEmpty(reportFile.getReportPdfCompressed())) {
            // 压缩文件独立做一个Document
            String compressReport = reportFile.getReportPdfCompressed();

            // @formatter:off
            doc.setOriginalName(getOriginalFileNameFromKey(compressReport))
                .setTags(Arrays.asList(reportType.name(), TAG_COMPRESSED))
                .setSize(reportFile.getSizeCompressed())
                .setResourceSrc(compressReport);
            // @formatter:on
        } else {
            // @formatter:off
            doc.setOriginalName(getOriginalFileNameFromKey(reportFile.getReportPdfFileName()))
                .setTags(Arrays.asList(reportType.name()))
                .setSize(reportFile.getSize())
                .setResourceSrc(reportFile.getReportPdfFileName());
            // @formatter:on
        }
        return doc;
    }

    private String getOriginalFileNameFromKey(String key) {
        return StringUtils.substringAfterLast(key, "/");
    }

    private String createFileName(Project project, ProjectReportFile reportFile) {
        String inspectionNumber = project.getInspectionNumber();
        ReportTypeEnum reportType = ReportTypeEnum.getEnum(reportFile.getReportType());
        String fullAddress = project.getFullAddress();
        String extension = reportType.getExtension();
        // SWYCBHO01547 - Premium Measurement Report - 24506 Lake Path Circle, Katy, TX.pdf
        return "%s - %s - %s.%s".formatted(inspectionNumber, reportType.getDisplay(), fullAddress, extension);
    }

}
