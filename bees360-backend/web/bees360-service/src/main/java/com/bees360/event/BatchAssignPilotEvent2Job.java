package com.bees360.event;

import com.bees360.event.registry.BatchAssignPilotEvent;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.BatchSchedulePilotJob;
import com.bees360.job.util.EventTriggeredJob;

import java.time.Duration;

/**
 * 将BatchAssignPilotEvent事件转换为可重试的BatchSchedulePilotJob任务
 */
public class BatchAssignPilotEvent2Job extends EventTriggeredJob<BatchAssignPilotEvent> {
    public BatchAssignPilotEvent2Job(JobScheduler jobScheduler) {
        super(jobScheduler);
    }

    private static final Integer RETRY_COUNT = 5;
    private static final Duration RETRY_DELAY = Duration.ofSeconds(1);
    private static final Float RETRY_DELAY_INCREASE_FACTOR = 2F;

    @Override
    protected Job convert(BatchAssignPilotEvent event) {
        var job = new BatchSchedulePilotJob();
        job.setPilotId(event.getPilotId());
        job.setProjectIds(event.getProjectIds());
        job.setOperationUserId(event.getOperationUserId());
        job.setInspectionTime(event.getInspectionTime());
        job.setPilotAccepted(event.getIsPilotAccepted());
        job.setVersion(event.getOperationTime());
        job.setAssignment(true);
        return RetryableJob.of(
                Job.ofPayload(job), RETRY_COUNT, RETRY_DELAY, RETRY_DELAY_INCREASE_FACTOR);
    }
}
