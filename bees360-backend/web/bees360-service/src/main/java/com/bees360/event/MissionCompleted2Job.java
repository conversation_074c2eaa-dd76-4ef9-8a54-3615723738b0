package com.bees360.event;

import com.bees360.event.registry.MissionCompletedEvent;
import com.bees360.firebase.FirebaseApi;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.FirebaseMissionCompleted;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.SerializableFirebaseMission;
import com.bees360.job.util.EventTriggeredJob;
import com.google.cloud.firestore.Firestore;
import com.google.common.base.Preconditions;
import lombok.extern.log4j.Log4j2;

import java.time.Duration;
import java.util.Objects;

import static com.bees360.service.firebase.FirebaseService.MISSION_COLLECTION;

/**
 * 处理任务完成事件，将事件转换为可重试的作业并过滤无效任务。
 */
@Log4j2
public class MissionCompleted2Job extends EventTriggeredJob<MissionCompletedEvent> {
    private final Firestore firestore;
    private static final Integer RETRY_COUNT = 5;
    private static final Duration RETRY_DELAY = Duration.ofSeconds(1);
    private static final Float RETRY_DELAY_INCREASE_FACTOR = 2F;

    public MissionCompleted2Job(JobScheduler jobScheduler, Firestore firestore) {
        super(jobScheduler);
        this.firestore = firestore;
    }

    @Override
    protected Job convert(MissionCompletedEvent event) {
        var missionId = event.getMissionId();
        var pilotId = event.getPilotId();
        var projectId = event.getProjectId();
        var ref = firestore.collection(MISSION_COLLECTION).document(missionId);

        var mission = FirebaseApi.fetch(ref.get()).toObject(SerializableFirebaseMission.class);
        Preconditions.checkState(
                Objects.nonNull(mission), "Mission %s not found.".formatted(missionId));
        mission.setId(missionId);
        mission.setPilotId(pilotId);

        FirebaseMissionCompleted missionJob = new FirebaseMissionCompleted();
        missionJob.setProjectId(projectId);
        missionJob.setPilotId(pilotId);
        missionJob.setMissionPath(ref.getPath());
        missionJob.setMission(mission);
        missionJob.setCompletedBy(event.getCompletedBy());
        missionJob.setCompletedAt(event.getCompletedAt());
        Job job = JobPayloads.encode(String.valueOf(missionJob.hashCode()), missionJob);
        return RetryableJob.of(job, RETRY_COUNT, RETRY_DELAY, RETRY_DELAY_INCREASE_FACTOR);
    }

    @Override
    protected boolean filter(MissionCompletedEvent event) {
        var missionId = event.getMissionId();
        var mission =
                FirebaseApi.fetch(
                                firestore.collection(MISSION_COLLECTION).document(missionId).get())
                        .toObject(SerializableFirebaseMission.class);
        return mission != null && !mission.needIgnore();
    }
}
