package com.bees360.service;

import com.bees360.entity.Member;
import com.bees360.entity.enums.RoleEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/27 14:59
 */
public interface ProjectMemberService {
    List<Member> listMembers(long projectId);

    Member addMember(long projectId, long assigned, long userId, RoleEnum role);

    List<Member> addMembers(List<Long> projectIds, long assigned, long userIds, RoleEnum role);

    Member removeMember(long projectId, long assigned, long userId);

    Member addVisitor(long projectId, long assigned, long userId);

    Member addVisitors(long projectId, long assigned, List<Long> userId);

    Member addProjectCreator(long projectId, long assigned, long userId);
}
