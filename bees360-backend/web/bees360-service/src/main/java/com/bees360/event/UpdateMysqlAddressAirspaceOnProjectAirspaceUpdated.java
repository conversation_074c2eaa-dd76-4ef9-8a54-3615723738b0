package com.bees360.event;

import com.bees360.entity.AddressAirspace;
import com.bees360.event.registry.ProjectAirspaceUpdatedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.AddressAirspaceMapper;
import com.bees360.service.ProjectService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听项目空域更新事件并更新MySQL中的地址空域数据
 */
@Log4j2
public class UpdateMysqlAddressAirspaceOnProjectAirspaceUpdated extends
    AbstractNamedEventListener<ProjectAirspaceUpdatedEvent> {
    private final AddressAirspaceMapper addressAirspaceMapper;
    private final ProjectService projectService;

    public UpdateMysqlAddressAirspaceOnProjectAirspaceUpdated(
        AddressAirspaceMapper addressAirspaceMapper,
        ProjectService projectService) {
        this.addressAirspaceMapper = addressAirspaceMapper;
        this.projectService = projectService;

        log.info("Created '{}(addressAirspaceMapper={}, projectService={})",
            this, this.addressAirspaceMapper, this.projectService);
    }

    @Override
    public void handle(ProjectAirspaceUpdatedEvent event) throws IOException {
        var addressId = projectService.getById(Long.parseLong(event.getProjectId())).getAddressId();
        AddressAirspace addressAirspace = new AddressAirspace();
        addressAirspace.setAddressId(addressId);
        addressAirspace.setStatus(event.getAirspaceTo().getStatus());
        addressAirspace.setHeightCeiling(event.getAirspaceTo().getHeightCeiling());
        addressAirspaceMapper.upsertByAddressId(addressAirspace);
    }
}
