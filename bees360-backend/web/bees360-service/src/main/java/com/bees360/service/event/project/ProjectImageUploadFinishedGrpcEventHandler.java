package com.bees360.service.event.project;

import com.bees360.common.grpc.GrpcClient;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.JobPayloads;
import com.bees360.job.registry.Web2AiDataTransferJob;
import com.bees360.schedule.service.SchedulerManagerTransactionalDelegate;
import com.bees360.service.grpc.GrpcReportGenerateService;
import com.bees360.service.grpc.GrpcStubConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.time.Duration;
import java.time.Instant;
import java.util.List;

/**
 * transfer data from web to ai
 */
@Slf4j
@Component
public class ProjectImageUploadFinishedGrpcEventHandler {
    private static final int RETRY_COUNT = 5;
    private static final float RETRY_INCREASE_FACTOR = 1.5F;
    private static final Duration RETRY_DELAY = Duration.ofMinutes(1);

    @Autowired
    private GrpcReportGenerateService grpcReportGenerateService;

    @Autowired
    private SchedulerManagerTransactionalDelegate schedulerManagerTransactionalDelegate;
    @Autowired private JobScheduler jobScheduler;

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void transferDataToAi(ProjectImageUploadFinishedGrpcEvent projectReportGeneratedGrpcEvent) {
        assert projectReportGeneratedGrpcEvent != null && projectReportGeneratedGrpcEvent.getProjectId() != 0;
        long projectId = projectReportGeneratedGrpcEvent.getProjectId();
        try {
            // 暂时改成同步全部类型的图片 @see issue#2094
            scheduleDataTransferJob(projectId, projectReportGeneratedGrpcEvent.isImageSyncFlag(),
                projectReportGeneratedGrpcEvent.getSyncPoint());
        } catch (Exception e) {
            String message = "Failed to generateReport for projectId %d when ProjectImageUploadFinishedEvent occur";
            message = message.formatted(projectId);
            log.error(message, e);
        }
    }

    private void scheduleDataTransferJob(long projectId, boolean imageSyncFlag, String syncPoint) {
        List<GrpcStubConfig> grpcStubConfigs = grpcReportGenerateService.getStubs();
        for(GrpcStubConfig grpcStubConfig : grpcStubConfigs) {
            GrpcClient grpcClient = grpcStubConfig.getAiGrpcClient();
            var transferJob = new Web2AiDataTransferJob();
            transferJob.setProjectId(projectId);
            transferJob.setImageSyncFlag(imageSyncFlag);
            transferJob.setSyncPoint(syncPoint);
            transferJob.setEndpoint(grpcClient.getEndpoints());
            transferJob.setUpdatedAt(Instant.now().toEpochMilli());
            var job = JobPayloads.encode(String.valueOf(transferJob.hashCode()), transferJob);
            job = RetryableJob.of(job, RETRY_COUNT, RETRY_DELAY, RETRY_INCREASE_FACTOR);
            jobScheduler.schedule(job);
        }
    }
}
