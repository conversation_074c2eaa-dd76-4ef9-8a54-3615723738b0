package com.bees360.service;

import com.bees360.base.exception.ServiceMessageException;
import com.bees360.entity.ReportTemplateCreateParam;
import com.bees360.entity.vo.ReportTemplateVo;
import java.util.List;

public interface PreferenceService {
	String getCustomizedProjectCode(long userId);

    void addProjectTemplate(long userId, ReportTemplateCreateParam param) throws ServiceMessageException;

    void addCompanyTemplates(List<ReportTemplateCreateParam> params);

    void deleteProjectTemplate(long userId, long templateId);

    List<ReportTemplateVo> listCompanyTemplates(long companyId, int reportType);
}
