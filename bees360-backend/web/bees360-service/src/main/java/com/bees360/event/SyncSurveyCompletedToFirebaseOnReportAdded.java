package com.bees360.event;

import com.bees360.event.registry.ReportAdded;
import com.bees360.job.Job;
import com.bees360.job.JobScheduler;
import com.bees360.job.RetryableJob;
import com.bees360.job.registry.SyncSurveyCompletedToFirebaseJob;
import com.bees360.job.util.EventTriggeredJob;
import com.bees360.project.report.ProjectReportProvider;
import com.bees360.report.ReportTypeEnum;
import com.bees360.util.Iterables;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.time.Duration;

/**
 * 该类的主要功能是在报告添加事件触发时，将完成的调查数据同步到Firebase数据库。
 */
@Log4j2
public class SyncSurveyCompletedToFirebaseOnReportAdded extends EventTriggeredJob<ReportAdded> {

    private final ProjectReportProvider projectReportProvider;

    public SyncSurveyCompletedToFirebaseOnReportAdded(JobScheduler jobScheduler, ProjectReportProvider projectReportProvider) {
        super(jobScheduler);
        this.projectReportProvider = projectReportProvider;
        log.info("Created {}.", this);
    }

    @Override
    protected Job convert(ReportAdded event) {
        var projects = projectReportProvider.findProjectId(event.getId());
        var payload = new SyncSurveyCompletedToFirebaseJob();
        payload.setProjectIds(Iterables.toList(projects));
        return RetryableJob.of(Job.ofPayload(payload), 3, Duration.ofSeconds(1), 1.0F);
    }

    @Override
    protected boolean filter(ReportAdded event) {
        return StringUtils.equals(event.getReportType(), ReportTypeEnum.HIS.getKey());
    }
}
