package com.bees360.service.impl;

import jakarta.inject.Inject;

import com.bees360.entity.enums.productandpayment.GlobalDiscountTypeEnum;
import com.bees360.entity.vo.WalletGlobalDiscount;
import com.bees360.util.DoubleArithmetic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.UserPaymentProfile;
import com.bees360.mapper.payment.UserPaymentProfileMapper;
import com.bees360.service.payment.UserPaymentProfileService;

/**
 *
 * <AUTHOR>
 *
 */
@Service("userPaymentProfileService")
public class UserPaymentProfileServiceImpl implements UserPaymentProfileService{

	private static final Logger logger = LoggerFactory.getLogger(UserPaymentProfileServiceImpl.class);

	@Inject
	UserPaymentProfileMapper userPaymentProfileMapper;

	@Override
	public WalletGlobalDiscount getRecommendedDiscount(long userId) throws ServiceException {
		UserPaymentProfile userPaymentProfile = null;
		WalletGlobalDiscount discount = new WalletGlobalDiscount();
		try {
			userPaymentProfile = userPaymentProfileMapper.getUserPaymentProfile(userId);

			double newCustomerDiscountPercent = userPaymentProfile == null ? 0 : userPaymentProfile.getNewCustomerDiscountPercent();
			int newCustomerDiscountProjectNum = userPaymentProfile == null ? 0 : userPaymentProfile.getNewCustomerDiscountProjectNum();
			double discountPercent = userPaymentProfile == null ? 0 : userPaymentProfile.getDiscountPercent();

			GlobalDiscountTypeEnum discountTypeEnum = null;
			if(newCustomerDiscountProjectNum > 0) {
				discountTypeEnum = GlobalDiscountTypeEnum.LIMITED_DISCOUNT;
				discount.setNum(newCustomerDiscountProjectNum);
				discount.setValue(newCustomerDiscountPercent);
			} else if (discountPercent > 0) {
				discountTypeEnum = GlobalDiscountTypeEnum.UNLIMITED_DISCOUNT;
				discount.setNum(0);
				discount.setValue(discountPercent);
			} else {
				return null;
			}

			discount.setDiscountType(discountTypeEnum.getCode());
			discount.setDiscountName(discountTypeEnum.getDisplay());
			discount.setIsNumberLimited(discountTypeEnum.isNumberLimited());
			discount.setOffType(discountTypeEnum.getOffType().getCode());

		} catch(Exception e) {
			logger.error("UserPaymentProfileServiceImpl getUserPaymentProfile failed", e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
		return discount;
	}

	@Override
	public UserPaymentProfile getUserPaymentProfile(long userId) throws ServiceException {
		UserPaymentProfile userPaymentProfile = null;
		try {
			userPaymentProfile = userPaymentProfileMapper.getUserPaymentProfile(userId);
		}catch(Exception e) {
			logger.error("UserPaymentProfileServiceImpl getUserPaymentProfile failed",e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
		return userPaymentProfile;
	}

	@Override
	public void updateUserPaymentProfileByBalance(long userId, double walletBalance, String currency) throws ServiceException {
		try {
			userPaymentProfileMapper.updateUserPaymentProfileByBalance(userId,walletBalance, currency);
		}catch(Exception e) {
			logger.error("UserPaymentProfileServiceImpl updateUserPaymentProfileByProject failed",e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}

	}

	@Override
	public void updateUserPaymentProfileByCommission(long userId, double commissionBalance, String currency) throws ServiceException {
		try {
			userPaymentProfileMapper.updateUserPaymentProfileByCommission(userId, commissionBalance, currency);
		}catch(Exception e) {
			logger.error("UserPaymentProfileServiceImpl updateUserPaymentProfileByBanlance failed",e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

	@Override
	public void updateFreeOnTrailProjectNum(long userId, int freeOnTrailProjectNum) throws ServiceException {
		try {
			logger.info("update freeOnTrailProjectNum of user ({}) to {}", userId, freeOnTrailProjectNum);
			userPaymentProfileMapper.updateFreeOnTrailProjectNum(userId, freeOnTrailProjectNum);
		}catch(Exception e) {
			logger.error("UserPaymentProfileServiceImpl updateFreeOnTrailProjectNum failed",e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

	@Override
	public void updateUserDiscountPercent(long userId, double discountPercent) throws ServiceException{
		try {
			userPaymentProfileMapper.updateUserDiscountPercent(userId, discountPercent);
		}catch(Exception e) {
			logger.error("UserPaymentProfileServiceImpl updateUserDiscountPercent failed",e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
	}

	@Override
	public void updateNewCustomerDiscountAndProjectNum(long userId, int freeOnTrailProjectNum, double newCustomerDiscountPercent, int projectNum) {
		try {
			userPaymentProfileMapper.updateNewCustomerTrailProjectNumAndDiscount(userId, freeOnTrailProjectNum, newCustomerDiscountPercent, projectNum);
		} catch (Exception e) {
			logger.error("UserPaymentProfileServiceImpl updataNewCustomerDiscountAndProjectNum failed",e);
		}
	}

	@Override
	public void updateNewCustomerDiscountAndProjectNum(long userId, double newCustomerDiscountPercent, int projectNum)
			throws ServiceException {
		try {
			userPaymentProfileMapper.updateNewCustomerDiscountAndProjectNum(userId, newCustomerDiscountPercent, projectNum);
		} catch (Exception e) {
			logger.error("UserPaymentProfileServiceImpl updataNewCustomerDiscountAndProjectNum failed",e);
		}
	}
}
