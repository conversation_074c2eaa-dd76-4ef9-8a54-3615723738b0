package com.bees360.event;

import com.bees360.event.registry.CommentChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.firebase.FirebaseService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听评论变更事件并更新项目笔记到Firebase
 */
@Log4j2
public class UpdateProjectNoteOnCommentChanged extends AbstractNamedEventListener<CommentChangedEvent> {

    private final FirebaseService firebaseService;

    private final ProjectMapper projectMapper;

    public UpdateProjectNoteOnCommentChanged(FirebaseService firebaseService, ProjectMapper projectMapper) {
        this.firebaseService = firebaseService;
        this.projectMapper = projectMapper;
        log.info("created {}, FirebaseService {}, projectMapper {}", this, firebaseService, projectMapper);
    }

    @Override
    public void handle(CommentChangedEvent commentChangedEvent) throws IOException {
        var comment = commentChangedEvent.getSource();
        var projectId = comment.getProjectId();
        var project = projectMapper.getById(projectId);

        // case还没被创建出来
        if (project == null) {
            return;
        }
        projectId = commentChangedEvent.getSource().getProjectId();
        firebaseService.updateNoteToFirebase(projectId);
    }
}
