package com.bees360.service.properties;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.api.client.util.Lists;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Validated
@Component
@ConfigurationProperties(prefix = "bees360.watcher")
public class Bees360WatcherProperties {

    private String adminEmail;
    private String adjusterEmail;
    @NotNull
    private List<String> statistics = Lists.newArrayList();
    @NotNull
    private List<String> claimPaid = Lists.newArrayList();
    @NotNull
    private List<String> swyfftAlert = Lists.newArrayList();
    @NotNull
    private Map<String, List<String>> projectCreatedCustomer = new HashMap<>();
}
