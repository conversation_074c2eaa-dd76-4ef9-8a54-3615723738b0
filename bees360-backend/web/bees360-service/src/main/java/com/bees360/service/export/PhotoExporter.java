package com.bees360.service.export;

import java.util.List;
import java.util.function.BiConsumer;

import com.bees360.entity.vo.ProjectImageAnnotationVo;

/**
 * <AUTHOR>
 */
public interface PhotoExporter {

    List<ExportableDocument> collectPhotos(long projectId);

    List<ExportableDocument> collectPhotos(long projectId,
        BiConsumer<ProjectImageAnnotationVo, ExportableDocument> convertImageToDoc);
}
