package com.bees360.job;

import com.bees360.job.registry.SerializableFirebaseMagicplan;
import com.bees360.job.registry.SerializableFirebaseMagicplanV2;
import com.bees360.job.util.AbstractJobExecutor;
import com.bees360.service.firebase.FirebaseMagicplanService;
import com.google.cloud.firestore.Firestore;

import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.beanutils.BeanUtils;
import java.io.IOException;

import static com.bees360.service.util.FirebaseExceptionTranslation.translateExceptionAndThrow;

@Log4j2
@ToString
public class FirebaseMagicplanChangedExecutorV2
        extends AbstractJobExecutor<SerializableFirebaseMagicplanV2> {

    private final FirebaseMagicplanService firebaseMagicplanService;
    private final Firestore firestore;

    public FirebaseMagicplanChangedExecutorV2(
            FirebaseMagicplanService firebaseMagicplanService, Firestore firestore) {
        this.firebaseMagicplanService = firebaseMagicplanService;
        this.firestore = firestore;

        log.info("Created '{}'", this);
    }

    @Override
    protected void handle(SerializableFirebaseMagicplanV2 magicplanV2) throws IOException {
        SerializableFirebaseMagicplan magicplan;
        try{
            magicplan = convert(magicplanV2);
        } catch (IllegalArgumentException e){
            log.error("Failed to handle magicplan '{}'.", magicplanV2, e);
            return;
        }

        try {
            log.info("Start to handle magicplan '{}' '{}'", magicplan.getId(), magicplan);
            firebaseMagicplanService.handleMagicplan(magicplan);
            log.info("Successfully handle magicplan '{}'", magicplan.getId());
        } catch (RuntimeException e) {
            log.warn("Failed to handle magicplan '{}'.", magicplan, e);
            translateExceptionAndThrow(e);
        }
    }

    private SerializableFirebaseMagicplan convert(SerializableFirebaseMagicplanV2 magicplanV2) throws IllegalArgumentException {
        SerializableFirebaseMagicplan magicplan = new SerializableFirebaseMagicplan();
        try {
            BeanUtils.copyProperties(magicplan, magicplanV2);
        } catch (Exception e) {
            throw new IllegalArgumentException(
                "Failed to convert SerializableFirebaseMagicplanV2 to SerializableFirebaseMagicplan.", e);
        }
        return magicplan;
    }
}
