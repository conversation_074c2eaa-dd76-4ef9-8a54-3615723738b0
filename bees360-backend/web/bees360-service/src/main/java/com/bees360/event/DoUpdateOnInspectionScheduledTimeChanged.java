package com.bees360.event;

import com.bees360.entity.ProjectInspectionSchedule;
import com.bees360.entity.User;
import com.bees360.event.registry.InspectionScheduledTimeChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectInspectionScheduleMapper;
import com.bees360.service.ActivityService;
import com.bees360.service.ProjectService;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;

/**
 * 监听巡检计划时间变更事件并更新相关数据
 */
@Log4j2
public class DoUpdateOnInspectionScheduledTimeChanged extends
    AbstractNamedEventListener<InspectionScheduledTimeChanged> {

    private final ProjectInspectionScheduleMapper projectInspectionScheduleMapper;

    private final ProjectService projectService;

    private final ActivityService activityService;

    public DoUpdateOnInspectionScheduledTimeChanged(@NonNull ProjectInspectionScheduleMapper projectInspectionScheduleMapper,
                                                    @NonNull ProjectService projectService,
                                                    @NonNull ActivityService activityService) {
        this.projectInspectionScheduleMapper = projectInspectionScheduleMapper;
        this.projectService = projectService;
        this.activityService = activityService;
        log.info("Create listener {}", this.getClass().getName());
    }

    @Override
    public void handle(InspectionScheduledTimeChanged inspectionScheduledTimeChanged) throws IOException {
        Long scheduledTime = inspectionScheduledTimeChanged.getScheduledTime();
        long projectId = Long.parseLong(inspectionScheduledTimeChanged.getProjectId());
        ProjectInspectionSchedule projectInspectionSchedule = projectInspectionScheduleMapper.getByProjectId(projectId);
        Long oldScheduledTime = Optional.ofNullable(projectInspectionSchedule)
            .map(ProjectInspectionSchedule::getScheduledTime).orElse(null);
        if (!Objects.equals(scheduledTime, oldScheduledTime)) {
            projectInspectionScheduleMapper.updateScheduledTime(projectId, scheduledTime);
            projectService.updateInspectionScheduledTime(User.BEES_PILOT_SYSTEM, projectId, scheduledTime);
            activityService.inspectionTimeChanged(
                projectId, inspectionScheduledTimeChanged.getUpdatedBy(),
                scheduledTime, oldScheduledTime, inspectionScheduledTimeChanged.getReason(),
                inspectionScheduledTimeChanged.getUpdatedTime());
        }
    }
}
