package com.bees360.service.job;

import com.bees360.address.AddressFlyZoneTypeManager;
import com.bees360.address.Message;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.flyzone.FlyZoneType;
import com.bees360.flyzone.FlyZoneTypeProvider;
import com.bees360.job.Job;
import com.bees360.job.JobExecutor;
import com.bees360.service.ProjectService;
import com.google.gson.Gson;
import com.google.protobuf.ByteString;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@Service(FlyZoneTypeUpdateJob.DEFAULT_NAME)
public class FlyZoneTypeUpdateJobExecutor implements JobExecutor {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private FlyZoneTypeProvider flyZoneTypeProvider;

    @Autowired
    private AddressFlyZoneTypeManager addressFlyZoneTypeManager;

    @Override
    public String getName() {
        return FlyZoneTypeUpdateJob.DEFAULT_NAME;
    }

    protected Project decode(Job job) {
        ByteString byteString = job.getPayload();
        byte[] bytes = byteString.toByteArray();
        return new Gson().fromJson(new String(bytes, StandardCharsets.UTF_8), Project.class);
    }

    @Override
    public void execute(Job job) throws IOException {
        log.debug("{} start to execute job ", this);
        Project project = decode(job);
        double lat = project.getLat();
        double lng = project.getLng();
        long projectId = project.getProjectId();
        FlyZoneType flyZoneType = flyZoneTypeProvider.getFlyZoneType(lat, lng);
        try {
            if (project.getFlyZoneType() == flyZoneType.getCode()) {
                return;
            }
            updateAddressFlyZoneType(project.getAddressId(), flyZoneType);
            syncFlyZoneTypeDataToPostgres(project.getAddressId(), flyZoneType.getCode());
            log.info("FlyZoneType of project {} will be {}.", projectId, flyZoneType);
        } catch (ServiceException e) {
            String message = "Fail to update flyZoneType for project(%s) at (%s, %s)";
            message = message.formatted(projectId, lng, lat);
            throw new IllegalStateException(message, e);
        }
    }

    private void updateAddressFlyZoneType(String addressId, FlyZoneType flyZoneType) throws ServiceException {
        List<Long> projectsToFix = projectService.listProjectIdsByAddressId(addressId);
        for (long projectId : projectsToFix) {
            projectService.updateFlyZoneType(projectId, flyZoneType);
        }
    }

    private void syncFlyZoneTypeDataToPostgres(String addressId, int typeCode) {
        Message.AddressMessage.FlyZoneType flyZoneTypeProto = Message.AddressMessage.FlyZoneType.forNumber(typeCode);
        addressFlyZoneTypeManager.updateAddressFlyZoneType(addressId, flyZoneTypeProto);
    }

}
