package com.bees360.service.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;
@ConfigurationProperties(prefix = "bees360.firebase.transfer")
@Component
@Data
public class FirebaseTransferProperties {
    private Integer retryCount = 5;
    private Duration retryDelay = Duration.ofSeconds(2);
    private Float retryDelayIncreaseFactor = 2f;
}
