package com.bees360.event;

import com.bees360.entity.User;
import com.bees360.event.registry.ResourceDeleted;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ProjectStatusService;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.util.function.Function;
import java.util.function.Predicate;

import jakarta.annotation.Nullable;

/**
 * 监听资源删除事件并根据项目状态更新项目状态为ClientReceived
 */
@Log4j2
public class ClientReceivedOnCustomerResourceDeleted
        extends AbstractNamedEventListener<ResourceDeleted> {

    private static final String EVENT_NAME_SEPARATOR = ".";
    private final ProjectStatusService projectStatusService;
    /** Namespace, 区分不同的Customer,与EventfulResourcePool一致 */
    private final String namespace;
    /** ResourceKeyDecoder, 从ResourceKey中解析出ProjectId,要与Encoder命名规则保持一致 */
    private final Function<String, String> resourceKeyDecoder;
    /** projectStatePredicate, 校验Project状态是否能够更新为ClientReceived */
    private final Predicate<String> projectStatePredicate;

    public ClientReceivedOnCustomerResourceDeleted(
            @Nullable String namespace,
            @NonNull ProjectStatusService projectStatusService,
            @NonNull Function<String, String> resourceKeyDecoder,
            @NonNull Predicate<String> projectStatePredicate) {
        this.namespace = namespace;
        this.projectStatusService = projectStatusService;
        this.resourceKeyDecoder = resourceKeyDecoder;
        this.projectStatePredicate = projectStatePredicate;

        log.info(
                "Created '{}(namespace={}, projectStatusService={},"
                        + " resourceKeyDecoder={}, projectStatePredicate={})'",
                this,
                this.namespace,
                this.projectStatusService,
                this.resourceKeyDecoder,
                this.projectStatePredicate);
    }

    @Override
    public void handle(ResourceDeleted resourceDeleted) throws IOException {
        var projectId = resourceKeyDecoder.apply(resourceDeleted.getKey());
        if (projectStatePredicate.test(projectId)) {
            log.info("Client Received on {} project {} resource deleted.", namespace, projectId);
            var message =
                "Change status since the resource %s has been received.".formatted(
                    resourceDeleted.getKey());
            projectStatusService.changeOnClientReceived(
                    User.AI_ID, Long.parseLong(projectId), message);
        }
    }

    @Override
    public String getEventName() {
        return namespace == null
                ? super.getEventName()
                : namespace + EVENT_NAME_SEPARATOR + super.getEventName();
    }
}
