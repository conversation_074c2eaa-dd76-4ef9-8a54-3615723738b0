package com.bees360.service.export;

import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Predicate;

import jakarta.annotation.Nullable;

import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectReportFile;

/**
 * <AUTHOR>
 */
public interface ReportExporter {

    /**
     * 导出报告文件，按照默认规则进行过滤，按照默认方式命名
     *
     * @param projectId
     * @return
     * @throws ServiceException
     */
    List<ExportableDocument> collectReports(long projectId);

    /**
     * 导出报告文件，按照默认规则进行过滤
     *
     * @param projectId
     * @param customFileName
     *            报告文件重命名
     * @return
     * @throws ServiceException
     */
    List<ExportableDocument> collectReports(long projectId,
        BiFunction<Project, ProjectReportFile, String> customFileName);

    /**
     * 导出报告文件
     *
     * @param projectId
     * @param filter
     *            对结果进行过滤
     * @param customFileName
     *            自定义文件名
     * @return
     * @throws ServiceException
     */
    List<ExportableDocument> collectReports(long projectId, Predicate<ProjectReportFile> filter,
        @Nullable BiFunction<Project, ProjectReportFile, String> customFileName);
}
