package com.bees360.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.mapper.CityMapper;
import com.bees360.mapper.CountryMapper;
import com.bees360.mapper.RegionMapper;
import com.bees360.service.LocationService;

@Service("locationService")
public class LocationServiceImpl implements LocationService{

	@Inject
	private CountryMapper countryMapper;
	@Inject
	private RegionMapper regionMapper;
	@Inject
	private CityMapper cityMapper;

	private final int NUMBER_LIMIT = 20;

	private Logger logger = LoggerFactory.getLogger(LocationServiceImpl.class);

	@Override
	public List<String> listCitiesWithPrefix(String prefix) throws ServiceException {
		List<String> cities = null;
		prefix = preProcessPrefix(prefix);
		try {
			cities = cityMapper.listCityWithPrefix(prefix, NUMBER_LIMIT);
			// remove namesake city
			Set<String> citySet = new LinkedHashSet<String>();
			if(cities != null){
				for(String city: cities){
					citySet.add(city);
				}
			}
			cities = new ArrayList<String>(citySet.size());
			for(String str: citySet){
				cities.add(str);
			}
		} catch (Exception e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
		return cities;
	}

	@Override
	public List<String> listRegionsWithPrefix(String prefix, int limit) throws ServiceException {
		List<String> regions = null;
		prefix = preProcessPrefix(prefix);
		try {
			regions = regionMapper.listRegionWithPrefix(prefix, limit);
		} catch (Exception e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
		return regions;
	}


	@Override
	public List<String> listCountriesWithPrefix(String prefix, int limit) throws ServiceException {
		List<String> countries = null;
		prefix = preProcessPrefix(prefix);
		try {
			countries = countryMapper.listCountriesWithPrefix(prefix, limit);
		} catch (Exception e) {
			logger.error(MessageCode.DATABASE_EXCEPTION, e);
			throw new ServiceException(MessageCode.DATABASE_EXCEPTION);
		}
		return countries;
	}

	private String preProcessPrefix(String prefix){
		int prefixMaxLen = 50;
		if(prefix != null && prefix.length() > 0){
			prefix = prefix.toLowerCase();
			if(prefix.length() > 50){
				return prefix.substring(0, prefixMaxLen) + "%";
			}
			return prefix + "%";
		} else {
			return null;
		}
	}

}
