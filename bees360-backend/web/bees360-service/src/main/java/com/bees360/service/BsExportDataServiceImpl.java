package com.bees360.service;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceMessageException;
import com.bees360.entity.BsExportData;
import com.bees360.mapper.BsExportDataMapper;
import com.bees360.util.AssertUtil;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;
import java.util.Objects;

@Service
public class BsExportDataServiceImpl implements BsExportDataService {


    @Inject
    private BsExportDataMapper bsExportDataMapper;


    @Override
    public List<BsExportData> getExportData(String relatedId, String type) {
        return bsExportDataMapper.getExportData(relatedId, type);
    }

    @Override
    public BsExportData insertOrUpdateData(BsExportData param) throws ServiceMessageException {
        AssertUtil.notNull(param, "data is null");
        AssertUtil.notNull(param.getRelatedId(), "id is null");
        AssertUtil.notNull(param.getRelatedType(), "type is null");
        if (param.getId() == 0L) {
            try {
                bsExportDataMapper.insertExportData(param);
            } catch (DuplicateKeyException e) {
                throw new ServiceMessageException(MessageCode.PARAM_INVALID, "relatedId and type existed");
            }
        } else {
            updateExportData(param);
        }
        return getByRelatedIdAndType(param.getRelatedId(), param.getRelatedType());
    }

    @Override
    public void updateExportData(BsExportData param) throws ServiceMessageException {
        if (Objects.equals(param.getId(), 0L)) {
            return;
        }
        BsExportData data = bsExportDataMapper.getById(param.getId());
        if (Objects.nonNull(data)) {
            BsExportData oldData = getByRelatedIdAndType(param.getRelatedId(), param.getRelatedType());
            if (Objects.nonNull(oldData) && !Objects.equals(oldData.getId(), data.getId())) {
                throw new ServiceMessageException(MessageCode.PARAM_INVALID, "relatedId and type existed");
            }
            bsExportDataMapper.updateExportData(param);
        }
    }

    @Override
    public void deleteExportData(long id) throws ServiceMessageException {
        bsExportDataMapper.deleteExportData(id);
    }

    @Override
    public BsExportData getByRelatedIdAndType(String relatedId, String type) {
        return bsExportDataMapper.getExportData(relatedId, type).stream().findFirst().orElse(null);
    }
}
