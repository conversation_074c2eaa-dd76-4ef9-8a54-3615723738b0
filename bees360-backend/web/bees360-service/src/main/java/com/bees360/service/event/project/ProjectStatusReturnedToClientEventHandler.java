package com.bees360.service.event.project;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.User;
import com.bees360.entity.enums.ProjectEventTypeEnum;
import com.bees360.schedule.job.retry.RetryableJobTriggerFactory;
import com.bees360.schedule.service.SchedulerManagerTransactionalDelegate;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.grpc.AiGrpcClient;
import com.bees360.service.grpc.GrpcReportGenerateService;
import com.bees360.service.grpc.GrpcStubConfig;
import com.bees360.service.grpc.impl.GrpcProjectStatusChangeServiceComponent;
import com.bees360.service.grpc.job.ProjectChangedToReturnToClientGrpcJob;
import com.bees360.service.grpc.job.ProjectChangedToReturnToClientJobDetail;
import com.bees360.web.event.project.ProjectStatusReturnedToClientEvent;
import java.util.function.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDetail;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.List;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
public class ProjectStatusReturnedToClientEventHandler {

    @Autowired
    private GrpcReportGenerateService grpcReportGenerateService;

    @Autowired
    private SchedulerManagerTransactionalDelegate schedulerManagerTransactionalDelegate;

    @Autowired
    private ProjectStatusService projectStatusService;

    @Autowired
    private GrpcProjectStatusChangeServiceComponent grpcProjectStatusChangeServiceComponent;

    @Autowired
    private Predicate<Project> projectAutoClientReceivedPredicate;

    @Transactional
    @EventListener
    public void changeStatusToReturnToClient(ProjectStatusReturnedToClientEvent projectStatusReturnedToClientEvent) {
        Project project = projectStatusReturnedToClientEvent.getProject();
        long projectId = project.getProjectId();
        try {
            log.info("Try to update the status of project {} in ai to return_to_client.", projectId);
            grpcProjectStatusChangeServiceComponent.updateProjectStatusToAi(projectId, ProjectEventTypeEnum.RETURN_TO_CLIENT,
                null, projectStatusReturnedToClientEvent.getStatus().getCreatedTime());
        } catch (Exception e) {
            log.error("Fail to update the status of project {} in ai to return_to_client.", projectId, e);
        }
    }

    @Async
    @TransactionalEventListener(fallbackExecution = true)
    public void autoClientReceiveOnReturnToClient(ProjectStatusReturnedToClientEvent event) {
        Project project = event.getProject();
        try {
            log.info("Try to update the status of project {} to client received.", project.getProjectId());
            if (projectAutoClientReceivedPredicate.test(project)) {
                ProjectStatus status = projectStatusService.changeOnClientReceived(User.AI_ID, project.getProjectId(), "");
                log.info("change the status of project {} to client received: {}", project.getProjectId(), status);
            }
        } catch (Exception e) {
            log.error("Fail to set return to client automatically for project {}.", project.getProjectId(), e);
        }
    }

    private void scheduleReturnToClientJob(long projectId) {
        try {
            List<GrpcStubConfig> stubConfigs = grpcReportGenerateService.getStubs();
            for(GrpcStubConfig grpcStubConfig : stubConfigs) {
                AiGrpcClient grpcClient = grpcStubConfig.getAiGrpcClient();
                JobDetail jobDetail = ProjectChangedToReturnToClientJobDetail.createJobDetail(projectId,
                    grpcClient.getEndpoints(), ProjectChangedToReturnToClientGrpcJob.class);
                Trigger trigger = RetryableJobTriggerFactory
                    .retryForeverTrigger(jobDetail.getKey().getName(), jobDetail.getKey().getGroup(), Duration.ofMinutes(1))
                    .build();

                schedulerManagerTransactionalDelegate.scheduleJob(jobDetail, trigger);
            }
        } catch (SchedulerException se) {
            log.error("Fail to schedule return-to-client Job using grpc: {}", se.getMessage(), se);
        }
    }
}
