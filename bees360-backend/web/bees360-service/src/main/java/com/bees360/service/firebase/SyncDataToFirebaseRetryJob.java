package com.bees360.service.firebase;

import com.bees360.schedule.exception.RetryableException;
import com.bees360.schedule.job.retry.RetryableJob;
import com.bees360.schedule.job.retry.RetryableOperation;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;
import com.bees360.schedule.util.QuartzJobConstant;
import com.google.api.core.ApiFuture;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.SetOptions;
import com.google.cloud.firestore.WriteResult;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.ExecutionException;
import java.util.function.Function;

@Slf4j
public class SyncDataToFirebaseRetryJob extends RetryableJob {
    @Autowired
    private Firestore firestore;
    @Setter
    private String id;
    @Setter
    private String documentPath;
    @Setter
    private Object data;
    @Override
    protected RetryableOperation getRetryableOperation() {
        return ctx -> {
            DocumentReference reference = firestore.document(documentPath);
            WriteResult result = fetch((a)-> reference.set(data,  SetOptions.merge()));
            log.info("Success sync '{}' to firebase at '{}'.", id, result.getUpdateTime());
        };
    }

    @Override
    protected RetryExceptionHandler getRetryExceptionHandler() {
        return ctx -> {
            String message = "Failed to sync data '%s' to firebase.".formatted(id);
            if (ctx.getRetryCount() > 20) {
                log.error(message, ctx.getLastEx());
                return false;
            }
            log.warn(message, ctx.getLastEx());
            return true;
        };
    }
    private WriteResult fetch(Function<Object, ApiFuture<WriteResult>> function) throws RetryableException {
        try {
            WriteResult writeResult = function.apply(data).get();
            if (writeResult == null) {
                throw new RetryableException("WriteResult is null");
            }
            return writeResult;
        } catch (InterruptedException | ExecutionException e) {
            throw new RetryableException(e);
        } catch (RuntimeException e) {
            log.error("Failed sync data '{}' to firebase.", id);
            throw  e;
        }
    }

    public static JobDetail createJobDetail(String id, String documentPath, Object data) {
        String jobName = "data-%s-transfer-to-firebase-%s".formatted(id, System.currentTimeMillis());
        JobDataMap jobData = new JobDataMap();
        jobData.put("id", id);
        jobData.put("documentPath", documentPath);
        jobData.put("data", data);
        String description = "Trigger sync data %s to firebase.".formatted(id);

        return JobBuilder.newJob(SyncDataToFirebaseRetryJob.class)
            .withIdentity(jobName, QuartzJobConstant.WebGroup.DATA_TRANSFER_TO_FIREBASE)
            .usingJobData(jobData)
            .withDescription(description)
            .requestRecovery(true)
            .build();
    }
}
