package com.bees360.service;

import java.util.List;

import com.bees360.base.exception.ServiceException;

public interface LocationService {

	/**
	 * Get cities with the specified prefix and limit the count.
	 * If the prefix too long, with length large than 50, get the substring(0, 50).
	 * Limit the count less than 20.
	 * <AUTHOR>
	 * @param prefix The prefix of the city
	 * @return a list of city name sorted by alphabetical order.
	 * @throws ServiceException
	 */
	List<String> listCitiesWithPrefix(String prefix) throws ServiceException;

	/**
	 * Get regions with the specified prefix and limit the count.
	 * If the prefix too long, with length large than 50, get the substring(0, 50).
	 * If the limit is lower than 0, get without count limit.
	 * <AUTHOR>
	 * @param prefix the prefix of region
	 * @param limit the max count of the regions
	 * @return a list of region name sorted by alphabetical order.
	 * @throws ServiceException
	 */
	List<String> listRegionsWithPrefix(String prefix, int limit) throws ServiceException;

	/**
	 * Get countries with the specified prefix and limit the count.
	 * If the prefix too long, with length large than 50, get the substring(0, 50).
	 * If the limit is lower than 0, get without count limit.
	 * <AUTHOR>
	 * @param prefix the prefix of the country
	 * @param limit the max count of the countries
	 * @return a list of country name sorted by alphabetical order.
	 * @throws ServiceException
	 */
	List<String> listCountriesWithPrefix(String prefix, int limit) throws ServiceException;
}
