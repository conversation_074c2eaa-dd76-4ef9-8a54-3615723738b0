package com.bees360.event;

import com.bees360.entity.User;
import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.event.registry.IBeesNotCompletedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.UserService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * 监听未完成事件并更新项目标签的处理器类
 */
@Log4j2
public class UpdateOperationTagOnIBeesNotComplete
        extends AbstractNamedEventListener<IBeesNotCompletedEvent> {

    private final ProjectLabelService projectLabelService;
    private final UserService userService;

    public UpdateOperationTagOnIBeesNotComplete(
            ProjectLabelService projectLabelService, UserService userService) {

        this.projectLabelService = projectLabelService;
        this.userService = userService;
        log.info("Created '{}(projectLabelService={}'", this, projectLabelService);
    }

    @Override
    public void handle(IBeesNotCompletedEvent iBeesNotCompletedEvent) throws IOException {
        var projectId = Long.parseLong(iBeesNotCompletedEvent.getProjectId());
        var userId =
                Optional.ofNullable(iBeesNotCompletedEvent.getUpdatedBy())
                        .filter(StringUtils::isNotBlank)
                        .map(userService::toWebUserId)
                        .orElse(User.AI_ID);
        projectLabelService.markAfterEraseLabel(
                projectId,
                List.of(ProjectLabelEnum.IBEES_NOT_COMPLETED.getLabelId()),
                userId,
                SystemTypeEnum.BEES360);
    }
}
