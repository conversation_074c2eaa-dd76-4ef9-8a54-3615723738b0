package com.bees360.service.util;

/**
 * <AUTHOR>
 */
public class ImageKeyUtil {

    private static final String ORIGINAL_RESOLUTIOIN_IMAGE_PREFIX = "project/{projectId}/images/origin/";
    private static final String MIDDLE_RESOLUTION_IMAGE_PREFIX = "project/{projectId}/images/biggerThumbnails/";
    private static final String LOWER_RESOLUTION_IMAGE_PREFIX = "project/{projectId}/images/thumbnails/";
    private static final String ANNOTATION_IMAGE_PREFIX = "project/{projectId}/images/annotations/";

    private static final String PROJECT_MARK = "{projectId}";

    public static ImageKeys createKeys(long projectId, String filename) {
        String projectIdStr = projectId + "";
        ImageKeys imageKeys = new ImageKeys()
            .setOriginal(ORIGINAL_RESOLUTIOIN_IMAGE_PREFIX.replace(PROJECT_MARK, projectIdStr) + filename)
            .setMiddleResolution(MIDDLE_RESOLUTION_IMAGE_PREFIX.replace(PROJECT_MARK, projectIdStr) + filename)
            .setLowerResolution(LOWER_RESOLUTION_IMAGE_PREFIX.replace(PROJECT_MARK, projectIdStr) + filename)
            .setAnnotation(ANNOTATION_IMAGE_PREFIX.replace(PROJECT_MARK, projectIdStr) + filename);

        return imageKeys;
    }
}
