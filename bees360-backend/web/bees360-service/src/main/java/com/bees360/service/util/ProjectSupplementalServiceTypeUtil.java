package com.bees360.service.util;

import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Set;

public class ProjectSupplementalServiceTypeUtil {

    private final Set<String> options;

    public ProjectSupplementalServiceTypeUtil(Set<String> options) {
        this.options = options;
    }

    public boolean checkValid(List<String> values) {
        if (CollectionUtils.isEmpty(options) || CollectionUtils.isEmpty(values)) {
            return true;
        }

        return options.containsAll(values);
    }

}
