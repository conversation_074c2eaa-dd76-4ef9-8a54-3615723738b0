package com.bees360.event;

import com.bees360.entity.enums.PayStatusEnum;
import com.bees360.event.registry.ProjectPaymentChanged;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectMapper;

import java.io.IOException;
import java.util.List;

/**
 * 监听项目支付变更事件并更新项目支付状态
 */
public class SetProjectPayStatusOnProjectPaymentChangedEvent extends AbstractNamedEventListener<ProjectPaymentChanged> {

    private final ProjectMapper projectMapper;

    public SetProjectPayStatusOnProjectPaymentChangedEvent(ProjectMapper projectMapper) {
        this.projectMapper = projectMapper;
    }

    @Override
    public void handle(ProjectPaymentChanged projectPaymentChanged) throws IOException {
        int status = projectPaymentChanged.getPayment().isPaid() ? PayStatusEnum.PAID.getCode() : PayStatusEnum.UNPAID.getCode();
        projectMapper.updatePayStatus(List.of(Long.parseLong(projectPaymentChanged.getPayment().getProjectId())), status);
    }
}
