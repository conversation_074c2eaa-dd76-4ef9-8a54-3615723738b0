package com.bees360.service.stat;

import com.bees360.entity.stat.converter.StatCardConverter;
import com.bees360.entity.stat.converter.StatListConverter;
import com.bees360.entity.stat.dto.ProjectListDataDto;
import com.bees360.entity.stat.dto.ProjectStatChartDto;
import com.bees360.entity.stat.enums.ProjectStatType;
import com.bees360.entity.stat.search.StatFullSearchOption;
import com.bees360.entity.stat.vo.StatComplexVo;
import com.bees360.entity.stat.vo.StatProjectCardVo;
import com.bees360.entity.stat.vo.StatProjectVo;
import com.bees360.entity.stat.vo.card.StatCreatedCardVo;
import com.bees360.entity.stat.vo.chart.StatChartVo;
import com.bees360.entity.stat.vo.chart.StatMapVo;
import com.bees360.entity.stat.vo.list.PagedResultVo;
import com.bees360.entity.stat.vo.list.ProjectListVo;
import com.bees360.entity.vo.Pagination;
import com.bees360.mapper.stat.ProjectStatMapper;
import com.bees360.project.ProjectDaysOldProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.bees360.service.stat.StatDaysOldUtil.NEW_DAYS_OLD;
import static com.bees360.service.stat.StatDaysOldUtil.preProcessDaysOldOption;
import static com.bees360.service.stat.StatDaysOldUtil.setProjectNewDaysOld;

@Service
public class StatProjectCreatedService implements StatComplexService {

    private static final Logger logger = LoggerFactory.getLogger(StatProjectCreatedService.class);

    @Autowired private ProjectStatMapper projectStatMapper;

    @Autowired private ProjectDaysOldProvider daysOldProvider;

    @Override
    public StatCreatedCardVo getCardStat(StatFullSearchOption searchOption) {

        final StatCreatedCardVo statCreatedCardVo =
                StatCardConverter.toCreatedCard(projectStatMapper.cardStat(searchOption));

        statCreatedCardVo.addDefaultServiceType(searchOption.getServiceTypes());

        return statCreatedCardVo;
    }

    @Override
    public StatChartVo getChartStat(StatFullSearchOption searchOption) {

        final List<ProjectStatChartDto> projects = projectStatMapper.chartStat(searchOption);

        return StatChartVo.buildVo(projects);
    }

    @Override
    public List<StatMapVo> getMapData(StatFullSearchOption fullSearchOption) {

        return StatMapVo.buildMapVo(projectStatMapper.chartStat(fullSearchOption));
    }

    @Override
    public PagedResultVo getProjectList(StatFullSearchOption searchOption) {

        return getPageResult(searchOption);
    }

    @Override
    public StatComplexVo getStatComplexInfo(StatFullSearchOption fullSearchOption) {
        var complexVo = new StatComplexVo();

        // add stat project list
        var pageResult = getPageResult(fullSearchOption);
        complexVo.setList(pageResult);
        fullSearchOption.setServiceTypes(fullSearchOption.getServiceTypesForChartAndCard());
        // add incomplete stat card vo
        var statProjectCard = new StatProjectCardVo();
        var cardVo =
                StatCardConverter.toCreatedCard(
                        projectStatMapper.cardStat(fullSearchOption));
        cardVo.addDefaultServiceType(fullSearchOption.getServiceTypes());
        complexVo.setCard(statProjectCard);
        // add stat project chart
        var statProjectChart = new StatProjectVo();
        var statType = ProjectStatType.getInstance(fullSearchOption.getType());
        if (statType == null) {
            complexVo.setChart(statProjectChart);
            return complexVo;
        }
        var chartStat = projectStatMapper.chartStat(fullSearchOption);
        var chartVo = StatChartVo.buildVo(chartStat);
        chartVo.addDefaultIfAbsentState(fullSearchOption.getStates());
        chartVo.addDefaultIfAbsentServiceType(fullSearchOption.getServiceTypes());
        statProjectChart.setChart(chartVo);
        var mapData = StatMapVo.buildMapVo(projectStatMapper.chartStat(fullSearchOption));
        statProjectChart.setMap(mapData);
        statProjectChart.setCard(cardVo);
        complexVo.setChart(statProjectChart);
        return complexVo;
    }

    private PagedResultVo getPageResult(StatFullSearchOption searchOption) {
        var pageIndex = searchOption.getPageIndex();
        var pageSize = searchOption.getPageSize();
        var sortOrder = searchOption.getSortOrder();
        var sortKey = searchOption.getSortKey();

        Map<String, Integer> daysOldMap = null;
        var projectIds =
                projectStatMapper.projectListId(searchOption).stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());
        if (Objects.equals(sortKey, NEW_DAYS_OLD)) {
            daysOldMap = daysOldProvider.findProjectDaysOld(projectIds);
            searchOption = preProcessDaysOldOption(daysOldMap, sortOrder, pageIndex, pageSize);
        }

        List<ProjectListDataDto> projects = projectStatMapper.listCreatedWithSearch(searchOption);

        if (daysOldMap == null) {
            var ids =
                    projects.stream()
                            .map(p -> String.valueOf(p.getProjectId()))
                            .collect(Collectors.toList());
            daysOldMap = daysOldProvider.findProjectDaysOld(ids);
        }
        logger.info("Filtered map is : " + daysOldMap + " search option is : " + searchOption);

        projects = setProjectNewDaysOld(projects, daysOldMap, sortOrder, sortKey);

        final List<ProjectListVo> createdVos =
                projects.stream().map(StatListConverter::toCreatedVo).collect(Collectors.toList());

        final Pagination pagination = new Pagination(pageIndex, pageSize, projectIds.size());

        PagedResultVo result = new PagedResultVo();
        result.setResult(createdVos);
        result.setPage(pagination);
        return result;
    }

    @Override
    public ProjectStatType getType() {
        return ProjectStatType.PROJECT_CREATED;
    }
}
