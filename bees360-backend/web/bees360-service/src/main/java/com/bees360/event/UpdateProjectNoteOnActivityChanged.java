package com.bees360.event;

import com.bees360.event.registry.ActivityChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectMapper;
import com.bees360.service.firebase.FirebaseService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听活动变更事件并更新项目备注到Firebase
 */
@Log4j2
public class UpdateProjectNoteOnActivityChanged  extends AbstractNamedEventListener<ActivityChangedEvent> {

    private final FirebaseService firebaseService;

    private final ProjectMapper projectMapper;

    public UpdateProjectNoteOnActivityChanged(FirebaseService firebaseService, ProjectMapper projectMapper) {
        this.firebaseService = firebaseService;
        this.projectMapper = projectMapper;
        log.info("created {}, FirebaseService {}, projectMapper {}", this, firebaseService, projectMapper);
    }

    @Override
    public void handle(ActivityChangedEvent activityChangedEvent) throws IOException {
        var activity = activityChangedEvent.getSource();
        var comment = activity.getComment();
        if (comment == null){
            return;
        }

        var projectId = comment.getProjectId();
        var project = projectMapper.getById(projectId);

        // 这里可能project还没被创建出来
        if (project == null) {
            return;
        }

        projectId = activityChangedEvent.getSource().getProjectId();
        firebaseService.updateNoteToFirebase(projectId);
    }
}
