package com.bees360.service.statistics;

import com.bees360.common.util.DateUtil;
import com.bees360.entity.Company;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectStatus;
import com.bees360.entity.dto.ProjectStatistics;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.map.RedisMap;
import com.bees360.mapper.CompanyMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.mapper.ProjectStatusMapper;
import com.bees360.project.tag.Message.ProjectTagType;
import com.bees360.project.tag.ProjectTag;
import com.bees360.project.tag.ProjectTagManager;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectService;
import com.bees360.util.ConstantUtil;
import com.bees360.util.Iterables;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAmount;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bees360.entity.dto.ProjectStatistics.ProjectLatestStatusMetrics;
import static com.bees360.entity.dto.ProjectStatistics.ProjectStatisticSummary;
import static com.bees360.entity.dto.ProjectStatistics.ProjectStatsPerCompany;
import static com.bees360.entity.dto.ProjectStatistics.ProjectStatsPerCompanySummary;
import static com.bees360.entity.dto.ProjectStatistics.ProjectStatusServiceType;
import static com.bees360.entity.enums.NewProjectStatusEnum.ASSIGNED_TO_PILOT;
import static com.bees360.entity.enums.NewProjectStatusEnum.CLIENT_RECEIVED;
import static com.bees360.entity.enums.NewProjectStatusEnum.CUSTOMER_CONTACTED;
import static com.bees360.entity.enums.NewProjectStatusEnum.IBEES_UPLOADED;
import static com.bees360.entity.enums.NewProjectStatusEnum.IMAGE_UPLOADED;
import static com.bees360.entity.enums.NewProjectStatusEnum.PENDING_ACCEPTANCE;
import static com.bees360.entity.enums.NewProjectStatusEnum.PROJECT_CANCELED;
import static com.bees360.entity.enums.NewProjectStatusEnum.PROJECT_CREATED;
import static com.bees360.entity.enums.NewProjectStatusEnum.PROJECT_REWORK;
import static com.bees360.entity.enums.NewProjectStatusEnum.RECEIVE_ERROR;
import static com.bees360.entity.enums.NewProjectStatusEnum.RETURNED_TO_CLIENT;
import static com.bees360.entity.enums.NewProjectStatusEnum.SITE_INSPECTED;

@Slf4j
@Service
public class DefaultProjectStatisticsService implements ProjectStatisticsService {

    private final ProjectMapper projectMapper;

    private final ProjectStatusMapper projectStatusMapper;

    private final CompanyMapper companyMapper;

    private final ProjectService projectService;

    private final RedisMap<String, Map<InspectionPurposeTypeEnum, ProjectStatisticSummary>> annualProjectStatsCache;

    public DefaultProjectStatisticsService(ProjectMapper projectMapper,
                                           ProjectStatusMapper projectStatusMapper,
                                           CompanyMapper companyMapper,
                                           ProjectService projectService, RedissonClient redissonClient) {
        this.projectMapper = projectMapper;
        this.projectStatusMapper = projectStatusMapper;
        this.companyMapper = companyMapper;
        this.projectService = projectService;
        this.annualProjectStatsCache =
            new RedisMap<>(redissonClient, "annualProjectStats", Duration.ofDays(6));
    }

    private static final Set<Integer> FINISHED_STATUS = Stream.of(RETURNED_TO_CLIENT, CLIENT_RECEIVED,
        RECEIVE_ERROR, PROJECT_CANCELED).map(NewProjectStatusEnum::getCode).collect(Collectors.toUnmodifiableSet());

    private static final Set<Integer> UNASSIGNED_STATUS = Stream.of(CUSTOMER_CONTACTED, PENDING_ACCEPTANCE)
        .map(NewProjectStatusEnum::getCode).collect(Collectors.toUnmodifiableSet());

    private static final Set<Integer> IMAGE_NOT_UPLOADED = Stream.of(ASSIGNED_TO_PILOT, PROJECT_REWORK,
        SITE_INSPECTED, IBEES_UPLOADED).map(NewProjectStatusEnum::getCode).collect(Collectors.toUnmodifiableSet());

    @Override
    public List<ProjectStatsPerCompany> projectStatusStatsPerCompany(Instant startDate, Instant endDate) {

        final List<Project> projects = getProjectOfInterval(startDate, endDate);
        if (projects.isEmpty()) {
            return new ArrayList<>();
        }

        final Map<Long, Long> projectCompanyIdMap = projects.stream()
            .collect(Collectors.toMap(Project::getProjectId, project ->
                Optional.ofNullable(project.getInsuranceCompany()).orElse(project.getRepairCompany()), (c0, c1) -> c0));
        final List<ProjectStatus> projectStatuses =
            projectStatusMapper.listByProjectIds(new ArrayList<>(projectCompanyIdMap.keySet()));
        final Map<Long, Company> companyIdMap = companyMapper.listIn(projectCompanyIdMap.values())
            .stream().collect(Collectors.toMap(Company::getCompanyId, Function.identity(), (c0, c1) -> c0));

        final Map<Long, List<NewProjectStatusEnum>> projectIdStatusMap =
            projectStatuses.stream().collect(Collectors.groupingBy(
                ProjectStatus::getProjectId, Collectors.mapping(ProjectStatus::getStatus, Collectors.mapping(
                    NewProjectStatusEnum::getEnum, Collectors.toList()
                ))
            ));

        final HashMap<Long, Map<InspectionPurposeTypeEnum, ProjectStatsPerCompany>> map = new HashMap<>();
        projects.forEach(project -> {

            final Long companyId = Optional.ofNullable(project.getInsuranceCompany()).orElse(project.getRepairCompany());
            final long projectId = project.getProjectId();
            InspectionPurposeTypeEnum serviceLine = getServiceLine(ProjectServiceTypeEnum.getEnum(project.getServiceType()));
            Map<InspectionPurposeTypeEnum, ProjectStatsPerCompany> subMap = map.get(companyId);
            ProjectStatsPerCompany stats;
            if (subMap == null) {
                subMap = new HashMap<>();
                map.put(companyId, subMap);
            }
            if ((stats = subMap.get(serviceLine)) == null) {
                final ProjectStatsPerCompany projectStatsPerCompany = new ProjectStatsPerCompany();
                Optional.ofNullable(companyIdMap.get(companyId))
                    .ifPresent(projectStatsPerCompany::setCompany);
                projectStatsPerCompany.setServiceType(serviceLine);
                subMap.put(serviceLine, projectStatsPerCompany);
                stats = projectStatsPerCompany;
            }

            final List<NewProjectStatusEnum> statuses = projectIdStatusMap.get(projectId);
            if (statuses == null) {
                log.warn("project has no statues info, projectId:{}", projectId);
                return;
            }
            if (statuses.contains(NewProjectStatusEnum.PROJECT_CREATED)) {
                stats.setProjectCreated(stats.getProjectCreated() + 1);
            }
            if (statuses.contains(NewProjectStatusEnum.ASSIGNED_TO_PILOT)) {
                stats.setProjectAssigned(stats.getProjectAssigned() + 1);
            }
            if (statuses.contains(NewProjectStatusEnum.CUSTOMER_CONTACTED)) {
                stats.setProjectContacted(stats.getProjectContacted() + 1);
            }
        });

        return map.values().stream().map(Map::values)
            .flatMap(Collection::stream).collect(Collectors.toList());
    }

    @Override
    public Map<InspectionPurposeTypeEnum, ProjectStatsPerCompanySummary>
        projectStatsSummary(Instant startDate, Instant endDate) {
        long startTime = startDate.toEpochMilli();
        long endTime = endDate.toEpochMilli();
        final List<ProjectStatsPerCompany> projectStatsPerCompany =
            this.projectStatusStatsPerCompany(startDate, endDate);
        final Map<InspectionPurposeTypeEnum, ProjectStatsPerCompanySummary> resultMap = new HashMap<>();
        resultMap.put(InspectionPurposeTypeEnum.CLAIM,
            ProjectStatsPerCompanySummary.emptySummary(InspectionPurposeTypeEnum.CLAIM,
                startTime, endTime));
        resultMap.put(
            InspectionPurposeTypeEnum.UNDERWRITING,
            ProjectStatsPerCompanySummary.emptySummary(InspectionPurposeTypeEnum.UNDERWRITING,
                startTime, endTime));
        if (projectStatsPerCompany.isEmpty()) {
            return resultMap;
        }

        final Map<InspectionPurposeTypeEnum, List<ProjectStatsPerCompany>> typeMap =
            projectStatsPerCompany.stream().collect(Collectors.groupingBy(ProjectStatsPerCompany::getServiceType));
        typeMap.entrySet().stream().map(entry -> toSummary(entry.getValue(), entry.getKey(), startDate, endDate))
            .forEach(summary -> {
                final InspectionPurposeTypeEnum serviceType = summary.getServiceType();
                resultMap.computeIfPresent(serviceType, (key, sum0) -> {
                    sum0.getPerCompanyStats().addAll(Optional.ofNullable(summary.getPerCompanyStats()).orElse(Collections.emptyList()));
                    sum0.setTotalProjectCreated(sum0.getTotalProjectCreated() + summary.getTotalProjectCreated());
                    sum0.setTotalProjectAssigned(sum0.getTotalProjectAssigned() + summary.getTotalProjectAssigned());
                    sum0.setTotalProjectContacted(sum0.getTotalProjectContacted() + summary.getTotalProjectContacted());
                    return sum0;
                });
            });
        return resultMap;
    }

    @Override
    public Map<InspectionPurposeTypeEnum, ProjectStatisticSummary> getStatisticsSummary(Instant startDate, Instant endDate) {
        long startTime = startDate.toEpochMilli();
        long endTime = endDate.toEpochMilli();
        final List<Project> projects = getProjectOfInterval(startDate, endDate);
        final Map<InspectionPurposeTypeEnum, Long> map = projects.stream().collect(Collectors.groupingBy(
            project -> getServiceLine(ProjectServiceTypeEnum.getEnum(project.getServiceType())),
            Collectors.counting()));
        long claimCount = Optional.ofNullable(map.get(InspectionPurposeTypeEnum.CLAIM)).orElse(0L);
        long underwritingCount = Optional.ofNullable(map.get(InspectionPurposeTypeEnum.UNDERWRITING)).orElse(0L);
        return Map.of(
            InspectionPurposeTypeEnum.CLAIM, new ProjectStatisticSummary(InspectionPurposeTypeEnum.CLAIM,
                startTime, endTime, claimCount),
            InspectionPurposeTypeEnum.UNDERWRITING, new ProjectStatisticSummary(InspectionPurposeTypeEnum.UNDERWRITING,
                startTime, endTime, underwritingCount)
        );
    }

    private static String intervalKey(long startTime, long endTime) {
        return startTime + "~" + endTime;
    }

    @Override
    public List<Map<InspectionPurposeTypeEnum, ProjectStatisticSummary>>
        getIntervalStatisticsSummary(LocalDate startDate, LocalDate endDate, ZoneId zoneId, int intervalDay) {
        final long days = endDate.toEpochDay() - startDate.toEpochDay();
        if (days < intervalDay || intervalDay < 1) {
            return List.of(getStatisticsSummary(DateUtil.toInstant(startDate.atTime(LocalTime.MIN), zoneId),
                DateUtil.toInstant(endDate.atTime(LocalTime.MAX), zoneId)));
        }

        final List<ProjectStatistics.TimeInterval> list =
            new ArrayList<>((int) Math.ceil(((float) days) / intervalDay));

        for (LocalDate startPointer = startDate, endPointer = startDate.plusDays(intervalDay - 1);
             endDate.isAfter(startPointer) || endDate.isEqual(startPointer);
             startPointer = startPointer.plusDays(intervalDay), endPointer = endPointer.plusDays(intervalDay)) {
            endPointer = endPointer.isAfter(endDate) ? endDate : endPointer;
            list.add(new ProjectStatistics.TimeInterval(DateUtil.toInstant(startPointer.atTime(LocalTime.MIN), zoneId).toEpochMilli(),
                DateUtil.toInstant(endPointer.atTime(LocalTime.MAX), zoneId).toEpochMilli()));
        }

        final ProjectStatistics.TimeInterval latestInterval = list.remove(list.size() - 1);
        final List<Map<InspectionPurposeTypeEnum, ProjectStatisticSummary>> resultList =
            list.stream().map(this::retrievedCache).collect(Collectors.toList());
        resultList.add(getStatisticsSummary(Instant.ofEpochMilli(latestInterval.getStartDate()),
            Instant.ofEpochMilli(latestInterval.getEndDate())));
        return resultList;
    }

    private Map<InspectionPurposeTypeEnum, ProjectStatisticSummary>
            retrievedCache(ProjectStatistics.TimeInterval timeInterval) {
        final long startDate = timeInterval.getStartDate();
        final long endDate = timeInterval.getEndDate();
        final String key = intervalKey(startDate, endDate);
        Map<InspectionPurposeTypeEnum, ProjectStatisticSummary> map = annualProjectStatsCache.get(key);
        if (map == null) {
            map = getStatisticsSummary(Instant.ofEpochMilli(startDate), Instant.ofEpochMilli(endDate));
            annualProjectStatsCache.put(key, map);
        }
        return map;
    }

    @Override
    public Map<InspectionPurposeTypeEnum, ProjectLatestStatusMetrics> getLatestProjectStatusMetrics() {

        final List<ProjectStatusServiceType> records =
            projectMapper.listProjectStatusView(true, true, FINISHED_STATUS);

        final Set<Integer> claimCodes = ProjectServiceTypeEnum.getClaimCodes();
        final Set<Integer> underwritingCodes = ProjectServiceTypeEnum.getUnderwritingCodes();
        final ProjectLatestStatusMetrics.Builder claimStats =
            ProjectLatestStatusMetrics.builder().inspectionType(InspectionPurposeTypeEnum.CLAIM);
        final ProjectLatestStatusMetrics.Builder underwritingStats =
            ProjectLatestStatusMetrics.builder().inspectionType(InspectionPurposeTypeEnum.UNDERWRITING);
        final Set<Long> ibeesUploadedClaim = Sets.newConcurrentHashSet();
        final Set<Long> ibeesUploadedUnderwriting = Sets.newConcurrentHashSet();

        records.stream().parallel().forEach(record -> {
            final int serviceType = record.getServiceType();
            final int projectStatus = record.getProjectStatus();

            if (claimCodes.contains(serviceType)) {
                if (projectStatus == IBEES_UPLOADED.getCode()) {
                    ibeesUploadedClaim.add(record.getProjectId());
                }
                doCount(projectStatus, claimStats);
            } else if (underwritingCodes.contains(serviceType)) {
                if (projectStatus == IBEES_UPLOADED.getCode()) {
                    ibeesUploadedUnderwriting.add(record.getProjectId());
                }
                doCount(projectStatus, underwritingStats);
            }
        });

        handleIbeesUploadedAfterImageUploaded(ibeesUploadedClaim, claimStats);
        handleIbeesUploadedAfterImageUploaded(ibeesUploadedUnderwriting, underwritingStats);
        checkAndCountFollowUp(records, claimStats);
        return Map.of(InspectionPurposeTypeEnum.CLAIM, claimStats.build(),
            InspectionPurposeTypeEnum.UNDERWRITING, underwritingStats.build());
    }

    private static void doCount(int projectStatus, ProjectLatestStatusMetrics.Builder accumulator) {
        if (projectStatus == PROJECT_CREATED.getCode()) {
            accumulator.getNotContacted().incrementAndGet();
            accumulator.getUnassigned().incrementAndGet();
        } else if (UNASSIGNED_STATUS.contains(projectStatus)) {
            accumulator.getUnassigned().incrementAndGet();
        } else if (projectStatus == IMAGE_UPLOADED.getCode()) {
            accumulator.getInProcess().incrementAndGet();
        } else if (IMAGE_NOT_UPLOADED.contains(projectStatus)) {
            accumulator.getImageNotUnloaded().incrementAndGet();
        }
    }


    private void checkAndCountFollowUp(List<ProjectStatusServiceType> records,
                                       ProjectLatestStatusMetrics.Builder accumulator) {
        HashSet<Long> projectIds = new HashSet<>(projectService.getProjectIdsOfFollowUp());
        records.forEach(record -> {
            if (projectIds.contains(record.getProjectId())) {
                accumulator.getFollowUp().incrementAndGet();
            }
        });
    }

    private void handleIbeesUploadedAfterImageUploaded(Set<Long> ibeesUploaded,
                                                              ProjectLatestStatusMetrics.Builder accumulator) {
        long ibeesUploadedAfterImageUploaded = 0;
        if (!ibeesUploaded.isEmpty()) {
            ibeesUploadedAfterImageUploaded =
                projectStatusMapper.listByProjectIds(new ArrayList<>(ibeesUploaded)).stream()
                    .filter(projectStatus -> IMAGE_UPLOADED.getCode() == projectStatus.getStatus())
                    .map(ProjectStatus::getProjectId)
                    .distinct()
                    .count();
        }

        accumulator.getInProcess().addAndGet(ibeesUploadedAfterImageUploaded);
        final long origin = accumulator.getImageNotUnloaded().get();
        if (origin >= ibeesUploadedAfterImageUploaded) {
            accumulator.getImageNotUnloaded().addAndGet(-ibeesUploadedAfterImageUploaded);
        } else {
            log.warn("There are more projects of IBEES_UPLOADED than that of IMAGE_NOT_UPLOADED, " +
                "ibeesUploadedAfterImageUploaded number: {}", ibeesUploadedAfterImageUploaded);
        }
    }


    private List<Project> getProjectOfInterval(Instant startDate, Instant endDate) {
        return projectMapper.listInTimeRange(startDate.toEpochMilli(),
                endDate.toEpochMilli())
            .stream()
            .filter(project -> !project.isTestFlag())
            .filter(project -> project.getInsuranceCompany() != null || project.getRepairCompany() != null)
            .filter(project -> project.getServiceType() != null)
            .collect(Collectors.toList());
    }

    private ProjectStatsPerCompanySummary toSummary(List<ProjectStatsPerCompany> projectStats,
                                                    InspectionPurposeTypeEnum typeEnum,
                                                    Instant startDate, Instant endDate) {
        final AtomicInteger totalCreated = new AtomicInteger(0);
        final AtomicInteger totalAssigned = new AtomicInteger(0);
        final AtomicInteger totalContacted = new AtomicInteger(0);
        projectStats.forEach(stat -> {
            totalCreated.addAndGet(stat.getProjectCreated());
            totalAssigned.addAndGet(stat.getProjectAssigned());
            totalContacted.addAndGet(stat.getProjectContacted());
        });
        return new ProjectStatsPerCompanySummary(typeEnum, startDate.toEpochMilli(), endDate.toEpochMilli(),
            projectStats, totalCreated.get(), totalAssigned.get(), totalContacted.get());
    }


    private static InspectionPurposeTypeEnum getServiceLine(ProjectServiceTypeEnum serviceTypeEnum) {
        if (serviceTypeEnum == null) {
            return null;
        }
        final InspectionPurposeTypeEnum inspectionPurposeType = serviceTypeEnum.getInspectionPurposeType();
        if (InspectionPurposeTypeEnum.CLAIM.equals(inspectionPurposeType)) {
            return InspectionPurposeTypeEnum.CLAIM;
        } else if (InspectionPurposeTypeEnum.UNDERWRITING.equals(inspectionPurposeType)) {
            return InspectionPurposeTypeEnum.UNDERWRITING;
        } else {
            return null;
        }
    }

}
