package com.bees360.service;

import java.util.List;
import java.util.Map;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.Permission;

public interface PermissionCacheService {

	/**
	 * get all permissions
	 * @return
	 * @throws ServiceException
	 */
	public List<Permission> getPermissions() throws ServiceException;

	/**
	 * get all permission in map structure
	 * @return
	 * @throws ServiceException
	 */
	public Map<Long, Permission> getPermissionMap() throws ServiceException;
}
