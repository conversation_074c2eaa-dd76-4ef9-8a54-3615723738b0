package com.bees360.service.statistics;

import static com.bees360.entity.dto.ProjectStatistics.*;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;


public interface ProjectStatisticsService {

    List<ProjectStatsPerCompany> projectStatusStatsPerCompany(Instant startDate, Instant endDate);

    Map<InspectionPurposeTypeEnum, ProjectStatsPerCompanySummary> projectStatsSummary(Instant startDate, Instant endDate);

    Map<InspectionPurposeTypeEnum, ProjectStatisticSummary> getStatisticsSummary(Instant startDate, Instant endDate);

    List<Map<InspectionPurposeTypeEnum, ProjectStatisticSummary>>
            getIntervalStatisticsSummary(LocalDate startDate, LocalDate endDate, ZoneId zoneId, int intervalDay);

    Map<InspectionPurposeTypeEnum, ProjectLatestStatusMetrics> getLatestProjectStatusMetrics();
}
