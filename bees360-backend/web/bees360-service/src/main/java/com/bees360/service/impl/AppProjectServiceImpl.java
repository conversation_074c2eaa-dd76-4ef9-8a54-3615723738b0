package com.bees360.service.impl;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.HouseImageSegmentType;
import com.bees360.mapper.HouseImageSegmentTypeMapper;
import com.bees360.service.AppProjectService;
import com.bees360.util.ConstantUtil;
import com.bees360.util.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.List;

@Service("appProjectService")
public class AppProjectServiceImpl implements AppProjectService {

	private Logger logger = LoggerFactory.getLogger(AppProjectServiceImpl.class);

	@Inject
	private HouseImageSegmentTypeMapper houseImageSegmentTypeMapper;

	@Autowired
	private RedisUtil redisUtil;

	/**
	 * set SegmentType to redis
	 * @throws ServiceException
	 */
	private List<HouseImageSegmentType> setSegmentType() throws ServiceException {
		List<HouseImageSegmentType> segmentTypeList = houseImageSegmentTypeMapper.getImageSegmentTypes();
		setDataToRedis(ConstantUtil.SEGMENT_TYPE_CACHE_KEY, segmentTypeList);
		return segmentTypeList;
	}

	/**
	 * get SegmentType to redis
	 * @return
	 * @throws ServiceException
	 */
	@Override
	public List<HouseImageSegmentType> listSegmentType() throws ServiceException {
		List<HouseImageSegmentType> segmentTypeList = listDataFromRedis(ConstantUtil.SEGMENT_TYPE_CACHE_KEY,
				HouseImageSegmentType.class);
		if (null == segmentTypeList) {
			segmentTypeList = setSegmentType();
		}
		return segmentTypeList;
	}

	/**
	 * add data to redis
	 *
	 * @param cacheKey the redis data key
	 * @param t the redis data value
	 * @param <T> the type
	 * @throws ServiceException Data settings failed.
	 */
	private <T> void setDataToRedis(String cacheKey, T t) throws ServiceException {
		try {
			redisUtil.setDataToRedis(cacheKey, t);
		} catch (Exception e) {
			logger.error("set redis failed, cacheKey:{}, data:{}", cacheKey, t.toString(), e);
			throw new ServiceException(MessageCode.REDIS_OPERATION_FAILED);
		}
	}

	/**
	 * list data from redis
	 *
	 * @param cacheKey the redis data key
	 * @param clazz the type
	 * @param <T> the type
	 * @return the data
	 * @throws ServiceException Data list failed.
	 */
	private <T> List<T> listDataFromRedis(String cacheKey, Class<T> clazz) throws ServiceException {
		List<T> list;
		try {
            list = redisUtil.listDataFromRedis(cacheKey, clazz);
		} catch (Exception e) {
			logger.error("list data from redis failed:", e);
			throw new ServiceException(MessageCode.REDIS_OPERATION_FAILED);
		}
		return list;
	}
}
