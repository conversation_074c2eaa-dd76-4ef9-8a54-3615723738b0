package com.bees360.event;

import com.bees360.event.registry.ProjectCreate;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.policy.Coverage;
import com.bees360.policy.Message.CoverageType;
import com.bees360.policy.Policy;
import com.bees360.project.ProjectIIManager;
import com.bees360.service.CommentService;
import com.bees360.util.Iterables;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * 该类是一个事件监听器，当项目创建时自动添加保单覆盖范围的评论内容到项目中。
 */
public class AddPolicyCoverageActivityOnProjectCreated extends AbstractNamedEventListener<ProjectCreate> {

    private final CommentService commentService;

    private final ProjectIIManager projectIIManager;

    private static final DecimalFormat decimalFormat = new DecimalFormat("###,###.00");

    public AddPolicyCoverageActivityOnProjectCreated(CommentService commentService, ProjectIIManager projectIIManager) {
        this.commentService = commentService;
        this.projectIIManager = projectIIManager;
    }

    @Override
    public void handle(ProjectCreate projectCreate) throws IOException {
        var project = projectCreate.getProject();
        if (project == null) {
            return;
        }

        var projectII = projectIIManager.findById(project.getId());
        var policy = projectII.getPolicy();
        Optional.ofNullable(policy)
                .map(Policy::getCoverage)
                .ifPresent(
                    coverages -> {
                        var coverageList = Iterables.toList(coverages);
                        if (CollectionUtils.isNotEmpty(coverageList)) {
                            var content = createCommentContent(coverageList);
                            commentService.addComment(
                                Long.parseLong(project.getId()),
                                project.getCreateBy().getId(),
                                content,
                                Instant.now().toEpochMilli());
                        }
                });
    }

    private String createCommentContent(List<? extends Coverage> coverages) {
        BigDecimal dwelling = null;
        BigDecimal otherStructure = null;
        BigDecimal contents = null;
        for (Coverage coverage : coverages) {
            if(StringUtils.equals(coverage.getType(), CoverageType.DWELLING.name())) {
                dwelling = coverage.getAmount();
            } else if (StringUtils.equals(coverage.getType(), CoverageType.OTHER_STRUCTURE.name())) {
                otherStructure = coverage.getAmount();
            } else if (StringUtils.equals(coverage.getType(), CoverageType.CONTENT.name())) {
                contents = coverage.getAmount();
            }
        }

        return getCoverageComment().formatted(
            formatNumber(dwelling),
            formatNumber(otherStructure),
            formatNumber(contents));
    }

    private String getCoverageComment() {
        return """
                Coverage:
                Main dwelling: %s
                Other Structure: %s
                Contents: %s""";
    }

    private String formatNumber(BigDecimal decimal) {
        return decimal == null ? "N/A" : "$" + decimalFormat.format(decimal);
    }
}
