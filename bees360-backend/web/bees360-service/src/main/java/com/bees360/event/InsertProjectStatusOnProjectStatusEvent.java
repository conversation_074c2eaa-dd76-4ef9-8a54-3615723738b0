package com.bees360.event;

import com.bees360.entity.ProjectStatus;
import com.bees360.event.registry.ProjectStatusHistoryInserted;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectStatusMapper;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.UserService;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听项目状态变更事件并插入新的项目状态记录到数据库
 */
@Log4j2
public class InsertProjectStatusOnProjectStatusEvent extends AbstractNamedEventListener<ProjectStatusHistoryInserted> {

    private final UserService userService;

    private final ProjectStatusMapper projectStatusMapper;

    private final ProjectStatusService projectStatusService;

    public InsertProjectStatusOnProjectStatusEvent(
        UserService userService,
        ProjectStatusMapper projectStatusMapper,
        ProjectStatusService projectStatusService) {
        this.userService = userService;
        this.projectStatusMapper = projectStatusMapper;
        this.projectStatusService = projectStatusService;
    }

    @Override
    public void handle(ProjectStatusHistoryInserted event) throws IOException {
        var projectId = Long.parseLong(event.getProjectId());
        var updatedAt = event.getUpdatedAt().toEpochMilli();
        var status = event.getStatus();

        var userId = userService.toWebUserId(event.getUpdatedBy());
        if(userId == null) {
            var msg = String.format("Failed to update project %s status " +
                "due to user %s not exists in web.", projectId, event.getUpdatedBy());
            throw new IllegalStateException(msg);
        }

        ProjectStatus projectStatus = new ProjectStatus();
        projectStatus.setUserId(userId);
        projectStatus.setProjectId(projectId);
        projectStatus.setStatus(status);
        projectStatus.setCreatedTime(updatedAt);
        projectStatusMapper.insert(projectStatus);
        publishEvent(projectStatus);
        log.info("Successfully insert project status with event {}", event);
    }

    /**
     * 发出相应状态的application event ProjectStatusEvent
     */
    private void publishEvent(ProjectStatus projectStatus) {
        projectStatusService.publishProjectStatusChangeEvent(projectStatus, false);
    }
}
