package com.bees360.service.beespilot.impl;

import static com.bees360.base.Constants.OPERATION_MANAGER_GROUP_ID_PREFIX;

import com.bees360.base.MessageCode;
import com.bees360.base.exception.ServiceException;
import com.bees360.entity.BeesBatchQuery;
import com.bees360.entity.BeesPilotBatch;
import com.bees360.entity.BeesPilotBatchItem;
import com.bees360.entity.BeesPilotBatchRequest;
import com.bees360.entity.BsExportData;
import com.bees360.entity.DailyBatchProjectModel;
import com.bees360.entity.DailyBatchStatusModel;
import com.bees360.entity.Member;
import com.bees360.entity.PilotProjectModel;
import com.bees360.entity.Project;
import com.bees360.entity.SchedulePilotDto;
import com.bees360.entity.User;
import com.bees360.entity.dto.MemberSchedule;
import com.bees360.entity.enums.BatchStatusResult;
import com.bees360.entity.enums.BsExportDataRelatedTypeEnum;
import com.bees360.entity.enums.NewProjectStatusEnum;
import com.bees360.entity.enums.PilotBatchEventTypeEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.firebase.BatchStatusEnum;
import com.bees360.entity.query.ProjectFilterQuery;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.event.EventPublisher;
import com.bees360.mapper.BeesPilotBatchMapper;
import com.bees360.mapper.MemberMapper;
import com.bees360.mapper.ProjectMapper;
import com.bees360.pipeline.PipelineService;
import com.bees360.project.member.MemberManager;
import com.bees360.service.ActivityService;
import com.bees360.service.BsExportDataService;
import com.bees360.service.CompanyService;
import com.bees360.service.EventHistoryService;
import com.bees360.service.MessageService;
import com.bees360.service.NotificationService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.beespilot.BeesPilotBatchItemService;
import com.bees360.service.beespilot.BeesPilotBatchService;
import com.bees360.service.firebase.FirebaseService;
import com.bees360.service.member.PostgresMemberManagerAdapter;
import com.bees360.user.Group;
import com.bees360.user.GroupProvider;
import com.bees360.user.UserProvider;
import com.bees360.util.DateUtil;
import com.bees360.util.Iterables;
import com.bees360.util.SequenceGenerator;
import com.bees360.util.report.CommonsUtil;
import com.bees360.util.user.Bees360UserUtils;
import com.bees360.util.user.UserAssemble;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class BeesPilotBatchServiceImpl implements BeesPilotBatchService {

    @Autowired private BeesPilotBatchItemService beesPilotBatchItemService;

    @Autowired private SequenceGenerator sequenceGenerator;

    @Autowired private BeesPilotBatchMapper beesPilotBatchMapper;

    @Autowired private ApplicationEventPublisher publisher;

    @Autowired private MemberMapper memberMapper;

    @Autowired private ProjectMapper projectMapper;

    @Autowired private ProjectStatusService projectStatusService;

    @Autowired private MessageService messageService;

    @Autowired private CompanyService companyService;

    @Autowired private NotificationService notificationService;

    @Autowired private ProjectService projectService;

    @Autowired private FirebaseService firebaseService;
    @Autowired private EventHistoryService eventHistoryService;

    @Autowired private BsExportDataService bsExportDataService;
    @Autowired private ActivityService activityService;

    @Autowired private UserProvider userProvider;

    @Autowired private GroupProvider groupProvider;
    @Autowired private EventPublisher eventPublisher;
    @Autowired private PipelineService pipelineService;
    @Autowired private MemberManager memberManager;
    @Autowired private PostgresMemberManagerAdapter postgresMemberManagerAdapter;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createPilotBatchTask(
            long creatorId, String pilotId, SchedulePilotDto schedulePilotDto)
            throws ServiceException {

        com.bees360.user.User pilotUser = userProvider.findUserById(pilotId);
        User pilot = UserAssemble.toWebUser(pilotUser);
        if (pilot == null) {
            throw new IllegalStateException("User with id:" + pilotId + " is not found");
        }

        // 设置将飞手更新到项目的member中，并更新项目状态(这里并没有处理batch的相关逻辑，batch由firebase的listener处理)
       doAddMemberAndChangeProjectStatus(
                pilotId,
                creatorId,
                schedulePilotDto.getProjectIds(),
                pilot,
                schedulePilotDto.getIsPendingAcceptance(),
                schedulePilotDto.getVersion());
    }

    public static com.bees360.user.User getOmFromPilot(
            GroupProvider groupProvider, UserProvider userProvider, String pilotId) {
        // add OperationsManager to member
        Iterable<? extends Group> groups = groupProvider.findGroupByUserId(pilotId);
        Group pilotOmGroup =
                Iterables.toStream(groups)
                        .filter(
                                g ->
                                        Optional.ofNullable(g.getId())
                                                .map(
                                                        s ->
                                                                s.startsWith(
                                                                        OPERATION_MANAGER_GROUP_ID_PREFIX))
                                                .orElse(false))
                        .findAny()
                        .orElse(null);
        if (pilotOmGroup == null) {
            log.warn("The pilot '{}' does not have a operations manager", pilotId);
            return null;
        }
        String omUserIdStr =
                Optional.ofNullable(pilotOmGroup.getId())
                        .map(s -> s.replaceFirst(OPERATION_MANAGER_GROUP_ID_PREFIX, Strings.EMPTY))
                        .orElse(Strings.EMPTY);
        return userProvider.findUserById(omUserIdStr);
    }

    @Override
    public synchronized void updateOrAddBatchDirectly(
            BeesPilotBatch beesPilotBatch, List<Long> activeProjects) {
        BeesPilotBatch oldBatch = beesPilotBatchMapper.getByBatchNo(beesPilotBatch.getBatchNo());
        if (oldBatch != null) {
            publishBatchEvent(oldBatch, beesPilotBatch, activeProjects);
        }
        if (beesPilotBatch.isDeleted()
                || Objects.equals(
                        beesPilotBatch.getStatus(), BatchStatusEnum.REJECTED.getStatus())) {
            beesPilotBatchMapper.deleteByBatchNo(beesPilotBatch.getBatchNo());
            return;
        }
        if (CollectionUtils.isEmpty(activeProjects)) {
            return;
        }

        // 更新batch
        if (oldBatch != null) {
            beesPilotBatchMapper.updatePilotBatch(ofBeesPilotBatchRequest(beesPilotBatch));
        }
        // 新增batch
        else {
            beesPilotBatchMapper.insert(beesPilotBatch);
            List<BeesPilotBatchItem> batchItems =
                    activeProjects.stream()
                            .map(item -> new BeesPilotBatchItem(beesPilotBatch.getBatchNo(), item))
                            .collect(Collectors.toList());
            beesPilotBatchItemService.addBeesPilotBatchItem(batchItems);
        }
    }

    private void publishBatchEvent(
            BeesPilotBatch oldBatch, BeesPilotBatch newBatch, List<Long> activeProjects) {
        String batchNo = newBatch.getBatchNo();
        if (oldBatch != null && !oldBatch.isDeleted()) {
            BigDecimal oldBatchAmount = oldBatch.getBasePay();
            boolean acceptBatch =
                    oldBatch.getStatus() == BatchStatusEnum.PENDING.getStatus()
                            && newBatch.getStatus() == BatchStatusEnum.ACCEPTED.getStatus();
            boolean rejectedBatch =
                    oldBatch.getStatus() == BatchStatusEnum.PENDING.getStatus()
                            && newBatch.getStatus() == BatchStatusEnum.REJECTED.getStatus();

            for (Long projectId : activeProjects) {
                if (acceptBatch) {
                    activityService.acceptBatch(projectId, newBatch.getUserId(), batchNo);
                }

                if (rejectedBatch) {
                    activityService.rejectBatch(
                            projectId, oldBatch.getUserId(), batchNo, newBatch.getReason());
                }
            }
        }
    }

    private BeesPilotBatchRequest ofBeesPilotBatchRequest(BeesPilotBatch batch) {
        BeesPilotBatchRequest request = new BeesPilotBatchRequest();
        request.setBasePay(batch.getBasePay());
        request.setExtraPay(batch.getExtraPay());
        request.setPlanPaymentDate(batch.getPlanPaymentDate());
        request.setNote(batch.getNote());
        request.setBatchNo(batch.getBatchNo());
        request.setDueDate(batch.getDueDate());
        request.setIsPendingAcceptance(batch.getStatus() == BatchStatusEnum.PENDING.getStatus());
        request.setStatus(batch.getStatus());
        request.setDeleted(batch.isDeleted());
        return request;
    }

    @Override
    public BatchStatusResult getBatchStatusResource(BeesBatchQuery query) throws ServiceException {
        BatchStatusResult result = new BatchStatusResult();
        List<BeesPilotBatch> batches = listPendingBatch(query.getPilotId());
        result.setHasPendingProject(batches.size() > 0);
        List<Long> projectIds = new ArrayList<>();
        for (BeesPilotBatch batch : batches) {
            List<BeesPilotBatchItem> items =
                    beesPilotBatchItemService.listByBatchNo(batch.getBatchNo());
            projectIds.addAll(
                    items.stream()
                            .map(BeesPilotBatchItem::getProjectId)
                            .collect(Collectors.toList()));
        }
        result.setProjectIds(projectIds);
        return result;
    }

    private List<BeesPilotBatch> listPendingBatch(long pilotId) {
        BeesBatchQuery query = new BeesBatchQuery();
        query.setDeleted(false);
        query.setPilotId(pilotId);
        query.setStatus(BatchStatusEnum.PENDING.getStatus());
        List<BeesPilotBatch> list = beesPilotBatchMapper.listBatch(query);
        return list;
    }

    @Override
    public void cancelAssignedAndRemovePilot(long operator, long projectId)
            throws ServiceException {
        doCancelAssignedAndRemovePilot(operator, projectId, true);
    }

    private void cancelAssignedAndRemovePilotNoActivity(long operator, long projectId)
            throws ServiceException {
        doCancelAssignedAndRemovePilot(operator, projectId, false);
    }

    @Override
    @Transactional
    public void cancelAssignedAndRemovePilot(long operator, List<Long> projectIds, @Nullable Long version)  {
        for (Long projectId : projectIds) {
            var projectStr = String.valueOf(projectId);
            var opUserId = String.valueOf(operator);
            try {
                var result =
                    memberManager.removeMember(projectStr, com.bees360.project.member.RoleEnum.PILOT, opUserId, version);
                if (!result){
                    log.info("Skipped to remove pilot from project '{}'.", projectId);
                    continue;
                }
                cancelAssignedAndRemovePilotNoActivity(operator, projectId);
            } catch (ServiceException e) {
                log.error("Failed to remove pilot for project '{}' at version {}.", projectId,version, e);
            }
        }
    }

    private void doCancelAssignedAndRemovePilot(long operator, long projectId, boolean sendActivity)
            throws ServiceException {
        final Member member =
                memberMapper.getActiveMemberByRole(projectId, RoleEnum.PILOT.getCode());
        if (sendActivity && member != null) {
            activityService.cancelAssignedPilot(projectId, operator, member.getUserId());
        }
        // 删除batch相关信息
        beesPilotBatchItemService.deleteByProjectIds(Collections.singletonList(projectId));
        MemberSchedule.MemberScheduleItem memberItem = new MemberSchedule.MemberScheduleItem();
        memberItem.setRoleId(RoleEnum.PILOT.getRoleId());
        projectService.changeMember(operator, projectId, memberItem);
        projectStatusService.changeOnCancelPilot(operator, projectId);
    }

    @Override
    public void updatePilotBatchDeletedStatus(String batchNo, Boolean isDeleted) {
        // 生成序列号
        if (org.apache.commons.lang3.StringUtils.isBlank(batchNo)) {
            return;
        }
        // 创建批量任务相关信息
        beesPilotBatchMapper.updatePilotBatchDeletedStatus(batchNo, isDeleted);
    }

    @Override
    public void weekSendPilotTaskNeedPayedEmailToAdmin(
            LocalDate curDate, LocalDate startDate, LocalDate endDate) {

        List<BeesPilotBatch> batchList =
                beesPilotBatchMapper.getByPlanPaymentTime(startDate, endDate);
        if (CollectionUtils.isEmpty(batchList)) {
            return;
        }
        List<DailyBatchProjectModel> projectModelList = new ArrayList<>();
        List<DailyBatchStatusModel> statusModelList = new ArrayList<>();
        batchList.forEach(
                batch -> {
                    try {
                        List<BeesPilotBatchItem> itemList =
                                beesPilotBatchItemService.listByBatchNo(batch.getBatchNo());
                        if (CollectionUtils.isEmpty(itemList)) {
                            return;
                        }
                        List<Project> projectList =
                                projectMapper.listProjects(
                                        itemList.stream()
                                                .map(BeesPilotBatchItem::getProjectId)
                                                .collect(Collectors.toList()));
                        if (CollectionUtils.isNotEmpty(projectList)) {
                            String inspectionTime = null;
                            Optional<Long> optional =
                                    projectList.stream()
                                            .map(Project::getInspectionTime)
                                            .filter(Objects::nonNull)
                                            .max(Long::compareTo);
                            if (optional.isPresent()) {
                                inspectionTime =
                                        DateUtil.convertDate(
                                                optional.get(),
                                                "MM/dd/yyyy",
                                                ZoneId.of(CommonsUtil.TIME_ZONE_US_CENTER));
                            }
                            // see #3412 taskCompleted定义改为是否已达到returnToClient状态下
                            boolean taskCompleted =
                                    projectList.stream()
                                            .map(Project::getProjectStatus)
                                            .map(NewProjectStatusEnum::isProjectReturnToClient)
                                            .noneMatch(o -> Objects.equals(o, false));
                            DailyBatchProjectModel projectModel =
                                    DailyBatchProjectModel.builder()
                                            .pilotId(batch.getUserId())
                                            .pilotName(
                                                    Optional.ofNullable(
                                                                    userProvider.findUserById(
                                                                            batch.getUserId() + ""))
                                                            .map(com.bees360.user.User::getName)
                                                            .orElse(null))
                                            .projectCount(projectList.size())
                                            .basePay(batch.getBasePay())
                                            .extraPay(batch.getExtraPay())
                                            .totalPay(
                                                    Optional.ofNullable(batch.getExtraPay())
                                                            .map(o -> batch.getBasePay().add(o))
                                                            .orElse(batch.getBasePay()))
                                            .playPaymentDate(
                                                    Optional.ofNullable(batch.getPlanPaymentDate())
                                                            .map(
                                                                    o ->
                                                                            com.bees360.common.util
                                                                                    .DateUtil
                                                                                    .formatLocalDate(
                                                                                            o,
                                                                                            "MM/dd/yyyy"))
                                                            .orElse(""))
                                            .dueDate(inspectionTime)
                                            .taskCompleted(taskCompleted ? "Y" : "N")
                                            .batchNo(batch.getBatchNo())
                                            .build();
                            projectModelList.add(projectModel);
                            projectList.forEach(
                                    project -> {
                                        DailyBatchStatusModel statusModel =
                                                DailyBatchStatusModel.builder()
                                                        .batchNo(batch.getBatchNo())
                                                        .projectId(project.getProjectId())
                                                        .serviceTypeName(
                                                                project.getServiceTypeName())
                                                        .returnToClient(
                                                                NewProjectStatusEnum
                                                                                .isProjectReturnToClient(
                                                                                        project
                                                                                                .getProjectStatus())
                                                                        ? "Y"
                                                                        : "N")
                                                        .build();
                                        statusModelList.add(statusModel);
                                    });
                        }
                    } catch (ServiceException e) {
                        log.error(
                                "weekSendPilotTaskNeedPayedEmailToAdmin batchNo:{}, error:{}, msg:{}",
                                batch.getBatchNo(),
                                e.getStackTrace(),
                                e.getMessage());
                    }
                });
        List<DailyBatchProjectModel> pilotBatchSortList =
                projectModelList.stream()
                        .sorted(Comparator.comparing(DailyBatchProjectModel::getPilotId))
                        .collect(Collectors.toList());
        List<String> adminEmails =
                bsExportDataService
                        .getExportData(
                                null, BsExportDataRelatedTypeEnum.WEEK_NOTIFY_ADMIN_EMAIL.getType())
                        .stream()
                        .map(BsExportData::getDataLog)
                        .collect(Collectors.toList());
        messageService.weekSendPilotTaskNeedPayedEmailToAdmin(
                adminEmails,
                com.bees360.common.util.DateUtil.formatLocalDate(curDate, "MM/dd/yyyy"),
                com.bees360.common.util.DateUtil.formatLocalDate(startDate, "MM/dd/yyyy"),
                com.bees360.common.util.DateUtil.formatLocalDate(endDate, "MM/dd/yyyy"),
                pilotBatchSortList,
                statusModelList);
    }

    @Override
    public void notifyPilotAfterBatchCreated(
            long pilotId, BeesPilotBatch pilotBatch, PilotBatchEventTypeEnum batchEventTypeEnum)
            throws ServiceException {
        if (!Objects.equals(batchEventTypeEnum, PilotBatchEventTypeEnum.CREATED)) {
            return;
        }
        // 发送邮件进行通知
        // UserTinyVo infoRecipient = new UserTinyVo(pilot, RoleEnum.PILOT);
        com.bees360.user.User pilot = userProvider.findUserById(pilotId + "");
        UserTinyVo infoRecipient = Bees360UserUtils.toUserTinyVo(pilot, RoleEnum.PILOT);
        if (CollectionUtils.isEmpty(pilotBatch.getBatchItems())) {
            throw new ServiceException(MessageCode.PARAM_INVALID);
        }

        List<Project> projects =
                pilotBatch.getBatchItems().stream()
                        .map(o -> projectMapper.getById(o.getProjectId()))
                        .collect(Collectors.toList());
        setInsuredBy(projects);
        messageService.sendToPilotArranged(infoRecipient, projects, pilotBatch);
        notificationService.infoMemberArranged(infoRecipient, RoleEnum.PILOT, projects);
    }

    private void setInsuredBy(List<Project> projects) {
        projects.forEach(
                o -> {
                    Optional.ofNullable(o.getInsuranceCompany())
                            .map(
                                    id -> companyService.getById(id))
                            .ifPresent(
                                    company -> o.setInsuredBy(company.getCompanyName()));
                });
    }

    @Override
    public void dailySendPilotTaskEmail() {
        ProjectFilterQuery projectFilterQuery = new ProjectFilterQuery();
        projectFilterQuery.addProjectStatuses(NewProjectStatusEnum.SITE_INSPECTED.getCode());
        List<Project> projects = projectMapper.listProjectsWithFilter(projectFilterQuery);
        if (CollectionUtils.isEmpty(projects)) {
            return;
        }
        setInsuredBy(projects);
        List<PilotProjectModel> pilotProjectModels =
                projects.stream()
                        .map(
                                project -> {
                                    Member member =
                                            memberMapper.getActiveMemberByRole(
                                                    project.getProjectId(),
                                                    RoleEnum.PILOT.getCode());
                                    if (Objects.isNull(member)) {
                                        return null;
                                    }
                                    return new PilotProjectModel(member.getUserId(), project);
                                })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        Map<Long, List<PilotProjectModel>> pilotProjectMap =
                pilotProjectModels.stream()
                        .collect(Collectors.groupingBy(PilotProjectModel::getPilotId));
        if (MapUtils.isEmpty(pilotProjectMap)) {
            return;
        }

        pilotProjectMap.forEach(
                (key, value) -> {
                    com.bees360.user.User user = userProvider.findUserById(key + "");
                    User pilot = UserAssemble.toWebUser(user);
                    if (Objects.isNull(pilot)) {
                        return;
                    }
                    UserTinyVo infoRecipient = new UserTinyVo(pilot, RoleEnum.PILOT);
                    messageService.sendToPilotDailyNotify(
                            infoRecipient,
                            value.stream()
                                    .map(PilotProjectModel::getProject)
                                    .collect(Collectors.toList()));
                });
    }

    private void doAddMemberAndChangeProjectStatus(
            String originalPilotId,
            long creatorId,
            List<Long> projectIds,
            User pilot,
            boolean pendingAcceptance,
            @Nullable Long version)
            throws ServiceException {
        // 记录相关projects对应的飞手
        for (long projectId : projectIds) {
            MemberSchedule.MemberScheduleItem memberItem = new MemberSchedule.MemberScheduleItem();
            memberItem.setRoleId(RoleEnum.PILOT.getRoleId());
            memberItem.setUserId(pilot.getUserId());
            // TODO 用 VERSION 来进行版本控制
            var result =
                    memberManager.setMember(
                            String.valueOf(projectId),
                            originalPilotId,
                            com.bees360.project.member.RoleEnum.PILOT,
                            String.valueOf(creatorId),
                            version);
            if (!result) {
                log.info("Skipped to assign pilot '{}' to project '{}'", originalPilotId, projectId);
                continue;
            }
            projectService.changeMember(creatorId, projectId, memberItem);
            if (pendingAcceptance) {
                eventHistoryService.insertHistoryToProject(
                        ProjectStatusEnum.WAITING_FOR_ACCEPTANCE, projectId, creatorId);
                projectStatusService.changeOnWaitingForAcceptance(creatorId, projectId);
            } else {
                eventHistoryService.insertHistoryToProject(
                        ProjectStatusEnum.PILOT_ASSIGNED, projectId, creatorId);
                projectStatusService.changeOnAssignedToPilot(
                        creatorId, projectId);
            }
        }

    }

    @Override
    public void deleteByBatchNo(String batchNo) throws ServiceException {
        BeesPilotBatch beesPilotBatch = getByBatchNoWithoutDeletedCheck(batchNo);
        if (Objects.isNull(beesPilotBatch) || beesPilotBatch.isDeleted()) {
            beesPilotBatchMapper.deleteByBatchNo(batchNo);
        }
    }

    @Override
    public BeesPilotBatch getByBatchNoWithoutDeletedCheck(String batchNo) throws ServiceException {
        BeesPilotBatch pilotBatch = beesPilotBatchMapper.getByBatchNoWithoutDeletedCheck(batchNo);
        if (Objects.isNull(pilotBatch)) {
            return null;
        }
        List<BeesPilotBatchItem> itemResult =
                beesPilotBatchItemService.listByBatchNoWithoutDeletedCheck(batchNo);
        Optional.ofNullable(itemResult).ifPresent(o -> pilotBatch.setBatchItems(itemResult));
        return pilotBatch;
    }
}
