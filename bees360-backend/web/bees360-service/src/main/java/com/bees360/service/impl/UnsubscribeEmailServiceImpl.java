package com.bees360.service.impl;

import com.bees360.entity.Project;
import com.bees360.entity.enums.InspectionPurposeTypeEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.service.ProjectService;
import com.bees360.service.UnsubscribeEmailService;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Map;

@Log4j2
public class UnsubscribeEmailServiceImpl implements UnsubscribeEmailService {

    private final Map<String, List<Long>> templateToUnsubscribedCompany;

    private final ProjectService projectService;

    private final InspectionPurposeTypeEnum inspectionPurposeType;

    public UnsubscribeEmailServiceImpl(
        @NonNull Map<String, List<Long>> templateToUnsubscribedCompany,
        @NonNull ProjectService projectService,
        @Nullable InspectionPurposeTypeEnum inspectionPurposeType) {
        this.templateToUnsubscribedCompany = templateToUnsubscribedCompany;
        this.projectService = projectService;
        this.inspectionPurposeType = inspectionPurposeType;
        log.info("Created {}(templateToUnsubscribedCompany={},projectService={},inspectionPurposeType={})",
            this,
            templateToUnsubscribedCompany,
            projectService,
            inspectionPurposeType);
    }

    @Override
    public boolean isUnsubscribed(String templateKey, long company) {
        List<Long> unsubscribedCompanies = templateToUnsubscribedCompany.get(templateKey);
        return unsubscribedCompanies != null && unsubscribedCompanies.contains(company);
    }

    @Override
    public boolean isUnsubscribedByProject(String templateKey, long projectId) {
        Project project = projectService.getById(projectId);
        var serviceType = ProjectServiceTypeEnum.getEnum(project.getServiceType());
        //TODO 后续需要优化
        if(project == null) {
            return false;
        }
        var isTargetPurposeType = (inspectionPurposeType == null || inspectionPurposeType.equals(serviceType.getInspectionPurposeType()));
        if (!isTargetPurposeType) {
            return false;
        }
        boolean isUnsubscribed = false;
        if(project.getInsuranceCompany() != null) {
            isUnsubscribed = isUnsubscribed || isUnsubscribed(templateKey, project.getInsuranceCompany());
        }
        if(project.getRepairCompany() != null) {
            isUnsubscribed = isUnsubscribed || isUnsubscribed(templateKey, project.getRepairCompany());
        }
        return isUnsubscribed;
    }
}
