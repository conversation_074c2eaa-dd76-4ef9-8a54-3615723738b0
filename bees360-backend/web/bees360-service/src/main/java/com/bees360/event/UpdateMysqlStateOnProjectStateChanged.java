package com.bees360.event;

import com.bees360.event.registry.ProjectStateChangedEvent;
import com.bees360.event.util.AbstractNamedEventListener;
import com.bees360.mapper.ProjectStateMapper;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * 监听项目状态变更事件并更新MySQL中的项目状态
 */
@Log4j2
public class UpdateMysqlStateOnProjectStateChanged extends AbstractNamedEventListener<ProjectStateChangedEvent> {
    private final ProjectStateMapper projectStateMapper;

    public UpdateMysqlStateOnProjectStateChanged(ProjectStateMapper projectStateMapper) {
        this.projectStateMapper = projectStateMapper;

        log.info("Created '{}(projectStateMapper={})", this, this.projectStateMapper);
    }

    @Override
    public void handle(ProjectStateChangedEvent event) throws IOException {
        var currentState = event.getCurrentState();
        var state = currentState.getState().name().toLowerCase();
        var changeReason = currentState.getStateChangeReason().getDisplayText();
        var changeReasonId = currentState.getStateChangeReason().getId();
        var projectId = event.getProjectId();
        var updatedAt = currentState.getUpdatedAt();
        projectStateMapper.save(projectId, state, changeReason, changeReasonId, updatedAt.toEpochMilli());
    }
}
