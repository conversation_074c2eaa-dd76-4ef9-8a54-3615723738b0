package com.bees360.service.statistics;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.Instant;
import java.util.List;

public interface ProjectAlertService {

    List<SwyfftUnderwritingAlert> getSwyfftUnderwritingAlert(Instant startTime, Instant endTime);

    @AllArgsConstructor
    @Getter
    class SwyfftUnderwritingAlert {
        @ExcelProperty("Project ID")
        private final String projectId;
        @ColumnWidth(40)
        @ExcelProperty("Policy #")
        private final String policyNumber;
        @ExcelProperty("Inspection #")
        private final String inspectionNumber;
        @ExcelProperty("Insured Name")
        private final String insuredName;
        @ExcelProperty("State")
        private final String state;
        @ExcelProperty("Score")
        private final Integer score;
        @ExcelProperty("Occupancy")
        private final String occupancy;
        @ExcelProperty("Vacant Reason")
        private final String vacantReason;
        @ColumnWidth(80)
        @ExcelProperty("Hazards Noticed")
        private String hazardsNoticed;
    }
}
