package com.bees360.service.stat;

import com.bees360.entity.stat.converter.StatCardConverter;
import com.bees360.entity.stat.converter.StatListConverter;
import com.bees360.entity.stat.dto.ProjectListDataDto;
import com.bees360.entity.stat.dto.ProjectStatChartDto;
import com.bees360.entity.stat.enums.ProjectStatType;
import com.bees360.entity.stat.search.StatFullSearchOption;
import com.bees360.entity.stat.vo.StatComplexVo;
import com.bees360.entity.stat.vo.StatProjectCardVo;
import com.bees360.entity.stat.vo.StatProjectVo;
import com.bees360.entity.stat.vo.card.StatCardVo;
import com.bees360.entity.stat.vo.chart.StatChartVo;
import com.bees360.entity.stat.vo.chart.StatMapVo;
import com.bees360.entity.stat.vo.list.PagedResultVo;
import com.bees360.entity.stat.vo.list.ProjectListVo;
import com.bees360.entity.vo.Pagination;
import com.bees360.mapper.stat.ProjectStatMapper;
import com.bees360.project.Message;
import com.bees360.project.ProjectDaysOldProvider;
import com.bees360.project.state.ProjectStateChangeReason;
import com.bees360.project.state.ProjectStateChangeReasonManager;
import com.bees360.project.state.ProjectStateProvider;
import com.bees360.project.state.ProjectStateQuery;
import com.bees360.util.Iterables;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.bees360.service.stat.StatDaysOldUtil.NEW_DAYS_OLD;
import static com.bees360.service.stat.StatDaysOldUtil.preProcessDaysOldOption;
import static com.bees360.service.stat.StatDaysOldUtil.setProjectNewDaysOld;

@Service
public class StatProjectCloseOutService implements StatComplexService {

    private static final Logger logger = LoggerFactory.getLogger(StatProjectCloseOutService.class);

    @Autowired private ProjectStatMapper statMapper;

    @Autowired private ProjectDaysOldProvider daysOldProvider;

    @Autowired private ProjectStateProvider projectStateProvider;

    @Autowired private ProjectStateChangeReasonManager projectStateChangeReasonManager;

    @Override
    public StatCardVo getCardStat(StatFullSearchOption searchOption) {
        addCloseoutOption(searchOption);
        final StatCardVo cardVo =
                StatCardConverter.toCloseoutCard(statMapper.cardStat(searchOption));

        cardVo.addDefaultServiceType(searchOption.getServiceTypes());

        return cardVo;
    }

    @Override
    public StatChartVo getChartStat(StatFullSearchOption searchOption) {

        addCloseoutOption(searchOption);

        final List<ProjectStatChartDto> projects = statMapper.chartStat(searchOption);

        return StatChartVo.buildVo(projects);
    }

    @Override
    public List<StatMapVo> getMapData(StatFullSearchOption fullSearchOption) {

        addCloseoutOption(fullSearchOption);

        return StatMapVo.buildMapVo(statMapper.chartStat(fullSearchOption));
    }

    @Override
    public PagedResultVo getProjectList(StatFullSearchOption searchOption) {
        addCloseoutOption(searchOption);

        return getPageResult(searchOption);
    }

    @Override
    public StatComplexVo getStatComplexInfo(StatFullSearchOption fullSearchOption) {
        addCloseoutOption(fullSearchOption);
        var complexVo = new StatComplexVo();

        // add stat project list
        var pageResult = getPageResult(fullSearchOption);
        complexVo.setList(pageResult);
        fullSearchOption.setServiceTypes(fullSearchOption.getServiceTypesForChartAndCard());
        // add incomplete stat card vo
        var statProjectCard = new StatProjectCardVo();
        var cardVo = StatCardConverter.toCloseoutCard(statMapper.cardStat(fullSearchOption));
        cardVo.addDefaultServiceType(fullSearchOption.getServiceTypes());
        statProjectCard.setCloseout(cardVo);
        complexVo.setCard(statProjectCard);
        // add stat project chart
        var statProjectChart = new StatProjectVo();
        var statType = ProjectStatType.getInstance(fullSearchOption.getType());
        if (statType == null) {
            complexVo.setChart(statProjectChart);
            return complexVo;
        }
        var chartStat = statMapper.chartStat(fullSearchOption);
        var chartVo = StatChartVo.buildVo(chartStat);
        chartVo.addDefaultIfAbsentState(fullSearchOption.getStates());
        chartVo.addDefaultIfAbsentServiceType(fullSearchOption.getServiceTypes());
        statProjectChart.setChart(chartVo);
        var mapData = StatMapVo.buildMapVo(statMapper.chartStat(fullSearchOption));
        statProjectChart.setMap(mapData);
        complexVo.setChart(statProjectChart);
        return complexVo;
    }

    private PagedResultVo getPageResult(StatFullSearchOption searchOption) {
        var pageIndex = searchOption.getPageIndex();
        var pageSize = searchOption.getPageSize();
        var sortOrder = searchOption.getSortOrder();
        var sortKey = searchOption.getSortKey();

        Map<String, Integer> daysOldMap = null;
        var projectIds =
                statMapper.projectListId(searchOption).stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());
        if (Objects.equals(sortKey, NEW_DAYS_OLD)) {
            daysOldMap = daysOldProvider.findProjectDaysOld(projectIds);
            searchOption = preProcessDaysOldOption(daysOldMap, sortOrder, pageIndex, pageSize);
        }

        List<ProjectListDataDto> projects = statMapper.listCompletedWithSearch(searchOption);

        if (daysOldMap == null) {
            var ids =
                    projects.stream()
                            .map(p -> String.valueOf(p.getProjectId()))
                            .collect(Collectors.toList());
            daysOldMap = daysOldProvider.findProjectDaysOld(ids);
        }

        projects = setProjectNewDaysOld(projects, daysOldMap, sortOrder, sortKey);

        final List<ProjectListVo> completedVos =
                projects.stream()
                        .map(StatListConverter::toCompletedVo)
                        .collect(Collectors.toList());

        final Pagination pagination =
                new Pagination(
                        searchOption.getPageIndex(), searchOption.getPageSize(), projectIds.size());

        PagedResultVo result = new PagedResultVo();
        result.setResult(completedVos);
        result.setPage(pagination);

        return result;
    }

    private void addCloseoutOption(StatFullSearchOption searchOption) {
        var start = searchOption.getStartTime();
        var startTime = Optional.ofNullable(start).map(Instant::ofEpochMilli).orElse(null);
        var end = searchOption.getEndTime();
        var endTime = Optional.ofNullable(end).map(Instant::ofEpochMilli).orElse(null);
        final String PROJECT_COMPLETE = "COMPLETED";
        var serviceType =
                searchOption.getServiceTypes().stream()
                        .map(Message.ServiceType::forNumber)
                        .collect(Collectors.toList());
        var companyIds = searchOption.getCompanyIds();
        if (companyIds == null) {
            companyIds =
                    Optional.ofNullable(searchOption.getCompanyId()).map(List::of).orElse(null);
        }
        var query =
                ProjectStateQuery.ProjectStateQueryBuilder.newBuilder()
                        .setProjectCreatedStart(startTime)
                        .setProjectCreatedEnd(endTime);
        if (CollectionUtils.isNotEmpty(companyIds)) {
            var companies =
                    Iterables.toStream(companyIds).map(Long::intValue).collect(Collectors.toList());
            query.setCompanyId(companies);
        }
        logger.info(
                "created query with start {}, end {}, company id {} and type {}",
                startTime,
                endTime,
                companyIds,
                serviceType);
        var projectClosed =
                Iterables.toSet(
                        projectStateProvider.findProjectByProjectState(
                                List.of(
                                        Message.ProjectMessage.ProjectState.ProjectStateEnum
                                                .PROJECT_CLOSE),
                                query.build()));
        projectClosed.add("-1");
        var projectStateChangeReason = Iterables.toStream(
            projectStateChangeReasonManager
                .findByQuery(List.of(), List.of(PROJECT_COMPLETE), List.of())).findFirst().orElse(null);
        if (Objects.isNull(projectStateChangeReason)) {
            throw new IllegalStateException("No change reason with key %s found".formatted(PROJECT_COMPLETE));
        }
        var projectCompleted =
                Iterables.toSet(
                        projectStateProvider.findProjectByStateChangeReason(
                                List.of(projectStateChangeReason.getDisplayText()), query.build()));
        logger.info(
                "find completed :{} and closed :{}", projectCompleted.size(), projectClosed.size());
        projectClosed.removeAll(projectCompleted);
        searchOption.intersectProjectIds(
                Iterables.toStream(projectClosed).map(Long::parseLong).collect(Collectors.toSet()));
    }

    @Override
    public ProjectStatType getType() {
        return ProjectStatType.PROJECT_CLOSE_OUT;
    }
}
