<?xml version="1.0"?>
<project
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
		xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.bees360.web</groupId>
		<artifactId>bees360</artifactId>
		<version>${revision}${changelist}</version>
	</parent>

	<artifactId>bees360-service</artifactId>
	<name>bees360-service</name>

	<dependencies>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-auth-grpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-job-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-job-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-event-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-event-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-user-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-mail-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-map-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-atomic-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-todo-job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-flyzone-api</artifactId>
        </dependency>
		<dependency>
			<groupId>com.bees360.web</groupId>
			<artifactId>bees360-mapper</artifactId>
		</dependency>
		<dependency>
			<groupId>com.bees360.web</groupId>
			<artifactId>bees360-web-grpc</artifactId>
		</dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-activity-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-grpc-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-image-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360.schedule</groupId>
            <artifactId>bees360-schedule</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-resource-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-resource-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360.web</groupId>
            <artifactId>bees360-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360.commons</groupId>
            <artifactId>invoice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-beespilot-batch-api</artifactId>
            <version>${bees360-solid.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-firebase-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-firebase-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-firebase-job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-activity-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-address-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-pipeline-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>webhook-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-project-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-report-api</artifactId>
        </dependency>
        <dependency>
            <groupId>co.realms9</groupId>
            <artifactId>realms9-bifrost-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-image-job</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.bees360</groupId>
                    <artifactId>bees360-event-autoconfig</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Third-party dependencies -->

        <!--Testing tools-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <!-- thymeleaf.spring5 removes the dependency class, which will cause an exception: java.lang.NoClassDefFoundError: ognl/PropertyAccessor -->
        <!-- @see https://github.com/thymeleaf/thymeleaf-spring/issues/203 -->
        <dependency>
            <groupId>ognl</groupId>
            <artifactId>ognl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-firebase-job</artifactId>
            <version>${bees360-solid.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bees360</groupId>
            <artifactId>bees360-activity-api</artifactId>
            <version>${bees360-solid.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <!-- stripe payment -->
		<dependency>
			<groupId>com.stripe</groupId>
			<artifactId>stripe-java</artifactId>
		</dependency>

		<dependency>
			<groupId>com.googlecode.libphonenumber</groupId>
			<artifactId>libphonenumber</artifactId>
		</dependency>
        <dependency>
            <groupId>com.bees360.commons</groupId>
            <artifactId>commons-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bees360.commons</groupId>
            <artifactId>firebase-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bees360.commons</groupId>
            <artifactId>hover-support</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
        </dependency>
    </dependencies>
</project>
