DROP DATABASE IF EXISTS Bees360;
CREATE DATABASE Bees360;
USE Bees360;
create TABLE Country
(
	id INT NOT NULL AUTO_INCREMENT,
	country_name VARCHAR(100) NOT NULL,
	primary KEY(id)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

create TABLE Region
(
	id BIGINT NOT NULL AUTO_INCREMENT,
	region_name VARCHAR(100) NOT NULL,
	country_id INT NOT NULL,
	PRIMARY KEY(id),
	FOREIGN KEY(country_id) REFERENCES Country(id),
	INDEX idx_country_id(country_id)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE City
(
	id BIGINT NOT NULL AUTO_INCREMENT,
	region_id BIGINT NOT NULL,
	city_name VARCHAR(100) NOT NULL,
	gps_location Point NOT NULL,
	PRIMARY KEY (id),
	FOREIG<PERSON> KEY(region_id) REFERENCES Region(id),
	INDEX idx_region_id(region_id)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE Company
(
	company_id BIGINT NOT NULL AUTO_INCREMENT,
	company_name VARCHAR(200) NOT NULL,
	company_type TINYINT NOT NULL,
	website varchar(100),
	logo varchar(100),
	contact_name varchar(100),
	phone VARCHAR(40),
	email VARCHAR(50),
	PRIMARY key(company_id)
)ENGINE=INNODB DEFAULT CHARSET=utf8 AUTO_INCREMENT=10001;

CREATE TABLE User
(
	user_id BIGINT NOT NULL AUTO_INCREMENT,
	first_name VARCHAR(50) NOT NULL,
	last_name VARCHAR(50) NOT NULL,
	email VARCHAR(50),
	qr_code VARCHAR(100),
	phone VARCHAR(50),
	address VARCHAR(100),
	city VARCHAR(50),
	state VARCHAR(50),
	country VARCHAR(50),
	zip_code VARCHAR(10),
	avatar VARCHAR(256),
	company_id BIGINT,
	employee_id VARCHAR(50),
	password VARCHAR(200) NOT NULL,
	last_login_time BIGINT,
	registration_time BIGINT NOT NULL,
	active_status TINYINT NOT NULL,
	/* The default Point is (0, 0). */
	gps_location POINT NOT NULL,
	most_recent_gps_location POINT NOT NULL,
	roles BIGINT NOT NULL DEFAULT 0,
	role_application_status BIGINT NOT NULL DEFAULT 0,
	certificate_list varchar(5000),
	free_on_trail_project_num int(3) DEFAULT 0,
	discount_percent decimal(5,3) DEFAULT 0,
	wallet_balance decimal(20,3) DEFAULT 0,
	commission_balance decimal(20,3) DEFAULT 0,
	currency varchar(100),

	inspection_service TINYINT(4),
	highfly_service TINYINT(4),
	order_service_time BIGINT DEFAULT 0,
	travel_radius DOUBLE DEFAULT 0,

	/* User does not include the financial information in the first version. */
	PRIMARY KEY(user_id),
	FOREIGN KEY(company_id) REFERENCES Company(company_id),
    INDEX idx_email(email),
    INDEX idx_phone(phone),
    INDEX idx_city(city),
    INDEX idx_state(state),
    INDEX idx_roles(roles),
    SPATIAL INDEX sidx_gps_location(gps_location),
    SPATIAL INDEX sidx_most_recent_gps_location(most_recent_gps_location)
)ENGINE=INNODB DEFAULT CHARSET=utf8 AUTO_INCREMENT=10001;

CREATE TABLE Project
(
	project_id BIGINT NOT NULL AUTO_INCREMENT,
	policy_number VARCHAR(20),
	claim_number VARCHAR(50),
	claim_rcv DECIMAL(20,5),
	claim_acv DECIMAL(20,5),
	claim_type TINYINT DEFAULT NULL,
	created_time BIGINT NOT NULL,
	damage_event_time BIGINT NOT NULL,
	created_by BIGINT NOT NULL,
	project_type TINYINT NOT NULL,
	address VARCHAR(100) NOT NULL,
	city VARCHAR(50) NOT NULL,
	state VARCHAR(50) NOT NULL,
	country VARCHAR(50) NOT NULL,
	zip_code VARCHAR(10) NOT NULL,
	gps_location POINT NOT NULL,
	asset_owner_name VARCHAR(100) NOT NULL,
  	asset_owner_phone VARCHAR(50) NOT NULL,
  	asset_owner_email VARCHAR(50) NOT NULL,
	insurance_company BIGINT,
	repair_company BIGINT,
	material_provider_company BIGINT,
	latest_status INT,
	description VARCHAR(1000) DEFAULT '',
	claim_note VARCHAR(1000) DEFAULT '',
	inspection_time BIGINT,

	is_booking TINYINT(1) NOT NULL,
	contacter_name VARCHAR(50),
	contacter_email VARCHAR(50),
	contacter_phone VARCHAR(50),
	roof_estimated_area_item TINYINT,
	report_service_option TINYINT,
	need_pilot TINYINT(1) NOT NULL,

	PRIMARY KEY(project_id),
	FOREIGN KEY(created_by) REFERENCES User(user_id),
	FOREIGN KEY(insurance_company) REFERENCES Company(company_id),
	FOREIGN KEY(repair_company) REFERENCES Company(company_id),
	FOREIGN KEY(material_provider_company) REFERENCES Company(company_id),
	INDEX idx_zip_code(zip_code),
	FULLTEXT ftidx_location(address, city, state, country, zip_code)
)ENGINE=INNODB DEFAULT CHARSET=utf8 AUTO_INCREMENT=10001;

CREATE TABLE Notification (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  project_id bigint(20) DEFAULT NULL,
  created_time bigint(20) NOT NULL,
  project_status int(11) DEFAULT NULL,
  recipient bigint(20) DEFAULT NULL,
  content varchar(1000) DEFAULT NULL,
  PRIMARY KEY (id),
  KEY project_id (project_id),
  KEY recipient (recipient),
  CONSTRAINT Notification_ibfk_1 FOREIGN KEY (project_id) REFERENCES Project (project_id),
  CONSTRAINT Notification_ibfk_2 FOREIGN KEY (recipient) REFERENCES User (user_id)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8;

CREATE TABLE Comment
(
	chat_id BIGINT NOT NULL AUTO_INCREMENT,
	project_id BIGINT NOT NULL,
	content VARCHAR(1000) NOT NULL,
	user_id BIGINT NOT NULL,
	created_time BIGINT NOT NULL,
	content_type TINYINT NOT NULL,
	PRIMARY KEY(chat_id),
	FOREIGN KEY(project_id) REFERENCES Project(project_id),
	FOREIGN KEY(user_id) REFERENCES User(user_id),
	FULLTEXT ftidx_content(content)
)ENGINE=INNODB DEFAULT CHARSET=utf8 AUTO_INCREMENT=10001;

CREATE TABLE Member
(
	project_id BIGINT NOT NULL,
	user_id BIGINT NOT NULL,
	role TINYINT NOT NULL,
	created_time BIGINT NOT NULL,
	created_by BIGINT NOT NULL,
	description VARCHAR(100) DEFAULT '',
	is_deleted TINYINT(1) NOT NULL DEFAULT 0,
	PRIMARY KEY(project_id, user_id, role),
	FOREIGN KEY(project_id) REFERENCES Project(project_id),
	FOREIGN KEY(user_id) REFERENCES User(user_id),
	FOREIGN KEY(created_by) REFERENCES User(user_id)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE Permission
(
	permission_id INT NOT NULL,
	parent_id int(11),
	operation_id int(11),
	permission_type tinyint not null,
	permission_number varchar(30) not null,
	display varchar(100),
	/* TODO@Yabing: Add content here. */
	PRIMARY KEY(permission_id)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE RolePermission
(
	role_id INT NOT NULL,
	permission_id INT NOT NULL,
	PRIMARY KEY(role_id, permission_id),
	FOREIGN KEY(permission_id) REFERENCES Permission(permission_id)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE ProjectImage
(
	image_id BIGINT NOT NULL AUTO_INCREMENT,
	file_name VARCHAR(256) NOT NULL,
	file_name_lower_resolution VARCHAR(256),
	file_name_middle_resolution VARCHAR(256),
	annotation_image VARCHAR(256),
	file_size BIGINT NOT NULL,
	user_id BIGINT NOT NULL,
	upload_time BIGINT NOT NULL,
	original_file_name VARCHAR(100) NOT NULL,
	file_source_type TINYINT NOT NULL,
	gps_location POINT NOT NULL,
	relative_altitude DOUBLE DEFAULT NULL,
	image_height INT NOT NULL,
	image_width INT NOT NULL,
	project_id BIGINT NOT NULL,
	direction TINYINT,
	image_type TINYINT,
	image_category varchar(50),
  	cam_property_matrix varchar(256),
	is_deleted TINYINT(1) NOT NULL DEFAULT 0,
	manually_annotated TINYINT(1) NOT NULL DEFAULT 0,
	parent_id BIGINT,
	PRIMARY KEY(image_id),
	FOREIGN KEY(project_id) REFERENCES Project(project_id),
	FOREIGN KEY(user_id) REFERENCES User(user_id),
    INDEX idx_upload_time(upload_time),
    INDEX idx_original_file_name(original_file_name),
	SPATIAL INDEX sidx_gps_location(gps_location)
)ENGINE=INNODB DEFAULT CHARSET=utf8 AUTO_INCREMENT=10001;


CREATE TABLE ProjectFacet
(
	/* For each Project, the facet starts with 1. */
	facet_id INT NOT NULL,
	project_id BIGINT NOT NULL,
	component_id INT NOT NULL,
	component_type TINYINT,
	name VARCHAR(10),
	created_by BIGINT NOT NULL,
	created_time BIGINT NOT NULL,
	orientation TINYINT,
	damage_percent TINYINT,
	area FLOAT,
	area_unit VARCHAR(20),
	pitch FLOAT,
	3d_path Text,
	path_unit VARCHAR(20),
  	path_type Text,
  	shared_facet Text,
  	plane_prop Text,
  	plane_coef Text,
  	is_high_roof TINYINT(1) DEFAULT 0,
	/* ShingleCount, PipeJackCount, ExhaustCapCount, FumaceVentCount, TurtleVentCount,
           SkylightCount, SatelliteCount,ChimneyCount will be added later.
	*/
	PRIMARY KEY(project_id, facet_id),
	FOREIGN KEY(created_by) REFERENCES User(user_id),
	FOREIGN KEY(project_id) REFERENCES Project(project_id)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE ImageFacet
(
	facet_id INT NOT NULL,
	image_id BIGINT NOT NULL,
	project_id BIGINT NOT NULL,
	path_2d_full GEOMETRY NOT NULL,
  	path_2d_seen GEOMETRY NOT NULL,
  	path_2d_crsp_overview GEOMETRY NOT NULL,
  	projected_angel FLOAT,
	abs_area_per_pixel FLOAT,
	center_point POINT NOT NULL,
  	relevance_score FLOAT NOT NULL,
	PRIMARY KEY(facet_id, image_id, project_id),
	/* (facet_id, project_id) should also appears in ProjectFacet(facet_id, project_id). */
	FOREIGN KEY(image_id) REFERENCES ProjectImage(image_id),
	FOREIGN KEY(project_id) REFERENCES Project(project_id),
	SPATIAL INDEX sidx_path_2d_full(path_2d_full),
	SPATIAL INDEX sidx_path_2d_seen(path_2d_seen),
	SPATIAL INDEX sidx_path_2d_crsp_overview(path_2d_crsp_overview)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE ImageAnnotation
(
	annotation_id BIGINT NOT NULL,
	image_id BIGINT NOT NULL,
	facet_id INT NOT NULL,
	project_id BIGINT NOT NULL,
	annotation_polygon GEOMETRY NOT NULL,
	created_time BIGINT NOT NULL,
	center_point POINT NOT NULL,
	annotation_type TINYINT NOT NULL,
	usage_type TINYINT NOT NULL,
	generated_by BIGINT NOT NULL,
    PRIMARY KEY(image_id, annotation_id),
	FOREIGN KEY(image_id) REFERENCES ProjectImage(image_id),
	FOREIGN KEY(project_id) REFERENCES Project(project_id),
	FOREIGN KEY(generated_by) REFERENCES User(user_id),
	SPATIAL INDEX sidx_annotation_polygon(annotation_polygon),
	SPATIAL INDEX sidx_center_point(center_point)
)ENGINE=INNODB DEFAULT CHARSET=utf8 AUTO_INCREMENT=10001;

CREATE TABLE ImageAnnotation2D
(
	annotation_id BIGINT AUTO_INCREMENT,
	image_id BIGINT NOT NULL,
	project_id BIGINT NOT NULL,
	annotation_polygon GEOMETRY NOT NULL,
	created_time BIGINT NOT NULL,
	center_point POINT NOT NULL,
	annotation_type TINYINT NOT NULL,
	usage_type TINYINT NOT NULL,
	generated_by bigint(20) NOT NULL,
	is_deleted TINYINT(1) NOT NULL DEFAULT 0,
   	PRIMARY KEY (annotation_id),
	FOREIGN KEY(image_id) REFERENCES ProjectImage(image_id),
	FOREIGN KEY(generated_by) REFERENCES User(user_id),
	SPATIAL INDEX sidx_annotation_polygon(annotation_polygon),
	SPATIAL INDEX sidx_center_point(center_point)
)ENGINE=INNODB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1;

CREATE TABLE EventHistory
(
	event_id BIGINT AUTO_INCREMENT,
	project_id BIGINT NOT NULL,
	user_id BIGINT NOT NULL,
	status INT NOT NULL,
	status_time BIGINT NOT NULL,
	description VARCHAR(100) DEFAULT '',
	created_time BIGINT NOT NULL,
	modified_by BIGINT NOT NULL,
	PRIMARY KEY(event_id),
	FOREIGN KEY(project_id) REFERENCES Project(project_id),
	FOREIGN KEY(user_id) REFERENCES User(user_id),
    FOREIGN KEY(modified_by) REFERENCES User(user_id)
)ENGINE=INNODB DEFAULT CHARSET=utf8 AUTO_INCREMENT=10001;

CREATE TABLE HouseImageSegmentType
(
	id INT NOT NULL,
	name VARCHAR(50) NOT NULL,
	parent_id INT,
	is_leaf TINYINT(1),
	value_type TINYINT(2),
	unit VARCHAR(50),
	code_type TINYINT(2),
	PRIMARY KEY(id),
	FOREIGN KEY(parent_id) REFERENCES HouseImageSegmentType(id)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE ProjectImageReportElement
(
	project_id BIGINT NOT NULL,
	component_id TINYINT NOT NULL,
	component_type TINYINT NOT NULL,
	inspection_category TINYINT NOT NULL,
	relative_position_type TINYINT NOT NULL,
	object_description_json TEXT,
	added_by BIGINT NOT NULL,
	created_time BIGINT NOT NULL,
	PRIMARY KEY(project_id, component_id, inspection_category, relative_position_type),
	FOREIGN KEY(project_id) REFERENCES Project(project_id),
	FOREIGN KEY(added_by) REFERENCES User(user_id)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE CellPhoneReportElement
(
	project_id BIGINT NOT NULL AUTO_INCREMENT,
	component_id TINYINT NOT NULL,
	component_type TINYINT NOT NULL,
	inspection_category TINYINT NOT NULL,
	relative_position_type TINYINT NOT NULL,
	object_description_json TEXT,
	added_by BIGINT NOT NULL,
	created_time BIGINT NOT NULL,
	PRIMARY KEY(project_id, component_id, inspection_category, relative_position_type),
	FOREIGN KEY(project_id) REFERENCES Project(project_id),
	FOREIGN KEY(added_by) REFERENCES User(user_id),
    INDEX idx_component_id(component_id),
    INDEX idx_inspection_category(inspection_category),
    INDEX idx_relative_position_type(relative_position_type)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE ProjectReportFile
(
	report_id BIGINT NOT NULL,
	project_id BIGINT NOT NULL,
	report_type INT NOT NULL,
	report_word_file_name VARCHAR(256) NOT NULL,
	report_pdf_file_name VARCHAR(256) NOT NULL,
	size INT NOT NULL,
	created_by BIGINT NOT NULL,
	created_time BIGINT NOT NULL,
	generation_status TINYINT NOT NULL DEFAULT 1,
	is_read TINYINT(1) NOT NULL DEFAULT 0,
	is_deleted TINYINT(1) NOT NULL DEFAULT 0,
	PRIMARY KEY(report_id),
	FOREIGN KEY(project_id) REFERENCES Project(project_id),
	FOREIGN KEY(created_by) REFERENCES User(user_id)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE `OnSiteReportImageElement`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `component_id` tinyint(4) NULL DEFAULT NULL,
  `element_type` int(11) NOT NULL,
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `image_id` bigint(20) NOT NULL,
  `parent_id` bigint(20) NULL DEFAULT NULL,
  `report_type` tinyint(4) NOT NULL,
  `annotation_id` bigint(20) NULL DEFAULT NULL,
  `annotation3d_id` bigint(20) NULL DEFAULT NULL,
  `source_type` tinyint(4) NULL DEFAULT 0 COMMENT '1 bees360 go app image, 0 drone image',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `image_id`(`image_id`) USING BTREE,
  INDEX `OnSiteReportImageElement_ibfk_3`(`parent_id`) USING BTREE,
  INDEX `OnSiteReportImageElement_ibfk_5`(`annotation_id`) USING BTREE,
  INDEX `OnSiteReportImageElement_ibfk_6`(`annotation3d_id`) USING BTREE,
  CONSTRAINT `OnSiteReportImageElement_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `OnSiteReportImageElement_ibfk_2` FOREIGN KEY (`image_id`) REFERENCES `ProjectImage` (`image_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `OnSiteReportImageElement_ibfk_3` FOREIGN KEY (`parent_id`) REFERENCES `OnSiteReportImageElement` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `OnSiteReportImageElement_ibfk_5` FOREIGN KEY (`annotation_id`) REFERENCES `ImageAnnotation2D` (`annotation_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `OnSiteReportImageElement_ibfk_6` FOREIGN KEY (`annotation3d_id`) REFERENCES `ImageAnnotation` (`annotation_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 11428 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE ProjectEmail
(
	id INT NOT NULL AUTO_INCREMENT,
	email_event_name VARCHAR(100) NOT NULL,
	email_title VARCHAR(200) NOT NULL,
	email_content VARCHAR(2000) NOT  NULL,
	target_role INT NOT NULL,
	PRIMARY KEY(id)
)ENGINE=INNODB DEFAULT CHARSET=utf8 AUTO_INCREMENT=10001;

CREATE TABLE ServicePrice
(
	service_id INT NOT NULL AUTO_INCREMENT,
	service_name VARCHAR(100) ,
	service_price FLOAT,
	currency VARCHAR(100),
	PRIMARY  KEY(service_id)

)ENGINE=INNODB DEFAULT CHARSET=utf8 AUTO_INCREMENT=10001;
/*===========*/


CREATE TABLE UserPayment
(
	payment_id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
	user_id BIGINT(20) NOT NULL,
	project_id BIGINT NOT NULL DEFAULT 0,
	total_fee_amount decimal(20,3) DEFAULT 0,
	tax decimal(20,3) NOT NULL DEFAULT 0,
	paid_service_fee_amount decimal(20,5) NOT NULL DEFAULT 0,
	payment_method varchar(50) NOT NULL,
	card_type varchar(50),
	first_name varchar(50),
	last_name varchar(50),
	card_num varchar(50),
	authorization_code varchar(4),
   	expiration_date int(11),
   	routing_number varchar(50),
   	account_number varchar(50),
   	currency varchar(10),
   	service_fee_type tinyint(3),
	paid_time bigint(20)
)ENGINE=INNODB DEFAULT CHARSET=utf8;


CREATE TABLE SystemValue
(
	service_id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
	country varchar(100),
	service_name varchar(100),
	service_value decimal(20,2),
	label varchar(100)
)ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE ContactUs
(
	id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
	name VARCHAR(50),
	email VARCHAR(50),
	phone VARCHAR(50),
	company VARCHAR(50),
	content VARCHAR(5000)
) ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE PartnerProgram
(
	id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
	inspection_service TINYINT(4),
	highfly_service TINYINT(4),
	name VARCHAR(50) DEFAULT '',
	email VARCHAR(50) DEFAULT '',
	phone VARCHAR(50) DEFAULT '',
	company VARCHAR(50) DEFAULT ''
) ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE `House`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `gps_location` point NOT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `state` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `city` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `address` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `zip_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `overview` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `stories` int(11) NULL DEFAULT NULL,
  `roof_primary_pitch` tinyint(2) NULL DEFAULT NULL COMMENT 'the value should be (0-24)/12',
  `roof_perimeter` float NULL DEFAULT NULL COMMENT 'default unit is ft',
  `longest_length` float NULL DEFAULT NULL COMMENT 'The longest side of the roof.',
  `roof_complexity` tinyint(2) NULL DEFAULT NULL COMMENT 'option from [1:Low, 2:]',
  `roof_area` float NULL DEFAULT NULL COMMENT 'default unit is sqft',
  `roof_boundary` geometry NULL COMMENT 'a polygon representing the edge of the roof',
  `roof_rect` geometry NULL COMMENT 'the minimum enclosing rectangle of the edge of the roof',
  `modified_by` bigint(20) NOT NULL,
  `modified_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `modified_by`(`modified_by`) USING BTREE,
  CONSTRAINT `House_ibfk_1` FOREIGN KEY (`modified_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 352 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'save the data of the house to reduce the calculation' ROW_FORMAT = Dynamic;

CREATE TABLE `Bidding`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `overview` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `stories` int(11) NULL DEFAULT NULL,
  `roof_primary_pitch` tinyint(2) NULL DEFAULT NULL COMMENT 'the value should be (0-24)/12',
  `roof_perimeter` float NULL DEFAULT NULL COMMENT 'default unit is ft',
  `longest_length` float NULL DEFAULT NULL COMMENT 'The longest side of the roof.',
  `roof_complexity` tinyint(2) NULL DEFAULT NULL COMMENT 'default unit is ',
  `roof_area` float NULL DEFAULT NULL COMMENT 'default unit is sqft',
  `roof_boundary` geometry NULL COMMENT 'a polygon representing the edge of the roof',
  `map_rect` geometry NULL,
  `roof_rect` geometry NULL COMMENT 'the minimum enclosing rectangle of the edge of the roof',
  `company_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `company_website` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `industry_experience` int(11) NULL DEFAULT NULL COMMENT '# of years in roofing industry',
  `shingle_grade` tinyint(4) NULL DEFAULT NULL,
  `shingle_price` float NULL DEFAULT NULL COMMENT 'Unit price for single($/SQ)',
  `high_roof` tinyint(1) NULL DEFAULT NULL COMMENT 'the # of stories is more than 2',
  `high_steep_roof_charge` float NULL DEFAULT NULL COMMENT 'Unit of price is $',
  `total_price` float NULL DEFAULT NULL COMMENT 'Unit of price is $',
  `intro` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `modified_by` bigint(20) NOT NULL,
  `modified_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `modified_by`(`modified_by`) USING BTREE,
  CONSTRAINT `Bidding_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Bidding_ibfk_2` FOREIGN KEY (`modified_by`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 562 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'save the data for square report and bidding' ROW_FORMAT = Dynamic;

CREATE TABLE `AppVersion`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `type` tinyint(4) NOT NULL,
  `is_display` tinyint(4) NULL DEFAULT NULL COMMENT 'Whether updates are displayed.',
  `is_compulsory` tinyint(1) NOT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

CREATE TABLE `HouseCategory`  (
  `id` bigint(20) NOT NULL,
  `selector` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_index` int(4) NOT NULL,
  `category_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subcategory_index` int(4) NULL DEFAULT NULL,
  `subcategory` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `additional` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

CREATE TABLE `HouseSegmentValue`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'the segment value id.',
  `project_id` bigint(20) NOT NULL COMMENT 'the project id.',
  `code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'the segment value code.quot HouseImageSegment id.',
  `value` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'the value.',
  `additional` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'additional input',
  `value_type` tinyint(2) NULL DEFAULT NULL COMMENT 'the value type, single election, check etc.',
  `code_type` tinyint(2) NOT NULL COMMENT 'the code type, structures,direction etc.',
  `root_id` bigint(20) NULL DEFAULT NULL COMMENT 'the segment tree root id.',
  `is_damage` tinyint(1) NULL DEFAULT NULL COMMENT '0:no damage 1:is damage.',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `HouseSegmentValue_ibfk_1`(`project_id`) USING BTREE,
  CONSTRAINT `HouseSegmentValue_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 709 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE UserReadReport
(
	id BIGINT NOT NULL AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    project_id BIGINT NOT NULL,
    report_id BIGINT NOT NULL,
    report_type INT NOT NULL,
    is_deleted TINYINT(1) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    PRIMARY KEY(id),
	FOREIGN KEY(user_id) REFERENCES User(user_id),
	FOREIGN KEY(project_id) REFERENCES Project(project_id),
	FOREIGN KEY(report_id) REFERENCES ProjectReportFile(report_id)
) ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE `ProjectCustomizedInfo`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `column_code` int(8) NOT NULL,
  `column_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ProjectCustomizedInfo_ibfk_1`(`project_id`) USING BTREE,
  CONSTRAINT `ProjectCustomizedInfo_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

/** added by xing.wang for join our roster */
CREATE TABLE Roster (
  roster_id bigint(20) NOT NULL AUTO_INCREMENT,
  first_name varchar(50) NOT NULL,
  last_name varchar(50) NOT NULL,
  phone varchar(50) NOT NULL,
  address varchar(100) NOT NULL,
  city varchar(50) NOT NULL,
  state varchar(50) NOT NULL,
  country varchar(50) NOT NULL,
  zip_code varchar(10) NOT NULL,
  gps_location point DEFAULT NULL,
  email varchar(50) NOT NULL,
  additional_operating_territories varchar(200) NOT NULL DEFAULT "",
  operating_city_state varchar(100) NOT NULL DEFAULT "",
  designated_home_state_license varchar(100) NOT NULL DEFAULT "",
  additional_license varchar(100) NOT NULL DEFAULT "",
  years_of_experience tinyint(3) NOT NULL,
  travel_radius int(11) NOT NULL,
  more_than_100miles_traveled tinyint(1) DEFAULT NULL,
  cat_event_deployed tinyint(1) DEFAULT NULL,
  message varchar(1000) NOT NULL DEFAULT "",
  resume_url varchar(256) NOT NULL DEFAULT "",
  created_time bigint(20) DEFAULT NULL,
  updated_time bigint(20) DEFAULT NULL,
  PRIMARY KEY (roster_id),
  KEY IDX_USER_NAME (first_name),
  KEY IDX_PHONE (phone),
  KEY IDX_EMAIL (email),
  KEY IDX_OPERATING_CITY_STATE (operating_city_state)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
