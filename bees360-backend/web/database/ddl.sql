-- [START] @guanrongYang 2019-01-04

-- [END]
-- [START] @shoushan.zhao 2019-01-10   Increase the primary key self-growth.
ALTER TABLE ProjectCustomizedInfo CHANGE id id bigint AUTO_INCREMENT;
-- [END]

-- [START] @shoushan.zhao 2019-01-09   Increase the address length. add table ProjectCustomizedInfo
ALTER TABLE House MODIFY COLUMN address VARCHAR(200);

CREATE TABLE `ProjectCustomizedInfo`  (
  `id` bigint(20) NOT NULL,
  `project_id` bigint(20) NOT NULL,
  `column_code` int(8) NOT NULL,
  `column_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ProjectCustomizedInfo_ibfk_1`(`project_id`) USING BTREE,
  CONSTRAINT `ProjectCustomizedInfo_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
-- [END]

-- [START] @shoushan.zhao 2019-01-02   Increase the Google URL length of the bidding interface.
ALTER TABLE Bidding MODIFY COLUMN overview VARCHAR(5000);

ALTER TABLE House MODIFY COLUMN overview VARCHAR(5000);

ALTER TABLE OnSiteReportImageElement ADD COLUMN source_type TINYINT(4) DEFAULT 0 COMMENT '1 bees360 go app image, 0 drone image';
-- [END]

-- [START] @guanrongYang 2019-01-10 增加用于记录用户阅读数据
CREATE TABLE UserReadReport
(
	id BIGINT NOT NULL AUTO_INCREMENT,,
    user_id BIGINT NOT NULL,
    project_id BIGINT NOT NULL,
    report_id BIGINT NOT NULL,
    report_type INT NOT NULL,
    is_deleted TINYINT(1) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    PRIMARY KEY(id),
	FOREIGN KEY(user_id) REFERENCES User(user_id),
	FOREIGN KEY(project_id) REFERENCES Project(project_id),
	FOREIGN KEY(report_id) REFERENCES ProjectReportFile(report_id)
) ENGINE=INNODB DEFAULT CHARSET=utf8;
-- [END]

-- [START] @guanrongYang 2019-01-12 增加Pilot专用数据表
Create table Pilot (
    id BIGINT NOT NULL AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    travel_radius FLOAT DEFAULT NULL,/* 服务器的半径 */
    PRIMARY KEY(id),
    FOREIGN KEY(user_id) REFERENCES User(user_id)
 ) ENGINE=INNODB DEFAULT CHARSET=utf8;
-- [END]
