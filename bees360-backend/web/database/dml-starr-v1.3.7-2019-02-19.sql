-- [START] @xing.wang 2019-02-19 v1.3.7
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'State Farm', 0, 'https://www.statefarm.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Berkshire Hathaway', 0, 'https://www.bhhc.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Liberty Mutual', 0, 'https://www.libertymutual.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Allstate', 0, 'https://www.allstate.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Progressive', 0, 'https://www.progressive.com/home/<USER>/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Travelers', 0, 'https://www.travelers.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Chubb', 0, 'https://www.chubb.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'USAA', 0, 'https://www.usaa.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Farmers', 0, 'https://www.farmers.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Nationwide', 0, 'https://www.nationwide.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'AIG', 0, 'https://www.aig.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Zurich', 0, 'https://www.zurich.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Hartford', 0, 'https://www.thehartford.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'CNA', 0, 'https://www.cna.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'AmTrust Group', 0, 'https://amtrustfinancial.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'American Family', 0, 'https://www.amfam.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Auto Owners Group', 0, 'https://www.auto-owners.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Erie Insurance', 0, 'https://www.erieinsurance.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Tokio Marine', 0, 'https://tmamerica.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'American Financial', 0, 'http://www.afisnow.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'W.R. Berkley', 0, 'https://www.berkley.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Assurant', 0, 'https://www.assurant.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Fairfax Financial Holdings', 0, 'https://www.fairfax.ca/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Cincinnati Financial', 0, 'https://www.cinfin.com/', '', '', '', '' from Company;
INSERT INTO Company(company_id, company_name, company_type, website, logo, contact_name, phone, email)
select max(company_id) + 1, 'Markel Corporation', 0, 'http://www.markelcorp.com/', '', '', '', '' from Company;
-- [END]
