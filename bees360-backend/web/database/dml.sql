-- [START] @xing.wang 2019-03-31 cancel the free trail project when user register --
update SystemValue set service_value = 0 where service_name = 'FreeOnTrailProjectNum';
-- [END]

-- [START] @shoushan.zhao 2019-03-26 this will finish , see #109
UPDATE Project SET repair_company=1110 WHERE repair_company=1059;
UPDATE Project SET material_provider_company=1110 WHERE material_provider_company=1059;
UPDATE Project SET company_id=1110 WHERE company_id=1059;
UPDATE `User` SET company_id=1110 WHERE company_id=1059;
UPDATE Company SET email='<EMAIL>' WHERE company_id=1110;
DELETE FROM Company WHERE company_id=1059;
-- [END]

-- [START] @shoushan.zhao 2019-03-26 Reports of report_type=3 and report_type=6 have been abolished.
UPDATE `ProjectReportFile` SET report_type=1 WHERE report_type=3;
UPDATE `ProjectReportFile` SET report_type=5 WHERE report_type=6;
-- [END]

-- [START] @xing.wang 2019-03-25 update new price for report. --
INSERT INTO SystemValue (service_id,country,service_name,service_value,label) select max(service_id) + 1, 'US','Report-Price-Real-Time-Damage-Assessment',10.00,'1 report $10.00' from SystemValue;
INSERT INTO SystemValue (service_id,country,service_name,service_value,label) select max(service_id) + 1, 'US','Report-Price-Premium-Measurement',25.00,'1 report $25.00' from SystemValue;
INSERT INTO SystemValue (service_id,country,service_name,service_value,label) select max(service_id) + 1, 'US','Report-Price-Premium-Damage-Assessment',40.00,'1 report $40.00' from SystemValue;
INSERT INTO SystemValue (service_id,country,service_name,service_value,label) select max(service_id) + 1, 'US','Report-Price-Property-Image',40.00,'1 report $40.00' from SystemValue;
INSERT INTO SystemValue (service_id,country,service_name,service_value,label) select max(service_id) + 1, 'US','Report-Price-Infrared-Damage-Assessment',2.5,'1 report $2.5/SQ' from SystemValue;
INSERT INTO SystemValue (service_id,country,service_name,service_value,label) select max(service_id) + 1, 'US','Report-Price-Preliminary-Damage-Assessment',30.00,'1 report $30.00' from SystemValue;
INSERT INTO SystemValue (service_id,country,service_name,service_value,label) select max(service_id) + 1, 'US','Report-Price-Highfly-Evaluation',10.00,'1 report $10.00' from SystemValue;
INSERT INTO SystemValue (service_id,country,service_name,service_value,label) select max(service_id) + 1, 'US','Report-Price-Real-Time-Quick-Square',0,'1 report $0' from SystemValue;
INSERT INTO SystemValue (service_id,country,service_name,service_value,label) select max(service_id) + 1, 'US','Report-Price-On-Site-Bidding',5.0,'1 report $5.0' from SystemValue;
INSERT INTO SystemValue (service_id,country,service_name,service_value,label) select max(service_id) + 1, 'US','Report-Price-Full-Scope-Underwriting-Report',50.00,'1 report $50.00' from SystemValue;
-- [END]

-- [START] @shoushan.zhao 2019-03-23 update the company logo of ridge cap.
UPDATE Company SET logo='https://bees360.s3.amazonaws.com/company/1059/logo/newlogo.png' WHERE company_id=1059;
UPDATE Company SET logo='https://bees360.s3.amazonaws.com/company/1110/logo/newlogo.png' WHERE company_id=1110;
-- [END]
-- [START] @shoushan.zhao 2019-03-22 update bees go app template.
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory_index`, `subcategory`, `description`, `additional`) VALUES (5050, '374', 'RFG', 30, 'Roof', 175, 'Shingle Size', 'Shingle size', '');
-- [END]
-- [START] @shoushan.zhao 2019-03-18 The problem of updating bees go app self-incrementing element type errors.
UPDATE OnSiteReportImageElement SET element_type=150 WHERE title='Risk overview' AND element_type=159 AND source_type=1 AND report_type=15 AND component_id=1;
-- [END]
-- [START] @xing.wang 2019-03-15 update company_id of project to user's company_id
update Project P inner join User U on P.created_by = U.user_id set P.company_id = U.company_id where U.company_id is not null;

-- [END]

-- [START] @shoushan.zhao 2018-02-27   Modify chimney storage location. Beesgo App Adds photo shooting verification. Increase the data needed for SIB's custom report.
ALTER TABLE Project ADD COLUMN chimney INT(5) NULL DEFAULT 0;

ALTER TABLE HouseSegmentValue ADD COLUMN is_required TINYINT(1) NULL DEFAULT 1;

CREATE TABLE `CustomizedReportElement`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `company_id` bigint(20) NOT NULL,
  `element_id` bigint(20) NOT NULL,
  `report_type` tinyint(4) NOT NULL,
  `type` tinyint(4) NOT NULL,
  `element_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'element_name',
  `element_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'element value',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `CustomizedReportElement_ibfk_1`(`element_id`) USING BTREE,
  INDEX `CustomizedReportElement_ibfk_2`(`project_id`) USING BTREE,
  CONSTRAINT `CustomizedReportElement_ibfk_1` FOREIGN KEY (`element_id`) REFERENCES `OnSiteReportImageElement` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `CustomizedReportElement_ibfk_2` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

CREATE TABLE `CustomizedSIBCategory` (
  `id` bigint(20) NOT NULL,
  `category_type` tinyint(2) NOT NULL COMMENT '0:root,1:subcategory,2:description',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `parent_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (10, 0, 'Roof', 0);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (20, 0, 'Exterior', 0);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (30, 0, 'Interior', 0);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (40, 1, 'Front Slope', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (50, 1, 'Right Slope', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (60, 1, 'Back Slope', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (70, 1, 'Left Slope', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (80, 1, 'Front Section - Flat Roof', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (90, 1, 'Right Section - Flat Roof', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (100, 1, 'Back Section - Flat Roof', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (110, 1, 'Left Section - Flat Roof', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (120, 2, 'Overhanging Trees', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (130, 2, 'Overhanging Vegetation', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (140, 2, 'Moss', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (150, 2, 'Algae', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (160, 2, 'Stain', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (170, 2, 'Discoloration', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (180, 2, 'Previous Repair', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (190, 2, 'Tiles Missing', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (200, 2, 'Tiles Broken', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (210, 2, 'Tiles Cracked', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (220, 2, 'Shingles Missing', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (230, 2, 'Shingles Broken', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (240, 2, 'Shingles Cracked', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (250, 2, 'No Damages', 10);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (260, 1, 'Front Elevation', 20);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (270, 1, 'Right Elevation', 20);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (280, 1, 'Back Elevation', 20);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (290, 1, 'Left Elevation', 20);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (300, 2, 'Water Leaking', 20);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (310, 2, 'Moss', 20);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (320, 2, 'Algae', 20);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (330, 2, 'Stain', 20);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (340, 2, 'Discoloration', 20);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (350, 2, 'Previous Repair', 20);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (360, 1, 'Living Room', 30);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (370, 1, 'Living Kitchen', 30);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (380, 1, 'Living Bedrooms', 30);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (395, 1, 'Living Bathrooms', 30);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (400, 1, 'Living Family', 30);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (410, 1, 'Room', 30);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (420, 1, 'Hallway', 30);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (430, 2, 'Water Leaking', 30);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (440, 2, 'Moss', 30);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (450, 2, 'Algae', 30);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (460, 2, 'Stain', 30);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (470, 2, 'Discoloration', 30);
INSERT INTO `Bees360`.`CustomizedSIBCategory`(`id`, `category_type`, `category`, `parent_id`) VALUES (480, 2, 'Previous Repair', 30);
-- [END]

-- [START] @shoushan.zhao 2018-01-22   update image segment item.
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`) VALUES (125, 'Shingle Length', 0, 1, 8, 'ft', 60);
-- [END]

-- [START] @shoushan.zhao 2018-01-22   update direction item.
INSERT INTO `Company`(`company_name`, `company_type`, `website`, `logo`, `contact_name`, `phone`, `email`) VALUES ('Associated Services Inspections', 1, 'http://associatedservicesinspections.net/index.html', 'https://bees360.s3.amazonaws.com/company/1313/asilogo.png', 'Associated Services Inspections', '************', '<EMAIL>');
-- [END]

-- [START] @shoushan.zhao 2018-01-14   update direction item.
UPDATE HouseCategory SET selector=223, subcategory='Right', description='Right overview.' WHERE id=400;
UPDATE HouseCategory SET selector=224, subcategory='Back', description='Back overview.' WHERE id=500;
UPDATE HouseCategory SET selector=225, subcategory='Left', description='Left overview.' WHERE id=600;
-- [END]

-- [START] @shoushan.zhao 2018-01-11   add Shingle item.
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory_index`, `subcategory`, `description`, `additional`) VALUES (3450, '254', 'RFG', 30, 'Roof', 135, 'Shingle', 'Shingle.', '');

UPDATE HouseImageSegmentType SET `name`='Roof' WHERE id=50;
UPDATE HouseImageSegmentType SET code_type=7 WHERE code_type=5;
UPDATE HouseImageSegmentType SET code_type=6 WHERE code_type=4;
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`) VALUES (75, 'Elevation', 0, 1, 3, NULL, 4);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`) VALUES (80, 'Front', 0, 1, 3, NULL, 5);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`) VALUES (85, 'Right', 0, 1, 3, NULL, 5);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`) VALUES (90, 'Back', 0, 1, 3, NULL, 5);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`) VALUES (95, 'Left', 0, 1, 3, NULL, 5);
UPDATE HouseImageSegmentType SET code_type=code_type*10;

UPDATE HouseImageSegmentType SET id=71 WHERE id=70;
UPDATE HouseImageSegmentType SET id=70 WHERE id=65;
UPDATE HouseImageSegmentType SET id=65 WHERE id=60;
UPDATE HouseImageSegmentType SET id=60 WHERE id=71;

UPDATE HouseCategory SET additional='choose material (composite shingle,asphalt)' WHERE id=15300;
-- [END]

-- [START] @shoushan.zhao 2018-01-09   Clean up the garbage data generated before bees go app.
--DELETE FROM ProjectImage WHERE image_id IN (SELECT t.image_id FROM OnSiteReportImageElement t WHERE t.report_type=15);
DELETE FROM OnSiteReportImageElement WHERE report_type=15;
DELETE FROM HouseSegmentValue;
-- [END]

-- [START] @shoushan.zhao 2018-01-08   update segment item description.
UPDATE HouseCategory SET description=CONCAT(UPPER(LEFT(description,1)),SUBSTRING(description,2,(LENGTH(description)-1)));
UPDATE HouseCategory SET description=CONCAT(description,'.');
-- [END]

-- [START] @shoushan.zhao 2018-01-02   update segment item name.
UPDATE HouseImageSegmentType SET name='HVAC Units' WHERE id=15;
UPDATE HouseImageSegmentType SET name='Other Structures' WHERE id=30;
UPDATE HouseImageSegmentType SET name='Shingle Layer' WHERE id=120;
UPDATE HouseImageSegmentType SET name='Single Layer' WHERE id=12000;
UPDATE HouseImageSegmentType SET name='Double Layer' WHERE id=12010;
UPDATE HouseImageSegmentType SET name='Multi Layer' WHERE id=12020;
UPDATE HouseImageSegmentType SET name='Severe Damage' WHERE id=1500000;
UPDATE HouseImageSegmentType SET name='Moderate Damage' WHERE id=1500010;
UPDATE HouseImageSegmentType SET name='Mild Damage' WHERE id=1500020;
UPDATE HouseImageSegmentType SET name='No Damage' WHERE id=1500030;

UPDATE HouseImageSegmentType SET value_type=8 WHERE id IN (SELECT a.tId FROM(SELECT t.id tId FROM HouseImageSegmentType t WHERE t.value_type=6) a);
UPDATE HouseImageSegmentType SET value_type=7 WHERE id IN (SELECT a.tId FROM(SELECT t.id tId FROM HouseImageSegmentType t WHERE t.value_type=5) a);
UPDATE HouseImageSegmentType SET value_type=6 WHERE id IN (SELECT a.tId FROM(SELECT t.id tId FROM HouseImageSegmentType t WHERE t.value_type=4) a);
UPDATE HouseImageSegmentType SET value_type=5 WHERE id IN (SELECT a.tId FROM(SELECT t.id tId FROM HouseImageSegmentType t WHERE t.value_type=3) a);
UPDATE HouseImageSegmentType SET value_type=3 WHERE id IN (SELECT a.tId FROM(SELECT t.id tId FROM HouseImageSegmentType t WHERE t.value_type=2) a);
-- [END]

--- [START] @guanrongYang 2019-01-11 利用现有数据，初始化 UserReadReport 的数据
insert into UserReadReport(user_id, project_id, report_id, report_type, is_deleted, created_time, updated_time)
select M.user_id, M.project_id, R.report_id, R.report_type, 0 as is_deleted, unix_timestamp(now()) * 1000 as created_time, unix_timestamp(now()) * 1000 as updated_time
from ProjectReportFile R, (select project_id, user_id from Member group by project_id, user_id) as M
where M.project_id = R.project_id and R.is_deleted = 0 and R.is_read = 1;
--- [END]

-- [START] @zijianLi 2019-01-28
UPDATE HouseCategory SET subcategory_index=90 WHERE id=1600;
UPDATE HouseCategory SET subcategory='Test Square', description='10x10 test square for hail damage' WHERE id=3450;
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory_index`, `subcategory`, `description`, `additional`) VALUES (3475, '254', 'SFG', 30, 'Roof', 137, 'Close-ups', 'Close-up image for roof damage', NULL);
UPDATE HouseCategory SET description = left(description,CHAR_LENGTH(description) - 1);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory_index`, `subcategory`, `description`, `additional`) VALUES (3480, '254', 'SFG', 30, 'Roof', 137, 'Layers', 'Roofing layers', NULL);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory_index`, `subcategory`, `description`, `additional`) VALUES (3485, '254', 'SFG', 30, 'Roof', 138, 'Drip Edge', 'Roofing drip edge', NULL);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory_index`, `subcategory`, `description`, `additional`) VALUES (3490, '254', 'SFG', 30, 'Roof', 139, 'Ridge Cap', 'Roofing ridge cap', NULL);
UPDATE HouseCategory SET subcategory_index=135 WHERE id=3450;
UPDATE HouseCategory SET subcategory_index=136 WHERE id=3475;
UPDATE HouseCategory SET subcategory='Accessories' WHERE id = 4000;
UPDATE HouseCategory SET subcategory='Accessories' WHERE id = 4100;
UPDATE HouseCategory SET subcategory='Accessories' WHERE id = 4200;
UPDATE HouseCategory SET subcategory='Accessories' WHERE id = 4300;
UPDATE HouseCategory SET subcategory='Accessories' WHERE id = 4400;
UPDATE HouseCategory SET subcategory='Accessories' WHERE id = 4500;
UPDATE HouseCategory SET subcategory='Accessories' WHERE id = 4600;
UPDATE HouseCategory SET subcategory='Accessories' WHERE id = 4700;
UPDATE HouseCategory SET subcategory='Accessories' WHERE id = 4800;
UPDATE HouseCategory SET subcategory='Accessories' WHERE id = 4900;
UPDATE HouseCategory SET subcategory='Accessories' WHERE id = 5000;
-- [END]

--[START] @zijianLi 2019-01-29
UPDATE HouseCategory SET description='Close-up image for roof damage' WHERE id = 3475;

UPDATE HouseCategory SET description='10x10 test square for hail damage' WHERE id = 3450;

UPDATE HouseImageSegmentType SET name='Gazebo' WHERE id = 15;
UPDATE HouseImageSegmentType SET name='Detached Garage' WHERE id = 20;
UPDATE HouseImageSegmentType SET name='Barn' WHERE id = 30;
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`) VALUES (35, 'Fence', '0', '1', '1', NULL, '10');
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`) VALUES (40, 'Other Structures', '0', '1', '1', NULL, '10');
-- [END]
