alter table User add column source TINYINT default 0 after travel_radius;


CREATE TABLE Adjuster (
    id BIGINT NOT NULL AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    operating_city_state VARCHAR(100) DEFAULT '',
    additional_operating_territories VARCHAR(200) DEFAULT '',
    designated_home_state_license VARCHAR(100) NOT NULL,
    additional_license VARCHAR(100) DEFAULT '',
    years_of_experience TINYINT(4) NOT NULL,
    more_than_100miles_traveled TINYINT(1) NOT NULL,
    cat_event_deployed TINYINT(1) NOT NULL,
    ai_roster_id BIGINT NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (user_id) REFERENCES User(user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
