/** added by xing.wang for join our roster */
CREATE TABLE Roster (
  roster_id bigint(20) NOT NULL AUTO_INCREMENT,
  first_name varchar(50) NOT NULL,
  last_name varchar(50) NOT NULL,
  phone varchar(50) NOT NULL,
  address varchar(100) NOT NULL,
  city varchar(50) NOT NULL,
  state varchar(50) NOT NULL,
  country varchar(50) NOT NULL,
  zip_code varchar(10) NOT NULL,
  gps_location point DEFAULT NULL,
  email varchar(50) NOT NULL,
  additional_operating_territories varchar(200) NOT NULL DEFAULT "",
  operating_city_state varchar(100) NOT NULL DEFAULT "",
  designated_home_state_license varchar(100) NOT NULL DEFAULT "",
  additional_license varchar(100) NOT NULL DEFAULT "",
  years_of_experience tinyint(3) NOT NULL,
  travel_radius int(11) NOT NULL,
  more_than_100miles_traveled tinyint(1) DEFAULT NULL,
  cat_event_deployed tinyint(1) DEFAULT NULL,
  message varchar(1000) NOT NULL DEFAULT "",
  resume_url varchar(256) NOT NULL DEFAULT "",
  created_time bigint(20) DEFAULT NULL,
  updated_time bigint(20) DEFAULT NULL,
  PRIMARY KEY (roster_id),
  KEY IDX_USER_NAME (first_name),
  KEY IDX_PHONE (phone),
  KEY IDX_EMAIL (email),
  KEY IDX_OPERATING_CITY_STATE (operating_city_state)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
