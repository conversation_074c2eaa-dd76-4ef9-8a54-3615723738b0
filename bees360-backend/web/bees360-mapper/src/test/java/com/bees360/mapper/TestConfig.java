package com.bees360.mapper;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.TestPropertySource;

@Configuration
@EnableAutoConfiguration(
    exclude = {
        RedisAutoConfiguration.class,
    })
@MapperScan(basePackageClasses = {com.bees360.mapper.PackageMarker.class})
@TestPropertySource(properties = "spring.config.location = classpath:application.yml")
public class TestConfig {

}
