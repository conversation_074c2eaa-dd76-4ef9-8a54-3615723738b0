package com.bees360.mapper;

import com.bees360.entity.ProjectAirspace;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Log4j2
@SpringBootTest(
    classes = {
        TestConfig.class,
    })
@Transactional
public class ProjectAirspaceMapperTest {
    private List<String> airspaceStatuses =
            List.of(
                    "clear",
                    "advisory_required",
                    "laanc_auto_approval",
                    "laanc_not_auto_approval",
                    "restricted",
                    "controlled_airspace",
                    "caution",
                    "unsupported_location");

    @Autowired
    ProjectAirspaceMapper projectAirspaceMapper;

    @Test
    public void testUpsertByProjectIdAndGetByProjectIds() {
        String projectId = String.valueOf(RandomUtils.nextLong(10000, 1000000));
        ProjectAirspace expectedAirspace = randomProjectAirspace(projectId);
        ProjectAirspace expectedAirspaceSecond = randomProjectAirspace(projectId);

        projectAirspaceMapper.upsertByProjectId(expectedAirspace);
        ProjectAirspace actualAirspace =
                projectAirspaceMapper.getByProjectIds(List.of(projectId)).get(0);
        assertProjectAirspace(expectedAirspace, actualAirspace);

        projectAirspaceMapper.upsertByProjectId(expectedAirspaceSecond);
        actualAirspace = projectAirspaceMapper.getByProjectIds(List.of(projectId)).get(0);
        assertProjectAirspace(expectedAirspaceSecond, actualAirspace);
    }

    private ProjectAirspace randomProjectAirspace(String projectId) {
        ProjectAirspace projectAirspace = new ProjectAirspace();
        projectAirspace.setProjectId(projectId);
        projectAirspace.setStatus(airspaceStatuses.get(RandomUtils.nextInt(0, airspaceStatuses.size())));
        projectAirspace.setHeightCeiling(RandomUtils.nextInt(10, 1000));
        return projectAirspace;
    }

    private void assertProjectAirspace(ProjectAirspace expected, ProjectAirspace actual) {
        Assertions.assertNotNull(expected, "expected project airspace cannot be null.");
        Assertions.assertNotNull(actual, "actual project airspace cannot be null.");
        Assertions.assertEquals(expected.getProjectId(), actual.getProjectId());
        Assertions.assertEquals(expected.getStatus(), actual.getStatus());
        Assertions.assertEquals(expected.getHeightCeiling(), actual.getHeightCeiling());
        Assertions.assertNotNull(actual.getCreatedAt());
        Assertions.assertNotNull(actual.getUpdatedAt());
    }
}
