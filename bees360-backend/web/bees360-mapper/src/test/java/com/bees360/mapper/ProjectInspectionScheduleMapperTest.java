package com.bees360.mapper;

import com.bees360.entity.ProjectInspectionSchedule;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertEquals;

@SpringBootTest(
    classes = {
        TestConfig.class,
    })
@Transactional
public class ProjectInspectionScheduleMapperTest {

    @Autowired
    private ProjectInspectionScheduleMapper projectInspectionScheduleMapper;

    @Test
    void testSaveThenGet() {
        var entity =
            new ProjectInspectionSchedule()
                .setProjectId(RandomUtils.nextLong(10000000L, 100000000L))
                .setDueDate(System.currentTimeMillis())
                .setScheduledTime(System.currentTimeMillis());
        projectInspectionScheduleMapper.save(entity);
        var result = projectInspectionScheduleMapper.getByProjectId(entity.getProjectId());
        assertEquals(entity, result);

        entity.setScheduledTime(System.currentTimeMillis());
        projectInspectionScheduleMapper.save(entity);
        result = projectInspectionScheduleMapper.getByProjectId(entity.getProjectId());
        assertEquals(entity, result);

        var newScheduledTime = System.currentTimeMillis() - Duration.ofDays(1).toMillis();
        projectInspectionScheduleMapper.updateScheduledTime(entity.getProjectId(), newScheduledTime);
        result = projectInspectionScheduleMapper.getByProjectId(entity.getProjectId());
        assertEquals(newScheduledTime, result.getScheduledTime());

        var newDueDate = System.currentTimeMillis() + Duration.ofDays(5).toMillis();
        projectInspectionScheduleMapper.updateDueDate(entity.getProjectId(), newDueDate);
        result = projectInspectionScheduleMapper.getByProjectId(entity.getProjectId());
        assertEquals(result.getDueDate(), newDueDate);
    }
}
