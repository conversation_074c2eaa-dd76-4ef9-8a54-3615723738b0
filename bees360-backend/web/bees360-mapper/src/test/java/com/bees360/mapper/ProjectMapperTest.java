package com.bees360.mapper;

import com.bees360.entity.Project;
import com.bees360.entity.ProjectInspectionSchedule;
import com.bees360.entity.User;
import com.bees360.entity.dto.ProjectSearchOption;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.ProjectServiceTypeEnum;
import com.bees360.entity.enums.ProjectStatusEnum;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.List;

@Log4j2
@SpringBootTest(
    classes = {
        TestConfig.class,
    })
@Transactional
public class ProjectMapperTest {

    private final ProjectMapper projectMapper;
    private final ProjectInspectionScheduleMapper projectInspectionScheduleMapper;
    private final List<Long> projectIds;

    public ProjectMapperTest(
            @Autowired ProjectMapper projectMapper,
            @Autowired ProjectInspectionScheduleMapper projectInspectionScheduleMapper) {
        this.projectMapper = projectMapper;
        this.projectInspectionScheduleMapper = projectInspectionScheduleMapper;
        projectIds = initData();
    }

    @Test
    public void testListTinyProjectWithSearchWithScheduledTime() {
        var searchOption = new ProjectSearchOption();
        searchOption.setScheduledTimeStart(System.currentTimeMillis() - Duration.ofDays(7).toMillis());
        var searchMap = searchOption.toMap();
        searchMap.put("pageSize", 10);
        searchMap.put("startIndex", 0);
        var count = projectMapper.countTinyProjectWithSearch(searchMap);
        var projects = projectMapper.listTinyProjectWithSearch(searchMap);
        Assertions.assertTrue(count > 0);
        Assertions.assertEquals(count, projects.size());

        searchOption.setScheduledTimeEnd(System.currentTimeMillis() - Duration.ofDays(5).toMillis());
        searchMap = searchOption.toMap();
        searchMap.put("pageSize", 10);
        searchMap.put("startIndex", 0);
        count = projectMapper.countTinyProjectWithSearch(searchMap);
        projects = projectMapper.listTinyProjectWithSearch(searchMap);
        Assertions.assertEquals(count, 0);
        Assertions.assertEquals(count, projects.size());
    }

    @Test
    public void updateInspectionTime() {
        var projectId = projectIds.get(0);
        var project = projectMapper.getById(projectId);
        var inspectionTime = System.currentTimeMillis() + Duration.ofDays(RandomUtils.nextInt(0, 7)).toMillis();
        projectMapper.updateInspectionTime(projectId, inspectionTime);
        project = projectMapper.getById(projectId);
        Assertions.assertEquals(inspectionTime, project.getInspectionTime());
    }

    @SneakyThrows
    List<Long> initData() {
        var project = new Project();
        project.setAddress("2825 Wilcrest Drive, Suite 270");
        project.setCity("Houston");
        project.setState("TX");
        project.setCountry("US");
        project.setZipCode("77042");
        project.setAssetOwnerName("");
        project.setAssetOwnerEmail("");
        project.setAssetOwnerPhone("");
        project.setServiceType(ProjectServiceTypeEnum.FULL_ADJUSTMENT.getCode());
        project.setClaimType(ClaimTypeEnum.HAIL.getCode());
        project.setCatNumber("2022");
        project.setLatestStatus(ProjectStatusEnum.NEW_PROJECT.getCode());
        project.setCreatedBy(User.AI_ID);
        project.setCreatedTime(System.currentTimeMillis());
        projectMapper.insertBaseInfo(project);

        var inspection = new ProjectInspectionSchedule();
        inspection.setProjectId(project.getProjectId());
        inspection.setScheduledTime(System.currentTimeMillis() - Duration.ofDays(2).toMillis());
        inspection.setDueDate(System.currentTimeMillis() + Duration.ofDays(7).toMillis());
        projectInspectionScheduleMapper.save(inspection);

        return List.of(project.getProjectId());
    }
}
