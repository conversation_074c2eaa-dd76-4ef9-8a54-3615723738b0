package com.bees360.mapper;

import com.bees360.entity.ProjectQuiz;
import com.bees360.entity.Quiz;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/4/10 12:21 PM
 **/
public interface ProjectQuizMapper {
    /**
     * 批量插入问卷回答
     *
     * @param quizList 回答列表
     * @return 插入成功数
     */
    int batchInsertAnswer(List<ProjectQuiz> quizList);


    /**
     * 查询项目问卷最新的回答列表
     * @param projectId 项目ID
     * @return 项目问卷回答列表
     */
    List<ProjectQuiz> listLatestAnswers(@Param("projectId") long projectId, @Param("claimType") int claimType);

    /**
     * 查询项目问卷
     * @param quizCode 问卷调查套题ID
     * @return 项目问卷列表
     */
    List<Quiz> listCompanyQuiz(long quizCode);

    /**
     * 更新答案
     * @param project_quiz_id
     * @param answer
     */
    void updateAnswers(@Param("projectQuizId") long project_quiz_id,@Param("answer") String answer);

    List<ProjectQuiz> listLatestAnswersOfQuizType(@Param("projectIds") List<Long> projectIds, @Param("quizId") long quizId);
}
