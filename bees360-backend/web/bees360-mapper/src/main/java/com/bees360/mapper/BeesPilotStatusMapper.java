package com.bees360.mapper;


import com.bees360.entity.BeesPilotStatus;
import com.bees360.entity.query.BeesPilotStatusQuery;

import java.util.List;

public interface BeesPilotStatusMapper {
    int deleteByPrimaryKey(Long taskId);

    boolean insert(BeesPilotStatus record);

    BeesPilotStatus getByProjectId(long projectId);

    List<BeesPilotStatus> listBeesPilotStatus(BeesPilotStatusQuery query);

    boolean updateByProjectId(BeesPilotStatus record);
}
