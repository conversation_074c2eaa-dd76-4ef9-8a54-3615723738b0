package com.bees360.mapper.pilot;

import com.bees360.entity.pilot.Pilot;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * 深圳聚蜂智能科技有限公司 版权所有 © Copyright 2019 Bees360
 *
 * @Description:
 * @Project: com.bees360.mapper.pilot
 * @CreateDate: 2019/11/8
 * @Author: <a href="<EMAIL>">jiankang.xia</a>
 */
@Repository
public interface PilotMapper {
    /**
     * 新增飞手
     * @param pilot
     * @return
     */
    Long insert(Pilot pilot);

    /**
     * 修改
     * @param pilot
     * @return
     */
    Integer update(Pilot pilot);

    /**
     * 根据用户id查询
     * @param userId
     * @return
     */
    Pilot queryByUserId(Long userId);

    /**
     * 根据条件模型查询
     * @param pilot
     * @return
     */
    Pilot queryByPilot(Pilot pilot);

    /**
     * 根据pilot ID 查询
     * @param userId
     * @return
     */
    Pilot queryById(Long userId);

    /**
     * 修改
     * @param map
     * @return
     */
    void updateByMap(Map<String, Object> map);
}
