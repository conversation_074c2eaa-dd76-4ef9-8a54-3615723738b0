package com.bees360.mapper;

import java.util.List;

import com.bees360.entity.query.EventHistoryQuery;
import org.apache.ibatis.annotations.Param;

import com.bees360.entity.EventHistory;
import com.bees360.entity.vo.HistoryLogVo;

public interface EventHistoryMapper {
	void insert(EventHistory history);

	boolean existStatus(@Param("projectId")long projectId, @Param("status")int status);

	List<EventHistory> listHistoryIn(@Param("projectId")long projectId, @Param("statuses")List<Integer> statuses);

	EventHistory getByStatus(@Param("projectId")long projectId, @Param("status")int status);

	/**
	 * select all event histories in specified project, and return the result order by createdTime asc
	 * @param projectId specify the project
	 * @return a eventHistory list ordered by createdTime asc
	 * @throws Exception something wrong with database or system
	 */
	List<EventHistory> listAll(long projectId);

	/**
	 * select all event histories in specified project, and return the result order by createdTime desc
	 * @param projectId specify the project
	 * @return a event history list ordered by createdTime desc
	 * @throws Exception something wrong with database or system
	 */
	List<HistoryLogVo> listHistoryLog(long projectId);

	List<EventHistory> getFanilyStatusIsNewProject();


    /**
     * 根据条件查询event history
     * 结果根据时间倒序
     * @param query 查询条件
     * @return event history列表
     */
    List<EventHistory> listEventHistory(EventHistoryQuery query);

    EventHistory getLastEventHistory(long projectId);
}
