package com.bees360.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.bees360.entity.RolePermission;

public interface RolePermissionMapper {

	/**
	 * get permission by roleId
	 * @param roleId
	 * @return
	 * @throws Exception
	 */
	List<RolePermission> getByRoleId(@Param("roleId") int roleId)throws Exception;


	/**
	 * get all permissions of roles which a user have
	 * @param roleIds
	 * @return
	 * @throws Exception
	 */
	List<RolePermission> getByRoleIds(@Param("roleIds") List<Integer> roleIds)throws Exception;

}
