package com.bees360.mapper;

import com.bees360.entity.MissionAttachFile;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问卷附件
 */
public interface MissionAttachFileMapper {

    /**
     * 批量插入
     * @param list
     */
    void batchInsert(List<MissionAttachFile> list);

    /**
     * 更新url和上传时间
     * @param id
     * @param uploadTime
     * @param url
     */
    void updateUrlAndUploadTime(@Param("id") long id, @Param("uploadTime") long uploadTime, @Param("url") String url);

    List<MissionAttachFile> listByProjectId(@Param("projectId") long projectId);
}
