package com.bees360.mapper;

import java.util.Collection;
import com.bees360.entity.vo.ProjectImageAnnotationVo;
import java.util.List;

import com.bees360.entity.dto.*;
import org.apache.ibatis.annotations.Param;

import com.bees360.entity.ProjectImage;

public interface ProjectImageMapper {

    /**
     * imageId is uuid
     * @param images
     */
    void insertBaseInfoList(List<ProjectImage> images);

    /**
     * get a ProjectImage except the value of attribute cam_property_matrix
     *
     * @param imageId
     *            the id of ProjectImage
     * @return the ProjectImage which the specified imageId is equal
     */
    ProjectImage getById(@Param("projectId") long projectId, @Param("imageId") String imageId);

    /**
     * set field is_deleted of ProjectImage true. the ProjectImage has the special imageId and projectId.
     *
     * @param imageId
     *            the imageId of ProjectImage
     * @param projectId
     *            the projectId of the project which the image belong to.
     * @throws Exception
     *             something wrong with mysql or system
     */
    void deleteById(@Param("projectId") long projectId, @Param("imageId") String imageId) throws Exception;

    List<ProjectImage> listAll(@Param("projectId") long projectId);

    List<ProjectImage> listAllContainDeleted(@Param("projectId") long projectId);
    /**
     * List image in the specified project and with specified fileSourceType. The images dosen't have camPropertyMatrix
     * value
     *
     * @param projectId
     * @param fileSourceType
     * @return The images without camPropertyMatrix value
     */
    List<ProjectImage> listImages(@Param("projectId") long projectId,
        @Param("fileSourceType") int fileSourceType);

    List<ProjectImageAnnotationVo> listImagesPage(ProjectImageSearchOptionDto queryParameter);

    int countImagesPageTotal(ProjectImageSearchOptionDto queryParameter);

    /**
     * get all file types of image in current projectId
     * @param projectId
     * @return
     */
    List<ProjectImage> listImagesByProjectIdExcludePlaceholder(@Param("projectId") long projectId);

    List<String> listOriginalFileNameIncludeCompleteDeleted(@Param("projectId") long projectId,
            @Param("imageIds") List<String> imageIds,
            @Param("fileSourceTypes") Collection<Integer> fileSourceTypes);

    int deleteImages(@Param("projectId") long projectId, @Param("imageIds") List<String> imageIds);

    int deleteCompletely(
            @Param("projectId") long projectId, @Param("imageIds") List<String> imageIds);

    List<ProjectImage> listImageByImageType(@Param("projectId") long projectId,
        @Param("fileSourceType") int fileSourceType, @Param("imageType") int imageType);

    ProjectImage getImageById(String imageId);

    int countImages(@Param("projectId") long projectId, @Param("fileSourceType") Integer fileSourceType,
        @Param("imageType") Integer imageType, @Param("deleted") Boolean deleted);

    int countImagesForMultiTypes(@Param("projectId") long projectId,
        @Param("fileSourceTypes") Collection<Integer> fileSourceTypes,
        @Param("imageTypes") Collection<Integer> imageTypes, @Param("deleted") Boolean deleted);

    List<ProjectImage> listByPartialType(@Param("projectId") long projectId, @Param("partialType") int partialType);

    int updateImageDeleted(
            @Param("projectId") long projectId,
            @Param("imageIds") List<String> imageIds,
            @Param("deleted") boolean deleted);

    void updateImageType(@Param("projectId") long projectId, @Param("imageId") String imageId,
        @Param("imageType") int imageType);

    /**
     * delete all screenshot in the specified project and has the parent image in the imageId list.
     */
    void deleteScreenshotIn(@Param("projectId") long projectId, @Param("imageIds") List<String> imageIds)
        throws Exception;

    /**
     * delete all the screenshots of the image with <code>imageId</code> in the specified project
     *
     * @param projectId
     * @param imageId
     * @throws Exception
     */
    void deleteScreenshotForImage(@Param("projectId") long projectId, @Param("imageId") String imageId)
        throws Exception;

    List<String> listScreenshotIds(@Param("projectId") long projectId, @Param("imageId") String imageId)
        throws Exception;

    void deleteByReportTypesAndImageIds(@Param("projectId") long projectId,
        @Param("reportTypes") List<Integer> reportTypes, @Param("imageIds") List<String> imageIds);

    void updateImageProperty(@Param("projectId") long projectId, @Param("image") ProjectImage image);

    List<String> listScreenshotIdsIn(@Param("projectId") long projectId, @Param("imageIds") List<String> imageIds)
        throws Exception;

    List<IdTypeDto> listImageFileSourceTypes(@Param("projectId") long projectId) throws Exception;

    List<ProjectImage> listFrontElevationByProjectIds(@Param("projectIds") List<Long> projectIdList,
        @Param("fileSourceType") int fileSourceType, @Param("orientation") int orientation,
        @Param("imageType") int imageType);

    List<ProjectImage> getImageByProjectId(@Param("projectId") long projectId);

    List<ProjectImage> listByImageTypeAndFileSourceType(@Param("projectId") long projectId,
        @Param("imageType") int imageType, @Param("fileSourceType") int fileSourceType,
        @Param("isDeleted") boolean isDeleted);

    void updateReportRelevantData(@Param("projectId") long projectId, @Param("imageList") List<ProjectImage> imageList);

    boolean existByPartialType(
            @Param("projectId") long projectId, @Param("partialTypes") List<Integer> partialTypes);
}
