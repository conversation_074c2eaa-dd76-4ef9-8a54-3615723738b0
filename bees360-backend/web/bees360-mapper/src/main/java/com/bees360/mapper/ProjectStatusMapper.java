package com.bees360.mapper;

import java.util.List;

import com.bees360.entity.ProjectStatus;
import com.bees360.entity.ProjectStatusUser;
import com.bees360.entity.query.ProjectStatusQuery;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2019/12/26 11:57
 */
public interface ProjectStatusMapper {
    int insert(ProjectStatus projectStatus);

    int delete(ProjectStatus projectStatus);

    int deleteBatch(List<ProjectStatus> projectStatuses);

    ProjectStatus getById(long id);

    List<ProjectStatus> listWithQuery(ProjectStatusQuery query);

    List<ProjectStatus> listByProjectId(long projectId);

    List<ProjectStatusUser> listByProjectIdWithUser(long projectId);

    List<ProjectStatus> listByProjectIds(List<Long> projectIds);

    List<ProjectStatusUser> listByProjectIdsWithUser(List<Long> projectIds);

    List<Long> getProjectsByStatusTime(@Param("projectStatus") Integer projectStatus,@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    List<Long> getProjectsByStatusLisAndTime(@Param("projectStatusList") List<Integer> projectStatusList, @Param("startTime") long startTime, @Param("endTime") long endTime);

    List<ProjectStatus> listByProjectIdsAndStatus(@Param("projectIds") List<Long> projectIds, @Param("projectStatus") Integer projectStatus);

    void updateCreatedTimeById(@Param("id") Long id, @Param("createdTime") long createdTime);

}
