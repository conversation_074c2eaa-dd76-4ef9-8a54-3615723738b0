package com.bees360.mapper;

import com.bees360.entity.ProjectInspectionSchedule;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

public interface ProjectInspectionScheduleMapper {

    void save(ProjectInspectionSchedule entity);

    void updateScheduledTime(@Param("projectId") long projectId, @Param("scheduledTime") Long scheduledTime);

    void updateDueDate(@Param("projectId") long projectId, @Param("dueDate") Long dueDate);

    ProjectInspectionSchedule getByProjectId(long projectId);
}
