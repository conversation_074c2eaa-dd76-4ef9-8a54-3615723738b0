package com.bees360.mapper;

import java.util.List;

import com.bees360.entity.ReportTemplateCreateParam;
import org.apache.ibatis.annotations.Param;

import com.bees360.entity.vo.ReportTemplateVo;

public interface ReportTemplateMapper {

	List<ReportTemplateVo> listTemplateByCompanyIdAndReportType(@Param("companyId") long companyId, @Param("reportType") int reportType);

    int insert(ReportTemplateCreateParam param);

    int insertBatch(@Param("params") List<ReportTemplateCreateParam> params);

    void deleteProjectTemplate(@Param("templateId") long templateId);
}
