package com.bees360.mapper;

import com.bees360.entity.Company;
import com.bees360.entity.Project;
import com.bees360.entity.ProjectTimeline;
import com.bees360.entity.dto.BeesPilotProjectDto;
import com.bees360.entity.dto.CreateOrUpdateProjectDto;
import com.bees360.entity.dto.ProjectSearchOptionForApp;
import com.bees360.entity.dto.ProjectStatistics;
import com.bees360.entity.query.BeesPilotProjectQuery;
import com.bees360.entity.query.ProjectFilterQuery;
import com.bees360.entity.vo.ProjectAbstractVo;
import com.bees360.entity.vo.ProjectCalSelectVo;
import com.bees360.entity.vo.ProjectImageTinyVoForApp;
import com.bees360.entity.vo.ProjectTinyVo;
import com.bees360.entity.vo.ProjectTinyVoForApp;
import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

public interface ProjectMapper {
//	Select One
	Project getById(long projectId);

    ProjectCalSelectVo getCalSelectVoById(@Param("centerUtcOffset") long centerUtcOffset, @Param("projectId")long projectId);

    List<Project> listByLatestStatus(@Param("latestStatus") int latestStatus, @Param("createdBy") Integer createdBy,
                                     @Param("startTime") Integer startTime, @Param("endTime") Integer endTime);

	List<Project> listByCompanyAndUserId(@Param("companyId") Long companyId, @Param("userId") long userId) throws Exception;

    List<Project> listByInsuranceCompanyAndStatus(@Param("companyId") Long companyId, @Param("status") Integer status,
                                         @Param("createdTime") long createdTime);

    List<Project> listInTimeRange(@Param("startTime") Long createTimeStart, @Param("endTime") Long createTimeEnd);


    /**
	 * list the id of the project which the user took part in.
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	// <EMAIL> improve the effect of the sql replaying the in type with "inner join" type
	List<Project> listByUserId(long userId) throws Exception;

	List<Long> listProjectIdUserIn(long userId) throws Exception;

	ProjectAbstractVo getAbstractById(long projectId) throws Exception;

	List<ProjectTinyVo> listTinyProjectWithSearch(Map<String, Object> searchMap);

    List<ProjectTinyVo> listProjectsForOpenapi(Map<String, Object> searchMap);

    List<Project> listProjectWithSearch(Map<String, Object> searchMap);

	int countTinyProjectWithSearch(Map<String, Object> searchMap);

    List<Long> listProjectIdWithSearch(Map<String, Object> searchMap);

	List<ProjectTinyVo> listTinyProjectByUserId(@Param("userId") long userId,
                                                @Param("startIndex") int startIndex, @Param("pageSize") int pageSize);

	/**
	 * list the images which has projectId in specific projectIds and a file source type in specific file source type.
	 * @param projectIds if the projectIds is empty, it will search in all projects
	 * @param fileSourceTypes it should have at least on element
	 * @return
	 * @throws Exception
	 */
	List<ProjectImageTinyVoForApp> listTinyProjectImageVoForApp(@Param("projectIds") List<Long> projectIds,
                                                                @Param("fileSourceTypes") List<Integer> fileSourceTypes) throws Exception;

	List<Map<String,Object>> listAllZeroLatLngsAddress();

	void updateProjectLatLng(@Param("projectId") long projectId,
                             @Param("lat") double lat, @Param("lng") double lng);

	/**
	 * list the project id and its created time. The project contained shouldn't be deleted.
	 * @param userId
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws Exception
	 */
	List<Map<String, Long>> listProjectIdAndCreatedTime(@Param("userId") long userId,
                                                        @Param("startTime") Long startTime, @Param("endTime") Long endTime);

	void insert(Project project) throws Exception;

	/**
	 * update the latest status of the Project
	 * @param projectId
	 * @param processStatus
	 * @throws Exception
	 */
	boolean updateLatestStatus(@Param("projectId") long projectId, @Param("processStatus") int processStatus);

	void updateProjectBaseInfor(Project project) throws Exception;

	String getStateById(long projectId) throws Exception;

	long insertBaseInfo(Project project);

	void updateClaimInfo(Map<String, Object> newCiMap) throws Exception;

    /**
     * 根据map中含有的字段进行更新，但只更新非null的字段
     */
	void update(@Param("projectId") long projectId, @Param("fields") Map<String, Object> fields);

	void updateNorth(@Param("projectId") long projectId, @Param("north") String north);

	void updateInspectionTypes(@Param("projectId") long projectId, @Param("inspectionTypes") long inspectionTypes) throws Exception;

    void updateInspectionTime(@Param("projectId") long projectId, @Param("inspectionTime") Long inspectionTime);

    List<Company> listComaniesInProjects(@Param("companyType") Integer companyType, @Param("curUserId") Long curUserId,
		@Param("managedBy") Long managedBy);

	boolean exist(long projectId);

	List<ProjectTinyVoForApp> listTinyProjectForApp(ProjectSearchOptionForApp projectSearch) throws Exception;

	int countTinyProjectForApp(ProjectSearchOptionForApp projectSearch) throws Exception;

	List<ProjectTinyVoForApp> listProjectsForAppByIds(@Param("curUserId") Long curUserId, @Param("managedBy") Long managedBy,
		@Param("projectIds") List<Long> projectIds);

	void updateDamageSeverity(@Param("projectId") long projectId, @Param("damageSeverity") int damageSeverity) throws Exception;

	void updateProjectCompanyId(@Param("userId") long userId, @Param("oldCompanyId") long oldCompanyId,
                                @Param("newCompanyId") long newCompanyId);

	void updateChimney(@Param("projectId") long projectId, @Param("chimney") int chimney);

	void updateRotationDegree(@Param("projectId") long projectId, @Param("rotationDegree") double rotationDegree);

	/**
	 * 更新图片的归档文件url
	 */
	public void updateImagesArchiveUrl(@Param("projectId") long projectId, @Param("imagesArchiveUrl") String imagesArchiveUrl);

	String getImagesArchiveUrl(long projectId);

    /**
     * 根据map中含有的字段进行更新，无论该值是否为null
     */
    int updateByMap(@Param("projectId") long projectId, @Param("fields") Map<String, Object> fields);

    void updateProjectStatus(@Param("projectId") long projectId, @Param("projectStatus") int status,
	    @Param("statusUpdateTime") Long statusUpdateTime, @Param("version") Long version);

    /**
     * 更新项目图片上传的状态
     * @param projectId 项目主键ID（projectId)
     * @param status 图片上传的状态
     * @see com.bees360.entity.enums.ImageUploadStatusEnum
     * @return 更新否成功
     */
    @Deprecated
    boolean updateImageUploadStatus(@Param("projectId") long projectId, @Param("status") int status);

    List<Project> listProjects(@Param("projectIds") List<Long> projectIds);

    List<Long> listProjectIdsByAddressId(String addressId);

    /**
     * 批量更新serviceType
     * @param projectIds
     * @param serviceType
     */
    void batchUpdateServiceType(@Param("projectIds") List<Long> projectIds, @Param("serviceType") int serviceType);

    void updateReportRelevantData(Project project);

	List<Project> listProjectsWithFilter(ProjectFilterQuery projectFilterQuery);

    /**
     * list bees pilot project
     * @param projectSearch
     * @return
     */
	List<BeesPilotProjectDto> listProjectForBeesPilot(BeesPilotProjectQuery projectSearch);

	List<Project> listByInspectionNumber(@Param("inspectionNumber") String inspectionNumber,
                                         @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<Project> listByInspectionNumberStartWith(@Param("inspectionNumber") String inspectionNumber,
        @Param("excludeStatuses") Set<Integer> excludeStatuses);

    void updatePlnarURL(@Param("projectId") long projectId, @Param("plnarURL") String plnarURL);

	void batchUpdatePlnarURL(@Param("projectIds") List<Long> projectIds, @Param("plnarURL")String plnarURL);

	void updatePlnarStatus(@Param("projectId") Long projectId, @Param("status") Integer status);

	void updateHoverJobStatus(@Param("projectId") Long projectId, @Param("status") Integer status);

    void updatePayStatus(@Param("projectIds") List<Long> projectIds, @Param("status") int status);

    @Deprecated
    void updateClaimNote(@Param("projectId") Long projectId, @Param("claimNote") String claimNote);

	int updateHoverJobId(@Param("projectId") Long projectId, @Param("hoverJobId") Long hoverJobId);

    void updateProject(CreateOrUpdateProjectDto projectDto);

    void upsertProjectTimeline(ProjectTimeline projectTimeline);

    List<Project> listProjectByClaimNumberOrInspectionNumber(@Param("claimNumber") String claimNumber, @Param("inspectionNumber") String inspectionNumber);

    List<Project> listByPolicyNumber(@Param("policyNumber") String policyNumber, @Nullable @Param("companyId") Long companyId,
                                     @Nullable @Param("createAtStart") Long createAtStart, @Nullable @Param("createAtEnd") Long createAtEnd);

    List<Long> listProjectId(ProjectFilterQuery query);

    List<ProjectStatistics.ProjectStatusServiceType> listProjectStatusView(@Param("filterTest") boolean filterTest,
                                                                           @Param("filterInsuredNull") boolean filterInsuredNull,
                                                                           @Param("filterStatus") Set<Integer> filterStatus);

    void updateProjectProperty(@Param("projectId") long projectId, @Param("propertyType") Integer propertyType);

    Set<Long> listAllCreators();
}
