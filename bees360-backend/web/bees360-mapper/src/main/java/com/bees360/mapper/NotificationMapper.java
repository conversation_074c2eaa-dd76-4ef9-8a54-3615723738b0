package com.bees360.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.bees360.entity.Notification;


public interface NotificationMapper {

	/**
	 * Save notification into the database;
	 * <AUTHOR>
	 * @param notification An example from the Notification Class.
	 * @throws Exception
	 */
	void insertNotification(Notification notification) throws Exception;

	void insertRoleApplicationNotification(Notification notification) throws Exception;

	List<Notification> listAll(@Param("userId")long userId) throws Exception;

	List<Notification> listPage(@Param("userId")long userId, @Param("startIndex")int startIndex,
			@Param("pageSize")int pageSize) throws Exception;

	int count(long userId) throws Exception;

	void deleteAllByUserId(@Param("userId")long userId) throws Exception;

	void deleteOne(@Param("userId")long userId, @Param("notificationId")long notificationId) throws Exception;

}
