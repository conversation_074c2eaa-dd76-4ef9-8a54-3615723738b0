package com.bees360.mapper.label;

import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.entity.label.ProjectLabel;
import org.apache.ibatis.annotations.Param;

import java.time.Instant;
import java.util.List;

public interface ProjectLabelMapper {

    int insert(ProjectLabel label);

    List<ProjectLabel> listLabels();

    List<BoundProjectLabel> getProjectLabels(@Param("projectIds") List<Long> projectIds);

    void markLabelForProjects(@Param("projectIds") List<Long> projectIds, @Param("labelId") long labelId);

    void markLabelsForProject(@Param("projectId")Long projectId, @Param("labelIds")List<Long> labelIds);

    void eraseLabelForProjects(@Param("projectIds") List<Long> projectIds, @Param("labelId") long labelId);

    void eraseLabelsForProject(@Param("projectId")Long projectId, @Param("labelIds") List<Long> labelIds);

    void eraseAllForProject(Long projectId);

    List<BoundProjectLabel> getByLabelIdAndCreatedTime(@Param("labelId") long labelId,
                                                       @Param("startTime") Instant startTime,
                                                       @Param("endTime") Instant endTime);

}
