package com.bees360.mapper;

import com.bees360.entity.InvoiceFile;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/11
 */
public interface InvoiceFileMapper {

    InvoiceFile getById(long id);

    InvoiceFile getByProjectIdAndId(@Param("projectId")long projectId, @Param("id")long id);

    InvoiceFile getByProjectIdAndInvoiceId(@Param("projectId")long projectId, @Param("invoiceId")long invoiceId);

    List<InvoiceFile> listByProjectId(long projectId);

    InvoiceFile getInvoiceFileForProject(long projectId);

    void insert(InvoiceFile invoiceFile);

    void delete(long id);

    void deleteByProjectIdAndInvoiceId(@Param("projectId")long projectId, @Param("invoiceId") long invoiceId);

    void deleteInvoiceFileForProject(@Param("projectId")long projectId);

}
