package com.bees360.mapper;

import com.bees360.entity.ProjectSyncLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProjectSyncLogMapper {

    List<ProjectSyncLog> getByProjectId(@Param("projectId") Long projectId);

    void insert(@Param("param") ProjectSyncLog param);

    void updateStatus(@Param("id") Long id, @Param("status") Integer status);

    boolean existedSync(@Param("projectId")long projectId, @Param("event")String event);

    boolean successSync(@Param("projectId")long projectId, @Param("event")String event);


}
