package com.bees360.mapper.openapi;

import com.bees360.entity.openapi.client.OpenApiClient;
import com.bees360.entity.openapi.client.OpenApiSearchFilterOption;

import java.util.List;

public interface OpenApiClientMapper {

    int insert(OpenApiClient openApiClient);

    int update(OpenApiClient openApiClient);

    int delete(long id);

    int deleteByClientId(String clientId);

    List<OpenApiClient> listAll();

    List<OpenApiClient> search(OpenApiSearchFilterOption filterOption);

    OpenApiClient getById(long id);

    OpenApiClient getByClientId(String clientId);

}
