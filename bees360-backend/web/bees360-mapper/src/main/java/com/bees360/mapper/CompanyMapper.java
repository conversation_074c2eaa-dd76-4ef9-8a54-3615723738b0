package com.bees360.mapper;

import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.bees360.entity.Company;
import com.bees360.entity.dto.CompanySearchOption;

public interface CompanyMapper extends BaseMapper<Company> {

	Company getByName(String companyName);

    Long getIdByKey(@Param("companyKey") String companyKey);

	List<Company> listByType(int companyType);

	List<Company> listWithPrefix(@Param("prefix")String prefix, @Param("limit")int limit);

	List<Company> listByTypeWithPrefix(@Param("companyType")int companyType,
			@Param("prefix")String prefix, @Param("limit")int limit);

	List<Company> listByOption(CompanySearchOption option);

	List<Company> listIn(@Param("companyIds") Collection<Long> companyIds);

	int countByOption(CompanySearchOption option);

	Company getByUserId(long userId);

	int delete(@Param("companyId")long companyId, @Param("updatedTime")long updatedTime);

    void updateLogo(@Param("companyId") long companyId, @Param("logo") String logo,
        @Param("updatedTime")long updatedTime);

    int updateCompanyKey(
        @Param("companyId") long companyId,
        @Param("companyKey") String companyKey,
        @Param("updatedTime") long updatedTime);
}
