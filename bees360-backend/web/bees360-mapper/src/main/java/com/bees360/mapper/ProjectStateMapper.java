package com.bees360.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProjectStateMapper {
    void save(@Param("projectId") long projectId,
              @Param("projectState") String projectState,
              @Param("projectStateReason") String projectStateReason,
              @Param("projectStateReasonId") String projectStateReasonId,
              @Param("updatedAt")Long updatedAt);

    List<Long> findProjectIdByChangeReasonIds(@Param("changeReasonIds") List<String> changeReasonIds);
}
