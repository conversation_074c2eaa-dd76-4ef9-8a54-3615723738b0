package com.bees360.mapper;


import com.bees360.entity.BeesBatchQuery;
import com.bees360.entity.BeesPilotBatch;
import com.bees360.entity.BeesPilotBatchRequest;
import com.bees360.entity.BeesPilotStatus;
import com.bees360.entity.query.BeesPilotStatusQuery;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface BeesPilotBatchMapper {

    boolean insert(BeesPilotBatch record);

    BeesPilotBatch getByBatchNo(@Param("batchNo") String batchNo);

    BeesPilotBatch getByBatchNoWithoutDeletedCheck(@Param("batchNo") String batchNo);

    List<BeesPilotBatch> listBatch(@Param("query") BeesBatchQuery query);

    void deleteByBatchNo(@Param("batchNo") String batchNo);

    boolean updatePilotBatch(BeesPilotBatchRequest batch);

    void updatePilotBatchPrice(@Param("batch") BeesPilotBatch batch);

    void updatePilotBatchDeletedStatus(@Param("batchNo") String batchNo, @Param("isDeleted") Boolean isDeleted);

    List<BeesPilotBatch> getByPlanPaymentTime(@Param("startDate") LocalDate startDate, @Param("endDate")LocalDate endDate);
}
