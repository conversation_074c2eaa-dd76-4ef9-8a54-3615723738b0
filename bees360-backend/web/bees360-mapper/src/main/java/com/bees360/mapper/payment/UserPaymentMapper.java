package com.bees360.mapper.payment;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.bees360.entity.UserPayment;

public interface UserPaymentMapper {

	public void insert(UserPayment userPayment) ;

	public void inserts(List<UserPayment> userPaymentList);

	public List<UserPayment> getUserPayments(@Param("userId")long userId);

	/**
	 * list the userPayment of the specified project and if there is a not null userId
	 * it will list the userPayment create by this user in the project.
	 * @param projectId
	 * @param userId
	 * @return
	 */
	public List<UserPayment> listUserPaymentsByProjectId(@Param("projectId")long projectId,
			@Param("userId")Long userId);

	public List<UserPayment> getUserPaymentsByProjectId(@Param("userId")long userId,
			@Param("projectId")long projectId);

	public int count(@Param("userId")long userId);

	public List<UserPayment> listPayments(@Param("userId")long userId,
			@Param("paidTimeStart")Long paidTimeStart, @Param("paidTimeEnd")Long paidTimeEnd);
}
