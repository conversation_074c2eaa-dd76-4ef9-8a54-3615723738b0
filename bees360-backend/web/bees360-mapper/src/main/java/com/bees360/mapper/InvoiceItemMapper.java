package com.bees360.mapper;

import com.bees360.entity.InvoiceItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/15
 */
public interface InvoiceItemMapper {
    InvoiceItem getById(long id);
    List<InvoiceItem> listByInvoiceId(long invoiceId);

    void insert(InvoiceItem invoiceItem);
    void insertBatch(@Param("invoiceItems") List<InvoiceItem> invoiceItems);
}
