package com.bees360.mapper;


import com.bees360.entity.BeesPilotBatchItem;
import com.bees360.entity.BeesPilotBatchItemVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BeesPilotBatchItemMapper {

    boolean insert(BeesPilotBatchItem record);

    List<BeesPilotBatchItem> listByProjectIds(@Param("projectIds") List<Long> projectIds);

    void batchInsert(List<BeesPilotBatchItem> item);

    List<BeesPilotBatchItem> listByBatchNo(@Param("batchNo") String batchNo);

    List<BeesPilotBatchItemVo> listByProjectIdsWithBatchInfo(@Param("projectIds") List<Long> projectIds);

    void deleteByBatchNo(@Param("batchNo") String batchNo);

    void deleteByProjectIds(@Param("projectIds") List<Long> projectIds);

    void updateBatchItemDeletedStatusByProjectIds(@Param("batchNo") String batchNo, @Param("projectIds") List<Long> projectIds, @Param("isDeleted") Boolean isDeleted);

    List<BeesPilotBatchItem> listByBatchNoWithoutDeletedCheck(String batchNo);

    List<BeesPilotBatchItem> listByProjectIdsWithoutDeletedCheck(@Param("projectIds") List<Long> projectIds);
}
