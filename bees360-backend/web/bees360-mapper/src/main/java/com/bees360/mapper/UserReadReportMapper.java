package com.bees360.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.bees360.entity.UserReadReport;

public interface UserReadReportMapper {
	List<UserReadReport> list(@Param("userId")long userId, @Param("projectId")long projectId);
	List<UserReadReport> listIn(@Param("userId")long userId, @Param("projectIds")List<Long> projectIds);
	List<UserReadReport> listByReportType(@Param("userId")long userId, @Param("projectId")long projectId,
			@Param("reportType")int reportType);

	UserReadReport getByReportId(@Param("userId")long userId, @Param("projectId")long projectId,
			@Param("reportId")String reportId);

	void insert(UserReadReport userReadReport);

	int deleteByReportId(@Param("userId")Long userId, @Param("projectId")long projectId,
			@Param("reportId")String reportId, @Param("updatedTime")long updatedTime);
	int deleteByReportType(@Param("userId")Long userId, @Param("projectId")long projectId,
			@Param("reportType")int reportType,	@Param("updatedTime")long updatedTime);
}
