package com.bees360.mapper;

import com.bees360.entity.InvoiceDiscount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/10
 */
public interface InvoiceDiscountMapper {

    void insert(InvoiceDiscount invoiceDiscount);

    void insertBatch(@Param("list") List<InvoiceDiscount> invoiceDiscounts);

    InvoiceDiscount getById(long id);

    List<InvoiceDiscount> listByInvoiceId(long invoiceId);
}
