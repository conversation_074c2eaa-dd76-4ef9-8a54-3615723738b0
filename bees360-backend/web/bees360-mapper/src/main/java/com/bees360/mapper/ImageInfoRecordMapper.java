package com.bees360.mapper;

import com.bees360.entity.dto.ImageInfoRecord;
import com.bees360.entity.query.ImageRecordInfoQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/30 2:31 下午
 **/
public interface ImageInfoRecordMapper {
    List<ImageInfoRecord> listImageInfo(ImageRecordInfoQuery query);

    void updateRecord(@Param(value = "projectId") long projectId, @Param(value = "item") ImageInfoRecord record);
}
