package com.bees360.mapper.stat;

import com.bees360.entity.stat.dto.ProjectCardDataDto;
import com.bees360.entity.stat.dto.ProjectListDataDto;
import com.bees360.entity.stat.dto.ProjectStatCardDto;
import com.bees360.entity.stat.dto.ProjectStatChartDto;
import com.bees360.entity.stat.search.StatFullSearchOption;

import java.util.List;

public interface ProjectStatMapper {

    List<ProjectCardDataDto> incompletedCardStat(StatFullSearchOption fullSearchOption);

    List<ProjectStatCardDto> cardStat(StatFullSearchOption fullSearchOption);

    List<ProjectCardDataDto> getRiskScoreCardStatData(StatFullSearchOption fullSearchOption);


    List<ProjectStatChartDto> chartStat(StatFullSearchOption fullSearchOption);

    List<ProjectStatChartDto> riskScoreChartStat(StatFullSearchOption fullSearchOption);


    List<ProjectListDataDto> listCreatedWithSearch(StatFullSearchOption fullSearchOption);

    List<ProjectListDataDto> listIncompletedWithSearch(StatFullSearchOption fullSearchOption);

    List<ProjectListDataDto> listCompletedWithSearch(StatFullSearchOption fullSearchOption);

    List<ProjectListDataDto> searchRiskScoreList(StatFullSearchOption fullSearchOption);

    Integer projectListCount(StatFullSearchOption fullSearchOption);

    List<Long> projectListId(StatFullSearchOption fullSearchOption);

    Integer searchRiskScoreCount(StatFullSearchOption fullSearchOption);

    List<Long> searchRiskScoreId(StatFullSearchOption fullSearchOption);
}
