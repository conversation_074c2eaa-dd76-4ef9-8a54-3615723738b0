package com.bees360.mapper;

import com.bees360.entity.ReportSummary;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportSummaryMapper {

    List<ReportSummary> listByProjectId(long projectId);

    List<ReportSummary> listByProjectIds(List<Long> projectIds);

    ReportSummary getOne(@Param("projectId") long projectId, @Param("reportType") int reportType);

    int insert(ReportSummary summary);

    int update(ReportSummary summary);
}
