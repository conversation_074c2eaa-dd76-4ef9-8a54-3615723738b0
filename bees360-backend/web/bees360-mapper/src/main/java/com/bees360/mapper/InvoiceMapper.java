package com.bees360.mapper;

import com.bees360.entity.Invoice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/08 17:46
 */
public interface InvoiceMapper {

    Invoice getById(long invoiceId);

    Invoice getByProjectIdAndInvoiceId(@Param("projectid")long projectId, @Param("invoiceId")long invoiceId);

    List<Invoice> listByProjectId(long projectId);

    List<Invoice> listByUserId(@Param("userId")long userId,
        @Param("startTime") long startTime, @Param("endTime") long endTime,
        @Param("startIndex")int startIndex, @Param("pageSize")int pageSize);

    void insert(Invoice invoice);

    void deleteById(long invoiceId);
}
