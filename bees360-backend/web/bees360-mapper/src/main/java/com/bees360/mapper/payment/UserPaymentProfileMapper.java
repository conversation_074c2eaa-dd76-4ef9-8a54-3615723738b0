package com.bees360.mapper.payment;

import org.apache.ibatis.annotations.Param;
import com.bees360.entity.UserPaymentProfile;

public interface UserPaymentProfileMapper {

	public UserPaymentProfile getUserPaymentProfile(@Param("userId") long userId);

	public void updateUserPaymentProfileByBalance(@Param("userId") long userId, @Param("walletBalance") double walletBalance, @Param("currency") String currency);

	public void updateUserPaymentProfileByCommission(@Param("userId") long userId, @Param("commissionBalance") double commissionBalance, @Param("currency") String currency);

	public void updateUserDiscountPercent(@Param("userId") long userId, @Param("discountPercent") double discountPercent);

	public void updateFreeOnTrailProjectNum(@Param("userId") long userId, @Param("freeOnTrailProjectNum") int freeOnTrailProjectNum);

	public void subFreeOnTrailProjectNum(@Param("userId") long userId, @Param("num") int num);

	public void updateNewCustomerTrailProjectNumAndDiscount(@Param("userId") long userId, @Param("freeOnTrailProjectNum") int freeOnTrailProjectNum,
			@Param("newCustomerDiscountPercent") double discountPercent, @Param("newCustomerDiscountProjectNum") int projectNum);

	public void updateNewCustomerDiscountAndProjectNum(@Param("userId") long userId,
			@Param("newCustomerDiscountPercent") double discountPercent, @Param("newCustomerDiscountProjectNum") int projectNum);

	public void subNewCustomerDiscountProjectNum(@Param("userId") long userId, @Param("num") int num);
}
