package com.bees360.mapper;

import com.bees360.entity.ProjectMessage;
import com.bees360.entity.ProjectQuiz;
import com.bees360.entity.Quiz;
import com.bees360.entity.query.ProjectMessageQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/4/10 12:21 PM
 **/
public interface ProjectMessageMapper {
    /**
     * 批量插入项目留言
     *
     * @param projectMessages 项目留言列表
     * @return 插入成功数
     */
    int batchInsert(List<ProjectMessage> projectMessages);

    boolean insert(ProjectMessage message);

    /**
     * 查询项目留言
     *
     * @param query 项目ID
     * @return 项目问卷回答列表
     */
    List<ProjectMessage> listMessage(@Param(value = "query") ProjectMessageQuery query);

    ProjectMessage getByPrimaryKey(long id);

    ProjectMessage getLatestMessage(@Param(value = "query") ProjectMessageQuery query);

    /**
     * 根据查询条件删除消息
     * @return 删除数据的条数
     */
    int delete(@Param(value = "query") ProjectMessageQuery query);
}
