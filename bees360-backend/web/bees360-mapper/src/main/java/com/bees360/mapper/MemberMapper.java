package com.bees360.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.bees360.entity.Member;
import com.bees360.entity.dto.IdValue;

public interface MemberMapper {
	void insert(Member member);

    /**
     * 批量插入Member数据
     * @param members
     */
    void insertBatch(List<Member> members);

	List<Member> listActiveMember(long projectId);

	/**
	 * get an active member playing this role from the project,
	 * no matter what active_status the user is.
	 *
	 * @param projectId
	 * @param userId
	 * <pre>{@code
	 * 	int role = RoleEnum.getCode();
	 * }</pre>
	 *
	 * @return an active member playing this role from the project
	 * @throws Exception something wrong with mysql or system
	 */
	List<Member> listActiveMembersByUserId(@Param("projectId")long projectId,
			@Param("userId") long userId);

	/**
	 * get an active member playing this role from the project,
	 * no matter what active_status the user is.
	 * If you use this method, please make sure the member with this one just has only one.
	 * @param projectId
	 * @param role the code the RoleEnum
	 * <pre>{@code
	 * 	int role = RoleEnum.getCode();
	 * }</pre>
	 *
	 * @return an active member playing this role from the project
	 * @throws Exception something wrong with mysql or system
	 */
	Member getActiveMemberByRole(@Param("projectId") long projectId,
			@Param("role") int role);

	/**
	 *
	 * @param projectId
	 * @param role
	 * @return
	 * @throws Exception
	 */
	List<Member> listActiveMembersByRole(@Param("projectId") long projectId,
			@Param("role") int role);

	/**
	 * get an member with {userId}, playing the role from the Project.
	 * no matter what active_status the user is, and the member was deleted or not.
	 *
	 * @param projectId
	 * @param userId
	 * @param role the code the RoleEnum
	 * <pre>{@code
	 * 	int role = RoleEnum.getCode();
	 * }</pre>
	 *
	 * @return
	 * @throws Exception
	 */
	Member getMemberByRoleAndUserId(@Param("projectId") long projectId,
			@Param("userId")long userId, @Param("role") int role);

	Long getAssetOwnerId(long projectId);

	int delete(@Param("projectId")long projectId,
			@Param("userId")long userId, @Param("role") int role);

	void activeMember(@Param("projectId") long projectId,
			@Param("userId")long userId, @Param("role") int role);

    void activeMembers(@Param("projectIds") Collection<Long> projectIds, @Param("userId") long usrId,
        @Param("role") int role);

	List<Member> listAllByUser(@Param("projectId")long projectId,
			@Param("userId")long userId);

	/**
	 * list the active members of this user. The deleted projects contains the members are included.
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	List<Member> listActiveMembers(@Param("userId")long userId, @Param("startTime")Long startTime,
			@Param("endTime")Long endTime);

	List<IdValue> listRolesGroupByProject(@Param("userId")long userId,
			@Param("projectIds")Collection<Long> projectIds) throws Exception;

	List<Member> listActiveMembersIn(@Param("projectIds")Collection<Long> projectIds,
			@Param("role")Integer role);

	List<Member> listMembersUserIn(@Param("projectIds")Collection<Long> projectIds,
	    @Param("userId")Long userId, @Param("role")Integer role, @Param("isDeleted")Boolean isDeleted);

	Set<Long> listProjectIdWithActiveMember(@Param("role")int role, @Param("userId") Long userId);
}
