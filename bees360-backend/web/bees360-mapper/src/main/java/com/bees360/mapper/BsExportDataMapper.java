package com.bees360.mapper;

import com.bees360.entity.BsExportData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BsExportDataMapper {

    int insertExportData(@Param("param")BsExportData param);

    List<BsExportData> getExportData(@Param("relatedId") String relatedId, @Param("relatedType") String relatedType);

    List<BsExportData> listAll();

    BsExportData getById(@Param("id") long id);

    void updateExportData(@Param("param") BsExportData param);

    void deleteExportData(long id);

    List<BsExportData> listIn(@Param("relatedIds") List<String> relatedIds, @Param("relatedType") String relatedType);
}
