package com.bees360.mapper;

import com.bees360.entity.dto.DataDictionary;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DataDictionaryMapper {
    /**
     * 根据命名空间和具体业务代码，查询唯一的一条数据字典
     * @param namespace
     * @param code
     * @return
     */
    DataDictionary getByNamespaceAndCode(@Param("namespace") String namespace, @Param("code") String code);

    /**
     * 查询同一命名空间下的数据字典，也就是同一业务的数据字典，根据排序号进行排序
     * @param namespace
     * @return
     */
    List<DataDictionary> listByNamespace(String namespace);

    void insert(DataDictionary dataDictionary);

    boolean updateByNamespaceAndCode(DataDictionary dataDictionary);
}
