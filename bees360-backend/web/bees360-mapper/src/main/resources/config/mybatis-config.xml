<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<!--
    更多详细的配置说明：https://mybatis.org/mybatis-3/zh/configuration.html
-->
<configuration>
    <properties>
        <!--set mybatis info output -->
        <property name="dialect" value="mysql" />
    </properties>

    <settings>
        <!-- set SIMPLE executor for a batch of insert which was expected to return self-growing id -->
        <setting name="defaultExecutorType" value="SIMPLE" />
        <!--         <setting name="defaultExecutorType" value="BATCH" /> -->
        <setting name="cacheEnabled" value="true"/>
        <setting name="lazyLoadingEnabled" value="true"/>
        <setting name="aggressiveLazyLoading" value="false"/>
        <setting name="multipleResultSetsEnabled" value="true"/>
        <setting name="useColumnLabel" value="true"/>
        <setting name="useGeneratedKeys" value="true"/>
        <setting name="autoMappingBehavior" value="FULL"/>
        <setting name="defaultStatementTimeout" value="25000"/>
        <setting name="logImpl" value="SLF4J" />

        <!-- 开启驼峰，开启后，只要数据库字段和对象属性名字母相同，无论中间加多少下划线都可以识别 -->
        <!-- 开启驼峰，可以不需要写ResultMap可以自动匹配 -->
        <setting name="mapUnderscoreToCamelCase" value="true" />
    </settings>

    <typeAliases>
        <typeAlias alias="Integer" type="java.lang.Integer"/>
        <typeAlias alias="Long" type="java.lang.Long"/>
        <typeAlias alias="String" type="java.lang.String" />
        <typeAlias alias="Double" type="java.lang.Double" />
        <typeAlias alias="Float" type="java.lang.Float" />
        <typeAlias alias="HashMap" type="java.util.HashMap"/>
        <typeAlias alias="LinkedHashMap" type="java.util.LinkedHashMap"/>
        <typeAlias alias="ArrayList" type="java.util.ArrayList"/>
        <typeAlias alias="LinkedList" type="java.util.LinkedList"/>

        <!-- 设置别名，允许在mapper.xml中直接使用类名，如用pilot代替com.bees360.entity.pilot.Pilot -->
        <package name="com.bees360.entity"/>
    </typeAliases>

</configuration>
