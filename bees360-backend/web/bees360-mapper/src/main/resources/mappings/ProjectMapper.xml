<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
[!important] each sql should consider a user can play multiple role in a project which will cause many repeated records.

 -->

<mapper namespace="com.bees360.mapper.ProjectMapper">

	<resultMap id="baseResultMap" type="com.bees360.entity.Project">
	    <id column="project_id" jdbcType="BIGINT" property="projectId"/>
	    <result column="policy_number" jdbcType="BIGINT" property="policyNumber"/>
	    <result column="claim_number" jdbcType="VARCHAR" property="claimNumber"/>
        <result column="cat_number" jdbcType="VARCHAR" property="catNumber"/>
	    <result column="claim_rcv" jdbcType="FLOAT" property="claimRcv"/>
	    <result column="claim_acv" jdbcType="FLOAT" property="claimAcv"/>
	    <result column="claim_type" jdbcType="INTEGER" property="claimType"/>
	    <result column="created_time" jdbcType="BIGINT" property="createdTime"/>
	    <result column="damage_event_time" jdbcType="BIGINT" property="damageEventTime"/>
	    <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
	    <result column="project_type" jdbcType="INTEGER" property="projectType"/>
	    <result column="address" jdbcType="VARCHAR" property="address"/>
	    <result column="city" jdbcType="VARCHAR" property="city"/>
	    <result column="state" jdbcType="VARCHAR" property="state"/>
	    <result column="country" jdbcType="VARCHAR" property="country"/>
	    <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>
	    <result column="gps_location_longitude" jdbcType="DOUBLE" property="gpsLocationLongitude"/>
	    <result column="gps_location_latitude" jdbcType="DOUBLE" property="gpsLocationLatitude"/>
        <result column="address_id" jdbcType="VARCHAR" property="addressId"/>
	    <result column="asset_owner_name" jdbcType="VARCHAR" property="assetOwnerName"/>
	    <result column="asset_owner_phone" jdbcType="VARCHAR" property="assetOwnerPhone"/>
	    <result column="asset_owner_email" jdbcType="VARCHAR" property="assetOwnerEmail"/>
	    <result column="insurance_company" jdbcType="BIGINT" property="insuranceCompany"/>
	    <result column="repair_company" jdbcType="BIGINT" property="repairCompany"/>
	    <result column="material_provider_company" jdbcType="BIGINT" property="materialProviderCompany"/>
	    <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="claim_note" jdbcType="VARCHAR" property="claimNote"/>
	    <result column="latest_status" jdbcType="INTEGER" property="latestStatus"/>
		<result column="project_status" jdbcType="INTEGER" property="projectStatus" />
	    <result column="north" jdbcType="VARCHAR" property="north"/>
        <result column="inspection_types" jdbcType="BIGINT" property="inspectionTypes"/>
	    <result column="inspection_time" jdbcType="BIGINT" property="inspectionTime"/>
	    <result column="damage_severity" jdbcType="INTEGER" property="damageSeverity" />

        <result column="is_booking" jdbcType="INTEGER" property="booking"/>
        <result column="contacter_name" jdbcType="VARCHAR" property="contacterName"/>
        <result column="contacter_email" jdbcType="VARCHAR" property="contacterEmail"/>
        <result column="contacter_phone" jdbcType="VARCHAR" property="contacterPhone"/>
        <result column="roof_estimated_area_item" jdbcType="INTEGER" property="roofEstimatedAreaItem"/>
        <result column="report_service_option" jdbcType="INTEGER" property="reportServiceOption"/>
        <result column="need_pilot" jdbcType="INTEGER" property="needPilot"/>
        <result column="chimney" jdbcType="INTEGER" property="chimney"/>
        <result column="fly_zone_type" jdbcType="INTEGER" property="flyZoneType"/>
		<result column="images_archive_url" jdbcType="VARCHAR" property="imagesArchiveUrl"/>
		<result column="rotation_degree" jdbcType="DOUBLE" property="rotationDegree"/>

		<result column="inspection_number" jdbcType="VARCHAR" property="inspectionNumber"/>
		<result column="due_date" jdbcType="BIGINT" property="dueDate"/>
		<result column="customer" jdbcType="VARCHAR" property="customer"/>
		<result column="inspection_type" jdbcType="VARCHAR" property="inspectionType"/>
		<result column="agent" jdbcType="VARCHAR" property="agent"/>
		<result column="agent_email" jdbcType="VARCHAR" property="agentEmail"/>
		<result column="agent_contact_name" jdbcType="VARCHAR" property="agentContactName"/>
		<result column="agent_phone" jdbcType="VARCHAR" property="agentPhone"/>
		<result column="guideline" jdbcType="VARCHAR" property="guideline"/>
		<result column="insured_home_phone" jdbcType="VARCHAR" property="insuredHomePhone"/>
		<result column="insured_work_phone" jdbcType="VARCHAR" property="insuredWorkPhone"/>

        <result column="status_update_time" jdbcType="BIGINT" property="statusUpdateTime"/>
        <result column="image_upload_status" jdbcType="TINYINT" property="imageUploadStatus"/>
        <result column="service_type" jdbcType="INTEGER" property="serviceType"/>
        <result column="special_instructions" jdbcType="VARCHAR" property="specialInstructions"/>
        <result column="special_instruction_comments" jdbcType="VARCHAR" property="specialInstructionComments"/>
        <result column="policy_effective_date" jdbcType="DATE" property="policyEffectiveDate"/>
        <result column="year_built" jdbcType="VARCHAR" property="yearBuilt"/>
        <result column="service_type_reason" jdbcType="VARCHAR" property="serviceTypeReason"/>
        <result column="gps_is_approximate" jdbcType="BOOLEAN" property="gpsIsApproximate"/>

        <result column="plnar_url" jdbcType="VARCHAR" property="plnarURL"/>
        <result column="plnar_status" jdbcType="INTEGER" property="plnarStatus"/>
        <result column="inspection_code" jdbcType="VARCHAR" property="inspectionCode"/>
        <result column="expiration_time" jdbcType="BIGINT" property="expirationTime"/>

        <result column="hover_job_id" jdbcType="BIGINT" property="hoverJobId"/>
        <result column="hover_job_status" jdbcType="INTEGER" property="hoverJobStatus"/>

        <result column="xactanalysis_id" jdbcType="VARCHAR" property="xactanalysisId" />
        <result column="test_flag" jdbcType="TINYINT" property="testFlag"/>
        <result column="mission_id" jdbcType="VARCHAR" property="missionId"/>
        <result column="unsigned_reason" jdbcType="VARCHAR" property="unsignedReason"/>

        <result column="operating_company" jdbcType="VARCHAR" property="operatingCompany" />
        <result column="initial_customer_contact_time" property="initialCustomerContactTime"/>
        <result column="customer_contacted_time" property="customerContactedAt"/>
    </resultMap>

    <resultMap id="SelectResultMap" type="com.bees360.entity.vo.ProjectCalSelectVo">
        <id column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="policy_number" jdbcType="BIGINT" property="policyNumber"/>
        <result column="claim_number" jdbcType="VARCHAR" property="claimNumber"/>
        <result column="claim_rcv" jdbcType="FLOAT" property="claimRcv"/>
        <result column="claim_acv" jdbcType="FLOAT" property="claimAcv"/>
        <result column="claim_type" jdbcType="INTEGER" property="claimType"/>
        <result column="created_time" jdbcType="BIGINT" property="createdTime"/>
        <result column="damage_event_time" jdbcType="BIGINT" property="damageEventTime"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="project_type" jdbcType="INTEGER" property="projectType"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>
        <result column="gps_location_longitude" jdbcType="DOUBLE" property="gpsLocationLongitude"/>
        <result column="gps_location_latitude" jdbcType="DOUBLE" property="gpsLocationLatitude"/>
        <result column="asset_owner_name" jdbcType="VARCHAR" property="assetOwnerName"/>
        <result column="asset_owner_phone" jdbcType="VARCHAR" property="assetOwnerPhone"/>
        <result column="asset_owner_email" jdbcType="VARCHAR" property="assetOwnerEmail"/>
        <result column="insurance_company" jdbcType="BIGINT" property="insuranceCompany"/>
        <result column="repair_company" jdbcType="BIGINT" property="repairCompany"/>
        <result column="material_provider_company" jdbcType="BIGINT" property="materialProviderCompany"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="claim_note" jdbcType="VARCHAR" property="claimNote"/>
        <result column="latest_status" jdbcType="INTEGER" property="latestStatus"/>
        <result column="project_status" jdbcType="INTEGER" property="projectStatus" />
        <result column="north" jdbcType="VARCHAR" property="north"/>
        <result column="inspection_types" jdbcType="BIGINT" property="inspectionTypes"/>
        <result column="inspection_time" jdbcType="BIGINT" property="inspectionTime"/>
        <result column="damage_severity" jdbcType="INTEGER" property="damageSeverity" />

        <result column="is_booking" jdbcType="INTEGER" property="booking"/>
        <result column="contacter_name" jdbcType="VARCHAR" property="contacterName"/>
        <result column="contacter_email" jdbcType="VARCHAR" property="contacterEmail"/>
        <result column="contacter_phone" jdbcType="VARCHAR" property="contacterPhone"/>
        <result column="roof_estimated_area_item" jdbcType="INTEGER" property="roofEstimatedAreaItem"/>
        <result column="report_service_option" jdbcType="INTEGER" property="reportServiceOption"/>
        <result column="need_pilot" jdbcType="INTEGER" property="needPilot"/>
        <result column="chimney" jdbcType="INTEGER" property="chimney"/>
        <result column="fly_zone_type" jdbcType="INTEGER" property="flyZoneType"/>
        <result column="images_archive_url" jdbcType="VARCHAR" property="imagesArchiveUrl"/>
        <result column="rotation_degree" jdbcType="DOUBLE" property="rotationDegree"/>

        <result column="inspection_number" jdbcType="VARCHAR" property="inspectionNumber"/>
        <result column="due_date" jdbcType="BIGINT" property="dueDate"/>
        <result column="customer" jdbcType="VARCHAR" property="customer"/>
        <result column="inspection_type" jdbcType="VARCHAR" property="inspectionType"/>
        <result column="agent" jdbcType="VARCHAR" property="agent"/>
        <result column="agent_email" jdbcType="VARCHAR" property="agentEmail"/>
        <result column="agent_contact_name" jdbcType="VARCHAR" property="agentContactName"/>
        <result column="agent_phone" jdbcType="VARCHAR" property="agentPhone"/>
        <result column="guideline" jdbcType="VARCHAR" property="guideline"/>
        <result column="insured_home_phone" jdbcType="VARCHAR" property="insuredHomePhone"/>
        <result column="insured_work_phone" jdbcType="VARCHAR" property="insuredWorkPhone"/>

        <result column="status_update_time" jdbcType="BIGINT" property="statusUpdateTime"/>
        <result column="image_upload_status" jdbcType="TINYINT" property="imageUploadStatus"/>
        <result column="service_type" jdbcType="INTEGER" property="serviceType"/>
        <result column="special_instructions" jdbcType="VARCHAR" property="specialInstructions"/>
        <result column="special_instruction_comments" jdbcType="VARCHAR" property="specialInstructionComments"/>
        <result column="policy_effective_date" jdbcType="DATE" property="policyEffectiveDate"/>
        <result column="year_built" jdbcType="VARCHAR" property="yearBuilt"/>
        <result column="days_old" jdbcType="INTEGER" property="daysOld"/>
        <result column="test_flag" jdbcType="TINYINT" property="testFlag"/>
        <result column="mission_id" jdbcType="VARCHAR" property="missionId"/>
        <result column="unsigned_reason" jdbcType="VARCHAR" property="unsignedReason"/>
    </resultMap>

	<resultMap id="projectAbstractMap" type="com.bees360.entity.vo.ProjectAbstractVo">
	    <id column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="project_type" jdbcType="INTEGER" property="projectType"/>
	    <result column="claim_rcv" jdbcType="FLOAT" property="claimRcv"/>
	    <result column="claim_acv" jdbcType="FLOAT" property="claimAcv"/>
	    <result column="claim_type" jdbcType="INTEGER" property="claimType"/>
	    <result column="asset_owner_name" jdbcType="VARCHAR" property="assetOwnerName"/>
	    <result column="asset_owner_phone" jdbcType="VARCHAR" property="assetOwnerPhone"/>
	    <result column="asset_owner_email" jdbcType="VARCHAR" property="assetOwnerEmail"/>
	    <result column="insurance_company" jdbcType="VARCHAR" property="insuranceCompany"/>
	    <result column="policy_number" jdbcType="VARCHAR" property="policyNumber" />
	</resultMap>

	<resultMap id="ProjectTinyMap" type="com.bees360.entity.vo.ProjectTinyVo">
	    <id column="project_id" jdbcType="BIGINT" property="projectId"/>
	    <result column="created_time" jdbcType="BIGINT" property="createdTime"/>
		<result column="due_date" jdbcType="BIGINT" property="dueDate"/>

		<result column="latest_status" jdbcType="INTEGER" property="latestStatus"/>
		<result column="project_status" jdbcType="INTEGER" property="projectStatus" />
        <result column="claim_number" jdbcType="VARCHAR" property="claimNumber"/>
		<result column="policy_number" jdbcType="VARCHAR" property="policyNumber"/>

		<result column="address" jdbcType="VARCHAR" property="address"/>
	    <result column="city" jdbcType="VARCHAR" property="city"/>
	    <result column="state" jdbcType="VARCHAR" property="state"/>
	    <result column="country" jdbcType="VARCHAR" property="country"/>
	    <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>

		<result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="project_type" jdbcType="INTEGER" property="projectType"/>
	    <result column="creator_email" jdbcType="VARCHAR" property="creatorEmail" />
	    <result column="creator_phone" jdbcType="VARCHAR" property="creatorPhone" />
	    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />

        <result column="is_booking" jdbcType="INTEGER" property="booking"/>
        <result column="need_pilot" jdbcType="INTEGER" property="needPilot"/>

	    <result column="inspection_types" jdbcType="BIGINT" property="inspectionTypes"/>
	    <result column="gps_location_longitude" jdbcType="DOUBLE" property="gpsLocationLongitude"/>
	    <result column="gps_location_latitude" jdbcType="DOUBLE" property="gpsLocationLatitude"/>
		<result column="fly_zone_type" jdbcType="INTEGER" property="flyZoneType"/>

		<result column="asset_owner_name" jdbcType="VARCHAR" property="assetOwnerName"/>
		<result column="asset_owner_phone" jdbcType="VARCHAR" property="assetOwnerPhone"/>
		<result column="asset_owner_email" jdbcType="VARCHAR" property="assetOwnerEmail"/>

        <result column="agent_contact_name" jdbcType="VARCHAR" property="agentName" />
        <result column="agent_phone" jdbcType="VARCHAR" property="agentPhone" />
        <result column="agent_email" jdbcType="VARCHAR" property="agentEmail" />

		<result column="repair_company" jdbcType="BIGINT" property="repairCompany"/>
        <result column="insurance_company" jdbcType="BIGINT" property="insuranceCompany"/>
        <result column="operating_company" jdbcType="VARCHAR" property="operatingCompany"/>

        <result column="inspection_time" jdbcType="BIGINT" property="inspectionTime"/>
        <result column="claim_type" jdbcType="INTEGER" property="claimType"/>
        <result column="days_old" jdbcType="INTEGER" property="daysOld"/>
        <result column="operationsManagerId" jdbcType="BIGINT" property="operationsManagerId"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>

        <result column="score" jdbcType="DECIMAL" property="riskScore"/>
        <result column="gps_is_approximate" jdbcType="BOOLEAN" property="gpsIsApproximate"/>
        <result column="initial_customer_contact_time" property="initialCustomerContactTime"/>
        <result column="customer_contacted_time" property="customerContactedTime"/>
        <result column="claim_note" jdbcType="VARCHAR" property="claimNote"/>
        <result column="scheduled_time" jdbcType="BIGINT" property="scheduledTime" />
        <result column="inspection_due_date" jdbcType="BIGINT" property="inspectionDueDate" />
        <result column="cat_number" jdbcType="VARCHAR" property="catNumber" />
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted" />
        <result column="project_state" jdbcType="VARCHAR" property="projectState" />
        <result column="project_state_reason" jdbcType="VARCHAR" property="projectStateChangeReason" />
        <result column="project_state_reason_id" jdbcType="VARCHAR" property="projectStateChangeReasonId" />
    </resultMap>

	<resultMap id="ProjectTinyMapForApp" type="com.bees360.entity.vo.ProjectTinyVoForApp">
	    <id column="project_id" jdbcType="BIGINT" property="projectId"/>
	    <result column="created_time" jdbcType="BIGINT" property="createdTime"/>
	    <result column="address" jdbcType="VARCHAR" property="address"/>
	    <result column="city" jdbcType="VARCHAR" property="city"/>
	    <result column="state" jdbcType="VARCHAR" property="state"/>
	    <result column="country" jdbcType="VARCHAR" property="country"/>
	    <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>
	    <result column="company_name" jdbcType="VARCHAR" property="insurCompanyName"/>
	    <result column="latest_status" jdbcType="INTEGER" property="latestStatus"/>
		<result column="project_status" jdbcType="INTEGER" property="projectStatus"/>

	    <result column="creator_email" jdbcType="VARCHAR" property="creatorEmail" />
        <result column="creator_phone" jdbcType="VARCHAR" property="creatorPhone" />
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />

        <result column="is_booking" jdbcType="INTEGER" property="booking"/>

	    <result column="gps_location_longitude" jdbcType="DOUBLE" property="gpsLocationLongitude"/>
	    <result column="gps_location_latitude" jdbcType="DOUBLE" property="gpsLocationLatitude"/>
	    <result column="status" jdbcType="INTEGER" property="checkStatusType"/>
	    <result column="pilotStatus" jdbcType="INTEGER" property="pilotCheckStatusType"/>
	    <result column="fly_zone_type" jdbcType="INTEGER" property="flyZoneType"/>

        <result column="inspection_time" jdbcType="BIGINT" property="inspectionTime"/>
        <result column="claim_type" jdbcType="INTEGER" property="claimType"/>
    </resultMap>

	<resultMap id="ProjectImageTinyMapForApp" type="ProjectImageTinyVoForApp">
	    <id column="image_id" jdbcType="VARCHAR" property="imageId"/>
	    <result column="project_id" jdbcType="BIGINT" property="projectId"/>
	    <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
	    <result column="file_name_middle_resolution" jdbcType="VARCHAR" property="fileNameMiddleResolution"/>
	    <result column="file_name_lower_resolution" jdbcType="VARCHAR" property="fileNameLowerResolution"/>
	    <result column="file_source_type" jdbcType="INTEGER" property="fileSourceType" />
	</resultMap>

	<resultMap id="baseCompanyMap" type="Company">
	    <id column="company_id" jdbcType="BIGINT" property="companyId"/>
	    <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
	    <result column="company_type" jdbcType="INTEGER" property="companyType"/>
	</resultMap>

	<resultMap id="IdNameDtoMap" type="com.bees360.entity.dto.IdNameDto">
        <id column="user_id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
    </resultMap>

    <resultMap id="ProjectMapPointMap" type="ProjectMapPoint">
    	<id column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="lng" jdbcType="DOUBLE" property="gpsLocationLongitude"/>
        <result column="lat" jdbcType="DOUBLE" property="gpsLocationLatitude"/>
    	<result column="inspection_time" jdbcType="BIGINT" property="inspectionTime"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="claim_type" jdbcType="INTEGER" property="claimType"/>
    </resultMap>

    <resultMap id="ProjectMapImageMap" type="ProjectMapImage">
        <id column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="inspection_time" jdbcType="BIGINT" property="inspectionTime"/>
        <result column="claim_type" jdbcType="INTEGER" property="claimType"/>
        <result column="shooting_time" jdbcType="BIGINT" property="shootingTime"/>
    </resultMap>

    <resultMap id="BeesPilotProjectMap" type="com.bees360.entity.dto.BeesPilotProjectDto">
        <id column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="created_time" jdbcType="BIGINT" property="createdTime"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>
        <result column="lat" jdbcType="DOUBLE" property="gpsLocationLatitude"/>
        <result column="lng" jdbcType="DOUBLE" property="gpsLocationLongitude"/>
        <result column="claim_type" jdbcType="INTEGER" property="claimType"/>
        <result column="fly_zone_type" jdbcType="INTEGER" property="flyZoneType"/>
        <result column="inspection_time" jdbcType="INTEGER" property="inspectionTime"/>
        <result column="latest_status" jdbcType="INTEGER" property="latestStatus"/>

        <result column="mobile_check_status" jdbcType="BOOLEAN" property="mobileCheckStatus" />
        <result column="drone_check_status" jdbcType="BOOLEAN" property="droneCheckStatus" />
        <result column="check_status" jdbcType="BOOLEAN" property="checkStatus" />
        <result column="quiz_completed" jdbcType="BOOLEAN" property="quizCompleted" />
        <result column="image_uploaded" jdbcType="BOOLEAN" property="imageUploaded" />
        <result column="address_verified" jdbcType="BOOLEAN" property="addressVerified" />
        <result column="image_uploaded_time" jdbcType="BIGINT" property="imageUploadedTime" />
        <result column="checkout_time" jdbcType="BIGINT" property="checkoutTime" />
        <result column="last_update_time" jdbcType="BIGINT" property="lastUpdateTime" />
        <result column="gps_is_approximate" jdbcType="BOOLEAN" property="gpsIsApproximate"/>
    </resultMap>

    <resultMap id="ProjectStatusServiceTypeMap" type="com.bees360.entity.dto.ProjectStatistics$ProjectStatusServiceType">
        <id column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="project_status" jdbcType="INTEGER" property="projectStatus"/>
        <result column="service_type" jdbcType="INTEGER" property="serviceType"/>
    </resultMap>

	<!-- <EMAIL>: consider whether it's necessary to get TEXT column? -->
	<sql id="projectColumns">
		project_id, policy_number, claim_number, cat_number, claim_acv, claim_rcv, claim_type, created_time,
		created_by,  project_type, address, city, state, country, zip_code, damage_event_time,
		ST_X(gps_location) as gps_location_longitude, ST_Y(gps_location) as gps_location_latitude, address_id,
		asset_owner_name, asset_owner_phone, asset_owner_email, insurance_company, repair_company, material_provider_company, company_id,
		description, claim_note, latest_status, project_status, north, inspection_types, inspection_time, damage_severity, is_booking,
        contacter_name, contacter_email, contacter_phone, roof_estimated_area_item, report_service_option,
        need_pilot, chimney, fly_zone_type, images_archive_url, rotation_degree,

        inspection_number, due_date,customer,inspection_type,
        agent,agent_email,agent_contact_name,agent_phone,guideline,
        insured_home_phone, insured_work_phone, status_update_time,
        image_upload_status, service_type, pay_status,

        special_instructions, special_instruction_comments,
        policy_effective_date, year_built,
        service_type_reason, gps_is_approximate, xactanalysis_id, test_flag,
        mission_id, unsigned_reason
    </sql>

    <sql id="projectColumnsWithPrefix">
        P.project_id, P.policy_number, P.claim_number, P.cat_number, P.claim_acv,P.claim_rcv, P.claim_type, P.created_time,
        P.created_by, P.project_type, P.address, P.city, P.state, P.country, P.zip_code, P.damage_event_time,
        ST_X(P.gps_location) as gps_location_longitude, ST_Y(P.gps_location) as gps_location_latitude, P.address_id,
        P.asset_owner_name, P.asset_owner_phone, P.asset_owner_email, P.insurance_company, P.repair_company, P.material_provider_company, P.company_id,
        P.description, P.claim_note, P.latest_status, P.project_status, north, P.inspection_types, P.inspection_time, P.damage_severity, P.is_booking,
        P.contacter_name, P.contacter_email, P.contacter_phone, P.roof_estimated_area_item, P.report_service_option,
        P.need_pilot, P.chimney, P.fly_zone_type, P.images_archive_url, P.rotation_degree,

        P.inspection_number, P.due_date,customer,P.inspection_type,
        P.agent,agent_email, P.agent_contact_name,P.agent_phone, P.guideline,
        P.insured_home_phone, P.insured_work_phone, P.status_update_time,
        P.image_upload_status, P.service_type, P.pay_status,

        P.special_instructions, P.special_instruction_comments,
        P.policy_effective_date, P.year_built,
        P.service_type_reason, P.gps_is_approximate, P.xactanalysis_id, P.test_flag, P.mission_id, P.unsigned_reason
    </sql>

	<sql id="projectAbstractColumns">
		P.project_id, P.project_type, P.claim_acv, P.claim_rcv, P.claim_type, P.policy_number,
		P.asset_owner_name, P.asset_owner_phone, P.asset_owner_email, iC.company_name as insurance_company
  	</sql>

  	<sql id="ProjectTinyColumns">
  		P.project_id, P.created_time, P.due_date, P.created_by, P.address, P.city, P.state, P.country, P.zip_code, P.fly_zone_type, P.project_type,
		P.claim_number, P.policy_number, ST_X(P.gps_location) as gps_location_longitude, ST_Y(P.gps_location) as gps_location_latitude, P.address_id,
  		<!-- status from EventHistory -->
  		P.latest_status, P.project_status, P.inspection_types, P.is_booking, P.need_pilot, P.asset_owner_name, P.asset_owner_phone,
  		P.asset_owner_email, P.repair_company, P.insurance_company, P.inspection_time, P.claim_type,P.service_type, P.pay_status, P.inspection_number, P.policy_effective_date,
  		P.gps_is_approximate, P.claim_note, P.cat_number, P.agent_contact_name, P.agent_email, P.agent_phone
	</sql>
  	<sql id="ProjectTinyColumnsForApp">
  		P.project_id, P.created_time, P.address, P.city, P.state, P.country, P.zip_code, P.fly_zone_type,
		P.gps_location_longitude, P.gps_location_latitude, P.address_id,
  		<!-- status from EventHistory -->
  		P.latest_status, P.project_status, P.company_name, P.is_booking, E.`status`, EP.`status` as pilotStatus,
  		U.email as creator_email, U.phone as creator_phone, concat(U.first_name, ' ', U.last_name) as creator_name,
        P.inspection_time, P.claim_type
  	</sql>
  	<sql id="projectColumnsForApp">
  		pro.project_id, pro.created_time, pro.address, pro.city, pro.state, pro.country, pro.zip_code,
		ST_X(pro.gps_location) as gps_location_longitude, ST_Y(pro.gps_location) as gps_location_latitude,
  		pro.latest_status, pro.project_status, pro.is_booking, pro.created_by, comp.company_name, pro.insurance_company,
		pro.repair_company, pro.claim_number, pro.fly_zone_type, pro.inspection_time, pro.claim_type, pro.status_update_time
  	</sql>
	<sql id="projectStatusAdjusterClaimCheckedIn">27</sql>
	<sql id="projectStatusAdjusterClaimCheckedOut">28</sql>
	<sql id="projectStatusAdjusterUnderwritingCheckedIn">41</sql>
	<sql id="projectStatusAdjusterUnderwritingCheckedOut">42</sql>
  	<sql id="getAdjusterCheckedStatus">
		left join (
			<!-- 获取最新的一个adjuster checkin/checkout 的状态 -->
			SELECT eha.project_id, eha.`status` FROM
				(SELECT max(eh.event_id) as event_id FROM EventHistory eh WHERE
					(eh.`status`=<include refid="projectStatusAdjusterClaimCheckedIn" />
						or eh.`status`=<include refid="projectStatusAdjusterClaimCheckedOut" />
						or eh.`status`=<include refid="projectStatusAdjusterUnderwritingCheckedIn" />
						or eh.`status`=<include refid="projectStatusAdjusterUnderwritingCheckedOut" />)
				GROUP BY eh.project_id) ehs
			LEFT JOIN EventHistory eha on eha.event_id=ehs.event_id
		) E on E.project_id = P.project_id
	</sql>
	<sql id="projectStatusPilotCheckedIn">23</sql>
	<sql id="projectStatusPilotCheckedOut">24</sql>
  	<sql id="getPilotCheckedStatus">
		left join (
			<!-- 获取最新的一个pilot checkin/checkout 的状态 -->
			SELECT ehp.project_id, ehp.`status` FROM
				(SELECT max(e.event_id) as event_id FROM EventHistory e WHERE
					(e.`status`=<include refid="projectStatusPilotCheckedIn" />
						or e.`status`=<include refid="projectStatusPilotCheckedOut" />)
				GROUP BY e.project_id) ehep
			LEFT JOIN EventHistory ehp on ehp.event_id=ehep.event_id
		) EP on EP.project_id = P.project_id
	</sql>
    <sql id="daysOldColumn">
        <include refid="daysOldSql" /> as days_old
    </sql>
    <sql id="daysOldAlisaColumn">
        <include refid="daysOldAliasSql" /> as days_old
    </sql>

    <sql id="daysOldSql">
        floor(GREATEST(0 , timestampdiff(
               second,
               GREATEST(
                       date_sub(from_unixtime(created_time / 1000), INTERVAL #{centerUtcOffset} HOUR),
                       if(policy_effective_date is null, 0, policy_effective_date)),
               date_sub(utc_timestamp(), INTERVAL #{centerUtcOffset} HOUR)
           )
           ) / 86400)
    </sql>

    <sql id="daysOldAliasSql">
        floor(GREATEST(0 , timestampdiff(
               second,
               GREATEST(
                       date_sub(from_unixtime(P.created_time / 1000), INTERVAL #{centerUtcOffset} HOUR),
                       if(P.policy_effective_date is null, 0, P.policy_effective_date)),
               date_sub(utc_timestamp(), INTERVAL #{centerUtcOffset} HOUR)
           )
           ) / 86400)
    </sql>

    <sql id="plnar_column">
        plnar_url, plnar_status
    </sql>

    <sql id="hover_column">
        hover_job_id,hover_job_status
    </sql>

    <sql id = "inspection_code">
        inspection_code, expiration_time
    </sql>

    <sql id = "operating_company_column">
        operating_company
    </sql>

    <sql id="project_timeline">
        PT.initial_customer_contact_time, PT.customer_contacted_time
    </sql>


    <sql id="project_state">
        PSTATE.project_state, PSTATE.project_state_reason, PSTATE.project_state_reason_id
    </sql>


    <sql id="full_project_column">
        <include refid="projectColumnsWithPrefix" />, <include refid="plnar_column"/>, <include refid="hover_column"/>
    </sql>

  	<select id="getById"  resultMap="baseResultMap">
  		select <include refid="projectColumnsWithPrefix" />, <include refid="plnar_column"/>, <include refid="inspection_code"/>,
        <include refid="hover_column"/>, <include refid="operating_company_column" />, <include refid="project_timeline"/>
        from Project P
        left join ProjectInspection PI on P.project_id = PI.project_id and PI.is_deleted = 0
        left join project_operating_company POC on P.project_id = POC.project_id
        left join ProjectTimeline PT on P.project_id = PT.project_id
        where P.project_id = #{projectId};
  	</select>

    <select id="getCalSelectVoById"  resultMap="SelectResultMap">
        select <include refid="projectColumns" />, <include refid="daysOldColumn" />  from Project where project_id = #{projectId};
    </select>

    <select id="listProjects"  resultMap="baseResultMap">
        select <include refid="projectColumnsWithPrefix" />,
        <include refid="plnar_column"/>,
        <include refid="project_timeline"/>
        from Project P left join ProjectTimeline PT on P.project_id = PT.project_id
        where P.project_id in
        <foreach collection="projectIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listProjectByXactanalysisId" resultMap="baseResultMap">
        select <include refid="full_project_column" /> from Project P where xactanalysis_id = #{xactanalysisId};
    </select>

	<select id="listByLatestStatus" resultMap="baseResultMap">
		select <include refid="projectColumns" /> from Project
		where latest_status = #{latestStatus}
			<if test="createdBy != null">
				and created_by = #{createdBy}
			</if>
			<if test="startTime != null">
				<![CDATA[
					and created_time >= #{startTime}
				]]>
			</if>
			<if test="endTime != null">
				<![CDATA[
					and created_time >= #{endTime}
				]]>
			</if>
			order by project_id desc;
	</select>

  	<select id="listProjectIdUserIn" resultType="long">
  	     select project_id
	        from Project P, (select distinct project_id from project_member where user_id = #{userId} and is_deleted = 0) as M
	        where P.project_id = M.project_id and P.latest_status != 0;
  	</select>

  	<select id="listByCompanyAndUserId"  resultMap="baseResultMap">
  		select <include refid="ProjectTinyColumns"/>
			from Project P
			where P.project_id in (select project_id from project_member where user_id = #{userId} and is_deleted = 0)
			<if test="companyId != null">
				and P.company_id = #{companyId}
			</if>
			and P.latest_status != 0;
  	</select>

    <select id="listByInsuranceCompanyAndStatus"  resultMap="baseResultMap">
        select <include refid="ProjectTinyColumns"/>
        from Project P
        where P.project_status = #{status}
        <if test="companyId != null">
            and P.insurance_company = #{companyId}
        </if>
        and P.created_time >= #{createdTime}
        and P.latest_status != 0;
    </select>

    <select id="listInTimeRange" resultMap="baseResultMap">
        select <include refid="projectColumns"/>
        from Project P
        where P.latest_status != 0
        <![CDATA[
                and created_time >= #{startTime}
            ]]>
        <![CDATA[
                and created_time <= #{endTime}
            ]]>
    </select>

  	<select id="listByUserId"  resultMap="baseResultMap">
  		select <include refid="ProjectTinyColumns"/>
			from Project P
			where P.project_id in (select project_id from project_member where user_id = #{userId} and is_deleted = 0)
			  and P.latest_status != 0;
  	</select>

	<select id="getAbstractById" resultMap="projectAbstractMap">
		select <include refid="projectAbstractColumns"/>
		from Project P,
			(select coalesce(company_name,'') as company_name from Company right join Project on (Company.company_id = Project.insurance_company) where Project.project_id = #{projectId}) as iC
<!-- 			(select coalesce(company_name,'') as company_name from Company right join Project on (Company.company_id = Project.repair_company) where Project.project_id = #{projectId}) as rC,  -->
<!-- 	        (select coalesce(company_name,'') as company_name from Company right join Project on (Company.company_id = Project.material_provider_company) where Project.project_id = #{projectId}) as mC -->
        where P.project_id = #{projectId};
	</select>

	<sql id="selectDistinctProjectIdUserIn">
		select distinct project_id from project_member where user_id = #{curUserId} and is_deleted = 0
	</sql>

	<!-- select except deleted project -->
	<select id="listTinyProjectByUserId" resultMap="ProjectTinyMap">
		select <include refid="ProjectTinyColumns"/>
		from Project P, (<include refid="selectDistinctProjectIdUserIn" />) as M
		where P.project_id = M.project_id and P.latest_status != 0
			limit #{startIndex}, #{pageSize};
	</select>

    <sql id="foreachSearchCompanyIdList">
        <foreach collection="searchCompanyIdList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </sql>

    <sql id="foreachInsuranceCompanyIdList">
        <foreach collection="insuranceCompanyIdList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </sql>

    <sql id="foreachProcessCompanyIdList">
        <foreach collection="processCompanyIdList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </sql>

	<sql id="filterProjectWithSearch">
		<if test="projectIds != null and projectIds.size > 0">
			and P.project_id in
			<foreach collection="projectIds" open="(" item="item" separator="," close=")">
				#{item}
			</foreach>
		</if>
        <if test="processingStatusList != null and processingStatusList.size > 0">
            and P.latest_status in
			<foreach collection="processingStatusList" item="item" index="index" open="(" close=")" separator=",">
         		#{item}
     		</foreach>
         </if>
		<if test="companyId != null and companyId > 0">
		    and P.company_id = #{companyId}
		</if>
		<!-- <if test="searchProjectId != null">
		    and P.project_id like #{searchProjectId}
		</if> -->
		<if test="searchProjectIdRegex != null">
		    and P.project_id like #{searchProjectIdRegex}
		</if>

        <if test="inspectionNumber != null">
            and P.inspection_number like concat('%', #{inspectionNumber}, '%')
        </if>

        <if test="inspectionNumbers != null and inspectionNumbers.size > 0">
            and
            <foreach item="inspectionNumber" collection="inspectionNumbers" open="(" separator=" OR " close=")">
                P.inspection_number LIKE concat('%', #{inspectionNumber}, '%')
            </foreach>
        </if>

		<if test="searchLatestStatus != null">
		    and P.latest_status = #{searchLatestStatus}
		</if>
		<if test="projectStatus != null">
			and P.project_status = #{projectStatus}
		</if>
		<if test="searchProjectType != null">
		    and P.project_type = #{searchProjectType}
		</if>
		<!-- <if test="searchAddress != null and searchAddress != ''">
			and match(P.address, P.city, P.state, P.zip_code, P.country) against(#{searchAddress})
		</if> -->
		<if test="searchAddressRegex != null and searchAddressRegex != ''">
			and concat(P.address, ',', P.city, ',', P.state, ' ', P.zip_code) like #{searchAddressRegex}
		</if>
		<if test="inspectionTypes != null">
		    and ( P.inspection_types &amp; #{inspectionTypes}) > 0
		</if>
		<if test="creator != null and creator != ''">
			and P.created_by = #{creator}
		</if>
		<if test="startTime != null and startTime > 0">
		    <![CDATA[
		        and P.created_time >= #{startTime}
		    ]]>
		</if>
		<if test="endTime != null and endTime > 0">
		    <![CDATA[
		        and P.created_time <= #{endTime}
		    ]]>
		</if>
		<if test="searchCompanyId != null">
		    and (P.insurance_company = #{searchCompanyId} or P.repair_company = #{searchCompanyId}
		        or P.material_provider_company = #{searchCompanyId}
		        or P.company_id = #{searchCompanyId})
		</if>
        <if test="searchCompanyIdList != null and searchCompanyIdList.size > 0">
            and (P.insurance_company in <include refid="foreachSearchCompanyIdList" />
            or P.repair_company in <include refid="foreachSearchCompanyIdList" />
            or P.material_provider_company in <include refid="foreachSearchCompanyIdList" />
            or P.company_id in <include refid="foreachSearchCompanyIdList" />)
        </if>
        <if test="insuranceCompanyIdList != null and insuranceCompanyIdList.size > 0">
            and P.insurance_company in
            <include refid="foreachInsuranceCompanyIdList"/>
        </if>
        <if test="processCompanyIdList != null and processCompanyIdList.size > 0">
            and P.repair_company in
            <include refid="foreachProcessCompanyIdList"/>
        </if>
        <if test="searchClaimNumber != null">
            and P.claim_number = #{searchClaimNumber}
        </if>
		<if test="flyZoneType != null">
			and P.fly_zone_type = #{flyZoneType}
		</if>
    <if test="flyZoneTypes != null and flyZoneTypes.size > 0">
      and P.fly_zone_type in
      <foreach collection="flyZoneTypes" open="(" item="item" separator="," close=")">
          #{item}
      </foreach>
    </if>
		<if test="policyNumber != null and policyNumber != ''">
			and P.policy_number = #{policyNumber}
		</if>
        <if test="policyNumbers != null and policyNumbers.size > 0">
            and policy_number in
            <foreach item="policyNumber" collection="policyNumbers" open="(" separator=", " close=")">
                #{policyNumber}
            </foreach>
        </if>
		<if test="insuredNameRegex != null and insuredNameRegex != ''">
			and P.asset_owner_name like #{insuredNameRegex}
		</if>
        <if test="insuredPhoneRegex != null">
            and (P.asset_owner_phone like #{insuredPhoneRegex} or
            P.insured_home_phone like #{insuredPhoneRegex} or
            P.insured_work_phone like #{insuredPhoneRegex})
        </if>
		<if test="addressRegex != null and addressRegex != ''">
			and P.address like #{addressRegex}
		</if>
		<if test="cityRegex != null and cityRegex != ''">
			and P.city like #{cityRegex}
		</if>
		<if test="state != null and state != ''">
            and (P.state = #{state} or P.state = #{fullNameState})
		</if>
		<if test="zipCode != null and zipCode != ''">
			and P.zip_code = #{zipCode}
		</if>
        <if test="states != null and states.size > 0">
            and (P.state in
            <foreach collection="states" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            or P.state in
            <foreach collection="fullNameStates" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
		<if test="dueDateStart != null">
			<![CDATA[
				and P.due_date >= #{dueDateStart}
			]]>
		</if>
		<if test="dueDateEnd != null">
			<![CDATA[
				and P.due_date <= #{dueDateEnd}
			]]>
		</if>
		<if test="projectStatusList != null and projectStatusList.size > 0">
			and P.project_status in
            <foreach collection="projectStatusList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
		</if>
        <if test="projectExcludeStatusList != null and projectExcludeStatusList.size > 0">
            and P.project_status not in
            <foreach collection="projectExcludeStatusList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="inspectionStartTime != null and inspectionStartTime > 0">
            <![CDATA[
		    	and P.inspection_time >= #{inspectionStartTime}
		  	]]>
        </if>
        <if test="inspectionEndTime != null and inspectionEndTime > 0">
            <![CDATA[
		      and P.inspection_time <= #{inspectionEndTime}
		    ]]>
        </if>
        <if test="scheduledTimeStart != null and scheduledTimeStart > 0">
            <![CDATA[
                and PIS.scheduled_time >= #{scheduledTimeStart}
            ]]>
        </if>
        <if test="scheduledTimeEnd != null and scheduledTimeEnd > 0">
            <![CDATA[
                and PIS.scheduled_time <= #{scheduledTimeEnd}
            ]]>
        </if>
        <if test="claimTypes != null and claimTypes.size() > 0">
        	and P.claim_type in
			<foreach collection="claimTypes" open="(" item="item" separator="," close=")">
				#{item}
			</foreach>
		</if>
        <if test="statusUpdateTimeProjectStatus != null and statusUpdateTimeStart != null">
			<![CDATA[
			and (P.project_status != #{statusUpdateTimeProjectStatus} or (P.project_status = #{statusUpdateTimeProjectStatus}
					and P.status_update_time > #{statusUpdateTimeStart}))
		    ]]>
		</if>
        <if test="serviceTypes != null and serviceTypes.size() > 0">
            and P.service_type in
            <foreach collection="serviceTypes" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="policyEffectiveStartDate != null">
            <![CDATA[
		    	and P.policy_effective_date >= #{policyEffectiveStartDate}
		  	]]>
        </if>
        <if test="policyEffectiveEndDate != null">
            <![CDATA[
		      and P.policy_effective_date <= #{policyEffectiveEndDate}
		    ]]>
        </if>
        <if test="daysOldStart != null">
            and <include refid="daysOldAliasSql" />
            <![CDATA[ >= #{daysOldStart} ]]>
        </if>
        <if test="daysOldEnd != null">
            and <include refid="daysOldAliasSql" />
            <![CDATA[ <= #{daysOldEnd} ]]>
        </if>
        <if test="startProjectId != null">
            <![CDATA[
                and P.project_id >= #{startProjectId}
            ]]>
        </if>
        <if test="projectTypes != null and projectTypes.size() > 0">
            and P.project_type in
            <foreach collection="projectTypes" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="operatingCompanyList != null and operatingCompanyList.size() > 0">
            and POC.operating_company in
            <foreach collection="operatingCompanyList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
	</sql>

	<sql id="filterProjectWithSearchSortAndLimit">
		<choose>
			<when test="sortKey != null and sortKey != ''">
		         <choose>
		                <when test="sortKey == 'project_id'">
		                    <if test="sortOrder == 'asc'">
		                        order by P.project_id asc
		                    </if>
		                    <if test="sortOrder == 'desc'">
		                        order by P.project_id desc
		                    </if>
		                </when>
		                <when test="sortKey == 'address'">
		                    <if test="sortOrder == 'asc'">
		                        order by P.address asc,
		                        P.city asc,
		                        P.state asc,
		                        P.zip_code asc,
		                        P.country asc
		                    </if>
		                    <if test="sortOrder == 'desc'">
		                        order by P.address desc,
		                        P.city desc,
		                        P.state desc,
		                        P.zip_code desc,
		                        P.country desc
		                    </if>
		                </when>
		                <when test="sortKey == 'claim_number'">
		                    <if test="sortOrder == 'asc'">
		                        order by P.claim_number asc
		                    </if>
		                    <if test="sortOrder == 'desc'">
		                        order by P.claim_number desc
		                    </if>
		                </when>
		                <when test="sortKey == 'latest_status'">
		                    <if test="sortOrder == 'asc'">
		                        order by P.latest_status asc
		                    </if>
		                    <if test="sortOrder == 'desc'">
		                        order by P.latest_status desc
		                    </if>
		                </when>
		                <when test="sortKey == 'created_time'">
		                    <if test="sortOrder == 'asc'">
		                        order by P.created_time asc
		                    </if>
		                    <if test="sortOrder == 'desc'">
		                        order by P.created_time desc
		                    </if>
		                </when>
                         <when test="sortKey == 'days_old'">
                             <if test="sortOrder == 'asc'">
                                 order by days_old asc
                             </if>
                             <if test="sortOrder == 'desc'">
                                 order by days_old desc
                             </if>
                         </when>
                        <when test="sortKey == 'score'">
                            <if test="sortOrder == 'asc'">
                                order by score asc
                            </if>
                            <if test="sortOrder == 'desc'">
                                order by score desc
                            </if>
                        </when>
                        <when test="sortKey == 'project_status'">
                            <if test="sortOrder == 'asc'">
                                order by status_update_time asc
                            </if>
                            <if test="sortOrder == 'desc'">
                                order by status_update_time desc
                            </if>
                        </when>
                        <when test="sortKey == 'policy_effective_date'">
                            <if test="sortOrder == 'asc'">
                                order by isnull(P.policy_effective_date), P.policy_effective_date asc
                            </if>
                            <if test="sortOrder == 'desc'">
                                order by P.policy_effective_date desc
                            </if>
                        </when>
		                <otherwise>
		                    order by P.project_id desc
		                 </otherwise>
		         </choose>
		         <if test="sortKey != 'created_time'">
		            ,P.created_time desc
		         </if>
		     </when>
		     <otherwise>
		        order by P.project_id desc
		     </otherwise>
		</choose>
		limit #{startIndex}, #{pageSize}
	</sql>

	<sql id="memberTimeSql">
		<if test="memberRole != null || memberUser != null">
			<include refid="projectMemberSearch" />
			GROUP BY P.project_id
		</if>
	</sql>

	<sql id="projectMemberSearch">
		<if test="memberRole != null or memberUser != null or (memberUserList != null and memberUserList.size() > 0)">
			and P.project_id = M.project_id and M.is_deleted = 0
			<if test="memberRole != null">
				and M.role = #{memberRole}
			</if>
			<if test="memberUser != null">
				and M.user_id = #{memberUser}
			</if>
            <if test="memberUserList != null and memberUserList.size() > 0">
                and M.user_id in
                <foreach collection="memberUserList" open="(" separator="," item="userId" close=")">
                    #{userId}
                </foreach>
			</if>
			<if
                test="assignStartTime != null">
				and M.created_time >= #{assignStartTime}
			</if>

			<if test="assignEndTime != null">
				<![CDATA[
		    	and M.created_time <= #{assignEndTime}
		  	]]>
			</if>
		</if>
        <if test="creatorList != null and creatorList.size() > 0">
            and P.project_id = M2.project_id and M2.role = -1 and M2.user_id in
            <foreach collection="creatorList" item="creator" open="(" separator="," close=")">
                #{creator}
            </foreach>
        </if>
        <if test="operationsManagerList != null and operationsManagerList.size() > 0">
            and P.project_id in (
            select project_id from project_member where role = 10 and is_deleted = 0 and user_id in
            <foreach collection="operationsManagerList" item="om" open="(" separator="," close=")">
                #{om}
            </foreach>
            )
        </if>
    </sql>

	<sql id="projectCurUserJoin">
		<if test="curUserId != null or managedBy != null">
			and (
			<if test="curUserId != null">
				(P.project_id in (select project_id from project_member where user_id = #{curUserId} and is_deleted = 0))
			</if>
			<if test="curUserId != null and managedBy != null">
				or
			</if>
			<if test="managedBy != null">
                (P.repair_company = #{managedBy} or P.insurance_company =  #{managedBy})
			</if>
			)
		</if>
	</sql>

    <sql id="filterProjectCurUserJoinOrManagedBy">
        <if test="curUserId != null or managedBy != null">
            and (
            <if test="curUserId != null">
                (JP.project_id is not null)
            </if>
            <if test="curUserId != null and managedBy != null">
                or
            </if>
            <if test="managedBy != null">
                (P.repair_company = #{managedBy} or P.insurance_company =  #{managedBy})
            </if>
            )
        </if>
    </sql>

    <sql id="filterBeesPilotBatchSearch">
        <if test="batchNo != null">
            and P.project_id = BI.project_id
            and BI.batch_no = #{batchNo}
            and BI.is_deleted = 0
        </if>
    </sql>

    <sql id="projectOverdueSearch">
        <if test="overdueExcludeTagIds != null and overdueExcludeTagIds.size() > 0">
            and (
                P.insurance_company != #{overdueInsuranceCompany}
                OR (P.insurance_company = #{overdueInsuranceCompany} and P.project_id not in
                    (select project_id from ProjectLabelBind where label_id in
                        <foreach collection="overdueExcludeTagIds" open="(" item="item" separator="," close=")">
                            #{item}
                        </foreach>
                    )
                )
            )
        </if>
    </sql>

    <sql id="projectLabelSearch">
        <choose>
            <when test="labelIds != null and labelIds.size() > 0 and naLabel != null">
                and (
                    ( P.project_id = LB.project_id and LB.label_id in
                    <foreach collection="labelIds" item="labelId" separator="," open="(" close=")">
                        #{labelId}
                    </foreach>
                    )
                    OR (P.project_id not in (select project_id from ProjectLabelBind))
                )
            </when>
            <otherwise>
                <choose>
                    <when test="labelIds != null and labelIds.size() > 0">
                        and P.project_id = LB.project_id and LB.label_id in
                        <foreach collection="labelIds" item="labelId" separator="," open="(" close=")">
                            #{labelId}
                        </foreach>
                    </when>
                    <when test="naLabel != null">
                        and P.project_id not in (select project_id from ProjectLabelBind)
                    </when>
                </choose>
            </otherwise>
        </choose>
    </sql>

    <sql id="enableSearchTag">
        <if test="searchTag != null and !searchTag.isEmpty()">
            and test_flag = 0 and insurance_company is not null
        </if>
        <if test="searchTag != null and searchTag.contains('RUSH_FOLDER')">
             and (
                P.project_status = 60 or
                (
                    P.project_status in (30, 40, 50, 70)
                    and <include refid="daysOldAliasSql" />
                    <![CDATA[ >= 5 ]]>
                )
            )
        </if>
        <if test="searchTag != null and searchTag.contains('NOT_CONTACTED')">
            and P.project_status = 10
        </if>
        <if test="searchTag != null and searchTag.contains('UNASSIGNED')">
            and P.project_status in (10, 30, 40)
        </if>
        <if test="searchTag != null and searchTag.contains('IMAGE_NOT_UPLOADED')">
            and P.project_status in (50, 60, 70, 79) and P.project_id not in
            (
                select ps.project_id from ProjectStatus ps where status = 80 and is_deleted = 0 and ps.project_id in
                (select p2.project_id from Project p2 where p2.project_status = 79 and p2.test_flag = 0 and
                p2.insurance_company is not null and p2.latest_status != 0)
            )
        </if>
        <if test="searchTag != null and searchTag.contains('IN_PROCESS')">
            AND P.project_status = 80 or P.project_id in
            (
                select ps.project_id from ProjectStatus ps where status = 80 and is_deleted = 0 and ps.project_id in
                (select p3.project_id from Project p3 where p3.project_status = 79 and p3.test_flag = 0 and
                p3.insurance_company is not null and p3.latest_status != 0)
            )
        </if>
    </sql>

    <sql id="enableProjectIdCompare">
        <if test="projectIdStart != null">
            and P.project_id <![CDATA[ >= #{projectIdStart} ]]>
        </if>
    </sql>

    <sql id="enableSearchTestCase">
        <if test="testFlag != null">
            and P.test_flag = #{testFlag}
        </if>
    </sql>

    <sql id="filterProjectWithPaidSearch">
        <if test="pay_status != null">
            and P.pay_status = #{pay_status}
        </if>
    </sql>

    <sql id="projectScoreJoin">
        LEFT JOIN ProjectScore S on P.project_id = S.project_id
    </sql>

    <sql id="projectScoreColumn">
        IFNULL(S.score, 0) as score
    </sql>

    <sql id="projectCurUserMemberJoin">
        <if test="curUserId != null">
            LEFT JOIN (select distinct CM.project_id from project_member CM where CM.user_id = #{curUserId} and CM.is_deleted = 0) as JP on JP.project_id = P.project_id
        </if>
    </sql>

    <sql id="projectAddressAirspaceJoin">
        <if test="(airspaceStatuses != null and airspaceStatuses.size() > 0) or
        (isAirspaceStatusesContainsNull != null and isAirspaceStatusesContainsNull == true)">
            LEFT JOIN address_airspace AA on P.address_id = AA.address_id
        </if>
    </sql>

    <sql id="projectProjectAirspaceJoin">
        <if test="(airspaceStatuses != null and airspaceStatuses.size() > 0) or
        (isAirspaceStatusesContainsNull != null and isAirspaceStatusesContainsNull == true)">
            LEFT JOIN project_airspace PA on P.project_id = PA.project_id
        </if>
    </sql>

    <sql id="projectSearchOptionForListTinyProjectWithSearch">
        <include refid="filterProjectCurUserJoinOrManagedBy" />
        <include refid="projectMemberSearch"/>
        <include refid="filterProjectWithSearch" />
        <include refid="filterBeesPilotBatchSearch" />
        <include refid="projectOverdueSearch"/>
        <include refid="enableSearchTag"/>
        <include refid="enableProjectIdCompare"/>
        <include refid="enableSearchTestCase"/>
        <include refid="filterProjectWithPaidSearch" />
    </sql>

	<select id="listTinyProjectWithSearch" resultMap="ProjectTinyMap">
        select <include refid="ProjectTinyColumns"/>,
            <include refid="daysOldAlisaColumn"/>,
            <include refid="projectScoreColumn"/>,
            <include refid="project_timeline"/>,
            <include refid="project_state"/>, PIS.scheduled_time, PIS.due_date as inspection_due_date,
            P.latest_status = 0 as is_deleted, POC.operating_company
        from Project P
        left join ProjectTimeline PT on P.project_id = PT.project_id
        left join project_inspection_schedule PIS on P.project_id = PIS.project_id
        left join project_state PSTATE on P.project_id = PSTATE.project_id
        left join project_operating_company POC on P.project_id = POC.project_id
        <include refid="projectCurUserMemberJoin"/>
        <include refid="projectScoreJoin"/>
        <include refid="projectProjectAirspaceJoin"/>
        <if test="memberRole != null or memberUser != null or (memberUserList != null and memberUserList.size() > 0)">
			, project_member M
		</if>
        <if test="creatorList != null and creatorList.size() > 0">
            , project_member M2
        </if>
        <if test="batchNo != null">
            , BeesPilotBatchItem BI
        </if>
        <if test="labelIds != null and labelIds.size() > 0">
            , ProjectLabelBind LB
        </if>
        <where>
            <if test="deletedInclusion == false">
                P.latest_status != 0
            </if>
            <if test="deletedInclusion == true and isDeleted == true">
                and P.latest_status = 0
            </if>
            <if test="excludeStatus != null">
                and P.latest_status != #{excludeStatus}
            </if>
            <include refid="projectSearchOptionForListTinyProjectWithSearch"/>
            <include refid="projectLabelSearch"/>
            <include refid="projectProjectAirspaceSearch"/>
        </where>
        <include refid="filterProjectWithSearchSortAndLimit" />
    </select>

    <select id="listProjectWithSearch" resultMap="baseResultMap">
        select <include refid="projectColumnsWithPrefix"/>, <include refid="project_timeline"/>
        from Project P left join ProjectTimeline PT on P.project_id = PT.project_id
        <where>
            P.latest_status != 0
            <if test="latestStatus != null">
                and latest_status = #{latestStatus}
            </if>
            <if test="lastCreateTime != null">
                and created_time > #{lastCreateTime}
            </if>
        </where>
        limit #{pageIndex}, #{pageSize}
    </select>


	<select id="countTinyProjectWithSearch" resultType="Integer">
		select count(*)
		from Project P
        left join project_inspection_schedule PIS on P.project_id = PIS.project_id
        left join project_operating_company POC on P.project_id = POC.project_id
        <include refid="projectCurUserMemberJoin" />
        <if test="memberRole != null or memberUser != null or (memberUserList != null and memberUserList.size() > 0)">
			, project_member M
		</if>
        <if test="creatorList != null and creatorList.size() > 0">
            , project_member M2
        </if>
        <if test="batchNo != null">
            , BeesPilotBatchItem BI
        </if>
        <if test="labelIds != null and labelIds.size() > 0">
            , ProjectLabelBind LB
        </if>
		<where>
			P.latest_status != 0
            <if test="excludeStatus != null">
                and P.latest_status != #{excludeStatus}
            </if>
            <if test="lastCreateTime != null">
                and P.created_time > #{lastCreateTime}
            </if>
			<include refid="projectSearchOptionForListTinyProjectWithSearch"/>
            <include refid="projectLabelSearch"/>
        </where>
    </select>

	<select id="listProjectIdWithSearch" resultType="long">
        select P.project_id
        from Project P
        left join project_inspection_schedule PIS on P.project_id = PIS.project_id
        left join project_operating_company POC on P.project_id = POC.project_id
        <include refid="projectCurUserMemberJoin" />
        <include refid="projectProjectAirspaceJoin" />
        <if test="memberRole != null or memberUser != null or (memberUserList != null and memberUserList.size() > 0)">
            , project_member M
        </if>
        <if test="creatorList != null and creatorList.size() > 0">
            , project_member M2
        </if>
        <if test="batchNo != null">
            , BeesPilotBatchItem BI
        </if>
        <if test="labelIds != null and labelIds.size() > 0">
            , ProjectLabelBind LB
        </if>
        <where>
            <if test="deletedInclusion == false or (deletedInclusion == true and isDeleted == false)">
                P.latest_status != 0
            </if>
            <if test="deletedInclusion == true and isDeleted == true">
                and P.latest_status = 0
            </if>
            <if test="excludeStatus != null">
                and P.latest_status != #{excludeStatus}
            </if>
            <if test="lastCreateTime != null">
                and P.created_time > #{lastCreateTime}
            </if>
            <include refid="projectSearchOptionForListTinyProjectWithSearch"/>
            <include refid="projectLabelSearch"/>
            <include refid="projectProjectAirspaceSearch"/>
		</where>
	</select>

    <sql id="projectAddressAirspaceSearch">
        <choose>
            <!-- 条件List不为空，且含NULL-->
            <when test="(airspaceStatuses != null and airspaceStatuses.size() > 0)
            and (isAirspaceStatusesContainsNull != null and isAirspaceStatusesContainsNull == true)">
                and (AA.status in
                <foreach collection="airspaceStatuses" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
                or AA.status is null)
            </when>
            <!-- 条件List不为空，且不含NULL-->
            <when test="airspaceStatuses != null and airspaceStatuses.size() > 0">
                and AA.status in
                <foreach collection="airspaceStatuses" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <!-- 条件List为空，但含NULL-->
            <when test="isAirspaceStatusesContainsNull != null and isAirspaceStatusesContainsNull == true">
                and AA.status is null
            </when>
        </choose>
    </sql>

    <sql id="projectProjectAirspaceSearch">
        <choose>
            <!-- 条件List不为空，且含NULL-->
            <when test="(airspaceStatuses != null and airspaceStatuses.size() > 0)
            and (isAirspaceStatusesContainsNull != null and isAirspaceStatusesContainsNull == true)">
                and (PA.status in
                <foreach collection="airspaceStatuses" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
                or PA.status is null)
            </when>
            <!-- 条件List不为空，且不含NULL-->
            <when test="airspaceStatuses != null and airspaceStatuses.size() > 0">
                and PA.status in
                <foreach collection="airspaceStatuses" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <!-- 条件List为空，但含NULL-->
            <when test="isAirspaceStatusesContainsNull != null and isAirspaceStatusesContainsNull == true">
                and PA.status is null
            </when>
        </choose>
    </sql>

	<sql id="filterProjectSearchOptionForApp">
		<if test="companyId != null and companyId > 0">
	 		and P.company_id = #{companyId}
		</if>
		<if test="projectIds != null and projectIds.size > 0">
			and P.project_id in
			<foreach collection="projectIds" open="(" item="item" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="projectId != null">
		   and P.project_id = #{projectId}
		</if>
		<if test="searchProjectId != null">
		   and P.project_id like concat('%', #{searchProjectId}, '%')
		</if>
		<if test="statuses != null and statuses.length > 0">
			and P.latest_status in
		    <foreach collection="statuses" index="index" item="item" open="(" separator="," close=")">
		        #{item}
		    </foreach>
		</if>
		<if test="projectStatuses != null and projectStatuses.length > 0">
			and P.project_status in
			<foreach collection="projectStatuses" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="insuranceCompanyIds != null and insuranceCompanyIds.length>0">
			and P.insurance_company in
		  	<foreach collection="insuranceCompanyIds" index="index" item="item" open="(" separator="," close=")">
		   		#{item}
		    </foreach>
		</if>
		<if test="zipCodes != null and zipCodes.length>0">
			and P.zip_code in
		  	<foreach collection="zipCodes" index="index" item="item" open="(" separator="," close=")">
		        #{item}
		    </foreach>
		</if>
		<if test="states != null and states.length>0">
		    and P.state in
		    <foreach collection="states" index="index" item="item" open="(" separator="," close=")">
		        #{item}
		    </foreach>
		</if>
		<if test="startTime != null and startTime > 0">
			<![CDATA[
		    	and P.created_time >= #{startTime}
		  	]]>
		</if>
		<if test="endTime != null and endTime > 0">
		    <![CDATA[
		      and P.created_time <= #{endTime}
		    ]]>
		</if>
		<if test="address != null and address != ''">
		    <!--  and match(P.address, P.city, P.state, P.zip_code, P.country) against(#{address}) -->
			and concat(P.address, ',', P.city, ',', P.state, ' ', P.zip_code) like #{addressRegex}
		</if>
		<if test="flyZoneType != null">
			and P.fly_zone_type = #{flyZoneType}
		</if>
        <if test="inspectionStartTime != null and inspectionStartTime > 0">
            <![CDATA[
		    	and P.inspection_time >= #{inspectionStartTime}
		  	]]>
        </if>
        <if test="inspectionEndTime != null and inspectionEndTime > 0">
            <![CDATA[
		      and P.inspection_time <= #{inspectionEndTime}
		    ]]>
        </if>
		<if test="claimTypes != null and claimTypes.size() > 0">
			and P.claim_type in
			<foreach collection="claimTypes" open="(" item="item" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="statusUpdateTimeProjectStatus != null and statusUpdateTimeStart != null">
			<![CDATA[
			and (P.project_status != #{statusUpdateTimeProjectStatus} or (P.project_status = #{statusUpdateTimeProjectStatus}
					and P.status_update_time > #{statusUpdateTimeStart}))
		    ]]>
		</if>
	</sql>

	<sql id="filterProjectSearchOptionSortAndLimitForApp">
		<choose>
			<when test="sortKey != null and sortKey != ''">
				<choose>
				    <when test="sortKey == 'ADDR'">
				    	order by P.address ${sortOrder}
			        </when>
			        <when test="sortKey == 'PID'">
                    	order by P.project_id ${sortOrder}
                    </when>
                    <when test="sortKey == 'CN'">
				    	order by P.claim_number ${sortOrder}
			        </when>
			        <when test="sortKey == 'CT'">
                    	order by P.created_time ${sortOrder}
                    </when>
			        <otherwise>
			             order by project_id desc
			        </otherwise>
			    </choose>
		    </when>
            <otherwise>
            	order by project_id desc
            </otherwise>
		</choose>
		<if test="startIndex != null and pageSize != null">
			limit #{startIndex}, #{pageSize}
		</if>
	</sql>

	<select id="listTinyProjectForApp" resultMap="ProjectTinyMapForApp">
		select <include refid="ProjectTinyColumnsForApp"/>
		from User U,
		(select <include refid="projectColumnsForApp"/> from Project pro left join Company comp on comp.company_id = pro.insurance_company) as P
		<include refid="getAdjusterCheckedStatus" />
		<include refid="getPilotCheckedStatus" />
		<if test="memberRole != null or memberUser != null">
			, project_member M
		</if>
		<where>
			P.created_by = U.user_id and P.latest_status != 0
			<include refid="projectCurUserJoin" />
			<include refid="filterProjectSearchOptionForApp" />
			<include refid="projectMemberSearch" />
		</where>
		<include refid="filterProjectSearchOptionSortAndLimitForApp" />;
	</select>

	<select id="countTinyProjectForApp" resultType="Integer">
		select count(*) from Project P
		<if test="memberRole != null or memberUser != null">
			, project_member M
		</if>
        <if test="creatorList != null and creatorList.size() > 0">
            , project_member M2
        </if>
		<where>
			P.latest_status != 0
			<include refid="projectCurUserJoin" />
			<include refid="filterProjectSearchOptionForApp" />
			<include refid="projectMemberSearch" />
		</where>
	</select>

	<select id="listProjectsForAppByIds" resultMap="ProjectTinyMapForApp">
		select <include refid="ProjectTinyColumnsForApp"/>
		from User U, (select <include refid="projectColumnsForApp"/> from Project pro left join Company comp on comp.company_id = pro.insurance_company) as P
			<include refid="getAdjusterCheckedStatus" />
			<include refid="getPilotCheckedStatus" />
		<where>
		   	P.created_by = U.user_id and P.latest_status != 0
		   	and P.project_id in
		   	<foreach collection="projectIds" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			<include refid="projectCurUserJoin" />
		</where>
	</select>

	<select id="listTinyProjectImageVoForApp" resultMap="ProjectImageTinyMapForApp">
	    select image_id, project_id, file_name, file_name_middle_resolution, file_name_lower_resolution, file_source_type
	    from ProjectImage
	    where is_deleted=0
	    <if test="fileSourceTypes != null and fileSourceTypes.size() > 0">
	       and file_source_type in
	       <foreach collection="fileSourceTypes" item="item" open="(" separator="," close=")">
	           #{item}
	       </foreach>
	    </if>
	    <if test="projectIds != null and projectIds.size() > 0">
		    and project_id in
		    <foreach collection="projectIds" index="index" item="item" open="(" separator="," close=")">
	  			#{item}
			</foreach>
		</if>
		<![CDATA[order by project_id desc, image_type is null asc, direction is null asc, image_type<>2 asc,
				direction<>1 asc, direction<>2 asc, file_source_type asc, original_file_name asc, upload_time desc]]>
	</select>

	<select id="listComaniesInProjects" resultMap="baseCompanyMap">
	   select distinct C.company_id, C.company_name, C.company_type
            from Company C, Project P
            <where>
	            <choose>
	               <when test="companyType == null">
	                   (C.company_id=P.insurance_company or C.company_id = P.repair_company or C.company_id = P.material_provider_company or C.company_id = P.company_id)
	               </when>
	               <when test="companyType == 0">
	                   C.company_id=P.insurance_company
	               </when>
	               <when test="companyType == 1">
	                   C.company_id = P.repair_company
	               </when>
	               <when test="companyType == 2">
	                   C.company_id = P.material_provider_company
	               </when>
	               <when test="companyType == 4">
	                   C.company_id = P.company_id
	               </when>
	               <otherwise>
	                   1 = 2
	               </otherwise>
	            </choose>
				<include refid="projectCurUserJoin" />
			</where>
            order by C.company_name;
	</select>

    <select id="getStateById" resultType="java.lang.String">
       select state from Project where project_id = #{projectId};
    </select>

    <select id="exist" resultType="java.lang.Boolean">
        select count(*) > 0 from Project where project_id = #{projectId} and latest_status != 0;
    </select>

	<select id="listAllZeroLatLngsAddress" resultType="Map">
	    select project_id as projectId,address,city, state, country, zip_code as zipCode,inspection_types as inspectionTypes from Project
	    where latest_status != 0 and ST_X(gps_location) = 0 and ST_Y(gps_location) = 0;
	</select>

	<select id="listProjectIdAndCreatedTime" resultType="Map">
		select P.project_id as projectId, P.created_time as createdTime
		from Project P
		where P.latest_status != 0
		   and P.project_id in (select project_id from project_member where user_id = #{userId} and is_deleted = 0)
		   <if test="startTime != null">
               <![CDATA[
                   and created_time >= #{startTime}
               ]]>
           </if>
           <if test="endTime != null">
               <![CDATA[
                   and created_time <= #{endTime}
               ]]>
           </if>
           order by P.project_id
	</select>

    <sql id="projectMapImageColumn">
		P.project_id, P.inspection_time,
       	P.claim_type, PIG.shooting_time
	</sql>

	<select id="getImagesArchiveUrl" resultType="String">
		select images_archive_url from Project where project_id = #{projectId};
	</select>

	<select id="listProjectId" resultType="java.lang.Long">
		select project_id projectId from Project P
		<where>
            <if test="creatorId != null">
                P.created_by = #{creatorId}
            </if>
			<if test="repairCompany != null">
                and P.repair_company = #{repairCompany}
			</if>
			<if test="insuranceCompany != null">
				and P.insurance_company = #{insuranceCompany}
			</if>
			<if test="materialProviderCompany != null">
				and P.material_provider_company = #{materialProviderCompany}
			</if>
			<if test="projectStatuses != null and projectStatuses.size() > 0">
				and P.project_status in
                <foreach collection="projectStatuses" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
			</if>
			<if test="projectStatusTimeStart != null">
				<![CDATA[
					and P.status_update_time >= #{projectStatusTimeStart}
		        ]]>
			</if>
			<if test="projectStatusTimeEnd != null">
				<![CDATA[
					and P.status_update_time <= #{projectStatusTimeEnd}
		        ]]>
			</if>
		    <if test="projectIds != null and projectIds.size() > 0">
		        and project_id in
                <foreach collection="projectIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="inspectionStartTime != null and inspectionStartTime > 0">
                <![CDATA[
		    	and P.inspection_time > #{inspectionStartTime}
		  	]]>
            </if>
            <if test="inspectionEndTime != null and inspectionEndTime > 0">
                <![CDATA[
		      and P.inspection_time <= #{inspectionEndTime}
		    ]]>
            </if>
            <if test="exclusiveStatuses != null and exclusiveStatuses.size() > 0">
                and P.project_status not in
                <foreach collection="exclusiveStatuses" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="projectIdStart != null">
                <![CDATA[
				    and P.project_id >= #{projectIdStart}
		        ]]>
            </if>
            <if test="projectIdEnd != null">
                <![CDATA[
					and P.project_id <= #{projectIdEnd}
		        ]]>
            </if>
            <if test="isTestCase != null">
                and test_flag = #{isTestCase}
            </if>
            <if test="filterInsuredCompanyNull">
                and insurance_company is not null
            </if>
            <if test="projectServiceType != null and !projectServiceType.isEmpty()">
                and service_type in
                <foreach collection="projectServiceType" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="searchAssetOwnerWithoutPhone">
                and asset_owner_phone = '' and (insured_home_phone = '' or insured_home_phone is null)
                and (insured_work_phone = '' or insured_work_phone is null) and asset_owner_name != ''
            </if>
        </where>
	</select>

    <select id="listProjectStatusView" resultMap="ProjectStatusServiceTypeMap">
        select project_id, project_status, inspection_time, service_type from Project
        <where>
            test_flag = !#{filterTest} and latest_status != 0
            <if test="filterInsuredNull">
                and insurance_company is not null
            </if>
            <if test="filterStatus != null and !filterStatus.isEmpty()">
                and project_status not in
                <foreach collection="filterStatus" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listProjectForBeesPilot" resultMap="BeesPilotProjectMap">
        select P.project_id, P.created_time,P.address, P.city, P.state, P.country, P.zip_code,
               ST_X(P.gps_location) as lng, ST_Y(P.gps_location) as lat, P.claim_type, P.fly_zone_type,
               P.inspection_time,insurance_company, service_type, agent, agent_phone, asset_owner_name,
        P.asset_owner_email, P.asset_owner_phone, P.status_update_time,P.latest_status,
        P.special_instructions, P.special_instruction_comments, P.claim_note, P.gps_is_approximate
        BPS.mobile_check_status, BPS.drone_check_status, BPS.check_status,
        BPS.quiz_completed, BPS.image_uploaded, BPS.address_verified,
        BPS.image_uploaded_time, BPS.checkout_time, BPS.last_update_time
        from Project P
        left join project_member M on P.project_id = M.project_id
        left join BeesPilotStatus BPS on M.project_id = BPS.project_id
        <where>
            P.latest_status != 0
            and user_id = #{userId}
            and is_deleted = 0 and M.role = 2
            <if test="lastCreateTime != null">
                and P.created_time > #{lastCreateTime}
            </if>
            <if test="searchProjectId != null and searchProjectId.length() > 0">
                and  P.project_id like concat('%', #{searchProjectId}, '%')
            </if>
            <if test="collectFinished != null and collectFinished == true">
                and BPS.check_status = 2 and BPS.image_uploaded = 1
            </if>
            <if test="imageUploaded != null">
                and BPS.image_uploaded = #{imageUploaded}
            </if>
            <if test="collectFinished != null and collectFinished == false">
                and (BPS.check_status is null or BPS.check_status != 2
                or BPS.image_uploaded is null or BPS.image_uploaded != 1)
            </if>
            <if test="inspectionStartTime != null and inspectionStartTime > 0">
                <![CDATA[
		    	and P.inspection_time >= #{inspectionStartTime}
		  	]]>
            </if>
            <if test="inspectionEndTime != null and inspectionEndTime > 0">
                <![CDATA[
		      and P.inspection_time <= #{inspectionEndTime}
		    ]]>
            </if>
            <if test="projectIds != null and projectIds.size > 0">
                and P.project_id in
                <foreach collection="projectIds" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="address != null and address != ''">
                and concat(P.address, ',', P.city, ',', P.state, ' ', P.zip_code) like  concat('%', #{address}, '%')
            </if>
        </where>
        order by BPS.image_uploaded_time desc, BPS.last_update_time desc
    </select>

	<!-- use projectId in entity but not project_id in database to point to the key -->
	<insert id="insert" parameterType="Project" useGeneratedKeys="true" keyProperty="projectId">
        insert into Project
        (policy_number, claim_number, cat_number, claim_rcv, claim_acv, claim_type, created_time,
        damage_event_time, created_by, project_type, address, city, state, country, zip_code,
        gps_location, asset_owner_name, asset_owner_phone, asset_owner_email, insurance_company, repair_company,
        material_provider_company, company_id,
        description, latest_status, fly_zone_type, test_flag)
        values
        (#{policyNumber}, #{claimNumber}, #{catNumber}, #{claimRcv}, #{claimAcv}, #{claimType}, #{createdTime},
        #{damageEventTime}, #{createdBy}, #{projectType}, #{address}, #{city}, #{state}, #{country}, #{zipCode},
        Point(#{gpsLocationLongitude},#{gpsLocationLatitude}), #{assetOwnerName}, #{assetOwnerPhone},
        #{assetOwnerEmail},
        #{insuranceCompany}, #{repairCompany}, #{materialProviderCompany}, #{companyId},
        #{description}, #{latestStatus}, #{flyZoneType}, #{testFlag}
        )
    </insert>

	<insert id="insertBaseInfo" parameterType="Project" useGeneratedKeys="true" keyProperty="projectId">

        insert into Project
	        (project_id, claim_number, cat_number, policy_number, claim_type, damage_event_time,
	        asset_owner_name, asset_owner_phone, asset_owner_email, project_type,
	        address, city, state, country, zip_code, gps_location,
	        insurance_company, repair_company, company_id, description,
        latest_status, created_by, created_time, claim_note,
        is_booking, contacter_name, contacter_email, contacter_phone,
        roof_estimated_area_item, report_service_option, need_pilot, fly_zone_type,


        inspection_number, due_date, customer, inspection_type,
        agent, agent_email, agent_contact_name, agent_phone,
        guideline, insured_home_phone, insured_work_phone,service_type,

        special_instructions, special_instruction_comments,
        policy_effective_date, year_built, gps_is_approximate, test_flag, address_id
        )
        values
        (#{projectId}, #{claimNumber}, #{catNumber}, #{policyNumber}, #{claimType},#{damageEventTime},
        #{assetOwnerName}, #{assetOwnerPhone}, #{assetOwnerEmail}, #{projectType},
        #{address}, #{city}, #{state}, #{country}, #{zipCode}, Point(#{gpsLocationLongitude},#{gpsLocationLatitude}),
        #{insuranceCompany}, #{repairCompany}, #{companyId}, #{description},
        #{latestStatus}, #{createdBy}, #{createdTime}, #{claimNote},
        #{booking}, #{contacterName}, #{contacterEmail}, #{contacterPhone},
        #{roofEstimatedAreaItem}, #{reportServiceOption}, #{needPilot}, #{flyZoneType},

        #{inspectionNumber}, #{dueDate}, #{customer}, #{inspectionType},
        #{agent}, #{agentEmail}, #{agentContactName}, #{agentPhone},#{guideline},
        #{insuredHomePhone}, #{insuredWorkPhone}, #{serviceType},

        #{specialInstructions}, #{specialInstructionComments},
        #{policyEffectiveDate}, #{yearBuilt}, #{gpsIsApproximate}, #{testFlag}, #{addressId}
        )
    </insert>

	<update id="update" parameterType="java.util.Map">
	   update Project
	   <set>
	       project_id = project_id,
	       <trim suffix="" suffixOverrides=",">
	           <if test="fields.policyNumber != null"> policy_number = #{fields.policyNumber}, </if>
	           <if test="fields.claimNumber != null"> claim_number = #{fields.claimNumber}, </if>
               <if test="fields.catNumber != null"> cat_number = #{fields.catNumber}, </if>
               <if test="fields.claimRcv != null"> claim_rcv = #{fields.claimRcv}, </if>
               <if test="fields.claimAcv != null"> claim_acv = #{fields.claimAcv}, </if>
               <if test="fields.claimType != null"> claim_type = #{fields.claimType}, </if>
               <if test="fields.createdTime != null"> created_time = #{fields.createdTime}, </if>
               <if test="fields.damageEventTime != null"> damage_event_time = #{fields.damageEventTime}, </if>
               <if test="fields.createdBy != null"> created_by = #{fields.createdBy}, </if>
               <if test="fields.projectType != null"> project_type = #{fields.projectType}, </if>
               <if test="fields.address != null"> address = #{fields.address}, </if>
               <if test="fields.city != null"> city = #{fields.city}, </if>
               <if test="fields.state != null"> state = #{fields.state}, </if>
               <if test="fields.country != null"> country = #{fields.country}, </if>
               <if test="fields.zipCode != null"> zip_code = #{fields.zipCode}, </if>
               <if test="fields.gpsLocationLatitude != null and fields.gpsLocationLongitude != null">
               		gps_location = Point(#{fields.gpsLocationLongitude},#{fields.gpsLocationLatitude}),
               </if>
               <if test="fields.assetOwnerName != null"> asset_owner_name = #{fields.assetOwnerName}, </if>
               <if test="fields.assetOwnerPhone != null"> asset_owner_phone = #{fields.assetOwnerPhone}, </if>
               <if test="fields.assetOwnerEmail != null"> asset_owner_email = #{fields.assetOwnerEmail}, </if>
               <if test="fields.insuranceCompany != null"> insurance_company = #{fields.insuranceCompany}, </if>
               <if test="fields.repairCompany != null"> repair_company = #{fields.repairCompany}, </if>
               <if test="fields.materialProviderCompany != null"> material_provider_company = #{fields.materialProviderCompany}, </if>
               <if test="fields.description != null"> description = #{fields.description}, </if>
               <if test="fields.claimNote != null"> claim_note = #{fields.claimNote}, </if>
               <if test="fields.latestStatus != null"> latest_status = #{fields.latestStatus}, </if>
			   <if test="fields.inspectionNumber != null"> inspection_number = #{fields.inspectionNumber}, </if>
               <if test="fields.needPilot != null"> need_pilot = #{fields.needPilot}, </if>
               <if test="fields.testFlag != null">test_flag = #{fields.testFlag}</if>
               <if test="fields.missionId != null">mission_id = #{fields.missionId}</if>
               <if test="fields.unsignedReason != null">unsigned_reason = #{fields.unsignedReason}</if>
	       </trim>
	   </set>
	   where project_id = #{projectId}
	</update>

	<update id="updateLatestStatus">
		update Project set latest_status = #{processStatus} where project_id = #{projectId} and  latest_status != #{processStatus};
	</update>

	<update id="updateClaimInfo" parameterType="java.util.Map">
	    update Project set policy_number = #{policyNumber}, damage_event_time = #{damageEventTime},
	    claim_type = #{claimType}, claim_note = #{claimNote}
	    <if test="claimNumber != null and claimNumber != ''">
	       ,claim_number = #{claimNumber}
	    </if>
	    where project_id = #{projectId}
	</update>

	<update id="updateProjectBaseInfor"  parameterType="Project">
		update Project set
		 asset_owner_name = #{assetOwnerName}, asset_owner_phone = #{assetOwnerPhone}, asset_owner_email = #{assetOwnerEmail},
		 project_type = #{projectType}, address = #{address}, city = #{city}, state = #{state}, country = #{country},
		 zip_code = #{zipCode}, gps_location = Point(#{gpsLocationLongitude},#{gpsLocationLatitude}),
		 insurance_company = #{insuranceCompany}, repair_company = #{repairCompany},
		 description = #{description}, fly_zone_type = #{flyZoneType},
        <if test="inspectionNumber != null  and inspectionNumber !=''">
                inspection_number = #{inspectionNumber},
        </if>
		  due_date = #{dueDate} , customer = #{customer}, inspection_type = #{inspectionType},
			agent = #{agent}, agent_email = #{agentEmail}, agent_contact_name = #{agentContactName}, agent_phone = #{agentPhone},
			guideline = #{guideline}, insured_home_phone = #{insuredHomePhone}, insured_work_phone = #{insuredWorkPhone}
		where project_id = #{projectId}
	</update>

	<update id="updateNorth">
	   update Project set north = #{north} where project_id = #{projectId}
	</update>

	<update id="updateProjectLatLng">
	   update Project set gps_location = Point(#{lng},#{lat}) where project_id = #{projectId}
	</update>

	<update id="updateInspectionTypes">
	   update Project set inspection_types = IFNULL(inspection_types,0) | #{inspectionTypes} where project_id = #{projectId}
	</update>

    <update id="updateInspectionTime">
        update Project set inspection_time = #{inspectionTime} where project_id = #{projectId}
    </update>

	<update id="updateDamageSeverity">
	   update Project set damage_severity=#{damageSeverity} where project_id=#{projectId}
	</update>

	<update id="updateProjectCompanyId">
	   update Project set company_id=#{newCompanyId} where created_by=#{userId} and company_id=#{oldCompanyId}
	</update>

	<update id="updateChimney">
		update Project set chimney = #{chimney} where project_id = #{projectId};
	</update>

	<update id="updateRotationDegree">
		update Project set rotation_degree = #{rotationDegree} where project_id = #{projectId};
	</update>

	<update id="updateImagesArchiveUrl">
		update Project set images_archive_url = #{imagesArchiveUrl} where project_id = #{projectId}
	</update>

    <update id="updateProjectStatus">
		update Project set project_status = #{projectStatus}, status_update_time = #{statusUpdateTime}
        where project_id = #{projectId}
        <if test="version != null">
            <![CDATA[
                    and status_update_time <= #{version}
                ]]>
        </if>
        ;
	</update>

    <update id="batchUpdateServiceType">
		update Project set service_type = #{serviceType} where project_id in
        <foreach collection="projectIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
	</update>

    <update id="updateByMap" parameterType="java.util.Map">
        update Project
        <set>
            project_id = project_id,
            <trim suffix="" suffixOverrides=",">
                <if test="fields.containsKey('policyNumber')"> policy_number = #{fields.policyNumber}, </if>
                <if test="fields.containsKey('claimNumber')"> claim_number = #{fields.claimNumber}, </if>
                <if test="fields.containsKey('catNumber')"> cat_number = #{fields.catNumber}, </if>
                <if test="fields.containsKey('claimRcv')"> claim_rcv = #{fields.claimRcv}, </if>
                <if test="fields.containsKey('claimAcv')"> claim_acv = #{fields.claimAcv}, </if>
                <if test="fields.containsKey('claimType')"> claim_type = #{fields.claimType}, </if>
                <if test="fields.containsKey('createdTime')"> created_time = #{fields.createdTime}, </if>
                <if test="fields.containsKey('damageEventTime')"> damage_event_time = #{fields.damageEventTime}, </if>
                <if test="fields.containsKey('createdBy')"> created_by = #{fields.createdBy}, </if>
                <if test="fields.containsKey('projectType')"> project_type = #{fields.projectType}, </if>
                <if test="fields.containsKey('address')"> address = #{fields.address}, </if>
                <if test="fields.containsKey('city')"> city = #{fields.city}, </if>
                <if test="fields.containsKey('state')"> state = #{fields.state}, </if>
                <if test="fields.containsKey('country')"> country = #{fields.country}, </if>
                <if test="fields.containsKey('zipCode')"> zip_code = #{fields.zipCode}, </if>
                <if test="fields.containsKey('addressId')"> address_id = #{fields.addressId}, </if>
                <if test="fields.containsKey('gpsIsApproximate')"> gps_is_approximate = #{fields.gpsIsApproximate}, </if>
                <if test="fields.containsKey('gpsLocationLatitude') and fields.containsKey('gpsLocationLongitude')">
                    gps_location = Point(#{fields.gpsLocationLongitude},#{fields.gpsLocationLatitude}),
                </if>
                <if test="fields.containsKey('assetOwnerName')"> asset_owner_name = #{fields.assetOwnerName}, </if>
                <if test="fields.containsKey('assetOwnerPhone')"> asset_owner_phone = #{fields.assetOwnerPhone}, </if>
                <if test="fields.containsKey('assetOwnerEmail')"> asset_owner_email = #{fields.assetOwnerEmail}, </if>
                <if test="fields.containsKey('insuranceCompany')"> insurance_company = #{fields.insuranceCompany}, </if>
                <if test="fields.containsKey('repairCompany')"> repair_company = #{fields.repairCompany}, </if>
                <if test="fields.containsKey('aterialProviderCompany')"> material_provider_company = #{fields.materialProviderCompany}, </if>
                <if test="fields.containsKey('description')"> description = #{fields.description}, </if>
                <if test="fields.containsKey('claimNote')"> claim_note = #{fields.claimNote}, </if>
                <if test="fields.containsKey('latestStatus')"> latest_status = #{fields.latestStatus}, </if>
                <if test="fields.containsKey('inspectionNumber')"> inspection_number = #{fields.inspectionNumber}, </if>
                <if test="fields.containsKey('needPilot')"> need_pilot = #{fields.needPilot}, </if>

                <if test="fields.containsKey('agent')"> agent = #{fields.agent}, </if>
                <if test="fields.containsKey('agentContactName')"> agent_contact_name = #{fields.agentContactName}, </if>
                <if test="fields.containsKey('agentPhone')"> agent_phone = #{fields.agentPhone}, </if>
                <if test="fields.containsKey('agentEmail')"> agent_email = #{fields.agentEmail}, </if>

                <if test="fields.containsKey('dueDate')">due_date = #{fields.dueDate}, </if>
                <if test="fields.containsKey('serviceType')">service_type = #{fields.serviceType}, </if>
                <if test="fields.containsKey('payStatus')">pay_status = #{fields.payStatus}, </if>

                <if test="fields.containsKey('policyEffectiveDate')">policy_effective_date = #{fields.policyEffectiveDate}, </if>
                <if test="fields.containsKey('yearBuilt')">year_built = #{fields.yearBuilt}, </if>
                <if test="fields.containsKey('flyZoneType')">fly_zone_type = #{fields.flyZoneType}, </if>
                <if test="fields.containsKey('serviceTypeReason')">service_type_reason = #{fields.serviceTypeReason}, </if>
                <if test="fields.containsKey('testFlag')">test_flag = #{fields.testFlag}</if>
            </trim>
        </set>
        where project_id = #{projectId}
    </update>

    <update id="updateImageUploadStatus">
        update Project
        set image_upload_status = #{status}
        where project_id = #{projectId}
    </update>

    <update id="updateReportRelevantData" parameterType="Project">
		update Project set chimney = #{chimney}, rotation_degree = #{rotationDegree}, north = #{north}, damage_severity=#{damageSeverity}
		    where project_id = #{projectId};
	</update>

    <select id="listByInspectionNumber" resultMap="baseResultMap">
        select <include refid="projectColumns" /> from Project
        where inspection_number = #{inspectionNumber} and latest_status != 0
        <if test="startTime != null">
            <![CDATA[
                and created_time >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
                and created_time <= #{endTime}
            ]]>
        </if>
        order by project_id desc;
    </select>

    <select id="listByInspectionNumberStartWith" resultMap="baseResultMap">
        select <include refid="projectColumns" /> from Project
        where inspection_number like concat(#{inspectionNumber}, '%') and latest_status != 0
        <if test="excludeStatuses.size > 0">
            and project_status not in
            <foreach collection="excludeStatuses" open="(" separator="," item="item" close=")">
                #{item}
            </foreach>
        </if>
        order by project_id desc;
    </select>

    <select id="listProjectWithSymbility" resultMap="baseResultMap">
        select P.* from ProjectSymbilityClaim SC left join Project P on SC.project_id = P.project_id
        where P.project_status not in (
            <foreach collection="excludeProjectStatuses" item="item" separator=",">
                #{item}
            </foreach>
        ) and P.latest_status not in (
            <foreach collection="excludeLatestStatuses" item="item" separator=",">
                #{item}
            </foreach>
        );
    </select>

    <select id="listProjectByClaimNumberOrInspectionNumber" resultMap="baseResultMap">
        select <include refid="projectColumns"/> from Project where claim_number = #{claimNumber} or inspection_number = #{inspectionNumber};
    </select>

    <select id="listByPolicyNumber" resultMap="baseResultMap">
        select <include refid="projectColumns"/> from Project where policy_number = #{policyNumber}
            <if test="createAtStart != null">
                <![CDATA[
                    and created_time >= #{createAtStart}
                ]]>
            </if>
            <if test="createAtEnd != null">
                <![CDATA[
                    and created_time <= #{createAtEnd}
                ]]>
            </if>
            <if test="companyId != null">
                and (insurance_company = #{companyId} or repair_company = #{companyId})
            </if>
    </select>


    <select id="listProjectsWithFilter"  resultMap="baseResultMap">
        select <include refid="projectColumns" /> from Project P
        <where>
            <if test="creatorId != null">
                P.created_by = #{creatorId}
            </if>
            <if test="repairCompany != null">
                and P.repair_company = #{repairCompany}
            </if>
            <if test="insuranceCompany != null">
                and P.insurance_company = #{insuranceCompany}
            </if>
            <if test="materialProviderCompany != null">
                and P.material_provider_company = #{materialProviderCompany}
            </if>
            <if test="projectStatuses != null and projectStatuses.size() > 0">
                and P.project_status in
                <foreach collection="projectStatuses" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="projectStatusTimeStart != null">
                <![CDATA[
					and P.status_update_time >= #{projectStatusTimeStart}
		        ]]>
            </if>
            <if test="projectStatusTimeEnd != null">
                <![CDATA[
					and P.status_update_time <= #{projectStatusTimeEnd}
		        ]]>
            </if>
            <if test="projectIds != null and projectIds.size() > 0">
                and project_id in
                <foreach collection="projectIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="inspectionStartTime != null and inspectionStartTime > 0">
                <![CDATA[
		    	and P.inspection_time >= #{inspectionStartTime}
		  	]]>
            </if>
            <if test="inspectionEndTime != null and inspectionEndTime > 0">
                <![CDATA[
		      and P.inspection_time <= #{inspectionEndTime}
		    ]]>
            </if>
            <if test="exclusiveStatuses != null and exclusiveStatuses.size() > 0">
                and P.project_status not in
                <foreach collection="exclusiveStatuses" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>

    <update id="updatePlnarURL">
        update Project set plnar_url = #{plnarURL} where project_id = #{projectId}
    </update>

    <update id="batchUpdatePlnarURL">
        update Project set plnar_url = #{plnarURL} where project_id in
        <foreach collection="projectIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updatePlnarStatus">
        update Project set plnar_status = #{status} where project_id = #{projectId}
    </update>

    <update id="updateHoverJobStatus">
      update Project set hover_job_status = #{status} where project_id = #{projectId}
    </update>

    <update id="updateClaimNote">
        update Project set claim_note = #{claimNote} where project_id = #{projectId}
    </update>

    <update id="updateHoverJobId">
        update Project set hover_job_id = #{hoverJobId} where project_id = #{projectId}
    </update>

    <update id="updateProject">
        update Project
        <set>
            policy_number = #{policyNumber},
            inspection_number = #{inspectionNumber},
            claim_type = #{claimType},
            fly_zone_type = #{flyZoneType},
            service_type = #{serviceType},
            damage_event_time = #{damageEventTime},
            policy_effective_date = #{policyEffectiveDate},
            due_date = #{dueDate},
            address = #{address},
            city = #{city},
            state = #{state},
            country = #{country},
            zip_code = #{zipCode},
            gps_location = Point(#{gpsLocationLongitude},#{gpsLocationLatitude}),
            gps_is_approximate = #{gpsIsApproximate},
            year_built = #{yearBuilt},
            project_type = #{projectType},
            repair_company = #{repairCompany},
            insurance_company = #{insuranceCompany}
        </set>
        <where>
            project_id = #{projectId}
        </where>
    </update>

    <update id="updateXactanalysisId">
        update Project set xactanalysis_id = #{xactanalysisId} where project_id = #{projectId};
    </update>

    <update id="updatePayStatus">
        update Project set pay_status = #{status} where project_id in
        <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
            #{projectId}
        </foreach>
    </update>

    <update id="updateProjectProperty">
        update Project set project_type = #{propertyType} where project_id = #{projectId}
    </update>

    <insert id="upsertProjectTimeline">
        insert into ProjectTimeline(project_id, initial_customer_contact_time, customer_contacted_time)
        values (#{projectId}, #{initialCustomerContactTime}, #{customerContactedTime})
        ON DUPLICATE KEY UPDATE
        <trim suffixOverrides="," suffix=";" >
            <if test="customerContactedTime != null">
                customer_contacted_time = #{customerContactedTime},
            </if>
            <if test="initialCustomerContactTime != null">
                initial_customer_contact_time = #{initialCustomerContactTime},
            </if>
        </trim>
    </insert>

	<select id="listProjectIdsByAddressId" resultType="java.lang.Long">
        select project_id from Project where address_id = #{addressId}
    </select>

    <select id="listProjectsForOpenapi" resultMap="ProjectTinyMap">
        select <include refid="ProjectTinyColumns"/>
        from Project P
        <include refid="projectCurUserMemberJoin"/>
        <where>
            <if test="deletedInclusion == false">
                P.latest_status != 0
            </if>
            <if test="deletedInclusion == true and isDeleted == true">
                and P.latest_status = 0
            </if>
            <if test="excludeStatus != null">
                and P.latest_status != #{excludeStatus}
            </if>
            <include refid="filterProjectCurUserJoinOrManagedBy" />
            <include refid="filterProjectWithSearch" />
        </where>
        <include refid="filterProjectWithSearchSortAndLimit" />
    </select>

    <select id="listAllCreators" resultType="java.lang.Long">
        select distinct created_by from Project where latest_status != 0;
    </select>
</mapper>
