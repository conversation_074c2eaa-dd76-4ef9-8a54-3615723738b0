<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.BeesPilotBatchMapper">

    <resultMap id="BaseResultMap" type="com.bees360.entity.BeesPilotBatch">
        <id column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="base_pay" jdbcType="DECIMAL" property="basePay"/>
        <result column="extra_pay" jdbcType="DECIMAL" property="extraPay"/>
        <result column="plan_payment_date" jdbcType="DATE" property="planPaymentDate"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="pay_time" jdbcType="BIGINT" property="payTime"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="due_date" jdbcType="DATE" property="dueDate"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>

    <sql id="baseColumns">
	   batch_no, user_id, base_pay, extra_pay, plan_payment_date, note, pay_time,
	    created_by, created_at, updated_at, due_date, is_deleted, status
	</sql>

    <insert id="insert" keyColumn="batch_no" keyProperty="batchNo" parameterType="com.bees360.entity.BeesPilotBatch">
        insert ignore into BeesPilotBatch (batch_no, user_id, base_pay, extra_pay,
                                     plan_payment_date, note, pay_time, created_by, due_date, status)
        values
		(#{batchNo}, #{userId}, #{basePay}, #{extraPay},
		 #{planPaymentDate}, #{note}, #{payTime},#{createdBy},#{dueDate}, #{status})
    </insert>

    <select id="getByBatchNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from BeesPilotBatch
        where batch_no = #{batchNo}
        and is_deleted = 0;
    </select>

    <select id="getByBatchNoWithoutDeletedCheck" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from BeesPilotBatch
        where batch_no = #{batchNo};
    </select>

    <select id="listBatch" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from BeesPilotBatch
        <where>
            <if test="query.pilotId != null">
                user_id =#{query.pilotId,jdbcType=BIGINT}
            </if>
            <if test="query.status != null">
                and status = #{query.status,jdbcType=INTEGER}
            </if>
            <if test="query.deleted != null">
                and is_deleted = #{query.deleted,jdbcType=BOOLEAN}
            </if>
        </where>
    </select>

    <select id="getByPlanPaymentTime" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from BeesPilotBatch
        where is_deleted = 0
        <if test="startDate != null">
            <![CDATA[
               and plan_payment_date >= #{startDate}
           ]]>
        </if>
        <if test="endDate != null">
            <![CDATA[
               and plan_payment_date <= #{endDate}
           ]]>
        </if>
        order by plan_payment_date asc;
    </select>

    <update id="deleteByBatchNo">
        update BeesPilotBatch set is_deleted = true
        where batch_no = #{batchNo};
    </update>

    <update id="updatePilotBatch"  parameterType="com.bees360.entity.BeesPilotBatchRequest">
        update BeesPilotBatch
        <set>
            <if test="basePay != null">
                base_pay = #{basePay},
            </if>
            <if test="extraPay != null">
                extra_pay = #{extraPay},
            </if>
            <if test="planPaymentDate != null">
                plan_payment_date = #{planPaymentDate},
            </if>
            <if test="note != null">
                note = #{note},
            </if>
            <if test="dueDate != null">
                due_date = #{dueDate},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                is_deleted = #{deleted,jdbcType=BOOLEAN}
            </if>
        </set>
        where batch_no = #{batchNo};
    </update>

    <update id="updatePilotBatchPrice" parameterType="com.bees360.entity.BeesPilotBatch">
        update BeesPilotBatch
        <set>
            base_pay = #{batch.basePay},
            extra_pay = #{batch.extraPay},
        </set>
        where batch_no = #{batch.batchNo};
    </update>

    <update id="updatePilotBatchDeletedStatus">
        update BeesPilotBatch set is_deleted = #{isDeleted}
       where batch_no = #{batchNo};
    </update>
</mapper>
