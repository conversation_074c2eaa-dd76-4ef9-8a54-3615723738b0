<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ProjectCreationMapper">

    <insert id="insert">
        insert into project_creation(project_id, creation_channel)
        values(#{projectId}, #{creationChannel});
    </insert>

    <select id="getByProjectId" resultType="com.bees360.entity.ProjectCreation">
        select * from project_creation where project_id = #{projectId};
    </select>
</mapper>
