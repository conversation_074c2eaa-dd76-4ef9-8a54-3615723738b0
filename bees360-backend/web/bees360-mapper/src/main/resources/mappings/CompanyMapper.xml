<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.CompanyMapper">

	<resultMap id="baseCompanyMap" type="com.bees360.entity.Company">
	    <id column="company_id" jdbcType="BIGINT" property="companyId"/>
	    <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="company_key" jdbcType="VARCHAR" property="companyKey"/>
        <result column="company_type" jdbcType="INTEGER" property="companyType"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="website" jdbcType="VARCHAR" property="website"/>
        <result column="logo" jdbcType="VARCHAR" property="logo"/>
		<result column="auto_pay" jdbcType="INTEGER" property="autoPay" />
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
        <result column="created_time" jdbcType="BIGINT" property="createdTime" />
        <result column="updated_time" jdbcType="BIGINT" property="updatedTime" />
	</resultMap>

	<sql id="companyColumns">
	   C.company_id, C.company_name, C.company_key, C.company_type, C.phone, C.email, C.contact_name, C.website, C.logo,
	   C.auto_pay, C.is_deleted, C.created_time, C.updated_time
	</sql>

    <select id="listAll" resultMap="baseCompanyMap">
        select * from Company where is_deleted = 0 order by company_name
	</select>

	<select id="listWithPrefix" resultMap="baseCompanyMap">
        select * from Company where is_deleted = 0 and lower(company_name) like #{prefix} order by company_name limit #{limit};
    </select>

	<select id="listByType" resultMap="baseCompanyMap">
        select * from Company where is_deleted = 0 and company_type = #{companyType} order by company_name;
	</select>

    <select id="listByTypeWithPrefix" resultMap="baseCompanyMap">
        select * from Company
        where is_deleted = 0 and lower(company_name) like #{prefix} and company_type = #{companyType}
            order by company_name limit #{limit};
    </select>

    <sql id="companySearchOptionFilter">
    	<if test="companyId != null">
    		and company_id = #{companyId}
    	</if>
    	<if test="companyNameRegex != null and companyNameRegex.length() > 0">
    		and company_name like #{companyNameRegex}
    	</if>
    	<if test="companyType != null">
    		and company_type = #{companyType}
    	</if>
    	<if test="autoPay != null">
			and auto_pay = #{autoPay}
		</if>
    </sql>

    <sql id="companySearchOptionOrder">
   		<choose>
   			<when test="sortKey == 'companyId'">
    			order by company_id
    		</when>
    		<when test="sortKey == 'companyName'">
    			order by company_name
    		</when>
    		<when test="sortKey == 'createdTime'">
    			order by created_time
    		</when>
    		<when test="sortKey == 'companyType'">
    			order by company_type
    		</when>
   			<otherwise>
   				order by created_time
   			</otherwise>
   		</choose>
   		<choose>
   			<when test="sortOrder == 'ASC'">
   				asc
   			</when>
   			<otherwise>
   				desc
   			</otherwise>
   		</choose>
    </sql>

    <select id="listByOption" resultMap="baseCompanyMap">
    	select * from Company
    	<where>
            <include refid="companySearchOptionFilter" />
    	    and is_deleted = 0
    	</where>
    	<include refid="companySearchOptionOrder" />
    	limit #{indexStart}, #{pageSize};
    </select>

	<select id="listIn" resultMap="baseCompanyMap">
		select * from Company
		where company_id in
			<foreach collection="companyIds" open="(" separator="," item="item" close=")">
				#{item}
			</foreach>
	</select>

    <select id="countByOption" resultType="int">
    	select count(*) from Company
    	<where>
            <include refid="companySearchOptionFilter" />
            and is_deleted = 0
    	</where>
    </select>

    <select id="getByName" resultMap="baseCompanyMap">
        select * from Company where is_deleted = 0 and company_name = #{companyName} limit 1;
    </select>

    <select id="getIdByKey" resultType="java.lang.Long">
        select company_id from Company where is_deleted = 0 and company_key = #{companyKey} limit 1;
    </select>

    <select id="getByUserId" resultMap="baseCompanyMap">
        select <include refid="companyColumns" /> from Company C, User U
           where C.company_id = U.company_id and U.user_id = #{userId}
    </select>

	<select id="getById" resultMap="baseCompanyMap">
    	select * from Company where company_id = #{companyId};
	</select>

	<insert id="insert" useGeneratedKeys="true" keyProperty="companyId">
		insert into Company(company_name, company_key, company_type, phone, email, contact_name, website, logo,
			auto_pay, is_deleted, created_time, updated_time)
		values
		(#{companyName}, #{companyKey}, #{companyType}, #{phone}, #{email}, #{contactName}, #{website}, #{logo},
			#{autoPay}, #{isDeleted}, #{createdTime}, #{updatedTime})
	</insert>

	<update id="update">
		update Company
		<set>
			company_name = #{companyName},
			company_type = #{companyType},
			phone = #{phone},
			email = #{email},
			contact_name = #{contactName},
			website = #{website},
			logo = #{logo},
			auto_pay = #{autoPay},
			updated_time = #{updatedTime}
		</set>
		where company_id = #{companyId};
	</update>

    <update id="updateCompanyKey">
        update Company c
        <set>
            company_key = #{companyKey},
            updated_time = #{updatedTime}
        </set>
        where c.company_id = #{companyId} and c.is_deleted = 0 and not exists (select 1 from (select company_id from
        Company c1 where
        c1.company_key = #{companyKey} and c1.is_deleted = 0) tmp);
    </update>

    <update id="updateLogo">
		update Company
		<set>
			logo = #{logo},
			updated_time = #{updatedTime}
		</set>
		where company_id = #{companyId};
	</update>

	<update id="delete">
		update Company set is_deleted = #{companyId}, updated_time = #{updatedTime} where company_id = #{companyId};
	</update>
</mapper>
