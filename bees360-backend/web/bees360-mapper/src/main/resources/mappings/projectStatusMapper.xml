<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bees360.mapper.ProjectStatusMapper">

    <resultMap id="baseColumn" type="com.bees360.entity.ProjectStatus">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="created_time" jdbcType="BIGINT" property="createdTime" />
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
    </resultMap>

    <resultMap id="productStatusUserMap" type="com.bees360.entity.ProjectStatusUser">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="created_time" jdbcType="BIGINT" property="createdTime" />
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
    </resultMap>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into ProjectStatus(project_id, user_id, status, created_time)
        value(#{projectId}, #{userId}, #{status}, #{createdTime});
    </insert>

    <update id="delete">
		update ProjectStatus set is_deleted = 1 where id = #{id};
    </update>

    <update id="deleteBatch">
        update ProjectStatus set is_deleted = 1  where id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="getById" resultMap="baseColumn" >
        select * from ProjectStatus where id = #{id};
    </select>

    <select id="listByProjectId" resultMap="baseColumn">
        select * from ProjectStatus P
         where P.project_id = #{projectId}
         and P.is_deleted = 0 order by created_time desc;
    </select>

    <select id="listByProjectIdWithUser" resultMap="productStatusUserMap">
        select P.*, CONCAT(U.first_name, ' ', U.last_name) as user_name from ProjectStatus P
        left join User U on U.user_id = P.user_id  where P.project_id = #{projectId} and P.is_deleted = 0 order by P.created_time desc;
    </select>

    <select id="listByProjectIds" resultMap="baseColumn">
        select * from ProjectStatus where project_id in
            <foreach collection="list" open="(" item="item" separator="," close=")" >
                #{item}
            </foreach>
        and is_deleted = 0
        order by created_time desc;
    </select>

    <select id="listByProjectIdsWithUser" resultMap="productStatusUserMap">
        select P.*, CONCAT(U.first_name, ' ', U.last_name) as user_name from ProjectStatus P
        left join User U on U.user_id = P.user_id where P.project_id in
        <foreach collection="list" open="(" item="item" separator="," close=")" >
            #{item}
        </foreach>
        and P.is_deleted = 0
        order by P.created_time asc;
    </select>

    <select id="listWithQuery" resultMap="baseColumn">
        select * from ProjectStatus
        <where>
            <if test="projectId != null">
                project_id = #{projectId}
            </if>
            <if test="projectStatus != null">
                and status = #{projectStatus}
            </if>
            <if test="projectIds != null and projectIds.size() > 0">
                and project_id in (
                <foreach collection="projectIds" item="id" separator=",">
                    #{id}
                </foreach>
                )
            </if>
            <if test="createdTimeStart != null">
                <![CDATA[
                    and created_time >= #{createdTimeStart}
                ]]>
            </if>
            <if test="createdTimeEnd != null">
                <![CDATA[
                    and created_time < #{createdTimeEnd}
                ]]>
            </if>
            <if test="projectStatusLeft != null">
                <![CDATA[
                    and status <= #{projectStatusLeft}
                ]]>
            </if>
            <if test="projectStatusRight != null">
                <![CDATA[
                    and status >= #{projectStatusRight}
                ]]>
            </if>
        </where>
        and is_deleted = 0
        order by created_time desc;
    </select>

    <select id="getProjectsByStatusTime" resultType="java.lang.Long">
        select
            distinct P.project_id
        from ProjectStatus P
        where P.status = #{projectStatus}
        and P.is_deleted = 0
        and P.created_time >= #{startTime}
         <![CDATA[ and P.created_time <= #{endTime} ]]>
        order by P.project_id asc;
    </select>

    <select id="listByProjectIdsAndStatus" resultMap="baseColumn">
        select * from ProjectStatus P where P.project_id in (
            <foreach collection="projectIds" item="id" separator=",">
                #{id}
            </foreach>
        )
        and P.status = #{projectStatus}
        and P.is_deleted = 0
        order by created_time desc;
    </select>

    <update id="updateCreatedTimeById">
        update ProjectStatus set created_time = #{createdTime}
        where id = #{id}
    </update>

    <select id="getProjectsByStatusLisAndTime" resultType="java.lang.Long">
        select
        distinct P.project_id
        from ProjectStatus P
        where P.status
        in
        <foreach collection="projectStatusList" item="projectStatus" separator="," open="(" close=")">
            #{projectStatus}
        </foreach>
        and P.is_deleted = 0
        and P.created_time >= #{startTime}
        <![CDATA[ and P.created_time <= #{endTime} ]]>
        order by P.project_id asc;
    </select>
</mapper>
