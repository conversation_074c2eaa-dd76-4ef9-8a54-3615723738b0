<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.MissionAttachFileMapper">

    <resultMap id="resultMap" type="com.bees360.entity.MissionAttachFile">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="upload_time" jdbcType="BIGINT" property="uploadTime"/>
    </resultMap>

    <sql id="allColumn">
        id, project_id, url, deleted, type, create_time, upload_time
    </sql>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into MissionAttachFile(project_id, url, deleted, type, create_time, upload_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.projectId, jdbcType=BIGINT}, #{item.url, jdbcType=VARCHAR},
            #{item.deleted, jdbcType=BOOLEAN}, #{item.type},
            #{item.createTime, jdbcType=BIGINT}, #{item.uploadTime, jdbcType=BIGINT} )
        </foreach>
    </insert>

    <update id="updateUrlAndUploadTime">
        update MissionAttachFile
        set url = #{url}, upload_time = #{uploadTime}
        where id = #{id}
    </update>

    <select id="listByProjectId" resultMap="resultMap">
        select
        <include refid="allColumn"/>
        from MissionAttachFile
        where project_id = #{projectId} and deleted != 1
    </select>

</mapper>
