<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ProjectAirspaceMapper">
    <insert id="upsertByProjectId">
        insert into project_airspace(project_id, status, height_ceiling)
        values (#{projectId}, #{status}, #{heightCeiling})
        ON DUPLICATE KEY UPDATE status = #{status}, height_ceiling = #{heightCeiling}
    </insert>

    <select id="getByProjectIds" resultType="com.bees360.entity.ProjectAirspace">
        select * from project_airspace
        where project_id in
        <foreach collection="projectIds" open="(" separator="," item="item" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
