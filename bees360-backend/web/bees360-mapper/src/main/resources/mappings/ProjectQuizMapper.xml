<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ProjectQuizMapper">

	<resultMap id="answerResultMap" type="com.bees360.entity.ProjectQuiz">
	    <id column="project_quiz_id" jdbcType="BIGINT" property="projectQuizId"/>
	    <id column="quiz_id" jdbcType="BIGINT" property="quizId"/>
	    <id column="project_id" jdbcType="BIGINT" property="projectId"/>
	    <result column="answer" jdbcType="VARCHAR" property="answer"/>
	    <result column="create_time" jdbcType="BIGINT" property="createTime"/>
    </resultMap>

    <resultMap id="companyQuizResultMap" type="com.bees360.entity.Quiz">
        <id column="quiz_id" jdbcType="BIGINT" property="quizId"/>
        <result column="subject" jdbcType="VARCHAR" property="subject"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="choices" jdbcType="VARCHAR" property="choices"/>
        <result column="sequence" jdbcType="INTEGER" property="sequence"/>
    </resultMap>

    <sql id="answerColumn">
      pq.project_quiz_id, pq.quiz_id, pq.project_id, pq.answer, pq.create_time
    </sql>

    <sql id="quizColumn">
        q.quiz_id, q.subject, q.type, q.choices, c.sequence
    </sql>
    <insert id="batchInsertAnswer" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="projectQuizId">
        insert into ProjectQuiz(quiz_id, project_id, answer, create_time, claim_type)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.quizId, jdbcType=BIGINT}, #{item.projectId, jdbcType=BIGINT},
            #{item.answer, jdbcType=VARCHAR}, #{item.createTime, jdbcType=BIGINT},
            #{item.claimType, jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="listLatestAnswers" resultMap="answerResultMap">
        select <include refid="answerColumn"/>
        from ProjectQuiz pq,
        (select quiz_id, MAX(create_time) as create_time
            from ProjectQuiz
            where project_id = #{projectId} and claim_type = #{claimType} group by quiz_id) o
        where pq.project_id = #{projectId} and pq.quiz_id=o.quiz_id and pq.create_time=o.create_time
    </select>

    <select id="listCompanyQuiz" resultMap="companyQuizResultMap">
        select <include refid="quizColumn"/>
        from CompanyQuiz c inner join Quiz q on c.quiz_id = q.quiz_id
        where c.company_id = #{possessBy} order by c.sequence
    </select>

    <select id="listLatestAnswersOfQuizType" resultMap="answerResultMap">
        select <include refid="answerColumn"/>
        from ProjectQuiz pq where pq.project_quiz_id in (select max(project_quiz_id) from ProjectQuiz
        group by quiz_id, project_id)
        <if test="quizId != null">
            and pq.quiz_id = #{quizId}
        </if>
        <if test="projectIds != null and !projectIds.isEmpty()">
            and pq.project_id in
            <foreach item="projectId" collection="projectIds" open="(" close=")" index="index" separator=",">
                #{projectId}
            </foreach>
        </if>
    </select>

    <update id="updateAnswers">
        update ProjectQuiz
        set answer = #{answer}
        where project_quiz_id = #{projectQuizId}
    </update>
</mapper>
