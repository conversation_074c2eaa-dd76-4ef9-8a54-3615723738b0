<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ServiceFeeTypeMapper">

	<resultMap id="baseMap" type="ServiceFeeType">
	    <id column="service_fee_type" jdbcType="INTEGER" property="serviceFeeType"/>
	    <id column="report_type" jdbcType="INTEGER" property="reportType" />
	</resultMap>

	<sql id="baseColumn" >
		service_fee_type, report_type
	</sql>

	<select id="list" resultMap="baseMap">
		select <include refid="baseColumn" /> from ServiceFeeType;
	</select>

	<select id="get" resultMap="baseMap">
		select <include refid="baseColumn" /> from ServiceFeeType
		where service_fee_type = #{serviceFeeType} and report_type = #{reportType};
	</select>
</mapper>
