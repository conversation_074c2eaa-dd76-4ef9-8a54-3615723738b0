<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.BsExportDataMapper">


	<resultMap id="bsExportDataResultMap"
		type="com.bees360.entity.BsExportData">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="related_id" jdbcType="VARCHAR" property="relatedId" />
		<result column="related_type" jdbcType="VARCHAR" property="relatedType" />
		<result column="data_log" jdbcType="VARCHAR" property="dataLog" />
	</resultMap>

	<sql id="bsExportDataColumns">
	   id, related_id, related_type, data_log
	</sql>

	<select id="listAll" resultMap="bsExportDataResultMap">
	    select <include refid="bsExportDataColumns" /> from bs_export_data;
	</select>

    <select id="getById" resultMap="bsExportDataResultMap">
    	select * from bs_export_data where id = #{id};
	</select>

    <select id="getExportData" resultMap="bsExportDataResultMap">
        select
        <include refid="bsExportDataColumns"/>
        from bs_export_data
        <where>
            <if test="relatedType != null and relatedType.length() > 0">
                related_type  = #{relatedType}
            </if>
            <if test="relatedId != null and relatedId.length() > 0">
                and related_id  = #{relatedId}
            </if>
        </where>
    </select>

    <select id="listIn" resultMap="bsExportDataResultMap">
        select
        <include refid="bsExportDataColumns"/>
        from bs_export_data
        <where>
            <choose>
                <when test="relatedIds != null and relatedIds.size() > 0">
                    related_id in
                    <foreach collection="relatedIds" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    1 = 2
                </otherwise>
            </choose>
            and related_type = #{relatedType}
        </where>
    </select>

    <insert id="insertExportData" useGeneratedKeys="true" keyProperty="id">
        insert into bs_export_data(related_id, related_type, data_log)
        values (#{param.relatedId}, #{param.relatedType}, #{param.dataLog})
    </insert>

    <update id="updateExportData">
        update bs_export_data set data_log = #{param.dataLog}, related_id = #{param.relatedId}, related_type = #{param.relatedType}
        where id = #{param.id}
    </update>

    <delete id="deleteExportData">
      delete from bs_export_data
      where id = #{id}
    </delete>
</mapper>
