<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bees360.mapper.BeesPilotStatusMapper">
    <resultMap id="BaseResultMap" type="com.bees360.entity.BeesPilotStatus">
        <id column="status_id" jdbcType="BIGINT" property="statusId"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="mobile_check_status" jdbcType="INTEGER" property="mobileCheckStatus"/>
        <result column="drone_check_status" jdbcType="INTEGER" property="droneCheckStatus"/>
        <result column="check_status" jdbcType="INTEGER" property="checkStatus"/>
        <result column="quiz_completed" jdbcType="BOOLEAN" property="quizCompleted"/>
        <result column="image_upload_status" jdbcType="INTEGER" property="imageUploadStatus"/>
        <result column="image_uploaded" jdbcType="INTEGER" property="imageUploaded"/>
        <result column="address_verified" jdbcType="BOOLEAN" property="addressVerified"/>
        <result column="image_uploaded_time" jdbcType="BIGINT" property="imageUploadedTime"/>
        <result column="checkout_time" jdbcType="BIGINT" property="checkoutTime"/>
        <result column="last_update_time" jdbcType="BIGINT" property="lastUpdateTime"/>
        <result column="pre_checkout_reason" jdbcType="INTEGER" property="preCheckoutReason"/>
    </resultMap>
    <sql id="Base_Column_List">
        status_id, project_id, mobile_check_status, drone_check_status, check_status,
    quiz_completed, image_upload_status, image_uploaded, address_verified, image_uploaded_time,
    checkout_time, last_update_time, pre_checkout_reason
    </sql>

    <select id="getByProjectId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from BeesPilotStatus
        where project_id = #{projectId,jdbcType=BIGINT}
    </select>

    <select id="listBeesPilotStatus" parameterType="com.bees360.entity.query.BeesPilotStatusQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from BeesPilotStatus
        <where>
            <if test="projectIdList != null and projectIdList.size() > 0">
                project_id in
                <foreach collection="projectIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from BeesPilotStatus
        where status_id = #{statusId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="status_id" keyProperty="statusId" parameterType="com.bees360.entity.BeesPilotStatus"
            useGeneratedKeys="true">
        insert into BeesPilotStatus (project_id, mobile_check_status, drone_check_status, check_status,
                                     quiz_completed, image_upload_status, image_uploaded, address_verified,
                                     image_uploaded_time,checkout_time, last_update_time)
        values (#{projectId,jdbcType=BIGINT},
                #{mobileCheckStatus,jdbcType=INTEGER},#{droneCheckStatus,jdbcType=INTEGER},#{checkStatus,jdbcType=INTEGER},
                #{quizCompleted,jdbcType=BOOLEAN}, #{imageUploadStatus}, #{imageUploaded,jdbcType=BOOLEAN},#{addressVerified,jdbcType=BOOLEAN},
                #{imageUploadedTime,jdbcType=BIGINT},#{checkoutTime,jdbcType=BIGINT}, #{lastUpdateTime, jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="status_id" keyProperty="statusId"
            parameterType="com.bees360.entity.BeesPilotStatus" useGeneratedKeys="true">
        insert into BeesPilotStatus
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">
                project_id,
            </if>
            <if test="mobileCheckStatus != null">
                mobile_check_status,
            </if>
            <if test="droneCheckStatus != null">
                drone_check_status,
            </if>
            <if test="checkStatus != null">
                check_status,
            </if>
            <if test="quizCompleted != null">
                quiz_completed,
            </if>
            <if test="imageUploadStatus != null">
                image_upload_status,
            </if>
            <if test="imageUploaded != null">
                image_uploaded,
            </if>
            <if test="addressVerified != null">
                address_verified,
            </if>
            <if test="imageUploadedTime != null">
                image_uploaded_time,
            </if>
            <if test="checkoutTime != null">
                checkout_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">
                #{projectId,jdbcType=BIGINT},
            </if>
            <if test="mobileCheckStatus != null">
                #{mobileCheckStatus,jdbcType=INTEGER},
            </if>
            <if test="droneCheckStatus != null">
                #{droneCheckStatus,jdbcType=INTEGER},
            </if>
            <if test="checkStatus != null">
                #{checkStatus,jdbcType=INTEGER},
            </if>
            <if test="quizCompleted != null">
                #{quizCompleted,jdbcType=BOOLEAN},
            </if>
            <if test="imageUploadStatus != null">
                #{imageUploadStatus,jdbcType=INTEGER},
            </if>
            <if test="imageUploaded != null">
                #{imageUploaded,jdbcType=BOOLEAN},
            </if>
            <if test="addressVerified != null">
                #{addressVerified,jdbcType=BOOLEAN},
            </if>
            <if test="imageUploadedTime != null">
                #{imageUploadedTime,jdbcType=BIGINT},
            </if>
            <if test="checkoutTime != null">
                #{checkoutTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateByProjectId" parameterType="com.bees360.entity.BeesPilotStatus">
        update BeesPilotStatus
        set project_id          = #{projectId,jdbcType=BIGINT},
            mobile_check_status = #{mobileCheckStatus,jdbcType=INTEGER},
            drone_check_status  = #{droneCheckStatus,jdbcType=INTEGER},
            check_status        = #{checkStatus,jdbcType=INTEGER},
            quiz_completed      = #{quizCompleted,jdbcType=BOOLEAN},
            image_upload_status = #{imageUploadStatus,jdbcType=INTEGER},
            image_uploaded      = #{imageUploaded,jdbcType=BOOLEAN},
            address_verified    = #{addressVerified,jdbcType=BOOLEAN},
            image_uploaded_time = #{imageUploadedTime,jdbcType=BIGINT},
            checkout_time       = #{checkoutTime,jdbcType=BIGINT},
            pre_checkout_reason = #{preCheckoutReason,jdbcType=INTEGER},
            last_update_time    = #{lastUpdateTime,jdbcType=BIGINT}
        where project_id = #{projectId,jdbcType=BIGINT}
    </update>
</mapper>
