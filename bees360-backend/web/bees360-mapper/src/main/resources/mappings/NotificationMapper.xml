<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.NotificationMapper">
	<resultMap id="baseNotificationMap" type="com.bees360.entity.Notification">
	    <id column="id" jdbcType="BIGINT" property="notificationId"/>
	    <result column="project_id" jdbcType="BIGINT" property="projectId"/>
	    <result column="created_time" jdbcType="BIGINT" property="createdTime"/>
	    <result column="project_status" jdbcType="INTEGER" property="projectStatus"/>
	    <result column="recipient" jdbcType="BIGINT" property="recipient"/>
	    <result column="content" jdbcType="VARCHAR" property="content"/>
	</resultMap>

	<select id="listAll" resultMap="baseNotificationMap">
		select *
			from Notification
			where recipient = #{userId}
			    order by created_time desc
	</select>

	<select id="listPage" resultMap="baseNotificationMap">
        select *
            from Notification
            where recipient = #{userId}
                order by created_time desc limit #{startIndex}, #{pageSize};
    </select>

    <select id="count" resultType="int">
        select count(*) from Notification where recipient = #{userId};
    </select>

	<delete id="deleteAllByUserId">
		delete from Notification
		      where recipient = #{userId}
	</delete>

	<delete id="deleteOne">
		delete from Notification
			where recipient = #{userId} and id = #{notificationId}
	</delete>

    <insert id="insertNotification" parameterType="Notification" useGeneratedKeys="true" keyProperty="notificationId">
    	insert into Notification
	    	(project_id, created_time, project_status, recipient, content)
	    	values
	    	(#{projectId}, #{createdTime}, #{projectStatus}, #{recipient}, #{content})
	</insert>

	<insert id="insertRoleApplicationNotification" parameterType="Notification" useGeneratedKeys="true" keyProperty="notificationId">
    	insert into Notification
	    	(created_time, recipient, content)
	    	values
	    	(#{createdTime}, #{recipient}, #{content})
	</insert>
</mapper>
