<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.AddressAirspaceMapper">
    <insert id="upsertByAddressId">
        insert into address_airspace(address_id, status, height_ceiling)
        values (#{addressId}, #{status}, #{heightCeiling})
        ON DUPLICATE KEY UPDATE status = #{status}, height_ceiling = #{heightCeiling}
    </insert>

    <select id="getByAddressIds" resultType="com.bees360.entity.AddressAirspace">
        select * from address_airspace
        where address_id in
            <foreach collection="addressIds" open="(" separator="," item="item" close=")">
                #{item}
            </foreach>
    </select>
</mapper>
