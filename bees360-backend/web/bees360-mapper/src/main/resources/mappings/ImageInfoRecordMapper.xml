<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ImageInfoRecordMapper">

	<resultMap id="fullMap" type="com.bees360.entity.dto.ImageInfoRecord">
	    <id column="id" jdbcType="BIGINT" property="id"/>
	    <result column="project_id" jdbcType="INTEGER" property="projectId" />
	    <result column="original_file_name" jdbcType="VARCHAR" property="originalFileName" />
	    <result column="file_size" jdbcType="BIGINT" property="fileSize" />
	    <result column="file_source_type" jdbcType="INTEGER" property="fileSourceType" />
        <result column="image_type" jdbcType="INTEGER" property="imageType" />
        <result column="partial_type" jdbcType="INTEGER" property="partialType"/>
        <result column="uploaded_to_s3" jdbcType="INTEGER" property="uploadedToS3"/>
        <result column="md5" jdbcType="INTEGER" property="md5"/>
	</resultMap>

    <sql id="full_column">
        id, project_id, original_file_name, file_size, file_source_type,image_type, partial_type, md5, uploaded_to_s3
    </sql>

    <sql id="full_column_exclude_id">
        project_id, original_file_name, file_size, file_source_type, image_type, partial_type, md5, uploaded_to_s3
    </sql>

    <select id="listImageInfo" resultMap="fullMap">
        select <include refid="full_column"/> from ImageInfoRecord
        <where>
            <if test="projectId != null">
                project_id = #{projectId}
            </if>
            <if test="uploadedToS3 != null">
                and uploaded_to_s3 = #{uploadedToS3}
            </if>
        </where>
    </select>

    <update id="updateRecord">
        update ImageInfoRecord set
        original_file_name = #{item.originalFileName}
        , file_size = #{item.fileSize}
        , file_source_type = #{item.fileSourceType}
        , image_type = #{item.imageType}
        , partial_type = #{item.partialType}
        , md5 = #{item.md5}
        , uploaded_to_s3 = #{item.uploadedToS3}
        where project_id = #{projectId} and id = #{item.id}
    </update>
</mapper>
