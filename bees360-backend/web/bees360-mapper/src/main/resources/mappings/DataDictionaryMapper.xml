<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.DataDictionaryMapper">

	<resultMap id="baseMapper" type="com.bees360.entity.dto.DataDictionary">
	    <id column="id" jdbcType="BIGINT" property="id"/>
	    <result column="namespace" jdbcType="VARCHAR" property="namespace"/>
	    <result column="code" jdbcType="VARCHAR" property="code"/>
	    <result column="name" jdbcType="VARCHAR" property="name"/>
	    <result column="value" jdbcType="VARCHAR" property="value"/>
	    <result column="sequence" jdbcType="INTEGER" property="sequence"/>
	</resultMap>

    <sql id="baseSql">id, namespace, `code`, `name`, `value`, `sequence`</sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into DataDictionary( namespace, `code`, `name`, `value`, `sequence`)
        value (#{namespace}, #{code}, #{name}, #{value}, #{sequence})

    </insert>
    <update id="updateByNamespaceAndCode" >
        update DataDictionary
        set name = #{name,jdbcType=VARCHAR}, value = #{value,jdbcType=VARCHAR}, sequence = #{sequence}
        where namespace = #{namespace,jdbcType=VARCHAR} and code = #{code,jdbcType=VARCHAR}
    </update>

    <select id="getByNamespaceAndCode" resultMap="baseMapper">
        select <include refid="baseSql"/> from DataDictionary where namespace = #{namespace} and code = #{code}
    </select>


    <select id="listByNamespace" resultMap="baseMapper">
        select <include refid="baseSql"/> from DataDictionary where namespace = #{namespace}  order by sequence;
    </select>
</mapper>
