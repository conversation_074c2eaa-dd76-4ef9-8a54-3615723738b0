<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bees360.mapper.pilot.PilotMapper">

	<resultMap id="baseMap" type="com.bees360.entity.pilot.Base" >
		<result column="is_enable" jdbcType="TINYINT" property="isEnable" />
		<result column="is_delete" jdbcType="TINYINT" property="isDelete" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="create_by" jdbcType="INTEGER" property="createBy" />
		<result column="update_by" jdbcType="INTEGER" property="updateBy" />
		<result column="version" jdbcType="INTEGER" property="version" />
	</resultMap>

	<resultMap id="baseResultMap" type="com.bees360.entity.pilot.Pilot" extends="baseMap">
		<id column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="email" jdbcType="VARCHAR" property="email" />
		<result column="phone" jdbcType="VARCHAR" property="phone" />
		<result column="account" jdbcType="VARCHAR" property="account" />
		<result column="user_name" jdbcType="VARCHAR" property="userName" />
		<result column="travel_radius" jdbcType="INTEGER" property="travelRadius" />
		<result column="license_key_urls" jdbcType="VARCHAR" property="licenseKeyUrls" />
		<result column="insurance_key_urls" jdbcType="VARCHAR" property="insuranceKeyUrls" />
		<result column="license_issue_date" jdbcType="TIMESTAMP" property="licenseIssueDate" />
		<result column="license_expiry_date" jdbcType="TIMESTAMP" property="licenseExpiryDate" />
		<result column="insurance_expiry_date" jdbcType="TIMESTAMP" property="insuranceExpiryDate" />
		<result column="insurance_underwriting_date" jdbcType="TIMESTAMP" property="insuranceUnderwritingDate" />
		<result column="insurance_amount" jdbcType="DECIMAL" property="insuranceAmount" />
		<result column="license_number" jdbcType="VARCHAR" property="licenseNumber" />
		<result column="insurance_number" jdbcType="VARCHAR" property="insuranceNumber" />
	</resultMap>

    <sql id="pilotColumns">

	</sql>


	<select id="queryByUserId" resultMap="baseResultMap">
		select
		*
        from pilot
		where user_id = #{value}
    	and is_enable = 1 and is_delete = 0 limit 1
	</select>

	<select id="queryById" resultMap="baseResultMap">
		select
		*
        from pilot
		where id = #{value}
    	and is_enable = 1 and is_delete = 0 limit 1
	</select>

	<select id="queryByPilot" resultMap="baseResultMap">
		select
		*
        from pilot
		<trim prefix="WHERE (" suffix=")" prefixOverrides="AND |OR ">
			<if test="phone != null and phone != ''">
				phone = #{phone}
			</if>
			<if test="email != null and email != ''">
				or email = #{email}
			</if>
			<if test="account != null and account != ''">
				or account = #{account}
			</if>
		</trim>
    	and is_enable = 1 and is_delete = 0 limit 1
	</select>

	<insert id="insert" parameterType="com.bees360.entity.pilot.Pilot" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO pilot
			(
			  user_id,
			  account,
			  user_name,
			  email,
			  phone,

			  travel_radius,
			  license_key_urls,
			  license_issue_date,
			  license_expiry_date,

			  insurance_key_urls,
			  insurance_amount,
			  insurance_expiry_date,
			  insurance_underwriting_date,
			  is_enable,
			  is_delete,
			  license_number,
    		  insurance_number,
			  create_by
			)
			VALUES
			(
			#{userId},
			#{account},
			#{userName},
			#{email},
			#{phone},


			#{travelRadius},
			#{licenseKeyUrls},
			#{licenseIssueDate},
			#{licenseExpiryDate},

			#{insuranceKeyUrls},
			#{insuranceAmount},
			#{insuranceExpiryDate},
			#{insuranceUnderwritingDate},
			#{isEnable},
			#{isDelete},
			#{licenseNumber},
            #{insuranceNumber},
			#{createBy}
			)
  </insert>

	<update id="update" parameterType="com.bees360.entity.pilot.Pilot">
		UPDATE pilot
		<set>
			<trim suffix="" suffixOverrides=",">
				<if test="userName != null and userName !='' ">
					user_name=#{userName},
				</if>
				<if test="email != null and email !='' ">
					email=#{email},
				</if>
				<if test="phone != null and phone !='' ">
					phone=#{phone},
				</if>
				<if test="travelRadius != null and travelRadius !='' ">
					travel_radius=#{travelRadius},
				</if>
				<if test="licenseKeyUrls != null and licenseKeyUrls !='' ">
					license_key_urls=#{licenseKeyUrls},
				</if>
				<if test="licenseIssueDate != null">
					license_issue_date=#{licenseIssueDate},
				</if>
				<if test="licenseExpiryDate != null">
					license_expiry_date=#{licenseExpiryDate},
				</if>
				<if test="insuranceKeyUrls != null and insuranceKeyUrls !='' ">
					insurance_key_urls=#{insuranceKeyUrls},
				</if>
				<if test="insuranceAmount != null and insuranceAmount !='' ">
					insurance_amount=#{insuranceAmount},
				</if>
				<if test="insuranceExpiryDate != null">
					insurance_expiry_date=#{insuranceExpiryDate},
				</if>
				<if test="insuranceUnderwritingDate != null">
					insurance_underwriting_date=#{insuranceUnderwritingDate},
				</if>
				<if test="isEnable != null and isEnable !='' ">
					is_enable=#{isEnable},
				</if>
				<if test="isDelete != null and isDelete !='' ">
					is_delete=#{isDelete},
				</if>
				<if test="updateBy != null and updateBy !='' ">
					update_by=#{updateBy},
				</if>
			</trim>
		</set>
		WHERE user_id = #{userId}
	</update>


    <update id="updateByMap" parameterType="java.util.Map">
        UPDATE pilot
        <set>
            <trim suffix="" suffixOverrides=",">
                <if test="userName != null">
                    user_name=#{userName},
                </if>
                <if test="email != null">
                    email=#{email},
                </if>
                <if test="phone != null">
                    phone=#{phone},
                </if>
                <if test="travelRadius != null">
                    travel_radius=#{travelRadius},
                </if>
                <if test="licenseKeyUrls != null">
                    license_key_urls=#{licenseKeyUrls},
                </if>
                <if test="licenseIssueDate != null">
                    license_issue_date=#{licenseIssueDate},
                </if>
                <if test="licenseExpiryDate != null">
                    license_expiry_date=#{licenseExpiryDate},
                </if>
                <if test="insuranceKeyUrls != null">
                    insurance_key_urls=#{insuranceKeyUrls},
                </if>
                <if test="insuranceAmount != null">
                    insurance_amount=#{insuranceAmount},
                </if>
                <if test="insuranceExpiryDate != null">
                    insurance_expiry_date=#{insuranceExpiryDate},
                </if>
                <if test="insuranceUnderwritingDate != null">
                    insurance_underwriting_date=#{insuranceUnderwritingDate},
                </if>
                <if test="isEnable != null">
                    is_enable=#{isEnable},
                </if>
                <if test="isDelete != null">
                    is_delete=#{isDelete},
                </if>
                <if test="updateBy != null">
                    update_by=#{updateBy},
                </if>
            </trim>
        </set>
        WHERE user_id = #{userId}
    </update>


</mapper>
