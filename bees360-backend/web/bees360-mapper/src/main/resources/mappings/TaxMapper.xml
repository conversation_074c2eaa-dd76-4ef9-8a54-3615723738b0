<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.TaxMapper">

	<resultMap id="baseMap" type="com.bees360.entity.Tax">
	    <id column="tax_id" jdbcType="INTEGER" property="taxId"/>
	    <result column="tax_name" jdbcType="VARCHAR" property="taxName" />
	    <result column="product_type" jdbcType="INTEGER" property="productType"/>
	    <result column="tax_rate" jdbcType="DOUBLE" property="taxRate"/>
	    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
	    <result column="created_time" jdbcType="BIGINT" property="createdTime"/>
	    <result column="updated_time" jdbcType="BIGINT" property="updatedTime"/>
	</resultMap>

	<sql id="baseColumn" >
		tax_id, tax_name, product_type, tax_rate, is_deleted, created_time, updated_time
	</sql>

	<insert id="insert" useGeneratedKeys="true" keyProperty="taxId">
		insert into Tax(tax_name, product_type, tax_rate, is_deleted, created_time, updated_time)
		values(#{taxName}, #{productType}, #{taxRate}, #{isDeleted}, #{createdTime}, #{updatedTime});
	</insert>

	<select id="getById" resultMap="baseMap">
		select <include refid="baseColumn" /> from Tax where tax_id = #{taxId};
	</select>

	<select id="getByProductType" resultMap="baseMap">
		select <include refid="baseColumn" /> from Tax where product_type = #{productType} and is_deleted = 0;
	</select>

	<select id="list" resultMap="baseMap">
		select <include refid="baseColumn" /> from Tax where is_deleted = 0;
	</select>

	<update id="updateTaxRate">
		update Tax set tax_rate = #{taxRate} where tax_id = #{taxId};
	</update>

	<update id="delete">
		update Tax set is_deleted = 1 where tax_id = #{taxId}
	</update>
</mapper>
