<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.payment.SystemValueMapper">

	<resultMap id="baseSystemValueMap" type="com.bees360.entity.SystemValue">
	    <id column="service_id" jdbcType="BIGINT" property="serviceId"/>
	    <result column="country" jdbcType="VARCHAR" property="country"/>
	    <result column="service_name" jdbcType="VARCHAR" property="serviceName"/>
	    <result column="service_value" jdbcType="DOUBLE" property="serviceValue"/>
	    <result column="label" jdbcType="VARCHAR" property="label"/>
	</resultMap>

  	<sql id="baseSystemValueColumns">
  		service_id,country,service_name,service_value,label
  	</sql>

	<insert id="insert" parameterType="SystemValue" useGeneratedKeys="true" keyProperty="serviceId">
		insert into SystemValue (country,service_name,service_value,label)
		values(#{country},#{serviceName},#{serviceValue},#{label})
	</insert>

	<select id="getAllSystemValues" resultMap="baseSystemValueMap">
		select <include refid="baseSystemValueColumns" /> from SystemValue
	</select>
</mapper>
