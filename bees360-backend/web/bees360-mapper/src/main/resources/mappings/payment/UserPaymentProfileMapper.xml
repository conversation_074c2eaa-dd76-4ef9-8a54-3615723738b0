<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.payment.UserPaymentProfileMapper">

	<resultMap id="baseUserPaymentProfileMap" type="com.bees360.entity.UserPaymentProfile">
	    <id column="user_id" jdbcType="BIGINT" property="userId"/>
	    <result column="discount_percent" jdbcType="DOUBLE" property="discountPercent"/>
	    <result column="free_on_trail_project_num" jdbcType="INTEGER" property="freeOnTrailProjectNum"/>
	    <result column="new_customer_discount_percent" jdbcType="DOUBLE" property="newCustomerDiscountPercent"/>
	    <result column="new_customer_discount_project_num" jdbcType="INTEGER" property="newCustomerDiscountProjectNum"/>
	    <result column="wallet_balance" jdbcType="DOUBLE" property="walletBalance"/>
	    <result column="commission_balance" jdbcType="DOUBLE" property="commissionBalance"/>
	    <result column="currency" jdbcType="VARCHAR" property="currency"/>
	</resultMap>

  	<sql id="baseUserPaymentProfileColumns">
  		user_id,discount_percent,free_on_trail_project_num,wallet_balance,commission_balance,currency,new_customer_discount_percent,new_customer_discount_project_num
  	</sql>

	<update id="updateUserPaymentProfileByBalance" parameterType="UserPaymentProfile">
		update User set wallet_balance = #{walletBalance}, currency = #{currency} where user_id = #{userId}
	</update>

	<update id="updateUserPaymentProfileByCommission" parameterType="UserPaymentProfile">
		update User set commission_balance = #{commissionBalance},currency = #{currency} where user_id = #{userId}
	</update>

	<update id="updateUserDiscountPercent">
		update User set discount_percent = #{discountPercent} where user_id = #{userId}
	</update>

	<update id="updateNewCustomerTrailProjectNumAndDiscount">
		update User set free_on_trail_project_num = #{freeOnTrailProjectNum}, new_customer_discount_percent = #{newCustomerDiscountPercent},
		new_customer_discount_project_num = #{newCustomerDiscountProjectNum} where user_id = #{userId}
	</update>

	<update id="updateFreeOnTrailProjectNum">
		update User set free_on_trail_project_num = #{freeOnTrailProjectNum} where user_id = #{userId}
	</update>

	<update id="subFreeOnTrailProjectNum">
		update User set free_on_trail_project_num = free_on_trail_project_num - #{num} where user_id = #{userId}
	</update>

	<update id="updateNewCustomerDiscountAndProjectNum">
		update User set new_customer_discount_percent = #{newCustomerDiscountPercent},
			new_customer_discount_project_num = #{newCustomerDiscountProjectNum}
		where user_id = #{userId}
	</update>

	<update id="subNewCustomerDiscountProjectNum">
		update User set new_customer_discount_project_num = new_customer_discount_project_num - #{num}
		where user_id = #{userId}
	</update>

	<select id="getUserPaymentProfile" parameterType="long" resultMap="baseUserPaymentProfileMap">
		select <include refid="baseUserPaymentProfileColumns" /> from User where user_id = #{userId}
	</select>

</mapper>
