<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.payment.UserPaymentMapper">

	<resultMap id="baseUserPaymentMap" type="com.bees360.entity.UserPayment">
	    <id column="payment_id" jdbcType="BIGINT" property="paymentId"/>
	    <result column="user_id" jdbcType="BIGINT" property="userId"/>
	    <result column="project_id" jdbcType="BIGINT" property="projectId"/>

	    <result column="payment_method" jdbcType="VARCHAR" property="paymentMethod"/>
	    <result column="operation_type" jdbcType="INTEGER" property="operationType"/>

	    <result column="total_fee_amount" jdbcType="DOUBLE" property="totalFeeAmount" />
	    <result column="paid_service_fee_amount" jdbcType="DOUBLE" property="paidServiceFeeAmount" />
	    <result column="tax" jdbcType="DOUBLE" property="tax" />

	    <result column="channel" jdbcType="INTEGER" property="channel"/>
	    <result column="transaction_no" jdbcType="VARCHAR" property="transactionNo"/>

	    <result column="paid_time" jdbcType="BIGINT" property="paidTime"/>

	    <result column="service_fee_type" jdbcType="INTEGER" property="serviceFeeType"/>
	    <result column="description" jdbcType="VARCHAR" property="description"/>
	</resultMap>

	<resultMap id="userPaymentItemVoMap" type="UserPaymentItemVo">
	    <id column="payment_id" jdbcType="BIGINT" property="paymentId"/>
	    <result column="user_id" jdbcType="BIGINT" property="userId"/>
	    <result column="project_id" jdbcType="BIGINT" property="projectId"/>
	    <result column="total_fee_amount" jdbcType="DOUBLE" property="totalFeeAmount" />
	    <result column="paid_service_fee_amount" jdbcType="DOUBLE" property="paidServiceFeeAmount" />
	    <result column="tax" jdbcType="DOUBLE" property="tax" />

	    <result column="operation_type" jdbcType="VARCHAR" property="operationType"/>
	    <result column="payment_method" jdbcType="VARCHAR" property="paymentMethod"/>

	    <result column="service_fee_type" jdbcType="INTEGER" property="serviceFeeType"/>
	    <result column="paid_time" jdbcType="BIGINT" property="paidTime"/>
	    <result column="description" jdbcType="VARCHAR" property="description"/>
	    <result column="address" jdbcType="VARCHAR" property="address"/>
	</resultMap>

  	<sql id="baseUserPaymentColumns">
  		payment_id, user_id,project_id, payment_method, operation_type,
  		total_fee_amount, paid_service_fee_amount, tax, channel, transaction_no, paid_time,
  		service_fee_type, description
  	</sql>

  	<sql id="userPaymentItemVoColumns">
  		UP.payment_id, UP.user_id, UP.project_id, UP.total_fee_amount, UP.paid_service_fee_amount, UP.tax,
  		UP.operation_type, UP.payment_method, UP.service_fee_type, UP.paid_time, UP.description, P.address
  	</sql>

  	<sql id="baseUserPaymentColumnsForInsert">
  		payment_id, user_id, project_id, payment_method, operation_type,
  		total_fee_amount, paid_service_fee_amount, tax, channel, transaction_no, paid_time,
  		service_fee_type, description
  	</sql>

	<insert id="insert" parameterType="UserPayment" useGeneratedKeys="true" keyProperty="paymentId">
		insert into UserPayment (<include refid="baseUserPaymentColumns" />)
		values(#{paymentId}, #{userId}, #{projectId}, #{paymentMethod}, #{operationType},
			#{totalFeeAmount}, #{paidServiceFeeAmount}, #{tax}, #{channel}, #{transactionNo}, #{paidTime},
			#{serviceFeeType}, #{description}
		)
	</insert>

	<insert id="inserts" parameterType="java.util.List" useGeneratedKeys="true">
	    <selectKey resultType="long" keyProperty="paymentId" order="AFTER">
	    SELECT LAST_INSERT_ID()
	    </selectKey>
	    insert into UserPayment (<include refid="baseUserPaymentColumnsForInsert" />)
	    values
        <foreach collection="list" item="item" separator=",">
	        (#{item.paymentId}, #{item.userId}, #{item.projectId}, #{item.paymentMethod}, #{item.operationType},
				#{item.totalFeeAmount}, #{item.paidServiceFeeAmount}, #{item.tax}, #{item.channel}, #{item.transactionNo}, #{item.paidTime},
				#{item.serviceFeeType}, #{item.description}
			)
        </foreach>
	</insert>

	<select id="getUserPayments" parameterType="long" resultMap="baseUserPaymentMap">
		select <include refid="baseUserPaymentColumns" /> from UserPayment
		where user_id = #{userId} order by payment_id desc
	</select>

	<select id="listUserPaymentsByProjectId" resultMap="baseUserPaymentMap">
	   select <include refid="baseUserPaymentColumns" /> from UserPayment
	   where project_id = #{projectId}
	   <if test="userId != null">
	       and user_id = #{userId}
	   </if>
       order by payment_id desc
	</select>

	<select id="getUserPaymentsByProjectId" resultMap="baseUserPaymentMap">
		select <include refid="baseUserPaymentColumns" /> from UserPayment
		<where>
			<trim prefix="" prefixOverrides="and">
				<if test="userId != 0">
					user_id = #{userId}
				</if>
				<if test="projectId != 0">
					and project_id = #{projectId}
				</if>
			</trim>
		</where>
		order by payment_id desc
	</select>

	<select id="count" parameterType="long" resultType="Integer">
		select count(*) from UserPayment where user_id = #{userId}
	</select>

	<select id="listPayments" resultMap="baseUserPaymentMap">
		select <include refid="baseUserPaymentColumns" /> from UserPayment
		where user_id = #{userId}
		<if test="paidTimeStart != null">
		 	<![CDATA[
				and paid_time >= #{paidTimeStart}
			]]>
		</if>
		<if test="paidTimeEnd != null">
			<![CDATA[
				and paid_time <= #{paidTimeEnd}
			]]>
		</if>
		order by payment_id desc
	</select>
</mapper>
