<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.EventHistoryMapper">

	<resultMap id="baseEventHistoryMap" type="com.bees360.entity.EventHistory">
	    <id column="event_id" jdbcType="BIGINT" property="eventId"/>
	    <result column="project_id" jdbcType="BIGINT" property="projectId"/>
	    <result column="user_id" jdbcType="BIGINT" property="userId"/>
	    <result column="status" jdbcType="INTEGER" property="status"/>
	    <result column="status_time" jdbcType="BIGINT" property="statusTime"/>
	    <result column="description" jdbcType="VARCHAR" property="description"/>
	    <result column="created_time" jdbcType="BIGINT" property="createdTime"/>
	    <result column="modified_by" jdbcType="BIGINT" property="modifiedBy"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
    </resultMap>

	<resultMap id="HistoryLogMap" type="com.bees360.entity.vo.HistoryLogVo">
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="user" jdbcType="VARCHAR" property="user"/>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"/>
        <result column="status_time" jdbcType="BIGINT" property="statusTime"/>
        <result column="created_time" jdbcType="BIGINT" property="createdTime"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
    </resultMap>

    <sql id="baseSql">event_id, project_id, user_id, `status`, status_time, description, created_time, modified_by, title</sql>

	 <select id="existStatus" resultType="java.lang.Boolean">
	 	select count(*) > 0 from EventHistory where project_id = #{projectId} and status = #{status};
	 </select>

	 <select id="listAll" resultMap="baseEventHistoryMap">
	   select * from EventHistory where project_id = #{projectId} order by created_time
	 </select>

	 <select id="listHistoryLog" resultMap="HistoryLogMap">
	   select H.project_id, H.status, H.user_id, H.modified_by as modified_by,
	       H.user_id as user, H.status_time, H.created_time, H.title, H.description from EventHistory H
			   where H.project_id = #{project_id} order by H.created_time desc
	 </select>

	 <select id="listHistoryIn" resultMap="baseEventHistoryMap">
	   select * from EventHistory where project_id = #{projectId} and status in
	  <foreach collection="statuses" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
	 </select>

	 <select id="getByStatus" resultMap="baseEventHistoryMap">
	   select * from EventHistory where project_id = #{projectId} and status = #{status} order by created_time desc limit 1;
	 </select>

    <select id="listEventHistory" resultMap="baseEventHistoryMap">
        select <include refid="baseSql"/> from EventHistory
        <where>
            <if test="projectId != null">
                project_id = #{projectId}
            </if>
            <if test="status != null">
                and `status` = #{status}
            </if>
            <if test="projectIdList != null and projectIdList.size() > 0">
                and project_id in (
                <foreach collection="projectIdList" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and `status` in (
                <foreach collection="statusList" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </where>
        order by created_time desc;
    </select>

    <insert id="insert" parameterType="EventHistory" useGeneratedKeys="true" keyProperty="eventId">
    	insert into EventHistory
    	(project_id, user_id, status, status_time, description, created_time, modified_by, title)
    	values
    	(#{projectId}, #{userId}, #{status}, #{statusTime}, #{description}, #{createdTime}, #{modifiedBy}, #{title})
	</insert>

    <select id="getLastEventHistory"  resultMap="baseEventHistoryMap">
        select <include refid="baseSql"/> from EventHistory
            where project_id = #{projectId}
        order by created_time desc limit 1
    </select>
</mapper>
