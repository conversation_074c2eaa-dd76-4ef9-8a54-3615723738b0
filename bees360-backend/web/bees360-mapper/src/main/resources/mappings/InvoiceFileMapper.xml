<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.InvoiceFileMapper">
	<resultMap id="invoiceFileMap"
		type="com.bees360.entity.InvoiceFile">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="project_id" jdbcType="BIGINT" property="projectId" />
		<result column="invoice_id" jdbcType="BIGINT" property="invoiceId" />
		<result column="invoice_pdf_url" jdbcType="VARCHAR" property="invoicePdfUrl" />
		<result column="created_time" jdbcType="BIGINT" property="createdTime" />
		<result column="is_deleted" jdbcType="INTEGER" property="deleted" />
	</resultMap>

	<sql id="invoiceFileColumnts">
		id, project_id, invoice_id, invoice_pdf_url, created_time, is_deleted
	</sql>
	<insert id="insert">
		insert into InvoiceFile(project_id, invoice_id, invoice_pdf_url, created_time, is_deleted)
		values (#{projectId}, #{invoiceId}, #{invoicePdfUrl}, #{createdTime}, #{isDeleted});
	</insert>

	<update id="delete">
		update InvoiceFile set is_deleted = 1 where id = #{id};
	</update>

	<update id="deleteByProjectIdAndInvoiceId">
		update InvoiceFile set is_deleted = 1 where project_id = #{projectId} and invoice_id = #{invoiceId};
	</update>

	<delete id="deleteInvoiceFileForProject">
		update InvoiceFile set is_deleted = 1 where project_id = #{projectId} and invoice_id is null;
	</delete>

	<select id="listByProjectId" resultMap="invoiceFileMap">
		select <include refid="invoiceFileColumnts" /> from InvoiceFile
		where project_id = #{projectId} and is_deleted = 0;
	</select>

	<select id="getById" resultMap="invoiceFileMap">
		select <include refid="invoiceFileColumnts" /> from InvoiceFile
		where id = #{id};
	</select>

	<select id="getInvoiceFileForProject" resultMap="invoiceFileMap">
		select <include refid="invoiceFileColumnts" /> from InvoiceFile
		where project_id = #{projectId} and invoice_id is null and is_deleted = 0;
	</select>

	<select id="getByProjectIdAndInvoiceId" resultMap="invoiceFileMap">
		select <include refid="invoiceFileColumnts" /> from InvoiceFile
		where project_id = #{projectId} and invoice_id = #{invoiceId} and is_deleted = 0;
	</select>

	<select id="getByProjectIdAndId" resultMap="invoiceFileMap">
		select <include refid="invoiceFileColumnts" /> from InvoiceFile
		where project_id = #{projectId} and id = #{id} and is_deleted = 0;
	</select>
</mapper>
