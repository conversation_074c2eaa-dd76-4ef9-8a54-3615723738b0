<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ProjectSyncLogMapper">


	<resultMap id="baseResultMap"
		type="com.bees360.entity.ProjectSyncLog">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
		<result column="sync_event" jdbcType="VARCHAR" property="syncEvent" />
		<result column="status" jdbcType="INTEGER" property="status" />
	</resultMap>

	<sql id="dataColumns">
	   id, project_id, sync_event, status
	</sql>

    <select id="getByProjectId" resultMap="baseResultMap">
    	select <include refid="dataColumns" /> from ProjectSyncLog where project_id = #{projectId};
	</select>

    <select id="existedSync" resultType="java.lang.Boolean">
	 	select count(*) > 0 from ProjectSyncLog where project_id = #{projectId} and status != -1
	 	    <if test="event != null and event.length() > 0">
	 	      and sync_event = #{event}
            </if>;
	 </select>

    <select id="successSync" resultType="java.lang.Boolean">
        select count(*) > 0 from ProjectSyncLog where project_id = #{projectId} and status = 1
        <if test="event != null and event.length() > 0">
            and sync_event = #{event}
        </if>;
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into ProjectSyncLog(project_id, sync_event, status)
        values (#{param.projectId}, #{param.syncEvent}, #{param.status})
    </insert>

    <update id="updateStatus">
        update ProjectSyncLog set status = #{status}
        where id = #{id}
    </update>


</mapper>
