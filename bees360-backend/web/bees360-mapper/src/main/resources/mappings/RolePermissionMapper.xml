<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
[!important] each sql should consider a user can play multiple role in a project which will cause many repeated records.

 -->

<mapper namespace="com.bees360.mapper.RolePermissionMapper">

	<sql id="rolePermissionColumns">
		role_id, permission_id
	</sql>

	<sql id="rolePermissionMappingColumns">
		RP.role_id, RP.permission_id ,P.parent_id,
		P.operation_id, P.permission_type, P.display
	</sql>

	<select id="getByRoleId" resultType="rolePermission">
  		select <include refid="rolePermissionMappingColumns" /> from RolePermission RP LEFT JOIN Permission P
  		ON RP.permission_id = P.permission_id
  		where RP.role_id = #{roleId}
  	</select>

  	<select id="getByRoleIds"  resultType="rolePermission">
  		select <include refid="rolePermissionMappingColumns" /> from RolePermission RP LEFT JOIN Permission P
  		ON RP.permission_id = P.permission_id
  		where RP.role_id in
  		<foreach collection="roleIds" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
  	</select>
</mapper>
