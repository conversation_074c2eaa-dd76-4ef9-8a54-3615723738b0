<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.label.ProjectLabelMapper">

    <resultMap id="projectLabelMap" type="com.bees360.entity.label.ProjectLabel">
        <id column="id" jdbcType="BIGINT" property="labelId"/>
        <result column="label_name" jdbcType="VARCHAR" property="labelName"/>
        <result column="label_desc" jdbcType="VARCHAR" property="labelDesc"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="label_type" jdbcType="VARCHAR" property="labelType"/>
    </resultMap>

    <resultMap id="boundProjectLabelMap" type="com.bees360.entity.label.BoundProjectLabel">
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <collection property="projectLabels" resultMap="projectLabelMap"/>
    </resultMap>

    <sql id="projectLabelColumn">
        id, label_name, label_desc, create_at, label_type
    </sql>

    <insert id="insert" parameterType="com.bees360.entity.label.ProjectLabel">
        insert into ProjectLabel(label_name, label_desc) values (#{labelName}, #{labelDesc})
    </insert>

    <select id="listLabels" resultMap="projectLabelMap">
        select <include refid="projectLabelColumn"/>
        from ProjectLabel
    </select>

    <select id="getProjectLabels" resultMap="boundProjectLabelMap">
        select
            B.project_id, B.create_at, L.id, L.label_name, L.label_desc
        from ProjectLabelBind B
        join ProjectLabel L on B.label_id = L.id
        <where>
            <if test="projectIds != null and projectIds.size() > 0">
                and B.project_id in
                <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                    #{projectId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getByLabelIdAndCreatedTime" resultMap="boundProjectLabelMap">
        select project_id, label_id, create_at from ProjectLabelBind where label_id = #{labelId} and
        create_at between #{startTime} and #{endTime}
    </select>

    <insert id="markLabelForProjects">
        insert into ProjectLabelBind(project_id, label_id)
        values
        <foreach collection="projectIds" item="projectId" separator=",">
            (#{projectId}, #{labelId})
        </foreach>
    </insert>

    <insert id="markLabelsForProject">
        insert into ProjectLabelBind(project_id, label_id)
        values
        <foreach collection="labelIds" item="labelId" separator=",">
            (#{projectId}, #{labelId})
        </foreach>
    </insert>

    <delete id="eraseLabelForProjects">
        delete from ProjectLabelBind
        <where>
            <if test="labelId != null">
                and label_id = #{labelId}
            </if>
            <if test="projectIds != null and projectIds.size() > 0">
                and project_id in
                <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                    #{projectId}
                </foreach>
            </if>
        </where>
    </delete>

    <delete id="eraseLabelsForProject">
        delete from ProjectLabelBind
        <where>
            <if test="projectId != null">
                and project_id = #{projectId}
            </if>
            <if test="labelIds != null and labelIds.size() > 0">
                and label_id in
                <foreach collection="labelIds" item="labelId" open="(" close=")" separator=",">
                    #{labelId}
                </foreach>
            </if>
        </where>
    </delete>

    <delete id="eraseAllForProject">
        delete from ProjectLabelBind where project_id = #{projectId}
    </delete>
</mapper>
