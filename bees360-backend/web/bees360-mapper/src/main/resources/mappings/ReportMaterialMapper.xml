<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ReportMaterialMapper">


    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
		insert into ReportMaterial(material_type, position, data_source, check_is_implement, is_show_damage_type, size, is_bold,
		color, align, material_url, width, height, material_num)
		values(#{materialType}, Point(#{positionX}, #{positionY}), #{dataSource}, #{checkIsImplement}, #{isShowDamageType},
		#{size}, #{isBold}, #{color}, #{align}, #{materialUrl}, #{width}, #{height},  #{materialNum});
	</insert>
</mapper>
