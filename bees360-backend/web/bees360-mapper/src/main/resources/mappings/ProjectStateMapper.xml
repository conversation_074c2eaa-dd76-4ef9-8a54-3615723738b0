<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bees360.mapper.ProjectStateMapper">
    <insert id="save">
        INSERT INTO project_state(project_id, project_state, project_state_reason, project_state_reason_id, updated_at)
        VALUE (#{projectId}, #{projectState}, #{projectStateReason}, #{projectStateReasonId}, #{updatedAt})
        ON DUPLICATE KEY UPDATE
        project_state = IF(updated_at IS NULL <![CDATA[ or updated_at < #{updatedAt} ]]>, #{projectState}, project_state),
        project_state_reason = IF(updated_at IS NULL <![CDATA[ or updated_at < #{updatedAt} ]]>, #{projectStateReason}, project_state_reason),
        project_state_reason_id = IF(updated_at IS NULL <![CDATA[ or updated_at < #{updatedAt} ]]>, #{projectStateReasonId}, project_state_reason_id),
        updated_at = IF(updated_at IS NULL <![CDATA[ or updated_at < #{updatedAt} ]]>, #{updatedAt}, updated_at)
    </insert>

    <select id="findProjectIdByChangeReasonIds" resultType="java.lang.String">
        SELECT DISTINCT project_id FROM project_state
        WHERE project_state_reason_id in
        <foreach collection="changeReasonIds" item="changeReasonId" index="index" open="(" close=")" separator=",">
            #{changeReasonId}
        </foreach>
    </select>
</mapper>
