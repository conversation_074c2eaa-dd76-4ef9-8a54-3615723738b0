<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.HouseImageSegmentTypeMapper">
  	<resultMap id="baseHouseImageSegmentType" type="com.bees360.entity.HouseImageSegmentType">
	    <id column="id" jdbcType="BIGINT" property="id"/>
	    <result column="name" jdbcType="VARCHAR" property="name"/>
	    <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
	    <result column="value_type" jdbcType="BIGINT" property="valueType"/>
	    <result column="code_type" jdbcType="BIGINT" property="codeType"/>
	    <result column="is_leaf" jdbcType="BIGINT" property="isLeaf"/>
		<result column="unit" jdbcType="VARCHAR" property="unit"/>
	</resultMap>

  	<sql id="SegmentTypeColumns">
		id, name, parent_id, value_type, code_type, is_leaf, unit
	</sql>

  	<sql id="segmentRootType">10</sql>

  	<select id="getImageSegmentTypes" resultMap="baseHouseImageSegmentType">
  		select <include refid="SegmentTypeColumns" /> from HouseImageSegmentType ;
  	</select>
</mapper>
