<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.AdjusterMapper">
	<resultMap id="adjusterMapper"
		type="com.bees360.entity.Adjuster">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="operating_city_state" jdbcType="VARCHAR" property="operatingCityState" />
		<result column="additional_operating_territories" jdbcType="VARCHAR" property="additionalOperatingTerritories" />
		<result column="designated_home_state_license" jdbcType="VARCHAR" property="designatedHomeStateLicense" />
		<result column="additional_license" jdbcType="VARCHAR" property="additionalLicense" />
		<result column="license_files" jdbcType="VARCHAR" property="licenseFiles" />
		<result column="years_of_experience" jdbcType="INTEGER" property="yearsOfExperience" />
        <result column="more_than_100miles_traveled" jdbcType="INTEGER" property="moreThan100MilesTraveled" />
        <result column="cat_event_deployed" jdbcType="INTEGER" property="catEventDeployed" />
        <result column="ai_roster_id" jdbcType="BIGINT" property="aiRosterId" />
        <result column="active_license_state" jdbcType="VARCHAR" property="activeLicenseState" />
        <result column="certifications" jdbcType="VARCHAR" property="certifications" />
        <result column="field_experience" jdbcType="VARCHAR" property="fieldExperience" />
        <result column="desk_experience" jdbcType="VARCHAR" property="deskExperience" />
        <result column="commercial_experience" jdbcType="VARCHAR" property="commercialExperience" />
        <result column="xactimate_platform_experience" jdbcType="VARCHAR" property="xactimatePlatformExperience" />
        <result column="symbility_platform_experience" jdbcType="VARCHAR" property="symbilityPlatformExperience" />
        <result column="other_platform_experience" jdbcType="VARCHAR" property="otherPlatformExperience" />
	</resultMap>

	<sql id="adjusterColumn">
	   id, user_id, operating_city_state, additional_operating_territories,
       designated_home_state_license, additional_license, license_files,
       years_of_experience, more_than_100miles_traveled,
       cat_event_deployed, ai_roster_id,
       active_license_state, certifications, field_experience, desk_experience,
       commercial_experience, xactimate_platform_experience,
       symbility_platform_experience, other_platform_experience
	</sql>

	<select id="listAll" resultMap="adjusterMapper">
	    select <include refid="adjusterColumn" /> from Adjuster;
	</select>

    <select id="getByUserId" resultMap="adjusterMapper">
        select <include refid="adjusterColumn" /> from Adjuster where user_id = #{userId};
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert Adjuster(user_id, operating_city_state, additional_operating_territories,
        designated_home_state_license, additional_license, license_files, years_of_experience,
        more_than_100miles_traveled,cat_event_deployed, ai_roster_id, active_license_state,
        field_experience, desk_experience,commercial_experience, certifications,
        xactimate_platform_experience, symbility_platform_experience,other_platform_experience)
        values
        (#{userId}, #{operatingCityState}, #{additionalOperatingTerritories},
        #{designatedHomeStateLicense}, #{additionalLicense}, #{licenseFiles}, #{yearsOfExperience}, #{moreThan100MilesTraveled},
        #{catEventDeployed}, #{aiRosterId}, #{activeLicenseState}, #{fieldExperience}, #{deskExperience},
        #{commercialExperience}, #{certifications},
        #{xactimatePlatformExperience}, #{symbilityPlatformExperience}, #{otherPlatformExperience});
    </insert>
</mapper>
