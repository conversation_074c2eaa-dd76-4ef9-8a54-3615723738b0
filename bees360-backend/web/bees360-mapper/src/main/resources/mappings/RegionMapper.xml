<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.RegionMapper">

	<resultMap id="baseRegionMap" type="com.bees360.entity.Region">
	    <id column="id" jdbcType="BIGINT" property="id"/>
	    <result column="country_id" jdbcType="VARCHAR" property="countryId"/>
	    <result column="region_name" jdbcType="VARCHAR" property="regionName"/>
	</resultMap>

	<select id="listRegionWithPrefix" resultType="java.lang.String">
		select region_name from Region
		<if test="regex != null and regex.length() > 0">
		  where lower(region_name) like #{regex}
		</if>
		order by region_name
		<if test="limit >= 0">
		  limit #{limit}
		</if>
	</select>
</mapper>
