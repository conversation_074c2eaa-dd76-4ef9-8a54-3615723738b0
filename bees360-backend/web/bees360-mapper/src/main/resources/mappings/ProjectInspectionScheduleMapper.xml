<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bees360.mapper.ProjectInspectionScheduleMapper">

    <resultMap id="baseResultMap" type="com.bees360.entity.ProjectInspectionSchedule">
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="scheduled_time" jdbcType="BIGINT" property="scheduledTime"/>
        <result column="due_date" jdbcType="DATE" property="dueDate"/>
    </resultMap>

    <sql id="baseColumns">
        project_id, scheduled_time, due_date
    </sql>

    <insert id="save" parameterType="com.bees360.entity.ProjectInspectionSchedule">
        INSERT INTO project_inspection_schedule(project_id, scheduled_time, due_date)
        VALUE (#{projectId}, #{scheduledTime}, #{dueDate})
        ON DUPLICATE KEY UPDATE scheduled_time = #{scheduledTime}, due_date = #{dueDate}
    </insert>

    <update id="updateScheduledTime">
        INSERT INTO project_inspection_schedule(project_id, scheduled_time)
        VALUE (#{projectId}, #{scheduledTime})
        ON DUPLICATE KEY UPDATE scheduled_time = #{scheduledTime}
    </update>

    <update id="updateDueDate">
        INSERT INTO project_inspection_schedule(project_id, due_date)
        VALUE (#{projectId}, #{dueDate})
        ON DUPLICATE KEY UPDATE due_date = #{dueDate}
    </update>

    <select id="getByProjectId" resultMap="baseResultMap">
        select <include refid="baseColumns" /> from project_inspection_schedule where project_id = #{projectId};
    </select>
</mapper>
