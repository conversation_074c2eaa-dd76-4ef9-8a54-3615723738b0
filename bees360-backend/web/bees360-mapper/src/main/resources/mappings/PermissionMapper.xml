<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
[!important] each sql should consider a user can play multiple role in a project which will cause many repeated records.

 -->

<mapper namespace="com.bees360.mapper.PermissionMapper">

	<resultMap id="baseResultMap" type="Permission">
	    <id column="permission_id" jdbcType="BIGINT" property="permissionId"/>
	    <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
	    <result column="operation_id" jdbcType="INTEGER" property="operationId"/>
	    <result column="permission_number" jdbcType="VARCHAR" property="permissionNumber"/>
	    <result column="permission_type" jdbcType="INTEGER" property="permissionType"/>
	    <result column="display" jdbcType="VARCHAR" property="display"/>
	</resultMap>

	<resultMap id="parentJoinResultMap" type="Permission">
	    <id property="permissionId" column="permission_id"/>
	    <result property="parentId"  column="parent_id" />
	    <result property="operationId"  column="operation_id" />
	    <result property="permissionNumber"  column="permission_number"/>
	    <result property="permissionType" column="permission_type"/>
	    <result property="display"  column="display"/>

	    <association property="parent" javaType="Permission" column="parentId">
			<id property="permissionId" column="p_permission_id"/>
			<result property="parentId"  column="p_parent_id" />
		    <result property="operationId" column="p_operation_id"/>
		    <result property="permissionNumber" column="p_permission_number"/>
		    <result property="permissionType" column="p_permission_type" />
		    <result property="display" column="p_display"/>
	    </association>
	</resultMap>

	<sql id="permissionColumns">
		permission_id, parent_id, operation_id, permission_number, permission_type, display
	</sql>

	<sql id="permissionParentJoinColumns">
		C.permission_id, C.parent_id, C.operation_id, C.permission_number, C.permission_type, C.display,
		P.permission_id as p_permission_id, P.parent_id as p_parent_id, P.operation_id as p_operation_id,
		P.permission_number as p_permission_number, P.permission_type as p_permission_type, P.display as p_display
	</sql>

  	<select id="getAllPermissions" resultMap="baseResultMap">
		select <include refid="permissionColumns" /> from Permission
	</select>

  	<select id="getById" parameterType="long" resultMap="parentJoinResultMap">
		select <include refid="permissionParentJoinColumns" />
		from Permission C left join Permission P on C.parent_id = P.permission_id
		where C.permission_id = #{permissionId}
	</select>

	<select id="getByPId" parameterType="long" resultMap="parentJoinResultMap">
		select <include refid="permissionParentJoinColumns" /> from Permission
		where parent_id = #{parentId}
	</select>

	<select id="getByPermissionIds"  resultMap="parentJoinResultMap">
  		select <include refid="permissionParentJoinColumns" />
  		from Permission C left join Permission P on C.parent_id = P.permission_id
  		where C.permission_id in
  		<foreach collection="permissionIds" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
  	</select>
</mapper>
