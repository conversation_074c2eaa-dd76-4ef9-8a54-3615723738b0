<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ProductMapper">

	<resultMap id="baseMap" type="com.bees360.entity.Product">
		<id column="product_id" jdbcType="BIGINT" property="productId"/>
		<result column="product_type" jdbcType="INTEGER" property="productType"/>
		<result column="internal_type" jdbcType="INTEGER" property="internalType"/>
		<result column="price_type" jdbcType="INTEGER" property="priceType"/>
		<result column="price" jdbcType="INTEGER" property="price"/>
		<result column="subtitle" jdbcType="VARCHAR" property="subtitle" />
		<result column="caption" jdbcType="VARCHAR" property="caption" />
		<result column="url" jdbcType="VARCHAR" property="url" />
		<result column="created_time" jdbcType="BIGINT" property="createdTime" />
		<result column="updated_time" jdbcType="BIGINT" property="updatedTime" />
	</resultMap>

	<sql id="baseColumn">
		product_id, product_type, internal_type, price_type, price,
		subtitle, caption, url, created_time, updated_time
	</sql>

    <update id="updateProductInfo">
		update Product set price_type = #{priceType}, price = #{price}, subtitle = #{subtitle},
			caption = #{caption}, url = #{url}, updated_time = #{updatedTime}
			where product_id = #{productId};
	</update>

    <select id="list" resultMap="baseMap">
		select <include refid="baseColumn" /> from Product
		<if test="productType != null" >
			where product_type = #{productType}
		</if>
		order by product_type, internal_type;
	</select>

	<select id="getById" resultMap="baseMap">
		select <include refid="baseColumn" /> from Product
		where product_id = #{productId};
	</select>
</mapper>
