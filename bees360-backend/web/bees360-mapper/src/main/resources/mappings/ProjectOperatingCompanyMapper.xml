<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
[!important] each sql should consider a user can play multiple role in a project which will cause many repeated records.

 -->

<mapper namespace="com.bees360.mapper.ProjectOperatingCompanyMapper">
    <select id="getByProjectId" resultType="String">
        select operating_company from project_operating_company
        where project_id = #{projectId};
    </select>

    <insert id="insert" parameterType="Project">
        insert into project_operating_company
        (operating_company, project_id)
        values
        (#{operatingCompany}, #{projectId})
        on duplicate key update operating_company = #{operatingCompany};
    </insert>
</mapper>
