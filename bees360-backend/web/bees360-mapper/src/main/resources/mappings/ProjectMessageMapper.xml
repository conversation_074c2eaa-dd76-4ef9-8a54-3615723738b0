<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ProjectMessageMapper">

	<resultMap id="baseResultMap" type="com.bees360.entity.ProjectMessage">
	    <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="sender_id" jdbcType="BIGINT" property="senderId"/>
	    <result column="title" jdbcType="VARCHAR" property="title"/>
	    <result column="content" jdbcType="VARCHAR" property="content"/>
	    <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="is_deleted" javaType="BOOLEAN" property="deleted"/>
        <result column="type" javaType="INTEGER" property="type"/>
    </resultMap>

    <sql id="baseColumn">
      `id`, `project_id`, `sender_id`, `title`, `content`, `create_time`, `is_deleted`, `type`
    </sql>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into ProjectMessage(project_id, sender_id, title, content, create_time, is_deleted,type)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.projectId, jdbcType=BIGINT}, #{item.senderId, jdbcType=BIGINT},
            #{item.title, jdbcType=VARCHAR}, #{item.content, jdbcType=VARCHAR},
            #{item.createTime, jdbcType=BIGINT}, #{item.deleted,javaType=BOOLEAN},
            #{item.type,javaType=INTEGER})
        </foreach>
    </insert>
    <insert id="insert" parameterType="ProjectMessage" useGeneratedKeys="true" keyProperty="id">
        insert into ProjectMessage(project_id, sender_id, title, content, create_time, is_deleted,type)
        values (#{projectId, jdbcType=BIGINT}, #{senderId, jdbcType=BIGINT},
        #{title, jdbcType=VARCHAR}, #{content, jdbcType=VARCHAR},
        #{createTime, jdbcType=BIGINT}, #{deleted,javaType=BOOLEAN},
        #{type,javaType=INTEGER})
    </insert>

    <select id="listMessage" parameterType="ProjectMessageQuery" resultMap="baseResultMap">
        select <include refid="baseColumn"/>
        from ProjectMessage
        <where>
            <if test="query.projectId != null">
                project_id = #{query.projectId,jdbcType=BIGINT}
            </if>
            <if test="query.projectIds != null and query.projectIds.size() > 0">
                and project_id in
                <foreach collection="query.projectIds" item="projectId" separator="," open="(" close=")">
                    #{projectId}
                </foreach>
            </if>
            <if test="query.senderId != null">
                and sender_id = #{query.senderId,jdbcType=BIGINT}
            </if>
            <if test="query.isDeleted != null">
                and is_deleted = #{query.isDeleted,jdbcType=BOOLEAN}
            </if>
            <if test="query.type != null">
                and type = #{query.type,jdbcType=INTEGER}
            </if>
        </where>
        order by create_time desc;
    </select>

    <select id="getLatestMessage" parameterType="ProjectMessageQuery" resultMap="baseResultMap">
        select <include refid="baseColumn"/>
        from ProjectMessage
        <where>
            <if test="query.projectId != null">
                project_id = #{query.projectId,jdbcType=BIGINT}
            </if>
            <if test="query.senderId != null">
                and sender_id = #{query.senderId,jdbcType=BIGINT}
            </if>
            <if test="query.isDeleted != null">
                and is_deleted = #{query.isDeleted,jdbcType=BOOLEAN}
            </if>
            <if test="query.type != null">
                and type = #{query.type,jdbcType=INTEGER}
            </if>
            order by create_time desc limit 1
        </where>
    </select>

    <delete id="delete"  parameterType="ProjectMessageQuery">
        delete from ProjectMessage
        <where>
            project_id = #{query.projectId,jdbcType=BIGINT}
            <if test="query.senderId != null"> and sender_id = #{query.senderId,jdbcType=BIGINT}</if>
            <if test="query.type != null">and type = #{query.type,jdbcType=INTEGER}</if>
        </where>
    </delete>
    <select id="getByPrimaryKey" resultMap="baseResultMap">
        select <include refid="baseColumn"/>
        from ProjectMessage
        where id = #{id}
    </select>
</mapper>
