<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bees360.mapper.SystemConfigMapper">

    <resultMap id="baseResultMap" type="com.bees360.entity.SystemConfig">
        <id column="config_key" jdbcType="VARCHAR" property="configKey" />
        <result column="config_value" jdbcType="VARCHAR" property="configValue" />
    </resultMap>

    <insert id="insert">
        insert into SystemConfig(config_key, config_value) values(#{configKey}, #{configValue});
    </insert>

    <insert id="insertBatch">
        insert into SystemConfig(config_key, config_value) values
        <foreach collection="list" separator="," item="item">
            (#{item.configKey}, #{item.configValue})
        </foreach>
    </insert>

    <select id="listAll" resultMap="baseResultMap">
        select * from SystemConfig;
    </select>

    <select id="listWithPrefix" resultMap="baseResultMap">
        select * from SystemConfig where config_key like concat(#{prefix}, '%');
    </select>

    <select id="getOne" resultMap="baseResultMap">
        select * from SystemConfig where config_key = #{configKey};
    </select>

    <update id="update">
        update SystemConfig set config_value = #{configValue} where config_key = #{configKey};
    </update>

    <update id="updateBatch">
        update SystemConfig set config_value = CASE config_key
            <foreach collection="list" item="item">
                WHEN #{item.configKey} THEN #{item.configValue}
            </foreach>
        END
        where config_key in
        <foreach collection="list" open="(" item="item" separator="," close=")">
            #{item.configKey}
        </foreach>
    </update>
</mapper>
