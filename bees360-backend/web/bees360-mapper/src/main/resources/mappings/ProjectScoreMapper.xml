<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ProjectScoreMapper">
    <resultMap id="projectScoreMap" type="com.bees360.entity.ProjectScore">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
    </resultMap>

    <sql id="baseColumns">
        id, project_id, score, create_at, update_at
    </sql>

    <insert id="insert" parameterType="com.bees360.entity.ProjectScore">
        insert into ProjectScore(project_id, score) values (#{projectId}, #{score})
    </insert>

    <update id="update" parameterType="com.bees360.entity.ProjectScore">
        update ProjectScore
        <set>
            <if test="score != null">
                score = #{score},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getByProjectId" resultMap="projectScoreMap">
        select <include refid="baseColumns"/>
        from ProjectScore
        where project_id = #{projectId}
    </select>

    <select id="listByProjectIds" resultMap="projectScoreMap">
        select <include refid="baseColumns"/>
        from ProjectScore
        <where>
            <if test="projectIds != null and projectIds.size() > 0">
                project_id in
                <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                    #{projectId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
