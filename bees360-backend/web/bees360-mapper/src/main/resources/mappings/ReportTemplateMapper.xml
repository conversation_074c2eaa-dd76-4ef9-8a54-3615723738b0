<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ReportTemplateMapper">
	<resultMap id="baseReportTemplateMap" type="com.bees360.entity.vo.ReportTemplateVo">
	    <id column="id" jdbcType="BIGINT" property="id"/>
	    <result column="company_id" jdbcType="BIGINT" property="companyId"/>
	    <result column="material_id" jdbcType="BIGINT" property="materialId"/>
	    <result column="report_types" jdbcType="VARCHAR" property="reportTypes"/>
	    <result column="page_number" jdbcType="INTEGER" property="pageNumber"/>
	    <result column="page_type" jdbcType="INTEGER" property="pageType"/>
	    <result column="sort" jdbcType="INTEGER" property="sort"/>
	    <result column="material_type" jdbcType="INTEGER" property="materialType"/>
	    <result column="point_x" jdbcType="DOUBLE" property="pointX"/>
	    <result column="point_y" jdbcType="DOUBLE" property="pointY"/>
	    <result column="data_source" jdbcType="VARCHAR" property="dataSource"/>
	    <result column="check_is_implement" jdbcType="VARCHAR" property="checkIsImplement"/>
	    <result column="is_show_damage_type" jdbcType="INTEGER" property="isShowDamageType"/>
	    <result column="size" jdbcType="INTEGER" property="size"/>
	    <result column="is_bold" jdbcType="INTEGER" property="isBold"/>
	    <result column="color" jdbcType="VARCHAR" property="color"/>
	    <result column="align" jdbcType="INTEGER" property="align"/>
	    <result column="material_url" jdbcType="VARCHAR" property="materialUrl"/>
	    <result column="width" jdbcType="INTEGER" property="width"/>
	    <result column="height" jdbcType="INTEGER" property="height"/>
	    <result column="material_num" jdbcType="INTEGER" property="materialNum"/>
	</resultMap>

    <sql id="frontTemplate">0</sql>

	<sql id="reportTemplateColumns">
		rt.id, rt.company_id, rt.material_id, rt.report_types, rt.page_number, rt.page_type, rt.sort,
		rm.material_type, ST_X(rm.position) as point_x, ST_Y(rm.position) as point_y, rm.data_source,
		rm.check_is_implement, rm.is_show_damage_type, rm.size, rm.is_bold, rm.color, rm.align,
		rm.material_url, rm.width, rm.height, rm.material_num
	</sql>
    <sql id="reportTemplateInsertColumns">
        company_id, material_id, report_types, page_number, page_type, sort, version, create_time, is_delete
    </sql>
    <sql id="reportTemplateInsertValueColumns">
        #{item.companyId}, #{item.materialId}, #{item.reportTypes}, #{item.pageNumber}, #{item.pageType},
        #{item.sort}, #{item.version}, #{item.createTime}, #{item.isDeleted}
    </sql>

	<select id="listTemplateByCompanyIdAndReportType" resultMap="baseReportTemplateMap">
		select <include refid="reportTemplateColumns" />
			from ReportTemplate rt left join ReportMaterial rm on rt.material_id = rm.id
			where find_in_set(#{reportType}, rt.report_types)>0 and rm.material_type = <include refid="frontTemplate" />
					and rt.is_delete = 0 and rt.company_id = #{companyId}
			order by rt.page_number, rt.sort asc;
	</select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
		insert into ReportTemplate(<include refid="reportTemplateInsertColumns" />)
		values(#{companyId}, #{materialId}, #{reportTypes}, #{pageNumber}, #{pageType}, #{sort}, #{version},
		 #{createTime}, #{isDeleted});
	</insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into ReportTemplate (<include refid="reportTemplateInsertColumns" />)
        values
        <foreach collection="params" item="item" index="index" separator=",">
            (<include refid="reportTemplateInsertValueColumns" />)
        </foreach>
    </insert>

    <update id="deleteProjectTemplate">
	   update ReportTemplate set is_delete = 1 where id = #{templateId};
	</update>

</mapper>
