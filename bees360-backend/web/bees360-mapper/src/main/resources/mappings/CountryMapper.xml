<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.CountryMapper">

	<select id="listCountriesWithPrefix" resultType="java.lang.String">
		select country_name
		  from Country
		  <if test="regex != null and regex.length() > 0">
		      where lower(country_name)  like #{regex}
		  </if>
		  order by country_name
		  <if test="limit >= 0">
		      limit #{limit}
		  </if>
	</select>

</mapper>
