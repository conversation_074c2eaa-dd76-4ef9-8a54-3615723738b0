<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.InvoiceMapper">
	<resultMap id="invoiceMap"
		type="com.bees360.entity.Invoice">
		<id column="invoice_id" jdbcType="BIGINT" property="invoiceId" />
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="company_id" jdbcType="BIGINT" property="companyId" />
		<result column="project_id" jdbcType="BIGINT" property="projectId" />
		<result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle" />
		<result column="description" jdbcType="VARCHAR" property="description" />
		<result column="status" jdbcType="INTEGER" property="status" />
		<result column="amount_due" jdbcType="DOUBLE" property="amountDue" />
		<result column="subtotal" jdbcType="DOUBLE" property="subtotal" />
		<result column="tax_amount" jdbcType="DOUBLE" property="taxAmount" />
		<result column="discount_amount" jdbcType="DOUBLE" property="discountAmount" />
		<result column="payment_method" jdbcType="VARCHAR" property="paymentMethod" />
		<result column="customer_name" jdbcType="VARCHAR" property="customerName" />
		<result column="customer_address" jdbcType="VARCHAR" property="customerAddress" />
		<result column="customer_city" jdbcType="VARCHAR" property="customerCity" />
		<result column="customer_state" jdbcType="VARCHAR" property="customerState" />
		<result column="customer_zip_code" jdbcType="VARCHAR" property="customerZipCode" />
		<result column="customer_email" jdbcType="VARCHAR" property="customerEmail" />
		<result column="customer_phone" jdbcType="VARCHAR" property="customerPhone" />
		<result column="customer_company" jdbcType="VARCHAR" property="customerCompany" />
		<result column="created_time" jdbcType="BIGINT" property="createdTime" />
		<result column="paid_time" jdbcType="BIGINT" property="paidTime" />
		<result column="due_time" jdbcType="BIGINT" property="dueTime" />
		<result column="is_deleted" jdbcType="INTEGER" property="deleted" />
	</resultMap>

	<sql id="invoiceColumns">
		invoice_id, user_id, company_id, project_id, invoice_title, description, status, amount_due,
		subtotal, tax_amount, discount_amount, payment_method, customer_name, customer_address, customer_city,
		customer_state, customer_zip_code, customer_email, customer_phone, customer_company,
		created_time, paid_time, due_time, is_deleted
	</sql>

	<select id="getById" resultMap="invoiceMap">
		select <include refid="invoiceColumns" /> from Invoice
		where invoice_id = #{invoiceId};
	</select>

	<select id="getByProjectIdAndInvoiceId" resultMap="invoiceMap">
		select <include refid="invoiceColumns" /> from Invoice
		where project_id = #{projectId} and invoice_id = #{invoiceId};
	</select>

	<select id="listByProjectId" resultMap="invoiceMap">
		select <include refid="invoiceColumns" /> from Invoice
		where project_id = #{projectId} and is_deleted = 0
		order by invoice_id desc;
	</select>

	<select id="listByUserId" resultMap="invoiceMap">
		select <include refid="invoiceColumns" /> from Invoice
		where user_id = #{userId} and is_deleted = 0
			<![CDATA[
				and #{startTime} <= created_time and created_time < #{endTime}
			]]>
		order by invoice_id limit #{startIndex}, #{pageSize};
	</select>

	<insert id="insert" parameterType="Invoice" useGeneratedKeys="true" keyProperty="invoiceId">
		insert into Invoice(user_id, company_id, project_id, invoice_title, description, status, amount_due,
			subtotal, tax_amount, discount_amount, payment_method, customer_name, customer_address, customer_city,
			customer_state, customer_zip_code, customer_email, customer_phone, customer_company,
			created_time, paid_time, due_time, is_deleted)
		values (#{userId}, #{companyId}, #{projectId}, #{invoiceTitle}, #{description}, #{status}, #{amountDue},
			#{subtotal}, #{taxAmount}, #{discountAmount}, #{paymentMethod}, #{customerName}, #{customerAddress}, #{customerCity},
			#{customerState}, #{customerZipCode}, #{customerEmail}, #{customerPhone}, #{customerCompany},
			#{createdTime}, #{paidTime}, #{dueTime}, #{isDeleted});
	</insert>

	<update id="deleteById">
		update Invoice set is_deleted = 1 where invoice_id = #{InvoiceId};
	</update>
</mapper>
