<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.UserReadReportMapper">
	<resultMap id="userReadReportMap" type="com.bees360.entity.UserReadReport">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="project_id" jdbcType="BIGINT" property="projectId" />
		<result column="report_id" jdbcType="VARCHAR" property="reportId" />
		<result column="report_type" jdbcType="INTEGER" property="reportType" />
		<result column="is_deleted" jdbcType="INTEGER"	property="isDeleted" />
        <result column="created_time" jdbcType="BIGINT" property="createdTime" />
        <result column="updated_time" jdbcType="BIGINT" property="updatedTime" />
	</resultMap>

    <select id="list" resultMap="userReadReportMap">
        select * from UserReadReport where user_id = #{userId} and project_id = #{projectId}
        and is_deleted = false;
    </select>

    <select id="listIn" resultMap="userReadReportMap">
        select * from UserReadReport where user_id = #{userId} and is_deleted = false
        and project_id in
        <foreach collection="projectIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
    </select>

    <select id="listByReportType" resultMap="userReadReportMap">
        select * from UserReadReport where user_id = #{userId} and project_id = #{projectId}
            and report_type = #{reportType} and is_deleted = false;
    </select>

    <select id="getByReportId" resultMap="userReadReportMap">
        select * from UserReadReport where user_id = #{userId} and project_id = #{projectId}
            and report_id = #{reportId} and is_deleted = false;
    </select>

    <insert id="insert">
        insert into UserReadReport(user_id, project_id, report_id, report_type, is_deleted, created_time, updated_time)
        values(#{userId}, #{projectId}, #{reportId}, #{reportType}, #{isDeleted}, #{createdTime}, #{updatedTime});
    </insert>

    <update id="deleteByReportId">
        update UserReadReport set is_deleted = true, updated_time = #{updatedTime}
        where project_id = #{projectId} and report_id = #{reportId}
        <if test="userId != null">
            and user_id = #{userId}
        </if>
    </update>

    <update id="deleteByReportType">
        update UserReadReport set is_deleted = true, updated_time = #{updatedTime}
        where project_id = #{projectId} and report_type = #{reportType}
        <if test="userId != null">
            and user_id = #{userId}
        </if>
    </update>
</mapper>
