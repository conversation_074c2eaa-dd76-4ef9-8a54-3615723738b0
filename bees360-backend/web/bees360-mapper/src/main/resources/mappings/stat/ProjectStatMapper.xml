<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.stat.ProjectStatMapper">

    <resultMap id="chartResult" type="com.bees360.entity.stat.dto.ProjectStatChartDto">
        <result column="project_id" jdbcType="INTEGER" property="projectId"/>
        <result column="service_type" jdbcType="INTEGER" property="serviceType"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="gps_location_longitude" jdbcType="DOUBLE" property="gpsLocationLongitude"/>
        <result column="gps_location_latitude" jdbcType="DOUBLE" property="gpsLocationLatitude"/>
    </resultMap>

    <resultMap id="listResult" type="com.bees360.entity.stat.dto.ProjectListDataDto">
        <result column="project_id" jdbcType="INTEGER" property="projectId"/>
        <result column="project_status" jdbcType="INTEGER" property="projectStatus"/>
        <result column="service_type" jdbcType="INTEGER" property="serviceType"/>
        <result column="insurance_company" jdbcType="BIGINT" property="insuranceCompany"/>
        <result column="insurance_company_name" jdbcType="VARCHAR" property="insuranceCompanyName"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>
        <result column="policy_number" jdbcType="VARCHAR" property="policyNumber"/>
        <result column="claim_number" jdbcType="VARCHAR" property="claimNumber"/>
        <result column="asset_owner_name" jdbcType="VARCHAR" property="assertOwnerName"/>
        <result column="asset_owner_phone" jdbcType="VARCHAR" property="assertOwnerPhone"/>
        <result column="days_old" jdbcType="INTEGER" property="daysOld"/>
        <result column="inspected_date" jdbcType="BIGINT" property="dateInspected"/>
        <result column="returned_date" jdbcType="BIGINT" property="dateReturned"/>
        <result column="policy_effective_date" jdbcType="DATE" property="policyEffectiveDate"/>
        <result column="created_time" jdbcType="BIGINT" property="createTime"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
    </resultMap>

    <resultMap id="cardStatResult" type="com.bees360.entity.stat.dto.ProjectStatCardDto">
        <result column="service_type" jdbcType="INTEGER" property="serviceType"/>
        <result column="count" jdbcType="INTEGER" property="count"/>
    </resultMap>

    <resultMap id="cardDataResult" type="com.bees360.entity.stat.dto.ProjectCardDataDto">
        <result column="project_id" jdbcType="INTEGER" property="projectId"/>
        <result column="insurance_company" jdbcType="BIGINT" property="insuranceCompany"/>
        <result column="insurance_company_name" jdbcType="VARCHAR" property="insuranceCompanyName"/>
        <result column="service_type" jdbcType="INTEGER" property="serviceType"/>
        <result column="project_status" jdbcType="INTEGER" property="projectStatus"/>
        <result column="days_old" jdbcType="INTEGER" property="daysOld"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
    </resultMap>

    <sql id="daysOldSql">
        floor(
            GREATEST(
                0,
                timestampdiff(
                    second,
                    GREATEST(
                        date_sub(from_unixtime(P.created_time / 1000), INTERVAL #{centerUtcOffset} HOUR),
                        if(P.policy_effective_date is null, 0, P.policy_effective_date)
                    ),
                    date_sub(utc_timestamp(), INTERVAL #{centerUtcOffset} HOUR)
                )
            ) / 86400
        )
    </sql>

    <sql id="orderBy">
        <choose>
            <when test="sortKey != null and sortKey != ''">
                <choose>
                    <when test="sortOrder == 'asc'">
                        order by ${sortKey} asc
                    </when>
                    <otherwise>
                        order by ${sortKey} desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <choose>
                    <when test="sortOrder == 'asc'">
                        order by project_id asc
                    </when>
                    <otherwise>
                        order by project_id desc
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </sql>

    <sql id="orderByAndLimit">
        <include refid="orderBy"/>
        limit #{startIndex}, #{pageSize}
    </sql>

    <sql id="companyIds">
        <foreach collection="companyIds" item="companySingleId" separator="," open="(" close=")">
            #{companySingleId}
        </foreach>
    </sql>

    <sql id="baseSelect">
        select Pr.*
        from Project Pr
        left join project_inspection_schedule PIS on Pr.project_id = PIS.project_id
        <if test="batchNo != null">
            , BeesPilotBatchItem BI
        </if>
        <if test="memberRole != null or (memberUserList != null and memberUserList.size() > 0)">
            , project_member M
        </if>
        <if test="creatorList != null and creatorList.size() > 0">
            , project_member M2
        </if>
        <where>
            <if test="createdBy != null">
                and Pr.created_by = #{createdBy}
            </if>
            <if test="startTime != null">
                <![CDATA[
				    and Pr.created_time >= #{startTime}
			    ]]>
            </if>
            <if test="endTime != null">
                <![CDATA[
				    and Pr.created_time <= #{endTime}
			    ]]>
            </if>
            <if test="companyId != null">
                and (
                    Pr.company_id = #{companyId}
                    or Pr.insurance_company = #{companyId}
                    or Pr.repair_company = #{companyId}
                )
            </if>
            <if test="serviceTypes != null and serviceTypes.size() > 0">
                and Pr.service_type in
                <foreach collection="serviceTypes" item="serviceType" index="index" separator="," open="(" close=")">
                    #{serviceType}
                </foreach>
            </if>
            <if test="includeProjectStatus != null and includeProjectStatus.size() > 0">
                and Pr.project_status in
                <foreach collection="includeProjectStatus" item="projectStatus" separator="," open="(" close=")">
                    #{projectStatus}
                </foreach>
            </if>
            <if test="excludeProjectStatus != null and excludeProjectStatus.size() > 0">
                <foreach collection="excludeProjectStatus" item="projectStatus">
                    and  Pr.project_status != #{projectStatus}
                </foreach>
            </if>
            <if test="states != null and states.size() > 0">
                and Pr.state in
                <foreach collection="states" item="state" separator="," open="(" close=")">
                    #{state}
                </foreach>
            </if>
            <if test="companyIds != null and companyIds.size() > 0">
                and (
                Pr.company_id in <include refid="companyIds"/>
                or Pr.insurance_company in <include refid="companyIds"/>
                or Pr.repair_company in <include refid="companyIds"/>
                )
            </if>
            <if test="projectIds != null and projectIds.size > 0">
			and Pr.project_id in
			<foreach collection="projectIds" open="(" item="item" separator="," close=")">
				#{item}
			</foreach>
            </if>
            <if test="policyNumber != null and policyNumber != ''">
                and Pr.policy_number = #{policyNumber}
            </if>
            <if test="insuredNameRegex != null and insuredNameRegex != ''">
                and Pr.asset_owner_name like #{insuredNameRegex}
            </if>
            <if test="insuredPhoneRegex != null">
                and (Pr.asset_owner_phone like #{insuredPhoneRegex} or
                Pr.insured_home_phone like #{insuredPhoneRegex} or
                Pr.insured_work_phone like #{insuredPhoneRegex})
            </if>
            <if test="policyEffectiveStartDate != null">
                <![CDATA[
		    	and Pr.policy_effective_date >= #{policyEffectiveStartDate}
		  	]]>
            </if>
            <if test="addressRegex != null and addressRegex != ''">
                and Pr.address like #{addressRegex}
            </if>
            <if test="cityRegex != null and cityRegex != ''">
                and Pr.city like #{cityRegex}
            </if>
            <if test="inspectionNumber != null  and inspectionNumber !=''">
               and Pr.inspection_number = #{inspectionNumber}
            </if>
            <if test="inspectionStartTime != null and inspectionStartTime > 0">
                <![CDATA[
		    	and Pr.inspection_time >= #{inspectionStartTime}
		  	]]>
            </if>
            <if test="inspectionEndTime != null and inspectionEndTime > 0">
                <![CDATA[
		      and Pr.inspection_time <= #{inspectionEndTime}
		    ]]>
            </if>
            <if test="zipCode != null and zipCode != ''">
                and Pr.zip_code = #{zipCode}
            </if>
            <if test="batchNo != null">
                and Pr.project_id = BI.project_id
                and BI.batch_no = #{batchNo}
                and BI.is_deleted = 0
            </if>
            <if test="scheduledTimeStart != null and scheduledTimeStart > 0">
                <![CDATA[
                and PIS.scheduled_time >= #{scheduledTimeStart}
            ]]>
            </if>
            <if test="scheduledTimeEnd != null and scheduledTimeEnd > 0">
                <![CDATA[
                and PIS.scheduled_time <= #{scheduledTimeEnd}
            ]]>
            </if>
            <if test="memberRole != null or (memberUserList != null and memberUserList.size() > 0)">
                and Pr.project_id = M.project_id and M.is_deleted = 0
                <if test="memberRole != null">
                    and M.role = #{memberRole}
                </if>
                <if test="memberUserList != null and memberUserList.size() > 0">
                    and M.user_id in
                    <foreach collection="memberUserList" open="(" separator="," item="userId" close=")">
                        #{userId}
                    </foreach>
                </if>
            </if>
            <if test="searchCompanyIdList != null and searchCompanyIdList.size > 0">
                and (Pr.insurance_company in
                <include refid="foreachSearchCompanyIdList"/>
                or Pr.repair_company in
                <include refid="foreachSearchCompanyIdList"/>
                or Pr.material_provider_company in
                <include refid="foreachSearchCompanyIdList"/>
                or Pr.company_id in<include refid="foreachSearchCompanyIdList"/>)
            </if>
            <if test="insuranceCompanyIdList != null and insuranceCompanyIdList.size > 0">
                and Pr.insurance_company in
                <include refid="foreachInsuranceCompanyIdList"/>
            </if>
            <if test="processCompanyIdList != null and processCompanyIdList.size > 0">
                and Pr.repair_company in
                <include refid="foreachProcessCompanyIdList"/>
            </if>
        <if test="creatorList != null and creatorList.size() > 0">
            and Pr.project_id = M2.project_id and M2.role = -1 and M2.user_id in
            <foreach collection="creatorList" item="creator" open="(" separator="," close=")">
                #{creator}
            </foreach>
        </if>
            and Pr.latest_status != 0 AND Pr.service_type is NOT NULL
        </where>
    </sql>

    <select id="getRiskScoreCardStatData" resultMap="cardDataResult">
        SELECT
            P.project_id, P.service_type, S.score
        FROM (
            <include refid="baseSelect"/>
        ) P JOIN ProjectScore S
        ON P.project_id = S.project_id
        <where>
            <if test="riskScoreMin != null">
                <![CDATA[
				    and S.score >= #{riskScoreMin}
			    ]]>
            </if>
            <if test="riskScoreMax != null">
                <![CDATA[
				    and S.score <= #{riskScoreMax}
			    ]]>
            </if>
        </where>
        ORDER BY S.score asc
    </select>

    <select id="incompletedCardStat" resultMap="cardDataResult">
        SELECT
            P.project_id, P.service_type, P.project_status, P.insurance_company, C.company_name as insurance_company_name,
            <include refid="daysOldSql"/> as days_old
        FROM (
            <include refid="baseSelect"/>
        ) P LEFT JOIN Company C on P.insurance_company = C.company_id
        <where>
            and P.project_status != 90 and P.project_status != 100 and P.project_status != 110
        </where>
    </select>

    <select id="cardStat" resultMap="cardStatResult">
        select count(project_id) as count, service_type
        from (
            <include refid="baseSelect"/>
        ) P
        group by service_type
        order by service_type asc
    </select>

    <select id="chartStat" resultMap="chartResult">
        select
            P.project_id, P.service_type, P.country, P.state, P.city,
            P.address, P.zip_code,
            ST_X(P.gps_location) as gps_location_longitude, ST_Y(P.gps_location) as gps_location_latitude
        from(
            <include refid="baseSelect"/>
        ) P
    </select>

    <select id="riskScoreChartStat" resultMap="chartResult">
        select
        P.project_id, P.service_type, P.country, P.state, P.city,
        P.address, P.zip_code,
        ST_X(P.gps_location) as gps_location_longitude, ST_Y(P.gps_location) as gps_location_latitude
        from (
            select X.* from (<include refid="baseSelect"/>) X JOIN ProjectScore S on X.project_id = S.project_id
            <where>
                <if test="riskScoreMin != null">
                    <![CDATA[
                        and S.score >= #{riskScoreMin}
                    ]]>
                </if>
                <if test="riskScoreMax != null">
                    <![CDATA[
                        and S.score <= #{riskScoreMax}
                    ]]>
                </if>
            </where>
        ) P
    </select>


    <select id="listCreatedWithSearch" resultMap="listResult">
        select
            P.project_id, P.project_status, P.policy_number, P.claim_number,
            P.address, P.city, P.state, P.country, P.zip_code,
            P.asset_owner_name, P.asset_owner_phone,P.insurance_company, C.company_name as insurance_company_name,
            <include refid="daysOldSql"/> as days_old
        from(
            <include refid="baseSelect"/>
        ) P LEFT JOIN Company C on P.insurance_company = C.company_id
        <include refid="orderByAndLimit"/>
    </select>

    <select id="listIncompletedWithSearch" resultMap="listResult">
        select
            P.project_id, P.project_status, P.policy_number, P.claim_number,
            P.address, P.city, P.state, P.country, P.zip_code, P.service_type,
            P.asset_owner_name, P.asset_owner_phone, P.insurance_company, C.company_name as insurance_company_name,
            <include refid="daysOldSql"/> as days_old
        from(
            <include refid="baseSelect"/>
        ) P LEFT JOIN Company C on P.insurance_company = C.company_id
        <where>
            and P.project_status != 90 and P.project_status != 100 and P.project_status != 110
        </where>
        <include refid="orderByAndLimit"/>
    </select>



    <select id="listCompletedWithSearch" resultMap="listResult">
        select
            X.project_id, X.inspected_date, X.returned_date,
            P.policy_number, P.claim_number,
            P.address, P.city, P.state, P.country, P.zip_code,
            P.asset_owner_name, P.asset_owner_phone, P.policy_effective_date, P.created_time,
            <include refid="daysOldSql"/> as days_old
        FROM (
            select
                A.project_id,
                max(case when B.status = 70 then B.created_time else 0 end) as inspected_date,
                max(case when B.status = 90 then B.created_time else 0 end) as returned_date
            from (
                <include refid="baseSelect"/>
            ) A
            left join (select * from ProjectStatus where is_deleted = 0) B
            on A.project_id = B.project_id
            group by A.project_id
            order by A.project_id asc
        ) X JOIN Project P ON X.project_id = P.project_id
        <include refid="orderByAndLimit"/>
    </select>

    <select id="projectListCount" resultType="java.lang.Integer">
        select count(1) from(
        <include refid="baseSelect"/>
        )P
    </select>

    <select id="projectListId" resultType="long">
        select P.project_id from(
        <include refid="baseSelect"/>
        )P
    </select>

    <select id="searchRiskScoreList" resultMap="listResult">
        SELECT
            P.project_id, P.policy_number, P.claim_number,
            P.address, P.city, P.state, P.country, P.zip_code,
            P.asset_owner_name, P.asset_owner_phone,
            S.score
        FROM (
            <include refid="baseSelect"/>
        ) P JOIN ProjectScore S
        ON P.project_id = S.project_id
        <where>
            <if test="riskScoreMin != null">
                <![CDATA[
				    and S.score >= #{riskScoreMin}
			    ]]>
            </if>
            <if test="riskScoreMax != null">
                <![CDATA[
				    and S.score <= #{riskScoreMax}
			    ]]>
            </if>
        </where>
        <include refid="orderByAndLimit"/>
    </select>

    <select id="searchRiskScoreCount" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM (
            <include refid="baseSelect"/>
        ) P JOIN ProjectScore S
        ON P.project_id = S.project_id
        <where>
            <if test="riskScoreMin != null">
                <![CDATA[
				    and S.score >= #{riskScoreMin}
			    ]]>
            </if>
            <if test="riskScoreMax != null">
                <![CDATA[
				    and S.score <= #{riskScoreMax}
			    ]]>
            </if>
        </where>
    </select>

        <select id="searchRiskScoreId" resultType="long">
        SELECT
            P.project_id
        FROM (
            <include refid="baseSelect"/>
        ) P JOIN ProjectScore S
        ON P.project_id = S.project_id
        <where>
            <if test="riskScoreMin != null">
                <![CDATA[
				    and S.score >= #{riskScoreMin}
			    ]]>
            </if>
            <if test="riskScoreMax != null">
                <![CDATA[
				    and S.score <= #{riskScoreMax}
			    ]]>
            </if>
        </where>
    </select>

    <sql id="foreachSearchCompanyIdList">
        <foreach collection="searchCompanyIdList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </sql>

    <sql id="foreachInsuranceCompanyIdList">
        <foreach collection="insuranceCompanyIdList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </sql>

    <sql id="foreachProcessCompanyIdList">
        <foreach collection="processCompanyIdList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </sql>
</mapper>
