<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.BeesPilotBatchItemMapper">

    <resultMap id="BaseResultMap" type="com.bees360.entity.BeesPilotBatchItem">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="is_deleted" jdbcType="INTEGER" property="deleted"/>
    </resultMap>

    <resultMap id="BatchItemResultMap" type="com.bees360.entity.BeesPilotBatchItemVo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="base_pay" jdbcType="DECIMAL" property="basePay"/>
        <result column="extra_pay" jdbcType="DECIMAL" property="extraPay"/>
        <result column="plan_payment_date" jdbcType="DATE" property="planPaymentDate"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="pay_time" jdbcType="BIGINT" property="payTime"/>
        <result column="due_date" jdbcType="DATE" property="dueDate"/>
        <result column="is_deleted" jdbcType="INTEGER" property="deleted"/>
    </resultMap>

    <sql id="baseColumns">
	   item.id, item.batch_no, item.project_id, item.created_at,item.updated_at, item.is_deleted
	</sql>

    <sql id="batchColumns">
        bpb.user_id, bpb.base_pay, bpb.extra_pay, bpb.plan_payment_date, bpb.note, bpb.pay_time, bpb.due_date,bpb.status
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.bees360.entity.BeesPilotBatchItem"
            useGeneratedKeys="true">
        insert into BeesPilotBatchItem (batch_no, project_id)
        values
		(#{batchNo}, #{projectId})
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into BeesPilotBatchItem (batch_no, project_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            ('${item.batchNo}',${item.projectId})
        </foreach>;
    </insert>

    <select id="listByProjectIds" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from BeesPilotBatchItem item join BeesPilotBatch batch
        on item.batch_no = batch.batch_no
        <where>
            <if test="projectIds != null and projectIds.size() > 0">
                item.project_id in
                <foreach collection="projectIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        and item.is_deleted = 0 and batch.is_deleted = 0;
    </select>

    <select id="listByBatchNo" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from BeesPilotBatchItem item where item.batch_no = #{batchNo,jdbcType=VARCHAR}
        and item.is_deleted = 0;
    </select>

    <select id="listByProjectIdsWithBatchInfo" resultMap="BatchItemResultMap">
        select
        <include refid="baseColumns"/>,
        <include refid="batchColumns"/>
        from BeesPilotBatchItem item
        left join BeesPilotBatch bpb on bpb.batch_no = item.batch_no and item.is_deleted = 0
        <where>
            <if test="projectIds != null and projectIds.size() > 0">
                item.project_id in
                <foreach collection="projectIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            and bpb.is_deleted = 0;
        </where>
    </select>

    <update id="deleteByBatchNo">
        update BeesPilotBatchItem set is_deleted = true
        where batch_no = #{batchNo};
    </update>

    <update id="deleteByProjectIds">
        update BeesPilotBatchItem set is_deleted = true
        where project_id in
        <foreach collection="projectIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </update>

    <update id="updateBatchItemDeletedStatusByProjectIds">
        update BeesPilotBatchItem set is_deleted = #{isDeleted}
        where batch_no = #{batchNo} and project_id in
        <foreach collection="projectIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>

    </update>

    <select id="listByBatchNoWithoutDeletedCheck" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from BeesPilotBatchItem item where item.batch_no = #{batchNo,jdbcType=VARCHAR};
    </select>

    <select id="listByProjectIdsWithoutDeletedCheck" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from BeesPilotBatchItem item
        <where>
            <if test="projectIds != null and projectIds.size() > 0">
                item.project_id in
                <foreach collection="projectIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
