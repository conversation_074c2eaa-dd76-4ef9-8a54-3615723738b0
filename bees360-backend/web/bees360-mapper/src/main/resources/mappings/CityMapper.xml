<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.CityMapper">

	<resultMap id="baseCityMap" type="com.bees360.entity.City">
	    <id column="id" jdbcType="BIGINT" property="id"/>
	    <result column="region_id" jdbcType="VARCHAR" property="regionId"/>
	    <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
	</resultMap>

	<select id="listCityWithPrefix" resultType="java.lang.String">
		select city_name from City
		<if test="regex != null and regex.length() > 0">
            where lower(city_name)  like #{regex}
        </if>
        order by city_name
        <if test="limit >= 0">
            limit #{limit}
        </if>
	</select>
</mapper>
