<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.MemberMapper">

	<resultMap id="baseMemberMap" type="com.bees360.entity.Member">
	    <id column="project_id" jdbcType="BIGINT" property="projectId"/>
	    <result column="user_id" jdbcType="BIGINT" property="userId"/>
	    <result column="role" jdbcType="INTEGER" property="role"/>
	    <result column="created_time" jdbcType="BIGINT" property="createdTime"/>
	    <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
	    <result column="description" jdbcType="VARCHAR" property="description"/>
	    <result column="is_deleted" jdbcType="INTEGER" property="deleted"/>
	</resultMap>

	<select id="listActiveMember" resultMap="baseMemberMap">
	   select * from project_member where project_id = #{projectId} and is_deleted = 0;
	</select>

  	<select id="listActiveMembersByUserId" resultMap="baseMemberMap">
  		select * from project_member where user_id = #{userId} and project_id = #{projectId} and is_deleted = 0;
  	</select>

  	<select id="getAssetOwnerId" resultType="java.lang.Long">
  		select user_id from project_member where project_id = #{projectId} and is_deleted = 0 and role = 0;
  	</select>

  	<select id="getActiveMemberByRole" resultMap="baseMemberMap">
  		select * from project_member where role = #{role} and project_id = #{projectId} and is_deleted = 0;
  	</select>

  	<select id="getMemberByRoleAndUserId" resultMap="baseMemberMap">
  		select * from project_member where role = #{role} and project_id = #{projectId} and user_id = #{userId};
  	</select>

  	<select id="listActiveMembersByRole" resultMap="baseMemberMap">
  	    select * from project_member where role = #{role} and project_id = #{projectId} and is_deleted = 0;
  	</select>

    <select id="listAllByUser" resultMap="baseMemberMap">
        select * from project_member where project_id = #{projectId} and user_id = #{userId}
    </select>

     <select id="listActiveMembers" resultMap="baseMemberMap">
        select * from project_member
        where user_id = #{userId} and is_deleted = 0
        <if test="startTime != null">
            <![CDATA[
                and created_time >= #{startTime}
            ]]>
        </if>
         <if test="endTime != null">
            <![CDATA[
                and created_time <= #{endTime}
            ]]>
        </if>
        order by project_id asc, created_time asc
    </select>

    <select id="listRolesGroupByProject" resultType="IdValue">
       select project_id as id, group_concat(role) as value
       from project_member
       where user_id = #{userId} and is_deleted = 0 and project_id in
	       <foreach collection="projectIds" index="index" item="item" open="(" separator="," close=")">
	           #{item}
	       </foreach>
	   group by project_id;
    </select>

    <select id="listActiveMembersIn" resultMap="baseMemberMap">
        select * from project_member
        where is_deleted = 0 and project_id in
            <foreach collection="projectIds" index="index" item="item" open="(" separator="," close=")">
               #{item}
            </foreach>
        <if test="role != null">
            and role = #{role}
        </if>
    </select>

    <select id="listMembersUserIn" resultMap="baseMemberMap">
        select * from project_member
        <where>
	        project_id in
	            <foreach collection="projectIds" index="index" item="item" open="(" separator="," close=")">
	               #{item}
	            </foreach>
	        <if test="role != null">
	            and role = #{role}
	        </if>
	        <if test="userId != null">
	            and user_id = #{userId}
	        </if>
	        <if test="isDeleted != null">
	            <![CDATA[ and (is_deleted != 0) = #{isDeleted} ]]>
	        </if>
		</where>
    </select>

    <select id="listProjectIdWithActiveMember" resultType="Long">
        select distinct M.project_id from project_member M
        <where>
        	M.role = #{role} and M.is_deleted = 0
        	<if test="userId != null">
        		and M.user_id = #{userId}
        	</if>
         </where>
    </select>

    <insert id="insert" parameterType="Member">
    	insert ignore into project_member
    	(project_id, user_id, role, created_time, created_by, description, is_deleted)
    	values
    	(#{projectId}, #{userId}, #{role}, #{createdTime}, #{createdBy}, #{description}, #{deleted})
	</insert>

    <insert id="insertBatch" parameterType="java.util.List">
    	insert into project_member
    		(project_id, user_id, role, created_time, created_by, description, is_deleted)
    	values
    	<foreach collection="list" item="item" separator=",">
    		(#{item.projectId}, #{item.userId}, #{item.role}, #{item.createdTime}, #{item.createdBy},
    			#{item.description}, #{item.deleted})
    	</foreach>
	</insert>

	<update id="delete">
		update project_member set is_deleted = 1 where project_id = #{projectId} and user_id = #{userId} and role = #{role};
	</update>

	<update id="activeMember">
		update project_member set is_deleted = 0 where project_id = #{projectId} and user_id = #{userId} and role = #{role};
	</update>

	<update id="activeMembers">
		update project_member set is_deleted = 0
		<where>
			user_id = #{userId} and role = #{role} and project_id in
				<foreach collection="projectIds" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
		</where>
	</update>
</mapper>
