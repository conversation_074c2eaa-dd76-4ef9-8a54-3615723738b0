<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ProjectCustomizedInfoMapper">
  	<resultMap id="baseProjectCustomizedInfo" type="com.bees360.entity.ProjectCustomizedInfo">
	    <id column="id" jdbcType="BIGINT" property="id"/>
	    <result column="project_id" jdbcType="BIGINT" property="projectId"/>
	    <result column="column_code" jdbcType="INTEGER" property="columnCode"/>
	    <result column="column_value" jdbcType="VARCHAR" property="columnValue"/>
	</resultMap>

  	<sql id="customizedInfoColumns">
		id, project_id, column_code, column_value
	</sql>

  	<select id="listCustomizedInfos" resultMap="baseProjectCustomizedInfo">
  		select <include refid="customizedInfoColumns" /> from ProjectCustomizedInfo where project_id=#{projectId};
  	</select>

  	<insert id="insert">
  	    insert into ProjectCustomizedInfo(project_id, column_code, column_value) values (#{projectId}, #{columnCode}, #{columnValue});
  	</insert>

  	<insert id="insertBatch">
  	    insert into ProjectCustomizedInfo(project_id, column_code, column_value) values
  	    <foreach collection="list" item="item" index="index" separator=",">
          (#{item.projectId}, #{item.columnCode}, #{item.columnValue})
        </foreach>;
  	</insert>

</mapper>
