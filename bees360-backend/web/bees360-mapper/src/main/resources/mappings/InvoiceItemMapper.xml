<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.InvoiceItemMapper">
	<resultMap id="invoiceItemMap"
		type="com.bees360.entity.InvoiceItem">
		<id column="item_id" jdbcType="BIGINT" property="itemId" />
		<result column="invoice_id" jdbcType="BIGINT" property="invoiceId" />
		<result column="product_id" jdbcType="INTEGER" property="productId" />
		<result column="item_title" jdbcType="VARCHAR" property="itemTitle" />
		<result column="description" jdbcType="VARCHAR" property="description" />
		<result column="quantity" jdbcType="INTEGER" property="quantity" />
		<result column="unit_price" jdbcType="DOUBLE" property="unitPrice" />
		<result column="amount" jdbcType="DOUBLE" property="amount" />
		<result column="discount_amount" jdbcType="DOUBLE" property="discountAmount" />
		<result column="tax_rate_id" jdbcType="INTEGER" property="taxRateId" />
		<result column="tax_rate_name" jdbcType="VARCHAR" property="taxRateName" />
		<result column="tax_rate_percentage" jdbcType="DOUBLE" property="taxRatePercentage" />
		<result column="tax_amount" jdbcType="DOUBLE" property="taxAmount" />
		<result column="created_time" jdbcType="BIGINT" property="createdTime" />
	</resultMap>

	<sql id="invoiceItemColumns">
		item_id, invoice_id, product_id, item_title, description, quantity, unit_price,
		amount, discount_amount, tax_rate_id, tax_rate_name, tax_rate_percentage, tax_amount,
		created_time
	</sql>

	<insert id="insert" parameterType="InvoiceItem" useGeneratedKeys="true" keyProperty="itemId">
		insert into InvoiceItem (invoice_id, product_id, item_title, description, quantity, unit_price,
			amount, discount_amount, tax_rate_id, tax_rate_name, tax_rate_percentage, tax_amount,
			created_time)
		values (#{invoiceItem}, #{invoiceId}, #{productId}, #{itemTitle}, #{description},#{quantity}, #{unitPrice},
			#{amount}, #{discountAmount}, #{taxRateId}, #{taxRateName}, #{taxRatePercentage}, #{taxAmount},
			#{createdTIme});
	</insert>

	<insert id="insertBatch" parameterType="java.util.List">
		insert into InvoiceItem (invoice_id, product_id, item_title, description,
			quantity, unit_price, amount, discount_amount, tax_rate_id, tax_rate_name,
			tax_rate_percentage, tax_amount, created_time)
		values <foreach collection="invoiceItems" item="item" index="index" separator=",">
				(#{item.invoiceId}, #{item.productId}, #{item.itemTitle}, #{item.description},
				#{item.quantity}, #{item.unitPrice}, #{item.amount}, #{item.discountAmount}, #{item.taxRateId}, #{item.taxRateName},
				#{item.taxRatePercentage}, #{item.taxAmount}, #{item.createdTime})
			</foreach>
	</insert>

	<select id="getById" resultMap="invoiceItemMap">
		select <include refid="invoiceItemColumns" /> from InvoiceItem where item_id = #{itemId};
	</select>

	<select id="listByInvoiceId" resultMap="invoiceItemMap">
		select <include refid="invoiceItemColumns" /> from InvoiceItem where invoice_id = #{invoiceId};
	</select>

</mapper>
