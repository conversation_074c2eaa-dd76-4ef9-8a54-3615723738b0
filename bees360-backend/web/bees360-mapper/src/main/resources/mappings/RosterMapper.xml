<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.RosterMapper">

	<resultMap id="rosterMap" type="com.bees360.entity.Roster">
	    <id column="roster_id" jdbcType="BIGINT" property="rosterId"/>
	    <result column="first_name" jdbcType="VARCHAR" property="firstName"/>
	    <result column="last_name" jdbcType="VARCHAR" property="lastName"/>
		<result column="phone" jdbcType="VARCHAR" property="phone"/>
		<result column="email" jdbcType="VARCHAR" property="email"/>
		<result column="address" jdbcType="VARCHAR" property="address"/>
		<result column="city" jdbcType="VARCHAR" property="city"/>
		<result column="state" jdbcType="VARCHAR" property="state"/>
		<result column="country" jdbcType="VARCHAR" property="country"/>
		<result column="zipCode" jdbcType="VARCHAR" property="zipCode"/>
		<result column="lng" jdbcType="DOUBLE" property="lng"/>
		<result column="lat" jdbcType="DOUBLE" property="lat"/>
		<result column="additional_operating_territories" jdbcType="VARCHAR" property="additionalOperatingTerritories"/>
		<result column="operating_city_state" jdbcType="VARCHAR" property="operatingCityState"/>
		<result column="designated_home_state_license" jdbcType="VARCHAR" property="designatedHomeStateLicense"/>
		<result column="additional_license" jdbcType="VARCHAR" property="additionalLicense"/>
		<result column="years_of_experience" jdbcType="INTEGER" property="yearsOfExperience"/>
		<result column="resume_url" jdbcType="VARCHAR" property="resumeUrl"/>
		<result column="travel_radius" jdbcType="INTEGER" property="travelRadius"/>
		<result column="more_than_100miles_traveled" jdbcType="INTEGER" property="moreThan100MilesTraveled"/>
		<result column="cat_event_deployed" jdbcType="INTEGER" property="catEventDeployed"/>
		<result column="created_time" jdbcType="BIGINT" property="createdTime"/>
		<result column="updated_time" jdbcType="BIGINT" property="updatedTime"/>
	</resultMap>

	<sql id="rosterColumns">
		first_name, last_name, phone,email, address, city, state, country, zip_code, gps_location,
		additional_operating_territories,operating_city_state,  designated_home_state_license,
		additional_license,  years_of_experience, message, resume_url, travel_radius,
		more_than_100miles_traveled, cat_event_deployed, created_time, updated_time
	</sql>


	<insert id="insert">
		INSERT into Roster(first_name, last_name, phone,email, address, city, state, country, zip_code,
		gps_location, additional_operating_territories,
		operating_city_state,  designated_home_state_license,  additional_license,  years_of_experience, message, resume_url,
		travel_radius, more_than_100miles_traveled, cat_event_deployed, created_time, updated_time) values (#{firstName},
		#{lastName}, #{phone}, #{email}, #{address}, #{city}, #{state}, #{country}, #{zipCode}, Point(#{lng},#{lat}),
		#{additionalOperatingTerritories},#{operatingCityState}, #{designatedHomeStateLicense}, #{additionalLicense},
		#{yearsOfExperience}, #{message}, #{resumeUrl},#{travelRadius}, #{moreThan100MilesTraveled}, #{catEventDeployed},
		#{createdTime}, #{updatedTime})
	</insert>


	<select id="listRostersByName" resultMap="rosterMap">
		select <include refid="rosterColumns" /> from Roster
		<where>
			<if test="firstName != null and firstName.length() > 0">
				first_name  = #{firstName}
			</if>
			<if test="lastName != null and lastName.length() > 0">
				and last_name  = #{lastName}
			</if>
		</where>
        order by roster_id desc
	</select>
</mapper>
