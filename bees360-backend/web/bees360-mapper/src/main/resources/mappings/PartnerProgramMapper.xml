<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.PartnerProgramMapper">

	<resultMap id="baseMap" type="com.bees360.entity.PartnerProgram">
	    <id column="id" jdbcType="BIGINT" property="id"/>
	    <result column="inspection_service" jdbcType="INTEGER" property="inspectionService"/>
        <result column="highfly_service" jdbcType="INTEGER" property="highflyService"/>
        <result column="first_name" jdbcType="VARCHAR" property="firstName"/>
        <result column="last_name" jdbcType="VARCHAR" property="lastName"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="company" jdbcType="VARCHAR" property="company"/>
	</resultMap>

  	<sql id="baseColumn">
  	     inspection_service, highfly_service,
  	     fist_name, last_name, email, phone, company
  	</sql>

  	<select id="listAll" resultMap="baseMap">
  	     select <include refid="baseColumn" /> from PartnerProgram;
  	</select>

	<insert id="insert" parameterType="PartnerProgram" useGeneratedKeys="true" keyProperty="id">
		insert into PartnerProgram
		(inspection_service, highfly_service,
         first_name, last_name, email, phone, company)
		values
		(#{inspectionService}, #{highflyService},
		#{firstName}, #{lastName}, #{email}, #{phone}, #{company})
	</insert>
</mapper>
