<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bees360.mapper.ProjectInspectionMapper">

    <resultMap id="baseResultMap" type="com.bees360.entity.ProjectInspection">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="inspection_code" jdbcType="BIGINT" property="inspectionCode"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="expiration_time" jdbcType="BIGINT" property="expirationTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted"/>
    </resultMap>

    <sql id="baseColumns">
        id, project_id, inspection_code, create_time, expiration_time, is_deleted
    </sql>

    <insert id="insert" parameterType="com.bees360.entity.ProjectInspection" useGeneratedKeys="true" keyProperty="id">
        insert into ProjectInspection(project_id, inspection_code, create_time, expiration_time, is_deleted)
        values (#{projectId,jdbcType=BIGINT}, #{inspectionCode,jdbcType=VARCHAR},
        #{createTime,jdbcType=BIGINT}, #{expirationTime, jdbcType=BIGINT}, #{isDeleted, jdbcType=BOOLEAN})
    </insert>

    <update id="deleteByProjectId">
        update ProjectInspection set is_deleted = 1 where project_id = #{projectId}
    </update>
</mapper>
