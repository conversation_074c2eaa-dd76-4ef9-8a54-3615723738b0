<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.openapi.OpenApiClientMapper">

    <resultMap id="apiSecretMapper" type="com.bees360.entity.openapi.client.OpenApiClient">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="client_id" jdbcType="VARCHAR" property="clientId"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="client_secret" jdbcType="VARCHAR" property="clientSecret"/>
        <result column="access_token_validate_seconds" jdbcType="BIGINT" property="accessTokenValidateSeconds"/>
        <result column="refresh_token_validate_seconds" jdbcType="BIGINT" property="refreshTokenValidateSeconds"/>
        <result column="scopes" jdbcType="VARCHAR" property="scopes"/>
        <result column="grant_type" jdbcType="INTEGER" property="grantType"/>
        <result column="create_at" jdbcType="BIGINT" property="createAt"/>
        <result column="update_at" jdbcType="BIGINT" property="updateAt"/>
        <result column="add_by" jdbcType="BIGINT" property="addBy"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
    </resultMap>

    <sql id="allColumn">
        id, client_name, client_id, company_id, client_secret,
        access_token_validate_seconds, refresh_token_validate_seconds,
        scopes, grant_type, create_at, update_at, add_by, update_by
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert OpenApiClient(client_name, client_id, company_id, client_secret,
        access_token_validate_seconds, refresh_token_validate_seconds,
        scopes, grant_type, create_at, update_at, add_by, update_by)
        values(#{clientName}, #{clientId}, #{companyId}, #{clientSecret},
        #{accessTokenValidateSeconds}, #{refreshTokenValidateSeconds},
        #{scopes}, #{grantType}, #{createAt}, #{updateAt}, #{addBy}, #{updateBy})
    </insert>

    <update id="update">
        update OpenApiClient
        <set>
            <if test="companyId != null">
                company_id = #{companyId},
            </if>
            <if test="clientName != null">
                client_name = #{clientName},
            </if>
            <if test="clientId != null">
                client_id = #{clientId},
            </if>
            <if test="clientSecret != null">
                client_secret = #{clientSecret},
            </if>
            <if test="accessTokenValidateSeconds != null">
                access_token_validate_seconds = #{accessTokenValidateSeconds},
            </if>
            <if test="refreshTokenValidateSeconds != null">
                refresh_token_validate_seconds = #{refreshTokenValidateSeconds},
            </if>
            <if test="scopes != null">
                scopes = #{scopes},
            </if>
            <if test="grantType != null">
                grant_type = #{grantType},
            </if>
            <if test="createAt != null">
                create_at = #{createAt},
            </if>
            <if test="updateAt != null">
                update_at = #{updateAt},
            </if>
            <if test="addBy != null">
                add_by = #{addBy},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <delete id="delete">
        delete from OpenApiClient where id = #{id}
    </delete>

    <delete id="deleteByClientId">
        delete from OpenApiClient where client_id = #{clientId}
    </delete>

    <select id="getById" resultMap="apiSecretMapper">
        select <include refid="allColumn"/>
        from OpenApiClient where id = #{id}
    </select>

    <select id="getByClientId" resultMap="apiSecretMapper">
        select <include refid="allColumn"/>
        from OpenApiClient where client_id = #{clientId}
    </select>

    <select id="listAll" resultMap="apiSecretMapper">
        select <include refid="allColumn"/>
        from OpenApiClient
    </select>

    <select id="search" resultMap="apiSecretMapper">
        select <include refid="allColumn"/>
        from OpenApiClient
        <where>
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
        </where>
    </select>

</mapper>
