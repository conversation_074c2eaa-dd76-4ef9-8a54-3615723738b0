<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.ReportAnnotationImageMapper">
    <resultMap id="reportAnnotationImageMap" type="com.bees360.entity.ReportAnnotationImage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="image_id" jdbcType="VARCHAR" property="imageId"/>
        <result column="report_type" jdbcType="INTEGER" property="reportType"/>
        <result column="component_id" jdbcType="INTEGER" property="componentId"/>
        <result column="caption" jdbcType="VARCHAR" property="caption"/>
        <result column="alias" jdbcType="VARCHAR" property="alias"/>
        <result column="page" jdbcType="INTEGER" property="page"/>
        <association property="createTime" javaType="com.bees360.entity.grpc.Timestamp">
            <result column="create_time" jdbcType="BIGINT" property="seconds"/>
        </association>
    </resultMap>

    <sql id="reportAnnotationImageColumns">
		project_id, image_id, report_type, component_id, caption, alias, page, create_time
	</sql>

    <sql id="deleted">1</sql>
    <sql id="notDeleted">0</sql>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into ReportAnnotationImage (<include refid="reportAnnotationImageColumns"/>, is_deleted)
        values
        <foreach collection="reportAnnotationImages" item="item" index="index" separator=",">
            (#{item.projectId}, #{item.imageId}, #{item.reportType}, #{item.componentId},
            #{item.caption}, #{item.alias}, #{item.page}, #{item.createTime.seconds},<include refid="notDeleted"/>)
        </foreach>
    </insert>

    <update id="deleteByProjectIdAndReportTypes">
        update ReportAnnotationImage set is_deleted = <include refid="deleted"/>
        where project_id = #{projectId} and report_type in
        <foreach collection="reportTypes" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

</mapper>
