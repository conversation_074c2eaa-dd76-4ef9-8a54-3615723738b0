<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bees360.mapper.InvoiceDiscountMapper">
	<resultMap id="invoiceDiscountMap"
		type="com.bees360.entity.InvoiceDiscount">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="invoice_id" jdbcType="BIGINT" property="invoiceId" />
		<result column="discount_type" jdbcType="INTEGER" property="discountType" />
		<result column="discount_name" jdbcType="VARCHAR" property="discountName" />
		<result column="off_type" jdbcType="INTEGER" property="offType" />
		<result column="percentage_off" jdbcType="DOUBLE" property="percentageOff" />
		<result column="amount_off" jdbcType="DOUBLE" property="amountOff" />
		<result column="amount" jdbcType="DOUBLE" property="amount" />
		<result column="created_time" jdbcType="BIGINT" property="createdTime" />
	</resultMap>

	<sql id="invoiceDiscountColumns">
		id, invoice_id, discount_type, discount_name, off_type, percentage_off, amount_off, amount, created_time
	</sql>

	<insert id="insert" parameterType="InvoiceDiscount" useGeneratedKeys="true" keyProperty="id">
		insert into
			InvoiceDiscount(invoice_id, discount_type, discount_name, off_type, percentage_off,
			amount_off, amount, created_time)
		values (#{invoiceId}, #{discountType}, #{discountName}, #{offType}, #{percentageOff},
			#{amountOff}, #{amount}, #{createdTime});
	</insert>

	<insert id="insertBatch" parameterType="java.util.List">
		insert into
			InvoiceDiscount(invoice_id, discount_type, discount_name, off_type,
			percentage_off,	amount_off, amount, created_time)
		values <foreach collection="list" item="item" index="index" separator=",">
				(#{item.invoiceId}, #{item.discountType}, #{item.discountName}, #{item.offType},
				#{item.percentageOff}, #{item.amountOff}, #{item.amount}, #{item.createdTime})
			</foreach>
	</insert>

	<select id="listByInvoiceId" resultMap="invoiceDiscountMap">
		select <include refid="invoiceDiscountColumns" /> from InvoiceDiscount where invoice_id = #{invoiceId};
	</select>

	<select id="getById" resultMap="invoiceDiscountMap">
		select <include refid="invoiceDiscountColumns" /> from InvoiceDiscount where id = #{id}
	</select>
</mapper>
