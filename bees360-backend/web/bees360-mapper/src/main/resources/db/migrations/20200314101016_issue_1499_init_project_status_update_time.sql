-- migrate:up
alter table Project add column status_update_time bigint default null after project_status;

update Project P,
    (select PS.project_id project_id, max(PS.created_time) status_update_time from ProjectStatus PS, Project P
    where PS.project_id = P.project_id and PS.status = P.project_status group by project_id) SUT
set P.status_update_time = SUT.status_update_time where P.project_id = SUT.project_id;

-- migrate:down
alter table Project drop column status_update_time;
