-- migrate:up
ALTER TABLE `Project`
ADD COLUMN `inspection_number` varchar(50) DEFAULT '' COMMENT 'inspection_number' AFTER `rotation_degree`,
ADD COLUMN `due_date`  bigint(20) DEFAULT NULL COMMENT 'due_date',
ADD COLUMN `customer` varchar(50) DEFAULT '' COMMENT 'customer',
ADD COLUMN `inspection_type` varchar(50) DEFAULT '' COMMENT 'inspection_type',
ADD COLUMN `agent` varchar(50) DEFAULT '' COMMENT 'agent',
ADD COLUMN `agent_email` varchar(50) DEFAULT '' COMMENT 'agent_email',
ADD COLUMN `agent_contact_name` varchar(50) DEFAULT '' COMMENT 'agent_contact_name',
ADD COLUMN `agent_phone` varchar(50) DEFAULT '' COMMENT 'agent_phone',
ADD COLUMN `guideline` text COMMENT 'guideline',
ADD COLUMN `insured_home_phone` varchar(50) DEFAULT '' COMMENT 'insured_home_phone',
ADD COLUMN `insured_work_phone` varchar(50) DEFAULT '' COMMENT 'insured_work_phone';

-- migrate:down
ALTER TABLE `Project`
DROP COLUMN `inspection_number`,
DROP COLUMN `due_date`,
DROP COLUMN `customer`,
DROP COLUMN `inspection_type`,
DROP COLUMN `agent`,
DROP COLUMN `agent_email`,
DROP COLUMN `agent_contact_name` ,
DROP COLUMN `agent_phone`,
DROP COLUMN `guideline`,
DROP COLUMN `insured_home_phone`,
DROP COLUMN `insured_work_phone`;
