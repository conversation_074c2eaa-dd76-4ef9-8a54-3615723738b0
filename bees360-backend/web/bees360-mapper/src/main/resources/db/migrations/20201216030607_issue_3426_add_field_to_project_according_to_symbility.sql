-- migrate:up
-- @guanrong.yang
begin;

-- -[TABLE Project]
Alter table Project add column cat_number varchar(100) NOT NULL DEFAULT '' comment 'a field from symbility' after claim_number;

-- -[TABLE ProjectSymbilityClaim]
Alter table ProjectSymbilityClaim add column claim_number varchar(100) NOT NULL DEFAULT '' comment 'a field from symbility' after claim_unique_id;
Alter table ProjectSymbilityClaim add column primary_adjuster_first_name varchar(100) NOT NULL DEFAULT '' comment 'a field from symbility' after claim_number;
Alter table ProjectSymbilityClaim add column primary_adjuster_last_name varchar(100) NOT NULL DEFAULT '' comment 'a field from symbility' after primary_adjuster_first_name;
Alter table ProjectSymbilityClaim add column primary_adjuster_email varchar(100) NOT NULL DEFAULT '' comment 'a field from symbility' after primary_adjuster_last_name;
Alter table ProjectSymbilityClaim add column primary_adjuster_phone varchar(100) NOT NULL DEFAULT '' comment 'a field from symbility' after primary_adjuster_email;

commit;

-- ------------------------------------------------【I AM BOTTOM LINE】------------------------------------------------

-- migrate:down
begin;

-- -[TABLE Project]
Alter table Project drop column cat_number;

-- -[TABLE ProjectSymbilityClaim]
Alter table ProjectSymbilityClaim drop column claim_number;
Alter table ProjectSymbilityClaim drop column primary_adjuster_first_name;
Alter table ProjectSymbilityClaim drop column primary_adjuster_last_name;
Alter table ProjectSymbilityClaim drop column primary_adjuster_email;
Alter table ProjectSymbilityClaim drop column primary_adjuster_phone;

commit;
