-- migrate:up

begin;
alter table ProjectLabel add column label_type varchar(20);
update ProjectLabel set label_type = 'underwriting' where label_name in
('Cancellation TBD', 'Denied', 'Not Closed', 'IBees not completed', 'Cancellation Unconfirmed');
update ProjectLabel set label_type = 'claim' where label_name in
('No response from insured', 'No response from PA', 'No response from roofer', 'Drone inspection declined',
'No LOR on file', 'Pending to Reschedule');
update ProjectLabel set label_type = 'common' where label_name = "Wrong #/ # Doesn't Belong To Insured";
commit;

-- migrate:down
alter table ProjectLabel drop column label_type;
