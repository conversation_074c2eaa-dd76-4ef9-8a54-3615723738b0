-- migrate:up
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (17, 50, NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, 'report/material/company/2096/PageOneTemplateIRS.pdf', NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (18, 1, ST_GeomFromText('POINT(236 40)'), '{[com.bees360.util.report.CommonsUtil].getYear}', NULL, NULL, 12, 1, '0,0,0', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (19, 1, ST_GeomFromText('POINT(45 345)'), '{reportParam.getProject.getAddress}', NULL, NULL, 25, 1, '64,64,64', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (20, 1, ST_GeomFromText('POINT(45 310)'), '{reportParam.getProject.getCity}, {reportParam.getProject.getState} {reportParam.getProject.getZipCode}', NULL, NULL, 25, 1, '64,64,64', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (21, 50, NULL, NULL, '44-{[com.bees360.util.report.CommonsUtil].getAddress(reportParam.getProject).length}', NULL, 1, NULL, NULL, NULL, 'report/material/company/2096/DamageReportPageTwo.pdf', NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (22, 50, NULL, NULL, '{[com.bees360.util.report.CommonsUtil].getAddress(reportParam.getProject).length}-43', NULL, 1, NULL, NULL, NULL, 'report/material/company/2096/DamageReportPageLongTwo.pdf', NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (23, 1, ST_GeomFromText('POINT(557 41)'), '{pageParam.getWriter.getPageNumber}', NULL, NULL, 11, NULL, '99,112,82', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (24, 1, ST_GeomFromText('POINT(110 651)'), '{reportParam.getParam.get(dateInspected)}', '44-{[com.bees360.util.report.CommonsUtil].getAddress(reportParam.getProject).length}', NULL, 11, NULL, '0,0,0', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (25, 1, ST_GeomFromText('POINT(110 630)'), '{reportParam.getParam.get(dateInspected)}', '{[com.bees360.util.report.CommonsUtil].getAddress(reportParam.getProject).length}-43', NULL, 11, NULL, '0,0,0', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (26, 1, ST_GeomFromText('POINT(110 672)'), '{[com.bees360.util.report.CommonsUtil].getAddress(reportParam.getProject)}', '44-{[com.bees360.util.report.CommonsUtil].getAddress(reportParam.getProject).length}', NULL, 11, NULL, '0,0,0', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (27, 1, ST_GeomFromText('POINT(110 670)'), '{reportParam.getProject.getAddress}', '{[com.bees360.util.report.CommonsUtil].getAddress(reportParam.getProject).length}-43', NULL, 11, NULL, '0,0,0', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (28, 1, ST_GeomFromText('POINT(110 655)'), '{reportParam.getProject.getCity}, {reportParam.getProject.getState} {reportParam.getProject.getZipCode}', '{[com.bees360.util.report.CommonsUtil].getAddress(reportParam.getProject).length}-43', NULL, 11, NULL, '0,0,0', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (29, 1, ST_GeomFromText('POINT(110 629)'), 'INSURANCE RISK SERVICES, INC.', '44-{[com.bees360.util.report.CommonsUtil].getAddress(reportParam.getProject).length}', NULL, 11, NULL, '0,0,0', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (30, 1, ST_GeomFromText('POINT(110 608)'), 'INSURANCE RISK SERVICES, INC.', '{[com.bees360.util.report.CommonsUtil].getAddress(reportParam.getProject).length}-43', NULL, 11, NULL, '0,0,0', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (31, 1, ST_GeomFromText('POINT(536 751)'), 'UNDERWRITING', NULL, NULL, 16, 1, '0,175,80', 1, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (32, 1, ST_GeomFromText('POINT(536 734)'), 'REPORT', NULL, NULL, 16, 1, '0,175,80', 1, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (33, 60, NULL, '[com.bees360.util.report.CommonsUtil].initAddress(pageParam.getCanvas,[com.bees360.util.report.CommonsUtil].getAddress(reportParam.getProject))', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (34, 20, ST_GeomFromText('POINT(30 170)'), 'reportParam.getElementVos.get(0i).getUrl', NULL, 0, 140, NULL, NULL, NULL, NULL, 552, 310, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (35, 20, ST_GeomFromText('POINT(340 503)'), 'reportParam.getElementVos.get(1i).getUrl', NULL, 0, 40, NULL, NULL, NULL, NULL, 250, 180, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (36, 1, ST_GeomFromText('POINT(41 41)'), '{[org.apache.commons.lang.StringUtils].trimToEmpty(reportParam.getProject.getAssetOwnerName.toUpperCase)}', NULL, NULL, 11, NULL, '99,112,82', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (37, 1, ST_GeomFromText('POINT(306 41)'), '{[com.bees360.util.report.CommonsUtil].getReportNum(reportParam.getReportType,reportParam.getProject.getProjectId.longValue)}', NULL, NULL, 11, NULL, '99,112,82', 1, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (40, 20, ST_GeomFromText('POINT(40 723)'), 'reportParam.getCompany.getLogo', NULL, 0, NULL, NULL, NULL, NULL, NULL, 80, 60, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (41, 50, NULL, ' ', '6-{pageParam.getElements.size}', NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, 2004);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (42, 50, NULL, NULL, '{pageParam.getElements.size}-7', NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, 2004);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (43, 21, ST_GeomFromText('POINT(35 548)'), 'pageParam.getElements.get(0i)', NULL, 0, 0, NULL, NULL, NULL, NULL, 256, 155, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (44, 22, ST_GeomFromText('POINT(318 548)'), 'pageParam.getElements.get(1i)', NULL, 0, 0, NULL, NULL, NULL, NULL, 256, 155, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (45, 23, ST_GeomFromText('POINT(50 235)'), 'pageParam.getElements.get(2i)', '6-{pageParam.getElements.size}&&{pageParam.getElements.size}-3', 1, 0, NULL, NULL, NULL, NULL, 512, 286, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (46, 23, ST_GeomFromText('POINT(25 287)'), 'pageParam.getElements.get(2i)', '{pageParam.getElements.size}-7', 1, 0, NULL, NULL, NULL, NULL, 400, 235, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (47, 24, ST_GeomFromText('POINT(30 65)'), 'pageParam.getElements.get(3i)', '6-{pageParam.getElements.size}&&{pageParam.getElements.size}-4', 0, 0, NULL, NULL, NULL, NULL, 176, 119, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (48, 24, ST_GeomFromText('POINT(25 168)'), 'pageParam.getElements.get(3i)', '{pageParam.getElements.size}-7', 0, 0, NULL, NULL, NULL, NULL, 136, 95, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (49, 24, ST_GeomFromText('POINT(214 65)'), 'pageParam.getElements.get(4i)', '6-{pageParam.getElements.size}&&{pageParam.getElements.size}-5', 0, 0, NULL, NULL, NULL, NULL, 176, 119, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (50, 24, ST_GeomFromText('POINT(167 168)'), 'pageParam.getElements.get(4i)', '{pageParam.getElements.size}-7', 0, 0, NULL, NULL, NULL, NULL, 136, 95, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (51, 24, ST_GeomFromText('POINT(398 65)'), 'pageParam.getElements.get(5i)', '6-{pageParam.getElements.size}&&{pageParam.getElements.size}-6', 0, 0, NULL, NULL, NULL, NULL, 176, 119, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (52, 24, ST_GeomFromText('POINT(309 168)'), 'pageParam.getElements.get(5i)', '{pageParam.getElements.size}-7', 0, 0, NULL, NULL, NULL, NULL, 136, 95, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (53, 24, ST_GeomFromText('POINT(451 168)'), 'pageParam.getElements.get(6i)', '{pageParam.getElements.size}-7', 0, 0, NULL, NULL, NULL, NULL, 136, 95, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (54, 24, ST_GeomFromText('POINT(25 66)'), 'pageParam.getElements.get(7i)', '{pageParam.getElements.size}-8', 0, 0, NULL, NULL, NULL, NULL, 136, 95, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (55, 24, ST_GeomFromText('POINT(167 66)'), 'pageParam.getElements.get(8i)', '{pageParam.getElements.size}-9', 0, 0, NULL, NULL, NULL, NULL, 136, 95, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (56, 24, ST_GeomFromText('POINT(309 66)'), 'pageParam.getElements.get(9i)', '{pageParam.getElements.size}-10', 0, 0, NULL, NULL, NULL, NULL, 136, 95, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (57, 24, ST_GeomFromText('POINT(451 66)'), 'pageParam.getElements.get(10i)', '{pageParam.getElements.size}-11', 0, 0, NULL, NULL, NULL, NULL, 136, 95, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (61, 2, ST_GeomFromText('POINT(35 709)'), '{[com.bees360.util.report.CommonsUtil].getTitle(reportParam,pageParam,0i)}', NULL, NULL, 11, NULL, '0,0,0', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (62, 2, ST_GeomFromText('POINT(35 534)'), '{[com.bees360.util.report.CommonsUtil].getTitle(reportParam,pageParam,1i)}', NULL, NULL, 11, NULL, '0,0,0', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (63, 2, ST_GeomFromText('POINT(35 189)'), '{[com.bees360.util.report.CommonsUtil].getTitle(reportParam,pageParam,2i)}', '6-{pageParam.getElements.size}', NULL, 11, NULL, '0,0,0', 0, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (64, 2, ST_GeomFromText('POINT(35 268)'), '{[com.bees360.util.report.CommonsUtil].getTitle(reportParam,pageParam,2i)}', '{pageParam.getElements.size}-7', NULL, 11, NULL, '0,0,0', 0, NULL, NULL, NULL, NULL);


INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (18, 2096, 17, '17', 1, 1, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (19, 2096, 18, '17', 1, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (20, 2096, 19, '17', 1, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (21, 2096, 20, '17', 1, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (22, 2096, 21, '17', 2, 1, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (23, 2096, 22, '17', 2, 1, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (24, 2096, 23, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (25, 2096, 24, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (26, 2096, 25, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (27, 2096, 26, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (28, 2096, 27, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (29, 2096, 28, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (30, 2096, 29, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (31, 2096, 30, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (32, 2096, 31, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (33, 2096, 32, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (34, 2096, 33, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (35, 2096, 34, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (36, 2096, 35, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (37, 2096, 40, '17', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (41, 2096, 41, '17', 3, 2, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (42, 2096, 42, '17', 3, 2, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (43, 2096, 33, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (44, 2096, 40, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (45, 2096, 23, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (46, 2096, 31, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (47, 2096, 32, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (48, 2096, 36, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (49, 2096, 37, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (50, 2096, 43, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (51, 2096, 44, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (52, 2096, 45, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (53, 2096, 46, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (54, 2096, 47, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (55, 2096, 48, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (56, 2096, 49, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (57, 2096, 50, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (58, 2096, 51, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (59, 2096, 52, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (60, 2096, 53, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (61, 2096, 54, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (62, 2096, 55, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (63, 2096, 56, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (64, 2096, 57, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (71, 2096, 61, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (72, 2096, 62, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (73, 2096, 63, '17', 3, 2, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (74, 2096, 64, '17', 3, 2, 2, 1, 1556606590477, 0);


-- migrate:down
DELETE FROM ReportTemplate WHERE id IN (18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,71,72,73,74);

DELETE FROM ReportMaterial WHERE id IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,61,62,63,64);
