-- migrate:up
-- @guanrong.yang
-- 修改Product
begin;
UPDATE Product set product_name = 'Roof-only Underwriting Report' where product_type = 1 and internal_type = 17;
INSERT INTO Product(product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time)
  values (1, 18, 'Full-scope Underwriting Report', 1, 75.0, '', '', 1571105029747, 1571105029747);

-- 修改SystemValue
UPDATE SystemValue set service_name = 'Report-Price-Roof-Only-Underwriting-Report' where service_name = 'Report-Price-Full-Scope-Underwriting-Report';
INSERT INTO SystemValue(country, service_name, service_value, label)
  values ('US', 'Report-Price-Full-Scope-Underwriting-Report', 75.0, '1 report 75.00');
commit;

-- 修改ServiceFeeType
INSERT INTO ServiceFeeType(service_fee_type, report_type) values (12, 18);

-- migrate:down
-- 修改Product
begin;
DELETE FROM Product where product_type = 1 and internal_type = 18;
UPDATE Product set product_name = 'Full-scope Underwriting Report' where product_type = 1 and internal_type = 17;

-- 修改SystemValue
DELETE FROM SystemValue where service_name = 'Report-Price-Full-Scope-Underwriting-Report';
UPDATE SystemValue set service_name = 'Report-Price-Full-Scope-Underwriting-Report' where service_name = 'Report-Price-Roof-Only-Underwriting-Report';

-- 修改ServiceFeeType
DELETE FROM ServiceFeeType where service_fee_type = 12 and report_type = 18;

commit;
