-- migrate:up
begin;

-- -【sandbox】---

set @s3prefixHostType = 'https://stag.bees360.ai.s3.amazonaws.com/';
set @s3prefixPathType = 'https://s3.amazonaws.com/stag.bees360.io/';

-- --- report_word_file_name ---------------
update ProjectReportFile set report_word_file_name = replace(report_word_file_name, @s3prefixHostType, '');
update ProjectReportFile set report_word_file_name = replace(report_word_file_name, @s3prefixPathType, '');

-- --- report_pdf_file_name ----------------
update ProjectReportFile set report_pdf_file_name = replace(report_pdf_file_name, @s3prefixHostType, '');
update ProjectReportFile set report_pdf_file_name = replace(report_pdf_file_name, @s3prefixPathType, '');

-- --- report_pdf_compressed ---------------
update ProjectReportFile set report_pdf_compressed = replace(report_pdf_compressed, @s3prefixHostType, '');
update ProjectReportFile set report_pdf_compressed = replace(report_pdf_compressed, @s3prefixPathType, '');

commit;

-- migrate:down

-- 上面是一个不可恢复操作，因此这里没有回退的sql
