-- migrate:up

ALTER TABLE Adjuster ADD active_license_state varchar(200) comment '执照合法的州,多个州用#分割' NOT NULL;
ALTER TABLE Adjuster ADD certifications varchar(200) comment '飞手有的证书' NOT NULL;
ALTER TABLE Adjuster ADD field_experience varchar(200) comment '场地经验' NOT NULL;
ALTER TABLE Adjuster ADD desk_experience varchar(200) comment '办公室经验' NOT NULL;
ALTER TABLE Adjuster ADD commercial_experience varchar(200) comment '商业经验' NULL;
ALTER TABLE Adjuster ADD xactimate_platform_experience varchar(200) comment 'xactimate平台经验 可以为空' ;
ALTER TABLE Adjuster ADD symbility_platform_experience varchar(200) comment 'symbility 平台经验可以为空';
ALTER TABLE Adjuster ADD other_platform_experience varchar(200) comment '其它平台经验 可以为空';
-- migrate:down

ALTER TABLE Adjuster DROP active_license_state ;
ALTER TABLE Adjuster DROP certifications;
ALTER TABLE Adjuster DROP field_experience;
ALTER TABLE Adjuster DROP desk_experience;
ALTER TABLE Adjuster DROP commercial_experience;
ALTER TABLE Adjuster DROP xactimate_platform_experience;
ALTER TABLE Adjuster DROP symbility_platform_experience;
ALTER TABLE Adjuster DROP other_platform_experience;
