-- migrate:up

DROP TABLE IF EXISTS `pilot`;
CREATE TABLE `pilot` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint(20) NOT NULL COMMENT 'user_id',
  `account` varchar(50) DEFAULT NULL COMMENT '账户名',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `email` varchar(50) DEFAULT NULL COMMENT 'email',
  `phone` varchar(50) DEFAULT NULL COMMENT 'phone',
  `travel_radius` int(10) DEFAULT NULL COMMENT '飞行半径',
  `license_key_urls` varchar(1000) DEFAULT NULL COMMENT '执照照片地址集',
  `license_issue_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '执照签发日期',
  `license_expiry_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '执照失效日期',
  `insurance_key_urls` varchar(1000) DEFAULT NULL COMMENT '保单照片集',
  `insurance_amount` decimal(12,9) DEFAULT NULL COMMENT '保单金额',
  `insurance_expiry_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '保单到期时间',
  `insurance_underwriting_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '保单承保时间',
  `is_enable` tinyint(2) DEFAULT '1' COMMENT 'data status: 1-enabled,2-inactived,0-disabled ',
  `is_delete` tinyint(2) DEFAULT '0' COMMENT 'data logic delete status: 1-deleted,0-not deleted ',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create_time',
  `create_by` int(20) DEFAULT NULL COMMENT 'created_by user',
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'update_time',
  `update_by` int(20) DEFAULT NULL COMMENT 'updated_by user',
  `version` int(10) DEFAULT '0' COMMENT 'data version',
  `license_number` varchar(50) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '执照号',
  `insurance_number` varchar(50) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '保单号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unqk_user_id` (`user_id`) USING BTREE,
  KEY `idx_email` (`email`),
  KEY `idx_phone` (`phone`),
  KEY `idx_user_name` (`user_name`),
  KEY `idx_account` (`account`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1050 DEFAULT CHARSET=utf8 COMMENT='飞手信息表-创建人:xjk'

-- migrate:down
DROP TABLE pilot;
