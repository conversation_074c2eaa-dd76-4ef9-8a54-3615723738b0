-- migrate:up
CREATE TABLE `AppDroneParameter`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `image_specifications` tinyint(4) NOT NULL DEFAULT 0 COMMENT 'picture specifications, 1,  16:9  2,  4:3',
  `house_height` double(20, 0) NULL DEFAULT NULL COMMENT 'the height of the house',
  `max_height` double(20, 0) NULL DEFAULT NULL COMMENT 'the max height of the obstacles',
  `roof_obstacle_height` double(20, 0) NULL DEFAULT NULL COMMENT 'maximum barrier height on the roof',
  `lat_lng_points` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'the points of the house',
  `land_points` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'the land',
  `drone_type` tinyint(4) NULL DEFAULT NULL COMMENT '1: the zoom 2: other',
  `type` tinyint(4) NULL DEFAULT NULL COMMENT '1:bees drone auto fly',
  `create_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `AppDroneParameter_ibfk_1`(`project_id`) USING BTREE,
  CONSTRAINT `AppDroneParameter_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- migrate:down
DROP TABLE IF EXISTS `AppDroneParameter`;
