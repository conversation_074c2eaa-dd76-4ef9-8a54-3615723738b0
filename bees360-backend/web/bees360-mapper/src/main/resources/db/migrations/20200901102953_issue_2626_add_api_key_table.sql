-- migrate:up
DROP TABLE IF EXISTS `OpenApiClient`;

CREATE TABLE `OpenApiClient` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `client_name` varchar(50) NOT NULL,
    `client_id` varchar(50) NOT NULL,
    `company_id` bigint NOT NULL,
    `client_secret` varchar(256) NOT NULL DEFAULT '',
    `grant_type` int(11) NOT NULL,
    `access_token_validate_seconds` bigint NOT NULL,
    `refresh_token_validate_seconds` bigint NOT NULL,
    `scopes` varchar(256) NOT NULL,
    `create_at` bigint NOT NULL,
    `update_at` bigint NOT NULL,
    `add_by` bigint NOT NULL,
    `update_by` bigint NOT NULL,
    PRIMARY KEY (`id`),
    unique index `uidx_client` (`client_id`)
)ENGINE=InnoDB AUTO_INCREMENT = 10001 DEFAULT CHARSET=utf8 COMMENT 'OpenApi Client';

-- migrate:down
DROP TABLE IF EXISTS `OpenApiClient`;
