-- migrate:up

create table DataDictionary (
    id bigint(20) primary key AUTO_INCREMENT,
    namespace varchar(50) not null comment 'namespace for the same category',
    code varchar(200) not null comment 'the code identify in the same namespace',
    name varchar(200) not null comment 'display or name of the value',
    value varchar(200) not null comment 'the value of this data',
    sequence int(11) not null  default  '0' comment 'the order number the this data',
    unique key (namespace, code)
) AUTO_INCREMENT = 1000;

insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('BeespilotTask', '1', 'Verify Address Task, it is a task to take a Address type images.', 'Verify Address', 100);
insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('BeespilotTask', '2', 'Take mobile images task.', 'Take Mobile Image', 200);
insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('BeespilotTask', '3', 'Take drone image task', 'Take Drone Image', 300);
insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('BeespilotTask', '4', 'Complete quizzes task.', 'Fill in Form', 400);

insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('ServiceType2Task', '0', 'Quick Inspect claim task id list', '1,2,3,4', 100);
insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('ServiceType2Task', '1', 'Full Adjustment task id list', '1,2,3,4', 200);
insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('ServiceType2Task', '2', 'Roof Only task id list', '1,2,3', 300);
insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('ServiceType2Task', '3', 'Exterior task id list', '1,2,3,4', 400);
insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('ServiceType2Task', '4', '4-point task id list', '1,2,3,4', 500);


# android
insert into DataDictionary (namespace, code, name, value, sequence)
VALUES ('ImageCompressConst', 'Underwriting-2-0', 'Underwriting project, Android platform Drone image compress radio', '0.5', 100);
insert into DataDictionary (namespace, code, name, value, sequence)
VALUES ('ImageCompressConst', 'Underwriting-2-1', 'Underwriting project, Android platform mobile image compress radio', '0.4', 200);

insert into DataDictionary (namespace, code, name, value, sequence)
VALUES ('ImageCompressConst', 'Claim-2-0', 'Claim project, Android platform Drone image compress radio', '1', 300);
insert into DataDictionary (namespace, code, name, value, sequence)
VALUES ('ImageCompressConst', 'Claim-2-1', 'Claim project, Android platform mobile image compress radio', '0.5', 400);


# ios
insert into DataDictionary (namespace, code, name, value, sequence)
VALUES ('ImageCompressConst', 'Underwriting-3-0', 'Underwriting project, IOS platform Drone image compress radio', '0.1', 500);
insert into DataDictionary (namespace, code, name, value, sequence)
VALUES ('ImageCompressConst', 'Underwriting-3-1', 'Underwriting project, IOS platform mobile image compress radio', '0.33', 600);
insert into DataDictionary (namespace, code, name, value, sequence)
VALUES ('ImageCompressConst', 'Claim-3-0', 'Claim project, IOS platform Drone image compress radio', '1', 700);
insert into DataDictionary (namespace, code, name, value, sequence)
VALUES ('ImageCompressConst', 'Claim-3-1', 'Claim project, IOS platform mobile image compress radio', '0.33', 800);




insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('DroneFlyPattern', 'Underwriting', 'Automatically flying', '2', 100);
insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('DroneFlyPattern', 'Claim', 'Automatically flying, Manually flying', '3', 200);


-- migrate:down

drop table DataDictionary;
