-- migrate:up
-- <AUTHOR>
update Product set caption = 'Fee includes:
1. Dispatch a pilot on Bees360 pilot network for inspection job.
2. Pilot payment
3. Logistics caused during inspection' where product_type = 2 and internal_type = 1;

-- PREMIUM_DAMAGE_ASSESSMENT_REPORT
update Product set caption = 'Full damage report, with categorized structures, elevations, and test squares.' where product_type = 1 and internal_type = 1;
-- PREMIUM_ROOF_MEASUREMENT_REPORT
update Product set caption = 'Detailed and accurate measurement report using drone images, providing area, pitches, and necessary values to form work order.
For total area under 40SQ.' where product_type = 1 and internal_type = 2;
-- MACRO_XML_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 4;
-- QUICK_DAMAGE_ASSESSMENT_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 5;
-- QUICK_ROOF_EVALUATION_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 7;
-- REALTIME_ROOF_INSPECTION_REPORT
update Product set caption = 'Real-time damage report generation and process.' where product_type = 1 and internal_type = 8;
-- SYMBILITY_SCAN_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 9;
-- WEATHER_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 10;
-- NARRATIVE_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 11;
-- DXF_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 12;
-- BID_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 13;
-- QUICK_SQUARE_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 14;
-- PROPERTY_IMAGE_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 15;
-- INFRARED_DAMAGE_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 16;
-- ROOF_ONLY_UNDERWRITING_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 17;
-- ROOF_ONLY_UNDERWRITING_REPORT
update Product set caption = '' where product_type = 1 and internal_type = 18;

-- migrate:down
