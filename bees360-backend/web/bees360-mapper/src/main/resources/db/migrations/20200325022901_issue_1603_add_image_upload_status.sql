-- migrate:up
ALTER TABLE Project ADD image_upload_status tinyint DEFAULT 0 NOT NULL comment '图片是否全部上传完成： 0未上传，1部分上传，2上传完成';
update Project P left join (select project_id, count(*) > 5
    as all_uploaded from ProjectImage group by project_id) PIC
        on P.project_id = PIC.project_id set P.image_upload_status = (
    case all_uploaded
    when true then 2
    when false then 1
    else 0
    end
);

-- migrate:down
ALTER TABLE Project DROP image_upload_status;
