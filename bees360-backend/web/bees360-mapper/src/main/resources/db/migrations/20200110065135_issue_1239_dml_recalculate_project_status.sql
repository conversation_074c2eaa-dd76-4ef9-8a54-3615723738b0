-- migrate:up
-- 用ProjectStatus.status的最大值更新Project.project_status
update Project P, (select project_id, max(status) as status from ProjectStatus group by project_id) PS
    set P.project_status = PS.status
where P.project_id = PS.project_id;

-- migrate:down
-- 利用ProjectStatus重新初始化Project.project_status，projectStatus中id最大的projectStatus就是最新的ProjectStatus
update Project P, (select * from ProjectStatus where id in (select max(id) as max_id from ProjectStatus group by project_id)) as PS
    set P.project_status = PS.status
where P.project_id = PS.project_id;
