-- migrate:up

insert into ProjectScore(project_id, score)
select
	project_id,
    case overallCondition
    when 'excellent' then 1
    when 'good' then 2
    when 'average' then 3
    when 'fair' then 4
    when 'poor' then 5
    else 0
    end as riskScore
from (
    select
        project_id,
		IFNULL(riskOverallCondition,
			IFNULL(bldgOverallCondition,
                IFNULL(roofOverallCondition,
                    IFNULL(exteriorOverallCondition,
                        IFNULL(interiorOverallCondition,'')
                    )
                )
            )
        ) as overallCondition
	from (
		select
		    S.project_id,
			case when JSON_VALID(S.risk) = 1 then replace(lower(JSON_EXTRACT(S.risk, '$.overallCondition')), '"', '') else null end as riskOverallCondition,
			case when JSON_VALID(S.bldg) = 1 then replace(lower(JSON_EXTRACT(S.bldg, '$.overallCondition')), '"', '') else null end as bldgOverallCondition,
			case when JSON_VALID(S.roof) = 1 then replace(lower(JSON_EXTRACT(S.roof, '$.overallCondition')), '"', '') else null end as roofOverallCondition,
			case when JSON_VALID(S.exterior) = 1 then replace(lower(JSON_EXTRACT(S.exterior, '$.overallCondition')), '"', '') else null end as exteriorOverallCondition,
			case when JSON_VALID(S.interior) = 1 then replace(lower(JSON_EXTRACT(S.interior, '$.overallCondition')), '"', '') else null end as interiorOverallCondition
		from (
			select
            R.project_id, R.summary,
			case when JSON_VALID(R.summary) = 1 then JSON_EXTRACT(R.summary, '$.risk') else null end as risk,
			case when JSON_VALID(R.summary) = 1 then JSON_EXTRACT(R.summary, '$.bldg') else null end as bldg,
			case when JSON_VALID(R.summary) = 1 then JSON_EXTRACT(R.summary, '$.roof') else null end as roof,
			case when JSON_VALID(R.summary) = 1 then JSON_EXTRACT(R.summary, '$.exterior') else null end as exterior,
			case when JSON_VALID(R.summary) = 1 then JSON_EXTRACT(R.summary, '$.interior') else null end as interior
			from ReportSummary R
			join Project P on R.project_id = P.project_id
			join Company C on P.insurance_company = C.company_id or P.repair_company = C.company_id
			where
				R.deleted = 0 and R.report_type = 18
				and R.project_id not in (select project_id from ProjectScore)
				and C.company_name = 'Swyfft Underwriting'
		) S
    ) X
    where X.riskOverallCondition is not null
    or X.bldgOverallCondition is not null
    or X.roofOverallCondition is not null
    or X.exteriorOverallCondition is not null
    or X.interiorOverallCondition is not null
) M
;

-- migrate:down
