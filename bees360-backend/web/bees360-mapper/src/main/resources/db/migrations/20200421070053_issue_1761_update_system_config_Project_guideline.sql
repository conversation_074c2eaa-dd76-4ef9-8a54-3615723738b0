-- migrate:up
update SystemConfig set config_value = '1. Elevation and Interior
    · Address verification
    · Front, Right, Rear, Left Elevation
        - Overview
        - Window, door, gutter, downspout, etc
    · Noted damages
    · Interior (if needed)

2. Roof
    · Overhead
    · Closeup
    · Birdsview
    · Zigzag' where config_key = 'project.defaultGuideline';
-- migrate:down

update SystemConfig set config_value =
'The image requirement varies between projects. Please follow the specific instructions per assignment. Below is a general guideline for acquiring images for Bees360.

1. Address Verification
Always the first photo taken.

2. Elevations
Capture the entire elevation by making sure there is ground and roof visible in the photo. If any additional hazards or damages are present, you must get an additional close up photo of each.
-- Risk/Front - Overview
-- Front/Right Corner - Overview
-- Right - Overview
-- Right/Rear Corner - Overview
-- Rear - Overview
-- Rear/Left Corner - Overview
-- Left - Overview
-- Left/Front Corner - Overview
-- Fences, swimming pools, hot tubs, animals, AC units, sheds, garages, type of foundation, etc.

3. Roof
Please refer to Resources tab at www.bees360.com for more details. Below are our standard flight patterns. Verify the requirements for each individual assignment prior to flight.
-- Risk/Front - Overview. If it is a roof only inspection we still need a risk photo.
-- Overview - Capture the entire roof from a 90° angle.
-- Birdsview - 360° around the property at a 45° angle (Oblique Pattern).
-- Lawnmower - Zig zag across the roof overlapping each image until all is photographed. The ideal height is 10-15 feet above the roof surface.
-- Snapshot - Close up of flashings, vents, transitions, etc.
' where config_key = 'project.defaultGuideline';
