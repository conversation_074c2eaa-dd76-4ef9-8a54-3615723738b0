-- migrate:up
-- @tang<PERSON><PERSON>ong
DROP TABLE IF EXISTS `Quiz`;
DROP TABLE IF EXISTS `CompanyQuiz`;
DROP TABLE IF EXISTS `ProjectQuiz`;

CREATE TABLE `Quiz`  (
    `quiz_id` bigint NOT NULL,
    `subject` varchar(100)  CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL comment '题干',
    `type` tinyint(4) comment '1 判断题（YES or NO), 2 单选题, 3 日期, 4 填空',
    `choices` varchar(100)  CHARACTER SET utf8 COLLATE utf8_general_ci comment '如果是判断或选择题，这里便是选项值以::分割，判断题为"NO::YES", NO在前，如果是日期，这里便是日期格式的字符串，如果是填空题的话这里可以是提示选项，多个以::分割',
    PRIMARY KEY (`quiz_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic
    comment '问卷';
create table `CompanyQuiz` (
    `quiz_id` bigint not null,
    `company_id` bigint not null,
    `sequence` integer not null comment '排序号, 只是起到排序作用',
    `create_time` bigint not null,
    PRIMARY KEY (`quiz_id`, `company_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic comment '公司问卷题';
create table `ProjectQuiz` (
    `project_quiz_id` bigint(20) not null AUTO_INCREMENT,
    `quiz_id` bigint(20) not null,
    `project_id` bigint(20) not null,
    `answer` varchar(100) not null comment '问卷答案，如果多个答案用::分割',
    `create_time` bigint(20) not null,
    PRIMARY KEY (`project_quiz_id`) USING BTREE

) ENGINE = InnoDB auto_increment=1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic comment '项目问卷';


insert into Quiz values (1, 'Interview conducted on Premises?', 1,  'NO::YES');
insert into Quiz values (2, 'Who was present during the inspection?', 3,  'INSURED::CONTRACTOR::TENANT::OTHERS');
insert into Quiz values (3, 'Who is occupying the property?', 3,  'INSURED::TENANT::OTHERS');
insert into Quiz values (4, 'When were damages discovered?', 4,  'MM/yyyy');
insert into Quiz values (5, 'Have damages been filed under a previous claim?', 1,  'NO::YES');
insert into Quiz values (6, 'How long has the insured owned the property?', 8,  'Year(s)::Month(s)' );
insert into Quiz values (7, 'Building occupancy?', 3,  'OCCUPIED::VACANT');
insert into Quiz values (8, 'Located on a paved road?', 1,  'NO::YES');
insert into Quiz values (9, 'Located in a gated community?', 1,  'NO::YES');


insert into CompanyQuiz values (1, 2294, 1, now());
insert into CompanyQuiz values (2, 2294, 2, now());
insert into CompanyQuiz values (3, 2294, 3, now());
insert into CompanyQuiz values (4, 2294, 4, now());
insert into CompanyQuiz values (5, 2294, 5, now());
insert into CompanyQuiz values (6, 2294, 6, now());

insert into CompanyQuiz values (1, 2096, 1, now());
insert into CompanyQuiz values (7, 2096, 2, now());
insert into CompanyQuiz values (8, 2096, 3, now());
insert into CompanyQuiz values (9, 2096, 4, now());

-- migrate:down
DROP TABLE IF EXISTS `Quiz`;
DROP TABLE IF EXISTS `CompanyQuiz`;
DROP TABLE IF EXISTS `ProjectQuiz`;
