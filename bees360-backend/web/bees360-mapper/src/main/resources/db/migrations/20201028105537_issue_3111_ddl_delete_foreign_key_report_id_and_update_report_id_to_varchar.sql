-- migrate:up
ALTER TABLE UserReadReport DROP FOREIGN KEY UserReadReport_ibfk_3;

ALTER TABLE ProjectReportFile MODIFY COLUMN report_id VARCHAR(256) NOT NULL;
ALTER TABLE UserReadReport MODIFY COLUMN report_id VARCHAR(256) NOT NULL;

-- migrate:down
UPDATE ProjectReportFile SET report_id=REPLACE(report_id,'A','') WHERE report_id LIKE 'A%';

ALTER TABLE ProjectReportFile MODIFY COLUMN report_id BIGINT(20) NOT NULL AUTO_INCREMENT;
ALTER TABLE UserReadReport MODIFY COLUMN report_id BIGINT(20) NOT NULL;

ALTER TABLE UserReadReport ADD CONSTRAINT UserReadReport_ibfk_3 FOREIGN KEY (report_id) REFERENCES ProjectReportFile(report_id);
