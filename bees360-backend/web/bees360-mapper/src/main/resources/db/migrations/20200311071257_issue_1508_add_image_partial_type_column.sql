-- migrate:up
ALTER TABLE ProjectImage ADD partial_type INTEGER(11) DEFAULT 1 COMMENT 'Image Partial View Type, 1:Roof, 2:Elevation, 3:Interior,4:Garage,5:APS,6:Others';
UPDATE ProjectImage SET partial_type=6, image_type=6 WHERE file_source_type=1 AND image_type=220;
UPDATE ProjectImage SET partial_type=5, image_type=6 WHERE file_source_type=1 AND image_type=205;
UPDATE ProjectImage SET partial_type=4, image_type=6 WHERE file_source_type=1 AND image_type=185;
UPDATE ProjectImage SET partial_type=3, image_type=6 WHERE file_source_type=1 AND image_type=95;
UPDATE ProjectImage SET partial_type=2, image_type=6 WHERE file_source_type=1 AND image_type IN (10,30,50,70);

ALTER TABLE HouseImageUnderwriting ADD partial_type INTEGER(11) DEFAULT 6 COMMENT 'Image Partial View Type, 1:Roof, 2:Elevation, 3:Interior,4:Garage,5:APS,6:Others';
UPDATE HouseImageUnderwriting SET partial_type=2 WHERE id IN (10,30,50,70);
UPDATE HouseImageUnderwriting SET partial_type=3 WHERE id = 95;
UPDATE HouseImageUnderwriting SET partial_type=4 WHERE id = 185;
UPDATE HouseImageUnderwriting SET partial_type=5 WHERE id = 205;
UPDATE HouseImageUnderwriting SET partial_type=6 WHERE id = 220;

-- migrate:down
UPDATE ProjectImage SET image_type=220 WHERE file_source_type=1 AND partial_type=6;
UPDATE ProjectImage SET image_type=205 WHERE file_source_type=1 AND partial_type=5;
UPDATE ProjectImage SET image_type=185 WHERE file_source_type=1 AND partial_type=4;
UPDATE ProjectImage SET image_type=95 WHERE file_source_type=1 AND partial_type=3;
UPDATE ProjectImage SET image_type=70 WHERE file_source_type=1 AND partial_type=2 AND orientation=3;
UPDATE ProjectImage SET image_type=50 WHERE file_source_type=1 AND partial_type=2 AND orientation=2;
UPDATE ProjectImage SET image_type=30 WHERE file_source_type=1 AND partial_type=2 AND orientation=4;
UPDATE ProjectImage SET image_type=10 WHERE file_source_type=1 AND partial_type=2 AND orientation=1;
ALTER TABLE ProjectImage DROP COLUMN partial_type;


ALTER TABLE HouseImageUnderwriting DROP COLUMN partial_type;
