-- migrate:up

-- guanrong.yang
-- // ProjectReportStatus
CREATE TABLE ProjectReportStatus (
    project_id BIGINT NOT NULL COMMENT '项目id',
    report_type INT NOT NULL COMMENT '报告类型',
    status TINYINT NOT NULL COMMENT '报告的状态',
    PRIMARY KEY (project_id, report_type),
    FOREIGN KEY (project_id) REFERENCES Project(project_id)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='报告状态表，为Project模块服务';

-- // ReportStatusConfig
CREATE TABLE ReportStatusConfig (
    status TINYINT NOT NULL COMMENT '状态id',
    status_name VARCHAR(100) NOT NULL COMMENT '状态名称',
    PRIMARY KEY (status)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT = '报告状态静态配置表';

-- 数据初始化

-- // ProjectReportStatus
-- 1. 利用(order, payment)中存现的报告类型，创建状态 wait for images
insert into ProjectReportStatus
  select U.project_id as project_id, S.report_type as report_type, 1 as status from UserPayment U, ServiceFeeType S
    where U.service_fee_type = S.service_fee_type and U.project_id != 0
  UNION
  select T.project_id as project_id, T.report_type as report_type, 1 as status from ReportTask T
    where T.is_deleted = 0;

-- 2. 将含有图片的项目的所有报告的状态设置为 processing
update ProjectReportStatus set status = 4 where project_id in (select distinct project_id from ProjectImage where is_deleted = 0 and file_source_type != 3);

-- 3. 将payment中出现的报告类型的状态设置为 complete
update ProjectReportStatus PS, (select U.project_id, S.report_type from UserPayment U, ServiceFeeType S where U.service_fee_type = S.service_fee_type) UP
  set PS.status = 7 where PS.project_id = UP.project_id and PS.report_type = UP.report_type;

-- // ReportStatusConfig
INSERT INTO ReportStatusConfig (status, status_name)
  VALUES (1, 'Wait for Images'), (4, 'Processing'), (7, 'Complete');

-- migrate:down

-- // ProjectReportStatus
DROP TABLE ProjectReportStatus;

-- // ReportStatusConfig
DROP TABLE ReportStatusConfig;
