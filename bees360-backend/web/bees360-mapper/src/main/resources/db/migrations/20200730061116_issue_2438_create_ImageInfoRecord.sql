-- migrate:up
create table ImageInfoRecord (
    id bigint(20) primary key auto_increment,
    project_id bigint(20) not null ,
    file_size bigint(20) not null ,
    original_file_name varchar(200) not null,
    file_source_type int(11) not null,
    partial_type int(11) not null,
    image_type int(11) not null,
    uploaded_to_S3 tinyint(1) not null  default '0' comment 'Indicates whether the image was uploaded to S3 or not',
    md5 char(32) comment '32bit lower case presentation MD5 of picture',
    unique index `unique_index_md5`(project_id, md5),
    unique index `unique_index_original_file_name`(project_id, original_file_name)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='Record picture upload information';

-- migrate:down
drop table ImageInfoRecord;
