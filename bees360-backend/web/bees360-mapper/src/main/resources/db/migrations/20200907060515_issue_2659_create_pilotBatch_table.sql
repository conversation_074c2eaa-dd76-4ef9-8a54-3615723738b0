-- migrate:up
CREATE TABLE `BeesPilotBatch`  (
  `batch_no` VARCHAR(30) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `base_pay` DECIMAL(20,2) DEFAULT NULL,
  `extra_pay` DECIMAL(20,2) DEFAULT NULL,
  `plan_payment_date` date DEFAULT NULL,
  `note` VARCHAR(500) DEFAULT NULL,
  `pay_time` bigint(20) DEFAULT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` timestamp not null default current_timestamp,
  `updated_at` timestamp default current_timestamp on update current_timestamp,
  PRIMARY KEY (`batch_no`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

CREATE TABLE `BeesPilotBatchItem`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` VARCHAR(30) NOT NULL,
  `project_id` bigint(20) NOT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp not null default current_timestamp,
  `updated_at` timestamp default current_timestamp on update current_timestamp,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_BeesPilotBatchItem_projectId` (`project_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;


-- migrate:down

drop table BeesPilotBatch;
drop table BeesPilotBatchItem;
