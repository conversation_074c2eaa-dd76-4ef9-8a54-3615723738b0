-- migrate:up
INSERT INTO bs_export_data (related_id, related_type, data_log) VALUES ( 'LIU', 'WEEK_NOTIFY_ADMIN_ROLE_WORK_EMAIL',  '<EMAIL>');
INSERT INTO bs_export_data (related_id, related_type, data_log) VALUES ( 'WU', 'WEEK_NOTIFY_ADMIN_ROLE_WORK_EMAIL', '<EMAIL>');
INSERT INTO bs_export_data (related_id, related_type, data_log) VALUES ( 'YANG', 'WEEK_NOTIFY_ADMIN_ROLE_WORK_EMAIL', 'ch<PERSON><EMAIL>');
INSERT INTO bs_export_data (related_id, related_type, data_log) VALUES ( 'LIN', 'WEEK_NOTIFY_ADMIN_ROLE_WORK_EMAIL', '<EMAIL>');


-- migrate:down
delete from bs_export_data where related_type = 'WEEK_NOTIFY_ADMIN_ROLE_WORK_EMAIL';
