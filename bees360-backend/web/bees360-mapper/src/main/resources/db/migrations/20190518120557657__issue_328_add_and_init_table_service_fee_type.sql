-- migrate:up
-- <AUTHOR>

CREATE TABLE ServiceFeeType (
    service_fee_type INT NOT NULL COMMENT "ServiceFeeType 编码值",
    report_type INT NOT NULL COMMENT "报告类型",

    PRIMARY KEY (service_fee_type, report_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

insert into ServiceFeeType(service_fee_type, report_type) values(1,1);
insert into ServiceFeeType(service_fee_type, report_type) values(1,2);
insert into ServiceFeeType(service_fee_type, report_type) values(2,2);
insert into ServiceFeeType(service_fee_type, report_type) values(3,5);
insert into ServiceFeeType(service_fee_type, report_type) values(4,13);
insert into ServiceFeeType(service_fee_type, report_type) values(5,14);
insert into ServiceFeeType(service_fee_type, report_type) values(6,7);
insert into ServiceFeeType(service_fee_type, report_type) values(7,8);
insert into ServiceFeeType(service_fee_type, report_type) values(8,1);
insert into ServiceFeeType(service_fee_type, report_type) values(9,15);
insert into ServiceFeeType(service_fee_type, report_type) values(10,16);
insert into ServiceFeeType(service_fee_type, report_type) values(11,17);

-- migrate:down
drop table ServiceFeeType;
