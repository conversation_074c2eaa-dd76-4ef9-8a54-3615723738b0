-- migrate:up
ALTER TABLE ReportMaterial MODIFY COLUMN data_source VARCHAR(1000);

INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (1, 60, NULL, '[com.bees360.util.report.ReportPageOne].initPageOne(pageParam.getWriter,pageParam.getCanvas,reportParam.getReportType,[com.bees360.util.report.CommonsUtil].getAddressArray(reportParam.getProject),reportParam.getCompany,reportParam.getCompany.getLogo,1b)', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (2, 60, NULL, '[com.bees360.util.report.measurement.MeasurementPdfReport].initPageTwo(pageParam.getCanvas,pageParam.getWriter,reportParam,0b,0b))', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (3, 60, NULL, '[com.bees360.util.report.measurement.MeasurementPdfReport].initNormalPage(pageParam.getCanvas,pageParam.getWriter,reportParam,0b,0b,reportParam.getMeasurementReportParam.getLengthMap,reportParam.getMeasurementReportParam.getPitchMap,reportParam.getMeasurementReportParam.getAreaMap,reportParam.getMeasurementReportParam.getPointList,reportParam.getMeasurementReportParam.getTypeList,reportParam.getMeasurementReportParam.getBorder,reportParam.getMeasurementReportParam.getEdgeCount,reportParam.getMeasurementReportParam.getAreas,reportParam.getMeasurementReportParam.getPitchs,reportParam.getMeasurementReportParam.getPitchFacetList))', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (4, 60, NULL, '[com.bees360.util.report.measurement.MeasurementPdfReport].initNormalPage(pageParam.getCanvas,pageParam.getWriter,reportParam,0b,0b,reportParam.getMeasurementReportParam.getLengthMap,reportParam.getMeasurementReportParam.getPitchMap,reportParam.getMeasurementReportParam.getAreaMap,reportParam.getMeasurementReportParam.getPointList,reportParam.getMeasurementReportParam.getTypeList,reportParam.getMeasurementReportParam.getBorder,reportParam.getMeasurementReportParam.getEdgeCount,reportParam.getMeasurementReportParam.getAreas,reportParam.getMeasurementReportParam.getPitchs,reportParam.getMeasurementReportParam.getPitchFacetList))', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (5, 20, ST_GeomFromText('POINT(20 725)'), 'reportParam.getCompany.getLogo', NULL, 0, NULL, NULL, NULL, NULL, NULL, 70, 45, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (6, 20, ST_GeomFromText('POINT(20 730)'), 'reportParam.getCompany.getLogo', NULL, 0, NULL, NULL, NULL, NULL, NULL, 70, 45, NULL);

INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (1, 1961, 1, '2', 1, 1, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (2, 1961, 2, '2', 2, 1, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (3, 1961, 5, '2', 2, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (4, 1961, 3, '2', 3, 1, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (5, 1961, 6, '2', 3, 1, 2, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (6, 1961, 3, '2', 4, 1, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (7, 1961, 6, '2', 4, 1, 2, 1, 1556606590477, 0);

-- migrate:down
ALTER TABLE ReportMaterial MODIFY COLUMN data_source VARCHAR(500);
DELETE FROM ReportMaterial WHERE id IN (1,2,3,4,5,6);
DELETE FROM ReportTemplate WHERE id IN (1,2,3,4,5,6,7);
