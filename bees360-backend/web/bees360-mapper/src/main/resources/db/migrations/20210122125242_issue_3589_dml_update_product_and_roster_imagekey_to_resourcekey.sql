-- migrate:up
INSERT INTO TempUrlToKey (origin_id, type, origin_url) SELECT p.product_id, 12, p.url FROM Product p
WHERE p.url LIKE '%s3.amazonaws.com%';

UPDATE Product SET url=replace(url, 'https://bees360.s3.amazonaws.com/', ''),
                   url=replace(url, 'https://s3.amazonaws.com/bees360/', '');

INSERT INTO TempUrlToKey (origin_id, type, origin_url) SELECT r.roster_id, 13, r.resume_url FROM Roster r
WHERE r.resume_url LIKE '%s3.amazonaws.com%';

UPDATE Roster SET resume_url=replace(resume_url, 'https://bees360.s3.amazonaws.com/', ''),
                  resume_url=replace(resume_url, 'https://s3.amazonaws.com/bees360/', '');

-- migrate:down
UPDATE Product a
    INNER JOIN (SELECT t.origin_id, t.origin_url FROM TempUrlToKey t WHERE t.type = 12) t2
SET a.url = t2.origin_url
WHERE a.product_id = t2.origin_id;

DELETE FROM TempUrlToKey WHERE type = 12;

UPDATE Roster a
    INNER JOIN (SELECT t.origin_id, t.origin_url FROM TempUrlToKey t WHERE t.type = 13) t2
SET a.resume_url = t2.origin_url
WHERE a.roster_id = t2.origin_id;

DELETE FROM TempUrlToKey WHERE type = 13;
