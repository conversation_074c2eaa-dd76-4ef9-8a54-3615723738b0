-- migrate:up
CREATE TABLE `ReportMaterial`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `material_type` tinyint(4) NOT NULL COMMENT '0:front template\r\n1:String 2:Damage title String data 3:measurement picture String data 4:measurement table data\r\n10:material 11:measurement length chart 12:measurementarea chart 13:measurement direction image\r\n20: image, 21: report one image, 22: report two image, 23:report close up image 24: report crop image\r\n30: line\r\n40: point\r\n50: background square',
  `position` point NULL,
  `data_source` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'for example reportParam.getCompany.getLogo',
  `check_is_implement` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `is_show_damage_type` tinyint(1) NULL DEFAULT NULL,
  `size` int(8) NULL DEFAULT NULL,
  `is_bold` tinyint(1) NULL DEFAULT NULL,
  `color` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'for example (64,64,64) is gray. default is (0, 0, 0) black',
  `align` tinyint(2) NULL DEFAULT NULL COMMENT '0:Element.ALIGN_LEFT, 1: Element.ALIGN_CENTER, 2: Element.ALIGN_RIGHT',
  `material_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `width` int(8) NULL DEFAULT NULL,
  `height` int(8) NULL DEFAULT NULL,
  `material_num` int(8) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 517 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

CREATE TABLE `ReportTemplate`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `company_id` bigint(20) NOT NULL,
  `material_id` bigint(20) NOT NULL,
  `report_types` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'for example 5,7,8 belong to  Preliminary Damage Assessment Report, Highfly Evaluation Report and Real-time Damage Assessment Report, 0 is all report',
  `page_number` int(8) NOT NULL COMMENT 'normal 1: page one, 2:page two, 3: page next',
  `page_type` tinyint(2) NOT NULL COMMENT '1:single page, 2: Multiple page, 3:default page',
  `sort` int(8) NOT NULL,
  `version` int(8) NOT NULL,
  `create_time` bigint(20) NOT NULL,
  `is_delete` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ReportTemplate_ibfk_1`(`company_id`) USING BTREE,
  INDEX `ReportTemplate_ibfk_2`(`material_id`) USING BTREE,
  CONSTRAINT `ReportTemplate_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `Company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ReportTemplate_ibfk_2` FOREIGN KEY (`material_id`) REFERENCES `ReportMaterial` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 85 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- migrate:down
drop table ReportMaterial;
drop table ReportTemplate;
