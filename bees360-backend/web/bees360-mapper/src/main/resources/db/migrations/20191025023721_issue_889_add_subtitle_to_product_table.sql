-- migrate:up
-- @guanrong.yang

-- 添加字段
ALTER TABLE Product ADD COLUMN subtitle VARCHAR(50) NOT NULL DEFAULT '' AFTER price;

-- 插入字段值
-- Premium Damage Assessment Report
UPDATE Product SET subtitle = 'Hail.Wind.Varies Type.' WHERE product_type = 1 and internal_type = 1;
-- Premium Measurement Report
UPDATE Product SET subtitle = 'High Precision.Fast Turnaround.' WHERE product_type = 1 and internal_type = 2;
-- Real-time Roof Inspection Report
UPDATE Product SET subtitle = 'Real-time.Pecise findings.' WHERE product_type = 1 and internal_type = 8;
-- Roof-only Underwriting Report
UPDATE Product SET subtitle = 'Make most valuable under your control.' WHERE product_type = 1 and internal_type = 17;
-- Full-scope Underwriting Report
UPDATE Product SET subtitle = 'More info.Less risk.' WHERE product_type = 1 and internal_type = 18;

-- migrate:down
ALTER TABLE Product DROP COLUMN subtitle;
