-- migrate:up
-- <AUTHOR>

/* 修改字段名或者属性 */
alter table UserPayment modify column total_fee_amount DECIMAL(20,3) NOT NULL DEFAULT '0.000';
alter table UserPayment modify column paid_service_fee_amount DECIMAL(20,3) NOT NULL DEFAULT '0.000';
alter table UserPayment modify column paid_time BIGINT NOT NULL;

/* 增加新的字段 */
-- User
alter table User add column stripe_customer VARCHAR(100) NOT NULL DEFAULT '';

-- UserPayment
alter table UserPayment add column operation_type INT NOT NULL DEFAULT 2 after payment_method;
alter table UserPayment modify column operation_type INT NOT NULL;

alter table UserPayment add column channel INT NOT NULL DEFAULT 0 after operation_type;

alter table UserPayment add column transaction_no VARCHAR(256) NOT NULL DEFAULT "" after channel;
alter table UserPayment add column description VARCHAR(256) NOT NULL DEFAULT "" after paid_time;

/* 修改字段值 */
-- 1. 描述字段
-- 支付服务
update UserPayment set description = (
	CASE service_fee_type
		WHEN 1 THEN 'Premium Roof Measurement Report And Damage Report'
		WHEN 2 THEN 'Premium Measurement Report'
		WHEN 3 THEN 'Preliminary Damage Assessment Report'
		WHEN 4 THEN 'On-Site Bidding Report'
		WHEN 5 THEN 'Real-Time Quick Square Report'
		WHEN 6 THEN 'Highfly Evaluation Report'
		WHEN 7 THEN 'Real-Time Damage Assessment Report'
		WHEN 8 THEN 'Premium Damage Assessment Report'
		WHEN 9 THEN 'Property Image Report'
		WHEN 10 THEN 'Infrared Damage Assessment Report'
		WHEN 11 THEN 'Full-scope Underwriting Report'
		ELSE '' END
	);
-- 充值
update UserPayment set description = "Recharge" where project_id = 0;

-- 2. 支付渠道 这部分会比较麻烦，需要从支付记录进行逐一的对比才能找到所有的值。Stripe 或者 余额的渠道需要通过另外的方式进行处理。

update UserPayment set channel = 0;
-- 设置为squareup渠道
update UserPayment set channel = 3 where payment_method = 'credit_card';
-- 设置为余额支付渠道
update UserPayment set channel = 1 where payment_method = 'wallet';
-- PayPal
update UserPayment set channel = 4 where payment_method = 'paypal';

-- 3. 操作类型
-- 设置操作类型为 充值
update UserPayment set operation_type = 1 where project_id = 0;
-- 设置操作类型为 支付
update UserPayment set operation_type = 2 where project_id != 0;

/* 移除不需要的字段 */
alter table UserPayment change column card_type deprecated_card_type varchar(50) DEFAULT NULL;
alter table UserPayment change column first_name deprecated_first_name varchar(50) DEFAULT NULL;
alter table UserPayment change column last_name deprecated_last_name varchar(50) DEFAULT NULL;
alter table UserPayment change column card_num deprecated_card_num varchar(50) DEFAULT NULL;
alter table UserPayment change column authorization_code deprecated_authorization_code varchar(4) DEFAULT NULL;
alter table UserPayment change column expiration_date deprecated_expiration_date int(11) DEFAULT NULL;
alter table UserPayment change column routing_number deprecated_routing_number varchar(50) DEFAULT NULL;
alter table UserPayment change column account_number deprecated_account_number varchar(50) DEFAULT NULL;
alter table UserPayment change column currency deprecated_currency varchar(10) DEFAULT NULL;
-- -------------------------------------------------------------------------------------------------------------------------- --

-- migrate:down
/* 修改字段名或者属性 */
alter table UserPayment modify column total_fee_amount decimal(20,3) DEFAULT '0.000';
alter table UserPayment modify column paid_service_fee_amount decimal(20,3) DEFAULT NULL;
alter table UserPayment modify column paid_time bigint(20) DEFAULT NULL;

/* 增加新的字段 */
-- User
alter table User drop column stripe_customer;

-- UserPayment
alter table UserPayment drop column operation_type;

alter table UserPayment drop column channel;

alter table UserPayment drop column transaction_no;
alter table UserPayment drop column description;

/* 修改字段值 */

/* 移除不需要的字段 */
-- 恢复字段
alter table UserPayment change column deprecated_card_type card_type varchar(50) DEFAULT NULL;
alter table UserPayment change column deprecated_first_name first_name varchar(50) DEFAULT NULL;
alter table UserPayment change column deprecated_last_name last_name varchar(50) DEFAULT NULL;
alter table UserPayment change column deprecated_card_num card_num varchar(50) DEFAULT NULL;
alter table UserPayment change column deprecated_authorization_code authorization_code varchar(4) DEFAULT NULL;
alter table UserPayment change column deprecated_expiration_date expiration_date int(11) DEFAULT NULL;
alter table UserPayment change column deprecated_routing_number routing_number varchar(50) DEFAULT NULL;
alter table UserPayment change column deprecated_account_number account_number varchar(50) DEFAULT NULL;
alter table UserPayment change column deprecated_currency currency varchar(10) DEFAULT NULL;
