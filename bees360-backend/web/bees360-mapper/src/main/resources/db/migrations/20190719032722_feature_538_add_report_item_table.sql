-- migrate:up
CREATE TABLE `CustomizedReportItem`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `report_type` tinyint(4) NOT NULL,
  `item_type` tinyint(4) NOT NULL COMMENT '1:root age,2:roof material,3:damage severity,4:description',
  `value` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `CustomizedReportItem_ibfk_1`(`project_id`) USING BTREE,
  CONSTRAINT `CustomizedReportItem_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `Project` (`project_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- migrate:down
DROP TABLE IF EXISTS `CustomizedReportItem`;
