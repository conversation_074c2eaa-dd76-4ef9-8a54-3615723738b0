-- migrate:up
CREATE TABLE `ProjectSyncLog` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) DEFAULT NULL,
  `sync_event` varchar(50) DEFAULT NULL,
  `status` int(2) DEFAULT 0 COMMENT '0:已同步，1:成功，-1失败',
  `createtm` timestamp NULL DEFAULT CURRENT_TIMESTAMP ,
  `updatetm` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- migrate:down
drop table ProjectSyncLog;
