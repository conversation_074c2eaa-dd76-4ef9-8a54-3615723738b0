-- migrate:up
CREATE TABLE `bs_export_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `related_id` varchar(50) DEFAULT NULL COMMENT 'data related id',
  `related_type` varchar(50) DEFAULT NULL COMMENT 'data related type eg:[project]',
  `data_log` text  DEFAULT NULL COMMENT 'data log json',
  `createtm` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'data create time',
  `updatetm` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'data update time',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `bs_export_data_type` (`related_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='export data log';

-- migrate:down
drop table bs_export_data;
