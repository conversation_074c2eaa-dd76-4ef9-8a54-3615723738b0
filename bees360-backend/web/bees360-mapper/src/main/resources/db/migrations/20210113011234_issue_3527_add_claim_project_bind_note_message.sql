-- migrate:up
INSERT INTO bs_export_data (related_id, related_type, data_log) VALUES ( 'AL', 'SWYFFT_PROJECT_BIND', 'Insurance Company: Swyfft/Clear Blue Ins Co');
INSERT INTO bs_export_data (related_id, related_type, data_log) VALUES ( 'SB', 'SWYFFT_PROJECT_BIND', 'Insurance Company: Swyfft/Benchmark Ins Co');


-- migrate:down

delete from bs_export_data where related_id = 'AL' and related_type = 'SWYFFT_PROJECT_BIND';
delete from bs_export_data where related_id = 'SB' and related_type = 'SWYFFT_PROJECT_BIND';
