-- migrate:up
-- @guanrong.yang

-- !!! 需要确保该脚本的执行时区为UTC，要么数据库连接使用UTC，要么数据设置默认时区为UTC

-- 关于美东时间
-- 地区：美国 纽约
-- 时区：UTC/GMT -5.00 (西五区)
-- 夏令时：美国东部标准时间正处于夏令时中，时区+1，与北京时间时差-1
-- 夏令时开始时间：2020-3-8 2:00:00
-- 夏令时结束时间：2020-11-1 2:00:00
-- 夏令时与UTC时差为 -4:00

-- 获取数据库全局时区和当前回话时区 SELECT @@GLOBAL.time_zone, @@SESSION.time_zone;

-- 导出数据保存
-- select project_id, policy_effective_date from Project where policy_effective_date is not null order by project_id;

-- 修改当前会话时区
SET time_zone = '+00:00';
ALTER TABLE Project ADD COLUMN policy_effective_date_date DATE DEFAULT NULL after policy_effective_date;
-- UTC 转 GMT: datetime -> date，丢失time部分
update Project set policy_effective_date_date = DATE(CONVERT_TZ(FROM_UNIXTIME((policy_effective_date / 1000)), '+00:00','-4:00'));
ALTER TABLE Project DROP COLUMN policy_effective_date;
ALTER TABLE Project CHANGE COLUMN policy_effective_date_date policy_effective_date DATE DEFAULT NULL;

-- migrate:down

-- 导出数据保存
-- select project_id, policy_effective_date from Project where policy_effective_date is not null order by project_id;

SET time_zone = '+00:00';
ALTER TABLE Project ADD COLUMN policy_effective_date_date BIGINT DEFAULT NULL after policy_effective_date;
-- GMT 转 UTC: 该脚本不能完全恢复原来的数据，因为原来的datetime -> date时会丢失time部分
update Project set policy_effective_date_date = UNIX_TIMESTAMP(CONVERT_TZ(policy_effective_date, '-04:00','+00:00')) * 1000;
ALTER TABLE Project DROP COLUMN policy_effective_date;
ALTER TABLE Project CHANGE COLUMN policy_effective_date_date policy_effective_date BIGINT DEFAULT NULL;
