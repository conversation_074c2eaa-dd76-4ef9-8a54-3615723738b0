-- migrate:up
CREATE TABLE Tax (
    tax_id INT NOT NULL AUTO_INCREMENT,
    tax_name VARCHAR(50) NOT NULL,
    product_type TINYINT(4) NOT NULL,
    tax_rate DECIMAL(7,3) NOT NULL,
    is_deleted TINYINT(1) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,

    PRIMARY KEY(tax_id)
) ENGINE=INNODB DEFAULT CHARSET=utf8 AUTO_INCREMENT=10001;

CREATE TABLE Product(
    product_id INT NOT NULL AUTO_INCREMENT,
    product_type TINYINT(4) NOT NULL,
    internal_type TINYINT(4) NOT NULL,
    product_name VARCHAR(100) NOT NULL,
    price_type TINYINT(4) NOT NULL,
    price DECIMAL(20,2) DEFAULT NULL COMMENT '商品的价格。如果商品的价格不是确定的，那么就记录为null，防止默认为0导致的计算错误。',
    caption VARCHAR(500) NOT NULL,
    url VARCHAR(256) NOT NULL,
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    PRIMARY KEY(product_id)
) ENGINE=INNODB DEFAULT CHARSET=utf8;


/** ======================= **/
insert into Tax(tax_name, product_type, tax_rate, is_deleted, created_time, updated_time) values ('Report Tax', 1, 8.25, 0, 1556187483366, 1556187483366);
insert into Tax(tax_name, product_type, tax_rate, is_deleted, created_time, updated_time) values ('Pilot Tax', 2, 6.25, 0, 1556187483366, 1556187483366);

/** ====================== **/
insert into Product(product_id, product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time)
values(2, 1,2, 'Premium Measurement Report', 1, 25, '', '', 1556187483366, 1556187483366);

insert into Product(product_id, product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time)
values(3, 1,5, 'Preliminary Damage Assessment Report', 1, 30, '', '', 1556187483366, 1556187483366);

insert into Product(product_id, product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time)
values(4, 1,13, 'On-Site Bidding Report', 1, 5, '', '', 1556187483366, 1556187483366);

insert into Product(product_id, product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time)
values(5, 1,14, 'Real-time Quick Square Report', 1, 0, '', '', 1556187483366, 1556187483366);

insert into Product(product_id, product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time)
values(6, 1,7, 'Highfly Evaluation Report', 1, 10, '', '', 1556187483366, 1556187483366);

insert into Product(product_id, product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time)
values(7, 1,8, 'Real-time Damage Assessment Report', 1, 10, '', '', 1556187483366, 1556187483366);

insert into Product(product_id, product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time)
values(8, 1,1, 'Premium Damage Assessment Report', 1, 40, '', '', 1556187483366, 1556187483366);

insert into Product(product_id, product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time)
values(9, 1,15, 'Property Image Report', 1, 40, '', '', 1556187483366, 1556187483366);

insert into Product(product_id, product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time)
values(10, 1,16, 'Infrared Damage Assessment Report', 3, null, '', '', 1556187483366, 1556187483366);

insert into Product(product_id, product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time)
values(11, 1,17, 'Full-scope Underwriting Report', 1, 50, '', '', 1556187483366, 1556187483366);

insert into Product(product_id, product_type, internal_type, product_name, price_type, price, caption, url, created_time, updated_time)
values(10001, 2, 1, 'normal pilot', 1, 75, '', '', 1556187483366, 1556187483366);

-- migrate:down
drop table Tax;
drop table Product;
