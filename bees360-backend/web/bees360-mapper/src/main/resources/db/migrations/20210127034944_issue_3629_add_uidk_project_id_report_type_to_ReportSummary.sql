-- migrate:up
alter table ReportSummary modify column deleted bigint NOT NULL DEFAULT '0' COMMENT 'deletion mark';
update ReportSummary set deleted = id where deleted != 0;
create unique index uidx_report_summary_p_r_d on ReportSummary (project_id, report_type, deleted);

-- migrate:down

drop index uidx_report_summary_p_r_d on ReportSummary;
update ReportSummary set deleted = 1 where deleted != 0;
alter table ReportSummary modify column deleted tinyint NOT NULL DEFAULT '0' COMMENT 'deletion mark';
