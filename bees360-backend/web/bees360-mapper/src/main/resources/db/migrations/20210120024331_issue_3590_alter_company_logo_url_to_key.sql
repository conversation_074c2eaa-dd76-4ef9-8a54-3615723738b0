-- migrate:up
DROP TABLE IF EXISTS `TempCompanyLogoUrlToKey`;

CREATE TABLE `TempCompanyLogoUrlToKey`(
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `origin_id` bigint(20)  DEFAULT null ,
    `origin_url` varchar(2200) NOT NULL DEFAULT '',
    PRIMARY KEY (`id`) USING BTREE
)ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8;

insert into TempCompanyLogoUrlToKey (origin_id, origin_url)
select company_id, logo from Company where logo like '%s3.amazonaws.com%';

update Company set logo = replace(logo, 'https://s3.amazonaws.com/bees360/', '')
 where logo like '%https://s3.amazonaws.com/bees360%';

update Company set logo = replace(logo, 'https://bees360.s3.amazonaws.com/', '')
 where logo like '%https://bees360.s3.amazonaws.com%';

-- migrate:down

update Company t1, TempCompanyLogoUrlToKey t2
set t1.logo = t2.origin_url
where t1.id = t2.origin_id;
