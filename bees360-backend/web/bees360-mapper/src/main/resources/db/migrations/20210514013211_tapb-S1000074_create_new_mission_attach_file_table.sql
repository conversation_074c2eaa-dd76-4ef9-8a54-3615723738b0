-- migrate:up
CREATE TABLE `MissionAttachFile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `deleted` tinyint(1) COMMENT '是否已被删除',
  `type` int(11) COMMENT '附件类型',
  `create_time` bigint(20) NULL,
  `upload_time` bigint(20) NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `AttachFile_project_id_IDX` (`project_id`,`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- migrate:down
drop table MissionAttachFile;
