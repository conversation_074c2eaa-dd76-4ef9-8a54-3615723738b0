-- migrate:up

insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('PreCheckoutReason', '1', 'BeesPilot™ is not working', 'BeesPilot™ is not working', 100);
insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('PreCheckoutReason', '2', 'Restricted airspace', 'Restricted airspace', 200);
insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('PreCheckoutReason', '3', 'No internet connection', 'No internet connection', 300);
insert into DataDictionary (namespace, code, name, value, sequence) VALUES ('PreCheckoutReason', '4', 'Others', 'Others', 400);
-- migrate:down
delete from DataDictionary where namespace = 'PreCheckoutReason' and code = '1';
delete from DataDictionary where namespace = 'PreCheckoutReason' and code = '2';
delete from DataDictionary where namespace = 'PreCheckoutReason' and code = '3';
delete from DataDictionary where namespace = 'PreCheckoutReason' and code = '4';
