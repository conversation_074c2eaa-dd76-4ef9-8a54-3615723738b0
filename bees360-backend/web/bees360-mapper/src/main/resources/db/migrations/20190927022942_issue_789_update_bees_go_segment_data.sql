-- migrate:up
UPDATE HouseImageSegmentType SET `name`='Asphalt/composition shingles installed over wood shake or shingles' WHERE id=9030;
UPDATE HouseImageSegmentType SET `name`='Chimney condition (rotted wood, crumbled masonry, leaning)' WHERE id=9040;

INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9050, 'Corroded metal roof covering', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9060, 'Curling, clawing, or splitting shingles', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9070, 'Evidence of impact damage (hail or wind borne debris) found on roof covering', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9080, 'Evidence of impact damage found on roof vents, gutters, or other equipment', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9090, 'Excessive moss build-up', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9100, 'Flashing missing or damaged', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9110, 'Granular loss', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9120, 'Gutters missing or damaged', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9130, 'Ice or snow build-up', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9140, 'Loose or hanging roof covering', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9150, 'Multiple layers, number observed', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9160, 'Missing roof covering', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9170, 'Roof leak confirmed by onsite contact', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9180, 'Roof mounted structure or equipment improperly installed', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9190, 'Rotting, decaying, or damaged wood roof covering', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9200, 'Sagging or bowed roof structure', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9210, 'Standing water', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9220, 'Tar patched roof covering', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9230, 'Tarp over roof area', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9240, 'Uneven or wavy roof covering', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9250, 'Other', 123, 1, NULL, NULL, 60, 1);

-- migrate:down
UPDATE HouseImageSegmentType SET `name`='Curling, clawing, or splitting shingles' WHERE id=9030;
UPDATE HouseImageSegmentType SET `name`='Excessive moss build-up' WHERE id=9040;

DELETE FROM HouseImageSegmentType WHERE id IN (9050, 9060, 9070, 9080, 9090, 9100, 9110, 9120, 9130, 9140, 9150, 9160, 9170, 9180, 9190, 9200, 9210, 9220, 9230, 9240, 9250);
