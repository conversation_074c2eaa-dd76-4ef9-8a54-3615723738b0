-- migrate:up
create table BeesPilotStatus
(
    status_id                bigint(20) primary key auto_increment,
    project_id               bigint(20) not null,
    mobile_check_status          tinyint(4) default 0 comment 'Mobile image check status:  0 None, 1 check in， 2 check out.',
    drone_check_status tinyint(4) default 0 comment 'Drone image check status: 0 None, 1 check in， 2 check out.',
    check_status   tinyint(4) default 0 comment 'Pilot check status: 0 None, 1 check in， 2 check out.',
    quiz_completed    tinyint(1) default 0 comment 'Indicate the pilot finished quiz or not.',
    image_uploaded           tinyint(1) default 0 comment 'Indicate all images have been successfully uploaded or not.',
    address_verified    tinyint(1) default 0 comment 'Indicate the address verified or not.',
    image_uploaded_time      bigint(20) comment 'The timestamp when all images have been successfully uploaded.',
    checkout_time       bigint(20) comment 'The timestamp when pilot check out this job ',
    last_update_time       bigint(20) ,
    unique index `unique_index_project_id` (project_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10001
  DEFAULT CHARSET = utf8mb4 COMMENT ='BeesPilot status';

insert into BeesPilotStatus(project_id, mobile_check_status, drone_check_status, check_status,
                            quiz_completed, image_uploaded, address_verified,
                            image_uploaded_time, checkout_time, last_update_time)
select project_id,2,2,2,
       1,1,1,
       max(created_time),max(created_time),max(created_time)
from (select project_id,created_time from ProjectStatus where status = 70 order by created_time desc) P1 group by project_id;

-- migrate:down
drop table if exists BeesPilotStatus;
