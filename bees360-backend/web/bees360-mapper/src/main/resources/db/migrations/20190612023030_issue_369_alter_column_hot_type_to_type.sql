-- migrate:up
ALTER TABLE HouseCategory CHANGE hot_type type INT(8) DEFAULT 0 NOT NULL COMMENT '10:roof, 20: hot roof, 30:elevation, 40 hot elevation';
UPDATE HouseImageSegmentType SET name='Dwelling' WHERE id=10;
UPDATE HouseImageSegmentType SET code_type=0 WHERE id=15 OR id=30;

-- migrate:down
ALTER TABLE HouseCategory CHANGE type hot_type INT(8) DEFAULT 0 NOT NULL;
UPDATE HouseImageSegmentType SET name='Main Structure' WHERE id=10;
UPDATE HouseImageSegmentType SET code_type=10 WHERE id=15 OR id=30;
