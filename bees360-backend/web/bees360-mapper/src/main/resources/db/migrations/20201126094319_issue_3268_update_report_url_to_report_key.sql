-- migrate:up
-- @guanrong.yang

-- https://s3.amazonaws.com/bees360/project/10016/measurement/1517297957153ImageWord.docx
-- https://bees360.s3.amazonaws.com/project/1000574/measurement/1532076400307RealtimeDamageReport.pdf

-- -【prod/stag/dev】---
begin;

Alter table ProjectReportFile add column bk_report_word_file_name varchar(256) DEFAULT NULL after report_word_file_name;
Alter table ProjectReportFile add column bk_report_pdf_file_name varchar(256) NOT NULL after report_pdf_file_name;
Alter table ProjectReportFile add column bk_report_pdf_compressed varchar(500) NOT NULL DEFAULT '' after report_pdf_compressed;

update ProjectReportFile set bk_report_word_file_name = report_word_file_name,
                             bk_report_pdf_file_name = report_pdf_file_name,
                             bk_report_pdf_compressed = report_pdf_compressed;

-- -【prod/stag/dev】---

set @s3prefixHostType = 'https://bees360.s3.amazonaws.com/';
set @s3prefixPathType = 'https://s3.amazonaws.com/bees360/';

-- --- report_word_file_name ---------------
update ProjectReportFile set report_word_file_name = replace(report_word_file_name, @s3prefixHostType, '');
update ProjectReportFile set report_word_file_name = replace(report_word_file_name, @s3prefixPathType, '');

-- --- report_pdf_file_name ----------------
update ProjectReportFile set report_pdf_file_name = replace(report_pdf_file_name, @s3prefixHostType, '');
update ProjectReportFile set report_pdf_file_name = replace(report_pdf_file_name, @s3prefixPathType, '');

-- --- report_pdf_compressed ---------------
update ProjectReportFile set report_pdf_compressed = replace(report_pdf_compressed, @s3prefixHostType, '');
update ProjectReportFile set report_pdf_compressed = replace(report_pdf_compressed, @s3prefixPathType, '');

-- -【sandbox】---

set @s3prefixHostType = 'https://stag.bees360.ai.s3.amazonaws.com/';
set @s3prefixPathType = 'https://s3.amazonaws.com/stag.bees360.ai/';

-- --- report_word_file_name ---------------
update ProjectReportFile set report_word_file_name = replace(report_word_file_name, @s3prefixHostType, '');
update ProjectReportFile set report_word_file_name = replace(report_word_file_name, @s3prefixPathType, '');

-- --- report_pdf_file_name ----------------
update ProjectReportFile set report_pdf_file_name = replace(report_pdf_file_name, @s3prefixHostType, '');
update ProjectReportFile set report_pdf_file_name = replace(report_pdf_file_name, @s3prefixPathType, '');

-- --- report_pdf_compressed ---------------
update ProjectReportFile set report_pdf_compressed = replace(report_pdf_compressed, @s3prefixHostType, '');
update ProjectReportFile set report_pdf_compressed = replace(report_pdf_compressed, @s3prefixPathType, '');

commit;
-- -------------------------------------------- THE BOTTOM LINE ---------------------------------------------------

-- migrate:down
begin;

update ProjectReportFile set report_word_file_name = bk_report_word_file_name,
                             report_pdf_file_name = bk_report_pdf_file_name,
                             report_pdf_compressed = bk_report_pdf_compressed;

Alter table ProjectReportFile drop column bk_report_word_file_name;
Alter table ProjectReportFile drop column bk_report_pdf_file_name;
Alter table ProjectReportFile drop column bk_report_pdf_compressed;

commit;
