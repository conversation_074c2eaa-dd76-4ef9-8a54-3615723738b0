-- migrate:up
INSERT INTO TempUrlToKey (origin_varchar_id, type, origin_url ) SELECT pi.image_id, 8, pi.file_name FROM ProjectImage pi
WHERE pi.file_name LIKE '%s3.amazonaws.com%';
INSERT INTO TempUrlToKey (origin_varchar_id, type, origin_url ) SELECT pi.image_id, 9, pi.file_name_lower_resolution FROM ProjectImage pi
WHERE pi.file_name_lower_resolution LIKE '%s3.amazonaws.com%';
INSERT INTO TempUrlToKey (origin_varchar_id, type, origin_url ) SELECT pi.image_id, 10, pi.file_name_middle_resolution FROM ProjectImage pi
WHERE pi.file_name_middle_resolution LIKE '%s3.amazonaws.com%';

UPDATE ProjectImage SET file_name=replace(file_name, 'https://stag.bees360.io.s3.amazonaws.com/', ''),
                        file_name=replace(file_name, 'https://s3.amazonaws.com/stag.bees360.io/', ''),
                        file_name_lower_resolution=replace(file_name_lower_resolution, 'https://stag.bees360.io.s3.amazonaws.com/', ''),
                        file_name_lower_resolution=replace(file_name_lower_resolution, 'https://s3.amazonaws.com/stag.bees360.io/', ''),
                        file_name_middle_resolution=replace(file_name_middle_resolution, 'https://stag.bees360.io.s3.amazonaws.com/', ''),
                        file_name_middle_resolution=replace(file_name_middle_resolution, 'https://s3.amazonaws.com/stag.bees360.io/', '');

UPDATE ProjectImage SET file_name=replace(file_name, 'https://bees360.s3.amazonaws.com/', ''),
                        file_name=replace(file_name, 'https://s3.amazonaws.com/bees360/', ''),
                        file_name_lower_resolution=replace(file_name_lower_resolution, 'https://bees360.s3.amazonaws.com/', ''),
                        file_name_lower_resolution=replace(file_name_lower_resolution, 'https://s3.amazonaws.com/bees360/', ''),
                        file_name_middle_resolution=replace(file_name_middle_resolution, 'https://bees360.s3.amazonaws.com/', ''),
                        file_name_middle_resolution=replace(file_name_middle_resolution, 'https://s3.amazonaws.com/bees360/', '');

-- migrate:down

UPDATE ProjectImage a
    INNER JOIN (SELECT t.origin_varchar_id, t.origin_url FROM TempUrlToKey t WHERE t.type = 8) t2
SET a.file_name = t2.origin_url
WHERE a.image_id = t2.origin_varchar_id;
UPDATE ProjectImage a
    INNER JOIN (SELECT t.origin_varchar_id, t.origin_url FROM TempUrlToKey t WHERE t.type = 9) t2
SET a.file_name_lower_resolution = t2.origin_url
WHERE a.image_id = t2.origin_varchar_id;
UPDATE ProjectImage a
    INNER JOIN (SELECT t.origin_varchar_id, t.origin_url FROM TempUrlToKey t WHERE t.type = 10) t2
SET a.file_name_middle_resolution = t2.origin_url
WHERE a.image_id = t2.origin_varchar_id;
