-- migrate:up
-- @yangguanrong
CREATE TABLE ExternalClient (
    client_id VARCHAR(50) NOT NULL UNIQUE,
    client_integrated VARCHAR(50) DEFAULT NULL,
    oauth_username VARCHAR(50) NOT NULL,
    oauth_password VARCHAR(200) NOT NULL,
    created_time BIGINT NOT NULL,
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT 'undeleted if 0, deleted if 1',
    PRIMARY KEY (client_id)
) ENGINE = InnoDB CHARACTER SET = utf8;

CREATE TABLE SymbilityConnect (
    id INTEGER NOT NULL AUTO_INCREMENT,
    company_id BIGINT NOT NULL,
    symbility_id VARCHAR(100) NOT NULL,
    symbility_company_name VARCHAR(100) NOT NULL DEFAULT '',
    user_id_bound BIGINT NOT NULL,
    created_time BIGINT NOT NULL,
    deleted INTEGER NOT NULL DEFAULT 0 COMMENT 'undeleted if 0, deleted if equals to id',
    PRIMARY KEY (id),
    UNIQUE (symbility_id, deleted)
) ENGINE = InnoDB AUTO_INCREMENT = 10000 CHARACTER SET = utf8;

CREATE TABLE ProjectSymbilityClaim (
    project_id BIGINT NOT NULL,
    claim_unique_id VARCHAR(100) NOT NULL COMMENT 'unique_id of Symbility Claim.',
    PRIMARY KEY (project_id, claim_unique_id)
) ENGINE = InnoDB AUTO_INCREMENT = 10000 CHARACTER SET = utf8 COMMENT 'connect Project and Symbility Claim.';

-- migrate:down

DROP TABLE ExternalClient;
DROP TABLE SymbilityConnect;
DROP TABLE ProjectSymbilityClaim;
