-- migrate:up
create TABLE IF NOT EXISTS Bees360.`ProjectInspection` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
	`inspection_code` varchar(6) NOT NULL,
	`project_id` bigint(20) NOT NULL,
	`create_time` bigint(20) NOT NULL,
	`is_deleted` tinyint(1) NOT NULL DEFAULT '0' comment '0-not deleted, 1 deleted',
	`expiration_time` bigint(20) NOT NULL,
	 PRIMARY KEY (`id`) USING BTREE,
	index `index_project_id` (`project_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic
comment '使用IBees拍摄房屋的验证码文档表';


-- migrate:down
drop table if EXISTS `ProjectInspection`;
