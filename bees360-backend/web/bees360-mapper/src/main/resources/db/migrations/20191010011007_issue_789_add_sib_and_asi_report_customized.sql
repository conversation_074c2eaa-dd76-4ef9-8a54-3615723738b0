-- migrate:up
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (7, 60, NULL, '[com.bees360.util.report.company.sib.SIBQuickDamagePdfReport].createReportPdf(reportParam,pageParam,0b)', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (8, 60, NULL, '[com.bees360.util.report.company.sib.SIBQuickDamagePdfReport].createReportPdf(reportParam,pageParam,1b)', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (9, 60, NULL, '[com.bees360.util.report.company.asi.ASIQuickDamagePdfReport].createReportPdf(reportParam,pageParam)', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (8, 1260, 7, '17,18', 1, 1, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (9, 1849, 8, '17,18', 1, 1, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (10, 1313, 9, '17,18', 1, 1, 1, 1, 1556606590477, 0);

-- migrate:down
DELETE FROM ReportTemplate WHERE id IN (8,9,10);
DELETE FROM ReportMaterial WHERE id IN (7,8,9);
