-- migrate:up
INSERT INTO User(user_id, first_name, last_name, email, qr_code, phone, address, city, state, country, zip_code, avatar, company_id, employee_id, password, last_login_time, registration_time, active_status, gps_location, most_recent_gps_location, roles, role_application_status, certificate_list, discount_percent, free_on_trail_project_num, new_customer_discount_percent, new_customer_discount_project_num, wallet_balance, commission_balance, currency, inspection_service, highfly_service, order_service_time, travel_radius, source)
VALUES (9999, 'BeesPilot System', '', '<EMAIL>', NULL, '', '', '', '', '', '', 'https://s3.amazonaws.com/bees360/user/10005/avatar/1517273056251.png', 1260, '', 'vql7+ys5zHU7A6kxHep80ItQNyzCY2InWW2daXpzJ4c4bK+OXe2zMyRlHc88Ggab', 1555725399170, 1520476419365, 1, ST_GeomFromText('POINT(0 0)'), ST_GeomFromText('POINT(0 0)'), 4611686018427388161, 0, NULL, 0.000, 0, 0.000, 0, 0.000, NULL, NULL, NULL, NULL, 0, 0, 0);

-- migrate:down
delete from User where `user_id` = 9999;
