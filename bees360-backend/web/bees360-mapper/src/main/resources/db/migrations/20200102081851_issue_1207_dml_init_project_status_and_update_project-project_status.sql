-- migrate:up
-- 新的ProjectStatus
set @projectCreated = 10,
    @customerContacted = 30,
    @assignedToPilot = 50,
    @siteInspected = 70,
    @returnedToClient = 90;

set @imageUploadEvent = 26;
-- pilot 角色code
set @pilotRole = 2;

set @reportGenerationStatusApproved = 3;
-- event history 中所有和report approved相关的status
set @DAMAGE_REPORT_APPROVED = 111,
    @MEASUREMENT_REPORT_APPROVED = 122,
    @IMAGE_REPORT_APPROVED = 125,
    @ONSITE_DAMAGE_REPORT_APPROVED = 128,
    @ONSITE_IMAGE_REPORT_APPROVED = 131,
    @DAMAGE_ASSESSMENT_REPORT_APPROVED = 134,
    @APP_DAMAGE_REPORT_APPROVED = 152,
    @WEATHER_REPORT_APPROVED = 137,
    @NARRATIVE_REPORT_APPROVED = 140,
    @DXF_REPORT_APPROVED = 144,
    @BIDDING_APPROVED = 149,
    @QUICK_SQUARE_APPROVED = 165,
    @REALTIME_APP_DAMAGE_REPORT_APPROVED = 163,
    @INFRARED_DAMAGE_REPORT_APPROVED = 170,
    @ROOF_ONLY_UNDERWRITING_REPORT_APPROVED = 174,
    @FULL_SCOPE_UNDERWRITING_REPORT_APPROVED = 178;

insert into ProjectStatus(project_id, user_id, status, created_time)
-- project created      // 利用Project创建信息构建projectCreated状态，且ProjectStatus没有projectCreated的记录时写入
select project_id, created_by as user_id, @projectCreated as status, created_time
from Project
where project_id not in
    (select project_id from ProjectStatus where status = @projectCreated)

-- customer contacted   // 无需初始化
UNION ALL

-- assigned to pilot    // 利用Project中的Pilot成员信息构建assignedToPilot状态，且ProjectStatus没有assignedToPilot的记录时写入
select project_id, created_by as user_id, @assignedToPilot as status, created_time
from Member
where is_deleted = 0 and role = @pilotRole
and project_id not in (select project_id from ProjectStatus where status = @assignedToPilot)

UNION ALL

-- site inspected       // 利用最先写入的imageUpload的EventHistory信息构建siteInspected状态，要求图片张数大于或者等于5，且ProjectStatus没有siteInspected的记录时写入
select E.project_id, E.modified_by as user_id, @siteInspected as status, E.created_time
from EventHistory E, (select min(event_id) event_id from EventHistory where status = @imageUploadEvent group by project_id) ME
where E.event_id = ME.event_id and E.project_id in
    (select project_id from ProjectImage where is_deleted = 0 group by project_id having count(*) >= 5)
    and E.project_id not in (select project_id from ProjectStatus where status = @siteInspected)

UNION ALL

-- return to client     // 利用最新approved的报告的EventHistory信息进行构建returnedToClient状态，要求必须有下订单，且订单中的报告均已经approved，
--                      // ，且ProjectStatus没有returnedToClient的记录时写入
select E.project_id, E.modified_by as user_id, @returnedToClient as status, E.created_time
from EventHistory E, (select max(event_id) event_id from EventHistory where status in (
       @DAMAGE_REPORT_APPROVED,
       @MEASUREMENT_REPORT_APPROVED,
       @IMAGE_REPORT_APPROVED,
       @ONSITE_DAMAGE_REPORT_APPROVED,
       @ONSITE_IMAGE_REPORT_APPROVED,
       @DAMAGE_ASSESSMENT_REPORT_APPROVED,
       @APP_DAMAGE_REPORT_APPROVED,
       @WEATHER_REPORT_APPROVED,
       @NARRATIVE_REPORT_APPROVED,
       @DXF_REPORT_APPROVED,
       @BIDDING_APPROVED,
       @QUICK_SQUARE_APPROVED,
       @REALTIME_APP_DAMAGE_REPORT_APPROVED,
       @INFRARED_DAMAGE_REPORT_APPROVED,
       @ROOF_ONLY_UNDERWRITING_REPORT_APPROVED,
       @FULL_SCOPE_UNDERWRITING_REPORT_APPROVED
    ) group by project_id) ME
where E.event_id = ME.event_id and E.project_id in (
    select RF.project_id from ReportTask RT left join ProjectReportFile RF
        on RT.project_id = RF.project_id and RT.report_type = RF.report_type
    where RT.is_deleted = 0 and RF.is_deleted = 0 and RF.generation_status = @reportGenerationStatusApproved
        and RT.project_id not in
            (select RT.project_id from ReportTask RT left join ProjectReportFile RF
                on RT.project_id = RF.project_id and RT.report_type = RF.report_type
            where RT.is_deleted = 0 and RF.is_deleted = 0 and RF.generation_status != @reportGenerationStatusApproved)
    )
    and E.project_id not in (select project_id from ProjectStatus where status = @returnedToClient)

-- 按照时间进行排序，确保生成的id也是按照时间进行升序的
ORDER BY created_time ASC, status ASC;

-- 利用ProjectStatus重新初始化Project.project_status，上面的脚本已经确保一个project中id最大的projectStatus就是最新的ProjectStatus
update Project P, (select * from ProjectStatus where id in (select max(id) as max_id from ProjectStatus group by project_id)) as PS
set P.project_status = PS.status where P.project_id = PS.project_id;

-- migrate:down

set @projectCreated = 10,
    @customerContacted = 30,
    @assignedToPilot = 50,
    @siteInspected = 70,
    @returnedToClient = 90;

-- project created
delete from ProjectStatus where status = @projectCreated;

-- customer contacted // 没有初始化，无需回滚

-- assigned to pilot
delete from ProjectStatus where status = @assignedToPilot;

-- site inspected
delete from ProjectStatus where status = @siteInspected;

-- return to client
delete from ProjectStatus where status = @returnedToClient;

-- 恢复Project.project_status // 由于该值只有一个状态，因而被覆盖的值无法进行恢复
