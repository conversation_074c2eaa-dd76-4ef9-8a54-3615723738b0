-- migrate:up
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (8990, 'Excellent', 97, 1, NULL, NULL, 60, 1);

UPDATE HouseImageSegmentType SET name='Single' WHERE id=12000;
UPDATE HouseImageSegmentType SET name='Double' WHERE id=12010;
UPDATE HouseImageSegmentType SET name='Multilayer' WHERE id=12020;

#Roof
UPDATE HouseCategory SET type=10  WHERE category IN ( 'FRM', 'MTL', 'PNT', 'RFG', 'SDG', 'SFG', 'TMB' ) AND version = 2 AND id < 222200;
#Ridge cap
UPDATE HouseCategory SET type=15 WHERE id in (180960,180970,180980,180990,181000,181010,181020,181030,181060,181070);
#Drip edge
UPDATE HouseCategory SET type=20 WHERE id in (178990,179000,179010,179020,179030);
#Valley
UPDATE HouseCategory SET type=25 WHERE id in (178650,178660,182930,182940,182950,182960,182970);
#Exhaust cap
UPDATE HouseCategory SET type=30 WHERE id in (182700,182710);
#Pipe jack
UPDATE HouseCategory SET type=35 WHERE id in (179470,179480,179490,179500,179510,179520,180300);
#Attic vent
UPDATE HouseCategory SET type=40 WHERE id in (180860,180870,180880,180890,180900,180910);
#Attic vents
UPDATE HouseCategory SET type=45 WHERE id in (180860,180870,180880,180890,180900,180910,184670,184680,184690,184700,184710,184720,184730,184740,184750,184760);
#Ridge Vents
UPDATE HouseCategory SET type=50 WHERE id in (180320,180330,180340,180350,182640,182810,182820);
#Flashing
UPDATE HouseCategory SET type=55 WHERE id in (179240,179250,179260,179410,179420,179430,179470,179540,179550,179560,179570);
#Roof Vents
UPDATE HouseCategory SET type=60 WHERE id in (182650,182660,182670,182680,182690,182760,182770,182780,182790,182800,182830,182840,182860,182870,182880);
#Ventilator
UPDATE HouseCategory SET type=65 WHERE id in (180320,182760,182890,182900);

#Elevation
UPDATE HouseCategory SET type=100 WHERE category NOT IN ( 'FRM', 'MTL', 'PNT', 'RFG', 'SDG', 'SFG', 'TMB' ) AND version = 2 AND id < 222200;
#Window screen
UPDATE HouseCategory SET type=105 WHERE id in (211690,211700,211710,211720,211730,211740,211750,211760,211770);
#Window reglaze
UPDATE HouseCategory SET type=110 WHERE id in (211500,211510,211520,211530,211540,211550,211560,211570);
#Window beading
UPDATE HouseCategory SET type=115 WHERE id in (211480,211490);
#Gutters/Downspouts
UPDATE HouseCategory SET type=120 WHERE id in (179000,185340,185350,185390,185400,185410,185420,185450,185460,185470,185480,185490,185500,185660);
#A/C fins
UPDATE HouseCategory SET type=125 WHERE id in (135280,135290,135300,135310);
#Fascia
UPDATE HouseCategory SET type=130 WHERE id in (185110,185120,185130,185140,185150,185160,185170,185180,185190,185200,185700,185710,185720,185730,186010,186020,186030,186040,186050);
#Windows
UPDATE HouseCategory SET type=135 WHERE id in (209330,209340,209350,213960,213970,214120,214130,214870,214880,215060,215070,215080,215360,215370,215380,216060,216070,216080);

UPDATE HouseImageSegmentType SET `name`='General Information' WHERE id=75;
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9990, '25 year 3 Tab', 100, 1, NULL, NULL, 60, 1);
UPDATE HouseImageSegmentType SET `code_type`=0 WHERE id=130;
DELETE FROM HouseImageSegmentType WHERE id in (13010,13020);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (132, 'Valley Lining', 0, 0, 5, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (13010, 'Ice & water Shield', 132, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (13020, 'Valley Metal', 132, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (13030, 'W- Valley', 132, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (13040, '90lb Felt', 132, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (13050, 'Other', 132, 1, NULL, NULL, 60, 1);
UPDATE HouseImageSegmentType SET `name`='overhang in inches' WHERE id=13520;
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (170, 'High profile ridge cap', 0, 0, 6, NULL, 60, 1);

ALTER TABLE HouseSegmentValue ADD COLUMN type int(8) DEFAULT 100 NOT NULL COMMENT 'the HouseCategory.type 10:roof, 15: Ridge cap, 20: Drip edge, 25:Valley, 30 Exhaust cap, 35: Pipe jack, 40: Attic vent, 45: Attic vents, 50: Ridge Vents, 55: Flashing, 60: Roof Vents, 65: Ventilator, 100: Elevation, 105: Window screen, 110: Window reglaze, 115: Window beading, 120: Gutters/Downspouts, 125: A/C fins, 130: Fascia, 135: Windows, 200: close up or overview(Unmodifiable)';

INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222200, '202200', 'RFG', 2000, '', '', 202200, 'Roof layer', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222210, '202210', 'RFG', 2010, '', '', 202210, 'Roofing closeups', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222220, '202220', 'RFG', 2020, '', '', 202220, 'Shingle gauge', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222230, '202230', 'RFG', 2030, '', '', 202230, 'Test Square', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222240, '202240', 'RFG', 2040, '', '', 202240, 'Front overview', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222250, '202250', 'RFG', 2050, '', '', 202250, 'Right overview', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222260, '202260', 'RFG', 2060, '', '', 202260, 'Back overview', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222270, '202270', 'RFG', 2070, '', '', 202270, 'Left overview', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222310, '202310', 'XST', 2310, '', '', 202310, 'Overview', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222320, '202320', 'XST', 2320, '', '', 202320, 'Close up', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222330, '202330', 'XST', 2330, '', '', 202330, 'Address verification', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222340, '202340', 'XST', 2340, '', '', 202340, 'Risk overview', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222350, '202350', 'XST', 2350, '', '', 202350, 'Front overview', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222360, '202360', 'XST', 2360, '', '', 202360, 'Right overview', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222370, '202370', 'XST', 2370, '', '', 202370, 'Back overview', NULL, '', 2, 200);
INSERT INTO `HouseCategory`(`id`, `selector`, `category`, `category_index`, `category_code`, `subcategory`, `subcategory_index`, `description`, `additional`, `subcategory_code`, `version`, `type`) VALUES (222380, '202380', 'XST', 2380, '', '', 202380, 'Left overview', NULL, '', 2, 200);

INSERT INTO `HouseCategoryVersion`(`id`, `type`, `version`) VALUES (2, 1, 3);
UPDATE HouseCategory SET version=3 WHERE version=2;

-- migrate:down
DELETE FROM `HouseImageSegmentType` WHERE id=8990;

UPDATE HouseImageSegmentType SET name='Single Layer' WHERE id=12000;
UPDATE HouseImageSegmentType SET name='Double Layer' WHERE id=12010;
UPDATE HouseImageSegmentType SET name='Multi Layer' WHERE id=12020;

#Ridge cap
UPDATE HouseCategory SET type=0 WHERE version = 2;

UPDATE HouseImageSegmentType SET `name`='General' WHERE id=75;
DELETE FROM HouseImageSegmentType WHERE id=9990;
UPDATE HouseImageSegmentType SET `code_type`=60 WHERE id=130;
DELETE FROM HouseImageSegmentType WHERE id in (132,13010,13020,13030,13040,13050,170);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (13000, 'Yes', 130, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (13010, 'No', 130, 1, NULL, NULL, 60, 1);
UPDATE HouseImageSegmentType SET `name`='Overhang Measurement' WHERE id=13520;

ALTER TABLE HouseSegmentValue DROP COLUMN type;
DELETE FROM HouseCategory WHERE id in (222200,222210,222220,222230,222240,222250,222260,222270,222310,222320,222330,222340,222350,222360,222370,222380);

DELETE FROM HouseCategoryVersion WHERE id=2;
UPDATE HouseCategory SET version=2 WHERE version=3;
