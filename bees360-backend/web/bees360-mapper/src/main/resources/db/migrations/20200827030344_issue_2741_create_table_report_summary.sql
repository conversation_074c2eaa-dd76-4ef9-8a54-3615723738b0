-- migrate:up
create table ReportSummary (
    id bigint(20) primary key auto_increment,
    project_id bigint(20) not null,
    report_type tinyint(4) not null,
    summary varchar(5000) not null default '' comment 'a json type data',
    deleted  tinyint(4) not null default 0 comment 'deletion mark'
) ENGINE = InnoDB
  AUTO_INCREMENT = 10001
  DEFAULT CHARSET = utf8mb4 COMMENT ='Summary data of report';

-- migrate:down
drop table if exists ReportSummary;
