-- migrate:up
CREATE TABLE `HouseImageUnderwriting`  (
  `id` int(11) NOT NULL,
  `description` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `tips` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'the input unit.',
  `version` int(10) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

INSERT INTO `HouseImageUnderwriting` VALUES (10, 'Front Elevation', 'face to front door', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (20, 'Front-right Corner', 'the corner between front and right elevation', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (30, 'Right Elevation', 'right side of the house', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (40, 'Back-right Corner', 'the corner between back and right elevation', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (50, 'Back Elevation', 'back side of the house', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (60, 'Back-left Corner', 'the corner between back and left elevation', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (70, 'Left Elevation', 'left side of the house', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (80, 'Front-left Corner', 'the corner between front and left elevation', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (90, 'Trees', 'trees within premise of the property', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (100, 'Animal', 'dog/livestock/horse/exotic animal', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (110, 'Trampoline', NULL, 6);
INSERT INTO `HouseImageUnderwriting` VALUES (120, '2nd Floor Door', NULL, 6);
INSERT INTO `HouseImageUnderwriting` VALUES (130, 'Driveway/Sidewalk', 'adjacent to the house', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (140, 'Yard', 'attractive nuisance on yard', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (150, 'Adjacent Hazard', 'tidal water', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (160, 'Business/Farming', 'whether the property used as business location or farming', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (170, 'Fuel Storage Tank', 'alternate heat/solid fuel burning appliance', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (180, 'In Ground Pool', 'built-in pool and accessories', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (190, 'Above Ground Pool', 'inflatable pool and accessories', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (200, 'Detached Garage', 'garage separated from the dwelling', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (210, 'Out Building', 'shed/barn/storage house/additional buildings on the property', 6);
INSERT INTO `HouseImageUnderwriting` VALUES (220, 'Other Hazards', NULL, 6);

UPDATE HouseImageSegmentType SET code_type=0 WHERE id = 97;
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (123, 'Roof Condition Concrens', 0, 0, 5, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9030, 'Curling, clawing, or splitting shingles', 123, 1, NULL, NULL, 60, 1);
INSERT INTO `HouseImageSegmentType`(`id`, `name`, `parent_id`, `is_leaf`, `value_type`, `unit`, `code_type`, `version`) VALUES (9040, 'Excessive moss build-up', 123, 1, NULL, NULL, 60, 1);

-- migrate:down
DROP TABLE IF EXISTS `HouseImageUnderwriting`;
UPDATE HouseImageSegmentType SET code_type=60 WHERE id = 97;
DELETE FROM HouseImageSegmentType WHERE id IN (123, 9030, 9040);
