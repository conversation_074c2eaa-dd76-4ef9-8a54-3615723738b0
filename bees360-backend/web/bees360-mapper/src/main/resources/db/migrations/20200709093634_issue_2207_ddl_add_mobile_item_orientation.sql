-- migrate:up
ALTER TABLE HouseImageUnderwriting ADD orientation TINYINT DEFAULT NULL COMMENT 'orientation:front:1,right:4,back:2,left:3.';

UPDATE HouseImageUnderwriting SET orientation=1 WHERE id=10;
UPDATE HouseImageUnderwriting SET orientation=4 WHERE id=30;
UPDATE HouseImageUnderwriting SET orientation=2 WHERE id=50;
UPDATE HouseImageUnderwriting SET orientation=3 WHERE id=70;

-- migrate:down
ALTER TABLE HouseImageUnderwriting DROP COLUMN orientation;
