-- migrate:up
INSERT INTO `ReportMaterial`(`id`, `material_type`, `position`, `data_source`, `check_is_implement`, `is_show_damage_type`, `size`, `is_bold`, `color`, `align`, `material_url`, `width`, `height`, `material_num`) VALUES (11, 0, NULL, 'IRS', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (14, 1260, 10, '17,18', 1, 1, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (15, 1849, 10, '17,18', 1, 1, 1, 1, 1556606590477, 0);
INSERT INTO `ReportTemplate`(`id`, `company_id`, `material_id`, `report_types`, `page_number`, `page_type`, `sort`, `version`, `create_time`, `is_delete`) VALUES (16, 2096, 11, '17,18', 1, 1, 1, 1, 1556606590477, 0);


-- migrate:down
DELETE FROM ReportMaterial WHERE id in (14,15,16);
DELETE FROM ReportMaterial WHERE id=11;
