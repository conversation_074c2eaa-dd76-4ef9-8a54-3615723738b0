-- migrate:up
UPDATE ProjectImage pi
LEFT JOIN OnSiteReportImageElement osrie ON pi.image_id=osrie.image_id
LEFT JOIN HouseSegmentValue hsv ON SUBSTRING_INDEX(hsv.code,'-',-1)=osrie.id
SET pi.partial_type=(
CASE hsv.value
WHEN '10' THEN 2
WHEN '30' THEN 2
WHEN '50' THEN 2
WHEN '70' THEN 2
WHEN '95' THEN 3
WHEN '185' THEN 4
WHEN '205' THEN 5
WHEN '220' THEN 6
END
),
pi.orientation=(
CASE hsv.value
WHEN '10' THEN 1
WHEN '30' THEN 4
WHEN '50' THEN 2
WHEN '70' THEN 3
END
)
WHERE pi.file_source_type=1 AND pi.is_deleted=0 AND osrie.source_type=1 AND hsv.type=300;

-- migrate:down
