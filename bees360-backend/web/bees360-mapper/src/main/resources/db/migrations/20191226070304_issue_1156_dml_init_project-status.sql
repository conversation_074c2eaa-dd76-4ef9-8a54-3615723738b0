-- migrate:up
-- init
set @projectCreated = 10,
    @customerContacted = 30,
    @assignedToPilot = 50,
    @siteInspected = 70,
    @returnedToClient = 90;

set @deleted = 0,
    @new = 1,
    @imageUploaded = 2,
    @inProcess = 3,
    @underReview = 4,
    @reportApproved = 5;

UPDATE Project SET project_status = CASE latest_status
    WHEN @deleted THEN NULL
    WHEN @new THEN @projectCreated
    WHEN @imageUploaded THEN @siteInspected
    WHEN @inProcess THEN @siteInspected
    WHEN @underReview THEN @siteInspected
    WHEN @reportApproved THEN @siteInspected
    END;

-- 有飞手的且图片张数少于5张的设置状态为 ASSIGNED_TO_PILOT
update Project set project_status = @assignedToPilot where project_id in(
    select M.project_id from Member M where role = 2 and is_deleted = 0
        and M.project_id in (
    select project_id from ProjectImage where is_deleted = 0 group by project_id having count(*) < 5)
);

-- 只有全部订购的报告均已经审核通过才设置状态为 RETURNED_TO_CLIENT
set @approved = 3;
update Project set project_status = @returnedToClient where project_id in (
    select RT.project_id from ReportTask RT left join ProjectReportFile RF
        on RT.project_id = RF.project_id and RT.report_type = RF.report_type
    where RT.is_deleted = 0 and RF.is_deleted = 0 and RF.generation_status = @approved
        and RT.project_id not in
        (select RT.project_id from ReportTask RT left join ProjectReportFile RF
            on RT.project_id = RF.project_id and RT.report_type = RF.report_type
        where RT.is_deleted = 0 and RF.is_deleted = 0 and RF.generation_status != @approved)
    );

-- migrate:down
-- 初始化之前Project.status均为NULL
UPDATE Project SET project_status = NULL;
