-- migrate:up
DROP TABLE IF EXISTS `ProjectScore`;
CREATE TABLE `ProjectScore`(
    `id` bigint NOT NULL AUTO_INCREMENT,
    `project_id` bigint NOT NULL,
    `score` DECIMAL(20,2) NOT NULL,
    `create_at` timestamp NOT NULL DEFAULT current_timestamp,
    `update_at` timestamp NOT NULL DEFAULT current_timestamp on update current_timestamp,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uidx_project_id` (`project_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci;


-- migrate:down
DROP TABLE IF EXISTS `ProjectScore`;
