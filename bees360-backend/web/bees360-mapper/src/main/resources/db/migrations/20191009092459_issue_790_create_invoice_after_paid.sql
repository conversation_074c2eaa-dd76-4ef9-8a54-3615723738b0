-- migrate:up
-- @guanrong.yang

create table Invoice (
	invoice_id bigint not null auto_increment,
    user_id bigint not null comment '支付的用户id',
    company_id bigint not null comment '公司id，采用和project所属的id相同',
    project_id bigint not null,
    invoice_title varchar(500) not null default '' comment 'invoice的标题',
    description varchar(500) not null default '' comment '描述内容',
    status tinyint not null comment 'invoice的状态',
    amount_due decimal(20, 2) not null comment '该invoice应该支付的金额，subtotal + tax_amount',
    subtotal decimal(20, 2) not null comment '原价总金额减去折扣总金额',
    tax_amount decimal(20, 2) not null comment '总税金额',
    discount_amount decimal(20, 2) not null comment '折扣总金额',
    payment_method varchar(50) not null comment '支付方式',
    customer_address varchar(100) not null comment '客户所在公司的详细地址',
    customer_city varchar(50) not null comment '客户所在公司的城市',
    customer_state varchar(50) not null comment '客户所在公司的州',
    customer_zip_code varchar(50) not null comment '客户所在地址的zip code',
    customer_email varchar(50) not null comment '客户所在公司的邮箱',
    customer_phone varchar(50) not null comment '客户所在公司的联系电话',
    customer_company varchar(50) not null comment '客户所在的公司的名称',
    created_time bigint not null comment '创建时间',
    paid_time bigint not null comment '支付时间',
    due_time bigint not null comment '截止时间',
    is_deleted tinyint(1) not null default 0 comment '软删除标志位',
    primary key (invoice_id),
    foreign key (user_id) references User(user_id),
    foreign key (company_id) references Company(company_id),
    foreign key (project_id) references Project(project_id)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='invoice信息表';

create table InvoiceItem (
	item_id bigint not null auto_increment,
    invoice_id bigint not null comment '所属的Invoice Id',
    product_id int not null comment '相关的商品的id',
    item_title varchar(100) not null default '' comment '商品标题',
    description varchar(500) not null default '' comment '商品描述',
    quantity int not null comment '商品个数',
    unit_price decimal(20, 2) not null comment '商品单位价格',
    amount decimal(20, 2) not null comment '总额，quantity * unit_price',
    discount_amount decimal(20, 2) not null comment '折扣总金额',
    -- 没有使用税率的时候为null值
    tax_rate_id int default null comment '该item所使用的税，没有使用税率的时为null值',
    tax_rate_name varchar(50) not null comment '税名称',
    tax_rate_percentage double not null comment '税率，当税率为8.25%时，值为8.25',
    tax_amount decimal(20, 2) not null comment '税总额',
    created_time bigint not null comment '创建时间',
    primary key (item_id),
    foreign key (invoice_id) references Invoice (invoice_id),
    foreign key (product_id) references Product (product_id),
    foreign key (tax_rate_id) references Tax (tax_id)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='invoice的单项信息表';

create table InvoiceDiscount (
	id bigint not null auto_increment,
    invoice_id bigint not null comment 'invoice的id',
    discount_type int not null comment '折扣类型',
    discount_name varchar(50) not null comment '折扣名称',
    off_type int not null comment '折扣方式',
    percentage_off double not null default 0.0 comment '百分比折扣方式的值',
    amount_off double not null default 0.0 comment '定额折扣方式的值',
    amount double not null default 0.0 comment '折扣总金额',
    created_time bigint not null comment '创建时间',
    primary key (id),
    foreign key (invoice_id) references Invoice (invoice_id)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='invoice的折扣统计信息表';

create table InvoiceFile (
	id bigint not null auto_increment,
    project_id bigint not null comment '项目id',
    invoice_id bigint comment 'invoice id',
    invoice_pdf_url varchar(256) not null comment '文件在文件服务的完整url',
    created_time bigint not null comment '创建时间',
    is_deleted tinyint(1) not null comment '软删除标识位',
    primary key (id),
    foreign key (project_id) references Project (project_id),
    foreign key (invoice_id) references Invoice (invoice_id)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='invoice对应的invoice文件';

-- migrate:down
drop table InvoiceFile;
drop table InvoiceDiscount;
drop table InvoiceItem;
drop table Invoice;
