-- migrate:up
ALTER TABLE TempUrlToKey ADD origin_varchar_id VARCHAR(256) DEFAULT NULL COMMENT 'String ID.';

INSERT INTO TempUrlToKey (origin_varchar_id, type, origin_url ) SELECT pi.image_id, 8, pi.file_name FROM ProjectImage pi
WHERE pi.file_name LIKE '%s3.amazonaws.com%';
INSERT INTO TempUrlToKey (origin_varchar_id, type, origin_url ) SELECT pi.image_id, 9, pi.file_name_lower_resolution FROM ProjectImage pi
WHERE pi.file_name_lower_resolution LIKE '%s3.amazonaws.com%';
INSERT INTO TempUrlToKey (origin_varchar_id, type, origin_url ) SELECT pi.image_id, 10, pi.file_name_middle_resolution FROM ProjectImage pi
WHERE pi.file_name_middle_resolution LIKE '%s3.amazonaws.com%';
INSERT INTO TempUrlToKey (origin_varchar_id, type, origin_url ) SELECT pi.image_id, 11, pi.annotation_image FROM ProjectImage pi
WHERE pi.annotation_image LIKE '%s3.amazonaws.com%';

UPDATE ProjectImage SET file_name=replace(file_name, 'https://stag.bees360.io.s3.amazonaws.com/', ''),
                        file_name=replace(file_name, 'https://s3.amazonaws.com/stag.bees360.io/', ''),
                        file_name_lower_resolution=replace(file_name_lower_resolution, 'https://stag.bees360.io.s3.amazonaws.com/', ''),
                        file_name_lower_resolution=replace(file_name_lower_resolution, 'https://s3.amazonaws.com/stag.bees360.io/', ''),
                        file_name_middle_resolution=replace(file_name_middle_resolution, 'https://stag.bees360.io.s3.amazonaws.com/', ''),
                        file_name_middle_resolution=replace(file_name_middle_resolution, 'https://s3.amazonaws.com/stag.bees360.io/', ''),
                        annotation_image=replace(annotation_image, 'https://stag.bees360.io.s3.amazonaws.com/', ''),
                        annotation_image=replace(annotation_image, 'https://s3.amazonaws.com/stag.bees360.io/', '')
                            WHERE file_name LIKE '%stag.bees360.io%';

UPDATE ProjectImage SET file_name=replace(file_name, 'https://bees360.s3.amazonaws.com/', ''),
                        file_name=replace(file_name, 'https://s3.amazonaws.com/bees360/', ''),
                        file_name_lower_resolution=replace(file_name_lower_resolution, 'https://bees360.s3.amazonaws.com/', ''),
                        file_name_lower_resolution=replace(file_name_lower_resolution, 'https://s3.amazonaws.com/bees360/', ''),
                        file_name_middle_resolution=replace(file_name_middle_resolution, 'https://bees360.s3.amazonaws.com/', ''),
                        file_name_middle_resolution=replace(file_name_middle_resolution, 'https://s3.amazonaws.com/bees360/', ''),
                        annotation_image=replace(annotation_image, 'https://bees360.s3.amazonaws.com/', ''),
                        annotation_image=replace(annotation_image, 'https://s3.amazonaws.com/bees360/', '');

ALTER TABLE TempUrlToKey MODIFY COLUMN type INT(2) COMMENT 'Cache other table data. 1:Adjuster.license_files, 2:pilot.license_key_urls, 3:pilot.insurance_key_urls, 4:InvoiceFile.invoice_pdf_url, 5:User.avatar, 6:User.certificate_list, 7:User.insurance_key_urls, 8:ProjectImage.file_name, 9:ProjectImage.file_name_lower_resolution, 10:ProjectImage.file_name_middle_resolution, 11:ProjectImage.annotation_image, 12:Product.url, 13:Roster.resume_url';


-- migrate:down
UPDATE ProjectImage a
    INNER JOIN (SELECT t.origin_varchar_id, t.origin_url FROM TempUrlToKey t WHERE t.type = 8) t2
SET a.file_name = t2.origin_url
WHERE a.image_id = t2.origin_varchar_id;
UPDATE ProjectImage a
    INNER JOIN (SELECT t.origin_varchar_id, t.origin_url FROM TempUrlToKey t WHERE t.type = 9) t2
SET a.file_name_lower_resolution = t2.origin_url
WHERE a.image_id = t2.origin_varchar_id;
UPDATE ProjectImage a
    INNER JOIN (SELECT t.origin_varchar_id, t.origin_url FROM TempUrlToKey t WHERE t.type = 10) t2
SET a.file_name_middle_resolution = t2.origin_url
WHERE a.image_id = t2.origin_varchar_id;
UPDATE ProjectImage a
    INNER JOIN (SELECT t.origin_varchar_id, t.origin_url FROM TempUrlToKey t WHERE t.type = 11) t2
SET a.annotation_image = t2.origin_url
WHERE a.image_id = t2.origin_varchar_id;

DELETE FROM TempUrlToKey WHERE type IN (8, 9, 10, 11);
ALTER TABLE TempUrlToKey DROP origin_varchar_id;

ALTER TABLE TempUrlToKey MODIFY COLUMN type INT(2) COMMENT '';
