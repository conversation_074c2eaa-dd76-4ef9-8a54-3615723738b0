-- migrate:up
CREATE TABLE `ProjectMessage`(
    id bigint(20) primary key auto_increment comment '主键ID',
    `project_id` bigint(20) not null comment '关联项目ID',
    `sender_id` bigint(20) not null  comment '消息发送人ID',
    `title` varchar(200) default null comment '消息的标题，可以为空',
    `content` varchar(500) not null comment '消息的内容',
    `create_time` bigint not null comment '消息发送时间戳',
    `is_deleted` tinyint(1) default 0 comment '消息是否删除',
    `type` tinyint(4)  comment '消息类型：1 管理员发送的note, 2 飞手feedback'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目消息记录';

-- migrate:down
DROP TABLE IF EXISTS  ProjectMessage;
