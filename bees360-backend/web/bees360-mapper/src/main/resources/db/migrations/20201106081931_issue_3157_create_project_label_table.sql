-- migrate:up
DROP TABLE IF EXISTS ProjectLabel;

CREATE TABLE IF NOT EXISTS ProjectLabel(
    `id` bigint NOT NULL AUTO_INCREMENT,
    `label_name` VARCHAR(50) NOT NULL,
    `label_desc` VARCHAR(255) NOT NULL DEFAULT '',
    `create_at` timestamp NOT NULL DEFAULT current_timestamp,
    PRIMARY KEY (`id`) USING BTREE
)ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

INSERT INTO ProjectLabel(`label_name`, `label_desc`)
VALUES ('Cancellation TBD', ''), ('Denied', ''), ('Not Closed','');

CREATE TABLE IF NOT EXISTS ProjectLabelBind(
    `project_id` bigint NOT NULL,
    `label_id` bigint NOT NULL,
    `create_at` timestamp NOT NULL DEFAULT current_timestamp,
    PRIMARY KEY pk(`project_id`, `label_id`)
)ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

-- migrate:down

DROP TABLE ProjectLabel;

DROP TABLE ProjectLabelBind;
