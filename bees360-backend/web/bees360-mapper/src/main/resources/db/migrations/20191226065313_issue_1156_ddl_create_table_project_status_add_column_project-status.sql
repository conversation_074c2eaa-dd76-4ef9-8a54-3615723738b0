-- migrate:up
-- create table ProjectReportStatus
CREATE TABLE ProjectStatus (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    project_id BIGINT NOT NULL COMMENT '项目id',
    user_id BIGINT NOT NULL COMMENT '触发本次状态记录的用户',
    status TINYINT(4) NOT NULL COMMENT '项目的状态',
    created_time BIGINT NOT NULL COMMENT '记录写入时间',
    PRIMARY KEY (id),
    FOREIGN KEY (project_id) REFERENCES Project(project_id),
    FOREIGN KEY (user_id) REFERENCES User(user_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='项目状态变化记录表';

-- add new column
ALTER TABLE Project ADD COLUMN project_status TINYINT(4) DEFAULT NULL AFTER latest_status;

-- migrate:down

DROP TABLE ProjectStatus;
ALTER TABLE Project DROP COLUMN project_status;
