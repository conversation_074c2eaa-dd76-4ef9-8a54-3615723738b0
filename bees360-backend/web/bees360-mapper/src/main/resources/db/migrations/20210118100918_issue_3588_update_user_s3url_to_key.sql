-- migrate:up
DROP TABLE IF EXISTS `TempUrlToKey`;
-- 旧数据保存
CREATE TABLE `TempUrlToKey` (
                            `id` bigint(20) NOT NULL AUTO_INCREMENT,
                            `origin_id` bigint(20)  DEFAULT null ,
                            `type` int(2) DEFAULT null,
                            `origin_url` varchar(2200) NOT NULL DEFAULT '',
                            PRIMARY KEY (`id`) USING BTREE
 ) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8;

-- 1 对应Adjuster的 license_files 字段
insert into TempUrlToKey (origin_id, type, origin_url ) select Adjuster.id, 1, Adjuster.license_files from Adjuster
    where license_files like '%s3.amazonaws.com%';

update Adjuster set license_files = replace(license_files, 'https://s3.amazonaws.com/bees360/', '')
 where license_files like '%https://s3.amazonaws.com/bees360%';

update Adjuster set license_files = replace(license_files, 'https://bees360.s3.amazonaws.com/', '')
 where license_files like '%https://bees360.s3.amazonaws.com%';

-- 2 对应pilot的 license_files 字段
insert into TempUrlToKey (origin_id, type, origin_url ) select pilot.id, 2, pilot.license_key_urls from pilot
    where license_key_urls like '%s3.amazonaws.com%';

update pilot set license_key_urls = replace(license_key_urls, 'https://s3.amazonaws.com/bees360/', '')
 where pilot.license_key_urls like '%https://s3.amazonaws.com/bees360%';

update pilot set license_key_urls = replace(license_key_urls, 'https://bees360.s3.amazonaws.com/', '')
 where pilot.license_key_urls like '%https://bees360.s3.amazonaws.com%';

-- 3 对应pilot的 insurance_key_urls 字段
insert into TempUrlToKey (origin_id, type, origin_url ) select pilot.id, 3, pilot.insurance_key_urls from pilot
    where insurance_key_urls like '%s3.amazonaws.com%';

update pilot set insurance_key_urls = replace(insurance_key_urls, 'https://s3.amazonaws.com/bees360/', '')
 where pilot.insurance_key_urls like '%https://s3.amazonaws.com/bees360%';

update pilot set insurance_key_urls = replace(insurance_key_urls, 'https://bees360.s3.amazonaws.com/', '')
 where pilot.insurance_key_urls like '%https://bees360.s3.amazonaws.com%';

-- 4 对应 InvoiceFile 的 invoice_pdf_url 字段
insert into TempUrlToKey (origin_id, type, origin_url ) select InvoiceFile.id, 4, InvoiceFile.invoice_pdf_url from InvoiceFile
    where InvoiceFile.invoice_pdf_url like '%s3.amazonaws.com%';

update InvoiceFile set invoice_pdf_url = replace(invoice_pdf_url, 'https://s3.amazonaws.com/bees360/', '')
 where invoice_pdf_url like '%https://s3.amazonaws.com/bees360%';

update InvoiceFile set invoice_pdf_url = replace(invoice_pdf_url, 'https://bees360.s3.amazonaws.com/', '')
 where invoice_pdf_url like '%https://bees360.s3.amazonaws.com%';

-- 5 对应 User 的 avatar 字段
insert into TempUrlToKey (origin_id, type, origin_url ) select User.user_id, 5, User.avatar from User
    where User.avatar like '%s3.amazonaws.com%';

update User set avatar = replace(avatar, 'https://s3.amazonaws.com/bees360/', '')
 where avatar like '%https://s3.amazonaws.com/bees360%';

 update User set avatar = replace(avatar, 'https://bees360.s3.amazonaws.com/', '')
 where avatar like '%https://bees360.s3.amazonaws.com%';

-- 6 对应 User 的 certificate_list 字段
insert into TempUrlToKey (origin_id, type, origin_url ) select User.user_id, 6, User.certificate_list from User
    where User.certificate_list like '%s3.amazonaws.com%';

update User set certificate_list = replace(certificate_list, 'https://s3.amazonaws.com/bees360/', '')
 where certificate_list like '%https://s3.amazonaws.com/bees360%';

 update User set certificate_list = replace(certificate_list, 'https://bees360.s3.amazonaws.com/', '')
 where certificate_list like '%https://bees360.s3.amazonaws.com%';

-- 7 对应 User 的 insurance_key_urls 字段
insert into TempUrlToKey (origin_id, type, origin_url ) select User.user_id, 7, User.insurance_key_urls from User
    where User.insurance_key_urls like '%s3.amazonaws.com%';

update User set insurance_key_urls = replace(insurance_key_urls, 'https://s3.amazonaws.com/bees360/', '')
 where insurance_key_urls like '%https://s3.amazonaws.com/bees360%';

 update User set insurance_key_urls = replace(insurance_key_urls, 'https://bees360.s3.amazonaws.com/', '')
 where insurance_key_urls like '%https://bees360.s3.amazonaws.com%';

-- migrate:down
update Adjuster a
    inner join (select t.origin_id, t.origin_url from TempUrlToKey t where t.type = 1) t2
set a.license_files = t2.origin_url
where a.id = t2.origin_id;

update pilot a
    inner join (select t.origin_id, t.origin_url from TempUrlToKey t where t.type = 2) t2
set a.license_key_urls = t2.origin_url
where a.id = t2.origin_id;

update pilot a
    inner join (select t.origin_id, t.origin_url from TempUrlToKey t where t.type = 3) t2
set a.insurance_key_urls = t2.origin_url
where a.id = t2.origin_id;

update InvoiceFile a
    inner join (select t.origin_id, t.origin_url from TempUrlToKey t where t.type = 4) t2
set a.invoice_pdf_url = t2.origin_url
where a.id = t2.origin_id;

update User a
    inner join (select t.origin_id, t.origin_url from TempUrlToKey t where t.type = 5) t2
set a.avatar = t2.origin_url
where a.user_id = t2.origin_id;

update User a
    inner join (select t.origin_id, t.origin_url from TempUrlToKey t where t.type = 6) t2
set a.certificate_list = t2.origin_url
where a.user_id = t2.origin_id;

update User a
    inner join (select t.origin_id, t.origin_url from TempUrlToKey t where t.type = 7) t2
set a.insurance_key_urls = t2.origin_url
where a.user_id = t2.origin_id;

-- 临时表 TempUrlToKey 先不删除，以防止数据有误可验证和恢复
