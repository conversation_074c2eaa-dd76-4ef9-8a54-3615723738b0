-- migrate:up
ALTER TABLE OnSiteReportImageElement DROP FOREIGN KEY OnSiteReportImageElement_ibfk_2;
ALTER TABLE ImageFacet DROP FOREIGN KEY ImageFacet_ibfk_1;
ALTER TABLE ImageAnnotation DROP FOREIGN KEY ImageAnnotation_ibfk_1;
ALTER TABLE ImageAnnotation2D DROP FOREIGN KEY ImageAnnotation2D_ibfk_1;

ALTER TABLE ProjectImage MODIFY COLUMN image_id VARCHAR(256) NOT NULL, MODIFY COLUMN parent_id VARCHAR(256);
ALTER TABLE OnSiteReportImageElement MODIFY COLUMN image_id VARCHAR(256) NOT NULL;
ALTER TABLE ImageFacet MODIFY COLUMN image_id VARCHAR(256) NOT NULL;
ALTER TABLE ImageAnnotation MODIFY COLUMN image_id VARCHAR(256) NOT NULL;
ALTER TABLE ImageAnnotation2D MODIFY COLUMN image_id VARCHAR(256) NOT NULL;
ALTER TABLE ReportAnnotationImage MODIFY COLUMN image_id VARCHAR(256) NOT NULL;

ALTER TABLE ProjectImage ADD INDEX pk_image_id (image_id);
ALTER TABLE ImageAnnotation ADD INDEX idx_image_id (image_id);
ALTER TABLE ImageAnnotation2D ADD INDEX idx_project_id (project_id);

ALTER TABLE ReportAnnotationImage ADD INDEX idx_project_id (project_id);
ALTER TABLE ReportAnnotationImage ADD INDEX idx_image_id (image_id);

-- migrate:down
DROP INDEX pk_image_id ON ProjectImage;
DROP INDEX idx_image_id ON ImageAnnotation;
DROP INDEX idx_project_id ON ImageAnnotation2D;

DROP INDEX idx_project_id ON ReportAnnotationImage;
DROP INDEX idx_image_id ON ReportAnnotationImage;

ALTER TABLE ProjectImage MODIFY COLUMN image_id BIGINT(20) NOT NULL AUTO_INCREMENT, MODIFY COLUMN parent_id BIGINT(20);
ALTER TABLE OnSiteReportImageElement MODIFY COLUMN image_id BIGINT(20) NOT NULL;
ALTER TABLE ImageFacet MODIFY COLUMN image_id BIGINT(20) NOT NULL;
ALTER TABLE ImageAnnotation MODIFY COLUMN image_id BIGINT(20) NOT NULL;
ALTER TABLE ImageAnnotation2D MODIFY COLUMN image_id BIGINT(20) NOT NULL;
ALTER TABLE ReportAnnotationImage MODIFY COLUMN image_id BIGINT(20) NOT NULL;

ALTER TABLE OnSiteReportImageElement ADD CONSTRAINT OnSiteReportImageElement_ibfk_2 FOREIGN KEY (image_id) REFERENCES ProjectImage(image_id);
ALTER TABLE ImageFacet ADD CONSTRAINT ImageFacet_ibfk_1 FOREIGN KEY (image_id) REFERENCES ProjectImage(image_id);
ALTER TABLE ImageAnnotation ADD CONSTRAINT ImageAnnotation_ibfk_1 FOREIGN KEY (image_id) REFERENCES ProjectImage(image_id);
ALTER TABLE ImageAnnotation2D ADD CONSTRAINT ImageAnnotation2D_ibfk_1 FOREIGN KEY (image_id) REFERENCES ProjectImage(image_id);
