INSERT INTO `HouseCategory` VALUES (100, '220', 'RFG', 10, 'Elevation', 10, 'General', 'Address verification', 'street number');
INSERT INTO `HouseCategory` VALUES (200, '221', 'RFG', 10, 'Elevation', 10, 'General', 'Risk overview', '');
INSERT INTO `HouseCategory` VALUES (300, '222', 'RFG', 10, 'Elevation', 20, 'Front', 'Front overview', '');
INSERT INTO `HouseCategory` VALUES (400, '223', 'RFG', 10, 'Elevation', 30, 'Right', 'Right overview', '');
INSERT INTO `HouseCategory` VALUES (500, '224', 'RFG', 10, 'Elevation', 40, 'Back', 'Back overview', '');
INSERT INTO `HouseCategory` VALUES (600, '225', 'RFG', 10, 'Elevation', 50, 'Left', 'Left overview', '');
INSERT INTO `HouseCategory` VALUES (700, '226', 'RFG', 20, 'Roof material', 60, 'Removal', 'Tear off, incl. haul', '');
INSERT INTO `HouseCategory` VALUES (800, '227', 'RFG', 20, 'Roof material', 60, 'Removal', 'Tear off, no haul', '');
INSERT INTO `HouseCategory` VALUES (900, '228', 'RFG', 20, 'Roof material', 70, 'Composite Shingle', '3-tab, 20 yr, incl. felt', '');
INSERT INTO `HouseCategory` VALUES (1000, '229', 'RFG', 20, 'Roof material', 70, 'Composite Shingle', '3-tab, 20 yr, w/o felt', '');
INSERT INTO `HouseCategory` VALUES (1100, '230', 'RFG', 20, 'Roof material', 70, 'Composite Shingle', '3-tab, 25 yr, incl. felt', '');
INSERT INTO `HouseCategory` VALUES (1200, '231', 'RFG', 20, 'Roof material', 70, 'Composite Shingle', '3-tab, 25 yr, w/o felt', '');
INSERT INTO `HouseCategory` VALUES (1300, '232', 'RFG', 20, 'Roof material', 70, 'Composite Shingle', 'Laminated shingle, incl. felt', '');
INSERT INTO `HouseCategory` VALUES (1400, '233', 'RFG', 20, 'Roof material', 70, 'Composite Shingle', 'Laminated shingle, w/o felt', '');
INSERT INTO `HouseCategory` VALUES (1500, '234', 'RFG', 20, 'Roof material', 70, 'Composite Shingle', 'Laminated high grad', '');
INSERT INTO `HouseCategory` VALUES (1600, '235', 'RFG', 20, 'Roof material', 90, 'Wood Shingle', 'Wood shakes/shingles - tapersawn', '');
INSERT INTO `HouseCategory` VALUES (1700, '236', 'RFG', 20, 'Roof material', 90, 'Wood Shingle', 'Wood shingles - tapersawn', '');
INSERT INTO `HouseCategory` VALUES (1800, '237', 'RFG', 20, 'Roof material', 90, 'Wood Shingle', 'Wood shingles - tapersawn, no felt', '');
INSERT INTO `HouseCategory` VALUES (1900, '238', 'RFG', 20, 'Roof material', 100, 'Tile Shingle', 'Clay - S or flat', '');
INSERT INTO `HouseCategory` VALUES (2000, '239', 'RFG', 20, 'Roof material', 100, 'Tile Shingle', 'Concrete - S or flat', '');
INSERT INTO `HouseCategory` VALUES (2100, '240', 'RFG', 20, 'Roof material', 100, 'Tile Shingle', 'Clay - Barrel', '');
INSERT INTO `HouseCategory` VALUES (2200, '241', 'RFG', 20, 'Roof material', 100, 'Tile Shingle', 'Glazed - Barrel', '');
INSERT INTO `HouseCategory` VALUES (2300, '242', 'RFG', 20, 'Roof material', 110, 'Roll Roof', 'Roll roofing', '');
INSERT INTO `HouseCategory` VALUES (2400, '243', 'RFG', 20, 'Roof material', 110, 'Roll Roof', 'Roll roofing, w/o felt', '');
INSERT INTO `HouseCategory` VALUES (2500, '244', 'RFG', 20, 'Roof material', 110, 'Roll Roof', 'Roll roofing - 50% overlap', '');
INSERT INTO `HouseCategory` VALUES (2600, '245', 'RFG', 20, 'Roof material', 110, 'Roll Roof', 'Roll roofing - 50% overlap, w/o felt', '');
INSERT INTO `HouseCategory` VALUES (2700, '246', 'RFG', 20, 'Roof material', 120, 'Bitumen Roof', 'Modified bitumen roof', '');
INSERT INTO `HouseCategory` VALUES (2800, '247', 'RFG', 20, 'Roof material', 120, 'Bitumen Roof', 'Modified bitumen roof - hot mopped', '');
INSERT INTO `HouseCategory` VALUES (2900, '248', 'RFG', 20, 'Roof material', 120, 'Bitumen Roof', 'Modified bitumen roof - add base sheet', '');
INSERT INTO `HouseCategory` VALUES (3000, '249', 'RFG', 20, 'Roof material', 120, 'Bitumen Roof', 'Modified bitumen roof - add glass felt', '');
INSERT INTO `HouseCategory` VALUES (3100, '250', 'RFG', 20, 'Roof material', 130, 'Metal Roofing', 'Metal roofing', '');
INSERT INTO `HouseCategory` VALUES (3200, '251', 'RFG', 20, 'Roof material', 130, 'Metal Roofing', 'Metal roofing - standard', '');
INSERT INTO `HouseCategory` VALUES (3300, '252', 'RFG', 20, 'Roof material', 130, 'Metal Roofing', 'Metal roofing - high', '');
INSERT INTO `HouseCategory` VALUES (3400, '253', 'RFG', 20, 'Roof material', 130, 'Metal Roofing', 'Metal roofing - premium', '');
INSERT INTO `HouseCategory` VALUES (3450, '254', 'RFG', 30, 'Roof', 135, 'Test Square', '10x10 test square for hail damage', '');
INSERT INTO `HouseCategory` VALUES (3475, '254', 'SFG', 30, 'Roof', 136, 'Close-ups', 'Close-up image for roof damage', NULL);
INSERT INTO `HouseCategory` VALUES (3480, '254', 'SFG', 30, 'Roof', 137, 'Layers', 'Roofing layers', NULL);
INSERT INTO `HouseCategory` VALUES (3485, '254', 'SFG', 30, 'Roof', 138, 'Drip Edge', 'Roofing drip edge', NULL);
INSERT INTO `HouseCategory` VALUES (3490, '254', 'SFG', 30, 'Roof', 139, 'Ridge Cap', 'Roofing ridge cap', NULL);
INSERT INTO `HouseCategory` VALUES (3500, '254', 'RFG', 30, 'Roof', 140, 'Felt', 'Roofing felt', 'choose size');
INSERT INTO `HouseCategory` VALUES (3600, '255', 'RFG', 30, 'Roof', 150, 'Valley', 'Valley metal', '');
INSERT INTO `HouseCategory` VALUES (3700, '256', 'RFG', 30, 'Roof', 150, 'Valley', 'Valley metal (w) profile', '');
INSERT INTO `HouseCategory` VALUES (3800, '257', 'RFG', 30, 'Roof', 160, 'Flashing', 'Step flashing', '');
INSERT INTO `HouseCategory` VALUES (3900, '258', 'RFG', 30, 'Roof', 160, 'Flashing', 'Flashing 14\"', '');
INSERT INTO `HouseCategory` VALUES (4000, '259', 'RFG', 30, 'Roof', 170, 'Accessories', 'Exhaust cap', 'choose size');
INSERT INTO `HouseCategory` VALUES (4100, '260', 'RFG', 30, 'Roof', 170, 'Accessories', 'Plastic power attic', '');
INSERT INTO `HouseCategory` VALUES (4200, '261', 'RFG', 30, 'Roof', 170, 'Accessories', 'Metal power attic', '');
INSERT INTO `HouseCategory` VALUES (4300, '262', 'RFG', 30, 'Roof', 170, 'Accessories', 'Soffit vent', '');
INSERT INTO `HouseCategory` VALUES (4400, '263', 'RFG', 30, 'Roof', 170, 'Accessories', 'Metal turtle vent', '');
INSERT INTO `HouseCategory` VALUES (4500, '264', 'RFG', 30, 'Roof', 170, 'Accessories', 'Plastic turtle vent', '');
INSERT INTO `HouseCategory` VALUES (4600, '265', 'RFG', 30, 'Roof', 170, 'Accessories', 'Furnace vent', '');
INSERT INTO `HouseCategory` VALUES (4700, '266', 'RFG', 30, 'Roof', 170, 'Accessories', 'Pipe jack', '');
INSERT INTO `HouseCategory` VALUES (4800, '267', 'RFG', 30, 'Roof', 170, 'Accessories', 'Flashing pipe jack', 'choose size');
INSERT INTO `HouseCategory` VALUES (4900, '268', 'RFG', 30, 'Roof', 170, 'Accessories', 'Flashing pipe jack split boot', '');
INSERT INTO `HouseCategory` VALUES (5000, '269', 'RFG', 30, 'Roof', 170, 'Accessories', 'Vent cap', '');
INSERT INTO `HouseCategory` VALUES (5050, '374', 'RFG', 30, 'Roof', 175, 'Shingle Size', 'Shingle size', '');
INSERT INTO `HouseCategory` VALUES (5100, '270', 'SDG', 40, 'Siding', 180, 'Parts', 'Aluminum wrap wood', '');
INSERT INTO `HouseCategory` VALUES (5200, '271', 'SDG', 40, 'Siding', 180, 'Parts', 'Small aluminum wrap wood', '');
INSERT INTO `HouseCategory` VALUES (5300, '272', 'SDG', 40, 'Siding', 180, 'Parts', 'Large aluminum wrap wood', '');
INSERT INTO `HouseCategory` VALUES (5400, '273', 'SDG', 40, 'Siding', 180, 'Parts', 'Xlarge aluminum wrap wood', '');
INSERT INTO `HouseCategory` VALUES (5500, '274', 'SDG', 40, 'Siding', 180, 'Parts', 'Vinyl trim', '');
INSERT INTO `HouseCategory` VALUES (5600, '275', 'SDG', 50, 'Siding material', 190, 'Vinyl', 'Vinyl siding', '');
INSERT INTO `HouseCategory` VALUES (5700, '276', 'SDG', 50, 'Siding material', 190, 'Vinyl', 'Vinyl siding high grade', '');
INSERT INTO `HouseCategory` VALUES (5800, '277', 'SDG', 50, 'Siding material', 190, 'Vinyl', 'Vinyl siding premium grade', '');
INSERT INTO `HouseCategory` VALUES (5900, '278', 'SDG', 50, 'Siding material', 200, 'Fiber', 'Fiber cement lap', 'choose size');
INSERT INTO `HouseCategory` VALUES (6000, '279', 'SDG', 50, 'Siding material', 210, 'Hardboard', 'Hardboard lap', 'choose size');
INSERT INTO `HouseCategory` VALUES (6100, '280', 'SDG', 50, 'Siding material', 220, 'Aluminum', 'Aluminum', 'choose size');
INSERT INTO `HouseCategory` VALUES (6200, '281', 'SDG', 50, 'Siding material', 230, 'Steel', 'Steel 29 gauge', '');
INSERT INTO `HouseCategory` VALUES (6300, '282', 'PNT', 60, 'Paint', 240, 'General', 'Paint one coat', '');
INSERT INTO `HouseCategory` VALUES (6400, '283', 'PNT', 60, 'Paint', 240, 'General', 'Paint exterior one coat', '');
INSERT INTO `HouseCategory` VALUES (6500, '284', 'PNT', 60, 'Paint', 240, 'General', 'Paint trim one coat', '');
INSERT INTO `HouseCategory` VALUES (6600, '285', 'PNT', 60, 'Paint', 240, 'General', 'Paint corner trim one coat', '');
INSERT INTO `HouseCategory` VALUES (6700, '286', 'PNT', 60, 'Paint', 240, 'General', 'Seal w/ anti0microbial one coat', '');
INSERT INTO `HouseCategory` VALUES (6800, '287', 'PNT', 60, 'Paint', 240, 'General', 'Seal w/ PVA one coat', '');
INSERT INTO `HouseCategory` VALUES (6900, '288', 'PNT', 60, 'Paint', 250, 'Exterior', 'Door or window opening one coat', '');
INSERT INTO `HouseCategory` VALUES (7000, '289', 'PNT', 60, 'Paint', 250, 'Exterior', 'Door or window trim one coat', '');
INSERT INTO `HouseCategory` VALUES (7100, '290', 'PNT', 60, 'Paint', 250, 'Exterior', 'Overhaed garage door one coat', '');
INSERT INTO `HouseCategory` VALUES (7200, '291', 'PNT', 60, 'Paint', 250, 'Exterior', 'Single garage door one coat', '');
INSERT INTO `HouseCategory` VALUES (7300, '292', 'PNT', 60, 'Paint', 250, 'Exterior', 'Double garage door one coat', '');
INSERT INTO `HouseCategory` VALUES (7400, '293', 'PNT', 60, 'Paint', 250, 'Exterior', 'Metal siding one coat', '');
INSERT INTO `HouseCategory` VALUES (7500, '294', 'PNT', 60, 'Paint', 250, 'Exterior', 'Wood siding one coat', '');
INSERT INTO `HouseCategory` VALUES (7600, '295', 'PNT', 70, 'Clean', 260, 'Exterior', 'Pressure wash', '');
INSERT INTO `HouseCategory` VALUES (7700, '296', 'PNT', 70, 'Clean', 260, 'Exterior', 'Pressure wash - siding', '');
INSERT INTO `HouseCategory` VALUES (7800, '297', 'PNT', 80, 'Dry', 270, 'Exterior', 'Drywall hung taped ready for texture', '');
INSERT INTO `HouseCategory` VALUES (7900, '298', 'PNT', 80, 'Dry', 270, 'Exterior', 'Drywall patch/small repair ready for paint', '');
INSERT INTO `HouseCategory` VALUES (8000, '299', 'WDW', 90, 'Window', 280, 'General', 'Reglazed window', 'choose size');
INSERT INTO `HouseCategory` VALUES (8100, '300', 'WDW', 90, 'Window', 280, 'General', 'Window screen', 'choose size');
INSERT INTO `HouseCategory` VALUES (8200, '301', 'WDW', 90, 'Window', 280, 'General', 'Window - skylight', '');
INSERT INTO `HouseCategory` VALUES (8300, '302', 'WDW', 90, 'Window', 280, 'General', 'Window installer - per hour', '');
INSERT INTO `HouseCategory` VALUES (8400, '303', 'WDW', 90, 'Window', 290, 'Aluminum Window', 'Window glass - \"Low E\"', '');
INSERT INTO `HouseCategory` VALUES (8500, '304', 'WDW', 90, 'Window', 290, 'Aluminum Window', 'Additional charge for retrofit window', 'choose level');
INSERT INTO `HouseCategory` VALUES (8600, '305', 'WDW', 90, 'Window', 290, 'Aluminum Window', 'Window, single hung', 'choose size');
INSERT INTO `HouseCategory` VALUES (8700, '306', 'WDW', 90, 'Window', 300, 'Vinyl Window', 'Window glass - \"Low E\"', '');
INSERT INTO `HouseCategory` VALUES (8800, '307', 'WDW', 90, 'Window', 300, 'Vinyl Window', 'Additional charge for retrofit window', 'choose level');
INSERT INTO `HouseCategory` VALUES (8900, '308', 'WDW', 90, 'Window', 300, 'Vinyl Window', 'Window, single hung', 'choose size');
INSERT INTO `HouseCategory` VALUES (9000, '309', 'WDW', 90, 'Window', 310, 'Wood Window', 'Window glass - \"Low E\"', '');
INSERT INTO `HouseCategory` VALUES (9100, '310', 'WDW', 90, 'Window', 310, 'Wood Window', 'Additional charge for retrofit window', 'choose level');
INSERT INTO `HouseCategory` VALUES (9200, '311', 'WDW', 90, 'Window', 310, 'Wood Window', 'Window, single hung', 'choose size');
INSERT INTO `HouseCategory` VALUES (9300, '312', 'DOO', 100, 'Door', 320, 'Exterior', 'Exterior door', '');
INSERT INTO `HouseCategory` VALUES (9400, '313', 'DOO', 100, 'Door', 320, 'Exterior', 'Exterior door - metal - insulted', '');
INSERT INTO `HouseCategory` VALUES (9500, '314', 'DOO', 100, 'Door', 320, 'Exterior', 'Exterior door - metal - insulted - standard', '');
INSERT INTO `HouseCategory` VALUES (9600, '315', 'DOO', 100, 'Door', 320, 'Exterior', 'Exterior door - metal - insulted - high', '');
INSERT INTO `HouseCategory` VALUES (9700, '316', 'DOO', 100, 'Door', 320, 'Exterior', 'Exterior door - deluxe grade', '');
INSERT INTO `HouseCategory` VALUES (9800, '317', 'DOO', 100, 'Door', 320, 'Exterior', 'French door - prehung unit', '');
INSERT INTO `HouseCategory` VALUES (9900, '318', 'DOO', 100, 'Door', 320, 'Exterior', 'French door - wood', '');
INSERT INTO `HouseCategory` VALUES (10000, '319', 'DOO', 100, 'Door', 320, 'Exterior', 'French door - stain grade', '');
INSERT INTO `HouseCategory` VALUES (10100, '320', 'DOO', 100, 'Door', 320, 'Exterior', 'French double door', '');
INSERT INTO `HouseCategory` VALUES (10200, '321', 'DOO', 100, 'Door', 320, 'Exterior', 'French double door - prehung unit', '');
INSERT INTO `HouseCategory` VALUES (10300, '322', 'HVAC', 110, 'HVAC', 330, 'Labor', 'Technician - per hour', '');
INSERT INTO `HouseCategory` VALUES (10400, '323', 'HVAC', 110, 'HVAC', 330, 'Labor', 'General laborer - per hour', '');
INSERT INTO `HouseCategory` VALUES (10500, '324', 'HVAC', 110, 'HVAC', 340, 'Unit', 'AC unit', 'choose size (2 ton,5 ton,8 ton,10 ton,15 ton)');
INSERT INTO `HouseCategory` VALUES (10600, '325', 'HVAC', 110, 'HVAC', 340, 'Unit', 'Window AC unit', '');
INSERT INTO `HouseCategory` VALUES (10700, '326', 'HVAC', 110, 'HVAC', 350, 'Parts', 'AC cover', '');
INSERT INTO `HouseCategory` VALUES (10800, '327', 'HVAC', 110, 'HVAC', 350, 'Parts', 'AC secruity cage', '');
INSERT INTO `HouseCategory` VALUES (10900, '328', 'HVAC', 110, 'HVAC', 350, 'Parts', 'AC security cage - high grage', '');
INSERT INTO `HouseCategory` VALUES (11000, '329', 'HVAC', 110, 'HVAC', 350, 'Parts', 'AC condenser unit', '');
INSERT INTO `HouseCategory` VALUES (11100, '330', 'SFG', 120, 'Garage', 360, 'Garage Door', 'Overhead garage door opener', '');
INSERT INTO `HouseCategory` VALUES (11200, '331', 'SFG', 120, 'Garage', 360, 'Garage Door', 'Overhead garage door opener - standard', '');
INSERT INTO `HouseCategory` VALUES (11300, '332', 'SFG', 120, 'Garage', 360, 'Garage Door', 'Overhead garage door opener - high', '');
INSERT INTO `HouseCategory` VALUES (11400, '333', 'SFG', 120, 'Garage', 360, 'Garage Door', 'Overhead garage door opener - premium', '');
INSERT INTO `HouseCategory` VALUES (11500, '334', 'SFG', 120, 'Garage', 360, 'Garage Door', 'Overhead garage door opener - deluxe', '');
INSERT INTO `HouseCategory` VALUES (11600, '335', 'SFG', 120, 'Garage', 360, 'Garage Door', 'Clean overhad garage door opener', '');
INSERT INTO `HouseCategory` VALUES (11700, '336', 'SFG', 120, 'Garage', 370, 'General', 'Demolish/remove detached garage', '');
INSERT INTO `HouseCategory` VALUES (11800, '337', 'SFG', 130, 'Storage unit', 380, 'Shed', 'Storage shed - wood', 'choose size (10x12,8x10,10x14,12x14,12x16,8x6)');
INSERT INTO `HouseCategory` VALUES (11900, '338', 'SFG', 130, 'Storage unit', 380, 'Shed', 'Storage shed - vinyl - Gable type', 'choose size (10x8,8x6,10x13)');
INSERT INTO `HouseCategory` VALUES (12000, '339', 'SFG', 130, 'Storage unit', 380, 'Shed', 'Storage shed - metal - barn type', 'choose size (10x14,10x8)');
INSERT INTO `HouseCategory` VALUES (12100, '340', 'SFG', 130, 'Storage unit', 380, 'Shed', 'Storage shed - metal - Gable type', 'choose size (10x8, 8x6,10x12,10x17,10x21,10x25)');
INSERT INTO `HouseCategory` VALUES (12200, '341', 'SFG', 130, 'Storage unit', 390, 'Bin', 'Grain storage bin', 'choose size (12,15,21,24,27,31,33,36,39,42,48)');
INSERT INTO `HouseCategory` VALUES (12300, '342', 'SFG', 140, 'Pool', 400, 'General', 'Repaint pool', '');
INSERT INTO `HouseCategory` VALUES (12400, '343', 'SFG', 140, 'Pool', 400, 'General', 'Patio/pool enclosure - rescreen', '');
INSERT INTO `HouseCategory` VALUES (12500, '344', 'SFG', 140, 'Pool', 400, 'General', 'Patio/pool enclosure - full screen', '');
INSERT INTO `HouseCategory` VALUES (12600, '345', 'SFG', 140, 'Pool', 400, 'General', 'Swimming pool cover - solid vinyl', '');
INSERT INTO `HouseCategory` VALUES (12700, '346', 'SFG', 140, 'Pool', 400, 'General', 'Swimming pool cover - mesh', '');
INSERT INTO `HouseCategory` VALUES (12800, '347', 'SFG', 140, 'Pool', 400, 'General', 'Swimming pool cover - automatic', '');
INSERT INTO `HouseCategory` VALUES (12900, '348', 'SFG', 140, 'Pool', 400, 'General', 'Swimming pool heater - gas', '');
INSERT INTO `HouseCategory` VALUES (13000, '349', 'SFG', 140, 'Pool', 400, 'General', 'Swimming pool heater - gas - large', '');
INSERT INTO `HouseCategory` VALUES (13100, '350', 'SFG', 140, 'Pool', 400, 'General', 'Swimming pool heater - solar', '');
INSERT INTO `HouseCategory` VALUES (13200, '351', 'SFG', 140, 'Pool', 400, 'General', 'Swimming pool heater - electric', '');
INSERT INTO `HouseCategory` VALUES (13300, '352', 'SFG', 140, 'Pool', 400, 'General', 'Swimming pool pump', '');
INSERT INTO `HouseCategory` VALUES (13400, '353', 'SFG', 140, 'Pool', 400, 'General', 'Swimming pool - sprayed concrete', '');
INSERT INTO `HouseCategory` VALUES (13500, '354', 'SFG', 140, 'Pool', 400, 'General', 'Swimming pool - filter', '');
INSERT INTO `HouseCategory` VALUES (13600, '355', 'SFG', 140, 'Pool', 400, 'General', 'Swimming pool - liner', '');
INSERT INTO `HouseCategory` VALUES (13700, '356', 'SFG', 150, 'Fence', 410, 'Wood Fence', '5\'-6\' high', 'fill length');
INSERT INTO `HouseCategory` VALUES (13800, '357', 'SFG', 150, 'Fence', 410, 'Wood Fence', '5\'-6\' high - treated', 'fill length');
INSERT INTO `HouseCategory` VALUES (13900, '358', 'SFG', 150, 'Fence', 410, 'Wood Fence', '3\'-4\' high - cedar or equal', 'fill length');
INSERT INTO `HouseCategory` VALUES (14000, '359', 'SFG', 150, 'Fence', 410, 'Wood Fence', '3\'-4\' high - treated', 'fill length');
INSERT INTO `HouseCategory` VALUES (14100, '360', 'SFG', 150, 'Fence', 410, 'Wood Fence', '7\'-8\' high - cedar or equal', 'fill length');
INSERT INTO `HouseCategory` VALUES (14200, '361', 'SFG', 150, 'Fence', 410, 'Wood Fence', '7\'-8\' high - treated', 'fill length');
INSERT INTO `HouseCategory` VALUES (14300, '362', 'SFG', 150, 'Fence', 410, 'Wood Fence', 'Slate 5\'-6\' high', 'fill length');
INSERT INTO `HouseCategory` VALUES (14400, '363', 'SFG', 150, 'Fence', 410, 'Wood Fence', 'Slate 3\'-4\' high', 'fill length');
INSERT INTO `HouseCategory` VALUES (14500, '364', 'SFG', 150, 'Fence', 410, 'Wood Fence', 'Slate 7\'-8\' high', 'fill length');
INSERT INTO `HouseCategory` VALUES (14600, '365', 'SFG', 150, 'Fence', 420, 'Aluminum Fence', '3\'-4\' high', '');
INSERT INTO `HouseCategory` VALUES (14700, '366', 'SFG', 150, 'Fence', 420, 'Aluminum Fence', '5\'-6\' high', '');
INSERT INTO `HouseCategory` VALUES (14800, '367', 'SFG', 150, 'Fence', 430, 'Vinyl Fence', '3\'-4\' high', '');
INSERT INTO `HouseCategory` VALUES (14900, '368', 'SFG', 150, 'Fence', 430, 'Vinyl Fence', '5\'-6\' high', '');
INSERT INTO `HouseCategory` VALUES (15000, '369', 'SFG', 150, 'Fence', 430, 'Vinyl Fence', '5\'-6\' high - w/ lattice', '');
INSERT INTO `HouseCategory` VALUES (15100, '370', 'SFG', 150, 'Fence', 440, 'Steel Pipe Fence', '3 rail, 4\' high', '');
INSERT INTO `HouseCategory` VALUES (15200, '371', 'SFG', 150, 'Fence', 440, 'Steel Pipe Fence', '4 rail - 5\' high', '');
INSERT INTO `HouseCategory` VALUES (15300, '372', 'SFG', 160, 'Horse stable', 450, 'Exterior', 'Roof material', 'choose material (composite shingle,asphalt)');
INSERT INTO `HouseCategory` VALUES (15400, '373', 'SFG', 160, 'Horse stable', 450, 'Exterior', 'Exterior door', '');
