# 数据库初始化

## db/init

根据`db/init`中的文件，创建一个`all.sql`(其中的<your-path>为你保存脚本文件的绝对路径)，登录数据库然后执行。

```shell script
source <your-path>/Bees360-init-dcl.sql

use Bees360;
source <your-path>/Bees360-init-ddl.sql
source <your-path>/Bees360-init-dml-1-Country.sql
source <your-path>/Bees360-init-dml-2-Region.sql
source <your-path>/Bees360-init-dml-3-City.sql
source <your-path>/Bees360-init-dml-4-Company.sql
source <your-path>/Bees360-init-dml-5-HouseCategory.sql
source <your-path>/Bees360-init-dml-6-HouseImageSegmentType.sql
source <your-path>/Bees360-init-dml-7-User.sql
source <your-path>/Bees360-init-dml-8-SystemValue.sql
```

## db/migrations

### 执行方法一

修改`.env`文件为：

```
# <user-name>: 账号名
# <password>: 账号密码
# <your-ip>: 你的ip地址
DATABASE_URL="mysql://<user-name>:<password>@<your-ip>:3306/Bees360"
```

```shell script
dbmate migrate
```

### 执行方法二

当前位置在resources中
```shell script
cd db
dbmate dump
pwd schema.sql
```

然后登录你的数据库
```sql
use Bees360
source <path-to-schema.sql>/schema.sql
```

### 遇到的问题

脚本执行完成之后，部分sql文件没有完整执行，但schema_migrations表中含有版本号。

## 待完善

- [ ] 之前在开发user模块时候保留了部分表还在sql文件中，需要进行删除
- [ ] `HouseCategoryVersion` 在系统初始化时需要存在一个值，否则无法正常启动
