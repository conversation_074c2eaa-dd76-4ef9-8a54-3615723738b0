package com.bees360.web.project;

import com.bees360.catastrophe.ClaimCatastrophe;
import com.bees360.contract.Contract;
import com.bees360.contract.Message;
import com.bees360.customer.CustomerManager;
import com.bees360.entity.dto.CreateOrUpdateProjectDto;
import com.bees360.entity.dto.ProjectStatusTimeLineDto;
import com.bees360.entity.vo.ProjectStatusVo;
import com.bees360.mapper.MemberMapper;
import com.bees360.policy.PolicyManager;
import com.bees360.project.Address;
import com.bees360.project.Building;
import com.bees360.project.ContactManager;
import com.bees360.customer.Customer;
import com.bees360.project.MemberManager;
import com.bees360.project.Message.ProjectMessage;
import com.bees360.project.ParticipantManager;
import com.bees360.project.Project;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIManager;
import com.bees360.project.ProjectManager;
import com.bees360.project.claim.ProjectCatastropheManager;
import com.bees360.project.status.ProjectStatusManager;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.UserService;
import com.bees360.user.UserProvider;
import com.bees360.util.DateTimes;
import com.bees360.web.core.constant.DateTimeConst;
import com.bees360.web.project.util.IdConverter;
import com.bees360.web.project.util.ProjectAssemble;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import jakarta.annotation.Nullable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class SiProjectManager implements ProjectManager {
    @Autowired private ProjectService projectService;
    @Autowired private ProjectStatusService projectStatusService;
    @Autowired private ProjectLabelService projectLabelService;
    @Autowired private UserService userService;
    @Autowired private UserProvider userProvider;
    @Autowired private CustomerManager customerManager;
    @Autowired private MemberMapper memberMapper;
    @Autowired private ContactManager siProjectContactManager;
    @Autowired private ProjectCatastropheManager projectCatastropheManager;
    @Autowired private PolicyManager policyManager;
    @Autowired private ProjectIIManager projectIIManager;

    public void setUserProvider(UserProvider userProvider) {
        this.userProvider = userProvider;
    }

    public void setCustomerManager(CustomerManager customerManager) {
        this.customerManager = customerManager;
    }

    @Override
    public void updateProject(Project project) {
        CreateOrUpdateProjectDto createOrUpdateProjectDto = ProjectAssemble.toCreateOrUpdateProjectDto(project);
        projectService.updateProject(createOrUpdateProjectDto);
    }

    @Override
    public Optional<Address> getAddress(String projectId) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public void updateAddress(String projectId, Address address) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public Optional<Building> getBuilding(String projectId) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public void updateBuilding(String projectId, Building building) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public Optional<Customer> getInsuredBy(String projectId) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public void updateInsuredBy(String projectId, Customer customer) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public Optional<Customer> getProcessedBy(String projectId) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public void updateProcessedBy(String projectId, Customer customer) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public Optional<ContactManager> getContactManager(String projectId) {
        return Optional.of(siProjectContactManager);
    }

    @Override
    public Optional<MemberManager> getMemberManager(String projectId) {
        SiProjectMemberManager memberManager =
            new SiProjectMemberManager(projectId, userService);
        memberManager.setUserProvider(userProvider);
        return Optional.of(memberManager);
    }

    @Override
    public Optional<ParticipantManager> getParticipantManager(String projectId) {
        SiProjectParticipantManager participantManager =
            new SiProjectParticipantManager(projectId, memberMapper, projectService);
        participantManager.setUserProvider(userProvider);
        return Optional.of(participantManager);
    }

    @Override
    public Optional<ProjectStatusManager> getStatusManager(String projectId) {
        return Optional.empty();
    }

    @Override
    public String namespace() {
        return "project/project";
    }

    @SneakyThrows
    @Override
    public Project findById(String id) {
        long projectIdLong = IdConverter.toProjectId(id);
        com.bees360.entity.Project project = projectService.getById(projectIdLong);
        var projectII = projectIIManager.get(id);
        var policyType =
                projectService.getPolicyType(
                        projectIdLong,
                        project.getClaimNote(),
                        project.getInsuranceCompany(),
                        project.getClaimType());
        ProjectStatusTimeLineDto projectStatusTimeLine =
                projectStatusService.getProjectStatusTimeLine(projectIdLong);
        Long inspectionCompletedTime =
                Optional.ofNullable(projectStatusTimeLine)
                        .map(ProjectStatusTimeLineDto::getSiteInspected)
                        .map(ProjectStatusVo::getCreatedTime)
                        .orElse(null);
        String timeZone = getTimeZone(projectIdLong, project.getDamageEventTime());
        var cat = projectCatastropheManager.findByProjectId(id);
        return Optional.ofNullable(project)
                .map(p -> toProject(p, inspectionCompletedTime, timeZone))
                .map(p -> setCatastrophe(cat, p))
                .map(p -> setPolicyType(policyType, p))
                .map(p -> setProjectState(projectII, p))
                .map(p -> setProjectDaysOld(projectII, p))
                .orElse(null);
    }

    private Project setProjectState(ProjectII projectII,Project project) {
        var builder = project.toMessage().toBuilder();
        builder.setCurrentState(projectII.getCurrentState().toMessage());
        return Project.from(builder.build());

    }

    private Project setCatastrophe(ClaimCatastrophe catastrophe, Project project) {
        if (Objects.isNull(catastrophe)) {
            return project;
        }
        var builder = project.toMessage().toBuilder();
        builder.setCatastrophe(catastrophe.toMessage());
        return Project.from(builder.build());
    }

    private Project setPolicyType(@Nullable String policyType, Project project) {
        if (StringUtils.isEmpty(policyType)) {
            return project;
        }
        var builder = project.toMessage().toBuilder();
        var policyBuilder = builder.getPolicy().toBuilder();
        policyBuilder.setType(policyType);
        builder.setPolicy(policyBuilder.build());
        return Project.from(builder.build());
    }

    private Project setProjectDaysOld(ProjectII projectII, Project project) {
        if (Objects.isNull(projectII) || Objects.isNull(projectII.getDaysOld())) {
            return project;
        }
        var builder = project.toMessage().toBuilder();
        builder.setDaysOld(projectII.getDaysOld());
        return Project.from(builder.build());
    }

    private Project toProject(com.bees360.entity.Project projectDO, @Nullable Long inspectionCompletedTime, @Nullable String timeZone) {
        ProjectMessage message = ProjectAssemble.toProjectMessage(projectDO, inspectionCompletedTime, timeZone);
        ProjectMessage.Builder builder = message.toBuilder();
        setCreateBy(builder);
        setContract(builder);
        return Project.of(builder.build());
    }

    private String getTimeZone(long projectId, Long timeOfDate) {
        if (timeOfDate == null || timeOfDate < 0) {
            return null;
        }
        LocalDateTime easternTime = DateTimes.fromEpochMilli(timeOfDate, ZoneId.of(DateTimeConst.TIME_ZONE_US_EASTERN));
        LocalDateTime pacificTime = DateTimes.fromEpochMilli(timeOfDate, ZoneId.of(DateTimeConst.TIME_ZONE_US_PACIFIC));
        if (easternTime.getDayOfMonth() == pacificTime.getDayOfMonth()) {
            // 美国本土同日
            return DateTimeConst.TIME_ZONE_US_CENTRAL;
        }

        // 非整点，直接用中部时间
        if (timeOfDate % 3600000 > 0) {
            return DateTimeConst.TIME_ZONE_US_CENTRAL;
        }
        if (easternTime.getHour() == 0) {
            return DateTimeConst.TIME_ZONE_US_EASTERN;
        }
        if (pacificTime.getHour() == 0) {
            return DateTimeConst.TIME_ZONE_US_PACIFIC;
        }
        LocalDateTime mountainTime = DateTimes.fromEpochMilli(timeOfDate, ZoneId.of(DateTimeConst.TIME_ZONE_US_MOUNTAIN));
        if (mountainTime.getHour() == 0) {
            return DateTimeConst.TIME_ZONE_US_MOUNTAIN;
        }
        return DateTimeConst.TIME_ZONE_US_CENTRAL;
    }

    private void setCreateBy(ProjectMessage.Builder builder) {
        if (userProvider == null) {
            return;
        }
        if (builder.hasCreateBy() && StringUtils.isNotBlank(builder.getCreateBy().getId())) {
            String createByUserId = builder.getCreateBy().getId();
            Optional.ofNullable(userProvider.findUserById(createByUserId))
                .ifPresent(u -> builder.setCreateBy(u.toMessage().toBuilder().clearAuthority().build()));
        }
    }

    private void setContract(ProjectMessage.Builder builder) {
        Contract contract = Contract.from(builder.getContract());
        if(Objects.isNull(contract)) {
            return;
        }

        Customer insuredBy = contract.getInsuredBy();
        var contractMessage = Message.ContractMessage.newBuilder();

        if (insuredBy != null && StringUtils.isNotBlank(insuredBy.getId())) {
            String insuredById = contract.getInsuredBy().getId();
            Optional.ofNullable(customerManager.findById(insuredById))
                .ifPresent(c -> contractMessage.setInsuredBy(c.toMessage()));
        }

        Customer processedBy = contract.getProcessedBy();
        if (processedBy != null && StringUtils.isNotBlank(processedBy.getId())) {
            String processedById = processedBy.getId();
            Optional.ofNullable(customerManager.findById(processedById))
                .ifPresent(c -> contractMessage.setProcessedBy(c.toMessage()));
        }

        builder.setContract(contractMessage);
    }

    @Override
    public Iterable<? extends Project> findAllById(Iterable<String> iterable) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public Iterable<? extends Project> loadAll() {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public boolean existsById(String projectId) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public String save(Project project) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public Iterable<String> saveAll(Iterable<? extends Project> iterable) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public void deleteById(String projectId) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public void deleteAllById(Iterable<String> iterable) {
        throw new IllegalStateException("Not implemented.");
    }
}
