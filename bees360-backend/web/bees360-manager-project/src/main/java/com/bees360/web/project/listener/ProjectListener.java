package com.bees360.web.project.listener;

import com.bees360.event.EventPublisher;
import com.bees360.event.registry.ProjectAddressChangedEvent;
import com.bees360.event.registry.ProjectChanged;
import com.bees360.event.registry.ProjectClosedEvent;
import com.bees360.event.registry.ProjectCreate;
import com.bees360.event.registry.ProjectReopenedEvent;
import com.bees360.pipeline.PipelineService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.web.event.project.ProjectAddressChangeEvent;
import com.bees360.web.event.project.ProjectCanceledEvent;
import com.bees360.web.event.project.ProjectChangedEvent;
import com.bees360.web.event.project.ProjectCreatedEvent;
import com.bees360.web.event.project.ProjectRecoveredEvent;
import com.bees360.web.project.util.ProjectAssemble;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.time.Instant;


@Component
@Log4j2
public class ProjectListener {
    @Autowired private EventPublisher eventPublisher;
    @Autowired private PipelineService pipelineService;
    @Autowired private Bees360FeatureSwitch bees360FeatureSwitch;
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void publishProjectCreateEvent(ProjectCreatedEvent event) {
        if (bees360FeatureSwitch.isEnableCreatedEvent()) {
            return;
        }

        var project = ProjectAssemble.from(event.getProject());
        var projectCreate = new ProjectCreate();
        try {
            projectCreate.setProject(project);
            eventPublisher.publish(projectCreate);
            log.info(
                    "Successfully publish ProjectCreatedEvent on project '{}' created.",
                    project.getId());
        } catch (RuntimeException e) {
            log.error(
                    "Failed to publish ProjectCreatedEvent on project '{}' created.",
                    project.getId());
        }
    }

    @EventListener
    @Async
    public void publishProjectChanged(ProjectChangedEvent event) {
        var project = event.getProject();
        var oldProject = event.getOldProject();
        try {
            var projectChanged = new ProjectChanged();
            projectChanged.setProject(ProjectAssemble.from(project));
            projectChanged.setOldProject(ProjectAssemble.from(oldProject));
            eventPublisher.publish(projectChanged);
            log.info(
                    "Successfully publish ProjectChanged on project '{}' changed.",
                    project.getProjectId());
        } catch (RuntimeException e) {
            log.error(
                    "Failed to publish ProjectChanged on project '{}' changed.",
                    project.getProjectId(),
                    e);
        }
    }

    @EventListener
    @Async
    public void closeProjectOnProjectCancel(ProjectCanceledEvent event) {
        var project = event.getProject();
        try {
            var closedEvent = new ProjectClosedEvent();
            closedEvent.setProject(ProjectAssemble.from(project));
            eventPublisher.publish(closedEvent);
            log.info(
                    "Successfully publish ProjectClosedEvent on project '{}' closed.",
                    project.getProjectId());
        } catch (RuntimeException e) {
            log.error(
                    "Failed to publish ProjectClosedEvent on project '{}' closed.",
                    project.getProjectId(),
                    e);
        }
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void recoverProjectOnProjectRecoveredEvent(ProjectRecoveredEvent event) {
        var project = event.getProject();
        try {
            var recoveredEvent = new ProjectReopenedEvent();
            recoveredEvent.setProject(ProjectAssemble.from(project));
            eventPublisher.publish(recoveredEvent);
            log.info(
                    "Successfully publish ProjectReopenedEvent project '{}' recover event.",
                    project.getProjectId());
        } catch (RuntimeException e) {
            log.error(
                    "Failed to publish ProjectReopenedEvent project '{}' recover event.",
                    project.getProjectId(),
                    e);
        }
    }

    @EventListener
    public void publishAddressChangedEventOnProjectAddressChange(ProjectAddressChangeEvent event) {
        if (StringUtils.isEmpty(event.getOldAddress()) || StringUtils.isEmpty(event.getNewAddress())
            || StringUtils.equalsIgnoreCase(event.getOldAddress(), event.getNewAddress())) {
            return;
        }

        var addressEvent = new ProjectAddressChangedEvent();
        addressEvent.setProjectId(String.valueOf(event.getProject().getProjectId()));
        addressEvent.setOldAddress(event.getOldAddress());
        addressEvent.setNewAddress(event.getNewAddress());
        addressEvent.setUpdatedBy(String.valueOf(event.getUpdatedBy()));
        addressEvent.setUpdatedAt(Instant.now());
        eventPublisher.publish(addressEvent);
        log.info("Successfully publish address changed event {}.", addressEvent);
    }
}
