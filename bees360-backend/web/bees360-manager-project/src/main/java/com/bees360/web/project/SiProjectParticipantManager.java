package com.bees360.web.project;

import com.bees360.entity.Member;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.mapper.MemberMapper;
import com.bees360.project.ParticipantManager;
import com.bees360.service.ProjectService;
import com.bees360.user.Message.UserMessage;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import com.bees360.web.project.util.IdConverter;
import com.google.common.base.Preconditions;
import jakarta.annotation.Nullable;
import java.util.HashSet;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.SneakyThrows;

public class SiProjectParticipantManager implements ParticipantManager {
    private final RoleEnum PARTICIPANT_ROLE = RoleEnum.VISITOR;
    private final int PARTICIPANT_ROLE_ID = PARTICIPANT_ROLE.getRoleId();

    private final Long projectId;
    private final MemberMapper memberMapper;
    private ProjectService projectService;

    private UserProvider userProvider;

    public SiProjectParticipantManager(String projectId, MemberMapper memberMapper, ProjectService projectService) {
        this.projectId = IdConverter.toLong(projectId);
        this.memberMapper = memberMapper;
        this.projectService = projectService;

        Preconditions.checkArgument(Objects.nonNull(this.projectId), "Project id is required.");
    }

    public void setUserProvider(@Nullable UserProvider userProvider) {
        this.userProvider = userProvider;
    }

    @Override
    public String namespace() {
        return "project/participant";
    }

    @Override
    public User findById(String id) {
        Long userId = IdConverter.toLong(id);
        if (userId == null) {
            return null;
        }
        Member member = memberMapper.getMemberByRoleAndUserId(projectId, userId, PARTICIPANT_ROLE_ID);
        return Optional.ofNullable(member).map(m -> findUserById(m.getUserId() + "")).orElse(null);
    }

    private User findUserById(String userId) {
        User user = null;
        if (userProvider != null) {
            user = userProvider.findUserById(userId);
        }
        if (user == null) {
            return User.from(UserMessage.newBuilder().setId(userId).build());
        }
        return User.from(user.toMessage().toBuilder().clearAuthority().build());
    }

    @Override
    public Iterable<? extends User> findAllById(Iterable<String> ids) {
        Set<String> userIds = new HashSet<>(loadAllParticipants());
        return Iterables.transform(ids, id -> {
            if (userIds.contains(id)) {
                return findUserById(id);
            }
            return null;
        });
    }

    @Override
    public Iterable<? extends User> loadAll() {
        List<String> userIds = loadAllParticipants();
        return userIds.stream().map(id -> findUserById(id)).collect(Collectors.toList());
    }

    @SneakyThrows
    private List<String> loadAllParticipants() {
        List<Member> members = memberMapper.listActiveMembersByRole(projectId, PARTICIPANT_ROLE_ID);
        List<String> userIds =
            members.stream().map(Member::getUserId).map(String::valueOf).collect(Collectors.toList());
        return userIds;
    }

    @Override
    public boolean existsById(String id) {
        Long userId = null;
        try {
            userId = IdConverter.toUserId(id);
        } catch (NoSuchElementException ex) {
            return false;
        }
        Member member = memberMapper.getMemberByRoleAndUserId(projectId, userId, PARTICIPANT_ROLE_ID);
        return member != null;
    }

    @SneakyThrows
    @Override
    public String save(User user) {
        Long userId = null;
        try {
            userId = IdConverter.toUserId(user.getId());
        } catch (NoSuchElementException ex) {
            throw new IllegalArgumentException(ex.getMessage(), ex);
        }
        projectService.inviteVisitor(projectId, userId);
        return user.getId();
    }

    @Override
    public Iterable<String> saveAll(Iterable<? extends User> users) {
        return Iterables.toStream(users).map(u -> save(u)).collect(Collectors.toList());
    }

    @SneakyThrows
    @Override
    public void deleteById(String id) {
        Long userId = null;
        try {
            userId = IdConverter.toUserId(id);
        } catch (NoSuchElementException ex) {
            throw new IllegalArgumentException(ex.getMessage(), ex);
        }
        projectService.deleteVisitor(projectId, userId);
    }

    @Override
    public void deleteAllById(Iterable<String> ids) {
        Iterables.toStream(ids).forEach(this::deleteById);
    }
}
