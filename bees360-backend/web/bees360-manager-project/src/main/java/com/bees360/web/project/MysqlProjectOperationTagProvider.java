package com.bees360.web.project;

import com.bees360.entity.label.BoundProjectLabel;
import com.bees360.entity.label.ProjectLabel;
import com.bees360.project.OperationTag;
import com.bees360.project.ProjectOperationTagProvider;
import com.bees360.service.ProjectLabelService;
import com.bees360.util.Iterables;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Optional;

public class MysqlProjectOperationTagProvider implements ProjectOperationTagProvider {

    private final ProjectLabelService projectLabelService;

    public MysqlProjectOperationTagProvider(ProjectLabelService projectLabelService) {
        this.projectLabelService = projectLabelService;
    }

    @Override
    public Iterable<? extends OperationTag> findByProjectId(String projectId) {
        return Iterables.transform(loadAllLabel(projectId), OperationTagProjectLabelAdapter::adaptForProjectLabel);
    }

    private List<ProjectLabel> loadAllLabel(String projectId) {
        Optional<BoundProjectLabel> projectLabels = projectLabelService.projectLabel(Long.parseLong(projectId));
        if (projectLabels.isEmpty()) {
            return Lists.newArrayList();
        }
        return projectLabels.get().getProjectLabels();
    }
}
