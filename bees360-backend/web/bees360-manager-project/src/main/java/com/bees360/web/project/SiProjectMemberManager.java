package com.bees360.web.project;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.project.MemberManager;
import com.bees360.service.UserService;
import com.bees360.user.Message.UserMessage;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import com.bees360.web.project.util.IdConverter;
import com.google.common.base.Functions;
import com.google.common.base.Preconditions;

import lombok.SneakyThrows;

import jakarta.annotation.Nullable;

/**
 * <AUTHOR>
 */
public class SiProjectMemberManager implements MemberManager {

    private final Long projectId;
    private final UserService userService;
    private UserProvider userProvider;

    public SiProjectMemberManager(String projectId, UserService userService) {
        this.projectId = IdConverter.toLong(projectId);
        this.userService = userService;

        Preconditions.checkArgument(Objects.nonNull(this.projectId), "Project id is required.");
    }

    public void setUserProvider(@Nullable UserProvider userProvider) {
        this.userProvider = userProvider;
    }

    @Override
    public String namespace() {
        return "project/member";
    }

    @Override
    public User findById(String id) {
        return Iterables.toStream(loadAll()).filter(m -> Objects.equals(id, m.getId())).findFirst().orElse(null);
    }

    @Override
    public Iterable<? extends User> findAllById(Iterable<String> ids) {
        Map<String, User> map =
            Iterables.toStream(loadAll()).collect(Collectors.toMap(User::getId, Functions.identity()));
        return Iterables.transform(ids, id -> map.get(id));
    }

    @SneakyThrows
    @Override
    public Iterable<? extends User> loadAll() {
        return Iterables.transform(loadAllMembers(), u -> toMember(u));
    }

    @SneakyThrows
    private List<UserTinyVo> loadAllMembers() {
        List<UserTinyVo> members = userService.listMemberInProject(projectId, null);
        return members.stream().filter(u -> !Objects.equals(u.getRoleId(), RoleEnum.VISITOR.getRoleId()))
            .collect(Collectors.toList());
    }

    private User toMember(UserTinyVo userTiny) {
        RoleEnum role = RoleEnum.getEnum(userTiny.getRoleId());
        User user = findUserById(userTiny.getUserId() + "");
        UserMessage userMessage = user.toMessage().toBuilder().clearAuthority()
            .addAuthority(role.getAuthorityDisplay()).build();
        return User.from(userMessage);
    }

    private User findUserById(String userId) {
        User user = null;
        if (userProvider != null) {
            user = userProvider.findUserById(userId);
        }
        return user != null ? user : User.from(UserMessage.newBuilder().setId(userId).build());
    }

    @Override
    public boolean existsById(String id) {
        return findById(id) != null;
    }

    @Override
    public String save(User member) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public Iterable<String> saveAll(Iterable<? extends User> iterable) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public void deleteById(String id) {
        throw new IllegalStateException("Not implemented.");
    }

    @Override
    public void deleteAllById(Iterable<String> iterable) {
        throw new IllegalStateException("Not implemented.");
    }

}
