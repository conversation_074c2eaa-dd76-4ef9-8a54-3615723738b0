package com.bees360.web.project;

import com.bees360.entity.Member;
import com.bees360.entity.enums.RoleEnum;
import com.bees360.entity.vo.UserTinyVo;
import com.bees360.mapper.MemberMapper;
import com.bees360.project.Contact;
import com.bees360.project.ContactManager;
import com.bees360.project.Message;
import com.bees360.service.ProjectService;
import com.bees360.user.User;
import com.bees360.user.UserProvider;
import com.bees360.util.Iterables;
import com.bees360.util.user.Bees360UserUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class SiProjectContactManager implements ContactManager {

    private final ProjectService projectService;
    private final MemberMapper memberMapper;
    private final UserProvider userProvider;
    private final ContactManager contactManager;

    public SiProjectContactManager(ProjectService projectService,
                                   MemberMapper memberMapper, UserProvider userProvider, ContactManager contactManager) {
        this.projectService = projectService;
        this.memberMapper = memberMapper;
        this.userProvider = userProvider;
        this.contactManager = contactManager;
    }

    private List<Contact> getCreatorAndPilot(long projectId) {
        List<Member> members = memberMapper.listActiveMember(projectId).stream()
            .filter(member -> Objects.equals(member.getRole(), RoleEnum.CREATOR.getCode()) ||
                Objects.equals(member.getRole(), RoleEnum.PILOT.getCode()))
            .collect(Collectors.toList());
        Map<String, List<Integer>> memberRolesMap = Bees360UserUtils.memberRolesMap(members);
        Iterable<? extends User> usersResult = userProvider.findUserById(memberRolesMap.keySet());
        List<? extends User> users = Iterables.toList(usersResult);
        List<UserTinyVo> userTinyVos = Bees360UserUtils.toUserTinyVo(users, memberRolesMap);

        return userTinyVos.stream()
            .map(this::userTinyVOToContactConverter)
            .collect(Collectors.toList());
    }

    private Contact userTinyVOToContactConverter(UserTinyVo user) {
        return Contact.of(Message.ProjectMessage.Contact.newBuilder()
            .setFullName(StringUtils.trimToEmpty(user.getName()))
            .setPrimaryEmail(StringUtils.trimToEmpty(user.getEmail()))
            .setPrimaryPhone(StringUtils.trimToEmpty(user.getPhone()))
            .setRole(Optional.of(user.getRole()).orElse(null))
            .build());
    }

    @Override
    public String addContact(String projectId, Contact contact, String opUserId) {
        return contactManager.addContact(projectId, contact, opUserId);
    }

    @Override
    public String updateContact(Contact contact, String opUserId) {
        return contactManager.updateContact(contact, opUserId);
    }

    @Override
    public Iterable<? extends Contact> findByProjectId(String id) {
        long projectId = Long.parseLong(id);

        List<Contact> contactInNewRepository = new ArrayList<>(Iterables.toList(contactManager.findByProjectId(id)));
        // TODO@ygr member模块完善之后，不再在contact中返回creator和pilot
        contactInNewRepository.addAll(getCreatorAndPilot(projectId));

        return contactInNewRepository;
    }

    @Override
    public void removeContact(String contactId, String opUserId) {
        contactManager.removeContact(contactId, opUserId);
    }
}
