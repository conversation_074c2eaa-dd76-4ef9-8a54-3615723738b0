package com.bees360.web.project.listener;

import com.bees360.entity.User;
import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.service.MessageService;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.properties.Bees360CompanyConfig;
import com.bees360.web.event.project.ProjectCreatedEvent;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;

@Component
@Log4j2
public class EmergencyCaseListener {
    @Autowired private ProjectLabelService projectLabelService;
    @Autowired private Bees360CompanyConfig bees360CompanyConfig;
    @Autowired private MessageService messageService;

    @EventListener
    @Async
    public void setOperationTagOnMatchEmergencyCase(ProjectCreatedEvent event) {
        var project = event.getProject();
        var projectId = project.getProjectId();
        var claimNote = project.getClaimNote();
        var insuranceCompany = project.getInsuranceCompany();
        var lossDesc = getEmergencyCaseFilter(claimNote, insuranceCompany);
        if (StringUtils.isNotBlank(lossDesc)) {
            projectLabelService.markAfterEraseLabel(
                    projectId,
                    Collections.singletonList(
                            ProjectLabelEnum.ATTENTION_NEEDED_FOR_LOSS_TYPE.getLabelId()),
                    User.AI_ID,
                    SystemTypeEnum.BEES360);
        }
    }

    @EventListener
    @Async
    public void sendEmailOnMatchEmergencyCase(ProjectCreatedEvent event) {
        var project = event.getProject();
        var claimNote = project.getClaimNote();
        var insuranceCompany = project.getInsuranceCompany();
        var lossDesc = getEmergencyCaseFilter(claimNote, insuranceCompany);
        if (StringUtils.isNotBlank(lossDesc)) {
            var note = lossDesc.replaceAll("\r?\n", "<br>");
            messageService.sendEmailNoticeOnEmergencyCase(note, project);
        }
    }

    private String getEmergencyCaseFilter(String claimNote, Long insuranceCompany) {
        if (Objects.isNull(insuranceCompany) || StringUtils.isEmpty(claimNote)) {
            return Strings.EMPTY;
        }
        // 获取保险公司配置
        var configItem = bees360CompanyConfig.findConfig(insuranceCompany);
        var emergencyCasePattern =
                Optional.ofNullable(configItem)
                        .map(Bees360CompanyConfig.CompanyConfigItem::getEmergencyCaseFilterRegex)
                        .orElse(null);
        var lossDescPattern =
                Optional.ofNullable(configItem)
                        .map(Bees360CompanyConfig.CompanyConfigItem::getLossDescExtractRegex)
                        .orElse(null);
        if (Objects.isNull(emergencyCasePattern) || Objects.isNull(lossDescPattern)) {
            return Strings.EMPTY;
        }
        Matcher lossDescMatcher = lossDescPattern.matcher(claimNote + "\n\n");
        // 将规则作用到字符串上，并进行符合规则的子串查找
        if (lossDescMatcher.find()) {
            // 获取匹配后LossDescription组结果
            String lossDescription = lossDescMatcher.group("LossDescription").trim();
            Matcher emergencyCaseMatcher = emergencyCasePattern.matcher(lossDescription);
            if (emergencyCaseMatcher.find()) {
                return lossDescription;
            }
        }
        return Strings.EMPTY;
    }
}
