package com.bees360.web.project;

import com.bees360.entity.label.ProjectLabel;
import com.bees360.project.OperationTag;

import jakarta.annotation.Nullable;

class OperationTagProjectLabelAdapter implements OperationTag {

    private final ProjectLabel projectLabel;

    private OperationTagProjectLabelAdapter(ProjectLabel projectLabel) {
        this.projectLabel = projectLabel;
    }

    public static OperationTag adaptForProjectLabel(ProjectLabel projectLabel) {
        return new OperationTagProjectLabelAdapter(projectLabel);
    }

    @Nullable
    @Override
    public String getTitle() {
        return projectLabel.getLabelName();
    }

    @Nullable
    @Override
    public String getDescription() {
        return projectLabel.getLabelDesc();
    }

    @Nullable
    @Override
    public String getColor() {
        return null;
    }

    @Nullable
    @Override
    public String getIcon() {
        return null;
    }

    @Nullable
    @Override
    public String getId() {
        return String.valueOf(projectLabel.getLabelId());
    }
}
