package com.bees360.web.project;

import com.bees360.api.UnimplementedException;
import com.bees360.attachment.BatchAttachment;
import com.bees360.base.exception.ServiceException;
import com.bees360.building.Message;
import com.bees360.entity.Project;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.CreationChannelType;
import com.bees360.project.ContactRoleEnum;
import com.bees360.project.GenericProjectCreator;
import com.bees360.project.ProjectCreationRequest;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectTypeEnum;
import com.bees360.service.ProjectService;
import com.bees360.service.UserService;
import jakarta.validation.constraints.NotNull;
import java.time.ZoneOffset;
import java.util.Optional;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

@Log4j2
public class SiGenericProjectCreator implements GenericProjectCreator {

    private final ProjectService projectService;

    private final UserService userService;

    public SiGenericProjectCreator(ProjectService projectService, UserService userService) {
        this.projectService = projectService;
        this.userService = userService;
        log.info("Created {}.", this);
    }

    @Override
    public ProjectII create(@NotNull ProjectCreationRequest projectCreationRequest, boolean allowDuplication, String creationChannel) {
        var user = userService.getUserById(Long.parseLong(projectCreationRequest.getCreatedBy()));
        var project = toProject(projectCreationRequest, user.getCompanyId());
        try {
            projectService.initProjectData(project, CreationChannelType.valueOf(creationChannel));
        } catch (ServiceException e) {
            throw new IllegalStateException(
                "Failed to sync project creation to mysql with request (%s)".formatted(projectCreationRequest.toMessage()), e);
        }

        return ProjectII.from(
            com.bees360.project.Message.ProjectMessage.newBuilder()
                .setId(String.valueOf(project.getProjectId()))
                .build());
    }

    @Override
    public ProjectII create(
        @NotNull ProjectCreationRequest projectCreationRequest,
        boolean allowDuplication,
        String creationChannel,
        BatchAttachment batchAttachment) {
        throw new UnimplementedException();
    }

    private Project toProject(ProjectCreationRequest projectCreationRequest, Long companyId) {
        var project = new Project();
        var createdBy = Long.parseLong(projectCreationRequest.getCreatedBy());

        project.setProjectId(Long.parseLong(projectCreationRequest.getProjectId()));
        project.setCreatedBy(createdBy);
        project.setTestFlag(projectCreationRequest.isTestCase());

        var contract = projectCreationRequest.getContract();
        project.setInsuranceCompany(Long.valueOf(contract.getInsuredBy().getId()));
        project.setRepairCompany(Long.valueOf(contract.getProcessedBy().getId()));
        project.setOperatingCompany(projectCreationRequest.getOperatingCompany());
        project.setClaimNote(projectCreationRequest.getNote());
        project.setCompanyId(companyId);
        if (projectCreationRequest.getProjectType() == ProjectTypeEnum.CLAIM) {
            var claim = projectCreationRequest.getClaim();
            project.setInspectionNumber(claim.getClaimNo());
            project.setServiceType(claim.getServiceType().getCode());
            project.setClaimType(claim.getClaimType().getCode());
            project.setDamageEventTime(
                Optional.ofNullable(claim.getDateOfLoss())
                    .map(d -> d.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli())
                    .orElse(null));
        } else {
            var underwriting = projectCreationRequest.getUnderwriting();
            project.setServiceType(underwriting.getServiceType().getCode());
            project.setClaimType(ClaimTypeEnum.UNDERWRITING_FIRST_INSPECTION.getCode());
        }

        var policy = projectCreationRequest.getPolicy();
        if (policy != null) {
            project.setPolicyNumber(policy.getPolicyNo());
            project.setPolicyEffectiveDate(policy.getPolicyEffectiveDate());

            var address = policy.getAddress();
            if (address != null) {
                project.setAddressId(address.getId());
                project.setAddress(address.getStreetAddress());
                project.setCity(address.getCity());
                project.setCountry(address.getCountry());
                project.setState(address.getState());
                project.setZipCode(address.getZip());
                project.setLat(address.getLat());
                project.setLng(address.getLng());
                project.setGpsIsApproximate(Optional.ofNullable(address.isGpsApproximate()).orElse(true));
            }

            var building = policy.getBuilding();
            if (building != null) {
                project.setProjectType(
                    building.getType() == Message.BuildingType.UNRECOGNIZED ? null :  building.getType().getNumber());
                project.setYearBuilt(
                    Optional.ofNullable(building.getYearBuilt()).map(String::valueOf).orElse(null));
            }
        }

        var inspection = projectCreationRequest.getInspection();
        if (inspection != null) {
            project.setInspectionNumber(inspection.getInspectionNo());
            project.setDueDate(Optional.ofNullable(inspection.getDueDate())
                .map(d -> d.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli())
                .orElse(null));
        }

        projectCreationRequest.getContact().forEach(
            e -> {
                if (StringUtils.equals(e.getRole(), ContactRoleEnum.INSURED.getName())) {
                    project.setAssetOwnerName(e.getFullName());
                    project.setAssetOwnerEmail(e.getPrimaryEmail());
                    project.setAssetOwnerPhone(e.getPrimaryPhone());
                } else if (StringUtils.equals(e.getRole(), ContactRoleEnum.AGENT.getName())) {
                    project.setAgentContactName(e.getFullName());
                    project.setAgentPhone(e.getPrimaryPhone());
                    project.setAgentEmail(e.getPrimaryEmail());
                }
            }
        );

        return project;
    }
}
