package com.bees360.web.project;

import java.util.List;
import java.util.Optional;

import com.bees360.base.exception.NotImplementedException;
import com.bees360.project.ExternalIntegration;
import com.bees360.project.ExternalIntegrationManager;
import com.bees360.service.ProjectService;
import com.bees360.web.project.util.ProjectAssemble;

/**
 * <AUTHOR>
 */
public class SiProjectExternalIntegrationManager implements ExternalIntegrationManager {

    private final ProjectService projectService;

    public SiProjectExternalIntegrationManager(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Override
    public Iterable<? extends ExternalIntegration> findAllById(Iterable<String> ids) {
        throw new NotImplementedException();
    }

    @Override
    public ExternalIntegration findBySubReference(
        String integrationType, String referenceNumber, String subReferenceNumber) {
        throw new NotImplementedException();
    }

    @Override
    public Iterable<? extends ExternalIntegration> findAllByReference(
        String integrationType, Iterable<String> referenceNumbers) {
        throw new NotImplementedException();
    }

    @Override
    public Iterable<? extends ExternalIntegration> findAllByProjectId(String projectId) {
        var projectIdLong = Long.parseLong(projectId);
        Optional<com.bees360.entity.Project> projectDO = projectService.findById(projectIdLong);
        if (projectDO.isEmpty()) {
            return List.of();
        }
        return ProjectAssemble.toIntegration(projectDO.get());
    }

    @Override
    public Iterable<? extends ExternalIntegration> findAllByProjectId(Iterable<String> projectIds) {
        throw new NotImplementedException();
    }

    @Override
    public String save(ExternalIntegration externalIntegration) {
        throw new NotImplementedException();
    }

    @Override
    public String create(
        String dataset,
        String integrationType,
        String referenceNumber,
        String subReferenceNumber) {
        throw new NotImplementedException();
    }

    @Override
    public void setProjectId(String id, String projectId) {
        throw new NotImplementedException();
    }

    @Override
    public boolean deleteByProjectIdAndType(String projectId, String integrationType) {
        throw new NotImplementedException();
    }
}
