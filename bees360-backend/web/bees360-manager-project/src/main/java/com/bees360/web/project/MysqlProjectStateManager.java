package com.bees360.web.project;

import com.bees360.base.exception.ServiceException;
import com.bees360.base.exception.UncheckedServiceException;
import com.bees360.entity.User;
import com.bees360.entity.enums.ClaimTypeEnum;
import com.bees360.entity.enums.ProjectLabelEnum;
import com.bees360.entity.enums.SystemTypeEnum;
import com.bees360.project.Message;
import com.bees360.project.Message.ProjectMessage.ProjectState.ProjectStateEnum;
import com.bees360.project.ProjectII;
import com.bees360.project.ProjectIIRepository;
import com.bees360.project.state.ProjectOperationFeedback;
import com.bees360.project.state.ProjectState;
import com.bees360.project.state.ProjectStateChangeReason;
import com.bees360.project.state.ProjectStateChangeReasonManager;
import com.bees360.project.state.ProjectStateManager;
import com.bees360.project.state.ProjectStateQuery;
import com.bees360.service.ProjectLabelService;
import com.bees360.service.ProjectService;
import com.bees360.service.ProjectStatusService;
import com.bees360.service.UserService;
import com.bees360.service.properties.Bees360FeatureSwitch;
import com.bees360.util.Iterables;
import lombok.Getter;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Pattern;

@Log4j2
public class MysqlProjectStateManager implements ProjectStateManager {
    enum ProjectStateChangeEnum {
        IBEES_UPLOADED("IBEES UPLOADED"),
        COMPLETED("COMPLETED"),
        RECEIVE_ERROR("RECEIVED ERROR"),
        DUPLICATED_INSPECTION("DUPLICATE INSPECTION"),
        TEST_OR_DEMO_CASE("TEST OR DEMO CASE"),
        ;

        /**
         * must be key of project_state_change_reason (cannot use display_text)
         */
        @Getter private final String value;

        public static ProjectStateChangeEnum getEnumIgnoreCase(String value) {
            for (ProjectStateChangeEnum projectStateChangeEnum : values()) {
                if (StringUtils.equalsIgnoreCase(projectStateChangeEnum.getValue(), value)) {
                    return projectStateChangeEnum;
                }
            }
            return null;
        }

        ProjectStateChangeEnum(String value) {
            this.value = value;
        }
    }

    private final ProjectStateManager delegate;
    private final ProjectStatusService projectStatusService;
    private final UserService userService;
    private final ProjectService projectService;
    private final ProjectIIRepository projectIIRepository;
    private final ProjectStateChangeReasonManager projectStateChangeReasonManager;
    private final ProjectLabelService projectLabelService;
    private final Optional<Function<String, Long>> closeReasonToOperationTagIdConverter;
    private final Function<String, ProjectLabelEnum> changeReasonKeyToLabelEnumConverter;
    private final Bees360FeatureSwitch featureSwitch;

    private static final Pattern pattern = Pattern.compile("^\\d+$");

    public MysqlProjectStateManager(
        @NonNull ProjectStateManager delegate,
        @NonNull ProjectStatusService projectStatusService,
        @NonNull UserService userService,
        @NonNull ProjectService projectService,
        @NonNull ProjectIIRepository projectIIRepository,
        @NonNull ProjectStateChangeReasonManager projectStateChangeReasonManager,
        @NonNull ProjectLabelService projectLabelService,
        Optional<Function<String, Long>> closeReasonToOperationTagIdConverter,
        Function<String, ProjectLabelEnum> changeReasonKeyToLabelEnumConverter,
        Bees360FeatureSwitch featureSwitch) {
        this.delegate = delegate;
        this.projectStatusService = projectStatusService;
        this.userService = userService;
        this.projectService = projectService;
        this.projectIIRepository = projectIIRepository;
        this.projectStateChangeReasonManager = projectStateChangeReasonManager;
        this.projectLabelService = projectLabelService;
        this.closeReasonToOperationTagIdConverter = closeReasonToOperationTagIdConverter;
        this.changeReasonKeyToLabelEnumConverter = changeReasonKeyToLabelEnumConverter;
        this.featureSwitch = featureSwitch;
    }

    @Override
    public ProjectOperationFeedback changeProjectState(
            String projectId,
            ProjectStateEnum state,
            String changeReason,
            String changedBy,
            @Nullable String comment,
            @Nullable Long version,
            boolean changeForce) {

        // TODO 临时读取pg数据避免调用pg后is_canceled字段被修改, 后续移除mysql后需要移除
        var isCanceledProject = false;
        if (ProjectStateEnum.PROJECT_OPEN.equals(state)) {
            isCanceledProject =
                    Optional.ofNullable(projectIIRepository.findById(projectId))
                            .map(ProjectII::isCanceled)
                            .orElse(false);
        }

        var stateChangeReason = findStateChangeReason(changeReason);
        var projectOperationFeedback = delegate.changeProjectState(projectId, state, stateChangeReason.getId(), changedBy, comment, version, changeForce);
        try {
            if (Message.ProjectOperationFeedback.OperationFeedback.ALLOWED.equals(projectOperationFeedback.getOperationFeedback())) {
                doHandleOnStateChange(projectId, state, stateChangeReason, changedBy, comment, isCanceledProject);
            }
            return projectOperationFeedback;
        } catch (ServiceException e) {
            log.error(
                    "Failed to handle project '{}' after state changed to ({}, {}). Please handle it manually.",
                    projectId,
                    state,
                    changeReason,
                    e);
            throw new UncheckedServiceException(e);
        }
    }

    public void doHandleOnStateChange(
            String projectId,
            ProjectStateEnum state,
            ProjectStateChangeReason changeReason,
            String changedBy,
            @Nullable String comment,
            boolean isCanceledProject)
            throws ServiceException {
        long changeByWebUserId = Optional.ofNullable(userService.toWebUserId(changedBy)).orElse(User.AI_ID);
        var projectIdL = Long.parseLong(projectId);

        var changeReasonKey = changeReason.getKey();
        // modify ProjectLabelEnum#getEnumByCancelReason to changeReasonKeyToLabelEnumConverter
        var cancellation = changeReasonKeyToLabelEnumConverter.apply(changeReasonKey);

        var labelIdByConverter =
                closeReasonToOperationTagIdConverter
                        .map(converter -> converter.apply(changeReasonKey))
                        .orElse(null);
        var project = projectService.getById(projectIdL);
        var claimType = project.getClaimType();

        if ((cancellation != null || labelIdByConverter != null) && ClaimTypeEnum.isUnderwriting(claimType)) {
            var labelId = cancellation == null ? labelIdByConverter : cancellation.getLabelId();
            projectLabelService.markAfterEraseLabel(
                    projectIdL, List.of(labelId), changeByWebUserId, SystemTypeEnum.BEES360);
            if (cancellation != null) {
                return;
            }
        }

        // complete project if change reason is custom
        if (StringUtils.isEmpty(changeReasonKey) && ProjectStateEnum.PROJECT_CLOSE.equals(state)) {
            handleCompleted(projectIdL, claimType, changeByWebUserId, comment);
            return;
        }

        var reasonEnum = ProjectStateChangeEnum.getEnumIgnoreCase(changeReasonKey);
        if (reasonEnum == null) {
            if (ProjectStateEnum.PROJECT_OPEN.equals(state)) {
                handleOnReopen(projectIdL, changeByWebUserId, isCanceledProject);
            } else if (ProjectStateEnum.PROJECT_CLOSE.equals(state)) {
                projectService.handelCloseReport(projectIdL);
                if (featureSwitch.isEnableCloseOutToCancelled() || !ClaimTypeEnum.isUnderwriting(claimType)) {
                    projectService.cancelProject(projectIdL, changeByWebUserId, changeReason.getDisplayText());
                }
            }
            return;
        }

        switch (reasonEnum) {
            case IBEES_UPLOADED:
                projectStatusService.changeOnIBeesUploaded(changeByWebUserId, projectIdL);
                break;

            case COMPLETED:
                handleCompleted(projectIdL, claimType, changeByWebUserId, comment);
                break;

            case RECEIVE_ERROR:
                projectStatusService.changeOnReceiveError(changeByWebUserId, projectIdL, comment);
                break;
            case DUPLICATED_INSPECTION:
                projectService.cancelProject(projectIdL, changeByWebUserId, comment);
                break;

            case TEST_OR_DEMO_CASE:
                projectService.deleteProject(projectIdL, User.BEES_PILOT_SYSTEM);
                break;
        }
    }

    private void handleCompleted(long projectId, int claimType, long userId, String comment) {
        if (ClaimTypeEnum.isClaim(claimType)) {
            projectStatusService.changeOnClientReceived(
                userId, projectId, SystemTypeEnum.BEES360, comment);
        } else {
            projectStatusService.changeOnReturnedToClient(
                userId, projectId, comment);
        }
    }

    private void handleOnReopen(long projectId, long userId, boolean isCanceledProject) throws ServiceException {
        if (isCanceledProject) {
            projectService.recoverProject(projectId, userId);
        }

        var projectLabelOptional = projectLabelService.projectLabel(projectId);
        if (projectLabelOptional.isPresent()) {
            var projectLabel = projectLabelOptional.get();
            if (projectLabel.getProjectLabels().stream()
                    .anyMatch(label -> ProjectLabelEnum.isCancellation(label.getLabelId()))) {
                // clear project operation tag
                projectLabelService.markAfterEraseLabel(
                        projectId, List.of(), userId, SystemTypeEnum.BEES360);
            }
        }
    }

    /**
     * if: 兼容 changeReason 传入的是 displayText 的情况
     *
     * @param changeReason changeReasonId or changeReasonDisplayText
     * @return changeReasonId
     */
    private ProjectStateChangeReason findStateChangeReason(String changeReason) {
        // toDo： 不传 displayText 之后，if 逻辑可删除
        if (!pattern.matcher(changeReason).find()) {
            var stateChangeReason =
                Iterables.toStream(
                        projectStateChangeReasonManager.findByQuery(
                            List.of(), List.of(), List.of(changeReason)))
                    .findFirst()
                    .orElse(null);
            if (Objects.nonNull(stateChangeReason)) {
                return stateChangeReason;
            }
            log.info("No change reason with displayText %s found".formatted(changeReason));
        }
        var stateChangeReason =
            Iterables.toStream(
                    projectStateChangeReasonManager.findByQuery(
                        List.of(changeReason), List.of(), List.of()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(stateChangeReason)) {
            throw new IllegalArgumentException("No change reason with id %s found".formatted(changeReason));
        }
        return stateChangeReason;
    }

    @Override
    public Iterable<String> findProjectByProjectState(
            Iterable<ProjectStateEnum> iterable, ProjectStateQuery projectStateQuery) {
        return delegate.findProjectByProjectState(iterable, projectStateQuery);
    }

    @Override
    public Iterable<String> findProjectByStateChangeReason(
            Iterable<String> iterable, ProjectStateQuery projectStateQuery) {
        return delegate.findProjectByStateChangeReason(iterable, projectStateQuery);
    }

    @Override
    public Iterable<? extends ProjectState> findStateHistoryByProjectId(String s) {
        throw new UnsupportedOperationException();
    }
}
