SUBDIRS := $(wildcard */.)
EXCLUDEDIR := api/.
SUBDIRS := $(filter-out $(EXCLUDEDIR), $(SUBDIRS))

MODE=$(mode)
PROPERTY=$(property)
PARAMS := $(MODE) $(PROPERTY)
PARAM_TEST_SKIP := -Dmaven.test.skip=true

ifeq ($(strip $(PARAMS)),)
PARAMS := -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn
endif

make:  clean compile

compile:
	./mvnw compile $(PARAMS)

test:
	./mvnw test $(PARAMS)

package:
	./mvnw -U package $(PARAMS)

install:
	./mvnw install dependency:copy-dependencies $(PARAMS) ${PARAM_TEST_SKIP}

dist: install

$(SUBDIRS):
	$(MAKE) -C $@ $(MAKECMDGOALS)

check: $(SUBDIRS)
	@echo "there is nothing to do in parent project"

clean:
	./mvnw clean $(PARAMS)

.PHONY: make test dist check clean $(SUBDIRS)
