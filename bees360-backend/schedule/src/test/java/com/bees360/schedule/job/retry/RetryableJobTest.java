package com.bees360.schedule.job.retry;

import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
public class RetryableJobTest {

    @Slf4j
    public static class RetryableJobSample extends RetryableJob {

        @Override
        public RetryableOperation getRetryableOperation() {
            return (jobContext) -> {
                // do something
                log.info("job {} is running...", jobContext.getJobDetail().getKey());
            };
        }

        @Override
        public RetryExceptionHandler getRetryExceptionHandler() {
            return (jobRetryContext) -> {
                // 该job（job相关的trigger）最多只能执行10次
                return jobRetryContext.getRetryCount() <= 10;
            };
        }
    }
}
