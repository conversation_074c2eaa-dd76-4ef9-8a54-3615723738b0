package com.bees360.schedule.job.retry.handler;

import com.bees360.schedule.exception.RetryableException;
import com.bees360.schedule.job.retry.JobRetryContext;
import org.junit.jupiter.api.Test;
import org.quartz.JobExecutionContext;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
public class RetryExceptionHandlerBuilderTest {

    @Test
    public void alwaysRetry() {
        RetryExceptionHandler handler = RetryExceptionHandlers.newBuilder().retryAlways().build();

        JobRetryContext ctx = new JobRetryContext(null, 0, new IOException());
        assertTrue(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 100, new IOException());
        assertTrue(handler.handleException(ctx));
    }

    @Test
    public void neverRetry() {
        RetryExceptionHandler handler = RetryExceptionHandlers.newBuilder().retryNever().build();

        JobRetryContext ctx = new JobRetryContext(null, 0, new IOException());
        assertFalse(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 100, new IOException());
        assertFalse(handler.handleException(ctx));
    }

    @Test
    public void retrySpecifiedCount() {
        RetryExceptionHandler handler = RetryExceptionHandlers.newBuilder().retryCount(3).build();

        JobRetryContext ctx = new JobRetryContext(null, 0, new IOException());
        assertTrue(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 2, new IOException());
        assertTrue(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 3, new IOException());
        assertFalse(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 4, new IOException());
        assertFalse(handler.handleException(ctx));
    }

    @Test
    public void retryOnCauses() {
        RetryExceptionHandler handler = RetryExceptionHandlers.newBuilder()
            .retryOnCause(IOException.class, IllegalStateException.class).build();

        JobRetryContext ctx = new JobRetryContext(null, 100, new IOException());
        assertTrue(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 100, new IllegalStateException());
        assertTrue(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 100, new IllegalArgumentException());
        assertFalse(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 100, new RetryableException());
        assertTrue(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 100, new RetryableException(new IOException()));
        assertTrue(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 100, new RetryableException(new IllegalStateException()));
        assertTrue(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 100, new RetryableException(new IllegalArgumentException()));
        assertFalse(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 100, null);
        assertTrue(handler.handleException(ctx));
    }

    @Test
    public void retryOnCausesLimitByCount() {
        RetryExceptionHandler handler = RetryExceptionHandlers.newBuilder()
            .retryOnCause(IOException.class, IllegalStateException.class)
            .retryCount(3)
            .build();

        JobRetryContext ctx = new JobRetryContext(null, 1, new IOException());
        assertTrue(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 5, new IOException());
        assertFalse(handler.handleException(ctx));

        ctx = new JobRetryContext(null, 1, new IllegalArgumentException());
        assertFalse(handler.handleException(ctx));
    }

    @Test
    public void retryPost() {
        AtomicInteger countPostRetry = new AtomicInteger();
        AtomicInteger countPostRetryStop = new AtomicInteger();
        RetryExceptionHandler handler = RetryExceptionHandlers.newBuilder()
            .retryOnCause(IOException.class, IllegalStateException.class)
            .retryCount(3)
            .postRetry((ctx) ->
                countPostRetry.incrementAndGet())
            .postRetryStop((ctx) ->
                countPostRetryStop.incrementAndGet())
            .build();

        JobRetryContext ctx = new JobRetryContext(null, 1, new IOException());
        assertTrue(handler.handleException(ctx));
        assertTrue(handler.handleException(ctx));
        ctx = new JobRetryContext(null, 5, new IOException());
        assertFalse(handler.handleException(ctx));

        assertEquals(2, countPostRetry.intValue());
        assertEquals(1, countPostRetryStop.intValue());
    }


    @Test
    public void retryPostWhenNeverRetry() {
        AtomicBoolean runPostRetryStop = new AtomicBoolean(false);
        RetryExceptionHandler handler = RetryExceptionHandlers.newBuilder()
            .retryNever()
            .postRetryStop((ctx) -> runPostRetryStop.set(true))
            .build();
        JobRetryContext ctx = new JobRetryContext(null, 0, new IOException());
        assertFalse(handler.handleException(ctx));
        assertEquals(true, runPostRetryStop.get());

        runPostRetryStop.set(false);
        ctx.setRetryCount(1);
        assertFalse(handler.handleException(ctx));
        assertEquals(true, runPostRetryStop.get());
    }
}
