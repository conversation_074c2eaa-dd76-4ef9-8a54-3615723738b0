package com.bees360.schedule.config;

import java.time.Duration;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.bees360.schedule.listener.LoggingOnSchedulerError;
import com.bees360.schedule.listener.LoggingOnTriggerMisfireListener;
import com.bees360.schedule.service.SchedulerManagerTransactionalDelegate;
import com.bees360.schedule.support.AutowiringSpringBeanJobFactory;
import com.bees360.schedule.support.SchedulerFactoryBeanAssemble;
import com.bees360.schedule.support.SchedulerProviderManager;
import lombok.extern.log4j.Log4j2;
import org.quartz.Calendar;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobListener;
import org.quartz.SchedulerListener;
import org.quartz.Trigger;
import org.quartz.TriggerListener;
import org.quartz.spi.JobFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.quartz.QuartzProperties;
import org.springframework.boot.autoconfigure.quartz.SchedulerFactoryBeanCustomizer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.bees360.schedule.support.DefaultSchedulerProviderManager;
import com.bees360.schedule.support.SchedulerFactoryBeanProvider;
import com.bees360.schedule.support.SchedulerProvider;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> Yang
 */
@Log4j2
@RequiredArgsConstructor
@Configuration
public class QuartzBeanConfig {

    private final QuartzProperties properties;

    private final JobDetail[] jobDetails;

    private final Map<String, Calendar> calendars;

    private final Trigger[] triggers;

    private final ApplicationContext context;

    private final SchedulerFactoryBeanAssemble schedulerFactoryBeanAssemble;

    @Bean
    public DefaultSchedulerProviderManager schedulerProviderManager(List<SchedulerProvider> providers) {
        DefaultSchedulerProviderManager providerManager = new DefaultSchedulerProviderManager(providers);
        return providerManager;
    }

    @Bean
    public SchedulerManagerTransactionalDelegate schedulerManagerTransactionalDelegator(SchedulerProviderManager
            schedulerProviderManager) {
        return new SchedulerManagerTransactionalDelegate(schedulerProviderManager);
    }

    @Bean
    @Primary
    public SchedulerFactoryBeanProvider defaultQuartzScheduler() {
        Collection<Class<? extends Job>> jobClasses = new HashSet<>();
        // as default to support any job
        jobClasses.add(null);
        SchedulerFactoryBeanProvider schedulerFactoryBean = new SchedulerFactoryBeanProvider(jobClasses);

        schedulerFactoryBeanAssemble.assemble(schedulerFactoryBean, this.properties.getSchedulerName());
        if (this.jobDetails != null && this.jobDetails.length > 0) {
            schedulerFactoryBean.setJobDetails(this.jobDetails);
        }
        if (this.calendars != null && !this.calendars.isEmpty()) {
            schedulerFactoryBean.setCalendars(this.calendars);
        }
        if (this.triggers != null && this.triggers.length > 0) {
            schedulerFactoryBean.setTriggers(this.triggers);
        }
        schedulerFactoryBeanAssemble.customize(schedulerFactoryBean);
        return schedulerFactoryBean;
    }

    @Bean
    @ConditionalOnProperty(prefix = "scheduler.quartz", name = "start-up-delay")
    SchedulerFactoryBeanCustomizer startUpDelayCustomizer(@Value("${scheduler.quartz.start-up-delay}") Duration startUpDelay) {
        return bean -> {
            bean.setStartupDelay((int) startUpDelay.toSeconds());
            log.info("Config {} with startUpDelay {}.", bean, startUpDelay);
        };
    }

    @Bean
    public JobFactory autowiringSpringBeanJobFactory() {
        AutowiringSpringBeanJobFactory factory = new AutowiringSpringBeanJobFactory();
        factory.setApplicationContext(context);
        return factory;
    }

    /**
     * 设置 schedulerFactoryBean 的 jobFactory
     *
     * @return
     */
    @Bean
    public SchedulerFactoryBeanCustomizer schedulerFactoryBeanCustomizer(Optional<JobFactory> jobFactory) {
        if(jobFactory.isPresent()) {
            return schedulerFactoryBean -> {
                schedulerFactoryBean.setJobFactory(jobFactory.get());
            };
        } else {
            return schedulerFactoryBean -> {};
        }
    }

    /**
     * 全局监听misfired trigger，然后输出日志。
     */
    @Bean
    public LoggingOnTriggerMisfireListener loggingOnTriggerMisfireListener() {
        return new LoggingOnTriggerMisfireListener("GlobalLoggingOnTriggerMisfireListener");
    }

    @Bean
    public LoggingOnSchedulerError loggingOnSchedulerError() {
        return new LoggingOnSchedulerError();
    }

    /**
     * 将Spring容器中的监听器注入到Scheduler中
     */
    @Bean
    public SchedulerFactoryBeanCustomizer schedulerFactoryBeanCustomizerForTriggerListener(
            ObjectProvider<SchedulerListener> schedulerListeners, ObjectProvider<TriggerListener> triggerListeners,
            ObjectProvider<JobListener> jobListeners) {
        return schedulerFactoryBean -> {
            schedulerFactoryBean.setSchedulerListeners(schedulerListeners.orderedStream().toArray(SchedulerListener[]::new));
            schedulerFactoryBean.setGlobalTriggerListeners(triggerListeners.orderedStream().toArray(TriggerListener[]::new));
            schedulerFactoryBean.setGlobalJobListeners(jobListeners.orderedStream().toArray(JobListener[]::new));
        };
    }
}
