package com.bees360.schedule.job.retry;

import com.bees360.schedule.exception.RetryableException;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;
import java.util.List;
import java.util.Objects;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;

/**
 * 批量操作可重试类，可设置itemRetryCount（每一项操作重试的次数），如果同时设置了父类RetryableJob的retryCount参数 每一项操作将重试 itemRetryCount*retryCount
 * （可以理解成retryCount为外层循环，itemRetryCount为内层循环） 如果对执行顺序有严格要求，retryCount应设置为1。例如itemRetryCount设置为10，重试10此如果都没有成功，
 * 打印error日志或其他方式处理错误，然后继续执行下一项。如果没有严格执行顺序的批量操作，两个值都可以设置。
 * <p>
 * 对于JobDataMap中的值，如果是引用类型，修改属性则会在执行之后更新到数据库，如果是基础类型，修改之后需要手动调用JobDataMap#put方法更新数据。
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BatchRetryableJob<T> extends RetryableJob {

    private static final String ITEM_FAILED_RETRY_COUNT = "itemRetryCount";

    private static final String EXECUTE_INDEX = "executeIndex";

    @Setter
    private int itemRetryCount = 0;

    @Setter
    private int executeIndex = 0;

    @Override
    protected RetryableOperation getRetryableOperation() {
        return (context) -> {
            RetryExceptionHandler itemExHandler = getItemRetryExceptionHandler();
            List<JobData<T>> tList = getJobDataList();
            Throwable lastException = null;
            try {
                for (; executeIndex < tList.size(); executeIndex++) {
                    JobData<T> jobData = tList.get(executeIndex);
                    if (jobData.isExecute()) {
                        continue;
                    }
                    JobRetryContext jobRetryContext = new JobRetryContext(context, itemRetryCount);
                    RetryableOperation retryableOperation = getItemRetryableOperation(jobData);
                    doItemJobRetry(jobRetryContext, retryableOperation, itemExHandler);
                    jobData.setExecute(Objects.isNull(jobRetryContext.getLastEx()));
                    if (Objects.nonNull(jobRetryContext.getLastEx())) {
                        lastException = jobRetryContext.getLastEx().getCause();
                    }
                }
            } finally {
                allItemsExecute(context, lastException);
            }
        };
    }

    private void doItemJobRetry(JobRetryContext jobRetryContext, RetryableOperation operation,
        RetryExceptionHandler itemExHandler) {
        boolean retryable = false;
        int retryCount = jobRetryContext.getRetryCount() + 1;
        jobRetryContext.setRetryCount(retryCount);
        updateContextIntValue(jobRetryContext.getJobContext(), ITEM_FAILED_RETRY_COUNT, retryCount);
        try {
            operation.execute(jobRetryContext.getJobContext());
        } catch (Exception e) {
            jobRetryContext.setLastEx(e);
            try {
                if (isRetryable(e)) {
                    retryable = itemExHandler != null && itemExHandler.handleException(jobRetryContext);
                }
            } catch (Exception ex) {
                log.error("Unexpected error happens when try to process job {}",
                    jobRetryContext.getJobContext().getJobDetail().getKey(), ex);
                retryable = false;
            }
            if (retryable) {
                jobRetryContext.setLastEx(null);
                doItemJobRetry(jobRetryContext, operation, itemExHandler);
            }
        } finally {
            updateContextIntValue(jobRetryContext.getJobContext(), ITEM_FAILED_RETRY_COUNT, 0);
        }
    }

    private void allItemsExecute(JobExecutionContext jobContext, Throwable lastException) throws RetryableException {
        String keyName = jobContext.getJobDetail().getKey().getName();
        List<JobData<T>> tList = getJobDataList();
        int succeedCount = (int) tList.stream().filter(JobData::isExecute).count();
        int failedCount = tList.size() - succeedCount;
        if (failedCount > 0) {
            log.warn("{} jobs were successfully executed, but {} failed. keyName is {}", succeedCount, failedCount,
                keyName);
            // 抛出RetryableException可能外层会继续重试。如果外层不继续重试，error日志或错误处理由外层的ExceptionHandler处理
            // 即父类的getRetryExceptionHandler
            throw new RetryableException(lastException);
        } else {
            log.info("All {} tasks were carried out successfully. keyName is {}", succeedCount, keyName);
        }
        // 设置为0，如果失败重试从0开始。
        updateContextIntValue(jobContext, EXECUTE_INDEX, 0);
        updateContextIntValue(jobContext, ITEM_FAILED_RETRY_COUNT, 0);
    }

    protected abstract List<JobData<T>> getJobDataList();

    protected abstract RetryableOperation getItemRetryableOperation(JobData<T> jobData);

    protected abstract RetryExceptionHandler getItemRetryExceptionHandler();
}
