package com.bees360.schedule.job.retry.handler;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.bees360.schedule.job.retry.JobRetryContext;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class CompositeRetryExceptionHandler implements RetryExceptionHandler {

    private List<RetryExceptionHandler> handlers;
    /**
     * 是否需要全部都为true才返回true
     */
    private boolean optimistic = false;

    public CompositeRetryExceptionHandler(boolean optimistic, List<RetryExceptionHandler> handlers) {
        this.optimistic = optimistic;
        this.handlers = handlers;
    }

    public CompositeRetryExceptionHandler(boolean optimistic, RetryExceptionHandler... handlers) {
        this.optimistic = optimistic;
        this.handlers = new ArrayList<>(Arrays.asList(handlers));
    }

    @Override
    public boolean handleException(JobRetryContext jobRetryContext) {
        boolean retryable = false;
        if (optimistic) {
            for (RetryExceptionHandler handler : handlers) {
                retryable |= handler.handleException(jobRetryContext);
            }
        } else {
            retryable = true;
            for (RetryExceptionHandler handler : handlers) {
                retryable &= handler.handleException(jobRetryContext);
            }
        }
        log.debug("Job ({}) Retryable[{}]: {}", jobRetryContext.getJobContext().getJobDetail().getKey(),
            jobRetryContext.getRetryCount(), retryable);
        return retryable;
    }
}
