package com.bees360.schedule.listener;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobListener;

/**
 * <AUTHOR>
 */
public interface DefaultJobListener extends JobListener {

    /**
     * {@inheritDoc}
     */
    @Override
    default void jobToBeExecuted(JobExecutionContext context) {}

    /**
     * {@inheritDoc}
     */
    @Override
    default void jobExecutionVetoed(JobExecutionContext context) {}

    /**
     * {@inheritDoc}
     */
    @Override
    default void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {}
}
