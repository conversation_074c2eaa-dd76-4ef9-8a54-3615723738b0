package com.bees360.schedule.listener;

import org.quartz.JobExecutionContext;
import org.quartz.Trigger;
import org.quartz.TriggerListener;

/**
 * <AUTHOR>
 *
 * 提供默认空实现的TriggerListener，允许用户仅仅实现自己希望的方法，而不实现不关注的方法。
 */
public interface DefaultTriggerListener extends TriggerListener {

    /**
     * {@inheritDoc}
     */
    @Override
    default void triggerFired(Trigger trigger, JobExecutionContext context) {}

    /**
     * {@inheritDoc}
     * 默认【不否决】Job的执行。
     */
    @Override
    default boolean vetoJobExecution(Trigger trigger, JobExecutionContext context) {
        return false;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    default void triggerMisfired(Trigger trigger) {}

    /**
     * {@inheritDoc}
     */
    @Override
    default void triggerComplete(Trigger trigger, JobExecutionContext context,
                         Trigger.CompletedExecutionInstruction triggerInstructionCode) {}
}
