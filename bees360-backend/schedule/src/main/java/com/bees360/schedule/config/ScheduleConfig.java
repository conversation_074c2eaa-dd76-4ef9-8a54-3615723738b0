package com.bees360.schedule.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import com.bees360.commons.springsupport.property.MixPropertySourceFactory;
import com.bees360.schedule.EnableQuartzScheduler;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> <PERSON>
 */
@RequiredArgsConstructor
@Configuration
@PropertySource(value = "classpath:application-schedule.yml", factory = MixPropertySourceFactory.class)
@ComponentScan(basePackageClasses = EnableQuartzScheduler.class)
public class ScheduleConfig {

}
