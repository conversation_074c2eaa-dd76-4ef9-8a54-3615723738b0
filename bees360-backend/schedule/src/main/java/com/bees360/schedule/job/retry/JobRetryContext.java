package com.bees360.schedule.job.retry;

import lombok.Data;
import org.quartz.JobExecutionContext;

/**
 * <AUTHOR>
 */
@Data
public class JobRetryContext {
    private int retryCount;
    private Exception lastEx;
    private JobExecutionContext jobContext;

    public JobRetryContext(JobExecutionContext jobContext) {
        this(jobContext,0);
    }

    public JobRetryContext(JobExecutionContext jobContext, int retryCount) {
        this(jobContext, retryCount, null);
    }

    public JobRetryContext(JobExecutionContext jobContext, int retryCount, Exception lastEx) {
        this.retryCount = retryCount;
        this.lastEx = lastEx;
        this.jobContext = jobContext;
    }
}
