package com.bees360.schedule.support;

import lombok.RequiredArgsConstructor;
import org.quartz.Calendar;
import org.quartz.JobDetail;
import org.quartz.Trigger;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.quartz.QuartzProperties;
import org.springframework.boot.autoconfigure.quartz.SchedulerFactoryBeanCustomizer;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.scheduling.quartz.SpringBeanJobFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR> <PERSON>
 */
@RequiredArgsConstructor
@Component
public class SchedulerFactoryBeanAssemble {

    private final QuartzProperties properties;

    private final ObjectProvider<SchedulerFactoryBeanCustomizer> customizers;

    private final ApplicationContext applicationContext;

    public void assemble(SchedulerFactoryBean schedulerFactoryBean, String schedulerName) {
        configSchedulerFactoryBean(schedulerFactoryBean, getDefaultQuartzProperties(), schedulerName);
    }

    public void assemble(SchedulerFactoryBean schedulerFactoryBean, Properties quartzProperties,
        String schedulerName) {
        configSchedulerFactoryBean(schedulerFactoryBean, quartzProperties, schedulerName);
    }

    private void configSchedulerFactoryBean(SchedulerFactoryBean schedulerFactoryBean, Properties quartzProperties,
        String schedulerName) {
        SpringBeanJobFactory jobFactory = new SpringBeanJobFactory();
        jobFactory.setApplicationContext(this.applicationContext);
        schedulerFactoryBean.setJobFactory(jobFactory);
        if (schedulerName != null) {
            schedulerFactoryBean.setSchedulerName(schedulerName);
        }
        schedulerFactoryBean.setAutoStartup(this.properties.isAutoStartup());
        schedulerFactoryBean.setStartupDelay((int)this.properties.getStartupDelay().getSeconds());
        schedulerFactoryBean.setWaitForJobsToCompleteOnShutdown(this.properties.isWaitForJobsToCompleteOnShutdown());
        schedulerFactoryBean.setOverwriteExistingJobs(this.properties.isOverwriteExistingJobs());
        if(!quartzProperties.isEmpty()) {
            schedulerFactoryBean.setQuartzProperties(quartzProperties);
        }
    }

    public Properties getDefaultQuartzProperties() {
        return asProperties(this.properties.getProperties());
    }

    private Properties asProperties(Map<String, String> source) {
        Properties properties = new Properties();
        properties.putAll(source);
        return properties;
    }

    public void customize(SchedulerFactoryBean schedulerFactoryBean) {
        this.customizers.orderedStream().forEach((customizer) -> customizer.customize(schedulerFactoryBean));
    }
}
