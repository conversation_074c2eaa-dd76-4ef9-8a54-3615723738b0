package com.bees360.schedule.job.retry;

import java.time.Duration;
import java.util.Date;

import org.apache.commons.lang3.time.DateUtils;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.SimpleTrigger;
import org.quartz.TriggerBuilder;

/**
 * <AUTHOR> Yang
 */
public class RetryableJobTriggerFactory {

    public static TriggerBuilder retryForeverTrigger(String name, String group, Duration interver) {
        return retryFixCountTrigger(name, group, SimpleTrigger.REPEAT_INDEFINITELY, interver);
    }

    public static TriggerBuilder retryFixCountTrigger(String name, String group, int retryCount) {
        return retryFixCountTrigger(name, group, retryCount, Duration.ofMillis(0));
    }

    public static TriggerBuilder retryFixCountTrigger(String name, String group, int retryCount, Duration interver) {

        SimpleScheduleBuilder simpleScheduleBuilder = simpleScheduleBuilder(retryCount, interver);

        // @formatter:off
        TriggerBuilder builder = TriggerBuilder.newTrigger()
            .withIdentity(name, group)
            .withSchedule(simpleScheduleBuilder)
            .startNow();
        // @formatter:on
        return builder;
    }

    public static TriggerBuilder retryForeverTriggerStartAt(String name, String group, Duration interver, Date startAt) {
        SimpleScheduleBuilder simpleScheduleBuilder = simpleScheduleBuilder(SimpleTrigger.REPEAT_INDEFINITELY, interver);

        // @formatter:off
        TriggerBuilder builder = TriggerBuilder.newTrigger()
            .withIdentity(name, group)
            .withSchedule(simpleScheduleBuilder)
            .startAt(startAt);
        // @formatter:on
        return builder;
    }

    private static SimpleScheduleBuilder simpleScheduleBuilder(int retryCount, Duration interver) {
        // @formatter: off
        SimpleScheduleBuilder simpleScheduleBuilder = SimpleScheduleBuilder.simpleSchedule()
            .withRepeatCount(retryCount)
            .withIntervalInMilliseconds(interver.toMillis());
        // @formatter: on
        return simpleScheduleBuilder;
    }
}
