package com.bees360.schedule.service;

import com.bees360.schedule.support.SchedulerProviderManager;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerKey;
import org.quartz.UnableToInterruptJobException;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.lang.NonNull;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 代理SchedulerProviderManager的事务安全类。提供scheduler在事务中安全执行的方法。
 *
 * <AUTHOR>
 */
@Slf4j
public class SchedulerManagerTransactionalDelegate extends SchedulerTransactionalDelegate {

    private SchedulerProviderManager schedulerProviderManager;

    public SchedulerManagerTransactionalDelegate(@NonNull SchedulerProviderManager schedulerProviderManager) {
        Preconditions.checkNotNull(schedulerProviderManager, "schedulerProviderManager may not be null.");
        this.schedulerProviderManager = schedulerProviderManager;
    }

    /**
     * 通过 JobDetail#getJobClass() 获取对应的Scheduler，在进行schedule操作
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void scheduleJob(@NonNull JobDetail jobDetail, @NonNull Trigger trigger) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobDetail);
        scheduleJob(scheduler, jobDetail, trigger);
    }

    /**
     * 通过 JobDetail#getJobClass() 获取对应的Scheduler，在进行schedule操作
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void scheduleJob(@NonNull JobDetail jobDetail, @NonNull Set<? extends Trigger> triggersForJob,
        boolean replace) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobDetail);
        scheduleJob(scheduler, jobDetail, triggersForJob, replace);
    }

    /**
     * 通过 JobDetail#getJobClass() 获取对应的Scheduler，在进行addJob操作
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void addJob(@NonNull JobDetail jobDetail, boolean replace) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobDetail);
        addJob(scheduler, jobDetail, replace);
    }

    /**
     * 通过 JobDetail#getJobClass() 获取对应的Scheduler，在进行addJob操作
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void addJob(@NonNull JobDetail jobDetail, boolean replace, boolean storeNonDurableWhileAwaitingScheduling)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobDetail);
        addJob(scheduler, jobDetail, replace, storeNonDurableWhileAwaitingScheduling);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void scheduleJob(@NonNull Class<? extends Job> jobClass, @NonNull JobDetail jobDetail, @NonNull Trigger trigger)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        scheduleJob(scheduler, jobDetail, trigger);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void scheduleJob(@NonNull Class<? extends Job> jobClass, @NonNull Trigger trigger) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        scheduleJob(scheduler, trigger);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void scheduleJobs(@NonNull Class<? extends Job> jobClass,
                             @NonNull Map<JobDetail, Set<? extends Trigger>> triggersAndJobs, boolean replace) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        scheduleJobs(scheduler, triggersAndJobs, replace);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void scheduleJob(@NonNull Class<? extends Job> jobClass, @NonNull JobDetail jobDetail,
                            @NonNull Set<? extends Trigger> triggersForJob, boolean replace) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        scheduleJob(scheduler, jobDetail, triggersForJob, replace);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public boolean unscheduleJob(@NonNull Class<? extends Job> jobClass, @NonNull TriggerKey triggerKey)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return unscheduleJob(scheduler, triggerKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public boolean unscheduleJobs(@NonNull Class<? extends Job> jobClass, @NonNull List<TriggerKey> triggerKeys)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return unscheduleJobs(scheduler, triggerKeys);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public Date rescheduleJob(@NonNull Class<? extends Job> jobClass, @NonNull TriggerKey triggerKey, @NonNull Trigger newTrigger)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return rescheduleJob(scheduler, triggerKey, newTrigger);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void addJob(@NonNull Class<? extends Job> jobClass, @NonNull JobDetail jobDetail, boolean replace)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        addJob(scheduler, jobDetail, replace);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void addJob(@NonNull Class<? extends Job> jobClass, @NonNull JobDetail jobDetail, boolean replace,
                       boolean storeNonDurableWhileAwaitingScheduling) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        addJob(scheduler, jobDetail, replace, storeNonDurableWhileAwaitingScheduling);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public boolean deleteJob(@NonNull Class<? extends Job> jobClass, @NonNull JobKey jobKey) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return deleteJob(scheduler, jobKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public boolean deleteJobs(@NonNull Class<? extends Job> jobClass, @NonNull List<JobKey> jobKeys) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return deleteJobs(scheduler, jobKeys);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void triggerJob(@NonNull Class<? extends Job> jobClass, @NonNull JobKey jobKey) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        triggerJob(scheduler, jobKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void triggerJob(@NonNull Class<? extends Job> jobClass, @NonNull JobKey jobKey, @NonNull JobDataMap data)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        triggerJob(scheduler, jobKey, data);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void pauseJob(@NonNull Class<? extends Job> jobClass, @NonNull JobKey jobKey) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        pauseJob(scheduler, jobKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void pauseJobs(@NonNull Class<? extends Job> jobClass, @NonNull GroupMatcher<JobKey> matcher)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        pauseJobs(scheduler, matcher);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void pauseTrigger(@NonNull Class<? extends Job> jobClass, @NonNull TriggerKey triggerKey) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        pauseTrigger(scheduler, triggerKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void pauseTriggers(@NonNull Class<? extends Job> jobClass, @NonNull GroupMatcher<TriggerKey> matcher)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        pauseTriggers(scheduler, matcher);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void resumeJob(@NonNull Class<? extends Job> jobClass, @NonNull JobKey jobKey) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        resumeJob(scheduler, jobKey);
    }

    /**
     *
     *
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void resumeJobs(@NonNull Class<? extends Job> jobClass, @NonNull GroupMatcher<JobKey> matcher)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        resumeJobs(scheduler, matcher);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void resumeTrigger(@NonNull Class<? extends Job> jobClass, @NonNull TriggerKey triggerKey) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        resumeTrigger(scheduler, triggerKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void resumeTriggers(@NonNull Class<? extends Job> jobClass, @NonNull GroupMatcher<TriggerKey> matcher)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        resumeTriggers(scheduler, matcher);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void pauseAll(@NonNull Class<? extends Job> jobClass) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        pauseAll(scheduler);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void resumeAll(@NonNull Class<? extends Job> jobClass) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        resumeAll(scheduler);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(readOnly = true)
    public List<String> getJobGroupNames(@NonNull Class<? extends Job> jobClass) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return getJobGroupNames(scheduler);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(readOnly = true)
    public Set<JobKey> getJobKeys(@NonNull Class<? extends Job> jobClass, @NonNull GroupMatcher<JobKey> matcher)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return getJobKeys(scheduler, matcher);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(readOnly = true)
    public List<? extends Trigger> getTriggersOfJob(@NonNull Class<? extends Job> jobClass, @NonNull JobKey jobKey)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return getTriggersOfJob(scheduler, jobKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(readOnly = true)
    public List<String> getTriggerGroupNames(@NonNull Class<? extends Job> jobClass) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return getTriggerGroupNames(scheduler);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(readOnly = true)
    public Set<TriggerKey> getTriggerKeys(@NonNull Class<? extends Job> jobClass, @NonNull GroupMatcher<TriggerKey> matcher)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return getTriggerKeys(scheduler, matcher);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(readOnly = true)
    public Set<String> getPausedTriggerGroups(@NonNull Class<? extends Job> jobClass) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return getPausedTriggerGroups(scheduler);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(readOnly = true)
    public JobDetail getJobDetail(@NonNull Class<? extends Job> jobClass, @NonNull JobKey jobKey) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return getJobDetail(scheduler, jobKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(readOnly = true)
    public Trigger getTrigger(@NonNull Class<? extends Job> jobClass, @NonNull TriggerKey triggerKey) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return getTrigger(scheduler, triggerKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(readOnly = true)
    public Trigger.TriggerState getTriggerState(@NonNull Class<? extends Job> jobClass, @NonNull TriggerKey triggerKey)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return getTriggerState(scheduler, triggerKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void resetTriggerFromErrorState(@NonNull Class<? extends Job> jobClass, @NonNull TriggerKey triggerKey)
        throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        resetTriggerFromErrorState(scheduler, triggerKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public boolean interrupt(@NonNull Class<? extends Job> jobClass, @NonNull JobKey jobKey)
        throws UnableToInterruptJobException {
        Scheduler scheduler = getScheduler(jobClass);
        return interrupt(scheduler, jobKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public boolean interrupt(@NonNull Class<? extends Job> jobClass, @NonNull String fireInstanceId) throws UnableToInterruptJobException {
        Scheduler scheduler = getScheduler(jobClass);
        return interrupt(scheduler, fireInstanceId);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(readOnly = true)
    public boolean checkExists(@NonNull Class<? extends Job> jobClass, @NonNull JobKey jobKey) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return checkExists(scheduler, jobKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(readOnly = true)
    public boolean checkExists(@NonNull Class<? extends Job> jobClass, @NonNull TriggerKey triggerKey) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        return checkExists(scheduler, triggerKey);
    }

    /**
     * @param jobClass 用于查找SchedulerProviderManager中的Scheduler
     */
    @Transactional(rollbackFor = SchedulerException.class)
    public void clear(@NonNull Class<? extends Job> jobClass) throws SchedulerException {
        Scheduler scheduler = getScheduler(jobClass);
        clear(scheduler);
    }

    private Scheduler getScheduler(@NonNull JobDetail jobDetail) {
        return schedulerProviderManager.getProvider(jobDetail.getJobClass()).getScheduler();
    }

    private Scheduler getScheduler(@NonNull Class<? extends Job> jobClass) {
        return schedulerProviderManager.getProvider(jobClass).getScheduler();
    }
}
