package com.bees360.schedule.job.retry;

import java.util.Objects;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.bees360.schedule.exception.RetryableException;
import com.bees360.schedule.job.retry.handler.RetryExceptionHandler;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 可重试类，该类为一个有状态的Job，其子类将均为you状态类，无需重新使用`@PersistJobDataAfterExecution`和`@DisallowConcurrentExecution`注解。
 *
 * 对于JobDataMap中的值，如果是引用类型，修改属性则会在执行之后更新到数据库，如果是基础类型，修改之后需要手动调用JobDataMap#put方法更新数据。
 *
 * <AUTHOR>
 */
@Slf4j
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public abstract class RetryableJob extends QuartzJobBean {

    private static final String FIELD_PARAM_RETRY_COUNT = "retryCount";

    @Setter
    private int retryCount = 0;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
       JobRetryContext jobRetryContext = new JobRetryContext(context, retryCount);

        RetryableOperation operation = getRetryableOperation();
        RetryExceptionHandler exHandler = getRetryExceptionHandler();

        doWithRetry(jobRetryContext, operation, exHandler);
    }

    private void doWithRetry(JobRetryContext jobRetryContext, RetryableOperation operation,
        RetryExceptionHandler exHandler) throws JobExecutionException {
        boolean successful = true;
        boolean retryable = false;
        try {
            jobRetryContext.setRetryCount(jobRetryContext.getRetryCount() + 1);
            operation.execute(jobRetryContext.getJobContext());
        } catch (Exception e) {
            successful = false;
            jobRetryContext.setLastEx(e);
            try {
                if (isRetryable(e)) {
                    retryable = (exHandler != null && exHandler.handleException(jobRetryContext));
                }
            } catch (Exception ex) {
                log.error("Unexpected error happens when try to process job {}",
                    jobRetryContext.getJobContext().getJobDetail().getKey(), ex);
                retryable = false;
            }
        } finally {
            updateContextIntValue(jobRetryContext.getJobContext(), FIELD_PARAM_RETRY_COUNT,
                jobRetryContext.getRetryCount());
            if (!retryable) {
                // 彻底结束执行
                unscheduleAllTriggers(successful, jobRetryContext);
            }
        }
    }

    protected boolean isRetryable(Exception e) {
        return e instanceof RetryableException;
    }

    /**
     * 更改的JobDataMap会在执行完成之后，因为{@code @PersistJobDataAfterExecution}而持久化到数据库
     */
    protected void updateContextIntValue(JobExecutionContext jobContext, String key, int value) {
        JobDataMap jobDataMap = jobContext.getJobDetail().getJobDataMap();
        if (!jobDataMap.containsKey(key) || !Objects.equals(value, jobDataMap.getIntValue(key))) {
            jobDataMap.put(key, value);
        }
    }

    private void unscheduleAllTriggers(boolean successful, JobRetryContext jobRetryContext)
        throws JobExecutionException {
        JobKey jobKey = jobRetryContext.getJobContext().getJobDetail().getKey();
        String message;
        if (successful) {
            message = "Unschedule all triggers of the job (%s) since the job is completed.";
            message = message.formatted(jobKey);
        } else {
            message =
                "Unschedule all triggers of the job (%s) because it can't complete its job and was stopped by its handler.";
            message = message.formatted(jobKey);
            // JobExecutionException 的抛出只会输出Info级别日志，这里补充一个ERROR级别的日志输出
            log.error(message, jobRetryContext.getLastEx());
        }
        JobExecutionException e = new JobExecutionException(message, jobRetryContext.getLastEx());
        // 结束所有和该job相关的调度器
        e.setUnscheduleAllTriggers(true);
        try {
            beforeJobEnd(successful, jobRetryContext);
        } catch (Exception ex) {
            log.error("Fail to execute beforeJobEnd: {}", ex.getMessage(), ex);
        }
        throw e;
    }

    protected void beforeJobEnd(boolean successful, JobRetryContext jobRetryContext) {
        // do nothing
    }

    protected abstract RetryableOperation getRetryableOperation();

    protected abstract RetryExceptionHandler getRetryExceptionHandler();
}
