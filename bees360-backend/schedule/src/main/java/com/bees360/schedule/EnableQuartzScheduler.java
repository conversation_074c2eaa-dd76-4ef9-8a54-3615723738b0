package com.bees360.schedule;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.bees360.schedule.config.ScheduleConfig;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @date 2020/04/28 15:12
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import({ScheduleConfig.class})
public @interface EnableQuartzScheduler {}
