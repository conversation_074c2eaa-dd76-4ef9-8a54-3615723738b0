package com.bees360.schedule.job.retry.handler;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;

import com.bees360.schedule.exception.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.bees360.schedule.job.retry.JobRetryContext;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
public class RetryExceptionHandlers {

    private RetryExceptionHandlers() {
        throw new AssertionError("Can't be instantiated.");
    }

    /**
     * 直接结束重试。
     *
     * @return true
     */
    public static RetryExceptionHandler getNeverRetryExceptionHandler() {
        return (ctx) -> false;
    }

    /**
     * 永远不停止重试。{@code logError}满足条件时，会输出error级别日志。
     *
     * @param logError
     *            判断是否输出error级别日志（Nullable）
     * @return true
     */
    public static RetryExceptionHandler getAlwaysRetryExceptionHandler(final Predicate<JobRetryContext> logError) {
        final Logger log = LoggerFactory.getLogger(getClassName("AlwaysRetryExceptionHandler"));
        return (ctx) -> {
            if (logError != null && logError.test(ctx)) {
                Exception e = ctx.getLastEx();
                log.error("Retry [{}] fails: {}", ctx.getRetryCount(), e.getMessage(), e);
            } else {
                log.warn("Job ({}) Retryable[{}]: {}", ctx.getJobContext().getJobDetail().getKey(), ctx.getRetryCount(),
                    true, ctx.getLastEx());
            }
            return true;
        };
    }

    /**
     * 具有重试次数上限。
     *
     * @param maxRetryCount
     *            重试次数。
     * @return
     */
    public static RetryExceptionHandler getSimpleRetryExceptionHandler(final int maxRetryCount) {
        final Logger log = LoggerFactory.getLogger(getClassName("SimpleRetryExceptionHandler"));
        return (ctx) -> {
            boolean retryable = maxRetryCount > ctx.getRetryCount();
            log.warn("Job ({}) Retryable[{}]: {}", ctx.getJobContext().getJobDetail().getKey(), ctx.getRetryCount(),
                retryable, ctx.getLastEx());
            return retryable;
        };
    }

    /**
     * 只有特定的异常类或者其子类才会继续重试。
     *
     * @param exClasses
     *            检测异常类
     * @param isToRetry
     *            找到异常类时，是否重试
     * @return 最后抛出的异常<b>不为</b>{@code exToRetry}中的类或者子类
     */
    public static RetryExceptionHandler getClassifierRetryExceptionHandler(
        final Collection<Class<? extends Throwable>> exClasses, final boolean isToRetry, final boolean withCause) {
        final Logger log = LoggerFactory.getLogger(getClassName("ClassifierRetryExceptionHandler"));
        final Set<Class<? extends Throwable>> classifier = new HashSet<>(exClasses);
        return (ctx) -> {
            Boolean isIncluded = false;
            Exception lastEx = ctx.getLastEx();
            for (Class<? extends Throwable> t : classifier) {
                if (t == null) {
                    continue;
                }
                if (t.isInstance(lastEx)
                    || (withCause && lastEx.getCause() != null && t.isInstance(lastEx.getCause()))) {
                    isIncluded = true;
                    break;
                }
            }
            boolean retryable = Objects.equals(isToRetry, isIncluded);
            log.warn("Job ({}) Retryable[{}]: {}", ctx.getJobContext().getJobDetail().getKey(), ctx.getRetryCount(),
                retryable, lastEx);
            return retryable;
        };
    }

    public static RetryExceptionHandler getLogErrorRetryException(final Predicate<JobRetryContext> isLogError,
        final Function<JobRetryContext, String> message) {
        final Logger log = LoggerFactory.getLogger(getClassName("LogErrorRetryException"));
        return (ctx) -> {
            if (isLogError.test(ctx)) {
                log.error(message.apply(ctx), ctx.getLastEx());
            } else {
                log.warn(message.apply(ctx), ctx.getLastEx());
            }
            return true;
        };
    }

    /**
     * 组合类型
     *
     * @param optimistic，true：只要有一个返回true，结果委托true；
     *            false：全部handler返回true，结果才为true
     * @param handlers
     * @return
     */
    public static RetryExceptionHandler getCompositeRetryExceptionHandler(final boolean optimistic,
        final RetryExceptionHandler... handlers) {
        return new CompositeRetryExceptionHandler(optimistic, handlers);
    }

    /**
     * 后处理handler，该handler允许用户在执行{@code handler}之后根据其结果一些操作。
     *
     * @param handler
     *            真正执行的handler，该handler的结果为最终的结果
     * @param retryConsumer
     *            如果handler的结果为true，即继续重试，则执行
     * @param stopConsumer
     *            如果handler的结果为false，即放弃重试，则执行
     * @return
     */
    public static RetryExceptionHandler getPostProccessRetryExceptionHandler(final RetryExceptionHandler handler,
        final Consumer<JobRetryContext> retryConsumer, final Consumer<JobRetryContext> stopConsumer) {
        final Logger log = LoggerFactory.getLogger(getClassName("PostProccessRetryExceptionHandler"));
        return (ctx) -> {
            boolean retryable = handler.handleException(ctx);
            if (retryable) {
                if (retryConsumer != null) {
                    retryConsumer.accept(ctx);
                }
            } else {
                if (stopConsumer != null) {
                    stopConsumer.accept(ctx);
                }
            }
            log.warn("Job ({}) Retryable[{}]: {}", ctx.getJobContext().getJobDetail().getKey(), ctx.getRetryCount(),
                retryable, ctx.getLastEx());
            return retryable;
        };
    }

    private static String getClassName(String innerClassName) {
        return RetryExceptionHandlers.class.getName() + "$" + innerClassName;
    }

    public static RetryExceptionHandlerBuilder newBuilder() {
        return new RetryExceptionHandlerBuilder();
    }

    @Slf4j
    public static class RetryExceptionHandlerBuilder {

        public static final int INFINITE_RETRY_COUNT = -1;

        private int retryCount = INFINITE_RETRY_COUNT;

        private Collection<Class<? extends Throwable>> retryCauses;

        private Predicate<JobRetryContext> startToLogError = (ctx) -> false;

        private Consumer<JobRetryContext> postRetry;

        private Consumer<JobRetryContext> postRetryStop;

        private boolean enableLog = true;

        /**
         * 达到指定重试次数，日志输出从warning转化为error。
         */
        public RetryExceptionHandlerBuilder setStartToLogErrorRetryCount(int startToLogErrorRetryCount) {
            this.startToLogError = (ctx) -> { return ctx.getRetryCount() >= startToLogErrorRetryCount;};
            return this;
        }

        /**
         * 该类内置了日志输出，如果不想要内置的日志输出，可以进行关闭。
         */
        public RetryExceptionHandlerBuilder setEnableLog(boolean enableLog) {
            this.enableLog = enableLog;
            return this;
        }

        public RetryExceptionHandlerBuilder disableLog() {
            return setEnableLog(false);
        }

        /**
         * 发生指定异常时进行重试，如果 {@link JobRetryContext#getLastEx()} 是 {@link RetryableException} 则会获取lastEx的cause进行判断。
         * 因此，RetryableException 会被忽略。
         */
        public RetryExceptionHandlerBuilder retryOnCause(Class<? extends Throwable>... causes) {
            this.retryCauses = Arrays.asList(causes);
            return this;
        }

        public RetryExceptionHandlerBuilder retryCount(int retryCount) {
            this.retryCount = retryCount;
            return this;
        }

        public RetryExceptionHandlerBuilder retryNever() {
            retryCount(0);
            return retryOnCause();
        }

        public RetryExceptionHandlerBuilder retryAlways() {
            retryCount(INFINITE_RETRY_COUNT);
            return retryOnCause();
        }

        /**
         * 每次重试完成之后，如果还可以重试，则会执行。
         */
        public RetryExceptionHandlerBuilder postRetry(Consumer<JobRetryContext> postRetry) {
            this.postRetry = postRetry;
            return this;
        }

        /**
         * 每次重试完成之后，如果不再重试，则会执行。
         */
        public RetryExceptionHandlerBuilder postRetryStop(Consumer<JobRetryContext> postRetryStop) {
            this.postRetryStop = postRetryStop;
            return this;
        }

        public RetryExceptionHandler build() {
            return (ctx) -> {
                boolean retryable = isRetryable(ctx);
                if (retryable) {
                    Optional.ofNullable(postRetry).ifPresent(post -> post.accept(ctx));
                } else {
                    Optional.ofNullable(postRetryStop).ifPresent(post -> post.accept(ctx));
                }
                String jobKey = Optional.ofNullable(ctx.getJobContext())
                    .map(jctx -> jctx.getJobDetail().getKey().toString()).orElse("NO_PROVIDED");
                if (startToLogError.test(ctx)) {
                    log.error("Job ({}) Retryable[{}]: {}", jobKey, ctx.getRetryCount(), retryable, ctx.getLastEx());
                } else {
                    log.warn("Job ({}) Retryable[{}]: {}", jobKey, ctx.getRetryCount(), retryable, ctx.getLastEx());
                }
                return retryable;
            };
        }

        private boolean isRetryable(JobRetryContext ctx) {
            if (!isInfiniteRetry() && ctx.getRetryCount() >= retryCount) {
                return false;
            }
            if (!CollectionUtils.isEmpty(retryCauses)) {
                final Exception lastEx = ctx.getLastEx();
                final Throwable lastThrowable = (lastEx instanceof RetryableException? lastEx.getCause(): lastEx);
                if (Objects.nonNull(lastThrowable)) {
                    return retryCauses.stream().anyMatch(retryCause -> {
                        return Objects.nonNull(retryCause) && retryCause.isInstance(lastThrowable);
                    });
                }
            }
            return true;
        }

        private boolean isInfiniteRetry() {
            return retryCount == INFINITE_RETRY_COUNT;
        }
    }
}
