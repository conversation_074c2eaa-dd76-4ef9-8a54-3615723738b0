package com.bees360.schedule.job.retry.handler;

import com.bees360.schedule.job.retry.JobRetryContext;

/**
 * 处理重试过程中发生异常的handler，该handler的返回值将决定是否继续重试。
 *
 * <AUTHOR>
 */
@FunctionalInterface
public interface RetryExceptionHandler {

    /**
     * 执行抛出异常之后调用，返回的结果将决定是否停止重试
     *
     * @param jobRetryContext
     *            Job执行上下文，handler可以从中获取必要信息
     * @return true,继续重试；false,结束重试
     */
    boolean handleException(JobRetryContext jobRetryContext);
}
