package com.bees360.schedule.support;

import org.quartz.Job;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;

/**
 * <AUTHOR>
 */
public class SchedulerFactoryBeanProvider extends SchedulerFactoryBean implements SchedulerProvider {

    private final Collection<Class<? extends Job>> jobClasses;

    public SchedulerFactoryBeanProvider() {
        jobClasses = new HashSet<>();
    }

    public SchedulerFactoryBeanProvider(Class<? extends Job>... jobClasses) {
        this(Arrays.asList(jobClasses));
    }

    public SchedulerFactoryBeanProvider(Collection<Class<? extends Job>> jobClasses) {
        this.jobClasses = Collections.unmodifiableCollection(jobClasses);
    }

    @Override
    public Collection<Class<? extends Job>> getSupportedJobTypes() {
        return jobClasses;
    }
}
