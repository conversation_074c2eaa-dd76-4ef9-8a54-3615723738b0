package com.bees360.schedule.listener;

import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.quartz.SchedulerListener;
import org.quartz.Trigger;
import org.quartz.TriggerKey;

/**
 * <AUTHOR>
 */
public interface DefaultSchedulerListener extends SchedulerListener {

    @Override
    default void jobScheduled(Trigger trigger) {}

    @Override
    default void jobUnscheduled(TriggerKey triggerKey) {}

    @Override
    default void triggerFinalized(Trigger trigger) {}

    @Override
    default void triggerPaused(TriggerKey triggerKey) {}

    @Override
    default void triggersPaused(String triggerGroup) {}

    @Override
    default void triggerResumed(<PERSON><PERSON><PERSON><PERSON> triggerKey) {}

    @Override
    default void triggersResumed(String triggerGroup) {}

    @Override
    default void jobAdded(JobDetail jobDetail) {}

    @Override
    default void jobDeleted(<PERSON><PERSON><PERSON> jobKey) {}

    @Override
    default void jobPaused(JobKey jobKey) {}

    @Override
    default void jobsPaused(String jobGroup) {}

    @Override
    default void jobResumed(<PERSON><PERSON><PERSON> jobKey) {}

    @Override
    default void jobsResumed(String jobGroup) {}

    @Override
    default void schedulerError(String msg, SchedulerException cause) {}

    @Override
    default void schedulerInStandbyMode() {}

    @Override
    default void schedulerStarted() {}

    @Override
    default void schedulerStarting() {}

    @Override
    default void schedulerShutdown() {}

    @Override
    default void schedulerShuttingdown() {}

    @Override
    default void schedulingDataCleared() {}
}
