package com.bees360.schedule.listener;

import lombok.extern.slf4j.Slf4j;
import org.quartz.Trigger;

/**
 * <AUTHOR>
 */
@Slf4j
public class LoggingOnTriggerMisfireListener implements DefaultTriggerListener {

    private String name;

    public LoggingOnTriggerMisfireListener(String name) {
        this.name = name;
    }

    @Override
    public String getName() {
        return name;
    }

    /**
     * 所有的misfired trigger都会执行该方法，请不要在该方法中做长时间操作
     */
    @Override
    public void triggerMisfired(Trigger trigger) {
        log.warn("Trigger `{}` is misfired, which triggered for job `{}`", trigger.getKey(), trigger.getJobKey());
    }
}
