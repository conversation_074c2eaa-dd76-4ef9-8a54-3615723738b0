spring:
  quartz:
    #Related attribute configuration
    properties:
      org.quartz:
        scheduler:
          # Scheduler name - also the cluster name in a cluster environment
          instanceName: Bees360QuartzScheduler
          # Instance name: Generate instance name by machine name and local time
          instanceId: AUTO
        threadPool:
          class: org.quartz.simpl.SimpleThreadPool
          # Optimal number of threads = (thread waiting time / thread cpu time + 1) * N
          # CPU intensive: thread pool size = total number of CPUs + 1
          # IO intensive: thread pool size = 2 * total number of CPUs + 1
          threadCount: 40
          threadPriority: 5
          # Whether the ClassLoader that loads the task code is inherited from the outside
          threadsInheritContextClassLoaderOfInitializingThread: true
          # Whether to set the scheduler thread as a daemon thread
          # makeSchedulerThreadDaemon: true
        jobStore:
          # Select the JDBC storage method
          class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
          # Use org/quartz/impl/jdbcjobstore/table_mysql_innodbs.sql
          driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
          # Are all JobDataMaps of String type?
          # When set to true, the value in JobDataMaps must be a string
          useProperties: false
          # The maximum tolerable trigger timeout. If it times out, it is considered a "failure"
          misfireThreshold: 6000
          # The prefix of the table storing related information
          tablePrefix: QRTZ_
          # Whether the application is in a cluster. If the application is in a cluster, it must be set to TRUE, otherwise an error will occur.
          isClustered: true
          # Only used when isClustered is set to true, set a frequency (in milliseconds) for the instance to report to other instances in the cluster.
          # This affects the speed of detecting failed instances. Default is 15s
          clusterCheckinInterval: 15000
          # This is the maximum number of missed triggers that the JobStore can handle. Handling too many (2 dozen) will quickly cause the database table to be locked long enough,
          # This will hinder the performance of other triggers (which have not missed the trigger) to execute.
#          maxMisfiresToHandleAtATime: 20
          # Setting this parameter to true will tell Quartz not to call its setAutoCommit(false) method after obtaining a connection from the data source.
          # It is useful in a few cases, for example, a driver is originally closed, but this shutdown method is called. But in most cases, the driver requires calling setAutoCommit(false)
#          dontSetAutoCommitFalse: false
        plugin:
          shutdownHook:
            class: org.quartz.plugins.management.ShutdownHookPlugin
            cleanShutdown: TRUE
    job-store-type: jdbc # Use jdbcJobStore, default is in-memory
    jdbc:
      initialize-schema: never # always will delete and recreate the database table each time it is started. If it is set to never, you need to find the script in the quartz jar to manually initialize the database table.
      comment-prefix: '#'
    # Whether to wait until the task is completed before closing the container
#    wait-for-jobs-to-complete-on-shutdown: false
    # Whether the configured job will overwrite the existing job information
    # After quartz is started, the database will be used as the standard. If this property is false, the configuration file will not take effect after modification.
#    overwrite-existing-jobs: false
