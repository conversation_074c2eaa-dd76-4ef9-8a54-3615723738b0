#!/bin/bash

REMOTE_MASTER_COMMIT_ID=$(git ls-remote | grep -w 'refs/heads/master$' | awk -F ' ' '{print $1}' | head -n 1)

if [[ $(echo "$REMOTE_MASTER_COMMIT_ID" | wc -l) -ne 1 ]]; then
  echo "ERROR: Can't find remote master HEAD commit id in local git history."
  exit 1
fi

COMMIT_IDS_ALL=$(git log --pretty='%H')
CURRENT_COMMIT_ID=$(echo "$COMMIT_IDS_ALL" | head -n 1)

if [[ $(echo "$CURRENT_COMMIT_ID" | wc -l) -ne 1 ]]; then
  echo "ERROR: Can't find current commit id."
  exit 1
fi

# Check whether it lags behind the remote master branch
if [[ $(echo "$COMMIT_IDS_ALL" | grep -c "$REMOTE_MASTER_COMMIT_ID") -lt 1 ]]; then
  echo "ERROR: The current commit lags behind the remote master HEAD."
  echo "Please use git pull origin master --rebase to solve it"
  exit 1
fi

COMMIT_COUNT_ALL=$(echo "$COMMIT_IDS_ALL" | wc -l)

if [[ $(echo "$CURRENT_COMMIT_ID" | wc -l) -lt 1 ]]; then
  echo "ERROR: It has missed commit message."
  exit 1
fi

FRONT_REMOTE_MASTER_COUNT=$(
  git log --pretty='%H' | head -30 |
    awk -F " " \
      '{
          if ($1 == id) {
            print NR-1;
          }
        }' \
      id="$REMOTE_MASTER_COMMIT_ID"
)

printf "Current commit is %d commit ahead of remote master HEAD.\n" "$FRONT_REMOTE_MASTER_COUNT"

CURRENT_COMMIT_IDS=$(git log --pretty='%H')
CURRENT_COMMIT=$(git log --pretty='%s' | head -n "$FRONT_REMOTE_MASTER_COUNT")
FRONT_REMOTE_MASTER_COUNT_PLUS=$((FRONT_REMOTE_MASTER_COUNT + 1))
MASTER_HEAD_TO_CURRENT_COMMIT=$(echo "$CURRENT_COMMIT_IDS" | head -n "$FRONT_REMOTE_MASTER_COUNT_PLUS")
CURRENT_COMMIT_COUNT=$(echo "$CURRENT_COMMIT" | wc -l)

if [[ $CURRENT_COMMIT_COUNT -lt 1 ]]; then
  echo "ERROR: Can't get commit message this times between remote master HEAD and current commit."
  exit 1
fi

CURRENT_NO_REPEAT_COMMIT_COUNT=$(echo "$CURRENT_COMMIT" | sort | uniq -c | wc -l)
