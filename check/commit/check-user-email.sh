#!/bin/bash

source ./check/commit/common.sh

COMPANY_EMAIL="@9realms.co"
CURRENT_COMMIT_EMAIL_COUNT=$(
  git log --pretty='%ae' |
    head -n "$FRONT_REMOTE_MASTER_COUNT" |
    grep -c "$COMPANY_EMAIL"
)

if [[ $CURRENT_COMMIT_EMAIL_COUNT -lt 1 ]]; then
  echo "ERROR: It is not allowed to use external email box to commit."
  exit 1
fi

# Check the company mailbox
if [[ $CURRENT_COMMIT_COUNT -ne $CURRENT_COMMIT_EMAIL_COUNT ]]; then
  echo "Commit with external mailbox is not allowed."
  exit 1
fi

exit 0
