import json
import logging
import os, sys
import subprocess
import requests
import urllib.request as request

read_token = sys.argv[1]
write_token = sys.argv[2]

email_dict = {
    "<EMAIL>": ["<EMAIL>", "<EMAIL>"],
    "tang<PERSON><PERSON><PERSON>@9realms.co": ["<EMAIL>"],
    "<EMAIL>": ["<EMAIL>"],
    "z<PERSON><PERSON><PERSON><PERSON>@9realms.co": ["<EMAIL>"],
    "<EMAIL>": ["<EMAIL>"],
    "<EMAIL>": ["<EMAIL>"],
    "<EMAIL>": ["<EMAIL>", "<EMAIL>"]
}

exclude_members = ("<EMAIL>", "<EMAIL>")

code_owner = {
    "bees360-backend/ai": "pizhicheng",
    "bees360-backend/web": "pizhicheng",
    "bees360-backend/bom": "pizhicheng",
    "bees360-backend/report": "pizhicheng",
    "bees360-backend/api/ai2client": "pizhicheng",
}

path = ".gitlab"
if not os.path.exists(path):
    os.mkdir(path)

url = "https://gitlab.bees360.com/api/v4/users/?private_token={}&per_page=100&page=".format(read_token)
suffix_email = "@9realms.co"


def get_gitlab_user(url, suffix_email):
    gitlab_user_dict = {}
    tmp_url = url
    page = 1
    # 通过分页方式轮询获取gitlab user信息
    while True:
        url = tmp_url + str(page)
        try:
            res = request.urlopen(url=url, timeout=5)
            res = json.loads(res.read())
            if res:
                for i in res:
                    if i.get("commit_email") and i.get("username") and  i.get("state") == "active":
                        gitlab_user_dict[i.get("commit_email")] = i.get("username")
                page += 1
            else:
                break
        except Exception as e:
            print(e)
            break
    res_gitlab_user_dict = {}
    for k, v in gitlab_user_dict.items():
        if k.endswith(suffix_email):
            res_gitlab_user_dict[k] = v
    return res_gitlab_user_dict


def find_all_path():
    file_dict = {}
    dir_dict = {}
    res = subprocess.Popen("git ls-tree -t -r --full-tree HEAD", shell=True, stdout=subprocess.PIPE)
    for i in res.stdout.readlines():
        path = i.decode("utf-8").split()[-1]
        if os.path.isfile(path):
            file_dict[path] = ""
        else:
            dir_dict[path] = {}
    return file_dict, dir_dict


def file_email(file, email_dict, user_dict):
    print(file)
    owner_email_dict = {}
    tmp_owner_dict = {}
    res = subprocess.Popen("git blame -e {}".format(file), shell=True, stdout=subprocess.PIPE)
    for i in res.stdout.readlines():
        try:
            owner_email = i.decode().split("(", 1)[1].split(" ")[0].lstrip("<").rstrip(">")
        except Exception as e:
            owner_email = str(i).split("(", 1)[1].split(" ")[0].lstrip("<").rstrip(">")
        if owner_email_dict.get(owner_email):
            owner_email_dict[owner_email] = owner_email_dict.get(owner_email) + 1
        else:
            owner_email_dict[owner_email] = 1
    if owner_email_dict:
        for k1, v1 in owner_email_dict.items():
            flag = True
            for k2, v2 in email_dict.items():
                if k1 in v2:
                    if k2 in tmp_owner_dict.keys():
                        tmp_owner_dict[k2] += v1
                    else:
                        tmp_owner_dict[k2] = v1
                    flag = False
                    break
            if flag:
                if k1 in tmp_owner_dict.keys():
                    tmp_owner_dict[k1] += v1
                else:
                    tmp_owner_dict[k1] = v1
        if tmp_owner_dict:
            sort_owner_dict = sorted(tmp_owner_dict.items(), key=lambda x: x[1], reverse=True)
            sort_owner_dict = list(filter(lambda x:x[0] in user_dict and x[0] not in exclude_members, sort_owner_dict ))
            if not sort_owner_dict:
                return None
            return sort_owner_dict[0][0]
        else:
            try:
                raise Exception("The codeowner of {} cannot be found:".format(file))
            except Exception as e:
                print(e)
            return None
    else:
        return None


def file_owner(file_dict, dir_dict, user_dict, email_dict):
    dir_dict["*"] = {}
    for k1 in file_dict.keys():
        flag = False
        owner_email = file_email(file=k1, email_dict=email_dict, user_dict= user_dict)
        for k2, v2 in user_dict.items():
            if k2 == owner_email:
                file_dict[k1] = v2
                flag = True
                break
        if flag:
            if k1.rsplit("/", 1)[0] in dir_dict.keys():
                tmp_dict = dir_dict[k1.rsplit("/", 1)[0]]
                if file_dict[k1] in tmp_dict.keys():
                    tmp_dict[file_dict[k1]] += 1
                else:
                    tmp_dict[file_dict[k1]] = 1
            else:
                tmp_dict = dir_dict["*"]
                if file_dict[k1] in tmp_dict.keys():
                    tmp_dict[file_dict[k1]] += 1
                else:
                    tmp_dict[file_dict[k1]] = 1
        else:
            try:
                raise Exception(
                    "The codeowner of {} cannot be found:The user may no longer exist on gitlab!".format(k1))
            except Exception as e:
                print(e)
                continue
    return file_dict, dir_dict


def dir_owner(dir_dict):
    dir_dict = dict(sorted(dir_dict.items(), key=lambda x: x[0], reverse=True))
    for k, v in dir_dict.items():
        if len(k.rsplit("/", 1)) == 2:
            if k.rsplit("/", 1)[0] in dir_dict.keys():
                for i, j in v.items():
                    if i in dir_dict[k.rsplit("/", 1)[0]].keys():
                        dir_dict[k.rsplit("/", 1)[0]][i] += 1
                    else:
                        dir_dict[k.rsplit("/", 1)[0]][i] = 1
                    dir_dict[k.rsplit("/", 1)[0]] = dict(
                        sorted(dir_dict[k.rsplit("/", 1)[0]].items(), key=lambda x: x[1], reverse=True))
                    break
            else:
                pass
        else:
            if k != "*":
                for i, j in v.items():
                    if i in dir_dict["*"].keys():
                        dir_dict["*"][i] += 1
                    else:
                        dir_dict["*"][i] = 1
                    dir_dict["*"] = dict(sorted(dir_dict["*"].items(), key=lambda x: x[1], reverse=True))
                    break
    return dir_dict

def update_code_owner(code_owner, file_dict, dir_dict):
    std = sorted(code_owner, key=lambda file: (os.path.dirname(file), os.path.basename(file)))

    codeowner = {}
    for dir in std:
        codeowner.update({dir: code_owner[dir]})

    for i in codeowner:
        for j in dir_dict:
            if j.startswith(i):
                dir_dict[j] = {codeowner[i]: 1}

    for i in codeowner:
        for j in file_dict:
            if j.startswith(i):
                file_dict[j] = codeowner[i]

    return file_dict, dir_dict

# 先写目录codeowners,后写文件codeowners,文件的codeowners才会覆盖目录的
def write(file_dict, dir_dict):
    dir = ""
    dir_dict = dict(sorted(dir_dict.items(), key=lambda x: x[0], reverse=False))
    for k1, v1 in dir_dict.items():
        if v1:
            if k1 != "*":
                for k2, v2 in v1.items():
                    dir += "/" + k1 + "/    @" + k2 + "\n"
                    break
            else:
                for k2, v2 in v1.items():
                    dir += "/" + "    @" + k2 + "\n"
                    break
    with open(".gitlab/CODEOWNERS", "w") as f:
        f.write(dir + "\n\n")
    file = ""
    for k, v in file_dict.items():
        if v:
            file += "/" + k + "    @" + v + "\n"
    with open(".gitlab/CODEOWNERS", "a") as f:
        f.write(file)


def main(email_dict, url, suffix_email):
    try:
        user_dict = get_gitlab_user(url=url, suffix_email=suffix_email)
        if user_dict:
            file_dict, dir_dict = find_all_path()
            file_dict, dir_dict = file_owner(file_dict=file_dict, dir_dict=dir_dict, user_dict=user_dict, email_dict=email_dict)
            dir_dict = dir_owner(dir_dict=dir_dict)
            file_dict, dir_dict = update_code_owner(code_owner, file_dict, dir_dict)
            write(file_dict=file_dict, dir_dict=dir_dict)
            print("Success")
        else:
            print("Gitlab user data is null")
            print("Fail")
    except Exception as e:
        print(e)
        print("Fail")


def update_codeowners():
    print("start read file")
    try:
        with open(".gitlab/CODEOWNERS", "r") as f:
            content = f.read()
    except Exception as e:
        print(e)
        os._exit(1)

    headers = {"PRIVATE-TOKEN": write_token, "Content-Type": "application/json"}
    url = "https://gitlab.bees360.com/api/v4/projects/10/repository/commits"

    text = {
        "branch": "master",
        "commit_message": "update .gitlab/CODEOWNERS",
        "actions": [
            {
                "action": "update",
                "file_path": ".gitlab/CODEOWNERS",
                "content": ""
            }
        ]
    }

    text["actions"][0]["content"] = content

    ret = requests.post(url=url, headers=headers, data=json.dumps(text))
    if  ret.status_code != 201:
        print("请求状态码: {} msg: {}".format(ret.status_code, ret.text))
        text["actions"][0]["action"] = "create"
        ret = requests.post(url=url, headers=headers, data=json.dumps(text))
        if ret.status_code != 201:
            print("请求状态码: {} msg: {}".format(ret.status_code, ret.text))
            print("update failed")
        else:
            print("create successful")
    else:
        print("update successful")


main(email_dict=email_dict, url=url, suffix_email=suffix_email)

update_codeowners()
