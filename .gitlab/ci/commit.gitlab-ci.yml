.commit-base:
  image: harbor.9realms.co/private/git

check-user-email:
  stage: commit
  extends: .commit-base
  only:
    - merge_requests
  cache: { }
  script:
    - ./check/commit/check-user-email.sh

check-commit-message:
  stage: commit
  extends: .commit-base
  only:
    - merge_requests
  cache: { }
  script:
    - ./check/commit/check-commit-message.sh

job:update-condeowners:
  stage: build
  image: harbor.9realms.co/private/python-ci
  variables:
    GIT_STRATEGY: clone
    GIT_DEPTH: 0
  only:
    refs:
      - schedules
    variables:
      - $SCHEDULE_TYPE == "updatecodeowners"
  script:
    - python python/generatecodeownersfile.py ${GITLAB_READ_TOKEN} ${GITLAB_WRITE_TOKEN}
