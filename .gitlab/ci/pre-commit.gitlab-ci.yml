# using pre-commit to verify
# see https://pre-commit.com/#usage-in-continuous-integration
check-pre-commit:
  stage: check
  image: harbor.9realms.co/private/pre-commit-hooks
  only:
    refs:
      - merge_requests
      - master
  variables:
    GIT_CLONE_PATH: $CI_BUILDS_DIR/$CI_PROJECT_PATH
  script:
    - pre-commit run --all-files --hook-stage commit


gitleaks-check:
  stage: check
  only:
    refs:
      - merge_requests
  image:
    name: harbor.9realms.co/public/gitleaks:latest
    entrypoint: [""]
  script:
    - gitleaks detect -c .gitleaks.toml --source="$CI_PROJECT_DIR" -v --no-git -r "$CI_PROJECT_DIR/gitleaks-report.json"
  artifacts:
    paths:
      - gitleaks-report.json
    expire_in: 1 week
    expose_as: 'gitleaks report'
    when: always
