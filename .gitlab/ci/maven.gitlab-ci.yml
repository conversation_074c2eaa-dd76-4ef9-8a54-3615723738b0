# maven for gitlab-ci
variables:
  MAVEN_OPTS: >-
    -Dhttps.protocols=TLSv1.2 -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository
    -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN
    -Dorg.slf4j.simpleLogger.showDateTime=true
    -Djava.awt.headless=true
    -Xmx3072m
    -Xms3072m
  MAVEN_CLI_OPTS: >-
    -P ci -Dchangelist=-SNAPSHOT
    -s .m2/settings.xml --batch-mode --errors --fail-at-end --show-version
    -DinstallAtEnd=true -DdeployAtEnd=true
  MYSQL_ROOT_PASSWORD: 123456
  MYSQL_DOCKER_VERSION: latest
  LIQUIBASE_COMMAND_USERNAME: root
  LIQUIBASE_COMMAND_PASSWORD: 123456
  LIQUIBASE_COMMAND_URL: *********************************

.maven-cache:
  cache:
    key: ${CI_COMMIT_REF_SLUG}-maven-cache
    paths:
      - .m2/repository/

.maven-base-only-pull:
  cache:
    paths:
      - .m2/repository
    key: ${CI_COMMIT_REF_SLUG}-maven-cache
    policy: pull

maven-compile:
  stage: build
  extends: .maven-cache
  only:
    refs:
      - merge_requests
    changes:
      - bees360-backend/**/*
  before_script:
    - cd bees360-backend
  script:
    - ./mvnw $MAVEN_CLI_OPTS clean compile -DskipTests -Dmaven-repository.snapshot-enabled=true

sonarqube-check:
  stage: test
  except:
    - master
    - /^release-.*$/i
    - test
    - prod
  only:
    refs:
      - merge_requests
    changes:
      - bees360-backend/**/*
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
    SONAR_TOKEN: "sqp_fb3d20fefab0ad1b83b75382ea1368c343e2d968"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  before_script:
    - cd bees360-backend
  script:
    - mvn clean compile $MAVEN_CLI_OPTS sonar:sonar -Dsonar.projectKey=root_upstream_AYG4luW8zonU-541_0Aa
  allow_failure: true


maven-verify:
  stage: test
  extends: .maven-cache
  except:
    - master
    - /^release-.*$/i
    - test
    - prod
  only:
    refs:
      - merge_requests
    changes:
      - bees360-backend/**/*
  services:
    - name: harbor.9realms.co/private/mysql8:latest
      alias: mysql
      command: ["--explicit-defaults-for-timestamp=ON"]
    - name: redis
      alias: redis
  before_script:
    - cd bees360-backend
  script:
    - ./mvnw $MAVEN_CLI_OPTS clean verify
    - >-
      awk -F "," '{
        total_instructions += $4 + $5;
        covered_instructions += $5;
        total_lines += $8 + $9;
        covered_lines += $9;
      } END {
        print covered_instructions, "/" ,total_instructions, "instructions covered";
        print "instruction coverage: "100*covered_instructions/total_instructions"%";
        print covered_lines, "/" ,total_lines, "lines covered";
        print "line coverage: "100*covered_lines/total_lines"%";
      }'
      test-report-aggregate/target/site/jacoco-aggregate/jacoco.csv

maven-test-release:
  stage: release
  extends: .maven-base-only-pull
  only:
    refs:
      - /^test-.*$/i # run only on branch or tag that starts with 'release-'.
  before_script:
    - mkdir -p ~/.docker
    - echo $DOCKER_CONFIG > ~/.docker/config.json
    - cd bees360-backend
  script:
    - >-
      ./mvnw $MAVEN_CLI_OPTS -P release deploy -DskipTests -DskipNexusDeploy=true -Dmaven-repository.snapshot-enabled=true
      -Dchangelist=-SNAPSHOT
      -Drevision=${CI_COMMIT_REF_NAME#test-}

maven-release:
  stage: release
  extends: .maven-base-only-pull
  only:
    refs:
      - /^release-.*$/i # run only on branch or tag that starts with 'release-'.
  before_script:
    - mkdir -p ~/.docker
    - echo $DOCKER_CONFIG > ~/.docker/config.json
    - cd bees360-backend
  script:
    - >-
      ./mvnw $MAVEN_CLI_OPTS -P release deploy -DskipTests -DskipNexusDeploy=true
      -Dchangelist=-RELEASE
      -Drevision=${CI_COMMIT_REF_NAME#release-}

# failed because of unknown reason
.maven-deploy-pages:
  stage: site
  extends: .maven-cache
  only:
    refs:
      - master
    changes:
      - bees360-backend/**/*
  before_script:
    - cd bees360-backend
  script:
    - ./mvnw $MAVEN_CLI_OPTS -P master javadoc:aggregate
    - mv target/site/apidocs public
  artifacts:
    paths:
      - public
