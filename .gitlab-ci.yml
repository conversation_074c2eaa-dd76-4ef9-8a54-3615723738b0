stages:
  # validate the gitlab commit information is correct
  - commit
  # validate the project is correct and all necessary information is available
  - check
  # compile the source code of the project
  - build
  # run tests using a suitable unit testing framework. These tests should not require the code be packaged or deployed
  - test
  # process and deploy the package if necessary into an environment where integration tests can be run.
  - integration-test
  # run any checks to verify the package is valid and meets quality criteria.
  - verify
  # package the code and copies the final package to the remote repository for sharing with other developers and projects.
  - release
  # generate the project site
  - site
  # deploy the project and make the service available to access

default:
  image: harbor.9realms.co/private/ci-base
  tags:
    - upstream

variables:
  DOCKER_HOST: "tcp://docker:2376"
  DOCKER_TLS_CERTDIR: "/certs"
  AWS_K8S_PATH: "k8s/aws/bees360/"
  SZ_K8S_PATH: "k8s/sz/bees360/"

# pipeline only runs when merging request to release branch or commiting to release branch
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_TAG
    - if: $CI_PIPELINE_SOURCE == "schedule"

include:
  - local: .gitlab/ci/*.gitlab-ci.yml

.sbom-check:
  variables:
    SBOM_PATH: "${CI_PROJECT_DIR}/bees360-backend/target/bom.xml"
    SCANMETHOD: Source_code_scanning
    PROJECT_NAME: upstream/upstream
  except:
    - schedules
  image: harbor.9realms.co/private/ci-base
  script:
    - |
      if [[ $dependency_check == "NO" ]];then
        echo check skip
      else
        mvn org.cyclonedx:cyclonedx-maven-plugin:2.9.1:makeAggregateBom -s .m2/settings.xml  -Dmaven-repository.snapshot-enabled=true
        python3 /opt/python-script/sbom_handle.py --step=upload_sbom
      fi
  after_script:
    - python3 /opt/python-script/sbom_handle.py --step=upload_nocobase

.check_file_change:
  before_script:
    - git fetch origin ${CI_DEFAULT_BRANCH}
    - change_files=`git diff --name-only $CI_COMMIT_SHA remotes/origin/${CI_DEFAULT_BRANCH}`
    - echo "change_files $change_files"
    - |
      if echo "$change_files" | egrep ${FILE_CHANGE}$;then
        echo There are dependency updates that need to be checked
      else
        echo Dependency is not updated, skip
        export dependency_check='NO'
      fi
    - cd $WORKDIR

sbom-check-mr:
  stage: test
  extends: .sbom-check
  variables:
    PAREN_PROJECT_NAME: upstream/upstream
    PROJECT_VERSION: ${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME}
    TIGGER_TYPE: "mr"
    WORKDIR: bees360-backend
  before_script:
    - cd $WORKDIR
  only:
    refs:
      - merge_requests
    changes:
      - bees360-backend/**/pom.xml

sbom-check-tag:
  stage: release
  extends:
    - .check_file_change
    - .sbom-check
  variables:
    PAREN_PROJECT_NAME: upstream/upstream
    PROJECT_VERSION: ${CI_COMMIT_TAG}
    TIGGER_TYPE: "tag"
    WORKDIR: bees360-backend
    FILE_CHANGE: "^bees360-backend.*pom.xml"
  only:
    refs:
      - /^release-.*$/i
      - /^test-.*$/i
    changes:
      - bees360-backend/**/pom.xml

sbom-check:
  stage: release
  extends: .sbom-check
  variables:
    PAREN_PROJECT_NAME: upstream
    PROJECT_VERSION: master
    TIGGER_TYPE: "merge_event"
    WORKDIR: bees360-backend
  before_script:
    - |
      cd $WORKDIR
      MERGE_INFO=$(curl --header "PRIVATE-TOKEN: ${GITLAB_READ_TOKEN}" \
      "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/repository/commits/${CI_COMMIT_SHA}/merge_requests")
      SOURCE_BRANCH=$(echo $MERGE_INFO | jq -r '.[0].source_branch')
      echo "Source branch was: $SOURCE_BRANCH"
      export SUB_PROJECT_VERSION=$SOURCE_BRANCH
  only:
    refs:
      - master
    changes:
      -  bees360-backend/**/pom.xml

.code-owner:
  cache: {}
  except:
    - schedules
  image: harbor.9realms.co/ops/ops-ci:latest
  only:
    refs:
      - merge_requests
  script:
    - git fetch origin ${CI_MERGE_REQUEST_TARGET_BRANCH_NAME}
    - python3 /opt/check_code_owner.py

check-commit-emails&&show-code-owner:
  stage: commit
  variables:
    CODE_OWNERS_STEP: SHOW_CODE_OWNERS
  before_script:
    - python3 /opt/check_email.py
  extends:
    - .code-owner

check-code-owner:
  stage: release
  extends:
    - .code-owner
